{"ast": null, "code": "const table = {\n  /**\n   * Default table styles\n   */\n  borderCollapse: {\n    value: 'collapse'\n  },\n  display: {\n    value: 'table'\n  },\n  width: {\n    value: '100%'\n  },\n  /**\n   * Default table head styles\n   */\n  head: {\n    display: {\n      value: 'table-header-group'\n    },\n    verticalAlign: {\n      value: 'middle'\n    }\n  },\n  /**\n   * Default table body styles\n   */\n  body: {\n    display: {\n      value: 'table-row-group'\n    },\n    verticalAlign: {\n      value: 'middle'\n    }\n  },\n  /**\n   * Default table foot styles\n   */\n  foot: {\n    display: {\n      value: 'table-footer-group'\n    },\n    verticalAlign: {\n      value: 'middle'\n    }\n  },\n  /**\n   * Default table row styles\n   */\n  row: {\n    display: {\n      value: 'table-row'\n    },\n    verticalAlign: {\n      value: 'middle'\n    },\n    hover: {\n      backgroundColor: {\n        value: '{colors.background.tertiary.value}'\n      }\n    },\n    striped: {\n      backgroundColor: {\n        value: '{colors.background.secondary.value}'\n      }\n    }\n  },\n  /**\n   * Default table header cell styles\n   */\n  header: {\n    borderColor: {\n      value: '{colors.border.tertiary.value}'\n    },\n    borderStyle: {\n      value: 'solid'\n    },\n    borderWidth: {\n      value: '{borderWidths.small.value}'\n    },\n    color: {\n      value: '{colors.font.primary.value}'\n    },\n    display: {\n      value: 'table-cell'\n    },\n    fontSize: {\n      value: '{fontSizes.medium.value}'\n    },\n    fontWeight: {\n      value: '{fontWeights.bold.value}'\n    },\n    padding: {\n      value: '{space.medium.value}'\n    },\n    verticalAlign: {\n      value: 'middle'\n    },\n    large: {\n      fontSize: {\n        value: '{fontSizes.large.value}'\n      },\n      padding: {\n        value: '{space.large.value}'\n      }\n    },\n    small: {\n      fontSize: {\n        value: '{fontSizes.small.value}'\n      },\n      padding: {\n        value: '{space.xs.value}'\n      }\n    }\n  },\n  /**\n   * Default table data cell styles\n   */\n  data: {\n    borderColor: {\n      value: '{colors.border.tertiary.value}'\n    },\n    borderStyle: {\n      value: 'solid'\n    },\n    borderWidth: {\n      value: '{borderWidths.small.value}'\n    },\n    color: {\n      value: '{colors.font.primary.value}'\n    },\n    display: {\n      value: 'table-cell'\n    },\n    fontSize: {\n      value: '{fontSizes.medium.value}'\n    },\n    fontWeight: {\n      value: '{fontWeights.normal.value}'\n    },\n    padding: {\n      value: '{space.medium.value}'\n    },\n    verticalAlign: {\n      value: 'middle'\n    },\n    large: {\n      fontSize: {\n        value: '{fontSizes.large.value}'\n      },\n      padding: {\n        value: '{space.large.value}'\n      }\n    },\n    small: {\n      fontSize: {\n        value: '{fontSizes.small.value}'\n      },\n      padding: {\n        value: '{space.xs.value}'\n      }\n    }\n  },\n  /**\n   * Default table caption styles\n   */\n  caption: {\n    captionSide: {\n      value: 'bottom'\n    },\n    color: {\n      value: '{colors.font.primary.value}'\n    },\n    display: {\n      value: 'table-caption'\n    },\n    fontSize: {\n      value: '{fontSizes.medium.value}'\n    },\n    textAlign: {\n      value: 'center'\n    },\n    wordBreak: {\n      value: 'break-all'\n    },\n    large: {\n      fontSize: {\n        value: '{fontSizes.large.value}'\n      }\n    },\n    small: {\n      fontSize: {\n        value: '{fontSizes.small.value}'\n      }\n    }\n  }\n};\nexport { table };", "map": {"version": 3, "names": ["table", "borderCollapse", "value", "display", "width", "head", "verticalAlign", "body", "foot", "row", "hover", "backgroundColor", "striped", "header", "borderColor", "borderStyle", "borderWidth", "color", "fontSize", "fontWeight", "padding", "large", "small", "data", "caption", "captionSide", "textAlign", "wordBreak"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/table.mjs"], "sourcesContent": ["const table = {\n    /**\n     * Default table styles\n     */\n    borderCollapse: { value: 'collapse' },\n    display: { value: 'table' },\n    width: { value: '100%' },\n    /**\n     * Default table head styles\n     */\n    head: {\n        display: { value: 'table-header-group' },\n        verticalAlign: { value: 'middle' },\n    },\n    /**\n     * Default table body styles\n     */\n    body: {\n        display: { value: 'table-row-group' },\n        verticalAlign: { value: 'middle' },\n    },\n    /**\n     * Default table foot styles\n     */\n    foot: {\n        display: { value: 'table-footer-group' },\n        verticalAlign: { value: 'middle' },\n    },\n    /**\n     * Default table row styles\n     */\n    row: {\n        display: { value: 'table-row' },\n        verticalAlign: { value: 'middle' },\n        hover: {\n            backgroundColor: { value: '{colors.background.tertiary.value}' },\n        },\n        striped: {\n            backgroundColor: { value: '{colors.background.secondary.value}' },\n        },\n    },\n    /**\n     * Default table header cell styles\n     */\n    header: {\n        borderColor: { value: '{colors.border.tertiary.value}' },\n        borderStyle: { value: 'solid' },\n        borderWidth: { value: '{borderWidths.small.value}' },\n        color: { value: '{colors.font.primary.value}' },\n        display: { value: 'table-cell' },\n        fontSize: { value: '{fontSizes.medium.value}' },\n        fontWeight: { value: '{fontWeights.bold.value}' },\n        padding: { value: '{space.medium.value}' },\n        verticalAlign: { value: 'middle' },\n        large: {\n            fontSize: { value: '{fontSizes.large.value}' },\n            padding: { value: '{space.large.value}' },\n        },\n        small: {\n            fontSize: { value: '{fontSizes.small.value}' },\n            padding: { value: '{space.xs.value}' },\n        },\n    },\n    /**\n     * Default table data cell styles\n     */\n    data: {\n        borderColor: { value: '{colors.border.tertiary.value}' },\n        borderStyle: { value: 'solid' },\n        borderWidth: { value: '{borderWidths.small.value}' },\n        color: { value: '{colors.font.primary.value}' },\n        display: { value: 'table-cell' },\n        fontSize: { value: '{fontSizes.medium.value}' },\n        fontWeight: { value: '{fontWeights.normal.value}' },\n        padding: { value: '{space.medium.value}' },\n        verticalAlign: { value: 'middle' },\n        large: {\n            fontSize: { value: '{fontSizes.large.value}' },\n            padding: { value: '{space.large.value}' },\n        },\n        small: {\n            fontSize: { value: '{fontSizes.small.value}' },\n            padding: { value: '{space.xs.value}' },\n        },\n    },\n    /**\n     * Default table caption styles\n     */\n    caption: {\n        captionSide: { value: 'bottom' },\n        color: { value: '{colors.font.primary.value}' },\n        display: { value: 'table-caption' },\n        fontSize: { value: '{fontSizes.medium.value}' },\n        textAlign: { value: 'center' },\n        wordBreak: { value: 'break-all' },\n        large: {\n            fontSize: { value: '{fontSizes.large.value}' },\n        },\n        small: {\n            fontSize: { value: '{fontSizes.small.value}' },\n        },\n    },\n};\n\nexport { table };\n"], "mappings": "AAAA,MAAMA,KAAK,GAAG;EACV;AACJ;AACA;EACIC,cAAc,EAAE;IAAEC,KAAK,EAAE;EAAW,CAAC;EACrCC,OAAO,EAAE;IAAED,KAAK,EAAE;EAAQ,CAAC;EAC3BE,KAAK,EAAE;IAAEF,KAAK,EAAE;EAAO,CAAC;EACxB;AACJ;AACA;EACIG,IAAI,EAAE;IACFF,OAAO,EAAE;MAAED,KAAK,EAAE;IAAqB,CAAC;IACxCI,aAAa,EAAE;MAAEJ,KAAK,EAAE;IAAS;EACrC,CAAC;EACD;AACJ;AACA;EACIK,IAAI,EAAE;IACFJ,OAAO,EAAE;MAAED,KAAK,EAAE;IAAkB,CAAC;IACrCI,aAAa,EAAE;MAAEJ,KAAK,EAAE;IAAS;EACrC,CAAC;EACD;AACJ;AACA;EACIM,IAAI,EAAE;IACFL,OAAO,EAAE;MAAED,KAAK,EAAE;IAAqB,CAAC;IACxCI,aAAa,EAAE;MAAEJ,KAAK,EAAE;IAAS;EACrC,CAAC;EACD;AACJ;AACA;EACIO,GAAG,EAAE;IACDN,OAAO,EAAE;MAAED,KAAK,EAAE;IAAY,CAAC;IAC/BI,aAAa,EAAE;MAAEJ,KAAK,EAAE;IAAS,CAAC;IAClCQ,KAAK,EAAE;MACHC,eAAe,EAAE;QAAET,KAAK,EAAE;MAAqC;IACnE,CAAC;IACDU,OAAO,EAAE;MACLD,eAAe,EAAE;QAAET,KAAK,EAAE;MAAsC;IACpE;EACJ,CAAC;EACD;AACJ;AACA;EACIW,MAAM,EAAE;IACJC,WAAW,EAAE;MAAEZ,KAAK,EAAE;IAAiC,CAAC;IACxDa,WAAW,EAAE;MAAEb,KAAK,EAAE;IAAQ,CAAC;IAC/Bc,WAAW,EAAE;MAAEd,KAAK,EAAE;IAA6B,CAAC;IACpDe,KAAK,EAAE;MAAEf,KAAK,EAAE;IAA8B,CAAC;IAC/CC,OAAO,EAAE;MAAED,KAAK,EAAE;IAAa,CAAC;IAChCgB,QAAQ,EAAE;MAAEhB,KAAK,EAAE;IAA2B,CAAC;IAC/CiB,UAAU,EAAE;MAAEjB,KAAK,EAAE;IAA2B,CAAC;IACjDkB,OAAO,EAAE;MAAElB,KAAK,EAAE;IAAuB,CAAC;IAC1CI,aAAa,EAAE;MAAEJ,KAAK,EAAE;IAAS,CAAC;IAClCmB,KAAK,EAAE;MACHH,QAAQ,EAAE;QAAEhB,KAAK,EAAE;MAA0B,CAAC;MAC9CkB,OAAO,EAAE;QAAElB,KAAK,EAAE;MAAsB;IAC5C,CAAC;IACDoB,KAAK,EAAE;MACHJ,QAAQ,EAAE;QAAEhB,KAAK,EAAE;MAA0B,CAAC;MAC9CkB,OAAO,EAAE;QAAElB,KAAK,EAAE;MAAmB;IACzC;EACJ,CAAC;EACD;AACJ;AACA;EACIqB,IAAI,EAAE;IACFT,WAAW,EAAE;MAAEZ,KAAK,EAAE;IAAiC,CAAC;IACxDa,WAAW,EAAE;MAAEb,KAAK,EAAE;IAAQ,CAAC;IAC/Bc,WAAW,EAAE;MAAEd,KAAK,EAAE;IAA6B,CAAC;IACpDe,KAAK,EAAE;MAAEf,KAAK,EAAE;IAA8B,CAAC;IAC/CC,OAAO,EAAE;MAAED,KAAK,EAAE;IAAa,CAAC;IAChCgB,QAAQ,EAAE;MAAEhB,KAAK,EAAE;IAA2B,CAAC;IAC/CiB,UAAU,EAAE;MAAEjB,KAAK,EAAE;IAA6B,CAAC;IACnDkB,OAAO,EAAE;MAAElB,KAAK,EAAE;IAAuB,CAAC;IAC1CI,aAAa,EAAE;MAAEJ,KAAK,EAAE;IAAS,CAAC;IAClCmB,KAAK,EAAE;MACHH,QAAQ,EAAE;QAAEhB,KAAK,EAAE;MAA0B,CAAC;MAC9CkB,OAAO,EAAE;QAAElB,KAAK,EAAE;MAAsB;IAC5C,CAAC;IACDoB,KAAK,EAAE;MACHJ,QAAQ,EAAE;QAAEhB,KAAK,EAAE;MAA0B,CAAC;MAC9CkB,OAAO,EAAE;QAAElB,KAAK,EAAE;MAAmB;IACzC;EACJ,CAAC;EACD;AACJ;AACA;EACIsB,OAAO,EAAE;IACLC,WAAW,EAAE;MAAEvB,KAAK,EAAE;IAAS,CAAC;IAChCe,KAAK,EAAE;MAAEf,KAAK,EAAE;IAA8B,CAAC;IAC/CC,OAAO,EAAE;MAAED,KAAK,EAAE;IAAgB,CAAC;IACnCgB,QAAQ,EAAE;MAAEhB,KAAK,EAAE;IAA2B,CAAC;IAC/CwB,SAAS,EAAE;MAAExB,KAAK,EAAE;IAAS,CAAC;IAC9ByB,SAAS,EAAE;MAAEzB,KAAK,EAAE;IAAY,CAAC;IACjCmB,KAAK,EAAE;MACHH,QAAQ,EAAE;QAAEhB,KAAK,EAAE;MAA0B;IACjD,CAAC;IACDoB,KAAK,EAAE;MACHJ,QAAQ,EAAE;QAAEhB,KAAK,EAAE;MAA0B;IACjD;EACJ;AACJ,CAAC;AAED,SAASF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}