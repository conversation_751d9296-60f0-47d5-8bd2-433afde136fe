{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { handleUserSRPAuthFlow, getActiveSignInUsername, getSignInResult, getSignInResultFromError } from '../utils/signInHelpers.mjs';\nimport { setActiveSignInState, resetActiveSignInState } from '../../../client/utils/store/signInStore.mjs';\nimport { cacheCognitoTokens } from '../tokenProvider/cacheTokens.mjs';\nimport '../utils/refreshAuthTokens.mjs';\nimport '../tokenProvider/errorHelpers.mjs';\nimport '../utils/types.mjs';\nimport { tokenOrchestrator } from '../tokenProvider/tokenProvider.mjs';\nimport { dispatchSignedInHubEvent } from '../utils/dispatchSignedInHubEvent.mjs';\nimport { getNewDeviceMetadata } from '../utils/getNewDeviceMetadata.mjs';\nimport { resetAutoSignIn } from './autoSignIn.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Signs a user in\n *\n * @param input - The SignInWithSRPInput object\n * @returns SignInWithSRPOutput\n * @throws service: {@link InitiateAuthException }, {@link RespondToAuthChallengeException } - Cognito service errors\n * thrown during the sign-in process.\n * @throws validation: {@link AuthValidationErrorCode  } - Validation errors thrown when either username or password\n *  are not defined.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nfunction signInWithSRP(_x) {\n  return _signInWithSRP.apply(this, arguments);\n}\nfunction _signInWithSRP() {\n  _signInWithSRP = _asyncToGenerator(function* (input) {\n    const {\n      username,\n      password\n    } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    const signInDetails = {\n      loginId: username,\n      authFlowType: 'USER_SRP_AUTH'\n    };\n    assertTokenProviderConfig(authConfig);\n    const clientMetaData = input.options?.clientMetadata;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptySignInUsername);\n    assertValidationError(!!password, AuthValidationErrorCode.EmptySignInPassword);\n    try {\n      const {\n        ChallengeName: handledChallengeName,\n        ChallengeParameters: handledChallengeParameters,\n        AuthenticationResult,\n        Session\n      } = yield handleUserSRPAuthFlow(username, password, clientMetaData, authConfig, tokenOrchestrator);\n      const activeUsername = getActiveSignInUsername(username);\n      // sets up local state used during the sign-in process\n      setActiveSignInState({\n        signInSession: Session,\n        username: activeUsername,\n        challengeName: handledChallengeName,\n        signInDetails\n      });\n      if (AuthenticationResult) {\n        yield cacheCognitoTokens({\n          username: activeUsername,\n          ...AuthenticationResult,\n          NewDeviceMetadata: yield getNewDeviceMetadata({\n            userPoolId: authConfig.userPoolId,\n            userPoolEndpoint: authConfig.userPoolEndpoint,\n            newDeviceMetadata: AuthenticationResult.NewDeviceMetadata,\n            accessToken: AuthenticationResult.AccessToken\n          }),\n          signInDetails\n        });\n        resetActiveSignInState();\n        yield dispatchSignedInHubEvent();\n        resetAutoSignIn();\n        return {\n          isSignedIn: true,\n          nextStep: {\n            signInStep: 'DONE'\n          }\n        };\n      }\n      return getSignInResult({\n        challengeName: handledChallengeName,\n        challengeParameters: handledChallengeParameters\n      });\n    } catch (error) {\n      resetActiveSignInState();\n      resetAutoSignIn();\n      assertServiceError(error);\n      const result = getSignInResultFromError(error.name);\n      if (result) return result;\n      throw error;\n    }\n  });\n  return _signInWithSRP.apply(this, arguments);\n}\nexport { signInWithSRP };", "map": {"version": 3, "names": ["Amplify", "assertTokenProviderConfig", "AuthValidationErrorCode", "assertValidationError", "assertServiceError", "handleUserSRPAuthFlow", "getActiveSignInUsername", "getSignInResult", "getSignInResultFromError", "setActiveSignInState", "resetActiveSignInState", "cacheCognitoTokens", "tokenOrchestrator", "dispatchSignedInHubEvent", "getNewDeviceMetadata", "resetAutoSignIn", "signInWithSRP", "_x", "_signInWithSRP", "apply", "arguments", "_asyncToGenerator", "input", "username", "password", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "signInDetails", "loginId", "authFlowType", "clientMetaData", "options", "clientMetadata", "EmptySignInUsername", "EmptySignInPassword", "ChallengeName", "handledChallengeName", "ChallengeParameters", "handledChallengeParameters", "AuthenticationResult", "Session", "activeUsername", "signInSession", "challenge<PERSON>ame", "NewDeviceMetadata", "userPoolId", "userPoolEndpoint", "newDeviceMetadata", "accessToken", "AccessToken", "isSignedIn", "nextStep", "signInStep", "challengeParameters", "error", "result", "name"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/signInWithSRP.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { handleUserSRPAuthFlow, getActiveSignInUsername, getSignInResult, getSignInResultFromError } from '../utils/signInHelpers.mjs';\nimport { setActiveSignInState, resetActiveSignInState } from '../../../client/utils/store/signInStore.mjs';\nimport { cacheCognitoTokens } from '../tokenProvider/cacheTokens.mjs';\nimport '../utils/refreshAuthTokens.mjs';\nimport '../tokenProvider/errorHelpers.mjs';\nimport '../utils/types.mjs';\nimport { tokenOrchestrator } from '../tokenProvider/tokenProvider.mjs';\nimport { dispatchSignedInHubEvent } from '../utils/dispatchSignedInHubEvent.mjs';\nimport { getNewDeviceMetadata } from '../utils/getNewDeviceMetadata.mjs';\nimport { resetAutoSignIn } from './autoSignIn.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Signs a user in\n *\n * @param input - The SignInWithSRPInput object\n * @returns SignInWithSRPOutput\n * @throws service: {@link InitiateAuthException }, {@link RespondToAuthChallengeException } - Cognito service errors\n * thrown during the sign-in process.\n * @throws validation: {@link AuthValidationErrorCode  } - Validation errors thrown when either username or password\n *  are not defined.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nasync function signInWithSRP(input) {\n    const { username, password } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    const signInDetails = {\n        loginId: username,\n        authFlowType: 'USER_SRP_AUTH',\n    };\n    assertTokenProviderConfig(authConfig);\n    const clientMetaData = input.options?.clientMetadata;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptySignInUsername);\n    assertValidationError(!!password, AuthValidationErrorCode.EmptySignInPassword);\n    try {\n        const { ChallengeName: handledChallengeName, ChallengeParameters: handledChallengeParameters, AuthenticationResult, Session, } = await handleUserSRPAuthFlow(username, password, clientMetaData, authConfig, tokenOrchestrator);\n        const activeUsername = getActiveSignInUsername(username);\n        // sets up local state used during the sign-in process\n        setActiveSignInState({\n            signInSession: Session,\n            username: activeUsername,\n            challengeName: handledChallengeName,\n            signInDetails,\n        });\n        if (AuthenticationResult) {\n            await cacheCognitoTokens({\n                username: activeUsername,\n                ...AuthenticationResult,\n                NewDeviceMetadata: await getNewDeviceMetadata({\n                    userPoolId: authConfig.userPoolId,\n                    userPoolEndpoint: authConfig.userPoolEndpoint,\n                    newDeviceMetadata: AuthenticationResult.NewDeviceMetadata,\n                    accessToken: AuthenticationResult.AccessToken,\n                }),\n                signInDetails,\n            });\n            resetActiveSignInState();\n            await dispatchSignedInHubEvent();\n            resetAutoSignIn();\n            return {\n                isSignedIn: true,\n                nextStep: { signInStep: 'DONE' },\n            };\n        }\n        return getSignInResult({\n            challengeName: handledChallengeName,\n            challengeParameters: handledChallengeParameters,\n        });\n    }\n    catch (error) {\n        resetActiveSignInState();\n        resetAutoSignIn();\n        assertServiceError(error);\n        const result = getSignInResultFromError(error.name);\n        if (result)\n            return result;\n        throw error;\n    }\n}\n\nexport { signInWithSRP };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,yBAAyB,QAAQ,mCAAmC;AAC7E,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,qBAAqB,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,wBAAwB,QAAQ,4BAA4B;AACtI,SAASC,oBAAoB,EAAEC,sBAAsB,QAAQ,6CAA6C;AAC1G,SAASC,kBAAkB,QAAQ,kCAAkC;AACrE,OAAO,gCAAgC;AACvC,OAAO,mCAAmC;AAC1C,OAAO,oBAAoB;AAC3B,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,SAASC,wBAAwB,QAAQ,uCAAuC;AAChF,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,eAAe,QAAQ,kBAAkB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,SAWeC,aAAaA,CAAAC,EAAA;EAAA,OAAAC,cAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,eAAA;EAAAA,cAAA,GAAAG,iBAAA,CAA5B,WAA6BC,KAAK,EAAE;IAChC,MAAM;MAAEC,QAAQ;MAAEC;IAAS,CAAC,GAAGF,KAAK;IACpC,MAAMG,UAAU,GAAGzB,OAAO,CAAC0B,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpD,MAAMC,aAAa,GAAG;MAClBC,OAAO,EAAEP,QAAQ;MACjBQ,YAAY,EAAE;IAClB,CAAC;IACD9B,yBAAyB,CAACwB,UAAU,CAAC;IACrC,MAAMO,cAAc,GAAGV,KAAK,CAACW,OAAO,EAAEC,cAAc;IACpD/B,qBAAqB,CAAC,CAAC,CAACoB,QAAQ,EAAErB,uBAAuB,CAACiC,mBAAmB,CAAC;IAC9EhC,qBAAqB,CAAC,CAAC,CAACqB,QAAQ,EAAEtB,uBAAuB,CAACkC,mBAAmB,CAAC;IAC9E,IAAI;MACA,MAAM;QAAEC,aAAa,EAAEC,oBAAoB;QAAEC,mBAAmB,EAAEC,0BAA0B;QAAEC,oBAAoB;QAAEC;MAAS,CAAC,SAASrC,qBAAqB,CAACkB,QAAQ,EAAEC,QAAQ,EAAEQ,cAAc,EAAEP,UAAU,EAAEb,iBAAiB,CAAC;MAC/N,MAAM+B,cAAc,GAAGrC,uBAAuB,CAACiB,QAAQ,CAAC;MACxD;MACAd,oBAAoB,CAAC;QACjBmC,aAAa,EAAEF,OAAO;QACtBnB,QAAQ,EAAEoB,cAAc;QACxBE,aAAa,EAAEP,oBAAoB;QACnCT;MACJ,CAAC,CAAC;MACF,IAAIY,oBAAoB,EAAE;QACtB,MAAM9B,kBAAkB,CAAC;UACrBY,QAAQ,EAAEoB,cAAc;UACxB,GAAGF,oBAAoB;UACvBK,iBAAiB,QAAQhC,oBAAoB,CAAC;YAC1CiC,UAAU,EAAEtB,UAAU,CAACsB,UAAU;YACjCC,gBAAgB,EAAEvB,UAAU,CAACuB,gBAAgB;YAC7CC,iBAAiB,EAAER,oBAAoB,CAACK,iBAAiB;YACzDI,WAAW,EAAET,oBAAoB,CAACU;UACtC,CAAC,CAAC;UACFtB;QACJ,CAAC,CAAC;QACFnB,sBAAsB,CAAC,CAAC;QACxB,MAAMG,wBAAwB,CAAC,CAAC;QAChCE,eAAe,CAAC,CAAC;QACjB,OAAO;UACHqC,UAAU,EAAE,IAAI;UAChBC,QAAQ,EAAE;YAAEC,UAAU,EAAE;UAAO;QACnC,CAAC;MACL;MACA,OAAO/C,eAAe,CAAC;QACnBsC,aAAa,EAAEP,oBAAoB;QACnCiB,mBAAmB,EAAEf;MACzB,CAAC,CAAC;IACN,CAAC,CACD,OAAOgB,KAAK,EAAE;MACV9C,sBAAsB,CAAC,CAAC;MACxBK,eAAe,CAAC,CAAC;MACjBX,kBAAkB,CAACoD,KAAK,CAAC;MACzB,MAAMC,MAAM,GAAGjD,wBAAwB,CAACgD,KAAK,CAACE,IAAI,CAAC;MACnD,IAAID,MAAM,EACN,OAAOA,MAAM;MACjB,MAAMD,KAAK;IACf;EACJ,CAAC;EAAA,OAAAtC,cAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}