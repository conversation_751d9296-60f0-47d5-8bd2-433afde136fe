{"ast": null, "code": "'use strict';\n\nvar callBound = require('call-bound');\nvar isRegex = require('is-regex');\nvar $exec = callBound('RegExp.prototype.exec');\nvar $TypeError = require('es-errors/type');\n\n/** @type {import('.')} */\nmodule.exports = function regexTester(regex) {\n  if (!isRegex(regex)) {\n    throw new $TypeError('`regex` must be a RegExp');\n  }\n  return function test(s) {\n    return $exec(regex, s) !== null;\n  };\n};", "map": {"version": 3, "names": ["callBound", "require", "isRegex", "$exec", "$TypeError", "module", "exports", "regexTester", "regex", "test", "s"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/safe-regex-test/index.js"], "sourcesContent": ["'use strict';\n\nvar callBound = require('call-bound');\nvar isRegex = require('is-regex');\n\nvar $exec = callBound('RegExp.prototype.exec');\nvar $TypeError = require('es-errors/type');\n\n/** @type {import('.')} */\nmodule.exports = function regexTester(regex) {\n\tif (!isRegex(regex)) {\n\t\tthrow new $TypeError('`regex` must be a RegExp');\n\t}\n\treturn function test(s) {\n\t\treturn $exec(regex, s) !== null;\n\t};\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,SAAS,GAAGC,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIC,OAAO,GAAGD,OAAO,CAAC,UAAU,CAAC;AAEjC,IAAIE,KAAK,GAAGH,SAAS,CAAC,uBAAuB,CAAC;AAC9C,IAAII,UAAU,GAAGH,OAAO,CAAC,gBAAgB,CAAC;;AAE1C;AACAI,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC5C,IAAI,CAACN,OAAO,CAACM,KAAK,CAAC,EAAE;IACpB,MAAM,IAAIJ,UAAU,CAAC,0BAA0B,CAAC;EACjD;EACA,OAAO,SAASK,IAAIA,CAACC,CAAC,EAAE;IACvB,OAAOP,KAAK,CAACK,KAAK,EAAEE,CAAC,CAAC,KAAK,IAAI;EAChC,CAAC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}