{"ast": null, "code": "import { base64Decoder } from '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Converts a base64url encoded string to an ArrayBuffer\n * @param base64url - a base64url encoded string\n * @returns ArrayBuffer\n */\nconst convertBase64UrlToArrayBuffer = base64url => {\n  return Uint8Array.from(base64Decoder.convert(base64url, {\n    urlSafe: true\n  }), x => x.charCodeAt(0)).buffer;\n};\nexport { convertBase64UrlToArrayBuffer };", "map": {"version": 3, "names": ["base64Decoder", "convertBase64UrlToArrayBuffer", "base64url", "Uint8Array", "from", "convert", "urlSafe", "x", "charCodeAt", "buffer"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/convert/base64url/convertBase64UrlToArrayBuffer.mjs"], "sourcesContent": ["import { base64Decoder } from '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Converts a base64url encoded string to an ArrayBuffer\n * @param base64url - a base64url encoded string\n * @returns ArrayBuffer\n */\nconst convertBase64UrlToArrayBuffer = (base64url) => {\n    return Uint8Array.from(base64Decoder.convert(base64url, { urlSafe: true }), x => x.charCodeAt(0)).buffer;\n};\n\nexport { convertBase64UrlToArrayBuffer };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,mCAAmC;;AAEjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,6BAA6B,GAAIC,SAAS,IAAK;EACjD,OAAOC,UAAU,CAACC,IAAI,CAACJ,aAAa,CAACK,OAAO,CAACH,SAAS,EAAE;IAAEI,OAAO,EAAE;EAAK,CAAC,CAAC,EAAEC,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC,CAACC,MAAM;AAC5G,CAAC;AAED,SAASR,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}