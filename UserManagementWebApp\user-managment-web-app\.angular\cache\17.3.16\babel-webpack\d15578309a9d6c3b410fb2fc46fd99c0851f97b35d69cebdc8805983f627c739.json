{"ast": null, "code": "const aiConversation = {\n  message: {\n    backgroundColor: {\n      value: '{colors.background.secondary.value}'\n    },\n    borderRadius: {\n      value: '{radii.large.value}'\n    },\n    gap: {\n      value: '{space.small.value}'\n    },\n    paddingBlock: {\n      value: '{space.small.value}'\n    },\n    paddingInline: {\n      value: '{space.small.value}'\n    },\n    user: {\n      backgroundColor: {\n        value: '{colors.background.secondary.value}'\n      }\n    },\n    assistant: {\n      backgroundColor: {\n        value: '{colors.primary.10.value}'\n      }\n    },\n    sender: {\n      gap: {\n        value: '{space.small.value}'\n      },\n      username: {\n        color: {\n          value: '{colors.font.primary.value}'\n        },\n        fontSize: {\n          value: 'inherit'\n        },\n        fontWeight: {\n          value: '{fontWeights.bold.value}'\n        }\n      },\n      timestamp: {\n        color: {\n          value: '{colors.font.tertiary.value}'\n        },\n        fontSize: {\n          value: 'inherit'\n        },\n        fontWeight: {\n          value: 'inherit'\n        }\n      }\n    },\n    body: {\n      gap: {\n        value: '{space.xs.value}'\n      }\n    },\n    actions: {\n      gap: {\n        value: '{space.xs.value}'\n      }\n    }\n  },\n  form: {\n    gap: {\n      value: '{space.small.value}'\n    },\n    padding: {\n      value: '{space.small.value}'\n    }\n  },\n  attachment: {\n    borderColor: {\n      value: '{colors.border.secondary.value}'\n    },\n    borderWidth: {\n      value: '{borderWidths.small.value}'\n    },\n    borderRadius: {\n      value: '{radii.small.value}'\n    },\n    fontSize: {\n      value: '{fontSizes.small.value}'\n    },\n    paddingBlock: {\n      value: '{space.xxxs.value}'\n    },\n    paddingInline: {\n      value: '{space.xs.value}'\n    },\n    gap: {\n      value: '{space.xs.value}'\n    },\n    list: {\n      padding: {\n        value: '{space.xs.value}'\n      },\n      paddingBlockStart: {\n        value: '0'\n      },\n      gap: {\n        value: '{space.xxs.value}'\n      }\n    },\n    name: {\n      color: {\n        value: '{colors.font.primary.value}'\n      },\n      fontSize: {\n        value: '{fontSizes.small.value}'\n      },\n      fontWeight: {\n        value: '{fontWeights.normal.value}'\n      }\n    },\n    size: {\n      color: {\n        value: '{colors.font.tertiary.value}'\n      },\n      fontSize: {\n        value: '{fontSizes.small.value}'\n      },\n      fontWeight: {\n        value: '{fontWeights.normal.value}'\n      }\n    },\n    remove: {\n      padding: {\n        value: '{space.xxs.value}'\n      }\n    },\n    image: {\n      width: {\n        value: '{fontSizes.medium.value}'\n      },\n      height: {\n        value: '{fontSizes.medium.value}'\n      }\n    }\n  }\n};\nexport { aiConversation };", "map": {"version": 3, "names": ["aiConversation", "message", "backgroundColor", "value", "borderRadius", "gap", "paddingBlock", "paddingInline", "user", "assistant", "sender", "username", "color", "fontSize", "fontWeight", "timestamp", "body", "actions", "form", "padding", "attachment", "borderColor", "borderWidth", "list", "paddingBlockStart", "name", "size", "remove", "image", "width", "height"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/aiConversation.mjs"], "sourcesContent": ["const aiConversation = {\n    message: {\n        backgroundColor: { value: '{colors.background.secondary.value}' },\n        borderRadius: { value: '{radii.large.value}' },\n        gap: { value: '{space.small.value}' },\n        paddingBlock: { value: '{space.small.value}' },\n        paddingInline: { value: '{space.small.value}' },\n        user: {\n            backgroundColor: { value: '{colors.background.secondary.value}' },\n        },\n        assistant: {\n            backgroundColor: { value: '{colors.primary.10.value}' },\n        },\n        sender: {\n            gap: { value: '{space.small.value}' },\n            username: {\n                color: { value: '{colors.font.primary.value}' },\n                fontSize: { value: 'inherit' },\n                fontWeight: { value: '{fontWeights.bold.value}' },\n            },\n            timestamp: {\n                color: { value: '{colors.font.tertiary.value}' },\n                fontSize: { value: 'inherit' },\n                fontWeight: { value: 'inherit' },\n            },\n        },\n        body: { gap: { value: '{space.xs.value}' } },\n        actions: { gap: { value: '{space.xs.value}' } },\n    },\n    form: {\n        gap: { value: '{space.small.value}' },\n        padding: { value: '{space.small.value}' },\n    },\n    attachment: {\n        borderColor: { value: '{colors.border.secondary.value}' },\n        borderWidth: { value: '{borderWidths.small.value}' },\n        borderRadius: { value: '{radii.small.value}' },\n        fontSize: { value: '{fontSizes.small.value}' },\n        paddingBlock: { value: '{space.xxxs.value}' },\n        paddingInline: { value: '{space.xs.value}' },\n        gap: { value: '{space.xs.value}' },\n        list: {\n            padding: { value: '{space.xs.value}' },\n            paddingBlockStart: { value: '0' },\n            gap: { value: '{space.xxs.value}' },\n        },\n        name: {\n            color: { value: '{colors.font.primary.value}' },\n            fontSize: { value: '{fontSizes.small.value}' },\n            fontWeight: { value: '{fontWeights.normal.value}' },\n        },\n        size: {\n            color: { value: '{colors.font.tertiary.value}' },\n            fontSize: { value: '{fontSizes.small.value}' },\n            fontWeight: { value: '{fontWeights.normal.value}' },\n        },\n        remove: {\n            padding: { value: '{space.xxs.value}' },\n        },\n        image: {\n            width: { value: '{fontSizes.medium.value}' },\n            height: { value: '{fontSizes.medium.value}' },\n        },\n    },\n};\n\nexport { aiConversation };\n"], "mappings": "AAAA,MAAMA,cAAc,GAAG;EACnBC,OAAO,EAAE;IACLC,eAAe,EAAE;MAAEC,KAAK,EAAE;IAAsC,CAAC;IACjEC,YAAY,EAAE;MAAED,KAAK,EAAE;IAAsB,CAAC;IAC9CE,GAAG,EAAE;MAAEF,KAAK,EAAE;IAAsB,CAAC;IACrCG,YAAY,EAAE;MAAEH,KAAK,EAAE;IAAsB,CAAC;IAC9CI,aAAa,EAAE;MAAEJ,KAAK,EAAE;IAAsB,CAAC;IAC/CK,IAAI,EAAE;MACFN,eAAe,EAAE;QAAEC,KAAK,EAAE;MAAsC;IACpE,CAAC;IACDM,SAAS,EAAE;MACPP,eAAe,EAAE;QAAEC,KAAK,EAAE;MAA4B;IAC1D,CAAC;IACDO,MAAM,EAAE;MACJL,GAAG,EAAE;QAAEF,KAAK,EAAE;MAAsB,CAAC;MACrCQ,QAAQ,EAAE;QACNC,KAAK,EAAE;UAAET,KAAK,EAAE;QAA8B,CAAC;QAC/CU,QAAQ,EAAE;UAAEV,KAAK,EAAE;QAAU,CAAC;QAC9BW,UAAU,EAAE;UAAEX,KAAK,EAAE;QAA2B;MACpD,CAAC;MACDY,SAAS,EAAE;QACPH,KAAK,EAAE;UAAET,KAAK,EAAE;QAA+B,CAAC;QAChDU,QAAQ,EAAE;UAAEV,KAAK,EAAE;QAAU,CAAC;QAC9BW,UAAU,EAAE;UAAEX,KAAK,EAAE;QAAU;MACnC;IACJ,CAAC;IACDa,IAAI,EAAE;MAAEX,GAAG,EAAE;QAAEF,KAAK,EAAE;MAAmB;IAAE,CAAC;IAC5Cc,OAAO,EAAE;MAAEZ,GAAG,EAAE;QAAEF,KAAK,EAAE;MAAmB;IAAE;EAClD,CAAC;EACDe,IAAI,EAAE;IACFb,GAAG,EAAE;MAAEF,KAAK,EAAE;IAAsB,CAAC;IACrCgB,OAAO,EAAE;MAAEhB,KAAK,EAAE;IAAsB;EAC5C,CAAC;EACDiB,UAAU,EAAE;IACRC,WAAW,EAAE;MAAElB,KAAK,EAAE;IAAkC,CAAC;IACzDmB,WAAW,EAAE;MAAEnB,KAAK,EAAE;IAA6B,CAAC;IACpDC,YAAY,EAAE;MAAED,KAAK,EAAE;IAAsB,CAAC;IAC9CU,QAAQ,EAAE;MAAEV,KAAK,EAAE;IAA0B,CAAC;IAC9CG,YAAY,EAAE;MAAEH,KAAK,EAAE;IAAqB,CAAC;IAC7CI,aAAa,EAAE;MAAEJ,KAAK,EAAE;IAAmB,CAAC;IAC5CE,GAAG,EAAE;MAAEF,KAAK,EAAE;IAAmB,CAAC;IAClCoB,IAAI,EAAE;MACFJ,OAAO,EAAE;QAAEhB,KAAK,EAAE;MAAmB,CAAC;MACtCqB,iBAAiB,EAAE;QAAErB,KAAK,EAAE;MAAI,CAAC;MACjCE,GAAG,EAAE;QAAEF,KAAK,EAAE;MAAoB;IACtC,CAAC;IACDsB,IAAI,EAAE;MACFb,KAAK,EAAE;QAAET,KAAK,EAAE;MAA8B,CAAC;MAC/CU,QAAQ,EAAE;QAAEV,KAAK,EAAE;MAA0B,CAAC;MAC9CW,UAAU,EAAE;QAAEX,KAAK,EAAE;MAA6B;IACtD,CAAC;IACDuB,IAAI,EAAE;MACFd,KAAK,EAAE;QAAET,KAAK,EAAE;MAA+B,CAAC;MAChDU,QAAQ,EAAE;QAAEV,KAAK,EAAE;MAA0B,CAAC;MAC9CW,UAAU,EAAE;QAAEX,KAAK,EAAE;MAA6B;IACtD,CAAC;IACDwB,MAAM,EAAE;MACJR,OAAO,EAAE;QAAEhB,KAAK,EAAE;MAAoB;IAC1C,CAAC;IACDyB,KAAK,EAAE;MACHC,KAAK,EAAE;QAAE1B,KAAK,EAAE;MAA2B,CAAC;MAC5C2B,MAAM,EAAE;QAAE3B,KAAK,EAAE;MAA2B;IAChD;EACJ;AACJ,CAAC;AAED,SAASH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}