{"ast": null, "code": "import { windowExists, globalExists } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with nuxt 2.15 / vue 2.7\nfunction nuxtWebDetect() {\n  return windowExists() && (window.__NUXT__ !== undefined || window.$nuxt !== undefined);\n}\nfunction nuxtSSRDetect() {\n  return globalExists() && typeof global.__NUXT_PATHS__ !== 'undefined';\n}\nexport { nuxtSSRDetect, nuxtWebDetect };", "map": {"version": 3, "names": ["windowExists", "globalExists", "nuxtWebDetect", "window", "__NUXT__", "undefined", "$nuxt", "nuxtSSRDetect", "global", "__NUXT_PATHS__"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Platform/detection/Nuxt.mjs"], "sourcesContent": ["import { windowExists, globalExists } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with nuxt 2.15 / vue 2.7\nfunction nuxtWebDetect() {\n    return (windowExists() &&\n        (window.__NUXT__ !== undefined ||\n            window.$nuxt !== undefined));\n}\nfunction nuxtSSRDetect() {\n    return (globalExists() && typeof global.__NUXT_PATHS__ !== 'undefined');\n}\n\nexport { nuxtSSRDetect, nuxtWebDetect };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,YAAY,QAAQ,eAAe;;AAE1D;AACA;AACA;AACA,SAASC,aAAaA,CAAA,EAAG;EACrB,OAAQF,YAAY,CAAC,CAAC,KACjBG,MAAM,CAACC,QAAQ,KAAKC,SAAS,IAC1BF,MAAM,CAACG,KAAK,KAAKD,SAAS,CAAC;AACvC;AACA,SAASE,aAAaA,CAAA,EAAG;EACrB,OAAQN,YAAY,CAAC,CAAC,IAAI,OAAOO,MAAM,CAACC,cAAc,KAAK,WAAW;AAC1E;AAEA,SAASF,aAAa,EAAEL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}