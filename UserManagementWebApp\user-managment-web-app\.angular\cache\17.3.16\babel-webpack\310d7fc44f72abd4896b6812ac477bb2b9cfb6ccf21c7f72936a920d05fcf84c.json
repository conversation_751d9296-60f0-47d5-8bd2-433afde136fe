{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from 'aws-amplify';\nimport { resendSignUpCode, resetPassword, confirmResetPassword, confirmSignUp, confirmSignIn, signUp, signIn, getCurrentUser } from 'aws-amplify/auth';\nimport '@aws-amplify/core/internals/utils';\nimport 'aws-amplify/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport '../../types/authenticator/user.mjs';\nimport '../../types/authenticator/attributes.mjs';\nimport { hasSpecialChars } from '../../helpers/authenticator/utils.mjs';\nimport '../../helpers/accountSettings/utils.mjs';\n\n// Cognito does not allow a password length less then 8 characters\nconst DEFAULT_COGNITO_PASSWORD_MIN_LENGTH = 8;\nconst isInvalidUserAtributes = userAttributes => Array.isArray(userAttributes);\nconst parseUserAttributes = userAttributes => {\n  if (!userAttributes) {\n    return undefined;\n  }\n  // `aws-amplify` versions <= 6.0.5 return an array of `userAttributes` rather than an object\n  if (isInvalidUserAtributes(userAttributes)) {\n    return Object.entries(userAttributes).map(([_, value]) => Object.keys(value)[0]);\n  }\n  return Object.keys(userAttributes);\n};\nconst defaultServices = {\n  getAmplifyConfig() {\n    return _asyncToGenerator(function* () {\n      const result = Amplify.getConfig();\n      const cliConfig = result.Auth?.Cognito;\n      const {\n        loginWith,\n        userAttributes\n      } = result.Auth?.Cognito ?? {};\n      const parsedLoginMechanisms = loginWith ? Object.entries(loginWith).filter(([key, _value]) => key !== 'oauth').filter(([_key, value]) => !!value).map(keyValueArray => {\n        return keyValueArray[0] === 'phone' // the key for phone_number is phone in getConfig but everywhere else we treat is as phone_number\n        ? 'phone_number' : keyValueArray[0];\n      }) : undefined;\n      const parsedSignupAttributes = parseUserAttributes(userAttributes);\n      const parsedSocialProviders = loginWith?.oauth?.providers ? loginWith.oauth.providers?.map(provider => provider.toString().toLowerCase()) : undefined;\n      return {\n        ...cliConfig,\n        loginMechanisms: parsedLoginMechanisms,\n        signUpAttributes: parsedSignupAttributes,\n        socialProviders: parsedSocialProviders\n      };\n    })();\n  },\n  getCurrentUser,\n  handleSignIn: signIn,\n  handleSignUp: signUp,\n  handleConfirmSignIn: confirmSignIn,\n  handleConfirmSignUp: confirmSignUp,\n  handleForgotPasswordSubmit: confirmResetPassword,\n  handleForgotPassword: resetPassword,\n  handleResendSignUpCode: resendSignUpCode,\n  // Validation hooks for overriding\n  validateCustomSignUp(_, __) {\n    return _asyncToGenerator(function* () {})();\n  },\n  validateFormPassword(formData, touchData, passwordSettings) {\n    return _asyncToGenerator(function* () {\n      const {\n        password\n      } = formData;\n      const {\n        password: touched_password\n      } = touchData;\n      /**\n       * If the password is not touched,\n       * or if the password settings are not set, we don't need to validate it.\n       */\n      if (!touched_password || !passwordSettings) return null;\n      const password_complexity = [];\n      const policyMinLength = passwordSettings.minLength ?? DEFAULT_COGNITO_PASSWORD_MIN_LENGTH;\n      if (password.length < policyMinLength) {\n        password_complexity.push(`Password must have at least ${policyMinLength} characters`);\n      }\n      if (passwordSettings.requireLowercase && !/[a-z]/.test(password)) password_complexity.push('Password must have lower case letters');\n      if (passwordSettings.requireUppercase && !/[A-Z]/.test(password)) password_complexity.push('Password must have upper case letters');\n      if (passwordSettings.requireNumbers && !/[0-9]/.test(password)) password_complexity.push('Password must have numbers');\n      // https://docs.aws.amazon.com/cognito/latest/developerguide/user-pool-settings-policies.html\n      if (passwordSettings.requireSpecialCharacters && !hasSpecialChars(password)) password_complexity.push('Password must have special characters');\n      /**\n       * Only return an error if there is at least one error.\n       */\n      return password_complexity.length !== 0 ? {\n        password: password_complexity\n      } : null;\n    })();\n  },\n  validateConfirmPassword(formData, touchData) {\n    return _asyncToGenerator(function* () {\n      const {\n        password,\n        confirm_password\n      } = formData;\n      const {\n        confirm_password: touched_confirm_password,\n        password: touched_password\n      } = touchData;\n      if (!password && !confirm_password) {\n        // these inputs are clean, don't complain yet\n        return null;\n      } else if ((password || confirm_password) && password !== confirm_password && (touched_confirm_password && touched_password || password?.length >= 6 && confirm_password?.length >= 6)) {\n        // Only return an error if both fields have text entered,\n        // the passwords do not match, and the fields have been\n        // touched or the password and confirm password is longer then or equal to 6.\n        return {\n          confirm_password: 'Your passwords must match'\n        };\n      }\n    })();\n  },\n  validatePreferredUsername(_, __) {\n    return _asyncToGenerator(function* () {})();\n  }\n};\nexport { defaultServices };", "map": {"version": 3, "names": ["Amplify", "resendSignUpCode", "resetPassword", "confirmResetPassword", "confirmSignUp", "confirmSignIn", "signUp", "signIn", "getCurrentUser", "hasSpecialChars", "DEFAULT_COGNITO_PASSWORD_MIN_LENGTH", "isInvalidUserAtributes", "userAttributes", "Array", "isArray", "parseUserAttributes", "undefined", "Object", "entries", "map", "_", "value", "keys", "defaultServices", "getAmplifyConfig", "_asyncToGenerator", "result", "getConfig", "cliConfig", "<PERSON><PERSON>", "Cognito", "loginWith", "parsedLoginMechanisms", "filter", "key", "_value", "_key", "key<PERSON><PERSON>ueArray", "parsedSignupAttributes", "parsedSocialProviders", "o<PERSON>h", "providers", "provider", "toString", "toLowerCase", "loginMechanisms", "signUpAttributes", "socialProviders", "handleSignIn", "handleSignUp", "handleConfirmSignIn", "handleConfirmSignUp", "handleForgotPasswordSubmit", "handleForgotPassword", "handleResendSignUpCode", "validateCustomSignUp", "__", "validateFormPassword", "formData", "touchData", "passwordSettings", "password", "touched_password", "password_complexity", "policy<PERSON>in<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "length", "push", "requireLowercase", "test", "requireUppercase", "requireNumbers", "requireSpecialCharacters", "validateConfirmPassword", "confirm_password", "touched_confirm_password", "validatePreferredUsername"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/machines/authenticator/defaultServices.mjs"], "sourcesContent": ["import { Amplify } from 'aws-amplify';\nimport { resendSignUpCode, resetPassword, confirmResetPassword, confirmSignUp, confirmSignIn, signUp, signIn, getCurrentUser } from 'aws-amplify/auth';\nimport '@aws-amplify/core/internals/utils';\nimport 'aws-amplify/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport '../../types/authenticator/user.mjs';\nimport '../../types/authenticator/attributes.mjs';\nimport { hasSpecialChars } from '../../helpers/authenticator/utils.mjs';\nimport '../../helpers/accountSettings/utils.mjs';\n\n// Cognito does not allow a password length less then 8 characters\nconst DEFAULT_COGNITO_PASSWORD_MIN_LENGTH = 8;\nconst isInvalidUserAtributes = (userAttributes) => Array.isArray(userAttributes);\nconst parseUserAttributes = (userAttributes) => {\n    if (!userAttributes) {\n        return undefined;\n    }\n    // `aws-amplify` versions <= 6.0.5 return an array of `userAttributes` rather than an object\n    if (isInvalidUserAtributes(userAttributes)) {\n        return Object.entries(userAttributes).map(([_, value]) => Object.keys(value)[0]);\n    }\n    return Object.keys(userAttributes);\n};\nconst defaultServices = {\n    async getAmplifyConfig() {\n        const result = Amplify.getConfig();\n        const cliConfig = result.Auth?.Cognito;\n        const { loginWith, userAttributes } = result.Auth?.Cognito ?? {};\n        const parsedLoginMechanisms = loginWith\n            ? Object.entries(loginWith)\n                .filter(([key, _value]) => key !== 'oauth')\n                .filter(([_key, value]) => !!value)\n                .map((keyValueArray) => {\n                return keyValueArray[0] === 'phone' // the key for phone_number is phone in getConfig but everywhere else we treat is as phone_number\n                    ? 'phone_number'\n                    : keyValueArray[0];\n            })\n            : undefined;\n        const parsedSignupAttributes = parseUserAttributes(userAttributes);\n        const parsedSocialProviders = loginWith?.oauth?.providers\n            ? loginWith.oauth.providers?.map((provider) => provider.toString().toLowerCase())\n            : undefined;\n        return {\n            ...cliConfig,\n            loginMechanisms: parsedLoginMechanisms,\n            signUpAttributes: parsedSignupAttributes,\n            socialProviders: parsedSocialProviders,\n        };\n    },\n    getCurrentUser,\n    handleSignIn: signIn,\n    handleSignUp: signUp,\n    handleConfirmSignIn: confirmSignIn,\n    handleConfirmSignUp: confirmSignUp,\n    handleForgotPasswordSubmit: confirmResetPassword,\n    handleForgotPassword: resetPassword,\n    handleResendSignUpCode: resendSignUpCode,\n    // Validation hooks for overriding\n    async validateCustomSignUp(_, __) { },\n    async validateFormPassword(formData, touchData, passwordSettings) {\n        const { password } = formData;\n        const { password: touched_password } = touchData;\n        /**\n         * If the password is not touched,\n         * or if the password settings are not set, we don't need to validate it.\n         */\n        if (!touched_password || !passwordSettings)\n            return null;\n        const password_complexity = [];\n        const policyMinLength = passwordSettings.minLength ?? DEFAULT_COGNITO_PASSWORD_MIN_LENGTH;\n        if (password.length < policyMinLength) {\n            password_complexity.push(`Password must have at least ${policyMinLength} characters`);\n        }\n        if (passwordSettings.requireLowercase && !/[a-z]/.test(password))\n            password_complexity.push('Password must have lower case letters');\n        if (passwordSettings.requireUppercase && !/[A-Z]/.test(password))\n            password_complexity.push('Password must have upper case letters');\n        if (passwordSettings.requireNumbers && !/[0-9]/.test(password))\n            password_complexity.push('Password must have numbers');\n        // https://docs.aws.amazon.com/cognito/latest/developerguide/user-pool-settings-policies.html\n        if (passwordSettings.requireSpecialCharacters && !hasSpecialChars(password))\n            password_complexity.push('Password must have special characters');\n        /**\n         * Only return an error if there is at least one error.\n         */\n        return password_complexity.length !== 0\n            ? { password: password_complexity }\n            : null;\n    },\n    async validateConfirmPassword(formData, touchData) {\n        const { password, confirm_password } = formData;\n        const { confirm_password: touched_confirm_password, password: touched_password, } = touchData;\n        if (!password && !confirm_password) {\n            // these inputs are clean, don't complain yet\n            return null;\n        }\n        else if ((password || confirm_password) &&\n            password !== confirm_password &&\n            ((touched_confirm_password && touched_password) ||\n                (password?.length >= 6 && confirm_password?.length >= 6))) {\n            // Only return an error if both fields have text entered,\n            // the passwords do not match, and the fields have been\n            // touched or the password and confirm password is longer then or equal to 6.\n            return {\n                confirm_password: 'Your passwords must match',\n            };\n        }\n    },\n    async validatePreferredUsername(_, __) { },\n};\n\nexport { defaultServices };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,MAAM,EAAEC,MAAM,EAAEC,cAAc,QAAQ,kBAAkB;AACtJ,OAAO,mCAAmC;AAC1C,OAAO,mBAAmB;AAC1B,OAAO,wCAAwC;AAC/C,OAAO,oCAAoC;AAC3C,OAAO,0CAA0C;AACjD,SAASC,eAAe,QAAQ,uCAAuC;AACvE,OAAO,yCAAyC;;AAEhD;AACA,MAAMC,mCAAmC,GAAG,CAAC;AAC7C,MAAMC,sBAAsB,GAAIC,cAAc,IAAKC,KAAK,CAACC,OAAO,CAACF,cAAc,CAAC;AAChF,MAAMG,mBAAmB,GAAIH,cAAc,IAAK;EAC5C,IAAI,CAACA,cAAc,EAAE;IACjB,OAAOI,SAAS;EACpB;EACA;EACA,IAAIL,sBAAsB,CAACC,cAAc,CAAC,EAAE;IACxC,OAAOK,MAAM,CAACC,OAAO,CAACN,cAAc,CAAC,CAACO,GAAG,CAAC,CAAC,CAACC,CAAC,EAAEC,KAAK,CAAC,KAAKJ,MAAM,CAACK,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EACpF;EACA,OAAOJ,MAAM,CAACK,IAAI,CAACV,cAAc,CAAC;AACtC,CAAC;AACD,MAAMW,eAAe,GAAG;EACdC,gBAAgBA,CAAA,EAAG;IAAA,OAAAC,iBAAA;MACrB,MAAMC,MAAM,GAAG1B,OAAO,CAAC2B,SAAS,CAAC,CAAC;MAClC,MAAMC,SAAS,GAAGF,MAAM,CAACG,IAAI,EAAEC,OAAO;MACtC,MAAM;QAAEC,SAAS;QAAEnB;MAAe,CAAC,GAAGc,MAAM,CAACG,IAAI,EAAEC,OAAO,IAAI,CAAC,CAAC;MAChE,MAAME,qBAAqB,GAAGD,SAAS,GACjCd,MAAM,CAACC,OAAO,CAACa,SAAS,CAAC,CACtBE,MAAM,CAAC,CAAC,CAACC,GAAG,EAAEC,MAAM,CAAC,KAAKD,GAAG,KAAK,OAAO,CAAC,CAC1CD,MAAM,CAAC,CAAC,CAACG,IAAI,EAAEf,KAAK,CAAC,KAAK,CAAC,CAACA,KAAK,CAAC,CAClCF,GAAG,CAAEkB,aAAa,IAAK;QACxB,OAAOA,aAAa,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC;QAAA,EAC9B,cAAc,GACdA,aAAa,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,GACArB,SAAS;MACf,MAAMsB,sBAAsB,GAAGvB,mBAAmB,CAACH,cAAc,CAAC;MAClE,MAAM2B,qBAAqB,GAAGR,SAAS,EAAES,KAAK,EAAEC,SAAS,GACnDV,SAAS,CAACS,KAAK,CAACC,SAAS,EAAEtB,GAAG,CAAEuB,QAAQ,IAAKA,QAAQ,CAACC,QAAQ,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,GAC/E5B,SAAS;MACf,OAAO;QACH,GAAGY,SAAS;QACZiB,eAAe,EAAEb,qBAAqB;QACtCc,gBAAgB,EAAER,sBAAsB;QACxCS,eAAe,EAAER;MACrB,CAAC;IAAC;EACN,CAAC;EACD/B,cAAc;EACdwC,YAAY,EAAEzC,MAAM;EACpB0C,YAAY,EAAE3C,MAAM;EACpB4C,mBAAmB,EAAE7C,aAAa;EAClC8C,mBAAmB,EAAE/C,aAAa;EAClCgD,0BAA0B,EAAEjD,oBAAoB;EAChDkD,oBAAoB,EAAEnD,aAAa;EACnCoD,sBAAsB,EAAErD,gBAAgB;EACxC;EACMsD,oBAAoBA,CAACnC,CAAC,EAAEoC,EAAE,EAAE;IAAA,OAAA/B,iBAAA;EAAE,CAAC;EAC/BgC,oBAAoBA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,gBAAgB,EAAE;IAAA,OAAAnC,iBAAA;MAC9D,MAAM;QAAEoC;MAAS,CAAC,GAAGH,QAAQ;MAC7B,MAAM;QAAEG,QAAQ,EAAEC;MAAiB,CAAC,GAAGH,SAAS;MAChD;AACR;AACA;AACA;MACQ,IAAI,CAACG,gBAAgB,IAAI,CAACF,gBAAgB,EACtC,OAAO,IAAI;MACf,MAAMG,mBAAmB,GAAG,EAAE;MAC9B,MAAMC,eAAe,GAAGJ,gBAAgB,CAACK,SAAS,IAAIvD,mCAAmC;MACzF,IAAImD,QAAQ,CAACK,MAAM,GAAGF,eAAe,EAAE;QACnCD,mBAAmB,CAACI,IAAI,CAAC,+BAA+BH,eAAe,aAAa,CAAC;MACzF;MACA,IAAIJ,gBAAgB,CAACQ,gBAAgB,IAAI,CAAC,OAAO,CAACC,IAAI,CAACR,QAAQ,CAAC,EAC5DE,mBAAmB,CAACI,IAAI,CAAC,uCAAuC,CAAC;MACrE,IAAIP,gBAAgB,CAACU,gBAAgB,IAAI,CAAC,OAAO,CAACD,IAAI,CAACR,QAAQ,CAAC,EAC5DE,mBAAmB,CAACI,IAAI,CAAC,uCAAuC,CAAC;MACrE,IAAIP,gBAAgB,CAACW,cAAc,IAAI,CAAC,OAAO,CAACF,IAAI,CAACR,QAAQ,CAAC,EAC1DE,mBAAmB,CAACI,IAAI,CAAC,4BAA4B,CAAC;MAC1D;MACA,IAAIP,gBAAgB,CAACY,wBAAwB,IAAI,CAAC/D,eAAe,CAACoD,QAAQ,CAAC,EACvEE,mBAAmB,CAACI,IAAI,CAAC,uCAAuC,CAAC;MACrE;AACR;AACA;MACQ,OAAOJ,mBAAmB,CAACG,MAAM,KAAK,CAAC,GACjC;QAAEL,QAAQ,EAAEE;MAAoB,CAAC,GACjC,IAAI;IAAC;EACf,CAAC;EACKU,uBAAuBA,CAACf,QAAQ,EAAEC,SAAS,EAAE;IAAA,OAAAlC,iBAAA;MAC/C,MAAM;QAAEoC,QAAQ;QAAEa;MAAiB,CAAC,GAAGhB,QAAQ;MAC/C,MAAM;QAAEgB,gBAAgB,EAAEC,wBAAwB;QAAEd,QAAQ,EAAEC;MAAkB,CAAC,GAAGH,SAAS;MAC7F,IAAI,CAACE,QAAQ,IAAI,CAACa,gBAAgB,EAAE;QAChC;QACA,OAAO,IAAI;MACf,CAAC,MACI,IAAI,CAACb,QAAQ,IAAIa,gBAAgB,KAClCb,QAAQ,KAAKa,gBAAgB,KAC3BC,wBAAwB,IAAIb,gBAAgB,IACzCD,QAAQ,EAAEK,MAAM,IAAI,CAAC,IAAIQ,gBAAgB,EAAER,MAAM,IAAI,CAAE,CAAC,EAAE;QAC/D;QACA;QACA;QACA,OAAO;UACHQ,gBAAgB,EAAE;QACtB,CAAC;MACL;IAAC;EACL,CAAC;EACKE,yBAAyBA,CAACxD,CAAC,EAAEoC,EAAE,EAAE;IAAA,OAAA/B,iBAAA;EAAE;AAC7C,CAAC;AAED,SAASF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}