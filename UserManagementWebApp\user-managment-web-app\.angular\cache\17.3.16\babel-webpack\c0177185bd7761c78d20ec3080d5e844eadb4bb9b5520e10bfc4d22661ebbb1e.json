{"ast": null, "code": "import { windowExists, keyPrefixMatch, processExists } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with svelte 3.59\nfunction svelteWebDetect() {\n  return windowExists() && keyPrefixMatch(window, '__SVELTE');\n}\nfunction svelteSSRDetect() {\n  return processExists() && typeof process.env !== 'undefined' && !!Object.keys(process.env).find(key => key.includes('svelte'));\n}\nexport { svelteSSRDetect, svelteWebDetect };", "map": {"version": 3, "names": ["windowExists", "keyPrefixMatch", "processExists", "svelteWebDetect", "window", "svelteSSRDetect", "process", "env", "Object", "keys", "find", "key", "includes"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Platform/detection/Svelte.mjs"], "sourcesContent": ["import { windowExists, keyPrefixMatch, processExists } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with svelte 3.59\nfunction svelteWebDetect() {\n    return windowExists() && keyPrefixMatch(window, '__SVELTE');\n}\nfunction svelteSSRDetect() {\n    return (processExists() &&\n        typeof process.env !== 'undefined' &&\n        !!Object.keys(process.env).find(key => key.includes('svelte')));\n}\n\nexport { svelteSSRDetect, svelteWebDetect };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,cAAc,EAAEC,aAAa,QAAQ,eAAe;;AAE3E;AACA;AACA;AACA,SAASC,eAAeA,CAAA,EAAG;EACvB,OAAOH,YAAY,CAAC,CAAC,IAAIC,cAAc,CAACG,MAAM,EAAE,UAAU,CAAC;AAC/D;AACA,SAASC,eAAeA,CAAA,EAAG;EACvB,OAAQH,aAAa,CAAC,CAAC,IACnB,OAAOI,OAAO,CAACC,GAAG,KAAK,WAAW,IAClC,CAAC,CAACC,MAAM,CAACC,IAAI,CAACH,OAAO,CAACC,GAAG,CAAC,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACtE;AAEA,SAASP,eAAe,EAAEF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}