{"ast": null, "code": "export { composeServiceApi } from './composeServiceApi.mjs';\nexport { composeTransferHandler } from './composeTransferHandler.mjs';", "map": {"version": 3, "names": ["composeServiceApi", "composeTransferHandler"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/internal/index.mjs"], "sourcesContent": ["export { composeServiceApi } from './composeServiceApi.mjs';\nexport { composeTransferHandler } from './composeTransferHandler.mjs';\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,sBAAsB,QAAQ,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}