{"ast": null, "code": "import { composeService<PERSON><PERSON> } from '@aws-amplify/core/internals/aws-client-utils/composers';\nimport { createUserPoolSerializer } from './shared/serde/createUserPoolSerializer.mjs';\nimport { createUserPoolDeserializer } from './shared/serde/createUserPoolDeserializer.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '@aws-amplify/core/internals/utils';\nimport { cognitoUserPoolTransferHandler } from './shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport { DEFAULT_SERVICE_CLIENT_API_CONFIG } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createInitiateAuthClient = config => composeServiceApi(cognitoUserPoolTransferHandler, createUserPoolSerializer('InitiateAuth'), createUserPoolDeserializer(), {\n  ...DEFAULT_SERVICE_CLIENT_API_CONFIG,\n  ...config\n});\nexport { createInitiateAuthClient };", "map": {"version": 3, "names": ["composeServiceApi", "createUserPoolSerializer", "createUserPoolDeserializer", "cognitoUserPoolTransferHandler", "DEFAULT_SERVICE_CLIENT_API_CONFIG", "createInitiateAuthClient", "config"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/factories/serviceClients/cognitoIdentityProvider/createInitiateAuthClient.mjs"], "sourcesContent": ["import { composeService<PERSON><PERSON> } from '@aws-amplify/core/internals/aws-client-utils/composers';\nimport { createUserPoolSerializer } from './shared/serde/createUserPoolSerializer.mjs';\nimport { createUserPoolDeserializer } from './shared/serde/createUserPoolDeserializer.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '@aws-amplify/core/internals/utils';\nimport { cognitoUserPoolTransferHandler } from './shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport { DEFAULT_SERVICE_CLIENT_API_CONFIG } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createInitiateAuthClient = (config) => composeServiceApi(cognitoUserPoolTransferHandler, createUserPoolSerializer('InitiateAuth'), createUserPoolDeserializer(), {\n    ...DEFAULT_SERVICE_CLIENT_API_CONFIG,\n    ...config,\n});\n\nexport { createInitiateAuthClient };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,wDAAwD;AAC1F,SAASC,wBAAwB,QAAQ,6CAA6C;AACtF,SAASC,0BAA0B,QAAQ,+CAA+C;AAC1F,OAAO,8CAA8C;AACrD,OAAO,mCAAmC;AAC1C,SAASC,8BAA8B,QAAQ,qDAAqD;AACpG,SAASC,iCAAiC,QAAQ,iBAAiB;;AAEnE;AACA;AACA,MAAMC,wBAAwB,GAAIC,MAAM,IAAKN,iBAAiB,CAACG,8BAA8B,EAAEF,wBAAwB,CAAC,cAAc,CAAC,EAAEC,0BAA0B,CAAC,CAAC,EAAE;EACnK,GAAGE,iCAAiC;EACpC,GAAGE;AACP,CAAC,CAAC;AAEF,SAASD,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}