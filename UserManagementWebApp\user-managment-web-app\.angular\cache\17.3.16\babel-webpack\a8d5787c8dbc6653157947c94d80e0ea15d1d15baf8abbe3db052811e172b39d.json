{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HubInternal } from '@aws-amplify/core/internals/utils';\nimport { signIn } from '../apis/signIn.mjs';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { setAutoSignIn, resetAutoSignIn } from '../apis/autoSignIn.mjs';\nimport { AUTO_SIGN_IN_EXCEPTION } from '../../../errors/constants.mjs';\nimport { signInWithUserAuth } from '../apis/signInWithUserAuth.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst MAX_AUTOSIGNIN_POLLING_MS = 3 * 60 * 1000;\nfunction handleCodeAutoSignIn(signInInput) {\n  const stopHubListener = HubInternal.listen('auth-internal', /*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(function* ({\n      payload\n    }) {\n      switch (payload.event) {\n        case 'confirmSignUp':\n          {\n            const response = payload.data;\n            if (response?.isSignUpComplete) {\n              HubInternal.dispatch('auth-internal', {\n                event: 'autoSignIn'\n              });\n              setAutoSignIn(autoSignInWithCode(signInInput));\n              stopHubListener();\n            }\n          }\n      }\n    });\n    return function (_x) {\n      return _ref.apply(this, arguments);\n    };\n  }());\n  // This will stop the listener if confirmSignUp is not resolved.\n  const timeOutId = setTimeout(() => {\n    stopHubListener();\n    clearTimeout(timeOutId);\n    resetAutoSignIn();\n  }, MAX_AUTOSIGNIN_POLLING_MS);\n}\nfunction debounce(fun, delay) {\n  let timer;\n  return args => {\n    if (!timer) {\n      fun(...args);\n    }\n    clearTimeout(timer);\n    timer = setTimeout(() => {\n      timer = undefined;\n    }, delay);\n  };\n}\nfunction handleAutoSignInWithLink(signInInput, resolve, reject) {\n  const start = Date.now();\n  const autoSignInPollingIntervalId = setInterval(/*#__PURE__*/_asyncToGenerator(function* () {\n    const elapsedTime = Date.now() - start;\n    const maxTime = MAX_AUTOSIGNIN_POLLING_MS;\n    if (elapsedTime > maxTime) {\n      clearInterval(autoSignInPollingIntervalId);\n      reject(new AuthError({\n        name: AUTO_SIGN_IN_EXCEPTION,\n        message: 'The account was not confirmed on time.',\n        recoverySuggestion: 'Try to verify your account by clicking the link sent your email or phone and then login manually.'\n      }));\n      resetAutoSignIn();\n    } else {\n      try {\n        const signInOutput = yield signIn(signInInput);\n        if (signInOutput.nextStep.signInStep !== 'CONFIRM_SIGN_UP') {\n          resolve(signInOutput);\n          clearInterval(autoSignInPollingIntervalId);\n          resetAutoSignIn();\n        }\n      } catch (error) {\n        clearInterval(autoSignInPollingIntervalId);\n        reject(error);\n        resetAutoSignIn();\n      }\n    }\n  }), 5000);\n}\nconst debouncedAutoSignInWithLink = debounce(handleAutoSignInWithLink, 300);\nconst debouncedAutoSignWithCodeOrUserConfirmed = debounce(handleAutoSignInWithCodeOrUserConfirmed, 300);\nfunction autoSignInWhenUserIsConfirmedWithLink(signInInput) {\n  return /*#__PURE__*/_asyncToGenerator(function* () {\n    return new Promise((resolve, reject) => {\n      debouncedAutoSignInWithLink([signInInput, resolve, reject]);\n    });\n  });\n}\nfunction handleAutoSignInWithCodeOrUserConfirmed(_x2, _x3, _x4) {\n  return _handleAutoSignInWithCodeOrUserConfirmed.apply(this, arguments);\n}\nfunction _handleAutoSignInWithCodeOrUserConfirmed() {\n  _handleAutoSignInWithCodeOrUserConfirmed = _asyncToGenerator(function* (signInInput, resolve, reject) {\n    try {\n      const output = signInInput?.options?.authFlowType === 'USER_AUTH' ? yield signInWithUserAuth(signInInput) : yield signIn(signInInput);\n      resolve(output);\n      resetAutoSignIn();\n    } catch (error) {\n      reject(error);\n      resetAutoSignIn();\n    }\n  });\n  return _handleAutoSignInWithCodeOrUserConfirmed.apply(this, arguments);\n}\nfunction autoSignInWithCode(signInInput) {\n  return /*#__PURE__*/_asyncToGenerator(function* () {\n    return new Promise((resolve, reject) => {\n      debouncedAutoSignWithCodeOrUserConfirmed([signInInput, resolve, reject]);\n    });\n  });\n}\nconst autoSignInUserConfirmed = autoSignInWithCode;\nexport { autoSignInUserConfirmed, autoSignInWhenUserIsConfirmedWithLink, handleCodeAutoSignIn };", "map": {"version": 3, "names": ["HubInternal", "signIn", "<PERSON>th<PERSON><PERSON><PERSON>", "setAutoSignIn", "resetAutoSignIn", "AUTO_SIGN_IN_EXCEPTION", "signInWithUserAuth", "MAX_AUTOSIGNIN_POLLING_MS", "handleCodeAutoSignIn", "signInInput", "stopHubListener", "listen", "_ref", "_asyncToGenerator", "payload", "event", "response", "data", "isSignUpComplete", "dispatch", "autoSignInWithCode", "_x", "apply", "arguments", "timeOutId", "setTimeout", "clearTimeout", "debounce", "fun", "delay", "timer", "args", "undefined", "handleAutoSignInWithLink", "resolve", "reject", "start", "Date", "now", "autoSignInPollingIntervalId", "setInterval", "elapsedTime", "maxTime", "clearInterval", "name", "message", "recoverySuggestion", "signInOutput", "nextStep", "signInStep", "error", "debouncedAutoSignInWithLink", "debouncedAutoSignWithCodeOrUserConfirmed", "handleAutoSignInWithCodeOrUserConfirmed", "autoSignInWhenUserIsConfirmedWithLink", "Promise", "_x2", "_x3", "_x4", "_handleAutoSignInWithCodeOrUserConfirmed", "output", "options", "authFlowType", "autoSignInUserConfirmed"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/signUpHelpers.mjs"], "sourcesContent": ["import { HubInternal } from '@aws-amplify/core/internals/utils';\nimport { signIn } from '../apis/signIn.mjs';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { setAutoSignIn, resetAutoSignIn } from '../apis/autoSignIn.mjs';\nimport { AUTO_SIGN_IN_EXCEPTION } from '../../../errors/constants.mjs';\nimport { signInWithUserAuth } from '../apis/signInWithUserAuth.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst MAX_AUTOSIGNIN_POLLING_MS = 3 * 60 * 1000;\nfunction handleCodeAutoSignIn(signInInput) {\n    const stopHubListener = HubInternal.listen('auth-internal', async ({ payload }) => {\n        switch (payload.event) {\n            case 'confirmSignUp': {\n                const response = payload.data;\n                if (response?.isSignUpComplete) {\n                    HubInternal.dispatch('auth-internal', {\n                        event: 'autoSignIn',\n                    });\n                    setAutoSignIn(autoSignInWithCode(signInInput));\n                    stopHubListener();\n                }\n            }\n        }\n    });\n    // This will stop the listener if confirmSignUp is not resolved.\n    const timeOutId = setTimeout(() => {\n        stopHubListener();\n        clearTimeout(timeOutId);\n        resetAutoSignIn();\n    }, MAX_AUTOSIGNIN_POLLING_MS);\n}\nfunction debounce(fun, delay) {\n    let timer;\n    return (args) => {\n        if (!timer) {\n            fun(...args);\n        }\n        clearTimeout(timer);\n        timer = setTimeout(() => {\n            timer = undefined;\n        }, delay);\n    };\n}\nfunction handleAutoSignInWithLink(signInInput, resolve, reject) {\n    const start = Date.now();\n    const autoSignInPollingIntervalId = setInterval(async () => {\n        const elapsedTime = Date.now() - start;\n        const maxTime = MAX_AUTOSIGNIN_POLLING_MS;\n        if (elapsedTime > maxTime) {\n            clearInterval(autoSignInPollingIntervalId);\n            reject(new AuthError({\n                name: AUTO_SIGN_IN_EXCEPTION,\n                message: 'The account was not confirmed on time.',\n                recoverySuggestion: 'Try to verify your account by clicking the link sent your email or phone and then login manually.',\n            }));\n            resetAutoSignIn();\n        }\n        else {\n            try {\n                const signInOutput = await signIn(signInInput);\n                if (signInOutput.nextStep.signInStep !== 'CONFIRM_SIGN_UP') {\n                    resolve(signInOutput);\n                    clearInterval(autoSignInPollingIntervalId);\n                    resetAutoSignIn();\n                }\n            }\n            catch (error) {\n                clearInterval(autoSignInPollingIntervalId);\n                reject(error);\n                resetAutoSignIn();\n            }\n        }\n    }, 5000);\n}\nconst debouncedAutoSignInWithLink = debounce(handleAutoSignInWithLink, 300);\nconst debouncedAutoSignWithCodeOrUserConfirmed = debounce(handleAutoSignInWithCodeOrUserConfirmed, 300);\nfunction autoSignInWhenUserIsConfirmedWithLink(signInInput) {\n    return async () => {\n        return new Promise((resolve, reject) => {\n            debouncedAutoSignInWithLink([signInInput, resolve, reject]);\n        });\n    };\n}\nasync function handleAutoSignInWithCodeOrUserConfirmed(signInInput, resolve, reject) {\n    try {\n        const output = signInInput?.options?.authFlowType === 'USER_AUTH'\n            ? await signInWithUserAuth(signInInput)\n            : await signIn(signInInput);\n        resolve(output);\n        resetAutoSignIn();\n    }\n    catch (error) {\n        reject(error);\n        resetAutoSignIn();\n    }\n}\nfunction autoSignInWithCode(signInInput) {\n    return async () => {\n        return new Promise((resolve, reject) => {\n            debouncedAutoSignWithCodeOrUserConfirmed([signInInput, resolve, reject]);\n        });\n    };\n}\nconst autoSignInUserConfirmed = autoSignInWithCode;\n\nexport { autoSignInUserConfirmed, autoSignInWhenUserIsConfirmedWithLink, handleCodeAutoSignIn };\n"], "mappings": ";AAAA,SAASA,WAAW,QAAQ,mCAAmC;AAC/D,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,aAAa,EAAEC,eAAe,QAAQ,wBAAwB;AACvE,SAASC,sBAAsB,QAAQ,+BAA+B;AACtE,SAASC,kBAAkB,QAAQ,gCAAgC;;AAEnE;AACA;AACA,MAAMC,yBAAyB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;AAC/C,SAASC,oBAAoBA,CAACC,WAAW,EAAE;EACvC,MAAMC,eAAe,GAAGV,WAAW,CAACW,MAAM,CAAC,eAAe;IAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAE,WAAO;MAAEC;IAAQ,CAAC,EAAK;MAC/E,QAAQA,OAAO,CAACC,KAAK;QACjB,KAAK,eAAe;UAAE;YAClB,MAAMC,QAAQ,GAAGF,OAAO,CAACG,IAAI;YAC7B,IAAID,QAAQ,EAAEE,gBAAgB,EAAE;cAC5BlB,WAAW,CAACmB,QAAQ,CAAC,eAAe,EAAE;gBAClCJ,KAAK,EAAE;cACX,CAAC,CAAC;cACFZ,aAAa,CAACiB,kBAAkB,CAACX,WAAW,CAAC,CAAC;cAC9CC,eAAe,CAAC,CAAC;YACrB;UACJ;MACJ;IACJ,CAAC;IAAA,iBAAAW,EAAA;MAAA,OAAAT,IAAA,CAAAU,KAAA,OAAAC,SAAA;IAAA;EAAA,IAAC;EACF;EACA,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAM;IAC/Bf,eAAe,CAAC,CAAC;IACjBgB,YAAY,CAACF,SAAS,CAAC;IACvBpB,eAAe,CAAC,CAAC;EACrB,CAAC,EAAEG,yBAAyB,CAAC;AACjC;AACA,SAASoB,QAAQA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAC1B,IAAIC,KAAK;EACT,OAAQC,IAAI,IAAK;IACb,IAAI,CAACD,KAAK,EAAE;MACRF,GAAG,CAAC,GAAGG,IAAI,CAAC;IAChB;IACAL,YAAY,CAACI,KAAK,CAAC;IACnBA,KAAK,GAAGL,UAAU,CAAC,MAAM;MACrBK,KAAK,GAAGE,SAAS;IACrB,CAAC,EAAEH,KAAK,CAAC;EACb,CAAC;AACL;AACA,SAASI,wBAAwBA,CAACxB,WAAW,EAAEyB,OAAO,EAAEC,MAAM,EAAE;EAC5D,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EACxB,MAAMC,2BAA2B,GAAGC,WAAW,cAAA3B,iBAAA,CAAC,aAAY;IACxD,MAAM4B,WAAW,GAAGJ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,KAAK;IACtC,MAAMM,OAAO,GAAGnC,yBAAyB;IACzC,IAAIkC,WAAW,GAAGC,OAAO,EAAE;MACvBC,aAAa,CAACJ,2BAA2B,CAAC;MAC1CJ,MAAM,CAAC,IAAIjC,SAAS,CAAC;QACjB0C,IAAI,EAAEvC,sBAAsB;QAC5BwC,OAAO,EAAE,wCAAwC;QACjDC,kBAAkB,EAAE;MACxB,CAAC,CAAC,CAAC;MACH1C,eAAe,CAAC,CAAC;IACrB,CAAC,MACI;MACD,IAAI;QACA,MAAM2C,YAAY,SAAS9C,MAAM,CAACQ,WAAW,CAAC;QAC9C,IAAIsC,YAAY,CAACC,QAAQ,CAACC,UAAU,KAAK,iBAAiB,EAAE;UACxDf,OAAO,CAACa,YAAY,CAAC;UACrBJ,aAAa,CAACJ,2BAA2B,CAAC;UAC1CnC,eAAe,CAAC,CAAC;QACrB;MACJ,CAAC,CACD,OAAO8C,KAAK,EAAE;QACVP,aAAa,CAACJ,2BAA2B,CAAC;QAC1CJ,MAAM,CAACe,KAAK,CAAC;QACb9C,eAAe,CAAC,CAAC;MACrB;IACJ;EACJ,CAAC,GAAE,IAAI,CAAC;AACZ;AACA,MAAM+C,2BAA2B,GAAGxB,QAAQ,CAACM,wBAAwB,EAAE,GAAG,CAAC;AAC3E,MAAMmB,wCAAwC,GAAGzB,QAAQ,CAAC0B,uCAAuC,EAAE,GAAG,CAAC;AACvG,SAASC,qCAAqCA,CAAC7C,WAAW,EAAE;EACxD,oBAAAI,iBAAA,CAAO,aAAY;IACf,OAAO,IAAI0C,OAAO,CAAC,CAACrB,OAAO,EAAEC,MAAM,KAAK;MACpCgB,2BAA2B,CAAC,CAAC1C,WAAW,EAAEyB,OAAO,EAAEC,MAAM,CAAC,CAAC;IAC/D,CAAC,CAAC;EACN,CAAC;AACL;AAAC,SACckB,uCAAuCA,CAAAG,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,wCAAA,CAAArC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAoC,yCAAA;EAAAA,wCAAA,GAAA9C,iBAAA,CAAtD,WAAuDJ,WAAW,EAAEyB,OAAO,EAAEC,MAAM,EAAE;IACjF,IAAI;MACA,MAAMyB,MAAM,GAAGnD,WAAW,EAAEoD,OAAO,EAAEC,YAAY,KAAK,WAAW,SACrDxD,kBAAkB,CAACG,WAAW,CAAC,SAC/BR,MAAM,CAACQ,WAAW,CAAC;MAC/ByB,OAAO,CAAC0B,MAAM,CAAC;MACfxD,eAAe,CAAC,CAAC;IACrB,CAAC,CACD,OAAO8C,KAAK,EAAE;MACVf,MAAM,CAACe,KAAK,CAAC;MACb9C,eAAe,CAAC,CAAC;IACrB;EACJ,CAAC;EAAA,OAAAuD,wCAAA,CAAArC,KAAA,OAAAC,SAAA;AAAA;AACD,SAASH,kBAAkBA,CAACX,WAAW,EAAE;EACrC,oBAAAI,iBAAA,CAAO,aAAY;IACf,OAAO,IAAI0C,OAAO,CAAC,CAACrB,OAAO,EAAEC,MAAM,KAAK;MACpCiB,wCAAwC,CAAC,CAAC3C,WAAW,EAAEyB,OAAO,EAAEC,MAAM,CAAC,CAAC;IAC5E,CAAC,CAAC;EACN,CAAC;AACL;AACA,MAAM4B,uBAAuB,GAAG3C,kBAAkB;AAElD,SAAS2C,uBAAuB,EAAET,qCAAqC,EAAE9C,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}