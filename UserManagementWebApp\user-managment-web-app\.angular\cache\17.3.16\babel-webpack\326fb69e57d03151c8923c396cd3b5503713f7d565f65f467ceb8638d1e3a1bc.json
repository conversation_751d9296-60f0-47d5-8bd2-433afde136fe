{"ast": null, "code": "import pickBy from 'lodash/pickBy.js';\nimport '@aws-amplify/core/internals/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { isString } from '../../utils/utils.mjs';\n\n// default `autoSignIn` flag is `true`\nconst DEFAULT_AUTO_SIGN_IN = true;\nconst EMPTY_STRING = '';\nconst sanitizePhoneNumber = (dialCode, phoneNumber) => `${dialCode}${phoneNumber}`.replace(/[^A-Z0-9+]/gi, '');\nconst selectUserAttributes = (_, key) => {\n  // Allowlist of Cognito User Pool Attributes (from OpenID Connect specification)\n  // See: https://docs.aws.amazon.com/cognito/latest/developerguide/user-pool-settings-attributes.html\n  switch (key) {\n    case 'address':\n    case 'birthdate':\n    case 'email':\n    case 'family_name':\n    case 'gender':\n    case 'given_name':\n    case 'locale':\n    case 'middle_name':\n    case 'name':\n    case 'nickname':\n    case 'phone_number':\n    case 'picture':\n    case 'preferred_username':\n    case 'profile':\n    case 'updated_at':\n    case 'website':\n    case 'zoneinfo':\n      return true;\n    // Otherwise, it's a custom attribute\n    default:\n      return key.startsWith('custom:');\n  }\n};\nconst getUserAttributes = formValues => {\n  const {\n    phone_number,\n    ...userAttributes\n  } = pickBy(formValues, selectUserAttributes);\n  // only include `phone_number` attribute in `userAttributes` if it has a value\n  if (isString(phone_number) && phone_number !== EMPTY_STRING) {\n    const {\n      country_code\n    } = formValues;\n    return {\n      ...userAttributes,\n      phone_number: sanitizePhoneNumber(country_code, phone_number)\n    };\n  }\n  return userAttributes;\n};\nconst getSignUpInput = (username, formValues, loginMechanism) => {\n  const {\n    password,\n    ...values\n  } = formValues;\n  const attributes = getUserAttributes(values);\n  const options = {\n    autoSignIn: DEFAULT_AUTO_SIGN_IN,\n    userAttributes: {\n      // use `username` value for `phone_number`\n      ...(loginMechanism === 'phone_number' ? {\n        ...attributes,\n        phone_number: username\n      } : attributes)\n    }\n  };\n  return {\n    username,\n    password,\n    options\n  };\n};\nconst getUsernameSignUp = ({\n  formValues,\n  loginMechanisms\n}) => {\n  const loginMechanism = loginMechanisms[0];\n  if (loginMechanism === 'phone_number') {\n    const {\n      country_code,\n      phone_number\n    } = formValues;\n    return sanitizePhoneNumber(country_code, phone_number);\n  }\n  return formValues[loginMechanism];\n};\nexport { getSignUpInput, getUserAttributes, getUsernameSignUp, sanitizePhoneNumber };", "map": {"version": 3, "names": ["pickBy", "isString", "DEFAULT_AUTO_SIGN_IN", "EMPTY_STRING", "sanitizePhoneNumber", "dialCode", "phoneNumber", "replace", "selectUserAttributes", "_", "key", "startsWith", "getUserAttributes", "formValues", "phone_number", "userAttributes", "country_code", "getSignUpInput", "username", "loginMechanism", "password", "values", "attributes", "options", "autoSignIn", "getUsernameSignUp", "loginMechanisms"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/machines/authenticator/utils.mjs"], "sourcesContent": ["import pickBy from 'lodash/pickBy.js';\nimport '@aws-amplify/core/internals/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { isString } from '../../utils/utils.mjs';\n\n// default `autoSignIn` flag is `true`\nconst DEFAULT_AUTO_SIGN_IN = true;\nconst EMPTY_STRING = '';\nconst sanitizePhoneNumber = (dialCode, phoneNumber) => `${dialCode}${phoneNumber}`.replace(/[^A-Z0-9+]/gi, '');\nconst selectUserAttributes = (_, key) => {\n    // Allowlist of Cognito User Pool Attributes (from OpenID Connect specification)\n    // See: https://docs.aws.amazon.com/cognito/latest/developerguide/user-pool-settings-attributes.html\n    switch (key) {\n        case 'address':\n        case 'birthdate':\n        case 'email':\n        case 'family_name':\n        case 'gender':\n        case 'given_name':\n        case 'locale':\n        case 'middle_name':\n        case 'name':\n        case 'nickname':\n        case 'phone_number':\n        case 'picture':\n        case 'preferred_username':\n        case 'profile':\n        case 'updated_at':\n        case 'website':\n        case 'zoneinfo':\n            return true;\n        // Otherwise, it's a custom attribute\n        default:\n            return key.startsWith('custom:');\n    }\n};\nconst getUserAttributes = (formValues) => {\n    const { phone_number, ...userAttributes } = pickBy(formValues, selectUserAttributes);\n    // only include `phone_number` attribute in `userAttributes` if it has a value\n    if (isString(phone_number) && phone_number !== EMPTY_STRING) {\n        const { country_code } = formValues;\n        return {\n            ...userAttributes,\n            phone_number: sanitizePhoneNumber(country_code, phone_number),\n        };\n    }\n    return userAttributes;\n};\nconst getSignUpInput = (username, formValues, loginMechanism) => {\n    const { password, ...values } = formValues;\n    const attributes = getUserAttributes(values);\n    const options = {\n        autoSignIn: DEFAULT_AUTO_SIGN_IN,\n        userAttributes: {\n            // use `username` value for `phone_number`\n            ...(loginMechanism === 'phone_number'\n                ? { ...attributes, phone_number: username }\n                : attributes),\n        },\n    };\n    return { username, password, options };\n};\nconst getUsernameSignUp = ({ formValues, loginMechanisms, }) => {\n    const loginMechanism = loginMechanisms[0];\n    if (loginMechanism === 'phone_number') {\n        const { country_code, phone_number } = formValues;\n        return sanitizePhoneNumber(country_code, phone_number);\n    }\n    return formValues[loginMechanism];\n};\n\nexport { getSignUpInput, getUserAttributes, getUsernameSignUp, sanitizePhoneNumber };\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,kBAAkB;AACrC,OAAO,mCAAmC;AAC1C,OAAO,wCAAwC;AAC/C,SAASC,QAAQ,QAAQ,uBAAuB;;AAEhD;AACA,MAAMC,oBAAoB,GAAG,IAAI;AACjC,MAAMC,YAAY,GAAG,EAAE;AACvB,MAAMC,mBAAmB,GAAGA,CAACC,QAAQ,EAAEC,WAAW,KAAK,GAAGD,QAAQ,GAAGC,WAAW,EAAE,CAACC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;AAC9G,MAAMC,oBAAoB,GAAGA,CAACC,CAAC,EAAEC,GAAG,KAAK;EACrC;EACA;EACA,QAAQA,GAAG;IACP,KAAK,SAAS;IACd,KAAK,WAAW;IAChB,KAAK,OAAO;IACZ,KAAK,aAAa;IAClB,KAAK,QAAQ;IACb,KAAK,YAAY;IACjB,KAAK,QAAQ;IACb,KAAK,aAAa;IAClB,KAAK,MAAM;IACX,KAAK,UAAU;IACf,KAAK,cAAc;IACnB,KAAK,SAAS;IACd,KAAK,oBAAoB;IACzB,KAAK,SAAS;IACd,KAAK,YAAY;IACjB,KAAK,SAAS;IACd,KAAK,UAAU;MACX,OAAO,IAAI;IACf;IACA;MACI,OAAOA,GAAG,CAACC,UAAU,CAAC,SAAS,CAAC;EACxC;AACJ,CAAC;AACD,MAAMC,iBAAiB,GAAIC,UAAU,IAAK;EACtC,MAAM;IAAEC,YAAY;IAAE,GAAGC;EAAe,CAAC,GAAGf,MAAM,CAACa,UAAU,EAAEL,oBAAoB,CAAC;EACpF;EACA,IAAIP,QAAQ,CAACa,YAAY,CAAC,IAAIA,YAAY,KAAKX,YAAY,EAAE;IACzD,MAAM;MAAEa;IAAa,CAAC,GAAGH,UAAU;IACnC,OAAO;MACH,GAAGE,cAAc;MACjBD,YAAY,EAAEV,mBAAmB,CAACY,YAAY,EAAEF,YAAY;IAChE,CAAC;EACL;EACA,OAAOC,cAAc;AACzB,CAAC;AACD,MAAME,cAAc,GAAGA,CAACC,QAAQ,EAAEL,UAAU,EAAEM,cAAc,KAAK;EAC7D,MAAM;IAAEC,QAAQ;IAAE,GAAGC;EAAO,CAAC,GAAGR,UAAU;EAC1C,MAAMS,UAAU,GAAGV,iBAAiB,CAACS,MAAM,CAAC;EAC5C,MAAME,OAAO,GAAG;IACZC,UAAU,EAAEtB,oBAAoB;IAChCa,cAAc,EAAE;MACZ;MACA,IAAII,cAAc,KAAK,cAAc,GAC/B;QAAE,GAAGG,UAAU;QAAER,YAAY,EAAEI;MAAS,CAAC,GACzCI,UAAU;IACpB;EACJ,CAAC;EACD,OAAO;IAAEJ,QAAQ;IAAEE,QAAQ;IAAEG;EAAQ,CAAC;AAC1C,CAAC;AACD,MAAME,iBAAiB,GAAGA,CAAC;EAAEZ,UAAU;EAAEa;AAAiB,CAAC,KAAK;EAC5D,MAAMP,cAAc,GAAGO,eAAe,CAAC,CAAC,CAAC;EACzC,IAAIP,cAAc,KAAK,cAAc,EAAE;IACnC,MAAM;MAAEH,YAAY;MAAEF;IAAa,CAAC,GAAGD,UAAU;IACjD,OAAOT,mBAAmB,CAACY,YAAY,EAAEF,YAAY,CAAC;EAC1D;EACA,OAAOD,UAAU,CAACM,cAAc,CAAC;AACrC,CAAC;AAED,SAASF,cAAc,EAAEL,iBAAiB,EAAEa,iBAAiB,EAAErB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}