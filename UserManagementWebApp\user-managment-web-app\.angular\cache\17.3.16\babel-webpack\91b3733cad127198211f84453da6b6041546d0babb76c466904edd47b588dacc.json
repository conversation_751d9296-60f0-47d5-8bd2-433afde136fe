{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { getUserContextData } from '../utils/userContextData.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../types/errors.mjs';\nimport { createForgotPasswordClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createForgotPasswordClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Resets a user's password.\n *\n * @param input -  The ResetPasswordInput object.\n * @returns ResetPasswordOutput\n * @throws -{@link ForgotPasswordException }\n * Thrown due to an invalid confirmation code or password.\n * @throws -{@link AuthValidationErrorCode }\n * Thrown due to an empty username.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n **/\nfunction resetPassword(_x) {\n  return _resetPassword.apply(this, arguments);\n}\nfunction _resetPassword() {\n  _resetPassword = _asyncToGenerator(function* (input) {\n    const {\n      username\n    } = input;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptyResetPasswordUsername);\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolClientId,\n      userPoolId,\n      userPoolEndpoint\n    } = authConfig;\n    const clientMetadata = input.options?.clientMetadata;\n    const UserContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const forgotPassword = createForgotPasswordClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const res = yield forgotPassword({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.ResetPassword)\n    }, {\n      Username: username,\n      ClientMetadata: clientMetadata,\n      ClientId: userPoolClientId,\n      UserContextData\n    });\n    const codeDeliveryDetails = res.CodeDeliveryDetails;\n    return {\n      isPasswordReset: false,\n      nextStep: {\n        resetPasswordStep: 'CONFIRM_RESET_PASSWORD_WITH_CODE',\n        codeDeliveryDetails: {\n          deliveryMedium: codeDeliveryDetails?.DeliveryMedium,\n          destination: codeDeliveryDetails?.Destination,\n          attributeName: codeDeliveryDetails?.AttributeName\n        }\n      }\n    };\n  });\n  return _resetPassword.apply(this, arguments);\n}\nexport { resetPassword };", "map": {"version": 3, "names": ["Amplify", "assertTokenProviderConfig", "AuthAction", "AuthValidationErrorCode", "assertValidationError", "getRegionFromUserPoolId", "getAuthUserAgentValue", "getUserContextData", "createForgotPasswordClient", "createCognitoUserPoolEndpointResolver", "resetPassword", "_x", "_resetPassword", "apply", "arguments", "_asyncToGenerator", "input", "username", "EmptyResetPasswordUsername", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userPoolClientId", "userPoolId", "userPoolEndpoint", "clientMetadata", "options", "UserContextData", "forgotPassword", "endpointResolver", "endpointOverride", "res", "region", "userAgentValue", "ResetPassword", "Username", "ClientMetadata", "ClientId", "codeDeliveryDetails", "CodeDeliveryDetails", "isPasswordReset", "nextStep", "resetPasswordStep", "deliveryMedium", "DeliveryMedium", "destination", "Destination", "attributeName", "AttributeName"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/resetPassword.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { getUserContextData } from '../utils/userContextData.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../types/errors.mjs';\nimport { createForgotPasswordClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createForgotPasswordClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Resets a user's password.\n *\n * @param input -  The ResetPasswordInput object.\n * @returns ResetPasswordOutput\n * @throws -{@link ForgotPasswordException }\n * Thrown due to an invalid confirmation code or password.\n * @throws -{@link AuthValidationErrorCode }\n * Thrown due to an empty username.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n **/\nasync function resetPassword(input) {\n    const { username } = input;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptyResetPasswordUsername);\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolClientId, userPoolId, userPoolEndpoint } = authConfig;\n    const clientMetadata = input.options?.clientMetadata;\n    const UserContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const forgotPassword = createForgotPasswordClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const res = await forgotPassword({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ResetPassword),\n    }, {\n        Username: username,\n        ClientMetadata: clientMetadata,\n        ClientId: userPoolClientId,\n        UserContextData,\n    });\n    const codeDeliveryDetails = res.CodeDeliveryDetails;\n    return {\n        isPasswordReset: false,\n        nextStep: {\n            resetPasswordStep: 'CONFIRM_RESET_PASSWORD_WITH_CODE',\n            codeDeliveryDetails: {\n                deliveryMedium: codeDeliveryDetails?.DeliveryMedium,\n                destination: codeDeliveryDetails?.Destination,\n                attributeName: codeDeliveryDetails?.AttributeName,\n            },\n        },\n    };\n}\n\nexport { resetPassword };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,qBAAqB;AAC5B,SAASC,0BAA0B,QAAQ,qGAAqG;AAChJ,SAASC,qCAAqC,QAAQ,wDAAwD;;AAE9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,SAWeC,aAAaA,CAAAC,EAAA;EAAA,OAAAC,cAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,eAAA;EAAAA,cAAA,GAAAG,iBAAA,CAA5B,WAA6BC,KAAK,EAAE;IAChC,MAAM;MAAEC;IAAS,CAAC,GAAGD,KAAK;IAC1BZ,qBAAqB,CAAC,CAAC,CAACa,QAAQ,EAAEd,uBAAuB,CAACe,0BAA0B,CAAC;IACrF,MAAMC,UAAU,GAAGnB,OAAO,CAACoB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDrB,yBAAyB,CAACkB,UAAU,CAAC;IACrC,MAAM;MAAEI,gBAAgB;MAAEC,UAAU;MAAEC;IAAiB,CAAC,GAAGN,UAAU;IACrE,MAAMO,cAAc,GAAGV,KAAK,CAACW,OAAO,EAAED,cAAc;IACpD,MAAME,eAAe,GAAGrB,kBAAkB,CAAC;MACvCU,QAAQ;MACRO,UAAU;MACVD;IACJ,CAAC,CAAC;IACF,MAAMM,cAAc,GAAGrB,0BAA0B,CAAC;MAC9CsB,gBAAgB,EAAErB,qCAAqC,CAAC;QACpDsB,gBAAgB,EAAEN;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMO,GAAG,SAASH,cAAc,CAAC;MAC7BI,MAAM,EAAE5B,uBAAuB,CAACmB,UAAU,CAAC;MAC3CU,cAAc,EAAE5B,qBAAqB,CAACJ,UAAU,CAACiC,aAAa;IAClE,CAAC,EAAE;MACCC,QAAQ,EAAEnB,QAAQ;MAClBoB,cAAc,EAAEX,cAAc;MAC9BY,QAAQ,EAAEf,gBAAgB;MAC1BK;IACJ,CAAC,CAAC;IACF,MAAMW,mBAAmB,GAAGP,GAAG,CAACQ,mBAAmB;IACnD,OAAO;MACHC,eAAe,EAAE,KAAK;MACtBC,QAAQ,EAAE;QACNC,iBAAiB,EAAE,kCAAkC;QACrDJ,mBAAmB,EAAE;UACjBK,cAAc,EAAEL,mBAAmB,EAAEM,cAAc;UACnDC,WAAW,EAAEP,mBAAmB,EAAEQ,WAAW;UAC7CC,aAAa,EAAET,mBAAmB,EAAEU;QACxC;MACJ;IACJ,CAAC;EACL,CAAC;EAAA,OAAArC,cAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}