{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport BigInteger from '../BigInteger/BigInteger.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nconst calculateA = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* ({\n    a,\n    g,\n    N\n  }) {\n    return new Promise((resolve, reject) => {\n      g.modPow(a, N, (err, A) => {\n        if (err) {\n          reject(err);\n          return;\n        }\n        if (A.mod(N).equals(BigInteger.ZERO)) {\n          reject(new Error('Illegal parameter. A mod N cannot be 0.'));\n          return;\n        }\n        resolve(A);\n      });\n    });\n  });\n  return function calculateA(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { calculateA };", "map": {"version": 3, "names": ["BigInteger", "calculateA", "_ref", "_asyncToGenerator", "a", "g", "N", "Promise", "resolve", "reject", "modPow", "err", "A", "mod", "equals", "ZERO", "Error", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/srp/calculate/calculateA.mjs"], "sourcesContent": ["import BigInteger from '../BigInteger/BigInteger.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nconst calculateA = async ({ a, g, N, }) => {\n    return new Promise((resolve, reject) => {\n        g.modPow(a, N, (err, A) => {\n            if (err) {\n                reject(err);\n                return;\n            }\n            if (A.mod(N).equals(BigInteger.ZERO)) {\n                reject(new Error('Illegal parameter. A mod N cannot be 0.'));\n                return;\n            }\n            resolve(A);\n        });\n    });\n};\n\nexport { calculateA };\n"], "mappings": ";AAAA,OAAOA,UAAU,MAAM,8BAA8B;;AAErD;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAO;IAAEC,CAAC;IAAEC,CAAC;IAAEC;EAAG,CAAC,EAAK;IACvC,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpCJ,CAAC,CAACK,MAAM,CAACN,CAAC,EAAEE,CAAC,EAAE,CAACK,GAAG,EAAEC,CAAC,KAAK;QACvB,IAAID,GAAG,EAAE;UACLF,MAAM,CAACE,GAAG,CAAC;UACX;QACJ;QACA,IAAIC,CAAC,CAACC,GAAG,CAACP,CAAC,CAAC,CAACQ,MAAM,CAACd,UAAU,CAACe,IAAI,CAAC,EAAE;UAClCN,MAAM,CAAC,IAAIO,KAAK,CAAC,yCAAyC,CAAC,CAAC;UAC5D;QACJ;QACAR,OAAO,CAACI,CAAC,CAAC;MACd,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EAAA,gBAdKX,UAAUA,CAAAgB,EAAA;IAAA,OAAAf,IAAA,CAAAgB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAcf;AAED,SAASlB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}