{"ast": null, "code": "const radio = {\n  alignItems: {\n    value: 'center'\n  },\n  justifyContent: {\n    value: 'flex-start'\n  },\n  gap: {\n    value: 'inherit'\n  },\n  _disabled: {\n    cursor: {\n      value: 'not-allowed'\n    }\n  },\n  button: {\n    alignItems: {\n      value: 'center'\n    },\n    justifyContent: {\n      value: 'center'\n    },\n    width: {\n      value: '{fontSizes.medium.value}'\n    },\n    height: {\n      value: '{fontSizes.medium.value}'\n    },\n    boxSizing: {\n      value: 'border-box'\n    },\n    borderWidth: {\n      value: '{borderWidths.medium.value}'\n    },\n    borderStyle: {\n      value: 'solid'\n    },\n    borderRadius: {\n      value: '50%'\n    },\n    borderColor: {\n      value: '{colors.border.primary.value}'\n    },\n    color: {\n      value: '{colors.background.primary.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.primary.value}'\n    },\n    transitionProperty: {\n      value: 'all'\n    },\n    transitionDuration: {\n      value: '{time.medium.value}'\n    },\n    outlineColor: {\n      value: '{colors.transparent.value}'\n    },\n    outlineStyle: {\n      value: 'solid'\n    },\n    outlineWidth: {\n      value: '{outlineWidths.medium.value}'\n    },\n    outlineOffset: {\n      value: '{outlineOffsets.medium.value}'\n    },\n    // We want the dot inside the border to be a border-width from the border\n    padding: {\n      value: '{borderWidths.medium.value}'\n    },\n    small: {\n      width: {\n        value: '{fontSizes.small.value}'\n      },\n      height: {\n        value: '{fontSizes.small.value}'\n      }\n    },\n    large: {\n      width: {\n        value: '{fontSizes.large.value}'\n      },\n      height: {\n        value: '{fontSizes.large.value}'\n      }\n    },\n    _checked: {\n      color: {\n        value: '{colors.primary.80.value}'\n      },\n      _disabled: {\n        color: {\n          value: '{colors.background.disabled.value}'\n        }\n      }\n    },\n    _focus: {\n      borderColor: {\n        value: '{colors.border.focus.value}'\n      },\n      boxShadow: {\n        value: '{components.fieldcontrol._focus.boxShadow.value}'\n      }\n    },\n    _error: {\n      borderColor: {\n        value: '{colors.border.error.value}'\n      },\n      _focus: {\n        boxShadow: {\n          value: '{components.fieldcontrol._error._focus.boxShadow.value}'\n        }\n      }\n    },\n    _disabled: {\n      borderColor: {\n        value: '{colors.border.disabled.value}'\n      },\n      backgroundColor: {\n        value: '{colors.background.primary.value}'\n      }\n    }\n  },\n  label: {\n    color: {\n      value: '{components.text.color.value}'\n    },\n    _disabled: {\n      color: {\n        value: '{colors.font.disabled.value}'\n      }\n    }\n  }\n};\nexport { radio };", "map": {"version": 3, "names": ["radio", "alignItems", "value", "justifyContent", "gap", "_disabled", "cursor", "button", "width", "height", "boxSizing", "borderWidth", "borderStyle", "borderRadius", "borderColor", "color", "backgroundColor", "transitionProperty", "transitionDuration", "outlineColor", "outlineStyle", "outlineWidth", "outlineOffset", "padding", "small", "large", "_checked", "_focus", "boxShadow", "_error", "label"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/radio.mjs"], "sourcesContent": ["const radio = {\n    alignItems: { value: 'center' },\n    justifyContent: { value: 'flex-start' },\n    gap: { value: 'inherit' },\n    _disabled: { cursor: { value: 'not-allowed' } },\n    button: {\n        alignItems: { value: 'center' },\n        justifyContent: { value: 'center' },\n        width: { value: '{fontSizes.medium.value}' },\n        height: { value: '{fontSizes.medium.value}' },\n        boxSizing: { value: 'border-box' },\n        borderWidth: { value: '{borderWidths.medium.value}' },\n        borderStyle: { value: 'solid' },\n        borderRadius: { value: '50%' },\n        borderColor: { value: '{colors.border.primary.value}' },\n        color: { value: '{colors.background.primary.value}' },\n        backgroundColor: { value: '{colors.background.primary.value}' },\n        transitionProperty: { value: 'all' },\n        transitionDuration: { value: '{time.medium.value}' },\n        outlineColor: { value: '{colors.transparent.value}' },\n        outlineStyle: { value: 'solid' },\n        outlineWidth: { value: '{outlineWidths.medium.value}' },\n        outlineOffset: { value: '{outlineOffsets.medium.value}' },\n        // We want the dot inside the border to be a border-width from the border\n        padding: { value: '{borderWidths.medium.value}' },\n        small: {\n            width: { value: '{fontSizes.small.value}' },\n            height: { value: '{fontSizes.small.value}' },\n        },\n        large: {\n            width: { value: '{fontSizes.large.value}' },\n            height: { value: '{fontSizes.large.value}' },\n        },\n        _checked: {\n            color: {\n                value: '{colors.primary.80.value}',\n            },\n            _disabled: { color: { value: '{colors.background.disabled.value}' } },\n        },\n        _focus: {\n            borderColor: { value: '{colors.border.focus.value}' },\n            boxShadow: { value: '{components.fieldcontrol._focus.boxShadow.value}' },\n        },\n        _error: {\n            borderColor: { value: '{colors.border.error.value}' },\n            _focus: {\n                boxShadow: {\n                    value: '{components.fieldcontrol._error._focus.boxShadow.value}',\n                },\n            },\n        },\n        _disabled: {\n            borderColor: { value: '{colors.border.disabled.value}' },\n            backgroundColor: { value: '{colors.background.primary.value}' },\n        },\n    },\n    label: {\n        color: { value: '{components.text.color.value}' },\n        _disabled: {\n            color: {\n                value: '{colors.font.disabled.value}',\n            },\n        },\n    },\n};\n\nexport { radio };\n"], "mappings": "AAAA,MAAMA,KAAK,GAAG;EACVC,UAAU,EAAE;IAAEC,KAAK,EAAE;EAAS,CAAC;EAC/BC,cAAc,EAAE;IAAED,KAAK,EAAE;EAAa,CAAC;EACvCE,GAAG,EAAE;IAAEF,KAAK,EAAE;EAAU,CAAC;EACzBG,SAAS,EAAE;IAAEC,MAAM,EAAE;MAAEJ,KAAK,EAAE;IAAc;EAAE,CAAC;EAC/CK,MAAM,EAAE;IACJN,UAAU,EAAE;MAAEC,KAAK,EAAE;IAAS,CAAC;IAC/BC,cAAc,EAAE;MAAED,KAAK,EAAE;IAAS,CAAC;IACnCM,KAAK,EAAE;MAAEN,KAAK,EAAE;IAA2B,CAAC;IAC5CO,MAAM,EAAE;MAAEP,KAAK,EAAE;IAA2B,CAAC;IAC7CQ,SAAS,EAAE;MAAER,KAAK,EAAE;IAAa,CAAC;IAClCS,WAAW,EAAE;MAAET,KAAK,EAAE;IAA8B,CAAC;IACrDU,WAAW,EAAE;MAAEV,KAAK,EAAE;IAAQ,CAAC;IAC/BW,YAAY,EAAE;MAAEX,KAAK,EAAE;IAAM,CAAC;IAC9BY,WAAW,EAAE;MAAEZ,KAAK,EAAE;IAAgC,CAAC;IACvDa,KAAK,EAAE;MAAEb,KAAK,EAAE;IAAoC,CAAC;IACrDc,eAAe,EAAE;MAAEd,KAAK,EAAE;IAAoC,CAAC;IAC/De,kBAAkB,EAAE;MAAEf,KAAK,EAAE;IAAM,CAAC;IACpCgB,kBAAkB,EAAE;MAAEhB,KAAK,EAAE;IAAsB,CAAC;IACpDiB,YAAY,EAAE;MAAEjB,KAAK,EAAE;IAA6B,CAAC;IACrDkB,YAAY,EAAE;MAAElB,KAAK,EAAE;IAAQ,CAAC;IAChCmB,YAAY,EAAE;MAAEnB,KAAK,EAAE;IAA+B,CAAC;IACvDoB,aAAa,EAAE;MAAEpB,KAAK,EAAE;IAAgC,CAAC;IACzD;IACAqB,OAAO,EAAE;MAAErB,KAAK,EAAE;IAA8B,CAAC;IACjDsB,KAAK,EAAE;MACHhB,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA0B,CAAC;MAC3CO,MAAM,EAAE;QAAEP,KAAK,EAAE;MAA0B;IAC/C,CAAC;IACDuB,KAAK,EAAE;MACHjB,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA0B,CAAC;MAC3CO,MAAM,EAAE;QAAEP,KAAK,EAAE;MAA0B;IAC/C,CAAC;IACDwB,QAAQ,EAAE;MACNX,KAAK,EAAE;QACHb,KAAK,EAAE;MACX,CAAC;MACDG,SAAS,EAAE;QAAEU,KAAK,EAAE;UAAEb,KAAK,EAAE;QAAqC;MAAE;IACxE,CAAC;IACDyB,MAAM,EAAE;MACJb,WAAW,EAAE;QAAEZ,KAAK,EAAE;MAA8B,CAAC;MACrD0B,SAAS,EAAE;QAAE1B,KAAK,EAAE;MAAmD;IAC3E,CAAC;IACD2B,MAAM,EAAE;MACJf,WAAW,EAAE;QAAEZ,KAAK,EAAE;MAA8B,CAAC;MACrDyB,MAAM,EAAE;QACJC,SAAS,EAAE;UACP1B,KAAK,EAAE;QACX;MACJ;IACJ,CAAC;IACDG,SAAS,EAAE;MACPS,WAAW,EAAE;QAAEZ,KAAK,EAAE;MAAiC,CAAC;MACxDc,eAAe,EAAE;QAAEd,KAAK,EAAE;MAAoC;IAClE;EACJ,CAAC;EACD4B,KAAK,EAAE;IACHf,KAAK,EAAE;MAAEb,KAAK,EAAE;IAAgC,CAAC;IACjDG,SAAS,EAAE;MACPU,KAAK,EAAE;QACHb,KAAK,EAAE;MACX;IACJ;EACJ;AACJ,CAAC;AAED,SAASF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}