{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar UpdateEndpointException;\n(function (UpdateEndpointException) {\n  UpdateEndpointException[\"BadRequestException\"] = \"BadRequestException\";\n  UpdateEndpointException[\"ForbiddenException\"] = \"ForbiddenException\";\n  UpdateEndpointException[\"InternalServerErrorException\"] = \"InternalServerErrorException\";\n  UpdateEndpointException[\"MethodNotAllowedException\"] = \"MethodNotAllowedException\";\n  UpdateEndpointException[\"NotFoundException\"] = \"NotFoundException\";\n  UpdateEndpointException[\"PayloadTooLargeException\"] = \"PayloadTooLargeException\";\n  UpdateEndpointException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n})(UpdateEndpointException || (UpdateEndpointException = {}));\nexport { UpdateEndpointException };", "map": {"version": 3, "names": ["UpdateEndpointException"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/providers/pinpoint/types/errors.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar UpdateEndpointException;\n(function (UpdateEndpointException) {\n    UpdateEndpointException[\"BadRequestException\"] = \"BadRequestException\";\n    UpdateEndpointException[\"ForbiddenException\"] = \"ForbiddenException\";\n    UpdateEndpointException[\"InternalServerErrorException\"] = \"InternalServerErrorException\";\n    UpdateEndpointException[\"MethodNotAllowedException\"] = \"MethodNotAllowedException\";\n    UpdateEndpointException[\"NotFoundException\"] = \"NotFoundException\";\n    UpdateEndpointException[\"PayloadTooLargeException\"] = \"PayloadTooLargeException\";\n    UpdateEndpointException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n})(UpdateEndpointException || (UpdateEndpointException = {}));\n\nexport { UpdateEndpointException };\n"], "mappings": "AAAA;AACA;AACA,IAAIA,uBAAuB;AAC3B,CAAC,UAAUA,uBAAuB,EAAE;EAChCA,uBAAuB,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EACtEA,uBAAuB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACpEA,uBAAuB,CAAC,8BAA8B,CAAC,GAAG,8BAA8B;EACxFA,uBAAuB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAClFA,uBAAuB,CAAC,mBAAmB,CAAC,GAAG,mBAAmB;EAClEA,uBAAuB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAChFA,uBAAuB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;AACpF,CAAC,EAAEA,uBAAuB,KAAKA,uBAAuB,GAAG,CAAC,CAAC,CAAC,CAAC;AAE7D,SAASA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}