{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar AuthErrorTypes;\n(function (AuthErrorTypes) {\n  AuthErrorTypes[\"NoConfig\"] = \"noConfig\";\n  AuthErrorTypes[\"MissingAuthConfig\"] = \"missingAuthConfig\";\n  AuthErrorTypes[\"EmptyUsername\"] = \"emptyUsername\";\n  AuthErrorTypes[\"InvalidUsername\"] = \"invalidUsername\";\n  AuthErrorTypes[\"EmptyPassword\"] = \"emptyPassword\";\n  AuthErrorTypes[\"EmptyCode\"] = \"emptyCode\";\n  AuthErrorTypes[\"SignUpError\"] = \"signUpError\";\n  AuthErrorTypes[\"NoMFA\"] = \"noMFA\";\n  AuthErrorTypes[\"InvalidMFA\"] = \"invalidMFA\";\n  AuthErrorTypes[\"EmptyChallengeResponse\"] = \"emptyChallengeResponse\";\n  AuthErrorTypes[\"NoUserSession\"] = \"noUserSession\";\n  AuthErrorTypes[\"Default\"] = \"default\";\n  AuthErrorTypes[\"DeviceConfig\"] = \"deviceConfig\";\n  AuthErrorTypes[\"NetworkError\"] = \"networkError\";\n  AuthErrorTypes[\"AutoSignInError\"] = \"autoSignInError\";\n  AuthErrorTypes[\"OAuthSignInError\"] = \"oauthSignInError\";\n})(AuthErrorTypes || (AuthErrorTypes = {}));\nexport { AuthErrorTypes };", "map": {"version": 3, "names": ["AuthErrorTypes"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/types/Auth.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar AuthErrorTypes;\n(function (AuthErrorTypes) {\n    AuthErrorTypes[\"NoConfig\"] = \"noConfig\";\n    AuthErrorTypes[\"MissingAuthConfig\"] = \"missingAuthConfig\";\n    AuthErrorTypes[\"EmptyUsername\"] = \"emptyUsername\";\n    AuthErrorTypes[\"InvalidUsername\"] = \"invalidUsername\";\n    AuthErrorTypes[\"EmptyPassword\"] = \"emptyPassword\";\n    AuthErrorTypes[\"EmptyCode\"] = \"emptyCode\";\n    AuthErrorTypes[\"SignUpError\"] = \"signUpError\";\n    AuthErrorTypes[\"NoMFA\"] = \"noMFA\";\n    AuthErrorTypes[\"InvalidMFA\"] = \"invalidMFA\";\n    AuthErrorTypes[\"EmptyChallengeResponse\"] = \"emptyChallengeResponse\";\n    AuthErrorTypes[\"NoUserSession\"] = \"noUserSession\";\n    AuthErrorTypes[\"Default\"] = \"default\";\n    AuthErrorTypes[\"DeviceConfig\"] = \"deviceConfig\";\n    AuthErrorTypes[\"NetworkError\"] = \"networkError\";\n    AuthErrorTypes[\"AutoSignInError\"] = \"autoSignInError\";\n    AuthErrorTypes[\"OAuthSignInError\"] = \"oauthSignInError\";\n})(AuthErrorTypes || (AuthErrorTypes = {}));\n\nexport { AuthErrorTypes };\n"], "mappings": "AAAA;AACA;AACA,IAAIA,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAAC,UAAU,CAAC,GAAG,UAAU;EACvCA,cAAc,CAAC,mBAAmB,CAAC,GAAG,mBAAmB;EACzDA,cAAc,CAAC,eAAe,CAAC,GAAG,eAAe;EACjDA,cAAc,CAAC,iBAAiB,CAAC,GAAG,iBAAiB;EACrDA,cAAc,CAAC,eAAe,CAAC,GAAG,eAAe;EACjDA,cAAc,CAAC,WAAW,CAAC,GAAG,WAAW;EACzCA,cAAc,CAAC,aAAa,CAAC,GAAG,aAAa;EAC7CA,cAAc,CAAC,OAAO,CAAC,GAAG,OAAO;EACjCA,cAAc,CAAC,YAAY,CAAC,GAAG,YAAY;EAC3CA,cAAc,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACnEA,cAAc,CAAC,eAAe,CAAC,GAAG,eAAe;EACjDA,cAAc,CAAC,SAAS,CAAC,GAAG,SAAS;EACrCA,cAAc,CAAC,cAAc,CAAC,GAAG,cAAc;EAC/CA,cAAc,CAAC,cAAc,CAAC,GAAG,cAAc;EAC/CA,cAAc,CAAC,iBAAiB,CAAC,GAAG,iBAAiB;EACrDA,cAAc,CAAC,kBAAkB,CAAC,GAAG,kBAAkB;AAC3D,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAE3C,SAASA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}