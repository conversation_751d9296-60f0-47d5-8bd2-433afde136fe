{"ast": null, "code": "const fontSizes = {\n  xxxs: {\n    value: '0.375rem'\n  },\n  xxs: {\n    value: '0.5rem'\n  },\n  xs: {\n    value: '0.75rem'\n  },\n  small: {\n    value: '0.875rem'\n  },\n  medium: {\n    value: '1rem'\n  },\n  large: {\n    value: '1.25rem'\n  },\n  xl: {\n    value: '1.5rem'\n  },\n  xxl: {\n    value: '2rem'\n  },\n  xxxl: {\n    value: '2.5rem'\n  },\n  xxxxl: {\n    value: '3rem'\n  }\n};\nexport { fontSizes };", "map": {"version": 3, "names": ["fontSizes", "xxxs", "value", "xxs", "xs", "small", "medium", "large", "xl", "xxl", "xxxl", "xxxxl"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/fontSizes.mjs"], "sourcesContent": ["const fontSizes = {\n    xxxs: { value: '0.375rem' },\n    xxs: { value: '0.5rem' },\n    xs: { value: '0.75rem' },\n    small: { value: '0.875rem' },\n    medium: { value: '1rem' },\n    large: { value: '1.25rem' },\n    xl: { value: '1.5rem' },\n    xxl: { value: '2rem' },\n    xxxl: { value: '2.5rem' },\n    xxxxl: { value: '3rem' },\n};\n\nexport { fontSizes };\n"], "mappings": "AAAA,MAAMA,SAAS,GAAG;EACdC,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAW,CAAC;EAC3BC,GAAG,EAAE;IAAED,KAAK,EAAE;EAAS,CAAC;EACxBE,EAAE,EAAE;IAAEF,KAAK,EAAE;EAAU,CAAC;EACxBG,KAAK,EAAE;IAAEH,KAAK,EAAE;EAAW,CAAC;EAC5BI,MAAM,EAAE;IAAEJ,KAAK,EAAE;EAAO,CAAC;EACzBK,KAAK,EAAE;IAAEL,KAAK,EAAE;EAAU,CAAC;EAC3BM,EAAE,EAAE;IAAEN,KAAK,EAAE;EAAS,CAAC;EACvBO,GAAG,EAAE;IAAEP,KAAK,EAAE;EAAO,CAAC;EACtBQ,IAAI,EAAE;IAAER,KAAK,EAAE;EAAS,CAAC;EACzBS,KAAK,EAAE;IAAET,KAAK,EAAE;EAAO;AAC3B,CAAC;AAED,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}