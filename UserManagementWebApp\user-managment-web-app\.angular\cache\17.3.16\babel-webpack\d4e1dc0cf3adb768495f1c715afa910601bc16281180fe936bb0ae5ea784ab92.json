{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../types/errors.mjs';\nimport { createChangePasswordClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createChangePasswordClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Updates user's password while authenticated.\n *\n * @param input - The UpdatePasswordInput object.\n * @throws - {@link ChangePasswordException} - Cognito service errors thrown when updating a password.\n * @throws - {@link AuthValidationErrorCode} - Validation errors thrown when oldPassword or newPassword are empty.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nfunction updatePassword(_x) {\n  return _updatePassword.apply(this, arguments);\n}\nfunction _updatePassword() {\n  _updatePassword = _asyncToGenerator(function* (input) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolEndpoint,\n      userPoolId\n    } = authConfig;\n    const {\n      oldPassword,\n      newPassword\n    } = input;\n    assertValidationError(!!oldPassword, AuthValidationErrorCode.EmptyUpdatePassword);\n    assertValidationError(!!newPassword, AuthValidationErrorCode.EmptyUpdatePassword);\n    const {\n      tokens\n    } = yield fetchAuthSession({\n      forceRefresh: false\n    });\n    assertAuthTokens(tokens);\n    const changePassword = createChangePasswordClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    yield changePassword({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.UpdatePassword)\n    }, {\n      AccessToken: tokens.accessToken.toString(),\n      PreviousPassword: oldPassword,\n      ProposedPassword: newPassword\n    });\n  });\n  return _updatePassword.apply(this, arguments);\n}\nexport { updatePassword };", "map": {"version": 3, "names": ["Amplify", "fetchAuthSession", "assertTokenProviderConfig", "AuthAction", "AuthValidationErrorCode", "assertValidationError", "getRegionFromUserPoolId", "assertAuthTokens", "getAuthUserAgentValue", "createChangePasswordClient", "createCognitoUserPoolEndpointResolver", "updatePassword", "_x", "_updatePassword", "apply", "arguments", "_asyncToGenerator", "input", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userPoolEndpoint", "userPoolId", "oldPassword", "newPassword", "EmptyUpdatePassword", "tokens", "forceRefresh", "changePassword", "endpointResolver", "endpointOverride", "region", "userAgentValue", "UpdatePassword", "AccessToken", "accessToken", "toString", "PreviousPassword", "ProposedPassword"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/updatePassword.mjs"], "sourcesContent": ["import { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../types/errors.mjs';\nimport { createChangePasswordClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createChangePasswordClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Updates user's password while authenticated.\n *\n * @param input - The UpdatePasswordInput object.\n * @throws - {@link ChangePasswordException} - Cognito service errors thrown when updating a password.\n * @throws - {@link AuthValidationErrorCode} - Validation errors thrown when oldPassword or newPassword are empty.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nasync function updatePassword(input) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolEndpoint, userPoolId } = authConfig;\n    const { oldPassword, newPassword } = input;\n    assertValidationError(!!oldPassword, AuthValidationErrorCode.EmptyUpdatePassword);\n    assertValidationError(!!newPassword, AuthValidationErrorCode.EmptyUpdatePassword);\n    const { tokens } = await fetchAuthSession({ forceRefresh: false });\n    assertAuthTokens(tokens);\n    const changePassword = createChangePasswordClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    await changePassword({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.UpdatePassword),\n    }, {\n        AccessToken: tokens.accessToken.toString(),\n        PreviousPassword: oldPassword,\n        ProposedPassword: newPassword,\n    });\n}\n\nexport { updatePassword };\n"], "mappings": ";AAAA,SAASA,OAAO,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC7D,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,qBAAqB;AAC5B,SAASC,0BAA0B,QAAQ,qGAAqG;AAChJ,SAASC,qCAAqC,QAAQ,wDAAwD;;AAE9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAQeC,cAAcA,CAAAC,EAAA;EAAA,OAAAC,eAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,gBAAA;EAAAA,eAAA,GAAAG,iBAAA,CAA7B,WAA8BC,KAAK,EAAE;IACjC,MAAMC,UAAU,GAAGlB,OAAO,CAACmB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDnB,yBAAyB,CAACgB,UAAU,CAAC;IACrC,MAAM;MAAEI,gBAAgB;MAAEC;IAAW,CAAC,GAAGL,UAAU;IACnD,MAAM;MAAEM,WAAW;MAAEC;IAAY,CAAC,GAAGR,KAAK;IAC1CZ,qBAAqB,CAAC,CAAC,CAACmB,WAAW,EAAEpB,uBAAuB,CAACsB,mBAAmB,CAAC;IACjFrB,qBAAqB,CAAC,CAAC,CAACoB,WAAW,EAAErB,uBAAuB,CAACsB,mBAAmB,CAAC;IACjF,MAAM;MAAEC;IAAO,CAAC,SAAS1B,gBAAgB,CAAC;MAAE2B,YAAY,EAAE;IAAM,CAAC,CAAC;IAClErB,gBAAgB,CAACoB,MAAM,CAAC;IACxB,MAAME,cAAc,GAAGpB,0BAA0B,CAAC;MAC9CqB,gBAAgB,EAAEpB,qCAAqC,CAAC;QACpDqB,gBAAgB,EAAET;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMO,cAAc,CAAC;MACjBG,MAAM,EAAE1B,uBAAuB,CAACiB,UAAU,CAAC;MAC3CU,cAAc,EAAEzB,qBAAqB,CAACL,UAAU,CAAC+B,cAAc;IACnE,CAAC,EAAE;MACCC,WAAW,EAAER,MAAM,CAACS,WAAW,CAACC,QAAQ,CAAC,CAAC;MAC1CC,gBAAgB,EAAEd,WAAW;MAC7Be,gBAAgB,EAAEd;IACtB,CAAC,CAAC;EACN,CAAC;EAAA,OAAAZ,eAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}