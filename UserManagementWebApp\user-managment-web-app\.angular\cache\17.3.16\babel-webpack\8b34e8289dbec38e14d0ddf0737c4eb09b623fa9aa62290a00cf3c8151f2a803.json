{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { assertCredentialIsPkcWithAuthenticatorAttestationResponse } from './types/index.mjs';\nimport { deserializeJsonToPkcCreationOptions, serializePkcWithAttestationToJson } from './serde.mjs';\nimport { assertPasskeyError, PasskeyErrorCode, handlePasskeyRegistrationError } from './errors.mjs';\nimport { getIsPasskeySupported } from './getIsPasskeySupported.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Registers a new passkey for user\n * @param input - PasskeyCreateOptionsJson\n * @returns serialized PasskeyCreateResult\n */\nconst registerPasskey = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (input) {\n    try {\n      const isPasskeySupported = getIsPasskeySupported();\n      assertPasskeyError(isPasskeySupported, PasskeyErrorCode.PasskeyNotSupported);\n      const passkeyCreationOptions = deserializeJsonToPkcCreationOptions(input);\n      const credential = yield navigator.credentials.create({\n        publicKey: passkeyCreationOptions\n      });\n      assertCredentialIsPkcWithAuthenticatorAttestationResponse(credential);\n      return serializePkcWithAttestationToJson(credential);\n    } catch (err) {\n      throw handlePasskeyRegistrationError(err);\n    }\n  });\n  return function registerPasskey(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { registerPasskey };", "map": {"version": 3, "names": ["assertCredentialIsPkcWithAuthenticatorAttestationResponse", "deserializeJsonToPkcCreationOptions", "serializePkcWithAttestationToJson", "assertPasskeyError", "PasskeyErrorCode", "handlePasskeyRegistrationError", "getIsPasskeySupported", "registerPasskey", "_ref", "_asyncToGenerator", "input", "isPasskeySupported", "PasskeyNotSupported", "passkeyCreationOptions", "credential", "navigator", "credentials", "create", "public<PERSON>ey", "err", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/utils/passkey/registerPasskey.mjs"], "sourcesContent": ["import { assertCredentialIsPkcWithAuthenticatorAttestationResponse } from './types/index.mjs';\nimport { deserializeJsonToPkcCreationOptions, serializePkcWithAttestationToJson } from './serde.mjs';\nimport { assertPasskeyError, PasskeyErrorCode, handlePasskeyRegistrationError } from './errors.mjs';\nimport { getIsPasskeySupported } from './getIsPasskeySupported.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Registers a new passkey for user\n * @param input - PasskeyCreateOptionsJson\n * @returns serialized PasskeyCreateResult\n */\nconst registerPasskey = async (input) => {\n    try {\n        const isPasskeySupported = getIsPasskeySupported();\n        assertPasskeyError(isPasskeySupported, PasskeyErrorCode.PasskeyNotSupported);\n        const passkeyCreationOptions = deserializeJsonToPkcCreationOptions(input);\n        const credential = await navigator.credentials.create({\n            publicKey: passkeyCreationOptions,\n        });\n        assertCredentialIsPkcWithAuthenticatorAttestationResponse(credential);\n        return serializePkcWithAttestationToJson(credential);\n    }\n    catch (err) {\n        throw handlePasskeyRegistrationError(err);\n    }\n};\n\nexport { registerPasskey };\n"], "mappings": ";AAAA,SAASA,yDAAyD,QAAQ,mBAAmB;AAC7F,SAASC,mCAAmC,EAAEC,iCAAiC,QAAQ,aAAa;AACpG,SAASC,kBAAkB,EAAEC,gBAAgB,EAAEC,8BAA8B,QAAQ,cAAc;AACnG,SAASC,qBAAqB,QAAQ,6BAA6B;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,KAAK,EAAK;IACrC,IAAI;MACA,MAAMC,kBAAkB,GAAGL,qBAAqB,CAAC,CAAC;MAClDH,kBAAkB,CAACQ,kBAAkB,EAAEP,gBAAgB,CAACQ,mBAAmB,CAAC;MAC5E,MAAMC,sBAAsB,GAAGZ,mCAAmC,CAACS,KAAK,CAAC;MACzE,MAAMI,UAAU,SAASC,SAAS,CAACC,WAAW,CAACC,MAAM,CAAC;QAClDC,SAAS,EAAEL;MACf,CAAC,CAAC;MACFb,yDAAyD,CAACc,UAAU,CAAC;MACrE,OAAOZ,iCAAiC,CAACY,UAAU,CAAC;IACxD,CAAC,CACD,OAAOK,GAAG,EAAE;MACR,MAAMd,8BAA8B,CAACc,GAAG,CAAC;IAC7C;EACJ,CAAC;EAAA,gBAdKZ,eAAeA,CAAAa,EAAA;IAAA,OAAAZ,IAAA,CAAAa,KAAA,OAAAC,SAAA;EAAA;AAAA,GAcpB;AAED,SAASf,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}