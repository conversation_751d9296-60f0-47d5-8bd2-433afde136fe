{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AuthAction } from '@aws-amplify/core/internals/utils';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../../../providers/cognito/types/errors.mjs';\nimport { createRespondToAuthChallengeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createRespondToAuthChallengeClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Handles the SELECT_CHALLENGE response for authentication.\n * Initiates the selected authentication challenge based on user choice.\n *\n * @param {Object} params - The parameters for handling the selected challenge\n * @param {string} params.username - The username for authentication\n * @param {string} params.session - The current authentication session token\n * @param {string} params.selectedChallenge - The challenge type selected by the user\n * @param {CognitoUserPoolConfig} params.config - Cognito User Pool configuration\n * @param {ClientMetadata} [params.clientMetadata] - Optional metadata to be sent with auth requests\n *\n * @returns {Promise<RespondToAuthChallengeCommandOutput>} The challenge response\n */\nfunction initiateSelectedChallenge(_x) {\n  return _initiateSelectedChallenge.apply(this, arguments);\n}\nfunction _initiateSelectedChallenge() {\n  _initiateSelectedChallenge = _asyncToGenerator(function* ({\n    username,\n    session,\n    selectedChallenge,\n    config,\n    clientMetadata\n  }) {\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: config.userPoolEndpoint\n      })\n    });\n    return respondToAuthChallenge({\n      region: getRegionFromUserPoolId(config.userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn)\n    }, {\n      ChallengeName: 'SELECT_CHALLENGE',\n      ChallengeResponses: {\n        USERNAME: username,\n        ANSWER: selectedChallenge\n      },\n      ClientId: config.userPoolClientId,\n      Session: session,\n      ClientMetadata: clientMetadata\n    });\n  });\n  return _initiateSelectedChallenge.apply(this, arguments);\n}\nexport { initiateSelectedChallenge };", "map": {"version": 3, "names": ["AuthAction", "createRespondToAuthChallengeClient", "createCognitoUserPoolEndpointResolver", "getRegionFromUserPoolId", "getAuthUserAgentValue", "initiateSelectedChallenge", "_x", "_initiateSelectedChallenge", "apply", "arguments", "_asyncToGenerator", "username", "session", "selected<PERSON>hall<PERSON><PERSON>", "config", "clientMetadata", "respondToAuthChallenge", "endpointResolver", "endpointOverride", "userPoolEndpoint", "region", "userPoolId", "userAgentValue", "ConfirmSignIn", "ChallengeName", "ChallengeResponses", "USERNAME", "ANSWER", "ClientId", "userPoolClientId", "Session", "ClientMetadata"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/flows/userAuth/handleSelectChallenge.mjs"], "sourcesContent": ["import { AuthAction } from '@aws-amplify/core/internals/utils';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../../../providers/cognito/types/errors.mjs';\nimport { createRespondToAuthChallengeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createRespondToAuthChallengeClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Handles the SELECT_CHALLENGE response for authentication.\n * Initiates the selected authentication challenge based on user choice.\n *\n * @param {Object} params - The parameters for handling the selected challenge\n * @param {string} params.username - The username for authentication\n * @param {string} params.session - The current authentication session token\n * @param {string} params.selectedChallenge - The challenge type selected by the user\n * @param {CognitoUserPoolConfig} params.config - Cognito User Pool configuration\n * @param {ClientMetadata} [params.clientMetadata] - Optional metadata to be sent with auth requests\n *\n * @returns {Promise<RespondToAuthChallengeCommandOutput>} The challenge response\n */\nasync function initiateSelectedChallenge({ username, session, selectedChallenge, config, clientMetadata, }) {\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: config.userPoolEndpoint,\n        }),\n    });\n    return respondToAuthChallenge({\n        region: getRegionFromUserPoolId(config.userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn),\n    }, {\n        ChallengeName: 'SELECT_CHALLENGE',\n        ChallengeResponses: {\n            USERNAME: username,\n            ANSWER: selectedChallenge,\n        },\n        ClientId: config.userPoolClientId,\n        Session: session,\n        ClientMetadata: clientMetadata,\n    });\n}\n\nexport { initiateSelectedChallenge };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,mCAAmC;AAC9D,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,sCAAsC;AAC7C,OAAO,6CAA6C;AACpD,SAASC,kCAAkC,QAAQ,6GAA6G;AAChK,SAASC,qCAAqC,QAAQ,gFAAgF;AACtI,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,qBAAqB,QAAQ,0CAA0C;;AAEhF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,SAaeC,yBAAyBA,CAAAC,EAAA;EAAA,OAAAC,0BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,2BAAA;EAAAA,0BAAA,GAAAG,iBAAA,CAAxC,WAAyC;IAAEC,QAAQ;IAAEC,OAAO;IAAEC,iBAAiB;IAAEC,MAAM;IAAEC;EAAgB,CAAC,EAAE;IACxG,MAAMC,sBAAsB,GAAGf,kCAAkC,CAAC;MAC9DgB,gBAAgB,EAAEf,qCAAqC,CAAC;QACpDgB,gBAAgB,EAAEJ,MAAM,CAACK;MAC7B,CAAC;IACL,CAAC,CAAC;IACF,OAAOH,sBAAsB,CAAC;MAC1BI,MAAM,EAAEjB,uBAAuB,CAACW,MAAM,CAACO,UAAU,CAAC;MAClDC,cAAc,EAAElB,qBAAqB,CAACJ,UAAU,CAACuB,aAAa;IAClE,CAAC,EAAE;MACCC,aAAa,EAAE,kBAAkB;MACjCC,kBAAkB,EAAE;QAChBC,QAAQ,EAAEf,QAAQ;QAClBgB,MAAM,EAAEd;MACZ,CAAC;MACDe,QAAQ,EAAEd,MAAM,CAACe,gBAAgB;MACjCC,OAAO,EAAElB,OAAO;MAChBmB,cAAc,EAAEhB;IACpB,CAAC,CAAC;EACN,CAAC;EAAA,OAAAR,0BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}