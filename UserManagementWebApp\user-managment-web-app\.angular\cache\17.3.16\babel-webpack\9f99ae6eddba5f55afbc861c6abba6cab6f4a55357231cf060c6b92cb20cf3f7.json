{"ast": null, "code": "export { getDnsSuffix } from './endpoints/getDnsSuffix.mjs';\nexport { fetchTransferHandler } from './handlers/fetch.mjs';\nexport { unauthenticatedHandler } from './handlers/aws/unauthenticated.mjs';\nexport { authenticatedHandler } from './handlers/aws/authenticated.mjs';\nexport { signRequest } from './middleware/signing/signer/signatureV4/signRequest.mjs';\nexport { presignUrl } from './middleware/signing/signer/signatureV4/presignUrl.mjs';\nexport { EMPTY_HASH as EMPTY_SHA256_HASH } from './middleware/signing/signer/signatureV4/constants.mjs';\nexport { getHashedPayload } from './middleware/signing/signer/signatureV4/utils/getHashedPayload.mjs';\nexport { extendedEncodeURIComponent } from './middleware/signing/utils/extendedEncodeURIComponent.mjs';\nexport { signingMiddlewareFactory } from './middleware/signing/middleware.mjs';\nexport { retryMiddlewareFactory } from './middleware/retry/retryMiddleware.mjs';\nexport { jitteredBackoff } from './middleware/retry/jitteredBackoff.mjs';\nexport { getRetryDecider } from './middleware/retry/defaultRetryDecider.mjs';\nexport { amzSdkInvocationIdHeaderMiddlewareFactory } from './middleware/retry/amzSdkInvocationIdHeaderMiddleware.mjs';\nexport { amzSdkRequestHeaderMiddlewareFactory } from './middleware/retry/amzSdkRequestHeaderMiddleware.mjs';\nexport { userAgentMiddlewareFactory } from './middleware/userAgent/middleware.mjs';\nexport { parseMetadata } from './serde/responseInfo.mjs';\nexport { parseJsonBody, parseJsonError } from './serde/json.mjs';\nexport { withMemoization } from './utils/memoization.mjs';", "map": {"version": 3, "names": ["getDnsSuffix", "fetchTransferHandler", "unauthentica<PERSON><PERSON><PERSON><PERSON>", "authenticated<PERSON><PERSON><PERSON>", "signRequest", "presignUrl", "EMPTY_HASH", "EMPTY_SHA256_HASH", "getHashedPayload", "extendedEncodeURIComponent", "signingMiddlewareFactory", "retryMiddlewareFactory", "jittered<PERSON><PERSON>off", "getRetryDecider", "amzSdkInvocationIdHeaderMiddlewareFactory", "amzSdkRequestHeaderMiddlewareFactory", "userAgentMiddlewareFactory", "parseMetadata", "parseJsonBody", "parseJsonError", "withMemoization"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/index.mjs"], "sourcesContent": ["export { getDnsSuffix } from './endpoints/getDnsSuffix.mjs';\nexport { fetchTransferHandler } from './handlers/fetch.mjs';\nexport { unauthenticatedHandler } from './handlers/aws/unauthenticated.mjs';\nexport { authenticatedHandler } from './handlers/aws/authenticated.mjs';\nexport { signRequest } from './middleware/signing/signer/signatureV4/signRequest.mjs';\nexport { presignUrl } from './middleware/signing/signer/signatureV4/presignUrl.mjs';\nexport { EMPTY_HASH as EMPTY_SHA256_HASH } from './middleware/signing/signer/signatureV4/constants.mjs';\nexport { getHashedPayload } from './middleware/signing/signer/signatureV4/utils/getHashedPayload.mjs';\nexport { extendedEncodeURIComponent } from './middleware/signing/utils/extendedEncodeURIComponent.mjs';\nexport { signingMiddlewareFactory } from './middleware/signing/middleware.mjs';\nexport { retryMiddlewareFactory } from './middleware/retry/retryMiddleware.mjs';\nexport { jitteredBackoff } from './middleware/retry/jitteredBackoff.mjs';\nexport { getRetryDecider } from './middleware/retry/defaultRetryDecider.mjs';\nexport { amzSdkInvocationIdHeaderMiddlewareFactory } from './middleware/retry/amzSdkInvocationIdHeaderMiddleware.mjs';\nexport { amzSdkRequestHeaderMiddlewareFactory } from './middleware/retry/amzSdkRequestHeaderMiddleware.mjs';\nexport { userAgentMiddlewareFactory } from './middleware/userAgent/middleware.mjs';\nexport { parseMetadata } from './serde/responseInfo.mjs';\nexport { parseJsonBody, parseJsonError } from './serde/json.mjs';\nexport { withMemoization } from './utils/memoization.mjs';\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,WAAW,QAAQ,yDAAyD;AACrF,SAASC,UAAU,QAAQ,wDAAwD;AACnF,SAASC,UAAU,IAAIC,iBAAiB,QAAQ,uDAAuD;AACvG,SAASC,gBAAgB,QAAQ,oEAAoE;AACrG,SAASC,0BAA0B,QAAQ,2DAA2D;AACtG,SAASC,wBAAwB,QAAQ,qCAAqC;AAC9E,SAASC,sBAAsB,QAAQ,wCAAwC;AAC/E,SAASC,eAAe,QAAQ,wCAAwC;AACxE,SAASC,eAAe,QAAQ,4CAA4C;AAC5E,SAASC,yCAAyC,QAAQ,2DAA2D;AACrH,SAASC,oCAAoC,QAAQ,sDAAsD;AAC3G,SAASC,0BAA0B,QAAQ,uCAAuC;AAClF,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,aAAa,EAAEC,cAAc,QAAQ,kBAAkB;AAChE,SAASC,eAAe,QAAQ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}