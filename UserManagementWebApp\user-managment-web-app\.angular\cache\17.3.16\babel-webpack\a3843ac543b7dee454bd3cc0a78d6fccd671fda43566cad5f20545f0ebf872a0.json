{"ast": null, "code": "class AmplifyError extends Error {\n  /**\n   *  Constructs an AmplifyError.\n   *\n   * @param message text that describes the main problem.\n   * @param underlyingError the underlying cause of the error.\n   * @param recoverySuggestion suggestion to recover from the error.\n   *\n   */\n  constructor({\n    message,\n    name,\n    recoverySuggestion,\n    underlyingError,\n    metadata\n  }) {\n    super(message);\n    this.name = name;\n    this.underlyingError = underlyingError;\n    this.recoverySuggestion = recoverySuggestion;\n    if (metadata) {\n      // If metadata exists, explicitly only record the following properties.\n      const {\n        extendedRequestId,\n        httpStatusCode,\n        requestId\n      } = metadata;\n      this.metadata = {\n        extendedRequestId,\n        httpStatusCode,\n        requestId\n      };\n    }\n    // Hack for making the custom error class work when transpiled to es5\n    // TODO: Delete the following 2 lines after we change the build target to >= es2015\n    this.constructor = AmplifyError;\n    Object.setPrototypeOf(this, AmplifyError.prototype);\n  }\n}\nexport { AmplifyError };", "map": {"version": 3, "names": ["AmplifyError", "Error", "constructor", "message", "name", "recoverySuggestion", "underlyingError", "metadata", "extendedRequestId", "httpStatusCode", "requestId", "Object", "setPrototypeOf", "prototype"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/errors/AmplifyError.mjs"], "sourcesContent": ["class AmplifyError extends Error {\n    /**\n     *  Constructs an AmplifyError.\n     *\n     * @param message text that describes the main problem.\n     * @param underlyingError the underlying cause of the error.\n     * @param recoverySuggestion suggestion to recover from the error.\n     *\n     */\n    constructor({ message, name, recoverySuggestion, underlyingError, metadata, }) {\n        super(message);\n        this.name = name;\n        this.underlyingError = underlyingError;\n        this.recoverySuggestion = recoverySuggestion;\n        if (metadata) {\n            // If metadata exists, explicitly only record the following properties.\n            const { extendedRequestId, httpStatusCode, requestId } = metadata;\n            this.metadata = { extendedRequestId, httpStatusCode, requestId };\n        }\n        // Hack for making the custom error class work when transpiled to es5\n        // TODO: Delete the following 2 lines after we change the build target to >= es2015\n        this.constructor = AmplifyError;\n        Object.setPrototypeOf(this, AmplifyError.prototype);\n    }\n}\n\nexport { AmplifyError };\n"], "mappings": "AAAA,MAAMA,YAAY,SAASC,KAAK,CAAC;EAC7B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAAC;IAAEC,OAAO;IAAEC,IAAI;IAAEC,kBAAkB;IAAEC,eAAe;IAAEC;EAAU,CAAC,EAAE;IAC3E,KAAK,CAACJ,OAAO,CAAC;IACd,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACD,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAIE,QAAQ,EAAE;MACV;MACA,MAAM;QAAEC,iBAAiB;QAAEC,cAAc;QAAEC;MAAU,CAAC,GAAGH,QAAQ;MACjE,IAAI,CAACA,QAAQ,GAAG;QAAEC,iBAAiB;QAAEC,cAAc;QAAEC;MAAU,CAAC;IACpE;IACA;IACA;IACA,IAAI,CAACR,WAAW,GAAGF,YAAY;IAC/BW,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEZ,YAAY,CAACa,SAAS,CAAC;EACvD;AACJ;AAEA,SAASb,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}