{"ast": null, "code": "/* eslint-disable no-prototype-builtins */\nvar g = typeof globalThis !== 'undefined' && globalThis || typeof self !== 'undefined' && self ||\n// eslint-disable-next-line no-undef\ntypeof global !== 'undefined' && global || {};\nvar support = {\n  searchParams: 'URLSearchParams' in g,\n  iterable: 'Symbol' in g && 'iterator' in Symbol,\n  blob: 'FileReader' in g && 'Blob' in g && function () {\n    try {\n      new Blob();\n      return true;\n    } catch (e) {\n      return false;\n    }\n  }(),\n  formData: 'FormData' in g,\n  arrayBuffer: 'ArrayBuffer' in g\n};\nfunction isDataView(obj) {\n  return obj && DataView.prototype.isPrototypeOf(obj);\n}\nif (support.arrayBuffer) {\n  var viewClasses = ['[object Int8Array]', '[object Uint8Array]', '[object Uint8ClampedArray]', '[object Int16Array]', '[object Uint16Array]', '[object Int32Array]', '[object Uint32Array]', '[object Float32Array]', '[object Float64Array]'];\n  var isArrayBufferView = ArrayBuffer.isView || function (obj) {\n    return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1;\n  };\n}\nfunction normalizeName(name) {\n  if (typeof name !== 'string') {\n    name = String(name);\n  }\n  if (/[^a-z0-9\\-#$%&'*+.^_`|~!]/i.test(name) || name === '') {\n    throw new TypeError('Invalid character in header field name: \"' + name + '\"');\n  }\n  return name.toLowerCase();\n}\nfunction normalizeValue(value) {\n  if (typeof value !== 'string') {\n    value = String(value);\n  }\n  return value;\n}\n\n// Build a destructive iterator for the value list\nfunction iteratorFor(items) {\n  var iterator = {\n    next: function () {\n      var value = items.shift();\n      return {\n        done: value === undefined,\n        value: value\n      };\n    }\n  };\n  if (support.iterable) {\n    iterator[Symbol.iterator] = function () {\n      return iterator;\n    };\n  }\n  return iterator;\n}\nexport function Headers(headers) {\n  this.map = {};\n  if (headers instanceof Headers) {\n    headers.forEach(function (value, name) {\n      this.append(name, value);\n    }, this);\n  } else if (Array.isArray(headers)) {\n    headers.forEach(function (header) {\n      if (header.length != 2) {\n        throw new TypeError('Headers constructor: expected name/value pair to be length 2, found' + header.length);\n      }\n      this.append(header[0], header[1]);\n    }, this);\n  } else if (headers) {\n    Object.getOwnPropertyNames(headers).forEach(function (name) {\n      this.append(name, headers[name]);\n    }, this);\n  }\n}\nHeaders.prototype.append = function (name, value) {\n  name = normalizeName(name);\n  value = normalizeValue(value);\n  var oldValue = this.map[name];\n  this.map[name] = oldValue ? oldValue + ', ' + value : value;\n};\nHeaders.prototype['delete'] = function (name) {\n  delete this.map[normalizeName(name)];\n};\nHeaders.prototype.get = function (name) {\n  name = normalizeName(name);\n  return this.has(name) ? this.map[name] : null;\n};\nHeaders.prototype.has = function (name) {\n  return this.map.hasOwnProperty(normalizeName(name));\n};\nHeaders.prototype.set = function (name, value) {\n  this.map[normalizeName(name)] = normalizeValue(value);\n};\nHeaders.prototype.forEach = function (callback, thisArg) {\n  for (var name in this.map) {\n    if (this.map.hasOwnProperty(name)) {\n      callback.call(thisArg, this.map[name], name, this);\n    }\n  }\n};\nHeaders.prototype.keys = function () {\n  var items = [];\n  this.forEach(function (value, name) {\n    items.push(name);\n  });\n  return iteratorFor(items);\n};\nHeaders.prototype.values = function () {\n  var items = [];\n  this.forEach(function (value) {\n    items.push(value);\n  });\n  return iteratorFor(items);\n};\nHeaders.prototype.entries = function () {\n  var items = [];\n  this.forEach(function (value, name) {\n    items.push([name, value]);\n  });\n  return iteratorFor(items);\n};\nif (support.iterable) {\n  Headers.prototype[Symbol.iterator] = Headers.prototype.entries;\n}\nfunction consumed(body) {\n  if (body._noBody) return;\n  if (body.bodyUsed) {\n    return Promise.reject(new TypeError('Already read'));\n  }\n  body.bodyUsed = true;\n}\nfunction fileReaderReady(reader) {\n  return new Promise(function (resolve, reject) {\n    reader.onload = function () {\n      resolve(reader.result);\n    };\n    reader.onerror = function () {\n      reject(reader.error);\n    };\n  });\n}\nfunction readBlobAsArrayBuffer(blob) {\n  var reader = new FileReader();\n  var promise = fileReaderReady(reader);\n  reader.readAsArrayBuffer(blob);\n  return promise;\n}\nfunction readBlobAsText(blob) {\n  var reader = new FileReader();\n  var promise = fileReaderReady(reader);\n  var match = /charset=([A-Za-z0-9_-]+)/.exec(blob.type);\n  var encoding = match ? match[1] : 'utf-8';\n  reader.readAsText(blob, encoding);\n  return promise;\n}\nfunction readArrayBufferAsText(buf) {\n  var view = new Uint8Array(buf);\n  var chars = new Array(view.length);\n  for (var i = 0; i < view.length; i++) {\n    chars[i] = String.fromCharCode(view[i]);\n  }\n  return chars.join('');\n}\nfunction bufferClone(buf) {\n  if (buf.slice) {\n    return buf.slice(0);\n  } else {\n    var view = new Uint8Array(buf.byteLength);\n    view.set(new Uint8Array(buf));\n    return view.buffer;\n  }\n}\nfunction Body() {\n  this.bodyUsed = false;\n  this._initBody = function (body) {\n    /*\n      fetch-mock wraps the Response object in an ES6 Proxy to\n      provide useful test harness features such as flush. However, on\n      ES5 browsers without fetch or Proxy support pollyfills must be used;\n      the proxy-pollyfill is unable to proxy an attribute unless it exists\n      on the object before the Proxy is created. This change ensures\n      Response.bodyUsed exists on the instance, while maintaining the\n      semantic of setting Request.bodyUsed in the constructor before\n      _initBody is called.\n    */\n    // eslint-disable-next-line no-self-assign\n    this.bodyUsed = this.bodyUsed;\n    this._bodyInit = body;\n    if (!body) {\n      this._noBody = true;\n      this._bodyText = '';\n    } else if (typeof body === 'string') {\n      this._bodyText = body;\n    } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n      this._bodyBlob = body;\n    } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n      this._bodyFormData = body;\n    } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n      this._bodyText = body.toString();\n    } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n      this._bodyArrayBuffer = bufferClone(body.buffer);\n      // IE 10-11 can't handle a DataView body.\n      this._bodyInit = new Blob([this._bodyArrayBuffer]);\n    } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n      this._bodyArrayBuffer = bufferClone(body);\n    } else {\n      this._bodyText = body = Object.prototype.toString.call(body);\n    }\n    if (!this.headers.get('content-type')) {\n      if (typeof body === 'string') {\n        this.headers.set('content-type', 'text/plain;charset=UTF-8');\n      } else if (this._bodyBlob && this._bodyBlob.type) {\n        this.headers.set('content-type', this._bodyBlob.type);\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8');\n      }\n    }\n  };\n  if (support.blob) {\n    this.blob = function () {\n      var rejected = consumed(this);\n      if (rejected) {\n        return rejected;\n      }\n      if (this._bodyBlob) {\n        return Promise.resolve(this._bodyBlob);\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(new Blob([this._bodyArrayBuffer]));\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as blob');\n      } else {\n        return Promise.resolve(new Blob([this._bodyText]));\n      }\n    };\n  }\n  this.arrayBuffer = function () {\n    if (this._bodyArrayBuffer) {\n      var isConsumed = consumed(this);\n      if (isConsumed) {\n        return isConsumed;\n      } else if (ArrayBuffer.isView(this._bodyArrayBuffer)) {\n        return Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset, this._bodyArrayBuffer.byteOffset + this._bodyArrayBuffer.byteLength));\n      } else {\n        return Promise.resolve(this._bodyArrayBuffer);\n      }\n    } else if (support.blob) {\n      return this.blob().then(readBlobAsArrayBuffer);\n    } else {\n      throw new Error('could not read as ArrayBuffer');\n    }\n  };\n  this.text = function () {\n    var rejected = consumed(this);\n    if (rejected) {\n      return rejected;\n    }\n    if (this._bodyBlob) {\n      return readBlobAsText(this._bodyBlob);\n    } else if (this._bodyArrayBuffer) {\n      return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer));\n    } else if (this._bodyFormData) {\n      throw new Error('could not read FormData body as text');\n    } else {\n      return Promise.resolve(this._bodyText);\n    }\n  };\n  if (support.formData) {\n    this.formData = function () {\n      return this.text().then(decode);\n    };\n  }\n  this.json = function () {\n    return this.text().then(JSON.parse);\n  };\n  return this;\n}\n\n// HTTP methods whose capitalization should be normalized\nvar methods = ['CONNECT', 'DELETE', 'GET', 'HEAD', 'OPTIONS', 'PATCH', 'POST', 'PUT', 'TRACE'];\nfunction normalizeMethod(method) {\n  var upcased = method.toUpperCase();\n  return methods.indexOf(upcased) > -1 ? upcased : method;\n}\nexport function Request(input, options) {\n  if (!(this instanceof Request)) {\n    throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.');\n  }\n  options = options || {};\n  var body = options.body;\n  if (input instanceof Request) {\n    if (input.bodyUsed) {\n      throw new TypeError('Already read');\n    }\n    this.url = input.url;\n    this.credentials = input.credentials;\n    if (!options.headers) {\n      this.headers = new Headers(input.headers);\n    }\n    this.method = input.method;\n    this.mode = input.mode;\n    this.signal = input.signal;\n    if (!body && input._bodyInit != null) {\n      body = input._bodyInit;\n      input.bodyUsed = true;\n    }\n  } else {\n    this.url = String(input);\n  }\n  this.credentials = options.credentials || this.credentials || 'same-origin';\n  if (options.headers || !this.headers) {\n    this.headers = new Headers(options.headers);\n  }\n  this.method = normalizeMethod(options.method || this.method || 'GET');\n  this.mode = options.mode || this.mode || null;\n  this.signal = options.signal || this.signal || function () {\n    if ('AbortController' in g) {\n      var ctrl = new AbortController();\n      return ctrl.signal;\n    }\n  }();\n  this.referrer = null;\n  if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n    throw new TypeError('Body not allowed for GET or HEAD requests');\n  }\n  this._initBody(body);\n  if (this.method === 'GET' || this.method === 'HEAD') {\n    if (options.cache === 'no-store' || options.cache === 'no-cache') {\n      // Search for a '_' parameter in the query string\n      var reParamSearch = /([?&])_=[^&]*/;\n      if (reParamSearch.test(this.url)) {\n        // If it already exists then set the value with the current time\n        this.url = this.url.replace(reParamSearch, '$1_=' + new Date().getTime());\n      } else {\n        // Otherwise add a new '_' parameter to the end with the current time\n        var reQueryString = /\\?/;\n        this.url += (reQueryString.test(this.url) ? '&' : '?') + '_=' + new Date().getTime();\n      }\n    }\n  }\n}\nRequest.prototype.clone = function () {\n  return new Request(this, {\n    body: this._bodyInit\n  });\n};\nfunction decode(body) {\n  var form = new FormData();\n  body.trim().split('&').forEach(function (bytes) {\n    if (bytes) {\n      var split = bytes.split('=');\n      var name = split.shift().replace(/\\+/g, ' ');\n      var value = split.join('=').replace(/\\+/g, ' ');\n      form.append(decodeURIComponent(name), decodeURIComponent(value));\n    }\n  });\n  return form;\n}\nfunction parseHeaders(rawHeaders) {\n  var headers = new Headers();\n  // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n  // https://tools.ietf.org/html/rfc7230#section-3.2\n  var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ');\n  // Avoiding split via regex to work around a common IE11 bug with the core-js 3.6.0 regex polyfill\n  // https://github.com/github/fetch/issues/748\n  // https://github.com/zloirock/core-js/issues/751\n  preProcessedHeaders.split('\\r').map(function (header) {\n    return header.indexOf('\\n') === 0 ? header.substr(1, header.length) : header;\n  }).forEach(function (line) {\n    var parts = line.split(':');\n    var key = parts.shift().trim();\n    if (key) {\n      var value = parts.join(':').trim();\n      try {\n        headers.append(key, value);\n      } catch (error) {\n        console.warn('Response ' + error.message);\n      }\n    }\n  });\n  return headers;\n}\nBody.call(Request.prototype);\nexport function Response(bodyInit, options) {\n  if (!(this instanceof Response)) {\n    throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.');\n  }\n  if (!options) {\n    options = {};\n  }\n  this.type = 'default';\n  this.status = options.status === undefined ? 200 : options.status;\n  if (this.status < 200 || this.status > 599) {\n    throw new RangeError(\"Failed to construct 'Response': The status provided (0) is outside the range [200, 599].\");\n  }\n  this.ok = this.status >= 200 && this.status < 300;\n  this.statusText = options.statusText === undefined ? '' : '' + options.statusText;\n  this.headers = new Headers(options.headers);\n  this.url = options.url || '';\n  this._initBody(bodyInit);\n}\nBody.call(Response.prototype);\nResponse.prototype.clone = function () {\n  return new Response(this._bodyInit, {\n    status: this.status,\n    statusText: this.statusText,\n    headers: new Headers(this.headers),\n    url: this.url\n  });\n};\nResponse.error = function () {\n  var response = new Response(null, {\n    status: 200,\n    statusText: ''\n  });\n  response.ok = false;\n  response.status = 0;\n  response.type = 'error';\n  return response;\n};\nvar redirectStatuses = [301, 302, 303, 307, 308];\nResponse.redirect = function (url, status) {\n  if (redirectStatuses.indexOf(status) === -1) {\n    throw new RangeError('Invalid status code');\n  }\n  return new Response(null, {\n    status: status,\n    headers: {\n      location: url\n    }\n  });\n};\nexport var DOMException = g.DOMException;\ntry {\n  new DOMException();\n} catch (err) {\n  DOMException = function (message, name) {\n    this.message = message;\n    this.name = name;\n    var error = Error(message);\n    this.stack = error.stack;\n  };\n  DOMException.prototype = Object.create(Error.prototype);\n  DOMException.prototype.constructor = DOMException;\n}\nexport function fetch(input, init) {\n  return new Promise(function (resolve, reject) {\n    var request = new Request(input, init);\n    if (request.signal && request.signal.aborted) {\n      return reject(new DOMException('Aborted', 'AbortError'));\n    }\n    var xhr = new XMLHttpRequest();\n    function abortXhr() {\n      xhr.abort();\n    }\n    xhr.onload = function () {\n      var options = {\n        statusText: xhr.statusText,\n        headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n      };\n      // This check if specifically for when a user fetches a file locally from the file system\n      // Only if the status is out of a normal range\n      if (request.url.indexOf('file://') === 0 && (xhr.status < 200 || xhr.status > 599)) {\n        options.status = 200;\n      } else {\n        options.status = xhr.status;\n      }\n      options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL');\n      var body = 'response' in xhr ? xhr.response : xhr.responseText;\n      setTimeout(function () {\n        resolve(new Response(body, options));\n      }, 0);\n    };\n    xhr.onerror = function () {\n      setTimeout(function () {\n        reject(new TypeError('Network request failed'));\n      }, 0);\n    };\n    xhr.ontimeout = function () {\n      setTimeout(function () {\n        reject(new TypeError('Network request timed out'));\n      }, 0);\n    };\n    xhr.onabort = function () {\n      setTimeout(function () {\n        reject(new DOMException('Aborted', 'AbortError'));\n      }, 0);\n    };\n    function fixUrl(url) {\n      try {\n        return url === '' && g.location.href ? g.location.href : url;\n      } catch (e) {\n        return url;\n      }\n    }\n    xhr.open(request.method, fixUrl(request.url), true);\n    if (request.credentials === 'include') {\n      xhr.withCredentials = true;\n    } else if (request.credentials === 'omit') {\n      xhr.withCredentials = false;\n    }\n    if ('responseType' in xhr) {\n      if (support.blob) {\n        xhr.responseType = 'blob';\n      } else if (support.arrayBuffer) {\n        xhr.responseType = 'arraybuffer';\n      }\n    }\n    if (init && typeof init.headers === 'object' && !(init.headers instanceof Headers || g.Headers && init.headers instanceof g.Headers)) {\n      var names = [];\n      Object.getOwnPropertyNames(init.headers).forEach(function (name) {\n        names.push(normalizeName(name));\n        xhr.setRequestHeader(name, normalizeValue(init.headers[name]));\n      });\n      request.headers.forEach(function (value, name) {\n        if (names.indexOf(name) === -1) {\n          xhr.setRequestHeader(name, value);\n        }\n      });\n    } else {\n      request.headers.forEach(function (value, name) {\n        xhr.setRequestHeader(name, value);\n      });\n    }\n    if (request.signal) {\n      request.signal.addEventListener('abort', abortXhr);\n      xhr.onreadystatechange = function () {\n        // DONE (success or failure)\n        if (xhr.readyState === 4) {\n          request.signal.removeEventListener('abort', abortXhr);\n        }\n      };\n    }\n    xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit);\n  });\n}\nfetch.polyfill = true;\nif (!g.fetch) {\n  g.fetch = fetch;\n  g.Headers = Headers;\n  g.Request = Request;\n  g.Response = Response;\n}", "map": {"version": 3, "names": ["g", "globalThis", "self", "global", "support", "searchParams", "iterable", "Symbol", "blob", "Blob", "e", "formData", "arrayBuffer", "isDataView", "obj", "DataView", "prototype", "isPrototypeOf", "viewClasses", "isArrayBuffer<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "indexOf", "Object", "toString", "call", "normalizeName", "name", "String", "test", "TypeError", "toLowerCase", "normalizeValue", "value", "iteratorFor", "items", "iterator", "next", "shift", "done", "undefined", "Headers", "headers", "map", "for<PERSON>ach", "append", "Array", "isArray", "header", "length", "getOwnPropertyNames", "oldValue", "get", "has", "hasOwnProperty", "set", "callback", "thisArg", "keys", "push", "values", "entries", "consumed", "body", "_noBody", "bodyUsed", "Promise", "reject", "fileReaderReady", "reader", "resolve", "onload", "result", "onerror", "error", "readBlobAsArrayBuffer", "FileReader", "promise", "readAsA<PERSON>y<PERSON><PERSON>er", "readBlobAsText", "match", "exec", "type", "encoding", "readAsText", "readArrayBufferAsText", "buf", "view", "Uint8Array", "chars", "i", "fromCharCode", "join", "bufferClone", "slice", "byteLength", "buffer", "Body", "_initBody", "_bodyInit", "_bodyText", "_bodyBlob", "FormData", "_bodyFormData", "URLSearchParams", "_body<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rejected", "Error", "isConsumed", "byteOffset", "then", "text", "decode", "json", "JSON", "parse", "methods", "normalizeMethod", "method", "upcased", "toUpperCase", "Request", "input", "options", "url", "credentials", "mode", "signal", "ctrl", "AbortController", "referrer", "cache", "reParamSearch", "replace", "Date", "getTime", "reQueryString", "clone", "form", "trim", "split", "bytes", "decodeURIComponent", "parseHeaders", "rawHeaders", "preProcessedHeaders", "substr", "line", "parts", "key", "console", "warn", "message", "Response", "bodyInit", "status", "RangeError", "ok", "statusText", "response", "redirectStatuses", "redirect", "location", "DOMException", "err", "stack", "create", "constructor", "fetch", "init", "request", "aborted", "xhr", "XMLHttpRequest", "abortXhr", "abort", "getAllResponseHeaders", "responseURL", "responseText", "setTimeout", "ontimeout", "<PERSON>ab<PERSON>", "fixUrl", "href", "open", "withCredentials", "responseType", "names", "setRequestHeader", "addEventListener", "onreadystatechange", "readyState", "removeEventListener", "send", "polyfill"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/whatwg-fetch/fetch.js"], "sourcesContent": ["/* eslint-disable no-prototype-builtins */\nvar g =\n  (typeof globalThis !== 'undefined' && globalThis) ||\n  (typeof self !== 'undefined' && self) ||\n  // eslint-disable-next-line no-undef\n  (typeof global !== 'undefined' && global) ||\n  {}\n\nvar support = {\n  searchParams: 'URLSearchParams' in g,\n  iterable: 'Symbol' in g && 'iterator' in Symbol,\n  blob:\n    'FileReader' in g &&\n    'Blob' in g &&\n    (function() {\n      try {\n        new Blob()\n        return true\n      } catch (e) {\n        return false\n      }\n    })(),\n  formData: 'FormData' in g,\n  arrayBuffer: 'ArrayBuffer' in g\n}\n\nfunction isDataView(obj) {\n  return obj && DataView.prototype.isPrototypeOf(obj)\n}\n\nif (support.arrayBuffer) {\n  var viewClasses = [\n    '[object Int8Array]',\n    '[object Uint8Array]',\n    '[object Uint8ClampedArray]',\n    '[object Int16Array]',\n    '[object Uint16Array]',\n    '[object Int32Array]',\n    '[object Uint32Array]',\n    '[object Float32Array]',\n    '[object Float64Array]'\n  ]\n\n  var isArrayBufferView =\n    ArrayBuffer.isView ||\n    function(obj) {\n      return obj && viewClasses.indexOf(Object.prototype.toString.call(obj)) > -1\n    }\n}\n\nfunction normalizeName(name) {\n  if (typeof name !== 'string') {\n    name = String(name)\n  }\n  if (/[^a-z0-9\\-#$%&'*+.^_`|~!]/i.test(name) || name === '') {\n    throw new TypeError('Invalid character in header field name: \"' + name + '\"')\n  }\n  return name.toLowerCase()\n}\n\nfunction normalizeValue(value) {\n  if (typeof value !== 'string') {\n    value = String(value)\n  }\n  return value\n}\n\n// Build a destructive iterator for the value list\nfunction iteratorFor(items) {\n  var iterator = {\n    next: function() {\n      var value = items.shift()\n      return {done: value === undefined, value: value}\n    }\n  }\n\n  if (support.iterable) {\n    iterator[Symbol.iterator] = function() {\n      return iterator\n    }\n  }\n\n  return iterator\n}\n\nexport function Headers(headers) {\n  this.map = {}\n\n  if (headers instanceof Headers) {\n    headers.forEach(function(value, name) {\n      this.append(name, value)\n    }, this)\n  } else if (Array.isArray(headers)) {\n    headers.forEach(function(header) {\n      if (header.length != 2) {\n        throw new TypeError('Headers constructor: expected name/value pair to be length 2, found' + header.length)\n      }\n      this.append(header[0], header[1])\n    }, this)\n  } else if (headers) {\n    Object.getOwnPropertyNames(headers).forEach(function(name) {\n      this.append(name, headers[name])\n    }, this)\n  }\n}\n\nHeaders.prototype.append = function(name, value) {\n  name = normalizeName(name)\n  value = normalizeValue(value)\n  var oldValue = this.map[name]\n  this.map[name] = oldValue ? oldValue + ', ' + value : value\n}\n\nHeaders.prototype['delete'] = function(name) {\n  delete this.map[normalizeName(name)]\n}\n\nHeaders.prototype.get = function(name) {\n  name = normalizeName(name)\n  return this.has(name) ? this.map[name] : null\n}\n\nHeaders.prototype.has = function(name) {\n  return this.map.hasOwnProperty(normalizeName(name))\n}\n\nHeaders.prototype.set = function(name, value) {\n  this.map[normalizeName(name)] = normalizeValue(value)\n}\n\nHeaders.prototype.forEach = function(callback, thisArg) {\n  for (var name in this.map) {\n    if (this.map.hasOwnProperty(name)) {\n      callback.call(thisArg, this.map[name], name, this)\n    }\n  }\n}\n\nHeaders.prototype.keys = function() {\n  var items = []\n  this.forEach(function(value, name) {\n    items.push(name)\n  })\n  return iteratorFor(items)\n}\n\nHeaders.prototype.values = function() {\n  var items = []\n  this.forEach(function(value) {\n    items.push(value)\n  })\n  return iteratorFor(items)\n}\n\nHeaders.prototype.entries = function() {\n  var items = []\n  this.forEach(function(value, name) {\n    items.push([name, value])\n  })\n  return iteratorFor(items)\n}\n\nif (support.iterable) {\n  Headers.prototype[Symbol.iterator] = Headers.prototype.entries\n}\n\nfunction consumed(body) {\n  if (body._noBody) return\n  if (body.bodyUsed) {\n    return Promise.reject(new TypeError('Already read'))\n  }\n  body.bodyUsed = true\n}\n\nfunction fileReaderReady(reader) {\n  return new Promise(function(resolve, reject) {\n    reader.onload = function() {\n      resolve(reader.result)\n    }\n    reader.onerror = function() {\n      reject(reader.error)\n    }\n  })\n}\n\nfunction readBlobAsArrayBuffer(blob) {\n  var reader = new FileReader()\n  var promise = fileReaderReady(reader)\n  reader.readAsArrayBuffer(blob)\n  return promise\n}\n\nfunction readBlobAsText(blob) {\n  var reader = new FileReader()\n  var promise = fileReaderReady(reader)\n  var match = /charset=([A-Za-z0-9_-]+)/.exec(blob.type)\n  var encoding = match ? match[1] : 'utf-8'\n  reader.readAsText(blob, encoding)\n  return promise\n}\n\nfunction readArrayBufferAsText(buf) {\n  var view = new Uint8Array(buf)\n  var chars = new Array(view.length)\n\n  for (var i = 0; i < view.length; i++) {\n    chars[i] = String.fromCharCode(view[i])\n  }\n  return chars.join('')\n}\n\nfunction bufferClone(buf) {\n  if (buf.slice) {\n    return buf.slice(0)\n  } else {\n    var view = new Uint8Array(buf.byteLength)\n    view.set(new Uint8Array(buf))\n    return view.buffer\n  }\n}\n\nfunction Body() {\n  this.bodyUsed = false\n\n  this._initBody = function(body) {\n    /*\n      fetch-mock wraps the Response object in an ES6 Proxy to\n      provide useful test harness features such as flush. However, on\n      ES5 browsers without fetch or Proxy support pollyfills must be used;\n      the proxy-pollyfill is unable to proxy an attribute unless it exists\n      on the object before the Proxy is created. This change ensures\n      Response.bodyUsed exists on the instance, while maintaining the\n      semantic of setting Request.bodyUsed in the constructor before\n      _initBody is called.\n    */\n    // eslint-disable-next-line no-self-assign\n    this.bodyUsed = this.bodyUsed\n    this._bodyInit = body\n    if (!body) {\n      this._noBody = true;\n      this._bodyText = ''\n    } else if (typeof body === 'string') {\n      this._bodyText = body\n    } else if (support.blob && Blob.prototype.isPrototypeOf(body)) {\n      this._bodyBlob = body\n    } else if (support.formData && FormData.prototype.isPrototypeOf(body)) {\n      this._bodyFormData = body\n    } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n      this._bodyText = body.toString()\n    } else if (support.arrayBuffer && support.blob && isDataView(body)) {\n      this._bodyArrayBuffer = bufferClone(body.buffer)\n      // IE 10-11 can't handle a DataView body.\n      this._bodyInit = new Blob([this._bodyArrayBuffer])\n    } else if (support.arrayBuffer && (ArrayBuffer.prototype.isPrototypeOf(body) || isArrayBufferView(body))) {\n      this._bodyArrayBuffer = bufferClone(body)\n    } else {\n      this._bodyText = body = Object.prototype.toString.call(body)\n    }\n\n    if (!this.headers.get('content-type')) {\n      if (typeof body === 'string') {\n        this.headers.set('content-type', 'text/plain;charset=UTF-8')\n      } else if (this._bodyBlob && this._bodyBlob.type) {\n        this.headers.set('content-type', this._bodyBlob.type)\n      } else if (support.searchParams && URLSearchParams.prototype.isPrototypeOf(body)) {\n        this.headers.set('content-type', 'application/x-www-form-urlencoded;charset=UTF-8')\n      }\n    }\n  }\n\n  if (support.blob) {\n    this.blob = function() {\n      var rejected = consumed(this)\n      if (rejected) {\n        return rejected\n      }\n\n      if (this._bodyBlob) {\n        return Promise.resolve(this._bodyBlob)\n      } else if (this._bodyArrayBuffer) {\n        return Promise.resolve(new Blob([this._bodyArrayBuffer]))\n      } else if (this._bodyFormData) {\n        throw new Error('could not read FormData body as blob')\n      } else {\n        return Promise.resolve(new Blob([this._bodyText]))\n      }\n    }\n  }\n\n  this.arrayBuffer = function() {\n    if (this._bodyArrayBuffer) {\n      var isConsumed = consumed(this)\n      if (isConsumed) {\n        return isConsumed\n      } else if (ArrayBuffer.isView(this._bodyArrayBuffer)) {\n        return Promise.resolve(\n          this._bodyArrayBuffer.buffer.slice(\n            this._bodyArrayBuffer.byteOffset,\n            this._bodyArrayBuffer.byteOffset + this._bodyArrayBuffer.byteLength\n          )\n        )\n      } else {\n        return Promise.resolve(this._bodyArrayBuffer)\n      }\n    } else if (support.blob) {\n      return this.blob().then(readBlobAsArrayBuffer)\n    } else {\n      throw new Error('could not read as ArrayBuffer')\n    }\n  }\n\n  this.text = function() {\n    var rejected = consumed(this)\n    if (rejected) {\n      return rejected\n    }\n\n    if (this._bodyBlob) {\n      return readBlobAsText(this._bodyBlob)\n    } else if (this._bodyArrayBuffer) {\n      return Promise.resolve(readArrayBufferAsText(this._bodyArrayBuffer))\n    } else if (this._bodyFormData) {\n      throw new Error('could not read FormData body as text')\n    } else {\n      return Promise.resolve(this._bodyText)\n    }\n  }\n\n  if (support.formData) {\n    this.formData = function() {\n      return this.text().then(decode)\n    }\n  }\n\n  this.json = function() {\n    return this.text().then(JSON.parse)\n  }\n\n  return this\n}\n\n// HTTP methods whose capitalization should be normalized\nvar methods = ['CONNECT', 'DELETE', 'GET', 'HEAD', 'OPTIONS', 'PATCH', 'POST', 'PUT', 'TRACE']\n\nfunction normalizeMethod(method) {\n  var upcased = method.toUpperCase()\n  return methods.indexOf(upcased) > -1 ? upcased : method\n}\n\nexport function Request(input, options) {\n  if (!(this instanceof Request)) {\n    throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n  }\n\n  options = options || {}\n  var body = options.body\n\n  if (input instanceof Request) {\n    if (input.bodyUsed) {\n      throw new TypeError('Already read')\n    }\n    this.url = input.url\n    this.credentials = input.credentials\n    if (!options.headers) {\n      this.headers = new Headers(input.headers)\n    }\n    this.method = input.method\n    this.mode = input.mode\n    this.signal = input.signal\n    if (!body && input._bodyInit != null) {\n      body = input._bodyInit\n      input.bodyUsed = true\n    }\n  } else {\n    this.url = String(input)\n  }\n\n  this.credentials = options.credentials || this.credentials || 'same-origin'\n  if (options.headers || !this.headers) {\n    this.headers = new Headers(options.headers)\n  }\n  this.method = normalizeMethod(options.method || this.method || 'GET')\n  this.mode = options.mode || this.mode || null\n  this.signal = options.signal || this.signal || (function () {\n    if ('AbortController' in g) {\n      var ctrl = new AbortController();\n      return ctrl.signal;\n    }\n  }());\n  this.referrer = null\n\n  if ((this.method === 'GET' || this.method === 'HEAD') && body) {\n    throw new TypeError('Body not allowed for GET or HEAD requests')\n  }\n  this._initBody(body)\n\n  if (this.method === 'GET' || this.method === 'HEAD') {\n    if (options.cache === 'no-store' || options.cache === 'no-cache') {\n      // Search for a '_' parameter in the query string\n      var reParamSearch = /([?&])_=[^&]*/\n      if (reParamSearch.test(this.url)) {\n        // If it already exists then set the value with the current time\n        this.url = this.url.replace(reParamSearch, '$1_=' + new Date().getTime())\n      } else {\n        // Otherwise add a new '_' parameter to the end with the current time\n        var reQueryString = /\\?/\n        this.url += (reQueryString.test(this.url) ? '&' : '?') + '_=' + new Date().getTime()\n      }\n    }\n  }\n}\n\nRequest.prototype.clone = function() {\n  return new Request(this, {body: this._bodyInit})\n}\n\nfunction decode(body) {\n  var form = new FormData()\n  body\n    .trim()\n    .split('&')\n    .forEach(function(bytes) {\n      if (bytes) {\n        var split = bytes.split('=')\n        var name = split.shift().replace(/\\+/g, ' ')\n        var value = split.join('=').replace(/\\+/g, ' ')\n        form.append(decodeURIComponent(name), decodeURIComponent(value))\n      }\n    })\n  return form\n}\n\nfunction parseHeaders(rawHeaders) {\n  var headers = new Headers()\n  // Replace instances of \\r\\n and \\n followed by at least one space or horizontal tab with a space\n  // https://tools.ietf.org/html/rfc7230#section-3.2\n  var preProcessedHeaders = rawHeaders.replace(/\\r?\\n[\\t ]+/g, ' ')\n  // Avoiding split via regex to work around a common IE11 bug with the core-js 3.6.0 regex polyfill\n  // https://github.com/github/fetch/issues/748\n  // https://github.com/zloirock/core-js/issues/751\n  preProcessedHeaders\n    .split('\\r')\n    .map(function(header) {\n      return header.indexOf('\\n') === 0 ? header.substr(1, header.length) : header\n    })\n    .forEach(function(line) {\n      var parts = line.split(':')\n      var key = parts.shift().trim()\n      if (key) {\n        var value = parts.join(':').trim()\n        try {\n          headers.append(key, value)\n        } catch (error) {\n          console.warn('Response ' + error.message)\n        }\n      }\n    })\n  return headers\n}\n\nBody.call(Request.prototype)\n\nexport function Response(bodyInit, options) {\n  if (!(this instanceof Response)) {\n    throw new TypeError('Please use the \"new\" operator, this DOM object constructor cannot be called as a function.')\n  }\n  if (!options) {\n    options = {}\n  }\n\n  this.type = 'default'\n  this.status = options.status === undefined ? 200 : options.status\n  if (this.status < 200 || this.status > 599) {\n    throw new RangeError(\"Failed to construct 'Response': The status provided (0) is outside the range [200, 599].\")\n  }\n  this.ok = this.status >= 200 && this.status < 300\n  this.statusText = options.statusText === undefined ? '' : '' + options.statusText\n  this.headers = new Headers(options.headers)\n  this.url = options.url || ''\n  this._initBody(bodyInit)\n}\n\nBody.call(Response.prototype)\n\nResponse.prototype.clone = function() {\n  return new Response(this._bodyInit, {\n    status: this.status,\n    statusText: this.statusText,\n    headers: new Headers(this.headers),\n    url: this.url\n  })\n}\n\nResponse.error = function() {\n  var response = new Response(null, {status: 200, statusText: ''})\n  response.ok = false\n  response.status = 0\n  response.type = 'error'\n  return response\n}\n\nvar redirectStatuses = [301, 302, 303, 307, 308]\n\nResponse.redirect = function(url, status) {\n  if (redirectStatuses.indexOf(status) === -1) {\n    throw new RangeError('Invalid status code')\n  }\n\n  return new Response(null, {status: status, headers: {location: url}})\n}\n\nexport var DOMException = g.DOMException\ntry {\n  new DOMException()\n} catch (err) {\n  DOMException = function(message, name) {\n    this.message = message\n    this.name = name\n    var error = Error(message)\n    this.stack = error.stack\n  }\n  DOMException.prototype = Object.create(Error.prototype)\n  DOMException.prototype.constructor = DOMException\n}\n\nexport function fetch(input, init) {\n  return new Promise(function(resolve, reject) {\n    var request = new Request(input, init)\n\n    if (request.signal && request.signal.aborted) {\n      return reject(new DOMException('Aborted', 'AbortError'))\n    }\n\n    var xhr = new XMLHttpRequest()\n\n    function abortXhr() {\n      xhr.abort()\n    }\n\n    xhr.onload = function() {\n      var options = {\n        statusText: xhr.statusText,\n        headers: parseHeaders(xhr.getAllResponseHeaders() || '')\n      }\n      // This check if specifically for when a user fetches a file locally from the file system\n      // Only if the status is out of a normal range\n      if (request.url.indexOf('file://') === 0 && (xhr.status < 200 || xhr.status > 599)) {\n        options.status = 200;\n      } else {\n        options.status = xhr.status;\n      }\n      options.url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL')\n      var body = 'response' in xhr ? xhr.response : xhr.responseText\n      setTimeout(function() {\n        resolve(new Response(body, options))\n      }, 0)\n    }\n\n    xhr.onerror = function() {\n      setTimeout(function() {\n        reject(new TypeError('Network request failed'))\n      }, 0)\n    }\n\n    xhr.ontimeout = function() {\n      setTimeout(function() {\n        reject(new TypeError('Network request timed out'))\n      }, 0)\n    }\n\n    xhr.onabort = function() {\n      setTimeout(function() {\n        reject(new DOMException('Aborted', 'AbortError'))\n      }, 0)\n    }\n\n    function fixUrl(url) {\n      try {\n        return url === '' && g.location.href ? g.location.href : url\n      } catch (e) {\n        return url\n      }\n    }\n\n    xhr.open(request.method, fixUrl(request.url), true)\n\n    if (request.credentials === 'include') {\n      xhr.withCredentials = true\n    } else if (request.credentials === 'omit') {\n      xhr.withCredentials = false\n    }\n\n    if ('responseType' in xhr) {\n      if (support.blob) {\n        xhr.responseType = 'blob'\n      } else if (\n        support.arrayBuffer\n      ) {\n        xhr.responseType = 'arraybuffer'\n      }\n    }\n\n    if (init && typeof init.headers === 'object' && !(init.headers instanceof Headers || (g.Headers && init.headers instanceof g.Headers))) {\n      var names = [];\n      Object.getOwnPropertyNames(init.headers).forEach(function(name) {\n        names.push(normalizeName(name))\n        xhr.setRequestHeader(name, normalizeValue(init.headers[name]))\n      })\n      request.headers.forEach(function(value, name) {\n        if (names.indexOf(name) === -1) {\n          xhr.setRequestHeader(name, value)\n        }\n      })\n    } else {\n      request.headers.forEach(function(value, name) {\n        xhr.setRequestHeader(name, value)\n      })\n    }\n\n    if (request.signal) {\n      request.signal.addEventListener('abort', abortXhr)\n\n      xhr.onreadystatechange = function() {\n        // DONE (success or failure)\n        if (xhr.readyState === 4) {\n          request.signal.removeEventListener('abort', abortXhr)\n        }\n      }\n    }\n\n    xhr.send(typeof request._bodyInit === 'undefined' ? null : request._bodyInit)\n  })\n}\n\nfetch.polyfill = true\n\nif (!g.fetch) {\n  g.fetch = fetch\n  g.Headers = Headers\n  g.Request = Request\n  g.Response = Response\n}\n"], "mappings": "AAAA;AACA,IAAIA,CAAC,GACF,OAAOC,UAAU,KAAK,WAAW,IAAIA,UAAU,IAC/C,OAAOC,IAAI,KAAK,WAAW,IAAIA,IAAK;AACrC;AACC,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAO,IACzC,CAAC,CAAC;AAEJ,IAAIC,OAAO,GAAG;EACZC,YAAY,EAAE,iBAAiB,IAAIL,CAAC;EACpCM,QAAQ,EAAE,QAAQ,IAAIN,CAAC,IAAI,UAAU,IAAIO,MAAM;EAC/CC,IAAI,EACF,YAAY,IAAIR,CAAC,IACjB,MAAM,IAAIA,CAAC,IACV,YAAW;IACV,IAAI;MACF,IAAIS,IAAI,CAAC,CAAC;MACV,OAAO,IAAI;IACb,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV,OAAO,KAAK;IACd;EACF,CAAC,CAAE,CAAC;EACNC,QAAQ,EAAE,UAAU,IAAIX,CAAC;EACzBY,WAAW,EAAE,aAAa,IAAIZ;AAChC,CAAC;AAED,SAASa,UAAUA,CAACC,GAAG,EAAE;EACvB,OAAOA,GAAG,IAAIC,QAAQ,CAACC,SAAS,CAACC,aAAa,CAACH,GAAG,CAAC;AACrD;AAEA,IAAIV,OAAO,CAACQ,WAAW,EAAE;EACvB,IAAIM,WAAW,GAAG,CAChB,oBAAoB,EACpB,qBAAqB,EACrB,4BAA4B,EAC5B,qBAAqB,EACrB,sBAAsB,EACtB,qBAAqB,EACrB,sBAAsB,EACtB,uBAAuB,EACvB,uBAAuB,CACxB;EAED,IAAIC,iBAAiB,GACnBC,WAAW,CAACC,MAAM,IAClB,UAASP,GAAG,EAAE;IACZ,OAAOA,GAAG,IAAII,WAAW,CAACI,OAAO,CAACC,MAAM,CAACP,SAAS,CAACQ,QAAQ,CAACC,IAAI,CAACX,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7E,CAAC;AACL;AAEA,SAASY,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5BA,IAAI,GAAGC,MAAM,CAACD,IAAI,CAAC;EACrB;EACA,IAAI,4BAA4B,CAACE,IAAI,CAACF,IAAI,CAAC,IAAIA,IAAI,KAAK,EAAE,EAAE;IAC1D,MAAM,IAAIG,SAAS,CAAC,2CAA2C,GAAGH,IAAI,GAAG,GAAG,CAAC;EAC/E;EACA,OAAOA,IAAI,CAACI,WAAW,CAAC,CAAC;AAC3B;AAEA,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7BA,KAAK,GAAGL,MAAM,CAACK,KAAK,CAAC;EACvB;EACA,OAAOA,KAAK;AACd;;AAEA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAIC,QAAQ,GAAG;IACbC,IAAI,EAAE,SAAAA,CAAA,EAAW;MACf,IAAIJ,KAAK,GAAGE,KAAK,CAACG,KAAK,CAAC,CAAC;MACzB,OAAO;QAACC,IAAI,EAAEN,KAAK,KAAKO,SAAS;QAAEP,KAAK,EAAEA;MAAK,CAAC;IAClD;EACF,CAAC;EAED,IAAI7B,OAAO,CAACE,QAAQ,EAAE;IACpB8B,QAAQ,CAAC7B,MAAM,CAAC6B,QAAQ,CAAC,GAAG,YAAW;MACrC,OAAOA,QAAQ;IACjB,CAAC;EACH;EAEA,OAAOA,QAAQ;AACjB;AAEA,OAAO,SAASK,OAAOA,CAACC,OAAO,EAAE;EAC/B,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC;EAEb,IAAID,OAAO,YAAYD,OAAO,EAAE;IAC9BC,OAAO,CAACE,OAAO,CAAC,UAASX,KAAK,EAAEN,IAAI,EAAE;MACpC,IAAI,CAACkB,MAAM,CAAClB,IAAI,EAAEM,KAAK,CAAC;IAC1B,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,MAAM,IAAIa,KAAK,CAACC,OAAO,CAACL,OAAO,CAAC,EAAE;IACjCA,OAAO,CAACE,OAAO,CAAC,UAASI,MAAM,EAAE;MAC/B,IAAIA,MAAM,CAACC,MAAM,IAAI,CAAC,EAAE;QACtB,MAAM,IAAInB,SAAS,CAAC,qEAAqE,GAAGkB,MAAM,CAACC,MAAM,CAAC;MAC5G;MACA,IAAI,CAACJ,MAAM,CAACG,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,MAAM,IAAIN,OAAO,EAAE;IAClBnB,MAAM,CAAC2B,mBAAmB,CAACR,OAAO,CAAC,CAACE,OAAO,CAAC,UAASjB,IAAI,EAAE;MACzD,IAAI,CAACkB,MAAM,CAAClB,IAAI,EAAEe,OAAO,CAACf,IAAI,CAAC,CAAC;IAClC,CAAC,EAAE,IAAI,CAAC;EACV;AACF;AAEAc,OAAO,CAACzB,SAAS,CAAC6B,MAAM,GAAG,UAASlB,IAAI,EAAEM,KAAK,EAAE;EAC/CN,IAAI,GAAGD,aAAa,CAACC,IAAI,CAAC;EAC1BM,KAAK,GAAGD,cAAc,CAACC,KAAK,CAAC;EAC7B,IAAIkB,QAAQ,GAAG,IAAI,CAACR,GAAG,CAAChB,IAAI,CAAC;EAC7B,IAAI,CAACgB,GAAG,CAAChB,IAAI,CAAC,GAAGwB,QAAQ,GAAGA,QAAQ,GAAG,IAAI,GAAGlB,KAAK,GAAGA,KAAK;AAC7D,CAAC;AAEDQ,OAAO,CAACzB,SAAS,CAAC,QAAQ,CAAC,GAAG,UAASW,IAAI,EAAE;EAC3C,OAAO,IAAI,CAACgB,GAAG,CAACjB,aAAa,CAACC,IAAI,CAAC,CAAC;AACtC,CAAC;AAEDc,OAAO,CAACzB,SAAS,CAACoC,GAAG,GAAG,UAASzB,IAAI,EAAE;EACrCA,IAAI,GAAGD,aAAa,CAACC,IAAI,CAAC;EAC1B,OAAO,IAAI,CAAC0B,GAAG,CAAC1B,IAAI,CAAC,GAAG,IAAI,CAACgB,GAAG,CAAChB,IAAI,CAAC,GAAG,IAAI;AAC/C,CAAC;AAEDc,OAAO,CAACzB,SAAS,CAACqC,GAAG,GAAG,UAAS1B,IAAI,EAAE;EACrC,OAAO,IAAI,CAACgB,GAAG,CAACW,cAAc,CAAC5B,aAAa,CAACC,IAAI,CAAC,CAAC;AACrD,CAAC;AAEDc,OAAO,CAACzB,SAAS,CAACuC,GAAG,GAAG,UAAS5B,IAAI,EAAEM,KAAK,EAAE;EAC5C,IAAI,CAACU,GAAG,CAACjB,aAAa,CAACC,IAAI,CAAC,CAAC,GAAGK,cAAc,CAACC,KAAK,CAAC;AACvD,CAAC;AAEDQ,OAAO,CAACzB,SAAS,CAAC4B,OAAO,GAAG,UAASY,QAAQ,EAAEC,OAAO,EAAE;EACtD,KAAK,IAAI9B,IAAI,IAAI,IAAI,CAACgB,GAAG,EAAE;IACzB,IAAI,IAAI,CAACA,GAAG,CAACW,cAAc,CAAC3B,IAAI,CAAC,EAAE;MACjC6B,QAAQ,CAAC/B,IAAI,CAACgC,OAAO,EAAE,IAAI,CAACd,GAAG,CAAChB,IAAI,CAAC,EAAEA,IAAI,EAAE,IAAI,CAAC;IACpD;EACF;AACF,CAAC;AAEDc,OAAO,CAACzB,SAAS,CAAC0C,IAAI,GAAG,YAAW;EAClC,IAAIvB,KAAK,GAAG,EAAE;EACd,IAAI,CAACS,OAAO,CAAC,UAASX,KAAK,EAAEN,IAAI,EAAE;IACjCQ,KAAK,CAACwB,IAAI,CAAChC,IAAI,CAAC;EAClB,CAAC,CAAC;EACF,OAAOO,WAAW,CAACC,KAAK,CAAC;AAC3B,CAAC;AAEDM,OAAO,CAACzB,SAAS,CAAC4C,MAAM,GAAG,YAAW;EACpC,IAAIzB,KAAK,GAAG,EAAE;EACd,IAAI,CAACS,OAAO,CAAC,UAASX,KAAK,EAAE;IAC3BE,KAAK,CAACwB,IAAI,CAAC1B,KAAK,CAAC;EACnB,CAAC,CAAC;EACF,OAAOC,WAAW,CAACC,KAAK,CAAC;AAC3B,CAAC;AAEDM,OAAO,CAACzB,SAAS,CAAC6C,OAAO,GAAG,YAAW;EACrC,IAAI1B,KAAK,GAAG,EAAE;EACd,IAAI,CAACS,OAAO,CAAC,UAASX,KAAK,EAAEN,IAAI,EAAE;IACjCQ,KAAK,CAACwB,IAAI,CAAC,CAAChC,IAAI,EAAEM,KAAK,CAAC,CAAC;EAC3B,CAAC,CAAC;EACF,OAAOC,WAAW,CAACC,KAAK,CAAC;AAC3B,CAAC;AAED,IAAI/B,OAAO,CAACE,QAAQ,EAAE;EACpBmC,OAAO,CAACzB,SAAS,CAACT,MAAM,CAAC6B,QAAQ,CAAC,GAAGK,OAAO,CAACzB,SAAS,CAAC6C,OAAO;AAChE;AAEA,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAIA,IAAI,CAACC,OAAO,EAAE;EAClB,IAAID,IAAI,CAACE,QAAQ,EAAE;IACjB,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIrC,SAAS,CAAC,cAAc,CAAC,CAAC;EACtD;EACAiC,IAAI,CAACE,QAAQ,GAAG,IAAI;AACtB;AAEA,SAASG,eAAeA,CAACC,MAAM,EAAE;EAC/B,OAAO,IAAIH,OAAO,CAAC,UAASI,OAAO,EAAEH,MAAM,EAAE;IAC3CE,MAAM,CAACE,MAAM,GAAG,YAAW;MACzBD,OAAO,CAACD,MAAM,CAACG,MAAM,CAAC;IACxB,CAAC;IACDH,MAAM,CAACI,OAAO,GAAG,YAAW;MAC1BN,MAAM,CAACE,MAAM,CAACK,KAAK,CAAC;IACtB,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,SAASC,qBAAqBA,CAACnE,IAAI,EAAE;EACnC,IAAI6D,MAAM,GAAG,IAAIO,UAAU,CAAC,CAAC;EAC7B,IAAIC,OAAO,GAAGT,eAAe,CAACC,MAAM,CAAC;EACrCA,MAAM,CAACS,iBAAiB,CAACtE,IAAI,CAAC;EAC9B,OAAOqE,OAAO;AAChB;AAEA,SAASE,cAAcA,CAACvE,IAAI,EAAE;EAC5B,IAAI6D,MAAM,GAAG,IAAIO,UAAU,CAAC,CAAC;EAC7B,IAAIC,OAAO,GAAGT,eAAe,CAACC,MAAM,CAAC;EACrC,IAAIW,KAAK,GAAG,0BAA0B,CAACC,IAAI,CAACzE,IAAI,CAAC0E,IAAI,CAAC;EACtD,IAAIC,QAAQ,GAAGH,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO;EACzCX,MAAM,CAACe,UAAU,CAAC5E,IAAI,EAAE2E,QAAQ,CAAC;EACjC,OAAON,OAAO;AAChB;AAEA,SAASQ,qBAAqBA,CAACC,GAAG,EAAE;EAClC,IAAIC,IAAI,GAAG,IAAIC,UAAU,CAACF,GAAG,CAAC;EAC9B,IAAIG,KAAK,GAAG,IAAI3C,KAAK,CAACyC,IAAI,CAACtC,MAAM,CAAC;EAElC,KAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACtC,MAAM,EAAEyC,CAAC,EAAE,EAAE;IACpCD,KAAK,CAACC,CAAC,CAAC,GAAG9D,MAAM,CAAC+D,YAAY,CAACJ,IAAI,CAACG,CAAC,CAAC,CAAC;EACzC;EACA,OAAOD,KAAK,CAACG,IAAI,CAAC,EAAE,CAAC;AACvB;AAEA,SAASC,WAAWA,CAACP,GAAG,EAAE;EACxB,IAAIA,GAAG,CAACQ,KAAK,EAAE;IACb,OAAOR,GAAG,CAACQ,KAAK,CAAC,CAAC,CAAC;EACrB,CAAC,MAAM;IACL,IAAIP,IAAI,GAAG,IAAIC,UAAU,CAACF,GAAG,CAACS,UAAU,CAAC;IACzCR,IAAI,CAAChC,GAAG,CAAC,IAAIiC,UAAU,CAACF,GAAG,CAAC,CAAC;IAC7B,OAAOC,IAAI,CAACS,MAAM;EACpB;AACF;AAEA,SAASC,IAAIA,CAAA,EAAG;EACd,IAAI,CAAChC,QAAQ,GAAG,KAAK;EAErB,IAAI,CAACiC,SAAS,GAAG,UAASnC,IAAI,EAAE;IAC9B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI;IACA,IAAI,CAACE,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC7B,IAAI,CAACkC,SAAS,GAAGpC,IAAI;IACrB,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAACC,OAAO,GAAG,IAAI;MACnB,IAAI,CAACoC,SAAS,GAAG,EAAE;IACrB,CAAC,MAAM,IAAI,OAAOrC,IAAI,KAAK,QAAQ,EAAE;MACnC,IAAI,CAACqC,SAAS,GAAGrC,IAAI;IACvB,CAAC,MAAM,IAAI3D,OAAO,CAACI,IAAI,IAAIC,IAAI,CAACO,SAAS,CAACC,aAAa,CAAC8C,IAAI,CAAC,EAAE;MAC7D,IAAI,CAACsC,SAAS,GAAGtC,IAAI;IACvB,CAAC,MAAM,IAAI3D,OAAO,CAACO,QAAQ,IAAI2F,QAAQ,CAACtF,SAAS,CAACC,aAAa,CAAC8C,IAAI,CAAC,EAAE;MACrE,IAAI,CAACwC,aAAa,GAAGxC,IAAI;IAC3B,CAAC,MAAM,IAAI3D,OAAO,CAACC,YAAY,IAAImG,eAAe,CAACxF,SAAS,CAACC,aAAa,CAAC8C,IAAI,CAAC,EAAE;MAChF,IAAI,CAACqC,SAAS,GAAGrC,IAAI,CAACvC,QAAQ,CAAC,CAAC;IAClC,CAAC,MAAM,IAAIpB,OAAO,CAACQ,WAAW,IAAIR,OAAO,CAACI,IAAI,IAAIK,UAAU,CAACkD,IAAI,CAAC,EAAE;MAClE,IAAI,CAAC0C,gBAAgB,GAAGZ,WAAW,CAAC9B,IAAI,CAACiC,MAAM,CAAC;MAChD;MACA,IAAI,CAACG,SAAS,GAAG,IAAI1F,IAAI,CAAC,CAAC,IAAI,CAACgG,gBAAgB,CAAC,CAAC;IACpD,CAAC,MAAM,IAAIrG,OAAO,CAACQ,WAAW,KAAKQ,WAAW,CAACJ,SAAS,CAACC,aAAa,CAAC8C,IAAI,CAAC,IAAI5C,iBAAiB,CAAC4C,IAAI,CAAC,CAAC,EAAE;MACxG,IAAI,CAAC0C,gBAAgB,GAAGZ,WAAW,CAAC9B,IAAI,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAACqC,SAAS,GAAGrC,IAAI,GAAGxC,MAAM,CAACP,SAAS,CAACQ,QAAQ,CAACC,IAAI,CAACsC,IAAI,CAAC;IAC9D;IAEA,IAAI,CAAC,IAAI,CAACrB,OAAO,CAACU,GAAG,CAAC,cAAc,CAAC,EAAE;MACrC,IAAI,OAAOW,IAAI,KAAK,QAAQ,EAAE;QAC5B,IAAI,CAACrB,OAAO,CAACa,GAAG,CAAC,cAAc,EAAE,0BAA0B,CAAC;MAC9D,CAAC,MAAM,IAAI,IAAI,CAAC8C,SAAS,IAAI,IAAI,CAACA,SAAS,CAACnB,IAAI,EAAE;QAChD,IAAI,CAACxC,OAAO,CAACa,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC8C,SAAS,CAACnB,IAAI,CAAC;MACvD,CAAC,MAAM,IAAI9E,OAAO,CAACC,YAAY,IAAImG,eAAe,CAACxF,SAAS,CAACC,aAAa,CAAC8C,IAAI,CAAC,EAAE;QAChF,IAAI,CAACrB,OAAO,CAACa,GAAG,CAAC,cAAc,EAAE,iDAAiD,CAAC;MACrF;IACF;EACF,CAAC;EAED,IAAInD,OAAO,CAACI,IAAI,EAAE;IAChB,IAAI,CAACA,IAAI,GAAG,YAAW;MACrB,IAAIkG,QAAQ,GAAG5C,QAAQ,CAAC,IAAI,CAAC;MAC7B,IAAI4C,QAAQ,EAAE;QACZ,OAAOA,QAAQ;MACjB;MAEA,IAAI,IAAI,CAACL,SAAS,EAAE;QAClB,OAAOnC,OAAO,CAACI,OAAO,CAAC,IAAI,CAAC+B,SAAS,CAAC;MACxC,CAAC,MAAM,IAAI,IAAI,CAACI,gBAAgB,EAAE;QAChC,OAAOvC,OAAO,CAACI,OAAO,CAAC,IAAI7D,IAAI,CAAC,CAAC,IAAI,CAACgG,gBAAgB,CAAC,CAAC,CAAC;MAC3D,CAAC,MAAM,IAAI,IAAI,CAACF,aAAa,EAAE;QAC7B,MAAM,IAAII,KAAK,CAAC,sCAAsC,CAAC;MACzD,CAAC,MAAM;QACL,OAAOzC,OAAO,CAACI,OAAO,CAAC,IAAI7D,IAAI,CAAC,CAAC,IAAI,CAAC2F,SAAS,CAAC,CAAC,CAAC;MACpD;IACF,CAAC;EACH;EAEA,IAAI,CAACxF,WAAW,GAAG,YAAW;IAC5B,IAAI,IAAI,CAAC6F,gBAAgB,EAAE;MACzB,IAAIG,UAAU,GAAG9C,QAAQ,CAAC,IAAI,CAAC;MAC/B,IAAI8C,UAAU,EAAE;QACd,OAAOA,UAAU;MACnB,CAAC,MAAM,IAAIxF,WAAW,CAACC,MAAM,CAAC,IAAI,CAACoF,gBAAgB,CAAC,EAAE;QACpD,OAAOvC,OAAO,CAACI,OAAO,CACpB,IAAI,CAACmC,gBAAgB,CAACT,MAAM,CAACF,KAAK,CAChC,IAAI,CAACW,gBAAgB,CAACI,UAAU,EAChC,IAAI,CAACJ,gBAAgB,CAACI,UAAU,GAAG,IAAI,CAACJ,gBAAgB,CAACV,UAC3D,CACF,CAAC;MACH,CAAC,MAAM;QACL,OAAO7B,OAAO,CAACI,OAAO,CAAC,IAAI,CAACmC,gBAAgB,CAAC;MAC/C;IACF,CAAC,MAAM,IAAIrG,OAAO,CAACI,IAAI,EAAE;MACvB,OAAO,IAAI,CAACA,IAAI,CAAC,CAAC,CAACsG,IAAI,CAACnC,qBAAqB,CAAC;IAChD,CAAC,MAAM;MACL,MAAM,IAAIgC,KAAK,CAAC,+BAA+B,CAAC;IAClD;EACF,CAAC;EAED,IAAI,CAACI,IAAI,GAAG,YAAW;IACrB,IAAIL,QAAQ,GAAG5C,QAAQ,CAAC,IAAI,CAAC;IAC7B,IAAI4C,QAAQ,EAAE;MACZ,OAAOA,QAAQ;IACjB;IAEA,IAAI,IAAI,CAACL,SAAS,EAAE;MAClB,OAAOtB,cAAc,CAAC,IAAI,CAACsB,SAAS,CAAC;IACvC,CAAC,MAAM,IAAI,IAAI,CAACI,gBAAgB,EAAE;MAChC,OAAOvC,OAAO,CAACI,OAAO,CAACe,qBAAqB,CAAC,IAAI,CAACoB,gBAAgB,CAAC,CAAC;IACtE,CAAC,MAAM,IAAI,IAAI,CAACF,aAAa,EAAE;MAC7B,MAAM,IAAII,KAAK,CAAC,sCAAsC,CAAC;IACzD,CAAC,MAAM;MACL,OAAOzC,OAAO,CAACI,OAAO,CAAC,IAAI,CAAC8B,SAAS,CAAC;IACxC;EACF,CAAC;EAED,IAAIhG,OAAO,CAACO,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,GAAG,YAAW;MACzB,OAAO,IAAI,CAACoG,IAAI,CAAC,CAAC,CAACD,IAAI,CAACE,MAAM,CAAC;IACjC,CAAC;EACH;EAEA,IAAI,CAACC,IAAI,GAAG,YAAW;IACrB,OAAO,IAAI,CAACF,IAAI,CAAC,CAAC,CAACD,IAAI,CAACI,IAAI,CAACC,KAAK,CAAC;EACrC,CAAC;EAED,OAAO,IAAI;AACb;;AAEA;AACA,IAAIC,OAAO,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC;AAE9F,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC/B,IAAIC,OAAO,GAAGD,MAAM,CAACE,WAAW,CAAC,CAAC;EAClC,OAAOJ,OAAO,CAAC9F,OAAO,CAACiG,OAAO,CAAC,GAAG,CAAC,CAAC,GAAGA,OAAO,GAAGD,MAAM;AACzD;AAEA,OAAO,SAASG,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACtC,IAAI,EAAE,IAAI,YAAYF,OAAO,CAAC,EAAE;IAC9B,MAAM,IAAI3F,SAAS,CAAC,4FAA4F,CAAC;EACnH;EAEA6F,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAI5D,IAAI,GAAG4D,OAAO,CAAC5D,IAAI;EAEvB,IAAI2D,KAAK,YAAYD,OAAO,EAAE;IAC5B,IAAIC,KAAK,CAACzD,QAAQ,EAAE;MAClB,MAAM,IAAInC,SAAS,CAAC,cAAc,CAAC;IACrC;IACA,IAAI,CAAC8F,GAAG,GAAGF,KAAK,CAACE,GAAG;IACpB,IAAI,CAACC,WAAW,GAAGH,KAAK,CAACG,WAAW;IACpC,IAAI,CAACF,OAAO,CAACjF,OAAO,EAAE;MACpB,IAAI,CAACA,OAAO,GAAG,IAAID,OAAO,CAACiF,KAAK,CAAChF,OAAO,CAAC;IAC3C;IACA,IAAI,CAAC4E,MAAM,GAAGI,KAAK,CAACJ,MAAM;IAC1B,IAAI,CAACQ,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACtB,IAAI,CAACC,MAAM,GAAGL,KAAK,CAACK,MAAM;IAC1B,IAAI,CAAChE,IAAI,IAAI2D,KAAK,CAACvB,SAAS,IAAI,IAAI,EAAE;MACpCpC,IAAI,GAAG2D,KAAK,CAACvB,SAAS;MACtBuB,KAAK,CAACzD,QAAQ,GAAG,IAAI;IACvB;EACF,CAAC,MAAM;IACL,IAAI,CAAC2D,GAAG,GAAGhG,MAAM,CAAC8F,KAAK,CAAC;EAC1B;EAEA,IAAI,CAACG,WAAW,GAAGF,OAAO,CAACE,WAAW,IAAI,IAAI,CAACA,WAAW,IAAI,aAAa;EAC3E,IAAIF,OAAO,CAACjF,OAAO,IAAI,CAAC,IAAI,CAACA,OAAO,EAAE;IACpC,IAAI,CAACA,OAAO,GAAG,IAAID,OAAO,CAACkF,OAAO,CAACjF,OAAO,CAAC;EAC7C;EACA,IAAI,CAAC4E,MAAM,GAAGD,eAAe,CAACM,OAAO,CAACL,MAAM,IAAI,IAAI,CAACA,MAAM,IAAI,KAAK,CAAC;EACrE,IAAI,CAACQ,IAAI,GAAGH,OAAO,CAACG,IAAI,IAAI,IAAI,CAACA,IAAI,IAAI,IAAI;EAC7C,IAAI,CAACC,MAAM,GAAGJ,OAAO,CAACI,MAAM,IAAI,IAAI,CAACA,MAAM,IAAK,YAAY;IAC1D,IAAI,iBAAiB,IAAI/H,CAAC,EAAE;MAC1B,IAAIgI,IAAI,GAAG,IAAIC,eAAe,CAAC,CAAC;MAChC,OAAOD,IAAI,CAACD,MAAM;IACpB;EACF,CAAC,CAAC,CAAE;EACJ,IAAI,CAACG,QAAQ,GAAG,IAAI;EAEpB,IAAI,CAAC,IAAI,CAACZ,MAAM,KAAK,KAAK,IAAI,IAAI,CAACA,MAAM,KAAK,MAAM,KAAKvD,IAAI,EAAE;IAC7D,MAAM,IAAIjC,SAAS,CAAC,2CAA2C,CAAC;EAClE;EACA,IAAI,CAACoE,SAAS,CAACnC,IAAI,CAAC;EAEpB,IAAI,IAAI,CAACuD,MAAM,KAAK,KAAK,IAAI,IAAI,CAACA,MAAM,KAAK,MAAM,EAAE;IACnD,IAAIK,OAAO,CAACQ,KAAK,KAAK,UAAU,IAAIR,OAAO,CAACQ,KAAK,KAAK,UAAU,EAAE;MAChE;MACA,IAAIC,aAAa,GAAG,eAAe;MACnC,IAAIA,aAAa,CAACvG,IAAI,CAAC,IAAI,CAAC+F,GAAG,CAAC,EAAE;QAChC;QACA,IAAI,CAACA,GAAG,GAAG,IAAI,CAACA,GAAG,CAACS,OAAO,CAACD,aAAa,EAAE,MAAM,GAAG,IAAIE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;MAC3E,CAAC,MAAM;QACL;QACA,IAAIC,aAAa,GAAG,IAAI;QACxB,IAAI,CAACZ,GAAG,IAAI,CAACY,aAAa,CAAC3G,IAAI,CAAC,IAAI,CAAC+F,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAIU,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MACtF;IACF;EACF;AACF;AAEAd,OAAO,CAACzG,SAAS,CAACyH,KAAK,GAAG,YAAW;EACnC,OAAO,IAAIhB,OAAO,CAAC,IAAI,EAAE;IAAC1D,IAAI,EAAE,IAAI,CAACoC;EAAS,CAAC,CAAC;AAClD,CAAC;AAED,SAASa,MAAMA,CAACjD,IAAI,EAAE;EACpB,IAAI2E,IAAI,GAAG,IAAIpC,QAAQ,CAAC,CAAC;EACzBvC,IAAI,CACD4E,IAAI,CAAC,CAAC,CACNC,KAAK,CAAC,GAAG,CAAC,CACVhG,OAAO,CAAC,UAASiG,KAAK,EAAE;IACvB,IAAIA,KAAK,EAAE;MACT,IAAID,KAAK,GAAGC,KAAK,CAACD,KAAK,CAAC,GAAG,CAAC;MAC5B,IAAIjH,IAAI,GAAGiH,KAAK,CAACtG,KAAK,CAAC,CAAC,CAAC+F,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;MAC5C,IAAIpG,KAAK,GAAG2G,KAAK,CAAChD,IAAI,CAAC,GAAG,CAAC,CAACyC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;MAC/CK,IAAI,CAAC7F,MAAM,CAACiG,kBAAkB,CAACnH,IAAI,CAAC,EAAEmH,kBAAkB,CAAC7G,KAAK,CAAC,CAAC;IAClE;EACF,CAAC,CAAC;EACJ,OAAOyG,IAAI;AACb;AAEA,SAASK,YAAYA,CAACC,UAAU,EAAE;EAChC,IAAItG,OAAO,GAAG,IAAID,OAAO,CAAC,CAAC;EAC3B;EACA;EACA,IAAIwG,mBAAmB,GAAGD,UAAU,CAACX,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;EACjE;EACA;EACA;EACAY,mBAAmB,CAChBL,KAAK,CAAC,IAAI,CAAC,CACXjG,GAAG,CAAC,UAASK,MAAM,EAAE;IACpB,OAAOA,MAAM,CAAC1B,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG0B,MAAM,CAACkG,MAAM,CAAC,CAAC,EAAElG,MAAM,CAACC,MAAM,CAAC,GAAGD,MAAM;EAC9E,CAAC,CAAC,CACDJ,OAAO,CAAC,UAASuG,IAAI,EAAE;IACtB,IAAIC,KAAK,GAAGD,IAAI,CAACP,KAAK,CAAC,GAAG,CAAC;IAC3B,IAAIS,GAAG,GAAGD,KAAK,CAAC9G,KAAK,CAAC,CAAC,CAACqG,IAAI,CAAC,CAAC;IAC9B,IAAIU,GAAG,EAAE;MACP,IAAIpH,KAAK,GAAGmH,KAAK,CAACxD,IAAI,CAAC,GAAG,CAAC,CAAC+C,IAAI,CAAC,CAAC;MAClC,IAAI;QACFjG,OAAO,CAACG,MAAM,CAACwG,GAAG,EAAEpH,KAAK,CAAC;MAC5B,CAAC,CAAC,OAAOyC,KAAK,EAAE;QACd4E,OAAO,CAACC,IAAI,CAAC,WAAW,GAAG7E,KAAK,CAAC8E,OAAO,CAAC;MAC3C;IACF;EACF,CAAC,CAAC;EACJ,OAAO9G,OAAO;AAChB;AAEAuD,IAAI,CAACxE,IAAI,CAACgG,OAAO,CAACzG,SAAS,CAAC;AAE5B,OAAO,SAASyI,QAAQA,CAACC,QAAQ,EAAE/B,OAAO,EAAE;EAC1C,IAAI,EAAE,IAAI,YAAY8B,QAAQ,CAAC,EAAE;IAC/B,MAAM,IAAI3H,SAAS,CAAC,4FAA4F,CAAC;EACnH;EACA,IAAI,CAAC6F,OAAO,EAAE;IACZA,OAAO,GAAG,CAAC,CAAC;EACd;EAEA,IAAI,CAACzC,IAAI,GAAG,SAAS;EACrB,IAAI,CAACyE,MAAM,GAAGhC,OAAO,CAACgC,MAAM,KAAKnH,SAAS,GAAG,GAAG,GAAGmF,OAAO,CAACgC,MAAM;EACjE,IAAI,IAAI,CAACA,MAAM,GAAG,GAAG,IAAI,IAAI,CAACA,MAAM,GAAG,GAAG,EAAE;IAC1C,MAAM,IAAIC,UAAU,CAAC,0FAA0F,CAAC;EAClH;EACA,IAAI,CAACC,EAAE,GAAG,IAAI,CAACF,MAAM,IAAI,GAAG,IAAI,IAAI,CAACA,MAAM,GAAG,GAAG;EACjD,IAAI,CAACG,UAAU,GAAGnC,OAAO,CAACmC,UAAU,KAAKtH,SAAS,GAAG,EAAE,GAAG,EAAE,GAAGmF,OAAO,CAACmC,UAAU;EACjF,IAAI,CAACpH,OAAO,GAAG,IAAID,OAAO,CAACkF,OAAO,CAACjF,OAAO,CAAC;EAC3C,IAAI,CAACkF,GAAG,GAAGD,OAAO,CAACC,GAAG,IAAI,EAAE;EAC5B,IAAI,CAAC1B,SAAS,CAACwD,QAAQ,CAAC;AAC1B;AAEAzD,IAAI,CAACxE,IAAI,CAACgI,QAAQ,CAACzI,SAAS,CAAC;AAE7ByI,QAAQ,CAACzI,SAAS,CAACyH,KAAK,GAAG,YAAW;EACpC,OAAO,IAAIgB,QAAQ,CAAC,IAAI,CAACtD,SAAS,EAAE;IAClCwD,MAAM,EAAE,IAAI,CAACA,MAAM;IACnBG,UAAU,EAAE,IAAI,CAACA,UAAU;IAC3BpH,OAAO,EAAE,IAAID,OAAO,CAAC,IAAI,CAACC,OAAO,CAAC;IAClCkF,GAAG,EAAE,IAAI,CAACA;EACZ,CAAC,CAAC;AACJ,CAAC;AAED6B,QAAQ,CAAC/E,KAAK,GAAG,YAAW;EAC1B,IAAIqF,QAAQ,GAAG,IAAIN,QAAQ,CAAC,IAAI,EAAE;IAACE,MAAM,EAAE,GAAG;IAAEG,UAAU,EAAE;EAAE,CAAC,CAAC;EAChEC,QAAQ,CAACF,EAAE,GAAG,KAAK;EACnBE,QAAQ,CAACJ,MAAM,GAAG,CAAC;EACnBI,QAAQ,CAAC7E,IAAI,GAAG,OAAO;EACvB,OAAO6E,QAAQ;AACjB,CAAC;AAED,IAAIC,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAEhDP,QAAQ,CAACQ,QAAQ,GAAG,UAASrC,GAAG,EAAE+B,MAAM,EAAE;EACxC,IAAIK,gBAAgB,CAAC1I,OAAO,CAACqI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;IAC3C,MAAM,IAAIC,UAAU,CAAC,qBAAqB,CAAC;EAC7C;EAEA,OAAO,IAAIH,QAAQ,CAAC,IAAI,EAAE;IAACE,MAAM,EAAEA,MAAM;IAAEjH,OAAO,EAAE;MAACwH,QAAQ,EAAEtC;IAAG;EAAC,CAAC,CAAC;AACvE,CAAC;AAED,OAAO,IAAIuC,YAAY,GAAGnK,CAAC,CAACmK,YAAY;AACxC,IAAI;EACF,IAAIA,YAAY,CAAC,CAAC;AACpB,CAAC,CAAC,OAAOC,GAAG,EAAE;EACZD,YAAY,GAAG,SAAAA,CAASX,OAAO,EAAE7H,IAAI,EAAE;IACrC,IAAI,CAAC6H,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC7H,IAAI,GAAGA,IAAI;IAChB,IAAI+C,KAAK,GAAGiC,KAAK,CAAC6C,OAAO,CAAC;IAC1B,IAAI,CAACa,KAAK,GAAG3F,KAAK,CAAC2F,KAAK;EAC1B,CAAC;EACDF,YAAY,CAACnJ,SAAS,GAAGO,MAAM,CAAC+I,MAAM,CAAC3D,KAAK,CAAC3F,SAAS,CAAC;EACvDmJ,YAAY,CAACnJ,SAAS,CAACuJ,WAAW,GAAGJ,YAAY;AACnD;AAEA,OAAO,SAASK,KAAKA,CAAC9C,KAAK,EAAE+C,IAAI,EAAE;EACjC,OAAO,IAAIvG,OAAO,CAAC,UAASI,OAAO,EAAEH,MAAM,EAAE;IAC3C,IAAIuG,OAAO,GAAG,IAAIjD,OAAO,CAACC,KAAK,EAAE+C,IAAI,CAAC;IAEtC,IAAIC,OAAO,CAAC3C,MAAM,IAAI2C,OAAO,CAAC3C,MAAM,CAAC4C,OAAO,EAAE;MAC5C,OAAOxG,MAAM,CAAC,IAAIgG,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;IAC1D;IAEA,IAAIS,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;IAE9B,SAASC,QAAQA,CAAA,EAAG;MAClBF,GAAG,CAACG,KAAK,CAAC,CAAC;IACb;IAEAH,GAAG,CAACrG,MAAM,GAAG,YAAW;MACtB,IAAIoD,OAAO,GAAG;QACZmC,UAAU,EAAEc,GAAG,CAACd,UAAU;QAC1BpH,OAAO,EAAEqG,YAAY,CAAC6B,GAAG,CAACI,qBAAqB,CAAC,CAAC,IAAI,EAAE;MACzD,CAAC;MACD;MACA;MACA,IAAIN,OAAO,CAAC9C,GAAG,CAACtG,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,KAAKsJ,GAAG,CAACjB,MAAM,GAAG,GAAG,IAAIiB,GAAG,CAACjB,MAAM,GAAG,GAAG,CAAC,EAAE;QAClFhC,OAAO,CAACgC,MAAM,GAAG,GAAG;MACtB,CAAC,MAAM;QACLhC,OAAO,CAACgC,MAAM,GAAGiB,GAAG,CAACjB,MAAM;MAC7B;MACAhC,OAAO,CAACC,GAAG,GAAG,aAAa,IAAIgD,GAAG,GAAGA,GAAG,CAACK,WAAW,GAAGtD,OAAO,CAACjF,OAAO,CAACU,GAAG,CAAC,eAAe,CAAC;MAC3F,IAAIW,IAAI,GAAG,UAAU,IAAI6G,GAAG,GAAGA,GAAG,CAACb,QAAQ,GAAGa,GAAG,CAACM,YAAY;MAC9DC,UAAU,CAAC,YAAW;QACpB7G,OAAO,CAAC,IAAImF,QAAQ,CAAC1F,IAAI,EAAE4D,OAAO,CAAC,CAAC;MACtC,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;IAEDiD,GAAG,CAACnG,OAAO,GAAG,YAAW;MACvB0G,UAAU,CAAC,YAAW;QACpBhH,MAAM,CAAC,IAAIrC,SAAS,CAAC,wBAAwB,CAAC,CAAC;MACjD,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;IAED8I,GAAG,CAACQ,SAAS,GAAG,YAAW;MACzBD,UAAU,CAAC,YAAW;QACpBhH,MAAM,CAAC,IAAIrC,SAAS,CAAC,2BAA2B,CAAC,CAAC;MACpD,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;IAED8I,GAAG,CAACS,OAAO,GAAG,YAAW;MACvBF,UAAU,CAAC,YAAW;QACpBhH,MAAM,CAAC,IAAIgG,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;MACnD,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;IAED,SAASmB,MAAMA,CAAC1D,GAAG,EAAE;MACnB,IAAI;QACF,OAAOA,GAAG,KAAK,EAAE,IAAI5H,CAAC,CAACkK,QAAQ,CAACqB,IAAI,GAAGvL,CAAC,CAACkK,QAAQ,CAACqB,IAAI,GAAG3D,GAAG;MAC9D,CAAC,CAAC,OAAOlH,CAAC,EAAE;QACV,OAAOkH,GAAG;MACZ;IACF;IAEAgD,GAAG,CAACY,IAAI,CAACd,OAAO,CAACpD,MAAM,EAAEgE,MAAM,CAACZ,OAAO,CAAC9C,GAAG,CAAC,EAAE,IAAI,CAAC;IAEnD,IAAI8C,OAAO,CAAC7C,WAAW,KAAK,SAAS,EAAE;MACrC+C,GAAG,CAACa,eAAe,GAAG,IAAI;IAC5B,CAAC,MAAM,IAAIf,OAAO,CAAC7C,WAAW,KAAK,MAAM,EAAE;MACzC+C,GAAG,CAACa,eAAe,GAAG,KAAK;IAC7B;IAEA,IAAI,cAAc,IAAIb,GAAG,EAAE;MACzB,IAAIxK,OAAO,CAACI,IAAI,EAAE;QAChBoK,GAAG,CAACc,YAAY,GAAG,MAAM;MAC3B,CAAC,MAAM,IACLtL,OAAO,CAACQ,WAAW,EACnB;QACAgK,GAAG,CAACc,YAAY,GAAG,aAAa;MAClC;IACF;IAEA,IAAIjB,IAAI,IAAI,OAAOA,IAAI,CAAC/H,OAAO,KAAK,QAAQ,IAAI,EAAE+H,IAAI,CAAC/H,OAAO,YAAYD,OAAO,IAAKzC,CAAC,CAACyC,OAAO,IAAIgI,IAAI,CAAC/H,OAAO,YAAY1C,CAAC,CAACyC,OAAQ,CAAC,EAAE;MACtI,IAAIkJ,KAAK,GAAG,EAAE;MACdpK,MAAM,CAAC2B,mBAAmB,CAACuH,IAAI,CAAC/H,OAAO,CAAC,CAACE,OAAO,CAAC,UAASjB,IAAI,EAAE;QAC9DgK,KAAK,CAAChI,IAAI,CAACjC,aAAa,CAACC,IAAI,CAAC,CAAC;QAC/BiJ,GAAG,CAACgB,gBAAgB,CAACjK,IAAI,EAAEK,cAAc,CAACyI,IAAI,CAAC/H,OAAO,CAACf,IAAI,CAAC,CAAC,CAAC;MAChE,CAAC,CAAC;MACF+I,OAAO,CAAChI,OAAO,CAACE,OAAO,CAAC,UAASX,KAAK,EAAEN,IAAI,EAAE;QAC5C,IAAIgK,KAAK,CAACrK,OAAO,CAACK,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;UAC9BiJ,GAAG,CAACgB,gBAAgB,CAACjK,IAAI,EAAEM,KAAK,CAAC;QACnC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLyI,OAAO,CAAChI,OAAO,CAACE,OAAO,CAAC,UAASX,KAAK,EAAEN,IAAI,EAAE;QAC5CiJ,GAAG,CAACgB,gBAAgB,CAACjK,IAAI,EAAEM,KAAK,CAAC;MACnC,CAAC,CAAC;IACJ;IAEA,IAAIyI,OAAO,CAAC3C,MAAM,EAAE;MAClB2C,OAAO,CAAC3C,MAAM,CAAC8D,gBAAgB,CAAC,OAAO,EAAEf,QAAQ,CAAC;MAElDF,GAAG,CAACkB,kBAAkB,GAAG,YAAW;QAClC;QACA,IAAIlB,GAAG,CAACmB,UAAU,KAAK,CAAC,EAAE;UACxBrB,OAAO,CAAC3C,MAAM,CAACiE,mBAAmB,CAAC,OAAO,EAAElB,QAAQ,CAAC;QACvD;MACF,CAAC;IACH;IAEAF,GAAG,CAACqB,IAAI,CAAC,OAAOvB,OAAO,CAACvE,SAAS,KAAK,WAAW,GAAG,IAAI,GAAGuE,OAAO,CAACvE,SAAS,CAAC;EAC/E,CAAC,CAAC;AACJ;AAEAqE,KAAK,CAAC0B,QAAQ,GAAG,IAAI;AAErB,IAAI,CAAClM,CAAC,CAACwK,KAAK,EAAE;EACZxK,CAAC,CAACwK,KAAK,GAAGA,KAAK;EACfxK,CAAC,CAACyC,OAAO,GAAGA,OAAO;EACnBzC,CAAC,CAACyH,OAAO,GAAGA,OAAO;EACnBzH,CAAC,CAACyJ,QAAQ,GAAGA,QAAQ;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}