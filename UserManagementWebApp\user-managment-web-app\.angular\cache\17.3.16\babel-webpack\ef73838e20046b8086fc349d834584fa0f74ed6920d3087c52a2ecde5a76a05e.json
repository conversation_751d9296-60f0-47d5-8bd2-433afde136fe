{"ast": null, "code": "// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport { fromUtf8 as fromUtf8Browser } from \"@smithy/util-utf8\";\n// Quick polyfill\nvar fromUtf8 = typeof Buffer !== \"undefined\" && Buffer.from ? function (input) {\n  return Buffer.from(input, \"utf8\");\n} : fromUtf8Browser;\nexport function convertToBuffer(data) {\n  // Already a Uint8, do nothing\n  if (data instanceof Uint8Array) return data;\n  if (typeof data === \"string\") {\n    return fromUtf8(data);\n  }\n  if (ArrayBuffer.isView(data)) {\n    return new Uint8Array(data.buffer, data.byteOffset, data.byteLength / Uint8Array.BYTES_PER_ELEMENT);\n  }\n  return new Uint8Array(data);\n}", "map": {"version": 3, "names": ["fromUtf8", "fromUtf8Browser", "<PERSON><PERSON><PERSON>", "from", "input", "convertToBuffer", "data", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "byteOffset", "byteLength", "BYTES_PER_ELEMENT"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-crypto/util/build/module/convertToBuffer.js"], "sourcesContent": ["// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport { fromUtf8 as fromUtf8Browser } from \"@smithy/util-utf8\";\n// Quick polyfill\nvar fromUtf8 = typeof Buffer !== \"undefined\" && Buffer.from\n    ? function (input) { return Buffer.from(input, \"utf8\"); }\n    : fromUtf8Browser;\nexport function convertToBuffer(data) {\n    // Already a Uint8, do nothing\n    if (data instanceof Uint8Array)\n        return data;\n    if (typeof data === \"string\") {\n        return fromUtf8(data);\n    }\n    if (ArrayBuffer.isView(data)) {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength / Uint8Array.BYTES_PER_ELEMENT);\n    }\n    return new Uint8Array(data);\n}\n"], "mappings": "AAAA;AACA;AACA,SAASA,QAAQ,IAAIC,eAAe,QAAQ,mBAAmB;AAC/D;AACA,IAAID,QAAQ,GAAG,OAAOE,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,IAAI,GACrD,UAAUC,KAAK,EAAE;EAAE,OAAOF,MAAM,CAACC,IAAI,CAACC,KAAK,EAAE,MAAM,CAAC;AAAE,CAAC,GACvDH,eAAe;AACrB,OAAO,SAASI,eAAeA,CAACC,IAAI,EAAE;EAClC;EACA,IAAIA,IAAI,YAAYC,UAAU,EAC1B,OAAOD,IAAI;EACf,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAON,QAAQ,CAACM,IAAI,CAAC;EACzB;EACA,IAAIE,WAAW,CAACC,MAAM,CAACH,IAAI,CAAC,EAAE;IAC1B,OAAO,IAAIC,UAAU,CAACD,IAAI,CAACI,MAAM,EAAEJ,IAAI,CAACK,UAAU,EAAEL,IAAI,CAACM,UAAU,GAAGL,UAAU,CAACM,iBAAiB,CAAC;EACvG;EACA,OAAO,IAAIN,UAAU,CAACD,IAAI,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}