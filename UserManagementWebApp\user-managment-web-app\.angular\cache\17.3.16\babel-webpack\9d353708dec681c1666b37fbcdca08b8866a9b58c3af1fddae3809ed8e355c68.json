{"ast": null, "code": "import { isString, isObject, has } from './utils.mjs';\nconst classNames = (...args) => {\n  const classes = [];\n  for (const arg of args) {\n    // skip falsey values\n    if (!arg) {\n      continue;\n    }\n    if (isString(arg)) {\n      classes.push(arg);\n      continue;\n    }\n    if (typeof arg === 'number') {\n      classes.push(arg.toString());\n      continue;\n    }\n    if (Array.isArray(arg)) {\n      classes.push(classNames(...arg));\n      continue;\n    }\n    if (isObject(arg)) {\n      // check if the object has a valid .toString() method\n      if (arg.toString !== Object.prototype.toString && arg.toString() !== '[object Object]') {\n        classes.push(arg.toString());\n        continue;\n      }\n      for (const key in arg) {\n        if (has(arg, key) && arg[key]) {\n          classes.push(key);\n        }\n      }\n    }\n  }\n  return classes.join(' ');\n};\nexport { classNames };", "map": {"version": 3, "names": ["isString", "isObject", "has", "classNames", "args", "classes", "arg", "push", "toString", "Array", "isArray", "Object", "prototype", "key", "join"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/utils/classNames.mjs"], "sourcesContent": ["import { isString, isObject, has } from './utils.mjs';\n\nconst classNames = (...args) => {\n    const classes = [];\n    for (const arg of args) {\n        // skip falsey values\n        if (!arg) {\n            continue;\n        }\n        if (isString(arg)) {\n            classes.push(arg);\n            continue;\n        }\n        if (typeof arg === 'number') {\n            classes.push(arg.toString());\n            continue;\n        }\n        if (Array.isArray(arg)) {\n            classes.push(classNames(...arg));\n            continue;\n        }\n        if (isObject(arg)) {\n            // check if the object has a valid .toString() method\n            if (arg.toString !== Object.prototype.toString &&\n                arg.toString() !== '[object Object]') {\n                classes.push(arg.toString());\n                continue;\n            }\n            for (const key in arg) {\n                if (has(arg, key) && arg[key]) {\n                    classes.push(key);\n                }\n            }\n        }\n    }\n    return classes.join(' ');\n};\n\nexport { classNames };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,aAAa;AAErD,MAAMC,UAAU,GAAGA,CAAC,GAAGC,IAAI,KAAK;EAC5B,MAAMC,OAAO,GAAG,EAAE;EAClB,KAAK,MAAMC,GAAG,IAAIF,IAAI,EAAE;IACpB;IACA,IAAI,CAACE,GAAG,EAAE;MACN;IACJ;IACA,IAAIN,QAAQ,CAACM,GAAG,CAAC,EAAE;MACfD,OAAO,CAACE,IAAI,CAACD,GAAG,CAAC;MACjB;IACJ;IACA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACzBD,OAAO,CAACE,IAAI,CAACD,GAAG,CAACE,QAAQ,CAAC,CAAC,CAAC;MAC5B;IACJ;IACA,IAAIC,KAAK,CAACC,OAAO,CAACJ,GAAG,CAAC,EAAE;MACpBD,OAAO,CAACE,IAAI,CAACJ,UAAU,CAAC,GAAGG,GAAG,CAAC,CAAC;MAChC;IACJ;IACA,IAAIL,QAAQ,CAACK,GAAG,CAAC,EAAE;MACf;MACA,IAAIA,GAAG,CAACE,QAAQ,KAAKG,MAAM,CAACC,SAAS,CAACJ,QAAQ,IAC1CF,GAAG,CAACE,QAAQ,CAAC,CAAC,KAAK,iBAAiB,EAAE;QACtCH,OAAO,CAACE,IAAI,CAACD,GAAG,CAACE,QAAQ,CAAC,CAAC,CAAC;QAC5B;MACJ;MACA,KAAK,MAAMK,GAAG,IAAIP,GAAG,EAAE;QACnB,IAAIJ,GAAG,CAACI,GAAG,EAAEO,GAAG,CAAC,IAAIP,GAAG,CAACO,GAAG,CAAC,EAAE;UAC3BR,OAAO,CAACE,IAAI,CAACM,GAAG,CAAC;QACrB;MACJ;IACJ;EACJ;EACA,OAAOR,OAAO,CAACS,IAAI,CAAC,GAAG,CAAC;AAC5B,CAAC;AAED,SAASX,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}