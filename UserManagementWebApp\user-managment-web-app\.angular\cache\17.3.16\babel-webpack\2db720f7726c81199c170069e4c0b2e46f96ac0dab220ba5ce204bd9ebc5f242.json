{"ast": null, "code": "import { Amplify } from '@aws-amplify/core';\nimport { isB<PERSON><PERSON>, ADD_OAUTH_LISTENER } from '@aws-amplify/core/internals/utils';\nimport { attemptCompleteOAuthFlow } from './attemptCompleteOAuthFlow.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// attach the side effect for handling the completion of an inflight oauth flow\n// this side effect works only on Web\nisBrowser() && (() => {\n  // add the listener to the singleton for triggering\n  Amplify[ADD_OAUTH_LISTENER](attemptCompleteOAuthFlow);\n})();", "map": {"version": 3, "names": ["Amplify", "<PERSON><PERSON><PERSON><PERSON>", "ADD_OAUTH_LISTENER", "attemptCompleteOAuthFlow"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/oauth/enableOAuthListener.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { isB<PERSON><PERSON>, ADD_OAUTH_LISTENER } from '@aws-amplify/core/internals/utils';\nimport { attemptCompleteOAuthFlow } from './attemptCompleteOAuthFlow.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// attach the side effect for handling the completion of an inflight oauth flow\n// this side effect works only on Web\nisBrowser() &&\n    (() => {\n        // add the listener to the singleton for triggering\n        Amplify[ADD_OAUTH_LISTENER](attemptCompleteOAuthFlow);\n    })();\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,SAAS,EAAEC,kBAAkB,QAAQ,mCAAmC;AACjF,SAASC,wBAAwB,QAAQ,gCAAgC;;AAEzE;AACA;AACA;AACA;AACAF,SAAS,CAAC,CAAC,IACP,CAAC,MAAM;EACH;EACAD,OAAO,CAACE,kBAAkB,CAAC,CAACC,wBAAwB,CAAC;AACzD,CAAC,EAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}