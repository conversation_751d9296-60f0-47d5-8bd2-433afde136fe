{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\nimport { SearchPlaceIndexForPositionRequestFilterSensitiveLog, SearchPlaceIndexForPositionResponseFilterSensitiveLog } from \"../models/models_0\";\nimport { deserializeAws_restJson1SearchPlaceIndexForPositionCommand, serializeAws_restJson1SearchPlaceIndexForPositionCommand } from \"../protocols/Aws_restJson1\";\nvar SearchPlaceIndexForPositionCommand = function (_super) {\n  __extends(SearchPlaceIndexForPositionCommand, _super);\n  function SearchPlaceIndexForPositionCommand(input) {\n    var _this = _super.call(this) || this;\n    _this.input = input;\n    return _this;\n  }\n  SearchPlaceIndexForPositionCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n    this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n    var stack = clientStack.concat(this.middlewareStack);\n    var logger = configuration.logger;\n    var clientName = \"LocationClient\";\n    var commandName = \"SearchPlaceIndexForPositionCommand\";\n    var handlerExecutionContext = {\n      logger: logger,\n      clientName: clientName,\n      commandName: commandName,\n      inputFilterSensitiveLog: SearchPlaceIndexForPositionRequestFilterSensitiveLog,\n      outputFilterSensitiveLog: SearchPlaceIndexForPositionResponseFilterSensitiveLog\n    };\n    var requestHandler = configuration.requestHandler;\n    return stack.resolve(function (request) {\n      return requestHandler.handle(request.request, options || {});\n    }, handlerExecutionContext);\n  };\n  SearchPlaceIndexForPositionCommand.prototype.serialize = function (input, context) {\n    return serializeAws_restJson1SearchPlaceIndexForPositionCommand(input, context);\n  };\n  SearchPlaceIndexForPositionCommand.prototype.deserialize = function (output, context) {\n    return deserializeAws_restJson1SearchPlaceIndexForPositionCommand(output, context);\n  };\n  return SearchPlaceIndexForPositionCommand;\n}($Command);\nexport { SearchPlaceIndexForPositionCommand };", "map": {"version": 3, "names": ["__extends", "getSerdePlugin", "Command", "$Command", "SearchPlaceIndexForPositionRequestFilterSensitiveLog", "SearchPlaceIndexForPositionResponseFilterSensitiveLog", "deserializeAws_restJson1SearchPlaceIndexForPositionCommand", "serializeAws_restJson1SearchPlaceIndexForPositionCommand", "SearchPlaceIndexForPositionCommand", "_super", "input", "_this", "call", "prototype", "resolveMiddleware", "clientStack", "configuration", "options", "middlewareStack", "use", "serialize", "deserialize", "stack", "concat", "logger", "clientName", "commandName", "handlerExecutionContext", "inputFilterSensitiveLog", "outputFilterSensitiveLog", "requestHandler", "resolve", "request", "handle", "context", "output"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-location/dist-es/commands/SearchPlaceIndexForPositionCommand.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\nimport { SearchPlaceIndexForPositionRequestFilterSensitiveLog, SearchPlaceIndexForPositionResponseFilterSensitiveLog, } from \"../models/models_0\";\nimport { deserializeAws_restJson1SearchPlaceIndexForPositionCommand, serializeAws_restJson1SearchPlaceIndexForPositionCommand, } from \"../protocols/Aws_restJson1\";\nvar SearchPlaceIndexForPositionCommand = (function (_super) {\n    __extends(SearchPlaceIndexForPositionCommand, _super);\n    function SearchPlaceIndexForPositionCommand(input) {\n        var _this = _super.call(this) || this;\n        _this.input = input;\n        return _this;\n    }\n    SearchPlaceIndexForPositionCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n        this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n        var stack = clientStack.concat(this.middlewareStack);\n        var logger = configuration.logger;\n        var clientName = \"LocationClient\";\n        var commandName = \"SearchPlaceIndexForPositionCommand\";\n        var handlerExecutionContext = {\n            logger: logger,\n            clientName: clientName,\n            commandName: commandName,\n            inputFilterSensitiveLog: SearchPlaceIndexForPositionRequestFilterSensitiveLog,\n            outputFilterSensitiveLog: SearchPlaceIndexForPositionResponseFilterSensitiveLog,\n        };\n        var requestHandler = configuration.requestHandler;\n        return stack.resolve(function (request) {\n            return requestHandler.handle(request.request, options || {});\n        }, handlerExecutionContext);\n    };\n    SearchPlaceIndexForPositionCommand.prototype.serialize = function (input, context) {\n        return serializeAws_restJson1SearchPlaceIndexForPositionCommand(input, context);\n    };\n    SearchPlaceIndexForPositionCommand.prototype.deserialize = function (output, context) {\n        return deserializeAws_restJson1SearchPlaceIndexForPositionCommand(output, context);\n    };\n    return SearchPlaceIndexForPositionCommand;\n}($Command));\nexport { SearchPlaceIndexForPositionCommand };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,OAAO,IAAIC,QAAQ,QAAQ,wBAAwB;AAC5D,SAASC,oDAAoD,EAAEC,qDAAqD,QAAS,oBAAoB;AACjJ,SAASC,0DAA0D,EAAEC,wDAAwD,QAAS,4BAA4B;AAClK,IAAIC,kCAAkC,GAAI,UAAUC,MAAM,EAAE;EACxDT,SAAS,CAACQ,kCAAkC,EAAEC,MAAM,CAAC;EACrD,SAASD,kCAAkCA,CAACE,KAAK,EAAE;IAC/C,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACD,KAAK,GAAGA,KAAK;IACnB,OAAOC,KAAK;EAChB;EACAH,kCAAkC,CAACK,SAAS,CAACC,iBAAiB,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAEC,OAAO,EAAE;IAC5G,IAAI,CAACC,eAAe,CAACC,GAAG,CAAClB,cAAc,CAACe,aAAa,EAAE,IAAI,CAACI,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;IACzF,IAAIC,KAAK,GAAGP,WAAW,CAACQ,MAAM,CAAC,IAAI,CAACL,eAAe,CAAC;IACpD,IAAIM,MAAM,GAAGR,aAAa,CAACQ,MAAM;IACjC,IAAIC,UAAU,GAAG,gBAAgB;IACjC,IAAIC,WAAW,GAAG,oCAAoC;IACtD,IAAIC,uBAAuB,GAAG;MAC1BH,MAAM,EAAEA,MAAM;MACdC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA,WAAW;MACxBE,uBAAuB,EAAExB,oDAAoD;MAC7EyB,wBAAwB,EAAExB;IAC9B,CAAC;IACD,IAAIyB,cAAc,GAAGd,aAAa,CAACc,cAAc;IACjD,OAAOR,KAAK,CAACS,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpC,OAAOF,cAAc,CAACG,MAAM,CAACD,OAAO,CAACA,OAAO,EAAEf,OAAO,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC,EAAEU,uBAAuB,CAAC;EAC/B,CAAC;EACDnB,kCAAkC,CAACK,SAAS,CAACO,SAAS,GAAG,UAAUV,KAAK,EAAEwB,OAAO,EAAE;IAC/E,OAAO3B,wDAAwD,CAACG,KAAK,EAAEwB,OAAO,CAAC;EACnF,CAAC;EACD1B,kCAAkC,CAACK,SAAS,CAACQ,WAAW,GAAG,UAAUc,MAAM,EAAED,OAAO,EAAE;IAClF,OAAO5B,0DAA0D,CAAC6B,MAAM,EAAED,OAAO,CAAC;EACtF,CAAC;EACD,OAAO1B,kCAAkC;AAC7C,CAAC,CAACL,QAAQ,CAAE;AACZ,SAASK,kCAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}