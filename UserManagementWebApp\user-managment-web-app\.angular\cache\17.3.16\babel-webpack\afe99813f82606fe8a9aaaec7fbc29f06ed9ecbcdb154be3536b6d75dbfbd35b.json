{"ast": null, "code": "const time = {\n  short: {\n    value: '100ms'\n  },\n  medium: {\n    value: '250ms'\n  },\n  long: {\n    value: '500ms'\n  }\n};\nexport { time };", "map": {"version": 3, "names": ["time", "short", "value", "medium", "long"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/time.mjs"], "sourcesContent": ["const time = {\n    short: { value: '100ms' },\n    medium: { value: '250ms' },\n    long: { value: '500ms' },\n};\n\nexport { time };\n"], "mappings": "AAAA,MAAMA,IAAI,GAAG;EACTC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAQ,CAAC;EACzBC,MAAM,EAAE;IAAED,KAAK,EAAE;EAAQ,CAAC;EAC1BE,IAAI,EAAE;IAAEF,KAAK,EAAE;EAAQ;AAC3B,CAAC;AAED,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}