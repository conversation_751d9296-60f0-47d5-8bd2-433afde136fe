{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar AssociateSoftwareTokenException;\n(function (AssociateSoftwareTokenException) {\n  AssociateSoftwareTokenException[\"ConcurrentModificationException\"] = \"ConcurrentModificationException\";\n  AssociateSoftwareTokenException[\"ForbiddenException\"] = \"ForbiddenException\";\n  AssociateSoftwareTokenException[\"InternalErrorException\"] = \"InternalErrorException\";\n  AssociateSoftwareTokenException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  AssociateSoftwareTokenException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  AssociateSoftwareTokenException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  AssociateSoftwareTokenException[\"SoftwareTokenMFANotFoundException\"] = \"SoftwareTokenMFANotFoundException\";\n})(AssociateSoftwareTokenException || (AssociateSoftwareTokenException = {}));\nvar ChangePasswordException;\n(function (ChangePasswordException) {\n  ChangePasswordException[\"ForbiddenException\"] = \"ForbiddenException\";\n  ChangePasswordException[\"InternalErrorException\"] = \"InternalErrorException\";\n  ChangePasswordException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  ChangePasswordException[\"InvalidPasswordException\"] = \"InvalidPasswordException\";\n  ChangePasswordException[\"LimitExceededException\"] = \"LimitExceededException\";\n  ChangePasswordException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  ChangePasswordException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  ChangePasswordException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  ChangePasswordException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  ChangePasswordException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  ChangePasswordException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ChangePasswordException || (ChangePasswordException = {}));\nvar ConfirmDeviceException;\n(function (ConfirmDeviceException) {\n  ConfirmDeviceException[\"ForbiddenException\"] = \"ForbiddenException\";\n  ConfirmDeviceException[\"InternalErrorException\"] = \"InternalErrorException\";\n  ConfirmDeviceException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n  ConfirmDeviceException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  ConfirmDeviceException[\"InvalidPasswordException\"] = \"InvalidPasswordException\";\n  ConfirmDeviceException[\"InvalidUserPoolConfigurationException\"] = \"InvalidUserPoolConfigurationException\";\n  ConfirmDeviceException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  ConfirmDeviceException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  ConfirmDeviceException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  ConfirmDeviceException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  ConfirmDeviceException[\"UsernameExistsException\"] = \"UsernameExistsException\";\n  ConfirmDeviceException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  ConfirmDeviceException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ConfirmDeviceException || (ConfirmDeviceException = {}));\nvar ConfirmForgotPasswordException;\n(function (ConfirmForgotPasswordException) {\n  ConfirmForgotPasswordException[\"CodeMismatchException\"] = \"CodeMismatchException\";\n  ConfirmForgotPasswordException[\"ExpiredCodeException\"] = \"ExpiredCodeException\";\n  ConfirmForgotPasswordException[\"ForbiddenException\"] = \"ForbiddenException\";\n  ConfirmForgotPasswordException[\"InternalErrorException\"] = \"InternalErrorException\";\n  ConfirmForgotPasswordException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n  ConfirmForgotPasswordException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  ConfirmForgotPasswordException[\"InvalidPasswordException\"] = \"InvalidPasswordException\";\n  ConfirmForgotPasswordException[\"LimitExceededException\"] = \"LimitExceededException\";\n  ConfirmForgotPasswordException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  ConfirmForgotPasswordException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  ConfirmForgotPasswordException[\"TooManyFailedAttemptsException\"] = \"TooManyFailedAttemptsException\";\n  ConfirmForgotPasswordException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  ConfirmForgotPasswordException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n  ConfirmForgotPasswordException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n  ConfirmForgotPasswordException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  ConfirmForgotPasswordException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ConfirmForgotPasswordException || (ConfirmForgotPasswordException = {}));\nvar ConfirmSignUpException;\n(function (ConfirmSignUpException) {\n  ConfirmSignUpException[\"AliasExistsException\"] = \"AliasExistsException\";\n  ConfirmSignUpException[\"CodeMismatchException\"] = \"CodeMismatchException\";\n  ConfirmSignUpException[\"ExpiredCodeException\"] = \"ExpiredCodeException\";\n  ConfirmSignUpException[\"ForbiddenException\"] = \"ForbiddenException\";\n  ConfirmSignUpException[\"InternalErrorException\"] = \"InternalErrorException\";\n  ConfirmSignUpException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n  ConfirmSignUpException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  ConfirmSignUpException[\"LimitExceededException\"] = \"LimitExceededException\";\n  ConfirmSignUpException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  ConfirmSignUpException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  ConfirmSignUpException[\"TooManyFailedAttemptsException\"] = \"TooManyFailedAttemptsException\";\n  ConfirmSignUpException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  ConfirmSignUpException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n  ConfirmSignUpException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n  ConfirmSignUpException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ConfirmSignUpException || (ConfirmSignUpException = {}));\nvar DeleteUserAttributesException;\n(function (DeleteUserAttributesException) {\n  DeleteUserAttributesException[\"ForbiddenException\"] = \"ForbiddenException\";\n  DeleteUserAttributesException[\"InternalErrorException\"] = \"InternalErrorException\";\n  DeleteUserAttributesException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  DeleteUserAttributesException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  DeleteUserAttributesException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  DeleteUserAttributesException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  DeleteUserAttributesException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  DeleteUserAttributesException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  DeleteUserAttributesException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(DeleteUserAttributesException || (DeleteUserAttributesException = {}));\nvar DeleteUserException;\n(function (DeleteUserException) {\n  DeleteUserException[\"ForbiddenException\"] = \"ForbiddenException\";\n  DeleteUserException[\"InternalErrorException\"] = \"InternalErrorException\";\n  DeleteUserException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  DeleteUserException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  DeleteUserException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  DeleteUserException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  DeleteUserException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  DeleteUserException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  DeleteUserException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(DeleteUserException || (DeleteUserException = {}));\nvar ForgetDeviceException;\n(function (ForgetDeviceException) {\n  ForgetDeviceException[\"ForbiddenException\"] = \"ForbiddenException\";\n  ForgetDeviceException[\"InternalErrorException\"] = \"InternalErrorException\";\n  ForgetDeviceException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  ForgetDeviceException[\"InvalidUserPoolConfigurationException\"] = \"InvalidUserPoolConfigurationException\";\n  ForgetDeviceException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  ForgetDeviceException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  ForgetDeviceException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  ForgetDeviceException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  ForgetDeviceException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  ForgetDeviceException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ForgetDeviceException || (ForgetDeviceException = {}));\nvar ForgotPasswordException;\n(function (ForgotPasswordException) {\n  ForgotPasswordException[\"CodeDeliveryFailureException\"] = \"CodeDeliveryFailureException\";\n  ForgotPasswordException[\"ForbiddenException\"] = \"ForbiddenException\";\n  ForgotPasswordException[\"InternalErrorException\"] = \"InternalErrorException\";\n  ForgotPasswordException[\"InvalidEmailRoleAccessPolicyException\"] = \"InvalidEmailRoleAccessPolicyException\";\n  ForgotPasswordException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n  ForgotPasswordException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  ForgotPasswordException[\"InvalidSmsRoleAccessPolicyException\"] = \"InvalidSmsRoleAccessPolicyException\";\n  ForgotPasswordException[\"InvalidSmsRoleTrustRelationshipException\"] = \"InvalidSmsRoleTrustRelationshipException\";\n  ForgotPasswordException[\"LimitExceededException\"] = \"LimitExceededException\";\n  ForgotPasswordException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  ForgotPasswordException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  ForgotPasswordException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  ForgotPasswordException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n  ForgotPasswordException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n  ForgotPasswordException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ForgotPasswordException || (ForgotPasswordException = {}));\nvar GetUserException;\n(function (GetUserException) {\n  GetUserException[\"ForbiddenException\"] = \"ForbiddenException\";\n  GetUserException[\"InternalErrorException\"] = \"InternalErrorException\";\n  GetUserException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  GetUserException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  GetUserException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  GetUserException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  GetUserException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  GetUserException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  GetUserException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(GetUserException || (GetUserException = {}));\nvar GetIdException;\n(function (GetIdException) {\n  GetIdException[\"ExternalServiceException\"] = \"ExternalServiceException\";\n  GetIdException[\"InternalErrorException\"] = \"InternalErrorException\";\n  GetIdException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  GetIdException[\"LimitExceededException\"] = \"LimitExceededException\";\n  GetIdException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  GetIdException[\"ResourceConflictException\"] = \"ResourceConflictException\";\n  GetIdException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  GetIdException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n})(GetIdException || (GetIdException = {}));\nvar GetCredentialsForIdentityException;\n(function (GetCredentialsForIdentityException) {\n  GetCredentialsForIdentityException[\"ExternalServiceException\"] = \"ExternalServiceException\";\n  GetCredentialsForIdentityException[\"InternalErrorException\"] = \"InternalErrorException\";\n  GetCredentialsForIdentityException[\"InvalidIdentityPoolConfigurationException\"] = \"InvalidIdentityPoolConfigurationException\";\n  GetCredentialsForIdentityException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  GetCredentialsForIdentityException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  GetCredentialsForIdentityException[\"ResourceConflictException\"] = \"ResourceConflictException\";\n  GetCredentialsForIdentityException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  GetCredentialsForIdentityException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n})(GetCredentialsForIdentityException || (GetCredentialsForIdentityException = {}));\nvar GetUserAttributeVerificationException;\n(function (GetUserAttributeVerificationException) {\n  GetUserAttributeVerificationException[\"CodeDeliveryFailureException\"] = \"CodeDeliveryFailureException\";\n  GetUserAttributeVerificationException[\"ForbiddenException\"] = \"ForbiddenException\";\n  GetUserAttributeVerificationException[\"InternalErrorException\"] = \"InternalErrorException\";\n  GetUserAttributeVerificationException[\"InvalidEmailRoleAccessPolicyException\"] = \"InvalidEmailRoleAccessPolicyException\";\n  GetUserAttributeVerificationException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n  GetUserAttributeVerificationException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  GetUserAttributeVerificationException[\"InvalidSmsRoleAccessPolicyException\"] = \"InvalidSmsRoleAccessPolicyException\";\n  GetUserAttributeVerificationException[\"InvalidSmsRoleTrustRelationshipException\"] = \"InvalidSmsRoleTrustRelationshipException\";\n  GetUserAttributeVerificationException[\"LimitExceededException\"] = \"LimitExceededException\";\n  GetUserAttributeVerificationException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  GetUserAttributeVerificationException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  GetUserAttributeVerificationException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  GetUserAttributeVerificationException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  GetUserAttributeVerificationException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n  GetUserAttributeVerificationException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n  GetUserAttributeVerificationException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  GetUserAttributeVerificationException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(GetUserAttributeVerificationException || (GetUserAttributeVerificationException = {}));\nvar GlobalSignOutException;\n(function (GlobalSignOutException) {\n  GlobalSignOutException[\"ForbiddenException\"] = \"ForbiddenException\";\n  GlobalSignOutException[\"InternalErrorException\"] = \"InternalErrorException\";\n  GlobalSignOutException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  GlobalSignOutException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  GlobalSignOutException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  GlobalSignOutException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  GlobalSignOutException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  GlobalSignOutException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n})(GlobalSignOutException || (GlobalSignOutException = {}));\nvar InitiateAuthException;\n(function (InitiateAuthException) {\n  InitiateAuthException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  InitiateAuthException[\"ForbiddenException\"] = \"ForbiddenException\";\n  InitiateAuthException[\"InternalErrorException\"] = \"InternalErrorException\";\n  InitiateAuthException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n  InitiateAuthException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  InitiateAuthException[\"InvalidSmsRoleAccessPolicyException\"] = \"InvalidSmsRoleAccessPolicyException\";\n  InitiateAuthException[\"InvalidSmsRoleTrustRelationshipException\"] = \"InvalidSmsRoleTrustRelationshipException\";\n  InitiateAuthException[\"InvalidUserPoolConfigurationException\"] = \"InvalidUserPoolConfigurationException\";\n  InitiateAuthException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  InitiateAuthException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  InitiateAuthException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  InitiateAuthException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n  InitiateAuthException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n  InitiateAuthException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  InitiateAuthException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(InitiateAuthException || (InitiateAuthException = {}));\nvar ResendConfirmationException;\n(function (ResendConfirmationException) {\n  ResendConfirmationException[\"CodeDeliveryFailureException\"] = \"CodeDeliveryFailureException\";\n  ResendConfirmationException[\"ForbiddenException\"] = \"ForbiddenException\";\n  ResendConfirmationException[\"InternalErrorException\"] = \"InternalErrorException\";\n  ResendConfirmationException[\"InvalidEmailRoleAccessPolicyException\"] = \"InvalidEmailRoleAccessPolicyException\";\n  ResendConfirmationException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n  ResendConfirmationException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  ResendConfirmationException[\"InvalidSmsRoleAccessPolicyException\"] = \"InvalidSmsRoleAccessPolicyException\";\n  ResendConfirmationException[\"InvalidSmsRoleTrustRelationshipException\"] = \"InvalidSmsRoleTrustRelationshipException\";\n  ResendConfirmationException[\"LimitExceededException\"] = \"LimitExceededException\";\n  ResendConfirmationException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  ResendConfirmationException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  ResendConfirmationException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  ResendConfirmationException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n  ResendConfirmationException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n  ResendConfirmationException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ResendConfirmationException || (ResendConfirmationException = {}));\nvar RespondToAuthChallengeException;\n(function (RespondToAuthChallengeException) {\n  RespondToAuthChallengeException[\"AliasExistsException\"] = \"AliasExistsException\";\n  RespondToAuthChallengeException[\"CodeMismatchException\"] = \"CodeMismatchException\";\n  RespondToAuthChallengeException[\"ExpiredCodeException\"] = \"ExpiredCodeException\";\n  RespondToAuthChallengeException[\"ForbiddenException\"] = \"ForbiddenException\";\n  RespondToAuthChallengeException[\"InternalErrorException\"] = \"InternalErrorException\";\n  RespondToAuthChallengeException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n  RespondToAuthChallengeException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  RespondToAuthChallengeException[\"InvalidPasswordException\"] = \"InvalidPasswordException\";\n  RespondToAuthChallengeException[\"InvalidSmsRoleAccessPolicyException\"] = \"InvalidSmsRoleAccessPolicyException\";\n  RespondToAuthChallengeException[\"InvalidSmsRoleTrustRelationshipException\"] = \"InvalidSmsRoleTrustRelationshipException\";\n  RespondToAuthChallengeException[\"InvalidUserPoolConfigurationException\"] = \"InvalidUserPoolConfigurationException\";\n  RespondToAuthChallengeException[\"MFAMethodNotFoundException\"] = \"MFAMethodNotFoundException\";\n  RespondToAuthChallengeException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  RespondToAuthChallengeException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  RespondToAuthChallengeException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  RespondToAuthChallengeException[\"SoftwareTokenMFANotFoundException\"] = \"SoftwareTokenMFANotFoundException\";\n  RespondToAuthChallengeException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  RespondToAuthChallengeException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n  RespondToAuthChallengeException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n  RespondToAuthChallengeException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  RespondToAuthChallengeException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(RespondToAuthChallengeException || (RespondToAuthChallengeException = {}));\nvar SetUserMFAPreferenceException;\n(function (SetUserMFAPreferenceException) {\n  SetUserMFAPreferenceException[\"ForbiddenException\"] = \"ForbiddenException\";\n  SetUserMFAPreferenceException[\"InternalErrorException\"] = \"InternalErrorException\";\n  SetUserMFAPreferenceException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  SetUserMFAPreferenceException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  SetUserMFAPreferenceException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  SetUserMFAPreferenceException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  SetUserMFAPreferenceException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  SetUserMFAPreferenceException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(SetUserMFAPreferenceException || (SetUserMFAPreferenceException = {}));\nvar SignUpException;\n(function (SignUpException) {\n  SignUpException[\"CodeDeliveryFailureException\"] = \"CodeDeliveryFailureException\";\n  SignUpException[\"InternalErrorException\"] = \"InternalErrorException\";\n  SignUpException[\"InvalidEmailRoleAccessPolicyException\"] = \"InvalidEmailRoleAccessPolicyException\";\n  SignUpException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n  SignUpException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  SignUpException[\"InvalidPasswordException\"] = \"InvalidPasswordException\";\n  SignUpException[\"InvalidSmsRoleAccessPolicyException\"] = \"InvalidSmsRoleAccessPolicyException\";\n  SignUpException[\"InvalidSmsRoleTrustRelationshipException\"] = \"InvalidSmsRoleTrustRelationshipException\";\n  SignUpException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  SignUpException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  SignUpException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  SignUpException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n  SignUpException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n  SignUpException[\"UsernameExistsException\"] = \"UsernameExistsException\";\n})(SignUpException || (SignUpException = {}));\nvar UpdateUserAttributesException;\n(function (UpdateUserAttributesException) {\n  UpdateUserAttributesException[\"AliasExistsException\"] = \"AliasExistsException\";\n  UpdateUserAttributesException[\"CodeDeliveryFailureException\"] = \"CodeDeliveryFailureException\";\n  UpdateUserAttributesException[\"CodeMismatchException\"] = \"CodeMismatchException\";\n  UpdateUserAttributesException[\"ExpiredCodeException\"] = \"ExpiredCodeException\";\n  UpdateUserAttributesException[\"ForbiddenException\"] = \"ForbiddenException\";\n  UpdateUserAttributesException[\"InternalErrorException\"] = \"InternalErrorException\";\n  UpdateUserAttributesException[\"InvalidEmailRoleAccessPolicyException\"] = \"InvalidEmailRoleAccessPolicyException\";\n  UpdateUserAttributesException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n  UpdateUserAttributesException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  UpdateUserAttributesException[\"InvalidSmsRoleAccessPolicyException\"] = \"InvalidSmsRoleAccessPolicyException\";\n  UpdateUserAttributesException[\"InvalidSmsRoleTrustRelationshipException\"] = \"InvalidSmsRoleTrustRelationshipException\";\n  UpdateUserAttributesException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  UpdateUserAttributesException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  UpdateUserAttributesException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  UpdateUserAttributesException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  UpdateUserAttributesException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n  UpdateUserAttributesException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n  UpdateUserAttributesException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  UpdateUserAttributesException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(UpdateUserAttributesException || (UpdateUserAttributesException = {}));\nvar VerifySoftwareTokenException;\n(function (VerifySoftwareTokenException) {\n  VerifySoftwareTokenException[\"CodeMismatchException\"] = \"CodeMismatchException\";\n  VerifySoftwareTokenException[\"EnableSoftwareTokenMFAException\"] = \"EnableSoftwareTokenMFAException\";\n  VerifySoftwareTokenException[\"ForbiddenException\"] = \"ForbiddenException\";\n  VerifySoftwareTokenException[\"InternalErrorException\"] = \"InternalErrorException\";\n  VerifySoftwareTokenException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  VerifySoftwareTokenException[\"InvalidUserPoolConfigurationException\"] = \"InvalidUserPoolConfigurationException\";\n  VerifySoftwareTokenException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  VerifySoftwareTokenException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  VerifySoftwareTokenException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  VerifySoftwareTokenException[\"SoftwareTokenMFANotFoundException\"] = \"SoftwareTokenMFANotFoundException\";\n  VerifySoftwareTokenException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  VerifySoftwareTokenException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  VerifySoftwareTokenException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(VerifySoftwareTokenException || (VerifySoftwareTokenException = {}));\nvar VerifyUserAttributeException;\n(function (VerifyUserAttributeException) {\n  VerifyUserAttributeException[\"AliasExistsException\"] = \"AliasExistsException\";\n  VerifyUserAttributeException[\"CodeMismatchException\"] = \"CodeMismatchException\";\n  VerifyUserAttributeException[\"ExpiredCodeException\"] = \"ExpiredCodeException\";\n  VerifyUserAttributeException[\"ForbiddenException\"] = \"ForbiddenException\";\n  VerifyUserAttributeException[\"InternalErrorException\"] = \"InternalErrorException\";\n  VerifyUserAttributeException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  VerifyUserAttributeException[\"LimitExceededException\"] = \"LimitExceededException\";\n  VerifyUserAttributeException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  VerifyUserAttributeException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  VerifyUserAttributeException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  VerifyUserAttributeException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  VerifyUserAttributeException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  VerifyUserAttributeException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(VerifyUserAttributeException || (VerifyUserAttributeException = {}));\nvar UpdateDeviceStatusException;\n(function (UpdateDeviceStatusException) {\n  UpdateDeviceStatusException[\"ForbiddenException\"] = \"ForbiddenException\";\n  UpdateDeviceStatusException[\"InternalErrorException\"] = \"InternalErrorException\";\n  UpdateDeviceStatusException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  UpdateDeviceStatusException[\"InvalidUserPoolConfigurationException\"] = \"InvalidUserPoolConfigurationException\";\n  UpdateDeviceStatusException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  UpdateDeviceStatusException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  UpdateDeviceStatusException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  UpdateDeviceStatusException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  UpdateDeviceStatusException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  UpdateDeviceStatusException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(UpdateDeviceStatusException || (UpdateDeviceStatusException = {}));\nvar ListDevicesException;\n(function (ListDevicesException) {\n  ListDevicesException[\"ForbiddenException\"] = \"ForbiddenException\";\n  ListDevicesException[\"InternalErrorException\"] = \"InternalErrorException\";\n  ListDevicesException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n  ListDevicesException[\"InvalidUserPoolConfigurationException\"] = \"InvalidUserPoolConfigurationException\";\n  ListDevicesException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n  ListDevicesException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n  ListDevicesException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n  ListDevicesException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n  ListDevicesException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n  ListDevicesException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ListDevicesException || (ListDevicesException = {}));\nconst SETUP_TOTP_EXCEPTION = 'SetUpTOTPException';\nexport { AssociateSoftwareTokenException, ChangePasswordException, ConfirmDeviceException, ConfirmForgotPasswordException, ConfirmSignUpException, DeleteUserAttributesException, DeleteUserException, ForgetDeviceException, ForgotPasswordException, GetCredentialsForIdentityException, GetIdException, GetUserAttributeVerificationException, GetUserException, GlobalSignOutException, InitiateAuthException, ListDevicesException, ResendConfirmationException, RespondToAuthChallengeException, SETUP_TOTP_EXCEPTION, SetUserMFAPreferenceException, SignUpException, UpdateDeviceStatusException, UpdateUserAttributesException, VerifySoftwareTokenException, VerifyUserAttributeException };", "map": {"version": 3, "names": ["AssociateSoftwareTokenException", "ChangePasswordException", "ConfirmDeviceException", "ConfirmForgotPasswordException", "ConfirmSignUpException", "DeleteUserAttributesException", "DeleteUserException", "ForgetDeviceException", "ForgotPasswordException", "GetUserException", "GetIdException", "GetCredentialsForIdentityException", "GetUserAttributeVerificationException", "GlobalSignOutException", "InitiateAuthException", "ResendConfirmationException", "RespondToAuthChallengeException", "SetUserMFAPreferenceException", "SignUpException", "UpdateUserAttributesException", "VerifySoftwareTokenException", "VerifyUserAttributeException", "UpdateDeviceStatusException", "ListDevicesException", "SETUP_TOTP_EXCEPTION"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/types/errors.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar AssociateSoftwareTokenException;\n(function (AssociateSoftwareTokenException) {\n    AssociateSoftwareTokenException[\"ConcurrentModificationException\"] = \"ConcurrentModificationException\";\n    AssociateSoftwareTokenException[\"ForbiddenException\"] = \"ForbiddenException\";\n    AssociateSoftwareTokenException[\"InternalErrorException\"] = \"InternalErrorException\";\n    AssociateSoftwareTokenException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    AssociateSoftwareTokenException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    AssociateSoftwareTokenException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    AssociateSoftwareTokenException[\"SoftwareTokenMFANotFoundException\"] = \"SoftwareTokenMFANotFoundException\";\n})(AssociateSoftwareTokenException || (AssociateSoftwareTokenException = {}));\nvar ChangePasswordException;\n(function (ChangePasswordException) {\n    ChangePasswordException[\"ForbiddenException\"] = \"ForbiddenException\";\n    ChangePasswordException[\"InternalErrorException\"] = \"InternalErrorException\";\n    ChangePasswordException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    ChangePasswordException[\"InvalidPasswordException\"] = \"InvalidPasswordException\";\n    ChangePasswordException[\"LimitExceededException\"] = \"LimitExceededException\";\n    ChangePasswordException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    ChangePasswordException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    ChangePasswordException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    ChangePasswordException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    ChangePasswordException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    ChangePasswordException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ChangePasswordException || (ChangePasswordException = {}));\nvar ConfirmDeviceException;\n(function (ConfirmDeviceException) {\n    ConfirmDeviceException[\"ForbiddenException\"] = \"ForbiddenException\";\n    ConfirmDeviceException[\"InternalErrorException\"] = \"InternalErrorException\";\n    ConfirmDeviceException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n    ConfirmDeviceException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    ConfirmDeviceException[\"InvalidPasswordException\"] = \"InvalidPasswordException\";\n    ConfirmDeviceException[\"InvalidUserPoolConfigurationException\"] = \"InvalidUserPoolConfigurationException\";\n    ConfirmDeviceException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    ConfirmDeviceException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    ConfirmDeviceException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    ConfirmDeviceException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    ConfirmDeviceException[\"UsernameExistsException\"] = \"UsernameExistsException\";\n    ConfirmDeviceException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    ConfirmDeviceException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ConfirmDeviceException || (ConfirmDeviceException = {}));\nvar ConfirmForgotPasswordException;\n(function (ConfirmForgotPasswordException) {\n    ConfirmForgotPasswordException[\"CodeMismatchException\"] = \"CodeMismatchException\";\n    ConfirmForgotPasswordException[\"ExpiredCodeException\"] = \"ExpiredCodeException\";\n    ConfirmForgotPasswordException[\"ForbiddenException\"] = \"ForbiddenException\";\n    ConfirmForgotPasswordException[\"InternalErrorException\"] = \"InternalErrorException\";\n    ConfirmForgotPasswordException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n    ConfirmForgotPasswordException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    ConfirmForgotPasswordException[\"InvalidPasswordException\"] = \"InvalidPasswordException\";\n    ConfirmForgotPasswordException[\"LimitExceededException\"] = \"LimitExceededException\";\n    ConfirmForgotPasswordException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    ConfirmForgotPasswordException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    ConfirmForgotPasswordException[\"TooManyFailedAttemptsException\"] = \"TooManyFailedAttemptsException\";\n    ConfirmForgotPasswordException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    ConfirmForgotPasswordException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n    ConfirmForgotPasswordException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n    ConfirmForgotPasswordException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    ConfirmForgotPasswordException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ConfirmForgotPasswordException || (ConfirmForgotPasswordException = {}));\nvar ConfirmSignUpException;\n(function (ConfirmSignUpException) {\n    ConfirmSignUpException[\"AliasExistsException\"] = \"AliasExistsException\";\n    ConfirmSignUpException[\"CodeMismatchException\"] = \"CodeMismatchException\";\n    ConfirmSignUpException[\"ExpiredCodeException\"] = \"ExpiredCodeException\";\n    ConfirmSignUpException[\"ForbiddenException\"] = \"ForbiddenException\";\n    ConfirmSignUpException[\"InternalErrorException\"] = \"InternalErrorException\";\n    ConfirmSignUpException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n    ConfirmSignUpException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    ConfirmSignUpException[\"LimitExceededException\"] = \"LimitExceededException\";\n    ConfirmSignUpException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    ConfirmSignUpException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    ConfirmSignUpException[\"TooManyFailedAttemptsException\"] = \"TooManyFailedAttemptsException\";\n    ConfirmSignUpException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    ConfirmSignUpException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n    ConfirmSignUpException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n    ConfirmSignUpException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ConfirmSignUpException || (ConfirmSignUpException = {}));\nvar DeleteUserAttributesException;\n(function (DeleteUserAttributesException) {\n    DeleteUserAttributesException[\"ForbiddenException\"] = \"ForbiddenException\";\n    DeleteUserAttributesException[\"InternalErrorException\"] = \"InternalErrorException\";\n    DeleteUserAttributesException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    DeleteUserAttributesException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    DeleteUserAttributesException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    DeleteUserAttributesException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    DeleteUserAttributesException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    DeleteUserAttributesException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    DeleteUserAttributesException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(DeleteUserAttributesException || (DeleteUserAttributesException = {}));\nvar DeleteUserException;\n(function (DeleteUserException) {\n    DeleteUserException[\"ForbiddenException\"] = \"ForbiddenException\";\n    DeleteUserException[\"InternalErrorException\"] = \"InternalErrorException\";\n    DeleteUserException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    DeleteUserException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    DeleteUserException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    DeleteUserException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    DeleteUserException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    DeleteUserException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    DeleteUserException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(DeleteUserException || (DeleteUserException = {}));\nvar ForgetDeviceException;\n(function (ForgetDeviceException) {\n    ForgetDeviceException[\"ForbiddenException\"] = \"ForbiddenException\";\n    ForgetDeviceException[\"InternalErrorException\"] = \"InternalErrorException\";\n    ForgetDeviceException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    ForgetDeviceException[\"InvalidUserPoolConfigurationException\"] = \"InvalidUserPoolConfigurationException\";\n    ForgetDeviceException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    ForgetDeviceException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    ForgetDeviceException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    ForgetDeviceException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    ForgetDeviceException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    ForgetDeviceException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ForgetDeviceException || (ForgetDeviceException = {}));\nvar ForgotPasswordException;\n(function (ForgotPasswordException) {\n    ForgotPasswordException[\"CodeDeliveryFailureException\"] = \"CodeDeliveryFailureException\";\n    ForgotPasswordException[\"ForbiddenException\"] = \"ForbiddenException\";\n    ForgotPasswordException[\"InternalErrorException\"] = \"InternalErrorException\";\n    ForgotPasswordException[\"InvalidEmailRoleAccessPolicyException\"] = \"InvalidEmailRoleAccessPolicyException\";\n    ForgotPasswordException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n    ForgotPasswordException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    ForgotPasswordException[\"InvalidSmsRoleAccessPolicyException\"] = \"InvalidSmsRoleAccessPolicyException\";\n    ForgotPasswordException[\"InvalidSmsRoleTrustRelationshipException\"] = \"InvalidSmsRoleTrustRelationshipException\";\n    ForgotPasswordException[\"LimitExceededException\"] = \"LimitExceededException\";\n    ForgotPasswordException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    ForgotPasswordException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    ForgotPasswordException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    ForgotPasswordException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n    ForgotPasswordException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n    ForgotPasswordException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ForgotPasswordException || (ForgotPasswordException = {}));\nvar GetUserException;\n(function (GetUserException) {\n    GetUserException[\"ForbiddenException\"] = \"ForbiddenException\";\n    GetUserException[\"InternalErrorException\"] = \"InternalErrorException\";\n    GetUserException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    GetUserException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    GetUserException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    GetUserException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    GetUserException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    GetUserException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    GetUserException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(GetUserException || (GetUserException = {}));\nvar GetIdException;\n(function (GetIdException) {\n    GetIdException[\"ExternalServiceException\"] = \"ExternalServiceException\";\n    GetIdException[\"InternalErrorException\"] = \"InternalErrorException\";\n    GetIdException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    GetIdException[\"LimitExceededException\"] = \"LimitExceededException\";\n    GetIdException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    GetIdException[\"ResourceConflictException\"] = \"ResourceConflictException\";\n    GetIdException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    GetIdException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n})(GetIdException || (GetIdException = {}));\nvar GetCredentialsForIdentityException;\n(function (GetCredentialsForIdentityException) {\n    GetCredentialsForIdentityException[\"ExternalServiceException\"] = \"ExternalServiceException\";\n    GetCredentialsForIdentityException[\"InternalErrorException\"] = \"InternalErrorException\";\n    GetCredentialsForIdentityException[\"InvalidIdentityPoolConfigurationException\"] = \"InvalidIdentityPoolConfigurationException\";\n    GetCredentialsForIdentityException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    GetCredentialsForIdentityException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    GetCredentialsForIdentityException[\"ResourceConflictException\"] = \"ResourceConflictException\";\n    GetCredentialsForIdentityException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    GetCredentialsForIdentityException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n})(GetCredentialsForIdentityException || (GetCredentialsForIdentityException = {}));\nvar GetUserAttributeVerificationException;\n(function (GetUserAttributeVerificationException) {\n    GetUserAttributeVerificationException[\"CodeDeliveryFailureException\"] = \"CodeDeliveryFailureException\";\n    GetUserAttributeVerificationException[\"ForbiddenException\"] = \"ForbiddenException\";\n    GetUserAttributeVerificationException[\"InternalErrorException\"] = \"InternalErrorException\";\n    GetUserAttributeVerificationException[\"InvalidEmailRoleAccessPolicyException\"] = \"InvalidEmailRoleAccessPolicyException\";\n    GetUserAttributeVerificationException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n    GetUserAttributeVerificationException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    GetUserAttributeVerificationException[\"InvalidSmsRoleAccessPolicyException\"] = \"InvalidSmsRoleAccessPolicyException\";\n    GetUserAttributeVerificationException[\"InvalidSmsRoleTrustRelationshipException\"] = \"InvalidSmsRoleTrustRelationshipException\";\n    GetUserAttributeVerificationException[\"LimitExceededException\"] = \"LimitExceededException\";\n    GetUserAttributeVerificationException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    GetUserAttributeVerificationException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    GetUserAttributeVerificationException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    GetUserAttributeVerificationException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    GetUserAttributeVerificationException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n    GetUserAttributeVerificationException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n    GetUserAttributeVerificationException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    GetUserAttributeVerificationException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(GetUserAttributeVerificationException || (GetUserAttributeVerificationException = {}));\nvar GlobalSignOutException;\n(function (GlobalSignOutException) {\n    GlobalSignOutException[\"ForbiddenException\"] = \"ForbiddenException\";\n    GlobalSignOutException[\"InternalErrorException\"] = \"InternalErrorException\";\n    GlobalSignOutException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    GlobalSignOutException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    GlobalSignOutException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    GlobalSignOutException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    GlobalSignOutException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    GlobalSignOutException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n})(GlobalSignOutException || (GlobalSignOutException = {}));\nvar InitiateAuthException;\n(function (InitiateAuthException) {\n    InitiateAuthException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    InitiateAuthException[\"ForbiddenException\"] = \"ForbiddenException\";\n    InitiateAuthException[\"InternalErrorException\"] = \"InternalErrorException\";\n    InitiateAuthException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n    InitiateAuthException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    InitiateAuthException[\"InvalidSmsRoleAccessPolicyException\"] = \"InvalidSmsRoleAccessPolicyException\";\n    InitiateAuthException[\"InvalidSmsRoleTrustRelationshipException\"] = \"InvalidSmsRoleTrustRelationshipException\";\n    InitiateAuthException[\"InvalidUserPoolConfigurationException\"] = \"InvalidUserPoolConfigurationException\";\n    InitiateAuthException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    InitiateAuthException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    InitiateAuthException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    InitiateAuthException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n    InitiateAuthException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n    InitiateAuthException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    InitiateAuthException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(InitiateAuthException || (InitiateAuthException = {}));\nvar ResendConfirmationException;\n(function (ResendConfirmationException) {\n    ResendConfirmationException[\"CodeDeliveryFailureException\"] = \"CodeDeliveryFailureException\";\n    ResendConfirmationException[\"ForbiddenException\"] = \"ForbiddenException\";\n    ResendConfirmationException[\"InternalErrorException\"] = \"InternalErrorException\";\n    ResendConfirmationException[\"InvalidEmailRoleAccessPolicyException\"] = \"InvalidEmailRoleAccessPolicyException\";\n    ResendConfirmationException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n    ResendConfirmationException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    ResendConfirmationException[\"InvalidSmsRoleAccessPolicyException\"] = \"InvalidSmsRoleAccessPolicyException\";\n    ResendConfirmationException[\"InvalidSmsRoleTrustRelationshipException\"] = \"InvalidSmsRoleTrustRelationshipException\";\n    ResendConfirmationException[\"LimitExceededException\"] = \"LimitExceededException\";\n    ResendConfirmationException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    ResendConfirmationException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    ResendConfirmationException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    ResendConfirmationException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n    ResendConfirmationException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n    ResendConfirmationException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ResendConfirmationException || (ResendConfirmationException = {}));\nvar RespondToAuthChallengeException;\n(function (RespondToAuthChallengeException) {\n    RespondToAuthChallengeException[\"AliasExistsException\"] = \"AliasExistsException\";\n    RespondToAuthChallengeException[\"CodeMismatchException\"] = \"CodeMismatchException\";\n    RespondToAuthChallengeException[\"ExpiredCodeException\"] = \"ExpiredCodeException\";\n    RespondToAuthChallengeException[\"ForbiddenException\"] = \"ForbiddenException\";\n    RespondToAuthChallengeException[\"InternalErrorException\"] = \"InternalErrorException\";\n    RespondToAuthChallengeException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n    RespondToAuthChallengeException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    RespondToAuthChallengeException[\"InvalidPasswordException\"] = \"InvalidPasswordException\";\n    RespondToAuthChallengeException[\"InvalidSmsRoleAccessPolicyException\"] = \"InvalidSmsRoleAccessPolicyException\";\n    RespondToAuthChallengeException[\"InvalidSmsRoleTrustRelationshipException\"] = \"InvalidSmsRoleTrustRelationshipException\";\n    RespondToAuthChallengeException[\"InvalidUserPoolConfigurationException\"] = \"InvalidUserPoolConfigurationException\";\n    RespondToAuthChallengeException[\"MFAMethodNotFoundException\"] = \"MFAMethodNotFoundException\";\n    RespondToAuthChallengeException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    RespondToAuthChallengeException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    RespondToAuthChallengeException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    RespondToAuthChallengeException[\"SoftwareTokenMFANotFoundException\"] = \"SoftwareTokenMFANotFoundException\";\n    RespondToAuthChallengeException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    RespondToAuthChallengeException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n    RespondToAuthChallengeException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n    RespondToAuthChallengeException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    RespondToAuthChallengeException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(RespondToAuthChallengeException || (RespondToAuthChallengeException = {}));\nvar SetUserMFAPreferenceException;\n(function (SetUserMFAPreferenceException) {\n    SetUserMFAPreferenceException[\"ForbiddenException\"] = \"ForbiddenException\";\n    SetUserMFAPreferenceException[\"InternalErrorException\"] = \"InternalErrorException\";\n    SetUserMFAPreferenceException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    SetUserMFAPreferenceException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    SetUserMFAPreferenceException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    SetUserMFAPreferenceException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    SetUserMFAPreferenceException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    SetUserMFAPreferenceException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(SetUserMFAPreferenceException || (SetUserMFAPreferenceException = {}));\nvar SignUpException;\n(function (SignUpException) {\n    SignUpException[\"CodeDeliveryFailureException\"] = \"CodeDeliveryFailureException\";\n    SignUpException[\"InternalErrorException\"] = \"InternalErrorException\";\n    SignUpException[\"InvalidEmailRoleAccessPolicyException\"] = \"InvalidEmailRoleAccessPolicyException\";\n    SignUpException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n    SignUpException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    SignUpException[\"InvalidPasswordException\"] = \"InvalidPasswordException\";\n    SignUpException[\"InvalidSmsRoleAccessPolicyException\"] = \"InvalidSmsRoleAccessPolicyException\";\n    SignUpException[\"InvalidSmsRoleTrustRelationshipException\"] = \"InvalidSmsRoleTrustRelationshipException\";\n    SignUpException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    SignUpException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    SignUpException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    SignUpException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n    SignUpException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n    SignUpException[\"UsernameExistsException\"] = \"UsernameExistsException\";\n})(SignUpException || (SignUpException = {}));\nvar UpdateUserAttributesException;\n(function (UpdateUserAttributesException) {\n    UpdateUserAttributesException[\"AliasExistsException\"] = \"AliasExistsException\";\n    UpdateUserAttributesException[\"CodeDeliveryFailureException\"] = \"CodeDeliveryFailureException\";\n    UpdateUserAttributesException[\"CodeMismatchException\"] = \"CodeMismatchException\";\n    UpdateUserAttributesException[\"ExpiredCodeException\"] = \"ExpiredCodeException\";\n    UpdateUserAttributesException[\"ForbiddenException\"] = \"ForbiddenException\";\n    UpdateUserAttributesException[\"InternalErrorException\"] = \"InternalErrorException\";\n    UpdateUserAttributesException[\"InvalidEmailRoleAccessPolicyException\"] = \"InvalidEmailRoleAccessPolicyException\";\n    UpdateUserAttributesException[\"InvalidLambdaResponseException\"] = \"InvalidLambdaResponseException\";\n    UpdateUserAttributesException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    UpdateUserAttributesException[\"InvalidSmsRoleAccessPolicyException\"] = \"InvalidSmsRoleAccessPolicyException\";\n    UpdateUserAttributesException[\"InvalidSmsRoleTrustRelationshipException\"] = \"InvalidSmsRoleTrustRelationshipException\";\n    UpdateUserAttributesException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    UpdateUserAttributesException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    UpdateUserAttributesException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    UpdateUserAttributesException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    UpdateUserAttributesException[\"UnexpectedLambdaException\"] = \"UnexpectedLambdaException\";\n    UpdateUserAttributesException[\"UserLambdaValidationException\"] = \"UserLambdaValidationException\";\n    UpdateUserAttributesException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    UpdateUserAttributesException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(UpdateUserAttributesException || (UpdateUserAttributesException = {}));\nvar VerifySoftwareTokenException;\n(function (VerifySoftwareTokenException) {\n    VerifySoftwareTokenException[\"CodeMismatchException\"] = \"CodeMismatchException\";\n    VerifySoftwareTokenException[\"EnableSoftwareTokenMFAException\"] = \"EnableSoftwareTokenMFAException\";\n    VerifySoftwareTokenException[\"ForbiddenException\"] = \"ForbiddenException\";\n    VerifySoftwareTokenException[\"InternalErrorException\"] = \"InternalErrorException\";\n    VerifySoftwareTokenException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    VerifySoftwareTokenException[\"InvalidUserPoolConfigurationException\"] = \"InvalidUserPoolConfigurationException\";\n    VerifySoftwareTokenException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    VerifySoftwareTokenException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    VerifySoftwareTokenException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    VerifySoftwareTokenException[\"SoftwareTokenMFANotFoundException\"] = \"SoftwareTokenMFANotFoundException\";\n    VerifySoftwareTokenException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    VerifySoftwareTokenException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    VerifySoftwareTokenException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(VerifySoftwareTokenException || (VerifySoftwareTokenException = {}));\nvar VerifyUserAttributeException;\n(function (VerifyUserAttributeException) {\n    VerifyUserAttributeException[\"AliasExistsException\"] = \"AliasExistsException\";\n    VerifyUserAttributeException[\"CodeMismatchException\"] = \"CodeMismatchException\";\n    VerifyUserAttributeException[\"ExpiredCodeException\"] = \"ExpiredCodeException\";\n    VerifyUserAttributeException[\"ForbiddenException\"] = \"ForbiddenException\";\n    VerifyUserAttributeException[\"InternalErrorException\"] = \"InternalErrorException\";\n    VerifyUserAttributeException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    VerifyUserAttributeException[\"LimitExceededException\"] = \"LimitExceededException\";\n    VerifyUserAttributeException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    VerifyUserAttributeException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    VerifyUserAttributeException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    VerifyUserAttributeException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    VerifyUserAttributeException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    VerifyUserAttributeException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(VerifyUserAttributeException || (VerifyUserAttributeException = {}));\nvar UpdateDeviceStatusException;\n(function (UpdateDeviceStatusException) {\n    UpdateDeviceStatusException[\"ForbiddenException\"] = \"ForbiddenException\";\n    UpdateDeviceStatusException[\"InternalErrorException\"] = \"InternalErrorException\";\n    UpdateDeviceStatusException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    UpdateDeviceStatusException[\"InvalidUserPoolConfigurationException\"] = \"InvalidUserPoolConfigurationException\";\n    UpdateDeviceStatusException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    UpdateDeviceStatusException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    UpdateDeviceStatusException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    UpdateDeviceStatusException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    UpdateDeviceStatusException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    UpdateDeviceStatusException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(UpdateDeviceStatusException || (UpdateDeviceStatusException = {}));\nvar ListDevicesException;\n(function (ListDevicesException) {\n    ListDevicesException[\"ForbiddenException\"] = \"ForbiddenException\";\n    ListDevicesException[\"InternalErrorException\"] = \"InternalErrorException\";\n    ListDevicesException[\"InvalidParameterException\"] = \"InvalidParameterException\";\n    ListDevicesException[\"InvalidUserPoolConfigurationException\"] = \"InvalidUserPoolConfigurationException\";\n    ListDevicesException[\"NotAuthorizedException\"] = \"NotAuthorizedException\";\n    ListDevicesException[\"PasswordResetRequiredException\"] = \"PasswordResetRequiredException\";\n    ListDevicesException[\"ResourceNotFoundException\"] = \"ResourceNotFoundException\";\n    ListDevicesException[\"TooManyRequestsException\"] = \"TooManyRequestsException\";\n    ListDevicesException[\"UserNotConfirmedException\"] = \"UserNotConfirmedException\";\n    ListDevicesException[\"UserNotFoundException\"] = \"UserNotFoundException\";\n})(ListDevicesException || (ListDevicesException = {}));\nconst SETUP_TOTP_EXCEPTION = 'SetUpTOTPException';\n\nexport { AssociateSoftwareTokenException, ChangePasswordException, ConfirmDeviceException, ConfirmForgotPasswordException, ConfirmSignUpException, DeleteUserAttributesException, DeleteUserException, ForgetDeviceException, ForgotPasswordException, GetCredentialsForIdentityException, GetIdException, GetUserAttributeVerificationException, GetUserException, GlobalSignOutException, InitiateAuthException, ListDevicesException, ResendConfirmationException, RespondToAuthChallengeException, SETUP_TOTP_EXCEPTION, SetUserMFAPreferenceException, SignUpException, UpdateDeviceStatusException, UpdateUserAttributesException, VerifySoftwareTokenException, VerifyUserAttributeException };\n"], "mappings": "AAAA;AACA;AACA,IAAIA,+BAA+B;AACnC,CAAC,UAAUA,+BAA+B,EAAE;EACxCA,+BAA+B,CAAC,iCAAiC,CAAC,GAAG,iCAAiC;EACtGA,+BAA+B,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAC5EA,+BAA+B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACpFA,+BAA+B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC1FA,+BAA+B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACpFA,+BAA+B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC1FA,+BAA+B,CAAC,mCAAmC,CAAC,GAAG,mCAAmC;AAC9G,CAAC,EAAEA,+BAA+B,KAAKA,+BAA+B,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7E,IAAIC,uBAAuB;AAC3B,CAAC,UAAUA,uBAAuB,EAAE;EAChCA,uBAAuB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACpEA,uBAAuB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC5EA,uBAAuB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAClFA,uBAAuB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAChFA,uBAAuB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC5EA,uBAAuB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC5EA,uBAAuB,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAC5FA,uBAAuB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAClFA,uBAAuB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAChFA,uBAAuB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAClFA,uBAAuB,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AAC9E,CAAC,EAAEA,uBAAuB,KAAKA,uBAAuB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7D,IAAIC,sBAAsB;AAC1B,CAAC,UAAUA,sBAAsB,EAAE;EAC/BA,sBAAsB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACnEA,sBAAsB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC3EA,sBAAsB,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAC3FA,sBAAsB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACjFA,sBAAsB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAC/EA,sBAAsB,CAAC,uCAAuC,CAAC,GAAG,uCAAuC;EACzGA,sBAAsB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC3EA,sBAAsB,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAC3FA,sBAAsB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACjFA,sBAAsB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAC/EA,sBAAsB,CAAC,yBAAyB,CAAC,GAAG,yBAAyB;EAC7EA,sBAAsB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACjFA,sBAAsB,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AAC7E,CAAC,EAAEA,sBAAsB,KAAKA,sBAAsB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3D,IAAIC,8BAA8B;AAClC,CAAC,UAAUA,8BAA8B,EAAE;EACvCA,8BAA8B,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;EACjFA,8BAA8B,CAAC,sBAAsB,CAAC,GAAG,sBAAsB;EAC/EA,8BAA8B,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAC3EA,8BAA8B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACnFA,8BAA8B,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EACnGA,8BAA8B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACzFA,8BAA8B,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACvFA,8BAA8B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACnFA,8BAA8B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACnFA,8BAA8B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACzFA,8BAA8B,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EACnGA,8BAA8B,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACvFA,8BAA8B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACzFA,8BAA8B,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;EACjGA,8BAA8B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACzFA,8BAA8B,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AACrF,CAAC,EAAEA,8BAA8B,KAAKA,8BAA8B,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3E,IAAIC,sBAAsB;AAC1B,CAAC,UAAUA,sBAAsB,EAAE;EAC/BA,sBAAsB,CAAC,sBAAsB,CAAC,GAAG,sBAAsB;EACvEA,sBAAsB,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;EACzEA,sBAAsB,CAAC,sBAAsB,CAAC,GAAG,sBAAsB;EACvEA,sBAAsB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACnEA,sBAAsB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC3EA,sBAAsB,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAC3FA,sBAAsB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACjFA,sBAAsB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC3EA,sBAAsB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC3EA,sBAAsB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACjFA,sBAAsB,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAC3FA,sBAAsB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAC/EA,sBAAsB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACjFA,sBAAsB,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;EACzFA,sBAAsB,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AAC7E,CAAC,EAAEA,sBAAsB,KAAKA,sBAAsB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3D,IAAIC,6BAA6B;AACjC,CAAC,UAAUA,6BAA6B,EAAE;EACtCA,6BAA6B,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAC1EA,6BAA6B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAClFA,6BAA6B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACxFA,6BAA6B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAClFA,6BAA6B,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAClGA,6BAA6B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACxFA,6BAA6B,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACtFA,6BAA6B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACxFA,6BAA6B,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AACpF,CAAC,EAAEA,6BAA6B,KAAKA,6BAA6B,GAAG,CAAC,CAAC,CAAC,CAAC;AACzE,IAAIC,mBAAmB;AACvB,CAAC,UAAUA,mBAAmB,EAAE;EAC5BA,mBAAmB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAChEA,mBAAmB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACxEA,mBAAmB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC9EA,mBAAmB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACxEA,mBAAmB,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EACxFA,mBAAmB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC9EA,mBAAmB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAC5EA,mBAAmB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC9EA,mBAAmB,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AAC1E,CAAC,EAAEA,mBAAmB,KAAKA,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC;AACrD,IAAIC,qBAAqB;AACzB,CAAC,UAAUA,qBAAqB,EAAE;EAC9BA,qBAAqB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAClEA,qBAAqB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC1EA,qBAAqB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAChFA,qBAAqB,CAAC,uCAAuC,CAAC,GAAG,uCAAuC;EACxGA,qBAAqB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC1EA,qBAAqB,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAC1FA,qBAAqB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAChFA,qBAAqB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAC9EA,qBAAqB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAChFA,qBAAqB,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AAC5E,CAAC,EAAEA,qBAAqB,KAAKA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD,IAAIC,uBAAuB;AAC3B,CAAC,UAAUA,uBAAuB,EAAE;EAChCA,uBAAuB,CAAC,8BAA8B,CAAC,GAAG,8BAA8B;EACxFA,uBAAuB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACpEA,uBAAuB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC5EA,uBAAuB,CAAC,uCAAuC,CAAC,GAAG,uCAAuC;EAC1GA,uBAAuB,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAC5FA,uBAAuB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAClFA,uBAAuB,CAAC,qCAAqC,CAAC,GAAG,qCAAqC;EACtGA,uBAAuB,CAAC,0CAA0C,CAAC,GAAG,0CAA0C;EAChHA,uBAAuB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC5EA,uBAAuB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC5EA,uBAAuB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAClFA,uBAAuB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAChFA,uBAAuB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAClFA,uBAAuB,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;EAC1FA,uBAAuB,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AAC9E,CAAC,EAAEA,uBAAuB,KAAKA,uBAAuB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7D,IAAIC,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAC7DA,gBAAgB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACrEA,gBAAgB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC3EA,gBAAgB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACrEA,gBAAgB,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EACrFA,gBAAgB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC3EA,gBAAgB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACzEA,gBAAgB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC3EA,gBAAgB,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AACvE,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,IAAIC,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACvEA,cAAc,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACnEA,cAAc,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACzEA,cAAc,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACnEA,cAAc,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACnEA,cAAc,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACzEA,cAAc,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACzEA,cAAc,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;AAC3E,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,IAAIC,kCAAkC;AACtC,CAAC,UAAUA,kCAAkC,EAAE;EAC3CA,kCAAkC,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAC3FA,kCAAkC,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACvFA,kCAAkC,CAAC,2CAA2C,CAAC,GAAG,2CAA2C;EAC7HA,kCAAkC,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC7FA,kCAAkC,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACvFA,kCAAkC,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC7FA,kCAAkC,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC7FA,kCAAkC,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;AAC/F,CAAC,EAAEA,kCAAkC,KAAKA,kCAAkC,GAAG,CAAC,CAAC,CAAC,CAAC;AACnF,IAAIC,qCAAqC;AACzC,CAAC,UAAUA,qCAAqC,EAAE;EAC9CA,qCAAqC,CAAC,8BAA8B,CAAC,GAAG,8BAA8B;EACtGA,qCAAqC,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAClFA,qCAAqC,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC1FA,qCAAqC,CAAC,uCAAuC,CAAC,GAAG,uCAAuC;EACxHA,qCAAqC,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAC1GA,qCAAqC,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAChGA,qCAAqC,CAAC,qCAAqC,CAAC,GAAG,qCAAqC;EACpHA,qCAAqC,CAAC,0CAA0C,CAAC,GAAG,0CAA0C;EAC9HA,qCAAqC,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC1FA,qCAAqC,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC1FA,qCAAqC,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAC1GA,qCAAqC,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAChGA,qCAAqC,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAC9FA,qCAAqC,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAChGA,qCAAqC,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;EACxGA,qCAAqC,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAChGA,qCAAqC,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AAC5F,CAAC,EAAEA,qCAAqC,KAAKA,qCAAqC,GAAG,CAAC,CAAC,CAAC,CAAC;AACzF,IAAIC,sBAAsB;AAC1B,CAAC,UAAUA,sBAAsB,EAAE;EAC/BA,sBAAsB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACnEA,sBAAsB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC3EA,sBAAsB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACjFA,sBAAsB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC3EA,sBAAsB,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAC3FA,sBAAsB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACjFA,sBAAsB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAC/EA,sBAAsB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;AACrF,CAAC,EAAEA,sBAAsB,KAAKA,sBAAsB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3D,IAAIC,qBAAqB;AACzB,CAAC,UAAUA,qBAAqB,EAAE;EAC9BA,qBAAqB,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAC1FA,qBAAqB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAClEA,qBAAqB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC1EA,qBAAqB,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAC1FA,qBAAqB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAChFA,qBAAqB,CAAC,qCAAqC,CAAC,GAAG,qCAAqC;EACpGA,qBAAqB,CAAC,0CAA0C,CAAC,GAAG,0CAA0C;EAC9GA,qBAAqB,CAAC,uCAAuC,CAAC,GAAG,uCAAuC;EACxGA,qBAAqB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC1EA,qBAAqB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAChFA,qBAAqB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAC9EA,qBAAqB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAChFA,qBAAqB,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;EACxFA,qBAAqB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAChFA,qBAAqB,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AAC5E,CAAC,EAAEA,qBAAqB,KAAKA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AACzD,IAAIC,2BAA2B;AAC/B,CAAC,UAAUA,2BAA2B,EAAE;EACpCA,2BAA2B,CAAC,8BAA8B,CAAC,GAAG,8BAA8B;EAC5FA,2BAA2B,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACxEA,2BAA2B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAChFA,2BAA2B,CAAC,uCAAuC,CAAC,GAAG,uCAAuC;EAC9GA,2BAA2B,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAChGA,2BAA2B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACtFA,2BAA2B,CAAC,qCAAqC,CAAC,GAAG,qCAAqC;EAC1GA,2BAA2B,CAAC,0CAA0C,CAAC,GAAG,0CAA0C;EACpHA,2BAA2B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAChFA,2BAA2B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAChFA,2BAA2B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACtFA,2BAA2B,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACpFA,2BAA2B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACtFA,2BAA2B,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;EAC9FA,2BAA2B,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AAClF,CAAC,EAAEA,2BAA2B,KAAKA,2BAA2B,GAAG,CAAC,CAAC,CAAC,CAAC;AACrE,IAAIC,+BAA+B;AACnC,CAAC,UAAUA,+BAA+B,EAAE;EACxCA,+BAA+B,CAAC,sBAAsB,CAAC,GAAG,sBAAsB;EAChFA,+BAA+B,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;EAClFA,+BAA+B,CAAC,sBAAsB,CAAC,GAAG,sBAAsB;EAChFA,+BAA+B,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAC5EA,+BAA+B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACpFA,+BAA+B,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EACpGA,+BAA+B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC1FA,+BAA+B,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACxFA,+BAA+B,CAAC,qCAAqC,CAAC,GAAG,qCAAqC;EAC9GA,+BAA+B,CAAC,0CAA0C,CAAC,GAAG,0CAA0C;EACxHA,+BAA+B,CAAC,uCAAuC,CAAC,GAAG,uCAAuC;EAClHA,+BAA+B,CAAC,4BAA4B,CAAC,GAAG,4BAA4B;EAC5FA,+BAA+B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACpFA,+BAA+B,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EACpGA,+BAA+B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC1FA,+BAA+B,CAAC,mCAAmC,CAAC,GAAG,mCAAmC;EAC1GA,+BAA+B,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACxFA,+BAA+B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC1FA,+BAA+B,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;EAClGA,+BAA+B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC1FA,+BAA+B,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AACtF,CAAC,EAAEA,+BAA+B,KAAKA,+BAA+B,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7E,IAAIC,6BAA6B;AACjC,CAAC,UAAUA,6BAA6B,EAAE;EACtCA,6BAA6B,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAC1EA,6BAA6B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAClFA,6BAA6B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACxFA,6BAA6B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAClFA,6BAA6B,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAClGA,6BAA6B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACxFA,6BAA6B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACxFA,6BAA6B,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AACpF,CAAC,EAAEA,6BAA6B,KAAKA,6BAA6B,GAAG,CAAC,CAAC,CAAC,CAAC;AACzE,IAAIC,eAAe;AACnB,CAAC,UAAUA,eAAe,EAAE;EACxBA,eAAe,CAAC,8BAA8B,CAAC,GAAG,8BAA8B;EAChFA,eAAe,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACpEA,eAAe,CAAC,uCAAuC,CAAC,GAAG,uCAAuC;EAClGA,eAAe,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EACpFA,eAAe,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC1EA,eAAe,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACxEA,eAAe,CAAC,qCAAqC,CAAC,GAAG,qCAAqC;EAC9FA,eAAe,CAAC,0CAA0C,CAAC,GAAG,0CAA0C;EACxGA,eAAe,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACpEA,eAAe,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC1EA,eAAe,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACxEA,eAAe,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC1EA,eAAe,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;EAClFA,eAAe,CAAC,yBAAyB,CAAC,GAAG,yBAAyB;AAC1E,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C,IAAIC,6BAA6B;AACjC,CAAC,UAAUA,6BAA6B,EAAE;EACtCA,6BAA6B,CAAC,sBAAsB,CAAC,GAAG,sBAAsB;EAC9EA,6BAA6B,CAAC,8BAA8B,CAAC,GAAG,8BAA8B;EAC9FA,6BAA6B,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;EAChFA,6BAA6B,CAAC,sBAAsB,CAAC,GAAG,sBAAsB;EAC9EA,6BAA6B,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EAC1EA,6BAA6B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAClFA,6BAA6B,CAAC,uCAAuC,CAAC,GAAG,uCAAuC;EAChHA,6BAA6B,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAClGA,6BAA6B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACxFA,6BAA6B,CAAC,qCAAqC,CAAC,GAAG,qCAAqC;EAC5GA,6BAA6B,CAAC,0CAA0C,CAAC,GAAG,0CAA0C;EACtHA,6BAA6B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAClFA,6BAA6B,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAClGA,6BAA6B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACxFA,6BAA6B,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACtFA,6BAA6B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACxFA,6BAA6B,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;EAChGA,6BAA6B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACxFA,6BAA6B,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AACpF,CAAC,EAAEA,6BAA6B,KAAKA,6BAA6B,GAAG,CAAC,CAAC,CAAC,CAAC;AACzE,IAAIC,4BAA4B;AAChC,CAAC,UAAUA,4BAA4B,EAAE;EACrCA,4BAA4B,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;EAC/EA,4BAA4B,CAAC,iCAAiC,CAAC,GAAG,iCAAiC;EACnGA,4BAA4B,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACzEA,4BAA4B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACjFA,4BAA4B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACvFA,4BAA4B,CAAC,uCAAuC,CAAC,GAAG,uCAAuC;EAC/GA,4BAA4B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACjFA,4BAA4B,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EACjGA,4BAA4B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACvFA,4BAA4B,CAAC,mCAAmC,CAAC,GAAG,mCAAmC;EACvGA,4BAA4B,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACrFA,4BAA4B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACvFA,4BAA4B,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AACnF,CAAC,EAAEA,4BAA4B,KAAKA,4BAA4B,GAAG,CAAC,CAAC,CAAC,CAAC;AACvE,IAAIC,4BAA4B;AAChC,CAAC,UAAUA,4BAA4B,EAAE;EACrCA,4BAA4B,CAAC,sBAAsB,CAAC,GAAG,sBAAsB;EAC7EA,4BAA4B,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;EAC/EA,4BAA4B,CAAC,sBAAsB,CAAC,GAAG,sBAAsB;EAC7EA,4BAA4B,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACzEA,4BAA4B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACjFA,4BAA4B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACvFA,4BAA4B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACjFA,4BAA4B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACjFA,4BAA4B,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EACjGA,4BAA4B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACvFA,4BAA4B,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACrFA,4BAA4B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACvFA,4BAA4B,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AACnF,CAAC,EAAEA,4BAA4B,KAAKA,4BAA4B,GAAG,CAAC,CAAC,CAAC,CAAC;AACvE,IAAIC,2BAA2B;AAC/B,CAAC,UAAUA,2BAA2B,EAAE;EACpCA,2BAA2B,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACxEA,2BAA2B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAChFA,2BAA2B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACtFA,2BAA2B,CAAC,uCAAuC,CAAC,GAAG,uCAAuC;EAC9GA,2BAA2B,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAChFA,2BAA2B,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EAChGA,2BAA2B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACtFA,2BAA2B,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACpFA,2BAA2B,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACtFA,2BAA2B,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AAClF,CAAC,EAAEA,2BAA2B,KAAKA,2BAA2B,GAAG,CAAC,CAAC,CAAC,CAAC;AACrE,IAAIC,oBAAoB;AACxB,CAAC,UAAUA,oBAAoB,EAAE;EAC7BA,oBAAoB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACjEA,oBAAoB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACzEA,oBAAoB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC/EA,oBAAoB,CAAC,uCAAuC,CAAC,GAAG,uCAAuC;EACvGA,oBAAoB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACzEA,oBAAoB,CAAC,gCAAgC,CAAC,GAAG,gCAAgC;EACzFA,oBAAoB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC/EA,oBAAoB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAC7EA,oBAAoB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC/EA,oBAAoB,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;AAC3E,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD,MAAMC,oBAAoB,GAAG,oBAAoB;AAEjD,SAASxB,+BAA+B,EAAEC,uBAAuB,EAAEC,sBAAsB,EAAEC,8BAA8B,EAAEC,sBAAsB,EAAEC,6BAA6B,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,uBAAuB,EAAEG,kCAAkC,EAAED,cAAc,EAAEE,qCAAqC,EAAEH,gBAAgB,EAAEI,sBAAsB,EAAEC,qBAAqB,EAAES,oBAAoB,EAAER,2BAA2B,EAAEC,+BAA+B,EAAEQ,oBAAoB,EAAEP,6BAA6B,EAAEC,eAAe,EAAEI,2BAA2B,EAAEH,6BAA6B,EAAEC,4BAA4B,EAAEC,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}