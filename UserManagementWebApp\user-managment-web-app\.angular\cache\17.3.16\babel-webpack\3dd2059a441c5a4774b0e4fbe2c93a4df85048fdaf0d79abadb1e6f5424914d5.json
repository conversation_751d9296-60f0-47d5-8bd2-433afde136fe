{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertAuthTokens } from '../../providers/cognito/utils/types.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../common/AuthErrorStrings.mjs';\nimport '../../errors/types/validation.mjs';\nimport '../../providers/cognito/types/errors.mjs';\nimport { createDeleteWebAuthnCredentialClient } from '../factories/serviceClients/cognitoIdentityProvider/createDeleteWebAuthnCredentialClient.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction deleteWebAuthnCredential(_x, _x2) {\n  return _deleteWebAuthnCredential.apply(this, arguments);\n}\nfunction _deleteWebAuthnCredential() {\n  _deleteWebAuthnCredential = _asyncToGenerator(function* (amplify, input) {\n    const authConfig = amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolEndpoint,\n      userPoolId\n    } = authConfig;\n    const {\n      tokens\n    } = yield amplify.Auth.fetchAuthSession();\n    assertAuthTokens(tokens);\n    const deleteWebAuthnCredentialResult = createDeleteWebAuthnCredentialClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    yield deleteWebAuthnCredentialResult({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.DeleteWebAuthnCredential)\n    }, {\n      AccessToken: tokens.accessToken.toString(),\n      CredentialId: input.credentialId\n    });\n  });\n  return _deleteWebAuthnCredential.apply(this, arguments);\n}\nexport { deleteWebAuthnCredential };", "map": {"version": 3, "names": ["assertTokenProviderConfig", "AuthAction", "assertAuthTokens", "createCognitoUserPoolEndpointResolver", "getRegionFromUserPoolId", "getAuthUserAgentValue", "createDeleteWebAuthnCredentialClient", "deleteWebAuthnCredential", "_x", "_x2", "_deleteWebAuthnCredential", "apply", "arguments", "_asyncToGenerator", "amplify", "input", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userPoolEndpoint", "userPoolId", "tokens", "fetchAuthSession", "deleteWebAuthnCredentialResult", "endpointResolver", "endpointOverride", "region", "userAgentValue", "DeleteWebAuthnCredential", "AccessToken", "accessToken", "toString", "CredentialId", "credentialId"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/apis/deleteWebAuthnCredential.mjs"], "sourcesContent": ["import { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertAuthTokens } from '../../providers/cognito/utils/types.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../common/AuthErrorStrings.mjs';\nimport '../../errors/types/validation.mjs';\nimport '../../providers/cognito/types/errors.mjs';\nimport { createDeleteWebAuthnCredentialClient } from '../factories/serviceClients/cognitoIdentityProvider/createDeleteWebAuthnCredentialClient.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nasync function deleteWebAuthnCredential(amplify, input) {\n    const authConfig = amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolEndpoint, userPoolId } = authConfig;\n    const { tokens } = await amplify.Auth.fetchAuthSession();\n    assertAuthTokens(tokens);\n    const deleteWebAuthnCredentialResult = createDeleteWebAuthnCredentialClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    await deleteWebAuthnCredentialResult({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.DeleteWebAuthnCredential),\n    }, {\n        AccessToken: tokens.accessToken.toString(),\n        CredentialId: input.credentialId,\n    });\n}\n\nexport { deleteWebAuthnCredential };\n"], "mappings": ";AAAA,SAASA,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,SAASC,qCAAqC,QAAQ,6EAA6E;AACnI,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,uGAAuG;AAC9G,OAAO,mEAAmE;AAC1E,OAAO,mCAAmC;AAC1C,OAAO,mCAAmC;AAC1C,OAAO,0CAA0C;AACjD,SAASC,oCAAoC,QAAQ,8FAA8F;;AAEnJ;AACA;AAAA,SACeC,wBAAwBA,CAAAC,EAAA,EAAAC,GAAA;EAAA,OAAAC,yBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,0BAAA;EAAAA,yBAAA,GAAAG,iBAAA,CAAvC,WAAwCC,OAAO,EAAEC,KAAK,EAAE;IACpD,MAAMC,UAAU,GAAGF,OAAO,CAACG,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDnB,yBAAyB,CAACgB,UAAU,CAAC;IACrC,MAAM;MAAEI,gBAAgB;MAAEC;IAAW,CAAC,GAAGL,UAAU;IACnD,MAAM;MAAEM;IAAO,CAAC,SAASR,OAAO,CAACI,IAAI,CAACK,gBAAgB,CAAC,CAAC;IACxDrB,gBAAgB,CAACoB,MAAM,CAAC;IACxB,MAAME,8BAA8B,GAAGlB,oCAAoC,CAAC;MACxEmB,gBAAgB,EAAEtB,qCAAqC,CAAC;QACpDuB,gBAAgB,EAAEN;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMI,8BAA8B,CAAC;MACjCG,MAAM,EAAEvB,uBAAuB,CAACiB,UAAU,CAAC;MAC3CO,cAAc,EAAEvB,qBAAqB,CAACJ,UAAU,CAAC4B,wBAAwB;IAC7E,CAAC,EAAE;MACCC,WAAW,EAAER,MAAM,CAACS,WAAW,CAACC,QAAQ,CAAC,CAAC;MAC1CC,YAAY,EAAElB,KAAK,CAACmB;IACxB,CAAC,CAAC;EACN,CAAC;EAAA,OAAAxB,yBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASL,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}