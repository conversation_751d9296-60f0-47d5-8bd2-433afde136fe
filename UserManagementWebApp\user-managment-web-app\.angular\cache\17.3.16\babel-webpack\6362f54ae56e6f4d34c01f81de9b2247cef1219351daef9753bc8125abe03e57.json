{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// query params\nconst ALGORITHM_QUERY_PARAM = 'X-Amz-Algorithm';\nconst AMZ_DATE_QUERY_PARAM = 'X-Amz-Date';\nconst CREDENTIAL_QUERY_PARAM = 'X-Amz-Credential';\nconst EXPIRES_QUERY_PARAM = 'X-Amz-Expires';\nconst REGION_SET_PARAM = 'X-Amz-Region-Set';\nconst SIGNATURE_QUERY_PARAM = 'X-Amz-Signature';\nconst SIGNED_HEADERS_QUERY_PARAM = 'X-Amz-SignedHeaders';\nconst TOKEN_QUERY_PARAM = 'X-Amz-Security-Token';\n// headers\nconst AUTH_HEADER = 'authorization';\nconst HOST_HEADER = 'host';\nconst AMZ_DATE_HEADER = AMZ_DATE_QUERY_PARAM.toLowerCase();\nconst TOKEN_HEADER = TOKEN_QUERY_PARAM.toLowerCase();\n// identifiers\nconst KEY_TYPE_IDENTIFIER = 'aws4_request';\nconst SHA256_ALGORITHM_IDENTIFIER = 'AWS4-HMAC-SHA256';\nconst SIGNATURE_IDENTIFIER = 'AWS4';\n// preset values\nconst EMPTY_HASH = 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855';\nconst UNSIGNED_PAYLOAD = 'UNSIGNED-PAYLOAD';\nexport { ALGORITHM_QUERY_PARAM, AMZ_DATE_HEADER, AMZ_DATE_QUERY_PARAM, AUTH_HEADER, CREDENTIAL_QUERY_PARAM, EMPTY_HASH, EXPIRES_QUERY_PARAM, HOST_HEADER, KEY_TYPE_IDENTIFIER, REGION_SET_PARAM, SHA256_ALGORITHM_IDENTIFIER, SIGNATURE_IDENTIFIER, SIGNATURE_QUERY_PARAM, SIGNED_HEADERS_QUERY_PARAM, TOKEN_HEADER, TOKEN_QUERY_PARAM, UNSIGNED_PAYLOAD };", "map": {"version": 3, "names": ["ALGORITHM_QUERY_PARAM", "AMZ_DATE_QUERY_PARAM", "CREDENTIAL_QUERY_PARAM", "EXPIRES_QUERY_PARAM", "REGION_SET_PARAM", "SIGNATURE_QUERY_PARAM", "SIGNED_HEADERS_QUERY_PARAM", "TOKEN_QUERY_PARAM", "AUTH_HEADER", "HOST_HEADER", "AMZ_DATE_HEADER", "toLowerCase", "TOKEN_HEADER", "KEY_TYPE_IDENTIFIER", "SHA256_ALGORITHM_IDENTIFIER", "SIGNATURE_IDENTIFIER", "EMPTY_HASH", "UNSIGNED_PAYLOAD"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/signer/signatureV4/constants.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// query params\nconst ALGORITHM_QUERY_PARAM = 'X-Amz-Algorithm';\nconst AMZ_DATE_QUERY_PARAM = 'X-Amz-Date';\nconst CREDENTIAL_QUERY_PARAM = 'X-Amz-Credential';\nconst EXPIRES_QUERY_PARAM = 'X-Amz-Expires';\nconst REGION_SET_PARAM = 'X-Amz-Region-Set';\nconst SIGNATURE_QUERY_PARAM = 'X-Amz-Signature';\nconst SIGNED_HEADERS_QUERY_PARAM = 'X-Amz-SignedHeaders';\nconst TOKEN_QUERY_PARAM = 'X-Amz-Security-Token';\n// headers\nconst AUTH_HEADER = 'authorization';\nconst HOST_HEADER = 'host';\nconst AMZ_DATE_HEADER = AMZ_DATE_QUERY_PARAM.toLowerCase();\nconst TOKEN_HEADER = TOKEN_QUERY_PARAM.toLowerCase();\n// identifiers\nconst KEY_TYPE_IDENTIFIER = 'aws4_request';\nconst SHA256_ALGORITHM_IDENTIFIER = 'AWS4-HMAC-SHA256';\nconst SIGNATURE_IDENTIFIER = 'AWS4';\n// preset values\nconst EMPTY_HASH = 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855';\nconst UNSIGNED_PAYLOAD = 'UNSIGNED-PAYLOAD';\n\nexport { ALGORITHM_QUERY_PARAM, AMZ_DATE_HEADER, AMZ_DATE_QUERY_PARAM, AUTH_HEADER, CREDENTIAL_QUERY_PARAM, EMPTY_HASH, EXPIRES_QUERY_PARAM, HOST_HEADER, KEY_TYPE_IDENTIFIER, REGION_SET_PARAM, SHA256_ALGORITHM_IDENTIFIER, SIGNATURE_IDENTIFIER, SIGNATURE_QUERY_PARAM, SIGNED_HEADERS_QUERY_PARAM, TOKEN_HEADER, TOKEN_QUERY_PARAM, UNSIGNED_PAYLOAD };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,qBAAqB,GAAG,iBAAiB;AAC/C,MAAMC,oBAAoB,GAAG,YAAY;AACzC,MAAMC,sBAAsB,GAAG,kBAAkB;AACjD,MAAMC,mBAAmB,GAAG,eAAe;AAC3C,MAAMC,gBAAgB,GAAG,kBAAkB;AAC3C,MAAMC,qBAAqB,GAAG,iBAAiB;AAC/C,MAAMC,0BAA0B,GAAG,qBAAqB;AACxD,MAAMC,iBAAiB,GAAG,sBAAsB;AAChD;AACA,MAAMC,WAAW,GAAG,eAAe;AACnC,MAAMC,WAAW,GAAG,MAAM;AAC1B,MAAMC,eAAe,GAAGT,oBAAoB,CAACU,WAAW,CAAC,CAAC;AAC1D,MAAMC,YAAY,GAAGL,iBAAiB,CAACI,WAAW,CAAC,CAAC;AACpD;AACA,MAAME,mBAAmB,GAAG,cAAc;AAC1C,MAAMC,2BAA2B,GAAG,kBAAkB;AACtD,MAAMC,oBAAoB,GAAG,MAAM;AACnC;AACA,MAAMC,UAAU,GAAG,kEAAkE;AACrF,MAAMC,gBAAgB,GAAG,kBAAkB;AAE3C,SAASjB,qBAAqB,EAAEU,eAAe,EAAET,oBAAoB,EAAEO,WAAW,EAAEN,sBAAsB,EAAEc,UAAU,EAAEb,mBAAmB,EAAEM,WAAW,EAAEI,mBAAmB,EAAET,gBAAgB,EAAEU,2BAA2B,EAAEC,oBAAoB,EAAEV,qBAAqB,EAAEC,0BAA0B,EAAEM,YAAY,EAAEL,iBAAiB,EAAEU,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}