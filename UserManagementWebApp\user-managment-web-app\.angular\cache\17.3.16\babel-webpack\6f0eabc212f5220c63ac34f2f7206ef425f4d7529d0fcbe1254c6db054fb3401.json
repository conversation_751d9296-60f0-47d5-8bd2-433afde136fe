{"ast": null, "code": "import { SyncKeyValueStorage } from './SyncKeyValueStorage.mjs';\nimport { getSessionStorageWithFallback } from './utils.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nclass SyncSessionStorage extends SyncKeyValueStorage {\n  constructor() {\n    super(getSessionStorageWithFallback());\n  }\n}\nexport { SyncSessionStorage };", "map": {"version": 3, "names": ["SyncKeyValueStorage", "getSessionStorageWithFallback", "SyncSessionStorage", "constructor"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/storage/SyncSessionStorage.mjs"], "sourcesContent": ["import { SyncKeyValueStorage } from './SyncKeyValueStorage.mjs';\nimport { getSessionStorageWithFallback } from './utils.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nclass SyncSessionStorage extends SyncKeyValueStorage {\n    constructor() {\n        super(getSessionStorageWithFallback());\n    }\n}\n\nexport { SyncSessionStorage };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,6BAA6B,QAAQ,aAAa;;AAE3D;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,SAASF,mBAAmB,CAAC;EACjDG,WAAWA,CAAA,EAAG;IACV,KAAK,CAACF,6BAA6B,CAAC,CAAC,CAAC;EAC1C;AACJ;AAEA,SAASC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}