{"ast": null, "code": "import { Amplify, CookieStorage, defaultStorage } from '@aws-amplify/core';\nimport { parseAmplifyConfig } from '@aws-amplify/core/internals/utils';\nimport { CognitoAWSCredentialsAndIdentityIdProvider, DefaultIdentityIdStore, cognitoCredentialsProvider, cognitoUserPoolsTokenProvider } from '@aws-amplify/auth/cognito';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst DefaultAmplify = {\n  /**\n   * Configures Amplify with the {@link resourceConfig} and {@link libraryOptions}.\n   *\n   * @param resourceConfig The {@link ResourcesConfig} object that is typically imported from the\n   * `amplifyconfiguration.json` file. It can also be an object literal created inline when calling `Amplify.configure`.\n   * @param libraryOptions The {@link LibraryOptions} additional options for the library.\n   *\n   * @example\n   * import config from './amplifyconfiguration.json';\n   *\n   * Amplify.configure(config);\n   */\n  configure(resourceConfig, libraryOptions) {\n    const resolvedResourceConfig = parseAmplifyConfig(resourceConfig);\n    const cookieBasedKeyValueStorage = new CookieStorage({\n      sameSite: 'lax'\n    });\n    const resolvedKeyValueStorage = libraryOptions?.ssr ? cookieBasedKeyValueStorage : defaultStorage;\n    const resolvedCredentialsProvider = libraryOptions?.ssr ? new CognitoAWSCredentialsAndIdentityIdProvider(new DefaultIdentityIdStore(cookieBasedKeyValueStorage)) : cognitoCredentialsProvider;\n    // If no Auth config is provided, no special handling will be required, configure as is.\n    // Otherwise, we can assume an Auth config is provided from here on.\n    if (!resolvedResourceConfig.Auth) {\n      Amplify.configure(resolvedResourceConfig, libraryOptions);\n      return;\n    }\n    // If Auth options are provided, always just configure as is.\n    // Otherwise, we can assume no Auth libraryOptions were provided from here on.\n    if (libraryOptions?.Auth) {\n      Amplify.configure(resolvedResourceConfig, libraryOptions);\n      return;\n    }\n    // If no Auth libraryOptions were previously configured, then always add default providers.\n    if (!Amplify.libraryOptions.Auth) {\n      cognitoUserPoolsTokenProvider.setAuthConfig(resolvedResourceConfig.Auth);\n      cognitoUserPoolsTokenProvider.setKeyValueStorage(\n      // TODO: allow configure with a public interface\n      resolvedKeyValueStorage);\n      Amplify.configure(resolvedResourceConfig, {\n        ...libraryOptions,\n        Auth: {\n          tokenProvider: cognitoUserPoolsTokenProvider,\n          credentialsProvider: resolvedCredentialsProvider\n        }\n      });\n      return;\n    }\n    // At this point, Auth libraryOptions would have been previously configured and no overriding\n    // Auth options were given, so we should preserve the currently configured Auth libraryOptions.\n    if (libraryOptions) {\n      const authLibraryOptions = Amplify.libraryOptions.Auth;\n      // If ssr is provided through libraryOptions, we should respect the intentional reconfiguration.\n      if (libraryOptions.ssr !== undefined) {\n        cognitoUserPoolsTokenProvider.setKeyValueStorage(\n        // TODO: allow configure with a public interface\n        resolvedKeyValueStorage);\n        authLibraryOptions.credentialsProvider = resolvedCredentialsProvider;\n      }\n      Amplify.configure(resolvedResourceConfig, {\n        Auth: authLibraryOptions,\n        ...libraryOptions\n      });\n      return;\n    }\n    // Finally, if there were no libraryOptions given at all, we should simply not touch the currently\n    // configured libraryOptions.\n    Amplify.configure(resolvedResourceConfig);\n  },\n  /**\n   * Returns the {@link ResourcesConfig} object passed in as the `resourceConfig` parameter when calling\n   * `Amplify.configure`.\n   *\n   * @returns An {@link ResourcesConfig} object.\n   */\n  getConfig() {\n    return Amplify.getConfig();\n  }\n};\nexport { DefaultAmplify };", "map": {"version": 3, "names": ["Amplify", "Cookie<PERSON>torage", "defaultStorage", "parseAmplifyConfig", "CognitoAWSCredentialsAndIdentityIdProvider", "DefaultIdentityIdStore", "cognitoCredentialsProvider", "cognitoUserPoolsTokenProvider", "DefaultAmplify", "configure", "resourceConfig", "libraryOptions", "resolvedResourceConfig", "cookieBasedKeyValueStorage", "sameSite", "resolvedKeyValueStorage", "ssr", "resolvedCredentialsProvider", "<PERSON><PERSON>", "setAuthConfig", "setKeyValueStorage", "tokenProvider", "<PERSON><PERSON><PERSON><PERSON>", "authLibraryOptions", "undefined", "getConfig"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/aws-amplify/dist/esm/initSingleton.mjs"], "sourcesContent": ["import { Amplify, CookieStorage, defaultStorage } from '@aws-amplify/core';\nimport { parseAmplifyConfig } from '@aws-amplify/core/internals/utils';\nimport { CognitoAWSCredentialsAndIdentityIdProvider, DefaultIdentityIdStore, cognitoCredentialsProvider, cognitoUserPoolsTokenProvider } from '@aws-amplify/auth/cognito';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst DefaultAmplify = {\n    /**\n     * Configures Amplify with the {@link resourceConfig} and {@link libraryOptions}.\n     *\n     * @param resourceConfig The {@link ResourcesConfig} object that is typically imported from the\n     * `amplifyconfiguration.json` file. It can also be an object literal created inline when calling `Amplify.configure`.\n     * @param libraryOptions The {@link LibraryOptions} additional options for the library.\n     *\n     * @example\n     * import config from './amplifyconfiguration.json';\n     *\n     * Amplify.configure(config);\n     */\n    configure(resourceConfig, libraryOptions) {\n        const resolvedResourceConfig = parseAmplifyConfig(resourceConfig);\n        const cookieBasedKeyValueStorage = new CookieStorage({ sameSite: 'lax' });\n        const resolvedKeyValueStorage = libraryOptions?.ssr\n            ? cookieBasedKeyValueStorage\n            : defaultStorage;\n        const resolvedCredentialsProvider = libraryOptions?.ssr\n            ? new CognitoAWSCredentialsAndIdentityIdProvider(new DefaultIdentityIdStore(cookieBasedKeyValueStorage))\n            : cognitoCredentialsProvider;\n        // If no Auth config is provided, no special handling will be required, configure as is.\n        // Otherwise, we can assume an Auth config is provided from here on.\n        if (!resolvedResourceConfig.Auth) {\n            Amplify.configure(resolvedResourceConfig, libraryOptions);\n            return;\n        }\n        // If Auth options are provided, always just configure as is.\n        // Otherwise, we can assume no Auth libraryOptions were provided from here on.\n        if (libraryOptions?.Auth) {\n            Amplify.configure(resolvedResourceConfig, libraryOptions);\n            return;\n        }\n        // If no Auth libraryOptions were previously configured, then always add default providers.\n        if (!Amplify.libraryOptions.Auth) {\n            cognitoUserPoolsTokenProvider.setAuthConfig(resolvedResourceConfig.Auth);\n            cognitoUserPoolsTokenProvider.setKeyValueStorage(\n            // TODO: allow configure with a public interface\n            resolvedKeyValueStorage);\n            Amplify.configure(resolvedResourceConfig, {\n                ...libraryOptions,\n                Auth: {\n                    tokenProvider: cognitoUserPoolsTokenProvider,\n                    credentialsProvider: resolvedCredentialsProvider,\n                },\n            });\n            return;\n        }\n        // At this point, Auth libraryOptions would have been previously configured and no overriding\n        // Auth options were given, so we should preserve the currently configured Auth libraryOptions.\n        if (libraryOptions) {\n            const authLibraryOptions = Amplify.libraryOptions.Auth;\n            // If ssr is provided through libraryOptions, we should respect the intentional reconfiguration.\n            if (libraryOptions.ssr !== undefined) {\n                cognitoUserPoolsTokenProvider.setKeyValueStorage(\n                // TODO: allow configure with a public interface\n                resolvedKeyValueStorage);\n                authLibraryOptions.credentialsProvider = resolvedCredentialsProvider;\n            }\n            Amplify.configure(resolvedResourceConfig, {\n                Auth: authLibraryOptions,\n                ...libraryOptions,\n            });\n            return;\n        }\n        // Finally, if there were no libraryOptions given at all, we should simply not touch the currently\n        // configured libraryOptions.\n        Amplify.configure(resolvedResourceConfig);\n    },\n    /**\n     * Returns the {@link ResourcesConfig} object passed in as the `resourceConfig` parameter when calling\n     * `Amplify.configure`.\n     *\n     * @returns An {@link ResourcesConfig} object.\n     */\n    getConfig() {\n        return Amplify.getConfig();\n    },\n};\n\nexport { DefaultAmplify };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,aAAa,EAAEC,cAAc,QAAQ,mBAAmB;AAC1E,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,0CAA0C,EAAEC,sBAAsB,EAAEC,0BAA0B,EAAEC,6BAA6B,QAAQ,2BAA2B;;AAEzK;AACA;AACA,MAAMC,cAAc,GAAG;EACnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACC,cAAc,EAAEC,cAAc,EAAE;IACtC,MAAMC,sBAAsB,GAAGT,kBAAkB,CAACO,cAAc,CAAC;IACjE,MAAMG,0BAA0B,GAAG,IAAIZ,aAAa,CAAC;MAAEa,QAAQ,EAAE;IAAM,CAAC,CAAC;IACzE,MAAMC,uBAAuB,GAAGJ,cAAc,EAAEK,GAAG,GAC7CH,0BAA0B,GAC1BX,cAAc;IACpB,MAAMe,2BAA2B,GAAGN,cAAc,EAAEK,GAAG,GACjD,IAAIZ,0CAA0C,CAAC,IAAIC,sBAAsB,CAACQ,0BAA0B,CAAC,CAAC,GACtGP,0BAA0B;IAChC;IACA;IACA,IAAI,CAACM,sBAAsB,CAACM,IAAI,EAAE;MAC9BlB,OAAO,CAACS,SAAS,CAACG,sBAAsB,EAAED,cAAc,CAAC;MACzD;IACJ;IACA;IACA;IACA,IAAIA,cAAc,EAAEO,IAAI,EAAE;MACtBlB,OAAO,CAACS,SAAS,CAACG,sBAAsB,EAAED,cAAc,CAAC;MACzD;IACJ;IACA;IACA,IAAI,CAACX,OAAO,CAACW,cAAc,CAACO,IAAI,EAAE;MAC9BX,6BAA6B,CAACY,aAAa,CAACP,sBAAsB,CAACM,IAAI,CAAC;MACxEX,6BAA6B,CAACa,kBAAkB;MAChD;MACAL,uBAAuB,CAAC;MACxBf,OAAO,CAACS,SAAS,CAACG,sBAAsB,EAAE;QACtC,GAAGD,cAAc;QACjBO,IAAI,EAAE;UACFG,aAAa,EAAEd,6BAA6B;UAC5Ce,mBAAmB,EAAEL;QACzB;MACJ,CAAC,CAAC;MACF;IACJ;IACA;IACA;IACA,IAAIN,cAAc,EAAE;MAChB,MAAMY,kBAAkB,GAAGvB,OAAO,CAACW,cAAc,CAACO,IAAI;MACtD;MACA,IAAIP,cAAc,CAACK,GAAG,KAAKQ,SAAS,EAAE;QAClCjB,6BAA6B,CAACa,kBAAkB;QAChD;QACAL,uBAAuB,CAAC;QACxBQ,kBAAkB,CAACD,mBAAmB,GAAGL,2BAA2B;MACxE;MACAjB,OAAO,CAACS,SAAS,CAACG,sBAAsB,EAAE;QACtCM,IAAI,EAAEK,kBAAkB;QACxB,GAAGZ;MACP,CAAC,CAAC;MACF;IACJ;IACA;IACA;IACAX,OAAO,CAACS,SAAS,CAACG,sBAAsB,CAAC;EAC7C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIa,SAASA,CAAA,EAAG;IACR,OAAOzB,OAAO,CAACyB,SAAS,CAAC,CAAC;EAC9B;AACJ,CAAC;AAED,SAASjB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}