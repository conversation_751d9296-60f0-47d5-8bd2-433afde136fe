{"ast": null, "code": "/* AUTO-GENERATED. DO NOT MODIFY. */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n\n\n Style HTML\n---------------\n\n  Written by Nochum Sossonko, (<EMAIL>)\n\n  Based on code initially developed by: Einar Lielmanis, <<EMAIL>>\n    https://beautifier.io/\n\n  Usage:\n    style_html(html_source);\n\n    style_html(html_source, options);\n\n  The options are:\n    indent_inner_html (default false)  — indent <head> and <body> sections,\n    indent_size (default 4)          — indentation size,\n    indent_char (default space)      — character to indent with,\n    wrap_line_length (default 250)            -  maximum amount of characters per line (0 = disable)\n    brace_style (default \"collapse\") - \"collapse\" | \"expand\" | \"end-expand\" | \"none\"\n            put braces on the same line as control statements (default), or put braces on own line (Allman / ANSI style), or just put end braces on own line, or attempt to keep them where they are.\n    inline (defaults to inline tags) - list of tags to be considered inline tags\n    unformatted (defaults to inline tags) - list of tags, that shouldn't be reformatted\n    content_unformatted (defaults to [\"pre\", \"textarea\"] tags) - list of tags, whose content shouldn't be reformatted\n    indent_scripts (default normal)  - \"keep\"|\"separate\"|\"normal\"\n    preserve_newlines (default true) - whether existing line breaks before elements should be preserved\n                                        Only works before elements, not inside tags or for text.\n    max_preserve_newlines (default unlimited) - maximum number of line breaks to be preserved in one chunk\n    indent_handlebars (default false) - format and indent {{#foo}} and {{/foo}}\n    end_with_newline (false)          - end with a newline\n    extra_liners (default [head,body,/html]) -List of tags that should have an extra newline before them.\n\n    e.g.\n\n    style_html(html_source, {\n      'indent_inner_html': false,\n      'indent_size': 2,\n      'indent_char': ' ',\n      'wrap_line_length': 78,\n      'brace_style': 'expand',\n      'preserve_newlines': true,\n      'max_preserve_newlines': 5,\n      'indent_handlebars': false,\n      'extra_liners': ['/html']\n    });\n*/\n\n(function () {\n  /* GENERATED_BUILD_OUTPUT */\n  var legacy_beautify_html;\n  /******/\n  (function () {\n    // webpackBootstrap\n    /******/\n    \"use strict\";\n\n    /******/\n    var __webpack_modules__ = [\n      /* 0 */\n      /* 1 */\n\n      /* 4 */\n      /* 5 */\n\n      /* 7 */\n\n      /* 15 */\n      /* 16 */\n      /* 17 */\n    ,, (/* 2 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      function OutputLine(parent) {\n        this.__parent = parent;\n        this.__character_count = 0;\n        // use indent_count as a marker for this.__lines that have preserved indentation\n        this.__indent_count = -1;\n        this.__alignment_count = 0;\n        this.__wrap_point_index = 0;\n        this.__wrap_point_character_count = 0;\n        this.__wrap_point_indent_count = -1;\n        this.__wrap_point_alignment_count = 0;\n        this.__items = [];\n      }\n      OutputLine.prototype.clone_empty = function () {\n        var line = new OutputLine(this.__parent);\n        line.set_indent(this.__indent_count, this.__alignment_count);\n        return line;\n      };\n      OutputLine.prototype.item = function (index) {\n        if (index < 0) {\n          return this.__items[this.__items.length + index];\n        } else {\n          return this.__items[index];\n        }\n      };\n      OutputLine.prototype.has_match = function (pattern) {\n        for (var lastCheckedOutput = this.__items.length - 1; lastCheckedOutput >= 0; lastCheckedOutput--) {\n          if (this.__items[lastCheckedOutput].match(pattern)) {\n            return true;\n          }\n        }\n        return false;\n      };\n      OutputLine.prototype.set_indent = function (indent, alignment) {\n        if (this.is_empty()) {\n          this.__indent_count = indent || 0;\n          this.__alignment_count = alignment || 0;\n          this.__character_count = this.__parent.get_indent_size(this.__indent_count, this.__alignment_count);\n        }\n      };\n      OutputLine.prototype._set_wrap_point = function () {\n        if (this.__parent.wrap_line_length) {\n          this.__wrap_point_index = this.__items.length;\n          this.__wrap_point_character_count = this.__character_count;\n          this.__wrap_point_indent_count = this.__parent.next_line.__indent_count;\n          this.__wrap_point_alignment_count = this.__parent.next_line.__alignment_count;\n        }\n      };\n      OutputLine.prototype._should_wrap = function () {\n        return this.__wrap_point_index && this.__character_count > this.__parent.wrap_line_length && this.__wrap_point_character_count > this.__parent.next_line.__character_count;\n      };\n      OutputLine.prototype._allow_wrap = function () {\n        if (this._should_wrap()) {\n          this.__parent.add_new_line();\n          var next = this.__parent.current_line;\n          next.set_indent(this.__wrap_point_indent_count, this.__wrap_point_alignment_count);\n          next.__items = this.__items.slice(this.__wrap_point_index);\n          this.__items = this.__items.slice(0, this.__wrap_point_index);\n          next.__character_count += this.__character_count - this.__wrap_point_character_count;\n          this.__character_count = this.__wrap_point_character_count;\n          if (next.__items[0] === \" \") {\n            next.__items.splice(0, 1);\n            next.__character_count -= 1;\n          }\n          return true;\n        }\n        return false;\n      };\n      OutputLine.prototype.is_empty = function () {\n        return this.__items.length === 0;\n      };\n      OutputLine.prototype.last = function () {\n        if (!this.is_empty()) {\n          return this.__items[this.__items.length - 1];\n        } else {\n          return null;\n        }\n      };\n      OutputLine.prototype.push = function (item) {\n        this.__items.push(item);\n        var last_newline_index = item.lastIndexOf('\\n');\n        if (last_newline_index !== -1) {\n          this.__character_count = item.length - last_newline_index;\n        } else {\n          this.__character_count += item.length;\n        }\n      };\n      OutputLine.prototype.pop = function () {\n        var item = null;\n        if (!this.is_empty()) {\n          item = this.__items.pop();\n          this.__character_count -= item.length;\n        }\n        return item;\n      };\n      OutputLine.prototype._remove_indent = function () {\n        if (this.__indent_count > 0) {\n          this.__indent_count -= 1;\n          this.__character_count -= this.__parent.indent_size;\n        }\n      };\n      OutputLine.prototype._remove_wrap_indent = function () {\n        if (this.__wrap_point_indent_count > 0) {\n          this.__wrap_point_indent_count -= 1;\n        }\n      };\n      OutputLine.prototype.trim = function () {\n        while (this.last() === ' ') {\n          this.__items.pop();\n          this.__character_count -= 1;\n        }\n      };\n      OutputLine.prototype.toString = function () {\n        var result = '';\n        if (this.is_empty()) {\n          if (this.__parent.indent_empty_lines) {\n            result = this.__parent.get_indent_string(this.__indent_count);\n          }\n        } else {\n          result = this.__parent.get_indent_string(this.__indent_count, this.__alignment_count);\n          result += this.__items.join('');\n        }\n        return result;\n      };\n      function IndentStringCache(options, baseIndentString) {\n        this.__cache = [''];\n        this.__indent_size = options.indent_size;\n        this.__indent_string = options.indent_char;\n        if (!options.indent_with_tabs) {\n          this.__indent_string = new Array(options.indent_size + 1).join(options.indent_char);\n        }\n\n        // Set to null to continue support for auto detection of base indent\n        baseIndentString = baseIndentString || '';\n        if (options.indent_level > 0) {\n          baseIndentString = new Array(options.indent_level + 1).join(this.__indent_string);\n        }\n        this.__base_string = baseIndentString;\n        this.__base_string_length = baseIndentString.length;\n      }\n      IndentStringCache.prototype.get_indent_size = function (indent, column) {\n        var result = this.__base_string_length;\n        column = column || 0;\n        if (indent < 0) {\n          result = 0;\n        }\n        result += indent * this.__indent_size;\n        result += column;\n        return result;\n      };\n      IndentStringCache.prototype.get_indent_string = function (indent_level, column) {\n        var result = this.__base_string;\n        column = column || 0;\n        if (indent_level < 0) {\n          indent_level = 0;\n          result = '';\n        }\n        column += indent_level * this.__indent_size;\n        this.__ensure_cache(column);\n        result += this.__cache[column];\n        return result;\n      };\n      IndentStringCache.prototype.__ensure_cache = function (column) {\n        while (column >= this.__cache.length) {\n          this.__add_column();\n        }\n      };\n      IndentStringCache.prototype.__add_column = function () {\n        var column = this.__cache.length;\n        var indent = 0;\n        var result = '';\n        if (this.__indent_size && column >= this.__indent_size) {\n          indent = Math.floor(column / this.__indent_size);\n          column -= indent * this.__indent_size;\n          result = new Array(indent + 1).join(this.__indent_string);\n        }\n        if (column) {\n          result += new Array(column + 1).join(' ');\n        }\n        this.__cache.push(result);\n      };\n      function Output(options, baseIndentString) {\n        this.__indent_cache = new IndentStringCache(options, baseIndentString);\n        this.raw = false;\n        this._end_with_newline = options.end_with_newline;\n        this.indent_size = options.indent_size;\n        this.wrap_line_length = options.wrap_line_length;\n        this.indent_empty_lines = options.indent_empty_lines;\n        this.__lines = [];\n        this.previous_line = null;\n        this.current_line = null;\n        this.next_line = new OutputLine(this);\n        this.space_before_token = false;\n        this.non_breaking_space = false;\n        this.previous_token_wrapped = false;\n        // initialize\n        this.__add_outputline();\n      }\n      Output.prototype.__add_outputline = function () {\n        this.previous_line = this.current_line;\n        this.current_line = this.next_line.clone_empty();\n        this.__lines.push(this.current_line);\n      };\n      Output.prototype.get_line_number = function () {\n        return this.__lines.length;\n      };\n      Output.prototype.get_indent_string = function (indent, column) {\n        return this.__indent_cache.get_indent_string(indent, column);\n      };\n      Output.prototype.get_indent_size = function (indent, column) {\n        return this.__indent_cache.get_indent_size(indent, column);\n      };\n      Output.prototype.is_empty = function () {\n        return !this.previous_line && this.current_line.is_empty();\n      };\n      Output.prototype.add_new_line = function (force_newline) {\n        // never newline at the start of file\n        // otherwise, newline only if we didn't just add one or we're forced\n        if (this.is_empty() || !force_newline && this.just_added_newline()) {\n          return false;\n        }\n\n        // if raw output is enabled, don't print additional newlines,\n        // but still return True as though you had\n        if (!this.raw) {\n          this.__add_outputline();\n        }\n        return true;\n      };\n      Output.prototype.get_code = function (eol) {\n        this.trim(true);\n\n        // handle some edge cases where the last tokens\n        // has text that ends with newline(s)\n        var last_item = this.current_line.pop();\n        if (last_item) {\n          if (last_item[last_item.length - 1] === '\\n') {\n            last_item = last_item.replace(/\\n+$/g, '');\n          }\n          this.current_line.push(last_item);\n        }\n        if (this._end_with_newline) {\n          this.__add_outputline();\n        }\n        var sweet_code = this.__lines.join('\\n');\n        if (eol !== '\\n') {\n          sweet_code = sweet_code.replace(/[\\n]/g, eol);\n        }\n        return sweet_code;\n      };\n      Output.prototype.set_wrap_point = function () {\n        this.current_line._set_wrap_point();\n      };\n      Output.prototype.set_indent = function (indent, alignment) {\n        indent = indent || 0;\n        alignment = alignment || 0;\n\n        // Next line stores alignment values\n        this.next_line.set_indent(indent, alignment);\n\n        // Never indent your first output indent at the start of the file\n        if (this.__lines.length > 1) {\n          this.current_line.set_indent(indent, alignment);\n          return true;\n        }\n        this.current_line.set_indent();\n        return false;\n      };\n      Output.prototype.add_raw_token = function (token) {\n        for (var x = 0; x < token.newlines; x++) {\n          this.__add_outputline();\n        }\n        this.current_line.set_indent(-1);\n        this.current_line.push(token.whitespace_before);\n        this.current_line.push(token.text);\n        this.space_before_token = false;\n        this.non_breaking_space = false;\n        this.previous_token_wrapped = false;\n      };\n      Output.prototype.add_token = function (printable_token) {\n        this.__add_space_before_token();\n        this.current_line.push(printable_token);\n        this.space_before_token = false;\n        this.non_breaking_space = false;\n        this.previous_token_wrapped = this.current_line._allow_wrap();\n      };\n      Output.prototype.__add_space_before_token = function () {\n        if (this.space_before_token && !this.just_added_newline()) {\n          if (!this.non_breaking_space) {\n            this.set_wrap_point();\n          }\n          this.current_line.push(' ');\n        }\n      };\n      Output.prototype.remove_indent = function (index) {\n        var output_length = this.__lines.length;\n        while (index < output_length) {\n          this.__lines[index]._remove_indent();\n          index++;\n        }\n        this.current_line._remove_wrap_indent();\n      };\n      Output.prototype.trim = function (eat_newlines) {\n        eat_newlines = eat_newlines === undefined ? false : eat_newlines;\n        this.current_line.trim();\n        while (eat_newlines && this.__lines.length > 1 && this.current_line.is_empty()) {\n          this.__lines.pop();\n          this.current_line = this.__lines[this.__lines.length - 1];\n          this.current_line.trim();\n        }\n        this.previous_line = this.__lines.length > 1 ? this.__lines[this.__lines.length - 2] : null;\n      };\n      Output.prototype.just_added_newline = function () {\n        return this.current_line.is_empty();\n      };\n      Output.prototype.just_added_blankline = function () {\n        return this.is_empty() || this.current_line.is_empty() && this.previous_line.is_empty();\n      };\n      Output.prototype.ensure_empty_line_above = function (starts_with, ends_with) {\n        var index = this.__lines.length - 2;\n        while (index >= 0) {\n          var potentialEmptyLine = this.__lines[index];\n          if (potentialEmptyLine.is_empty()) {\n            break;\n          } else if (potentialEmptyLine.item(0).indexOf(starts_with) !== 0 && potentialEmptyLine.item(-1) !== ends_with) {\n            this.__lines.splice(index + 1, 0, new OutputLine(this));\n            this.previous_line = this.__lines[this.__lines.length - 2];\n            break;\n          }\n          index--;\n        }\n      };\n      module.exports.Output = Output;\n\n      /***/\n    }), (/* 3 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      function Token(type, text, newlines, whitespace_before) {\n        this.type = type;\n        this.text = text;\n\n        // comments_before are\n        // comments that have a new line before them\n        // and may or may not have a newline after\n        // this is a set of comments before\n        this.comments_before = null; /* inline comment*/\n\n        // this.comments_after =  new TokenStream(); // no new line before and newline after\n        this.newlines = newlines || 0;\n        this.whitespace_before = whitespace_before || '';\n        this.parent = null;\n        this.next = null;\n        this.previous = null;\n        this.opened = null;\n        this.closed = null;\n        this.directives = null;\n      }\n      module.exports.Token = Token;\n\n      /***/\n    }),,, (/* 6 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      function Options(options, merge_child_field) {\n        this.raw_options = _mergeOpts(options, merge_child_field);\n\n        // Support passing the source text back with no change\n        this.disabled = this._get_boolean('disabled');\n        this.eol = this._get_characters('eol', 'auto');\n        this.end_with_newline = this._get_boolean('end_with_newline');\n        this.indent_size = this._get_number('indent_size', 4);\n        this.indent_char = this._get_characters('indent_char', ' ');\n        this.indent_level = this._get_number('indent_level');\n        this.preserve_newlines = this._get_boolean('preserve_newlines', true);\n        this.max_preserve_newlines = this._get_number('max_preserve_newlines', 32786);\n        if (!this.preserve_newlines) {\n          this.max_preserve_newlines = 0;\n        }\n        this.indent_with_tabs = this._get_boolean('indent_with_tabs', this.indent_char === '\\t');\n        if (this.indent_with_tabs) {\n          this.indent_char = '\\t';\n\n          // indent_size behavior changed after 1.8.6\n          // It used to be that indent_size would be\n          // set to 1 for indent_with_tabs. That is no longer needed and\n          // actually doesn't make sense - why not use spaces? Further,\n          // that might produce unexpected behavior - tabs being used\n          // for single-column alignment. So, when indent_with_tabs is true\n          // and indent_size is 1, reset indent_size to 4.\n          if (this.indent_size === 1) {\n            this.indent_size = 4;\n          }\n        }\n\n        // Backwards compat with 1.3.x\n        this.wrap_line_length = this._get_number('wrap_line_length', this._get_number('max_char'));\n        this.indent_empty_lines = this._get_boolean('indent_empty_lines');\n\n        // valid templating languages ['django', 'erb', 'handlebars', 'php', 'smarty', 'angular']\n        // For now, 'auto' = all off for javascript, all except angular on for html (and inline javascript/css).\n        // other values ignored\n        this.templating = this._get_selection_list('templating', ['auto', 'none', 'angular', 'django', 'erb', 'handlebars', 'php', 'smarty'], ['auto']);\n      }\n      Options.prototype._get_array = function (name, default_value) {\n        var option_value = this.raw_options[name];\n        var result = default_value || [];\n        if (typeof option_value === 'object') {\n          if (option_value !== null && typeof option_value.concat === 'function') {\n            result = option_value.concat();\n          }\n        } else if (typeof option_value === 'string') {\n          result = option_value.split(/[^a-zA-Z0-9_\\/\\-]+/);\n        }\n        return result;\n      };\n      Options.prototype._get_boolean = function (name, default_value) {\n        var option_value = this.raw_options[name];\n        var result = option_value === undefined ? !!default_value : !!option_value;\n        return result;\n      };\n      Options.prototype._get_characters = function (name, default_value) {\n        var option_value = this.raw_options[name];\n        var result = default_value || '';\n        if (typeof option_value === 'string') {\n          result = option_value.replace(/\\\\r/, '\\r').replace(/\\\\n/, '\\n').replace(/\\\\t/, '\\t');\n        }\n        return result;\n      };\n      Options.prototype._get_number = function (name, default_value) {\n        var option_value = this.raw_options[name];\n        default_value = parseInt(default_value, 10);\n        if (isNaN(default_value)) {\n          default_value = 0;\n        }\n        var result = parseInt(option_value, 10);\n        if (isNaN(result)) {\n          result = default_value;\n        }\n        return result;\n      };\n      Options.prototype._get_selection = function (name, selection_list, default_value) {\n        var result = this._get_selection_list(name, selection_list, default_value);\n        if (result.length !== 1) {\n          throw new Error(\"Invalid Option Value: The option '\" + name + \"' can only be one of the following values:\\n\" + selection_list + \"\\nYou passed in: '\" + this.raw_options[name] + \"'\");\n        }\n        return result[0];\n      };\n      Options.prototype._get_selection_list = function (name, selection_list, default_value) {\n        if (!selection_list || selection_list.length === 0) {\n          throw new Error(\"Selection list cannot be empty.\");\n        }\n        default_value = default_value || [selection_list[0]];\n        if (!this._is_valid_selection(default_value, selection_list)) {\n          throw new Error(\"Invalid Default Value!\");\n        }\n        var result = this._get_array(name, default_value);\n        if (!this._is_valid_selection(result, selection_list)) {\n          throw new Error(\"Invalid Option Value: The option '\" + name + \"' can contain only the following values:\\n\" + selection_list + \"\\nYou passed in: '\" + this.raw_options[name] + \"'\");\n        }\n        return result;\n      };\n      Options.prototype._is_valid_selection = function (result, selection_list) {\n        return result.length && selection_list.length && !result.some(function (item) {\n          return selection_list.indexOf(item) === -1;\n        });\n      };\n\n      // merges child options up with the parent options object\n      // Example: obj = {a: 1, b: {a: 2}}\n      //          mergeOpts(obj, 'b')\n      //\n      //          Returns: {a: 2}\n      function _mergeOpts(allOptions, childFieldName) {\n        var finalOpts = {};\n        allOptions = _normalizeOpts(allOptions);\n        var name;\n        for (name in allOptions) {\n          if (name !== childFieldName) {\n            finalOpts[name] = allOptions[name];\n          }\n        }\n\n        //merge in the per type settings for the childFieldName\n        if (childFieldName && allOptions[childFieldName]) {\n          for (name in allOptions[childFieldName]) {\n            finalOpts[name] = allOptions[childFieldName][name];\n          }\n        }\n        return finalOpts;\n      }\n      function _normalizeOpts(options) {\n        var convertedOpts = {};\n        var key;\n        for (key in options) {\n          var newKey = key.replace(/-/g, \"_\");\n          convertedOpts[newKey] = options[key];\n        }\n        return convertedOpts;\n      }\n      module.exports.Options = Options;\n      module.exports.normalizeOpts = _normalizeOpts;\n      module.exports.mergeOpts = _mergeOpts;\n\n      /***/\n    }),, (/* 8 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var regexp_has_sticky = RegExp.prototype.hasOwnProperty('sticky');\n      function InputScanner(input_string) {\n        this.__input = input_string || '';\n        this.__input_length = this.__input.length;\n        this.__position = 0;\n      }\n      InputScanner.prototype.restart = function () {\n        this.__position = 0;\n      };\n      InputScanner.prototype.back = function () {\n        if (this.__position > 0) {\n          this.__position -= 1;\n        }\n      };\n      InputScanner.prototype.hasNext = function () {\n        return this.__position < this.__input_length;\n      };\n      InputScanner.prototype.next = function () {\n        var val = null;\n        if (this.hasNext()) {\n          val = this.__input.charAt(this.__position);\n          this.__position += 1;\n        }\n        return val;\n      };\n      InputScanner.prototype.peek = function (index) {\n        var val = null;\n        index = index || 0;\n        index += this.__position;\n        if (index >= 0 && index < this.__input_length) {\n          val = this.__input.charAt(index);\n        }\n        return val;\n      };\n\n      // This is a JavaScript only helper function (not in python)\n      // Javascript doesn't have a match method\n      // and not all implementation support \"sticky\" flag.\n      // If they do not support sticky then both this.match() and this.test() method\n      // must get the match and check the index of the match.\n      // If sticky is supported and set, this method will use it.\n      // Otherwise it will check that global is set, and fall back to the slower method.\n      InputScanner.prototype.__match = function (pattern, index) {\n        pattern.lastIndex = index;\n        var pattern_match = pattern.exec(this.__input);\n        if (pattern_match && !(regexp_has_sticky && pattern.sticky)) {\n          if (pattern_match.index !== index) {\n            pattern_match = null;\n          }\n        }\n        return pattern_match;\n      };\n      InputScanner.prototype.test = function (pattern, index) {\n        index = index || 0;\n        index += this.__position;\n        if (index >= 0 && index < this.__input_length) {\n          return !!this.__match(pattern, index);\n        } else {\n          return false;\n        }\n      };\n      InputScanner.prototype.testChar = function (pattern, index) {\n        // test one character regex match\n        var val = this.peek(index);\n        pattern.lastIndex = 0;\n        return val !== null && pattern.test(val);\n      };\n      InputScanner.prototype.match = function (pattern) {\n        var pattern_match = this.__match(pattern, this.__position);\n        if (pattern_match) {\n          this.__position += pattern_match[0].length;\n        } else {\n          pattern_match = null;\n        }\n        return pattern_match;\n      };\n      InputScanner.prototype.read = function (starting_pattern, until_pattern, until_after) {\n        var val = '';\n        var match;\n        if (starting_pattern) {\n          match = this.match(starting_pattern);\n          if (match) {\n            val += match[0];\n          }\n        }\n        if (until_pattern && (match || !starting_pattern)) {\n          val += this.readUntil(until_pattern, until_after);\n        }\n        return val;\n      };\n      InputScanner.prototype.readUntil = function (pattern, until_after) {\n        var val = '';\n        var match_index = this.__position;\n        pattern.lastIndex = this.__position;\n        var pattern_match = pattern.exec(this.__input);\n        if (pattern_match) {\n          match_index = pattern_match.index;\n          if (until_after) {\n            match_index += pattern_match[0].length;\n          }\n        } else {\n          match_index = this.__input_length;\n        }\n        val = this.__input.substring(this.__position, match_index);\n        this.__position = match_index;\n        return val;\n      };\n      InputScanner.prototype.readUntilAfter = function (pattern) {\n        return this.readUntil(pattern, true);\n      };\n      InputScanner.prototype.get_regexp = function (pattern, match_from) {\n        var result = null;\n        var flags = 'g';\n        if (match_from && regexp_has_sticky) {\n          flags = 'y';\n        }\n        // strings are converted to regexp\n        if (typeof pattern === \"string\" && pattern !== '') {\n          // result = new RegExp(pattern.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'), flags);\n          result = new RegExp(pattern, flags);\n        } else if (pattern) {\n          result = new RegExp(pattern.source, flags);\n        }\n        return result;\n      };\n      InputScanner.prototype.get_literal_regexp = function (literal_string) {\n        return RegExp(literal_string.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'));\n      };\n\n      /* css beautifier legacy helpers */\n      InputScanner.prototype.peekUntilAfter = function (pattern) {\n        var start = this.__position;\n        var val = this.readUntilAfter(pattern);\n        this.__position = start;\n        return val;\n      };\n      InputScanner.prototype.lookBack = function (testVal) {\n        var start = this.__position - 1;\n        return start >= testVal.length && this.__input.substring(start - testVal.length, start).toLowerCase() === testVal;\n      };\n      module.exports.InputScanner = InputScanner;\n\n      /***/\n    }), (/* 9 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var InputScanner = __webpack_require__(8).InputScanner;\n      var Token = __webpack_require__(3).Token;\n      var TokenStream = __webpack_require__(10).TokenStream;\n      var WhitespacePattern = __webpack_require__(11).WhitespacePattern;\n      var TOKEN = {\n        START: 'TK_START',\n        RAW: 'TK_RAW',\n        EOF: 'TK_EOF'\n      };\n      var Tokenizer = function (input_string, options) {\n        this._input = new InputScanner(input_string);\n        this._options = options || {};\n        this.__tokens = null;\n        this._patterns = {};\n        this._patterns.whitespace = new WhitespacePattern(this._input);\n      };\n      Tokenizer.prototype.tokenize = function () {\n        this._input.restart();\n        this.__tokens = new TokenStream();\n        this._reset();\n        var current;\n        var previous = new Token(TOKEN.START, '');\n        var open_token = null;\n        var open_stack = [];\n        var comments = new TokenStream();\n        while (previous.type !== TOKEN.EOF) {\n          current = this._get_next_token(previous, open_token);\n          while (this._is_comment(current)) {\n            comments.add(current);\n            current = this._get_next_token(previous, open_token);\n          }\n          if (!comments.isEmpty()) {\n            current.comments_before = comments;\n            comments = new TokenStream();\n          }\n          current.parent = open_token;\n          if (this._is_opening(current)) {\n            open_stack.push(open_token);\n            open_token = current;\n          } else if (open_token && this._is_closing(current, open_token)) {\n            current.opened = open_token;\n            open_token.closed = current;\n            open_token = open_stack.pop();\n            current.parent = open_token;\n          }\n          current.previous = previous;\n          previous.next = current;\n          this.__tokens.add(current);\n          previous = current;\n        }\n        return this.__tokens;\n      };\n      Tokenizer.prototype._is_first_token = function () {\n        return this.__tokens.isEmpty();\n      };\n      Tokenizer.prototype._reset = function () {};\n      Tokenizer.prototype._get_next_token = function (previous_token, open_token) {\n        // jshint unused:false\n        this._readWhitespace();\n        var resulting_string = this._input.read(/.+/g);\n        if (resulting_string) {\n          return this._create_token(TOKEN.RAW, resulting_string);\n        } else {\n          return this._create_token(TOKEN.EOF, '');\n        }\n      };\n      Tokenizer.prototype._is_comment = function (current_token) {\n        // jshint unused:false\n        return false;\n      };\n      Tokenizer.prototype._is_opening = function (current_token) {\n        // jshint unused:false\n        return false;\n      };\n      Tokenizer.prototype._is_closing = function (current_token, open_token) {\n        // jshint unused:false\n        return false;\n      };\n      Tokenizer.prototype._create_token = function (type, text) {\n        var token = new Token(type, text, this._patterns.whitespace.newline_count, this._patterns.whitespace.whitespace_before_token);\n        return token;\n      };\n      Tokenizer.prototype._readWhitespace = function () {\n        return this._patterns.whitespace.read();\n      };\n      module.exports.Tokenizer = Tokenizer;\n      module.exports.TOKEN = TOKEN;\n\n      /***/\n    }), (/* 10 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      function TokenStream(parent_token) {\n        // private\n        this.__tokens = [];\n        this.__tokens_length = this.__tokens.length;\n        this.__position = 0;\n        this.__parent_token = parent_token;\n      }\n      TokenStream.prototype.restart = function () {\n        this.__position = 0;\n      };\n      TokenStream.prototype.isEmpty = function () {\n        return this.__tokens_length === 0;\n      };\n      TokenStream.prototype.hasNext = function () {\n        return this.__position < this.__tokens_length;\n      };\n      TokenStream.prototype.next = function () {\n        var val = null;\n        if (this.hasNext()) {\n          val = this.__tokens[this.__position];\n          this.__position += 1;\n        }\n        return val;\n      };\n      TokenStream.prototype.peek = function (index) {\n        var val = null;\n        index = index || 0;\n        index += this.__position;\n        if (index >= 0 && index < this.__tokens_length) {\n          val = this.__tokens[index];\n        }\n        return val;\n      };\n      TokenStream.prototype.add = function (token) {\n        if (this.__parent_token) {\n          token.parent = this.__parent_token;\n        }\n        this.__tokens.push(token);\n        this.__tokens_length += 1;\n      };\n      module.exports.TokenStream = TokenStream;\n\n      /***/\n    }), (/* 11 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var Pattern = __webpack_require__(12).Pattern;\n      function WhitespacePattern(input_scanner, parent) {\n        Pattern.call(this, input_scanner, parent);\n        if (parent) {\n          this._line_regexp = this._input.get_regexp(parent._line_regexp);\n        } else {\n          this.__set_whitespace_patterns('', '');\n        }\n        this.newline_count = 0;\n        this.whitespace_before_token = '';\n      }\n      WhitespacePattern.prototype = new Pattern();\n      WhitespacePattern.prototype.__set_whitespace_patterns = function (whitespace_chars, newline_chars) {\n        whitespace_chars += '\\\\t ';\n        newline_chars += '\\\\n\\\\r';\n        this._match_pattern = this._input.get_regexp('[' + whitespace_chars + newline_chars + ']+', true);\n        this._newline_regexp = this._input.get_regexp('\\\\r\\\\n|[' + newline_chars + ']');\n      };\n      WhitespacePattern.prototype.read = function () {\n        this.newline_count = 0;\n        this.whitespace_before_token = '';\n        var resulting_string = this._input.read(this._match_pattern);\n        if (resulting_string === ' ') {\n          this.whitespace_before_token = ' ';\n        } else if (resulting_string) {\n          var matches = this.__split(this._newline_regexp, resulting_string);\n          this.newline_count = matches.length - 1;\n          this.whitespace_before_token = matches[this.newline_count];\n        }\n        return resulting_string;\n      };\n      WhitespacePattern.prototype.matching = function (whitespace_chars, newline_chars) {\n        var result = this._create();\n        result.__set_whitespace_patterns(whitespace_chars, newline_chars);\n        result._update();\n        return result;\n      };\n      WhitespacePattern.prototype._create = function () {\n        return new WhitespacePattern(this._input, this);\n      };\n      WhitespacePattern.prototype.__split = function (regexp, input_string) {\n        regexp.lastIndex = 0;\n        var start_index = 0;\n        var result = [];\n        var next_match = regexp.exec(input_string);\n        while (next_match) {\n          result.push(input_string.substring(start_index, next_match.index));\n          start_index = next_match.index + next_match[0].length;\n          next_match = regexp.exec(input_string);\n        }\n        if (start_index < input_string.length) {\n          result.push(input_string.substring(start_index, input_string.length));\n        } else {\n          result.push('');\n        }\n        return result;\n      };\n      module.exports.WhitespacePattern = WhitespacePattern;\n\n      /***/\n    }), (/* 12 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      function Pattern(input_scanner, parent) {\n        this._input = input_scanner;\n        this._starting_pattern = null;\n        this._match_pattern = null;\n        this._until_pattern = null;\n        this._until_after = false;\n        if (parent) {\n          this._starting_pattern = this._input.get_regexp(parent._starting_pattern, true);\n          this._match_pattern = this._input.get_regexp(parent._match_pattern, true);\n          this._until_pattern = this._input.get_regexp(parent._until_pattern);\n          this._until_after = parent._until_after;\n        }\n      }\n      Pattern.prototype.read = function () {\n        var result = this._input.read(this._starting_pattern);\n        if (!this._starting_pattern || result) {\n          result += this._input.read(this._match_pattern, this._until_pattern, this._until_after);\n        }\n        return result;\n      };\n      Pattern.prototype.read_match = function () {\n        return this._input.match(this._match_pattern);\n      };\n      Pattern.prototype.until_after = function (pattern) {\n        var result = this._create();\n        result._until_after = true;\n        result._until_pattern = this._input.get_regexp(pattern);\n        result._update();\n        return result;\n      };\n      Pattern.prototype.until = function (pattern) {\n        var result = this._create();\n        result._until_after = false;\n        result._until_pattern = this._input.get_regexp(pattern);\n        result._update();\n        return result;\n      };\n      Pattern.prototype.starting_with = function (pattern) {\n        var result = this._create();\n        result._starting_pattern = this._input.get_regexp(pattern, true);\n        result._update();\n        return result;\n      };\n      Pattern.prototype.matching = function (pattern) {\n        var result = this._create();\n        result._match_pattern = this._input.get_regexp(pattern, true);\n        result._update();\n        return result;\n      };\n      Pattern.prototype._create = function () {\n        return new Pattern(this._input, this);\n      };\n      Pattern.prototype._update = function () {};\n      module.exports.Pattern = Pattern;\n\n      /***/\n    }), (/* 13 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      function Directives(start_block_pattern, end_block_pattern) {\n        start_block_pattern = typeof start_block_pattern === 'string' ? start_block_pattern : start_block_pattern.source;\n        end_block_pattern = typeof end_block_pattern === 'string' ? end_block_pattern : end_block_pattern.source;\n        this.__directives_block_pattern = new RegExp(start_block_pattern + / beautify( \\w+[:]\\w+)+ /.source + end_block_pattern, 'g');\n        this.__directive_pattern = / (\\w+)[:](\\w+)/g;\n        this.__directives_end_ignore_pattern = new RegExp(start_block_pattern + /\\sbeautify\\signore:end\\s/.source + end_block_pattern, 'g');\n      }\n      Directives.prototype.get_directives = function (text) {\n        if (!text.match(this.__directives_block_pattern)) {\n          return null;\n        }\n        var directives = {};\n        this.__directive_pattern.lastIndex = 0;\n        var directive_match = this.__directive_pattern.exec(text);\n        while (directive_match) {\n          directives[directive_match[1]] = directive_match[2];\n          directive_match = this.__directive_pattern.exec(text);\n        }\n        return directives;\n      };\n      Directives.prototype.readIgnored = function (input) {\n        return input.readUntilAfter(this.__directives_end_ignore_pattern);\n      };\n      module.exports.Directives = Directives;\n\n      /***/\n    }), (/* 14 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var Pattern = __webpack_require__(12).Pattern;\n      var template_names = {\n        django: false,\n        erb: false,\n        handlebars: false,\n        php: false,\n        smarty: false,\n        angular: false\n      };\n\n      // This lets templates appear anywhere we would do a readUntil\n      // The cost is higher but it is pay to play.\n      function TemplatablePattern(input_scanner, parent) {\n        Pattern.call(this, input_scanner, parent);\n        this.__template_pattern = null;\n        this._disabled = Object.assign({}, template_names);\n        this._excluded = Object.assign({}, template_names);\n        if (parent) {\n          this.__template_pattern = this._input.get_regexp(parent.__template_pattern);\n          this._excluded = Object.assign(this._excluded, parent._excluded);\n          this._disabled = Object.assign(this._disabled, parent._disabled);\n        }\n        var pattern = new Pattern(input_scanner);\n        this.__patterns = {\n          handlebars_comment: pattern.starting_with(/{{!--/).until_after(/--}}/),\n          handlebars_unescaped: pattern.starting_with(/{{{/).until_after(/}}}/),\n          handlebars: pattern.starting_with(/{{/).until_after(/}}/),\n          php: pattern.starting_with(/<\\?(?:[= ]|php)/).until_after(/\\?>/),\n          erb: pattern.starting_with(/<%[^%]/).until_after(/[^%]%>/),\n          // django coflicts with handlebars a bit.\n          django: pattern.starting_with(/{%/).until_after(/%}/),\n          django_value: pattern.starting_with(/{{/).until_after(/}}/),\n          django_comment: pattern.starting_with(/{#/).until_after(/#}/),\n          smarty: pattern.starting_with(/{(?=[^}{\\s\\n])/).until_after(/[^\\s\\n]}/),\n          smarty_comment: pattern.starting_with(/{\\*/).until_after(/\\*}/),\n          smarty_literal: pattern.starting_with(/{literal}/).until_after(/{\\/literal}/)\n        };\n      }\n      TemplatablePattern.prototype = new Pattern();\n      TemplatablePattern.prototype._create = function () {\n        return new TemplatablePattern(this._input, this);\n      };\n      TemplatablePattern.prototype._update = function () {\n        this.__set_templated_pattern();\n      };\n      TemplatablePattern.prototype.disable = function (language) {\n        var result = this._create();\n        result._disabled[language] = true;\n        result._update();\n        return result;\n      };\n      TemplatablePattern.prototype.read_options = function (options) {\n        var result = this._create();\n        for (var language in template_names) {\n          result._disabled[language] = options.templating.indexOf(language) === -1;\n        }\n        result._update();\n        return result;\n      };\n      TemplatablePattern.prototype.exclude = function (language) {\n        var result = this._create();\n        result._excluded[language] = true;\n        result._update();\n        return result;\n      };\n      TemplatablePattern.prototype.read = function () {\n        var result = '';\n        if (this._match_pattern) {\n          result = this._input.read(this._starting_pattern);\n        } else {\n          result = this._input.read(this._starting_pattern, this.__template_pattern);\n        }\n        var next = this._read_template();\n        while (next) {\n          if (this._match_pattern) {\n            next += this._input.read(this._match_pattern);\n          } else {\n            next += this._input.readUntil(this.__template_pattern);\n          }\n          result += next;\n          next = this._read_template();\n        }\n        if (this._until_after) {\n          result += this._input.readUntilAfter(this._until_pattern);\n        }\n        return result;\n      };\n      TemplatablePattern.prototype.__set_templated_pattern = function () {\n        var items = [];\n        if (!this._disabled.php) {\n          items.push(this.__patterns.php._starting_pattern.source);\n        }\n        if (!this._disabled.handlebars) {\n          items.push(this.__patterns.handlebars._starting_pattern.source);\n        }\n        if (!this._disabled.angular) {\n          // Handlebars ('{{' and '}}') are also special tokens in Angular)\n          items.push(this.__patterns.handlebars._starting_pattern.source);\n        }\n        if (!this._disabled.erb) {\n          items.push(this.__patterns.erb._starting_pattern.source);\n        }\n        if (!this._disabled.django) {\n          items.push(this.__patterns.django._starting_pattern.source);\n          // The starting pattern for django is more complex because it has different\n          // patterns for value, comment, and other sections\n          items.push(this.__patterns.django_value._starting_pattern.source);\n          items.push(this.__patterns.django_comment._starting_pattern.source);\n        }\n        if (!this._disabled.smarty) {\n          items.push(this.__patterns.smarty._starting_pattern.source);\n        }\n        if (this._until_pattern) {\n          items.push(this._until_pattern.source);\n        }\n        this.__template_pattern = this._input.get_regexp('(?:' + items.join('|') + ')');\n      };\n      TemplatablePattern.prototype._read_template = function () {\n        var resulting_string = '';\n        var c = this._input.peek();\n        if (c === '<') {\n          var peek1 = this._input.peek(1);\n          //if we're in a comment, do something special\n          // We treat all comments as literals, even more than preformatted tags\n          // we just look for the appropriate close tag\n          if (!this._disabled.php && !this._excluded.php && peek1 === '?') {\n            resulting_string = resulting_string || this.__patterns.php.read();\n          }\n          if (!this._disabled.erb && !this._excluded.erb && peek1 === '%') {\n            resulting_string = resulting_string || this.__patterns.erb.read();\n          }\n        } else if (c === '{') {\n          if (!this._disabled.handlebars && !this._excluded.handlebars) {\n            resulting_string = resulting_string || this.__patterns.handlebars_comment.read();\n            resulting_string = resulting_string || this.__patterns.handlebars_unescaped.read();\n            resulting_string = resulting_string || this.__patterns.handlebars.read();\n          }\n          if (!this._disabled.django) {\n            // django coflicts with handlebars a bit.\n            if (!this._excluded.django && !this._excluded.handlebars) {\n              resulting_string = resulting_string || this.__patterns.django_value.read();\n            }\n            if (!this._excluded.django) {\n              resulting_string = resulting_string || this.__patterns.django_comment.read();\n              resulting_string = resulting_string || this.__patterns.django.read();\n            }\n          }\n          if (!this._disabled.smarty) {\n            // smarty cannot be enabled with django or handlebars enabled\n            if (this._disabled.django && this._disabled.handlebars) {\n              resulting_string = resulting_string || this.__patterns.smarty_comment.read();\n              resulting_string = resulting_string || this.__patterns.smarty_literal.read();\n              resulting_string = resulting_string || this.__patterns.smarty.read();\n            }\n          }\n        }\n        return resulting_string;\n      };\n      module.exports.TemplatablePattern = TemplatablePattern;\n\n      /***/\n    }),,,, (/* 18 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var Beautifier = __webpack_require__(19).Beautifier,\n        Options = __webpack_require__(20).Options;\n      function style_html(html_source, options, js_beautify, css_beautify) {\n        var beautifier = new Beautifier(html_source, options, js_beautify, css_beautify);\n        return beautifier.beautify();\n      }\n      module.exports = style_html;\n      module.exports.defaultOptions = function () {\n        return new Options();\n      };\n\n      /***/\n    }), (/* 19 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var Options = __webpack_require__(20).Options;\n      var Output = __webpack_require__(2).Output;\n      var Tokenizer = __webpack_require__(21).Tokenizer;\n      var TOKEN = __webpack_require__(21).TOKEN;\n      var lineBreak = /\\r\\n|[\\r\\n]/;\n      var allLineBreaks = /\\r\\n|[\\r\\n]/g;\n      var Printer = function (options, base_indent_string) {\n        //handles input/output and some other printing functions\n\n        this.indent_level = 0;\n        this.alignment_size = 0;\n        this.max_preserve_newlines = options.max_preserve_newlines;\n        this.preserve_newlines = options.preserve_newlines;\n        this._output = new Output(options, base_indent_string);\n      };\n      Printer.prototype.current_line_has_match = function (pattern) {\n        return this._output.current_line.has_match(pattern);\n      };\n      Printer.prototype.set_space_before_token = function (value, non_breaking) {\n        this._output.space_before_token = value;\n        this._output.non_breaking_space = non_breaking;\n      };\n      Printer.prototype.set_wrap_point = function () {\n        this._output.set_indent(this.indent_level, this.alignment_size);\n        this._output.set_wrap_point();\n      };\n      Printer.prototype.add_raw_token = function (token) {\n        this._output.add_raw_token(token);\n      };\n      Printer.prototype.print_preserved_newlines = function (raw_token) {\n        var newlines = 0;\n        if (raw_token.type !== TOKEN.TEXT && raw_token.previous.type !== TOKEN.TEXT) {\n          newlines = raw_token.newlines ? 1 : 0;\n        }\n        if (this.preserve_newlines) {\n          newlines = raw_token.newlines < this.max_preserve_newlines + 1 ? raw_token.newlines : this.max_preserve_newlines + 1;\n        }\n        for (var n = 0; n < newlines; n++) {\n          this.print_newline(n > 0);\n        }\n        return newlines !== 0;\n      };\n      Printer.prototype.traverse_whitespace = function (raw_token) {\n        if (raw_token.whitespace_before || raw_token.newlines) {\n          if (!this.print_preserved_newlines(raw_token)) {\n            this._output.space_before_token = true;\n          }\n          return true;\n        }\n        return false;\n      };\n      Printer.prototype.previous_token_wrapped = function () {\n        return this._output.previous_token_wrapped;\n      };\n      Printer.prototype.print_newline = function (force) {\n        this._output.add_new_line(force);\n      };\n      Printer.prototype.print_token = function (token) {\n        if (token.text) {\n          this._output.set_indent(this.indent_level, this.alignment_size);\n          this._output.add_token(token.text);\n        }\n      };\n      Printer.prototype.indent = function () {\n        this.indent_level++;\n      };\n      Printer.prototype.deindent = function () {\n        if (this.indent_level > 0) {\n          this.indent_level--;\n          this._output.set_indent(this.indent_level, this.alignment_size);\n        }\n      };\n      Printer.prototype.get_full_indent = function (level) {\n        level = this.indent_level + (level || 0);\n        if (level < 1) {\n          return '';\n        }\n        return this._output.get_indent_string(level);\n      };\n      var get_type_attribute = function (start_token) {\n        var result = null;\n        var raw_token = start_token.next;\n\n        // Search attributes for a type attribute\n        while (raw_token.type !== TOKEN.EOF && start_token.closed !== raw_token) {\n          if (raw_token.type === TOKEN.ATTRIBUTE && raw_token.text === 'type') {\n            if (raw_token.next && raw_token.next.type === TOKEN.EQUALS && raw_token.next.next && raw_token.next.next.type === TOKEN.VALUE) {\n              result = raw_token.next.next.text;\n            }\n            break;\n          }\n          raw_token = raw_token.next;\n        }\n        return result;\n      };\n      var get_custom_beautifier_name = function (tag_check, raw_token) {\n        var typeAttribute = null;\n        var result = null;\n        if (!raw_token.closed) {\n          return null;\n        }\n        if (tag_check === 'script') {\n          typeAttribute = 'text/javascript';\n        } else if (tag_check === 'style') {\n          typeAttribute = 'text/css';\n        }\n        typeAttribute = get_type_attribute(raw_token) || typeAttribute;\n\n        // For script and style tags that have a type attribute, only enable custom beautifiers for matching values\n        // For those without a type attribute use default;\n        if (typeAttribute.search('text/css') > -1) {\n          result = 'css';\n        } else if (typeAttribute.search(/module|((text|application|dojo)\\/(x-)?(javascript|ecmascript|jscript|livescript|(ld\\+)?json|method|aspect))/) > -1) {\n          result = 'javascript';\n        } else if (typeAttribute.search(/(text|application|dojo)\\/(x-)?(html)/) > -1) {\n          result = 'html';\n        } else if (typeAttribute.search(/test\\/null/) > -1) {\n          // Test only mime-type for testing the beautifier when null is passed as beautifing function\n          result = 'null';\n        }\n        return result;\n      };\n      function in_array(what, arr) {\n        return arr.indexOf(what) !== -1;\n      }\n      function TagFrame(parent, parser_token, indent_level) {\n        this.parent = parent || null;\n        this.tag = parser_token ? parser_token.tag_name : '';\n        this.indent_level = indent_level || 0;\n        this.parser_token = parser_token || null;\n      }\n      function TagStack(printer) {\n        this._printer = printer;\n        this._current_frame = null;\n      }\n      TagStack.prototype.get_parser_token = function () {\n        return this._current_frame ? this._current_frame.parser_token : null;\n      };\n      TagStack.prototype.record_tag = function (parser_token) {\n        //function to record a tag and its parent in this.tags Object\n        var new_frame = new TagFrame(this._current_frame, parser_token, this._printer.indent_level);\n        this._current_frame = new_frame;\n      };\n      TagStack.prototype._try_pop_frame = function (frame) {\n        //function to retrieve the opening tag to the corresponding closer\n        var parser_token = null;\n        if (frame) {\n          parser_token = frame.parser_token;\n          this._printer.indent_level = frame.indent_level;\n          this._current_frame = frame.parent;\n        }\n        return parser_token;\n      };\n      TagStack.prototype._get_frame = function (tag_list, stop_list) {\n        //function to retrieve the opening tag to the corresponding closer\n        var frame = this._current_frame;\n        while (frame) {\n          //till we reach '' (the initial value);\n          if (tag_list.indexOf(frame.tag) !== -1) {\n            //if this is it use it\n            break;\n          } else if (stop_list && stop_list.indexOf(frame.tag) !== -1) {\n            frame = null;\n            break;\n          }\n          frame = frame.parent;\n        }\n        return frame;\n      };\n      TagStack.prototype.try_pop = function (tag, stop_list) {\n        //function to retrieve the opening tag to the corresponding closer\n        var frame = this._get_frame([tag], stop_list);\n        return this._try_pop_frame(frame);\n      };\n      TagStack.prototype.indent_to_tag = function (tag_list) {\n        var frame = this._get_frame(tag_list);\n        if (frame) {\n          this._printer.indent_level = frame.indent_level;\n        }\n      };\n      function Beautifier(source_text, options, js_beautify, css_beautify) {\n        //Wrapper function to invoke all the necessary constructors and deal with the output.\n        this._source_text = source_text || '';\n        options = options || {};\n        this._js_beautify = js_beautify;\n        this._css_beautify = css_beautify;\n        this._tag_stack = null;\n\n        // Allow the setting of language/file-type specific options\n        // with inheritance of overall settings\n        var optionHtml = new Options(options, 'html');\n        this._options = optionHtml;\n        this._is_wrap_attributes_force = this._options.wrap_attributes.substr(0, 'force'.length) === 'force';\n        this._is_wrap_attributes_force_expand_multiline = this._options.wrap_attributes === 'force-expand-multiline';\n        this._is_wrap_attributes_force_aligned = this._options.wrap_attributes === 'force-aligned';\n        this._is_wrap_attributes_aligned_multiple = this._options.wrap_attributes === 'aligned-multiple';\n        this._is_wrap_attributes_preserve = this._options.wrap_attributes.substr(0, 'preserve'.length) === 'preserve';\n        this._is_wrap_attributes_preserve_aligned = this._options.wrap_attributes === 'preserve-aligned';\n      }\n      Beautifier.prototype.beautify = function () {\n        // if disabled, return the input unchanged.\n        if (this._options.disabled) {\n          return this._source_text;\n        }\n        var source_text = this._source_text;\n        var eol = this._options.eol;\n        if (this._options.eol === 'auto') {\n          eol = '\\n';\n          if (source_text && lineBreak.test(source_text)) {\n            eol = source_text.match(lineBreak)[0];\n          }\n        }\n\n        // HACK: newline parsing inconsistent. This brute force normalizes the input.\n        source_text = source_text.replace(allLineBreaks, '\\n');\n        var baseIndentString = source_text.match(/^[\\t ]*/)[0];\n        var last_token = {\n          text: '',\n          type: ''\n        };\n        var last_tag_token = new TagOpenParserToken(this._options);\n        var printer = new Printer(this._options, baseIndentString);\n        var tokens = new Tokenizer(source_text, this._options).tokenize();\n        this._tag_stack = new TagStack(printer);\n        var parser_token = null;\n        var raw_token = tokens.next();\n        while (raw_token.type !== TOKEN.EOF) {\n          if (raw_token.type === TOKEN.TAG_OPEN || raw_token.type === TOKEN.COMMENT) {\n            parser_token = this._handle_tag_open(printer, raw_token, last_tag_token, last_token, tokens);\n            last_tag_token = parser_token;\n          } else if (raw_token.type === TOKEN.ATTRIBUTE || raw_token.type === TOKEN.EQUALS || raw_token.type === TOKEN.VALUE || raw_token.type === TOKEN.TEXT && !last_tag_token.tag_complete) {\n            parser_token = this._handle_inside_tag(printer, raw_token, last_tag_token, last_token);\n          } else if (raw_token.type === TOKEN.TAG_CLOSE) {\n            parser_token = this._handle_tag_close(printer, raw_token, last_tag_token);\n          } else if (raw_token.type === TOKEN.TEXT) {\n            parser_token = this._handle_text(printer, raw_token, last_tag_token);\n          } else if (raw_token.type === TOKEN.CONTROL_FLOW_OPEN) {\n            parser_token = this._handle_control_flow_open(printer, raw_token);\n          } else if (raw_token.type === TOKEN.CONTROL_FLOW_CLOSE) {\n            parser_token = this._handle_control_flow_close(printer, raw_token);\n          } else {\n            // This should never happen, but if it does. Print the raw token\n            printer.add_raw_token(raw_token);\n          }\n          last_token = parser_token;\n          raw_token = tokens.next();\n        }\n        var sweet_code = printer._output.get_code(eol);\n        return sweet_code;\n      };\n      Beautifier.prototype._handle_control_flow_open = function (printer, raw_token) {\n        var parser_token = {\n          text: raw_token.text,\n          type: raw_token.type\n        };\n        printer.set_space_before_token(raw_token.newlines || raw_token.whitespace_before !== '', true);\n        if (raw_token.newlines) {\n          printer.print_preserved_newlines(raw_token);\n        } else {\n          printer.set_space_before_token(raw_token.newlines || raw_token.whitespace_before !== '', true);\n        }\n        printer.print_token(raw_token);\n        printer.indent();\n        return parser_token;\n      };\n      Beautifier.prototype._handle_control_flow_close = function (printer, raw_token) {\n        var parser_token = {\n          text: raw_token.text,\n          type: raw_token.type\n        };\n        printer.deindent();\n        if (raw_token.newlines) {\n          printer.print_preserved_newlines(raw_token);\n        } else {\n          printer.set_space_before_token(raw_token.newlines || raw_token.whitespace_before !== '', true);\n        }\n        printer.print_token(raw_token);\n        return parser_token;\n      };\n      Beautifier.prototype._handle_tag_close = function (printer, raw_token, last_tag_token) {\n        var parser_token = {\n          text: raw_token.text,\n          type: raw_token.type\n        };\n        printer.alignment_size = 0;\n        last_tag_token.tag_complete = true;\n        printer.set_space_before_token(raw_token.newlines || raw_token.whitespace_before !== '', true);\n        if (last_tag_token.is_unformatted) {\n          printer.add_raw_token(raw_token);\n        } else {\n          if (last_tag_token.tag_start_char === '<') {\n            printer.set_space_before_token(raw_token.text[0] === '/', true); // space before />, no space before >\n            if (this._is_wrap_attributes_force_expand_multiline && last_tag_token.has_wrapped_attrs) {\n              printer.print_newline(false);\n            }\n          }\n          printer.print_token(raw_token);\n        }\n        if (last_tag_token.indent_content && !(last_tag_token.is_unformatted || last_tag_token.is_content_unformatted)) {\n          printer.indent();\n\n          // only indent once per opened tag\n          last_tag_token.indent_content = false;\n        }\n        if (!last_tag_token.is_inline_element && !(last_tag_token.is_unformatted || last_tag_token.is_content_unformatted)) {\n          printer.set_wrap_point();\n        }\n        return parser_token;\n      };\n      Beautifier.prototype._handle_inside_tag = function (printer, raw_token, last_tag_token, last_token) {\n        var wrapped = last_tag_token.has_wrapped_attrs;\n        var parser_token = {\n          text: raw_token.text,\n          type: raw_token.type\n        };\n        printer.set_space_before_token(raw_token.newlines || raw_token.whitespace_before !== '', true);\n        if (last_tag_token.is_unformatted) {\n          printer.add_raw_token(raw_token);\n        } else if (last_tag_token.tag_start_char === '{' && raw_token.type === TOKEN.TEXT) {\n          // For the insides of handlebars allow newlines or a single space between open and contents\n          if (printer.print_preserved_newlines(raw_token)) {\n            raw_token.newlines = 0;\n            printer.add_raw_token(raw_token);\n          } else {\n            printer.print_token(raw_token);\n          }\n        } else {\n          if (raw_token.type === TOKEN.ATTRIBUTE) {\n            printer.set_space_before_token(true);\n          } else if (raw_token.type === TOKEN.EQUALS) {\n            //no space before =\n            printer.set_space_before_token(false);\n          } else if (raw_token.type === TOKEN.VALUE && raw_token.previous.type === TOKEN.EQUALS) {\n            //no space before value\n            printer.set_space_before_token(false);\n          }\n          if (raw_token.type === TOKEN.ATTRIBUTE && last_tag_token.tag_start_char === '<') {\n            if (this._is_wrap_attributes_preserve || this._is_wrap_attributes_preserve_aligned) {\n              printer.traverse_whitespace(raw_token);\n              wrapped = wrapped || raw_token.newlines !== 0;\n            }\n\n            // Wrap for 'force' options, and if the number of attributes is at least that specified in 'wrap_attributes_min_attrs':\n            // 1. always wrap the second and beyond attributes\n            // 2. wrap the first attribute only if 'force-expand-multiline' is specified\n            if (this._is_wrap_attributes_force && last_tag_token.attr_count >= this._options.wrap_attributes_min_attrs && (last_token.type !== TOKEN.TAG_OPEN ||\n            // ie. second attribute and beyond\n            this._is_wrap_attributes_force_expand_multiline)) {\n              printer.print_newline(false);\n              wrapped = true;\n            }\n          }\n          printer.print_token(raw_token);\n          wrapped = wrapped || printer.previous_token_wrapped();\n          last_tag_token.has_wrapped_attrs = wrapped;\n        }\n        return parser_token;\n      };\n      Beautifier.prototype._handle_text = function (printer, raw_token, last_tag_token) {\n        var parser_token = {\n          text: raw_token.text,\n          type: 'TK_CONTENT'\n        };\n        if (last_tag_token.custom_beautifier_name) {\n          //check if we need to format javascript\n          this._print_custom_beatifier_text(printer, raw_token, last_tag_token);\n        } else if (last_tag_token.is_unformatted || last_tag_token.is_content_unformatted) {\n          printer.add_raw_token(raw_token);\n        } else {\n          printer.traverse_whitespace(raw_token);\n          printer.print_token(raw_token);\n        }\n        return parser_token;\n      };\n      Beautifier.prototype._print_custom_beatifier_text = function (printer, raw_token, last_tag_token) {\n        var local = this;\n        if (raw_token.text !== '') {\n          var text = raw_token.text,\n            _beautifier,\n            script_indent_level = 1,\n            pre = '',\n            post = '';\n          if (last_tag_token.custom_beautifier_name === 'javascript' && typeof this._js_beautify === 'function') {\n            _beautifier = this._js_beautify;\n          } else if (last_tag_token.custom_beautifier_name === 'css' && typeof this._css_beautify === 'function') {\n            _beautifier = this._css_beautify;\n          } else if (last_tag_token.custom_beautifier_name === 'html') {\n            _beautifier = function (html_source, options) {\n              var beautifier = new Beautifier(html_source, options, local._js_beautify, local._css_beautify);\n              return beautifier.beautify();\n            };\n          }\n          if (this._options.indent_scripts === \"keep\") {\n            script_indent_level = 0;\n          } else if (this._options.indent_scripts === \"separate\") {\n            script_indent_level = -printer.indent_level;\n          }\n          var indentation = printer.get_full_indent(script_indent_level);\n\n          // if there is at least one empty line at the end of this text, strip it\n          // we'll be adding one back after the text but before the containing tag.\n          text = text.replace(/\\n[ \\t]*$/, '');\n\n          // Handle the case where content is wrapped in a comment or cdata.\n          if (last_tag_token.custom_beautifier_name !== 'html' && text[0] === '<' && text.match(/^(<!--|<!\\[CDATA\\[)/)) {\n            var matched = /^(<!--[^\\n]*|<!\\[CDATA\\[)(\\n?)([ \\t\\n]*)([\\s\\S]*)(-->|]]>)$/.exec(text);\n\n            // if we start to wrap but don't finish, print raw\n            if (!matched) {\n              printer.add_raw_token(raw_token);\n              return;\n            }\n            pre = indentation + matched[1] + '\\n';\n            text = matched[4];\n            if (matched[5]) {\n              post = indentation + matched[5];\n            }\n\n            // if there is at least one empty line at the end of this text, strip it\n            // we'll be adding one back after the text but before the containing tag.\n            text = text.replace(/\\n[ \\t]*$/, '');\n            if (matched[2] || matched[3].indexOf('\\n') !== -1) {\n              // if the first line of the non-comment text has spaces\n              // use that as the basis for indenting in null case.\n              matched = matched[3].match(/[ \\t]+$/);\n              if (matched) {\n                raw_token.whitespace_before = matched[0];\n              }\n            }\n          }\n          if (text) {\n            if (_beautifier) {\n              // call the Beautifier if avaliable\n              var Child_options = function () {\n                this.eol = '\\n';\n              };\n              Child_options.prototype = this._options.raw_options;\n              var child_options = new Child_options();\n              text = _beautifier(indentation + text, child_options);\n            } else {\n              // simply indent the string otherwise\n              var white = raw_token.whitespace_before;\n              if (white) {\n                text = text.replace(new RegExp('\\n(' + white + ')?', 'g'), '\\n');\n              }\n              text = indentation + text.replace(/\\n/g, '\\n' + indentation);\n            }\n          }\n          if (pre) {\n            if (!text) {\n              text = pre + post;\n            } else {\n              text = pre + text + '\\n' + post;\n            }\n          }\n          printer.print_newline(false);\n          if (text) {\n            raw_token.text = text;\n            raw_token.whitespace_before = '';\n            raw_token.newlines = 0;\n            printer.add_raw_token(raw_token);\n            printer.print_newline(true);\n          }\n        }\n      };\n      Beautifier.prototype._handle_tag_open = function (printer, raw_token, last_tag_token, last_token, tokens) {\n        var parser_token = this._get_tag_open_token(raw_token);\n        if ((last_tag_token.is_unformatted || last_tag_token.is_content_unformatted) && !last_tag_token.is_empty_element && raw_token.type === TOKEN.TAG_OPEN && !parser_token.is_start_tag) {\n          // End element tags for unformatted or content_unformatted elements\n          // are printed raw to keep any newlines inside them exactly the same.\n          printer.add_raw_token(raw_token);\n          parser_token.start_tag_token = this._tag_stack.try_pop(parser_token.tag_name);\n        } else {\n          printer.traverse_whitespace(raw_token);\n          this._set_tag_position(printer, raw_token, parser_token, last_tag_token, last_token);\n          if (!parser_token.is_inline_element) {\n            printer.set_wrap_point();\n          }\n          printer.print_token(raw_token);\n        }\n\n        // count the number of attributes\n        if (parser_token.is_start_tag && this._is_wrap_attributes_force) {\n          var peek_index = 0;\n          var peek_token;\n          do {\n            peek_token = tokens.peek(peek_index);\n            if (peek_token.type === TOKEN.ATTRIBUTE) {\n              parser_token.attr_count += 1;\n            }\n            peek_index += 1;\n          } while (peek_token.type !== TOKEN.EOF && peek_token.type !== TOKEN.TAG_CLOSE);\n        }\n\n        //indent attributes an auto, forced, aligned or forced-align line-wrap\n        if (this._is_wrap_attributes_force_aligned || this._is_wrap_attributes_aligned_multiple || this._is_wrap_attributes_preserve_aligned) {\n          parser_token.alignment_size = raw_token.text.length + 1;\n        }\n        if (!parser_token.tag_complete && !parser_token.is_unformatted) {\n          printer.alignment_size = parser_token.alignment_size;\n        }\n        return parser_token;\n      };\n      var TagOpenParserToken = function (options, parent, raw_token) {\n        this.parent = parent || null;\n        this.text = '';\n        this.type = 'TK_TAG_OPEN';\n        this.tag_name = '';\n        this.is_inline_element = false;\n        this.is_unformatted = false;\n        this.is_content_unformatted = false;\n        this.is_empty_element = false;\n        this.is_start_tag = false;\n        this.is_end_tag = false;\n        this.indent_content = false;\n        this.multiline_content = false;\n        this.custom_beautifier_name = null;\n        this.start_tag_token = null;\n        this.attr_count = 0;\n        this.has_wrapped_attrs = false;\n        this.alignment_size = 0;\n        this.tag_complete = false;\n        this.tag_start_char = '';\n        this.tag_check = '';\n        if (!raw_token) {\n          this.tag_complete = true;\n        } else {\n          var tag_check_match;\n          this.tag_start_char = raw_token.text[0];\n          this.text = raw_token.text;\n          if (this.tag_start_char === '<') {\n            tag_check_match = raw_token.text.match(/^<([^\\s>]*)/);\n            this.tag_check = tag_check_match ? tag_check_match[1] : '';\n          } else {\n            tag_check_match = raw_token.text.match(/^{{~?(?:[\\^]|#\\*?)?([^\\s}]+)/);\n            this.tag_check = tag_check_match ? tag_check_match[1] : '';\n\n            // handle \"{{#> myPartial}}\" or \"{{~#> myPartial}}\"\n            if ((raw_token.text.startsWith('{{#>') || raw_token.text.startsWith('{{~#>')) && this.tag_check[0] === '>') {\n              if (this.tag_check === '>' && raw_token.next !== null) {\n                this.tag_check = raw_token.next.text.split(' ')[0];\n              } else {\n                this.tag_check = raw_token.text.split('>')[1];\n              }\n            }\n          }\n          this.tag_check = this.tag_check.toLowerCase();\n          if (raw_token.type === TOKEN.COMMENT) {\n            this.tag_complete = true;\n          }\n          this.is_start_tag = this.tag_check.charAt(0) !== '/';\n          this.tag_name = !this.is_start_tag ? this.tag_check.substr(1) : this.tag_check;\n          this.is_end_tag = !this.is_start_tag || raw_token.closed && raw_token.closed.text === '/>';\n\n          // if whitespace handler ~ included (i.e. {{~#if true}}), handlebars tags start at pos 3 not pos 2\n          var handlebar_starts = 2;\n          if (this.tag_start_char === '{' && this.text.length >= 3) {\n            if (this.text.charAt(2) === '~') {\n              handlebar_starts = 3;\n            }\n          }\n\n          // handlebars tags that don't start with # or ^ are single_tags, and so also start and end.\n          // if they start with # or ^, they are still considered single tags if indenting of handlebars is set to false\n          this.is_end_tag = this.is_end_tag || this.tag_start_char === '{' && (!options.indent_handlebars || this.text.length < 3 || /[^#\\^]/.test(this.text.charAt(handlebar_starts)));\n        }\n      };\n      Beautifier.prototype._get_tag_open_token = function (raw_token) {\n        //function to get a full tag and parse its type\n        var parser_token = new TagOpenParserToken(this._options, this._tag_stack.get_parser_token(), raw_token);\n        parser_token.alignment_size = this._options.wrap_attributes_indent_size;\n        parser_token.is_end_tag = parser_token.is_end_tag || in_array(parser_token.tag_check, this._options.void_elements);\n        parser_token.is_empty_element = parser_token.tag_complete || parser_token.is_start_tag && parser_token.is_end_tag;\n        parser_token.is_unformatted = !parser_token.tag_complete && in_array(parser_token.tag_check, this._options.unformatted);\n        parser_token.is_content_unformatted = !parser_token.is_empty_element && in_array(parser_token.tag_check, this._options.content_unformatted);\n        parser_token.is_inline_element = in_array(parser_token.tag_name, this._options.inline) || this._options.inline_custom_elements && parser_token.tag_name.includes(\"-\") || parser_token.tag_start_char === '{';\n        return parser_token;\n      };\n      Beautifier.prototype._set_tag_position = function (printer, raw_token, parser_token, last_tag_token, last_token) {\n        if (!parser_token.is_empty_element) {\n          if (parser_token.is_end_tag) {\n            //this tag is a double tag so check for tag-ending\n            parser_token.start_tag_token = this._tag_stack.try_pop(parser_token.tag_name); //remove it and all ancestors\n          } else {\n            // it's a start-tag\n            // check if this tag is starting an element that has optional end element\n            // and do an ending needed\n            if (this._do_optional_end_element(parser_token)) {\n              if (!parser_token.is_inline_element) {\n                printer.print_newline(false);\n              }\n            }\n            this._tag_stack.record_tag(parser_token); //push it on the tag stack\n\n            if ((parser_token.tag_name === 'script' || parser_token.tag_name === 'style') && !(parser_token.is_unformatted || parser_token.is_content_unformatted)) {\n              parser_token.custom_beautifier_name = get_custom_beautifier_name(parser_token.tag_check, raw_token);\n            }\n          }\n        }\n        if (in_array(parser_token.tag_check, this._options.extra_liners)) {\n          //check if this double needs an extra line\n          printer.print_newline(false);\n          if (!printer._output.just_added_blankline()) {\n            printer.print_newline(true);\n          }\n        }\n        if (parser_token.is_empty_element) {\n          //if this tag name is a single tag type (either in the list or has a closing /)\n\n          // if you hit an else case, reset the indent level if you are inside an:\n          // 'if', 'unless', or 'each' block.\n          if (parser_token.tag_start_char === '{' && parser_token.tag_check === 'else') {\n            this._tag_stack.indent_to_tag(['if', 'unless', 'each']);\n            parser_token.indent_content = true;\n            // Don't add a newline if opening {{#if}} tag is on the current line\n            var foundIfOnCurrentLine = printer.current_line_has_match(/{{#if/);\n            if (!foundIfOnCurrentLine) {\n              printer.print_newline(false);\n            }\n          }\n\n          // Don't add a newline before elements that should remain where they are.\n          if (parser_token.tag_name === '!--' && last_token.type === TOKEN.TAG_CLOSE && last_tag_token.is_end_tag && parser_token.text.indexOf('\\n') === -1) {\n            //Do nothing. Leave comments on same line.\n          } else {\n            if (!(parser_token.is_inline_element || parser_token.is_unformatted)) {\n              printer.print_newline(false);\n            }\n            this._calcluate_parent_multiline(printer, parser_token);\n          }\n        } else if (parser_token.is_end_tag) {\n          //this tag is a double tag so check for tag-ending\n          var do_end_expand = false;\n\n          // deciding whether a block is multiline should not be this hard\n          do_end_expand = parser_token.start_tag_token && parser_token.start_tag_token.multiline_content;\n          do_end_expand = do_end_expand || !parser_token.is_inline_element && !(last_tag_token.is_inline_element || last_tag_token.is_unformatted) && !(last_token.type === TOKEN.TAG_CLOSE && parser_token.start_tag_token === last_tag_token) && last_token.type !== 'TK_CONTENT';\n          if (parser_token.is_content_unformatted || parser_token.is_unformatted) {\n            do_end_expand = false;\n          }\n          if (do_end_expand) {\n            printer.print_newline(false);\n          }\n        } else {\n          // it's a start-tag\n          parser_token.indent_content = !parser_token.custom_beautifier_name;\n          if (parser_token.tag_start_char === '<') {\n            if (parser_token.tag_name === 'html') {\n              parser_token.indent_content = this._options.indent_inner_html;\n            } else if (parser_token.tag_name === 'head') {\n              parser_token.indent_content = this._options.indent_head_inner_html;\n            } else if (parser_token.tag_name === 'body') {\n              parser_token.indent_content = this._options.indent_body_inner_html;\n            }\n          }\n          if (!(parser_token.is_inline_element || parser_token.is_unformatted) && (last_token.type !== 'TK_CONTENT' || parser_token.is_content_unformatted)) {\n            printer.print_newline(false);\n          }\n          this._calcluate_parent_multiline(printer, parser_token);\n        }\n      };\n      Beautifier.prototype._calcluate_parent_multiline = function (printer, parser_token) {\n        if (parser_token.parent && printer._output.just_added_newline() && !((parser_token.is_inline_element || parser_token.is_unformatted) && parser_token.parent.is_inline_element)) {\n          parser_token.parent.multiline_content = true;\n        }\n      };\n\n      //To be used for <p> tag special case:\n      var p_closers = ['address', 'article', 'aside', 'blockquote', 'details', 'div', 'dl', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'header', 'hr', 'main', 'menu', 'nav', 'ol', 'p', 'pre', 'section', 'table', 'ul'];\n      var p_parent_excludes = ['a', 'audio', 'del', 'ins', 'map', 'noscript', 'video'];\n      Beautifier.prototype._do_optional_end_element = function (parser_token) {\n        var result = null;\n        // NOTE: cases of \"if there is no more content in the parent element\"\n        // are handled automatically by the beautifier.\n        // It assumes parent or ancestor close tag closes all children.\n        // https://www.w3.org/TR/html5/syntax.html#optional-tags\n        if (parser_token.is_empty_element || !parser_token.is_start_tag || !parser_token.parent) {\n          return;\n        }\n        if (parser_token.tag_name === 'body') {\n          // A head element’s end tag may be omitted if the head element is not immediately followed by a space character or a comment.\n          result = result || this._tag_stack.try_pop('head');\n\n          //} else if (parser_token.tag_name === 'body') {\n          // DONE: A body element’s end tag may be omitted if the body element is not immediately followed by a comment.\n        } else if (parser_token.tag_name === 'li') {\n          // An li element’s end tag may be omitted if the li element is immediately followed by another li element or if there is no more content in the parent element.\n          result = result || this._tag_stack.try_pop('li', ['ol', 'ul', 'menu']);\n        } else if (parser_token.tag_name === 'dd' || parser_token.tag_name === 'dt') {\n          // A dd element’s end tag may be omitted if the dd element is immediately followed by another dd element or a dt element, or if there is no more content in the parent element.\n          // A dt element’s end tag may be omitted if the dt element is immediately followed by another dt element or a dd element.\n          result = result || this._tag_stack.try_pop('dt', ['dl']);\n          result = result || this._tag_stack.try_pop('dd', ['dl']);\n        } else if (parser_token.parent.tag_name === 'p' && p_closers.indexOf(parser_token.tag_name) !== -1) {\n          // IMPORTANT: this else-if works because p_closers has no overlap with any other element we look for in this method\n          // check for the parent element is an HTML element that is not an <a>, <audio>, <del>, <ins>, <map>, <noscript>, or <video> element,  or an autonomous custom element.\n          // To do this right, this needs to be coded as an inclusion of the inverse of the exclusion above.\n          // But to start with (if we ignore \"autonomous custom elements\") the exclusion would be fine.\n          var p_parent = parser_token.parent.parent;\n          if (!p_parent || p_parent_excludes.indexOf(p_parent.tag_name) === -1) {\n            result = result || this._tag_stack.try_pop('p');\n          }\n        } else if (parser_token.tag_name === 'rp' || parser_token.tag_name === 'rt') {\n          // An rt element’s end tag may be omitted if the rt element is immediately followed by an rt or rp element, or if there is no more content in the parent element.\n          // An rp element’s end tag may be omitted if the rp element is immediately followed by an rt or rp element, or if there is no more content in the parent element.\n          result = result || this._tag_stack.try_pop('rt', ['ruby', 'rtc']);\n          result = result || this._tag_stack.try_pop('rp', ['ruby', 'rtc']);\n        } else if (parser_token.tag_name === 'optgroup') {\n          // An optgroup element’s end tag may be omitted if the optgroup element is immediately followed by another optgroup element, or if there is no more content in the parent element.\n          // An option element’s end tag may be omitted if the option element is immediately followed by another option element, or if it is immediately followed by an optgroup element, or if there is no more content in the parent element.\n          result = result || this._tag_stack.try_pop('optgroup', ['select']);\n          //result = result || this._tag_stack.try_pop('option', ['select']);\n        } else if (parser_token.tag_name === 'option') {\n          // An option element’s end tag may be omitted if the option element is immediately followed by another option element, or if it is immediately followed by an optgroup element, or if there is no more content in the parent element.\n          result = result || this._tag_stack.try_pop('option', ['select', 'datalist', 'optgroup']);\n        } else if (parser_token.tag_name === 'colgroup') {\n          // DONE: A colgroup element’s end tag may be omitted if the colgroup element is not immediately followed by a space character or a comment.\n          // A caption element's end tag may be ommitted if a colgroup, thead, tfoot, tbody, or tr element is started.\n          result = result || this._tag_stack.try_pop('caption', ['table']);\n        } else if (parser_token.tag_name === 'thead') {\n          // A colgroup element's end tag may be ommitted if a thead, tfoot, tbody, or tr element is started.\n          // A caption element's end tag may be ommitted if a colgroup, thead, tfoot, tbody, or tr element is started.\n          result = result || this._tag_stack.try_pop('caption', ['table']);\n          result = result || this._tag_stack.try_pop('colgroup', ['table']);\n\n          //} else if (parser_token.tag_name === 'caption') {\n          // DONE: A caption element’s end tag may be omitted if the caption element is not immediately followed by a space character or a comment.\n        } else if (parser_token.tag_name === 'tbody' || parser_token.tag_name === 'tfoot') {\n          // A thead element’s end tag may be omitted if the thead element is immediately followed by a tbody or tfoot element.\n          // A tbody element’s end tag may be omitted if the tbody element is immediately followed by a tbody or tfoot element, or if there is no more content in the parent element.\n          // A colgroup element's end tag may be ommitted if a thead, tfoot, tbody, or tr element is started.\n          // A caption element's end tag may be ommitted if a colgroup, thead, tfoot, tbody, or tr element is started.\n          result = result || this._tag_stack.try_pop('caption', ['table']);\n          result = result || this._tag_stack.try_pop('colgroup', ['table']);\n          result = result || this._tag_stack.try_pop('thead', ['table']);\n          result = result || this._tag_stack.try_pop('tbody', ['table']);\n\n          //} else if (parser_token.tag_name === 'tfoot') {\n          // DONE: A tfoot element’s end tag may be omitted if there is no more content in the parent element.\n        } else if (parser_token.tag_name === 'tr') {\n          // A tr element’s end tag may be omitted if the tr element is immediately followed by another tr element, or if there is no more content in the parent element.\n          // A colgroup element's end tag may be ommitted if a thead, tfoot, tbody, or tr element is started.\n          // A caption element's end tag may be ommitted if a colgroup, thead, tfoot, tbody, or tr element is started.\n          result = result || this._tag_stack.try_pop('caption', ['table']);\n          result = result || this._tag_stack.try_pop('colgroup', ['table']);\n          result = result || this._tag_stack.try_pop('tr', ['table', 'thead', 'tbody', 'tfoot']);\n        } else if (parser_token.tag_name === 'th' || parser_token.tag_name === 'td') {\n          // A td element’s end tag may be omitted if the td element is immediately followed by a td or th element, or if there is no more content in the parent element.\n          // A th element’s end tag may be omitted if the th element is immediately followed by a td or th element, or if there is no more content in the parent element.\n          result = result || this._tag_stack.try_pop('td', ['table', 'thead', 'tbody', 'tfoot', 'tr']);\n          result = result || this._tag_stack.try_pop('th', ['table', 'thead', 'tbody', 'tfoot', 'tr']);\n        }\n\n        // Start element omission not handled currently\n        // A head element’s start tag may be omitted if the element is empty, or if the first thing inside the head element is an element.\n        // A tbody element’s start tag may be omitted if the first thing inside the tbody element is a tr element, and if the element is not immediately preceded by a tbody, thead, or tfoot element whose end tag has been omitted. (It can’t be omitted if the element is empty.)\n        // A colgroup element’s start tag may be omitted if the first thing inside the colgroup element is a col element, and if the element is not immediately preceded by another colgroup element whose end tag has been omitted. (It can’t be omitted if the element is empty.)\n\n        // Fix up the parent of the parser token\n        parser_token.parent = this._tag_stack.get_parser_token();\n        return result;\n      };\n      module.exports.Beautifier = Beautifier;\n\n      /***/\n    }), (/* 20 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var BaseOptions = __webpack_require__(6).Options;\n      function Options(options) {\n        BaseOptions.call(this, options, 'html');\n        if (this.templating.length === 1 && this.templating[0] === 'auto') {\n          this.templating = ['django', 'erb', 'handlebars', 'php'];\n        }\n        this.indent_inner_html = this._get_boolean('indent_inner_html');\n        this.indent_body_inner_html = this._get_boolean('indent_body_inner_html', true);\n        this.indent_head_inner_html = this._get_boolean('indent_head_inner_html', true);\n        this.indent_handlebars = this._get_boolean('indent_handlebars', true);\n        this.wrap_attributes = this._get_selection('wrap_attributes', ['auto', 'force', 'force-aligned', 'force-expand-multiline', 'aligned-multiple', 'preserve', 'preserve-aligned']);\n        this.wrap_attributes_min_attrs = this._get_number('wrap_attributes_min_attrs', 2);\n        this.wrap_attributes_indent_size = this._get_number('wrap_attributes_indent_size', this.indent_size);\n        this.extra_liners = this._get_array('extra_liners', ['head', 'body', '/html']);\n\n        // Block vs inline elements\n        // https://developer.mozilla.org/en-US/docs/Web/HTML/Block-level_elements\n        // https://developer.mozilla.org/en-US/docs/Web/HTML/Inline_elements\n        // https://www.w3.org/TR/html5/dom.html#phrasing-content\n        this.inline = this._get_array('inline', ['a', 'abbr', 'area', 'audio', 'b', 'bdi', 'bdo', 'br', 'button', 'canvas', 'cite', 'code', 'data', 'datalist', 'del', 'dfn', 'em', 'embed', 'i', 'iframe', 'img', 'input', 'ins', 'kbd', 'keygen', 'label', 'map', 'mark', 'math', 'meter', 'noscript', 'object', 'output', 'progress', 'q', 'ruby', 's', 'samp', /* 'script', */'select', 'small', 'span', 'strong', 'sub', 'sup', 'svg', 'template', 'textarea', 'time', 'u', 'var', 'video', 'wbr', 'text',\n        // obsolete inline tags\n        'acronym', 'big', 'strike', 'tt']);\n        this.inline_custom_elements = this._get_boolean('inline_custom_elements', true);\n        this.void_elements = this._get_array('void_elements', [\n        // HTLM void elements - aka self-closing tags - aka singletons\n        // https://www.w3.org/html/wg/drafts/html/master/syntax.html#void-elements\n        'area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input', 'keygen', 'link', 'menuitem', 'meta', 'param', 'source', 'track', 'wbr',\n        // NOTE: Optional tags are too complex for a simple list\n        // they are hard coded in _do_optional_end_element\n\n        // Doctype and xml elements\n        '!doctype', '?xml',\n        // obsolete tags\n        // basefont: https://www.computerhope.com/jargon/h/html-basefont-tag.htm\n        // isndex: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/isindex\n        'basefont', 'isindex']);\n        this.unformatted = this._get_array('unformatted', []);\n        this.content_unformatted = this._get_array('content_unformatted', ['pre', 'textarea']);\n        this.unformatted_content_delimiter = this._get_characters('unformatted_content_delimiter');\n        this.indent_scripts = this._get_selection('indent_scripts', ['normal', 'keep', 'separate']);\n      }\n      Options.prototype = new BaseOptions();\n      module.exports.Options = Options;\n\n      /***/\n    }), (/* 21 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var BaseTokenizer = __webpack_require__(9).Tokenizer;\n      var BASETOKEN = __webpack_require__(9).TOKEN;\n      var Directives = __webpack_require__(13).Directives;\n      var TemplatablePattern = __webpack_require__(14).TemplatablePattern;\n      var Pattern = __webpack_require__(12).Pattern;\n      var TOKEN = {\n        TAG_OPEN: 'TK_TAG_OPEN',\n        TAG_CLOSE: 'TK_TAG_CLOSE',\n        CONTROL_FLOW_OPEN: 'TK_CONTROL_FLOW_OPEN',\n        CONTROL_FLOW_CLOSE: 'TK_CONTROL_FLOW_CLOSE',\n        ATTRIBUTE: 'TK_ATTRIBUTE',\n        EQUALS: 'TK_EQUALS',\n        VALUE: 'TK_VALUE',\n        COMMENT: 'TK_COMMENT',\n        TEXT: 'TK_TEXT',\n        UNKNOWN: 'TK_UNKNOWN',\n        START: BASETOKEN.START,\n        RAW: BASETOKEN.RAW,\n        EOF: BASETOKEN.EOF\n      };\n      var directives_core = new Directives(/<\\!--/, /-->/);\n      var Tokenizer = function (input_string, options) {\n        BaseTokenizer.call(this, input_string, options);\n        this._current_tag_name = '';\n\n        // Words end at whitespace or when a tag starts\n        // if we are indenting handlebars, they are considered tags\n        var templatable_reader = new TemplatablePattern(this._input).read_options(this._options);\n        var pattern_reader = new Pattern(this._input);\n        this.__patterns = {\n          word: templatable_reader.until(/[\\n\\r\\t <]/),\n          word_control_flow_close_excluded: templatable_reader.until(/[\\n\\r\\t <}]/),\n          single_quote: templatable_reader.until_after(/'/),\n          double_quote: templatable_reader.until_after(/\"/),\n          attribute: templatable_reader.until(/[\\n\\r\\t =>]|\\/>/),\n          element_name: templatable_reader.until(/[\\n\\r\\t >\\/]/),\n          angular_control_flow_start: pattern_reader.matching(/\\@[a-zA-Z]+[^({]*[({]/),\n          handlebars_comment: pattern_reader.starting_with(/{{!--/).until_after(/--}}/),\n          handlebars: pattern_reader.starting_with(/{{/).until_after(/}}/),\n          handlebars_open: pattern_reader.until(/[\\n\\r\\t }]/),\n          handlebars_raw_close: pattern_reader.until(/}}/),\n          comment: pattern_reader.starting_with(/<!--/).until_after(/-->/),\n          cdata: pattern_reader.starting_with(/<!\\[CDATA\\[/).until_after(/]]>/),\n          // https://en.wikipedia.org/wiki/Conditional_comment\n          conditional_comment: pattern_reader.starting_with(/<!\\[/).until_after(/]>/),\n          processing: pattern_reader.starting_with(/<\\?/).until_after(/\\?>/)\n        };\n        if (this._options.indent_handlebars) {\n          this.__patterns.word = this.__patterns.word.exclude('handlebars');\n          this.__patterns.word_control_flow_close_excluded = this.__patterns.word_control_flow_close_excluded.exclude('handlebars');\n        }\n        this._unformatted_content_delimiter = null;\n        if (this._options.unformatted_content_delimiter) {\n          var literal_regexp = this._input.get_literal_regexp(this._options.unformatted_content_delimiter);\n          this.__patterns.unformatted_content_delimiter = pattern_reader.matching(literal_regexp).until_after(literal_regexp);\n        }\n      };\n      Tokenizer.prototype = new BaseTokenizer();\n      Tokenizer.prototype._is_comment = function (current_token) {\n        // jshint unused:false\n        return false; //current_token.type === TOKEN.COMMENT || current_token.type === TOKEN.UNKNOWN;\n      };\n      Tokenizer.prototype._is_opening = function (current_token) {\n        return current_token.type === TOKEN.TAG_OPEN || current_token.type === TOKEN.CONTROL_FLOW_OPEN;\n      };\n      Tokenizer.prototype._is_closing = function (current_token, open_token) {\n        return current_token.type === TOKEN.TAG_CLOSE && open_token && ((current_token.text === '>' || current_token.text === '/>') && open_token.text[0] === '<' || current_token.text === '}}' && open_token.text[0] === '{' && open_token.text[1] === '{') || current_token.type === TOKEN.CONTROL_FLOW_CLOSE && current_token.text === '}' && open_token.text.endsWith('{');\n      };\n      Tokenizer.prototype._reset = function () {\n        this._current_tag_name = '';\n      };\n      Tokenizer.prototype._get_next_token = function (previous_token, open_token) {\n        // jshint unused:false\n        var token = null;\n        this._readWhitespace();\n        var c = this._input.peek();\n        if (c === null) {\n          return this._create_token(TOKEN.EOF, '');\n        }\n        token = token || this._read_open_handlebars(c, open_token);\n        token = token || this._read_attribute(c, previous_token, open_token);\n        token = token || this._read_close(c, open_token);\n        token = token || this._read_script_and_style(c, previous_token);\n        token = token || this._read_control_flows(c, open_token);\n        token = token || this._read_raw_content(c, previous_token, open_token);\n        token = token || this._read_content_word(c, open_token);\n        token = token || this._read_comment_or_cdata(c);\n        token = token || this._read_processing(c);\n        token = token || this._read_open(c, open_token);\n        token = token || this._create_token(TOKEN.UNKNOWN, this._input.next());\n        return token;\n      };\n      Tokenizer.prototype._read_comment_or_cdata = function (c) {\n        // jshint unused:false\n        var token = null;\n        var resulting_string = null;\n        var directives = null;\n        if (c === '<') {\n          var peek1 = this._input.peek(1);\n          // We treat all comments as literals, even more than preformatted tags\n          // we only look for the appropriate closing marker\n          if (peek1 === '!') {\n            resulting_string = this.__patterns.comment.read();\n\n            // only process directive on html comments\n            if (resulting_string) {\n              directives = directives_core.get_directives(resulting_string);\n              if (directives && directives.ignore === 'start') {\n                resulting_string += directives_core.readIgnored(this._input);\n              }\n            } else {\n              resulting_string = this.__patterns.cdata.read();\n            }\n          }\n          if (resulting_string) {\n            token = this._create_token(TOKEN.COMMENT, resulting_string);\n            token.directives = directives;\n          }\n        }\n        return token;\n      };\n      Tokenizer.prototype._read_processing = function (c) {\n        // jshint unused:false\n        var token = null;\n        var resulting_string = null;\n        var directives = null;\n        if (c === '<') {\n          var peek1 = this._input.peek(1);\n          if (peek1 === '!' || peek1 === '?') {\n            resulting_string = this.__patterns.conditional_comment.read();\n            resulting_string = resulting_string || this.__patterns.processing.read();\n          }\n          if (resulting_string) {\n            token = this._create_token(TOKEN.COMMENT, resulting_string);\n            token.directives = directives;\n          }\n        }\n        return token;\n      };\n      Tokenizer.prototype._read_open = function (c, open_token) {\n        var resulting_string = null;\n        var token = null;\n        if (!open_token || open_token.type === TOKEN.CONTROL_FLOW_OPEN) {\n          if (c === '<') {\n            resulting_string = this._input.next();\n            if (this._input.peek() === '/') {\n              resulting_string += this._input.next();\n            }\n            resulting_string += this.__patterns.element_name.read();\n            token = this._create_token(TOKEN.TAG_OPEN, resulting_string);\n          }\n        }\n        return token;\n      };\n      Tokenizer.prototype._read_open_handlebars = function (c, open_token) {\n        var resulting_string = null;\n        var token = null;\n        if (!open_token || open_token.type === TOKEN.CONTROL_FLOW_OPEN) {\n          if ((this._options.templating.includes('angular') || this._options.indent_handlebars) && c === '{' && this._input.peek(1) === '{') {\n            if (this._options.indent_handlebars && this._input.peek(2) === '!') {\n              resulting_string = this.__patterns.handlebars_comment.read();\n              resulting_string = resulting_string || this.__patterns.handlebars.read();\n              token = this._create_token(TOKEN.COMMENT, resulting_string);\n            } else {\n              resulting_string = this.__patterns.handlebars_open.read();\n              token = this._create_token(TOKEN.TAG_OPEN, resulting_string);\n            }\n          }\n        }\n        return token;\n      };\n      Tokenizer.prototype._read_control_flows = function (c, open_token) {\n        var resulting_string = '';\n        var token = null;\n        // Only check for control flows if angular templating is set\n        if (!this._options.templating.includes('angular')) {\n          return token;\n        }\n        if (c === '@') {\n          resulting_string = this.__patterns.angular_control_flow_start.read();\n          if (resulting_string === '') {\n            return token;\n          }\n          var opening_parentheses_count = resulting_string.endsWith('(') ? 1 : 0;\n          var closing_parentheses_count = 0;\n          // The opening brace of the control flow is where the number of opening and closing parentheses equal\n          // e.g. @if({value: true} !== null) { \n          while (!(resulting_string.endsWith('{') && opening_parentheses_count === closing_parentheses_count)) {\n            var next_char = this._input.next();\n            if (next_char === null) {\n              break;\n            } else if (next_char === '(') {\n              opening_parentheses_count++;\n            } else if (next_char === ')') {\n              closing_parentheses_count++;\n            }\n            resulting_string += next_char;\n          }\n          token = this._create_token(TOKEN.CONTROL_FLOW_OPEN, resulting_string);\n        } else if (c === '}' && open_token && open_token.type === TOKEN.CONTROL_FLOW_OPEN) {\n          resulting_string = this._input.next();\n          token = this._create_token(TOKEN.CONTROL_FLOW_CLOSE, resulting_string);\n        }\n        return token;\n      };\n      Tokenizer.prototype._read_close = function (c, open_token) {\n        var resulting_string = null;\n        var token = null;\n        if (open_token && open_token.type === TOKEN.TAG_OPEN) {\n          if (open_token.text[0] === '<' && (c === '>' || c === '/' && this._input.peek(1) === '>')) {\n            resulting_string = this._input.next();\n            if (c === '/') {\n              //  for close tag \"/>\"\n              resulting_string += this._input.next();\n            }\n            token = this._create_token(TOKEN.TAG_CLOSE, resulting_string);\n          } else if (open_token.text[0] === '{' && c === '}' && this._input.peek(1) === '}') {\n            this._input.next();\n            this._input.next();\n            token = this._create_token(TOKEN.TAG_CLOSE, '}}');\n          }\n        }\n        return token;\n      };\n      Tokenizer.prototype._read_attribute = function (c, previous_token, open_token) {\n        var token = null;\n        var resulting_string = '';\n        if (open_token && open_token.text[0] === '<') {\n          if (c === '=') {\n            token = this._create_token(TOKEN.EQUALS, this._input.next());\n          } else if (c === '\"' || c === \"'\") {\n            var content = this._input.next();\n            if (c === '\"') {\n              content += this.__patterns.double_quote.read();\n            } else {\n              content += this.__patterns.single_quote.read();\n            }\n            token = this._create_token(TOKEN.VALUE, content);\n          } else {\n            resulting_string = this.__patterns.attribute.read();\n            if (resulting_string) {\n              if (previous_token.type === TOKEN.EQUALS) {\n                token = this._create_token(TOKEN.VALUE, resulting_string);\n              } else {\n                token = this._create_token(TOKEN.ATTRIBUTE, resulting_string);\n              }\n            }\n          }\n        }\n        return token;\n      };\n      Tokenizer.prototype._is_content_unformatted = function (tag_name) {\n        // void_elements have no content and so cannot have unformatted content\n        // script and style tags should always be read as unformatted content\n        // finally content_unformatted and unformatted element contents are unformatted\n        return this._options.void_elements.indexOf(tag_name) === -1 && (this._options.content_unformatted.indexOf(tag_name) !== -1 || this._options.unformatted.indexOf(tag_name) !== -1);\n      };\n      Tokenizer.prototype._read_raw_content = function (c, previous_token, open_token) {\n        // jshint unused:false\n        var resulting_string = '';\n        if (open_token && open_token.text[0] === '{') {\n          resulting_string = this.__patterns.handlebars_raw_close.read();\n        } else if (previous_token.type === TOKEN.TAG_CLOSE && previous_token.opened.text[0] === '<' && previous_token.text[0] !== '/') {\n          // ^^ empty tag has no content \n          var tag_name = previous_token.opened.text.substr(1).toLowerCase();\n          if (this._is_content_unformatted(tag_name)) {\n            resulting_string = this._input.readUntil(new RegExp('</' + tag_name + '[\\\\n\\\\r\\\\t ]*?>', 'ig'));\n          }\n        }\n        if (resulting_string) {\n          return this._create_token(TOKEN.TEXT, resulting_string);\n        }\n        return null;\n      };\n      Tokenizer.prototype._read_script_and_style = function (c, previous_token) {\n        // jshint unused:false \n        if (previous_token.type === TOKEN.TAG_CLOSE && previous_token.opened.text[0] === '<' && previous_token.text[0] !== '/') {\n          var tag_name = previous_token.opened.text.substr(1).toLowerCase();\n          if (tag_name === 'script' || tag_name === 'style') {\n            // Script and style tags are allowed to have comments wrapping their content\n            // or just have regular content.\n            var token = this._read_comment_or_cdata(c);\n            if (token) {\n              token.type = TOKEN.TEXT;\n              return token;\n            }\n            var resulting_string = this._input.readUntil(new RegExp('</' + tag_name + '[\\\\n\\\\r\\\\t ]*?>', 'ig'));\n            if (resulting_string) {\n              return this._create_token(TOKEN.TEXT, resulting_string);\n            }\n          }\n        }\n        return null;\n      };\n      Tokenizer.prototype._read_content_word = function (c, open_token) {\n        var resulting_string = '';\n        if (this._options.unformatted_content_delimiter) {\n          if (c === this._options.unformatted_content_delimiter[0]) {\n            resulting_string = this.__patterns.unformatted_content_delimiter.read();\n          }\n        }\n        if (!resulting_string) {\n          resulting_string = open_token && open_token.type === TOKEN.CONTROL_FLOW_OPEN ? this.__patterns.word_control_flow_close_excluded.read() : this.__patterns.word.read();\n        }\n        if (resulting_string) {\n          return this._create_token(TOKEN.TEXT, resulting_string);\n        }\n        return null;\n      };\n      module.exports.Tokenizer = Tokenizer;\n      module.exports.TOKEN = TOKEN;\n\n      /***/\n    }\n    /******/)];\n    /************************************************************************/\n    /******/ // The module cache\n    /******/\n    var __webpack_module_cache__ = {};\n    /******/\n    /******/ // The require function\n    /******/\n    function __webpack_require__(moduleId) {\n      /******/ // Check if module is in cache\n      /******/var cachedModule = __webpack_module_cache__[moduleId];\n      /******/\n      if (cachedModule !== undefined) {\n        /******/return cachedModule.exports;\n        /******/\n      }\n      /******/ // Create a new module (and put it into the cache)\n      /******/\n      var module = __webpack_module_cache__[moduleId] = {\n        /******/ // no module.id needed\n        /******/ // no module.loaded needed\n        /******/exports: {}\n        /******/\n      };\n      /******/\n      /******/ // Execute the module function\n      /******/\n      __webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n      /******/\n      /******/ // Return the exports of the module\n      /******/\n      return module.exports;\n      /******/\n    }\n    /******/\n    /************************************************************************/\n    /******/\n    /******/ // startup\n    /******/ // Load entry module and return exports\n    /******/ // This entry module is referenced by other modules so it can't be inlined\n    /******/\n    var __webpack_exports__ = __webpack_require__(18);\n    /******/\n    legacy_beautify_html = __webpack_exports__;\n    /******/\n    /******/\n  })();\n  var style_html = legacy_beautify_html;\n  /* Footer */\n  if (typeof define === \"function\" && define.amd) {\n    // Add support for AMD ( https://github.com/amdjs/amdjs-api/wiki/AMD#defineamd-property- )\n    define([\"require\", \"./beautify\", \"./beautify-css\"], function (requireamd) {\n      var js_beautify = requireamd(\"./beautify\");\n      var css_beautify = requireamd(\"./beautify-css\");\n      return {\n        html_beautify: function (html_source, options) {\n          return style_html(html_source, options, js_beautify.js_beautify, css_beautify.css_beautify);\n        }\n      };\n    });\n  } else if (typeof exports !== \"undefined\") {\n    // Add support for CommonJS. Just put this file somewhere on your require.paths\n    // and you will be able to `var html_beautify = require(\"beautify\").html_beautify`.\n    var js_beautify = require('./beautify.js');\n    var css_beautify = require('./beautify-css.js');\n    exports.html_beautify = function (html_source, options) {\n      return style_html(html_source, options, js_beautify.js_beautify, css_beautify.css_beautify);\n    };\n  } else if (typeof window !== \"undefined\") {\n    // If we're running a web page and don't have either of the above, add our one global\n    window.html_beautify = function (html_source, options) {\n      return style_html(html_source, options, window.js_beautify, window.css_beautify);\n    };\n  } else if (typeof global !== \"undefined\") {\n    // If we don't even have window, try global.\n    global.html_beautify = function (html_source, options) {\n      return style_html(html_source, options, global.js_beautify, global.css_beautify);\n    };\n  }\n})();", "map": {"version": 3, "names": ["legacy_beautify_html", "__webpack_modules__", "module", "OutputLine", "parent", "__parent", "__character_count", "__indent_count", "__alignment_count", "__wrap_point_index", "__wrap_point_character_count", "__wrap_point_indent_count", "__wrap_point_alignment_count", "__items", "prototype", "clone_empty", "line", "set_indent", "item", "index", "length", "has_match", "pattern", "lastCheckedOutput", "match", "indent", "alignment", "is_empty", "get_indent_size", "_set_wrap_point", "wrap_line_length", "next_line", "_should_wrap", "_allow_wrap", "add_new_line", "next", "current_line", "slice", "splice", "last", "push", "last_newline_index", "lastIndexOf", "pop", "_remove_indent", "indent_size", "_remove_wrap_indent", "trim", "toString", "result", "indent_empty_lines", "get_indent_string", "join", "IndentStringCache", "options", "baseIndentString", "__cache", "__indent_size", "__indent_string", "indent_char", "indent_with_tabs", "Array", "indent_level", "__base_string", "__base_string_length", "column", "__ensure_cache", "__add_column", "Math", "floor", "Output", "__indent_cache", "raw", "_end_with_newline", "end_with_newline", "__lines", "previous_line", "space_before_token", "non_breaking_space", "previous_token_wrapped", "__add_outputline", "get_line_number", "force_newline", "just_added_newline", "get_code", "eol", "last_item", "replace", "sweet_code", "set_wrap_point", "add_raw_token", "token", "x", "newlines", "whitespace_before", "text", "add_token", "printable_token", "__add_space_before_token", "remove_indent", "output_length", "eat_newlines", "undefined", "just_added_blankline", "ensure_empty_line_above", "starts_with", "ends_with", "potentialEmptyLine", "indexOf", "exports", "Token", "type", "comments_before", "previous", "opened", "closed", "directives", "Options", "merge_child_field", "raw_options", "_mergeOpts", "disabled", "_get_boolean", "_get_characters", "_get_number", "preserve_newlines", "max_preserve_newlines", "templating", "_get_selection_list", "_get_array", "name", "default_value", "option_value", "concat", "split", "parseInt", "isNaN", "_get_selection", "selection_list", "Error", "_is_valid_selection", "some", "allOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalOpts", "_normalizeOpts", "convertedOpts", "key", "new<PERSON>ey", "normalizeOpts", "mergeOpts", "regexp_has_sticky", "RegExp", "hasOwnProperty", "InputScanner", "input_string", "__input", "__input_length", "__position", "restart", "back", "hasNext", "val", "char<PERSON>t", "peek", "__match", "lastIndex", "pattern_match", "exec", "sticky", "test", "testChar", "read", "starting_pattern", "until_pattern", "until_after", "readUntil", "match_index", "substring", "readUntilAfter", "get_regexp", "match_from", "flags", "source", "get_literal_regexp", "literal_string", "peekUntilAfter", "start", "lookBack", "testVal", "toLowerCase", "__unused_webpack_exports", "__webpack_require__", "TokenStream", "WhitespacePattern", "TOKEN", "START", "RAW", "EOF", "Tokenizer", "_input", "_options", "__tokens", "_patterns", "whitespace", "tokenize", "_reset", "current", "open_token", "open_stack", "comments", "_get_next_token", "_is_comment", "add", "isEmpty", "_is_opening", "_is_closing", "_is_first_token", "previous_token", "_readWhitespace", "resulting_string", "_create_token", "current_token", "newline_count", "whitespace_before_token", "parent_token", "__tokens_length", "__parent_token", "Pattern", "input_scanner", "call", "_line_regexp", "__set_whitespace_patterns", "whitespace_chars", "newline_chars", "_match_pattern", "_newline_regexp", "matches", "__split", "matching", "_create", "_update", "regexp", "start_index", "next_match", "_starting_pattern", "_until_pattern", "_until_after", "read_match", "until", "starting_with", "Directives", "start_block_pattern", "end_block_pattern", "__directives_block_pattern", "__directive_pattern", "__directives_end_ignore_pattern", "get_directives", "directive_match", "readIgnored", "input", "template_names", "django", "erb", "handlebars", "php", "smarty", "angular", "TemplatablePattern", "__template_pattern", "_disabled", "Object", "assign", "_excluded", "__patterns", "handlebars_comment", "handlebars_unescaped", "django_value", "django_comment", "smarty_comment", "smarty_literal", "__set_templated_pattern", "disable", "language", "read_options", "exclude", "_read_template", "items", "c", "peek1", "Beautifier", "style_html", "html_source", "js_beautify", "css_beautify", "beautifier", "beautify", "defaultOptions", "lineBreak", "allLineBreaks", "Printer", "base_indent_string", "alignment_size", "_output", "current_line_has_match", "set_space_before_token", "value", "non_breaking", "print_preserved_newlines", "raw_token", "TEXT", "n", "print_newline", "traverse_whitespace", "force", "print_token", "deindent", "get_full_indent", "level", "get_type_attribute", "start_token", "ATTRIBUTE", "EQUALS", "VALUE", "get_custom_beautifier_name", "tag_check", "typeAttribute", "search", "in_array", "what", "arr", "TagFrame", "parser_token", "tag", "tag_name", "TagStack", "printer", "_printer", "_current_frame", "get_parser_token", "record_tag", "new_frame", "_try_pop_frame", "frame", "_get_frame", "tag_list", "stop_list", "try_pop", "indent_to_tag", "source_text", "_source_text", "_js_beautify", "_css_beautify", "_tag_stack", "optionHtml", "_is_wrap_attributes_force", "wrap_attributes", "substr", "_is_wrap_attributes_force_expand_multiline", "_is_wrap_attributes_force_aligned", "_is_wrap_attributes_aligned_multiple", "_is_wrap_attributes_preserve", "_is_wrap_attributes_preserve_aligned", "last_token", "last_tag_token", "TagOpenParserToken", "tokens", "TAG_OPEN", "COMMENT", "_handle_tag_open", "tag_complete", "_handle_inside_tag", "TAG_CLOSE", "_handle_tag_close", "_handle_text", "CONTROL_FLOW_OPEN", "_handle_control_flow_open", "CONTROL_FLOW_CLOSE", "_handle_control_flow_close", "is_unformatted", "tag_start_char", "has_wrapped_attrs", "indent_content", "is_content_unformatted", "is_inline_element", "wrapped", "attr_count", "wrap_attributes_min_attrs", "custom_beautifier_name", "_print_custom_beatifier_text", "local", "_beautifier", "script_indent_level", "pre", "post", "indent_scripts", "indentation", "matched", "Child_options", "child_options", "white", "_get_tag_open_token", "is_empty_element", "is_start_tag", "start_tag_token", "_set_tag_position", "peek_index", "peek_token", "is_end_tag", "multiline_content", "tag_check_match", "startsWith", "handlebar_starts", "indent_handlebars", "wrap_attributes_indent_size", "void_elements", "unformatted", "content_unformatted", "inline", "inline_custom_elements", "includes", "_do_optional_end_element", "extra_liners", "foundIfOnCurrentLine", "_calcluate_parent_multiline", "do_end_expand", "indent_inner_html", "indent_head_inner_html", "indent_body_inner_html", "p_closers", "p_parent_excludes", "p_parent", "BaseOptions", "unformatted_content_delimiter", "BaseTokenizer", "BASETOKEN", "UNKNOWN", "directives_core", "_current_tag_name", "templatable_reader", "pattern_reader", "word", "word_control_flow_close_excluded", "single_quote", "double_quote", "attribute", "element_name", "angular_control_flow_start", "handlebars_open", "handlebars_raw_close", "comment", "cdata", "conditional_comment", "processing", "_unformatted_content_delimiter", "literal_regexp", "endsWith", "_read_open_handlebars", "_read_attribute", "_read_close", "_read_script_and_style", "_read_control_flows", "_read_raw_content", "_read_content_word", "_read_comment_or_cdata", "_read_processing", "_read_open", "ignore", "opening_parentheses_count", "closing_parentheses_count", "next_char", "content", "_is_content_unformatted", "__webpack_module_cache__", "moduleId", "cachedModule", "__webpack_exports__", "define", "amd", "requireamd", "html_beautify", "require", "window", "global"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/js-beautify/js/lib/beautify-html.js"], "sourcesContent": ["/* AUTO-GENERATED. DO NOT MODIFY. */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n\n\n Style HTML\n---------------\n\n  Written by Nochum Sossonko, (<EMAIL>)\n\n  Based on code initially developed by: Einar Lielmanis, <<EMAIL>>\n    https://beautifier.io/\n\n  Usage:\n    style_html(html_source);\n\n    style_html(html_source, options);\n\n  The options are:\n    indent_inner_html (default false)  — indent <head> and <body> sections,\n    indent_size (default 4)          — indentation size,\n    indent_char (default space)      — character to indent with,\n    wrap_line_length (default 250)            -  maximum amount of characters per line (0 = disable)\n    brace_style (default \"collapse\") - \"collapse\" | \"expand\" | \"end-expand\" | \"none\"\n            put braces on the same line as control statements (default), or put braces on own line (Allman / ANSI style), or just put end braces on own line, or attempt to keep them where they are.\n    inline (defaults to inline tags) - list of tags to be considered inline tags\n    unformatted (defaults to inline tags) - list of tags, that shouldn't be reformatted\n    content_unformatted (defaults to [\"pre\", \"textarea\"] tags) - list of tags, whose content shouldn't be reformatted\n    indent_scripts (default normal)  - \"keep\"|\"separate\"|\"normal\"\n    preserve_newlines (default true) - whether existing line breaks before elements should be preserved\n                                        Only works before elements, not inside tags or for text.\n    max_preserve_newlines (default unlimited) - maximum number of line breaks to be preserved in one chunk\n    indent_handlebars (default false) - format and indent {{#foo}} and {{/foo}}\n    end_with_newline (false)          - end with a newline\n    extra_liners (default [head,body,/html]) -List of tags that should have an extra newline before them.\n\n    e.g.\n\n    style_html(html_source, {\n      'indent_inner_html': false,\n      'indent_size': 2,\n      'indent_char': ' ',\n      'wrap_line_length': 78,\n      'brace_style': 'expand',\n      'preserve_newlines': true,\n      'max_preserve_newlines': 5,\n      'indent_handlebars': false,\n      'extra_liners': ['/html']\n    });\n*/\n\n(function() {\n\n/* GENERATED_BUILD_OUTPUT */\nvar legacy_beautify_html;\n/******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ([\n/* 0 */,\n/* 1 */,\n/* 2 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nfunction OutputLine(parent) {\n  this.__parent = parent;\n  this.__character_count = 0;\n  // use indent_count as a marker for this.__lines that have preserved indentation\n  this.__indent_count = -1;\n  this.__alignment_count = 0;\n  this.__wrap_point_index = 0;\n  this.__wrap_point_character_count = 0;\n  this.__wrap_point_indent_count = -1;\n  this.__wrap_point_alignment_count = 0;\n\n  this.__items = [];\n}\n\nOutputLine.prototype.clone_empty = function() {\n  var line = new OutputLine(this.__parent);\n  line.set_indent(this.__indent_count, this.__alignment_count);\n  return line;\n};\n\nOutputLine.prototype.item = function(index) {\n  if (index < 0) {\n    return this.__items[this.__items.length + index];\n  } else {\n    return this.__items[index];\n  }\n};\n\nOutputLine.prototype.has_match = function(pattern) {\n  for (var lastCheckedOutput = this.__items.length - 1; lastCheckedOutput >= 0; lastCheckedOutput--) {\n    if (this.__items[lastCheckedOutput].match(pattern)) {\n      return true;\n    }\n  }\n  return false;\n};\n\nOutputLine.prototype.set_indent = function(indent, alignment) {\n  if (this.is_empty()) {\n    this.__indent_count = indent || 0;\n    this.__alignment_count = alignment || 0;\n    this.__character_count = this.__parent.get_indent_size(this.__indent_count, this.__alignment_count);\n  }\n};\n\nOutputLine.prototype._set_wrap_point = function() {\n  if (this.__parent.wrap_line_length) {\n    this.__wrap_point_index = this.__items.length;\n    this.__wrap_point_character_count = this.__character_count;\n    this.__wrap_point_indent_count = this.__parent.next_line.__indent_count;\n    this.__wrap_point_alignment_count = this.__parent.next_line.__alignment_count;\n  }\n};\n\nOutputLine.prototype._should_wrap = function() {\n  return this.__wrap_point_index &&\n    this.__character_count > this.__parent.wrap_line_length &&\n    this.__wrap_point_character_count > this.__parent.next_line.__character_count;\n};\n\nOutputLine.prototype._allow_wrap = function() {\n  if (this._should_wrap()) {\n    this.__parent.add_new_line();\n    var next = this.__parent.current_line;\n    next.set_indent(this.__wrap_point_indent_count, this.__wrap_point_alignment_count);\n    next.__items = this.__items.slice(this.__wrap_point_index);\n    this.__items = this.__items.slice(0, this.__wrap_point_index);\n\n    next.__character_count += this.__character_count - this.__wrap_point_character_count;\n    this.__character_count = this.__wrap_point_character_count;\n\n    if (next.__items[0] === \" \") {\n      next.__items.splice(0, 1);\n      next.__character_count -= 1;\n    }\n    return true;\n  }\n  return false;\n};\n\nOutputLine.prototype.is_empty = function() {\n  return this.__items.length === 0;\n};\n\nOutputLine.prototype.last = function() {\n  if (!this.is_empty()) {\n    return this.__items[this.__items.length - 1];\n  } else {\n    return null;\n  }\n};\n\nOutputLine.prototype.push = function(item) {\n  this.__items.push(item);\n  var last_newline_index = item.lastIndexOf('\\n');\n  if (last_newline_index !== -1) {\n    this.__character_count = item.length - last_newline_index;\n  } else {\n    this.__character_count += item.length;\n  }\n};\n\nOutputLine.prototype.pop = function() {\n  var item = null;\n  if (!this.is_empty()) {\n    item = this.__items.pop();\n    this.__character_count -= item.length;\n  }\n  return item;\n};\n\n\nOutputLine.prototype._remove_indent = function() {\n  if (this.__indent_count > 0) {\n    this.__indent_count -= 1;\n    this.__character_count -= this.__parent.indent_size;\n  }\n};\n\nOutputLine.prototype._remove_wrap_indent = function() {\n  if (this.__wrap_point_indent_count > 0) {\n    this.__wrap_point_indent_count -= 1;\n  }\n};\nOutputLine.prototype.trim = function() {\n  while (this.last() === ' ') {\n    this.__items.pop();\n    this.__character_count -= 1;\n  }\n};\n\nOutputLine.prototype.toString = function() {\n  var result = '';\n  if (this.is_empty()) {\n    if (this.__parent.indent_empty_lines) {\n      result = this.__parent.get_indent_string(this.__indent_count);\n    }\n  } else {\n    result = this.__parent.get_indent_string(this.__indent_count, this.__alignment_count);\n    result += this.__items.join('');\n  }\n  return result;\n};\n\nfunction IndentStringCache(options, baseIndentString) {\n  this.__cache = [''];\n  this.__indent_size = options.indent_size;\n  this.__indent_string = options.indent_char;\n  if (!options.indent_with_tabs) {\n    this.__indent_string = new Array(options.indent_size + 1).join(options.indent_char);\n  }\n\n  // Set to null to continue support for auto detection of base indent\n  baseIndentString = baseIndentString || '';\n  if (options.indent_level > 0) {\n    baseIndentString = new Array(options.indent_level + 1).join(this.__indent_string);\n  }\n\n  this.__base_string = baseIndentString;\n  this.__base_string_length = baseIndentString.length;\n}\n\nIndentStringCache.prototype.get_indent_size = function(indent, column) {\n  var result = this.__base_string_length;\n  column = column || 0;\n  if (indent < 0) {\n    result = 0;\n  }\n  result += indent * this.__indent_size;\n  result += column;\n  return result;\n};\n\nIndentStringCache.prototype.get_indent_string = function(indent_level, column) {\n  var result = this.__base_string;\n  column = column || 0;\n  if (indent_level < 0) {\n    indent_level = 0;\n    result = '';\n  }\n  column += indent_level * this.__indent_size;\n  this.__ensure_cache(column);\n  result += this.__cache[column];\n  return result;\n};\n\nIndentStringCache.prototype.__ensure_cache = function(column) {\n  while (column >= this.__cache.length) {\n    this.__add_column();\n  }\n};\n\nIndentStringCache.prototype.__add_column = function() {\n  var column = this.__cache.length;\n  var indent = 0;\n  var result = '';\n  if (this.__indent_size && column >= this.__indent_size) {\n    indent = Math.floor(column / this.__indent_size);\n    column -= indent * this.__indent_size;\n    result = new Array(indent + 1).join(this.__indent_string);\n  }\n  if (column) {\n    result += new Array(column + 1).join(' ');\n  }\n\n  this.__cache.push(result);\n};\n\nfunction Output(options, baseIndentString) {\n  this.__indent_cache = new IndentStringCache(options, baseIndentString);\n  this.raw = false;\n  this._end_with_newline = options.end_with_newline;\n  this.indent_size = options.indent_size;\n  this.wrap_line_length = options.wrap_line_length;\n  this.indent_empty_lines = options.indent_empty_lines;\n  this.__lines = [];\n  this.previous_line = null;\n  this.current_line = null;\n  this.next_line = new OutputLine(this);\n  this.space_before_token = false;\n  this.non_breaking_space = false;\n  this.previous_token_wrapped = false;\n  // initialize\n  this.__add_outputline();\n}\n\nOutput.prototype.__add_outputline = function() {\n  this.previous_line = this.current_line;\n  this.current_line = this.next_line.clone_empty();\n  this.__lines.push(this.current_line);\n};\n\nOutput.prototype.get_line_number = function() {\n  return this.__lines.length;\n};\n\nOutput.prototype.get_indent_string = function(indent, column) {\n  return this.__indent_cache.get_indent_string(indent, column);\n};\n\nOutput.prototype.get_indent_size = function(indent, column) {\n  return this.__indent_cache.get_indent_size(indent, column);\n};\n\nOutput.prototype.is_empty = function() {\n  return !this.previous_line && this.current_line.is_empty();\n};\n\nOutput.prototype.add_new_line = function(force_newline) {\n  // never newline at the start of file\n  // otherwise, newline only if we didn't just add one or we're forced\n  if (this.is_empty() ||\n    (!force_newline && this.just_added_newline())) {\n    return false;\n  }\n\n  // if raw output is enabled, don't print additional newlines,\n  // but still return True as though you had\n  if (!this.raw) {\n    this.__add_outputline();\n  }\n  return true;\n};\n\nOutput.prototype.get_code = function(eol) {\n  this.trim(true);\n\n  // handle some edge cases where the last tokens\n  // has text that ends with newline(s)\n  var last_item = this.current_line.pop();\n  if (last_item) {\n    if (last_item[last_item.length - 1] === '\\n') {\n      last_item = last_item.replace(/\\n+$/g, '');\n    }\n    this.current_line.push(last_item);\n  }\n\n  if (this._end_with_newline) {\n    this.__add_outputline();\n  }\n\n  var sweet_code = this.__lines.join('\\n');\n\n  if (eol !== '\\n') {\n    sweet_code = sweet_code.replace(/[\\n]/g, eol);\n  }\n  return sweet_code;\n};\n\nOutput.prototype.set_wrap_point = function() {\n  this.current_line._set_wrap_point();\n};\n\nOutput.prototype.set_indent = function(indent, alignment) {\n  indent = indent || 0;\n  alignment = alignment || 0;\n\n  // Next line stores alignment values\n  this.next_line.set_indent(indent, alignment);\n\n  // Never indent your first output indent at the start of the file\n  if (this.__lines.length > 1) {\n    this.current_line.set_indent(indent, alignment);\n    return true;\n  }\n\n  this.current_line.set_indent();\n  return false;\n};\n\nOutput.prototype.add_raw_token = function(token) {\n  for (var x = 0; x < token.newlines; x++) {\n    this.__add_outputline();\n  }\n  this.current_line.set_indent(-1);\n  this.current_line.push(token.whitespace_before);\n  this.current_line.push(token.text);\n  this.space_before_token = false;\n  this.non_breaking_space = false;\n  this.previous_token_wrapped = false;\n};\n\nOutput.prototype.add_token = function(printable_token) {\n  this.__add_space_before_token();\n  this.current_line.push(printable_token);\n  this.space_before_token = false;\n  this.non_breaking_space = false;\n  this.previous_token_wrapped = this.current_line._allow_wrap();\n};\n\nOutput.prototype.__add_space_before_token = function() {\n  if (this.space_before_token && !this.just_added_newline()) {\n    if (!this.non_breaking_space) {\n      this.set_wrap_point();\n    }\n    this.current_line.push(' ');\n  }\n};\n\nOutput.prototype.remove_indent = function(index) {\n  var output_length = this.__lines.length;\n  while (index < output_length) {\n    this.__lines[index]._remove_indent();\n    index++;\n  }\n  this.current_line._remove_wrap_indent();\n};\n\nOutput.prototype.trim = function(eat_newlines) {\n  eat_newlines = (eat_newlines === undefined) ? false : eat_newlines;\n\n  this.current_line.trim();\n\n  while (eat_newlines && this.__lines.length > 1 &&\n    this.current_line.is_empty()) {\n    this.__lines.pop();\n    this.current_line = this.__lines[this.__lines.length - 1];\n    this.current_line.trim();\n  }\n\n  this.previous_line = this.__lines.length > 1 ?\n    this.__lines[this.__lines.length - 2] : null;\n};\n\nOutput.prototype.just_added_newline = function() {\n  return this.current_line.is_empty();\n};\n\nOutput.prototype.just_added_blankline = function() {\n  return this.is_empty() ||\n    (this.current_line.is_empty() && this.previous_line.is_empty());\n};\n\nOutput.prototype.ensure_empty_line_above = function(starts_with, ends_with) {\n  var index = this.__lines.length - 2;\n  while (index >= 0) {\n    var potentialEmptyLine = this.__lines[index];\n    if (potentialEmptyLine.is_empty()) {\n      break;\n    } else if (potentialEmptyLine.item(0).indexOf(starts_with) !== 0 &&\n      potentialEmptyLine.item(-1) !== ends_with) {\n      this.__lines.splice(index + 1, 0, new OutputLine(this));\n      this.previous_line = this.__lines[this.__lines.length - 2];\n      break;\n    }\n    index--;\n  }\n};\n\nmodule.exports.Output = Output;\n\n\n/***/ }),\n/* 3 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nfunction Token(type, text, newlines, whitespace_before) {\n  this.type = type;\n  this.text = text;\n\n  // comments_before are\n  // comments that have a new line before them\n  // and may or may not have a newline after\n  // this is a set of comments before\n  this.comments_before = null; /* inline comment*/\n\n\n  // this.comments_after =  new TokenStream(); // no new line before and newline after\n  this.newlines = newlines || 0;\n  this.whitespace_before = whitespace_before || '';\n  this.parent = null;\n  this.next = null;\n  this.previous = null;\n  this.opened = null;\n  this.closed = null;\n  this.directives = null;\n}\n\n\nmodule.exports.Token = Token;\n\n\n/***/ }),\n/* 4 */,\n/* 5 */,\n/* 6 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nfunction Options(options, merge_child_field) {\n  this.raw_options = _mergeOpts(options, merge_child_field);\n\n  // Support passing the source text back with no change\n  this.disabled = this._get_boolean('disabled');\n\n  this.eol = this._get_characters('eol', 'auto');\n  this.end_with_newline = this._get_boolean('end_with_newline');\n  this.indent_size = this._get_number('indent_size', 4);\n  this.indent_char = this._get_characters('indent_char', ' ');\n  this.indent_level = this._get_number('indent_level');\n\n  this.preserve_newlines = this._get_boolean('preserve_newlines', true);\n  this.max_preserve_newlines = this._get_number('max_preserve_newlines', 32786);\n  if (!this.preserve_newlines) {\n    this.max_preserve_newlines = 0;\n  }\n\n  this.indent_with_tabs = this._get_boolean('indent_with_tabs', this.indent_char === '\\t');\n  if (this.indent_with_tabs) {\n    this.indent_char = '\\t';\n\n    // indent_size behavior changed after 1.8.6\n    // It used to be that indent_size would be\n    // set to 1 for indent_with_tabs. That is no longer needed and\n    // actually doesn't make sense - why not use spaces? Further,\n    // that might produce unexpected behavior - tabs being used\n    // for single-column alignment. So, when indent_with_tabs is true\n    // and indent_size is 1, reset indent_size to 4.\n    if (this.indent_size === 1) {\n      this.indent_size = 4;\n    }\n  }\n\n  // Backwards compat with 1.3.x\n  this.wrap_line_length = this._get_number('wrap_line_length', this._get_number('max_char'));\n\n  this.indent_empty_lines = this._get_boolean('indent_empty_lines');\n\n  // valid templating languages ['django', 'erb', 'handlebars', 'php', 'smarty', 'angular']\n  // For now, 'auto' = all off for javascript, all except angular on for html (and inline javascript/css).\n  // other values ignored\n  this.templating = this._get_selection_list('templating', ['auto', 'none', 'angular', 'django', 'erb', 'handlebars', 'php', 'smarty'], ['auto']);\n}\n\nOptions.prototype._get_array = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  var result = default_value || [];\n  if (typeof option_value === 'object') {\n    if (option_value !== null && typeof option_value.concat === 'function') {\n      result = option_value.concat();\n    }\n  } else if (typeof option_value === 'string') {\n    result = option_value.split(/[^a-zA-Z0-9_\\/\\-]+/);\n  }\n  return result;\n};\n\nOptions.prototype._get_boolean = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  var result = option_value === undefined ? !!default_value : !!option_value;\n  return result;\n};\n\nOptions.prototype._get_characters = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  var result = default_value || '';\n  if (typeof option_value === 'string') {\n    result = option_value.replace(/\\\\r/, '\\r').replace(/\\\\n/, '\\n').replace(/\\\\t/, '\\t');\n  }\n  return result;\n};\n\nOptions.prototype._get_number = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  default_value = parseInt(default_value, 10);\n  if (isNaN(default_value)) {\n    default_value = 0;\n  }\n  var result = parseInt(option_value, 10);\n  if (isNaN(result)) {\n    result = default_value;\n  }\n  return result;\n};\n\nOptions.prototype._get_selection = function(name, selection_list, default_value) {\n  var result = this._get_selection_list(name, selection_list, default_value);\n  if (result.length !== 1) {\n    throw new Error(\n      \"Invalid Option Value: The option '\" + name + \"' can only be one of the following values:\\n\" +\n      selection_list + \"\\nYou passed in: '\" + this.raw_options[name] + \"'\");\n  }\n\n  return result[0];\n};\n\n\nOptions.prototype._get_selection_list = function(name, selection_list, default_value) {\n  if (!selection_list || selection_list.length === 0) {\n    throw new Error(\"Selection list cannot be empty.\");\n  }\n\n  default_value = default_value || [selection_list[0]];\n  if (!this._is_valid_selection(default_value, selection_list)) {\n    throw new Error(\"Invalid Default Value!\");\n  }\n\n  var result = this._get_array(name, default_value);\n  if (!this._is_valid_selection(result, selection_list)) {\n    throw new Error(\n      \"Invalid Option Value: The option '\" + name + \"' can contain only the following values:\\n\" +\n      selection_list + \"\\nYou passed in: '\" + this.raw_options[name] + \"'\");\n  }\n\n  return result;\n};\n\nOptions.prototype._is_valid_selection = function(result, selection_list) {\n  return result.length && selection_list.length &&\n    !result.some(function(item) { return selection_list.indexOf(item) === -1; });\n};\n\n\n// merges child options up with the parent options object\n// Example: obj = {a: 1, b: {a: 2}}\n//          mergeOpts(obj, 'b')\n//\n//          Returns: {a: 2}\nfunction _mergeOpts(allOptions, childFieldName) {\n  var finalOpts = {};\n  allOptions = _normalizeOpts(allOptions);\n  var name;\n\n  for (name in allOptions) {\n    if (name !== childFieldName) {\n      finalOpts[name] = allOptions[name];\n    }\n  }\n\n  //merge in the per type settings for the childFieldName\n  if (childFieldName && allOptions[childFieldName]) {\n    for (name in allOptions[childFieldName]) {\n      finalOpts[name] = allOptions[childFieldName][name];\n    }\n  }\n  return finalOpts;\n}\n\nfunction _normalizeOpts(options) {\n  var convertedOpts = {};\n  var key;\n\n  for (key in options) {\n    var newKey = key.replace(/-/g, \"_\");\n    convertedOpts[newKey] = options[key];\n  }\n  return convertedOpts;\n}\n\nmodule.exports.Options = Options;\nmodule.exports.normalizeOpts = _normalizeOpts;\nmodule.exports.mergeOpts = _mergeOpts;\n\n\n/***/ }),\n/* 7 */,\n/* 8 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar regexp_has_sticky = RegExp.prototype.hasOwnProperty('sticky');\n\nfunction InputScanner(input_string) {\n  this.__input = input_string || '';\n  this.__input_length = this.__input.length;\n  this.__position = 0;\n}\n\nInputScanner.prototype.restart = function() {\n  this.__position = 0;\n};\n\nInputScanner.prototype.back = function() {\n  if (this.__position > 0) {\n    this.__position -= 1;\n  }\n};\n\nInputScanner.prototype.hasNext = function() {\n  return this.__position < this.__input_length;\n};\n\nInputScanner.prototype.next = function() {\n  var val = null;\n  if (this.hasNext()) {\n    val = this.__input.charAt(this.__position);\n    this.__position += 1;\n  }\n  return val;\n};\n\nInputScanner.prototype.peek = function(index) {\n  var val = null;\n  index = index || 0;\n  index += this.__position;\n  if (index >= 0 && index < this.__input_length) {\n    val = this.__input.charAt(index);\n  }\n  return val;\n};\n\n// This is a JavaScript only helper function (not in python)\n// Javascript doesn't have a match method\n// and not all implementation support \"sticky\" flag.\n// If they do not support sticky then both this.match() and this.test() method\n// must get the match and check the index of the match.\n// If sticky is supported and set, this method will use it.\n// Otherwise it will check that global is set, and fall back to the slower method.\nInputScanner.prototype.__match = function(pattern, index) {\n  pattern.lastIndex = index;\n  var pattern_match = pattern.exec(this.__input);\n\n  if (pattern_match && !(regexp_has_sticky && pattern.sticky)) {\n    if (pattern_match.index !== index) {\n      pattern_match = null;\n    }\n  }\n\n  return pattern_match;\n};\n\nInputScanner.prototype.test = function(pattern, index) {\n  index = index || 0;\n  index += this.__position;\n\n  if (index >= 0 && index < this.__input_length) {\n    return !!this.__match(pattern, index);\n  } else {\n    return false;\n  }\n};\n\nInputScanner.prototype.testChar = function(pattern, index) {\n  // test one character regex match\n  var val = this.peek(index);\n  pattern.lastIndex = 0;\n  return val !== null && pattern.test(val);\n};\n\nInputScanner.prototype.match = function(pattern) {\n  var pattern_match = this.__match(pattern, this.__position);\n  if (pattern_match) {\n    this.__position += pattern_match[0].length;\n  } else {\n    pattern_match = null;\n  }\n  return pattern_match;\n};\n\nInputScanner.prototype.read = function(starting_pattern, until_pattern, until_after) {\n  var val = '';\n  var match;\n  if (starting_pattern) {\n    match = this.match(starting_pattern);\n    if (match) {\n      val += match[0];\n    }\n  }\n  if (until_pattern && (match || !starting_pattern)) {\n    val += this.readUntil(until_pattern, until_after);\n  }\n  return val;\n};\n\nInputScanner.prototype.readUntil = function(pattern, until_after) {\n  var val = '';\n  var match_index = this.__position;\n  pattern.lastIndex = this.__position;\n  var pattern_match = pattern.exec(this.__input);\n  if (pattern_match) {\n    match_index = pattern_match.index;\n    if (until_after) {\n      match_index += pattern_match[0].length;\n    }\n  } else {\n    match_index = this.__input_length;\n  }\n\n  val = this.__input.substring(this.__position, match_index);\n  this.__position = match_index;\n  return val;\n};\n\nInputScanner.prototype.readUntilAfter = function(pattern) {\n  return this.readUntil(pattern, true);\n};\n\nInputScanner.prototype.get_regexp = function(pattern, match_from) {\n  var result = null;\n  var flags = 'g';\n  if (match_from && regexp_has_sticky) {\n    flags = 'y';\n  }\n  // strings are converted to regexp\n  if (typeof pattern === \"string\" && pattern !== '') {\n    // result = new RegExp(pattern.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'), flags);\n    result = new RegExp(pattern, flags);\n  } else if (pattern) {\n    result = new RegExp(pattern.source, flags);\n  }\n  return result;\n};\n\nInputScanner.prototype.get_literal_regexp = function(literal_string) {\n  return RegExp(literal_string.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'));\n};\n\n/* css beautifier legacy helpers */\nInputScanner.prototype.peekUntilAfter = function(pattern) {\n  var start = this.__position;\n  var val = this.readUntilAfter(pattern);\n  this.__position = start;\n  return val;\n};\n\nInputScanner.prototype.lookBack = function(testVal) {\n  var start = this.__position - 1;\n  return start >= testVal.length && this.__input.substring(start - testVal.length, start)\n    .toLowerCase() === testVal;\n};\n\nmodule.exports.InputScanner = InputScanner;\n\n\n/***/ }),\n/* 9 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar InputScanner = (__webpack_require__(8).InputScanner);\nvar Token = (__webpack_require__(3).Token);\nvar TokenStream = (__webpack_require__(10).TokenStream);\nvar WhitespacePattern = (__webpack_require__(11).WhitespacePattern);\n\nvar TOKEN = {\n  START: 'TK_START',\n  RAW: 'TK_RAW',\n  EOF: 'TK_EOF'\n};\n\nvar Tokenizer = function(input_string, options) {\n  this._input = new InputScanner(input_string);\n  this._options = options || {};\n  this.__tokens = null;\n\n  this._patterns = {};\n  this._patterns.whitespace = new WhitespacePattern(this._input);\n};\n\nTokenizer.prototype.tokenize = function() {\n  this._input.restart();\n  this.__tokens = new TokenStream();\n\n  this._reset();\n\n  var current;\n  var previous = new Token(TOKEN.START, '');\n  var open_token = null;\n  var open_stack = [];\n  var comments = new TokenStream();\n\n  while (previous.type !== TOKEN.EOF) {\n    current = this._get_next_token(previous, open_token);\n    while (this._is_comment(current)) {\n      comments.add(current);\n      current = this._get_next_token(previous, open_token);\n    }\n\n    if (!comments.isEmpty()) {\n      current.comments_before = comments;\n      comments = new TokenStream();\n    }\n\n    current.parent = open_token;\n\n    if (this._is_opening(current)) {\n      open_stack.push(open_token);\n      open_token = current;\n    } else if (open_token && this._is_closing(current, open_token)) {\n      current.opened = open_token;\n      open_token.closed = current;\n      open_token = open_stack.pop();\n      current.parent = open_token;\n    }\n\n    current.previous = previous;\n    previous.next = current;\n\n    this.__tokens.add(current);\n    previous = current;\n  }\n\n  return this.__tokens;\n};\n\n\nTokenizer.prototype._is_first_token = function() {\n  return this.__tokens.isEmpty();\n};\n\nTokenizer.prototype._reset = function() {};\n\nTokenizer.prototype._get_next_token = function(previous_token, open_token) { // jshint unused:false\n  this._readWhitespace();\n  var resulting_string = this._input.read(/.+/g);\n  if (resulting_string) {\n    return this._create_token(TOKEN.RAW, resulting_string);\n  } else {\n    return this._create_token(TOKEN.EOF, '');\n  }\n};\n\nTokenizer.prototype._is_comment = function(current_token) { // jshint unused:false\n  return false;\n};\n\nTokenizer.prototype._is_opening = function(current_token) { // jshint unused:false\n  return false;\n};\n\nTokenizer.prototype._is_closing = function(current_token, open_token) { // jshint unused:false\n  return false;\n};\n\nTokenizer.prototype._create_token = function(type, text) {\n  var token = new Token(type, text,\n    this._patterns.whitespace.newline_count,\n    this._patterns.whitespace.whitespace_before_token);\n  return token;\n};\n\nTokenizer.prototype._readWhitespace = function() {\n  return this._patterns.whitespace.read();\n};\n\n\n\nmodule.exports.Tokenizer = Tokenizer;\nmodule.exports.TOKEN = TOKEN;\n\n\n/***/ }),\n/* 10 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nfunction TokenStream(parent_token) {\n  // private\n  this.__tokens = [];\n  this.__tokens_length = this.__tokens.length;\n  this.__position = 0;\n  this.__parent_token = parent_token;\n}\n\nTokenStream.prototype.restart = function() {\n  this.__position = 0;\n};\n\nTokenStream.prototype.isEmpty = function() {\n  return this.__tokens_length === 0;\n};\n\nTokenStream.prototype.hasNext = function() {\n  return this.__position < this.__tokens_length;\n};\n\nTokenStream.prototype.next = function() {\n  var val = null;\n  if (this.hasNext()) {\n    val = this.__tokens[this.__position];\n    this.__position += 1;\n  }\n  return val;\n};\n\nTokenStream.prototype.peek = function(index) {\n  var val = null;\n  index = index || 0;\n  index += this.__position;\n  if (index >= 0 && index < this.__tokens_length) {\n    val = this.__tokens[index];\n  }\n  return val;\n};\n\nTokenStream.prototype.add = function(token) {\n  if (this.__parent_token) {\n    token.parent = this.__parent_token;\n  }\n  this.__tokens.push(token);\n  this.__tokens_length += 1;\n};\n\nmodule.exports.TokenStream = TokenStream;\n\n\n/***/ }),\n/* 11 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar Pattern = (__webpack_require__(12).Pattern);\n\nfunction WhitespacePattern(input_scanner, parent) {\n  Pattern.call(this, input_scanner, parent);\n  if (parent) {\n    this._line_regexp = this._input.get_regexp(parent._line_regexp);\n  } else {\n    this.__set_whitespace_patterns('', '');\n  }\n\n  this.newline_count = 0;\n  this.whitespace_before_token = '';\n}\nWhitespacePattern.prototype = new Pattern();\n\nWhitespacePattern.prototype.__set_whitespace_patterns = function(whitespace_chars, newline_chars) {\n  whitespace_chars += '\\\\t ';\n  newline_chars += '\\\\n\\\\r';\n\n  this._match_pattern = this._input.get_regexp(\n    '[' + whitespace_chars + newline_chars + ']+', true);\n  this._newline_regexp = this._input.get_regexp(\n    '\\\\r\\\\n|[' + newline_chars + ']');\n};\n\nWhitespacePattern.prototype.read = function() {\n  this.newline_count = 0;\n  this.whitespace_before_token = '';\n\n  var resulting_string = this._input.read(this._match_pattern);\n  if (resulting_string === ' ') {\n    this.whitespace_before_token = ' ';\n  } else if (resulting_string) {\n    var matches = this.__split(this._newline_regexp, resulting_string);\n    this.newline_count = matches.length - 1;\n    this.whitespace_before_token = matches[this.newline_count];\n  }\n\n  return resulting_string;\n};\n\nWhitespacePattern.prototype.matching = function(whitespace_chars, newline_chars) {\n  var result = this._create();\n  result.__set_whitespace_patterns(whitespace_chars, newline_chars);\n  result._update();\n  return result;\n};\n\nWhitespacePattern.prototype._create = function() {\n  return new WhitespacePattern(this._input, this);\n};\n\nWhitespacePattern.prototype.__split = function(regexp, input_string) {\n  regexp.lastIndex = 0;\n  var start_index = 0;\n  var result = [];\n  var next_match = regexp.exec(input_string);\n  while (next_match) {\n    result.push(input_string.substring(start_index, next_match.index));\n    start_index = next_match.index + next_match[0].length;\n    next_match = regexp.exec(input_string);\n  }\n\n  if (start_index < input_string.length) {\n    result.push(input_string.substring(start_index, input_string.length));\n  } else {\n    result.push('');\n  }\n\n  return result;\n};\n\n\n\nmodule.exports.WhitespacePattern = WhitespacePattern;\n\n\n/***/ }),\n/* 12 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nfunction Pattern(input_scanner, parent) {\n  this._input = input_scanner;\n  this._starting_pattern = null;\n  this._match_pattern = null;\n  this._until_pattern = null;\n  this._until_after = false;\n\n  if (parent) {\n    this._starting_pattern = this._input.get_regexp(parent._starting_pattern, true);\n    this._match_pattern = this._input.get_regexp(parent._match_pattern, true);\n    this._until_pattern = this._input.get_regexp(parent._until_pattern);\n    this._until_after = parent._until_after;\n  }\n}\n\nPattern.prototype.read = function() {\n  var result = this._input.read(this._starting_pattern);\n  if (!this._starting_pattern || result) {\n    result += this._input.read(this._match_pattern, this._until_pattern, this._until_after);\n  }\n  return result;\n};\n\nPattern.prototype.read_match = function() {\n  return this._input.match(this._match_pattern);\n};\n\nPattern.prototype.until_after = function(pattern) {\n  var result = this._create();\n  result._until_after = true;\n  result._until_pattern = this._input.get_regexp(pattern);\n  result._update();\n  return result;\n};\n\nPattern.prototype.until = function(pattern) {\n  var result = this._create();\n  result._until_after = false;\n  result._until_pattern = this._input.get_regexp(pattern);\n  result._update();\n  return result;\n};\n\nPattern.prototype.starting_with = function(pattern) {\n  var result = this._create();\n  result._starting_pattern = this._input.get_regexp(pattern, true);\n  result._update();\n  return result;\n};\n\nPattern.prototype.matching = function(pattern) {\n  var result = this._create();\n  result._match_pattern = this._input.get_regexp(pattern, true);\n  result._update();\n  return result;\n};\n\nPattern.prototype._create = function() {\n  return new Pattern(this._input, this);\n};\n\nPattern.prototype._update = function() {};\n\nmodule.exports.Pattern = Pattern;\n\n\n/***/ }),\n/* 13 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nfunction Directives(start_block_pattern, end_block_pattern) {\n  start_block_pattern = typeof start_block_pattern === 'string' ? start_block_pattern : start_block_pattern.source;\n  end_block_pattern = typeof end_block_pattern === 'string' ? end_block_pattern : end_block_pattern.source;\n  this.__directives_block_pattern = new RegExp(start_block_pattern + / beautify( \\w+[:]\\w+)+ /.source + end_block_pattern, 'g');\n  this.__directive_pattern = / (\\w+)[:](\\w+)/g;\n\n  this.__directives_end_ignore_pattern = new RegExp(start_block_pattern + /\\sbeautify\\signore:end\\s/.source + end_block_pattern, 'g');\n}\n\nDirectives.prototype.get_directives = function(text) {\n  if (!text.match(this.__directives_block_pattern)) {\n    return null;\n  }\n\n  var directives = {};\n  this.__directive_pattern.lastIndex = 0;\n  var directive_match = this.__directive_pattern.exec(text);\n\n  while (directive_match) {\n    directives[directive_match[1]] = directive_match[2];\n    directive_match = this.__directive_pattern.exec(text);\n  }\n\n  return directives;\n};\n\nDirectives.prototype.readIgnored = function(input) {\n  return input.readUntilAfter(this.__directives_end_ignore_pattern);\n};\n\n\nmodule.exports.Directives = Directives;\n\n\n/***/ }),\n/* 14 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar Pattern = (__webpack_require__(12).Pattern);\n\n\nvar template_names = {\n  django: false,\n  erb: false,\n  handlebars: false,\n  php: false,\n  smarty: false,\n  angular: false\n};\n\n// This lets templates appear anywhere we would do a readUntil\n// The cost is higher but it is pay to play.\nfunction TemplatablePattern(input_scanner, parent) {\n  Pattern.call(this, input_scanner, parent);\n  this.__template_pattern = null;\n  this._disabled = Object.assign({}, template_names);\n  this._excluded = Object.assign({}, template_names);\n\n  if (parent) {\n    this.__template_pattern = this._input.get_regexp(parent.__template_pattern);\n    this._excluded = Object.assign(this._excluded, parent._excluded);\n    this._disabled = Object.assign(this._disabled, parent._disabled);\n  }\n  var pattern = new Pattern(input_scanner);\n  this.__patterns = {\n    handlebars_comment: pattern.starting_with(/{{!--/).until_after(/--}}/),\n    handlebars_unescaped: pattern.starting_with(/{{{/).until_after(/}}}/),\n    handlebars: pattern.starting_with(/{{/).until_after(/}}/),\n    php: pattern.starting_with(/<\\?(?:[= ]|php)/).until_after(/\\?>/),\n    erb: pattern.starting_with(/<%[^%]/).until_after(/[^%]%>/),\n    // django coflicts with handlebars a bit.\n    django: pattern.starting_with(/{%/).until_after(/%}/),\n    django_value: pattern.starting_with(/{{/).until_after(/}}/),\n    django_comment: pattern.starting_with(/{#/).until_after(/#}/),\n    smarty: pattern.starting_with(/{(?=[^}{\\s\\n])/).until_after(/[^\\s\\n]}/),\n    smarty_comment: pattern.starting_with(/{\\*/).until_after(/\\*}/),\n    smarty_literal: pattern.starting_with(/{literal}/).until_after(/{\\/literal}/)\n  };\n}\nTemplatablePattern.prototype = new Pattern();\n\nTemplatablePattern.prototype._create = function() {\n  return new TemplatablePattern(this._input, this);\n};\n\nTemplatablePattern.prototype._update = function() {\n  this.__set_templated_pattern();\n};\n\nTemplatablePattern.prototype.disable = function(language) {\n  var result = this._create();\n  result._disabled[language] = true;\n  result._update();\n  return result;\n};\n\nTemplatablePattern.prototype.read_options = function(options) {\n  var result = this._create();\n  for (var language in template_names) {\n    result._disabled[language] = options.templating.indexOf(language) === -1;\n  }\n  result._update();\n  return result;\n};\n\nTemplatablePattern.prototype.exclude = function(language) {\n  var result = this._create();\n  result._excluded[language] = true;\n  result._update();\n  return result;\n};\n\nTemplatablePattern.prototype.read = function() {\n  var result = '';\n  if (this._match_pattern) {\n    result = this._input.read(this._starting_pattern);\n  } else {\n    result = this._input.read(this._starting_pattern, this.__template_pattern);\n  }\n  var next = this._read_template();\n  while (next) {\n    if (this._match_pattern) {\n      next += this._input.read(this._match_pattern);\n    } else {\n      next += this._input.readUntil(this.__template_pattern);\n    }\n    result += next;\n    next = this._read_template();\n  }\n\n  if (this._until_after) {\n    result += this._input.readUntilAfter(this._until_pattern);\n  }\n  return result;\n};\n\nTemplatablePattern.prototype.__set_templated_pattern = function() {\n  var items = [];\n\n  if (!this._disabled.php) {\n    items.push(this.__patterns.php._starting_pattern.source);\n  }\n  if (!this._disabled.handlebars) {\n    items.push(this.__patterns.handlebars._starting_pattern.source);\n  }\n  if (!this._disabled.angular) {\n    // Handlebars ('{{' and '}}') are also special tokens in Angular)\n    items.push(this.__patterns.handlebars._starting_pattern.source);\n  }\n  if (!this._disabled.erb) {\n    items.push(this.__patterns.erb._starting_pattern.source);\n  }\n  if (!this._disabled.django) {\n    items.push(this.__patterns.django._starting_pattern.source);\n    // The starting pattern for django is more complex because it has different\n    // patterns for value, comment, and other sections\n    items.push(this.__patterns.django_value._starting_pattern.source);\n    items.push(this.__patterns.django_comment._starting_pattern.source);\n  }\n  if (!this._disabled.smarty) {\n    items.push(this.__patterns.smarty._starting_pattern.source);\n  }\n\n  if (this._until_pattern) {\n    items.push(this._until_pattern.source);\n  }\n  this.__template_pattern = this._input.get_regexp('(?:' + items.join('|') + ')');\n};\n\nTemplatablePattern.prototype._read_template = function() {\n  var resulting_string = '';\n  var c = this._input.peek();\n  if (c === '<') {\n    var peek1 = this._input.peek(1);\n    //if we're in a comment, do something special\n    // We treat all comments as literals, even more than preformatted tags\n    // we just look for the appropriate close tag\n    if (!this._disabled.php && !this._excluded.php && peek1 === '?') {\n      resulting_string = resulting_string ||\n        this.__patterns.php.read();\n    }\n    if (!this._disabled.erb && !this._excluded.erb && peek1 === '%') {\n      resulting_string = resulting_string ||\n        this.__patterns.erb.read();\n    }\n  } else if (c === '{') {\n    if (!this._disabled.handlebars && !this._excluded.handlebars) {\n      resulting_string = resulting_string ||\n        this.__patterns.handlebars_comment.read();\n      resulting_string = resulting_string ||\n        this.__patterns.handlebars_unescaped.read();\n      resulting_string = resulting_string ||\n        this.__patterns.handlebars.read();\n    }\n    if (!this._disabled.django) {\n      // django coflicts with handlebars a bit.\n      if (!this._excluded.django && !this._excluded.handlebars) {\n        resulting_string = resulting_string ||\n          this.__patterns.django_value.read();\n      }\n      if (!this._excluded.django) {\n        resulting_string = resulting_string ||\n          this.__patterns.django_comment.read();\n        resulting_string = resulting_string ||\n          this.__patterns.django.read();\n      }\n    }\n    if (!this._disabled.smarty) {\n      // smarty cannot be enabled with django or handlebars enabled\n      if (this._disabled.django && this._disabled.handlebars) {\n        resulting_string = resulting_string ||\n          this.__patterns.smarty_comment.read();\n        resulting_string = resulting_string ||\n          this.__patterns.smarty_literal.read();\n        resulting_string = resulting_string ||\n          this.__patterns.smarty.read();\n      }\n    }\n  }\n  return resulting_string;\n};\n\n\nmodule.exports.TemplatablePattern = TemplatablePattern;\n\n\n/***/ }),\n/* 15 */,\n/* 16 */,\n/* 17 */,\n/* 18 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar Beautifier = (__webpack_require__(19).Beautifier),\n  Options = (__webpack_require__(20).Options);\n\nfunction style_html(html_source, options, js_beautify, css_beautify) {\n  var beautifier = new Beautifier(html_source, options, js_beautify, css_beautify);\n  return beautifier.beautify();\n}\n\nmodule.exports = style_html;\nmodule.exports.defaultOptions = function() {\n  return new Options();\n};\n\n\n/***/ }),\n/* 19 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar Options = (__webpack_require__(20).Options);\nvar Output = (__webpack_require__(2).Output);\nvar Tokenizer = (__webpack_require__(21).Tokenizer);\nvar TOKEN = (__webpack_require__(21).TOKEN);\n\nvar lineBreak = /\\r\\n|[\\r\\n]/;\nvar allLineBreaks = /\\r\\n|[\\r\\n]/g;\n\nvar Printer = function(options, base_indent_string) { //handles input/output and some other printing functions\n\n  this.indent_level = 0;\n  this.alignment_size = 0;\n  this.max_preserve_newlines = options.max_preserve_newlines;\n  this.preserve_newlines = options.preserve_newlines;\n\n  this._output = new Output(options, base_indent_string);\n\n};\n\nPrinter.prototype.current_line_has_match = function(pattern) {\n  return this._output.current_line.has_match(pattern);\n};\n\nPrinter.prototype.set_space_before_token = function(value, non_breaking) {\n  this._output.space_before_token = value;\n  this._output.non_breaking_space = non_breaking;\n};\n\nPrinter.prototype.set_wrap_point = function() {\n  this._output.set_indent(this.indent_level, this.alignment_size);\n  this._output.set_wrap_point();\n};\n\n\nPrinter.prototype.add_raw_token = function(token) {\n  this._output.add_raw_token(token);\n};\n\nPrinter.prototype.print_preserved_newlines = function(raw_token) {\n  var newlines = 0;\n  if (raw_token.type !== TOKEN.TEXT && raw_token.previous.type !== TOKEN.TEXT) {\n    newlines = raw_token.newlines ? 1 : 0;\n  }\n\n  if (this.preserve_newlines) {\n    newlines = raw_token.newlines < this.max_preserve_newlines + 1 ? raw_token.newlines : this.max_preserve_newlines + 1;\n  }\n  for (var n = 0; n < newlines; n++) {\n    this.print_newline(n > 0);\n  }\n\n  return newlines !== 0;\n};\n\nPrinter.prototype.traverse_whitespace = function(raw_token) {\n  if (raw_token.whitespace_before || raw_token.newlines) {\n    if (!this.print_preserved_newlines(raw_token)) {\n      this._output.space_before_token = true;\n    }\n    return true;\n  }\n  return false;\n};\n\nPrinter.prototype.previous_token_wrapped = function() {\n  return this._output.previous_token_wrapped;\n};\n\nPrinter.prototype.print_newline = function(force) {\n  this._output.add_new_line(force);\n};\n\nPrinter.prototype.print_token = function(token) {\n  if (token.text) {\n    this._output.set_indent(this.indent_level, this.alignment_size);\n    this._output.add_token(token.text);\n  }\n};\n\nPrinter.prototype.indent = function() {\n  this.indent_level++;\n};\n\nPrinter.prototype.deindent = function() {\n  if (this.indent_level > 0) {\n    this.indent_level--;\n    this._output.set_indent(this.indent_level, this.alignment_size);\n  }\n};\n\nPrinter.prototype.get_full_indent = function(level) {\n  level = this.indent_level + (level || 0);\n  if (level < 1) {\n    return '';\n  }\n\n  return this._output.get_indent_string(level);\n};\n\nvar get_type_attribute = function(start_token) {\n  var result = null;\n  var raw_token = start_token.next;\n\n  // Search attributes for a type attribute\n  while (raw_token.type !== TOKEN.EOF && start_token.closed !== raw_token) {\n    if (raw_token.type === TOKEN.ATTRIBUTE && raw_token.text === 'type') {\n      if (raw_token.next && raw_token.next.type === TOKEN.EQUALS &&\n        raw_token.next.next && raw_token.next.next.type === TOKEN.VALUE) {\n        result = raw_token.next.next.text;\n      }\n      break;\n    }\n    raw_token = raw_token.next;\n  }\n\n  return result;\n};\n\nvar get_custom_beautifier_name = function(tag_check, raw_token) {\n  var typeAttribute = null;\n  var result = null;\n\n  if (!raw_token.closed) {\n    return null;\n  }\n\n  if (tag_check === 'script') {\n    typeAttribute = 'text/javascript';\n  } else if (tag_check === 'style') {\n    typeAttribute = 'text/css';\n  }\n\n  typeAttribute = get_type_attribute(raw_token) || typeAttribute;\n\n  // For script and style tags that have a type attribute, only enable custom beautifiers for matching values\n  // For those without a type attribute use default;\n  if (typeAttribute.search('text/css') > -1) {\n    result = 'css';\n  } else if (typeAttribute.search(/module|((text|application|dojo)\\/(x-)?(javascript|ecmascript|jscript|livescript|(ld\\+)?json|method|aspect))/) > -1) {\n    result = 'javascript';\n  } else if (typeAttribute.search(/(text|application|dojo)\\/(x-)?(html)/) > -1) {\n    result = 'html';\n  } else if (typeAttribute.search(/test\\/null/) > -1) {\n    // Test only mime-type for testing the beautifier when null is passed as beautifing function\n    result = 'null';\n  }\n\n  return result;\n};\n\nfunction in_array(what, arr) {\n  return arr.indexOf(what) !== -1;\n}\n\nfunction TagFrame(parent, parser_token, indent_level) {\n  this.parent = parent || null;\n  this.tag = parser_token ? parser_token.tag_name : '';\n  this.indent_level = indent_level || 0;\n  this.parser_token = parser_token || null;\n}\n\nfunction TagStack(printer) {\n  this._printer = printer;\n  this._current_frame = null;\n}\n\nTagStack.prototype.get_parser_token = function() {\n  return this._current_frame ? this._current_frame.parser_token : null;\n};\n\nTagStack.prototype.record_tag = function(parser_token) { //function to record a tag and its parent in this.tags Object\n  var new_frame = new TagFrame(this._current_frame, parser_token, this._printer.indent_level);\n  this._current_frame = new_frame;\n};\n\nTagStack.prototype._try_pop_frame = function(frame) { //function to retrieve the opening tag to the corresponding closer\n  var parser_token = null;\n\n  if (frame) {\n    parser_token = frame.parser_token;\n    this._printer.indent_level = frame.indent_level;\n    this._current_frame = frame.parent;\n  }\n\n  return parser_token;\n};\n\nTagStack.prototype._get_frame = function(tag_list, stop_list) { //function to retrieve the opening tag to the corresponding closer\n  var frame = this._current_frame;\n\n  while (frame) { //till we reach '' (the initial value);\n    if (tag_list.indexOf(frame.tag) !== -1) { //if this is it use it\n      break;\n    } else if (stop_list && stop_list.indexOf(frame.tag) !== -1) {\n      frame = null;\n      break;\n    }\n    frame = frame.parent;\n  }\n\n  return frame;\n};\n\nTagStack.prototype.try_pop = function(tag, stop_list) { //function to retrieve the opening tag to the corresponding closer\n  var frame = this._get_frame([tag], stop_list);\n  return this._try_pop_frame(frame);\n};\n\nTagStack.prototype.indent_to_tag = function(tag_list) {\n  var frame = this._get_frame(tag_list);\n  if (frame) {\n    this._printer.indent_level = frame.indent_level;\n  }\n};\n\nfunction Beautifier(source_text, options, js_beautify, css_beautify) {\n  //Wrapper function to invoke all the necessary constructors and deal with the output.\n  this._source_text = source_text || '';\n  options = options || {};\n  this._js_beautify = js_beautify;\n  this._css_beautify = css_beautify;\n  this._tag_stack = null;\n\n  // Allow the setting of language/file-type specific options\n  // with inheritance of overall settings\n  var optionHtml = new Options(options, 'html');\n\n  this._options = optionHtml;\n\n  this._is_wrap_attributes_force = this._options.wrap_attributes.substr(0, 'force'.length) === 'force';\n  this._is_wrap_attributes_force_expand_multiline = (this._options.wrap_attributes === 'force-expand-multiline');\n  this._is_wrap_attributes_force_aligned = (this._options.wrap_attributes === 'force-aligned');\n  this._is_wrap_attributes_aligned_multiple = (this._options.wrap_attributes === 'aligned-multiple');\n  this._is_wrap_attributes_preserve = this._options.wrap_attributes.substr(0, 'preserve'.length) === 'preserve';\n  this._is_wrap_attributes_preserve_aligned = (this._options.wrap_attributes === 'preserve-aligned');\n}\n\nBeautifier.prototype.beautify = function() {\n\n  // if disabled, return the input unchanged.\n  if (this._options.disabled) {\n    return this._source_text;\n  }\n\n  var source_text = this._source_text;\n  var eol = this._options.eol;\n  if (this._options.eol === 'auto') {\n    eol = '\\n';\n    if (source_text && lineBreak.test(source_text)) {\n      eol = source_text.match(lineBreak)[0];\n    }\n  }\n\n  // HACK: newline parsing inconsistent. This brute force normalizes the input.\n  source_text = source_text.replace(allLineBreaks, '\\n');\n\n  var baseIndentString = source_text.match(/^[\\t ]*/)[0];\n\n  var last_token = {\n    text: '',\n    type: ''\n  };\n\n  var last_tag_token = new TagOpenParserToken(this._options);\n\n  var printer = new Printer(this._options, baseIndentString);\n  var tokens = new Tokenizer(source_text, this._options).tokenize();\n\n  this._tag_stack = new TagStack(printer);\n\n  var parser_token = null;\n  var raw_token = tokens.next();\n  while (raw_token.type !== TOKEN.EOF) {\n\n    if (raw_token.type === TOKEN.TAG_OPEN || raw_token.type === TOKEN.COMMENT) {\n      parser_token = this._handle_tag_open(printer, raw_token, last_tag_token, last_token, tokens);\n      last_tag_token = parser_token;\n    } else if ((raw_token.type === TOKEN.ATTRIBUTE || raw_token.type === TOKEN.EQUALS || raw_token.type === TOKEN.VALUE) ||\n      (raw_token.type === TOKEN.TEXT && !last_tag_token.tag_complete)) {\n      parser_token = this._handle_inside_tag(printer, raw_token, last_tag_token, last_token);\n    } else if (raw_token.type === TOKEN.TAG_CLOSE) {\n      parser_token = this._handle_tag_close(printer, raw_token, last_tag_token);\n    } else if (raw_token.type === TOKEN.TEXT) {\n      parser_token = this._handle_text(printer, raw_token, last_tag_token);\n    } else if (raw_token.type === TOKEN.CONTROL_FLOW_OPEN) {\n      parser_token = this._handle_control_flow_open(printer, raw_token);\n    } else if (raw_token.type === TOKEN.CONTROL_FLOW_CLOSE) {\n      parser_token = this._handle_control_flow_close(printer, raw_token);\n    } else {\n      // This should never happen, but if it does. Print the raw token\n      printer.add_raw_token(raw_token);\n    }\n\n    last_token = parser_token;\n\n    raw_token = tokens.next();\n  }\n  var sweet_code = printer._output.get_code(eol);\n\n  return sweet_code;\n};\n\nBeautifier.prototype._handle_control_flow_open = function(printer, raw_token) {\n  var parser_token = {\n    text: raw_token.text,\n    type: raw_token.type\n  };\n  printer.set_space_before_token(raw_token.newlines || raw_token.whitespace_before !== '', true);\n  if (raw_token.newlines) {\n    printer.print_preserved_newlines(raw_token);\n  } else {\n    printer.set_space_before_token(raw_token.newlines || raw_token.whitespace_before !== '', true);\n  }\n  printer.print_token(raw_token);\n  printer.indent();\n  return parser_token;\n};\n\nBeautifier.prototype._handle_control_flow_close = function(printer, raw_token) {\n  var parser_token = {\n    text: raw_token.text,\n    type: raw_token.type\n  };\n\n  printer.deindent();\n  if (raw_token.newlines) {\n    printer.print_preserved_newlines(raw_token);\n  } else {\n    printer.set_space_before_token(raw_token.newlines || raw_token.whitespace_before !== '', true);\n  }\n  printer.print_token(raw_token);\n  return parser_token;\n};\n\nBeautifier.prototype._handle_tag_close = function(printer, raw_token, last_tag_token) {\n  var parser_token = {\n    text: raw_token.text,\n    type: raw_token.type\n  };\n  printer.alignment_size = 0;\n  last_tag_token.tag_complete = true;\n\n  printer.set_space_before_token(raw_token.newlines || raw_token.whitespace_before !== '', true);\n  if (last_tag_token.is_unformatted) {\n    printer.add_raw_token(raw_token);\n  } else {\n    if (last_tag_token.tag_start_char === '<') {\n      printer.set_space_before_token(raw_token.text[0] === '/', true); // space before />, no space before >\n      if (this._is_wrap_attributes_force_expand_multiline && last_tag_token.has_wrapped_attrs) {\n        printer.print_newline(false);\n      }\n    }\n    printer.print_token(raw_token);\n\n  }\n\n  if (last_tag_token.indent_content &&\n    !(last_tag_token.is_unformatted || last_tag_token.is_content_unformatted)) {\n    printer.indent();\n\n    // only indent once per opened tag\n    last_tag_token.indent_content = false;\n  }\n\n  if (!last_tag_token.is_inline_element &&\n    !(last_tag_token.is_unformatted || last_tag_token.is_content_unformatted)) {\n    printer.set_wrap_point();\n  }\n\n  return parser_token;\n};\n\nBeautifier.prototype._handle_inside_tag = function(printer, raw_token, last_tag_token, last_token) {\n  var wrapped = last_tag_token.has_wrapped_attrs;\n  var parser_token = {\n    text: raw_token.text,\n    type: raw_token.type\n  };\n\n  printer.set_space_before_token(raw_token.newlines || raw_token.whitespace_before !== '', true);\n  if (last_tag_token.is_unformatted) {\n    printer.add_raw_token(raw_token);\n  } else if (last_tag_token.tag_start_char === '{' && raw_token.type === TOKEN.TEXT) {\n    // For the insides of handlebars allow newlines or a single space between open and contents\n    if (printer.print_preserved_newlines(raw_token)) {\n      raw_token.newlines = 0;\n      printer.add_raw_token(raw_token);\n    } else {\n      printer.print_token(raw_token);\n    }\n  } else {\n    if (raw_token.type === TOKEN.ATTRIBUTE) {\n      printer.set_space_before_token(true);\n    } else if (raw_token.type === TOKEN.EQUALS) { //no space before =\n      printer.set_space_before_token(false);\n    } else if (raw_token.type === TOKEN.VALUE && raw_token.previous.type === TOKEN.EQUALS) { //no space before value\n      printer.set_space_before_token(false);\n    }\n\n    if (raw_token.type === TOKEN.ATTRIBUTE && last_tag_token.tag_start_char === '<') {\n      if (this._is_wrap_attributes_preserve || this._is_wrap_attributes_preserve_aligned) {\n        printer.traverse_whitespace(raw_token);\n        wrapped = wrapped || raw_token.newlines !== 0;\n      }\n\n      // Wrap for 'force' options, and if the number of attributes is at least that specified in 'wrap_attributes_min_attrs':\n      // 1. always wrap the second and beyond attributes\n      // 2. wrap the first attribute only if 'force-expand-multiline' is specified\n      if (this._is_wrap_attributes_force &&\n        last_tag_token.attr_count >= this._options.wrap_attributes_min_attrs &&\n        (last_token.type !== TOKEN.TAG_OPEN || // ie. second attribute and beyond\n          this._is_wrap_attributes_force_expand_multiline)) {\n        printer.print_newline(false);\n        wrapped = true;\n      }\n    }\n    printer.print_token(raw_token);\n    wrapped = wrapped || printer.previous_token_wrapped();\n    last_tag_token.has_wrapped_attrs = wrapped;\n  }\n  return parser_token;\n};\n\nBeautifier.prototype._handle_text = function(printer, raw_token, last_tag_token) {\n  var parser_token = {\n    text: raw_token.text,\n    type: 'TK_CONTENT'\n  };\n  if (last_tag_token.custom_beautifier_name) { //check if we need to format javascript\n    this._print_custom_beatifier_text(printer, raw_token, last_tag_token);\n  } else if (last_tag_token.is_unformatted || last_tag_token.is_content_unformatted) {\n    printer.add_raw_token(raw_token);\n  } else {\n    printer.traverse_whitespace(raw_token);\n    printer.print_token(raw_token);\n  }\n  return parser_token;\n};\n\nBeautifier.prototype._print_custom_beatifier_text = function(printer, raw_token, last_tag_token) {\n  var local = this;\n  if (raw_token.text !== '') {\n\n    var text = raw_token.text,\n      _beautifier,\n      script_indent_level = 1,\n      pre = '',\n      post = '';\n    if (last_tag_token.custom_beautifier_name === 'javascript' && typeof this._js_beautify === 'function') {\n      _beautifier = this._js_beautify;\n    } else if (last_tag_token.custom_beautifier_name === 'css' && typeof this._css_beautify === 'function') {\n      _beautifier = this._css_beautify;\n    } else if (last_tag_token.custom_beautifier_name === 'html') {\n      _beautifier = function(html_source, options) {\n        var beautifier = new Beautifier(html_source, options, local._js_beautify, local._css_beautify);\n        return beautifier.beautify();\n      };\n    }\n\n    if (this._options.indent_scripts === \"keep\") {\n      script_indent_level = 0;\n    } else if (this._options.indent_scripts === \"separate\") {\n      script_indent_level = -printer.indent_level;\n    }\n\n    var indentation = printer.get_full_indent(script_indent_level);\n\n    // if there is at least one empty line at the end of this text, strip it\n    // we'll be adding one back after the text but before the containing tag.\n    text = text.replace(/\\n[ \\t]*$/, '');\n\n    // Handle the case where content is wrapped in a comment or cdata.\n    if (last_tag_token.custom_beautifier_name !== 'html' &&\n      text[0] === '<' && text.match(/^(<!--|<!\\[CDATA\\[)/)) {\n      var matched = /^(<!--[^\\n]*|<!\\[CDATA\\[)(\\n?)([ \\t\\n]*)([\\s\\S]*)(-->|]]>)$/.exec(text);\n\n      // if we start to wrap but don't finish, print raw\n      if (!matched) {\n        printer.add_raw_token(raw_token);\n        return;\n      }\n\n      pre = indentation + matched[1] + '\\n';\n      text = matched[4];\n      if (matched[5]) {\n        post = indentation + matched[5];\n      }\n\n      // if there is at least one empty line at the end of this text, strip it\n      // we'll be adding one back after the text but before the containing tag.\n      text = text.replace(/\\n[ \\t]*$/, '');\n\n      if (matched[2] || matched[3].indexOf('\\n') !== -1) {\n        // if the first line of the non-comment text has spaces\n        // use that as the basis for indenting in null case.\n        matched = matched[3].match(/[ \\t]+$/);\n        if (matched) {\n          raw_token.whitespace_before = matched[0];\n        }\n      }\n    }\n\n    if (text) {\n      if (_beautifier) {\n\n        // call the Beautifier if avaliable\n        var Child_options = function() {\n          this.eol = '\\n';\n        };\n        Child_options.prototype = this._options.raw_options;\n        var child_options = new Child_options();\n        text = _beautifier(indentation + text, child_options);\n      } else {\n        // simply indent the string otherwise\n        var white = raw_token.whitespace_before;\n        if (white) {\n          text = text.replace(new RegExp('\\n(' + white + ')?', 'g'), '\\n');\n        }\n\n        text = indentation + text.replace(/\\n/g, '\\n' + indentation);\n      }\n    }\n\n    if (pre) {\n      if (!text) {\n        text = pre + post;\n      } else {\n        text = pre + text + '\\n' + post;\n      }\n    }\n\n    printer.print_newline(false);\n    if (text) {\n      raw_token.text = text;\n      raw_token.whitespace_before = '';\n      raw_token.newlines = 0;\n      printer.add_raw_token(raw_token);\n      printer.print_newline(true);\n    }\n  }\n};\n\nBeautifier.prototype._handle_tag_open = function(printer, raw_token, last_tag_token, last_token, tokens) {\n  var parser_token = this._get_tag_open_token(raw_token);\n\n  if ((last_tag_token.is_unformatted || last_tag_token.is_content_unformatted) &&\n    !last_tag_token.is_empty_element &&\n    raw_token.type === TOKEN.TAG_OPEN && !parser_token.is_start_tag) {\n    // End element tags for unformatted or content_unformatted elements\n    // are printed raw to keep any newlines inside them exactly the same.\n    printer.add_raw_token(raw_token);\n    parser_token.start_tag_token = this._tag_stack.try_pop(parser_token.tag_name);\n  } else {\n    printer.traverse_whitespace(raw_token);\n    this._set_tag_position(printer, raw_token, parser_token, last_tag_token, last_token);\n    if (!parser_token.is_inline_element) {\n      printer.set_wrap_point();\n    }\n    printer.print_token(raw_token);\n  }\n\n  // count the number of attributes\n  if (parser_token.is_start_tag && this._is_wrap_attributes_force) {\n    var peek_index = 0;\n    var peek_token;\n    do {\n      peek_token = tokens.peek(peek_index);\n      if (peek_token.type === TOKEN.ATTRIBUTE) {\n        parser_token.attr_count += 1;\n      }\n      peek_index += 1;\n    } while (peek_token.type !== TOKEN.EOF && peek_token.type !== TOKEN.TAG_CLOSE);\n  }\n\n  //indent attributes an auto, forced, aligned or forced-align line-wrap\n  if (this._is_wrap_attributes_force_aligned || this._is_wrap_attributes_aligned_multiple || this._is_wrap_attributes_preserve_aligned) {\n    parser_token.alignment_size = raw_token.text.length + 1;\n  }\n\n  if (!parser_token.tag_complete && !parser_token.is_unformatted) {\n    printer.alignment_size = parser_token.alignment_size;\n  }\n\n  return parser_token;\n};\n\nvar TagOpenParserToken = function(options, parent, raw_token) {\n  this.parent = parent || null;\n  this.text = '';\n  this.type = 'TK_TAG_OPEN';\n  this.tag_name = '';\n  this.is_inline_element = false;\n  this.is_unformatted = false;\n  this.is_content_unformatted = false;\n  this.is_empty_element = false;\n  this.is_start_tag = false;\n  this.is_end_tag = false;\n  this.indent_content = false;\n  this.multiline_content = false;\n  this.custom_beautifier_name = null;\n  this.start_tag_token = null;\n  this.attr_count = 0;\n  this.has_wrapped_attrs = false;\n  this.alignment_size = 0;\n  this.tag_complete = false;\n  this.tag_start_char = '';\n  this.tag_check = '';\n\n  if (!raw_token) {\n    this.tag_complete = true;\n  } else {\n    var tag_check_match;\n\n    this.tag_start_char = raw_token.text[0];\n    this.text = raw_token.text;\n\n    if (this.tag_start_char === '<') {\n      tag_check_match = raw_token.text.match(/^<([^\\s>]*)/);\n      this.tag_check = tag_check_match ? tag_check_match[1] : '';\n    } else {\n      tag_check_match = raw_token.text.match(/^{{~?(?:[\\^]|#\\*?)?([^\\s}]+)/);\n      this.tag_check = tag_check_match ? tag_check_match[1] : '';\n\n      // handle \"{{#> myPartial}}\" or \"{{~#> myPartial}}\"\n      if ((raw_token.text.startsWith('{{#>') || raw_token.text.startsWith('{{~#>')) && this.tag_check[0] === '>') {\n        if (this.tag_check === '>' && raw_token.next !== null) {\n          this.tag_check = raw_token.next.text.split(' ')[0];\n        } else {\n          this.tag_check = raw_token.text.split('>')[1];\n        }\n      }\n    }\n\n    this.tag_check = this.tag_check.toLowerCase();\n\n    if (raw_token.type === TOKEN.COMMENT) {\n      this.tag_complete = true;\n    }\n\n    this.is_start_tag = this.tag_check.charAt(0) !== '/';\n    this.tag_name = !this.is_start_tag ? this.tag_check.substr(1) : this.tag_check;\n    this.is_end_tag = !this.is_start_tag ||\n      (raw_token.closed && raw_token.closed.text === '/>');\n\n    // if whitespace handler ~ included (i.e. {{~#if true}}), handlebars tags start at pos 3 not pos 2\n    var handlebar_starts = 2;\n    if (this.tag_start_char === '{' && this.text.length >= 3) {\n      if (this.text.charAt(2) === '~') {\n        handlebar_starts = 3;\n      }\n    }\n\n    // handlebars tags that don't start with # or ^ are single_tags, and so also start and end.\n    // if they start with # or ^, they are still considered single tags if indenting of handlebars is set to false\n    this.is_end_tag = this.is_end_tag ||\n      (this.tag_start_char === '{' && (!options.indent_handlebars || this.text.length < 3 || (/[^#\\^]/.test(this.text.charAt(handlebar_starts)))));\n  }\n};\n\nBeautifier.prototype._get_tag_open_token = function(raw_token) { //function to get a full tag and parse its type\n  var parser_token = new TagOpenParserToken(this._options, this._tag_stack.get_parser_token(), raw_token);\n\n  parser_token.alignment_size = this._options.wrap_attributes_indent_size;\n\n  parser_token.is_end_tag = parser_token.is_end_tag ||\n    in_array(parser_token.tag_check, this._options.void_elements);\n\n  parser_token.is_empty_element = parser_token.tag_complete ||\n    (parser_token.is_start_tag && parser_token.is_end_tag);\n\n  parser_token.is_unformatted = !parser_token.tag_complete && in_array(parser_token.tag_check, this._options.unformatted);\n  parser_token.is_content_unformatted = !parser_token.is_empty_element && in_array(parser_token.tag_check, this._options.content_unformatted);\n  parser_token.is_inline_element = in_array(parser_token.tag_name, this._options.inline) || (this._options.inline_custom_elements && parser_token.tag_name.includes(\"-\")) || parser_token.tag_start_char === '{';\n\n  return parser_token;\n};\n\nBeautifier.prototype._set_tag_position = function(printer, raw_token, parser_token, last_tag_token, last_token) {\n\n  if (!parser_token.is_empty_element) {\n    if (parser_token.is_end_tag) { //this tag is a double tag so check for tag-ending\n      parser_token.start_tag_token = this._tag_stack.try_pop(parser_token.tag_name); //remove it and all ancestors\n    } else { // it's a start-tag\n      // check if this tag is starting an element that has optional end element\n      // and do an ending needed\n      if (this._do_optional_end_element(parser_token)) {\n        if (!parser_token.is_inline_element) {\n          printer.print_newline(false);\n        }\n      }\n\n      this._tag_stack.record_tag(parser_token); //push it on the tag stack\n\n      if ((parser_token.tag_name === 'script' || parser_token.tag_name === 'style') &&\n        !(parser_token.is_unformatted || parser_token.is_content_unformatted)) {\n        parser_token.custom_beautifier_name = get_custom_beautifier_name(parser_token.tag_check, raw_token);\n      }\n    }\n  }\n\n  if (in_array(parser_token.tag_check, this._options.extra_liners)) { //check if this double needs an extra line\n    printer.print_newline(false);\n    if (!printer._output.just_added_blankline()) {\n      printer.print_newline(true);\n    }\n  }\n\n  if (parser_token.is_empty_element) { //if this tag name is a single tag type (either in the list or has a closing /)\n\n    // if you hit an else case, reset the indent level if you are inside an:\n    // 'if', 'unless', or 'each' block.\n    if (parser_token.tag_start_char === '{' && parser_token.tag_check === 'else') {\n      this._tag_stack.indent_to_tag(['if', 'unless', 'each']);\n      parser_token.indent_content = true;\n      // Don't add a newline if opening {{#if}} tag is on the current line\n      var foundIfOnCurrentLine = printer.current_line_has_match(/{{#if/);\n      if (!foundIfOnCurrentLine) {\n        printer.print_newline(false);\n      }\n    }\n\n    // Don't add a newline before elements that should remain where they are.\n    if (parser_token.tag_name === '!--' && last_token.type === TOKEN.TAG_CLOSE &&\n      last_tag_token.is_end_tag && parser_token.text.indexOf('\\n') === -1) {\n      //Do nothing. Leave comments on same line.\n    } else {\n      if (!(parser_token.is_inline_element || parser_token.is_unformatted)) {\n        printer.print_newline(false);\n      }\n      this._calcluate_parent_multiline(printer, parser_token);\n    }\n  } else if (parser_token.is_end_tag) { //this tag is a double tag so check for tag-ending\n    var do_end_expand = false;\n\n    // deciding whether a block is multiline should not be this hard\n    do_end_expand = parser_token.start_tag_token && parser_token.start_tag_token.multiline_content;\n    do_end_expand = do_end_expand || (!parser_token.is_inline_element &&\n      !(last_tag_token.is_inline_element || last_tag_token.is_unformatted) &&\n      !(last_token.type === TOKEN.TAG_CLOSE && parser_token.start_tag_token === last_tag_token) &&\n      last_token.type !== 'TK_CONTENT'\n    );\n\n    if (parser_token.is_content_unformatted || parser_token.is_unformatted) {\n      do_end_expand = false;\n    }\n\n    if (do_end_expand) {\n      printer.print_newline(false);\n    }\n  } else { // it's a start-tag\n    parser_token.indent_content = !parser_token.custom_beautifier_name;\n\n    if (parser_token.tag_start_char === '<') {\n      if (parser_token.tag_name === 'html') {\n        parser_token.indent_content = this._options.indent_inner_html;\n      } else if (parser_token.tag_name === 'head') {\n        parser_token.indent_content = this._options.indent_head_inner_html;\n      } else if (parser_token.tag_name === 'body') {\n        parser_token.indent_content = this._options.indent_body_inner_html;\n      }\n    }\n\n    if (!(parser_token.is_inline_element || parser_token.is_unformatted) &&\n      (last_token.type !== 'TK_CONTENT' || parser_token.is_content_unformatted)) {\n      printer.print_newline(false);\n    }\n\n    this._calcluate_parent_multiline(printer, parser_token);\n  }\n};\n\nBeautifier.prototype._calcluate_parent_multiline = function(printer, parser_token) {\n  if (parser_token.parent && printer._output.just_added_newline() &&\n    !((parser_token.is_inline_element || parser_token.is_unformatted) && parser_token.parent.is_inline_element)) {\n    parser_token.parent.multiline_content = true;\n  }\n};\n\n//To be used for <p> tag special case:\nvar p_closers = ['address', 'article', 'aside', 'blockquote', 'details', 'div', 'dl', 'fieldset', 'figcaption', 'figure', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'header', 'hr', 'main', 'menu', 'nav', 'ol', 'p', 'pre', 'section', 'table', 'ul'];\nvar p_parent_excludes = ['a', 'audio', 'del', 'ins', 'map', 'noscript', 'video'];\n\nBeautifier.prototype._do_optional_end_element = function(parser_token) {\n  var result = null;\n  // NOTE: cases of \"if there is no more content in the parent element\"\n  // are handled automatically by the beautifier.\n  // It assumes parent or ancestor close tag closes all children.\n  // https://www.w3.org/TR/html5/syntax.html#optional-tags\n  if (parser_token.is_empty_element || !parser_token.is_start_tag || !parser_token.parent) {\n    return;\n\n  }\n\n  if (parser_token.tag_name === 'body') {\n    // A head element’s end tag may be omitted if the head element is not immediately followed by a space character or a comment.\n    result = result || this._tag_stack.try_pop('head');\n\n    //} else if (parser_token.tag_name === 'body') {\n    // DONE: A body element’s end tag may be omitted if the body element is not immediately followed by a comment.\n\n  } else if (parser_token.tag_name === 'li') {\n    // An li element’s end tag may be omitted if the li element is immediately followed by another li element or if there is no more content in the parent element.\n    result = result || this._tag_stack.try_pop('li', ['ol', 'ul', 'menu']);\n\n  } else if (parser_token.tag_name === 'dd' || parser_token.tag_name === 'dt') {\n    // A dd element’s end tag may be omitted if the dd element is immediately followed by another dd element or a dt element, or if there is no more content in the parent element.\n    // A dt element’s end tag may be omitted if the dt element is immediately followed by another dt element or a dd element.\n    result = result || this._tag_stack.try_pop('dt', ['dl']);\n    result = result || this._tag_stack.try_pop('dd', ['dl']);\n\n\n  } else if (parser_token.parent.tag_name === 'p' && p_closers.indexOf(parser_token.tag_name) !== -1) {\n    // IMPORTANT: this else-if works because p_closers has no overlap with any other element we look for in this method\n    // check for the parent element is an HTML element that is not an <a>, <audio>, <del>, <ins>, <map>, <noscript>, or <video> element,  or an autonomous custom element.\n    // To do this right, this needs to be coded as an inclusion of the inverse of the exclusion above.\n    // But to start with (if we ignore \"autonomous custom elements\") the exclusion would be fine.\n    var p_parent = parser_token.parent.parent;\n    if (!p_parent || p_parent_excludes.indexOf(p_parent.tag_name) === -1) {\n      result = result || this._tag_stack.try_pop('p');\n    }\n  } else if (parser_token.tag_name === 'rp' || parser_token.tag_name === 'rt') {\n    // An rt element’s end tag may be omitted if the rt element is immediately followed by an rt or rp element, or if there is no more content in the parent element.\n    // An rp element’s end tag may be omitted if the rp element is immediately followed by an rt or rp element, or if there is no more content in the parent element.\n    result = result || this._tag_stack.try_pop('rt', ['ruby', 'rtc']);\n    result = result || this._tag_stack.try_pop('rp', ['ruby', 'rtc']);\n\n  } else if (parser_token.tag_name === 'optgroup') {\n    // An optgroup element’s end tag may be omitted if the optgroup element is immediately followed by another optgroup element, or if there is no more content in the parent element.\n    // An option element’s end tag may be omitted if the option element is immediately followed by another option element, or if it is immediately followed by an optgroup element, or if there is no more content in the parent element.\n    result = result || this._tag_stack.try_pop('optgroup', ['select']);\n    //result = result || this._tag_stack.try_pop('option', ['select']);\n\n  } else if (parser_token.tag_name === 'option') {\n    // An option element’s end tag may be omitted if the option element is immediately followed by another option element, or if it is immediately followed by an optgroup element, or if there is no more content in the parent element.\n    result = result || this._tag_stack.try_pop('option', ['select', 'datalist', 'optgroup']);\n\n  } else if (parser_token.tag_name === 'colgroup') {\n    // DONE: A colgroup element’s end tag may be omitted if the colgroup element is not immediately followed by a space character or a comment.\n    // A caption element's end tag may be ommitted if a colgroup, thead, tfoot, tbody, or tr element is started.\n    result = result || this._tag_stack.try_pop('caption', ['table']);\n\n  } else if (parser_token.tag_name === 'thead') {\n    // A colgroup element's end tag may be ommitted if a thead, tfoot, tbody, or tr element is started.\n    // A caption element's end tag may be ommitted if a colgroup, thead, tfoot, tbody, or tr element is started.\n    result = result || this._tag_stack.try_pop('caption', ['table']);\n    result = result || this._tag_stack.try_pop('colgroup', ['table']);\n\n    //} else if (parser_token.tag_name === 'caption') {\n    // DONE: A caption element’s end tag may be omitted if the caption element is not immediately followed by a space character or a comment.\n\n  } else if (parser_token.tag_name === 'tbody' || parser_token.tag_name === 'tfoot') {\n    // A thead element’s end tag may be omitted if the thead element is immediately followed by a tbody or tfoot element.\n    // A tbody element’s end tag may be omitted if the tbody element is immediately followed by a tbody or tfoot element, or if there is no more content in the parent element.\n    // A colgroup element's end tag may be ommitted if a thead, tfoot, tbody, or tr element is started.\n    // A caption element's end tag may be ommitted if a colgroup, thead, tfoot, tbody, or tr element is started.\n    result = result || this._tag_stack.try_pop('caption', ['table']);\n    result = result || this._tag_stack.try_pop('colgroup', ['table']);\n    result = result || this._tag_stack.try_pop('thead', ['table']);\n    result = result || this._tag_stack.try_pop('tbody', ['table']);\n\n    //} else if (parser_token.tag_name === 'tfoot') {\n    // DONE: A tfoot element’s end tag may be omitted if there is no more content in the parent element.\n\n  } else if (parser_token.tag_name === 'tr') {\n    // A tr element’s end tag may be omitted if the tr element is immediately followed by another tr element, or if there is no more content in the parent element.\n    // A colgroup element's end tag may be ommitted if a thead, tfoot, tbody, or tr element is started.\n    // A caption element's end tag may be ommitted if a colgroup, thead, tfoot, tbody, or tr element is started.\n    result = result || this._tag_stack.try_pop('caption', ['table']);\n    result = result || this._tag_stack.try_pop('colgroup', ['table']);\n    result = result || this._tag_stack.try_pop('tr', ['table', 'thead', 'tbody', 'tfoot']);\n\n  } else if (parser_token.tag_name === 'th' || parser_token.tag_name === 'td') {\n    // A td element’s end tag may be omitted if the td element is immediately followed by a td or th element, or if there is no more content in the parent element.\n    // A th element’s end tag may be omitted if the th element is immediately followed by a td or th element, or if there is no more content in the parent element.\n    result = result || this._tag_stack.try_pop('td', ['table', 'thead', 'tbody', 'tfoot', 'tr']);\n    result = result || this._tag_stack.try_pop('th', ['table', 'thead', 'tbody', 'tfoot', 'tr']);\n  }\n\n  // Start element omission not handled currently\n  // A head element’s start tag may be omitted if the element is empty, or if the first thing inside the head element is an element.\n  // A tbody element’s start tag may be omitted if the first thing inside the tbody element is a tr element, and if the element is not immediately preceded by a tbody, thead, or tfoot element whose end tag has been omitted. (It can’t be omitted if the element is empty.)\n  // A colgroup element’s start tag may be omitted if the first thing inside the colgroup element is a col element, and if the element is not immediately preceded by another colgroup element whose end tag has been omitted. (It can’t be omitted if the element is empty.)\n\n  // Fix up the parent of the parser token\n  parser_token.parent = this._tag_stack.get_parser_token();\n\n  return result;\n};\n\nmodule.exports.Beautifier = Beautifier;\n\n\n/***/ }),\n/* 20 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar BaseOptions = (__webpack_require__(6).Options);\n\nfunction Options(options) {\n  BaseOptions.call(this, options, 'html');\n  if (this.templating.length === 1 && this.templating[0] === 'auto') {\n    this.templating = ['django', 'erb', 'handlebars', 'php'];\n  }\n\n  this.indent_inner_html = this._get_boolean('indent_inner_html');\n  this.indent_body_inner_html = this._get_boolean('indent_body_inner_html', true);\n  this.indent_head_inner_html = this._get_boolean('indent_head_inner_html', true);\n\n  this.indent_handlebars = this._get_boolean('indent_handlebars', true);\n  this.wrap_attributes = this._get_selection('wrap_attributes',\n    ['auto', 'force', 'force-aligned', 'force-expand-multiline', 'aligned-multiple', 'preserve', 'preserve-aligned']);\n  this.wrap_attributes_min_attrs = this._get_number('wrap_attributes_min_attrs', 2);\n  this.wrap_attributes_indent_size = this._get_number('wrap_attributes_indent_size', this.indent_size);\n  this.extra_liners = this._get_array('extra_liners', ['head', 'body', '/html']);\n\n  // Block vs inline elements\n  // https://developer.mozilla.org/en-US/docs/Web/HTML/Block-level_elements\n  // https://developer.mozilla.org/en-US/docs/Web/HTML/Inline_elements\n  // https://www.w3.org/TR/html5/dom.html#phrasing-content\n  this.inline = this._get_array('inline', [\n    'a', 'abbr', 'area', 'audio', 'b', 'bdi', 'bdo', 'br', 'button', 'canvas', 'cite',\n    'code', 'data', 'datalist', 'del', 'dfn', 'em', 'embed', 'i', 'iframe', 'img',\n    'input', 'ins', 'kbd', 'keygen', 'label', 'map', 'mark', 'math', 'meter', 'noscript',\n    'object', 'output', 'progress', 'q', 'ruby', 's', 'samp', /* 'script', */ 'select', 'small',\n    'span', 'strong', 'sub', 'sup', 'svg', 'template', 'textarea', 'time', 'u', 'var',\n    'video', 'wbr', 'text',\n    // obsolete inline tags\n    'acronym', 'big', 'strike', 'tt'\n  ]);\n  this.inline_custom_elements = this._get_boolean('inline_custom_elements', true);\n  this.void_elements = this._get_array('void_elements', [\n    // HTLM void elements - aka self-closing tags - aka singletons\n    // https://www.w3.org/html/wg/drafts/html/master/syntax.html#void-elements\n    'area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input', 'keygen',\n    'link', 'menuitem', 'meta', 'param', 'source', 'track', 'wbr',\n    // NOTE: Optional tags are too complex for a simple list\n    // they are hard coded in _do_optional_end_element\n\n    // Doctype and xml elements\n    '!doctype', '?xml',\n\n    // obsolete tags\n    // basefont: https://www.computerhope.com/jargon/h/html-basefont-tag.htm\n    // isndex: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/isindex\n    'basefont', 'isindex'\n  ]);\n  this.unformatted = this._get_array('unformatted', []);\n  this.content_unformatted = this._get_array('content_unformatted', [\n    'pre', 'textarea'\n  ]);\n  this.unformatted_content_delimiter = this._get_characters('unformatted_content_delimiter');\n  this.indent_scripts = this._get_selection('indent_scripts', ['normal', 'keep', 'separate']);\n\n}\nOptions.prototype = new BaseOptions();\n\n\n\nmodule.exports.Options = Options;\n\n\n/***/ }),\n/* 21 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar BaseTokenizer = (__webpack_require__(9).Tokenizer);\nvar BASETOKEN = (__webpack_require__(9).TOKEN);\nvar Directives = (__webpack_require__(13).Directives);\nvar TemplatablePattern = (__webpack_require__(14).TemplatablePattern);\nvar Pattern = (__webpack_require__(12).Pattern);\n\nvar TOKEN = {\n  TAG_OPEN: 'TK_TAG_OPEN',\n  TAG_CLOSE: 'TK_TAG_CLOSE',\n  CONTROL_FLOW_OPEN: 'TK_CONTROL_FLOW_OPEN',\n  CONTROL_FLOW_CLOSE: 'TK_CONTROL_FLOW_CLOSE',\n  ATTRIBUTE: 'TK_ATTRIBUTE',\n  EQUALS: 'TK_EQUALS',\n  VALUE: 'TK_VALUE',\n  COMMENT: 'TK_COMMENT',\n  TEXT: 'TK_TEXT',\n  UNKNOWN: 'TK_UNKNOWN',\n  START: BASETOKEN.START,\n  RAW: BASETOKEN.RAW,\n  EOF: BASETOKEN.EOF\n};\n\nvar directives_core = new Directives(/<\\!--/, /-->/);\n\nvar Tokenizer = function(input_string, options) {\n  BaseTokenizer.call(this, input_string, options);\n  this._current_tag_name = '';\n\n  // Words end at whitespace or when a tag starts\n  // if we are indenting handlebars, they are considered tags\n  var templatable_reader = new TemplatablePattern(this._input).read_options(this._options);\n  var pattern_reader = new Pattern(this._input);\n\n  this.__patterns = {\n    word: templatable_reader.until(/[\\n\\r\\t <]/),\n    word_control_flow_close_excluded: templatable_reader.until(/[\\n\\r\\t <}]/),\n    single_quote: templatable_reader.until_after(/'/),\n    double_quote: templatable_reader.until_after(/\"/),\n    attribute: templatable_reader.until(/[\\n\\r\\t =>]|\\/>/),\n    element_name: templatable_reader.until(/[\\n\\r\\t >\\/]/),\n\n    angular_control_flow_start: pattern_reader.matching(/\\@[a-zA-Z]+[^({]*[({]/),\n    handlebars_comment: pattern_reader.starting_with(/{{!--/).until_after(/--}}/),\n    handlebars: pattern_reader.starting_with(/{{/).until_after(/}}/),\n    handlebars_open: pattern_reader.until(/[\\n\\r\\t }]/),\n    handlebars_raw_close: pattern_reader.until(/}}/),\n    comment: pattern_reader.starting_with(/<!--/).until_after(/-->/),\n    cdata: pattern_reader.starting_with(/<!\\[CDATA\\[/).until_after(/]]>/),\n    // https://en.wikipedia.org/wiki/Conditional_comment\n    conditional_comment: pattern_reader.starting_with(/<!\\[/).until_after(/]>/),\n    processing: pattern_reader.starting_with(/<\\?/).until_after(/\\?>/)\n  };\n\n  if (this._options.indent_handlebars) {\n    this.__patterns.word = this.__patterns.word.exclude('handlebars');\n    this.__patterns.word_control_flow_close_excluded = this.__patterns.word_control_flow_close_excluded.exclude('handlebars');\n  }\n\n  this._unformatted_content_delimiter = null;\n\n  if (this._options.unformatted_content_delimiter) {\n    var literal_regexp = this._input.get_literal_regexp(this._options.unformatted_content_delimiter);\n    this.__patterns.unformatted_content_delimiter =\n      pattern_reader.matching(literal_regexp)\n      .until_after(literal_regexp);\n  }\n};\nTokenizer.prototype = new BaseTokenizer();\n\nTokenizer.prototype._is_comment = function(current_token) { // jshint unused:false\n  return false; //current_token.type === TOKEN.COMMENT || current_token.type === TOKEN.UNKNOWN;\n};\n\nTokenizer.prototype._is_opening = function(current_token) {\n  return current_token.type === TOKEN.TAG_OPEN || current_token.type === TOKEN.CONTROL_FLOW_OPEN;\n};\n\nTokenizer.prototype._is_closing = function(current_token, open_token) {\n  return (current_token.type === TOKEN.TAG_CLOSE &&\n    (open_token && (\n      ((current_token.text === '>' || current_token.text === '/>') && open_token.text[0] === '<') ||\n      (current_token.text === '}}' && open_token.text[0] === '{' && open_token.text[1] === '{')))\n  ) || (current_token.type === TOKEN.CONTROL_FLOW_CLOSE &&\n    (current_token.text === '}' && open_token.text.endsWith('{')));\n};\n\nTokenizer.prototype._reset = function() {\n  this._current_tag_name = '';\n};\n\nTokenizer.prototype._get_next_token = function(previous_token, open_token) { // jshint unused:false\n  var token = null;\n  this._readWhitespace();\n  var c = this._input.peek();\n\n  if (c === null) {\n    return this._create_token(TOKEN.EOF, '');\n  }\n\n  token = token || this._read_open_handlebars(c, open_token);\n  token = token || this._read_attribute(c, previous_token, open_token);\n  token = token || this._read_close(c, open_token);\n  token = token || this._read_script_and_style(c, previous_token);\n  token = token || this._read_control_flows(c, open_token);\n  token = token || this._read_raw_content(c, previous_token, open_token);\n  token = token || this._read_content_word(c, open_token);\n  token = token || this._read_comment_or_cdata(c);\n  token = token || this._read_processing(c);\n  token = token || this._read_open(c, open_token);\n  token = token || this._create_token(TOKEN.UNKNOWN, this._input.next());\n\n  return token;\n};\n\nTokenizer.prototype._read_comment_or_cdata = function(c) { // jshint unused:false\n  var token = null;\n  var resulting_string = null;\n  var directives = null;\n\n  if (c === '<') {\n    var peek1 = this._input.peek(1);\n    // We treat all comments as literals, even more than preformatted tags\n    // we only look for the appropriate closing marker\n    if (peek1 === '!') {\n      resulting_string = this.__patterns.comment.read();\n\n      // only process directive on html comments\n      if (resulting_string) {\n        directives = directives_core.get_directives(resulting_string);\n        if (directives && directives.ignore === 'start') {\n          resulting_string += directives_core.readIgnored(this._input);\n        }\n      } else {\n        resulting_string = this.__patterns.cdata.read();\n      }\n    }\n\n    if (resulting_string) {\n      token = this._create_token(TOKEN.COMMENT, resulting_string);\n      token.directives = directives;\n    }\n  }\n\n  return token;\n};\n\nTokenizer.prototype._read_processing = function(c) { // jshint unused:false\n  var token = null;\n  var resulting_string = null;\n  var directives = null;\n\n  if (c === '<') {\n    var peek1 = this._input.peek(1);\n    if (peek1 === '!' || peek1 === '?') {\n      resulting_string = this.__patterns.conditional_comment.read();\n      resulting_string = resulting_string || this.__patterns.processing.read();\n    }\n\n    if (resulting_string) {\n      token = this._create_token(TOKEN.COMMENT, resulting_string);\n      token.directives = directives;\n    }\n  }\n\n  return token;\n};\n\nTokenizer.prototype._read_open = function(c, open_token) {\n  var resulting_string = null;\n  var token = null;\n  if (!open_token || open_token.type === TOKEN.CONTROL_FLOW_OPEN) {\n    if (c === '<') {\n\n      resulting_string = this._input.next();\n      if (this._input.peek() === '/') {\n        resulting_string += this._input.next();\n      }\n      resulting_string += this.__patterns.element_name.read();\n      token = this._create_token(TOKEN.TAG_OPEN, resulting_string);\n    }\n  }\n  return token;\n};\n\nTokenizer.prototype._read_open_handlebars = function(c, open_token) {\n  var resulting_string = null;\n  var token = null;\n  if (!open_token || open_token.type === TOKEN.CONTROL_FLOW_OPEN) {\n    if ((this._options.templating.includes('angular') || this._options.indent_handlebars) && c === '{' && this._input.peek(1) === '{') {\n      if (this._options.indent_handlebars && this._input.peek(2) === '!') {\n        resulting_string = this.__patterns.handlebars_comment.read();\n        resulting_string = resulting_string || this.__patterns.handlebars.read();\n        token = this._create_token(TOKEN.COMMENT, resulting_string);\n      } else {\n        resulting_string = this.__patterns.handlebars_open.read();\n        token = this._create_token(TOKEN.TAG_OPEN, resulting_string);\n      }\n    }\n  }\n  return token;\n};\n\nTokenizer.prototype._read_control_flows = function(c, open_token) {\n  var resulting_string = '';\n  var token = null;\n  // Only check for control flows if angular templating is set\n  if (!this._options.templating.includes('angular')) {\n    return token;\n  }\n\n  if (c === '@') {\n    resulting_string = this.__patterns.angular_control_flow_start.read();\n    if (resulting_string === '') {\n      return token;\n    }\n\n    var opening_parentheses_count = resulting_string.endsWith('(') ? 1 : 0;\n    var closing_parentheses_count = 0;\n    // The opening brace of the control flow is where the number of opening and closing parentheses equal\n    // e.g. @if({value: true} !== null) { \n    while (!(resulting_string.endsWith('{') && opening_parentheses_count === closing_parentheses_count)) {\n      var next_char = this._input.next();\n      if (next_char === null) {\n        break;\n      } else if (next_char === '(') {\n        opening_parentheses_count++;\n      } else if (next_char === ')') {\n        closing_parentheses_count++;\n      }\n      resulting_string += next_char;\n    }\n    token = this._create_token(TOKEN.CONTROL_FLOW_OPEN, resulting_string);\n  } else if (c === '}' && open_token && open_token.type === TOKEN.CONTROL_FLOW_OPEN) {\n    resulting_string = this._input.next();\n    token = this._create_token(TOKEN.CONTROL_FLOW_CLOSE, resulting_string);\n  }\n  return token;\n};\n\n\nTokenizer.prototype._read_close = function(c, open_token) {\n  var resulting_string = null;\n  var token = null;\n  if (open_token && open_token.type === TOKEN.TAG_OPEN) {\n    if (open_token.text[0] === '<' && (c === '>' || (c === '/' && this._input.peek(1) === '>'))) {\n      resulting_string = this._input.next();\n      if (c === '/') { //  for close tag \"/>\"\n        resulting_string += this._input.next();\n      }\n      token = this._create_token(TOKEN.TAG_CLOSE, resulting_string);\n    } else if (open_token.text[0] === '{' && c === '}' && this._input.peek(1) === '}') {\n      this._input.next();\n      this._input.next();\n      token = this._create_token(TOKEN.TAG_CLOSE, '}}');\n    }\n  }\n\n  return token;\n};\n\nTokenizer.prototype._read_attribute = function(c, previous_token, open_token) {\n  var token = null;\n  var resulting_string = '';\n  if (open_token && open_token.text[0] === '<') {\n\n    if (c === '=') {\n      token = this._create_token(TOKEN.EQUALS, this._input.next());\n    } else if (c === '\"' || c === \"'\") {\n      var content = this._input.next();\n      if (c === '\"') {\n        content += this.__patterns.double_quote.read();\n      } else {\n        content += this.__patterns.single_quote.read();\n      }\n      token = this._create_token(TOKEN.VALUE, content);\n    } else {\n      resulting_string = this.__patterns.attribute.read();\n\n      if (resulting_string) {\n        if (previous_token.type === TOKEN.EQUALS) {\n          token = this._create_token(TOKEN.VALUE, resulting_string);\n        } else {\n          token = this._create_token(TOKEN.ATTRIBUTE, resulting_string);\n        }\n      }\n    }\n  }\n  return token;\n};\n\nTokenizer.prototype._is_content_unformatted = function(tag_name) {\n  // void_elements have no content and so cannot have unformatted content\n  // script and style tags should always be read as unformatted content\n  // finally content_unformatted and unformatted element contents are unformatted\n  return this._options.void_elements.indexOf(tag_name) === -1 &&\n    (this._options.content_unformatted.indexOf(tag_name) !== -1 ||\n      this._options.unformatted.indexOf(tag_name) !== -1);\n};\n\nTokenizer.prototype._read_raw_content = function(c, previous_token, open_token) { // jshint unused:false\n  var resulting_string = '';\n  if (open_token && open_token.text[0] === '{') {\n    resulting_string = this.__patterns.handlebars_raw_close.read();\n  } else if (previous_token.type === TOKEN.TAG_CLOSE &&\n    previous_token.opened.text[0] === '<' && previous_token.text[0] !== '/') {\n    // ^^ empty tag has no content \n    var tag_name = previous_token.opened.text.substr(1).toLowerCase();\n    if (this._is_content_unformatted(tag_name)) {\n\n      resulting_string = this._input.readUntil(new RegExp('</' + tag_name + '[\\\\n\\\\r\\\\t ]*?>', 'ig'));\n    }\n  }\n\n  if (resulting_string) {\n    return this._create_token(TOKEN.TEXT, resulting_string);\n  }\n\n  return null;\n};\n\nTokenizer.prototype._read_script_and_style = function(c, previous_token) { // jshint unused:false \n  if (previous_token.type === TOKEN.TAG_CLOSE && previous_token.opened.text[0] === '<' && previous_token.text[0] !== '/') {\n    var tag_name = previous_token.opened.text.substr(1).toLowerCase();\n    if (tag_name === 'script' || tag_name === 'style') {\n      // Script and style tags are allowed to have comments wrapping their content\n      // or just have regular content.\n      var token = this._read_comment_or_cdata(c);\n      if (token) {\n        token.type = TOKEN.TEXT;\n        return token;\n      }\n      var resulting_string = this._input.readUntil(new RegExp('</' + tag_name + '[\\\\n\\\\r\\\\t ]*?>', 'ig'));\n      if (resulting_string) {\n        return this._create_token(TOKEN.TEXT, resulting_string);\n      }\n    }\n  }\n  return null;\n};\n\nTokenizer.prototype._read_content_word = function(c, open_token) {\n  var resulting_string = '';\n  if (this._options.unformatted_content_delimiter) {\n    if (c === this._options.unformatted_content_delimiter[0]) {\n      resulting_string = this.__patterns.unformatted_content_delimiter.read();\n    }\n  }\n\n  if (!resulting_string) {\n    resulting_string = (open_token && open_token.type === TOKEN.CONTROL_FLOW_OPEN) ? this.__patterns.word_control_flow_close_excluded.read() : this.__patterns.word.read();\n  }\n  if (resulting_string) {\n    return this._create_token(TOKEN.TEXT, resulting_string);\n  }\n  return null;\n};\n\nmodule.exports.Tokenizer = Tokenizer;\nmodule.exports.TOKEN = TOKEN;\n\n\n/***/ })\n/******/ \t]);\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t\n/******/ \t// startup\n/******/ \t// Load entry module and return exports\n/******/ \t// This entry module is referenced by other modules so it can't be inlined\n/******/ \tvar __webpack_exports__ = __webpack_require__(18);\n/******/ \tlegacy_beautify_html = __webpack_exports__;\n/******/ \t\n/******/ })()\n;\nvar style_html = legacy_beautify_html;\n/* Footer */\nif (typeof define === \"function\" && define.amd) {\n    // Add support for AMD ( https://github.com/amdjs/amdjs-api/wiki/AMD#defineamd-property- )\n    define([\"require\", \"./beautify\", \"./beautify-css\"], function(requireamd) {\n        var js_beautify = requireamd(\"./beautify\");\n        var css_beautify = requireamd(\"./beautify-css\");\n\n        return {\n            html_beautify: function(html_source, options) {\n                return style_html(html_source, options, js_beautify.js_beautify, css_beautify.css_beautify);\n            }\n        };\n    });\n} else if (typeof exports !== \"undefined\") {\n    // Add support for CommonJS. Just put this file somewhere on your require.paths\n    // and you will be able to `var html_beautify = require(\"beautify\").html_beautify`.\n    var js_beautify = require('./beautify.js');\n    var css_beautify = require('./beautify-css.js');\n\n    exports.html_beautify = function(html_source, options) {\n        return style_html(html_source, options, js_beautify.js_beautify, css_beautify.css_beautify);\n    };\n} else if (typeof window !== \"undefined\") {\n    // If we're running a web page and don't have either of the above, add our one global\n    window.html_beautify = function(html_source, options) {\n        return style_html(html_source, options, window.js_beautify, window.css_beautify);\n    };\n} else if (typeof global !== \"undefined\") {\n    // If we don't even have window, try global.\n    global.html_beautify = function(html_source, options) {\n        return style_html(html_source, options, global.js_beautify, global.css_beautify);\n    };\n}\n\n}());\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEC,aAAW;EAEZ;EACA,IAAIA,oBAAoB;EACxB;EAAS,CAAC,YAAW;IAAE;IACvB;IAAU,YAAY;;IACtB;IAAU,IAAIC,mBAAmB,GAAI;MACrC;MACA;;MAseA;MACA;;MAwMA;;MAk6BA;MACA;MACA;IAAA,IAllDA;IACA,KAAO,UAASC,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,SAASC,UAAUA,CAACC,MAAM,EAAE;QAC1B,IAAI,CAACC,QAAQ,GAAGD,MAAM;QACtB,IAAI,CAACE,iBAAiB,GAAG,CAAC;QAC1B;QACA,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAACC,iBAAiB,GAAG,CAAC;QAC1B,IAAI,CAACC,kBAAkB,GAAG,CAAC;QAC3B,IAAI,CAACC,4BAA4B,GAAG,CAAC;QACrC,IAAI,CAACC,yBAAyB,GAAG,CAAC,CAAC;QACnC,IAAI,CAACC,4BAA4B,GAAG,CAAC;QAErC,IAAI,CAACC,OAAO,GAAG,EAAE;MACnB;MAEAV,UAAU,CAACW,SAAS,CAACC,WAAW,GAAG,YAAW;QAC5C,IAAIC,IAAI,GAAG,IAAIb,UAAU,CAAC,IAAI,CAACE,QAAQ,CAAC;QACxCW,IAAI,CAACC,UAAU,CAAC,IAAI,CAACV,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC;QAC5D,OAAOQ,IAAI;MACb,CAAC;MAEDb,UAAU,CAACW,SAAS,CAACI,IAAI,GAAG,UAASC,KAAK,EAAE;QAC1C,IAAIA,KAAK,GAAG,CAAC,EAAE;UACb,OAAO,IAAI,CAACN,OAAO,CAAC,IAAI,CAACA,OAAO,CAACO,MAAM,GAAGD,KAAK,CAAC;QAClD,CAAC,MAAM;UACL,OAAO,IAAI,CAACN,OAAO,CAACM,KAAK,CAAC;QAC5B;MACF,CAAC;MAEDhB,UAAU,CAACW,SAAS,CAACO,SAAS,GAAG,UAASC,OAAO,EAAE;QACjD,KAAK,IAAIC,iBAAiB,GAAG,IAAI,CAACV,OAAO,CAACO,MAAM,GAAG,CAAC,EAAEG,iBAAiB,IAAI,CAAC,EAAEA,iBAAiB,EAAE,EAAE;UACjG,IAAI,IAAI,CAACV,OAAO,CAACU,iBAAiB,CAAC,CAACC,KAAK,CAACF,OAAO,CAAC,EAAE;YAClD,OAAO,IAAI;UACb;QACF;QACA,OAAO,KAAK;MACd,CAAC;MAEDnB,UAAU,CAACW,SAAS,CAACG,UAAU,GAAG,UAASQ,MAAM,EAAEC,SAAS,EAAE;QAC5D,IAAI,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;UACnB,IAAI,CAACpB,cAAc,GAAGkB,MAAM,IAAI,CAAC;UACjC,IAAI,CAACjB,iBAAiB,GAAGkB,SAAS,IAAI,CAAC;UACvC,IAAI,CAACpB,iBAAiB,GAAG,IAAI,CAACD,QAAQ,CAACuB,eAAe,CAAC,IAAI,CAACrB,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC;QACrG;MACF,CAAC;MAEDL,UAAU,CAACW,SAAS,CAACe,eAAe,GAAG,YAAW;QAChD,IAAI,IAAI,CAACxB,QAAQ,CAACyB,gBAAgB,EAAE;UAClC,IAAI,CAACrB,kBAAkB,GAAG,IAAI,CAACI,OAAO,CAACO,MAAM;UAC7C,IAAI,CAACV,4BAA4B,GAAG,IAAI,CAACJ,iBAAiB;UAC1D,IAAI,CAACK,yBAAyB,GAAG,IAAI,CAACN,QAAQ,CAAC0B,SAAS,CAACxB,cAAc;UACvE,IAAI,CAACK,4BAA4B,GAAG,IAAI,CAACP,QAAQ,CAAC0B,SAAS,CAACvB,iBAAiB;QAC/E;MACF,CAAC;MAEDL,UAAU,CAACW,SAAS,CAACkB,YAAY,GAAG,YAAW;QAC7C,OAAO,IAAI,CAACvB,kBAAkB,IAC5B,IAAI,CAACH,iBAAiB,GAAG,IAAI,CAACD,QAAQ,CAACyB,gBAAgB,IACvD,IAAI,CAACpB,4BAA4B,GAAG,IAAI,CAACL,QAAQ,CAAC0B,SAAS,CAACzB,iBAAiB;MACjF,CAAC;MAEDH,UAAU,CAACW,SAAS,CAACmB,WAAW,GAAG,YAAW;QAC5C,IAAI,IAAI,CAACD,YAAY,CAAC,CAAC,EAAE;UACvB,IAAI,CAAC3B,QAAQ,CAAC6B,YAAY,CAAC,CAAC;UAC5B,IAAIC,IAAI,GAAG,IAAI,CAAC9B,QAAQ,CAAC+B,YAAY;UACrCD,IAAI,CAAClB,UAAU,CAAC,IAAI,CAACN,yBAAyB,EAAE,IAAI,CAACC,4BAA4B,CAAC;UAClFuB,IAAI,CAACtB,OAAO,GAAG,IAAI,CAACA,OAAO,CAACwB,KAAK,CAAC,IAAI,CAAC5B,kBAAkB,CAAC;UAC1D,IAAI,CAACI,OAAO,GAAG,IAAI,CAACA,OAAO,CAACwB,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC5B,kBAAkB,CAAC;UAE7D0B,IAAI,CAAC7B,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACI,4BAA4B;UACpF,IAAI,CAACJ,iBAAiB,GAAG,IAAI,CAACI,4BAA4B;UAE1D,IAAIyB,IAAI,CAACtB,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAC3BsB,IAAI,CAACtB,OAAO,CAACyB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;YACzBH,IAAI,CAAC7B,iBAAiB,IAAI,CAAC;UAC7B;UACA,OAAO,IAAI;QACb;QACA,OAAO,KAAK;MACd,CAAC;MAEDH,UAAU,CAACW,SAAS,CAACa,QAAQ,GAAG,YAAW;QACzC,OAAO,IAAI,CAACd,OAAO,CAACO,MAAM,KAAK,CAAC;MAClC,CAAC;MAEDjB,UAAU,CAACW,SAAS,CAACyB,IAAI,GAAG,YAAW;QACrC,IAAI,CAAC,IAAI,CAACZ,QAAQ,CAAC,CAAC,EAAE;UACpB,OAAO,IAAI,CAACd,OAAO,CAAC,IAAI,CAACA,OAAO,CAACO,MAAM,GAAG,CAAC,CAAC;QAC9C,CAAC,MAAM;UACL,OAAO,IAAI;QACb;MACF,CAAC;MAEDjB,UAAU,CAACW,SAAS,CAAC0B,IAAI,GAAG,UAAStB,IAAI,EAAE;QACzC,IAAI,CAACL,OAAO,CAAC2B,IAAI,CAACtB,IAAI,CAAC;QACvB,IAAIuB,kBAAkB,GAAGvB,IAAI,CAACwB,WAAW,CAAC,IAAI,CAAC;QAC/C,IAAID,kBAAkB,KAAK,CAAC,CAAC,EAAE;UAC7B,IAAI,CAACnC,iBAAiB,GAAGY,IAAI,CAACE,MAAM,GAAGqB,kBAAkB;QAC3D,CAAC,MAAM;UACL,IAAI,CAACnC,iBAAiB,IAAIY,IAAI,CAACE,MAAM;QACvC;MACF,CAAC;MAEDjB,UAAU,CAACW,SAAS,CAAC6B,GAAG,GAAG,YAAW;QACpC,IAAIzB,IAAI,GAAG,IAAI;QACf,IAAI,CAAC,IAAI,CAACS,QAAQ,CAAC,CAAC,EAAE;UACpBT,IAAI,GAAG,IAAI,CAACL,OAAO,CAAC8B,GAAG,CAAC,CAAC;UACzB,IAAI,CAACrC,iBAAiB,IAAIY,IAAI,CAACE,MAAM;QACvC;QACA,OAAOF,IAAI;MACb,CAAC;MAGDf,UAAU,CAACW,SAAS,CAAC8B,cAAc,GAAG,YAAW;QAC/C,IAAI,IAAI,CAACrC,cAAc,GAAG,CAAC,EAAE;UAC3B,IAAI,CAACA,cAAc,IAAI,CAAC;UACxB,IAAI,CAACD,iBAAiB,IAAI,IAAI,CAACD,QAAQ,CAACwC,WAAW;QACrD;MACF,CAAC;MAED1C,UAAU,CAACW,SAAS,CAACgC,mBAAmB,GAAG,YAAW;QACpD,IAAI,IAAI,CAACnC,yBAAyB,GAAG,CAAC,EAAE;UACtC,IAAI,CAACA,yBAAyB,IAAI,CAAC;QACrC;MACF,CAAC;MACDR,UAAU,CAACW,SAAS,CAACiC,IAAI,GAAG,YAAW;QACrC,OAAO,IAAI,CAACR,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;UAC1B,IAAI,CAAC1B,OAAO,CAAC8B,GAAG,CAAC,CAAC;UAClB,IAAI,CAACrC,iBAAiB,IAAI,CAAC;QAC7B;MACF,CAAC;MAEDH,UAAU,CAACW,SAAS,CAACkC,QAAQ,GAAG,YAAW;QACzC,IAAIC,MAAM,GAAG,EAAE;QACf,IAAI,IAAI,CAACtB,QAAQ,CAAC,CAAC,EAAE;UACnB,IAAI,IAAI,CAACtB,QAAQ,CAAC6C,kBAAkB,EAAE;YACpCD,MAAM,GAAG,IAAI,CAAC5C,QAAQ,CAAC8C,iBAAiB,CAAC,IAAI,CAAC5C,cAAc,CAAC;UAC/D;QACF,CAAC,MAAM;UACL0C,MAAM,GAAG,IAAI,CAAC5C,QAAQ,CAAC8C,iBAAiB,CAAC,IAAI,CAAC5C,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC;UACrFyC,MAAM,IAAI,IAAI,CAACpC,OAAO,CAACuC,IAAI,CAAC,EAAE,CAAC;QACjC;QACA,OAAOH,MAAM;MACf,CAAC;MAED,SAASI,iBAAiBA,CAACC,OAAO,EAAEC,gBAAgB,EAAE;QACpD,IAAI,CAACC,OAAO,GAAG,CAAC,EAAE,CAAC;QACnB,IAAI,CAACC,aAAa,GAAGH,OAAO,CAACT,WAAW;QACxC,IAAI,CAACa,eAAe,GAAGJ,OAAO,CAACK,WAAW;QAC1C,IAAI,CAACL,OAAO,CAACM,gBAAgB,EAAE;UAC7B,IAAI,CAACF,eAAe,GAAG,IAAIG,KAAK,CAACP,OAAO,CAACT,WAAW,GAAG,CAAC,CAAC,CAACO,IAAI,CAACE,OAAO,CAACK,WAAW,CAAC;QACrF;;QAEA;QACAJ,gBAAgB,GAAGA,gBAAgB,IAAI,EAAE;QACzC,IAAID,OAAO,CAACQ,YAAY,GAAG,CAAC,EAAE;UAC5BP,gBAAgB,GAAG,IAAIM,KAAK,CAACP,OAAO,CAACQ,YAAY,GAAG,CAAC,CAAC,CAACV,IAAI,CAAC,IAAI,CAACM,eAAe,CAAC;QACnF;QAEA,IAAI,CAACK,aAAa,GAAGR,gBAAgB;QACrC,IAAI,CAACS,oBAAoB,GAAGT,gBAAgB,CAACnC,MAAM;MACrD;MAEAiC,iBAAiB,CAACvC,SAAS,CAACc,eAAe,GAAG,UAASH,MAAM,EAAEwC,MAAM,EAAE;QACrE,IAAIhB,MAAM,GAAG,IAAI,CAACe,oBAAoB;QACtCC,MAAM,GAAGA,MAAM,IAAI,CAAC;QACpB,IAAIxC,MAAM,GAAG,CAAC,EAAE;UACdwB,MAAM,GAAG,CAAC;QACZ;QACAA,MAAM,IAAIxB,MAAM,GAAG,IAAI,CAACgC,aAAa;QACrCR,MAAM,IAAIgB,MAAM;QAChB,OAAOhB,MAAM;MACf,CAAC;MAEDI,iBAAiB,CAACvC,SAAS,CAACqC,iBAAiB,GAAG,UAASW,YAAY,EAAEG,MAAM,EAAE;QAC7E,IAAIhB,MAAM,GAAG,IAAI,CAACc,aAAa;QAC/BE,MAAM,GAAGA,MAAM,IAAI,CAAC;QACpB,IAAIH,YAAY,GAAG,CAAC,EAAE;UACpBA,YAAY,GAAG,CAAC;UAChBb,MAAM,GAAG,EAAE;QACb;QACAgB,MAAM,IAAIH,YAAY,GAAG,IAAI,CAACL,aAAa;QAC3C,IAAI,CAACS,cAAc,CAACD,MAAM,CAAC;QAC3BhB,MAAM,IAAI,IAAI,CAACO,OAAO,CAACS,MAAM,CAAC;QAC9B,OAAOhB,MAAM;MACf,CAAC;MAEDI,iBAAiB,CAACvC,SAAS,CAACoD,cAAc,GAAG,UAASD,MAAM,EAAE;QAC5D,OAAOA,MAAM,IAAI,IAAI,CAACT,OAAO,CAACpC,MAAM,EAAE;UACpC,IAAI,CAAC+C,YAAY,CAAC,CAAC;QACrB;MACF,CAAC;MAEDd,iBAAiB,CAACvC,SAAS,CAACqD,YAAY,GAAG,YAAW;QACpD,IAAIF,MAAM,GAAG,IAAI,CAACT,OAAO,CAACpC,MAAM;QAChC,IAAIK,MAAM,GAAG,CAAC;QACd,IAAIwB,MAAM,GAAG,EAAE;QACf,IAAI,IAAI,CAACQ,aAAa,IAAIQ,MAAM,IAAI,IAAI,CAACR,aAAa,EAAE;UACtDhC,MAAM,GAAG2C,IAAI,CAACC,KAAK,CAACJ,MAAM,GAAG,IAAI,CAACR,aAAa,CAAC;UAChDQ,MAAM,IAAIxC,MAAM,GAAG,IAAI,CAACgC,aAAa;UACrCR,MAAM,GAAG,IAAIY,KAAK,CAACpC,MAAM,GAAG,CAAC,CAAC,CAAC2B,IAAI,CAAC,IAAI,CAACM,eAAe,CAAC;QAC3D;QACA,IAAIO,MAAM,EAAE;UACVhB,MAAM,IAAI,IAAIY,KAAK,CAACI,MAAM,GAAG,CAAC,CAAC,CAACb,IAAI,CAAC,GAAG,CAAC;QAC3C;QAEA,IAAI,CAACI,OAAO,CAAChB,IAAI,CAACS,MAAM,CAAC;MAC3B,CAAC;MAED,SAASqB,MAAMA,CAAChB,OAAO,EAAEC,gBAAgB,EAAE;QACzC,IAAI,CAACgB,cAAc,GAAG,IAAIlB,iBAAiB,CAACC,OAAO,EAAEC,gBAAgB,CAAC;QACtE,IAAI,CAACiB,GAAG,GAAG,KAAK;QAChB,IAAI,CAACC,iBAAiB,GAAGnB,OAAO,CAACoB,gBAAgB;QACjD,IAAI,CAAC7B,WAAW,GAAGS,OAAO,CAACT,WAAW;QACtC,IAAI,CAACf,gBAAgB,GAAGwB,OAAO,CAACxB,gBAAgB;QAChD,IAAI,CAACoB,kBAAkB,GAAGI,OAAO,CAACJ,kBAAkB;QACpD,IAAI,CAACyB,OAAO,GAAG,EAAE;QACjB,IAAI,CAACC,aAAa,GAAG,IAAI;QACzB,IAAI,CAACxC,YAAY,GAAG,IAAI;QACxB,IAAI,CAACL,SAAS,GAAG,IAAI5B,UAAU,CAAC,IAAI,CAAC;QACrC,IAAI,CAAC0E,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACC,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACC,sBAAsB,GAAG,KAAK;QACnC;QACA,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACzB;MAEAV,MAAM,CAACxD,SAAS,CAACkE,gBAAgB,GAAG,YAAW;QAC7C,IAAI,CAACJ,aAAa,GAAG,IAAI,CAACxC,YAAY;QACtC,IAAI,CAACA,YAAY,GAAG,IAAI,CAACL,SAAS,CAAChB,WAAW,CAAC,CAAC;QAChD,IAAI,CAAC4D,OAAO,CAACnC,IAAI,CAAC,IAAI,CAACJ,YAAY,CAAC;MACtC,CAAC;MAEDkC,MAAM,CAACxD,SAAS,CAACmE,eAAe,GAAG,YAAW;QAC5C,OAAO,IAAI,CAACN,OAAO,CAACvD,MAAM;MAC5B,CAAC;MAEDkD,MAAM,CAACxD,SAAS,CAACqC,iBAAiB,GAAG,UAAS1B,MAAM,EAAEwC,MAAM,EAAE;QAC5D,OAAO,IAAI,CAACM,cAAc,CAACpB,iBAAiB,CAAC1B,MAAM,EAAEwC,MAAM,CAAC;MAC9D,CAAC;MAEDK,MAAM,CAACxD,SAAS,CAACc,eAAe,GAAG,UAASH,MAAM,EAAEwC,MAAM,EAAE;QAC1D,OAAO,IAAI,CAACM,cAAc,CAAC3C,eAAe,CAACH,MAAM,EAAEwC,MAAM,CAAC;MAC5D,CAAC;MAEDK,MAAM,CAACxD,SAAS,CAACa,QAAQ,GAAG,YAAW;QACrC,OAAO,CAAC,IAAI,CAACiD,aAAa,IAAI,IAAI,CAACxC,YAAY,CAACT,QAAQ,CAAC,CAAC;MAC5D,CAAC;MAED2C,MAAM,CAACxD,SAAS,CAACoB,YAAY,GAAG,UAASgD,aAAa,EAAE;QACtD;QACA;QACA,IAAI,IAAI,CAACvD,QAAQ,CAAC,CAAC,IAChB,CAACuD,aAAa,IAAI,IAAI,CAACC,kBAAkB,CAAC,CAAE,EAAE;UAC/C,OAAO,KAAK;QACd;;QAEA;QACA;QACA,IAAI,CAAC,IAAI,CAACX,GAAG,EAAE;UACb,IAAI,CAACQ,gBAAgB,CAAC,CAAC;QACzB;QACA,OAAO,IAAI;MACb,CAAC;MAEDV,MAAM,CAACxD,SAAS,CAACsE,QAAQ,GAAG,UAASC,GAAG,EAAE;QACxC,IAAI,CAACtC,IAAI,CAAC,IAAI,CAAC;;QAEf;QACA;QACA,IAAIuC,SAAS,GAAG,IAAI,CAAClD,YAAY,CAACO,GAAG,CAAC,CAAC;QACvC,IAAI2C,SAAS,EAAE;UACb,IAAIA,SAAS,CAACA,SAAS,CAAClE,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;YAC5CkE,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;UAC5C;UACA,IAAI,CAACnD,YAAY,CAACI,IAAI,CAAC8C,SAAS,CAAC;QACnC;QAEA,IAAI,IAAI,CAACb,iBAAiB,EAAE;UAC1B,IAAI,CAACO,gBAAgB,CAAC,CAAC;QACzB;QAEA,IAAIQ,UAAU,GAAG,IAAI,CAACb,OAAO,CAACvB,IAAI,CAAC,IAAI,CAAC;QAExC,IAAIiC,GAAG,KAAK,IAAI,EAAE;UAChBG,UAAU,GAAGA,UAAU,CAACD,OAAO,CAAC,OAAO,EAAEF,GAAG,CAAC;QAC/C;QACA,OAAOG,UAAU;MACnB,CAAC;MAEDlB,MAAM,CAACxD,SAAS,CAAC2E,cAAc,GAAG,YAAW;QAC3C,IAAI,CAACrD,YAAY,CAACP,eAAe,CAAC,CAAC;MACrC,CAAC;MAEDyC,MAAM,CAACxD,SAAS,CAACG,UAAU,GAAG,UAASQ,MAAM,EAAEC,SAAS,EAAE;QACxDD,MAAM,GAAGA,MAAM,IAAI,CAAC;QACpBC,SAAS,GAAGA,SAAS,IAAI,CAAC;;QAE1B;QACA,IAAI,CAACK,SAAS,CAACd,UAAU,CAACQ,MAAM,EAAEC,SAAS,CAAC;;QAE5C;QACA,IAAI,IAAI,CAACiD,OAAO,CAACvD,MAAM,GAAG,CAAC,EAAE;UAC3B,IAAI,CAACgB,YAAY,CAACnB,UAAU,CAACQ,MAAM,EAAEC,SAAS,CAAC;UAC/C,OAAO,IAAI;QACb;QAEA,IAAI,CAACU,YAAY,CAACnB,UAAU,CAAC,CAAC;QAC9B,OAAO,KAAK;MACd,CAAC;MAEDqD,MAAM,CAACxD,SAAS,CAAC4E,aAAa,GAAG,UAASC,KAAK,EAAE;QAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,QAAQ,EAAED,CAAC,EAAE,EAAE;UACvC,IAAI,CAACZ,gBAAgB,CAAC,CAAC;QACzB;QACA,IAAI,CAAC5C,YAAY,CAACnB,UAAU,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAACmB,YAAY,CAACI,IAAI,CAACmD,KAAK,CAACG,iBAAiB,CAAC;QAC/C,IAAI,CAAC1D,YAAY,CAACI,IAAI,CAACmD,KAAK,CAACI,IAAI,CAAC;QAClC,IAAI,CAAClB,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACC,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACC,sBAAsB,GAAG,KAAK;MACrC,CAAC;MAEDT,MAAM,CAACxD,SAAS,CAACkF,SAAS,GAAG,UAASC,eAAe,EAAE;QACrD,IAAI,CAACC,wBAAwB,CAAC,CAAC;QAC/B,IAAI,CAAC9D,YAAY,CAACI,IAAI,CAACyD,eAAe,CAAC;QACvC,IAAI,CAACpB,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACC,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAAC3C,YAAY,CAACH,WAAW,CAAC,CAAC;MAC/D,CAAC;MAEDqC,MAAM,CAACxD,SAAS,CAACoF,wBAAwB,GAAG,YAAW;QACrD,IAAI,IAAI,CAACrB,kBAAkB,IAAI,CAAC,IAAI,CAACM,kBAAkB,CAAC,CAAC,EAAE;UACzD,IAAI,CAAC,IAAI,CAACL,kBAAkB,EAAE;YAC5B,IAAI,CAACW,cAAc,CAAC,CAAC;UACvB;UACA,IAAI,CAACrD,YAAY,CAACI,IAAI,CAAC,GAAG,CAAC;QAC7B;MACF,CAAC;MAED8B,MAAM,CAACxD,SAAS,CAACqF,aAAa,GAAG,UAAShF,KAAK,EAAE;QAC/C,IAAIiF,aAAa,GAAG,IAAI,CAACzB,OAAO,CAACvD,MAAM;QACvC,OAAOD,KAAK,GAAGiF,aAAa,EAAE;UAC5B,IAAI,CAACzB,OAAO,CAACxD,KAAK,CAAC,CAACyB,cAAc,CAAC,CAAC;UACpCzB,KAAK,EAAE;QACT;QACA,IAAI,CAACiB,YAAY,CAACU,mBAAmB,CAAC,CAAC;MACzC,CAAC;MAEDwB,MAAM,CAACxD,SAAS,CAACiC,IAAI,GAAG,UAASsD,YAAY,EAAE;QAC7CA,YAAY,GAAIA,YAAY,KAAKC,SAAS,GAAI,KAAK,GAAGD,YAAY;QAElE,IAAI,CAACjE,YAAY,CAACW,IAAI,CAAC,CAAC;QAExB,OAAOsD,YAAY,IAAI,IAAI,CAAC1B,OAAO,CAACvD,MAAM,GAAG,CAAC,IAC5C,IAAI,CAACgB,YAAY,CAACT,QAAQ,CAAC,CAAC,EAAE;UAC9B,IAAI,CAACgD,OAAO,CAAChC,GAAG,CAAC,CAAC;UAClB,IAAI,CAACP,YAAY,GAAG,IAAI,CAACuC,OAAO,CAAC,IAAI,CAACA,OAAO,CAACvD,MAAM,GAAG,CAAC,CAAC;UACzD,IAAI,CAACgB,YAAY,CAACW,IAAI,CAAC,CAAC;QAC1B;QAEA,IAAI,CAAC6B,aAAa,GAAG,IAAI,CAACD,OAAO,CAACvD,MAAM,GAAG,CAAC,GAC1C,IAAI,CAACuD,OAAO,CAAC,IAAI,CAACA,OAAO,CAACvD,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;MAChD,CAAC;MAEDkD,MAAM,CAACxD,SAAS,CAACqE,kBAAkB,GAAG,YAAW;QAC/C,OAAO,IAAI,CAAC/C,YAAY,CAACT,QAAQ,CAAC,CAAC;MACrC,CAAC;MAED2C,MAAM,CAACxD,SAAS,CAACyF,oBAAoB,GAAG,YAAW;QACjD,OAAO,IAAI,CAAC5E,QAAQ,CAAC,CAAC,IACnB,IAAI,CAACS,YAAY,CAACT,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACiD,aAAa,CAACjD,QAAQ,CAAC,CAAE;MACnE,CAAC;MAED2C,MAAM,CAACxD,SAAS,CAAC0F,uBAAuB,GAAG,UAASC,WAAW,EAAEC,SAAS,EAAE;QAC1E,IAAIvF,KAAK,GAAG,IAAI,CAACwD,OAAO,CAACvD,MAAM,GAAG,CAAC;QACnC,OAAOD,KAAK,IAAI,CAAC,EAAE;UACjB,IAAIwF,kBAAkB,GAAG,IAAI,CAAChC,OAAO,CAACxD,KAAK,CAAC;UAC5C,IAAIwF,kBAAkB,CAAChF,QAAQ,CAAC,CAAC,EAAE;YACjC;UACF,CAAC,MAAM,IAAIgF,kBAAkB,CAACzF,IAAI,CAAC,CAAC,CAAC,CAAC0F,OAAO,CAACH,WAAW,CAAC,KAAK,CAAC,IAC9DE,kBAAkB,CAACzF,IAAI,CAAC,CAAC,CAAC,CAAC,KAAKwF,SAAS,EAAE;YAC3C,IAAI,CAAC/B,OAAO,CAACrC,MAAM,CAACnB,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,IAAIhB,UAAU,CAAC,IAAI,CAAC,CAAC;YACvD,IAAI,CAACyE,aAAa,GAAG,IAAI,CAACD,OAAO,CAAC,IAAI,CAACA,OAAO,CAACvD,MAAM,GAAG,CAAC,CAAC;YAC1D;UACF;UACAD,KAAK,EAAE;QACT;MACF,CAAC;MAEDjB,MAAM,CAAC2G,OAAO,CAACvC,MAAM,GAAGA,MAAM;;MAG9B;IAAM,CAAC,IACP;IACA,KAAO,UAASpE,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,SAAS4G,KAAKA,CAACC,IAAI,EAAEhB,IAAI,EAAEF,QAAQ,EAAEC,iBAAiB,EAAE;QACtD,IAAI,CAACiB,IAAI,GAAGA,IAAI;QAChB,IAAI,CAAChB,IAAI,GAAGA,IAAI;;QAEhB;QACA;QACA;QACA;QACA,IAAI,CAACiB,eAAe,GAAG,IAAI,CAAC,CAAC;;QAG7B;QACA,IAAI,CAACnB,QAAQ,GAAGA,QAAQ,IAAI,CAAC;QAC7B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB,IAAI,EAAE;QAChD,IAAI,CAAC1F,MAAM,GAAG,IAAI;QAClB,IAAI,CAAC+B,IAAI,GAAG,IAAI;QAChB,IAAI,CAAC8E,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACC,MAAM,GAAG,IAAI;QAClB,IAAI,CAACC,MAAM,GAAG,IAAI;QAClB,IAAI,CAACC,UAAU,GAAG,IAAI;MACxB;MAGAlH,MAAM,CAAC2G,OAAO,CAACC,KAAK,GAAGA,KAAK;;MAG5B;IAAM,CAAC,MAGP;IACA,KAAO,UAAS5G,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,SAASmH,OAAOA,CAAC/D,OAAO,EAAEgE,iBAAiB,EAAE;QAC3C,IAAI,CAACC,WAAW,GAAGC,UAAU,CAAClE,OAAO,EAAEgE,iBAAiB,CAAC;;QAEzD;QACA,IAAI,CAACG,QAAQ,GAAG,IAAI,CAACC,YAAY,CAAC,UAAU,CAAC;QAE7C,IAAI,CAACrC,GAAG,GAAG,IAAI,CAACsC,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC;QAC9C,IAAI,CAACjD,gBAAgB,GAAG,IAAI,CAACgD,YAAY,CAAC,kBAAkB,CAAC;QAC7D,IAAI,CAAC7E,WAAW,GAAG,IAAI,CAAC+E,WAAW,CAAC,aAAa,EAAE,CAAC,CAAC;QACrD,IAAI,CAACjE,WAAW,GAAG,IAAI,CAACgE,eAAe,CAAC,aAAa,EAAE,GAAG,CAAC;QAC3D,IAAI,CAAC7D,YAAY,GAAG,IAAI,CAAC8D,WAAW,CAAC,cAAc,CAAC;QAEpD,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACH,YAAY,CAAC,mBAAmB,EAAE,IAAI,CAAC;QACrE,IAAI,CAACI,qBAAqB,GAAG,IAAI,CAACF,WAAW,CAAC,uBAAuB,EAAE,KAAK,CAAC;QAC7E,IAAI,CAAC,IAAI,CAACC,iBAAiB,EAAE;UAC3B,IAAI,CAACC,qBAAqB,GAAG,CAAC;QAChC;QAEA,IAAI,CAAClE,gBAAgB,GAAG,IAAI,CAAC8D,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC/D,WAAW,KAAK,IAAI,CAAC;QACxF,IAAI,IAAI,CAACC,gBAAgB,EAAE;UACzB,IAAI,CAACD,WAAW,GAAG,IAAI;;UAEvB;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,IAAI,CAACd,WAAW,KAAK,CAAC,EAAE;YAC1B,IAAI,CAACA,WAAW,GAAG,CAAC;UACtB;QACF;;QAEA;QACA,IAAI,CAACf,gBAAgB,GAAG,IAAI,CAAC8F,WAAW,CAAC,kBAAkB,EAAE,IAAI,CAACA,WAAW,CAAC,UAAU,CAAC,CAAC;QAE1F,IAAI,CAAC1E,kBAAkB,GAAG,IAAI,CAACwE,YAAY,CAAC,oBAAoB,CAAC;;QAEjE;QACA;QACA;QACA,IAAI,CAACK,UAAU,GAAG,IAAI,CAACC,mBAAmB,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;MACjJ;MAEAX,OAAO,CAACvG,SAAS,CAACmH,UAAU,GAAG,UAASC,IAAI,EAAEC,aAAa,EAAE;QAC3D,IAAIC,YAAY,GAAG,IAAI,CAACb,WAAW,CAACW,IAAI,CAAC;QACzC,IAAIjF,MAAM,GAAGkF,aAAa,IAAI,EAAE;QAChC,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;UACpC,IAAIA,YAAY,KAAK,IAAI,IAAI,OAAOA,YAAY,CAACC,MAAM,KAAK,UAAU,EAAE;YACtEpF,MAAM,GAAGmF,YAAY,CAACC,MAAM,CAAC,CAAC;UAChC;QACF,CAAC,MAAM,IAAI,OAAOD,YAAY,KAAK,QAAQ,EAAE;UAC3CnF,MAAM,GAAGmF,YAAY,CAACE,KAAK,CAAC,oBAAoB,CAAC;QACnD;QACA,OAAOrF,MAAM;MACf,CAAC;MAEDoE,OAAO,CAACvG,SAAS,CAAC4G,YAAY,GAAG,UAASQ,IAAI,EAAEC,aAAa,EAAE;QAC7D,IAAIC,YAAY,GAAG,IAAI,CAACb,WAAW,CAACW,IAAI,CAAC;QACzC,IAAIjF,MAAM,GAAGmF,YAAY,KAAK9B,SAAS,GAAG,CAAC,CAAC6B,aAAa,GAAG,CAAC,CAACC,YAAY;QAC1E,OAAOnF,MAAM;MACf,CAAC;MAEDoE,OAAO,CAACvG,SAAS,CAAC6G,eAAe,GAAG,UAASO,IAAI,EAAEC,aAAa,EAAE;QAChE,IAAIC,YAAY,GAAG,IAAI,CAACb,WAAW,CAACW,IAAI,CAAC;QACzC,IAAIjF,MAAM,GAAGkF,aAAa,IAAI,EAAE;QAChC,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;UACpCnF,MAAM,GAAGmF,YAAY,CAAC7C,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;QACtF;QACA,OAAOtC,MAAM;MACf,CAAC;MAEDoE,OAAO,CAACvG,SAAS,CAAC8G,WAAW,GAAG,UAASM,IAAI,EAAEC,aAAa,EAAE;QAC5D,IAAIC,YAAY,GAAG,IAAI,CAACb,WAAW,CAACW,IAAI,CAAC;QACzCC,aAAa,GAAGI,QAAQ,CAACJ,aAAa,EAAE,EAAE,CAAC;QAC3C,IAAIK,KAAK,CAACL,aAAa,CAAC,EAAE;UACxBA,aAAa,GAAG,CAAC;QACnB;QACA,IAAIlF,MAAM,GAAGsF,QAAQ,CAACH,YAAY,EAAE,EAAE,CAAC;QACvC,IAAII,KAAK,CAACvF,MAAM,CAAC,EAAE;UACjBA,MAAM,GAAGkF,aAAa;QACxB;QACA,OAAOlF,MAAM;MACf,CAAC;MAEDoE,OAAO,CAACvG,SAAS,CAAC2H,cAAc,GAAG,UAASP,IAAI,EAAEQ,cAAc,EAAEP,aAAa,EAAE;QAC/E,IAAIlF,MAAM,GAAG,IAAI,CAAC+E,mBAAmB,CAACE,IAAI,EAAEQ,cAAc,EAAEP,aAAa,CAAC;QAC1E,IAAIlF,MAAM,CAAC7B,MAAM,KAAK,CAAC,EAAE;UACvB,MAAM,IAAIuH,KAAK,CACb,oCAAoC,GAAGT,IAAI,GAAG,8CAA8C,GAC5FQ,cAAc,GAAG,oBAAoB,GAAG,IAAI,CAACnB,WAAW,CAACW,IAAI,CAAC,GAAG,GAAG,CAAC;QACzE;QAEA,OAAOjF,MAAM,CAAC,CAAC,CAAC;MAClB,CAAC;MAGDoE,OAAO,CAACvG,SAAS,CAACkH,mBAAmB,GAAG,UAASE,IAAI,EAAEQ,cAAc,EAAEP,aAAa,EAAE;QACpF,IAAI,CAACO,cAAc,IAAIA,cAAc,CAACtH,MAAM,KAAK,CAAC,EAAE;UAClD,MAAM,IAAIuH,KAAK,CAAC,iCAAiC,CAAC;QACpD;QAEAR,aAAa,GAAGA,aAAa,IAAI,CAACO,cAAc,CAAC,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,CAACE,mBAAmB,CAACT,aAAa,EAAEO,cAAc,CAAC,EAAE;UAC5D,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;QAC3C;QAEA,IAAI1F,MAAM,GAAG,IAAI,CAACgF,UAAU,CAACC,IAAI,EAAEC,aAAa,CAAC;QACjD,IAAI,CAAC,IAAI,CAACS,mBAAmB,CAAC3F,MAAM,EAAEyF,cAAc,CAAC,EAAE;UACrD,MAAM,IAAIC,KAAK,CACb,oCAAoC,GAAGT,IAAI,GAAG,4CAA4C,GAC1FQ,cAAc,GAAG,oBAAoB,GAAG,IAAI,CAACnB,WAAW,CAACW,IAAI,CAAC,GAAG,GAAG,CAAC;QACzE;QAEA,OAAOjF,MAAM;MACf,CAAC;MAEDoE,OAAO,CAACvG,SAAS,CAAC8H,mBAAmB,GAAG,UAAS3F,MAAM,EAAEyF,cAAc,EAAE;QACvE,OAAOzF,MAAM,CAAC7B,MAAM,IAAIsH,cAAc,CAACtH,MAAM,IAC3C,CAAC6B,MAAM,CAAC4F,IAAI,CAAC,UAAS3H,IAAI,EAAE;UAAE,OAAOwH,cAAc,CAAC9B,OAAO,CAAC1F,IAAI,CAAC,KAAK,CAAC,CAAC;QAAE,CAAC,CAAC;MAChF,CAAC;;MAGD;MACA;MACA;MACA;MACA;MACA,SAASsG,UAAUA,CAACsB,UAAU,EAAEC,cAAc,EAAE;QAC9C,IAAIC,SAAS,GAAG,CAAC,CAAC;QAClBF,UAAU,GAAGG,cAAc,CAACH,UAAU,CAAC;QACvC,IAAIZ,IAAI;QAER,KAAKA,IAAI,IAAIY,UAAU,EAAE;UACvB,IAAIZ,IAAI,KAAKa,cAAc,EAAE;YAC3BC,SAAS,CAACd,IAAI,CAAC,GAAGY,UAAU,CAACZ,IAAI,CAAC;UACpC;QACF;;QAEA;QACA,IAAIa,cAAc,IAAID,UAAU,CAACC,cAAc,CAAC,EAAE;UAChD,KAAKb,IAAI,IAAIY,UAAU,CAACC,cAAc,CAAC,EAAE;YACvCC,SAAS,CAACd,IAAI,CAAC,GAAGY,UAAU,CAACC,cAAc,CAAC,CAACb,IAAI,CAAC;UACpD;QACF;QACA,OAAOc,SAAS;MAClB;MAEA,SAASC,cAAcA,CAAC3F,OAAO,EAAE;QAC/B,IAAI4F,aAAa,GAAG,CAAC,CAAC;QACtB,IAAIC,GAAG;QAEP,KAAKA,GAAG,IAAI7F,OAAO,EAAE;UACnB,IAAI8F,MAAM,GAAGD,GAAG,CAAC5D,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;UACnC2D,aAAa,CAACE,MAAM,CAAC,GAAG9F,OAAO,CAAC6F,GAAG,CAAC;QACtC;QACA,OAAOD,aAAa;MACtB;MAEAhJ,MAAM,CAAC2G,OAAO,CAACQ,OAAO,GAAGA,OAAO;MAChCnH,MAAM,CAAC2G,OAAO,CAACwC,aAAa,GAAGJ,cAAc;MAC7C/I,MAAM,CAAC2G,OAAO,CAACyC,SAAS,GAAG9B,UAAU;;MAGrC;IAAM,CAAC,KAEP;IACA,KAAO,UAAStH,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAIqJ,iBAAiB,GAAGC,MAAM,CAAC1I,SAAS,CAAC2I,cAAc,CAAC,QAAQ,CAAC;MAEjE,SAASC,YAAYA,CAACC,YAAY,EAAE;QAClC,IAAI,CAACC,OAAO,GAAGD,YAAY,IAAI,EAAE;QACjC,IAAI,CAACE,cAAc,GAAG,IAAI,CAACD,OAAO,CAACxI,MAAM;QACzC,IAAI,CAAC0I,UAAU,GAAG,CAAC;MACrB;MAEAJ,YAAY,CAAC5I,SAAS,CAACiJ,OAAO,GAAG,YAAW;QAC1C,IAAI,CAACD,UAAU,GAAG,CAAC;MACrB,CAAC;MAEDJ,YAAY,CAAC5I,SAAS,CAACkJ,IAAI,GAAG,YAAW;QACvC,IAAI,IAAI,CAACF,UAAU,GAAG,CAAC,EAAE;UACvB,IAAI,CAACA,UAAU,IAAI,CAAC;QACtB;MACF,CAAC;MAEDJ,YAAY,CAAC5I,SAAS,CAACmJ,OAAO,GAAG,YAAW;QAC1C,OAAO,IAAI,CAACH,UAAU,GAAG,IAAI,CAACD,cAAc;MAC9C,CAAC;MAEDH,YAAY,CAAC5I,SAAS,CAACqB,IAAI,GAAG,YAAW;QACvC,IAAI+H,GAAG,GAAG,IAAI;QACd,IAAI,IAAI,CAACD,OAAO,CAAC,CAAC,EAAE;UAClBC,GAAG,GAAG,IAAI,CAACN,OAAO,CAACO,MAAM,CAAC,IAAI,CAACL,UAAU,CAAC;UAC1C,IAAI,CAACA,UAAU,IAAI,CAAC;QACtB;QACA,OAAOI,GAAG;MACZ,CAAC;MAEDR,YAAY,CAAC5I,SAAS,CAACsJ,IAAI,GAAG,UAASjJ,KAAK,EAAE;QAC5C,IAAI+I,GAAG,GAAG,IAAI;QACd/I,KAAK,GAAGA,KAAK,IAAI,CAAC;QAClBA,KAAK,IAAI,IAAI,CAAC2I,UAAU;QACxB,IAAI3I,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAAC0I,cAAc,EAAE;UAC7CK,GAAG,GAAG,IAAI,CAACN,OAAO,CAACO,MAAM,CAAChJ,KAAK,CAAC;QAClC;QACA,OAAO+I,GAAG;MACZ,CAAC;;MAED;MACA;MACA;MACA;MACA;MACA;MACA;MACAR,YAAY,CAAC5I,SAAS,CAACuJ,OAAO,GAAG,UAAS/I,OAAO,EAAEH,KAAK,EAAE;QACxDG,OAAO,CAACgJ,SAAS,GAAGnJ,KAAK;QACzB,IAAIoJ,aAAa,GAAGjJ,OAAO,CAACkJ,IAAI,CAAC,IAAI,CAACZ,OAAO,CAAC;QAE9C,IAAIW,aAAa,IAAI,EAAEhB,iBAAiB,IAAIjI,OAAO,CAACmJ,MAAM,CAAC,EAAE;UAC3D,IAAIF,aAAa,CAACpJ,KAAK,KAAKA,KAAK,EAAE;YACjCoJ,aAAa,GAAG,IAAI;UACtB;QACF;QAEA,OAAOA,aAAa;MACtB,CAAC;MAEDb,YAAY,CAAC5I,SAAS,CAAC4J,IAAI,GAAG,UAASpJ,OAAO,EAAEH,KAAK,EAAE;QACrDA,KAAK,GAAGA,KAAK,IAAI,CAAC;QAClBA,KAAK,IAAI,IAAI,CAAC2I,UAAU;QAExB,IAAI3I,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAAC0I,cAAc,EAAE;UAC7C,OAAO,CAAC,CAAC,IAAI,CAACQ,OAAO,CAAC/I,OAAO,EAAEH,KAAK,CAAC;QACvC,CAAC,MAAM;UACL,OAAO,KAAK;QACd;MACF,CAAC;MAEDuI,YAAY,CAAC5I,SAAS,CAAC6J,QAAQ,GAAG,UAASrJ,OAAO,EAAEH,KAAK,EAAE;QACzD;QACA,IAAI+I,GAAG,GAAG,IAAI,CAACE,IAAI,CAACjJ,KAAK,CAAC;QAC1BG,OAAO,CAACgJ,SAAS,GAAG,CAAC;QACrB,OAAOJ,GAAG,KAAK,IAAI,IAAI5I,OAAO,CAACoJ,IAAI,CAACR,GAAG,CAAC;MAC1C,CAAC;MAEDR,YAAY,CAAC5I,SAAS,CAACU,KAAK,GAAG,UAASF,OAAO,EAAE;QAC/C,IAAIiJ,aAAa,GAAG,IAAI,CAACF,OAAO,CAAC/I,OAAO,EAAE,IAAI,CAACwI,UAAU,CAAC;QAC1D,IAAIS,aAAa,EAAE;UACjB,IAAI,CAACT,UAAU,IAAIS,aAAa,CAAC,CAAC,CAAC,CAACnJ,MAAM;QAC5C,CAAC,MAAM;UACLmJ,aAAa,GAAG,IAAI;QACtB;QACA,OAAOA,aAAa;MACtB,CAAC;MAEDb,YAAY,CAAC5I,SAAS,CAAC8J,IAAI,GAAG,UAASC,gBAAgB,EAAEC,aAAa,EAAEC,WAAW,EAAE;QACnF,IAAIb,GAAG,GAAG,EAAE;QACZ,IAAI1I,KAAK;QACT,IAAIqJ,gBAAgB,EAAE;UACpBrJ,KAAK,GAAG,IAAI,CAACA,KAAK,CAACqJ,gBAAgB,CAAC;UACpC,IAAIrJ,KAAK,EAAE;YACT0I,GAAG,IAAI1I,KAAK,CAAC,CAAC,CAAC;UACjB;QACF;QACA,IAAIsJ,aAAa,KAAKtJ,KAAK,IAAI,CAACqJ,gBAAgB,CAAC,EAAE;UACjDX,GAAG,IAAI,IAAI,CAACc,SAAS,CAACF,aAAa,EAAEC,WAAW,CAAC;QACnD;QACA,OAAOb,GAAG;MACZ,CAAC;MAEDR,YAAY,CAAC5I,SAAS,CAACkK,SAAS,GAAG,UAAS1J,OAAO,EAAEyJ,WAAW,EAAE;QAChE,IAAIb,GAAG,GAAG,EAAE;QACZ,IAAIe,WAAW,GAAG,IAAI,CAACnB,UAAU;QACjCxI,OAAO,CAACgJ,SAAS,GAAG,IAAI,CAACR,UAAU;QACnC,IAAIS,aAAa,GAAGjJ,OAAO,CAACkJ,IAAI,CAAC,IAAI,CAACZ,OAAO,CAAC;QAC9C,IAAIW,aAAa,EAAE;UACjBU,WAAW,GAAGV,aAAa,CAACpJ,KAAK;UACjC,IAAI4J,WAAW,EAAE;YACfE,WAAW,IAAIV,aAAa,CAAC,CAAC,CAAC,CAACnJ,MAAM;UACxC;QACF,CAAC,MAAM;UACL6J,WAAW,GAAG,IAAI,CAACpB,cAAc;QACnC;QAEAK,GAAG,GAAG,IAAI,CAACN,OAAO,CAACsB,SAAS,CAAC,IAAI,CAACpB,UAAU,EAAEmB,WAAW,CAAC;QAC1D,IAAI,CAACnB,UAAU,GAAGmB,WAAW;QAC7B,OAAOf,GAAG;MACZ,CAAC;MAEDR,YAAY,CAAC5I,SAAS,CAACqK,cAAc,GAAG,UAAS7J,OAAO,EAAE;QACxD,OAAO,IAAI,CAAC0J,SAAS,CAAC1J,OAAO,EAAE,IAAI,CAAC;MACtC,CAAC;MAEDoI,YAAY,CAAC5I,SAAS,CAACsK,UAAU,GAAG,UAAS9J,OAAO,EAAE+J,UAAU,EAAE;QAChE,IAAIpI,MAAM,GAAG,IAAI;QACjB,IAAIqI,KAAK,GAAG,GAAG;QACf,IAAID,UAAU,IAAI9B,iBAAiB,EAAE;UACnC+B,KAAK,GAAG,GAAG;QACb;QACA;QACA,IAAI,OAAOhK,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,EAAE,EAAE;UACjD;UACA2B,MAAM,GAAG,IAAIuG,MAAM,CAAClI,OAAO,EAAEgK,KAAK,CAAC;QACrC,CAAC,MAAM,IAAIhK,OAAO,EAAE;UAClB2B,MAAM,GAAG,IAAIuG,MAAM,CAAClI,OAAO,CAACiK,MAAM,EAAED,KAAK,CAAC;QAC5C;QACA,OAAOrI,MAAM;MACf,CAAC;MAEDyG,YAAY,CAAC5I,SAAS,CAAC0K,kBAAkB,GAAG,UAASC,cAAc,EAAE;QACnE,OAAOjC,MAAM,CAACiC,cAAc,CAAClG,OAAO,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;MACzE,CAAC;;MAED;MACAmE,YAAY,CAAC5I,SAAS,CAAC4K,cAAc,GAAG,UAASpK,OAAO,EAAE;QACxD,IAAIqK,KAAK,GAAG,IAAI,CAAC7B,UAAU;QAC3B,IAAII,GAAG,GAAG,IAAI,CAACiB,cAAc,CAAC7J,OAAO,CAAC;QACtC,IAAI,CAACwI,UAAU,GAAG6B,KAAK;QACvB,OAAOzB,GAAG;MACZ,CAAC;MAEDR,YAAY,CAAC5I,SAAS,CAAC8K,QAAQ,GAAG,UAASC,OAAO,EAAE;QAClD,IAAIF,KAAK,GAAG,IAAI,CAAC7B,UAAU,GAAG,CAAC;QAC/B,OAAO6B,KAAK,IAAIE,OAAO,CAACzK,MAAM,IAAI,IAAI,CAACwI,OAAO,CAACsB,SAAS,CAACS,KAAK,GAAGE,OAAO,CAACzK,MAAM,EAAEuK,KAAK,CAAC,CACpFG,WAAW,CAAC,CAAC,KAAKD,OAAO;MAC9B,CAAC;MAED3L,MAAM,CAAC2G,OAAO,CAAC6C,YAAY,GAAGA,YAAY;;MAG1C;IAAM,CAAC,IACP;IACA,KAAO,UAASxJ,MAAM,EAAE6L,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAItC,YAAY,GAAIsC,mBAAmB,CAAC,CAAC,CAAC,CAACtC,YAAa;MACxD,IAAI5C,KAAK,GAAIkF,mBAAmB,CAAC,CAAC,CAAC,CAAClF,KAAM;MAC1C,IAAImF,WAAW,GAAID,mBAAmB,CAAC,EAAE,CAAC,CAACC,WAAY;MACvD,IAAIC,iBAAiB,GAAIF,mBAAmB,CAAC,EAAE,CAAC,CAACE,iBAAkB;MAEnE,IAAIC,KAAK,GAAG;QACVC,KAAK,EAAE,UAAU;QACjBC,GAAG,EAAE,QAAQ;QACbC,GAAG,EAAE;MACP,CAAC;MAED,IAAIC,SAAS,GAAG,SAAAA,CAAS5C,YAAY,EAAErG,OAAO,EAAE;QAC9C,IAAI,CAACkJ,MAAM,GAAG,IAAI9C,YAAY,CAACC,YAAY,CAAC;QAC5C,IAAI,CAAC8C,QAAQ,GAAGnJ,OAAO,IAAI,CAAC,CAAC;QAC7B,IAAI,CAACoJ,QAAQ,GAAG,IAAI;QAEpB,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAACA,SAAS,CAACC,UAAU,GAAG,IAAIV,iBAAiB,CAAC,IAAI,CAACM,MAAM,CAAC;MAChE,CAAC;MAEDD,SAAS,CAACzL,SAAS,CAAC+L,QAAQ,GAAG,YAAW;QACxC,IAAI,CAACL,MAAM,CAACzC,OAAO,CAAC,CAAC;QACrB,IAAI,CAAC2C,QAAQ,GAAG,IAAIT,WAAW,CAAC,CAAC;QAEjC,IAAI,CAACa,MAAM,CAAC,CAAC;QAEb,IAAIC,OAAO;QACX,IAAI9F,QAAQ,GAAG,IAAIH,KAAK,CAACqF,KAAK,CAACC,KAAK,EAAE,EAAE,CAAC;QACzC,IAAIY,UAAU,GAAG,IAAI;QACrB,IAAIC,UAAU,GAAG,EAAE;QACnB,IAAIC,QAAQ,GAAG,IAAIjB,WAAW,CAAC,CAAC;QAEhC,OAAOhF,QAAQ,CAACF,IAAI,KAAKoF,KAAK,CAACG,GAAG,EAAE;UAClCS,OAAO,GAAG,IAAI,CAACI,eAAe,CAAClG,QAAQ,EAAE+F,UAAU,CAAC;UACpD,OAAO,IAAI,CAACI,WAAW,CAACL,OAAO,CAAC,EAAE;YAChCG,QAAQ,CAACG,GAAG,CAACN,OAAO,CAAC;YACrBA,OAAO,GAAG,IAAI,CAACI,eAAe,CAAClG,QAAQ,EAAE+F,UAAU,CAAC;UACtD;UAEA,IAAI,CAACE,QAAQ,CAACI,OAAO,CAAC,CAAC,EAAE;YACvBP,OAAO,CAAC/F,eAAe,GAAGkG,QAAQ;YAClCA,QAAQ,GAAG,IAAIjB,WAAW,CAAC,CAAC;UAC9B;UAEAc,OAAO,CAAC3M,MAAM,GAAG4M,UAAU;UAE3B,IAAI,IAAI,CAACO,WAAW,CAACR,OAAO,CAAC,EAAE;YAC7BE,UAAU,CAACzK,IAAI,CAACwK,UAAU,CAAC;YAC3BA,UAAU,GAAGD,OAAO;UACtB,CAAC,MAAM,IAAIC,UAAU,IAAI,IAAI,CAACQ,WAAW,CAACT,OAAO,EAAEC,UAAU,CAAC,EAAE;YAC9DD,OAAO,CAAC7F,MAAM,GAAG8F,UAAU;YAC3BA,UAAU,CAAC7F,MAAM,GAAG4F,OAAO;YAC3BC,UAAU,GAAGC,UAAU,CAACtK,GAAG,CAAC,CAAC;YAC7BoK,OAAO,CAAC3M,MAAM,GAAG4M,UAAU;UAC7B;UAEAD,OAAO,CAAC9F,QAAQ,GAAGA,QAAQ;UAC3BA,QAAQ,CAAC9E,IAAI,GAAG4K,OAAO;UAEvB,IAAI,CAACL,QAAQ,CAACW,GAAG,CAACN,OAAO,CAAC;UAC1B9F,QAAQ,GAAG8F,OAAO;QACpB;QAEA,OAAO,IAAI,CAACL,QAAQ;MACtB,CAAC;MAGDH,SAAS,CAACzL,SAAS,CAAC2M,eAAe,GAAG,YAAW;QAC/C,OAAO,IAAI,CAACf,QAAQ,CAACY,OAAO,CAAC,CAAC;MAChC,CAAC;MAEDf,SAAS,CAACzL,SAAS,CAACgM,MAAM,GAAG,YAAW,CAAC,CAAC;MAE1CP,SAAS,CAACzL,SAAS,CAACqM,eAAe,GAAG,UAASO,cAAc,EAAEV,UAAU,EAAE;QAAE;QAC3E,IAAI,CAACW,eAAe,CAAC,CAAC;QACtB,IAAIC,gBAAgB,GAAG,IAAI,CAACpB,MAAM,CAAC5B,IAAI,CAAC,KAAK,CAAC;QAC9C,IAAIgD,gBAAgB,EAAE;UACpB,OAAO,IAAI,CAACC,aAAa,CAAC1B,KAAK,CAACE,GAAG,EAAEuB,gBAAgB,CAAC;QACxD,CAAC,MAAM;UACL,OAAO,IAAI,CAACC,aAAa,CAAC1B,KAAK,CAACG,GAAG,EAAE,EAAE,CAAC;QAC1C;MACF,CAAC;MAEDC,SAAS,CAACzL,SAAS,CAACsM,WAAW,GAAG,UAASU,aAAa,EAAE;QAAE;QAC1D,OAAO,KAAK;MACd,CAAC;MAEDvB,SAAS,CAACzL,SAAS,CAACyM,WAAW,GAAG,UAASO,aAAa,EAAE;QAAE;QAC1D,OAAO,KAAK;MACd,CAAC;MAEDvB,SAAS,CAACzL,SAAS,CAAC0M,WAAW,GAAG,UAASM,aAAa,EAAEd,UAAU,EAAE;QAAE;QACtE,OAAO,KAAK;MACd,CAAC;MAEDT,SAAS,CAACzL,SAAS,CAAC+M,aAAa,GAAG,UAAS9G,IAAI,EAAEhB,IAAI,EAAE;QACvD,IAAIJ,KAAK,GAAG,IAAImB,KAAK,CAACC,IAAI,EAAEhB,IAAI,EAC9B,IAAI,CAAC4G,SAAS,CAACC,UAAU,CAACmB,aAAa,EACvC,IAAI,CAACpB,SAAS,CAACC,UAAU,CAACoB,uBAAuB,CAAC;QACpD,OAAOrI,KAAK;MACd,CAAC;MAED4G,SAAS,CAACzL,SAAS,CAAC6M,eAAe,GAAG,YAAW;QAC/C,OAAO,IAAI,CAAChB,SAAS,CAACC,UAAU,CAAChC,IAAI,CAAC,CAAC;MACzC,CAAC;MAID1K,MAAM,CAAC2G,OAAO,CAAC0F,SAAS,GAAGA,SAAS;MACpCrM,MAAM,CAAC2G,OAAO,CAACsF,KAAK,GAAGA,KAAK;;MAG5B;IAAM,CAAC,IACP;IACA,KAAO,UAASjM,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,SAAS+L,WAAWA,CAACgC,YAAY,EAAE;QACjC;QACA,IAAI,CAACvB,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACwB,eAAe,GAAG,IAAI,CAACxB,QAAQ,CAACtL,MAAM;QAC3C,IAAI,CAAC0I,UAAU,GAAG,CAAC;QACnB,IAAI,CAACqE,cAAc,GAAGF,YAAY;MACpC;MAEAhC,WAAW,CAACnL,SAAS,CAACiJ,OAAO,GAAG,YAAW;QACzC,IAAI,CAACD,UAAU,GAAG,CAAC;MACrB,CAAC;MAEDmC,WAAW,CAACnL,SAAS,CAACwM,OAAO,GAAG,YAAW;QACzC,OAAO,IAAI,CAACY,eAAe,KAAK,CAAC;MACnC,CAAC;MAEDjC,WAAW,CAACnL,SAAS,CAACmJ,OAAO,GAAG,YAAW;QACzC,OAAO,IAAI,CAACH,UAAU,GAAG,IAAI,CAACoE,eAAe;MAC/C,CAAC;MAEDjC,WAAW,CAACnL,SAAS,CAACqB,IAAI,GAAG,YAAW;QACtC,IAAI+H,GAAG,GAAG,IAAI;QACd,IAAI,IAAI,CAACD,OAAO,CAAC,CAAC,EAAE;UAClBC,GAAG,GAAG,IAAI,CAACwC,QAAQ,CAAC,IAAI,CAAC5C,UAAU,CAAC;UACpC,IAAI,CAACA,UAAU,IAAI,CAAC;QACtB;QACA,OAAOI,GAAG;MACZ,CAAC;MAED+B,WAAW,CAACnL,SAAS,CAACsJ,IAAI,GAAG,UAASjJ,KAAK,EAAE;QAC3C,IAAI+I,GAAG,GAAG,IAAI;QACd/I,KAAK,GAAGA,KAAK,IAAI,CAAC;QAClBA,KAAK,IAAI,IAAI,CAAC2I,UAAU;QACxB,IAAI3I,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAAC+M,eAAe,EAAE;UAC9ChE,GAAG,GAAG,IAAI,CAACwC,QAAQ,CAACvL,KAAK,CAAC;QAC5B;QACA,OAAO+I,GAAG;MACZ,CAAC;MAED+B,WAAW,CAACnL,SAAS,CAACuM,GAAG,GAAG,UAAS1H,KAAK,EAAE;QAC1C,IAAI,IAAI,CAACwI,cAAc,EAAE;UACvBxI,KAAK,CAACvF,MAAM,GAAG,IAAI,CAAC+N,cAAc;QACpC;QACA,IAAI,CAACzB,QAAQ,CAAClK,IAAI,CAACmD,KAAK,CAAC;QACzB,IAAI,CAACuI,eAAe,IAAI,CAAC;MAC3B,CAAC;MAEDhO,MAAM,CAAC2G,OAAO,CAACoF,WAAW,GAAGA,WAAW;;MAGxC;IAAM,CAAC,IACP;IACA,KAAO,UAAS/L,MAAM,EAAE6L,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAIoC,OAAO,GAAIpC,mBAAmB,CAAC,EAAE,CAAC,CAACoC,OAAQ;MAE/C,SAASlC,iBAAiBA,CAACmC,aAAa,EAAEjO,MAAM,EAAE;QAChDgO,OAAO,CAACE,IAAI,CAAC,IAAI,EAAED,aAAa,EAAEjO,MAAM,CAAC;QACzC,IAAIA,MAAM,EAAE;UACV,IAAI,CAACmO,YAAY,GAAG,IAAI,CAAC/B,MAAM,CAACpB,UAAU,CAAChL,MAAM,CAACmO,YAAY,CAAC;QACjE,CAAC,MAAM;UACL,IAAI,CAACC,yBAAyB,CAAC,EAAE,EAAE,EAAE,CAAC;QACxC;QAEA,IAAI,CAACT,aAAa,GAAG,CAAC;QACtB,IAAI,CAACC,uBAAuB,GAAG,EAAE;MACnC;MACA9B,iBAAiB,CAACpL,SAAS,GAAG,IAAIsN,OAAO,CAAC,CAAC;MAE3ClC,iBAAiB,CAACpL,SAAS,CAAC0N,yBAAyB,GAAG,UAASC,gBAAgB,EAAEC,aAAa,EAAE;QAChGD,gBAAgB,IAAI,MAAM;QAC1BC,aAAa,IAAI,QAAQ;QAEzB,IAAI,CAACC,cAAc,GAAG,IAAI,CAACnC,MAAM,CAACpB,UAAU,CAC1C,GAAG,GAAGqD,gBAAgB,GAAGC,aAAa,GAAG,IAAI,EAAE,IAAI,CAAC;QACtD,IAAI,CAACE,eAAe,GAAG,IAAI,CAACpC,MAAM,CAACpB,UAAU,CAC3C,UAAU,GAAGsD,aAAa,GAAG,GAAG,CAAC;MACrC,CAAC;MAEDxC,iBAAiB,CAACpL,SAAS,CAAC8J,IAAI,GAAG,YAAW;QAC5C,IAAI,CAACmD,aAAa,GAAG,CAAC;QACtB,IAAI,CAACC,uBAAuB,GAAG,EAAE;QAEjC,IAAIJ,gBAAgB,GAAG,IAAI,CAACpB,MAAM,CAAC5B,IAAI,CAAC,IAAI,CAAC+D,cAAc,CAAC;QAC5D,IAAIf,gBAAgB,KAAK,GAAG,EAAE;UAC5B,IAAI,CAACI,uBAAuB,GAAG,GAAG;QACpC,CAAC,MAAM,IAAIJ,gBAAgB,EAAE;UAC3B,IAAIiB,OAAO,GAAG,IAAI,CAACC,OAAO,CAAC,IAAI,CAACF,eAAe,EAAEhB,gBAAgB,CAAC;UAClE,IAAI,CAACG,aAAa,GAAGc,OAAO,CAACzN,MAAM,GAAG,CAAC;UACvC,IAAI,CAAC4M,uBAAuB,GAAGa,OAAO,CAAC,IAAI,CAACd,aAAa,CAAC;QAC5D;QAEA,OAAOH,gBAAgB;MACzB,CAAC;MAED1B,iBAAiB,CAACpL,SAAS,CAACiO,QAAQ,GAAG,UAASN,gBAAgB,EAAEC,aAAa,EAAE;QAC/E,IAAIzL,MAAM,GAAG,IAAI,CAAC+L,OAAO,CAAC,CAAC;QAC3B/L,MAAM,CAACuL,yBAAyB,CAACC,gBAAgB,EAAEC,aAAa,CAAC;QACjEzL,MAAM,CAACgM,OAAO,CAAC,CAAC;QAChB,OAAOhM,MAAM;MACf,CAAC;MAEDiJ,iBAAiB,CAACpL,SAAS,CAACkO,OAAO,GAAG,YAAW;QAC/C,OAAO,IAAI9C,iBAAiB,CAAC,IAAI,CAACM,MAAM,EAAE,IAAI,CAAC;MACjD,CAAC;MAEDN,iBAAiB,CAACpL,SAAS,CAACgO,OAAO,GAAG,UAASI,MAAM,EAAEvF,YAAY,EAAE;QACnEuF,MAAM,CAAC5E,SAAS,GAAG,CAAC;QACpB,IAAI6E,WAAW,GAAG,CAAC;QACnB,IAAIlM,MAAM,GAAG,EAAE;QACf,IAAImM,UAAU,GAAGF,MAAM,CAAC1E,IAAI,CAACb,YAAY,CAAC;QAC1C,OAAOyF,UAAU,EAAE;UACjBnM,MAAM,CAACT,IAAI,CAACmH,YAAY,CAACuB,SAAS,CAACiE,WAAW,EAAEC,UAAU,CAACjO,KAAK,CAAC,CAAC;UAClEgO,WAAW,GAAGC,UAAU,CAACjO,KAAK,GAAGiO,UAAU,CAAC,CAAC,CAAC,CAAChO,MAAM;UACrDgO,UAAU,GAAGF,MAAM,CAAC1E,IAAI,CAACb,YAAY,CAAC;QACxC;QAEA,IAAIwF,WAAW,GAAGxF,YAAY,CAACvI,MAAM,EAAE;UACrC6B,MAAM,CAACT,IAAI,CAACmH,YAAY,CAACuB,SAAS,CAACiE,WAAW,EAAExF,YAAY,CAACvI,MAAM,CAAC,CAAC;QACvE,CAAC,MAAM;UACL6B,MAAM,CAACT,IAAI,CAAC,EAAE,CAAC;QACjB;QAEA,OAAOS,MAAM;MACf,CAAC;MAID/C,MAAM,CAAC2G,OAAO,CAACqF,iBAAiB,GAAGA,iBAAiB;;MAGpD;IAAM,CAAC,IACP;IACA,KAAO,UAAShM,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,SAASkO,OAAOA,CAACC,aAAa,EAAEjO,MAAM,EAAE;QACtC,IAAI,CAACoM,MAAM,GAAG6B,aAAa;QAC3B,IAAI,CAACgB,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACV,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACW,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACC,YAAY,GAAG,KAAK;QAEzB,IAAInP,MAAM,EAAE;UACV,IAAI,CAACiP,iBAAiB,GAAG,IAAI,CAAC7C,MAAM,CAACpB,UAAU,CAAChL,MAAM,CAACiP,iBAAiB,EAAE,IAAI,CAAC;UAC/E,IAAI,CAACV,cAAc,GAAG,IAAI,CAACnC,MAAM,CAACpB,UAAU,CAAChL,MAAM,CAACuO,cAAc,EAAE,IAAI,CAAC;UACzE,IAAI,CAACW,cAAc,GAAG,IAAI,CAAC9C,MAAM,CAACpB,UAAU,CAAChL,MAAM,CAACkP,cAAc,CAAC;UACnE,IAAI,CAACC,YAAY,GAAGnP,MAAM,CAACmP,YAAY;QACzC;MACF;MAEAnB,OAAO,CAACtN,SAAS,CAAC8J,IAAI,GAAG,YAAW;QAClC,IAAI3H,MAAM,GAAG,IAAI,CAACuJ,MAAM,CAAC5B,IAAI,CAAC,IAAI,CAACyE,iBAAiB,CAAC;QACrD,IAAI,CAAC,IAAI,CAACA,iBAAiB,IAAIpM,MAAM,EAAE;UACrCA,MAAM,IAAI,IAAI,CAACuJ,MAAM,CAAC5B,IAAI,CAAC,IAAI,CAAC+D,cAAc,EAAE,IAAI,CAACW,cAAc,EAAE,IAAI,CAACC,YAAY,CAAC;QACzF;QACA,OAAOtM,MAAM;MACf,CAAC;MAEDmL,OAAO,CAACtN,SAAS,CAAC0O,UAAU,GAAG,YAAW;QACxC,OAAO,IAAI,CAAChD,MAAM,CAAChL,KAAK,CAAC,IAAI,CAACmN,cAAc,CAAC;MAC/C,CAAC;MAEDP,OAAO,CAACtN,SAAS,CAACiK,WAAW,GAAG,UAASzJ,OAAO,EAAE;QAChD,IAAI2B,MAAM,GAAG,IAAI,CAAC+L,OAAO,CAAC,CAAC;QAC3B/L,MAAM,CAACsM,YAAY,GAAG,IAAI;QAC1BtM,MAAM,CAACqM,cAAc,GAAG,IAAI,CAAC9C,MAAM,CAACpB,UAAU,CAAC9J,OAAO,CAAC;QACvD2B,MAAM,CAACgM,OAAO,CAAC,CAAC;QAChB,OAAOhM,MAAM;MACf,CAAC;MAEDmL,OAAO,CAACtN,SAAS,CAAC2O,KAAK,GAAG,UAASnO,OAAO,EAAE;QAC1C,IAAI2B,MAAM,GAAG,IAAI,CAAC+L,OAAO,CAAC,CAAC;QAC3B/L,MAAM,CAACsM,YAAY,GAAG,KAAK;QAC3BtM,MAAM,CAACqM,cAAc,GAAG,IAAI,CAAC9C,MAAM,CAACpB,UAAU,CAAC9J,OAAO,CAAC;QACvD2B,MAAM,CAACgM,OAAO,CAAC,CAAC;QAChB,OAAOhM,MAAM;MACf,CAAC;MAEDmL,OAAO,CAACtN,SAAS,CAAC4O,aAAa,GAAG,UAASpO,OAAO,EAAE;QAClD,IAAI2B,MAAM,GAAG,IAAI,CAAC+L,OAAO,CAAC,CAAC;QAC3B/L,MAAM,CAACoM,iBAAiB,GAAG,IAAI,CAAC7C,MAAM,CAACpB,UAAU,CAAC9J,OAAO,EAAE,IAAI,CAAC;QAChE2B,MAAM,CAACgM,OAAO,CAAC,CAAC;QAChB,OAAOhM,MAAM;MACf,CAAC;MAEDmL,OAAO,CAACtN,SAAS,CAACiO,QAAQ,GAAG,UAASzN,OAAO,EAAE;QAC7C,IAAI2B,MAAM,GAAG,IAAI,CAAC+L,OAAO,CAAC,CAAC;QAC3B/L,MAAM,CAAC0L,cAAc,GAAG,IAAI,CAACnC,MAAM,CAACpB,UAAU,CAAC9J,OAAO,EAAE,IAAI,CAAC;QAC7D2B,MAAM,CAACgM,OAAO,CAAC,CAAC;QAChB,OAAOhM,MAAM;MACf,CAAC;MAEDmL,OAAO,CAACtN,SAAS,CAACkO,OAAO,GAAG,YAAW;QACrC,OAAO,IAAIZ,OAAO,CAAC,IAAI,CAAC5B,MAAM,EAAE,IAAI,CAAC;MACvC,CAAC;MAED4B,OAAO,CAACtN,SAAS,CAACmO,OAAO,GAAG,YAAW,CAAC,CAAC;MAEzC/O,MAAM,CAAC2G,OAAO,CAACuH,OAAO,GAAGA,OAAO;;MAGhC;IAAM,CAAC,IACP;IACA,KAAO,UAASlO,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,SAASyP,UAAUA,CAACC,mBAAmB,EAAEC,iBAAiB,EAAE;QAC1DD,mBAAmB,GAAG,OAAOA,mBAAmB,KAAK,QAAQ,GAAGA,mBAAmB,GAAGA,mBAAmB,CAACrE,MAAM;QAChHsE,iBAAiB,GAAG,OAAOA,iBAAiB,KAAK,QAAQ,GAAGA,iBAAiB,GAAGA,iBAAiB,CAACtE,MAAM;QACxG,IAAI,CAACuE,0BAA0B,GAAG,IAAItG,MAAM,CAACoG,mBAAmB,GAAG,yBAAyB,CAACrE,MAAM,GAAGsE,iBAAiB,EAAE,GAAG,CAAC;QAC7H,IAAI,CAACE,mBAAmB,GAAG,iBAAiB;QAE5C,IAAI,CAACC,+BAA+B,GAAG,IAAIxG,MAAM,CAACoG,mBAAmB,GAAG,0BAA0B,CAACrE,MAAM,GAAGsE,iBAAiB,EAAE,GAAG,CAAC;MACrI;MAEAF,UAAU,CAAC7O,SAAS,CAACmP,cAAc,GAAG,UAASlK,IAAI,EAAE;QACnD,IAAI,CAACA,IAAI,CAACvE,KAAK,CAAC,IAAI,CAACsO,0BAA0B,CAAC,EAAE;UAChD,OAAO,IAAI;QACb;QAEA,IAAI1I,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC2I,mBAAmB,CAACzF,SAAS,GAAG,CAAC;QACtC,IAAI4F,eAAe,GAAG,IAAI,CAACH,mBAAmB,CAACvF,IAAI,CAACzE,IAAI,CAAC;QAEzD,OAAOmK,eAAe,EAAE;UACtB9I,UAAU,CAAC8I,eAAe,CAAC,CAAC,CAAC,CAAC,GAAGA,eAAe,CAAC,CAAC,CAAC;UACnDA,eAAe,GAAG,IAAI,CAACH,mBAAmB,CAACvF,IAAI,CAACzE,IAAI,CAAC;QACvD;QAEA,OAAOqB,UAAU;MACnB,CAAC;MAEDuI,UAAU,CAAC7O,SAAS,CAACqP,WAAW,GAAG,UAASC,KAAK,EAAE;QACjD,OAAOA,KAAK,CAACjF,cAAc,CAAC,IAAI,CAAC6E,+BAA+B,CAAC;MACnE,CAAC;MAGD9P,MAAM,CAAC2G,OAAO,CAAC8I,UAAU,GAAGA,UAAU;;MAGtC;IAAM,CAAC,IACP;IACA,KAAO,UAASzP,MAAM,EAAE6L,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAIoC,OAAO,GAAIpC,mBAAmB,CAAC,EAAE,CAAC,CAACoC,OAAQ;MAG/C,IAAIiC,cAAc,GAAG;QACnBC,MAAM,EAAE,KAAK;QACbC,GAAG,EAAE,KAAK;QACVC,UAAU,EAAE,KAAK;QACjBC,GAAG,EAAE,KAAK;QACVC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;MACX,CAAC;;MAED;MACA;MACA,SAASC,kBAAkBA,CAACvC,aAAa,EAAEjO,MAAM,EAAE;QACjDgO,OAAO,CAACE,IAAI,CAAC,IAAI,EAAED,aAAa,EAAEjO,MAAM,CAAC;QACzC,IAAI,CAACyQ,kBAAkB,GAAG,IAAI;QAC9B,IAAI,CAACC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEX,cAAc,CAAC;QAClD,IAAI,CAACY,SAAS,GAAGF,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEX,cAAc,CAAC;QAElD,IAAIjQ,MAAM,EAAE;UACV,IAAI,CAACyQ,kBAAkB,GAAG,IAAI,CAACrE,MAAM,CAACpB,UAAU,CAAChL,MAAM,CAACyQ,kBAAkB,CAAC;UAC3E,IAAI,CAACI,SAAS,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAACC,SAAS,EAAE7Q,MAAM,CAAC6Q,SAAS,CAAC;UAChE,IAAI,CAACH,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACF,SAAS,EAAE1Q,MAAM,CAAC0Q,SAAS,CAAC;QAClE;QACA,IAAIxP,OAAO,GAAG,IAAI8M,OAAO,CAACC,aAAa,CAAC;QACxC,IAAI,CAAC6C,UAAU,GAAG;UAChBC,kBAAkB,EAAE7P,OAAO,CAACoO,aAAa,CAAC,OAAO,CAAC,CAAC3E,WAAW,CAAC,MAAM,CAAC;UACtEqG,oBAAoB,EAAE9P,OAAO,CAACoO,aAAa,CAAC,KAAK,CAAC,CAAC3E,WAAW,CAAC,KAAK,CAAC;UACrEyF,UAAU,EAAElP,OAAO,CAACoO,aAAa,CAAC,IAAI,CAAC,CAAC3E,WAAW,CAAC,IAAI,CAAC;UACzD0F,GAAG,EAAEnP,OAAO,CAACoO,aAAa,CAAC,iBAAiB,CAAC,CAAC3E,WAAW,CAAC,KAAK,CAAC;UAChEwF,GAAG,EAAEjP,OAAO,CAACoO,aAAa,CAAC,QAAQ,CAAC,CAAC3E,WAAW,CAAC,QAAQ,CAAC;UAC1D;UACAuF,MAAM,EAAEhP,OAAO,CAACoO,aAAa,CAAC,IAAI,CAAC,CAAC3E,WAAW,CAAC,IAAI,CAAC;UACrDsG,YAAY,EAAE/P,OAAO,CAACoO,aAAa,CAAC,IAAI,CAAC,CAAC3E,WAAW,CAAC,IAAI,CAAC;UAC3DuG,cAAc,EAAEhQ,OAAO,CAACoO,aAAa,CAAC,IAAI,CAAC,CAAC3E,WAAW,CAAC,IAAI,CAAC;UAC7D2F,MAAM,EAAEpP,OAAO,CAACoO,aAAa,CAAC,gBAAgB,CAAC,CAAC3E,WAAW,CAAC,UAAU,CAAC;UACvEwG,cAAc,EAAEjQ,OAAO,CAACoO,aAAa,CAAC,KAAK,CAAC,CAAC3E,WAAW,CAAC,KAAK,CAAC;UAC/DyG,cAAc,EAAElQ,OAAO,CAACoO,aAAa,CAAC,WAAW,CAAC,CAAC3E,WAAW,CAAC,aAAa;QAC9E,CAAC;MACH;MACA6F,kBAAkB,CAAC9P,SAAS,GAAG,IAAIsN,OAAO,CAAC,CAAC;MAE5CwC,kBAAkB,CAAC9P,SAAS,CAACkO,OAAO,GAAG,YAAW;QAChD,OAAO,IAAI4B,kBAAkB,CAAC,IAAI,CAACpE,MAAM,EAAE,IAAI,CAAC;MAClD,CAAC;MAEDoE,kBAAkB,CAAC9P,SAAS,CAACmO,OAAO,GAAG,YAAW;QAChD,IAAI,CAACwC,uBAAuB,CAAC,CAAC;MAChC,CAAC;MAEDb,kBAAkB,CAAC9P,SAAS,CAAC4Q,OAAO,GAAG,UAASC,QAAQ,EAAE;QACxD,IAAI1O,MAAM,GAAG,IAAI,CAAC+L,OAAO,CAAC,CAAC;QAC3B/L,MAAM,CAAC6N,SAAS,CAACa,QAAQ,CAAC,GAAG,IAAI;QACjC1O,MAAM,CAACgM,OAAO,CAAC,CAAC;QAChB,OAAOhM,MAAM;MACf,CAAC;MAED2N,kBAAkB,CAAC9P,SAAS,CAAC8Q,YAAY,GAAG,UAAStO,OAAO,EAAE;QAC5D,IAAIL,MAAM,GAAG,IAAI,CAAC+L,OAAO,CAAC,CAAC;QAC3B,KAAK,IAAI2C,QAAQ,IAAItB,cAAc,EAAE;UACnCpN,MAAM,CAAC6N,SAAS,CAACa,QAAQ,CAAC,GAAGrO,OAAO,CAACyE,UAAU,CAACnB,OAAO,CAAC+K,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC1E;QACA1O,MAAM,CAACgM,OAAO,CAAC,CAAC;QAChB,OAAOhM,MAAM;MACf,CAAC;MAED2N,kBAAkB,CAAC9P,SAAS,CAAC+Q,OAAO,GAAG,UAASF,QAAQ,EAAE;QACxD,IAAI1O,MAAM,GAAG,IAAI,CAAC+L,OAAO,CAAC,CAAC;QAC3B/L,MAAM,CAACgO,SAAS,CAACU,QAAQ,CAAC,GAAG,IAAI;QACjC1O,MAAM,CAACgM,OAAO,CAAC,CAAC;QAChB,OAAOhM,MAAM;MACf,CAAC;MAED2N,kBAAkB,CAAC9P,SAAS,CAAC8J,IAAI,GAAG,YAAW;QAC7C,IAAI3H,MAAM,GAAG,EAAE;QACf,IAAI,IAAI,CAAC0L,cAAc,EAAE;UACvB1L,MAAM,GAAG,IAAI,CAACuJ,MAAM,CAAC5B,IAAI,CAAC,IAAI,CAACyE,iBAAiB,CAAC;QACnD,CAAC,MAAM;UACLpM,MAAM,GAAG,IAAI,CAACuJ,MAAM,CAAC5B,IAAI,CAAC,IAAI,CAACyE,iBAAiB,EAAE,IAAI,CAACwB,kBAAkB,CAAC;QAC5E;QACA,IAAI1O,IAAI,GAAG,IAAI,CAAC2P,cAAc,CAAC,CAAC;QAChC,OAAO3P,IAAI,EAAE;UACX,IAAI,IAAI,CAACwM,cAAc,EAAE;YACvBxM,IAAI,IAAI,IAAI,CAACqK,MAAM,CAAC5B,IAAI,CAAC,IAAI,CAAC+D,cAAc,CAAC;UAC/C,CAAC,MAAM;YACLxM,IAAI,IAAI,IAAI,CAACqK,MAAM,CAACxB,SAAS,CAAC,IAAI,CAAC6F,kBAAkB,CAAC;UACxD;UACA5N,MAAM,IAAId,IAAI;UACdA,IAAI,GAAG,IAAI,CAAC2P,cAAc,CAAC,CAAC;QAC9B;QAEA,IAAI,IAAI,CAACvC,YAAY,EAAE;UACrBtM,MAAM,IAAI,IAAI,CAACuJ,MAAM,CAACrB,cAAc,CAAC,IAAI,CAACmE,cAAc,CAAC;QAC3D;QACA,OAAOrM,MAAM;MACf,CAAC;MAED2N,kBAAkB,CAAC9P,SAAS,CAAC2Q,uBAAuB,GAAG,YAAW;QAChE,IAAIM,KAAK,GAAG,EAAE;QAEd,IAAI,CAAC,IAAI,CAACjB,SAAS,CAACL,GAAG,EAAE;UACvBsB,KAAK,CAACvP,IAAI,CAAC,IAAI,CAAC0O,UAAU,CAACT,GAAG,CAACpB,iBAAiB,CAAC9D,MAAM,CAAC;QAC1D;QACA,IAAI,CAAC,IAAI,CAACuF,SAAS,CAACN,UAAU,EAAE;UAC9BuB,KAAK,CAACvP,IAAI,CAAC,IAAI,CAAC0O,UAAU,CAACV,UAAU,CAACnB,iBAAiB,CAAC9D,MAAM,CAAC;QACjE;QACA,IAAI,CAAC,IAAI,CAACuF,SAAS,CAACH,OAAO,EAAE;UAC3B;UACAoB,KAAK,CAACvP,IAAI,CAAC,IAAI,CAAC0O,UAAU,CAACV,UAAU,CAACnB,iBAAiB,CAAC9D,MAAM,CAAC;QACjE;QACA,IAAI,CAAC,IAAI,CAACuF,SAAS,CAACP,GAAG,EAAE;UACvBwB,KAAK,CAACvP,IAAI,CAAC,IAAI,CAAC0O,UAAU,CAACX,GAAG,CAAClB,iBAAiB,CAAC9D,MAAM,CAAC;QAC1D;QACA,IAAI,CAAC,IAAI,CAACuF,SAAS,CAACR,MAAM,EAAE;UAC1ByB,KAAK,CAACvP,IAAI,CAAC,IAAI,CAAC0O,UAAU,CAACZ,MAAM,CAACjB,iBAAiB,CAAC9D,MAAM,CAAC;UAC3D;UACA;UACAwG,KAAK,CAACvP,IAAI,CAAC,IAAI,CAAC0O,UAAU,CAACG,YAAY,CAAChC,iBAAiB,CAAC9D,MAAM,CAAC;UACjEwG,KAAK,CAACvP,IAAI,CAAC,IAAI,CAAC0O,UAAU,CAACI,cAAc,CAACjC,iBAAiB,CAAC9D,MAAM,CAAC;QACrE;QACA,IAAI,CAAC,IAAI,CAACuF,SAAS,CAACJ,MAAM,EAAE;UAC1BqB,KAAK,CAACvP,IAAI,CAAC,IAAI,CAAC0O,UAAU,CAACR,MAAM,CAACrB,iBAAiB,CAAC9D,MAAM,CAAC;QAC7D;QAEA,IAAI,IAAI,CAAC+D,cAAc,EAAE;UACvByC,KAAK,CAACvP,IAAI,CAAC,IAAI,CAAC8M,cAAc,CAAC/D,MAAM,CAAC;QACxC;QACA,IAAI,CAACsF,kBAAkB,GAAG,IAAI,CAACrE,MAAM,CAACpB,UAAU,CAAC,KAAK,GAAG2G,KAAK,CAAC3O,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;MACjF,CAAC;MAEDwN,kBAAkB,CAAC9P,SAAS,CAACgR,cAAc,GAAG,YAAW;QACvD,IAAIlE,gBAAgB,GAAG,EAAE;QACzB,IAAIoE,CAAC,GAAG,IAAI,CAACxF,MAAM,CAACpC,IAAI,CAAC,CAAC;QAC1B,IAAI4H,CAAC,KAAK,GAAG,EAAE;UACb,IAAIC,KAAK,GAAG,IAAI,CAACzF,MAAM,CAACpC,IAAI,CAAC,CAAC,CAAC;UAC/B;UACA;UACA;UACA,IAAI,CAAC,IAAI,CAAC0G,SAAS,CAACL,GAAG,IAAI,CAAC,IAAI,CAACQ,SAAS,CAACR,GAAG,IAAIwB,KAAK,KAAK,GAAG,EAAE;YAC/DrE,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAACsD,UAAU,CAACT,GAAG,CAAC7F,IAAI,CAAC,CAAC;UAC9B;UACA,IAAI,CAAC,IAAI,CAACkG,SAAS,CAACP,GAAG,IAAI,CAAC,IAAI,CAACU,SAAS,CAACV,GAAG,IAAI0B,KAAK,KAAK,GAAG,EAAE;YAC/DrE,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAACsD,UAAU,CAACX,GAAG,CAAC3F,IAAI,CAAC,CAAC;UAC9B;QACF,CAAC,MAAM,IAAIoH,CAAC,KAAK,GAAG,EAAE;UACpB,IAAI,CAAC,IAAI,CAAClB,SAAS,CAACN,UAAU,IAAI,CAAC,IAAI,CAACS,SAAS,CAACT,UAAU,EAAE;YAC5D5C,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAACsD,UAAU,CAACC,kBAAkB,CAACvG,IAAI,CAAC,CAAC;YAC3CgD,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAACsD,UAAU,CAACE,oBAAoB,CAACxG,IAAI,CAAC,CAAC;YAC7CgD,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAACsD,UAAU,CAACV,UAAU,CAAC5F,IAAI,CAAC,CAAC;UACrC;UACA,IAAI,CAAC,IAAI,CAACkG,SAAS,CAACR,MAAM,EAAE;YAC1B;YACA,IAAI,CAAC,IAAI,CAACW,SAAS,CAACX,MAAM,IAAI,CAAC,IAAI,CAACW,SAAS,CAACT,UAAU,EAAE;cACxD5C,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAACsD,UAAU,CAACG,YAAY,CAACzG,IAAI,CAAC,CAAC;YACvC;YACA,IAAI,CAAC,IAAI,CAACqG,SAAS,CAACX,MAAM,EAAE;cAC1B1C,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAACsD,UAAU,CAACI,cAAc,CAAC1G,IAAI,CAAC,CAAC;cACvCgD,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAACsD,UAAU,CAACZ,MAAM,CAAC1F,IAAI,CAAC,CAAC;YACjC;UACF;UACA,IAAI,CAAC,IAAI,CAACkG,SAAS,CAACJ,MAAM,EAAE;YAC1B;YACA,IAAI,IAAI,CAACI,SAAS,CAACR,MAAM,IAAI,IAAI,CAACQ,SAAS,CAACN,UAAU,EAAE;cACtD5C,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAACsD,UAAU,CAACK,cAAc,CAAC3G,IAAI,CAAC,CAAC;cACvCgD,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAACsD,UAAU,CAACM,cAAc,CAAC5G,IAAI,CAAC,CAAC;cACvCgD,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAACsD,UAAU,CAACR,MAAM,CAAC9F,IAAI,CAAC,CAAC;YACjC;UACF;QACF;QACA,OAAOgD,gBAAgB;MACzB,CAAC;MAGD1N,MAAM,CAAC2G,OAAO,CAAC+J,kBAAkB,GAAGA,kBAAkB;;MAGtD;IAAM,CAAC,OAIP;IACA,KAAO,UAAS1Q,MAAM,EAAE6L,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAIkG,UAAU,GAAIlG,mBAAmB,CAAC,EAAE,CAAC,CAACkG,UAAW;QACnD7K,OAAO,GAAI2E,mBAAmB,CAAC,EAAE,CAAC,CAAC3E,OAAQ;MAE7C,SAAS8K,UAAUA,CAACC,WAAW,EAAE9O,OAAO,EAAE+O,WAAW,EAAEC,YAAY,EAAE;QACnE,IAAIC,UAAU,GAAG,IAAIL,UAAU,CAACE,WAAW,EAAE9O,OAAO,EAAE+O,WAAW,EAAEC,YAAY,CAAC;QAChF,OAAOC,UAAU,CAACC,QAAQ,CAAC,CAAC;MAC9B;MAEAtS,MAAM,CAAC2G,OAAO,GAAGsL,UAAU;MAC3BjS,MAAM,CAAC2G,OAAO,CAAC4L,cAAc,GAAG,YAAW;QACzC,OAAO,IAAIpL,OAAO,CAAC,CAAC;MACtB,CAAC;;MAGD;IAAM,CAAC,IACP;IACA,KAAO,UAASnH,MAAM,EAAE6L,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAI3E,OAAO,GAAI2E,mBAAmB,CAAC,EAAE,CAAC,CAAC3E,OAAQ;MAC/C,IAAI/C,MAAM,GAAI0H,mBAAmB,CAAC,CAAC,CAAC,CAAC1H,MAAO;MAC5C,IAAIiI,SAAS,GAAIP,mBAAmB,CAAC,EAAE,CAAC,CAACO,SAAU;MACnD,IAAIJ,KAAK,GAAIH,mBAAmB,CAAC,EAAE,CAAC,CAACG,KAAM;MAE3C,IAAIuG,SAAS,GAAG,aAAa;MAC7B,IAAIC,aAAa,GAAG,cAAc;MAElC,IAAIC,OAAO,GAAG,SAAAA,CAAStP,OAAO,EAAEuP,kBAAkB,EAAE;QAAE;;QAEpD,IAAI,CAAC/O,YAAY,GAAG,CAAC;QACrB,IAAI,CAACgP,cAAc,GAAG,CAAC;QACvB,IAAI,CAAChL,qBAAqB,GAAGxE,OAAO,CAACwE,qBAAqB;QAC1D,IAAI,CAACD,iBAAiB,GAAGvE,OAAO,CAACuE,iBAAiB;QAElD,IAAI,CAACkL,OAAO,GAAG,IAAIzO,MAAM,CAAChB,OAAO,EAAEuP,kBAAkB,CAAC;MAExD,CAAC;MAEDD,OAAO,CAAC9R,SAAS,CAACkS,sBAAsB,GAAG,UAAS1R,OAAO,EAAE;QAC3D,OAAO,IAAI,CAACyR,OAAO,CAAC3Q,YAAY,CAACf,SAAS,CAACC,OAAO,CAAC;MACrD,CAAC;MAEDsR,OAAO,CAAC9R,SAAS,CAACmS,sBAAsB,GAAG,UAASC,KAAK,EAAEC,YAAY,EAAE;QACvE,IAAI,CAACJ,OAAO,CAAClO,kBAAkB,GAAGqO,KAAK;QACvC,IAAI,CAACH,OAAO,CAACjO,kBAAkB,GAAGqO,YAAY;MAChD,CAAC;MAEDP,OAAO,CAAC9R,SAAS,CAAC2E,cAAc,GAAG,YAAW;QAC5C,IAAI,CAACsN,OAAO,CAAC9R,UAAU,CAAC,IAAI,CAAC6C,YAAY,EAAE,IAAI,CAACgP,cAAc,CAAC;QAC/D,IAAI,CAACC,OAAO,CAACtN,cAAc,CAAC,CAAC;MAC/B,CAAC;MAGDmN,OAAO,CAAC9R,SAAS,CAAC4E,aAAa,GAAG,UAASC,KAAK,EAAE;QAChD,IAAI,CAACoN,OAAO,CAACrN,aAAa,CAACC,KAAK,CAAC;MACnC,CAAC;MAEDiN,OAAO,CAAC9R,SAAS,CAACsS,wBAAwB,GAAG,UAASC,SAAS,EAAE;QAC/D,IAAIxN,QAAQ,GAAG,CAAC;QAChB,IAAIwN,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAACmH,IAAI,IAAID,SAAS,CAACpM,QAAQ,CAACF,IAAI,KAAKoF,KAAK,CAACmH,IAAI,EAAE;UAC3EzN,QAAQ,GAAGwN,SAAS,CAACxN,QAAQ,GAAG,CAAC,GAAG,CAAC;QACvC;QAEA,IAAI,IAAI,CAACgC,iBAAiB,EAAE;UAC1BhC,QAAQ,GAAGwN,SAAS,CAACxN,QAAQ,GAAG,IAAI,CAACiC,qBAAqB,GAAG,CAAC,GAAGuL,SAAS,CAACxN,QAAQ,GAAG,IAAI,CAACiC,qBAAqB,GAAG,CAAC;QACtH;QACA,KAAK,IAAIyL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1N,QAAQ,EAAE0N,CAAC,EAAE,EAAE;UACjC,IAAI,CAACC,aAAa,CAACD,CAAC,GAAG,CAAC,CAAC;QAC3B;QAEA,OAAO1N,QAAQ,KAAK,CAAC;MACvB,CAAC;MAED+M,OAAO,CAAC9R,SAAS,CAAC2S,mBAAmB,GAAG,UAASJ,SAAS,EAAE;QAC1D,IAAIA,SAAS,CAACvN,iBAAiB,IAAIuN,SAAS,CAACxN,QAAQ,EAAE;UACrD,IAAI,CAAC,IAAI,CAACuN,wBAAwB,CAACC,SAAS,CAAC,EAAE;YAC7C,IAAI,CAACN,OAAO,CAAClO,kBAAkB,GAAG,IAAI;UACxC;UACA,OAAO,IAAI;QACb;QACA,OAAO,KAAK;MACd,CAAC;MAED+N,OAAO,CAAC9R,SAAS,CAACiE,sBAAsB,GAAG,YAAW;QACpD,OAAO,IAAI,CAACgO,OAAO,CAAChO,sBAAsB;MAC5C,CAAC;MAED6N,OAAO,CAAC9R,SAAS,CAAC0S,aAAa,GAAG,UAASE,KAAK,EAAE;QAChD,IAAI,CAACX,OAAO,CAAC7Q,YAAY,CAACwR,KAAK,CAAC;MAClC,CAAC;MAEDd,OAAO,CAAC9R,SAAS,CAAC6S,WAAW,GAAG,UAAShO,KAAK,EAAE;QAC9C,IAAIA,KAAK,CAACI,IAAI,EAAE;UACd,IAAI,CAACgN,OAAO,CAAC9R,UAAU,CAAC,IAAI,CAAC6C,YAAY,EAAE,IAAI,CAACgP,cAAc,CAAC;UAC/D,IAAI,CAACC,OAAO,CAAC/M,SAAS,CAACL,KAAK,CAACI,IAAI,CAAC;QACpC;MACF,CAAC;MAED6M,OAAO,CAAC9R,SAAS,CAACW,MAAM,GAAG,YAAW;QACpC,IAAI,CAACqC,YAAY,EAAE;MACrB,CAAC;MAED8O,OAAO,CAAC9R,SAAS,CAAC8S,QAAQ,GAAG,YAAW;QACtC,IAAI,IAAI,CAAC9P,YAAY,GAAG,CAAC,EAAE;UACzB,IAAI,CAACA,YAAY,EAAE;UACnB,IAAI,CAACiP,OAAO,CAAC9R,UAAU,CAAC,IAAI,CAAC6C,YAAY,EAAE,IAAI,CAACgP,cAAc,CAAC;QACjE;MACF,CAAC;MAEDF,OAAO,CAAC9R,SAAS,CAAC+S,eAAe,GAAG,UAASC,KAAK,EAAE;QAClDA,KAAK,GAAG,IAAI,CAAChQ,YAAY,IAAIgQ,KAAK,IAAI,CAAC,CAAC;QACxC,IAAIA,KAAK,GAAG,CAAC,EAAE;UACb,OAAO,EAAE;QACX;QAEA,OAAO,IAAI,CAACf,OAAO,CAAC5P,iBAAiB,CAAC2Q,KAAK,CAAC;MAC9C,CAAC;MAED,IAAIC,kBAAkB,GAAG,SAAAA,CAASC,WAAW,EAAE;QAC7C,IAAI/Q,MAAM,GAAG,IAAI;QACjB,IAAIoQ,SAAS,GAAGW,WAAW,CAAC7R,IAAI;;QAEhC;QACA,OAAOkR,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAACG,GAAG,IAAI0H,WAAW,CAAC7M,MAAM,KAAKkM,SAAS,EAAE;UACvE,IAAIA,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAAC8H,SAAS,IAAIZ,SAAS,CAACtN,IAAI,KAAK,MAAM,EAAE;YACnE,IAAIsN,SAAS,CAAClR,IAAI,IAAIkR,SAAS,CAAClR,IAAI,CAAC4E,IAAI,KAAKoF,KAAK,CAAC+H,MAAM,IACxDb,SAAS,CAAClR,IAAI,CAACA,IAAI,IAAIkR,SAAS,CAAClR,IAAI,CAACA,IAAI,CAAC4E,IAAI,KAAKoF,KAAK,CAACgI,KAAK,EAAE;cACjElR,MAAM,GAAGoQ,SAAS,CAAClR,IAAI,CAACA,IAAI,CAAC4D,IAAI;YACnC;YACA;UACF;UACAsN,SAAS,GAAGA,SAAS,CAAClR,IAAI;QAC5B;QAEA,OAAOc,MAAM;MACf,CAAC;MAED,IAAImR,0BAA0B,GAAG,SAAAA,CAASC,SAAS,EAAEhB,SAAS,EAAE;QAC9D,IAAIiB,aAAa,GAAG,IAAI;QACxB,IAAIrR,MAAM,GAAG,IAAI;QAEjB,IAAI,CAACoQ,SAAS,CAAClM,MAAM,EAAE;UACrB,OAAO,IAAI;QACb;QAEA,IAAIkN,SAAS,KAAK,QAAQ,EAAE;UAC1BC,aAAa,GAAG,iBAAiB;QACnC,CAAC,MAAM,IAAID,SAAS,KAAK,OAAO,EAAE;UAChCC,aAAa,GAAG,UAAU;QAC5B;QAEAA,aAAa,GAAGP,kBAAkB,CAACV,SAAS,CAAC,IAAIiB,aAAa;;QAE9D;QACA;QACA,IAAIA,aAAa,CAACC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE;UACzCtR,MAAM,GAAG,KAAK;QAChB,CAAC,MAAM,IAAIqR,aAAa,CAACC,MAAM,CAAC,6GAA6G,CAAC,GAAG,CAAC,CAAC,EAAE;UACnJtR,MAAM,GAAG,YAAY;QACvB,CAAC,MAAM,IAAIqR,aAAa,CAACC,MAAM,CAAC,sCAAsC,CAAC,GAAG,CAAC,CAAC,EAAE;UAC5EtR,MAAM,GAAG,MAAM;QACjB,CAAC,MAAM,IAAIqR,aAAa,CAACC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE;UAClD;UACAtR,MAAM,GAAG,MAAM;QACjB;QAEA,OAAOA,MAAM;MACf,CAAC;MAED,SAASuR,QAAQA,CAACC,IAAI,EAAEC,GAAG,EAAE;QAC3B,OAAOA,GAAG,CAAC9N,OAAO,CAAC6N,IAAI,CAAC,KAAK,CAAC,CAAC;MACjC;MAEA,SAASE,QAAQA,CAACvU,MAAM,EAAEwU,YAAY,EAAE9Q,YAAY,EAAE;QACpD,IAAI,CAAC1D,MAAM,GAAGA,MAAM,IAAI,IAAI;QAC5B,IAAI,CAACyU,GAAG,GAAGD,YAAY,GAAGA,YAAY,CAACE,QAAQ,GAAG,EAAE;QACpD,IAAI,CAAChR,YAAY,GAAGA,YAAY,IAAI,CAAC;QACrC,IAAI,CAAC8Q,YAAY,GAAGA,YAAY,IAAI,IAAI;MAC1C;MAEA,SAASG,QAAQA,CAACC,OAAO,EAAE;QACzB,IAAI,CAACC,QAAQ,GAAGD,OAAO;QACvB,IAAI,CAACE,cAAc,GAAG,IAAI;MAC5B;MAEAH,QAAQ,CAACjU,SAAS,CAACqU,gBAAgB,GAAG,YAAW;QAC/C,OAAO,IAAI,CAACD,cAAc,GAAG,IAAI,CAACA,cAAc,CAACN,YAAY,GAAG,IAAI;MACtE,CAAC;MAEDG,QAAQ,CAACjU,SAAS,CAACsU,UAAU,GAAG,UAASR,YAAY,EAAE;QAAE;QACvD,IAAIS,SAAS,GAAG,IAAIV,QAAQ,CAAC,IAAI,CAACO,cAAc,EAAEN,YAAY,EAAE,IAAI,CAACK,QAAQ,CAACnR,YAAY,CAAC;QAC3F,IAAI,CAACoR,cAAc,GAAGG,SAAS;MACjC,CAAC;MAEDN,QAAQ,CAACjU,SAAS,CAACwU,cAAc,GAAG,UAASC,KAAK,EAAE;QAAE;QACpD,IAAIX,YAAY,GAAG,IAAI;QAEvB,IAAIW,KAAK,EAAE;UACTX,YAAY,GAAGW,KAAK,CAACX,YAAY;UACjC,IAAI,CAACK,QAAQ,CAACnR,YAAY,GAAGyR,KAAK,CAACzR,YAAY;UAC/C,IAAI,CAACoR,cAAc,GAAGK,KAAK,CAACnV,MAAM;QACpC;QAEA,OAAOwU,YAAY;MACrB,CAAC;MAEDG,QAAQ,CAACjU,SAAS,CAAC0U,UAAU,GAAG,UAASC,QAAQ,EAAEC,SAAS,EAAE;QAAE;QAC9D,IAAIH,KAAK,GAAG,IAAI,CAACL,cAAc;QAE/B,OAAOK,KAAK,EAAE;UAAE;UACd,IAAIE,QAAQ,CAAC7O,OAAO,CAAC2O,KAAK,CAACV,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YAAE;YACxC;UACF,CAAC,MAAM,IAAIa,SAAS,IAAIA,SAAS,CAAC9O,OAAO,CAAC2O,KAAK,CAACV,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YAC3DU,KAAK,GAAG,IAAI;YACZ;UACF;UACAA,KAAK,GAAGA,KAAK,CAACnV,MAAM;QACtB;QAEA,OAAOmV,KAAK;MACd,CAAC;MAEDR,QAAQ,CAACjU,SAAS,CAAC6U,OAAO,GAAG,UAASd,GAAG,EAAEa,SAAS,EAAE;QAAE;QACtD,IAAIH,KAAK,GAAG,IAAI,CAACC,UAAU,CAAC,CAACX,GAAG,CAAC,EAAEa,SAAS,CAAC;QAC7C,OAAO,IAAI,CAACJ,cAAc,CAACC,KAAK,CAAC;MACnC,CAAC;MAEDR,QAAQ,CAACjU,SAAS,CAAC8U,aAAa,GAAG,UAASH,QAAQ,EAAE;QACpD,IAAIF,KAAK,GAAG,IAAI,CAACC,UAAU,CAACC,QAAQ,CAAC;QACrC,IAAIF,KAAK,EAAE;UACT,IAAI,CAACN,QAAQ,CAACnR,YAAY,GAAGyR,KAAK,CAACzR,YAAY;QACjD;MACF,CAAC;MAED,SAASoO,UAAUA,CAAC2D,WAAW,EAAEvS,OAAO,EAAE+O,WAAW,EAAEC,YAAY,EAAE;QACnE;QACA,IAAI,CAACwD,YAAY,GAAGD,WAAW,IAAI,EAAE;QACrCvS,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;QACvB,IAAI,CAACyS,YAAY,GAAG1D,WAAW;QAC/B,IAAI,CAAC2D,aAAa,GAAG1D,YAAY;QACjC,IAAI,CAAC2D,UAAU,GAAG,IAAI;;QAEtB;QACA;QACA,IAAIC,UAAU,GAAG,IAAI7O,OAAO,CAAC/D,OAAO,EAAE,MAAM,CAAC;QAE7C,IAAI,CAACmJ,QAAQ,GAAGyJ,UAAU;QAE1B,IAAI,CAACC,yBAAyB,GAAG,IAAI,CAAC1J,QAAQ,CAAC2J,eAAe,CAACC,MAAM,CAAC,CAAC,EAAE,OAAO,CAACjV,MAAM,CAAC,KAAK,OAAO;QACpG,IAAI,CAACkV,0CAA0C,GAAI,IAAI,CAAC7J,QAAQ,CAAC2J,eAAe,KAAK,wBAAyB;QAC9G,IAAI,CAACG,iCAAiC,GAAI,IAAI,CAAC9J,QAAQ,CAAC2J,eAAe,KAAK,eAAgB;QAC5F,IAAI,CAACI,oCAAoC,GAAI,IAAI,CAAC/J,QAAQ,CAAC2J,eAAe,KAAK,kBAAmB;QAClG,IAAI,CAACK,4BAA4B,GAAG,IAAI,CAAChK,QAAQ,CAAC2J,eAAe,CAACC,MAAM,CAAC,CAAC,EAAE,UAAU,CAACjV,MAAM,CAAC,KAAK,UAAU;QAC7G,IAAI,CAACsV,oCAAoC,GAAI,IAAI,CAACjK,QAAQ,CAAC2J,eAAe,KAAK,kBAAmB;MACpG;MAEAlE,UAAU,CAACpR,SAAS,CAAC0R,QAAQ,GAAG,YAAW;QAEzC;QACA,IAAI,IAAI,CAAC/F,QAAQ,CAAChF,QAAQ,EAAE;UAC1B,OAAO,IAAI,CAACqO,YAAY;QAC1B;QAEA,IAAID,WAAW,GAAG,IAAI,CAACC,YAAY;QACnC,IAAIzQ,GAAG,GAAG,IAAI,CAACoH,QAAQ,CAACpH,GAAG;QAC3B,IAAI,IAAI,CAACoH,QAAQ,CAACpH,GAAG,KAAK,MAAM,EAAE;UAChCA,GAAG,GAAG,IAAI;UACV,IAAIwQ,WAAW,IAAInD,SAAS,CAAChI,IAAI,CAACmL,WAAW,CAAC,EAAE;YAC9CxQ,GAAG,GAAGwQ,WAAW,CAACrU,KAAK,CAACkR,SAAS,CAAC,CAAC,CAAC,CAAC;UACvC;QACF;;QAEA;QACAmD,WAAW,GAAGA,WAAW,CAACtQ,OAAO,CAACoN,aAAa,EAAE,IAAI,CAAC;QAEtD,IAAIpP,gBAAgB,GAAGsS,WAAW,CAACrU,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAEtD,IAAImV,UAAU,GAAG;UACf5Q,IAAI,EAAE,EAAE;UACRgB,IAAI,EAAE;QACR,CAAC;QAED,IAAI6P,cAAc,GAAG,IAAIC,kBAAkB,CAAC,IAAI,CAACpK,QAAQ,CAAC;QAE1D,IAAIuI,OAAO,GAAG,IAAIpC,OAAO,CAAC,IAAI,CAACnG,QAAQ,EAAElJ,gBAAgB,CAAC;QAC1D,IAAIuT,MAAM,GAAG,IAAIvK,SAAS,CAACsJ,WAAW,EAAE,IAAI,CAACpJ,QAAQ,CAAC,CAACI,QAAQ,CAAC,CAAC;QAEjE,IAAI,CAACoJ,UAAU,GAAG,IAAIlB,QAAQ,CAACC,OAAO,CAAC;QAEvC,IAAIJ,YAAY,GAAG,IAAI;QACvB,IAAIvB,SAAS,GAAGyD,MAAM,CAAC3U,IAAI,CAAC,CAAC;QAC7B,OAAOkR,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAACG,GAAG,EAAE;UAEnC,IAAI+G,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAAC4K,QAAQ,IAAI1D,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAAC6K,OAAO,EAAE;YACzEpC,YAAY,GAAG,IAAI,CAACqC,gBAAgB,CAACjC,OAAO,EAAE3B,SAAS,EAAEuD,cAAc,EAAED,UAAU,EAAEG,MAAM,CAAC;YAC5FF,cAAc,GAAGhC,YAAY;UAC/B,CAAC,MAAM,IAAKvB,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAAC8H,SAAS,IAAIZ,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAAC+H,MAAM,IAAIb,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAACgI,KAAK,IAChHd,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAACmH,IAAI,IAAI,CAACsD,cAAc,CAACM,YAAa,EAAE;YACjEtC,YAAY,GAAG,IAAI,CAACuC,kBAAkB,CAACnC,OAAO,EAAE3B,SAAS,EAAEuD,cAAc,EAAED,UAAU,CAAC;UACxF,CAAC,MAAM,IAAItD,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAACiL,SAAS,EAAE;YAC7CxC,YAAY,GAAG,IAAI,CAACyC,iBAAiB,CAACrC,OAAO,EAAE3B,SAAS,EAAEuD,cAAc,CAAC;UAC3E,CAAC,MAAM,IAAIvD,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAACmH,IAAI,EAAE;YACxCsB,YAAY,GAAG,IAAI,CAAC0C,YAAY,CAACtC,OAAO,EAAE3B,SAAS,EAAEuD,cAAc,CAAC;UACtE,CAAC,MAAM,IAAIvD,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAACoL,iBAAiB,EAAE;YACrD3C,YAAY,GAAG,IAAI,CAAC4C,yBAAyB,CAACxC,OAAO,EAAE3B,SAAS,CAAC;UACnE,CAAC,MAAM,IAAIA,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAACsL,kBAAkB,EAAE;YACtD7C,YAAY,GAAG,IAAI,CAAC8C,0BAA0B,CAAC1C,OAAO,EAAE3B,SAAS,CAAC;UACpE,CAAC,MAAM;YACL;YACA2B,OAAO,CAACtP,aAAa,CAAC2N,SAAS,CAAC;UAClC;UAEAsD,UAAU,GAAG/B,YAAY;UAEzBvB,SAAS,GAAGyD,MAAM,CAAC3U,IAAI,CAAC,CAAC;QAC3B;QACA,IAAIqD,UAAU,GAAGwP,OAAO,CAACjC,OAAO,CAAC3N,QAAQ,CAACC,GAAG,CAAC;QAE9C,OAAOG,UAAU;MACnB,CAAC;MAED0M,UAAU,CAACpR,SAAS,CAAC0W,yBAAyB,GAAG,UAASxC,OAAO,EAAE3B,SAAS,EAAE;QAC5E,IAAIuB,YAAY,GAAG;UACjB7O,IAAI,EAAEsN,SAAS,CAACtN,IAAI;UACpBgB,IAAI,EAAEsM,SAAS,CAACtM;QAClB,CAAC;QACDiO,OAAO,CAAC/B,sBAAsB,CAACI,SAAS,CAACxN,QAAQ,IAAIwN,SAAS,CAACvN,iBAAiB,KAAK,EAAE,EAAE,IAAI,CAAC;QAC9F,IAAIuN,SAAS,CAACxN,QAAQ,EAAE;UACtBmP,OAAO,CAAC5B,wBAAwB,CAACC,SAAS,CAAC;QAC7C,CAAC,MAAM;UACL2B,OAAO,CAAC/B,sBAAsB,CAACI,SAAS,CAACxN,QAAQ,IAAIwN,SAAS,CAACvN,iBAAiB,KAAK,EAAE,EAAE,IAAI,CAAC;QAChG;QACAkP,OAAO,CAACrB,WAAW,CAACN,SAAS,CAAC;QAC9B2B,OAAO,CAACvT,MAAM,CAAC,CAAC;QAChB,OAAOmT,YAAY;MACrB,CAAC;MAED1C,UAAU,CAACpR,SAAS,CAAC4W,0BAA0B,GAAG,UAAS1C,OAAO,EAAE3B,SAAS,EAAE;QAC7E,IAAIuB,YAAY,GAAG;UACjB7O,IAAI,EAAEsN,SAAS,CAACtN,IAAI;UACpBgB,IAAI,EAAEsM,SAAS,CAACtM;QAClB,CAAC;QAEDiO,OAAO,CAACpB,QAAQ,CAAC,CAAC;QAClB,IAAIP,SAAS,CAACxN,QAAQ,EAAE;UACtBmP,OAAO,CAAC5B,wBAAwB,CAACC,SAAS,CAAC;QAC7C,CAAC,MAAM;UACL2B,OAAO,CAAC/B,sBAAsB,CAACI,SAAS,CAACxN,QAAQ,IAAIwN,SAAS,CAACvN,iBAAiB,KAAK,EAAE,EAAE,IAAI,CAAC;QAChG;QACAkP,OAAO,CAACrB,WAAW,CAACN,SAAS,CAAC;QAC9B,OAAOuB,YAAY;MACrB,CAAC;MAED1C,UAAU,CAACpR,SAAS,CAACuW,iBAAiB,GAAG,UAASrC,OAAO,EAAE3B,SAAS,EAAEuD,cAAc,EAAE;QACpF,IAAIhC,YAAY,GAAG;UACjB7O,IAAI,EAAEsN,SAAS,CAACtN,IAAI;UACpBgB,IAAI,EAAEsM,SAAS,CAACtM;QAClB,CAAC;QACDiO,OAAO,CAAClC,cAAc,GAAG,CAAC;QAC1B8D,cAAc,CAACM,YAAY,GAAG,IAAI;QAElClC,OAAO,CAAC/B,sBAAsB,CAACI,SAAS,CAACxN,QAAQ,IAAIwN,SAAS,CAACvN,iBAAiB,KAAK,EAAE,EAAE,IAAI,CAAC;QAC9F,IAAI8Q,cAAc,CAACe,cAAc,EAAE;UACjC3C,OAAO,CAACtP,aAAa,CAAC2N,SAAS,CAAC;QAClC,CAAC,MAAM;UACL,IAAIuD,cAAc,CAACgB,cAAc,KAAK,GAAG,EAAE;YACzC5C,OAAO,CAAC/B,sBAAsB,CAACI,SAAS,CAACtN,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;YACjE,IAAI,IAAI,CAACuQ,0CAA0C,IAAIM,cAAc,CAACiB,iBAAiB,EAAE;cACvF7C,OAAO,CAACxB,aAAa,CAAC,KAAK,CAAC;YAC9B;UACF;UACAwB,OAAO,CAACrB,WAAW,CAACN,SAAS,CAAC;QAEhC;QAEA,IAAIuD,cAAc,CAACkB,cAAc,IAC/B,EAAElB,cAAc,CAACe,cAAc,IAAIf,cAAc,CAACmB,sBAAsB,CAAC,EAAE;UAC3E/C,OAAO,CAACvT,MAAM,CAAC,CAAC;;UAEhB;UACAmV,cAAc,CAACkB,cAAc,GAAG,KAAK;QACvC;QAEA,IAAI,CAAClB,cAAc,CAACoB,iBAAiB,IACnC,EAAEpB,cAAc,CAACe,cAAc,IAAIf,cAAc,CAACmB,sBAAsB,CAAC,EAAE;UAC3E/C,OAAO,CAACvP,cAAc,CAAC,CAAC;QAC1B;QAEA,OAAOmP,YAAY;MACrB,CAAC;MAED1C,UAAU,CAACpR,SAAS,CAACqW,kBAAkB,GAAG,UAASnC,OAAO,EAAE3B,SAAS,EAAEuD,cAAc,EAAED,UAAU,EAAE;QACjG,IAAIsB,OAAO,GAAGrB,cAAc,CAACiB,iBAAiB;QAC9C,IAAIjD,YAAY,GAAG;UACjB7O,IAAI,EAAEsN,SAAS,CAACtN,IAAI;UACpBgB,IAAI,EAAEsM,SAAS,CAACtM;QAClB,CAAC;QAEDiO,OAAO,CAAC/B,sBAAsB,CAACI,SAAS,CAACxN,QAAQ,IAAIwN,SAAS,CAACvN,iBAAiB,KAAK,EAAE,EAAE,IAAI,CAAC;QAC9F,IAAI8Q,cAAc,CAACe,cAAc,EAAE;UACjC3C,OAAO,CAACtP,aAAa,CAAC2N,SAAS,CAAC;QAClC,CAAC,MAAM,IAAIuD,cAAc,CAACgB,cAAc,KAAK,GAAG,IAAIvE,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAACmH,IAAI,EAAE;UACjF;UACA,IAAI0B,OAAO,CAAC5B,wBAAwB,CAACC,SAAS,CAAC,EAAE;YAC/CA,SAAS,CAACxN,QAAQ,GAAG,CAAC;YACtBmP,OAAO,CAACtP,aAAa,CAAC2N,SAAS,CAAC;UAClC,CAAC,MAAM;YACL2B,OAAO,CAACrB,WAAW,CAACN,SAAS,CAAC;UAChC;QACF,CAAC,MAAM;UACL,IAAIA,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAAC8H,SAAS,EAAE;YACtCe,OAAO,CAAC/B,sBAAsB,CAAC,IAAI,CAAC;UACtC,CAAC,MAAM,IAAII,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAAC+H,MAAM,EAAE;YAAE;YAC5Cc,OAAO,CAAC/B,sBAAsB,CAAC,KAAK,CAAC;UACvC,CAAC,MAAM,IAAII,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAACgI,KAAK,IAAId,SAAS,CAACpM,QAAQ,CAACF,IAAI,KAAKoF,KAAK,CAAC+H,MAAM,EAAE;YAAE;YACvFc,OAAO,CAAC/B,sBAAsB,CAAC,KAAK,CAAC;UACvC;UAEA,IAAII,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAAC8H,SAAS,IAAI2C,cAAc,CAACgB,cAAc,KAAK,GAAG,EAAE;YAC/E,IAAI,IAAI,CAACnB,4BAA4B,IAAI,IAAI,CAACC,oCAAoC,EAAE;cAClF1B,OAAO,CAACvB,mBAAmB,CAACJ,SAAS,CAAC;cACtC4E,OAAO,GAAGA,OAAO,IAAI5E,SAAS,CAACxN,QAAQ,KAAK,CAAC;YAC/C;;YAEA;YACA;YACA;YACA,IAAI,IAAI,CAACsQ,yBAAyB,IAChCS,cAAc,CAACsB,UAAU,IAAI,IAAI,CAACzL,QAAQ,CAAC0L,yBAAyB,KACnExB,UAAU,CAAC5P,IAAI,KAAKoF,KAAK,CAAC4K,QAAQ;YAAI;YACrC,IAAI,CAACT,0CAA0C,CAAC,EAAE;cACpDtB,OAAO,CAACxB,aAAa,CAAC,KAAK,CAAC;cAC5ByE,OAAO,GAAG,IAAI;YAChB;UACF;UACAjD,OAAO,CAACrB,WAAW,CAACN,SAAS,CAAC;UAC9B4E,OAAO,GAAGA,OAAO,IAAIjD,OAAO,CAACjQ,sBAAsB,CAAC,CAAC;UACrD6R,cAAc,CAACiB,iBAAiB,GAAGI,OAAO;QAC5C;QACA,OAAOrD,YAAY;MACrB,CAAC;MAED1C,UAAU,CAACpR,SAAS,CAACwW,YAAY,GAAG,UAAStC,OAAO,EAAE3B,SAAS,EAAEuD,cAAc,EAAE;QAC/E,IAAIhC,YAAY,GAAG;UACjB7O,IAAI,EAAEsN,SAAS,CAACtN,IAAI;UACpBgB,IAAI,EAAE;QACR,CAAC;QACD,IAAI6P,cAAc,CAACwB,sBAAsB,EAAE;UAAE;UAC3C,IAAI,CAACC,4BAA4B,CAACrD,OAAO,EAAE3B,SAAS,EAAEuD,cAAc,CAAC;QACvE,CAAC,MAAM,IAAIA,cAAc,CAACe,cAAc,IAAIf,cAAc,CAACmB,sBAAsB,EAAE;UACjF/C,OAAO,CAACtP,aAAa,CAAC2N,SAAS,CAAC;QAClC,CAAC,MAAM;UACL2B,OAAO,CAACvB,mBAAmB,CAACJ,SAAS,CAAC;UACtC2B,OAAO,CAACrB,WAAW,CAACN,SAAS,CAAC;QAChC;QACA,OAAOuB,YAAY;MACrB,CAAC;MAED1C,UAAU,CAACpR,SAAS,CAACuX,4BAA4B,GAAG,UAASrD,OAAO,EAAE3B,SAAS,EAAEuD,cAAc,EAAE;QAC/F,IAAI0B,KAAK,GAAG,IAAI;QAChB,IAAIjF,SAAS,CAACtN,IAAI,KAAK,EAAE,EAAE;UAEzB,IAAIA,IAAI,GAAGsN,SAAS,CAACtN,IAAI;YACvBwS,WAAW;YACXC,mBAAmB,GAAG,CAAC;YACvBC,GAAG,GAAG,EAAE;YACRC,IAAI,GAAG,EAAE;UACX,IAAI9B,cAAc,CAACwB,sBAAsB,KAAK,YAAY,IAAI,OAAO,IAAI,CAACrC,YAAY,KAAK,UAAU,EAAE;YACrGwC,WAAW,GAAG,IAAI,CAACxC,YAAY;UACjC,CAAC,MAAM,IAAIa,cAAc,CAACwB,sBAAsB,KAAK,KAAK,IAAI,OAAO,IAAI,CAACpC,aAAa,KAAK,UAAU,EAAE;YACtGuC,WAAW,GAAG,IAAI,CAACvC,aAAa;UAClC,CAAC,MAAM,IAAIY,cAAc,CAACwB,sBAAsB,KAAK,MAAM,EAAE;YAC3DG,WAAW,GAAG,SAAAA,CAASnG,WAAW,EAAE9O,OAAO,EAAE;cAC3C,IAAIiP,UAAU,GAAG,IAAIL,UAAU,CAACE,WAAW,EAAE9O,OAAO,EAAEgV,KAAK,CAACvC,YAAY,EAAEuC,KAAK,CAACtC,aAAa,CAAC;cAC9F,OAAOzD,UAAU,CAACC,QAAQ,CAAC,CAAC;YAC9B,CAAC;UACH;UAEA,IAAI,IAAI,CAAC/F,QAAQ,CAACkM,cAAc,KAAK,MAAM,EAAE;YAC3CH,mBAAmB,GAAG,CAAC;UACzB,CAAC,MAAM,IAAI,IAAI,CAAC/L,QAAQ,CAACkM,cAAc,KAAK,UAAU,EAAE;YACtDH,mBAAmB,GAAG,CAACxD,OAAO,CAAClR,YAAY;UAC7C;UAEA,IAAI8U,WAAW,GAAG5D,OAAO,CAACnB,eAAe,CAAC2E,mBAAmB,CAAC;;UAE9D;UACA;UACAzS,IAAI,GAAGA,IAAI,CAACR,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;;UAEpC;UACA,IAAIqR,cAAc,CAACwB,sBAAsB,KAAK,MAAM,IAClDrS,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,IAAI,CAACvE,KAAK,CAAC,qBAAqB,CAAC,EAAE;YACtD,IAAIqX,OAAO,GAAG,6DAA6D,CAACrO,IAAI,CAACzE,IAAI,CAAC;;YAEtF;YACA,IAAI,CAAC8S,OAAO,EAAE;cACZ7D,OAAO,CAACtP,aAAa,CAAC2N,SAAS,CAAC;cAChC;YACF;YAEAoF,GAAG,GAAGG,WAAW,GAAGC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;YACrC9S,IAAI,GAAG8S,OAAO,CAAC,CAAC,CAAC;YACjB,IAAIA,OAAO,CAAC,CAAC,CAAC,EAAE;cACdH,IAAI,GAAGE,WAAW,GAAGC,OAAO,CAAC,CAAC,CAAC;YACjC;;YAEA;YACA;YACA9S,IAAI,GAAGA,IAAI,CAACR,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;YAEpC,IAAIsT,OAAO,CAAC,CAAC,CAAC,IAAIA,OAAO,CAAC,CAAC,CAAC,CAACjS,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;cACjD;cACA;cACAiS,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,CAACrX,KAAK,CAAC,SAAS,CAAC;cACrC,IAAIqX,OAAO,EAAE;gBACXxF,SAAS,CAACvN,iBAAiB,GAAG+S,OAAO,CAAC,CAAC,CAAC;cAC1C;YACF;UACF;UAEA,IAAI9S,IAAI,EAAE;YACR,IAAIwS,WAAW,EAAE;cAEf;cACA,IAAIO,aAAa,GAAG,SAAAA,CAAA,EAAW;gBAC7B,IAAI,CAACzT,GAAG,GAAG,IAAI;cACjB,CAAC;cACDyT,aAAa,CAAChY,SAAS,GAAG,IAAI,CAAC2L,QAAQ,CAAClF,WAAW;cACnD,IAAIwR,aAAa,GAAG,IAAID,aAAa,CAAC,CAAC;cACvC/S,IAAI,GAAGwS,WAAW,CAACK,WAAW,GAAG7S,IAAI,EAAEgT,aAAa,CAAC;YACvD,CAAC,MAAM;cACL;cACA,IAAIC,KAAK,GAAG3F,SAAS,CAACvN,iBAAiB;cACvC,IAAIkT,KAAK,EAAE;gBACTjT,IAAI,GAAGA,IAAI,CAACR,OAAO,CAAC,IAAIiE,MAAM,CAAC,KAAK,GAAGwP,KAAK,GAAG,IAAI,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC;cAClE;cAEAjT,IAAI,GAAG6S,WAAW,GAAG7S,IAAI,CAACR,OAAO,CAAC,KAAK,EAAE,IAAI,GAAGqT,WAAW,CAAC;YAC9D;UACF;UAEA,IAAIH,GAAG,EAAE;YACP,IAAI,CAAC1S,IAAI,EAAE;cACTA,IAAI,GAAG0S,GAAG,GAAGC,IAAI;YACnB,CAAC,MAAM;cACL3S,IAAI,GAAG0S,GAAG,GAAG1S,IAAI,GAAG,IAAI,GAAG2S,IAAI;YACjC;UACF;UAEA1D,OAAO,CAACxB,aAAa,CAAC,KAAK,CAAC;UAC5B,IAAIzN,IAAI,EAAE;YACRsN,SAAS,CAACtN,IAAI,GAAGA,IAAI;YACrBsN,SAAS,CAACvN,iBAAiB,GAAG,EAAE;YAChCuN,SAAS,CAACxN,QAAQ,GAAG,CAAC;YACtBmP,OAAO,CAACtP,aAAa,CAAC2N,SAAS,CAAC;YAChC2B,OAAO,CAACxB,aAAa,CAAC,IAAI,CAAC;UAC7B;QACF;MACF,CAAC;MAEDtB,UAAU,CAACpR,SAAS,CAACmW,gBAAgB,GAAG,UAASjC,OAAO,EAAE3B,SAAS,EAAEuD,cAAc,EAAED,UAAU,EAAEG,MAAM,EAAE;QACvG,IAAIlC,YAAY,GAAG,IAAI,CAACqE,mBAAmB,CAAC5F,SAAS,CAAC;QAEtD,IAAI,CAACuD,cAAc,CAACe,cAAc,IAAIf,cAAc,CAACmB,sBAAsB,KACzE,CAACnB,cAAc,CAACsC,gBAAgB,IAChC7F,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAAC4K,QAAQ,IAAI,CAACnC,YAAY,CAACuE,YAAY,EAAE;UACjE;UACA;UACAnE,OAAO,CAACtP,aAAa,CAAC2N,SAAS,CAAC;UAChCuB,YAAY,CAACwE,eAAe,GAAG,IAAI,CAACnD,UAAU,CAACN,OAAO,CAACf,YAAY,CAACE,QAAQ,CAAC;QAC/E,CAAC,MAAM;UACLE,OAAO,CAACvB,mBAAmB,CAACJ,SAAS,CAAC;UACtC,IAAI,CAACgG,iBAAiB,CAACrE,OAAO,EAAE3B,SAAS,EAAEuB,YAAY,EAAEgC,cAAc,EAAED,UAAU,CAAC;UACpF,IAAI,CAAC/B,YAAY,CAACoD,iBAAiB,EAAE;YACnChD,OAAO,CAACvP,cAAc,CAAC,CAAC;UAC1B;UACAuP,OAAO,CAACrB,WAAW,CAACN,SAAS,CAAC;QAChC;;QAEA;QACA,IAAIuB,YAAY,CAACuE,YAAY,IAAI,IAAI,CAAChD,yBAAyB,EAAE;UAC/D,IAAImD,UAAU,GAAG,CAAC;UAClB,IAAIC,UAAU;UACd,GAAG;YACDA,UAAU,GAAGzC,MAAM,CAAC1M,IAAI,CAACkP,UAAU,CAAC;YACpC,IAAIC,UAAU,CAACxS,IAAI,KAAKoF,KAAK,CAAC8H,SAAS,EAAE;cACvCW,YAAY,CAACsD,UAAU,IAAI,CAAC;YAC9B;YACAoB,UAAU,IAAI,CAAC;UACjB,CAAC,QAAQC,UAAU,CAACxS,IAAI,KAAKoF,KAAK,CAACG,GAAG,IAAIiN,UAAU,CAACxS,IAAI,KAAKoF,KAAK,CAACiL,SAAS;QAC/E;;QAEA;QACA,IAAI,IAAI,CAACb,iCAAiC,IAAI,IAAI,CAACC,oCAAoC,IAAI,IAAI,CAACE,oCAAoC,EAAE;UACpI9B,YAAY,CAAC9B,cAAc,GAAGO,SAAS,CAACtN,IAAI,CAAC3E,MAAM,GAAG,CAAC;QACzD;QAEA,IAAI,CAACwT,YAAY,CAACsC,YAAY,IAAI,CAACtC,YAAY,CAAC+C,cAAc,EAAE;UAC9D3C,OAAO,CAAClC,cAAc,GAAG8B,YAAY,CAAC9B,cAAc;QACtD;QAEA,OAAO8B,YAAY;MACrB,CAAC;MAED,IAAIiC,kBAAkB,GAAG,SAAAA,CAASvT,OAAO,EAAElD,MAAM,EAAEiT,SAAS,EAAE;QAC5D,IAAI,CAACjT,MAAM,GAAGA,MAAM,IAAI,IAAI;QAC5B,IAAI,CAAC2F,IAAI,GAAG,EAAE;QACd,IAAI,CAACgB,IAAI,GAAG,aAAa;QACzB,IAAI,CAAC+N,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACkD,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACL,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACI,sBAAsB,GAAG,KAAK;QACnC,IAAI,CAACmB,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACC,YAAY,GAAG,KAAK;QACzB,IAAI,CAACK,UAAU,GAAG,KAAK;QACvB,IAAI,CAAC1B,cAAc,GAAG,KAAK;QAC3B,IAAI,CAAC2B,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACrB,sBAAsB,GAAG,IAAI;QAClC,IAAI,CAACgB,eAAe,GAAG,IAAI;QAC3B,IAAI,CAAClB,UAAU,GAAG,CAAC;QACnB,IAAI,CAACL,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAAC/E,cAAc,GAAG,CAAC;QACvB,IAAI,CAACoE,YAAY,GAAG,KAAK;QACzB,IAAI,CAACU,cAAc,GAAG,EAAE;QACxB,IAAI,CAACvD,SAAS,GAAG,EAAE;QAEnB,IAAI,CAAChB,SAAS,EAAE;UACd,IAAI,CAAC6D,YAAY,GAAG,IAAI;QAC1B,CAAC,MAAM;UACL,IAAIwC,eAAe;UAEnB,IAAI,CAAC9B,cAAc,GAAGvE,SAAS,CAACtN,IAAI,CAAC,CAAC,CAAC;UACvC,IAAI,CAACA,IAAI,GAAGsN,SAAS,CAACtN,IAAI;UAE1B,IAAI,IAAI,CAAC6R,cAAc,KAAK,GAAG,EAAE;YAC/B8B,eAAe,GAAGrG,SAAS,CAACtN,IAAI,CAACvE,KAAK,CAAC,aAAa,CAAC;YACrD,IAAI,CAAC6S,SAAS,GAAGqF,eAAe,GAAGA,eAAe,CAAC,CAAC,CAAC,GAAG,EAAE;UAC5D,CAAC,MAAM;YACLA,eAAe,GAAGrG,SAAS,CAACtN,IAAI,CAACvE,KAAK,CAAC,8BAA8B,CAAC;YACtE,IAAI,CAAC6S,SAAS,GAAGqF,eAAe,GAAGA,eAAe,CAAC,CAAC,CAAC,GAAG,EAAE;;YAE1D;YACA,IAAI,CAACrG,SAAS,CAACtN,IAAI,CAAC4T,UAAU,CAAC,MAAM,CAAC,IAAItG,SAAS,CAACtN,IAAI,CAAC4T,UAAU,CAAC,OAAO,CAAC,KAAK,IAAI,CAACtF,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;cAC1G,IAAI,IAAI,CAACA,SAAS,KAAK,GAAG,IAAIhB,SAAS,CAAClR,IAAI,KAAK,IAAI,EAAE;gBACrD,IAAI,CAACkS,SAAS,GAAGhB,SAAS,CAAClR,IAAI,CAAC4D,IAAI,CAACuC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;cACpD,CAAC,MAAM;gBACL,IAAI,CAAC+L,SAAS,GAAGhB,SAAS,CAACtN,IAAI,CAACuC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;cAC/C;YACF;UACF;UAEA,IAAI,CAAC+L,SAAS,GAAG,IAAI,CAACA,SAAS,CAACvI,WAAW,CAAC,CAAC;UAE7C,IAAIuH,SAAS,CAACtM,IAAI,KAAKoF,KAAK,CAAC6K,OAAO,EAAE;YACpC,IAAI,CAACE,YAAY,GAAG,IAAI;UAC1B;UAEA,IAAI,CAACiC,YAAY,GAAG,IAAI,CAAC9E,SAAS,CAAClK,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;UACpD,IAAI,CAAC2K,QAAQ,GAAG,CAAC,IAAI,CAACqE,YAAY,GAAG,IAAI,CAAC9E,SAAS,CAACgC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAChC,SAAS;UAC9E,IAAI,CAACmF,UAAU,GAAG,CAAC,IAAI,CAACL,YAAY,IACjC9F,SAAS,CAAClM,MAAM,IAAIkM,SAAS,CAAClM,MAAM,CAACpB,IAAI,KAAK,IAAK;;UAEtD;UACA,IAAI6T,gBAAgB,GAAG,CAAC;UACxB,IAAI,IAAI,CAAChC,cAAc,KAAK,GAAG,IAAI,IAAI,CAAC7R,IAAI,CAAC3E,MAAM,IAAI,CAAC,EAAE;YACxD,IAAI,IAAI,CAAC2E,IAAI,CAACoE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;cAC/ByP,gBAAgB,GAAG,CAAC;YACtB;UACF;;UAEA;UACA;UACA,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACA,UAAU,IAC9B,IAAI,CAAC5B,cAAc,KAAK,GAAG,KAAK,CAACtU,OAAO,CAACuW,iBAAiB,IAAI,IAAI,CAAC9T,IAAI,CAAC3E,MAAM,GAAG,CAAC,IAAK,QAAQ,CAACsJ,IAAI,CAAC,IAAI,CAAC3E,IAAI,CAACoE,MAAM,CAACyP,gBAAgB,CAAC,CAAE,CAAE;QAChJ;MACF,CAAC;MAED1H,UAAU,CAACpR,SAAS,CAACmY,mBAAmB,GAAG,UAAS5F,SAAS,EAAE;QAAE;QAC/D,IAAIuB,YAAY,GAAG,IAAIiC,kBAAkB,CAAC,IAAI,CAACpK,QAAQ,EAAE,IAAI,CAACwJ,UAAU,CAACd,gBAAgB,CAAC,CAAC,EAAE9B,SAAS,CAAC;QAEvGuB,YAAY,CAAC9B,cAAc,GAAG,IAAI,CAACrG,QAAQ,CAACqN,2BAA2B;QAEvElF,YAAY,CAAC4E,UAAU,GAAG5E,YAAY,CAAC4E,UAAU,IAC/ChF,QAAQ,CAACI,YAAY,CAACP,SAAS,EAAE,IAAI,CAAC5H,QAAQ,CAACsN,aAAa,CAAC;QAE/DnF,YAAY,CAACsE,gBAAgB,GAAGtE,YAAY,CAACsC,YAAY,IACtDtC,YAAY,CAACuE,YAAY,IAAIvE,YAAY,CAAC4E,UAAW;QAExD5E,YAAY,CAAC+C,cAAc,GAAG,CAAC/C,YAAY,CAACsC,YAAY,IAAI1C,QAAQ,CAACI,YAAY,CAACP,SAAS,EAAE,IAAI,CAAC5H,QAAQ,CAACuN,WAAW,CAAC;QACvHpF,YAAY,CAACmD,sBAAsB,GAAG,CAACnD,YAAY,CAACsE,gBAAgB,IAAI1E,QAAQ,CAACI,YAAY,CAACP,SAAS,EAAE,IAAI,CAAC5H,QAAQ,CAACwN,mBAAmB,CAAC;QAC3IrF,YAAY,CAACoD,iBAAiB,GAAGxD,QAAQ,CAACI,YAAY,CAACE,QAAQ,EAAE,IAAI,CAACrI,QAAQ,CAACyN,MAAM,CAAC,IAAK,IAAI,CAACzN,QAAQ,CAAC0N,sBAAsB,IAAIvF,YAAY,CAACE,QAAQ,CAACsF,QAAQ,CAAC,GAAG,CAAE,IAAIxF,YAAY,CAACgD,cAAc,KAAK,GAAG;QAE9M,OAAOhD,YAAY;MACrB,CAAC;MAED1C,UAAU,CAACpR,SAAS,CAACuY,iBAAiB,GAAG,UAASrE,OAAO,EAAE3B,SAAS,EAAEuB,YAAY,EAAEgC,cAAc,EAAED,UAAU,EAAE;QAE9G,IAAI,CAAC/B,YAAY,CAACsE,gBAAgB,EAAE;UAClC,IAAItE,YAAY,CAAC4E,UAAU,EAAE;YAAE;YAC7B5E,YAAY,CAACwE,eAAe,GAAG,IAAI,CAACnD,UAAU,CAACN,OAAO,CAACf,YAAY,CAACE,QAAQ,CAAC,CAAC,CAAC;UACjF,CAAC,MAAM;YAAE;YACP;YACA;YACA,IAAI,IAAI,CAACuF,wBAAwB,CAACzF,YAAY,CAAC,EAAE;cAC/C,IAAI,CAACA,YAAY,CAACoD,iBAAiB,EAAE;gBACnChD,OAAO,CAACxB,aAAa,CAAC,KAAK,CAAC;cAC9B;YACF;YAEA,IAAI,CAACyC,UAAU,CAACb,UAAU,CAACR,YAAY,CAAC,CAAC,CAAC;;YAE1C,IAAI,CAACA,YAAY,CAACE,QAAQ,KAAK,QAAQ,IAAIF,YAAY,CAACE,QAAQ,KAAK,OAAO,KAC1E,EAAEF,YAAY,CAAC+C,cAAc,IAAI/C,YAAY,CAACmD,sBAAsB,CAAC,EAAE;cACvEnD,YAAY,CAACwD,sBAAsB,GAAGhE,0BAA0B,CAACQ,YAAY,CAACP,SAAS,EAAEhB,SAAS,CAAC;YACrG;UACF;QACF;QAEA,IAAImB,QAAQ,CAACI,YAAY,CAACP,SAAS,EAAE,IAAI,CAAC5H,QAAQ,CAAC6N,YAAY,CAAC,EAAE;UAAE;UAClEtF,OAAO,CAACxB,aAAa,CAAC,KAAK,CAAC;UAC5B,IAAI,CAACwB,OAAO,CAACjC,OAAO,CAACxM,oBAAoB,CAAC,CAAC,EAAE;YAC3CyO,OAAO,CAACxB,aAAa,CAAC,IAAI,CAAC;UAC7B;QACF;QAEA,IAAIoB,YAAY,CAACsE,gBAAgB,EAAE;UAAE;;UAEnC;UACA;UACA,IAAItE,YAAY,CAACgD,cAAc,KAAK,GAAG,IAAIhD,YAAY,CAACP,SAAS,KAAK,MAAM,EAAE;YAC5E,IAAI,CAAC4B,UAAU,CAACL,aAAa,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YACvDhB,YAAY,CAACkD,cAAc,GAAG,IAAI;YAClC;YACA,IAAIyC,oBAAoB,GAAGvF,OAAO,CAAChC,sBAAsB,CAAC,OAAO,CAAC;YAClE,IAAI,CAACuH,oBAAoB,EAAE;cACzBvF,OAAO,CAACxB,aAAa,CAAC,KAAK,CAAC;YAC9B;UACF;;UAEA;UACA,IAAIoB,YAAY,CAACE,QAAQ,KAAK,KAAK,IAAI6B,UAAU,CAAC5P,IAAI,KAAKoF,KAAK,CAACiL,SAAS,IACxER,cAAc,CAAC4C,UAAU,IAAI5E,YAAY,CAAC7O,IAAI,CAACa,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACrE;UAAA,CACD,MAAM;YACL,IAAI,EAAEgO,YAAY,CAACoD,iBAAiB,IAAIpD,YAAY,CAAC+C,cAAc,CAAC,EAAE;cACpE3C,OAAO,CAACxB,aAAa,CAAC,KAAK,CAAC;YAC9B;YACA,IAAI,CAACgH,2BAA2B,CAACxF,OAAO,EAAEJ,YAAY,CAAC;UACzD;QACF,CAAC,MAAM,IAAIA,YAAY,CAAC4E,UAAU,EAAE;UAAE;UACpC,IAAIiB,aAAa,GAAG,KAAK;;UAEzB;UACAA,aAAa,GAAG7F,YAAY,CAACwE,eAAe,IAAIxE,YAAY,CAACwE,eAAe,CAACK,iBAAiB;UAC9FgB,aAAa,GAAGA,aAAa,IAAK,CAAC7F,YAAY,CAACoD,iBAAiB,IAC/D,EAAEpB,cAAc,CAACoB,iBAAiB,IAAIpB,cAAc,CAACe,cAAc,CAAC,IACpE,EAAEhB,UAAU,CAAC5P,IAAI,KAAKoF,KAAK,CAACiL,SAAS,IAAIxC,YAAY,CAACwE,eAAe,KAAKxC,cAAc,CAAC,IACzFD,UAAU,CAAC5P,IAAI,KAAK,YACrB;UAED,IAAI6N,YAAY,CAACmD,sBAAsB,IAAInD,YAAY,CAAC+C,cAAc,EAAE;YACtE8C,aAAa,GAAG,KAAK;UACvB;UAEA,IAAIA,aAAa,EAAE;YACjBzF,OAAO,CAACxB,aAAa,CAAC,KAAK,CAAC;UAC9B;QACF,CAAC,MAAM;UAAE;UACPoB,YAAY,CAACkD,cAAc,GAAG,CAAClD,YAAY,CAACwD,sBAAsB;UAElE,IAAIxD,YAAY,CAACgD,cAAc,KAAK,GAAG,EAAE;YACvC,IAAIhD,YAAY,CAACE,QAAQ,KAAK,MAAM,EAAE;cACpCF,YAAY,CAACkD,cAAc,GAAG,IAAI,CAACrL,QAAQ,CAACiO,iBAAiB;YAC/D,CAAC,MAAM,IAAI9F,YAAY,CAACE,QAAQ,KAAK,MAAM,EAAE;cAC3CF,YAAY,CAACkD,cAAc,GAAG,IAAI,CAACrL,QAAQ,CAACkO,sBAAsB;YACpE,CAAC,MAAM,IAAI/F,YAAY,CAACE,QAAQ,KAAK,MAAM,EAAE;cAC3CF,YAAY,CAACkD,cAAc,GAAG,IAAI,CAACrL,QAAQ,CAACmO,sBAAsB;YACpE;UACF;UAEA,IAAI,EAAEhG,YAAY,CAACoD,iBAAiB,IAAIpD,YAAY,CAAC+C,cAAc,CAAC,KACjEhB,UAAU,CAAC5P,IAAI,KAAK,YAAY,IAAI6N,YAAY,CAACmD,sBAAsB,CAAC,EAAE;YAC3E/C,OAAO,CAACxB,aAAa,CAAC,KAAK,CAAC;UAC9B;UAEA,IAAI,CAACgH,2BAA2B,CAACxF,OAAO,EAAEJ,YAAY,CAAC;QACzD;MACF,CAAC;MAED1C,UAAU,CAACpR,SAAS,CAAC0Z,2BAA2B,GAAG,UAASxF,OAAO,EAAEJ,YAAY,EAAE;QACjF,IAAIA,YAAY,CAACxU,MAAM,IAAI4U,OAAO,CAACjC,OAAO,CAAC5N,kBAAkB,CAAC,CAAC,IAC7D,EAAE,CAACyP,YAAY,CAACoD,iBAAiB,IAAIpD,YAAY,CAAC+C,cAAc,KAAK/C,YAAY,CAACxU,MAAM,CAAC4X,iBAAiB,CAAC,EAAE;UAC7GpD,YAAY,CAACxU,MAAM,CAACqZ,iBAAiB,GAAG,IAAI;QAC9C;MACF,CAAC;;MAED;MACA,IAAIoB,SAAS,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC;MAClQ,IAAIC,iBAAiB,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,CAAC;MAEhF5I,UAAU,CAACpR,SAAS,CAACuZ,wBAAwB,GAAG,UAASzF,YAAY,EAAE;QACrE,IAAI3R,MAAM,GAAG,IAAI;QACjB;QACA;QACA;QACA;QACA,IAAI2R,YAAY,CAACsE,gBAAgB,IAAI,CAACtE,YAAY,CAACuE,YAAY,IAAI,CAACvE,YAAY,CAACxU,MAAM,EAAE;UACvF;QAEF;QAEA,IAAIwU,YAAY,CAACE,QAAQ,KAAK,MAAM,EAAE;UACpC;UACA7R,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,MAAM,CAAC;;UAElD;UACA;QAEF,CAAC,MAAM,IAAIf,YAAY,CAACE,QAAQ,KAAK,IAAI,EAAE;UACzC;UACA7R,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAExE,CAAC,MAAM,IAAIf,YAAY,CAACE,QAAQ,KAAK,IAAI,IAAIF,YAAY,CAACE,QAAQ,KAAK,IAAI,EAAE;UAC3E;UACA;UACA7R,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;UACxD1S,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;QAG1D,CAAC,MAAM,IAAIf,YAAY,CAACxU,MAAM,CAAC0U,QAAQ,KAAK,GAAG,IAAI+F,SAAS,CAACjU,OAAO,CAACgO,YAAY,CAACE,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;UAClG;UACA;UACA;UACA;UACA,IAAIiG,QAAQ,GAAGnG,YAAY,CAACxU,MAAM,CAACA,MAAM;UACzC,IAAI,CAAC2a,QAAQ,IAAID,iBAAiB,CAAClU,OAAO,CAACmU,QAAQ,CAACjG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;YACpE7R,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,GAAG,CAAC;UACjD;QACF,CAAC,MAAM,IAAIf,YAAY,CAACE,QAAQ,KAAK,IAAI,IAAIF,YAAY,CAACE,QAAQ,KAAK,IAAI,EAAE;UAC3E;UACA;UACA7R,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;UACjE1S,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAEnE,CAAC,MAAM,IAAIf,YAAY,CAACE,QAAQ,KAAK,UAAU,EAAE;UAC/C;UACA;UACA7R,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC;UAClE;QAEF,CAAC,MAAM,IAAIf,YAAY,CAACE,QAAQ,KAAK,QAAQ,EAAE;UAC7C;UACA7R,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAE1F,CAAC,MAAM,IAAIf,YAAY,CAACE,QAAQ,KAAK,UAAU,EAAE;UAC/C;UACA;UACA7R,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC;QAElE,CAAC,MAAM,IAAIf,YAAY,CAACE,QAAQ,KAAK,OAAO,EAAE;UAC5C;UACA;UACA7R,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC;UAChE1S,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC;;UAEjE;UACA;QAEF,CAAC,MAAM,IAAIf,YAAY,CAACE,QAAQ,KAAK,OAAO,IAAIF,YAAY,CAACE,QAAQ,KAAK,OAAO,EAAE;UACjF;UACA;UACA;UACA;UACA7R,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC;UAChE1S,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC;UACjE1S,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;UAC9D1S,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;;UAE9D;UACA;QAEF,CAAC,MAAM,IAAIf,YAAY,CAACE,QAAQ,KAAK,IAAI,EAAE;UACzC;UACA;UACA;UACA7R,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC;UAChE1S,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC;UACjE1S,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QAExF,CAAC,MAAM,IAAIf,YAAY,CAACE,QAAQ,KAAK,IAAI,IAAIF,YAAY,CAACE,QAAQ,KAAK,IAAI,EAAE;UAC3E;UACA;UACA7R,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;UAC5F1S,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgT,UAAU,CAACN,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAC9F;;QAEA;QACA;QACA;QACA;;QAEA;QACAf,YAAY,CAACxU,MAAM,GAAG,IAAI,CAAC6V,UAAU,CAACd,gBAAgB,CAAC,CAAC;QAExD,OAAOlS,MAAM;MACf,CAAC;MAED/C,MAAM,CAAC2G,OAAO,CAACqL,UAAU,GAAGA,UAAU;;MAGtC;IAAM,CAAC,IACP;IACA,KAAO,UAAShS,MAAM,EAAE6L,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAIgP,WAAW,GAAIhP,mBAAmB,CAAC,CAAC,CAAC,CAAC3E,OAAQ;MAElD,SAASA,OAAOA,CAAC/D,OAAO,EAAE;QACxB0X,WAAW,CAAC1M,IAAI,CAAC,IAAI,EAAEhL,OAAO,EAAE,MAAM,CAAC;QACvC,IAAI,IAAI,CAACyE,UAAU,CAAC3G,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC2G,UAAU,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;UACjE,IAAI,CAACA,UAAU,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,CAAC;QAC1D;QAEA,IAAI,CAAC2S,iBAAiB,GAAG,IAAI,CAAChT,YAAY,CAAC,mBAAmB,CAAC;QAC/D,IAAI,CAACkT,sBAAsB,GAAG,IAAI,CAAClT,YAAY,CAAC,wBAAwB,EAAE,IAAI,CAAC;QAC/E,IAAI,CAACiT,sBAAsB,GAAG,IAAI,CAACjT,YAAY,CAAC,wBAAwB,EAAE,IAAI,CAAC;QAE/E,IAAI,CAACmS,iBAAiB,GAAG,IAAI,CAACnS,YAAY,CAAC,mBAAmB,EAAE,IAAI,CAAC;QACrE,IAAI,CAAC0O,eAAe,GAAG,IAAI,CAAC3N,cAAc,CAAC,iBAAiB,EAC1D,CAAC,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,wBAAwB,EAAE,kBAAkB,EAAE,UAAU,EAAE,kBAAkB,CAAC,CAAC;QACnH,IAAI,CAAC0P,yBAAyB,GAAG,IAAI,CAACvQ,WAAW,CAAC,2BAA2B,EAAE,CAAC,CAAC;QACjF,IAAI,CAACkS,2BAA2B,GAAG,IAAI,CAAClS,WAAW,CAAC,6BAA6B,EAAE,IAAI,CAAC/E,WAAW,CAAC;QACpG,IAAI,CAACyX,YAAY,GAAG,IAAI,CAACrS,UAAU,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;;QAE9E;QACA;QACA;QACA;QACA,IAAI,CAACiS,MAAM,GAAG,IAAI,CAACjS,UAAU,CAAC,QAAQ,EAAE,CACtC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EACjF,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAC7E,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EACpF,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,eAAgB,QAAQ,EAAE,OAAO,EAC3F,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EACjF,OAAO,EAAE,KAAK,EAAE,MAAM;QACtB;QACA,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,CACjC,CAAC;QACF,IAAI,CAACkS,sBAAsB,GAAG,IAAI,CAACzS,YAAY,CAAC,wBAAwB,EAAE,IAAI,CAAC;QAC/E,IAAI,CAACqS,aAAa,GAAG,IAAI,CAAC9R,UAAU,CAAC,eAAe,EAAE;QACpD;QACA;QACA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EACpE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK;QAC7D;QACA;;QAEA;QACA,UAAU,EAAE,MAAM;QAElB;QACA;QACA;QACA,UAAU,EAAE,SAAS,CACtB,CAAC;QACF,IAAI,CAAC+R,WAAW,GAAG,IAAI,CAAC/R,UAAU,CAAC,aAAa,EAAE,EAAE,CAAC;QACrD,IAAI,CAACgS,mBAAmB,GAAG,IAAI,CAAChS,UAAU,CAAC,qBAAqB,EAAE,CAChE,KAAK,EAAE,UAAU,CAClB,CAAC;QACF,IAAI,CAACgT,6BAA6B,GAAG,IAAI,CAACtT,eAAe,CAAC,+BAA+B,CAAC;QAC1F,IAAI,CAACgR,cAAc,GAAG,IAAI,CAAClQ,cAAc,CAAC,gBAAgB,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;MAE7F;MACApB,OAAO,CAACvG,SAAS,GAAG,IAAIka,WAAW,CAAC,CAAC;MAIrC9a,MAAM,CAAC2G,OAAO,CAACQ,OAAO,GAAGA,OAAO;;MAGhC;IAAM,CAAC,IACP;IACA,KAAO,UAASnH,MAAM,EAAE6L,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAIkP,aAAa,GAAIlP,mBAAmB,CAAC,CAAC,CAAC,CAACO,SAAU;MACtD,IAAI4O,SAAS,GAAInP,mBAAmB,CAAC,CAAC,CAAC,CAACG,KAAM;MAC9C,IAAIwD,UAAU,GAAI3D,mBAAmB,CAAC,EAAE,CAAC,CAAC2D,UAAW;MACrD,IAAIiB,kBAAkB,GAAI5E,mBAAmB,CAAC,EAAE,CAAC,CAAC4E,kBAAmB;MACrE,IAAIxC,OAAO,GAAIpC,mBAAmB,CAAC,EAAE,CAAC,CAACoC,OAAQ;MAE/C,IAAIjC,KAAK,GAAG;QACV4K,QAAQ,EAAE,aAAa;QACvBK,SAAS,EAAE,cAAc;QACzBG,iBAAiB,EAAE,sBAAsB;QACzCE,kBAAkB,EAAE,uBAAuB;QAC3CxD,SAAS,EAAE,cAAc;QACzBC,MAAM,EAAE,WAAW;QACnBC,KAAK,EAAE,UAAU;QACjB6C,OAAO,EAAE,YAAY;QACrB1D,IAAI,EAAE,SAAS;QACf8H,OAAO,EAAE,YAAY;QACrBhP,KAAK,EAAE+O,SAAS,CAAC/O,KAAK;QACtBC,GAAG,EAAE8O,SAAS,CAAC9O,GAAG;QAClBC,GAAG,EAAE6O,SAAS,CAAC7O;MACjB,CAAC;MAED,IAAI+O,eAAe,GAAG,IAAI1L,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC;MAEpD,IAAIpD,SAAS,GAAG,SAAAA,CAAS5C,YAAY,EAAErG,OAAO,EAAE;QAC9C4X,aAAa,CAAC5M,IAAI,CAAC,IAAI,EAAE3E,YAAY,EAAErG,OAAO,CAAC;QAC/C,IAAI,CAACgY,iBAAiB,GAAG,EAAE;;QAE3B;QACA;QACA,IAAIC,kBAAkB,GAAG,IAAI3K,kBAAkB,CAAC,IAAI,CAACpE,MAAM,CAAC,CAACoF,YAAY,CAAC,IAAI,CAACnF,QAAQ,CAAC;QACxF,IAAI+O,cAAc,GAAG,IAAIpN,OAAO,CAAC,IAAI,CAAC5B,MAAM,CAAC;QAE7C,IAAI,CAAC0E,UAAU,GAAG;UAChBuK,IAAI,EAAEF,kBAAkB,CAAC9L,KAAK,CAAC,YAAY,CAAC;UAC5CiM,gCAAgC,EAAEH,kBAAkB,CAAC9L,KAAK,CAAC,aAAa,CAAC;UACzEkM,YAAY,EAAEJ,kBAAkB,CAACxQ,WAAW,CAAC,GAAG,CAAC;UACjD6Q,YAAY,EAAEL,kBAAkB,CAACxQ,WAAW,CAAC,GAAG,CAAC;UACjD8Q,SAAS,EAAEN,kBAAkB,CAAC9L,KAAK,CAAC,iBAAiB,CAAC;UACtDqM,YAAY,EAAEP,kBAAkB,CAAC9L,KAAK,CAAC,cAAc,CAAC;UAEtDsM,0BAA0B,EAAEP,cAAc,CAACzM,QAAQ,CAAC,uBAAuB,CAAC;UAC5EoC,kBAAkB,EAAEqK,cAAc,CAAC9L,aAAa,CAAC,OAAO,CAAC,CAAC3E,WAAW,CAAC,MAAM,CAAC;UAC7EyF,UAAU,EAAEgL,cAAc,CAAC9L,aAAa,CAAC,IAAI,CAAC,CAAC3E,WAAW,CAAC,IAAI,CAAC;UAChEiR,eAAe,EAAER,cAAc,CAAC/L,KAAK,CAAC,YAAY,CAAC;UACnDwM,oBAAoB,EAAET,cAAc,CAAC/L,KAAK,CAAC,IAAI,CAAC;UAChDyM,OAAO,EAAEV,cAAc,CAAC9L,aAAa,CAAC,MAAM,CAAC,CAAC3E,WAAW,CAAC,KAAK,CAAC;UAChEoR,KAAK,EAAEX,cAAc,CAAC9L,aAAa,CAAC,aAAa,CAAC,CAAC3E,WAAW,CAAC,KAAK,CAAC;UACrE;UACAqR,mBAAmB,EAAEZ,cAAc,CAAC9L,aAAa,CAAC,MAAM,CAAC,CAAC3E,WAAW,CAAC,IAAI,CAAC;UAC3EsR,UAAU,EAAEb,cAAc,CAAC9L,aAAa,CAAC,KAAK,CAAC,CAAC3E,WAAW,CAAC,KAAK;QACnE,CAAC;QAED,IAAI,IAAI,CAAC0B,QAAQ,CAACoN,iBAAiB,EAAE;UACnC,IAAI,CAAC3I,UAAU,CAACuK,IAAI,GAAG,IAAI,CAACvK,UAAU,CAACuK,IAAI,CAAC5J,OAAO,CAAC,YAAY,CAAC;UACjE,IAAI,CAACX,UAAU,CAACwK,gCAAgC,GAAG,IAAI,CAACxK,UAAU,CAACwK,gCAAgC,CAAC7J,OAAO,CAAC,YAAY,CAAC;QAC3H;QAEA,IAAI,CAACyK,8BAA8B,GAAG,IAAI;QAE1C,IAAI,IAAI,CAAC7P,QAAQ,CAACwO,6BAA6B,EAAE;UAC/C,IAAIsB,cAAc,GAAG,IAAI,CAAC/P,MAAM,CAAChB,kBAAkB,CAAC,IAAI,CAACiB,QAAQ,CAACwO,6BAA6B,CAAC;UAChG,IAAI,CAAC/J,UAAU,CAAC+J,6BAA6B,GAC3CO,cAAc,CAACzM,QAAQ,CAACwN,cAAc,CAAC,CACtCxR,WAAW,CAACwR,cAAc,CAAC;QAChC;MACF,CAAC;MACDhQ,SAAS,CAACzL,SAAS,GAAG,IAAIoa,aAAa,CAAC,CAAC;MAEzC3O,SAAS,CAACzL,SAAS,CAACsM,WAAW,GAAG,UAASU,aAAa,EAAE;QAAE;QAC1D,OAAO,KAAK,CAAC,CAAC;MAChB,CAAC;MAEDvB,SAAS,CAACzL,SAAS,CAACyM,WAAW,GAAG,UAASO,aAAa,EAAE;QACxD,OAAOA,aAAa,CAAC/G,IAAI,KAAKoF,KAAK,CAAC4K,QAAQ,IAAIjJ,aAAa,CAAC/G,IAAI,KAAKoF,KAAK,CAACoL,iBAAiB;MAChG,CAAC;MAEDhL,SAAS,CAACzL,SAAS,CAAC0M,WAAW,GAAG,UAASM,aAAa,EAAEd,UAAU,EAAE;QACpE,OAAQc,aAAa,CAAC/G,IAAI,KAAKoF,KAAK,CAACiL,SAAS,IAC3CpK,UAAU,KACR,CAACc,aAAa,CAAC/H,IAAI,KAAK,GAAG,IAAI+H,aAAa,CAAC/H,IAAI,KAAK,IAAI,KAAKiH,UAAU,CAACjH,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IACzF+H,aAAa,CAAC/H,IAAI,KAAK,IAAI,IAAIiH,UAAU,CAACjH,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIiH,UAAU,CAACjH,IAAI,CAAC,CAAC,CAAC,KAAK,GAAI,CAAE,IACzF+H,aAAa,CAAC/G,IAAI,KAAKoF,KAAK,CAACsL,kBAAkB,IAClD3J,aAAa,CAAC/H,IAAI,KAAK,GAAG,IAAIiH,UAAU,CAACjH,IAAI,CAACyW,QAAQ,CAAC,GAAG,CAAG;MAClE,CAAC;MAEDjQ,SAAS,CAACzL,SAAS,CAACgM,MAAM,GAAG,YAAW;QACtC,IAAI,CAACwO,iBAAiB,GAAG,EAAE;MAC7B,CAAC;MAED/O,SAAS,CAACzL,SAAS,CAACqM,eAAe,GAAG,UAASO,cAAc,EAAEV,UAAU,EAAE;QAAE;QAC3E,IAAIrH,KAAK,GAAG,IAAI;QAChB,IAAI,CAACgI,eAAe,CAAC,CAAC;QACtB,IAAIqE,CAAC,GAAG,IAAI,CAACxF,MAAM,CAACpC,IAAI,CAAC,CAAC;QAE1B,IAAI4H,CAAC,KAAK,IAAI,EAAE;UACd,OAAO,IAAI,CAACnE,aAAa,CAAC1B,KAAK,CAACG,GAAG,EAAE,EAAE,CAAC;QAC1C;QAEA3G,KAAK,GAAGA,KAAK,IAAI,IAAI,CAAC8W,qBAAqB,CAACzK,CAAC,EAAEhF,UAAU,CAAC;QAC1DrH,KAAK,GAAGA,KAAK,IAAI,IAAI,CAAC+W,eAAe,CAAC1K,CAAC,EAAEtE,cAAc,EAAEV,UAAU,CAAC;QACpErH,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACgX,WAAW,CAAC3K,CAAC,EAAEhF,UAAU,CAAC;QAChDrH,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACiX,sBAAsB,CAAC5K,CAAC,EAAEtE,cAAc,CAAC;QAC/D/H,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACkX,mBAAmB,CAAC7K,CAAC,EAAEhF,UAAU,CAAC;QACxDrH,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACmX,iBAAiB,CAAC9K,CAAC,EAAEtE,cAAc,EAAEV,UAAU,CAAC;QACtErH,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACoX,kBAAkB,CAAC/K,CAAC,EAAEhF,UAAU,CAAC;QACvDrH,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACqX,sBAAsB,CAAChL,CAAC,CAAC;QAC/CrM,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACsX,gBAAgB,CAACjL,CAAC,CAAC;QACzCrM,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACuX,UAAU,CAAClL,CAAC,EAAEhF,UAAU,CAAC;QAC/CrH,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACkI,aAAa,CAAC1B,KAAK,CAACiP,OAAO,EAAE,IAAI,CAAC5O,MAAM,CAACrK,IAAI,CAAC,CAAC,CAAC;QAEtE,OAAOwD,KAAK;MACd,CAAC;MAED4G,SAAS,CAACzL,SAAS,CAACkc,sBAAsB,GAAG,UAAShL,CAAC,EAAE;QAAE;QACzD,IAAIrM,KAAK,GAAG,IAAI;QAChB,IAAIiI,gBAAgB,GAAG,IAAI;QAC3B,IAAIxG,UAAU,GAAG,IAAI;QAErB,IAAI4K,CAAC,KAAK,GAAG,EAAE;UACb,IAAIC,KAAK,GAAG,IAAI,CAACzF,MAAM,CAACpC,IAAI,CAAC,CAAC,CAAC;UAC/B;UACA;UACA,IAAI6H,KAAK,KAAK,GAAG,EAAE;YACjBrE,gBAAgB,GAAG,IAAI,CAACsD,UAAU,CAACgL,OAAO,CAACtR,IAAI,CAAC,CAAC;;YAEjD;YACA,IAAIgD,gBAAgB,EAAE;cACpBxG,UAAU,GAAGiU,eAAe,CAACpL,cAAc,CAACrC,gBAAgB,CAAC;cAC7D,IAAIxG,UAAU,IAAIA,UAAU,CAAC+V,MAAM,KAAK,OAAO,EAAE;gBAC/CvP,gBAAgB,IAAIyN,eAAe,CAAClL,WAAW,CAAC,IAAI,CAAC3D,MAAM,CAAC;cAC9D;YACF,CAAC,MAAM;cACLoB,gBAAgB,GAAG,IAAI,CAACsD,UAAU,CAACiL,KAAK,CAACvR,IAAI,CAAC,CAAC;YACjD;UACF;UAEA,IAAIgD,gBAAgB,EAAE;YACpBjI,KAAK,GAAG,IAAI,CAACkI,aAAa,CAAC1B,KAAK,CAAC6K,OAAO,EAAEpJ,gBAAgB,CAAC;YAC3DjI,KAAK,CAACyB,UAAU,GAAGA,UAAU;UAC/B;QACF;QAEA,OAAOzB,KAAK;MACd,CAAC;MAED4G,SAAS,CAACzL,SAAS,CAACmc,gBAAgB,GAAG,UAASjL,CAAC,EAAE;QAAE;QACnD,IAAIrM,KAAK,GAAG,IAAI;QAChB,IAAIiI,gBAAgB,GAAG,IAAI;QAC3B,IAAIxG,UAAU,GAAG,IAAI;QAErB,IAAI4K,CAAC,KAAK,GAAG,EAAE;UACb,IAAIC,KAAK,GAAG,IAAI,CAACzF,MAAM,CAACpC,IAAI,CAAC,CAAC,CAAC;UAC/B,IAAI6H,KAAK,KAAK,GAAG,IAAIA,KAAK,KAAK,GAAG,EAAE;YAClCrE,gBAAgB,GAAG,IAAI,CAACsD,UAAU,CAACkL,mBAAmB,CAACxR,IAAI,CAAC,CAAC;YAC7DgD,gBAAgB,GAAGA,gBAAgB,IAAI,IAAI,CAACsD,UAAU,CAACmL,UAAU,CAACzR,IAAI,CAAC,CAAC;UAC1E;UAEA,IAAIgD,gBAAgB,EAAE;YACpBjI,KAAK,GAAG,IAAI,CAACkI,aAAa,CAAC1B,KAAK,CAAC6K,OAAO,EAAEpJ,gBAAgB,CAAC;YAC3DjI,KAAK,CAACyB,UAAU,GAAGA,UAAU;UAC/B;QACF;QAEA,OAAOzB,KAAK;MACd,CAAC;MAED4G,SAAS,CAACzL,SAAS,CAACoc,UAAU,GAAG,UAASlL,CAAC,EAAEhF,UAAU,EAAE;QACvD,IAAIY,gBAAgB,GAAG,IAAI;QAC3B,IAAIjI,KAAK,GAAG,IAAI;QAChB,IAAI,CAACqH,UAAU,IAAIA,UAAU,CAACjG,IAAI,KAAKoF,KAAK,CAACoL,iBAAiB,EAAE;UAC9D,IAAIvF,CAAC,KAAK,GAAG,EAAE;YAEbpE,gBAAgB,GAAG,IAAI,CAACpB,MAAM,CAACrK,IAAI,CAAC,CAAC;YACrC,IAAI,IAAI,CAACqK,MAAM,CAACpC,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;cAC9BwD,gBAAgB,IAAI,IAAI,CAACpB,MAAM,CAACrK,IAAI,CAAC,CAAC;YACxC;YACAyL,gBAAgB,IAAI,IAAI,CAACsD,UAAU,CAAC4K,YAAY,CAAClR,IAAI,CAAC,CAAC;YACvDjF,KAAK,GAAG,IAAI,CAACkI,aAAa,CAAC1B,KAAK,CAAC4K,QAAQ,EAAEnJ,gBAAgB,CAAC;UAC9D;QACF;QACA,OAAOjI,KAAK;MACd,CAAC;MAED4G,SAAS,CAACzL,SAAS,CAAC2b,qBAAqB,GAAG,UAASzK,CAAC,EAAEhF,UAAU,EAAE;QAClE,IAAIY,gBAAgB,GAAG,IAAI;QAC3B,IAAIjI,KAAK,GAAG,IAAI;QAChB,IAAI,CAACqH,UAAU,IAAIA,UAAU,CAACjG,IAAI,KAAKoF,KAAK,CAACoL,iBAAiB,EAAE;UAC9D,IAAI,CAAC,IAAI,CAAC9K,QAAQ,CAAC1E,UAAU,CAACqS,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC3N,QAAQ,CAACoN,iBAAiB,KAAK7H,CAAC,KAAK,GAAG,IAAI,IAAI,CAACxF,MAAM,CAACpC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACjI,IAAI,IAAI,CAACqC,QAAQ,CAACoN,iBAAiB,IAAI,IAAI,CAACrN,MAAM,CAACpC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;cAClEwD,gBAAgB,GAAG,IAAI,CAACsD,UAAU,CAACC,kBAAkB,CAACvG,IAAI,CAAC,CAAC;cAC5DgD,gBAAgB,GAAGA,gBAAgB,IAAI,IAAI,CAACsD,UAAU,CAACV,UAAU,CAAC5F,IAAI,CAAC,CAAC;cACxEjF,KAAK,GAAG,IAAI,CAACkI,aAAa,CAAC1B,KAAK,CAAC6K,OAAO,EAAEpJ,gBAAgB,CAAC;YAC7D,CAAC,MAAM;cACLA,gBAAgB,GAAG,IAAI,CAACsD,UAAU,CAAC8K,eAAe,CAACpR,IAAI,CAAC,CAAC;cACzDjF,KAAK,GAAG,IAAI,CAACkI,aAAa,CAAC1B,KAAK,CAAC4K,QAAQ,EAAEnJ,gBAAgB,CAAC;YAC9D;UACF;QACF;QACA,OAAOjI,KAAK;MACd,CAAC;MAED4G,SAAS,CAACzL,SAAS,CAAC+b,mBAAmB,GAAG,UAAS7K,CAAC,EAAEhF,UAAU,EAAE;QAChE,IAAIY,gBAAgB,GAAG,EAAE;QACzB,IAAIjI,KAAK,GAAG,IAAI;QAChB;QACA,IAAI,CAAC,IAAI,CAAC8G,QAAQ,CAAC1E,UAAU,CAACqS,QAAQ,CAAC,SAAS,CAAC,EAAE;UACjD,OAAOzU,KAAK;QACd;QAEA,IAAIqM,CAAC,KAAK,GAAG,EAAE;UACbpE,gBAAgB,GAAG,IAAI,CAACsD,UAAU,CAAC6K,0BAA0B,CAACnR,IAAI,CAAC,CAAC;UACpE,IAAIgD,gBAAgB,KAAK,EAAE,EAAE;YAC3B,OAAOjI,KAAK;UACd;UAEA,IAAIyX,yBAAyB,GAAGxP,gBAAgB,CAAC4O,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;UACtE,IAAIa,yBAAyB,GAAG,CAAC;UACjC;UACA;UACA,OAAO,EAAEzP,gBAAgB,CAAC4O,QAAQ,CAAC,GAAG,CAAC,IAAIY,yBAAyB,KAAKC,yBAAyB,CAAC,EAAE;YACnG,IAAIC,SAAS,GAAG,IAAI,CAAC9Q,MAAM,CAACrK,IAAI,CAAC,CAAC;YAClC,IAAImb,SAAS,KAAK,IAAI,EAAE;cACtB;YACF,CAAC,MAAM,IAAIA,SAAS,KAAK,GAAG,EAAE;cAC5BF,yBAAyB,EAAE;YAC7B,CAAC,MAAM,IAAIE,SAAS,KAAK,GAAG,EAAE;cAC5BD,yBAAyB,EAAE;YAC7B;YACAzP,gBAAgB,IAAI0P,SAAS;UAC/B;UACA3X,KAAK,GAAG,IAAI,CAACkI,aAAa,CAAC1B,KAAK,CAACoL,iBAAiB,EAAE3J,gBAAgB,CAAC;QACvE,CAAC,MAAM,IAAIoE,CAAC,KAAK,GAAG,IAAIhF,UAAU,IAAIA,UAAU,CAACjG,IAAI,KAAKoF,KAAK,CAACoL,iBAAiB,EAAE;UACjF3J,gBAAgB,GAAG,IAAI,CAACpB,MAAM,CAACrK,IAAI,CAAC,CAAC;UACrCwD,KAAK,GAAG,IAAI,CAACkI,aAAa,CAAC1B,KAAK,CAACsL,kBAAkB,EAAE7J,gBAAgB,CAAC;QACxE;QACA,OAAOjI,KAAK;MACd,CAAC;MAGD4G,SAAS,CAACzL,SAAS,CAAC6b,WAAW,GAAG,UAAS3K,CAAC,EAAEhF,UAAU,EAAE;QACxD,IAAIY,gBAAgB,GAAG,IAAI;QAC3B,IAAIjI,KAAK,GAAG,IAAI;QAChB,IAAIqH,UAAU,IAAIA,UAAU,CAACjG,IAAI,KAAKoF,KAAK,CAAC4K,QAAQ,EAAE;UACpD,IAAI/J,UAAU,CAACjH,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,KAAKiM,CAAC,KAAK,GAAG,IAAKA,CAAC,KAAK,GAAG,IAAI,IAAI,CAACxF,MAAM,CAACpC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAI,CAAC,EAAE;YAC3FwD,gBAAgB,GAAG,IAAI,CAACpB,MAAM,CAACrK,IAAI,CAAC,CAAC;YACrC,IAAI6P,CAAC,KAAK,GAAG,EAAE;cAAE;cACfpE,gBAAgB,IAAI,IAAI,CAACpB,MAAM,CAACrK,IAAI,CAAC,CAAC;YACxC;YACAwD,KAAK,GAAG,IAAI,CAACkI,aAAa,CAAC1B,KAAK,CAACiL,SAAS,EAAExJ,gBAAgB,CAAC;UAC/D,CAAC,MAAM,IAAIZ,UAAU,CAACjH,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIiM,CAAC,KAAK,GAAG,IAAI,IAAI,CAACxF,MAAM,CAACpC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACjF,IAAI,CAACoC,MAAM,CAACrK,IAAI,CAAC,CAAC;YAClB,IAAI,CAACqK,MAAM,CAACrK,IAAI,CAAC,CAAC;YAClBwD,KAAK,GAAG,IAAI,CAACkI,aAAa,CAAC1B,KAAK,CAACiL,SAAS,EAAE,IAAI,CAAC;UACnD;QACF;QAEA,OAAOzR,KAAK;MACd,CAAC;MAED4G,SAAS,CAACzL,SAAS,CAAC4b,eAAe,GAAG,UAAS1K,CAAC,EAAEtE,cAAc,EAAEV,UAAU,EAAE;QAC5E,IAAIrH,KAAK,GAAG,IAAI;QAChB,IAAIiI,gBAAgB,GAAG,EAAE;QACzB,IAAIZ,UAAU,IAAIA,UAAU,CAACjH,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UAE5C,IAAIiM,CAAC,KAAK,GAAG,EAAE;YACbrM,KAAK,GAAG,IAAI,CAACkI,aAAa,CAAC1B,KAAK,CAAC+H,MAAM,EAAE,IAAI,CAAC1H,MAAM,CAACrK,IAAI,CAAC,CAAC,CAAC;UAC9D,CAAC,MAAM,IAAI6P,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,EAAE;YACjC,IAAIuL,OAAO,GAAG,IAAI,CAAC/Q,MAAM,CAACrK,IAAI,CAAC,CAAC;YAChC,IAAI6P,CAAC,KAAK,GAAG,EAAE;cACbuL,OAAO,IAAI,IAAI,CAACrM,UAAU,CAAC0K,YAAY,CAAChR,IAAI,CAAC,CAAC;YAChD,CAAC,MAAM;cACL2S,OAAO,IAAI,IAAI,CAACrM,UAAU,CAACyK,YAAY,CAAC/Q,IAAI,CAAC,CAAC;YAChD;YACAjF,KAAK,GAAG,IAAI,CAACkI,aAAa,CAAC1B,KAAK,CAACgI,KAAK,EAAEoJ,OAAO,CAAC;UAClD,CAAC,MAAM;YACL3P,gBAAgB,GAAG,IAAI,CAACsD,UAAU,CAAC2K,SAAS,CAACjR,IAAI,CAAC,CAAC;YAEnD,IAAIgD,gBAAgB,EAAE;cACpB,IAAIF,cAAc,CAAC3G,IAAI,KAAKoF,KAAK,CAAC+H,MAAM,EAAE;gBACxCvO,KAAK,GAAG,IAAI,CAACkI,aAAa,CAAC1B,KAAK,CAACgI,KAAK,EAAEvG,gBAAgB,CAAC;cAC3D,CAAC,MAAM;gBACLjI,KAAK,GAAG,IAAI,CAACkI,aAAa,CAAC1B,KAAK,CAAC8H,SAAS,EAAErG,gBAAgB,CAAC;cAC/D;YACF;UACF;QACF;QACA,OAAOjI,KAAK;MACd,CAAC;MAED4G,SAAS,CAACzL,SAAS,CAAC0c,uBAAuB,GAAG,UAAS1I,QAAQ,EAAE;QAC/D;QACA;QACA;QACA,OAAO,IAAI,CAACrI,QAAQ,CAACsN,aAAa,CAACnT,OAAO,CAACkO,QAAQ,CAAC,KAAK,CAAC,CAAC,KACxD,IAAI,CAACrI,QAAQ,CAACwN,mBAAmB,CAACrT,OAAO,CAACkO,QAAQ,CAAC,KAAK,CAAC,CAAC,IACzD,IAAI,CAACrI,QAAQ,CAACuN,WAAW,CAACpT,OAAO,CAACkO,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;MACzD,CAAC;MAEDvI,SAAS,CAACzL,SAAS,CAACgc,iBAAiB,GAAG,UAAS9K,CAAC,EAAEtE,cAAc,EAAEV,UAAU,EAAE;QAAE;QAChF,IAAIY,gBAAgB,GAAG,EAAE;QACzB,IAAIZ,UAAU,IAAIA,UAAU,CAACjH,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UAC5C6H,gBAAgB,GAAG,IAAI,CAACsD,UAAU,CAAC+K,oBAAoB,CAACrR,IAAI,CAAC,CAAC;QAChE,CAAC,MAAM,IAAI8C,cAAc,CAAC3G,IAAI,KAAKoF,KAAK,CAACiL,SAAS,IAChD1J,cAAc,CAACxG,MAAM,CAACnB,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI2H,cAAc,CAAC3H,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACzE;UACA,IAAI+O,QAAQ,GAAGpH,cAAc,CAACxG,MAAM,CAACnB,IAAI,CAACsQ,MAAM,CAAC,CAAC,CAAC,CAACvK,WAAW,CAAC,CAAC;UACjE,IAAI,IAAI,CAAC0R,uBAAuB,CAAC1I,QAAQ,CAAC,EAAE;YAE1ClH,gBAAgB,GAAG,IAAI,CAACpB,MAAM,CAACxB,SAAS,CAAC,IAAIxB,MAAM,CAAC,IAAI,GAAGsL,QAAQ,GAAG,iBAAiB,EAAE,IAAI,CAAC,CAAC;UACjG;QACF;QAEA,IAAIlH,gBAAgB,EAAE;UACpB,OAAO,IAAI,CAACC,aAAa,CAAC1B,KAAK,CAACmH,IAAI,EAAE1F,gBAAgB,CAAC;QACzD;QAEA,OAAO,IAAI;MACb,CAAC;MAEDrB,SAAS,CAACzL,SAAS,CAAC8b,sBAAsB,GAAG,UAAS5K,CAAC,EAAEtE,cAAc,EAAE;QAAE;QACzE,IAAIA,cAAc,CAAC3G,IAAI,KAAKoF,KAAK,CAACiL,SAAS,IAAI1J,cAAc,CAACxG,MAAM,CAACnB,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI2H,cAAc,CAAC3H,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACtH,IAAI+O,QAAQ,GAAGpH,cAAc,CAACxG,MAAM,CAACnB,IAAI,CAACsQ,MAAM,CAAC,CAAC,CAAC,CAACvK,WAAW,CAAC,CAAC;UACjE,IAAIgJ,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,OAAO,EAAE;YACjD;YACA;YACA,IAAInP,KAAK,GAAG,IAAI,CAACqX,sBAAsB,CAAChL,CAAC,CAAC;YAC1C,IAAIrM,KAAK,EAAE;cACTA,KAAK,CAACoB,IAAI,GAAGoF,KAAK,CAACmH,IAAI;cACvB,OAAO3N,KAAK;YACd;YACA,IAAIiI,gBAAgB,GAAG,IAAI,CAACpB,MAAM,CAACxB,SAAS,CAAC,IAAIxB,MAAM,CAAC,IAAI,GAAGsL,QAAQ,GAAG,iBAAiB,EAAE,IAAI,CAAC,CAAC;YACnG,IAAIlH,gBAAgB,EAAE;cACpB,OAAO,IAAI,CAACC,aAAa,CAAC1B,KAAK,CAACmH,IAAI,EAAE1F,gBAAgB,CAAC;YACzD;UACF;QACF;QACA,OAAO,IAAI;MACb,CAAC;MAEDrB,SAAS,CAACzL,SAAS,CAACic,kBAAkB,GAAG,UAAS/K,CAAC,EAAEhF,UAAU,EAAE;QAC/D,IAAIY,gBAAgB,GAAG,EAAE;QACzB,IAAI,IAAI,CAACnB,QAAQ,CAACwO,6BAA6B,EAAE;UAC/C,IAAIjJ,CAAC,KAAK,IAAI,CAACvF,QAAQ,CAACwO,6BAA6B,CAAC,CAAC,CAAC,EAAE;YACxDrN,gBAAgB,GAAG,IAAI,CAACsD,UAAU,CAAC+J,6BAA6B,CAACrQ,IAAI,CAAC,CAAC;UACzE;QACF;QAEA,IAAI,CAACgD,gBAAgB,EAAE;UACrBA,gBAAgB,GAAIZ,UAAU,IAAIA,UAAU,CAACjG,IAAI,KAAKoF,KAAK,CAACoL,iBAAiB,GAAI,IAAI,CAACrG,UAAU,CAACwK,gCAAgC,CAAC9Q,IAAI,CAAC,CAAC,GAAG,IAAI,CAACsG,UAAU,CAACuK,IAAI,CAAC7Q,IAAI,CAAC,CAAC;QACxK;QACA,IAAIgD,gBAAgB,EAAE;UACpB,OAAO,IAAI,CAACC,aAAa,CAAC1B,KAAK,CAACmH,IAAI,EAAE1F,gBAAgB,CAAC;QACzD;QACA,OAAO,IAAI;MACb,CAAC;MAED1N,MAAM,CAAC2G,OAAO,CAAC0F,SAAS,GAAGA,SAAS;MACpCrM,MAAM,CAAC2G,OAAO,CAACsF,KAAK,GAAGA,KAAK;;MAG5B;IAAM;IACN,UAAY;IACZ;IACA,SAAU;IACV;IAAU,IAAIsR,wBAAwB,GAAG,CAAC,CAAC;IAC3C;IACA,SAAU;IACV;IAAU,SAASzR,mBAAmBA,CAAC0R,QAAQ,EAAE;MACjD,SAAW;MACX,QAAW,IAAIC,YAAY,GAAGF,wBAAwB,CAACC,QAAQ,CAAC;MAChE;MAAW,IAAIC,YAAY,KAAKrX,SAAS,EAAE;QAC3C,QAAY,OAAOqX,YAAY,CAAC9W,OAAO;QACvC;MAAW;MACX,SAAW;MACX;MAAW,IAAI3G,MAAM,GAAGud,wBAAwB,CAACC,QAAQ,CAAC,GAAG;QAC7D,SAAY;QACZ,SAAY;QACZ,QAAY7W,OAAO,EAAE,CAAC;QACtB;MAAW,CAAC;MACZ;MACA,SAAW;MACX;MAAW5G,mBAAmB,CAACyd,QAAQ,CAAC,CAACxd,MAAM,EAAEA,MAAM,CAAC2G,OAAO,EAAEmF,mBAAmB,CAAC;MACrF;MACA,SAAW;MACX;MAAW,OAAO9L,MAAM,CAAC2G,OAAO;MAChC;IAAU;IACV;IACA;IACA;IACA,SAAU;IACV,SAAU;IACV,SAAU;IACV;IAAU,IAAI+W,mBAAmB,GAAG5R,mBAAmB,CAAC,EAAE,CAAC;IAC3D;IAAUhM,oBAAoB,GAAG4d,mBAAmB;IACpD;IACA;EAAS,CAAC,EAAE,CAAC;EAEb,IAAIzL,UAAU,GAAGnS,oBAAoB;EACrC;EACA,IAAI,OAAO6d,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IAC5C;IACAD,MAAM,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE,gBAAgB,CAAC,EAAE,UAASE,UAAU,EAAE;MACrE,IAAI1L,WAAW,GAAG0L,UAAU,CAAC,YAAY,CAAC;MAC1C,IAAIzL,YAAY,GAAGyL,UAAU,CAAC,gBAAgB,CAAC;MAE/C,OAAO;QACHC,aAAa,EAAE,SAAAA,CAAS5L,WAAW,EAAE9O,OAAO,EAAE;UAC1C,OAAO6O,UAAU,CAACC,WAAW,EAAE9O,OAAO,EAAE+O,WAAW,CAACA,WAAW,EAAEC,YAAY,CAACA,YAAY,CAAC;QAC/F;MACJ,CAAC;IACL,CAAC,CAAC;EACN,CAAC,MAAM,IAAI,OAAOzL,OAAO,KAAK,WAAW,EAAE;IACvC;IACA;IACA,IAAIwL,WAAW,GAAG4L,OAAO,CAAC,eAAe,CAAC;IAC1C,IAAI3L,YAAY,GAAG2L,OAAO,CAAC,mBAAmB,CAAC;IAE/CpX,OAAO,CAACmX,aAAa,GAAG,UAAS5L,WAAW,EAAE9O,OAAO,EAAE;MACnD,OAAO6O,UAAU,CAACC,WAAW,EAAE9O,OAAO,EAAE+O,WAAW,CAACA,WAAW,EAAEC,YAAY,CAACA,YAAY,CAAC;IAC/F,CAAC;EACL,CAAC,MAAM,IAAI,OAAO4L,MAAM,KAAK,WAAW,EAAE;IACtC;IACAA,MAAM,CAACF,aAAa,GAAG,UAAS5L,WAAW,EAAE9O,OAAO,EAAE;MAClD,OAAO6O,UAAU,CAACC,WAAW,EAAE9O,OAAO,EAAE4a,MAAM,CAAC7L,WAAW,EAAE6L,MAAM,CAAC5L,YAAY,CAAC;IACpF,CAAC;EACL,CAAC,MAAM,IAAI,OAAO6L,MAAM,KAAK,WAAW,EAAE;IACtC;IACAA,MAAM,CAACH,aAAa,GAAG,UAAS5L,WAAW,EAAE9O,OAAO,EAAE;MAClD,OAAO6O,UAAU,CAACC,WAAW,EAAE9O,OAAO,EAAE6a,MAAM,CAAC9L,WAAW,EAAE8L,MAAM,CAAC7L,YAAY,CAAC;IACpF,CAAC;EACL;AAEA,CAAC,EAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}