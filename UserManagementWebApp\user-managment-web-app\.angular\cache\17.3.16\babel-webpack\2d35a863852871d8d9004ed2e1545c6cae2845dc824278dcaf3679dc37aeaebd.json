{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { handleUserPasswordAuthFlow, getActiveSignInUsername, getSignInResult, getSignInResultFromError } from '../utils/signInHelpers.mjs';\nimport { setActiveSignInState, resetActiveSignInState } from '../../../client/utils/store/signInStore.mjs';\nimport { cacheCognitoTokens } from '../tokenProvider/cacheTokens.mjs';\nimport '../utils/refreshAuthTokens.mjs';\nimport '../tokenProvider/errorHelpers.mjs';\nimport '../utils/types.mjs';\nimport { tokenOrchestrator } from '../tokenProvider/tokenProvider.mjs';\nimport { dispatchSignedInHubEvent } from '../utils/dispatchSignedInHubEvent.mjs';\nimport { retryOnResourceNotFoundException } from '../utils/retryOnResourceNotFoundException.mjs';\nimport { getNewDeviceMetadata } from '../utils/getNewDeviceMetadata.mjs';\nimport { resetAutoSignIn } from './autoSignIn.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Signs a user in using USER_PASSWORD_AUTH AuthFlowType\n *\n * @param input - The SignInWithUserPasswordInput object\n * @returns SignInWithUserPasswordOutput\n * @throws service: {@link InitiateAuthException } - Cognito service error thrown during the sign-in process.\n * @throws validation: {@link AuthValidationErrorCode  } - Validation errors thrown when either username or password\n *  are not defined.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nfunction signInWithUserPassword(_x) {\n  return _signInWithUserPassword.apply(this, arguments);\n}\nfunction _signInWithUserPassword() {\n  _signInWithUserPassword = _asyncToGenerator(function* (input) {\n    const {\n      username,\n      password,\n      options\n    } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    const signInDetails = {\n      loginId: username,\n      authFlowType: 'USER_PASSWORD_AUTH'\n    };\n    assertTokenProviderConfig(authConfig);\n    const metadata = options?.clientMetadata;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptySignInUsername);\n    assertValidationError(!!password, AuthValidationErrorCode.EmptySignInPassword);\n    try {\n      const {\n        ChallengeName: retiredChallengeName,\n        ChallengeParameters: retriedChallengeParameters,\n        AuthenticationResult,\n        Session\n      } = yield retryOnResourceNotFoundException(handleUserPasswordAuthFlow, [username, password, metadata, authConfig, tokenOrchestrator], username, tokenOrchestrator);\n      const activeUsername = getActiveSignInUsername(username);\n      // sets up local state used during the sign-in process\n      setActiveSignInState({\n        signInSession: Session,\n        username: activeUsername,\n        challengeName: retiredChallengeName,\n        signInDetails\n      });\n      if (AuthenticationResult) {\n        yield cacheCognitoTokens({\n          ...AuthenticationResult,\n          username: activeUsername,\n          NewDeviceMetadata: yield getNewDeviceMetadata({\n            userPoolId: authConfig.userPoolId,\n            userPoolEndpoint: authConfig.userPoolEndpoint,\n            newDeviceMetadata: AuthenticationResult.NewDeviceMetadata,\n            accessToken: AuthenticationResult.AccessToken\n          }),\n          signInDetails\n        });\n        resetActiveSignInState();\n        yield dispatchSignedInHubEvent();\n        resetAutoSignIn();\n        return {\n          isSignedIn: true,\n          nextStep: {\n            signInStep: 'DONE'\n          }\n        };\n      }\n      return getSignInResult({\n        challengeName: retiredChallengeName,\n        challengeParameters: retriedChallengeParameters\n      });\n    } catch (error) {\n      resetActiveSignInState();\n      resetAutoSignIn();\n      assertServiceError(error);\n      const result = getSignInResultFromError(error.name);\n      if (result) return result;\n      throw error;\n    }\n  });\n  return _signInWithUserPassword.apply(this, arguments);\n}\nexport { signInWithUserPassword };", "map": {"version": 3, "names": ["Amplify", "assertTokenProviderConfig", "AuthValidationErrorCode", "assertServiceError", "assertValidationError", "handleUserPasswordAuthFlow", "getActiveSignInUsername", "getSignInResult", "getSignInResultFromError", "setActiveSignInState", "resetActiveSignInState", "cacheCognitoTokens", "tokenOrchestrator", "dispatchSignedInHubEvent", "retryOnResourceNotFoundException", "getNewDeviceMetadata", "resetAutoSignIn", "signInWithUserPassword", "_x", "_signInWithUserPassword", "apply", "arguments", "_asyncToGenerator", "input", "username", "password", "options", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "signInDetails", "loginId", "authFlowType", "metadata", "clientMetadata", "EmptySignInUsername", "EmptySignInPassword", "ChallengeName", "retiredC<PERSON><PERSON><PERSON><PERSON><PERSON>", "ChallengeParameters", "retriedChallengeParameters", "AuthenticationResult", "Session", "activeUsername", "signInSession", "challenge<PERSON>ame", "NewDeviceMetadata", "userPoolId", "userPoolEndpoint", "newDeviceMetadata", "accessToken", "AccessToken", "isSignedIn", "nextStep", "signInStep", "challengeParameters", "error", "result", "name"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/signInWithUserPassword.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { handleUserPasswordAuthFlow, getActiveSignInUsername, getSignInResult, getSignInResultFromError } from '../utils/signInHelpers.mjs';\nimport { setActiveSignInState, resetActiveSignInState } from '../../../client/utils/store/signInStore.mjs';\nimport { cacheCognitoTokens } from '../tokenProvider/cacheTokens.mjs';\nimport '../utils/refreshAuthTokens.mjs';\nimport '../tokenProvider/errorHelpers.mjs';\nimport '../utils/types.mjs';\nimport { tokenOrchestrator } from '../tokenProvider/tokenProvider.mjs';\nimport { dispatchSignedInHubEvent } from '../utils/dispatchSignedInHubEvent.mjs';\nimport { retryOnResourceNotFoundException } from '../utils/retryOnResourceNotFoundException.mjs';\nimport { getNewDeviceMetadata } from '../utils/getNewDeviceMetadata.mjs';\nimport { resetAutoSignIn } from './autoSignIn.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Signs a user in using USER_PASSWORD_AUTH AuthFlowType\n *\n * @param input - The SignInWithUserPasswordInput object\n * @returns SignInWithUserPasswordOutput\n * @throws service: {@link InitiateAuthException } - Cognito service error thrown during the sign-in process.\n * @throws validation: {@link AuthValidationErrorCode  } - Validation errors thrown when either username or password\n *  are not defined.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nasync function signInWithUserPassword(input) {\n    const { username, password, options } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    const signInDetails = {\n        loginId: username,\n        authFlowType: 'USER_PASSWORD_AUTH',\n    };\n    assertTokenProviderConfig(authConfig);\n    const metadata = options?.clientMetadata;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptySignInUsername);\n    assertValidationError(!!password, AuthValidationErrorCode.EmptySignInPassword);\n    try {\n        const { ChallengeName: retiredChallengeName, ChallengeParameters: retriedChallengeParameters, AuthenticationResult, Session, } = await retryOnResourceNotFoundException(handleUserPasswordAuthFlow, [username, password, metadata, authConfig, tokenOrchestrator], username, tokenOrchestrator);\n        const activeUsername = getActiveSignInUsername(username);\n        // sets up local state used during the sign-in process\n        setActiveSignInState({\n            signInSession: Session,\n            username: activeUsername,\n            challengeName: retiredChallengeName,\n            signInDetails,\n        });\n        if (AuthenticationResult) {\n            await cacheCognitoTokens({\n                ...AuthenticationResult,\n                username: activeUsername,\n                NewDeviceMetadata: await getNewDeviceMetadata({\n                    userPoolId: authConfig.userPoolId,\n                    userPoolEndpoint: authConfig.userPoolEndpoint,\n                    newDeviceMetadata: AuthenticationResult.NewDeviceMetadata,\n                    accessToken: AuthenticationResult.AccessToken,\n                }),\n                signInDetails,\n            });\n            resetActiveSignInState();\n            await dispatchSignedInHubEvent();\n            resetAutoSignIn();\n            return {\n                isSignedIn: true,\n                nextStep: { signInStep: 'DONE' },\n            };\n        }\n        return getSignInResult({\n            challengeName: retiredChallengeName,\n            challengeParameters: retriedChallengeParameters,\n        });\n    }\n    catch (error) {\n        resetActiveSignInState();\n        resetAutoSignIn();\n        assertServiceError(error);\n        const result = getSignInResultFromError(error.name);\n        if (result)\n            return result;\n        throw error;\n    }\n}\n\nexport { signInWithUserPassword };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,yBAAyB,QAAQ,mCAAmC;AAC7E,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,0BAA0B,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,wBAAwB,QAAQ,4BAA4B;AAC3I,SAASC,oBAAoB,EAAEC,sBAAsB,QAAQ,6CAA6C;AAC1G,SAASC,kBAAkB,QAAQ,kCAAkC;AACrE,OAAO,gCAAgC;AACvC,OAAO,mCAAmC;AAC1C,OAAO,oBAAoB;AAC3B,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,SAASC,wBAAwB,QAAQ,uCAAuC;AAChF,SAASC,gCAAgC,QAAQ,+CAA+C;AAChG,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,eAAe,QAAQ,kBAAkB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,SAUeC,sBAAsBA,CAAAC,EAAA;EAAA,OAAAC,uBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,wBAAA;EAAAA,uBAAA,GAAAG,iBAAA,CAArC,WAAsCC,KAAK,EAAE;IACzC,MAAM;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC;IAAQ,CAAC,GAAGH,KAAK;IAC7C,MAAMI,UAAU,GAAG3B,OAAO,CAAC4B,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpD,MAAMC,aAAa,GAAG;MAClBC,OAAO,EAAER,QAAQ;MACjBS,YAAY,EAAE;IAClB,CAAC;IACDhC,yBAAyB,CAAC0B,UAAU,CAAC;IACrC,MAAMO,QAAQ,GAAGR,OAAO,EAAES,cAAc;IACxC/B,qBAAqB,CAAC,CAAC,CAACoB,QAAQ,EAAEtB,uBAAuB,CAACkC,mBAAmB,CAAC;IAC9EhC,qBAAqB,CAAC,CAAC,CAACqB,QAAQ,EAAEvB,uBAAuB,CAACmC,mBAAmB,CAAC;IAC9E,IAAI;MACA,MAAM;QAAEC,aAAa,EAAEC,oBAAoB;QAAEC,mBAAmB,EAAEC,0BAA0B;QAAEC,oBAAoB;QAAEC;MAAS,CAAC,SAAS7B,gCAAgC,CAACT,0BAA0B,EAAE,CAACmB,QAAQ,EAAEC,QAAQ,EAAES,QAAQ,EAAEP,UAAU,EAAEf,iBAAiB,CAAC,EAAEY,QAAQ,EAAEZ,iBAAiB,CAAC;MAC/R,MAAMgC,cAAc,GAAGtC,uBAAuB,CAACkB,QAAQ,CAAC;MACxD;MACAf,oBAAoB,CAAC;QACjBoC,aAAa,EAAEF,OAAO;QACtBnB,QAAQ,EAAEoB,cAAc;QACxBE,aAAa,EAAEP,oBAAoB;QACnCR;MACJ,CAAC,CAAC;MACF,IAAIW,oBAAoB,EAAE;QACtB,MAAM/B,kBAAkB,CAAC;UACrB,GAAG+B,oBAAoB;UACvBlB,QAAQ,EAAEoB,cAAc;UACxBG,iBAAiB,QAAQhC,oBAAoB,CAAC;YAC1CiC,UAAU,EAAErB,UAAU,CAACqB,UAAU;YACjCC,gBAAgB,EAAEtB,UAAU,CAACsB,gBAAgB;YAC7CC,iBAAiB,EAAER,oBAAoB,CAACK,iBAAiB;YACzDI,WAAW,EAAET,oBAAoB,CAACU;UACtC,CAAC,CAAC;UACFrB;QACJ,CAAC,CAAC;QACFrB,sBAAsB,CAAC,CAAC;QACxB,MAAMG,wBAAwB,CAAC,CAAC;QAChCG,eAAe,CAAC,CAAC;QACjB,OAAO;UACHqC,UAAU,EAAE,IAAI;UAChBC,QAAQ,EAAE;YAAEC,UAAU,EAAE;UAAO;QACnC,CAAC;MACL;MACA,OAAOhD,eAAe,CAAC;QACnBuC,aAAa,EAAEP,oBAAoB;QACnCiB,mBAAmB,EAAEf;MACzB,CAAC,CAAC;IACN,CAAC,CACD,OAAOgB,KAAK,EAAE;MACV/C,sBAAsB,CAAC,CAAC;MACxBM,eAAe,CAAC,CAAC;MACjBb,kBAAkB,CAACsD,KAAK,CAAC;MACzB,MAAMC,MAAM,GAAGlD,wBAAwB,CAACiD,KAAK,CAACE,IAAI,CAAC;MACnD,IAAID,MAAM,EACN,OAAOA,MAAM;MACjB,MAAMD,KAAK;IACf;EACJ,CAAC;EAAA,OAAAtC,uBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}