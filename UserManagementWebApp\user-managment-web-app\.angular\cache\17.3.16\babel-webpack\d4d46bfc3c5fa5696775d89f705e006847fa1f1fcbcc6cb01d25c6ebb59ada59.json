{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { decodeJWT, assertTokenProviderConfig } from '@aws-amplify/core/internals/utils';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { AuthTokenStorageKeys } from './types.mjs';\nimport { assert, TokenProviderErrorCode } from './errorHelpers.mjs';\nimport { AUTH_KEY_PREFIX } from './constants.mjs';\nclass DefaultTokenStore {\n  getKeyValueStorage() {\n    if (!this.keyValueStorage) {\n      throw new AuthError({\n        name: 'KeyValueStorageNotFoundException',\n        message: 'KeyValueStorage was not found in TokenStore'\n      });\n    }\n    return this.keyValueStorage;\n  }\n  setKeyValueStorage(keyValueStorage) {\n    this.keyValueStorage = keyValueStorage;\n  }\n  setAuthConfig(authConfig) {\n    this.authConfig = authConfig;\n  }\n  loadTokens() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // TODO(v6): migration logic should be here\n      // Reading V5 tokens old format\n      try {\n        const authKeys = yield _this.getAuthKeys();\n        const accessTokenString = yield _this.getKeyValueStorage().getItem(authKeys.accessToken);\n        if (!accessTokenString) {\n          throw new AuthError({\n            name: 'NoSessionFoundException',\n            message: 'Auth session was not found. Make sure to call signIn.'\n          });\n        }\n        const accessToken = decodeJWT(accessTokenString);\n        const itString = yield _this.getKeyValueStorage().getItem(authKeys.idToken);\n        const idToken = itString ? decodeJWT(itString) : undefined;\n        const refreshToken = (yield _this.getKeyValueStorage().getItem(authKeys.refreshToken)) ?? undefined;\n        const clockDriftString = (yield _this.getKeyValueStorage().getItem(authKeys.clockDrift)) ?? '0';\n        const clockDrift = Number.parseInt(clockDriftString);\n        const signInDetails = yield _this.getKeyValueStorage().getItem(authKeys.signInDetails);\n        const tokens = {\n          accessToken,\n          idToken,\n          refreshToken,\n          deviceMetadata: (yield _this.getDeviceMetadata()) ?? undefined,\n          clockDrift,\n          username: yield _this.getLastAuthUser()\n        };\n        if (signInDetails) {\n          tokens.signInDetails = JSON.parse(signInDetails);\n        }\n        return tokens;\n      } catch (err) {\n        return null;\n      }\n    })();\n  }\n  storeTokens(tokens) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      assert(tokens !== undefined, TokenProviderErrorCode.InvalidAuthTokens);\n      const lastAuthUser = tokens.username;\n      yield _this2.getKeyValueStorage().setItem(_this2.getLastAuthUserKey(), lastAuthUser);\n      const authKeys = yield _this2.getAuthKeys();\n      yield _this2.getKeyValueStorage().setItem(authKeys.accessToken, tokens.accessToken.toString());\n      if (tokens.idToken) {\n        yield _this2.getKeyValueStorage().setItem(authKeys.idToken, tokens.idToken.toString());\n      } else {\n        yield _this2.getKeyValueStorage().removeItem(authKeys.idToken);\n      }\n      if (tokens.refreshToken) {\n        yield _this2.getKeyValueStorage().setItem(authKeys.refreshToken, tokens.refreshToken);\n      } else {\n        yield _this2.getKeyValueStorage().removeItem(authKeys.refreshToken);\n      }\n      if (tokens.deviceMetadata) {\n        if (tokens.deviceMetadata.deviceKey) {\n          yield _this2.getKeyValueStorage().setItem(authKeys.deviceKey, tokens.deviceMetadata.deviceKey);\n        }\n        if (tokens.deviceMetadata.deviceGroupKey) {\n          yield _this2.getKeyValueStorage().setItem(authKeys.deviceGroupKey, tokens.deviceMetadata.deviceGroupKey);\n        }\n        yield _this2.getKeyValueStorage().setItem(authKeys.randomPasswordKey, tokens.deviceMetadata.randomPassword);\n      }\n      if (tokens.signInDetails) {\n        yield _this2.getKeyValueStorage().setItem(authKeys.signInDetails, JSON.stringify(tokens.signInDetails));\n      } else {\n        yield _this2.getKeyValueStorage().removeItem(authKeys.signInDetails);\n      }\n      yield _this2.getKeyValueStorage().setItem(authKeys.clockDrift, `${tokens.clockDrift}`);\n    })();\n  }\n  clearTokens() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const authKeys = yield _this3.getAuthKeys();\n      // Not calling clear because it can remove data that is not managed by AuthTokenStore\n      yield Promise.all([_this3.getKeyValueStorage().removeItem(authKeys.accessToken), _this3.getKeyValueStorage().removeItem(authKeys.idToken), _this3.getKeyValueStorage().removeItem(authKeys.clockDrift), _this3.getKeyValueStorage().removeItem(authKeys.refreshToken), _this3.getKeyValueStorage().removeItem(authKeys.signInDetails), _this3.getKeyValueStorage().removeItem(_this3.getLastAuthUserKey()), _this3.getKeyValueStorage().removeItem(authKeys.oauthMetadata)]);\n    })();\n  }\n  getDeviceMetadata(username) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const authKeys = yield _this4.getAuthKeys(username);\n      const deviceKey = yield _this4.getKeyValueStorage().getItem(authKeys.deviceKey);\n      const deviceGroupKey = yield _this4.getKeyValueStorage().getItem(authKeys.deviceGroupKey);\n      const randomPassword = yield _this4.getKeyValueStorage().getItem(authKeys.randomPasswordKey);\n      return randomPassword && deviceGroupKey && deviceKey ? {\n        deviceKey,\n        deviceGroupKey,\n        randomPassword\n      } : null;\n    })();\n  }\n  clearDeviceMetadata(username) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const authKeys = yield _this5.getAuthKeys(username);\n      yield Promise.all([_this5.getKeyValueStorage().removeItem(authKeys.deviceKey), _this5.getKeyValueStorage().removeItem(authKeys.deviceGroupKey), _this5.getKeyValueStorage().removeItem(authKeys.randomPasswordKey)]);\n    })();\n  }\n  getAuthKeys(username) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      assertTokenProviderConfig(_this6.authConfig?.Cognito);\n      const lastAuthUser = username ?? (yield _this6.getLastAuthUser());\n      return createKeysForAuthStorage(AUTH_KEY_PREFIX, `${_this6.authConfig.Cognito.userPoolClientId}.${lastAuthUser}`);\n    })();\n  }\n  getLastAuthUserKey() {\n    assertTokenProviderConfig(this.authConfig?.Cognito);\n    const identifier = this.authConfig.Cognito.userPoolClientId;\n    return `${AUTH_KEY_PREFIX}.${identifier}.LastAuthUser`;\n  }\n  getLastAuthUser() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const lastAuthUser = (yield _this7.getKeyValueStorage().getItem(_this7.getLastAuthUserKey())) ?? 'username';\n      return lastAuthUser;\n    })();\n  }\n  setOAuthMetadata(metadata) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        oauthMetadata: oauthMetadataKey\n      } = yield _this8.getAuthKeys();\n      yield _this8.getKeyValueStorage().setItem(oauthMetadataKey, JSON.stringify(metadata));\n    })();\n  }\n  getOAuthMetadata() {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        oauthMetadata: oauthMetadataKey\n      } = yield _this9.getAuthKeys();\n      const oauthMetadata = yield _this9.getKeyValueStorage().getItem(oauthMetadataKey);\n      return oauthMetadata && JSON.parse(oauthMetadata);\n    })();\n  }\n}\nconst createKeysForAuthStorage = (provider, identifier) => {\n  return getAuthStorageKeys(AuthTokenStorageKeys)(`${provider}`, identifier);\n};\nfunction getAuthStorageKeys(authKeys) {\n  const keys = Object.values({\n    ...authKeys\n  });\n  return (prefix, identifier) => keys.reduce((acc, authKey) => ({\n    ...acc,\n    [authKey]: `${prefix}.${identifier}.${authKey}`\n  }), {});\n}\nexport { DefaultTokenStore, createKeysForAuthStorage, getAuthStorageKeys };", "map": {"version": 3, "names": ["decodeJWT", "assertTokenProviderConfig", "<PERSON>th<PERSON><PERSON><PERSON>", "AuthTokenStorageKeys", "assert", "TokenProviderErrorCode", "AUTH_KEY_PREFIX", "DefaultTokenStore", "getKeyValueStorage", "keyValueStorage", "name", "message", "setKeyValueStorage", "setAuthConfig", "authConfig", "loadTokens", "_this", "_asyncToGenerator", "auth<PERSON><PERSON><PERSON>", "getAuth<PERSON>eys", "accessTokenString", "getItem", "accessToken", "itString", "idToken", "undefined", "refreshToken", "clockDriftString", "clockDrift", "Number", "parseInt", "signInDetails", "tokens", "deviceMetadata", "getDeviceMetadata", "username", "getLastAuthUser", "JSON", "parse", "err", "storeTokens", "_this2", "InvalidAuthTokens", "lastAuthUser", "setItem", "getLastAuthUserKey", "toString", "removeItem", "deviceKey", "deviceGroupKey", "randomPassword<PERSON>ey", "randomPassword", "stringify", "clearTokens", "_this3", "Promise", "all", "oauthMetadata", "_this4", "clearDeviceMetadata", "_this5", "_this6", "Cognito", "createKeysForAuthStorage", "userPoolClientId", "identifier", "_this7", "setOAuthMetadata", "metadata", "_this8", "oauthMetadataKey", "getOAuthMetadata", "_this9", "provider", "getAuthStorageKeys", "keys", "Object", "values", "prefix", "reduce", "acc", "auth<PERSON><PERSON>"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/tokenProvider/TokenStore.mjs"], "sourcesContent": ["import { decodeJWT, assertTokenProviderConfig } from '@aws-amplify/core/internals/utils';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { AuthTokenStorageKeys } from './types.mjs';\nimport { assert, TokenProviderErrorCode } from './errorHelpers.mjs';\nimport { AUTH_KEY_PREFIX } from './constants.mjs';\n\nclass DefaultTokenStore {\n    getKeyValueStorage() {\n        if (!this.keyValueStorage) {\n            throw new AuthError({\n                name: 'KeyValueStorageNotFoundException',\n                message: 'KeyValueStorage was not found in TokenStore',\n            });\n        }\n        return this.keyValueStorage;\n    }\n    setKeyValueStorage(keyValueStorage) {\n        this.keyValueStorage = keyValueStorage;\n    }\n    setAuthConfig(authConfig) {\n        this.authConfig = authConfig;\n    }\n    async loadTokens() {\n        // TODO(v6): migration logic should be here\n        // Reading V5 tokens old format\n        try {\n            const authKeys = await this.getAuthKeys();\n            const accessTokenString = await this.getKeyValueStorage().getItem(authKeys.accessToken);\n            if (!accessTokenString) {\n                throw new AuthError({\n                    name: 'NoSessionFoundException',\n                    message: 'Auth session was not found. Make sure to call signIn.',\n                });\n            }\n            const accessToken = decodeJWT(accessTokenString);\n            const itString = await this.getKeyValueStorage().getItem(authKeys.idToken);\n            const idToken = itString ? decodeJWT(itString) : undefined;\n            const refreshToken = (await this.getKeyValueStorage().getItem(authKeys.refreshToken)) ??\n                undefined;\n            const clockDriftString = (await this.getKeyValueStorage().getItem(authKeys.clockDrift)) ?? '0';\n            const clockDrift = Number.parseInt(clockDriftString);\n            const signInDetails = await this.getKeyValueStorage().getItem(authKeys.signInDetails);\n            const tokens = {\n                accessToken,\n                idToken,\n                refreshToken,\n                deviceMetadata: (await this.getDeviceMetadata()) ?? undefined,\n                clockDrift,\n                username: await this.getLastAuthUser(),\n            };\n            if (signInDetails) {\n                tokens.signInDetails = JSON.parse(signInDetails);\n            }\n            return tokens;\n        }\n        catch (err) {\n            return null;\n        }\n    }\n    async storeTokens(tokens) {\n        assert(tokens !== undefined, TokenProviderErrorCode.InvalidAuthTokens);\n        const lastAuthUser = tokens.username;\n        await this.getKeyValueStorage().setItem(this.getLastAuthUserKey(), lastAuthUser);\n        const authKeys = await this.getAuthKeys();\n        await this.getKeyValueStorage().setItem(authKeys.accessToken, tokens.accessToken.toString());\n        if (tokens.idToken) {\n            await this.getKeyValueStorage().setItem(authKeys.idToken, tokens.idToken.toString());\n        }\n        else {\n            await this.getKeyValueStorage().removeItem(authKeys.idToken);\n        }\n        if (tokens.refreshToken) {\n            await this.getKeyValueStorage().setItem(authKeys.refreshToken, tokens.refreshToken);\n        }\n        else {\n            await this.getKeyValueStorage().removeItem(authKeys.refreshToken);\n        }\n        if (tokens.deviceMetadata) {\n            if (tokens.deviceMetadata.deviceKey) {\n                await this.getKeyValueStorage().setItem(authKeys.deviceKey, tokens.deviceMetadata.deviceKey);\n            }\n            if (tokens.deviceMetadata.deviceGroupKey) {\n                await this.getKeyValueStorage().setItem(authKeys.deviceGroupKey, tokens.deviceMetadata.deviceGroupKey);\n            }\n            await this.getKeyValueStorage().setItem(authKeys.randomPasswordKey, tokens.deviceMetadata.randomPassword);\n        }\n        if (tokens.signInDetails) {\n            await this.getKeyValueStorage().setItem(authKeys.signInDetails, JSON.stringify(tokens.signInDetails));\n        }\n        else {\n            await this.getKeyValueStorage().removeItem(authKeys.signInDetails);\n        }\n        await this.getKeyValueStorage().setItem(authKeys.clockDrift, `${tokens.clockDrift}`);\n    }\n    async clearTokens() {\n        const authKeys = await this.getAuthKeys();\n        // Not calling clear because it can remove data that is not managed by AuthTokenStore\n        await Promise.all([\n            this.getKeyValueStorage().removeItem(authKeys.accessToken),\n            this.getKeyValueStorage().removeItem(authKeys.idToken),\n            this.getKeyValueStorage().removeItem(authKeys.clockDrift),\n            this.getKeyValueStorage().removeItem(authKeys.refreshToken),\n            this.getKeyValueStorage().removeItem(authKeys.signInDetails),\n            this.getKeyValueStorage().removeItem(this.getLastAuthUserKey()),\n            this.getKeyValueStorage().removeItem(authKeys.oauthMetadata),\n        ]);\n    }\n    async getDeviceMetadata(username) {\n        const authKeys = await this.getAuthKeys(username);\n        const deviceKey = await this.getKeyValueStorage().getItem(authKeys.deviceKey);\n        const deviceGroupKey = await this.getKeyValueStorage().getItem(authKeys.deviceGroupKey);\n        const randomPassword = await this.getKeyValueStorage().getItem(authKeys.randomPasswordKey);\n        return randomPassword && deviceGroupKey && deviceKey\n            ? {\n                deviceKey,\n                deviceGroupKey,\n                randomPassword,\n            }\n            : null;\n    }\n    async clearDeviceMetadata(username) {\n        const authKeys = await this.getAuthKeys(username);\n        await Promise.all([\n            this.getKeyValueStorage().removeItem(authKeys.deviceKey),\n            this.getKeyValueStorage().removeItem(authKeys.deviceGroupKey),\n            this.getKeyValueStorage().removeItem(authKeys.randomPasswordKey),\n        ]);\n    }\n    async getAuthKeys(username) {\n        assertTokenProviderConfig(this.authConfig?.Cognito);\n        const lastAuthUser = username ?? (await this.getLastAuthUser());\n        return createKeysForAuthStorage(AUTH_KEY_PREFIX, `${this.authConfig.Cognito.userPoolClientId}.${lastAuthUser}`);\n    }\n    getLastAuthUserKey() {\n        assertTokenProviderConfig(this.authConfig?.Cognito);\n        const identifier = this.authConfig.Cognito.userPoolClientId;\n        return `${AUTH_KEY_PREFIX}.${identifier}.LastAuthUser`;\n    }\n    async getLastAuthUser() {\n        const lastAuthUser = (await this.getKeyValueStorage().getItem(this.getLastAuthUserKey())) ??\n            'username';\n        return lastAuthUser;\n    }\n    async setOAuthMetadata(metadata) {\n        const { oauthMetadata: oauthMetadataKey } = await this.getAuthKeys();\n        await this.getKeyValueStorage().setItem(oauthMetadataKey, JSON.stringify(metadata));\n    }\n    async getOAuthMetadata() {\n        const { oauthMetadata: oauthMetadataKey } = await this.getAuthKeys();\n        const oauthMetadata = await this.getKeyValueStorage().getItem(oauthMetadataKey);\n        return oauthMetadata && JSON.parse(oauthMetadata);\n    }\n}\nconst createKeysForAuthStorage = (provider, identifier) => {\n    return getAuthStorageKeys(AuthTokenStorageKeys)(`${provider}`, identifier);\n};\nfunction getAuthStorageKeys(authKeys) {\n    const keys = Object.values({ ...authKeys });\n    return (prefix, identifier) => keys.reduce((acc, authKey) => ({\n        ...acc,\n        [authKey]: `${prefix}.${identifier}.${authKey}`,\n    }), {});\n}\n\nexport { DefaultTokenStore, createKeysForAuthStorage, getAuthStorageKeys };\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,yBAAyB,QAAQ,mCAAmC;AACxF,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,oBAAoB,QAAQ,aAAa;AAClD,SAASC,MAAM,EAAEC,sBAAsB,QAAQ,oBAAoB;AACnE,SAASC,eAAe,QAAQ,iBAAiB;AAEjD,MAAMC,iBAAiB,CAAC;EACpBC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MACvB,MAAM,IAAIP,SAAS,CAAC;QAChBQ,IAAI,EAAE,kCAAkC;QACxCC,OAAO,EAAE;MACb,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAACF,eAAe;EAC/B;EACAG,kBAAkBA,CAACH,eAAe,EAAE;IAChC,IAAI,CAACA,eAAe,GAAGA,eAAe;EAC1C;EACAI,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACMC,UAAUA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACf;MACA;MACA,IAAI;QACA,MAAMC,QAAQ,SAASF,KAAI,CAACG,WAAW,CAAC,CAAC;QACzC,MAAMC,iBAAiB,SAASJ,KAAI,CAACR,kBAAkB,CAAC,CAAC,CAACa,OAAO,CAACH,QAAQ,CAACI,WAAW,CAAC;QACvF,IAAI,CAACF,iBAAiB,EAAE;UACpB,MAAM,IAAIlB,SAAS,CAAC;YAChBQ,IAAI,EAAE,yBAAyB;YAC/BC,OAAO,EAAE;UACb,CAAC,CAAC;QACN;QACA,MAAMW,WAAW,GAAGtB,SAAS,CAACoB,iBAAiB,CAAC;QAChD,MAAMG,QAAQ,SAASP,KAAI,CAACR,kBAAkB,CAAC,CAAC,CAACa,OAAO,CAACH,QAAQ,CAACM,OAAO,CAAC;QAC1E,MAAMA,OAAO,GAAGD,QAAQ,GAAGvB,SAAS,CAACuB,QAAQ,CAAC,GAAGE,SAAS;QAC1D,MAAMC,YAAY,GAAG,OAAOV,KAAI,CAACR,kBAAkB,CAAC,CAAC,CAACa,OAAO,CAACH,QAAQ,CAACQ,YAAY,CAAC,KAChFD,SAAS;QACb,MAAME,gBAAgB,GAAG,OAAOX,KAAI,CAACR,kBAAkB,CAAC,CAAC,CAACa,OAAO,CAACH,QAAQ,CAACU,UAAU,CAAC,KAAK,GAAG;QAC9F,MAAMA,UAAU,GAAGC,MAAM,CAACC,QAAQ,CAACH,gBAAgB,CAAC;QACpD,MAAMI,aAAa,SAASf,KAAI,CAACR,kBAAkB,CAAC,CAAC,CAACa,OAAO,CAACH,QAAQ,CAACa,aAAa,CAAC;QACrF,MAAMC,MAAM,GAAG;UACXV,WAAW;UACXE,OAAO;UACPE,YAAY;UACZO,cAAc,EAAE,OAAOjB,KAAI,CAACkB,iBAAiB,CAAC,CAAC,KAAKT,SAAS;UAC7DG,UAAU;UACVO,QAAQ,QAAQnB,KAAI,CAACoB,eAAe,CAAC;QACzC,CAAC;QACD,IAAIL,aAAa,EAAE;UACfC,MAAM,CAACD,aAAa,GAAGM,IAAI,CAACC,KAAK,CAACP,aAAa,CAAC;QACpD;QACA,OAAOC,MAAM;MACjB,CAAC,CACD,OAAOO,GAAG,EAAE;QACR,OAAO,IAAI;MACf;IAAC;EACL;EACMC,WAAWA,CAACR,MAAM,EAAE;IAAA,IAAAS,MAAA;IAAA,OAAAxB,iBAAA;MACtBb,MAAM,CAAC4B,MAAM,KAAKP,SAAS,EAAEpB,sBAAsB,CAACqC,iBAAiB,CAAC;MACtE,MAAMC,YAAY,GAAGX,MAAM,CAACG,QAAQ;MACpC,MAAMM,MAAI,CAACjC,kBAAkB,CAAC,CAAC,CAACoC,OAAO,CAACH,MAAI,CAACI,kBAAkB,CAAC,CAAC,EAAEF,YAAY,CAAC;MAChF,MAAMzB,QAAQ,SAASuB,MAAI,CAACtB,WAAW,CAAC,CAAC;MACzC,MAAMsB,MAAI,CAACjC,kBAAkB,CAAC,CAAC,CAACoC,OAAO,CAAC1B,QAAQ,CAACI,WAAW,EAAEU,MAAM,CAACV,WAAW,CAACwB,QAAQ,CAAC,CAAC,CAAC;MAC5F,IAAId,MAAM,CAACR,OAAO,EAAE;QAChB,MAAMiB,MAAI,CAACjC,kBAAkB,CAAC,CAAC,CAACoC,OAAO,CAAC1B,QAAQ,CAACM,OAAO,EAAEQ,MAAM,CAACR,OAAO,CAACsB,QAAQ,CAAC,CAAC,CAAC;MACxF,CAAC,MACI;QACD,MAAML,MAAI,CAACjC,kBAAkB,CAAC,CAAC,CAACuC,UAAU,CAAC7B,QAAQ,CAACM,OAAO,CAAC;MAChE;MACA,IAAIQ,MAAM,CAACN,YAAY,EAAE;QACrB,MAAMe,MAAI,CAACjC,kBAAkB,CAAC,CAAC,CAACoC,OAAO,CAAC1B,QAAQ,CAACQ,YAAY,EAAEM,MAAM,CAACN,YAAY,CAAC;MACvF,CAAC,MACI;QACD,MAAMe,MAAI,CAACjC,kBAAkB,CAAC,CAAC,CAACuC,UAAU,CAAC7B,QAAQ,CAACQ,YAAY,CAAC;MACrE;MACA,IAAIM,MAAM,CAACC,cAAc,EAAE;QACvB,IAAID,MAAM,CAACC,cAAc,CAACe,SAAS,EAAE;UACjC,MAAMP,MAAI,CAACjC,kBAAkB,CAAC,CAAC,CAACoC,OAAO,CAAC1B,QAAQ,CAAC8B,SAAS,EAAEhB,MAAM,CAACC,cAAc,CAACe,SAAS,CAAC;QAChG;QACA,IAAIhB,MAAM,CAACC,cAAc,CAACgB,cAAc,EAAE;UACtC,MAAMR,MAAI,CAACjC,kBAAkB,CAAC,CAAC,CAACoC,OAAO,CAAC1B,QAAQ,CAAC+B,cAAc,EAAEjB,MAAM,CAACC,cAAc,CAACgB,cAAc,CAAC;QAC1G;QACA,MAAMR,MAAI,CAACjC,kBAAkB,CAAC,CAAC,CAACoC,OAAO,CAAC1B,QAAQ,CAACgC,iBAAiB,EAAElB,MAAM,CAACC,cAAc,CAACkB,cAAc,CAAC;MAC7G;MACA,IAAInB,MAAM,CAACD,aAAa,EAAE;QACtB,MAAMU,MAAI,CAACjC,kBAAkB,CAAC,CAAC,CAACoC,OAAO,CAAC1B,QAAQ,CAACa,aAAa,EAAEM,IAAI,CAACe,SAAS,CAACpB,MAAM,CAACD,aAAa,CAAC,CAAC;MACzG,CAAC,MACI;QACD,MAAMU,MAAI,CAACjC,kBAAkB,CAAC,CAAC,CAACuC,UAAU,CAAC7B,QAAQ,CAACa,aAAa,CAAC;MACtE;MACA,MAAMU,MAAI,CAACjC,kBAAkB,CAAC,CAAC,CAACoC,OAAO,CAAC1B,QAAQ,CAACU,UAAU,EAAE,GAAGI,MAAM,CAACJ,UAAU,EAAE,CAAC;IAAC;EACzF;EACMyB,WAAWA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAArC,iBAAA;MAChB,MAAMC,QAAQ,SAASoC,MAAI,CAACnC,WAAW,CAAC,CAAC;MACzC;MACA,MAAMoC,OAAO,CAACC,GAAG,CAAC,CACdF,MAAI,CAAC9C,kBAAkB,CAAC,CAAC,CAACuC,UAAU,CAAC7B,QAAQ,CAACI,WAAW,CAAC,EAC1DgC,MAAI,CAAC9C,kBAAkB,CAAC,CAAC,CAACuC,UAAU,CAAC7B,QAAQ,CAACM,OAAO,CAAC,EACtD8B,MAAI,CAAC9C,kBAAkB,CAAC,CAAC,CAACuC,UAAU,CAAC7B,QAAQ,CAACU,UAAU,CAAC,EACzD0B,MAAI,CAAC9C,kBAAkB,CAAC,CAAC,CAACuC,UAAU,CAAC7B,QAAQ,CAACQ,YAAY,CAAC,EAC3D4B,MAAI,CAAC9C,kBAAkB,CAAC,CAAC,CAACuC,UAAU,CAAC7B,QAAQ,CAACa,aAAa,CAAC,EAC5DuB,MAAI,CAAC9C,kBAAkB,CAAC,CAAC,CAACuC,UAAU,CAACO,MAAI,CAACT,kBAAkB,CAAC,CAAC,CAAC,EAC/DS,MAAI,CAAC9C,kBAAkB,CAAC,CAAC,CAACuC,UAAU,CAAC7B,QAAQ,CAACuC,aAAa,CAAC,CAC/D,CAAC;IAAC;EACP;EACMvB,iBAAiBA,CAACC,QAAQ,EAAE;IAAA,IAAAuB,MAAA;IAAA,OAAAzC,iBAAA;MAC9B,MAAMC,QAAQ,SAASwC,MAAI,CAACvC,WAAW,CAACgB,QAAQ,CAAC;MACjD,MAAMa,SAAS,SAASU,MAAI,CAAClD,kBAAkB,CAAC,CAAC,CAACa,OAAO,CAACH,QAAQ,CAAC8B,SAAS,CAAC;MAC7E,MAAMC,cAAc,SAASS,MAAI,CAAClD,kBAAkB,CAAC,CAAC,CAACa,OAAO,CAACH,QAAQ,CAAC+B,cAAc,CAAC;MACvF,MAAME,cAAc,SAASO,MAAI,CAAClD,kBAAkB,CAAC,CAAC,CAACa,OAAO,CAACH,QAAQ,CAACgC,iBAAiB,CAAC;MAC1F,OAAOC,cAAc,IAAIF,cAAc,IAAID,SAAS,GAC9C;QACEA,SAAS;QACTC,cAAc;QACdE;MACJ,CAAC,GACC,IAAI;IAAC;EACf;EACMQ,mBAAmBA,CAACxB,QAAQ,EAAE;IAAA,IAAAyB,MAAA;IAAA,OAAA3C,iBAAA;MAChC,MAAMC,QAAQ,SAAS0C,MAAI,CAACzC,WAAW,CAACgB,QAAQ,CAAC;MACjD,MAAMoB,OAAO,CAACC,GAAG,CAAC,CACdI,MAAI,CAACpD,kBAAkB,CAAC,CAAC,CAACuC,UAAU,CAAC7B,QAAQ,CAAC8B,SAAS,CAAC,EACxDY,MAAI,CAACpD,kBAAkB,CAAC,CAAC,CAACuC,UAAU,CAAC7B,QAAQ,CAAC+B,cAAc,CAAC,EAC7DW,MAAI,CAACpD,kBAAkB,CAAC,CAAC,CAACuC,UAAU,CAAC7B,QAAQ,CAACgC,iBAAiB,CAAC,CACnE,CAAC;IAAC;EACP;EACM/B,WAAWA,CAACgB,QAAQ,EAAE;IAAA,IAAA0B,MAAA;IAAA,OAAA5C,iBAAA;MACxBhB,yBAAyB,CAAC4D,MAAI,CAAC/C,UAAU,EAAEgD,OAAO,CAAC;MACnD,MAAMnB,YAAY,GAAGR,QAAQ,WAAW0B,MAAI,CAACzB,eAAe,CAAC,CAAC,CAAC;MAC/D,OAAO2B,wBAAwB,CAACzD,eAAe,EAAE,GAAGuD,MAAI,CAAC/C,UAAU,CAACgD,OAAO,CAACE,gBAAgB,IAAIrB,YAAY,EAAE,CAAC;IAAC;EACpH;EACAE,kBAAkBA,CAAA,EAAG;IACjB5C,yBAAyB,CAAC,IAAI,CAACa,UAAU,EAAEgD,OAAO,CAAC;IACnD,MAAMG,UAAU,GAAG,IAAI,CAACnD,UAAU,CAACgD,OAAO,CAACE,gBAAgB;IAC3D,OAAO,GAAG1D,eAAe,IAAI2D,UAAU,eAAe;EAC1D;EACM7B,eAAeA,CAAA,EAAG;IAAA,IAAA8B,MAAA;IAAA,OAAAjD,iBAAA;MACpB,MAAM0B,YAAY,GAAG,OAAOuB,MAAI,CAAC1D,kBAAkB,CAAC,CAAC,CAACa,OAAO,CAAC6C,MAAI,CAACrB,kBAAkB,CAAC,CAAC,CAAC,KACpF,UAAU;MACd,OAAOF,YAAY;IAAC;EACxB;EACMwB,gBAAgBA,CAACC,QAAQ,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAApD,iBAAA;MAC7B,MAAM;QAAEwC,aAAa,EAAEa;MAAiB,CAAC,SAASD,MAAI,CAAClD,WAAW,CAAC,CAAC;MACpE,MAAMkD,MAAI,CAAC7D,kBAAkB,CAAC,CAAC,CAACoC,OAAO,CAAC0B,gBAAgB,EAAEjC,IAAI,CAACe,SAAS,CAACgB,QAAQ,CAAC,CAAC;IAAC;EACxF;EACMG,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAvD,iBAAA;MACrB,MAAM;QAAEwC,aAAa,EAAEa;MAAiB,CAAC,SAASE,MAAI,CAACrD,WAAW,CAAC,CAAC;MACpE,MAAMsC,aAAa,SAASe,MAAI,CAAChE,kBAAkB,CAAC,CAAC,CAACa,OAAO,CAACiD,gBAAgB,CAAC;MAC/E,OAAOb,aAAa,IAAIpB,IAAI,CAACC,KAAK,CAACmB,aAAa,CAAC;IAAC;EACtD;AACJ;AACA,MAAMM,wBAAwB,GAAGA,CAACU,QAAQ,EAAER,UAAU,KAAK;EACvD,OAAOS,kBAAkB,CAACvE,oBAAoB,CAAC,CAAC,GAAGsE,QAAQ,EAAE,EAAER,UAAU,CAAC;AAC9E,CAAC;AACD,SAASS,kBAAkBA,CAACxD,QAAQ,EAAE;EAClC,MAAMyD,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC;IAAE,GAAG3D;EAAS,CAAC,CAAC;EAC3C,OAAO,CAAC4D,MAAM,EAAEb,UAAU,KAAKU,IAAI,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,MAAM;IAC1D,GAAGD,GAAG;IACN,CAACC,OAAO,GAAG,GAAGH,MAAM,IAAIb,UAAU,IAAIgB,OAAO;EACjD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACX;AAEA,SAAS1E,iBAAiB,EAAEwD,wBAAwB,EAAEW,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}