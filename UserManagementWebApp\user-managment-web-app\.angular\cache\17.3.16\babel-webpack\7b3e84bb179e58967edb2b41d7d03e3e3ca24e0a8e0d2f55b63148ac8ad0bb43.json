{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst openAuthSession = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (url) {\n    if (!window?.location) {\n      return;\n    }\n    // enforce HTTPS\n    window.location.href = url.replace('http://', 'https://');\n  });\n  return function openAuthSession(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { openAuthSession };", "map": {"version": 3, "names": ["openAuthSession", "_ref", "_asyncToGenerator", "url", "window", "location", "href", "replace", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/utils/openAuthSession.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst openAuthSession = async (url) => {\n    if (!window?.location) {\n        return;\n    }\n    // enforce HTTPS\n    window.location.href = url.replace('http://', 'https://');\n};\n\nexport { openAuthSession };\n"], "mappings": ";AAAA;AACA;AACA,MAAMA,eAAe;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,GAAG,EAAK;IACnC,IAAI,CAACC,MAAM,EAAEC,QAAQ,EAAE;MACnB;IACJ;IACA;IACAD,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGH,GAAG,CAACI,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC;EAC7D,CAAC;EAAA,gBANKP,eAAeA,CAAAQ,EAAA;IAAA,OAAAP,IAAA,CAAAQ,KAAA,OAAAC,SAAA;EAAA;AAAA,GAMpB;AAED,SAASV,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}