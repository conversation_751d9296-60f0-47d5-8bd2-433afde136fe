{"ast": null, "code": "const heDict = {\n  'Account recovery requires verified contact information': 'שחזור לקוח דורש עוד מידע',\n  'Authenticator App (TOTP)': 'אפליקציית אימות (TOTP)',\n  'Back to Sign In': 'חזור להרשמה',\n  'Change Password': 'עדכון סיסמא',\n  Changing: 'מעדכן',\n  Code: 'קוד',\n  'Confirm Email Code': 'אמת קוד אימייל',\n  'Confirm Password': 'אשר סיסמא',\n  'Confirm Sign Up': 'אשר הרשמה',\n  'Confirm SMS Code': 'אשר sms קוד',\n  'Confirm TOTP Code': 'אשר totp קוד',\n  Confirm: 'אישור',\n  'Confirmation Code': 'אישור קוד',\n  Confirming: 'מאשר',\n  'Create a new account': 'צור משתמש חדש',\n  'Create Account': 'צור משתמש',\n  'Creating Account': 'יצירת משתמש',\n  'Dismiss alert': 'הסר התראה',\n  Email: 'אימייל',\n  'Email Message': 'הודעת אימייל',\n  'Enter your code': 'הכנס את הקוד',\n  'Enter your Email': 'הכנס את המייל שלך',\n  'Enter your phone number': 'הכנס את מספר הטלפון שלך',\n  'Enter your username': 'הכנס את שם המתמש שלך',\n  'Forgot your password?': 'שכחת סיסמא ?',\n  'Hide password': 'הסתר סיסמא',\n  Loading: 'טוען',\n  'Multi-Factor Authentication': 'אימות רב-גורמי',\n  'Multi-Factor Authentication Setup': 'הגדרת אימות רב-גורמי',\n  'New password': 'סיסמא חדשה',\n  or: 'אוֹ',\n  Password: 'סיסמא',\n  'Phone Number': 'מספר טלפון',\n  'Resend Code': 'שלח קוד שוב',\n  'Reset your password': 'אפס סיסמא',\n  'Reset your Password': 'אפס סיסמא',\n  'Select MFA Type': 'בחר סוג אימות רב-גורמי',\n  'Send code': 'שלח קוד',\n  'Send Code': 'שלח קוד',\n  Sending: 'שולח',\n  'Setup Email': 'הגדר אימייל',\n  'Setup TOTP': 'Setup TOTP',\n  'Show password': 'הצג סיסמא',\n  'Sign in to your account': 'התחבר לחשבון שלך',\n  'Sign In with Amazon': 'Sign In with Amazon',\n  'Sign In with Apple': 'Sign In with Apple',\n  'Sign In with Facebook': 'Sign In with Facebook',\n  'Sign In with Google': 'Sign In with Google',\n  'Sign in': 'התחבר',\n  'Sign In': 'התחבר',\n  'Signing in': 'מתחבר',\n  Skip: 'דלג',\n  Submit: 'שלח',\n  Submitting: 'שולח',\n  'Text Message (SMS)': 'הודעת טקסט (SMS)',\n  Username: 'שם משתמש',\n  'Verify Contact': 'אמת איש קשר',\n  Verify: 'אמת'\n};\nexport { heDict };", "map": {"version": 3, "names": ["heDict", "Changing", "Code", "Confirm", "Confirming", "Email", "Loading", "or", "Password", "Sending", "<PERSON><PERSON>", "Submit", "Submitting", "Username", "Verify"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/i18n/dictionaries/authenticator/he.mjs"], "sourcesContent": ["const heDict = {\n    'Account recovery requires verified contact information': 'שחזור לקוח דורש עוד מידע',\n    'Authenticator App (TOTP)': 'אפליקציית אימות (TOTP)',\n    'Back to Sign In': 'חזור להרשמה',\n    'Change Password': 'עדכון סיסמא',\n    Changing: 'מעדכן',\n    Code: 'קוד',\n    'Confirm Email Code': 'אמת קוד אימייל',\n    'Confirm Password': 'אשר סיסמא',\n    'Confirm Sign Up': 'אשר הרשמה',\n    'Confirm SMS Code': 'אשר sms קוד',\n    'Confirm TOTP Code': 'אשר totp קוד',\n    Confirm: 'אישור',\n    'Confirmation Code': 'אישור קוד',\n    Confirming: 'מאשר',\n    'Create a new account': 'צור משתמש חדש',\n    'Create Account': 'צור משתמש',\n    'Creating Account': 'יצירת משתמש',\n    'Dismiss alert': 'הסר התראה',\n    Email: 'אימייל',\n    'Email Message': 'הודעת אימייל',\n    'Enter your code': 'הכנס את הקוד',\n    'Enter your Email': 'הכנס את המייל שלך',\n    'Enter your phone number': 'הכנס את מספר הטלפון שלך',\n    'Enter your username': 'הכנס את שם המתמש שלך',\n    'Forgot your password?': 'שכחת סיסמא ?',\n    'Hide password': 'הסתר סיסמא',\n    Loading: 'טוען',\n    'Multi-Factor Authentication': 'אימות רב-גורמי',\n    'Multi-Factor Authentication Setup': 'הגדרת אימות רב-גורמי',\n    'New password': 'סיסמא חדשה',\n    or: 'אוֹ',\n    Password: 'סיסמא',\n    'Phone Number': 'מספר טלפון',\n    'Resend Code': 'שלח קוד שוב',\n    'Reset your password': 'אפס סיסמא',\n    'Reset your Password': 'אפס סיסמא',\n    'Select MFA Type': 'בחר סוג אימות רב-גורמי',\n    'Send code': 'שלח קוד',\n    'Send Code': 'שלח קוד',\n    Sending: 'שולח',\n    'Setup Email': 'הגדר אימייל',\n    'Setup TOTP': 'Setup TOTP',\n    'Show password': 'הצג סיסמא',\n    'Sign in to your account': 'התחבר לחשבון שלך',\n    'Sign In with Amazon': 'Sign In with Amazon',\n    'Sign In with Apple': 'Sign In with Apple',\n    'Sign In with Facebook': 'Sign In with Facebook',\n    'Sign In with Google': 'Sign In with Google',\n    'Sign in': 'התחבר',\n    'Sign In': 'התחבר',\n    'Signing in': 'מתחבר',\n    Skip: 'דלג',\n    Submit: 'שלח',\n    Submitting: 'שולח',\n    'Text Message (SMS)': 'הודעת טקסט (SMS)',\n    Username: 'שם משתמש',\n    'Verify Contact': 'אמת איש קשר',\n    Verify: 'אמת',\n};\n\nexport { heDict };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACX,wDAAwD,EAAE,0BAA0B;EACpF,0BAA0B,EAAE,wBAAwB;EACpD,iBAAiB,EAAE,aAAa;EAChC,iBAAiB,EAAE,aAAa;EAChCC,QAAQ,EAAE,OAAO;EACjBC,IAAI,EAAE,KAAK;EACX,oBAAoB,EAAE,gBAAgB;EACtC,kBAAkB,EAAE,WAAW;EAC/B,iBAAiB,EAAE,WAAW;EAC9B,kBAAkB,EAAE,aAAa;EACjC,mBAAmB,EAAE,cAAc;EACnCC,OAAO,EAAE,OAAO;EAChB,mBAAmB,EAAE,WAAW;EAChCC,UAAU,EAAE,MAAM;EAClB,sBAAsB,EAAE,eAAe;EACvC,gBAAgB,EAAE,WAAW;EAC7B,kBAAkB,EAAE,aAAa;EACjC,eAAe,EAAE,WAAW;EAC5BC,KAAK,EAAE,QAAQ;EACf,eAAe,EAAE,cAAc;EAC/B,iBAAiB,EAAE,cAAc;EACjC,kBAAkB,EAAE,mBAAmB;EACvC,yBAAyB,EAAE,yBAAyB;EACpD,qBAAqB,EAAE,sBAAsB;EAC7C,uBAAuB,EAAE,cAAc;EACvC,eAAe,EAAE,YAAY;EAC7BC,OAAO,EAAE,MAAM;EACf,6BAA6B,EAAE,gBAAgB;EAC/C,mCAAmC,EAAE,sBAAsB;EAC3D,cAAc,EAAE,YAAY;EAC5BC,EAAE,EAAE,KAAK;EACTC,QAAQ,EAAE,OAAO;EACjB,cAAc,EAAE,YAAY;EAC5B,aAAa,EAAE,aAAa;EAC5B,qBAAqB,EAAE,WAAW;EAClC,qBAAqB,EAAE,WAAW;EAClC,iBAAiB,EAAE,wBAAwB;EAC3C,WAAW,EAAE,SAAS;EACtB,WAAW,EAAE,SAAS;EACtBC,OAAO,EAAE,MAAM;EACf,aAAa,EAAE,aAAa;EAC5B,YAAY,EAAE,YAAY;EAC1B,eAAe,EAAE,WAAW;EAC5B,yBAAyB,EAAE,kBAAkB;EAC7C,qBAAqB,EAAE,qBAAqB;EAC5C,oBAAoB,EAAE,oBAAoB;EAC1C,uBAAuB,EAAE,uBAAuB;EAChD,qBAAqB,EAAE,qBAAqB;EAC5C,SAAS,EAAE,OAAO;EAClB,SAAS,EAAE,OAAO;EAClB,YAAY,EAAE,OAAO;EACrBC,IAAI,EAAE,KAAK;EACXC,MAAM,EAAE,KAAK;EACbC,UAAU,EAAE,MAAM;EAClB,oBAAoB,EAAE,kBAAkB;EACxCC,QAAQ,EAAE,UAAU;EACpB,gBAAgB,EAAE,aAAa;EAC/BC,MAAM,EAAE;AACZ,CAAC;AAED,SAASd,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}