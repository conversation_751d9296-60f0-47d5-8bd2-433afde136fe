{"ast": null, "code": "import { getRetryD<PERSON>ider, parseJsonError, jitteredBackoff } from '@aws-amplify/core/internals/aws-client-utils';\nimport { getAmplifyUserAgent } from '@aws-amplify/core/internals/utils';\nimport { COGNITO_IDP_SERVICE_NAME } from '../../../constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst DEFAULT_SERVICE_CLIENT_API_CONFIG = {\n  service: COGNITO_IDP_SERVICE_NAME,\n  retryDecider: getRetryDecider(parseJsonError),\n  computeDelay: jitteredBackoff,\n  userAgentValue: getAmplifyUserAgent(),\n  cache: 'no-store'\n};\nexport { DEFAULT_SERVICE_CLIENT_API_CONFIG };", "map": {"version": 3, "names": ["getRetryDecider", "parseJsonError", "jittered<PERSON><PERSON>off", "getAmplifyUserAgent", "COGNITO_IDP_SERVICE_NAME", "DEFAULT_SERVICE_CLIENT_API_CONFIG", "service", "retryDecider", "computeDelay", "userAgentValue", "cache"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs"], "sourcesContent": ["import { getRetryD<PERSON>ider, parseJsonError, jitteredBackoff } from '@aws-amplify/core/internals/aws-client-utils';\nimport { getAmplifyUserAgent } from '@aws-amplify/core/internals/utils';\nimport { COGNITO_IDP_SERVICE_NAME } from '../../../constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst DEFAULT_SERVICE_CLIENT_API_CONFIG = {\n    service: COGNITO_IDP_SERVICE_NAME,\n    retryDecider: getRetryDecider(parseJsonError),\n    computeDelay: jitteredBackoff,\n    userAgentValue: getAmplifyUserAgent(),\n    cache: 'no-store',\n};\n\nexport { DEFAULT_SERVICE_CLIENT_API_CONFIG };\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,cAAc,EAAEC,eAAe,QAAQ,8CAA8C;AAC/G,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,wBAAwB,QAAQ,wBAAwB;;AAEjE;AACA;AACA,MAAMC,iCAAiC,GAAG;EACtCC,OAAO,EAAEF,wBAAwB;EACjCG,YAAY,EAAEP,eAAe,CAACC,cAAc,CAAC;EAC7CO,YAAY,EAAEN,eAAe;EAC7BO,cAAc,EAAEN,mBAAmB,CAAC,CAAC;EACrCO,KAAK,EAAE;AACX,CAAC;AAED,SAASL,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}