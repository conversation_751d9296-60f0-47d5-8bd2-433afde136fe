{"ast": null, "code": "// Breakpoint unit is in pixels\nconst breakpoints = {\n  values: {\n    base: 0,\n    small: 480,\n    medium: 768,\n    large: 992,\n    xl: 1280,\n    xxl: 1536\n  },\n  defaultBreakpoint: 'base'\n};\nexport { breakpoints };", "map": {"version": 3, "names": ["breakpoints", "values", "base", "small", "medium", "large", "xl", "xxl", "defaultBreakpoint"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/breakpoints.mjs"], "sourcesContent": ["// Breakpoint unit is in pixels\nconst breakpoints = {\n    values: {\n        base: 0,\n        small: 480,\n        medium: 768,\n        large: 992,\n        xl: 1280,\n        xxl: 1536,\n    },\n    defaultBreakpoint: 'base',\n};\n\nexport { breakpoints };\n"], "mappings": "AAAA;AACA,MAAMA,WAAW,GAAG;EAChBC,MAAM,EAAE;IACJC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,GAAG;IACVC,EAAE,EAAE,IAAI;IACRC,GAAG,EAAE;EACT,CAAC;EACDC,iBAAiB,EAAE;AACvB,CAAC;AAED,SAASR,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}