{"ast": null, "code": "import { MAX_DELAY_MS } from './constants.mjs';\nimport { jitteredBackoff } from './jitteredBackoff.mjs';\nimport { retry } from './retry.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @private\n * Internal use of Amplify only\n */\nconst jitteredExponentialRetry = (functionToRetry, args, maxDelayMs = MAX_DELAY_MS, onTerminate) => retry(functionToRetry, args, jitteredBackoff(maxDelayMs), onTerminate);\nexport { jitteredExponentialRetry };", "map": {"version": 3, "names": ["MAX_DELAY_MS", "jittered<PERSON><PERSON>off", "retry", "jitteredExponentialRetry", "functionToRetry", "args", "max<PERSON>elay<PERSON>", "onTerminate"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/retry/jitteredExponentialRetry.mjs"], "sourcesContent": ["import { MAX_DELAY_MS } from './constants.mjs';\nimport { jitteredBackoff } from './jitteredBackoff.mjs';\nimport { retry } from './retry.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @private\n * Internal use of Amplify only\n */\nconst jitteredExponentialRetry = (functionToRetry, args, maxDelayMs = MAX_DELAY_MS, onTerminate) => retry(functionToRetry, args, jitteredBackoff(maxDelayMs), onTerminate);\n\nexport { jitteredExponentialRetry };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,KAAK,QAAQ,aAAa;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAGA,CAACC,eAAe,EAAEC,IAAI,EAAEC,UAAU,GAAGN,YAAY,EAAEO,WAAW,KAAKL,KAAK,CAACE,eAAe,EAAEC,IAAI,EAAEJ,eAAe,CAACK,UAAU,CAAC,EAAEC,WAAW,CAAC;AAE1K,SAASJ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}