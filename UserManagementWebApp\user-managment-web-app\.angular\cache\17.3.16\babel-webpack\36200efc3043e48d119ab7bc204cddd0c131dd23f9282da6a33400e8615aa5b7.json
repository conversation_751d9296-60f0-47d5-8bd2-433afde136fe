{"ast": null, "code": "import { setCustomUserAgent } from '@aws-amplify/core/internals/utils';\nimport { STORAGE_BROWSER_INPUT_BASE, STORAGE_MANAGER_INPUT_BASE, MAP_VIEW_INPUT_BASE, LOCATION_SEARCH_INPUT_BASE, IN_APP_MESSAGING_INPUT_BASE, FILE_UPLOADER_BASE_INPUT, ACCOUNT_SETTINGS_INPUT_BASE, AUTHENTICATOR_INPUT_BASE, AI_INPUT_BASE } from './constants.mjs';\nimport { noop } from '../utils.mjs';\n\n/**\n * @example\n * ```ts\n * // set user agent options\n * const clear = setUserAgent(input);\n *\n * // clear user agent options\n * clear();\n * ```\n */\nconst setUserAgent = ({\n  componentName,\n  packageName,\n  version\n}) => {\n  const packageData = [`ui-${packageName}`, version];\n  switch (componentName) {\n    case 'AIConversation':\n      {\n        setCustomUserAgent({\n          ...AI_INPUT_BASE,\n          additionalDetails: [[componentName], packageData]\n        });\n        break;\n      }\n    case 'Authenticator':\n      {\n        setCustomUserAgent({\n          ...AUTHENTICATOR_INPUT_BASE,\n          additionalDetails: [[componentName], packageData]\n        });\n        break;\n      }\n    case 'ChangePassword':\n    case 'DeleteUser':\n      {\n        setCustomUserAgent({\n          ...ACCOUNT_SETTINGS_INPUT_BASE,\n          additionalDetails: [['AccountSettings'], packageData]\n        });\n        break;\n      }\n    case 'FileUploader':\n      {\n        setCustomUserAgent({\n          ...FILE_UPLOADER_BASE_INPUT,\n          additionalDetails: [[componentName], packageData]\n        });\n        break;\n      }\n    case 'InAppMessaging':\n      {\n        setCustomUserAgent({\n          ...IN_APP_MESSAGING_INPUT_BASE,\n          additionalDetails: [[componentName], packageData]\n        });\n        break;\n      }\n    case 'LocationSearch':\n      {\n        setCustomUserAgent({\n          ...LOCATION_SEARCH_INPUT_BASE,\n          additionalDetails: [[componentName], packageData]\n        });\n        break;\n      }\n    case 'MapView':\n      {\n        setCustomUserAgent({\n          ...MAP_VIEW_INPUT_BASE,\n          additionalDetails: [[componentName], packageData]\n        });\n        break;\n      }\n    case 'StorageManager':\n      {\n        setCustomUserAgent({\n          ...STORAGE_MANAGER_INPUT_BASE,\n          additionalDetails: [[componentName], packageData]\n        });\n        break;\n      }\n    case 'StorageBrowser':\n      {\n        setCustomUserAgent({\n          ...STORAGE_BROWSER_INPUT_BASE,\n          additionalDetails: [[componentName], packageData]\n        });\n        break;\n      }\n  }\n  return noop;\n};\nexport { setUserAgent };", "map": {"version": 3, "names": ["setCustomUserAgent", "STORAGE_BROWSER_INPUT_BASE", "STORAGE_MANAGER_INPUT_BASE", "MAP_VIEW_INPUT_BASE", "LOCATION_SEARCH_INPUT_BASE", "IN_APP_MESSAGING_INPUT_BASE", "FILE_UPLOADER_BASE_INPUT", "ACCOUNT_SETTINGS_INPUT_BASE", "AUTHENTICATOR_INPUT_BASE", "AI_INPUT_BASE", "noop", "setUserAgent", "componentName", "packageName", "version", "packageData", "additionalDetails"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/utils/setUserAgent/setUserAgent.mjs"], "sourcesContent": ["import { setCustomUserAgent } from '@aws-amplify/core/internals/utils';\nimport { STORAGE_BROWSER_INPUT_BASE, STORAGE_MANAGER_INPUT_BASE, MAP_VIEW_INPUT_BASE, LOCATION_SEARCH_INPUT_BASE, IN_APP_MESSAGING_INPUT_BASE, FILE_UPLOADER_BASE_INPUT, ACCOUNT_SETTINGS_INPUT_BASE, AUTHENTICATOR_INPUT_BASE, AI_INPUT_BASE } from './constants.mjs';\nimport { noop } from '../utils.mjs';\n\n/**\n * @example\n * ```ts\n * // set user agent options\n * const clear = setUserAgent(input);\n *\n * // clear user agent options\n * clear();\n * ```\n */\nconst setUserAgent = ({ componentName, packageName, version, }) => {\n    const packageData = [`ui-${packageName}`, version];\n    switch (componentName) {\n        case 'AIConversation': {\n            setCustomUserAgent({\n                ...AI_INPUT_BASE,\n                additionalDetails: [[componentName], packageData],\n            });\n            break;\n        }\n        case 'Authenticator': {\n            setCustomUserAgent({\n                ...AUTHENTICATOR_INPUT_BASE,\n                additionalDetails: [[componentName], packageData],\n            });\n            break;\n        }\n        case 'ChangePassword':\n        case 'DeleteUser': {\n            setCustomUserAgent({\n                ...ACCOUNT_SETTINGS_INPUT_BASE,\n                additionalDetails: [['AccountSettings'], packageData],\n            });\n            break;\n        }\n        case 'FileUploader': {\n            setCustomUserAgent({\n                ...FILE_UPLOADER_BASE_INPUT,\n                additionalDetails: [[componentName], packageData],\n            });\n            break;\n        }\n        case 'InAppMessaging': {\n            setCustomUserAgent({\n                ...IN_APP_MESSAGING_INPUT_BASE,\n                additionalDetails: [[componentName], packageData],\n            });\n            break;\n        }\n        case 'LocationSearch': {\n            setCustomUserAgent({\n                ...LOCATION_SEARCH_INPUT_BASE,\n                additionalDetails: [[componentName], packageData],\n            });\n            break;\n        }\n        case 'MapView': {\n            setCustomUserAgent({\n                ...MAP_VIEW_INPUT_BASE,\n                additionalDetails: [[componentName], packageData],\n            });\n            break;\n        }\n        case 'StorageManager': {\n            setCustomUserAgent({\n                ...STORAGE_MANAGER_INPUT_BASE,\n                additionalDetails: [[componentName], packageData],\n            });\n            break;\n        }\n        case 'StorageBrowser': {\n            setCustomUserAgent({\n                ...STORAGE_BROWSER_INPUT_BASE,\n                additionalDetails: [[componentName], packageData],\n            });\n            break;\n        }\n    }\n    return noop;\n};\n\nexport { setUserAgent };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,0BAA0B,EAAEC,0BAA0B,EAAEC,mBAAmB,EAAEC,0BAA0B,EAAEC,2BAA2B,EAAEC,wBAAwB,EAAEC,2BAA2B,EAAEC,wBAAwB,EAAEC,aAAa,QAAQ,iBAAiB;AACtQ,SAASC,IAAI,QAAQ,cAAc;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAGA,CAAC;EAAEC,aAAa;EAAEC,WAAW;EAAEC;AAAS,CAAC,KAAK;EAC/D,MAAMC,WAAW,GAAG,CAAC,MAAMF,WAAW,EAAE,EAAEC,OAAO,CAAC;EAClD,QAAQF,aAAa;IACjB,KAAK,gBAAgB;MAAE;QACnBZ,kBAAkB,CAAC;UACf,GAAGS,aAAa;UAChBO,iBAAiB,EAAE,CAAC,CAACJ,aAAa,CAAC,EAAEG,WAAW;QACpD,CAAC,CAAC;QACF;MACJ;IACA,KAAK,eAAe;MAAE;QAClBf,kBAAkB,CAAC;UACf,GAAGQ,wBAAwB;UAC3BQ,iBAAiB,EAAE,CAAC,CAACJ,aAAa,CAAC,EAAEG,WAAW;QACpD,CAAC,CAAC;QACF;MACJ;IACA,KAAK,gBAAgB;IACrB,KAAK,YAAY;MAAE;QACff,kBAAkB,CAAC;UACf,GAAGO,2BAA2B;UAC9BS,iBAAiB,EAAE,CAAC,CAAC,iBAAiB,CAAC,EAAED,WAAW;QACxD,CAAC,CAAC;QACF;MACJ;IACA,KAAK,cAAc;MAAE;QACjBf,kBAAkB,CAAC;UACf,GAAGM,wBAAwB;UAC3BU,iBAAiB,EAAE,CAAC,CAACJ,aAAa,CAAC,EAAEG,WAAW;QACpD,CAAC,CAAC;QACF;MACJ;IACA,KAAK,gBAAgB;MAAE;QACnBf,kBAAkB,CAAC;UACf,GAAGK,2BAA2B;UAC9BW,iBAAiB,EAAE,CAAC,CAACJ,aAAa,CAAC,EAAEG,WAAW;QACpD,CAAC,CAAC;QACF;MACJ;IACA,KAAK,gBAAgB;MAAE;QACnBf,kBAAkB,CAAC;UACf,GAAGI,0BAA0B;UAC7BY,iBAAiB,EAAE,CAAC,CAACJ,aAAa,CAAC,EAAEG,WAAW;QACpD,CAAC,CAAC;QACF;MACJ;IACA,KAAK,SAAS;MAAE;QACZf,kBAAkB,CAAC;UACf,GAAGG,mBAAmB;UACtBa,iBAAiB,EAAE,CAAC,CAACJ,aAAa,CAAC,EAAEG,WAAW;QACpD,CAAC,CAAC;QACF;MACJ;IACA,KAAK,gBAAgB;MAAE;QACnBf,kBAAkB,CAAC;UACf,GAAGE,0BAA0B;UAC7Bc,iBAAiB,EAAE,CAAC,CAACJ,aAAa,CAAC,EAAEG,WAAW;QACpD,CAAC,CAAC;QACF;MACJ;IACA,KAAK,gBAAgB;MAAE;QACnBf,kBAAkB,CAAC;UACf,GAAGC,0BAA0B;UAC7Be,iBAAiB,EAAE,CAAC,CAACJ,aAAa,CAAC,EAAEG,WAAW;QACpD,CAAC,CAAC;QACF;MACJ;EACJ;EACA,OAAOL,IAAI;AACf,CAAC;AAED,SAASC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}