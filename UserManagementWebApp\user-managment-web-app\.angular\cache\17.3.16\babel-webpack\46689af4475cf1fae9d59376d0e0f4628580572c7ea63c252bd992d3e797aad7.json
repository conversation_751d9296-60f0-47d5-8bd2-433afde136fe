{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createSetUserMFAPreferenceClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createSetUserMFAPreferenceClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Updates the MFA preference of the user.\n *\n * @param input - The UpdateMFAPreferenceInput object.\n * @throws -{@link SetUserMFAPreferenceException } - Service error thrown when the MFA preference cannot be updated.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nfunction updateMFAPreference(_x) {\n  return _updateMFAPreference.apply(this, arguments);\n}\nfunction _updateMFAPreference() {\n  _updateMFAPreference = _asyncToGenerator(function* (input) {\n    const {\n      sms,\n      totp,\n      email\n    } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolEndpoint,\n      userPoolId\n    } = authConfig;\n    const {\n      tokens\n    } = yield fetchAuthSession({\n      forceRefresh: false\n    });\n    assertAuthTokens(tokens);\n    const setUserMFAPreference = createSetUserMFAPreferenceClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    yield setUserMFAPreference({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.UpdateMFAPreference)\n    }, {\n      AccessToken: tokens.accessToken.toString(),\n      SMSMfaSettings: getMFASettings(sms),\n      SoftwareTokenMfaSettings: getMFASettings(totp),\n      EmailMfaSettings: getMFASettings(email)\n    });\n  });\n  return _updateMFAPreference.apply(this, arguments);\n}\nfunction getMFASettings(mfaPreference) {\n  if (mfaPreference === 'DISABLED') {\n    return {\n      Enabled: false\n    };\n  } else if (mfaPreference === 'PREFERRED') {\n    return {\n      Enabled: true,\n      PreferredMfa: true\n    };\n  } else if (mfaPreference === 'ENABLED') {\n    return {\n      Enabled: true\n    };\n  } else if (mfaPreference === 'NOT_PREFERRED') {\n    return {\n      Enabled: true,\n      PreferredMfa: false\n    };\n  }\n}\nexport { getMFASettings, updateMFAPreference };", "map": {"version": 3, "names": ["Amplify", "fetchAuthSession", "assertTokenProviderConfig", "AuthAction", "getRegionFromUserPoolId", "assertAuthTokens", "getAuthUserAgentValue", "createSetUserMFAPreferenceClient", "createCognitoUserPoolEndpointResolver", "updateMFAPreference", "_x", "_updateMFAPreference", "apply", "arguments", "_asyncToGenerator", "input", "sms", "totp", "email", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userPoolEndpoint", "userPoolId", "tokens", "forceRefresh", "setUserMFAPreference", "endpointResolver", "endpointOverride", "region", "userAgentValue", "UpdateMFAPreference", "AccessToken", "accessToken", "toString", "SMSMfaSettings", "getMFASettings", "SoftwareTokenMfaSettings", "EmailMfaSettings", "mfaPreference", "Enabled", "PreferredMfa"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/updateMFAPreference.mjs"], "sourcesContent": ["import { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createSetUserMFAPreferenceClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createSetUserMFAPreferenceClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Updates the MFA preference of the user.\n *\n * @param input - The UpdateMFAPreferenceInput object.\n * @throws -{@link SetUserMFAPreferenceException } - Service error thrown when the MFA preference cannot be updated.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nasync function updateMFAPreference(input) {\n    const { sms, totp, email } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolEndpoint, userPoolId } = authConfig;\n    const { tokens } = await fetchAuthSession({ forceRefresh: false });\n    assertAuthTokens(tokens);\n    const setUserMFAPreference = createSetUserMFAPreferenceClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    await setUserMFAPreference({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.UpdateMFAPreference),\n    }, {\n        AccessToken: tokens.accessToken.toString(),\n        SMSMfaSettings: getMFASettings(sms),\n        SoftwareTokenMfaSettings: getMFASettings(totp),\n        EmailMfaSettings: getMFASettings(email),\n    });\n}\nfunction getMFASettings(mfaPreference) {\n    if (mfaPreference === 'DISABLED') {\n        return {\n            Enabled: false,\n        };\n    }\n    else if (mfaPreference === 'PREFERRED') {\n        return {\n            Enabled: true,\n            PreferredMfa: true,\n        };\n    }\n    else if (mfaPreference === 'ENABLED') {\n        return {\n            Enabled: true,\n        };\n    }\n    else if (mfaPreference === 'NOT_PREFERRED') {\n        return {\n            Enabled: true,\n            PreferredMfa: false,\n        };\n    }\n}\n\nexport { getMFASettings, updateMFAPreference };\n"], "mappings": ";AAAA,SAASA,OAAO,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC7D,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,sCAAsC;AAC7C,OAAO,qBAAqB;AAC5B,SAASC,gCAAgC,QAAQ,2GAA2G;AAC5J,SAASC,qCAAqC,QAAQ,wDAAwD;;AAE9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA,SAOeC,mBAAmBA,CAAAC,EAAA;EAAA,OAAAC,oBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,qBAAA;EAAAA,oBAAA,GAAAG,iBAAA,CAAlC,WAAmCC,KAAK,EAAE;IACtC,MAAM;MAAEC,GAAG;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGH,KAAK;IAClC,MAAMI,UAAU,GAAGnB,OAAO,CAACoB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDpB,yBAAyB,CAACiB,UAAU,CAAC;IACrC,MAAM;MAAEI,gBAAgB;MAAEC;IAAW,CAAC,GAAGL,UAAU;IACnD,MAAM;MAAEM;IAAO,CAAC,SAASxB,gBAAgB,CAAC;MAAEyB,YAAY,EAAE;IAAM,CAAC,CAAC;IAClErB,gBAAgB,CAACoB,MAAM,CAAC;IACxB,MAAME,oBAAoB,GAAGpB,gCAAgC,CAAC;MAC1DqB,gBAAgB,EAAEpB,qCAAqC,CAAC;QACpDqB,gBAAgB,EAAEN;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMI,oBAAoB,CAAC;MACvBG,MAAM,EAAE1B,uBAAuB,CAACoB,UAAU,CAAC;MAC3CO,cAAc,EAAEzB,qBAAqB,CAACH,UAAU,CAAC6B,mBAAmB;IACxE,CAAC,EAAE;MACCC,WAAW,EAAER,MAAM,CAACS,WAAW,CAACC,QAAQ,CAAC,CAAC;MAC1CC,cAAc,EAAEC,cAAc,CAACrB,GAAG,CAAC;MACnCsB,wBAAwB,EAAED,cAAc,CAACpB,IAAI,CAAC;MAC9CsB,gBAAgB,EAAEF,cAAc,CAACnB,KAAK;IAC1C,CAAC,CAAC;EACN,CAAC;EAAA,OAAAP,oBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AACD,SAASwB,cAAcA,CAACG,aAAa,EAAE;EACnC,IAAIA,aAAa,KAAK,UAAU,EAAE;IAC9B,OAAO;MACHC,OAAO,EAAE;IACb,CAAC;EACL,CAAC,MACI,IAAID,aAAa,KAAK,WAAW,EAAE;IACpC,OAAO;MACHC,OAAO,EAAE,IAAI;MACbC,YAAY,EAAE;IAClB,CAAC;EACL,CAAC,MACI,IAAIF,aAAa,KAAK,SAAS,EAAE;IAClC,OAAO;MACHC,OAAO,EAAE;IACb,CAAC;EACL,CAAC,MACI,IAAID,aAAa,KAAK,eAAe,EAAE;IACxC,OAAO;MACHC,OAAO,EAAE,IAAI;MACbC,YAAY,EAAE;IAClB,CAAC;EACL;AACJ;AAEA,SAASL,cAAc,EAAE5B,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}