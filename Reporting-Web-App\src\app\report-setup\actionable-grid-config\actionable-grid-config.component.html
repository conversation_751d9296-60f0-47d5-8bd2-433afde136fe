<div class="actionable-grid-config-setting relative">
    @if (disabled) {
    <p-message severity="light" styleClass="w-full max-w-full" text="Project is Locked"></p-message>
    } @else {
        @if (displayScrollIndicator) {
            <div class="overlay-button">
                <span class="flex">
                    <p-button class="text-light" [rounded]="false" [outlined]="true" severity="primary"
                        icon="pi pi-angle-right" styleClass="h-3rem shadow-6" (click)="onClickScrollIndicator()"></p-button>
                </span>
            </div>
        }
    <ag-grid-angular id="ag-Configuration" domLayout="autoHeight" class="ag-theme-material format-columns-data border-none"
        rowHeight="55" [headerHeight]="55" [rowData]="columnsConfig" [animateRows]="true" [rowDragManaged]="true"
        [suppressMoveWhenRowDragging]="true" (rowDragEnd)="onRowDragEnd($event)" (gridReady)="onGridReady($event)"
        (firstDataRendered)="onFirstDataRendered($event)" [columnDefs]="columnDefs" [defaultColDef]="defaultColDef"
        [masterDetail]="true" [gridOptions]="gridOptions" [isRowMaster]="isRowMaster" [detailRowAutoHeight]="true"
        (bodyScrollEnd)="onBodyScrollEnd($event)">
    </ag-grid-angular>
    }
</div>