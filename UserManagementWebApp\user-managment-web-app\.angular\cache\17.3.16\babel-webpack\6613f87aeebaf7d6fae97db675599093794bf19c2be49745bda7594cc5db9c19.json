{"ast": null, "code": "import { composeService<PERSON><PERSON> } from '@aws-amplify/core/internals/aws-client-utils/composers';\nimport { DEFAULT_SERVICE_CLIENT_API_CONFIG } from './constants.mjs';\nimport { cognitoUserPoolTransferHandler } from './shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport { createUserPoolSerializer } from './shared/serde/createUserPoolSerializer.mjs';\nimport { createUserPoolDeserializer } from './shared/serde/createUserPoolDeserializer.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createConfirmSignUpClient = config => composeServiceApi(cognitoUserPoolTransferHandler, createUserPoolSerializer('ConfirmSignUp'), createUserPoolDeserializer(), {\n  ...DEFAULT_SERVICE_CLIENT_API_CONFIG,\n  ...config\n});\nexport { createConfirmSignUpClient };", "map": {"version": 3, "names": ["composeServiceApi", "DEFAULT_SERVICE_CLIENT_API_CONFIG", "cognitoUserPoolTransferHandler", "createUserPoolSerializer", "createUserPoolDeserializer", "createConfirmSignUpClient", "config"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/factories/serviceClients/cognitoIdentityProvider/createConfirmSignUpClient.mjs"], "sourcesContent": ["import { composeService<PERSON><PERSON> } from '@aws-amplify/core/internals/aws-client-utils/composers';\nimport { DEFAULT_SERVICE_CLIENT_API_CONFIG } from './constants.mjs';\nimport { cognitoUserPoolTransferHandler } from './shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport { createUserPoolSerializer } from './shared/serde/createUserPoolSerializer.mjs';\nimport { createUserPoolDeserializer } from './shared/serde/createUserPoolDeserializer.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createConfirmSignUpClient = (config) => composeServiceApi(cognitoUserPoolTransferHandler, createUserPoolSerializer('ConfirmSignUp'), createUserPoolDeserializer(), {\n    ...DEFAULT_SERVICE_CLIENT_API_CONFIG,\n    ...config,\n});\n\nexport { createConfirmSignUpClient };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,wDAAwD;AAC1F,SAASC,iCAAiC,QAAQ,iBAAiB;AACnE,SAASC,8BAA8B,QAAQ,qDAAqD;AACpG,SAASC,wBAAwB,QAAQ,6CAA6C;AACtF,SAASC,0BAA0B,QAAQ,+CAA+C;AAC1F,OAAO,8CAA8C;AACrD,OAAO,mCAAmC;;AAE1C;AACA;AACA,MAAMC,yBAAyB,GAAIC,MAAM,IAAKN,iBAAiB,CAACE,8BAA8B,EAAEC,wBAAwB,CAAC,eAAe,CAAC,EAAEC,0BAA0B,CAAC,CAAC,EAAE;EACrK,GAAGH,iCAAiC;EACpC,GAAGK;AACP,CAAC,CAAC;AAEF,SAASD,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}