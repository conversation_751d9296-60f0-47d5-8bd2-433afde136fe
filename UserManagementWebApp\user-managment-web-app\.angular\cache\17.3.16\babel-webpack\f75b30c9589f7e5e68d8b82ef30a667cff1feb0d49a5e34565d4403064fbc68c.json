{"ast": null, "code": "import { windowExists, keyPrefixMatch, globalExists } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with vue 3.3.2\nfunction vueWebDetect() {\n  return windowExists() && keyPrefixMatch(window, '__VUE');\n}\nfunction vueSSRDetect() {\n  return globalExists() && keyPrefixMatch(global, '__VUE');\n}\nexport { vueSSRDetect, vueWebDetect };", "map": {"version": 3, "names": ["windowExists", "keyPrefixMatch", "globalExists", "vueWebDetect", "window", "vueSSRDetect", "global"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Platform/detection/Vue.mjs"], "sourcesContent": ["import { windowExists, keyPrefixMatch, globalExists } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with vue 3.3.2\nfunction vueWebDetect() {\n    return windowExists() && keyPrefixMatch(window, '__VUE');\n}\nfunction vueSSRDetect() {\n    return globalExists() && keyPrefixMatch(global, '__VUE');\n}\n\nexport { vueSSRDetect, vueWebDetect };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,cAAc,EAAEC,YAAY,QAAQ,eAAe;;AAE1E;AACA;AACA;AACA,SAASC,YAAYA,CAAA,EAAG;EACpB,OAAOH,YAAY,CAAC,CAAC,IAAIC,cAAc,CAACG,MAAM,EAAE,OAAO,CAAC;AAC5D;AACA,SAASC,YAAYA,CAAA,EAAG;EACpB,OAAOH,YAAY,CAAC,CAAC,IAAID,cAAc,CAACK,MAAM,EAAE,OAAO,CAAC;AAC5D;AAEA,SAASD,YAAY,EAAEF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}