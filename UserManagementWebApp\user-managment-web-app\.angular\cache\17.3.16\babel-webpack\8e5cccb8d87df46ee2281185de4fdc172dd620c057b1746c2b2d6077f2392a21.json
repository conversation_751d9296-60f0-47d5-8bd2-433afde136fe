{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Wraps encodeURIComponent to encode additional characters to fully adhere to RFC 3986.\n * @see https://github.com/aws/aws-sdk-js-v3/blob/86b432c464150069678b25ff88d57c2ca26e75a2/packages/smithy-client/src/extended-encode-uri-component.ts#L7\n *\n * @param uri URI string to encode\n * @returns RFC 3986 encoded string\n *\n * @internal\n */\nconst extendedEncodeURIComponent = uri => {\n  // Match characters normally not encoded by `encodeURIComponent`\n  const extendedCharacters = /[!'()*]/g;\n  return encodeURIComponent(uri).replace(extendedCharacters, hexEncode);\n};\nconst hexEncode = c => `%${c.charCodeAt(0).toString(16).toUpperCase()}`;\nexport { extendedEncodeURIComponent };", "map": {"version": 3, "names": ["extendedEncodeURIComponent", "uri", "extendedCharacters", "encodeURIComponent", "replace", "hexEncode", "c", "charCodeAt", "toString", "toUpperCase"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/utils/extendedEncodeURIComponent.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Wraps encodeURIComponent to encode additional characters to fully adhere to RFC 3986.\n * @see https://github.com/aws/aws-sdk-js-v3/blob/86b432c464150069678b25ff88d57c2ca26e75a2/packages/smithy-client/src/extended-encode-uri-component.ts#L7\n *\n * @param uri URI string to encode\n * @returns RFC 3986 encoded string\n *\n * @internal\n */\nconst extendedEncodeURIComponent = (uri) => {\n    // Match characters normally not encoded by `encodeURIComponent`\n    const extendedCharacters = /[!'()*]/g;\n    return encodeURIComponent(uri).replace(extendedCharacters, hexEncode);\n};\nconst hexEncode = (c) => `%${c.charCodeAt(0).toString(16).toUpperCase()}`;\n\nexport { extendedEncodeURIComponent };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,0BAA0B,GAAIC,GAAG,IAAK;EACxC;EACA,MAAMC,kBAAkB,GAAG,UAAU;EACrC,OAAOC,kBAAkB,CAACF,GAAG,CAAC,CAACG,OAAO,CAACF,kBAAkB,EAAEG,SAAS,CAAC;AACzE,CAAC;AACD,MAAMA,SAAS,GAAIC,CAAC,IAAK,IAAIA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE;AAEzE,SAAST,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}