{"ast": null, "code": "import { Sha256 } from '@aws-crypto/sha256-js';\nimport { getCrypto, base64Encoder } from '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst CODE_VERIFIER_CHARSET = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n/**\n *\n * @param length Desired length of the code verifier.\n *\n * **NOTE:** According to the [RFC 7636](https://datatracker.ietf.org/doc/html/rfc7636#section-4.1)\n * A code verifier must be with a length >= 43 and <= 128.\n *\n * @returns An object that contains the generated `codeVerifier` and a method\n * `toCodeChallenge` to generate the code challenge from the `codeVerifier`\n * following the spec of [RFC 7636](https://datatracker.ietf.org/doc/html/rfc7636#section-4.2).\n */\nconst generateCodeVerifier = length => {\n  const randomBytes = new Uint8Array(length);\n  getCrypto().getRandomValues(randomBytes);\n  let value = '';\n  let codeChallenge;\n  for (const byte of randomBytes) {\n    value += CODE_VERIFIER_CHARSET.charAt(byte % CODE_VERIFIER_CHARSET.length);\n  }\n  return {\n    value,\n    method: 'S256',\n    toCodeChallenge() {\n      if (codeChallenge) {\n        return codeChallenge;\n      }\n      codeChallenge = generateCodeChallenge(value);\n      return codeChallenge;\n    }\n  };\n};\nfunction generateCodeChallenge(codeVerifier) {\n  const awsCryptoHash = new Sha256();\n  awsCryptoHash.update(codeVerifier);\n  const codeChallenge = removePaddingChar(base64Encoder.convert(awsCryptoHash.digestSync(), {\n    urlSafe: true\n  }));\n  return codeChallenge;\n}\nfunction removePaddingChar(base64Encoded) {\n  return base64Encoded.replace(/=/g, '');\n}\nexport { generateCodeVerifier };", "map": {"version": 3, "names": ["Sha256", "getCrypto", "base64Encoder", "CODE_VERIFIER_CHARSET", "generateCodeVerifier", "length", "randomBytes", "Uint8Array", "getRandomValues", "value", "codeChallenge", "byte", "char<PERSON>t", "method", "toCodeChallenge", "generateCodeChallenge", "codeVerifier", "awsCryptoHash", "update", "removePaddingChar", "convert", "digestSync", "urlSafe", "base64Encoded", "replace"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/oauth/generateCodeVerifier.mjs"], "sourcesContent": ["import { Sha256 } from '@aws-crypto/sha256-js';\nimport { getCrypto, base64Encoder } from '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst CODE_VERIFIER_CHARSET = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n/**\n *\n * @param length Desired length of the code verifier.\n *\n * **NOTE:** According to the [RFC 7636](https://datatracker.ietf.org/doc/html/rfc7636#section-4.1)\n * A code verifier must be with a length >= 43 and <= 128.\n *\n * @returns An object that contains the generated `codeVerifier` and a method\n * `toCodeChallenge` to generate the code challenge from the `codeVerifier`\n * following the spec of [RFC 7636](https://datatracker.ietf.org/doc/html/rfc7636#section-4.2).\n */\nconst generateCodeVerifier = (length) => {\n    const randomBytes = new Uint8Array(length);\n    getCrypto().getRandomValues(randomBytes);\n    let value = '';\n    let codeChallenge;\n    for (const byte of randomBytes) {\n        value += CODE_VERIFIER_CHARSET.charAt(byte % CODE_VERIFIER_CHARSET.length);\n    }\n    return {\n        value,\n        method: 'S256',\n        toCodeChallenge() {\n            if (codeChallenge) {\n                return codeChallenge;\n            }\n            codeChallenge = generateCodeChallenge(value);\n            return codeChallenge;\n        },\n    };\n};\nfunction generateCodeChallenge(codeVerifier) {\n    const awsCryptoHash = new Sha256();\n    awsCryptoHash.update(codeVerifier);\n    const codeChallenge = removePaddingChar(base64Encoder.convert(awsCryptoHash.digestSync(), { urlSafe: true }));\n    return codeChallenge;\n}\nfunction removePaddingChar(base64Encoded) {\n    return base64Encoded.replace(/=/g, '');\n}\n\nexport { generateCodeVerifier };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,SAAS,EAAEC,aAAa,QAAQ,mCAAmC;;AAE5E;AACA;AACA,MAAMC,qBAAqB,GAAG,gEAAgE;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAIC,MAAM,IAAK;EACrC,MAAMC,WAAW,GAAG,IAAIC,UAAU,CAACF,MAAM,CAAC;EAC1CJ,SAAS,CAAC,CAAC,CAACO,eAAe,CAACF,WAAW,CAAC;EACxC,IAAIG,KAAK,GAAG,EAAE;EACd,IAAIC,aAAa;EACjB,KAAK,MAAMC,IAAI,IAAIL,WAAW,EAAE;IAC5BG,KAAK,IAAIN,qBAAqB,CAACS,MAAM,CAACD,IAAI,GAAGR,qBAAqB,CAACE,MAAM,CAAC;EAC9E;EACA,OAAO;IACHI,KAAK;IACLI,MAAM,EAAE,MAAM;IACdC,eAAeA,CAAA,EAAG;MACd,IAAIJ,aAAa,EAAE;QACf,OAAOA,aAAa;MACxB;MACAA,aAAa,GAAGK,qBAAqB,CAACN,KAAK,CAAC;MAC5C,OAAOC,aAAa;IACxB;EACJ,CAAC;AACL,CAAC;AACD,SAASK,qBAAqBA,CAACC,YAAY,EAAE;EACzC,MAAMC,aAAa,GAAG,IAAIjB,MAAM,CAAC,CAAC;EAClCiB,aAAa,CAACC,MAAM,CAACF,YAAY,CAAC;EAClC,MAAMN,aAAa,GAAGS,iBAAiB,CAACjB,aAAa,CAACkB,OAAO,CAACH,aAAa,CAACI,UAAU,CAAC,CAAC,EAAE;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC,CAAC;EAC7G,OAAOZ,aAAa;AACxB;AACA,SAASS,iBAAiBA,CAACI,aAAa,EAAE;EACtC,OAAOA,aAAa,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;AAC1C;AAEA,SAASpB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}