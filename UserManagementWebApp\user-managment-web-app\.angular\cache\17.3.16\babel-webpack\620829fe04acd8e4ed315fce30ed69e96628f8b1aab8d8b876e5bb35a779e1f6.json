{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with react-native 0.17.7\nfunction reactNativeDetect() {\n  return typeof navigator !== 'undefined' && typeof navigator.product !== 'undefined' && navigator.product === 'ReactNative';\n}\nexport { reactNativeDetect };", "map": {"version": 3, "names": ["reactNativeDetect", "navigator", "product"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Platform/detection/ReactNative.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with react-native 0.17.7\nfunction reactNativeDetect() {\n    return (typeof navigator !== 'undefined' &&\n        typeof navigator.product !== 'undefined' &&\n        navigator.product === 'ReactNative');\n}\n\nexport { reactNativeDetect };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,iBAAiBA,CAAA,EAAG;EACzB,OAAQ,OAAOC,SAAS,KAAK,WAAW,IACpC,OAAOA,SAAS,CAACC,OAAO,KAAK,WAAW,IACxCD,SAAS,CAACC,OAAO,KAAK,aAAa;AAC3C;AAEA,SAASF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}