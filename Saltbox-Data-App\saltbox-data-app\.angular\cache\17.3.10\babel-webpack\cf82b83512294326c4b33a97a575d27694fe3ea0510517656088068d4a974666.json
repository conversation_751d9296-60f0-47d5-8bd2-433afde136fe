{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { DynamicFormLayoutAreas } from '../../../enums/dynamic-form';\nimport { FormlyRendererTypes } from 'src/app/sb-formly-renderer/enums/formly-renderer-types.enum';\nimport { DynamicFormFieldEditorComponent } from '../form-elements/field-editor/dynamic-form-field-editor.component';\nimport { FormElementsRendererComponent } from './form-elements-renderer/form-elements-renderer.component';\nimport { DragDropModule } from 'primeng/dragdrop';\nimport { DynamicFormSectionComponent } from '../../../dynamic-form-section/dynamic-form-section.component';\nimport { NgIf, NgClass } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/dynamic-forms.service\";\nimport * as i2 from \"primeng/dragdrop\";\nconst _c0 = a0 => ({\n  \"mr-1\": a0\n});\nconst _c1 = a0 => ({\n  \"disabled-element\": a0\n});\nfunction LayoutPreviewComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"app-dynamic-form-section\", 11);\n    i0.ɵɵlistener(\"contentChooserClick\", function LayoutPreviewComponent_div_3_Template_app_dynamic_form_section_contentChooserClick_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClickContentChooser($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, (ctx_r1.dynamicForm.layout == null ? null : ctx_r1.dynamicForm.layout.headerLeft == null ? null : ctx_r1.dynamicForm.layout.headerLeft.visible) && (ctx_r1.dynamicForm.layout == null ? null : ctx_r1.dynamicForm.layout.headerRight == null ? null : ctx_r1.dynamicForm.layout.headerRight.visible)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dynamicForm\", ctx_r1.dynamicForm)(\"layoutArea\", ctx_r1.DynamicFormAreas.HeaderLeft)(\"isPreview\", true)(\"disabled\", ctx_r1.disabled);\n  }\n}\nfunction LayoutPreviewComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"app-dynamic-form-section\", 11);\n    i0.ɵɵlistener(\"contentChooserClick\", function LayoutPreviewComponent_div_4_Template_app_dynamic_form_section_contentChooserClick_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClickContentChooser($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dynamicForm\", ctx_r1.dynamicForm)(\"layoutArea\", ctx_r1.DynamicFormAreas.HeaderRight)(\"isPreview\", true)(\"disabled\", ctx_r1.disabled);\n  }\n}\nfunction LayoutPreviewComponent_app_dynamic_form_section_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-dynamic-form-section\", 13);\n    i0.ɵɵlistener(\"contentChooserClick\", function LayoutPreviewComponent_app_dynamic_form_section_5_Template_app_dynamic_form_section_contentChooserClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClickContentChooser($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"dynamicForm\", ctx_r1.dynamicForm)(\"layoutArea\", ctx_r1.DynamicFormAreas.Instruction)(\"isPreview\", true)(\"disabled\", ctx_r1.disabled);\n  }\n}\nfunction LayoutPreviewComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"app-form-elements-renderer\", 15);\n    i0.ɵɵtwoWayListener(\"fieldsConfigChange\", function LayoutPreviewComponent_div_10_Template_app_form_elements_renderer_fieldsConfigChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.dynamicForm.fieldsConfig, $event) || (ctx_r1.dynamicForm.fieldsConfig = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, ctx_r1.dynamicForm.customFieldsConfig));\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"fieldsConfig\", ctx_r1.dynamicForm.fieldsConfig);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled)(\"baseSchema\", ctx_r1.datastore.baseSchema)(\"draggedField\", ctx_r1.draggedField);\n  }\n}\nfunction LayoutPreviewComponent_app_dynamic_form_section_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-dynamic-form-section\", 13);\n    i0.ɵɵlistener(\"contentChooserClick\", function LayoutPreviewComponent_app_dynamic_form_section_11_Template_app_dynamic_form_section_contentChooserClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClickContentChooser($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"dynamicForm\", ctx_r1.dynamicForm)(\"layoutArea\", ctx_r1.DynamicFormAreas.Footer)(\"isPreview\", true)(\"disabled\", ctx_r1.disabled);\n  }\n}\nfunction LayoutPreviewComponent_app_form_field_editor_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-form-field-editor\", 16);\n    i0.ɵɵtwoWayListener(\"visibleChange\", function LayoutPreviewComponent_app_form_field_editor_12_Template_app_form_field_editor_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.showFieldEditor, $event) || (ctx_r1.showFieldEditor = $event);\n      return i0.ɵɵresetView($event);\n    })(\"fieldChange\", function LayoutPreviewComponent_app_form_field_editor_12_Template_app_form_field_editor_fieldChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedField, $event) || (ctx_r1.selectedField = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"fieldChange\", function LayoutPreviewComponent_app_form_field_editor_12_Template_app_form_field_editor_fieldChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.updateField($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵtwoWayProperty(\"visible\", ctx_r1.showFieldEditor);\n    i0.ɵɵproperty(\"baseProperty\", ctx_r1.datastore == null ? null : ctx_r1.datastore.baseSchema == null ? null : ctx_r1.datastore.baseSchema.properties[ctx_r1.selectedField == null ? null : ctx_r1.selectedField.key]);\n    i0.ɵɵtwoWayProperty(\"field\", ctx_r1.selectedField);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n  }\n}\nexport class LayoutPreviewComponent {\n  constructor(dynamicFormsService) {\n    this.dynamicFormsService = dynamicFormsService;\n    this.disabled = false;\n    this.dynamicFormChange = new EventEmitter();\n    this.draggedFieldChange = new EventEmitter();\n    this.showFieldEditor = false;\n    this.FormlyRendererTypes = FormlyRendererTypes;\n    this.DynamicFormAreas = DynamicFormLayoutAreas;\n  }\n  ngOnInit() {\n    this.dynamicFormsService.connectionSettingChanged.subscribe(params => {\n      this.datastore = params.datastore;\n    });\n  }\n  updateDynamicForm() {\n    this.dynamicFormChange.emit(this.dynamicForm);\n  }\n  openFieldEditor(field) {\n    this.selectedField = field;\n    this.showFieldEditor = true;\n  }\n  updateField(field) {\n    const fieldIndex = this.dynamicForm.fieldsConfig.findIndex(f => f.key === field.key);\n    this.dynamicForm.fieldsConfig[fieldIndex] = field;\n    this.updateDynamicForm();\n  }\n  deleteField(field) {\n    this.dynamicForm.fieldsConfig = this.dynamicForm.fieldsConfig.filter(f => f.key != field.key);\n    this.updateDynamicForm();\n    this.dynamicFormsService.refreshFormElements.next();\n  }\n  drop() {\n    if (this.disabled || this.dynamicForm.customFieldsConfig) {\n      return;\n    }\n    if (!this.dynamicForm.fieldsConfig) this.dynamicForm.fieldsConfig = [];\n    this.dynamicForm.fieldsConfig.push(this.draggedField);\n    this.draggedField = null;\n    this.draggedFieldChange.emit(undefined);\n    this.updateDynamicForm();\n    this.dynamicFormsService.refreshFormElements.next();\n  }\n  moveFieldConfigUp(index) {\n    if (index > 0) {\n      [this.dynamicForm.fieldsConfig[index], this.dynamicForm.fieldsConfig[index - 1]] = [this.dynamicForm.fieldsConfig[index - 1], this.dynamicForm.fieldsConfig[index]];\n    }\n  }\n  moveFieldConfigDown(index) {\n    if (index < this.dynamicForm.fieldsConfig.length - 1) {\n      [this.dynamicForm.fieldsConfig[index], this.dynamicForm.fieldsConfig[index + 1]] = [this.dynamicForm.fieldsConfig[index + 1], this.dynamicForm.fieldsConfig[index]];\n    }\n  }\n  static {\n    this.ɵfac = function LayoutPreviewComponent_Factory(t) {\n      return new (t || LayoutPreviewComponent)(i0.ɵɵdirectiveInject(i1.DynamicFormsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LayoutPreviewComponent,\n      selectors: [[\"app-form-layout-preview\"]],\n      inputs: {\n        dynamicForm: \"dynamicForm\",\n        draggedField: \"draggedField\",\n        disabled: \"disabled\"\n      },\n      outputs: {\n        dynamicFormChange: \"dynamicFormChange\",\n        draggedFieldChange: \"draggedFieldChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 13,\n      vars: 6,\n      consts: [[1, \"card\", \"grid\"], [1, \"col-12\", \"p-0\"], [1, \"grid\", \"header-wrap\"], [\"class\", \"col pb-0 form-layout-header-text-editor\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"col pb-0 form-layout-header-text-editor\", 4, \"ngIf\"], [\"class\", \"col-12 drag-outline\", 3, \"dynamicForm\", \"layoutArea\", \"isPreview\", \"disabled\", \"contentChooserClick\", 4, \"ngIf\"], [\"pDroppable\", \"\", 1, \"drag-outline\", \"col-12\", \"assets-dropzone\", 3, \"onDrop\"], [1, \"section-controls\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"visible\", \"baseProperty\", \"field\", \"disabled\", \"visibleChange\", \"fieldChange\", 4, \"ngIf\"], [1, \"col\", \"pb-0\", \"form-layout-header-text-editor\", 3, \"ngClass\"], [1, \"col-12\", 3, \"contentChooserClick\", \"dynamicForm\", \"layoutArea\", \"isPreview\", \"disabled\"], [1, \"col\", \"pb-0\", \"form-layout-header-text-editor\"], [1, \"col-12\", \"drag-outline\", 3, \"contentChooserClick\", \"dynamicForm\", \"layoutArea\", \"isPreview\", \"disabled\"], [3, \"ngClass\"], [3, \"fieldsConfigChange\", \"fieldsConfig\", \"disabled\", \"baseSchema\", \"draggedField\"], [3, \"visibleChange\", \"fieldChange\", \"visible\", \"baseProperty\", \"field\", \"disabled\"]],\n      template: function LayoutPreviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, LayoutPreviewComponent_div_3_Template, 2, 7, \"div\", 3)(4, LayoutPreviewComponent_div_4_Template, 2, 4, \"div\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(5, LayoutPreviewComponent_app_dynamic_form_section_5_Template, 1, 4, \"app-dynamic-form-section\", 5);\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵlistener(\"onDrop\", function LayoutPreviewComponent_Template_div_onDrop_6_listener() {\n            return ctx.drop();\n          });\n          i0.ɵɵelementStart(7, \"div\", 7)(8, \"span\");\n          i0.ɵɵtext(9, \"Drag and Drop Form Elements Here\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(10, LayoutPreviewComponent_div_10_Template, 2, 7, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(11, LayoutPreviewComponent_app_dynamic_form_section_11_Template, 1, 4, \"app-dynamic-form-section\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, LayoutPreviewComponent_app_form_field_editor_12_Template, 1, 4, \"app-form-field-editor\", 9);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.dynamicForm.layout == null ? null : ctx.dynamicForm.layout.headerLeft == null ? null : ctx.dynamicForm.layout.headerLeft.visible);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dynamicForm.layout == null ? null : ctx.dynamicForm.layout.headerRight == null ? null : ctx.dynamicForm.layout.headerRight.visible);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dynamicForm.layout == null ? null : ctx.dynamicForm.layout.instruction == null ? null : ctx.dynamicForm.layout.instruction.visible);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.datastore);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dynamicForm.layout == null ? null : ctx.dynamicForm.layout.footer == null ? null : ctx.dynamicForm.layout.footer.visible);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showFieldEditor);\n        }\n      },\n      dependencies: [NgIf, NgClass, DynamicFormSectionComponent, DragDropModule, i2.Droppable, FormElementsRendererComponent, DynamicFormFieldEditorComponent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "DynamicFormLayoutAreas", "FormlyRendererTypes", "DynamicFormFieldEditorComponent", "FormElementsRendererComponent", "DragDropModule", "DynamicFormSectionComponent", "NgIf", "Ng<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵlistener", "LayoutPreviewComponent_div_3_Template_app_dynamic_form_section_contentChooserClick_1_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onClickContentChooser", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "dynamicForm", "layout", "headerLeft", "visible", "headerRight", "ɵɵadvance", "DynamicFormAreas", "HeaderLeft", "disabled", "LayoutPreviewComponent_div_4_Template_app_dynamic_form_section_contentChooserClick_1_listener", "_r3", "HeaderRight", "LayoutPreviewComponent_app_dynamic_form_section_5_Template_app_dynamic_form_section_contentChooserClick_0_listener", "_r4", "Instruction", "ɵɵtwoWayListener", "LayoutPreviewComponent_div_10_Template_app_form_elements_renderer_fieldsConfigChange_1_listener", "_r5", "ɵɵtwoWayBindingSet", "fieldsConfig", "_c1", "customFieldsConfig", "ɵɵtwoWayProperty", "datastore", "baseSchema", "<PERSON><PERSON><PERSON>", "LayoutPreviewComponent_app_dynamic_form_section_11_Template_app_dynamic_form_section_contentChooserClick_0_listener", "_r6", "Footer", "LayoutPreviewComponent_app_form_field_editor_12_Template_app_form_field_editor_visibleChange_0_listener", "_r7", "showFieldEditor", "LayoutPreviewComponent_app_form_field_editor_12_Template_app_form_field_editor_fieldChange_0_listener", "<PERSON><PERSON><PERSON>", "updateField", "properties", "key", "LayoutPreviewComponent", "constructor", "dynamicFormsService", "dynamicFormChange", "draggedFieldChange", "ngOnInit", "connectionSettingChanged", "subscribe", "params", "updateDynamicForm", "emit", "openFieldEditor", "field", "fieldIndex", "findIndex", "f", "deleteField", "filter", "refreshFormElements", "next", "drop", "push", "undefined", "moveFieldConfigUp", "index", "moveFieldConfigDown", "length", "ɵɵdirectiveInject", "i1", "DynamicFormsService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "LayoutPreviewComponent_Template", "rf", "ctx", "ɵɵtemplate", "LayoutPreviewComponent_div_3_Template", "LayoutPreviewComponent_div_4_Template", "LayoutPreviewComponent_app_dynamic_form_section_5_Template", "LayoutPreviewComponent_Template_div_onDrop_6_listener", "ɵɵtext", "LayoutPreviewComponent_div_10_Template", "LayoutPreviewComponent_app_dynamic_form_section_11_Template", "LayoutPreviewComponent_app_form_field_editor_12_Template", "instruction", "footer", "i2", "Droppable", "encapsulation"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\dynamic-forms\\dynamic-form-setup\\dynamic-form-designer\\layout-preview\\layout-preview.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\dynamic-forms\\dynamic-form-setup\\dynamic-form-designer\\layout-preview\\layout-preview.component.html"], "sourcesContent": ["import { Component, Input, EventEmitter, Output, OnInit } from '@angular/core';\r\nimport { DynamicForm } from '../../../models/dynamic-form';\r\nimport { DynamicFormsService } from '../../../services/dynamic-forms.service';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { Datastore } from 'src/app/shared/models/datastore';\r\nimport { DynamicFormLayoutAreas } from '../../../enums/dynamic-form';\r\nimport { FormlyRendererTypes } from 'src/app/sb-formly-renderer/enums/formly-renderer-types.enum';\r\nimport { DynamicFormFieldEditorComponent } from '../form-elements/field-editor/dynamic-form-field-editor.component';\r\nimport { FormElementsRendererComponent } from './form-elements-renderer/form-elements-renderer.component';\r\nimport { DragDropModule } from 'primeng/dragdrop';\r\nimport { DynamicFormSectionComponent } from '../../../dynamic-form-section/dynamic-form-section.component';\r\nimport { NgIf, NgClass } from '@angular/common';\r\n\r\n@Component({\r\n    selector: 'app-form-layout-preview',\r\n    templateUrl: './layout-preview.component.html',\r\n    standalone: true,\r\n    imports: [NgIf, NgClass, DynamicFormSectionComponent, DragDropModule, FormElementsRendererComponent, DynamicFormFieldEditorComponent]\r\n})\r\nexport class LayoutPreviewComponent implements OnInit {\r\n\r\n  @Input() dynamicForm: DynamicForm;\r\n  @Input() draggedField: FormlyFieldConfig;\r\n  @Input() disabled = false;\r\n\r\n  @Output() dynamicFormChange = new EventEmitter<DynamicForm>;\r\n  @Output() draggedFieldChange = new EventEmitter<FormlyFieldConfig>;\r\n\r\n  selectedField: FormlyFieldConfig;\r\n  datastore: Datastore;\r\n  showFieldEditor = false;\r\n \r\n  protected readonly FormlyRendererTypes = FormlyRendererTypes;\r\n  protected readonly DynamicFormAreas = DynamicFormLayoutAreas;\r\n\r\n  constructor(private dynamicFormsService: DynamicFormsService) { }\r\n\r\n  ngOnInit() {\r\n    this.dynamicFormsService.connectionSettingChanged.subscribe((params: { datastore: Datastore; resetLayout: boolean }) => {\r\n      this.datastore = params.datastore;\r\n    });\r\n  }\r\n\r\n  updateDynamicForm() {\r\n    this.dynamicFormChange.emit(this.dynamicForm);\r\n  }\r\n\r\n  openFieldEditor(field) {\r\n    this.selectedField = field;\r\n    this.showFieldEditor = true;\r\n  }\r\n\r\n  updateField(field) {\r\n    const fieldIndex = this.dynamicForm.fieldsConfig.findIndex(f => f.key === field.key);\r\n    this.dynamicForm.fieldsConfig[fieldIndex] = field;\r\n    this.updateDynamicForm();\r\n  }\r\n\r\n  deleteField(field) {\r\n    this.dynamicForm.fieldsConfig = this.dynamicForm.fieldsConfig.filter(f => f.key != field.key);\r\n\r\n    this.updateDynamicForm();\r\n    this.dynamicFormsService.refreshFormElements.next();\r\n  }\r\n\r\n  drop() {\r\n    if (this.disabled || this.dynamicForm.customFieldsConfig) {\r\n      return;\r\n    }\r\n\r\n    if (!this.dynamicForm.fieldsConfig)\r\n      this.dynamicForm.fieldsConfig = [];\r\n\r\n    this.dynamicForm.fieldsConfig.push(this.draggedField);\r\n    this.draggedField = null;\r\n    this.draggedFieldChange.emit(undefined);\r\n    this.updateDynamicForm();\r\n    this.dynamicFormsService.refreshFormElements.next();\r\n  }\r\n\r\n  moveFieldConfigUp(index: number): void {\r\n    if (index > 0) {\r\n      [this.dynamicForm.fieldsConfig[index], this.dynamicForm.fieldsConfig[index - 1]] = [this.dynamicForm.fieldsConfig[index - 1], this.dynamicForm.fieldsConfig[index]];\r\n    }\r\n  }\r\n\r\n  moveFieldConfigDown(index: number): void {\r\n    if (index < this.dynamicForm.fieldsConfig.length - 1) {\r\n      [this.dynamicForm.fieldsConfig[index], this.dynamicForm.fieldsConfig[index + 1]] = [this.dynamicForm.fieldsConfig[index + 1], this.dynamicForm.fieldsConfig[index]];\r\n    }\r\n  }\r\n}\r\n", "<div class=\"card grid\">\r\n    <div class=\"col-12 p-0\">\r\n        <div class=\"grid header-wrap\">\r\n            <div [ngClass]=\"{'mr-1': dynamicForm.layout?.headerLeft?.visible && dynamicForm.layout?.headerRight?.visible}\"\r\n                class=\"col pb-0 form-layout-header-text-editor\" *ngIf=\"dynamicForm.layout?.headerLeft?.visible\">\r\n                <app-dynamic-form-section [dynamicForm]=\"dynamicForm\" (contentChooserClick)=\"onClickContentChooser($event)\"\r\n                    [layoutArea]=\"DynamicFormAreas.HeaderLeft\" [isPreview]=\"true\" [disabled]=\"disabled\" class=\"col-12\">\r\n                </app-dynamic-form-section>\r\n            </div>\r\n\r\n            <div class=\"col pb-0 form-layout-header-text-editor\" *ngIf=\"dynamicForm.layout?.headerRight?.visible\">\r\n                <app-dynamic-form-section [dynamicForm]=\"dynamicForm\" (contentChooserClick)=\"onClickContentChooser($event)\"\r\n                    [layoutArea]=\"DynamicFormAreas.HeaderRight\" [isPreview]=\"true\" [disabled]=\"disabled\" class=\"col-12\">\r\n                </app-dynamic-form-section>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <app-dynamic-form-section class=\"col-12 drag-outline\" *ngIf=\"dynamicForm.layout?.instruction?.visible\"\r\n        [dynamicForm]=\"dynamicForm\" (contentChooserClick)=\"onClickContentChooser($event)\"\r\n        [layoutArea]=\"DynamicFormAreas.Instruction\" [isPreview]=\"true\" [disabled]=\"disabled\">\r\n    </app-dynamic-form-section>\r\n\r\n    <div class=\"drag-outline col-12 assets-dropzone\" pDroppable (onDrop)=\"drop()\">\r\n        <div class=\"section-controls\">\r\n            <span>Drag and Drop Form Elements Here</span>\r\n        </div>\r\n        <div *ngIf=\"datastore\" [ngClass]=\"{'disabled-element': dynamicForm.customFieldsConfig}\">\r\n            <app-form-elements-renderer [(fieldsConfig)]=\"this.dynamicForm.fieldsConfig\" [disabled]=\"disabled\"\r\n                [baseSchema]=\"datastore.baseSchema\" [draggedField]=\"draggedField\"></app-form-elements-renderer>\r\n        </div>\r\n    </div>\r\n    <app-dynamic-form-section class=\"col-12 drag-outline\" *ngIf=\"dynamicForm.layout?.footer?.visible\"\r\n        [dynamicForm]=\"dynamicForm\" (contentChooserClick)=\"onClickContentChooser($event)\"\r\n        [layoutArea]=\"DynamicFormAreas.Footer\" [isPreview]=\"true\" [disabled]=\"disabled\">\r\n    </app-dynamic-form-section>\r\n</div>\r\n\r\n<app-form-field-editor *ngIf=\"showFieldEditor\" [(visible)]=\"showFieldEditor\"\r\n    [baseProperty]=\"datastore?.baseSchema?.properties[selectedField?.key]\" [(field)]=\"selectedField\"\r\n    (fieldChange)=\"updateField($event)\" [disabled]=\"disabled\">\r\n</app-form-field-editor>"], "mappings": "AAAA,SAA2BA,YAAY,QAAwB,eAAe;AAK9E,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,mBAAmB,QAAQ,6DAA6D;AACjG,SAASC,+BAA+B,QAAQ,mEAAmE;AACnH,SAASC,6BAA6B,QAAQ,2DAA2D;AACzG,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,2BAA2B,QAAQ,8DAA8D;AAC1G,SAASC,IAAI,EAAEC,OAAO,QAAQ,iBAAiB;;;;;;;;;;;;;ICN/BC,EAFJ,CAAAC,cAAA,cACoG,mCAEO;IADjDD,EAAA,CAAAE,UAAA,iCAAAC,8FAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAuBF,MAAA,CAAAG,qBAAA,CAAAN,MAAA,CAA6B;IAAA,EAAC;IAG/GJ,EADI,CAAAW,YAAA,EAA2B,EACzB;;;;IALDX,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAa,eAAA,IAAAC,GAAA,GAAAP,MAAA,CAAAQ,WAAA,CAAAC,MAAA,kBAAAT,MAAA,CAAAQ,WAAA,CAAAC,MAAA,CAAAC,UAAA,kBAAAV,MAAA,CAAAQ,WAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,MAAAX,MAAA,CAAAQ,WAAA,CAAAC,MAAA,kBAAAT,MAAA,CAAAQ,WAAA,CAAAC,MAAA,CAAAG,WAAA,kBAAAZ,MAAA,CAAAQ,WAAA,CAAAC,MAAA,CAAAG,WAAA,CAAAD,OAAA,GAAyG;IAEhFlB,EAAA,CAAAoB,SAAA,EAA2B;IACapB,EADxC,CAAAY,UAAA,gBAAAL,MAAA,CAAAQ,WAAA,CAA2B,eAAAR,MAAA,CAAAc,gBAAA,CAAAC,UAAA,CACP,mBAAmB,aAAAf,MAAA,CAAAgB,QAAA,CAAsB;;;;;;IAKvFvB,EADJ,CAAAC,cAAA,cAAsG,mCAEM;IADlDD,EAAA,CAAAE,UAAA,iCAAAsB,8FAAApB,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAuBF,MAAA,CAAAG,qBAAA,CAAAN,MAAA,CAA6B;IAAA,EAAC;IAG/GJ,EADI,CAAAW,YAAA,EAA2B,EACzB;;;;IAHwBX,EAAA,CAAAoB,SAAA,EAA2B;IACcpB,EADzC,CAAAY,UAAA,gBAAAL,MAAA,CAAAQ,WAAA,CAA2B,eAAAR,MAAA,CAAAc,gBAAA,CAAAK,WAAA,CACN,mBAAmB,aAAAnB,MAAA,CAAAgB,QAAA,CAAsB;;;;;;IAMpGvB,EAAA,CAAAC,cAAA,mCAEyF;IADzDD,EAAA,CAAAE,UAAA,iCAAAyB,mHAAAvB,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAuBF,MAAA,CAAAG,qBAAA,CAAAN,MAAA,CAA6B;IAAA,EAAC;IAErFJ,EAAA,CAAAW,YAAA,EAA2B;;;;IADwCX,EAD/D,CAAAY,UAAA,gBAAAL,MAAA,CAAAQ,WAAA,CAA2B,eAAAR,MAAA,CAAAc,gBAAA,CAAAQ,WAAA,CACgB,mBAAmB,aAAAtB,MAAA,CAAAgB,QAAA,CAAsB;;;;;;IAQhFvB,EADJ,CAAAC,cAAA,cAAwF,qCAEd;IAD1CD,EAAA,CAAA8B,gBAAA,gCAAAC,gGAAA3B,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA2B,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAiC,kBAAA,CAAA1B,MAAA,CAAAQ,WAAA,CAAAmB,YAAA,EAAA9B,MAAA,MAAAG,MAAA,CAAAQ,WAAA,CAAAmB,YAAA,GAAA9B,MAAA;MAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;IAAA,EAAgD;IAEhFJ,EAD0E,CAAAW,YAAA,EAA6B,EACjG;;;;IAHiBX,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAa,eAAA,IAAAsB,GAAA,EAAA5B,MAAA,CAAAQ,WAAA,CAAAqB,kBAAA,EAAgE;IACvDpC,EAAA,CAAAoB,SAAA,EAAgD;IAAhDpB,EAAA,CAAAqC,gBAAA,iBAAA9B,MAAA,CAAAQ,WAAA,CAAAmB,YAAA,CAAgD;IACpClC,EADqC,CAAAY,UAAA,aAAAL,MAAA,CAAAgB,QAAA,CAAqB,eAAAhB,MAAA,CAAA+B,SAAA,CAAAC,UAAA,CAC3D,iBAAAhC,MAAA,CAAAiC,YAAA,CAA8B;;;;;;IAG7ExC,EAAA,CAAAC,cAAA,mCAEoF;IADpDD,EAAA,CAAAE,UAAA,iCAAAuC,oHAAArC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAuBF,MAAA,CAAAG,qBAAA,CAAAN,MAAA,CAA6B;IAAA,EAAC;IAErFJ,EAAA,CAAAW,YAAA,EAA2B;;;;IADmCX,EAD1D,CAAAY,UAAA,gBAAAL,MAAA,CAAAQ,WAAA,CAA2B,eAAAR,MAAA,CAAAc,gBAAA,CAAAsB,MAAA,CACW,mBAAmB,aAAApC,MAAA,CAAAgB,QAAA,CAAsB;;;;;;IAIvFvB,EAAA,CAAAC,cAAA,gCAE8D;IADaD,EAD5B,CAAA8B,gBAAA,2BAAAc,wGAAAxC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAiC,kBAAA,CAAA1B,MAAA,CAAAuC,eAAA,EAAA1C,MAAA,MAAAG,MAAA,CAAAuC,eAAA,GAAA1C,MAAA;MAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;IAAA,EAA6B,yBAAA2C,sGAAA3C,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAAiC,kBAAA,CAAA1B,MAAA,CAAAyC,aAAA,EAAA5C,MAAA,MAAAG,MAAA,CAAAyC,aAAA,GAAA5C,MAAA;MAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;IAAA,EACwB;IAChGJ,EAAA,CAAAE,UAAA,yBAAA6C,sGAAA3C,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAwC,GAAA;MAAA,MAAAtC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAeF,MAAA,CAAA0C,WAAA,CAAA7C,MAAA,CAAmB;IAAA,EAAC;IACvCJ,EAAA,CAAAW,YAAA,EAAwB;;;;IAHuBX,EAAA,CAAAqC,gBAAA,YAAA9B,MAAA,CAAAuC,eAAA,CAA6B;IACxE9C,EAAA,CAAAY,UAAA,iBAAAL,MAAA,CAAA+B,SAAA,kBAAA/B,MAAA,CAAA+B,SAAA,CAAAC,UAAA,kBAAAhC,MAAA,CAAA+B,SAAA,CAAAC,UAAA,CAAAW,UAAA,CAAA3C,MAAA,CAAAyC,aAAA,kBAAAzC,MAAA,CAAAyC,aAAA,CAAAG,GAAA,EAAsE;IAACnD,EAAA,CAAAqC,gBAAA,UAAA9B,MAAA,CAAAyC,aAAA,CAAyB;IAC5DhD,EAAA,CAAAY,UAAA,aAAAL,MAAA,CAAAgB,QAAA,CAAqB;;;ADrB7D,OAAM,MAAO6B,sBAAsB;EAgBjCC,YAAoBC,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;IAZ9B,KAAA/B,QAAQ,GAAG,KAAK;IAEf,KAAAgC,iBAAiB,GAAG,IAAIhE,YAAyB,CAAzB,CAAyB;IACjD,KAAAiE,kBAAkB,GAAG,IAAIjE,YAA+B,CAA/B,CAA+B;IAIlE,KAAAuD,eAAe,GAAG,KAAK;IAEJ,KAAArD,mBAAmB,GAAGA,mBAAmB;IACzC,KAAA4B,gBAAgB,GAAG7B,sBAAsB;EAEI;EAEhEiE,QAAQA,CAAA;IACN,IAAI,CAACH,mBAAmB,CAACI,wBAAwB,CAACC,SAAS,CAAEC,MAAsD,IAAI;MACrH,IAAI,CAACtB,SAAS,GAAGsB,MAAM,CAACtB,SAAS;IACnC,CAAC,CAAC;EACJ;EAEAuB,iBAAiBA,CAAA;IACf,IAAI,CAACN,iBAAiB,CAACO,IAAI,CAAC,IAAI,CAAC/C,WAAW,CAAC;EAC/C;EAEAgD,eAAeA,CAACC,KAAK;IACnB,IAAI,CAAChB,aAAa,GAAGgB,KAAK;IAC1B,IAAI,CAAClB,eAAe,GAAG,IAAI;EAC7B;EAEAG,WAAWA,CAACe,KAAK;IACf,MAAMC,UAAU,GAAG,IAAI,CAAClD,WAAW,CAACmB,YAAY,CAACgC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAChB,GAAG,KAAKa,KAAK,CAACb,GAAG,CAAC;IACpF,IAAI,CAACpC,WAAW,CAACmB,YAAY,CAAC+B,UAAU,CAAC,GAAGD,KAAK;IACjD,IAAI,CAACH,iBAAiB,EAAE;EAC1B;EAEAO,WAAWA,CAACJ,KAAK;IACf,IAAI,CAACjD,WAAW,CAACmB,YAAY,GAAG,IAAI,CAACnB,WAAW,CAACmB,YAAY,CAACmC,MAAM,CAACF,CAAC,IAAIA,CAAC,CAAChB,GAAG,IAAIa,KAAK,CAACb,GAAG,CAAC;IAE7F,IAAI,CAACU,iBAAiB,EAAE;IACxB,IAAI,CAACP,mBAAmB,CAACgB,mBAAmB,CAACC,IAAI,EAAE;EACrD;EAEAC,IAAIA,CAAA;IACF,IAAI,IAAI,CAACjD,QAAQ,IAAI,IAAI,CAACR,WAAW,CAACqB,kBAAkB,EAAE;MACxD;IACF;IAEA,IAAI,CAAC,IAAI,CAACrB,WAAW,CAACmB,YAAY,EAChC,IAAI,CAACnB,WAAW,CAACmB,YAAY,GAAG,EAAE;IAEpC,IAAI,CAACnB,WAAW,CAACmB,YAAY,CAACuC,IAAI,CAAC,IAAI,CAACjC,YAAY,CAAC;IACrD,IAAI,CAACA,YAAY,GAAG,IAAI;IACxB,IAAI,CAACgB,kBAAkB,CAACM,IAAI,CAACY,SAAS,CAAC;IACvC,IAAI,CAACb,iBAAiB,EAAE;IACxB,IAAI,CAACP,mBAAmB,CAACgB,mBAAmB,CAACC,IAAI,EAAE;EACrD;EAEAI,iBAAiBA,CAACC,KAAa;IAC7B,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,CAAC,IAAI,CAAC7D,WAAW,CAACmB,YAAY,CAAC0C,KAAK,CAAC,EAAE,IAAI,CAAC7D,WAAW,CAACmB,YAAY,CAAC0C,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC7D,WAAW,CAACmB,YAAY,CAAC0C,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC7D,WAAW,CAACmB,YAAY,CAAC0C,KAAK,CAAC,CAAC;IACrK;EACF;EAEAC,mBAAmBA,CAACD,KAAa;IAC/B,IAAIA,KAAK,GAAG,IAAI,CAAC7D,WAAW,CAACmB,YAAY,CAAC4C,MAAM,GAAG,CAAC,EAAE;MACpD,CAAC,IAAI,CAAC/D,WAAW,CAACmB,YAAY,CAAC0C,KAAK,CAAC,EAAE,IAAI,CAAC7D,WAAW,CAACmB,YAAY,CAAC0C,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC7D,WAAW,CAACmB,YAAY,CAAC0C,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC7D,WAAW,CAACmB,YAAY,CAAC0C,KAAK,CAAC,CAAC;IACrK;EACF;;;uBAvEWxB,sBAAsB,EAAApD,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAtB7B,sBAAsB;MAAA8B,SAAA;MAAAC,MAAA;QAAApE,WAAA;QAAAyB,YAAA;QAAAjB,QAAA;MAAA;MAAA6D,OAAA;QAAA7B,iBAAA;QAAAC,kBAAA;MAAA;MAAA6B,UAAA;MAAAC,QAAA,GAAAtF,EAAA,CAAAuF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjB3B7F,EAFR,CAAAC,cAAA,aAAuB,aACK,aACU;UAQ1BD,EAPA,CAAA+F,UAAA,IAAAC,qCAAA,iBACoG,IAAAC,qCAAA,iBAME;UAM9GjG,EADI,CAAAW,YAAA,EAAM,EACJ;UAENX,EAAA,CAAA+F,UAAA,IAAAG,0DAAA,sCAEyF;UAGzFlG,EAAA,CAAAC,cAAA,aAA8E;UAAlBD,EAAA,CAAAE,UAAA,oBAAAiG,sDAAA;YAAA,OAAUL,GAAA,CAAAtB,IAAA,EAAM;UAAA,EAAC;UAErExE,EADJ,CAAAC,cAAA,aAA8B,WACpB;UAAAD,EAAA,CAAAoG,MAAA,uCAAgC;UAC1CpG,EAD0C,CAAAW,YAAA,EAAO,EAC3C;UACNX,EAAA,CAAA+F,UAAA,KAAAM,sCAAA,iBAAwF;UAI5FrG,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAA+F,UAAA,KAAAO,2DAAA,sCAEoF;UAExFtG,EAAA,CAAAW,YAAA,EAAM;UAENX,EAAA,CAAA+F,UAAA,KAAAQ,wDAAA,mCAE8D;;;UApCGvG,EAAA,CAAAoB,SAAA,GAA6C;UAA7CpB,EAAA,CAAAY,UAAA,SAAAkF,GAAA,CAAA/E,WAAA,CAAAC,MAAA,kBAAA8E,GAAA,CAAA/E,WAAA,CAAAC,MAAA,CAAAC,UAAA,kBAAA6E,GAAA,CAAA/E,WAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAA6C;UAM5ClB,EAAA,CAAAoB,SAAA,EAA8C;UAA9CpB,EAAA,CAAAY,UAAA,SAAAkF,GAAA,CAAA/E,WAAA,CAAAC,MAAA,kBAAA8E,GAAA,CAAA/E,WAAA,CAAAC,MAAA,CAAAG,WAAA,kBAAA2E,GAAA,CAAA/E,WAAA,CAAAC,MAAA,CAAAG,WAAA,CAAAD,OAAA,CAA8C;UAQrDlB,EAAA,CAAAoB,SAAA,EAA8C;UAA9CpB,EAAA,CAAAY,UAAA,SAAAkF,GAAA,CAAA/E,WAAA,CAAAC,MAAA,kBAAA8E,GAAA,CAAA/E,WAAA,CAAAC,MAAA,CAAAwF,WAAA,kBAAAV,GAAA,CAAA/E,WAAA,CAAAC,MAAA,CAAAwF,WAAA,CAAAtF,OAAA,CAA8C;UAS3FlB,EAAA,CAAAoB,SAAA,GAAe;UAAfpB,EAAA,CAAAY,UAAA,SAAAkF,GAAA,CAAAxD,SAAA,CAAe;UAK8BtC,EAAA,CAAAoB,SAAA,EAAyC;UAAzCpB,EAAA,CAAAY,UAAA,SAAAkF,GAAA,CAAA/E,WAAA,CAAAC,MAAA,kBAAA8E,GAAA,CAAA/E,WAAA,CAAAC,MAAA,CAAAyF,MAAA,kBAAAX,GAAA,CAAA/E,WAAA,CAAAC,MAAA,CAAAyF,MAAA,CAAAvF,OAAA,CAAyC;UAM5ElB,EAAA,CAAAoB,SAAA,EAAqB;UAArBpB,EAAA,CAAAY,UAAA,SAAAkF,GAAA,CAAAhD,eAAA,CAAqB;;;qBDrB/BhD,IAAI,EAAEC,OAAO,EAAEF,2BAA2B,EAAED,cAAc,EAAA8G,EAAA,CAAAC,SAAA,EAAEhH,6BAA6B,EAAED,+BAA+B;MAAAkH,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}