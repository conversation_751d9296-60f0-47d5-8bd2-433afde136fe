{"ast": null, "code": "const loader = {\n  width: {\n    value: '{fontSizes.medium.value}'\n  },\n  height: {\n    value: '{fontSizes.medium.value}'\n  },\n  fontSize: {\n    value: '{fontSizes.xs.value}'\n  },\n  strokeEmpty: {\n    value: '{colors.neutral.20.value}'\n  },\n  strokeFilled: {\n    value: '{colors.primary.80.value}'\n  },\n  strokeLinecap: {\n    value: 'round'\n  },\n  animationDuration: {\n    value: '1s'\n  },\n  small: {\n    width: {\n      value: '{fontSizes.small.value}'\n    },\n    height: {\n      value: '{fontSizes.small.value}'\n    },\n    fontSize: {\n      value: '{fontSizes.xxs.value}'\n    }\n  },\n  large: {\n    width: {\n      value: '{fontSizes.large.value}'\n    },\n    height: {\n      value: '{fontSizes.large.value}'\n    },\n    fontSize: {\n      value: '{fontSizes.small.value}'\n    }\n  },\n  linear: {\n    width: {\n      value: '100%'\n    },\n    minWidth: {\n      value: '5rem'\n    },\n    fontSize: {\n      value: '{fontSizes.xxs.value}'\n    },\n    strokeWidth: {\n      value: '{fontSizes.xxs.value}'\n    },\n    strokeFilled: {\n      value: '{colors.primary.80.value}'\n    },\n    strokeEmpty: {\n      value: '{colors.neutral.20.value}'\n    },\n    strokeLinecap: {\n      value: 'round'\n    },\n    animationDuration: {\n      value: '1s'\n    },\n    small: {\n      strokeWidth: {\n        value: '{fontSizes.xxxs.value}'\n      },\n      fontSize: {\n        value: '{fontSizes.xxxs.value}'\n      }\n    },\n    large: {\n      strokeWidth: {\n        value: '{fontSizes.xs.value}'\n      },\n      fontSize: {\n        value: '{fontSizes.xs.value}'\n      }\n    }\n  },\n  text: {\n    fill: {\n      value: '{colors.font.primary.value}'\n    }\n  }\n};\nexport { loader };", "map": {"version": 3, "names": ["loader", "width", "value", "height", "fontSize", "strokeEmpty", "strokeFilled", "strokeLinecap", "animationDuration", "small", "large", "linear", "min<PERSON><PERSON><PERSON>", "strokeWidth", "text", "fill"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/loader.mjs"], "sourcesContent": ["const loader = {\n    width: { value: '{fontSizes.medium.value}' },\n    height: { value: '{fontSizes.medium.value}' },\n    fontSize: { value: '{fontSizes.xs.value}' },\n    strokeEmpty: { value: '{colors.neutral.20.value}' },\n    strokeFilled: { value: '{colors.primary.80.value}' },\n    strokeLinecap: { value: 'round' },\n    animationDuration: { value: '1s' },\n    small: {\n        width: { value: '{fontSizes.small.value}' },\n        height: { value: '{fontSizes.small.value}' },\n        fontSize: { value: '{fontSizes.xxs.value}' },\n    },\n    large: {\n        width: { value: '{fontSizes.large.value}' },\n        height: { value: '{fontSizes.large.value}' },\n        fontSize: { value: '{fontSizes.small.value}' },\n    },\n    linear: {\n        width: { value: '100%' },\n        minWidth: { value: '5rem' },\n        fontSize: { value: '{fontSizes.xxs.value}' },\n        strokeWidth: { value: '{fontSizes.xxs.value}' },\n        strokeFilled: { value: '{colors.primary.80.value}' },\n        strokeEmpty: { value: '{colors.neutral.20.value}' },\n        strokeLinecap: { value: 'round' },\n        animationDuration: { value: '1s' },\n        small: {\n            strokeWidth: { value: '{fontSizes.xxxs.value}' },\n            fontSize: { value: '{fontSizes.xxxs.value}' },\n        },\n        large: {\n            strokeWidth: { value: '{fontSizes.xs.value}' },\n            fontSize: { value: '{fontSizes.xs.value}' },\n        },\n    },\n    text: {\n        fill: { value: '{colors.font.primary.value}' },\n    },\n};\n\nexport { loader };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACXC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAA2B,CAAC;EAC5CC,MAAM,EAAE;IAAED,KAAK,EAAE;EAA2B,CAAC;EAC7CE,QAAQ,EAAE;IAAEF,KAAK,EAAE;EAAuB,CAAC;EAC3CG,WAAW,EAAE;IAAEH,KAAK,EAAE;EAA4B,CAAC;EACnDI,YAAY,EAAE;IAAEJ,KAAK,EAAE;EAA4B,CAAC;EACpDK,aAAa,EAAE;IAAEL,KAAK,EAAE;EAAQ,CAAC;EACjCM,iBAAiB,EAAE;IAAEN,KAAK,EAAE;EAAK,CAAC;EAClCO,KAAK,EAAE;IACHR,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA0B,CAAC;IAC3CC,MAAM,EAAE;MAAED,KAAK,EAAE;IAA0B,CAAC;IAC5CE,QAAQ,EAAE;MAAEF,KAAK,EAAE;IAAwB;EAC/C,CAAC;EACDQ,KAAK,EAAE;IACHT,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA0B,CAAC;IAC3CC,MAAM,EAAE;MAAED,KAAK,EAAE;IAA0B,CAAC;IAC5CE,QAAQ,EAAE;MAAEF,KAAK,EAAE;IAA0B;EACjD,CAAC;EACDS,MAAM,EAAE;IACJV,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IACxBU,QAAQ,EAAE;MAAEV,KAAK,EAAE;IAAO,CAAC;IAC3BE,QAAQ,EAAE;MAAEF,KAAK,EAAE;IAAwB,CAAC;IAC5CW,WAAW,EAAE;MAAEX,KAAK,EAAE;IAAwB,CAAC;IAC/CI,YAAY,EAAE;MAAEJ,KAAK,EAAE;IAA4B,CAAC;IACpDG,WAAW,EAAE;MAAEH,KAAK,EAAE;IAA4B,CAAC;IACnDK,aAAa,EAAE;MAAEL,KAAK,EAAE;IAAQ,CAAC;IACjCM,iBAAiB,EAAE;MAAEN,KAAK,EAAE;IAAK,CAAC;IAClCO,KAAK,EAAE;MACHI,WAAW,EAAE;QAAEX,KAAK,EAAE;MAAyB,CAAC;MAChDE,QAAQ,EAAE;QAAEF,KAAK,EAAE;MAAyB;IAChD,CAAC;IACDQ,KAAK,EAAE;MACHG,WAAW,EAAE;QAAEX,KAAK,EAAE;MAAuB,CAAC;MAC9CE,QAAQ,EAAE;QAAEF,KAAK,EAAE;MAAuB;IAC9C;EACJ,CAAC;EACDY,IAAI,EAAE;IACFC,IAAI,EAAE;MAAEb,KAAK,EAAE;IAA8B;EACjD;AACJ,CAAC;AAED,SAASF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}