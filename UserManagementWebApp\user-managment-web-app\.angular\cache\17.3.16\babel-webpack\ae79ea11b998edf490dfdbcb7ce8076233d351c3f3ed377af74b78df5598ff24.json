{"ast": null, "code": "import { getCrypto } from './globalHelpers/index.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/*\n * Cryptographically secure pseudorandom number generator\n * As Math.random() is cryptographically not safe to use\n */\nfunction cryptoSecureRandomInt() {\n  const crypto = getCrypto();\n  const randomResult = crypto.getRandomValues(new Uint32Array(1))[0];\n  return randomResult;\n}\nexport { cryptoSecureRandomInt };", "map": {"version": 3, "names": ["getCrypto", "cryptoSecureRandomInt", "crypto", "randomResult", "getRandomValues", "Uint32Array"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/cryptoSecureRandomInt.mjs"], "sourcesContent": ["import { getCrypto } from './globalHelpers/index.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/*\n * Cryptographically secure pseudorandom number generator\n * As Math.random() is cryptographically not safe to use\n */\nfunction cryptoSecureRandomInt() {\n    const crypto = getCrypto();\n    const randomResult = crypto.getRandomValues(new Uint32Array(1))[0];\n    return randomResult;\n}\n\nexport { cryptoSecureRandomInt };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,2BAA2B;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAAA,EAAG;EAC7B,MAAMC,MAAM,GAAGF,SAAS,CAAC,CAAC;EAC1B,MAAMG,YAAY,GAAGD,MAAM,CAACE,eAAe,CAAC,IAAIC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClE,OAAOF,YAAY;AACvB;AAEA,SAASF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}