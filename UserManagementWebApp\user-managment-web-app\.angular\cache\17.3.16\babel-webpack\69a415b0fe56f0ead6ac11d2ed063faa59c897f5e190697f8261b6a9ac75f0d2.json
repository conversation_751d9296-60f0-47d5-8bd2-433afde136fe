{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { inject, ViewContainerRef, Component, Injectable, NgZone, EventEmitter, booleanAttribute, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { _removeFromParent, BaseComponentWrapper, VanillaFrameworkOverrides, _combineAttributesAndGridOptions, createGrid, _processOnChange, _BOOLEAN_MIXED_GRID_OPTIONS } from 'ag-grid-community';\n\n// To speed up the removal of custom components we create a number of shards to contain them.\n// Removing a single component calls a function within Angular called removeFromArray.\n// This is a lot faster if the array is smaller.\nclass AgComponentContainer {\n  constructor() {\n    this.vcr = inject(ViewContainerRef);\n  }\n  static {\n    this.ɵfac = function AgComponentContainer_Factory(t) {\n      return new (t || AgComponentContainer)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: AgComponentContainer,\n      selectors: [[\"ag-component-container\"]],\n      decls: 0,\n      vars: 0,\n      template: function AgComponentContainer_Template(rf, ctx) {},\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AgComponentContainer, [{\n    type: Component,\n    args: [{\n      selector: 'ag-component-container',\n      template: ''\n    }]\n  }], null, null);\n})();\nconst NUM_SHARDS = 16;\nlet shardIdx = 0;\nfunction createComponentContainers(vcr) {\n  const containerMap = new Map();\n  for (let i = 0; i < NUM_SHARDS; i++) {\n    const container = vcr.createComponent(AgComponentContainer);\n    containerMap.set(i, container);\n    _removeFromParent(container.location.nativeElement);\n  }\n  return containerMap;\n}\n/**\n * These methods are called on a hot path for every row so we do not want to enter / exit NgZone each time.\n * Also these methods should not be used to update the UI, so we don't need to run them inside Angular.\n */\nconst runOutsideMethods = new Set(['doesFilterPass', 'isFilterActive']);\nclass AngularFrameworkComponentWrapper extends BaseComponentWrapper {\n  setViewContainerRef(viewContainerRef, angularFrameworkOverrides) {\n    this.viewContainerRef = viewContainerRef;\n    this.angularFrameworkOverrides = angularFrameworkOverrides;\n  }\n  createWrapper(OriginalConstructor) {\n    const angularFrameworkOverrides = this.angularFrameworkOverrides;\n    const that = this;\n    that.compShards ??= createComponentContainers(this.viewContainerRef);\n    class DynamicAgNg2Component extends BaseGuiComponent {\n      init(params) {\n        angularFrameworkOverrides.runInsideAngular(() => {\n          super.init(params);\n          this._componentRef.changeDetectorRef.detectChanges();\n        });\n      }\n      createComponent() {\n        return that.createComponent(OriginalConstructor);\n      }\n      hasMethod(name) {\n        return wrapper.getFrameworkComponentInstance()[name] != null;\n      }\n      callMethod(name, args) {\n        const componentRef = this.getFrameworkComponentInstance();\n        const methodCall = componentRef[name];\n        if (runOutsideMethods.has(name)) {\n          return methodCall.apply(componentRef, args);\n        }\n        return angularFrameworkOverrides.runInsideAngular(() => methodCall.apply(componentRef, args));\n      }\n      addMethod(name, callback) {\n        wrapper[name] = callback;\n      }\n    }\n    const wrapper = new DynamicAgNg2Component();\n    return wrapper;\n  }\n  createComponent(componentType) {\n    shardIdx = (shardIdx + 1) % NUM_SHARDS;\n    const container = this.compShards.get(shardIdx);\n    return container.instance.vcr.createComponent(componentType);\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵAngularFrameworkComponentWrapper_BaseFactory;\n      return function AngularFrameworkComponentWrapper_Factory(t) {\n        return (ɵAngularFrameworkComponentWrapper_BaseFactory || (ɵAngularFrameworkComponentWrapper_BaseFactory = i0.ɵɵgetInheritedFactory(AngularFrameworkComponentWrapper)))(t || AngularFrameworkComponentWrapper);\n      };\n    })();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AngularFrameworkComponentWrapper,\n      factory: AngularFrameworkComponentWrapper.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularFrameworkComponentWrapper, [{\n    type: Injectable\n  }], null, null);\n})();\nclass BaseGuiComponent {\n  init(params) {\n    this._params = params;\n    this._componentRef = this.createComponent();\n    this._agAwareComponent = this._componentRef.instance;\n    this._frameworkComponentInstance = this._componentRef.instance;\n    this._eGui = this._componentRef.location.nativeElement;\n    // Angular appends the component to the DOM, so remove it\n    _removeFromParent(this._eGui);\n    this._agAwareComponent.agInit(this._params);\n  }\n  getGui() {\n    return this._eGui;\n  }\n  /** `getGui()` returns the `ng-component` element. This returns the actual root element. */\n  getRootElement() {\n    const firstChild = this._eGui.firstChild;\n    return firstChild;\n  }\n  destroy() {\n    if (this._frameworkComponentInstance && typeof this._frameworkComponentInstance.destroy === 'function') {\n      this._frameworkComponentInstance.destroy();\n    }\n    this._componentRef?.destroy();\n  }\n  getFrameworkComponentInstance() {\n    return this._frameworkComponentInstance;\n  }\n}\nclass AngularFrameworkEventListenerService {\n  constructor(frameworkOverrides) {\n    this.frameworkOverrides = frameworkOverrides;\n    // Map from user listener to wrapped listener so we can remove listener provided by user\n    this.wrappedListeners = new Map();\n    this.wrappedGlobalListeners = new Map();\n  }\n  wrap(eventType, userListener) {\n    const {\n      frameworkOverrides,\n      wrappedListeners\n    } = this;\n    let listener = userListener;\n    if (frameworkOverrides.shouldWrapOutgoing) {\n      listener = event => {\n        frameworkOverrides.wrapOutgoing(() => userListener(event));\n      };\n      let eventListeners = wrappedListeners.get(eventType);\n      if (!eventListeners) {\n        eventListeners = new Map();\n        wrappedListeners.set(eventType, eventListeners);\n      }\n      eventListeners.set(userListener, listener);\n    }\n    return listener;\n  }\n  wrapGlobal(userListener) {\n    const {\n      frameworkOverrides,\n      wrappedGlobalListeners\n    } = this;\n    let listener = userListener;\n    if (frameworkOverrides.shouldWrapOutgoing) {\n      listener = (eventType, event) => {\n        frameworkOverrides.wrapOutgoing(() => userListener(eventType, event));\n      };\n      wrappedGlobalListeners.set(userListener, listener);\n    }\n    return listener;\n  }\n  unwrap(eventType, userListener) {\n    const {\n      wrappedListeners\n    } = this;\n    const eventListeners = wrappedListeners.get(eventType);\n    if (eventListeners) {\n      const wrapped = eventListeners.get(userListener);\n      if (wrapped) {\n        eventListeners.delete(userListener);\n        if (eventListeners.size === 0) {\n          wrappedListeners.delete(eventType);\n        }\n        return wrapped;\n      }\n    }\n    return userListener;\n  }\n  unwrapGlobal(userListener) {\n    const {\n      wrappedGlobalListeners\n    } = this;\n    const wrapped = wrappedGlobalListeners.get(userListener);\n    if (wrapped) {\n      wrappedGlobalListeners.delete(userListener);\n      return wrapped;\n    }\n    return userListener;\n  }\n}\nclass AngularFrameworkOverrides extends VanillaFrameworkOverrides {\n  constructor(_ngZone) {\n    super('angular');\n    this._ngZone = _ngZone;\n    this.batchFrameworkComps = true;\n    // Flag used to control Zone behaviour when running tests as many test features rely on Zone.\n    this.isRunningWithinTestZone = false;\n    // Make all events run outside Angular as they often trigger the setup of event listeners\n    // By having the event listeners outside Angular we can avoid triggering change detection\n    // This also means that if a user calls an AG Grid API method from within their component\n    // the internal side effects will not trigger change detection. Without this the events would\n    // run inside Angular and trigger change detection as the source of the event was within the angular zone.\n    this.wrapIncoming = (callback, source) => this.runOutside(callback, source);\n    /**\n     * Make sure that any code that is executed outside of AG Grid is running within the Angular zone.\n     * This means users can update templates and use binding without having to do anything extra.\n     */\n    this.wrapOutgoing = callback => this.runInsideAngular(callback);\n    this.isRunningWithinTestZone = window?.AG_GRID_UNDER_TEST ?? !!window?.Zone?.AsyncTestZoneSpec;\n    if (!this._ngZone) {\n      this.runOutside = callback => callback();\n    } else if (this.isRunningWithinTestZone) {\n      this.runOutside = (callback, source) => {\n        if (source === 'resize-observer' || source === 'popupPositioning') {\n          // ensure resize observer callbacks are run outside of Angular even under test due to Jest not supporting ResizeObserver\n          // which means it just loops continuously with a setTimeout with no way to flush the queue or have fixture.whenStable() resolve.\n          return this._ngZone.runOutsideAngular(callback);\n        }\n        // When under test run inside Angular so that tests can use fixture.whenStable() to wait for async operations to complete.\n        return callback();\n      };\n    } else {\n      this.runOutside = callback => this._ngZone.runOutsideAngular(callback);\n    }\n  }\n  /**\n   * The shouldWrapOutgoing property is used to determine if events should be run outside of Angular or not.\n   * If an event handler is registered outside of Angular then we should not wrap the event handler\n   * with runInsideAngular() as the user may not have wanted this.\n   * This is also used to not wrap internal event listeners that are registered with RowNodes and Columns.\n   */\n  get shouldWrapOutgoing() {\n    return this._ngZone && NgZone.isInAngularZone();\n  }\n  createLocalEventListenerWrapper(existingFrameworkEventListenerService, localEventService) {\n    if (this.shouldWrapOutgoing) {\n      return existingFrameworkEventListenerService ?? (() => {\n        localEventService.setFrameworkOverrides(this);\n        return new AngularFrameworkEventListenerService(this);\n      })();\n    }\n    return undefined;\n  }\n  createGlobalEventListenerWrapper() {\n    return new AngularFrameworkEventListenerService(this);\n  }\n  isFrameworkComponent(comp) {\n    if (!comp) {\n      return false;\n    }\n    const prototype = comp.prototype;\n    return prototype && 'agInit' in prototype;\n  }\n  runInsideAngular(callback) {\n    if (!this._ngZone || NgZone.isInAngularZone()) {\n      return callback();\n    }\n    // Check for _ngZone existence as it is not present when Zoneless\n    return this._ngZone.run(callback);\n  }\n  runOutsideAngular(callback, source) {\n    return this.runOutside(callback, source);\n  }\n  static {\n    this.ɵfac = function AngularFrameworkOverrides_Factory(t) {\n      return new (t || AngularFrameworkOverrides)(i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AngularFrameworkOverrides,\n      factory: AngularFrameworkOverrides.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularFrameworkOverrides, [{\n    type: Injectable\n  }], () => [{\n    type: i0.NgZone\n  }], null);\n})();\n\n// False positive lint error, ElementRef and co can't be type imports\n// eslint-disable-next-line @typescript-eslint/consistent-type-imports\nclass AgGridAngular {\n  constructor(elementDef, _viewContainerRef, _angularFrameworkOverrides, _frameworkCompWrapper) {\n    this._viewContainerRef = _viewContainerRef;\n    this._angularFrameworkOverrides = _angularFrameworkOverrides;\n    this._frameworkCompWrapper = _frameworkCompWrapper;\n    this._initialised = false;\n    this._destroyed = false;\n    // in order to ensure firing of gridReady is deterministic\n    this._holdEvents = true;\n    this._fullyReady = new Promise(resolve => {\n      this._resolveFullyReady = resolve;\n    });\n    // @START@\n    /** Specifies the status bar components to use in the status bar.\n     * @agModule `StatusBarModule`\n     */\n    this.statusBar = undefined;\n    /** Specifies the side bar components.\n     * @agModule `SideBarModule`\n     */\n    this.sideBar = undefined;\n    /** Set to `true` to not show the context menu. Use if you don't want to use the default 'right click' context menu.\n     * @default false\n     */\n    this.suppressContextMenu = undefined;\n    /** When using `suppressContextMenu`, you can use the `onCellContextMenu` function to provide your own code to handle cell `contextmenu` events.\n     * This flag is useful to prevent the browser from showing its default context menu.\n     * @default false\n     */\n    this.preventDefaultOnContextMenu = undefined;\n    /** Allows context menu to show, even when `Ctrl` key is held down.\n     * @default false\n     * @agModule `ContextMenuModule`\n     */\n    this.allowContextMenuWithControlKey = undefined;\n    /** Changes the display type of the column menu.\n     * `'new'` just displays the main list of menu items. `'legacy'` displays a tabbed menu.\n     * @default 'new'\n     * @initial\n     */\n    this.columnMenu = undefined;\n    /** Only recommended for use if `columnMenu = 'legacy'`.\n     * When `true`, the column menu button will always be shown.\n     * When `false`, the column menu button will only show when the mouse is over the column header.\n     * When using `columnMenu = 'legacy'`, this will default to `false` instead of `true`.\n     * @default true\n     */\n    this.suppressMenuHide = undefined;\n    /** Set to `true` to use the browser's default tooltip instead of using the grid's Tooltip Component.\n     * @default false\n     * @initial\n     * @agModule `TooltipModule`\n     */\n    this.enableBrowserTooltips = undefined;\n    /** The trigger that will cause tooltips to show and hide.\n     *  - `hover` - The tooltip will show/hide when a cell/header is hovered.\n     *  - `focus` - The tooltip will show/hide when a cell/header is focused.\n     * @default 'hover'\n     * @initial\n     * @agModule `TooltipModule`\n     */\n    this.tooltipTrigger = undefined;\n    /** The delay in milliseconds that it takes for tooltips to show up once an element is hovered over.\n     *     **Note:** This property does not work if `enableBrowserTooltips` is `true`.\n     * @default 2000\n     * @agModule `TooltipModule`\n     */\n    this.tooltipShowDelay = undefined;\n    /** The delay in milliseconds that it takes for tooltips to hide once they have been displayed.\n     *     **Note:** This property does not work if `enableBrowserTooltips` is `true` and `tooltipHideTriggers` includes `timeout`.\n     * @default 10000\n     * @agModule `TooltipModule`\n     */\n    this.tooltipHideDelay = undefined;\n    /** Set to `true` to have tooltips follow the cursor once they are displayed.\n     * @default false\n     * @initial\n     * @agModule `TooltipModule`\n     */\n    this.tooltipMouseTrack = undefined;\n    /** This defines when tooltip will show up for Cells, Headers and SetFilter Items.\n     *  - `standard` - The tooltip always shows up when the items configured with Tooltips are hovered.\n     * - `whenTruncated` - The tooltip will only be displayed when the items hovered have truncated (showing ellipsis) values. This property does not work when `enableBrowserTooltips={true}`.\n     * @default `standard`\n     * @agModule `TooltipModule`\n     */\n    this.tooltipShowMode = undefined;\n    /** Set to `true` to enable tooltip interaction. When this option is enabled, the tooltip will not hide while the\n     * tooltip itself it being hovered or has focus.\n     * @default false\n     * @initial\n     * @agModule `TooltipModule`\n     */\n    this.tooltipInteraction = undefined;\n    /** DOM element to use as the popup parent for grid popups (context menu, column menu etc).\n     */\n    this.popupParent = undefined;\n    /** Set to `true` to also include headers when copying to clipboard using `Ctrl + C` clipboard.\n     * @default false\n     * @agModule `ClipboardModule`\n     */\n    this.copyHeadersToClipboard = undefined;\n    /** Set to `true` to also include group headers when copying to clipboard using `Ctrl + C` clipboard.\n     * @default false\n     * @agModule `ClipboardModule`\n     */\n    this.copyGroupHeadersToClipboard = undefined;\n    /** Specify the delimiter to use when copying to clipboard.\n     * @default '\\t'\n     * @agModule `ClipboardModule`\n     */\n    this.clipboardDelimiter = undefined;\n    /** Set to `true` to copy the cell range or focused cell to the clipboard and never the selected rows.\n     * @default false\n     * @deprecated v32.2 Use `rowSelection.copySelectedRows` instead.\n     */\n    this.suppressCopyRowsToClipboard = undefined;\n    /** Set to `true` to copy rows instead of ranges when a range with only a single cell is selected.\n     * @default false\n     * @deprecated v32.2 Use `rowSelection.copySelectedRows` instead.\n     */\n    this.suppressCopySingleCellRanges = undefined;\n    /** Set to `true` to work around a bug with Excel (Windows) that adds an extra empty line at the end of ranges copied to the clipboard.\n     * @default false\n     * @agModule `ClipboardModule`\n     */\n    this.suppressLastEmptyLineOnPaste = undefined;\n    /** Set to `true` to turn off paste operations within the grid.\n     * @default false\n     * @agModule `ClipboardModule`\n     */\n    this.suppressClipboardPaste = undefined;\n    /** Set to `true` to stop the grid trying to use the Clipboard API, if it is blocked, and immediately fallback to the workaround.\n     * @default false\n     * @agModule `ClipboardModule`\n     */\n    this.suppressClipboardApi = undefined;\n    /** Set to `true` to block     **cut** operations within the grid.\n     * @default false\n     * @agModule `ClipboardModule`\n     */\n    this.suppressCutToClipboard = undefined;\n    /** Array of Column / Column Group definitions.\n     */\n    this.columnDefs = undefined;\n    /** A default column definition. Items defined in the actual column definitions get precedence.\n     */\n    this.defaultColDef = undefined;\n    /** A default column group definition. All column group definitions will use these properties. Items defined in the actual column group definition get precedence.\n     * @initial\n     */\n    this.defaultColGroupDef = undefined;\n    /** An object map of custom column types which contain groups of properties that column definitions can reuse by referencing in their `type` property.\n     */\n    this.columnTypes = undefined;\n    /** An object map of cell data types to their definitions.\n     * Cell data types can either override/update the pre-defined data types\n     * (`'text'`, `'number'`,  `'boolean'`,  `'date'`,  `'dateString'` or  `'object'`),\n     * or can be custom data types.\n     */\n    this.dataTypeDefinitions = undefined;\n    /** Keeps the order of Columns maintained after new Column Definitions are updated.\n     *\n     * @default false\n     */\n    this.maintainColumnOrder = undefined;\n    /** Resets pivot column order when impacted by filters, data or configuration changes\n     *\n     * @default false\n     * @agModule `PivotModule`\n     */\n    this.enableStrictPivotColumnOrder = undefined;\n    /** If `true`, then dots in field names (e.g. `'address.firstLine'`) are not treated as deep references. Allows you to use dots in your field name if you prefer.\n     * @default false\n     */\n    this.suppressFieldDotNotation = undefined;\n    /** The height in pixels for the row containing the column label header. If not specified, it uses the theme value of `header-height`.\n     */\n    this.headerHeight = undefined;\n    /** The height in pixels for the rows containing header column groups. If not specified, it uses `headerHeight`.\n     */\n    this.groupHeaderHeight = undefined;\n    /** The height in pixels for the row containing the floating filters. If not specified, it uses the theme value of `header-height`.\n     */\n    this.floatingFiltersHeight = undefined;\n    /** The height in pixels for the row containing the columns when in pivot mode. If not specified, it uses `headerHeight`.\n     */\n    this.pivotHeaderHeight = undefined;\n    /** The height in pixels for the row containing header column groups when in pivot mode. If not specified, it uses `groupHeaderHeight`.\n     */\n    this.pivotGroupHeaderHeight = undefined;\n    /** Allow reordering and pinning columns by dragging columns from the Columns Tool Panel to the grid.\n     * @default false\n     * @agModule `ColumnsToolPanelModule`\n     */\n    this.allowDragFromColumnsToolPanel = undefined;\n    /** Set to `true` to suppress column moving, i.e. to make the columns fixed position.\n     * @default false\n     */\n    this.suppressMovableColumns = undefined;\n    /** If `true`, the `ag-column-moving` class is not added to the grid while columns are moving. In the default themes, this results in no animation when moving columns.\n     * @default false\n     */\n    this.suppressColumnMoveAnimation = undefined;\n    /** Set to `true` to suppress moving columns while dragging the Column Header. This option highlights the position where the column will be placed and it will only move it on mouse up.\n     * @default false\n     */\n    this.suppressMoveWhenColumnDragging = undefined;\n    /** If `true`, when you drag a column out of the grid (e.g. to the group zone) the column is not hidden.\n     * @default false\n     */\n    this.suppressDragLeaveHidesColumns = undefined;\n    /** Enable to prevent column visibility changing when grouped columns are changed.\n     * @default false\n     */\n    this.suppressGroupChangesColumnVisibility = undefined;\n    /** By default, when a column is un-grouped, i.e. using the Row Group Panel, it is made visible in the grid. This property stops the column becoming visible again when un-grouping.\n     * @default false\n     * @deprecated v33.0.0 - Use `suppressGroupChangesColumnVisibility: 'suppressShowOnUngroup'` instead.\n     */\n    this.suppressMakeColumnVisibleAfterUnGroup = undefined;\n    /** If `true`, when you drag a column into a row group panel the column is not hidden.\n     * @default false\n     * @deprecated v33.0.0 - Use `suppressGroupChangesColumnVisibility: 'suppressHideOnGroup'` instead.\n     */\n    this.suppressRowGroupHidesColumns = undefined;\n    /** Set to `'shift'` to have shift-resize as the default resize operation (same as user holding down `Shift` while resizing).\n     */\n    this.colResizeDefault = undefined;\n    /** Suppresses auto-sizing columns for columns. In other words, double clicking a column's header's edge will not auto-size.\n     * @default false\n     * @initial\n     */\n    this.suppressAutoSize = undefined;\n    /** Number of pixels to add to a column width after the [auto-sizing](./column-sizing/#auto-size-columns-to-fit-cell-contents) calculation.\n     * Set this if you want to add extra room to accommodate (for example) sort icons, or some other dynamic nature of the header.\n     * @default 20\n     */\n    this.autoSizePadding = undefined;\n    /** Set this to `true` to skip the `headerName` when `autoSize` is called by default.\n     * @default false\n     * @initial\n     * @agModule `ColumnAutoSizeModule`\n     */\n    this.skipHeaderOnAutoSize = undefined;\n    /** Auto-size the columns when the grid is loaded. Can size to fit the grid width, fit a provided width, or fit the cell contents.\n     * @initial\n     * @agModule `ColumnAutoSizeModule`\n     */\n    this.autoSizeStrategy = undefined;\n    /** A map of component names to components.\n     * @initial\n     */\n    this.components = undefined;\n    /** Set to `'fullRow'` to enable Full Row Editing. Otherwise leave blank to edit one cell at a time.\n     * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n     */\n    this.editType = undefined;\n    /** Set to `true` to enable Single Click Editing for cells, to start editing with a single click.\n     * @default false\n     * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n     */\n    this.singleClickEdit = undefined;\n    /** Set to `true` so that neither single nor double click starts editing.\n     * @default false\n     * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n     */\n    this.suppressClickEdit = undefined;\n    /** Set to `true` to stop the grid updating data after `Edit`, `Clipboard` and `Fill Handle` operations. When this is set, it is intended the application will update the data, eg in an external immutable store, and then pass the new dataset to the grid. <br />**Note:** `rowNode.setDataValue()` does not update the value of the cell when this is `True`, it fires `onCellEditRequest` instead.\n     * @default false\n     * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n     */\n    this.readOnlyEdit = undefined;\n    /** Set this to `true` to stop cell editing when grid loses focus.\n     * The default is that the grid stays editing until focus goes onto another cell.\n     * @default false\n     * @initial\n     * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n     */\n    this.stopEditingWhenCellsLoseFocus = undefined;\n    /** Set to `true` along with `enterNavigatesVerticallyAfterEdit` to have Excel-style behaviour for the `Enter` key.\n     * i.e. pressing the `Enter` key will move down to the cell beneath and `Shift+Enter` will move up to the cell above.\n     * @default false\n     * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n     */\n    this.enterNavigatesVertically = undefined;\n    /** Set to `true` along with `enterNavigatesVertically` to have Excel-style behaviour for the 'Enter' key.\n     * i.e. pressing the Enter key will move down to the cell beneath and Shift+Enter key will move up to the cell above.\n     * @default false\n     * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n     */\n    this.enterNavigatesVerticallyAfterEdit = undefined;\n    /** Forces Cell Editing to start when backspace is pressed. This is only relevant for MacOS users.\n     * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n     */\n    this.enableCellEditingOnBackspace = undefined;\n    /** Set to `true` to enable Undo / Redo while editing.\n     * @initial\n     * @agModule `UndoRedoEditModule`\n     */\n    this.undoRedoCellEditing = undefined;\n    /** Set the size of the undo / redo stack.\n     * @default 10\n     * @initial\n     * @agModule `UndoRedoEditModule`\n     */\n    this.undoRedoCellEditingLimit = undefined;\n    /** A default configuration object used to export to CSV.\n     * @agModule `CsvExportModule`\n     */\n    this.defaultCsvExportParams = undefined;\n    /** Prevents the user from exporting the grid to CSV.\n     * @default false\n     */\n    this.suppressCsvExport = undefined;\n    /** A default configuration object used to export to Excel.\n     * @agModule `ExcelExportModule`\n     */\n    this.defaultExcelExportParams = undefined;\n    /** Prevents the user from exporting the grid to Excel.\n     * @default false\n     */\n    this.suppressExcelExport = undefined;\n    /** A list (array) of Excel styles to be used when exporting to Excel with styles.\n     * @initial\n     * @agModule `ExcelExportModule`\n     */\n    this.excelStyles = undefined;\n    /** Text to find within the grid.\n     * @agModule `FindModule`\n     */\n    this.findSearchValue = undefined;\n    /** Options for the Find feature.\n     * @agModule `FindModule`\n     */\n    this.findOptions = undefined;\n    /** Rows are filtered using this text as a Quick Filter.\n     * Only supported for Client-Side Row Model.\n     * @agModule `QuickFilterModule`\n     */\n    this.quickFilterText = undefined;\n    /** Set to `true` to turn on the Quick Filter cache, used to improve performance when using the Quick Filter.\n     * @default false\n     * @initial\n     * @agModule `QuickFilterModule`\n     */\n    this.cacheQuickFilter = undefined;\n    /** Hidden columns are excluded from the Quick Filter by default.\n     * To include hidden columns, set to `true`.\n     * @default false\n     * @agModule `QuickFilterModule`\n     */\n    this.includeHiddenColumnsInQuickFilter = undefined;\n    /** Changes how the Quick Filter splits the Quick Filter text into search terms.\n     * @agModule `QuickFilterModule`\n     */\n    this.quickFilterParser = undefined;\n    /** Changes the matching logic for whether a row passes the Quick Filter.\n     * @agModule `QuickFilterModule`\n     */\n    this.quickFilterMatcher = undefined;\n    /** When pivoting, Quick Filter is only applied on the pivoted data\n     * (or aggregated data if `groupAggFiltering = true`).\n     * Set to `true` to apply Quick Filter before pivoting (/aggregating) instead.\n     * @default false\n     * @agModule `QuickFilterModule`\n     */\n    this.applyQuickFilterBeforePivotOrAgg = undefined;\n    /** Set to `true` to override the default tree data filtering behaviour to instead exclude child nodes from filter results.\n     * @default false\n     * @agModule `TreeDataModule`\n     */\n    this.excludeChildrenWhenTreeDataFiltering = undefined;\n    /** Set to true to enable the Advanced Filter.\n     * @default false\n     * @agModule `AdvancedFilterModule`\n     */\n    this.enableAdvancedFilter = undefined;\n    /** Allows rows to always be displayed, even if they don't match the applied filtering.\n     * Return `true` for the provided row to always be displayed.\n     * Only works with the Client-Side Row Model.\n     * @agModule `TextFilterModule` / `NumberFilterModule` / `DateFilterModule` / `SetFilterModule` / `MultiFilterModule` / `CustomFilterModule` / `QuickFilterModule` / `ExternalFilterModule` / `AdvancedFilterModule`\n     */\n    this.alwaysPassFilter = undefined;\n    /** Hidden columns are excluded from the Advanced Filter by default.\n     * To include hidden columns, set to `true`.\n     * @default false\n     * @agModule `AdvancedFilterModule`\n     */\n    this.includeHiddenColumnsInAdvancedFilter = undefined;\n    /** DOM element to use as the parent for the Advanced Filter to allow it to appear outside of the grid.\n     * Set to `null` or `undefined` to appear inside the grid.\n     * @agModule `AdvancedFilterModule`\n     */\n    this.advancedFilterParent = undefined;\n    /** Customise the parameters passed to the Advanced Filter Builder.\n     * @agModule `AdvancedFilterModule`\n     */\n    this.advancedFilterBuilderParams = undefined;\n    /** By default, Advanced Filter sanitises user input and passes it to `new Function()` to provide the best performance.\n     * Set to `true` to prevent this and use defined functions instead.\n     * This will result in slower filtering, but it enables Advanced Filter to work when `unsafe-eval` is disabled.\n     * @default false\n     * @agModule `AdvancedFilterModule`\n     */\n    this.suppressAdvancedFilterEval = undefined;\n    /** When using AG Grid Enterprise, the Set Filter is used by default when `filter: true` is set on column definitions.\n     * Set to `true` to prevent this and instead use the Text Filter, Number Filter or Date Filter based on the cell data type,\n     * the same as when using AG Grid Community.\n     * @default false\n     * @initial\n     * @agModule TextFilterModule / NumberFilterModule / DateFilterModule / MultiFilterModule / CustomFilterModule\n     */\n    this.suppressSetFilterByDefault = undefined;\n    /** Set to `true` to Enable Charts.\n     * @default false\n     * @agModule `IntegratedChartsModule`\n     */\n    this.enableCharts = undefined;\n    /** The list of chart themes that a user can choose from in the chart panel.\n     * @default ['ag-default', 'ag-material', 'ag-sheets', 'ag-polychroma', 'ag-vivid'];\n     * @initial\n     * @agModule `IntegratedChartsModule`\n     */\n    this.chartThemes = undefined;\n    /** A map containing custom chart themes.\n     * @initial\n     * @agModule `IntegratedChartsModule`\n     */\n    this.customChartThemes = undefined;\n    /** Chart theme overrides applied to all themes.\n     * @initial\n     * @agModule `IntegratedChartsModule`\n     */\n    this.chartThemeOverrides = undefined;\n    /** Allows customisation of the Chart Tool Panels, such as changing the tool panels visibility and order, as well as choosing which charts should be displayed in the chart panel.\n     * @initial\n     * @agModule `IntegratedChartsModule`\n     */\n    this.chartToolPanelsDef = undefined;\n    /** Get chart menu items. Only applies when using AG Charts Enterprise.\n     * @agModule `IntegratedChartsModule`\n     */\n    this.chartMenuItems = undefined;\n    /** Provide your own loading cell renderer to use when data is loading via a DataSource.\n     * See [Loading Cell Renderer](https://www.ag-grid.com/javascript-data-grid/component-loading-cell-renderer/) for framework specific implementation details.\n     */\n    this.loadingCellRenderer = undefined;\n    /** Params to be passed to the `loadingCellRenderer` component.\n     */\n    this.loadingCellRendererParams = undefined;\n    /** Callback to select which loading cell renderer to be used when data is loading via a DataSource.\n     * @initial\n     */\n    this.loadingCellRendererSelector = undefined;\n    /** A map of key->value pairs for localising text within the grid.\n     * @initial\n     * @agModule `LocaleModule`\n     */\n    this.localeText = undefined;\n    /** Set to `true` to enable Master Detail.\n     * @default false\n     * @agModule `MasterDetailModule`\n     */\n    this.masterDetail = undefined;\n    /** Set to `true` to keep detail rows for when they are displayed again.\n     * @default false\n     * @initial\n     * @agModule `MasterDetailModule`\n     */\n    this.keepDetailRows = undefined;\n    /** Sets the number of details rows to keep.\n     * @default 10\n     * @initial\n     * @agModule `MasterDetailModule`\n     */\n    this.keepDetailRowsCount = undefined;\n    /** Provide a custom `detailCellRenderer` to use when a master row is expanded.\n     * See [Detail Cell Renderer](https://www.ag-grid.com/javascript-data-grid/master-detail-custom-detail/) for framework specific implementation details.\n     * @agModule `MasterDetailModule`\n     */\n    this.detailCellRenderer = undefined;\n    /** Specifies the params to be used by the Detail Cell Renderer. Can also be a function that provides the params to enable dynamic definitions of the params.\n     * @agModule `MasterDetailModule`\n     */\n    this.detailCellRendererParams = undefined;\n    /** Set fixed height in pixels for each detail row.\n     * @initial\n     * @agModule `MasterDetailModule`\n     */\n    this.detailRowHeight = undefined;\n    /** Set to `true` to have the detail grid dynamically change it's height to fit it's rows.\n     * @initial\n     * @agModule `MasterDetailModule`\n     */\n    this.detailRowAutoHeight = undefined;\n    /** Provides a context object that is provided to different callbacks the grid uses. Used for passing additional information to the callbacks used by your application.\n     * @initial\n     */\n    this.context = undefined;\n    /**\n     * A list of grids to treat as Aligned Grids.\n     * Provide a list if the grids / apis already exist or return via a callback to allow the aligned grids to be retrieved asynchronously.\n     * If grids are aligned then the columns and horizontal scrolling will be kept in sync.\n     * @agModule `AlignedGridsModule`\n     */\n    this.alignedGrids = undefined;\n    /** Change this value to set the tabIndex order of the Grid within your application.\n     * @default 0\n     * @initial\n     */\n    this.tabIndex = undefined;\n    /** The number of rows rendered outside the viewable area the grid renders.\n     * Having a buffer means the grid will have rows ready to show as the user slowly scrolls vertically.\n     * @default 10\n     */\n    this.rowBuffer = undefined;\n    /** Set to `true` to turn on the value cache.\n     * @default false\n     * @initial\n     * @agModule `ValueCacheModule`\n     */\n    this.valueCache = undefined;\n    /** Set to `true` to configure the value cache to not expire after data updates.\n     * @default false\n     * @initial\n     * @agModule `ValueCacheModule`\n     */\n    this.valueCacheNeverExpires = undefined;\n    /** Set to `true` to allow cell expressions.\n     * @default false\n     * @initial\n     */\n    this.enableCellExpressions = undefined;\n    /** Disables touch support (but does not remove the browser's efforts to simulate mouse events on touch).\n     * @default false\n     * @initial\n     */\n    this.suppressTouch = undefined;\n    /** Set to `true` to not set focus back on the grid after a refresh. This can avoid issues where you want to keep the focus on another part of the browser.\n     * @default false\n     */\n    this.suppressFocusAfterRefresh = undefined;\n    /** @deprecated As of v32.2 the grid always uses the browser's ResizeObserver, this grid option has no effect\n     * @default false\n     * @initial\n     */\n    this.suppressBrowserResizeObserver = undefined;\n    /** @deprecated As of v33 `gridOptions` and `columnDefs` both have a `context` property that should be used for arbitrary user data. This means that column definitions and gridOptions should only contain valid properties making this property redundant.\n     * @default false\n     * @initial\n     */\n    this.suppressPropertyNamesCheck = undefined;\n    /** Disables change detection.\n     * @default false\n     */\n    this.suppressChangeDetection = undefined;\n    /** Set this to `true` to enable debug information from the grid and related components. Will result in additional logging being output, but very useful when investigating problems.\n     * It is also recommended to register the `ValidationModule` to identify any misconfigurations.\n     * @default false\n     * @initial\n     */\n    this.debug = undefined;\n    /** Show or hide the loading overlay.\n     */\n    this.loading = undefined;\n    /** Provide a HTML string to override the default loading overlay. Supports non-empty plain text or HTML with a single root element.\n     */\n    this.overlayLoadingTemplate = undefined;\n    /** Provide a custom loading overlay component.\n     * @initial\n     */\n    this.loadingOverlayComponent = undefined;\n    /** Customise the parameters provided to the loading overlay component.\n     */\n    this.loadingOverlayComponentParams = undefined;\n    /** Disables the 'loading' overlay.\n     * @deprecated v32 - Deprecated. Use `loading=false` instead.\n     * @default false\n     * @initial\n     */\n    this.suppressLoadingOverlay = undefined;\n    /** Provide a HTML string to override the default no-rows overlay. Supports non-empty plain text or HTML with a single root element.\n     */\n    this.overlayNoRowsTemplate = undefined;\n    /** Provide a custom no-rows overlay component.\n     * @initial\n     */\n    this.noRowsOverlayComponent = undefined;\n    /** Customise the parameters provided to the no-rows overlay component.\n     */\n    this.noRowsOverlayComponentParams = undefined;\n    /** Set to `true` to prevent the no-rows overlay being shown when there is no row data.\n     * @default false\n     * @initial\n     */\n    this.suppressNoRowsOverlay = undefined;\n    /** Set whether pagination is enabled.\n     * @default false\n     * @agModule `PaginationModule`\n     */\n    this.pagination = undefined;\n    /** How many rows to load per page. If `paginationAutoPageSize` is specified, this property is ignored.\n     * @default 100\n     * @agModule `PaginationModule`\n     */\n    this.paginationPageSize = undefined;\n    /** Determines if the page size selector is shown in the pagination panel or not.\n     * Set to an array of values to show the page size selector with custom list of possible page sizes.\n     * Set to `true` to show the page size selector with the default page sizes `[20, 50, 100]`.\n     * Set to `false` to hide the page size selector.\n     * @default true\n     * @initial\n     * @agModule `PaginationModule`\n     */\n    this.paginationPageSizeSelector = undefined;\n    /** Set to `true` so that the number of rows to load per page is automatically adjusted by the grid so each page shows enough rows to just fill the area designated for the grid. If `false`, `paginationPageSize` is used.\n     * @default false\n     * @agModule `PaginationModule`\n     */\n    this.paginationAutoPageSize = undefined;\n    /** Set to `true` to have pages split children of groups when using Row Grouping or detail rows with Master Detail.\n     * @default false\n     * @initial\n     * @agModule `PaginationModule`\n     */\n    this.paginateChildRows = undefined;\n    /** If `true`, the default grid controls for navigation are hidden.\n     * This is useful if `pagination=true` and you want to provide your own pagination controls.\n     * Otherwise, when `pagination=true` the grid automatically shows the necessary controls at the bottom so that the user can navigate through the different pages.\n     * @default false\n     * @agModule `PaginationModule`\n     */\n    this.suppressPaginationPanel = undefined;\n    /** Set to `true` to enable pivot mode.\n     * @default false\n     * @agModule `PivotModule`\n     */\n    this.pivotMode = undefined;\n    /** When to show the 'pivot panel' (where you drag rows to pivot) at the top. Note that the pivot panel will never show if `pivotMode` is off.\n     * @default 'never'\n     * @initial\n     * @agModule `RowGroupingPanelModule`\n     */\n    this.pivotPanelShow = undefined;\n    /** The maximum number of generated columns before the grid halts execution. Upon reaching this number, the grid halts generation of columns\n     * and triggers a `pivotMaxColumnsExceeded` event. `-1` for no limit.\n     * @default -1\n     * @agModule `PivotModule`\n     */\n    this.pivotMaxGeneratedColumns = undefined;\n    /** If pivoting, set to the number of column group levels to expand by default, e.g. `0` for none, `1` for first level only, etc. Set to `-1` to expand everything.\n     * @default 0\n     * @agModule `PivotModule`\n     */\n    this.pivotDefaultExpanded = undefined;\n    /** When set and the grid is in pivot mode, automatically calculated totals will appear within the Pivot Column Groups, in the position specified.\n     * @agModule `PivotModule`\n     */\n    this.pivotColumnGroupTotals = undefined;\n    /** When set and the grid is in pivot mode, automatically calculated totals will appear for each value column in the position specified.\n     * @agModule `PivotModule`\n     */\n    this.pivotRowTotals = undefined;\n    /** If `true`, the grid will not swap in the grouping column when pivoting. Useful if pivoting using Server Side Row Model or Viewport Row Model and you want full control of all columns including the group column.\n     * @default false\n     * @initial\n     * @agModule `PivotModule`\n     */\n    this.pivotSuppressAutoColumn = undefined;\n    /** When enabled, pivot column groups will appear 'fixed', without the ability to expand and collapse the column groups.\n     * @default false\n     * @initial\n     * @agModule `PivotModule`\n     */\n    this.suppressExpandablePivotGroups = undefined;\n    /** If `true`, then row group, pivot and value aggregation will be read-only from the GUI. The grid will display what values are used for each, but will not allow the user to change the selection.\n     * @default false\n     * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n     */\n    this.functionsReadOnly = undefined;\n    /** A map of 'function name' to 'function' for custom aggregation functions.\n     * @initial\n     * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n     */\n    this.aggFuncs = undefined;\n    /** When `true`, column headers won't include the `aggFunc` name, e.g. `'sum(Bank Balance)`' will just be `'Bank Balance'`.\n     * @default false\n     * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n     */\n    this.suppressAggFuncInHeader = undefined;\n    /** When using aggregations, the grid will always calculate the root level aggregation value.\n     * @default false\n     * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n     */\n    this.alwaysAggregateAtRootLevel = undefined;\n    /** When using change detection, only the updated column will be re-aggregated.\n     * @default false\n     * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n     */\n    this.aggregateOnlyChangedColumns = undefined;\n    /** Set to `true` so that aggregations are not impacted by filtering.\n     * @default false\n     * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n     */\n    this.suppressAggFilteredOnly = undefined;\n    /** Set to `true` to omit the value Column header when there is only a single value column.\n     * @default false\n     * @agModule `PivotModule`\n     */\n    this.removePivotHeaderRowWhenSingleValueColumn = undefined;\n    /** Set to `false` to disable Row Animation which is enabled by default.\n     * @default true\n     */\n    this.animateRows = undefined;\n    /** Sets the duration in milliseconds of how long a cell should remain in its \"flashed\" state.\n     * If `0`, the cell will not flash.\n     * @default 500\n     */\n    this.cellFlashDuration = undefined;\n    /** Sets the duration in milliseconds of how long the \"flashed\" state animation takes to fade away after the timer set by `cellFlashDuration` has completed.\n     * @default 1000\n     */\n    this.cellFadeDuration = undefined;\n    /** Set to `true` to have cells flash after data changes even when the change is due to filtering.\n     * @default false\n     * @initial\n     */\n    this.allowShowChangeAfterFilter = undefined;\n    /** Switch between layout options: `normal`, `autoHeight`, `print`.\n     * @default 'normal'\n     */\n    this.domLayout = undefined;\n    /** When `true`, the order of rows and columns in the DOM are consistent with what is on screen.\n     * Disables row animations.\n     * @default false\n     * @initial\n     */\n    this.ensureDomOrder = undefined;\n    /** When `true`, enables the cell span feature allowing for the use of the `colDef.spanRows` property.\n     * @default false\n     * @initial\n     * @agModule `CellSpanModule`\n     */\n    this.enableCellSpan = undefined;\n    /** Set to `true` to operate the grid in RTL (Right to Left) mode.\n     * @default false\n     * @initial\n     */\n    this.enableRtl = undefined;\n    /** Set to `true` so that the grid doesn't virtualise the columns. For example, if you have 100 columns, but only 10 visible due to scrolling, all 100 will always be rendered.\n     *     **It is not recommended to set this to `true` as it may cause performance issues.**\n     * @default false\n     * @initial\n     */\n    this.suppressColumnVirtualisation = undefined;\n    /** By default the grid has a limit of rendering a maximum of 500 rows at once (remember the grid only renders rows you can see, so unless your display shows more than 500 rows without vertically scrolling this will never be an issue).\n     * <br />**This is only relevant if you are manually setting `rowBuffer` to a high value (rendering more rows than can be seen), or `suppressRowVirtualisation` is true, or if your grid height is able to display more than 500 rows at once.**\n     * @default false\n     * @initial\n     */\n    this.suppressMaxRenderedRowRestriction = undefined;\n    /** Set to `true` so that the grid doesn't virtualise the rows. For example, if you have 100 rows, but only 10 visible due to scrolling, all 100 will always be rendered.\n     *     **It is not recommended to set this to `true` as it may cause performance issues.**\n     * @default false\n     * @initial\n     */\n    this.suppressRowVirtualisation = undefined;\n    /** Set to `true` to enable Managed Row Dragging.\n     * @default false\n     * @agModule `RowDragModule`\n     */\n    this.rowDragManaged = undefined;\n    /** Set to `true` to suppress row dragging.\n     * @default false\n     */\n    this.suppressRowDrag = undefined;\n    /** Set to `true` to suppress moving rows while dragging the `rowDrag` waffle. This option highlights the position where the row will be placed and it will only move the row on mouse up.\n     * @default false\n     * @agModule `RowDragModule`\n     */\n    this.suppressMoveWhenRowDragging = undefined;\n    /** Set to `true` to enable clicking and dragging anywhere on the row without the need for a drag handle.\n     * @default false\n     * @agModule `RowDragModule`\n     */\n    this.rowDragEntireRow = undefined;\n    /** Set to `true` to enable dragging multiple rows at the same time.\n     * @default false\n     * @agModule `RowDragModule`\n     */\n    this.rowDragMultiRow = undefined;\n    /** A callback that should return a string to be displayed by the `rowDragComp` while dragging a row.\n     * If this callback is not set, the current cell value will be used.\n     * If the `rowDragText` callback is set in the ColDef it will take precedence over this, except when\n     * `rowDragEntireRow=true`.\n     * @initial\n     * @agModule `RowDragModule`\n     */\n    this.rowDragText = undefined;\n    /** Provide a custom drag and drop image component.\n     * @initial\n     * @agModule `RowDragModule`\n     */\n    this.dragAndDropImageComponent = undefined;\n    /** Customise the parameters provided to the Drag and Drop Image Component.\n     * @agModule `RowDragModule`\n     */\n    this.dragAndDropImageComponentParams = undefined;\n    /** Provide your own cell renderer component to use for full width rows.\n     * See [Full Width Rows](https://www.ag-grid.com/javascript-data-grid/full-width-rows/) for framework specific implementation details.\n     */\n    this.fullWidthCellRenderer = undefined;\n    /** Customise the parameters provided to the `fullWidthCellRenderer` component.\n     */\n    this.fullWidthCellRendererParams = undefined;\n    /** Set to `true` to have the Full Width Rows embedded in grid's main container so they can be scrolled horizontally.\n     */\n    this.embedFullWidthRows = undefined;\n    /** Specifies how the results of row grouping should be displayed.\n     *\n     *  The options are:\n     *\n     * - `'singleColumn'`: single group column automatically added by the grid.\n     * - `'multipleColumns'`: a group column per row group is added automatically.\n     * - `'groupRows'`: group rows are automatically added instead of group columns.\n     * - `'custom'`: informs the grid that group columns will be provided.\n     * @agModule `RowGroupingModule`\n     */\n    this.groupDisplayType = undefined;\n    /** If grouping, set to the number of levels to expand by default, e.g. `0` for none, `1` for first level only, etc. Set to `-1` to expand everything.\n     * @default 0\n     * @agModule `RowGroupingModule` / `TreeDataModule`\n     */\n    this.groupDefaultExpanded = undefined;\n    /** Allows specifying the group 'auto column' if you are not happy with the default. If grouping, this column definition is included as the first column in the grid. If not grouping, this column is not included.\n     * @agModule `RowGroupingModule` / `TreeDataModule`\n     */\n    this.autoGroupColumnDef = undefined;\n    /** When `true`, preserves the current group order when sorting on non-group columns.\n     * @default false\n     * @agModule `RowGroupingModule`\n     */\n    this.groupMaintainOrder = undefined;\n    /** When `true`, if you select a group, the children of the group will also be selected.\n     * @default false\n     * @deprecated v32.2 Use `rowSelection.groupSelects` instead\n     */\n    this.groupSelectsChildren = undefined;\n    /** If grouping, locks the group settings of a number of columns, e.g. `0` for no group locking. `1` for first group column locked, `-1` for all group columns locked.\n     * @default 0\n     * @initial\n     * @agModule `RowGroupingModule`\n     */\n    this.groupLockGroupColumns = undefined;\n    /** Set to determine whether filters should be applied on aggregated group values.\n     * @default false\n     * @agModule `RowGroupingModule`\n     */\n    this.groupAggFiltering = undefined;\n    /** When provided, an extra row group total row will be inserted into row groups at the specified position, to display\n     * when the group is expanded. This row will contain the aggregate values for the group. If a callback function is\n     * provided, it can be used to selectively determine which groups will have a total row added.\n     * @agModule `RowGroupingModule` / `ServerSideRowModelModule`\n     */\n    this.groupTotalRow = undefined;\n    /** When provided, an extra grand total row will be inserted into the grid at the specified position.\n     * This row displays the aggregate totals of all rows in the grid.\n     * @agModule `RowGroupingModule` / `ServerSideRowModelModule`\n     */\n    this.grandTotalRow = undefined;\n    /** Suppress the sticky behaviour of the total rows, can be suppressed individually by passing `'grand'` or `'group'`.\n     * @agModule `RowGroupingModule` / `ServerSideRowModelModule`\n     */\n    this.suppressStickyTotalRow = undefined;\n    /** If `true`, and showing footer, aggregate data will always be displayed at both the header and footer levels. This stops the possibly undesirable behaviour of the header details 'jumping' to the footer on expand.\n     * @default false\n     * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n     */\n    this.groupSuppressBlankHeader = undefined;\n    /** If using `groupSelectsChildren`, then only the children that pass the current filter will get selected.\n     * @default false\n     * @deprecated v32.2 Use `rowSelection.groupSelects` instead\n     */\n    this.groupSelectsFiltered = undefined;\n    /** Shows the open group in the group column for non-group rows.\n     * @default false\n     * @agModule `RowGroupingModule`\n     */\n    this.showOpenedGroup = undefined;\n    /** Enable to display the child row in place of the group row when the group only has a single child.\n     * @default false\n     * @agModule `RowGroupingModule`\n     */\n    this.groupHideParentOfSingleChild = undefined;\n    /** Set to `true` to collapse groups that only have one child.\n     * @default false\n     * @deprecated v33.0.0 - use `groupHideParentOfSingleChild` instead.\n     */\n    this.groupRemoveSingleChildren = undefined;\n    /** Set to `true` to collapse lowest level groups that only have one child.\n     * @default false\n     * @deprecated v33.0.0 - use `groupHideParentOfSingleChild: 'leafGroupsOnly'` instead.\n     */\n    this.groupRemoveLowestSingleChildren = undefined;\n    /** Set to `true` to hide parents that are open. When used with multiple columns for showing groups, it can give a more pleasing user experience.\n     * @default false\n     * @agModule `RowGroupingModule`\n     */\n    this.groupHideOpenParents = undefined;\n    /** Set to `true` to prevent the grid from creating a '(Blanks)' group for nodes which do not belong to a group, and display the unbalanced nodes alongside group nodes.\n     * @default false\n     * @agModule `RowGroupingModule`\n     */\n    this.groupAllowUnbalanced = undefined;\n    /** When to show the 'row group panel' (where you drag rows to group) at the top.\n     * @default 'never'\n     * @agModule `RowGroupingPanelModule`\n     */\n    this.rowGroupPanelShow = undefined;\n    /** Provide the Cell Renderer to use when `groupDisplayType = 'groupRows'`.\n     * See [Group Row Cell Renderer](https://www.ag-grid.com/javascript-data-grid/grouping-group-rows/#providing-cell-renderer) for framework specific implementation details.\n     * @agModule `RowGroupingModule`\n     */\n    this.groupRowRenderer = undefined;\n    /** Customise the parameters provided to the `groupRowRenderer` component.\n     * @agModule `RowGroupingModule`\n     */\n    this.groupRowRendererParams = undefined;\n    /** Set to `true` to enable the Grid to work with Tree Data.\n     * You must also implement the `getDataPath(data)` callback.\n     * @default false\n     * @agModule `TreeDataModule`\n     */\n    this.treeData = undefined;\n    /** The name of the field to use in a data item to retrieve the array of children nodes of a node when while using treeData=true.\n     * It supports accessing nested fields using the dot notation.\n     * @agModule `TreeDataModule`\n     */\n    this.treeDataChildrenField = undefined;\n    /** The name of the field to use in a data item to find the parent node of a node when using treeData=true.\n     * The tree will be constructed via relationships between nodes using this field.\n     * getRowId callback need to be provided as well for this to work.\n     * It supports accessing nested fields using the dot notation.\n     * @agModule `TreeDataModule`\n     */\n    this.treeDataParentIdField = undefined;\n    /** Set to `true` to suppress sort indicators and actions from the row group panel.\n     * @default false\n     * @agModule `RowGroupingPanelModule`\n     */\n    this.rowGroupPanelSuppressSort = undefined;\n    /** Set to `true` prevent Group Rows from sticking to the top of the grid.\n     * @default false\n     * @initial\n     * @agModule `RowGroupingModule` / `TreeDataModule`\n     */\n    this.suppressGroupRowsSticky = undefined;\n    /** Data to be displayed as pinned top rows in the grid.\n     * @agModule `PinnedRowModule`\n     */\n    this.pinnedTopRowData = undefined;\n    /** Data to be displayed as pinned bottom rows in the grid.\n     * @agModule `PinnedRowModule`\n     */\n    this.pinnedBottomRowData = undefined;\n    /** Determines whether manual row pinning is enabled via the row context menu.\n     *\n     * Set to `true` to allow pinning rows to top or bottom.\n     * Set to `'top'` to allow pinning rows to the top only.\n     * Set to `'bottom'` to allow pinning rows to the bottom only.\n     * @agModule `PinnedRowModule`\n     */\n    this.enableRowPinning = undefined;\n    /** Return `true` if the grid should allow the row to be manually pinned.\n     * Return `false` if the grid should prevent the row from being pinned\n     *\n     * When not defined, all rows default to pinnable.\n     * @agModule `PinnedRowModule`\n     */\n    this.isRowPinnable = undefined;\n    /** Called for every row in the grid.\n     *\n     * Return `true` if the row should be pinned initially. Return `false` otherwise.\n     * User interactions can subsequently still change the pinned state of a row.\n     * @agModule `PinnedRowModule`\n     */\n    this.isRowPinned = undefined;\n    /** Sets the row model type.\n     * @default 'clientSide'\n     * @initial\n     * @agModule `ClientSideRowModelModule` / `InfiniteRowModelModule` / `ServerSideRowModelModule` / `ViewportRowModelModule`\n     */\n    this.rowModelType = undefined;\n    /** Set the data to be displayed as rows in the grid.\n     * @agModule `ClientSideRowModelModule`\n     */\n    this.rowData = undefined;\n    /** How many milliseconds to wait before executing a batch of async transactions.\n     */\n    this.asyncTransactionWaitMillis = undefined;\n    /** Prevents Transactions changing sort, filter, group or pivot state when transaction only contains updates.\n     * @default false\n     */\n    this.suppressModelUpdateAfterUpdateTransaction = undefined;\n    /** Provide the datasource for infinite scrolling.\n     * @agModule `InfiniteRowModelModule`\n     */\n    this.datasource = undefined;\n    /** How many extra blank rows to display to the user at the end of the dataset, which sets the vertical scroll and then allows the grid to request viewing more rows of data.\n     * @default 1\n     * @initial\n     * @agModule `InfiniteRowModelModule`\n     */\n    this.cacheOverflowSize = undefined;\n    /** How many extra blank rows to display to the user at the end of the dataset, which sets the vertical scroll and then allows the grid to request viewing more rows of data.\n     * @default 1\n     * @initial\n     * @agModule `InfiniteRowModelModule`\n     */\n    this.infiniteInitialRowCount = undefined;\n    /** Set how many loading rows to display to the user for the root level group.\n     * @default 1\n     * @initial\n     * @agModule `ServerSideRowModelModule`\n     */\n    this.serverSideInitialRowCount = undefined;\n    /** When `true`, the Server-side Row Model will not use a full width loading renderer, instead using the colDef `loadingCellRenderer` if present.\n     * @agModule `ServerSideRowModelModule`\n     */\n    this.suppressServerSideFullWidthLoadingRow = undefined;\n    /** How many rows for each block in the store, i.e. how many rows returned from the server at a time.\n     * @default 100\n     * @agModule `ServerSideRowModelModule` / `InfiniteRowModelModule`\n     */\n    this.cacheBlockSize = undefined;\n    /** How many blocks to keep in the store. Default is no limit, so every requested block is kept. Use this if you have memory concerns, and blocks that were least recently viewed will be purged when the limit is hit. The grid will additionally make sure it has all the blocks needed to display what is currently visible, in case this property is set to a low value.\n     * @initial\n     * @agModule `ServerSideRowModelModule` / `InfiniteRowModelModule`\n     */\n    this.maxBlocksInCache = undefined;\n    /** How many requests to hit the server with concurrently. If the max is reached, requests are queued.\n     * Set to `-1` for no maximum restriction on requests.\n     * @default 2\n     * @initial\n     * @agModule `ServerSideRowModelModule` / `InfiniteRowModelModule`\n     */\n    this.maxConcurrentDatasourceRequests = undefined;\n    /** How many milliseconds to wait before loading a block. Useful when scrolling over many blocks, as it prevents blocks loading until scrolling has settled.\n     * @initial\n     * @agModule `ServerSideRowModelModule` / `InfiniteRowModelModule`\n     */\n    this.blockLoadDebounceMillis = undefined;\n    /** When enabled, closing group rows will remove children of that row. Next time the row is opened, child rows will be read from the datasource again. This property only applies when there is Row Grouping or Tree Data.\n     * @default false\n     * @agModule `ServerSideRowModelModule`\n     */\n    this.purgeClosedRowNodes = undefined;\n    /** Provide the `serverSideDatasource` for server side row model.\n     * @agModule `ServerSideRowModelModule`\n     */\n    this.serverSideDatasource = undefined;\n    /** When enabled, always refreshes top level groups regardless of which column was sorted. This property only applies when there is Row Grouping & sorting is handled on the server.\n     * @default false\n     * @agModule `ServerSideRowModelModule`\n     */\n    this.serverSideSortAllLevels = undefined;\n    /** When enabled, sorts fully loaded groups in the browser instead of requesting from the server.\n     * @default false\n     * @agModule `ServerSideRowModelModule`\n     */\n    this.serverSideEnableClientSideSort = undefined;\n    /** When enabled, only refresh groups directly impacted by a filter. This property only applies when there is Row Grouping & filtering is handled on the server.\n     * @default false\n     * @initial\n     * @agModule `ServerSideRowModelModule`\n     */\n    this.serverSideOnlyRefreshFilteredGroups = undefined;\n    /** Used to split pivot field strings for generating pivot result columns when `pivotResultFields` is provided as part of a `getRows` success.\n     * @default '_'\n     * @initial\n     * @agModule `ServerSideRowModelModule`\n     */\n    this.serverSidePivotResultFieldSeparator = undefined;\n    /** To use the viewport row model you need to provide the grid with a `viewportDatasource`.\n     * @agModule `ViewportRowModelModule`\n     */\n    this.viewportDatasource = undefined;\n    /** When using viewport row model, sets the page size for the viewport.\n     * @initial\n     * @agModule `ViewportRowModelModule`\n     */\n    this.viewportRowModelPageSize = undefined;\n    /** When using viewport row model, sets the buffer size for the viewport.\n     * @initial\n     * @agModule `ViewportRowModelModule`\n     */\n    this.viewportRowModelBufferSize = undefined;\n    /** Set to `true` to always show the horizontal scrollbar.\n     * @default false\n     */\n    this.alwaysShowHorizontalScroll = undefined;\n    /** Set to `true` to always show the vertical scrollbar.\n     * @default false\n     */\n    this.alwaysShowVerticalScroll = undefined;\n    /** Set to `true` to debounce the vertical scrollbar. Can provide smoother scrolling on slow machines.\n     * @default false\n     * @initial\n     */\n    this.debounceVerticalScrollbar = undefined;\n    /** Set to `true` to never show the horizontal scroll. This is useful if the grid is aligned with another grid and will scroll when the other grid scrolls. (Should not be used in combination with `alwaysShowHorizontalScroll`.)\n     * @default false\n     */\n    this.suppressHorizontalScroll = undefined;\n    /** When `true`, the grid will not scroll to the top when new row data is provided. Use this if you don't want the default behaviour of scrolling to the top every time you load new data.\n     * @default false\n     */\n    this.suppressScrollOnNewData = undefined;\n    /** When `true`, the grid will not allow mousewheel / touchpad scroll when popup elements are present.\n     * @default false\n     */\n    this.suppressScrollWhenPopupsAreOpen = undefined;\n    /** When `true`, the grid will not use animation frames when drawing rows while scrolling. Use this if and only if the grid is working fast enough on all users machines and you want to avoid the temporarily empty rows.\n     *     **Note:** It is not recommended to set suppressAnimationFrame to `true` in most use cases as this can seriously degrade the user experience as all cells are rendered synchronously blocking the UI thread from scrolling.\n     * @default false\n     * @initial\n     */\n    this.suppressAnimationFrame = undefined;\n    /** If `true`, middle clicks will result in `click` events for cells and rows. Otherwise the browser will use middle click to scroll the grid.<br />**Note:** Not all browsers fire `click` events with the middle button. Most will fire only `mousedown` and `mouseup` events, which can be used to focus a cell, but will not work to call the `onCellClicked` function.\n     * @default false\n     */\n    this.suppressMiddleClickScrolls = undefined;\n    /** If `true`, mouse wheel events will be passed to the browser. Useful if your grid has no vertical scrolls and you want the mouse to scroll the browser page.\n     * @default false\n     * @initial\n     */\n    this.suppressPreventDefaultOnMouseWheel = undefined;\n    /** Tell the grid how wide in pixels the scrollbar is, which is used in grid width calculations. Set only if using non-standard browser-provided scrollbars, so the grid can use the non-standard size in its calculations.\n     * @initial\n     */\n    this.scrollbarWidth = undefined;\n    /** Use the `RowSelectionOptions` object to configure row selection. The string values `'single'` and `'multiple'` are deprecated.\n     * @agModule `RowSelectionModule`\n     */\n    this.rowSelection = undefined;\n    /** Configure cell selection.\n     * @agModule `CellSelectionModule`\n     */\n    this.cellSelection = undefined;\n    /** Set to `true` to allow multiple rows to be selected using single click.\n     * @default false\n     * @deprecated v32.2 Use `rowSelection.enableSelectionWithoutKeys` instead\n     */\n    this.rowMultiSelectWithClick = undefined;\n    /** If `true`, rows will not be deselected if you hold down `Ctrl` and click the row or press `Space`.\n     * @default false\n     * @deprecated v32.2 Use `rowSelection.enableClickSelection` instead\n     */\n    this.suppressRowDeselection = undefined;\n    /** If `true`, row selection won't happen when rows are clicked. Use when you only want checkbox selection.\n     * @default false\n     * @deprecated v32.2 Use `rowSelection.enableClickSelection` instead\n     */\n    this.suppressRowClickSelection = undefined;\n    /** If `true`, cells won't be focusable. This means keyboard navigation will be disabled for grid cells, but remain enabled in other elements of the grid such as column headers, floating filters, tool panels.\n     * @default false\n     */\n    this.suppressCellFocus = undefined;\n    /** If `true`, header cells won't be focusable. This means keyboard navigation will be disabled for grid header cells, but remain enabled in other elements of the grid such as grid cells and tool panels.\n     * @default false\n     */\n    this.suppressHeaderFocus = undefined;\n    /** Configure the selection column, used for displaying checkboxes.\n     *\n     * Note that due to the nature of this column, this type is a subset of `ColDef`, which does not support several normal column features such as editing, pivoting and grouping.\n     */\n    this.selectionColumnDef = undefined;\n    /** Configure the Row Numbers Feature.\n     * @default false\n     * @agModule `RowNumbersModule`\n     */\n    this.rowNumbers = undefined;\n    /** If `true`, only a single range can be selected.\n     * @default false\n     * @deprecated v32.2 Use `cellSelection.suppressMultiRanges` instead\n     */\n    this.suppressMultiRangeSelection = undefined;\n    /** Set to `true` to be able to select the text within cells.\n     *\n     *     **Note:** When this is set to `true`, the clipboard service is disabled and only selected text is copied.\n     * @default false\n     */\n    this.enableCellTextSelection = undefined;\n    /** Set to `true` to enable Range Selection.\n     * @default false\n     * @deprecated v32.2 Use `cellSelection = true` instead\n     * @agModule `CellSelectionModule`\n     */\n    this.enableRangeSelection = undefined;\n    /** Set to `true` to enable the Range Handle.\n     * @default false\n     * @deprecated v32.2 Use `cellSelection.handle` instead\n     */\n    this.enableRangeHandle = undefined;\n    /** Set to `true` to enable the Fill Handle.\n     * @default false\n     * @deprecated v32.2 Use `cellSelection.handle` instead\n     */\n    this.enableFillHandle = undefined;\n    /** Set to `'x'` to force the fill handle direction to horizontal, or set to `'y'` to force the fill handle direction to vertical.\n     * @default 'xy'\n     * @deprecated v32.2 Use `cellSelection.handle.direction` instead\n     */\n    this.fillHandleDirection = undefined;\n    /** Set this to `true` to prevent cell values from being cleared when the Range Selection is reduced by the Fill Handle.\n     * @default false\n     * @deprecated v32.2 Use `cellSelection.suppressClearOnFillReduction` instead\n     */\n    this.suppressClearOnFillReduction = undefined;\n    /** Array defining the order in which sorting occurs (if sorting is enabled). Values can be `'asc'`, `'desc'` or `null`. For example: `sortingOrder: ['asc', 'desc']`.\n     * @default [null, 'asc', 'desc']\n     * @deprecated v33 Use `defaultColDef.sortingOrder` instead\n     */\n    this.sortingOrder = undefined;\n    /** Set to `true` to specify that the sort should take accented characters into account. If this feature is turned on the sort will be slower.\n     * @default false\n     */\n    this.accentedSort = undefined;\n    /** Set to `true` to show the 'no sort' icon.\n     * @default false\n     * @deprecated v33 Use `defaultColDef.unSortIcon` instead\n     */\n    this.unSortIcon = undefined;\n    /** Set to `true` to suppress multi-sort when the user shift-clicks a column header.\n     * @default false\n     */\n    this.suppressMultiSort = undefined;\n    /** Set to `true` to always multi-sort when the user clicks a column header, regardless of key presses.\n     * @default false\n     */\n    this.alwaysMultiSort = undefined;\n    /** Set to `'ctrl'` to have multi sorting by clicking work using the `Ctrl` (or `Command ⌘` for Mac) key.\n     */\n    this.multiSortKey = undefined;\n    /** Set to `true` to suppress sorting of un-sorted data to match original row data.\n     * @default false\n     */\n    this.suppressMaintainUnsortedOrder = undefined;\n    /** Icons to use inside the grid instead of the grid's default icons.\n     * @initial\n     */\n    this.icons = undefined;\n    /** Default row height in pixels.\n     * @default 25\n     */\n    this.rowHeight = undefined;\n    /** The style properties to apply to all rows. Set to an object of key (style names) and values (style values).\n     * @agModule `RowStyleModule`\n     */\n    this.rowStyle = undefined;\n    /** CSS class(es) for all rows. Provide either a string (class name) or array of strings (array of class names).\n     * @agModule `RowStyleModule`\n     */\n    this.rowClass = undefined;\n    /** Rules which can be applied to include certain CSS classes.\n     * @agModule `RowStyleModule`\n     */\n    this.rowClassRules = undefined;\n    /** Set to `true` to not highlight rows by adding the `ag-row-hover` CSS class.\n     * @default false\n     */\n    this.suppressRowHoverHighlight = undefined;\n    /** Uses CSS `top` instead of CSS `transform` for positioning rows. Useful if the transform function is causing issues such as used in row spanning.\n     * @default false\n     * @initial\n     */\n    this.suppressRowTransform = undefined;\n    /** Set to `true` to highlight columns by adding the `ag-column-hover` CSS class.\n     * @default false\n     * @agModule `ColumnHoverModule`\n     */\n    this.columnHoverHighlight = undefined;\n    /** Provide a custom `gridId` for this instance of the grid. Value will be set on the root DOM node using the attribute `grid-id` as well as being accessible via the `gridApi.getGridId()` method.\n     * @initial\n     */\n    this.gridId = undefined;\n    /** When enabled, sorts only the rows added/updated by a transaction.\n     * @default false\n     */\n    this.deltaSort = undefined;\n    /**/\n    this.treeDataDisplayType = undefined;\n    /** @initial\n     */\n    this.enableGroupEdit = undefined;\n    /** Initial state for the grid. Only read once on initialization. Can be used in conjunction with `api.getState()` to save and restore grid state.\n     * @initial\n     * @agModule `GridStateModule`\n     */\n    this.initialState = undefined;\n    /** Theme to apply to the grid, or the string \"legacy\" to opt back into the\n     * v32 style of theming where themes were imported as CSS files and applied\n     * by setting a class name on the parent element.\n     *\n     * @default themeQuartz\n     */\n    this.theme = undefined;\n    /** If your theme uses a font that is available on Google Fonts, pass true to load it from Google's CDN.\n     */\n    this.loadThemeGoogleFonts = undefined;\n    /** The CSS layer that this theme should be rendered onto. If your\n     * application loads its styles into a CSS layer, use this to load the grid\n     * styles into a previous layer so that application styles can override grid\n     * styles.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@layer\n     */\n    this.themeCssLayer = undefined;\n    /** The nonce attribute to set on style elements added to the document by\n     * themes. If \"foo\" is passed to this property, the grid can use the Content\n     * Security Policy `style-src 'nonce-foo'`, instead of the less secure\n     * `style-src 'unsafe-inline'`.\n     *\n     * Note: CSP nonces are global to a page, where a page has multiple grids,\n     * every one must have the same styleNonce set.\n     */\n    this.styleNonce = undefined;\n    /** An element to insert style elements into when injecting styles into the\n     * grid. If undefined, styles will be added to the document head for grids\n     * rendered in the main document fragment, or to the grid wrapper element\n     * for other grids (e.g. those rendered in a shadow DOM or detached from the\n     * document).\n     *\n     * @initial\n     */\n    this.themeStyleContainer = undefined;\n    /** For customising the context menu.\n     * @agModule `ContextMenuModule`\n     */\n    this.getContextMenuItems = undefined;\n    /** For customising the main 'column header' menu.\n     * @initial\n     * @agModule `ColumnMenuModule`\n     */\n    this.getMainMenuItems = undefined;\n    /** Allows user to process popups after they are created. Applications can use this if they want to, for example, reposition the popup.\n     */\n    this.postProcessPopup = undefined;\n    /** Allows the user to process the columns being removed from the pinned section because the viewport is too small to accommodate them.\n     * Returns an array of columns to be removed from the pinned areas.\n     * @initial\n     */\n    this.processUnpinnedColumns = undefined;\n    /** Allows you to process cells for the clipboard. Handy if for example you have `Date` objects that need to have a particular format if importing into Excel.\n     * @agModule `ClipboardModule`\n     */\n    this.processCellForClipboard = undefined;\n    /** Allows you to process header values for the clipboard.\n     * @agModule `ClipboardModule`\n     */\n    this.processHeaderForClipboard = undefined;\n    /** Allows you to process group header values for the clipboard.\n     * @agModule `ClipboardModule`\n     */\n    this.processGroupHeaderForClipboard = undefined;\n    /** Allows you to process cells from the clipboard. Handy if for example you have number fields and want to block non-numbers from getting into the grid.\n     * @agModule `ClipboardModule`\n     */\n    this.processCellFromClipboard = undefined;\n    /** Allows you to get the data that would otherwise go to the clipboard. To be used when you want to control the 'copy to clipboard' operation yourself.\n     * @agModule `ClipboardModule`\n     */\n    this.sendToClipboard = undefined;\n    /** Allows complete control of the paste operation, including cancelling the operation (so nothing happens) or replacing the data with other data.\n     * @agModule `ClipboardModule`\n     */\n    this.processDataFromClipboard = undefined;\n    /** Grid calls this method to know if an external filter is present.\n     * @agModule `ExternalFilterModule`\n     */\n    this.isExternalFilterPresent = undefined;\n    /** Should return `true` if external filter passes, otherwise `false`.\n     * @agModule `ExternalFilterModule`\n     */\n    this.doesExternalFilterPass = undefined;\n    /** Callback to be used to customise the chart toolbar items.\n     * @initial\n     * @agModule `IntegratedChartsModule`\n     */\n    this.getChartToolbarItems = undefined;\n    /** Callback to enable displaying the chart in an alternative chart container.\n     * @initial\n     * @agModule `IntegratedChartsModule`\n     */\n    this.createChartContainer = undefined;\n    /** Allows overriding the element that will be focused when the grid receives focus from outside elements (tabbing into the grid).\n     * @returns `True` if this function should override the grid's default behavior, `False` to allow the grid's default behavior.\n     */\n    this.focusGridInnerElement = undefined;\n    /** Allows overriding the default behaviour for when user hits navigation (arrow) key when a header is focused. Return the next Header position to navigate to or `null` to stay on current header.\n     */\n    this.navigateToNextHeader = undefined;\n    /** Allows overriding the default behaviour for when user hits `Tab` key when a header is focused.\n     * Return the next header position to navigate to, `true` to stay on the current header,\n     * or `false` to let the browser handle the tab behaviour.\n     */\n    this.tabToNextHeader = undefined;\n    /** Allows overriding the default behaviour for when user hits navigation (arrow) key when a cell is focused. Return the next Cell position to navigate to or `null` to stay on current cell.\n     */\n    this.navigateToNextCell = undefined;\n    /** Allows overriding the default behaviour for when user hits `Tab` key when a cell is focused.\n     * Return the next cell position to navigate to, `true` to stay on the current cell,\n     * or `false` to let the browser handle the tab behaviour.\n     */\n    this.tabToNextCell = undefined;\n    /** A callback for localising text within the grid.\n     * @initial\n     * @agModule `LocaleModule`\n     */\n    this.getLocaleText = undefined;\n    /** Allows overriding what `document` is used. Currently used by Drag and Drop (may extend to other places in the future). Use this when you want the grid to use a different `document` than the one available on the global scope. This can happen if docking out components (something which Electron supports)\n     */\n    this.getDocument = undefined;\n    /** Allows user to format the numbers in the pagination panel, i.e. 'row count' and 'page number' labels. This is for pagination panel only, to format numbers inside the grid's cells (i.e. your data), then use `valueFormatter` in the column definitions.\n     * @initial\n     * @agModule `PaginationModule`\n     */\n    this.paginationNumberFormatter = undefined;\n    /** Callback to use when you need access to more then the current column for aggregation.\n     * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n     */\n    this.getGroupRowAgg = undefined;\n    /** (Client-side Row Model only) Allows groups to be open by default.\n     * @agModule `RowGroupingModule` / `TreeDataModule`\n     */\n    this.isGroupOpenByDefault = undefined;\n    /** Allows default sorting of groups.\n     * @agModule `RowGroupingModule`\n     */\n    this.initialGroupOrderComparator = undefined;\n    /** Callback for the mutation of the generated pivot result column definitions\n     * @agModule `PivotModule`\n     */\n    this.processPivotResultColDef = undefined;\n    /** Callback for the mutation of the generated pivot result column group definitions\n     * @agModule `PivotModule`\n     */\n    this.processPivotResultColGroupDef = undefined;\n    /** Callback to be used when working with Tree Data when `treeData = true`.\n     * @initial\n     * @agModule `TreeDataModule`\n     */\n    this.getDataPath = undefined;\n    /** Allows setting the child count for a group row.\n     * @initial\n     * @agModule `ServerSideRowModelModule`\n     */\n    this.getChildCount = undefined;\n    /** Allows providing different params for different levels of grouping.\n     * @initial\n     * @agModule `ServerSideRowModelModule`\n     */\n    this.getServerSideGroupLevelParams = undefined;\n    /** Allows groups to be open by default.\n     * @agModule `ServerSideRowModelModule`\n     */\n    this.isServerSideGroupOpenByDefault = undefined;\n    /** Allows cancelling transactions.\n     * @agModule `ServerSideRowModelModule`\n     */\n    this.isApplyServerSideTransaction = undefined;\n    /** SSRM Tree Data: Allows specifying which rows are expandable.\n     * @agModule `ServerSideRowModelModule`\n     */\n    this.isServerSideGroup = undefined;\n    /** SSRM Tree Data: Allows specifying group keys.\n     * @agModule `ServerSideRowModelModule`\n     */\n    this.getServerSideGroupKey = undefined;\n    /** Return a business key for the node. If implemented, each row in the DOM will have an attribute `row-business-key='abc'` where `abc` is what you return as the business key.\n     * This is useful for automated testing, as it provides a way for your tool to identify rows based on unique business keys.\n     */\n    this.getBusinessKeyForNode = undefined;\n    /** Provide a pure function that returns a string ID to uniquely identify a given row. This enables the grid to work optimally with data changes and updates.\n     * @initial\n     */\n    this.getRowId = undefined;\n    /** When enabled, getRowId() callback is implemented and new Row Data is set, the grid will disregard all previous rows and treat the new Row Data as new data. As a consequence, all Row State (eg selection, rendered rows) will be reset.\n     * @default false\n     * @agModule `ClientSideRowModelModule`\n     */\n    this.resetRowDataOnUpdate = undefined;\n    /** Callback fired after the row is rendered into the DOM. Should not be used to initiate side effects.\n     */\n    this.processRowPostCreate = undefined;\n    /** Callback to be used to determine which rows are selectable. By default rows are selectable, so return `false` to make a row un-selectable.\n     * @deprecated v32.2 Use `rowSelection.isRowSelectable` instead\n     */\n    this.isRowSelectable = undefined;\n    /** Callback to be used with Master Detail to determine if a row should be a master row. If `false` is returned no detail row will exist for this row.\n     * @agModule `MasterDetailModule`\n     */\n    this.isRowMaster = undefined;\n    /** Callback to fill values instead of simply copying values or increasing number values using linear progression.\n     * @deprecated v32.2 Use `cellSelection.handle.setFillValue` instead\n     */\n    this.fillOperation = undefined;\n    /** Callback to perform additional sorting after the grid has sorted the rows.\n     */\n    this.postSortRows = undefined;\n    /** Callback version of property `rowStyle` to set style for each row individually. Function should return an object of CSS values or undefined for no styles.\n     * @agModule `RowStyleModule`\n     */\n    this.getRowStyle = undefined;\n    /** Callback version of property `rowClass` to set class(es) for each row individually. Function should return either a string (class name), array of strings (array of class names) or undefined for no class.\n     * @agModule `RowStyleModule`\n     */\n    this.getRowClass = undefined;\n    /** Callback version of property `rowHeight` to set height for each row individually. Function should return a positive number of pixels, or return `null`/`undefined` to use the default row height.\n     */\n    this.getRowHeight = undefined;\n    /** Tells the grid if this row should be rendered as full width.\n     */\n    this.isFullWidthRow = undefined;\n    /** The tool panel visibility has changed. Fires twice if switching between panels - once with the old panel and once with the new panel.\n     */\n    this.toolPanelVisibleChanged = new EventEmitter();\n    /** The tool panel size has been changed.\n     */\n    this.toolPanelSizeChanged = new EventEmitter();\n    /** The column menu visibility has changed. Fires twice if switching between tabs - once with the old tab and once with the new tab.\n     */\n    this.columnMenuVisibleChanged = new EventEmitter();\n    /** The context menu visibility has changed (opened or closed).\n     */\n    this.contextMenuVisibleChanged = new EventEmitter();\n    /** Cut operation has started.\n     */\n    this.cutStart = new EventEmitter();\n    /** Cut operation has ended.\n     */\n    this.cutEnd = new EventEmitter();\n    /** Paste operation has started.\n     */\n    this.pasteStart = new EventEmitter();\n    /** Paste operation has ended.\n     */\n    this.pasteEnd = new EventEmitter();\n    /** A column, or group of columns, was hidden / shown.\n     */\n    this.columnVisible = new EventEmitter();\n    /** A column, or group of columns, was pinned / unpinned.\n     */\n    this.columnPinned = new EventEmitter();\n    /** A column was resized.\n     */\n    this.columnResized = new EventEmitter();\n    /** A column was moved.\n     */\n    this.columnMoved = new EventEmitter();\n    /** A value column was added or removed.\n     */\n    this.columnValueChanged = new EventEmitter();\n    /** The pivot mode flag was changed.\n     */\n    this.columnPivotModeChanged = new EventEmitter();\n    /** A pivot column was added, removed or order changed.\n     */\n    this.columnPivotChanged = new EventEmitter();\n    /** A column group was opened / closed.\n     */\n    this.columnGroupOpened = new EventEmitter();\n    /** User set new columns.\n     */\n    this.newColumnsLoaded = new EventEmitter();\n    /** The list of grid columns changed.\n     */\n    this.gridColumnsChanged = new EventEmitter();\n    /** The list of displayed columns changed. This can result from columns open / close, column move, pivot, group, etc.\n     */\n    this.displayedColumnsChanged = new EventEmitter();\n    /** The list of rendered columns changed (only columns in the visible scrolled viewport are rendered by default).\n     */\n    this.virtualColumnsChanged = new EventEmitter();\n    /** @deprecated v32.2 Either use `onDisplayedColumnsChanged` which is fired at the same time,\n     * or use one of the more specific column events.\n     */\n    this.columnEverythingChanged = new EventEmitter();\n    /** A mouse cursor is initially moved over a column header.\n     */\n    this.columnHeaderMouseOver = new EventEmitter();\n    /** A mouse cursor is moved out of a column header.\n     */\n    this.columnHeaderMouseLeave = new EventEmitter();\n    /** A click is performed on a column header.\n     */\n    this.columnHeaderClicked = new EventEmitter();\n    /** A context menu action, such as right-click or context menu key press, is performed on a column header.\n     */\n    this.columnHeaderContextMenu = new EventEmitter();\n    /** Only used by Angular, React and VueJS AG Grid components (not used if doing plain JavaScript).\n     * If the grid receives changes due to bound properties, this event fires after the grid has finished processing the change.\n     */\n    this.componentStateChanged = new EventEmitter();\n    /** Cell value has changed. This occurs after the following scenarios:\n     * - Editing. Will not fire if any of the following are true:\n     *     new value is the same as old value;\n     *     `readOnlyEdit = true`;\n     *     editing was cancelled (e.g. Escape key was pressed);\n     *     or new value is of the wrong cell data type for the column.\n     *  - Cut.\n     *  - Paste.\n     *  - Cell clear (pressing Delete key).\n     *  - Fill handle.\n     *  - Copy range down.\n     *  - Undo and redo.\n     */\n    this.cellValueChanged = new EventEmitter();\n    /** Value has changed after editing. Only fires when `readOnlyEdit=true`.\n     */\n    this.cellEditRequest = new EventEmitter();\n    /** A cell's value within a row has changed. This event corresponds to Full Row Editing only.\n     */\n    this.rowValueChanged = new EventEmitter();\n    /** Editing a cell has started.\n     */\n    this.cellEditingStarted = new EventEmitter();\n    /** Editing a cell has stopped.\n     */\n    this.cellEditingStopped = new EventEmitter();\n    /** Editing a row has started (when row editing is enabled). When row editing, this event will be fired once and `cellEditingStarted` will be fired for each individual cell. Only fires when doing Full Row Editing.\n     */\n    this.rowEditingStarted = new EventEmitter();\n    /** Editing a row has stopped (when row editing is enabled). When row editing, this event will be fired once and `cellEditingStopped` will be fired for each individual cell. Only fires when doing Full Row Editing.\n     */\n    this.rowEditingStopped = new EventEmitter();\n    /** Undo operation has started.\n     */\n    this.undoStarted = new EventEmitter();\n    /** Undo operation has ended.\n     */\n    this.undoEnded = new EventEmitter();\n    /** Redo operation has started.\n     */\n    this.redoStarted = new EventEmitter();\n    /** Redo operation has ended.\n     */\n    this.redoEnded = new EventEmitter();\n    /** Cell selection delete operation (cell clear) has started.\n     */\n    this.cellSelectionDeleteStart = new EventEmitter();\n    /** Cell selection delete operation (cell clear) has ended.\n     */\n    this.cellSelectionDeleteEnd = new EventEmitter();\n    /** Range delete operation (cell clear) has started.\n     *\n     * @deprecated v32.2 Use `onCellSelectionDeleteStart` instead\n     */\n    this.rangeDeleteStart = new EventEmitter();\n    /** Range delete operation (cell clear) has ended.\n     *\n     * @deprecated v32.2 Use `onCellSelectionDeleteEnd` instead\n     */\n    this.rangeDeleteEnd = new EventEmitter();\n    /** Fill operation has started.\n     */\n    this.fillStart = new EventEmitter();\n    /** Fill operation has ended.\n     */\n    this.fillEnd = new EventEmitter();\n    /** Filter has been opened.\n     */\n    this.filterOpened = new EventEmitter();\n    /** Filter has been modified and applied.\n     */\n    this.filterChanged = new EventEmitter();\n    /** Filter was modified but not applied. Used when filters have 'Apply' buttons.\n     */\n    this.filterModified = new EventEmitter();\n    /** Advanced Filter Builder visibility has changed (opened or closed).\n     */\n    this.advancedFilterBuilderVisibleChanged = new EventEmitter();\n    /** Find details have changed (e.g. Find search value, active match, or updates to grid cells).\n     */\n    this.findChanged = new EventEmitter();\n    /** A chart has been created.\n     */\n    this.chartCreated = new EventEmitter();\n    /** The data range for the chart has been changed.\n     */\n    this.chartRangeSelectionChanged = new EventEmitter();\n    /** Formatting changes have been made by users through the Customize Panel.\n     */\n    this.chartOptionsChanged = new EventEmitter();\n    /** A chart has been destroyed.\n     */\n    this.chartDestroyed = new EventEmitter();\n    /** DOM event `keyDown` happened on a cell.\n     */\n    this.cellKeyDown = new EventEmitter();\n    /** The grid has initialised and is ready for most api calls, but may not be fully rendered yet      */\n    this.gridReady = new EventEmitter();\n    /** Fired the first time data is rendered into the grid. Use this event if you want to auto resize columns based on their contents     */\n    this.firstDataRendered = new EventEmitter();\n    /** The size of the grid `div` has changed. In other words, the grid was resized.\n     */\n    this.gridSizeChanged = new EventEmitter();\n    /** Displayed rows have changed. Triggered after sort, filter or tree expand / collapse events.\n     */\n    this.modelUpdated = new EventEmitter();\n    /** A row was removed from the DOM, for any reason. Use to clean up resources (if any) used by the row.\n     */\n    this.virtualRowRemoved = new EventEmitter();\n    /** Which rows are rendered in the DOM has changed.\n     */\n    this.viewportChanged = new EventEmitter();\n    /** The body was scrolled horizontally or vertically.\n     */\n    this.bodyScroll = new EventEmitter();\n    /** Main body of the grid has stopped scrolling, either horizontally or vertically.\n     */\n    this.bodyScrollEnd = new EventEmitter();\n    /** When dragging starts. This could be any action that uses the grid's Drag and Drop service, e.g. Column Moving, Column Resizing, Range Selection, Fill Handle, etc.\n     */\n    this.dragStarted = new EventEmitter();\n    /** When dragging stops. This could be any action that uses the grid's Drag and Drop service, e.g. Column Moving, Column Resizing, Range Selection, Fill Handle, etc.\n     */\n    this.dragStopped = new EventEmitter();\n    /** When dragging is cancelled stops. This is caused by pressing `Escape` while dragging elements within the grid that uses the grid's Drag and Drop service, e.g. Column Moving, Column Resizing, Range Selection, Fill Handle, etc.\n     */\n    this.dragCancelled = new EventEmitter();\n    /** Grid state has been updated.\n     */\n    this.stateUpdated = new EventEmitter();\n    /** Triggered every time the paging state changes. Some of the most common scenarios for this event to be triggered are:\n     *\n     *  - The page size changes.\n     *  - The current shown page is changed.\n     *  - New data is loaded onto the grid.\n     */\n    this.paginationChanged = new EventEmitter();\n    /** A drag has started, or dragging was already started and the mouse has re-entered the grid having previously left the grid.\n     */\n    this.rowDragEnter = new EventEmitter();\n    /** The mouse has moved while dragging.\n     */\n    this.rowDragMove = new EventEmitter();\n    /** The mouse has left the grid while dragging.\n     */\n    this.rowDragLeave = new EventEmitter();\n    /** The drag has finished over the grid.\n     */\n    this.rowDragEnd = new EventEmitter();\n    /** The drag has been cancelled over the grid.\n     */\n    this.rowDragCancel = new EventEmitter();\n    /** The row resize has started (Row Numbers Feature)\n     */\n    this.rowResizeStarted = new EventEmitter();\n    /** The row resize has ended (Row Numbers Feature)\n     */\n    this.rowResizeEnded = new EventEmitter();\n    /** A row group column was added, removed or reordered.\n     */\n    this.columnRowGroupChanged = new EventEmitter();\n    /** A row group was opened or closed.\n     */\n    this.rowGroupOpened = new EventEmitter();\n    /** Fired when calling either of the API methods `expandAll()` or `collapseAll()`.\n     */\n    this.expandOrCollapseAll = new EventEmitter();\n    /** Exceeded the `pivotMaxGeneratedColumns` limit when generating columns.\n     */\n    this.pivotMaxColumnsExceeded = new EventEmitter();\n    /** The client has set new pinned row data into the grid.\n     */\n    this.pinnedRowDataChanged = new EventEmitter();\n    /** A row has been pinned to top or bottom, or unpinned.\n     */\n    this.pinnedRowsChanged = new EventEmitter();\n    /** Client-Side Row Model only. The client has updated data for the grid by either a) setting new Row Data or b) Applying a Row Transaction.\n     */\n    this.rowDataUpdated = new EventEmitter();\n    /** Async transactions have been applied. Contains a list of all transaction results.\n     */\n    this.asyncTransactionsFlushed = new EventEmitter();\n    /** A server side store has finished refreshing.\n     */\n    this.storeRefreshed = new EventEmitter();\n    /** Header is focused.\n     */\n    this.headerFocused = new EventEmitter();\n    /** Cell is clicked.\n     */\n    this.cellClicked = new EventEmitter();\n    /** Cell is double clicked.\n     */\n    this.cellDoubleClicked = new EventEmitter();\n    /** Cell is focused.\n     */\n    this.cellFocused = new EventEmitter();\n    /** Mouse entered cell.\n     */\n    this.cellMouseOver = new EventEmitter();\n    /** Mouse left cell.\n     */\n    this.cellMouseOut = new EventEmitter();\n    /** Mouse down on cell.\n     */\n    this.cellMouseDown = new EventEmitter();\n    /** Row is clicked.\n     */\n    this.rowClicked = new EventEmitter();\n    /** Row is double clicked.\n     */\n    this.rowDoubleClicked = new EventEmitter();\n    /** Row is selected or deselected. The event contains the node in question, so call the node's `isSelected()` method to see if it was just selected or deselected.\n     */\n    this.rowSelected = new EventEmitter();\n    /** Row selection is changed. Use the `selectedNodes` field to get the list of selected nodes at the time of the event. When using the SSRM, `selectedNodes` will be `null`\n     * when selecting all nodes. Instead, refer to the `serverSideState` field.\n     */\n    this.selectionChanged = new EventEmitter();\n    /** Cell is right clicked.\n     */\n    this.cellContextMenu = new EventEmitter();\n    /** A change to range selection has occurred.\n     *\n     * @deprecated v32.2 Use `onCellSelectionChanged` instead\n     */\n    this.rangeSelectionChanged = new EventEmitter();\n    /** A change to cell selection has occurred.\n     */\n    this.cellSelectionChanged = new EventEmitter();\n    /** A tooltip has been displayed     */\n    this.tooltipShow = new EventEmitter();\n    /** A tooltip was hidden     */\n    this.tooltipHide = new EventEmitter();\n    /** Sort has changed. The grid also listens for this and updates the model.\n     */\n    this.sortChanged = new EventEmitter();\n    this._nativeElement = elementDef.nativeElement;\n    this._fullyReady.then(() => {\n      // Register the status flag reset before any events are fired\n      // so that we can swap to synchronous event firing as soon as the grid is ready\n      this._holdEvents = false;\n    });\n  }\n  ngAfterViewInit() {\n    // Run the setup outside of angular so all the event handlers that are created do not trigger change detection\n    this._angularFrameworkOverrides.runOutsideAngular(() => {\n      this._frameworkCompWrapper.setViewContainerRef(this._viewContainerRef, this._angularFrameworkOverrides);\n      // Get all the inputs that are valid GridOptions\n      const gridOptionKeys = Object.keys(this).filter(key => !(key.startsWith('_') || key == 'gridOptions' || key == 'modules' || this[key] instanceof EventEmitter));\n      const coercedGridOptions = {};\n      gridOptionKeys.forEach(key => {\n        const valueToUse = getValueOrCoercedValue(key, this[key]);\n        coercedGridOptions[key] = valueToUse;\n      });\n      const mergedGridOps = _combineAttributesAndGridOptions(this.gridOptions, coercedGridOptions, gridOptionKeys);\n      const gridParams = {\n        globalListener: this.globalListener.bind(this),\n        frameworkOverrides: this._angularFrameworkOverrides,\n        providedBeanInstances: {\n          frameworkCompWrapper: this._frameworkCompWrapper\n        },\n        modules: this.modules || [],\n        setThemeOnGridDiv: true\n      };\n      const api = createGrid(this._nativeElement, mergedGridOps, gridParams);\n      if (api) {\n        this.api = api;\n      }\n      this._initialised = true;\n      // sometimes, especially in large client apps gridReady can fire before ngAfterViewInit\n      // this ties these together so that gridReady will always fire after agGridAngular's ngAfterViewInit\n      // the actual containing component's ngAfterViewInit will fire just after agGridAngular's\n      this._resolveFullyReady();\n    });\n  }\n  ngOnChanges(changes) {\n    if (this._initialised) {\n      // Run the changes outside of angular so any event handlers that are created do not trigger change detection\n      this._angularFrameworkOverrides.runOutsideAngular(() => {\n        const gridOptions = {};\n        for (const key of Object.keys(changes)) {\n          const value = changes[key];\n          gridOptions[key] = value.currentValue;\n        }\n        _processOnChange(gridOptions, this.api);\n      });\n    }\n  }\n  ngOnDestroy() {\n    if (this._initialised) {\n      // need to do this before the destroy, so we know not to emit any events\n      // while tearing down the grid.\n      this._destroyed = true;\n      // could be null if grid failed to initialise\n      this.api?.destroy();\n    }\n  }\n  // we'll emit the emit if a user is listening for a given event either on the component via normal angular binding\n  // or via gridOptions\n  isEmitterUsed(eventType) {\n    const emitter = this[eventType];\n    // For RxJs compatibility we need to check for observed v7+ or observers v6\n    const emitterAny = emitter;\n    const hasEmitter = emitterAny?.observed ?? emitterAny?.observers?.length > 0;\n    // gridReady => onGridReady\n    const asEventName = `on${eventType.charAt(0).toUpperCase()}${eventType.substring(1)}`;\n    const hasGridOptionListener = !!this.gridOptions && !!this.gridOptions[asEventName];\n    return hasEmitter || hasGridOptionListener;\n  }\n  globalListener(eventType, event) {\n    // if we are tearing down, don't emit angular events, as this causes\n    // problems with the angular router\n    if (this._destroyed) {\n      return;\n    }\n    // generically look up the eventType\n    const emitter = this[eventType];\n    if (emitter && this.isEmitterUsed(eventType)) {\n      // Make sure we emit within the angular zone, so change detection works properly\n      const fireEmitter = () => this._angularFrameworkOverrides.runInsideAngular(() => emitter.emit(event));\n      if (this._holdEvents) {\n        // if the user is listening to events, wait for ngAfterViewInit to fire first, then emit the grid events\n        this._fullyReady.then(() => fireEmitter());\n      } else {\n        fireEmitter();\n      }\n    }\n  }\n  static {\n    this.ɵfac = function AgGridAngular_Factory(t) {\n      return new (t || AgGridAngular)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(AngularFrameworkOverrides), i0.ɵɵdirectiveInject(AngularFrameworkComponentWrapper));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: AgGridAngular,\n      selectors: [[\"ag-grid-angular\"]],\n      inputs: {\n        gridOptions: \"gridOptions\",\n        modules: \"modules\",\n        statusBar: \"statusBar\",\n        sideBar: \"sideBar\",\n        suppressContextMenu: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressContextMenu\", \"suppressContextMenu\", booleanAttribute],\n        preventDefaultOnContextMenu: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"preventDefaultOnContextMenu\", \"preventDefaultOnContextMenu\", booleanAttribute],\n        allowContextMenuWithControlKey: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"allowContextMenuWithControlKey\", \"allowContextMenuWithControlKey\", booleanAttribute],\n        columnMenu: \"columnMenu\",\n        suppressMenuHide: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressMenuHide\", \"suppressMenuHide\", booleanAttribute],\n        enableBrowserTooltips: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"enableBrowserTooltips\", \"enableBrowserTooltips\", booleanAttribute],\n        tooltipTrigger: \"tooltipTrigger\",\n        tooltipShowDelay: \"tooltipShowDelay\",\n        tooltipHideDelay: \"tooltipHideDelay\",\n        tooltipMouseTrack: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tooltipMouseTrack\", \"tooltipMouseTrack\", booleanAttribute],\n        tooltipShowMode: \"tooltipShowMode\",\n        tooltipInteraction: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tooltipInteraction\", \"tooltipInteraction\", booleanAttribute],\n        popupParent: \"popupParent\",\n        copyHeadersToClipboard: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"copyHeadersToClipboard\", \"copyHeadersToClipboard\", booleanAttribute],\n        copyGroupHeadersToClipboard: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"copyGroupHeadersToClipboard\", \"copyGroupHeadersToClipboard\", booleanAttribute],\n        clipboardDelimiter: \"clipboardDelimiter\",\n        suppressCopyRowsToClipboard: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressCopyRowsToClipboard\", \"suppressCopyRowsToClipboard\", booleanAttribute],\n        suppressCopySingleCellRanges: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressCopySingleCellRanges\", \"suppressCopySingleCellRanges\", booleanAttribute],\n        suppressLastEmptyLineOnPaste: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressLastEmptyLineOnPaste\", \"suppressLastEmptyLineOnPaste\", booleanAttribute],\n        suppressClipboardPaste: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressClipboardPaste\", \"suppressClipboardPaste\", booleanAttribute],\n        suppressClipboardApi: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressClipboardApi\", \"suppressClipboardApi\", booleanAttribute],\n        suppressCutToClipboard: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressCutToClipboard\", \"suppressCutToClipboard\", booleanAttribute],\n        columnDefs: \"columnDefs\",\n        defaultColDef: \"defaultColDef\",\n        defaultColGroupDef: \"defaultColGroupDef\",\n        columnTypes: \"columnTypes\",\n        dataTypeDefinitions: \"dataTypeDefinitions\",\n        maintainColumnOrder: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"maintainColumnOrder\", \"maintainColumnOrder\", booleanAttribute],\n        enableStrictPivotColumnOrder: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"enableStrictPivotColumnOrder\", \"enableStrictPivotColumnOrder\", booleanAttribute],\n        suppressFieldDotNotation: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressFieldDotNotation\", \"suppressFieldDotNotation\", booleanAttribute],\n        headerHeight: \"headerHeight\",\n        groupHeaderHeight: \"groupHeaderHeight\",\n        floatingFiltersHeight: \"floatingFiltersHeight\",\n        pivotHeaderHeight: \"pivotHeaderHeight\",\n        pivotGroupHeaderHeight: \"pivotGroupHeaderHeight\",\n        allowDragFromColumnsToolPanel: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"allowDragFromColumnsToolPanel\", \"allowDragFromColumnsToolPanel\", booleanAttribute],\n        suppressMovableColumns: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressMovableColumns\", \"suppressMovableColumns\", booleanAttribute],\n        suppressColumnMoveAnimation: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressColumnMoveAnimation\", \"suppressColumnMoveAnimation\", booleanAttribute],\n        suppressMoveWhenColumnDragging: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressMoveWhenColumnDragging\", \"suppressMoveWhenColumnDragging\", booleanAttribute],\n        suppressDragLeaveHidesColumns: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressDragLeaveHidesColumns\", \"suppressDragLeaveHidesColumns\", booleanAttribute],\n        suppressGroupChangesColumnVisibility: \"suppressGroupChangesColumnVisibility\",\n        suppressMakeColumnVisibleAfterUnGroup: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressMakeColumnVisibleAfterUnGroup\", \"suppressMakeColumnVisibleAfterUnGroup\", booleanAttribute],\n        suppressRowGroupHidesColumns: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressRowGroupHidesColumns\", \"suppressRowGroupHidesColumns\", booleanAttribute],\n        colResizeDefault: \"colResizeDefault\",\n        suppressAutoSize: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressAutoSize\", \"suppressAutoSize\", booleanAttribute],\n        autoSizePadding: \"autoSizePadding\",\n        skipHeaderOnAutoSize: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"skipHeaderOnAutoSize\", \"skipHeaderOnAutoSize\", booleanAttribute],\n        autoSizeStrategy: \"autoSizeStrategy\",\n        components: \"components\",\n        editType: \"editType\",\n        singleClickEdit: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"singleClickEdit\", \"singleClickEdit\", booleanAttribute],\n        suppressClickEdit: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressClickEdit\", \"suppressClickEdit\", booleanAttribute],\n        readOnlyEdit: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"readOnlyEdit\", \"readOnlyEdit\", booleanAttribute],\n        stopEditingWhenCellsLoseFocus: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"stopEditingWhenCellsLoseFocus\", \"stopEditingWhenCellsLoseFocus\", booleanAttribute],\n        enterNavigatesVertically: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"enterNavigatesVertically\", \"enterNavigatesVertically\", booleanAttribute],\n        enterNavigatesVerticallyAfterEdit: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"enterNavigatesVerticallyAfterEdit\", \"enterNavigatesVerticallyAfterEdit\", booleanAttribute],\n        enableCellEditingOnBackspace: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"enableCellEditingOnBackspace\", \"enableCellEditingOnBackspace\", booleanAttribute],\n        undoRedoCellEditing: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"undoRedoCellEditing\", \"undoRedoCellEditing\", booleanAttribute],\n        undoRedoCellEditingLimit: \"undoRedoCellEditingLimit\",\n        defaultCsvExportParams: \"defaultCsvExportParams\",\n        suppressCsvExport: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressCsvExport\", \"suppressCsvExport\", booleanAttribute],\n        defaultExcelExportParams: \"defaultExcelExportParams\",\n        suppressExcelExport: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressExcelExport\", \"suppressExcelExport\", booleanAttribute],\n        excelStyles: \"excelStyles\",\n        findSearchValue: \"findSearchValue\",\n        findOptions: \"findOptions\",\n        quickFilterText: \"quickFilterText\",\n        cacheQuickFilter: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cacheQuickFilter\", \"cacheQuickFilter\", booleanAttribute],\n        includeHiddenColumnsInQuickFilter: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"includeHiddenColumnsInQuickFilter\", \"includeHiddenColumnsInQuickFilter\", booleanAttribute],\n        quickFilterParser: \"quickFilterParser\",\n        quickFilterMatcher: \"quickFilterMatcher\",\n        applyQuickFilterBeforePivotOrAgg: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"applyQuickFilterBeforePivotOrAgg\", \"applyQuickFilterBeforePivotOrAgg\", booleanAttribute],\n        excludeChildrenWhenTreeDataFiltering: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"excludeChildrenWhenTreeDataFiltering\", \"excludeChildrenWhenTreeDataFiltering\", booleanAttribute],\n        enableAdvancedFilter: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"enableAdvancedFilter\", \"enableAdvancedFilter\", booleanAttribute],\n        alwaysPassFilter: \"alwaysPassFilter\",\n        includeHiddenColumnsInAdvancedFilter: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"includeHiddenColumnsInAdvancedFilter\", \"includeHiddenColumnsInAdvancedFilter\", booleanAttribute],\n        advancedFilterParent: \"advancedFilterParent\",\n        advancedFilterBuilderParams: \"advancedFilterBuilderParams\",\n        suppressAdvancedFilterEval: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressAdvancedFilterEval\", \"suppressAdvancedFilterEval\", booleanAttribute],\n        suppressSetFilterByDefault: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressSetFilterByDefault\", \"suppressSetFilterByDefault\", booleanAttribute],\n        enableCharts: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"enableCharts\", \"enableCharts\", booleanAttribute],\n        chartThemes: \"chartThemes\",\n        customChartThemes: \"customChartThemes\",\n        chartThemeOverrides: \"chartThemeOverrides\",\n        chartToolPanelsDef: \"chartToolPanelsDef\",\n        chartMenuItems: \"chartMenuItems\",\n        loadingCellRenderer: \"loadingCellRenderer\",\n        loadingCellRendererParams: \"loadingCellRendererParams\",\n        loadingCellRendererSelector: \"loadingCellRendererSelector\",\n        localeText: \"localeText\",\n        masterDetail: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"masterDetail\", \"masterDetail\", booleanAttribute],\n        keepDetailRows: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"keepDetailRows\", \"keepDetailRows\", booleanAttribute],\n        keepDetailRowsCount: \"keepDetailRowsCount\",\n        detailCellRenderer: \"detailCellRenderer\",\n        detailCellRendererParams: \"detailCellRendererParams\",\n        detailRowHeight: \"detailRowHeight\",\n        detailRowAutoHeight: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"detailRowAutoHeight\", \"detailRowAutoHeight\", booleanAttribute],\n        context: \"context\",\n        alignedGrids: \"alignedGrids\",\n        tabIndex: \"tabIndex\",\n        rowBuffer: \"rowBuffer\",\n        valueCache: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"valueCache\", \"valueCache\", booleanAttribute],\n        valueCacheNeverExpires: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"valueCacheNeverExpires\", \"valueCacheNeverExpires\", booleanAttribute],\n        enableCellExpressions: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"enableCellExpressions\", \"enableCellExpressions\", booleanAttribute],\n        suppressTouch: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressTouch\", \"suppressTouch\", booleanAttribute],\n        suppressFocusAfterRefresh: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressFocusAfterRefresh\", \"suppressFocusAfterRefresh\", booleanAttribute],\n        suppressBrowserResizeObserver: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressBrowserResizeObserver\", \"suppressBrowserResizeObserver\", booleanAttribute],\n        suppressPropertyNamesCheck: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressPropertyNamesCheck\", \"suppressPropertyNamesCheck\", booleanAttribute],\n        suppressChangeDetection: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressChangeDetection\", \"suppressChangeDetection\", booleanAttribute],\n        debug: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"debug\", \"debug\", booleanAttribute],\n        loading: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"loading\", \"loading\", booleanAttribute],\n        overlayLoadingTemplate: \"overlayLoadingTemplate\",\n        loadingOverlayComponent: \"loadingOverlayComponent\",\n        loadingOverlayComponentParams: \"loadingOverlayComponentParams\",\n        suppressLoadingOverlay: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressLoadingOverlay\", \"suppressLoadingOverlay\", booleanAttribute],\n        overlayNoRowsTemplate: \"overlayNoRowsTemplate\",\n        noRowsOverlayComponent: \"noRowsOverlayComponent\",\n        noRowsOverlayComponentParams: \"noRowsOverlayComponentParams\",\n        suppressNoRowsOverlay: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressNoRowsOverlay\", \"suppressNoRowsOverlay\", booleanAttribute],\n        pagination: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"pagination\", \"pagination\", booleanAttribute],\n        paginationPageSize: \"paginationPageSize\",\n        paginationPageSizeSelector: \"paginationPageSizeSelector\",\n        paginationAutoPageSize: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"paginationAutoPageSize\", \"paginationAutoPageSize\", booleanAttribute],\n        paginateChildRows: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"paginateChildRows\", \"paginateChildRows\", booleanAttribute],\n        suppressPaginationPanel: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressPaginationPanel\", \"suppressPaginationPanel\", booleanAttribute],\n        pivotMode: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"pivotMode\", \"pivotMode\", booleanAttribute],\n        pivotPanelShow: \"pivotPanelShow\",\n        pivotMaxGeneratedColumns: \"pivotMaxGeneratedColumns\",\n        pivotDefaultExpanded: \"pivotDefaultExpanded\",\n        pivotColumnGroupTotals: \"pivotColumnGroupTotals\",\n        pivotRowTotals: \"pivotRowTotals\",\n        pivotSuppressAutoColumn: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"pivotSuppressAutoColumn\", \"pivotSuppressAutoColumn\", booleanAttribute],\n        suppressExpandablePivotGroups: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressExpandablePivotGroups\", \"suppressExpandablePivotGroups\", booleanAttribute],\n        functionsReadOnly: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"functionsReadOnly\", \"functionsReadOnly\", booleanAttribute],\n        aggFuncs: \"aggFuncs\",\n        suppressAggFuncInHeader: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressAggFuncInHeader\", \"suppressAggFuncInHeader\", booleanAttribute],\n        alwaysAggregateAtRootLevel: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"alwaysAggregateAtRootLevel\", \"alwaysAggregateAtRootLevel\", booleanAttribute],\n        aggregateOnlyChangedColumns: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"aggregateOnlyChangedColumns\", \"aggregateOnlyChangedColumns\", booleanAttribute],\n        suppressAggFilteredOnly: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressAggFilteredOnly\", \"suppressAggFilteredOnly\", booleanAttribute],\n        removePivotHeaderRowWhenSingleValueColumn: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"removePivotHeaderRowWhenSingleValueColumn\", \"removePivotHeaderRowWhenSingleValueColumn\", booleanAttribute],\n        animateRows: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"animateRows\", \"animateRows\", booleanAttribute],\n        cellFlashDuration: \"cellFlashDuration\",\n        cellFadeDuration: \"cellFadeDuration\",\n        allowShowChangeAfterFilter: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"allowShowChangeAfterFilter\", \"allowShowChangeAfterFilter\", booleanAttribute],\n        domLayout: \"domLayout\",\n        ensureDomOrder: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"ensureDomOrder\", \"ensureDomOrder\", booleanAttribute],\n        enableCellSpan: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"enableCellSpan\", \"enableCellSpan\", booleanAttribute],\n        enableRtl: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"enableRtl\", \"enableRtl\", booleanAttribute],\n        suppressColumnVirtualisation: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressColumnVirtualisation\", \"suppressColumnVirtualisation\", booleanAttribute],\n        suppressMaxRenderedRowRestriction: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressMaxRenderedRowRestriction\", \"suppressMaxRenderedRowRestriction\", booleanAttribute],\n        suppressRowVirtualisation: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressRowVirtualisation\", \"suppressRowVirtualisation\", booleanAttribute],\n        rowDragManaged: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"rowDragManaged\", \"rowDragManaged\", booleanAttribute],\n        suppressRowDrag: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressRowDrag\", \"suppressRowDrag\", booleanAttribute],\n        suppressMoveWhenRowDragging: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressMoveWhenRowDragging\", \"suppressMoveWhenRowDragging\", booleanAttribute],\n        rowDragEntireRow: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"rowDragEntireRow\", \"rowDragEntireRow\", booleanAttribute],\n        rowDragMultiRow: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"rowDragMultiRow\", \"rowDragMultiRow\", booleanAttribute],\n        rowDragText: \"rowDragText\",\n        dragAndDropImageComponent: \"dragAndDropImageComponent\",\n        dragAndDropImageComponentParams: \"dragAndDropImageComponentParams\",\n        fullWidthCellRenderer: \"fullWidthCellRenderer\",\n        fullWidthCellRendererParams: \"fullWidthCellRendererParams\",\n        embedFullWidthRows: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"embedFullWidthRows\", \"embedFullWidthRows\", booleanAttribute],\n        groupDisplayType: \"groupDisplayType\",\n        groupDefaultExpanded: \"groupDefaultExpanded\",\n        autoGroupColumnDef: \"autoGroupColumnDef\",\n        groupMaintainOrder: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"groupMaintainOrder\", \"groupMaintainOrder\", booleanAttribute],\n        groupSelectsChildren: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"groupSelectsChildren\", \"groupSelectsChildren\", booleanAttribute],\n        groupLockGroupColumns: \"groupLockGroupColumns\",\n        groupAggFiltering: \"groupAggFiltering\",\n        groupTotalRow: \"groupTotalRow\",\n        grandTotalRow: \"grandTotalRow\",\n        suppressStickyTotalRow: \"suppressStickyTotalRow\",\n        groupSuppressBlankHeader: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"groupSuppressBlankHeader\", \"groupSuppressBlankHeader\", booleanAttribute],\n        groupSelectsFiltered: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"groupSelectsFiltered\", \"groupSelectsFiltered\", booleanAttribute],\n        showOpenedGroup: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showOpenedGroup\", \"showOpenedGroup\", booleanAttribute],\n        groupHideParentOfSingleChild: \"groupHideParentOfSingleChild\",\n        groupRemoveSingleChildren: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"groupRemoveSingleChildren\", \"groupRemoveSingleChildren\", booleanAttribute],\n        groupRemoveLowestSingleChildren: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"groupRemoveLowestSingleChildren\", \"groupRemoveLowestSingleChildren\", booleanAttribute],\n        groupHideOpenParents: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"groupHideOpenParents\", \"groupHideOpenParents\", booleanAttribute],\n        groupAllowUnbalanced: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"groupAllowUnbalanced\", \"groupAllowUnbalanced\", booleanAttribute],\n        rowGroupPanelShow: \"rowGroupPanelShow\",\n        groupRowRenderer: \"groupRowRenderer\",\n        groupRowRendererParams: \"groupRowRendererParams\",\n        treeData: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"treeData\", \"treeData\", booleanAttribute],\n        treeDataChildrenField: \"treeDataChildrenField\",\n        treeDataParentIdField: \"treeDataParentIdField\",\n        rowGroupPanelSuppressSort: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"rowGroupPanelSuppressSort\", \"rowGroupPanelSuppressSort\", booleanAttribute],\n        suppressGroupRowsSticky: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressGroupRowsSticky\", \"suppressGroupRowsSticky\", booleanAttribute],\n        pinnedTopRowData: \"pinnedTopRowData\",\n        pinnedBottomRowData: \"pinnedBottomRowData\",\n        enableRowPinning: \"enableRowPinning\",\n        isRowPinnable: \"isRowPinnable\",\n        isRowPinned: \"isRowPinned\",\n        rowModelType: \"rowModelType\",\n        rowData: \"rowData\",\n        asyncTransactionWaitMillis: \"asyncTransactionWaitMillis\",\n        suppressModelUpdateAfterUpdateTransaction: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressModelUpdateAfterUpdateTransaction\", \"suppressModelUpdateAfterUpdateTransaction\", booleanAttribute],\n        datasource: \"datasource\",\n        cacheOverflowSize: \"cacheOverflowSize\",\n        infiniteInitialRowCount: \"infiniteInitialRowCount\",\n        serverSideInitialRowCount: \"serverSideInitialRowCount\",\n        suppressServerSideFullWidthLoadingRow: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressServerSideFullWidthLoadingRow\", \"suppressServerSideFullWidthLoadingRow\", booleanAttribute],\n        cacheBlockSize: \"cacheBlockSize\",\n        maxBlocksInCache: \"maxBlocksInCache\",\n        maxConcurrentDatasourceRequests: \"maxConcurrentDatasourceRequests\",\n        blockLoadDebounceMillis: \"blockLoadDebounceMillis\",\n        purgeClosedRowNodes: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"purgeClosedRowNodes\", \"purgeClosedRowNodes\", booleanAttribute],\n        serverSideDatasource: \"serverSideDatasource\",\n        serverSideSortAllLevels: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"serverSideSortAllLevels\", \"serverSideSortAllLevels\", booleanAttribute],\n        serverSideEnableClientSideSort: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"serverSideEnableClientSideSort\", \"serverSideEnableClientSideSort\", booleanAttribute],\n        serverSideOnlyRefreshFilteredGroups: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"serverSideOnlyRefreshFilteredGroups\", \"serverSideOnlyRefreshFilteredGroups\", booleanAttribute],\n        serverSidePivotResultFieldSeparator: \"serverSidePivotResultFieldSeparator\",\n        viewportDatasource: \"viewportDatasource\",\n        viewportRowModelPageSize: \"viewportRowModelPageSize\",\n        viewportRowModelBufferSize: \"viewportRowModelBufferSize\",\n        alwaysShowHorizontalScroll: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"alwaysShowHorizontalScroll\", \"alwaysShowHorizontalScroll\", booleanAttribute],\n        alwaysShowVerticalScroll: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"alwaysShowVerticalScroll\", \"alwaysShowVerticalScroll\", booleanAttribute],\n        debounceVerticalScrollbar: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"debounceVerticalScrollbar\", \"debounceVerticalScrollbar\", booleanAttribute],\n        suppressHorizontalScroll: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressHorizontalScroll\", \"suppressHorizontalScroll\", booleanAttribute],\n        suppressScrollOnNewData: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressScrollOnNewData\", \"suppressScrollOnNewData\", booleanAttribute],\n        suppressScrollWhenPopupsAreOpen: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressScrollWhenPopupsAreOpen\", \"suppressScrollWhenPopupsAreOpen\", booleanAttribute],\n        suppressAnimationFrame: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressAnimationFrame\", \"suppressAnimationFrame\", booleanAttribute],\n        suppressMiddleClickScrolls: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressMiddleClickScrolls\", \"suppressMiddleClickScrolls\", booleanAttribute],\n        suppressPreventDefaultOnMouseWheel: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressPreventDefaultOnMouseWheel\", \"suppressPreventDefaultOnMouseWheel\", booleanAttribute],\n        scrollbarWidth: \"scrollbarWidth\",\n        rowSelection: \"rowSelection\",\n        cellSelection: \"cellSelection\",\n        rowMultiSelectWithClick: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"rowMultiSelectWithClick\", \"rowMultiSelectWithClick\", booleanAttribute],\n        suppressRowDeselection: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressRowDeselection\", \"suppressRowDeselection\", booleanAttribute],\n        suppressRowClickSelection: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressRowClickSelection\", \"suppressRowClickSelection\", booleanAttribute],\n        suppressCellFocus: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressCellFocus\", \"suppressCellFocus\", booleanAttribute],\n        suppressHeaderFocus: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressHeaderFocus\", \"suppressHeaderFocus\", booleanAttribute],\n        selectionColumnDef: \"selectionColumnDef\",\n        rowNumbers: \"rowNumbers\",\n        suppressMultiRangeSelection: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressMultiRangeSelection\", \"suppressMultiRangeSelection\", booleanAttribute],\n        enableCellTextSelection: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"enableCellTextSelection\", \"enableCellTextSelection\", booleanAttribute],\n        enableRangeSelection: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"enableRangeSelection\", \"enableRangeSelection\", booleanAttribute],\n        enableRangeHandle: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"enableRangeHandle\", \"enableRangeHandle\", booleanAttribute],\n        enableFillHandle: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"enableFillHandle\", \"enableFillHandle\", booleanAttribute],\n        fillHandleDirection: \"fillHandleDirection\",\n        suppressClearOnFillReduction: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressClearOnFillReduction\", \"suppressClearOnFillReduction\", booleanAttribute],\n        sortingOrder: \"sortingOrder\",\n        accentedSort: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"accentedSort\", \"accentedSort\", booleanAttribute],\n        unSortIcon: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"unSortIcon\", \"unSortIcon\", booleanAttribute],\n        suppressMultiSort: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressMultiSort\", \"suppressMultiSort\", booleanAttribute],\n        alwaysMultiSort: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"alwaysMultiSort\", \"alwaysMultiSort\", booleanAttribute],\n        multiSortKey: \"multiSortKey\",\n        suppressMaintainUnsortedOrder: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressMaintainUnsortedOrder\", \"suppressMaintainUnsortedOrder\", booleanAttribute],\n        icons: \"icons\",\n        rowHeight: \"rowHeight\",\n        rowStyle: \"rowStyle\",\n        rowClass: \"rowClass\",\n        rowClassRules: \"rowClassRules\",\n        suppressRowHoverHighlight: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressRowHoverHighlight\", \"suppressRowHoverHighlight\", booleanAttribute],\n        suppressRowTransform: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"suppressRowTransform\", \"suppressRowTransform\", booleanAttribute],\n        columnHoverHighlight: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"columnHoverHighlight\", \"columnHoverHighlight\", booleanAttribute],\n        gridId: \"gridId\",\n        deltaSort: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"deltaSort\", \"deltaSort\", booleanAttribute],\n        treeDataDisplayType: \"treeDataDisplayType\",\n        enableGroupEdit: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"enableGroupEdit\", \"enableGroupEdit\", booleanAttribute],\n        initialState: \"initialState\",\n        theme: \"theme\",\n        loadThemeGoogleFonts: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"loadThemeGoogleFonts\", \"loadThemeGoogleFonts\", booleanAttribute],\n        themeCssLayer: \"themeCssLayer\",\n        styleNonce: \"styleNonce\",\n        themeStyleContainer: \"themeStyleContainer\",\n        getContextMenuItems: \"getContextMenuItems\",\n        getMainMenuItems: \"getMainMenuItems\",\n        postProcessPopup: \"postProcessPopup\",\n        processUnpinnedColumns: \"processUnpinnedColumns\",\n        processCellForClipboard: \"processCellForClipboard\",\n        processHeaderForClipboard: \"processHeaderForClipboard\",\n        processGroupHeaderForClipboard: \"processGroupHeaderForClipboard\",\n        processCellFromClipboard: \"processCellFromClipboard\",\n        sendToClipboard: \"sendToClipboard\",\n        processDataFromClipboard: \"processDataFromClipboard\",\n        isExternalFilterPresent: \"isExternalFilterPresent\",\n        doesExternalFilterPass: \"doesExternalFilterPass\",\n        getChartToolbarItems: \"getChartToolbarItems\",\n        createChartContainer: \"createChartContainer\",\n        focusGridInnerElement: \"focusGridInnerElement\",\n        navigateToNextHeader: \"navigateToNextHeader\",\n        tabToNextHeader: \"tabToNextHeader\",\n        navigateToNextCell: \"navigateToNextCell\",\n        tabToNextCell: \"tabToNextCell\",\n        getLocaleText: \"getLocaleText\",\n        getDocument: \"getDocument\",\n        paginationNumberFormatter: \"paginationNumberFormatter\",\n        getGroupRowAgg: \"getGroupRowAgg\",\n        isGroupOpenByDefault: \"isGroupOpenByDefault\",\n        initialGroupOrderComparator: \"initialGroupOrderComparator\",\n        processPivotResultColDef: \"processPivotResultColDef\",\n        processPivotResultColGroupDef: \"processPivotResultColGroupDef\",\n        getDataPath: \"getDataPath\",\n        getChildCount: \"getChildCount\",\n        getServerSideGroupLevelParams: \"getServerSideGroupLevelParams\",\n        isServerSideGroupOpenByDefault: \"isServerSideGroupOpenByDefault\",\n        isApplyServerSideTransaction: \"isApplyServerSideTransaction\",\n        isServerSideGroup: \"isServerSideGroup\",\n        getServerSideGroupKey: \"getServerSideGroupKey\",\n        getBusinessKeyForNode: \"getBusinessKeyForNode\",\n        getRowId: \"getRowId\",\n        resetRowDataOnUpdate: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"resetRowDataOnUpdate\", \"resetRowDataOnUpdate\", booleanAttribute],\n        processRowPostCreate: \"processRowPostCreate\",\n        isRowSelectable: \"isRowSelectable\",\n        isRowMaster: \"isRowMaster\",\n        fillOperation: \"fillOperation\",\n        postSortRows: \"postSortRows\",\n        getRowStyle: \"getRowStyle\",\n        getRowClass: \"getRowClass\",\n        getRowHeight: \"getRowHeight\",\n        isFullWidthRow: \"isFullWidthRow\"\n      },\n      outputs: {\n        toolPanelVisibleChanged: \"toolPanelVisibleChanged\",\n        toolPanelSizeChanged: \"toolPanelSizeChanged\",\n        columnMenuVisibleChanged: \"columnMenuVisibleChanged\",\n        contextMenuVisibleChanged: \"contextMenuVisibleChanged\",\n        cutStart: \"cutStart\",\n        cutEnd: \"cutEnd\",\n        pasteStart: \"pasteStart\",\n        pasteEnd: \"pasteEnd\",\n        columnVisible: \"columnVisible\",\n        columnPinned: \"columnPinned\",\n        columnResized: \"columnResized\",\n        columnMoved: \"columnMoved\",\n        columnValueChanged: \"columnValueChanged\",\n        columnPivotModeChanged: \"columnPivotModeChanged\",\n        columnPivotChanged: \"columnPivotChanged\",\n        columnGroupOpened: \"columnGroupOpened\",\n        newColumnsLoaded: \"newColumnsLoaded\",\n        gridColumnsChanged: \"gridColumnsChanged\",\n        displayedColumnsChanged: \"displayedColumnsChanged\",\n        virtualColumnsChanged: \"virtualColumnsChanged\",\n        columnEverythingChanged: \"columnEverythingChanged\",\n        columnHeaderMouseOver: \"columnHeaderMouseOver\",\n        columnHeaderMouseLeave: \"columnHeaderMouseLeave\",\n        columnHeaderClicked: \"columnHeaderClicked\",\n        columnHeaderContextMenu: \"columnHeaderContextMenu\",\n        componentStateChanged: \"componentStateChanged\",\n        cellValueChanged: \"cellValueChanged\",\n        cellEditRequest: \"cellEditRequest\",\n        rowValueChanged: \"rowValueChanged\",\n        cellEditingStarted: \"cellEditingStarted\",\n        cellEditingStopped: \"cellEditingStopped\",\n        rowEditingStarted: \"rowEditingStarted\",\n        rowEditingStopped: \"rowEditingStopped\",\n        undoStarted: \"undoStarted\",\n        undoEnded: \"undoEnded\",\n        redoStarted: \"redoStarted\",\n        redoEnded: \"redoEnded\",\n        cellSelectionDeleteStart: \"cellSelectionDeleteStart\",\n        cellSelectionDeleteEnd: \"cellSelectionDeleteEnd\",\n        rangeDeleteStart: \"rangeDeleteStart\",\n        rangeDeleteEnd: \"rangeDeleteEnd\",\n        fillStart: \"fillStart\",\n        fillEnd: \"fillEnd\",\n        filterOpened: \"filterOpened\",\n        filterChanged: \"filterChanged\",\n        filterModified: \"filterModified\",\n        advancedFilterBuilderVisibleChanged: \"advancedFilterBuilderVisibleChanged\",\n        findChanged: \"findChanged\",\n        chartCreated: \"chartCreated\",\n        chartRangeSelectionChanged: \"chartRangeSelectionChanged\",\n        chartOptionsChanged: \"chartOptionsChanged\",\n        chartDestroyed: \"chartDestroyed\",\n        cellKeyDown: \"cellKeyDown\",\n        gridReady: \"gridReady\",\n        firstDataRendered: \"firstDataRendered\",\n        gridSizeChanged: \"gridSizeChanged\",\n        modelUpdated: \"modelUpdated\",\n        virtualRowRemoved: \"virtualRowRemoved\",\n        viewportChanged: \"viewportChanged\",\n        bodyScroll: \"bodyScroll\",\n        bodyScrollEnd: \"bodyScrollEnd\",\n        dragStarted: \"dragStarted\",\n        dragStopped: \"dragStopped\",\n        dragCancelled: \"dragCancelled\",\n        stateUpdated: \"stateUpdated\",\n        paginationChanged: \"paginationChanged\",\n        rowDragEnter: \"rowDragEnter\",\n        rowDragMove: \"rowDragMove\",\n        rowDragLeave: \"rowDragLeave\",\n        rowDragEnd: \"rowDragEnd\",\n        rowDragCancel: \"rowDragCancel\",\n        rowResizeStarted: \"rowResizeStarted\",\n        rowResizeEnded: \"rowResizeEnded\",\n        columnRowGroupChanged: \"columnRowGroupChanged\",\n        rowGroupOpened: \"rowGroupOpened\",\n        expandOrCollapseAll: \"expandOrCollapseAll\",\n        pivotMaxColumnsExceeded: \"pivotMaxColumnsExceeded\",\n        pinnedRowDataChanged: \"pinnedRowDataChanged\",\n        pinnedRowsChanged: \"pinnedRowsChanged\",\n        rowDataUpdated: \"rowDataUpdated\",\n        asyncTransactionsFlushed: \"asyncTransactionsFlushed\",\n        storeRefreshed: \"storeRefreshed\",\n        headerFocused: \"headerFocused\",\n        cellClicked: \"cellClicked\",\n        cellDoubleClicked: \"cellDoubleClicked\",\n        cellFocused: \"cellFocused\",\n        cellMouseOver: \"cellMouseOver\",\n        cellMouseOut: \"cellMouseOut\",\n        cellMouseDown: \"cellMouseDown\",\n        rowClicked: \"rowClicked\",\n        rowDoubleClicked: \"rowDoubleClicked\",\n        rowSelected: \"rowSelected\",\n        selectionChanged: \"selectionChanged\",\n        cellContextMenu: \"cellContextMenu\",\n        rangeSelectionChanged: \"rangeSelectionChanged\",\n        cellSelectionChanged: \"cellSelectionChanged\",\n        tooltipShow: \"tooltipShow\",\n        tooltipHide: \"tooltipHide\",\n        sortChanged: \"sortChanged\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([AngularFrameworkOverrides, AngularFrameworkComponentWrapper]), i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 0,\n      vars: 0,\n      template: function AgGridAngular_Template(rf, ctx) {},\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AgGridAngular, [{\n    type: Component,\n    args: [{\n      selector: 'ag-grid-angular',\n      standalone: true,\n      template: '',\n      providers: [AngularFrameworkOverrides, AngularFrameworkComponentWrapper],\n      // tell angular we don't want view encapsulation, we don't want a shadow root\n      encapsulation: ViewEncapsulation.None\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: AngularFrameworkOverrides\n  }, {\n    type: AngularFrameworkComponentWrapper\n  }], {\n    gridOptions: [{\n      type: Input\n    }],\n    modules: [{\n      type: Input\n    }],\n    statusBar: [{\n      type: Input\n    }],\n    sideBar: [{\n      type: Input\n    }],\n    suppressContextMenu: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    preventDefaultOnContextMenu: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    allowContextMenuWithControlKey: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    columnMenu: [{\n      type: Input\n    }],\n    suppressMenuHide: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    enableBrowserTooltips: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tooltipTrigger: [{\n      type: Input\n    }],\n    tooltipShowDelay: [{\n      type: Input\n    }],\n    tooltipHideDelay: [{\n      type: Input\n    }],\n    tooltipMouseTrack: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tooltipShowMode: [{\n      type: Input\n    }],\n    tooltipInteraction: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    popupParent: [{\n      type: Input\n    }],\n    copyHeadersToClipboard: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    copyGroupHeadersToClipboard: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    clipboardDelimiter: [{\n      type: Input\n    }],\n    suppressCopyRowsToClipboard: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressCopySingleCellRanges: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressLastEmptyLineOnPaste: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressClipboardPaste: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressClipboardApi: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressCutToClipboard: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    columnDefs: [{\n      type: Input\n    }],\n    defaultColDef: [{\n      type: Input\n    }],\n    defaultColGroupDef: [{\n      type: Input\n    }],\n    columnTypes: [{\n      type: Input\n    }],\n    dataTypeDefinitions: [{\n      type: Input\n    }],\n    maintainColumnOrder: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    enableStrictPivotColumnOrder: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressFieldDotNotation: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    headerHeight: [{\n      type: Input\n    }],\n    groupHeaderHeight: [{\n      type: Input\n    }],\n    floatingFiltersHeight: [{\n      type: Input\n    }],\n    pivotHeaderHeight: [{\n      type: Input\n    }],\n    pivotGroupHeaderHeight: [{\n      type: Input\n    }],\n    allowDragFromColumnsToolPanel: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressMovableColumns: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressColumnMoveAnimation: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressMoveWhenColumnDragging: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressDragLeaveHidesColumns: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressGroupChangesColumnVisibility: [{\n      type: Input\n    }],\n    suppressMakeColumnVisibleAfterUnGroup: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressRowGroupHidesColumns: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    colResizeDefault: [{\n      type: Input\n    }],\n    suppressAutoSize: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoSizePadding: [{\n      type: Input\n    }],\n    skipHeaderOnAutoSize: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    autoSizeStrategy: [{\n      type: Input\n    }],\n    components: [{\n      type: Input\n    }],\n    editType: [{\n      type: Input\n    }],\n    singleClickEdit: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressClickEdit: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    readOnlyEdit: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    stopEditingWhenCellsLoseFocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    enterNavigatesVertically: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    enterNavigatesVerticallyAfterEdit: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    enableCellEditingOnBackspace: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    undoRedoCellEditing: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    undoRedoCellEditingLimit: [{\n      type: Input\n    }],\n    defaultCsvExportParams: [{\n      type: Input\n    }],\n    suppressCsvExport: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    defaultExcelExportParams: [{\n      type: Input\n    }],\n    suppressExcelExport: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    excelStyles: [{\n      type: Input\n    }],\n    findSearchValue: [{\n      type: Input\n    }],\n    findOptions: [{\n      type: Input\n    }],\n    quickFilterText: [{\n      type: Input\n    }],\n    cacheQuickFilter: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    includeHiddenColumnsInQuickFilter: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    quickFilterParser: [{\n      type: Input\n    }],\n    quickFilterMatcher: [{\n      type: Input\n    }],\n    applyQuickFilterBeforePivotOrAgg: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    excludeChildrenWhenTreeDataFiltering: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    enableAdvancedFilter: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    alwaysPassFilter: [{\n      type: Input\n    }],\n    includeHiddenColumnsInAdvancedFilter: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    advancedFilterParent: [{\n      type: Input\n    }],\n    advancedFilterBuilderParams: [{\n      type: Input\n    }],\n    suppressAdvancedFilterEval: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressSetFilterByDefault: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    enableCharts: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    chartThemes: [{\n      type: Input\n    }],\n    customChartThemes: [{\n      type: Input\n    }],\n    chartThemeOverrides: [{\n      type: Input\n    }],\n    chartToolPanelsDef: [{\n      type: Input\n    }],\n    chartMenuItems: [{\n      type: Input\n    }],\n    loadingCellRenderer: [{\n      type: Input\n    }],\n    loadingCellRendererParams: [{\n      type: Input\n    }],\n    loadingCellRendererSelector: [{\n      type: Input\n    }],\n    localeText: [{\n      type: Input\n    }],\n    masterDetail: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    keepDetailRows: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    keepDetailRowsCount: [{\n      type: Input\n    }],\n    detailCellRenderer: [{\n      type: Input\n    }],\n    detailCellRendererParams: [{\n      type: Input\n    }],\n    detailRowHeight: [{\n      type: Input\n    }],\n    detailRowAutoHeight: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    context: [{\n      type: Input\n    }],\n    alignedGrids: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input\n    }],\n    rowBuffer: [{\n      type: Input\n    }],\n    valueCache: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    valueCacheNeverExpires: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    enableCellExpressions: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressTouch: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressFocusAfterRefresh: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressBrowserResizeObserver: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressPropertyNamesCheck: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressChangeDetection: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    debug: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    loading: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    overlayLoadingTemplate: [{\n      type: Input\n    }],\n    loadingOverlayComponent: [{\n      type: Input\n    }],\n    loadingOverlayComponentParams: [{\n      type: Input\n    }],\n    suppressLoadingOverlay: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    overlayNoRowsTemplate: [{\n      type: Input\n    }],\n    noRowsOverlayComponent: [{\n      type: Input\n    }],\n    noRowsOverlayComponentParams: [{\n      type: Input\n    }],\n    suppressNoRowsOverlay: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    pagination: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    paginationPageSize: [{\n      type: Input\n    }],\n    paginationPageSizeSelector: [{\n      type: Input\n    }],\n    paginationAutoPageSize: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    paginateChildRows: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressPaginationPanel: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    pivotMode: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    pivotPanelShow: [{\n      type: Input\n    }],\n    pivotMaxGeneratedColumns: [{\n      type: Input\n    }],\n    pivotDefaultExpanded: [{\n      type: Input\n    }],\n    pivotColumnGroupTotals: [{\n      type: Input\n    }],\n    pivotRowTotals: [{\n      type: Input\n    }],\n    pivotSuppressAutoColumn: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressExpandablePivotGroups: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    functionsReadOnly: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    aggFuncs: [{\n      type: Input\n    }],\n    suppressAggFuncInHeader: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    alwaysAggregateAtRootLevel: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    aggregateOnlyChangedColumns: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressAggFilteredOnly: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    removePivotHeaderRowWhenSingleValueColumn: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    animateRows: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    cellFlashDuration: [{\n      type: Input\n    }],\n    cellFadeDuration: [{\n      type: Input\n    }],\n    allowShowChangeAfterFilter: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    domLayout: [{\n      type: Input\n    }],\n    ensureDomOrder: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    enableCellSpan: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    enableRtl: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressColumnVirtualisation: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressMaxRenderedRowRestriction: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressRowVirtualisation: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    rowDragManaged: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressRowDrag: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressMoveWhenRowDragging: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    rowDragEntireRow: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    rowDragMultiRow: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    rowDragText: [{\n      type: Input\n    }],\n    dragAndDropImageComponent: [{\n      type: Input\n    }],\n    dragAndDropImageComponentParams: [{\n      type: Input\n    }],\n    fullWidthCellRenderer: [{\n      type: Input\n    }],\n    fullWidthCellRendererParams: [{\n      type: Input\n    }],\n    embedFullWidthRows: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    groupDisplayType: [{\n      type: Input\n    }],\n    groupDefaultExpanded: [{\n      type: Input\n    }],\n    autoGroupColumnDef: [{\n      type: Input\n    }],\n    groupMaintainOrder: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    groupSelectsChildren: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    groupLockGroupColumns: [{\n      type: Input\n    }],\n    groupAggFiltering: [{\n      type: Input\n    }],\n    groupTotalRow: [{\n      type: Input\n    }],\n    grandTotalRow: [{\n      type: Input\n    }],\n    suppressStickyTotalRow: [{\n      type: Input\n    }],\n    groupSuppressBlankHeader: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    groupSelectsFiltered: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showOpenedGroup: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    groupHideParentOfSingleChild: [{\n      type: Input\n    }],\n    groupRemoveSingleChildren: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    groupRemoveLowestSingleChildren: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    groupHideOpenParents: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    groupAllowUnbalanced: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    rowGroupPanelShow: [{\n      type: Input\n    }],\n    groupRowRenderer: [{\n      type: Input\n    }],\n    groupRowRendererParams: [{\n      type: Input\n    }],\n    treeData: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    treeDataChildrenField: [{\n      type: Input\n    }],\n    treeDataParentIdField: [{\n      type: Input\n    }],\n    rowGroupPanelSuppressSort: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressGroupRowsSticky: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    pinnedTopRowData: [{\n      type: Input\n    }],\n    pinnedBottomRowData: [{\n      type: Input\n    }],\n    enableRowPinning: [{\n      type: Input\n    }],\n    isRowPinnable: [{\n      type: Input\n    }],\n    isRowPinned: [{\n      type: Input\n    }],\n    rowModelType: [{\n      type: Input\n    }],\n    rowData: [{\n      type: Input\n    }],\n    asyncTransactionWaitMillis: [{\n      type: Input\n    }],\n    suppressModelUpdateAfterUpdateTransaction: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    datasource: [{\n      type: Input\n    }],\n    cacheOverflowSize: [{\n      type: Input\n    }],\n    infiniteInitialRowCount: [{\n      type: Input\n    }],\n    serverSideInitialRowCount: [{\n      type: Input\n    }],\n    suppressServerSideFullWidthLoadingRow: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    cacheBlockSize: [{\n      type: Input\n    }],\n    maxBlocksInCache: [{\n      type: Input\n    }],\n    maxConcurrentDatasourceRequests: [{\n      type: Input\n    }],\n    blockLoadDebounceMillis: [{\n      type: Input\n    }],\n    purgeClosedRowNodes: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    serverSideDatasource: [{\n      type: Input\n    }],\n    serverSideSortAllLevels: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    serverSideEnableClientSideSort: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    serverSideOnlyRefreshFilteredGroups: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    serverSidePivotResultFieldSeparator: [{\n      type: Input\n    }],\n    viewportDatasource: [{\n      type: Input\n    }],\n    viewportRowModelPageSize: [{\n      type: Input\n    }],\n    viewportRowModelBufferSize: [{\n      type: Input\n    }],\n    alwaysShowHorizontalScroll: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    alwaysShowVerticalScroll: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    debounceVerticalScrollbar: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressHorizontalScroll: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressScrollOnNewData: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressScrollWhenPopupsAreOpen: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressAnimationFrame: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressMiddleClickScrolls: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressPreventDefaultOnMouseWheel: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    scrollbarWidth: [{\n      type: Input\n    }],\n    rowSelection: [{\n      type: Input\n    }],\n    cellSelection: [{\n      type: Input\n    }],\n    rowMultiSelectWithClick: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressRowDeselection: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressRowClickSelection: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressCellFocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressHeaderFocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectionColumnDef: [{\n      type: Input\n    }],\n    rowNumbers: [{\n      type: Input\n    }],\n    suppressMultiRangeSelection: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    enableCellTextSelection: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    enableRangeSelection: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    enableRangeHandle: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    enableFillHandle: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    fillHandleDirection: [{\n      type: Input\n    }],\n    suppressClearOnFillReduction: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    sortingOrder: [{\n      type: Input\n    }],\n    accentedSort: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    unSortIcon: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressMultiSort: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    alwaysMultiSort: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    multiSortKey: [{\n      type: Input\n    }],\n    suppressMaintainUnsortedOrder: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    icons: [{\n      type: Input\n    }],\n    rowHeight: [{\n      type: Input\n    }],\n    rowStyle: [{\n      type: Input\n    }],\n    rowClass: [{\n      type: Input\n    }],\n    rowClassRules: [{\n      type: Input\n    }],\n    suppressRowHoverHighlight: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    suppressRowTransform: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    columnHoverHighlight: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    gridId: [{\n      type: Input\n    }],\n    deltaSort: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    treeDataDisplayType: [{\n      type: Input\n    }],\n    enableGroupEdit: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    initialState: [{\n      type: Input\n    }],\n    theme: [{\n      type: Input\n    }],\n    loadThemeGoogleFonts: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    themeCssLayer: [{\n      type: Input\n    }],\n    styleNonce: [{\n      type: Input\n    }],\n    themeStyleContainer: [{\n      type: Input\n    }],\n    getContextMenuItems: [{\n      type: Input\n    }],\n    getMainMenuItems: [{\n      type: Input\n    }],\n    postProcessPopup: [{\n      type: Input\n    }],\n    processUnpinnedColumns: [{\n      type: Input\n    }],\n    processCellForClipboard: [{\n      type: Input\n    }],\n    processHeaderForClipboard: [{\n      type: Input\n    }],\n    processGroupHeaderForClipboard: [{\n      type: Input\n    }],\n    processCellFromClipboard: [{\n      type: Input\n    }],\n    sendToClipboard: [{\n      type: Input\n    }],\n    processDataFromClipboard: [{\n      type: Input\n    }],\n    isExternalFilterPresent: [{\n      type: Input\n    }],\n    doesExternalFilterPass: [{\n      type: Input\n    }],\n    getChartToolbarItems: [{\n      type: Input\n    }],\n    createChartContainer: [{\n      type: Input\n    }],\n    focusGridInnerElement: [{\n      type: Input\n    }],\n    navigateToNextHeader: [{\n      type: Input\n    }],\n    tabToNextHeader: [{\n      type: Input\n    }],\n    navigateToNextCell: [{\n      type: Input\n    }],\n    tabToNextCell: [{\n      type: Input\n    }],\n    getLocaleText: [{\n      type: Input\n    }],\n    getDocument: [{\n      type: Input\n    }],\n    paginationNumberFormatter: [{\n      type: Input\n    }],\n    getGroupRowAgg: [{\n      type: Input\n    }],\n    isGroupOpenByDefault: [{\n      type: Input\n    }],\n    initialGroupOrderComparator: [{\n      type: Input\n    }],\n    processPivotResultColDef: [{\n      type: Input\n    }],\n    processPivotResultColGroupDef: [{\n      type: Input\n    }],\n    getDataPath: [{\n      type: Input\n    }],\n    getChildCount: [{\n      type: Input\n    }],\n    getServerSideGroupLevelParams: [{\n      type: Input\n    }],\n    isServerSideGroupOpenByDefault: [{\n      type: Input\n    }],\n    isApplyServerSideTransaction: [{\n      type: Input\n    }],\n    isServerSideGroup: [{\n      type: Input\n    }],\n    getServerSideGroupKey: [{\n      type: Input\n    }],\n    getBusinessKeyForNode: [{\n      type: Input\n    }],\n    getRowId: [{\n      type: Input\n    }],\n    resetRowDataOnUpdate: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    processRowPostCreate: [{\n      type: Input\n    }],\n    isRowSelectable: [{\n      type: Input\n    }],\n    isRowMaster: [{\n      type: Input\n    }],\n    fillOperation: [{\n      type: Input\n    }],\n    postSortRows: [{\n      type: Input\n    }],\n    getRowStyle: [{\n      type: Input\n    }],\n    getRowClass: [{\n      type: Input\n    }],\n    getRowHeight: [{\n      type: Input\n    }],\n    isFullWidthRow: [{\n      type: Input\n    }],\n    toolPanelVisibleChanged: [{\n      type: Output\n    }],\n    toolPanelSizeChanged: [{\n      type: Output\n    }],\n    columnMenuVisibleChanged: [{\n      type: Output\n    }],\n    contextMenuVisibleChanged: [{\n      type: Output\n    }],\n    cutStart: [{\n      type: Output\n    }],\n    cutEnd: [{\n      type: Output\n    }],\n    pasteStart: [{\n      type: Output\n    }],\n    pasteEnd: [{\n      type: Output\n    }],\n    columnVisible: [{\n      type: Output\n    }],\n    columnPinned: [{\n      type: Output\n    }],\n    columnResized: [{\n      type: Output\n    }],\n    columnMoved: [{\n      type: Output\n    }],\n    columnValueChanged: [{\n      type: Output\n    }],\n    columnPivotModeChanged: [{\n      type: Output\n    }],\n    columnPivotChanged: [{\n      type: Output\n    }],\n    columnGroupOpened: [{\n      type: Output\n    }],\n    newColumnsLoaded: [{\n      type: Output\n    }],\n    gridColumnsChanged: [{\n      type: Output\n    }],\n    displayedColumnsChanged: [{\n      type: Output\n    }],\n    virtualColumnsChanged: [{\n      type: Output\n    }],\n    columnEverythingChanged: [{\n      type: Output\n    }],\n    columnHeaderMouseOver: [{\n      type: Output\n    }],\n    columnHeaderMouseLeave: [{\n      type: Output\n    }],\n    columnHeaderClicked: [{\n      type: Output\n    }],\n    columnHeaderContextMenu: [{\n      type: Output\n    }],\n    componentStateChanged: [{\n      type: Output\n    }],\n    cellValueChanged: [{\n      type: Output\n    }],\n    cellEditRequest: [{\n      type: Output\n    }],\n    rowValueChanged: [{\n      type: Output\n    }],\n    cellEditingStarted: [{\n      type: Output\n    }],\n    cellEditingStopped: [{\n      type: Output\n    }],\n    rowEditingStarted: [{\n      type: Output\n    }],\n    rowEditingStopped: [{\n      type: Output\n    }],\n    undoStarted: [{\n      type: Output\n    }],\n    undoEnded: [{\n      type: Output\n    }],\n    redoStarted: [{\n      type: Output\n    }],\n    redoEnded: [{\n      type: Output\n    }],\n    cellSelectionDeleteStart: [{\n      type: Output\n    }],\n    cellSelectionDeleteEnd: [{\n      type: Output\n    }],\n    rangeDeleteStart: [{\n      type: Output\n    }],\n    rangeDeleteEnd: [{\n      type: Output\n    }],\n    fillStart: [{\n      type: Output\n    }],\n    fillEnd: [{\n      type: Output\n    }],\n    filterOpened: [{\n      type: Output\n    }],\n    filterChanged: [{\n      type: Output\n    }],\n    filterModified: [{\n      type: Output\n    }],\n    advancedFilterBuilderVisibleChanged: [{\n      type: Output\n    }],\n    findChanged: [{\n      type: Output\n    }],\n    chartCreated: [{\n      type: Output\n    }],\n    chartRangeSelectionChanged: [{\n      type: Output\n    }],\n    chartOptionsChanged: [{\n      type: Output\n    }],\n    chartDestroyed: [{\n      type: Output\n    }],\n    cellKeyDown: [{\n      type: Output\n    }],\n    gridReady: [{\n      type: Output\n    }],\n    firstDataRendered: [{\n      type: Output\n    }],\n    gridSizeChanged: [{\n      type: Output\n    }],\n    modelUpdated: [{\n      type: Output\n    }],\n    virtualRowRemoved: [{\n      type: Output\n    }],\n    viewportChanged: [{\n      type: Output\n    }],\n    bodyScroll: [{\n      type: Output\n    }],\n    bodyScrollEnd: [{\n      type: Output\n    }],\n    dragStarted: [{\n      type: Output\n    }],\n    dragStopped: [{\n      type: Output\n    }],\n    dragCancelled: [{\n      type: Output\n    }],\n    stateUpdated: [{\n      type: Output\n    }],\n    paginationChanged: [{\n      type: Output\n    }],\n    rowDragEnter: [{\n      type: Output\n    }],\n    rowDragMove: [{\n      type: Output\n    }],\n    rowDragLeave: [{\n      type: Output\n    }],\n    rowDragEnd: [{\n      type: Output\n    }],\n    rowDragCancel: [{\n      type: Output\n    }],\n    rowResizeStarted: [{\n      type: Output\n    }],\n    rowResizeEnded: [{\n      type: Output\n    }],\n    columnRowGroupChanged: [{\n      type: Output\n    }],\n    rowGroupOpened: [{\n      type: Output\n    }],\n    expandOrCollapseAll: [{\n      type: Output\n    }],\n    pivotMaxColumnsExceeded: [{\n      type: Output\n    }],\n    pinnedRowDataChanged: [{\n      type: Output\n    }],\n    pinnedRowsChanged: [{\n      type: Output\n    }],\n    rowDataUpdated: [{\n      type: Output\n    }],\n    asyncTransactionsFlushed: [{\n      type: Output\n    }],\n    storeRefreshed: [{\n      type: Output\n    }],\n    headerFocused: [{\n      type: Output\n    }],\n    cellClicked: [{\n      type: Output\n    }],\n    cellDoubleClicked: [{\n      type: Output\n    }],\n    cellFocused: [{\n      type: Output\n    }],\n    cellMouseOver: [{\n      type: Output\n    }],\n    cellMouseOut: [{\n      type: Output\n    }],\n    cellMouseDown: [{\n      type: Output\n    }],\n    rowClicked: [{\n      type: Output\n    }],\n    rowDoubleClicked: [{\n      type: Output\n    }],\n    rowSelected: [{\n      type: Output\n    }],\n    selectionChanged: [{\n      type: Output\n    }],\n    cellContextMenu: [{\n      type: Output\n    }],\n    rangeSelectionChanged: [{\n      type: Output\n    }],\n    cellSelectionChanged: [{\n      type: Output\n    }],\n    tooltipShow: [{\n      type: Output\n    }],\n    tooltipHide: [{\n      type: Output\n    }],\n    sortChanged: [{\n      type: Output\n    }]\n  });\n})();\nconst booleanMixedGridOptions = new Set(_BOOLEAN_MIXED_GRID_OPTIONS);\n/**\n * Used to support the user setting combined boolean and string / object properties\n * as plain HTML attributes and us correctly mapping that to true.\n * For example cellSection can be boolean or an object\n */\nfunction getValueOrCoercedValue(key, valueToUse) {\n  if (booleanMixedGridOptions.has(key)) {\n    // Handle plain HTML boolean attributes and convert them to boolean values\n    // Also handle the false string case to match Angular boolean attributes\n    return valueToUse === '' ? true : valueToUse === 'false' ? false : valueToUse;\n  }\n  return valueToUse;\n}\nclass AgGridModule {\n  static {\n    this.ɵfac = function AgGridModule_Factory(t) {\n      return new (t || AgGridModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: AgGridModule,\n      imports: [AgGridAngular],\n      exports: [AgGridAngular]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AgGridModule, [{\n    type: NgModule,\n    args: [{\n      imports: [AgGridAngular],\n      exports: [AgGridAngular]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AgComponentContainer, AgGridAngular, AgGridModule, AngularFrameworkComponentWrapper, AngularFrameworkOverrides };", "map": {"version": 3, "names": ["i0", "inject", "ViewContainerRef", "Component", "Injectable", "NgZone", "EventEmitter", "booleanAttribute", "ViewEncapsulation", "Input", "Output", "NgModule", "_removeFromParent", "BaseComponentWrapper", "VanillaFrameworkOverrides", "_combineAttributesAndGridOptions", "createGrid", "_processOnChange", "_BOOLEAN_MIXED_GRID_OPTIONS", "AgComponentContainer", "constructor", "vcr", "ɵfac", "AgComponentContainer_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "decls", "vars", "template", "AgComponentContainer_Template", "rf", "ctx", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "NUM_SHARDS", "shardIdx", "createComponentContainers", "containerMap", "Map", "i", "container", "createComponent", "set", "location", "nativeElement", "runOutsideMethods", "Set", "AngularFrameworkComponentWrapper", "setViewContainerRef", "viewContainerRef", "angularFrameworkOverrides", "createWrapper", "OriginalConstructor", "that", "compShards", "DynamicAgNg2Component", "BaseGuiComponent", "init", "params", "runInsideAngular", "_componentRef", "changeDetectorRef", "detectChanges", "has<PERSON><PERSON><PERSON>", "name", "wrapper", "getFrameworkComponentInstance", "callMethod", "componentRef", "methodCall", "has", "apply", "addMethod", "callback", "componentType", "get", "instance", "ɵAngularFrameworkComponentWrapper_BaseFactory", "AngularFrameworkComponentWrapper_Factory", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "_params", "_agAwareComponent", "_frameworkComponentInstance", "_eGui", "agInit", "getGui", "getRootElement", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "AngularFrameworkEventListenerService", "frameworkOverrides", "wrappedListeners", "wrappedGlobalListeners", "wrap", "eventType", "userListener", "listener", "shouldWrapOutgoing", "event", "wrapOutgoing", "eventListeners", "wrapGlobal", "unwrap", "wrapped", "delete", "size", "unwrapGlobal", "AngularFrameworkOverrides", "_ngZone", "batchFrameworkComps", "isRunningWithinTestZone", "wrapIncoming", "source", "runOutside", "window", "AG_GRID_UNDER_TEST", "Zone", "AsyncTestZoneSpec", "runOutsideAngular", "isInAngularZone", "createLocalEventListenerWrapper", "existingFrameworkEventListenerService", "localEventService", "setFrameworkOverrides", "undefined", "createGlobalEventListenerWrapper", "isFrameworkComponent", "comp", "prototype", "run", "AngularFrameworkOverrides_Factory", "ɵɵinject", "AgGridAngular", "elementDef", "_viewContainerRef", "_angularFrameworkOverrides", "_frameworkCompWrapper", "_initialised", "_destroyed", "_holdEvents", "_fullyReady", "Promise", "resolve", "_resolveF<PERSON><PERSON><PERSON>y", "statusBar", "sideBar", "suppressContextMenu", "preventDefaultOnContextMenu", "allowContextMenuWithControlKey", "columnMenu", "suppressMenuHide", "enableBrowserTooltips", "tooltipTrigger", "tooltipShowDelay", "tooltipHideDelay", "tooltipMouseTrack", "tooltipShowMode", "tooltipInteraction", "popupParent", "copyHeadersToClipboard", "copyGroupHeadersToClipboard", "clipboardDelimiter", "suppressCopyRowsToClipboard", "suppressCopySingleCellRanges", "suppressLastEmptyLineOnPaste", "suppressClipboardPaste", "suppressClipboardApi", "suppressCutToClipboard", "columnDefs", "defaultColDef", "defaultColGroupDef", "columnTypes", "dataTypeDefinitions", "maintainColumnOrder", "enableStrictPivotColumnOrder", "suppressFieldDotNotation", "headerHeight", "groupHeaderHeight", "floatingFiltersHeight", "pivotHeaderHeight", "pivotGroupHeaderHeight", "allowDragFromColumnsToolPanel", "suppressMovableColumns", "suppressColumnMoveAnimation", "suppressMoveWhenColumnDragging", "suppressDragLeaveHidesColumns", "suppressGroupChangesColumnVisibility", "suppressMakeColumnVisibleAfterUnGroup", "suppressRowGroupHidesColumns", "colResizeDefault", "suppressAutoSize", "autoSizePadding", "skipHeaderOnAutoSize", "autoSizeStrategy", "components", "editType", "singleClickEdit", "suppressClickEdit", "readOnlyEdit", "stopEditingWhenCellsLoseFocus", "enterNavigatesVertically", "enterNavigatesVerticallyAfterEdit", "enableCellEditingOnBackspace", "undoRedoCellEditing", "undoRedoCellEditingLimit", "defaultCsvExportParams", "suppressCsvExport", "defaultExcelExportParams", "suppressExcelExport", "excelStyles", "findSearchValue", "findOptions", "quickFilterText", "cacheQuickFilter", "includeHiddenColumnsInQuickFilter", "quickFilter<PERSON><PERSON>er", "quickFilterMatcher", "applyQuickFilterBeforePivotOrAgg", "excludeChildrenWhenTreeDataFiltering", "enableAdvancedFilter", "always<PERSON><PERSON><PERSON><PERSON>er", "includeHiddenColumnsInAdvancedFilter", "advancedFilterParent", "advancedFilterBuilderParams", "suppressAdvancedFilterEval", "suppressSetFilterByDefault", "<PERSON><PERSON><PERSON><PERSON>", "chartThemes", "customChartThemes", "chartThemeOverrides", "chartToolPanelsDef", "chartMenuItems", "loadingCell<PERSON><PERSON>er", "loadingCellRendererParams", "loadingCellRendererSelector", "localeText", "masterDetail", "keepDetailRows", "keepDetailRowsCount", "detail<PERSON><PERSON><PERSON><PERSON><PERSON>", "detailCellRendererParams", "detailRowHeight", "detailRowAutoHeight", "context", "alignedGrids", "tabIndex", "<PERSON><PERSON><PERSON><PERSON>", "valueCache", "valueCacheNeverExpires", "enableCellExpressions", "suppressTouch", "suppressFocusAfterRefresh", "suppressBrowserResizeObserver", "suppressPropertyNamesCheck", "suppressChangeDetection", "debug", "loading", "overlayLoadingTemplate", "loadingOverlayComponent", "loadingOverlayComponentParams", "suppressLoadingOverlay", "overlayNoRowsTemplate", "noRowsOverlayComponent", "noRowsOverlayComponentParams", "suppressNoRowsOverlay", "pagination", "paginationPageSize", "paginationPageSizeSelector", "paginationAutoPageSize", "paginateChildRows", "suppressPaginationPanel", "pivotMode", "pivotPanelShow", "pivotMaxGeneratedColumns", "pivotDefaultExpanded", "pivotColumnGroupTotals", "pivotRowTotals", "pivotSuppressAutoColumn", "suppressExpandablePivotGroups", "functionsReadOnly", "aggFuncs", "suppressAggFuncInHeader", "alwaysAggregateAtRootLevel", "aggregateOnlyChangedColumns", "suppressAggFilteredOnly", "removePivotHeaderRowWhenSingleValueColumn", "animateRows", "cellFlashDuration", "cellFadeDuration", "allowShowChangeAfterFilter", "domLayout", "ensureDomOrder", "enableCellSpan", "enableRtl", "suppressColumnVirtualisation", "suppressMaxRenderedRowRestriction", "suppressRowVirtualisation", "rowDragManaged", "suppressRowDrag", "suppressMoveWhenRowDragging", "rowDragEntireRow", "rowDragMultiRow", "rowDragText", "dragAndDropImageComponent", "dragAndDropImageComponentParams", "fullWidth<PERSON>ell<PERSON><PERSON><PERSON>", "fullWidthCellRendererParams", "embedFullWidthRows", "groupDisplayType", "groupDefaultExpanded", "autoGroupColumnDef", "groupMaintainOrder", "groupSelectsChildren", "groupLockGroupColumns", "groupAggFiltering", "groupTotalRow", "grandTotalRow", "suppressStickyTotalRow", "groupSuppressBlankHeader", "groupSelectsFiltered", "showOpenedGroup", "groupHideParentOfSingleChild", "groupRemoveSingleChildren", "groupRemoveLowestSingleChildren", "groupHideOpenParents", "groupAllowUnbalanced", "rowGroupPanelShow", "groupRowRenderer", "groupRowRendererParams", "treeData", "treeDataChildrenField", "treeDataParentIdField", "rowGroupPanelSuppressSort", "suppressGroupRowsSticky", "pinnedTopRowData", "pinnedBottomRowData", "enableRowPinning", "isRowPinnable", "isRowPinned", "rowModelType", "rowData", "asyncTransaction<PERSON>ait<PERSON><PERSON><PERSON>", "suppressModelUpdateAfterUpdateTransaction", "datasource", "cacheOverflowSize", "infiniteInitialRowCount", "serverSideInitialRowCount", "suppressServerSideFullWidthLoadingRow", "cacheBlockSize", "maxBlocksInCache", "maxConcurrentDatasourceRequests", "blockLoadDebounceMillis", "purgeClosedRowNodes", "serverSideDatasource", "serverSideSortAllLevels", "serverSideEnableClientSideSort", "serverSideOnlyRefreshFilteredGroups", "serverSidePivotResultFieldSeparator", "viewportDatasource", "viewportRowModelPageSize", "viewportRowModelBufferSize", "alwaysShowHorizontalScroll", "alwaysShowVerticalScroll", "debounceVerticalScrollbar", "suppressHorizontalScroll", "suppressScrollOnNewData", "suppressScrollWhenPopupsAreOpen", "suppressAnimationFrame", "suppressMiddleClickScrolls", "suppressPreventDefaultOnMouseWheel", "scrollbarWidth", "rowSelection", "cellSelection", "rowMultiSelectWithClick", "suppressRowDeselection", "suppressRowClickSelection", "suppressCellFocus", "suppressHeaderFocus", "selectionColumnDef", "rowNumbers", "suppressMultiRangeSelection", "enableCellTextSelection", "enableRangeSelection", "enableRangeHandle", "enableFillHandle", "fillHandleDirection", "suppressClearOnFillReduction", "sortingOrder", "accentedSort", "unSortIcon", "suppressMultiSort", "alwaysMultiSort", "multiSortKey", "suppressMaintainUnsortedOrder", "icons", "rowHeight", "rowStyle", "rowClass", "rowClassRules", "suppressRowHoverHighlight", "suppressRowTransform", "columnHoverHighlight", "gridId", "deltaSort", "treeDataDisplayType", "enableGroupEdit", "initialState", "theme", "loadThemeGoogleFonts", "themeCssLayer", "styleNonce", "themeStyleContainer", "getContextMenuItems", "getMainMenuItems", "postProcessPopup", "processUnpinnedColumns", "processCellForClipboard", "processHeaderForClipboard", "processGroupHeaderForClipboard", "processCellFromClipboard", "sendToClipboard", "processDataFromClipboard", "isExternalFilterPresent", "doesExternalFilterPass", "getChartToolbarItems", "createChartContainer", "focusGridInnerElement", "navigateToNextHeader", "tabToNextHeader", "navigateToNextCell", "tabToNextCell", "getLocaleText", "getDocument", "pagination<PERSON><PERSON>ber<PERSON><PERSON><PERSON><PERSON>", "getGroupRowAgg", "isGroupOpenByDefault", "initialGroupOrderComparator", "processPivotResultColDef", "processPivotResultColGroupDef", "get<PERSON>ata<PERSON><PERSON>", "get<PERSON><PERSON>d<PERSON>ount", "getServerSideGroupLevelParams", "isServerSideGroupOpenByDefault", "isApplyServerSideTransaction", "isServerSideGroup", "getServerSideGroupKey", "getBusinessKeyForNode", "getRowId", "resetRowDataOnUpdate", "processRowPostCreate", "isRowSelectable", "isRowMaster", "fillOperation", "postSortRows", "getRowStyle", "getRowClass", "getRowHeight", "isFullWidthRow", "toolPanelVisibleChanged", "toolPanelSizeChanged", "columnMenuVisibleChanged", "contextMenuVisibleChanged", "cutStart", "cutEnd", "pasteStart", "pasteEnd", "columnVisible", "columnPinned", "columnResized", "columnMoved", "columnValueChanged", "columnPivotModeChanged", "columnPivotChanged", "columnGroupOpened", "newColumnsLoaded", "gridColumnsChanged", "displayedColumnsChanged", "virtualColumnsChanged", "columnEverythingChanged", "columnHeaderMouseOver", "columnHeaderMouseLeave", "columnHeaderClicked", "columnHeaderContextMenu", "componentStateChanged", "cellValueChanged", "cellEditRequest", "rowV<PERSON>ueChanged", "cellEditingStarted", "cellEditingStopped", "rowEditingStarted", "rowEditingStopped", "undoStarted", "undoEnded", "redoStarted", "redoEnded", "cellSelectionDeleteStart", "cellSelectionDeleteEnd", "rangeDeleteStart", "rangeDeleteEnd", "fillStart", "fillEnd", "filterOpened", "filterChanged", "filterModified", "advancedFilterBuilderVisibleChanged", "findChanged", "chartCreated", "chartRangeSelectionChanged", "chartOptionsChanged", "chartDestroyed", "cellKeyDown", "gridReady", "first<PERSON><PERSON><PERSON><PERSON>ed", "gridSizeChanged", "modelUpdated", "virtualRowRemoved", "viewportChanged", "bodyScroll", "bodyScrollEnd", "dragStarted", "dragStopped", "dragCancelled", "stateUpdated", "paginationChanged", "rowDragEnter", "rowDragMove", "rowDragLeave", "rowDragEnd", "rowDragCancel", "rowResizeStarted", "rowResizeEnded", "columnRowGroupChanged", "rowGroupOpened", "expandOrCollapseAll", "pivotMaxColumnsExceeded", "pinnedRowDataChanged", "pinnedRowsChanged", "rowDataUpdated", "asyncTransactionsFlushed", "storeRefreshed", "headerFocused", "cellClicked", "cellDoubleClicked", "cellFocused", "cellMouseOver", "cellMouseOut", "cellMouseDown", "rowClicked", "rowDoubleClicked", "rowSelected", "selectionChanged", "cellContextMenu", "rangeSelectionChanged", "cellSelectionChanged", "tooltipShow", "tooltipHide", "sortChanged", "_nativeElement", "then", "ngAfterViewInit", "gridOptionKeys", "Object", "keys", "filter", "key", "startsWith", "coercedGridOptions", "for<PERSON>ach", "valueToUse", "getValueOrCoercedValue", "mergedGridOps", "gridOptions", "gridParams", "globalListener", "bind", "providedBeanInstances", "frameworkCompWrapper", "modules", "setThemeOnGridDiv", "api", "ngOnChanges", "changes", "value", "currentValue", "ngOnDestroy", "isEmitterUsed", "emitter", "emitterAny", "hasEmitter", "observed", "observers", "length", "asEventName", "char<PERSON>t", "toUpperCase", "substring", "hasGridOptionListener", "fireEmitter", "emit", "AgGridAngular_Factory", "ɵɵdirectiveInject", "ElementRef", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "outputs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "AgGridAngular_Template", "providers", "None", "transform", "booleanMixedGridOptions", "AgGridModule", "AgGridModule_Factory", "ɵmod", "ɵɵdefineNgModule", "imports", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/ag-grid-angular/fesm2022/ag-grid-angular.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, ViewContainerRef, Component, Injectable, NgZone, EventEmitter, booleanAttribute, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { _removeFromParent, BaseComponentWrapper, VanillaFrameworkOverrides, _combineAttributesAndGridOptions, createGrid, _processOnChange, _BOOLEAN_MIXED_GRID_OPTIONS } from 'ag-grid-community';\n\n// To speed up the removal of custom components we create a number of shards to contain them.\n// Removing a single component calls a function within Angular called removeFromArray.\n// This is a lot faster if the array is smaller.\nclass AgComponentContainer {\n    constructor() {\n        this.vcr = inject(ViewContainerRef);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AgComponentContainer, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.12\", type: AgComponentContainer, selector: \"ag-component-container\", ngImport: i0, template: '', isInline: true }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AgComponentContainer, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ag-component-container',\n                    template: '',\n                }]\n        }] });\nconst NUM_SHARDS = 16;\nlet shardIdx = 0;\nfunction createComponentContainers(vcr) {\n    const containerMap = new Map();\n    for (let i = 0; i < NUM_SHARDS; i++) {\n        const container = vcr.createComponent(AgComponentContainer);\n        containerMap.set(i, container);\n        _removeFromParent(container.location.nativeElement);\n    }\n    return containerMap;\n}\n/**\n * These methods are called on a hot path for every row so we do not want to enter / exit NgZone each time.\n * Also these methods should not be used to update the UI, so we don't need to run them inside Angular.\n */\nconst runOutsideMethods = new Set(['doesFilterPass', 'isFilterActive']);\nclass AngularFrameworkComponentWrapper extends BaseComponentWrapper {\n    setViewContainerRef(viewContainerRef, angularFrameworkOverrides) {\n        this.viewContainerRef = viewContainerRef;\n        this.angularFrameworkOverrides = angularFrameworkOverrides;\n    }\n    createWrapper(OriginalConstructor) {\n        const angularFrameworkOverrides = this.angularFrameworkOverrides;\n        const that = this;\n        that.compShards ??= createComponentContainers(this.viewContainerRef);\n        class DynamicAgNg2Component extends BaseGuiComponent {\n            init(params) {\n                angularFrameworkOverrides.runInsideAngular(() => {\n                    super.init(params);\n                    this._componentRef.changeDetectorRef.detectChanges();\n                });\n            }\n            createComponent() {\n                return that.createComponent(OriginalConstructor);\n            }\n            hasMethod(name) {\n                return wrapper.getFrameworkComponentInstance()[name] != null;\n            }\n            callMethod(name, args) {\n                const componentRef = this.getFrameworkComponentInstance();\n                const methodCall = componentRef[name];\n                if (runOutsideMethods.has(name)) {\n                    return methodCall.apply(componentRef, args);\n                }\n                return angularFrameworkOverrides.runInsideAngular(() => methodCall.apply(componentRef, args));\n            }\n            addMethod(name, callback) {\n                wrapper[name] = callback;\n            }\n        }\n        const wrapper = new DynamicAgNg2Component();\n        return wrapper;\n    }\n    createComponent(componentType) {\n        shardIdx = (shardIdx + 1) % NUM_SHARDS;\n        const container = this.compShards.get(shardIdx);\n        return container.instance.vcr.createComponent(componentType);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AngularFrameworkComponentWrapper, deps: null, target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AngularFrameworkComponentWrapper }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AngularFrameworkComponentWrapper, decorators: [{\n            type: Injectable\n        }] });\nclass BaseGuiComponent {\n    init(params) {\n        this._params = params;\n        this._componentRef = this.createComponent();\n        this._agAwareComponent = this._componentRef.instance;\n        this._frameworkComponentInstance = this._componentRef.instance;\n        this._eGui = this._componentRef.location.nativeElement;\n        // Angular appends the component to the DOM, so remove it\n        _removeFromParent(this._eGui);\n        this._agAwareComponent.agInit(this._params);\n    }\n    getGui() {\n        return this._eGui;\n    }\n    /** `getGui()` returns the `ng-component` element. This returns the actual root element. */\n    getRootElement() {\n        const firstChild = this._eGui.firstChild;\n        return firstChild;\n    }\n    destroy() {\n        if (this._frameworkComponentInstance && typeof this._frameworkComponentInstance.destroy === 'function') {\n            this._frameworkComponentInstance.destroy();\n        }\n        this._componentRef?.destroy();\n    }\n    getFrameworkComponentInstance() {\n        return this._frameworkComponentInstance;\n    }\n}\n\nclass AngularFrameworkEventListenerService {\n    constructor(frameworkOverrides) {\n        this.frameworkOverrides = frameworkOverrides;\n        // Map from user listener to wrapped listener so we can remove listener provided by user\n        this.wrappedListeners = new Map();\n        this.wrappedGlobalListeners = new Map();\n    }\n    wrap(eventType, userListener) {\n        const { frameworkOverrides, wrappedListeners } = this;\n        let listener = userListener;\n        if (frameworkOverrides.shouldWrapOutgoing) {\n            listener = (event) => {\n                frameworkOverrides.wrapOutgoing(() => userListener(event));\n            };\n            let eventListeners = wrappedListeners.get(eventType);\n            if (!eventListeners) {\n                eventListeners = new Map();\n                wrappedListeners.set(eventType, eventListeners);\n            }\n            eventListeners.set(userListener, listener);\n        }\n        return listener;\n    }\n    wrapGlobal(userListener) {\n        const { frameworkOverrides, wrappedGlobalListeners } = this;\n        let listener = userListener;\n        if (frameworkOverrides.shouldWrapOutgoing) {\n            listener = (eventType, event) => {\n                frameworkOverrides.wrapOutgoing(() => userListener(eventType, event));\n            };\n            wrappedGlobalListeners.set(userListener, listener);\n        }\n        return listener;\n    }\n    unwrap(eventType, userListener) {\n        const { wrappedListeners } = this;\n        const eventListeners = wrappedListeners.get(eventType);\n        if (eventListeners) {\n            const wrapped = eventListeners.get(userListener);\n            if (wrapped) {\n                eventListeners.delete(userListener);\n                if (eventListeners.size === 0) {\n                    wrappedListeners.delete(eventType);\n                }\n                return wrapped;\n            }\n        }\n        return userListener;\n    }\n    unwrapGlobal(userListener) {\n        const { wrappedGlobalListeners } = this;\n        const wrapped = wrappedGlobalListeners.get(userListener);\n        if (wrapped) {\n            wrappedGlobalListeners.delete(userListener);\n            return wrapped;\n        }\n        return userListener;\n    }\n}\n\nclass AngularFrameworkOverrides extends VanillaFrameworkOverrides {\n    constructor(_ngZone) {\n        super('angular');\n        this._ngZone = _ngZone;\n        this.batchFrameworkComps = true;\n        // Flag used to control Zone behaviour when running tests as many test features rely on Zone.\n        this.isRunningWithinTestZone = false;\n        // Make all events run outside Angular as they often trigger the setup of event listeners\n        // By having the event listeners outside Angular we can avoid triggering change detection\n        // This also means that if a user calls an AG Grid API method from within their component\n        // the internal side effects will not trigger change detection. Without this the events would\n        // run inside Angular and trigger change detection as the source of the event was within the angular zone.\n        this.wrapIncoming = (callback, source) => this.runOutside(callback, source);\n        /**\n         * Make sure that any code that is executed outside of AG Grid is running within the Angular zone.\n         * This means users can update templates and use binding without having to do anything extra.\n         */\n        this.wrapOutgoing = (callback) => this.runInsideAngular(callback);\n        this.isRunningWithinTestZone =\n            window?.AG_GRID_UNDER_TEST ?? !!window?.Zone?.AsyncTestZoneSpec;\n        if (!this._ngZone) {\n            this.runOutside = (callback) => callback();\n        }\n        else if (this.isRunningWithinTestZone) {\n            this.runOutside = (callback, source) => {\n                if (source === 'resize-observer' || source === 'popupPositioning') {\n                    // ensure resize observer callbacks are run outside of Angular even under test due to Jest not supporting ResizeObserver\n                    // which means it just loops continuously with a setTimeout with no way to flush the queue or have fixture.whenStable() resolve.\n                    return this._ngZone.runOutsideAngular(callback);\n                }\n                // When under test run inside Angular so that tests can use fixture.whenStable() to wait for async operations to complete.\n                return callback();\n            };\n        }\n        else {\n            this.runOutside = (callback) => this._ngZone.runOutsideAngular(callback);\n        }\n    }\n    /**\n     * The shouldWrapOutgoing property is used to determine if events should be run outside of Angular or not.\n     * If an event handler is registered outside of Angular then we should not wrap the event handler\n     * with runInsideAngular() as the user may not have wanted this.\n     * This is also used to not wrap internal event listeners that are registered with RowNodes and Columns.\n     */\n    get shouldWrapOutgoing() {\n        return this._ngZone && NgZone.isInAngularZone();\n    }\n    createLocalEventListenerWrapper(existingFrameworkEventListenerService, localEventService) {\n        if (this.shouldWrapOutgoing) {\n            return (existingFrameworkEventListenerService ??\n                (() => {\n                    localEventService.setFrameworkOverrides(this);\n                    return new AngularFrameworkEventListenerService(this);\n                })());\n        }\n        return undefined;\n    }\n    createGlobalEventListenerWrapper() {\n        return new AngularFrameworkEventListenerService(this);\n    }\n    isFrameworkComponent(comp) {\n        if (!comp) {\n            return false;\n        }\n        const prototype = comp.prototype;\n        return prototype && 'agInit' in prototype;\n    }\n    runInsideAngular(callback) {\n        if (!this._ngZone || NgZone.isInAngularZone()) {\n            return callback();\n        }\n        // Check for _ngZone existence as it is not present when Zoneless\n        return this._ngZone.run(callback);\n    }\n    runOutsideAngular(callback, source) {\n        return this.runOutside(callback, source);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AngularFrameworkOverrides, deps: [{ token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AngularFrameworkOverrides }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AngularFrameworkOverrides, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i0.NgZone }] });\n\n// False positive lint error, ElementRef and co can't be type imports\n// eslint-disable-next-line @typescript-eslint/consistent-type-imports\nclass AgGridAngular {\n    constructor(elementDef, _viewContainerRef, _angularFrameworkOverrides, _frameworkCompWrapper) {\n        this._viewContainerRef = _viewContainerRef;\n        this._angularFrameworkOverrides = _angularFrameworkOverrides;\n        this._frameworkCompWrapper = _frameworkCompWrapper;\n        this._initialised = false;\n        this._destroyed = false;\n        // in order to ensure firing of gridReady is deterministic\n        this._holdEvents = true;\n        this._fullyReady = new Promise((resolve) => {\n            this._resolveFullyReady = resolve;\n        });\n        // @START@\n        /** Specifies the status bar components to use in the status bar.\n         * @agModule `StatusBarModule`\n         */\n        this.statusBar = undefined;\n        /** Specifies the side bar components.\n         * @agModule `SideBarModule`\n         */\n        this.sideBar = undefined;\n        /** Set to `true` to not show the context menu. Use if you don't want to use the default 'right click' context menu.\n         * @default false\n         */\n        this.suppressContextMenu = undefined;\n        /** When using `suppressContextMenu`, you can use the `onCellContextMenu` function to provide your own code to handle cell `contextmenu` events.\n         * This flag is useful to prevent the browser from showing its default context menu.\n         * @default false\n         */\n        this.preventDefaultOnContextMenu = undefined;\n        /** Allows context menu to show, even when `Ctrl` key is held down.\n         * @default false\n         * @agModule `ContextMenuModule`\n         */\n        this.allowContextMenuWithControlKey = undefined;\n        /** Changes the display type of the column menu.\n         * `'new'` just displays the main list of menu items. `'legacy'` displays a tabbed menu.\n         * @default 'new'\n         * @initial\n         */\n        this.columnMenu = undefined;\n        /** Only recommended for use if `columnMenu = 'legacy'`.\n         * When `true`, the column menu button will always be shown.\n         * When `false`, the column menu button will only show when the mouse is over the column header.\n         * When using `columnMenu = 'legacy'`, this will default to `false` instead of `true`.\n         * @default true\n         */\n        this.suppressMenuHide = undefined;\n        /** Set to `true` to use the browser's default tooltip instead of using the grid's Tooltip Component.\n         * @default false\n         * @initial\n         * @agModule `TooltipModule`\n         */\n        this.enableBrowserTooltips = undefined;\n        /** The trigger that will cause tooltips to show and hide.\n         *  - `hover` - The tooltip will show/hide when a cell/header is hovered.\n         *  - `focus` - The tooltip will show/hide when a cell/header is focused.\n         * @default 'hover'\n         * @initial\n         * @agModule `TooltipModule`\n         */\n        this.tooltipTrigger = undefined;\n        /** The delay in milliseconds that it takes for tooltips to show up once an element is hovered over.\n         *     **Note:** This property does not work if `enableBrowserTooltips` is `true`.\n         * @default 2000\n         * @agModule `TooltipModule`\n         */\n        this.tooltipShowDelay = undefined;\n        /** The delay in milliseconds that it takes for tooltips to hide once they have been displayed.\n         *     **Note:** This property does not work if `enableBrowserTooltips` is `true` and `tooltipHideTriggers` includes `timeout`.\n         * @default 10000\n         * @agModule `TooltipModule`\n         */\n        this.tooltipHideDelay = undefined;\n        /** Set to `true` to have tooltips follow the cursor once they are displayed.\n         * @default false\n         * @initial\n         * @agModule `TooltipModule`\n         */\n        this.tooltipMouseTrack = undefined;\n        /** This defines when tooltip will show up for Cells, Headers and SetFilter Items.\n         *  - `standard` - The tooltip always shows up when the items configured with Tooltips are hovered.\n         * - `whenTruncated` - The tooltip will only be displayed when the items hovered have truncated (showing ellipsis) values. This property does not work when `enableBrowserTooltips={true}`.\n         * @default `standard`\n         * @agModule `TooltipModule`\n         */\n        this.tooltipShowMode = undefined;\n        /** Set to `true` to enable tooltip interaction. When this option is enabled, the tooltip will not hide while the\n         * tooltip itself it being hovered or has focus.\n         * @default false\n         * @initial\n         * @agModule `TooltipModule`\n         */\n        this.tooltipInteraction = undefined;\n        /** DOM element to use as the popup parent for grid popups (context menu, column menu etc).\n         */\n        this.popupParent = undefined;\n        /** Set to `true` to also include headers when copying to clipboard using `Ctrl + C` clipboard.\n         * @default false\n         * @agModule `ClipboardModule`\n         */\n        this.copyHeadersToClipboard = undefined;\n        /** Set to `true` to also include group headers when copying to clipboard using `Ctrl + C` clipboard.\n         * @default false\n         * @agModule `ClipboardModule`\n         */\n        this.copyGroupHeadersToClipboard = undefined;\n        /** Specify the delimiter to use when copying to clipboard.\n         * @default '\\t'\n         * @agModule `ClipboardModule`\n         */\n        this.clipboardDelimiter = undefined;\n        /** Set to `true` to copy the cell range or focused cell to the clipboard and never the selected rows.\n         * @default false\n         * @deprecated v32.2 Use `rowSelection.copySelectedRows` instead.\n         */\n        this.suppressCopyRowsToClipboard = undefined;\n        /** Set to `true` to copy rows instead of ranges when a range with only a single cell is selected.\n         * @default false\n         * @deprecated v32.2 Use `rowSelection.copySelectedRows` instead.\n         */\n        this.suppressCopySingleCellRanges = undefined;\n        /** Set to `true` to work around a bug with Excel (Windows) that adds an extra empty line at the end of ranges copied to the clipboard.\n         * @default false\n         * @agModule `ClipboardModule`\n         */\n        this.suppressLastEmptyLineOnPaste = undefined;\n        /** Set to `true` to turn off paste operations within the grid.\n         * @default false\n         * @agModule `ClipboardModule`\n         */\n        this.suppressClipboardPaste = undefined;\n        /** Set to `true` to stop the grid trying to use the Clipboard API, if it is blocked, and immediately fallback to the workaround.\n         * @default false\n         * @agModule `ClipboardModule`\n         */\n        this.suppressClipboardApi = undefined;\n        /** Set to `true` to block     **cut** operations within the grid.\n         * @default false\n         * @agModule `ClipboardModule`\n         */\n        this.suppressCutToClipboard = undefined;\n        /** Array of Column / Column Group definitions.\n         */\n        this.columnDefs = undefined;\n        /** A default column definition. Items defined in the actual column definitions get precedence.\n         */\n        this.defaultColDef = undefined;\n        /** A default column group definition. All column group definitions will use these properties. Items defined in the actual column group definition get precedence.\n         * @initial\n         */\n        this.defaultColGroupDef = undefined;\n        /** An object map of custom column types which contain groups of properties that column definitions can reuse by referencing in their `type` property.\n         */\n        this.columnTypes = undefined;\n        /** An object map of cell data types to their definitions.\n         * Cell data types can either override/update the pre-defined data types\n         * (`'text'`, `'number'`,  `'boolean'`,  `'date'`,  `'dateString'` or  `'object'`),\n         * or can be custom data types.\n         */\n        this.dataTypeDefinitions = undefined;\n        /** Keeps the order of Columns maintained after new Column Definitions are updated.\n         *\n         * @default false\n         */\n        this.maintainColumnOrder = undefined;\n        /** Resets pivot column order when impacted by filters, data or configuration changes\n         *\n         * @default false\n         * @agModule `PivotModule`\n         */\n        this.enableStrictPivotColumnOrder = undefined;\n        /** If `true`, then dots in field names (e.g. `'address.firstLine'`) are not treated as deep references. Allows you to use dots in your field name if you prefer.\n         * @default false\n         */\n        this.suppressFieldDotNotation = undefined;\n        /** The height in pixels for the row containing the column label header. If not specified, it uses the theme value of `header-height`.\n         */\n        this.headerHeight = undefined;\n        /** The height in pixels for the rows containing header column groups. If not specified, it uses `headerHeight`.\n         */\n        this.groupHeaderHeight = undefined;\n        /** The height in pixels for the row containing the floating filters. If not specified, it uses the theme value of `header-height`.\n         */\n        this.floatingFiltersHeight = undefined;\n        /** The height in pixels for the row containing the columns when in pivot mode. If not specified, it uses `headerHeight`.\n         */\n        this.pivotHeaderHeight = undefined;\n        /** The height in pixels for the row containing header column groups when in pivot mode. If not specified, it uses `groupHeaderHeight`.\n         */\n        this.pivotGroupHeaderHeight = undefined;\n        /** Allow reordering and pinning columns by dragging columns from the Columns Tool Panel to the grid.\n         * @default false\n         * @agModule `ColumnsToolPanelModule`\n         */\n        this.allowDragFromColumnsToolPanel = undefined;\n        /** Set to `true` to suppress column moving, i.e. to make the columns fixed position.\n         * @default false\n         */\n        this.suppressMovableColumns = undefined;\n        /** If `true`, the `ag-column-moving` class is not added to the grid while columns are moving. In the default themes, this results in no animation when moving columns.\n         * @default false\n         */\n        this.suppressColumnMoveAnimation = undefined;\n        /** Set to `true` to suppress moving columns while dragging the Column Header. This option highlights the position where the column will be placed and it will only move it on mouse up.\n         * @default false\n         */\n        this.suppressMoveWhenColumnDragging = undefined;\n        /** If `true`, when you drag a column out of the grid (e.g. to the group zone) the column is not hidden.\n         * @default false\n         */\n        this.suppressDragLeaveHidesColumns = undefined;\n        /** Enable to prevent column visibility changing when grouped columns are changed.\n         * @default false\n         */\n        this.suppressGroupChangesColumnVisibility = undefined;\n        /** By default, when a column is un-grouped, i.e. using the Row Group Panel, it is made visible in the grid. This property stops the column becoming visible again when un-grouping.\n         * @default false\n         * @deprecated v33.0.0 - Use `suppressGroupChangesColumnVisibility: 'suppressShowOnUngroup'` instead.\n         */\n        this.suppressMakeColumnVisibleAfterUnGroup = undefined;\n        /** If `true`, when you drag a column into a row group panel the column is not hidden.\n         * @default false\n         * @deprecated v33.0.0 - Use `suppressGroupChangesColumnVisibility: 'suppressHideOnGroup'` instead.\n         */\n        this.suppressRowGroupHidesColumns = undefined;\n        /** Set to `'shift'` to have shift-resize as the default resize operation (same as user holding down `Shift` while resizing).\n         */\n        this.colResizeDefault = undefined;\n        /** Suppresses auto-sizing columns for columns. In other words, double clicking a column's header's edge will not auto-size.\n         * @default false\n         * @initial\n         */\n        this.suppressAutoSize = undefined;\n        /** Number of pixels to add to a column width after the [auto-sizing](./column-sizing/#auto-size-columns-to-fit-cell-contents) calculation.\n         * Set this if you want to add extra room to accommodate (for example) sort icons, or some other dynamic nature of the header.\n         * @default 20\n         */\n        this.autoSizePadding = undefined;\n        /** Set this to `true` to skip the `headerName` when `autoSize` is called by default.\n         * @default false\n         * @initial\n         * @agModule `ColumnAutoSizeModule`\n         */\n        this.skipHeaderOnAutoSize = undefined;\n        /** Auto-size the columns when the grid is loaded. Can size to fit the grid width, fit a provided width, or fit the cell contents.\n         * @initial\n         * @agModule `ColumnAutoSizeModule`\n         */\n        this.autoSizeStrategy = undefined;\n        /** A map of component names to components.\n         * @initial\n         */\n        this.components = undefined;\n        /** Set to `'fullRow'` to enable Full Row Editing. Otherwise leave blank to edit one cell at a time.\n         * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n         */\n        this.editType = undefined;\n        /** Set to `true` to enable Single Click Editing for cells, to start editing with a single click.\n         * @default false\n         * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n         */\n        this.singleClickEdit = undefined;\n        /** Set to `true` so that neither single nor double click starts editing.\n         * @default false\n         * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n         */\n        this.suppressClickEdit = undefined;\n        /** Set to `true` to stop the grid updating data after `Edit`, `Clipboard` and `Fill Handle` operations. When this is set, it is intended the application will update the data, eg in an external immutable store, and then pass the new dataset to the grid. <br />**Note:** `rowNode.setDataValue()` does not update the value of the cell when this is `True`, it fires `onCellEditRequest` instead.\n         * @default false\n         * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n         */\n        this.readOnlyEdit = undefined;\n        /** Set this to `true` to stop cell editing when grid loses focus.\n         * The default is that the grid stays editing until focus goes onto another cell.\n         * @default false\n         * @initial\n         * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n         */\n        this.stopEditingWhenCellsLoseFocus = undefined;\n        /** Set to `true` along with `enterNavigatesVerticallyAfterEdit` to have Excel-style behaviour for the `Enter` key.\n         * i.e. pressing the `Enter` key will move down to the cell beneath and `Shift+Enter` will move up to the cell above.\n         * @default false\n         * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n         */\n        this.enterNavigatesVertically = undefined;\n        /** Set to `true` along with `enterNavigatesVertically` to have Excel-style behaviour for the 'Enter' key.\n         * i.e. pressing the Enter key will move down to the cell beneath and Shift+Enter key will move up to the cell above.\n         * @default false\n         * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n         */\n        this.enterNavigatesVerticallyAfterEdit = undefined;\n        /** Forces Cell Editing to start when backspace is pressed. This is only relevant for MacOS users.\n         * @agModule `TextEditorModule` / `LargeTextEditorModule` / `NumberEditorModule` / `DateEditorModule` / `CheckboxEditorModule` / `CustomEditorModule` / `SelectEditorModule` / `RichSelectModule`\n         */\n        this.enableCellEditingOnBackspace = undefined;\n        /** Set to `true` to enable Undo / Redo while editing.\n         * @initial\n         * @agModule `UndoRedoEditModule`\n         */\n        this.undoRedoCellEditing = undefined;\n        /** Set the size of the undo / redo stack.\n         * @default 10\n         * @initial\n         * @agModule `UndoRedoEditModule`\n         */\n        this.undoRedoCellEditingLimit = undefined;\n        /** A default configuration object used to export to CSV.\n         * @agModule `CsvExportModule`\n         */\n        this.defaultCsvExportParams = undefined;\n        /** Prevents the user from exporting the grid to CSV.\n         * @default false\n         */\n        this.suppressCsvExport = undefined;\n        /** A default configuration object used to export to Excel.\n         * @agModule `ExcelExportModule`\n         */\n        this.defaultExcelExportParams = undefined;\n        /** Prevents the user from exporting the grid to Excel.\n         * @default false\n         */\n        this.suppressExcelExport = undefined;\n        /** A list (array) of Excel styles to be used when exporting to Excel with styles.\n         * @initial\n         * @agModule `ExcelExportModule`\n         */\n        this.excelStyles = undefined;\n        /** Text to find within the grid.\n         * @agModule `FindModule`\n         */\n        this.findSearchValue = undefined;\n        /** Options for the Find feature.\n         * @agModule `FindModule`\n         */\n        this.findOptions = undefined;\n        /** Rows are filtered using this text as a Quick Filter.\n         * Only supported for Client-Side Row Model.\n         * @agModule `QuickFilterModule`\n         */\n        this.quickFilterText = undefined;\n        /** Set to `true` to turn on the Quick Filter cache, used to improve performance when using the Quick Filter.\n         * @default false\n         * @initial\n         * @agModule `QuickFilterModule`\n         */\n        this.cacheQuickFilter = undefined;\n        /** Hidden columns are excluded from the Quick Filter by default.\n         * To include hidden columns, set to `true`.\n         * @default false\n         * @agModule `QuickFilterModule`\n         */\n        this.includeHiddenColumnsInQuickFilter = undefined;\n        /** Changes how the Quick Filter splits the Quick Filter text into search terms.\n         * @agModule `QuickFilterModule`\n         */\n        this.quickFilterParser = undefined;\n        /** Changes the matching logic for whether a row passes the Quick Filter.\n         * @agModule `QuickFilterModule`\n         */\n        this.quickFilterMatcher = undefined;\n        /** When pivoting, Quick Filter is only applied on the pivoted data\n         * (or aggregated data if `groupAggFiltering = true`).\n         * Set to `true` to apply Quick Filter before pivoting (/aggregating) instead.\n         * @default false\n         * @agModule `QuickFilterModule`\n         */\n        this.applyQuickFilterBeforePivotOrAgg = undefined;\n        /** Set to `true` to override the default tree data filtering behaviour to instead exclude child nodes from filter results.\n         * @default false\n         * @agModule `TreeDataModule`\n         */\n        this.excludeChildrenWhenTreeDataFiltering = undefined;\n        /** Set to true to enable the Advanced Filter.\n         * @default false\n         * @agModule `AdvancedFilterModule`\n         */\n        this.enableAdvancedFilter = undefined;\n        /** Allows rows to always be displayed, even if they don't match the applied filtering.\n         * Return `true` for the provided row to always be displayed.\n         * Only works with the Client-Side Row Model.\n         * @agModule `TextFilterModule` / `NumberFilterModule` / `DateFilterModule` / `SetFilterModule` / `MultiFilterModule` / `CustomFilterModule` / `QuickFilterModule` / `ExternalFilterModule` / `AdvancedFilterModule`\n         */\n        this.alwaysPassFilter = undefined;\n        /** Hidden columns are excluded from the Advanced Filter by default.\n         * To include hidden columns, set to `true`.\n         * @default false\n         * @agModule `AdvancedFilterModule`\n         */\n        this.includeHiddenColumnsInAdvancedFilter = undefined;\n        /** DOM element to use as the parent for the Advanced Filter to allow it to appear outside of the grid.\n         * Set to `null` or `undefined` to appear inside the grid.\n         * @agModule `AdvancedFilterModule`\n         */\n        this.advancedFilterParent = undefined;\n        /** Customise the parameters passed to the Advanced Filter Builder.\n         * @agModule `AdvancedFilterModule`\n         */\n        this.advancedFilterBuilderParams = undefined;\n        /** By default, Advanced Filter sanitises user input and passes it to `new Function()` to provide the best performance.\n         * Set to `true` to prevent this and use defined functions instead.\n         * This will result in slower filtering, but it enables Advanced Filter to work when `unsafe-eval` is disabled.\n         * @default false\n         * @agModule `AdvancedFilterModule`\n         */\n        this.suppressAdvancedFilterEval = undefined;\n        /** When using AG Grid Enterprise, the Set Filter is used by default when `filter: true` is set on column definitions.\n         * Set to `true` to prevent this and instead use the Text Filter, Number Filter or Date Filter based on the cell data type,\n         * the same as when using AG Grid Community.\n         * @default false\n         * @initial\n         * @agModule TextFilterModule / NumberFilterModule / DateFilterModule / MultiFilterModule / CustomFilterModule\n         */\n        this.suppressSetFilterByDefault = undefined;\n        /** Set to `true` to Enable Charts.\n         * @default false\n         * @agModule `IntegratedChartsModule`\n         */\n        this.enableCharts = undefined;\n        /** The list of chart themes that a user can choose from in the chart panel.\n         * @default ['ag-default', 'ag-material', 'ag-sheets', 'ag-polychroma', 'ag-vivid'];\n         * @initial\n         * @agModule `IntegratedChartsModule`\n         */\n        this.chartThemes = undefined;\n        /** A map containing custom chart themes.\n         * @initial\n         * @agModule `IntegratedChartsModule`\n         */\n        this.customChartThemes = undefined;\n        /** Chart theme overrides applied to all themes.\n         * @initial\n         * @agModule `IntegratedChartsModule`\n         */\n        this.chartThemeOverrides = undefined;\n        /** Allows customisation of the Chart Tool Panels, such as changing the tool panels visibility and order, as well as choosing which charts should be displayed in the chart panel.\n         * @initial\n         * @agModule `IntegratedChartsModule`\n         */\n        this.chartToolPanelsDef = undefined;\n        /** Get chart menu items. Only applies when using AG Charts Enterprise.\n         * @agModule `IntegratedChartsModule`\n         */\n        this.chartMenuItems = undefined;\n        /** Provide your own loading cell renderer to use when data is loading via a DataSource.\n         * See [Loading Cell Renderer](https://www.ag-grid.com/javascript-data-grid/component-loading-cell-renderer/) for framework specific implementation details.\n         */\n        this.loadingCellRenderer = undefined;\n        /** Params to be passed to the `loadingCellRenderer` component.\n         */\n        this.loadingCellRendererParams = undefined;\n        /** Callback to select which loading cell renderer to be used when data is loading via a DataSource.\n         * @initial\n         */\n        this.loadingCellRendererSelector = undefined;\n        /** A map of key->value pairs for localising text within the grid.\n         * @initial\n         * @agModule `LocaleModule`\n         */\n        this.localeText = undefined;\n        /** Set to `true` to enable Master Detail.\n         * @default false\n         * @agModule `MasterDetailModule`\n         */\n        this.masterDetail = undefined;\n        /** Set to `true` to keep detail rows for when they are displayed again.\n         * @default false\n         * @initial\n         * @agModule `MasterDetailModule`\n         */\n        this.keepDetailRows = undefined;\n        /** Sets the number of details rows to keep.\n         * @default 10\n         * @initial\n         * @agModule `MasterDetailModule`\n         */\n        this.keepDetailRowsCount = undefined;\n        /** Provide a custom `detailCellRenderer` to use when a master row is expanded.\n         * See [Detail Cell Renderer](https://www.ag-grid.com/javascript-data-grid/master-detail-custom-detail/) for framework specific implementation details.\n         * @agModule `MasterDetailModule`\n         */\n        this.detailCellRenderer = undefined;\n        /** Specifies the params to be used by the Detail Cell Renderer. Can also be a function that provides the params to enable dynamic definitions of the params.\n         * @agModule `MasterDetailModule`\n         */\n        this.detailCellRendererParams = undefined;\n        /** Set fixed height in pixels for each detail row.\n         * @initial\n         * @agModule `MasterDetailModule`\n         */\n        this.detailRowHeight = undefined;\n        /** Set to `true` to have the detail grid dynamically change it's height to fit it's rows.\n         * @initial\n         * @agModule `MasterDetailModule`\n         */\n        this.detailRowAutoHeight = undefined;\n        /** Provides a context object that is provided to different callbacks the grid uses. Used for passing additional information to the callbacks used by your application.\n         * @initial\n         */\n        this.context = undefined;\n        /**\n         * A list of grids to treat as Aligned Grids.\n         * Provide a list if the grids / apis already exist or return via a callback to allow the aligned grids to be retrieved asynchronously.\n         * If grids are aligned then the columns and horizontal scrolling will be kept in sync.\n         * @agModule `AlignedGridsModule`\n         */\n        this.alignedGrids = undefined;\n        /** Change this value to set the tabIndex order of the Grid within your application.\n         * @default 0\n         * @initial\n         */\n        this.tabIndex = undefined;\n        /** The number of rows rendered outside the viewable area the grid renders.\n         * Having a buffer means the grid will have rows ready to show as the user slowly scrolls vertically.\n         * @default 10\n         */\n        this.rowBuffer = undefined;\n        /** Set to `true` to turn on the value cache.\n         * @default false\n         * @initial\n         * @agModule `ValueCacheModule`\n         */\n        this.valueCache = undefined;\n        /** Set to `true` to configure the value cache to not expire after data updates.\n         * @default false\n         * @initial\n         * @agModule `ValueCacheModule`\n         */\n        this.valueCacheNeverExpires = undefined;\n        /** Set to `true` to allow cell expressions.\n         * @default false\n         * @initial\n         */\n        this.enableCellExpressions = undefined;\n        /** Disables touch support (but does not remove the browser's efforts to simulate mouse events on touch).\n         * @default false\n         * @initial\n         */\n        this.suppressTouch = undefined;\n        /** Set to `true` to not set focus back on the grid after a refresh. This can avoid issues where you want to keep the focus on another part of the browser.\n         * @default false\n         */\n        this.suppressFocusAfterRefresh = undefined;\n        /** @deprecated As of v32.2 the grid always uses the browser's ResizeObserver, this grid option has no effect\n         * @default false\n         * @initial\n         */\n        this.suppressBrowserResizeObserver = undefined;\n        /** @deprecated As of v33 `gridOptions` and `columnDefs` both have a `context` property that should be used for arbitrary user data. This means that column definitions and gridOptions should only contain valid properties making this property redundant.\n         * @default false\n         * @initial\n         */\n        this.suppressPropertyNamesCheck = undefined;\n        /** Disables change detection.\n         * @default false\n         */\n        this.suppressChangeDetection = undefined;\n        /** Set this to `true` to enable debug information from the grid and related components. Will result in additional logging being output, but very useful when investigating problems.\n         * It is also recommended to register the `ValidationModule` to identify any misconfigurations.\n         * @default false\n         * @initial\n         */\n        this.debug = undefined;\n        /** Show or hide the loading overlay.\n         */\n        this.loading = undefined;\n        /** Provide a HTML string to override the default loading overlay. Supports non-empty plain text or HTML with a single root element.\n         */\n        this.overlayLoadingTemplate = undefined;\n        /** Provide a custom loading overlay component.\n         * @initial\n         */\n        this.loadingOverlayComponent = undefined;\n        /** Customise the parameters provided to the loading overlay component.\n         */\n        this.loadingOverlayComponentParams = undefined;\n        /** Disables the 'loading' overlay.\n         * @deprecated v32 - Deprecated. Use `loading=false` instead.\n         * @default false\n         * @initial\n         */\n        this.suppressLoadingOverlay = undefined;\n        /** Provide a HTML string to override the default no-rows overlay. Supports non-empty plain text or HTML with a single root element.\n         */\n        this.overlayNoRowsTemplate = undefined;\n        /** Provide a custom no-rows overlay component.\n         * @initial\n         */\n        this.noRowsOverlayComponent = undefined;\n        /** Customise the parameters provided to the no-rows overlay component.\n         */\n        this.noRowsOverlayComponentParams = undefined;\n        /** Set to `true` to prevent the no-rows overlay being shown when there is no row data.\n         * @default false\n         * @initial\n         */\n        this.suppressNoRowsOverlay = undefined;\n        /** Set whether pagination is enabled.\n         * @default false\n         * @agModule `PaginationModule`\n         */\n        this.pagination = undefined;\n        /** How many rows to load per page. If `paginationAutoPageSize` is specified, this property is ignored.\n         * @default 100\n         * @agModule `PaginationModule`\n         */\n        this.paginationPageSize = undefined;\n        /** Determines if the page size selector is shown in the pagination panel or not.\n         * Set to an array of values to show the page size selector with custom list of possible page sizes.\n         * Set to `true` to show the page size selector with the default page sizes `[20, 50, 100]`.\n         * Set to `false` to hide the page size selector.\n         * @default true\n         * @initial\n         * @agModule `PaginationModule`\n         */\n        this.paginationPageSizeSelector = undefined;\n        /** Set to `true` so that the number of rows to load per page is automatically adjusted by the grid so each page shows enough rows to just fill the area designated for the grid. If `false`, `paginationPageSize` is used.\n         * @default false\n         * @agModule `PaginationModule`\n         */\n        this.paginationAutoPageSize = undefined;\n        /** Set to `true` to have pages split children of groups when using Row Grouping or detail rows with Master Detail.\n         * @default false\n         * @initial\n         * @agModule `PaginationModule`\n         */\n        this.paginateChildRows = undefined;\n        /** If `true`, the default grid controls for navigation are hidden.\n         * This is useful if `pagination=true` and you want to provide your own pagination controls.\n         * Otherwise, when `pagination=true` the grid automatically shows the necessary controls at the bottom so that the user can navigate through the different pages.\n         * @default false\n         * @agModule `PaginationModule`\n         */\n        this.suppressPaginationPanel = undefined;\n        /** Set to `true` to enable pivot mode.\n         * @default false\n         * @agModule `PivotModule`\n         */\n        this.pivotMode = undefined;\n        /** When to show the 'pivot panel' (where you drag rows to pivot) at the top. Note that the pivot panel will never show if `pivotMode` is off.\n         * @default 'never'\n         * @initial\n         * @agModule `RowGroupingPanelModule`\n         */\n        this.pivotPanelShow = undefined;\n        /** The maximum number of generated columns before the grid halts execution. Upon reaching this number, the grid halts generation of columns\n         * and triggers a `pivotMaxColumnsExceeded` event. `-1` for no limit.\n         * @default -1\n         * @agModule `PivotModule`\n         */\n        this.pivotMaxGeneratedColumns = undefined;\n        /** If pivoting, set to the number of column group levels to expand by default, e.g. `0` for none, `1` for first level only, etc. Set to `-1` to expand everything.\n         * @default 0\n         * @agModule `PivotModule`\n         */\n        this.pivotDefaultExpanded = undefined;\n        /** When set and the grid is in pivot mode, automatically calculated totals will appear within the Pivot Column Groups, in the position specified.\n         * @agModule `PivotModule`\n         */\n        this.pivotColumnGroupTotals = undefined;\n        /** When set and the grid is in pivot mode, automatically calculated totals will appear for each value column in the position specified.\n         * @agModule `PivotModule`\n         */\n        this.pivotRowTotals = undefined;\n        /** If `true`, the grid will not swap in the grouping column when pivoting. Useful if pivoting using Server Side Row Model or Viewport Row Model and you want full control of all columns including the group column.\n         * @default false\n         * @initial\n         * @agModule `PivotModule`\n         */\n        this.pivotSuppressAutoColumn = undefined;\n        /** When enabled, pivot column groups will appear 'fixed', without the ability to expand and collapse the column groups.\n         * @default false\n         * @initial\n         * @agModule `PivotModule`\n         */\n        this.suppressExpandablePivotGroups = undefined;\n        /** If `true`, then row group, pivot and value aggregation will be read-only from the GUI. The grid will display what values are used for each, but will not allow the user to change the selection.\n         * @default false\n         * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n         */\n        this.functionsReadOnly = undefined;\n        /** A map of 'function name' to 'function' for custom aggregation functions.\n         * @initial\n         * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n         */\n        this.aggFuncs = undefined;\n        /** When `true`, column headers won't include the `aggFunc` name, e.g. `'sum(Bank Balance)`' will just be `'Bank Balance'`.\n         * @default false\n         * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n         */\n        this.suppressAggFuncInHeader = undefined;\n        /** When using aggregations, the grid will always calculate the root level aggregation value.\n         * @default false\n         * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n         */\n        this.alwaysAggregateAtRootLevel = undefined;\n        /** When using change detection, only the updated column will be re-aggregated.\n         * @default false\n         * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n         */\n        this.aggregateOnlyChangedColumns = undefined;\n        /** Set to `true` so that aggregations are not impacted by filtering.\n         * @default false\n         * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n         */\n        this.suppressAggFilteredOnly = undefined;\n        /** Set to `true` to omit the value Column header when there is only a single value column.\n         * @default false\n         * @agModule `PivotModule`\n         */\n        this.removePivotHeaderRowWhenSingleValueColumn = undefined;\n        /** Set to `false` to disable Row Animation which is enabled by default.\n         * @default true\n         */\n        this.animateRows = undefined;\n        /** Sets the duration in milliseconds of how long a cell should remain in its \"flashed\" state.\n         * If `0`, the cell will not flash.\n         * @default 500\n         */\n        this.cellFlashDuration = undefined;\n        /** Sets the duration in milliseconds of how long the \"flashed\" state animation takes to fade away after the timer set by `cellFlashDuration` has completed.\n         * @default 1000\n         */\n        this.cellFadeDuration = undefined;\n        /** Set to `true` to have cells flash after data changes even when the change is due to filtering.\n         * @default false\n         * @initial\n         */\n        this.allowShowChangeAfterFilter = undefined;\n        /** Switch between layout options: `normal`, `autoHeight`, `print`.\n         * @default 'normal'\n         */\n        this.domLayout = undefined;\n        /** When `true`, the order of rows and columns in the DOM are consistent with what is on screen.\n         * Disables row animations.\n         * @default false\n         * @initial\n         */\n        this.ensureDomOrder = undefined;\n        /** When `true`, enables the cell span feature allowing for the use of the `colDef.spanRows` property.\n         * @default false\n         * @initial\n         * @agModule `CellSpanModule`\n         */\n        this.enableCellSpan = undefined;\n        /** Set to `true` to operate the grid in RTL (Right to Left) mode.\n         * @default false\n         * @initial\n         */\n        this.enableRtl = undefined;\n        /** Set to `true` so that the grid doesn't virtualise the columns. For example, if you have 100 columns, but only 10 visible due to scrolling, all 100 will always be rendered.\n         *     **It is not recommended to set this to `true` as it may cause performance issues.**\n         * @default false\n         * @initial\n         */\n        this.suppressColumnVirtualisation = undefined;\n        /** By default the grid has a limit of rendering a maximum of 500 rows at once (remember the grid only renders rows you can see, so unless your display shows more than 500 rows without vertically scrolling this will never be an issue).\n         * <br />**This is only relevant if you are manually setting `rowBuffer` to a high value (rendering more rows than can be seen), or `suppressRowVirtualisation` is true, or if your grid height is able to display more than 500 rows at once.**\n         * @default false\n         * @initial\n         */\n        this.suppressMaxRenderedRowRestriction = undefined;\n        /** Set to `true` so that the grid doesn't virtualise the rows. For example, if you have 100 rows, but only 10 visible due to scrolling, all 100 will always be rendered.\n         *     **It is not recommended to set this to `true` as it may cause performance issues.**\n         * @default false\n         * @initial\n         */\n        this.suppressRowVirtualisation = undefined;\n        /** Set to `true` to enable Managed Row Dragging.\n         * @default false\n         * @agModule `RowDragModule`\n         */\n        this.rowDragManaged = undefined;\n        /** Set to `true` to suppress row dragging.\n         * @default false\n         */\n        this.suppressRowDrag = undefined;\n        /** Set to `true` to suppress moving rows while dragging the `rowDrag` waffle. This option highlights the position where the row will be placed and it will only move the row on mouse up.\n         * @default false\n         * @agModule `RowDragModule`\n         */\n        this.suppressMoveWhenRowDragging = undefined;\n        /** Set to `true` to enable clicking and dragging anywhere on the row without the need for a drag handle.\n         * @default false\n         * @agModule `RowDragModule`\n         */\n        this.rowDragEntireRow = undefined;\n        /** Set to `true` to enable dragging multiple rows at the same time.\n         * @default false\n         * @agModule `RowDragModule`\n         */\n        this.rowDragMultiRow = undefined;\n        /** A callback that should return a string to be displayed by the `rowDragComp` while dragging a row.\n         * If this callback is not set, the current cell value will be used.\n         * If the `rowDragText` callback is set in the ColDef it will take precedence over this, except when\n         * `rowDragEntireRow=true`.\n         * @initial\n         * @agModule `RowDragModule`\n         */\n        this.rowDragText = undefined;\n        /** Provide a custom drag and drop image component.\n         * @initial\n         * @agModule `RowDragModule`\n         */\n        this.dragAndDropImageComponent = undefined;\n        /** Customise the parameters provided to the Drag and Drop Image Component.\n         * @agModule `RowDragModule`\n         */\n        this.dragAndDropImageComponentParams = undefined;\n        /** Provide your own cell renderer component to use for full width rows.\n         * See [Full Width Rows](https://www.ag-grid.com/javascript-data-grid/full-width-rows/) for framework specific implementation details.\n         */\n        this.fullWidthCellRenderer = undefined;\n        /** Customise the parameters provided to the `fullWidthCellRenderer` component.\n         */\n        this.fullWidthCellRendererParams = undefined;\n        /** Set to `true` to have the Full Width Rows embedded in grid's main container so they can be scrolled horizontally.\n         */\n        this.embedFullWidthRows = undefined;\n        /** Specifies how the results of row grouping should be displayed.\n         *\n         *  The options are:\n         *\n         * - `'singleColumn'`: single group column automatically added by the grid.\n         * - `'multipleColumns'`: a group column per row group is added automatically.\n         * - `'groupRows'`: group rows are automatically added instead of group columns.\n         * - `'custom'`: informs the grid that group columns will be provided.\n         * @agModule `RowGroupingModule`\n         */\n        this.groupDisplayType = undefined;\n        /** If grouping, set to the number of levels to expand by default, e.g. `0` for none, `1` for first level only, etc. Set to `-1` to expand everything.\n         * @default 0\n         * @agModule `RowGroupingModule` / `TreeDataModule`\n         */\n        this.groupDefaultExpanded = undefined;\n        /** Allows specifying the group 'auto column' if you are not happy with the default. If grouping, this column definition is included as the first column in the grid. If not grouping, this column is not included.\n         * @agModule `RowGroupingModule` / `TreeDataModule`\n         */\n        this.autoGroupColumnDef = undefined;\n        /** When `true`, preserves the current group order when sorting on non-group columns.\n         * @default false\n         * @agModule `RowGroupingModule`\n         */\n        this.groupMaintainOrder = undefined;\n        /** When `true`, if you select a group, the children of the group will also be selected.\n         * @default false\n         * @deprecated v32.2 Use `rowSelection.groupSelects` instead\n         */\n        this.groupSelectsChildren = undefined;\n        /** If grouping, locks the group settings of a number of columns, e.g. `0` for no group locking. `1` for first group column locked, `-1` for all group columns locked.\n         * @default 0\n         * @initial\n         * @agModule `RowGroupingModule`\n         */\n        this.groupLockGroupColumns = undefined;\n        /** Set to determine whether filters should be applied on aggregated group values.\n         * @default false\n         * @agModule `RowGroupingModule`\n         */\n        this.groupAggFiltering = undefined;\n        /** When provided, an extra row group total row will be inserted into row groups at the specified position, to display\n         * when the group is expanded. This row will contain the aggregate values for the group. If a callback function is\n         * provided, it can be used to selectively determine which groups will have a total row added.\n         * @agModule `RowGroupingModule` / `ServerSideRowModelModule`\n         */\n        this.groupTotalRow = undefined;\n        /** When provided, an extra grand total row will be inserted into the grid at the specified position.\n         * This row displays the aggregate totals of all rows in the grid.\n         * @agModule `RowGroupingModule` / `ServerSideRowModelModule`\n         */\n        this.grandTotalRow = undefined;\n        /** Suppress the sticky behaviour of the total rows, can be suppressed individually by passing `'grand'` or `'group'`.\n         * @agModule `RowGroupingModule` / `ServerSideRowModelModule`\n         */\n        this.suppressStickyTotalRow = undefined;\n        /** If `true`, and showing footer, aggregate data will always be displayed at both the header and footer levels. This stops the possibly undesirable behaviour of the header details 'jumping' to the footer on expand.\n         * @default false\n         * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n         */\n        this.groupSuppressBlankHeader = undefined;\n        /** If using `groupSelectsChildren`, then only the children that pass the current filter will get selected.\n         * @default false\n         * @deprecated v32.2 Use `rowSelection.groupSelects` instead\n         */\n        this.groupSelectsFiltered = undefined;\n        /** Shows the open group in the group column for non-group rows.\n         * @default false\n         * @agModule `RowGroupingModule`\n         */\n        this.showOpenedGroup = undefined;\n        /** Enable to display the child row in place of the group row when the group only has a single child.\n         * @default false\n         * @agModule `RowGroupingModule`\n         */\n        this.groupHideParentOfSingleChild = undefined;\n        /** Set to `true` to collapse groups that only have one child.\n         * @default false\n         * @deprecated v33.0.0 - use `groupHideParentOfSingleChild` instead.\n         */\n        this.groupRemoveSingleChildren = undefined;\n        /** Set to `true` to collapse lowest level groups that only have one child.\n         * @default false\n         * @deprecated v33.0.0 - use `groupHideParentOfSingleChild: 'leafGroupsOnly'` instead.\n         */\n        this.groupRemoveLowestSingleChildren = undefined;\n        /** Set to `true` to hide parents that are open. When used with multiple columns for showing groups, it can give a more pleasing user experience.\n         * @default false\n         * @agModule `RowGroupingModule`\n         */\n        this.groupHideOpenParents = undefined;\n        /** Set to `true` to prevent the grid from creating a '(Blanks)' group for nodes which do not belong to a group, and display the unbalanced nodes alongside group nodes.\n         * @default false\n         * @agModule `RowGroupingModule`\n         */\n        this.groupAllowUnbalanced = undefined;\n        /** When to show the 'row group panel' (where you drag rows to group) at the top.\n         * @default 'never'\n         * @agModule `RowGroupingPanelModule`\n         */\n        this.rowGroupPanelShow = undefined;\n        /** Provide the Cell Renderer to use when `groupDisplayType = 'groupRows'`.\n         * See [Group Row Cell Renderer](https://www.ag-grid.com/javascript-data-grid/grouping-group-rows/#providing-cell-renderer) for framework specific implementation details.\n         * @agModule `RowGroupingModule`\n         */\n        this.groupRowRenderer = undefined;\n        /** Customise the parameters provided to the `groupRowRenderer` component.\n         * @agModule `RowGroupingModule`\n         */\n        this.groupRowRendererParams = undefined;\n        /** Set to `true` to enable the Grid to work with Tree Data.\n         * You must also implement the `getDataPath(data)` callback.\n         * @default false\n         * @agModule `TreeDataModule`\n         */\n        this.treeData = undefined;\n        /** The name of the field to use in a data item to retrieve the array of children nodes of a node when while using treeData=true.\n         * It supports accessing nested fields using the dot notation.\n         * @agModule `TreeDataModule`\n         */\n        this.treeDataChildrenField = undefined;\n        /** The name of the field to use in a data item to find the parent node of a node when using treeData=true.\n         * The tree will be constructed via relationships between nodes using this field.\n         * getRowId callback need to be provided as well for this to work.\n         * It supports accessing nested fields using the dot notation.\n         * @agModule `TreeDataModule`\n         */\n        this.treeDataParentIdField = undefined;\n        /** Set to `true` to suppress sort indicators and actions from the row group panel.\n         * @default false\n         * @agModule `RowGroupingPanelModule`\n         */\n        this.rowGroupPanelSuppressSort = undefined;\n        /** Set to `true` prevent Group Rows from sticking to the top of the grid.\n         * @default false\n         * @initial\n         * @agModule `RowGroupingModule` / `TreeDataModule`\n         */\n        this.suppressGroupRowsSticky = undefined;\n        /** Data to be displayed as pinned top rows in the grid.\n         * @agModule `PinnedRowModule`\n         */\n        this.pinnedTopRowData = undefined;\n        /** Data to be displayed as pinned bottom rows in the grid.\n         * @agModule `PinnedRowModule`\n         */\n        this.pinnedBottomRowData = undefined;\n        /** Determines whether manual row pinning is enabled via the row context menu.\n         *\n         * Set to `true` to allow pinning rows to top or bottom.\n         * Set to `'top'` to allow pinning rows to the top only.\n         * Set to `'bottom'` to allow pinning rows to the bottom only.\n         * @agModule `PinnedRowModule`\n         */\n        this.enableRowPinning = undefined;\n        /** Return `true` if the grid should allow the row to be manually pinned.\n         * Return `false` if the grid should prevent the row from being pinned\n         *\n         * When not defined, all rows default to pinnable.\n         * @agModule `PinnedRowModule`\n         */\n        this.isRowPinnable = undefined;\n        /** Called for every row in the grid.\n         *\n         * Return `true` if the row should be pinned initially. Return `false` otherwise.\n         * User interactions can subsequently still change the pinned state of a row.\n         * @agModule `PinnedRowModule`\n         */\n        this.isRowPinned = undefined;\n        /** Sets the row model type.\n         * @default 'clientSide'\n         * @initial\n         * @agModule `ClientSideRowModelModule` / `InfiniteRowModelModule` / `ServerSideRowModelModule` / `ViewportRowModelModule`\n         */\n        this.rowModelType = undefined;\n        /** Set the data to be displayed as rows in the grid.\n         * @agModule `ClientSideRowModelModule`\n         */\n        this.rowData = undefined;\n        /** How many milliseconds to wait before executing a batch of async transactions.\n         */\n        this.asyncTransactionWaitMillis = undefined;\n        /** Prevents Transactions changing sort, filter, group or pivot state when transaction only contains updates.\n         * @default false\n         */\n        this.suppressModelUpdateAfterUpdateTransaction = undefined;\n        /** Provide the datasource for infinite scrolling.\n         * @agModule `InfiniteRowModelModule`\n         */\n        this.datasource = undefined;\n        /** How many extra blank rows to display to the user at the end of the dataset, which sets the vertical scroll and then allows the grid to request viewing more rows of data.\n         * @default 1\n         * @initial\n         * @agModule `InfiniteRowModelModule`\n         */\n        this.cacheOverflowSize = undefined;\n        /** How many extra blank rows to display to the user at the end of the dataset, which sets the vertical scroll and then allows the grid to request viewing more rows of data.\n         * @default 1\n         * @initial\n         * @agModule `InfiniteRowModelModule`\n         */\n        this.infiniteInitialRowCount = undefined;\n        /** Set how many loading rows to display to the user for the root level group.\n         * @default 1\n         * @initial\n         * @agModule `ServerSideRowModelModule`\n         */\n        this.serverSideInitialRowCount = undefined;\n        /** When `true`, the Server-side Row Model will not use a full width loading renderer, instead using the colDef `loadingCellRenderer` if present.\n         * @agModule `ServerSideRowModelModule`\n         */\n        this.suppressServerSideFullWidthLoadingRow = undefined;\n        /** How many rows for each block in the store, i.e. how many rows returned from the server at a time.\n         * @default 100\n         * @agModule `ServerSideRowModelModule` / `InfiniteRowModelModule`\n         */\n        this.cacheBlockSize = undefined;\n        /** How many blocks to keep in the store. Default is no limit, so every requested block is kept. Use this if you have memory concerns, and blocks that were least recently viewed will be purged when the limit is hit. The grid will additionally make sure it has all the blocks needed to display what is currently visible, in case this property is set to a low value.\n         * @initial\n         * @agModule `ServerSideRowModelModule` / `InfiniteRowModelModule`\n         */\n        this.maxBlocksInCache = undefined;\n        /** How many requests to hit the server with concurrently. If the max is reached, requests are queued.\n         * Set to `-1` for no maximum restriction on requests.\n         * @default 2\n         * @initial\n         * @agModule `ServerSideRowModelModule` / `InfiniteRowModelModule`\n         */\n        this.maxConcurrentDatasourceRequests = undefined;\n        /** How many milliseconds to wait before loading a block. Useful when scrolling over many blocks, as it prevents blocks loading until scrolling has settled.\n         * @initial\n         * @agModule `ServerSideRowModelModule` / `InfiniteRowModelModule`\n         */\n        this.blockLoadDebounceMillis = undefined;\n        /** When enabled, closing group rows will remove children of that row. Next time the row is opened, child rows will be read from the datasource again. This property only applies when there is Row Grouping or Tree Data.\n         * @default false\n         * @agModule `ServerSideRowModelModule`\n         */\n        this.purgeClosedRowNodes = undefined;\n        /** Provide the `serverSideDatasource` for server side row model.\n         * @agModule `ServerSideRowModelModule`\n         */\n        this.serverSideDatasource = undefined;\n        /** When enabled, always refreshes top level groups regardless of which column was sorted. This property only applies when there is Row Grouping & sorting is handled on the server.\n         * @default false\n         * @agModule `ServerSideRowModelModule`\n         */\n        this.serverSideSortAllLevels = undefined;\n        /** When enabled, sorts fully loaded groups in the browser instead of requesting from the server.\n         * @default false\n         * @agModule `ServerSideRowModelModule`\n         */\n        this.serverSideEnableClientSideSort = undefined;\n        /** When enabled, only refresh groups directly impacted by a filter. This property only applies when there is Row Grouping & filtering is handled on the server.\n         * @default false\n         * @initial\n         * @agModule `ServerSideRowModelModule`\n         */\n        this.serverSideOnlyRefreshFilteredGroups = undefined;\n        /** Used to split pivot field strings for generating pivot result columns when `pivotResultFields` is provided as part of a `getRows` success.\n         * @default '_'\n         * @initial\n         * @agModule `ServerSideRowModelModule`\n         */\n        this.serverSidePivotResultFieldSeparator = undefined;\n        /** To use the viewport row model you need to provide the grid with a `viewportDatasource`.\n         * @agModule `ViewportRowModelModule`\n         */\n        this.viewportDatasource = undefined;\n        /** When using viewport row model, sets the page size for the viewport.\n         * @initial\n         * @agModule `ViewportRowModelModule`\n         */\n        this.viewportRowModelPageSize = undefined;\n        /** When using viewport row model, sets the buffer size for the viewport.\n         * @initial\n         * @agModule `ViewportRowModelModule`\n         */\n        this.viewportRowModelBufferSize = undefined;\n        /** Set to `true` to always show the horizontal scrollbar.\n         * @default false\n         */\n        this.alwaysShowHorizontalScroll = undefined;\n        /** Set to `true` to always show the vertical scrollbar.\n         * @default false\n         */\n        this.alwaysShowVerticalScroll = undefined;\n        /** Set to `true` to debounce the vertical scrollbar. Can provide smoother scrolling on slow machines.\n         * @default false\n         * @initial\n         */\n        this.debounceVerticalScrollbar = undefined;\n        /** Set to `true` to never show the horizontal scroll. This is useful if the grid is aligned with another grid and will scroll when the other grid scrolls. (Should not be used in combination with `alwaysShowHorizontalScroll`.)\n         * @default false\n         */\n        this.suppressHorizontalScroll = undefined;\n        /** When `true`, the grid will not scroll to the top when new row data is provided. Use this if you don't want the default behaviour of scrolling to the top every time you load new data.\n         * @default false\n         */\n        this.suppressScrollOnNewData = undefined;\n        /** When `true`, the grid will not allow mousewheel / touchpad scroll when popup elements are present.\n         * @default false\n         */\n        this.suppressScrollWhenPopupsAreOpen = undefined;\n        /** When `true`, the grid will not use animation frames when drawing rows while scrolling. Use this if and only if the grid is working fast enough on all users machines and you want to avoid the temporarily empty rows.\n         *     **Note:** It is not recommended to set suppressAnimationFrame to `true` in most use cases as this can seriously degrade the user experience as all cells are rendered synchronously blocking the UI thread from scrolling.\n         * @default false\n         * @initial\n         */\n        this.suppressAnimationFrame = undefined;\n        /** If `true`, middle clicks will result in `click` events for cells and rows. Otherwise the browser will use middle click to scroll the grid.<br />**Note:** Not all browsers fire `click` events with the middle button. Most will fire only `mousedown` and `mouseup` events, which can be used to focus a cell, but will not work to call the `onCellClicked` function.\n         * @default false\n         */\n        this.suppressMiddleClickScrolls = undefined;\n        /** If `true`, mouse wheel events will be passed to the browser. Useful if your grid has no vertical scrolls and you want the mouse to scroll the browser page.\n         * @default false\n         * @initial\n         */\n        this.suppressPreventDefaultOnMouseWheel = undefined;\n        /** Tell the grid how wide in pixels the scrollbar is, which is used in grid width calculations. Set only if using non-standard browser-provided scrollbars, so the grid can use the non-standard size in its calculations.\n         * @initial\n         */\n        this.scrollbarWidth = undefined;\n        /** Use the `RowSelectionOptions` object to configure row selection. The string values `'single'` and `'multiple'` are deprecated.\n         * @agModule `RowSelectionModule`\n         */\n        this.rowSelection = undefined;\n        /** Configure cell selection.\n         * @agModule `CellSelectionModule`\n         */\n        this.cellSelection = undefined;\n        /** Set to `true` to allow multiple rows to be selected using single click.\n         * @default false\n         * @deprecated v32.2 Use `rowSelection.enableSelectionWithoutKeys` instead\n         */\n        this.rowMultiSelectWithClick = undefined;\n        /** If `true`, rows will not be deselected if you hold down `Ctrl` and click the row or press `Space`.\n         * @default false\n         * @deprecated v32.2 Use `rowSelection.enableClickSelection` instead\n         */\n        this.suppressRowDeselection = undefined;\n        /** If `true`, row selection won't happen when rows are clicked. Use when you only want checkbox selection.\n         * @default false\n         * @deprecated v32.2 Use `rowSelection.enableClickSelection` instead\n         */\n        this.suppressRowClickSelection = undefined;\n        /** If `true`, cells won't be focusable. This means keyboard navigation will be disabled for grid cells, but remain enabled in other elements of the grid such as column headers, floating filters, tool panels.\n         * @default false\n         */\n        this.suppressCellFocus = undefined;\n        /** If `true`, header cells won't be focusable. This means keyboard navigation will be disabled for grid header cells, but remain enabled in other elements of the grid such as grid cells and tool panels.\n         * @default false\n         */\n        this.suppressHeaderFocus = undefined;\n        /** Configure the selection column, used for displaying checkboxes.\n         *\n         * Note that due to the nature of this column, this type is a subset of `ColDef`, which does not support several normal column features such as editing, pivoting and grouping.\n         */\n        this.selectionColumnDef = undefined;\n        /** Configure the Row Numbers Feature.\n         * @default false\n         * @agModule `RowNumbersModule`\n         */\n        this.rowNumbers = undefined;\n        /** If `true`, only a single range can be selected.\n         * @default false\n         * @deprecated v32.2 Use `cellSelection.suppressMultiRanges` instead\n         */\n        this.suppressMultiRangeSelection = undefined;\n        /** Set to `true` to be able to select the text within cells.\n         *\n         *     **Note:** When this is set to `true`, the clipboard service is disabled and only selected text is copied.\n         * @default false\n         */\n        this.enableCellTextSelection = undefined;\n        /** Set to `true` to enable Range Selection.\n         * @default false\n         * @deprecated v32.2 Use `cellSelection = true` instead\n         * @agModule `CellSelectionModule`\n         */\n        this.enableRangeSelection = undefined;\n        /** Set to `true` to enable the Range Handle.\n         * @default false\n         * @deprecated v32.2 Use `cellSelection.handle` instead\n         */\n        this.enableRangeHandle = undefined;\n        /** Set to `true` to enable the Fill Handle.\n         * @default false\n         * @deprecated v32.2 Use `cellSelection.handle` instead\n         */\n        this.enableFillHandle = undefined;\n        /** Set to `'x'` to force the fill handle direction to horizontal, or set to `'y'` to force the fill handle direction to vertical.\n         * @default 'xy'\n         * @deprecated v32.2 Use `cellSelection.handle.direction` instead\n         */\n        this.fillHandleDirection = undefined;\n        /** Set this to `true` to prevent cell values from being cleared when the Range Selection is reduced by the Fill Handle.\n         * @default false\n         * @deprecated v32.2 Use `cellSelection.suppressClearOnFillReduction` instead\n         */\n        this.suppressClearOnFillReduction = undefined;\n        /** Array defining the order in which sorting occurs (if sorting is enabled). Values can be `'asc'`, `'desc'` or `null`. For example: `sortingOrder: ['asc', 'desc']`.\n         * @default [null, 'asc', 'desc']\n         * @deprecated v33 Use `defaultColDef.sortingOrder` instead\n         */\n        this.sortingOrder = undefined;\n        /** Set to `true` to specify that the sort should take accented characters into account. If this feature is turned on the sort will be slower.\n         * @default false\n         */\n        this.accentedSort = undefined;\n        /** Set to `true` to show the 'no sort' icon.\n         * @default false\n         * @deprecated v33 Use `defaultColDef.unSortIcon` instead\n         */\n        this.unSortIcon = undefined;\n        /** Set to `true` to suppress multi-sort when the user shift-clicks a column header.\n         * @default false\n         */\n        this.suppressMultiSort = undefined;\n        /** Set to `true` to always multi-sort when the user clicks a column header, regardless of key presses.\n         * @default false\n         */\n        this.alwaysMultiSort = undefined;\n        /** Set to `'ctrl'` to have multi sorting by clicking work using the `Ctrl` (or `Command ⌘` for Mac) key.\n         */\n        this.multiSortKey = undefined;\n        /** Set to `true` to suppress sorting of un-sorted data to match original row data.\n         * @default false\n         */\n        this.suppressMaintainUnsortedOrder = undefined;\n        /** Icons to use inside the grid instead of the grid's default icons.\n         * @initial\n         */\n        this.icons = undefined;\n        /** Default row height in pixels.\n         * @default 25\n         */\n        this.rowHeight = undefined;\n        /** The style properties to apply to all rows. Set to an object of key (style names) and values (style values).\n         * @agModule `RowStyleModule`\n         */\n        this.rowStyle = undefined;\n        /** CSS class(es) for all rows. Provide either a string (class name) or array of strings (array of class names).\n         * @agModule `RowStyleModule`\n         */\n        this.rowClass = undefined;\n        /** Rules which can be applied to include certain CSS classes.\n         * @agModule `RowStyleModule`\n         */\n        this.rowClassRules = undefined;\n        /** Set to `true` to not highlight rows by adding the `ag-row-hover` CSS class.\n         * @default false\n         */\n        this.suppressRowHoverHighlight = undefined;\n        /** Uses CSS `top` instead of CSS `transform` for positioning rows. Useful if the transform function is causing issues such as used in row spanning.\n         * @default false\n         * @initial\n         */\n        this.suppressRowTransform = undefined;\n        /** Set to `true` to highlight columns by adding the `ag-column-hover` CSS class.\n         * @default false\n         * @agModule `ColumnHoverModule`\n         */\n        this.columnHoverHighlight = undefined;\n        /** Provide a custom `gridId` for this instance of the grid. Value will be set on the root DOM node using the attribute `grid-id` as well as being accessible via the `gridApi.getGridId()` method.\n         * @initial\n         */\n        this.gridId = undefined;\n        /** When enabled, sorts only the rows added/updated by a transaction.\n         * @default false\n         */\n        this.deltaSort = undefined;\n        /**/\n        this.treeDataDisplayType = undefined;\n        /** @initial\n         */\n        this.enableGroupEdit = undefined;\n        /** Initial state for the grid. Only read once on initialization. Can be used in conjunction with `api.getState()` to save and restore grid state.\n         * @initial\n         * @agModule `GridStateModule`\n         */\n        this.initialState = undefined;\n        /** Theme to apply to the grid, or the string \"legacy\" to opt back into the\n         * v32 style of theming where themes were imported as CSS files and applied\n         * by setting a class name on the parent element.\n         *\n         * @default themeQuartz\n         */\n        this.theme = undefined;\n        /** If your theme uses a font that is available on Google Fonts, pass true to load it from Google's CDN.\n         */\n        this.loadThemeGoogleFonts = undefined;\n        /** The CSS layer that this theme should be rendered onto. If your\n         * application loads its styles into a CSS layer, use this to load the grid\n         * styles into a previous layer so that application styles can override grid\n         * styles.\n         *\n         * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@layer\n         */\n        this.themeCssLayer = undefined;\n        /** The nonce attribute to set on style elements added to the document by\n         * themes. If \"foo\" is passed to this property, the grid can use the Content\n         * Security Policy `style-src 'nonce-foo'`, instead of the less secure\n         * `style-src 'unsafe-inline'`.\n         *\n         * Note: CSP nonces are global to a page, where a page has multiple grids,\n         * every one must have the same styleNonce set.\n         */\n        this.styleNonce = undefined;\n        /** An element to insert style elements into when injecting styles into the\n         * grid. If undefined, styles will be added to the document head for grids\n         * rendered in the main document fragment, or to the grid wrapper element\n         * for other grids (e.g. those rendered in a shadow DOM or detached from the\n         * document).\n         *\n         * @initial\n         */\n        this.themeStyleContainer = undefined;\n        /** For customising the context menu.\n         * @agModule `ContextMenuModule`\n         */\n        this.getContextMenuItems = undefined;\n        /** For customising the main 'column header' menu.\n         * @initial\n         * @agModule `ColumnMenuModule`\n         */\n        this.getMainMenuItems = undefined;\n        /** Allows user to process popups after they are created. Applications can use this if they want to, for example, reposition the popup.\n         */\n        this.postProcessPopup = undefined;\n        /** Allows the user to process the columns being removed from the pinned section because the viewport is too small to accommodate them.\n         * Returns an array of columns to be removed from the pinned areas.\n         * @initial\n         */\n        this.processUnpinnedColumns = undefined;\n        /** Allows you to process cells for the clipboard. Handy if for example you have `Date` objects that need to have a particular format if importing into Excel.\n         * @agModule `ClipboardModule`\n         */\n        this.processCellForClipboard = undefined;\n        /** Allows you to process header values for the clipboard.\n         * @agModule `ClipboardModule`\n         */\n        this.processHeaderForClipboard = undefined;\n        /** Allows you to process group header values for the clipboard.\n         * @agModule `ClipboardModule`\n         */\n        this.processGroupHeaderForClipboard = undefined;\n        /** Allows you to process cells from the clipboard. Handy if for example you have number fields and want to block non-numbers from getting into the grid.\n         * @agModule `ClipboardModule`\n         */\n        this.processCellFromClipboard = undefined;\n        /** Allows you to get the data that would otherwise go to the clipboard. To be used when you want to control the 'copy to clipboard' operation yourself.\n         * @agModule `ClipboardModule`\n         */\n        this.sendToClipboard = undefined;\n        /** Allows complete control of the paste operation, including cancelling the operation (so nothing happens) or replacing the data with other data.\n         * @agModule `ClipboardModule`\n         */\n        this.processDataFromClipboard = undefined;\n        /** Grid calls this method to know if an external filter is present.\n         * @agModule `ExternalFilterModule`\n         */\n        this.isExternalFilterPresent = undefined;\n        /** Should return `true` if external filter passes, otherwise `false`.\n         * @agModule `ExternalFilterModule`\n         */\n        this.doesExternalFilterPass = undefined;\n        /** Callback to be used to customise the chart toolbar items.\n         * @initial\n         * @agModule `IntegratedChartsModule`\n         */\n        this.getChartToolbarItems = undefined;\n        /** Callback to enable displaying the chart in an alternative chart container.\n         * @initial\n         * @agModule `IntegratedChartsModule`\n         */\n        this.createChartContainer = undefined;\n        /** Allows overriding the element that will be focused when the grid receives focus from outside elements (tabbing into the grid).\n         * @returns `True` if this function should override the grid's default behavior, `False` to allow the grid's default behavior.\n         */\n        this.focusGridInnerElement = undefined;\n        /** Allows overriding the default behaviour for when user hits navigation (arrow) key when a header is focused. Return the next Header position to navigate to or `null` to stay on current header.\n         */\n        this.navigateToNextHeader = undefined;\n        /** Allows overriding the default behaviour for when user hits `Tab` key when a header is focused.\n         * Return the next header position to navigate to, `true` to stay on the current header,\n         * or `false` to let the browser handle the tab behaviour.\n         */\n        this.tabToNextHeader = undefined;\n        /** Allows overriding the default behaviour for when user hits navigation (arrow) key when a cell is focused. Return the next Cell position to navigate to or `null` to stay on current cell.\n         */\n        this.navigateToNextCell = undefined;\n        /** Allows overriding the default behaviour for when user hits `Tab` key when a cell is focused.\n         * Return the next cell position to navigate to, `true` to stay on the current cell,\n         * or `false` to let the browser handle the tab behaviour.\n         */\n        this.tabToNextCell = undefined;\n        /** A callback for localising text within the grid.\n         * @initial\n         * @agModule `LocaleModule`\n         */\n        this.getLocaleText = undefined;\n        /** Allows overriding what `document` is used. Currently used by Drag and Drop (may extend to other places in the future). Use this when you want the grid to use a different `document` than the one available on the global scope. This can happen if docking out components (something which Electron supports)\n         */\n        this.getDocument = undefined;\n        /** Allows user to format the numbers in the pagination panel, i.e. 'row count' and 'page number' labels. This is for pagination panel only, to format numbers inside the grid's cells (i.e. your data), then use `valueFormatter` in the column definitions.\n         * @initial\n         * @agModule `PaginationModule`\n         */\n        this.paginationNumberFormatter = undefined;\n        /** Callback to use when you need access to more then the current column for aggregation.\n         * @agModule `RowGroupingModule` / `PivotModule` / `TreeDataModule` / `ServerSideRowModelModule`\n         */\n        this.getGroupRowAgg = undefined;\n        /** (Client-side Row Model only) Allows groups to be open by default.\n         * @agModule `RowGroupingModule` / `TreeDataModule`\n         */\n        this.isGroupOpenByDefault = undefined;\n        /** Allows default sorting of groups.\n         * @agModule `RowGroupingModule`\n         */\n        this.initialGroupOrderComparator = undefined;\n        /** Callback for the mutation of the generated pivot result column definitions\n         * @agModule `PivotModule`\n         */\n        this.processPivotResultColDef = undefined;\n        /** Callback for the mutation of the generated pivot result column group definitions\n         * @agModule `PivotModule`\n         */\n        this.processPivotResultColGroupDef = undefined;\n        /** Callback to be used when working with Tree Data when `treeData = true`.\n         * @initial\n         * @agModule `TreeDataModule`\n         */\n        this.getDataPath = undefined;\n        /** Allows setting the child count for a group row.\n         * @initial\n         * @agModule `ServerSideRowModelModule`\n         */\n        this.getChildCount = undefined;\n        /** Allows providing different params for different levels of grouping.\n         * @initial\n         * @agModule `ServerSideRowModelModule`\n         */\n        this.getServerSideGroupLevelParams = undefined;\n        /** Allows groups to be open by default.\n         * @agModule `ServerSideRowModelModule`\n         */\n        this.isServerSideGroupOpenByDefault = undefined;\n        /** Allows cancelling transactions.\n         * @agModule `ServerSideRowModelModule`\n         */\n        this.isApplyServerSideTransaction = undefined;\n        /** SSRM Tree Data: Allows specifying which rows are expandable.\n         * @agModule `ServerSideRowModelModule`\n         */\n        this.isServerSideGroup = undefined;\n        /** SSRM Tree Data: Allows specifying group keys.\n         * @agModule `ServerSideRowModelModule`\n         */\n        this.getServerSideGroupKey = undefined;\n        /** Return a business key for the node. If implemented, each row in the DOM will have an attribute `row-business-key='abc'` where `abc` is what you return as the business key.\n         * This is useful for automated testing, as it provides a way for your tool to identify rows based on unique business keys.\n         */\n        this.getBusinessKeyForNode = undefined;\n        /** Provide a pure function that returns a string ID to uniquely identify a given row. This enables the grid to work optimally with data changes and updates.\n         * @initial\n         */\n        this.getRowId = undefined;\n        /** When enabled, getRowId() callback is implemented and new Row Data is set, the grid will disregard all previous rows and treat the new Row Data as new data. As a consequence, all Row State (eg selection, rendered rows) will be reset.\n         * @default false\n         * @agModule `ClientSideRowModelModule`\n         */\n        this.resetRowDataOnUpdate = undefined;\n        /** Callback fired after the row is rendered into the DOM. Should not be used to initiate side effects.\n         */\n        this.processRowPostCreate = undefined;\n        /** Callback to be used to determine which rows are selectable. By default rows are selectable, so return `false` to make a row un-selectable.\n         * @deprecated v32.2 Use `rowSelection.isRowSelectable` instead\n         */\n        this.isRowSelectable = undefined;\n        /** Callback to be used with Master Detail to determine if a row should be a master row. If `false` is returned no detail row will exist for this row.\n         * @agModule `MasterDetailModule`\n         */\n        this.isRowMaster = undefined;\n        /** Callback to fill values instead of simply copying values or increasing number values using linear progression.\n         * @deprecated v32.2 Use `cellSelection.handle.setFillValue` instead\n         */\n        this.fillOperation = undefined;\n        /** Callback to perform additional sorting after the grid has sorted the rows.\n         */\n        this.postSortRows = undefined;\n        /** Callback version of property `rowStyle` to set style for each row individually. Function should return an object of CSS values or undefined for no styles.\n         * @agModule `RowStyleModule`\n         */\n        this.getRowStyle = undefined;\n        /** Callback version of property `rowClass` to set class(es) for each row individually. Function should return either a string (class name), array of strings (array of class names) or undefined for no class.\n         * @agModule `RowStyleModule`\n         */\n        this.getRowClass = undefined;\n        /** Callback version of property `rowHeight` to set height for each row individually. Function should return a positive number of pixels, or return `null`/`undefined` to use the default row height.\n         */\n        this.getRowHeight = undefined;\n        /** Tells the grid if this row should be rendered as full width.\n         */\n        this.isFullWidthRow = undefined;\n        /** The tool panel visibility has changed. Fires twice if switching between panels - once with the old panel and once with the new panel.\n         */\n        this.toolPanelVisibleChanged = new EventEmitter();\n        /** The tool panel size has been changed.\n         */\n        this.toolPanelSizeChanged = new EventEmitter();\n        /** The column menu visibility has changed. Fires twice if switching between tabs - once with the old tab and once with the new tab.\n         */\n        this.columnMenuVisibleChanged = new EventEmitter();\n        /** The context menu visibility has changed (opened or closed).\n         */\n        this.contextMenuVisibleChanged = new EventEmitter();\n        /** Cut operation has started.\n         */\n        this.cutStart = new EventEmitter();\n        /** Cut operation has ended.\n         */\n        this.cutEnd = new EventEmitter();\n        /** Paste operation has started.\n         */\n        this.pasteStart = new EventEmitter();\n        /** Paste operation has ended.\n         */\n        this.pasteEnd = new EventEmitter();\n        /** A column, or group of columns, was hidden / shown.\n         */\n        this.columnVisible = new EventEmitter();\n        /** A column, or group of columns, was pinned / unpinned.\n         */\n        this.columnPinned = new EventEmitter();\n        /** A column was resized.\n         */\n        this.columnResized = new EventEmitter();\n        /** A column was moved.\n         */\n        this.columnMoved = new EventEmitter();\n        /** A value column was added or removed.\n         */\n        this.columnValueChanged = new EventEmitter();\n        /** The pivot mode flag was changed.\n         */\n        this.columnPivotModeChanged = new EventEmitter();\n        /** A pivot column was added, removed or order changed.\n         */\n        this.columnPivotChanged = new EventEmitter();\n        /** A column group was opened / closed.\n         */\n        this.columnGroupOpened = new EventEmitter();\n        /** User set new columns.\n         */\n        this.newColumnsLoaded = new EventEmitter();\n        /** The list of grid columns changed.\n         */\n        this.gridColumnsChanged = new EventEmitter();\n        /** The list of displayed columns changed. This can result from columns open / close, column move, pivot, group, etc.\n         */\n        this.displayedColumnsChanged = new EventEmitter();\n        /** The list of rendered columns changed (only columns in the visible scrolled viewport are rendered by default).\n         */\n        this.virtualColumnsChanged = new EventEmitter();\n        /** @deprecated v32.2 Either use `onDisplayedColumnsChanged` which is fired at the same time,\n         * or use one of the more specific column events.\n         */\n        this.columnEverythingChanged = new EventEmitter();\n        /** A mouse cursor is initially moved over a column header.\n         */\n        this.columnHeaderMouseOver = new EventEmitter();\n        /** A mouse cursor is moved out of a column header.\n         */\n        this.columnHeaderMouseLeave = new EventEmitter();\n        /** A click is performed on a column header.\n         */\n        this.columnHeaderClicked = new EventEmitter();\n        /** A context menu action, such as right-click or context menu key press, is performed on a column header.\n         */\n        this.columnHeaderContextMenu = new EventEmitter();\n        /** Only used by Angular, React and VueJS AG Grid components (not used if doing plain JavaScript).\n         * If the grid receives changes due to bound properties, this event fires after the grid has finished processing the change.\n         */\n        this.componentStateChanged = new EventEmitter();\n        /** Cell value has changed. This occurs after the following scenarios:\n         * - Editing. Will not fire if any of the following are true:\n         *     new value is the same as old value;\n         *     `readOnlyEdit = true`;\n         *     editing was cancelled (e.g. Escape key was pressed);\n         *     or new value is of the wrong cell data type for the column.\n         *  - Cut.\n         *  - Paste.\n         *  - Cell clear (pressing Delete key).\n         *  - Fill handle.\n         *  - Copy range down.\n         *  - Undo and redo.\n         */\n        this.cellValueChanged = new EventEmitter();\n        /** Value has changed after editing. Only fires when `readOnlyEdit=true`.\n         */\n        this.cellEditRequest = new EventEmitter();\n        /** A cell's value within a row has changed. This event corresponds to Full Row Editing only.\n         */\n        this.rowValueChanged = new EventEmitter();\n        /** Editing a cell has started.\n         */\n        this.cellEditingStarted = new EventEmitter();\n        /** Editing a cell has stopped.\n         */\n        this.cellEditingStopped = new EventEmitter();\n        /** Editing a row has started (when row editing is enabled). When row editing, this event will be fired once and `cellEditingStarted` will be fired for each individual cell. Only fires when doing Full Row Editing.\n         */\n        this.rowEditingStarted = new EventEmitter();\n        /** Editing a row has stopped (when row editing is enabled). When row editing, this event will be fired once and `cellEditingStopped` will be fired for each individual cell. Only fires when doing Full Row Editing.\n         */\n        this.rowEditingStopped = new EventEmitter();\n        /** Undo operation has started.\n         */\n        this.undoStarted = new EventEmitter();\n        /** Undo operation has ended.\n         */\n        this.undoEnded = new EventEmitter();\n        /** Redo operation has started.\n         */\n        this.redoStarted = new EventEmitter();\n        /** Redo operation has ended.\n         */\n        this.redoEnded = new EventEmitter();\n        /** Cell selection delete operation (cell clear) has started.\n         */\n        this.cellSelectionDeleteStart = new EventEmitter();\n        /** Cell selection delete operation (cell clear) has ended.\n         */\n        this.cellSelectionDeleteEnd = new EventEmitter();\n        /** Range delete operation (cell clear) has started.\n         *\n         * @deprecated v32.2 Use `onCellSelectionDeleteStart` instead\n         */\n        this.rangeDeleteStart = new EventEmitter();\n        /** Range delete operation (cell clear) has ended.\n         *\n         * @deprecated v32.2 Use `onCellSelectionDeleteEnd` instead\n         */\n        this.rangeDeleteEnd = new EventEmitter();\n        /** Fill operation has started.\n         */\n        this.fillStart = new EventEmitter();\n        /** Fill operation has ended.\n         */\n        this.fillEnd = new EventEmitter();\n        /** Filter has been opened.\n         */\n        this.filterOpened = new EventEmitter();\n        /** Filter has been modified and applied.\n         */\n        this.filterChanged = new EventEmitter();\n        /** Filter was modified but not applied. Used when filters have 'Apply' buttons.\n         */\n        this.filterModified = new EventEmitter();\n        /** Advanced Filter Builder visibility has changed (opened or closed).\n         */\n        this.advancedFilterBuilderVisibleChanged = new EventEmitter();\n        /** Find details have changed (e.g. Find search value, active match, or updates to grid cells).\n         */\n        this.findChanged = new EventEmitter();\n        /** A chart has been created.\n         */\n        this.chartCreated = new EventEmitter();\n        /** The data range for the chart has been changed.\n         */\n        this.chartRangeSelectionChanged = new EventEmitter();\n        /** Formatting changes have been made by users through the Customize Panel.\n         */\n        this.chartOptionsChanged = new EventEmitter();\n        /** A chart has been destroyed.\n         */\n        this.chartDestroyed = new EventEmitter();\n        /** DOM event `keyDown` happened on a cell.\n         */\n        this.cellKeyDown = new EventEmitter();\n        /** The grid has initialised and is ready for most api calls, but may not be fully rendered yet      */\n        this.gridReady = new EventEmitter();\n        /** Fired the first time data is rendered into the grid. Use this event if you want to auto resize columns based on their contents     */\n        this.firstDataRendered = new EventEmitter();\n        /** The size of the grid `div` has changed. In other words, the grid was resized.\n         */\n        this.gridSizeChanged = new EventEmitter();\n        /** Displayed rows have changed. Triggered after sort, filter or tree expand / collapse events.\n         */\n        this.modelUpdated = new EventEmitter();\n        /** A row was removed from the DOM, for any reason. Use to clean up resources (if any) used by the row.\n         */\n        this.virtualRowRemoved = new EventEmitter();\n        /** Which rows are rendered in the DOM has changed.\n         */\n        this.viewportChanged = new EventEmitter();\n        /** The body was scrolled horizontally or vertically.\n         */\n        this.bodyScroll = new EventEmitter();\n        /** Main body of the grid has stopped scrolling, either horizontally or vertically.\n         */\n        this.bodyScrollEnd = new EventEmitter();\n        /** When dragging starts. This could be any action that uses the grid's Drag and Drop service, e.g. Column Moving, Column Resizing, Range Selection, Fill Handle, etc.\n         */\n        this.dragStarted = new EventEmitter();\n        /** When dragging stops. This could be any action that uses the grid's Drag and Drop service, e.g. Column Moving, Column Resizing, Range Selection, Fill Handle, etc.\n         */\n        this.dragStopped = new EventEmitter();\n        /** When dragging is cancelled stops. This is caused by pressing `Escape` while dragging elements within the grid that uses the grid's Drag and Drop service, e.g. Column Moving, Column Resizing, Range Selection, Fill Handle, etc.\n         */\n        this.dragCancelled = new EventEmitter();\n        /** Grid state has been updated.\n         */\n        this.stateUpdated = new EventEmitter();\n        /** Triggered every time the paging state changes. Some of the most common scenarios for this event to be triggered are:\n         *\n         *  - The page size changes.\n         *  - The current shown page is changed.\n         *  - New data is loaded onto the grid.\n         */\n        this.paginationChanged = new EventEmitter();\n        /** A drag has started, or dragging was already started and the mouse has re-entered the grid having previously left the grid.\n         */\n        this.rowDragEnter = new EventEmitter();\n        /** The mouse has moved while dragging.\n         */\n        this.rowDragMove = new EventEmitter();\n        /** The mouse has left the grid while dragging.\n         */\n        this.rowDragLeave = new EventEmitter();\n        /** The drag has finished over the grid.\n         */\n        this.rowDragEnd = new EventEmitter();\n        /** The drag has been cancelled over the grid.\n         */\n        this.rowDragCancel = new EventEmitter();\n        /** The row resize has started (Row Numbers Feature)\n         */\n        this.rowResizeStarted = new EventEmitter();\n        /** The row resize has ended (Row Numbers Feature)\n         */\n        this.rowResizeEnded = new EventEmitter();\n        /** A row group column was added, removed or reordered.\n         */\n        this.columnRowGroupChanged = new EventEmitter();\n        /** A row group was opened or closed.\n         */\n        this.rowGroupOpened = new EventEmitter();\n        /** Fired when calling either of the API methods `expandAll()` or `collapseAll()`.\n         */\n        this.expandOrCollapseAll = new EventEmitter();\n        /** Exceeded the `pivotMaxGeneratedColumns` limit when generating columns.\n         */\n        this.pivotMaxColumnsExceeded = new EventEmitter();\n        /** The client has set new pinned row data into the grid.\n         */\n        this.pinnedRowDataChanged = new EventEmitter();\n        /** A row has been pinned to top or bottom, or unpinned.\n         */\n        this.pinnedRowsChanged = new EventEmitter();\n        /** Client-Side Row Model only. The client has updated data for the grid by either a) setting new Row Data or b) Applying a Row Transaction.\n         */\n        this.rowDataUpdated = new EventEmitter();\n        /** Async transactions have been applied. Contains a list of all transaction results.\n         */\n        this.asyncTransactionsFlushed = new EventEmitter();\n        /** A server side store has finished refreshing.\n         */\n        this.storeRefreshed = new EventEmitter();\n        /** Header is focused.\n         */\n        this.headerFocused = new EventEmitter();\n        /** Cell is clicked.\n         */\n        this.cellClicked = new EventEmitter();\n        /** Cell is double clicked.\n         */\n        this.cellDoubleClicked = new EventEmitter();\n        /** Cell is focused.\n         */\n        this.cellFocused = new EventEmitter();\n        /** Mouse entered cell.\n         */\n        this.cellMouseOver = new EventEmitter();\n        /** Mouse left cell.\n         */\n        this.cellMouseOut = new EventEmitter();\n        /** Mouse down on cell.\n         */\n        this.cellMouseDown = new EventEmitter();\n        /** Row is clicked.\n         */\n        this.rowClicked = new EventEmitter();\n        /** Row is double clicked.\n         */\n        this.rowDoubleClicked = new EventEmitter();\n        /** Row is selected or deselected. The event contains the node in question, so call the node's `isSelected()` method to see if it was just selected or deselected.\n         */\n        this.rowSelected = new EventEmitter();\n        /** Row selection is changed. Use the `selectedNodes` field to get the list of selected nodes at the time of the event. When using the SSRM, `selectedNodes` will be `null`\n         * when selecting all nodes. Instead, refer to the `serverSideState` field.\n         */\n        this.selectionChanged = new EventEmitter();\n        /** Cell is right clicked.\n         */\n        this.cellContextMenu = new EventEmitter();\n        /** A change to range selection has occurred.\n         *\n         * @deprecated v32.2 Use `onCellSelectionChanged` instead\n         */\n        this.rangeSelectionChanged = new EventEmitter();\n        /** A change to cell selection has occurred.\n         */\n        this.cellSelectionChanged = new EventEmitter();\n        /** A tooltip has been displayed     */\n        this.tooltipShow = new EventEmitter();\n        /** A tooltip was hidden     */\n        this.tooltipHide = new EventEmitter();\n        /** Sort has changed. The grid also listens for this and updates the model.\n         */\n        this.sortChanged = new EventEmitter();\n        this._nativeElement = elementDef.nativeElement;\n        this._fullyReady.then(() => {\n            // Register the status flag reset before any events are fired\n            // so that we can swap to synchronous event firing as soon as the grid is ready\n            this._holdEvents = false;\n        });\n    }\n    ngAfterViewInit() {\n        // Run the setup outside of angular so all the event handlers that are created do not trigger change detection\n        this._angularFrameworkOverrides.runOutsideAngular(() => {\n            this._frameworkCompWrapper.setViewContainerRef(this._viewContainerRef, this._angularFrameworkOverrides);\n            // Get all the inputs that are valid GridOptions\n            const gridOptionKeys = Object.keys(this).filter((key) => !(key.startsWith('_') ||\n                key == 'gridOptions' ||\n                key == 'modules' ||\n                this[key] instanceof EventEmitter));\n            const coercedGridOptions = {};\n            gridOptionKeys.forEach((key) => {\n                const valueToUse = getValueOrCoercedValue(key, this[key]);\n                coercedGridOptions[key] = valueToUse;\n            });\n            const mergedGridOps = _combineAttributesAndGridOptions(this.gridOptions, coercedGridOptions, gridOptionKeys);\n            const gridParams = {\n                globalListener: this.globalListener.bind(this),\n                frameworkOverrides: this._angularFrameworkOverrides,\n                providedBeanInstances: {\n                    frameworkCompWrapper: this._frameworkCompWrapper,\n                },\n                modules: (this.modules || []),\n                setThemeOnGridDiv: true,\n            };\n            const api = createGrid(this._nativeElement, mergedGridOps, gridParams);\n            if (api) {\n                this.api = api;\n            }\n            this._initialised = true;\n            // sometimes, especially in large client apps gridReady can fire before ngAfterViewInit\n            // this ties these together so that gridReady will always fire after agGridAngular's ngAfterViewInit\n            // the actual containing component's ngAfterViewInit will fire just after agGridAngular's\n            this._resolveFullyReady();\n        });\n    }\n    ngOnChanges(changes) {\n        if (this._initialised) {\n            // Run the changes outside of angular so any event handlers that are created do not trigger change detection\n            this._angularFrameworkOverrides.runOutsideAngular(() => {\n                const gridOptions = {};\n                for (const key of Object.keys(changes)) {\n                    const value = changes[key];\n                    gridOptions[key] = value.currentValue;\n                }\n                _processOnChange(gridOptions, this.api);\n            });\n        }\n    }\n    ngOnDestroy() {\n        if (this._initialised) {\n            // need to do this before the destroy, so we know not to emit any events\n            // while tearing down the grid.\n            this._destroyed = true;\n            // could be null if grid failed to initialise\n            this.api?.destroy();\n        }\n    }\n    // we'll emit the emit if a user is listening for a given event either on the component via normal angular binding\n    // or via gridOptions\n    isEmitterUsed(eventType) {\n        const emitter = this[eventType];\n        // For RxJs compatibility we need to check for observed v7+ or observers v6\n        const emitterAny = emitter;\n        const hasEmitter = emitterAny?.observed ?? emitterAny?.observers?.length > 0;\n        // gridReady => onGridReady\n        const asEventName = `on${eventType.charAt(0).toUpperCase()}${eventType.substring(1)}`;\n        const hasGridOptionListener = !!this.gridOptions && !!this.gridOptions[asEventName];\n        return hasEmitter || hasGridOptionListener;\n    }\n    globalListener(eventType, event) {\n        // if we are tearing down, don't emit angular events, as this causes\n        // problems with the angular router\n        if (this._destroyed) {\n            return;\n        }\n        // generically look up the eventType\n        const emitter = this[eventType];\n        if (emitter && this.isEmitterUsed(eventType)) {\n            // Make sure we emit within the angular zone, so change detection works properly\n            const fireEmitter = () => this._angularFrameworkOverrides.runInsideAngular(() => emitter.emit(event));\n            if (this._holdEvents) {\n                // if the user is listening to events, wait for ngAfterViewInit to fire first, then emit the grid events\n                this._fullyReady.then(() => fireEmitter());\n            }\n            else {\n                fireEmitter();\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AgGridAngular, deps: [{ token: i0.ElementRef }, { token: i0.ViewContainerRef }, { token: AngularFrameworkOverrides }, { token: AngularFrameworkComponentWrapper }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.3.12\", type: AgGridAngular, isStandalone: true, selector: \"ag-grid-angular\", inputs: { gridOptions: \"gridOptions\", modules: \"modules\", statusBar: \"statusBar\", sideBar: \"sideBar\", suppressContextMenu: [\"suppressContextMenu\", \"suppressContextMenu\", booleanAttribute], preventDefaultOnContextMenu: [\"preventDefaultOnContextMenu\", \"preventDefaultOnContextMenu\", booleanAttribute], allowContextMenuWithControlKey: [\"allowContextMenuWithControlKey\", \"allowContextMenuWithControlKey\", booleanAttribute], columnMenu: \"columnMenu\", suppressMenuHide: [\"suppressMenuHide\", \"suppressMenuHide\", booleanAttribute], enableBrowserTooltips: [\"enableBrowserTooltips\", \"enableBrowserTooltips\", booleanAttribute], tooltipTrigger: \"tooltipTrigger\", tooltipShowDelay: \"tooltipShowDelay\", tooltipHideDelay: \"tooltipHideDelay\", tooltipMouseTrack: [\"tooltipMouseTrack\", \"tooltipMouseTrack\", booleanAttribute], tooltipShowMode: \"tooltipShowMode\", tooltipInteraction: [\"tooltipInteraction\", \"tooltipInteraction\", booleanAttribute], popupParent: \"popupParent\", copyHeadersToClipboard: [\"copyHeadersToClipboard\", \"copyHeadersToClipboard\", booleanAttribute], copyGroupHeadersToClipboard: [\"copyGroupHeadersToClipboard\", \"copyGroupHeadersToClipboard\", booleanAttribute], clipboardDelimiter: \"clipboardDelimiter\", suppressCopyRowsToClipboard: [\"suppressCopyRowsToClipboard\", \"suppressCopyRowsToClipboard\", booleanAttribute], suppressCopySingleCellRanges: [\"suppressCopySingleCellRanges\", \"suppressCopySingleCellRanges\", booleanAttribute], suppressLastEmptyLineOnPaste: [\"suppressLastEmptyLineOnPaste\", \"suppressLastEmptyLineOnPaste\", booleanAttribute], suppressClipboardPaste: [\"suppressClipboardPaste\", \"suppressClipboardPaste\", booleanAttribute], suppressClipboardApi: [\"suppressClipboardApi\", \"suppressClipboardApi\", booleanAttribute], suppressCutToClipboard: [\"suppressCutToClipboard\", \"suppressCutToClipboard\", booleanAttribute], columnDefs: \"columnDefs\", defaultColDef: \"defaultColDef\", defaultColGroupDef: \"defaultColGroupDef\", columnTypes: \"columnTypes\", dataTypeDefinitions: \"dataTypeDefinitions\", maintainColumnOrder: [\"maintainColumnOrder\", \"maintainColumnOrder\", booleanAttribute], enableStrictPivotColumnOrder: [\"enableStrictPivotColumnOrder\", \"enableStrictPivotColumnOrder\", booleanAttribute], suppressFieldDotNotation: [\"suppressFieldDotNotation\", \"suppressFieldDotNotation\", booleanAttribute], headerHeight: \"headerHeight\", groupHeaderHeight: \"groupHeaderHeight\", floatingFiltersHeight: \"floatingFiltersHeight\", pivotHeaderHeight: \"pivotHeaderHeight\", pivotGroupHeaderHeight: \"pivotGroupHeaderHeight\", allowDragFromColumnsToolPanel: [\"allowDragFromColumnsToolPanel\", \"allowDragFromColumnsToolPanel\", booleanAttribute], suppressMovableColumns: [\"suppressMovableColumns\", \"suppressMovableColumns\", booleanAttribute], suppressColumnMoveAnimation: [\"suppressColumnMoveAnimation\", \"suppressColumnMoveAnimation\", booleanAttribute], suppressMoveWhenColumnDragging: [\"suppressMoveWhenColumnDragging\", \"suppressMoveWhenColumnDragging\", booleanAttribute], suppressDragLeaveHidesColumns: [\"suppressDragLeaveHidesColumns\", \"suppressDragLeaveHidesColumns\", booleanAttribute], suppressGroupChangesColumnVisibility: \"suppressGroupChangesColumnVisibility\", suppressMakeColumnVisibleAfterUnGroup: [\"suppressMakeColumnVisibleAfterUnGroup\", \"suppressMakeColumnVisibleAfterUnGroup\", booleanAttribute], suppressRowGroupHidesColumns: [\"suppressRowGroupHidesColumns\", \"suppressRowGroupHidesColumns\", booleanAttribute], colResizeDefault: \"colResizeDefault\", suppressAutoSize: [\"suppressAutoSize\", \"suppressAutoSize\", booleanAttribute], autoSizePadding: \"autoSizePadding\", skipHeaderOnAutoSize: [\"skipHeaderOnAutoSize\", \"skipHeaderOnAutoSize\", booleanAttribute], autoSizeStrategy: \"autoSizeStrategy\", components: \"components\", editType: \"editType\", singleClickEdit: [\"singleClickEdit\", \"singleClickEdit\", booleanAttribute], suppressClickEdit: [\"suppressClickEdit\", \"suppressClickEdit\", booleanAttribute], readOnlyEdit: [\"readOnlyEdit\", \"readOnlyEdit\", booleanAttribute], stopEditingWhenCellsLoseFocus: [\"stopEditingWhenCellsLoseFocus\", \"stopEditingWhenCellsLoseFocus\", booleanAttribute], enterNavigatesVertically: [\"enterNavigatesVertically\", \"enterNavigatesVertically\", booleanAttribute], enterNavigatesVerticallyAfterEdit: [\"enterNavigatesVerticallyAfterEdit\", \"enterNavigatesVerticallyAfterEdit\", booleanAttribute], enableCellEditingOnBackspace: [\"enableCellEditingOnBackspace\", \"enableCellEditingOnBackspace\", booleanAttribute], undoRedoCellEditing: [\"undoRedoCellEditing\", \"undoRedoCellEditing\", booleanAttribute], undoRedoCellEditingLimit: \"undoRedoCellEditingLimit\", defaultCsvExportParams: \"defaultCsvExportParams\", suppressCsvExport: [\"suppressCsvExport\", \"suppressCsvExport\", booleanAttribute], defaultExcelExportParams: \"defaultExcelExportParams\", suppressExcelExport: [\"suppressExcelExport\", \"suppressExcelExport\", booleanAttribute], excelStyles: \"excelStyles\", findSearchValue: \"findSearchValue\", findOptions: \"findOptions\", quickFilterText: \"quickFilterText\", cacheQuickFilter: [\"cacheQuickFilter\", \"cacheQuickFilter\", booleanAttribute], includeHiddenColumnsInQuickFilter: [\"includeHiddenColumnsInQuickFilter\", \"includeHiddenColumnsInQuickFilter\", booleanAttribute], quickFilterParser: \"quickFilterParser\", quickFilterMatcher: \"quickFilterMatcher\", applyQuickFilterBeforePivotOrAgg: [\"applyQuickFilterBeforePivotOrAgg\", \"applyQuickFilterBeforePivotOrAgg\", booleanAttribute], excludeChildrenWhenTreeDataFiltering: [\"excludeChildrenWhenTreeDataFiltering\", \"excludeChildrenWhenTreeDataFiltering\", booleanAttribute], enableAdvancedFilter: [\"enableAdvancedFilter\", \"enableAdvancedFilter\", booleanAttribute], alwaysPassFilter: \"alwaysPassFilter\", includeHiddenColumnsInAdvancedFilter: [\"includeHiddenColumnsInAdvancedFilter\", \"includeHiddenColumnsInAdvancedFilter\", booleanAttribute], advancedFilterParent: \"advancedFilterParent\", advancedFilterBuilderParams: \"advancedFilterBuilderParams\", suppressAdvancedFilterEval: [\"suppressAdvancedFilterEval\", \"suppressAdvancedFilterEval\", booleanAttribute], suppressSetFilterByDefault: [\"suppressSetFilterByDefault\", \"suppressSetFilterByDefault\", booleanAttribute], enableCharts: [\"enableCharts\", \"enableCharts\", booleanAttribute], chartThemes: \"chartThemes\", customChartThemes: \"customChartThemes\", chartThemeOverrides: \"chartThemeOverrides\", chartToolPanelsDef: \"chartToolPanelsDef\", chartMenuItems: \"chartMenuItems\", loadingCellRenderer: \"loadingCellRenderer\", loadingCellRendererParams: \"loadingCellRendererParams\", loadingCellRendererSelector: \"loadingCellRendererSelector\", localeText: \"localeText\", masterDetail: [\"masterDetail\", \"masterDetail\", booleanAttribute], keepDetailRows: [\"keepDetailRows\", \"keepDetailRows\", booleanAttribute], keepDetailRowsCount: \"keepDetailRowsCount\", detailCellRenderer: \"detailCellRenderer\", detailCellRendererParams: \"detailCellRendererParams\", detailRowHeight: \"detailRowHeight\", detailRowAutoHeight: [\"detailRowAutoHeight\", \"detailRowAutoHeight\", booleanAttribute], context: \"context\", alignedGrids: \"alignedGrids\", tabIndex: \"tabIndex\", rowBuffer: \"rowBuffer\", valueCache: [\"valueCache\", \"valueCache\", booleanAttribute], valueCacheNeverExpires: [\"valueCacheNeverExpires\", \"valueCacheNeverExpires\", booleanAttribute], enableCellExpressions: [\"enableCellExpressions\", \"enableCellExpressions\", booleanAttribute], suppressTouch: [\"suppressTouch\", \"suppressTouch\", booleanAttribute], suppressFocusAfterRefresh: [\"suppressFocusAfterRefresh\", \"suppressFocusAfterRefresh\", booleanAttribute], suppressBrowserResizeObserver: [\"suppressBrowserResizeObserver\", \"suppressBrowserResizeObserver\", booleanAttribute], suppressPropertyNamesCheck: [\"suppressPropertyNamesCheck\", \"suppressPropertyNamesCheck\", booleanAttribute], suppressChangeDetection: [\"suppressChangeDetection\", \"suppressChangeDetection\", booleanAttribute], debug: [\"debug\", \"debug\", booleanAttribute], loading: [\"loading\", \"loading\", booleanAttribute], overlayLoadingTemplate: \"overlayLoadingTemplate\", loadingOverlayComponent: \"loadingOverlayComponent\", loadingOverlayComponentParams: \"loadingOverlayComponentParams\", suppressLoadingOverlay: [\"suppressLoadingOverlay\", \"suppressLoadingOverlay\", booleanAttribute], overlayNoRowsTemplate: \"overlayNoRowsTemplate\", noRowsOverlayComponent: \"noRowsOverlayComponent\", noRowsOverlayComponentParams: \"noRowsOverlayComponentParams\", suppressNoRowsOverlay: [\"suppressNoRowsOverlay\", \"suppressNoRowsOverlay\", booleanAttribute], pagination: [\"pagination\", \"pagination\", booleanAttribute], paginationPageSize: \"paginationPageSize\", paginationPageSizeSelector: \"paginationPageSizeSelector\", paginationAutoPageSize: [\"paginationAutoPageSize\", \"paginationAutoPageSize\", booleanAttribute], paginateChildRows: [\"paginateChildRows\", \"paginateChildRows\", booleanAttribute], suppressPaginationPanel: [\"suppressPaginationPanel\", \"suppressPaginationPanel\", booleanAttribute], pivotMode: [\"pivotMode\", \"pivotMode\", booleanAttribute], pivotPanelShow: \"pivotPanelShow\", pivotMaxGeneratedColumns: \"pivotMaxGeneratedColumns\", pivotDefaultExpanded: \"pivotDefaultExpanded\", pivotColumnGroupTotals: \"pivotColumnGroupTotals\", pivotRowTotals: \"pivotRowTotals\", pivotSuppressAutoColumn: [\"pivotSuppressAutoColumn\", \"pivotSuppressAutoColumn\", booleanAttribute], suppressExpandablePivotGroups: [\"suppressExpandablePivotGroups\", \"suppressExpandablePivotGroups\", booleanAttribute], functionsReadOnly: [\"functionsReadOnly\", \"functionsReadOnly\", booleanAttribute], aggFuncs: \"aggFuncs\", suppressAggFuncInHeader: [\"suppressAggFuncInHeader\", \"suppressAggFuncInHeader\", booleanAttribute], alwaysAggregateAtRootLevel: [\"alwaysAggregateAtRootLevel\", \"alwaysAggregateAtRootLevel\", booleanAttribute], aggregateOnlyChangedColumns: [\"aggregateOnlyChangedColumns\", \"aggregateOnlyChangedColumns\", booleanAttribute], suppressAggFilteredOnly: [\"suppressAggFilteredOnly\", \"suppressAggFilteredOnly\", booleanAttribute], removePivotHeaderRowWhenSingleValueColumn: [\"removePivotHeaderRowWhenSingleValueColumn\", \"removePivotHeaderRowWhenSingleValueColumn\", booleanAttribute], animateRows: [\"animateRows\", \"animateRows\", booleanAttribute], cellFlashDuration: \"cellFlashDuration\", cellFadeDuration: \"cellFadeDuration\", allowShowChangeAfterFilter: [\"allowShowChangeAfterFilter\", \"allowShowChangeAfterFilter\", booleanAttribute], domLayout: \"domLayout\", ensureDomOrder: [\"ensureDomOrder\", \"ensureDomOrder\", booleanAttribute], enableCellSpan: [\"enableCellSpan\", \"enableCellSpan\", booleanAttribute], enableRtl: [\"enableRtl\", \"enableRtl\", booleanAttribute], suppressColumnVirtualisation: [\"suppressColumnVirtualisation\", \"suppressColumnVirtualisation\", booleanAttribute], suppressMaxRenderedRowRestriction: [\"suppressMaxRenderedRowRestriction\", \"suppressMaxRenderedRowRestriction\", booleanAttribute], suppressRowVirtualisation: [\"suppressRowVirtualisation\", \"suppressRowVirtualisation\", booleanAttribute], rowDragManaged: [\"rowDragManaged\", \"rowDragManaged\", booleanAttribute], suppressRowDrag: [\"suppressRowDrag\", \"suppressRowDrag\", booleanAttribute], suppressMoveWhenRowDragging: [\"suppressMoveWhenRowDragging\", \"suppressMoveWhenRowDragging\", booleanAttribute], rowDragEntireRow: [\"rowDragEntireRow\", \"rowDragEntireRow\", booleanAttribute], rowDragMultiRow: [\"rowDragMultiRow\", \"rowDragMultiRow\", booleanAttribute], rowDragText: \"rowDragText\", dragAndDropImageComponent: \"dragAndDropImageComponent\", dragAndDropImageComponentParams: \"dragAndDropImageComponentParams\", fullWidthCellRenderer: \"fullWidthCellRenderer\", fullWidthCellRendererParams: \"fullWidthCellRendererParams\", embedFullWidthRows: [\"embedFullWidthRows\", \"embedFullWidthRows\", booleanAttribute], groupDisplayType: \"groupDisplayType\", groupDefaultExpanded: \"groupDefaultExpanded\", autoGroupColumnDef: \"autoGroupColumnDef\", groupMaintainOrder: [\"groupMaintainOrder\", \"groupMaintainOrder\", booleanAttribute], groupSelectsChildren: [\"groupSelectsChildren\", \"groupSelectsChildren\", booleanAttribute], groupLockGroupColumns: \"groupLockGroupColumns\", groupAggFiltering: \"groupAggFiltering\", groupTotalRow: \"groupTotalRow\", grandTotalRow: \"grandTotalRow\", suppressStickyTotalRow: \"suppressStickyTotalRow\", groupSuppressBlankHeader: [\"groupSuppressBlankHeader\", \"groupSuppressBlankHeader\", booleanAttribute], groupSelectsFiltered: [\"groupSelectsFiltered\", \"groupSelectsFiltered\", booleanAttribute], showOpenedGroup: [\"showOpenedGroup\", \"showOpenedGroup\", booleanAttribute], groupHideParentOfSingleChild: \"groupHideParentOfSingleChild\", groupRemoveSingleChildren: [\"groupRemoveSingleChildren\", \"groupRemoveSingleChildren\", booleanAttribute], groupRemoveLowestSingleChildren: [\"groupRemoveLowestSingleChildren\", \"groupRemoveLowestSingleChildren\", booleanAttribute], groupHideOpenParents: [\"groupHideOpenParents\", \"groupHideOpenParents\", booleanAttribute], groupAllowUnbalanced: [\"groupAllowUnbalanced\", \"groupAllowUnbalanced\", booleanAttribute], rowGroupPanelShow: \"rowGroupPanelShow\", groupRowRenderer: \"groupRowRenderer\", groupRowRendererParams: \"groupRowRendererParams\", treeData: [\"treeData\", \"treeData\", booleanAttribute], treeDataChildrenField: \"treeDataChildrenField\", treeDataParentIdField: \"treeDataParentIdField\", rowGroupPanelSuppressSort: [\"rowGroupPanelSuppressSort\", \"rowGroupPanelSuppressSort\", booleanAttribute], suppressGroupRowsSticky: [\"suppressGroupRowsSticky\", \"suppressGroupRowsSticky\", booleanAttribute], pinnedTopRowData: \"pinnedTopRowData\", pinnedBottomRowData: \"pinnedBottomRowData\", enableRowPinning: \"enableRowPinning\", isRowPinnable: \"isRowPinnable\", isRowPinned: \"isRowPinned\", rowModelType: \"rowModelType\", rowData: \"rowData\", asyncTransactionWaitMillis: \"asyncTransactionWaitMillis\", suppressModelUpdateAfterUpdateTransaction: [\"suppressModelUpdateAfterUpdateTransaction\", \"suppressModelUpdateAfterUpdateTransaction\", booleanAttribute], datasource: \"datasource\", cacheOverflowSize: \"cacheOverflowSize\", infiniteInitialRowCount: \"infiniteInitialRowCount\", serverSideInitialRowCount: \"serverSideInitialRowCount\", suppressServerSideFullWidthLoadingRow: [\"suppressServerSideFullWidthLoadingRow\", \"suppressServerSideFullWidthLoadingRow\", booleanAttribute], cacheBlockSize: \"cacheBlockSize\", maxBlocksInCache: \"maxBlocksInCache\", maxConcurrentDatasourceRequests: \"maxConcurrentDatasourceRequests\", blockLoadDebounceMillis: \"blockLoadDebounceMillis\", purgeClosedRowNodes: [\"purgeClosedRowNodes\", \"purgeClosedRowNodes\", booleanAttribute], serverSideDatasource: \"serverSideDatasource\", serverSideSortAllLevels: [\"serverSideSortAllLevels\", \"serverSideSortAllLevels\", booleanAttribute], serverSideEnableClientSideSort: [\"serverSideEnableClientSideSort\", \"serverSideEnableClientSideSort\", booleanAttribute], serverSideOnlyRefreshFilteredGroups: [\"serverSideOnlyRefreshFilteredGroups\", \"serverSideOnlyRefreshFilteredGroups\", booleanAttribute], serverSidePivotResultFieldSeparator: \"serverSidePivotResultFieldSeparator\", viewportDatasource: \"viewportDatasource\", viewportRowModelPageSize: \"viewportRowModelPageSize\", viewportRowModelBufferSize: \"viewportRowModelBufferSize\", alwaysShowHorizontalScroll: [\"alwaysShowHorizontalScroll\", \"alwaysShowHorizontalScroll\", booleanAttribute], alwaysShowVerticalScroll: [\"alwaysShowVerticalScroll\", \"alwaysShowVerticalScroll\", booleanAttribute], debounceVerticalScrollbar: [\"debounceVerticalScrollbar\", \"debounceVerticalScrollbar\", booleanAttribute], suppressHorizontalScroll: [\"suppressHorizontalScroll\", \"suppressHorizontalScroll\", booleanAttribute], suppressScrollOnNewData: [\"suppressScrollOnNewData\", \"suppressScrollOnNewData\", booleanAttribute], suppressScrollWhenPopupsAreOpen: [\"suppressScrollWhenPopupsAreOpen\", \"suppressScrollWhenPopupsAreOpen\", booleanAttribute], suppressAnimationFrame: [\"suppressAnimationFrame\", \"suppressAnimationFrame\", booleanAttribute], suppressMiddleClickScrolls: [\"suppressMiddleClickScrolls\", \"suppressMiddleClickScrolls\", booleanAttribute], suppressPreventDefaultOnMouseWheel: [\"suppressPreventDefaultOnMouseWheel\", \"suppressPreventDefaultOnMouseWheel\", booleanAttribute], scrollbarWidth: \"scrollbarWidth\", rowSelection: \"rowSelection\", cellSelection: \"cellSelection\", rowMultiSelectWithClick: [\"rowMultiSelectWithClick\", \"rowMultiSelectWithClick\", booleanAttribute], suppressRowDeselection: [\"suppressRowDeselection\", \"suppressRowDeselection\", booleanAttribute], suppressRowClickSelection: [\"suppressRowClickSelection\", \"suppressRowClickSelection\", booleanAttribute], suppressCellFocus: [\"suppressCellFocus\", \"suppressCellFocus\", booleanAttribute], suppressHeaderFocus: [\"suppressHeaderFocus\", \"suppressHeaderFocus\", booleanAttribute], selectionColumnDef: \"selectionColumnDef\", rowNumbers: \"rowNumbers\", suppressMultiRangeSelection: [\"suppressMultiRangeSelection\", \"suppressMultiRangeSelection\", booleanAttribute], enableCellTextSelection: [\"enableCellTextSelection\", \"enableCellTextSelection\", booleanAttribute], enableRangeSelection: [\"enableRangeSelection\", \"enableRangeSelection\", booleanAttribute], enableRangeHandle: [\"enableRangeHandle\", \"enableRangeHandle\", booleanAttribute], enableFillHandle: [\"enableFillHandle\", \"enableFillHandle\", booleanAttribute], fillHandleDirection: \"fillHandleDirection\", suppressClearOnFillReduction: [\"suppressClearOnFillReduction\", \"suppressClearOnFillReduction\", booleanAttribute], sortingOrder: \"sortingOrder\", accentedSort: [\"accentedSort\", \"accentedSort\", booleanAttribute], unSortIcon: [\"unSortIcon\", \"unSortIcon\", booleanAttribute], suppressMultiSort: [\"suppressMultiSort\", \"suppressMultiSort\", booleanAttribute], alwaysMultiSort: [\"alwaysMultiSort\", \"alwaysMultiSort\", booleanAttribute], multiSortKey: \"multiSortKey\", suppressMaintainUnsortedOrder: [\"suppressMaintainUnsortedOrder\", \"suppressMaintainUnsortedOrder\", booleanAttribute], icons: \"icons\", rowHeight: \"rowHeight\", rowStyle: \"rowStyle\", rowClass: \"rowClass\", rowClassRules: \"rowClassRules\", suppressRowHoverHighlight: [\"suppressRowHoverHighlight\", \"suppressRowHoverHighlight\", booleanAttribute], suppressRowTransform: [\"suppressRowTransform\", \"suppressRowTransform\", booleanAttribute], columnHoverHighlight: [\"columnHoverHighlight\", \"columnHoverHighlight\", booleanAttribute], gridId: \"gridId\", deltaSort: [\"deltaSort\", \"deltaSort\", booleanAttribute], treeDataDisplayType: \"treeDataDisplayType\", enableGroupEdit: [\"enableGroupEdit\", \"enableGroupEdit\", booleanAttribute], initialState: \"initialState\", theme: \"theme\", loadThemeGoogleFonts: [\"loadThemeGoogleFonts\", \"loadThemeGoogleFonts\", booleanAttribute], themeCssLayer: \"themeCssLayer\", styleNonce: \"styleNonce\", themeStyleContainer: \"themeStyleContainer\", getContextMenuItems: \"getContextMenuItems\", getMainMenuItems: \"getMainMenuItems\", postProcessPopup: \"postProcessPopup\", processUnpinnedColumns: \"processUnpinnedColumns\", processCellForClipboard: \"processCellForClipboard\", processHeaderForClipboard: \"processHeaderForClipboard\", processGroupHeaderForClipboard: \"processGroupHeaderForClipboard\", processCellFromClipboard: \"processCellFromClipboard\", sendToClipboard: \"sendToClipboard\", processDataFromClipboard: \"processDataFromClipboard\", isExternalFilterPresent: \"isExternalFilterPresent\", doesExternalFilterPass: \"doesExternalFilterPass\", getChartToolbarItems: \"getChartToolbarItems\", createChartContainer: \"createChartContainer\", focusGridInnerElement: \"focusGridInnerElement\", navigateToNextHeader: \"navigateToNextHeader\", tabToNextHeader: \"tabToNextHeader\", navigateToNextCell: \"navigateToNextCell\", tabToNextCell: \"tabToNextCell\", getLocaleText: \"getLocaleText\", getDocument: \"getDocument\", paginationNumberFormatter: \"paginationNumberFormatter\", getGroupRowAgg: \"getGroupRowAgg\", isGroupOpenByDefault: \"isGroupOpenByDefault\", initialGroupOrderComparator: \"initialGroupOrderComparator\", processPivotResultColDef: \"processPivotResultColDef\", processPivotResultColGroupDef: \"processPivotResultColGroupDef\", getDataPath: \"getDataPath\", getChildCount: \"getChildCount\", getServerSideGroupLevelParams: \"getServerSideGroupLevelParams\", isServerSideGroupOpenByDefault: \"isServerSideGroupOpenByDefault\", isApplyServerSideTransaction: \"isApplyServerSideTransaction\", isServerSideGroup: \"isServerSideGroup\", getServerSideGroupKey: \"getServerSideGroupKey\", getBusinessKeyForNode: \"getBusinessKeyForNode\", getRowId: \"getRowId\", resetRowDataOnUpdate: [\"resetRowDataOnUpdate\", \"resetRowDataOnUpdate\", booleanAttribute], processRowPostCreate: \"processRowPostCreate\", isRowSelectable: \"isRowSelectable\", isRowMaster: \"isRowMaster\", fillOperation: \"fillOperation\", postSortRows: \"postSortRows\", getRowStyle: \"getRowStyle\", getRowClass: \"getRowClass\", getRowHeight: \"getRowHeight\", isFullWidthRow: \"isFullWidthRow\" }, outputs: { toolPanelVisibleChanged: \"toolPanelVisibleChanged\", toolPanelSizeChanged: \"toolPanelSizeChanged\", columnMenuVisibleChanged: \"columnMenuVisibleChanged\", contextMenuVisibleChanged: \"contextMenuVisibleChanged\", cutStart: \"cutStart\", cutEnd: \"cutEnd\", pasteStart: \"pasteStart\", pasteEnd: \"pasteEnd\", columnVisible: \"columnVisible\", columnPinned: \"columnPinned\", columnResized: \"columnResized\", columnMoved: \"columnMoved\", columnValueChanged: \"columnValueChanged\", columnPivotModeChanged: \"columnPivotModeChanged\", columnPivotChanged: \"columnPivotChanged\", columnGroupOpened: \"columnGroupOpened\", newColumnsLoaded: \"newColumnsLoaded\", gridColumnsChanged: \"gridColumnsChanged\", displayedColumnsChanged: \"displayedColumnsChanged\", virtualColumnsChanged: \"virtualColumnsChanged\", columnEverythingChanged: \"columnEverythingChanged\", columnHeaderMouseOver: \"columnHeaderMouseOver\", columnHeaderMouseLeave: \"columnHeaderMouseLeave\", columnHeaderClicked: \"columnHeaderClicked\", columnHeaderContextMenu: \"columnHeaderContextMenu\", componentStateChanged: \"componentStateChanged\", cellValueChanged: \"cellValueChanged\", cellEditRequest: \"cellEditRequest\", rowValueChanged: \"rowValueChanged\", cellEditingStarted: \"cellEditingStarted\", cellEditingStopped: \"cellEditingStopped\", rowEditingStarted: \"rowEditingStarted\", rowEditingStopped: \"rowEditingStopped\", undoStarted: \"undoStarted\", undoEnded: \"undoEnded\", redoStarted: \"redoStarted\", redoEnded: \"redoEnded\", cellSelectionDeleteStart: \"cellSelectionDeleteStart\", cellSelectionDeleteEnd: \"cellSelectionDeleteEnd\", rangeDeleteStart: \"rangeDeleteStart\", rangeDeleteEnd: \"rangeDeleteEnd\", fillStart: \"fillStart\", fillEnd: \"fillEnd\", filterOpened: \"filterOpened\", filterChanged: \"filterChanged\", filterModified: \"filterModified\", advancedFilterBuilderVisibleChanged: \"advancedFilterBuilderVisibleChanged\", findChanged: \"findChanged\", chartCreated: \"chartCreated\", chartRangeSelectionChanged: \"chartRangeSelectionChanged\", chartOptionsChanged: \"chartOptionsChanged\", chartDestroyed: \"chartDestroyed\", cellKeyDown: \"cellKeyDown\", gridReady: \"gridReady\", firstDataRendered: \"firstDataRendered\", gridSizeChanged: \"gridSizeChanged\", modelUpdated: \"modelUpdated\", virtualRowRemoved: \"virtualRowRemoved\", viewportChanged: \"viewportChanged\", bodyScroll: \"bodyScroll\", bodyScrollEnd: \"bodyScrollEnd\", dragStarted: \"dragStarted\", dragStopped: \"dragStopped\", dragCancelled: \"dragCancelled\", stateUpdated: \"stateUpdated\", paginationChanged: \"paginationChanged\", rowDragEnter: \"rowDragEnter\", rowDragMove: \"rowDragMove\", rowDragLeave: \"rowDragLeave\", rowDragEnd: \"rowDragEnd\", rowDragCancel: \"rowDragCancel\", rowResizeStarted: \"rowResizeStarted\", rowResizeEnded: \"rowResizeEnded\", columnRowGroupChanged: \"columnRowGroupChanged\", rowGroupOpened: \"rowGroupOpened\", expandOrCollapseAll: \"expandOrCollapseAll\", pivotMaxColumnsExceeded: \"pivotMaxColumnsExceeded\", pinnedRowDataChanged: \"pinnedRowDataChanged\", pinnedRowsChanged: \"pinnedRowsChanged\", rowDataUpdated: \"rowDataUpdated\", asyncTransactionsFlushed: \"asyncTransactionsFlushed\", storeRefreshed: \"storeRefreshed\", headerFocused: \"headerFocused\", cellClicked: \"cellClicked\", cellDoubleClicked: \"cellDoubleClicked\", cellFocused: \"cellFocused\", cellMouseOver: \"cellMouseOver\", cellMouseOut: \"cellMouseOut\", cellMouseDown: \"cellMouseDown\", rowClicked: \"rowClicked\", rowDoubleClicked: \"rowDoubleClicked\", rowSelected: \"rowSelected\", selectionChanged: \"selectionChanged\", cellContextMenu: \"cellContextMenu\", rangeSelectionChanged: \"rangeSelectionChanged\", cellSelectionChanged: \"cellSelectionChanged\", tooltipShow: \"tooltipShow\", tooltipHide: \"tooltipHide\", sortChanged: \"sortChanged\" }, providers: [AngularFrameworkOverrides, AngularFrameworkComponentWrapper], usesOnChanges: true, ngImport: i0, template: '', isInline: true, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AgGridAngular, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'ag-grid-angular',\n                    standalone: true,\n                    template: '',\n                    providers: [AngularFrameworkOverrides, AngularFrameworkComponentWrapper],\n                    // tell angular we don't want view encapsulation, we don't want a shadow root\n                    encapsulation: ViewEncapsulation.None,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ViewContainerRef }, { type: AngularFrameworkOverrides }, { type: AngularFrameworkComponentWrapper }], propDecorators: { gridOptions: [{\n                type: Input\n            }], modules: [{\n                type: Input\n            }], statusBar: [{\n                type: Input\n            }], sideBar: [{\n                type: Input\n            }], suppressContextMenu: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], preventDefaultOnContextMenu: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], allowContextMenuWithControlKey: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], columnMenu: [{\n                type: Input\n            }], suppressMenuHide: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], enableBrowserTooltips: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tooltipTrigger: [{\n                type: Input\n            }], tooltipShowDelay: [{\n                type: Input\n            }], tooltipHideDelay: [{\n                type: Input\n            }], tooltipMouseTrack: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tooltipShowMode: [{\n                type: Input\n            }], tooltipInteraction: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], popupParent: [{\n                type: Input\n            }], copyHeadersToClipboard: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], copyGroupHeadersToClipboard: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], clipboardDelimiter: [{\n                type: Input\n            }], suppressCopyRowsToClipboard: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressCopySingleCellRanges: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressLastEmptyLineOnPaste: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressClipboardPaste: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressClipboardApi: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressCutToClipboard: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], columnDefs: [{\n                type: Input\n            }], defaultColDef: [{\n                type: Input\n            }], defaultColGroupDef: [{\n                type: Input\n            }], columnTypes: [{\n                type: Input\n            }], dataTypeDefinitions: [{\n                type: Input\n            }], maintainColumnOrder: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], enableStrictPivotColumnOrder: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressFieldDotNotation: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], headerHeight: [{\n                type: Input\n            }], groupHeaderHeight: [{\n                type: Input\n            }], floatingFiltersHeight: [{\n                type: Input\n            }], pivotHeaderHeight: [{\n                type: Input\n            }], pivotGroupHeaderHeight: [{\n                type: Input\n            }], allowDragFromColumnsToolPanel: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressMovableColumns: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressColumnMoveAnimation: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressMoveWhenColumnDragging: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressDragLeaveHidesColumns: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressGroupChangesColumnVisibility: [{\n                type: Input\n            }], suppressMakeColumnVisibleAfterUnGroup: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressRowGroupHidesColumns: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], colResizeDefault: [{\n                type: Input\n            }], suppressAutoSize: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], autoSizePadding: [{\n                type: Input\n            }], skipHeaderOnAutoSize: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], autoSizeStrategy: [{\n                type: Input\n            }], components: [{\n                type: Input\n            }], editType: [{\n                type: Input\n            }], singleClickEdit: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressClickEdit: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], readOnlyEdit: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], stopEditingWhenCellsLoseFocus: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], enterNavigatesVertically: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], enterNavigatesVerticallyAfterEdit: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], enableCellEditingOnBackspace: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], undoRedoCellEditing: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], undoRedoCellEditingLimit: [{\n                type: Input\n            }], defaultCsvExportParams: [{\n                type: Input\n            }], suppressCsvExport: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], defaultExcelExportParams: [{\n                type: Input\n            }], suppressExcelExport: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], excelStyles: [{\n                type: Input\n            }], findSearchValue: [{\n                type: Input\n            }], findOptions: [{\n                type: Input\n            }], quickFilterText: [{\n                type: Input\n            }], cacheQuickFilter: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], includeHiddenColumnsInQuickFilter: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], quickFilterParser: [{\n                type: Input\n            }], quickFilterMatcher: [{\n                type: Input\n            }], applyQuickFilterBeforePivotOrAgg: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], excludeChildrenWhenTreeDataFiltering: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], enableAdvancedFilter: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], alwaysPassFilter: [{\n                type: Input\n            }], includeHiddenColumnsInAdvancedFilter: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], advancedFilterParent: [{\n                type: Input\n            }], advancedFilterBuilderParams: [{\n                type: Input\n            }], suppressAdvancedFilterEval: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressSetFilterByDefault: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], enableCharts: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], chartThemes: [{\n                type: Input\n            }], customChartThemes: [{\n                type: Input\n            }], chartThemeOverrides: [{\n                type: Input\n            }], chartToolPanelsDef: [{\n                type: Input\n            }], chartMenuItems: [{\n                type: Input\n            }], loadingCellRenderer: [{\n                type: Input\n            }], loadingCellRendererParams: [{\n                type: Input\n            }], loadingCellRendererSelector: [{\n                type: Input\n            }], localeText: [{\n                type: Input\n            }], masterDetail: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], keepDetailRows: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], keepDetailRowsCount: [{\n                type: Input\n            }], detailCellRenderer: [{\n                type: Input\n            }], detailCellRendererParams: [{\n                type: Input\n            }], detailRowHeight: [{\n                type: Input\n            }], detailRowAutoHeight: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], context: [{\n                type: Input\n            }], alignedGrids: [{\n                type: Input\n            }], tabIndex: [{\n                type: Input\n            }], rowBuffer: [{\n                type: Input\n            }], valueCache: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], valueCacheNeverExpires: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], enableCellExpressions: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressTouch: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressFocusAfterRefresh: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressBrowserResizeObserver: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressPropertyNamesCheck: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressChangeDetection: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], debug: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], loading: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], overlayLoadingTemplate: [{\n                type: Input\n            }], loadingOverlayComponent: [{\n                type: Input\n            }], loadingOverlayComponentParams: [{\n                type: Input\n            }], suppressLoadingOverlay: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], overlayNoRowsTemplate: [{\n                type: Input\n            }], noRowsOverlayComponent: [{\n                type: Input\n            }], noRowsOverlayComponentParams: [{\n                type: Input\n            }], suppressNoRowsOverlay: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], pagination: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], paginationPageSize: [{\n                type: Input\n            }], paginationPageSizeSelector: [{\n                type: Input\n            }], paginationAutoPageSize: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], paginateChildRows: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressPaginationPanel: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], pivotMode: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], pivotPanelShow: [{\n                type: Input\n            }], pivotMaxGeneratedColumns: [{\n                type: Input\n            }], pivotDefaultExpanded: [{\n                type: Input\n            }], pivotColumnGroupTotals: [{\n                type: Input\n            }], pivotRowTotals: [{\n                type: Input\n            }], pivotSuppressAutoColumn: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressExpandablePivotGroups: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], functionsReadOnly: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], aggFuncs: [{\n                type: Input\n            }], suppressAggFuncInHeader: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], alwaysAggregateAtRootLevel: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], aggregateOnlyChangedColumns: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressAggFilteredOnly: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], removePivotHeaderRowWhenSingleValueColumn: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], animateRows: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], cellFlashDuration: [{\n                type: Input\n            }], cellFadeDuration: [{\n                type: Input\n            }], allowShowChangeAfterFilter: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], domLayout: [{\n                type: Input\n            }], ensureDomOrder: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], enableCellSpan: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], enableRtl: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressColumnVirtualisation: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressMaxRenderedRowRestriction: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressRowVirtualisation: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], rowDragManaged: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressRowDrag: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressMoveWhenRowDragging: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], rowDragEntireRow: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], rowDragMultiRow: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], rowDragText: [{\n                type: Input\n            }], dragAndDropImageComponent: [{\n                type: Input\n            }], dragAndDropImageComponentParams: [{\n                type: Input\n            }], fullWidthCellRenderer: [{\n                type: Input\n            }], fullWidthCellRendererParams: [{\n                type: Input\n            }], embedFullWidthRows: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], groupDisplayType: [{\n                type: Input\n            }], groupDefaultExpanded: [{\n                type: Input\n            }], autoGroupColumnDef: [{\n                type: Input\n            }], groupMaintainOrder: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], groupSelectsChildren: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], groupLockGroupColumns: [{\n                type: Input\n            }], groupAggFiltering: [{\n                type: Input\n            }], groupTotalRow: [{\n                type: Input\n            }], grandTotalRow: [{\n                type: Input\n            }], suppressStickyTotalRow: [{\n                type: Input\n            }], groupSuppressBlankHeader: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], groupSelectsFiltered: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], showOpenedGroup: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], groupHideParentOfSingleChild: [{\n                type: Input\n            }], groupRemoveSingleChildren: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], groupRemoveLowestSingleChildren: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], groupHideOpenParents: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], groupAllowUnbalanced: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], rowGroupPanelShow: [{\n                type: Input\n            }], groupRowRenderer: [{\n                type: Input\n            }], groupRowRendererParams: [{\n                type: Input\n            }], treeData: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], treeDataChildrenField: [{\n                type: Input\n            }], treeDataParentIdField: [{\n                type: Input\n            }], rowGroupPanelSuppressSort: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressGroupRowsSticky: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], pinnedTopRowData: [{\n                type: Input\n            }], pinnedBottomRowData: [{\n                type: Input\n            }], enableRowPinning: [{\n                type: Input\n            }], isRowPinnable: [{\n                type: Input\n            }], isRowPinned: [{\n                type: Input\n            }], rowModelType: [{\n                type: Input\n            }], rowData: [{\n                type: Input\n            }], asyncTransactionWaitMillis: [{\n                type: Input\n            }], suppressModelUpdateAfterUpdateTransaction: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], datasource: [{\n                type: Input\n            }], cacheOverflowSize: [{\n                type: Input\n            }], infiniteInitialRowCount: [{\n                type: Input\n            }], serverSideInitialRowCount: [{\n                type: Input\n            }], suppressServerSideFullWidthLoadingRow: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], cacheBlockSize: [{\n                type: Input\n            }], maxBlocksInCache: [{\n                type: Input\n            }], maxConcurrentDatasourceRequests: [{\n                type: Input\n            }], blockLoadDebounceMillis: [{\n                type: Input\n            }], purgeClosedRowNodes: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], serverSideDatasource: [{\n                type: Input\n            }], serverSideSortAllLevels: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], serverSideEnableClientSideSort: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], serverSideOnlyRefreshFilteredGroups: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], serverSidePivotResultFieldSeparator: [{\n                type: Input\n            }], viewportDatasource: [{\n                type: Input\n            }], viewportRowModelPageSize: [{\n                type: Input\n            }], viewportRowModelBufferSize: [{\n                type: Input\n            }], alwaysShowHorizontalScroll: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], alwaysShowVerticalScroll: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], debounceVerticalScrollbar: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressHorizontalScroll: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressScrollOnNewData: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressScrollWhenPopupsAreOpen: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressAnimationFrame: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressMiddleClickScrolls: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressPreventDefaultOnMouseWheel: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], scrollbarWidth: [{\n                type: Input\n            }], rowSelection: [{\n                type: Input\n            }], cellSelection: [{\n                type: Input\n            }], rowMultiSelectWithClick: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressRowDeselection: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressRowClickSelection: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressCellFocus: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressHeaderFocus: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectionColumnDef: [{\n                type: Input\n            }], rowNumbers: [{\n                type: Input\n            }], suppressMultiRangeSelection: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], enableCellTextSelection: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], enableRangeSelection: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], enableRangeHandle: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], enableFillHandle: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], fillHandleDirection: [{\n                type: Input\n            }], suppressClearOnFillReduction: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], sortingOrder: [{\n                type: Input\n            }], accentedSort: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], unSortIcon: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressMultiSort: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], alwaysMultiSort: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], multiSortKey: [{\n                type: Input\n            }], suppressMaintainUnsortedOrder: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], icons: [{\n                type: Input\n            }], rowHeight: [{\n                type: Input\n            }], rowStyle: [{\n                type: Input\n            }], rowClass: [{\n                type: Input\n            }], rowClassRules: [{\n                type: Input\n            }], suppressRowHoverHighlight: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], suppressRowTransform: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], columnHoverHighlight: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], gridId: [{\n                type: Input\n            }], deltaSort: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], treeDataDisplayType: [{\n                type: Input\n            }], enableGroupEdit: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], initialState: [{\n                type: Input\n            }], theme: [{\n                type: Input\n            }], loadThemeGoogleFonts: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], themeCssLayer: [{\n                type: Input\n            }], styleNonce: [{\n                type: Input\n            }], themeStyleContainer: [{\n                type: Input\n            }], getContextMenuItems: [{\n                type: Input\n            }], getMainMenuItems: [{\n                type: Input\n            }], postProcessPopup: [{\n                type: Input\n            }], processUnpinnedColumns: [{\n                type: Input\n            }], processCellForClipboard: [{\n                type: Input\n            }], processHeaderForClipboard: [{\n                type: Input\n            }], processGroupHeaderForClipboard: [{\n                type: Input\n            }], processCellFromClipboard: [{\n                type: Input\n            }], sendToClipboard: [{\n                type: Input\n            }], processDataFromClipboard: [{\n                type: Input\n            }], isExternalFilterPresent: [{\n                type: Input\n            }], doesExternalFilterPass: [{\n                type: Input\n            }], getChartToolbarItems: [{\n                type: Input\n            }], createChartContainer: [{\n                type: Input\n            }], focusGridInnerElement: [{\n                type: Input\n            }], navigateToNextHeader: [{\n                type: Input\n            }], tabToNextHeader: [{\n                type: Input\n            }], navigateToNextCell: [{\n                type: Input\n            }], tabToNextCell: [{\n                type: Input\n            }], getLocaleText: [{\n                type: Input\n            }], getDocument: [{\n                type: Input\n            }], paginationNumberFormatter: [{\n                type: Input\n            }], getGroupRowAgg: [{\n                type: Input\n            }], isGroupOpenByDefault: [{\n                type: Input\n            }], initialGroupOrderComparator: [{\n                type: Input\n            }], processPivotResultColDef: [{\n                type: Input\n            }], processPivotResultColGroupDef: [{\n                type: Input\n            }], getDataPath: [{\n                type: Input\n            }], getChildCount: [{\n                type: Input\n            }], getServerSideGroupLevelParams: [{\n                type: Input\n            }], isServerSideGroupOpenByDefault: [{\n                type: Input\n            }], isApplyServerSideTransaction: [{\n                type: Input\n            }], isServerSideGroup: [{\n                type: Input\n            }], getServerSideGroupKey: [{\n                type: Input\n            }], getBusinessKeyForNode: [{\n                type: Input\n            }], getRowId: [{\n                type: Input\n            }], resetRowDataOnUpdate: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], processRowPostCreate: [{\n                type: Input\n            }], isRowSelectable: [{\n                type: Input\n            }], isRowMaster: [{\n                type: Input\n            }], fillOperation: [{\n                type: Input\n            }], postSortRows: [{\n                type: Input\n            }], getRowStyle: [{\n                type: Input\n            }], getRowClass: [{\n                type: Input\n            }], getRowHeight: [{\n                type: Input\n            }], isFullWidthRow: [{\n                type: Input\n            }], toolPanelVisibleChanged: [{\n                type: Output\n            }], toolPanelSizeChanged: [{\n                type: Output\n            }], columnMenuVisibleChanged: [{\n                type: Output\n            }], contextMenuVisibleChanged: [{\n                type: Output\n            }], cutStart: [{\n                type: Output\n            }], cutEnd: [{\n                type: Output\n            }], pasteStart: [{\n                type: Output\n            }], pasteEnd: [{\n                type: Output\n            }], columnVisible: [{\n                type: Output\n            }], columnPinned: [{\n                type: Output\n            }], columnResized: [{\n                type: Output\n            }], columnMoved: [{\n                type: Output\n            }], columnValueChanged: [{\n                type: Output\n            }], columnPivotModeChanged: [{\n                type: Output\n            }], columnPivotChanged: [{\n                type: Output\n            }], columnGroupOpened: [{\n                type: Output\n            }], newColumnsLoaded: [{\n                type: Output\n            }], gridColumnsChanged: [{\n                type: Output\n            }], displayedColumnsChanged: [{\n                type: Output\n            }], virtualColumnsChanged: [{\n                type: Output\n            }], columnEverythingChanged: [{\n                type: Output\n            }], columnHeaderMouseOver: [{\n                type: Output\n            }], columnHeaderMouseLeave: [{\n                type: Output\n            }], columnHeaderClicked: [{\n                type: Output\n            }], columnHeaderContextMenu: [{\n                type: Output\n            }], componentStateChanged: [{\n                type: Output\n            }], cellValueChanged: [{\n                type: Output\n            }], cellEditRequest: [{\n                type: Output\n            }], rowValueChanged: [{\n                type: Output\n            }], cellEditingStarted: [{\n                type: Output\n            }], cellEditingStopped: [{\n                type: Output\n            }], rowEditingStarted: [{\n                type: Output\n            }], rowEditingStopped: [{\n                type: Output\n            }], undoStarted: [{\n                type: Output\n            }], undoEnded: [{\n                type: Output\n            }], redoStarted: [{\n                type: Output\n            }], redoEnded: [{\n                type: Output\n            }], cellSelectionDeleteStart: [{\n                type: Output\n            }], cellSelectionDeleteEnd: [{\n                type: Output\n            }], rangeDeleteStart: [{\n                type: Output\n            }], rangeDeleteEnd: [{\n                type: Output\n            }], fillStart: [{\n                type: Output\n            }], fillEnd: [{\n                type: Output\n            }], filterOpened: [{\n                type: Output\n            }], filterChanged: [{\n                type: Output\n            }], filterModified: [{\n                type: Output\n            }], advancedFilterBuilderVisibleChanged: [{\n                type: Output\n            }], findChanged: [{\n                type: Output\n            }], chartCreated: [{\n                type: Output\n            }], chartRangeSelectionChanged: [{\n                type: Output\n            }], chartOptionsChanged: [{\n                type: Output\n            }], chartDestroyed: [{\n                type: Output\n            }], cellKeyDown: [{\n                type: Output\n            }], gridReady: [{\n                type: Output\n            }], firstDataRendered: [{\n                type: Output\n            }], gridSizeChanged: [{\n                type: Output\n            }], modelUpdated: [{\n                type: Output\n            }], virtualRowRemoved: [{\n                type: Output\n            }], viewportChanged: [{\n                type: Output\n            }], bodyScroll: [{\n                type: Output\n            }], bodyScrollEnd: [{\n                type: Output\n            }], dragStarted: [{\n                type: Output\n            }], dragStopped: [{\n                type: Output\n            }], dragCancelled: [{\n                type: Output\n            }], stateUpdated: [{\n                type: Output\n            }], paginationChanged: [{\n                type: Output\n            }], rowDragEnter: [{\n                type: Output\n            }], rowDragMove: [{\n                type: Output\n            }], rowDragLeave: [{\n                type: Output\n            }], rowDragEnd: [{\n                type: Output\n            }], rowDragCancel: [{\n                type: Output\n            }], rowResizeStarted: [{\n                type: Output\n            }], rowResizeEnded: [{\n                type: Output\n            }], columnRowGroupChanged: [{\n                type: Output\n            }], rowGroupOpened: [{\n                type: Output\n            }], expandOrCollapseAll: [{\n                type: Output\n            }], pivotMaxColumnsExceeded: [{\n                type: Output\n            }], pinnedRowDataChanged: [{\n                type: Output\n            }], pinnedRowsChanged: [{\n                type: Output\n            }], rowDataUpdated: [{\n                type: Output\n            }], asyncTransactionsFlushed: [{\n                type: Output\n            }], storeRefreshed: [{\n                type: Output\n            }], headerFocused: [{\n                type: Output\n            }], cellClicked: [{\n                type: Output\n            }], cellDoubleClicked: [{\n                type: Output\n            }], cellFocused: [{\n                type: Output\n            }], cellMouseOver: [{\n                type: Output\n            }], cellMouseOut: [{\n                type: Output\n            }], cellMouseDown: [{\n                type: Output\n            }], rowClicked: [{\n                type: Output\n            }], rowDoubleClicked: [{\n                type: Output\n            }], rowSelected: [{\n                type: Output\n            }], selectionChanged: [{\n                type: Output\n            }], cellContextMenu: [{\n                type: Output\n            }], rangeSelectionChanged: [{\n                type: Output\n            }], cellSelectionChanged: [{\n                type: Output\n            }], tooltipShow: [{\n                type: Output\n            }], tooltipHide: [{\n                type: Output\n            }], sortChanged: [{\n                type: Output\n            }] } });\nconst booleanMixedGridOptions = new Set(_BOOLEAN_MIXED_GRID_OPTIONS);\n/**\n * Used to support the user setting combined boolean and string / object properties\n * as plain HTML attributes and us correctly mapping that to true.\n * For example cellSection can be boolean or an object\n */\nfunction getValueOrCoercedValue(key, valueToUse) {\n    if (booleanMixedGridOptions.has(key)) {\n        // Handle plain HTML boolean attributes and convert them to boolean values\n        // Also handle the false string case to match Angular boolean attributes\n        return valueToUse === '' ? true : valueToUse === 'false' ? false : valueToUse;\n    }\n    return valueToUse;\n}\n\nclass AgGridModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AgGridModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.12\", ngImport: i0, type: AgGridModule, imports: [AgGridAngular], exports: [AgGridAngular] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AgGridModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: AgGridModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [AgGridAngular],\n                    exports: [AgGridAngular],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AgComponentContainer, AgGridAngular, AgGridModule, AngularFrameworkComponentWrapper, AngularFrameworkOverrides };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACnK,SAASC,iBAAiB,EAAEC,oBAAoB,EAAEC,yBAAyB,EAAEC,gCAAgC,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,2BAA2B,QAAQ,mBAAmB;;AAEnM;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,GAAG,GAAGpB,MAAM,CAACC,gBAAgB,CAAC;EACvC;EACA;IAAS,IAAI,CAACoB,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFL,oBAAoB;IAAA,CAAmD;EAAE;EACpL;IAAS,IAAI,CAACM,IAAI,kBAD+EzB,EAAE,CAAA0B,iBAAA;MAAAC,IAAA,EACJR,oBAAoB;MAAAS,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAAC,aAAA;IAAA,EAAmF;EAAE;AAC5M;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHqGpC,EAAE,CAAAqC,iBAAA,CAGXlB,oBAAoB,EAAc,CAAC;IACnHQ,IAAI,EAAExB,SAAS;IACfmC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClCR,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMS,UAAU,GAAG,EAAE;AACrB,IAAIC,QAAQ,GAAG,CAAC;AAChB,SAASC,yBAAyBA,CAACrB,GAAG,EAAE;EACpC,MAAMsB,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,UAAU,EAAEK,CAAC,EAAE,EAAE;IACjC,MAAMC,SAAS,GAAGzB,GAAG,CAAC0B,eAAe,CAAC5B,oBAAoB,CAAC;IAC3DwB,YAAY,CAACK,GAAG,CAACH,CAAC,EAAEC,SAAS,CAAC;IAC9BlC,iBAAiB,CAACkC,SAAS,CAACG,QAAQ,CAACC,aAAa,CAAC;EACvD;EACA,OAAOP,YAAY;AACvB;AACA;AACA;AACA;AACA;AACA,MAAMQ,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;AACvE,MAAMC,gCAAgC,SAASxC,oBAAoB,CAAC;EAChEyC,mBAAmBA,CAACC,gBAAgB,EAAEC,yBAAyB,EAAE;IAC7D,IAAI,CAACD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;EAC9D;EACAC,aAAaA,CAACC,mBAAmB,EAAE;IAC/B,MAAMF,yBAAyB,GAAG,IAAI,CAACA,yBAAyB;IAChE,MAAMG,IAAI,GAAG,IAAI;IACjBA,IAAI,CAACC,UAAU,KAAKlB,yBAAyB,CAAC,IAAI,CAACa,gBAAgB,CAAC;IACpE,MAAMM,qBAAqB,SAASC,gBAAgB,CAAC;MACjDC,IAAIA,CAACC,MAAM,EAAE;QACTR,yBAAyB,CAACS,gBAAgB,CAAC,MAAM;UAC7C,KAAK,CAACF,IAAI,CAACC,MAAM,CAAC;UAClB,IAAI,CAACE,aAAa,CAACC,iBAAiB,CAACC,aAAa,CAAC,CAAC;QACxD,CAAC,CAAC;MACN;MACArB,eAAeA,CAAA,EAAG;QACd,OAAOY,IAAI,CAACZ,eAAe,CAACW,mBAAmB,CAAC;MACpD;MACAW,SAASA,CAACC,IAAI,EAAE;QACZ,OAAOC,OAAO,CAACC,6BAA6B,CAAC,CAAC,CAACF,IAAI,CAAC,IAAI,IAAI;MAChE;MACAG,UAAUA,CAACH,IAAI,EAAEhC,IAAI,EAAE;QACnB,MAAMoC,YAAY,GAAG,IAAI,CAACF,6BAA6B,CAAC,CAAC;QACzD,MAAMG,UAAU,GAAGD,YAAY,CAACJ,IAAI,CAAC;QACrC,IAAInB,iBAAiB,CAACyB,GAAG,CAACN,IAAI,CAAC,EAAE;UAC7B,OAAOK,UAAU,CAACE,KAAK,CAACH,YAAY,EAAEpC,IAAI,CAAC;QAC/C;QACA,OAAOkB,yBAAyB,CAACS,gBAAgB,CAAC,MAAMU,UAAU,CAACE,KAAK,CAACH,YAAY,EAAEpC,IAAI,CAAC,CAAC;MACjG;MACAwC,SAASA,CAACR,IAAI,EAAES,QAAQ,EAAE;QACtBR,OAAO,CAACD,IAAI,CAAC,GAAGS,QAAQ;MAC5B;IACJ;IACA,MAAMR,OAAO,GAAG,IAAIV,qBAAqB,CAAC,CAAC;IAC3C,OAAOU,OAAO;EAClB;EACAxB,eAAeA,CAACiC,aAAa,EAAE;IAC3BvC,QAAQ,GAAG,CAACA,QAAQ,GAAG,CAAC,IAAID,UAAU;IACtC,MAAMM,SAAS,GAAG,IAAI,CAACc,UAAU,CAACqB,GAAG,CAACxC,QAAQ,CAAC;IAC/C,OAAOK,SAAS,CAACoC,QAAQ,CAAC7D,GAAG,CAAC0B,eAAe,CAACiC,aAAa,CAAC;EAChE;EACA;IAAS,IAAI,CAAC1D,IAAI;MAAA,IAAA6D,6CAAA;MAAA,gBAAAC,yCAAA5D,CAAA;QAAA,QAAA2D,6CAAA,KAAAA,6CAAA,GApE+EnF,EAAE,CAAAqF,qBAAA,CAoEQhC,gCAAgC,IAAA7B,CAAA,IAAhC6B,gCAAgC;MAAA;IAAA,IAAsD;EAAE;EACnM;IAAS,IAAI,CAACiC,KAAK,kBArE8EtF,EAAE,CAAAuF,kBAAA;MAAAC,KAAA,EAqEYnC,gCAAgC;MAAAoC,OAAA,EAAhCpC,gCAAgC,CAAA/B;IAAA,EAAG;EAAE;AACxJ;AACA;EAAA,QAAAc,SAAA,oBAAAA,SAAA,KAvEqGpC,EAAE,CAAAqC,iBAAA,CAuEXgB,gCAAgC,EAAc,CAAC;IAC/H1B,IAAI,EAAEvB;EACV,CAAC,CAAC;AAAA;AACV,MAAM0D,gBAAgB,CAAC;EACnBC,IAAIA,CAACC,MAAM,EAAE;IACT,IAAI,CAAC0B,OAAO,GAAG1B,MAAM;IACrB,IAAI,CAACE,aAAa,GAAG,IAAI,CAACnB,eAAe,CAAC,CAAC;IAC3C,IAAI,CAAC4C,iBAAiB,GAAG,IAAI,CAACzB,aAAa,CAACgB,QAAQ;IACpD,IAAI,CAACU,2BAA2B,GAAG,IAAI,CAAC1B,aAAa,CAACgB,QAAQ;IAC9D,IAAI,CAACW,KAAK,GAAG,IAAI,CAAC3B,aAAa,CAACjB,QAAQ,CAACC,aAAa;IACtD;IACAtC,iBAAiB,CAAC,IAAI,CAACiF,KAAK,CAAC;IAC7B,IAAI,CAACF,iBAAiB,CAACG,MAAM,CAAC,IAAI,CAACJ,OAAO,CAAC;EAC/C;EACAK,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACF,KAAK;EACrB;EACA;EACAG,cAAcA,CAAA,EAAG;IACb,MAAMC,UAAU,GAAG,IAAI,CAACJ,KAAK,CAACI,UAAU;IACxC,OAAOA,UAAU;EACrB;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACN,2BAA2B,IAAI,OAAO,IAAI,CAACA,2BAA2B,CAACM,OAAO,KAAK,UAAU,EAAE;MACpG,IAAI,CAACN,2BAA2B,CAACM,OAAO,CAAC,CAAC;IAC9C;IACA,IAAI,CAAChC,aAAa,EAAEgC,OAAO,CAAC,CAAC;EACjC;EACA1B,6BAA6BA,CAAA,EAAG;IAC5B,OAAO,IAAI,CAACoB,2BAA2B;EAC3C;AACJ;AAEA,MAAMO,oCAAoC,CAAC;EACvC/E,WAAWA,CAACgF,kBAAkB,EAAE;IAC5B,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;IAC5C;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAIzD,GAAG,CAAC,CAAC;IACjC,IAAI,CAAC0D,sBAAsB,GAAG,IAAI1D,GAAG,CAAC,CAAC;EAC3C;EACA2D,IAAIA,CAACC,SAAS,EAAEC,YAAY,EAAE;IAC1B,MAAM;MAAEL,kBAAkB;MAAEC;IAAiB,CAAC,GAAG,IAAI;IACrD,IAAIK,QAAQ,GAAGD,YAAY;IAC3B,IAAIL,kBAAkB,CAACO,kBAAkB,EAAE;MACvCD,QAAQ,GAAIE,KAAK,IAAK;QAClBR,kBAAkB,CAACS,YAAY,CAAC,MAAMJ,YAAY,CAACG,KAAK,CAAC,CAAC;MAC9D,CAAC;MACD,IAAIE,cAAc,GAAGT,gBAAgB,CAACpB,GAAG,CAACuB,SAAS,CAAC;MACpD,IAAI,CAACM,cAAc,EAAE;QACjBA,cAAc,GAAG,IAAIlE,GAAG,CAAC,CAAC;QAC1ByD,gBAAgB,CAACrD,GAAG,CAACwD,SAAS,EAAEM,cAAc,CAAC;MACnD;MACAA,cAAc,CAAC9D,GAAG,CAACyD,YAAY,EAAEC,QAAQ,CAAC;IAC9C;IACA,OAAOA,QAAQ;EACnB;EACAK,UAAUA,CAACN,YAAY,EAAE;IACrB,MAAM;MAAEL,kBAAkB;MAAEE;IAAuB,CAAC,GAAG,IAAI;IAC3D,IAAII,QAAQ,GAAGD,YAAY;IAC3B,IAAIL,kBAAkB,CAACO,kBAAkB,EAAE;MACvCD,QAAQ,GAAGA,CAACF,SAAS,EAAEI,KAAK,KAAK;QAC7BR,kBAAkB,CAACS,YAAY,CAAC,MAAMJ,YAAY,CAACD,SAAS,EAAEI,KAAK,CAAC,CAAC;MACzE,CAAC;MACDN,sBAAsB,CAACtD,GAAG,CAACyD,YAAY,EAAEC,QAAQ,CAAC;IACtD;IACA,OAAOA,QAAQ;EACnB;EACAM,MAAMA,CAACR,SAAS,EAAEC,YAAY,EAAE;IAC5B,MAAM;MAAEJ;IAAiB,CAAC,GAAG,IAAI;IACjC,MAAMS,cAAc,GAAGT,gBAAgB,CAACpB,GAAG,CAACuB,SAAS,CAAC;IACtD,IAAIM,cAAc,EAAE;MAChB,MAAMG,OAAO,GAAGH,cAAc,CAAC7B,GAAG,CAACwB,YAAY,CAAC;MAChD,IAAIQ,OAAO,EAAE;QACTH,cAAc,CAACI,MAAM,CAACT,YAAY,CAAC;QACnC,IAAIK,cAAc,CAACK,IAAI,KAAK,CAAC,EAAE;UAC3Bd,gBAAgB,CAACa,MAAM,CAACV,SAAS,CAAC;QACtC;QACA,OAAOS,OAAO;MAClB;IACJ;IACA,OAAOR,YAAY;EACvB;EACAW,YAAYA,CAACX,YAAY,EAAE;IACvB,MAAM;MAAEH;IAAuB,CAAC,GAAG,IAAI;IACvC,MAAMW,OAAO,GAAGX,sBAAsB,CAACrB,GAAG,CAACwB,YAAY,CAAC;IACxD,IAAIQ,OAAO,EAAE;MACTX,sBAAsB,CAACY,MAAM,CAACT,YAAY,CAAC;MAC3C,OAAOQ,OAAO;IAClB;IACA,OAAOR,YAAY;EACvB;AACJ;AAEA,MAAMY,yBAAyB,SAASvG,yBAAyB,CAAC;EAC9DM,WAAWA,CAACkG,OAAO,EAAE;IACjB,KAAK,CAAC,SAAS,CAAC;IAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B;IACA,IAAI,CAACC,uBAAuB,GAAG,KAAK;IACpC;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,YAAY,GAAG,CAAC1C,QAAQ,EAAE2C,MAAM,KAAK,IAAI,CAACC,UAAU,CAAC5C,QAAQ,EAAE2C,MAAM,CAAC;IAC3E;AACR;AACA;AACA;IACQ,IAAI,CAACb,YAAY,GAAI9B,QAAQ,IAAK,IAAI,CAACd,gBAAgB,CAACc,QAAQ,CAAC;IACjE,IAAI,CAACyC,uBAAuB,GACxBI,MAAM,EAAEC,kBAAkB,IAAI,CAAC,CAACD,MAAM,EAAEE,IAAI,EAAEC,iBAAiB;IACnE,IAAI,CAAC,IAAI,CAACT,OAAO,EAAE;MACf,IAAI,CAACK,UAAU,GAAI5C,QAAQ,IAAKA,QAAQ,CAAC,CAAC;IAC9C,CAAC,MACI,IAAI,IAAI,CAACyC,uBAAuB,EAAE;MACnC,IAAI,CAACG,UAAU,GAAG,CAAC5C,QAAQ,EAAE2C,MAAM,KAAK;QACpC,IAAIA,MAAM,KAAK,iBAAiB,IAAIA,MAAM,KAAK,kBAAkB,EAAE;UAC/D;UACA;UACA,OAAO,IAAI,CAACJ,OAAO,CAACU,iBAAiB,CAACjD,QAAQ,CAAC;QACnD;QACA;QACA,OAAOA,QAAQ,CAAC,CAAC;MACrB,CAAC;IACL,CAAC,MACI;MACD,IAAI,CAAC4C,UAAU,GAAI5C,QAAQ,IAAK,IAAI,CAACuC,OAAO,CAACU,iBAAiB,CAACjD,QAAQ,CAAC;IAC5E;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI4B,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACW,OAAO,IAAIjH,MAAM,CAAC4H,eAAe,CAAC,CAAC;EACnD;EACAC,+BAA+BA,CAACC,qCAAqC,EAAEC,iBAAiB,EAAE;IACtF,IAAI,IAAI,CAACzB,kBAAkB,EAAE;MACzB,OAAQwB,qCAAqC,IACzC,CAAC,MAAM;QACHC,iBAAiB,CAACC,qBAAqB,CAAC,IAAI,CAAC;QAC7C,OAAO,IAAIlC,oCAAoC,CAAC,IAAI,CAAC;MACzD,CAAC,EAAE,CAAC;IACZ;IACA,OAAOmC,SAAS;EACpB;EACAC,gCAAgCA,CAAA,EAAG;IAC/B,OAAO,IAAIpC,oCAAoC,CAAC,IAAI,CAAC;EACzD;EACAqC,oBAAoBA,CAACC,IAAI,EAAE;IACvB,IAAI,CAACA,IAAI,EAAE;MACP,OAAO,KAAK;IAChB;IACA,MAAMC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAChC,OAAOA,SAAS,IAAI,QAAQ,IAAIA,SAAS;EAC7C;EACAzE,gBAAgBA,CAACc,QAAQ,EAAE;IACvB,IAAI,CAAC,IAAI,CAACuC,OAAO,IAAIjH,MAAM,CAAC4H,eAAe,CAAC,CAAC,EAAE;MAC3C,OAAOlD,QAAQ,CAAC,CAAC;IACrB;IACA;IACA,OAAO,IAAI,CAACuC,OAAO,CAACqB,GAAG,CAAC5D,QAAQ,CAAC;EACrC;EACAiD,iBAAiBA,CAACjD,QAAQ,EAAE2C,MAAM,EAAE;IAChC,OAAO,IAAI,CAACC,UAAU,CAAC5C,QAAQ,EAAE2C,MAAM,CAAC;EAC5C;EACA;IAAS,IAAI,CAACpG,IAAI,YAAAsH,kCAAApH,CAAA;MAAA,YAAAA,CAAA,IAAyF6F,yBAAyB,EAjPnCrH,EAAE,CAAA6I,QAAA,CAiPmD7I,EAAE,CAACK,MAAM;IAAA,CAA6C;EAAE;EAC9M;IAAS,IAAI,CAACiF,KAAK,kBAlP8EtF,EAAE,CAAAuF,kBAAA;MAAAC,KAAA,EAkPY6B,yBAAyB;MAAA5B,OAAA,EAAzB4B,yBAAyB,CAAA/F;IAAA,EAAG;EAAE;AACjJ;AACA;EAAA,QAAAc,SAAA,oBAAAA,SAAA,KApPqGpC,EAAE,CAAAqC,iBAAA,CAoPXgF,yBAAyB,EAAc,CAAC;IACxH1F,IAAI,EAAEvB;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEuB,IAAI,EAAE3B,EAAE,CAACK;EAAO,CAAC,CAAC;AAAA;;AAEvD;AACA;AACA,MAAMyI,aAAa,CAAC;EAChB1H,WAAWA,CAAC2H,UAAU,EAAEC,iBAAiB,EAAEC,0BAA0B,EAAEC,qBAAqB,EAAE;IAC1F,IAAI,CAACF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,0BAA0B,GAAGA,0BAA0B;IAC5D,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,WAAW,GAAG,IAAIC,OAAO,CAAEC,OAAO,IAAK;MACxC,IAAI,CAACC,kBAAkB,GAAGD,OAAO;IACrC,CAAC,CAAC;IACF;IACA;AACR;AACA;IACQ,IAAI,CAACE,SAAS,GAAGpB,SAAS;IAC1B;AACR;AACA;IACQ,IAAI,CAACqB,OAAO,GAAGrB,SAAS;IACxB;AACR;AACA;IACQ,IAAI,CAACsB,mBAAmB,GAAGtB,SAAS;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAACuB,2BAA2B,GAAGvB,SAAS;IAC5C;AACR;AACA;AACA;IACQ,IAAI,CAACwB,8BAA8B,GAAGxB,SAAS;IAC/C;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACyB,UAAU,GAAGzB,SAAS;IAC3B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC0B,gBAAgB,GAAG1B,SAAS;IACjC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC2B,qBAAqB,GAAG3B,SAAS;IACtC;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC4B,cAAc,GAAG5B,SAAS;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC6B,gBAAgB,GAAG7B,SAAS;IACjC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC8B,gBAAgB,GAAG9B,SAAS;IACjC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC+B,iBAAiB,GAAG/B,SAAS;IAClC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACgC,eAAe,GAAGhC,SAAS;IAChC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACiC,kBAAkB,GAAGjC,SAAS;IACnC;AACR;IACQ,IAAI,CAACkC,WAAW,GAAGlC,SAAS;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACmC,sBAAsB,GAAGnC,SAAS;IACvC;AACR;AACA;AACA;IACQ,IAAI,CAACoC,2BAA2B,GAAGpC,SAAS;IAC5C;AACR;AACA;AACA;IACQ,IAAI,CAACqC,kBAAkB,GAAGrC,SAAS;IACnC;AACR;AACA;AACA;IACQ,IAAI,CAACsC,2BAA2B,GAAGtC,SAAS;IAC5C;AACR;AACA;AACA;IACQ,IAAI,CAACuC,4BAA4B,GAAGvC,SAAS;IAC7C;AACR;AACA;AACA;IACQ,IAAI,CAACwC,4BAA4B,GAAGxC,SAAS;IAC7C;AACR;AACA;AACA;IACQ,IAAI,CAACyC,sBAAsB,GAAGzC,SAAS;IACvC;AACR;AACA;AACA;IACQ,IAAI,CAAC0C,oBAAoB,GAAG1C,SAAS;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAAC2C,sBAAsB,GAAG3C,SAAS;IACvC;AACR;IACQ,IAAI,CAAC4C,UAAU,GAAG5C,SAAS;IAC3B;AACR;IACQ,IAAI,CAAC6C,aAAa,GAAG7C,SAAS;IAC9B;AACR;AACA;IACQ,IAAI,CAAC8C,kBAAkB,GAAG9C,SAAS;IACnC;AACR;IACQ,IAAI,CAAC+C,WAAW,GAAG/C,SAAS;IAC5B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACgD,mBAAmB,GAAGhD,SAAS;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAACiD,mBAAmB,GAAGjD,SAAS;IACpC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACkD,4BAA4B,GAAGlD,SAAS;IAC7C;AACR;AACA;IACQ,IAAI,CAACmD,wBAAwB,GAAGnD,SAAS;IACzC;AACR;IACQ,IAAI,CAACoD,YAAY,GAAGpD,SAAS;IAC7B;AACR;IACQ,IAAI,CAACqD,iBAAiB,GAAGrD,SAAS;IAClC;AACR;IACQ,IAAI,CAACsD,qBAAqB,GAAGtD,SAAS;IACtC;AACR;IACQ,IAAI,CAACuD,iBAAiB,GAAGvD,SAAS;IAClC;AACR;IACQ,IAAI,CAACwD,sBAAsB,GAAGxD,SAAS;IACvC;AACR;AACA;AACA;IACQ,IAAI,CAACyD,6BAA6B,GAAGzD,SAAS;IAC9C;AACR;AACA;IACQ,IAAI,CAAC0D,sBAAsB,GAAG1D,SAAS;IACvC;AACR;AACA;IACQ,IAAI,CAAC2D,2BAA2B,GAAG3D,SAAS;IAC5C;AACR;AACA;IACQ,IAAI,CAAC4D,8BAA8B,GAAG5D,SAAS;IAC/C;AACR;AACA;IACQ,IAAI,CAAC6D,6BAA6B,GAAG7D,SAAS;IAC9C;AACR;AACA;IACQ,IAAI,CAAC8D,oCAAoC,GAAG9D,SAAS;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAAC+D,qCAAqC,GAAG/D,SAAS;IACtD;AACR;AACA;AACA;IACQ,IAAI,CAACgE,4BAA4B,GAAGhE,SAAS;IAC7C;AACR;IACQ,IAAI,CAACiE,gBAAgB,GAAGjE,SAAS;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACkE,gBAAgB,GAAGlE,SAAS;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACmE,eAAe,GAAGnE,SAAS;IAChC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACoE,oBAAoB,GAAGpE,SAAS;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACqE,gBAAgB,GAAGrE,SAAS;IACjC;AACR;AACA;IACQ,IAAI,CAACsE,UAAU,GAAGtE,SAAS;IAC3B;AACR;AACA;IACQ,IAAI,CAACuE,QAAQ,GAAGvE,SAAS;IACzB;AACR;AACA;AACA;IACQ,IAAI,CAACwE,eAAe,GAAGxE,SAAS;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAACyE,iBAAiB,GAAGzE,SAAS;IAClC;AACR;AACA;AACA;IACQ,IAAI,CAAC0E,YAAY,GAAG1E,SAAS;IAC7B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC2E,6BAA6B,GAAG3E,SAAS;IAC9C;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC4E,wBAAwB,GAAG5E,SAAS;IACzC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC6E,iCAAiC,GAAG7E,SAAS;IAClD;AACR;AACA;IACQ,IAAI,CAAC8E,4BAA4B,GAAG9E,SAAS;IAC7C;AACR;AACA;AACA;IACQ,IAAI,CAAC+E,mBAAmB,GAAG/E,SAAS;IACpC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACgF,wBAAwB,GAAGhF,SAAS;IACzC;AACR;AACA;IACQ,IAAI,CAACiF,sBAAsB,GAAGjF,SAAS;IACvC;AACR;AACA;IACQ,IAAI,CAACkF,iBAAiB,GAAGlF,SAAS;IAClC;AACR;AACA;IACQ,IAAI,CAACmF,wBAAwB,GAAGnF,SAAS;IACzC;AACR;AACA;IACQ,IAAI,CAACoF,mBAAmB,GAAGpF,SAAS;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAACqF,WAAW,GAAGrF,SAAS;IAC5B;AACR;AACA;IACQ,IAAI,CAACsF,eAAe,GAAGtF,SAAS;IAChC;AACR;AACA;IACQ,IAAI,CAACuF,WAAW,GAAGvF,SAAS;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACwF,eAAe,GAAGxF,SAAS;IAChC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACyF,gBAAgB,GAAGzF,SAAS;IACjC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC0F,iCAAiC,GAAG1F,SAAS;IAClD;AACR;AACA;IACQ,IAAI,CAAC2F,iBAAiB,GAAG3F,SAAS;IAClC;AACR;AACA;IACQ,IAAI,CAAC4F,kBAAkB,GAAG5F,SAAS;IACnC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC6F,gCAAgC,GAAG7F,SAAS;IACjD;AACR;AACA;AACA;IACQ,IAAI,CAAC8F,oCAAoC,GAAG9F,SAAS;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAAC+F,oBAAoB,GAAG/F,SAAS;IACrC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACgG,gBAAgB,GAAGhG,SAAS;IACjC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACiG,oCAAoC,GAAGjG,SAAS;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAACkG,oBAAoB,GAAGlG,SAAS;IACrC;AACR;AACA;IACQ,IAAI,CAACmG,2BAA2B,GAAGnG,SAAS;IAC5C;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACoG,0BAA0B,GAAGpG,SAAS;IAC3C;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACqG,0BAA0B,GAAGrG,SAAS;IAC3C;AACR;AACA;AACA;IACQ,IAAI,CAACsG,YAAY,GAAGtG,SAAS;IAC7B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACuG,WAAW,GAAGvG,SAAS;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACwG,iBAAiB,GAAGxG,SAAS;IAClC;AACR;AACA;AACA;IACQ,IAAI,CAACyG,mBAAmB,GAAGzG,SAAS;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAAC0G,kBAAkB,GAAG1G,SAAS;IACnC;AACR;AACA;IACQ,IAAI,CAAC2G,cAAc,GAAG3G,SAAS;IAC/B;AACR;AACA;IACQ,IAAI,CAAC4G,mBAAmB,GAAG5G,SAAS;IACpC;AACR;IACQ,IAAI,CAAC6G,yBAAyB,GAAG7G,SAAS;IAC1C;AACR;AACA;IACQ,IAAI,CAAC8G,2BAA2B,GAAG9G,SAAS;IAC5C;AACR;AACA;AACA;IACQ,IAAI,CAAC+G,UAAU,GAAG/G,SAAS;IAC3B;AACR;AACA;AACA;IACQ,IAAI,CAACgH,YAAY,GAAGhH,SAAS;IAC7B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACiH,cAAc,GAAGjH,SAAS;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACkH,mBAAmB,GAAGlH,SAAS;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAACmH,kBAAkB,GAAGnH,SAAS;IACnC;AACR;AACA;IACQ,IAAI,CAACoH,wBAAwB,GAAGpH,SAAS;IACzC;AACR;AACA;AACA;IACQ,IAAI,CAACqH,eAAe,GAAGrH,SAAS;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAACsH,mBAAmB,GAAGtH,SAAS;IACpC;AACR;AACA;IACQ,IAAI,CAACuH,OAAO,GAAGvH,SAAS;IACxB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACwH,YAAY,GAAGxH,SAAS;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAACyH,QAAQ,GAAGzH,SAAS;IACzB;AACR;AACA;AACA;IACQ,IAAI,CAAC0H,SAAS,GAAG1H,SAAS;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC2H,UAAU,GAAG3H,SAAS;IAC3B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC4H,sBAAsB,GAAG5H,SAAS;IACvC;AACR;AACA;AACA;IACQ,IAAI,CAAC6H,qBAAqB,GAAG7H,SAAS;IACtC;AACR;AACA;AACA;IACQ,IAAI,CAAC8H,aAAa,GAAG9H,SAAS;IAC9B;AACR;AACA;IACQ,IAAI,CAAC+H,yBAAyB,GAAG/H,SAAS;IAC1C;AACR;AACA;AACA;IACQ,IAAI,CAACgI,6BAA6B,GAAGhI,SAAS;IAC9C;AACR;AACA;AACA;IACQ,IAAI,CAACiI,0BAA0B,GAAGjI,SAAS;IAC3C;AACR;AACA;IACQ,IAAI,CAACkI,uBAAuB,GAAGlI,SAAS;IACxC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACmI,KAAK,GAAGnI,SAAS;IACtB;AACR;IACQ,IAAI,CAACoI,OAAO,GAAGpI,SAAS;IACxB;AACR;IACQ,IAAI,CAACqI,sBAAsB,GAAGrI,SAAS;IACvC;AACR;AACA;IACQ,IAAI,CAACsI,uBAAuB,GAAGtI,SAAS;IACxC;AACR;IACQ,IAAI,CAACuI,6BAA6B,GAAGvI,SAAS;IAC9C;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACwI,sBAAsB,GAAGxI,SAAS;IACvC;AACR;IACQ,IAAI,CAACyI,qBAAqB,GAAGzI,SAAS;IACtC;AACR;AACA;IACQ,IAAI,CAAC0I,sBAAsB,GAAG1I,SAAS;IACvC;AACR;IACQ,IAAI,CAAC2I,4BAA4B,GAAG3I,SAAS;IAC7C;AACR;AACA;AACA;IACQ,IAAI,CAAC4I,qBAAqB,GAAG5I,SAAS;IACtC;AACR;AACA;AACA;IACQ,IAAI,CAAC6I,UAAU,GAAG7I,SAAS;IAC3B;AACR;AACA;AACA;IACQ,IAAI,CAAC8I,kBAAkB,GAAG9I,SAAS;IACnC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC+I,0BAA0B,GAAG/I,SAAS;IAC3C;AACR;AACA;AACA;IACQ,IAAI,CAACgJ,sBAAsB,GAAGhJ,SAAS;IACvC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACiJ,iBAAiB,GAAGjJ,SAAS;IAClC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkJ,uBAAuB,GAAGlJ,SAAS;IACxC;AACR;AACA;AACA;IACQ,IAAI,CAACmJ,SAAS,GAAGnJ,SAAS;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACoJ,cAAc,GAAGpJ,SAAS;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACqJ,wBAAwB,GAAGrJ,SAAS;IACzC;AACR;AACA;AACA;IACQ,IAAI,CAACsJ,oBAAoB,GAAGtJ,SAAS;IACrC;AACR;AACA;IACQ,IAAI,CAACuJ,sBAAsB,GAAGvJ,SAAS;IACvC;AACR;AACA;IACQ,IAAI,CAACwJ,cAAc,GAAGxJ,SAAS;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACyJ,uBAAuB,GAAGzJ,SAAS;IACxC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC0J,6BAA6B,GAAG1J,SAAS;IAC9C;AACR;AACA;AACA;IACQ,IAAI,CAAC2J,iBAAiB,GAAG3J,SAAS;IAClC;AACR;AACA;AACA;IACQ,IAAI,CAAC4J,QAAQ,GAAG5J,SAAS;IACzB;AACR;AACA;AACA;IACQ,IAAI,CAAC6J,uBAAuB,GAAG7J,SAAS;IACxC;AACR;AACA;AACA;IACQ,IAAI,CAAC8J,0BAA0B,GAAG9J,SAAS;IAC3C;AACR;AACA;AACA;IACQ,IAAI,CAAC+J,2BAA2B,GAAG/J,SAAS;IAC5C;AACR;AACA;AACA;IACQ,IAAI,CAACgK,uBAAuB,GAAGhK,SAAS;IACxC;AACR;AACA;AACA;IACQ,IAAI,CAACiK,yCAAyC,GAAGjK,SAAS;IAC1D;AACR;AACA;IACQ,IAAI,CAACkK,WAAW,GAAGlK,SAAS;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACmK,iBAAiB,GAAGnK,SAAS;IAClC;AACR;AACA;IACQ,IAAI,CAACoK,gBAAgB,GAAGpK,SAAS;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACqK,0BAA0B,GAAGrK,SAAS;IAC3C;AACR;AACA;IACQ,IAAI,CAACsK,SAAS,GAAGtK,SAAS;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACuK,cAAc,GAAGvK,SAAS;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACwK,cAAc,GAAGxK,SAAS;IAC/B;AACR;AACA;AACA;IACQ,IAAI,CAACyK,SAAS,GAAGzK,SAAS;IAC1B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC0K,4BAA4B,GAAG1K,SAAS;IAC7C;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC2K,iCAAiC,GAAG3K,SAAS;IAClD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC4K,yBAAyB,GAAG5K,SAAS;IAC1C;AACR;AACA;AACA;IACQ,IAAI,CAAC6K,cAAc,GAAG7K,SAAS;IAC/B;AACR;AACA;IACQ,IAAI,CAAC8K,eAAe,GAAG9K,SAAS;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAAC+K,2BAA2B,GAAG/K,SAAS;IAC5C;AACR;AACA;AACA;IACQ,IAAI,CAACgL,gBAAgB,GAAGhL,SAAS;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACiL,eAAe,GAAGjL,SAAS;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkL,WAAW,GAAGlL,SAAS;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACmL,yBAAyB,GAAGnL,SAAS;IAC1C;AACR;AACA;IACQ,IAAI,CAACoL,+BAA+B,GAAGpL,SAAS;IAChD;AACR;AACA;IACQ,IAAI,CAACqL,qBAAqB,GAAGrL,SAAS;IACtC;AACR;IACQ,IAAI,CAACsL,2BAA2B,GAAGtL,SAAS;IAC5C;AACR;IACQ,IAAI,CAACuL,kBAAkB,GAAGvL,SAAS;IACnC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACwL,gBAAgB,GAAGxL,SAAS;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACyL,oBAAoB,GAAGzL,SAAS;IACrC;AACR;AACA;IACQ,IAAI,CAAC0L,kBAAkB,GAAG1L,SAAS;IACnC;AACR;AACA;AACA;IACQ,IAAI,CAAC2L,kBAAkB,GAAG3L,SAAS;IACnC;AACR;AACA;AACA;IACQ,IAAI,CAAC4L,oBAAoB,GAAG5L,SAAS;IACrC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC6L,qBAAqB,GAAG7L,SAAS;IACtC;AACR;AACA;AACA;IACQ,IAAI,CAAC8L,iBAAiB,GAAG9L,SAAS;IAClC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC+L,aAAa,GAAG/L,SAAS;IAC9B;AACR;AACA;AACA;IACQ,IAAI,CAACgM,aAAa,GAAGhM,SAAS;IAC9B;AACR;AACA;IACQ,IAAI,CAACiM,sBAAsB,GAAGjM,SAAS;IACvC;AACR;AACA;AACA;IACQ,IAAI,CAACkM,wBAAwB,GAAGlM,SAAS;IACzC;AACR;AACA;AACA;IACQ,IAAI,CAACmM,oBAAoB,GAAGnM,SAAS;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACoM,eAAe,GAAGpM,SAAS;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAACqM,4BAA4B,GAAGrM,SAAS;IAC7C;AACR;AACA;AACA;IACQ,IAAI,CAACsM,yBAAyB,GAAGtM,SAAS;IAC1C;AACR;AACA;AACA;IACQ,IAAI,CAACuM,+BAA+B,GAAGvM,SAAS;IAChD;AACR;AACA;AACA;IACQ,IAAI,CAACwM,oBAAoB,GAAGxM,SAAS;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACyM,oBAAoB,GAAGzM,SAAS;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAAC0M,iBAAiB,GAAG1M,SAAS;IAClC;AACR;AACA;AACA;IACQ,IAAI,CAAC2M,gBAAgB,GAAG3M,SAAS;IACjC;AACR;AACA;IACQ,IAAI,CAAC4M,sBAAsB,GAAG5M,SAAS;IACvC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC6M,QAAQ,GAAG7M,SAAS;IACzB;AACR;AACA;AACA;IACQ,IAAI,CAAC8M,qBAAqB,GAAG9M,SAAS;IACtC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC+M,qBAAqB,GAAG/M,SAAS;IACtC;AACR;AACA;AACA;IACQ,IAAI,CAACgN,yBAAyB,GAAGhN,SAAS;IAC1C;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACiN,uBAAuB,GAAGjN,SAAS;IACxC;AACR;AACA;IACQ,IAAI,CAACkN,gBAAgB,GAAGlN,SAAS;IACjC;AACR;AACA;IACQ,IAAI,CAACmN,mBAAmB,GAAGnN,SAAS;IACpC;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACoN,gBAAgB,GAAGpN,SAAS;IACjC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACqN,aAAa,GAAGrN,SAAS;IAC9B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACsN,WAAW,GAAGtN,SAAS;IAC5B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACuN,YAAY,GAAGvN,SAAS;IAC7B;AACR;AACA;IACQ,IAAI,CAACwN,OAAO,GAAGxN,SAAS;IACxB;AACR;IACQ,IAAI,CAACyN,0BAA0B,GAAGzN,SAAS;IAC3C;AACR;AACA;IACQ,IAAI,CAAC0N,yCAAyC,GAAG1N,SAAS;IAC1D;AACR;AACA;IACQ,IAAI,CAAC2N,UAAU,GAAG3N,SAAS;IAC3B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC4N,iBAAiB,GAAG5N,SAAS;IAClC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC6N,uBAAuB,GAAG7N,SAAS;IACxC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC8N,yBAAyB,GAAG9N,SAAS;IAC1C;AACR;AACA;IACQ,IAAI,CAAC+N,qCAAqC,GAAG/N,SAAS;IACtD;AACR;AACA;AACA;IACQ,IAAI,CAACgO,cAAc,GAAGhO,SAAS;IAC/B;AACR;AACA;AACA;IACQ,IAAI,CAACiO,gBAAgB,GAAGjO,SAAS;IACjC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkO,+BAA+B,GAAGlO,SAAS;IAChD;AACR;AACA;AACA;IACQ,IAAI,CAACmO,uBAAuB,GAAGnO,SAAS;IACxC;AACR;AACA;AACA;IACQ,IAAI,CAACoO,mBAAmB,GAAGpO,SAAS;IACpC;AACR;AACA;IACQ,IAAI,CAACqO,oBAAoB,GAAGrO,SAAS;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACsO,uBAAuB,GAAGtO,SAAS;IACxC;AACR;AACA;AACA;IACQ,IAAI,CAACuO,8BAA8B,GAAGvO,SAAS;IAC/C;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACwO,mCAAmC,GAAGxO,SAAS;IACpD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACyO,mCAAmC,GAAGzO,SAAS;IACpD;AACR;AACA;IACQ,IAAI,CAAC0O,kBAAkB,GAAG1O,SAAS;IACnC;AACR;AACA;AACA;IACQ,IAAI,CAAC2O,wBAAwB,GAAG3O,SAAS;IACzC;AACR;AACA;AACA;IACQ,IAAI,CAAC4O,0BAA0B,GAAG5O,SAAS;IAC3C;AACR;AACA;IACQ,IAAI,CAAC6O,0BAA0B,GAAG7O,SAAS;IAC3C;AACR;AACA;IACQ,IAAI,CAAC8O,wBAAwB,GAAG9O,SAAS;IACzC;AACR;AACA;AACA;IACQ,IAAI,CAAC+O,yBAAyB,GAAG/O,SAAS;IAC1C;AACR;AACA;IACQ,IAAI,CAACgP,wBAAwB,GAAGhP,SAAS;IACzC;AACR;AACA;IACQ,IAAI,CAACiP,uBAAuB,GAAGjP,SAAS;IACxC;AACR;AACA;IACQ,IAAI,CAACkP,+BAA+B,GAAGlP,SAAS;IAChD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACmP,sBAAsB,GAAGnP,SAAS;IACvC;AACR;AACA;IACQ,IAAI,CAACoP,0BAA0B,GAAGpP,SAAS;IAC3C;AACR;AACA;AACA;IACQ,IAAI,CAACqP,kCAAkC,GAAGrP,SAAS;IACnD;AACR;AACA;IACQ,IAAI,CAACsP,cAAc,GAAGtP,SAAS;IAC/B;AACR;AACA;IACQ,IAAI,CAACuP,YAAY,GAAGvP,SAAS;IAC7B;AACR;AACA;IACQ,IAAI,CAACwP,aAAa,GAAGxP,SAAS;IAC9B;AACR;AACA;AACA;IACQ,IAAI,CAACyP,uBAAuB,GAAGzP,SAAS;IACxC;AACR;AACA;AACA;IACQ,IAAI,CAAC0P,sBAAsB,GAAG1P,SAAS;IACvC;AACR;AACA;AACA;IACQ,IAAI,CAAC2P,yBAAyB,GAAG3P,SAAS;IAC1C;AACR;AACA;IACQ,IAAI,CAAC4P,iBAAiB,GAAG5P,SAAS;IAClC;AACR;AACA;IACQ,IAAI,CAAC6P,mBAAmB,GAAG7P,SAAS;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAAC8P,kBAAkB,GAAG9P,SAAS;IACnC;AACR;AACA;AACA;IACQ,IAAI,CAAC+P,UAAU,GAAG/P,SAAS;IAC3B;AACR;AACA;AACA;IACQ,IAAI,CAACgQ,2BAA2B,GAAGhQ,SAAS;IAC5C;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACiQ,uBAAuB,GAAGjQ,SAAS;IACxC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACkQ,oBAAoB,GAAGlQ,SAAS;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACmQ,iBAAiB,GAAGnQ,SAAS;IAClC;AACR;AACA;AACA;IACQ,IAAI,CAACoQ,gBAAgB,GAAGpQ,SAAS;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACqQ,mBAAmB,GAAGrQ,SAAS;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAACsQ,4BAA4B,GAAGtQ,SAAS;IAC7C;AACR;AACA;AACA;IACQ,IAAI,CAACuQ,YAAY,GAAGvQ,SAAS;IAC7B;AACR;AACA;IACQ,IAAI,CAACwQ,YAAY,GAAGxQ,SAAS;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAACyQ,UAAU,GAAGzQ,SAAS;IAC3B;AACR;AACA;IACQ,IAAI,CAAC0Q,iBAAiB,GAAG1Q,SAAS;IAClC;AACR;AACA;IACQ,IAAI,CAAC2Q,eAAe,GAAG3Q,SAAS;IAChC;AACR;IACQ,IAAI,CAAC4Q,YAAY,GAAG5Q,SAAS;IAC7B;AACR;AACA;IACQ,IAAI,CAAC6Q,6BAA6B,GAAG7Q,SAAS;IAC9C;AACR;AACA;IACQ,IAAI,CAAC8Q,KAAK,GAAG9Q,SAAS;IACtB;AACR;AACA;IACQ,IAAI,CAAC+Q,SAAS,GAAG/Q,SAAS;IAC1B;AACR;AACA;IACQ,IAAI,CAACgR,QAAQ,GAAGhR,SAAS;IACzB;AACR;AACA;IACQ,IAAI,CAACiR,QAAQ,GAAGjR,SAAS;IACzB;AACR;AACA;IACQ,IAAI,CAACkR,aAAa,GAAGlR,SAAS;IAC9B;AACR;AACA;IACQ,IAAI,CAACmR,yBAAyB,GAAGnR,SAAS;IAC1C;AACR;AACA;AACA;IACQ,IAAI,CAACoR,oBAAoB,GAAGpR,SAAS;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACqR,oBAAoB,GAAGrR,SAAS;IACrC;AACR;AACA;IACQ,IAAI,CAACsR,MAAM,GAAGtR,SAAS;IACvB;AACR;AACA;IACQ,IAAI,CAACuR,SAAS,GAAGvR,SAAS;IAC1B;IACA,IAAI,CAACwR,mBAAmB,GAAGxR,SAAS;IACpC;AACR;IACQ,IAAI,CAACyR,eAAe,GAAGzR,SAAS;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAAC0R,YAAY,GAAG1R,SAAS;IAC7B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC2R,KAAK,GAAG3R,SAAS;IACtB;AACR;IACQ,IAAI,CAAC4R,oBAAoB,GAAG5R,SAAS;IACrC;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC6R,aAAa,GAAG7R,SAAS;IAC9B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC8R,UAAU,GAAG9R,SAAS;IAC3B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC+R,mBAAmB,GAAG/R,SAAS;IACpC;AACR;AACA;IACQ,IAAI,CAACgS,mBAAmB,GAAGhS,SAAS;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAACiS,gBAAgB,GAAGjS,SAAS;IACjC;AACR;IACQ,IAAI,CAACkS,gBAAgB,GAAGlS,SAAS;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACmS,sBAAsB,GAAGnS,SAAS;IACvC;AACR;AACA;IACQ,IAAI,CAACoS,uBAAuB,GAAGpS,SAAS;IACxC;AACR;AACA;IACQ,IAAI,CAACqS,yBAAyB,GAAGrS,SAAS;IAC1C;AACR;AACA;IACQ,IAAI,CAACsS,8BAA8B,GAAGtS,SAAS;IAC/C;AACR;AACA;IACQ,IAAI,CAACuS,wBAAwB,GAAGvS,SAAS;IACzC;AACR;AACA;IACQ,IAAI,CAACwS,eAAe,GAAGxS,SAAS;IAChC;AACR;AACA;IACQ,IAAI,CAACyS,wBAAwB,GAAGzS,SAAS;IACzC;AACR;AACA;IACQ,IAAI,CAAC0S,uBAAuB,GAAG1S,SAAS;IACxC;AACR;AACA;IACQ,IAAI,CAAC2S,sBAAsB,GAAG3S,SAAS;IACvC;AACR;AACA;AACA;IACQ,IAAI,CAAC4S,oBAAoB,GAAG5S,SAAS;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAAC6S,oBAAoB,GAAG7S,SAAS;IACrC;AACR;AACA;IACQ,IAAI,CAAC8S,qBAAqB,GAAG9S,SAAS;IACtC;AACR;IACQ,IAAI,CAAC+S,oBAAoB,GAAG/S,SAAS;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACgT,eAAe,GAAGhT,SAAS;IAChC;AACR;IACQ,IAAI,CAACiT,kBAAkB,GAAGjT,SAAS;IACnC;AACR;AACA;AACA;IACQ,IAAI,CAACkT,aAAa,GAAGlT,SAAS;IAC9B;AACR;AACA;AACA;IACQ,IAAI,CAACmT,aAAa,GAAGnT,SAAS;IAC9B;AACR;IACQ,IAAI,CAACoT,WAAW,GAAGpT,SAAS;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACqT,yBAAyB,GAAGrT,SAAS;IAC1C;AACR;AACA;IACQ,IAAI,CAACsT,cAAc,GAAGtT,SAAS;IAC/B;AACR;AACA;IACQ,IAAI,CAACuT,oBAAoB,GAAGvT,SAAS;IACrC;AACR;AACA;IACQ,IAAI,CAACwT,2BAA2B,GAAGxT,SAAS;IAC5C;AACR;AACA;IACQ,IAAI,CAACyT,wBAAwB,GAAGzT,SAAS;IACzC;AACR;AACA;IACQ,IAAI,CAAC0T,6BAA6B,GAAG1T,SAAS;IAC9C;AACR;AACA;AACA;IACQ,IAAI,CAAC2T,WAAW,GAAG3T,SAAS;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAAC4T,aAAa,GAAG5T,SAAS;IAC9B;AACR;AACA;AACA;IACQ,IAAI,CAAC6T,6BAA6B,GAAG7T,SAAS;IAC9C;AACR;AACA;IACQ,IAAI,CAAC8T,8BAA8B,GAAG9T,SAAS;IAC/C;AACR;AACA;IACQ,IAAI,CAAC+T,4BAA4B,GAAG/T,SAAS;IAC7C;AACR;AACA;IACQ,IAAI,CAACgU,iBAAiB,GAAGhU,SAAS;IAClC;AACR;AACA;IACQ,IAAI,CAACiU,qBAAqB,GAAGjU,SAAS;IACtC;AACR;AACA;IACQ,IAAI,CAACkU,qBAAqB,GAAGlU,SAAS;IACtC;AACR;AACA;IACQ,IAAI,CAACmU,QAAQ,GAAGnU,SAAS;IACzB;AACR;AACA;AACA;IACQ,IAAI,CAACoU,oBAAoB,GAAGpU,SAAS;IACrC;AACR;IACQ,IAAI,CAACqU,oBAAoB,GAAGrU,SAAS;IACrC;AACR;AACA;IACQ,IAAI,CAACsU,eAAe,GAAGtU,SAAS;IAChC;AACR;AACA;IACQ,IAAI,CAACuU,WAAW,GAAGvU,SAAS;IAC5B;AACR;AACA;IACQ,IAAI,CAACwU,aAAa,GAAGxU,SAAS;IAC9B;AACR;IACQ,IAAI,CAACyU,YAAY,GAAGzU,SAAS;IAC7B;AACR;AACA;IACQ,IAAI,CAAC0U,WAAW,GAAG1U,SAAS;IAC5B;AACR;AACA;IACQ,IAAI,CAAC2U,WAAW,GAAG3U,SAAS;IAC5B;AACR;IACQ,IAAI,CAAC4U,YAAY,GAAG5U,SAAS;IAC7B;AACR;IACQ,IAAI,CAAC6U,cAAc,GAAG7U,SAAS;IAC/B;AACR;IACQ,IAAI,CAAC8U,uBAAuB,GAAG,IAAI9c,YAAY,CAAC,CAAC;IACjD;AACR;IACQ,IAAI,CAAC+c,oBAAoB,GAAG,IAAI/c,YAAY,CAAC,CAAC;IAC9C;AACR;IACQ,IAAI,CAACgd,wBAAwB,GAAG,IAAIhd,YAAY,CAAC,CAAC;IAClD;AACR;IACQ,IAAI,CAACid,yBAAyB,GAAG,IAAIjd,YAAY,CAAC,CAAC;IACnD;AACR;IACQ,IAAI,CAACkd,QAAQ,GAAG,IAAIld,YAAY,CAAC,CAAC;IAClC;AACR;IACQ,IAAI,CAACmd,MAAM,GAAG,IAAInd,YAAY,CAAC,CAAC;IAChC;AACR;IACQ,IAAI,CAACod,UAAU,GAAG,IAAIpd,YAAY,CAAC,CAAC;IACpC;AACR;IACQ,IAAI,CAACqd,QAAQ,GAAG,IAAIrd,YAAY,CAAC,CAAC;IAClC;AACR;IACQ,IAAI,CAACsd,aAAa,GAAG,IAAItd,YAAY,CAAC,CAAC;IACvC;AACR;IACQ,IAAI,CAACud,YAAY,GAAG,IAAIvd,YAAY,CAAC,CAAC;IACtC;AACR;IACQ,IAAI,CAACwd,aAAa,GAAG,IAAIxd,YAAY,CAAC,CAAC;IACvC;AACR;IACQ,IAAI,CAACyd,WAAW,GAAG,IAAIzd,YAAY,CAAC,CAAC;IACrC;AACR;IACQ,IAAI,CAAC0d,kBAAkB,GAAG,IAAI1d,YAAY,CAAC,CAAC;IAC5C;AACR;IACQ,IAAI,CAAC2d,sBAAsB,GAAG,IAAI3d,YAAY,CAAC,CAAC;IAChD;AACR;IACQ,IAAI,CAAC4d,kBAAkB,GAAG,IAAI5d,YAAY,CAAC,CAAC;IAC5C;AACR;IACQ,IAAI,CAAC6d,iBAAiB,GAAG,IAAI7d,YAAY,CAAC,CAAC;IAC3C;AACR;IACQ,IAAI,CAAC8d,gBAAgB,GAAG,IAAI9d,YAAY,CAAC,CAAC;IAC1C;AACR;IACQ,IAAI,CAAC+d,kBAAkB,GAAG,IAAI/d,YAAY,CAAC,CAAC;IAC5C;AACR;IACQ,IAAI,CAACge,uBAAuB,GAAG,IAAIhe,YAAY,CAAC,CAAC;IACjD;AACR;IACQ,IAAI,CAACie,qBAAqB,GAAG,IAAIje,YAAY,CAAC,CAAC;IAC/C;AACR;AACA;IACQ,IAAI,CAACke,uBAAuB,GAAG,IAAIle,YAAY,CAAC,CAAC;IACjD;AACR;IACQ,IAAI,CAACme,qBAAqB,GAAG,IAAIne,YAAY,CAAC,CAAC;IAC/C;AACR;IACQ,IAAI,CAACoe,sBAAsB,GAAG,IAAIpe,YAAY,CAAC,CAAC;IAChD;AACR;IACQ,IAAI,CAACqe,mBAAmB,GAAG,IAAIre,YAAY,CAAC,CAAC;IAC7C;AACR;IACQ,IAAI,CAACse,uBAAuB,GAAG,IAAIte,YAAY,CAAC,CAAC;IACjD;AACR;AACA;IACQ,IAAI,CAACue,qBAAqB,GAAG,IAAIve,YAAY,CAAC,CAAC;IAC/C;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACwe,gBAAgB,GAAG,IAAIxe,YAAY,CAAC,CAAC;IAC1C;AACR;IACQ,IAAI,CAACye,eAAe,GAAG,IAAIze,YAAY,CAAC,CAAC;IACzC;AACR;IACQ,IAAI,CAAC0e,eAAe,GAAG,IAAI1e,YAAY,CAAC,CAAC;IACzC;AACR;IACQ,IAAI,CAAC2e,kBAAkB,GAAG,IAAI3e,YAAY,CAAC,CAAC;IAC5C;AACR;IACQ,IAAI,CAAC4e,kBAAkB,GAAG,IAAI5e,YAAY,CAAC,CAAC;IAC5C;AACR;IACQ,IAAI,CAAC6e,iBAAiB,GAAG,IAAI7e,YAAY,CAAC,CAAC;IAC3C;AACR;IACQ,IAAI,CAAC8e,iBAAiB,GAAG,IAAI9e,YAAY,CAAC,CAAC;IAC3C;AACR;IACQ,IAAI,CAAC+e,WAAW,GAAG,IAAI/e,YAAY,CAAC,CAAC;IACrC;AACR;IACQ,IAAI,CAACgf,SAAS,GAAG,IAAIhf,YAAY,CAAC,CAAC;IACnC;AACR;IACQ,IAAI,CAACif,WAAW,GAAG,IAAIjf,YAAY,CAAC,CAAC;IACrC;AACR;IACQ,IAAI,CAACkf,SAAS,GAAG,IAAIlf,YAAY,CAAC,CAAC;IACnC;AACR;IACQ,IAAI,CAACmf,wBAAwB,GAAG,IAAInf,YAAY,CAAC,CAAC;IAClD;AACR;IACQ,IAAI,CAACof,sBAAsB,GAAG,IAAIpf,YAAY,CAAC,CAAC;IAChD;AACR;AACA;AACA;IACQ,IAAI,CAACqf,gBAAgB,GAAG,IAAIrf,YAAY,CAAC,CAAC;IAC1C;AACR;AACA;AACA;IACQ,IAAI,CAACsf,cAAc,GAAG,IAAItf,YAAY,CAAC,CAAC;IACxC;AACR;IACQ,IAAI,CAACuf,SAAS,GAAG,IAAIvf,YAAY,CAAC,CAAC;IACnC;AACR;IACQ,IAAI,CAACwf,OAAO,GAAG,IAAIxf,YAAY,CAAC,CAAC;IACjC;AACR;IACQ,IAAI,CAACyf,YAAY,GAAG,IAAIzf,YAAY,CAAC,CAAC;IACtC;AACR;IACQ,IAAI,CAAC0f,aAAa,GAAG,IAAI1f,YAAY,CAAC,CAAC;IACvC;AACR;IACQ,IAAI,CAAC2f,cAAc,GAAG,IAAI3f,YAAY,CAAC,CAAC;IACxC;AACR;IACQ,IAAI,CAAC4f,mCAAmC,GAAG,IAAI5f,YAAY,CAAC,CAAC;IAC7D;AACR;IACQ,IAAI,CAAC6f,WAAW,GAAG,IAAI7f,YAAY,CAAC,CAAC;IACrC;AACR;IACQ,IAAI,CAAC8f,YAAY,GAAG,IAAI9f,YAAY,CAAC,CAAC;IACtC;AACR;IACQ,IAAI,CAAC+f,0BAA0B,GAAG,IAAI/f,YAAY,CAAC,CAAC;IACpD;AACR;IACQ,IAAI,CAACggB,mBAAmB,GAAG,IAAIhgB,YAAY,CAAC,CAAC;IAC7C;AACR;IACQ,IAAI,CAACigB,cAAc,GAAG,IAAIjgB,YAAY,CAAC,CAAC;IACxC;AACR;IACQ,IAAI,CAACkgB,WAAW,GAAG,IAAIlgB,YAAY,CAAC,CAAC;IACrC;IACA,IAAI,CAACmgB,SAAS,GAAG,IAAIngB,YAAY,CAAC,CAAC;IACnC;IACA,IAAI,CAACogB,iBAAiB,GAAG,IAAIpgB,YAAY,CAAC,CAAC;IAC3C;AACR;IACQ,IAAI,CAACqgB,eAAe,GAAG,IAAIrgB,YAAY,CAAC,CAAC;IACzC;AACR;IACQ,IAAI,CAACsgB,YAAY,GAAG,IAAItgB,YAAY,CAAC,CAAC;IACtC;AACR;IACQ,IAAI,CAACugB,iBAAiB,GAAG,IAAIvgB,YAAY,CAAC,CAAC;IAC3C;AACR;IACQ,IAAI,CAACwgB,eAAe,GAAG,IAAIxgB,YAAY,CAAC,CAAC;IACzC;AACR;IACQ,IAAI,CAACygB,UAAU,GAAG,IAAIzgB,YAAY,CAAC,CAAC;IACpC;AACR;IACQ,IAAI,CAAC0gB,aAAa,GAAG,IAAI1gB,YAAY,CAAC,CAAC;IACvC;AACR;IACQ,IAAI,CAAC2gB,WAAW,GAAG,IAAI3gB,YAAY,CAAC,CAAC;IACrC;AACR;IACQ,IAAI,CAAC4gB,WAAW,GAAG,IAAI5gB,YAAY,CAAC,CAAC;IACrC;AACR;IACQ,IAAI,CAAC6gB,aAAa,GAAG,IAAI7gB,YAAY,CAAC,CAAC;IACvC;AACR;IACQ,IAAI,CAAC8gB,YAAY,GAAG,IAAI9gB,YAAY,CAAC,CAAC;IACtC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC+gB,iBAAiB,GAAG,IAAI/gB,YAAY,CAAC,CAAC;IAC3C;AACR;IACQ,IAAI,CAACghB,YAAY,GAAG,IAAIhhB,YAAY,CAAC,CAAC;IACtC;AACR;IACQ,IAAI,CAACihB,WAAW,GAAG,IAAIjhB,YAAY,CAAC,CAAC;IACrC;AACR;IACQ,IAAI,CAACkhB,YAAY,GAAG,IAAIlhB,YAAY,CAAC,CAAC;IACtC;AACR;IACQ,IAAI,CAACmhB,UAAU,GAAG,IAAInhB,YAAY,CAAC,CAAC;IACpC;AACR;IACQ,IAAI,CAACohB,aAAa,GAAG,IAAIphB,YAAY,CAAC,CAAC;IACvC;AACR;IACQ,IAAI,CAACqhB,gBAAgB,GAAG,IAAIrhB,YAAY,CAAC,CAAC;IAC1C;AACR;IACQ,IAAI,CAACshB,cAAc,GAAG,IAAIthB,YAAY,CAAC,CAAC;IACxC;AACR;IACQ,IAAI,CAACuhB,qBAAqB,GAAG,IAAIvhB,YAAY,CAAC,CAAC;IAC/C;AACR;IACQ,IAAI,CAACwhB,cAAc,GAAG,IAAIxhB,YAAY,CAAC,CAAC;IACxC;AACR;IACQ,IAAI,CAACyhB,mBAAmB,GAAG,IAAIzhB,YAAY,CAAC,CAAC;IAC7C;AACR;IACQ,IAAI,CAAC0hB,uBAAuB,GAAG,IAAI1hB,YAAY,CAAC,CAAC;IACjD;AACR;IACQ,IAAI,CAAC2hB,oBAAoB,GAAG,IAAI3hB,YAAY,CAAC,CAAC;IAC9C;AACR;IACQ,IAAI,CAAC4hB,iBAAiB,GAAG,IAAI5hB,YAAY,CAAC,CAAC;IAC3C;AACR;IACQ,IAAI,CAAC6hB,cAAc,GAAG,IAAI7hB,YAAY,CAAC,CAAC;IACxC;AACR;IACQ,IAAI,CAAC8hB,wBAAwB,GAAG,IAAI9hB,YAAY,CAAC,CAAC;IAClD;AACR;IACQ,IAAI,CAAC+hB,cAAc,GAAG,IAAI/hB,YAAY,CAAC,CAAC;IACxC;AACR;IACQ,IAAI,CAACgiB,aAAa,GAAG,IAAIhiB,YAAY,CAAC,CAAC;IACvC;AACR;IACQ,IAAI,CAACiiB,WAAW,GAAG,IAAIjiB,YAAY,CAAC,CAAC;IACrC;AACR;IACQ,IAAI,CAACkiB,iBAAiB,GAAG,IAAIliB,YAAY,CAAC,CAAC;IAC3C;AACR;IACQ,IAAI,CAACmiB,WAAW,GAAG,IAAIniB,YAAY,CAAC,CAAC;IACrC;AACR;IACQ,IAAI,CAACoiB,aAAa,GAAG,IAAIpiB,YAAY,CAAC,CAAC;IACvC;AACR;IACQ,IAAI,CAACqiB,YAAY,GAAG,IAAIriB,YAAY,CAAC,CAAC;IACtC;AACR;IACQ,IAAI,CAACsiB,aAAa,GAAG,IAAItiB,YAAY,CAAC,CAAC;IACvC;AACR;IACQ,IAAI,CAACuiB,UAAU,GAAG,IAAIviB,YAAY,CAAC,CAAC;IACpC;AACR;IACQ,IAAI,CAACwiB,gBAAgB,GAAG,IAAIxiB,YAAY,CAAC,CAAC;IAC1C;AACR;IACQ,IAAI,CAACyiB,WAAW,GAAG,IAAIziB,YAAY,CAAC,CAAC;IACrC;AACR;AACA;IACQ,IAAI,CAAC0iB,gBAAgB,GAAG,IAAI1iB,YAAY,CAAC,CAAC;IAC1C;AACR;IACQ,IAAI,CAAC2iB,eAAe,GAAG,IAAI3iB,YAAY,CAAC,CAAC;IACzC;AACR;AACA;AACA;IACQ,IAAI,CAAC4iB,qBAAqB,GAAG,IAAI5iB,YAAY,CAAC,CAAC;IAC/C;AACR;IACQ,IAAI,CAAC6iB,oBAAoB,GAAG,IAAI7iB,YAAY,CAAC,CAAC;IAC9C;IACA,IAAI,CAAC8iB,WAAW,GAAG,IAAI9iB,YAAY,CAAC,CAAC;IACrC;IACA,IAAI,CAAC+iB,WAAW,GAAG,IAAI/iB,YAAY,CAAC,CAAC;IACrC;AACR;IACQ,IAAI,CAACgjB,WAAW,GAAG,IAAIhjB,YAAY,CAAC,CAAC;IACrC,IAAI,CAACijB,cAAc,GAAGxa,UAAU,CAAC7F,aAAa;IAC9C,IAAI,CAACoG,WAAW,CAACka,IAAI,CAAC,MAAM;MACxB;MACA;MACA,IAAI,CAACna,WAAW,GAAG,KAAK;IAC5B,CAAC,CAAC;EACN;EACAoa,eAAeA,CAAA,EAAG;IACd;IACA,IAAI,CAACxa,0BAA0B,CAACjB,iBAAiB,CAAC,MAAM;MACpD,IAAI,CAACkB,qBAAqB,CAAC5F,mBAAmB,CAAC,IAAI,CAAC0F,iBAAiB,EAAE,IAAI,CAACC,0BAA0B,CAAC;MACvG;MACA,MAAMya,cAAc,GAAGC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,CAACC,MAAM,CAAEC,GAAG,IAAK,EAAEA,GAAG,CAACC,UAAU,CAAC,GAAG,CAAC,IAC1ED,GAAG,IAAI,aAAa,IACpBA,GAAG,IAAI,SAAS,IAChB,IAAI,CAACA,GAAG,CAAC,YAAYxjB,YAAY,CAAC,CAAC;MACvC,MAAM0jB,kBAAkB,GAAG,CAAC,CAAC;MAC7BN,cAAc,CAACO,OAAO,CAAEH,GAAG,IAAK;QAC5B,MAAMI,UAAU,GAAGC,sBAAsB,CAACL,GAAG,EAAE,IAAI,CAACA,GAAG,CAAC,CAAC;QACzDE,kBAAkB,CAACF,GAAG,CAAC,GAAGI,UAAU;MACxC,CAAC,CAAC;MACF,MAAME,aAAa,GAAGrjB,gCAAgC,CAAC,IAAI,CAACsjB,WAAW,EAAEL,kBAAkB,EAAEN,cAAc,CAAC;MAC5G,MAAMY,UAAU,GAAG;QACfC,cAAc,EAAE,IAAI,CAACA,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;QAC9Cpe,kBAAkB,EAAE,IAAI,CAAC6C,0BAA0B;QACnDwb,qBAAqB,EAAE;UACnBC,oBAAoB,EAAE,IAAI,CAACxb;QAC/B,CAAC;QACDyb,OAAO,EAAG,IAAI,CAACA,OAAO,IAAI,EAAG;QAC7BC,iBAAiB,EAAE;MACvB,CAAC;MACD,MAAMC,GAAG,GAAG7jB,UAAU,CAAC,IAAI,CAACuiB,cAAc,EAAEa,aAAa,EAAEE,UAAU,CAAC;MACtE,IAAIO,GAAG,EAAE;QACL,IAAI,CAACA,GAAG,GAAGA,GAAG;MAClB;MACA,IAAI,CAAC1b,YAAY,GAAG,IAAI;MACxB;MACA;MACA;MACA,IAAI,CAACM,kBAAkB,CAAC,CAAC;IAC7B,CAAC,CAAC;EACN;EACAqb,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,IAAI,CAAC5b,YAAY,EAAE;MACnB;MACA,IAAI,CAACF,0BAA0B,CAACjB,iBAAiB,CAAC,MAAM;QACpD,MAAMqc,WAAW,GAAG,CAAC,CAAC;QACtB,KAAK,MAAMP,GAAG,IAAIH,MAAM,CAACC,IAAI,CAACmB,OAAO,CAAC,EAAE;UACpC,MAAMC,KAAK,GAAGD,OAAO,CAACjB,GAAG,CAAC;UAC1BO,WAAW,CAACP,GAAG,CAAC,GAAGkB,KAAK,CAACC,YAAY;QACzC;QACAhkB,gBAAgB,CAACojB,WAAW,EAAE,IAAI,CAACQ,GAAG,CAAC;MAC3C,CAAC,CAAC;IACN;EACJ;EACAK,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC/b,YAAY,EAAE;MACnB;MACA;MACA,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB;MACA,IAAI,CAACyb,GAAG,EAAE3e,OAAO,CAAC,CAAC;IACvB;EACJ;EACA;EACA;EACAif,aAAaA,CAAC3e,SAAS,EAAE;IACrB,MAAM4e,OAAO,GAAG,IAAI,CAAC5e,SAAS,CAAC;IAC/B;IACA,MAAM6e,UAAU,GAAGD,OAAO;IAC1B,MAAME,UAAU,GAAGD,UAAU,EAAEE,QAAQ,IAAIF,UAAU,EAAEG,SAAS,EAAEC,MAAM,GAAG,CAAC;IAC5E;IACA,MAAMC,WAAW,GAAG,KAAKlf,SAAS,CAACmf,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGpf,SAAS,CAACqf,SAAS,CAAC,CAAC,CAAC,EAAE;IACrF,MAAMC,qBAAqB,GAAG,CAAC,CAAC,IAAI,CAACzB,WAAW,IAAI,CAAC,CAAC,IAAI,CAACA,WAAW,CAACqB,WAAW,CAAC;IACnF,OAAOJ,UAAU,IAAIQ,qBAAqB;EAC9C;EACAvB,cAAcA,CAAC/d,SAAS,EAAEI,KAAK,EAAE;IAC7B;IACA;IACA,IAAI,IAAI,CAACwC,UAAU,EAAE;MACjB;IACJ;IACA;IACA,MAAMgc,OAAO,GAAG,IAAI,CAAC5e,SAAS,CAAC;IAC/B,IAAI4e,OAAO,IAAI,IAAI,CAACD,aAAa,CAAC3e,SAAS,CAAC,EAAE;MAC1C;MACA,MAAMuf,WAAW,GAAGA,CAAA,KAAM,IAAI,CAAC9c,0BAA0B,CAAChF,gBAAgB,CAAC,MAAMmhB,OAAO,CAACY,IAAI,CAACpf,KAAK,CAAC,CAAC;MACrG,IAAI,IAAI,CAACyC,WAAW,EAAE;QAClB;QACA,IAAI,CAACC,WAAW,CAACka,IAAI,CAAC,MAAMuC,WAAW,CAAC,CAAC,CAAC;MAC9C,CAAC,MACI;QACDA,WAAW,CAAC,CAAC;MACjB;IACJ;EACJ;EACA;IAAS,IAAI,CAACzkB,IAAI,YAAA2kB,sBAAAzkB,CAAA;MAAA,YAAAA,CAAA,IAAyFsH,aAAa,EA5oEvB9I,EAAE,CAAAkmB,iBAAA,CA4oEuClmB,EAAE,CAACmmB,UAAU,GA5oEtDnmB,EAAE,CAAAkmB,iBAAA,CA4oEiElmB,EAAE,CAACE,gBAAgB,GA5oEtFF,EAAE,CAAAkmB,iBAAA,CA4oEiG7e,yBAAyB,GA5oE5HrH,EAAE,CAAAkmB,iBAAA,CA4oEuI7iB,gCAAgC;IAAA,CAA4C;EAAE;EACxT;IAAS,IAAI,CAAC5B,IAAI,kBA7oE+EzB,EAAE,CAAA0B,iBAAA;MAAAC,IAAA,EA6oEJmH,aAAa;MAAAlH,SAAA;MAAAwkB,MAAA;QAAA/B,WAAA;QAAAM,OAAA;QAAAjb,SAAA;QAAAC,OAAA;QAAAC,mBAAA,GA7oEX5J,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gDA6oEsO/lB,gBAAgB;QAAAsJ,2BAAA,GA7oExP7J,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gEA6oEqV/lB,gBAAgB;QAAAuJ,8BAAA,GA7oEvW9J,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sEA6oE6c/lB,gBAAgB;QAAAwJ,UAAA;QAAAC,gBAAA,GA7oE/dhK,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,0CA6oEqjB/lB,gBAAgB;QAAA0J,qBAAA,GA7oEvkBjK,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,oDA6oEkpB/lB,gBAAgB;QAAA2J,cAAA;QAAAC,gBAAA;QAAAC,gBAAA;QAAAC,iBAAA,GA7oEpqBrK,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4CA6oEi1B/lB,gBAAgB;QAAA+J,eAAA;QAAAC,kBAAA,GA7oEn2BvK,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,8CA6oEy8B/lB,gBAAgB;QAAAiK,WAAA;QAAAC,sBAAA,GA7oE39BzK,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sDA6oEqkC/lB,gBAAgB;QAAAmK,2BAAA,GA7oEvlC1K,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gEA6oEorC/lB,gBAAgB;QAAAoK,kBAAA;QAAAC,2BAAA,GA7oEtsC5K,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gEA6oE60C/lB,gBAAgB;QAAAsK,4BAAA,GA7oE/1C7K,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kEA6oE+7C/lB,gBAAgB;QAAAuK,4BAAA,GA7oEj9C9K,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kEA6oEijD/lB,gBAAgB;QAAAwK,sBAAA,GA7oEnkD/K,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sDA6oEipD/lB,gBAAgB;QAAAyK,oBAAA,GA7oEnqDhL,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kDA6oE2uD/lB,gBAAgB;QAAA0K,sBAAA,GA7oE7vDjL,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sDA6oE20D/lB,gBAAgB;QAAA2K,UAAA;QAAAC,aAAA;QAAAC,kBAAA;QAAAC,WAAA;QAAAC,mBAAA;QAAAC,mBAAA,GA7oE71DvL,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gDA6oE8kE/lB,gBAAgB;QAAAiL,4BAAA,GA7oEhmExL,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kEA6oEgsE/lB,gBAAgB;QAAAkL,wBAAA,GA7oEltEzL,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,0DA6oEsyE/lB,gBAAgB;QAAAmL,YAAA;QAAAC,iBAAA;QAAAC,qBAAA;QAAAC,iBAAA;QAAAC,sBAAA;QAAAC,6BAAA,GA7oExzE/L,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,oEA6oE2mF/lB,gBAAgB;QAAAyL,sBAAA,GA7oE7nFhM,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sDA6oE2sF/lB,gBAAgB;QAAA0L,2BAAA,GA7oE7tFjM,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gEA6oE0zF/lB,gBAAgB;QAAA2L,8BAAA,GA7oE50FlM,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sEA6oEk7F/lB,gBAAgB;QAAA4L,6BAAA,GA7oEp8FnM,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,oEA6oEuiG/lB,gBAAgB;QAAA6L,oCAAA;QAAAC,qCAAA,GA7oEzjGrM,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,oFA6oEkwG/lB,gBAAgB;QAAA+L,4BAAA,GA7oEpxGtM,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kEA6oEo3G/lB,gBAAgB;QAAAgM,gBAAA;QAAAC,gBAAA,GA7oEt4GxM,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,0CA6oEw+G/lB,gBAAgB;QAAAkM,eAAA;QAAAC,oBAAA,GA7oE1/G1M,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kDA6oEsmH/lB,gBAAgB;QAAAoM,gBAAA;QAAAC,UAAA;QAAAC,QAAA;QAAAC,eAAA,GA7oExnH9M,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wCA6oEuwH/lB,gBAAgB;QAAAwM,iBAAA,GA7oEzxH/M,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4CA6oEw1H/lB,gBAAgB;QAAAyM,YAAA,GA7oE12HhN,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kCA6oE05H/lB,gBAAgB;QAAA0M,6BAAA,GA7oE56HjN,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,oEA6oE+gI/lB,gBAAgB;QAAA2M,wBAAA,GA7oEjiIlN,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,0DA6oEqnI/lB,gBAAgB;QAAA4M,iCAAA,GA7oEvoInN,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4EA6oEsvI/lB,gBAAgB;QAAA6M,4BAAA,GA7oExwIpN,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kEA6oEw2I/lB,gBAAgB;QAAA8M,mBAAA,GA7oE13IrN,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gDA6oE+7I/lB,gBAAgB;QAAA+M,wBAAA;QAAAC,sBAAA;QAAAC,iBAAA,GA7oEj9IxN,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4CA6oEwnJ/lB,gBAAgB;QAAAkN,wBAAA;QAAAC,mBAAA,GA7oE1oJ1N,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gDA6oEqwJ/lB,gBAAgB;QAAAoN,WAAA;QAAAC,eAAA;QAAAC,WAAA;QAAAC,eAAA;QAAAC,gBAAA,GA7oEvxJ/N,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,0CA6oEm9J/lB,gBAAgB;QAAAyN,iCAAA,GA7oEr+JhO,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4EA6oEolK/lB,gBAAgB;QAAA0N,iBAAA;QAAAC,kBAAA;QAAAC,gCAAA,GA7oEtmKnO,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,0EA6oEoyK/lB,gBAAgB;QAAA6N,oCAAA,GA7oEtzKpO,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kFA6oE86K/lB,gBAAgB;QAAA8N,oBAAA,GA7oEh8KrO,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kDA6oEwgL/lB,gBAAgB;QAAA+N,gBAAA;QAAAC,oCAAA,GA7oE1hLvO,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kFA6oEwrL/lB,gBAAgB;QAAAiO,oBAAA;QAAAC,2BAAA;QAAAC,0BAAA,GA7oE1sL1O,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,8DA6oE84L/lB,gBAAgB;QAAAoO,0BAAA,GA7oEh6L3O,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,8DA6oE0/L/lB,gBAAgB;QAAAqO,YAAA,GA7oE5gM5O,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kCA6oE4jM/lB,gBAAgB;QAAAsO,WAAA;QAAAC,iBAAA;QAAAC,mBAAA;QAAAC,kBAAA;QAAAC,cAAA;QAAAC,mBAAA;QAAAC,yBAAA;QAAAC,2BAAA;QAAAC,UAAA;QAAAC,YAAA,GA7oE9kMtP,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kCA6oEo/M/lB,gBAAgB;QAAAgP,cAAA,GA7oEtgNvP,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sCA6oE4jN/lB,gBAAgB;QAAAiP,mBAAA;QAAAC,kBAAA;QAAAC,wBAAA;QAAAC,eAAA;QAAAC,mBAAA,GA7oE9kN5P,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gDA6oEm0N/lB,gBAAgB;QAAAsP,OAAA;QAAAC,YAAA;QAAAC,QAAA;QAAAC,SAAA;QAAAC,UAAA,GA7oEr1NjQ,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,8BA6oE+9N/lB,gBAAgB;QAAA2P,sBAAA,GA7oEj/NlQ,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sDA6oE+jO/lB,gBAAgB;QAAA4P,qBAAA,GA7oEjlOnQ,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,oDA6oE4pO/lB,gBAAgB;QAAA6P,aAAA,GA7oE9qOpQ,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,oCA6oEiuO/lB,gBAAgB;QAAA8P,yBAAA,GA7oEnvOrQ,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4DA6oE00O/lB,gBAAgB;QAAA+P,6BAAA,GA7oE51OtQ,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,oEA6oE+7O/lB,gBAAgB;QAAAgQ,0BAAA,GA7oEj9OvQ,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,8DA6oE2iP/lB,gBAAgB;QAAAiQ,uBAAA,GA7oE7jPxQ,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wDA6oE8oP/lB,gBAAgB;QAAAkQ,KAAA,GA7oEhqPzQ,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,oBA6oE2rP/lB,gBAAgB;QAAAmQ,OAAA,GA7oE7sP1Q,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wBA6oE8uP/lB,gBAAgB;QAAAoQ,sBAAA;QAAAC,uBAAA;QAAAC,6BAAA;QAAAC,sBAAA,GA7oEhwP9Q,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sDA6oEo/P/lB,gBAAgB;QAAAwQ,qBAAA;QAAAC,sBAAA;QAAAC,4BAAA;QAAAC,qBAAA,GA7oEtgQlR,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,oDA6oEivQ/lB,gBAAgB;QAAA4Q,UAAA,GA7oEnwQnR,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,8BA6oE6yQ/lB,gBAAgB;QAAA6Q,kBAAA;QAAAC,0BAAA;QAAAC,sBAAA,GA7oE/zQtR,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sDA6oEi/Q/lB,gBAAgB;QAAAgR,iBAAA,GA7oEngRvR,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4CA6oEkkR/lB,gBAAgB;QAAAiR,uBAAA,GA7oEplRxR,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wDA6oEqqR/lB,gBAAgB;QAAAkR,SAAA,GA7oEvrRzR,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4BA6oE8tR/lB,gBAAgB;QAAAmR,cAAA;QAAAC,wBAAA;QAAAC,oBAAA;QAAAC,sBAAA;QAAAC,cAAA;QAAAC,uBAAA,GA7oEhvR/R,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wDA6oE2hS/lB,gBAAgB;QAAAyR,6BAAA,GA7oE7iShS,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,oEA6oEgpS/lB,gBAAgB;QAAA0R,iBAAA,GA7oElqSjS,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4CA6oEiuS/lB,gBAAgB;QAAA2R,QAAA;QAAAC,uBAAA,GA7oEnvSnS,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wDA6oE01S/lB,gBAAgB;QAAA6R,0BAAA,GA7oE52SpS,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,8DA6oEs8S/lB,gBAAgB;QAAA8R,2BAAA,GA7oEx9SrS,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gEA6oEqjT/lB,gBAAgB;QAAA+R,uBAAA,GA7oEvkTtS,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wDA6oEwpT/lB,gBAAgB;QAAAgS,yCAAA,GA7oE1qTvS,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4FA6oEizT/lB,gBAAgB;QAAAiS,WAAA,GA7oEn0TxS,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gCA6oEg3T/lB,gBAAgB;QAAAkS,iBAAA;QAAAC,gBAAA;QAAAC,0BAAA,GA7oEl4T3S,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,8DA6oE0iU/lB,gBAAgB;QAAAqS,SAAA;QAAAC,cAAA,GA7oE5jU7S,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sCA6oE0oU/lB,gBAAgB;QAAAuS,cAAA,GA7oE5pU9S,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sCA6oEktU/lB,gBAAgB;QAAAwS,SAAA,GA7oEpuU/S,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4BA6oE2wU/lB,gBAAgB;QAAAyS,4BAAA,GA7oE7xUhT,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kEA6oE63U/lB,gBAAgB;QAAA0S,iCAAA,GA7oE/4UjT,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4EA6oE8/U/lB,gBAAgB;QAAA2S,yBAAA,GA7oEhhVlT,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4DA6oEumV/lB,gBAAgB;QAAA4S,cAAA,GA7oEznVnT,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sCA6oE+qV/lB,gBAAgB;QAAA6S,eAAA,GA7oEjsVpT,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wCA6oE0vV/lB,gBAAgB;QAAA8S,2BAAA,GA7oE5wVrT,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gEA6oEy2V/lB,gBAAgB;QAAA+S,gBAAA,GA7oE33VtT,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,0CA6oEu7V/lB,gBAAgB;QAAAgT,eAAA,GA7oEz8VvT,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wCA6oEkgW/lB,gBAAgB;QAAAiT,WAAA;QAAAC,yBAAA;QAAAC,+BAAA;QAAAC,qBAAA;QAAAC,2BAAA;QAAAC,kBAAA,GA7oEphW7T,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,8CA6oE01W/lB,gBAAgB;QAAAuT,gBAAA;QAAAC,oBAAA;QAAAC,kBAAA;QAAAC,kBAAA,GA7oE52WjU,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,8CA6oE4iX/lB,gBAAgB;QAAA2T,oBAAA,GA7oE9jXlU,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kDA6oEsoX/lB,gBAAgB;QAAA4T,qBAAA;QAAAC,iBAAA;QAAAC,aAAA;QAAAC,aAAA;QAAAC,sBAAA;QAAAC,wBAAA,GA7oExpXxU,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,0DA6oEs7X/lB,gBAAgB;QAAAkU,oBAAA,GA7oEx8XzU,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kDA6oEghY/lB,gBAAgB;QAAAmU,eAAA,GA7oEliY1U,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wCA6oE2lY/lB,gBAAgB;QAAAoU,4BAAA;QAAAC,yBAAA,GA7oE7mY5U,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4DA6oEkwY/lB,gBAAgB;QAAAsU,+BAAA,GA7oEpxY7U,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wEA6oE63Y/lB,gBAAgB;QAAAuU,oBAAA,GA7oE/4Y9U,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kDA6oEu9Y/lB,gBAAgB;QAAAwU,oBAAA,GA7oEz+Y/U,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kDA6oEijZ/lB,gBAAgB;QAAAyU,iBAAA;QAAAC,gBAAA;QAAAC,sBAAA;QAAAC,QAAA,GA7oEnkZnV,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,0BA6oEuuZ/lB,gBAAgB;QAAA6U,qBAAA;QAAAC,qBAAA;QAAAC,yBAAA,GA7oEzvZtV,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4DA6oEg7Z/lB,gBAAgB;QAAAgV,uBAAA,GA7oEl8ZvV,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wDA6oEmha/lB,gBAAgB;QAAAiV,gBAAA;QAAAC,mBAAA;QAAAC,gBAAA;QAAAC,aAAA;QAAAC,WAAA;QAAAC,YAAA;QAAAC,OAAA;QAAAC,0BAAA;QAAAC,yCAAA,GA7oEriahW,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4FA6oE48a/lB,gBAAgB;QAAA0V,UAAA;QAAAC,iBAAA;QAAAC,uBAAA;QAAAC,yBAAA;QAAAC,qCAAA,GA7oE99arW,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,oFA6oEuwb/lB,gBAAgB;QAAA+V,cAAA;QAAAC,gBAAA;QAAAC,+BAAA;QAAAC,uBAAA;QAAAC,mBAAA,GA7oEzxb1W,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gDA6oE8hc/lB,gBAAgB;QAAAoW,oBAAA;QAAAC,uBAAA,GA7oEhjc5W,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wDA6oE+qc/lB,gBAAgB;QAAAsW,8BAAA,GA7oEjsc7W,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sEA6oEuyc/lB,gBAAgB;QAAAuW,mCAAA,GA7oEzzc9W,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gFA6oE86c/lB,gBAAgB;QAAAwW,mCAAA;QAAAC,kBAAA;QAAAC,wBAAA;QAAAC,0BAAA;QAAAC,0BAAA,GA7oEh8cnX,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,8DA6oEgwd/lB,gBAAgB;QAAA6W,wBAAA,GA7oElxdpX,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,0DA6oEs2d/lB,gBAAgB;QAAA8W,yBAAA,GA7oEx3drX,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4DA6oE+8d/lB,gBAAgB;QAAA+W,wBAAA,GA7oEj+dtX,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,0DA6oEqje/lB,gBAAgB;QAAAgX,uBAAA,GA7oEvkevX,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wDA6oEwpe/lB,gBAAgB;QAAAiX,+BAAA,GA7oE1qexX,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wEA6oEmxe/lB,gBAAgB;QAAAkX,sBAAA,GA7oEryezX,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sDA6oEm3e/lB,gBAAgB;QAAAmX,0BAAA,GA7oEr4e1X,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,8DA6oE+9e/lB,gBAAgB;QAAAoX,kCAAA,GA7oEj/e3X,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,8EA6oEmmf/lB,gBAAgB;QAAAqX,cAAA;QAAAC,YAAA;QAAAC,aAAA;QAAAC,uBAAA,GA7oErnf/X,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wDA6oEsyf/lB,gBAAgB;QAAAyX,sBAAA,GA7oExzfhY,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,sDA6oEs4f/lB,gBAAgB;QAAA0X,yBAAA,GA7oEx5fjY,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4DA6oE++f/lB,gBAAgB;QAAA2X,iBAAA,GA7oEjggBlY,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4CA6oEgkgB/lB,gBAAgB;QAAA4X,mBAAA,GA7oEllgBnY,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gDA6oEupgB/lB,gBAAgB;QAAA6X,kBAAA;QAAAC,UAAA;QAAAC,2BAAA,GA7oEzqgBtY,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,gEA6oE00gB/lB,gBAAgB;QAAAgY,uBAAA,GA7oE51gBvY,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wDA6oE66gB/lB,gBAAgB;QAAAiY,oBAAA,GA7oE/7gBxY,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kDA6oEughB/lB,gBAAgB;QAAAkY,iBAAA,GA7oEzhhBzY,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4CA6oEwlhB/lB,gBAAgB;QAAAmY,gBAAA,GA7oE1mhB1Y,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,0CA6oEsqhB/lB,gBAAgB;QAAAoY,mBAAA;QAAAC,4BAAA,GA7oExrhB5Y,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kEA6oEo0hB/lB,gBAAgB;QAAAsY,YAAA;QAAAC,YAAA,GA7oEt1hB9Y,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kCA6oEo6hB/lB,gBAAgB;QAAAwY,UAAA,GA7oEt7hB/Y,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,8BA6oEg+hB/lB,gBAAgB;QAAAyY,iBAAA,GA7oEl/hBhZ,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4CA6oEijiB/lB,gBAAgB;QAAA0Y,eAAA,GA7oEnkiBjZ,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wCA6oE4niB/lB,gBAAgB;QAAA2Y,YAAA;QAAAC,6BAAA,GA7oE9oiBnZ,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,oEA6oE+wiB/lB,gBAAgB;QAAA6Y,KAAA;QAAAC,SAAA;QAAAC,QAAA;QAAAC,QAAA;QAAAC,aAAA;QAAAC,yBAAA,GA7oEjyiBzZ,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4DA6oE4+iB/lB,gBAAgB;QAAAmZ,oBAAA,GA7oE9/iB1Z,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kDA6oEskjB/lB,gBAAgB;QAAAoZ,oBAAA,GA7oExljB3Z,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kDA6oEgqjB/lB,gBAAgB;QAAAqZ,MAAA;QAAAC,SAAA,GA7oElrjB7Z,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,4BA6oE2ujB/lB,gBAAgB;QAAAuZ,mBAAA;QAAAC,eAAA,GA7oE7vjB/Z,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,wCA6oEk2jB/lB,gBAAgB;QAAAyZ,YAAA;QAAAC,KAAA;QAAAC,oBAAA,GA7oEp3jBla,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kDA6oE0+jB/lB,gBAAgB;QAAA4Z,aAAA;QAAAC,UAAA;QAAAC,mBAAA;QAAAC,mBAAA;QAAAC,gBAAA;QAAAC,gBAAA;QAAAC,sBAAA;QAAAC,uBAAA;QAAAC,yBAAA;QAAAC,8BAAA;QAAAC,wBAAA;QAAAC,eAAA;QAAAC,wBAAA;QAAAC,uBAAA;QAAAC,sBAAA;QAAAC,oBAAA;QAAAC,oBAAA;QAAAC,qBAAA;QAAAC,oBAAA;QAAAC,eAAA;QAAAC,kBAAA;QAAAC,aAAA;QAAAC,aAAA;QAAAC,WAAA;QAAAC,yBAAA;QAAAC,cAAA;QAAAC,oBAAA;QAAAC,2BAAA;QAAAC,wBAAA;QAAAC,6BAAA;QAAAC,WAAA;QAAAC,aAAA;QAAAC,6BAAA;QAAAC,8BAAA;QAAAC,4BAAA;QAAAC,iBAAA;QAAAC,qBAAA;QAAAC,qBAAA;QAAAC,QAAA;QAAAC,oBAAA,GA7oE5/jB1c,EAAE,CAAAqmB,YAAA,CAAAC,0BAAA,kDA6oEgznB/lB,gBAAgB;QAAAoc,oBAAA;QAAAC,eAAA;QAAAC,WAAA;QAAAC,aAAA;QAAAC,YAAA;QAAAC,WAAA;QAAAC,WAAA;QAAAC,YAAA;QAAAC,cAAA;MAAA;MAAAoJ,OAAA;QAAAnJ,uBAAA;QAAAC,oBAAA;QAAAC,wBAAA;QAAAC,yBAAA;QAAAC,QAAA;QAAAC,MAAA;QAAAC,UAAA;QAAAC,QAAA;QAAAC,aAAA;QAAAC,YAAA;QAAAC,aAAA;QAAAC,WAAA;QAAAC,kBAAA;QAAAC,sBAAA;QAAAC,kBAAA;QAAAC,iBAAA;QAAAC,gBAAA;QAAAC,kBAAA;QAAAC,uBAAA;QAAAC,qBAAA;QAAAC,uBAAA;QAAAC,qBAAA;QAAAC,sBAAA;QAAAC,mBAAA;QAAAC,uBAAA;QAAAC,qBAAA;QAAAC,gBAAA;QAAAC,eAAA;QAAAC,eAAA;QAAAC,kBAAA;QAAAC,kBAAA;QAAAC,iBAAA;QAAAC,iBAAA;QAAAC,WAAA;QAAAC,SAAA;QAAAC,WAAA;QAAAC,SAAA;QAAAC,wBAAA;QAAAC,sBAAA;QAAAC,gBAAA;QAAAC,cAAA;QAAAC,SAAA;QAAAC,OAAA;QAAAC,YAAA;QAAAC,aAAA;QAAAC,cAAA;QAAAC,mCAAA;QAAAC,WAAA;QAAAC,YAAA;QAAAC,0BAAA;QAAAC,mBAAA;QAAAC,cAAA;QAAAC,WAAA;QAAAC,SAAA;QAAAC,iBAAA;QAAAC,eAAA;QAAAC,YAAA;QAAAC,iBAAA;QAAAC,eAAA;QAAAC,UAAA;QAAAC,aAAA;QAAAC,WAAA;QAAAC,WAAA;QAAAC,aAAA;QAAAC,YAAA;QAAAC,iBAAA;QAAAC,YAAA;QAAAC,WAAA;QAAAC,YAAA;QAAAC,UAAA;QAAAC,aAAA;QAAAC,gBAAA;QAAAC,cAAA;QAAAC,qBAAA;QAAAC,cAAA;QAAAC,mBAAA;QAAAC,uBAAA;QAAAC,oBAAA;QAAAC,iBAAA;QAAAC,cAAA;QAAAC,wBAAA;QAAAC,cAAA;QAAAC,aAAA;QAAAC,WAAA;QAAAC,iBAAA;QAAAC,WAAA;QAAAC,aAAA;QAAAC,YAAA;QAAAC,aAAA;QAAAC,UAAA;QAAAC,gBAAA;QAAAC,WAAA;QAAAC,gBAAA;QAAAC,eAAA;QAAAC,qBAAA;QAAAC,oBAAA;QAAAC,WAAA;QAAAC,WAAA;QAAAC,WAAA;MAAA;MAAAkD,UAAA;MAAAC,QAAA,GA7oEl0nBzmB,EAAE,CAAA0mB,kBAAA,CA6oEmsvB,CAACrf,yBAAyB,EAAEhE,gCAAgC,CAAC,GA7oElwvBrD,EAAE,CAAA2mB,wBAAA,EAAF3mB,EAAE,CAAA4mB,oBAAA,EAAF5mB,EAAE,CAAA6mB,mBAAA;MAAAhlB,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAA+kB,uBAAA7kB,EAAA,EAAAC,GAAA;MAAAC,aAAA;IAAA,EA6oE82vB;EAAE;AACv9vB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/oEqGpC,EAAE,CAAAqC,iBAAA,CA+oEXyG,aAAa,EAAc,CAAC;IAC5GnH,IAAI,EAAExB,SAAS;IACfmC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BikB,UAAU,EAAE,IAAI;MAChBzkB,QAAQ,EAAE,EAAE;MACZglB,SAAS,EAAE,CAAC1f,yBAAyB,EAAEhE,gCAAgC,CAAC;MACxE;MACAlB,aAAa,EAAE3B,iBAAiB,CAACwmB;IACrC,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAErlB,IAAI,EAAE3B,EAAE,CAACmmB;EAAW,CAAC,EAAE;IAAExkB,IAAI,EAAE3B,EAAE,CAACE;EAAiB,CAAC,EAAE;IAAEyB,IAAI,EAAE0F;EAA0B,CAAC,EAAE;IAAE1F,IAAI,EAAE0B;EAAiC,CAAC,CAAC,EAAkB;IAAEghB,WAAW,EAAE,CAAC;MAC7L1iB,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEkkB,OAAO,EAAE,CAAC;MACVhjB,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEiJ,SAAS,EAAE,CAAC;MACZ/H,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEkJ,OAAO,EAAE,CAAC;MACVhI,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEmJ,mBAAmB,EAAE,CAAC;MACtBjI,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsJ,2BAA2B,EAAE,CAAC;MAC9BlI,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuJ,8BAA8B,EAAE,CAAC;MACjCnI,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwJ,UAAU,EAAE,CAAC;MACbpI,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEuJ,gBAAgB,EAAE,CAAC;MACnBrI,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0J,qBAAqB,EAAE,CAAC;MACxBtI,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2J,cAAc,EAAE,CAAC;MACjBvI,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE0J,gBAAgB,EAAE,CAAC;MACnBxI,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE2J,gBAAgB,EAAE,CAAC;MACnBzI,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE4J,iBAAiB,EAAE,CAAC;MACpB1I,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+J,eAAe,EAAE,CAAC;MAClB3I,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE8J,kBAAkB,EAAE,CAAC;MACrB5I,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiK,WAAW,EAAE,CAAC;MACd7I,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEgK,sBAAsB,EAAE,CAAC;MACzB9I,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmK,2BAA2B,EAAE,CAAC;MAC9B/I,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoK,kBAAkB,EAAE,CAAC;MACrBhJ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEmK,2BAA2B,EAAE,CAAC;MAC9BjJ,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsK,4BAA4B,EAAE,CAAC;MAC/BlJ,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuK,4BAA4B,EAAE,CAAC;MAC/BnJ,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwK,sBAAsB,EAAE,CAAC;MACzBpJ,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyK,oBAAoB,EAAE,CAAC;MACvBrJ,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0K,sBAAsB,EAAE,CAAC;MACzBtJ,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2K,UAAU,EAAE,CAAC;MACbvJ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE0K,aAAa,EAAE,CAAC;MAChBxJ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE2K,kBAAkB,EAAE,CAAC;MACrBzJ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE4K,WAAW,EAAE,CAAC;MACd1J,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE6K,mBAAmB,EAAE,CAAC;MACtB3J,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE8K,mBAAmB,EAAE,CAAC;MACtB5J,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiL,4BAA4B,EAAE,CAAC;MAC/B7J,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkL,wBAAwB,EAAE,CAAC;MAC3B9J,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmL,YAAY,EAAE,CAAC;MACf/J,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEkL,iBAAiB,EAAE,CAAC;MACpBhK,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEmL,qBAAqB,EAAE,CAAC;MACxBjK,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEoL,iBAAiB,EAAE,CAAC;MACpBlK,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEqL,sBAAsB,EAAE,CAAC;MACzBnK,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEsL,6BAA6B,EAAE,CAAC;MAChCpK,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyL,sBAAsB,EAAE,CAAC;MACzBrK,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0L,2BAA2B,EAAE,CAAC;MAC9BtK,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2L,8BAA8B,EAAE,CAAC;MACjCvK,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4L,6BAA6B,EAAE,CAAC;MAChCxK,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6L,oCAAoC,EAAE,CAAC;MACvCzK,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE4L,qCAAqC,EAAE,CAAC;MACxC1K,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+L,4BAA4B,EAAE,CAAC;MAC/B3K,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgM,gBAAgB,EAAE,CAAC;MACnB5K,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE+L,gBAAgB,EAAE,CAAC;MACnB7K,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkM,eAAe,EAAE,CAAC;MAClB9K,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEiM,oBAAoB,EAAE,CAAC;MACvB/K,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoM,gBAAgB,EAAE,CAAC;MACnBhL,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEmM,UAAU,EAAE,CAAC;MACbjL,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEoM,QAAQ,EAAE,CAAC;MACXlL,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEqM,eAAe,EAAE,CAAC;MAClBnL,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwM,iBAAiB,EAAE,CAAC;MACpBpL,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyM,YAAY,EAAE,CAAC;MACfrL,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0M,6BAA6B,EAAE,CAAC;MAChCtL,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2M,wBAAwB,EAAE,CAAC;MAC3BvL,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4M,iCAAiC,EAAE,CAAC;MACpCxL,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6M,4BAA4B,EAAE,CAAC;MAC/BzL,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE8M,mBAAmB,EAAE,CAAC;MACtB1L,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+M,wBAAwB,EAAE,CAAC;MAC3B3L,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE8M,sBAAsB,EAAE,CAAC;MACzB5L,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE+M,iBAAiB,EAAE,CAAC;MACpB7L,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkN,wBAAwB,EAAE,CAAC;MAC3B9L,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEiN,mBAAmB,EAAE,CAAC;MACtB/L,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoN,WAAW,EAAE,CAAC;MACdhM,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEmN,eAAe,EAAE,CAAC;MAClBjM,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEoN,WAAW,EAAE,CAAC;MACdlM,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEqN,eAAe,EAAE,CAAC;MAClBnM,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEsN,gBAAgB,EAAE,CAAC;MACnBpM,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyN,iCAAiC,EAAE,CAAC;MACpCrM,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0N,iBAAiB,EAAE,CAAC;MACpBtM,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEyN,kBAAkB,EAAE,CAAC;MACrBvM,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE0N,gCAAgC,EAAE,CAAC;MACnCxM,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6N,oCAAoC,EAAE,CAAC;MACvCzM,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE8N,oBAAoB,EAAE,CAAC;MACvB1M,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+N,gBAAgB,EAAE,CAAC;MACnB3M,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE8N,oCAAoC,EAAE,CAAC;MACvC5M,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiO,oBAAoB,EAAE,CAAC;MACvB7M,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEgO,2BAA2B,EAAE,CAAC;MAC9B9M,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEiO,0BAA0B,EAAE,CAAC;MAC7B/M,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoO,0BAA0B,EAAE,CAAC;MAC7BhN,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEqO,YAAY,EAAE,CAAC;MACfjN,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsO,WAAW,EAAE,CAAC;MACdlN,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEqO,iBAAiB,EAAE,CAAC;MACpBnN,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEsO,mBAAmB,EAAE,CAAC;MACtBpN,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEuO,kBAAkB,EAAE,CAAC;MACrBrN,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEwO,cAAc,EAAE,CAAC;MACjBtN,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEyO,mBAAmB,EAAE,CAAC;MACtBvN,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE0O,yBAAyB,EAAE,CAAC;MAC5BxN,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE2O,2BAA2B,EAAE,CAAC;MAC9BzN,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE4O,UAAU,EAAE,CAAC;MACb1N,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE6O,YAAY,EAAE,CAAC;MACf3N,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgP,cAAc,EAAE,CAAC;MACjB5N,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiP,mBAAmB,EAAE,CAAC;MACtB7N,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEgP,kBAAkB,EAAE,CAAC;MACrB9N,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEiP,wBAAwB,EAAE,CAAC;MAC3B/N,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEkP,eAAe,EAAE,CAAC;MAClBhO,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEmP,mBAAmB,EAAE,CAAC;MACtBjO,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsP,OAAO,EAAE,CAAC;MACVlO,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEqP,YAAY,EAAE,CAAC;MACfnO,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEsP,QAAQ,EAAE,CAAC;MACXpO,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEuP,SAAS,EAAE,CAAC;MACZrO,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEwP,UAAU,EAAE,CAAC;MACbtO,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2P,sBAAsB,EAAE,CAAC;MACzBvO,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4P,qBAAqB,EAAE,CAAC;MACxBxO,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6P,aAAa,EAAE,CAAC;MAChBzO,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE8P,yBAAyB,EAAE,CAAC;MAC5B1O,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+P,6BAA6B,EAAE,CAAC;MAChC3O,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgQ,0BAA0B,EAAE,CAAC;MAC7B5O,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiQ,uBAAuB,EAAE,CAAC;MAC1B7O,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkQ,KAAK,EAAE,CAAC;MACR9O,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmQ,OAAO,EAAE,CAAC;MACV/O,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoQ,sBAAsB,EAAE,CAAC;MACzBhP,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEmQ,uBAAuB,EAAE,CAAC;MAC1BjP,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEoQ,6BAA6B,EAAE,CAAC;MAChClP,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEqQ,sBAAsB,EAAE,CAAC;MACzBnP,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwQ,qBAAqB,EAAE,CAAC;MACxBpP,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEuQ,sBAAsB,EAAE,CAAC;MACzBrP,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEwQ,4BAA4B,EAAE,CAAC;MAC/BtP,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEyQ,qBAAqB,EAAE,CAAC;MACxBvP,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4Q,UAAU,EAAE,CAAC;MACbxP,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6Q,kBAAkB,EAAE,CAAC;MACrBzP,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE4Q,0BAA0B,EAAE,CAAC;MAC7B1P,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE6Q,sBAAsB,EAAE,CAAC;MACzB3P,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgR,iBAAiB,EAAE,CAAC;MACpB5P,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiR,uBAAuB,EAAE,CAAC;MAC1B7P,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkR,SAAS,EAAE,CAAC;MACZ9P,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmR,cAAc,EAAE,CAAC;MACjB/P,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEkR,wBAAwB,EAAE,CAAC;MAC3BhQ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEmR,oBAAoB,EAAE,CAAC;MACvBjQ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEoR,sBAAsB,EAAE,CAAC;MACzBlQ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEqR,cAAc,EAAE,CAAC;MACjBnQ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEsR,uBAAuB,EAAE,CAAC;MAC1BpQ,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyR,6BAA6B,EAAE,CAAC;MAChCrQ,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0R,iBAAiB,EAAE,CAAC;MACpBtQ,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2R,QAAQ,EAAE,CAAC;MACXvQ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE0R,uBAAuB,EAAE,CAAC;MAC1BxQ,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6R,0BAA0B,EAAE,CAAC;MAC7BzQ,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE8R,2BAA2B,EAAE,CAAC;MAC9B1Q,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+R,uBAAuB,EAAE,CAAC;MAC1B3Q,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgS,yCAAyC,EAAE,CAAC;MAC5C5Q,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiS,WAAW,EAAE,CAAC;MACd7Q,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkS,iBAAiB,EAAE,CAAC;MACpB9Q,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEiS,gBAAgB,EAAE,CAAC;MACnB/Q,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEkS,0BAA0B,EAAE,CAAC;MAC7BhR,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEqS,SAAS,EAAE,CAAC;MACZjR,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEoS,cAAc,EAAE,CAAC;MACjBlR,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuS,cAAc,EAAE,CAAC;MACjBnR,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwS,SAAS,EAAE,CAAC;MACZpR,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyS,4BAA4B,EAAE,CAAC;MAC/BrR,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0S,iCAAiC,EAAE,CAAC;MACpCtR,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2S,yBAAyB,EAAE,CAAC;MAC5BvR,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4S,cAAc,EAAE,CAAC;MACjBxR,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6S,eAAe,EAAE,CAAC;MAClBzR,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE8S,2BAA2B,EAAE,CAAC;MAC9B1R,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+S,gBAAgB,EAAE,CAAC;MACnB3R,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgT,eAAe,EAAE,CAAC;MAClB5R,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiT,WAAW,EAAE,CAAC;MACd7R,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEgT,yBAAyB,EAAE,CAAC;MAC5B9R,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEiT,+BAA+B,EAAE,CAAC;MAClC/R,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEkT,qBAAqB,EAAE,CAAC;MACxBhS,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEmT,2BAA2B,EAAE,CAAC;MAC9BjS,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEoT,kBAAkB,EAAE,CAAC;MACrBlS,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuT,gBAAgB,EAAE,CAAC;MACnBnS,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEsT,oBAAoB,EAAE,CAAC;MACvBpS,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEuT,kBAAkB,EAAE,CAAC;MACrBrS,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEwT,kBAAkB,EAAE,CAAC;MACrBtS,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2T,oBAAoB,EAAE,CAAC;MACvBvS,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4T,qBAAqB,EAAE,CAAC;MACxBxS,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE2T,iBAAiB,EAAE,CAAC;MACpBzS,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE4T,aAAa,EAAE,CAAC;MAChB1S,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE6T,aAAa,EAAE,CAAC;MAChB3S,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE8T,sBAAsB,EAAE,CAAC;MACzB5S,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE+T,wBAAwB,EAAE,CAAC;MAC3B7S,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkU,oBAAoB,EAAE,CAAC;MACvB9S,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmU,eAAe,EAAE,CAAC;MAClB/S,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoU,4BAA4B,EAAE,CAAC;MAC/BhT,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEmU,yBAAyB,EAAE,CAAC;MAC5BjT,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsU,+BAA+B,EAAE,CAAC;MAClClT,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuU,oBAAoB,EAAE,CAAC;MACvBnT,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwU,oBAAoB,EAAE,CAAC;MACvBpT,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyU,iBAAiB,EAAE,CAAC;MACpBrT,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEwU,gBAAgB,EAAE,CAAC;MACnBtT,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEyU,sBAAsB,EAAE,CAAC;MACzBvT,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE0U,QAAQ,EAAE,CAAC;MACXxT,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6U,qBAAqB,EAAE,CAAC;MACxBzT,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE4U,qBAAqB,EAAE,CAAC;MACxB1T,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE6U,yBAAyB,EAAE,CAAC;MAC5B3T,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgV,uBAAuB,EAAE,CAAC;MAC1B5T,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiV,gBAAgB,EAAE,CAAC;MACnB7T,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEgV,mBAAmB,EAAE,CAAC;MACtB9T,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEiV,gBAAgB,EAAE,CAAC;MACnB/T,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEkV,aAAa,EAAE,CAAC;MAChBhU,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEmV,WAAW,EAAE,CAAC;MACdjU,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEoV,YAAY,EAAE,CAAC;MACflU,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEqV,OAAO,EAAE,CAAC;MACVnU,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEsV,0BAA0B,EAAE,CAAC;MAC7BpU,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEuV,yCAAyC,EAAE,CAAC;MAC5CrU,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0V,UAAU,EAAE,CAAC;MACbtU,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEyV,iBAAiB,EAAE,CAAC;MACpBvU,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE0V,uBAAuB,EAAE,CAAC;MAC1BxU,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE2V,yBAAyB,EAAE,CAAC;MAC5BzU,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE4V,qCAAqC,EAAE,CAAC;MACxC1U,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+V,cAAc,EAAE,CAAC;MACjB3U,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE8V,gBAAgB,EAAE,CAAC;MACnB5U,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE+V,+BAA+B,EAAE,CAAC;MAClC7U,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEgW,uBAAuB,EAAE,CAAC;MAC1B9U,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEiW,mBAAmB,EAAE,CAAC;MACtB/U,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoW,oBAAoB,EAAE,CAAC;MACvBhV,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEmW,uBAAuB,EAAE,CAAC;MAC1BjV,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsW,8BAA8B,EAAE,CAAC;MACjClV,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuW,mCAAmC,EAAE,CAAC;MACtCnV,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwW,mCAAmC,EAAE,CAAC;MACtCpV,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEuW,kBAAkB,EAAE,CAAC;MACrBrV,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEwW,wBAAwB,EAAE,CAAC;MAC3BtV,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEyW,0BAA0B,EAAE,CAAC;MAC7BvV,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE0W,0BAA0B,EAAE,CAAC;MAC7BxV,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6W,wBAAwB,EAAE,CAAC;MAC3BzV,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE8W,yBAAyB,EAAE,CAAC;MAC5B1V,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE+W,wBAAwB,EAAE,CAAC;MAC3B3V,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgX,uBAAuB,EAAE,CAAC;MAC1B5V,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiX,+BAA+B,EAAE,CAAC;MAClC7V,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkX,sBAAsB,EAAE,CAAC;MACzB9V,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmX,0BAA0B,EAAE,CAAC;MAC7B/V,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoX,kCAAkC,EAAE,CAAC;MACrChW,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEqX,cAAc,EAAE,CAAC;MACjBjW,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEoX,YAAY,EAAE,CAAC;MACflW,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEqX,aAAa,EAAE,CAAC;MAChBnW,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEsX,uBAAuB,EAAE,CAAC;MAC1BpW,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyX,sBAAsB,EAAE,CAAC;MACzBrW,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0X,yBAAyB,EAAE,CAAC;MAC5BtW,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2X,iBAAiB,EAAE,CAAC;MACpBvW,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4X,mBAAmB,EAAE,CAAC;MACtBxW,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6X,kBAAkB,EAAE,CAAC;MACrBzW,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE4X,UAAU,EAAE,CAAC;MACb1W,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE6X,2BAA2B,EAAE,CAAC;MAC9B3W,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgY,uBAAuB,EAAE,CAAC;MAC1B5W,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiY,oBAAoB,EAAE,CAAC;MACvB7W,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkY,iBAAiB,EAAE,CAAC;MACpB9W,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmY,gBAAgB,EAAE,CAAC;MACnB/W,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoY,mBAAmB,EAAE,CAAC;MACtBhX,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEmY,4BAA4B,EAAE,CAAC;MAC/BjX,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsY,YAAY,EAAE,CAAC;MACflX,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEqY,YAAY,EAAE,CAAC;MACfnX,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwY,UAAU,EAAE,CAAC;MACbpX,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyY,iBAAiB,EAAE,CAAC;MACpBrX,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0Y,eAAe,EAAE,CAAC;MAClBtX,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2Y,YAAY,EAAE,CAAC;MACfvX,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE0Y,6BAA6B,EAAE,CAAC;MAChCxX,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6Y,KAAK,EAAE,CAAC;MACRzX,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE4Y,SAAS,EAAE,CAAC;MACZ1X,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE6Y,QAAQ,EAAE,CAAC;MACX3X,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE8Y,QAAQ,EAAE,CAAC;MACX5X,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE+Y,aAAa,EAAE,CAAC;MAChB7X,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEgZ,yBAAyB,EAAE,CAAC;MAC5B9X,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmZ,oBAAoB,EAAE,CAAC;MACvB/X,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoZ,oBAAoB,EAAE,CAAC;MACvBhY,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEqZ,MAAM,EAAE,CAAC;MACTjY,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEoZ,SAAS,EAAE,CAAC;MACZlY,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuZ,mBAAmB,EAAE,CAAC;MACtBnY,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEsZ,eAAe,EAAE,CAAC;MAClBpY,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyZ,YAAY,EAAE,CAAC;MACfrY,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEwZ,KAAK,EAAE,CAAC;MACRtY,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEyZ,oBAAoB,EAAE,CAAC;MACvBvY,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4Z,aAAa,EAAE,CAAC;MAChBxY,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE2Z,UAAU,EAAE,CAAC;MACbzY,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE4Z,mBAAmB,EAAE,CAAC;MACtB1Y,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE6Z,mBAAmB,EAAE,CAAC;MACtB3Y,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE8Z,gBAAgB,EAAE,CAAC;MACnB5Y,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE+Z,gBAAgB,EAAE,CAAC;MACnB7Y,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEga,sBAAsB,EAAE,CAAC;MACzB9Y,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEia,uBAAuB,EAAE,CAAC;MAC1B/Y,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEka,yBAAyB,EAAE,CAAC;MAC5BhZ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEma,8BAA8B,EAAE,CAAC;MACjCjZ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEoa,wBAAwB,EAAE,CAAC;MAC3BlZ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEqa,eAAe,EAAE,CAAC;MAClBnZ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEsa,wBAAwB,EAAE,CAAC;MAC3BpZ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEua,uBAAuB,EAAE,CAAC;MAC1BrZ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEwa,sBAAsB,EAAE,CAAC;MACzBtZ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEya,oBAAoB,EAAE,CAAC;MACvBvZ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE0a,oBAAoB,EAAE,CAAC;MACvBxZ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE2a,qBAAqB,EAAE,CAAC;MACxBzZ,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE4a,oBAAoB,EAAE,CAAC;MACvB1Z,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE6a,eAAe,EAAE,CAAC;MAClB3Z,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE8a,kBAAkB,EAAE,CAAC;MACrB5Z,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE+a,aAAa,EAAE,CAAC;MAChB7Z,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEgb,aAAa,EAAE,CAAC;MAChB9Z,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEib,WAAW,EAAE,CAAC;MACd/Z,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEkb,yBAAyB,EAAE,CAAC;MAC5Bha,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEmb,cAAc,EAAE,CAAC;MACjBja,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEob,oBAAoB,EAAE,CAAC;MACvBla,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEqb,2BAA2B,EAAE,CAAC;MAC9Bna,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEsb,wBAAwB,EAAE,CAAC;MAC3Bpa,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEub,6BAA6B,EAAE,CAAC;MAChCra,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEwb,WAAW,EAAE,CAAC;MACdta,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEyb,aAAa,EAAE,CAAC;MAChBva,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE0b,6BAA6B,EAAE,CAAC;MAChCxa,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE2b,8BAA8B,EAAE,CAAC;MACjCza,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE4b,4BAA4B,EAAE,CAAC;MAC/B1a,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE6b,iBAAiB,EAAE,CAAC;MACpB3a,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE8b,qBAAqB,EAAE,CAAC;MACxB5a,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE+b,qBAAqB,EAAE,CAAC;MACxB7a,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEgc,QAAQ,EAAE,CAAC;MACX9a,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEic,oBAAoB,EAAE,CAAC;MACvB/a,IAAI,EAAElB,KAAK;MACX6B,IAAI,EAAE,CAAC;QAAE2kB,SAAS,EAAE1mB;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoc,oBAAoB,EAAE,CAAC;MACvBhb,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEmc,eAAe,EAAE,CAAC;MAClBjb,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEoc,WAAW,EAAE,CAAC;MACdlb,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEqc,aAAa,EAAE,CAAC;MAChBnb,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEsc,YAAY,EAAE,CAAC;MACfpb,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEuc,WAAW,EAAE,CAAC;MACdrb,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEwc,WAAW,EAAE,CAAC;MACdtb,IAAI,EAAElB;IACV,CAAC,CAAC;IAAEyc,YAAY,EAAE,CAAC;MACfvb,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE0c,cAAc,EAAE,CAAC;MACjBxb,IAAI,EAAElB;IACV,CAAC,CAAC;IAAE2c,uBAAuB,EAAE,CAAC;MAC1Bzb,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE2c,oBAAoB,EAAE,CAAC;MACvB1b,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE4c,wBAAwB,EAAE,CAAC;MAC3B3b,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE6c,yBAAyB,EAAE,CAAC;MAC5B5b,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE8c,QAAQ,EAAE,CAAC;MACX7b,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE+c,MAAM,EAAE,CAAC;MACT9b,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEgd,UAAU,EAAE,CAAC;MACb/b,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEid,QAAQ,EAAE,CAAC;MACXhc,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEkd,aAAa,EAAE,CAAC;MAChBjc,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEmd,YAAY,EAAE,CAAC;MACflc,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEod,aAAa,EAAE,CAAC;MAChBnc,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEqd,WAAW,EAAE,CAAC;MACdpc,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEsd,kBAAkB,EAAE,CAAC;MACrBrc,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEud,sBAAsB,EAAE,CAAC;MACzBtc,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEwd,kBAAkB,EAAE,CAAC;MACrBvc,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEyd,iBAAiB,EAAE,CAAC;MACpBxc,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE0d,gBAAgB,EAAE,CAAC;MACnBzc,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE2d,kBAAkB,EAAE,CAAC;MACrB1c,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE4d,uBAAuB,EAAE,CAAC;MAC1B3c,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE6d,qBAAqB,EAAE,CAAC;MACxB5c,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE8d,uBAAuB,EAAE,CAAC;MAC1B7c,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE+d,qBAAqB,EAAE,CAAC;MACxB9c,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEge,sBAAsB,EAAE,CAAC;MACzB/c,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEie,mBAAmB,EAAE,CAAC;MACtBhd,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEke,uBAAuB,EAAE,CAAC;MAC1Bjd,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEme,qBAAqB,EAAE,CAAC;MACxBld,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEoe,gBAAgB,EAAE,CAAC;MACnBnd,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEqe,eAAe,EAAE,CAAC;MAClBpd,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEse,eAAe,EAAE,CAAC;MAClBrd,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEue,kBAAkB,EAAE,CAAC;MACrBtd,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEwe,kBAAkB,EAAE,CAAC;MACrBvd,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEye,iBAAiB,EAAE,CAAC;MACpBxd,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE0e,iBAAiB,EAAE,CAAC;MACpBzd,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE2e,WAAW,EAAE,CAAC;MACd1d,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE4e,SAAS,EAAE,CAAC;MACZ3d,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE6e,WAAW,EAAE,CAAC;MACd5d,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE8e,SAAS,EAAE,CAAC;MACZ7d,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE+e,wBAAwB,EAAE,CAAC;MAC3B9d,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEgf,sBAAsB,EAAE,CAAC;MACzB/d,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEif,gBAAgB,EAAE,CAAC;MACnBhe,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEkf,cAAc,EAAE,CAAC;MACjBje,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEmf,SAAS,EAAE,CAAC;MACZle,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEof,OAAO,EAAE,CAAC;MACVne,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEqf,YAAY,EAAE,CAAC;MACfpe,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEsf,aAAa,EAAE,CAAC;MAChBre,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEuf,cAAc,EAAE,CAAC;MACjBte,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEwf,mCAAmC,EAAE,CAAC;MACtCve,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEyf,WAAW,EAAE,CAAC;MACdxe,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE0f,YAAY,EAAE,CAAC;MACfze,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE2f,0BAA0B,EAAE,CAAC;MAC7B1e,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE4f,mBAAmB,EAAE,CAAC;MACtB3e,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE6f,cAAc,EAAE,CAAC;MACjB5e,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE8f,WAAW,EAAE,CAAC;MACd7e,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE+f,SAAS,EAAE,CAAC;MACZ9e,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEggB,iBAAiB,EAAE,CAAC;MACpB/e,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEigB,eAAe,EAAE,CAAC;MAClBhf,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEkgB,YAAY,EAAE,CAAC;MACfjf,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEmgB,iBAAiB,EAAE,CAAC;MACpBlf,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEogB,eAAe,EAAE,CAAC;MAClBnf,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEqgB,UAAU,EAAE,CAAC;MACbpf,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEsgB,aAAa,EAAE,CAAC;MAChBrf,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEugB,WAAW,EAAE,CAAC;MACdtf,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEwgB,WAAW,EAAE,CAAC;MACdvf,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEygB,aAAa,EAAE,CAAC;MAChBxf,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE0gB,YAAY,EAAE,CAAC;MACfzf,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE2gB,iBAAiB,EAAE,CAAC;MACpB1f,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE4gB,YAAY,EAAE,CAAC;MACf3f,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE6gB,WAAW,EAAE,CAAC;MACd5f,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE8gB,YAAY,EAAE,CAAC;MACf7f,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE+gB,UAAU,EAAE,CAAC;MACb9f,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEghB,aAAa,EAAE,CAAC;MAChB/f,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEihB,gBAAgB,EAAE,CAAC;MACnBhgB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEkhB,cAAc,EAAE,CAAC;MACjBjgB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEmhB,qBAAqB,EAAE,CAAC;MACxBlgB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEohB,cAAc,EAAE,CAAC;MACjBngB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEqhB,mBAAmB,EAAE,CAAC;MACtBpgB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEshB,uBAAuB,EAAE,CAAC;MAC1BrgB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEuhB,oBAAoB,EAAE,CAAC;MACvBtgB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEwhB,iBAAiB,EAAE,CAAC;MACpBvgB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEyhB,cAAc,EAAE,CAAC;MACjBxgB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE0hB,wBAAwB,EAAE,CAAC;MAC3BzgB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE2hB,cAAc,EAAE,CAAC;MACjB1gB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE4hB,aAAa,EAAE,CAAC;MAChB3gB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE6hB,WAAW,EAAE,CAAC;MACd5gB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE8hB,iBAAiB,EAAE,CAAC;MACpB7gB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE+hB,WAAW,EAAE,CAAC;MACd9gB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEgiB,aAAa,EAAE,CAAC;MAChB/gB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEiiB,YAAY,EAAE,CAAC;MACfhhB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEkiB,aAAa,EAAE,CAAC;MAChBjhB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEmiB,UAAU,EAAE,CAAC;MACblhB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEoiB,gBAAgB,EAAE,CAAC;MACnBnhB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEqiB,WAAW,EAAE,CAAC;MACdphB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEsiB,gBAAgB,EAAE,CAAC;MACnBrhB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEuiB,eAAe,EAAE,CAAC;MAClBthB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEwiB,qBAAqB,EAAE,CAAC;MACxBvhB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAEyiB,oBAAoB,EAAE,CAAC;MACvBxhB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE0iB,WAAW,EAAE,CAAC;MACdzhB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE2iB,WAAW,EAAE,CAAC;MACd1hB,IAAI,EAAEjB;IACV,CAAC,CAAC;IAAE4iB,WAAW,EAAE,CAAC;MACd3hB,IAAI,EAAEjB;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMwmB,uBAAuB,GAAG,IAAI9jB,GAAG,CAAClC,2BAA2B,CAAC;AACpE;AACA;AACA;AACA;AACA;AACA,SAASijB,sBAAsBA,CAACL,GAAG,EAAEI,UAAU,EAAE;EAC7C,IAAIgD,uBAAuB,CAACtiB,GAAG,CAACkf,GAAG,CAAC,EAAE;IAClC;IACA;IACA,OAAOI,UAAU,KAAK,EAAE,GAAG,IAAI,GAAGA,UAAU,KAAK,OAAO,GAAG,KAAK,GAAGA,UAAU;EACjF;EACA,OAAOA,UAAU;AACrB;AAEA,MAAMiD,YAAY,CAAC;EACf;IAAS,IAAI,CAAC7lB,IAAI,YAAA8lB,qBAAA5lB,CAAA;MAAA,YAAAA,CAAA,IAAyF2lB,YAAY;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBAnnG+ErnB,EAAE,CAAAsnB,gBAAA;MAAA3lB,IAAA,EAmnGSwlB,YAAY;MAAAI,OAAA,GAAYze,aAAa;MAAA0e,OAAA,GAAa1e,aAAa;IAAA,EAAI;EAAE;EACjL;IAAS,IAAI,CAAC2e,IAAI,kBApnG+EznB,EAAE,CAAA0nB,gBAAA,IAonGwB;EAAE;AACjI;AACA;EAAA,QAAAtlB,SAAA,oBAAAA,SAAA,KAtnGqGpC,EAAE,CAAAqC,iBAAA,CAsnGX8kB,YAAY,EAAc,CAAC;IAC3GxlB,IAAI,EAAEhB,QAAQ;IACd2B,IAAI,EAAE,CAAC;MACCilB,OAAO,EAAE,CAACze,aAAa,CAAC;MACxB0e,OAAO,EAAE,CAAC1e,aAAa;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS3H,oBAAoB,EAAE2H,aAAa,EAAEqe,YAAY,EAAE9jB,gCAAgC,EAAEgE,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}