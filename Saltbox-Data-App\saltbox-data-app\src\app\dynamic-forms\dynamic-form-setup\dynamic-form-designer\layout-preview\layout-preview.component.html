<div class="card grid">
    <div class="col-12 p-0">
        <div class="grid header-wrap">
            <div [ngClass]="{'mr-1': dynamicForm.layout?.headerLeft?.visible && dynamicForm.layout?.headerRight?.visible}"
                class="col pb-0 form-layout-header-text-editor" *ngIf="dynamicForm.layout?.headerLeft?.visible">
                <app-dynamic-form-section [dynamicForm]="dynamicForm" (contentChooserClick)="onClickContentChooser($event)"
                    [layoutArea]="DynamicFormAreas.HeaderLeft" [isPreview]="true" [disabled]="disabled" class="col-12">
                </app-dynamic-form-section>
            </div>

            <div class="col pb-0 form-layout-header-text-editor" *ngIf="dynamicForm.layout?.headerRight?.visible">
                <app-dynamic-form-section [dynamicForm]="dynamicForm" (contentChooserClick)="onClickContentChooser($event)"
                    [layoutArea]="DynamicFormAreas.HeaderRight" [isPreview]="true" [disabled]="disabled" class="col-12">
                </app-dynamic-form-section>
            </div>
        </div>
    </div>

    <app-dynamic-form-section class="col-12 drag-outline" *ngIf="dynamicForm.layout?.instruction?.visible"
        [dynamicForm]="dynamicForm" (contentChooserClick)="onClickContentChooser($event)"
        [layoutArea]="DynamicFormAreas.Instruction" [isPreview]="true" [disabled]="disabled">
    </app-dynamic-form-section>

    <div class="drag-outline col-12 assets-dropzone" pDroppable (onDrop)="drop()">
        <div class="section-controls">
            <span>Drag and Drop Form Elements Here</span>
        </div>
        <div *ngIf="datastore" [ngClass]="{'disabled-element': dynamicForm.customFieldsConfig}">
            <app-form-elements-renderer [(fieldsConfig)]="this.dynamicForm.fieldsConfig" [disabled]="disabled"
                [baseSchema]="datastore.baseSchema" [draggedField]="draggedField"></app-form-elements-renderer>
        </div>
    </div>
    <app-dynamic-form-section class="col-12 drag-outline" *ngIf="dynamicForm.layout?.footer?.visible"
        [dynamicForm]="dynamicForm" (contentChooserClick)="onClickContentChooser($event)"
        [layoutArea]="DynamicFormAreas.Footer" [isPreview]="true" [disabled]="disabled">
    </app-dynamic-form-section>
</div>

<app-form-field-editor *ngIf="showFieldEditor" [(visible)]="showFieldEditor"
    [baseProperty]="datastore?.baseSchema?.properties[selectedField?.key]" [(field)]="selectedField"
    (fieldChange)="updateField($event)" [disabled]="disabled">
</app-form-field-editor>