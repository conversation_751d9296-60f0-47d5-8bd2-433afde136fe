{"ast": null, "code": "const heading = {\n  color: {\n    value: '{colors.font.primary.value}'\n  },\n  lineHeight: {\n    value: '{lineHeights.small.value}'\n  },\n  1: {\n    fontSize: {\n      value: '{fontSizes.xxxxl.value}'\n    },\n    fontWeight: {\n      value: '{fontWeights.light.value}'\n    }\n  },\n  2: {\n    fontSize: {\n      value: '{fontSizes.xxxl.value}'\n    },\n    fontWeight: {\n      value: '{fontWeights.normal.value}'\n    }\n  },\n  3: {\n    fontSize: {\n      value: '{fontSizes.xxl.value}'\n    },\n    fontWeight: {\n      value: '{fontWeights.medium.value}'\n    }\n  },\n  4: {\n    fontSize: {\n      value: '{fontSizes.xl.value}'\n    },\n    fontWeight: {\n      value: '{fontWeights.semibold.value}'\n    }\n  },\n  5: {\n    fontSize: {\n      value: '{fontSizes.large.value}'\n    },\n    fontWeight: {\n      value: '{fontWeights.bold.value}'\n    }\n  },\n  6: {\n    fontSize: {\n      value: '{fontSizes.medium.value}'\n    },\n    fontWeight: {\n      value: '{fontWeights.extrabold.value}'\n    }\n  }\n};\nexport { heading };", "map": {"version": 3, "names": ["heading", "color", "value", "lineHeight", "fontSize", "fontWeight"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/heading.mjs"], "sourcesContent": ["const heading = {\n    color: { value: '{colors.font.primary.value}' },\n    lineHeight: { value: '{lineHeights.small.value}' },\n    1: {\n        fontSize: { value: '{fontSizes.xxxxl.value}' },\n        fontWeight: { value: '{fontWeights.light.value}' },\n    },\n    2: {\n        fontSize: { value: '{fontSizes.xxxl.value}' },\n        fontWeight: { value: '{fontWeights.normal.value}' },\n    },\n    3: {\n        fontSize: { value: '{fontSizes.xxl.value}' },\n        fontWeight: { value: '{fontWeights.medium.value}' },\n    },\n    4: {\n        fontSize: { value: '{fontSizes.xl.value}' },\n        fontWeight: { value: '{fontWeights.semibold.value}' },\n    },\n    5: {\n        fontSize: { value: '{fontSizes.large.value}' },\n        fontWeight: { value: '{fontWeights.bold.value}' },\n    },\n    6: {\n        fontSize: { value: '{fontSizes.medium.value}' },\n        fontWeight: { value: '{fontWeights.extrabold.value}' },\n    },\n};\n\nexport { heading };\n"], "mappings": "AAAA,MAAMA,OAAO,GAAG;EACZC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAA8B,CAAC;EAC/CC,UAAU,EAAE;IAAED,KAAK,EAAE;EAA4B,CAAC;EAClD,CAAC,EAAE;IACCE,QAAQ,EAAE;MAAEF,KAAK,EAAE;IAA0B,CAAC;IAC9CG,UAAU,EAAE;MAAEH,KAAK,EAAE;IAA4B;EACrD,CAAC;EACD,CAAC,EAAE;IACCE,QAAQ,EAAE;MAAEF,KAAK,EAAE;IAAyB,CAAC;IAC7CG,UAAU,EAAE;MAAEH,KAAK,EAAE;IAA6B;EACtD,CAAC;EACD,CAAC,EAAE;IACCE,QAAQ,EAAE;MAAEF,KAAK,EAAE;IAAwB,CAAC;IAC5CG,UAAU,EAAE;MAAEH,KAAK,EAAE;IAA6B;EACtD,CAAC;EACD,CAAC,EAAE;IACCE,QAAQ,EAAE;MAAEF,KAAK,EAAE;IAAuB,CAAC;IAC3CG,UAAU,EAAE;MAAEH,KAAK,EAAE;IAA+B;EACxD,CAAC;EACD,CAAC,EAAE;IACCE,QAAQ,EAAE;MAAEF,KAAK,EAAE;IAA0B,CAAC;IAC9CG,UAAU,EAAE;MAAEH,KAAK,EAAE;IAA2B;EACpD,CAAC;EACD,CAAC,EAAE;IACCE,QAAQ,EAAE;MAAEF,KAAK,EAAE;IAA2B,CAAC;IAC/CG,UAAU,EAAE;MAAEH,KAAK,EAAE;IAAgC;EACzD;AACJ,CAAC;AAED,SAASF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}