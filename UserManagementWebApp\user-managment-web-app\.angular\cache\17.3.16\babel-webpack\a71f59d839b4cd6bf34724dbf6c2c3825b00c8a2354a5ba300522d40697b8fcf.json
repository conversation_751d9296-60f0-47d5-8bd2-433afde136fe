{"ast": null, "code": "import { alert } from './alert.mjs';\nimport { aiConversation } from './aiConversation.mjs';\nimport { autocomplete } from './autocomplete.mjs';\nimport { authenticator } from './authenticator.mjs';\nimport { avatar } from './avatar.mjs';\nimport { badge } from './badge.mjs';\nimport { breadcrumbs } from './breadcrumbs.mjs';\nimport { button } from './button.mjs';\nimport { card } from './card.mjs';\nimport { checkbox } from './checkbox.mjs';\nimport { checkboxfield } from './checkboxField.mjs';\nimport { collection } from './collection.mjs';\nimport { copy } from './copy.mjs';\nimport { dialcodeselect } from './dialCodeSelect.mjs';\nimport { divider } from './divider.mjs';\nimport { dropzone } from './dropZone.mjs';\nimport { accordion } from './accordion.mjs';\nimport { field } from './field.mjs';\nimport { fieldcontrol } from './fieldControl.mjs';\nimport { fieldgroup } from './fieldGroup.mjs';\nimport { fieldset } from './fieldset.mjs';\nimport { fieldmessages } from './fieldMessages.mjs';\nimport { fileuploader } from './fileuploader.mjs';\nimport { flex } from './flex.mjs';\nimport { heading } from './heading.mjs';\nimport { highlightmatch } from './highlightMatch.mjs';\nimport { icon } from './icon.mjs';\nimport { input } from './input.mjs';\nimport { image } from './image.mjs';\nimport { inappmessaging } from './inAppMessaging.mjs';\nimport { link } from './link.mjs';\nimport { liveness } from './liveness.mjs';\nimport { loader } from './loader.mjs';\nimport { menu } from './menu.mjs';\nimport { message } from './message.mjs';\nimport { pagination } from './pagination.mjs';\nimport { passwordfield } from './passwordField.mjs';\nimport { phonenumberfield } from './phoneNumberField.mjs';\nimport { placeholder } from './placeholder.mjs';\nimport { radio } from './radio.mjs';\nimport { radiogroup } from './radioGroup.mjs';\nimport { rating } from './rating.mjs';\nimport { searchfield } from './searchField.mjs';\nimport { select } from './select.mjs';\nimport { selectfield } from './selectField.mjs';\nimport { sliderfield } from './sliderField.mjs';\nimport { stepperfield } from './stepperField.mjs';\nimport { storagemanager } from './storagemanager.mjs';\nimport { switchfield } from './switchField.mjs';\nimport { table } from './table.mjs';\nimport { tabs } from './tabs.mjs';\nimport { text } from './text.mjs';\nimport { textareafield } from './textAreaField.mjs';\nimport { textfield } from './textField.mjs';\nimport { togglebutton } from './toggleButton.mjs';\nimport { togglebuttongroup } from './toggleButtonGroup.mjs';\nconst components = {\n  accordion,\n  aiConversation,\n  alert,\n  authenticator,\n  autocomplete,\n  avatar,\n  badge,\n  breadcrumbs,\n  button,\n  card,\n  checkbox,\n  checkboxfield,\n  collection,\n  copy,\n  countrycodeselect: dialcodeselect,\n  divider,\n  dropzone,\n  field,\n  fieldcontrol,\n  fieldgroup,\n  fieldmessages,\n  fieldset,\n  fileuploader,\n  flex,\n  heading,\n  icon,\n  highlightmatch,\n  image,\n  inappmessaging,\n  input,\n  link,\n  liveness,\n  loader,\n  menu,\n  message,\n  pagination,\n  passwordfield,\n  phonenumberfield,\n  placeholder,\n  radio,\n  radiogroup,\n  rating,\n  searchfield,\n  select,\n  selectfield,\n  sliderfield,\n  stepperfield,\n  storagemanager,\n  switchfield,\n  table,\n  tabs,\n  text,\n  textareafield,\n  textfield,\n  togglebutton,\n  togglebuttongroup\n};\nexport { components };", "map": {"version": 3, "names": ["alert", "aiConversation", "autocomplete", "authenticator", "avatar", "badge", "breadcrumbs", "button", "card", "checkbox", "checkboxfield", "collection", "copy", "dialcodeselect", "divider", "dropzone", "accordion", "field", "fieldcontrol", "fieldgroup", "fieldset", "fieldmessages", "fileuploader", "flex", "heading", "highlightmatch", "icon", "input", "image", "inappmessaging", "link", "liveness", "loader", "menu", "message", "pagination", "passwordfield", "phonenumberfield", "placeholder", "radio", "radiogroup", "rating", "searchfield", "select", "selectfield", "sliderfield", "stepper<PERSON>", "storagemanager", "switchfield", "table", "tabs", "text", "textareafield", "textfield", "to<PERSON><PERSON><PERSON>", "togglebuttongroup", "components", "countrycodeselect"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/index.mjs"], "sourcesContent": ["import { alert } from './alert.mjs';\nimport { aiConversation } from './aiConversation.mjs';\nimport { autocomplete } from './autocomplete.mjs';\nimport { authenticator } from './authenticator.mjs';\nimport { avatar } from './avatar.mjs';\nimport { badge } from './badge.mjs';\nimport { breadcrumbs } from './breadcrumbs.mjs';\nimport { button } from './button.mjs';\nimport { card } from './card.mjs';\nimport { checkbox } from './checkbox.mjs';\nimport { checkboxfield } from './checkboxField.mjs';\nimport { collection } from './collection.mjs';\nimport { copy } from './copy.mjs';\nimport { dialcodeselect } from './dialCodeSelect.mjs';\nimport { divider } from './divider.mjs';\nimport { dropzone } from './dropZone.mjs';\nimport { accordion } from './accordion.mjs';\nimport { field } from './field.mjs';\nimport { fieldcontrol } from './fieldControl.mjs';\nimport { fieldgroup } from './fieldGroup.mjs';\nimport { fieldset } from './fieldset.mjs';\nimport { fieldmessages } from './fieldMessages.mjs';\nimport { fileuploader } from './fileuploader.mjs';\nimport { flex } from './flex.mjs';\nimport { heading } from './heading.mjs';\nimport { highlightmatch } from './highlightMatch.mjs';\nimport { icon } from './icon.mjs';\nimport { input } from './input.mjs';\nimport { image } from './image.mjs';\nimport { inappmessaging } from './inAppMessaging.mjs';\nimport { link } from './link.mjs';\nimport { liveness } from './liveness.mjs';\nimport { loader } from './loader.mjs';\nimport { menu } from './menu.mjs';\nimport { message } from './message.mjs';\nimport { pagination } from './pagination.mjs';\nimport { passwordfield } from './passwordField.mjs';\nimport { phonenumberfield } from './phoneNumberField.mjs';\nimport { placeholder } from './placeholder.mjs';\nimport { radio } from './radio.mjs';\nimport { radiogroup } from './radioGroup.mjs';\nimport { rating } from './rating.mjs';\nimport { searchfield } from './searchField.mjs';\nimport { select } from './select.mjs';\nimport { selectfield } from './selectField.mjs';\nimport { sliderfield } from './sliderField.mjs';\nimport { stepperfield } from './stepperField.mjs';\nimport { storagemanager } from './storagemanager.mjs';\nimport { switchfield } from './switchField.mjs';\nimport { table } from './table.mjs';\nimport { tabs } from './tabs.mjs';\nimport { text } from './text.mjs';\nimport { textareafield } from './textAreaField.mjs';\nimport { textfield } from './textField.mjs';\nimport { togglebutton } from './toggleButton.mjs';\nimport { togglebuttongroup } from './toggleButtonGroup.mjs';\n\nconst components = {\n    accordion,\n    aiConversation,\n    alert,\n    authenticator,\n    autocomplete,\n    avatar,\n    badge,\n    breadcrumbs,\n    button,\n    card,\n    checkbox,\n    checkboxfield,\n    collection,\n    copy,\n    countrycodeselect: dialcodeselect,\n    divider,\n    dropzone,\n    field,\n    fieldcontrol,\n    fieldgroup,\n    fieldmessages,\n    fieldset,\n    fileuploader,\n    flex,\n    heading,\n    icon,\n    highlightmatch,\n    image,\n    inappmessaging,\n    input,\n    link,\n    liveness,\n    loader,\n    menu,\n    message,\n    pagination,\n    passwordfield,\n    phonenumberfield,\n    placeholder,\n    radio,\n    radiogroup,\n    rating,\n    searchfield,\n    select,\n    selectfield,\n    sliderfield,\n    stepperfield,\n    storagemanager,\n    switchfield,\n    table,\n    tabs,\n    text,\n    textareafield,\n    textfield,\n    togglebutton,\n    togglebuttongroup,\n};\n\nexport { components };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,aAAa;AACnC,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,iBAAiB,QAAQ,yBAAyB;AAE3D,MAAMC,UAAU,GAAG;EACfxC,SAAS;EACTf,cAAc;EACdD,KAAK;EACLG,aAAa;EACbD,YAAY;EACZE,MAAM;EACNC,KAAK;EACLC,WAAW;EACXC,MAAM;EACNC,IAAI;EACJC,QAAQ;EACRC,aAAa;EACbC,UAAU;EACVC,IAAI;EACJ6C,iBAAiB,EAAE5C,cAAc;EACjCC,OAAO;EACPC,QAAQ;EACRE,KAAK;EACLC,YAAY;EACZC,UAAU;EACVE,aAAa;EACbD,QAAQ;EACRE,YAAY;EACZC,IAAI;EACJC,OAAO;EACPE,IAAI;EACJD,cAAc;EACdG,KAAK;EACLC,cAAc;EACdF,KAAK;EACLG,IAAI;EACJC,QAAQ;EACRC,MAAM;EACNC,IAAI;EACJC,OAAO;EACPC,UAAU;EACVC,aAAa;EACbC,gBAAgB;EAChBC,WAAW;EACXC,KAAK;EACLC,UAAU;EACVC,MAAM;EACNC,WAAW;EACXC,MAAM;EACNC,WAAW;EACXC,WAAW;EACXC,YAAY;EACZC,cAAc;EACdC,WAAW;EACXC,KAAK;EACLC,IAAI;EACJC,IAAI;EACJC,aAAa;EACbC,SAAS;EACTC,YAAY;EACZC;AACJ,CAAC;AAED,SAASC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}