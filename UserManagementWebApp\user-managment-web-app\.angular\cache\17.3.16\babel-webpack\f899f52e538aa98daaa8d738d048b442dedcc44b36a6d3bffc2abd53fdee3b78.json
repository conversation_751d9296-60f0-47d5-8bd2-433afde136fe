{"ast": null, "code": "import { BLOCK_SIZE, DIGEST_LENGTH, INIT, KEY, MAX_HASHABLE_LENGTH } from \"./constants\";\n/**\n * @internal\n */\nvar RawSha256 = /** @class */function () {\n  function RawSha256() {\n    this.state = Int32Array.from(INIT);\n    this.temp = new Int32Array(64);\n    this.buffer = new Uint8Array(64);\n    this.bufferLength = 0;\n    this.bytesHashed = 0;\n    /**\n     * @internal\n     */\n    this.finished = false;\n  }\n  RawSha256.prototype.update = function (data) {\n    if (this.finished) {\n      throw new Error(\"Attempted to update an already finished hash.\");\n    }\n    var position = 0;\n    var byteLength = data.byteLength;\n    this.bytesHashed += byteLength;\n    if (this.bytesHashed * 8 > MAX_HASHABLE_LENGTH) {\n      throw new Error(\"Cannot hash more than 2^53 - 1 bits\");\n    }\n    while (byteLength > 0) {\n      this.buffer[this.bufferLength++] = data[position++];\n      byteLength--;\n      if (this.bufferLength === BLOCK_SIZE) {\n        this.hashBuffer();\n        this.bufferLength = 0;\n      }\n    }\n  };\n  RawSha256.prototype.digest = function () {\n    if (!this.finished) {\n      var bitsHashed = this.bytesHashed * 8;\n      var bufferView = new DataView(this.buffer.buffer, this.buffer.byteOffset, this.buffer.byteLength);\n      var undecoratedLength = this.bufferLength;\n      bufferView.setUint8(this.bufferLength++, 0x80);\n      // Ensure the final block has enough room for the hashed length\n      if (undecoratedLength % BLOCK_SIZE >= BLOCK_SIZE - 8) {\n        for (var i = this.bufferLength; i < BLOCK_SIZE; i++) {\n          bufferView.setUint8(i, 0);\n        }\n        this.hashBuffer();\n        this.bufferLength = 0;\n      }\n      for (var i = this.bufferLength; i < BLOCK_SIZE - 8; i++) {\n        bufferView.setUint8(i, 0);\n      }\n      bufferView.setUint32(BLOCK_SIZE - 8, Math.floor(bitsHashed / 0x100000000), true);\n      bufferView.setUint32(BLOCK_SIZE - 4, bitsHashed);\n      this.hashBuffer();\n      this.finished = true;\n    }\n    // The value in state is little-endian rather than big-endian, so flip\n    // each word into a new Uint8Array\n    var out = new Uint8Array(DIGEST_LENGTH);\n    for (var i = 0; i < 8; i++) {\n      out[i * 4] = this.state[i] >>> 24 & 0xff;\n      out[i * 4 + 1] = this.state[i] >>> 16 & 0xff;\n      out[i * 4 + 2] = this.state[i] >>> 8 & 0xff;\n      out[i * 4 + 3] = this.state[i] >>> 0 & 0xff;\n    }\n    return out;\n  };\n  RawSha256.prototype.hashBuffer = function () {\n    var _a = this,\n      buffer = _a.buffer,\n      state = _a.state;\n    var state0 = state[0],\n      state1 = state[1],\n      state2 = state[2],\n      state3 = state[3],\n      state4 = state[4],\n      state5 = state[5],\n      state6 = state[6],\n      state7 = state[7];\n    for (var i = 0; i < BLOCK_SIZE; i++) {\n      if (i < 16) {\n        this.temp[i] = (buffer[i * 4] & 0xff) << 24 | (buffer[i * 4 + 1] & 0xff) << 16 | (buffer[i * 4 + 2] & 0xff) << 8 | buffer[i * 4 + 3] & 0xff;\n      } else {\n        var u = this.temp[i - 2];\n        var t1_1 = (u >>> 17 | u << 15) ^ (u >>> 19 | u << 13) ^ u >>> 10;\n        u = this.temp[i - 15];\n        var t2_1 = (u >>> 7 | u << 25) ^ (u >>> 18 | u << 14) ^ u >>> 3;\n        this.temp[i] = (t1_1 + this.temp[i - 7] | 0) + (t2_1 + this.temp[i - 16] | 0);\n      }\n      var t1 = (((state4 >>> 6 | state4 << 26) ^ (state4 >>> 11 | state4 << 21) ^ (state4 >>> 25 | state4 << 7)) + (state4 & state5 ^ ~state4 & state6) | 0) + (state7 + (KEY[i] + this.temp[i] | 0) | 0) | 0;\n      var t2 = ((state0 >>> 2 | state0 << 30) ^ (state0 >>> 13 | state0 << 19) ^ (state0 >>> 22 | state0 << 10)) + (state0 & state1 ^ state0 & state2 ^ state1 & state2) | 0;\n      state7 = state6;\n      state6 = state5;\n      state5 = state4;\n      state4 = state3 + t1 | 0;\n      state3 = state2;\n      state2 = state1;\n      state1 = state0;\n      state0 = t1 + t2 | 0;\n    }\n    state[0] += state0;\n    state[1] += state1;\n    state[2] += state2;\n    state[3] += state3;\n    state[4] += state4;\n    state[5] += state5;\n    state[6] += state6;\n    state[7] += state7;\n  };\n  return RawSha256;\n}();\nexport { RawSha256 };", "map": {"version": 3, "names": ["BLOCK_SIZE", "DIGEST_LENGTH", "INIT", "KEY", "MAX_HASHABLE_LENGTH", "RawSha256", "state", "Int32Array", "from", "temp", "buffer", "Uint8Array", "bufferLength", "bytesHashed", "finished", "prototype", "update", "data", "Error", "position", "byteLength", "hash<PERSON><PERSON><PERSON>", "digest", "bitsHashed", "bufferView", "DataView", "byteOffset", "undecoratedLength", "setUint8", "i", "setUint32", "Math", "floor", "out", "_a", "state0", "state1", "state2", "state3", "state4", "state5", "state6", "state7", "u", "t1_1", "t2_1", "t1", "t2"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-crypto/sha256-js/build/module/RawSha256.js"], "sourcesContent": ["import { BLOCK_SIZE, DIGEST_LENGTH, INIT, KEY, MAX_HASHABLE_LENGTH } from \"./constants\";\n/**\n * @internal\n */\nvar RawSha256 = /** @class */ (function () {\n    function RawSha256() {\n        this.state = Int32Array.from(INIT);\n        this.temp = new Int32Array(64);\n        this.buffer = new Uint8Array(64);\n        this.bufferLength = 0;\n        this.bytesHashed = 0;\n        /**\n         * @internal\n         */\n        this.finished = false;\n    }\n    RawSha256.prototype.update = function (data) {\n        if (this.finished) {\n            throw new Error(\"Attempted to update an already finished hash.\");\n        }\n        var position = 0;\n        var byteLength = data.byteLength;\n        this.bytesHashed += byteLength;\n        if (this.bytesHashed * 8 > MAX_HASHABLE_LENGTH) {\n            throw new Error(\"Cannot hash more than 2^53 - 1 bits\");\n        }\n        while (byteLength > 0) {\n            this.buffer[this.bufferLength++] = data[position++];\n            byteLength--;\n            if (this.bufferLength === BLOCK_SIZE) {\n                this.hashBuffer();\n                this.bufferLength = 0;\n            }\n        }\n    };\n    RawSha256.prototype.digest = function () {\n        if (!this.finished) {\n            var bitsHashed = this.bytesHashed * 8;\n            var bufferView = new DataView(this.buffer.buffer, this.buffer.byteOffset, this.buffer.byteLength);\n            var undecoratedLength = this.bufferLength;\n            bufferView.setUint8(this.bufferLength++, 0x80);\n            // Ensure the final block has enough room for the hashed length\n            if (undecoratedLength % BLOCK_SIZE >= BLOCK_SIZE - 8) {\n                for (var i = this.bufferLength; i < BLOCK_SIZE; i++) {\n                    bufferView.setUint8(i, 0);\n                }\n                this.hashBuffer();\n                this.bufferLength = 0;\n            }\n            for (var i = this.bufferLength; i < BLOCK_SIZE - 8; i++) {\n                bufferView.setUint8(i, 0);\n            }\n            bufferView.setUint32(BLOCK_SIZE - 8, Math.floor(bitsHashed / 0x100000000), true);\n            bufferView.setUint32(BLOCK_SIZE - 4, bitsHashed);\n            this.hashBuffer();\n            this.finished = true;\n        }\n        // The value in state is little-endian rather than big-endian, so flip\n        // each word into a new Uint8Array\n        var out = new Uint8Array(DIGEST_LENGTH);\n        for (var i = 0; i < 8; i++) {\n            out[i * 4] = (this.state[i] >>> 24) & 0xff;\n            out[i * 4 + 1] = (this.state[i] >>> 16) & 0xff;\n            out[i * 4 + 2] = (this.state[i] >>> 8) & 0xff;\n            out[i * 4 + 3] = (this.state[i] >>> 0) & 0xff;\n        }\n        return out;\n    };\n    RawSha256.prototype.hashBuffer = function () {\n        var _a = this, buffer = _a.buffer, state = _a.state;\n        var state0 = state[0], state1 = state[1], state2 = state[2], state3 = state[3], state4 = state[4], state5 = state[5], state6 = state[6], state7 = state[7];\n        for (var i = 0; i < BLOCK_SIZE; i++) {\n            if (i < 16) {\n                this.temp[i] =\n                    ((buffer[i * 4] & 0xff) << 24) |\n                        ((buffer[i * 4 + 1] & 0xff) << 16) |\n                        ((buffer[i * 4 + 2] & 0xff) << 8) |\n                        (buffer[i * 4 + 3] & 0xff);\n            }\n            else {\n                var u = this.temp[i - 2];\n                var t1_1 = ((u >>> 17) | (u << 15)) ^ ((u >>> 19) | (u << 13)) ^ (u >>> 10);\n                u = this.temp[i - 15];\n                var t2_1 = ((u >>> 7) | (u << 25)) ^ ((u >>> 18) | (u << 14)) ^ (u >>> 3);\n                this.temp[i] =\n                    ((t1_1 + this.temp[i - 7]) | 0) + ((t2_1 + this.temp[i - 16]) | 0);\n            }\n            var t1 = ((((((state4 >>> 6) | (state4 << 26)) ^\n                ((state4 >>> 11) | (state4 << 21)) ^\n                ((state4 >>> 25) | (state4 << 7))) +\n                ((state4 & state5) ^ (~state4 & state6))) |\n                0) +\n                ((state7 + ((KEY[i] + this.temp[i]) | 0)) | 0)) |\n                0;\n            var t2 = ((((state0 >>> 2) | (state0 << 30)) ^\n                ((state0 >>> 13) | (state0 << 19)) ^\n                ((state0 >>> 22) | (state0 << 10))) +\n                ((state0 & state1) ^ (state0 & state2) ^ (state1 & state2))) |\n                0;\n            state7 = state6;\n            state6 = state5;\n            state5 = state4;\n            state4 = (state3 + t1) | 0;\n            state3 = state2;\n            state2 = state1;\n            state1 = state0;\n            state0 = (t1 + t2) | 0;\n        }\n        state[0] += state0;\n        state[1] += state1;\n        state[2] += state2;\n        state[3] += state3;\n        state[4] += state4;\n        state[5] += state5;\n        state[6] += state6;\n        state[7] += state7;\n    };\n    return RawSha256;\n}());\nexport { RawSha256 };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,aAAa,EAAEC,IAAI,EAAEC,GAAG,EAAEC,mBAAmB,QAAQ,aAAa;AACvF;AACA;AACA;AACA,IAAIC,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAAA,EAAG;IACjB,IAAI,CAACC,KAAK,GAAGC,UAAU,CAACC,IAAI,CAACN,IAAI,CAAC;IAClC,IAAI,CAACO,IAAI,GAAG,IAAIF,UAAU,CAAC,EAAE,CAAC;IAC9B,IAAI,CAACG,MAAM,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;IAChC,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,KAAK;EACzB;EACAT,SAAS,CAACU,SAAS,CAACC,MAAM,GAAG,UAAUC,IAAI,EAAE;IACzC,IAAI,IAAI,CAACH,QAAQ,EAAE;MACf,MAAM,IAAII,KAAK,CAAC,+CAA+C,CAAC;IACpE;IACA,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIC,UAAU,GAAGH,IAAI,CAACG,UAAU;IAChC,IAAI,CAACP,WAAW,IAAIO,UAAU;IAC9B,IAAI,IAAI,CAACP,WAAW,GAAG,CAAC,GAAGT,mBAAmB,EAAE;MAC5C,MAAM,IAAIc,KAAK,CAAC,qCAAqC,CAAC;IAC1D;IACA,OAAOE,UAAU,GAAG,CAAC,EAAE;MACnB,IAAI,CAACV,MAAM,CAAC,IAAI,CAACE,YAAY,EAAE,CAAC,GAAGK,IAAI,CAACE,QAAQ,EAAE,CAAC;MACnDC,UAAU,EAAE;MACZ,IAAI,IAAI,CAACR,YAAY,KAAKZ,UAAU,EAAE;QAClC,IAAI,CAACqB,UAAU,CAAC,CAAC;QACjB,IAAI,CAACT,YAAY,GAAG,CAAC;MACzB;IACJ;EACJ,CAAC;EACDP,SAAS,CAACU,SAAS,CAACO,MAAM,GAAG,YAAY;IACrC,IAAI,CAAC,IAAI,CAACR,QAAQ,EAAE;MAChB,IAAIS,UAAU,GAAG,IAAI,CAACV,WAAW,GAAG,CAAC;MACrC,IAAIW,UAAU,GAAG,IAAIC,QAAQ,CAAC,IAAI,CAACf,MAAM,CAACA,MAAM,EAAE,IAAI,CAACA,MAAM,CAACgB,UAAU,EAAE,IAAI,CAAChB,MAAM,CAACU,UAAU,CAAC;MACjG,IAAIO,iBAAiB,GAAG,IAAI,CAACf,YAAY;MACzCY,UAAU,CAACI,QAAQ,CAAC,IAAI,CAAChB,YAAY,EAAE,EAAE,IAAI,CAAC;MAC9C;MACA,IAAIe,iBAAiB,GAAG3B,UAAU,IAAIA,UAAU,GAAG,CAAC,EAAE;QAClD,KAAK,IAAI6B,CAAC,GAAG,IAAI,CAACjB,YAAY,EAAEiB,CAAC,GAAG7B,UAAU,EAAE6B,CAAC,EAAE,EAAE;UACjDL,UAAU,CAACI,QAAQ,CAACC,CAAC,EAAE,CAAC,CAAC;QAC7B;QACA,IAAI,CAACR,UAAU,CAAC,CAAC;QACjB,IAAI,CAACT,YAAY,GAAG,CAAC;MACzB;MACA,KAAK,IAAIiB,CAAC,GAAG,IAAI,CAACjB,YAAY,EAAEiB,CAAC,GAAG7B,UAAU,GAAG,CAAC,EAAE6B,CAAC,EAAE,EAAE;QACrDL,UAAU,CAACI,QAAQ,CAACC,CAAC,EAAE,CAAC,CAAC;MAC7B;MACAL,UAAU,CAACM,SAAS,CAAC9B,UAAU,GAAG,CAAC,EAAE+B,IAAI,CAACC,KAAK,CAACT,UAAU,GAAG,WAAW,CAAC,EAAE,IAAI,CAAC;MAChFC,UAAU,CAACM,SAAS,CAAC9B,UAAU,GAAG,CAAC,EAAEuB,UAAU,CAAC;MAChD,IAAI,CAACF,UAAU,CAAC,CAAC;MACjB,IAAI,CAACP,QAAQ,GAAG,IAAI;IACxB;IACA;IACA;IACA,IAAImB,GAAG,GAAG,IAAItB,UAAU,CAACV,aAAa,CAAC;IACvC,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxBI,GAAG,CAACJ,CAAC,GAAG,CAAC,CAAC,GAAI,IAAI,CAACvB,KAAK,CAACuB,CAAC,CAAC,KAAK,EAAE,GAAI,IAAI;MAC1CI,GAAG,CAACJ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAI,IAAI,CAACvB,KAAK,CAACuB,CAAC,CAAC,KAAK,EAAE,GAAI,IAAI;MAC9CI,GAAG,CAACJ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAI,IAAI,CAACvB,KAAK,CAACuB,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;MAC7CI,GAAG,CAACJ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAI,IAAI,CAACvB,KAAK,CAACuB,CAAC,CAAC,KAAK,CAAC,GAAI,IAAI;IACjD;IACA,OAAOI,GAAG;EACd,CAAC;EACD5B,SAAS,CAACU,SAAS,CAACM,UAAU,GAAG,YAAY;IACzC,IAAIa,EAAE,GAAG,IAAI;MAAExB,MAAM,GAAGwB,EAAE,CAACxB,MAAM;MAAEJ,KAAK,GAAG4B,EAAE,CAAC5B,KAAK;IACnD,IAAI6B,MAAM,GAAG7B,KAAK,CAAC,CAAC,CAAC;MAAE8B,MAAM,GAAG9B,KAAK,CAAC,CAAC,CAAC;MAAE+B,MAAM,GAAG/B,KAAK,CAAC,CAAC,CAAC;MAAEgC,MAAM,GAAGhC,KAAK,CAAC,CAAC,CAAC;MAAEiC,MAAM,GAAGjC,KAAK,CAAC,CAAC,CAAC;MAAEkC,MAAM,GAAGlC,KAAK,CAAC,CAAC,CAAC;MAAEmC,MAAM,GAAGnC,KAAK,CAAC,CAAC,CAAC;MAAEoC,MAAM,GAAGpC,KAAK,CAAC,CAAC,CAAC;IAC1J,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7B,UAAU,EAAE6B,CAAC,EAAE,EAAE;MACjC,IAAIA,CAAC,GAAG,EAAE,EAAE;QACR,IAAI,CAACpB,IAAI,CAACoB,CAAC,CAAC,GACP,CAACnB,MAAM,CAACmB,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE,GACxB,CAACnB,MAAM,CAACmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,EAAG,GACjC,CAACnB,MAAM,CAACmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAE,GAChCnB,MAAM,CAACmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAK;MACtC,CAAC,MACI;QACD,IAAIc,CAAC,GAAG,IAAI,CAAClC,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC;QACxB,IAAIe,IAAI,GAAG,CAAED,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,EAAG,KAAMA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,EAAG,CAAC,GAAIA,CAAC,KAAK,EAAG;QAC3EA,CAAC,GAAG,IAAI,CAAClC,IAAI,CAACoB,CAAC,GAAG,EAAE,CAAC;QACrB,IAAIgB,IAAI,GAAG,CAAEF,CAAC,KAAK,CAAC,GAAKA,CAAC,IAAI,EAAG,KAAMA,CAAC,KAAK,EAAE,GAAKA,CAAC,IAAI,EAAG,CAAC,GAAIA,CAAC,KAAK,CAAE;QACzE,IAAI,CAAClC,IAAI,CAACoB,CAAC,CAAC,GACR,CAAEe,IAAI,GAAG,IAAI,CAACnC,IAAI,CAACoB,CAAC,GAAG,CAAC,CAAC,GAAI,CAAC,KAAMgB,IAAI,GAAG,IAAI,CAACpC,IAAI,CAACoB,CAAC,GAAG,EAAE,CAAC,GAAI,CAAC,CAAC;MAC1E;MACA,IAAIiB,EAAE,GAAI,CAAE,CAAC,CAAEP,MAAM,KAAK,CAAC,GAAKA,MAAM,IAAI,EAAG,KACvCA,MAAM,KAAK,EAAE,GAAKA,MAAM,IAAI,EAAG,CAAC,IAChCA,MAAM,KAAK,EAAE,GAAKA,MAAM,IAAI,CAAE,CAAC,KAC/BA,MAAM,GAAGC,MAAM,GAAK,CAACD,MAAM,GAAGE,MAAO,CAAC,GACxC,CAAC,KACCC,MAAM,IAAKvC,GAAG,CAAC0B,CAAC,CAAC,GAAG,IAAI,CAACpB,IAAI,CAACoB,CAAC,CAAC,GAAI,CAAC,CAAC,GAAI,CAAC,CAAC,GAC9C,CAAC;MACL,IAAIkB,EAAE,GAAI,CAAC,CAAEZ,MAAM,KAAK,CAAC,GAAKA,MAAM,IAAI,EAAG,KACrCA,MAAM,KAAK,EAAE,GAAKA,MAAM,IAAI,EAAG,CAAC,IAChCA,MAAM,KAAK,EAAE,GAAKA,MAAM,IAAI,EAAG,CAAC,KAChCA,MAAM,GAAGC,MAAM,GAAKD,MAAM,GAAGE,MAAO,GAAID,MAAM,GAAGC,MAAO,CAAC,GAC3D,CAAC;MACLK,MAAM,GAAGD,MAAM;MACfA,MAAM,GAAGD,MAAM;MACfA,MAAM,GAAGD,MAAM;MACfA,MAAM,GAAID,MAAM,GAAGQ,EAAE,GAAI,CAAC;MAC1BR,MAAM,GAAGD,MAAM;MACfA,MAAM,GAAGD,MAAM;MACfA,MAAM,GAAGD,MAAM;MACfA,MAAM,GAAIW,EAAE,GAAGC,EAAE,GAAI,CAAC;IAC1B;IACAzC,KAAK,CAAC,CAAC,CAAC,IAAI6B,MAAM;IAClB7B,KAAK,CAAC,CAAC,CAAC,IAAI8B,MAAM;IAClB9B,KAAK,CAAC,CAAC,CAAC,IAAI+B,MAAM;IAClB/B,KAAK,CAAC,CAAC,CAAC,IAAIgC,MAAM;IAClBhC,KAAK,CAAC,CAAC,CAAC,IAAIiC,MAAM;IAClBjC,KAAK,CAAC,CAAC,CAAC,IAAIkC,MAAM;IAClBlC,KAAK,CAAC,CAAC,CAAC,IAAImC,MAAM;IAClBnC,KAAK,CAAC,CAAC,CAAC,IAAIoC,MAAM;EACtB,CAAC;EACD,OAAOrC,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,SAASA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}