{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\nimport { ListGeofenceCollectionsRequestFilterSensitiveLog, ListGeofenceCollectionsResponseFilterSensitiveLog } from \"../models/models_0\";\nimport { deserializeAws_restJson1ListGeofenceCollectionsCommand, serializeAws_restJson1ListGeofenceCollectionsCommand } from \"../protocols/Aws_restJson1\";\nvar ListGeofenceCollectionsCommand = function (_super) {\n  __extends(ListGeofenceCollectionsCommand, _super);\n  function ListGeofenceCollectionsCommand(input) {\n    var _this = _super.call(this) || this;\n    _this.input = input;\n    return _this;\n  }\n  ListGeofenceCollectionsCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n    this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n    var stack = clientStack.concat(this.middlewareStack);\n    var logger = configuration.logger;\n    var clientName = \"LocationClient\";\n    var commandName = \"ListGeofenceCollectionsCommand\";\n    var handlerExecutionContext = {\n      logger: logger,\n      clientName: clientName,\n      commandName: commandName,\n      inputFilterSensitiveLog: ListGeofenceCollectionsRequestFilterSensitiveLog,\n      outputFilterSensitiveLog: ListGeofenceCollectionsResponseFilterSensitiveLog\n    };\n    var requestHandler = configuration.requestHandler;\n    return stack.resolve(function (request) {\n      return requestHandler.handle(request.request, options || {});\n    }, handlerExecutionContext);\n  };\n  ListGeofenceCollectionsCommand.prototype.serialize = function (input, context) {\n    return serializeAws_restJson1ListGeofenceCollectionsCommand(input, context);\n  };\n  ListGeofenceCollectionsCommand.prototype.deserialize = function (output, context) {\n    return deserializeAws_restJson1ListGeofenceCollectionsCommand(output, context);\n  };\n  return ListGeofenceCollectionsCommand;\n}($Command);\nexport { ListGeofenceCollectionsCommand };", "map": {"version": 3, "names": ["__extends", "getSerdePlugin", "Command", "$Command", "ListGeofenceCollectionsRequestFilterSensitiveLog", "ListGeofenceCollectionsResponseFilterSensitiveLog", "deserializeAws_restJson1ListGeofenceCollectionsCommand", "serializeAws_restJson1ListGeofenceCollectionsCommand", "ListGeofenceCollectionsCommand", "_super", "input", "_this", "call", "prototype", "resolveMiddleware", "clientStack", "configuration", "options", "middlewareStack", "use", "serialize", "deserialize", "stack", "concat", "logger", "clientName", "commandName", "handlerExecutionContext", "inputFilterSensitiveLog", "outputFilterSensitiveLog", "requestHandler", "resolve", "request", "handle", "context", "output"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-location/dist-es/commands/ListGeofenceCollectionsCommand.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\nimport { ListGeofenceCollectionsRequestFilterSensitiveLog, ListGeofenceCollectionsResponseFilterSensitiveLog, } from \"../models/models_0\";\nimport { deserializeAws_restJson1ListGeofenceCollectionsCommand, serializeAws_restJson1ListGeofenceCollectionsCommand, } from \"../protocols/Aws_restJson1\";\nvar ListGeofenceCollectionsCommand = (function (_super) {\n    __extends(ListGeofenceCollectionsCommand, _super);\n    function ListGeofenceCollectionsCommand(input) {\n        var _this = _super.call(this) || this;\n        _this.input = input;\n        return _this;\n    }\n    ListGeofenceCollectionsCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n        this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n        var stack = clientStack.concat(this.middlewareStack);\n        var logger = configuration.logger;\n        var clientName = \"LocationClient\";\n        var commandName = \"ListGeofenceCollectionsCommand\";\n        var handlerExecutionContext = {\n            logger: logger,\n            clientName: clientName,\n            commandName: commandName,\n            inputFilterSensitiveLog: ListGeofenceCollectionsRequestFilterSensitiveLog,\n            outputFilterSensitiveLog: ListGeofenceCollectionsResponseFilterSensitiveLog,\n        };\n        var requestHandler = configuration.requestHandler;\n        return stack.resolve(function (request) {\n            return requestHandler.handle(request.request, options || {});\n        }, handlerExecutionContext);\n    };\n    ListGeofenceCollectionsCommand.prototype.serialize = function (input, context) {\n        return serializeAws_restJson1ListGeofenceCollectionsCommand(input, context);\n    };\n    ListGeofenceCollectionsCommand.prototype.deserialize = function (output, context) {\n        return deserializeAws_restJson1ListGeofenceCollectionsCommand(output, context);\n    };\n    return ListGeofenceCollectionsCommand;\n}($Command));\nexport { ListGeofenceCollectionsCommand };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,OAAO,IAAIC,QAAQ,QAAQ,wBAAwB;AAC5D,SAASC,gDAAgD,EAAEC,iDAAiD,QAAS,oBAAoB;AACzI,SAASC,sDAAsD,EAAEC,oDAAoD,QAAS,4BAA4B;AAC1J,IAAIC,8BAA8B,GAAI,UAAUC,MAAM,EAAE;EACpDT,SAAS,CAACQ,8BAA8B,EAAEC,MAAM,CAAC;EACjD,SAASD,8BAA8BA,CAACE,KAAK,EAAE;IAC3C,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACD,KAAK,GAAGA,KAAK;IACnB,OAAOC,KAAK;EAChB;EACAH,8BAA8B,CAACK,SAAS,CAACC,iBAAiB,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAEC,OAAO,EAAE;IACxG,IAAI,CAACC,eAAe,CAACC,GAAG,CAAClB,cAAc,CAACe,aAAa,EAAE,IAAI,CAACI,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;IACzF,IAAIC,KAAK,GAAGP,WAAW,CAACQ,MAAM,CAAC,IAAI,CAACL,eAAe,CAAC;IACpD,IAAIM,MAAM,GAAGR,aAAa,CAACQ,MAAM;IACjC,IAAIC,UAAU,GAAG,gBAAgB;IACjC,IAAIC,WAAW,GAAG,gCAAgC;IAClD,IAAIC,uBAAuB,GAAG;MAC1BH,MAAM,EAAEA,MAAM;MACdC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA,WAAW;MACxBE,uBAAuB,EAAExB,gDAAgD;MACzEyB,wBAAwB,EAAExB;IAC9B,CAAC;IACD,IAAIyB,cAAc,GAAGd,aAAa,CAACc,cAAc;IACjD,OAAOR,KAAK,CAACS,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpC,OAAOF,cAAc,CAACG,MAAM,CAACD,OAAO,CAACA,OAAO,EAAEf,OAAO,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC,EAAEU,uBAAuB,CAAC;EAC/B,CAAC;EACDnB,8BAA8B,CAACK,SAAS,CAACO,SAAS,GAAG,UAAUV,KAAK,EAAEwB,OAAO,EAAE;IAC3E,OAAO3B,oDAAoD,CAACG,KAAK,EAAEwB,OAAO,CAAC;EAC/E,CAAC;EACD1B,8BAA8B,CAACK,SAAS,CAACQ,WAAW,GAAG,UAAUc,MAAM,EAAED,OAAO,EAAE;IAC9E,OAAO5B,sDAAsD,CAAC6B,MAAM,EAAED,OAAO,CAAC;EAClF,CAAC;EACD,OAAO1B,8BAA8B;AACzC,CAAC,CAACL,QAAQ,CAAE;AACZ,SAASK,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}