{"ast": null, "code": "const textareafield = {\n  color: {\n    value: '{components.fieldcontrol.color.value}'\n  },\n  borderColor: {\n    value: '{components.fieldcontrol.borderColor.value}'\n  },\n  _focus: {\n    borderColor: {\n      value: '{components.fieldcontrol._focus.borderColor.value}'\n    }\n  }\n};\nexport { textareafield };", "map": {"version": 3, "names": ["textareafield", "color", "value", "borderColor", "_focus"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/textAreaField.mjs"], "sourcesContent": ["const textareafield = {\n    color: { value: '{components.fieldcontrol.color.value}' },\n    borderColor: { value: '{components.fieldcontrol.borderColor.value}' },\n    _focus: {\n        borderColor: {\n            value: '{components.fieldcontrol._focus.borderColor.value}',\n        },\n    },\n};\n\nexport { textareafield };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAG;EAClBC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAwC,CAAC;EACzDC,WAAW,EAAE;IAAED,KAAK,EAAE;EAA8C,CAAC;EACrEE,MAAM,EAAE;IACJD,WAAW,EAAE;MACTD,KAAK,EAAE;IACX;EACJ;AACJ,CAAC;AAED,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}