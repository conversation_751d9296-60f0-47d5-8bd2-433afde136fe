{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { assertPasskeyError, PasskeyErrorCode, handlePasskeyAuthenticationError } from './errors.mjs';\nimport { getIsPasskeySupported } from './getIsPasskeySupported.mjs';\nimport { deserializeJsonToPkcGetOptions, serializePkcWithAssertionToJson } from './serde.mjs';\nimport { assertCredentialIsPkcWithAuthenticatorAssertionResponse } from './types/index.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst getPasskey = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (input) {\n    try {\n      const isPasskeySupported = getIsPasskeySupported();\n      assertPasskeyError(isPasskeySupported, PasskeyErrorCode.PasskeyNotSupported);\n      const passkeyGetOptions = deserializeJsonToPkcGetOptions(input);\n      const credential = yield navigator.credentials.get({\n        publicKey: passkeyGetOptions\n      });\n      assertCredentialIsPkcWithAuthenticatorAssertionResponse(credential);\n      return serializePkcWithAssertionToJson(credential);\n    } catch (err) {\n      throw handlePasskeyAuthenticationError(err);\n    }\n  });\n  return function getPasskey(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { getPasskey };", "map": {"version": 3, "names": ["assertPasskeyError", "PasskeyErrorCode", "handlePasskeyAuthenticationError", "getIsPasskeySupported", "deserializeJsonToPkcGetOptions", "serializePkcWithAssertionToJson", "assertCredentialIsPkcWithAuthenticatorAssertionResponse", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "_asyncToGenerator", "input", "isPasskeySupported", "PasskeyNotSupported", "passkeyGetOptions", "credential", "navigator", "credentials", "get", "public<PERSON>ey", "err", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/utils/passkey/getPasskey.mjs"], "sourcesContent": ["import { assertPasskeyError, PasskeyErrorCode, handlePasskeyAuthenticationError } from './errors.mjs';\nimport { getIsPasskeySupported } from './getIsPasskeySupported.mjs';\nimport { deserializeJsonToPkcGetOptions, serializePkcWithAssertionToJson } from './serde.mjs';\nimport { assertCredentialIsPkcWithAuthenticatorAssertionResponse } from './types/index.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst getPasskey = async (input) => {\n    try {\n        const isPasskeySupported = getIsPasskeySupported();\n        assertPasskeyError(isPasskeySupported, PasskeyErrorCode.PasskeyNotSupported);\n        const passkeyGetOptions = deserializeJsonToPkcGetOptions(input);\n        const credential = await navigator.credentials.get({\n            publicKey: passkeyGetOptions,\n        });\n        assertCredentialIsPkcWithAuthenticatorAssertionResponse(credential);\n        return serializePkcWithAssertionToJson(credential);\n    }\n    catch (err) {\n        throw handlePasskeyAuthenticationError(err);\n    }\n};\n\nexport { getPasskey };\n"], "mappings": ";AAAA,SAASA,kBAAkB,EAAEC,gBAAgB,EAAEC,gCAAgC,QAAQ,cAAc;AACrG,SAASC,qBAAqB,QAAQ,6BAA6B;AACnE,SAASC,8BAA8B,EAAEC,+BAA+B,QAAQ,aAAa;AAC7F,SAASC,uDAAuD,QAAQ,mBAAmB;;AAE3F;AACA;AACA,MAAMC,UAAU;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,KAAK,EAAK;IAChC,IAAI;MACA,MAAMC,kBAAkB,GAAGR,qBAAqB,CAAC,CAAC;MAClDH,kBAAkB,CAACW,kBAAkB,EAAEV,gBAAgB,CAACW,mBAAmB,CAAC;MAC5E,MAAMC,iBAAiB,GAAGT,8BAA8B,CAACM,KAAK,CAAC;MAC/D,MAAMI,UAAU,SAASC,SAAS,CAACC,WAAW,CAACC,GAAG,CAAC;QAC/CC,SAAS,EAAEL;MACf,CAAC,CAAC;MACFP,uDAAuD,CAACQ,UAAU,CAAC;MACnE,OAAOT,+BAA+B,CAACS,UAAU,CAAC;IACtD,CAAC,CACD,OAAOK,GAAG,EAAE;MACR,MAAMjB,gCAAgC,CAACiB,GAAG,CAAC;IAC/C;EACJ,CAAC;EAAA,gBAdKZ,UAAUA,CAAAa,EAAA;IAAA,OAAAZ,IAAA,CAAAa,KAAA,OAAAC,SAAA;EAAA;AAAA,GAcf;AAED,SAASf,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}