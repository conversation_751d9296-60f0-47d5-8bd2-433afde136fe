{"ast": null, "code": "import devAssert from \"../jsutils/devAssert.mjs\";\n\n/**\n * Given a GraphQLError, format it according to the rules described by the\n * Response Format, Errors section of the GraphQL Specification.\n */\nexport function formatError(error) {\n  var _error$message;\n  error || devAssert(0, 'Received null or undefined error.');\n  var message = (_error$message = error.message) !== null && _error$message !== void 0 ? _error$message : 'An unknown error occurred.';\n  var locations = error.locations;\n  var path = error.path;\n  var extensions = error.extensions;\n  return extensions && Object.keys(extensions).length > 0 ? {\n    message: message,\n    locations: locations,\n    path: path,\n    extensions: extensions\n  } : {\n    message: message,\n    locations: locations,\n    path: path\n  };\n}\n/**\n * @see https://github.com/graphql/graphql-spec/blob/master/spec/Section%207%20--%20Response.md#errors\n */", "map": {"version": 3, "names": ["devAssert", "formatError", "error", "_error$message", "message", "locations", "path", "extensions", "Object", "keys", "length"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/graphql/error/formatError.mjs"], "sourcesContent": ["import devAssert from \"../jsutils/devAssert.mjs\";\n\n/**\n * Given a GraphQLError, format it according to the rules described by the\n * Response Format, Errors section of the GraphQL Specification.\n */\nexport function formatError(error) {\n  var _error$message;\n\n  error || devAssert(0, 'Received null or undefined error.');\n  var message = (_error$message = error.message) !== null && _error$message !== void 0 ? _error$message : 'An unknown error occurred.';\n  var locations = error.locations;\n  var path = error.path;\n  var extensions = error.extensions;\n  return extensions && Object.keys(extensions).length > 0 ? {\n    message: message,\n    locations: locations,\n    path: path,\n    extensions: extensions\n  } : {\n    message: message,\n    locations: locations,\n    path: path\n  };\n}\n/**\n * @see https://github.com/graphql/graphql-spec/blob/master/spec/Section%207%20--%20Response.md#errors\n */\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,0BAA0B;;AAEhD;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,IAAIC,cAAc;EAElBD,KAAK,IAAIF,SAAS,CAAC,CAAC,EAAE,mCAAmC,CAAC;EAC1D,IAAII,OAAO,GAAG,CAACD,cAAc,GAAGD,KAAK,CAACE,OAAO,MAAM,IAAI,IAAID,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAG,4BAA4B;EACpI,IAAIE,SAAS,GAAGH,KAAK,CAACG,SAAS;EAC/B,IAAIC,IAAI,GAAGJ,KAAK,CAACI,IAAI;EACrB,IAAIC,UAAU,GAAGL,KAAK,CAACK,UAAU;EACjC,OAAOA,UAAU,IAAIC,MAAM,CAACC,IAAI,CAACF,UAAU,CAAC,CAACG,MAAM,GAAG,CAAC,GAAG;IACxDN,OAAO,EAAEA,OAAO;IAChBC,SAAS,EAAEA,SAAS;IACpBC,IAAI,EAAEA,IAAI;IACVC,UAAU,EAAEA;EACd,CAAC,GAAG;IACFH,OAAO,EAAEA,OAAO;IAChBC,SAAS,EAAEA,SAAS;IACpBC,IAAI,EAAEA;EACR,CAAC;AACH;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}