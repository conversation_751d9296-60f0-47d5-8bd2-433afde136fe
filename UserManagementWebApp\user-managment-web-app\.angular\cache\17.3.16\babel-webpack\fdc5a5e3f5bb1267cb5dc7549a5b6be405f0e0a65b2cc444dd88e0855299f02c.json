{"ast": null, "code": "import { __asyncGenerator, __await, __awaiter, __generator, __read, __spreadArray } from \"tslib\";\nimport { ListGeofenceCollectionsCommand } from \"../commands/ListGeofenceCollectionsCommand\";\nimport { Location } from \"../Location\";\nimport { LocationClient } from \"../LocationClient\";\nvar makePagedClientRequest = function (client, input) {\n  var args = [];\n  for (var _i = 2; _i < arguments.length; _i++) {\n    args[_i - 2] = arguments[_i];\n  }\n  return __awaiter(void 0, void 0, void 0, function () {\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          return [4, client.send.apply(client, __spreadArray([new ListGeofenceCollectionsCommand(input)], __read(args), false))];\n        case 1:\n          return [2, _a.sent()];\n      }\n    });\n  });\n};\nvar makePagedRequest = function (client, input) {\n  var args = [];\n  for (var _i = 2; _i < arguments.length; _i++) {\n    args[_i - 2] = arguments[_i];\n  }\n  return __awaiter(void 0, void 0, void 0, function () {\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          return [4, client.listGeofenceCollections.apply(client, __spreadArray([input], __read(args), false))];\n        case 1:\n          return [2, _a.sent()];\n      }\n    });\n  });\n};\nexport function paginateListGeofenceCollections(config, input) {\n  var additionalArguments = [];\n  for (var _i = 2; _i < arguments.length; _i++) {\n    additionalArguments[_i - 2] = arguments[_i];\n  }\n  return __asyncGenerator(this, arguments, function paginateListGeofenceCollections_1() {\n    var token, hasNext, page, prevToken;\n    return __generator(this, function (_a) {\n      switch (_a.label) {\n        case 0:\n          token = config.startingToken || undefined;\n          hasNext = true;\n          _a.label = 1;\n        case 1:\n          if (!hasNext) return [3, 9];\n          input.NextToken = token;\n          input[\"MaxResults\"] = config.pageSize;\n          if (!(config.client instanceof Location)) return [3, 3];\n          return [4, __await(makePagedRequest.apply(void 0, __spreadArray([config.client, input], __read(additionalArguments), false)))];\n        case 2:\n          page = _a.sent();\n          return [3, 6];\n        case 3:\n          if (!(config.client instanceof LocationClient)) return [3, 5];\n          return [4, __await(makePagedClientRequest.apply(void 0, __spreadArray([config.client, input], __read(additionalArguments), false)))];\n        case 4:\n          page = _a.sent();\n          return [3, 6];\n        case 5:\n          throw new Error(\"Invalid client, expected Location | LocationClient\");\n        case 6:\n          return [4, __await(page)];\n        case 7:\n          return [4, _a.sent()];\n        case 8:\n          _a.sent();\n          prevToken = token;\n          token = page.NextToken;\n          hasNext = !!(token && (!config.stopOnSameToken || token !== prevToken));\n          return [3, 1];\n        case 9:\n          return [4, __await(undefined)];\n        case 10:\n          return [2, _a.sent()];\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["__asyncGenerator", "__await", "__awaiter", "__generator", "__read", "__spread<PERSON><PERSON>y", "ListGeofenceCollectionsCommand", "Location", "LocationClient", "makePagedClientRequest", "client", "input", "args", "_i", "arguments", "length", "_a", "label", "send", "apply", "sent", "makePagedRequest", "listGeofenceCollections", "paginateListGeofenceCollections", "config", "additionalArguments", "paginateListGeofenceCollections_1", "token", "hasNext", "page", "prevToken", "startingToken", "undefined", "NextToken", "pageSize", "Error", "stopOnSameToken"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-location/dist-es/pagination/ListGeofenceCollectionsPaginator.js"], "sourcesContent": ["import { __asyncGenerator, __await, __awaiter, __generator, __read, __spreadArray } from \"tslib\";\nimport { ListGeofenceCollectionsCommand, } from \"../commands/ListGeofenceCollectionsCommand\";\nimport { Location } from \"../Location\";\nimport { LocationClient } from \"../LocationClient\";\nvar makePagedClientRequest = function (client, input) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        args[_i - 2] = arguments[_i];\n    }\n    return __awaiter(void 0, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0: return [4, client.send.apply(client, __spreadArray([new ListGeofenceCollectionsCommand(input)], __read(args), false))];\n                case 1: return [2, _a.sent()];\n            }\n        });\n    });\n};\nvar makePagedRequest = function (client, input) {\n    var args = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        args[_i - 2] = arguments[_i];\n    }\n    return __awaiter(void 0, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0: return [4, client.listGeofenceCollections.apply(client, __spreadArray([input], __read(args), false))];\n                case 1: return [2, _a.sent()];\n            }\n        });\n    });\n};\nexport function paginateListGeofenceCollections(config, input) {\n    var additionalArguments = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        additionalArguments[_i - 2] = arguments[_i];\n    }\n    return __asyncGenerator(this, arguments, function paginateListGeofenceCollections_1() {\n        var token, hasNext, page, prevToken;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    token = config.startingToken || undefined;\n                    hasNext = true;\n                    _a.label = 1;\n                case 1:\n                    if (!hasNext) return [3, 9];\n                    input.NextToken = token;\n                    input[\"MaxResults\"] = config.pageSize;\n                    if (!(config.client instanceof Location)) return [3, 3];\n                    return [4, __await(makePagedRequest.apply(void 0, __spreadArray([config.client, input], __read(additionalArguments), false)))];\n                case 2:\n                    page = _a.sent();\n                    return [3, 6];\n                case 3:\n                    if (!(config.client instanceof LocationClient)) return [3, 5];\n                    return [4, __await(makePagedClientRequest.apply(void 0, __spreadArray([config.client, input], __read(additionalArguments), false)))];\n                case 4:\n                    page = _a.sent();\n                    return [3, 6];\n                case 5: throw new Error(\"Invalid client, expected Location | LocationClient\");\n                case 6: return [4, __await(page)];\n                case 7: return [4, _a.sent()];\n                case 8:\n                    _a.sent();\n                    prevToken = token;\n                    token = page.NextToken;\n                    hasNext = !!(token && (!config.stopOnSameToken || token !== prevToken));\n                    return [3, 1];\n                case 9: return [4, __await(undefined)];\n                case 10: return [2, _a.sent()];\n            }\n        });\n    });\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,OAAO,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAChG,SAASC,8BAA8B,QAAS,4CAA4C;AAC5F,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,cAAc,QAAQ,mBAAmB;AAClD,IAAIC,sBAAsB,GAAG,SAAAA,CAAUC,MAAM,EAAEC,KAAK,EAAE;EAClD,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAChC;EACA,OAAOX,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;IACjD,OAAOC,WAAW,CAAC,IAAI,EAAE,UAAUa,EAAE,EAAE;MACnC,QAAQA,EAAE,CAACC,KAAK;QACZ,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,EAAEP,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAEL,aAAa,CAAC,CAAC,IAAIC,8BAA8B,CAACK,KAAK,CAAC,CAAC,EAAEP,MAAM,CAACQ,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QAC9H,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,EAAEI,EAAE,CAACI,IAAI,CAAC,CAAC,CAAC;MACjC;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC;AACD,IAAIC,gBAAgB,GAAG,SAAAA,CAAUX,MAAM,EAAEC,KAAK,EAAE;EAC5C,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CD,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAChC;EACA,OAAOX,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;IACjD,OAAOC,WAAW,CAAC,IAAI,EAAE,UAAUa,EAAE,EAAE;MACnC,QAAQA,EAAE,CAACC,KAAK;QACZ,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,EAAEP,MAAM,CAACY,uBAAuB,CAACH,KAAK,CAACT,MAAM,EAAEL,aAAa,CAAC,CAACM,KAAK,CAAC,EAAEP,MAAM,CAACQ,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QAC7G,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,EAAEI,EAAE,CAACI,IAAI,CAAC,CAAC,CAAC;MACjC;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC;AACD,OAAO,SAASG,+BAA+BA,CAACC,MAAM,EAAEb,KAAK,EAAE;EAC3D,IAAIc,mBAAmB,GAAG,EAAE;EAC5B,KAAK,IAAIZ,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC1CY,mBAAmB,CAACZ,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;EAC/C;EACA,OAAOb,gBAAgB,CAAC,IAAI,EAAEc,SAAS,EAAE,SAASY,iCAAiCA,CAAA,EAAG;IAClF,IAAIC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,SAAS;IACnC,OAAO3B,WAAW,CAAC,IAAI,EAAE,UAAUa,EAAE,EAAE;MACnC,QAAQA,EAAE,CAACC,KAAK;QACZ,KAAK,CAAC;UACFU,KAAK,GAAGH,MAAM,CAACO,aAAa,IAAIC,SAAS;UACzCJ,OAAO,GAAG,IAAI;UACdZ,EAAE,CAACC,KAAK,GAAG,CAAC;QAChB,KAAK,CAAC;UACF,IAAI,CAACW,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;UAC3BjB,KAAK,CAACsB,SAAS,GAAGN,KAAK;UACvBhB,KAAK,CAAC,YAAY,CAAC,GAAGa,MAAM,CAACU,QAAQ;UACrC,IAAI,EAAEV,MAAM,CAACd,MAAM,YAAYH,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;UACvD,OAAO,CAAC,CAAC,EAAEN,OAAO,CAACoB,gBAAgB,CAACF,KAAK,CAAC,KAAK,CAAC,EAAEd,aAAa,CAAC,CAACmB,MAAM,CAACd,MAAM,EAAEC,KAAK,CAAC,EAAEP,MAAM,CAACqB,mBAAmB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QAClI,KAAK,CAAC;UACFI,IAAI,GAAGb,EAAE,CAACI,IAAI,CAAC,CAAC;UAChB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;QACjB,KAAK,CAAC;UACF,IAAI,EAAEI,MAAM,CAACd,MAAM,YAAYF,cAAc,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;UAC7D,OAAO,CAAC,CAAC,EAAEP,OAAO,CAACQ,sBAAsB,CAACU,KAAK,CAAC,KAAK,CAAC,EAAEd,aAAa,CAAC,CAACmB,MAAM,CAACd,MAAM,EAAEC,KAAK,CAAC,EAAEP,MAAM,CAACqB,mBAAmB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QACxI,KAAK,CAAC;UACFI,IAAI,GAAGb,EAAE,CAACI,IAAI,CAAC,CAAC;UAChB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;QACjB,KAAK,CAAC;UAAE,MAAM,IAAIe,KAAK,CAAC,oDAAoD,CAAC;QAC7E,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,EAAElC,OAAO,CAAC4B,IAAI,CAAC,CAAC;QACjC,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,EAAEb,EAAE,CAACI,IAAI,CAAC,CAAC,CAAC;QAC7B,KAAK,CAAC;UACFJ,EAAE,CAACI,IAAI,CAAC,CAAC;UACTU,SAAS,GAAGH,KAAK;UACjBA,KAAK,GAAGE,IAAI,CAACI,SAAS;UACtBL,OAAO,GAAG,CAAC,EAAED,KAAK,KAAK,CAACH,MAAM,CAACY,eAAe,IAAIT,KAAK,KAAKG,SAAS,CAAC,CAAC;UACvE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;QACjB,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,EAAE7B,OAAO,CAAC+B,SAAS,CAAC,CAAC;QACtC,KAAK,EAAE;UAAE,OAAO,CAAC,CAAC,EAAEhB,EAAE,CAACI,IAAI,CAAC,CAAC,CAAC;MAClC;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}