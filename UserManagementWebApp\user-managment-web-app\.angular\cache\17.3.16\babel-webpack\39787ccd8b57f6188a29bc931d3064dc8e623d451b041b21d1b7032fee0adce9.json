{"ast": null, "code": "export { signUp } from './apis/signUp.mjs';\nexport { resetPassword } from './apis/resetPassword.mjs';\nexport { confirmResetPassword } from './apis/confirmResetPassword.mjs';\nexport { signIn } from './apis/signIn.mjs';\nexport { resendSignUpCode } from './apis/resendSignUpCode.mjs';\nexport { confirmSignUp } from './apis/confirmSignUp.mjs';\nexport { confirmSignIn } from './apis/confirmSignIn.mjs';\nexport { updateMFAPreference } from './apis/updateMFAPreference.mjs';\nexport { fetchMFAPreference } from './apis/fetchMFAPreference.mjs';\nexport { verifyTOTPSetup } from './apis/verifyTOTPSetup.mjs';\nexport { updatePassword } from './apis/updatePassword.mjs';\nexport { setUpTOTP } from './apis/setUpTOTP.mjs';\nexport { updateUserAttributes } from './apis/updateUserAttributes.mjs';\nexport { updateUserAttribute } from './apis/updateUserAttribute.mjs';\nexport { getCurrentUser } from './apis/getCurrentUser.mjs';\nexport { confirmUserAttribute } from './apis/confirmUserAttribute.mjs';\nexport { signInWithRedirect } from './apis/signInWithRedirect.mjs';\nexport { fetchUserAttributes } from './apis/fetchUserAttributes.mjs';\nexport { signOut } from './apis/signOut.mjs';\nexport { sendUserAttributeVerificationCode } from './apis/sendUserAttributeVerificationCode.mjs';\nexport { deleteUserAttributes } from './apis/deleteUserAttributes.mjs';\nexport { deleteUser } from './apis/deleteUser.mjs';\nexport { rememberDevice } from './apis/rememberDevice.mjs';\nexport { forgetDevice } from './apis/forgetDevice.mjs';\nexport { fetchDevices } from './apis/fetchDevices.mjs';\nexport { autoSignIn } from './apis/autoSignIn.mjs';\nexport { cognitoCredentialsProvider } from './credentialsProvider/index.mjs';\nexport { refreshAuthTokens, refreshAuthTokensWithoutDedupe } from './utils/refreshAuthTokens.mjs';\nexport { DefaultTokenStore, createKeysForAuthStorage } from './tokenProvider/TokenStore.mjs';\nexport { TokenOrchestrator } from './tokenProvider/TokenOrchestrator.mjs';\nexport { cognitoUserPoolsTokenProvider } from './tokenProvider/tokenProvider.mjs';\nexport { AUTH_KEY_PREFIX } from './tokenProvider/constants.mjs';\nexport { generateCodeVerifier } from './utils/oauth/generateCodeVerifier.mjs';\nexport { generateState } from './utils/oauth/generateState.mjs';\nimport '@aws-amplify/core';\nimport '@aws-amplify/core/internals/utils';\nexport { getRedirectUrl } from './utils/oauth/getRedirectUrl.mjs';\nimport './utils/types.mjs';\nimport '../../errors/constants.mjs';\nimport '../../Errors.mjs';\nimport '../../common/AuthErrorStrings.mjs';\nexport { validateState } from './utils/oauth/validateState.mjs';\nexport { CognitoAWSCredentialsAndIdentityIdProvider } from './credentialsProvider/credentialsProvider.mjs';\nexport { DefaultIdentityIdStore } from './credentialsProvider/IdentityIdStore.mjs';", "map": {"version": 3, "names": ["signUp", "resetPassword", "confirmResetPassword", "signIn", "resendSignUpCode", "confirmSignUp", "confirmSignIn", "updateMFAPreference", "fetchMFAPreference", "verifyTOTPSetup", "updatePassword", "setUpTOTP", "updateUserAttributes", "updateUserAttribute", "getCurrentUser", "confirmUserAttribute", "signInWithRedirect", "fetchUserAttributes", "signOut", "sendUserAttributeVerificationCode", "deleteUserAttributes", "deleteUser", "rememberDevice", "forgetDevice", "fetchDevices", "autoSignIn", "cognitoCredentialsProvider", "refreshAuthTokens", "refreshAuthTokensWithoutDedupe", "DefaultTokenStore", "createKeysForAuthStorage", "TokenOrchestrator", "cognitoUserPoolsTokenProvider", "AUTH_KEY_PREFIX", "generateCodeVerifier", "generateState", "getRedirectUrl", "validateState", "CognitoAWSCredentialsAndIdentityIdProvider", "DefaultIdentityIdStore"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/index.mjs"], "sourcesContent": ["export { signUp } from './apis/signUp.mjs';\nexport { resetPassword } from './apis/resetPassword.mjs';\nexport { confirmResetPassword } from './apis/confirmResetPassword.mjs';\nexport { signIn } from './apis/signIn.mjs';\nexport { resendSignUpCode } from './apis/resendSignUpCode.mjs';\nexport { confirmSignUp } from './apis/confirmSignUp.mjs';\nexport { confirmSignIn } from './apis/confirmSignIn.mjs';\nexport { updateMFAPreference } from './apis/updateMFAPreference.mjs';\nexport { fetchMFAPreference } from './apis/fetchMFAPreference.mjs';\nexport { verifyTOTPSetup } from './apis/verifyTOTPSetup.mjs';\nexport { updatePassword } from './apis/updatePassword.mjs';\nexport { setUpTOTP } from './apis/setUpTOTP.mjs';\nexport { updateUserAttributes } from './apis/updateUserAttributes.mjs';\nexport { updateUserAttribute } from './apis/updateUserAttribute.mjs';\nexport { getCurrentUser } from './apis/getCurrentUser.mjs';\nexport { confirmUserAttribute } from './apis/confirmUserAttribute.mjs';\nexport { signInWithRedirect } from './apis/signInWithRedirect.mjs';\nexport { fetchUserAttributes } from './apis/fetchUserAttributes.mjs';\nexport { signOut } from './apis/signOut.mjs';\nexport { sendUserAttributeVerificationCode } from './apis/sendUserAttributeVerificationCode.mjs';\nexport { deleteUserAttributes } from './apis/deleteUserAttributes.mjs';\nexport { deleteUser } from './apis/deleteUser.mjs';\nexport { rememberDevice } from './apis/rememberDevice.mjs';\nexport { forgetDevice } from './apis/forgetDevice.mjs';\nexport { fetchDevices } from './apis/fetchDevices.mjs';\nexport { autoSignIn } from './apis/autoSignIn.mjs';\nexport { cognitoCredentialsProvider } from './credentialsProvider/index.mjs';\nexport { refreshAuthTokens, refreshAuthTokensWithoutDedupe } from './utils/refreshAuthTokens.mjs';\nexport { DefaultTokenStore, createKeysForAuthStorage } from './tokenProvider/TokenStore.mjs';\nexport { TokenOrchestrator } from './tokenProvider/TokenOrchestrator.mjs';\nexport { cognitoUserPoolsTokenProvider } from './tokenProvider/tokenProvider.mjs';\nexport { AUTH_KEY_PREFIX } from './tokenProvider/constants.mjs';\nexport { generateCodeVerifier } from './utils/oauth/generateCodeVerifier.mjs';\nexport { generateState } from './utils/oauth/generateState.mjs';\nimport '@aws-amplify/core';\nimport '@aws-amplify/core/internals/utils';\nexport { getRedirectUrl } from './utils/oauth/getRedirectUrl.mjs';\nimport './utils/types.mjs';\nimport '../../errors/constants.mjs';\nimport '../../Errors.mjs';\nimport '../../common/AuthErrorStrings.mjs';\nexport { validateState } from './utils/oauth/validateState.mjs';\nexport { CognitoAWSCredentialsAndIdentityIdProvider } from './credentialsProvider/credentialsProvider.mjs';\nexport { DefaultIdentityIdStore } from './credentialsProvider/IdentityIdStore.mjs';\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,mBAAmB,QAAQ,gCAAgC;AACpE,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,mBAAmB,QAAQ,gCAAgC;AACpE,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,mBAAmB,QAAQ,gCAAgC;AACpE,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,iCAAiC,QAAQ,8CAA8C;AAChG,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,iBAAiB,EAAEC,8BAA8B,QAAQ,+BAA+B;AACjG,SAASC,iBAAiB,EAAEC,wBAAwB,QAAQ,gCAAgC;AAC5F,SAASC,iBAAiB,QAAQ,uCAAuC;AACzE,SAASC,6BAA6B,QAAQ,mCAAmC;AACjF,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,OAAO,mBAAmB;AAC1B,OAAO,mCAAmC;AAC1C,SAASC,cAAc,QAAQ,kCAAkC;AACjE,OAAO,mBAAmB;AAC1B,OAAO,4BAA4B;AACnC,OAAO,kBAAkB;AACzB,OAAO,mCAAmC;AAC1C,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,0CAA0C,QAAQ,+CAA+C;AAC1G,SAASC,sBAAsB,QAAQ,2CAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}