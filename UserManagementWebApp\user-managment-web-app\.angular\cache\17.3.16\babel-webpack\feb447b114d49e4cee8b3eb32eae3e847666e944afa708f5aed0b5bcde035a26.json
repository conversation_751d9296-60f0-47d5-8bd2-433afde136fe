{"ast": null, "code": "'use strict';\n\nvar toStr = Object.prototype.toString;\nmodule.exports = function isArguments(value) {\n  var str = toStr.call(value);\n  var isArgs = str === '[object Arguments]';\n  if (!isArgs) {\n    isArgs = str !== '[object Array]' && value !== null && typeof value === 'object' && typeof value.length === 'number' && value.length >= 0 && toStr.call(value.callee) === '[object Function]';\n  }\n  return isArgs;\n};", "map": {"version": 3, "names": ["toStr", "Object", "prototype", "toString", "module", "exports", "isArguments", "value", "str", "call", "is<PERSON><PERSON><PERSON>", "length", "callee"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/object-keys/isArguments.js"], "sourcesContent": ["'use strict';\n\nvar toStr = Object.prototype.toString;\n\nmodule.exports = function isArguments(value) {\n\tvar str = toStr.call(value);\n\tvar isArgs = str === '[object Arguments]';\n\tif (!isArgs) {\n\t\tisArgs = str !== '[object Array]' &&\n\t\t\tvalue !== null &&\n\t\t\ttypeof value === 'object' &&\n\t\t\ttypeof value.length === 'number' &&\n\t\t\tvalue.length >= 0 &&\n\t\t\ttoStr.call(value.callee) === '[object Function]';\n\t}\n\treturn isArgs;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ;AAErCC,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC5C,IAAIC,GAAG,GAAGR,KAAK,CAACS,IAAI,CAACF,KAAK,CAAC;EAC3B,IAAIG,MAAM,GAAGF,GAAG,KAAK,oBAAoB;EACzC,IAAI,CAACE,MAAM,EAAE;IACZA,MAAM,GAAGF,GAAG,KAAK,gBAAgB,IAChCD,KAAK,KAAK,IAAI,IACd,OAAOA,KAAK,KAAK,QAAQ,IACzB,OAAOA,KAAK,CAACI,MAAM,KAAK,QAAQ,IAChCJ,KAAK,CAACI,MAAM,IAAI,CAAC,IACjBX,KAAK,CAACS,IAAI,CAACF,KAAK,CAACK,MAAM,CAAC,KAAK,mBAAmB;EAClD;EACA,OAAOF,MAAM;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}