{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns signed headers.\n *\n * @param headers `headers` from the request.\n * @returns List of headers included in canonical headers, separated by semicolons (;). This indicates which headers\n * are part of the signing process. Header names must use lowercase characters and must appear in alphabetical order.\n *\n * @internal\n */\nconst getSignedHeaders = headers => Object.keys(headers).map(key => key.toLowerCase()).sort().join(';');\nexport { getSignedHeaders };", "map": {"version": 3, "names": ["getSignedHeaders", "headers", "Object", "keys", "map", "key", "toLowerCase", "sort", "join"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/signer/signatureV4/utils/getSignedHeaders.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns signed headers.\n *\n * @param headers `headers` from the request.\n * @returns List of headers included in canonical headers, separated by semicolons (;). This indicates which headers\n * are part of the signing process. Header names must use lowercase characters and must appear in alphabetical order.\n *\n * @internal\n */\nconst getSignedHeaders = (headers) => Object.keys(headers)\n    .map(key => key.toLowerCase())\n    .sort()\n    .join(';');\n\nexport { getSignedHeaders };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,gBAAgB,GAAIC,OAAO,IAAKC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CACrDG,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC,CAC7BC,IAAI,CAAC,CAAC,CACNC,IAAI,CAAC,GAAG,CAAC;AAEd,SAASR,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}