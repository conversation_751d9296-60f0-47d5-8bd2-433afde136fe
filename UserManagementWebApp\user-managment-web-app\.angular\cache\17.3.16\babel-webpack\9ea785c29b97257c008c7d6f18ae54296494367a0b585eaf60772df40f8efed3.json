{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * The service name used to sign requests if the API requires authentication.\n */\nconst COGNITO_IDP_SERVICE_NAME = 'cognito-idp';\nexport { COGNITO_IDP_SERVICE_NAME };", "map": {"version": 3, "names": ["COGNITO_IDP_SERVICE_NAME"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/constants.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * The service name used to sign requests if the API requires authentication.\n */\nconst COGNITO_IDP_SERVICE_NAME = 'cognito-idp';\n\nexport { COGNITO_IDP_SERVICE_NAME };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,MAAMA,wBAAwB,GAAG,aAAa;AAE9C,SAASA,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}