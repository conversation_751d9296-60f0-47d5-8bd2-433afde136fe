{"ast": null, "code": "const breadcrumbs = {\n  flexDirection: {\n    value: 'row'\n  },\n  flexWrap: {\n    value: 'wrap'\n  },\n  gap: {\n    value: '0'\n  },\n  color: {\n    value: '{colors.font.tertiary}'\n  },\n  item: {\n    flexDirection: {\n      value: 'row'\n    },\n    color: {\n      value: 'inherit'\n    },\n    fontSize: {\n      value: 'inherit'\n    },\n    alignItems: {\n      value: 'center'\n    },\n    lineHeight: {\n      value: '1'\n    }\n  },\n  separator: {\n    color: {\n      value: 'inherit'\n    },\n    fontSize: {\n      value: 'inherit'\n    },\n    paddingInline: {\n      value: '{space.xxs}'\n    }\n  },\n  link: {\n    color: {\n      value: '{components.link.color}'\n    },\n    fontSize: {\n      value: 'inherit'\n    },\n    fontWeight: {\n      value: 'normal'\n    },\n    textDecoration: {\n      value: 'none'\n    },\n    paddingInline: {\n      value: '{space.xs}'\n    },\n    paddingBlock: {\n      value: '{space.xxs}'\n    },\n    current: {\n      color: {\n        value: 'inherit'\n      },\n      fontSize: {\n        value: 'inherit'\n      },\n      fontWeight: {\n        value: 'normal'\n      },\n      textDecoration: {\n        value: 'none'\n      }\n    }\n  }\n};\nexport { breadcrumbs };", "map": {"version": 3, "names": ["breadcrumbs", "flexDirection", "value", "flexWrap", "gap", "color", "item", "fontSize", "alignItems", "lineHeight", "separator", "paddingInline", "link", "fontWeight", "textDecoration", "paddingBlock", "current"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/breadcrumbs.mjs"], "sourcesContent": ["const breadcrumbs = {\n    flexDirection: { value: 'row' },\n    flexWrap: { value: 'wrap' },\n    gap: { value: '0' },\n    color: { value: '{colors.font.tertiary}' },\n    item: {\n        flexDirection: { value: 'row' },\n        color: { value: 'inherit' },\n        fontSize: { value: 'inherit' },\n        alignItems: { value: 'center' },\n        lineHeight: { value: '1' },\n    },\n    separator: {\n        color: { value: 'inherit' },\n        fontSize: { value: 'inherit' },\n        paddingInline: { value: '{space.xxs}' },\n    },\n    link: {\n        color: { value: '{components.link.color}' },\n        fontSize: { value: 'inherit' },\n        fontWeight: { value: 'normal' },\n        textDecoration: { value: 'none' },\n        paddingInline: { value: '{space.xs}' },\n        paddingBlock: { value: '{space.xxs}' },\n        current: {\n            color: { value: 'inherit' },\n            fontSize: { value: 'inherit' },\n            fontWeight: { value: 'normal' },\n            textDecoration: { value: 'none' },\n        },\n    },\n};\n\nexport { breadcrumbs };\n"], "mappings": "AAAA,MAAMA,WAAW,GAAG;EAChBC,aAAa,EAAE;IAAEC,KAAK,EAAE;EAAM,CAAC;EAC/BC,QAAQ,EAAE;IAAED,KAAK,EAAE;EAAO,CAAC;EAC3BE,GAAG,EAAE;IAAEF,KAAK,EAAE;EAAI,CAAC;EACnBG,KAAK,EAAE;IAAEH,KAAK,EAAE;EAAyB,CAAC;EAC1CI,IAAI,EAAE;IACFL,aAAa,EAAE;MAAEC,KAAK,EAAE;IAAM,CAAC;IAC/BG,KAAK,EAAE;MAAEH,KAAK,EAAE;IAAU,CAAC;IAC3BK,QAAQ,EAAE;MAAEL,KAAK,EAAE;IAAU,CAAC;IAC9BM,UAAU,EAAE;MAAEN,KAAK,EAAE;IAAS,CAAC;IAC/BO,UAAU,EAAE;MAAEP,KAAK,EAAE;IAAI;EAC7B,CAAC;EACDQ,SAAS,EAAE;IACPL,KAAK,EAAE;MAAEH,KAAK,EAAE;IAAU,CAAC;IAC3BK,QAAQ,EAAE;MAAEL,KAAK,EAAE;IAAU,CAAC;IAC9BS,aAAa,EAAE;MAAET,KAAK,EAAE;IAAc;EAC1C,CAAC;EACDU,IAAI,EAAE;IACFP,KAAK,EAAE;MAAEH,KAAK,EAAE;IAA0B,CAAC;IAC3CK,QAAQ,EAAE;MAAEL,KAAK,EAAE;IAAU,CAAC;IAC9BW,UAAU,EAAE;MAAEX,KAAK,EAAE;IAAS,CAAC;IAC/BY,cAAc,EAAE;MAAEZ,KAAK,EAAE;IAAO,CAAC;IACjCS,aAAa,EAAE;MAAET,KAAK,EAAE;IAAa,CAAC;IACtCa,YAAY,EAAE;MAAEb,KAAK,EAAE;IAAc,CAAC;IACtCc,OAAO,EAAE;MACLX,KAAK,EAAE;QAAEH,KAAK,EAAE;MAAU,CAAC;MAC3BK,QAAQ,EAAE;QAAEL,KAAK,EAAE;MAAU,CAAC;MAC9BW,UAAU,EAAE;QAAEX,KAAK,EAAE;MAAS,CAAC;MAC/BY,cAAc,EAAE;QAAEZ,KAAK,EAAE;MAAO;IACpC;EACJ;AACJ,CAAC;AAED,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}