{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { ListStreamProcessorsRequest, ListStreamProcessorsResponse } from \"../models/models_0\";\nimport { deserializeAws_json1_1ListStreamProcessorsCommand, serializeAws_json1_1ListStreamProcessorsCommand } from \"../protocols/Aws_json1_1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Gets a list of stream processors that you have created with <a>CreateStreamProcessor</a>. </p>\n */\nvar ListStreamProcessorsCommand = /** @class */function (_super) {\n  __extends(ListStreamProcessorsCommand, _super);\n  // Start section: command_properties\n  // End section: command_properties\n  function ListStreamProcessorsCommand(input) {\n    var _this =\n    // Start section: command_constructor\n    _super.call(this) || this;\n    _this.input = input;\n    return _this;\n    // End section: command_constructor\n  }\n  /**\n   * @internal\n   */\n  ListStreamProcessorsCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n    this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n    var stack = clientStack.concat(this.middlewareStack);\n    var logger = configuration.logger;\n    var clientName = \"RekognitionClient\";\n    var commandName = \"ListStreamProcessorsCommand\";\n    var handlerExecutionContext = {\n      logger: logger,\n      clientName: clientName,\n      commandName: commandName,\n      inputFilterSensitiveLog: ListStreamProcessorsRequest.filterSensitiveLog,\n      outputFilterSensitiveLog: ListStreamProcessorsResponse.filterSensitiveLog\n    };\n    var requestHandler = configuration.requestHandler;\n    return stack.resolve(function (request) {\n      return requestHandler.handle(request.request, options || {});\n    }, handlerExecutionContext);\n  };\n  ListStreamProcessorsCommand.prototype.serialize = function (input, context) {\n    return serializeAws_json1_1ListStreamProcessorsCommand(input, context);\n  };\n  ListStreamProcessorsCommand.prototype.deserialize = function (output, context) {\n    return deserializeAws_json1_1ListStreamProcessorsCommand(output, context);\n  };\n  return ListStreamProcessorsCommand;\n}($Command);\nexport { ListStreamProcessorsCommand };", "map": {"version": 3, "names": ["__extends", "ListStreamProcessorsRequest", "ListStreamProcessorsResponse", "deserializeAws_json1_1ListStreamProcessorsCommand", "serializeAws_json1_1ListStreamProcessorsCommand", "getSerdePlugin", "Command", "$Command", "ListStreamProcessorsCommand", "_super", "input", "_this", "call", "prototype", "resolveMiddleware", "clientStack", "configuration", "options", "middlewareStack", "use", "serialize", "deserialize", "stack", "concat", "logger", "clientName", "commandName", "handlerExecutionContext", "inputFilterSensitiveLog", "filterSensitiveLog", "outputFilterSensitiveLog", "requestHandler", "resolve", "request", "handle", "context", "output"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-rekognition/dist/es/commands/ListStreamProcessorsCommand.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { ListStreamProcessorsRequest, ListStreamProcessorsResponse } from \"../models/models_0\";\nimport { deserializeAws_json1_1ListStreamProcessorsCommand, serializeAws_json1_1ListStreamProcessorsCommand, } from \"../protocols/Aws_json1_1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Gets a list of stream processors that you have created with <a>CreateStreamProcessor</a>. </p>\n */\nvar ListStreamProcessorsCommand = /** @class */ (function (_super) {\n    __extends(ListStreamProcessorsCommand, _super);\n    // Start section: command_properties\n    // End section: command_properties\n    function ListStreamProcessorsCommand(input) {\n        var _this = \n        // Start section: command_constructor\n        _super.call(this) || this;\n        _this.input = input;\n        return _this;\n        // End section: command_constructor\n    }\n    /**\n     * @internal\n     */\n    ListStreamProcessorsCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n        this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n        var stack = clientStack.concat(this.middlewareStack);\n        var logger = configuration.logger;\n        var clientName = \"RekognitionClient\";\n        var commandName = \"ListStreamProcessorsCommand\";\n        var handlerExecutionContext = {\n            logger: logger,\n            clientName: clientName,\n            commandName: commandName,\n            inputFilterSensitiveLog: ListStreamProcessorsRequest.filterSensitiveLog,\n            outputFilterSensitiveLog: ListStreamProcessorsResponse.filterSensitiveLog,\n        };\n        var requestHandler = configuration.requestHandler;\n        return stack.resolve(function (request) {\n            return requestHandler.handle(request.request, options || {});\n        }, handlerExecutionContext);\n    };\n    ListStreamProcessorsCommand.prototype.serialize = function (input, context) {\n        return serializeAws_json1_1ListStreamProcessorsCommand(input, context);\n    };\n    ListStreamProcessorsCommand.prototype.deserialize = function (output, context) {\n        return deserializeAws_json1_1ListStreamProcessorsCommand(output, context);\n    };\n    return ListStreamProcessorsCommand;\n}($Command));\nexport { ListStreamProcessorsCommand };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,2BAA2B,EAAEC,4BAA4B,QAAQ,oBAAoB;AAC9F,SAASC,iDAAiD,EAAEC,+CAA+C,QAAS,0BAA0B;AAC9I,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,OAAO,IAAIC,QAAQ,QAAQ,wBAAwB;AAC5D;AACA;AACA;AACA,IAAIC,2BAA2B,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC/DT,SAAS,CAACQ,2BAA2B,EAAEC,MAAM,CAAC;EAC9C;EACA;EACA,SAASD,2BAA2BA,CAACE,KAAK,EAAE;IACxC,IAAIC,KAAK;IACT;IACAF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACzBD,KAAK,CAACD,KAAK,GAAGA,KAAK;IACnB,OAAOC,KAAK;IACZ;EACJ;EACA;AACJ;AACA;EACIH,2BAA2B,CAACK,SAAS,CAACC,iBAAiB,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAEC,OAAO,EAAE;IACrG,IAAI,CAACC,eAAe,CAACC,GAAG,CAACd,cAAc,CAACW,aAAa,EAAE,IAAI,CAACI,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;IACzF,IAAIC,KAAK,GAAGP,WAAW,CAACQ,MAAM,CAAC,IAAI,CAACL,eAAe,CAAC;IACpD,IAAIM,MAAM,GAAGR,aAAa,CAACQ,MAAM;IACjC,IAAIC,UAAU,GAAG,mBAAmB;IACpC,IAAIC,WAAW,GAAG,6BAA6B;IAC/C,IAAIC,uBAAuB,GAAG;MAC1BH,MAAM,EAAEA,MAAM;MACdC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA,WAAW;MACxBE,uBAAuB,EAAE3B,2BAA2B,CAAC4B,kBAAkB;MACvEC,wBAAwB,EAAE5B,4BAA4B,CAAC2B;IAC3D,CAAC;IACD,IAAIE,cAAc,GAAGf,aAAa,CAACe,cAAc;IACjD,OAAOT,KAAK,CAACU,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpC,OAAOF,cAAc,CAACG,MAAM,CAACD,OAAO,CAACA,OAAO,EAAEhB,OAAO,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC,EAAEU,uBAAuB,CAAC;EAC/B,CAAC;EACDnB,2BAA2B,CAACK,SAAS,CAACO,SAAS,GAAG,UAAUV,KAAK,EAAEyB,OAAO,EAAE;IACxE,OAAO/B,+CAA+C,CAACM,KAAK,EAAEyB,OAAO,CAAC;EAC1E,CAAC;EACD3B,2BAA2B,CAACK,SAAS,CAACQ,WAAW,GAAG,UAAUe,MAAM,EAAED,OAAO,EAAE;IAC3E,OAAOhC,iDAAiD,CAACiC,MAAM,EAAED,OAAO,CAAC;EAC7E,CAAC;EACD,OAAO3B,2BAA2B;AACtC,CAAC,CAACD,QAAQ,CAAE;AACZ,SAASC,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}