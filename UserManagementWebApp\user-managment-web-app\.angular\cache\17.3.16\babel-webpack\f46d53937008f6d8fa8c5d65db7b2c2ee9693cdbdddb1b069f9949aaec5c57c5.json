{"ast": null, "code": "import { GraphQLError } from \"../../error/GraphQLError.mjs\";\nimport { isEnumType } from \"../../type/definition.mjs\";\n\n/**\n * Unique enum value names\n *\n * A GraphQL enum type is only valid if all its values are uniquely named.\n */\nexport function UniqueEnumValueNamesRule(context) {\n  var schema = context.getSchema();\n  var existingTypeMap = schema ? schema.getTypeMap() : Object.create(null);\n  var knownValueNames = Object.create(null);\n  return {\n    EnumTypeDefinition: checkValueUniqueness,\n    EnumTypeExtension: checkValueUniqueness\n  };\n  function checkValueUniqueness(node) {\n    var _node$values;\n    var typeName = node.name.value;\n    if (!knownValueNames[typeName]) {\n      knownValueNames[typeName] = Object.create(null);\n    } // istanbul ignore next (See: 'https://github.com/graphql/graphql-js/issues/2203')\n\n    var valueNodes = (_node$values = node.values) !== null && _node$values !== void 0 ? _node$values : [];\n    var valueNames = knownValueNames[typeName];\n    for (var _i2 = 0; _i2 < valueNodes.length; _i2++) {\n      var valueDef = valueNodes[_i2];\n      var valueName = valueDef.name.value;\n      var existingType = existingTypeMap[typeName];\n      if (isEnumType(existingType) && existingType.getValue(valueName)) {\n        context.reportError(new GraphQLError(\"Enum value \\\"\".concat(typeName, \".\").concat(valueName, \"\\\" already exists in the schema. It cannot also be defined in this type extension.\"), valueDef.name));\n      } else if (valueNames[valueName]) {\n        context.reportError(new GraphQLError(\"Enum value \\\"\".concat(typeName, \".\").concat(valueName, \"\\\" can only be defined once.\"), [valueNames[valueName], valueDef.name]));\n      } else {\n        valueNames[valueName] = valueDef.name;\n      }\n    }\n    return false;\n  }\n}", "map": {"version": 3, "names": ["GraphQLError", "isEnumType", "UniqueEnumValueNamesRule", "context", "schema", "getSchema", "existingTypeMap", "getTypeMap", "Object", "create", "knownValueNames", "EnumTypeDefinition", "checkValueUniqueness", "EnumTypeExtension", "node", "_node$values", "typeName", "name", "value", "valueNodes", "values", "valueNames", "_i2", "length", "valueDef", "valueName", "existingType", "getValue", "reportError", "concat"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/graphql/validation/rules/UniqueEnumValueNamesRule.mjs"], "sourcesContent": ["import { GraphQLError } from \"../../error/GraphQLError.mjs\";\nimport { isEnumType } from \"../../type/definition.mjs\";\n\n/**\n * Unique enum value names\n *\n * A GraphQL enum type is only valid if all its values are uniquely named.\n */\nexport function UniqueEnumValueNamesRule(context) {\n  var schema = context.getSchema();\n  var existingTypeMap = schema ? schema.getTypeMap() : Object.create(null);\n  var knownValueNames = Object.create(null);\n  return {\n    EnumTypeDefinition: checkValueUniqueness,\n    EnumTypeExtension: checkValueUniqueness\n  };\n\n  function checkValueUniqueness(node) {\n    var _node$values;\n\n    var typeName = node.name.value;\n\n    if (!knownValueNames[typeName]) {\n      knownValueNames[typeName] = Object.create(null);\n    } // istanbul ignore next (See: 'https://github.com/graphql/graphql-js/issues/2203')\n\n\n    var valueNodes = (_node$values = node.values) !== null && _node$values !== void 0 ? _node$values : [];\n    var valueNames = knownValueNames[typeName];\n\n    for (var _i2 = 0; _i2 < valueNodes.length; _i2++) {\n      var valueDef = valueNodes[_i2];\n      var valueName = valueDef.name.value;\n      var existingType = existingTypeMap[typeName];\n\n      if (isEnumType(existingType) && existingType.getValue(valueName)) {\n        context.reportError(new GraphQLError(\"Enum value \\\"\".concat(typeName, \".\").concat(valueName, \"\\\" already exists in the schema. It cannot also be defined in this type extension.\"), valueDef.name));\n      } else if (valueNames[valueName]) {\n        context.reportError(new GraphQLError(\"Enum value \\\"\".concat(typeName, \".\").concat(valueName, \"\\\" can only be defined once.\"), [valueNames[valueName], valueDef.name]));\n      } else {\n        valueNames[valueName] = valueDef.name;\n      }\n    }\n\n    return false;\n  }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,UAAU,QAAQ,2BAA2B;;AAEtD;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAACC,OAAO,EAAE;EAChD,IAAIC,MAAM,GAAGD,OAAO,CAACE,SAAS,CAAC,CAAC;EAChC,IAAIC,eAAe,GAAGF,MAAM,GAAGA,MAAM,CAACG,UAAU,CAAC,CAAC,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACxE,IAAIC,eAAe,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACzC,OAAO;IACLE,kBAAkB,EAAEC,oBAAoB;IACxCC,iBAAiB,EAAED;EACrB,CAAC;EAED,SAASA,oBAAoBA,CAACE,IAAI,EAAE;IAClC,IAAIC,YAAY;IAEhB,IAAIC,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACC,KAAK;IAE9B,IAAI,CAACR,eAAe,CAACM,QAAQ,CAAC,EAAE;MAC9BN,eAAe,CAACM,QAAQ,CAAC,GAAGR,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACjD,CAAC,CAAC;;IAGF,IAAIU,UAAU,GAAG,CAACJ,YAAY,GAAGD,IAAI,CAACM,MAAM,MAAM,IAAI,IAAIL,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG,EAAE;IACrG,IAAIM,UAAU,GAAGX,eAAe,CAACM,QAAQ,CAAC;IAE1C,KAAK,IAAIM,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGH,UAAU,CAACI,MAAM,EAAED,GAAG,EAAE,EAAE;MAChD,IAAIE,QAAQ,GAAGL,UAAU,CAACG,GAAG,CAAC;MAC9B,IAAIG,SAAS,GAAGD,QAAQ,CAACP,IAAI,CAACC,KAAK;MACnC,IAAIQ,YAAY,GAAGpB,eAAe,CAACU,QAAQ,CAAC;MAE5C,IAAIf,UAAU,CAACyB,YAAY,CAAC,IAAIA,YAAY,CAACC,QAAQ,CAACF,SAAS,CAAC,EAAE;QAChEtB,OAAO,CAACyB,WAAW,CAAC,IAAI5B,YAAY,CAAC,eAAe,CAAC6B,MAAM,CAACb,QAAQ,EAAE,GAAG,CAAC,CAACa,MAAM,CAACJ,SAAS,EAAE,oFAAoF,CAAC,EAAED,QAAQ,CAACP,IAAI,CAAC,CAAC;MACrM,CAAC,MAAM,IAAII,UAAU,CAACI,SAAS,CAAC,EAAE;QAChCtB,OAAO,CAACyB,WAAW,CAAC,IAAI5B,YAAY,CAAC,eAAe,CAAC6B,MAAM,CAACb,QAAQ,EAAE,GAAG,CAAC,CAACa,MAAM,CAACJ,SAAS,EAAE,8BAA8B,CAAC,EAAE,CAACJ,UAAU,CAACI,SAAS,CAAC,EAAED,QAAQ,CAACP,IAAI,CAAC,CAAC,CAAC;MACxK,CAAC,MAAM;QACLI,UAAU,CAACI,SAAS,CAAC,GAAGD,QAAQ,CAACP,IAAI;MACvC;IACF;IAEA,OAAO,KAAK;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}