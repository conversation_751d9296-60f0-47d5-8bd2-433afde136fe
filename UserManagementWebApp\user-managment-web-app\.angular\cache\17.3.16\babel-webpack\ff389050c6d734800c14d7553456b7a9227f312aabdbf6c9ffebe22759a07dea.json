{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, assertOAuthConfig, urlSafeEncode, isBrowser, AuthAction } from '@aws-amplify/core/internals/utils';\nimport '../utils/oauth/enableOAuthListener.mjs';\nimport { cognitoHostedUIIdentityProviderMap } from '../types/models.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { openAuthSession } from '../../../utils/openAuthSession.mjs';\nimport { assertUserNotAuthenticated } from '../utils/signInHelpers.mjs';\nimport { generateCodeVerifier } from '../utils/oauth/generateCodeVerifier.mjs';\nimport { generateState } from '../utils/oauth/generateState.mjs';\nimport '../utils/refreshAuthTokens.mjs';\nimport '../tokenProvider/errorHelpers.mjs';\nimport { oAuthStore } from '../utils/oauth/oAuthStore.mjs';\nimport '../tokenProvider/tokenProvider.mjs';\nimport { getRedirectUrl } from '../utils/oauth/getRedirectUrl.mjs';\nimport { handleFailure } from '../utils/oauth/handleFailure.mjs';\nimport { completeOAuthFlow } from '../utils/oauth/completeOAuthFlow.mjs';\nimport '../../../types/Auth.mjs';\nimport { createOAuthError } from '../utils/oauth/createOAuthError.mjs';\nimport { listenForOAuthFlowCancellation } from '../utils/oauth/cancelOAuthFlow.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Signs in a user with OAuth. Redirects the application to an Identity Provider.\n *\n * @param input - The SignInWithRedirectInput object, if empty it will redirect to Cognito HostedUI\n *\n * @throws AuthTokenConfigException - Thrown when the user pool config is invalid.\n * @throws OAuthNotConfigureException - Thrown when the oauth config is invalid.\n */\nfunction signInWithRedirect(_x) {\n  return _signInWithRedirect.apply(this, arguments);\n}\nfunction _signInWithRedirect() {\n  _signInWithRedirect = _asyncToGenerator(function* (input) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    assertOAuthConfig(authConfig);\n    oAuthStore.setAuthConfig(authConfig);\n    yield assertUserNotAuthenticated();\n    let provider = 'COGNITO'; // Default\n    if (typeof input?.provider === 'string') {\n      provider = cognitoHostedUIIdentityProviderMap[input.provider];\n    } else if (input?.provider?.custom) {\n      provider = input.provider.custom;\n    }\n    return oauthSignIn({\n      oauthConfig: authConfig.loginWith.oauth,\n      clientId: authConfig.userPoolClientId,\n      provider,\n      customState: input?.customState,\n      preferPrivateSession: input?.options?.preferPrivateSession,\n      options: {\n        loginHint: input?.options?.loginHint,\n        lang: input?.options?.lang,\n        nonce: input?.options?.nonce\n      }\n    });\n  });\n  return _signInWithRedirect.apply(this, arguments);\n}\nconst oauthSignIn = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* ({\n    oauthConfig,\n    provider,\n    clientId,\n    customState,\n    preferPrivateSession,\n    options\n  }) {\n    const {\n      domain,\n      redirectSignIn,\n      responseType,\n      scopes\n    } = oauthConfig;\n    const {\n      loginHint,\n      lang,\n      nonce\n    } = options ?? {};\n    const randomState = generateState();\n    /* encodeURIComponent is not URL safe, use urlSafeEncode instead. Cognito\n    single-encodes/decodes url on first sign in and double-encodes/decodes url\n    when user already signed in. Using encodeURIComponent, Base32, Base64 add\n    characters % or = which on further encoding becomes unsafe. '=' create issue\n    for parsing query params.\n    Refer: https://github.com/aws-amplify/amplify-js/issues/5218 */\n    const state = customState ? `${randomState}-${urlSafeEncode(customState)}` : randomState;\n    const {\n      value,\n      method,\n      toCodeChallenge\n    } = generateCodeVerifier(128);\n    const redirectUri = getRedirectUrl(oauthConfig.redirectSignIn);\n    if (isBrowser()) oAuthStore.storeOAuthInFlight(true);\n    oAuthStore.storeOAuthState(state);\n    oAuthStore.storePKCE(value);\n    const queryString = Object.entries({\n      redirect_uri: redirectUri,\n      response_type: responseType,\n      client_id: clientId,\n      identity_provider: provider,\n      scope: scopes.join(' '),\n      // eslint-disable-next-line camelcase\n      ...(loginHint && {\n        login_hint: loginHint\n      }),\n      ...(lang && {\n        lang\n      }),\n      ...(nonce && {\n        nonce\n      }),\n      state,\n      ...(responseType === 'code' && {\n        code_challenge: toCodeChallenge(),\n        code_challenge_method: method\n      })\n    }).map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(v)}`).join('&');\n    // TODO(v6): use URL object instead\n    const oAuthUrl = `https://${domain}/oauth2/authorize?${queryString}`;\n    // this will only take effect in the following scenarios:\n    // 1. the user cancels the OAuth flow on web via back button, and\n    // 2. when bfcache is enabled\n    listenForOAuthFlowCancellation(oAuthStore);\n    // the following is effective only in react-native as openAuthSession resolves only in react-native\n    const {\n      type,\n      error,\n      url\n    } = (yield openAuthSession(oAuthUrl)) ?? {};\n    try {\n      if (type === 'error') {\n        throw createOAuthError(String(error));\n      }\n      if (type === 'success' && url) {\n        yield completeOAuthFlow({\n          currentUrl: url,\n          clientId,\n          domain,\n          redirectUri,\n          responseType,\n          userAgentValue: getAuthUserAgentValue(AuthAction.SignInWithRedirect),\n          preferPrivateSession\n        });\n      }\n    } catch (err) {\n      yield handleFailure(err);\n      // rethrow the error so it can be caught by `await signInWithRedirect()` in react-native\n      throw err;\n    }\n  });\n  return function oauthSignIn(_x2) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { signInWithRedirect };", "map": {"version": 3, "names": ["Amplify", "assertTokenProviderConfig", "assertOAuthConfig", "urlSafeEncode", "<PERSON><PERSON><PERSON><PERSON>", "AuthAction", "cognitoHostedUIIdentityProviderMap", "getAuthUserAgentValue", "openAuthSession", "assertUserNotAuthenticated", "generateCodeVerifier", "generateState", "oAuthStore", "getRedirectUrl", "handleFailure", "completeOAuthFlow", "createOAuthError", "listenForOAuthFlowCancellation", "signInWithRedirect", "_x", "_signInWithRedirect", "apply", "arguments", "_asyncToGenerator", "input", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "setAuthConfig", "provider", "custom", "oauthSignIn", "oauthConfig", "loginWith", "o<PERSON>h", "clientId", "userPoolClientId", "customState", "preferPrivateSession", "options", "loginHint", "lang", "nonce", "_ref", "domain", "redirectSignIn", "responseType", "scopes", "randomState", "state", "value", "method", "toCodeChallenge", "redirectUri", "storeOAuthInFlight", "storeOAuthState", "storePKCE", "queryString", "Object", "entries", "redirect_uri", "response_type", "client_id", "identity_provider", "scope", "join", "login_hint", "code_challenge", "code_challenge_method", "map", "k", "v", "encodeURIComponent", "oAuthUrl", "type", "error", "url", "String", "currentUrl", "userAgentValue", "SignInWithRedirect", "err", "_x2"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/signInWithRedirect.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, assertOAuthConfig, urlSafeEnco<PERSON>, isBrowser, AuthAction } from '@aws-amplify/core/internals/utils';\nimport '../utils/oauth/enableOAuthListener.mjs';\nimport { cognitoHostedUIIdentityProviderMap } from '../types/models.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { openAuthSession } from '../../../utils/openAuthSession.mjs';\nimport { assertUserNotAuthenticated } from '../utils/signInHelpers.mjs';\nimport { generateCodeVerifier } from '../utils/oauth/generateCodeVerifier.mjs';\nimport { generateState } from '../utils/oauth/generateState.mjs';\nimport '../utils/refreshAuthTokens.mjs';\nimport '../tokenProvider/errorHelpers.mjs';\nimport { oAuthStore } from '../utils/oauth/oAuthStore.mjs';\nimport '../tokenProvider/tokenProvider.mjs';\nimport { getRedirectUrl } from '../utils/oauth/getRedirectUrl.mjs';\nimport { handleFailure } from '../utils/oauth/handleFailure.mjs';\nimport { completeOAuthFlow } from '../utils/oauth/completeOAuthFlow.mjs';\nimport '../../../types/Auth.mjs';\nimport { createOAuthError } from '../utils/oauth/createOAuthError.mjs';\nimport { listenForOAuthFlowCancellation } from '../utils/oauth/cancelOAuthFlow.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Signs in a user with OAuth. Redirects the application to an Identity Provider.\n *\n * @param input - The SignInWithRedirectInput object, if empty it will redirect to Cognito HostedUI\n *\n * @throws AuthTokenConfigException - Thrown when the user pool config is invalid.\n * @throws OAuthNotConfigureException - Thrown when the oauth config is invalid.\n */\nasync function signInWithRedirect(input) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    assertOAuthConfig(authConfig);\n    oAuthStore.setAuthConfig(authConfig);\n    await assertUserNotAuthenticated();\n    let provider = 'COGNITO'; // Default\n    if (typeof input?.provider === 'string') {\n        provider = cognitoHostedUIIdentityProviderMap[input.provider];\n    }\n    else if (input?.provider?.custom) {\n        provider = input.provider.custom;\n    }\n    return oauthSignIn({\n        oauthConfig: authConfig.loginWith.oauth,\n        clientId: authConfig.userPoolClientId,\n        provider,\n        customState: input?.customState,\n        preferPrivateSession: input?.options?.preferPrivateSession,\n        options: {\n            loginHint: input?.options?.loginHint,\n            lang: input?.options?.lang,\n            nonce: input?.options?.nonce,\n        },\n    });\n}\nconst oauthSignIn = async ({ oauthConfig, provider, clientId, customState, preferPrivateSession, options, }) => {\n    const { domain, redirectSignIn, responseType, scopes } = oauthConfig;\n    const { loginHint, lang, nonce } = options ?? {};\n    const randomState = generateState();\n    /* encodeURIComponent is not URL safe, use urlSafeEncode instead. Cognito\n    single-encodes/decodes url on first sign in and double-encodes/decodes url\n    when user already signed in. Using encodeURIComponent, Base32, Base64 add\n    characters % or = which on further encoding becomes unsafe. '=' create issue\n    for parsing query params.\n    Refer: https://github.com/aws-amplify/amplify-js/issues/5218 */\n    const state = customState\n        ? `${randomState}-${urlSafeEncode(customState)}`\n        : randomState;\n    const { value, method, toCodeChallenge } = generateCodeVerifier(128);\n    const redirectUri = getRedirectUrl(oauthConfig.redirectSignIn);\n    if (isBrowser())\n        oAuthStore.storeOAuthInFlight(true);\n    oAuthStore.storeOAuthState(state);\n    oAuthStore.storePKCE(value);\n    const queryString = Object.entries({\n        redirect_uri: redirectUri,\n        response_type: responseType,\n        client_id: clientId,\n        identity_provider: provider,\n        scope: scopes.join(' '),\n        // eslint-disable-next-line camelcase\n        ...(loginHint && { login_hint: loginHint }),\n        ...(lang && { lang }),\n        ...(nonce && { nonce }),\n        state,\n        ...(responseType === 'code' && {\n            code_challenge: toCodeChallenge(),\n            code_challenge_method: method,\n        }),\n    })\n        .map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(v)}`)\n        .join('&');\n    // TODO(v6): use URL object instead\n    const oAuthUrl = `https://${domain}/oauth2/authorize?${queryString}`;\n    // this will only take effect in the following scenarios:\n    // 1. the user cancels the OAuth flow on web via back button, and\n    // 2. when bfcache is enabled\n    listenForOAuthFlowCancellation(oAuthStore);\n    // the following is effective only in react-native as openAuthSession resolves only in react-native\n    const { type, error, url } = (await openAuthSession(oAuthUrl)) ??\n        {};\n    try {\n        if (type === 'error') {\n            throw createOAuthError(String(error));\n        }\n        if (type === 'success' && url) {\n            await completeOAuthFlow({\n                currentUrl: url,\n                clientId,\n                domain,\n                redirectUri,\n                responseType,\n                userAgentValue: getAuthUserAgentValue(AuthAction.SignInWithRedirect),\n                preferPrivateSession,\n            });\n        }\n    }\n    catch (err) {\n        await handleFailure(err);\n        // rethrow the error so it can be caught by `await signInWithRedirect()` in react-native\n        throw err;\n    }\n};\n\nexport { signInWithRedirect };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,yBAAyB,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,SAAS,EAAEC,UAAU,QAAQ,mCAAmC;AACtI,OAAO,wCAAwC;AAC/C,SAASC,kCAAkC,QAAQ,qBAAqB;AACxE,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,0BAA0B,QAAQ,4BAA4B;AACvE,SAASC,oBAAoB,QAAQ,yCAAyC;AAC9E,SAASC,aAAa,QAAQ,kCAAkC;AAChE,OAAO,gCAAgC;AACvC,OAAO,mCAAmC;AAC1C,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,OAAO,oCAAoC;AAC3C,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,OAAO,yBAAyB;AAChC,SAASC,gBAAgB,QAAQ,qCAAqC;AACtE,SAASC,8BAA8B,QAAQ,oCAAoC;;AAEnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAQeC,kBAAkBA,CAAAC,EAAA;EAAA,OAAAC,mBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,oBAAA;EAAAA,mBAAA,GAAAG,iBAAA,CAAjC,WAAkCC,KAAK,EAAE;IACrC,MAAMC,UAAU,GAAGzB,OAAO,CAAC0B,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpD3B,yBAAyB,CAACwB,UAAU,CAAC;IACrCvB,iBAAiB,CAACuB,UAAU,CAAC;IAC7Bb,UAAU,CAACiB,aAAa,CAACJ,UAAU,CAAC;IACpC,MAAMhB,0BAA0B,CAAC,CAAC;IAClC,IAAIqB,QAAQ,GAAG,SAAS,CAAC,CAAC;IAC1B,IAAI,OAAON,KAAK,EAAEM,QAAQ,KAAK,QAAQ,EAAE;MACrCA,QAAQ,GAAGxB,kCAAkC,CAACkB,KAAK,CAACM,QAAQ,CAAC;IACjE,CAAC,MACI,IAAIN,KAAK,EAAEM,QAAQ,EAAEC,MAAM,EAAE;MAC9BD,QAAQ,GAAGN,KAAK,CAACM,QAAQ,CAACC,MAAM;IACpC;IACA,OAAOC,WAAW,CAAC;MACfC,WAAW,EAAER,UAAU,CAACS,SAAS,CAACC,KAAK;MACvCC,QAAQ,EAAEX,UAAU,CAACY,gBAAgB;MACrCP,QAAQ;MACRQ,WAAW,EAAEd,KAAK,EAAEc,WAAW;MAC/BC,oBAAoB,EAAEf,KAAK,EAAEgB,OAAO,EAAED,oBAAoB;MAC1DC,OAAO,EAAE;QACLC,SAAS,EAAEjB,KAAK,EAAEgB,OAAO,EAAEC,SAAS;QACpCC,IAAI,EAAElB,KAAK,EAAEgB,OAAO,EAAEE,IAAI;QAC1BC,KAAK,EAAEnB,KAAK,EAAEgB,OAAO,EAAEG;MAC3B;IACJ,CAAC,CAAC;EACN,CAAC;EAAA,OAAAvB,mBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AACD,MAAMU,WAAW;EAAA,IAAAY,IAAA,GAAArB,iBAAA,CAAG,WAAO;IAAEU,WAAW;IAAEH,QAAQ;IAAEM,QAAQ;IAAEE,WAAW;IAAEC,oBAAoB;IAAEC;EAAS,CAAC,EAAK;IAC5G,MAAM;MAAEK,MAAM;MAAEC,cAAc;MAAEC,YAAY;MAAEC;IAAO,CAAC,GAAGf,WAAW;IACpE,MAAM;MAAEQ,SAAS;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGH,OAAO,IAAI,CAAC,CAAC;IAChD,MAAMS,WAAW,GAAGtC,aAAa,CAAC,CAAC;IACnC;AACJ;AACA;AACA;AACA;AACA;IACI,MAAMuC,KAAK,GAAGZ,WAAW,GACnB,GAAGW,WAAW,IAAI9C,aAAa,CAACmC,WAAW,CAAC,EAAE,GAC9CW,WAAW;IACjB,MAAM;MAAEE,KAAK;MAAEC,MAAM;MAAEC;IAAgB,CAAC,GAAG3C,oBAAoB,CAAC,GAAG,CAAC;IACpE,MAAM4C,WAAW,GAAGzC,cAAc,CAACoB,WAAW,CAACa,cAAc,CAAC;IAC9D,IAAI1C,SAAS,CAAC,CAAC,EACXQ,UAAU,CAAC2C,kBAAkB,CAAC,IAAI,CAAC;IACvC3C,UAAU,CAAC4C,eAAe,CAACN,KAAK,CAAC;IACjCtC,UAAU,CAAC6C,SAAS,CAACN,KAAK,CAAC;IAC3B,MAAMO,WAAW,GAAGC,MAAM,CAACC,OAAO,CAAC;MAC/BC,YAAY,EAAEP,WAAW;MACzBQ,aAAa,EAAEf,YAAY;MAC3BgB,SAAS,EAAE3B,QAAQ;MACnB4B,iBAAiB,EAAElC,QAAQ;MAC3BmC,KAAK,EAAEjB,MAAM,CAACkB,IAAI,CAAC,GAAG,CAAC;MACvB;MACA,IAAIzB,SAAS,IAAI;QAAE0B,UAAU,EAAE1B;MAAU,CAAC,CAAC;MAC3C,IAAIC,IAAI,IAAI;QAAEA;MAAK,CAAC,CAAC;MACrB,IAAIC,KAAK,IAAI;QAAEA;MAAM,CAAC,CAAC;MACvBO,KAAK;MACL,IAAIH,YAAY,KAAK,MAAM,IAAI;QAC3BqB,cAAc,EAAEf,eAAe,CAAC,CAAC;QACjCgB,qBAAqB,EAAEjB;MAC3B,CAAC;IACL,CAAC,CAAC,CACGkB,GAAG,CAAC,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,KAAK,GAAGC,kBAAkB,CAACF,CAAC,CAAC,IAAIE,kBAAkB,CAACD,CAAC,CAAC,EAAE,CAAC,CACpEN,IAAI,CAAC,GAAG,CAAC;IACd;IACA,MAAMQ,QAAQ,GAAG,WAAW7B,MAAM,qBAAqBa,WAAW,EAAE;IACpE;IACA;IACA;IACAzC,8BAA8B,CAACL,UAAU,CAAC;IAC1C;IACA,MAAM;MAAE+D,IAAI;MAAEC,KAAK;MAAEC;IAAI,CAAC,GAAG,OAAOrE,eAAe,CAACkE,QAAQ,CAAC,KACzD,CAAC,CAAC;IACN,IAAI;MACA,IAAIC,IAAI,KAAK,OAAO,EAAE;QAClB,MAAM3D,gBAAgB,CAAC8D,MAAM,CAACF,KAAK,CAAC,CAAC;MACzC;MACA,IAAID,IAAI,KAAK,SAAS,IAAIE,GAAG,EAAE;QAC3B,MAAM9D,iBAAiB,CAAC;UACpBgE,UAAU,EAAEF,GAAG;UACfzC,QAAQ;UACRS,MAAM;UACNS,WAAW;UACXP,YAAY;UACZiC,cAAc,EAAEzE,qBAAqB,CAACF,UAAU,CAAC4E,kBAAkB,CAAC;UACpE1C;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CACD,OAAO2C,GAAG,EAAE;MACR,MAAMpE,aAAa,CAACoE,GAAG,CAAC;MACxB;MACA,MAAMA,GAAG;IACb;EACJ,CAAC;EAAA,gBAnEKlD,WAAWA,CAAAmD,GAAA;IAAA,OAAAvC,IAAA,CAAAvB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAmEhB;AAED,SAASJ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}