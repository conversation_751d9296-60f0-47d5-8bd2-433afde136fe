{"ast": null, "code": "import { Amplify } from '../Amplify.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction clearCredentials() {\n  return Amplify.Auth.clearCredentials();\n}\nexport { clearCredentials };", "map": {"version": 3, "names": ["Amplify", "clearCredentials", "<PERSON><PERSON>"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/singleton/apis/clearCredentials.mjs"], "sourcesContent": ["import { Amplify } from '../Amplify.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction clearCredentials() {\n    return Amplify.Auth.clearCredentials();\n}\n\nexport { clearCredentials };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,gBAAgB;;AAExC;AACA;AACA,SAASC,gBAAgBA,CAAA,EAAG;EACxB,OAAOD,OAAO,CAACE,IAAI,CAACD,gBAAgB,CAAC,CAAC;AAC1C;AAEA,SAASA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}