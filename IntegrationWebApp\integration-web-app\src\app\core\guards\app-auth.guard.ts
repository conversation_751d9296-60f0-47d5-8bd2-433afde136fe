import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn, Router } from '@angular/router';
import { constants } from '../constants/constants';
import { AppGroupRole } from '../models/app-group-role';
import { Application } from '../models/application';
import { AppService } from '../services/app.service';
import { BreadcrumbService } from '../services/breadcrumb.service';
import { ProjectsService } from '../services/projects.service';
import { RouterInfoService } from '../services/router-info.service';
import { CompanyService } from '../services/company.service ';
import { firstValueFrom } from 'rxjs';
import { TenantFeature } from '../enums/tenant-feature.enum';

export const appAuthGuard: CanActivateFn = async (route: ActivatedRouteSnapshot) => {
  const appService = inject(AppService);
  const projectsService = inject(ProjectsService);
  const breadcrumbService = inject(BreadcrumbService);
  const routerInfoService = inject(RouterInfoService);
  const router = inject(Router);
  const companyService = inject(CompanyService);
  const routerParams = routerInfoService
    .collectRouteParams(route.root);
  const versionId = +routerParams.versionId;
  const assetId = routerParams.workflowId;
  let appGroupRoles = appService.cachedAppGroupRoles;
  if (!appGroupRoles?.length) {
    appGroupRoles = await firstValueFrom(appService.fetchAppGroupRolesForCurrentUserTenant());
  }

  let app: Application = null;

  if (assetId) {
    app = await getApp(breadcrumbService, projectsService, versionId);
  }

  const hasAccess = await checkAccess(companyService, router, appGroupRoles, versionId, assetId, app);
  return hasAccess;
};

async function getApp(breadcrumbService: BreadcrumbService, projectsService: ProjectsService, versionId: number) {
  let currentProjectVersion = versionId ? breadcrumbService?.currentProjectVersion : null;
  let app: Application = null;

  if (!currentProjectVersion || currentProjectVersion.id !== versionId) {
    currentProjectVersion = await firstValueFrom(projectsService.getProjectVersion(versionId));
    breadcrumbService.currentProjectVersion = currentProjectVersion;
  }

  if (currentProjectVersion?.isApp) {
    app = currentProjectVersion.appManifest;
  }

  return app;
}

async function checkAccess(companyService: CompanyService, router: Router, appGroupRoles: AppGroupRole[], versionId?: number, assetId?: string, app?: Application) {
  let filteredAppGroupRoles = [...appGroupRoles];
  if (versionId) {
    filteredAppGroupRoles = filteredAppGroupRoles
      .filter(appGroupRole => appGroupRole.projectVersionId === versionId);
  }

  if (assetId) {
    const defaultMenuDetails = app?.menus?.find(m => m.name === 'Default');
    const roles = defaultMenuDetails?.roles ?? [];
    const assetRoles = roles
      .filter(role => role.assets
        .some(asset => asset.identifier === assetId &&
          asset.permission !== constants.appPermissions.nonePermission.name));
    filteredAppGroupRoles = filteredAppGroupRoles
      .filter(appGroupRole => assetRoles.some(role => role.code === appGroupRole.roleCode));
  }

  let hasAccess = filteredAppGroupRoles.length > 0;
  if (hasAccess) {
    // If the company info is not in cache or is an empty object({}), get it from the API.
    if(!companyService.getCompanyInformationFromCache()?.id) {
      await firstValueFrom(companyService.getCompany());
    }

    hasAccess = companyService.hasTenantFeatures(TenantFeature.APP_DASHBOARD);
  }

  if (!hasAccess) {
    router.navigateByUrl('/accessDenied');
  }

  return hasAccess;
}
