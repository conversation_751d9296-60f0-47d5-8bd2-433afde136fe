{"ast": null, "code": "import { AggridActionableGridConfigDropDownRendererComponent } from 'src/app/core/ag-grid/renderers/ag-grid-config-dropdown.renderer.component';\nimport { AgGridFormatSettingRendererComponent } from 'src/app/core/ag-grid/renderers/ag-grid-format-settings.renderer.component';\nimport { AgGridMattSlideToggleRendererComponent } from 'src/app/core/ag-grid/renderers/ag-grid-matt-slide-toggle.renderer.component';\nimport { AgGridCheckboxRendererComponent } from '../../core/ag-grid/renderers/ag-grid-checkbox.renderer.component';\nimport { AgGridDefaultValueRendererComponent } from 'src/app/core/ag-grid/renderers/ag-grid-default-value.renderer.component';\nimport { ActionableGridColumnType } from 'src/app/core/enums/actionable-grid-enums';\nimport { JsonDataTypes } from 'src/app/shared/enums/json-schema.enum';\nimport { AgGridCheckMarkRendererComponent } from 'src/app/core/ag-grid/renderers/ag-grid-check-mark.renderer.component';\nimport { AgGridModule } from 'ag-grid-angular';\nimport { MessageModule } from 'primeng/message';\nimport { ButtonModule } from 'primeng/button';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/report-admin.service\";\nimport * as i2 from \"ag-grid-angular\";\nimport * as i3 from \"primeng/message\";\nimport * as i4 from \"primeng/button\";\nfunction ActionableGridConfigComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-message\", 1);\n  }\n}\nfunction ActionableGridConfigComponent_Conditional_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"span\", 4)(2, \"p-button\", 5);\n    i0.ɵɵlistener(\"click\", function ActionableGridConfigComponent_Conditional_2_Conditional_0_Template_p_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onClickScrollIndicator());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"rounded\", false)(\"outlined\", true);\n  }\n}\nfunction ActionableGridConfigComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵtemplate(0, ActionableGridConfigComponent_Conditional_2_Conditional_0_Template, 3, 2, \"div\", 2);\n    i0.ɵɵelementStart(1, \"ag-grid-angular\", 3);\n    i0.ɵɵlistener(\"rowDragEnd\", function ActionableGridConfigComponent_Conditional_2_Template_ag_grid_angular_rowDragEnd_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRowDragEnd($event));\n    })(\"gridReady\", function ActionableGridConfigComponent_Conditional_2_Template_ag_grid_angular_gridReady_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onGridReady($event));\n    })(\"firstDataRendered\", function ActionableGridConfigComponent_Conditional_2_Template_ag_grid_angular_firstDataRendered_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFirstDataRendered($event));\n    })(\"bodyScrollEnd\", function ActionableGridConfigComponent_Conditional_2_Template_ag_grid_angular_bodyScrollEnd_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onBodyScrollEnd($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, ctx_r2.displayScrollIndicator ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"headerHeight\", 55)(\"rowData\", ctx_r2.columnsConfig)(\"animateRows\", true)(\"rowDragManaged\", true)(\"suppressMoveWhenRowDragging\", true)(\"columnDefs\", ctx_r2.columnDefs)(\"defaultColDef\", ctx_r2.defaultColDef)(\"masterDetail\", true)(\"gridOptions\", ctx_r2.gridOptions)(\"isRowMaster\", ctx_r2.isRowMaster)(\"detailRowAutoHeight\", true);\n  }\n}\nexport class ActionableGridConfigComponent {\n  constructor(reportAdminService) {\n    this.reportAdminService = reportAdminService;\n    this.disabled = false;\n    this._columnsConfig = [];\n    this.columnsList = [];\n    this.projectVariables = [];\n    this.columnDefs = [{\n      field: 'column',\n      pinned: 'left',\n      suppressSizeToFit: true,\n      rowDrag: true,\n      suppressHeaderMenuButton: true,\n      cellRenderer: 'agGroupCellRenderer',\n      valueFormatter: params => params.data?.displayName || params.value\n    }, {\n      field: 'typeDescription',\n      suppressSizeToFit: true,\n      suppressHeaderMenuButton: true,\n      headerName: 'Type'\n    }, {\n      field: 'format',\n      suppressHeaderMenuButton: true,\n      headerName: 'Format',\n      cellRenderer: AgGridFormatSettingRendererComponent,\n      cellRendererParams: {\n        onChange: params => {\n          if (params.data?.format?.type === ActionableGridColumnType.Link) {\n            params.node.setDataValue('allowUserUpdate', false);\n            this.gridApi.refreshCells({\n              columns: ['allowUserUpdate'],\n              force: true\n            });\n          }\n          params.data.link = null;\n          params.data.values = null;\n        }\n      }\n    }, {\n      headerName: 'Calculated Field',\n      suppressHeaderMenuButton: true,\n      suppressSizeToFit: true,\n      cellRenderer: AgGridCheckMarkRendererComponent,\n      cellClass: 'justify-content-center',\n      valueGetter: params => {\n        return params.data.schemaProperty?.presentationProperties?.enableFormula;\n      }\n    }, {\n      field: 'groupByHeader',\n      suppressHeaderMenuButton: true,\n      suppressSizeToFit: true,\n      cellRenderer: AgGridCheckboxRendererComponent,\n      cellRendererParams: {\n        field: 'groupByHeader',\n        visible: params => params.data?.format?.baseType !== JsonDataTypes.Array\n      }\n    }, {\n      field: 'allowUserUpdate',\n      suppressHeaderMenuButton: true,\n      suppressSizeToFit: true,\n      cellRenderer: AgGridMattSlideToggleRendererComponent,\n      cellRendererParams: {\n        disabled: params => params.data?.schemaProperty?.typeProperties?.required || params.data?.format?.type === ActionableGridColumnType.Link,\n        onToggle: (params, checked) => {\n          if (!checked) params.node.setDataValue('required', false);\n          if (checked && params.context?.parentParams) params.context.parentParams.node.setDataValue('allowUserUpdate', true);\n        },\n        visible: params => !params.data?.schemaProperty?.presentationProperties?.enableFormula\n      }\n    }, {\n      field: 'required',\n      suppressHeaderMenuButton: true,\n      suppressSizeToFit: true,\n      cellRenderer: AgGridCheckboxRendererComponent,\n      cellRendererParams: {\n        field: 'required',\n        visible: params => params.data?.format?.baseType !== JsonDataTypes.Array && !params.data?.schemaProperty?.presentationProperties?.enableFormula\n      }\n    }, {\n      field: 'enableLookup',\n      suppressHeaderMenuButton: true,\n      suppressSizeToFit: true,\n      cellRenderer: AgGridCheckboxRendererComponent,\n      cellRendererParams: {\n        field: 'enableLookup',\n        visible: params => params.data?.schemaProperty?.presentationProperties?.enableLookup && !params.data?.schemaProperty?.presentationProperties?.enableFormula\n      }\n    }, {\n      field: 'dropdown',\n      suppressHeaderMenuButton: true,\n      suppressSizeToFit: true,\n      cellRenderer: AgGridCheckboxRendererComponent,\n      cellRendererParams: {\n        field: 'dropdown',\n        visible: params => params.data?.format?.baseType !== JsonDataTypes.Array\n      }\n    }, {\n      field: 'values',\n      suppressHeaderMenuButton: true,\n      width: 240,\n      minWidth: 240,\n      maxWidth: 240,\n      resizable: false,\n      cellRenderer: AggridActionableGridConfigDropDownRendererComponent,\n      cellRendererParams: {\n        getProjectVariables: () => this.projectVariables,\n        getColumns: () => this.columnsList\n      }\n    }, {\n      field: 'defaultValue',\n      suppressHeaderMenuButton: true,\n      width: 260,\n      minWidth: 260,\n      maxWidth: 260,\n      resizable: false,\n      cellRenderer: AgGridDefaultValueRendererComponent\n    }, {\n      field: 'slicerFilter',\n      suppressHeaderMenuButton: true,\n      suppressSizeToFit: true,\n      headerName: 'Slicer',\n      cellRenderer: AgGridCheckboxRendererComponent,\n      cellRendererParams: {\n        field: 'slicerFilter',\n        visible: params => params.data?.format?.baseType !== JsonDataTypes.Array,\n        disabled: params => params.data?.format?.baseType !== JsonDataTypes.String,\n        onToggle: (params, checked) => {\n          if (!checked) {\n            params.node.setDataValue('slicerFilter', false);\n          }\n        }\n      }\n    }];\n    // DefaultColDef sets all columns common properties\n    this.defaultColDef = {\n      resizable: true\n    };\n    this.isRowMaster = dataItem => {\n      return dataItem?.children?.length;\n    };\n    this.gridOptions = {\n      detailCellRendererParams: params => {\n        return this.getDetailCellRendererParams(params);\n      },\n      getContextMenuItems: null,\n      suppressContextMenu: true\n    };\n    this.displayScrollIndicator = true;\n  }\n  set columnsConfig(value) {\n    this._columnsConfig = value;\n    this.columnsList = value.map(v => ({\n      value: v.column,\n      label: v.displayName ?? v.column\n    }));\n  }\n  get columnsConfig() {\n    return this._columnsConfig;\n  }\n  ngOnChanges(changes) {\n    if (changes.columnsConfig && !changes.columnsConfig.firstChange) {\n      // Timeout to ensure detail grids are initialized\n      setTimeout(() => {\n        this.autoSizeAllDetailGrids(this.gridApi);\n      });\n    }\n  }\n  // returning detail cell renderer params for master details, Note: if isRowMaster is false, all these options will be ignored \n  getDetailCellRendererParams(params) {\n    return {\n      detailGridOptions: {\n        masterDetail: true,\n        // we can set other properties for the detail heigth like: detailRowHeight: 55 and domLayout: \"autoHeight\",\n        headerHeight: 55,\n        detailRowAutoHeight: true,\n        defaultColDef: this.defaultColDef,\n        animateRows: true,\n        rowDragManaged: true,\n        suppressMoveWhenRowDragging: true,\n        rowDragEnd: params => this.onRowDragEnd(params),\n        columnDefs: this.columnDefs,\n        isRowMaster: data => {\n          return !!data?.children && data?.children?.length;\n        },\n        context: {\n          parentParams: params\n        },\n        detailCellRendererParams: params => {\n          // Check if the current row has further children\n          if (params?.data?.children?.length) {\n            return this.getDetailCellRendererParams(params);\n          }\n          return null;\n        },\n        onFirstDataRendered: params => this.onFirstDataRendered(params)\n      },\n      getDetailRowData: params => {\n        params.successCallback(params?.data?.children);\n      }\n    };\n  }\n  ngOnInit() {\n    this.reportAdminService.getProjectVariables(this.projectVersionId).subscribe({\n      next: projectVariables => {\n        if (projectVariables) {\n          projectVariables.forEach(pv => {\n            this.projectVariables.push({\n              value: pv.variable,\n              label: pv.variable,\n              title: pv.description\n            });\n          });\n        }\n      }\n    });\n  }\n  onGridReady(params) {\n    this.gridApi = params.api;\n    this.gridApi.sizeColumnsToFit();\n  }\n  onFirstDataRendered(params) {\n    // Timeout to ensure detail grids are initialized\n    setTimeout(() => {\n      this.autoSizeAllDetailGrids(params.api);\n    });\n  }\n  autoSizeAllDetailGrids(gridApi) {\n    gridApi?.sizeColumnsToFit();\n    gridApi?.autoSizeAllColumns();\n    gridApi?.forEachDetailGridInfo(detailGridInfo => {\n      detailGridInfo.api?.sizeColumnsToFit();\n      gridApi?.autoSizeAllColumns();\n    });\n  }\n  onRowDragEnd(event) {\n    // event.node is moving data and event.overNode is over node\n    if (event.node !== event.overNode) {\n      // the list of rows we have is data, not row nodes, so extract the data\n      const fromIndex = this.columnsConfig.indexOf(event.node.data);\n      const toIndex = this.columnsConfig.indexOf(event.overNode.data);\n      moveInArray(this.columnsConfig, fromIndex, toIndex);\n      this.gridApi.setGridOption('rowData', this.columnsConfig);\n      this.gridApi.clearFocusedCell();\n    }\n    function moveInArray(array, from, to) {\n      const element = array[from];\n      array.splice(from, 1);\n      array.splice(to, 0, element);\n    }\n  }\n  onBodyScrollEnd(event) {\n    // comparing scroll bar width to scroll bar position to hide content indicator\n    if (event.direction === 'horizontal') {\n      const pinnedWidth = event.api.getColumnState()[0].width;\n      const scrollPosition = Math.ceil(event.api.getHorizontalPixelRange().right);\n      let width = -1 * pinnedWidth;\n      event.api.getColumnState().forEach(col => {\n        width += col.width;\n      });\n      this.displayScrollIndicator = scrollPosition <= width - 24;\n    }\n  }\n  onClickScrollIndicator() {\n    this.gridApi.ensureColumnVisible('slicerFilter', 'end');\n  }\n  static {\n    this.ɵfac = function ActionableGridConfigComponent_Factory(t) {\n      return new (t || ActionableGridConfigComponent)(i0.ɵɵdirectiveInject(i1.ReportAdminService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActionableGridConfigComponent,\n      selectors: [[\"app-actionable-grid-config\"]],\n      inputs: {\n        columnsConfig: \"columnsConfig\",\n        projectVersionId: \"projectVersionId\",\n        disabled: \"disabled\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 1,\n      consts: [[1, \"actionable-grid-config-setting\", \"relative\"], [\"severity\", \"light\", \"styleClass\", \"w-full max-w-full\", \"text\", \"Project is Locked\"], [1, \"overlay-button\"], [\"id\", \"ag-Configuration\", \"domLayout\", \"autoHeight\", \"rowHeight\", \"55\", 1, \"ag-theme-material\", \"format-columns-data\", \"border-none\", 3, \"rowDragEnd\", \"gridReady\", \"firstDataRendered\", \"bodyScrollEnd\", \"headerHeight\", \"rowData\", \"animateRows\", \"rowDragManaged\", \"suppressMoveWhenRowDragging\", \"columnDefs\", \"defaultColDef\", \"masterDetail\", \"gridOptions\", \"isRowMaster\", \"detailRowAutoHeight\"], [1, \"flex\"], [\"severity\", \"primary\", \"icon\", \"pi pi-angle-right\", \"styleClass\", \"h-3rem shadow-6\", 1, \"text-light\", 3, \"click\", \"rounded\", \"outlined\"]],\n      template: function ActionableGridConfigComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ActionableGridConfigComponent_Conditional_1_Template, 1, 0, \"p-message\", 1)(2, ActionableGridConfigComponent_Conditional_2_Template, 2, 12);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.disabled ? 1 : 2);\n        }\n      },\n      dependencies: [AgGridModule, i2.AgGridAngular, MessageModule, i3.UIMessage, ButtonModule, i4.Button],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AggridActionableGridConfigDropDownRendererComponent", "AgGridFormatSettingRendererComponent", "AgGridMattSlideToggleRendererComponent", "AgGridCheckboxRendererComponent", "AgGridDefaultValueRendererComponent", "ActionableGridColumnType", "JsonDataTypes", "AgGridCheckMarkRendererComponent", "AgGridModule", "MessageModule", "ButtonModule", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵlistener", "ActionableGridConfigComponent_Conditional_2_Conditional_0_Template_p_button_click_2_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onClickScrollIndicator", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵtemplate", "ActionableGridConfigComponent_Conditional_2_Conditional_0_Template", "ActionableGridConfigComponent_Conditional_2_Template_ag_grid_angular_rowDragEnd_1_listener", "$event", "_r1", "onRowDragEnd", "ActionableGridConfigComponent_Conditional_2_Template_ag_grid_angular_gridReady_1_listener", "onGridReady", "ActionableGridConfigComponent_Conditional_2_Template_ag_grid_angular_firstDataRendered_1_listener", "onFirstDataR<PERSON>ed", "ActionableGridConfigComponent_Conditional_2_Template_ag_grid_angular_bodyScrollEnd_1_listener", "onBodyScrollEnd", "ɵɵconditional", "displayScrollIndicator", "columnsConfig", "columnDefs", "defaultColDef", "gridOptions", "isRowMaster", "ActionableGridConfigComponent", "constructor", "reportAdminService", "disabled", "_columnsConfig", "columnsList", "projectVariables", "field", "pinned", "suppressSizeToFit", "rowDrag", "suppressHeaderMenuButton", "cell<PERSON><PERSON><PERSON>", "valueFormatter", "params", "data", "displayName", "value", "headerName", "cellRendererParams", "onChange", "format", "type", "Link", "node", "setDataValue", "gridApi", "refresh<PERSON>ells", "columns", "force", "link", "values", "cellClass", "valueGetter", "schemaProperty", "presentationProperties", "enableFormula", "visible", "baseType", "Array", "typeProperties", "required", "onToggle", "checked", "context", "parentParams", "enableLookup", "width", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "resizable", "getProjectVariables", "getColumns", "String", "dataItem", "children", "length", "detailCellRendererParams", "getDetailCellRendererParams", "getContextMenuItems", "suppressContextMenu", "map", "v", "column", "label", "ngOnChanges", "changes", "firstChange", "setTimeout", "autoSizeAllDetailGrids", "detailGridOptions", "masterDetail", "headerHeight", "detailRowAutoHeight", "animateRows", "rowDragManaged", "suppressMoveWhenRowDragging", "rowDragEnd", "getDetailRowData", "success<PERSON>allback", "ngOnInit", "projectVersionId", "subscribe", "next", "for<PERSON>ach", "pv", "push", "variable", "title", "description", "api", "sizeColumnsToFit", "autoSizeAllColumns", "forEachDetailGridInfo", "detailGridInfo", "event", "overNode", "fromIndex", "indexOf", "toIndex", "moveInArray", "setGridOption", "clearFocusedCell", "array", "from", "to", "element", "splice", "direction", "<PERSON><PERSON><PERSON><PERSON>", "getColumnState", "scrollPosition", "Math", "ceil", "getHorizontalPixelRange", "right", "col", "ensureColumnVisible", "ɵɵdirectiveInject", "i1", "ReportAdminService", "selectors", "inputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ActionableGridConfigComponent_Template", "rf", "ctx", "ActionableGridConfigComponent_Conditional_1_Template", "ActionableGridConfigComponent_Conditional_2_Template", "i2", "AgGridAngular", "i3", "UIMessage", "i4", "<PERSON><PERSON>", "styles"], "sources": ["C:\\Projects\\SaltboxReporting\\Reporting-Web-App\\src\\app\\report-setup\\actionable-grid-config\\actionable-grid-config.component.ts", "C:\\Projects\\SaltboxReporting\\Reporting-Web-App\\src\\app\\report-setup\\actionable-grid-config\\actionable-grid-config.component.html"], "sourcesContent": ["import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';\r\nimport { ColDef, FirstDataRenderedEvent, GridApi, GridReadyEvent, RowDragEndEvent, GridOptions, BodyScrollEvent } from 'ag-grid-community';\r\nimport { AggridActionableGridConfigDropDownRendererComponent } from 'src/app/core/ag-grid/renderers/ag-grid-config-dropdown.renderer.component';\r\nimport { AgGridFormatSettingRendererComponent } from 'src/app/core/ag-grid/renderers/ag-grid-format-settings.renderer.component';\r\nimport { AgGridMattSlideToggleRendererComponent } from 'src/app/core/ag-grid/renderers/ag-grid-matt-slide-toggle.renderer.component';\r\nimport { ActionableGridColumnConfig } from 'src/app/core/models/actionable-grid-column-config';\r\nimport { AgGridCheckboxRendererComponent } from '../../core/ag-grid/renderers/ag-grid-checkbox.renderer.component';\r\nimport { AgGridDefaultValueRendererComponent } from 'src/app/core/ag-grid/renderers/ag-grid-default-value.renderer.component';\r\nimport { ActionableGridColumnType } from 'src/app/core/enums/actionable-grid-enums';\r\nimport { UrlSubstitutionName } from 'src/app/core/models/url-substitution-name';\r\nimport { ReportAdminService } from 'src/app/core/services/report-admin.service';\r\nimport { IsRowMaster } from '@ag-grid-community/core';\r\nimport { JsonDataTypes } from 'src/app/shared/enums/json-schema.enum';\r\nimport { AgGridCheckMarkRendererComponent } from 'src/app/core/ag-grid/renderers/ag-grid-check-mark.renderer.component';\r\nimport { AgGridModule } from 'ag-grid-angular';\r\nimport { NgIf } from '@angular/common';\r\nimport { MessageModule } from 'primeng/message';\r\nimport { ButtonModule } from 'primeng/button';\r\n\r\n@Component({\r\n  selector: 'app-actionable-grid-config',\r\n  templateUrl: './actionable-grid-config.component.html',\r\n  styleUrls: ['./actionable-grid-config.component.scss'],\r\n  standalone: true,\r\n  imports: [AgGridModule, NgIf, MessageModule, ButtonModule]\r\n})\r\nexport class ActionableGridConfigComponent implements OnChanges, OnInit {\r\n  constructor(private reportAdminService: ReportAdminService) { }\r\n\r\n  @Input() set columnsConfig(value: ActionableGridColumnConfig[]) {\r\n    this._columnsConfig = value;\r\n    this.columnsList = value.map(v => ({ value: v.column, label: v.displayName ?? v.column }));\r\n  }\r\n  get columnsConfig() {\r\n    return this._columnsConfig;\r\n  }\r\n  @Input() projectVersionId: string;\r\n  @Input() disabled = false;\r\n\r\n  private gridApi!: GridApi;\r\n  private _columnsConfig: ActionableGridColumnConfig[] = [];\r\n\r\n  columnsList: UrlSubstitutionName[] = [];\r\n  projectVariables: UrlSubstitutionName[] = [];\r\n\r\n  columnDefs: ColDef[] = [\r\n    {\r\n      field: 'column', pinned: 'left', suppressSizeToFit: true, rowDrag: true, suppressHeaderMenuButton: true, cellRenderer: 'agGroupCellRenderer',\r\n      valueFormatter: (params) => params.data?.displayName || params.value\r\n    },\r\n    {\r\n      field: 'typeDescription', suppressSizeToFit: true, suppressHeaderMenuButton: true, headerName: 'Type'\r\n    },\r\n    {\r\n      field: 'format', suppressHeaderMenuButton: true, headerName: 'Format', cellRenderer: AgGridFormatSettingRendererComponent,\r\n      cellRendererParams: {\r\n        onChange: params => {\r\n          if (params.data?.format?.type === ActionableGridColumnType.Link) {\r\n            params.node.setDataValue('allowUserUpdate', false);\r\n            this.gridApi.refreshCells({ columns: ['allowUserUpdate'], force: true });\r\n          }\r\n          params.data.link = null;\r\n          params.data.values = null;\r\n        }\r\n      }\r\n    },\r\n    {\r\n      headerName: 'Calculated Field', suppressHeaderMenuButton: true, suppressSizeToFit: true, cellRenderer: AgGridCheckMarkRendererComponent,\r\n      cellClass: 'justify-content-center',\r\n      valueGetter: (params) => { return params.data.schemaProperty?.presentationProperties?.enableFormula; }\r\n    },\r\n    {\r\n      field: 'groupByHeader', suppressHeaderMenuButton: true, suppressSizeToFit: true, cellRenderer: AgGridCheckboxRendererComponent,\r\n      cellRendererParams: {\r\n        field: 'groupByHeader',\r\n        visible: params => params.data?.format?.baseType !== JsonDataTypes.Array\r\n      }\r\n    },\r\n    {\r\n      field: 'allowUserUpdate', suppressHeaderMenuButton: true, suppressSizeToFit: true, cellRenderer: AgGridMattSlideToggleRendererComponent,\r\n      cellRendererParams: {\r\n        disabled: params => params.data?.schemaProperty?.typeProperties?.required || params.data?.format?.type === ActionableGridColumnType.Link,\r\n        onToggle: (params, checked) => {\r\n          if (!checked)\r\n            params.node.setDataValue('required', false);\r\n\r\n          if (checked && params.context?.parentParams)\r\n            params.context.parentParams.node.setDataValue('allowUserUpdate', true);\r\n        },\r\n        visible: params => !params.data?.schemaProperty?.presentationProperties?.enableFormula\r\n      }\r\n    },\r\n    {\r\n      field: 'required', suppressHeaderMenuButton: true, suppressSizeToFit: true, cellRenderer: AgGridCheckboxRendererComponent,\r\n      cellRendererParams: {\r\n        field: 'required',\r\n        visible: params => params.data?.format?.baseType !== JsonDataTypes.Array && !params.data?.schemaProperty?.presentationProperties?.enableFormula\r\n      }\r\n    },\r\n    {\r\n      field: 'enableLookup', suppressHeaderMenuButton: true, suppressSizeToFit: true, cellRenderer: AgGridCheckboxRendererComponent,\r\n      cellRendererParams: {\r\n        field: 'enableLookup',\r\n        visible: params => params.data?.schemaProperty?.presentationProperties?.enableLookup && !params.data?.schemaProperty?.presentationProperties?.enableFormula,\r\n      }\r\n    },\r\n    {\r\n      field: 'dropdown', suppressHeaderMenuButton: true, suppressSizeToFit: true, cellRenderer: AgGridCheckboxRendererComponent,\r\n      cellRendererParams: {\r\n        field: 'dropdown',\r\n        visible: params => params.data?.format?.baseType !== JsonDataTypes.Array\r\n      }\r\n    },\r\n    {\r\n      field: 'values', suppressHeaderMenuButton: true, width: 240, minWidth: 240, maxWidth: 240, resizable: false,\r\n      cellRenderer: AggridActionableGridConfigDropDownRendererComponent,\r\n      cellRendererParams: { getProjectVariables: () => this.projectVariables, getColumns: () => this.columnsList }\r\n    },\r\n    {\r\n      field: 'defaultValue', suppressHeaderMenuButton: true, width: 260, minWidth: 260, maxWidth: 260, resizable: false,\r\n      cellRenderer: AgGridDefaultValueRendererComponent,\r\n    },\r\n    {\r\n      field: 'slicerFilter', suppressHeaderMenuButton: true, suppressSizeToFit: true,\r\n      headerName: 'Slicer', cellRenderer: AgGridCheckboxRendererComponent,\r\n      cellRendererParams: {\r\n        field: 'slicerFilter',\r\n        visible: params => params.data?.format?.baseType !== JsonDataTypes.Array,\r\n        disabled: params => params.data?.format?.baseType !== JsonDataTypes.String,\r\n        onToggle: (params, checked) => { if (!checked) { params.node.setDataValue('slicerFilter', false); } }\r\n      }\r\n    }\r\n  ];\r\n\r\n  // DefaultColDef sets all columns common properties\r\n  defaultColDef: ColDef = {\r\n    resizable: true\r\n  };\r\n\r\n  isRowMaster: IsRowMaster = (dataItem: any) => {\r\n    return dataItem?.children?.length;\r\n  };\r\n\r\n  gridOptions: GridOptions = {\r\n    detailCellRendererParams: (params) => {\r\n      return this.getDetailCellRendererParams(params);\r\n    },\r\n    getContextMenuItems: null,\r\n    suppressContextMenu: true\r\n  }\r\n\r\n  displayScrollIndicator = true;\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes.columnsConfig && !changes.columnsConfig.firstChange) {\r\n      // Timeout to ensure detail grids are initialized\r\n      setTimeout(() => {\r\n        this.autoSizeAllDetailGrids(this.gridApi);\r\n      });\r\n    }\r\n  }\r\n\r\n  // returning detail cell renderer params for master details, Note: if isRowMaster is false, all these options will be ignored \r\n  getDetailCellRendererParams(params) {\r\n    return {\r\n      detailGridOptions: {\r\n        masterDetail: true,\r\n        // we can set other properties for the detail heigth like: detailRowHeight: 55 and domLayout: \"autoHeight\",\r\n        headerHeight: 55,\r\n        detailRowAutoHeight: true,\r\n        defaultColDef: this.defaultColDef,\r\n        animateRows: true,\r\n        rowDragManaged: true,\r\n        suppressMoveWhenRowDragging: true,\r\n        rowDragEnd: (params) => this.onRowDragEnd(params),\r\n        columnDefs: this.columnDefs,\r\n        isRowMaster: data => {\r\n          return !!data?.children && data?.children?.length;\r\n        },\r\n        context: {\r\n          parentParams: params\r\n        },\r\n        detailCellRendererParams: (params) => {\r\n          // Check if the current row has further children\r\n          if (params?.data?.children?.length) {\r\n            return this.getDetailCellRendererParams(params);\r\n          }\r\n\r\n          return null;\r\n        },\r\n        onFirstDataRendered: params => this.onFirstDataRendered(params)\r\n      },\r\n      getDetailRowData: (params) => {\r\n        params.successCallback(params?.data?.children);\r\n      },\r\n    };\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.reportAdminService.getProjectVariables(this.projectVersionId).subscribe({\r\n      next: (projectVariables) => {\r\n        if (projectVariables) {\r\n          projectVariables.forEach(pv => {\r\n            this.projectVariables.push({ value: pv.variable, label: pv.variable, title: pv.description });\r\n          });\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  onGridReady(params: GridReadyEvent) {\r\n    this.gridApi = params.api;\r\n    this.gridApi.sizeColumnsToFit();\r\n  }\r\n\r\n  onFirstDataRendered(params: FirstDataRenderedEvent) {\r\n    // Timeout to ensure detail grids are initialized\r\n    setTimeout(() => {\r\n      this.autoSizeAllDetailGrids(params.api);\r\n    });\r\n  }\r\n\r\n  autoSizeAllDetailGrids(gridApi: GridApi) {\r\n    gridApi?.sizeColumnsToFit();\r\n    gridApi?.autoSizeAllColumns();\r\n\r\n    gridApi?.forEachDetailGridInfo((detailGridInfo) => {\r\n      detailGridInfo.api?.sizeColumnsToFit();\r\n      gridApi?.autoSizeAllColumns();\r\n    });\r\n  }\r\n\r\n  onRowDragEnd(event: RowDragEndEvent) {\r\n\r\n    // event.node is moving data and event.overNode is over node\r\n    if (event.node !== event.overNode) {\r\n      // the list of rows we have is data, not row nodes, so extract the data\r\n      const fromIndex = this.columnsConfig.indexOf(event.node.data);\r\n      const toIndex = this.columnsConfig.indexOf(event.overNode.data);\r\n\r\n      moveInArray(this.columnsConfig, fromIndex, toIndex);\r\n      this.gridApi.setGridOption('rowData', this.columnsConfig);\r\n      this.gridApi.clearFocusedCell();\r\n    }\r\n\r\n    function moveInArray(array: any[], from: number, to: number) {\r\n      const element = array[from];\r\n      array.splice(from, 1);\r\n      array.splice(to, 0, element);\r\n    }\r\n  }\r\n\r\n  onBodyScrollEnd(event: BodyScrollEvent) {\r\n    // comparing scroll bar width to scroll bar position to hide content indicator\r\n    if (event.direction === 'horizontal') {\r\n      const pinnedWidth = event.api.getColumnState()[0].width;\r\n      const scrollPosition = Math.ceil(event.api.getHorizontalPixelRange().right);\r\n      let width = -1 * pinnedWidth;\r\n\r\n      event.api.getColumnState().forEach(col => {\r\n        width += col.width;\r\n      })\r\n\r\n      this.displayScrollIndicator = scrollPosition <= width - 24;\r\n    }\r\n  }\r\n\r\n  onClickScrollIndicator() {\r\n    this.gridApi.ensureColumnVisible('slicerFilter', 'end');\r\n  }\r\n}\r\n", "<div class=\"actionable-grid-config-setting relative\">\r\n    @if (disabled) {\r\n    <p-message severity=\"light\" styleClass=\"w-full max-w-full\" text=\"Project is Locked\"></p-message>\r\n    } @else {\r\n        @if (displayScrollIndicator) {\r\n            <div class=\"overlay-button\">\r\n                <span class=\"flex\">\r\n                    <p-button class=\"text-light\" [rounded]=\"false\" [outlined]=\"true\" severity=\"primary\"\r\n                        icon=\"pi pi-angle-right\" styleClass=\"h-3rem shadow-6\" (click)=\"onClickScrollIndicator()\"></p-button>\r\n                </span>\r\n            </div>\r\n        }\r\n    <ag-grid-angular id=\"ag-Configuration\" domLayout=\"autoHeight\" class=\"ag-theme-material format-columns-data border-none\"\r\n        rowHeight=\"55\" [headerHeight]=\"55\" [rowData]=\"columnsConfig\" [animateRows]=\"true\" [rowDragManaged]=\"true\"\r\n        [suppressMoveWhenRowDragging]=\"true\" (rowDragEnd)=\"onRowDragEnd($event)\" (gridReady)=\"onGridReady($event)\"\r\n        (firstDataRendered)=\"onFirstDataRendered($event)\" [columnDefs]=\"columnDefs\" [defaultColDef]=\"defaultColDef\"\r\n        [masterDetail]=\"true\" [gridOptions]=\"gridOptions\" [isRowMaster]=\"isRowMaster\" [detailRowAutoHeight]=\"true\"\r\n        (bodyScrollEnd)=\"onBodyScrollEnd($event)\">\r\n    </ag-grid-angular>\r\n    }\r\n</div>"], "mappings": "AAEA,SAASA,mDAAmD,QAAQ,2EAA2E;AAC/I,SAASC,oCAAoC,QAAQ,2EAA2E;AAChI,SAASC,sCAAsC,QAAQ,6EAA6E;AAEpI,SAASC,+BAA+B,QAAQ,kEAAkE;AAClH,SAASC,mCAAmC,QAAQ,yEAAyE;AAC7H,SAASC,wBAAwB,QAAQ,0CAA0C;AAInF,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,gCAAgC,QAAQ,sEAAsE;AACvH,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;;;;;;;;ICfzCC,EAAA,CAAAC,SAAA,mBAAgG;;;;;;IAKhFD,EAFR,CAAAE,cAAA,aAA4B,cACL,kBAE8E;IAAnCF,EAAA,CAAAG,UAAA,mBAAAC,6FAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAG,sBAAA,EAAwB;IAAA,EAAC;IAEpGV,EAFqG,CAAAW,YAAA,EAAW,EACrG,EACL;;;IAH+BX,EAAA,CAAAY,SAAA,GAAiB;IAACZ,EAAlB,CAAAa,UAAA,kBAAiB,kBAAkB;;;;;;IAH5Eb,EAAA,CAAAc,UAAA,IAAAC,kEAAA,iBAA8B;IAQlCf,EAAA,CAAAE,cAAA,yBAK8C;IAA1CF,EAHqC,CAAAG,UAAA,wBAAAa,2FAAAC,MAAA;MAAAjB,EAAA,CAAAK,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAcF,MAAA,CAAAY,YAAA,CAAAF,MAAA,CAAoB;IAAA,EAAC,uBAAAG,0FAAAH,MAAA;MAAAjB,EAAA,CAAAK,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAcF,MAAA,CAAAc,WAAA,CAAAJ,MAAA,CAAmB;IAAA,EAAC,+BAAAK,kGAAAL,MAAA;MAAAjB,EAAA,CAAAK,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CACrFF,MAAA,CAAAgB,mBAAA,CAAAN,MAAA,CAA2B;IAAA,EAAC,2BAAAO,8FAAAP,MAAA;MAAAjB,EAAA,CAAAK,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAEhCF,MAAA,CAAAkB,eAAA,CAAAR,MAAA,CAAuB;IAAA,EAAC;IAC7CjB,EAAA,CAAAW,YAAA,EAAkB;;;;IAddX,EAAA,CAAA0B,aAAA,IAAAnB,MAAA,CAAAoB,sBAAA,UAOC;IAEc3B,EAAA,CAAAY,SAAA,EAAmB;IAG4CZ,EAH/D,CAAAa,UAAA,oBAAmB,YAAAN,MAAA,CAAAqB,aAAA,CAA0B,qBAAqB,wBAAwB,qCACrE,eAAArB,MAAA,CAAAsB,UAAA,CACuC,kBAAAtB,MAAA,CAAAuB,aAAA,CAAgC,sBACtF,gBAAAvB,MAAA,CAAAwB,WAAA,CAA4B,gBAAAxB,MAAA,CAAAyB,WAAA,CAA4B,6BAA6B;;;ADUlH,OAAM,MAAOC,6BAA6B;EACxCC,YAAoBC,kBAAsC;IAAtC,KAAAA,kBAAkB,GAAlBA,kBAAkB;IAU7B,KAAAC,QAAQ,GAAG,KAAK;IAGjB,KAAAC,cAAc,GAAiC,EAAE;IAEzD,KAAAC,WAAW,GAA0B,EAAE;IACvC,KAAAC,gBAAgB,GAA0B,EAAE;IAE5C,KAAAV,UAAU,GAAa,CACrB;MACEW,KAAK,EAAE,QAAQ;MAAEC,MAAM,EAAE,MAAM;MAAEC,iBAAiB,EAAE,IAAI;MAAEC,OAAO,EAAE,IAAI;MAAEC,wBAAwB,EAAE,IAAI;MAAEC,YAAY,EAAE,qBAAqB;MAC5IC,cAAc,EAAGC,MAAM,IAAKA,MAAM,CAACC,IAAI,EAAEC,WAAW,IAAIF,MAAM,CAACG;KAChE,EACD;MACEV,KAAK,EAAE,iBAAiB;MAAEE,iBAAiB,EAAE,IAAI;MAAEE,wBAAwB,EAAE,IAAI;MAAEO,UAAU,EAAE;KAChG,EACD;MACEX,KAAK,EAAE,QAAQ;MAAEI,wBAAwB,EAAE,IAAI;MAAEO,UAAU,EAAE,QAAQ;MAAEN,YAAY,EAAEvD,oCAAoC;MACzH8D,kBAAkB,EAAE;QAClBC,QAAQ,EAAEN,MAAM,IAAG;UACjB,IAAIA,MAAM,CAACC,IAAI,EAAEM,MAAM,EAAEC,IAAI,KAAK7D,wBAAwB,CAAC8D,IAAI,EAAE;YAC/DT,MAAM,CAACU,IAAI,CAACC,YAAY,CAAC,iBAAiB,EAAE,KAAK,CAAC;YAClD,IAAI,CAACC,OAAO,CAACC,YAAY,CAAC;cAAEC,OAAO,EAAE,CAAC,iBAAiB,CAAC;cAAEC,KAAK,EAAE;YAAI,CAAE,CAAC;UAC1E;UACAf,MAAM,CAACC,IAAI,CAACe,IAAI,GAAG,IAAI;UACvBhB,MAAM,CAACC,IAAI,CAACgB,MAAM,GAAG,IAAI;QAC3B;;KAEH,EACD;MACEb,UAAU,EAAE,kBAAkB;MAAEP,wBAAwB,EAAE,IAAI;MAAEF,iBAAiB,EAAE,IAAI;MAAEG,YAAY,EAAEjD,gCAAgC;MACvIqE,SAAS,EAAE,wBAAwB;MACnCC,WAAW,EAAGnB,MAAM,IAAI;QAAG,OAAOA,MAAM,CAACC,IAAI,CAACmB,cAAc,EAAEC,sBAAsB,EAAEC,aAAa;MAAE;KACtG,EACD;MACE7B,KAAK,EAAE,eAAe;MAAEI,wBAAwB,EAAE,IAAI;MAAEF,iBAAiB,EAAE,IAAI;MAAEG,YAAY,EAAErD,+BAA+B;MAC9H4D,kBAAkB,EAAE;QAClBZ,KAAK,EAAE,eAAe;QACtB8B,OAAO,EAAEvB,MAAM,IAAIA,MAAM,CAACC,IAAI,EAAEM,MAAM,EAAEiB,QAAQ,KAAK5E,aAAa,CAAC6E;;KAEtE,EACD;MACEhC,KAAK,EAAE,iBAAiB;MAAEI,wBAAwB,EAAE,IAAI;MAAEF,iBAAiB,EAAE,IAAI;MAAEG,YAAY,EAAEtD,sCAAsC;MACvI6D,kBAAkB,EAAE;QAClBhB,QAAQ,EAAEW,MAAM,IAAIA,MAAM,CAACC,IAAI,EAAEmB,cAAc,EAAEM,cAAc,EAAEC,QAAQ,IAAI3B,MAAM,CAACC,IAAI,EAAEM,MAAM,EAAEC,IAAI,KAAK7D,wBAAwB,CAAC8D,IAAI;QACxImB,QAAQ,EAAEA,CAAC5B,MAAM,EAAE6B,OAAO,KAAI;UAC5B,IAAI,CAACA,OAAO,EACV7B,MAAM,CAACU,IAAI,CAACC,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC;UAE7C,IAAIkB,OAAO,IAAI7B,MAAM,CAAC8B,OAAO,EAAEC,YAAY,EACzC/B,MAAM,CAAC8B,OAAO,CAACC,YAAY,CAACrB,IAAI,CAACC,YAAY,CAAC,iBAAiB,EAAE,IAAI,CAAC;QAC1E,CAAC;QACDY,OAAO,EAAEvB,MAAM,IAAI,CAACA,MAAM,CAACC,IAAI,EAAEmB,cAAc,EAAEC,sBAAsB,EAAEC;;KAE5E,EACD;MACE7B,KAAK,EAAE,UAAU;MAAEI,wBAAwB,EAAE,IAAI;MAAEF,iBAAiB,EAAE,IAAI;MAAEG,YAAY,EAAErD,+BAA+B;MACzH4D,kBAAkB,EAAE;QAClBZ,KAAK,EAAE,UAAU;QACjB8B,OAAO,EAAEvB,MAAM,IAAIA,MAAM,CAACC,IAAI,EAAEM,MAAM,EAAEiB,QAAQ,KAAK5E,aAAa,CAAC6E,KAAK,IAAI,CAACzB,MAAM,CAACC,IAAI,EAAEmB,cAAc,EAAEC,sBAAsB,EAAEC;;KAErI,EACD;MACE7B,KAAK,EAAE,cAAc;MAAEI,wBAAwB,EAAE,IAAI;MAAEF,iBAAiB,EAAE,IAAI;MAAEG,YAAY,EAAErD,+BAA+B;MAC7H4D,kBAAkB,EAAE;QAClBZ,KAAK,EAAE,cAAc;QACrB8B,OAAO,EAAEvB,MAAM,IAAIA,MAAM,CAACC,IAAI,EAAEmB,cAAc,EAAEC,sBAAsB,EAAEW,YAAY,IAAI,CAAChC,MAAM,CAACC,IAAI,EAAEmB,cAAc,EAAEC,sBAAsB,EAAEC;;KAEjJ,EACD;MACE7B,KAAK,EAAE,UAAU;MAAEI,wBAAwB,EAAE,IAAI;MAAEF,iBAAiB,EAAE,IAAI;MAAEG,YAAY,EAAErD,+BAA+B;MACzH4D,kBAAkB,EAAE;QAClBZ,KAAK,EAAE,UAAU;QACjB8B,OAAO,EAAEvB,MAAM,IAAIA,MAAM,CAACC,IAAI,EAAEM,MAAM,EAAEiB,QAAQ,KAAK5E,aAAa,CAAC6E;;KAEtE,EACD;MACEhC,KAAK,EAAE,QAAQ;MAAEI,wBAAwB,EAAE,IAAI;MAAEoC,KAAK,EAAE,GAAG;MAAEC,QAAQ,EAAE,GAAG;MAAEC,QAAQ,EAAE,GAAG;MAAEC,SAAS,EAAE,KAAK;MAC3GtC,YAAY,EAAExD,mDAAmD;MACjE+D,kBAAkB,EAAE;QAAEgC,mBAAmB,EAAEA,CAAA,KAAM,IAAI,CAAC7C,gBAAgB;QAAE8C,UAAU,EAAEA,CAAA,KAAM,IAAI,CAAC/C;MAAW;KAC3G,EACD;MACEE,KAAK,EAAE,cAAc;MAAEI,wBAAwB,EAAE,IAAI;MAAEoC,KAAK,EAAE,GAAG;MAAEC,QAAQ,EAAE,GAAG;MAAEC,QAAQ,EAAE,GAAG;MAAEC,SAAS,EAAE,KAAK;MACjHtC,YAAY,EAAEpD;KACf,EACD;MACE+C,KAAK,EAAE,cAAc;MAAEI,wBAAwB,EAAE,IAAI;MAAEF,iBAAiB,EAAE,IAAI;MAC9ES,UAAU,EAAE,QAAQ;MAAEN,YAAY,EAAErD,+BAA+B;MACnE4D,kBAAkB,EAAE;QAClBZ,KAAK,EAAE,cAAc;QACrB8B,OAAO,EAAEvB,MAAM,IAAIA,MAAM,CAACC,IAAI,EAAEM,MAAM,EAAEiB,QAAQ,KAAK5E,aAAa,CAAC6E,KAAK;QACxEpC,QAAQ,EAAEW,MAAM,IAAIA,MAAM,CAACC,IAAI,EAAEM,MAAM,EAAEiB,QAAQ,KAAK5E,aAAa,CAAC2F,MAAM;QAC1EX,QAAQ,EAAEA,CAAC5B,MAAM,EAAE6B,OAAO,KAAI;UAAG,IAAI,CAACA,OAAO,EAAE;YAAE7B,MAAM,CAACU,IAAI,CAACC,YAAY,CAAC,cAAc,EAAE,KAAK,CAAC;UAAE;QAAE;;KAEvG,CACF;IAED;IACA,KAAA5B,aAAa,GAAW;MACtBqD,SAAS,EAAE;KACZ;IAED,KAAAnD,WAAW,GAAiBuD,QAAa,IAAI;MAC3C,OAAOA,QAAQ,EAAEC,QAAQ,EAAEC,MAAM;IACnC,CAAC;IAED,KAAA1D,WAAW,GAAgB;MACzB2D,wBAAwB,EAAG3C,MAAM,IAAI;QACnC,OAAO,IAAI,CAAC4C,2BAA2B,CAAC5C,MAAM,CAAC;MACjD,CAAC;MACD6C,mBAAmB,EAAE,IAAI;MACzBC,mBAAmB,EAAE;KACtB;IAED,KAAAlE,sBAAsB,GAAG,IAAI;EA5HiC;EAE9D,IAAaC,aAAaA,CAACsB,KAAmC;IAC5D,IAAI,CAACb,cAAc,GAAGa,KAAK;IAC3B,IAAI,CAACZ,WAAW,GAAGY,KAAK,CAAC4C,GAAG,CAACC,CAAC,KAAK;MAAE7C,KAAK,EAAE6C,CAAC,CAACC,MAAM;MAAEC,KAAK,EAAEF,CAAC,CAAC9C,WAAW,IAAI8C,CAAC,CAACC;IAAM,CAAE,CAAC,CAAC;EAC5F;EACA,IAAIpE,aAAaA,CAAA;IACf,OAAO,IAAI,CAACS,cAAc;EAC5B;EAsHA6D,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAACvE,aAAa,IAAI,CAACuE,OAAO,CAACvE,aAAa,CAACwE,WAAW,EAAE;MAC/D;MACAC,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,sBAAsB,CAAC,IAAI,CAAC3C,OAAO,CAAC;MAC3C,CAAC,CAAC;IACJ;EACF;EAEA;EACAgC,2BAA2BA,CAAC5C,MAAM;IAChC,OAAO;MACLwD,iBAAiB,EAAE;QACjBC,YAAY,EAAE,IAAI;QAClB;QACAC,YAAY,EAAE,EAAE;QAChBC,mBAAmB,EAAE,IAAI;QACzB5E,aAAa,EAAE,IAAI,CAACA,aAAa;QACjC6E,WAAW,EAAE,IAAI;QACjBC,cAAc,EAAE,IAAI;QACpBC,2BAA2B,EAAE,IAAI;QACjCC,UAAU,EAAG/D,MAAM,IAAK,IAAI,CAAC5B,YAAY,CAAC4B,MAAM,CAAC;QACjDlB,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BG,WAAW,EAAEgB,IAAI,IAAG;UAClB,OAAO,CAAC,CAACA,IAAI,EAAEwC,QAAQ,IAAIxC,IAAI,EAAEwC,QAAQ,EAAEC,MAAM;QACnD,CAAC;QACDZ,OAAO,EAAE;UACPC,YAAY,EAAE/B;SACf;QACD2C,wBAAwB,EAAG3C,MAAM,IAAI;UACnC;UACA,IAAIA,MAAM,EAAEC,IAAI,EAAEwC,QAAQ,EAAEC,MAAM,EAAE;YAClC,OAAO,IAAI,CAACE,2BAA2B,CAAC5C,MAAM,CAAC;UACjD;UAEA,OAAO,IAAI;QACb,CAAC;QACDxB,mBAAmB,EAAEwB,MAAM,IAAI,IAAI,CAACxB,mBAAmB,CAACwB,MAAM;OAC/D;MACDgE,gBAAgB,EAAGhE,MAAM,IAAI;QAC3BA,MAAM,CAACiE,eAAe,CAACjE,MAAM,EAAEC,IAAI,EAAEwC,QAAQ,CAAC;MAChD;KACD;EACH;EAEAyB,QAAQA,CAAA;IACN,IAAI,CAAC9E,kBAAkB,CAACiD,mBAAmB,CAAC,IAAI,CAAC8B,gBAAgB,CAAC,CAACC,SAAS,CAAC;MAC3EC,IAAI,EAAG7E,gBAAgB,IAAI;QACzB,IAAIA,gBAAgB,EAAE;UACpBA,gBAAgB,CAAC8E,OAAO,CAACC,EAAE,IAAG;YAC5B,IAAI,CAAC/E,gBAAgB,CAACgF,IAAI,CAAC;cAAErE,KAAK,EAAEoE,EAAE,CAACE,QAAQ;cAAEvB,KAAK,EAAEqB,EAAE,CAACE,QAAQ;cAAEC,KAAK,EAAEH,EAAE,CAACI;YAAW,CAAE,CAAC;UAC/F,CAAC,CAAC;QACJ;MACF;KACD,CAAC;EACJ;EAEArG,WAAWA,CAAC0B,MAAsB;IAChC,IAAI,CAACY,OAAO,GAAGZ,MAAM,CAAC4E,GAAG;IACzB,IAAI,CAAChE,OAAO,CAACiE,gBAAgB,EAAE;EACjC;EAEArG,mBAAmBA,CAACwB,MAA8B;IAChD;IACAsD,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,sBAAsB,CAACvD,MAAM,CAAC4E,GAAG,CAAC;IACzC,CAAC,CAAC;EACJ;EAEArB,sBAAsBA,CAAC3C,OAAgB;IACrCA,OAAO,EAAEiE,gBAAgB,EAAE;IAC3BjE,OAAO,EAAEkE,kBAAkB,EAAE;IAE7BlE,OAAO,EAAEmE,qBAAqB,CAAEC,cAAc,IAAI;MAChDA,cAAc,CAACJ,GAAG,EAAEC,gBAAgB,EAAE;MACtCjE,OAAO,EAAEkE,kBAAkB,EAAE;IAC/B,CAAC,CAAC;EACJ;EAEA1G,YAAYA,CAAC6G,KAAsB;IAEjC;IACA,IAAIA,KAAK,CAACvE,IAAI,KAAKuE,KAAK,CAACC,QAAQ,EAAE;MACjC;MACA,MAAMC,SAAS,GAAG,IAAI,CAACtG,aAAa,CAACuG,OAAO,CAACH,KAAK,CAACvE,IAAI,CAACT,IAAI,CAAC;MAC7D,MAAMoF,OAAO,GAAG,IAAI,CAACxG,aAAa,CAACuG,OAAO,CAACH,KAAK,CAACC,QAAQ,CAACjF,IAAI,CAAC;MAE/DqF,WAAW,CAAC,IAAI,CAACzG,aAAa,EAAEsG,SAAS,EAAEE,OAAO,CAAC;MACnD,IAAI,CAACzE,OAAO,CAAC2E,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC1G,aAAa,CAAC;MACzD,IAAI,CAAC+B,OAAO,CAAC4E,gBAAgB,EAAE;IACjC;IAEA,SAASF,WAAWA,CAACG,KAAY,EAAEC,IAAY,EAAEC,EAAU;MACzD,MAAMC,OAAO,GAAGH,KAAK,CAACC,IAAI,CAAC;MAC3BD,KAAK,CAACI,MAAM,CAACH,IAAI,EAAE,CAAC,CAAC;MACrBD,KAAK,CAACI,MAAM,CAACF,EAAE,EAAE,CAAC,EAAEC,OAAO,CAAC;IAC9B;EACF;EAEAlH,eAAeA,CAACuG,KAAsB;IACpC;IACA,IAAIA,KAAK,CAACa,SAAS,KAAK,YAAY,EAAE;MACpC,MAAMC,WAAW,GAAGd,KAAK,CAACL,GAAG,CAACoB,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC/D,KAAK;MACvD,MAAMgE,cAAc,GAAGC,IAAI,CAACC,IAAI,CAAClB,KAAK,CAACL,GAAG,CAACwB,uBAAuB,EAAE,CAACC,KAAK,CAAC;MAC3E,IAAIpE,KAAK,GAAG,CAAC,CAAC,GAAG8D,WAAW;MAE5Bd,KAAK,CAACL,GAAG,CAACoB,cAAc,EAAE,CAAC1B,OAAO,CAACgC,GAAG,IAAG;QACvCrE,KAAK,IAAIqE,GAAG,CAACrE,KAAK;MACpB,CAAC,CAAC;MAEF,IAAI,CAACrD,sBAAsB,GAAGqH,cAAc,IAAIhE,KAAK,GAAG,EAAE;IAC5D;EACF;EAEAtE,sBAAsBA,CAAA;IACpB,IAAI,CAACiD,OAAO,CAAC2F,mBAAmB,CAAC,cAAc,EAAE,KAAK,CAAC;EACzD;;;uBAnPWrH,6BAA6B,EAAAjC,EAAA,CAAAuJ,iBAAA,CAAAC,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAA7BxH,6BAA6B;MAAAyH,SAAA;MAAAC,MAAA;QAAA/H,aAAA;QAAAsF,gBAAA;QAAA9E,QAAA;MAAA;MAAAwH,UAAA;MAAAC,QAAA,GAAA7J,EAAA,CAAA8J,oBAAA,EAAA9J,EAAA,CAAA+J,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1B1CrK,EAAA,CAAAE,cAAA,aAAqD;UAG/CF,EAFF,CAAAc,UAAA,IAAAyJ,oDAAA,uBAAgB,IAAAC,oDAAA,QAEP;UAiBbxK,EAAA,CAAAW,YAAA,EAAM;;;UAnBFX,EAAA,CAAAY,SAAA,EAkBC;UAlBDZ,EAAA,CAAA0B,aAAA,IAAA4I,GAAA,CAAAlI,QAAA,SAkBC;;;qBDKOvC,YAAY,EAAA4K,EAAA,CAAAC,aAAA,EAAQ5K,aAAa,EAAA6K,EAAA,CAAAC,SAAA,EAAE7K,YAAY,EAAA8K,EAAA,CAAAC,MAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}