{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig } from '@aws-amplify/core/internals/utils';\nimport '../../../client/utils/store/autoSignInStore.mjs';\nimport { signInStore, setActiveSignInState, resetActiveSignInState } from '../../../client/utils/store/signInStore.mjs';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { handleChallengeName, getSignInResult, getSignInResultFromError } from '../utils/signInHelpers.mjs';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { AuthErrorCodes } from '../../../common/AuthErrorStrings.mjs';\nimport { cacheCognitoTokens } from '../tokenProvider/cacheTokens.mjs';\nimport '../utils/refreshAuthTokens.mjs';\nimport '../tokenProvider/errorHelpers.mjs';\nimport '../utils/types.mjs';\nimport { tokenOrchestrator } from '../tokenProvider/tokenProvider.mjs';\nimport { dispatchSignedInHubEvent } from '../utils/dispatchSignedInHubEvent.mjs';\nimport { getNewDeviceMetadata } from '../utils/getNewDeviceMetadata.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Continues or completes the sign in process when required by the initial call to `signIn`.\n *\n * @param input -  The ConfirmSignInInput object\n * @returns ConfirmSignInOutput\n * @throws  -{@link VerifySoftwareTokenException }:\n * Thrown due to an invalid MFA token.\n * @throws  -{@link RespondToAuthChallengeException }:\n * Thrown due to an invalid auth challenge response.\n * @throws  -{@link AssociateSoftwareTokenException}:\n * Thrown due to a service error during the MFA setup process.\n * @throws  -{@link AuthValidationErrorCode }:\n * Thrown when `challengeResponse` is not defined.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nfunction confirmSignIn(_x) {\n  return _confirmSignIn.apply(this, arguments);\n}\nfunction _confirmSignIn() {\n  _confirmSignIn = _asyncToGenerator(function* (input) {\n    const {\n      challengeResponse,\n      options\n    } = input;\n    const {\n      username,\n      challengeName,\n      signInSession,\n      signInDetails\n    } = signInStore.getState();\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const clientMetaData = options?.clientMetadata;\n    assertValidationError(!!challengeResponse, AuthValidationErrorCode.EmptyChallengeResponse);\n    if (!username || !challengeName || !signInSession)\n      // TODO: remove this error message for production apps\n      throw new AuthError({\n        name: AuthErrorCodes.SignInException,\n        message: `\n\t\t\tAn error occurred during the sign in process.\n\n\t\t\tThis most likely occurred due to:\n\t\t\t1. signIn was not called before confirmSignIn.\n\t\t\t2. signIn threw an exception.\n\t\t\t3. page was refreshed during the sign in flow and session has expired.\n\t\t\t`,\n        recoverySuggestion: 'Make sure a successful call to signIn is made before calling confirmSignIn' + 'and that the session has not expired.'\n      });\n    try {\n      const {\n        Session,\n        ChallengeName: handledChallengeName,\n        AuthenticationResult,\n        ChallengeParameters: handledChallengeParameters\n      } = yield handleChallengeName(username, challengeName, signInSession, challengeResponse, authConfig, tokenOrchestrator, clientMetaData, options);\n      // sets up local state used during the sign-in process\n      setActiveSignInState({\n        signInSession: Session,\n        username,\n        challengeName: handledChallengeName,\n        signInDetails\n      });\n      if (AuthenticationResult) {\n        yield cacheCognitoTokens({\n          username,\n          ...AuthenticationResult,\n          NewDeviceMetadata: yield getNewDeviceMetadata({\n            userPoolId: authConfig.userPoolId,\n            userPoolEndpoint: authConfig.userPoolEndpoint,\n            newDeviceMetadata: AuthenticationResult.NewDeviceMetadata,\n            accessToken: AuthenticationResult.AccessToken\n          }),\n          signInDetails\n        });\n        resetActiveSignInState();\n        yield dispatchSignedInHubEvent();\n        return {\n          isSignedIn: true,\n          nextStep: {\n            signInStep: 'DONE'\n          }\n        };\n      }\n      return getSignInResult({\n        challengeName: handledChallengeName,\n        challengeParameters: handledChallengeParameters\n      });\n    } catch (error) {\n      assertServiceError(error);\n      const result = getSignInResultFromError(error.name);\n      if (result) return result;\n      throw error;\n    }\n  });\n  return _confirmSignIn.apply(this, arguments);\n}\nexport { confirmSignIn };", "map": {"version": 3, "names": ["Amplify", "assertTokenProviderConfig", "signInStore", "setActiveSignInState", "resetActiveSignInState", "<PERSON>th<PERSON><PERSON><PERSON>", "handleChallengeName", "getSignInResult", "getSignInResultFromError", "assertServiceError", "assertValidationError", "AuthValidationErrorCode", "AuthErrorCodes", "cacheCognitoTokens", "tokenOrchestrator", "dispatchSignedInHubEvent", "getNewDeviceMetadata", "confirmSignIn", "_x", "_confirmSignIn", "apply", "arguments", "_asyncToGenerator", "input", "challengeResponse", "options", "username", "challenge<PERSON>ame", "signInSession", "signInDetails", "getState", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "clientMetaData", "clientMetadata", "EmptyChallengeResponse", "name", "SignInException", "message", "recoverySuggestion", "Session", "ChallengeName", "handledChallengeName", "AuthenticationResult", "ChallengeParameters", "handledChallengeParameters", "NewDeviceMetadata", "userPoolId", "userPoolEndpoint", "newDeviceMetadata", "accessToken", "AccessToken", "isSignedIn", "nextStep", "signInStep", "challengeParameters", "error", "result"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/confirmSignIn.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig } from '@aws-amplify/core/internals/utils';\nimport '../../../client/utils/store/autoSignInStore.mjs';\nimport { signInStore, setActiveSignInState, resetActiveSignInState } from '../../../client/utils/store/signInStore.mjs';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { handleChallengeName, getSignInResult, getSignInResultFromError } from '../utils/signInHelpers.mjs';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { AuthErrorCodes } from '../../../common/AuthErrorStrings.mjs';\nimport { cacheCognitoTokens } from '../tokenProvider/cacheTokens.mjs';\nimport '../utils/refreshAuthTokens.mjs';\nimport '../tokenProvider/errorHelpers.mjs';\nimport '../utils/types.mjs';\nimport { tokenOrchestrator } from '../tokenProvider/tokenProvider.mjs';\nimport { dispatchSignedInHubEvent } from '../utils/dispatchSignedInHubEvent.mjs';\nimport { getNewDeviceMetadata } from '../utils/getNewDeviceMetadata.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Continues or completes the sign in process when required by the initial call to `signIn`.\n *\n * @param input -  The ConfirmSignInInput object\n * @returns ConfirmSignInOutput\n * @throws  -{@link VerifySoftwareTokenException }:\n * Thrown due to an invalid MFA token.\n * @throws  -{@link RespondToAuthChallengeException }:\n * Thrown due to an invalid auth challenge response.\n * @throws  -{@link AssociateSoftwareTokenException}:\n * Thrown due to a service error during the MFA setup process.\n * @throws  -{@link AuthValidationErrorCode }:\n * Thrown when `challengeResponse` is not defined.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nasync function confirmSignIn(input) {\n    const { challengeResponse, options } = input;\n    const { username, challengeName, signInSession, signInDetails } = signInStore.getState();\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const clientMetaData = options?.clientMetadata;\n    assertValidationError(!!challengeResponse, AuthValidationErrorCode.EmptyChallengeResponse);\n    if (!username || !challengeName || !signInSession)\n        // TODO: remove this error message for production apps\n        throw new AuthError({\n            name: AuthErrorCodes.SignInException,\n            message: `\n\t\t\tAn error occurred during the sign in process.\n\n\t\t\tThis most likely occurred due to:\n\t\t\t1. signIn was not called before confirmSignIn.\n\t\t\t2. signIn threw an exception.\n\t\t\t3. page was refreshed during the sign in flow and session has expired.\n\t\t\t`,\n            recoverySuggestion: 'Make sure a successful call to signIn is made before calling confirmSignIn' +\n                'and that the session has not expired.',\n        });\n    try {\n        const { Session, ChallengeName: handledChallengeName, AuthenticationResult, ChallengeParameters: handledChallengeParameters, } = await handleChallengeName(username, challengeName, signInSession, challengeResponse, authConfig, tokenOrchestrator, clientMetaData, options);\n        // sets up local state used during the sign-in process\n        setActiveSignInState({\n            signInSession: Session,\n            username,\n            challengeName: handledChallengeName,\n            signInDetails,\n        });\n        if (AuthenticationResult) {\n            await cacheCognitoTokens({\n                username,\n                ...AuthenticationResult,\n                NewDeviceMetadata: await getNewDeviceMetadata({\n                    userPoolId: authConfig.userPoolId,\n                    userPoolEndpoint: authConfig.userPoolEndpoint,\n                    newDeviceMetadata: AuthenticationResult.NewDeviceMetadata,\n                    accessToken: AuthenticationResult.AccessToken,\n                }),\n                signInDetails,\n            });\n            resetActiveSignInState();\n            await dispatchSignedInHubEvent();\n            return {\n                isSignedIn: true,\n                nextStep: { signInStep: 'DONE' },\n            };\n        }\n        return getSignInResult({\n            challengeName: handledChallengeName,\n            challengeParameters: handledChallengeParameters,\n        });\n    }\n    catch (error) {\n        assertServiceError(error);\n        const result = getSignInResultFromError(error.name);\n        if (result)\n            return result;\n        throw error;\n    }\n}\n\nexport { confirmSignIn };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,yBAAyB,QAAQ,mCAAmC;AAC7E,OAAO,iDAAiD;AACxD,SAASC,WAAW,EAAEC,oBAAoB,EAAEC,sBAAsB,QAAQ,6CAA6C;AACvH,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,mBAAmB,EAAEC,eAAe,EAAEC,wBAAwB,QAAQ,4BAA4B;AAC3G,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,cAAc,QAAQ,sCAAsC;AACrE,SAASC,kBAAkB,QAAQ,kCAAkC;AACrE,OAAO,gCAAgC;AACvC,OAAO,mCAAmC;AAC1C,OAAO,oBAAoB;AAC3B,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,SAASC,wBAAwB,QAAQ,uCAAuC;AAChF,SAASC,oBAAoB,QAAQ,mCAAmC;;AAExE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,SAeeC,aAAaA,CAAAC,EAAA;EAAA,OAAAC,cAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,eAAA;EAAAA,cAAA,GAAAG,iBAAA,CAA5B,WAA6BC,KAAK,EAAE;IAChC,MAAM;MAAEC,iBAAiB;MAAEC;IAAQ,CAAC,GAAGF,KAAK;IAC5C,MAAM;MAAEG,QAAQ;MAAEC,aAAa;MAAEC,aAAa;MAAEC;IAAc,CAAC,GAAG3B,WAAW,CAAC4B,QAAQ,CAAC,CAAC;IACxF,MAAMC,UAAU,GAAG/B,OAAO,CAACgC,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDjC,yBAAyB,CAAC8B,UAAU,CAAC;IACrC,MAAMI,cAAc,GAAGV,OAAO,EAAEW,cAAc;IAC9C1B,qBAAqB,CAAC,CAAC,CAACc,iBAAiB,EAAEb,uBAAuB,CAAC0B,sBAAsB,CAAC;IAC1F,IAAI,CAACX,QAAQ,IAAI,CAACC,aAAa,IAAI,CAACC,aAAa;MAC7C;MACA,MAAM,IAAIvB,SAAS,CAAC;QAChBiC,IAAI,EAAE1B,cAAc,CAAC2B,eAAe;QACpCC,OAAO,EAAE;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;QACQC,kBAAkB,EAAE,4EAA4E,GAC5F;MACR,CAAC,CAAC;IACN,IAAI;MACA,MAAM;QAAEC,OAAO;QAAEC,aAAa,EAAEC,oBAAoB;QAAEC,oBAAoB;QAAEC,mBAAmB,EAAEC;MAA4B,CAAC,SAASzC,mBAAmB,CAACoB,QAAQ,EAAEC,aAAa,EAAEC,aAAa,EAAEJ,iBAAiB,EAAEO,UAAU,EAAEjB,iBAAiB,EAAEqB,cAAc,EAAEV,OAAO,CAAC;MAC7Q;MACAtB,oBAAoB,CAAC;QACjByB,aAAa,EAAEc,OAAO;QACtBhB,QAAQ;QACRC,aAAa,EAAEiB,oBAAoB;QACnCf;MACJ,CAAC,CAAC;MACF,IAAIgB,oBAAoB,EAAE;QACtB,MAAMhC,kBAAkB,CAAC;UACrBa,QAAQ;UACR,GAAGmB,oBAAoB;UACvBG,iBAAiB,QAAQhC,oBAAoB,CAAC;YAC1CiC,UAAU,EAAElB,UAAU,CAACkB,UAAU;YACjCC,gBAAgB,EAAEnB,UAAU,CAACmB,gBAAgB;YAC7CC,iBAAiB,EAAEN,oBAAoB,CAACG,iBAAiB;YACzDI,WAAW,EAAEP,oBAAoB,CAACQ;UACtC,CAAC,CAAC;UACFxB;QACJ,CAAC,CAAC;QACFzB,sBAAsB,CAAC,CAAC;QACxB,MAAMW,wBAAwB,CAAC,CAAC;QAChC,OAAO;UACHuC,UAAU,EAAE,IAAI;UAChBC,QAAQ,EAAE;YAAEC,UAAU,EAAE;UAAO;QACnC,CAAC;MACL;MACA,OAAOjD,eAAe,CAAC;QACnBoB,aAAa,EAAEiB,oBAAoB;QACnCa,mBAAmB,EAAEV;MACzB,CAAC,CAAC;IACN,CAAC,CACD,OAAOW,KAAK,EAAE;MACVjD,kBAAkB,CAACiD,KAAK,CAAC;MACzB,MAAMC,MAAM,GAAGnD,wBAAwB,CAACkD,KAAK,CAACpB,IAAI,CAAC;MACnD,IAAIqB,MAAM,EACN,OAAOA,MAAM;MACjB,MAAMD,KAAK;IACf;EACJ,CAAC;EAAA,OAAAvC,cAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}