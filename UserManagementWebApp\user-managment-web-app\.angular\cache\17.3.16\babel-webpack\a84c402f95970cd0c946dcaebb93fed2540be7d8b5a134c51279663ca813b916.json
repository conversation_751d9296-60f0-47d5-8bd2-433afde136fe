{"ast": null, "code": "const passwordfield = {\n  color: {\n    value: '{components.fieldcontrol.color.value}'\n  },\n  button: {\n    color: {\n      value: '{components.button.color.value}'\n    },\n    _active: {\n      backgroundColor: {\n        value: '{components.button._active.backgroundColor.value}'\n      },\n      borderColor: {\n        value: '{components.button._active.borderColor.value}'\n      },\n      color: {\n        value: '{components.button._active.color.value}'\n      }\n    },\n    _disabled: {\n      backgroundColor: {\n        value: '{components.button._disabled.backgroundColor.value}'\n      },\n      borderColor: {\n        value: '{components.button._disabled.borderColor.value}'\n      },\n      color: {\n        value: '{components.button._disabled.color.value}'\n      }\n    },\n    _error: {\n      color: {\n        value: '{components.button.outlined.error.color.value}'\n      },\n      backgroundColor: {\n        value: '{components.button.outlined.error.backgroundColor.value}'\n      },\n      borderColor: {\n        value: '{components.button.outlined.error.borderColor.value}'\n      },\n      _active: {\n        borderColor: {\n          value: '{components.button.outlined.error._active.borderColor.value}'\n        },\n        backgroundColor: {\n          value: '{components.button.outlined.error._active.backgroundColor.value}'\n        },\n        color: {\n          value: '{components.button.outlined.error._active.color.value}'\n        }\n      },\n      _focus: {\n        borderColor: {\n          value: '{components.button.outlined.error._focus.borderColor.value}'\n        },\n        backgroundColor: {\n          value: '{components.button.outlined.error._focus.backgroundColor.value}'\n        },\n        color: {\n          value: '{components.button.outlined.error._focus.color.value}'\n        },\n        boxShadow: {\n          value: '{components.button.outlined.error._focus.boxShadow.value}'\n        }\n      },\n      _hover: {\n        borderColor: {\n          value: '{components.button.outlined.error._hover.borderColor.value}'\n        },\n        backgroundColor: {\n          value: '{components.button.outlined.error._hover.backgroundColor.value}'\n        },\n        color: {\n          value: '{components.button.outlined.error._hover.color.value}'\n        }\n      }\n    },\n    _focus: {\n      backgroundColor: {\n        value: '{components.button._focus.backgroundColor.value}'\n      },\n      borderColor: {\n        value: '{components.button._focus.borderColor.value}'\n      },\n      color: {\n        value: '{components.button._focus.color.value}'\n      }\n    },\n    _hover: {\n      backgroundColor: {\n        value: '{components.button._hover.backgroundColor.value}'\n      },\n      borderColor: {\n        value: '{components.button._hover.borderColor.value}'\n      },\n      color: {\n        value: '{components.button._hover.color.value}'\n      }\n    }\n  }\n};\nexport { passwordfield };", "map": {"version": 3, "names": ["passwordfield", "color", "value", "button", "_active", "backgroundColor", "borderColor", "_disabled", "_error", "_focus", "boxShadow", "_hover"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/passwordField.mjs"], "sourcesContent": ["const passwordfield = {\n    color: { value: '{components.fieldcontrol.color.value}' },\n    button: {\n        color: { value: '{components.button.color.value}' },\n        _active: {\n            backgroundColor: {\n                value: '{components.button._active.backgroundColor.value}',\n            },\n            borderColor: { value: '{components.button._active.borderColor.value}' },\n            color: { value: '{components.button._active.color.value}' },\n        },\n        _disabled: {\n            backgroundColor: {\n                value: '{components.button._disabled.backgroundColor.value}',\n            },\n            borderColor: {\n                value: '{components.button._disabled.borderColor.value}',\n            },\n            color: { value: '{components.button._disabled.color.value}' },\n        },\n        _error: {\n            color: { value: '{components.button.outlined.error.color.value}' },\n            backgroundColor: {\n                value: '{components.button.outlined.error.backgroundColor.value}',\n            },\n            borderColor: {\n                value: '{components.button.outlined.error.borderColor.value}',\n            },\n            _active: {\n                borderColor: {\n                    value: '{components.button.outlined.error._active.borderColor.value}',\n                },\n                backgroundColor: {\n                    value: '{components.button.outlined.error._active.backgroundColor.value}',\n                },\n                color: {\n                    value: '{components.button.outlined.error._active.color.value}',\n                },\n            },\n            _focus: {\n                borderColor: {\n                    value: '{components.button.outlined.error._focus.borderColor.value}',\n                },\n                backgroundColor: {\n                    value: '{components.button.outlined.error._focus.backgroundColor.value}',\n                },\n                color: {\n                    value: '{components.button.outlined.error._focus.color.value}',\n                },\n                boxShadow: {\n                    value: '{components.button.outlined.error._focus.boxShadow.value}',\n                },\n            },\n            _hover: {\n                borderColor: {\n                    value: '{components.button.outlined.error._hover.borderColor.value}',\n                },\n                backgroundColor: {\n                    value: '{components.button.outlined.error._hover.backgroundColor.value}',\n                },\n                color: {\n                    value: '{components.button.outlined.error._hover.color.value}',\n                },\n            },\n        },\n        _focus: {\n            backgroundColor: {\n                value: '{components.button._focus.backgroundColor.value}',\n            },\n            borderColor: { value: '{components.button._focus.borderColor.value}' },\n            color: { value: '{components.button._focus.color.value}' },\n        },\n        _hover: {\n            backgroundColor: {\n                value: '{components.button._hover.backgroundColor.value}',\n            },\n            borderColor: { value: '{components.button._hover.borderColor.value}' },\n            color: { value: '{components.button._hover.color.value}' },\n        },\n    },\n};\n\nexport { passwordfield };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAG;EAClBC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAwC,CAAC;EACzDC,MAAM,EAAE;IACJF,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAkC,CAAC;IACnDE,OAAO,EAAE;MACLC,eAAe,EAAE;QACbH,KAAK,EAAE;MACX,CAAC;MACDI,WAAW,EAAE;QAAEJ,KAAK,EAAE;MAAgD,CAAC;MACvED,KAAK,EAAE;QAAEC,KAAK,EAAE;MAA0C;IAC9D,CAAC;IACDK,SAAS,EAAE;MACPF,eAAe,EAAE;QACbH,KAAK,EAAE;MACX,CAAC;MACDI,WAAW,EAAE;QACTJ,KAAK,EAAE;MACX,CAAC;MACDD,KAAK,EAAE;QAAEC,KAAK,EAAE;MAA4C;IAChE,CAAC;IACDM,MAAM,EAAE;MACJP,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAiD,CAAC;MAClEG,eAAe,EAAE;QACbH,KAAK,EAAE;MACX,CAAC;MACDI,WAAW,EAAE;QACTJ,KAAK,EAAE;MACX,CAAC;MACDE,OAAO,EAAE;QACLE,WAAW,EAAE;UACTJ,KAAK,EAAE;QACX,CAAC;QACDG,eAAe,EAAE;UACbH,KAAK,EAAE;QACX,CAAC;QACDD,KAAK,EAAE;UACHC,KAAK,EAAE;QACX;MACJ,CAAC;MACDO,MAAM,EAAE;QACJH,WAAW,EAAE;UACTJ,KAAK,EAAE;QACX,CAAC;QACDG,eAAe,EAAE;UACbH,KAAK,EAAE;QACX,CAAC;QACDD,KAAK,EAAE;UACHC,KAAK,EAAE;QACX,CAAC;QACDQ,SAAS,EAAE;UACPR,KAAK,EAAE;QACX;MACJ,CAAC;MACDS,MAAM,EAAE;QACJL,WAAW,EAAE;UACTJ,KAAK,EAAE;QACX,CAAC;QACDG,eAAe,EAAE;UACbH,KAAK,EAAE;QACX,CAAC;QACDD,KAAK,EAAE;UACHC,KAAK,EAAE;QACX;MACJ;IACJ,CAAC;IACDO,MAAM,EAAE;MACJJ,eAAe,EAAE;QACbH,KAAK,EAAE;MACX,CAAC;MACDI,WAAW,EAAE;QAAEJ,KAAK,EAAE;MAA+C,CAAC;MACtED,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAyC;IAC7D,CAAC;IACDS,MAAM,EAAE;MACJN,eAAe,EAAE;QACbH,KAAK,EAAE;MACX,CAAC;MACDI,WAAW,EAAE;QAAEJ,KAAK,EAAE;MAA+C,CAAC;MACtED,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAyC;IAC7D;EACJ;AACJ,CAAC;AAED,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}