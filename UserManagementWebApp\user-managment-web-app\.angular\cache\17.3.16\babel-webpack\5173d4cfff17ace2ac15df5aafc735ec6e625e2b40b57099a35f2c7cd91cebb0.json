{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { getUserContextData } from '../utils/userContextData.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../types/errors.mjs';\nimport { createConfirmForgotPasswordClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createConfirmForgotPasswordClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Confirms the new password and verification code to reset the password.\n *\n * @param input -  The ConfirmResetPasswordInput object.\n * @throws -{@link ConfirmForgotPasswordException }\n * Thrown due to an invalid confirmation code or password.\n * @throws -{@link AuthValidationErrorCode }\n * Thrown due to an empty confirmation code, password or username.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nfunction confirmResetPassword(_x) {\n  return _confirmResetPassword.apply(this, arguments);\n}\nfunction _confirmResetPassword() {\n  _confirmResetPassword = _asyncToGenerator(function* (input) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolClientId,\n      userPoolId,\n      userPoolEndpoint\n    } = authConfig;\n    const {\n      username,\n      newPassword\n    } = input;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptyConfirmResetPasswordUsername);\n    assertValidationError(!!newPassword, AuthValidationErrorCode.EmptyConfirmResetPasswordNewPassword);\n    const code = input.confirmationCode;\n    assertValidationError(!!code, AuthValidationErrorCode.EmptyConfirmResetPasswordConfirmationCode);\n    const metadata = input.options?.clientMetadata;\n    const UserContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const confirmForgotPassword = createConfirmForgotPasswordClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    yield confirmForgotPassword({\n      region: getRegionFromUserPoolId(authConfig.userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmResetPassword)\n    }, {\n      Username: username,\n      ConfirmationCode: code,\n      Password: newPassword,\n      ClientMetadata: metadata,\n      ClientId: authConfig.userPoolClientId,\n      UserContextData\n    });\n  });\n  return _confirmResetPassword.apply(this, arguments);\n}\nexport { confirmResetPassword };", "map": {"version": 3, "names": ["Amplify", "assertTokenProviderConfig", "AuthAction", "AuthValidationErrorCode", "assertValidationError", "getAuthUserAgentValue", "getUserContextData", "createConfirmForgotPasswordClient", "createCognitoUserPoolEndpointResolver", "getRegionFromUserPoolId", "confirmResetPassword", "_x", "_confirmResetPassword", "apply", "arguments", "_asyncToGenerator", "input", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userPoolClientId", "userPoolId", "userPoolEndpoint", "username", "newPassword", "EmptyConfirmResetPasswordUsername", "EmptyConfirmResetPasswordNewPassword", "code", "confirmationCode", "EmptyConfirmResetPasswordConfirmationCode", "metadata", "options", "clientMetadata", "UserContextData", "confirmForgotPassword", "endpointResolver", "endpointOverride", "region", "userAgentValue", "ConfirmResetPassword", "Username", "ConfirmationCode", "Password", "ClientMetadata", "ClientId"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/confirmResetPassword.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { getUserContextData } from '../utils/userContextData.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../types/errors.mjs';\nimport { createConfirmForgotPasswordClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createConfirmForgotPasswordClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Confirms the new password and verification code to reset the password.\n *\n * @param input -  The ConfirmResetPasswordInput object.\n * @throws -{@link ConfirmForgotPasswordException }\n * Thrown due to an invalid confirmation code or password.\n * @throws -{@link AuthValidationErrorCode }\n * Thrown due to an empty confirmation code, password or username.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nasync function confirmResetPassword(input) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolClientId, userPoolId, userPoolEndpoint } = authConfig;\n    const { username, newPassword } = input;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptyConfirmResetPasswordUsername);\n    assertValidationError(!!newPassword, AuthValidationErrorCode.EmptyConfirmResetPasswordNewPassword);\n    const code = input.confirmationCode;\n    assertValidationError(!!code, AuthValidationErrorCode.EmptyConfirmResetPasswordConfirmationCode);\n    const metadata = input.options?.clientMetadata;\n    const UserContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const confirmForgotPassword = createConfirmForgotPasswordClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    await confirmForgotPassword({\n        region: getRegionFromUserPoolId(authConfig.userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmResetPassword),\n    }, {\n        Username: username,\n        ConfirmationCode: code,\n        Password: newPassword,\n        ClientMetadata: metadata,\n        ClientId: authConfig.userPoolClientId,\n        UserContextData,\n    });\n}\n\nexport { confirmResetPassword };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,qBAAqB;AAC5B,SAASC,iCAAiC,QAAQ,4GAA4G;AAC9J,SAASC,qCAAqC,QAAQ,wDAAwD;AAC9G,SAASC,uBAAuB,QAAQ,+CAA+C;;AAEvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,SAUeC,oBAAoBA,CAAAC,EAAA;EAAA,OAAAC,qBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,sBAAA;EAAAA,qBAAA,GAAAG,iBAAA,CAAnC,WAAoCC,KAAK,EAAE;IACvC,MAAMC,UAAU,GAAGjB,OAAO,CAACkB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDnB,yBAAyB,CAACgB,UAAU,CAAC;IACrC,MAAM;MAAEI,gBAAgB;MAAEC,UAAU;MAAEC;IAAiB,CAAC,GAAGN,UAAU;IACrE,MAAM;MAAEO,QAAQ;MAAEC;IAAY,CAAC,GAAGT,KAAK;IACvCZ,qBAAqB,CAAC,CAAC,CAACoB,QAAQ,EAAErB,uBAAuB,CAACuB,iCAAiC,CAAC;IAC5FtB,qBAAqB,CAAC,CAAC,CAACqB,WAAW,EAAEtB,uBAAuB,CAACwB,oCAAoC,CAAC;IAClG,MAAMC,IAAI,GAAGZ,KAAK,CAACa,gBAAgB;IACnCzB,qBAAqB,CAAC,CAAC,CAACwB,IAAI,EAAEzB,uBAAuB,CAAC2B,yCAAyC,CAAC;IAChG,MAAMC,QAAQ,GAAGf,KAAK,CAACgB,OAAO,EAAEC,cAAc;IAC9C,MAAMC,eAAe,GAAG5B,kBAAkB,CAAC;MACvCkB,QAAQ;MACRF,UAAU;MACVD;IACJ,CAAC,CAAC;IACF,MAAMc,qBAAqB,GAAG5B,iCAAiC,CAAC;MAC5D6B,gBAAgB,EAAE5B,qCAAqC,CAAC;QACpD6B,gBAAgB,EAAEd;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMY,qBAAqB,CAAC;MACxBG,MAAM,EAAE7B,uBAAuB,CAACQ,UAAU,CAACK,UAAU,CAAC;MACtDiB,cAAc,EAAElC,qBAAqB,CAACH,UAAU,CAACsC,oBAAoB;IACzE,CAAC,EAAE;MACCC,QAAQ,EAAEjB,QAAQ;MAClBkB,gBAAgB,EAAEd,IAAI;MACtBe,QAAQ,EAAElB,WAAW;MACrBmB,cAAc,EAAEb,QAAQ;MACxBc,QAAQ,EAAE5B,UAAU,CAACI,gBAAgB;MACrCa;IACJ,CAAC,CAAC;EACN,CAAC;EAAA,OAAAtB,qBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}