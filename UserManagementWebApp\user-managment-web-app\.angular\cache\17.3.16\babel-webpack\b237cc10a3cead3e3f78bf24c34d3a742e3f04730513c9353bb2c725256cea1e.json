{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst inflightPromises = [];\nconst addInflightPromise = resolver => {\n  inflightPromises.push(resolver);\n};\nconst resolveAndClearInflightPromises = () => {\n  while (inflightPromises.length) {\n    inflightPromises.pop()?.();\n  }\n};\nexport { addInflightPromise, resolveAndClearInflightPromises };", "map": {"version": 3, "names": ["inflightPromises", "addInflightPromise", "resolver", "push", "resolveAndClearInflightPromises", "length", "pop"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/oauth/inflightPromise.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst inflightPromises = [];\nconst addInflightPromise = (resolver) => {\n    inflightPromises.push(resolver);\n};\nconst resolveAndClearInflightPromises = () => {\n    while (inflightPromises.length) {\n        inflightPromises.pop()?.();\n    }\n};\n\nexport { addInflightPromise, resolveAndClearInflightPromises };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,gBAAgB,GAAG,EAAE;AAC3B,MAAMC,kBAAkB,GAAIC,QAAQ,IAAK;EACrCF,gBAAgB,CAACG,IAAI,CAACD,QAAQ,CAAC;AACnC,CAAC;AACD,MAAME,+BAA+B,GAAGA,CAAA,KAAM;EAC1C,OAAOJ,gBAAgB,CAACK,MAAM,EAAE;IAC5BL,gBAAgB,CAACM,GAAG,CAAC,CAAC,GAAG,CAAC;EAC9B;AACJ,CAAC;AAED,SAASL,kBAAkB,EAAEG,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}