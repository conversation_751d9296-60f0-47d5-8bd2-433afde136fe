{"ast": null, "code": "import { CognitoUserPoolsTokenProvider } from './CognitoUserPoolsTokenProvider.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * The default provider for the JWT access token and ID token issued from the configured Cognito user pool. It manages\n * the refresh and storage of the tokens. It stores the tokens in `window.localStorage` if available, and falls back to\n * in-memory storage if not.\n */\nconst cognitoUserPoolsTokenProvider = new CognitoUserPoolsTokenProvider();\nconst {\n  tokenOrchestrator\n} = cognitoUserPoolsTokenProvider;\nexport { cognitoUserPoolsTokenProvider, tokenOrchestrator };", "map": {"version": 3, "names": ["CognitoUserPoolsTokenProvider", "cognitoUserPoolsTokenProvider", "tokenOrchestrator"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/tokenProvider/tokenProvider.mjs"], "sourcesContent": ["import { CognitoUserPoolsTokenProvider } from './CognitoUserPoolsTokenProvider.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * The default provider for the JWT access token and ID token issued from the configured Cognito user pool. It manages\n * the refresh and storage of the tokens. It stores the tokens in `window.localStorage` if available, and falls back to\n * in-memory storage if not.\n */\nconst cognitoUserPoolsTokenProvider = new CognitoUserPoolsTokenProvider();\nconst { tokenOrchestrator } = cognitoUserPoolsTokenProvider;\n\nexport { cognitoUserPoolsTokenProvider, tokenOrchestrator };\n"], "mappings": "AAAA,SAASA,6BAA6B,QAAQ,qCAAqC;;AAEnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,6BAA6B,GAAG,IAAID,6BAA6B,CAAC,CAAC;AACzE,MAAM;EAAEE;AAAkB,CAAC,GAAGD,6BAA6B;AAE3D,SAASA,6BAA6B,EAAEC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}