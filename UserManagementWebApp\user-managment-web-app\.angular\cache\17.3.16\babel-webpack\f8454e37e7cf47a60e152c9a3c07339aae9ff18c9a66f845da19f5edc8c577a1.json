{"ast": null, "code": "const borderWidths = {\n  small: {\n    value: '1px'\n  },\n  medium: {\n    value: '2px'\n  },\n  large: {\n    value: '3px'\n  }\n};\nexport { borderWidths };", "map": {"version": 3, "names": ["borderWidths", "small", "value", "medium", "large"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/borderWidths.mjs"], "sourcesContent": ["const borderWidths = {\n    small: { value: '1px' },\n    medium: { value: '2px' },\n    large: { value: '3px' },\n};\n\nexport { borderWidths };\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG;EACjBC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAM,CAAC;EACvBC,MAAM,EAAE;IAAED,KAAK,EAAE;EAAM,CAAC;EACxBE,KAAK,EAAE;IAAEF,KAAK,EAAE;EAAM;AAC1B,CAAC;AAED,SAASF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}