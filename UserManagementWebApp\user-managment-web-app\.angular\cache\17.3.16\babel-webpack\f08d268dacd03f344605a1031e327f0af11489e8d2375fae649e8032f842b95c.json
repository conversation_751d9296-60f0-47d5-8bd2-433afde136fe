{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ConsoleLogger } from '../Logger/ConsoleLogger.mjs';\nimport { defaultConfig, currentSizeKey } from './constants.mjs';\nimport { getCurrentSizeKey, getCurrentTime, getByteLength } from './utils/cacheHelpers.mjs';\nimport { assert, CacheErrorCode } from './utils/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('StorageCache');\n/**\n * Initialization of the cache\n *\n */\nclass StorageCacheCommon {\n  /**\n   * Initialize the cache\n   *\n   * @param config - Custom configuration for this instance.\n   */\n  constructor({\n    config,\n    keyValueStorage\n  }) {\n    this.config = {\n      ...defaultConfig,\n      ...config\n    };\n    this.keyValueStorage = keyValueStorage;\n    this.sanitizeConfig();\n  }\n  getModuleName() {\n    return 'Cache';\n  }\n  /**\n   * Set custom configuration for the cache instance.\n   *\n   * @param config - customized configuration (without keyPrefix, which can't be changed)\n   *\n   * @return - the current configuration\n   */\n  configure(config) {\n    if (config) {\n      if (config.keyPrefix) {\n        logger.warn('keyPrefix can not be re-configured on an existing Cache instance.');\n      }\n      this.config = {\n        ...this.config,\n        ...config\n      };\n    }\n    this.sanitizeConfig();\n    return this.config;\n  }\n  /**\n   * return the current size of the cache\n   * @return {Promise}\n   */\n  getCurrentCacheSize() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      let size = yield _this.getStorage().getItem(getCurrentSizeKey(_this.config.keyPrefix));\n      if (!size) {\n        yield _this.getStorage().setItem(getCurrentSizeKey(_this.config.keyPrefix), '0');\n        size = '0';\n      }\n      return Number(size);\n    })();\n  }\n  /**\n   * Set item into cache. You can put number, string, boolean or object.\n   * The cache will first check whether has the same key.\n   * If it has, it will delete the old item and then put the new item in\n   * The cache will pop out items if it is full\n   * You can specify the cache item options. The cache will abort and output a warning:\n   * If the key is invalid\n   * If the size of the item exceeds itemMaxSize.\n   * If the value is undefined\n   * If incorrect cache item configuration\n   * If error happened with browser storage\n   *\n   * @param {String} key - the key of the item\n   * @param {Object} value - the value of the item\n   * @param {Object} [options] - optional, the specified meta-data\n   *\n   * @return {Promise}\n   */\n  setItem(key, value, options) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      logger.debug(`Set item: key is ${key}, value is ${value} with options: ${options}`);\n      if (!key || key === currentSizeKey) {\n        logger.warn(`Invalid key: should not be empty or reserved key: '${currentSizeKey}'`);\n        return;\n      }\n      if (typeof value === 'undefined') {\n        logger.warn(`The value of item should not be undefined!`);\n        return;\n      }\n      const cacheItemOptions = {\n        priority: options?.priority !== undefined ? options.priority : _this2.config.defaultPriority,\n        expires: options?.expires !== undefined ? options.expires : _this2.config.defaultTTL + getCurrentTime()\n      };\n      if (cacheItemOptions.priority < 1 || cacheItemOptions.priority > 5) {\n        logger.warn(`Invalid parameter: priority due to out or range. It should be within 1 and 5.`);\n        return;\n      }\n      const prefixedKey = `${_this2.config.keyPrefix}${key}`;\n      const item = _this2.fillCacheItem(prefixedKey, value, cacheItemOptions);\n      // check whether this item is too big;\n      if (item.byteSize > _this2.config.itemMaxSize) {\n        logger.warn(`Item with key: ${key} you are trying to put into is too big!`);\n        return;\n      }\n      try {\n        // first look into the storage, if it exists, delete it.\n        const val = yield _this2.getStorage().getItem(prefixedKey);\n        if (val) {\n          yield _this2.removeCacheItem(prefixedKey, JSON.parse(val).byteSize);\n        }\n        // check whether the cache is full\n        if (yield _this2.isCacheFull(item.byteSize)) {\n          const validKeys = yield _this2.clearInvalidAndGetRemainingKeys();\n          if (yield _this2.isCacheFull(item.byteSize)) {\n            const sizeToPop = yield _this2.sizeToPop(item.byteSize);\n            yield _this2.popOutItems(validKeys, sizeToPop);\n          }\n        }\n        // put item in the cache\n        return _this2.setCacheItem(prefixedKey, item);\n      } catch (e) {\n        logger.warn(`setItem failed! ${e}`);\n      }\n    })();\n  }\n  /**\n   * Get item from cache. It will return null if item doesn’t exist or it has been expired.\n   * If you specified callback function in the options,\n   * then the function will be executed if no such item in the cache\n   * and finally put the return value into cache.\n   * Please make sure the callback function will return the value you want to put into the cache.\n   * The cache will abort output a warning:\n   * If the key is invalid\n   * If error happened with AsyncStorage\n   *\n   * @param {String} key - the key of the item\n   * @param {Object} [options] - the options of callback function\n   *\n   * @return {Promise} - return a promise resolves to be the value of the item\n   */\n  getItem(key, options) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      logger.debug(`Get item: key is ${key} with options ${options}`);\n      let cached;\n      if (!key || key === currentSizeKey) {\n        logger.warn(`Invalid key: should not be empty or reserved key: '${currentSizeKey}'`);\n        return null;\n      }\n      const prefixedKey = `${_this3.config.keyPrefix}${key}`;\n      try {\n        cached = yield _this3.getStorage().getItem(prefixedKey);\n        if (cached != null) {\n          if (yield _this3.isExpired(prefixedKey)) {\n            // if expired, remove that item and return null\n            yield _this3.removeCacheItem(prefixedKey, JSON.parse(cached).byteSize);\n          } else {\n            // if not expired, update its visitedTime and return the value\n            const item = yield _this3.updateVisitedTime(JSON.parse(cached), prefixedKey);\n            return item.data;\n          }\n        }\n        if (options?.callback) {\n          const val = options.callback();\n          if (val !== null) {\n            yield _this3.setItem(key, val, options);\n          }\n          return val;\n        }\n        return null;\n      } catch (e) {\n        logger.warn(`getItem failed! ${e}`);\n        return null;\n      }\n    })();\n  }\n  /**\n   * remove item from the cache\n   * The cache will abort output a warning:\n   * If error happened with AsyncStorage\n   * @param {String} key - the key of the item\n   * @return {Promise}\n   */\n  removeItem(key) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      logger.debug(`Remove item: key is ${key}`);\n      if (!key || key === currentSizeKey) {\n        logger.warn(`Invalid key: should not be empty or reserved key: '${currentSizeKey}'`);\n        return;\n      }\n      const prefixedKey = `${_this4.config.keyPrefix}${key}`;\n      try {\n        const val = yield _this4.getStorage().getItem(prefixedKey);\n        if (val) {\n          yield _this4.removeCacheItem(prefixedKey, JSON.parse(val).byteSize);\n        }\n      } catch (e) {\n        logger.warn(`removeItem failed! ${e}`);\n      }\n    })();\n  }\n  /**\n   * Return all the keys owned by this cache.\n   * Will return an empty array if error occurred.\n   *\n   * @return {Promise}\n   */\n  getAllKeys() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        return yield _this5.getAllCacheKeys();\n      } catch (e) {\n        logger.warn(`getAllkeys failed! ${e}`);\n        return [];\n      }\n    })();\n  }\n  getStorage() {\n    return this.keyValueStorage;\n  }\n  /**\n   * check whether item is expired\n   *\n   * @param key - the key of the item\n   *\n   * @return true if the item is expired.\n   */\n  isExpired(key) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const text = yield _this6.getStorage().getItem(key);\n      assert(text !== null, CacheErrorCode.NoCacheItem, `Key: ${key}`);\n      const item = JSON.parse(text);\n      if (getCurrentTime() >= item.expires) {\n        return true;\n      }\n      return false;\n    })();\n  }\n  /**\n   * delete item from cache\n   *\n   * @param prefixedKey - the key of the item\n   * @param size - optional, the byte size of the item\n   */\n  removeCacheItem(prefixedKey, size) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const item = yield _this7.getStorage().getItem(prefixedKey);\n      assert(item !== null, CacheErrorCode.NoCacheItem, `Key: ${prefixedKey}`);\n      const itemSize = size ?? JSON.parse(item).byteSize;\n      // first try to update the current size of the cache\n      yield _this7.decreaseCurrentSizeInBytes(itemSize);\n      // try to remove the item from cache\n      try {\n        yield _this7.getStorage().removeItem(prefixedKey);\n      } catch (removeItemError) {\n        // if some error happened, we need to rollback the current size\n        yield _this7.increaseCurrentSizeInBytes(itemSize);\n        logger.error(`Failed to remove item: ${removeItemError}`);\n      }\n    })();\n  }\n  /**\n   * produce a JSON object with meta-data and data value\n   * @param value - the value of the item\n   * @param options - optional, the specified meta-data\n   *\n   * @return - the item which has the meta-data and the value\n   */\n  fillCacheItem(key, value, options) {\n    const item = {\n      key,\n      data: value,\n      timestamp: getCurrentTime(),\n      visitedTime: getCurrentTime(),\n      priority: options.priority ?? 0,\n      expires: options.expires ?? 0,\n      type: typeof value,\n      byteSize: 0\n    };\n    // calculate byte size\n    item.byteSize = getByteLength(JSON.stringify(item));\n    // re-calculate using cache item with updated byteSize property\n    item.byteSize = getByteLength(JSON.stringify(item));\n    return item;\n  }\n  sanitizeConfig() {\n    if (this.config.itemMaxSize > this.config.capacityInBytes) {\n      logger.error('Invalid parameter: itemMaxSize. It should be smaller than capacityInBytes. Setting back to default.');\n      this.config.itemMaxSize = defaultConfig.itemMaxSize;\n    }\n    if (this.config.defaultPriority > 5 || this.config.defaultPriority < 1) {\n      logger.error('Invalid parameter: defaultPriority. It should be between 1 and 5. Setting back to default.');\n      this.config.defaultPriority = defaultConfig.defaultPriority;\n    }\n    if (Number(this.config.warningThreshold) > 1 || Number(this.config.warningThreshold) < 0) {\n      logger.error('Invalid parameter: warningThreshold. It should be between 0 and 1. Setting back to default.');\n      this.config.warningThreshold = defaultConfig.warningThreshold;\n    }\n    // Set 5MB limit\n    const cacheLimit = 5 * 1024 * 1024;\n    if (this.config.capacityInBytes > cacheLimit) {\n      logger.error('Cache Capacity should be less than 5MB. Setting back to default. Setting back to default.');\n      this.config.capacityInBytes = defaultConfig.capacityInBytes;\n    }\n  }\n  /**\n   * increase current size of the cache\n   *\n   * @param amount - the amount of the cache szie which need to be increased\n   */\n  increaseCurrentSizeInBytes(amount) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      const size = yield _this8.getCurrentCacheSize();\n      yield _this8.getStorage().setItem(getCurrentSizeKey(_this8.config.keyPrefix), (size + amount).toString());\n    })();\n  }\n  /**\n   * decrease current size of the cache\n   *\n   * @param amount - the amount of the cache size which needs to be decreased\n   */\n  decreaseCurrentSizeInBytes(amount) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      const size = yield _this9.getCurrentCacheSize();\n      yield _this9.getStorage().setItem(getCurrentSizeKey(_this9.config.keyPrefix), (size - amount).toString());\n    })();\n  }\n  /**\n   * update the visited time if item has been visited\n   *\n   * @param item - the item which need to be updated\n   * @param prefixedKey - the key of the item\n   *\n   * @return the updated item\n   */\n  updateVisitedTime(item, prefixedKey) {\n    var _this10 = this;\n    return _asyncToGenerator(function* () {\n      item.visitedTime = getCurrentTime();\n      yield _this10.getStorage().setItem(prefixedKey, JSON.stringify(item));\n      return item;\n    })();\n  }\n  /**\n   * put item into cache\n   *\n   * @param prefixedKey - the key of the item\n   * @param itemData - the value of the item\n   * @param itemSizeInBytes - the byte size of the item\n   */\n  setCacheItem(prefixedKey, item) {\n    var _this11 = this;\n    return _asyncToGenerator(function* () {\n      // first try to update the current size of the cache.\n      yield _this11.increaseCurrentSizeInBytes(item.byteSize);\n      // try to add the item into cache\n      try {\n        yield _this11.getStorage().setItem(prefixedKey, JSON.stringify(item));\n      } catch (setItemErr) {\n        // if some error happened, we need to rollback the current size\n        yield _this11.decreaseCurrentSizeInBytes(item.byteSize);\n        logger.error(`Failed to set item ${setItemErr}`);\n      }\n    })();\n  }\n  /**\n   * total space needed when poping out items\n   *\n   * @param itemSize\n   *\n   * @return total space needed\n   */\n  sizeToPop(itemSize) {\n    var _this12 = this;\n    return _asyncToGenerator(function* () {\n      const cur = yield _this12.getCurrentCacheSize();\n      const spaceItemNeed = cur + itemSize - _this12.config.capacityInBytes;\n      const cacheThresholdSpace = (1 - _this12.config.warningThreshold) * _this12.config.capacityInBytes;\n      return spaceItemNeed > cacheThresholdSpace ? spaceItemNeed : cacheThresholdSpace;\n    })();\n  }\n  /**\n   * see whether cache is full\n   *\n   * @param itemSize\n   *\n   * @return true if cache is full\n   */\n  isCacheFull(itemSize) {\n    var _this13 = this;\n    return _asyncToGenerator(function* () {\n      const cur = yield _this13.getCurrentCacheSize();\n      return itemSize + cur > _this13.config.capacityInBytes;\n    })();\n  }\n  /**\n   * get all the items we have, sort them by their priority,\n   * if priority is same, sort them by their last visited time\n   * pop out items from the low priority (5 is the lowest)\n   * @private\n   * @param keys - all the keys in this cache\n   * @param sizeToPop - the total size of the items which needed to be poped out\n   */\n  popOutItems(keys, sizeToPop) {\n    var _this14 = this;\n    return _asyncToGenerator(function* () {\n      const items = [];\n      let remainedSize = sizeToPop;\n      for (const key of keys) {\n        const val = yield _this14.getStorage().getItem(key);\n        if (val != null) {\n          const item = JSON.parse(val);\n          items.push(item);\n        }\n      }\n      // first compare priority\n      // then compare visited time\n      items.sort((a, b) => {\n        if (a.priority > b.priority) {\n          return -1;\n        } else if (a.priority < b.priority) {\n          return 1;\n        } else {\n          if (a.visitedTime < b.visitedTime) {\n            return -1;\n          } else return 1;\n        }\n      });\n      for (const item of items) {\n        // pop out items until we have enough room for new item\n        yield _this14.removeCacheItem(item.key, item.byteSize);\n        remainedSize -= item.byteSize;\n        if (remainedSize <= 0) {\n          return;\n        }\n      }\n    })();\n  }\n  /**\n   * Scan the storage and combine the following operations for efficiency\n   *   1. Clear out all expired keys owned by this cache, not including the size key.\n   *   2. Return the remaining keys.\n   *\n   * @return The remaining valid keys\n   */\n  clearInvalidAndGetRemainingKeys() {\n    var _this15 = this;\n    return _asyncToGenerator(function* () {\n      const remainingKeys = [];\n      const keys = yield _this15.getAllCacheKeys({\n        omitSizeKey: true\n      });\n      for (const key of keys) {\n        if (yield _this15.isExpired(key)) {\n          yield _this15.removeCacheItem(key);\n        } else {\n          remainingKeys.push(key);\n        }\n      }\n      return remainingKeys;\n    })();\n  }\n  /**\n   * clear the entire cache\n   * The cache will abort and output a warning if error occurs\n   * @return {Promise}\n   */\n  clear() {\n    var _this16 = this;\n    return _asyncToGenerator(function* () {\n      logger.debug(`Clear Cache`);\n      try {\n        const keys = yield _this16.getAllKeys();\n        for (const key of keys) {\n          const prefixedKey = `${_this16.config.keyPrefix}${key}`;\n          yield _this16.getStorage().removeItem(prefixedKey);\n        }\n      } catch (e) {\n        logger.warn(`clear failed! ${e}`);\n      }\n    })();\n  }\n}\nexport { StorageCacheCommon };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultConfig", "currentSizeKey", "getCurrentSizeKey", "getCurrentTime", "getByteLength", "assert", "CacheErrorCode", "logger", "StorageCacheCommon", "constructor", "config", "keyValueStorage", "sanitizeConfig", "getModuleName", "configure", "keyPrefix", "warn", "getCurrentCacheSize", "_this", "_asyncToGenerator", "size", "getStorage", "getItem", "setItem", "Number", "key", "value", "options", "_this2", "debug", "cacheItemOptions", "priority", "undefined", "defaultPriority", "expires", "defaultTTL", "prefixedKey", "item", "fillCacheItem", "byteSize", "itemMaxSize", "val", "removeCacheItem", "JSON", "parse", "isCacheFull", "validKeys", "clearInvalidAndGetRemainingKeys", "sizeToPop", "popOutItems", "setCacheItem", "e", "_this3", "cached", "isExpired", "updateVisitedTime", "data", "callback", "removeItem", "_this4", "getAllKeys", "_this5", "getAllCache<PERSON><PERSON>s", "_this6", "text", "NoCacheItem", "_this7", "itemSize", "decreaseCurrentSizeInBytes", "removeItemError", "increaseCurrentSizeInBytes", "error", "timestamp", "visitedTime", "type", "stringify", "capacityInBytes", "warningThreshold", "cacheLimit", "amount", "_this8", "toString", "_this9", "_this10", "_this11", "setItemErr", "_this12", "cur", "spaceItemNeed", "cacheThresholdSpace", "_this13", "keys", "_this14", "items", "remainedSize", "push", "sort", "a", "b", "_this15", "remainingKeys", "omitS<PERSON><PERSON>ey", "clear", "_this16"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Cache/StorageCacheCommon.mjs"], "sourcesContent": ["import { ConsoleLogger } from '../Logger/ConsoleLogger.mjs';\nimport { defaultConfig, currentSizeKey } from './constants.mjs';\nimport { getCurrentSizeKey, getCurrentTime, getByteLength } from './utils/cacheHelpers.mjs';\nimport { assert, CacheErrorCode } from './utils/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('StorageCache');\n/**\n * Initialization of the cache\n *\n */\nclass StorageCacheCommon {\n    /**\n     * Initialize the cache\n     *\n     * @param config - Custom configuration for this instance.\n     */\n    constructor({ config, keyValueStorage, }) {\n        this.config = {\n            ...defaultConfig,\n            ...config,\n        };\n        this.keyValueStorage = keyValueStorage;\n        this.sanitizeConfig();\n    }\n    getModuleName() {\n        return 'Cache';\n    }\n    /**\n     * Set custom configuration for the cache instance.\n     *\n     * @param config - customized configuration (without keyPrefix, which can't be changed)\n     *\n     * @return - the current configuration\n     */\n    configure(config) {\n        if (config) {\n            if (config.keyPrefix) {\n                logger.warn('keyPrefix can not be re-configured on an existing Cache instance.');\n            }\n            this.config = {\n                ...this.config,\n                ...config,\n            };\n        }\n        this.sanitizeConfig();\n        return this.config;\n    }\n    /**\n     * return the current size of the cache\n     * @return {Promise}\n     */\n    async getCurrentCacheSize() {\n        let size = await this.getStorage().getItem(getCurrentSizeKey(this.config.keyPrefix));\n        if (!size) {\n            await this.getStorage().setItem(getCurrentSizeKey(this.config.keyPrefix), '0');\n            size = '0';\n        }\n        return Number(size);\n    }\n    /**\n     * Set item into cache. You can put number, string, boolean or object.\n     * The cache will first check whether has the same key.\n     * If it has, it will delete the old item and then put the new item in\n     * The cache will pop out items if it is full\n     * You can specify the cache item options. The cache will abort and output a warning:\n     * If the key is invalid\n     * If the size of the item exceeds itemMaxSize.\n     * If the value is undefined\n     * If incorrect cache item configuration\n     * If error happened with browser storage\n     *\n     * @param {String} key - the key of the item\n     * @param {Object} value - the value of the item\n     * @param {Object} [options] - optional, the specified meta-data\n     *\n     * @return {Promise}\n     */\n    async setItem(key, value, options) {\n        logger.debug(`Set item: key is ${key}, value is ${value} with options: ${options}`);\n        if (!key || key === currentSizeKey) {\n            logger.warn(`Invalid key: should not be empty or reserved key: '${currentSizeKey}'`);\n            return;\n        }\n        if (typeof value === 'undefined') {\n            logger.warn(`The value of item should not be undefined!`);\n            return;\n        }\n        const cacheItemOptions = {\n            priority: options?.priority !== undefined\n                ? options.priority\n                : this.config.defaultPriority,\n            expires: options?.expires !== undefined\n                ? options.expires\n                : this.config.defaultTTL + getCurrentTime(),\n        };\n        if (cacheItemOptions.priority < 1 || cacheItemOptions.priority > 5) {\n            logger.warn(`Invalid parameter: priority due to out or range. It should be within 1 and 5.`);\n            return;\n        }\n        const prefixedKey = `${this.config.keyPrefix}${key}`;\n        const item = this.fillCacheItem(prefixedKey, value, cacheItemOptions);\n        // check whether this item is too big;\n        if (item.byteSize > this.config.itemMaxSize) {\n            logger.warn(`Item with key: ${key} you are trying to put into is too big!`);\n            return;\n        }\n        try {\n            // first look into the storage, if it exists, delete it.\n            const val = await this.getStorage().getItem(prefixedKey);\n            if (val) {\n                await this.removeCacheItem(prefixedKey, JSON.parse(val).byteSize);\n            }\n            // check whether the cache is full\n            if (await this.isCacheFull(item.byteSize)) {\n                const validKeys = await this.clearInvalidAndGetRemainingKeys();\n                if (await this.isCacheFull(item.byteSize)) {\n                    const sizeToPop = await this.sizeToPop(item.byteSize);\n                    await this.popOutItems(validKeys, sizeToPop);\n                }\n            }\n            // put item in the cache\n            return this.setCacheItem(prefixedKey, item);\n        }\n        catch (e) {\n            logger.warn(`setItem failed! ${e}`);\n        }\n    }\n    /**\n     * Get item from cache. It will return null if item doesn’t exist or it has been expired.\n     * If you specified callback function in the options,\n     * then the function will be executed if no such item in the cache\n     * and finally put the return value into cache.\n     * Please make sure the callback function will return the value you want to put into the cache.\n     * The cache will abort output a warning:\n     * If the key is invalid\n     * If error happened with AsyncStorage\n     *\n     * @param {String} key - the key of the item\n     * @param {Object} [options] - the options of callback function\n     *\n     * @return {Promise} - return a promise resolves to be the value of the item\n     */\n    async getItem(key, options) {\n        logger.debug(`Get item: key is ${key} with options ${options}`);\n        let cached;\n        if (!key || key === currentSizeKey) {\n            logger.warn(`Invalid key: should not be empty or reserved key: '${currentSizeKey}'`);\n            return null;\n        }\n        const prefixedKey = `${this.config.keyPrefix}${key}`;\n        try {\n            cached = await this.getStorage().getItem(prefixedKey);\n            if (cached != null) {\n                if (await this.isExpired(prefixedKey)) {\n                    // if expired, remove that item and return null\n                    await this.removeCacheItem(prefixedKey, JSON.parse(cached).byteSize);\n                }\n                else {\n                    // if not expired, update its visitedTime and return the value\n                    const item = await this.updateVisitedTime(JSON.parse(cached), prefixedKey);\n                    return item.data;\n                }\n            }\n            if (options?.callback) {\n                const val = options.callback();\n                if (val !== null) {\n                    await this.setItem(key, val, options);\n                }\n                return val;\n            }\n            return null;\n        }\n        catch (e) {\n            logger.warn(`getItem failed! ${e}`);\n            return null;\n        }\n    }\n    /**\n     * remove item from the cache\n     * The cache will abort output a warning:\n     * If error happened with AsyncStorage\n     * @param {String} key - the key of the item\n     * @return {Promise}\n     */\n    async removeItem(key) {\n        logger.debug(`Remove item: key is ${key}`);\n        if (!key || key === currentSizeKey) {\n            logger.warn(`Invalid key: should not be empty or reserved key: '${currentSizeKey}'`);\n            return;\n        }\n        const prefixedKey = `${this.config.keyPrefix}${key}`;\n        try {\n            const val = await this.getStorage().getItem(prefixedKey);\n            if (val) {\n                await this.removeCacheItem(prefixedKey, JSON.parse(val).byteSize);\n            }\n        }\n        catch (e) {\n            logger.warn(`removeItem failed! ${e}`);\n        }\n    }\n    /**\n     * Return all the keys owned by this cache.\n     * Will return an empty array if error occurred.\n     *\n     * @return {Promise}\n     */\n    async getAllKeys() {\n        try {\n            return await this.getAllCacheKeys();\n        }\n        catch (e) {\n            logger.warn(`getAllkeys failed! ${e}`);\n            return [];\n        }\n    }\n    getStorage() {\n        return this.keyValueStorage;\n    }\n    /**\n     * check whether item is expired\n     *\n     * @param key - the key of the item\n     *\n     * @return true if the item is expired.\n     */\n    async isExpired(key) {\n        const text = await this.getStorage().getItem(key);\n        assert(text !== null, CacheErrorCode.NoCacheItem, `Key: ${key}`);\n        const item = JSON.parse(text);\n        if (getCurrentTime() >= item.expires) {\n            return true;\n        }\n        return false;\n    }\n    /**\n     * delete item from cache\n     *\n     * @param prefixedKey - the key of the item\n     * @param size - optional, the byte size of the item\n     */\n    async removeCacheItem(prefixedKey, size) {\n        const item = await this.getStorage().getItem(prefixedKey);\n        assert(item !== null, CacheErrorCode.NoCacheItem, `Key: ${prefixedKey}`);\n        const itemSize = size ?? JSON.parse(item).byteSize;\n        // first try to update the current size of the cache\n        await this.decreaseCurrentSizeInBytes(itemSize);\n        // try to remove the item from cache\n        try {\n            await this.getStorage().removeItem(prefixedKey);\n        }\n        catch (removeItemError) {\n            // if some error happened, we need to rollback the current size\n            await this.increaseCurrentSizeInBytes(itemSize);\n            logger.error(`Failed to remove item: ${removeItemError}`);\n        }\n    }\n    /**\n     * produce a JSON object with meta-data and data value\n     * @param value - the value of the item\n     * @param options - optional, the specified meta-data\n     *\n     * @return - the item which has the meta-data and the value\n     */\n    fillCacheItem(key, value, options) {\n        const item = {\n            key,\n            data: value,\n            timestamp: getCurrentTime(),\n            visitedTime: getCurrentTime(),\n            priority: options.priority ?? 0,\n            expires: options.expires ?? 0,\n            type: typeof value,\n            byteSize: 0,\n        };\n        // calculate byte size\n        item.byteSize = getByteLength(JSON.stringify(item));\n        // re-calculate using cache item with updated byteSize property\n        item.byteSize = getByteLength(JSON.stringify(item));\n        return item;\n    }\n    sanitizeConfig() {\n        if (this.config.itemMaxSize > this.config.capacityInBytes) {\n            logger.error('Invalid parameter: itemMaxSize. It should be smaller than capacityInBytes. Setting back to default.');\n            this.config.itemMaxSize = defaultConfig.itemMaxSize;\n        }\n        if (this.config.defaultPriority > 5 || this.config.defaultPriority < 1) {\n            logger.error('Invalid parameter: defaultPriority. It should be between 1 and 5. Setting back to default.');\n            this.config.defaultPriority = defaultConfig.defaultPriority;\n        }\n        if (Number(this.config.warningThreshold) > 1 ||\n            Number(this.config.warningThreshold) < 0) {\n            logger.error('Invalid parameter: warningThreshold. It should be between 0 and 1. Setting back to default.');\n            this.config.warningThreshold = defaultConfig.warningThreshold;\n        }\n        // Set 5MB limit\n        const cacheLimit = 5 * 1024 * 1024;\n        if (this.config.capacityInBytes > cacheLimit) {\n            logger.error('Cache Capacity should be less than 5MB. Setting back to default. Setting back to default.');\n            this.config.capacityInBytes = defaultConfig.capacityInBytes;\n        }\n    }\n    /**\n     * increase current size of the cache\n     *\n     * @param amount - the amount of the cache szie which need to be increased\n     */\n    async increaseCurrentSizeInBytes(amount) {\n        const size = await this.getCurrentCacheSize();\n        await this.getStorage().setItem(getCurrentSizeKey(this.config.keyPrefix), (size + amount).toString());\n    }\n    /**\n     * decrease current size of the cache\n     *\n     * @param amount - the amount of the cache size which needs to be decreased\n     */\n    async decreaseCurrentSizeInBytes(amount) {\n        const size = await this.getCurrentCacheSize();\n        await this.getStorage().setItem(getCurrentSizeKey(this.config.keyPrefix), (size - amount).toString());\n    }\n    /**\n     * update the visited time if item has been visited\n     *\n     * @param item - the item which need to be updated\n     * @param prefixedKey - the key of the item\n     *\n     * @return the updated item\n     */\n    async updateVisitedTime(item, prefixedKey) {\n        item.visitedTime = getCurrentTime();\n        await this.getStorage().setItem(prefixedKey, JSON.stringify(item));\n        return item;\n    }\n    /**\n     * put item into cache\n     *\n     * @param prefixedKey - the key of the item\n     * @param itemData - the value of the item\n     * @param itemSizeInBytes - the byte size of the item\n     */\n    async setCacheItem(prefixedKey, item) {\n        // first try to update the current size of the cache.\n        await this.increaseCurrentSizeInBytes(item.byteSize);\n        // try to add the item into cache\n        try {\n            await this.getStorage().setItem(prefixedKey, JSON.stringify(item));\n        }\n        catch (setItemErr) {\n            // if some error happened, we need to rollback the current size\n            await this.decreaseCurrentSizeInBytes(item.byteSize);\n            logger.error(`Failed to set item ${setItemErr}`);\n        }\n    }\n    /**\n     * total space needed when poping out items\n     *\n     * @param itemSize\n     *\n     * @return total space needed\n     */\n    async sizeToPop(itemSize) {\n        const cur = await this.getCurrentCacheSize();\n        const spaceItemNeed = cur + itemSize - this.config.capacityInBytes;\n        const cacheThresholdSpace = (1 - this.config.warningThreshold) * this.config.capacityInBytes;\n        return spaceItemNeed > cacheThresholdSpace\n            ? spaceItemNeed\n            : cacheThresholdSpace;\n    }\n    /**\n     * see whether cache is full\n     *\n     * @param itemSize\n     *\n     * @return true if cache is full\n     */\n    async isCacheFull(itemSize) {\n        const cur = await this.getCurrentCacheSize();\n        return itemSize + cur > this.config.capacityInBytes;\n    }\n    /**\n     * get all the items we have, sort them by their priority,\n     * if priority is same, sort them by their last visited time\n     * pop out items from the low priority (5 is the lowest)\n     * @private\n     * @param keys - all the keys in this cache\n     * @param sizeToPop - the total size of the items which needed to be poped out\n     */\n    async popOutItems(keys, sizeToPop) {\n        const items = [];\n        let remainedSize = sizeToPop;\n        for (const key of keys) {\n            const val = await this.getStorage().getItem(key);\n            if (val != null) {\n                const item = JSON.parse(val);\n                items.push(item);\n            }\n        }\n        // first compare priority\n        // then compare visited time\n        items.sort((a, b) => {\n            if (a.priority > b.priority) {\n                return -1;\n            }\n            else if (a.priority < b.priority) {\n                return 1;\n            }\n            else {\n                if (a.visitedTime < b.visitedTime) {\n                    return -1;\n                }\n                else\n                    return 1;\n            }\n        });\n        for (const item of items) {\n            // pop out items until we have enough room for new item\n            await this.removeCacheItem(item.key, item.byteSize);\n            remainedSize -= item.byteSize;\n            if (remainedSize <= 0) {\n                return;\n            }\n        }\n    }\n    /**\n     * Scan the storage and combine the following operations for efficiency\n     *   1. Clear out all expired keys owned by this cache, not including the size key.\n     *   2. Return the remaining keys.\n     *\n     * @return The remaining valid keys\n     */\n    async clearInvalidAndGetRemainingKeys() {\n        const remainingKeys = [];\n        const keys = await this.getAllCacheKeys({\n            omitSizeKey: true,\n        });\n        for (const key of keys) {\n            if (await this.isExpired(key)) {\n                await this.removeCacheItem(key);\n            }\n            else {\n                remainingKeys.push(key);\n            }\n        }\n        return remainingKeys;\n    }\n    /**\n     * clear the entire cache\n     * The cache will abort and output a warning if error occurs\n     * @return {Promise}\n     */\n    async clear() {\n        logger.debug(`Clear Cache`);\n        try {\n            const keys = await this.getAllKeys();\n            for (const key of keys) {\n                const prefixedKey = `${this.config.keyPrefix}${key}`;\n                await this.getStorage().removeItem(prefixedKey);\n            }\n        }\n        catch (e) {\n            logger.warn(`clear failed! ${e}`);\n        }\n    }\n}\n\nexport { StorageCacheCommon };\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,aAAa,EAAEC,cAAc,QAAQ,iBAAiB;AAC/D,SAASC,iBAAiB,EAAEC,cAAc,EAAEC,aAAa,QAAQ,0BAA0B;AAC3F,SAASC,MAAM,EAAEC,cAAc,QAAQ,0BAA0B;;AAEjE;AACA;AACA,MAAMC,MAAM,GAAG,IAAIR,aAAa,CAAC,cAAc,CAAC;AAChD;AACA;AACA;AACA;AACA,MAAMS,kBAAkB,CAAC;EACrB;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAAC;IAAEC,MAAM;IAAEC;EAAiB,CAAC,EAAE;IACtC,IAAI,CAACD,MAAM,GAAG;MACV,GAAGV,aAAa;MAChB,GAAGU;IACP,CAAC;IACD,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,cAAc,CAAC,CAAC;EACzB;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAO,OAAO;EAClB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACJ,MAAM,EAAE;IACd,IAAIA,MAAM,EAAE;MACR,IAAIA,MAAM,CAACK,SAAS,EAAE;QAClBR,MAAM,CAACS,IAAI,CAAC,mEAAmE,CAAC;MACpF;MACA,IAAI,CAACN,MAAM,GAAG;QACV,GAAG,IAAI,CAACA,MAAM;QACd,GAAGA;MACP,CAAC;IACL;IACA,IAAI,CAACE,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACF,MAAM;EACtB;EACA;AACJ;AACA;AACA;EACUO,mBAAmBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACxB,IAAIC,IAAI,SAASF,KAAI,CAACG,UAAU,CAAC,CAAC,CAACC,OAAO,CAACpB,iBAAiB,CAACgB,KAAI,CAACR,MAAM,CAACK,SAAS,CAAC,CAAC;MACpF,IAAI,CAACK,IAAI,EAAE;QACP,MAAMF,KAAI,CAACG,UAAU,CAAC,CAAC,CAACE,OAAO,CAACrB,iBAAiB,CAACgB,KAAI,CAACR,MAAM,CAACK,SAAS,CAAC,EAAE,GAAG,CAAC;QAC9EK,IAAI,GAAG,GAAG;MACd;MACA,OAAOI,MAAM,CAACJ,IAAI,CAAC;IAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUG,OAAOA,CAACE,GAAG,EAAEC,KAAK,EAAEC,OAAO,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAT,iBAAA;MAC/BZ,MAAM,CAACsB,KAAK,CAAC,oBAAoBJ,GAAG,cAAcC,KAAK,kBAAkBC,OAAO,EAAE,CAAC;MACnF,IAAI,CAACF,GAAG,IAAIA,GAAG,KAAKxB,cAAc,EAAE;QAChCM,MAAM,CAACS,IAAI,CAAC,sDAAsDf,cAAc,GAAG,CAAC;QACpF;MACJ;MACA,IAAI,OAAOyB,KAAK,KAAK,WAAW,EAAE;QAC9BnB,MAAM,CAACS,IAAI,CAAC,4CAA4C,CAAC;QACzD;MACJ;MACA,MAAMc,gBAAgB,GAAG;QACrBC,QAAQ,EAAEJ,OAAO,EAAEI,QAAQ,KAAKC,SAAS,GACnCL,OAAO,CAACI,QAAQ,GAChBH,MAAI,CAAClB,MAAM,CAACuB,eAAe;QACjCC,OAAO,EAAEP,OAAO,EAAEO,OAAO,KAAKF,SAAS,GACjCL,OAAO,CAACO,OAAO,GACfN,MAAI,CAAClB,MAAM,CAACyB,UAAU,GAAGhC,cAAc,CAAC;MAClD,CAAC;MACD,IAAI2B,gBAAgB,CAACC,QAAQ,GAAG,CAAC,IAAID,gBAAgB,CAACC,QAAQ,GAAG,CAAC,EAAE;QAChExB,MAAM,CAACS,IAAI,CAAC,+EAA+E,CAAC;QAC5F;MACJ;MACA,MAAMoB,WAAW,GAAG,GAAGR,MAAI,CAAClB,MAAM,CAACK,SAAS,GAAGU,GAAG,EAAE;MACpD,MAAMY,IAAI,GAAGT,MAAI,CAACU,aAAa,CAACF,WAAW,EAAEV,KAAK,EAAEI,gBAAgB,CAAC;MACrE;MACA,IAAIO,IAAI,CAACE,QAAQ,GAAGX,MAAI,CAAClB,MAAM,CAAC8B,WAAW,EAAE;QACzCjC,MAAM,CAACS,IAAI,CAAC,kBAAkBS,GAAG,yCAAyC,CAAC;QAC3E;MACJ;MACA,IAAI;QACA;QACA,MAAMgB,GAAG,SAASb,MAAI,CAACP,UAAU,CAAC,CAAC,CAACC,OAAO,CAACc,WAAW,CAAC;QACxD,IAAIK,GAAG,EAAE;UACL,MAAMb,MAAI,CAACc,eAAe,CAACN,WAAW,EAAEO,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC,CAACF,QAAQ,CAAC;QACrE;QACA;QACA,UAAUX,MAAI,CAACiB,WAAW,CAACR,IAAI,CAACE,QAAQ,CAAC,EAAE;UACvC,MAAMO,SAAS,SAASlB,MAAI,CAACmB,+BAA+B,CAAC,CAAC;UAC9D,UAAUnB,MAAI,CAACiB,WAAW,CAACR,IAAI,CAACE,QAAQ,CAAC,EAAE;YACvC,MAAMS,SAAS,SAASpB,MAAI,CAACoB,SAAS,CAACX,IAAI,CAACE,QAAQ,CAAC;YACrD,MAAMX,MAAI,CAACqB,WAAW,CAACH,SAAS,EAAEE,SAAS,CAAC;UAChD;QACJ;QACA;QACA,OAAOpB,MAAI,CAACsB,YAAY,CAACd,WAAW,EAAEC,IAAI,CAAC;MAC/C,CAAC,CACD,OAAOc,CAAC,EAAE;QACN5C,MAAM,CAACS,IAAI,CAAC,mBAAmBmC,CAAC,EAAE,CAAC;MACvC;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACU7B,OAAOA,CAACG,GAAG,EAAEE,OAAO,EAAE;IAAA,IAAAyB,MAAA;IAAA,OAAAjC,iBAAA;MACxBZ,MAAM,CAACsB,KAAK,CAAC,oBAAoBJ,GAAG,iBAAiBE,OAAO,EAAE,CAAC;MAC/D,IAAI0B,MAAM;MACV,IAAI,CAAC5B,GAAG,IAAIA,GAAG,KAAKxB,cAAc,EAAE;QAChCM,MAAM,CAACS,IAAI,CAAC,sDAAsDf,cAAc,GAAG,CAAC;QACpF,OAAO,IAAI;MACf;MACA,MAAMmC,WAAW,GAAG,GAAGgB,MAAI,CAAC1C,MAAM,CAACK,SAAS,GAAGU,GAAG,EAAE;MACpD,IAAI;QACA4B,MAAM,SAASD,MAAI,CAAC/B,UAAU,CAAC,CAAC,CAACC,OAAO,CAACc,WAAW,CAAC;QACrD,IAAIiB,MAAM,IAAI,IAAI,EAAE;UAChB,UAAUD,MAAI,CAACE,SAAS,CAAClB,WAAW,CAAC,EAAE;YACnC;YACA,MAAMgB,MAAI,CAACV,eAAe,CAACN,WAAW,EAAEO,IAAI,CAACC,KAAK,CAACS,MAAM,CAAC,CAACd,QAAQ,CAAC;UACxE,CAAC,MACI;YACD;YACA,MAAMF,IAAI,SAASe,MAAI,CAACG,iBAAiB,CAACZ,IAAI,CAACC,KAAK,CAACS,MAAM,CAAC,EAAEjB,WAAW,CAAC;YAC1E,OAAOC,IAAI,CAACmB,IAAI;UACpB;QACJ;QACA,IAAI7B,OAAO,EAAE8B,QAAQ,EAAE;UACnB,MAAMhB,GAAG,GAAGd,OAAO,CAAC8B,QAAQ,CAAC,CAAC;UAC9B,IAAIhB,GAAG,KAAK,IAAI,EAAE;YACd,MAAMW,MAAI,CAAC7B,OAAO,CAACE,GAAG,EAAEgB,GAAG,EAAEd,OAAO,CAAC;UACzC;UACA,OAAOc,GAAG;QACd;QACA,OAAO,IAAI;MACf,CAAC,CACD,OAAOU,CAAC,EAAE;QACN5C,MAAM,CAACS,IAAI,CAAC,mBAAmBmC,CAAC,EAAE,CAAC;QACnC,OAAO,IAAI;MACf;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUO,UAAUA,CAACjC,GAAG,EAAE;IAAA,IAAAkC,MAAA;IAAA,OAAAxC,iBAAA;MAClBZ,MAAM,CAACsB,KAAK,CAAC,uBAAuBJ,GAAG,EAAE,CAAC;MAC1C,IAAI,CAACA,GAAG,IAAIA,GAAG,KAAKxB,cAAc,EAAE;QAChCM,MAAM,CAACS,IAAI,CAAC,sDAAsDf,cAAc,GAAG,CAAC;QACpF;MACJ;MACA,MAAMmC,WAAW,GAAG,GAAGuB,MAAI,CAACjD,MAAM,CAACK,SAAS,GAAGU,GAAG,EAAE;MACpD,IAAI;QACA,MAAMgB,GAAG,SAASkB,MAAI,CAACtC,UAAU,CAAC,CAAC,CAACC,OAAO,CAACc,WAAW,CAAC;QACxD,IAAIK,GAAG,EAAE;UACL,MAAMkB,MAAI,CAACjB,eAAe,CAACN,WAAW,EAAEO,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC,CAACF,QAAQ,CAAC;QACrE;MACJ,CAAC,CACD,OAAOY,CAAC,EAAE;QACN5C,MAAM,CAACS,IAAI,CAAC,sBAAsBmC,CAAC,EAAE,CAAC;MAC1C;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;EACUS,UAAUA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA1C,iBAAA;MACf,IAAI;QACA,aAAa0C,MAAI,CAACC,eAAe,CAAC,CAAC;MACvC,CAAC,CACD,OAAOX,CAAC,EAAE;QACN5C,MAAM,CAACS,IAAI,CAAC,sBAAsBmC,CAAC,EAAE,CAAC;QACtC,OAAO,EAAE;MACb;IAAC;EACL;EACA9B,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACV,eAAe;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACU2C,SAASA,CAAC7B,GAAG,EAAE;IAAA,IAAAsC,MAAA;IAAA,OAAA5C,iBAAA;MACjB,MAAM6C,IAAI,SAASD,MAAI,CAAC1C,UAAU,CAAC,CAAC,CAACC,OAAO,CAACG,GAAG,CAAC;MACjDpB,MAAM,CAAC2D,IAAI,KAAK,IAAI,EAAE1D,cAAc,CAAC2D,WAAW,EAAE,QAAQxC,GAAG,EAAE,CAAC;MAChE,MAAMY,IAAI,GAAGM,IAAI,CAACC,KAAK,CAACoB,IAAI,CAAC;MAC7B,IAAI7D,cAAc,CAAC,CAAC,IAAIkC,IAAI,CAACH,OAAO,EAAE;QAClC,OAAO,IAAI;MACf;MACA,OAAO,KAAK;IAAC;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;EACUQ,eAAeA,CAACN,WAAW,EAAEhB,IAAI,EAAE;IAAA,IAAA8C,MAAA;IAAA,OAAA/C,iBAAA;MACrC,MAAMkB,IAAI,SAAS6B,MAAI,CAAC7C,UAAU,CAAC,CAAC,CAACC,OAAO,CAACc,WAAW,CAAC;MACzD/B,MAAM,CAACgC,IAAI,KAAK,IAAI,EAAE/B,cAAc,CAAC2D,WAAW,EAAE,QAAQ7B,WAAW,EAAE,CAAC;MACxE,MAAM+B,QAAQ,GAAG/C,IAAI,IAAIuB,IAAI,CAACC,KAAK,CAACP,IAAI,CAAC,CAACE,QAAQ;MAClD;MACA,MAAM2B,MAAI,CAACE,0BAA0B,CAACD,QAAQ,CAAC;MAC/C;MACA,IAAI;QACA,MAAMD,MAAI,CAAC7C,UAAU,CAAC,CAAC,CAACqC,UAAU,CAACtB,WAAW,CAAC;MACnD,CAAC,CACD,OAAOiC,eAAe,EAAE;QACpB;QACA,MAAMH,MAAI,CAACI,0BAA0B,CAACH,QAAQ,CAAC;QAC/C5D,MAAM,CAACgE,KAAK,CAAC,0BAA0BF,eAAe,EAAE,CAAC;MAC7D;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI/B,aAAaA,CAACb,GAAG,EAAEC,KAAK,EAAEC,OAAO,EAAE;IAC/B,MAAMU,IAAI,GAAG;MACTZ,GAAG;MACH+B,IAAI,EAAE9B,KAAK;MACX8C,SAAS,EAAErE,cAAc,CAAC,CAAC;MAC3BsE,WAAW,EAAEtE,cAAc,CAAC,CAAC;MAC7B4B,QAAQ,EAAEJ,OAAO,CAACI,QAAQ,IAAI,CAAC;MAC/BG,OAAO,EAAEP,OAAO,CAACO,OAAO,IAAI,CAAC;MAC7BwC,IAAI,EAAE,OAAOhD,KAAK;MAClBa,QAAQ,EAAE;IACd,CAAC;IACD;IACAF,IAAI,CAACE,QAAQ,GAAGnC,aAAa,CAACuC,IAAI,CAACgC,SAAS,CAACtC,IAAI,CAAC,CAAC;IACnD;IACAA,IAAI,CAACE,QAAQ,GAAGnC,aAAa,CAACuC,IAAI,CAACgC,SAAS,CAACtC,IAAI,CAAC,CAAC;IACnD,OAAOA,IAAI;EACf;EACAzB,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACF,MAAM,CAAC8B,WAAW,GAAG,IAAI,CAAC9B,MAAM,CAACkE,eAAe,EAAE;MACvDrE,MAAM,CAACgE,KAAK,CAAC,qGAAqG,CAAC;MACnH,IAAI,CAAC7D,MAAM,CAAC8B,WAAW,GAAGxC,aAAa,CAACwC,WAAW;IACvD;IACA,IAAI,IAAI,CAAC9B,MAAM,CAACuB,eAAe,GAAG,CAAC,IAAI,IAAI,CAACvB,MAAM,CAACuB,eAAe,GAAG,CAAC,EAAE;MACpE1B,MAAM,CAACgE,KAAK,CAAC,4FAA4F,CAAC;MAC1G,IAAI,CAAC7D,MAAM,CAACuB,eAAe,GAAGjC,aAAa,CAACiC,eAAe;IAC/D;IACA,IAAIT,MAAM,CAAC,IAAI,CAACd,MAAM,CAACmE,gBAAgB,CAAC,GAAG,CAAC,IACxCrD,MAAM,CAAC,IAAI,CAACd,MAAM,CAACmE,gBAAgB,CAAC,GAAG,CAAC,EAAE;MAC1CtE,MAAM,CAACgE,KAAK,CAAC,6FAA6F,CAAC;MAC3G,IAAI,CAAC7D,MAAM,CAACmE,gBAAgB,GAAG7E,aAAa,CAAC6E,gBAAgB;IACjE;IACA;IACA,MAAMC,UAAU,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;IAClC,IAAI,IAAI,CAACpE,MAAM,CAACkE,eAAe,GAAGE,UAAU,EAAE;MAC1CvE,MAAM,CAACgE,KAAK,CAAC,2FAA2F,CAAC;MACzG,IAAI,CAAC7D,MAAM,CAACkE,eAAe,GAAG5E,aAAa,CAAC4E,eAAe;IAC/D;EACJ;EACA;AACJ;AACA;AACA;AACA;EACUN,0BAA0BA,CAACS,MAAM,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA7D,iBAAA;MACrC,MAAMC,IAAI,SAAS4D,MAAI,CAAC/D,mBAAmB,CAAC,CAAC;MAC7C,MAAM+D,MAAI,CAAC3D,UAAU,CAAC,CAAC,CAACE,OAAO,CAACrB,iBAAiB,CAAC8E,MAAI,CAACtE,MAAM,CAACK,SAAS,CAAC,EAAE,CAACK,IAAI,GAAG2D,MAAM,EAAEE,QAAQ,CAAC,CAAC,CAAC;IAAC;EAC1G;EACA;AACJ;AACA;AACA;AACA;EACUb,0BAA0BA,CAACW,MAAM,EAAE;IAAA,IAAAG,MAAA;IAAA,OAAA/D,iBAAA;MACrC,MAAMC,IAAI,SAAS8D,MAAI,CAACjE,mBAAmB,CAAC,CAAC;MAC7C,MAAMiE,MAAI,CAAC7D,UAAU,CAAC,CAAC,CAACE,OAAO,CAACrB,iBAAiB,CAACgF,MAAI,CAACxE,MAAM,CAACK,SAAS,CAAC,EAAE,CAACK,IAAI,GAAG2D,MAAM,EAAEE,QAAQ,CAAC,CAAC,CAAC;IAAC;EAC1G;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACU1B,iBAAiBA,CAAClB,IAAI,EAAED,WAAW,EAAE;IAAA,IAAA+C,OAAA;IAAA,OAAAhE,iBAAA;MACvCkB,IAAI,CAACoC,WAAW,GAAGtE,cAAc,CAAC,CAAC;MACnC,MAAMgF,OAAI,CAAC9D,UAAU,CAAC,CAAC,CAACE,OAAO,CAACa,WAAW,EAAEO,IAAI,CAACgC,SAAS,CAACtC,IAAI,CAAC,CAAC;MAClE,OAAOA,IAAI;IAAC;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUa,YAAYA,CAACd,WAAW,EAAEC,IAAI,EAAE;IAAA,IAAA+C,OAAA;IAAA,OAAAjE,iBAAA;MAClC;MACA,MAAMiE,OAAI,CAACd,0BAA0B,CAACjC,IAAI,CAACE,QAAQ,CAAC;MACpD;MACA,IAAI;QACA,MAAM6C,OAAI,CAAC/D,UAAU,CAAC,CAAC,CAACE,OAAO,CAACa,WAAW,EAAEO,IAAI,CAACgC,SAAS,CAACtC,IAAI,CAAC,CAAC;MACtE,CAAC,CACD,OAAOgD,UAAU,EAAE;QACf;QACA,MAAMD,OAAI,CAAChB,0BAA0B,CAAC/B,IAAI,CAACE,QAAQ,CAAC;QACpDhC,MAAM,CAACgE,KAAK,CAAC,sBAAsBc,UAAU,EAAE,CAAC;MACpD;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUrC,SAASA,CAACmB,QAAQ,EAAE;IAAA,IAAAmB,OAAA;IAAA,OAAAnE,iBAAA;MACtB,MAAMoE,GAAG,SAASD,OAAI,CAACrE,mBAAmB,CAAC,CAAC;MAC5C,MAAMuE,aAAa,GAAGD,GAAG,GAAGpB,QAAQ,GAAGmB,OAAI,CAAC5E,MAAM,CAACkE,eAAe;MAClE,MAAMa,mBAAmB,GAAG,CAAC,CAAC,GAAGH,OAAI,CAAC5E,MAAM,CAACmE,gBAAgB,IAAIS,OAAI,CAAC5E,MAAM,CAACkE,eAAe;MAC5F,OAAOY,aAAa,GAAGC,mBAAmB,GACpCD,aAAa,GACbC,mBAAmB;IAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACU5C,WAAWA,CAACsB,QAAQ,EAAE;IAAA,IAAAuB,OAAA;IAAA,OAAAvE,iBAAA;MACxB,MAAMoE,GAAG,SAASG,OAAI,CAACzE,mBAAmB,CAAC,CAAC;MAC5C,OAAOkD,QAAQ,GAAGoB,GAAG,GAAGG,OAAI,CAAChF,MAAM,CAACkE,eAAe;IAAC;EACxD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACU3B,WAAWA,CAAC0C,IAAI,EAAE3C,SAAS,EAAE;IAAA,IAAA4C,OAAA;IAAA,OAAAzE,iBAAA;MAC/B,MAAM0E,KAAK,GAAG,EAAE;MAChB,IAAIC,YAAY,GAAG9C,SAAS;MAC5B,KAAK,MAAMvB,GAAG,IAAIkE,IAAI,EAAE;QACpB,MAAMlD,GAAG,SAASmD,OAAI,CAACvE,UAAU,CAAC,CAAC,CAACC,OAAO,CAACG,GAAG,CAAC;QAChD,IAAIgB,GAAG,IAAI,IAAI,EAAE;UACb,MAAMJ,IAAI,GAAGM,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC;UAC5BoD,KAAK,CAACE,IAAI,CAAC1D,IAAI,CAAC;QACpB;MACJ;MACA;MACA;MACAwD,KAAK,CAACG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACjB,IAAID,CAAC,CAAClE,QAAQ,GAAGmE,CAAC,CAACnE,QAAQ,EAAE;UACzB,OAAO,CAAC,CAAC;QACb,CAAC,MACI,IAAIkE,CAAC,CAAClE,QAAQ,GAAGmE,CAAC,CAACnE,QAAQ,EAAE;UAC9B,OAAO,CAAC;QACZ,CAAC,MACI;UACD,IAAIkE,CAAC,CAACxB,WAAW,GAAGyB,CAAC,CAACzB,WAAW,EAAE;YAC/B,OAAO,CAAC,CAAC;UACb,CAAC,MAEG,OAAO,CAAC;QAChB;MACJ,CAAC,CAAC;MACF,KAAK,MAAMpC,IAAI,IAAIwD,KAAK,EAAE;QACtB;QACA,MAAMD,OAAI,CAAClD,eAAe,CAACL,IAAI,CAACZ,GAAG,EAAEY,IAAI,CAACE,QAAQ,CAAC;QACnDuD,YAAY,IAAIzD,IAAI,CAACE,QAAQ;QAC7B,IAAIuD,YAAY,IAAI,CAAC,EAAE;UACnB;QACJ;MACJ;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACU/C,+BAA+BA,CAAA,EAAG;IAAA,IAAAoD,OAAA;IAAA,OAAAhF,iBAAA;MACpC,MAAMiF,aAAa,GAAG,EAAE;MACxB,MAAMT,IAAI,SAASQ,OAAI,CAACrC,eAAe,CAAC;QACpCuC,WAAW,EAAE;MACjB,CAAC,CAAC;MACF,KAAK,MAAM5E,GAAG,IAAIkE,IAAI,EAAE;QACpB,UAAUQ,OAAI,CAAC7C,SAAS,CAAC7B,GAAG,CAAC,EAAE;UAC3B,MAAM0E,OAAI,CAACzD,eAAe,CAACjB,GAAG,CAAC;QACnC,CAAC,MACI;UACD2E,aAAa,CAACL,IAAI,CAACtE,GAAG,CAAC;QAC3B;MACJ;MACA,OAAO2E,aAAa;IAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;EACUE,KAAKA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAApF,iBAAA;MACVZ,MAAM,CAACsB,KAAK,CAAC,aAAa,CAAC;MAC3B,IAAI;QACA,MAAM8D,IAAI,SAASY,OAAI,CAAC3C,UAAU,CAAC,CAAC;QACpC,KAAK,MAAMnC,GAAG,IAAIkE,IAAI,EAAE;UACpB,MAAMvD,WAAW,GAAG,GAAGmE,OAAI,CAAC7F,MAAM,CAACK,SAAS,GAAGU,GAAG,EAAE;UACpD,MAAM8E,OAAI,CAAClF,UAAU,CAAC,CAAC,CAACqC,UAAU,CAACtB,WAAW,CAAC;QACnD;MACJ,CAAC,CACD,OAAOe,CAAC,EAAE;QACN5C,MAAM,CAACS,IAAI,CAAC,iBAAiBmC,CAAC,EAAE,CAAC;MACrC;IAAC;EACL;AACJ;AAEA,SAAS3C,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}