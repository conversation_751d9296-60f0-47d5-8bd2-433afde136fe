{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AuthError } from '../../../errors/AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * It will retry the function if the error is a `ResourceNotFoundException` and\n * will clean the device keys stored in the storage mechanism.\n *\n */\nfunction retryOnResourceNotFoundException(_x, _x2, _x3, _x4) {\n  return _retryOnResourceNotFoundException.apply(this, arguments);\n}\nfunction _retryOnResourceNotFoundException() {\n  _retryOnResourceNotFoundException = _asyncToGenerator(function* (func, args, username, tokenOrchestrator) {\n    try {\n      return yield func(...args);\n    } catch (error) {\n      if (error instanceof AuthError && error.name === 'ResourceNotFoundException' && error.message.includes('Device does not exist.')) {\n        yield tokenOrchestrator.clearDeviceMetadata(username);\n        return func(...args);\n      }\n      throw error;\n    }\n  });\n  return _retryOnResourceNotFoundException.apply(this, arguments);\n}\nexport { retryOnResourceNotFoundException };", "map": {"version": 3, "names": ["<PERSON>th<PERSON><PERSON><PERSON>", "retryOnResourceNotFoundException", "_x", "_x2", "_x3", "_x4", "_retryOnResourceNotFoundException", "apply", "arguments", "_asyncToGenerator", "func", "args", "username", "tokenOrchestrator", "error", "name", "message", "includes", "clearDeviceMetadata"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/retryOnResourceNotFoundException.mjs"], "sourcesContent": ["import { AuthError } from '../../../errors/AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * It will retry the function if the error is a `ResourceNotFoundException` and\n * will clean the device keys stored in the storage mechanism.\n *\n */\nasync function retryOnResourceNotFoundException(func, args, username, tokenOrchestrator) {\n    try {\n        return await func(...args);\n    }\n    catch (error) {\n        if (error instanceof AuthError &&\n            error.name === 'ResourceNotFoundException' &&\n            error.message.includes('Device does not exist.')) {\n            await tokenOrchestrator.clearDeviceMetadata(username);\n            return func(...args);\n        }\n        throw error;\n    }\n}\n\nexport { retryOnResourceNotFoundException };\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,+BAA+B;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AAJA,SAKeC,gCAAgCA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,iCAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,kCAAA;EAAAA,iCAAA,GAAAG,iBAAA,CAA/C,WAAgDC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,iBAAiB,EAAE;IACrF,IAAI;MACA,aAAaH,IAAI,CAAC,GAAGC,IAAI,CAAC;IAC9B,CAAC,CACD,OAAOG,KAAK,EAAE;MACV,IAAIA,KAAK,YAAYd,SAAS,IAC1Bc,KAAK,CAACC,IAAI,KAAK,2BAA2B,IAC1CD,KAAK,CAACE,OAAO,CAACC,QAAQ,CAAC,wBAAwB,CAAC,EAAE;QAClD,MAAMJ,iBAAiB,CAACK,mBAAmB,CAACN,QAAQ,CAAC;QACrD,OAAOF,IAAI,CAAC,GAAGC,IAAI,CAAC;MACxB;MACA,MAAMG,KAAK;IACf;EACJ,CAAC;EAAA,OAAAR,iCAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASP,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}