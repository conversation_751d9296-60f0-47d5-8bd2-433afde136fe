{"ast": null, "code": "'use strict';\n\n/** @type {import('./gOPD')} */\nmodule.exports = Object.getOwnPropertyDescriptor;", "map": {"version": 3, "names": ["module", "exports", "Object", "getOwnPropertyDescriptor"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/gopd/gOPD.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./gOPD')} */\nmodule.exports = Object.getOwnPropertyDescriptor;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,MAAM,CAACC,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}