{"ast": null, "code": "const shadows = {\n  small: {\n    value: {\n      offsetX: '0px',\n      offsetY: '2px',\n      blurRadius: '4px',\n      color: '{colors.shadow.tertiary.value}'\n    }\n  },\n  medium: {\n    value: {\n      offsetX: '0px',\n      offsetY: '2px',\n      blurRadius: '6px',\n      color: '{colors.shadow.secondary.value}'\n    }\n  },\n  large: {\n    value: {\n      offsetX: '0px',\n      offsetY: '4px',\n      blurRadius: '12px',\n      color: '{colors.shadow.primary.value}'\n    }\n  }\n};\nexport { shadows };", "map": {"version": 3, "names": ["shadows", "small", "value", "offsetX", "offsetY", "blurRadius", "color", "medium", "large"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/shadows.mjs"], "sourcesContent": ["const shadows = {\n    small: {\n        value: {\n            offsetX: '0px',\n            offsetY: '2px',\n            blurRadius: '4px',\n            color: '{colors.shadow.tertiary.value}',\n        },\n    },\n    medium: {\n        value: {\n            offsetX: '0px',\n            offsetY: '2px',\n            blurRadius: '6px',\n            color: '{colors.shadow.secondary.value}',\n        },\n    },\n    large: {\n        value: {\n            offsetX: '0px',\n            offsetY: '4px',\n            blurRadius: '12px',\n            color: '{colors.shadow.primary.value}',\n        },\n    },\n};\n\nexport { shadows };\n"], "mappings": "AAAA,MAAMA,OAAO,GAAG;EACZC,KAAK,EAAE;IACHC,KAAK,EAAE;MACHC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,KAAK,EAAE;IACX;EACJ,CAAC;EACDC,MAAM,EAAE;IACJL,KAAK,EAAE;MACHC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,KAAK,EAAE;IACX;EACJ,CAAC;EACDE,KAAK,EAAE;IACHN,KAAK,EAAE;MACHC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE;IACX;EACJ;AACJ,CAAC;AAED,SAASN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}