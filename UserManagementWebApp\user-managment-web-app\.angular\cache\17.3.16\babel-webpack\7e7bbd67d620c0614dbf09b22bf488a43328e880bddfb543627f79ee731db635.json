{"ast": null, "code": "const enDict = {\n  'Account recovery requires verified contact information': 'Account recovery requires verified contact information',\n  'Add your Profile': 'Add your Profile',\n  'Add your Website': 'Add your Website',\n  'Authenticator App (TOTP)': 'Authenticator App (TOTP)',\n  'Back to Sign In': 'Back to Sign In',\n  'Change Password': 'Change Password',\n  Changing: 'Changing',\n  Code: 'Code',\n  'Confirm Email Code': 'Confirm Email Code',\n  'Confirm Password': 'Confirm Password',\n  'Confirm Sign Up': 'Confirm Sign Up',\n  'Confirm SMS Code': 'Confirm SMS Code',\n  'Confirm MFA Code': 'Confirm MFA Code',\n  'Confirm TOTP Code': 'Confirm TOTP Code',\n  Confirm: 'Confirm',\n  'Confirmation Code': 'Confirmation Code',\n  Confirming: 'Confirming',\n  'Create a new account': 'Create a new account',\n  'Create Account': 'Create Account',\n  'Creating Account': 'Creating Account',\n  'Dismiss alert': 'Dismiss alert',\n  Email: 'Email',\n  'Email Message': 'Email Message',\n  'Enter your Birthdate': 'Enter your Birthdate',\n  'Enter your code': 'Enter your code',\n  'Enter your Confirmation Code': 'Enter your Confirmation Code',\n  'Enter your Email': 'Enter your Email',\n  'Enter your Family Name': 'Enter your Family Name',\n  'Enter your Given Name': 'Enter your Given Name',\n  'Enter your Middle Name': 'Enter your Middle Name',\n  'Enter your Name': 'Enter your Name',\n  'Enter your Nickname': 'Enter your Nickname',\n  'Enter your Password': 'Enter your Password',\n  'Enter your phone number': 'Enter your phone number',\n  'Enter your Preferred Username': 'Enter your Preferred Username',\n  'Enter your username': 'Enter your username',\n  'Forgot password?': 'Forgot password?',\n  'Forgot your password?': 'Forgot your password?',\n  'Hide password': 'Hide password',\n  'It may take a minute to arrive': 'It may take a minute to arrive',\n  Loading: 'Loading',\n  'Multi-Factor Authentication': 'Multi-Factor Authentication',\n  'Multi-Factor Authentication Setup': 'Multi-Factor Authentication Setup',\n  'New password': 'New password',\n  or: 'or',\n  Password: 'Password',\n  'Phone Number': 'Phone Number',\n  'Please confirm your Password': 'Please confirm your Password',\n  'Resend Code': 'Resend Code',\n  'Reset your password': 'Reset your password',\n  'Reset your Password': 'Reset your Password',\n  'Select MFA Type': 'Select MFA Type',\n  'Send code': 'Send code',\n  'Send Code': 'Send Code',\n  Sending: 'Sending',\n  'Setup Email': 'Setup Email',\n  'Setup TOTP': 'Setup TOTP',\n  'Show password': 'Show password',\n  'Sign in to your account': 'Sign in to your account',\n  'Sign In with Amazon': 'Sign In with Amazon',\n  'Sign In with Apple': 'Sign In with Apple',\n  'Sign In with Facebook': 'Sign In with Facebook',\n  'Sign In with Google': 'Sign In with Google',\n  'Sign in': 'Sign in',\n  'Sign In': 'Sign In',\n  'Signing in': 'Signing in',\n  Skip: 'Skip',\n  Submit: 'Submit',\n  Submitting: 'Submitting',\n  'Text Message (SMS)': 'Text Message (SMS)',\n  Username: 'Username',\n  'Verify Contact': 'Verify Contact',\n  Verify: 'Verify',\n  'We Emailed You': 'We Emailed You',\n  'We Sent A Code': 'We Sent A Code',\n  'We Texted You': 'We Texted You',\n  'Your code is on the way. To log in, enter the code we emailed to': 'Your code is on the way. To log in, enter the code we emailed to',\n  'Your code is on the way. To log in, enter the code we sent you': 'Your code is on the way. To log in, enter the code we sent you',\n  'Your code is on the way. To log in, enter the code we texted to': 'Your code is on the way. To log in, enter the code we texted to'\n};\nexport { enDict };", "map": {"version": 3, "names": ["enDict", "Changing", "Code", "Confirm", "Confirming", "Email", "Loading", "or", "Password", "Sending", "<PERSON><PERSON>", "Submit", "Submitting", "Username", "Verify"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/i18n/dictionaries/authenticator/en.mjs"], "sourcesContent": ["const enDict = {\n    'Account recovery requires verified contact information': 'Account recovery requires verified contact information',\n    'Add your Profile': 'Add your Profile',\n    'Add your Website': 'Add your Website',\n    'Authenticator App (TOTP)': 'Authenticator App (TOTP)',\n    'Back to Sign In': 'Back to Sign In',\n    'Change Password': 'Change Password',\n    Changing: 'Changing',\n    Code: 'Code',\n    'Confirm Email Code': 'Confirm Email Code',\n    'Confirm Password': 'Confirm Password',\n    'Confirm Sign Up': 'Confirm Sign Up',\n    'Confirm SMS Code': 'Confirm SMS Code',\n    'Confirm MFA Code': 'Confirm MFA Code',\n    'Confirm TOTP Code': 'Confirm TOTP Code',\n    Confirm: 'Confirm',\n    'Confirmation Code': 'Confirmation Code',\n    Confirming: 'Confirming',\n    'Create a new account': 'Create a new account',\n    'Create Account': 'Create Account',\n    'Creating Account': 'Creating Account',\n    'Dismiss alert': 'Dismiss alert',\n    Email: 'Email',\n    'Email Message': 'Email Message',\n    'Enter your Birthdate': 'Enter your Birthdate',\n    'Enter your code': 'Enter your code',\n    'Enter your Confirmation Code': 'Enter your Confirmation Code',\n    'Enter your Email': 'Enter your Email',\n    'Enter your Family Name': 'Enter your Family Name',\n    'Enter your Given Name': 'Enter your Given Name',\n    'Enter your Middle Name': 'Enter your Middle Name',\n    'Enter your Name': 'Enter your Name',\n    'Enter your Nickname': 'Enter your Nickname',\n    'Enter your Password': 'Enter your Password',\n    'Enter your phone number': 'Enter your phone number',\n    'Enter your Preferred Username': 'Enter your Preferred Username',\n    'Enter your username': 'Enter your username',\n    'Forgot password?': 'Forgot password?',\n    'Forgot your password?': 'Forgot your password?',\n    'Hide password': 'Hide password',\n    'It may take a minute to arrive': 'It may take a minute to arrive',\n    Loading: 'Loading',\n    'Multi-Factor Authentication': 'Multi-Factor Authentication',\n    'Multi-Factor Authentication Setup': 'Multi-Factor Authentication Setup',\n    'New password': 'New password',\n    or: 'or',\n    Password: 'Password',\n    'Phone Number': 'Phone Number',\n    'Please confirm your Password': 'Please confirm your Password',\n    'Resend Code': 'Resend Code',\n    'Reset your password': 'Reset your password',\n    'Reset your Password': 'Reset your Password',\n    'Select MFA Type': 'Select MFA Type',\n    'Send code': 'Send code',\n    'Send Code': 'Send Code',\n    Sending: 'Sending',\n    'Setup Email': 'Setup Email',\n    'Setup TOTP': 'Setup TOTP',\n    'Show password': 'Show password',\n    'Sign in to your account': 'Sign in to your account',\n    'Sign In with Amazon': 'Sign In with Amazon',\n    'Sign In with Apple': 'Sign In with Apple',\n    'Sign In with Facebook': 'Sign In with Facebook',\n    'Sign In with Google': 'Sign In with Google',\n    'Sign in': 'Sign in',\n    'Sign In': 'Sign In',\n    'Signing in': 'Signing in',\n    Skip: 'Skip',\n    Submit: 'Submit',\n    Submitting: 'Submitting',\n    'Text Message (SMS)': 'Text Message (SMS)',\n    Username: 'Username',\n    'Verify Contact': 'Verify Contact',\n    Verify: 'Verify',\n    'We Emailed You': 'We Emailed You',\n    'We Sent A Code': 'We Sent A Code',\n    'We Texted You': 'We Texted You',\n    'Your code is on the way. To log in, enter the code we emailed to': 'Your code is on the way. To log in, enter the code we emailed to',\n    'Your code is on the way. To log in, enter the code we sent you': 'Your code is on the way. To log in, enter the code we sent you',\n    'Your code is on the way. To log in, enter the code we texted to': 'Your code is on the way. To log in, enter the code we texted to',\n};\n\nexport { enDict };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACX,wDAAwD,EAAE,wDAAwD;EAClH,kBAAkB,EAAE,kBAAkB;EACtC,kBAAkB,EAAE,kBAAkB;EACtC,0BAA0B,EAAE,0BAA0B;EACtD,iBAAiB,EAAE,iBAAiB;EACpC,iBAAiB,EAAE,iBAAiB;EACpCC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,MAAM;EACZ,oBAAoB,EAAE,oBAAoB;EAC1C,kBAAkB,EAAE,kBAAkB;EACtC,iBAAiB,EAAE,iBAAiB;EACpC,kBAAkB,EAAE,kBAAkB;EACtC,kBAAkB,EAAE,kBAAkB;EACtC,mBAAmB,EAAE,mBAAmB;EACxCC,OAAO,EAAE,SAAS;EAClB,mBAAmB,EAAE,mBAAmB;EACxCC,UAAU,EAAE,YAAY;EACxB,sBAAsB,EAAE,sBAAsB;EAC9C,gBAAgB,EAAE,gBAAgB;EAClC,kBAAkB,EAAE,kBAAkB;EACtC,eAAe,EAAE,eAAe;EAChCC,KAAK,EAAE,OAAO;EACd,eAAe,EAAE,eAAe;EAChC,sBAAsB,EAAE,sBAAsB;EAC9C,iBAAiB,EAAE,iBAAiB;EACpC,8BAA8B,EAAE,8BAA8B;EAC9D,kBAAkB,EAAE,kBAAkB;EACtC,wBAAwB,EAAE,wBAAwB;EAClD,uBAAuB,EAAE,uBAAuB;EAChD,wBAAwB,EAAE,wBAAwB;EAClD,iBAAiB,EAAE,iBAAiB;EACpC,qBAAqB,EAAE,qBAAqB;EAC5C,qBAAqB,EAAE,qBAAqB;EAC5C,yBAAyB,EAAE,yBAAyB;EACpD,+BAA+B,EAAE,+BAA+B;EAChE,qBAAqB,EAAE,qBAAqB;EAC5C,kBAAkB,EAAE,kBAAkB;EACtC,uBAAuB,EAAE,uBAAuB;EAChD,eAAe,EAAE,eAAe;EAChC,gCAAgC,EAAE,gCAAgC;EAClEC,OAAO,EAAE,SAAS;EAClB,6BAA6B,EAAE,6BAA6B;EAC5D,mCAAmC,EAAE,mCAAmC;EACxE,cAAc,EAAE,cAAc;EAC9BC,EAAE,EAAE,IAAI;EACRC,QAAQ,EAAE,UAAU;EACpB,cAAc,EAAE,cAAc;EAC9B,8BAA8B,EAAE,8BAA8B;EAC9D,aAAa,EAAE,aAAa;EAC5B,qBAAqB,EAAE,qBAAqB;EAC5C,qBAAqB,EAAE,qBAAqB;EAC5C,iBAAiB,EAAE,iBAAiB;EACpC,WAAW,EAAE,WAAW;EACxB,WAAW,EAAE,WAAW;EACxBC,OAAO,EAAE,SAAS;EAClB,aAAa,EAAE,aAAa;EAC5B,YAAY,EAAE,YAAY;EAC1B,eAAe,EAAE,eAAe;EAChC,yBAAyB,EAAE,yBAAyB;EACpD,qBAAqB,EAAE,qBAAqB;EAC5C,oBAAoB,EAAE,oBAAoB;EAC1C,uBAAuB,EAAE,uBAAuB;EAChD,qBAAqB,EAAE,qBAAqB;EAC5C,SAAS,EAAE,SAAS;EACpB,SAAS,EAAE,SAAS;EACpB,YAAY,EAAE,YAAY;EAC1BC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,YAAY;EACxB,oBAAoB,EAAE,oBAAoB;EAC1CC,QAAQ,EAAE,UAAU;EACpB,gBAAgB,EAAE,gBAAgB;EAClCC,MAAM,EAAE,QAAQ;EAChB,gBAAgB,EAAE,gBAAgB;EAClC,gBAAgB,EAAE,gBAAgB;EAClC,eAAe,EAAE,eAAe;EAChC,kEAAkE,EAAE,kEAAkE;EACtI,gEAAgE,EAAE,gEAAgE;EAClI,iEAAiE,EAAE;AACvE,CAAC;AAED,SAASd,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}