import { ColDef, GridOptions } from "ag-grid-community";
import { ActionableGridColumnConfig } from "src/app/core/models/actionable-grid-column-config";
import { ExtendedJsonSchema } from "src/app/shared/models/extended-json-schema";

export class ColDefOptions {
    public columnConfig: ActionableGridColumnConfig;
    public colDef: ColDef;
    public baseProperty?: ExtendedJsonSchema;
    public gridOptions?: GridOptions;
    public projectVariables?: any[];
    public projectVersionId?: number;
    public projectId?: number;
    public allowAddNewRow?: boolean;
    public parentColumn?: ActionableGridColumnConfig;
    public baseSchema?: ExtendedJsonSchema;
    public bypassObjects = true; // For actionable grids because we don't support objects now, but it is being supported in forms
    public showFloatingFilter = true;

    constructor(init?: Partial<ColDefOptions>) {
        Object.assign(this, init);
    }
}
