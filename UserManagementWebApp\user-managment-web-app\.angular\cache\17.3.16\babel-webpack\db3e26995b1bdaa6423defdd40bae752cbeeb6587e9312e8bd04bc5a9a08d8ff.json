{"ast": null, "code": "'use strict';\n\n// modified from https://github.com/es-shims/es6-shim\nvar objectKeys = require('object-keys');\nvar hasSymbols = require('has-symbols/shams')();\nvar callBound = require('call-bound');\nvar $Object = require('es-object-atoms');\nvar $push = callBound('Array.prototype.push');\nvar $propIsEnumerable = callBound('Object.prototype.propertyIsEnumerable');\nvar originalGetSymbols = hasSymbols ? $Object.getOwnPropertySymbols : null;\n\n// eslint-disable-next-line no-unused-vars\nmodule.exports = function assign(target, source1) {\n  if (target == null) {\n    throw new TypeError('target must be an object');\n  }\n  var to = $Object(target); // step 1\n  if (arguments.length === 1) {\n    return to; // step 2\n  }\n  for (var s = 1; s < arguments.length; ++s) {\n    var from = $Object(arguments[s]); // step 3.a.i\n\n    // step 3.a.ii:\n    var keys = objectKeys(from);\n    var getSymbols = hasSymbols && ($Object.getOwnPropertySymbols || originalGetSymbols);\n    if (getSymbols) {\n      var syms = getSymbols(from);\n      for (var j = 0; j < syms.length; ++j) {\n        var key = syms[j];\n        if ($propIsEnumerable(from, key)) {\n          $push(keys, key);\n        }\n      }\n    }\n\n    // step 3.a.iii:\n    for (var i = 0; i < keys.length; ++i) {\n      var nextKey = keys[i];\n      if ($propIsEnumerable(from, nextKey)) {\n        // step 3.a.iii.2\n        var propValue = from[nextKey]; // step 3.a.iii.2.a\n        to[nextKey] = propValue; // step 3.a.iii.2.b\n      }\n    }\n  }\n  return to; // step 4\n};", "map": {"version": 3, "names": ["objectKeys", "require", "hasSymbols", "callBound", "$Object", "$push", "$propIsEnumerable", "originalGetSymbols", "getOwnPropertySymbols", "module", "exports", "assign", "target", "source1", "TypeError", "to", "arguments", "length", "s", "from", "keys", "getSymbols", "syms", "j", "key", "i", "<PERSON><PERSON><PERSON>", "propValue"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/object.assign/implementation.js"], "sourcesContent": ["'use strict';\n\n// modified from https://github.com/es-shims/es6-shim\nvar objectKeys = require('object-keys');\nvar hasSymbols = require('has-symbols/shams')();\nvar callBound = require('call-bound');\nvar $Object = require('es-object-atoms');\nvar $push = callBound('Array.prototype.push');\nvar $propIsEnumerable = callBound('Object.prototype.propertyIsEnumerable');\nvar originalGetSymbols = hasSymbols ? $Object.getOwnPropertySymbols : null;\n\n// eslint-disable-next-line no-unused-vars\nmodule.exports = function assign(target, source1) {\n\tif (target == null) { throw new TypeError('target must be an object'); }\n\tvar to = $Object(target); // step 1\n\tif (arguments.length === 1) {\n\t\treturn to; // step 2\n\t}\n\tfor (var s = 1; s < arguments.length; ++s) {\n\t\tvar from = $Object(arguments[s]); // step 3.a.i\n\n\t\t// step 3.a.ii:\n\t\tvar keys = objectKeys(from);\n\t\tvar getSymbols = hasSymbols && ($Object.getOwnPropertySymbols || originalGetSymbols);\n\t\tif (getSymbols) {\n\t\t\tvar syms = getSymbols(from);\n\t\t\tfor (var j = 0; j < syms.length; ++j) {\n\t\t\t\tvar key = syms[j];\n\t\t\t\tif ($propIsEnumerable(from, key)) {\n\t\t\t\t\t$push(keys, key);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// step 3.a.iii:\n\t\tfor (var i = 0; i < keys.length; ++i) {\n\t\t\tvar nextKey = keys[i];\n\t\t\tif ($propIsEnumerable(from, nextKey)) { // step 3.a.iii.2\n\t\t\t\tvar propValue = from[nextKey]; // step 3.a.iii.2.a\n\t\t\t\tto[nextKey] = propValue; // step 3.a.iii.2.b\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to; // step 4\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA,IAAIA,UAAU,GAAGC,OAAO,CAAC,aAAa,CAAC;AACvC,IAAIC,UAAU,GAAGD,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;AAC/C,IAAIE,SAAS,GAAGF,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIG,OAAO,GAAGH,OAAO,CAAC,iBAAiB,CAAC;AACxC,IAAII,KAAK,GAAGF,SAAS,CAAC,sBAAsB,CAAC;AAC7C,IAAIG,iBAAiB,GAAGH,SAAS,CAAC,uCAAuC,CAAC;AAC1E,IAAII,kBAAkB,GAAGL,UAAU,GAAGE,OAAO,CAACI,qBAAqB,GAAG,IAAI;;AAE1E;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,MAAMA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACjD,IAAID,MAAM,IAAI,IAAI,EAAE;IAAE,MAAM,IAAIE,SAAS,CAAC,0BAA0B,CAAC;EAAE;EACvE,IAAIC,EAAE,GAAGX,OAAO,CAACQ,MAAM,CAAC,CAAC,CAAC;EAC1B,IAAII,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;IAC3B,OAAOF,EAAE,CAAC,CAAC;EACZ;EACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACC,MAAM,EAAE,EAAEC,CAAC,EAAE;IAC1C,IAAIC,IAAI,GAAGf,OAAO,CAACY,SAAS,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElC;IACA,IAAIE,IAAI,GAAGpB,UAAU,CAACmB,IAAI,CAAC;IAC3B,IAAIE,UAAU,GAAGnB,UAAU,KAAKE,OAAO,CAACI,qBAAqB,IAAID,kBAAkB,CAAC;IACpF,IAAIc,UAAU,EAAE;MACf,IAAIC,IAAI,GAAGD,UAAU,CAACF,IAAI,CAAC;MAC3B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACL,MAAM,EAAE,EAAEM,CAAC,EAAE;QACrC,IAAIC,GAAG,GAAGF,IAAI,CAACC,CAAC,CAAC;QACjB,IAAIjB,iBAAiB,CAACa,IAAI,EAAEK,GAAG,CAAC,EAAE;UACjCnB,KAAK,CAACe,IAAI,EAAEI,GAAG,CAAC;QACjB;MACD;IACD;;IAEA;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,CAACH,MAAM,EAAE,EAAEQ,CAAC,EAAE;MACrC,IAAIC,OAAO,GAAGN,IAAI,CAACK,CAAC,CAAC;MACrB,IAAInB,iBAAiB,CAACa,IAAI,EAAEO,OAAO,CAAC,EAAE;QAAE;QACvC,IAAIC,SAAS,GAAGR,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC;QAC/BX,EAAE,CAACW,OAAO,CAAC,GAAGC,SAAS,CAAC,CAAC;MAC1B;IACD;EACD;EAEA,OAAOZ,EAAE,CAAC,CAAC;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}