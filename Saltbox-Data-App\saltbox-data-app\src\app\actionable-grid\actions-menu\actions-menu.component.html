<div class="grid-toolbar">
  @if(this.calendarViews?.length > 0 && this.undoActions?.length > 0) {
  <p-toolbar [style]="{width: 'auto'}" class="desktop-grid-tb">
    <div class="p-toolbar-group-start">
      <h3><i class="pi pi-chart-line"></i> {{reportInfo?.description}}</h3>
    </div>
    <div class="p-toolbar-group-end">
      <span class="tb-menu-desktop inline-flex align-items-center">
        @if (this.validationResultsCount) {
        <p-button icon="fa-solid fa-exclamation" severity="warning" pTooltip="Show Errors/Warnings"
          tooltipPosition="top" (onClick)="onClickShowValidationResults()" styleClass="p-overlay-badge mr-3"
          [badge]="validationResultsCount" badgeClass="p-badge-warning"></p-button>
        }

        @if (this.showColumnsOptions) {
        <p-toggleButton [(ngModel)]="columnsOptions" [onLabel]="'Columns Options On'" [offLabel]="'Columns Options Off'"
          (onChange)="onColumnsOptionsChange($event)" />
        }

        @if (!this.previewMode) {
        <p-button [icon]="isFavorite ? 'pi pi-star-fill' : 'pi pi-star'"
          [pTooltip]="isFavorite ? 'Remove Favorite' : 'Add Favorite'" tooltipPosition="top"
          (onClick)="onUpdateUserFavorite()"></p-button>
        }

        <span pTooltip="{{disabled? 'Project is locked' : ''}}" tooltipPosition="top"
          class="inline-flex align-items-center">

          @if (!this.reportInfo?.allowAddNewRow || disabled) {
          <p-button pTooltip="Add Row (permission required)" tooltipPosition="top" icon="pi pi-plus"
            (onClick)="onClickShowFormEditor(false)"
            [disabled]="!this.reportInfo?.allowAddNewRow || disabled"></p-button>
          } @else {
          <p-button icon="pi pi-plus" (onClick)="onClickShowFormEditor(false)"
            [disabled]="!this.reportInfo?.allowAddNewRow || disabled"></p-button>
          }

          <p-splitButton [icon]="undoActions[0]?.icon" (onClick)="undoActions[0]?.command()"
            [disabled]="!checkHasChanges || disabled" appendTo="body" [model]="undoActions"></p-splitButton>

          @if (this.agentChatFlag) {
          <p-button icon="fa-regular fa-comment" pTooltip="Launch Agent Chat" (onClick)="onClickShowAgent()"></p-button>
          }

          <app-grid-layout-manager [reportInfo]="reportInfo" [agGrid]="agGrid" [disabled]="disabled" [layouts]="layouts"
            (selectedLayoutChange)="onSelectedLayoutChange($event)">
          </app-grid-layout-manager>

          @if (calendarViewFlag && reportInfo?.enableCalendarView) {
          <p-menu #calendar_view [popup]="true" [model]="this.calendarViews" appendTo="body">
            <ng-template pTemplate="item" let-item>
              <div pRipple class="p-ripple cursor-pointer p-element flex items-center p-menu-item-link p-3"
                [ngClass]="{'bg-blue-100': (item.value === selectedView)}">
                <span class="text-base text-base-text">
                  <i class="p-menuitem-icon" [class]="item.icon"
                    [ngClass]="{'vertical-align-top': (item.icon === 'sb sb-icon-slice')}"></i>
                </span>
                <span class="ml-2 p-menuitem-text text-sm text-base-text vertical-align-middle line-height-3"
                  [ngClass]="{'font-bold': (item.value === selectedView)}">{{ item.label }}</span>
              </div>
            </ng-template>
          </p-menu>
          <p-button [rounded]="true" (click)="calendar_view.toggle($event)" [disabled]="disabled" pTooltip="Switch View"
            tooltipPosition="top" styleClass="min-w-0">
            <span class="inline-flex align-items-center">
              <i class="{{selectedViewIcon}} vertical-align-middle"></i>
              <span class="ml-2">{{selectedView}}</span>
              <i class="pi pi-angle-down vertical-align-middle ml-2"></i>
            </span>
          </p-button>
          }

          @if (checkHasSlicers) {
          <p-button icon="pi pi-filter" (onClick)="onClickShowRefiner()" [disabled]="disabled"
            pTooltip="Filter Your Data" tooltipPosition="top"></p-button>
          }

          <p-button [rounded]="true" (onClick)="menu_download.toggle($event)" [disabled]="disabled" pTooltip="Export"
            tooltipPosition="top" styleClass="min-w-0">
            <span><i class="pi pi-file-export vertical-align-bottom"></i></span>
            <span><i class="pi pi-angle-down vertical-align-middle ml-1"></i></span>
          </p-button>
          <p-menu #menu_download [popup]="true" [model]="this.exportItems" appendTo="body"></p-menu>

          <p-button icon="pi pi-refresh" (onClick)="onClickRefreshGridData()" [disabled]="disabled" pTooltip="Refresh"
            tooltipPosition="top"></p-button>

          <p-button icon="pi pi-save" (onClick)="onClickSaveChanges()" [disabled]="!checkHasChanges || disabled"
            pTooltip="Save" tooltipPosition="top"></p-button>
        </span>
      </span>
    </div>
  </p-toolbar>
  }
</div>
<div class="mobile-app-menu">
  <span class="report-title"><i class="pi pi-chart-line"></i> {{reportInfo?.description}}</span>
  <p-menu #menu [model]="mobileActions" [popup]="true" appendTo="body"></p-menu>
  <p-button class="mobile-menu-tb" type="button" (onClick)="menu.toggle($event)" icon="pi pi-ellipsis-h"></p-button>
</div>