{"ast": null, "code": "/** Federated IDPs that Authenticator supports */\nvar FederatedIdentityProviders;\n(function (FederatedIdentityProviders) {\n  FederatedIdentityProviders[\"Apple\"] = \"Apple\";\n  FederatedIdentityProviders[\"Amazon\"] = \"Amazon\";\n  FederatedIdentityProviders[\"Facebook\"] = \"Facebook\";\n  FederatedIdentityProviders[\"Google\"] = \"Google\";\n})(FederatedIdentityProviders || (FederatedIdentityProviders = {}));\n/**\n * Cognito user contact method types that have not been verified as valid\n */\nvar UnverifiedContactMethodType;\n(function (UnverifiedContactMethodType) {\n  UnverifiedContactMethodType[\"Email\"] = \"email\";\n  UnverifiedContactMethodType[\"PhoneNumber\"] = \"phone_number\";\n})(UnverifiedContactMethodType || (UnverifiedContactMethodType = {}));\nexport { FederatedIdentityProviders, UnverifiedContactMethodType };", "map": {"version": 3, "names": ["FederatedIdentityProviders", "UnverifiedContactMethodType"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/types/authenticator/user.mjs"], "sourcesContent": ["/** Federated IDPs that Authenticator supports */\nvar FederatedIdentityProviders;\n(function (FederatedIdentityProviders) {\n    FederatedIdentityProviders[\"Apple\"] = \"Apple\";\n    FederatedIdentityProviders[\"Amazon\"] = \"Amazon\";\n    FederatedIdentityProviders[\"Facebook\"] = \"Facebook\";\n    FederatedIdentityProviders[\"Google\"] = \"Google\";\n})(FederatedIdentityProviders || (FederatedIdentityProviders = {}));\n/**\n * Cognito user contact method types that have not been verified as valid\n */\nvar UnverifiedContactMethodType;\n(function (UnverifiedContactMethodType) {\n    UnverifiedContactMethodType[\"Email\"] = \"email\";\n    UnverifiedContactMethodType[\"PhoneNumber\"] = \"phone_number\";\n})(UnverifiedContactMethodType || (UnverifiedContactMethodType = {}));\n\nexport { FederatedIdentityProviders, UnverifiedContactMethodType };\n"], "mappings": "AAAA;AACA,IAAIA,0BAA0B;AAC9B,CAAC,UAAUA,0BAA0B,EAAE;EACnCA,0BAA0B,CAAC,OAAO,CAAC,GAAG,OAAO;EAC7CA,0BAA0B,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC/CA,0BAA0B,CAAC,UAAU,CAAC,GAAG,UAAU;EACnDA,0BAA0B,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACnD,CAAC,EAAEA,0BAA0B,KAAKA,0BAA0B,GAAG,CAAC,CAAC,CAAC,CAAC;AACnE;AACA;AACA;AACA,IAAIC,2BAA2B;AAC/B,CAAC,UAAUA,2BAA2B,EAAE;EACpCA,2BAA2B,CAAC,OAAO,CAAC,GAAG,OAAO;EAC9CA,2BAA2B,CAAC,aAAa,CAAC,GAAG,cAAc;AAC/D,CAAC,EAAEA,2BAA2B,KAAKA,2BAA2B,GAAG,CAAC,CAAC,CAAC,CAAC;AAErE,SAASD,0BAA0B,EAAEC,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}