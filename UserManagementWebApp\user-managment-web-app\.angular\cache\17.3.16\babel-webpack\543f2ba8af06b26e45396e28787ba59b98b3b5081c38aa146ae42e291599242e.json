{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction defaultState() {\n  return {\n    active: false\n  };\n}\nconst autoSignInReducer = (state, action) => {\n  switch (action.type) {\n    case 'SET_USERNAME':\n      return {\n        ...state,\n        username: action.value\n      };\n    case 'SET_SESSION':\n      return {\n        ...state,\n        session: action.value\n      };\n    case 'START':\n      return {\n        ...state,\n        active: true\n      };\n    case 'RESET':\n      return defaultState();\n    default:\n      return state;\n  }\n};\nconst createAutoSignInStore = reducer => {\n  let currentState = reducer(defaultState(), {\n    type: 'RESET'\n  });\n  return {\n    getState: () => currentState,\n    dispatch: action => {\n      currentState = reducer(currentState, action);\n    }\n  };\n};\nconst autoSignInStore = createAutoSignInStore(autoSignInReducer);\nexport { autoSignInStore };", "map": {"version": 3, "names": ["defaultState", "active", "autoSignInReducer", "state", "action", "type", "username", "value", "session", "createAutoSignInStore", "reducer", "currentState", "getState", "dispatch", "autoSignInStore"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/utils/store/autoSignInStore.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction defaultState() {\n    return {\n        active: false,\n    };\n}\nconst autoSignInReducer = (state, action) => {\n    switch (action.type) {\n        case 'SET_USERNAME':\n            return {\n                ...state,\n                username: action.value,\n            };\n        case 'SET_SESSION':\n            return {\n                ...state,\n                session: action.value,\n            };\n        case 'START':\n            return {\n                ...state,\n                active: true,\n            };\n        case 'RESET':\n            return defaultState();\n        default:\n            return state;\n    }\n};\nconst createAutoSignInStore = (reducer) => {\n    let currentState = reducer(defaultState(), { type: 'RESET' });\n    return {\n        getState: () => currentState,\n        dispatch: action => {\n            currentState = reducer(currentState, action);\n        },\n    };\n};\nconst autoSignInStore = createAutoSignInStore(autoSignInReducer);\n\nexport { autoSignInStore };\n"], "mappings": "AAAA;AACA;AACA,SAASA,YAAYA,CAAA,EAAG;EACpB,OAAO;IACHC,MAAM,EAAE;EACZ,CAAC;AACL;AACA,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACzC,QAAQA,MAAM,CAACC,IAAI;IACf,KAAK,cAAc;MACf,OAAO;QACH,GAAGF,KAAK;QACRG,QAAQ,EAAEF,MAAM,CAACG;MACrB,CAAC;IACL,KAAK,aAAa;MACd,OAAO;QACH,GAAGJ,KAAK;QACRK,OAAO,EAAEJ,MAAM,CAACG;MACpB,CAAC;IACL,KAAK,OAAO;MACR,OAAO;QACH,GAAGJ,KAAK;QACRF,MAAM,EAAE;MACZ,CAAC;IACL,KAAK,OAAO;MACR,OAAOD,YAAY,CAAC,CAAC;IACzB;MACI,OAAOG,KAAK;EACpB;AACJ,CAAC;AACD,MAAMM,qBAAqB,GAAIC,OAAO,IAAK;EACvC,IAAIC,YAAY,GAAGD,OAAO,CAACV,YAAY,CAAC,CAAC,EAAE;IAAEK,IAAI,EAAE;EAAQ,CAAC,CAAC;EAC7D,OAAO;IACHO,QAAQ,EAAEA,CAAA,KAAMD,YAAY;IAC5BE,QAAQ,EAAET,MAAM,IAAI;MAChBO,YAAY,GAAGD,OAAO,CAACC,YAAY,EAAEP,MAAM,CAAC;IAChD;EACJ,CAAC;AACL,CAAC;AACD,MAAMU,eAAe,GAAGL,qBAAqB,CAACP,iBAAiB,CAAC;AAEhE,SAASY,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}