{"ast": null, "code": "export { signUp } from './providers/cognito/apis/signUp.mjs';\nexport { resetPassword } from './providers/cognito/apis/resetPassword.mjs';\nexport { confirmResetPassword } from './providers/cognito/apis/confirmResetPassword.mjs';\nexport { signIn } from './providers/cognito/apis/signIn.mjs';\nexport { resendSignUpCode } from './providers/cognito/apis/resendSignUpCode.mjs';\nexport { confirmSignUp } from './providers/cognito/apis/confirmSignUp.mjs';\nexport { confirmSignIn } from './providers/cognito/apis/confirmSignIn.mjs';\nexport { updateMFAPreference } from './providers/cognito/apis/updateMFAPreference.mjs';\nexport { fetchMFAPreference } from './providers/cognito/apis/fetchMFAPreference.mjs';\nexport { verifyTOTPSetup } from './providers/cognito/apis/verifyTOTPSetup.mjs';\nexport { updatePassword } from './providers/cognito/apis/updatePassword.mjs';\nexport { setUpTOTP } from './providers/cognito/apis/setUpTOTP.mjs';\nexport { updateUserAttributes } from './providers/cognito/apis/updateUserAttributes.mjs';\nexport { updateUserAttribute } from './providers/cognito/apis/updateUserAttribute.mjs';\nexport { getCurrentUser } from './providers/cognito/apis/getCurrentUser.mjs';\nexport { confirmUserAttribute } from './providers/cognito/apis/confirmUserAttribute.mjs';\nexport { signInWithRedirect } from './providers/cognito/apis/signInWithRedirect.mjs';\nexport { fetchUserAttributes } from './providers/cognito/apis/fetchUserAttributes.mjs';\nexport { signOut } from './providers/cognito/apis/signOut.mjs';\nexport { sendUserAttributeVerificationCode } from './providers/cognito/apis/sendUserAttributeVerificationCode.mjs';\nexport { deleteUserAttributes } from './providers/cognito/apis/deleteUserAttributes.mjs';\nexport { deleteUser } from './providers/cognito/apis/deleteUser.mjs';\nexport { rememberDevice } from './providers/cognito/apis/rememberDevice.mjs';\nexport { forgetDevice } from './providers/cognito/apis/forgetDevice.mjs';\nexport { fetchDevices } from './providers/cognito/apis/fetchDevices.mjs';\nexport { autoSignIn } from './providers/cognito/apis/autoSignIn.mjs';\nexport { decodeJWT, fetchAuthSession } from '@aws-amplify/core';\nimport './providers/cognito/credentialsProvider/IdentityIdStore.mjs';\nimport './providers/cognito/credentialsProvider/credentialsProvider.mjs';\nimport './providers/cognito/utils/refreshAuthTokens.mjs';\nimport '@aws-amplify/core/internals/utils';\nexport { AuthError } from './errors/AuthError.mjs';\nimport './providers/cognito/tokenProvider/errorHelpers.mjs';\nimport './providers/cognito/utils/types.mjs';\nimport './providers/cognito/tokenProvider/tokenProvider.mjs';\nimport '@aws-crypto/sha256-js';\nimport './errors/constants.mjs';\nimport './Errors.mjs';\nimport './common/AuthErrorStrings.mjs';\nimport './types/Auth.mjs';\nexport { associateWebAuthnCredential } from './client/apis/associateWebAuthnCredential.mjs';\nexport { listWebAuthnCredentials } from './client/apis/listWebAuthnCredentials.mjs';\nexport { deleteWebAuthnCredential } from './client/apis/deleteWebAuthnCredential.mjs';", "map": {"version": 3, "names": ["signUp", "resetPassword", "confirmResetPassword", "signIn", "resendSignUpCode", "confirmSignUp", "confirmSignIn", "updateMFAPreference", "fetchMFAPreference", "verifyTOTPSetup", "updatePassword", "setUpTOTP", "updateUserAttributes", "updateUserAttribute", "getCurrentUser", "confirmUserAttribute", "signInWithRedirect", "fetchUserAttributes", "signOut", "sendUserAttributeVerificationCode", "deleteUserAttributes", "deleteUser", "rememberDevice", "forgetDevice", "fetchDevices", "autoSignIn", "decodeJWT", "fetchAuthSession", "<PERSON>th<PERSON><PERSON><PERSON>", "associateWebAuthnCredential", "listWebAuthnCredentials", "deleteWebAuthnCredential"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/index.mjs"], "sourcesContent": ["export { signUp } from './providers/cognito/apis/signUp.mjs';\nexport { resetPassword } from './providers/cognito/apis/resetPassword.mjs';\nexport { confirmResetPassword } from './providers/cognito/apis/confirmResetPassword.mjs';\nexport { signIn } from './providers/cognito/apis/signIn.mjs';\nexport { resendSignUpCode } from './providers/cognito/apis/resendSignUpCode.mjs';\nexport { confirmSignUp } from './providers/cognito/apis/confirmSignUp.mjs';\nexport { confirmSignIn } from './providers/cognito/apis/confirmSignIn.mjs';\nexport { updateMFAPreference } from './providers/cognito/apis/updateMFAPreference.mjs';\nexport { fetchMFAPreference } from './providers/cognito/apis/fetchMFAPreference.mjs';\nexport { verifyTOTPSetup } from './providers/cognito/apis/verifyTOTPSetup.mjs';\nexport { updatePassword } from './providers/cognito/apis/updatePassword.mjs';\nexport { setUpTOTP } from './providers/cognito/apis/setUpTOTP.mjs';\nexport { updateUserAttributes } from './providers/cognito/apis/updateUserAttributes.mjs';\nexport { updateUserAttribute } from './providers/cognito/apis/updateUserAttribute.mjs';\nexport { getCurrentUser } from './providers/cognito/apis/getCurrentUser.mjs';\nexport { confirmUserAttribute } from './providers/cognito/apis/confirmUserAttribute.mjs';\nexport { signInWithRedirect } from './providers/cognito/apis/signInWithRedirect.mjs';\nexport { fetchUserAttributes } from './providers/cognito/apis/fetchUserAttributes.mjs';\nexport { signOut } from './providers/cognito/apis/signOut.mjs';\nexport { sendUserAttributeVerificationCode } from './providers/cognito/apis/sendUserAttributeVerificationCode.mjs';\nexport { deleteUserAttributes } from './providers/cognito/apis/deleteUserAttributes.mjs';\nexport { deleteUser } from './providers/cognito/apis/deleteUser.mjs';\nexport { rememberDevice } from './providers/cognito/apis/rememberDevice.mjs';\nexport { forgetDevice } from './providers/cognito/apis/forgetDevice.mjs';\nexport { fetchDevices } from './providers/cognito/apis/fetchDevices.mjs';\nexport { autoSignIn } from './providers/cognito/apis/autoSignIn.mjs';\nexport { decodeJWT, fetchAuthSession } from '@aws-amplify/core';\nimport './providers/cognito/credentialsProvider/IdentityIdStore.mjs';\nimport './providers/cognito/credentialsProvider/credentialsProvider.mjs';\nimport './providers/cognito/utils/refreshAuthTokens.mjs';\nimport '@aws-amplify/core/internals/utils';\nexport { AuthError } from './errors/AuthError.mjs';\nimport './providers/cognito/tokenProvider/errorHelpers.mjs';\nimport './providers/cognito/utils/types.mjs';\nimport './providers/cognito/tokenProvider/tokenProvider.mjs';\nimport '@aws-crypto/sha256-js';\nimport './errors/constants.mjs';\nimport './Errors.mjs';\nimport './common/AuthErrorStrings.mjs';\nimport './types/Auth.mjs';\nexport { associateWebAuthnCredential } from './client/apis/associateWebAuthnCredential.mjs';\nexport { listWebAuthnCredentials } from './client/apis/listWebAuthnCredentials.mjs';\nexport { deleteWebAuthnCredential } from './client/apis/deleteWebAuthnCredential.mjs';\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,qCAAqC;AAC5D,SAASC,aAAa,QAAQ,4CAA4C;AAC1E,SAASC,oBAAoB,QAAQ,mDAAmD;AACxF,SAASC,MAAM,QAAQ,qCAAqC;AAC5D,SAASC,gBAAgB,QAAQ,+CAA+C;AAChF,SAASC,aAAa,QAAQ,4CAA4C;AAC1E,SAASC,aAAa,QAAQ,4CAA4C;AAC1E,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,eAAe,QAAQ,8CAA8C;AAC9E,SAASC,cAAc,QAAQ,6CAA6C;AAC5E,SAASC,SAAS,QAAQ,wCAAwC;AAClE,SAASC,oBAAoB,QAAQ,mDAAmD;AACxF,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,cAAc,QAAQ,6CAA6C;AAC5E,SAASC,oBAAoB,QAAQ,mDAAmD;AACxF,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,mBAAmB,QAAQ,kDAAkD;AACtF,SAASC,OAAO,QAAQ,sCAAsC;AAC9D,SAASC,iCAAiC,QAAQ,gEAAgE;AAClH,SAASC,oBAAoB,QAAQ,mDAAmD;AACxF,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,cAAc,QAAQ,6CAA6C;AAC5E,SAASC,YAAY,QAAQ,2CAA2C;AACxE,SAASC,YAAY,QAAQ,2CAA2C;AACxE,SAASC,UAAU,QAAQ,yCAAyC;AACpE,SAASC,SAAS,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC/D,OAAO,6DAA6D;AACpE,OAAO,iEAAiE;AACxE,OAAO,iDAAiD;AACxD,OAAO,mCAAmC;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,OAAO,oDAAoD;AAC3D,OAAO,qCAAqC;AAC5C,OAAO,qDAAqD;AAC5D,OAAO,uBAAuB;AAC9B,OAAO,wBAAwB;AAC/B,OAAO,cAAc;AACrB,OAAO,+BAA+B;AACtC,OAAO,kBAAkB;AACzB,SAASC,2BAA2B,QAAQ,+CAA+C;AAC3F,SAASC,uBAAuB,QAAQ,2CAA2C;AACnF,SAASC,wBAAwB,QAAQ,4CAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}