{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { composeServiceApi } from '@aws-amplify/core/internals/aws-client-utils/composers';\nimport { parseJsonError, parseJsonBody } from '@aws-amplify/core/internals/aws-client-utils';\nimport { validationErrorMap } from '../../../../common/AuthErrorStrings.mjs';\nimport { AuthError } from '../../../../errors/AuthError.mjs';\nimport { AuthValidationErrorCode } from '../../../../errors/types/validation.mjs';\nimport { assertServiceError } from '../../../../errors/utils/assertServiceError.mjs';\nimport { SignUpException } from '../../../../providers/cognito/types/errors.mjs';\nimport { createUserPoolSerializer } from './shared/serde/createUserPoolSerializer.mjs';\nimport { cognitoUserPoolTransferHandler } from './shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport { DEFAULT_SERVICE_CLIENT_API_CONFIG } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createSignUpClientDeserializer = () => (/*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (response) {\n    if (response.statusCode >= 300) {\n      const error = yield parseJsonError(response);\n      assertServiceError(error);\n      if (\n      // Missing Password Error\n      // 1 validation error detected: Value at 'password'failed to satisfy constraint: Member must not be null\n      error.name === SignUpException.InvalidParameterException && /'password'/.test(error.message) && /Member must not be null/.test(error.message)) {\n        const name = AuthValidationErrorCode.EmptySignUpPassword;\n        const {\n          message,\n          recoverySuggestion\n        } = validationErrorMap[name];\n        throw new AuthError({\n          name,\n          message,\n          recoverySuggestion\n        });\n      }\n      throw new AuthError({\n        name: error.name,\n        message: error.message\n      });\n    }\n    return parseJsonBody(response);\n  });\n  return function (_x) {\n    return _ref.apply(this, arguments);\n  };\n}());\nconst createSignUpClient = config => composeServiceApi(cognitoUserPoolTransferHandler, createUserPoolSerializer('SignUp'), createSignUpClientDeserializer(), {\n  ...DEFAULT_SERVICE_CLIENT_API_CONFIG,\n  ...config\n});\nexport { createSignUpClient, createSignUpClientDeserializer };", "map": {"version": 3, "names": ["composeServiceApi", "parseJsonError", "parseJsonBody", "validationErrorMap", "<PERSON>th<PERSON><PERSON><PERSON>", "AuthValidationErrorCode", "assertServiceError", "SignUpException", "createUserPoolSerializer", "cognitoUserPoolTransferHandler", "DEFAULT_SERVICE_CLIENT_API_CONFIG", "createSignUpClientDeserializer", "_ref", "_asyncToGenerator", "response", "statusCode", "error", "name", "InvalidParameterException", "test", "message", "EmptySignUpPassword", "recoverySuggestion", "_x", "apply", "arguments", "createSignUpClient", "config"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/factories/serviceClients/cognitoIdentityProvider/createSignUpClient.mjs"], "sourcesContent": ["import { composeService<PERSON><PERSON> } from '@aws-amplify/core/internals/aws-client-utils/composers';\nimport { parseJsonError, parseJsonBody } from '@aws-amplify/core/internals/aws-client-utils';\nimport { validationErrorMap } from '../../../../common/AuthErrorStrings.mjs';\nimport { AuthError } from '../../../../errors/AuthError.mjs';\nimport { AuthValidationErrorCode } from '../../../../errors/types/validation.mjs';\nimport { assertServiceError } from '../../../../errors/utils/assertServiceError.mjs';\nimport { SignUpException } from '../../../../providers/cognito/types/errors.mjs';\nimport { createUserPoolSerializer } from './shared/serde/createUserPoolSerializer.mjs';\nimport { cognitoUserPoolTransferHandler } from './shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport { DEFAULT_SERVICE_CLIENT_API_CONFIG } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createSignUpClientDeserializer = () => async (response) => {\n    if (response.statusCode >= 300) {\n        const error = await parseJsonError(response);\n        assertServiceError(error);\n        if (\n        // Missing Password Error\n        // 1 validation error detected: Value at 'password'failed to satisfy constraint: Member must not be null\n        error.name === SignUpException.InvalidParameterException &&\n            /'password'/.test(error.message) &&\n            /Member must not be null/.test(error.message)) {\n            const name = AuthValidationErrorCode.EmptySignUpPassword;\n            const { message, recoverySuggestion } = validationErrorMap[name];\n            throw new AuthError({\n                name,\n                message,\n                recoverySuggestion,\n            });\n        }\n        throw new AuthError({ name: error.name, message: error.message });\n    }\n    return parseJsonBody(response);\n};\nconst createSignUpClient = (config) => composeServiceApi(cognitoUserPoolTransferHandler, createUserPoolSerializer('SignUp'), createSignUpClientDeserializer(), {\n    ...DEFAULT_SERVICE_CLIENT_API_CONFIG,\n    ...config,\n});\n\nexport { createSignUpClient, createSignUpClientDeserializer };\n"], "mappings": ";AAAA,SAASA,iBAAiB,QAAQ,wDAAwD;AAC1F,SAASC,cAAc,EAAEC,aAAa,QAAQ,8CAA8C;AAC5F,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,uBAAuB,QAAQ,yCAAyC;AACjF,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,eAAe,QAAQ,gDAAgD;AAChF,SAASC,wBAAwB,QAAQ,6CAA6C;AACtF,SAASC,8BAA8B,QAAQ,qDAAqD;AACpG,SAASC,iCAAiC,QAAQ,iBAAiB;;AAEnE;AACA;AACA,MAAMC,8BAA8B,GAAGA,CAAA;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAM,WAAOC,QAAQ,EAAK;IAC7D,IAAIA,QAAQ,CAACC,UAAU,IAAI,GAAG,EAAE;MAC5B,MAAMC,KAAK,SAASf,cAAc,CAACa,QAAQ,CAAC;MAC5CR,kBAAkB,CAACU,KAAK,CAAC;MACzB;MACA;MACA;MACAA,KAAK,CAACC,IAAI,KAAKV,eAAe,CAACW,yBAAyB,IACpD,YAAY,CAACC,IAAI,CAACH,KAAK,CAACI,OAAO,CAAC,IAChC,yBAAyB,CAACD,IAAI,CAACH,KAAK,CAACI,OAAO,CAAC,EAAE;QAC/C,MAAMH,IAAI,GAAGZ,uBAAuB,CAACgB,mBAAmB;QACxD,MAAM;UAAED,OAAO;UAAEE;QAAmB,CAAC,GAAGnB,kBAAkB,CAACc,IAAI,CAAC;QAChE,MAAM,IAAIb,SAAS,CAAC;UAChBa,IAAI;UACJG,OAAO;UACPE;QACJ,CAAC,CAAC;MACN;MACA,MAAM,IAAIlB,SAAS,CAAC;QAAEa,IAAI,EAAED,KAAK,CAACC,IAAI;QAAEG,OAAO,EAAEJ,KAAK,CAACI;MAAQ,CAAC,CAAC;IACrE;IACA,OAAOlB,aAAa,CAACY,QAAQ,CAAC;EAClC,CAAC;EAAA,iBAAAS,EAAA;IAAA,OAAAX,IAAA,CAAAY,KAAA,OAAAC,SAAA;EAAA;AAAA;AACD,MAAMC,kBAAkB,GAAIC,MAAM,IAAK3B,iBAAiB,CAACS,8BAA8B,EAAED,wBAAwB,CAAC,QAAQ,CAAC,EAAEG,8BAA8B,CAAC,CAAC,EAAE;EAC3J,GAAGD,iCAAiC;EACpC,GAAGiB;AACP,CAAC,CAAC;AAEF,SAASD,kBAAkB,EAAEf,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}