{"ast": null, "code": "const autocomplete = {\n  menu: {\n    width: {\n      value: '100%'\n    },\n    marginBlockStart: {\n      value: '{space.xxxs}'\n    },\n    backgroundColor: {\n      value: '{colors.background.primary}'\n    },\n    borderColor: {\n      value: '{colors.border.primary}'\n    },\n    borderWidth: {\n      value: '{borderWidths.small}'\n    },\n    borderStyle: {\n      value: 'solid'\n    },\n    borderRadius: {\n      value: '{radii.small}'\n    },\n    options: {\n      display: {\n        value: 'flex'\n      },\n      flexDirection: {\n        value: 'column'\n      },\n      maxHeight: {\n        value: '300px'\n      }\n    },\n    option: {\n      backgroundColor: {\n        value: '{colors.background.primary}'\n      },\n      color: {\n        value: 'currentcolor'\n      },\n      cursor: {\n        value: 'pointer'\n      },\n      transitionDuration: {\n        value: '{time.short}'\n      },\n      transitionProperty: {\n        value: 'background-color, color'\n      },\n      transitionTimingFunction: {\n        value: 'ease'\n      },\n      _active: {\n        backgroundColor: {\n          value: '{colors.primary.80}'\n        },\n        color: {\n          value: '{colors.white}'\n        }\n      }\n    },\n    _empty: {\n      display: {\n        value: 'flex'\n      }\n    },\n    _loading: {\n      alignItems: {\n        value: 'center'\n      },\n      display: {\n        value: 'flex'\n      },\n      gap: {\n        value: '{space.xxxs}'\n      }\n    },\n    spaceShared: {\n      paddingBlock: {\n        value: '{space.xs}'\n      },\n      paddingInline: {\n        value: '{space.small}'\n      }\n    }\n  }\n};\nexport { autocomplete };", "map": {"version": 3, "names": ["autocomplete", "menu", "width", "value", "marginBlockStart", "backgroundColor", "borderColor", "borderWidth", "borderStyle", "borderRadius", "options", "display", "flexDirection", "maxHeight", "option", "color", "cursor", "transitionDuration", "transitionProperty", "transitionTimingFunction", "_active", "_empty", "_loading", "alignItems", "gap", "spaceShared", "paddingBlock", "paddingInline"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/autocomplete.mjs"], "sourcesContent": ["const autocomplete = {\n    menu: {\n        width: { value: '100%' },\n        marginBlockStart: { value: '{space.xxxs}' },\n        backgroundColor: { value: '{colors.background.primary}' },\n        borderColor: { value: '{colors.border.primary}' },\n        borderWidth: { value: '{borderWidths.small}' },\n        borderStyle: { value: 'solid' },\n        borderRadius: { value: '{radii.small}' },\n        options: {\n            display: { value: 'flex' },\n            flexDirection: { value: 'column' },\n            maxHeight: { value: '300px' },\n        },\n        option: {\n            backgroundColor: { value: '{colors.background.primary}' },\n            color: { value: 'currentcolor' },\n            cursor: { value: 'pointer' },\n            transitionDuration: { value: '{time.short}' },\n            transitionProperty: { value: 'background-color, color' },\n            transitionTimingFunction: { value: 'ease' },\n            _active: {\n                backgroundColor: { value: '{colors.primary.80}' },\n                color: { value: '{colors.white}' },\n            },\n        },\n        _empty: {\n            display: { value: 'flex' },\n        },\n        _loading: {\n            alignItems: { value: 'center' },\n            display: { value: 'flex' },\n            gap: { value: '{space.xxxs}' },\n        },\n        spaceShared: {\n            paddingBlock: { value: '{space.xs}' },\n            paddingInline: { value: '{space.small}' },\n        },\n    },\n};\n\nexport { autocomplete };\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG;EACjBC,IAAI,EAAE;IACFC,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IACxBC,gBAAgB,EAAE;MAAED,KAAK,EAAE;IAAe,CAAC;IAC3CE,eAAe,EAAE;MAAEF,KAAK,EAAE;IAA8B,CAAC;IACzDG,WAAW,EAAE;MAAEH,KAAK,EAAE;IAA0B,CAAC;IACjDI,WAAW,EAAE;MAAEJ,KAAK,EAAE;IAAuB,CAAC;IAC9CK,WAAW,EAAE;MAAEL,KAAK,EAAE;IAAQ,CAAC;IAC/BM,YAAY,EAAE;MAAEN,KAAK,EAAE;IAAgB,CAAC;IACxCO,OAAO,EAAE;MACLC,OAAO,EAAE;QAAER,KAAK,EAAE;MAAO,CAAC;MAC1BS,aAAa,EAAE;QAAET,KAAK,EAAE;MAAS,CAAC;MAClCU,SAAS,EAAE;QAAEV,KAAK,EAAE;MAAQ;IAChC,CAAC;IACDW,MAAM,EAAE;MACJT,eAAe,EAAE;QAAEF,KAAK,EAAE;MAA8B,CAAC;MACzDY,KAAK,EAAE;QAAEZ,KAAK,EAAE;MAAe,CAAC;MAChCa,MAAM,EAAE;QAAEb,KAAK,EAAE;MAAU,CAAC;MAC5Bc,kBAAkB,EAAE;QAAEd,KAAK,EAAE;MAAe,CAAC;MAC7Ce,kBAAkB,EAAE;QAAEf,KAAK,EAAE;MAA0B,CAAC;MACxDgB,wBAAwB,EAAE;QAAEhB,KAAK,EAAE;MAAO,CAAC;MAC3CiB,OAAO,EAAE;QACLf,eAAe,EAAE;UAAEF,KAAK,EAAE;QAAsB,CAAC;QACjDY,KAAK,EAAE;UAAEZ,KAAK,EAAE;QAAiB;MACrC;IACJ,CAAC;IACDkB,MAAM,EAAE;MACJV,OAAO,EAAE;QAAER,KAAK,EAAE;MAAO;IAC7B,CAAC;IACDmB,QAAQ,EAAE;MACNC,UAAU,EAAE;QAAEpB,KAAK,EAAE;MAAS,CAAC;MAC/BQ,OAAO,EAAE;QAAER,KAAK,EAAE;MAAO,CAAC;MAC1BqB,GAAG,EAAE;QAAErB,KAAK,EAAE;MAAe;IACjC,CAAC;IACDsB,WAAW,EAAE;MACTC,YAAY,EAAE;QAAEvB,KAAK,EAAE;MAAa,CAAC;MACrCwB,aAAa,EAAE;QAAExB,KAAK,EAAE;MAAgB;IAC5C;EACJ;AACJ,CAAC;AAED,SAASH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}