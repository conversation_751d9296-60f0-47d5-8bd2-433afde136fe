{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { autoSignInStore } from '../../../client/utils/store/autoSignInStore.mjs';\nimport '../../../client/utils/store/signInStore.mjs';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { AUTO_SIGN_IN_EXCEPTION } from '../../../errors/constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst initialAutoSignIn = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* () {\n    throw new AuthError({\n      name: AUTO_SIGN_IN_EXCEPTION,\n      message: 'The autoSignIn flow has not started, or has been cancelled/completed.',\n      recoverySuggestion: 'Please try to use the signIn API or log out before starting a new autoSignIn flow.'\n    });\n  });\n  return function initialAutoSignIn() {\n    return _ref.apply(this, arguments);\n  };\n}();\n/**\n * Signs a user in automatically after finishing the sign-up process.\n *\n * This API will automatically sign a user in if the autoSignIn flow has been completed in the following cases:\n * - User confirmed their account with a verification code sent to their phone or email (default option).\n * - User confirmed their account with a verification link sent to their phone or email. In order to\n * enable this option you need to go to the Amazon Cognito [console](https://aws.amazon.com/pm/cognito),\n * look for your userpool, then go to the `Messaging` tab and enable `link` mode inside the `Verification message` option.\n * Finally you need to define the `signUpVerificationMethod` in your `Auth` config.\n *\n * @example\n * ```typescript\n *  Amplify.configure({\n *    Auth: {\n *     Cognito: {\n *    ...cognitoConfig,\n *    signUpVerificationMethod: \"link\" // the default value is \"code\"\n *   }\n *\t}});\n * ```\n *\n * @throws AutoSignInException - Thrown when the autoSignIn flow has not started, or has been cancelled/completed.\n * @returns The signInOutput.\n *\n * @example\n * ```typescript\n *  // handleSignUp.ts\n * async function handleSignUp(\n *   username:string,\n *   password:string\n * ){\n *   try {\n *     const { nextStep } = await signUp({\n *       username,\n *       password,\n *       options: {\n *         userAttributes:{ email:'<EMAIL>'},\n *         autoSignIn: true // This enables the auto sign-in flow.\n *       },\n *     });\n *\n *     handleSignUpStep(nextStep);\n *\n *   } catch (error) {\n *     console.log(error);\n *   }\n * }\n *\n * // handleConfirmSignUp.ts\n * async function handleConfirmSignUp(username:string, confirmationCode:string) {\n *   try {\n *     const { nextStep } = await confirmSignUp({\n *       username,\n *       confirmationCode,\n *     });\n *\n *     handleSignUpStep(nextStep);\n *   } catch (error) {\n *     console.log(error);\n *   }\n * }\n *\n * // signUpUtils.ts\n * async function handleSignUpStep( step: SignUpOutput[\"nextStep\"]) {\n * switch (step.signUpStep) {\n *   case \"CONFIRM_SIGN_UP\":\n *\n *    // Redirect end-user to confirm-sign up screen.\n *\n *   case \"COMPLETE_AUTO_SIGN_IN\":\n *\t   const codeDeliveryDetails = step.codeDeliveryDetails;\n *     if (codeDeliveryDetails) {\n *      // Redirect user to confirm-sign-up with link screen.\n *     }\n *     const signInOutput = await autoSignIn();\n *   // handle sign-in steps\n * }\n *\n * ```\n */\n// TODO(Eslint): can this be refactored not using `let` on exported member?\n// eslint-disable-next-line import/no-mutable-exports\nlet autoSignIn = initialAutoSignIn;\n/**\n * Sets the context of autoSignIn at run time.\n * @internal\n */\nfunction setAutoSignIn(callback) {\n  autoSignIn = callback;\n}\n/**\n * Resets the context\n *\n * @internal\n */\nfunction resetAutoSignIn(resetCallback = true) {\n  if (resetCallback) {\n    autoSignIn = initialAutoSignIn;\n  }\n  autoSignInStore.dispatch({\n    type: 'RESET'\n  });\n}\nexport { autoSignIn, resetAutoSignIn, setAutoSignIn };", "map": {"version": 3, "names": ["autoSignInStore", "<PERSON>th<PERSON><PERSON><PERSON>", "AUTO_SIGN_IN_EXCEPTION", "initialAutoSignIn", "_ref", "_asyncToGenerator", "name", "message", "recoverySuggestion", "apply", "arguments", "autoSignIn", "setAutoSignIn", "callback", "resetAutoSignIn", "resetCallback", "dispatch", "type"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/autoSignIn.mjs"], "sourcesContent": ["import { autoSignInStore } from '../../../client/utils/store/autoSignInStore.mjs';\nimport '../../../client/utils/store/signInStore.mjs';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { AUTO_SIGN_IN_EXCEPTION } from '../../../errors/constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst initialAutoSignIn = async () => {\n    throw new AuthError({\n        name: AUTO_SIGN_IN_EXCEPTION,\n        message: 'The autoSignIn flow has not started, or has been cancelled/completed.',\n        recoverySuggestion: 'Please try to use the signIn API or log out before starting a new autoSignIn flow.',\n    });\n};\n/**\n * Signs a user in automatically after finishing the sign-up process.\n *\n * This API will automatically sign a user in if the autoSignIn flow has been completed in the following cases:\n * - User confirmed their account with a verification code sent to their phone or email (default option).\n * - User confirmed their account with a verification link sent to their phone or email. In order to\n * enable this option you need to go to the Amazon Cognito [console](https://aws.amazon.com/pm/cognito),\n * look for your userpool, then go to the `Messaging` tab and enable `link` mode inside the `Verification message` option.\n * Finally you need to define the `signUpVerificationMethod` in your `Auth` config.\n *\n * @example\n * ```typescript\n *  Amplify.configure({\n *    Auth: {\n *     Cognito: {\n *    ...cognitoConfig,\n *    signUpVerificationMethod: \"link\" // the default value is \"code\"\n *   }\n *\t}});\n * ```\n *\n * @throws AutoSignInException - Thrown when the autoSignIn flow has not started, or has been cancelled/completed.\n * @returns The signInOutput.\n *\n * @example\n * ```typescript\n *  // handleSignUp.ts\n * async function handleSignUp(\n *   username:string,\n *   password:string\n * ){\n *   try {\n *     const { nextStep } = await signUp({\n *       username,\n *       password,\n *       options: {\n *         userAttributes:{ email:'<EMAIL>'},\n *         autoSignIn: true // This enables the auto sign-in flow.\n *       },\n *     });\n *\n *     handleSignUpStep(nextStep);\n *\n *   } catch (error) {\n *     console.log(error);\n *   }\n * }\n *\n * // handleConfirmSignUp.ts\n * async function handleConfirmSignUp(username:string, confirmationCode:string) {\n *   try {\n *     const { nextStep } = await confirmSignUp({\n *       username,\n *       confirmationCode,\n *     });\n *\n *     handleSignUpStep(nextStep);\n *   } catch (error) {\n *     console.log(error);\n *   }\n * }\n *\n * // signUpUtils.ts\n * async function handleSignUpStep( step: SignUpOutput[\"nextStep\"]) {\n * switch (step.signUpStep) {\n *   case \"CONFIRM_SIGN_UP\":\n *\n *    // Redirect end-user to confirm-sign up screen.\n *\n *   case \"COMPLETE_AUTO_SIGN_IN\":\n *\t   const codeDeliveryDetails = step.codeDeliveryDetails;\n *     if (codeDeliveryDetails) {\n *      // Redirect user to confirm-sign-up with link screen.\n *     }\n *     const signInOutput = await autoSignIn();\n *   // handle sign-in steps\n * }\n *\n * ```\n */\n// TODO(Eslint): can this be refactored not using `let` on exported member?\n// eslint-disable-next-line import/no-mutable-exports\nlet autoSignIn = initialAutoSignIn;\n/**\n * Sets the context of autoSignIn at run time.\n * @internal\n */\nfunction setAutoSignIn(callback) {\n    autoSignIn = callback;\n}\n/**\n * Resets the context\n *\n * @internal\n */\nfunction resetAutoSignIn(resetCallback = true) {\n    if (resetCallback) {\n        autoSignIn = initialAutoSignIn;\n    }\n    autoSignInStore.dispatch({ type: 'RESET' });\n}\n\nexport { autoSignIn, resetAutoSignIn, setAutoSignIn };\n"], "mappings": ";AAAA,SAASA,eAAe,QAAQ,iDAAiD;AACjF,OAAO,6CAA6C;AACpD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,sBAAsB,QAAQ,+BAA+B;;AAEtE;AACA;AACA,MAAMC,iBAAiB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;IAClC,MAAM,IAAIJ,SAAS,CAAC;MAChBK,IAAI,EAAEJ,sBAAsB;MAC5BK,OAAO,EAAE,uEAAuE;MAChFC,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN,CAAC;EAAA,gBANKL,iBAAiBA,CAAA;IAAA,OAAAC,IAAA,CAAAK,KAAA,OAAAC,SAAA;EAAA;AAAA,GAMtB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAGR,iBAAiB;AAClC;AACA;AACA;AACA;AACA,SAASS,aAAaA,CAACC,QAAQ,EAAE;EAC7BF,UAAU,GAAGE,QAAQ;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,aAAa,GAAG,IAAI,EAAE;EAC3C,IAAIA,aAAa,EAAE;IACfJ,UAAU,GAAGR,iBAAiB;EAClC;EACAH,eAAe,CAACgB,QAAQ,CAAC;IAAEC,IAAI,EAAE;EAAQ,CAAC,CAAC;AAC/C;AAEA,SAASN,UAAU,EAAEG,eAAe,EAAEF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}