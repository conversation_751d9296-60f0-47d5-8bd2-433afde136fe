{"ast": null, "code": "import isPromise from \"./isPromise.mjs\";\n/**\n * Similar to Array.prototype.reduce(), however the reducing callback may return\n * a Promise, in which case reduction will continue after each promise resolves.\n *\n * If the callback does not return a Promise, then this function will also not\n * return a Promise.\n */\n\nexport default function promiseReduce(values, callback, initialValue) {\n  return values.reduce(function (previous, value) {\n    return isPromise(previous) ? previous.then(function (resolved) {\n      return callback(resolved, value);\n    }) : callback(previous, value);\n  }, initialValue);\n}", "map": {"version": 3, "names": ["isPromise", "promiseReduce", "values", "callback", "initialValue", "reduce", "previous", "value", "then", "resolved"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/graphql/jsutils/promiseReduce.mjs"], "sourcesContent": ["import isPromise from \"./isPromise.mjs\";\n/**\n * Similar to Array.prototype.reduce(), however the reducing callback may return\n * a Promise, in which case reduction will continue after each promise resolves.\n *\n * If the callback does not return a Promise, then this function will also not\n * return a Promise.\n */\n\nexport default function promiseReduce(values, callback, initialValue) {\n  return values.reduce(function (previous, value) {\n    return isPromise(previous) ? previous.then(function (resolved) {\n      return callback(resolved, value);\n    }) : callback(previous, value);\n  }, initialValue);\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,aAAaA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE;EACpE,OAAOF,MAAM,CAACG,MAAM,CAAC,UAAUC,QAAQ,EAAEC,KAAK,EAAE;IAC9C,OAAOP,SAAS,CAACM,QAAQ,CAAC,GAAGA,QAAQ,CAACE,IAAI,CAAC,UAAUC,QAAQ,EAAE;MAC7D,OAAON,QAAQ,CAACM,QAAQ,EAAEF,KAAK,CAAC;IAClC,CAAC,CAAC,GAAGJ,QAAQ,CAACG,QAAQ,EAAEC,KAAK,CAAC;EAChC,CAAC,EAAEH,YAAY,CAAC;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}