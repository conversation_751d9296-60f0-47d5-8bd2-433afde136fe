{"ast": null, "code": "import { signRequest } from '../clients/middleware/signing/signer/signatureV4/signRequest.mjs';\nimport { presignUrl } from '../clients/middleware/signing/signer/signatureV4/presignUrl.mjs';\nimport { TOKEN_QUERY_PARAM } from '../clients/middleware/signing/signer/signatureV4/constants.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport { AmplifyUrl } from '../utils/amplifyUrl/index.mjs';\nimport { DateUtils } from './DateUtils.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst IOT_SERVICE_NAME = 'iotdevicegateway';\n// Best practice regex to parse the service and region from an AWS endpoint\nconst AWS_ENDPOINT_REGEX = /([^.]+)\\.(?:([^.]*)\\.)?amazonaws\\.com(.cn)?$/;\n/**\n * This class is intended to be deprecated and replaced by `signRequest` and `presignUrl` functions from\n * `clients/middleware/signing/signer/signatureV4`.\n *\n * TODO: refactor the logics here into `signRequest` and `presignUrl` functions and remove this class.\n *\n * @internal\n * @deprecated\n */\nclass Signer {\n  /**\n  * Sign a HTTP request, add 'Authorization' header to request param\n  * @method sign\n  * @memberof Signer\n  * @static\n  *\n  * @param {object} request - HTTP request object\n  <pre>\n  request: {\n      method: GET | POST | PUT ...\n      url: ...,\n      headers: {\n          header1: ...\n      },\n      data: data\n  }\n  </pre>\n  * @param {object} access_info - AWS access credential info\n  <pre>\n  access_info: {\n      access_key: ...,\n      secret_key: ...,\n      session_token: ...\n  }\n  </pre>\n  * @param {object} [service_info] - AWS service type and region, optional,\n  *                                  if not provided then parse out from url\n  <pre>\n  service_info: {\n      service: ...,\n      region: ...\n  }\n  </pre>\n  *\n  * @returns {object} Signed HTTP request\n  */\n  static sign(request, accessInfo, serviceInfo) {\n    request.headers = request.headers || {};\n    if (request.body && !request.data) {\n      throw new Error('The attribute \"body\" was found on the request object. Please use the attribute \"data\" instead.');\n    }\n    const requestToSign = {\n      ...request,\n      body: request.data,\n      url: new AmplifyUrl(request.url)\n    };\n    const options = getOptions(requestToSign, accessInfo, serviceInfo);\n    const signedRequest = signRequest(requestToSign, options);\n    // Prior to using `signRequest`, Signer accepted urls as strings and outputted urls as string. Coerce the property\n    // back to a string so as not to disrupt consumers of Signer.\n    signedRequest.url = signedRequest.url.toString();\n    // HTTP headers should be case insensitive but, to maintain parity with the previous Signer implementation and\n    // limit the impact of this implementation swap, replace lowercased headers with title cased ones.\n    signedRequest.headers.Authorization = signedRequest.headers.authorization;\n    signedRequest.headers['X-Amz-Security-Token'] = signedRequest.headers['x-amz-security-token'];\n    delete signedRequest.headers.authorization;\n    delete signedRequest.headers['x-amz-security-token'];\n    return signedRequest;\n  }\n  static signUrl(urlOrRequest, accessInfo, serviceInfo, expiration) {\n    const urlToSign = typeof urlOrRequest === 'object' ? urlOrRequest.url : urlOrRequest;\n    const method = typeof urlOrRequest === 'object' ? urlOrRequest.method : 'GET';\n    const body = typeof urlOrRequest === 'object' ? urlOrRequest.body : undefined;\n    const presignable = {\n      body,\n      method,\n      url: new AmplifyUrl(urlToSign)\n    };\n    const options = getOptions(presignable, accessInfo, serviceInfo, expiration);\n    const signedUrl = presignUrl(presignable, options);\n    if (accessInfo.session_token && !sessionTokenRequiredInSigning(options.signingService)) {\n      signedUrl.searchParams.append(TOKEN_QUERY_PARAM, accessInfo.session_token);\n    }\n    return signedUrl.toString();\n  }\n}\nconst getOptions = (request, accessInfo, serviceInfo, expiration) => {\n  const {\n    access_key,\n    secret_key,\n    session_token\n  } = accessInfo ?? {};\n  const {\n    region: urlRegion,\n    service: urlService\n  } = parseServiceInfo(request.url);\n  const {\n    region = urlRegion,\n    service = urlService\n  } = serviceInfo ?? {};\n  const credentials = {\n    accessKeyId: access_key,\n    secretAccessKey: secret_key,\n    ...(sessionTokenRequiredInSigning(service) ? {\n      sessionToken: session_token\n    } : {})\n  };\n  return {\n    credentials,\n    signingDate: DateUtils.getDateWithClockOffset(),\n    signingRegion: region,\n    signingService: service,\n    ...(expiration && {\n      expiration\n    })\n  };\n};\nconst parseServiceInfo = url => {\n  const {\n    host\n  } = url;\n  const matched = host.match(AWS_ENDPOINT_REGEX) ?? [];\n  let parsed = matched.slice(1, 3);\n  if (parsed[1] === 'es') {\n    // Elastic Search\n    parsed = parsed.reverse();\n  }\n  return {\n    service: parsed[0],\n    region: parsed[1]\n  };\n};\n// IoT service does not allow the session token in the canonical request\n// https://docs.aws.amazon.com/general/latest/gr/sigv4-add-signature-to-request.html\nconst sessionTokenRequiredInSigning = service => service !== IOT_SERVICE_NAME;\nexport { Signer };", "map": {"version": 3, "names": ["signRequest", "presignUrl", "TOKEN_QUERY_PARAM", "AmplifyUrl", "DateUtils", "IOT_SERVICE_NAME", "AWS_ENDPOINT_REGEX", "Signer", "sign", "request", "accessInfo", "serviceInfo", "headers", "body", "data", "Error", "requestToSign", "url", "options", "getOptions", "signedRequest", "toString", "Authorization", "authorization", "signUrl", "urlOrRequest", "expiration", "urlToSign", "method", "undefined", "presignable", "signedUrl", "session_token", "sessionTokenRequiredInSigning", "signingService", "searchParams", "append", "access_key", "secret_key", "region", "urlRegion", "service", "urlService", "parseServiceInfo", "credentials", "accessKeyId", "secretAccessKey", "sessionToken", "signingDate", "getDateWithClockOffset", "signingRegion", "host", "matched", "match", "parsed", "slice", "reverse"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Signer/Signer.mjs"], "sourcesContent": ["import { signRequest } from '../clients/middleware/signing/signer/signatureV4/signRequest.mjs';\nimport { presignUrl } from '../clients/middleware/signing/signer/signatureV4/presignUrl.mjs';\nimport { TOKEN_QUERY_PARAM } from '../clients/middleware/signing/signer/signatureV4/constants.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport { AmplifyUrl } from '../utils/amplifyUrl/index.mjs';\nimport { DateUtils } from './DateUtils.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst IOT_SERVICE_NAME = 'iotdevicegateway';\n// Best practice regex to parse the service and region from an AWS endpoint\nconst AWS_ENDPOINT_REGEX = /([^.]+)\\.(?:([^.]*)\\.)?amazonaws\\.com(.cn)?$/;\n/**\n * This class is intended to be deprecated and replaced by `signRequest` and `presignUrl` functions from\n * `clients/middleware/signing/signer/signatureV4`.\n *\n * TODO: refactor the logics here into `signRequest` and `presignUrl` functions and remove this class.\n *\n * @internal\n * @deprecated\n */\nclass Signer {\n    /**\n    * Sign a HTTP request, add 'Authorization' header to request param\n    * @method sign\n    * @memberof Signer\n    * @static\n    *\n    * @param {object} request - HTTP request object\n    <pre>\n    request: {\n        method: GET | POST | PUT ...\n        url: ...,\n        headers: {\n            header1: ...\n        },\n        data: data\n    }\n    </pre>\n    * @param {object} access_info - AWS access credential info\n    <pre>\n    access_info: {\n        access_key: ...,\n        secret_key: ...,\n        session_token: ...\n    }\n    </pre>\n    * @param {object} [service_info] - AWS service type and region, optional,\n    *                                  if not provided then parse out from url\n    <pre>\n    service_info: {\n        service: ...,\n        region: ...\n    }\n    </pre>\n    *\n    * @returns {object} Signed HTTP request\n    */\n    static sign(request, accessInfo, serviceInfo) {\n        request.headers = request.headers || {};\n        if (request.body && !request.data) {\n            throw new Error('The attribute \"body\" was found on the request object. Please use the attribute \"data\" instead.');\n        }\n        const requestToSign = {\n            ...request,\n            body: request.data,\n            url: new AmplifyUrl(request.url),\n        };\n        const options = getOptions(requestToSign, accessInfo, serviceInfo);\n        const signedRequest = signRequest(requestToSign, options);\n        // Prior to using `signRequest`, Signer accepted urls as strings and outputted urls as string. Coerce the property\n        // back to a string so as not to disrupt consumers of Signer.\n        signedRequest.url = signedRequest.url.toString();\n        // HTTP headers should be case insensitive but, to maintain parity with the previous Signer implementation and\n        // limit the impact of this implementation swap, replace lowercased headers with title cased ones.\n        signedRequest.headers.Authorization = signedRequest.headers.authorization;\n        signedRequest.headers['X-Amz-Security-Token'] =\n            signedRequest.headers['x-amz-security-token'];\n        delete signedRequest.headers.authorization;\n        delete signedRequest.headers['x-amz-security-token'];\n        return signedRequest;\n    }\n    static signUrl(urlOrRequest, accessInfo, serviceInfo, expiration) {\n        const urlToSign = typeof urlOrRequest === 'object' ? urlOrRequest.url : urlOrRequest;\n        const method = typeof urlOrRequest === 'object' ? urlOrRequest.method : 'GET';\n        const body = typeof urlOrRequest === 'object' ? urlOrRequest.body : undefined;\n        const presignable = {\n            body,\n            method,\n            url: new AmplifyUrl(urlToSign),\n        };\n        const options = getOptions(presignable, accessInfo, serviceInfo, expiration);\n        const signedUrl = presignUrl(presignable, options);\n        if (accessInfo.session_token &&\n            !sessionTokenRequiredInSigning(options.signingService)) {\n            signedUrl.searchParams.append(TOKEN_QUERY_PARAM, accessInfo.session_token);\n        }\n        return signedUrl.toString();\n    }\n}\nconst getOptions = (request, accessInfo, serviceInfo, expiration) => {\n    const { access_key, secret_key, session_token } = accessInfo ?? {};\n    const { region: urlRegion, service: urlService } = parseServiceInfo(request.url);\n    const { region = urlRegion, service = urlService } = serviceInfo ?? {};\n    const credentials = {\n        accessKeyId: access_key,\n        secretAccessKey: secret_key,\n        ...(sessionTokenRequiredInSigning(service)\n            ? { sessionToken: session_token }\n            : {}),\n    };\n    return {\n        credentials,\n        signingDate: DateUtils.getDateWithClockOffset(),\n        signingRegion: region,\n        signingService: service,\n        ...(expiration && { expiration }),\n    };\n};\nconst parseServiceInfo = (url) => {\n    const { host } = url;\n    const matched = host.match(AWS_ENDPOINT_REGEX) ?? [];\n    let parsed = matched.slice(1, 3);\n    if (parsed[1] === 'es') {\n        // Elastic Search\n        parsed = parsed.reverse();\n    }\n    return {\n        service: parsed[0],\n        region: parsed[1],\n    };\n};\n// IoT service does not allow the session token in the canonical request\n// https://docs.aws.amazon.com/general/latest/gr/sigv4-add-signature-to-request.html\nconst sessionTokenRequiredInSigning = (service) => service !== IOT_SERVICE_NAME;\n\nexport { Signer };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kEAAkE;AAC9F,SAASC,UAAU,QAAQ,iEAAiE;AAC5F,SAASC,iBAAiB,QAAQ,gEAAgE;AAClG,OAAO,uBAAuB;AAC9B,OAAO,2BAA2B;AAClC,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,SAAS,QAAQ,iBAAiB;;AAE3C;AACA;AACA,MAAMC,gBAAgB,GAAG,kBAAkB;AAC3C;AACA,MAAMC,kBAAkB,GAAG,8CAA8C;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,CAAC;EACT;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,IAAIA,CAACC,OAAO,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAC1CF,OAAO,CAACG,OAAO,GAAGH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;IACvC,IAAIH,OAAO,CAACI,IAAI,IAAI,CAACJ,OAAO,CAACK,IAAI,EAAE;MAC/B,MAAM,IAAIC,KAAK,CAAC,gGAAgG,CAAC;IACrH;IACA,MAAMC,aAAa,GAAG;MAClB,GAAGP,OAAO;MACVI,IAAI,EAAEJ,OAAO,CAACK,IAAI;MAClBG,GAAG,EAAE,IAAId,UAAU,CAACM,OAAO,CAACQ,GAAG;IACnC,CAAC;IACD,MAAMC,OAAO,GAAGC,UAAU,CAACH,aAAa,EAAEN,UAAU,EAAEC,WAAW,CAAC;IAClE,MAAMS,aAAa,GAAGpB,WAAW,CAACgB,aAAa,EAAEE,OAAO,CAAC;IACzD;IACA;IACAE,aAAa,CAACH,GAAG,GAAGG,aAAa,CAACH,GAAG,CAACI,QAAQ,CAAC,CAAC;IAChD;IACA;IACAD,aAAa,CAACR,OAAO,CAACU,aAAa,GAAGF,aAAa,CAACR,OAAO,CAACW,aAAa;IACzEH,aAAa,CAACR,OAAO,CAAC,sBAAsB,CAAC,GACzCQ,aAAa,CAACR,OAAO,CAAC,sBAAsB,CAAC;IACjD,OAAOQ,aAAa,CAACR,OAAO,CAACW,aAAa;IAC1C,OAAOH,aAAa,CAACR,OAAO,CAAC,sBAAsB,CAAC;IACpD,OAAOQ,aAAa;EACxB;EACA,OAAOI,OAAOA,CAACC,YAAY,EAAEf,UAAU,EAAEC,WAAW,EAAEe,UAAU,EAAE;IAC9D,MAAMC,SAAS,GAAG,OAAOF,YAAY,KAAK,QAAQ,GAAGA,YAAY,CAACR,GAAG,GAAGQ,YAAY;IACpF,MAAMG,MAAM,GAAG,OAAOH,YAAY,KAAK,QAAQ,GAAGA,YAAY,CAACG,MAAM,GAAG,KAAK;IAC7E,MAAMf,IAAI,GAAG,OAAOY,YAAY,KAAK,QAAQ,GAAGA,YAAY,CAACZ,IAAI,GAAGgB,SAAS;IAC7E,MAAMC,WAAW,GAAG;MAChBjB,IAAI;MACJe,MAAM;MACNX,GAAG,EAAE,IAAId,UAAU,CAACwB,SAAS;IACjC,CAAC;IACD,MAAMT,OAAO,GAAGC,UAAU,CAACW,WAAW,EAAEpB,UAAU,EAAEC,WAAW,EAAEe,UAAU,CAAC;IAC5E,MAAMK,SAAS,GAAG9B,UAAU,CAAC6B,WAAW,EAAEZ,OAAO,CAAC;IAClD,IAAIR,UAAU,CAACsB,aAAa,IACxB,CAACC,6BAA6B,CAACf,OAAO,CAACgB,cAAc,CAAC,EAAE;MACxDH,SAAS,CAACI,YAAY,CAACC,MAAM,CAAClC,iBAAiB,EAAEQ,UAAU,CAACsB,aAAa,CAAC;IAC9E;IACA,OAAOD,SAAS,CAACV,QAAQ,CAAC,CAAC;EAC/B;AACJ;AACA,MAAMF,UAAU,GAAGA,CAACV,OAAO,EAAEC,UAAU,EAAEC,WAAW,EAAEe,UAAU,KAAK;EACjE,MAAM;IAAEW,UAAU;IAAEC,UAAU;IAAEN;EAAc,CAAC,GAAGtB,UAAU,IAAI,CAAC,CAAC;EAClE,MAAM;IAAE6B,MAAM,EAAEC,SAAS;IAAEC,OAAO,EAAEC;EAAW,CAAC,GAAGC,gBAAgB,CAAClC,OAAO,CAACQ,GAAG,CAAC;EAChF,MAAM;IAAEsB,MAAM,GAAGC,SAAS;IAAEC,OAAO,GAAGC;EAAW,CAAC,GAAG/B,WAAW,IAAI,CAAC,CAAC;EACtE,MAAMiC,WAAW,GAAG;IAChBC,WAAW,EAAER,UAAU;IACvBS,eAAe,EAAER,UAAU;IAC3B,IAAIL,6BAA6B,CAACQ,OAAO,CAAC,GACpC;MAAEM,YAAY,EAAEf;IAAc,CAAC,GAC/B,CAAC,CAAC;EACZ,CAAC;EACD,OAAO;IACHY,WAAW;IACXI,WAAW,EAAE5C,SAAS,CAAC6C,sBAAsB,CAAC,CAAC;IAC/CC,aAAa,EAAEX,MAAM;IACrBL,cAAc,EAAEO,OAAO;IACvB,IAAIf,UAAU,IAAI;MAAEA;IAAW,CAAC;EACpC,CAAC;AACL,CAAC;AACD,MAAMiB,gBAAgB,GAAI1B,GAAG,IAAK;EAC9B,MAAM;IAAEkC;EAAK,CAAC,GAAGlC,GAAG;EACpB,MAAMmC,OAAO,GAAGD,IAAI,CAACE,KAAK,CAAC/C,kBAAkB,CAAC,IAAI,EAAE;EACpD,IAAIgD,MAAM,GAAGF,OAAO,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChC,IAAID,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;IACpB;IACAA,MAAM,GAAGA,MAAM,CAACE,OAAO,CAAC,CAAC;EAC7B;EACA,OAAO;IACHf,OAAO,EAAEa,MAAM,CAAC,CAAC,CAAC;IAClBf,MAAM,EAAEe,MAAM,CAAC,CAAC;EACpB,CAAC;AACL,CAAC;AACD;AACA;AACA,MAAMrB,6BAA6B,GAAIQ,OAAO,IAAKA,OAAO,KAAKpC,gBAAgB;AAE/E,SAASE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}