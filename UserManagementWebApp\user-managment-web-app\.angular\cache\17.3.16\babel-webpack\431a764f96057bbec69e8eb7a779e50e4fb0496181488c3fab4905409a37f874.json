{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { getDnsSuffix } from '../../clients/endpoints/getDnsSuffix.mjs';\nimport '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\nimport { unauthenticatedHandler } from '../../clients/handlers/aws/unauthenticated.mjs';\nimport { jitteredBackoff } from '../../clients/middleware/retry/jitteredBackoff.mjs';\nimport { getRetryDecider } from '../../clients/middleware/retry/defaultRetryDecider.mjs';\nimport '../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../utils/retry/retry.mjs';\nimport '../../parseAWSExports.mjs';\nimport 'uuid';\nimport { AmplifyUrl } from '../../utils/amplifyUrl/index.mjs';\nimport '../../singleton/Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport { getAmplifyUserAgent } from '../../Platform/index.mjs';\nimport '../../Platform/types.mjs';\nimport '../../BackgroundProcessManager/types.mjs';\nimport '../../Reachability/Reachability.mjs';\nimport '../../Hub/index.mjs';\nimport '../../utils/sessionListener/index.mjs';\nimport { composeTransferHandler } from '../../clients/internal/composeTransferHandler.mjs';\nimport { parseJsonError } from '../../clients/serde/json.mjs';\nimport { observeFrameworkChanges } from '../../Platform/detectFramework.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * The service name used to sign requests if the API requires authentication.\n */\nconst SERVICE_NAME = 'cognito-identity';\n/**\n * The endpoint resolver function that returns the endpoint URL for a given region.\n */\nconst endpointResolver = ({\n  region\n}) => ({\n  url: new AmplifyUrl(`https://cognito-identity.${region}.${getDnsSuffix(region)}`)\n});\n/**\n * A Cognito Identity-specific middleware that disables caching for all requests.\n */\nconst disableCacheMiddlewareFactory = () => next => (/*#__PURE__*/function () {\n  var _disableCacheMiddleware = _asyncToGenerator(function* (request) {\n    request.headers['cache-control'] = 'no-store';\n    return next(request);\n  });\n  function disableCacheMiddleware(_x) {\n    return _disableCacheMiddleware.apply(this, arguments);\n  }\n  return disableCacheMiddleware;\n}());\n/**\n * A Cognito Identity-specific transfer handler that does NOT sign requests, and\n * disables caching.\n *\n * @internal\n */\nconst cognitoIdentityTransferHandler = composeTransferHandler(unauthenticatedHandler, [disableCacheMiddlewareFactory]);\n/**\n * @internal\n */\nconst defaultConfig = {\n  service: SERVICE_NAME,\n  endpointResolver,\n  retryDecider: getRetryDecider(parseJsonError),\n  computeDelay: jitteredBackoff,\n  userAgentValue: getAmplifyUserAgent(),\n  cache: 'no-store'\n};\nobserveFrameworkChanges(() => {\n  defaultConfig.userAgentValue = getAmplifyUserAgent();\n});\n/**\n * @internal\n */\nconst getSharedHeaders = operation => ({\n  'content-type': 'application/x-amz-json-1.1',\n  'x-amz-target': `AWSCognitoIdentityService.${operation}`\n});\n/**\n * @internal\n */\nconst buildHttpRpcRequest = ({\n  url\n}, headers, body) => ({\n  headers,\n  url,\n  body,\n  method: 'POST'\n});\nexport { buildHttpRpcRequest, cognitoIdentityTransferHandler, defaultConfig, getSharedHeaders };", "map": {"version": 3, "names": ["getDnsSuffix", "unauthentica<PERSON><PERSON><PERSON><PERSON>", "jittered<PERSON><PERSON>off", "getRetryDecider", "AmplifyUrl", "getAmplifyUserAgent", "composeTransferHandler", "parseJsonError", "observeFrameworkChanges", "SERVICE_NAME", "endpointResolver", "region", "url", "disableCacheMiddlewareFactory", "next", "_disableCacheMiddleware", "_asyncToGenerator", "request", "headers", "disableCacheMiddleware", "_x", "apply", "arguments", "cognitoIdentityTransferHandler", "defaultConfig", "service", "retryDecider", "computeDelay", "userAgentValue", "cache", "getSharedHeaders", "operation", "buildHttpRpcRequest", "body", "method"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/awsClients/cognitoIdentity/base.mjs"], "sourcesContent": ["import { getDnsSuffix } from '../../clients/endpoints/getDnsSuffix.mjs';\nimport '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\nimport { unauthenticatedHandler } from '../../clients/handlers/aws/unauthenticated.mjs';\nimport { jitteredBackoff } from '../../clients/middleware/retry/jitteredBackoff.mjs';\nimport { getRetryDecider } from '../../clients/middleware/retry/defaultRetryDecider.mjs';\nimport '../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../utils/retry/retry.mjs';\nimport '../../parseAWSExports.mjs';\nimport 'uuid';\nimport { AmplifyUrl } from '../../utils/amplifyUrl/index.mjs';\nimport '../../singleton/Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport { getAmplifyUserAgent } from '../../Platform/index.mjs';\nimport '../../Platform/types.mjs';\nimport '../../BackgroundProcessManager/types.mjs';\nimport '../../Reachability/Reachability.mjs';\nimport '../../Hub/index.mjs';\nimport '../../utils/sessionListener/index.mjs';\nimport { composeTransferHandler } from '../../clients/internal/composeTransferHandler.mjs';\nimport { parseJsonError } from '../../clients/serde/json.mjs';\nimport { observeFrameworkChanges } from '../../Platform/detectFramework.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * The service name used to sign requests if the API requires authentication.\n */\nconst SERVICE_NAME = 'cognito-identity';\n/**\n * The endpoint resolver function that returns the endpoint URL for a given region.\n */\nconst endpointResolver = ({ region }) => ({\n    url: new AmplifyUrl(`https://cognito-identity.${region}.${getDnsSuffix(region)}`),\n});\n/**\n * A Cognito Identity-specific middleware that disables caching for all requests.\n */\nconst disableCacheMiddlewareFactory = () => next => async function disableCacheMiddleware(request) {\n    request.headers['cache-control'] = 'no-store';\n    return next(request);\n};\n/**\n * A Cognito Identity-specific transfer handler that does NOT sign requests, and\n * disables caching.\n *\n * @internal\n */\nconst cognitoIdentityTransferHandler = composeTransferHandler(unauthenticatedHandler, [disableCacheMiddlewareFactory]);\n/**\n * @internal\n */\nconst defaultConfig = {\n    service: SERVICE_NAME,\n    endpointResolver,\n    retryDecider: getRetryDecider(parseJsonError),\n    computeDelay: jitteredBackoff,\n    userAgentValue: getAmplifyUserAgent(),\n    cache: 'no-store',\n};\nobserveFrameworkChanges(() => {\n    defaultConfig.userAgentValue = getAmplifyUserAgent();\n});\n/**\n * @internal\n */\nconst getSharedHeaders = (operation) => ({\n    'content-type': 'application/x-amz-json-1.1',\n    'x-amz-target': `AWSCognitoIdentityService.${operation}`,\n});\n/**\n * @internal\n */\nconst buildHttpRpcRequest = ({ url }, headers, body) => ({\n    headers,\n    url,\n    body,\n    method: 'POST',\n});\n\nexport { buildHttpRpcRequest, cognitoIdentityTransferHandler, defaultConfig, getSharedHeaders };\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,0CAA0C;AACvE,OAAO,wBAAwB;AAC/B,OAAO,+BAA+B;AACtC,SAASC,sBAAsB,QAAQ,gDAAgD;AACvF,SAASC,eAAe,QAAQ,oDAAoD;AACpF,SAASC,eAAe,QAAQ,wDAAwD;AACxF,OAAO,6CAA6C;AACpD,OAAO,6BAA6B;AACpC,OAAO,2BAA2B;AAClC,OAAO,MAAM;AACb,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,OAAO,6CAA6C;AACpD,OAAO,uBAAuB;AAC9B,OAAO,2BAA2B;AAClC,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,OAAO,0BAA0B;AACjC,OAAO,0CAA0C;AACjD,OAAO,qCAAqC;AAC5C,OAAO,qBAAqB;AAC5B,OAAO,uCAAuC;AAC9C,SAASC,sBAAsB,QAAQ,mDAAmD;AAC1F,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,uBAAuB,QAAQ,oCAAoC;;AAE5E;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,kBAAkB;AACvC;AACA;AACA;AACA,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC;AAAO,CAAC,MAAM;EACtCC,GAAG,EAAE,IAAIR,UAAU,CAAC,4BAA4BO,MAAM,IAAIX,YAAY,CAACW,MAAM,CAAC,EAAE;AACpF,CAAC,CAAC;AACF;AACA;AACA;AACA,MAAME,6BAA6B,GAAGA,CAAA,KAAMC,IAAI;EAAA,IAAAC,uBAAA,GAAAC,iBAAA,CAAI,WAAsCC,OAAO,EAAE;IAC/FA,OAAO,CAACC,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU;IAC7C,OAAOJ,IAAI,CAACG,OAAO,CAAC;EACxB,CAAC;EAAA,SAHkEE,sBAAsBA,CAAAC,EAAA;IAAA,OAAAL,uBAAA,CAAAM,KAAA,OAAAC,SAAA;EAAA;EAAA,OAAtBH,sBAAsB;AAAA,IAGxF;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,8BAA8B,GAAGjB,sBAAsB,CAACL,sBAAsB,EAAE,CAACY,6BAA6B,CAAC,CAAC;AACtH;AACA;AACA;AACA,MAAMW,aAAa,GAAG;EAClBC,OAAO,EAAEhB,YAAY;EACrBC,gBAAgB;EAChBgB,YAAY,EAAEvB,eAAe,CAACI,cAAc,CAAC;EAC7CoB,YAAY,EAAEzB,eAAe;EAC7B0B,cAAc,EAAEvB,mBAAmB,CAAC,CAAC;EACrCwB,KAAK,EAAE;AACX,CAAC;AACDrB,uBAAuB,CAAC,MAAM;EAC1BgB,aAAa,CAACI,cAAc,GAAGvB,mBAAmB,CAAC,CAAC;AACxD,CAAC,CAAC;AACF;AACA;AACA;AACA,MAAMyB,gBAAgB,GAAIC,SAAS,KAAM;EACrC,cAAc,EAAE,4BAA4B;EAC5C,cAAc,EAAE,6BAA6BA,SAAS;AAC1D,CAAC,CAAC;AACF;AACA;AACA;AACA,MAAMC,mBAAmB,GAAGA,CAAC;EAAEpB;AAAI,CAAC,EAAEM,OAAO,EAAEe,IAAI,MAAM;EACrDf,OAAO;EACPN,GAAG;EACHqB,IAAI;EACJC,MAAM,EAAE;AACZ,CAAC,CAAC;AAEF,SAASF,mBAAmB,EAAET,8BAA8B,EAAEC,aAAa,EAAEM,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}