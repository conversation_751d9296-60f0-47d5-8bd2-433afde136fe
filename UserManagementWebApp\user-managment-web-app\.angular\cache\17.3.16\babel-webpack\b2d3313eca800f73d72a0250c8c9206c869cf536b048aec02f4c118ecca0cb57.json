{"ast": null, "code": "import { AmplifyErrorCode } from '../types/errors.mjs';\nimport { createAssertionFunction } from './createAssertionFunction.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst amplifyErrorMap = {\n  [AmplifyErrorCode.NoEndpointId]: {\n    message: 'Endpoint ID was not found and was unable to be created.'\n  },\n  [AmplifyErrorCode.PlatformNotSupported]: {\n    message: 'Function not supported on current platform.'\n  },\n  [AmplifyErrorCode.Unknown]: {\n    message: 'An unknown error occurred.'\n  },\n  [AmplifyErrorCode.NetworkError]: {\n    message: 'A network error has occurred.'\n  }\n};\nconst assert = createAssertionFunction(amplifyErrorMap);\nexport { assert };", "map": {"version": 3, "names": ["AmplifyErrorCode", "createAssertionFunction", "amplifyErrorMap", "NoEndpointId", "message", "PlatformNotSupported", "Unknown", "NetworkError", "assert"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/errors/errorHelpers.mjs"], "sourcesContent": ["import { AmplifyErrorCode } from '../types/errors.mjs';\nimport { createAssertionFunction } from './createAssertionFunction.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst amplifyErrorMap = {\n    [AmplifyErrorCode.NoEndpointId]: {\n        message: 'Endpoint ID was not found and was unable to be created.',\n    },\n    [AmplifyErrorCode.PlatformNotSupported]: {\n        message: 'Function not supported on current platform.',\n    },\n    [AmplifyErrorCode.Unknown]: {\n        message: 'An unknown error occurred.',\n    },\n    [AmplifyErrorCode.NetworkError]: {\n        message: 'A network error has occurred.',\n    },\n};\nconst assert = createAssertionFunction(amplifyErrorMap);\n\nexport { assert };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,uBAAuB,QAAQ,+BAA+B;;AAEvE;AACA;AACA,MAAMC,eAAe,GAAG;EACpB,CAACF,gBAAgB,CAACG,YAAY,GAAG;IAC7BC,OAAO,EAAE;EACb,CAAC;EACD,CAACJ,gBAAgB,CAACK,oBAAoB,GAAG;IACrCD,OAAO,EAAE;EACb,CAAC;EACD,CAACJ,gBAAgB,CAACM,OAAO,GAAG;IACxBF,OAAO,EAAE;EACb,CAAC;EACD,CAACJ,gBAAgB,CAACO,YAAY,GAAG;IAC7BH,OAAO,EAAE;EACb;AACJ,CAAC;AACD,MAAMI,MAAM,GAAGP,uBAAuB,CAACC,eAAe,CAAC;AAEvD,SAASM,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}