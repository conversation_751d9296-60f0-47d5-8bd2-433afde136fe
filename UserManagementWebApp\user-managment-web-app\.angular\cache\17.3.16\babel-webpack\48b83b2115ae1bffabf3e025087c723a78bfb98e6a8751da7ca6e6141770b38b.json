{"ast": null, "code": "const getRoute = (state, actorState) => {\n  // 'federatedSignIn' exists as a state on both the 'signInActor' and 'signUpActor',\n  // match against the `actorState` initially to determine if the federated sign in flow\n  // has begun, then which actor has begun the flow and return the corresponding `route`\n  if (actorState?.matches('federatedSignIn')) {\n    if (state.matches('signUpActor')) {\n      return 'signUp';\n    }\n    if (state.matches('signInActor')) {\n      return 'signIn';\n    }\n  }\n  switch (true) {\n    case state.matches('idle'):\n      return 'idle';\n    case state.matches('setup'):\n      return 'setup';\n    case state.matches('signOut'):\n      return 'signOut';\n    case state.matches('authenticated'):\n      return 'authenticated';\n    case actorState?.matches('confirmSignUp'):\n    case actorState?.matches('resendSignUpCode'):\n      return 'confirmSignUp';\n    case actorState?.matches('confirmSignIn'):\n      return 'confirmSignIn';\n    case actorState?.matches('setupTotp.edit'):\n    case actorState?.matches('setupTotp.submit'):\n      return 'setupTotp';\n    case actorState?.matches('signIn'):\n      return 'signIn';\n    case actorState?.matches('signUp'):\n    case actorState?.matches('autoSignIn'):\n      return 'signUp';\n    case actorState?.matches('forceChangePassword'):\n      return 'forceNewPassword';\n    case actorState?.matches('forgotPassword'):\n      return 'forgotPassword';\n    case actorState?.matches('confirmResetPassword'):\n      return 'confirmResetPassword';\n    case actorState?.matches('selectUserAttributes'):\n      return 'verifyUser';\n    case actorState?.matches('confirmVerifyUserAttribute'):\n      return 'confirmVerifyUser';\n    case actorState?.matches('setupEmail'):\n      return 'setupEmail';\n    case actorState?.matches('selectMfaType'):\n      return 'selectMfaType';\n    case state.matches('getCurrentUser'):\n    case actorState?.matches('fetchUserAttributes'):\n      /**\n       * This route is needed for autoSignIn to capture both the\n       * autoSignIn.pending and the resolved states when the\n       * signIn actor is running.\n       */\n      return 'transition';\n    default:\n      return null;\n  }\n};\nexport { getRoute };", "map": {"version": 3, "names": ["getRoute", "state", "actor<PERSON><PERSON>", "matches"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/helpers/authenticator/getRoute.mjs"], "sourcesContent": ["const getRoute = (state, actorState) => {\n    // 'federatedSignIn' exists as a state on both the 'signInActor' and 'signUpActor',\n    // match against the `actorState` initially to determine if the federated sign in flow\n    // has begun, then which actor has begun the flow and return the corresponding `route`\n    if (actorState?.matches('federatedSignIn')) {\n        if (state.matches('signUpActor')) {\n            return 'signUp';\n        }\n        if (state.matches('signInActor')) {\n            return 'signIn';\n        }\n    }\n    switch (true) {\n        case state.matches('idle'):\n            return 'idle';\n        case state.matches('setup'):\n            return 'setup';\n        case state.matches('signOut'):\n            return 'signOut';\n        case state.matches('authenticated'):\n            return 'authenticated';\n        case actorState?.matches('confirmSignUp'):\n        case actorState?.matches('resendSignUpCode'):\n            return 'confirmSignUp';\n        case actorState?.matches('confirmSignIn'):\n            return 'confirmSignIn';\n        case actorState?.matches('setupTotp.edit'):\n        case actorState?.matches('setupTotp.submit'):\n            return 'setupTotp';\n        case actorState?.matches('signIn'):\n            return 'signIn';\n        case actorState?.matches('signUp'):\n        case actorState?.matches('autoSignIn'):\n            return 'signUp';\n        case actorState?.matches('forceChangePassword'):\n            return 'forceNewPassword';\n        case actorState?.matches('forgotPassword'):\n            return 'forgotPassword';\n        case actorState?.matches('confirmResetPassword'):\n            return 'confirmResetPassword';\n        case actorState?.matches('selectUserAttributes'):\n            return 'verifyUser';\n        case actorState?.matches('confirmVerifyUserAttribute'):\n            return 'confirmVerifyUser';\n        case actorState?.matches('setupEmail'):\n            return 'setupEmail';\n        case actorState?.matches('selectMfaType'):\n            return 'selectMfaType';\n        case state.matches('getCurrentUser'):\n        case actorState?.matches('fetchUserAttributes'):\n            /**\n             * This route is needed for autoSignIn to capture both the\n             * autoSignIn.pending and the resolved states when the\n             * signIn actor is running.\n             */\n            return 'transition';\n        default:\n            return null;\n    }\n};\n\nexport { getRoute };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAGA,CAACC,KAAK,EAAEC,UAAU,KAAK;EACpC;EACA;EACA;EACA,IAAIA,UAAU,EAAEC,OAAO,CAAC,iBAAiB,CAAC,EAAE;IACxC,IAAIF,KAAK,CAACE,OAAO,CAAC,aAAa,CAAC,EAAE;MAC9B,OAAO,QAAQ;IACnB;IACA,IAAIF,KAAK,CAACE,OAAO,CAAC,aAAa,CAAC,EAAE;MAC9B,OAAO,QAAQ;IACnB;EACJ;EACA,QAAQ,IAAI;IACR,KAAKF,KAAK,CAACE,OAAO,CAAC,MAAM,CAAC;MACtB,OAAO,MAAM;IACjB,KAAKF,KAAK,CAACE,OAAO,CAAC,OAAO,CAAC;MACvB,OAAO,OAAO;IAClB,KAAKF,KAAK,CAACE,OAAO,CAAC,SAAS,CAAC;MACzB,OAAO,SAAS;IACpB,KAAKF,KAAK,CAACE,OAAO,CAAC,eAAe,CAAC;MAC/B,OAAO,eAAe;IAC1B,KAAKD,UAAU,EAAEC,OAAO,CAAC,eAAe,CAAC;IACzC,KAAKD,UAAU,EAAEC,OAAO,CAAC,kBAAkB,CAAC;MACxC,OAAO,eAAe;IAC1B,KAAKD,UAAU,EAAEC,OAAO,CAAC,eAAe,CAAC;MACrC,OAAO,eAAe;IAC1B,KAAKD,UAAU,EAAEC,OAAO,CAAC,gBAAgB,CAAC;IAC1C,KAAKD,UAAU,EAAEC,OAAO,CAAC,kBAAkB,CAAC;MACxC,OAAO,WAAW;IACtB,KAAKD,UAAU,EAAEC,OAAO,CAAC,QAAQ,CAAC;MAC9B,OAAO,QAAQ;IACnB,KAAKD,UAAU,EAAEC,OAAO,CAAC,QAAQ,CAAC;IAClC,KAAKD,UAAU,EAAEC,OAAO,CAAC,YAAY,CAAC;MAClC,OAAO,QAAQ;IACnB,KAAKD,UAAU,EAAEC,OAAO,CAAC,qBAAqB,CAAC;MAC3C,OAAO,kBAAkB;IAC7B,KAAKD,UAAU,EAAEC,OAAO,CAAC,gBAAgB,CAAC;MACtC,OAAO,gBAAgB;IAC3B,KAAKD,UAAU,EAAEC,OAAO,CAAC,sBAAsB,CAAC;MAC5C,OAAO,sBAAsB;IACjC,KAAKD,UAAU,EAAEC,OAAO,CAAC,sBAAsB,CAAC;MAC5C,OAAO,YAAY;IACvB,KAAKD,UAAU,EAAEC,OAAO,CAAC,4BAA4B,CAAC;MAClD,OAAO,mBAAmB;IAC9B,KAAKD,UAAU,EAAEC,OAAO,CAAC,YAAY,CAAC;MAClC,OAAO,YAAY;IACvB,KAAKD,UAAU,EAAEC,OAAO,CAAC,eAAe,CAAC;MACrC,OAAO,eAAe;IAC1B,KAAKF,KAAK,CAACE,OAAO,CAAC,gBAAgB,CAAC;IACpC,KAAKD,UAAU,EAAEC,OAAO,CAAC,qBAAqB,CAAC;MAC3C;AACZ;AACA;AACA;AACA;MACY,OAAO,YAAY;IACvB;MACI,OAAO,IAAI;EACnB;AACJ,CAAC;AAED,SAASH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}