{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns a canonical uri.\n *\n * @param pathname `pathname` from request url.\n * @param uriEscapePath Whether to uri encode the path as part of canonical uri. It's used for S3 only where the\n *   pathname is already uri encoded, and the signing process is not expected to uri encode it again. Defaults to true.\n * @returns URI-encoded version of the absolute path component URL (everything between the host and the question mark\n * character (?) that starts the query string parameters). If the absolute path is empty, a forward slash character (/).\n *\n * @internal\n */\nconst getCanonicalUri = (pathname, uriEscapePath = true) => pathname ? uriEscapePath ? encodeURIComponent(pathname).replace(/%2F/g, '/') : pathname : '/';\nexport { getCanonicalUri };", "map": {"version": 3, "names": ["getCanonicalUri", "pathname", "uriEscapePath", "encodeURIComponent", "replace"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/signer/signatureV4/utils/getCanonicalUri.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns a canonical uri.\n *\n * @param pathname `pathname` from request url.\n * @param uriEscapePath Whether to uri encode the path as part of canonical uri. It's used for S3 only where the\n *   pathname is already uri encoded, and the signing process is not expected to uri encode it again. Defaults to true.\n * @returns URI-encoded version of the absolute path component URL (everything between the host and the question mark\n * character (?) that starts the query string parameters). If the absolute path is empty, a forward slash character (/).\n *\n * @internal\n */\nconst getCanonicalUri = (pathname, uriEscapePath = true) => pathname\n    ? uriEscapePath\n        ? encodeURIComponent(pathname).replace(/%2F/g, '/')\n        : pathname\n    : '/';\n\nexport { getCanonicalUri };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,eAAe,GAAGA,CAACC,QAAQ,EAAEC,aAAa,GAAG,IAAI,KAAKD,QAAQ,GAC9DC,aAAa,GACTC,kBAAkB,CAACF,QAAQ,CAAC,CAACG,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,GACjDH,QAAQ,GACZ,GAAG;AAET,SAASD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}