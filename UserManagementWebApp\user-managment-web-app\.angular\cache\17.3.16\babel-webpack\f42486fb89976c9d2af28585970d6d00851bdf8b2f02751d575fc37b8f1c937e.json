{"ast": null, "code": "import { __assign, __awaiter, __generator } from \"tslib\";\nimport { HttpRequest, HttpResponse } from \"@aws-sdk/protocol-http\";\nimport { getSkewCorrectedDate } from \"./utils/getSkewCorrectedDate\";\nimport { getUpdatedSystemClockOffset } from \"./utils/getUpdatedSystemClockOffset\";\nexport var awsAuthMiddleware = function (options) {\n  return function (next, context) {\n    return function (args) {\n      var _a, _b, _c;\n      return __awaiter(this, void 0, void 0, function () {\n        var authScheme, signer, output, _d, _e, dateHeader;\n        var _f;\n        return __generator(this, function (_g) {\n          switch (_g.label) {\n            case 0:\n              if (!HttpRequest.isInstance(args.request)) return [2, next(args)];\n              authScheme = (_c = (_b = (_a = context.endpointV2) === null || _a === void 0 ? void 0 : _a.properties) === null || _b === void 0 ? void 0 : _b.authSchemes) === null || _c === void 0 ? void 0 : _c[0];\n              return [4, options.signer(authScheme)];\n            case 1:\n              signer = _g.sent();\n              _d = next;\n              _e = [__assign({}, args)];\n              _f = {};\n              return [4, signer.sign(args.request, {\n                signingDate: getSkewCorrectedDate(options.systemClockOffset),\n                signingRegion: context[\"signing_region\"],\n                signingService: context[\"signing_service\"]\n              })];\n            case 2:\n              return [4, _d.apply(void 0, [__assign.apply(void 0, _e.concat([(_f.request = _g.sent(), _f)]))]).catch(function (error) {\n                var _a;\n                var serverTime = (_a = error.ServerTime) !== null && _a !== void 0 ? _a : getDateHeader(error.$response);\n                if (serverTime) {\n                  options.systemClockOffset = getUpdatedSystemClockOffset(serverTime, options.systemClockOffset);\n                }\n                throw error;\n              })];\n            case 3:\n              output = _g.sent();\n              dateHeader = getDateHeader(output.response);\n              if (dateHeader) {\n                options.systemClockOffset = getUpdatedSystemClockOffset(dateHeader, options.systemClockOffset);\n              }\n              return [2, output];\n          }\n        });\n      });\n    };\n  };\n};\nvar getDateHeader = function (response) {\n  var _a, _b, _c;\n  return HttpResponse.isInstance(response) ? (_b = (_a = response.headers) === null || _a === void 0 ? void 0 : _a.date) !== null && _b !== void 0 ? _b : (_c = response.headers) === null || _c === void 0 ? void 0 : _c.Date : undefined;\n};\nexport var awsAuthMiddlewareOptions = {\n  name: \"awsAuthMiddleware\",\n  tags: [\"SIGNATURE\", \"AWSAUTH\"],\n  relation: \"after\",\n  toMiddleware: \"retryMiddleware\",\n  override: true\n};\nexport var getAwsAuthPlugin = function (options) {\n  return {\n    applyToStack: function (clientStack) {\n      clientStack.addRelativeTo(awsAuthMiddleware(options), awsAuthMiddlewareOptions);\n    }\n  };\n};\nexport var getSigV4AuthPlugin = getAwsAuthPlugin;", "map": {"version": 3, "names": ["__assign", "__awaiter", "__generator", "HttpRequest", "HttpResponse", "getSkewCorrectedDate", "getUpdatedSystemClockOffset", "awsAuthMiddleware", "options", "next", "context", "args", "_a", "_b", "_c", "authScheme", "signer", "output", "_d", "_e", "<PERSON><PERSON><PERSON><PERSON>", "_f", "_g", "label", "isInstance", "request", "endpointV2", "properties", "authSchemes", "sent", "sign", "signingDate", "systemClockOffset", "signingRegion", "signingService", "apply", "concat", "catch", "error", "serverTime", "ServerTime", "getDateHeader", "$response", "response", "headers", "date", "Date", "undefined", "awsAuthMiddlewareOptions", "name", "tags", "relation", "toMiddleware", "override", "getAwsAuthPlugin", "applyToStack", "clientStack", "addRelativeTo", "getSigV4AuthPlugin"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-lex-runtime-service/node_modules/@aws-sdk/middleware-signing/dist-es/middleware.js"], "sourcesContent": ["import { __assign, __awaiter, __generator } from \"tslib\";\nimport { HttpRequest, HttpResponse } from \"@aws-sdk/protocol-http\";\nimport { getSkewCorrectedDate } from \"./utils/getSkewCorrectedDate\";\nimport { getUpdatedSystemClockOffset } from \"./utils/getUpdatedSystemClockOffset\";\nexport var awsAuthMiddleware = function (options) {\n    return function (next, context) {\n        return function (args) {\n            var _a, _b, _c;\n            return __awaiter(this, void 0, void 0, function () {\n                var authScheme, signer, output, _d, _e, dateHeader;\n                var _f;\n                return __generator(this, function (_g) {\n                    switch (_g.label) {\n                        case 0:\n                            if (!HttpRequest.isInstance(args.request))\n                                return [2, next(args)];\n                            authScheme = (_c = (_b = (_a = (context.endpointV2)) === null || _a === void 0 ? void 0 : _a.properties) === null || _b === void 0 ? void 0 : _b.authSchemes) === null || _c === void 0 ? void 0 : _c[0];\n                            return [4, options.signer(authScheme)];\n                        case 1:\n                            signer = _g.sent();\n                            _d = next;\n                            _e = [__assign({}, args)];\n                            _f = {};\n                            return [4, signer.sign(args.request, {\n                                    signingDate: getSkewCorrectedDate(options.systemClockOffset),\n                                    signingRegion: context[\"signing_region\"],\n                                    signingService: context[\"signing_service\"],\n                                })];\n                        case 2: return [4, _d.apply(void 0, [__assign.apply(void 0, _e.concat([(_f.request = _g.sent(), _f)]))]).catch(function (error) {\n                                var _a;\n                                var serverTime = (_a = error.ServerTime) !== null && _a !== void 0 ? _a : getDateHeader(error.$response);\n                                if (serverTime) {\n                                    options.systemClockOffset = getUpdatedSystemClockOffset(serverTime, options.systemClockOffset);\n                                }\n                                throw error;\n                            })];\n                        case 3:\n                            output = _g.sent();\n                            dateHeader = getDateHeader(output.response);\n                            if (dateHeader) {\n                                options.systemClockOffset = getUpdatedSystemClockOffset(dateHeader, options.systemClockOffset);\n                            }\n                            return [2, output];\n                    }\n                });\n            });\n        };\n    };\n};\nvar getDateHeader = function (response) { var _a, _b, _c; return HttpResponse.isInstance(response) ? (_b = (_a = response.headers) === null || _a === void 0 ? void 0 : _a.date) !== null && _b !== void 0 ? _b : (_c = response.headers) === null || _c === void 0 ? void 0 : _c.Date : undefined; };\nexport var awsAuthMiddlewareOptions = {\n    name: \"awsAuthMiddleware\",\n    tags: [\"SIGNATURE\", \"AWSAUTH\"],\n    relation: \"after\",\n    toMiddleware: \"retryMiddleware\",\n    override: true,\n};\nexport var getAwsAuthPlugin = function (options) { return ({\n    applyToStack: function (clientStack) {\n        clientStack.addRelativeTo(awsAuthMiddleware(options), awsAuthMiddlewareOptions);\n    },\n}); };\nexport var getSigV4AuthPlugin = getAwsAuthPlugin;\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,WAAW,EAAEC,YAAY,QAAQ,wBAAwB;AAClE,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,2BAA2B,QAAQ,qCAAqC;AACjF,OAAO,IAAIC,iBAAiB,GAAG,SAAAA,CAAUC,OAAO,EAAE;EAC9C,OAAO,UAAUC,IAAI,EAAEC,OAAO,EAAE;IAC5B,OAAO,UAAUC,IAAI,EAAE;MACnB,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE;MACd,OAAOb,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;QAC/C,IAAIc,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,UAAU;QAClD,IAAIC,EAAE;QACN,OAAOnB,WAAW,CAAC,IAAI,EAAE,UAAUoB,EAAE,EAAE;UACnC,QAAQA,EAAE,CAACC,KAAK;YACZ,KAAK,CAAC;cACF,IAAI,CAACpB,WAAW,CAACqB,UAAU,CAACb,IAAI,CAACc,OAAO,CAAC,EACrC,OAAO,CAAC,CAAC,EAAEhB,IAAI,CAACE,IAAI,CAAC,CAAC;cAC1BI,UAAU,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAIF,OAAO,CAACgB,UAAW,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,UAAU,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,WAAW,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC;cACxM,OAAO,CAAC,CAAC,EAAEN,OAAO,CAACQ,MAAM,CAACD,UAAU,CAAC,CAAC;YAC1C,KAAK,CAAC;cACFC,MAAM,GAAGM,EAAE,CAACO,IAAI,CAAC,CAAC;cAClBX,EAAE,GAAGT,IAAI;cACTU,EAAE,GAAG,CAACnB,QAAQ,CAAC,CAAC,CAAC,EAAEW,IAAI,CAAC,CAAC;cACzBU,EAAE,GAAG,CAAC,CAAC;cACP,OAAO,CAAC,CAAC,EAAEL,MAAM,CAACc,IAAI,CAACnB,IAAI,CAACc,OAAO,EAAE;gBAC7BM,WAAW,EAAE1B,oBAAoB,CAACG,OAAO,CAACwB,iBAAiB,CAAC;gBAC5DC,aAAa,EAAEvB,OAAO,CAAC,gBAAgB,CAAC;gBACxCwB,cAAc,EAAExB,OAAO,CAAC,iBAAiB;cAC7C,CAAC,CAAC,CAAC;YACX,KAAK,CAAC;cAAE,OAAO,CAAC,CAAC,EAAEQ,EAAE,CAACiB,KAAK,CAAC,KAAK,CAAC,EAAE,CAACnC,QAAQ,CAACmC,KAAK,CAAC,KAAK,CAAC,EAAEhB,EAAE,CAACiB,MAAM,CAAC,EAAEf,EAAE,CAACI,OAAO,GAAGH,EAAE,CAACO,IAAI,CAAC,CAAC,EAAER,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAACgB,KAAK,CAAC,UAAUC,KAAK,EAAE;gBACxH,IAAI1B,EAAE;gBACN,IAAI2B,UAAU,GAAG,CAAC3B,EAAE,GAAG0B,KAAK,CAACE,UAAU,MAAM,IAAI,IAAI5B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG6B,aAAa,CAACH,KAAK,CAACI,SAAS,CAAC;gBACxG,IAAIH,UAAU,EAAE;kBACZ/B,OAAO,CAACwB,iBAAiB,GAAG1B,2BAA2B,CAACiC,UAAU,EAAE/B,OAAO,CAACwB,iBAAiB,CAAC;gBAClG;gBACA,MAAMM,KAAK;cACf,CAAC,CAAC,CAAC;YACP,KAAK,CAAC;cACFrB,MAAM,GAAGK,EAAE,CAACO,IAAI,CAAC,CAAC;cAClBT,UAAU,GAAGqB,aAAa,CAACxB,MAAM,CAAC0B,QAAQ,CAAC;cAC3C,IAAIvB,UAAU,EAAE;gBACZZ,OAAO,CAACwB,iBAAiB,GAAG1B,2BAA2B,CAACc,UAAU,EAAEZ,OAAO,CAACwB,iBAAiB,CAAC;cAClG;cACA,OAAO,CAAC,CAAC,EAAEf,MAAM,CAAC;UAC1B;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC;EACL,CAAC;AACL,CAAC;AACD,IAAIwB,aAAa,GAAG,SAAAA,CAAUE,QAAQ,EAAE;EAAE,IAAI/B,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAAE,OAAOV,YAAY,CAACoB,UAAU,CAACmB,QAAQ,CAAC,GAAG,CAAC9B,EAAE,GAAG,CAACD,EAAE,GAAG+B,QAAQ,CAACC,OAAO,MAAM,IAAI,IAAIhC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiC,IAAI,MAAM,IAAI,IAAIhC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAACC,EAAE,GAAG6B,QAAQ,CAACC,OAAO,MAAM,IAAI,IAAI9B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgC,IAAI,GAAGC,SAAS;AAAE,CAAC;AACrS,OAAO,IAAIC,wBAAwB,GAAG;EAClCC,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;EAC9BC,QAAQ,EAAE,OAAO;EACjBC,YAAY,EAAE,iBAAiB;EAC/BC,QAAQ,EAAE;AACd,CAAC;AACD,OAAO,IAAIC,gBAAgB,GAAG,SAAAA,CAAU9C,OAAO,EAAE;EAAE,OAAQ;IACvD+C,YAAY,EAAE,SAAAA,CAAUC,WAAW,EAAE;MACjCA,WAAW,CAACC,aAAa,CAAClD,iBAAiB,CAACC,OAAO,CAAC,EAAEwC,wBAAwB,CAAC;IACnF;EACJ,CAAC;AAAG,CAAC;AACL,OAAO,IAAIU,kBAAkB,GAAGJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}