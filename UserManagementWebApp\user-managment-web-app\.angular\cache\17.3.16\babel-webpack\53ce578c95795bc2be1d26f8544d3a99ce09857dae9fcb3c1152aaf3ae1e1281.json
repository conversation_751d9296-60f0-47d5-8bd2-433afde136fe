{"ast": null, "code": "const menu = {\n  backgroundColor: {\n    value: '{colors.background.primary.value}'\n  },\n  borderRadius: {\n    value: '{radii.medium.value}'\n  },\n  borderWidth: {\n    value: '{borderWidths.small.value}'\n  },\n  borderStyle: {\n    value: 'solid'\n  },\n  borderColor: {\n    value: '{colors.border.primary.value}'\n  },\n  boxShadow: {\n    value: '{shadows.large.value}'\n  },\n  flexDirection: {\n    value: 'column'\n  },\n  gap: {\n    value: '{space.zero.value}'\n  },\n  maxWidth: {\n    value: '30rem'\n  },\n  minWidth: {\n    value: '14rem'\n  },\n  small: {\n    width: {\n      value: '{fontSizes.medium.value}'\n    },\n    height: {\n      value: '{fontSizes.medium.value}'\n    }\n  },\n  large: {\n    width: {\n      value: '{fontSizes.xxxl.value}'\n    },\n    height: {\n      value: '{fontSizes.xxxl.value}'\n    }\n  },\n  item: {\n    minHeight: {\n      value: '2.5rem'\n    },\n    paddingInlineStart: {\n      value: '{space.medium.value}'\n    },\n    paddingInlineEnd: {\n      value: '{space.medium.value}'\n    }\n  }\n};\nexport { menu };", "map": {"version": 3, "names": ["menu", "backgroundColor", "value", "borderRadius", "borderWidth", "borderStyle", "borderColor", "boxShadow", "flexDirection", "gap", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "small", "width", "height", "large", "item", "minHeight", "paddingInlineStart", "paddingInlineEnd"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/menu.mjs"], "sourcesContent": ["const menu = {\n    backgroundColor: { value: '{colors.background.primary.value}' },\n    borderRadius: { value: '{radii.medium.value}' },\n    borderWidth: { value: '{borderWidths.small.value}' },\n    borderStyle: { value: 'solid' },\n    borderColor: { value: '{colors.border.primary.value}' },\n    boxShadow: { value: '{shadows.large.value}' },\n    flexDirection: { value: 'column' },\n    gap: { value: '{space.zero.value}' },\n    maxWidth: { value: '30rem' },\n    minWidth: { value: '14rem' },\n    small: {\n        width: { value: '{fontSizes.medium.value}' },\n        height: { value: '{fontSizes.medium.value}' },\n    },\n    large: {\n        width: { value: '{fontSizes.xxxl.value}' },\n        height: { value: '{fontSizes.xxxl.value}' },\n    },\n    item: {\n        minHeight: { value: '2.5rem' },\n        paddingInlineStart: { value: '{space.medium.value}' },\n        paddingInlineEnd: { value: '{space.medium.value}' },\n    },\n};\n\nexport { menu };\n"], "mappings": "AAAA,MAAMA,IAAI,GAAG;EACTC,eAAe,EAAE;IAAEC,KAAK,EAAE;EAAoC,CAAC;EAC/DC,YAAY,EAAE;IAAED,KAAK,EAAE;EAAuB,CAAC;EAC/CE,WAAW,EAAE;IAAEF,KAAK,EAAE;EAA6B,CAAC;EACpDG,WAAW,EAAE;IAAEH,KAAK,EAAE;EAAQ,CAAC;EAC/BI,WAAW,EAAE;IAAEJ,KAAK,EAAE;EAAgC,CAAC;EACvDK,SAAS,EAAE;IAAEL,KAAK,EAAE;EAAwB,CAAC;EAC7CM,aAAa,EAAE;IAAEN,KAAK,EAAE;EAAS,CAAC;EAClCO,GAAG,EAAE;IAAEP,KAAK,EAAE;EAAqB,CAAC;EACpCQ,QAAQ,EAAE;IAAER,KAAK,EAAE;EAAQ,CAAC;EAC5BS,QAAQ,EAAE;IAAET,KAAK,EAAE;EAAQ,CAAC;EAC5BU,KAAK,EAAE;IACHC,KAAK,EAAE;MAAEX,KAAK,EAAE;IAA2B,CAAC;IAC5CY,MAAM,EAAE;MAAEZ,KAAK,EAAE;IAA2B;EAChD,CAAC;EACDa,KAAK,EAAE;IACHF,KAAK,EAAE;MAAEX,KAAK,EAAE;IAAyB,CAAC;IAC1CY,MAAM,EAAE;MAAEZ,KAAK,EAAE;IAAyB;EAC9C,CAAC;EACDc,IAAI,EAAE;IACFC,SAAS,EAAE;MAAEf,KAAK,EAAE;IAAS,CAAC;IAC9BgB,kBAAkB,EAAE;MAAEhB,KAAK,EAAE;IAAuB,CAAC;IACrDiB,gBAAgB,EAAE;MAAEjB,KAAK,EAAE;IAAuB;EACtD;AACJ,CAAC;AAED,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}