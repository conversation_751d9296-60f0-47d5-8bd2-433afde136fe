{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\nimport '../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../utils/retry/retry.mjs';\nimport '../../parseAWSExports.mjs';\nimport 'uuid';\nimport '../../singleton/Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport '../../Platform/index.mjs';\nimport '../../Platform/types.mjs';\nimport '../../BackgroundProcessManager/types.mjs';\nimport '../../Reachability/Reachability.mjs';\nimport '../../Hub/index.mjs';\nimport '../../utils/sessionListener/index.mjs';\nimport { parseMetadata } from '../../clients/serde/responseInfo.mjs';\nimport { parseJsonError, parseJsonBody } from '../../clients/serde/json.mjs';\nimport { composeServiceApi } from '../../clients/internal/composeServiceApi.mjs';\nimport { defaultConfig, cognitoIdentityTransferHandler, buildHttpRpcRequest, getSharedHeaders } from './base.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst getIdSerializer = (input, endpoint) => {\n  const headers = getSharedHeaders('GetId');\n  const body = JSON.stringify(input);\n  return buildHttpRpcRequest(endpoint, headers, body);\n};\nconst getIdDeserializer = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (response) {\n    if (response.statusCode >= 300) {\n      const error = yield parseJsonError(response);\n      throw error;\n    } else {\n      const body = yield parseJsonBody(response);\n      return {\n        IdentityId: body.IdentityId,\n        $metadata: parseMetadata(response)\n      };\n    }\n  });\n  return function getIdDeserializer(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\n/**\n * @internal\n */\nconst getId = composeServiceApi(cognitoIdentityTransferHandler, getIdSerializer, getIdDeserializer, defaultConfig);\nexport { getId };", "map": {"version": 3, "names": ["parseMetadata", "parseJsonError", "parseJsonBody", "composeServiceApi", "defaultConfig", "cognitoIdentityTransferHandler", "buildHttpRpcRequest", "getSharedHeaders", "getIdSerializer", "input", "endpoint", "headers", "body", "JSON", "stringify", "getIdDeserializer", "_ref", "_asyncToGenerator", "response", "statusCode", "error", "IdentityId", "$metadata", "_x", "apply", "arguments", "getId"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/awsClients/cognitoIdentity/getId.mjs"], "sourcesContent": ["import '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\nimport '../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../utils/retry/retry.mjs';\nimport '../../parseAWSExports.mjs';\nimport 'uuid';\nimport '../../singleton/Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport '../../Platform/index.mjs';\nimport '../../Platform/types.mjs';\nimport '../../BackgroundProcessManager/types.mjs';\nimport '../../Reachability/Reachability.mjs';\nimport '../../Hub/index.mjs';\nimport '../../utils/sessionListener/index.mjs';\nimport { parseMetadata } from '../../clients/serde/responseInfo.mjs';\nimport { parseJsonError, parseJsonBody } from '../../clients/serde/json.mjs';\nimport { composeServiceApi } from '../../clients/internal/composeServiceApi.mjs';\nimport { defaultConfig, cognitoIdentityTransferHandler, buildHttpRpcRequest, getSharedHeaders } from './base.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst getIdSerializer = (input, endpoint) => {\n    const headers = getSharedHeaders('GetId');\n    const body = JSON.stringify(input);\n    return buildHttpRpcRequest(endpoint, headers, body);\n};\nconst getIdDeserializer = async (response) => {\n    if (response.statusCode >= 300) {\n        const error = await parseJsonError(response);\n        throw error;\n    }\n    else {\n        const body = await parseJsonBody(response);\n        return {\n            IdentityId: body.IdentityId,\n            $metadata: parseMetadata(response),\n        };\n    }\n};\n/**\n * @internal\n */\nconst getId = composeServiceApi(cognitoIdentityTransferHandler, getIdSerializer, getIdDeserializer, defaultConfig);\n\nexport { getId };\n"], "mappings": ";AAAA,OAAO,wBAAwB;AAC/B,OAAO,+BAA+B;AACtC,OAAO,6CAA6C;AACpD,OAAO,6BAA6B;AACpC,OAAO,2BAA2B;AAClC,OAAO,MAAM;AACb,OAAO,6CAA6C;AACpD,OAAO,uBAAuB;AAC9B,OAAO,2BAA2B;AAClC,OAAO,0BAA0B;AACjC,OAAO,0BAA0B;AACjC,OAAO,0CAA0C;AACjD,OAAO,qCAAqC;AAC5C,OAAO,qBAAqB;AAC5B,OAAO,uCAAuC;AAC9C,SAASA,aAAa,QAAQ,sCAAsC;AACpE,SAASC,cAAc,EAAEC,aAAa,QAAQ,8BAA8B;AAC5E,SAASC,iBAAiB,QAAQ,8CAA8C;AAChF,SAASC,aAAa,EAAEC,8BAA8B,EAAEC,mBAAmB,EAAEC,gBAAgB,QAAQ,YAAY;;AAEjH;AACA;AACA,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;EACzC,MAAMC,OAAO,GAAGJ,gBAAgB,CAAC,OAAO,CAAC;EACzC,MAAMK,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACL,KAAK,CAAC;EAClC,OAAOH,mBAAmB,CAACI,QAAQ,EAAEC,OAAO,EAAEC,IAAI,CAAC;AACvD,CAAC;AACD,MAAMG,iBAAiB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,QAAQ,EAAK;IAC1C,IAAIA,QAAQ,CAACC,UAAU,IAAI,GAAG,EAAE;MAC5B,MAAMC,KAAK,SAASnB,cAAc,CAACiB,QAAQ,CAAC;MAC5C,MAAME,KAAK;IACf,CAAC,MACI;MACD,MAAMR,IAAI,SAASV,aAAa,CAACgB,QAAQ,CAAC;MAC1C,OAAO;QACHG,UAAU,EAAET,IAAI,CAACS,UAAU;QAC3BC,SAAS,EAAEtB,aAAa,CAACkB,QAAQ;MACrC,CAAC;IACL;EACJ,CAAC;EAAA,gBAZKH,iBAAiBA,CAAAQ,EAAA;IAAA,OAAAP,IAAA,CAAAQ,KAAA,OAAAC,SAAA;EAAA;AAAA,GAYtB;AACD;AACA;AACA;AACA,MAAMC,KAAK,GAAGvB,iBAAiB,CAACE,8BAA8B,EAAEG,eAAe,EAAEO,iBAAiB,EAAEX,aAAa,CAAC;AAElH,SAASsB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}