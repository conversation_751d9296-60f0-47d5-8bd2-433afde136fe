{"ast": null, "code": "const getFederatedSignInState = target => ({\n  entry: ['sendUpdate', 'clearError'],\n  invoke: {\n    src: 'signInWithRedirect',\n    onDone: {\n      target\n    },\n    onError: {\n      actions: 'setRemoteError',\n      target\n    }\n  }\n});\nconst getConfirmSignInFormValuesKey = signInStep => {\n  if (['CONTINUE_SIGN_IN_WITH_MFA_SELECTION', 'CONTINUE_SIGN_IN_WITH_MFA_SETUP_SELECTION'].includes(signInStep)) {\n    return 'mfa_type';\n  }\n  if (signInStep === 'CONTINUE_SIGN_IN_WITH_EMAIL_SETUP') {\n    return 'email';\n  }\n  return 'confirmation_code';\n};\nexport { getConfirmSignInFormValuesKey, getFederatedSignInState };", "map": {"version": 3, "names": ["getFederatedSignInState", "target", "entry", "invoke", "src", "onDone", "onError", "actions", "getConfirmSignInFormValuesKey", "signInStep", "includes"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/machines/authenticator/actors/utils.mjs"], "sourcesContent": ["const getFederatedSignInState = (target) => ({\n    entry: ['sendUpdate', 'clearError'],\n    invoke: {\n        src: 'signInWithRedirect',\n        onDone: { target },\n        onError: { actions: 'setRemoteError', target },\n    },\n});\nconst getConfirmSignInFormValuesKey = (signInStep) => {\n    if ([\n        'CONTINUE_SIGN_IN_WITH_MFA_SELECTION',\n        'CONTINUE_SIGN_IN_WITH_MFA_SETUP_SELECTION',\n    ].includes(signInStep)) {\n        return 'mfa_type';\n    }\n    if (signInStep === 'CONTINUE_SIGN_IN_WITH_EMAIL_SETUP') {\n        return 'email';\n    }\n    return 'confirmation_code';\n};\n\nexport { getConfirmSignInFormValuesKey, getFederatedSignInState };\n"], "mappings": "AAAA,MAAMA,uBAAuB,GAAIC,MAAM,KAAM;EACzCC,KAAK,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;EACnCC,MAAM,EAAE;IACJC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE;MAAEJ;IAAO,CAAC;IAClBK,OAAO,EAAE;MAAEC,OAAO,EAAE,gBAAgB;MAAEN;IAAO;EACjD;AACJ,CAAC,CAAC;AACF,MAAMO,6BAA6B,GAAIC,UAAU,IAAK;EAClD,IAAI,CACA,qCAAqC,EACrC,2CAA2C,CAC9C,CAACC,QAAQ,CAACD,UAAU,CAAC,EAAE;IACpB,OAAO,UAAU;EACrB;EACA,IAAIA,UAAU,KAAK,mCAAmC,EAAE;IACpD,OAAO,OAAO;EAClB;EACA,OAAO,mBAAmB;AAC9B,CAAC;AAED,SAASD,6BAA6B,EAAER,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}