{"ast": null, "code": "import { ConsoleLogger } from '../Logger/ConsoleLogger.mjs';\nimport { I18n as I18n$1 } from './I18n.mjs';\nimport { assert, I18nErrorCode } from './errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('I18n');\nlet _config = {\n  language: null\n};\nlet _i18n = null;\n/**\n * Export I18n APIs\n */\nclass I18n {\n  /**\n   * @static\n   * @method\n   * Configure I18n part\n   * @param {Object} config - Configuration of the I18n\n   */\n  static configure(config) {\n    logger.debug('configure I18n');\n    if (!config) {\n      return _config;\n    }\n    _config = Object.assign({}, _config, config.I18n || config);\n    I18n.createInstance();\n    return _config;\n  }\n  static getModuleName() {\n    return 'I18n';\n  }\n  /**\n   * @static\n   * @method\n   * Create an instance of I18n for the library\n   */\n  static createInstance() {\n    logger.debug('create I18n instance');\n    if (_i18n) {\n      return;\n    }\n    _i18n = new I18n$1();\n  }\n  /**\n   * @static @method\n   * Explicitly setting language\n   * @param {String} lang\n   */\n  static setLanguage(lang) {\n    I18n.checkConfig();\n    assert(!!_i18n, I18nErrorCode.NotConfigured);\n    _i18n.setLanguage(lang);\n  }\n  /**\n   * @static @method\n   * Get value\n   * @param {String} key\n   * @param {String} defVal - Default value\n   */\n  static get(key, defVal) {\n    if (!I18n.checkConfig()) {\n      return typeof defVal === 'undefined' ? key : defVal;\n    }\n    assert(!!_i18n, I18nErrorCode.NotConfigured);\n    return _i18n.get(key, defVal);\n  }\n  /**\n   * @static\n   * @method\n   * Add vocabularies for one language\n   * @param {String} language - Language of the dictionary\n   * @param {Object} vocabularies - Object that has key-value as dictionary entry\n   */\n  static putVocabulariesForLanguage(language, vocabularies) {\n    I18n.checkConfig();\n    assert(!!_i18n, I18nErrorCode.NotConfigured);\n    _i18n.putVocabulariesForLanguage(language, vocabularies);\n  }\n  /**\n   * @static\n   * @method\n   * Add vocabularies for one language\n   * @param {Object} vocabularies - Object that has language as key,\n   *                                vocabularies of each language as value\n   */\n  static putVocabularies(vocabularies) {\n    I18n.checkConfig();\n    assert(!!_i18n, I18nErrorCode.NotConfigured);\n    _i18n.putVocabularies(vocabularies);\n  }\n  static checkConfig() {\n    if (!_i18n) {\n      I18n.createInstance();\n    }\n    return true;\n  }\n}\n// Create an instance of I18n in the static class\nI18n.createInstance();\nexport { I18n };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "I18n", "I18n$1", "assert", "I18nErrorCode", "logger", "_config", "language", "_i18n", "configure", "config", "debug", "Object", "assign", "createInstance", "getModuleName", "setLanguage", "lang", "checkConfig", "NotConfigured", "get", "key", "defVal", "putVocabulariesForLanguage", "vocabularies", "putVocabularies"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/I18n/index.mjs"], "sourcesContent": ["import { ConsoleLogger } from '../Logger/ConsoleLogger.mjs';\nimport { I18n as I18n$1 } from './I18n.mjs';\nimport { assert, I18nErrorCode } from './errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('I18n');\nlet _config = { language: null };\nlet _i18n = null;\n/**\n * Export I18n APIs\n */\nclass I18n {\n    /**\n     * @static\n     * @method\n     * Configure I18n part\n     * @param {Object} config - Configuration of the I18n\n     */\n    static configure(config) {\n        logger.debug('configure I18n');\n        if (!config) {\n            return _config;\n        }\n        _config = Object.assign({}, _config, config.I18n || config);\n        I18n.createInstance();\n        return _config;\n    }\n    static getModuleName() {\n        return 'I18n';\n    }\n    /**\n     * @static\n     * @method\n     * Create an instance of I18n for the library\n     */\n    static createInstance() {\n        logger.debug('create I18n instance');\n        if (_i18n) {\n            return;\n        }\n        _i18n = new I18n$1();\n    }\n    /**\n     * @static @method\n     * Explicitly setting language\n     * @param {String} lang\n     */\n    static setLanguage(lang) {\n        I18n.checkConfig();\n        assert(!!_i18n, I18nErrorCode.NotConfigured);\n        _i18n.setLanguage(lang);\n    }\n    /**\n     * @static @method\n     * Get value\n     * @param {String} key\n     * @param {String} defVal - Default value\n     */\n    static get(key, defVal) {\n        if (!I18n.checkConfig()) {\n            return typeof defVal === 'undefined' ? key : defVal;\n        }\n        assert(!!_i18n, I18nErrorCode.NotConfigured);\n        return _i18n.get(key, defVal);\n    }\n    /**\n     * @static\n     * @method\n     * Add vocabularies for one language\n     * @param {String} language - Language of the dictionary\n     * @param {Object} vocabularies - Object that has key-value as dictionary entry\n     */\n    static putVocabulariesForLanguage(language, vocabularies) {\n        I18n.checkConfig();\n        assert(!!_i18n, I18nErrorCode.NotConfigured);\n        _i18n.putVocabulariesForLanguage(language, vocabularies);\n    }\n    /**\n     * @static\n     * @method\n     * Add vocabularies for one language\n     * @param {Object} vocabularies - Object that has language as key,\n     *                                vocabularies of each language as value\n     */\n    static putVocabularies(vocabularies) {\n        I18n.checkConfig();\n        assert(!!_i18n, I18nErrorCode.NotConfigured);\n        _i18n.putVocabularies(vocabularies);\n    }\n    static checkConfig() {\n        if (!_i18n) {\n            I18n.createInstance();\n        }\n        return true;\n    }\n}\n// Create an instance of I18n in the static class\nI18n.createInstance();\n\nexport { I18n };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,IAAI,IAAIC,MAAM,QAAQ,YAAY;AAC3C,SAASC,MAAM,EAAEC,aAAa,QAAQ,oBAAoB;;AAE1D;AACA;AACA,MAAMC,MAAM,GAAG,IAAIL,aAAa,CAAC,MAAM,CAAC;AACxC,IAAIM,OAAO,GAAG;EAAEC,QAAQ,EAAE;AAAK,CAAC;AAChC,IAAIC,KAAK,GAAG,IAAI;AAChB;AACA;AACA;AACA,MAAMP,IAAI,CAAC;EACP;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOQ,SAASA,CAACC,MAAM,EAAE;IACrBL,MAAM,CAACM,KAAK,CAAC,gBAAgB,CAAC;IAC9B,IAAI,CAACD,MAAM,EAAE;MACT,OAAOJ,OAAO;IAClB;IACAA,OAAO,GAAGM,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,OAAO,EAAEI,MAAM,CAACT,IAAI,IAAIS,MAAM,CAAC;IAC3DT,IAAI,CAACa,cAAc,CAAC,CAAC;IACrB,OAAOR,OAAO;EAClB;EACA,OAAOS,aAAaA,CAAA,EAAG;IACnB,OAAO,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOD,cAAcA,CAAA,EAAG;IACpBT,MAAM,CAACM,KAAK,CAAC,sBAAsB,CAAC;IACpC,IAAIH,KAAK,EAAE;MACP;IACJ;IACAA,KAAK,GAAG,IAAIN,MAAM,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;EACI,OAAOc,WAAWA,CAACC,IAAI,EAAE;IACrBhB,IAAI,CAACiB,WAAW,CAAC,CAAC;IAClBf,MAAM,CAAC,CAAC,CAACK,KAAK,EAAEJ,aAAa,CAACe,aAAa,CAAC;IAC5CX,KAAK,CAACQ,WAAW,CAACC,IAAI,CAAC;EAC3B;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOG,GAAGA,CAACC,GAAG,EAAEC,MAAM,EAAE;IACpB,IAAI,CAACrB,IAAI,CAACiB,WAAW,CAAC,CAAC,EAAE;MACrB,OAAO,OAAOI,MAAM,KAAK,WAAW,GAAGD,GAAG,GAAGC,MAAM;IACvD;IACAnB,MAAM,CAAC,CAAC,CAACK,KAAK,EAAEJ,aAAa,CAACe,aAAa,CAAC;IAC5C,OAAOX,KAAK,CAACY,GAAG,CAACC,GAAG,EAAEC,MAAM,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,0BAA0BA,CAAChB,QAAQ,EAAEiB,YAAY,EAAE;IACtDvB,IAAI,CAACiB,WAAW,CAAC,CAAC;IAClBf,MAAM,CAAC,CAAC,CAACK,KAAK,EAAEJ,aAAa,CAACe,aAAa,CAAC;IAC5CX,KAAK,CAACe,0BAA0B,CAAChB,QAAQ,EAAEiB,YAAY,CAAC;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,eAAeA,CAACD,YAAY,EAAE;IACjCvB,IAAI,CAACiB,WAAW,CAAC,CAAC;IAClBf,MAAM,CAAC,CAAC,CAACK,KAAK,EAAEJ,aAAa,CAACe,aAAa,CAAC;IAC5CX,KAAK,CAACiB,eAAe,CAACD,YAAY,CAAC;EACvC;EACA,OAAON,WAAWA,CAAA,EAAG;IACjB,IAAI,CAACV,KAAK,EAAE;MACRP,IAAI,CAACa,cAAc,CAAC,CAAC;IACzB;IACA,OAAO,IAAI;EACf;AACJ;AACA;AACAb,IAAI,CAACa,cAAc,CAAC,CAAC;AAErB,SAASb,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}