{"ast": null, "code": "import { fromUtf8 as jsFromUtf8, toUtf8 as jsToUtf8 } from \"./pureJs\";\nimport { fromUtf8 as textEncoderFromUtf8, toUtf8 as textEncoderToUtf8 } from \"./whatwgEncodingApi\";\nexport const fromUtf8 = input => typeof TextEncoder === \"function\" ? textEncoderFromUtf8(input) : jsFromUtf8(input);\nexport const toUtf8 = input => typeof TextDecoder === \"function\" ? textEncoderToUtf8(input) : jsToUtf8(input);", "map": {"version": 3, "names": ["fromUtf8", "jsFromUtf8", "toUtf8", "jsToUtf8", "textEncoderFromUtf8", "textEncoderToUtf8", "input", "TextEncoder", "TextDecoder"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/util-utf8-browser/dist-es/index.js"], "sourcesContent": ["import { fromUtf8 as jsFromUtf8, toUtf8 as jsToUtf8 } from \"./pureJs\";\nimport { fromUtf8 as textEncoderFromUtf8, toUtf8 as textEncoderToUtf8 } from \"./whatwgEncodingApi\";\nexport const fromUtf8 = (input) => typeof TextEncoder === \"function\" ? textEncoderFromUtf8(input) : jsFromUtf8(input);\nexport const toUtf8 = (input) => typeof TextDecoder === \"function\" ? textEncoderToUtf8(input) : jsToUtf8(input);\n"], "mappings": "AAAA,SAASA,QAAQ,IAAIC,UAAU,EAAEC,MAAM,IAAIC,QAAQ,QAAQ,UAAU;AACrE,SAASH,QAAQ,IAAII,mBAAmB,EAAEF,MAAM,IAAIG,iBAAiB,QAAQ,qBAAqB;AAClG,OAAO,MAAML,QAAQ,GAAIM,KAAK,IAAK,OAAOC,WAAW,KAAK,UAAU,GAAGH,mBAAmB,CAACE,KAAK,CAAC,GAAGL,UAAU,CAACK,KAAK,CAAC;AACrH,OAAO,MAAMJ,MAAM,GAAII,KAAK,IAAK,OAAOE,WAAW,KAAK,UAAU,GAAGH,iBAAiB,CAACC,KAAK,CAAC,GAAGH,QAAQ,CAACG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}