import { Component, EventEmitter, Input, OnInit, Output, ViewChild, OnChanges, SimpleChanges } from '@angular/core';
import { FileInfo } from '../models/file-info';
import { FileUploadDetails } from '../../shared/models/file-upload-details';
import moment from 'moment/moment';
import { HttpEventType } from '@angular/common/http';
import { FileUpload, FileUploadModule } from 'primeng/fileupload';
import { NotificationService } from 'src/app/core/services/notification.service';
import { FileUploadService } from './services/file-upload.service';
import { UserPermissionLevels } from 'src/app/core/enums/shared';
import { TooltipModule } from 'primeng/tooltip';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { SharedModule } from 'primeng/api';
import { NgClass, NgTemplateOutlet } from '@angular/common';
import { PanelModule } from 'primeng/panel';
import { take } from 'rxjs/operators';

@Component({
  selector: 'app-file-upload',
  templateUrl: './file-upload.component.html',
  styleUrls: ['./file-upload.component.scss'],
  standalone: true,
  imports: [NgClass, FileUploadModule, SharedModule, TableModule, ButtonModule, TooltipModule, PanelModule, NgTemplateOutlet]
})
export class FileUploadComponent implements OnInit, OnChanges {

  @Input() uploadedFiles: FileInfo[] = [];
  @Input() maximumFileSize = 5242880; // Maximum file size for each file
  @Input() multipleAllowed = true; // Defines the number of uploads allowed to upload
  @Input() fileToAccept = '.pdf,.xls,.xlsx,.doc,.csv,.docx,.txt,.ppt,.pptx,.jpeg,.jpg,.gif,.png'; // File Types to Accept
  @Input() fileUploadContainerClass = 'default-fileUpload';  // Customized Default container DIV wrapper-class
  @Input() projectId = '';
  @Input() versionId = '';
  @Input() dataStoreName = '';
  @Input() recordId = '';
  @Input() dateFormat = 'YYYY/MM/DD';
  @Input() aliasIds: string[] = [];
  @Input() fileService: FileUploadService;
  @Input() isPreview = false;
  @Input() userPermissionLevel: UserPermissionLevels = UserPermissionLevels.EditAndDelete;

  @Output() fileUploadEvent: EventEmitter<any> = new EventEmitter();
  @Output() getFilesUploaded: EventEmitter<FileInfo[]> = new EventEmitter();

  @ViewChild('fileUploader') fileUploader: FileUpload;

  loadingFiles = false;
  tableStyle = { 'table-layout': 'auto', width: '100%' };
  previewAttachmentInfo: { url: string, fileType: string, fileName: string };
  imageFileTypes = ['image/jpg', 'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/tiff', 'image/webp', 'image/svg+xml', 'image/x-icon', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp', 'svg+xml', 'x-icon', 'svg', 'ico'];

  constructor(private messageSvc: NotificationService) { }

  ngOnInit(): void {
    if (this.aliasIds) {
      this.getFilesFromServer();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.aliasIds && !changes.aliasIds?.isFirstChange()) {
      this.getFilesFromServer();
    }
  }

  onFileSelectClick(event): void {
    const currentFiles: File[] = event.currentFiles;
    this.processSelectedFiles(currentFiles).then(res => {
      this.aliasIds = this.uploadedFiles.map(file => {
        return file.aliasId;
      });

      this.fileUploadEvent.emit(this.aliasIds);
      this.fileUploader.clear();
    });
  }

  async processSelectedFiles(files: File[]) {
    for (const file of files) {
      const fileUploadDetails: FileUploadDetails = new FileUploadDetails(this.projectId, this.versionId,
        this.recordId, this.dataStoreName, file.name, file);
      await this.saveFile(fileUploadDetails);
    }
  }

  saveFile(fileUploadDetails: FileUploadDetails) {
    return new Promise<void>(resolve => {
      this.fileService.uploadFile(fileUploadDetails).subscribe(
        (fileInfo: FileInfo) => {
          fileInfo.uploadedDate = moment(fileInfo.uploadedDate, this.dateFormat, false)
            .format(this.dateFormat).toString();
          this.uploadedFiles.push(fileInfo);
          resolve();
        }, error => {
          console.log(error);
          resolve();
        });
    });
  }

  getFilesFromServer(): void {

    this.loadingFiles = true;

    const fileUploadDetails: FileUploadDetails = new FileUploadDetails(
      this.projectId, this.versionId, this.recordId, this.dataStoreName);
    fileUploadDetails.aliasIds = this.aliasIds;
    // start rewriting some things here
    const getFilesCall = async () => {
      if (this.recordId) {
        await this.getFileInfo(fileUploadDetails);
      }
    };

    getFilesCall().then(res => {
      this.loadingFiles = false;
    });
  }

  getFileInfo(fileUploadDetails: FileUploadDetails) {
    return new Promise<void>(resolve => {
      this.fileService.getFiles(fileUploadDetails)
        .pipe(take(1))
        .subscribe({
          next: (filesInfo: FileInfo[]) => {
            filesInfo.forEach(fileUpload => {
              fileUpload.uploadedDate = moment(fileUpload.uploadedDate, this.dateFormat, false)
                .format(this.dateFormat).toString();
            });
            this.uploadedFiles = filesInfo;
            resolve();
          },
          error: (error) => {
            console.log(`File Request Error: ${error.statusText}...`);
            resolve();
          }
        });
    });
  }

  onDownloadFileClick(attchmentInfo: FileInfo) {
    const fileUploadDetails: FileUploadDetails = new FileUploadDetails(this.projectId, this.versionId,
      this.recordId, this.dataStoreName, attchmentInfo.name);
    fileUploadDetails.aliasId = attchmentInfo.aliasId;

    this.fileService.getFileContent(fileUploadDetails)
      .pipe(take(1))
      .subscribe({
        next: (event) => {
          if (event.type === HttpEventType.Response) {
            const targetFileElement = document.createElement('a');
            targetFileElement.setAttribute('style', 'display:none;');
            document.body.appendChild(targetFileElement);
            targetFileElement.href = URL.createObjectURL(event.body);
            targetFileElement.download = `${attchmentInfo.name}.${attchmentInfo.fileShareFileType}`;
            targetFileElement.target = '_blank';
            targetFileElement.click();
            document.body.removeChild(targetFileElement);
          }
        },
        error: (error) => {
          console.log(`File Download Error: ${error.status}: Unable to complete request...`);
        }
      });
  }

  onPreviewFileClick(attchmentInfo: FileInfo) {
    const fileUploadDetails: FileUploadDetails = new FileUploadDetails(this.projectId, this.versionId,
      this.recordId, this.dataStoreName, attchmentInfo.name);
    fileUploadDetails.aliasId = attchmentInfo.aliasId;

    this.fileService.getFileContent(fileUploadDetails)
      .pipe(take(1))
      .subscribe({
        next: (event) => {
          if (event.type === HttpEventType.Response) {
            const url = URL.createObjectURL(event.body);
            this.previewAttachmentInfo = { url, fileType: attchmentInfo.fileShareFileType, fileName: attchmentInfo.name };
            console.log(this.previewAttachmentInfo);
          }
        },
        error: (error) => {
          console.log(`File Download Error: ${error.status}: Unable to complete request...`);
        }
      });
  }

  onRemoveFileClick(fileInfo: FileInfo) {
    const fileUploadDetails: FileUploadDetails = new FileUploadDetails(this.projectId, this.versionId,
      this.recordId, this.dataStoreName, fileInfo.name);
    fileUploadDetails.aliasId = fileInfo.aliasId;

    this.fileService.deleteFile(fileUploadDetails)
      .pipe(take(1))
      .subscribe({
        next: () => {
          const fileIndex = this.uploadedFiles.findIndex(file => file.aliasId === fileInfo.aliasId);

          if (fileIndex >= 0) {
            this.uploadedFiles.splice(fileIndex, 1);
            this.fileUploadEvent.emit(this.uploadedFiles.map(f => f.aliasId));
          }
        },
        error: () => {
          this.messageSvc.showError('Error', `There was a problem deleting '${fileInfo.name}' from the server`);
        }
      });
  }
}
