{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\nimport { ListTagsForResourceRequestFilterSensitiveLog, ListTagsForResourceResponseFilterSensitiveLog } from \"../models/models_0\";\nimport { deserializeAws_restJson1ListTagsForResourceCommand, serializeAws_restJson1ListTagsForResourceCommand } from \"../protocols/Aws_restJson1\";\nvar ListTagsForResourceCommand = function (_super) {\n  __extends(ListTagsForResourceCommand, _super);\n  function ListTagsForResourceCommand(input) {\n    var _this = _super.call(this) || this;\n    _this.input = input;\n    return _this;\n  }\n  ListTagsForResourceCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n    this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n    var stack = clientStack.concat(this.middlewareStack);\n    var logger = configuration.logger;\n    var clientName = \"LocationClient\";\n    var commandName = \"ListTagsForResourceCommand\";\n    var handlerExecutionContext = {\n      logger: logger,\n      clientName: clientName,\n      commandName: commandName,\n      inputFilterSensitiveLog: ListTagsForResourceRequestFilterSensitiveLog,\n      outputFilterSensitiveLog: ListTagsForResourceResponseFilterSensitiveLog\n    };\n    var requestHandler = configuration.requestHandler;\n    return stack.resolve(function (request) {\n      return requestHandler.handle(request.request, options || {});\n    }, handlerExecutionContext);\n  };\n  ListTagsForResourceCommand.prototype.serialize = function (input, context) {\n    return serializeAws_restJson1ListTagsForResourceCommand(input, context);\n  };\n  ListTagsForResourceCommand.prototype.deserialize = function (output, context) {\n    return deserializeAws_restJson1ListTagsForResourceCommand(output, context);\n  };\n  return ListTagsForResourceCommand;\n}($Command);\nexport { ListTagsForResourceCommand };", "map": {"version": 3, "names": ["__extends", "getSerdePlugin", "Command", "$Command", "ListTagsForResourceRequestFilterSensitiveLog", "ListTagsForResourceResponseFilterSensitiveLog", "deserializeAws_restJson1ListTagsForResourceCommand", "serializeAws_restJson1ListTagsForResourceCommand", "ListTagsForResourceCommand", "_super", "input", "_this", "call", "prototype", "resolveMiddleware", "clientStack", "configuration", "options", "middlewareStack", "use", "serialize", "deserialize", "stack", "concat", "logger", "clientName", "commandName", "handlerExecutionContext", "inputFilterSensitiveLog", "outputFilterSensitiveLog", "requestHandler", "resolve", "request", "handle", "context", "output"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-location/dist-es/commands/ListTagsForResourceCommand.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\nimport { ListTagsForResourceRequestFilterSensitiveLog, ListTagsForResourceResponseFilterSensitiveLog, } from \"../models/models_0\";\nimport { deserializeAws_restJson1ListTagsForResourceCommand, serializeAws_restJson1ListTagsForResourceCommand, } from \"../protocols/Aws_restJson1\";\nvar ListTagsForResourceCommand = (function (_super) {\n    __extends(ListTagsForResourceCommand, _super);\n    function ListTagsForResourceCommand(input) {\n        var _this = _super.call(this) || this;\n        _this.input = input;\n        return _this;\n    }\n    ListTagsForResourceCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n        this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n        var stack = clientStack.concat(this.middlewareStack);\n        var logger = configuration.logger;\n        var clientName = \"LocationClient\";\n        var commandName = \"ListTagsForResourceCommand\";\n        var handlerExecutionContext = {\n            logger: logger,\n            clientName: clientName,\n            commandName: commandName,\n            inputFilterSensitiveLog: ListTagsForResourceRequestFilterSensitiveLog,\n            outputFilterSensitiveLog: ListTagsForResourceResponseFilterSensitiveLog,\n        };\n        var requestHandler = configuration.requestHandler;\n        return stack.resolve(function (request) {\n            return requestHandler.handle(request.request, options || {});\n        }, handlerExecutionContext);\n    };\n    ListTagsForResourceCommand.prototype.serialize = function (input, context) {\n        return serializeAws_restJson1ListTagsForResourceCommand(input, context);\n    };\n    ListTagsForResourceCommand.prototype.deserialize = function (output, context) {\n        return deserializeAws_restJson1ListTagsForResourceCommand(output, context);\n    };\n    return ListTagsForResourceCommand;\n}($Command));\nexport { ListTagsForResourceCommand };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,OAAO,IAAIC,QAAQ,QAAQ,wBAAwB;AAC5D,SAASC,4CAA4C,EAAEC,6CAA6C,QAAS,oBAAoB;AACjI,SAASC,kDAAkD,EAAEC,gDAAgD,QAAS,4BAA4B;AAClJ,IAAIC,0BAA0B,GAAI,UAAUC,MAAM,EAAE;EAChDT,SAAS,CAACQ,0BAA0B,EAAEC,MAAM,CAAC;EAC7C,SAASD,0BAA0BA,CAACE,KAAK,EAAE;IACvC,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACD,KAAK,GAAGA,KAAK;IACnB,OAAOC,KAAK;EAChB;EACAH,0BAA0B,CAACK,SAAS,CAACC,iBAAiB,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAEC,OAAO,EAAE;IACpG,IAAI,CAACC,eAAe,CAACC,GAAG,CAAClB,cAAc,CAACe,aAAa,EAAE,IAAI,CAACI,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;IACzF,IAAIC,KAAK,GAAGP,WAAW,CAACQ,MAAM,CAAC,IAAI,CAACL,eAAe,CAAC;IACpD,IAAIM,MAAM,GAAGR,aAAa,CAACQ,MAAM;IACjC,IAAIC,UAAU,GAAG,gBAAgB;IACjC,IAAIC,WAAW,GAAG,4BAA4B;IAC9C,IAAIC,uBAAuB,GAAG;MAC1BH,MAAM,EAAEA,MAAM;MACdC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA,WAAW;MACxBE,uBAAuB,EAAExB,4CAA4C;MACrEyB,wBAAwB,EAAExB;IAC9B,CAAC;IACD,IAAIyB,cAAc,GAAGd,aAAa,CAACc,cAAc;IACjD,OAAOR,KAAK,CAACS,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpC,OAAOF,cAAc,CAACG,MAAM,CAACD,OAAO,CAACA,OAAO,EAAEf,OAAO,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC,EAAEU,uBAAuB,CAAC;EAC/B,CAAC;EACDnB,0BAA0B,CAACK,SAAS,CAACO,SAAS,GAAG,UAAUV,KAAK,EAAEwB,OAAO,EAAE;IACvE,OAAO3B,gDAAgD,CAACG,KAAK,EAAEwB,OAAO,CAAC;EAC3E,CAAC;EACD1B,0BAA0B,CAACK,SAAS,CAACQ,WAAW,GAAG,UAAUc,MAAM,EAAED,OAAO,EAAE;IAC1E,OAAO5B,kDAAkD,CAAC6B,MAAM,EAAED,OAAO,CAAC;EAC9E,CAAC;EACD,OAAO1B,0BAA0B;AACrC,CAAC,CAACL,QAAQ,CAAE;AACZ,SAASK,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}