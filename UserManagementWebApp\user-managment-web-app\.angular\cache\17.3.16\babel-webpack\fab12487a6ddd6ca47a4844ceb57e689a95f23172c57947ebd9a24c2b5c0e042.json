{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../types/errors.mjs';\nimport { createVerifyUserAttributeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createVerifyUserAttributeClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Confirms a user attribute with the confirmation code.\n *\n * @param input -  The ConfirmUserAttributeInput object\n * @throws  -{@link AuthValidationErrorCode } -\n * Thrown when `confirmationCode` is not defined.\n * @throws  -{@link VerifyUserAttributeException } - Thrown due to an invalid confirmation code or attribute.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nfunction confirmUserAttribute(_x) {\n  return _confirmUserAttribute.apply(this, arguments);\n}\nfunction _confirmUserAttribute() {\n  _confirmUserAttribute = _asyncToGenerator(function* (input) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolEndpoint,\n      userPoolId\n    } = authConfig;\n    const {\n      confirmationCode,\n      userAttributeKey\n    } = input;\n    assertValidationError(!!confirmationCode, AuthValidationErrorCode.EmptyConfirmUserAttributeCode);\n    const {\n      tokens\n    } = yield fetchAuthSession({\n      forceRefresh: false\n    });\n    assertAuthTokens(tokens);\n    const verifyUserAttribute = createVerifyUserAttributeClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    yield verifyUserAttribute({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmUserAttribute)\n    }, {\n      AccessToken: tokens.accessToken.toString(),\n      AttributeName: userAttributeKey,\n      Code: confirmationCode\n    });\n  });\n  return _confirmUserAttribute.apply(this, arguments);\n}\nexport { confirmUserAttribute };", "map": {"version": 3, "names": ["Amplify", "fetchAuthSession", "assertTokenProviderConfig", "AuthAction", "AuthValidationErrorCode", "assertValidationError", "getRegionFromUserPoolId", "assertAuthTokens", "getAuthUserAgentValue", "createVerifyUserAttributeClient", "createCognitoUserPoolEndpointResolver", "confirmUserAttribute", "_x", "_confirmUserAttribute", "apply", "arguments", "_asyncToGenerator", "input", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userPoolEndpoint", "userPoolId", "confirmationCode", "userAttributeKey", "EmptyConfirmUserAttributeCode", "tokens", "forceRefresh", "verifyUserAttribute", "endpointResolver", "endpointOverride", "region", "userAgentValue", "ConfirmUserAttribute", "AccessToken", "accessToken", "toString", "AttributeName", "Code"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/confirmUserAttribute.mjs"], "sourcesContent": ["import { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../types/errors.mjs';\nimport { createVerifyUserAttributeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createVerifyUserAttributeClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Confirms a user attribute with the confirmation code.\n *\n * @param input -  The ConfirmUserAttributeInput object\n * @throws  -{@link AuthValidationErrorCode } -\n * Thrown when `confirmationCode` is not defined.\n * @throws  -{@link VerifyUserAttributeException } - Thrown due to an invalid confirmation code or attribute.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nasync function confirmUserAttribute(input) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolEndpoint, userPoolId } = authConfig;\n    const { confirmationCode, userAttributeKey } = input;\n    assertValidationError(!!confirmationCode, AuthValidationErrorCode.EmptyConfirmUserAttributeCode);\n    const { tokens } = await fetchAuthSession({ forceRefresh: false });\n    assertAuthTokens(tokens);\n    const verifyUserAttribute = createVerifyUserAttributeClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    await verifyUserAttribute({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmUserAttribute),\n    }, {\n        AccessToken: tokens.accessToken.toString(),\n        AttributeName: userAttributeKey,\n        Code: confirmationCode,\n    });\n}\n\nexport { confirmUserAttribute };\n"], "mappings": ";AAAA,SAASA,OAAO,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC7D,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,qBAAqB;AAC5B,SAASC,+BAA+B,QAAQ,0GAA0G;AAC1J,SAASC,qCAAqC,QAAQ,wDAAwD;;AAE9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SASeC,oBAAoBA,CAAAC,EAAA;EAAA,OAAAC,qBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,sBAAA;EAAAA,qBAAA,GAAAG,iBAAA,CAAnC,WAAoCC,KAAK,EAAE;IACvC,MAAMC,UAAU,GAAGlB,OAAO,CAACmB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDnB,yBAAyB,CAACgB,UAAU,CAAC;IACrC,MAAM;MAAEI,gBAAgB;MAAEC;IAAW,CAAC,GAAGL,UAAU;IACnD,MAAM;MAAEM,gBAAgB;MAAEC;IAAiB,CAAC,GAAGR,KAAK;IACpDZ,qBAAqB,CAAC,CAAC,CAACmB,gBAAgB,EAAEpB,uBAAuB,CAACsB,6BAA6B,CAAC;IAChG,MAAM;MAAEC;IAAO,CAAC,SAAS1B,gBAAgB,CAAC;MAAE2B,YAAY,EAAE;IAAM,CAAC,CAAC;IAClErB,gBAAgB,CAACoB,MAAM,CAAC;IACxB,MAAME,mBAAmB,GAAGpB,+BAA+B,CAAC;MACxDqB,gBAAgB,EAAEpB,qCAAqC,CAAC;QACpDqB,gBAAgB,EAAET;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMO,mBAAmB,CAAC;MACtBG,MAAM,EAAE1B,uBAAuB,CAACiB,UAAU,CAAC;MAC3CU,cAAc,EAAEzB,qBAAqB,CAACL,UAAU,CAAC+B,oBAAoB;IACzE,CAAC,EAAE;MACCC,WAAW,EAAER,MAAM,CAACS,WAAW,CAACC,QAAQ,CAAC,CAAC;MAC1CC,aAAa,EAAEb,gBAAgB;MAC/Bc,IAAI,EAAEf;IACV,CAAC,CAAC;EACN,CAAC;EAAA,OAAAX,qBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}