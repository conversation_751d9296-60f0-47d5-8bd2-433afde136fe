{"ast": null, "code": "import { decodeJWT } from '@aws-amplify/core/internals/utils';\nimport { AuthError } from '../../../errors/AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction formLoginsMap(idToken) {\n  const issuer = decodeJWT(idToken).payload.iss;\n  const res = {};\n  if (!issuer) {\n    throw new AuthError({\n      name: 'InvalidIdTokenException',\n      message: 'Invalid Idtoken.'\n    });\n  }\n  const domainName = issuer.replace(/(^\\w+:|^)\\/\\//, '');\n  res[domainName] = idToken;\n  return res;\n}\nexport { formLoginsMap };", "map": {"version": 3, "names": ["decodeJWT", "<PERSON>th<PERSON><PERSON><PERSON>", "formLoginsMap", "idToken", "issuer", "payload", "iss", "res", "name", "message", "domainName", "replace"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/credentialsProvider/utils.mjs"], "sourcesContent": ["import { decodeJWT } from '@aws-amplify/core/internals/utils';\nimport { AuthError } from '../../../errors/AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction formLoginsMap(idToken) {\n    const issuer = decodeJWT(idToken).payload.iss;\n    const res = {};\n    if (!issuer) {\n        throw new AuthError({\n            name: 'InvalidIdTokenException',\n            message: 'Invalid Idtoken.',\n        });\n    }\n    const domainName = issuer.replace(/(^\\w+:|^)\\/\\//, '');\n    res[domainName] = idToken;\n    return res;\n}\n\nexport { formLoginsMap };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,mCAAmC;AAC7D,SAASC,SAAS,QAAQ,+BAA+B;;AAEzD;AACA;AACA,SAASC,aAAaA,CAACC,OAAO,EAAE;EAC5B,MAAMC,MAAM,GAAGJ,SAAS,CAACG,OAAO,CAAC,CAACE,OAAO,CAACC,GAAG;EAC7C,MAAMC,GAAG,GAAG,CAAC,CAAC;EACd,IAAI,CAACH,MAAM,EAAE;IACT,MAAM,IAAIH,SAAS,CAAC;MAChBO,IAAI,EAAE,yBAAyB;MAC/BC,OAAO,EAAE;IACb,CAAC,CAAC;EACN;EACA,MAAMC,UAAU,GAAGN,MAAM,CAACO,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;EACtDJ,GAAG,CAACG,UAAU,CAAC,GAAGP,OAAO;EACzB,OAAOI,GAAG;AACd;AAEA,SAASL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}