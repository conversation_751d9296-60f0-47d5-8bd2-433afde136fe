{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport { listWebAuthnCredentials as listWebAuthnCredentials$1 } from '../../foundation/apis/listWebAuthnCredentials.mjs';\nimport '@aws-amplify/core/internals/utils';\nimport '../../providers/cognito/utils/types.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../common/AuthErrorStrings.mjs';\nimport '../../errors/types/validation.mjs';\nimport '../../providers/cognito/types/errors.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Lists registered credentials for an authenticated user\n *\n * @param {ListWebAuthnCredentialsInput} input The list input parameters including page size and next token.\n * @returns Promise<ListWebAuthnCredentialsOutput>\n * @throws - {@link AuthError}:\n * - Thrown when user is unauthenticated\n * @throws - {@link ListWebAuthnCredentialsException}\n * - Thrown due to a service error when listing WebAuthn credentials\n */\nfunction listWebAuthnCredentials(_x) {\n  return _listWebAuthnCredentials.apply(this, arguments);\n}\nfunction _listWebAuthnCredentials() {\n  _listWebAuthnCredentials = _asyncToGenerator(function* (input) {\n    return listWebAuthnCredentials$1(Amplify, input);\n  });\n  return _listWebAuthnCredentials.apply(this, arguments);\n}\nexport { listWebAuthnCredentials };", "map": {"version": 3, "names": ["Amplify", "listWebAuthnCredentials", "listWebAuthnCredentials$1", "_x", "_listWebAuthnCredentials", "apply", "arguments", "_asyncToGenerator", "input"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/apis/listWebAuthnCredentials.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { listWebAuthnCredentials as listWebAuthnCredentials$1 } from '../../foundation/apis/listWebAuthnCredentials.mjs';\nimport '@aws-amplify/core/internals/utils';\nimport '../../providers/cognito/utils/types.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../common/AuthErrorStrings.mjs';\nimport '../../errors/types/validation.mjs';\nimport '../../providers/cognito/types/errors.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Lists registered credentials for an authenticated user\n *\n * @param {ListWebAuthnCredentialsInput} input The list input parameters including page size and next token.\n * @returns Promise<ListWebAuthnCredentialsOutput>\n * @throws - {@link AuthError}:\n * - Thrown when user is unauthenticated\n * @throws - {@link ListWebAuthnCredentialsException}\n * - Thrown due to a service error when listing WebAuthn credentials\n */\nasync function listWebAuthnCredentials(input) {\n    return listWebAuthnCredentials$1(Amplify, input);\n}\n\nexport { listWebAuthnCredentials };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,uBAAuB,IAAIC,yBAAyB,QAAQ,mDAAmD;AACxH,OAAO,mCAAmC;AAC1C,OAAO,yCAAyC;AAChD,OAAO,8CAA8C;AACrD,OAAO,wDAAwD;AAC/D,OAAO,qHAAqH;AAC5H,OAAO,iFAAiF;AACxF,OAAO,mCAAmC;AAC1C,OAAO,mCAAmC;AAC1C,OAAO,0CAA0C;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,SAUeD,uBAAuBA,CAAAE,EAAA;EAAA,OAAAC,wBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,yBAAA;EAAAA,wBAAA,GAAAG,iBAAA,CAAtC,WAAuCC,KAAK,EAAE;IAC1C,OAAON,yBAAyB,CAACF,OAAO,EAAEQ,KAAK,CAAC;EACpD,CAAC;EAAA,OAAAJ,wBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASL,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}