{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { updatePassword, deleteUser as deleteUser$1 } from 'aws-amplify/auth';\nimport { getLogger } from '../utils.mjs';\nconst logger = getLogger('Auth');\nconst changePassword = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* ({\n    currentPassword,\n    newPassword\n  }) {\n    try {\n      logger.debug('calling Auth.updatePassword');\n      yield updatePassword({\n        oldPassword: currentPassword,\n        newPassword\n      });\n      logger.debug('Auth.updatePassword was successful');\n      return Promise.resolve();\n    } catch (e) {\n      logger.debug('Auth.updatePassword failed with error', e);\n      return Promise.reject(e);\n    }\n  });\n  return function changePassword(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nconst deleteUser = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* () {\n    try {\n      logger.debug('calling Auth.deleteUser');\n      yield deleteUser$1();\n      logger.debug('Auth.deleteUser was successful');\n      return Promise.resolve();\n    } catch (e) {\n      logger.debug('Auth.deleteUser failed with error', e);\n      return Promise.reject(e);\n    }\n  });\n  return function deleteUser() {\n    return _ref2.apply(this, arguments);\n  };\n}();\nexport { changePassword, deleteUser };", "map": {"version": 3, "names": ["updatePassword", "deleteUser", "deleteUser$1", "<PERSON><PERSON><PERSON><PERSON>", "logger", "changePassword", "_ref", "_asyncToGenerator", "currentPassword", "newPassword", "debug", "oldPassword", "Promise", "resolve", "e", "reject", "_x", "apply", "arguments", "_ref2"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/helpers/accountSettings/utils.mjs"], "sourcesContent": ["import { updatePassword, deleteUser as deleteUser$1 } from 'aws-amplify/auth';\nimport { getLogger } from '../utils.mjs';\n\nconst logger = getLogger('Auth');\nconst changePassword = async ({ currentPassword, newPassword, }) => {\n    try {\n        logger.debug('calling Auth.updatePassword');\n        await updatePassword({\n            oldPassword: currentPassword,\n            newPassword,\n        });\n        logger.debug('Auth.updatePassword was successful');\n        return Promise.resolve();\n    }\n    catch (e) {\n        logger.debug('Auth.updatePassword failed with error', e);\n        return Promise.reject(e);\n    }\n};\nconst deleteUser = async () => {\n    try {\n        logger.debug('calling Auth.deleteUser');\n        await deleteUser$1();\n        logger.debug('Auth.deleteUser was successful');\n        return Promise.resolve();\n    }\n    catch (e) {\n        logger.debug('Auth.deleteUser failed with error', e);\n        return Promise.reject(e);\n    }\n};\n\nexport { changePassword, deleteUser };\n"], "mappings": ";AAAA,SAASA,cAAc,EAAEC,UAAU,IAAIC,YAAY,QAAQ,kBAAkB;AAC7E,SAASC,SAAS,QAAQ,cAAc;AAExC,MAAMC,MAAM,GAAGD,SAAS,CAAC,MAAM,CAAC;AAChC,MAAME,cAAc;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAO;IAAEC,eAAe;IAAEC;EAAa,CAAC,EAAK;IAChE,IAAI;MACAL,MAAM,CAACM,KAAK,CAAC,6BAA6B,CAAC;MAC3C,MAAMV,cAAc,CAAC;QACjBW,WAAW,EAAEH,eAAe;QAC5BC;MACJ,CAAC,CAAC;MACFL,MAAM,CAACM,KAAK,CAAC,oCAAoC,CAAC;MAClD,OAAOE,OAAO,CAACC,OAAO,CAAC,CAAC;IAC5B,CAAC,CACD,OAAOC,CAAC,EAAE;MACNV,MAAM,CAACM,KAAK,CAAC,uCAAuC,EAAEI,CAAC,CAAC;MACxD,OAAOF,OAAO,CAACG,MAAM,CAACD,CAAC,CAAC;IAC5B;EACJ,CAAC;EAAA,gBAdKT,cAAcA,CAAAW,EAAA;IAAA,OAAAV,IAAA,CAAAW,KAAA,OAAAC,SAAA;EAAA;AAAA,GAcnB;AACD,MAAMjB,UAAU;EAAA,IAAAkB,KAAA,GAAAZ,iBAAA,CAAG,aAAY;IAC3B,IAAI;MACAH,MAAM,CAACM,KAAK,CAAC,yBAAyB,CAAC;MACvC,MAAMR,YAAY,CAAC,CAAC;MACpBE,MAAM,CAACM,KAAK,CAAC,gCAAgC,CAAC;MAC9C,OAAOE,OAAO,CAACC,OAAO,CAAC,CAAC;IAC5B,CAAC,CACD,OAAOC,CAAC,EAAE;MACNV,MAAM,CAACM,KAAK,CAAC,mCAAmC,EAAEI,CAAC,CAAC;MACpD,OAAOF,OAAO,CAACG,MAAM,CAACD,CAAC,CAAC;IAC5B;EACJ,CAAC;EAAA,gBAXKb,UAAUA,CAAA;IAAA,OAAAkB,KAAA,CAAAF,KAAA,OAAAC,SAAA;EAAA;AAAA,GAWf;AAED,SAASb,cAAc,EAAEJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}