{"ast": null, "code": "const plDict = {\n  'Account recovery requires verified contact information': 'Odzyskanie konta wymaga zweryfikowanych danych kontaktowych',\n  'Authenticator App (TOTP)': 'Aplikacja uwi<PERSON> (TOTP)',\n  'Back to Sign In': 'Powrót do logowania',\n  'Change Password': '<PERSON>mie<PERSON> hasło',\n  Changing: 'Zmienianie',\n  Code: 'Kod',\n  'Confirm Email Code': 'Potwierdź kod e-mail',\n  'Confirm Password': 'Potwierdź Hasło',\n  'Confirm Sign Up': 'Potwierdź rejestrację',\n  'Confirm SMS Code': 'Potwierd<PERSON> kod SMS',\n  'Confirm TOTP Code': 'Potwierd<PERSON> hasło jednorazowe',\n  Confirm: 'Potwierdź',\n  'Confirmation Code': 'Kod potwierdzenia',\n  Confirming: 'Potwierdzanie',\n  'Create a new account': 'Utw<PERSON>rz nowe konto',\n  'Create Account': 'Utw<PERSON><PERSON> konto',\n  'Creating Account': 'Tworz<PERSON>e konta',\n  'Dismiss alert': 'Odrzuć alert',\n  Email: 'E-mail',\n  'Email Message': 'Wiadomość e-mail',\n  'Enter your code': 'Wprowadź swój kod',\n  'Enter your Email': 'Wpisz swój adres e-mail',\n  'Enter your phone number': 'Wpisz swój numer telefonu',\n  'Enter your username': 'Wprowadź swoją nazwę użytkownika',\n  'Forgot your password?': 'Zapomniałeś hasła? ',\n  'Hide password': 'Ukryj hasło',\n  'It may take a minute to arrive': 'Może to chwilę potrwać',\n  Loading: 'Ładowanie',\n  'Multi-Factor Authentication': 'Uwierzytelnianie wieloskładnikowe',\n  'Multi-Factor Authentication Setup': 'Konfigurowanie uwierzytelniania wieloskładnikowego',\n  'New password': 'Nowe hasło',\n  or: 'albo',\n  Password: 'Hasło',\n  'Phone Number': 'Numer telefonu',\n  'Resend Code': 'Wyślij kod ponownie',\n  'Reset your password': 'Zresetuj swoje hasło',\n  'Reset your Password': 'Zresetuj swoje hasło',\n  'Select MFA Type': 'Wybierz typ MFA',\n  'Send code': 'Wyślij kod',\n  'Send Code': 'Zresetuj hasło',\n  Sending: 'Wysyłanie',\n  'Setup Email': 'Konfiguracja poczty e-mail',\n  'Setup TOTP': 'Konfiguruj TOTP',\n  'Show password': 'Pokaż hasło',\n  'Sign in to your account': 'Zaloguj się na swoje konto',\n  'Sign In with Amazon': 'Zaloguj z Amazon',\n  'Sign In with Apple': 'Zaloguj z Apple',\n  'Sign In with Facebook': 'Zaloguj z Facebook',\n  'Sign In with Google': 'Zaloguj z Google',\n  'Sign In': 'Logowanie',\n  'Sign in': 'Zaloguj',\n  'Signing in': 'Logowanie',\n  Skip: 'Pomiń',\n  Submit: 'Wyślij',\n  Submitting: 'Wysyłanie',\n  'Text Message (SMS)': 'Wiadomość tekstowa (SMS)',\n  Username: 'Nazwa użytkownika',\n  'Verify Contact': 'Weryfikacja danych kontaktowych',\n  Verify: 'Zweryfikuj',\n  // Additional translations provided by customers\n  Birthdate: 'Data urodzenia',\n  'Family Name': 'Nazwisko',\n  'Given Name': 'Pierwsze imię',\n  'Middle Name': 'Drugie imię',\n  Name: 'Imię i nazwisko',\n  Nickname: 'Pseudonim',\n  'Preferred Username': 'Preferowana nazwa użytkownika',\n  Profile: 'Profil',\n  Website: 'Strona internetowa',\n  'We Emailed You': 'Wysłaliśmy Ci wiadomość e-mail',\n  'We Sent A Code': 'Wysłaliśmy kod',\n  'We Texted You': 'Wysłaliśmy Ci wiadomość SMS',\n  'Your code is on the way. To log in, enter the code we emailed to': 'Twój kod został wysłany. Aby się zalogować, wprowadź kod wysłany na adres e-mail',\n  'Your code is on the way. To log in, enter the code we sent you': 'Twój kod został wysłany. Aby się zalogować, wprowadź wysłany do Ciebie kod',\n  'Your code is on the way. To log in, enter the code we texted to': 'Twój kod został wysłany. Aby się zalogować, wprowadź kod wysłany do Ciebie w wiadomości SMS pod numer'\n};\nexport { plDict };", "map": {"version": 3, "names": ["plDict", "Changing", "Code", "Confirm", "Confirming", "Email", "Loading", "or", "Password", "Sending", "<PERSON><PERSON>", "Submit", "Submitting", "Username", "Verify", "Birthdate", "Name", "Nickname", "Profile", "Website"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/i18n/dictionaries/authenticator/pl.mjs"], "sourcesContent": ["const plDict = {\n    'Account recovery requires verified contact information': 'Odzyskanie konta wymaga zweryfikowanych danych kontaktowych',\n    'Authenticator App (TOTP)': 'Aplikacja uwi<PERSON> (TOTP)',\n    'Back to Sign In': 'Powrót do logowania',\n    'Change Password': '<PERSON>mie<PERSON> hasło',\n    Changing: 'Zmienianie',\n    Code: 'Kod',\n    'Confirm Email Code': 'Potwierdź kod e-mail',\n    'Confirm Password': 'Potwierdź Hasło',\n    'Confirm Sign Up': 'Potwierdź rejestrację',\n    'Confirm SMS Code': 'Potwierd<PERSON> kod SMS',\n    'Confirm TOTP Code': 'Potwierd<PERSON> hasło jednorazowe',\n    Confirm: 'Potwierdź',\n    'Confirmation Code': 'Kod potwierdzenia',\n    Confirming: 'Potwierdzanie',\n    'Create a new account': 'Utw<PERSON>rz nowe konto',\n    'Create Account': 'Utw<PERSON><PERSON> konto',\n    'Creating Account': 'Tworz<PERSON>e konta',\n    'Dismiss alert': 'Odrzuć alert',\n    Email: 'E-mail',\n    'Email Message': 'Wiadomość e-mail',\n    'Enter your code': 'Wprowadź swój kod',\n    'Enter your Email': 'Wpisz swój adres e-mail',\n    'Enter your phone number': 'Wpisz swój numer telefonu',\n    'Enter your username': 'Wprowadź swoją nazwę użytkownika',\n    'Forgot your password?': 'Zapomniałeś hasła? ',\n    'Hide password': 'Ukryj hasło',\n    'It may take a minute to arrive': 'Może to chwilę potrwać',\n    Loading: 'Ładowanie',\n    'Multi-Factor Authentication': 'Uwierzytelnianie wieloskładnikowe',\n    'Multi-Factor Authentication Setup': 'Konfigurowanie uwierzytelniania wieloskładnikowego',\n    'New password': 'Nowe hasło',\n    or: 'albo',\n    Password: 'Hasło',\n    'Phone Number': 'Numer telefonu',\n    'Resend Code': 'Wyślij kod ponownie',\n    'Reset your password': 'Zresetuj swoje hasło',\n    'Reset your Password': 'Zresetuj swoje hasło',\n    'Select MFA Type': 'Wybierz typ MFA',\n    'Send code': 'Wyślij kod',\n    'Send Code': 'Zresetuj hasło',\n    Sending: 'Wysyłanie',\n    'Setup Email': 'Konfiguracja poczty e-mail',\n    'Setup TOTP': 'Konfiguruj TOTP',\n    'Show password': 'Pokaż hasło',\n    'Sign in to your account': 'Zaloguj się na swoje konto',\n    'Sign In with Amazon': 'Zaloguj z Amazon',\n    'Sign In with Apple': 'Zaloguj z Apple',\n    'Sign In with Facebook': 'Zaloguj z Facebook',\n    'Sign In with Google': 'Zaloguj z Google',\n    'Sign In': 'Logowanie',\n    'Sign in': 'Zaloguj',\n    'Signing in': 'Logowanie',\n    Skip: 'Pomiń',\n    Submit: 'Wyślij',\n    Submitting: 'Wysyłanie',\n    'Text Message (SMS)': 'Wiadomość tekstowa (SMS)',\n    Username: 'Nazwa użytkownika',\n    'Verify Contact': 'Weryfikacja danych kontaktowych',\n    Verify: 'Zweryfikuj',\n    // Additional translations provided by customers\n    Birthdate: 'Data urodzenia',\n    'Family Name': 'Nazwisko',\n    'Given Name': 'Pierwsze imię',\n    'Middle Name': 'Drugie imię',\n    Name: 'Imię i nazwisko',\n    Nickname: 'Pseudonim',\n    'Preferred Username': 'Preferowana nazwa użytkownika',\n    Profile: 'Profil',\n    Website: 'Strona internetowa',\n    'We Emailed You': 'Wysłaliśmy Ci wiadomość e-mail',\n    'We Sent A Code': 'Wysłaliśmy kod',\n    'We Texted You': 'Wysłaliśmy Ci wiadomość SMS',\n    'Your code is on the way. To log in, enter the code we emailed to': 'Twój kod został wysłany. Aby się zalogować, wprowadź kod wysłany na adres e-mail',\n    'Your code is on the way. To log in, enter the code we sent you': 'Twój kod został wysłany. Aby się zalogować, wprowadź wysłany do Ciebie kod',\n    'Your code is on the way. To log in, enter the code we texted to': 'Twój kod został wysłany. Aby się zalogować, wprowadź kod wysłany do Ciebie w wiadomości SMS pod numer',\n};\n\nexport { plDict };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACX,wDAAwD,EAAE,6DAA6D;EACvH,0BAA0B,EAAE,mCAAmC;EAC/D,iBAAiB,EAAE,qBAAqB;EACxC,iBAAiB,EAAE,aAAa;EAChCC,QAAQ,EAAE,YAAY;EACtBC,IAAI,EAAE,KAAK;EACX,oBAAoB,EAAE,sBAAsB;EAC5C,kBAAkB,EAAE,iBAAiB;EACrC,iBAAiB,EAAE,uBAAuB;EAC1C,kBAAkB,EAAE,mBAAmB;EACvC,mBAAmB,EAAE,6BAA6B;EAClDC,OAAO,EAAE,WAAW;EACpB,mBAAmB,EAAE,mBAAmB;EACxCC,UAAU,EAAE,eAAe;EAC3B,sBAAsB,EAAE,mBAAmB;EAC3C,gBAAgB,EAAE,cAAc;EAChC,kBAAkB,EAAE,iBAAiB;EACrC,eAAe,EAAE,cAAc;EAC/BC,KAAK,EAAE,QAAQ;EACf,eAAe,EAAE,kBAAkB;EACnC,iBAAiB,EAAE,mBAAmB;EACtC,kBAAkB,EAAE,yBAAyB;EAC7C,yBAAyB,EAAE,2BAA2B;EACtD,qBAAqB,EAAE,kCAAkC;EACzD,uBAAuB,EAAE,qBAAqB;EAC9C,eAAe,EAAE,aAAa;EAC9B,gCAAgC,EAAE,wBAAwB;EAC1DC,OAAO,EAAE,WAAW;EACpB,6BAA6B,EAAE,mCAAmC;EAClE,mCAAmC,EAAE,oDAAoD;EACzF,cAAc,EAAE,YAAY;EAC5BC,EAAE,EAAE,MAAM;EACVC,QAAQ,EAAE,OAAO;EACjB,cAAc,EAAE,gBAAgB;EAChC,aAAa,EAAE,qBAAqB;EACpC,qBAAqB,EAAE,sBAAsB;EAC7C,qBAAqB,EAAE,sBAAsB;EAC7C,iBAAiB,EAAE,iBAAiB;EACpC,WAAW,EAAE,YAAY;EACzB,WAAW,EAAE,gBAAgB;EAC7BC,OAAO,EAAE,WAAW;EACpB,aAAa,EAAE,4BAA4B;EAC3C,YAAY,EAAE,iBAAiB;EAC/B,eAAe,EAAE,aAAa;EAC9B,yBAAyB,EAAE,4BAA4B;EACvD,qBAAqB,EAAE,kBAAkB;EACzC,oBAAoB,EAAE,iBAAiB;EACvC,uBAAuB,EAAE,oBAAoB;EAC7C,qBAAqB,EAAE,kBAAkB;EACzC,SAAS,EAAE,WAAW;EACtB,SAAS,EAAE,SAAS;EACpB,YAAY,EAAE,WAAW;EACzBC,IAAI,EAAE,OAAO;EACbC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,WAAW;EACvB,oBAAoB,EAAE,0BAA0B;EAChDC,QAAQ,EAAE,mBAAmB;EAC7B,gBAAgB,EAAE,iCAAiC;EACnDC,MAAM,EAAE,YAAY;EACpB;EACAC,SAAS,EAAE,gBAAgB;EAC3B,aAAa,EAAE,UAAU;EACzB,YAAY,EAAE,eAAe;EAC7B,aAAa,EAAE,aAAa;EAC5BC,IAAI,EAAE,iBAAiB;EACvBC,QAAQ,EAAE,WAAW;EACrB,oBAAoB,EAAE,+BAA+B;EACrDC,OAAO,EAAE,QAAQ;EACjBC,OAAO,EAAE,oBAAoB;EAC7B,gBAAgB,EAAE,gCAAgC;EAClD,gBAAgB,EAAE,gBAAgB;EAClC,eAAe,EAAE,6BAA6B;EAC9C,kEAAkE,EAAE,kFAAkF;EACtJ,gEAAgE,EAAE,4EAA4E;EAC9I,iEAAiE,EAAE;AACvE,CAAC;AAED,SAASnB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}