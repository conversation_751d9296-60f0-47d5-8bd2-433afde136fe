{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst isBrowser = () => typeof window !== 'undefined' && typeof window.document !== 'undefined';\nexport { isBrowser };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "window", "document"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/isBrowser.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst isBrowser = () => typeof window !== 'undefined' && typeof window.document !== 'undefined';\n\nexport { isBrowser };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,SAAS,GAAGA,CAAA,KAAM,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,WAAW;AAE/F,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}