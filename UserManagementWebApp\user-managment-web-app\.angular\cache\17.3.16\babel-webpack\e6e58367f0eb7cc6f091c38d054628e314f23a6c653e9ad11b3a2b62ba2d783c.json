{"ast": null, "code": "const countryDialCodes = ['+1', '+7', '+20', '+27', '+30', '+31', '+32', '+33', '+34', '+36', '+39', '+40', '+41', '+43', '+44', '+45', '+46', '+47', '+48', '+49', '+51', '+52', '+53', '+54', '+55', '+56', '+57', '+58', '+60', '+61', '+62', '+63', '+64', '+65', '+66', '+81', '+82', '+84', '+86', '+90', '+91', '+92', '+93', '+94', '+95', '+98', '+212', '+213', '+216', '+218', '+220', '+221', '+222', '+223', '+224', '+225', '+226', '+227', '+228', '+229', '+230', '+231', '+232', '+233', '+234', '+235', '+236', '+237', '+238', '+239', '+240', '+241', '+242', '+243', '+244', '+245', '+246', '+248', '+249', '+250', '+251', '+252', '+253', '+254', '+255', '+256', '+257', '+258', '+260', '+261', '+262', '+263', '+264', '+265', '+266', '+267', '+268', '+269', '+290', '+291', '+297', '+298', '+299', '+345', '+350', '+351', '+352', '+353', '+354', '+355', '+356', '+357', '+358', '+359', '+370', '+371', '+372', '+373', '+374', '+375', '+376', '+377', '+378', '+379', '+380', '+381', '+382', '+385', '+386', '+387', '+389', '+420', '+421', '+423', '+500', '+501', '+502', '+503', '+504', '+505', '+506', '+507', '+508', '+509', '+537', '+590', '+591', '+593', '+594', '+595', '+596', '+597', '+598', '+599', '+670', '+672', '+673', '+674', '+675', '+676', '+677', '+678', '+679', '+680', '+681', '+682', '+683', '+685', '+686', '+687', '+688', '+689', '+690', '+691', '+692', '+850', '+852', '+853', '+855', '+856', '+872', '+880', '+886', '+960', '+961', '+962', '+963', '+964', '+965', '+966', '+967', '+968', '+970', '+971', '+972', '+973', '+974', '+975', '+976', '+977', '+992', '+993', '+994', '+995', '+996', '+998'];\nexport { countryDialCodes };", "map": {"version": 3, "names": ["countryDialCodes"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/i18n/country-dial-codes.mjs"], "sourcesContent": ["const countryDialCodes = [\n    '+1',\n    '+7',\n    '+20',\n    '+27',\n    '+30',\n    '+31',\n    '+32',\n    '+33',\n    '+34',\n    '+36',\n    '+39',\n    '+40',\n    '+41',\n    '+43',\n    '+44',\n    '+45',\n    '+46',\n    '+47',\n    '+48',\n    '+49',\n    '+51',\n    '+52',\n    '+53',\n    '+54',\n    '+55',\n    '+56',\n    '+57',\n    '+58',\n    '+60',\n    '+61',\n    '+62',\n    '+63',\n    '+64',\n    '+65',\n    '+66',\n    '+81',\n    '+82',\n    '+84',\n    '+86',\n    '+90',\n    '+91',\n    '+92',\n    '+93',\n    '+94',\n    '+95',\n    '+98',\n    '+212',\n    '+213',\n    '+216',\n    '+218',\n    '+220',\n    '+221',\n    '+222',\n    '+223',\n    '+224',\n    '+225',\n    '+226',\n    '+227',\n    '+228',\n    '+229',\n    '+230',\n    '+231',\n    '+232',\n    '+233',\n    '+234',\n    '+235',\n    '+236',\n    '+237',\n    '+238',\n    '+239',\n    '+240',\n    '+241',\n    '+242',\n    '+243',\n    '+244',\n    '+245',\n    '+246',\n    '+248',\n    '+249',\n    '+250',\n    '+251',\n    '+252',\n    '+253',\n    '+254',\n    '+255',\n    '+256',\n    '+257',\n    '+258',\n    '+260',\n    '+261',\n    '+262',\n    '+263',\n    '+264',\n    '+265',\n    '+266',\n    '+267',\n    '+268',\n    '+269',\n    '+290',\n    '+291',\n    '+297',\n    '+298',\n    '+299',\n    '+345',\n    '+350',\n    '+351',\n    '+352',\n    '+353',\n    '+354',\n    '+355',\n    '+356',\n    '+357',\n    '+358',\n    '+359',\n    '+370',\n    '+371',\n    '+372',\n    '+373',\n    '+374',\n    '+375',\n    '+376',\n    '+377',\n    '+378',\n    '+379',\n    '+380',\n    '+381',\n    '+382',\n    '+385',\n    '+386',\n    '+387',\n    '+389',\n    '+420',\n    '+421',\n    '+423',\n    '+500',\n    '+501',\n    '+502',\n    '+503',\n    '+504',\n    '+505',\n    '+506',\n    '+507',\n    '+508',\n    '+509',\n    '+537',\n    '+590',\n    '+591',\n    '+593',\n    '+594',\n    '+595',\n    '+596',\n    '+597',\n    '+598',\n    '+599',\n    '+670',\n    '+672',\n    '+673',\n    '+674',\n    '+675',\n    '+676',\n    '+677',\n    '+678',\n    '+679',\n    '+680',\n    '+681',\n    '+682',\n    '+683',\n    '+685',\n    '+686',\n    '+687',\n    '+688',\n    '+689',\n    '+690',\n    '+691',\n    '+692',\n    '+850',\n    '+852',\n    '+853',\n    '+855',\n    '+856',\n    '+872',\n    '+880',\n    '+886',\n    '+960',\n    '+961',\n    '+962',\n    '+963',\n    '+964',\n    '+965',\n    '+966',\n    '+967',\n    '+968',\n    '+970',\n    '+971',\n    '+972',\n    '+973',\n    '+974',\n    '+975',\n    '+976',\n    '+977',\n    '+992',\n    '+993',\n    '+994',\n    '+995',\n    '+996',\n    '+998',\n];\n\nexport { countryDialCodes };\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAG,CACrB,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACT;AAED,SAASA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}