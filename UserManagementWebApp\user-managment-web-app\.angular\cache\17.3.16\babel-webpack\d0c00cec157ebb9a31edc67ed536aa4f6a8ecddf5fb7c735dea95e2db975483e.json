{"ast": null, "code": "const placeholder = {\n  borderRadius: {\n    value: '{radii.small.value}'\n  },\n  transitionDuration: {\n    value: '{time.long.value}'\n  },\n  startColor: {\n    value: '{colors.background.secondary.value}'\n  },\n  endColor: {\n    value: '{colors.background.tertiary.value}'\n  },\n  // sizes\n  small: {\n    height: {\n      value: '{space.small.value}'\n    }\n  },\n  default: {\n    height: {\n      value: '{space.medium.value}'\n    }\n  },\n  large: {\n    height: {\n      value: '{space.large.value}'\n    }\n  }\n};\nexport { placeholder };", "map": {"version": 3, "names": ["placeholder", "borderRadius", "value", "transitionDuration", "startColor", "endColor", "small", "height", "default", "large"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/placeholder.mjs"], "sourcesContent": ["const placeholder = {\n    borderRadius: { value: '{radii.small.value}' },\n    transitionDuration: { value: '{time.long.value}' },\n    startColor: { value: '{colors.background.secondary.value}' },\n    endColor: { value: '{colors.background.tertiary.value}' },\n    // sizes\n    small: {\n        height: { value: '{space.small.value}' },\n    },\n    default: {\n        height: { value: '{space.medium.value}' },\n    },\n    large: {\n        height: { value: '{space.large.value}' },\n    },\n};\n\nexport { placeholder };\n"], "mappings": "AAAA,MAAMA,WAAW,GAAG;EAChBC,YAAY,EAAE;IAAEC,KAAK,EAAE;EAAsB,CAAC;EAC9CC,kBAAkB,EAAE;IAAED,KAAK,EAAE;EAAoB,CAAC;EAClDE,UAAU,EAAE;IAAEF,KAAK,EAAE;EAAsC,CAAC;EAC5DG,QAAQ,EAAE;IAAEH,KAAK,EAAE;EAAqC,CAAC;EACzD;EACAI,KAAK,EAAE;IACHC,MAAM,EAAE;MAAEL,KAAK,EAAE;IAAsB;EAC3C,CAAC;EACDM,OAAO,EAAE;IACLD,MAAM,EAAE;MAAEL,KAAK,EAAE;IAAuB;EAC5C,CAAC;EACDO,KAAK,EAAE;IACHF,MAAM,EAAE;MAAEL,KAAK,EAAE;IAAsB;EAC3C;AACJ,CAAC;AAED,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}