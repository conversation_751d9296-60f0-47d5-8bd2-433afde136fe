{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, Input, Output } from '@angular/core';\nimport { formatCurrency } from 'src/app/shared/utilities/format.functions';\nimport { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';\nimport { MenuModule } from 'primeng/menu';\nimport { ButtonModule } from 'primeng/button';\nimport { BadgeModule } from 'primeng/badge';\nimport { NgClass, NgIf } from '@angular/common';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ToolbarModule } from 'primeng/toolbar';\nimport { SplitButtonModule } from 'primeng/splitbutton';\nimport { ActionableGridViewModeLabels, ActionableGridViewModes } from 'src/app/core/enums/actionable-grid';\nimport { GridLayoutManagerComponent } from './grid-layout-manager/grid-layout-manager.component';\nimport { ToggleswitchModule } from 'primeng/toggleswitch';\nlet ActionsMenuComponent = class ActionsMenuComponent {\n  constructor(communicationService, changeDetectorRef, userActivityService) {\n    this.communicationService = communicationService;\n    this.changeDetectorRef = changeDetectorRef;\n    this.userActivityService = userActivityService;\n    this.hasFailures = false;\n    this.disabled = false;\n    this.selectedView = ActionableGridViewModes.Month;\n    this.calendarViewFlag = false;\n    this.agentChatFlag = false;\n    this.enablePivoting = false;\n    this.pivotMode = false;\n    this.showFormEditor = new EventEmitter();\n    this.undoLastChange = new EventEmitter();\n    this.undoAll = new EventEmitter();\n    this.saveChanges = new EventEmitter();\n    this.refreshGridData = new EventEmitter();\n    this.sendEmailClick = new EventEmitter();\n    this.showRefiner = new EventEmitter();\n    this.showValidationResults = new EventEmitter();\n    this.changeSelectedView = new EventEmitter();\n    this.showAgentChat = new EventEmitter();\n    this.exportItems = [{\n      value: null,\n      styleClass: 'hidden'\n    }, {\n      label: 'Send Email',\n      icon: 'pi pi-envelope',\n      command: () => {\n        this.onClickSendEmail();\n      }\n    }, {\n      label: 'Export as Excel',\n      icon: 'pi pi-file-excel',\n      command: () => {\n        this.onExcelExportClick();\n      }\n    }, {\n      label: 'Export as CSV',\n      icon: 'pi pi-file',\n      command: () => {\n        this.onCsvExportClick();\n      }\n    }];\n    this.calendarViews = [];\n    this.undoActions = [];\n    this.selectedViewIcon = 'fa-regular fa-calendar';\n    this.allReportFavorites = [];\n  }\n  ngOnInit() {\n    this.userFavoriteSub = this.communicationService.userFavorite.subscribe(event => {\n      this.allReportFavorites = event.data;\n      this.setIsFavorite();\n    });\n    this.communicationService.refreshUserFavoriteList();\n    this.mobileActions = [{\n      label: 'Favorite',\n      icon: 'pi pi-star',\n      command: () => {\n        this.onUpdateUserFavorite();\n      }\n    }, {\n      label: 'Add',\n      icon: 'pi pi-plus',\n      command: () => {\n        this.onClickShowFormEditor(false);\n      }\n    }, {\n      label: 'Undo',\n      icon: 'sb sb-icon-undo',\n      command: () => {\n        this.onClickUndoLastChange();\n      }\n    }, {\n      label: 'Undo All',\n      icon: 'pi pi-times',\n      command: () => {\n        this.onClickUndoAll();\n      }\n    }, {\n      label: 'Save',\n      icon: 'pi pi-save',\n      command: () => {\n        this.onClickSaveChanges();\n      }\n    }, {\n      label: 'Refresh',\n      icon: 'pi pi-sync',\n      command: () => {\n        this.onClickRefreshGridData();\n      }\n    }, {\n      label: 'Send Email',\n      icon: 'pi pi-envelope',\n      command: () => {\n        this.onClickSendEmail();\n      }\n    }, {\n      label: 'Export to Excel',\n      icon: 'pi pi-file-excel',\n      command: () => {\n        this.onExcelExportClick();\n      }\n    }, {\n      label: 'Export to CSV',\n      icon: 'pi pi-file',\n      command: () => {\n        this.onCsvExportClick();\n      }\n    }];\n    this.calendarViews = [{\n      value: null,\n      styleClass: 'hidden'\n    }, {\n      value: ActionableGridViewModes.Table,\n      label: ActionableGridViewModeLabels.Table,\n      icon: 'pi pi-list',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Table, 'pi pi-list')\n    }, {\n      value: ActionableGridViewModes.Year,\n      label: ActionableGridViewModeLabels.Year,\n      icon: 'fa-solid fa-calendar',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Year, 'fa-solid fa-calendar')\n    }, {\n      value: ActionableGridViewModes.Month,\n      label: ActionableGridViewModeLabels.Month,\n      icon: 'fa-regular fa-calendar',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Month, 'fa-regular fa-calendar')\n    }, {\n      value: ActionableGridViewModes.Week,\n      label: ActionableGridViewModeLabels.Week,\n      icon: 'fa-solid fa-calendar-week',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Week, 'fa-solid fa-calendar-week')\n    }];\n    this.undoActions = [{\n      label: 'Undo',\n      icon: 'sb sb-icon-undo',\n      command: () => this.onClickUndoLastChange()\n    }, {\n      label: 'Undo All',\n      icon: 'pi pi-times',\n      command: () => this.onClickUndoAll()\n    }];\n  }\n  ngOnDestroy() {\n    if (this.userFavoriteSub) {\n      this.userFavoriteSub.unsubscribe();\n    }\n  }\n  onClickShowFormEditor(arg) {\n    if (this.disabled) return;\n    this.showFormEditor.emit(arg);\n  }\n  onClickUndoLastChange() {\n    if (this.disabled) return;\n    this.undoLastChange.emit();\n  }\n  onClickUndoAll() {\n    if (this.disabled) return;\n    this.undoAll.emit();\n  }\n  onClickSaveChanges() {\n    if (this.disabled) return;\n    this.saveChanges.emit();\n  }\n  onClickRefreshGridData() {\n    if (this.disabled) return;\n    this.refreshGridData.emit();\n  }\n  onClickShowRefiner() {\n    if (this.disabled) return;\n    this.showRefiner.emit();\n  }\n  onClickShowValidationResults() {\n    this.showValidationResults.emit();\n  }\n  onClickShowAgent() {\n    this.showAgentChat.emit();\n  }\n  setIsFavorite() {\n    this.isFavorite = this.allReportFavorites.some(f => f.reportId === this.reportInfo?.reportId && f.isApp);\n    // for some weird reason angular doesn't detect the changes\n    this.changeDetectorRef.detectChanges();\n  }\n  onUpdateUserFavorite() {\n    let userFavorite = this.allReportFavorites?.find(favorite => favorite.projectId.toString() === this.reportInfo.projectId && favorite.reportId === this.reportInfo.reportId);\n    if (userFavorite) {\n      userFavorite.active = false;\n      userFavorite.isApp = window.location.pathname.includes('app-view') ? true : false;\n    } else {\n      userFavorite = this.addNewUserFavorite();\n    }\n    this.userActivityService.upsertUserFavorite(userFavorite);\n    // because of the delay we set isFavorite temporarily, after updating is done it will update again.\n    this.isFavorite = userFavorite.active;\n  }\n  addNewUserFavorite() {\n    const userFavorite = {\n      projectId: +this.reportInfo.projectId,\n      url: window.location.pathname,\n      reportId: this.reportInfo.reportId,\n      reportName: this.reportInfo.reportName,\n      projectVersionId: +this.reportInfo.projectVersionId,\n      active: true,\n      isApp: window.location.pathname.includes('app-view') ? true : false\n    };\n    return userFavorite;\n  }\n  onClickSendEmail() {\n    if (this.disabled) return;\n    this.sendEmailClick.emit();\n  }\n  onExcelExportClick() {\n    if (this.disabled) return;\n    this.agGrid.api.exportDataAsExcel({\n      processCellCallback: params => {\n        return this.processCellsForExport(params);\n      },\n      columnKeys: this.columnsToExport,\n      fileName: this.reportInfo.reportName,\n      sheetName: this.reportInfo.reportName\n    });\n  }\n  onCsvExportClick() {\n    if (this.disabled) return;\n    this.agGrid.api.exportDataAsCsv({\n      processCellCallback: params => {\n        return this.processCellsForExport(params);\n      },\n      columnKeys: this.columnsToExport,\n      fileName: this.reportInfo.reportName\n    });\n  }\n  processCellsForExport(params) {\n    const exportParamsColDef = params.column.getColDef();\n    const actionableGridColDef = this.reportInfo.formatConfig?.actionableGridColumnsConfig.find(x => x.column === exportParamsColDef.field);\n    if (actionableGridColDef?.format.type === EditorColumnType.Currency) {\n      exportParamsColDef.valueFormatter = formatCurrency(params.value, actionableGridColDef.format.currency, actionableGridColDef.format.decimalPlaces);\n      return exportParamsColDef.valueFormatter;\n    }\n    return params.value;\n  }\n  onChangeSelectedView(view, icon) {\n    this.changeSelectedView.emit(view);\n    this.selectedViewIcon = icon;\n    this.selectedView = view;\n    // Resize columns if no layout is selected\n    setTimeout(() => {\n      if (this.slectedLayout === undefined) {\n        this.agGrid.api?.autoSizeAllColumns();\n      }\n    }, 0);\n  }\n  onSelectedLayoutChange(layout) {\n    this.slectedLayout = layout;\n  }\n};\n__decorate([Input()], ActionsMenuComponent.prototype, \"previewMode\", void 0);\n__decorate([Input()], ActionsMenuComponent.prototype, \"reportInfo\", void 0);\n__decorate([Input()], ActionsMenuComponent.prototype, \"checkHasChanges\", void 0);\n__decorate([Input()], ActionsMenuComponent.prototype, \"checkHasSlicers\", void 0);\n__decorate([Input()], ActionsMenuComponent.prototype, \"checkHasPendingChanges\", void 0);\n__decorate([Input()], ActionsMenuComponent.prototype, \"agGrid\", void 0);\n__decorate([Input()], ActionsMenuComponent.prototype, \"columnsToExport\", void 0);\n__decorate([Input()], ActionsMenuComponent.prototype, \"validationResultsCount\", void 0);\n__decorate([Input()], ActionsMenuComponent.prototype, \"hasFailures\", void 0);\n__decorate([Input()], ActionsMenuComponent.prototype, \"disabled\", void 0);\n__decorate([Input()], ActionsMenuComponent.prototype, \"selectedView\", void 0);\n__decorate([Input()], ActionsMenuComponent.prototype, \"calendarViewFlag\", void 0);\n__decorate([Input()], ActionsMenuComponent.prototype, \"agentChatFlag\", void 0);\n__decorate([Input()], ActionsMenuComponent.prototype, \"enablePivoting\", void 0);\n__decorate([Input()], ActionsMenuComponent.prototype, \"pivotMode\", void 0);\n__decorate([Input()], ActionsMenuComponent.prototype, \"layouts\", void 0);\n__decorate([Output()], ActionsMenuComponent.prototype, \"showFormEditor\", void 0);\n__decorate([Output()], ActionsMenuComponent.prototype, \"undoLastChange\", void 0);\n__decorate([Output()], ActionsMenuComponent.prototype, \"undoAll\", void 0);\n__decorate([Output()], ActionsMenuComponent.prototype, \"saveChanges\", void 0);\n__decorate([Output()], ActionsMenuComponent.prototype, \"refreshGridData\", void 0);\n__decorate([Output()], ActionsMenuComponent.prototype, \"sendEmailClick\", void 0);\n__decorate([Output()], ActionsMenuComponent.prototype, \"showRefiner\", void 0);\n__decorate([Output()], ActionsMenuComponent.prototype, \"showValidationResults\", void 0);\n__decorate([Output()], ActionsMenuComponent.prototype, \"changeSelectedView\", void 0);\n__decorate([Output()], ActionsMenuComponent.prototype, \"showAgentChat\", void 0);\nActionsMenuComponent = __decorate([Component({\n  selector: 'app-actions-menu',\n  templateUrl: './actions-menu.component.html',\n  styleUrls: ['./actions-menu.component.scss'],\n  standalone: true,\n  imports: [ToolbarModule, TooltipModule, BadgeModule, NgClass, ButtonModule, MenuModule, SplitButtonModule, NgIf, GridLayoutManagerComponent, ToggleswitchModule]\n})], ActionsMenuComponent);\nexport { ActionsMenuComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "Input", "Output", "formatCurrency", "EditorColumnType", "MenuModule", "ButtonModule", "BadgeModule", "Ng<PERSON><PERSON>", "NgIf", "TooltipModule", "ToolbarModule", "SplitButtonModule", "ActionableGridViewModeLabels", "ActionableGridViewModes", "GridLayoutManagerComponent", "ToggleswitchModule", "ActionsMenuComponent", "constructor", "communicationService", "changeDetectorRef", "userActivityService", "hasFailures", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "Month", "calendarViewFlag", "agentChatFlag", "enablePivoting", "pivotMode", "showFormEditor", "undoLastChange", "undoAll", "saveChanges", "refreshGridData", "sendEmailClick", "showRefiner", "showValidationResults", "changeSelectedView", "showAgentChat", "exportItems", "value", "styleClass", "label", "icon", "command", "onClickSendEmail", "onExcelExportClick", "onCsvExportClick", "calendarViews", "undoActions", "selectedViewIcon", "allReportFavorites", "ngOnInit", "userFavoriteSub", "userFavorite", "subscribe", "event", "data", "setIsFavorite", "refreshUserFavoriteList", "mobileActions", "onUpdateUserFavorite", "onClickShowFormEditor", "onClickUndoLastChange", "onClickUndoAll", "onClickSaveChanges", "onClickRefreshGridData", "Table", "onChangeSelectedView", "Year", "Week", "ngOnDestroy", "unsubscribe", "arg", "emit", "onClickShowRefiner", "onClickShowValidationResults", "onClickShowAgent", "isFavorite", "some", "f", "reportId", "reportInfo", "isApp", "detectChanges", "find", "favorite", "projectId", "toString", "active", "window", "location", "pathname", "includes", "addNewUserFavorite", "upsertUserFavorite", "url", "reportName", "projectVersionId", "agGrid", "api", "exportDataAsExcel", "processCellCallback", "params", "processCellsForExport", "columnKeys", "columnsToExport", "fileName", "sheetName", "exportDataAsCsv", "exportParamsColDef", "column", "getColDef", "actionableGridColDef", "formatConfig", "actionableGridColumnsConfig", "x", "field", "format", "type", "<PERSON><PERSON><PERSON><PERSON>", "valueFormatter", "currency", "decimalPlaces", "view", "setTimeout", "s<PERSON><PERSON><PERSON><PERSON>", "undefined", "autoSizeAllColumns", "onSelectedLayoutChange", "layout", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\actions-menu\\actions-menu.component.ts"], "sourcesContent": ["import { ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';\r\nimport { AgGridAngular } from 'ag-grid-angular';\r\nimport { ProcessCellForExportParams } from 'ag-grid-community';\r\nimport { Subscription } from 'rxjs';\r\nimport { UserFavorite } from 'src/app/core/models/user-favorite';\r\nimport { CommunicationService } from 'src/app/core/services/communication.service';\r\nimport { UserActivityService } from 'src/app/core/services/user-activity.service';\r\nimport { ReportInfo } from 'src/app/core/models/report-info';\r\nimport { formatCurrency } from 'src/app/shared/utilities/format.functions';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';\r\nimport { MenuModule } from 'primeng/menu';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { BadgeModule } from 'primeng/badge';\r\nimport { Ng<PERSON><PERSON>, NgIf } from '@angular/common';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { ToolbarModule } from 'primeng/toolbar';\r\nimport { SplitButtonModule } from 'primeng/splitbutton';\r\nimport { ActionableGridViewModeLabels, ActionableGridViewModes } from 'src/app/core/enums/actionable-grid';\r\nimport { GridLayoutManagerComponent } from './grid-layout-manager/grid-layout-manager.component';\r\nimport { GridLayout } from '../model/grid-layout';\r\nimport { ToggleswitchModule } from 'primeng/toggleswitch';\r\n\r\n@Component({\r\n  selector: 'app-actions-menu',\r\n  templateUrl: './actions-menu.component.html',\r\n  styleUrls: ['./actions-menu.component.scss'],\r\n  standalone: true,\r\n  imports: [ToolbarModule, TooltipModule, BadgeModule, NgClass, ButtonModule, MenuModule, SplitButtonModule, NgIf, GridLayoutManagerComponent, ToggleswitchModule]\r\n})\r\nexport class ActionsMenuComponent implements OnInit, OnDestroy {\r\n\r\n  @Input() previewMode: boolean;\r\n  @Input() reportInfo: ReportInfo;\r\n  @Input() checkHasChanges: boolean;\r\n  @Input() checkHasSlicers: boolean;\r\n  @Input() checkHasPendingChanges: boolean;\r\n  @Input() agGrid: AgGridAngular;\r\n  @Input() columnsToExport: string[];\r\n  @Input() validationResultsCount: any;\r\n  @Input() hasFailures = false;\r\n  @Input() disabled = false;\r\n  @Input() selectedView = ActionableGridViewModes.Month;\r\n  @Input() calendarViewFlag = false;\r\n  @Input() agentChatFlag = false;\r\n  @Input() enablePivoting = false;\r\n  @Input() pivotMode = false;\r\n  @Input() layouts: GridLayout[];\r\n\r\n  @Output() showFormEditor: EventEmitter<any> = new EventEmitter();\r\n  @Output() undoLastChange: EventEmitter<any> = new EventEmitter();\r\n  @Output() undoAll: EventEmitter<any> = new EventEmitter();\r\n  @Output() saveChanges: EventEmitter<any> = new EventEmitter();\r\n  @Output() refreshGridData: EventEmitter<any> = new EventEmitter();\r\n  @Output() sendEmailClick: EventEmitter<any> = new EventEmitter();\r\n  @Output() showRefiner: EventEmitter<any> = new EventEmitter();\r\n  @Output() showValidationResults: EventEmitter<any> = new EventEmitter();\r\n  @Output() changeSelectedView: EventEmitter<string> = new EventEmitter();\r\n  @Output() showAgentChat: EventEmitter<string> = new EventEmitter();\r\n\r\n  mobileActions: MenuItem[];\r\n\r\n  exportItems = [\r\n    {\r\n      value: null,\r\n      styleClass: 'hidden'\r\n    },\r\n    {\r\n      label: 'Send Email', icon: 'pi pi-envelope', command: () => {\r\n        this.onClickSendEmail();\r\n      }\r\n    },\r\n    {\r\n      label: 'Export as Excel', icon: 'pi pi-file-excel', command: () => {\r\n        this.onExcelExportClick();\r\n      },\r\n    },\r\n    {\r\n      label: 'Export as CSV', icon: 'pi pi-file', command: () => {\r\n        this.onCsvExportClick();\r\n      }\r\n    }\r\n  ];\r\n\r\n  calendarViews = [];\r\n  undoActions = [];\r\n  isFavorite: boolean;\r\n  selectedViewIcon: string = 'fa-regular fa-calendar';\r\n  slectedLayout: GridLayout | undefined;\r\n\r\n  private userFavoriteSub: Subscription;\r\n  private allReportFavorites: UserFavorite[] = [];\r\n\r\n  constructor(\r\n    private communicationService: CommunicationService,\r\n    private changeDetectorRef: ChangeDetectorRef,\r\n    private userActivityService: UserActivityService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.userFavoriteSub = this.communicationService.userFavorite\r\n      .subscribe(event => {\r\n        this.allReportFavorites = event.data;\r\n        this.setIsFavorite();\r\n      });\r\n    this.communicationService.refreshUserFavoriteList();\r\n\r\n    this.mobileActions = [\r\n      {\r\n        label: 'Favorite',\r\n        icon: 'pi pi-star',\r\n        command: () => {\r\n          this.onUpdateUserFavorite();\r\n        }\r\n      },\r\n      {\r\n        label: 'Add',\r\n        icon: 'pi pi-plus',\r\n        command: () => {\r\n          this.onClickShowFormEditor(false);\r\n        }\r\n      },\r\n      {\r\n        label: 'Undo',\r\n        icon: 'sb sb-icon-undo',\r\n        command: () => {\r\n          this.onClickUndoLastChange();\r\n        }\r\n      },\r\n      {\r\n        label: 'Undo All',\r\n        icon: 'pi pi-times',\r\n        command: () => {\r\n          this.onClickUndoAll();\r\n        }\r\n      },\r\n      {\r\n        label: 'Save',\r\n        icon: 'pi pi-save',\r\n        command: () => {\r\n          this.onClickSaveChanges();\r\n        }\r\n      },\r\n      {\r\n        label: 'Refresh',\r\n        icon: 'pi pi-sync',\r\n        command: () => {\r\n          this.onClickRefreshGridData();\r\n        }\r\n      },\r\n      {\r\n        label: 'Send Email',\r\n        icon: 'pi pi-envelope',\r\n        command: () => {\r\n          this.onClickSendEmail();\r\n        }\r\n      },\r\n      {\r\n        label: 'Export to Excel',\r\n        icon: 'pi pi-file-excel',\r\n        command: () => {\r\n          this.onExcelExportClick();\r\n        }\r\n      },\r\n      {\r\n        label: 'Export to CSV',\r\n        icon: 'pi pi-file',\r\n        command: () => {\r\n          this.onCsvExportClick();\r\n        }\r\n      },\r\n    ];\r\n\r\n    this.calendarViews = [\r\n      {\r\n        value: null,\r\n        styleClass: 'hidden'\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Table, label: ActionableGridViewModeLabels.Table, icon: 'pi pi-list',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Table, 'pi pi-list')\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Year, label: ActionableGridViewModeLabels.Year, icon: 'fa-solid fa-calendar',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Year, 'fa-solid fa-calendar')\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Month, label: ActionableGridViewModeLabels.Month, icon: 'fa-regular fa-calendar',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Month, 'fa-regular fa-calendar')\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Week, label: ActionableGridViewModeLabels.Week, icon: 'fa-solid fa-calendar-week',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Week, 'fa-solid fa-calendar-week')\r\n      },\r\n    ];\r\n\r\n    this.undoActions = [\r\n      {\r\n        label: 'Undo', icon: 'sb sb-icon-undo', command: () => this.onClickUndoLastChange()\r\n      },\r\n      {\r\n        label: 'Undo All', icon: 'pi pi-times', command: () => this.onClickUndoAll()\r\n      },\r\n    ];\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.userFavoriteSub) {\r\n      this.userFavoriteSub.unsubscribe();\r\n    }\r\n  }\r\n\r\n  onClickShowFormEditor(arg: boolean): void {\r\n    if (this.disabled) return;\r\n\r\n    this.showFormEditor.emit(arg);\r\n  }\r\n\r\n  onClickUndoLastChange(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.undoLastChange.emit();\r\n  }\r\n\r\n  onClickUndoAll(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.undoAll.emit();\r\n  }\r\n\r\n  onClickSaveChanges(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.saveChanges.emit();\r\n  }\r\n\r\n  onClickRefreshGridData(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.refreshGridData.emit();\r\n  }\r\n\r\n  onClickShowRefiner(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.showRefiner.emit();\r\n  }\r\n\r\n  onClickShowValidationResults(): void {\r\n    this.showValidationResults.emit();\r\n  }\r\n\r\n  onClickShowAgent(): void {\r\n    this.showAgentChat.emit();\r\n  }\r\n\r\n  setIsFavorite() {\r\n    this.isFavorite = this.allReportFavorites.some(f => f.reportId === this.reportInfo?.reportId && f.isApp);\r\n    // for some weird reason angular doesn't detect the changes\r\n    this.changeDetectorRef.detectChanges();\r\n  }\r\n\r\n  onUpdateUserFavorite(): void {\r\n    let userFavorite: UserFavorite = this.allReportFavorites?.find(favorite =>\r\n      favorite.projectId.toString() === this.reportInfo.projectId && favorite.reportId === this.reportInfo.reportId);\r\n\r\n    if (userFavorite) {\r\n      userFavorite.active = false;\r\n      userFavorite.isApp = window.location.pathname.includes('app-view') ? true : false;\r\n    }\r\n    else {\r\n      userFavorite = this.addNewUserFavorite();\r\n    }\r\n    this.userActivityService.upsertUserFavorite(userFavorite);\r\n    // because of the delay we set isFavorite temporarily, after updating is done it will update again.\r\n    this.isFavorite = userFavorite.active;\r\n  }\r\n\r\n  addNewUserFavorite() {\r\n    const userFavorite: UserFavorite = {\r\n      projectId: +this.reportInfo.projectId,\r\n      url: window.location.pathname,\r\n      reportId: this.reportInfo.reportId,\r\n      reportName: this.reportInfo.reportName,\r\n      projectVersionId: +this.reportInfo.projectVersionId,\r\n      active: true,\r\n      isApp: window.location.pathname.includes('app-view') ? true : false\r\n    };\r\n    return userFavorite;\r\n  }\r\n\r\n  onClickSendEmail(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.sendEmailClick.emit();\r\n  }\r\n\r\n  onExcelExportClick() {\r\n    if (this.disabled) return;\r\n\r\n    this.agGrid.api.exportDataAsExcel({\r\n      processCellCallback: (params) => {\r\n        return this.processCellsForExport(params);\r\n      },\r\n      columnKeys: this.columnsToExport, fileName: this.reportInfo.reportName, sheetName: this.reportInfo.reportName\r\n    });\r\n  }\r\n\r\n  onCsvExportClick() {\r\n    if (this.disabled) return;\r\n\r\n    this.agGrid.api.exportDataAsCsv({\r\n      processCellCallback: (params) => {\r\n        return this.processCellsForExport(params);\r\n      },\r\n      columnKeys: this.columnsToExport, fileName: this.reportInfo.reportName\r\n    });\r\n  }\r\n\r\n  processCellsForExport(params: ProcessCellForExportParams) {\r\n    const exportParamsColDef = params.column.getColDef();\r\n    const actionableGridColDef = this.reportInfo.formatConfig?.actionableGridColumnsConfig.find(x => x.column === exportParamsColDef.field);\r\n    if (actionableGridColDef?.format.type === EditorColumnType.Currency) {\r\n      exportParamsColDef.valueFormatter = formatCurrency(\r\n        params.value,\r\n        actionableGridColDef.format.currency,\r\n        actionableGridColDef.format.decimalPlaces);\r\n      return exportParamsColDef.valueFormatter;\r\n    }\r\n\r\n    return params.value;\r\n  }\r\n\r\n  onChangeSelectedView(view: ActionableGridViewModes, icon: string) {\r\n    this.changeSelectedView.emit(view);\r\n    this.selectedViewIcon = icon;\r\n    this.selectedView = view;\r\n\r\n\r\n    // Resize columns if no layout is selected\r\n    setTimeout(() => {\r\n      if (this.slectedLayout === undefined) {\r\n        this.agGrid.api?.autoSizeAllColumns();\r\n      }\r\n    }, 0);\r\n  }\r\n\r\n  onSelectedLayoutChange(layout: GridLayout) {\r\n    this.slectedLayout = layout;\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAA4BA,SAAS,EAAEC,YAAY,EAAEC,KAAK,EAAqBC,MAAM,QAAQ,eAAe;AAQ5G,SAASC,cAAc,QAAQ,2CAA2C;AAE1E,SAASC,gBAAgB,QAAQ,8CAA8C;AAC/E,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,OAAO,EAAEC,IAAI,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,4BAA4B,EAAEC,uBAAuB,QAAQ,oCAAoC;AAC1G,SAASC,0BAA0B,QAAQ,qDAAqD;AAEhG,SAASC,kBAAkB,QAAQ,sBAAsB;AASlD,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EA+D/BC,YACUC,oBAA0C,EAC1CC,iBAAoC,EACpCC,mBAAwC;IAFxC,KAAAF,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAxDpB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAGV,uBAAuB,CAACW,KAAK;IAC5C,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,SAAS,GAAG,KAAK;IAGhB,KAAAC,cAAc,GAAsB,IAAI9B,YAAY,EAAE;IACtD,KAAA+B,cAAc,GAAsB,IAAI/B,YAAY,EAAE;IACtD,KAAAgC,OAAO,GAAsB,IAAIhC,YAAY,EAAE;IAC/C,KAAAiC,WAAW,GAAsB,IAAIjC,YAAY,EAAE;IACnD,KAAAkC,eAAe,GAAsB,IAAIlC,YAAY,EAAE;IACvD,KAAAmC,cAAc,GAAsB,IAAInC,YAAY,EAAE;IACtD,KAAAoC,WAAW,GAAsB,IAAIpC,YAAY,EAAE;IACnD,KAAAqC,qBAAqB,GAAsB,IAAIrC,YAAY,EAAE;IAC7D,KAAAsC,kBAAkB,GAAyB,IAAItC,YAAY,EAAE;IAC7D,KAAAuC,aAAa,GAAyB,IAAIvC,YAAY,EAAE;IAIlE,KAAAwC,WAAW,GAAG,CACZ;MACEC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE;KACb,EACD;MACEC,KAAK,EAAE,YAAY;MAAEC,IAAI,EAAE,gBAAgB;MAAEC,OAAO,EAAEA,CAAA,KAAK;QACzD,IAAI,CAACC,gBAAgB,EAAE;MACzB;KACD,EACD;MACEH,KAAK,EAAE,iBAAiB;MAAEC,IAAI,EAAE,kBAAkB;MAAEC,OAAO,EAAEA,CAAA,KAAK;QAChE,IAAI,CAACE,kBAAkB,EAAE;MAC3B;KACD,EACD;MACEJ,KAAK,EAAE,eAAe;MAAEC,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAEA,CAAA,KAAK;QACxD,IAAI,CAACG,gBAAgB,EAAE;MACzB;KACD,CACF;IAED,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,WAAW,GAAG,EAAE;IAEhB,KAAAC,gBAAgB,GAAW,wBAAwB;IAI3C,KAAAC,kBAAkB,GAAmB,EAAE;EAM3C;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,GAAG,IAAI,CAACnC,oBAAoB,CAACoC,YAAY,CAC1DC,SAAS,CAACC,KAAK,IAAG;MACjB,IAAI,CAACL,kBAAkB,GAAGK,KAAK,CAACC,IAAI;MACpC,IAAI,CAACC,aAAa,EAAE;IACtB,CAAC,CAAC;IACJ,IAAI,CAACxC,oBAAoB,CAACyC,uBAAuB,EAAE;IAEnD,IAAI,CAACC,aAAa,GAAG,CACnB;MACElB,KAAK,EAAE,UAAU;MACjBC,IAAI,EAAE,YAAY;MAClBC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACiB,oBAAoB,EAAE;MAC7B;KACD,EACD;MACEnB,KAAK,EAAE,KAAK;MACZC,IAAI,EAAE,YAAY;MAClBC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACkB,qBAAqB,CAAC,KAAK,CAAC;MACnC;KACD,EACD;MACEpB,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,iBAAiB;MACvBC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACmB,qBAAqB,EAAE;MAC9B;KACD,EACD;MACErB,KAAK,EAAE,UAAU;MACjBC,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACoB,cAAc,EAAE;MACvB;KACD,EACD;MACEtB,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,YAAY;MAClBC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACqB,kBAAkB,EAAE;MAC3B;KACD,EACD;MACEvB,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,YAAY;MAClBC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACsB,sBAAsB,EAAE;MAC/B;KACD,EACD;MACExB,KAAK,EAAE,YAAY;MACnBC,IAAI,EAAE,gBAAgB;MACtBC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACC,gBAAgB,EAAE;MACzB;KACD,EACD;MACEH,KAAK,EAAE,iBAAiB;MACxBC,IAAI,EAAE,kBAAkB;MACxBC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACE,kBAAkB,EAAE;MAC3B;KACD,EACD;MACEJ,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE,YAAY;MAClBC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACG,gBAAgB,EAAE;MACzB;KACD,CACF;IAED,IAAI,CAACC,aAAa,GAAG,CACnB;MACER,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE;KACb,EACD;MACED,KAAK,EAAE3B,uBAAuB,CAACsD,KAAK;MAAEzB,KAAK,EAAE9B,4BAA4B,CAACuD,KAAK;MAAExB,IAAI,EAAE,YAAY;MACnGC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACwB,oBAAoB,CAACvD,uBAAuB,CAACsD,KAAK,EAAE,YAAY;KACrF,EACD;MACE3B,KAAK,EAAE3B,uBAAuB,CAACwD,IAAI;MAAE3B,KAAK,EAAE9B,4BAA4B,CAACyD,IAAI;MAAE1B,IAAI,EAAE,sBAAsB;MAC3GC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACwB,oBAAoB,CAACvD,uBAAuB,CAACwD,IAAI,EAAE,sBAAsB;KAC9F,EACD;MACE7B,KAAK,EAAE3B,uBAAuB,CAACW,KAAK;MAAEkB,KAAK,EAAE9B,4BAA4B,CAACY,KAAK;MAAEmB,IAAI,EAAE,wBAAwB;MAC/GC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACwB,oBAAoB,CAACvD,uBAAuB,CAACW,KAAK,EAAE,wBAAwB;KACjG,EACD;MACEgB,KAAK,EAAE3B,uBAAuB,CAACyD,IAAI;MAAE5B,KAAK,EAAE9B,4BAA4B,CAAC0D,IAAI;MAAE3B,IAAI,EAAE,2BAA2B;MAChHC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACwB,oBAAoB,CAACvD,uBAAuB,CAACyD,IAAI,EAAE,2BAA2B;KACnG,CACF;IAED,IAAI,CAACrB,WAAW,GAAG,CACjB;MACEP,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,iBAAiB;MAAEC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACmB,qBAAqB;KAClF,EACD;MACErB,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACoB,cAAc;KAC3E,CACF;EACH;EAEAO,WAAWA,CAAA;IACT,IAAI,IAAI,CAAClB,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACmB,WAAW,EAAE;IACpC;EACF;EAEAV,qBAAqBA,CAACW,GAAY;IAChC,IAAI,IAAI,CAACnD,QAAQ,EAAE;IAEnB,IAAI,CAACO,cAAc,CAAC6C,IAAI,CAACD,GAAG,CAAC;EAC/B;EAEAV,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAACzC,QAAQ,EAAE;IAEnB,IAAI,CAACQ,cAAc,CAAC4C,IAAI,EAAE;EAC5B;EAEAV,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC1C,QAAQ,EAAE;IAEnB,IAAI,CAACS,OAAO,CAAC2C,IAAI,EAAE;EACrB;EAEAT,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC3C,QAAQ,EAAE;IAEnB,IAAI,CAACU,WAAW,CAAC0C,IAAI,EAAE;EACzB;EAEAR,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAAC5C,QAAQ,EAAE;IAEnB,IAAI,CAACW,eAAe,CAACyC,IAAI,EAAE;EAC7B;EAEAC,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACrD,QAAQ,EAAE;IAEnB,IAAI,CAACa,WAAW,CAACuC,IAAI,EAAE;EACzB;EAEAE,4BAA4BA,CAAA;IAC1B,IAAI,CAACxC,qBAAqB,CAACsC,IAAI,EAAE;EACnC;EAEAG,gBAAgBA,CAAA;IACd,IAAI,CAACvC,aAAa,CAACoC,IAAI,EAAE;EAC3B;EAEAhB,aAAaA,CAAA;IACX,IAAI,CAACoB,UAAU,GAAG,IAAI,CAAC3B,kBAAkB,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAK,IAAI,CAACC,UAAU,EAAED,QAAQ,IAAID,CAAC,CAACG,KAAK,CAAC;IACxG;IACA,IAAI,CAAChE,iBAAiB,CAACiE,aAAa,EAAE;EACxC;EAEAvB,oBAAoBA,CAAA;IAClB,IAAIP,YAAY,GAAiB,IAAI,CAACH,kBAAkB,EAAEkC,IAAI,CAACC,QAAQ,IACrEA,QAAQ,CAACC,SAAS,CAACC,QAAQ,EAAE,KAAK,IAAI,CAACN,UAAU,CAACK,SAAS,IAAID,QAAQ,CAACL,QAAQ,KAAK,IAAI,CAACC,UAAU,CAACD,QAAQ,CAAC;IAEhH,IAAI3B,YAAY,EAAE;MAChBA,YAAY,CAACmC,MAAM,GAAG,KAAK;MAC3BnC,YAAY,CAAC6B,KAAK,GAAGO,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK;IACnF,CAAC,MACI;MACHvC,YAAY,GAAG,IAAI,CAACwC,kBAAkB,EAAE;IAC1C;IACA,IAAI,CAAC1E,mBAAmB,CAAC2E,kBAAkB,CAACzC,YAAY,CAAC;IACzD;IACA,IAAI,CAACwB,UAAU,GAAGxB,YAAY,CAACmC,MAAM;EACvC;EAEAK,kBAAkBA,CAAA;IAChB,MAAMxC,YAAY,GAAiB;MACjCiC,SAAS,EAAE,CAAC,IAAI,CAACL,UAAU,CAACK,SAAS;MACrCS,GAAG,EAAEN,MAAM,CAACC,QAAQ,CAACC,QAAQ;MAC7BX,QAAQ,EAAE,IAAI,CAACC,UAAU,CAACD,QAAQ;MAClCgB,UAAU,EAAE,IAAI,CAACf,UAAU,CAACe,UAAU;MACtCC,gBAAgB,EAAE,CAAC,IAAI,CAAChB,UAAU,CAACgB,gBAAgB;MACnDT,MAAM,EAAE,IAAI;MACZN,KAAK,EAAEO,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG;KAC/D;IACD,OAAOvC,YAAY;EACrB;EAEAT,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACvB,QAAQ,EAAE;IAEnB,IAAI,CAACY,cAAc,CAACwC,IAAI,EAAE;EAC5B;EAEA5B,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACxB,QAAQ,EAAE;IAEnB,IAAI,CAAC6E,MAAM,CAACC,GAAG,CAACC,iBAAiB,CAAC;MAChCC,mBAAmB,EAAGC,MAAM,IAAI;QAC9B,OAAO,IAAI,CAACC,qBAAqB,CAACD,MAAM,CAAC;MAC3C,CAAC;MACDE,UAAU,EAAE,IAAI,CAACC,eAAe;MAAEC,QAAQ,EAAE,IAAI,CAACzB,UAAU,CAACe,UAAU;MAAEW,SAAS,EAAE,IAAI,CAAC1B,UAAU,CAACe;KACpG,CAAC;EACJ;EAEAlD,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACzB,QAAQ,EAAE;IAEnB,IAAI,CAAC6E,MAAM,CAACC,GAAG,CAACS,eAAe,CAAC;MAC9BP,mBAAmB,EAAGC,MAAM,IAAI;QAC9B,OAAO,IAAI,CAACC,qBAAqB,CAACD,MAAM,CAAC;MAC3C,CAAC;MACDE,UAAU,EAAE,IAAI,CAACC,eAAe;MAAEC,QAAQ,EAAE,IAAI,CAACzB,UAAU,CAACe;KAC7D,CAAC;EACJ;EAEAO,qBAAqBA,CAACD,MAAkC;IACtD,MAAMO,kBAAkB,GAAGP,MAAM,CAACQ,MAAM,CAACC,SAAS,EAAE;IACpD,MAAMC,oBAAoB,GAAG,IAAI,CAAC/B,UAAU,CAACgC,YAAY,EAAEC,2BAA2B,CAAC9B,IAAI,CAAC+B,CAAC,IAAIA,CAAC,CAACL,MAAM,KAAKD,kBAAkB,CAACO,KAAK,CAAC;IACvI,IAAIJ,oBAAoB,EAAEK,MAAM,CAACC,IAAI,KAAKpH,gBAAgB,CAACqH,QAAQ,EAAE;MACnEV,kBAAkB,CAACW,cAAc,GAAGvH,cAAc,CAChDqG,MAAM,CAAC/D,KAAK,EACZyE,oBAAoB,CAACK,MAAM,CAACI,QAAQ,EACpCT,oBAAoB,CAACK,MAAM,CAACK,aAAa,CAAC;MAC5C,OAAOb,kBAAkB,CAACW,cAAc;IAC1C;IAEA,OAAOlB,MAAM,CAAC/D,KAAK;EACrB;EAEA4B,oBAAoBA,CAACwD,IAA6B,EAAEjF,IAAY;IAC9D,IAAI,CAACN,kBAAkB,CAACqC,IAAI,CAACkD,IAAI,CAAC;IAClC,IAAI,CAAC1E,gBAAgB,GAAGP,IAAI;IAC5B,IAAI,CAACpB,YAAY,GAAGqG,IAAI;IAGxB;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACC,aAAa,KAAKC,SAAS,EAAE;QACpC,IAAI,CAAC5B,MAAM,CAACC,GAAG,EAAE4B,kBAAkB,EAAE;MACvC;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEAC,sBAAsBA,CAACC,MAAkB;IACvC,IAAI,CAACJ,aAAa,GAAGI,MAAM;EAC7B;CACD;AA9TUC,UAAA,EAARnI,KAAK,EAAE,C,wDAAsB;AACrBmI,UAAA,EAARnI,KAAK,EAAE,C,uDAAwB;AACvBmI,UAAA,EAARnI,KAAK,EAAE,C,4DAA0B;AACzBmI,UAAA,EAARnI,KAAK,EAAE,C,4DAA0B;AACzBmI,UAAA,EAARnI,KAAK,EAAE,C,mEAAiC;AAChCmI,UAAA,EAARnI,KAAK,EAAE,C,mDAAuB;AACtBmI,UAAA,EAARnI,KAAK,EAAE,C,4DAA2B;AAC1BmI,UAAA,EAARnI,KAAK,EAAE,C,mEAA6B;AAC5BmI,UAAA,EAARnI,KAAK,EAAE,C,wDAAqB;AACpBmI,UAAA,EAARnI,KAAK,EAAE,C,qDAAkB;AACjBmI,UAAA,EAARnI,KAAK,EAAE,C,yDAA8C;AAC7CmI,UAAA,EAARnI,KAAK,EAAE,C,6DAA0B;AACzBmI,UAAA,EAARnI,KAAK,EAAE,C,0DAAuB;AACtBmI,UAAA,EAARnI,KAAK,EAAE,C,2DAAwB;AACvBmI,UAAA,EAARnI,KAAK,EAAE,C,sDAAmB;AAClBmI,UAAA,EAARnI,KAAK,EAAE,C,oDAAuB;AAErBmI,UAAA,EAATlI,MAAM,EAAE,C,2DAAwD;AACvDkI,UAAA,EAATlI,MAAM,EAAE,C,2DAAwD;AACvDkI,UAAA,EAATlI,MAAM,EAAE,C,oDAAiD;AAChDkI,UAAA,EAATlI,MAAM,EAAE,C,wDAAqD;AACpDkI,UAAA,EAATlI,MAAM,EAAE,C,4DAAyD;AACxDkI,UAAA,EAATlI,MAAM,EAAE,C,2DAAwD;AACvDkI,UAAA,EAATlI,MAAM,EAAE,C,wDAAqD;AACpDkI,UAAA,EAATlI,MAAM,EAAE,C,kEAA+D;AAC9DkI,UAAA,EAATlI,MAAM,EAAE,C,+DAA+D;AAC9DkI,UAAA,EAATlI,MAAM,EAAE,C,0DAA0D;AA5BxDe,oBAAoB,GAAAmH,UAAA,EAPhCrI,SAAS,CAAC;EACTsI,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,+BAA+B,CAAC;EAC5CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC9H,aAAa,EAAED,aAAa,EAAEH,WAAW,EAAEC,OAAO,EAAEF,YAAY,EAAED,UAAU,EAAEO,iBAAiB,EAAEH,IAAI,EAAEM,0BAA0B,EAAEC,kBAAkB;CAChK,CAAC,C,EACWC,oBAAoB,CAgUhC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}