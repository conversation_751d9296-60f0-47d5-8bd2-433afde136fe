{"ast": null, "code": "import { createAssertionFunction } from '../../errors/createAssertionFunction.mjs';\nimport '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar PinpointValidationErrorCode;\n(function (PinpointValidationErrorCode) {\n  PinpointValidationErrorCode[\"NoAppId\"] = \"NoAppId\";\n})(PinpointValidationErrorCode || (PinpointValidationErrorCode = {}));\nconst pinpointValidationErrorMap = {\n  [PinpointValidationErrorCode.NoAppId]: {\n    message: 'Missing application id.'\n  }\n};\nconst assert = createAssertionFunction(pinpointValidationErrorMap);\nexport { PinpointValidationErrorCode, assert };", "map": {"version": 3, "names": ["createAssertionFunction", "PinpointValidationErrorCode", "pinpointValidationErrorMap", "NoAppId", "message", "assert"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/awsClients/pinpoint/errorHelpers.mjs"], "sourcesContent": ["import { createAssertionFunction } from '../../errors/createAssertionFunction.mjs';\nimport '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar PinpointValidationErrorCode;\n(function (PinpointValidationErrorCode) {\n    PinpointValidationErrorCode[\"NoAppId\"] = \"NoAppId\";\n})(PinpointValidationErrorCode || (PinpointValidationErrorCode = {}));\nconst pinpointValidationErrorMap = {\n    [PinpointValidationErrorCode.NoAppId]: {\n        message: 'Missing application id.',\n    },\n};\nconst assert = createAssertionFunction(pinpointValidationErrorMap);\n\nexport { PinpointValidationErrorCode, assert };\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,0CAA0C;AAClF,OAAO,wBAAwB;AAC/B,OAAO,+BAA+B;;AAEtC;AACA;AACA,IAAIC,2BAA2B;AAC/B,CAAC,UAAUA,2BAA2B,EAAE;EACpCA,2BAA2B,CAAC,SAAS,CAAC,GAAG,SAAS;AACtD,CAAC,EAAEA,2BAA2B,KAAKA,2BAA2B,GAAG,CAAC,CAAC,CAAC,CAAC;AACrE,MAAMC,0BAA0B,GAAG;EAC/B,CAACD,2BAA2B,CAACE,OAAO,GAAG;IACnCC,OAAO,EAAE;EACb;AACJ,CAAC;AACD,MAAMC,MAAM,GAAGL,uBAAuB,CAACE,0BAA0B,CAAC;AAElE,SAASD,2BAA2B,EAAEI,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}