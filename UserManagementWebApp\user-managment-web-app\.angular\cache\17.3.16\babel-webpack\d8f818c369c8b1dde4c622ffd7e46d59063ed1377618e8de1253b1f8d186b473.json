{"ast": null, "code": "'use strict';\n\nvar callBound = require('call-bound');\nvar safeRegexTest = require('safe-regex-test');\nvar isFnRegex = safeRegexTest(/^\\s*(?:function)?\\*/);\nvar hasToStringTag = require('has-tostringtag/shams')();\nvar getProto = require('get-proto');\nvar toStr = callBound('Object.prototype.toString');\nvar fnToStr = callBound('Function.prototype.toString');\nvar getGeneratorFunc = function () {\n  // eslint-disable-line consistent-return\n  if (!hasToStringTag) {\n    return false;\n  }\n  try {\n    return Function('return function*() {}')();\n  } catch (e) {}\n};\n/** @type {undefined | false | null | GeneratorFunctionConstructor} */\nvar GeneratorFunction;\n\n/** @type {import('.')} */\nmodule.exports = function isGeneratorFunction(fn) {\n  if (typeof fn !== 'function') {\n    return false;\n  }\n  if (isFnRegex(fnToStr(fn))) {\n    return true;\n  }\n  if (!hasToStringTag) {\n    var str = toStr(fn);\n    return str === '[object GeneratorFunction]';\n  }\n  if (!getProto) {\n    return false;\n  }\n  if (typeof GeneratorFunction === 'undefined') {\n    var generatorFunc = getGeneratorFunc();\n    GeneratorFunction = generatorFunc\n    // eslint-disable-next-line no-extra-parens\n    ? (/** @type {GeneratorFunctionConstructor} */getProto(generatorFunc)) : false;\n  }\n  return getProto(fn) === GeneratorFunction;\n};", "map": {"version": 3, "names": ["callBound", "require", "safeRegexTest", "isFnRegex", "hasToStringTag", "getProto", "toStr", "fnToStr", "getGeneratorFunc", "Function", "e", "GeneratorFunction", "module", "exports", "isGeneratorFunction", "fn", "str", "generatorFunc"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/is-generator-function/index.js"], "sourcesContent": ["'use strict';\n\nvar callBound = require('call-bound');\nvar safeRegexTest = require('safe-regex-test');\nvar isFnRegex = safeRegexTest(/^\\s*(?:function)?\\*/);\nvar hasToStringTag = require('has-tostringtag/shams')();\nvar getProto = require('get-proto');\n\nvar toStr = callBound('Object.prototype.toString');\nvar fnToStr = callBound('Function.prototype.toString');\n\nvar getGeneratorFunc = function () { // eslint-disable-line consistent-return\n\tif (!hasToStringTag) {\n\t\treturn false;\n\t}\n\ttry {\n\t\treturn Function('return function*() {}')();\n\t} catch (e) {\n\t}\n};\n/** @type {undefined | false | null | GeneratorFunctionConstructor} */\nvar GeneratorFunction;\n\n/** @type {import('.')} */\nmodule.exports = function isGeneratorFunction(fn) {\n\tif (typeof fn !== 'function') {\n\t\treturn false;\n\t}\n\tif (isFnRegex(fnToStr(fn))) {\n\t\treturn true;\n\t}\n\tif (!hasToStringTag) {\n\t\tvar str = toStr(fn);\n\t\treturn str === '[object GeneratorFunction]';\n\t}\n\tif (!getProto) {\n\t\treturn false;\n\t}\n\tif (typeof GeneratorFunction === 'undefined') {\n\t\tvar generatorFunc = getGeneratorFunc();\n\t\tGeneratorFunction = generatorFunc\n\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\t? /** @type {GeneratorFunctionConstructor} */ (getProto(generatorFunc))\n\t\t\t: false;\n\t}\n\treturn getProto(fn) === GeneratorFunction;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,SAAS,GAAGC,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIC,aAAa,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAC9C,IAAIE,SAAS,GAAGD,aAAa,CAAC,qBAAqB,CAAC;AACpD,IAAIE,cAAc,GAAGH,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC;AACvD,IAAII,QAAQ,GAAGJ,OAAO,CAAC,WAAW,CAAC;AAEnC,IAAIK,KAAK,GAAGN,SAAS,CAAC,2BAA2B,CAAC;AAClD,IAAIO,OAAO,GAAGP,SAAS,CAAC,6BAA6B,CAAC;AAEtD,IAAIQ,gBAAgB,GAAG,SAAAA,CAAA,EAAY;EAAE;EACpC,IAAI,CAACJ,cAAc,EAAE;IACpB,OAAO,KAAK;EACb;EACA,IAAI;IACH,OAAOK,QAAQ,CAAC,uBAAuB,CAAC,CAAC,CAAC;EAC3C,CAAC,CAAC,OAAOC,CAAC,EAAE,CACZ;AACD,CAAC;AACD;AACA,IAAIC,iBAAiB;;AAErB;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,mBAAmBA,CAACC,EAAE,EAAE;EACjD,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;IAC7B,OAAO,KAAK;EACb;EACA,IAAIZ,SAAS,CAACI,OAAO,CAACQ,EAAE,CAAC,CAAC,EAAE;IAC3B,OAAO,IAAI;EACZ;EACA,IAAI,CAACX,cAAc,EAAE;IACpB,IAAIY,GAAG,GAAGV,KAAK,CAACS,EAAE,CAAC;IACnB,OAAOC,GAAG,KAAK,4BAA4B;EAC5C;EACA,IAAI,CAACX,QAAQ,EAAE;IACd,OAAO,KAAK;EACb;EACA,IAAI,OAAOM,iBAAiB,KAAK,WAAW,EAAE;IAC7C,IAAIM,aAAa,GAAGT,gBAAgB,CAAC,CAAC;IACtCG,iBAAiB,GAAGM;IACnB;IAAA,GACE,2CAA6CZ,QAAQ,CAACY,aAAa,CAAC,IACpE,KAAK;EACT;EACA,OAAOZ,QAAQ,CAACU,EAAE,CAAC,KAAKJ,iBAAiB;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}