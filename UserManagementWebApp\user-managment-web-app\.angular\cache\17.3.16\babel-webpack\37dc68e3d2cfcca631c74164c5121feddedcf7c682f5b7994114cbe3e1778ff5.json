{"ast": null, "code": "'use strict';\n\nvar whichTypedArray = require('which-typed-array');\n\n/** @type {import('.')} */\nmodule.exports = function isTypedArray(value) {\n  return !!whichTypedArray(value);\n};", "map": {"version": 3, "names": ["whichTypedArray", "require", "module", "exports", "isTypedArray", "value"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/is-typed-array/index.js"], "sourcesContent": ["'use strict';\n\nvar whichTypedArray = require('which-typed-array');\n\n/** @type {import('.')} */\nmodule.exports = function isTypedArray(value) {\n\treturn !!whichTypedArray(value);\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,mBAAmB,CAAC;;AAElD;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC7C,OAAO,CAAC,CAACL,eAAe,CAACK,KAAK,CAAC;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}