{"ast": null, "code": "import { getCrypto } from './globalHelpers/index.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst generateRandomString = length => {\n  const STATE_CHARSET = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  const result = [];\n  const randomNums = new Uint8Array(length);\n  getCrypto().getRandomValues(randomNums);\n  for (const num of randomNums) {\n    result.push(STATE_CHARSET[num % STATE_CHARSET.length]);\n  }\n  return result.join('');\n};\nexport { generateRandomString };", "map": {"version": 3, "names": ["getCrypto", "generateRandomString", "length", "STATE_CHARSET", "result", "randomNums", "Uint8Array", "getRandomValues", "num", "push", "join"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/generateRandomString.mjs"], "sourcesContent": ["import { getCrypto } from './globalHelpers/index.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst generateRandomString = (length) => {\n    const STATE_CHARSET = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n    const result = [];\n    const randomNums = new Uint8Array(length);\n    getCrypto().getRandomValues(randomNums);\n    for (const num of randomNums) {\n        result.push(STATE_CHARSET[num % STATE_CHARSET.length]);\n    }\n    return result.join('');\n};\n\nexport { generateRandomString };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,2BAA2B;;AAErD;AACA;AACA,MAAMC,oBAAoB,GAAIC,MAAM,IAAK;EACrC,MAAMC,aAAa,GAAG,gEAAgE;EACtF,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAACJ,MAAM,CAAC;EACzCF,SAAS,CAAC,CAAC,CAACO,eAAe,CAACF,UAAU,CAAC;EACvC,KAAK,MAAMG,GAAG,IAAIH,UAAU,EAAE;IAC1BD,MAAM,CAACK,IAAI,CAACN,aAAa,CAACK,GAAG,GAAGL,aAAa,CAACD,MAAM,CAAC,CAAC;EAC1D;EACA,OAAOE,MAAM,CAACM,IAAI,CAAC,EAAE,CAAC;AAC1B,CAAC;AAED,SAAST,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}