{"ast": null, "code": "import { Hub, AMPLIFY_SYMBOL } from '../Hub/index.mjs';\nimport '../types/errors.mjs';\nimport '../errors/errorHelpers.mjs';\nimport '../utils/getClientInfo/getClientInfo.mjs';\nimport '../utils/retry/retry.mjs';\nimport { deepFreeze } from '../utils/deepFreeze.mjs';\nimport '../parseAWSExports.mjs';\nimport { ADD_OAUTH_LISTENER } from './constants.mjs';\nimport 'uuid';\nimport { parseAmplifyConfig } from '../utils/parseAmplifyConfig.mjs';\nimport './Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport '../Platform/index.mjs';\nimport '../Platform/types.mjs';\nimport '../BackgroundProcessManager/types.mjs';\nimport '../Reachability/Reachability.mjs';\nimport '../utils/sessionListener/index.mjs';\nimport { AuthClass } from './Auth/index.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass AmplifyClass {\n  constructor() {\n    this.oAuthListener = undefined;\n    this.resourcesConfig = {};\n    this.libraryOptions = {};\n    this.Auth = new AuthClass();\n  }\n  /**\n   * Configures Amplify for use with your back-end resources.\n   *\n   * @remarks\n   * This API does not perform any merging of either `resourcesConfig` or `libraryOptions`. The most recently\n   * provided values will be used after configuration.\n   *\n   * @remarks\n   * `configure` can be used to specify additional library options where available for supported categories.\n   *\n   * @param resourceConfig - Back-end resource configuration. Typically provided via the `aws-exports.js` file.\n   * @param libraryOptions - Additional options for customizing the behavior of the library.\n   */\n  configure(resourcesConfig, libraryOptions) {\n    const resolvedResourceConfig = parseAmplifyConfig(resourcesConfig);\n    this.resourcesConfig = resolvedResourceConfig;\n    if (libraryOptions) {\n      this.libraryOptions = libraryOptions;\n    }\n    // Make resource config immutable\n    this.resourcesConfig = deepFreeze(this.resourcesConfig);\n    this.Auth.configure(this.resourcesConfig.Auth, this.libraryOptions.Auth);\n    Hub.dispatch('core', {\n      event: 'configure',\n      data: this.resourcesConfig\n    }, 'Configure', AMPLIFY_SYMBOL);\n    this.notifyOAuthListener();\n  }\n  /**\n   * Provides access to the current back-end resource configuration for the Library.\n   *\n   * @returns Returns the immutable back-end resource configuration.\n   */\n  getConfig() {\n    return this.resourcesConfig;\n  }\n  /** @internal */\n  [ADD_OAUTH_LISTENER](listener) {\n    if (this.resourcesConfig.Auth?.Cognito.loginWith?.oauth) {\n      // when Amplify has been configured with a valid OAuth config while adding the listener, run it directly\n      listener(this.resourcesConfig.Auth?.Cognito);\n    } else {\n      // otherwise register the listener and run it later when Amplify gets configured with a valid oauth config\n      this.oAuthListener = listener;\n    }\n  }\n  notifyOAuthListener() {\n    if (!this.resourcesConfig.Auth?.Cognito.loginWith?.oauth || !this.oAuthListener) {\n      return;\n    }\n    this.oAuthListener(this.resourcesConfig.Auth?.Cognito);\n    // the listener should only be notified once with a valid oauth config\n    this.oAuthListener = undefined;\n  }\n}\n/**\n * The `Amplify` utility is used to configure the library.\n *\n * @remarks\n * `Amplify` orchestrates cross-category communication within the library.\n */\nconst Amplify = new AmplifyClass();\nexport { Amplify, AmplifyClass };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "AMPLIFY_SYMBOL", "deepFreeze", "ADD_OAUTH_LISTENER", "parseAmplifyConfig", "AuthClass", "AmplifyClass", "constructor", "oAuthListener", "undefined", "resourcesConfig", "libraryOptions", "<PERSON><PERSON>", "configure", "resolvedResourceConfig", "dispatch", "event", "data", "notifyOAuth<PERSON><PERSON><PERSON>", "getConfig", "listener", "Cognito", "loginWith", "o<PERSON>h", "Amplify"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/singleton/Amplify.mjs"], "sourcesContent": ["import { Hub, AMPLIFY_SYMBOL } from '../Hub/index.mjs';\nimport '../types/errors.mjs';\nimport '../errors/errorHelpers.mjs';\nimport '../utils/getClientInfo/getClientInfo.mjs';\nimport '../utils/retry/retry.mjs';\nimport { deepFreeze } from '../utils/deepFreeze.mjs';\nimport '../parseAWSExports.mjs';\nimport { ADD_OAUTH_LISTENER } from './constants.mjs';\nimport 'uuid';\nimport { parseAmplifyConfig } from '../utils/parseAmplifyConfig.mjs';\nimport './Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport '../Platform/index.mjs';\nimport '../Platform/types.mjs';\nimport '../BackgroundProcessManager/types.mjs';\nimport '../Reachability/Reachability.mjs';\nimport '../utils/sessionListener/index.mjs';\nimport { AuthClass } from './Auth/index.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass AmplifyClass {\n    constructor() {\n        this.oAuthListener = undefined;\n        this.resourcesConfig = {};\n        this.libraryOptions = {};\n        this.Auth = new AuthClass();\n    }\n    /**\n     * Configures Amplify for use with your back-end resources.\n     *\n     * @remarks\n     * This API does not perform any merging of either `resourcesConfig` or `libraryOptions`. The most recently\n     * provided values will be used after configuration.\n     *\n     * @remarks\n     * `configure` can be used to specify additional library options where available for supported categories.\n     *\n     * @param resourceConfig - Back-end resource configuration. Typically provided via the `aws-exports.js` file.\n     * @param libraryOptions - Additional options for customizing the behavior of the library.\n     */\n    configure(resourcesConfig, libraryOptions) {\n        const resolvedResourceConfig = parseAmplifyConfig(resourcesConfig);\n        this.resourcesConfig = resolvedResourceConfig;\n        if (libraryOptions) {\n            this.libraryOptions = libraryOptions;\n        }\n        // Make resource config immutable\n        this.resourcesConfig = deepFreeze(this.resourcesConfig);\n        this.Auth.configure(this.resourcesConfig.Auth, this.libraryOptions.Auth);\n        Hub.dispatch('core', {\n            event: 'configure',\n            data: this.resourcesConfig,\n        }, 'Configure', AMPLIFY_SYMBOL);\n        this.notifyOAuthListener();\n    }\n    /**\n     * Provides access to the current back-end resource configuration for the Library.\n     *\n     * @returns Returns the immutable back-end resource configuration.\n     */\n    getConfig() {\n        return this.resourcesConfig;\n    }\n    /** @internal */\n    [ADD_OAUTH_LISTENER](listener) {\n        if (this.resourcesConfig.Auth?.Cognito.loginWith?.oauth) {\n            // when Amplify has been configured with a valid OAuth config while adding the listener, run it directly\n            listener(this.resourcesConfig.Auth?.Cognito);\n        }\n        else {\n            // otherwise register the listener and run it later when Amplify gets configured with a valid oauth config\n            this.oAuthListener = listener;\n        }\n    }\n    notifyOAuthListener() {\n        if (!this.resourcesConfig.Auth?.Cognito.loginWith?.oauth ||\n            !this.oAuthListener) {\n            return;\n        }\n        this.oAuthListener(this.resourcesConfig.Auth?.Cognito);\n        // the listener should only be notified once with a valid oauth config\n        this.oAuthListener = undefined;\n    }\n}\n/**\n * The `Amplify` utility is used to configure the library.\n *\n * @remarks\n * `Amplify` orchestrates cross-category communication within the library.\n */\nconst Amplify = new AmplifyClass();\n\nexport { Amplify, AmplifyClass };\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,cAAc,QAAQ,kBAAkB;AACtD,OAAO,qBAAqB;AAC5B,OAAO,4BAA4B;AACnC,OAAO,0CAA0C;AACjD,OAAO,0BAA0B;AACjC,SAASC,UAAU,QAAQ,yBAAyB;AACpD,OAAO,wBAAwB;AAC/B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAO,MAAM;AACb,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,OAAO,+BAA+B;AACtC,OAAO,uBAAuB;AAC9B,OAAO,2BAA2B;AAClC,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,uCAAuC;AAC9C,OAAO,kCAAkC;AACzC,OAAO,oCAAoC;AAC3C,SAASC,SAAS,QAAQ,kBAAkB;;AAE5C;AACA;AACA,MAAMC,YAAY,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,aAAa,GAAGC,SAAS;IAC9B,IAAI,CAACC,eAAe,GAAG,CAAC,CAAC;IACzB,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;IACxB,IAAI,CAACC,IAAI,GAAG,IAAIP,SAAS,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIQ,SAASA,CAACH,eAAe,EAAEC,cAAc,EAAE;IACvC,MAAMG,sBAAsB,GAAGV,kBAAkB,CAACM,eAAe,CAAC;IAClE,IAAI,CAACA,eAAe,GAAGI,sBAAsB;IAC7C,IAAIH,cAAc,EAAE;MAChB,IAAI,CAACA,cAAc,GAAGA,cAAc;IACxC;IACA;IACA,IAAI,CAACD,eAAe,GAAGR,UAAU,CAAC,IAAI,CAACQ,eAAe,CAAC;IACvD,IAAI,CAACE,IAAI,CAACC,SAAS,CAAC,IAAI,CAACH,eAAe,CAACE,IAAI,EAAE,IAAI,CAACD,cAAc,CAACC,IAAI,CAAC;IACxEZ,GAAG,CAACe,QAAQ,CAAC,MAAM,EAAE;MACjBC,KAAK,EAAE,WAAW;MAClBC,IAAI,EAAE,IAAI,CAACP;IACf,CAAC,EAAE,WAAW,EAAET,cAAc,CAAC;IAC/B,IAAI,CAACiB,mBAAmB,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACIC,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACT,eAAe;EAC/B;EACA;EACA,CAACP,kBAAkB,EAAEiB,QAAQ,EAAE;IAC3B,IAAI,IAAI,CAACV,eAAe,CAACE,IAAI,EAAES,OAAO,CAACC,SAAS,EAAEC,KAAK,EAAE;MACrD;MACAH,QAAQ,CAAC,IAAI,CAACV,eAAe,CAACE,IAAI,EAAES,OAAO,CAAC;IAChD,CAAC,MACI;MACD;MACA,IAAI,CAACb,aAAa,GAAGY,QAAQ;IACjC;EACJ;EACAF,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC,IAAI,CAACR,eAAe,CAACE,IAAI,EAAES,OAAO,CAACC,SAAS,EAAEC,KAAK,IACpD,CAAC,IAAI,CAACf,aAAa,EAAE;MACrB;IACJ;IACA,IAAI,CAACA,aAAa,CAAC,IAAI,CAACE,eAAe,CAACE,IAAI,EAAES,OAAO,CAAC;IACtD;IACA,IAAI,CAACb,aAAa,GAAGC,SAAS;EAClC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMe,OAAO,GAAG,IAAIlB,YAAY,CAAC,CAAC;AAElC,SAASkB,OAAO,EAAElB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}