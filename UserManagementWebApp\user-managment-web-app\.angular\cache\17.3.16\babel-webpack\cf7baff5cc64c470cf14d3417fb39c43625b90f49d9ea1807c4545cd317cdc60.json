{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { assertTokenProviderConfig } from '@aws-amplify/core/internals/utils';\nimport { getAuthStorageKeys } from '../tokenProvider/TokenStore.mjs';\nimport { OAuthStorageKeys } from './types.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst V5_HOSTED_UI_KEY = 'amplify-signin-with-hostedUI';\nconst name = 'CognitoIdentityServiceProvider';\nclass DefaultOAuthStore {\n  constructor(keyValueStorage) {\n    this.keyValueStorage = keyValueStorage;\n  }\n  clearOAuthInflightData() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      assertTokenProviderConfig(_this.cognitoConfig);\n      const authKeys = createKeysForAuthStorage(name, _this.cognitoConfig.userPoolClientId);\n      yield Promise.all([_this.keyValueStorage.removeItem(authKeys.inflightOAuth), _this.keyValueStorage.removeItem(authKeys.oauthPKCE), _this.keyValueStorage.removeItem(authKeys.oauthState)]);\n    })();\n  }\n  clearOAuthData() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      assertTokenProviderConfig(_this2.cognitoConfig);\n      const authKeys = createKeysForAuthStorage(name, _this2.cognitoConfig.userPoolClientId);\n      yield _this2.clearOAuthInflightData();\n      yield _this2.keyValueStorage.removeItem(V5_HOSTED_UI_KEY); // remove in case a customer migrated an App from v5 to v6\n      return _this2.keyValueStorage.removeItem(authKeys.oauthSignIn);\n    })();\n  }\n  loadOAuthState() {\n    assertTokenProviderConfig(this.cognitoConfig);\n    const authKeys = createKeysForAuthStorage(name, this.cognitoConfig.userPoolClientId);\n    return this.keyValueStorage.getItem(authKeys.oauthState);\n  }\n  storeOAuthState(state) {\n    assertTokenProviderConfig(this.cognitoConfig);\n    const authKeys = createKeysForAuthStorage(name, this.cognitoConfig.userPoolClientId);\n    return this.keyValueStorage.setItem(authKeys.oauthState, state);\n  }\n  loadPKCE() {\n    assertTokenProviderConfig(this.cognitoConfig);\n    const authKeys = createKeysForAuthStorage(name, this.cognitoConfig.userPoolClientId);\n    return this.keyValueStorage.getItem(authKeys.oauthPKCE);\n  }\n  storePKCE(pkce) {\n    assertTokenProviderConfig(this.cognitoConfig);\n    const authKeys = createKeysForAuthStorage(name, this.cognitoConfig.userPoolClientId);\n    return this.keyValueStorage.setItem(authKeys.oauthPKCE, pkce);\n  }\n  setAuthConfig(authConfigParam) {\n    this.cognitoConfig = authConfigParam;\n  }\n  loadOAuthInFlight() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      assertTokenProviderConfig(_this3.cognitoConfig);\n      const authKeys = createKeysForAuthStorage(name, _this3.cognitoConfig.userPoolClientId);\n      return (yield _this3.keyValueStorage.getItem(authKeys.inflightOAuth)) === 'true';\n    })();\n  }\n  storeOAuthInFlight(inflight) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      assertTokenProviderConfig(_this4.cognitoConfig);\n      const authKeys = createKeysForAuthStorage(name, _this4.cognitoConfig.userPoolClientId);\n      yield _this4.keyValueStorage.setItem(authKeys.inflightOAuth, `${inflight}`);\n    })();\n  }\n  loadOAuthSignIn() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      assertTokenProviderConfig(_this5.cognitoConfig);\n      const authKeys = createKeysForAuthStorage(name, _this5.cognitoConfig.userPoolClientId);\n      const isLegacyHostedUISignIn = yield _this5.keyValueStorage.getItem(V5_HOSTED_UI_KEY);\n      const [isOAuthSignIn, preferPrivateSession] = (yield _this5.keyValueStorage.getItem(authKeys.oauthSignIn))?.split(',') ?? [];\n      return {\n        isOAuthSignIn: isOAuthSignIn === 'true' || isLegacyHostedUISignIn === 'true',\n        preferPrivateSession: preferPrivateSession === 'true'\n      };\n    })();\n  }\n  storeOAuthSignIn(oauthSignIn, preferPrivateSession = false) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      assertTokenProviderConfig(_this6.cognitoConfig);\n      const authKeys = createKeysForAuthStorage(name, _this6.cognitoConfig.userPoolClientId);\n      yield _this6.keyValueStorage.setItem(authKeys.oauthSignIn, `${oauthSignIn},${preferPrivateSession}`);\n    })();\n  }\n}\nconst createKeysForAuthStorage = (provider, identifier) => {\n  return getAuthStorageKeys(OAuthStorageKeys)(provider, identifier);\n};\nexport { DefaultOAuthStore };", "map": {"version": 3, "names": ["assertTokenProviderConfig", "getAuthStorageKeys", "OAuthStorageKeys", "V5_HOSTED_UI_KEY", "name", "DefaultOAuthStore", "constructor", "keyValueStorage", "clearOAuthInflightData", "_this", "_asyncToGenerator", "cognitoConfig", "auth<PERSON><PERSON><PERSON>", "createKeysForAuthStorage", "userPoolClientId", "Promise", "all", "removeItem", "inflightOAuth", "oauthPKCE", "oauthState", "clearOAuthData", "_this2", "oauthSignIn", "loadOAuthState", "getItem", "storeOAuthState", "state", "setItem", "loadPKCE", "storePKCE", "pkce", "setAuthConfig", "authConfigParam", "loadOAuthInFlight", "_this3", "storeOAuthInFlight", "inflight", "_this4", "loadOAuthSignIn", "_this5", "isLegacyHostedUISignIn", "isOAuthSignIn", "preferPrivateSession", "split", "storeOAuthSignIn", "_this6", "provider", "identifier"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/signInWithRedirectStore.mjs"], "sourcesContent": ["import { assertTokenProviderConfig } from '@aws-amplify/core/internals/utils';\nimport { getAuthStorageKeys } from '../tokenProvider/TokenStore.mjs';\nimport { OAuthStorageKeys } from './types.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst V5_HOSTED_UI_KEY = 'amplify-signin-with-hostedUI';\nconst name = 'CognitoIdentityServiceProvider';\nclass DefaultOAuthStore {\n    constructor(keyValueStorage) {\n        this.keyValueStorage = keyValueStorage;\n    }\n    async clearOAuthInflightData() {\n        assertTokenProviderConfig(this.cognitoConfig);\n        const authKeys = createKeysForAuthStorage(name, this.cognitoConfig.userPoolClientId);\n        await Promise.all([\n            this.keyValueStorage.removeItem(authKeys.inflightOAuth),\n            this.keyValueStorage.removeItem(authKeys.oauthPKCE),\n            this.keyValueStorage.removeItem(authKeys.oauthState),\n        ]);\n    }\n    async clearOAuthData() {\n        assertTokenProviderConfig(this.cognitoConfig);\n        const authKeys = createKeysForAuthStorage(name, this.cognitoConfig.userPoolClientId);\n        await this.clearOAuthInflightData();\n        await this.keyValueStorage.removeItem(V5_HOSTED_UI_KEY); // remove in case a customer migrated an App from v5 to v6\n        return this.keyValueStorage.removeItem(authKeys.oauthSignIn);\n    }\n    loadOAuthState() {\n        assertTokenProviderConfig(this.cognitoConfig);\n        const authKeys = createKeysForAuthStorage(name, this.cognitoConfig.userPoolClientId);\n        return this.keyValueStorage.getItem(authKeys.oauthState);\n    }\n    storeOAuthState(state) {\n        assertTokenProviderConfig(this.cognitoConfig);\n        const authKeys = createKeysForAuthStorage(name, this.cognitoConfig.userPoolClientId);\n        return this.keyValueStorage.setItem(authKeys.oauthState, state);\n    }\n    loadPKCE() {\n        assertTokenProviderConfig(this.cognitoConfig);\n        const authKeys = createKeysForAuthStorage(name, this.cognitoConfig.userPoolClientId);\n        return this.keyValueStorage.getItem(authKeys.oauthPKCE);\n    }\n    storePKCE(pkce) {\n        assertTokenProviderConfig(this.cognitoConfig);\n        const authKeys = createKeysForAuthStorage(name, this.cognitoConfig.userPoolClientId);\n        return this.keyValueStorage.setItem(authKeys.oauthPKCE, pkce);\n    }\n    setAuthConfig(authConfigParam) {\n        this.cognitoConfig = authConfigParam;\n    }\n    async loadOAuthInFlight() {\n        assertTokenProviderConfig(this.cognitoConfig);\n        const authKeys = createKeysForAuthStorage(name, this.cognitoConfig.userPoolClientId);\n        return ((await this.keyValueStorage.getItem(authKeys.inflightOAuth)) === 'true');\n    }\n    async storeOAuthInFlight(inflight) {\n        assertTokenProviderConfig(this.cognitoConfig);\n        const authKeys = createKeysForAuthStorage(name, this.cognitoConfig.userPoolClientId);\n        await this.keyValueStorage.setItem(authKeys.inflightOAuth, `${inflight}`);\n    }\n    async loadOAuthSignIn() {\n        assertTokenProviderConfig(this.cognitoConfig);\n        const authKeys = createKeysForAuthStorage(name, this.cognitoConfig.userPoolClientId);\n        const isLegacyHostedUISignIn = await this.keyValueStorage.getItem(V5_HOSTED_UI_KEY);\n        const [isOAuthSignIn, preferPrivateSession] = (await this.keyValueStorage.getItem(authKeys.oauthSignIn))?.split(',') ??\n            [];\n        return {\n            isOAuthSignIn: isOAuthSignIn === 'true' || isLegacyHostedUISignIn === 'true',\n            preferPrivateSession: preferPrivateSession === 'true',\n        };\n    }\n    async storeOAuthSignIn(oauthSignIn, preferPrivateSession = false) {\n        assertTokenProviderConfig(this.cognitoConfig);\n        const authKeys = createKeysForAuthStorage(name, this.cognitoConfig.userPoolClientId);\n        await this.keyValueStorage.setItem(authKeys.oauthSignIn, `${oauthSignIn},${preferPrivateSession}`);\n    }\n}\nconst createKeysForAuthStorage = (provider, identifier) => {\n    return getAuthStorageKeys(OAuthStorageKeys)(provider, identifier);\n};\n\nexport { DefaultOAuthStore };\n"], "mappings": ";AAAA,SAASA,yBAAyB,QAAQ,mCAAmC;AAC7E,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,gBAAgB,QAAQ,aAAa;;AAE9C;AACA;AACA,MAAMC,gBAAgB,GAAG,8BAA8B;AACvD,MAAMC,IAAI,GAAG,gCAAgC;AAC7C,MAAMC,iBAAiB,CAAC;EACpBC,WAAWA,CAACC,eAAe,EAAE;IACzB,IAAI,CAACA,eAAe,GAAGA,eAAe;EAC1C;EACMC,sBAAsBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC3BV,yBAAyB,CAACS,KAAI,CAACE,aAAa,CAAC;MAC7C,MAAMC,QAAQ,GAAGC,wBAAwB,CAACT,IAAI,EAAEK,KAAI,CAACE,aAAa,CAACG,gBAAgB,CAAC;MACpF,MAAMC,OAAO,CAACC,GAAG,CAAC,CACdP,KAAI,CAACF,eAAe,CAACU,UAAU,CAACL,QAAQ,CAACM,aAAa,CAAC,EACvDT,KAAI,CAACF,eAAe,CAACU,UAAU,CAACL,QAAQ,CAACO,SAAS,CAAC,EACnDV,KAAI,CAACF,eAAe,CAACU,UAAU,CAACL,QAAQ,CAACQ,UAAU,CAAC,CACvD,CAAC;IAAC;EACP;EACMC,cAAcA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAZ,iBAAA;MACnBV,yBAAyB,CAACsB,MAAI,CAACX,aAAa,CAAC;MAC7C,MAAMC,QAAQ,GAAGC,wBAAwB,CAACT,IAAI,EAAEkB,MAAI,CAACX,aAAa,CAACG,gBAAgB,CAAC;MACpF,MAAMQ,MAAI,CAACd,sBAAsB,CAAC,CAAC;MACnC,MAAMc,MAAI,CAACf,eAAe,CAACU,UAAU,CAACd,gBAAgB,CAAC,CAAC,CAAC;MACzD,OAAOmB,MAAI,CAACf,eAAe,CAACU,UAAU,CAACL,QAAQ,CAACW,WAAW,CAAC;IAAC;EACjE;EACAC,cAAcA,CAAA,EAAG;IACbxB,yBAAyB,CAAC,IAAI,CAACW,aAAa,CAAC;IAC7C,MAAMC,QAAQ,GAAGC,wBAAwB,CAACT,IAAI,EAAE,IAAI,CAACO,aAAa,CAACG,gBAAgB,CAAC;IACpF,OAAO,IAAI,CAACP,eAAe,CAACkB,OAAO,CAACb,QAAQ,CAACQ,UAAU,CAAC;EAC5D;EACAM,eAAeA,CAACC,KAAK,EAAE;IACnB3B,yBAAyB,CAAC,IAAI,CAACW,aAAa,CAAC;IAC7C,MAAMC,QAAQ,GAAGC,wBAAwB,CAACT,IAAI,EAAE,IAAI,CAACO,aAAa,CAACG,gBAAgB,CAAC;IACpF,OAAO,IAAI,CAACP,eAAe,CAACqB,OAAO,CAAChB,QAAQ,CAACQ,UAAU,EAAEO,KAAK,CAAC;EACnE;EACAE,QAAQA,CAAA,EAAG;IACP7B,yBAAyB,CAAC,IAAI,CAACW,aAAa,CAAC;IAC7C,MAAMC,QAAQ,GAAGC,wBAAwB,CAACT,IAAI,EAAE,IAAI,CAACO,aAAa,CAACG,gBAAgB,CAAC;IACpF,OAAO,IAAI,CAACP,eAAe,CAACkB,OAAO,CAACb,QAAQ,CAACO,SAAS,CAAC;EAC3D;EACAW,SAASA,CAACC,IAAI,EAAE;IACZ/B,yBAAyB,CAAC,IAAI,CAACW,aAAa,CAAC;IAC7C,MAAMC,QAAQ,GAAGC,wBAAwB,CAACT,IAAI,EAAE,IAAI,CAACO,aAAa,CAACG,gBAAgB,CAAC;IACpF,OAAO,IAAI,CAACP,eAAe,CAACqB,OAAO,CAAChB,QAAQ,CAACO,SAAS,EAAEY,IAAI,CAAC;EACjE;EACAC,aAAaA,CAACC,eAAe,EAAE;IAC3B,IAAI,CAACtB,aAAa,GAAGsB,eAAe;EACxC;EACMC,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAzB,iBAAA;MACtBV,yBAAyB,CAACmC,MAAI,CAACxB,aAAa,CAAC;MAC7C,MAAMC,QAAQ,GAAGC,wBAAwB,CAACT,IAAI,EAAE+B,MAAI,CAACxB,aAAa,CAACG,gBAAgB,CAAC;MACpF,OAAQ,OAAOqB,MAAI,CAAC5B,eAAe,CAACkB,OAAO,CAACb,QAAQ,CAACM,aAAa,CAAC,MAAM,MAAM;IAAE;EACrF;EACMkB,kBAAkBA,CAACC,QAAQ,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA5B,iBAAA;MAC/BV,yBAAyB,CAACsC,MAAI,CAAC3B,aAAa,CAAC;MAC7C,MAAMC,QAAQ,GAAGC,wBAAwB,CAACT,IAAI,EAAEkC,MAAI,CAAC3B,aAAa,CAACG,gBAAgB,CAAC;MACpF,MAAMwB,MAAI,CAAC/B,eAAe,CAACqB,OAAO,CAAChB,QAAQ,CAACM,aAAa,EAAE,GAAGmB,QAAQ,EAAE,CAAC;IAAC;EAC9E;EACME,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAA9B,iBAAA;MACpBV,yBAAyB,CAACwC,MAAI,CAAC7B,aAAa,CAAC;MAC7C,MAAMC,QAAQ,GAAGC,wBAAwB,CAACT,IAAI,EAAEoC,MAAI,CAAC7B,aAAa,CAACG,gBAAgB,CAAC;MACpF,MAAM2B,sBAAsB,SAASD,MAAI,CAACjC,eAAe,CAACkB,OAAO,CAACtB,gBAAgB,CAAC;MACnF,MAAM,CAACuC,aAAa,EAAEC,oBAAoB,CAAC,GAAG,OAAOH,MAAI,CAACjC,eAAe,CAACkB,OAAO,CAACb,QAAQ,CAACW,WAAW,CAAC,GAAGqB,KAAK,CAAC,GAAG,CAAC,IAChH,EAAE;MACN,OAAO;QACHF,aAAa,EAAEA,aAAa,KAAK,MAAM,IAAID,sBAAsB,KAAK,MAAM;QAC5EE,oBAAoB,EAAEA,oBAAoB,KAAK;MACnD,CAAC;IAAC;EACN;EACME,gBAAgBA,CAACtB,WAAW,EAAEoB,oBAAoB,GAAG,KAAK,EAAE;IAAA,IAAAG,MAAA;IAAA,OAAApC,iBAAA;MAC9DV,yBAAyB,CAAC8C,MAAI,CAACnC,aAAa,CAAC;MAC7C,MAAMC,QAAQ,GAAGC,wBAAwB,CAACT,IAAI,EAAE0C,MAAI,CAACnC,aAAa,CAACG,gBAAgB,CAAC;MACpF,MAAMgC,MAAI,CAACvC,eAAe,CAACqB,OAAO,CAAChB,QAAQ,CAACW,WAAW,EAAE,GAAGA,WAAW,IAAIoB,oBAAoB,EAAE,CAAC;IAAC;EACvG;AACJ;AACA,MAAM9B,wBAAwB,GAAGA,CAACkC,QAAQ,EAAEC,UAAU,KAAK;EACvD,OAAO/C,kBAAkB,CAACC,gBAAgB,CAAC,CAAC6C,QAAQ,EAAEC,UAAU,CAAC;AACrE,CAAC;AAED,SAAS3C,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}