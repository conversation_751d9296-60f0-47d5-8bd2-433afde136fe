{"ast": null, "code": "import { currentSizeKey } from '../constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * return the byte size of the string\n * @param str\n */\nfunction getByteLength(str) {\n  let ret = 0;\n  ret = str.length;\n  for (let i = str.length; i >= 0; i -= 1) {\n    const charCode = str.charCodeAt(i);\n    if (charCode > 0x7f && charCode <= 0x7ff) {\n      ret += 1;\n    } else if (charCode > 0x7ff && charCode <= 0xffff) {\n      ret += 2;\n    }\n    // trail surrogate\n    if (charCode >= 0xdc00 && charCode <= 0xdfff) {\n      i -= 1;\n    }\n  }\n  return ret;\n}\n/**\n * get current time\n */\nfunction getCurrentTime() {\n  const currentTime = new Date();\n  return currentTime.getTime();\n}\n/**\n * check if passed value is an integer\n */\nfunction isInteger(value) {\n  if (Number.isInteger) {\n    return Number.isInteger(value);\n  }\n  return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n}\nconst getCurrentSizeKey = keyPrefix => `${keyPrefix}${currentSizeKey}`;\nexport { getByteLength, getCurrentSizeKey, getCurrentTime, isInteger };", "map": {"version": 3, "names": ["currentSizeKey", "getByteLength", "str", "ret", "length", "i", "charCode", "charCodeAt", "getCurrentTime", "currentTime", "Date", "getTime", "isInteger", "value", "Number", "isFinite", "Math", "floor", "getCurrentSizeKey", "keyPrefix"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Cache/utils/cacheHelpers.mjs"], "sourcesContent": ["import { currentSizeKey } from '../constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * return the byte size of the string\n * @param str\n */\nfunction getByteLength(str) {\n    let ret = 0;\n    ret = str.length;\n    for (let i = str.length; i >= 0; i -= 1) {\n        const charCode = str.charCodeAt(i);\n        if (charCode > 0x7f && charCode <= 0x7ff) {\n            ret += 1;\n        }\n        else if (charCode > 0x7ff && charCode <= 0xffff) {\n            ret += 2;\n        }\n        // trail surrogate\n        if (charCode >= 0xdc00 && charCode <= 0xdfff) {\n            i -= 1;\n        }\n    }\n    return ret;\n}\n/**\n * get current time\n */\nfunction getCurrentTime() {\n    const currentTime = new Date();\n    return currentTime.getTime();\n}\n/**\n * check if passed value is an integer\n */\nfunction isInteger(value) {\n    if (Number.isInteger) {\n        return Number.isInteger(value);\n    }\n    return (typeof value === 'number' && isFinite(value) && Math.floor(value) === value);\n}\nconst getCurrentSizeKey = (keyPrefix) => `${keyPrefix}${currentSizeKey}`;\n\nexport { getByteLength, getCurrentSizeKey, getCurrentTime, isInteger };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,GAAG,EAAE;EACxB,IAAIC,GAAG,GAAG,CAAC;EACXA,GAAG,GAAGD,GAAG,CAACE,MAAM;EAChB,KAAK,IAAIC,CAAC,GAAGH,GAAG,CAACE,MAAM,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IACrC,MAAMC,QAAQ,GAAGJ,GAAG,CAACK,UAAU,CAACF,CAAC,CAAC;IAClC,IAAIC,QAAQ,GAAG,IAAI,IAAIA,QAAQ,IAAI,KAAK,EAAE;MACtCH,GAAG,IAAI,CAAC;IACZ,CAAC,MACI,IAAIG,QAAQ,GAAG,KAAK,IAAIA,QAAQ,IAAI,MAAM,EAAE;MAC7CH,GAAG,IAAI,CAAC;IACZ;IACA;IACA,IAAIG,QAAQ,IAAI,MAAM,IAAIA,QAAQ,IAAI,MAAM,EAAE;MAC1CD,CAAC,IAAI,CAAC;IACV;EACJ;EACA,OAAOF,GAAG;AACd;AACA;AACA;AACA;AACA,SAASK,cAAcA,CAAA,EAAG;EACtB,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC;EAC9B,OAAOD,WAAW,CAACE,OAAO,CAAC,CAAC;AAChC;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAE;EACtB,IAAIC,MAAM,CAACF,SAAS,EAAE;IAClB,OAAOE,MAAM,CAACF,SAAS,CAACC,KAAK,CAAC;EAClC;EACA,OAAQ,OAAOA,KAAK,KAAK,QAAQ,IAAIE,QAAQ,CAACF,KAAK,CAAC,IAAIG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,KAAKA,KAAK;AACvF;AACA,MAAMK,iBAAiB,GAAIC,SAAS,IAAK,GAAGA,SAAS,GAAGnB,cAAc,EAAE;AAExE,SAASC,aAAa,EAAEiB,iBAAiB,EAAEV,cAAc,EAAEI,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}