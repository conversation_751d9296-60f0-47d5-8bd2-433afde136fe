{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst deepFreeze = object => {\n  const propNames = Reflect.ownKeys(object);\n  for (const name of propNames) {\n    const value = object[name];\n    if (value && typeof value === 'object' || typeof value === 'function') {\n      deepFreeze(value);\n    }\n  }\n  return Object.freeze(object);\n};\nexport { deepFreeze };", "map": {"version": 3, "names": ["deepFreeze", "object", "propNames", "Reflect", "ownKeys", "name", "value", "Object", "freeze"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/deepFreeze.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst deepFreeze = (object) => {\n    const propNames = Reflect.ownKeys(object);\n    for (const name of propNames) {\n        const value = object[name];\n        if ((value && typeof value === 'object') || typeof value === 'function') {\n            deepFreeze(value);\n        }\n    }\n    return Object.freeze(object);\n};\n\nexport { deepFreeze };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,UAAU,GAAIC,MAAM,IAAK;EAC3B,MAAMC,SAAS,GAAGC,OAAO,CAACC,OAAO,CAACH,MAAM,CAAC;EACzC,KAAK,MAAMI,IAAI,IAAIH,SAAS,EAAE;IAC1B,MAAMI,KAAK,GAAGL,MAAM,CAACI,IAAI,CAAC;IAC1B,IAAKC,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAK,OAAOA,KAAK,KAAK,UAAU,EAAE;MACrEN,UAAU,CAACM,KAAK,CAAC;IACrB;EACJ;EACA,OAAOC,MAAM,CAACC,MAAM,CAACP,MAAM,CAAC;AAChC,CAAC;AAED,SAASD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}