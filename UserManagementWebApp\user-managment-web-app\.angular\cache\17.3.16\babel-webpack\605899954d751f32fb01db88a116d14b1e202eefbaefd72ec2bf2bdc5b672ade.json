{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AuthError } from '../../../../errors/AuthError.mjs';\nimport { AuthErrorTypes } from '../../../../types/Auth.mjs';\nimport { oAuthStore } from './oAuthStore.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst flowCancelledMessage = '`signInWithRedirect` has been canceled.';\nconst validationFailedMessage = 'An error occurred while validating the state.';\nconst validationRecoverySuggestion = 'Try to initiate an OAuth flow from Amplify';\nconst validateState = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (state) {\n    const savedState = yield oAuthStore.loadOAuthState();\n    // This is because savedState only exists if the flow was initiated by Amplify\n    const validatedState = state === savedState ? savedState : undefined;\n    if (!validatedState) {\n      throw new AuthError({\n        name: AuthErrorTypes.OAuthSignInError,\n        message: state === null ? flowCancelledMessage : validationFailedMessage,\n        recoverySuggestion: state === null ? undefined : validationRecoverySuggestion\n      });\n    }\n    return validatedState;\n  });\n  return function validateState(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { flowCancelledMessage, validateState, validationFailedMessage, validationRecoverySuggestion };", "map": {"version": 3, "names": ["<PERSON>th<PERSON><PERSON><PERSON>", "AuthErrorTypes", "oAuthStore", "flowCancelledMessage", "validationFailedMessage", "validationRecoverySuggestion", "validateState", "_ref", "_asyncToGenerator", "state", "savedState", "loadOAuthState", "validatedState", "undefined", "name", "OAuthSignInError", "message", "recoverySuggestion", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/oauth/validateState.mjs"], "sourcesContent": ["import { AuthError } from '../../../../errors/AuthError.mjs';\nimport { AuthErrorTypes } from '../../../../types/Auth.mjs';\nimport { oAuthStore } from './oAuthStore.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst flowCancelledMessage = '`signInWithRedirect` has been canceled.';\nconst validationFailedMessage = 'An error occurred while validating the state.';\nconst validationRecoverySuggestion = 'Try to initiate an OAuth flow from Amplify';\nconst validateState = async (state) => {\n    const savedState = await oAuthStore.loadOAuthState();\n    // This is because savedState only exists if the flow was initiated by Amplify\n    const validatedState = state === savedState ? savedState : undefined;\n    if (!validatedState) {\n        throw new AuthError({\n            name: AuthErrorTypes.OAuthSignInError,\n            message: state === null ? flowCancelledMessage : validationFailedMessage,\n            recoverySuggestion: state === null ? undefined : validationRecoverySuggestion,\n        });\n    }\n    return validatedState;\n};\n\nexport { flowCancelledMessage, validateState, validationFailedMessage, validationRecoverySuggestion };\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,UAAU,QAAQ,kBAAkB;;AAE7C;AACA;AACA,MAAMC,oBAAoB,GAAG,yCAAyC;AACtE,MAAMC,uBAAuB,GAAG,+CAA+C;AAC/E,MAAMC,4BAA4B,GAAG,4CAA4C;AACjF,MAAMC,aAAa;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,KAAK,EAAK;IACnC,MAAMC,UAAU,SAASR,UAAU,CAACS,cAAc,CAAC,CAAC;IACpD;IACA,MAAMC,cAAc,GAAGH,KAAK,KAAKC,UAAU,GAAGA,UAAU,GAAGG,SAAS;IACpE,IAAI,CAACD,cAAc,EAAE;MACjB,MAAM,IAAIZ,SAAS,CAAC;QAChBc,IAAI,EAAEb,cAAc,CAACc,gBAAgB;QACrCC,OAAO,EAAEP,KAAK,KAAK,IAAI,GAAGN,oBAAoB,GAAGC,uBAAuB;QACxEa,kBAAkB,EAAER,KAAK,KAAK,IAAI,GAAGI,SAAS,GAAGR;MACrD,CAAC,CAAC;IACN;IACA,OAAOO,cAAc;EACzB,CAAC;EAAA,gBAZKN,aAAaA,CAAAY,EAAA;IAAA,OAAAX,IAAA,CAAAY,KAAA,OAAAC,SAAA;EAAA;AAAA,GAYlB;AAED,SAASjB,oBAAoB,EAAEG,aAAa,EAAEF,uBAAuB,EAAEC,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}