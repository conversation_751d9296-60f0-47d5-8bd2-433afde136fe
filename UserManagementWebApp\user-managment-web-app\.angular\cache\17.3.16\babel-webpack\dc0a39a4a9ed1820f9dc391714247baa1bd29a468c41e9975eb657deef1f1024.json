{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns a canonical query string.\n *\n * @param searchParams `searchParams` from the request url.\n * @returns URL-encoded query string parameters, separated by ampersands (&). Percent-encode reserved characters,\n * including the space character. Encode names and values separately. If there are empty parameters, append the equals\n * sign to the parameter name before encoding. After encoding, sort the parameters alphabetically by key name. If there\n * is no query string, use an empty string (\"\").\n *\n * @internal\n */\nconst getCanonicalQueryString = searchParams => Array.from(searchParams).sort(([keyA, valA], [keyB, valB]) => {\n  if (keyA === keyB) {\n    return valA < valB ? -1 : 1;\n  }\n  return keyA < keyB ? -1 : 1;\n}).map(([key, val]) => `${escapeUri(key)}=${escapeUri(val)}`).join('&');\nconst escapeUri = uri => encodeURIComponent(uri).replace(/[!'()*]/g, hexEncode);\nconst hexEncode = c => `%${c.charCodeAt(0).toString(16).toUpperCase()}`;\nexport { getCanonicalQueryString };", "map": {"version": 3, "names": ["getCanonicalQueryString", "searchParams", "Array", "from", "sort", "keyA", "valA", "keyB", "valB", "map", "key", "val", "<PERSON><PERSON><PERSON>", "join", "uri", "encodeURIComponent", "replace", "hexEncode", "c", "charCodeAt", "toString", "toUpperCase"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/signer/signatureV4/utils/getCanonicalQueryString.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns a canonical query string.\n *\n * @param searchParams `searchParams` from the request url.\n * @returns URL-encoded query string parameters, separated by ampersands (&). Percent-encode reserved characters,\n * including the space character. Encode names and values separately. If there are empty parameters, append the equals\n * sign to the parameter name before encoding. After encoding, sort the parameters alphabetically by key name. If there\n * is no query string, use an empty string (\"\").\n *\n * @internal\n */\nconst getCanonicalQueryString = (searchParams) => Array.from(searchParams)\n    .sort(([keyA, valA], [keyB, valB]) => {\n    if (keyA === keyB) {\n        return valA < valB ? -1 : 1;\n    }\n    return keyA < keyB ? -1 : 1;\n})\n    .map(([key, val]) => `${escapeUri(key)}=${escapeUri(val)}`)\n    .join('&');\nconst escapeUri = (uri) => encodeURIComponent(uri).replace(/[!'()*]/g, hexEncode);\nconst hexEncode = (c) => `%${c.charCodeAt(0).toString(16).toUpperCase()}`;\n\nexport { getCanonicalQueryString };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,uBAAuB,GAAIC,YAAY,IAAKC,KAAK,CAACC,IAAI,CAACF,YAAY,CAAC,CACrEG,IAAI,CAAC,CAAC,CAACC,IAAI,EAAEC,IAAI,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,CAAC,KAAK;EACtC,IAAIH,IAAI,KAAKE,IAAI,EAAE;IACf,OAAOD,IAAI,GAAGE,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;EAC/B;EACA,OAAOH,IAAI,GAAGE,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;AAC/B,CAAC,CAAC,CACGE,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,GAAG,CAAC,KAAK,GAAGC,SAAS,CAACF,GAAG,CAAC,IAAIE,SAAS,CAACD,GAAG,CAAC,EAAE,CAAC,CAC1DE,IAAI,CAAC,GAAG,CAAC;AACd,MAAMD,SAAS,GAAIE,GAAG,IAAKC,kBAAkB,CAACD,GAAG,CAAC,CAACE,OAAO,CAAC,UAAU,EAAEC,SAAS,CAAC;AACjF,MAAMA,SAAS,GAAIC,CAAC,IAAK,IAAIA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE;AAEzE,SAASrB,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}