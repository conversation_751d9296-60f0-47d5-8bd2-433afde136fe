{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { assertOAuthConfig } from '@aws-amplify/core/internals/utils';\nimport { openAuthSession } from '../../../../utils/openAuthSession.mjs';\nimport { getRedirectUrl } from './getRedirectUrl.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst oAuthSignOutRedirect = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (authConfig, preferPrivateSession = false, redirectUrl) {\n    assertOAuthConfig(authConfig);\n    const {\n      loginWith,\n      userPoolClientId\n    } = authConfig;\n    const {\n      domain,\n      redirectSignOut\n    } = loginWith.oauth;\n    const signoutUri = getRedirectUrl(redirectSignOut, redirectUrl);\n    const oAuthLogoutEndpoint = `https://${domain}/logout?${Object.entries({\n      client_id: userPoolClientId,\n      logout_uri: encodeURIComponent(signoutUri)\n    }).map(([k, v]) => `${k}=${v}`).join('&')}`;\n    return openAuthSession(oAuthLogoutEndpoint);\n  });\n  return function oAuthSignOutRedirect(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { oAuthSignOutRedirect };", "map": {"version": 3, "names": ["assertOAuthConfig", "openAuthSession", "getRedirectUrl", "oAuthSignOutRedirect", "_ref", "_asyncToGenerator", "authConfig", "preferPrivateSession", "redirectUrl", "loginWith", "userPoolClientId", "domain", "redirectSignOut", "o<PERSON>h", "signoutUri", "oAuthLogoutEndpoint", "Object", "entries", "client_id", "logout_uri", "encodeURIComponent", "map", "k", "v", "join", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/oauth/oAuthSignOutRedirect.mjs"], "sourcesContent": ["import { assertOAuthConfig } from '@aws-amplify/core/internals/utils';\nimport { openAuthSession } from '../../../../utils/openAuthSession.mjs';\nimport { getRedirectUrl } from './getRedirectUrl.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst oAuthSignOutRedirect = async (authConfig, preferPrivateSession = false, redirectUrl) => {\n    assertOAuthConfig(authConfig);\n    const { loginWith, userPoolClientId } = authConfig;\n    const { domain, redirectSignOut } = loginWith.oauth;\n    const signoutUri = getRedirectUrl(redirectSignOut, redirectUrl);\n    const oAuthLogoutEndpoint = `https://${domain}/logout?${Object.entries({\n        client_id: userPoolClientId,\n        logout_uri: encodeURIComponent(signoutUri),\n    })\n        .map(([k, v]) => `${k}=${v}`)\n        .join('&')}`;\n    return openAuthSession(oAuthLogoutEndpoint);\n};\n\nexport { oAuthSignOutRedirect };\n"], "mappings": ";AAAA,SAASA,iBAAiB,QAAQ,mCAAmC;AACrE,SAASC,eAAe,QAAQ,uCAAuC;AACvE,SAASC,cAAc,QAAQ,sBAAsB;;AAErD;AACA;AACA,MAAMC,oBAAoB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,UAAU,EAAEC,oBAAoB,GAAG,KAAK,EAAEC,WAAW,EAAK;IAC1FR,iBAAiB,CAACM,UAAU,CAAC;IAC7B,MAAM;MAAEG,SAAS;MAAEC;IAAiB,CAAC,GAAGJ,UAAU;IAClD,MAAM;MAAEK,MAAM;MAAEC;IAAgB,CAAC,GAAGH,SAAS,CAACI,KAAK;IACnD,MAAMC,UAAU,GAAGZ,cAAc,CAACU,eAAe,EAAEJ,WAAW,CAAC;IAC/D,MAAMO,mBAAmB,GAAG,WAAWJ,MAAM,WAAWK,MAAM,CAACC,OAAO,CAAC;MACnEC,SAAS,EAAER,gBAAgB;MAC3BS,UAAU,EAAEC,kBAAkB,CAACN,UAAU;IAC7C,CAAC,CAAC,CACGO,GAAG,CAAC,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,KAAK,GAAGD,CAAC,IAAIC,CAAC,EAAE,CAAC,CAC5BC,IAAI,CAAC,GAAG,CAAC,EAAE;IAChB,OAAOvB,eAAe,CAACc,mBAAmB,CAAC;EAC/C,CAAC;EAAA,gBAZKZ,oBAAoBA,CAAAsB,EAAA;IAAA,OAAArB,IAAA,CAAAsB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAYzB;AAED,SAASxB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}