{"ast": null, "code": "import { translate, DefaultTexts } from '../../i18n/translations.mjs';\nimport { defaultTexts } from '../../i18n/dictionaries/index.mjs';\n\n/**\n * ConfirmSignIn\n */\nconst getChallengeText = challengeName => {\n  switch (challengeName) {\n    case 'EMAIL_OTP':\n      return translate(DefaultTexts.CONFIRM_EMAIL);\n    case 'SMS_MFA':\n      return translate(DefaultTexts.CONFIRM_SMS);\n    case 'SOFTWARE_TOKEN_MFA':\n      return translate(DefaultTexts.CONFIRM_TOTP);\n    default:\n      return translate(DefaultTexts.CONFIRM_MFA_DEFAULT);\n  }\n};\n/**\n * ConfirmSignUp\n */\nconst getDeliveryMessageText = codeDeliveryDetails => {\n  const {\n    DeliveryMedium,\n    Destination\n  } = codeDeliveryDetails ?? {};\n  const isEmailMessage = DeliveryMedium === 'EMAIL';\n  const isTextMessage = DeliveryMedium === 'SMS';\n  const arrivalMessage = translate(DefaultTexts.CODE_ARRIVAL);\n  if (!(isEmailMessage || isTextMessage)) {\n    return `${translate(DefaultTexts.CODE_SENT)}. ${arrivalMessage}.`;\n  }\n  const instructionMessage = isEmailMessage ? translate(DefaultTexts.CODE_EMAILED) : translate(DefaultTexts.CODE_TEXTED);\n  return `${instructionMessage} ${Destination}. ${arrivalMessage}.`;\n};\nconst getDeliveryMethodText = codeDeliveryDetails => {\n  const {\n    DeliveryMedium\n  } = codeDeliveryDetails ?? {};\n  const isEmailMessage = DeliveryMedium === 'EMAIL';\n  const isTextMessage = DeliveryMedium === 'SMS';\n  if (!isEmailMessage && isTextMessage) {\n    return translate(DefaultTexts.WE_SENT_CODE);\n  }\n  return isEmailMessage ? translate(DefaultTexts.WE_EMAILED) : translate(DefaultTexts.WE_TEXTED);\n};\n/**\n * FederatedSignIn\n */\nconst providerNameMap = {\n  amazon: 'Amazon',\n  apple: 'Apple',\n  facebook: 'Facebook',\n  google: 'Google'\n};\nconst getSignInWithFederationText = (route, provider) => {\n  const isSignIn = route === 'signIn';\n  return translate(`Sign ${isSignIn ? 'In' : 'Up'} with ${providerNameMap[provider]}`);\n};\n/**\n * SelectMfaType\n */\nconst getSelectMfaTypeByChallengeName = challengeName => {\n  if (challengeName === 'MFA_SETUP') {\n    return translate(DefaultTexts.MFA_SETUP_SELECTION);\n  }\n  return translate(DefaultTexts.MFA_SELECTION);\n};\nconst getMfaTypeLabelByValue = mfaType => {\n  switch (mfaType) {\n    case 'EMAIL':\n      return translate(defaultTexts.EMAIL_OTP);\n    case 'SMS':\n      return translate(defaultTexts.SMS_MFA);\n    case 'TOTP':\n      return translate(defaultTexts.SOFTWARE_TOKEN_MFA);\n    default:\n      return translate(mfaType);\n  }\n};\nconst authenticatorTextUtil = {\n  /** Shared */\n  getBackToSignInText: () => translate(DefaultTexts.BACK_SIGN_IN),\n  getChangePasswordText: () => translate(DefaultTexts.CHANGE_PASSWORD),\n  getChangingText: () => translate(DefaultTexts.CHANGING_PASSWORD),\n  getConfirmText: () => translate(DefaultTexts.CONFIRM),\n  getConfirmingText: () => translate(DefaultTexts.CONFIRMING),\n  getCopyText: () => translate(DefaultTexts.UPPERCASE_COPY),\n  getHidePasswordText: () => translate(DefaultTexts.HIDE_PASSWORD),\n  getLoadingText: () => translate(DefaultTexts.LOADING),\n  getOrText: () => translate(DefaultTexts.OR),\n  getResendCodeText: () => translate(DefaultTexts.RESEND_CODE),\n  getSendCodeText: () => translate(DefaultTexts.SEND_CODE),\n  getSendingText: () => translate(DefaultTexts.SENDING),\n  getShowPasswordText: () => translate(DefaultTexts.SHOW_PASSWORD),\n  getSubmitText: () => translate(DefaultTexts.SUBMIT),\n  getSubmittingText: () => translate(DefaultTexts.SUBMITTING),\n  /** SignInSignUpTabs */\n  getSignInTabText: () => translate(DefaultTexts.SIGN_IN_TAB),\n  getSignUpTabText: () => translate(DefaultTexts.CREATE_ACCOUNT),\n  /** SignIn */\n  getForgotPasswordText: shortVersion => translate(shortVersion ? DefaultTexts.FORGOT_PASSWORD : DefaultTexts.FORGOT_YOUR_PASSWORD),\n  getSigningInText: () => translate(DefaultTexts.SIGNING_IN_BUTTON),\n  getSignInText: () => translate(DefaultTexts.SIGN_IN_BUTTON),\n  /** SignUp */\n  getCreatingAccountText: () => translate(DefaultTexts.CREATING_ACCOUNT),\n  getCreateAccountText: () => translate(DefaultTexts.CREATE_ACCOUNT),\n  /** ConfirmSignUp */\n  getDeliveryMessageText,\n  getDeliveryMethodText,\n  /** ConfirmSignIn */\n  getChallengeText,\n  /** ForgotPassword */\n  getResetYourPasswordText: () => translate(DefaultTexts.RESET_PASSWORD),\n  /** SetupEmail */\n  getSetupEmailText: () => translate(DefaultTexts.SETUP_EMAIL),\n  /** SetupTotp */\n  getSetupTotpText: () => translate(DefaultTexts.SETUP_TOTP),\n  // TODO: add defaultText for below\n  getSetupTotpInstructionsText: () => translate('Copy and paste the secret key below into an authenticator app and then enter the code in the text field below.'),\n  // TODO: add defaultText for \"COPIED\"\n  getCopiedText: () => translate('COPIED'),\n  /** FederatedSignIn */\n  getSignInWithFederationText,\n  /** SelectMfaType */\n  getMfaTypeLabelByValue,\n  getSelectMfaTypeByChallengeName,\n  getSelectMfaTypeText: () => translate(DefaultTexts.SELECT_MFA_TYPE),\n  /** VerifyUser */\n  getSkipText: () => translate(DefaultTexts.SKIP),\n  getVerifyText: () => translate(DefaultTexts.VERIFY),\n  getVerifyContactText: () => translate(DefaultTexts.VERIFY_CONTACT),\n  getAccountRecoveryInfoText: () => translate(DefaultTexts.VERIFY_HEADING),\n  /** Validations */\n  // TODO: add defaultText\n  getInvalidEmailText: () => translate('Please enter a valid email'),\n  // TODO: add defaultText\n  getRequiredFieldText: () => translate('This field is required')\n}; // using `as const` so that keys are strongly typed\n\nexport { authenticatorTextUtil };", "map": {"version": 3, "names": ["translate", "DefaultTexts", "defaultTexts", "getChallengeText", "challenge<PERSON>ame", "CONFIRM_EMAIL", "CONFIRM_SMS", "CONFIRM_TOTP", "CONFIRM_MFA_DEFAULT", "getDeliveryMessageText", "codeDeliveryDetails", "DeliveryMedium", "Destination", "isEmailMessage", "isTextMessage", "arrivalMessage", "CODE_ARRIVAL", "CODE_SENT", "instructionMessage", "CODE_EMAILED", "CODE_TEXTED", "getDeliveryMethodText", "WE_SENT_CODE", "WE_EMAILED", "WE_TEXTED", "providerNameMap", "amazon", "apple", "facebook", "google", "getSignInWithFederationText", "route", "provider", "isSignIn", "getSelectMfaTypeByChallengeName", "MFA_SETUP_SELECTION", "MFA_SELECTION", "getMfaTypeLabelByValue", "mfaType", "EMAIL_OTP", "SMS_MFA", "SOFTWARE_TOKEN_MFA", "authenticatorTextUtil", "getBackToSignInText", "BACK_SIGN_IN", "getChangePasswordText", "CHANGE_PASSWORD", "getChangingText", "CHANGING_PASSWORD", "getConfirmText", "CONFIRM", "getConfirmingText", "CONFIRMING", "getCopyText", "UPPERCASE_COPY", "getHidePasswordText", "HIDE_PASSWORD", "getLoadingText", "LOADING", "getOrText", "OR", "getResendCodeText", "RESEND_CODE", "getSendCodeText", "SEND_CODE", "getSendingText", "SENDING", "getShowPasswordText", "SHOW_PASSWORD", "getSubmitText", "SUBMIT", "getSubmittingText", "SUBMITTING", "getSignInTabText", "SIGN_IN_TAB", "getSignUpTabText", "CREATE_ACCOUNT", "getForgotPasswordText", "shortVersion", "FORGOT_PASSWORD", "FORGOT_YOUR_PASSWORD", "getSigningInText", "SIGNING_IN_BUTTON", "getSignInText", "SIGN_IN_BUTTON", "getCreatingAccountText", "CREATING_ACCOUNT", "getCreateAccountText", "getResetYourPasswordText", "RESET_PASSWORD", "getSetupEmailText", "SETUP_EMAIL", "getSetupTotpText", "SETUP_TOTP", "getSetupTotpInstructionsText", "getCopiedText", "getSelectMfaTypeText", "SELECT_MFA_TYPE", "getSkipText", "SKIP", "getVerifyText", "VERIFY", "getVerifyContactText", "VERIFY_CONTACT", "getAccountRecoveryInfoText", "VERIFY_HEADING", "getInvalidEmailText", "getRequiredFieldText"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/helpers/authenticator/textUtil.mjs"], "sourcesContent": ["import { translate, DefaultTexts } from '../../i18n/translations.mjs';\nimport { defaultTexts } from '../../i18n/dictionaries/index.mjs';\n\n/**\n * ConfirmSignIn\n */\nconst getChallengeText = (challengeName) => {\n    switch (challengeName) {\n        case 'EMAIL_OTP':\n            return translate(DefaultTexts.CONFIRM_EMAIL);\n        case 'SMS_MFA':\n            return translate(DefaultTexts.CONFIRM_SMS);\n        case 'SOFTWARE_TOKEN_MFA':\n            return translate(DefaultTexts.CONFIRM_TOTP);\n        default:\n            return translate(DefaultTexts.CONFIRM_MFA_DEFAULT);\n    }\n};\n/**\n * ConfirmSignUp\n */\nconst getDeliveryMessageText = (codeDeliveryDetails) => {\n    const { DeliveryMedium, Destination } = codeDeliveryDetails ?? {};\n    const isEmailMessage = DeliveryMedium === 'EMAIL';\n    const isTextMessage = DeliveryMedium === 'SMS';\n    const arrivalMessage = translate(DefaultTexts.CODE_ARRIVAL);\n    if (!(isEmailMessage || isTextMessage)) {\n        return `${translate(DefaultTexts.CODE_SENT)}. ${arrivalMessage}.`;\n    }\n    const instructionMessage = isEmailMessage\n        ? translate(DefaultTexts.CODE_EMAILED)\n        : translate(DefaultTexts.CODE_TEXTED);\n    return `${instructionMessage} ${Destination}. ${arrivalMessage}.`;\n};\nconst getDeliveryMethodText = (codeDeliveryDetails) => {\n    const { DeliveryMedium } = codeDeliveryDetails ?? {};\n    const isEmailMessage = DeliveryMedium === 'EMAIL';\n    const isTextMessage = DeliveryMedium === 'SMS';\n    if (!isEmailMessage && isTextMessage) {\n        return translate(DefaultTexts.WE_SENT_CODE);\n    }\n    return isEmailMessage\n        ? translate(DefaultTexts.WE_EMAILED)\n        : translate(DefaultTexts.WE_TEXTED);\n};\n/**\n * FederatedSignIn\n */\nconst providerNameMap = {\n    amazon: 'Amazon',\n    apple: 'Apple',\n    facebook: 'Facebook',\n    google: 'Google',\n};\nconst getSignInWithFederationText = (route, provider) => {\n    const isSignIn = route === 'signIn';\n    return translate(`Sign ${isSignIn ? 'In' : 'Up'} with ${providerNameMap[provider]}`);\n};\n/**\n * SelectMfaType\n */\nconst getSelectMfaTypeByChallengeName = (challengeName) => {\n    if (challengeName === 'MFA_SETUP') {\n        return translate(DefaultTexts.MFA_SETUP_SELECTION);\n    }\n    return translate(DefaultTexts.MFA_SELECTION);\n};\nconst getMfaTypeLabelByValue = (mfaType) => {\n    switch (mfaType) {\n        case 'EMAIL':\n            return translate(defaultTexts.EMAIL_OTP);\n        case 'SMS':\n            return translate(defaultTexts.SMS_MFA);\n        case 'TOTP':\n            return translate(defaultTexts.SOFTWARE_TOKEN_MFA);\n        default:\n            return translate(mfaType);\n    }\n};\nconst authenticatorTextUtil = {\n    /** Shared */\n    getBackToSignInText: () => translate(DefaultTexts.BACK_SIGN_IN),\n    getChangePasswordText: () => translate(DefaultTexts.CHANGE_PASSWORD),\n    getChangingText: () => translate(DefaultTexts.CHANGING_PASSWORD),\n    getConfirmText: () => translate(DefaultTexts.CONFIRM),\n    getConfirmingText: () => translate(DefaultTexts.CONFIRMING),\n    getCopyText: () => translate(DefaultTexts.UPPERCASE_COPY),\n    getHidePasswordText: () => translate(DefaultTexts.HIDE_PASSWORD),\n    getLoadingText: () => translate(DefaultTexts.LOADING),\n    getOrText: () => translate(DefaultTexts.OR),\n    getResendCodeText: () => translate(DefaultTexts.RESEND_CODE),\n    getSendCodeText: () => translate(DefaultTexts.SEND_CODE),\n    getSendingText: () => translate(DefaultTexts.SENDING),\n    getShowPasswordText: () => translate(DefaultTexts.SHOW_PASSWORD),\n    getSubmitText: () => translate(DefaultTexts.SUBMIT),\n    getSubmittingText: () => translate(DefaultTexts.SUBMITTING),\n    /** SignInSignUpTabs */\n    getSignInTabText: () => translate(DefaultTexts.SIGN_IN_TAB),\n    getSignUpTabText: () => translate(DefaultTexts.CREATE_ACCOUNT),\n    /** SignIn */\n    getForgotPasswordText: (shortVersion) => translate(shortVersion\n        ? DefaultTexts.FORGOT_PASSWORD\n        : DefaultTexts.FORGOT_YOUR_PASSWORD),\n    getSigningInText: () => translate(DefaultTexts.SIGNING_IN_BUTTON),\n    getSignInText: () => translate(DefaultTexts.SIGN_IN_BUTTON),\n    /** SignUp */\n    getCreatingAccountText: () => translate(DefaultTexts.CREATING_ACCOUNT),\n    getCreateAccountText: () => translate(DefaultTexts.CREATE_ACCOUNT),\n    /** ConfirmSignUp */\n    getDeliveryMessageText,\n    getDeliveryMethodText,\n    /** ConfirmSignIn */\n    getChallengeText,\n    /** ForgotPassword */\n    getResetYourPasswordText: () => translate(DefaultTexts.RESET_PASSWORD),\n    /** SetupEmail */\n    getSetupEmailText: () => translate(DefaultTexts.SETUP_EMAIL),\n    /** SetupTotp */\n    getSetupTotpText: () => translate(DefaultTexts.SETUP_TOTP),\n    // TODO: add defaultText for below\n    getSetupTotpInstructionsText: () => translate('Copy and paste the secret key below into an authenticator app and then enter the code in the text field below.'),\n    // TODO: add defaultText for \"COPIED\"\n    getCopiedText: () => translate('COPIED'),\n    /** FederatedSignIn */\n    getSignInWithFederationText,\n    /** SelectMfaType */\n    getMfaTypeLabelByValue,\n    getSelectMfaTypeByChallengeName,\n    getSelectMfaTypeText: () => translate(DefaultTexts.SELECT_MFA_TYPE),\n    /** VerifyUser */\n    getSkipText: () => translate(DefaultTexts.SKIP),\n    getVerifyText: () => translate(DefaultTexts.VERIFY),\n    getVerifyContactText: () => translate(DefaultTexts.VERIFY_CONTACT),\n    getAccountRecoveryInfoText: () => translate(DefaultTexts.VERIFY_HEADING),\n    /** Validations */\n    // TODO: add defaultText\n    getInvalidEmailText: () => translate('Please enter a valid email'),\n    // TODO: add defaultText\n    getRequiredFieldText: () => translate('This field is required'),\n}; // using `as const` so that keys are strongly typed\n\nexport { authenticatorTextUtil };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,YAAY,QAAQ,6BAA6B;AACrE,SAASC,YAAY,QAAQ,mCAAmC;;AAEhE;AACA;AACA;AACA,MAAMC,gBAAgB,GAAIC,aAAa,IAAK;EACxC,QAAQA,aAAa;IACjB,KAAK,WAAW;MACZ,OAAOJ,SAAS,CAACC,YAAY,CAACI,aAAa,CAAC;IAChD,KAAK,SAAS;MACV,OAAOL,SAAS,CAACC,YAAY,CAACK,WAAW,CAAC;IAC9C,KAAK,oBAAoB;MACrB,OAAON,SAAS,CAACC,YAAY,CAACM,YAAY,CAAC;IAC/C;MACI,OAAOP,SAAS,CAACC,YAAY,CAACO,mBAAmB,CAAC;EAC1D;AACJ,CAAC;AACD;AACA;AACA;AACA,MAAMC,sBAAsB,GAAIC,mBAAmB,IAAK;EACpD,MAAM;IAAEC,cAAc;IAAEC;EAAY,CAAC,GAAGF,mBAAmB,IAAI,CAAC,CAAC;EACjE,MAAMG,cAAc,GAAGF,cAAc,KAAK,OAAO;EACjD,MAAMG,aAAa,GAAGH,cAAc,KAAK,KAAK;EAC9C,MAAMI,cAAc,GAAGf,SAAS,CAACC,YAAY,CAACe,YAAY,CAAC;EAC3D,IAAI,EAAEH,cAAc,IAAIC,aAAa,CAAC,EAAE;IACpC,OAAO,GAAGd,SAAS,CAACC,YAAY,CAACgB,SAAS,CAAC,KAAKF,cAAc,GAAG;EACrE;EACA,MAAMG,kBAAkB,GAAGL,cAAc,GACnCb,SAAS,CAACC,YAAY,CAACkB,YAAY,CAAC,GACpCnB,SAAS,CAACC,YAAY,CAACmB,WAAW,CAAC;EACzC,OAAO,GAAGF,kBAAkB,IAAIN,WAAW,KAAKG,cAAc,GAAG;AACrE,CAAC;AACD,MAAMM,qBAAqB,GAAIX,mBAAmB,IAAK;EACnD,MAAM;IAAEC;EAAe,CAAC,GAAGD,mBAAmB,IAAI,CAAC,CAAC;EACpD,MAAMG,cAAc,GAAGF,cAAc,KAAK,OAAO;EACjD,MAAMG,aAAa,GAAGH,cAAc,KAAK,KAAK;EAC9C,IAAI,CAACE,cAAc,IAAIC,aAAa,EAAE;IAClC,OAAOd,SAAS,CAACC,YAAY,CAACqB,YAAY,CAAC;EAC/C;EACA,OAAOT,cAAc,GACfb,SAAS,CAACC,YAAY,CAACsB,UAAU,CAAC,GAClCvB,SAAS,CAACC,YAAY,CAACuB,SAAS,CAAC;AAC3C,CAAC;AACD;AACA;AACA;AACA,MAAMC,eAAe,GAAG;EACpBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE;AACZ,CAAC;AACD,MAAMC,2BAA2B,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;EACrD,MAAMC,QAAQ,GAAGF,KAAK,KAAK,QAAQ;EACnC,OAAO/B,SAAS,CAAC,QAAQiC,QAAQ,GAAG,IAAI,GAAG,IAAI,SAASR,eAAe,CAACO,QAAQ,CAAC,EAAE,CAAC;AACxF,CAAC;AACD;AACA;AACA;AACA,MAAME,+BAA+B,GAAI9B,aAAa,IAAK;EACvD,IAAIA,aAAa,KAAK,WAAW,EAAE;IAC/B,OAAOJ,SAAS,CAACC,YAAY,CAACkC,mBAAmB,CAAC;EACtD;EACA,OAAOnC,SAAS,CAACC,YAAY,CAACmC,aAAa,CAAC;AAChD,CAAC;AACD,MAAMC,sBAAsB,GAAIC,OAAO,IAAK;EACxC,QAAQA,OAAO;IACX,KAAK,OAAO;MACR,OAAOtC,SAAS,CAACE,YAAY,CAACqC,SAAS,CAAC;IAC5C,KAAK,KAAK;MACN,OAAOvC,SAAS,CAACE,YAAY,CAACsC,OAAO,CAAC;IAC1C,KAAK,MAAM;MACP,OAAOxC,SAAS,CAACE,YAAY,CAACuC,kBAAkB,CAAC;IACrD;MACI,OAAOzC,SAAS,CAACsC,OAAO,CAAC;EACjC;AACJ,CAAC;AACD,MAAMI,qBAAqB,GAAG;EAC1B;EACAC,mBAAmB,EAAEA,CAAA,KAAM3C,SAAS,CAACC,YAAY,CAAC2C,YAAY,CAAC;EAC/DC,qBAAqB,EAAEA,CAAA,KAAM7C,SAAS,CAACC,YAAY,CAAC6C,eAAe,CAAC;EACpEC,eAAe,EAAEA,CAAA,KAAM/C,SAAS,CAACC,YAAY,CAAC+C,iBAAiB,CAAC;EAChEC,cAAc,EAAEA,CAAA,KAAMjD,SAAS,CAACC,YAAY,CAACiD,OAAO,CAAC;EACrDC,iBAAiB,EAAEA,CAAA,KAAMnD,SAAS,CAACC,YAAY,CAACmD,UAAU,CAAC;EAC3DC,WAAW,EAAEA,CAAA,KAAMrD,SAAS,CAACC,YAAY,CAACqD,cAAc,CAAC;EACzDC,mBAAmB,EAAEA,CAAA,KAAMvD,SAAS,CAACC,YAAY,CAACuD,aAAa,CAAC;EAChEC,cAAc,EAAEA,CAAA,KAAMzD,SAAS,CAACC,YAAY,CAACyD,OAAO,CAAC;EACrDC,SAAS,EAAEA,CAAA,KAAM3D,SAAS,CAACC,YAAY,CAAC2D,EAAE,CAAC;EAC3CC,iBAAiB,EAAEA,CAAA,KAAM7D,SAAS,CAACC,YAAY,CAAC6D,WAAW,CAAC;EAC5DC,eAAe,EAAEA,CAAA,KAAM/D,SAAS,CAACC,YAAY,CAAC+D,SAAS,CAAC;EACxDC,cAAc,EAAEA,CAAA,KAAMjE,SAAS,CAACC,YAAY,CAACiE,OAAO,CAAC;EACrDC,mBAAmB,EAAEA,CAAA,KAAMnE,SAAS,CAACC,YAAY,CAACmE,aAAa,CAAC;EAChEC,aAAa,EAAEA,CAAA,KAAMrE,SAAS,CAACC,YAAY,CAACqE,MAAM,CAAC;EACnDC,iBAAiB,EAAEA,CAAA,KAAMvE,SAAS,CAACC,YAAY,CAACuE,UAAU,CAAC;EAC3D;EACAC,gBAAgB,EAAEA,CAAA,KAAMzE,SAAS,CAACC,YAAY,CAACyE,WAAW,CAAC;EAC3DC,gBAAgB,EAAEA,CAAA,KAAM3E,SAAS,CAACC,YAAY,CAAC2E,cAAc,CAAC;EAC9D;EACAC,qBAAqB,EAAGC,YAAY,IAAK9E,SAAS,CAAC8E,YAAY,GACzD7E,YAAY,CAAC8E,eAAe,GAC5B9E,YAAY,CAAC+E,oBAAoB,CAAC;EACxCC,gBAAgB,EAAEA,CAAA,KAAMjF,SAAS,CAACC,YAAY,CAACiF,iBAAiB,CAAC;EACjEC,aAAa,EAAEA,CAAA,KAAMnF,SAAS,CAACC,YAAY,CAACmF,cAAc,CAAC;EAC3D;EACAC,sBAAsB,EAAEA,CAAA,KAAMrF,SAAS,CAACC,YAAY,CAACqF,gBAAgB,CAAC;EACtEC,oBAAoB,EAAEA,CAAA,KAAMvF,SAAS,CAACC,YAAY,CAAC2E,cAAc,CAAC;EAClE;EACAnE,sBAAsB;EACtBY,qBAAqB;EACrB;EACAlB,gBAAgB;EAChB;EACAqF,wBAAwB,EAAEA,CAAA,KAAMxF,SAAS,CAACC,YAAY,CAACwF,cAAc,CAAC;EACtE;EACAC,iBAAiB,EAAEA,CAAA,KAAM1F,SAAS,CAACC,YAAY,CAAC0F,WAAW,CAAC;EAC5D;EACAC,gBAAgB,EAAEA,CAAA,KAAM5F,SAAS,CAACC,YAAY,CAAC4F,UAAU,CAAC;EAC1D;EACAC,4BAA4B,EAAEA,CAAA,KAAM9F,SAAS,CAAC,gHAAgH,CAAC;EAC/J;EACA+F,aAAa,EAAEA,CAAA,KAAM/F,SAAS,CAAC,QAAQ,CAAC;EACxC;EACA8B,2BAA2B;EAC3B;EACAO,sBAAsB;EACtBH,+BAA+B;EAC/B8D,oBAAoB,EAAEA,CAAA,KAAMhG,SAAS,CAACC,YAAY,CAACgG,eAAe,CAAC;EACnE;EACAC,WAAW,EAAEA,CAAA,KAAMlG,SAAS,CAACC,YAAY,CAACkG,IAAI,CAAC;EAC/CC,aAAa,EAAEA,CAAA,KAAMpG,SAAS,CAACC,YAAY,CAACoG,MAAM,CAAC;EACnDC,oBAAoB,EAAEA,CAAA,KAAMtG,SAAS,CAACC,YAAY,CAACsG,cAAc,CAAC;EAClEC,0BAA0B,EAAEA,CAAA,KAAMxG,SAAS,CAACC,YAAY,CAACwG,cAAc,CAAC;EACxE;EACA;EACAC,mBAAmB,EAAEA,CAAA,KAAM1G,SAAS,CAAC,4BAA4B,CAAC;EAClE;EACA2G,oBAAoB,EAAEA,CAAA,KAAM3G,SAAS,CAAC,wBAAwB;AAClE,CAAC,CAAC,CAAC;;AAEH,SAAS0C,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}