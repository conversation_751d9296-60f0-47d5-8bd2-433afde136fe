{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./env.service\";\nexport class CompanyService {\n  setCompanyInformation(company) {\n    this.companyInformation.next(company);\n  }\n  getCompanyInformationFromCache() {\n    return this.companyInformation.value;\n  }\n  constructor(httpClient, envService) {\n    this.httpClient = httpClient;\n    this.envService = envService;\n    this.companyInformation = new BehaviorSubject({});\n    this.companyHandler = this.companyInformation.asObservable();\n  }\n  get companyBaseUrl() {\n    return `${this.envService.userManagementApiUrl}company`;\n  }\n  getCompany() {\n    const url = `${this.companyBaseUrl}/company`;\n    return this.httpClient.get(url).pipe(tap(companyInfo => this.setCompanyInformation(companyInfo)));\n  }\n  hasTenantFeatures(tenantFeature) {\n    console.log('company info', this.companyInformation?.value);\n    const company = this.companyInformation?.value;\n    return this.companyHasTenantFeatures(company, tenantFeature);\n  }\n  companyHasTenantFeatures(company, tenantFeature) {\n    console.log('company', company, company?.featureAssignment);\n    if (company?.featureAssignment) {\n      for (const feature of company.featureAssignment) {\n        if (feature) {\n          if (tenantFeature.toUpperCase() === feature.name.toUpperCase()) {\n            return true;\n          }\n        }\n      }\n    }\n    console.log('Doesn\\'t have feature', tenantFeature);\n    return false;\n  }\n  static {\n    this.ɵfac = function CompanyService_Factory(t) {\n      return new (t || CompanyService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.EnvService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CompanyService,\n      factory: CompanyService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "CompanyService", "setCompanyInformation", "company", "companyInformation", "next", "getCompanyInformationFromCache", "value", "constructor", "httpClient", "envService", "companyHandler", "asObservable", "companyBaseUrl", "userManagementApiUrl", "getCompany", "url", "get", "pipe", "companyInfo", "hasTenantFeatures", "tenantFeature", "console", "log", "companyHasTenantFeatures", "featureAssignment", "feature", "toUpperCase", "name", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "EnvService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\IntegrationPlatform\\IntegrationWebApp\\integration-web-app\\src\\app\\core\\services\\company.service .ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { EnvService } from './env.service';\r\nimport { BehaviorSubject } from 'rxjs';\r\nimport { Company } from '../models/company';\r\nimport { tap } from 'rxjs/operators';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class CompanyService {\r\n  private companyInformation = new BehaviorSubject<Company>({});\r\n  companyHandler = this.companyInformation.asObservable();\r\n\r\n  setCompanyInformation(company: Company) {\r\n    this.companyInformation.next(company);\r\n  }\r\n\r\n  getCompanyInformationFromCache() {\r\n    return this.companyInformation.value;\r\n  }\r\n\r\n  constructor(\r\n    private httpClient: HttpClient,\r\n    public envService: EnvService) { }\r\n  get companyBaseUrl() { return `${this.envService.userManagementApiUrl}company`; }\r\n  getCompany() {\r\n    const url = `${this.companyBaseUrl}/company`;\r\n    return this.httpClient.get<Company>(url).pipe(\r\n      tap(companyInfo => this.setCompanyInformation(companyInfo)));\r\n  }\r\n  hasTenantFeatures(tenantFeature: string) {\r\n    console.log('company info', this.companyInformation?.value);\r\n    const company = this.companyInformation?.value;\r\n    return this.companyHasTenantFeatures(company, tenantFeature);\r\n  }\r\n  \r\n  companyHasTenantFeatures(company: Company, tenantFeature: string) {    \r\n    console.log('company', company, company?.featureAssignment);\r\n    if (company?.featureAssignment) {\r\n      for (const feature of company.featureAssignment) {\r\n        if (feature) {\r\n          if (tenantFeature.toUpperCase() === feature.name.toUpperCase()) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    console.log('Doesn\\'t have feature', tenantFeature);\r\n    return false;\r\n  }\r\n}\r\n\r\n"], "mappings": "AAGA,SAASA,eAAe,QAAQ,MAAM;AAEtC,SAASC,GAAG,QAAQ,gBAAgB;;;;AAKpC,OAAM,MAAOC,cAAc;EAIzBC,qBAAqBA,CAACC,OAAgB;IACpC,IAAI,CAACC,kBAAkB,CAACC,IAAI,CAACF,OAAO,CAAC;EACvC;EAEAG,8BAA8BA,CAAA;IAC5B,OAAO,IAAI,CAACF,kBAAkB,CAACG,KAAK;EACtC;EAEAC,YACUC,UAAsB,EACvBC,UAAsB;IADrB,KAAAD,UAAU,GAAVA,UAAU;IACX,KAAAC,UAAU,GAAVA,UAAU;IAbX,KAAAN,kBAAkB,GAAG,IAAIL,eAAe,CAAU,EAAE,CAAC;IAC7D,KAAAY,cAAc,GAAG,IAAI,CAACP,kBAAkB,CAACQ,YAAY,EAAE;EAYpB;EACnC,IAAIC,cAAcA,CAAA;IAAK,OAAO,GAAG,IAAI,CAACH,UAAU,CAACI,oBAAoB,SAAS;EAAE;EAChFC,UAAUA,CAAA;IACR,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACH,cAAc,UAAU;IAC5C,OAAO,IAAI,CAACJ,UAAU,CAACQ,GAAG,CAAUD,GAAG,CAAC,CAACE,IAAI,CAC3ClB,GAAG,CAACmB,WAAW,IAAI,IAAI,CAACjB,qBAAqB,CAACiB,WAAW,CAAC,CAAC,CAAC;EAChE;EACAC,iBAAiBA,CAACC,aAAqB;IACrCC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACnB,kBAAkB,EAAEG,KAAK,CAAC;IAC3D,MAAMJ,OAAO,GAAG,IAAI,CAACC,kBAAkB,EAAEG,KAAK;IAC9C,OAAO,IAAI,CAACiB,wBAAwB,CAACrB,OAAO,EAAEkB,aAAa,CAAC;EAC9D;EAEAG,wBAAwBA,CAACrB,OAAgB,EAAEkB,aAAqB;IAC9DC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEpB,OAAO,EAAEA,OAAO,EAAEsB,iBAAiB,CAAC;IAC3D,IAAItB,OAAO,EAAEsB,iBAAiB,EAAE;MAC9B,KAAK,MAAMC,OAAO,IAAIvB,OAAO,CAACsB,iBAAiB,EAAE;QAC/C,IAAIC,OAAO,EAAE;UACX,IAAIL,aAAa,CAACM,WAAW,EAAE,KAAKD,OAAO,CAACE,IAAI,CAACD,WAAW,EAAE,EAAE;YAC9D,OAAO,IAAI;UACb;QACF;MACF;IACF;IACAL,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEF,aAAa,CAAC;IACnD,OAAO,KAAK;EACd;;;uBAxCWpB,cAAc,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAdjC,cAAc;MAAAkC,OAAA,EAAdlC,cAAc,CAAAmC,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}