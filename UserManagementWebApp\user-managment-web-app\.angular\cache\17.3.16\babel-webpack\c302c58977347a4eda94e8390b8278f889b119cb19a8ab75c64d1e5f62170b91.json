{"ast": null, "code": "import { propsToString } from './utils.mjs';\nimport '@aws-amplify/core/internals/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { isFunction, splitObject } from '../../utils/utils.mjs';\nfunction addVars(selector, vars) {\n  if (!vars) return '';\n  return `${selector} { ${Object.entries(vars).map(([key, value]) => {\n    return `--${key}:${value}; `;\n  }).join(' ')}}\\n`;\n}\nfunction recursiveComponentCSS(baseSelector, theme) {\n  let str = '';\n  const {\n    _modifiers = {},\n    _element = {},\n    _vars,\n    ...props\n  } = theme;\n  // if there are no props, skip\n  if (Object.keys(props).length) {\n    // separate psuedo/attribute selectors\n    const [selectors, other] = splitObject(props, key => key.startsWith(':') || key.startsWith('['));\n    Object.entries(selectors).forEach(([selector, value]) => {\n      // need to remove nested things like vars and elements\n      const {\n        _modifiers = {},\n        _element = {},\n        _vars,\n        ...props\n      } = value;\n      str += `${baseSelector}${selector} { ${propsToString(props)} }\\n`;\n      str += addVars(`${baseSelector}${selector}`, _vars);\n    });\n    str += `${baseSelector} { ${propsToString(other)} }\\n`;\n  }\n  str += addVars(baseSelector, _vars);\n  Object.entries(_modifiers).forEach(([key, value]) => {\n    if (value && Object.keys(value).length) {\n      str += recursiveComponentCSS(`${baseSelector}--${key}`, value);\n    }\n  });\n  Object.entries(_element).forEach(([key, value]) => {\n    if (value && Object.keys(value).length) {\n      str += recursiveComponentCSS(`${baseSelector}__${key}`, value);\n    }\n  });\n  return str;\n}\n/**\n * This will take a component theme and create the appropriate CSS for it.\n *\n */\nfunction createComponentCSS({\n  theme,\n  components\n}) {\n  let cssText = '';\n  const {\n    tokens,\n    name: themeName,\n    breakpoints\n  } = theme;\n  components.forEach(({\n    name,\n    theme,\n    overrides\n  }) => {\n    const baseComponentClassName = `amplify-${name}`;\n    const componentClassName = `[data-amplify-theme=\"${themeName}\"] .${baseComponentClassName}`;\n    // unwrap the component theme\n    // if it is a function: call it with the defaultTheme to get a static object\n    const componentTheme = isFunction(theme) ? theme(tokens) : theme;\n    cssText += recursiveComponentCSS(componentClassName, componentTheme);\n    // if the component theme has overrides\n    // generate the appropriate CSS for each of them\n    if (overrides) {\n      overrides.forEach(override => {\n        // unwrap the override component theme just like above\n        const componentTheme = isFunction(override.theme) ? override.theme(tokens) : override.theme;\n        if ('mediaQuery' in override) {\n          cssText += `@media (${override.mediaQuery}) {\\n ${recursiveComponentCSS(componentClassName, componentTheme)} \\n}`;\n        }\n        if ('breakpoint' in override) {\n          const breakpoint = breakpoints.values[override.breakpoint];\n          cssText += `\\n@media (min-width: ${breakpoint}px) {\\n ${recursiveComponentCSS(componentClassName, componentTheme)} \\n}`;\n        }\n        if ('selector' in override) {\n          cssText += recursiveComponentCSS(`${override.selector} .${baseComponentClassName}`, componentTheme);\n        }\n        if ('colorMode' in override) {\n          cssText += `\\n@media (prefers-color-scheme: ${override.colorMode}) {\\n${recursiveComponentCSS(`[data-amplify-theme=\"${themeName}\"][data-amplify-color-mode=\"system\"] .${baseComponentClassName}`, componentTheme)}\\n}\\n`;\n          cssText += recursiveComponentCSS(`[data-amplify-theme=\"${themeName}\"][data-amplify-color-mode=\"${override.colorMode}\"] .${baseComponentClassName}`, componentTheme);\n        }\n      });\n    }\n  });\n  return cssText;\n}\nexport { createComponentCSS, recursiveComponentCSS };", "map": {"version": 3, "names": ["propsToString", "isFunction", "splitObject", "addVars", "selector", "vars", "Object", "entries", "map", "key", "value", "join", "recursiveComponentCSS", "baseSelector", "theme", "str", "_modifiers", "_element", "_vars", "props", "keys", "length", "selectors", "other", "startsWith", "for<PERSON>ach", "createComponentCSS", "components", "cssText", "tokens", "name", "themeName", "breakpoints", "overrides", "baseComponentClassName", "componentClassName", "componentTheme", "override", "mediaQuery", "breakpoint", "values", "colorMode"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/createTheme/createComponentCSS.mjs"], "sourcesContent": ["import { propsToString } from './utils.mjs';\nimport '@aws-amplify/core/internals/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { isFunction, splitObject } from '../../utils/utils.mjs';\n\nfunction addVars(selector, vars) {\n    if (!vars)\n        return '';\n    return `${selector} { ${Object.entries(vars)\n        .map(([key, value]) => {\n        return `--${key}:${value}; `;\n    })\n        .join(' ')}}\\n`;\n}\nfunction recursiveComponentCSS(baseSelector, theme) {\n    let str = '';\n    const { _modifiers = {}, _element = {}, _vars, ...props } = theme;\n    // if there are no props, skip\n    if (Object.keys(props).length) {\n        // separate psuedo/attribute selectors\n        const [selectors, other] = splitObject(props, (key) => key.startsWith(':') || key.startsWith('['));\n        Object.entries(selectors).forEach(([selector, value]) => {\n            // need to remove nested things like vars and elements\n            const { _modifiers = {}, _element = {}, _vars, ...props } = value;\n            str += `${baseSelector}${selector} { ${propsToString(props)} }\\n`;\n            str += addVars(`${baseSelector}${selector}`, _vars);\n        });\n        str += `${baseSelector} { ${propsToString(other)} }\\n`;\n    }\n    str += addVars(baseSelector, _vars);\n    Object.entries(_modifiers).forEach(([key, value]) => {\n        if (value && Object.keys(value).length) {\n            str += recursiveComponentCSS(`${baseSelector}--${key}`, value);\n        }\n    });\n    Object.entries(_element).forEach(([key, value]) => {\n        if (value && Object.keys(value).length) {\n            str += recursiveComponentCSS(`${baseSelector}__${key}`, value);\n        }\n    });\n    return str;\n}\n/**\n * This will take a component theme and create the appropriate CSS for it.\n *\n */\nfunction createComponentCSS({ theme, components, }) {\n    let cssText = '';\n    const { tokens, name: themeName, breakpoints } = theme;\n    components.forEach(({ name, theme, overrides }) => {\n        const baseComponentClassName = `amplify-${name}`;\n        const componentClassName = `[data-amplify-theme=\"${themeName}\"] .${baseComponentClassName}`;\n        // unwrap the component theme\n        // if it is a function: call it with the defaultTheme to get a static object\n        const componentTheme = isFunction(theme)\n            ? theme(tokens)\n            : theme;\n        cssText += recursiveComponentCSS(componentClassName, componentTheme);\n        // if the component theme has overrides\n        // generate the appropriate CSS for each of them\n        if (overrides) {\n            overrides.forEach((override) => {\n                // unwrap the override component theme just like above\n                const componentTheme = isFunction(override.theme)\n                    ? override.theme(tokens)\n                    : override.theme;\n                if ('mediaQuery' in override) {\n                    cssText += `@media (${override.mediaQuery}) {\\n ${recursiveComponentCSS(componentClassName, componentTheme)} \\n}`;\n                }\n                if ('breakpoint' in override) {\n                    const breakpoint = breakpoints.values[override.breakpoint];\n                    cssText += `\\n@media (min-width: ${breakpoint}px) {\\n ${recursiveComponentCSS(componentClassName, componentTheme)} \\n}`;\n                }\n                if ('selector' in override) {\n                    cssText += recursiveComponentCSS(`${override.selector} .${baseComponentClassName}`, componentTheme);\n                }\n                if ('colorMode' in override) {\n                    cssText += `\\n@media (prefers-color-scheme: ${override.colorMode}) {\\n${recursiveComponentCSS(`[data-amplify-theme=\"${themeName}\"][data-amplify-color-mode=\"system\"] .${baseComponentClassName}`, componentTheme)}\\n}\\n`;\n                    cssText += recursiveComponentCSS(`[data-amplify-theme=\"${themeName}\"][data-amplify-color-mode=\"${override.colorMode}\"] .${baseComponentClassName}`, componentTheme);\n                }\n            });\n        }\n    });\n    return cssText;\n}\n\nexport { createComponentCSS, recursiveComponentCSS };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,aAAa;AAC3C,OAAO,mCAAmC;AAC1C,OAAO,wCAAwC;AAC/C,SAASC,UAAU,EAAEC,WAAW,QAAQ,uBAAuB;AAE/D,SAASC,OAAOA,CAACC,QAAQ,EAAEC,IAAI,EAAE;EAC7B,IAAI,CAACA,IAAI,EACL,OAAO,EAAE;EACb,OAAO,GAAGD,QAAQ,MAAME,MAAM,CAACC,OAAO,CAACF,IAAI,CAAC,CACvCG,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;IACvB,OAAO,KAAKD,GAAG,IAAIC,KAAK,IAAI;EAChC,CAAC,CAAC,CACGC,IAAI,CAAC,GAAG,CAAC,KAAK;AACvB;AACA,SAASC,qBAAqBA,CAACC,YAAY,EAAEC,KAAK,EAAE;EAChD,IAAIC,GAAG,GAAG,EAAE;EACZ,MAAM;IAAEC,UAAU,GAAG,CAAC,CAAC;IAAEC,QAAQ,GAAG,CAAC,CAAC;IAAEC,KAAK;IAAE,GAAGC;EAAM,CAAC,GAAGL,KAAK;EACjE;EACA,IAAIR,MAAM,CAACc,IAAI,CAACD,KAAK,CAAC,CAACE,MAAM,EAAE;IAC3B;IACA,MAAM,CAACC,SAAS,EAAEC,KAAK,CAAC,GAAGrB,WAAW,CAACiB,KAAK,EAAGV,GAAG,IAAKA,GAAG,CAACe,UAAU,CAAC,GAAG,CAAC,IAAIf,GAAG,CAACe,UAAU,CAAC,GAAG,CAAC,CAAC;IAClGlB,MAAM,CAACC,OAAO,CAACe,SAAS,CAAC,CAACG,OAAO,CAAC,CAAC,CAACrB,QAAQ,EAAEM,KAAK,CAAC,KAAK;MACrD;MACA,MAAM;QAAEM,UAAU,GAAG,CAAC,CAAC;QAAEC,QAAQ,GAAG,CAAC,CAAC;QAAEC,KAAK;QAAE,GAAGC;MAAM,CAAC,GAAGT,KAAK;MACjEK,GAAG,IAAI,GAAGF,YAAY,GAAGT,QAAQ,MAAMJ,aAAa,CAACmB,KAAK,CAAC,MAAM;MACjEJ,GAAG,IAAIZ,OAAO,CAAC,GAAGU,YAAY,GAAGT,QAAQ,EAAE,EAAEc,KAAK,CAAC;IACvD,CAAC,CAAC;IACFH,GAAG,IAAI,GAAGF,YAAY,MAAMb,aAAa,CAACuB,KAAK,CAAC,MAAM;EAC1D;EACAR,GAAG,IAAIZ,OAAO,CAACU,YAAY,EAAEK,KAAK,CAAC;EACnCZ,MAAM,CAACC,OAAO,CAACS,UAAU,CAAC,CAACS,OAAO,CAAC,CAAC,CAAChB,GAAG,EAAEC,KAAK,CAAC,KAAK;IACjD,IAAIA,KAAK,IAAIJ,MAAM,CAACc,IAAI,CAACV,KAAK,CAAC,CAACW,MAAM,EAAE;MACpCN,GAAG,IAAIH,qBAAqB,CAAC,GAAGC,YAAY,KAAKJ,GAAG,EAAE,EAAEC,KAAK,CAAC;IAClE;EACJ,CAAC,CAAC;EACFJ,MAAM,CAACC,OAAO,CAACU,QAAQ,CAAC,CAACQ,OAAO,CAAC,CAAC,CAAChB,GAAG,EAAEC,KAAK,CAAC,KAAK;IAC/C,IAAIA,KAAK,IAAIJ,MAAM,CAACc,IAAI,CAACV,KAAK,CAAC,CAACW,MAAM,EAAE;MACpCN,GAAG,IAAIH,qBAAqB,CAAC,GAAGC,YAAY,KAAKJ,GAAG,EAAE,EAAEC,KAAK,CAAC;IAClE;EACJ,CAAC,CAAC;EACF,OAAOK,GAAG;AACd;AACA;AACA;AACA;AACA;AACA,SAASW,kBAAkBA,CAAC;EAAEZ,KAAK;EAAEa;AAAY,CAAC,EAAE;EAChD,IAAIC,OAAO,GAAG,EAAE;EAChB,MAAM;IAAEC,MAAM;IAAEC,IAAI,EAAEC,SAAS;IAAEC;EAAY,CAAC,GAAGlB,KAAK;EACtDa,UAAU,CAACF,OAAO,CAAC,CAAC;IAAEK,IAAI;IAAEhB,KAAK;IAAEmB;EAAU,CAAC,KAAK;IAC/C,MAAMC,sBAAsB,GAAG,WAAWJ,IAAI,EAAE;IAChD,MAAMK,kBAAkB,GAAG,wBAAwBJ,SAAS,OAAOG,sBAAsB,EAAE;IAC3F;IACA;IACA,MAAME,cAAc,GAAGnC,UAAU,CAACa,KAAK,CAAC,GAClCA,KAAK,CAACe,MAAM,CAAC,GACbf,KAAK;IACXc,OAAO,IAAIhB,qBAAqB,CAACuB,kBAAkB,EAAEC,cAAc,CAAC;IACpE;IACA;IACA,IAAIH,SAAS,EAAE;MACXA,SAAS,CAACR,OAAO,CAAEY,QAAQ,IAAK;QAC5B;QACA,MAAMD,cAAc,GAAGnC,UAAU,CAACoC,QAAQ,CAACvB,KAAK,CAAC,GAC3CuB,QAAQ,CAACvB,KAAK,CAACe,MAAM,CAAC,GACtBQ,QAAQ,CAACvB,KAAK;QACpB,IAAI,YAAY,IAAIuB,QAAQ,EAAE;UAC1BT,OAAO,IAAI,WAAWS,QAAQ,CAACC,UAAU,SAAS1B,qBAAqB,CAACuB,kBAAkB,EAAEC,cAAc,CAAC,MAAM;QACrH;QACA,IAAI,YAAY,IAAIC,QAAQ,EAAE;UAC1B,MAAME,UAAU,GAAGP,WAAW,CAACQ,MAAM,CAACH,QAAQ,CAACE,UAAU,CAAC;UAC1DX,OAAO,IAAI,wBAAwBW,UAAU,WAAW3B,qBAAqB,CAACuB,kBAAkB,EAAEC,cAAc,CAAC,MAAM;QAC3H;QACA,IAAI,UAAU,IAAIC,QAAQ,EAAE;UACxBT,OAAO,IAAIhB,qBAAqB,CAAC,GAAGyB,QAAQ,CAACjC,QAAQ,KAAK8B,sBAAsB,EAAE,EAAEE,cAAc,CAAC;QACvG;QACA,IAAI,WAAW,IAAIC,QAAQ,EAAE;UACzBT,OAAO,IAAI,mCAAmCS,QAAQ,CAACI,SAAS,QAAQ7B,qBAAqB,CAAC,wBAAwBmB,SAAS,yCAAyCG,sBAAsB,EAAE,EAAEE,cAAc,CAAC,OAAO;UACxNR,OAAO,IAAIhB,qBAAqB,CAAC,wBAAwBmB,SAAS,+BAA+BM,QAAQ,CAACI,SAAS,OAAOP,sBAAsB,EAAE,EAAEE,cAAc,CAAC;QACvK;MACJ,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACF,OAAOR,OAAO;AAClB;AAEA,SAASF,kBAAkB,EAAEd,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}