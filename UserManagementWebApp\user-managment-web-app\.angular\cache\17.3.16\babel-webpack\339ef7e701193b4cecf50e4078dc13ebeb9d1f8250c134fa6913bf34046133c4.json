{"ast": null, "code": "var AmplifyErrorCode;\n(function (AmplifyErrorCode) {\n  AmplifyErrorCode[\"NoEndpointId\"] = \"NoEndpointId\";\n  AmplifyErrorCode[\"PlatformNotSupported\"] = \"PlatformNotSupported\";\n  AmplifyErrorCode[\"Unknown\"] = \"Unknown\";\n  AmplifyErrorCode[\"NetworkError\"] = \"NetworkError\";\n})(AmplifyErrorCode || (AmplifyErrorCode = {}));\nexport { AmplifyErrorCode };", "map": {"version": 3, "names": ["AmplifyErrorCode"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/types/errors.mjs"], "sourcesContent": ["var AmplifyErrorCode;\n(function (AmplifyErrorCode) {\n    AmplifyErrorCode[\"NoEndpointId\"] = \"NoEndpointId\";\n    AmplifyErrorCode[\"PlatformNotSupported\"] = \"PlatformNotSupported\";\n    AmplifyErrorCode[\"Unknown\"] = \"Unknown\";\n    AmplifyErrorCode[\"NetworkError\"] = \"NetworkError\";\n})(AmplifyErrorCode || (AmplifyErrorCode = {}));\n\nexport { AmplifyErrorCode };\n"], "mappings": "AAAA,IAAIA,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAAC,cAAc,CAAC,GAAG,cAAc;EACjDA,gBAAgB,CAAC,sBAAsB,CAAC,GAAG,sBAAsB;EACjEA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;EACvCA,gBAAgB,CAAC,cAAc,CAAC,GAAG,cAAc;AACrD,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAE/C,SAASA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}