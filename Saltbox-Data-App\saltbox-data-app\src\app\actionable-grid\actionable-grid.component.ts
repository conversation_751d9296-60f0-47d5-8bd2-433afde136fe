import { AfterContentInit, Component, HostListener, Inject, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AgGridAngular, AgGridModule } from 'ag-grid-angular';
import { GridOptions, IRowNode, GetRowIdParams, RowClassParams, RowStyle, themeQuartz, themeBalham, themeMaterial, themeAlpine } from 'ag-grid-community';
import { firstValueFrom, Subscription } from 'rxjs';
import { CRUDActions, DynamicAssetTypes, ModifierSourceType } from '../core/enums/shared';
import { CommunicationServiceType, CommunicationToken } from '../core/models/communication-service-type';
import { ReportInfo } from '../core/models/report-info';
import { ReportParameter } from '../core/models/report-parameter';
import { ActionableGridService } from './services/actionable-grid.service';
import { NotificationService } from '../core/services/notification.service';
import { ResolutionService } from '../core/services/resolution.service';
import { ActionableGridViewModes, ParameterTypes } from '../core/enums/actionable-grid';
import { JSONSchemaParam } from '../core/models/json-schema-param';
import { deepCopy } from '../shared/utilities/copy.functions';
import { CustomDateComponent } from '../core/custom-component/custom-date-component';
import { ICanComponentDeactivate } from '../core/interface/can-component-deactivate';
import { ActionableGridColumnConfig } from '../core/models/actionable-grid-column-config';
import { MenuItem, SharedModule } from 'primeng/api';
import { ProjectVersion } from '../core/models/project-version';
import { ProjectsService } from '../core/services/projects.service';
import { UserActivity } from '../shared/models/user-activity';
import { UserActivityService } from '../core/services/user-activity.service';
import { DatastoresService } from '../core/services/datastores.service';
import { Datastore } from '../shared/models/datastore';
import { RecordsSaveEvent } from '../shared/models/records-save-event';
import { AssetEventType } from '../shared/enums/asset-workflow';
import { getDefaultValue } from '../shared/utilities/datatype.functions';
import { TilesConfig } from '../visualizations/models/base/tiles-config';
import { ChartConfig } from './grid-charts/models/chart-config';
import { FeatureFlagService } from '../core/services/feature-flag.service';
import { Features } from '../shared/enums/features.enum';
import { ActionableGridContextMenuService } from './services/actionable-grid-context-menu.service';
import { TilesService } from '../visualizations/services/tiles.service';
import { IRowActionsParamsContext } from '../core/models/row-actions-params-context';
import { ChangeHistoryService } from './services/change-history.service';
import { GroupUpdateInfo } from './model/group-update-info';
import { GridOptionsService } from './services/grid-options-service';
import { PostSaveEventService } from '../core/services/post-save-events.service';
import { RecordRefiner } from './refine-records/models/record-refiner';
import { RefineRecordsRequest } from './refine-records/models/refine-records-request';
import { DB_PRIMARY_KEY } from '../shared/constants/record.const';
import { ConfirmationDialogService } from '../shared/confirmation-dialog/services/confirmation-dialog.service';
import { ConnectorAppConfig } from '../shared/models/connector-app-config';
import { SaveRecordsResponse } from '../core/models/save-records-response';
import { UserService } from '../core/services/user.service';
import { UserPermissionLevels } from '../core/enums/shared';
import { ConditionalEvaluationService } from './services/conditional-evaluation.service';
import { DynamicAssetInfo } from '../core/models/dynamic-asset-info';
import { FormlyRendererTypes } from '../sb-formly-renderer/enums/formly-renderer-types.enum';
import { AGGridService } from '../core/services/ag-grid.service';
import { GridFormulaProcessorService } from './services/grid-formula-processor.service';
import { ActionableGridColService } from './services/actionable-grid-col.service';
import { AssetUrlPipe } from '../shared/pipes/asset-url.pipe';
import { BlockUIModule } from 'primeng/blockui';
import { ValidationResultsDialogComponent } from '../business-validation-results/validation-results-dialog.component';
import { ConfirmationDialogComponent } from '../shared/confirmation-dialog/confirmation-dialog.component';
import { EmailGridComponent } from './email-grid/email-grid.component';
import { DataEntryFormDialogComponent } from '../dynamic-forms/data-entry-form-dialog/data-entry-form-dialog.component';
import { IframeDialogComponent } from '../shared/iframe-dialog/iframe-dialog.component';
import { DynamicFormBodyComponent } from '../dynamic-forms/dynamic-form-body/dynamic-form-body.component';
import { SidebarModule } from 'primeng/sidebar';
import { ChartsPanelComponent } from './grid-charts/charts-panel/charts-panel.component';
import { VisualizationPanelComponent } from '../visualizations/visualization-panel/visualization-panel.component';
import { AccordionModule } from 'primeng/accordion';
import { UserParamFilterComponent } from './user-param-filter/user-param-filter.component';
import { RefineRecordsComponent } from './refine-records/refine-records.component';
import { ActionsMenuComponent } from './actions-menu/actions-menu.component';
import { NgClass, NgStyle } from '@angular/common';
import { PanelModule } from 'primeng/panel';
import { SbFullCalendarComponent } from '../sb-full-calendar/sb-full-calendar.component';
import { CryptoService } from '../core/services/crypto.service';
import { EditorColumnType } from '../shared/enums/editor-column-type.enum';
import { SBCalendarOptions } from '../sb-full-calendar/models/sb-calendar-options';
import { AgentChatComponent } from '../utilities/agent-chat/agent-chat.component';
import { GridLayout } from './model/grid-layout';
import { GridLayoutsService } from './services/grid-layouts.service';
import { UserParamFilterService } from '../core/services/user-param-filter.service';
import { ProfileProperty } from '../core/models/profile-property';

@Component({
  selector: 'app-actionable-grid',
  templateUrl: './actionable-grid.component.html',
  styleUrls: ['./actionable-grid.component.scss'],
  providers: [GridFormulaProcessorService],
  standalone: true,
  imports: [PanelModule, NgClass, ActionsMenuComponent, RefineRecordsComponent, UserParamFilterComponent, AccordionModule, NgStyle, VisualizationPanelComponent, ChartsPanelComponent, AgGridModule, SidebarModule, SharedModule, DynamicFormBodyComponent, IframeDialogComponent, DataEntryFormDialogComponent, EmailGridComponent, ConfirmationDialogComponent, ValidationResultsDialogComponent, BlockUIModule, AssetUrlPipe, SbFullCalendarComponent, AgentChatComponent]
})

export class ActionableGridComponent implements OnInit, AfterContentInit, ICanComponentDeactivate {
  @ViewChild('sbFullCalendar', { static: false, read: SbFullCalendarComponent }) sbFullCalendarComponent?: SbFullCalendarComponent;
  @ViewChild('agGrid') agGrid!: AgGridAngular;
  @ViewChild('chat') agentChat?: AgentChatComponent;

  selectedRecordId = '';
  reportInfo: ReportInfo;
  reportData: any[];
  calendarData: any[];
  // keeping the original dataset to reset the data
  unmodifiedRowData: any[];
  reportId: string;
  projectId: number;
  projectVersionId: number;
  projectIsLocked = false;
  requiredColumns: ActionableGridColumnConfig[] = [];
  refinerFields: ActionableGridColumnConfig[] = [];

  newRowIdPrefix = '__';
  newRowId = 0;
  isPreview = false;
  sbiWrapperPad = 'sbi-view-pad';
  showUserParameter = false;
  showAgentChatSidebar = false;

  // This is used to lock updated rows when the save is taking place.
  lockRowsActionSubscription: Subscription;

  // This is to set color of flashing cell after save.
  flashingCellColor: string;

  // This is to expand all row groups by default.
  groupDefaultExpanded = -1;
  jsonSchemaParam = new JSONSchemaParam();
  showDataEntryForm = false;
  showFormlyDataEntryDialog = false;
  showConnectorScreen = false;
  showRefinerPanel = false;
  showValidationResults = false;
  saveRecordsResponse: SaveRecordsResponse;

  showEmailForm = false;
  columnsToExport: string[] = [];
  datastore: Datastore;
  uniqueFormName: string;
  layouts: GridLayout[];

  contextMenuItems = (params) => this.actionableGridContextMenuService.getContextMenuItems(params, this.agGrid.api);

  gridCharts: any[] = [];
  themes = [
    { label: "themeQuartz", theme: themeQuartz },
    { label: "themeBalham", theme: themeBalham },
    { label: "themeMaterial", theme: themeMaterial },
    { label: "themeAlpine", theme: themeAlpine },
  ];

  theme = themeBalham;

  public gridOptions: GridOptions = {
    components: {
      agDateInput: CustomDateComponent,
    },
    defaultColDef: {
      // enable sorting across all columns - including Row Group Columns
      sortable: true,
      filter: 'agMultiColumnFilter',
      resizable: true,
    },
    detailRowAutoHeight: true,
    customChartThemes: {
      sbChartsTheme: {
        palette: {
          fills: ['#007BB5', '#c35467', '#6197c4', '#d78a92', '#c3d2e2', '#e6bdc0'],
          strokes: ['#ccc'],
        }
      },
    },
    chartThemes: ['sbChartsTheme', 'ag-material'],
    getRowId: (params: GetRowIdParams) => { return String(params.data[DB_PRIMARY_KEY]) },
  };

  // for user entered parameters
  userParameters: ReportParameter[] = [];
  slicerParams: ReportParameter[];
  showActionableGrid = false;

  isShowSpinner = false;

  fileUploadProjectId: string;
  fileUploadProjectVersionId: string;
  fileUploadDataStoreName: string;

  projectVersion: ProjectVersion;

  disableFileUpload = false;
  visualizationFlag = false;
  gridChartsFlag = false;

  tilesConfigs: TilesConfig[] = [];
  chartConfigs: ChartConfig[] = [];
  recordRefiners: RecordRefiner[];

  connectorAppConfig = new ConnectorAppConfig();
  userPermissionLevel: UserPermissionLevels;
  formAsset: { assetInfo: DynamicAssetInfo, targetObj: any };

  selectedView: ActionableGridViewModes = ActionableGridViewModes.Table;  // Default to Table view
  calendarViewFlag: boolean;
  agentChatFlag: boolean;
  gridColumnsOptionFlag: boolean;
  calendarOptions: SBCalendarOptions;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private notificationService: NotificationService,
    private actionableGridService: ActionableGridService,
    private actionableGridColService: ActionableGridColService,
    private resolutionService: ResolutionService,
    private datastoresService: DatastoresService,
    private userActivityService: UserActivityService,
    private changeHistoryService: ChangeHistoryService,
    private projectsService: ProjectsService,
    private featureFlagService: FeatureFlagService,
    private actionableGridContextMenuService: ActionableGridContextMenuService,
    private tilesService: TilesService,
    private gridOptionsService: GridOptionsService,
    private aGGridService: AGGridService,
    private postSaveEventService: PostSaveEventService,
    private confirmationDialogService: ConfirmationDialogService,
    private userService: UserService,
    private conditionalEvaluationService: ConditionalEvaluationService,
    private formulaProcessorService: GridFormulaProcessorService,
    private cryptoService: CryptoService,
    private gridLayoutService: GridLayoutsService,
    private filterService: UserParamFilterService,
    @Inject(CommunicationToken) private communicationService: CommunicationServiceType) {
  }

  @HostListener('window:resize', ['$event.target'])
  setResolution(event): void {
    this.resolutionService.setResolution(event.innerWidth, event.innerHeight);
  }

  @HostListener('window:beforeunload', ['$event'])
  unloadNotification($event: any) {
    if (!this.canDeactivate()) {
      $event.returnValue = true;
    }
  }

  ngOnInit(): void {
    if (this.activatedRoute) {
      this.activatedRoute.queryParams.subscribe(urlParams => {
        // check if the route variable is coming from Reporting iFrame request (url variable: hideMenu)
        this.isPreview = urlParams.hideMenus;
      });

      this.activatedRoute.params.subscribe(params => {
        this.reportId = params.reportId;
        this.projectId = params.projectId;
        this.projectVersionId = params.projectVersionId;

        // logging user activity
        if (!this.isPreview) {
          this.userActivityService.addUserActivity(
            new UserActivity({ projectId: this.projectId, projectVersionId: this.projectVersionId, url: window.location.pathname, reportId: this.reportId }));
        }

        this.loadActionableGrid();
      });
    }
  }

  async loadActionableGrid() {
    // RESET THE DEFAULTS WHEN THE COMPONENT RELOADS
    this.tilesConfigs = [];
    this.chartConfigs = [];
    this.showUserParameter = false;
    this.showActionableGrid = false;
    this.reportInfo = undefined;
    this.reportData = [];
    this.unmodifiedRowData = [];
    this.reportData = [];
    this.datastore = undefined;
    this.recordRefiners = [];
    this.calendarOptions = undefined;
    this.saveRecordsResponse = undefined;
    this.selectedView = ActionableGridViewModes.Table;  // Default to Table view

    this.isShowSpinner = true;

    this.projectVersion = await this.getProjectVersion();
    if (this.projectVersion) {
      this.projectIsLocked = this.projectVersion.isLocked;
    }

    await this.setFeatureFlags();

    if (this.router.url.startsWith('/saltboxdataapp/embeddedview')) {
      this.isPreview = true;
      this.hideNavigationApp();
      this.sbiWrapperPad = 'sbi-view-pad-none';
    }

    if (!this.isPreview) {
      if (this.router.url.startsWith('/saltboxdataapp/app-view')) {
        await this.getAppUserPermission();

        if (this.userPermissionLevel === UserPermissionLevels.None) {
          return;
        }
      }
    }

    if (this.reportId) {
      // refactored out into its own method
      this.loadReportInfo();
    }

    this.actionableGridService.projectIdSubject$.subscribe(pId => this.fileUploadProjectId = pId);
    this.actionableGridService.versionIdSubject$.subscribe(vId => this.fileUploadProjectVersionId = vId);
    this.actionableGridService.datastoreNameSubject$.subscribe(dSn => this.fileUploadDataStoreName = dSn);
  }

  // After checking the content and ensuring it's loaded - remove the menus
  ngAfterContentInit() {
    if (this.isPreview) {
      this.hideMenusForEmbed();
    }

  }
  // this should be the new way to do it.  will need to refactor the other feature flag calls
  async setFeatureFlags(): Promise<void> {
    const featureFlags = await this.featureFlagService.getFeatureFlags();

    this.visualizationFlag = featureFlags.some(x => x.name === Features.Visualizations);
    this.gridChartsFlag = featureFlags.some(x => x.name === Features.Visualizations);
    this.calendarViewFlag = featureFlags.some(x => x.name === Features.CalendarView);
    this.agentChatFlag = featureFlags.some(x => x.name === Features.AgentChat);
    this.gridColumnsOptionFlag = featureFlags.some(x => x.name === Features.GridColumnsOptions);
  }

  loadReportInfo() {
    this.actionableGridService.getReportInfo(this.reportId).subscribe({
      next: async (reportInfo: ReportInfo) => {
        if (!reportInfo) {
          this.notificationService.showError('Error', `No report found for Report ID ${this.reportId}`);
          return;
        }

        // extracting paramList from reportInfo
        this.reportInfo = reportInfo;
        this.userParameters = reportInfo.params;
        this.slicerParams = deepCopy(this.userParameters);
        this.reportInfo.includeIdColumn = true;

        if (this.userPermissionLevel === UserPermissionLevels.View) {
          this.reportInfo.allowAddNewRow = false;
        }

        if (this.reportInfo.formatConfig?.tilesConfig) {
          this.tilesConfigs.push(...this.reportInfo.formatConfig.tilesConfig);
        }

        this.changeHistoryService.initChangeHistory(this.reportInfo.projectId, this.reportInfo.projectVersionId);

        this.requiredColumns = this.reportInfo?.formatConfig?.actionableGridColumnsConfig
          ?.filter(actionableGridColumnConfig => actionableGridColumnConfig.required);

        this.refinerFields = this.reportInfo?.formatConfig?.actionableGridColumnsConfig
          ?.filter(actionableGridColumnConfig => actionableGridColumnConfig.slicerFilter);

        this.gridOptionsService.setRowClassRules(this.gridOptions, this.requiredColumns);

        // Enabling columns options
        if (this.gridColumnsOptionFlag) this.gridOptionsService.enableColumnsOptions(this.gridOptions);

        // setting the row actions context. it's only here because of the allowDeleteRow
        const fieldsConfig = this.actionableGridColService.getFormlyFieldsConfigByGridConfig(this.reportInfo?.formatConfig?.actionableGridColumnsConfig);
        this.gridOptions.context = {
          parentComponent: this,
          allowDeleteRow: this.reportInfo?.allowAddNewRow,
          allowUploadFiles: true,
          userPermissionLevel: this.userPermissionLevel,
          showEditorFunction: (editMode) => this.showFormEditor(editMode),
          deleteRowFunction: (data) => this.deleteRow(data),
          getMainGridId: () => { return this.agGrid.api.getGridId() },
          getMasterRecordParams: (recordId) => { return { baseSchema: this.datastore.baseSchema, data: this.agGrid?.api?.getRowNode(recordId)?.data, fieldsConfig } }
        } as IRowActionsParamsContext;

        if (this.reportInfo.enablePagination) {
          this.gridOptions.pagination = true;
          this.gridOptions.paginateChildRows = true;
          this.gridOptions.paginationAutoPageSize = this.reportInfo.paginationAutoPaging;

          if (!this.reportInfo.paginationAutoPaging) {
            this.gridOptions.paginationPageSize = this.reportInfo.defaultPageSize;
            this.gridOptions.paginationPageSizeSelector = [20, 50, 100];
          }
        }

        // Loading datastore
        this.datastoresService.getDatastores(
          this.reportInfo.projectId,
          this.reportInfo.projectVersionId,
          this.reportInfo.formName).subscribe(
            async (datastores: Datastore[]) => {
              if (!datastores || datastores.length === 0) {
                this.notificationService.showError('Error', `Datastore not found!`);
                this.isShowSpinner = false;
                return;
              }

              if (datastores.length > 1) {
                this.notificationService.showError('Error', `Multiple datastore with the same name have been found!`);
                this.isShowSpinner = false;
                return;
              }

              this.datastore = datastores[0];

              this.reportInfo.columnDefs = await this.actionableGridColService
                .getColumnDefs(this.reportInfo.formatConfig?.actionableGridColumnsConfig,
                  this.datastore.baseSchema,
                  this.gridOptions,
                  true,
                  this.projectVersionId,
                  this.reportInfo.allowAddNewRow,
                  null,
                  this.userPermissionLevel);

              this.setConditionalFormatting();

              // setting the columns which has to be exported
              this.columnsToExport = this.reportInfo?.columnDefs
                ?.filter(x => x.field !== 'fileUpload' && x.field !== '_id')
                .map(x => x.field);

              this.showUserParameter = this.reportInfo?.params?.filter(x => x.paramType === ParameterTypes.UserEntered)?.length > 0;
              this.subscribeToDataReload();

              // Loading grid layouts
              await this.loadLayouts();
            });

        this.buildBreadcrumb();
      }
    });
  }

  private setConditionalFormatting() {
    if (!this.reportInfo.enableConditionalFormatting || !this.reportInfo?.conditionalFormattingConfig?.length)
      return;

    const setStyles = (profileProperties: ProfileProperty[]) => {

      // Row Styles
      const getRowStyleFunc: (params: RowClassParams) => RowStyle | undefined = (params) => {
        return this.gridOptionsService.getRowStyle(params.data, this.reportInfo?.conditionalFormattingConfig, profileProperties);
      };

      this.agGrid?.api ? this.agGrid.api.setGridOption('getRowStyle', getRowStyleFunc) : this.gridOptions.getRowStyle = getRowStyleFunc;

      // Cell Styles
      this.gridOptionsService.setCellStyles(this.reportInfo.columnDefs, this.reportInfo.conditionalFormattingConfig, profileProperties);
      if (this.agGrid?.api)
        this.agGrid.api.setGridOption('columnDefs', this.reportInfo.columnDefs);

      this.agGrid?.api?.redrawRows();
    }

    if (this.reportInfo?.conditionalFormattingConfig?.find(c => c.conditions?.some(c => c.isProfileParameter))) {
      this.userService.getCompleteUserProfilePropertyList().subscribe({
        next: (profileProperties: ProfileProperty[]) => {
          setStyles(profileProperties);
        }
      });
    } else {
      setStyles([]);
    }
  }

  private subscribeToDataReload() {
    this.filterService.reloadData$.subscribe(params => {
      if (params.clearData) {
        this.saveRecordsResponse = undefined;

        if (this.reportData?.length > 0) {
          this.unmodifiedRowData = [];
          this.reportData = [];
        }

        this.isShowSpinner = false;
        return;
      }

      this.slicerParams = params.params;
      this.showActionableGrid = true;
      this.loadReportData(this.slicerParams, true);
    });

  }

  private async loadLayouts() {

    let layouts = [];
    // If by any chance the grid layout service throws an error, we will catch it and continue without layouts
    try {
      layouts = await firstValueFrom(this.gridLayoutService.getLayouts(this.reportInfo.reportId));
    } catch (error) {
      layouts = [];
    }

    if (layouts.length > 0) {
      const layoutHashId = await this.gridLayoutService.getGridHashId(this.reportInfo);
      const defaultLayout = layouts.find(layout => layout.isCurrentLayout && layout.layoutHashId === layoutHashId)
        ?? layouts.find(layout => layout.isDefault && layout.layoutHashId === layoutHashId);
      this.layouts = layouts;

      // If there are saved slicers or user parameters and all parameters are optional, layout manager will load the report
      if (defaultLayout && (defaultLayout.hasRecordRefiners || defaultLayout.hasUserParams)) {
        this.showActionableGrid = true;
        return;
      }
    } else {
      this.layouts = [];
    }
  }

  private async getAppUserPermission() {
    this.userPermissionLevel = UserPermissionLevels[await this.userService.getUserAppGroupRoles(this.projectVersionId, this.projectVersion.appManifest, this.reportId)];

    if (this.userPermissionLevel === UserPermissionLevels.None) {
      this.notificationService.showError('Access Denied', "You do not have access to this grid. Please request access from your app admin.");
      this.isShowSpinner = false;
    }
  }

  loadReportData(params: ReportParameter[], showSpinner = false): void {

    if (this.filterService.validateUserEnteredParams(params, true) === false)
      return;

    if (showSpinner)
      this.isShowSpinner = showSpinner;

    // Resetting the profile properties so that the latest values are fetched from the db
    const refineRequest = new RefineRecordsRequest(this.reportId, this.reportInfo.dataStoreViewName, true, params);

    // Subscribe() would be enough to bind the grid, we are just setting the backup dataset here
    this.datastoresService.getRecords(this.datastore.id, refineRequest).subscribe({
      next: async (reportData: any[]) => {
        if (reportData) {
          this.reportData = reportData;
          this.unmodifiedRowData = deepCopy(this.reportData);

          // default to calendar view if calendar view is enabled
          if (this.calendarViewFlag && this.reportInfo.enableCalendarView && !this.calendarData) {
            await this.checkViewHashId();
            this.setCalendarOptions();
            this.switchView(this.reportInfo.calendarViewConfig?.initialView);
          }

          if (this.selectedView !== ActionableGridViewModes.Table)
            this.calendarData = this.reportData;

          // for refresh button so spinner timing is dependent on completion
          this.isShowSpinner = false;
        }
      },
      error: (error: any) => {
        if (error.status === 404) {
          this.reportData = [];
          this.unmodifiedRowData = [];
        }

        this.notificationService.showError('Error', error.message);
        this.isShowSpinner = false;
      }
    });

    // checking if agGrid is initialized to prevent a console error
    this.agGrid?.api?.refreshCells({ force: true });
  }

  onFirstDataRendered(params) {
    this.loadGridCharts();
  }

  loadGridCharts() {
    if (this.reportInfo.formatConfig?.chartConfigs) {
      this.chartConfigs.push(...this.reportInfo.formatConfig.chartConfigs);
    }
  }

  // If the embedded view is triggered we use the CommunicationService to remove the menus inside the iframe
  hideMenusForEmbed() {
    // Communication Service call to remove Menus
    this.hideNavigationApp();
    this.sbiWrapperPad = 'sbi-view-pad-none';
  }

  onColumnRowGroupChanged(event): void {
    const groupedColumns = event.columns.map((item) => item.colId);
    const autoColumn = event.api.getColumn('ag-Grid-AutoColumn'); // This is the 'Group' Column.

    if (autoColumn !== null) {
      autoColumn.colDef.headerName = `Group by ${groupedColumns.join(', ')}`;
      event.api.refreshHeader();
      event.api.autoSizeColumn(autoColumn);
    }
  }

  onGridReady(params) {
    const datastoreStructure = this.actionableGridService.getDatastoreStructure(this.reportInfo.formatConfig?.actionableGridColumnsConfig, this.datastore.baseSchema);
    this.aGGridService.registerGridDataProvider(params.api.getGridId(), { grid: this.agGrid, datastoreStructure });
    this.formulaProcessorService.setupDataChangeListener(this.agGrid);

    this.lockRowsActionSubscription = this.datastoresService.lockRowsAction.subscribe(lock => {
      if (lock) {
        this.changeHistoryService.getAllModifiedRecords()
          .forEach(modifiedRecord => modifiedRecord.saveApplied = lock);
      }
    });
  }

  saveChanges(): void {

    if (!this.hasChanges()) {
      return;
    }

    const errors = this.getValidationErrors();
    if (errors?.length) {
      this.notificationService.showError('Error', errors.join(',\n'));
      return;
    }

    this.isShowSpinner = true;
    this.addInvokeWorkflowEvent();
    this.datastoresService.saveRecords(this.changeHistoryService.getBatchChnages(true), this.datastore.id)
      .subscribe({
        next: (saveRecordsResponse: SaveRecordsResponse) => {
          this.saveRecordsResponse = saveRecordsResponse;
          this.isShowSpinner = false;
          this.showValidationResults = this.saveRecordsResponse?.failedValidations?.length > 0;

          if (!saveRecordsResponse.hasFailures) {
            // warning if some records sent to update but they didn't
            if (saveRecordsResponse.recordIds?.length != this.changeHistoryService.getBatchChnages()?.getMasterRecordObjectIds()?.size) {
              this.notificationService.showWarning("Warning", "The record(s) you are trying to update do not exist or have already been updated. Only the existing record(s) were updated.");
            }

            this.flashSavedRows(true);
            this.changeHistoryService.resetPendingChanges();
            this.notificationService.showSuccess('Success', 'Saved Successfully');

            // note: if loadReportData happens fast user will not set the flash changes
            this.loadReportData(this.slicerParams);
          }
        },
        error: err => {
          this.isShowSpinner = false;
          this.flashSavedRows(false);
          this.notificationService.showError('Error', err.error);
        }
      });
  }

  getValidationErrors() {
    const errors: string[] = [];

    if (!this.requiredColumns?.length) {
      return errors;
    }

    this.changeHistoryService.getAllModifiedRecords()
      .forEach(modifiedRecord => {
        if (!modifiedRecord?.data
          || modifiedRecord.data?.action === CRUDActions.Delete
          || modifiedRecord.data?.action === CRUDActions.DeleteArray)
          return;

        const requiredDisplayNames: string[] = [];
        const missingRequiredProperties = modifiedRecord.data?.properties
          ?.filter(changeDetail => changeDetail.newValue == null || changeDetail.newValue === '');
        this.requiredColumns.forEach(requiredColumn => {
          const rowData = this.reportData.find(row => row[DB_PRIMARY_KEY] === modifiedRecord.id);
          if (!rowData) {
            return;
          }

          const modifiedProperty = missingRequiredProperties.find(changeDetail => changeDetail.property === requiredColumn.column);

          if (modifiedProperty || rowData[requiredColumn.column] == null || rowData[requiredColumn.column] === '') {
            requiredDisplayNames.push(requiredColumn.displayName || requiredColumn.column);
          }
        });

        if (requiredDisplayNames?.length) {
          if (requiredDisplayNames.length === 1) {
            errors.push(`${requiredDisplayNames[0]} is a mandatory field and must be provided`);
          } else {
            const displayNameCsv = requiredDisplayNames
              .slice(0, requiredDisplayNames.length - 1)
              .join(', ');
            errors.push(`${displayNameCsv} & ${requiredDisplayNames[requiredDisplayNames.length - 1]} are mandatory fields and must be provided`);
          }
        }
      });

    return errors;
  }

  addInvokeWorkflowEvent() {
    const saveGridEvent = this.reportInfo.gridEvents?.find(gridEvent => gridEvent.eventType === AssetEventType.GridSave);
    if (!saveGridEvent) {
      return;
    }

    this.postSaveEventService.addInvokeWorkflowEvent({
      recordsSaveEvent: new RecordsSaveEvent(
        this.reportInfo.reportId, this.reportInfo.reportName,
        ModifierSourceType.ActionableGrid, this.datastore.name,
        this.datastore.id),
      assetEvent: saveGridEvent
    });
  }

  flashSavedRows(isSuccessful: boolean) {
    if (this.selectedView !== ActionableGridViewModes.Table) return;

    this.flashingCellColor = isSuccessful ? 'rgb(106, 186, 69, .35)' : 'pink';
    const modifiedRecordNodes: IRowNode[] = [];
    for (const modifiedRecord of this.changeHistoryService.getAllModifiedRecords().filter(x => !x.recordPath)) {
      if (modifiedRecord.data?.action === CRUDActions.Delete) { continue; }

      if (modifiedRecord.saveApplied) {
        const rowNode = this.agGrid.api.getRowNode(modifiedRecord.id);

        if (rowNode)
          modifiedRecordNodes.push(rowNode);
      }
    }

    if (modifiedRecordNodes.length === 0) return;

    this.agGrid.api.flashCells(
      {
        rowNodes: modifiedRecordNodes,
        flashDuration: 5000,
        fadeDuration: 2000
      });
  }

  undoAll(): void {
    // we need to make sure if this is the optimized way
    if (this.unmodifiedRowData) {
      this.reportData = deepCopy(this.unmodifiedRowData);
      this.changeHistoryService.resetPendingChanges();
      this.saveRecordsResponse = undefined;

      this.agGrid.api.updateGridOptions({ rowData: this.reportData });
      this.calendarData = this.reportData;
    }
  }

  undoLastChange(): void {
    // Note: reportData and ag grid row data is different, ag grid apply transactions doesn't apply on the actual data
    const recordIds = this.changeHistoryService.undo(this.agGrid, this.aGGridService.getRowData(this.agGrid.api));

    if (this.sbFullCalendarComponent) {
      const data = [];
      recordIds.forEach(id => {
        const node = this.agGrid.api.getRowNode(id);
        if (node) {
          data.push(node.data);
        }
      });

      this.sbFullCalendarComponent?.updateEvents(data);
    }
  }

  getProjectVersion() {
    return this.projectsService.getProjectVersion(`${this.projectVersionId}`);
  }

  async buildBreadcrumb() {
    if (this.projectVersion) {
      const items: MenuItem[] = [
        { label: 'My Apps', routerLink: '/dashboard-v3' },
        {
          label: this.projectVersion.appManifest?.name,
          routerLink: `/app-view/${this.reportInfo.projectId}/${this.reportInfo.projectVersionId}`
        },
        { label: this.reportInfo?.reportName }
      ];
      this.communicationService.updateBreadcrumb(items);
    }
  }

  hideNavigationApp() {
    this.communicationService.next({
      fromAppName: this.communicationService.currentAppName,
      toAppName: this.communicationService.navigationAppName,
      eventType: 'HideNavigationBar',
      data: {}
    });
  }

  onUserEnteredParamsSubmitted(event: ReportParameter[]): void {
    this.showActionableGrid = true;
    this.userParameters = deepCopy(this.userParameters);
    this.slicerParams = deepCopy(this.userParameters);

    const parameterFilters = this.userParameters.map(up => { return { 'label': up.paramSource, 'value': up.parameterValue }; });
    this.tilesService.updateFilterParams(parameterFilters);
  }

  onHideSliderPanel(event: boolean) {
    this.showRefinerPanel = event;
  }

  canDeactivate() {
    if (this.changeHistoryService.hasModifiedRecords()) {
      return confirm('There are unsaved changes. Do you want to continue without saving?');
    }

    return true;
  }

  async showFormEditor(editMode: boolean, calendarData?) {
    if (!calendarData && ((!editMode && !this.reportInfo?.allowAddNewRow)
      || (editMode && (this.agGrid.api.getSelectedNodes().length < 1 || this.agGrid.api.getSelectedNodes()[0].group)))) {
      return;
    }

    const handleUnsavedChanges = (onCancelAction) => {
      if (this.changeHistoryService.hasChanges() || this.changeHistoryService.hasModifiedRecords()) {
        this.confirmationDialogService.showDialog("actionable-grid", {
          header: 'Unsaved Changes Detected',
          message: 'You have made changes that have not been saved. What would you like to do?',
          okButtonText: 'Return to Editing',
          cancelButtonText: 'Discard Changes',
          onCancel: onCancelAction,
        });
        return true;
      }
      return false;
    };

    const data = calendarData ? calendarData : editMode ? this.agGrid.api.getSelectedNodes()[0].data : undefined;
    this.formAsset = await this.conditionalEvaluationService.getFormAsset(
      this.projectId, this.projectVersionId, data,
      this.reportInfo.enableConditionalForms ? this.reportInfo.conditionalFormsConfig : undefined,
      this.reportInfo?.formatConfig?.dynamicAssetInfo,
      this.reportInfo.formatConfig?.dynamicFormId,
      this.reportInfo.formatConfig.actionableGridColumnsConfig);

    this.selectedRecordId = editMode ? data[DB_PRIMARY_KEY] : '';

    let excludedRenderes = [];
    switch (this.formAsset?.assetInfo?.type) {
      case DynamicAssetTypes.ConnectorApp:
        if (!(this.formAsset?.targetObj as ConnectorAppConfig))
          return;

        (this.formAsset.targetObj as ConnectorAppConfig).connectorIdField = this.formAsset.assetInfo.idField;
        (this.formAsset.targetObj as ConnectorAppConfig).connectorIdFieldValue = data ? data[this.connectorAppConfig.connectorIdField] : undefined;

        if (handleUnsavedChanges(() => {
          this.undoAll();
          this.showConnectorScreen = true;
        })) return;

        this.showConnectorScreen = true;
        return;

      case DynamicAssetTypes.DynamicForm:
        if (handleUnsavedChanges(() => {
          this.undoAll();
          this.showFormlyDataEntryDialog = true;
        })) return;

        this.showFormlyDataEntryDialog = true;
        break;
      case DynamicAssetTypes.Default:
        // filtering the arrays and objects ** Note: type of target object is DynamicForm
        excludedRenderes = [FormlyRendererTypes.Object, FormlyRendererTypes.CollectionForm, FormlyRendererTypes.CollectionGrid, FormlyRendererTypes.CollectionRepeat];
        this.formAsset.targetObj.fieldsConfig = this.formAsset.targetObj.fieldsConfig.filter(fc => !excludedRenderes.includes(fc.type as FormlyRendererTypes));

        this.jsonSchemaParam.action = editMode ? CRUDActions.Update : CRUDActions.Create;
        this.jsonSchemaParam.title = editMode ? 'Update Selected Record' : 'Add New Record';
        this.jsonSchemaParam.originalData = editMode ? data : await this.actionableGridService.getNewRowData(this.reportInfo.formatConfig?.actionableGridColumnsConfig);
        this.jsonSchemaParam.data = deepCopy(this.jsonSchemaParam.originalData);
        this.showDataEntryForm = true;
        return;
      default:
        break;
    }
  }

  showRefiner() {
    this.showRefinerPanel = !this.showRefinerPanel;
  }

  onDataEntryFormSubmit(event: any) {
    const data = event.data;
    if (!data) {
      this.showDataEntryForm = false;
      return;
    }
    this.jsonSchemaParam.data = data;
    const fields = Object.getOwnPropertyNames(this.datastore.baseSchema.properties);

    let id = undefined;
    switch (this.jsonSchemaParam.action) {
      case CRUDActions.Update:
        {
          const originalData = deepCopy(this.jsonSchemaParam.originalData);
          id = originalData[DB_PRIMARY_KEY];
          const selectedNode = this.agGrid.api.getRowNode(id);
          if (selectedNode) {
            fields.forEach(field => {
              let fieldValue = data[field];

              if (fieldValue != this.jsonSchemaParam.originalData[field]) {
                const fieldType = this.datastore.baseSchema.properties[field].type;
                const fieldFormat = this.datastore.baseSchema.properties[field].format;

                fieldValue = fieldValue == null ? getDefaultValue(fieldType, fieldFormat) : fieldValue;

                // If this field exists in the grid, we need to update the grid, if not, we need to update the data
                if (this.agGrid.api.getColumnDef(field)) {
                  selectedNode.setDataValue(field, fieldValue);
                } else {
                  selectedNode.data[field] = fieldValue;
                }
              }
            });

            // Update the data in the calendar
            this.sbFullCalendarComponent?.updateEvents([selectedNode.data]);

            this.changeHistoryService.trackGroupUpdate([new GroupUpdateInfo(id, originalData, selectedNode.data)]);
            this.updateMetadata(selectedNode, event.aliasIds);
            this.agGrid.api.refreshClientSideRowModel('group');
          }
        }
        break;
      case CRUDActions.Create:
        {
          id = this.newRowIdPrefix + this.newRowId.toString();
          const newRowData = {};
          newRowData[DB_PRIMARY_KEY] = id;

          fields.forEach(field => {
            const fieldType = this.datastore.baseSchema.properties[field].type;
            const fieldFormat = this.datastore.baseSchema.properties[field].format;
            const fieldValue = (data[field] !== undefined && data[field] !== null) ? data[field] : getDefaultValue(fieldType, fieldFormat);

            // first we need to create the new rowdata, and we are doing this to cover undefined fields
            newRowData[field] = fieldValue;
          });

          // Update the data in the calendar
          this.sbFullCalendarComponent?.updateEvents([newRowData]);

          this.changeHistoryService.trackAdd(id, newRowData);

          this.reportData.push(newRowData);
          this.agGrid.api.applyTransaction({ add: [newRowData] });
          this.agGrid.api.refreshClientSideRowModel('group');

          this.updateMetadata(this.agGrid.api.getRowNode(id), event.aliasIds);
          this.aGGridService.scrollToRowNode(this.agGrid.api, id);

          this.newRowId++;
        }
        break;
    }

    this.aGGridService.triggerGridDataChangeEvent(this.agGrid.api.getGridId(), id);
    this.showDataEntryForm = false;
  }

  onDataEntryFormHide() {
    this.actionableGridService.dataEntryFormClosed.next(this.jsonSchemaParam.originalData[DB_PRIMARY_KEY]);
  }

  // for refresh button
  onRefreshGridData() {
    if (!this.showActionableGrid)
      return;

    const reset = () => {
      this.saveRecordsResponse = undefined;
      this.userService.reset();
      this.setConditionalFormatting();
      this.loadReportData(this.slicerParams, true);
    }

    if (this.hasPendingChanges() || this.hasChanges()) {
      this.confirmationDialogService.showDialog("actionable-grid",
        {
          header: 'Confirm Refresh', message: 'Unsaved changes detected, do you want to refresh without saving?', okButtonText: 'Confirm',
          onConfirm: () => {
            this.changeHistoryService.resetPendingChanges();
            reset();
          }
        })
    }
    else {
      reset();
    }
  }

  hasChanges() {
    return this.changeHistoryService.hasModifiedRecords();
  }

  hasSlicer() {
    return this.refinerFields?.length > 0;
  }

  hasPendingChanges() {
    return this.changeHistoryService.hasChanges();
  }

  onCellFocused(event) {
    this.actionableGridService.focusOnCellEditor(event?.column?.colId, event.rowIndex, this.agGrid.api);
  }

  updateMetadata(rowNode: IRowNode, aliasIds: string[]) {
    if (aliasIds) {
      const rowNodeAliasIds = rowNode.data?.__sbmeta?.Attachments;
      if (!rowNodeAliasIds ||
        rowNodeAliasIds.filter(x => !aliasIds.includes(x)).concat(aliasIds.filter(x => !rowNodeAliasIds.includes(x))).length !== 0) {
        this.changeHistoryService.trackAttachments(rowNode.id, aliasIds);
        rowNode.data.__sbmeta = this.actionableGridService.updateAttachments(rowNode.data.__sbmeta, aliasIds);
        rowNode.setData(rowNode.data);
      }
    }
  }

  onEmailDialogClose(event: boolean) {
    this.showEmailForm = event;
  }

  onSendEmail() {
    this.showEmailForm = true;
  }

  deleteRow(data: any): void {
    const id = data[DB_PRIMARY_KEY];
    const rowNode = this.agGrid.api.getRowNode(id);
    this.changeHistoryService.trackDelete(id, data, rowNode.rowIndex);
    this.agGrid.api.applyTransaction({ remove: [data] });
  }

  connectorAppClose() {
    this.loadReportData(this.slicerParams, true);
  }

  switchView(view: ActionableGridViewModes) {
    if (this.selectedView === ActionableGridViewModes.Table && view !== ActionableGridViewModes.Table)
      this.calendarData = this.agGrid?.api ? this.aGGridService.getRowData(this.agGrid.api) : this.reportData;

    this.selectedView = view;
  }

  async checkViewHashId() {
    const colsToShow = this.reportInfo.dataStoreViewName ? this.datastore.dataViews.find(view => view.viewId === this.reportInfo.dataStoreViewName)?.columnsToShow : 'none';

    const newHashId = colsToShow === 'none' ? 'none' : await this.cryptoService.hashSHA256(colsToShow?.sort()?.toString());
    if (this.reportInfo.calendarViewConfig.viewHashId === newHashId) {
      return;
    }

    this.notificationService.showWarning('The DataView associated with this grid has been editted. Errors may be present with Calendar and Grid views. Please contact your administrator if any errors are encountered.');
  }

  onCalendarDataUpdated(params: { prevData: any, newData: any }) {
    if (!params.prevData || !params.prevData[DB_PRIMARY_KEY] || !this.agGrid.api.getRowNode(params.prevData[DB_PRIMARY_KEY])) {
      this.notificationService.showError('Error', 'Record not found! Please refresh the data and try again.');
      console.error(`Record with the given ID:${params.prevData ? params.prevData[DB_PRIMARY_KEY] : 'undefined'} not found to update!`);
      return;
    }

    this.changeHistoryService.trackGroupUpdate([new GroupUpdateInfo(params.prevData[DB_PRIMARY_KEY], params.prevData, params.newData)]);
    this.agGrid.api.applyTransaction({ update: [params.newData] });
    this.agGrid.api.refreshClientSideRowModel('group');
  }

  setCalendarOptions() {
    const config = this.reportInfo?.calendarViewConfig;
    if (!config) return;

    const columns = {
      startDate: this.getColumnConfig(config.startDateCol),
      endDate: this.getColumnConfig(config.endDateCol),
      resourceId: this.getColumnConfig(config.resourceIdCol),
      resourceTitle: this.getColumnConfig(config.resourceTitleCol)
    };

    this.calendarOptions = new SBCalendarOptions({
      baseSchemaProps: this.datastore.baseSchema.properties,
      calendarViewConfig: config,
      isPreview: this.isPreview,
      showTime: columns.startDate?.format?.type === EditorColumnType.DateTime && columns.endDate?.format?.type === EditorColumnType.DateTime,
      startEditable: columns.startDate?.allowUserUpdate ?? false,
      startType: [EditorColumnType.DateTime, EditorColumnType.Date].includes(columns.startDate?.format?.type) ? columns.startDate?.format?.type : undefined, // If the type is something like link, we will follow the datastore
      endType: [EditorColumnType.DateTime, EditorColumnType.Date].includes(columns.endDate?.format?.type) ? columns.endDate?.format?.type : undefined, // If the type is something like link, we will follow the datastore
      durationEditable: (config.endDateCol && columns.endDate?.allowUserUpdate) ?? false,
      resourceEditable: (config.resourceIdCol && columns.resourceId?.allowUserUpdate) ?? false,
      resourceTitleEditable: (config.resourceTitleCol && columns.resourceTitle?.allowUserUpdate) ?? false,
    });
  }

  private getColumnConfig(columnName: string): any {
    return this.reportInfo?.formatConfig?.actionableGridColumnsConfig?.find(x => x.column === columnName);
  }

  onHideAgentChat() {
    this.showAgentChatSidebar = false;
    this.onRefreshGridData();
  }

  onShowAgentChat() {
    this.showAgentChatSidebar = true;
    setTimeout(() => {
      this.agentChat.scrollToBottom();
    }, 0);
  }
  
  onColumnsOptionsChange(columnsOptions: boolean) {
    this.gridOptionsService.toggleColumnsOptions(this.agGrid.api, columnsOptions);
  }
}
