{"ast": null, "code": "const select = {\n  color: {\n    value: '{components.fieldcontrol.color}'\n  },\n  backgroundColor: {\n    value: '{colors.background.primary.value}'\n  },\n  paddingInlineEnd: {\n    value: '{space.xxl.value}'\n  },\n  _disabled: {\n    color: {\n      value: '{colors.font.disabled.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.disabled.value}'\n    }\n  },\n  // wrappers\n  wrapper: {\n    flex: {\n      value: '1'\n    },\n    display: {\n      value: 'block'\n    },\n    position: {\n      value: 'relative'\n    },\n    cursor: {\n      value: 'pointer'\n    }\n  },\n  iconWrapper: {\n    alignItems: {\n      value: 'center'\n    },\n    position: {\n      value: 'absolute'\n    },\n    top: {\n      value: '50%'\n    },\n    right: {\n      value: '{space.medium.value}'\n    },\n    transform: {\n      value: 'translateY(-50%)'\n    },\n    pointerEvents: {\n      value: 'none'\n    },\n    small: {\n      right: {\n        value: '{space.xs.value}'\n      }\n    },\n    large: {\n      right: {\n        value: '{space.medium.value}'\n      }\n    }\n  },\n  // It's important to test these option values on Chrome/FireFox/Edge\n  // on Windows because they allow styling of the option element.\n  // Chrome/Safari/Firefox on Mac uses the system ui.\n  option: {\n    backgroundColor: {\n      value: 'transparent'\n    },\n    color: {\n      value: '{colors.font.primary.value}'\n    },\n    _disabled: {\n      color: {\n        value: '{colors.font.disabled.value}'\n      },\n      backgroundColor: {\n        value: 'transparent'\n      }\n    }\n  },\n  whiteSpace: {\n    value: 'nowrap'\n  },\n  minWidth: {\n    value: '6.5rem'\n  },\n  small: {\n    minWidth: {\n      value: '5.5rem'\n    },\n    paddingInlineEnd: {\n      value: '{space.xl.value}'\n    }\n  },\n  large: {\n    minWidth: {\n      value: '7.5rem'\n    },\n    paddingInlineEnd: {\n      value: '{space.xxl.value}'\n    }\n  },\n  expanded: {\n    paddingBlock: {\n      value: '{space.xs.value}'\n    },\n    paddingInline: {\n      value: '{space.small.value}'\n    },\n    option: {\n      paddingBlock: {\n        value: '{space.xs.value}'\n      },\n      paddingInline: {\n        value: '{space.small.value}'\n      }\n    }\n  }\n};\nexport { select };", "map": {"version": 3, "names": ["select", "color", "value", "backgroundColor", "paddingInlineEnd", "_disabled", "wrapper", "flex", "display", "position", "cursor", "iconWrapper", "alignItems", "top", "right", "transform", "pointerEvents", "small", "large", "option", "whiteSpace", "min<PERSON><PERSON><PERSON>", "expanded", "paddingBlock", "paddingInline"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/select.mjs"], "sourcesContent": ["const select = {\n    color: { value: '{components.fieldcontrol.color}' },\n    backgroundColor: { value: '{colors.background.primary.value}' },\n    paddingInlineEnd: { value: '{space.xxl.value}' },\n    _disabled: {\n        color: { value: '{colors.font.disabled.value}' },\n        backgroundColor: { value: '{colors.background.disabled.value}' },\n    },\n    // wrappers\n    wrapper: {\n        flex: { value: '1' },\n        display: { value: 'block' },\n        position: { value: 'relative' },\n        cursor: { value: 'pointer' },\n    },\n    iconWrapper: {\n        alignItems: { value: 'center' },\n        position: { value: 'absolute' },\n        top: { value: '50%' },\n        right: { value: '{space.medium.value}' },\n        transform: { value: 'translateY(-50%)' },\n        pointerEvents: { value: 'none' },\n        small: {\n            right: { value: '{space.xs.value}' },\n        },\n        large: {\n            right: { value: '{space.medium.value}' },\n        },\n    },\n    // It's important to test these option values on Chrome/FireFox/Edge\n    // on Windows because they allow styling of the option element.\n    // Chrome/Safari/Firefox on Mac uses the system ui.\n    option: {\n        backgroundColor: { value: 'transparent' },\n        color: { value: '{colors.font.primary.value}' },\n        _disabled: {\n            color: { value: '{colors.font.disabled.value}' },\n            backgroundColor: {\n                value: 'transparent',\n            },\n        },\n    },\n    whiteSpace: { value: 'nowrap' },\n    minWidth: { value: '6.5rem' },\n    small: {\n        minWidth: { value: '5.5rem' },\n        paddingInlineEnd: { value: '{space.xl.value}' },\n    },\n    large: {\n        minWidth: { value: '7.5rem' },\n        paddingInlineEnd: { value: '{space.xxl.value}' },\n    },\n    expanded: {\n        paddingBlock: { value: '{space.xs.value}' },\n        paddingInline: { value: '{space.small.value}' },\n        option: {\n            paddingBlock: { value: '{space.xs.value}' },\n            paddingInline: { value: '{space.small.value}' },\n        },\n    },\n};\n\nexport { select };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACXC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAkC,CAAC;EACnDC,eAAe,EAAE;IAAED,KAAK,EAAE;EAAoC,CAAC;EAC/DE,gBAAgB,EAAE;IAAEF,KAAK,EAAE;EAAoB,CAAC;EAChDG,SAAS,EAAE;IACPJ,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA+B,CAAC;IAChDC,eAAe,EAAE;MAAED,KAAK,EAAE;IAAqC;EACnE,CAAC;EACD;EACAI,OAAO,EAAE;IACLC,IAAI,EAAE;MAAEL,KAAK,EAAE;IAAI,CAAC;IACpBM,OAAO,EAAE;MAAEN,KAAK,EAAE;IAAQ,CAAC;IAC3BO,QAAQ,EAAE;MAAEP,KAAK,EAAE;IAAW,CAAC;IAC/BQ,MAAM,EAAE;MAA<PERSON>,KAAK,EAAE;IAAU;EAC/B,CAAC;EACDS,WAAW,EAAE;IACTC,UAAU,EAAE;MAAEV,KAAK,EAAE;IAAS,CAAC;IAC/BO,QAAQ,EAAE;MAAEP,KAAK,EAAE;IAAW,CAAC;IAC/BW,GAAG,EAAE;MAAEX,KAAK,EAAE;IAAM,CAAC;IACrBY,KAAK,EAAE;MAAEZ,KAAK,EAAE;IAAuB,CAAC;IACxCa,SAAS,EAAE;MAAEb,KAAK,EAAE;IAAmB,CAAC;IACxCc,aAAa,EAAE;MAAEd,KAAK,EAAE;IAAO,CAAC;IAChCe,KAAK,EAAE;MACHH,KAAK,EAAE;QAAEZ,KAAK,EAAE;MAAmB;IACvC,CAAC;IACDgB,KAAK,EAAE;MACHJ,KAAK,EAAE;QAAEZ,KAAK,EAAE;MAAuB;IAC3C;EACJ,CAAC;EACD;EACA;EACA;EACAiB,MAAM,EAAE;IACJhB,eAAe,EAAE;MAAED,KAAK,EAAE;IAAc,CAAC;IACzCD,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA8B,CAAC;IAC/CG,SAAS,EAAE;MACPJ,KAAK,EAAE;QAAEC,KAAK,EAAE;MAA+B,CAAC;MAChDC,eAAe,EAAE;QACbD,KAAK,EAAE;MACX;IACJ;EACJ,CAAC;EACDkB,UAAU,EAAE;IAAElB,KAAK,EAAE;EAAS,CAAC;EAC/BmB,QAAQ,EAAE;IAAEnB,KAAK,EAAE;EAAS,CAAC;EAC7Be,KAAK,EAAE;IACHI,QAAQ,EAAE;MAAEnB,KAAK,EAAE;IAAS,CAAC;IAC7BE,gBAAgB,EAAE;MAAEF,KAAK,EAAE;IAAmB;EAClD,CAAC;EACDgB,KAAK,EAAE;IACHG,QAAQ,EAAE;MAAEnB,KAAK,EAAE;IAAS,CAAC;IAC7BE,gBAAgB,EAAE;MAAEF,KAAK,EAAE;IAAoB;EACnD,CAAC;EACDoB,QAAQ,EAAE;IACNC,YAAY,EAAE;MAAErB,KAAK,EAAE;IAAmB,CAAC;IAC3CsB,aAAa,EAAE;MAAEtB,KAAK,EAAE;IAAsB,CAAC;IAC/CiB,MAAM,EAAE;MACJI,YAAY,EAAE;QAAErB,KAAK,EAAE;MAAmB,CAAC;MAC3CsB,aAAa,EAAE;QAAEtB,KAAK,EAAE;MAAsB;IAClD;EACJ;AACJ,CAAC;AAED,SAASF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}