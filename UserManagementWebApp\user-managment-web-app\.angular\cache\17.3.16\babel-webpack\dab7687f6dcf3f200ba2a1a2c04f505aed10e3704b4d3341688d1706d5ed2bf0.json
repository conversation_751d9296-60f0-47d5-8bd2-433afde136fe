{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst AmplifyUrl = URL;\nconst AmplifyUrlSearchParams = URLSearchParams;\nexport { AmplifyUrl, AmplifyUrlSearchParams };", "map": {"version": 3, "names": ["AmplifyUrl", "URL", "AmplifyUrlSearchParams", "URLSearchParams"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/amplifyUrl/index.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst AmplifyUrl = URL;\nconst AmplifyUrlSearchParams = URLSearchParams;\n\nexport { AmplifyUrl, AmplifyUrlSearchParams };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,UAAU,GAAGC,GAAG;AACtB,MAAMC,sBAAsB,GAAGC,eAAe;AAE9C,SAASH,UAAU,EAAEE,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}