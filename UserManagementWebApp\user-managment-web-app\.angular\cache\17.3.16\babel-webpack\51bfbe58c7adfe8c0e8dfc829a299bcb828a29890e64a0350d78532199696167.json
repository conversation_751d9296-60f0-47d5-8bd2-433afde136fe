{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns a `Date` that is corrected for clock skew.\n *\n * @param systemClockOffset The offset of the system clock in milliseconds.\n *\n * @returns `Date` representing the current time adjusted by the system clock offset.\n *\n * @internal\n */\nconst getSkewCorrectedDate = systemClockOffset => new Date(Date.now() + systemClockOffset);\nexport { getSkewCorrectedDate };", "map": {"version": 3, "names": ["getSkewCorrectedDate", "systemClockOffset", "Date", "now"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/utils/getSkewCorrectedDate.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns a `Date` that is corrected for clock skew.\n *\n * @param systemClockOffset The offset of the system clock in milliseconds.\n *\n * @returns `Date` representing the current time adjusted by the system clock offset.\n *\n * @internal\n */\nconst getSkewCorrectedDate = (systemClockOffset) => new Date(Date.now() + systemClockOffset);\n\nexport { getSkewCorrectedDate };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,oBAAoB,GAAIC,iBAAiB,IAAK,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,iBAAiB,CAAC;AAE5F,SAASD,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}