{"ast": null, "code": "export const fromUtf8 = input => new TextEncoder().encode(input);", "map": {"version": 3, "names": ["fromUtf8", "input", "TextEncoder", "encode"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@smithy/util-utf8/dist-es/fromUtf8.browser.js"], "sourcesContent": ["export const fromUtf8 = (input) => new TextEncoder().encode(input);\n"], "mappings": "AAAA,OAAO,MAAMA,QAAQ,GAAIC,KAAK,IAAK,IAAIC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACF,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}