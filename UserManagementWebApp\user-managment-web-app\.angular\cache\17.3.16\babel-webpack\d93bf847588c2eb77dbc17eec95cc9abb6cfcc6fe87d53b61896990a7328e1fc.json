{"ast": null, "code": "import { retryMiddlewareFactory } from '../../middleware/retry/retryMiddleware.mjs';\nimport '../../../types/errors.mjs';\nimport '../../../errors/errorHelpers.mjs';\nimport '../../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../../utils/retry/retry.mjs';\nimport { amzSdkInvocationIdHeaderMiddlewareFactory } from '../../middleware/retry/amzSdkInvocationIdHeaderMiddleware.mjs';\nimport { amzSdkRequestHeaderMiddlewareFactory } from '../../middleware/retry/amzSdkRequestHeaderMiddleware.mjs';\nimport { signingMiddlewareFactory } from '../../middleware/signing/middleware.mjs';\nimport { userAgentMiddlewareFactory } from '../../middleware/userAgent/middleware.mjs';\nimport { composeTransferHandler } from '../../internal/composeTransferHandler.mjs';\nimport { fetchTransferHandler } from '../fetch.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst authenticatedHandler = composeTransferHandler(fetchTransferHandler, [userAgentMiddlewareFactory, amzSdkInvocationIdHeaderMiddlewareFactory, retryMiddlewareFactory, amzSdkRequestHeaderMiddlewareFactory, signingMiddlewareFactory]);\nexport { authenticatedHandler };", "map": {"version": 3, "names": ["retryMiddlewareFactory", "amzSdkInvocationIdHeaderMiddlewareFactory", "amzSdkRequestHeaderMiddlewareFactory", "signingMiddlewareFactory", "userAgentMiddlewareFactory", "composeTransferHandler", "fetchTransferHandler", "authenticated<PERSON><PERSON><PERSON>"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/handlers/aws/authenticated.mjs"], "sourcesContent": ["import { retryMiddlewareFactory } from '../../middleware/retry/retryMiddleware.mjs';\nimport '../../../types/errors.mjs';\nimport '../../../errors/errorHelpers.mjs';\nimport '../../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../../utils/retry/retry.mjs';\nimport { amzSdkInvocationIdHeaderMiddlewareFactory } from '../../middleware/retry/amzSdkInvocationIdHeaderMiddleware.mjs';\nimport { amzSdkRequestHeaderMiddlewareFactory } from '../../middleware/retry/amzSdkRequestHeaderMiddleware.mjs';\nimport { signingMiddlewareFactory } from '../../middleware/signing/middleware.mjs';\nimport { userAgentMiddlewareFactory } from '../../middleware/userAgent/middleware.mjs';\nimport { composeTransferHandler } from '../../internal/composeTransferHandler.mjs';\nimport { fetchTransferHandler } from '../fetch.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst authenticatedHandler = composeTransferHandler(fetchTransferHandler, [\n    userAgentMiddlewareFactory,\n    amzSdkInvocationIdHeaderMiddlewareFactory,\n    retryMiddlewareFactory,\n    amzSdkRequestHeaderMiddlewareFactory,\n    signingMiddlewareFactory,\n]);\n\nexport { authenticatedHandler };\n"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,4CAA4C;AACnF,OAAO,2BAA2B;AAClC,OAAO,kCAAkC;AACzC,OAAO,gDAAgD;AACvD,OAAO,gCAAgC;AACvC,SAASC,yCAAyC,QAAQ,+DAA+D;AACzH,SAASC,oCAAoC,QAAQ,0DAA0D;AAC/G,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,0BAA0B,QAAQ,2CAA2C;AACtF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,oBAAoB,QAAQ,cAAc;;AAEnD;AACA;AACA,MAAMC,oBAAoB,GAAGF,sBAAsB,CAACC,oBAAoB,EAAE,CACtEF,0BAA0B,EAC1BH,yCAAyC,EACzCD,sBAAsB,EACtBE,oCAAoC,EACpCC,wBAAwB,CAC3B,CAAC;AAEF,SAASI,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}