{"ast": null, "code": "// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nexport function numToUint8(num) {\n  return new Uint8Array([(num & 0xff000000) >> 24, (num & 0x00ff0000) >> 16, (num & 0x0000ff00) >> 8, num & 0x000000ff]);\n}", "map": {"version": 3, "names": ["numToUint8", "num", "Uint8Array"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-crypto/util/build/module/numToUint8.js"], "sourcesContent": ["// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nexport function numToUint8(num) {\n    return new Uint8Array([\n        (num & 0xff000000) >> 24,\n        (num & 0x00ff0000) >> 16,\n        (num & 0x0000ff00) >> 8,\n        num & 0x000000ff,\n    ]);\n}\n"], "mappings": "AAAA;AACA;AACA,OAAO,SAASA,UAAUA,CAACC,GAAG,EAAE;EAC5B,OAAO,IAAIC,UAAU,CAAC,CAClB,CAACD,GAAG,GAAG,UAAU,KAAK,EAAE,EACxB,CAACA,GAAG,GAAG,UAAU,KAAK,EAAE,EACxB,CAACA,GAAG,GAAG,UAAU,KAAK,CAAC,EACvBA,GAAG,GAAG,UAAU,CACnB,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}