{"ast": null, "code": "import { WordArray } from '@aws-amplify/core/internals/utils';\nimport { getBytesFromHex } from './getBytesFromHex.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns a Uint8Array with a sequence of random nBytes\n *\n * @param {number} nBytes\n * @returns {Uint8Array} fixed-length sequence of random bytes\n */\nconst getRandomBytes = nBytes => {\n  const str = new WordArray().random(nBytes).toString();\n  return getBytesFromHex(str);\n};\nexport { getRandomBytes };", "map": {"version": 3, "names": ["WordArray", "getBytesFromHex", "getRandomBytes", "nBytes", "str", "random", "toString"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/srp/getRandomBytes.mjs"], "sourcesContent": ["import { WordArray } from '@aws-amplify/core/internals/utils';\nimport { getBytesFromHex } from './getBytesFromHex.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns a Uint8Array with a sequence of random nBytes\n *\n * @param {number} nBytes\n * @returns {Uint8Array} fixed-length sequence of random bytes\n */\nconst getRandomBytes = (nBytes) => {\n    const str = new WordArray().random(nBytes).toString();\n    return getBytesFromHex(str);\n};\n\nexport { getRandomBytes };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,mCAAmC;AAC7D,SAASC,eAAe,QAAQ,uBAAuB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAIC,MAAM,IAAK;EAC/B,MAAMC,GAAG,GAAG,IAAIJ,SAAS,CAAC,CAAC,CAACK,MAAM,CAACF,MAAM,CAAC,CAACG,QAAQ,CAAC,CAAC;EACrD,OAAOL,eAAe,CAACG,GAAG,CAAC;AAC/B,CAAC;AAED,SAASF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}