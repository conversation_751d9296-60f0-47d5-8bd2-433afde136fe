{"ast": null, "code": "import { actions } from 'xstate';\nimport '@aws-amplify/core/internals/utils';\nimport 'aws-amplify/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport '../../types/authenticator/user.mjs';\nimport '../../types/authenticator/attributes.mjs';\nimport { trimValues } from '../../helpers/authenticator/utils.mjs';\nimport '../../helpers/accountSettings/utils.mjs';\nimport 'aws-amplify';\nimport { sanitizePhoneNumber, getUsernameSignUp } from './utils.mjs';\nconst {\n  assign\n} = actions;\nconst clearActorDoneData = assign({\n  actorDoneData: undefined\n});\nconst clearChallengeName = assign({\n  challengeName: undefined\n});\nconst clearMissingAttributes = assign({\n  missingAttributes: undefined\n});\nconst clearError = assign({\n  remoteError: undefined\n});\nconst clearFormValues = assign({\n  formValues: {}\n});\nconst clearTouched = assign({\n  touched: {}\n});\nconst clearUser = assign({\n  user: undefined\n});\nconst clearValidationError = assign({\n  validationError: {}\n});\n/**\n * \"set\" actions\n */\nconst setTotpSecretCode = assign({\n  totpSecretCode: (_, {\n    data\n  }) => {\n    const {\n      sharedSecret\n    } = data.nextStep?.totpSetupDetails ?? {};\n    return sharedSecret;\n  }\n});\nconst setAllowedMfaTypes = assign({\n  allowedMfaTypes: (_, {\n    data\n  }) => {\n    return data.nextStep?.allowedMFATypes;\n  }\n});\nconst setSignInStep = assign({\n  step: 'SIGN_IN'\n});\nconst setShouldVerifyUserAttributeStep = assign({\n  step: 'SHOULD_CONFIRM_USER_ATTRIBUTE'\n});\nconst setConfirmAttributeCompleteStep = assign({\n  step: 'CONFIRM_ATTRIBUTE_COMPLETE'\n});\n// map v6 `signInStep` to v5 `challengeName`\nconst setChallengeName = assign({\n  challengeName: (_, {\n    data\n  }) => {\n    const {\n      signInStep\n    } = data.nextStep;\n    switch (signInStep) {\n      case 'CONFIRM_SIGN_IN_WITH_SMS_CODE':\n        return 'SMS_MFA';\n      case 'CONFIRM_SIGN_IN_WITH_TOTP_CODE':\n        return 'SOFTWARE_TOKEN_MFA';\n      case 'CONFIRM_SIGN_IN_WITH_EMAIL_CODE':\n        return 'EMAIL_OTP';\n      case 'CONTINUE_SIGN_IN_WITH_MFA_SETUP_SELECTION':\n      case 'CONTINUE_SIGN_IN_WITH_EMAIL_SETUP':\n      case 'CONTINUE_SIGN_IN_WITH_TOTP_SETUP':\n        return 'MFA_SETUP';\n      case 'CONTINUE_SIGN_IN_WITH_MFA_SELECTION':\n        return 'SELECT_MFA_TYPE';\n      default:\n        return undefined;\n    }\n  }\n});\nconst setUsernameForgotPassword = assign({\n  username: ({\n    formValues,\n    loginMechanisms\n  }) => {\n    const loginMechanism = loginMechanisms[0];\n    const {\n      username,\n      country_code\n    } = formValues;\n    if (loginMechanism === 'phone_number') {\n      // forgot password `formValues` uses `username` for base phone number value\n      // prefix `country_code` for full `username`\n      return sanitizePhoneNumber(country_code, username);\n    }\n    // default username field for loginMechanism === 'email' is \"username\" for SignIn\n    return username;\n  }\n});\nconst setUsernameSignUp = assign({\n  username: getUsernameSignUp\n});\nconst setUsernameSignIn = assign({\n  username: ({\n    formValues,\n    loginMechanisms\n  }) => {\n    const loginMechanism = loginMechanisms[0];\n    const {\n      username,\n      country_code\n    } = formValues;\n    if (loginMechanism === 'phone_number') {\n      // sign in `formValues` uses `username` for base phone number value\n      // prefix `country_code` for full `username`\n      return sanitizePhoneNumber(country_code, username);\n    }\n    // return `email` and `username`\n    return username;\n  }\n});\nconst setNextSignInStep = assign({\n  step: (_, {\n    data\n  }) => data.nextStep.signInStep === 'DONE' ? 'SIGN_IN_COMPLETE' : data.nextStep.signInStep\n});\nconst setNextSignUpStep = assign({\n  step: (_, {\n    data\n  }) => data.nextStep.signUpStep === 'DONE' ? 'SIGN_UP_COMPLETE' : data.nextStep.signUpStep\n});\nconst setNextResetPasswordStep = assign({\n  step: (_, {\n    data\n  }) => data.nextStep.resetPasswordStep === 'DONE' ? 'RESET_PASSWORD_COMPLETE' : data.nextStep.resetPasswordStep\n});\nconst setMissingAttributes = assign({\n  missingAttributes: (_, {\n    data\n  }) => data.nextStep?.missingAttributes\n});\nconst setFieldErrors = assign({\n  validationError: (_, {\n    data\n  }) => data\n});\nconst setRemoteError = assign({\n  remoteError: (_, {\n    data\n  }) => {\n    if (data.name === 'NoUserPoolError') {\n      return `Configuration error (see console) – please contact the administrator`;\n    }\n    return data?.message || data;\n  }\n});\nconst setUser = assign({\n  user: (_, {\n    data\n  }) => data\n});\nconst resolveCodeDeliveryDetails = details => ({\n  Destination: details.destination,\n  DeliveryMedium: details.deliveryMedium,\n  AttributeName: details.attributName\n});\nconst setCodeDeliveryDetails = assign({\n  codeDeliveryDetails: (_, {\n    data\n  }) => {\n    if (data?.nextStep?.codeDeliveryDetails) {\n      return resolveCodeDeliveryDetails(data.nextStep.codeDeliveryDetails);\n    }\n    return resolveCodeDeliveryDetails(data);\n  }\n});\nconst handleInput = assign({\n  formValues: (context, {\n    data\n  }) => {\n    const {\n      name,\n      value\n    } = data;\n    return {\n      ...context['formValues'],\n      [name]: value\n    };\n  }\n});\nconst handleSubmit = assign({\n  formValues: (context, {\n    data\n  }) =>\n  // do not trim password\n  trimValues({\n    ...context['formValues'],\n    ...data\n  }, 'password')\n});\nconst handleBlur = assign({\n  touched: (context, {\n    data\n  }) => ({\n    ...context['touched'],\n    [data.name]: true\n  })\n});\nconst setUnverifiedUserAttributes = assign({\n  unverifiedUserAttributes: (_, {\n    data\n  }) => {\n    const {\n      email,\n      phone_number\n    } = data;\n    const unverifiedUserAttributes = {\n      ...(email && {\n        email\n      }),\n      ...(phone_number && {\n        phone_number\n      })\n    };\n    return unverifiedUserAttributes;\n  }\n});\nconst clearSelectedUserAttribute = assign({\n  selectedUserAttribute: undefined\n});\nconst setSelectedUserAttribute = assign({\n  selectedUserAttribute: context => context.formValues?.unverifiedAttr\n});\n// Maps to unexposed `ConfirmSignUpSignUpStep`\nconst setConfirmSignUpSignUpStep = assign({\n  step: 'CONFIRM_SIGN_UP'\n});\nconst ACTIONS = {\n  clearActorDoneData,\n  clearChallengeName,\n  clearError,\n  clearFormValues,\n  clearMissingAttributes,\n  clearSelectedUserAttribute,\n  clearTouched,\n  clearUser,\n  clearValidationError,\n  handleBlur,\n  handleInput,\n  handleSubmit,\n  setAllowedMfaTypes,\n  setChallengeName,\n  setCodeDeliveryDetails,\n  setFieldErrors,\n  setMissingAttributes,\n  setNextResetPasswordStep,\n  setNextSignInStep,\n  setNextSignUpStep,\n  setRemoteError,\n  setConfirmAttributeCompleteStep,\n  setConfirmSignUpSignUpStep,\n  setShouldVerifyUserAttributeStep,\n  setSelectedUserAttribute,\n  setSignInStep,\n  setTotpSecretCode,\n  setUser,\n  setUnverifiedUserAttributes,\n  setUsernameForgotPassword,\n  setUsernameSignIn,\n  setUsernameSignUp\n};\nexport { ACTIONS as default };", "map": {"version": 3, "names": ["actions", "trimValues", "sanitizePhoneNumber", "getUsernameSignUp", "assign", "clearActorDoneData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "undefined", "clearChallengeName", "challenge<PERSON>ame", "clearMissingAttributes", "missingAttributes", "clearError", "remoteError", "clearFormValues", "formValues", "clearTouched", "touched", "clearUser", "user", "clearValidationError", "validationError", "setTotpSecretCode", "totpSecretCode", "_", "data", "sharedSecret", "nextStep", "totpSetupDetails", "setAllowedMfaTypes", "allowedMfaTypes", "allowedMFATypes", "setSignInStep", "step", "setShouldVerifyUserAttributeStep", "setConfirmAttributeCompleteStep", "setChallengeName", "signInStep", "setUsernameForgotPassword", "username", "loginMechanisms", "loginMechanism", "country_code", "setUsernameSignUp", "setUsernameSignIn", "setNextSignInStep", "setNextSignUpStep", "signUpStep", "setNextResetPasswordStep", "resetPasswordStep", "setMissingAttributes", "setFieldErrors", "setRemoteError", "name", "message", "setUser", "resolveCodeDeliveryDetails", "details", "Destination", "destination", "DeliveryMedium", "deliveryMedium", "AttributeName", "attributName", "setCodeDeliveryDetails", "codeDeliveryDetails", "handleInput", "context", "value", "handleSubmit", "handleBlur", "setUnverifiedUserAttributes", "unverifiedUserAttributes", "email", "phone_number", "clearSelectedUserAttribute", "selectedUserAttribute", "setSelectedUserAttribute", "unverifiedAttr", "setConfirmSignUpSignUpStep", "ACTIONS", "default"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/machines/authenticator/actions.mjs"], "sourcesContent": ["import { actions } from 'xstate';\nimport '@aws-amplify/core/internals/utils';\nimport 'aws-amplify/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport '../../types/authenticator/user.mjs';\nimport '../../types/authenticator/attributes.mjs';\nimport { trimValues } from '../../helpers/authenticator/utils.mjs';\nimport '../../helpers/accountSettings/utils.mjs';\nimport 'aws-amplify';\nimport { sanitizePhoneNumber, getUsernameSignUp } from './utils.mjs';\n\nconst { assign } = actions;\nconst clearActorDoneData = assign({ actorDoneData: undefined });\nconst clearChallengeName = assign({ challengeName: undefined });\nconst clearMissingAttributes = assign({ missingAttributes: undefined });\nconst clearError = assign({ remoteError: undefined });\nconst clearFormValues = assign({ formValues: {} });\nconst clearTouched = assign({ touched: {} });\nconst clearUser = assign({ user: undefined });\nconst clearValidationError = assign({ validationError: {} });\n/**\n * \"set\" actions\n */\nconst setTotpSecretCode = assign({\n    totpSecretCode: (_, { data }) => {\n        const { sharedSecret } = (data.nextStep?.totpSetupDetails ??\n            {});\n        return sharedSecret;\n    },\n});\nconst setAllowedMfaTypes = assign({\n    allowedMfaTypes: (_, { data }) => {\n        return data.nextStep?.allowedMFATypes;\n    },\n});\nconst setSignInStep = assign({ step: 'SIGN_IN' });\nconst setShouldVerifyUserAttributeStep = assign({\n    step: 'SHOULD_CONFIRM_USER_ATTRIBUTE',\n});\nconst setConfirmAttributeCompleteStep = assign({\n    step: 'CONFIRM_ATTRIBUTE_COMPLETE',\n});\n// map v6 `signInStep` to v5 `challengeName`\nconst setChallengeName = assign({\n    challengeName: (_, { data }) => {\n        const { signInStep } = data.nextStep;\n        switch (signInStep) {\n            case 'CONFIRM_SIGN_IN_WITH_SMS_CODE':\n                return 'SMS_MFA';\n            case 'CONFIRM_SIGN_IN_WITH_TOTP_CODE':\n                return 'SOFTWARE_TOKEN_MFA';\n            case 'CONFIRM_SIGN_IN_WITH_EMAIL_CODE':\n                return 'EMAIL_OTP';\n            case 'CONTINUE_SIGN_IN_WITH_MFA_SETUP_SELECTION':\n            case 'CONTINUE_SIGN_IN_WITH_EMAIL_SETUP':\n            case 'CONTINUE_SIGN_IN_WITH_TOTP_SETUP':\n                return 'MFA_SETUP';\n            case 'CONTINUE_SIGN_IN_WITH_MFA_SELECTION':\n                return 'SELECT_MFA_TYPE';\n            default:\n                return undefined;\n        }\n    },\n});\nconst setUsernameForgotPassword = assign({\n    username: ({ formValues, loginMechanisms }) => {\n        const loginMechanism = loginMechanisms[0];\n        const { username, country_code } = formValues;\n        if (loginMechanism === 'phone_number') {\n            // forgot password `formValues` uses `username` for base phone number value\n            // prefix `country_code` for full `username`\n            return sanitizePhoneNumber(country_code, username);\n        }\n        // default username field for loginMechanism === 'email' is \"username\" for SignIn\n        return username;\n    },\n});\nconst setUsernameSignUp = assign({ username: getUsernameSignUp });\nconst setUsernameSignIn = assign({\n    username: ({ formValues, loginMechanisms }) => {\n        const loginMechanism = loginMechanisms[0];\n        const { username, country_code } = formValues;\n        if (loginMechanism === 'phone_number') {\n            // sign in `formValues` uses `username` for base phone number value\n            // prefix `country_code` for full `username`\n            return sanitizePhoneNumber(country_code, username);\n        }\n        // return `email` and `username`\n        return username;\n    },\n});\nconst setNextSignInStep = assign({\n    step: (_, { data }) => data.nextStep.signInStep === 'DONE'\n        ? 'SIGN_IN_COMPLETE'\n        : data.nextStep.signInStep,\n});\nconst setNextSignUpStep = assign({\n    step: (_, { data }) => data.nextStep.signUpStep === 'DONE'\n        ? 'SIGN_UP_COMPLETE'\n        : data.nextStep.signUpStep,\n});\nconst setNextResetPasswordStep = assign({\n    step: (_, { data }) => data.nextStep.resetPasswordStep === 'DONE'\n        ? 'RESET_PASSWORD_COMPLETE'\n        : data.nextStep.resetPasswordStep,\n});\nconst setMissingAttributes = assign({\n    missingAttributes: (_, { data }) => data.nextStep?.missingAttributes,\n});\nconst setFieldErrors = assign({\n    validationError: (_, { data }) => data,\n});\nconst setRemoteError = assign({\n    remoteError: (_, { data }) => {\n        if (data.name === 'NoUserPoolError') {\n            return `Configuration error (see console) – please contact the administrator`;\n        }\n        return data?.message || data;\n    },\n});\nconst setUser = assign({ user: (_, { data }) => data });\nconst resolveCodeDeliveryDetails = (details) => ({\n    Destination: details.destination,\n    DeliveryMedium: details.deliveryMedium,\n    AttributeName: details.attributName,\n});\nconst setCodeDeliveryDetails = assign({\n    codeDeliveryDetails: (_, { data }) => {\n        if (data\n            ?.nextStep?.codeDeliveryDetails) {\n            return resolveCodeDeliveryDetails(data\n                .nextStep.codeDeliveryDetails);\n        }\n        return resolveCodeDeliveryDetails(data);\n    },\n});\nconst handleInput = assign({\n    formValues: (context, { data }) => {\n        const { name, value } = data;\n        return { ...context['formValues'], [name]: value };\n    },\n});\nconst handleSubmit = assign({\n    formValues: (context, { data }) => \n    // do not trim password\n    trimValues({ ...context['formValues'], ...data }, 'password'),\n});\nconst handleBlur = assign({\n    touched: (context, { data }) => ({\n        ...context['touched'],\n        [data.name]: true,\n    }),\n});\nconst setUnverifiedUserAttributes = assign({\n    unverifiedUserAttributes: (_, { data }) => {\n        const { email, phone_number } = data;\n        const unverifiedUserAttributes = {\n            ...(email && { email }),\n            ...(phone_number && { phone_number }),\n        };\n        return unverifiedUserAttributes;\n    },\n});\nconst clearSelectedUserAttribute = assign({ selectedUserAttribute: undefined });\nconst setSelectedUserAttribute = assign({\n    selectedUserAttribute: (context) => context.formValues?.unverifiedAttr,\n});\n// Maps to unexposed `ConfirmSignUpSignUpStep`\nconst setConfirmSignUpSignUpStep = assign({ step: 'CONFIRM_SIGN_UP' });\nconst ACTIONS = {\n    clearActorDoneData,\n    clearChallengeName,\n    clearError,\n    clearFormValues,\n    clearMissingAttributes,\n    clearSelectedUserAttribute,\n    clearTouched,\n    clearUser,\n    clearValidationError,\n    handleBlur,\n    handleInput,\n    handleSubmit,\n    setAllowedMfaTypes,\n    setChallengeName,\n    setCodeDeliveryDetails,\n    setFieldErrors,\n    setMissingAttributes,\n    setNextResetPasswordStep,\n    setNextSignInStep,\n    setNextSignUpStep,\n    setRemoteError,\n    setConfirmAttributeCompleteStep,\n    setConfirmSignUpSignUpStep,\n    setShouldVerifyUserAttributeStep,\n    setSelectedUserAttribute,\n    setSignInStep,\n    setTotpSecretCode,\n    setUser,\n    setUnverifiedUserAttributes,\n    setUsernameForgotPassword,\n    setUsernameSignIn,\n    setUsernameSignUp,\n};\n\nexport { ACTIONS as default };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,QAAQ;AAChC,OAAO,mCAAmC;AAC1C,OAAO,mBAAmB;AAC1B,OAAO,wCAAwC;AAC/C,OAAO,oCAAoC;AAC3C,OAAO,0CAA0C;AACjD,SAASC,UAAU,QAAQ,uCAAuC;AAClE,OAAO,yCAAyC;AAChD,OAAO,aAAa;AACpB,SAASC,mBAAmB,EAAEC,iBAAiB,QAAQ,aAAa;AAEpE,MAAM;EAAEC;AAAO,CAAC,GAAGJ,OAAO;AAC1B,MAAMK,kBAAkB,GAAGD,MAAM,CAAC;EAAEE,aAAa,EAAEC;AAAU,CAAC,CAAC;AAC/D,MAAMC,kBAAkB,GAAGJ,MAAM,CAAC;EAAEK,aAAa,EAAEF;AAAU,CAAC,CAAC;AAC/D,MAAMG,sBAAsB,GAAGN,MAAM,CAAC;EAAEO,iBAAiB,EAAEJ;AAAU,CAAC,CAAC;AACvE,MAAMK,UAAU,GAAGR,MAAM,CAAC;EAAES,WAAW,EAAEN;AAAU,CAAC,CAAC;AACrD,MAAMO,eAAe,GAAGV,MAAM,CAAC;EAAEW,UAAU,EAAE,CAAC;AAAE,CAAC,CAAC;AAClD,MAAMC,YAAY,GAAGZ,MAAM,CAAC;EAAEa,OAAO,EAAE,CAAC;AAAE,CAAC,CAAC;AAC5C,MAAMC,SAAS,GAAGd,MAAM,CAAC;EAAEe,IAAI,EAAEZ;AAAU,CAAC,CAAC;AAC7C,MAAMa,oBAAoB,GAAGhB,MAAM,CAAC;EAAEiB,eAAe,EAAE,CAAC;AAAE,CAAC,CAAC;AAC5D;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGlB,MAAM,CAAC;EAC7BmB,cAAc,EAAEA,CAACC,CAAC,EAAE;IAAEC;EAAK,CAAC,KAAK;IAC7B,MAAM;MAAEC;IAAa,CAAC,GAAID,IAAI,CAACE,QAAQ,EAAEC,gBAAgB,IACrD,CAAC,CAAE;IACP,OAAOF,YAAY;EACvB;AACJ,CAAC,CAAC;AACF,MAAMG,kBAAkB,GAAGzB,MAAM,CAAC;EAC9B0B,eAAe,EAAEA,CAACN,CAAC,EAAE;IAAEC;EAAK,CAAC,KAAK;IAC9B,OAAOA,IAAI,CAACE,QAAQ,EAAEI,eAAe;EACzC;AACJ,CAAC,CAAC;AACF,MAAMC,aAAa,GAAG5B,MAAM,CAAC;EAAE6B,IAAI,EAAE;AAAU,CAAC,CAAC;AACjD,MAAMC,gCAAgC,GAAG9B,MAAM,CAAC;EAC5C6B,IAAI,EAAE;AACV,CAAC,CAAC;AACF,MAAME,+BAA+B,GAAG/B,MAAM,CAAC;EAC3C6B,IAAI,EAAE;AACV,CAAC,CAAC;AACF;AACA,MAAMG,gBAAgB,GAAGhC,MAAM,CAAC;EAC5BK,aAAa,EAAEA,CAACe,CAAC,EAAE;IAAEC;EAAK,CAAC,KAAK;IAC5B,MAAM;MAAEY;IAAW,CAAC,GAAGZ,IAAI,CAACE,QAAQ;IACpC,QAAQU,UAAU;MACd,KAAK,+BAA+B;QAChC,OAAO,SAAS;MACpB,KAAK,gCAAgC;QACjC,OAAO,oBAAoB;MAC/B,KAAK,iCAAiC;QAClC,OAAO,WAAW;MACtB,KAAK,2CAA2C;MAChD,KAAK,mCAAmC;MACxC,KAAK,kCAAkC;QACnC,OAAO,WAAW;MACtB,KAAK,qCAAqC;QACtC,OAAO,iBAAiB;MAC5B;QACI,OAAO9B,SAAS;IACxB;EACJ;AACJ,CAAC,CAAC;AACF,MAAM+B,yBAAyB,GAAGlC,MAAM,CAAC;EACrCmC,QAAQ,EAAEA,CAAC;IAAExB,UAAU;IAAEyB;EAAgB,CAAC,KAAK;IAC3C,MAAMC,cAAc,GAAGD,eAAe,CAAC,CAAC,CAAC;IACzC,MAAM;MAAED,QAAQ;MAAEG;IAAa,CAAC,GAAG3B,UAAU;IAC7C,IAAI0B,cAAc,KAAK,cAAc,EAAE;MACnC;MACA;MACA,OAAOvC,mBAAmB,CAACwC,YAAY,EAAEH,QAAQ,CAAC;IACtD;IACA;IACA,OAAOA,QAAQ;EACnB;AACJ,CAAC,CAAC;AACF,MAAMI,iBAAiB,GAAGvC,MAAM,CAAC;EAAEmC,QAAQ,EAAEpC;AAAkB,CAAC,CAAC;AACjE,MAAMyC,iBAAiB,GAAGxC,MAAM,CAAC;EAC7BmC,QAAQ,EAAEA,CAAC;IAAExB,UAAU;IAAEyB;EAAgB,CAAC,KAAK;IAC3C,MAAMC,cAAc,GAAGD,eAAe,CAAC,CAAC,CAAC;IACzC,MAAM;MAAED,QAAQ;MAAEG;IAAa,CAAC,GAAG3B,UAAU;IAC7C,IAAI0B,cAAc,KAAK,cAAc,EAAE;MACnC;MACA;MACA,OAAOvC,mBAAmB,CAACwC,YAAY,EAAEH,QAAQ,CAAC;IACtD;IACA;IACA,OAAOA,QAAQ;EACnB;AACJ,CAAC,CAAC;AACF,MAAMM,iBAAiB,GAAGzC,MAAM,CAAC;EAC7B6B,IAAI,EAAEA,CAACT,CAAC,EAAE;IAAEC;EAAK,CAAC,KAAKA,IAAI,CAACE,QAAQ,CAACU,UAAU,KAAK,MAAM,GACpD,kBAAkB,GAClBZ,IAAI,CAACE,QAAQ,CAACU;AACxB,CAAC,CAAC;AACF,MAAMS,iBAAiB,GAAG1C,MAAM,CAAC;EAC7B6B,IAAI,EAAEA,CAACT,CAAC,EAAE;IAAEC;EAAK,CAAC,KAAKA,IAAI,CAACE,QAAQ,CAACoB,UAAU,KAAK,MAAM,GACpD,kBAAkB,GAClBtB,IAAI,CAACE,QAAQ,CAACoB;AACxB,CAAC,CAAC;AACF,MAAMC,wBAAwB,GAAG5C,MAAM,CAAC;EACpC6B,IAAI,EAAEA,CAACT,CAAC,EAAE;IAAEC;EAAK,CAAC,KAAKA,IAAI,CAACE,QAAQ,CAACsB,iBAAiB,KAAK,MAAM,GAC3D,yBAAyB,GACzBxB,IAAI,CAACE,QAAQ,CAACsB;AACxB,CAAC,CAAC;AACF,MAAMC,oBAAoB,GAAG9C,MAAM,CAAC;EAChCO,iBAAiB,EAAEA,CAACa,CAAC,EAAE;IAAEC;EAAK,CAAC,KAAKA,IAAI,CAACE,QAAQ,EAAEhB;AACvD,CAAC,CAAC;AACF,MAAMwC,cAAc,GAAG/C,MAAM,CAAC;EAC1BiB,eAAe,EAAEA,CAACG,CAAC,EAAE;IAAEC;EAAK,CAAC,KAAKA;AACtC,CAAC,CAAC;AACF,MAAM2B,cAAc,GAAGhD,MAAM,CAAC;EAC1BS,WAAW,EAAEA,CAACW,CAAC,EAAE;IAAEC;EAAK,CAAC,KAAK;IAC1B,IAAIA,IAAI,CAAC4B,IAAI,KAAK,iBAAiB,EAAE;MACjC,OAAO,sEAAsE;IACjF;IACA,OAAO5B,IAAI,EAAE6B,OAAO,IAAI7B,IAAI;EAChC;AACJ,CAAC,CAAC;AACF,MAAM8B,OAAO,GAAGnD,MAAM,CAAC;EAAEe,IAAI,EAAEA,CAACK,CAAC,EAAE;IAAEC;EAAK,CAAC,KAAKA;AAAK,CAAC,CAAC;AACvD,MAAM+B,0BAA0B,GAAIC,OAAO,KAAM;EAC7CC,WAAW,EAAED,OAAO,CAACE,WAAW;EAChCC,cAAc,EAAEH,OAAO,CAACI,cAAc;EACtCC,aAAa,EAAEL,OAAO,CAACM;AAC3B,CAAC,CAAC;AACF,MAAMC,sBAAsB,GAAG5D,MAAM,CAAC;EAClC6D,mBAAmB,EAAEA,CAACzC,CAAC,EAAE;IAAEC;EAAK,CAAC,KAAK;IAClC,IAAIA,IAAI,EACFE,QAAQ,EAAEsC,mBAAmB,EAAE;MACjC,OAAOT,0BAA0B,CAAC/B,IAAI,CACjCE,QAAQ,CAACsC,mBAAmB,CAAC;IACtC;IACA,OAAOT,0BAA0B,CAAC/B,IAAI,CAAC;EAC3C;AACJ,CAAC,CAAC;AACF,MAAMyC,WAAW,GAAG9D,MAAM,CAAC;EACvBW,UAAU,EAAEA,CAACoD,OAAO,EAAE;IAAE1C;EAAK,CAAC,KAAK;IAC/B,MAAM;MAAE4B,IAAI;MAAEe;IAAM,CAAC,GAAG3C,IAAI;IAC5B,OAAO;MAAE,GAAG0C,OAAO,CAAC,YAAY,CAAC;MAAE,CAACd,IAAI,GAAGe;IAAM,CAAC;EACtD;AACJ,CAAC,CAAC;AACF,MAAMC,YAAY,GAAGjE,MAAM,CAAC;EACxBW,UAAU,EAAEA,CAACoD,OAAO,EAAE;IAAE1C;EAAK,CAAC;EAC9B;EACAxB,UAAU,CAAC;IAAE,GAAGkE,OAAO,CAAC,YAAY,CAAC;IAAE,GAAG1C;EAAK,CAAC,EAAE,UAAU;AAChE,CAAC,CAAC;AACF,MAAM6C,UAAU,GAAGlE,MAAM,CAAC;EACtBa,OAAO,EAAEA,CAACkD,OAAO,EAAE;IAAE1C;EAAK,CAAC,MAAM;IAC7B,GAAG0C,OAAO,CAAC,SAAS,CAAC;IACrB,CAAC1C,IAAI,CAAC4B,IAAI,GAAG;EACjB,CAAC;AACL,CAAC,CAAC;AACF,MAAMkB,2BAA2B,GAAGnE,MAAM,CAAC;EACvCoE,wBAAwB,EAAEA,CAAChD,CAAC,EAAE;IAAEC;EAAK,CAAC,KAAK;IACvC,MAAM;MAAEgD,KAAK;MAAEC;IAAa,CAAC,GAAGjD,IAAI;IACpC,MAAM+C,wBAAwB,GAAG;MAC7B,IAAIC,KAAK,IAAI;QAAEA;MAAM,CAAC,CAAC;MACvB,IAAIC,YAAY,IAAI;QAAEA;MAAa,CAAC;IACxC,CAAC;IACD,OAAOF,wBAAwB;EACnC;AACJ,CAAC,CAAC;AACF,MAAMG,0BAA0B,GAAGvE,MAAM,CAAC;EAAEwE,qBAAqB,EAAErE;AAAU,CAAC,CAAC;AAC/E,MAAMsE,wBAAwB,GAAGzE,MAAM,CAAC;EACpCwE,qBAAqB,EAAGT,OAAO,IAAKA,OAAO,CAACpD,UAAU,EAAE+D;AAC5D,CAAC,CAAC;AACF;AACA,MAAMC,0BAA0B,GAAG3E,MAAM,CAAC;EAAE6B,IAAI,EAAE;AAAkB,CAAC,CAAC;AACtE,MAAM+C,OAAO,GAAG;EACZ3E,kBAAkB;EAClBG,kBAAkB;EAClBI,UAAU;EACVE,eAAe;EACfJ,sBAAsB;EACtBiE,0BAA0B;EAC1B3D,YAAY;EACZE,SAAS;EACTE,oBAAoB;EACpBkD,UAAU;EACVJ,WAAW;EACXG,YAAY;EACZxC,kBAAkB;EAClBO,gBAAgB;EAChB4B,sBAAsB;EACtBb,cAAc;EACdD,oBAAoB;EACpBF,wBAAwB;EACxBH,iBAAiB;EACjBC,iBAAiB;EACjBM,cAAc;EACdjB,+BAA+B;EAC/B4C,0BAA0B;EAC1B7C,gCAAgC;EAChC2C,wBAAwB;EACxB7C,aAAa;EACbV,iBAAiB;EACjBiC,OAAO;EACPgB,2BAA2B;EAC3BjC,yBAAyB;EACzBM,iBAAiB;EACjBD;AACJ,CAAC;AAED,SAASqC,OAAO,IAAIC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}