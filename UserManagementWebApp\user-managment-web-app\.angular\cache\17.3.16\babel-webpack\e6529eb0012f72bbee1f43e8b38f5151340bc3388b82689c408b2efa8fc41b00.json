{"ast": null, "code": "import { syncSessionStorage } from '@aws-amplify/core';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Minutes until stored session invalidates is defaulted to 3 minutes\n// to maintain parity with Amazon Cognito user pools API behavior\nconst MS_TO_EXPIRY = 3 * 60 * 1000;\nconst TGT_STATE = 'CognitoSignInState';\nconst SIGN_IN_STATE_KEYS = {\n  username: `${TGT_STATE}.username`,\n  challengeName: `${TGT_STATE}.challengeName`,\n  signInSession: `${TGT_STATE}.signInSession`,\n  expiry: `${TGT_STATE}.expiry`\n};\nconst signInReducer = (state, action) => {\n  switch (action.type) {\n    case 'SET_SIGN_IN_SESSION':\n      persistSignInState({\n        signInSession: action.value\n      });\n      return {\n        ...state,\n        signInSession: action.value\n      };\n    case 'SET_SIGN_IN_STATE':\n      persistSignInState(action.value);\n      return {\n        ...action.value\n      };\n    case 'SET_CHALLENGE_NAME':\n      persistSignInState({\n        challengeName: action.value\n      });\n      return {\n        ...state,\n        challengeName: action.value\n      };\n    case 'SET_USERNAME':\n      persistSignInState({\n        username: action.value\n      });\n      return {\n        ...state,\n        username: action.value\n      };\n    case 'SET_INITIAL_STATE':\n      return getInitialState();\n    case 'RESET_STATE':\n      clearPersistedSignInState();\n      return getDefaultState();\n    // this state is never reachable\n    default:\n      return state;\n  }\n};\nconst isExpired = expiryDate => {\n  const expiryTimestamp = Number(expiryDate);\n  const currentTimestamp = Date.now();\n  return expiryTimestamp <= currentTimestamp;\n};\nconst resetActiveSignInState = () => {\n  signInStore.dispatch({\n    type: 'RESET_STATE'\n  });\n};\nconst clearPersistedSignInState = () => {\n  for (const stateKey of Object.values(SIGN_IN_STATE_KEYS)) {\n    syncSessionStorage.removeItem(stateKey);\n  }\n};\nconst getDefaultState = () => ({\n  username: undefined,\n  challengeName: undefined,\n  signInSession: undefined\n});\n// Hydrate signInStore from syncSessionStorage if the session has not expired\nconst getInitialState = () => {\n  const expiry = syncSessionStorage.getItem(SIGN_IN_STATE_KEYS.expiry);\n  if (!expiry || isExpired(expiry)) {\n    clearPersistedSignInState();\n    return getDefaultState();\n  }\n  const username = syncSessionStorage.getItem(SIGN_IN_STATE_KEYS.username) ?? undefined;\n  const challengeName = syncSessionStorage.getItem(SIGN_IN_STATE_KEYS.challengeName) ?? undefined;\n  const signInSession = syncSessionStorage.getItem(SIGN_IN_STATE_KEYS.signInSession) ?? undefined;\n  return {\n    username,\n    challengeName,\n    signInSession\n  };\n};\nconst createStore = reducer => {\n  let currentState = reducer(getDefaultState(), {\n    type: 'SET_INITIAL_STATE'\n  });\n  return {\n    getState: () => currentState,\n    dispatch: action => {\n      currentState = reducer(currentState, action);\n    }\n  };\n};\nconst signInStore = createStore(signInReducer);\nfunction setActiveSignInState(state) {\n  signInStore.dispatch({\n    type: 'SET_SIGN_IN_STATE',\n    value: state\n  });\n}\n// Save local state into Session Storage\nconst persistSignInState = ({\n  challengeName,\n  signInSession,\n  username\n}) => {\n  username && syncSessionStorage.setItem(SIGN_IN_STATE_KEYS.username, username);\n  challengeName && syncSessionStorage.setItem(SIGN_IN_STATE_KEYS.challengeName, challengeName);\n  if (signInSession) {\n    syncSessionStorage.setItem(SIGN_IN_STATE_KEYS.signInSession, signInSession);\n    // Updates expiry when session is passed\n    syncSessionStorage.setItem(SIGN_IN_STATE_KEYS.expiry, String(Date.now() + MS_TO_EXPIRY));\n  }\n};\nexport { persistSignInState, resetActiveSignInState, setActiveSignInState, signInStore };", "map": {"version": 3, "names": ["syncSessionStorage", "MS_TO_EXPIRY", "TGT_STATE", "SIGN_IN_STATE_KEYS", "username", "challenge<PERSON>ame", "signInSession", "expiry", "signInReducer", "state", "action", "type", "persistSignInState", "value", "getInitialState", "clearPersistedSignInState", "getDefaultState", "isExpired", "expiryDate", "expiryTimestamp", "Number", "currentTimestamp", "Date", "now", "resetActiveSignInState", "signInStore", "dispatch", "stateKey", "Object", "values", "removeItem", "undefined", "getItem", "createStore", "reducer", "currentState", "getState", "setActiveSignInState", "setItem", "String"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/utils/store/signInStore.mjs"], "sourcesContent": ["import { syncSessionStorage } from '@aws-amplify/core';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Minutes until stored session invalidates is defaulted to 3 minutes\n// to maintain parity with Amazon Cognito user pools API behavior\nconst MS_TO_EXPIRY = 3 * 60 * 1000;\nconst TGT_STATE = 'CognitoSignInState';\nconst SIGN_IN_STATE_KEYS = {\n    username: `${TGT_STATE}.username`,\n    challengeName: `${TGT_STATE}.challengeName`,\n    signInSession: `${TGT_STATE}.signInSession`,\n    expiry: `${TGT_STATE}.expiry`,\n};\nconst signInReducer = (state, action) => {\n    switch (action.type) {\n        case 'SET_SIGN_IN_SESSION':\n            persistSignInState({ signInSession: action.value });\n            return {\n                ...state,\n                signInSession: action.value,\n            };\n        case 'SET_SIGN_IN_STATE':\n            persistSignInState(action.value);\n            return {\n                ...action.value,\n            };\n        case 'SET_CHALLENGE_NAME':\n            persistSignInState({ challengeName: action.value });\n            return {\n                ...state,\n                challengeName: action.value,\n            };\n        case 'SET_USERNAME':\n            persistSignInState({ username: action.value });\n            return {\n                ...state,\n                username: action.value,\n            };\n        case 'SET_INITIAL_STATE':\n            return getInitialState();\n        case 'RESET_STATE':\n            clearPersistedSignInState();\n            return getDefaultState();\n        // this state is never reachable\n        default:\n            return state;\n    }\n};\nconst isExpired = (expiryDate) => {\n    const expiryTimestamp = Number(expiryDate);\n    const currentTimestamp = Date.now();\n    return expiryTimestamp <= currentTimestamp;\n};\nconst resetActiveSignInState = () => {\n    signInStore.dispatch({ type: 'RESET_STATE' });\n};\nconst clearPersistedSignInState = () => {\n    for (const stateKey of Object.values(SIGN_IN_STATE_KEYS)) {\n        syncSessionStorage.removeItem(stateKey);\n    }\n};\nconst getDefaultState = () => ({\n    username: undefined,\n    challengeName: undefined,\n    signInSession: undefined,\n});\n// Hydrate signInStore from syncSessionStorage if the session has not expired\nconst getInitialState = () => {\n    const expiry = syncSessionStorage.getItem(SIGN_IN_STATE_KEYS.expiry);\n    if (!expiry || isExpired(expiry)) {\n        clearPersistedSignInState();\n        return getDefaultState();\n    }\n    const username = syncSessionStorage.getItem(SIGN_IN_STATE_KEYS.username) ?? undefined;\n    const challengeName = (syncSessionStorage.getItem(SIGN_IN_STATE_KEYS.challengeName) ?? undefined);\n    const signInSession = syncSessionStorage.getItem(SIGN_IN_STATE_KEYS.signInSession) ?? undefined;\n    return {\n        username,\n        challengeName,\n        signInSession,\n    };\n};\nconst createStore = reducer => {\n    let currentState = reducer(getDefaultState(), { type: 'SET_INITIAL_STATE' });\n    return {\n        getState: () => currentState,\n        dispatch: action => {\n            currentState = reducer(currentState, action);\n        },\n    };\n};\nconst signInStore = createStore(signInReducer);\nfunction setActiveSignInState(state) {\n    signInStore.dispatch({\n        type: 'SET_SIGN_IN_STATE',\n        value: state,\n    });\n}\n// Save local state into Session Storage\nconst persistSignInState = ({ challengeName, signInSession, username, }) => {\n    username && syncSessionStorage.setItem(SIGN_IN_STATE_KEYS.username, username);\n    challengeName &&\n        syncSessionStorage.setItem(SIGN_IN_STATE_KEYS.challengeName, challengeName);\n    if (signInSession) {\n        syncSessionStorage.setItem(SIGN_IN_STATE_KEYS.signInSession, signInSession);\n        // Updates expiry when session is passed\n        syncSessionStorage.setItem(SIGN_IN_STATE_KEYS.expiry, String(Date.now() + MS_TO_EXPIRY));\n    }\n};\n\nexport { persistSignInState, resetActiveSignInState, setActiveSignInState, signInStore };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,mBAAmB;;AAEtD;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;AAClC,MAAMC,SAAS,GAAG,oBAAoB;AACtC,MAAMC,kBAAkB,GAAG;EACvBC,QAAQ,EAAE,GAAGF,SAAS,WAAW;EACjCG,aAAa,EAAE,GAAGH,SAAS,gBAAgB;EAC3CI,aAAa,EAAE,GAAGJ,SAAS,gBAAgB;EAC3CK,MAAM,EAAE,GAAGL,SAAS;AACxB,CAAC;AACD,MAAMM,aAAa,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACf,KAAK,qBAAqB;MACtBC,kBAAkB,CAAC;QAAEN,aAAa,EAAEI,MAAM,CAACG;MAAM,CAAC,CAAC;MACnD,OAAO;QACH,GAAGJ,KAAK;QACRH,aAAa,EAAEI,MAAM,CAACG;MAC1B,CAAC;IACL,KAAK,mBAAmB;MACpBD,kBAAkB,CAACF,MAAM,CAACG,KAAK,CAAC;MAChC,OAAO;QACH,GAAGH,MAAM,CAACG;MACd,CAAC;IACL,KAAK,oBAAoB;MACrBD,kBAAkB,CAAC;QAAEP,aAAa,EAAEK,MAAM,CAACG;MAAM,CAAC,CAAC;MACnD,OAAO;QACH,GAAGJ,KAAK;QACRJ,aAAa,EAAEK,MAAM,CAACG;MAC1B,CAAC;IACL,KAAK,cAAc;MACfD,kBAAkB,CAAC;QAAER,QAAQ,EAAEM,MAAM,CAACG;MAAM,CAAC,CAAC;MAC9C,OAAO;QACH,GAAGJ,KAAK;QACRL,QAAQ,EAAEM,MAAM,CAACG;MACrB,CAAC;IACL,KAAK,mBAAmB;MACpB,OAAOC,eAAe,CAAC,CAAC;IAC5B,KAAK,aAAa;MACdC,yBAAyB,CAAC,CAAC;MAC3B,OAAOC,eAAe,CAAC,CAAC;IAC5B;IACA;MACI,OAAOP,KAAK;EACpB;AACJ,CAAC;AACD,MAAMQ,SAAS,GAAIC,UAAU,IAAK;EAC9B,MAAMC,eAAe,GAAGC,MAAM,CAACF,UAAU,CAAC;EAC1C,MAAMG,gBAAgB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EACnC,OAAOJ,eAAe,IAAIE,gBAAgB;AAC9C,CAAC;AACD,MAAMG,sBAAsB,GAAGA,CAAA,KAAM;EACjCC,WAAW,CAACC,QAAQ,CAAC;IAAEf,IAAI,EAAE;EAAc,CAAC,CAAC;AACjD,CAAC;AACD,MAAMI,yBAAyB,GAAGA,CAAA,KAAM;EACpC,KAAK,MAAMY,QAAQ,IAAIC,MAAM,CAACC,MAAM,CAAC1B,kBAAkB,CAAC,EAAE;IACtDH,kBAAkB,CAAC8B,UAAU,CAACH,QAAQ,CAAC;EAC3C;AACJ,CAAC;AACD,MAAMX,eAAe,GAAGA,CAAA,MAAO;EAC3BZ,QAAQ,EAAE2B,SAAS;EACnB1B,aAAa,EAAE0B,SAAS;EACxBzB,aAAa,EAAEyB;AACnB,CAAC,CAAC;AACF;AACA,MAAMjB,eAAe,GAAGA,CAAA,KAAM;EAC1B,MAAMP,MAAM,GAAGP,kBAAkB,CAACgC,OAAO,CAAC7B,kBAAkB,CAACI,MAAM,CAAC;EACpE,IAAI,CAACA,MAAM,IAAIU,SAAS,CAACV,MAAM,CAAC,EAAE;IAC9BQ,yBAAyB,CAAC,CAAC;IAC3B,OAAOC,eAAe,CAAC,CAAC;EAC5B;EACA,MAAMZ,QAAQ,GAAGJ,kBAAkB,CAACgC,OAAO,CAAC7B,kBAAkB,CAACC,QAAQ,CAAC,IAAI2B,SAAS;EACrF,MAAM1B,aAAa,GAAIL,kBAAkB,CAACgC,OAAO,CAAC7B,kBAAkB,CAACE,aAAa,CAAC,IAAI0B,SAAU;EACjG,MAAMzB,aAAa,GAAGN,kBAAkB,CAACgC,OAAO,CAAC7B,kBAAkB,CAACG,aAAa,CAAC,IAAIyB,SAAS;EAC/F,OAAO;IACH3B,QAAQ;IACRC,aAAa;IACbC;EACJ,CAAC;AACL,CAAC;AACD,MAAM2B,WAAW,GAAGC,OAAO,IAAI;EAC3B,IAAIC,YAAY,GAAGD,OAAO,CAAClB,eAAe,CAAC,CAAC,EAAE;IAAEL,IAAI,EAAE;EAAoB,CAAC,CAAC;EAC5E,OAAO;IACHyB,QAAQ,EAAEA,CAAA,KAAMD,YAAY;IAC5BT,QAAQ,EAAEhB,MAAM,IAAI;MAChByB,YAAY,GAAGD,OAAO,CAACC,YAAY,EAAEzB,MAAM,CAAC;IAChD;EACJ,CAAC;AACL,CAAC;AACD,MAAMe,WAAW,GAAGQ,WAAW,CAACzB,aAAa,CAAC;AAC9C,SAAS6B,oBAAoBA,CAAC5B,KAAK,EAAE;EACjCgB,WAAW,CAACC,QAAQ,CAAC;IACjBf,IAAI,EAAE,mBAAmB;IACzBE,KAAK,EAAEJ;EACX,CAAC,CAAC;AACN;AACA;AACA,MAAMG,kBAAkB,GAAGA,CAAC;EAAEP,aAAa;EAAEC,aAAa;EAAEF;AAAU,CAAC,KAAK;EACxEA,QAAQ,IAAIJ,kBAAkB,CAACsC,OAAO,CAACnC,kBAAkB,CAACC,QAAQ,EAAEA,QAAQ,CAAC;EAC7EC,aAAa,IACTL,kBAAkB,CAACsC,OAAO,CAACnC,kBAAkB,CAACE,aAAa,EAAEA,aAAa,CAAC;EAC/E,IAAIC,aAAa,EAAE;IACfN,kBAAkB,CAACsC,OAAO,CAACnC,kBAAkB,CAACG,aAAa,EAAEA,aAAa,CAAC;IAC3E;IACAN,kBAAkB,CAACsC,OAAO,CAACnC,kBAAkB,CAACI,MAAM,EAAEgC,MAAM,CAACjB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGtB,YAAY,CAAC,CAAC;EAC5F;AACJ,CAAC;AAED,SAASW,kBAAkB,EAAEY,sBAAsB,EAAEa,oBAAoB,EAAEZ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}