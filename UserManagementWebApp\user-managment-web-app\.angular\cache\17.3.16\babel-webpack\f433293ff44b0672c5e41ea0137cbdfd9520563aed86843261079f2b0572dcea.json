{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\nimport { DeleteGeofenceCollectionRequestFilterSensitiveLog, DeleteGeofenceCollectionResponseFilterSensitiveLog } from \"../models/models_0\";\nimport { deserializeAws_restJson1DeleteGeofenceCollectionCommand, serializeAws_restJson1DeleteGeofenceCollectionCommand } from \"../protocols/Aws_restJson1\";\nvar DeleteGeofenceCollectionCommand = function (_super) {\n  __extends(DeleteGeofenceCollectionCommand, _super);\n  function DeleteGeofenceCollectionCommand(input) {\n    var _this = _super.call(this) || this;\n    _this.input = input;\n    return _this;\n  }\n  DeleteGeofenceCollectionCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n    this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n    var stack = clientStack.concat(this.middlewareStack);\n    var logger = configuration.logger;\n    var clientName = \"LocationClient\";\n    var commandName = \"DeleteGeofenceCollectionCommand\";\n    var handlerExecutionContext = {\n      logger: logger,\n      clientName: clientName,\n      commandName: commandName,\n      inputFilterSensitiveLog: DeleteGeofenceCollectionRequestFilterSensitiveLog,\n      outputFilterSensitiveLog: DeleteGeofenceCollectionResponseFilterSensitiveLog\n    };\n    var requestHandler = configuration.requestHandler;\n    return stack.resolve(function (request) {\n      return requestHandler.handle(request.request, options || {});\n    }, handlerExecutionContext);\n  };\n  DeleteGeofenceCollectionCommand.prototype.serialize = function (input, context) {\n    return serializeAws_restJson1DeleteGeofenceCollectionCommand(input, context);\n  };\n  DeleteGeofenceCollectionCommand.prototype.deserialize = function (output, context) {\n    return deserializeAws_restJson1DeleteGeofenceCollectionCommand(output, context);\n  };\n  return DeleteGeofenceCollectionCommand;\n}($Command);\nexport { DeleteGeofenceCollectionCommand };", "map": {"version": 3, "names": ["__extends", "getSerdePlugin", "Command", "$Command", "DeleteGeofenceCollectionRequestFilterSensitiveLog", "DeleteGeofenceCollectionResponseFilterSensitiveLog", "deserializeAws_restJson1DeleteGeofenceCollectionCommand", "serializeAws_restJson1DeleteGeofenceCollectionCommand", "DeleteGeofenceCollectionCommand", "_super", "input", "_this", "call", "prototype", "resolveMiddleware", "clientStack", "configuration", "options", "middlewareStack", "use", "serialize", "deserialize", "stack", "concat", "logger", "clientName", "commandName", "handlerExecutionContext", "inputFilterSensitiveLog", "outputFilterSensitiveLog", "requestHandler", "resolve", "request", "handle", "context", "output"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-location/dist-es/commands/DeleteGeofenceCollectionCommand.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\nimport { DeleteGeofenceCollectionRequestFilterSensitiveLog, DeleteGeofenceCollectionResponseFilterSensitiveLog, } from \"../models/models_0\";\nimport { deserializeAws_restJson1DeleteGeofenceCollectionCommand, serializeAws_restJson1DeleteGeofenceCollectionCommand, } from \"../protocols/Aws_restJson1\";\nvar DeleteGeofenceCollectionCommand = (function (_super) {\n    __extends(DeleteGeofenceCollectionCommand, _super);\n    function DeleteGeofenceCollectionCommand(input) {\n        var _this = _super.call(this) || this;\n        _this.input = input;\n        return _this;\n    }\n    DeleteGeofenceCollectionCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n        this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n        var stack = clientStack.concat(this.middlewareStack);\n        var logger = configuration.logger;\n        var clientName = \"LocationClient\";\n        var commandName = \"DeleteGeofenceCollectionCommand\";\n        var handlerExecutionContext = {\n            logger: logger,\n            clientName: clientName,\n            commandName: commandName,\n            inputFilterSensitiveLog: DeleteGeofenceCollectionRequestFilterSensitiveLog,\n            outputFilterSensitiveLog: DeleteGeofenceCollectionResponseFilterSensitiveLog,\n        };\n        var requestHandler = configuration.requestHandler;\n        return stack.resolve(function (request) {\n            return requestHandler.handle(request.request, options || {});\n        }, handlerExecutionContext);\n    };\n    DeleteGeofenceCollectionCommand.prototype.serialize = function (input, context) {\n        return serializeAws_restJson1DeleteGeofenceCollectionCommand(input, context);\n    };\n    DeleteGeofenceCollectionCommand.prototype.deserialize = function (output, context) {\n        return deserializeAws_restJson1DeleteGeofenceCollectionCommand(output, context);\n    };\n    return DeleteGeofenceCollectionCommand;\n}($Command));\nexport { DeleteGeofenceCollectionCommand };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,OAAO,IAAIC,QAAQ,QAAQ,wBAAwB;AAC5D,SAASC,iDAAiD,EAAEC,kDAAkD,QAAS,oBAAoB;AAC3I,SAASC,uDAAuD,EAAEC,qDAAqD,QAAS,4BAA4B;AAC5J,IAAIC,+BAA+B,GAAI,UAAUC,MAAM,EAAE;EACrDT,SAAS,CAACQ,+BAA+B,EAAEC,MAAM,CAAC;EAClD,SAASD,+BAA+BA,CAACE,KAAK,EAAE;IAC5C,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACD,KAAK,GAAGA,KAAK;IACnB,OAAOC,KAAK;EAChB;EACAH,+BAA+B,CAACK,SAAS,CAACC,iBAAiB,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAEC,OAAO,EAAE;IACzG,IAAI,CAACC,eAAe,CAACC,GAAG,CAAClB,cAAc,CAACe,aAAa,EAAE,IAAI,CAACI,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;IACzF,IAAIC,KAAK,GAAGP,WAAW,CAACQ,MAAM,CAAC,IAAI,CAACL,eAAe,CAAC;IACpD,IAAIM,MAAM,GAAGR,aAAa,CAACQ,MAAM;IACjC,IAAIC,UAAU,GAAG,gBAAgB;IACjC,IAAIC,WAAW,GAAG,iCAAiC;IACnD,IAAIC,uBAAuB,GAAG;MAC1BH,MAAM,EAAEA,MAAM;MACdC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA,WAAW;MACxBE,uBAAuB,EAAExB,iDAAiD;MAC1EyB,wBAAwB,EAAExB;IAC9B,CAAC;IACD,IAAIyB,cAAc,GAAGd,aAAa,CAACc,cAAc;IACjD,OAAOR,KAAK,CAACS,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpC,OAAOF,cAAc,CAACG,MAAM,CAACD,OAAO,CAACA,OAAO,EAAEf,OAAO,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC,EAAEU,uBAAuB,CAAC;EAC/B,CAAC;EACDnB,+BAA+B,CAACK,SAAS,CAACO,SAAS,GAAG,UAAUV,KAAK,EAAEwB,OAAO,EAAE;IAC5E,OAAO3B,qDAAqD,CAACG,KAAK,EAAEwB,OAAO,CAAC;EAChF,CAAC;EACD1B,+BAA+B,CAACK,SAAS,CAACQ,WAAW,GAAG,UAAUc,MAAM,EAAED,OAAO,EAAE;IAC/E,OAAO5B,uDAAuD,CAAC6B,MAAM,EAAED,OAAO,CAAC;EACnF,CAAC;EACD,OAAO1B,+BAA+B;AAC1C,CAAC,CAACL,QAAQ,CAAE;AACZ,SAASK,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}