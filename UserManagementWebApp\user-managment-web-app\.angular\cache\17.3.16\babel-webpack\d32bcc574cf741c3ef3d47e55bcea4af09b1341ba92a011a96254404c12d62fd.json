{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Default partition for AWS services. This is used when the region is not provided or the region is not recognized.\n *\n * @internal\n */\nconst defaultPartition = {\n  id: 'aws',\n  outputs: {\n    dnsSuffix: 'amazonaws.com'\n  },\n  regionRegex: '^(us|eu|ap|sa|ca|me|af)\\\\-\\\\w+\\\\-\\\\d+$',\n  regions: ['aws-global']\n};\n/**\n * This data is adapted from the partition file from AWS SDK shared utilities but remove some contents for bundle size\n * concern. Information removed are `dualStackDnsSuffix`, `supportDualStack`, `supportFIPS`, restricted partitions, and\n * list of regions for each partition other than global regions.\n *\n * * Ref: https://docs.aws.amazon.com/general/latest/gr/rande.html#regional-endpoints\n * * Ref: https://github.com/aws/aws-sdk-js-v3/blob/0201baef03c2379f1f6f7150b9d401d4b230d488/packages/util-endpoints/src/lib/aws/partitions.json#L1\n *\n * @internal\n */\nconst partitionsInfo = {\n  partitions: [defaultPartition, {\n    id: 'aws-cn',\n    outputs: {\n      dnsSuffix: 'amazonaws.com.cn'\n    },\n    regionRegex: '^cn\\\\-\\\\w+\\\\-\\\\d+$',\n    regions: ['aws-cn-global']\n  }]\n};\nexport { defaultPartition, partitionsInfo };", "map": {"version": 3, "names": ["defaultPartition", "id", "outputs", "dnsSuffix", "regionRegex", "regions", "partitionsInfo", "partitions"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/endpoints/partitions.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Default partition for AWS services. This is used when the region is not provided or the region is not recognized.\n *\n * @internal\n */\nconst defaultPartition = {\n    id: 'aws',\n    outputs: {\n        dnsSuffix: 'amazonaws.com',\n    },\n    regionRegex: '^(us|eu|ap|sa|ca|me|af)\\\\-\\\\w+\\\\-\\\\d+$',\n    regions: ['aws-global'],\n};\n/**\n * This data is adapted from the partition file from AWS SDK shared utilities but remove some contents for bundle size\n * concern. Information removed are `dualStackDnsSuffix`, `supportDualStack`, `supportFIPS`, restricted partitions, and\n * list of regions for each partition other than global regions.\n *\n * * Ref: https://docs.aws.amazon.com/general/latest/gr/rande.html#regional-endpoints\n * * Ref: https://github.com/aws/aws-sdk-js-v3/blob/0201baef03c2379f1f6f7150b9d401d4b230d488/packages/util-endpoints/src/lib/aws/partitions.json#L1\n *\n * @internal\n */\nconst partitionsInfo = {\n    partitions: [\n        defaultPartition,\n        {\n            id: 'aws-cn',\n            outputs: {\n                dnsSuffix: 'amazonaws.com.cn',\n            },\n            regionRegex: '^cn\\\\-\\\\w+\\\\-\\\\d+$',\n            regions: ['aws-cn-global'],\n        },\n    ],\n};\n\nexport { defaultPartition, partitionsInfo };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,gBAAgB,GAAG;EACrBC,EAAE,EAAE,KAAK;EACTC,OAAO,EAAE;IACLC,SAAS,EAAE;EACf,CAAC;EACDC,WAAW,EAAE,wCAAwC;EACrDC,OAAO,EAAE,CAAC,YAAY;AAC1B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAG;EACnBC,UAAU,EAAE,CACRP,gBAAgB,EAChB;IACIC,EAAE,EAAE,QAAQ;IACZC,OAAO,EAAE;MACLC,SAAS,EAAE;IACf,CAAC;IACDC,WAAW,EAAE,oBAAoB;IACjCC,OAAO,EAAE,CAAC,eAAe;EAC7B,CAAC;AAET,CAAC;AAED,SAASL,gBAAgB,EAAEM,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}