<div class="col-12 p-inputgroup no-label" *ngIf="isDropdown() || isLinkFormat()">
    <p-dropdown *ngIf="isDropdown()" [options]="actionableGridColumnConfig.values" [(ngModel)]="selectedValue"
        name="selectedValue" appendTo="body" optionLabel="value" optionValue="value" (onHide)="autoSizeColumn()"
        panelStyleClass="panel-actionable-grid-format-dropdown"></p-dropdown>
    <input *ngIf="isLinkFormat()" [readonly]="true" type="text" pInputText
        [value]="actionableGridColumnConfig.link?.generatedLink ?? ''" />
    <span class="p-inputgroup-addon"><i class="pi pi-pencil" (click)="onConfigureFormatValuesClick()"></i></span>
</div>

<p-dialog [(visible)]="isDropdown() && showModalForm"  [style]="{ 'width': '470px'}" [modal]="true"
    (onHide)="onCancelButtonClick()" appendTo="body" styleClass="overflow-auto" contentStyleClass="pb-1">
    <ng-template pTemplate="header">
        <div class="p-dialog-title">
            Configure {{actionableGridColumnConfig.displayName || actionableGridColumnConfig.column}} Values
        </div>
    </ng-template>
    <div class="card p-2">
        <div *ngIf="actionableGridColumnConfig.format.type === actionableGridColumnType.String">
            <p class="section-description">Use new line to specify allowed values; i.e, one value per line</p>
            <textarea class="textarea-full" [rows]="10" [cols]="70" pInputTextarea autoResize="autoResize"
                [(ngModel)]="stringFormatValues"></textarea>
        </div>
        <div *ngIf="actionableGridColumnConfig.format.type !== actionableGridColumnType.String">
            <div *ngIf="actionableGridColumnConfig.format.type === actionableGridColumnType.Symbol"
                class="mb-2">
                <p-button pRipple icon="pi pi-plus" (onClick)="onAddNewSymbolClick()"></p-button>
            </div>
            <div class="actionable-grid-config-setting">
                <ag-grid-angular id="ag-Configuration-Symbol" domLayout="autoHeight"
                    class="ag-theme-material format-columns-data compact" rowHeight="55" [headerHeight]="55" [animateRows]="true"
                    [rowData]="actionableGridColumnConfig.values" [columnDefs]="columnDefs"
                    (gridReady)="onGridReady($event)" [defaultColDef]="defaultColDef">
                </ag-grid-angular>
            </div>
        </div>
    </div>
    <ng-template pTemplate="footer">
        <p-button pRipple type="button" label="Save" (onClick)="onSaveButtonClick()"></p-button>
        <p-button pRipple type="button" label="Cancel" [outlined]="true" severity="secondary" (onClick)="onCancelButtonClick()"></p-button>
    </ng-template>
</p-dialog>

<p-dialog [(visible)]="showModalForm && isLinkFormat() && actionableGridColumnConfig.link"
    styleClass="link-values-config" (onHide)="onCancelLinkValues()" [modal]="true" appendTo="body" contentStyleClass="pb-1">
    <ng-template pTemplate="header">
        <div class="p-dialog-title">
            Configure {{actionableGridColumnConfig.displayName || actionableGridColumnConfig.column}} Link Values
        </div>
    </ng-template>
    <div class="card">
        <div class="col-12">
            <p-table [value]="[actionableGridColumnConfig.link]">
                <ng-template pTemplate="header">
                    <tr>
                        <th class="w-27rem max-w-27rem">URL Alias String</th>
                        <th class="w-22rem max-w-22rem">URL Substitutions</th>
                        <th class="w-11rem max-w-11rem">Open In</th>
                        <th class="w-20rem max-w-20rem">Grid Generated Link</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-link>
                    <tr>
                        <td>
                            <input id="urlAlias" [(ngModel)]="link.urlAlias" required name="urlAlias"
                                placeholder="eg:http://xxxx.com?param={0}&param={1}"
                                (focusout)="generatePreviewLink(link)" pInputText />
                        </td>
                        <td>
                            <p-table [value]="actionableGridColumnConfig.link.urlSubstitutions" styleClass="p-0 m-0">
                                <ng-template pTemplate="body" let-urlSub let-i="rowIndex">
                                    <tr>
                                        <td class="border-top-none bg-surface-primary">
                                            <div class="p-inputgroup">
                                                <input pInputText type="text" name="urlSubstitution{{i}}" [(ngModel)]="urlSub.urlName"
                                                    [ngModelOptions]="{updateOn: 'blur'}" placeholder="Parameter">
                                                <span class="p-inputgroup-addon">
                                                    <p-button [text]="true" icon="pi pi-pencil" (onClick)="selectUrlSubstitution(urlSub,i)"></p-button>
                                                </span>
                                                <span class="p-inputgroup-addon">
                                                    <p-button pRipple name="deleteSubstitution{{i}}" icon="pi pi-trash" 
                                                        [text]="true" severity="danger" (onClick)="deleteSubstitution(i)"></p-button>
                                                </span><span class="p-inputgroup-addon">
                                                    <p-button pRipple name="addSubstitution{{i}}" icon="pi pi-plus"
                                                        [text]="true" (onClick)="addSubstitution()"></p-button>
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                </ng-template>
                            </p-table>
            </td>
            <td>
                <p-dropdown inputId="openIn" name="openIn" placeholder="Open in" [options]="openInValues"
                    [(ngModel)]="link.openIn" optionValue="target" optionLabel="displayName" appendTo="body">
                </p-dropdown>
            </td>
            <td>
                <div class="col-12"> {{link.generatedLink}}
                </div>
            </td>
            </tr>
            </ng-template>
            </p-table>
        </div>
    </div>
    <ng-template pTemplate="footer">
        <p-button pRipple type="button" label="Save" (click)="onSaveLinkValues()"></p-button>
        <p-button pRipple type="button" label="Cancel" (click)="onCancelLinkValues()" [outlined]="true" severity="secondary"></p-button>
    </ng-template>
</p-dialog>

<p-dialog *ngIf="showURLSubstitution" [(visible)]="showURLSubstitution"
    styleClass="url-substitution-values" [modal]="true" appendTo="body"  contentStyleClass="pb-1">
    <ng-template pTemplate="header">
        <div class="p-dialog-title">
            Select URL Substitution
        </div>
    </ng-template>
    <div class="grid card">
        <div class="col-6">
            <div class="field-radiobutton">
                <p-radioButton name="selectedUrlSubType" value="column" [(ngModel)]="selectedUrlSubType" inputId="rdbUrlColumn"
                    class="mb-1">
                </p-radioButton>
                <label for="rdbUrlColumn">Column</label>
            </div>
        </div>
        <div class="col-6">
            <div class="field-radiobutton">
                <p-radioButton name="selectedUrlSubType" value="projectVariable" [(ngModel)]="selectedUrlSubType" class="mb-1"
                    inputId="rdbUrlProjectVariable">
                </p-radioButton>
                <label for="rdbUrlProjectVariable">Project Variable</label>
            </div>
        </div>
        <div *ngIf="selectedUrlSubType === urlSubTypes.Column" class="col-12">
            <p-dropdown [options]="columnsListValues" [(ngModel)]="selectedUrlName" optionLabel="label"
                optionValue="value" [filter]="true" filterBy="label" [showClear]="true" placeholder="Column">
            </p-dropdown>
        </div>
        <div *ngIf="selectedUrlSubType === urlSubTypes.ProjectVariable" class="col-12">
            <p-dropdown [options]="projectVariables" [(ngModel)]="selectedUrlName" optionLabel="label" [filter]="true"
                optionValue="label" filterBy="label" [showClear]="true" placeholder="Project Variables">
                <ng-template let-projectVariable pTemplate="item">
                    <div>
                        {{projectVariable.label}}
                        <span style="float: right;"><i [pTooltip]="projectVariable.title" tooltipPosition="top"
                                class="pi pi-info-circle" appendTo="body"></i></span>
                    </div>
                </ng-template>
            </p-dropdown>
        </div>
    </div>
    <ng-template pTemplate="footer">
        <p-button pRipple type="button" label="Save" (click)="saveURLSubstitution()"></p-button>
        <p-button pRipple type="button" label="Cancel" (click)="onCancelURLSubstitution()"
            [outlined]="true" severity="secondary" (click)="showURLSubstitution=false"></p-button>
    </ng-template>
</p-dialog>