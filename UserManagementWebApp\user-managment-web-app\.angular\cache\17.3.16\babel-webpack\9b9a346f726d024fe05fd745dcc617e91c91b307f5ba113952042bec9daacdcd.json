{"ast": null, "code": "import { getActorContext, getActorState } from './actor.mjs';\nimport { NAVIGABLE_ROUTE_EVENT } from './constants.mjs';\nimport { getRoute } from './getRoute.mjs';\n\n/**\n * This file contains helpers that translates xstate internals to more\n * understandable authenticator contexts. We provide these contexts with\n * `useAuthenticator` hook/composable/service.\n */\n/**\n * Creates public facing auth helpers that abstracts out xstate implementation\n * detail. Each framework implementation can export these helpers so that\n * developers can send events without having to learn internals.\n *\n * ```\n * const [state, send] = useActor(...);\n * const { submit } = getSendEventAliases(send);\n * submit({ username, password})\n * ```\n */\nconst getSendEventAliases = send => {\n  const sendToMachine = type => {\n    // TODO If these were created during the creation of the machine & provider,\n    // then invalid transitions could be caught via https://xstate.js.org/docs/guides/states.html#state-can-event\n    return data => send({\n      type,\n      data\n    });\n  };\n  return {\n    initializeMachine: sendToMachine('INIT'),\n    resendCode: sendToMachine('RESEND'),\n    signOut: sendToMachine('SIGN_OUT'),\n    submitForm: sendToMachine('SUBMIT'),\n    updateForm: sendToMachine('CHANGE'),\n    updateBlur: sendToMachine('BLUR'),\n    // Actions that don't immediately invoke a service but instead transition to a screen\n    // are prefixed with `to*`\n    toFederatedSignIn: sendToMachine('FEDERATED_SIGN_IN'),\n    toForgotPassword: sendToMachine('FORGOT_PASSWORD'),\n    toSignIn: sendToMachine('SIGN_IN'),\n    toSignUp: sendToMachine('SIGN_UP'),\n    skipVerification: sendToMachine('SKIP')\n  };\n};\nconst getNextSendEventAliases = send => {\n  const {\n    toFederatedSignIn,\n    submitForm,\n    resendCode,\n    skipVerification\n  } = getSendEventAliases(send);\n  return {\n    handleSubmit: submitForm,\n    resendConfirmationCode: resendCode,\n    // manual \"route\" navigation\n    setRoute: route => send({\n      type: NAVIGABLE_ROUTE_EVENT[route]\n    }),\n    skipAttributeVerification: skipVerification,\n    toFederatedSignIn\n  };\n};\nconst getServiceContextFacade = state => {\n  const actorContext = getActorContext(state) ?? {};\n  const {\n    allowedMfaTypes,\n    challengeName,\n    codeDeliveryDetails,\n    remoteError: error,\n    validationError: validationErrors,\n    totpSecretCode = null,\n    unverifiedUserAttributes,\n    username\n  } = actorContext;\n  const {\n    socialProviders = []\n  } = state.context?.config ?? {};\n  // check for user in actorContext prior to state context. actorContext is more \"up to date\",\n  // but is not available on all states\n  const user = actorContext?.user ?? state.context?.user;\n  const hasValidationErrors = !!(validationErrors && Object.keys(validationErrors).length > 0);\n  const actorState = getActorState(state);\n  const isPending = state.hasTag('pending') || actorState?.hasTag('pending');\n  const route = getRoute(state, actorState);\n  // Auth status represents the current state of the auth flow\n  // The `configuring` state is used to indicate when the xState machine is loading\n  const authStatus = (route => {\n    switch (route) {\n      case 'idle':\n      case 'setup':\n        return 'configuring';\n      case 'authenticated':\n        return 'authenticated';\n      default:\n        return 'unauthenticated';\n    }\n  })(route);\n  const facade = {\n    allowedMfaTypes,\n    authStatus,\n    challengeName,\n    codeDeliveryDetails,\n    error,\n    hasValidationErrors,\n    isPending,\n    route,\n    socialProviders,\n    totpSecretCode,\n    unverifiedUserAttributes,\n    user,\n    username,\n    validationErrors\n    // @v6-migration-note\n    // While most of the properties\n    // on `AuthenticatorServiceContextFacade` can resolve to `undefined`, updating\n    // the interface requires material changes in consumers (namely `useAuthenticator`)\n    // which will have implications on the UI layer as typeguards and non-null checks\n    // are required to pass type checking. As the `Authenticator` is behaving as expected\n    // with the `AuthenticatorServiceContextFacade` interface, prefer to cast\n  };\n  return facade;\n};\nconst getNextServiceContextFacade = state => {\n  const actorContext = getActorContext(state) ?? {};\n  const {\n    allowedMfaTypes,\n    challengeName,\n    codeDeliveryDetails,\n    remoteError: errorMessage,\n    totpSecretCode,\n    unverifiedUserAttributes,\n    username\n  } = actorContext;\n  const {\n    socialProviders: federatedProviders,\n    loginMechanisms\n  } = state.context?.config ?? {};\n  const loginMechanism = loginMechanisms?.[0];\n  const actorState = getActorState(state);\n  const isPending = state.hasTag('pending') || actorState?.hasTag('pending');\n  // @todo-migration remove this cast for Authenticator.Next\n  const route = getRoute(state, actorState);\n  return {\n    allowedMfaTypes,\n    challengeName,\n    codeDeliveryDetails,\n    errorMessage,\n    federatedProviders,\n    isPending,\n    loginMechanism,\n    route,\n    totpSecretCode,\n    unverifiedUserAttributes,\n    username\n  };\n};\nconst getServiceFacade = ({\n  send,\n  state\n}) => {\n  const sendEventAliases = getSendEventAliases(send);\n  const serviceContext = getServiceContextFacade(state);\n  return {\n    ...sendEventAliases,\n    ...serviceContext\n  };\n};\nconst getNextServiceFacade = ({\n  send,\n  state\n}) => ({\n  ...getNextSendEventAliases(send),\n  ...getNextServiceContextFacade(state)\n});\nexport { getNextServiceContextFacade, getNextServiceFacade, getSendEventAliases, getServiceContextFacade, getServiceFacade };", "map": {"version": 3, "names": ["getActorContext", "getActorState", "NAVIGABLE_ROUTE_EVENT", "getRoute", "getSendEventAliases", "send", "sendToMachine", "type", "data", "initializeMachine", "resendCode", "signOut", "submitForm", "updateForm", "updateBlur", "toFederatedSignIn", "toForgotPassword", "toSignIn", "toSignUp", "skipVerification", "getNextSendEventAliases", "handleSubmit", "resendConfirmationCode", "setRoute", "route", "skipAttributeVerification", "getServiceContextFacade", "state", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allowedMfaTypes", "challenge<PERSON>ame", "codeDeliveryDetails", "remoteError", "error", "validationError", "validationErrors", "totpSecretCode", "unverifiedUserAttributes", "username", "socialProviders", "context", "config", "user", "hasValidationErrors", "Object", "keys", "length", "actor<PERSON><PERSON>", "isPending", "hasTag", "authStatus", "facade", "getNextServiceContextFacade", "errorMessage", "federatedProviders", "loginMechanisms", "loginMechanism", "getServiceFacade", "sendEventAliases", "serviceContext", "getNextServiceFacade"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/helpers/authenticator/facade.mjs"], "sourcesContent": ["import { getActorContext, getActorState } from './actor.mjs';\nimport { NAVIGABLE_ROUTE_EVENT } from './constants.mjs';\nimport { getRoute } from './getRoute.mjs';\n\n/**\n * This file contains helpers that translates xstate internals to more\n * understandable authenticator contexts. We provide these contexts with\n * `useAuthenticator` hook/composable/service.\n */\n/**\n * Creates public facing auth helpers that abstracts out xstate implementation\n * detail. Each framework implementation can export these helpers so that\n * developers can send events without having to learn internals.\n *\n * ```\n * const [state, send] = useActor(...);\n * const { submit } = getSendEventAliases(send);\n * submit({ username, password})\n * ```\n */\nconst getSendEventAliases = (send) => {\n    const sendToMachine = (type) => {\n        // TODO If these were created during the creation of the machine & provider,\n        // then invalid transitions could be caught via https://xstate.js.org/docs/guides/states.html#state-can-event\n        return (data) => send({ type, data });\n    };\n    return {\n        initializeMachine: sendToMachine('INIT'),\n        resendCode: sendToMachine('RESEND'),\n        signOut: sendToMachine('SIGN_OUT'),\n        submitForm: sendToMachine('SUBMIT'),\n        updateForm: sendToMachine('CHANGE'),\n        updateBlur: sendToMachine('BLUR'),\n        // Actions that don't immediately invoke a service but instead transition to a screen\n        // are prefixed with `to*`\n        toFederatedSignIn: sendToMachine('FEDERATED_SIGN_IN'),\n        toForgotPassword: sendToMachine('FORGOT_PASSWORD'),\n        toSignIn: sendToMachine('SIGN_IN'),\n        toSignUp: sendToMachine('SIGN_UP'),\n        skipVerification: sendToMachine('SKIP'),\n    };\n};\nconst getNextSendEventAliases = (send) => {\n    const { toFederatedSignIn, submitForm, resendCode, skipVerification } = getSendEventAliases(send);\n    return {\n        handleSubmit: submitForm,\n        resendConfirmationCode: resendCode,\n        // manual \"route\" navigation\n        setRoute: (route) => send({ type: NAVIGABLE_ROUTE_EVENT[route] }),\n        skipAttributeVerification: skipVerification,\n        toFederatedSignIn,\n    };\n};\nconst getServiceContextFacade = (state) => {\n    const actorContext = (getActorContext(state) ?? {});\n    const { allowedMfaTypes, challengeName, codeDeliveryDetails, remoteError: error, validationError: validationErrors, totpSecretCode = null, unverifiedUserAttributes, username, } = actorContext;\n    const { socialProviders = [] } = state.context?.config ?? {};\n    // check for user in actorContext prior to state context. actorContext is more \"up to date\",\n    // but is not available on all states\n    const user = actorContext?.user ?? state.context?.user;\n    const hasValidationErrors = !!(validationErrors && Object.keys(validationErrors).length > 0);\n    const actorState = getActorState(state);\n    const isPending = state.hasTag('pending') || actorState?.hasTag('pending');\n    const route = getRoute(state, actorState);\n    // Auth status represents the current state of the auth flow\n    // The `configuring` state is used to indicate when the xState machine is loading\n    const authStatus = ((route) => {\n        switch (route) {\n            case 'idle':\n            case 'setup':\n                return 'configuring';\n            case 'authenticated':\n                return 'authenticated';\n            default:\n                return 'unauthenticated';\n        }\n    })(route);\n    const facade = {\n        allowedMfaTypes,\n        authStatus,\n        challengeName,\n        codeDeliveryDetails,\n        error,\n        hasValidationErrors,\n        isPending,\n        route,\n        socialProviders,\n        totpSecretCode,\n        unverifiedUserAttributes,\n        user,\n        username,\n        validationErrors,\n        // @v6-migration-note\n        // While most of the properties\n        // on `AuthenticatorServiceContextFacade` can resolve to `undefined`, updating\n        // the interface requires material changes in consumers (namely `useAuthenticator`)\n        // which will have implications on the UI layer as typeguards and non-null checks\n        // are required to pass type checking. As the `Authenticator` is behaving as expected\n        // with the `AuthenticatorServiceContextFacade` interface, prefer to cast\n    };\n    return facade;\n};\nconst getNextServiceContextFacade = (state) => {\n    const actorContext = (getActorContext(state) ?? {});\n    const { allowedMfaTypes, challengeName, codeDeliveryDetails, remoteError: errorMessage, totpSecretCode, unverifiedUserAttributes, username, } = actorContext;\n    const { socialProviders: federatedProviders, loginMechanisms } = state.context?.config ?? {};\n    const loginMechanism = loginMechanisms?.[0];\n    const actorState = getActorState(state);\n    const isPending = state.hasTag('pending') || actorState?.hasTag('pending');\n    // @todo-migration remove this cast for Authenticator.Next\n    const route = getRoute(state, actorState);\n    return {\n        allowedMfaTypes,\n        challengeName,\n        codeDeliveryDetails,\n        errorMessage,\n        federatedProviders,\n        isPending,\n        loginMechanism,\n        route,\n        totpSecretCode,\n        unverifiedUserAttributes,\n        username,\n    };\n};\nconst getServiceFacade = ({ send, state, }) => {\n    const sendEventAliases = getSendEventAliases(send);\n    const serviceContext = getServiceContextFacade(state);\n    return {\n        ...sendEventAliases,\n        ...serviceContext,\n    };\n};\nconst getNextServiceFacade = ({ send, state, }) => ({\n    ...getNextSendEventAliases(send),\n    ...getNextServiceContextFacade(state),\n});\n\nexport { getNextServiceContextFacade, getNextServiceFacade, getSendEventAliases, getServiceContextFacade, getServiceFacade };\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,aAAa,QAAQ,aAAa;AAC5D,SAASC,qBAAqB,QAAQ,iBAAiB;AACvD,SAASC,QAAQ,QAAQ,gBAAgB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;EAClC,MAAMC,aAAa,GAAIC,IAAI,IAAK;IAC5B;IACA;IACA,OAAQC,IAAI,IAAKH,IAAI,CAAC;MAAEE,IAAI;MAAEC;IAAK,CAAC,CAAC;EACzC,CAAC;EACD,OAAO;IACHC,iBAAiB,EAAEH,aAAa,CAAC,MAAM,CAAC;IACxCI,UAAU,EAAEJ,aAAa,CAAC,QAAQ,CAAC;IACnCK,OAAO,EAAEL,aAAa,CAAC,UAAU,CAAC;IAClCM,UAAU,EAAEN,aAAa,CAAC,QAAQ,CAAC;IACnCO,UAAU,EAAEP,aAAa,CAAC,QAAQ,CAAC;IACnCQ,UAAU,EAAER,aAAa,CAAC,MAAM,CAAC;IACjC;IACA;IACAS,iBAAiB,EAAET,aAAa,CAAC,mBAAmB,CAAC;IACrDU,gBAAgB,EAAEV,aAAa,CAAC,iBAAiB,CAAC;IAClDW,QAAQ,EAAEX,aAAa,CAAC,SAAS,CAAC;IAClCY,QAAQ,EAAEZ,aAAa,CAAC,SAAS,CAAC;IAClCa,gBAAgB,EAAEb,aAAa,CAAC,MAAM;EAC1C,CAAC;AACL,CAAC;AACD,MAAMc,uBAAuB,GAAIf,IAAI,IAAK;EACtC,MAAM;IAAEU,iBAAiB;IAAEH,UAAU;IAAEF,UAAU;IAAES;EAAiB,CAAC,GAAGf,mBAAmB,CAACC,IAAI,CAAC;EACjG,OAAO;IACHgB,YAAY,EAAET,UAAU;IACxBU,sBAAsB,EAAEZ,UAAU;IAClC;IACAa,QAAQ,EAAGC,KAAK,IAAKnB,IAAI,CAAC;MAAEE,IAAI,EAAEL,qBAAqB,CAACsB,KAAK;IAAE,CAAC,CAAC;IACjEC,yBAAyB,EAAEN,gBAAgB;IAC3CJ;EACJ,CAAC;AACL,CAAC;AACD,MAAMW,uBAAuB,GAAIC,KAAK,IAAK;EACvC,MAAMC,YAAY,GAAI5B,eAAe,CAAC2B,KAAK,CAAC,IAAI,CAAC,CAAE;EACnD,MAAM;IAAEE,eAAe;IAAEC,aAAa;IAAEC,mBAAmB;IAAEC,WAAW,EAAEC,KAAK;IAAEC,eAAe,EAAEC,gBAAgB;IAAEC,cAAc,GAAG,IAAI;IAAEC,wBAAwB;IAAEC;EAAU,CAAC,GAAGV,YAAY;EAC/L,MAAM;IAAEW,eAAe,GAAG;EAAG,CAAC,GAAGZ,KAAK,CAACa,OAAO,EAAEC,MAAM,IAAI,CAAC,CAAC;EAC5D;EACA;EACA,MAAMC,IAAI,GAAGd,YAAY,EAAEc,IAAI,IAAIf,KAAK,CAACa,OAAO,EAAEE,IAAI;EACtD,MAAMC,mBAAmB,GAAG,CAAC,EAAER,gBAAgB,IAAIS,MAAM,CAACC,IAAI,CAACV,gBAAgB,CAAC,CAACW,MAAM,GAAG,CAAC,CAAC;EAC5F,MAAMC,UAAU,GAAG9C,aAAa,CAAC0B,KAAK,CAAC;EACvC,MAAMqB,SAAS,GAAGrB,KAAK,CAACsB,MAAM,CAAC,SAAS,CAAC,IAAIF,UAAU,EAAEE,MAAM,CAAC,SAAS,CAAC;EAC1E,MAAMzB,KAAK,GAAGrB,QAAQ,CAACwB,KAAK,EAAEoB,UAAU,CAAC;EACzC;EACA;EACA,MAAMG,UAAU,GAAG,CAAE1B,KAAK,IAAK;IAC3B,QAAQA,KAAK;MACT,KAAK,MAAM;MACX,KAAK,OAAO;QACR,OAAO,aAAa;MACxB,KAAK,eAAe;QAChB,OAAO,eAAe;MAC1B;QACI,OAAO,iBAAiB;IAChC;EACJ,CAAC,EAAEA,KAAK,CAAC;EACT,MAAM2B,MAAM,GAAG;IACXtB,eAAe;IACfqB,UAAU;IACVpB,aAAa;IACbC,mBAAmB;IACnBE,KAAK;IACLU,mBAAmB;IACnBK,SAAS;IACTxB,KAAK;IACLe,eAAe;IACfH,cAAc;IACdC,wBAAwB;IACxBK,IAAI;IACJJ,QAAQ;IACRH;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACJ,CAAC;EACD,OAAOgB,MAAM;AACjB,CAAC;AACD,MAAMC,2BAA2B,GAAIzB,KAAK,IAAK;EAC3C,MAAMC,YAAY,GAAI5B,eAAe,CAAC2B,KAAK,CAAC,IAAI,CAAC,CAAE;EACnD,MAAM;IAAEE,eAAe;IAAEC,aAAa;IAAEC,mBAAmB;IAAEC,WAAW,EAAEqB,YAAY;IAAEjB,cAAc;IAAEC,wBAAwB;IAAEC;EAAU,CAAC,GAAGV,YAAY;EAC5J,MAAM;IAAEW,eAAe,EAAEe,kBAAkB;IAAEC;EAAgB,CAAC,GAAG5B,KAAK,CAACa,OAAO,EAAEC,MAAM,IAAI,CAAC,CAAC;EAC5F,MAAMe,cAAc,GAAGD,eAAe,GAAG,CAAC,CAAC;EAC3C,MAAMR,UAAU,GAAG9C,aAAa,CAAC0B,KAAK,CAAC;EACvC,MAAMqB,SAAS,GAAGrB,KAAK,CAACsB,MAAM,CAAC,SAAS,CAAC,IAAIF,UAAU,EAAEE,MAAM,CAAC,SAAS,CAAC;EAC1E;EACA,MAAMzB,KAAK,GAAGrB,QAAQ,CAACwB,KAAK,EAAEoB,UAAU,CAAC;EACzC,OAAO;IACHlB,eAAe;IACfC,aAAa;IACbC,mBAAmB;IACnBsB,YAAY;IACZC,kBAAkB;IAClBN,SAAS;IACTQ,cAAc;IACdhC,KAAK;IACLY,cAAc;IACdC,wBAAwB;IACxBC;EACJ,CAAC;AACL,CAAC;AACD,MAAMmB,gBAAgB,GAAGA,CAAC;EAAEpD,IAAI;EAAEsB;AAAO,CAAC,KAAK;EAC3C,MAAM+B,gBAAgB,GAAGtD,mBAAmB,CAACC,IAAI,CAAC;EAClD,MAAMsD,cAAc,GAAGjC,uBAAuB,CAACC,KAAK,CAAC;EACrD,OAAO;IACH,GAAG+B,gBAAgB;IACnB,GAAGC;EACP,CAAC;AACL,CAAC;AACD,MAAMC,oBAAoB,GAAGA,CAAC;EAAEvD,IAAI;EAAEsB;AAAO,CAAC,MAAM;EAChD,GAAGP,uBAAuB,CAACf,IAAI,CAAC;EAChC,GAAG+C,2BAA2B,CAACzB,KAAK;AACxC,CAAC,CAAC;AAEF,SAASyB,2BAA2B,EAAEQ,oBAAoB,EAAExD,mBAAmB,EAAEsB,uBAAuB,EAAE+B,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}