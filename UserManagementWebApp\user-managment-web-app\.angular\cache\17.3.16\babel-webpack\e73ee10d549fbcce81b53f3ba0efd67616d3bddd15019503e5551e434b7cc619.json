{"ast": null, "code": "import { v4 } from 'uuid';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst amplifyUuid = v4;\nexport { amplifyUuid };", "map": {"version": 3, "names": ["v4", "amplifyUuid"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/amplifyUuid/index.mjs"], "sourcesContent": ["import { v4 } from 'uuid';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst amplifyUuid = v4;\n\nexport { amplifyUuid };\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,MAAM;;AAEzB;AACA;AACA,MAAMC,WAAW,GAAGD,EAAE;AAEtB,SAASC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}