{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar LogType;\n(function (LogType) {\n  LogType[\"DEBUG\"] = \"DEBUG\";\n  LogType[\"ERROR\"] = \"ERROR\";\n  LogType[\"INFO\"] = \"INFO\";\n  LogType[\"WARN\"] = \"WARN\";\n  LogType[\"VERBOSE\"] = \"VERBOSE\";\n  LogType[\"NONE\"] = \"NONE\";\n})(LogType || (LogType = {}));\nexport { LogType };", "map": {"version": 3, "names": ["LogType"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Logger/types.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar LogType;\n(function (LogType) {\n    LogType[\"DEBUG\"] = \"DEBUG\";\n    LogType[\"ERROR\"] = \"ERROR\";\n    LogType[\"INFO\"] = \"INFO\";\n    LogType[\"WARN\"] = \"WARN\";\n    LogType[\"VERBOSE\"] = \"VERBOSE\";\n    LogType[\"NONE\"] = \"NONE\";\n})(LogType || (LogType = {}));\n\nexport { LogType };\n"], "mappings": "AAAA;AACA;AACA,IAAIA,OAAO;AACX,CAAC,UAAUA,OAAO,EAAE;EAChBA,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO;EAC1BA,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO;EAC1BA,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM;EACxBA,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM;EACxBA,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS;EAC9BA,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM;AAC5B,CAAC,EAAEA,OAAO,KAAKA,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AAE7B,SAASA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}