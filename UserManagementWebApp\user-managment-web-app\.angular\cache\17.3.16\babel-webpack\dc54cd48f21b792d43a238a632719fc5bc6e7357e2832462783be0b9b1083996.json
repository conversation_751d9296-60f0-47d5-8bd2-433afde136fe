{"ast": null, "code": "import { documentExists, processExists } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with react 18.2 - built using Vite\nfunction reactWebDetect() {\n  const elementKeyPrefixedWithReact = key => {\n    return key.startsWith('_react') || key.startsWith('__react');\n  };\n  const elementIsReactEnabled = element => {\n    return Object.keys(element).find(elementKeyPrefixedWithReact);\n  };\n  const allElementsWithId = () => Array.from(document.querySelectorAll('[id]'));\n  return documentExists() && allElementsWithId().some(elementIsReactEnabled);\n}\nfunction reactSSRDetect() {\n  return processExists() && typeof process.env !== 'undefined' && !!Object.keys(process.env).find(key => key.includes('react'));\n}\n// use the some\n\nexport { reactSSRDetect, reactWebDetect };", "map": {"version": 3, "names": ["documentExists", "processExists", "reactWebDetect", "elementKeyPrefixedWithReact", "key", "startsWith", "elementIsReactEnabled", "element", "Object", "keys", "find", "allElementsWithId", "Array", "from", "document", "querySelectorAll", "some", "reactSSRDetect", "process", "env", "includes"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Platform/detection/React.mjs"], "sourcesContent": ["import { documentExists, processExists } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with react 18.2 - built using Vite\nfunction reactWebDetect() {\n    const elementKeyPrefixedWithReact = (key) => {\n        return key.startsWith('_react') || key.startsWith('__react');\n    };\n    const elementIsReactEnabled = (element) => {\n        return Object.keys(element).find(elementKeyPrefixedWithReact);\n    };\n    const allElementsWithId = () => Array.from(document.querySelectorAll('[id]'));\n    return documentExists() && allElementsWithId().some(elementIsReactEnabled);\n}\nfunction reactSSRDetect() {\n    return (processExists() &&\n        typeof process.env !== 'undefined' &&\n        !!Object.keys(process.env).find(key => key.includes('react')));\n}\n// use the some\n\nexport { reactSSRDetect, reactWebDetect };\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,aAAa,QAAQ,eAAe;;AAE7D;AACA;AACA;AACA,SAASC,cAAcA,CAAA,EAAG;EACtB,MAAMC,2BAA2B,GAAIC,GAAG,IAAK;IACzC,OAAOA,GAAG,CAACC,UAAU,CAAC,QAAQ,CAAC,IAAID,GAAG,CAACC,UAAU,CAAC,SAAS,CAAC;EAChE,CAAC;EACD,MAAMC,qBAAqB,GAAIC,OAAO,IAAK;IACvC,OAAOC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,IAAI,CAACP,2BAA2B,CAAC;EACjE,CAAC;EACD,MAAMQ,iBAAiB,GAAGA,CAAA,KAAMC,KAAK,CAACC,IAAI,CAACC,QAAQ,CAACC,gBAAgB,CAAC,MAAM,CAAC,CAAC;EAC7E,OAAOf,cAAc,CAAC,CAAC,IAAIW,iBAAiB,CAAC,CAAC,CAACK,IAAI,CAACV,qBAAqB,CAAC;AAC9E;AACA,SAASW,cAAcA,CAAA,EAAG;EACtB,OAAQhB,aAAa,CAAC,CAAC,IACnB,OAAOiB,OAAO,CAACC,GAAG,KAAK,WAAW,IAClC,CAAC,CAACX,MAAM,CAACC,IAAI,CAACS,OAAO,CAACC,GAAG,CAAC,CAACT,IAAI,CAACN,GAAG,IAAIA,GAAG,CAACgB,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrE;AACA;;AAEA,SAASH,cAAc,EAAEf,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}