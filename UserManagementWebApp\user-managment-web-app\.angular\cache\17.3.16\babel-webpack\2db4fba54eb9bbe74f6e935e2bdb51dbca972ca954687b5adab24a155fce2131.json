{"ast": null, "code": "/**\n * This file contains helpers that lets you easily access current actor's state\n * and context.\n */\n/**\n * Get the state of current actor. This is useful for checking which screen\n * to render: e.g. `getActorState(state).matches('confirmSignUp.edit').\n */\nconst getActorState = state => {\n  return state.context.actorRef?.getSnapshot();\n};\n/**\n * Get the context of current actor. Useful for getting any nested context\n * like remoteError.\n */\nconst getActorContext = state => {\n  return getActorState(state)?.context;\n};\nexport { getActorContext, getActorState };", "map": {"version": 3, "names": ["getActorState", "state", "context", "<PERSON><PERSON><PERSON>", "getSnapshot", "getActorContext"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/helpers/authenticator/actor.mjs"], "sourcesContent": ["/**\n * This file contains helpers that lets you easily access current actor's state\n * and context.\n */\n/**\n * Get the state of current actor. This is useful for checking which screen\n * to render: e.g. `getActorState(state).matches('confirmSignUp.edit').\n */\nconst getActorState = (state) => {\n    return state.context.actorRef?.getSnapshot();\n};\n/**\n * Get the context of current actor. Useful for getting any nested context\n * like remoteError.\n */\nconst getActorContext = (state) => {\n    return getActorState(state)?.context;\n};\n\nexport { getActorContext, getActorState };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,aAAa,GAAIC,KAAK,IAAK;EAC7B,OAAOA,KAAK,CAACC,OAAO,CAACC,QAAQ,EAAEC,WAAW,CAAC,CAAC;AAChD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAIJ,KAAK,IAAK;EAC/B,OAAOD,aAAa,CAACC,KAAK,CAAC,EAAEC,OAAO;AACxC,CAAC;AAED,SAASG,eAAe,EAAEL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}