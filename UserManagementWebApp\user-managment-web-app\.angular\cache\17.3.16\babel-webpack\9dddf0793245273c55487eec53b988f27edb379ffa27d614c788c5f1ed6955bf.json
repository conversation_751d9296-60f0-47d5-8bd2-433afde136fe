{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createUserPoolSerializer = operation => (input, endpoint) => {\n  const headers = getSharedHeaders(operation);\n  const body = JSON.stringify(input);\n  return buildHttpRpcRequest(endpoint, headers, body);\n};\nconst getSharedHeaders = operation => ({\n  'content-type': 'application/x-amz-json-1.1',\n  'x-amz-target': `AWSCognitoIdentityProviderService.${operation}`\n});\nconst buildHttpRpcRequest = ({\n  url\n}, headers, body) => ({\n  headers,\n  url,\n  body,\n  method: 'POST'\n});\nexport { createUserPoolSerializer };", "map": {"version": 3, "names": ["createUserPoolSerializer", "operation", "input", "endpoint", "headers", "getSharedHeaders", "body", "JSON", "stringify", "buildHttpRpcRequest", "url", "method"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/factories/serviceClients/cognitoIdentityProvider/shared/serde/createUserPoolSerializer.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createUserPoolSerializer = (operation) => (input, endpoint) => {\n    const headers = getSharedHeaders(operation);\n    const body = JSON.stringify(input);\n    return buildHttpRpcRequest(endpoint, headers, body);\n};\nconst getSharedHeaders = (operation) => ({\n    'content-type': 'application/x-amz-json-1.1',\n    'x-amz-target': `AWSCognitoIdentityProviderService.${operation}`,\n});\nconst buildHttpRpcRequest = ({ url }, headers, body) => ({\n    headers,\n    url,\n    body,\n    method: 'POST',\n});\n\nexport { createUserPoolSerializer };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,wBAAwB,GAAIC,SAAS,IAAK,CAACC,KAAK,EAAEC,QAAQ,KAAK;EACjE,MAAMC,OAAO,GAAGC,gBAAgB,CAACJ,SAAS,CAAC;EAC3C,MAAMK,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACN,KAAK,CAAC;EAClC,OAAOO,mBAAmB,CAACN,QAAQ,EAAEC,OAAO,EAAEE,IAAI,CAAC;AACvD,CAAC;AACD,MAAMD,gBAAgB,GAAIJ,SAAS,KAAM;EACrC,cAAc,EAAE,4BAA4B;EAC5C,cAAc,EAAE,qCAAqCA,SAAS;AAClE,CAAC,CAAC;AACF,MAAMQ,mBAAmB,GAAGA,CAAC;EAAEC;AAAI,CAAC,EAAEN,OAAO,EAAEE,IAAI,MAAM;EACrDF,OAAO;EACPM,GAAG;EACHJ,IAAI;EACJK,MAAM,EAAE;AACZ,CAAC,CAAC;AAEF,SAASX,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}