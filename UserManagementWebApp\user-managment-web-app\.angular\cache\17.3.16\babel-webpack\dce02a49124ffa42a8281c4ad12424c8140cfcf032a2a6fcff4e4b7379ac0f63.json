{"ast": null, "code": "import { invalidOriginException, invalidRedirectException, invalidPreferredRedirectUrlException } from '../../../../errors/constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/** @internal */\nfunction getRedirectUrl(redirects, preferredRedirectUrl) {\n  if (preferredRedirectUrl) {\n    const redirectUrl = redirects?.find(redirect => redirect === preferredRedirectUrl);\n    if (!redirectUrl) {\n      throw invalidPreferredRedirectUrlException;\n    }\n    return redirectUrl;\n  } else {\n    const redirectUrlFromTheSameOrigin = redirects?.find(isSameOriginAndPathName) ?? redirects?.find(isTheSameDomain);\n    const redirectUrlFromDifferentOrigin = redirects?.find(isHttps) ?? redirects?.find(isHttp);\n    if (redirectUrlFromTheSameOrigin) {\n      return redirectUrlFromTheSameOrigin;\n    } else if (redirectUrlFromDifferentOrigin) {\n      throw invalidOriginException;\n    }\n    throw invalidRedirectException;\n  }\n}\n// origin + pathname => https://example.com/app\nconst isSameOriginAndPathName = redirect => redirect.startsWith(\n// eslint-disable-next-line no-constant-binary-expression\nString(window.location.origin + window.location.pathname ?? '/'));\n// domain => outlook.live.com, github.com\nconst isTheSameDomain = redirect => redirect.includes(String(window.location.hostname));\nconst isHttp = redirect => redirect.startsWith('http://');\nconst isHttps = redirect => redirect.startsWith('https://');\nexport { getRedirectUrl };", "map": {"version": 3, "names": ["invalidOriginException", "invalidRedirectException", "invalidPreferredRedirectUrlException", "getRedirectUrl", "redirects", "preferredRedirectUrl", "redirectUrl", "find", "redirect", "redirectUrlFromTheSameOrigin", "isSameOriginAndPathName", "isTheSameDomain", "redirectUrlFromDifferentOrigin", "isHttps", "isHttp", "startsWith", "String", "window", "location", "origin", "pathname", "includes", "hostname"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/oauth/getRedirectUrl.mjs"], "sourcesContent": ["import { invalidOriginException, invalidRedirectException, invalidPreferredRedirectUrlException } from '../../../../errors/constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/** @internal */\nfunction getRedirectUrl(redirects, preferredRedirectUrl) {\n    if (preferredRedirectUrl) {\n        const redirectUrl = redirects?.find(redirect => redirect === preferredRedirectUrl);\n        if (!redirectUrl) {\n            throw invalidPreferredRedirectUrlException;\n        }\n        return redirectUrl;\n    }\n    else {\n        const redirectUrlFromTheSameOrigin = redirects?.find(isSameOriginAndPathName) ??\n            redirects?.find(isTheSameDomain);\n        const redirectUrlFromDifferentOrigin = redirects?.find(isHttps) ?? redirects?.find(isHttp);\n        if (redirectUrlFromTheSameOrigin) {\n            return redirectUrlFromTheSameOrigin;\n        }\n        else if (redirectUrlFromDifferentOrigin) {\n            throw invalidOriginException;\n        }\n        throw invalidRedirectException;\n    }\n}\n// origin + pathname => https://example.com/app\nconst isSameOriginAndPathName = (redirect) => redirect.startsWith(\n// eslint-disable-next-line no-constant-binary-expression\nString(window.location.origin + window.location.pathname ?? '/'));\n// domain => outlook.live.com, github.com\nconst isTheSameDomain = (redirect) => redirect.includes(String(window.location.hostname));\nconst isHttp = (redirect) => redirect.startsWith('http://');\nconst isHttps = (redirect) => redirect.startsWith('https://');\n\nexport { getRedirectUrl };\n"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,wBAAwB,EAAEC,oCAAoC,QAAQ,kCAAkC;;AAEzI;AACA;AACA;AACA,SAASC,cAAcA,CAACC,SAAS,EAAEC,oBAAoB,EAAE;EACrD,IAAIA,oBAAoB,EAAE;IACtB,MAAMC,WAAW,GAAGF,SAAS,EAAEG,IAAI,CAACC,QAAQ,IAAIA,QAAQ,KAAKH,oBAAoB,CAAC;IAClF,IAAI,CAACC,WAAW,EAAE;MACd,MAAMJ,oCAAoC;IAC9C;IACA,OAAOI,WAAW;EACtB,CAAC,MACI;IACD,MAAMG,4BAA4B,GAAGL,SAAS,EAAEG,IAAI,CAACG,uBAAuB,CAAC,IACzEN,SAAS,EAAEG,IAAI,CAACI,eAAe,CAAC;IACpC,MAAMC,8BAA8B,GAAGR,SAAS,EAAEG,IAAI,CAACM,OAAO,CAAC,IAAIT,SAAS,EAAEG,IAAI,CAACO,MAAM,CAAC;IAC1F,IAAIL,4BAA4B,EAAE;MAC9B,OAAOA,4BAA4B;IACvC,CAAC,MACI,IAAIG,8BAA8B,EAAE;MACrC,MAAMZ,sBAAsB;IAChC;IACA,MAAMC,wBAAwB;EAClC;AACJ;AACA;AACA,MAAMS,uBAAuB,GAAIF,QAAQ,IAAKA,QAAQ,CAACO,UAAU;AACjE;AACAC,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,GAAGF,MAAM,CAACC,QAAQ,CAACE,QAAQ,IAAI,GAAG,CAAC,CAAC;AACjE;AACA,MAAMT,eAAe,GAAIH,QAAQ,IAAKA,QAAQ,CAACa,QAAQ,CAACL,MAAM,CAACC,MAAM,CAACC,QAAQ,CAACI,QAAQ,CAAC,CAAC;AACzF,MAAMR,MAAM,GAAIN,QAAQ,IAAKA,QAAQ,CAACO,UAAU,CAAC,SAAS,CAAC;AAC3D,MAAMF,OAAO,GAAIL,QAAQ,IAAKA,QAAQ,CAACO,UAAU,CAAC,UAAU,CAAC;AAE7D,SAASZ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}