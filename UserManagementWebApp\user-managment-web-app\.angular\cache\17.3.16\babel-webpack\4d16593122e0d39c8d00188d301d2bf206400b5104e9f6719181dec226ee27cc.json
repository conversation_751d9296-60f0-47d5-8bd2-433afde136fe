{"ast": null, "code": "import { getHashedDataAsHex } from './dataHashHelpers.mjs';\nimport { getCanonicalRequest } from './getCanonicalRequest.mjs';\nimport { getSigningKey } from './getSigningKey.mjs';\nimport { getStringToSign } from './getStringToSign.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Calculates and returns an AWS API Signature.\n * https://docs.aws.amazon.com/IAM/latest/UserGuide/create-signed-request.html\n *\n * @param request `HttpRequest` to be signed.\n * @param signRequestOptions `SignRequestOptions` object containing values used to construct the signature.\n * @returns AWS API Signature to sign a request or url with.\n *\n * @internal\n */\nconst getSignature = (request, {\n  credentialScope,\n  longDate,\n  secretAccessKey,\n  shortDate,\n  signingRegion,\n  signingService,\n  uriEscapePath\n}) => {\n  // step 1: create a canonical request\n  const canonicalRequest = getCanonicalRequest(request, uriEscapePath);\n  // step 2: create a hash of the canonical request\n  const hashedRequest = getHashedDataAsHex(null, canonicalRequest);\n  // step 3: create a string to sign\n  const stringToSign = getStringToSign(longDate, credentialScope, hashedRequest);\n  // step 4: calculate the signature\n  const signature = getHashedDataAsHex(getSigningKey(secretAccessKey, shortDate, signingRegion, signingService), stringToSign);\n  return signature;\n};\nexport { getSignature };", "map": {"version": 3, "names": ["getHashedDataAsHex", "getCanonicalRequest", "getSigningKey", "getStringToSign", "getSignature", "request", "credentialScope", "longDate", "secretAccessKey", "shortDate", "signingRegion", "signingService", "uriEscapePath", "canonicalRequest", "hashedRequest", "stringToSign", "signature"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/signer/signatureV4/utils/getSignature.mjs"], "sourcesContent": ["import { getHashedDataAsHex } from './dataHashHelpers.mjs';\nimport { getCanonicalRequest } from './getCanonicalRequest.mjs';\nimport { getSigningKey } from './getSigningKey.mjs';\nimport { getStringToSign } from './getStringToSign.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Calculates and returns an AWS API Signature.\n * https://docs.aws.amazon.com/IAM/latest/UserGuide/create-signed-request.html\n *\n * @param request `HttpRequest` to be signed.\n * @param signRequestOptions `SignRequestOptions` object containing values used to construct the signature.\n * @returns AWS API Signature to sign a request or url with.\n *\n * @internal\n */\nconst getSignature = (request, { credentialScope, longDate, secretAccessKey, shortDate, signingRegion, signingService, uriEscapePath, }) => {\n    // step 1: create a canonical request\n    const canonicalRequest = getCanonicalRequest(request, uriEscapePath);\n    // step 2: create a hash of the canonical request\n    const hashedRequest = getHashedDataAsHex(null, canonicalRequest);\n    // step 3: create a string to sign\n    const stringToSign = getStringToSign(longDate, credentialScope, hashedRequest);\n    // step 4: calculate the signature\n    const signature = getHashedDataAsHex(getSigningKey(secretAccessKey, shortDate, signingRegion, signingService), stringToSign);\n    return signature;\n};\n\nexport { getSignature };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,eAAe,QAAQ,uBAAuB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAGA,CAACC,OAAO,EAAE;EAAEC,eAAe;EAAEC,QAAQ;EAAEC,eAAe;EAAEC,SAAS;EAAEC,aAAa;EAAEC,cAAc;EAAEC;AAAe,CAAC,KAAK;EACxI;EACA,MAAMC,gBAAgB,GAAGZ,mBAAmB,CAACI,OAAO,EAAEO,aAAa,CAAC;EACpE;EACA,MAAME,aAAa,GAAGd,kBAAkB,CAAC,IAAI,EAAEa,gBAAgB,CAAC;EAChE;EACA,MAAME,YAAY,GAAGZ,eAAe,CAACI,QAAQ,EAAED,eAAe,EAAEQ,aAAa,CAAC;EAC9E;EACA,MAAME,SAAS,GAAGhB,kBAAkB,CAACE,aAAa,CAACM,eAAe,EAAEC,SAAS,EAAEC,aAAa,EAAEC,cAAc,CAAC,EAAEI,YAAY,CAAC;EAC5H,OAAOC,SAAS;AACpB,CAAC;AAED,SAASZ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}