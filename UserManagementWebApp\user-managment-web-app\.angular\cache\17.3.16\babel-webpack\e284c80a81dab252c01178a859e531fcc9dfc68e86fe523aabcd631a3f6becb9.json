{"ast": null, "code": "const card = {\n  backgroundColor: {\n    value: '{colors.background.primary.value}'\n  },\n  borderRadius: {\n    value: '{radii.xs.value}'\n  },\n  borderWidth: {\n    value: '0'\n  },\n  borderStyle: {\n    value: 'solid'\n  },\n  borderColor: {\n    value: 'transparent'\n  },\n  boxShadow: {\n    value: 'none'\n  },\n  padding: {\n    value: '{space.medium.value}'\n  },\n  outlined: {\n    backgroundColor: {\n      value: '{components.card.backgroundColor.value}'\n    },\n    borderRadius: {\n      value: '{radii.xs.value}'\n    },\n    borderWidth: {\n      value: '{borderWidths.small.value}'\n    },\n    borderStyle: {\n      value: 'solid'\n    },\n    borderColor: {\n      value: '{colors.border.primary.value}'\n    },\n    boxShadow: {\n      value: '{components.card.boxShadow.value}'\n    }\n  },\n  elevated: {\n    backgroundColor: {\n      value: '{components.card.backgroundColor.value}'\n    },\n    borderRadius: {\n      value: '{radii.xs.value}'\n    },\n    borderWidth: {\n      value: '0'\n    },\n    borderStyle: {\n      value: 'solid'\n    },\n    borderColor: {\n      value: 'transparent'\n    },\n    boxShadow: {\n      value: '{shadows.medium.value}'\n    }\n  }\n};\nexport { card };", "map": {"version": 3, "names": ["card", "backgroundColor", "value", "borderRadius", "borderWidth", "borderStyle", "borderColor", "boxShadow", "padding", "outlined", "elevated"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/card.mjs"], "sourcesContent": ["const card = {\n    backgroundColor: { value: '{colors.background.primary.value}' },\n    borderRadius: { value: '{radii.xs.value}' },\n    borderWidth: { value: '0' },\n    borderStyle: { value: 'solid' },\n    borderColor: { value: 'transparent' },\n    boxShadow: { value: 'none' },\n    padding: { value: '{space.medium.value}' },\n    outlined: {\n        backgroundColor: { value: '{components.card.backgroundColor.value}' },\n        borderRadius: { value: '{radii.xs.value}' },\n        borderWidth: { value: '{borderWidths.small.value}' },\n        borderStyle: { value: 'solid' },\n        borderColor: { value: '{colors.border.primary.value}' },\n        boxShadow: { value: '{components.card.boxShadow.value}' },\n    },\n    elevated: {\n        backgroundColor: { value: '{components.card.backgroundColor.value}' },\n        borderRadius: { value: '{radii.xs.value}' },\n        borderWidth: { value: '0' },\n        borderStyle: { value: 'solid' },\n        borderColor: { value: 'transparent' },\n        boxShadow: { value: '{shadows.medium.value}' },\n    },\n};\n\nexport { card };\n"], "mappings": "AAAA,MAAMA,IAAI,GAAG;EACTC,eAAe,EAAE;IAAEC,KAAK,EAAE;EAAoC,CAAC;EAC/DC,YAAY,EAAE;IAAED,KAAK,EAAE;EAAmB,CAAC;EAC3CE,WAAW,EAAE;IAAEF,KAAK,EAAE;EAAI,CAAC;EAC3BG,WAAW,EAAE;IAAEH,KAAK,EAAE;EAAQ,CAAC;EAC/BI,WAAW,EAAE;IAAEJ,KAAK,EAAE;EAAc,CAAC;EACrCK,SAAS,EAAE;IAAEL,KAAK,EAAE;EAAO,CAAC;EAC5BM,OAAO,EAAE;IAAEN,KAAK,EAAE;EAAuB,CAAC;EAC1CO,QAAQ,EAAE;IACNR,eAAe,EAAE;MAAEC,KAAK,EAAE;IAA0C,CAAC;IACrEC,YAAY,EAAE;MAAED,KAAK,EAAE;IAAmB,CAAC;IAC3CE,WAAW,EAAE;MAAEF,KAAK,EAAE;IAA6B,CAAC;IACpDG,WAAW,EAAE;MAAEH,KAAK,EAAE;IAAQ,CAAC;IAC/BI,WAAW,EAAE;MAAEJ,KAAK,EAAE;IAAgC,CAAC;IACvDK,SAAS,EAAE;MAAEL,KAAK,EAAE;IAAoC;EAC5D,CAAC;EACDQ,QAAQ,EAAE;IACNT,eAAe,EAAE;MAAEC,KAAK,EAAE;IAA0C,CAAC;IACrEC,YAAY,EAAE;MAAED,KAAK,EAAE;IAAmB,CAAC;IAC3CE,WAAW,EAAE;MAAEF,KAAK,EAAE;IAAI,CAAC;IAC3BG,WAAW,EAAE;MAAEH,KAAK,EAAE;IAAQ,CAAC;IAC/BI,WAAW,EAAE;MAAEJ,KAAK,EAAE;IAAc,CAAC;IACrCK,SAAS,EAAE;MAAEL,KAAK,EAAE;IAAyB;EACjD;AACJ,CAAC;AAED,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}