{"ast": null, "code": "const space = {\n  zero: {\n    value: '0'\n  },\n  xxxs: {\n    value: '0.25rem'\n  },\n  xxs: {\n    value: '0.375rem'\n  },\n  xs: {\n    value: '0.5rem'\n  },\n  small: {\n    value: '0.75rem'\n  },\n  medium: {\n    value: '1rem'\n  },\n  large: {\n    value: '1.5rem'\n  },\n  xl: {\n    value: '2.0rem'\n  },\n  xxl: {\n    value: '3.0rem'\n  },\n  xxxl: {\n    value: '4.5rem'\n  },\n  relative: {\n    //creating a second set of sizes using em which will be sized relative to a parent instead of the root\n    xxxs: {\n      value: '0.25em'\n    },\n    xxs: {\n      value: '0.375em'\n    },\n    xs: {\n      value: '0.5em'\n    },\n    small: {\n      value: '0.75em'\n    },\n    medium: {\n      value: '1em'\n    },\n    large: {\n      value: '1.5em'\n    },\n    xl: {\n      value: '2.0em'\n    },\n    xxl: {\n      value: '3.0em'\n    },\n    xxxl: {\n      value: '4.5em'\n    },\n    full: {\n      value: '100%'\n    }\n  }\n};\n// I want to be able to pass in a Theme object that has extra tokens\n// and it returns that same object type WITH the extra tokens\n\nexport { space };", "map": {"version": 3, "names": ["space", "zero", "value", "xxxs", "xxs", "xs", "small", "medium", "large", "xl", "xxl", "xxxl", "relative", "full"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/space.mjs"], "sourcesContent": ["const space = {\n    zero: { value: '0' },\n    xxxs: { value: '0.25rem' },\n    xxs: { value: '0.375rem' },\n    xs: { value: '0.5rem' },\n    small: { value: '0.75rem' },\n    medium: { value: '1rem' },\n    large: { value: '1.5rem' },\n    xl: { value: '2.0rem' },\n    xxl: { value: '3.0rem' },\n    xxxl: { value: '4.5rem' },\n    relative: {\n        //creating a second set of sizes using em which will be sized relative to a parent instead of the root\n        xxxs: { value: '0.25em' },\n        xxs: { value: '0.375em' },\n        xs: { value: '0.5em' },\n        small: { value: '0.75em' },\n        medium: { value: '1em' },\n        large: { value: '1.5em' },\n        xl: { value: '2.0em' },\n        xxl: { value: '3.0em' },\n        xxxl: { value: '4.5em' },\n        full: { value: '100%' },\n    },\n};\n// I want to be able to pass in a Theme object that has extra tokens\n// and it returns that same object type WITH the extra tokens\n\nexport { space };\n"], "mappings": "AAAA,MAAMA,KAAK,GAAG;EACVC,IAAI,EAAE;IAAEC,KAAK,EAAE;EAAI,CAAC;EACpBC,IAAI,EAAE;IAAED,KAAK,EAAE;EAAU,CAAC;EAC1BE,GAAG,EAAE;IAAEF,KAAK,EAAE;EAAW,CAAC;EAC1BG,EAAE,EAAE;IAAEH,KAAK,EAAE;EAAS,CAAC;EACvBI,KAAK,EAAE;IAAEJ,KAAK,EAAE;EAAU,CAAC;EAC3BK,MAAM,EAAE;IAAEL,KAAK,EAAE;EAAO,CAAC;EACzBM,KAAK,EAAE;IAAEN,KAAK,EAAE;EAAS,CAAC;EAC1BO,EAAE,EAAE;IAAEP,KAAK,EAAE;EAAS,CAAC;EACvBQ,GAAG,EAAE;IAAER,KAAK,EAAE;EAAS,CAAC;EACxBS,IAAI,EAAE;IAAET,KAAK,EAAE;EAAS,CAAC;EACzBU,QAAQ,EAAE;IACN;IACAT,IAAI,EAAE;MAAED,KAAK,EAAE;IAAS,CAAC;IACzBE,GAAG,EAAE;MAAEF,KAAK,EAAE;IAAU,CAAC;IACzBG,EAAE,EAAE;MAAEH,KAAK,EAAE;IAAQ,CAAC;IACtBI,KAAK,EAAE;MAAEJ,KAAK,EAAE;IAAS,CAAC;IAC1BK,MAAM,EAAE;MAAEL,KAAK,EAAE;IAAM,CAAC;IACxBM,KAAK,EAAE;MAAEN,KAAK,EAAE;IAAQ,CAAC;IACzBO,EAAE,EAAE;MAAEP,KAAK,EAAE;IAAQ,CAAC;IACtBQ,GAAG,EAAE;MAAER,KAAK,EAAE;IAAQ,CAAC;IACvBS,IAAI,EAAE;MAAET,KAAK,EAAE;IAAQ,CAAC;IACxBW,IAAI,EAAE;MAAEX,KAAK,EAAE;IAAO;EAC1B;AACJ,CAAC;AACD;AACA;;AAEA,SAASF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}