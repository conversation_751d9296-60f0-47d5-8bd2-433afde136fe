{"ast": null, "code": "import { AuthError } from '../../../errors/AuthError.mjs';\nimport { TOKEN_REFRESH_EXCEPTION, USER_UNAUTHENTICATED_EXCEPTION, DEVICE_METADATA_NOT_FOUND_EXCEPTION } from '../../../errors/constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction isTypeUserPoolConfig(authConfig) {\n  if (authConfig && authConfig.Cognito.userPoolId && authConfig.Cognito.userPoolClientId) {\n    return true;\n  }\n  return false;\n}\nfunction assertAuthTokens(tokens) {\n  if (!tokens || !tokens.accessToken) {\n    throw new AuthError({\n      name: USER_UNAUTHENTICATED_EXCEPTION,\n      message: 'User needs to be authenticated to call this API.',\n      recoverySuggestion: 'Sign in before calling this API again.'\n    });\n  }\n}\nfunction assertIdTokenInAuthTokens(tokens) {\n  if (!tokens || !tokens.idToken) {\n    throw new AuthError({\n      name: USER_UNAUTHENTICATED_EXCEPTION,\n      message: 'User needs to be authenticated to call this API.',\n      recoverySuggestion: 'Sign in before calling this API again.'\n    });\n  }\n}\nconst oAuthTokenRefreshException = new AuthError({\n  name: TOKEN_REFRESH_EXCEPTION,\n  message: `Token refresh is not supported when authenticated with the 'implicit grant' (token) oauth flow. \n\tPlease change your oauth configuration to use 'code grant' flow.`,\n  recoverySuggestion: `Please logout and change your Amplify configuration to use \"code grant\" flow. \n\tE.g { responseType: 'code' }`\n});\nconst tokenRefreshException = new AuthError({\n  name: USER_UNAUTHENTICATED_EXCEPTION,\n  message: 'User needs to be authenticated to call this API.',\n  recoverySuggestion: 'Sign in before calling this API again.'\n});\nfunction assertAuthTokensWithRefreshToken(tokens) {\n  if (isAuthenticatedWithImplicitOauthFlow(tokens)) {\n    throw oAuthTokenRefreshException;\n  }\n  if (!isAuthenticatedWithRefreshToken(tokens)) {\n    throw tokenRefreshException;\n  }\n}\nfunction assertDeviceMetadata(deviceMetadata) {\n  if (!deviceMetadata || !deviceMetadata.deviceKey || !deviceMetadata.deviceGroupKey || !deviceMetadata.randomPassword) {\n    throw new AuthError({\n      name: DEVICE_METADATA_NOT_FOUND_EXCEPTION,\n      message: 'Either deviceKey, deviceGroupKey or secretPassword were not found during the sign-in process.',\n      recoverySuggestion: 'Make sure to not clear storage after calling the signIn API.'\n    });\n  }\n}\nconst OAuthStorageKeys = {\n  inflightOAuth: 'inflightOAuth',\n  oauthSignIn: 'oauthSignIn',\n  oauthPKCE: 'oauthPKCE',\n  oauthState: 'oauthState'\n};\nfunction isAuthenticated(tokens) {\n  return tokens?.accessToken || tokens?.idToken;\n}\nfunction isAuthenticatedWithRefreshToken(tokens) {\n  return isAuthenticated(tokens) && tokens?.refreshToken;\n}\nfunction isAuthenticatedWithImplicitOauthFlow(tokens) {\n  return isAuthenticated(tokens) && !tokens?.refreshToken;\n}\nexport { OAuthStorageKeys, assertAuthTokens, assertAuthTokensWithRefreshToken, assertDeviceMetadata, assertIdTokenInAuthTokens, isTypeUserPoolConfig, oAuthTokenRefreshException, tokenRefreshException };", "map": {"version": 3, "names": ["<PERSON>th<PERSON><PERSON><PERSON>", "TOKEN_REFRESH_EXCEPTION", "USER_UNAUTHENTICATED_EXCEPTION", "DEVICE_METADATA_NOT_FOUND_EXCEPTION", "isTypeUserPoolConfig", "authConfig", "Cognito", "userPoolId", "userPoolClientId", "assertAuthTokens", "tokens", "accessToken", "name", "message", "recoverySuggestion", "assertIdTokenInAuthTokens", "idToken", "oAuthTokenRefreshException", "tokenRefreshException", "assertAuthTokensWithRefreshToken", "isAuthenticatedWithImplicitOauthFlow", "isAuthenticatedWithRefreshToken", "assertDeviceMetadata", "deviceMetadata", "deviceKey", "deviceGroupKey", "randomPassword", "OAuthStorageKeys", "inflightOAuth", "oauthSignIn", "oauthPKCE", "oauthState", "isAuthenticated", "refreshToken"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/types.mjs"], "sourcesContent": ["import { AuthError } from '../../../errors/AuthError.mjs';\nimport { TOKEN_REFRESH_EXCEPTION, USER_UNAUTHENTICATED_EXCEPTION, DEVICE_METADATA_NOT_FOUND_EXCEPTION } from '../../../errors/constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction isTypeUserPoolConfig(authConfig) {\n    if (authConfig &&\n        authConfig.Cognito.userPoolId &&\n        authConfig.Cognito.userPoolClientId) {\n        return true;\n    }\n    return false;\n}\nfunction assertAuthTokens(tokens) {\n    if (!tokens || !tokens.accessToken) {\n        throw new AuthError({\n            name: USER_UNAUTHENTICATED_EXCEPTION,\n            message: 'User needs to be authenticated to call this API.',\n            recoverySuggestion: 'Sign in before calling this API again.',\n        });\n    }\n}\nfunction assertIdTokenInAuthTokens(tokens) {\n    if (!tokens || !tokens.idToken) {\n        throw new AuthError({\n            name: USER_UNAUTHENTICATED_EXCEPTION,\n            message: 'User needs to be authenticated to call this API.',\n            recoverySuggestion: 'Sign in before calling this API again.',\n        });\n    }\n}\nconst oAuthTokenRefreshException = new AuthError({\n    name: TOKEN_REFRESH_EXCEPTION,\n    message: `Token refresh is not supported when authenticated with the 'implicit grant' (token) oauth flow. \n\tPlease change your oauth configuration to use 'code grant' flow.`,\n    recoverySuggestion: `Please logout and change your Amplify configuration to use \"code grant\" flow. \n\tE.g { responseType: 'code' }`,\n});\nconst tokenRefreshException = new AuthError({\n    name: USER_UNAUTHENTICATED_EXCEPTION,\n    message: 'User needs to be authenticated to call this API.',\n    recoverySuggestion: 'Sign in before calling this API again.',\n});\nfunction assertAuthTokensWithRefreshToken(tokens) {\n    if (isAuthenticatedWithImplicitOauthFlow(tokens)) {\n        throw oAuthTokenRefreshException;\n    }\n    if (!isAuthenticatedWithRefreshToken(tokens)) {\n        throw tokenRefreshException;\n    }\n}\nfunction assertDeviceMetadata(deviceMetadata) {\n    if (!deviceMetadata ||\n        !deviceMetadata.deviceKey ||\n        !deviceMetadata.deviceGroupKey ||\n        !deviceMetadata.randomPassword) {\n        throw new AuthError({\n            name: DEVICE_METADATA_NOT_FOUND_EXCEPTION,\n            message: 'Either deviceKey, deviceGroupKey or secretPassword were not found during the sign-in process.',\n            recoverySuggestion: 'Make sure to not clear storage after calling the signIn API.',\n        });\n    }\n}\nconst OAuthStorageKeys = {\n    inflightOAuth: 'inflightOAuth',\n    oauthSignIn: 'oauthSignIn',\n    oauthPKCE: 'oauthPKCE',\n    oauthState: 'oauthState',\n};\nfunction isAuthenticated(tokens) {\n    return tokens?.accessToken || tokens?.idToken;\n}\nfunction isAuthenticatedWithRefreshToken(tokens) {\n    return isAuthenticated(tokens) && tokens?.refreshToken;\n}\nfunction isAuthenticatedWithImplicitOauthFlow(tokens) {\n    return isAuthenticated(tokens) && !tokens?.refreshToken;\n}\n\nexport { OAuthStorageKeys, assertAuthTokens, assertAuthTokensWithRefreshToken, assertDeviceMetadata, assertIdTokenInAuthTokens, isTypeUserPoolConfig, oAuthTokenRefreshException, tokenRefreshException };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,+BAA+B;AACzD,SAASC,uBAAuB,EAAEC,8BAA8B,EAAEC,mCAAmC,QAAQ,+BAA+B;;AAE5I;AACA;AACA,SAASC,oBAAoBA,CAACC,UAAU,EAAE;EACtC,IAAIA,UAAU,IACVA,UAAU,CAACC,OAAO,CAACC,UAAU,IAC7BF,UAAU,CAACC,OAAO,CAACE,gBAAgB,EAAE;IACrC,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB;AACA,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAC9B,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACC,WAAW,EAAE;IAChC,MAAM,IAAIX,SAAS,CAAC;MAChBY,IAAI,EAAEV,8BAA8B;MACpCW,OAAO,EAAE,kDAAkD;MAC3DC,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;AACJ;AACA,SAASC,yBAAyBA,CAACL,MAAM,EAAE;EACvC,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACM,OAAO,EAAE;IAC5B,MAAM,IAAIhB,SAAS,CAAC;MAChBY,IAAI,EAAEV,8BAA8B;MACpCW,OAAO,EAAE,kDAAkD;MAC3DC,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;AACJ;AACA,MAAMG,0BAA0B,GAAG,IAAIjB,SAAS,CAAC;EAC7CY,IAAI,EAAEX,uBAAuB;EAC7BY,OAAO,EAAE;AACb,kEAAkE;EAC9DC,kBAAkB,EAAE;AACxB;AACA,CAAC,CAAC;AACF,MAAMI,qBAAqB,GAAG,IAAIlB,SAAS,CAAC;EACxCY,IAAI,EAAEV,8BAA8B;EACpCW,OAAO,EAAE,kDAAkD;EAC3DC,kBAAkB,EAAE;AACxB,CAAC,CAAC;AACF,SAASK,gCAAgCA,CAACT,MAAM,EAAE;EAC9C,IAAIU,oCAAoC,CAACV,MAAM,CAAC,EAAE;IAC9C,MAAMO,0BAA0B;EACpC;EACA,IAAI,CAACI,+BAA+B,CAACX,MAAM,CAAC,EAAE;IAC1C,MAAMQ,qBAAqB;EAC/B;AACJ;AACA,SAASI,oBAAoBA,CAACC,cAAc,EAAE;EAC1C,IAAI,CAACA,cAAc,IACf,CAACA,cAAc,CAACC,SAAS,IACzB,CAACD,cAAc,CAACE,cAAc,IAC9B,CAACF,cAAc,CAACG,cAAc,EAAE;IAChC,MAAM,IAAI1B,SAAS,CAAC;MAChBY,IAAI,EAAET,mCAAmC;MACzCU,OAAO,EAAE,+FAA+F;MACxGC,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;AACJ;AACA,MAAMa,gBAAgB,GAAG;EACrBC,aAAa,EAAE,eAAe;EAC9BC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE,WAAW;EACtBC,UAAU,EAAE;AAChB,CAAC;AACD,SAASC,eAAeA,CAACtB,MAAM,EAAE;EAC7B,OAAOA,MAAM,EAAEC,WAAW,IAAID,MAAM,EAAEM,OAAO;AACjD;AACA,SAASK,+BAA+BA,CAACX,MAAM,EAAE;EAC7C,OAAOsB,eAAe,CAACtB,MAAM,CAAC,IAAIA,MAAM,EAAEuB,YAAY;AAC1D;AACA,SAASb,oCAAoCA,CAACV,MAAM,EAAE;EAClD,OAAOsB,eAAe,CAACtB,MAAM,CAAC,IAAI,CAACA,MAAM,EAAEuB,YAAY;AAC3D;AAEA,SAASN,gBAAgB,EAAElB,gBAAgB,EAAEU,gCAAgC,EAAEG,oBAAoB,EAAEP,yBAAyB,EAAEX,oBAAoB,EAAEa,0BAA0B,EAAEC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}