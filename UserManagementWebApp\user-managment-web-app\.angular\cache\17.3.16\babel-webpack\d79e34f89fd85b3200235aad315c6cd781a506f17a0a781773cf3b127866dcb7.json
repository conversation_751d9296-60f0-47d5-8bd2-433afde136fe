{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { getUserContextData } from '../utils/userContextData.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../types/errors.mjs';\nimport { createResendConfirmationCodeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createResendConfirmationCodeClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Resend the confirmation code while signing up\n *\n * @param input -  The ResendSignUpCodeInput object\n * @returns ResendSignUpCodeOutput\n * @throws service: {@link ResendConfirmationException } - Cognito service errors thrown when resending the code.\n * @throws validation: {@link AuthValidationErrorCode } - Validation errors thrown either username are not defined.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nfunction resendSignUpCode(_x) {\n  return _resendSignUpCode.apply(this, arguments);\n}\nfunction _resendSignUpCode() {\n  _resendSignUpCode = _asyncToGenerator(function* (input) {\n    const {\n      username\n    } = input;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptySignUpUsername);\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolClientId,\n      userPoolId,\n      userPoolEndpoint\n    } = authConfig;\n    const clientMetadata = input.options?.clientMetadata;\n    const UserContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const resendConfirmationCode = createResendConfirmationCodeClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const {\n      CodeDeliveryDetails\n    } = yield resendConfirmationCode({\n      region: getRegionFromUserPoolId(authConfig.userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.ResendSignUpCode)\n    }, {\n      Username: username,\n      ClientMetadata: clientMetadata,\n      ClientId: authConfig.userPoolClientId,\n      UserContextData\n    });\n    const {\n      DeliveryMedium,\n      AttributeName,\n      Destination\n    } = {\n      ...CodeDeliveryDetails\n    };\n    return {\n      destination: Destination,\n      deliveryMedium: DeliveryMedium,\n      attributeName: AttributeName ? AttributeName : undefined\n    };\n  });\n  return _resendSignUpCode.apply(this, arguments);\n}\nexport { resendSignUpCode };", "map": {"version": 3, "names": ["Amplify", "assertTokenProviderConfig", "AuthAction", "assertValidationError", "AuthValidationErrorCode", "getRegionFromUserPoolId", "getAuthUserAgentValue", "getUserContextData", "createResendConfirmationCodeClient", "createCognitoUserPoolEndpointResolver", "resendSignUpCode", "_x", "_resendSignUpCode", "apply", "arguments", "_asyncToGenerator", "input", "username", "EmptySignUpUsername", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userPoolClientId", "userPoolId", "userPoolEndpoint", "clientMetadata", "options", "UserContextData", "resendConfirmationCode", "endpointResolver", "endpointOverride", "CodeDeliveryDetails", "region", "userAgentValue", "ResendSignUpCode", "Username", "ClientMetadata", "ClientId", "DeliveryMedium", "AttributeName", "Destination", "destination", "deliveryMedium", "attributeName", "undefined"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/resendSignUpCode.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { getUserContextData } from '../utils/userContextData.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../types/errors.mjs';\nimport { createResendConfirmationCodeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createResendConfirmationCodeClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Resend the confirmation code while signing up\n *\n * @param input -  The ResendSignUpCodeInput object\n * @returns ResendSignUpCodeOutput\n * @throws service: {@link ResendConfirmationException } - Cognito service errors thrown when resending the code.\n * @throws validation: {@link AuthValidationErrorCode } - Validation errors thrown either username are not defined.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nasync function resendSignUpCode(input) {\n    const { username } = input;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptySignUpUsername);\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolClientId, userPoolId, userPoolEndpoint } = authConfig;\n    const clientMetadata = input.options?.clientMetadata;\n    const UserContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const resendConfirmationCode = createResendConfirmationCodeClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const { CodeDeliveryDetails } = await resendConfirmationCode({\n        region: getRegionFromUserPoolId(authConfig.userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ResendSignUpCode),\n    }, {\n        Username: username,\n        ClientMetadata: clientMetadata,\n        ClientId: authConfig.userPoolClientId,\n        UserContextData,\n    });\n    const { DeliveryMedium, AttributeName, Destination } = {\n        ...CodeDeliveryDetails,\n    };\n    return {\n        destination: Destination,\n        deliveryMedium: DeliveryMedium,\n        attributeName: AttributeName\n            ? AttributeName\n            : undefined,\n    };\n}\n\nexport { resendSignUpCode };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,qBAAqB;AAC5B,SAASC,kCAAkC,QAAQ,6GAA6G;AAChK,SAASC,qCAAqC,QAAQ,wDAAwD;;AAE9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SASeC,gBAAgBA,CAAAC,EAAA;EAAA,OAAAC,iBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,kBAAA;EAAAA,iBAAA,GAAAG,iBAAA,CAA/B,WAAgCC,KAAK,EAAE;IACnC,MAAM;MAAEC;IAAS,CAAC,GAAGD,KAAK;IAC1Bb,qBAAqB,CAAC,CAAC,CAACc,QAAQ,EAAEb,uBAAuB,CAACc,mBAAmB,CAAC;IAC9E,MAAMC,UAAU,GAAGnB,OAAO,CAACoB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDrB,yBAAyB,CAACkB,UAAU,CAAC;IACrC,MAAM;MAAEI,gBAAgB;MAAEC,UAAU;MAAEC;IAAiB,CAAC,GAAGN,UAAU;IACrE,MAAMO,cAAc,GAAGV,KAAK,CAACW,OAAO,EAAED,cAAc;IACpD,MAAME,eAAe,GAAGrB,kBAAkB,CAAC;MACvCU,QAAQ;MACRO,UAAU;MACVD;IACJ,CAAC,CAAC;IACF,MAAMM,sBAAsB,GAAGrB,kCAAkC,CAAC;MAC9DsB,gBAAgB,EAAErB,qCAAqC,CAAC;QACpDsB,gBAAgB,EAAEN;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAM;MAAEO;IAAoB,CAAC,SAASH,sBAAsB,CAAC;MACzDI,MAAM,EAAE5B,uBAAuB,CAACc,UAAU,CAACK,UAAU,CAAC;MACtDU,cAAc,EAAE5B,qBAAqB,CAACJ,UAAU,CAACiC,gBAAgB;IACrE,CAAC,EAAE;MACCC,QAAQ,EAAEnB,QAAQ;MAClBoB,cAAc,EAAEX,cAAc;MAC9BY,QAAQ,EAAEnB,UAAU,CAACI,gBAAgB;MACrCK;IACJ,CAAC,CAAC;IACF,MAAM;MAAEW,cAAc;MAAEC,aAAa;MAAEC;IAAY,CAAC,GAAG;MACnD,GAAGT;IACP,CAAC;IACD,OAAO;MACHU,WAAW,EAAED,WAAW;MACxBE,cAAc,EAAEJ,cAAc;MAC9BK,aAAa,EAAEJ,aAAa,GACtBA,aAAa,GACbK;IACV,CAAC;EACL,CAAC;EAAA,OAAAjC,iBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}