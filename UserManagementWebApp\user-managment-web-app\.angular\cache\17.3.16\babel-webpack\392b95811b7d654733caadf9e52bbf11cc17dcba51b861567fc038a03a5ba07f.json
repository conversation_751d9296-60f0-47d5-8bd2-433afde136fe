{"ast": null, "code": "const rating = {\n  large: {\n    size: {\n      value: '{fontSizes.xxxl.value}'\n    }\n  },\n  default: {\n    size: {\n      value: '{fontSizes.xl.value}'\n    }\n  },\n  small: {\n    size: {\n      value: '{fontSizes.small.value}'\n    }\n  },\n  filled: {\n    color: {\n      value: '{colors.secondary.80.value}'\n    }\n  },\n  empty: {\n    color: {\n      value: '{colors.background.tertiary.value}'\n    }\n  }\n};\nexport { rating };", "map": {"version": 3, "names": ["rating", "large", "size", "value", "default", "small", "filled", "color", "empty"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/rating.mjs"], "sourcesContent": ["const rating = {\n    large: { size: { value: '{fontSizes.xxxl.value}' } },\n    default: { size: { value: '{fontSizes.xl.value}' } },\n    small: { size: { value: '{fontSizes.small.value}' } },\n    filled: { color: { value: '{colors.secondary.80.value}' } },\n    empty: { color: { value: '{colors.background.tertiary.value}' } },\n};\n\nexport { rating };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACXC,KAAK,EAAE;IAAEC,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAyB;EAAE,CAAC;EACpDC,OAAO,EAAE;IAAEF,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAuB;EAAE,CAAC;EACpDE,KAAK,EAAE;IAAEH,IAAI,EAAE;MAAEC,KAAK,EAAE;IAA0B;EAAE,CAAC;EACrDG,MAAM,EAAE;IAAEC,KAAK,EAAE;MAAEJ,KAAK,EAAE;IAA8B;EAAE,CAAC;EAC3DK,KAAK,EAAE;IAAED,KAAK,EAAE;MAAEJ,KAAK,EAAE;IAAqC;EAAE;AACpE,CAAC;AAED,SAASH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}