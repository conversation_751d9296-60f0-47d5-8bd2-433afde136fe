{"ast": null, "code": "import { partitionsInfo, defaultPartition } from './partitions.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Get the AWS Services endpoint URL's DNS suffix for a given region. A typical AWS regional service endpoint URL will\n * follow this pattern: {endpointPrefix}.{region}.{dnsSuffix}. For example, the endpoint URL for Cognito Identity in\n * us-east-1 will be cognito-identity.us-east-1.amazonaws.com. Here the DnsSuffix is `amazonaws.com`.\n *\n * @param region\n * @returns The DNS suffix\n *\n * @internal\n */\nconst getDnsSuffix = region => {\n  const {\n    partitions\n  } = partitionsInfo;\n  for (const {\n    regions,\n    outputs,\n    regionRegex\n  } of partitions) {\n    const regex = new RegExp(regionRegex);\n    if (regions.includes(region) || regex.test(region)) {\n      return outputs.dnsSuffix;\n    }\n  }\n  return defaultPartition.outputs.dnsSuffix;\n};\nexport { getDnsSuffix };", "map": {"version": 3, "names": ["partitionsInfo", "defaultPartition", "getDnsSuffix", "region", "partitions", "regions", "outputs", "regionRegex", "regex", "RegExp", "includes", "test", "dnsSuffix"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/endpoints/getDnsSuffix.mjs"], "sourcesContent": ["import { partitionsInfo, defaultPartition } from './partitions.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Get the AWS Services endpoint URL's DNS suffix for a given region. A typical AWS regional service endpoint URL will\n * follow this pattern: {endpointPrefix}.{region}.{dnsSuffix}. For example, the endpoint URL for Cognito Identity in\n * us-east-1 will be cognito-identity.us-east-1.amazonaws.com. Here the DnsSuffix is `amazonaws.com`.\n *\n * @param region\n * @returns The DNS suffix\n *\n * @internal\n */\nconst getDnsSuffix = (region) => {\n    const { partitions } = partitionsInfo;\n    for (const { regions, outputs, regionRegex } of partitions) {\n        const regex = new RegExp(regionRegex);\n        if (regions.includes(region) || regex.test(region)) {\n            return outputs.dnsSuffix;\n        }\n    }\n    return defaultPartition.outputs.dnsSuffix;\n};\n\nexport { getDnsSuffix };\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,gBAAgB,QAAQ,kBAAkB;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAIC,MAAM,IAAK;EAC7B,MAAM;IAAEC;EAAW,CAAC,GAAGJ,cAAc;EACrC,KAAK,MAAM;IAAEK,OAAO;IAAEC,OAAO;IAAEC;EAAY,CAAC,IAAIH,UAAU,EAAE;IACxD,MAAMI,KAAK,GAAG,IAAIC,MAAM,CAACF,WAAW,CAAC;IACrC,IAAIF,OAAO,CAACK,QAAQ,CAACP,MAAM,CAAC,IAAIK,KAAK,CAACG,IAAI,CAACR,MAAM,CAAC,EAAE;MAChD,OAAOG,OAAO,CAACM,SAAS;IAC5B;EACJ;EACA,OAAOX,gBAAgB,CAACK,OAAO,CAACM,SAAS;AAC7C,CAAC;AAED,SAASV,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}