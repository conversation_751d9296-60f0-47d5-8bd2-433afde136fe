{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport bowser from \"bowser\";\n/**\n * Default provider to the user agent in browsers. It's a best effort to infer\n * the device information. It uses bowser library to detect the browser and virsion\n */\nexport var defaultUserAgent = function (_a) {\n  var serviceId = _a.serviceId,\n    clientVersion = _a.clientVersion;\n  return function () {\n    return __awaiter(void 0, void 0, void 0, function () {\n      var parsedUA, sections;\n      var _a, _b, _c, _d, _e, _f, _g;\n      return __generator(this, function (_h) {\n        parsedUA = ((_a = window === null || window === void 0 ? void 0 : window.navigator) === null || _a === void 0 ? void 0 : _a.userAgent) ? bowser.parse(window.navigator.userAgent) : undefined;\n        sections = [\n        // sdk-metadata\n        [\"aws-sdk-js\", clientVersion],\n        // os-metadata\n        [\"os/\" + (((_b = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.os) === null || _b === void 0 ? void 0 : _b.name) || \"other\"), (_c = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.os) === null || _c === void 0 ? void 0 : _c.version],\n        // language-metadata\n        // ECMAScript edition doesn't matter in JS.\n        [\"lang/js\"],\n        // browser vendor and version.\n        [\"md/browser\", ((_e = (_d = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.browser) === null || _d === void 0 ? void 0 : _d.name) !== null && _e !== void 0 ? _e : \"unknown\") + \"_\" + ((_g = (_f = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.browser) === null || _f === void 0 ? void 0 : _f.version) !== null && _g !== void 0 ? _g : \"unknown\")]];\n        if (serviceId) {\n          // api-metadata\n          // service Id may not appear in non-AWS clients\n          sections.push([\"api/\" + serviceId, clientVersion]);\n        }\n        return [2 /*return*/, sections];\n      });\n    });\n  };\n};", "map": {"version": 3, "names": ["__awaiter", "__generator", "bowser", "defaultUserAgent", "_a", "serviceId", "clientVersion", "parsedUA", "sections", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "window", "navigator", "userAgent", "parse", "undefined", "os", "name", "version", "browser", "push"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/util-user-agent-browser/dist/es/index.js"], "sourcesContent": ["import { __awaiter, __generator } from \"tslib\";\nimport bowser from \"bowser\";\n/**\n * Default provider to the user agent in browsers. It's a best effort to infer\n * the device information. It uses bowser library to detect the browser and virsion\n */\nexport var defaultUserAgent = function (_a) {\n    var serviceId = _a.serviceId, clientVersion = _a.clientVersion;\n    return function () { return __awaiter(void 0, void 0, void 0, function () {\n        var parsedUA, sections;\n        var _a, _b, _c, _d, _e, _f, _g;\n        return __generator(this, function (_h) {\n            parsedUA = ((_a = window === null || window === void 0 ? void 0 : window.navigator) === null || _a === void 0 ? void 0 : _a.userAgent) ? bowser.parse(window.navigator.userAgent) : undefined;\n            sections = [\n                // sdk-metadata\n                [\"aws-sdk-js\", clientVersion],\n                // os-metadata\n                [\"os/\" + (((_b = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.os) === null || _b === void 0 ? void 0 : _b.name) || \"other\"), (_c = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.os) === null || _c === void 0 ? void 0 : _c.version],\n                // language-metadata\n                // ECMAScript edition doesn't matter in JS.\n                [\"lang/js\"],\n                // browser vendor and version.\n                [\"md/browser\", ((_e = (_d = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.browser) === null || _d === void 0 ? void 0 : _d.name) !== null && _e !== void 0 ? _e : \"unknown\") + \"_\" + ((_g = (_f = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.browser) === null || _f === void 0 ? void 0 : _f.version) !== null && _g !== void 0 ? _g : \"unknown\")],\n            ];\n            if (serviceId) {\n                // api-metadata\n                // service Id may not appear in non-AWS clients\n                sections.push([\"api/\" + serviceId, clientVersion]);\n            }\n            return [2 /*return*/, sections];\n        });\n    }); };\n};\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC9C,OAAOC,MAAM,MAAM,QAAQ;AAC3B;AACA;AACA;AACA;AACA,OAAO,IAAIC,gBAAgB,GAAG,SAAAA,CAAUC,EAAE,EAAE;EACxC,IAAIC,SAAS,GAAGD,EAAE,CAACC,SAAS;IAAEC,aAAa,GAAGF,EAAE,CAACE,aAAa;EAC9D,OAAO,YAAY;IAAE,OAAON,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MACtE,IAAIO,QAAQ,EAAEC,QAAQ;MACtB,IAAIJ,EAAE,EAAEK,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;MAC9B,OAAOb,WAAW,CAAC,IAAI,EAAE,UAAUc,EAAE,EAAE;QACnCR,QAAQ,GAAG,CAAC,CAACH,EAAE,GAAGY,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,SAAS,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,SAAS,IAAIhB,MAAM,CAACiB,KAAK,CAACH,MAAM,CAACC,SAAS,CAACC,SAAS,CAAC,GAAGE,SAAS;QAC7LZ,QAAQ,GAAG;QACP;QACA,CAAC,YAAY,EAAEF,aAAa,CAAC;QAC7B;QACA,CAAC,KAAK,IAAI,CAAC,CAACG,EAAE,GAAGF,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACc,EAAE,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACa,IAAI,KAAK,OAAO,CAAC,EAAE,CAACZ,EAAE,GAAGH,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACc,EAAE,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACa,OAAO,CAAC;QACpQ;QACA;QACA,CAAC,SAAS,CAAC;QACX;QACA,CAAC,YAAY,EAAE,CAAC,CAACX,EAAE,GAAG,CAACD,EAAE,GAAGJ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACiB,OAAO,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,IAAI,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,SAAS,IAAI,GAAG,IAAI,CAACE,EAAE,GAAG,CAACD,EAAE,GAAGN,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACiB,OAAO,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,OAAO,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,SAAS,CAAC,CAAC,CACvX;QACD,IAAIT,SAAS,EAAE;UACX;UACA;UACAG,QAAQ,CAACiB,IAAI,CAAC,CAAC,MAAM,GAAGpB,SAAS,EAAEC,aAAa,CAAC,CAAC;QACtD;QACA,OAAO,CAAC,CAAC,CAAC,YAAYE,QAAQ,CAAC;MACnC,CAAC,CAAC;IACN,CAAC,CAAC;EAAE,CAAC;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}