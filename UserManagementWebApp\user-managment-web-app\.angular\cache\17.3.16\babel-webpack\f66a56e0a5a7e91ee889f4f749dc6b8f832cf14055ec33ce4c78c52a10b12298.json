{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction bytesToString(input) {\n  return Array.from(input, byte => String.fromCodePoint(byte)).join('');\n}\nexport { bytesToString };", "map": {"version": 3, "names": ["bytesToString", "input", "Array", "from", "byte", "String", "fromCodePoint", "join"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/convert/base64/bytesToString.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction bytesToString(input) {\n    return Array.from(input, byte => String.fromCodePoint(byte)).join('');\n}\n\nexport { bytesToString };\n"], "mappings": "AAAA;AACA;AACA,SAASA,aAAaA,CAACC,KAAK,EAAE;EAC1B,OAAOC,KAAK,CAACC,IAAI,CAACF,KAAK,EAAEG,IAAI,IAAIC,MAAM,CAACC,aAAa,CAACF,IAAI,CAAC,CAAC,CAACG,IAAI,CAAC,EAAE,CAAC;AACzE;AAEA,SAASP,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}