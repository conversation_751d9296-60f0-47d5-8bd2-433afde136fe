{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst globalExists = () => {\n  return typeof global !== 'undefined';\n};\nconst globalThisExists = () => {\n  return typeof globalThis !== 'undefined';\n};\nconst windowExists = () => {\n  return typeof window !== 'undefined';\n};\nconst documentExists = () => {\n  return typeof document !== 'undefined';\n};\nconst processExists = () => {\n  return typeof process !== 'undefined';\n};\nconst keyPrefixMatch = (object, prefix) => {\n  return !!Object.keys(object).find(key => key.startsWith(prefix));\n};\nexport { documentExists, globalExists, globalThisExists, keyPrefixMatch, processExists, windowExists };", "map": {"version": 3, "names": ["globalExists", "global", "globalThisExists", "globalThis", "windowExists", "window", "documentExists", "document", "processExists", "process", "keyPrefixMatch", "object", "prefix", "Object", "keys", "find", "key", "startsWith"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Platform/detection/helpers.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst globalExists = () => {\n    return typeof global !== 'undefined';\n};\nconst globalThisExists = () => {\n    return typeof globalThis !== 'undefined';\n};\nconst windowExists = () => {\n    return typeof window !== 'undefined';\n};\nconst documentExists = () => {\n    return typeof document !== 'undefined';\n};\nconst processExists = () => {\n    return typeof process !== 'undefined';\n};\nconst keyPrefixMatch = (object, prefix) => {\n    return !!Object.keys(object).find(key => key.startsWith(prefix));\n};\n\nexport { documentExists, globalExists, globalThisExists, keyPrefixMatch, processExists, windowExists };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,YAAY,GAAGA,CAAA,KAAM;EACvB,OAAO,OAAOC,MAAM,KAAK,WAAW;AACxC,CAAC;AACD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAC3B,OAAO,OAAOC,UAAU,KAAK,WAAW;AAC5C,CAAC;AACD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,OAAO,OAAOC,MAAM,KAAK,WAAW;AACxC,CAAC;AACD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EACzB,OAAO,OAAOC,QAAQ,KAAK,WAAW;AAC1C,CAAC;AACD,MAAMC,aAAa,GAAGA,CAAA,KAAM;EACxB,OAAO,OAAOC,OAAO,KAAK,WAAW;AACzC,CAAC;AACD,MAAMC,cAAc,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EACvC,OAAO,CAAC,CAACC,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,UAAU,CAACL,MAAM,CAAC,CAAC;AACpE,CAAC;AAED,SAASN,cAAc,EAAEN,YAAY,EAAEE,gBAAgB,EAAEQ,cAAc,EAAEF,aAAa,EAAEJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}