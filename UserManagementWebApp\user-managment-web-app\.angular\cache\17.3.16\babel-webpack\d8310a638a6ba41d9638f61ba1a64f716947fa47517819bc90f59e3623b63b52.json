{"ast": null, "code": "import { Framework } from './types.mjs';\nimport { detect } from './detection/index.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// We want to cache detection since the framework won't change\nlet frameworkCache;\nconst frameworkChangeObservers = [];\n// Setup the detection reset tracking / timeout delays\nlet resetTriggered = false;\nconst SSR_RESET_TIMEOUT = 10; // ms\nconst WEB_RESET_TIMEOUT = 10; // ms\nconst PRIME_FRAMEWORK_DELAY = 1000; // ms\nconst detectFramework = () => {\n  if (!frameworkCache) {\n    frameworkCache = detect();\n    if (resetTriggered) {\n      // The final run of detectFramework:\n      // Starting from this point, the `frameworkCache` becomes \"final\".\n      // So we don't need to notify the observers again so the observer\n      // can be removed after the final notice.\n      while (frameworkChangeObservers.length) {\n        frameworkChangeObservers.pop()?.();\n      }\n    } else {\n      // The first run of detectFramework:\n      // Every time we update the cache, call each observer function\n      frameworkChangeObservers.forEach(fcn => {\n        fcn();\n      });\n    }\n    // Retry once for either Unknown type after a delay (explained below)\n    resetTimeout(Framework.ServerSideUnknown, SSR_RESET_TIMEOUT);\n    resetTimeout(Framework.WebUnknown, WEB_RESET_TIMEOUT);\n  }\n  return frameworkCache;\n};\n/**\n * @internal Setup observer callback that will be called everytime the framework changes\n */\nconst observeFrameworkChanges = fcn => {\n  // When the `frameworkCache` won't be updated again, we ignore all incoming\n  // observers.\n  if (resetTriggered) {\n    return;\n  }\n  frameworkChangeObservers.push(fcn);\n};\nfunction clearCache() {\n  frameworkCache = undefined;\n}\n// For a framework type and a delay amount, setup the event to re-detect\n//   During the runtime boot, it is possible that framework detection will\n//   be triggered before the framework has made modifications to the\n//   global/window/etc needed for detection. When no framework is detected\n//   we will reset and try again to ensure we don't use a cached\n//   non-framework detection result for all requests.\nfunction resetTimeout(framework, delay) {\n  if (frameworkCache === framework && !resetTriggered) {\n    setTimeout(() => {\n      clearCache();\n      resetTriggered = true;\n      setTimeout(detectFramework, PRIME_FRAMEWORK_DELAY);\n    }, delay);\n  }\n}\nexport { clearCache, detectFramework, frameworkChangeObservers, observeFrameworkChanges };", "map": {"version": 3, "names": ["Framework", "detect", "frameworkCache", "frameworkChangeObservers", "resetTriggered", "SSR_RESET_TIMEOUT", "WEB_RESET_TIMEOUT", "PRIME_FRAMEWORK_DELAY", "detectFramework", "length", "pop", "for<PERSON>ach", "fcn", "resetTimeout", "ServerSideUnknown", "WebUnknown", "observeFrameworkChanges", "push", "clearCache", "undefined", "framework", "delay", "setTimeout"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Platform/detectFramework.mjs"], "sourcesContent": ["import { Framework } from './types.mjs';\nimport { detect } from './detection/index.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// We want to cache detection since the framework won't change\nlet frameworkCache;\nconst frameworkChangeObservers = [];\n// Setup the detection reset tracking / timeout delays\nlet resetTriggered = false;\nconst SSR_RESET_TIMEOUT = 10; // ms\nconst WEB_RESET_TIMEOUT = 10; // ms\nconst PRIME_FRAMEWORK_DELAY = 1000; // ms\nconst detectFramework = () => {\n    if (!frameworkCache) {\n        frameworkCache = detect();\n        if (resetTriggered) {\n            // The final run of detectFramework:\n            // Starting from this point, the `frameworkCache` becomes \"final\".\n            // So we don't need to notify the observers again so the observer\n            // can be removed after the final notice.\n            while (frameworkChangeObservers.length) {\n                frameworkChangeObservers.pop()?.();\n            }\n        }\n        else {\n            // The first run of detectFramework:\n            // Every time we update the cache, call each observer function\n            frameworkChangeObservers.forEach(fcn => {\n                fcn();\n            });\n        }\n        // Retry once for either Unknown type after a delay (explained below)\n        resetTimeout(Framework.ServerSideUnknown, SSR_RESET_TIMEOUT);\n        resetTimeout(Framework.WebUnknown, WEB_RESET_TIMEOUT);\n    }\n    return frameworkCache;\n};\n/**\n * @internal Setup observer callback that will be called everytime the framework changes\n */\nconst observeFrameworkChanges = (fcn) => {\n    // When the `frameworkCache` won't be updated again, we ignore all incoming\n    // observers.\n    if (resetTriggered) {\n        return;\n    }\n    frameworkChangeObservers.push(fcn);\n};\nfunction clearCache() {\n    frameworkCache = undefined;\n}\n// For a framework type and a delay amount, setup the event to re-detect\n//   During the runtime boot, it is possible that framework detection will\n//   be triggered before the framework has made modifications to the\n//   global/window/etc needed for detection. When no framework is detected\n//   we will reset and try again to ensure we don't use a cached\n//   non-framework detection result for all requests.\nfunction resetTimeout(framework, delay) {\n    if (frameworkCache === framework && !resetTriggered) {\n        setTimeout(() => {\n            clearCache();\n            resetTriggered = true;\n            setTimeout(detectFramework, PRIME_FRAMEWORK_DELAY);\n        }, delay);\n    }\n}\n\nexport { clearCache, detectFramework, frameworkChangeObservers, observeFrameworkChanges };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,SAASC,MAAM,QAAQ,uBAAuB;;AAE9C;AACA;AACA;AACA,IAAIC,cAAc;AAClB,MAAMC,wBAAwB,GAAG,EAAE;AACnC;AACA,IAAIC,cAAc,GAAG,KAAK;AAC1B,MAAMC,iBAAiB,GAAG,EAAE,CAAC,CAAC;AAC9B,MAAMC,iBAAiB,GAAG,EAAE,CAAC,CAAC;AAC9B,MAAMC,qBAAqB,GAAG,IAAI,CAAC,CAAC;AACpC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC1B,IAAI,CAACN,cAAc,EAAE;IACjBA,cAAc,GAAGD,MAAM,CAAC,CAAC;IACzB,IAAIG,cAAc,EAAE;MAChB;MACA;MACA;MACA;MACA,OAAOD,wBAAwB,CAACM,MAAM,EAAE;QACpCN,wBAAwB,CAACO,GAAG,CAAC,CAAC,GAAG,CAAC;MACtC;IACJ,CAAC,MACI;MACD;MACA;MACAP,wBAAwB,CAACQ,OAAO,CAACC,GAAG,IAAI;QACpCA,GAAG,CAAC,CAAC;MACT,CAAC,CAAC;IACN;IACA;IACAC,YAAY,CAACb,SAAS,CAACc,iBAAiB,EAAET,iBAAiB,CAAC;IAC5DQ,YAAY,CAACb,SAAS,CAACe,UAAU,EAAET,iBAAiB,CAAC;EACzD;EACA,OAAOJ,cAAc;AACzB,CAAC;AACD;AACA;AACA;AACA,MAAMc,uBAAuB,GAAIJ,GAAG,IAAK;EACrC;EACA;EACA,IAAIR,cAAc,EAAE;IAChB;EACJ;EACAD,wBAAwB,CAACc,IAAI,CAACL,GAAG,CAAC;AACtC,CAAC;AACD,SAASM,UAAUA,CAAA,EAAG;EAClBhB,cAAc,GAAGiB,SAAS;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASN,YAAYA,CAACO,SAAS,EAAEC,KAAK,EAAE;EACpC,IAAInB,cAAc,KAAKkB,SAAS,IAAI,CAAChB,cAAc,EAAE;IACjDkB,UAAU,CAAC,MAAM;MACbJ,UAAU,CAAC,CAAC;MACZd,cAAc,GAAG,IAAI;MACrBkB,UAAU,CAACd,eAAe,EAAED,qBAAqB,CAAC;IACtD,CAAC,EAAEc,KAAK,CAAC;EACb;AACJ;AAEA,SAASH,UAAU,EAAEV,eAAe,EAAEL,wBAAwB,EAAEa,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}