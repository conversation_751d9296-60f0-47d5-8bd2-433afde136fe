{"ast": null, "code": "/**\n * Represents a location in a Source.\n */\n\n/**\n * Takes a Source and a UTF-8 character offset, and returns the corresponding\n * line and column as a SourceLocation.\n */\nexport function getLocation(source, position) {\n  var lineRegexp = /\\r\\n|[\\n\\r]/g;\n  var line = 1;\n  var column = position + 1;\n  var match;\n  while ((match = lineRegexp.exec(source.body)) && match.index < position) {\n    line += 1;\n    column = position + 1 - (match.index + match[0].length);\n  }\n  return {\n    line: line,\n    column: column\n  };\n}", "map": {"version": 3, "names": ["getLocation", "source", "position", "lineRegexp", "line", "column", "match", "exec", "body", "index", "length"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/graphql/language/location.mjs"], "sourcesContent": ["/**\n * Represents a location in a Source.\n */\n\n/**\n * Takes a Source and a UTF-8 character offset, and returns the corresponding\n * line and column as a SourceLocation.\n */\nexport function getLocation(source, position) {\n  var lineRegexp = /\\r\\n|[\\n\\r]/g;\n  var line = 1;\n  var column = position + 1;\n  var match;\n\n  while ((match = lineRegexp.exec(source.body)) && match.index < position) {\n    line += 1;\n    column = position + 1 - (match.index + match[0].length);\n  }\n\n  return {\n    line: line,\n    column: column\n  };\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASA,WAAWA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC5C,IAAIC,UAAU,GAAG,cAAc;EAC/B,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,MAAM,GAAGH,QAAQ,GAAG,CAAC;EACzB,IAAII,KAAK;EAET,OAAO,CAACA,KAAK,GAAGH,UAAU,CAACI,IAAI,CAACN,MAAM,CAACO,IAAI,CAAC,KAAKF,KAAK,CAACG,KAAK,GAAGP,QAAQ,EAAE;IACvEE,IAAI,IAAI,CAAC;IACTC,MAAM,GAAGH,QAAQ,GAAG,CAAC,IAAII,KAAK,CAACG,KAAK,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC;EACzD;EAEA,OAAO;IACLN,IAAI,EAAEA,IAAI;IACVC,MAAM,EAAEA;EACV,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}