{"ast": null, "code": "import { getBtoa } from '../../globalHelpers/index.mjs';\nimport { bytesToString } from './bytesToString.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst base64Encoder = {\n  /**\n   * Convert input to base64-encoded string\n   * @param input - string to convert to base64\n   * @param options - encoding options that can optionally produce a base64url string\n   * @returns base64-encoded string\n   */\n  convert(input, options = {\n    urlSafe: false,\n    skipPadding: false\n  }) {\n    const inputStr = typeof input === 'string' ? input : bytesToString(input);\n    let encodedStr = getBtoa()(inputStr);\n    // urlSafe char replacement and skipPadding options conform to the base64url spec\n    // https://datatracker.ietf.org/doc/html/rfc4648#section-5\n    if (options.urlSafe) {\n      encodedStr = encodedStr.replace(/\\+/g, '-').replace(/\\//g, '_');\n    }\n    if (options.skipPadding) {\n      encodedStr = encodedStr.replace(/=/g, '');\n    }\n    return encodedStr;\n  }\n};\nexport { base64Encoder };", "map": {"version": 3, "names": ["getBtoa", "bytesToString", "base64Encoder", "convert", "input", "options", "urlSafe", "skipPadding", "inputStr", "encodedStr", "replace"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/convert/base64/base64Encoder.mjs"], "sourcesContent": ["import { getBtoa } from '../../globalHelpers/index.mjs';\nimport { bytesToString } from './bytesToString.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst base64Encoder = {\n    /**\n     * Convert input to base64-encoded string\n     * @param input - string to convert to base64\n     * @param options - encoding options that can optionally produce a base64url string\n     * @returns base64-encoded string\n     */\n    convert(input, options = {\n        urlSafe: false,\n        skipPadding: false,\n    }) {\n        const inputStr = typeof input === 'string' ? input : bytesToString(input);\n        let encodedStr = getBtoa()(inputStr);\n        // urlSafe char replacement and skipPadding options conform to the base64url spec\n        // https://datatracker.ietf.org/doc/html/rfc4648#section-5\n        if (options.urlSafe) {\n            encodedStr = encodedStr.replace(/\\+/g, '-').replace(/\\//g, '_');\n        }\n        if (options.skipPadding) {\n            encodedStr = encodedStr.replace(/=/g, '');\n        }\n        return encodedStr;\n    },\n};\n\nexport { base64Encoder };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,+BAA+B;AACvD,SAASC,aAAa,QAAQ,qBAAqB;;AAEnD;AACA;AACA,MAAMC,aAAa,GAAG;EAClB;AACJ;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACC,KAAK,EAAEC,OAAO,GAAG;IACrBC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAE;EACjB,CAAC,EAAE;IACC,MAAMC,QAAQ,GAAG,OAAOJ,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGH,aAAa,CAACG,KAAK,CAAC;IACzE,IAAIK,UAAU,GAAGT,OAAO,CAAC,CAAC,CAACQ,QAAQ,CAAC;IACpC;IACA;IACA,IAAIH,OAAO,CAACC,OAAO,EAAE;MACjBG,UAAU,GAAGA,UAAU,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IACnE;IACA,IAAIL,OAAO,CAACE,WAAW,EAAE;MACrBE,UAAU,GAAGA,UAAU,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;IAC7C;IACA,OAAOD,UAAU;EACrB;AACJ,CAAC;AAED,SAASP,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}