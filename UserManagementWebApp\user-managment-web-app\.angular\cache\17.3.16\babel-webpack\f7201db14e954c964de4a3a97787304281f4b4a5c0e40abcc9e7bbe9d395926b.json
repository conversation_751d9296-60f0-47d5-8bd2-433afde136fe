{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { CreateStreamProcessorRequest, CreateStreamProcessorResponse } from \"../models/models_0\";\nimport { deserializeAws_json1_1CreateStreamProcessorCommand, serializeAws_json1_1CreateStreamProcessorCommand } from \"../protocols/Aws_json1_1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Creates an Amazon Rekognition stream processor that you can use to detect and recognize faces in a streaming video.</p>\n *         <p>Amazon Rekognition Video is a consumer of live video from Amazon Kinesis Video Streams. Amazon Rekognition Video sends analysis results to Amazon Kinesis Data Streams.</p>\n *         <p>You provide as input a Kinesis video stream (<code>Input</code>) and a Kinesis data stream (<code>Output</code>) stream. You also specify the\n *             face recognition criteria in <code>Settings</code>. For example, the collection containing faces that you want to recognize.\n *             Use <code>Name</code> to assign an identifier for the stream processor. You use <code>Name</code>\n *             to manage the stream processor. For example, you can start processing the source video by calling <a>StartStreamProcessor</a> with\n *             the <code>Name</code> field. </p>\n *         <p>After you have finished analyzing a streaming video, use <a>StopStreamProcessor</a> to\n *         stop processing. You can delete the stream processor by calling <a>DeleteStreamProcessor</a>.</p>\n */\nvar CreateStreamProcessorCommand = /** @class */function (_super) {\n  __extends(CreateStreamProcessorCommand, _super);\n  // Start section: command_properties\n  // End section: command_properties\n  function CreateStreamProcessorCommand(input) {\n    var _this =\n    // Start section: command_constructor\n    _super.call(this) || this;\n    _this.input = input;\n    return _this;\n    // End section: command_constructor\n  }\n  /**\n   * @internal\n   */\n  CreateStreamProcessorCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n    this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n    var stack = clientStack.concat(this.middlewareStack);\n    var logger = configuration.logger;\n    var clientName = \"RekognitionClient\";\n    var commandName = \"CreateStreamProcessorCommand\";\n    var handlerExecutionContext = {\n      logger: logger,\n      clientName: clientName,\n      commandName: commandName,\n      inputFilterSensitiveLog: CreateStreamProcessorRequest.filterSensitiveLog,\n      outputFilterSensitiveLog: CreateStreamProcessorResponse.filterSensitiveLog\n    };\n    var requestHandler = configuration.requestHandler;\n    return stack.resolve(function (request) {\n      return requestHandler.handle(request.request, options || {});\n    }, handlerExecutionContext);\n  };\n  CreateStreamProcessorCommand.prototype.serialize = function (input, context) {\n    return serializeAws_json1_1CreateStreamProcessorCommand(input, context);\n  };\n  CreateStreamProcessorCommand.prototype.deserialize = function (output, context) {\n    return deserializeAws_json1_1CreateStreamProcessorCommand(output, context);\n  };\n  return CreateStreamProcessorCommand;\n}($Command);\nexport { CreateStreamProcessorCommand };", "map": {"version": 3, "names": ["__extends", "CreateStreamProcessorRequest", "CreateStreamProcessorResponse", "deserializeAws_json1_1CreateStreamProcessorCommand", "serializeAws_json1_1CreateStreamProcessorCommand", "getSerdePlugin", "Command", "$Command", "CreateStreamProcessorCommand", "_super", "input", "_this", "call", "prototype", "resolveMiddleware", "clientStack", "configuration", "options", "middlewareStack", "use", "serialize", "deserialize", "stack", "concat", "logger", "clientName", "commandName", "handlerExecutionContext", "inputFilterSensitiveLog", "filterSensitiveLog", "outputFilterSensitiveLog", "requestHandler", "resolve", "request", "handle", "context", "output"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-rekognition/dist/es/commands/CreateStreamProcessorCommand.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { CreateStreamProcessorRequest, CreateStreamProcessorResponse } from \"../models/models_0\";\nimport { deserializeAws_json1_1CreateStreamProcessorCommand, serializeAws_json1_1CreateStreamProcessorCommand, } from \"../protocols/Aws_json1_1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Creates an Amazon Rekognition stream processor that you can use to detect and recognize faces in a streaming video.</p>\n *         <p>Amazon Rekognition Video is a consumer of live video from Amazon Kinesis Video Streams. Amazon Rekognition Video sends analysis results to Amazon Kinesis Data Streams.</p>\n *         <p>You provide as input a Kinesis video stream (<code>Input</code>) and a Kinesis data stream (<code>Output</code>) stream. You also specify the\n *             face recognition criteria in <code>Settings</code>. For example, the collection containing faces that you want to recognize.\n *             Use <code>Name</code> to assign an identifier for the stream processor. You use <code>Name</code>\n *             to manage the stream processor. For example, you can start processing the source video by calling <a>StartStreamProcessor</a> with\n *             the <code>Name</code> field. </p>\n *         <p>After you have finished analyzing a streaming video, use <a>StopStreamProcessor</a> to\n *         stop processing. You can delete the stream processor by calling <a>DeleteStreamProcessor</a>.</p>\n */\nvar CreateStreamProcessorCommand = /** @class */ (function (_super) {\n    __extends(CreateStreamProcessorCommand, _super);\n    // Start section: command_properties\n    // End section: command_properties\n    function CreateStreamProcessorCommand(input) {\n        var _this = \n        // Start section: command_constructor\n        _super.call(this) || this;\n        _this.input = input;\n        return _this;\n        // End section: command_constructor\n    }\n    /**\n     * @internal\n     */\n    CreateStreamProcessorCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n        this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n        var stack = clientStack.concat(this.middlewareStack);\n        var logger = configuration.logger;\n        var clientName = \"RekognitionClient\";\n        var commandName = \"CreateStreamProcessorCommand\";\n        var handlerExecutionContext = {\n            logger: logger,\n            clientName: clientName,\n            commandName: commandName,\n            inputFilterSensitiveLog: CreateStreamProcessorRequest.filterSensitiveLog,\n            outputFilterSensitiveLog: CreateStreamProcessorResponse.filterSensitiveLog,\n        };\n        var requestHandler = configuration.requestHandler;\n        return stack.resolve(function (request) {\n            return requestHandler.handle(request.request, options || {});\n        }, handlerExecutionContext);\n    };\n    CreateStreamProcessorCommand.prototype.serialize = function (input, context) {\n        return serializeAws_json1_1CreateStreamProcessorCommand(input, context);\n    };\n    CreateStreamProcessorCommand.prototype.deserialize = function (output, context) {\n        return deserializeAws_json1_1CreateStreamProcessorCommand(output, context);\n    };\n    return CreateStreamProcessorCommand;\n}($Command));\nexport { CreateStreamProcessorCommand };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,4BAA4B,EAAEC,6BAA6B,QAAQ,oBAAoB;AAChG,SAASC,kDAAkD,EAAEC,gDAAgD,QAAS,0BAA0B;AAChJ,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,OAAO,IAAIC,QAAQ,QAAQ,wBAAwB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,4BAA4B,GAAG,aAAe,UAAUC,MAAM,EAAE;EAChET,SAAS,CAACQ,4BAA4B,EAAEC,MAAM,CAAC;EAC/C;EACA;EACA,SAASD,4BAA4BA,CAACE,KAAK,EAAE;IACzC,IAAIC,KAAK;IACT;IACAF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACzBD,KAAK,CAACD,KAAK,GAAGA,KAAK;IACnB,OAAOC,KAAK;IACZ;EACJ;EACA;AACJ;AACA;EACIH,4BAA4B,CAACK,SAAS,CAACC,iBAAiB,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAEC,OAAO,EAAE;IACtG,IAAI,CAACC,eAAe,CAACC,GAAG,CAACd,cAAc,CAACW,aAAa,EAAE,IAAI,CAACI,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;IACzF,IAAIC,KAAK,GAAGP,WAAW,CAACQ,MAAM,CAAC,IAAI,CAACL,eAAe,CAAC;IACpD,IAAIM,MAAM,GAAGR,aAAa,CAACQ,MAAM;IACjC,IAAIC,UAAU,GAAG,mBAAmB;IACpC,IAAIC,WAAW,GAAG,8BAA8B;IAChD,IAAIC,uBAAuB,GAAG;MAC1BH,MAAM,EAAEA,MAAM;MACdC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA,WAAW;MACxBE,uBAAuB,EAAE3B,4BAA4B,CAAC4B,kBAAkB;MACxEC,wBAAwB,EAAE5B,6BAA6B,CAAC2B;IAC5D,CAAC;IACD,IAAIE,cAAc,GAAGf,aAAa,CAACe,cAAc;IACjD,OAAOT,KAAK,CAACU,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpC,OAAOF,cAAc,CAACG,MAAM,CAACD,OAAO,CAACA,OAAO,EAAEhB,OAAO,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC,EAAEU,uBAAuB,CAAC;EAC/B,CAAC;EACDnB,4BAA4B,CAACK,SAAS,CAACO,SAAS,GAAG,UAAUV,KAAK,EAAEyB,OAAO,EAAE;IACzE,OAAO/B,gDAAgD,CAACM,KAAK,EAAEyB,OAAO,CAAC;EAC3E,CAAC;EACD3B,4BAA4B,CAACK,SAAS,CAACQ,WAAW,GAAG,UAAUe,MAAM,EAAED,OAAO,EAAE;IAC5E,OAAOhC,kDAAkD,CAACiC,MAAM,EAAED,OAAO,CAAC;EAC9E,CAAC;EACD,OAAO3B,4BAA4B;AACvC,CAAC,CAACD,QAAQ,CAAE;AACZ,SAASC,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}