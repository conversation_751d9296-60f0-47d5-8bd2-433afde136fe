{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { toAttributeType } from '../utils/apiHelpers.mjs';\nimport { handleCodeAutoSignIn, autoSignInUserConfirmed, autoSignInWhenUserIsConfirmedWithLink } from '../utils/signUpHelpers.mjs';\nimport { getUserContextData } from '../utils/userContextData.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport { createSignUpClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createSignUpClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { autoSignInStore } from '../../../client/utils/store/autoSignInStore.mjs';\nimport '../../../client/utils/store/signInStore.mjs';\nimport { setAutoSignIn } from './autoSignIn.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Creates a user\n *\n * @param input - The SignUpInput object\n * @returns SignUpOutput\n * @throws service: {@link SignUpException } - Cognito service errors thrown during the sign-up process.\n * @throws validation: {@link AuthValidationErrorCode } - Validation errors thrown either username or password\n *  are not defined.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nfunction signUp(_x) {\n  return _signUp.apply(this, arguments);\n}\nfunction _signUp() {\n  _signUp = _asyncToGenerator(function* (input) {\n    const {\n      username,\n      password,\n      options\n    } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    const signUpVerificationMethod = authConfig?.signUpVerificationMethod ?? 'code';\n    const {\n      clientMetadata,\n      validationData,\n      autoSignIn\n    } = input.options ?? {};\n    assertTokenProviderConfig(authConfig);\n    assertValidationError(!!username, AuthValidationErrorCode.EmptySignUpUsername);\n    const signInServiceOptions = typeof autoSignIn !== 'boolean' ? autoSignIn : undefined;\n    const signInInput = {\n      username,\n      options: signInServiceOptions\n    };\n    // if the authFlowType is 'CUSTOM_WITHOUT_SRP' then we don't include the password\n    if (signInServiceOptions?.authFlowType !== 'CUSTOM_WITHOUT_SRP') {\n      signInInput.password = password;\n    }\n    const {\n      userPoolId,\n      userPoolClientId,\n      userPoolEndpoint\n    } = authConfig;\n    const signUpClient = createSignUpClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const signUpClientInput = {\n      Username: username,\n      Password: undefined,\n      UserAttributes: options?.userAttributes && toAttributeType(options?.userAttributes),\n      ClientMetadata: clientMetadata,\n      ValidationData: validationData && toAttributeType(validationData),\n      ClientId: userPoolClientId,\n      UserContextData: getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId\n      })\n    };\n    if (password) {\n      signUpClientInput.Password = password;\n    }\n    const {\n      UserSub: userId,\n      CodeDeliveryDetails: cdd,\n      UserConfirmed: userConfirmed,\n      Session: session\n    } = yield signUpClient({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.SignUp)\n    }, signUpClientInput);\n    if (signInServiceOptions || autoSignIn === true) {\n      autoSignInStore.dispatch({\n        type: 'START'\n      });\n      autoSignInStore.dispatch({\n        type: 'SET_USERNAME',\n        value: username\n      });\n      autoSignInStore.dispatch({\n        type: 'SET_SESSION',\n        value: session\n      });\n    }\n    const codeDeliveryDetails = {\n      destination: cdd?.Destination,\n      deliveryMedium: cdd?.DeliveryMedium,\n      attributeName: cdd?.AttributeName\n    };\n    const isSignUpComplete = !!userConfirmed;\n    const isAutoSignInStarted = autoSignInStore.getState().active;\n    // Sign Up Complete\n    // No Confirm Sign In Step Required\n    if (isSignUpComplete) {\n      if (isAutoSignInStarted) {\n        setAutoSignIn(autoSignInUserConfirmed(signInInput));\n        return {\n          isSignUpComplete: true,\n          nextStep: {\n            signUpStep: 'COMPLETE_AUTO_SIGN_IN'\n          },\n          userId\n        };\n      }\n      return {\n        isSignUpComplete: true,\n        nextStep: {\n          signUpStep: 'DONE'\n        },\n        userId\n      };\n    }\n    // Sign Up Not Complete\n    // Confirm Sign Up Step Required\n    if (isAutoSignInStarted) {\n      // Confirmation Via Link Occurs In Separate Context\n      // AutoSignIn Fn Will Initiate Polling Once Executed\n      if (signUpVerificationMethod === 'link') {\n        setAutoSignIn(autoSignInWhenUserIsConfirmedWithLink(signInInput));\n        return {\n          isSignUpComplete: false,\n          nextStep: {\n            signUpStep: 'COMPLETE_AUTO_SIGN_IN',\n            codeDeliveryDetails\n          },\n          userId\n        };\n      }\n      // Confirmation Via Code Occurs In Same Context\n      // AutoSignIn Next Step Will Be Returned From Confirm Sign Up\n      handleCodeAutoSignIn(signInInput);\n    }\n    return {\n      isSignUpComplete: false,\n      nextStep: {\n        signUpStep: 'CONFIRM_SIGN_UP',\n        codeDeliveryDetails\n      },\n      userId\n    };\n  });\n  return _signUp.apply(this, arguments);\n}\nexport { signUp };", "map": {"version": 3, "names": ["Amplify", "assertTokenProviderConfig", "AuthAction", "assertValidationError", "AuthValidationErrorCode", "getRegionFromUserPoolId", "toAttributeType", "handleCodeAutoSignIn", "autoSignInUserConfirmed", "autoSignInWhenUserIsConfirmedWithLink", "getUserContextData", "getAuthUserAgentValue", "createSignUpClient", "createCognitoUserPoolEndpointResolver", "autoSignInStore", "setAutoSignIn", "signUp", "_x", "_signUp", "apply", "arguments", "_asyncToGenerator", "input", "username", "password", "options", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "signUpVerificationMethod", "clientMetadata", "validationData", "autoSignIn", "EmptySignUpUsername", "signInServiceOptions", "undefined", "signInInput", "authFlowType", "userPoolId", "userPoolClientId", "userPoolEndpoint", "signUpClient", "endpointResolver", "endpointOverride", "signUpClientInput", "Username", "Password", "UserAttributes", "userAttributes", "ClientMetadata", "ValidationData", "ClientId", "UserContextData", "UserSub", "userId", "CodeDeliveryDetails", "cdd", "UserConfirmed", "userConfirmed", "Session", "session", "region", "userAgentValue", "SignUp", "dispatch", "type", "value", "codeDeliveryDetails", "destination", "Destination", "deliveryMedium", "DeliveryMedium", "attributeName", "AttributeName", "isSignUpComplete", "isAutoSignInStarted", "getState", "active", "nextStep", "signUpStep"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/signUp.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { toAttributeType } from '../utils/apiHelpers.mjs';\nimport { handleCodeAutoSignIn, autoSignInUserConfirmed, autoSignInWhenUserIsConfirmedWithLink } from '../utils/signUpHelpers.mjs';\nimport { getUserContextData } from '../utils/userContextData.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport { createSignUpClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createSignUpClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { autoSignInStore } from '../../../client/utils/store/autoSignInStore.mjs';\nimport '../../../client/utils/store/signInStore.mjs';\nimport { setAutoSignIn } from './autoSignIn.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Creates a user\n *\n * @param input - The SignUpInput object\n * @returns SignUpOutput\n * @throws service: {@link SignUpException } - Cognito service errors thrown during the sign-up process.\n * @throws validation: {@link AuthValidationErrorCode } - Validation errors thrown either username or password\n *  are not defined.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nasync function signUp(input) {\n    const { username, password, options } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    const signUpVerificationMethod = authConfig?.signUpVerificationMethod ?? 'code';\n    const { clientMetadata, validationData, autoSignIn } = input.options ?? {};\n    assertTokenProviderConfig(authConfig);\n    assertValidationError(!!username, AuthValidationErrorCode.EmptySignUpUsername);\n    const signInServiceOptions = typeof autoSignIn !== 'boolean' ? autoSignIn : undefined;\n    const signInInput = {\n        username,\n        options: signInServiceOptions,\n    };\n    // if the authFlowType is 'CUSTOM_WITHOUT_SRP' then we don't include the password\n    if (signInServiceOptions?.authFlowType !== 'CUSTOM_WITHOUT_SRP') {\n        signInInput.password = password;\n    }\n    const { userPoolId, userPoolClientId, userPoolEndpoint } = authConfig;\n    const signUpClient = createSignUpClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const signUpClientInput = {\n        Username: username,\n        Password: undefined,\n        UserAttributes: options?.userAttributes && toAttributeType(options?.userAttributes),\n        ClientMetadata: clientMetadata,\n        ValidationData: validationData && toAttributeType(validationData),\n        ClientId: userPoolClientId,\n        UserContextData: getUserContextData({\n            username,\n            userPoolId,\n            userPoolClientId,\n        }),\n    };\n    if (password) {\n        signUpClientInput.Password = password;\n    }\n    const { UserSub: userId, CodeDeliveryDetails: cdd, UserConfirmed: userConfirmed, Session: session, } = await signUpClient({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.SignUp),\n    }, signUpClientInput);\n    if (signInServiceOptions || autoSignIn === true) {\n        autoSignInStore.dispatch({ type: 'START' });\n        autoSignInStore.dispatch({ type: 'SET_USERNAME', value: username });\n        autoSignInStore.dispatch({ type: 'SET_SESSION', value: session });\n    }\n    const codeDeliveryDetails = {\n        destination: cdd?.Destination,\n        deliveryMedium: cdd?.DeliveryMedium,\n        attributeName: cdd?.AttributeName,\n    };\n    const isSignUpComplete = !!userConfirmed;\n    const isAutoSignInStarted = autoSignInStore.getState().active;\n    // Sign Up Complete\n    // No Confirm Sign In Step Required\n    if (isSignUpComplete) {\n        if (isAutoSignInStarted) {\n            setAutoSignIn(autoSignInUserConfirmed(signInInput));\n            return {\n                isSignUpComplete: true,\n                nextStep: {\n                    signUpStep: 'COMPLETE_AUTO_SIGN_IN',\n                },\n                userId,\n            };\n        }\n        return {\n            isSignUpComplete: true,\n            nextStep: {\n                signUpStep: 'DONE',\n            },\n            userId,\n        };\n    }\n    // Sign Up Not Complete\n    // Confirm Sign Up Step Required\n    if (isAutoSignInStarted) {\n        // Confirmation Via Link Occurs In Separate Context\n        // AutoSignIn Fn Will Initiate Polling Once Executed\n        if (signUpVerificationMethod === 'link') {\n            setAutoSignIn(autoSignInWhenUserIsConfirmedWithLink(signInInput));\n            return {\n                isSignUpComplete: false,\n                nextStep: {\n                    signUpStep: 'COMPLETE_AUTO_SIGN_IN',\n                    codeDeliveryDetails,\n                },\n                userId,\n            };\n        }\n        // Confirmation Via Code Occurs In Same Context\n        // AutoSignIn Next Step Will Be Returned From Confirm Sign Up\n        handleCodeAutoSignIn(signInInput);\n    }\n    return {\n        isSignUpComplete: false,\n        nextStep: {\n            signUpStep: 'CONFIRM_SIGN_UP',\n            codeDeliveryDetails,\n        },\n        userId,\n    };\n}\n\nexport { signUp };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,oBAAoB,EAAEC,uBAAuB,EAAEC,qCAAqC,QAAQ,4BAA4B;AACjI,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,SAASC,kBAAkB,QAAQ,6FAA6F;AAChI,SAASC,qCAAqC,QAAQ,wDAAwD;AAC9G,SAASC,eAAe,QAAQ,iDAAiD;AACjF,OAAO,6CAA6C;AACpD,SAASC,aAAa,QAAQ,kBAAkB;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,SAUeC,MAAMA,CAAAC,EAAA;EAAA,OAAAC,OAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,QAAA;EAAAA,OAAA,GAAAG,iBAAA,CAArB,WAAsBC,KAAK,EAAE;IACzB,MAAM;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC;IAAQ,CAAC,GAAGH,KAAK;IAC7C,MAAMI,UAAU,GAAG1B,OAAO,CAAC2B,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpD,MAAMC,wBAAwB,GAAGJ,UAAU,EAAEI,wBAAwB,IAAI,MAAM;IAC/E,MAAM;MAAEC,cAAc;MAAEC,cAAc;MAAEC;IAAW,CAAC,GAAGX,KAAK,CAACG,OAAO,IAAI,CAAC,CAAC;IAC1ExB,yBAAyB,CAACyB,UAAU,CAAC;IACrCvB,qBAAqB,CAAC,CAAC,CAACoB,QAAQ,EAAEnB,uBAAuB,CAAC8B,mBAAmB,CAAC;IAC9E,MAAMC,oBAAoB,GAAG,OAAOF,UAAU,KAAK,SAAS,GAAGA,UAAU,GAAGG,SAAS;IACrF,MAAMC,WAAW,GAAG;MAChBd,QAAQ;MACRE,OAAO,EAAEU;IACb,CAAC;IACD;IACA,IAAIA,oBAAoB,EAAEG,YAAY,KAAK,oBAAoB,EAAE;MAC7DD,WAAW,CAACb,QAAQ,GAAGA,QAAQ;IACnC;IACA,MAAM;MAAEe,UAAU;MAAEC,gBAAgB;MAAEC;IAAiB,CAAC,GAAGf,UAAU;IACrE,MAAMgB,YAAY,GAAG9B,kBAAkB,CAAC;MACpC+B,gBAAgB,EAAE9B,qCAAqC,CAAC;QACpD+B,gBAAgB,EAAEH;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMI,iBAAiB,GAAG;MACtBC,QAAQ,EAAEvB,QAAQ;MAClBwB,QAAQ,EAAEX,SAAS;MACnBY,cAAc,EAAEvB,OAAO,EAAEwB,cAAc,IAAI3C,eAAe,CAACmB,OAAO,EAAEwB,cAAc,CAAC;MACnFC,cAAc,EAAEnB,cAAc;MAC9BoB,cAAc,EAAEnB,cAAc,IAAI1B,eAAe,CAAC0B,cAAc,CAAC;MACjEoB,QAAQ,EAAEZ,gBAAgB;MAC1Ba,eAAe,EAAE3C,kBAAkB,CAAC;QAChCa,QAAQ;QACRgB,UAAU;QACVC;MACJ,CAAC;IACL,CAAC;IACD,IAAIhB,QAAQ,EAAE;MACVqB,iBAAiB,CAACE,QAAQ,GAAGvB,QAAQ;IACzC;IACA,MAAM;MAAE8B,OAAO,EAAEC,MAAM;MAAEC,mBAAmB,EAAEC,GAAG;MAAEC,aAAa,EAAEC,aAAa;MAAEC,OAAO,EAAEC;IAAS,CAAC,SAASnB,YAAY,CAAC;MACtHoB,MAAM,EAAEzD,uBAAuB,CAACkC,UAAU,CAAC;MAC3CwB,cAAc,EAAEpD,qBAAqB,CAACT,UAAU,CAAC8D,MAAM;IAC3D,CAAC,EAAEnB,iBAAiB,CAAC;IACrB,IAAIV,oBAAoB,IAAIF,UAAU,KAAK,IAAI,EAAE;MAC7CnB,eAAe,CAACmD,QAAQ,CAAC;QAAEC,IAAI,EAAE;MAAQ,CAAC,CAAC;MAC3CpD,eAAe,CAACmD,QAAQ,CAAC;QAAEC,IAAI,EAAE,cAAc;QAAEC,KAAK,EAAE5C;MAAS,CAAC,CAAC;MACnET,eAAe,CAACmD,QAAQ,CAAC;QAAEC,IAAI,EAAE,aAAa;QAAEC,KAAK,EAAEN;MAAQ,CAAC,CAAC;IACrE;IACA,MAAMO,mBAAmB,GAAG;MACxBC,WAAW,EAAEZ,GAAG,EAAEa,WAAW;MAC7BC,cAAc,EAAEd,GAAG,EAAEe,cAAc;MACnCC,aAAa,EAAEhB,GAAG,EAAEiB;IACxB,CAAC;IACD,MAAMC,gBAAgB,GAAG,CAAC,CAAChB,aAAa;IACxC,MAAMiB,mBAAmB,GAAG9D,eAAe,CAAC+D,QAAQ,CAAC,CAAC,CAACC,MAAM;IAC7D;IACA;IACA,IAAIH,gBAAgB,EAAE;MAClB,IAAIC,mBAAmB,EAAE;QACrB7D,aAAa,CAACP,uBAAuB,CAAC6B,WAAW,CAAC,CAAC;QACnD,OAAO;UACHsC,gBAAgB,EAAE,IAAI;UACtBI,QAAQ,EAAE;YACNC,UAAU,EAAE;UAChB,CAAC;UACDzB;QACJ,CAAC;MACL;MACA,OAAO;QACHoB,gBAAgB,EAAE,IAAI;QACtBI,QAAQ,EAAE;UACNC,UAAU,EAAE;QAChB,CAAC;QACDzB;MACJ,CAAC;IACL;IACA;IACA;IACA,IAAIqB,mBAAmB,EAAE;MACrB;MACA;MACA,IAAI9C,wBAAwB,KAAK,MAAM,EAAE;QACrCf,aAAa,CAACN,qCAAqC,CAAC4B,WAAW,CAAC,CAAC;QACjE,OAAO;UACHsC,gBAAgB,EAAE,KAAK;UACvBI,QAAQ,EAAE;YACNC,UAAU,EAAE,uBAAuB;YACnCZ;UACJ,CAAC;UACDb;QACJ,CAAC;MACL;MACA;MACA;MACAhD,oBAAoB,CAAC8B,WAAW,CAAC;IACrC;IACA,OAAO;MACHsC,gBAAgB,EAAE,KAAK;MACvBI,QAAQ,EAAE;QACNC,UAAU,EAAE,iBAAiB;QAC7BZ;MACJ,CAAC;MACDb;IACJ,CAAC;EACL,CAAC;EAAA,OAAArC,OAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}