<div class="w-full">
  <div *ngIf="layoutSection?.visible" class="template-content">
    <div *ngIf="isPreview" class="section-controls">
      <span>{{areaName}}</span>
      <div class="form-section-btns">
        <p-button icon="pi pi-pencil" [disabled]="disabled" pTooltip="Edit" [text]="true" styleClass="px-0" (onClick)="editSection()"></p-button>
        <p-button icon="pi pi-trash" [disabled]="disabled" pTooltip="Remove" [text]="true" severity="danger" styleClass="px-0" (onClick)="deleteSection()"></p-button>
      </div>
    </div>
    <span *ngIf="!layoutSection?.content" class="text-300 text-xl">{{areaName}} Content</span>
    <div *ngIf="layoutSection?.content">
      <div class="section-content ql-editor" [innerHTML]="this.domSanitizer.bypassSecurityTrustHtml(this.layoutSection?.content)"></div>
    </div>
  </div>
</div>