<div class="form-designer">
  <div class="grid form-layout" [ngClass]="{'max-h-unset':isEmbeddedView}">
    <div *ngIf="showDynamicForm && !showWarningsBlock" class="col-12 data-entry-form" [ngClass]="{'card': !formBodyOnly}">
      <div *ngIf="!formBodyOnly" class="grid header-wrap m-0">
        <div *ngIf="dynamicForm?.layout?.headerLeft?.visible"
          [ngClass]="{'mr-1': dynamicForm?.layout?.headerLeft?.visible && dynamicForm?.layout?.headerRight?.visible}"
          class="col form-layout-header-text-editor">
          <app-dynamic-form-section [dynamicForm]="dynamicForm" [layoutArea]="DynamicFormAreas.HeaderLeft"
            isPreview="true">
          </app-dynamic-form-section>
        </div>
        <div *ngIf="dynamicForm?.layout?.headerRight?.visible" class="col form-layout-header-text-editor">
          <app-dynamic-form-section [dynamicForm]="dynamicForm" [layoutArea]="DynamicFormAreas.HeaderRight"
            isPreview="true">
          </app-dynamic-form-section>
        </div>
      </div>

      <div *ngIf="!formBodyOnly && dynamicForm?.layout?.instruction.visible" class="">
        <app-dynamic-form-section [dynamicForm]="dynamicForm" [layoutArea]="DynamicFormAreas.Instruction"
          isPreview="true">
        </app-dynamic-form-section>
      </div>
      
      <div *ngIf="showSaveErrorsBlock" class="notice-bold danger ml-2">
        <h3 class="mb-0 mt-2">Error</h3>
        <p>Unable to submit the form due to validation errors. <a href="#"
            (click)="showValidationResults = true; $event.preventDefault()">Review errors</a>. </p>
      </div>

      <div *ngIf="datastore" class="card-container md:h-auto" style="min-height: 8rem"
        [ngClass]="{'mt-3': !dynamicForm?.layout?.headerLeft?.visible && !dynamicForm?.layout?.headerRight?.visible}">
        <app-dynamic-form-body [triggerSaveEvent]="triggerSaveEvent" [dynamicForm]="dynamicForm"
          [data]="jsonSchemaParam.data" [projectId]='projectId' [projectVersionId]='projectVersionId'
          [datastore]="datastore" [recordId]="recordId" [isPreview]="isPreview" (formOnSubmit)="formOnSubmit($event)"
          [showSubmitButton]="false" [showFormOptions]="!formBodyOnly" [userPermissionLevel]="userPermissionLevel">
        </app-dynamic-form-body>
      </div>      

      <div *ngIf="!formBodyOnly && dynamicForm?.layout?.footer?.visible" class="my-2">
        <app-dynamic-form-section [dynamicForm]="dynamicForm" [layoutArea]="DynamicFormAreas.Footer" 
          isPreview="true">
        </app-dynamic-form-section>
      </div>

      <app-signatures-renderer *ngIf="!formBodyOnly && signatures?.length"
        [signatures]="signatures"></app-signatures-renderer>
        
      <div *ngIf="!formBodyOnly && !isPreview && dynamicForm" class="flex justify-content-end">
        <app-signature-button *ngIf="dynamicForm?.additionalSettings?.enableSignature" [(signature)]="selectedSignature"
          (signatureChange)="signatureChange($event)"></app-signature-button>
        <p-button pRipple icon="pi pi-send" type="button" label="Submit" (onClick)="submit()"></p-button>
      </div>
    </div>

    <div *ngIf="showWarningsBlock" class="notice-bold warning col-12">
      <h3>Warning</h3>
      <p>Data submitted successfully. However, there were some validation warnings.</p>
      <p>Choose from the following actions:</p>
      <ul class="font-medium">
        <li><a href="#" (click)="showValidationResults = true; $event.preventDefault()">View Warnings</a></li>
        <li><a href="#" (click)="editLastEntry(); $event.preventDefault()">Review Data</a></li>
        <li><a href="#" (click)="handleContinue(); $event.preventDefault()">Continue</a></li>
      </ul>
    </div>

    <div class="data-entry-confirmation-message col-12"
      *ngIf="!formBodyOnly && isSubmitted && dynamicForm.additionalSettings?.postSaveAction === DynamicFormPostSaveActions.ConfirmationMessage">
      <img [src]="'layout/images/gfx-form-sent.svg' | assetUrl" alt="" width="100%">
      <div [innerHtml]="dynamicForm.additionalSettings?.actionContent" class="mt-4"></div>
    </div>

    <app-page-error-block *ngIf="pageLoadErrorObj" [title]="pageLoadErrorObj?.title"
      [detailsHtml]="pageLoadErrorObj?.details" [moreDetailsHtml]="pageLoadErrorObj?.moreDetails"
      [showBackButton]="!formBodyOnly && !isPreview && !isEmbeddedView"></app-page-error-block>
  </div>
</div>
<app-validation-results-dialog *ngIf="showValidationResults" [saveRecordsResponse]="saveRecordsResponse"
  [(visible)]="showValidationResults" (visibleChange)="validationDialogVisibaleChanged($event)"
  [data]="[jsonSchemaParam.data]" [datastore]="datastore" singleRecordMode="true" [masterKeyColumn]="masterKeyColumn">
</app-validation-results-dialog>
<p-blockUI *ngIf="showSpinner" [blocked]="showSpinner" styleClass="z-5">
  <img class="ui-progress-spinner" [src]="'layout/images/salt-box-loading.gif' | assetUrl">
</p-blockUI>