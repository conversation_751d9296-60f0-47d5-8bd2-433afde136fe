{"ast": null, "code": "const selectfield = {\n  borderColor: {\n    value: '{components.fieldcontrol.borderColor}'\n  },\n  color: {\n    value: '{components.fieldcontrol.color}'\n  },\n  flexDirection: {\n    value: 'column'\n  },\n  fontSize: {\n    value: '{components.fieldcontrol.fontSize}'\n  },\n  _focus: {\n    borderColor: {\n      value: '{components.fieldcontrol._focus.borderColor}'\n    }\n  },\n  label: {\n    color: {\n      value: '{components.field.label.color}'\n    }\n  }\n};\nexport { selectfield };", "map": {"version": 3, "names": ["selectfield", "borderColor", "value", "color", "flexDirection", "fontSize", "_focus", "label"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/selectField.mjs"], "sourcesContent": ["const selectfield = {\n    borderColor: { value: '{components.fieldcontrol.borderColor}' },\n    color: { value: '{components.fieldcontrol.color}' },\n    flexDirection: {\n        value: 'column',\n    },\n    fontSize: { value: '{components.fieldcontrol.fontSize}' },\n    _focus: {\n        borderColor: { value: '{components.fieldcontrol._focus.borderColor}' },\n    },\n    label: {\n        color: { value: '{components.field.label.color}' },\n    },\n};\n\nexport { selectfield };\n"], "mappings": "AAAA,MAAMA,WAAW,GAAG;EAChBC,WAAW,EAAE;IAAEC,KAAK,EAAE;EAAwC,CAAC;EAC/DC,KAAK,EAAE;IAAED,KAAK,EAAE;EAAkC,CAAC;EACnDE,aAAa,EAAE;IACXF,KAAK,EAAE;EACX,CAAC;EACDG,QAAQ,EAAE;IAAEH,KAAK,EAAE;EAAqC,CAAC;EACzDI,MAAM,EAAE;IACJL,WAAW,EAAE;MAAEC,KAAK,EAAE;IAA+C;EACzE,CAAC;EACDK,KAAK,EAAE;IACHJ,KAAK,EAAE;MAAED,KAAK,EAAE;IAAiC;EACrD;AACJ,CAAC;AAED,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}