{"ast": null, "code": "import { __assign, __awaiter, __generator } from \"tslib\";\nimport { getRegionInfo } from \"@aws-sdk/config-resolver\";\nvar regionHash = {\n  \"us-east-1\": {\n    variants: [{\n      hostname: \"runtime-fips.lex.us-east-1.amazonaws.com\",\n      tags: [\"fips\"]\n    }]\n  },\n  \"us-gov-west-1\": {\n    variants: [{\n      hostname: \"runtime-fips.lex.us-gov-west-1.amazonaws.com\",\n      tags: [\"fips\"]\n    }]\n  },\n  \"us-west-2\": {\n    variants: [{\n      hostname: \"runtime-fips.lex.us-west-2.amazonaws.com\",\n      tags: [\"fips\"]\n    }]\n  }\n};\nvar partitionHash = {\n  aws: {\n    regions: [\"af-south-1\", \"ap-east-1\", \"ap-northeast-1\", \"ap-northeast-2\", \"ap-northeast-3\", \"ap-south-1\", \"ap-southeast-1\", \"ap-southeast-2\", \"ap-southeast-3\", \"ca-central-1\", \"eu-central-1\", \"eu-north-1\", \"eu-south-1\", \"eu-west-1\", \"eu-west-2\", \"eu-west-3\", \"me-central-1\", \"me-south-1\", \"sa-east-1\", \"us-east-1\", \"us-east-1-fips\", \"us-east-2\", \"us-west-1\", \"us-west-2\", \"us-west-2-fips\"],\n    regionRegex: \"^(us|eu|ap|sa|ca|me|af)\\\\-\\\\w+\\\\-\\\\d+$\",\n    variants: [{\n      hostname: \"runtime.lex.{region}.amazonaws.com\",\n      tags: []\n    }, {\n      hostname: \"runtime-fips.lex.{region}.amazonaws.com\",\n      tags: [\"fips\"]\n    }, {\n      hostname: \"runtime.lex-fips.{region}.api.aws\",\n      tags: [\"dualstack\", \"fips\"]\n    }, {\n      hostname: \"runtime.lex.{region}.api.aws\",\n      tags: [\"dualstack\"]\n    }]\n  },\n  \"aws-cn\": {\n    regions: [\"cn-north-1\", \"cn-northwest-1\"],\n    regionRegex: \"^cn\\\\-\\\\w+\\\\-\\\\d+$\",\n    variants: [{\n      hostname: \"runtime.lex.{region}.amazonaws.com.cn\",\n      tags: []\n    }, {\n      hostname: \"runtime.lex-fips.{region}.amazonaws.com.cn\",\n      tags: [\"fips\"]\n    }, {\n      hostname: \"runtime.lex-fips.{region}.api.amazonwebservices.com.cn\",\n      tags: [\"dualstack\", \"fips\"]\n    }, {\n      hostname: \"runtime.lex.{region}.api.amazonwebservices.com.cn\",\n      tags: [\"dualstack\"]\n    }]\n  },\n  \"aws-iso\": {\n    regions: [\"us-iso-east-1\", \"us-iso-west-1\"],\n    regionRegex: \"^us\\\\-iso\\\\-\\\\w+\\\\-\\\\d+$\",\n    variants: [{\n      hostname: \"runtime.lex.{region}.c2s.ic.gov\",\n      tags: []\n    }, {\n      hostname: \"runtime.lex-fips.{region}.c2s.ic.gov\",\n      tags: [\"fips\"]\n    }]\n  },\n  \"aws-iso-b\": {\n    regions: [\"us-isob-east-1\"],\n    regionRegex: \"^us\\\\-isob\\\\-\\\\w+\\\\-\\\\d+$\",\n    variants: [{\n      hostname: \"runtime.lex.{region}.sc2s.sgov.gov\",\n      tags: []\n    }, {\n      hostname: \"runtime.lex-fips.{region}.sc2s.sgov.gov\",\n      tags: [\"fips\"]\n    }]\n  },\n  \"aws-us-gov\": {\n    regions: [\"us-gov-east-1\", \"us-gov-west-1\", \"us-gov-west-1-fips\"],\n    regionRegex: \"^us\\\\-gov\\\\-\\\\w+\\\\-\\\\d+$\",\n    variants: [{\n      hostname: \"runtime.lex.{region}.amazonaws.com\",\n      tags: []\n    }, {\n      hostname: \"runtime-fips.lex.{region}.amazonaws.com\",\n      tags: [\"fips\"]\n    }, {\n      hostname: \"runtime.lex-fips.{region}.api.aws\",\n      tags: [\"dualstack\", \"fips\"]\n    }, {\n      hostname: \"runtime.lex.{region}.api.aws\",\n      tags: [\"dualstack\"]\n    }]\n  }\n};\nexport var defaultRegionInfoProvider = function (region, options) {\n  return __awaiter(void 0, void 0, void 0, function () {\n    return __generator(this, function (_a) {\n      return [2, getRegionInfo(region, __assign(__assign({}, options), {\n        signingService: \"lex\",\n        regionHash: regionHash,\n        partitionHash: partitionHash\n      }))];\n    });\n  });\n};", "map": {"version": 3, "names": ["__assign", "__awaiter", "__generator", "getRegionInfo", "regionHash", "variants", "hostname", "tags", "partitionHash", "aws", "regions", "regionRegex", "defaultRegionInfoProvider", "region", "options", "_a", "signingService"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-lex-runtime-service/dist-es/endpoints.js"], "sourcesContent": ["import { __assign, __awaiter, __generator } from \"tslib\";\nimport { getRegionInfo } from \"@aws-sdk/config-resolver\";\nvar regionHash = {\n    \"us-east-1\": {\n        variants: [\n            {\n                hostname: \"runtime-fips.lex.us-east-1.amazonaws.com\",\n                tags: [\"fips\"],\n            },\n        ],\n    },\n    \"us-gov-west-1\": {\n        variants: [\n            {\n                hostname: \"runtime-fips.lex.us-gov-west-1.amazonaws.com\",\n                tags: [\"fips\"],\n            },\n        ],\n    },\n    \"us-west-2\": {\n        variants: [\n            {\n                hostname: \"runtime-fips.lex.us-west-2.amazonaws.com\",\n                tags: [\"fips\"],\n            },\n        ],\n    },\n};\nvar partitionHash = {\n    aws: {\n        regions: [\n            \"af-south-1\",\n            \"ap-east-1\",\n            \"ap-northeast-1\",\n            \"ap-northeast-2\",\n            \"ap-northeast-3\",\n            \"ap-south-1\",\n            \"ap-southeast-1\",\n            \"ap-southeast-2\",\n            \"ap-southeast-3\",\n            \"ca-central-1\",\n            \"eu-central-1\",\n            \"eu-north-1\",\n            \"eu-south-1\",\n            \"eu-west-1\",\n            \"eu-west-2\",\n            \"eu-west-3\",\n            \"me-central-1\",\n            \"me-south-1\",\n            \"sa-east-1\",\n            \"us-east-1\",\n            \"us-east-1-fips\",\n            \"us-east-2\",\n            \"us-west-1\",\n            \"us-west-2\",\n            \"us-west-2-fips\",\n        ],\n        regionRegex: \"^(us|eu|ap|sa|ca|me|af)\\\\-\\\\w+\\\\-\\\\d+$\",\n        variants: [\n            {\n                hostname: \"runtime.lex.{region}.amazonaws.com\",\n                tags: [],\n            },\n            {\n                hostname: \"runtime-fips.lex.{region}.amazonaws.com\",\n                tags: [\"fips\"],\n            },\n            {\n                hostname: \"runtime.lex-fips.{region}.api.aws\",\n                tags: [\"dualstack\", \"fips\"],\n            },\n            {\n                hostname: \"runtime.lex.{region}.api.aws\",\n                tags: [\"dualstack\"],\n            },\n        ],\n    },\n    \"aws-cn\": {\n        regions: [\"cn-north-1\", \"cn-northwest-1\"],\n        regionRegex: \"^cn\\\\-\\\\w+\\\\-\\\\d+$\",\n        variants: [\n            {\n                hostname: \"runtime.lex.{region}.amazonaws.com.cn\",\n                tags: [],\n            },\n            {\n                hostname: \"runtime.lex-fips.{region}.amazonaws.com.cn\",\n                tags: [\"fips\"],\n            },\n            {\n                hostname: \"runtime.lex-fips.{region}.api.amazonwebservices.com.cn\",\n                tags: [\"dualstack\", \"fips\"],\n            },\n            {\n                hostname: \"runtime.lex.{region}.api.amazonwebservices.com.cn\",\n                tags: [\"dualstack\"],\n            },\n        ],\n    },\n    \"aws-iso\": {\n        regions: [\"us-iso-east-1\", \"us-iso-west-1\"],\n        regionRegex: \"^us\\\\-iso\\\\-\\\\w+\\\\-\\\\d+$\",\n        variants: [\n            {\n                hostname: \"runtime.lex.{region}.c2s.ic.gov\",\n                tags: [],\n            },\n            {\n                hostname: \"runtime.lex-fips.{region}.c2s.ic.gov\",\n                tags: [\"fips\"],\n            },\n        ],\n    },\n    \"aws-iso-b\": {\n        regions: [\"us-isob-east-1\"],\n        regionRegex: \"^us\\\\-isob\\\\-\\\\w+\\\\-\\\\d+$\",\n        variants: [\n            {\n                hostname: \"runtime.lex.{region}.sc2s.sgov.gov\",\n                tags: [],\n            },\n            {\n                hostname: \"runtime.lex-fips.{region}.sc2s.sgov.gov\",\n                tags: [\"fips\"],\n            },\n        ],\n    },\n    \"aws-us-gov\": {\n        regions: [\"us-gov-east-1\", \"us-gov-west-1\", \"us-gov-west-1-fips\"],\n        regionRegex: \"^us\\\\-gov\\\\-\\\\w+\\\\-\\\\d+$\",\n        variants: [\n            {\n                hostname: \"runtime.lex.{region}.amazonaws.com\",\n                tags: [],\n            },\n            {\n                hostname: \"runtime-fips.lex.{region}.amazonaws.com\",\n                tags: [\"fips\"],\n            },\n            {\n                hostname: \"runtime.lex-fips.{region}.api.aws\",\n                tags: [\"dualstack\", \"fips\"],\n            },\n            {\n                hostname: \"runtime.lex.{region}.api.aws\",\n                tags: [\"dualstack\"],\n            },\n        ],\n    },\n};\nexport var defaultRegionInfoProvider = function (region, options) { return __awaiter(void 0, void 0, void 0, function () {\n    return __generator(this, function (_a) {\n        return [2, getRegionInfo(region, __assign(__assign({}, options), { signingService: \"lex\", regionHash: regionHash, partitionHash: partitionHash }))];\n    });\n}); };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,aAAa,QAAQ,0BAA0B;AACxD,IAAIC,UAAU,GAAG;EACb,WAAW,EAAE;IACTC,QAAQ,EAAE,CACN;MACIC,QAAQ,EAAE,0CAA0C;MACpDC,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC;EAET,CAAC;EACD,eAAe,EAAE;IACbF,QAAQ,EAAE,CACN;MACIC,QAAQ,EAAE,8CAA8C;MACxDC,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC;EAET,CAAC;EACD,WAAW,EAAE;IACTF,QAAQ,EAAE,CACN;MACIC,QAAQ,EAAE,0CAA0C;MACpDC,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC;EAET;AACJ,CAAC;AACD,IAAIC,aAAa,GAAG;EAChBC,GAAG,EAAE;IACDC,OAAO,EAAE,CACL,YAAY,EACZ,WAAW,EACX,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,cAAc,EACd,cAAc,EACd,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,WAAW,EACX,WAAW,EACX,cAAc,EACd,YAAY,EACZ,WAAW,EACX,WAAW,EACX,gBAAgB,EAChB,WAAW,EACX,WAAW,EACX,WAAW,EACX,gBAAgB,CACnB;IACDC,WAAW,EAAE,wCAAwC;IACrDN,QAAQ,EAAE,CACN;MACIC,QAAQ,EAAE,oCAAoC;MAC9CC,IAAI,EAAE;IACV,CAAC,EACD;MACID,QAAQ,EAAE,yCAAyC;MACnDC,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC,EACD;MACID,QAAQ,EAAE,mCAAmC;MAC7CC,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM;IAC9B,CAAC,EACD;MACID,QAAQ,EAAE,8BAA8B;MACxCC,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC;EAET,CAAC;EACD,QAAQ,EAAE;IACNG,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;IACzCC,WAAW,EAAE,oBAAoB;IACjCN,QAAQ,EAAE,CACN;MACIC,QAAQ,EAAE,uCAAuC;MACjDC,IAAI,EAAE;IACV,CAAC,EACD;MACID,QAAQ,EAAE,4CAA4C;MACtDC,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC,EACD;MACID,QAAQ,EAAE,wDAAwD;MAClEC,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM;IAC9B,CAAC,EACD;MACID,QAAQ,EAAE,mDAAmD;MAC7DC,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC;EAET,CAAC;EACD,SAAS,EAAE;IACPG,OAAO,EAAE,CAAC,eAAe,EAAE,eAAe,CAAC;IAC3CC,WAAW,EAAE,0BAA0B;IACvCN,QAAQ,EAAE,CACN;MACIC,QAAQ,EAAE,iCAAiC;MAC3CC,IAAI,EAAE;IACV,CAAC,EACD;MACID,QAAQ,EAAE,sCAAsC;MAChDC,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC;EAET,CAAC;EACD,WAAW,EAAE;IACTG,OAAO,EAAE,CAAC,gBAAgB,CAAC;IAC3BC,WAAW,EAAE,2BAA2B;IACxCN,QAAQ,EAAE,CACN;MACIC,QAAQ,EAAE,oCAAoC;MAC9CC,IAAI,EAAE;IACV,CAAC,EACD;MACID,QAAQ,EAAE,yCAAyC;MACnDC,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC;EAET,CAAC;EACD,YAAY,EAAE;IACVG,OAAO,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,oBAAoB,CAAC;IACjEC,WAAW,EAAE,0BAA0B;IACvCN,QAAQ,EAAE,CACN;MACIC,QAAQ,EAAE,oCAAoC;MAC9CC,IAAI,EAAE;IACV,CAAC,EACD;MACID,QAAQ,EAAE,yCAAyC;MACnDC,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC,EACD;MACID,QAAQ,EAAE,mCAAmC;MAC7CC,IAAI,EAAE,CAAC,WAAW,EAAE,MAAM;IAC9B,CAAC,EACD;MACID,QAAQ,EAAE,8BAA8B;MACxCC,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC;EAET;AACJ,CAAC;AACD,OAAO,IAAIK,yBAAyB,GAAG,SAAAA,CAAUC,MAAM,EAAEC,OAAO,EAAE;EAAE,OAAOb,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;IACrH,OAAOC,WAAW,CAAC,IAAI,EAAE,UAAUa,EAAE,EAAE;MACnC,OAAO,CAAC,CAAC,EAAEZ,aAAa,CAACU,MAAM,EAAEb,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEc,OAAO,CAAC,EAAE;QAAEE,cAAc,EAAE,KAAK;QAAEZ,UAAU,EAAEA,UAAU;QAAEI,aAAa,EAAEA;MAAc,CAAC,CAAC,CAAC,CAAC;IACvJ,CAAC,CAAC;EACN,CAAC,CAAC;AAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}