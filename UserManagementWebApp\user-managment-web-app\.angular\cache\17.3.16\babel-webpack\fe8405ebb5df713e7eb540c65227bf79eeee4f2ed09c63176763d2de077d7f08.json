{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { GetFaceDetectionRequest, GetFaceDetectionResponse } from \"../models/models_0\";\nimport { deserializeAws_json1_1GetFaceDetectionCommand, serializeAws_json1_1GetFaceDetectionCommand } from \"../protocols/Aws_json1_1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Gets face detection results for a Amazon Rekognition Video analysis started by <a>StartFaceDetection</a>.</p>\n *          <p>Face detection with Amazon Rekognition Video is an asynchronous operation. You start face detection by calling <a>StartFaceDetection</a>\n *      which returns a job identifier (<code>JobId</code>). When the face detection operation finishes, Amazon Rekognition Video publishes a completion status to\n *      the Amazon Simple Notification Service topic registered in the initial call to <code>StartFaceDetection</code>. To get the results\n *      of the face detection operation, first check that the status value published to the Amazon SNS topic is <code>SUCCEEDED</code>.\n *      If so, call  <a>GetFaceDetection</a> and pass the job identifier\n *      (<code>JobId</code>) from the initial call to <code>StartFaceDetection</code>.</p>\n *          <p>\n *             <code>GetFaceDetection</code> returns an array of detected faces (<code>Faces</code>) sorted by the time the faces were detected. </p>\n *          <p>Use MaxResults parameter to limit the number of labels returned. If there are more results than\n *    specified in <code>MaxResults</code>, the value of <code>NextToken</code> in the operation response contains a pagination token for getting the next set\n *    of results. To get the next page of results, call <code>GetFaceDetection</code> and populate the <code>NextToken</code> request parameter with the token\n *     value returned from the previous call to <code>GetFaceDetection</code>.</p>\n */\nvar GetFaceDetectionCommand = /** @class */function (_super) {\n  __extends(GetFaceDetectionCommand, _super);\n  // Start section: command_properties\n  // End section: command_properties\n  function GetFaceDetectionCommand(input) {\n    var _this =\n    // Start section: command_constructor\n    _super.call(this) || this;\n    _this.input = input;\n    return _this;\n    // End section: command_constructor\n  }\n  /**\n   * @internal\n   */\n  GetFaceDetectionCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n    this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n    var stack = clientStack.concat(this.middlewareStack);\n    var logger = configuration.logger;\n    var clientName = \"RekognitionClient\";\n    var commandName = \"GetFaceDetectionCommand\";\n    var handlerExecutionContext = {\n      logger: logger,\n      clientName: clientName,\n      commandName: commandName,\n      inputFilterSensitiveLog: GetFaceDetectionRequest.filterSensitiveLog,\n      outputFilterSensitiveLog: GetFaceDetectionResponse.filterSensitiveLog\n    };\n    var requestHandler = configuration.requestHandler;\n    return stack.resolve(function (request) {\n      return requestHandler.handle(request.request, options || {});\n    }, handlerExecutionContext);\n  };\n  GetFaceDetectionCommand.prototype.serialize = function (input, context) {\n    return serializeAws_json1_1GetFaceDetectionCommand(input, context);\n  };\n  GetFaceDetectionCommand.prototype.deserialize = function (output, context) {\n    return deserializeAws_json1_1GetFaceDetectionCommand(output, context);\n  };\n  return GetFaceDetectionCommand;\n}($Command);\nexport { GetFaceDetectionCommand };", "map": {"version": 3, "names": ["__extends", "GetFaceDetectionRequest", "GetFaceDetectionResponse", "deserializeAws_json1_1GetFaceDetectionCommand", "serializeAws_json1_1GetFaceDetectionCommand", "getSerdePlugin", "Command", "$Command", "GetFaceDetectionCommand", "_super", "input", "_this", "call", "prototype", "resolveMiddleware", "clientStack", "configuration", "options", "middlewareStack", "use", "serialize", "deserialize", "stack", "concat", "logger", "clientName", "commandName", "handlerExecutionContext", "inputFilterSensitiveLog", "filterSensitiveLog", "outputFilterSensitiveLog", "requestHandler", "resolve", "request", "handle", "context", "output"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-rekognition/dist/es/commands/GetFaceDetectionCommand.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { GetFaceDetectionRequest, GetFaceDetectionResponse } from \"../models/models_0\";\nimport { deserializeAws_json1_1GetFaceDetectionCommand, serializeAws_json1_1GetFaceDetectionCommand, } from \"../protocols/Aws_json1_1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Gets face detection results for a Amazon Rekognition Video analysis started by <a>StartFaceDetection</a>.</p>\n *          <p>Face detection with Amazon Rekognition Video is an asynchronous operation. You start face detection by calling <a>StartFaceDetection</a>\n *      which returns a job identifier (<code>JobId</code>). When the face detection operation finishes, Amazon Rekognition Video publishes a completion status to\n *      the Amazon Simple Notification Service topic registered in the initial call to <code>StartFaceDetection</code>. To get the results\n *      of the face detection operation, first check that the status value published to the Amazon SNS topic is <code>SUCCEEDED</code>.\n *      If so, call  <a>GetFaceDetection</a> and pass the job identifier\n *      (<code>JobId</code>) from the initial call to <code>StartFaceDetection</code>.</p>\n *          <p>\n *             <code>GetFaceDetection</code> returns an array of detected faces (<code>Faces</code>) sorted by the time the faces were detected. </p>\n *          <p>Use MaxResults parameter to limit the number of labels returned. If there are more results than\n *    specified in <code>MaxResults</code>, the value of <code>NextToken</code> in the operation response contains a pagination token for getting the next set\n *    of results. To get the next page of results, call <code>GetFaceDetection</code> and populate the <code>NextToken</code> request parameter with the token\n *     value returned from the previous call to <code>GetFaceDetection</code>.</p>\n */\nvar GetFaceDetectionCommand = /** @class */ (function (_super) {\n    __extends(GetFaceDetectionCommand, _super);\n    // Start section: command_properties\n    // End section: command_properties\n    function GetFaceDetectionCommand(input) {\n        var _this = \n        // Start section: command_constructor\n        _super.call(this) || this;\n        _this.input = input;\n        return _this;\n        // End section: command_constructor\n    }\n    /**\n     * @internal\n     */\n    GetFaceDetectionCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n        this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n        var stack = clientStack.concat(this.middlewareStack);\n        var logger = configuration.logger;\n        var clientName = \"RekognitionClient\";\n        var commandName = \"GetFaceDetectionCommand\";\n        var handlerExecutionContext = {\n            logger: logger,\n            clientName: clientName,\n            commandName: commandName,\n            inputFilterSensitiveLog: GetFaceDetectionRequest.filterSensitiveLog,\n            outputFilterSensitiveLog: GetFaceDetectionResponse.filterSensitiveLog,\n        };\n        var requestHandler = configuration.requestHandler;\n        return stack.resolve(function (request) {\n            return requestHandler.handle(request.request, options || {});\n        }, handlerExecutionContext);\n    };\n    GetFaceDetectionCommand.prototype.serialize = function (input, context) {\n        return serializeAws_json1_1GetFaceDetectionCommand(input, context);\n    };\n    GetFaceDetectionCommand.prototype.deserialize = function (output, context) {\n        return deserializeAws_json1_1GetFaceDetectionCommand(output, context);\n    };\n    return GetFaceDetectionCommand;\n}($Command));\nexport { GetFaceDetectionCommand };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,uBAAuB,EAAEC,wBAAwB,QAAQ,oBAAoB;AACtF,SAASC,6CAA6C,EAAEC,2CAA2C,QAAS,0BAA0B;AACtI,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,OAAO,IAAIC,QAAQ,QAAQ,wBAAwB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,uBAAuB,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC3DT,SAAS,CAACQ,uBAAuB,EAAEC,MAAM,CAAC;EAC1C;EACA;EACA,SAASD,uBAAuBA,CAACE,KAAK,EAAE;IACpC,IAAIC,KAAK;IACT;IACAF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACzBD,KAAK,CAACD,KAAK,GAAGA,KAAK;IACnB,OAAOC,KAAK;IACZ;EACJ;EACA;AACJ;AACA;EACIH,uBAAuB,CAACK,SAAS,CAACC,iBAAiB,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAEC,OAAO,EAAE;IACjG,IAAI,CAACC,eAAe,CAACC,GAAG,CAACd,cAAc,CAACW,aAAa,EAAE,IAAI,CAACI,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;IACzF,IAAIC,KAAK,GAAGP,WAAW,CAACQ,MAAM,CAAC,IAAI,CAACL,eAAe,CAAC;IACpD,IAAIM,MAAM,GAAGR,aAAa,CAACQ,MAAM;IACjC,IAAIC,UAAU,GAAG,mBAAmB;IACpC,IAAIC,WAAW,GAAG,yBAAyB;IAC3C,IAAIC,uBAAuB,GAAG;MAC1BH,MAAM,EAAEA,MAAM;MACdC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA,WAAW;MACxBE,uBAAuB,EAAE3B,uBAAuB,CAAC4B,kBAAkB;MACnEC,wBAAwB,EAAE5B,wBAAwB,CAAC2B;IACvD,CAAC;IACD,IAAIE,cAAc,GAAGf,aAAa,CAACe,cAAc;IACjD,OAAOT,KAAK,CAACU,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpC,OAAOF,cAAc,CAACG,MAAM,CAACD,OAAO,CAACA,OAAO,EAAEhB,OAAO,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC,EAAEU,uBAAuB,CAAC;EAC/B,CAAC;EACDnB,uBAAuB,CAACK,SAAS,CAACO,SAAS,GAAG,UAAUV,KAAK,EAAEyB,OAAO,EAAE;IACpE,OAAO/B,2CAA2C,CAACM,KAAK,EAAEyB,OAAO,CAAC;EACtE,CAAC;EACD3B,uBAAuB,CAACK,SAAS,CAACQ,WAAW,GAAG,UAAUe,MAAM,EAAED,OAAO,EAAE;IACvE,OAAOhC,6CAA6C,CAACiC,MAAM,EAAED,OAAO,CAAC;EACzE,CAAC;EACD,OAAO3B,uBAAuB;AAClC,CAAC,CAACD,QAAQ,CAAE;AACZ,SAASC,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}