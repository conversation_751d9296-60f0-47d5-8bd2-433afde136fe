{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\nimport '../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../utils/retry/retry.mjs';\nimport '../../parseAWSExports.mjs';\nimport 'uuid';\nimport '../../singleton/Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport '../../Platform/index.mjs';\nimport '../../Platform/types.mjs';\nimport '../../BackgroundProcessManager/types.mjs';\nimport '../../Reachability/Reachability.mjs';\nimport '../../Hub/index.mjs';\nimport '../../utils/sessionListener/index.mjs';\nimport { parseMetadata } from '../../clients/serde/responseInfo.mjs';\nimport { parseJsonError, parseJsonBody } from '../../clients/serde/json.mjs';\nimport { composeServiceApi } from '../../clients/internal/composeServiceApi.mjs';\nimport { defaultConfig, cognitoIdentityTransferHandler, buildHttpRpcRequest, getSharedHeaders } from './base.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst getCredentialsForIdentitySerializer = (input, endpoint) => {\n  const headers = getSharedHeaders('GetCredentialsForIdentity');\n  const body = JSON.stringify(input);\n  return buildHttpRpcRequest(endpoint, headers, body);\n};\nconst getCredentialsForIdentityDeserializer = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (response) {\n    if (response.statusCode >= 300) {\n      const error = yield parseJsonError(response);\n      throw error;\n    } else {\n      const body = yield parseJsonBody(response);\n      return {\n        IdentityId: body.IdentityId,\n        Credentials: deserializeCredentials(body.Credentials),\n        $metadata: parseMetadata(response)\n      };\n    }\n  });\n  return function getCredentialsForIdentityDeserializer(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nconst deserializeCredentials = ({\n  AccessKeyId,\n  SecretKey,\n  SessionToken,\n  Expiration\n} = {}) => {\n  return {\n    AccessKeyId,\n    SecretKey,\n    SessionToken,\n    Expiration: Expiration && new Date(Expiration * 1000)\n  };\n};\n/**\n * @internal\n */\nconst getCredentialsForIdentity = composeServiceApi(cognitoIdentityTransferHandler, getCredentialsForIdentitySerializer, getCredentialsForIdentityDeserializer, defaultConfig);\nexport { getCredentialsForIdentity };", "map": {"version": 3, "names": ["parseMetadata", "parseJsonError", "parseJsonBody", "composeServiceApi", "defaultConfig", "cognitoIdentityTransferHandler", "buildHttpRpcRequest", "getSharedHeaders", "getCredentialsForIdentitySerializer", "input", "endpoint", "headers", "body", "JSON", "stringify", "getCredentialsForIdentityDeserializer", "_ref", "_asyncToGenerator", "response", "statusCode", "error", "IdentityId", "Credentials", "deserializeCredentials", "$metadata", "_x", "apply", "arguments", "AccessKeyId", "<PERSON><PERSON><PERSON>", "SessionToken", "Expiration", "Date", "getCredentialsForIdentity"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/awsClients/cognitoIdentity/getCredentialsForIdentity.mjs"], "sourcesContent": ["import '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\nimport '../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../utils/retry/retry.mjs';\nimport '../../parseAWSExports.mjs';\nimport 'uuid';\nimport '../../singleton/Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport '../../Platform/index.mjs';\nimport '../../Platform/types.mjs';\nimport '../../BackgroundProcessManager/types.mjs';\nimport '../../Reachability/Reachability.mjs';\nimport '../../Hub/index.mjs';\nimport '../../utils/sessionListener/index.mjs';\nimport { parseMetadata } from '../../clients/serde/responseInfo.mjs';\nimport { parseJsonError, parseJsonBody } from '../../clients/serde/json.mjs';\nimport { composeServiceApi } from '../../clients/internal/composeServiceApi.mjs';\nimport { defaultConfig, cognitoIdentityTransferHandler, buildHttpRpcRequest, getSharedHeaders } from './base.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst getCredentialsForIdentitySerializer = (input, endpoint) => {\n    const headers = getSharedHeaders('GetCredentialsForIdentity');\n    const body = JSON.stringify(input);\n    return buildHttpRpcRequest(endpoint, headers, body);\n};\nconst getCredentialsForIdentityDeserializer = async (response) => {\n    if (response.statusCode >= 300) {\n        const error = await parseJsonError(response);\n        throw error;\n    }\n    else {\n        const body = await parseJsonBody(response);\n        return {\n            IdentityId: body.IdentityId,\n            Credentials: deserializeCredentials(body.Credentials),\n            $metadata: parseMetadata(response),\n        };\n    }\n};\nconst deserializeCredentials = ({ AccessKeyId, SecretKey, SessionToken, Expiration, } = {}) => {\n    return {\n        AccessKeyId,\n        SecretKey,\n        SessionToken,\n        Expiration: Expiration && new Date(Expiration * 1000),\n    };\n};\n/**\n * @internal\n */\nconst getCredentialsForIdentity = composeServiceApi(cognitoIdentityTransferHandler, getCredentialsForIdentitySerializer, getCredentialsForIdentityDeserializer, defaultConfig);\n\nexport { getCredentialsForIdentity };\n"], "mappings": ";AAAA,OAAO,wBAAwB;AAC/B,OAAO,+BAA+B;AACtC,OAAO,6CAA6C;AACpD,OAAO,6BAA6B;AACpC,OAAO,2BAA2B;AAClC,OAAO,MAAM;AACb,OAAO,6CAA6C;AACpD,OAAO,uBAAuB;AAC9B,OAAO,2BAA2B;AAClC,OAAO,0BAA0B;AACjC,OAAO,0BAA0B;AACjC,OAAO,0CAA0C;AACjD,OAAO,qCAAqC;AAC5C,OAAO,qBAAqB;AAC5B,OAAO,uCAAuC;AAC9C,SAASA,aAAa,QAAQ,sCAAsC;AACpE,SAASC,cAAc,EAAEC,aAAa,QAAQ,8BAA8B;AAC5E,SAASC,iBAAiB,QAAQ,8CAA8C;AAChF,SAASC,aAAa,EAAEC,8BAA8B,EAAEC,mBAAmB,EAAEC,gBAAgB,QAAQ,YAAY;;AAEjH;AACA;AACA,MAAMC,mCAAmC,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;EAC7D,MAAMC,OAAO,GAAGJ,gBAAgB,CAAC,2BAA2B,CAAC;EAC7D,MAAMK,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACL,KAAK,CAAC;EAClC,OAAOH,mBAAmB,CAACI,QAAQ,EAAEC,OAAO,EAAEC,IAAI,CAAC;AACvD,CAAC;AACD,MAAMG,qCAAqC;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,QAAQ,EAAK;IAC9D,IAAIA,QAAQ,CAACC,UAAU,IAAI,GAAG,EAAE;MAC5B,MAAMC,KAAK,SAASnB,cAAc,CAACiB,QAAQ,CAAC;MAC5C,MAAME,KAAK;IACf,CAAC,MACI;MACD,MAAMR,IAAI,SAASV,aAAa,CAACgB,QAAQ,CAAC;MAC1C,OAAO;QACHG,UAAU,EAAET,IAAI,CAACS,UAAU;QAC3BC,WAAW,EAAEC,sBAAsB,CAACX,IAAI,CAACU,WAAW,CAAC;QACrDE,SAAS,EAAExB,aAAa,CAACkB,QAAQ;MACrC,CAAC;IACL;EACJ,CAAC;EAAA,gBAbKH,qCAAqCA,CAAAU,EAAA;IAAA,OAAAT,IAAA,CAAAU,KAAA,OAAAC,SAAA;EAAA;AAAA,GAa1C;AACD,MAAMJ,sBAAsB,GAAGA,CAAC;EAAEK,WAAW;EAAEC,SAAS;EAAEC,YAAY;EAAEC;AAAY,CAAC,GAAG,CAAC,CAAC,KAAK;EAC3F,OAAO;IACHH,WAAW;IACXC,SAAS;IACTC,YAAY;IACZC,UAAU,EAAEA,UAAU,IAAI,IAAIC,IAAI,CAACD,UAAU,GAAG,IAAI;EACxD,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA,MAAME,yBAAyB,GAAG9B,iBAAiB,CAACE,8BAA8B,EAAEG,mCAAmC,EAAEO,qCAAqC,EAAEX,aAAa,CAAC;AAE9K,SAAS6B,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}