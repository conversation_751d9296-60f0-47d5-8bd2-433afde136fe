{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar AuthValidationErrorCode;\n(function (AuthValidationErrorCode) {\n  AuthValidationErrorCode[\"EmptySignInUsername\"] = \"EmptySignInUsername\";\n  AuthValidationErrorCode[\"EmptySignInPassword\"] = \"EmptySignInPassword\";\n  AuthValidationErrorCode[\"CustomAuthSignInPassword\"] = \"CustomAuthSignInPassword\";\n  AuthValidationErrorCode[\"EmptySignUpUsername\"] = \"EmptySignUpUsername\";\n  AuthValidationErrorCode[\"EmptySignUpPassword\"] = \"EmptySignUpPassword\";\n  AuthValidationErrorCode[\"EmptyConfirmSignUpUsername\"] = \"EmptyConfirmSignUpUsername\";\n  AuthValidationErrorCode[\"EmptyConfirmSignUpCode\"] = \"EmptyConfirmSignUpCode\";\n  AuthValidationErrorCode[\"EmptyResendSignUpCodeUsername\"] = \"EmptyresendSignUpCodeUsername\";\n  AuthValidationErrorCode[\"EmptyChallengeResponse\"] = \"EmptyChallengeResponse\";\n  AuthValidationErrorCode[\"EmptyConfirmResetPasswordUsername\"] = \"EmptyConfirmResetPasswordUsername\";\n  AuthValidationErrorCode[\"EmptyConfirmResetPasswordNewPassword\"] = \"EmptyConfirmResetPasswordNewPassword\";\n  AuthValidationErrorCode[\"EmptyConfirmResetPasswordConfirmationCode\"] = \"EmptyConfirmResetPasswordConfirmationCode\";\n  AuthValidationErrorCode[\"EmptyResetPasswordUsername\"] = \"EmptyResetPasswordUsername\";\n  AuthValidationErrorCode[\"EmptyVerifyTOTPSetupCode\"] = \"EmptyVerifyTOTPSetupCode\";\n  AuthValidationErrorCode[\"EmptyConfirmUserAttributeCode\"] = \"EmptyConfirmUserAttributeCode\";\n  AuthValidationErrorCode[\"IncorrectMFAMethod\"] = \"IncorrectMFAMethod\";\n  AuthValidationErrorCode[\"EmptyUpdatePassword\"] = \"EmptyUpdatePassword\";\n})(AuthValidationErrorCode || (AuthValidationErrorCode = {}));\nexport { AuthValidationErrorCode };", "map": {"version": 3, "names": ["AuthValidationErrorCode"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/errors/types/validation.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar AuthValidationErrorCode;\n(function (AuthValidationErrorCode) {\n    AuthValidationErrorCode[\"EmptySignInUsername\"] = \"EmptySignInUsername\";\n    AuthValidationErrorCode[\"EmptySignInPassword\"] = \"EmptySignInPassword\";\n    AuthValidationErrorCode[\"CustomAuthSignInPassword\"] = \"CustomAuthSignInPassword\";\n    AuthValidationErrorCode[\"EmptySignUpUsername\"] = \"EmptySignUpUsername\";\n    AuthValidationErrorCode[\"EmptySignUpPassword\"] = \"EmptySignUpPassword\";\n    AuthValidationErrorCode[\"EmptyConfirmSignUpUsername\"] = \"EmptyConfirmSignUpUsername\";\n    AuthValidationErrorCode[\"EmptyConfirmSignUpCode\"] = \"EmptyConfirmSignUpCode\";\n    AuthValidationErrorCode[\"EmptyResendSignUpCodeUsername\"] = \"EmptyresendSignUpCodeUsername\";\n    AuthValidationErrorCode[\"EmptyChallengeResponse\"] = \"EmptyChallengeResponse\";\n    AuthValidationErrorCode[\"EmptyConfirmResetPasswordUsername\"] = \"EmptyConfirmResetPasswordUsername\";\n    AuthValidationErrorCode[\"EmptyConfirmResetPasswordNewPassword\"] = \"EmptyConfirmResetPasswordNewPassword\";\n    AuthValidationErrorCode[\"EmptyConfirmResetPasswordConfirmationCode\"] = \"EmptyConfirmResetPasswordConfirmationCode\";\n    AuthValidationErrorCode[\"EmptyResetPasswordUsername\"] = \"EmptyResetPasswordUsername\";\n    AuthValidationErrorCode[\"EmptyVerifyTOTPSetupCode\"] = \"EmptyVerifyTOTPSetupCode\";\n    AuthValidationErrorCode[\"EmptyConfirmUserAttributeCode\"] = \"EmptyConfirmUserAttributeCode\";\n    AuthValidationErrorCode[\"IncorrectMFAMethod\"] = \"IncorrectMFAMethod\";\n    AuthValidationErrorCode[\"EmptyUpdatePassword\"] = \"EmptyUpdatePassword\";\n})(AuthValidationErrorCode || (AuthValidationErrorCode = {}));\n\nexport { AuthValidationErrorCode };\n"], "mappings": "AAAA;AACA;AACA,IAAIA,uBAAuB;AAC3B,CAAC,UAAUA,uBAAuB,EAAE;EAChCA,uBAAuB,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EACtEA,uBAAuB,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EACtEA,uBAAuB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAChFA,uBAAuB,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EACtEA,uBAAuB,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EACtEA,uBAAuB,CAAC,4BAA4B,CAAC,GAAG,4BAA4B;EACpFA,uBAAuB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC5EA,uBAAuB,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;EAC1FA,uBAAuB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EAC5EA,uBAAuB,CAAC,mCAAmC,CAAC,GAAG,mCAAmC;EAClGA,uBAAuB,CAAC,sCAAsC,CAAC,GAAG,sCAAsC;EACxGA,uBAAuB,CAAC,2CAA2C,CAAC,GAAG,2CAA2C;EAClHA,uBAAuB,CAAC,4BAA4B,CAAC,GAAG,4BAA4B;EACpFA,uBAAuB,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EAChFA,uBAAuB,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;EAC1FA,uBAAuB,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACpEA,uBAAuB,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;AAC1E,CAAC,EAAEA,uBAAuB,KAAKA,uBAAuB,GAAG,CAAC,CAAC,CAAC,CAAC;AAE7D,SAASA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}