{"ast": null, "code": "import '@aws-amplify/core/internals/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { cloneDeep, has } from '../../utils/utils.mjs';\nimport { getName, getPathFromName, resolveReference, usesReference } from '../../utils/references.mjs';\nimport { REFERENCE_REGEX } from './constants.mjs';\nconst DEFAULTS = {\n  ignoreKeys: ['original']\n};\nfunction resolveObject(object) {\n  const foundCirc = {};\n  const clone = cloneDeep(object); // This object will be edited\n  const currentContext = []; // To maintain the context to be able to test for circular definitions\n  if (typeof object === 'object') {\n    return traverseObject({\n      slice: clone,\n      fullObj: clone,\n      currentContext,\n      foundCirc\n    });\n  } else {\n    throw new Error('Please pass an object in');\n  }\n}\n/**\n * Recursively traverses an object (slice) to resolve and uses\n * compileValue to replace any string references found within it\n */\nfunction traverseObject({\n  slice,\n  fullObj,\n  currentContext,\n  foundCirc\n}) {\n  for (let key in slice) {\n    if (!has(slice, key)) {\n      continue;\n    }\n    const prop = slice[key];\n    // We want to check for ignoredKeys, this is to\n    // skip over attributes that should not be\n    // mutated, like a copy of the original property.\n    if (DEFAULTS.ignoreKeys && DEFAULTS.ignoreKeys.indexOf(key) !== -1) {\n      continue;\n    }\n    currentContext.push(key);\n    if (typeof prop === 'object') {\n      traverseObject({\n        currentContext,\n        slice: prop,\n        fullObj,\n        foundCirc\n      });\n    } else {\n      if (typeof prop === 'string' && prop.indexOf('{') > -1) {\n        slice[key] = compileValue({\n          value: prop,\n          stack: [getName(currentContext)],\n          foundCirc,\n          fullObj\n        });\n      }\n    }\n    currentContext.pop();\n  }\n  return fullObj;\n}\n/**\n * Resolves references in a value, performing recursive lookups when references are nested.\n * value: The string that may contain references (e.g., {color.border.light}) that need to be replaced\n * stack: keeps track of the current chain of references to detect circular references\n * foundCirc: stores any detected circular references\n * fullObj: The full object where references are looked up, essentially the source of all values\n */\nfunction compileValue({\n  value,\n  stack,\n  foundCirc,\n  fullObj\n}) {\n  let toRet = value,\n    ref;\n  const regex = new RegExp(REFERENCE_REGEX);\n  // Replace the reference inline, but don't replace the whole string because\n  // references can be part of the value such as \"1px solid {color.border.light}\"\n  value.replace(regex, function (match, variable) {\n    variable = variable.trim();\n    // Find what the value is referencing\n    const pathName = getPathFromName(variable);\n    const refHasValue = pathName[pathName.length - 1] === 'value';\n    stack.push(variable);\n    ref = resolveReference(pathName, fullObj);\n    // If the reference doesn't end in 'value'\n    // and\n    // the reference points to someplace that has a `value` attribute\n    // we should take the '.value' of the reference\n    // per the W3C draft spec where references do not have .value\n    // https://design-tokens.github.io/community-group/format/#aliases-references\n    if (!refHasValue && ref && has(ref, 'value')) {\n      ref = ref.value;\n    }\n    if (typeof ref !== 'undefined') {\n      if (typeof ref === 'string' || typeof ref === 'number') {\n        toRet = value.replace(match, ref);\n        // Recursive, therefore we can compute multi-layer variables like a = b, b = c, eventually a = c\n        if (usesReference(toRet)) {\n          var reference = toRet.slice(1, -1);\n          // Compare to found circular references\n          if (has(foundCirc, reference)) ;else if (stack.indexOf(reference) !== -1) {\n            // If the current stack already contains the current reference, we found a new circular reference\n            // chop down only the circular part, save it to our circular reference info, and spit out an error\n            // Get the position of the existing reference in the stack\n            var stackIndexReference = stack.indexOf(reference);\n            // Get the portion of the stack that starts at the circular reference and brings you through until the end\n            var circStack = stack.slice(stackIndexReference);\n            // For all the references in this list, add them to the list of references that end up in a circular reference\n            circStack.forEach(function (key) {\n              foundCirc[key] = true;\n            });\n            // Add our found circular reference to the end of the cycle\n            circStack.push(reference);\n          } else {\n            toRet = compileValue({\n              value: toRet,\n              stack,\n              foundCirc,\n              fullObj\n            });\n          }\n        }\n        // if evaluated value is a number and equal to the reference, we want to keep the type\n        if (typeof ref === 'number' && ref.toString() === toRet) {\n          toRet = ref;\n        }\n      } else {\n        // if evaluated value is not a string or number, we want to keep the type\n        toRet = ref;\n      }\n    } else {\n      toRet = ref;\n    }\n    stack.pop(variable);\n    return toRet;\n  });\n  return toRet;\n}\nexport { compileValue, resolveObject, traverseObject };", "map": {"version": 3, "names": ["cloneDeep", "has", "getName", "getPathFromName", "resolveReference", "usesReference", "REFERENCE_REGEX", "DEFAULTS", "<PERSON><PERSON><PERSON><PERSON>", "resolveObject", "object", "foundCirc", "clone", "currentContext", "traverseObject", "slice", "fullObj", "Error", "key", "prop", "indexOf", "push", "compileValue", "value", "stack", "pop", "toRet", "ref", "regex", "RegExp", "replace", "match", "variable", "trim", "pathName", "refHasValue", "length", "reference", "stackIndexReference", "circStack", "for<PERSON>ach", "toString"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/createTheme/resolveObject.mjs"], "sourcesContent": ["import '@aws-amplify/core/internals/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { cloneDeep, has } from '../../utils/utils.mjs';\nimport { getName, getPathFromName, resolveReference, usesReference } from '../../utils/references.mjs';\nimport { REFERENCE_REGEX } from './constants.mjs';\n\nconst DEFAULTS = {\n    ignoreKeys: ['original'],\n};\nfunction resolveObject(object) {\n    const foundCirc = {};\n    const clone = cloneDeep(object); // This object will be edited\n    const currentContext = []; // To maintain the context to be able to test for circular definitions\n    if (typeof object === 'object') {\n        return traverseObject({\n            slice: clone,\n            fullObj: clone,\n            currentContext,\n            foundCirc,\n        });\n    }\n    else {\n        throw new Error('Please pass an object in');\n    }\n}\n/**\n * Recursively traverses an object (slice) to resolve and uses\n * compileValue to replace any string references found within it\n */\nfunction traverseObject({ slice, fullObj, currentContext, foundCirc, }) {\n    for (let key in slice) {\n        if (!has(slice, key)) {\n            continue;\n        }\n        const prop = slice[key];\n        // We want to check for ignoredKeys, this is to\n        // skip over attributes that should not be\n        // mutated, like a copy of the original property.\n        if (DEFAULTS.ignoreKeys && DEFAULTS.ignoreKeys.indexOf(key) !== -1) {\n            continue;\n        }\n        currentContext.push(key);\n        if (typeof prop === 'object') {\n            traverseObject({ currentContext, slice: prop, fullObj, foundCirc });\n        }\n        else {\n            if (typeof prop === 'string' && prop.indexOf('{') > -1) {\n                slice[key] = compileValue({\n                    value: prop,\n                    stack: [getName(currentContext)],\n                    foundCirc,\n                    fullObj,\n                });\n            }\n        }\n        currentContext.pop();\n    }\n    return fullObj;\n}\n/**\n * Resolves references in a value, performing recursive lookups when references are nested.\n * value: The string that may contain references (e.g., {color.border.light}) that need to be replaced\n * stack: keeps track of the current chain of references to detect circular references\n * foundCirc: stores any detected circular references\n * fullObj: The full object where references are looked up, essentially the source of all values\n */\nfunction compileValue({ value, stack, foundCirc, fullObj }) {\n    let toRet = value, ref;\n    const regex = new RegExp(REFERENCE_REGEX);\n    // Replace the reference inline, but don't replace the whole string because\n    // references can be part of the value such as \"1px solid {color.border.light}\"\n    value.replace(regex, function (match, variable) {\n        variable = variable.trim();\n        // Find what the value is referencing\n        const pathName = getPathFromName(variable);\n        const refHasValue = pathName[pathName.length - 1] === 'value';\n        stack.push(variable);\n        ref = resolveReference(pathName, fullObj);\n        // If the reference doesn't end in 'value'\n        // and\n        // the reference points to someplace that has a `value` attribute\n        // we should take the '.value' of the reference\n        // per the W3C draft spec where references do not have .value\n        // https://design-tokens.github.io/community-group/format/#aliases-references\n        if (!refHasValue && ref && has(ref, 'value')) {\n            ref = ref.value;\n        }\n        if (typeof ref !== 'undefined') {\n            if (typeof ref === 'string' || typeof ref === 'number') {\n                toRet = value.replace(match, ref);\n                // Recursive, therefore we can compute multi-layer variables like a = b, b = c, eventually a = c\n                if (usesReference(toRet)) {\n                    var reference = toRet.slice(1, -1);\n                    // Compare to found circular references\n                    if (has(foundCirc, reference)) ;\n                    else if (stack.indexOf(reference) !== -1) {\n                        // If the current stack already contains the current reference, we found a new circular reference\n                        // chop down only the circular part, save it to our circular reference info, and spit out an error\n                        // Get the position of the existing reference in the stack\n                        var stackIndexReference = stack.indexOf(reference);\n                        // Get the portion of the stack that starts at the circular reference and brings you through until the end\n                        var circStack = stack.slice(stackIndexReference);\n                        // For all the references in this list, add them to the list of references that end up in a circular reference\n                        circStack.forEach(function (key) {\n                            foundCirc[key] = true;\n                        });\n                        // Add our found circular reference to the end of the cycle\n                        circStack.push(reference);\n                    }\n                    else {\n                        toRet = compileValue({ value: toRet, stack, foundCirc, fullObj });\n                    }\n                }\n                // if evaluated value is a number and equal to the reference, we want to keep the type\n                if (typeof ref === 'number' && ref.toString() === toRet) {\n                    toRet = ref;\n                }\n            }\n            else {\n                // if evaluated value is not a string or number, we want to keep the type\n                toRet = ref;\n            }\n        }\n        else {\n            toRet = ref;\n        }\n        stack.pop(variable);\n        return toRet;\n    });\n    return toRet;\n}\n\nexport { compileValue, resolveObject, traverseObject };\n"], "mappings": "AAAA,OAAO,mCAAmC;AAC1C,OAAO,wCAAwC;AAC/C,SAASA,SAAS,EAAEC,GAAG,QAAQ,uBAAuB;AACtD,SAASC,OAAO,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,4BAA4B;AACtG,SAASC,eAAe,QAAQ,iBAAiB;AAEjD,MAAMC,QAAQ,GAAG;EACbC,UAAU,EAAE,CAAC,UAAU;AAC3B,CAAC;AACD,SAASC,aAAaA,CAACC,MAAM,EAAE;EAC3B,MAAMC,SAAS,GAAG,CAAC,CAAC;EACpB,MAAMC,KAAK,GAAGZ,SAAS,CAACU,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMG,cAAc,GAAG,EAAE,CAAC,CAAC;EAC3B,IAAI,OAAOH,MAAM,KAAK,QAAQ,EAAE;IAC5B,OAAOI,cAAc,CAAC;MAClBC,KAAK,EAAEH,KAAK;MACZI,OAAO,EAAEJ,KAAK;MACdC,cAAc;MACdF;IACJ,CAAC,CAAC;EACN,CAAC,MACI;IACD,MAAM,IAAIM,KAAK,CAAC,0BAA0B,CAAC;EAC/C;AACJ;AACA;AACA;AACA;AACA;AACA,SAASH,cAAcA,CAAC;EAAEC,KAAK;EAAEC,OAAO;EAAEH,cAAc;EAAEF;AAAW,CAAC,EAAE;EACpE,KAAK,IAAIO,GAAG,IAAIH,KAAK,EAAE;IACnB,IAAI,CAACd,GAAG,CAACc,KAAK,EAAEG,GAAG,CAAC,EAAE;MAClB;IACJ;IACA,MAAMC,IAAI,GAAGJ,KAAK,CAACG,GAAG,CAAC;IACvB;IACA;IACA;IACA,IAAIX,QAAQ,CAACC,UAAU,IAAID,QAAQ,CAACC,UAAU,CAACY,OAAO,CAACF,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAChE;IACJ;IACAL,cAAc,CAACQ,IAAI,CAACH,GAAG,CAAC;IACxB,IAAI,OAAOC,IAAI,KAAK,QAAQ,EAAE;MAC1BL,cAAc,CAAC;QAAED,cAAc;QAAEE,KAAK,EAAEI,IAAI;QAAEH,OAAO;QAAEL;MAAU,CAAC,CAAC;IACvE,CAAC,MACI;MACD,IAAI,OAAOQ,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;QACpDL,KAAK,CAACG,GAAG,CAAC,GAAGI,YAAY,CAAC;UACtBC,KAAK,EAAEJ,IAAI;UACXK,KAAK,EAAE,CAACtB,OAAO,CAACW,cAAc,CAAC,CAAC;UAChCF,SAAS;UACTK;QACJ,CAAC,CAAC;MACN;IACJ;IACAH,cAAc,CAACY,GAAG,CAAC,CAAC;EACxB;EACA,OAAOT,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,YAAYA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEb,SAAS;EAAEK;AAAQ,CAAC,EAAE;EACxD,IAAIU,KAAK,GAAGH,KAAK;IAAEI,GAAG;EACtB,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAACvB,eAAe,CAAC;EACzC;EACA;EACAiB,KAAK,CAACO,OAAO,CAACF,KAAK,EAAE,UAAUG,KAAK,EAAEC,QAAQ,EAAE;IAC5CA,QAAQ,GAAGA,QAAQ,CAACC,IAAI,CAAC,CAAC;IAC1B;IACA,MAAMC,QAAQ,GAAG/B,eAAe,CAAC6B,QAAQ,CAAC;IAC1C,MAAMG,WAAW,GAAGD,QAAQ,CAACA,QAAQ,CAACE,MAAM,GAAG,CAAC,CAAC,KAAK,OAAO;IAC7DZ,KAAK,CAACH,IAAI,CAACW,QAAQ,CAAC;IACpBL,GAAG,GAAGvB,gBAAgB,CAAC8B,QAAQ,EAAElB,OAAO,CAAC;IACzC;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACmB,WAAW,IAAIR,GAAG,IAAI1B,GAAG,CAAC0B,GAAG,EAAE,OAAO,CAAC,EAAE;MAC1CA,GAAG,GAAGA,GAAG,CAACJ,KAAK;IACnB;IACA,IAAI,OAAOI,GAAG,KAAK,WAAW,EAAE;MAC5B,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QACpDD,KAAK,GAAGH,KAAK,CAACO,OAAO,CAACC,KAAK,EAAEJ,GAAG,CAAC;QACjC;QACA,IAAItB,aAAa,CAACqB,KAAK,CAAC,EAAE;UACtB,IAAIW,SAAS,GAAGX,KAAK,CAACX,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAClC;UACA,IAAId,GAAG,CAACU,SAAS,EAAE0B,SAAS,CAAC,EAAE,CAAC,KAC3B,IAAIb,KAAK,CAACJ,OAAO,CAACiB,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;YACtC;YACA;YACA;YACA,IAAIC,mBAAmB,GAAGd,KAAK,CAACJ,OAAO,CAACiB,SAAS,CAAC;YAClD;YACA,IAAIE,SAAS,GAAGf,KAAK,CAACT,KAAK,CAACuB,mBAAmB,CAAC;YAChD;YACAC,SAAS,CAACC,OAAO,CAAC,UAAUtB,GAAG,EAAE;cAC7BP,SAAS,CAACO,GAAG,CAAC,GAAG,IAAI;YACzB,CAAC,CAAC;YACF;YACAqB,SAAS,CAAClB,IAAI,CAACgB,SAAS,CAAC;UAC7B,CAAC,MACI;YACDX,KAAK,GAAGJ,YAAY,CAAC;cAAEC,KAAK,EAAEG,KAAK;cAAEF,KAAK;cAAEb,SAAS;cAAEK;YAAQ,CAAC,CAAC;UACrE;QACJ;QACA;QACA,IAAI,OAAOW,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACc,QAAQ,CAAC,CAAC,KAAKf,KAAK,EAAE;UACrDA,KAAK,GAAGC,GAAG;QACf;MACJ,CAAC,MACI;QACD;QACAD,KAAK,GAAGC,GAAG;MACf;IACJ,CAAC,MACI;MACDD,KAAK,GAAGC,GAAG;IACf;IACAH,KAAK,CAACC,GAAG,CAACO,QAAQ,CAAC;IACnB,OAAON,KAAK;EAChB,CAAC,CAAC;EACF,OAAOA,KAAK;AAChB;AAEA,SAASJ,YAAY,EAAEb,aAAa,EAAEK,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}