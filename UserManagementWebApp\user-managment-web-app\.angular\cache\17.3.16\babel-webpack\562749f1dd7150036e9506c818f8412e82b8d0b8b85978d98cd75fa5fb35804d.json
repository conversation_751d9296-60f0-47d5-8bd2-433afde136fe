{"ast": null, "code": "const jaDict = {\n  'Account recovery requires verified contact information': 'アカウントの復旧には確認済みの連絡先が必要です',\n  'Authenticator App (TOTP)': '認証アプリ (TOTP)',\n  'Back to Sign In': 'サインインに戻る',\n  'Change Password': 'パスワードを変える ',\n  Changing: '変更中',\n  Code: 'コード',\n  'Confirm Email Code': 'E メールコードを確認',\n  'Confirm Password': 'パスワードの確認',\n  'Confirm Sign Up': '登録する',\n  'Confirm SMS Code': 'SMS コードを確認',\n  'Confirm TOTP Code': 'TOTP コードを確認',\n  Confirm: '確定',\n  'Confirmation Code': '確認コード',\n  Confirming: '確認中',\n  'Create a new account': '新しいアカウントを作る',\n  'Create Account': 'アカウントを作る',\n  'Creating Account': 'アカウントの作成中',\n  'Dismiss alert': 'アラートを閉じる',\n  Email: 'メールアドレス',\n  'Email Message': 'E メールメッセージ',\n  'Enter your code': 'コードを入力',\n  'Enter your Email': 'メールアドレスを入力',\n  'Enter your phone number': '電話番号を入力',\n  'Enter your username': 'ユーザー名を入力 ',\n  'Enter your Username': 'ユーザー名を入力 ',\n  'Forgot your password?': 'パスワードを忘れましたか？ ',\n  'Hide password': 'パスワードを非表示',\n  'It may take a minute to arrive': '到着するまでに 1 分かかることがあります',\n  Loading: 'ロード中',\n  'Multi-Factor Authentication': '多要素認証',\n  'Multi-Factor Authentication Setup': '多要素認証のセットアップ',\n  'New password': '新しいパスワード',\n  or: '又は',\n  Password: 'パスワード ',\n  'Phone Number': '電話番号',\n  'Resend Code': 'コードを再送信',\n  'Reset your Password': 'パスワードをリセット',\n  'Reset your password': 'パスワードをリセットする',\n  'Select MFA Type': 'MFA タイプを選択',\n  'Send code': 'コードを送信',\n  'Send Code': 'コードを送信',\n  Sending: '送信中',\n  'Setup Email': 'E メールをセットアップ',\n  'Setup TOTP': 'TOTP をセットアップ',\n  'Show password': 'パスワードを表示',\n  'Sign in to your account': 'アカウントにサインイン ',\n  'Sign In with Amazon': 'Amazonでサインイン',\n  'Sign In with Apple': 'Apple でサインイン',\n  'Sign In with Facebook': 'Facebookでサインイン',\n  'Sign In with Google': 'Googleでサインイン',\n  'Sign In': 'サインイン ',\n  'Sign in': 'サインイン',\n  'Signing in': 'サインイン中',\n  Skip: 'スキップ',\n  Submit: '送信',\n  Submitting: '送信中',\n  'Text Message (SMS)': 'テキストメッセージ (SMS)',\n  Username: 'ユーザー名 ',\n  'Verify Contact': '連絡先を確認',\n  Verify: '確認',\n  'We Sent A Code': 'コードが送信されました',\n  'We Texted You': 'テキストが送信されました',\n  'Your code is on the way. To log in, enter the code we sent you': 'コードが途中です。ログインするには、送信したコードを入力してください',\n  // Additional translations provided by customers\n  'An account with the given email already exists.': '入力されたメールアドレスのアカウントが既に存在します',\n  'Confirm a Code': 'コードを確認',\n  'Confirm Sign In': 'サインインする',\n  'Create account': 'アカウントを作る ',\n  'Enter your password': 'パスワードを入力 ',\n  'Enter your Password': 'パスワードを入力',\n  'Please confirm your Password': 'パスワードを入力',\n  'Forgot Password': 'パスワードを忘れた ',\n  'Have an account? ': 'アカウントを持っていますか？',\n  'Incorrect username or password': 'ユーザー名かパスワードが異なります ',\n  'Invalid password format': 'パスワードの形式が無効です ',\n  'Invalid phone number format': '不正な電話番号の形式です。\\n+*********** の形式で入力してください',\n  'It may take a minute to arrive.': 'コードを受信するまで数分かかる場合があります。',\n  'Lost your code? ': 'コードを失くしましたか？',\n  'New Password': '新しいパスワード',\n  'No account? ': 'アカウントが無いとき ',\n  'Password attempts exceeded': 'サインインの試行回数が上限に達しました',\n  'Reset password': 'パスワードをリセット ',\n  'Reset Password': 'パスワードをリセット',\n  'Sign Out': 'サインアウト ',\n  'Sign Up': '登録 ',\n  'User already exists': '既にユーザーが存在しています ',\n  'User does not exist': 'ユーザーが存在しません ',\n  'Username cannot be empty': 'ユーザー名は入力必須です',\n  'We Emailed You': 'コードを送信しました',\n  'Your code is on the way. To log in, enter the code we emailed to': 'ログインするには、メールに記載されたコードを入力してください。送信先:',\n  'Your code is on the way. To log in, enter the code we texted to': 'ログインするには、テキストメッセージに記載されたコードを入力してください。送信先:'\n};\nexport { jaDict };", "map": {"version": 3, "names": ["jaDict", "Changing", "Code", "Confirm", "Confirming", "Email", "Loading", "or", "Password", "Sending", "<PERSON><PERSON>", "Submit", "Submitting", "Username", "Verify"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/i18n/dictionaries/authenticator/ja.mjs"], "sourcesContent": ["const jaDict = {\n    'Account recovery requires verified contact information': 'アカウントの復旧には確認済みの連絡先が必要です',\n    'Authenticator App (TOTP)': '認証アプリ (TOTP)',\n    'Back to Sign In': 'サインインに戻る',\n    'Change Password': 'パスワードを変える ',\n    Changing: '変更中',\n    Code: 'コード',\n    'Confirm Email Code': 'E メールコードを確認',\n    'Confirm Password': 'パスワードの確認',\n    'Confirm Sign Up': '登録する',\n    'Confirm SMS Code': 'SMS コードを確認',\n    'Confirm TOTP Code': 'TOTP コードを確認',\n    Confirm: '確定',\n    'Confirmation Code': '確認コード',\n    Confirming: '確認中',\n    'Create a new account': '新しいアカウントを作る',\n    'Create Account': 'アカウントを作る',\n    'Creating Account': 'アカウントの作成中',\n    'Dismiss alert': 'アラートを閉じる',\n    Email: 'メールアドレス',\n    'Email Message': 'E メールメッセージ',\n    'Enter your code': 'コードを入力',\n    'Enter your Email': 'メールアドレスを入力',\n    'Enter your phone number': '電話番号を入力',\n    'Enter your username': 'ユーザー名を入力 ',\n    'Enter your Username': 'ユーザー名を入力 ',\n    'Forgot your password?': 'パスワードを忘れましたか？ ',\n    'Hide password': 'パスワードを非表示',\n    'It may take a minute to arrive': '到着するまでに 1 分かかることがあります',\n    Loading: 'ロード中',\n    'Multi-Factor Authentication': '多要素認証',\n    'Multi-Factor Authentication Setup': '多要素認証のセットアップ',\n    'New password': '新しいパスワード',\n    or: '又は',\n    Password: 'パスワード ',\n    'Phone Number': '電話番号',\n    'Resend Code': 'コードを再送信',\n    'Reset your Password': 'パスワードをリセット',\n    'Reset your password': 'パスワードをリセットする',\n    'Select MFA Type': 'MFA タイプを選択',\n    'Send code': 'コードを送信',\n    'Send Code': 'コードを送信',\n    Sending: '送信中',\n    'Setup Email': 'E メールをセットアップ',\n    'Setup TOTP': 'TOTP をセットアップ',\n    'Show password': 'パスワードを表示',\n    'Sign in to your account': 'アカウントにサインイン ',\n    'Sign In with Amazon': 'Amazonでサインイン',\n    'Sign In with Apple': 'Apple でサインイン',\n    'Sign In with Facebook': 'Facebookでサインイン',\n    'Sign In with Google': 'Googleでサインイン',\n    'Sign In': 'サインイン ',\n    'Sign in': 'サインイン',\n    'Signing in': 'サインイン中',\n    Skip: 'スキップ',\n    Submit: '送信',\n    Submitting: '送信中',\n    'Text Message (SMS)': 'テキストメッセージ (SMS)',\n    Username: 'ユーザー名 ',\n    'Verify Contact': '連絡先を確認',\n    Verify: '確認',\n    'We Sent A Code': 'コードが送信されました',\n    'We Texted You': 'テキストが送信されました',\n    'Your code is on the way. To log in, enter the code we sent you': 'コードが途中です。ログインするには、送信したコードを入力してください',\n    // Additional translations provided by customers\n    'An account with the given email already exists.': '入力されたメールアドレスのアカウントが既に存在します',\n    'Confirm a Code': 'コードを確認',\n    'Confirm Sign In': 'サインインする',\n    'Create account': 'アカウントを作る ',\n    'Enter your password': 'パスワードを入力 ',\n    'Enter your Password': 'パスワードを入力',\n    'Please confirm your Password': 'パスワードを入力',\n    'Forgot Password': 'パスワードを忘れた ',\n    'Have an account? ': 'アカウントを持っていますか？',\n    'Incorrect username or password': 'ユーザー名かパスワードが異なります ',\n    'Invalid password format': 'パスワードの形式が無効です ',\n    'Invalid phone number format': '不正な電話番号の形式です。\\n+*********** の形式で入力してください',\n    'It may take a minute to arrive.': 'コードを受信するまで数分かかる場合があります。',\n    'Lost your code? ': 'コードを失くしましたか？',\n    'New Password': '新しいパスワード',\n    'No account? ': 'アカウントが無いとき ',\n    'Password attempts exceeded': 'サインインの試行回数が上限に達しました',\n    'Reset password': 'パスワードをリセット ',\n    'Reset Password': 'パスワードをリセット',\n    'Sign Out': 'サインアウト ',\n    'Sign Up': '登録 ',\n    'User already exists': '既にユーザーが存在しています ',\n    'User does not exist': 'ユーザーが存在しません ',\n    'Username cannot be empty': 'ユーザー名は入力必須です',\n    'We Emailed You': 'コードを送信しました',\n    'Your code is on the way. To log in, enter the code we emailed to': 'ログインするには、メールに記載されたコードを入力してください。送信先:',\n    'Your code is on the way. To log in, enter the code we texted to': 'ログインするには、テキストメッセージに記載されたコードを入力してください。送信先:',\n};\n\nexport { jaDict };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACX,wDAAwD,EAAE,yBAAyB;EACnF,0BAA0B,EAAE,cAAc;EAC1C,iBAAiB,EAAE,UAAU;EAC7B,iBAAiB,EAAE,YAAY;EAC/BC,QAAQ,EAAE,KAAK;EACfC,IAAI,EAAE,KAAK;EACX,oBAAoB,EAAE,aAAa;EACnC,kBAAkB,EAAE,UAAU;EAC9B,iBAAiB,EAAE,MAAM;EACzB,kBAAkB,EAAE,YAAY;EAChC,mBAAmB,EAAE,aAAa;EAClCC,OAAO,EAAE,IAAI;EACb,mBAAmB,EAAE,OAAO;EAC5BC,UAAU,EAAE,KAAK;EACjB,sBAAsB,EAAE,aAAa;EACrC,gBAAgB,EAAE,UAAU;EAC5B,kBAAkB,EAAE,WAAW;EAC/B,eAAe,EAAE,UAAU;EAC3BC,KAAK,EAAE,SAAS;EAChB,eAAe,EAAE,YAAY;EAC7B,iBAAiB,EAAE,QAAQ;EAC3B,kBAAkB,EAAE,YAAY;EAChC,yBAAyB,EAAE,SAAS;EACpC,qBAAqB,EAAE,WAAW;EAClC,qBAAqB,EAAE,WAAW;EAClC,uBAAuB,EAAE,gBAAgB;EACzC,eAAe,EAAE,WAAW;EAC5B,gCAAgC,EAAE,uBAAuB;EACzDC,OAAO,EAAE,MAAM;EACf,6BAA6B,EAAE,OAAO;EACtC,mCAAmC,EAAE,cAAc;EACnD,cAAc,EAAE,UAAU;EAC1BC,EAAE,EAAE,IAAI;EACRC,QAAQ,EAAE,QAAQ;EAClB,cAAc,EAAE,MAAM;EACtB,aAAa,EAAE,SAAS;EACxB,qBAAqB,EAAE,YAAY;EACnC,qBAAqB,EAAE,cAAc;EACrC,iBAAiB,EAAE,YAAY;EAC/B,WAAW,EAAE,QAAQ;EACrB,WAAW,EAAE,QAAQ;EACrBC,OAAO,EAAE,KAAK;EACd,aAAa,EAAE,cAAc;EAC7B,YAAY,EAAE,cAAc;EAC5B,eAAe,EAAE,UAAU;EAC3B,yBAAyB,EAAE,cAAc;EACzC,qBAAqB,EAAE,cAAc;EACrC,oBAAoB,EAAE,cAAc;EACpC,uBAAuB,EAAE,gBAAgB;EACzC,qBAAqB,EAAE,cAAc;EACrC,SAAS,EAAE,QAAQ;EACnB,SAAS,EAAE,OAAO;EAClB,YAAY,EAAE,QAAQ;EACtBC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjB,oBAAoB,EAAE,iBAAiB;EACvCC,QAAQ,EAAE,QAAQ;EAClB,gBAAgB,EAAE,QAAQ;EAC1BC,MAAM,EAAE,IAAI;EACZ,gBAAgB,EAAE,aAAa;EAC/B,eAAe,EAAE,cAAc;EAC/B,gEAAgE,EAAE,oCAAoC;EACtG;EACA,iDAAiD,EAAE,4BAA4B;EAC/E,gBAAgB,EAAE,QAAQ;EAC1B,iBAAiB,EAAE,SAAS;EAC5B,gBAAgB,EAAE,WAAW;EAC7B,qBAAqB,EAAE,WAAW;EAClC,qBAAqB,EAAE,UAAU;EACjC,8BAA8B,EAAE,UAAU;EAC1C,iBAAiB,EAAE,YAAY;EAC/B,mBAAmB,EAAE,gBAAgB;EACrC,gCAAgC,EAAE,oBAAoB;EACtD,yBAAyB,EAAE,gBAAgB;EAC3C,6BAA6B,EAAE,0CAA0C;EACzE,iCAAiC,EAAE,yBAAyB;EAC5D,kBAAkB,EAAE,cAAc;EAClC,cAAc,EAAE,UAAU;EAC1B,cAAc,EAAE,aAAa;EAC7B,4BAA4B,EAAE,qBAAqB;EACnD,gBAAgB,EAAE,aAAa;EAC/B,gBAAgB,EAAE,YAAY;EAC9B,UAAU,EAAE,SAAS;EACrB,SAAS,EAAE,KAAK;EAChB,qBAAqB,EAAE,iBAAiB;EACxC,qBAAqB,EAAE,cAAc;EACrC,0BAA0B,EAAE,cAAc;EAC1C,gBAAgB,EAAE,YAAY;EAC9B,kEAAkE,EAAE,qCAAqC;EACzG,iEAAiE,EAAE;AACvE,CAAC;AAED,SAASd,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}