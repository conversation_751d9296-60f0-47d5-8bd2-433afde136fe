{"ast": null, "code": "import { getBytesFromHex } from './getBytesFromHex.mjs';\nimport { getHashFromData } from './getHashFromData.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Calculate a hash from a hex string\n * @param {string} hexStr Value to hash.\n * @returns {string} Hex-encoded hash.\n * @private\n */\nconst getHashFromHex = hexStr => getHashFromData(getBytesFromHex(hexStr));\nexport { getHashFromHex };", "map": {"version": 3, "names": ["getBytesFromHex", "getHashFromData", "getHashFromHex", "hexStr"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/srp/getHashFromHex.mjs"], "sourcesContent": ["import { getBytesFromHex } from './getBytesFromHex.mjs';\nimport { getHashFromData } from './getHashFromData.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Calculate a hash from a hex string\n * @param {string} hexStr Value to hash.\n * @returns {string} Hex-encoded hash.\n * @private\n */\nconst getHashFromHex = (hexStr) => getHashFromData(getBytesFromHex(hexStr));\n\nexport { getHashFromHex };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,uBAAuB;AACvD,SAASC,eAAe,QAAQ,uBAAuB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAIC,MAAM,IAAKF,eAAe,CAACD,eAAe,CAACG,MAAM,CAAC,CAAC;AAE3E,SAASD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}