{"ast": null, "code": "const copy = {\n  fontSize: {\n    value: '{fontSizes.xs}'\n  },\n  gap: {\n    value: '{space.relative.medium}'\n  },\n  svg: {\n    path: {\n      fill: {\n        value: '{colors.font.primary}'\n      }\n    }\n  },\n  toolTip: {\n    bottom: {\n      value: '{space.large}'\n    },\n    color: {\n      value: '{colors.teal.100}'\n    },\n    fontSize: {\n      value: '{fontSizes.xxs}'\n    }\n  }\n};\nexport { copy };", "map": {"version": 3, "names": ["copy", "fontSize", "value", "gap", "svg", "path", "fill", "toolTip", "bottom", "color"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/copy.mjs"], "sourcesContent": ["const copy = {\n    fontSize: { value: '{fontSizes.xs}' },\n    gap: { value: '{space.relative.medium}' },\n    svg: {\n        path: {\n            fill: {\n                value: '{colors.font.primary}',\n            },\n        },\n    },\n    toolTip: {\n        bottom: { value: '{space.large}' },\n        color: { value: '{colors.teal.100}' },\n        fontSize: { value: '{fontSizes.xxs}' },\n    },\n};\n\nexport { copy };\n"], "mappings": "AAAA,MAAMA,IAAI,GAAG;EACTC,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAiB,CAAC;EACrCC,GAAG,EAAE;IAAED,KAAK,EAAE;EAA0B,CAAC;EACzCE,GAAG,EAAE;IACDC,IAAI,EAAE;MACFC,IAAI,EAAE;QACFJ,KAAK,EAAE;MACX;IACJ;EACJ,CAAC;EACDK,OAAO,EAAE;IACLC,MAAM,EAAE;MAAEN,KAAK,EAAE;IAAgB,CAAC;IAClCO,KAAK,EAAE;MAAEP,KAAK,EAAE;IAAoB,CAAC;IACrCD,QAAQ,EAAE;MAAEC,KAAK,EAAE;IAAkB;EACzC;AACJ,CAAC;AAED,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}