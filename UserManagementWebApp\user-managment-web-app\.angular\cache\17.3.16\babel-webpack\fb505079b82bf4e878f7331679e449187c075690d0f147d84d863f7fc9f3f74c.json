{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { noCase } from \"no-case\";\nimport { upperCaseFirst } from \"upper-case-first\";\nexport function sentenceCaseTransform(input, index) {\n  var result = input.toLowerCase();\n  if (index === 0) return upperCaseFirst(result);\n  return result;\n}\nexport function sentenceCase(input, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return noCase(input, __assign({\n    delimiter: \" \",\n    transform: sentenceCaseTransform\n  }, options));\n}", "map": {"version": 3, "names": ["__assign", "noCase", "upperCaseFirst", "sentenceCaseTransform", "input", "index", "result", "toLowerCase", "sentenceCase", "options", "delimiter", "transform"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/sentence-case/dist.es2015/index.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { noCase } from \"no-case\";\nimport { upperCaseFirst } from \"upper-case-first\";\nexport function sentenceCaseTransform(input, index) {\n    var result = input.toLowerCase();\n    if (index === 0)\n        return upperCaseFirst(result);\n    return result;\n}\nexport function sentenceCase(input, options) {\n    if (options === void 0) { options = {}; }\n    return noCase(input, __assign({ delimiter: \" \", transform: sentenceCaseTransform }, options));\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,MAAM,QAAQ,SAAS;AAChC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAO,SAASC,qBAAqBA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAChD,IAAIC,MAAM,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;EAChC,IAAIF,KAAK,KAAK,CAAC,EACX,OAAOH,cAAc,CAACI,MAAM,CAAC;EACjC,OAAOA,MAAM;AACjB;AACA,OAAO,SAASE,YAAYA,CAACJ,KAAK,EAAEK,OAAO,EAAE;EACzC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC,CAAC;EAAE;EACxC,OAAOR,MAAM,CAACG,KAAK,EAAEJ,QAAQ,CAAC;IAAEU,SAAS,EAAE,GAAG;IAAEC,SAAS,EAAER;EAAsB,CAAC,EAAEM,OAAO,CAAC,CAAC;AACjG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}