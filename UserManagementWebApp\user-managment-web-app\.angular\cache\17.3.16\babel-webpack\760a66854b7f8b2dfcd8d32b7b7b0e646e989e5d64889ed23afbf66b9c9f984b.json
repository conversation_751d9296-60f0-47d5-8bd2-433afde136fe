{"ast": null, "code": "// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function (mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\")\n    // CommonJS\n    mod(require(\"../../lib/codemirror\"));else if (typeof define == \"function\" && define.amd)\n    // AMD\n    define([\"../../lib/codemirror\"], mod);else\n    // Plain browser env\n    mod(CodeMirror);\n})(function (CodeMirror) {\n  \"use strict\";\n\n  var GUTTER_ID = \"CodeMirror-lint-markers\";\n  var LINT_LINE_ID = \"CodeMirror-lint-line-\";\n  function showTooltip(cm, e, content) {\n    var tt = document.createElement(\"div\");\n    tt.className = \"CodeMirror-lint-tooltip cm-s-\" + cm.options.theme;\n    tt.appendChild(content.cloneNode(true));\n    if (cm.state.lint.options.selfContain) cm.getWrapperElement().appendChild(tt);else document.body.appendChild(tt);\n    function position(e) {\n      if (!tt.parentNode) return CodeMirror.off(document, \"mousemove\", position);\n      var top = Math.max(0, e.clientY - tt.offsetHeight - 5);\n      var left = Math.max(0, Math.min(e.clientX + 5, tt.ownerDocument.defaultView.innerWidth - tt.offsetWidth));\n      tt.style.top = top + \"px\";\n      tt.style.left = left + \"px\";\n    }\n    CodeMirror.on(document, \"mousemove\", position);\n    position(e);\n    if (tt.style.opacity != null) tt.style.opacity = 1;\n    return tt;\n  }\n  function rm(elt) {\n    if (elt.parentNode) elt.parentNode.removeChild(elt);\n  }\n  function hideTooltip(tt) {\n    if (!tt.parentNode) return;\n    if (tt.style.opacity == null) rm(tt);\n    tt.style.opacity = 0;\n    setTimeout(function () {\n      rm(tt);\n    }, 600);\n  }\n  function showTooltipFor(cm, e, content, node) {\n    var tooltip = showTooltip(cm, e, content);\n    function hide() {\n      CodeMirror.off(node, \"mouseout\", hide);\n      if (tooltip) {\n        hideTooltip(tooltip);\n        tooltip = null;\n      }\n    }\n    var poll = setInterval(function () {\n      if (tooltip) for (var n = node;; n = n.parentNode) {\n        if (n && n.nodeType == 11) n = n.host;\n        if (n == document.body) return;\n        if (!n) {\n          hide();\n          break;\n        }\n      }\n      if (!tooltip) return clearInterval(poll);\n    }, 400);\n    CodeMirror.on(node, \"mouseout\", hide);\n  }\n  function LintState(cm, conf, hasGutter) {\n    this.marked = [];\n    if (conf instanceof Function) conf = {\n      getAnnotations: conf\n    };\n    if (!conf || conf === true) conf = {};\n    this.options = {};\n    this.linterOptions = conf.options || {};\n    for (var prop in defaults) this.options[prop] = defaults[prop];\n    for (var prop in conf) {\n      if (defaults.hasOwnProperty(prop)) {\n        if (conf[prop] != null) this.options[prop] = conf[prop];\n      } else if (!conf.options) {\n        this.linterOptions[prop] = conf[prop];\n      }\n    }\n    this.timeout = null;\n    this.hasGutter = hasGutter;\n    this.onMouseOver = function (e) {\n      onMouseOver(cm, e);\n    };\n    this.waitingFor = 0;\n  }\n  var defaults = {\n    highlightLines: false,\n    tooltips: true,\n    delay: 500,\n    lintOnChange: true,\n    getAnnotations: null,\n    async: false,\n    selfContain: null,\n    formatAnnotation: null,\n    onUpdateLinting: null\n  };\n  function clearMarks(cm) {\n    var state = cm.state.lint;\n    if (state.hasGutter) cm.clearGutter(GUTTER_ID);\n    if (state.options.highlightLines) clearErrorLines(cm);\n    for (var i = 0; i < state.marked.length; ++i) state.marked[i].clear();\n    state.marked.length = 0;\n  }\n  function clearErrorLines(cm) {\n    cm.eachLine(function (line) {\n      var has = line.wrapClass && /\\bCodeMirror-lint-line-\\w+\\b/.exec(line.wrapClass);\n      if (has) cm.removeLineClass(line, \"wrap\", has[0]);\n    });\n  }\n  function makeMarker(cm, labels, severity, multiple, tooltips) {\n    var marker = document.createElement(\"div\"),\n      inner = marker;\n    marker.className = \"CodeMirror-lint-marker CodeMirror-lint-marker-\" + severity;\n    if (multiple) {\n      inner = marker.appendChild(document.createElement(\"div\"));\n      inner.className = \"CodeMirror-lint-marker CodeMirror-lint-marker-multiple\";\n    }\n    if (tooltips != false) CodeMirror.on(inner, \"mouseover\", function (e) {\n      showTooltipFor(cm, e, labels, inner);\n    });\n    return marker;\n  }\n  function getMaxSeverity(a, b) {\n    if (a == \"error\") return a;else return b;\n  }\n  function groupByLine(annotations) {\n    var lines = [];\n    for (var i = 0; i < annotations.length; ++i) {\n      var ann = annotations[i],\n        line = ann.from.line;\n      (lines[line] || (lines[line] = [])).push(ann);\n    }\n    return lines;\n  }\n  function annotationTooltip(ann) {\n    var severity = ann.severity;\n    if (!severity) severity = \"error\";\n    var tip = document.createElement(\"div\");\n    tip.className = \"CodeMirror-lint-message CodeMirror-lint-message-\" + severity;\n    if (typeof ann.messageHTML != 'undefined') {\n      tip.innerHTML = ann.messageHTML;\n    } else {\n      tip.appendChild(document.createTextNode(ann.message));\n    }\n    return tip;\n  }\n  function lintAsync(cm, getAnnotations) {\n    var state = cm.state.lint;\n    var id = ++state.waitingFor;\n    function abort() {\n      id = -1;\n      cm.off(\"change\", abort);\n    }\n    cm.on(\"change\", abort);\n    getAnnotations(cm.getValue(), function (annotations, arg2) {\n      cm.off(\"change\", abort);\n      if (state.waitingFor != id) return;\n      if (arg2 && annotations instanceof CodeMirror) annotations = arg2;\n      cm.operation(function () {\n        updateLinting(cm, annotations);\n      });\n    }, state.linterOptions, cm);\n  }\n  function startLinting(cm) {\n    var state = cm.state.lint;\n    if (!state) return;\n    var options = state.options;\n    /*\n     * Passing rules in `options` property prevents JSHint (and other linters) from complaining\n     * about unrecognized rules like `onUpdateLinting`, `delay`, `lintOnChange`, etc.\n     */\n    var getAnnotations = options.getAnnotations || cm.getHelper(CodeMirror.Pos(0, 0), \"lint\");\n    if (!getAnnotations) return;\n    if (options.async || getAnnotations.async) {\n      lintAsync(cm, getAnnotations);\n    } else {\n      var annotations = getAnnotations(cm.getValue(), state.linterOptions, cm);\n      if (!annotations) return;\n      if (annotations.then) annotations.then(function (issues) {\n        cm.operation(function () {\n          updateLinting(cm, issues);\n        });\n      });else cm.operation(function () {\n        updateLinting(cm, annotations);\n      });\n    }\n  }\n  function updateLinting(cm, annotationsNotSorted) {\n    var state = cm.state.lint;\n    if (!state) return;\n    var options = state.options;\n    clearMarks(cm);\n    var annotations = groupByLine(annotationsNotSorted);\n    for (var line = 0; line < annotations.length; ++line) {\n      var anns = annotations[line];\n      if (!anns) continue;\n      var maxSeverity = null;\n      var tipLabel = state.hasGutter && document.createDocumentFragment();\n      for (var i = 0; i < anns.length; ++i) {\n        var ann = anns[i];\n        var severity = ann.severity;\n        if (!severity) severity = \"error\";\n        maxSeverity = getMaxSeverity(maxSeverity, severity);\n        if (options.formatAnnotation) ann = options.formatAnnotation(ann);\n        if (state.hasGutter) tipLabel.appendChild(annotationTooltip(ann));\n        if (ann.to) state.marked.push(cm.markText(ann.from, ann.to, {\n          className: \"CodeMirror-lint-mark CodeMirror-lint-mark-\" + severity,\n          __annotation: ann\n        }));\n      }\n      if (state.hasGutter) cm.setGutterMarker(line, GUTTER_ID, makeMarker(cm, tipLabel, maxSeverity, anns.length > 1, options.tooltips));\n      if (options.highlightLines) cm.addLineClass(line, \"wrap\", LINT_LINE_ID + maxSeverity);\n    }\n    if (options.onUpdateLinting) options.onUpdateLinting(annotationsNotSorted, annotations, cm);\n  }\n  function onChange(cm) {\n    var state = cm.state.lint;\n    if (!state) return;\n    clearTimeout(state.timeout);\n    state.timeout = setTimeout(function () {\n      startLinting(cm);\n    }, state.options.delay);\n  }\n  function popupTooltips(cm, annotations, e) {\n    var target = e.target || e.srcElement;\n    var tooltip = document.createDocumentFragment();\n    for (var i = 0; i < annotations.length; i++) {\n      var ann = annotations[i];\n      tooltip.appendChild(annotationTooltip(ann));\n    }\n    showTooltipFor(cm, e, tooltip, target);\n  }\n  function onMouseOver(cm, e) {\n    var target = e.target || e.srcElement;\n    if (!/\\bCodeMirror-lint-mark-/.test(target.className)) return;\n    var box = target.getBoundingClientRect(),\n      x = (box.left + box.right) / 2,\n      y = (box.top + box.bottom) / 2;\n    var spans = cm.findMarksAt(cm.coordsChar({\n      left: x,\n      top: y\n    }, \"client\"));\n    var annotations = [];\n    for (var i = 0; i < spans.length; ++i) {\n      var ann = spans[i].__annotation;\n      if (ann) annotations.push(ann);\n    }\n    if (annotations.length) popupTooltips(cm, annotations, e);\n  }\n  CodeMirror.defineOption(\"lint\", false, function (cm, val, old) {\n    if (old && old != CodeMirror.Init) {\n      clearMarks(cm);\n      if (cm.state.lint.options.lintOnChange !== false) cm.off(\"change\", onChange);\n      CodeMirror.off(cm.getWrapperElement(), \"mouseover\", cm.state.lint.onMouseOver);\n      clearTimeout(cm.state.lint.timeout);\n      delete cm.state.lint;\n    }\n    if (val) {\n      var gutters = cm.getOption(\"gutters\"),\n        hasLintGutter = false;\n      for (var i = 0; i < gutters.length; ++i) if (gutters[i] == GUTTER_ID) hasLintGutter = true;\n      var state = cm.state.lint = new LintState(cm, val, hasLintGutter);\n      if (state.options.lintOnChange) cm.on(\"change\", onChange);\n      if (state.options.tooltips != false && state.options.tooltips != \"gutter\") CodeMirror.on(cm.getWrapperElement(), \"mouseover\", state.onMouseOver);\n      startLinting(cm);\n    }\n  });\n  CodeMirror.defineExtension(\"performLint\", function () {\n    startLinting(this);\n  });\n});", "map": {"version": 3, "names": ["mod", "exports", "module", "require", "define", "amd", "CodeMirror", "GUTTER_ID", "LINT_LINE_ID", "showTooltip", "cm", "e", "content", "tt", "document", "createElement", "className", "options", "theme", "append<PERSON><PERSON><PERSON>", "cloneNode", "state", "lint", "selfContain", "getWrapperElement", "body", "position", "parentNode", "off", "top", "Math", "max", "clientY", "offsetHeight", "left", "min", "clientX", "ownerDocument", "defaultView", "innerWidth", "offsetWidth", "style", "on", "opacity", "rm", "elt", "<PERSON><PERSON><PERSON><PERSON>", "hideTooltip", "setTimeout", "showTooltipFor", "node", "tooltip", "hide", "poll", "setInterval", "n", "nodeType", "host", "clearInterval", "LintState", "conf", "<PERSON><PERSON><PERSON>", "marked", "Function", "getAnnotations", "linterOptions", "prop", "defaults", "hasOwnProperty", "timeout", "onMouseOver", "waitingFor", "highlightLines", "tooltips", "delay", "lintOnChange", "async", "formatAnnotation", "onUpdateLinting", "clearMarks", "clear<PERSON>utter", "clearErrorLines", "i", "length", "clear", "eachLine", "line", "has", "wrapClass", "exec", "removeLineClass", "make<PERSON><PERSON><PERSON>", "labels", "severity", "multiple", "marker", "inner", "getMaxSeverity", "a", "b", "groupByLine", "annotations", "lines", "ann", "from", "push", "annotationTooltip", "tip", "messageHTML", "innerHTML", "createTextNode", "message", "lintAsync", "id", "abort", "getValue", "arg2", "operation", "updateLinting", "startLinting", "getHelper", "Pos", "then", "issues", "annotationsNotSorted", "anns", "maxSeverity", "tipLabel", "createDocumentFragment", "to", "markText", "__annotation", "set<PERSON><PERSON>Marker", "addLineClass", "onChange", "clearTimeout", "popupTooltips", "target", "srcElement", "test", "box", "getBoundingClientRect", "x", "right", "y", "bottom", "spans", "findMarksAt", "coordsChar", "defineOption", "val", "old", "Init", "gutters", "getOption", "has<PERSON>int<PERSON>utter", "defineExtension"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/codemirror/addon/lint/lint.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n  var GUTTER_ID = \"CodeMirror-lint-markers\";\n  var LINT_LINE_ID = \"CodeMirror-lint-line-\";\n\n  function showTooltip(cm, e, content) {\n    var tt = document.createElement(\"div\");\n    tt.className = \"CodeMirror-lint-tooltip cm-s-\" + cm.options.theme;\n    tt.appendChild(content.cloneNode(true));\n    if (cm.state.lint.options.selfContain)\n      cm.getWrapperElement().appendChild(tt);\n    else\n      document.body.appendChild(tt);\n\n    function position(e) {\n      if (!tt.parentNode) return CodeMirror.off(document, \"mousemove\", position);\n      var top = Math.max(0, e.clientY - tt.offsetHeight - 5);\n      var left = Math.max(0, Math.min(e.clientX + 5, tt.ownerDocument.defaultView.innerWidth - tt.offsetWidth));\n      tt.style.top = top + \"px\"\n      tt.style.left = left + \"px\";\n    }\n    CodeMirror.on(document, \"mousemove\", position);\n    position(e);\n    if (tt.style.opacity != null) tt.style.opacity = 1;\n    return tt;\n  }\n  function rm(elt) {\n    if (elt.parentNode) elt.parentNode.removeChild(elt);\n  }\n  function hideTooltip(tt) {\n    if (!tt.parentNode) return;\n    if (tt.style.opacity == null) rm(tt);\n    tt.style.opacity = 0;\n    setTimeout(function() { rm(tt); }, 600);\n  }\n\n  function showTooltipFor(cm, e, content, node) {\n    var tooltip = showTooltip(cm, e, content);\n    function hide() {\n      CodeMirror.off(node, \"mouseout\", hide);\n      if (tooltip) { hideTooltip(tooltip); tooltip = null; }\n    }\n    var poll = setInterval(function() {\n      if (tooltip) for (var n = node;; n = n.parentNode) {\n        if (n && n.nodeType == 11) n = n.host;\n        if (n == document.body) return;\n        if (!n) { hide(); break; }\n      }\n      if (!tooltip) return clearInterval(poll);\n    }, 400);\n    CodeMirror.on(node, \"mouseout\", hide);\n  }\n\n  function LintState(cm, conf, hasGutter) {\n    this.marked = [];\n    if (conf instanceof Function) conf = {getAnnotations: conf};\n    if (!conf || conf === true) conf = {};\n    this.options = {};\n    this.linterOptions = conf.options || {};\n    for (var prop in defaults) this.options[prop] = defaults[prop];\n    for (var prop in conf) {\n      if (defaults.hasOwnProperty(prop)) {\n        if (conf[prop] != null) this.options[prop] = conf[prop];\n      } else if (!conf.options) {\n        this.linterOptions[prop] = conf[prop];\n      }\n    }\n    this.timeout = null;\n    this.hasGutter = hasGutter;\n    this.onMouseOver = function(e) { onMouseOver(cm, e); };\n    this.waitingFor = 0\n  }\n\n  var defaults = {\n    highlightLines: false,\n    tooltips: true,\n    delay: 500,\n    lintOnChange: true,\n    getAnnotations: null,\n    async: false,\n    selfContain: null,\n    formatAnnotation: null,\n    onUpdateLinting: null\n  }\n\n  function clearMarks(cm) {\n    var state = cm.state.lint;\n    if (state.hasGutter) cm.clearGutter(GUTTER_ID);\n    if (state.options.highlightLines) clearErrorLines(cm);\n    for (var i = 0; i < state.marked.length; ++i)\n      state.marked[i].clear();\n    state.marked.length = 0;\n  }\n\n  function clearErrorLines(cm) {\n    cm.eachLine(function(line) {\n      var has = line.wrapClass && /\\bCodeMirror-lint-line-\\w+\\b/.exec(line.wrapClass);\n      if (has) cm.removeLineClass(line, \"wrap\", has[0]);\n    })\n  }\n\n  function makeMarker(cm, labels, severity, multiple, tooltips) {\n    var marker = document.createElement(\"div\"), inner = marker;\n    marker.className = \"CodeMirror-lint-marker CodeMirror-lint-marker-\" + severity;\n    if (multiple) {\n      inner = marker.appendChild(document.createElement(\"div\"));\n      inner.className = \"CodeMirror-lint-marker CodeMirror-lint-marker-multiple\";\n    }\n\n    if (tooltips != false) CodeMirror.on(inner, \"mouseover\", function(e) {\n      showTooltipFor(cm, e, labels, inner);\n    });\n\n    return marker;\n  }\n\n  function getMaxSeverity(a, b) {\n    if (a == \"error\") return a;\n    else return b;\n  }\n\n  function groupByLine(annotations) {\n    var lines = [];\n    for (var i = 0; i < annotations.length; ++i) {\n      var ann = annotations[i], line = ann.from.line;\n      (lines[line] || (lines[line] = [])).push(ann);\n    }\n    return lines;\n  }\n\n  function annotationTooltip(ann) {\n    var severity = ann.severity;\n    if (!severity) severity = \"error\";\n    var tip = document.createElement(\"div\");\n    tip.className = \"CodeMirror-lint-message CodeMirror-lint-message-\" + severity;\n    if (typeof ann.messageHTML != 'undefined') {\n      tip.innerHTML = ann.messageHTML;\n    } else {\n      tip.appendChild(document.createTextNode(ann.message));\n    }\n    return tip;\n  }\n\n  function lintAsync(cm, getAnnotations) {\n    var state = cm.state.lint\n    var id = ++state.waitingFor\n    function abort() {\n      id = -1\n      cm.off(\"change\", abort)\n    }\n    cm.on(\"change\", abort)\n    getAnnotations(cm.getValue(), function(annotations, arg2) {\n      cm.off(\"change\", abort)\n      if (state.waitingFor != id) return\n      if (arg2 && annotations instanceof CodeMirror) annotations = arg2\n      cm.operation(function() {updateLinting(cm, annotations)})\n    }, state.linterOptions, cm);\n  }\n\n  function startLinting(cm) {\n    var state = cm.state.lint;\n    if (!state) return;\n    var options = state.options;\n    /*\n     * Passing rules in `options` property prevents JSHint (and other linters) from complaining\n     * about unrecognized rules like `onUpdateLinting`, `delay`, `lintOnChange`, etc.\n     */\n    var getAnnotations = options.getAnnotations || cm.getHelper(CodeMirror.Pos(0, 0), \"lint\");\n    if (!getAnnotations) return;\n    if (options.async || getAnnotations.async) {\n      lintAsync(cm, getAnnotations)\n    } else {\n      var annotations = getAnnotations(cm.getValue(), state.linterOptions, cm);\n      if (!annotations) return;\n      if (annotations.then) annotations.then(function(issues) {\n        cm.operation(function() {updateLinting(cm, issues)})\n      });\n      else cm.operation(function() {updateLinting(cm, annotations)})\n    }\n  }\n\n  function updateLinting(cm, annotationsNotSorted) {\n    var state = cm.state.lint;\n    if (!state) return;\n    var options = state.options;\n    clearMarks(cm);\n\n    var annotations = groupByLine(annotationsNotSorted);\n\n    for (var line = 0; line < annotations.length; ++line) {\n      var anns = annotations[line];\n      if (!anns) continue;\n\n      var maxSeverity = null;\n      var tipLabel = state.hasGutter && document.createDocumentFragment();\n\n      for (var i = 0; i < anns.length; ++i) {\n        var ann = anns[i];\n        var severity = ann.severity;\n        if (!severity) severity = \"error\";\n        maxSeverity = getMaxSeverity(maxSeverity, severity);\n\n        if (options.formatAnnotation) ann = options.formatAnnotation(ann);\n        if (state.hasGutter) tipLabel.appendChild(annotationTooltip(ann));\n\n        if (ann.to) state.marked.push(cm.markText(ann.from, ann.to, {\n          className: \"CodeMirror-lint-mark CodeMirror-lint-mark-\" + severity,\n          __annotation: ann\n        }));\n      }\n      if (state.hasGutter)\n        cm.setGutterMarker(line, GUTTER_ID, makeMarker(cm, tipLabel, maxSeverity, anns.length > 1,\n                                                       options.tooltips));\n\n      if (options.highlightLines)\n        cm.addLineClass(line, \"wrap\", LINT_LINE_ID + maxSeverity);\n    }\n    if (options.onUpdateLinting) options.onUpdateLinting(annotationsNotSorted, annotations, cm);\n  }\n\n  function onChange(cm) {\n    var state = cm.state.lint;\n    if (!state) return;\n    clearTimeout(state.timeout);\n    state.timeout = setTimeout(function(){startLinting(cm);}, state.options.delay);\n  }\n\n  function popupTooltips(cm, annotations, e) {\n    var target = e.target || e.srcElement;\n    var tooltip = document.createDocumentFragment();\n    for (var i = 0; i < annotations.length; i++) {\n      var ann = annotations[i];\n      tooltip.appendChild(annotationTooltip(ann));\n    }\n    showTooltipFor(cm, e, tooltip, target);\n  }\n\n  function onMouseOver(cm, e) {\n    var target = e.target || e.srcElement;\n    if (!/\\bCodeMirror-lint-mark-/.test(target.className)) return;\n    var box = target.getBoundingClientRect(), x = (box.left + box.right) / 2, y = (box.top + box.bottom) / 2;\n    var spans = cm.findMarksAt(cm.coordsChar({left: x, top: y}, \"client\"));\n\n    var annotations = [];\n    for (var i = 0; i < spans.length; ++i) {\n      var ann = spans[i].__annotation;\n      if (ann) annotations.push(ann);\n    }\n    if (annotations.length) popupTooltips(cm, annotations, e);\n  }\n\n  CodeMirror.defineOption(\"lint\", false, function(cm, val, old) {\n    if (old && old != CodeMirror.Init) {\n      clearMarks(cm);\n      if (cm.state.lint.options.lintOnChange !== false)\n        cm.off(\"change\", onChange);\n      CodeMirror.off(cm.getWrapperElement(), \"mouseover\", cm.state.lint.onMouseOver);\n      clearTimeout(cm.state.lint.timeout);\n      delete cm.state.lint;\n    }\n\n    if (val) {\n      var gutters = cm.getOption(\"gutters\"), hasLintGutter = false;\n      for (var i = 0; i < gutters.length; ++i) if (gutters[i] == GUTTER_ID) hasLintGutter = true;\n      var state = cm.state.lint = new LintState(cm, val, hasLintGutter);\n      if (state.options.lintOnChange)\n        cm.on(\"change\", onChange);\n      if (state.options.tooltips != false && state.options.tooltips != \"gutter\")\n        CodeMirror.on(cm.getWrapperElement(), \"mouseover\", state.onMouseOver);\n\n      startLinting(cm);\n    }\n  });\n\n  CodeMirror.defineExtension(\"performLint\", function() {\n    startLinting(this);\n  });\n});\n"], "mappings": "AAAA;AACA;;AAEA,CAAC,UAASA,GAAG,EAAE;EACb,IAAI,OAAOC,OAAO,IAAI,QAAQ,IAAI,OAAOC,MAAM,IAAI,QAAQ;IAAE;IAC3DF,GAAG,CAACG,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC,KAClC,IAAI,OAAOC,MAAM,IAAI,UAAU,IAAIA,MAAM,CAACC,GAAG;IAAE;IAClDD,MAAM,CAAC,CAAC,sBAAsB,CAAC,EAAEJ,GAAG,CAAC,CAAC;IACnC;IACHA,GAAG,CAACM,UAAU,CAAC;AACnB,CAAC,EAAE,UAASA,UAAU,EAAE;EACtB,YAAY;;EACZ,IAAIC,SAAS,GAAG,yBAAyB;EACzC,IAAIC,YAAY,GAAG,uBAAuB;EAE1C,SAASC,WAAWA,CAACC,EAAE,EAAEC,CAAC,EAAEC,OAAO,EAAE;IACnC,IAAIC,EAAE,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACtCF,EAAE,CAACG,SAAS,GAAG,+BAA+B,GAAGN,EAAE,CAACO,OAAO,CAACC,KAAK;IACjEL,EAAE,CAACM,WAAW,CAACP,OAAO,CAACQ,SAAS,CAAC,IAAI,CAAC,CAAC;IACvC,IAAIV,EAAE,CAACW,KAAK,CAACC,IAAI,CAACL,OAAO,CAACM,WAAW,EACnCb,EAAE,CAACc,iBAAiB,CAAC,CAAC,CAACL,WAAW,CAACN,EAAE,CAAC,CAAC,KAEvCC,QAAQ,CAACW,IAAI,CAACN,WAAW,CAACN,EAAE,CAAC;IAE/B,SAASa,QAAQA,CAACf,CAAC,EAAE;MACnB,IAAI,CAACE,EAAE,CAACc,UAAU,EAAE,OAAOrB,UAAU,CAACsB,GAAG,CAACd,QAAQ,EAAE,WAAW,EAAEY,QAAQ,CAAC;MAC1E,IAAIG,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEpB,CAAC,CAACqB,OAAO,GAAGnB,EAAE,CAACoB,YAAY,GAAG,CAAC,CAAC;MACtD,IAAIC,IAAI,GAAGJ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACK,GAAG,CAACxB,CAAC,CAACyB,OAAO,GAAG,CAAC,EAAEvB,EAAE,CAACwB,aAAa,CAACC,WAAW,CAACC,UAAU,GAAG1B,EAAE,CAAC2B,WAAW,CAAC,CAAC;MACzG3B,EAAE,CAAC4B,KAAK,CAACZ,GAAG,GAAGA,GAAG,GAAG,IAAI;MACzBhB,EAAE,CAAC4B,KAAK,CAACP,IAAI,GAAGA,IAAI,GAAG,IAAI;IAC7B;IACA5B,UAAU,CAACoC,EAAE,CAAC5B,QAAQ,EAAE,WAAW,EAAEY,QAAQ,CAAC;IAC9CA,QAAQ,CAACf,CAAC,CAAC;IACX,IAAIE,EAAE,CAAC4B,KAAK,CAACE,OAAO,IAAI,IAAI,EAAE9B,EAAE,CAAC4B,KAAK,CAACE,OAAO,GAAG,CAAC;IAClD,OAAO9B,EAAE;EACX;EACA,SAAS+B,EAAEA,CAACC,GAAG,EAAE;IACf,IAAIA,GAAG,CAAClB,UAAU,EAAEkB,GAAG,CAAClB,UAAU,CAACmB,WAAW,CAACD,GAAG,CAAC;EACrD;EACA,SAASE,WAAWA,CAAClC,EAAE,EAAE;IACvB,IAAI,CAACA,EAAE,CAACc,UAAU,EAAE;IACpB,IAAId,EAAE,CAAC4B,KAAK,CAACE,OAAO,IAAI,IAAI,EAAEC,EAAE,CAAC/B,EAAE,CAAC;IACpCA,EAAE,CAAC4B,KAAK,CAACE,OAAO,GAAG,CAAC;IACpBK,UAAU,CAAC,YAAW;MAAEJ,EAAE,CAAC/B,EAAE,CAAC;IAAE,CAAC,EAAE,GAAG,CAAC;EACzC;EAEA,SAASoC,cAAcA,CAACvC,EAAE,EAAEC,CAAC,EAAEC,OAAO,EAAEsC,IAAI,EAAE;IAC5C,IAAIC,OAAO,GAAG1C,WAAW,CAACC,EAAE,EAAEC,CAAC,EAAEC,OAAO,CAAC;IACzC,SAASwC,IAAIA,CAAA,EAAG;MACd9C,UAAU,CAACsB,GAAG,CAACsB,IAAI,EAAE,UAAU,EAAEE,IAAI,CAAC;MACtC,IAAID,OAAO,EAAE;QAAEJ,WAAW,CAACI,OAAO,CAAC;QAAEA,OAAO,GAAG,IAAI;MAAE;IACvD;IACA,IAAIE,IAAI,GAAGC,WAAW,CAAC,YAAW;MAChC,IAAIH,OAAO,EAAE,KAAK,IAAII,CAAC,GAAGL,IAAI,GAAGK,CAAC,GAAGA,CAAC,CAAC5B,UAAU,EAAE;QACjD,IAAI4B,CAAC,IAAIA,CAAC,CAACC,QAAQ,IAAI,EAAE,EAAED,CAAC,GAAGA,CAAC,CAACE,IAAI;QACrC,IAAIF,CAAC,IAAIzC,QAAQ,CAACW,IAAI,EAAE;QACxB,IAAI,CAAC8B,CAAC,EAAE;UAAEH,IAAI,CAAC,CAAC;UAAE;QAAO;MAC3B;MACA,IAAI,CAACD,OAAO,EAAE,OAAOO,aAAa,CAACL,IAAI,CAAC;IAC1C,CAAC,EAAE,GAAG,CAAC;IACP/C,UAAU,CAACoC,EAAE,CAACQ,IAAI,EAAE,UAAU,EAAEE,IAAI,CAAC;EACvC;EAEA,SAASO,SAASA,CAACjD,EAAE,EAAEkD,IAAI,EAAEC,SAAS,EAAE;IACtC,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAIF,IAAI,YAAYG,QAAQ,EAAEH,IAAI,GAAG;MAACI,cAAc,EAAEJ;IAAI,CAAC;IAC3D,IAAI,CAACA,IAAI,IAAIA,IAAI,KAAK,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;IACrC,IAAI,CAAC3C,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACgD,aAAa,GAAGL,IAAI,CAAC3C,OAAO,IAAI,CAAC,CAAC;IACvC,KAAK,IAAIiD,IAAI,IAAIC,QAAQ,EAAE,IAAI,CAAClD,OAAO,CAACiD,IAAI,CAAC,GAAGC,QAAQ,CAACD,IAAI,CAAC;IAC9D,KAAK,IAAIA,IAAI,IAAIN,IAAI,EAAE;MACrB,IAAIO,QAAQ,CAACC,cAAc,CAACF,IAAI,CAAC,EAAE;QACjC,IAAIN,IAAI,CAACM,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,CAACjD,OAAO,CAACiD,IAAI,CAAC,GAAGN,IAAI,CAACM,IAAI,CAAC;MACzD,CAAC,MAAM,IAAI,CAACN,IAAI,CAAC3C,OAAO,EAAE;QACxB,IAAI,CAACgD,aAAa,CAACC,IAAI,CAAC,GAAGN,IAAI,CAACM,IAAI,CAAC;MACvC;IACF;IACA,IAAI,CAACG,OAAO,GAAG,IAAI;IACnB,IAAI,CAACR,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACS,WAAW,GAAG,UAAS3D,CAAC,EAAE;MAAE2D,WAAW,CAAC5D,EAAE,EAAEC,CAAC,CAAC;IAAE,CAAC;IACtD,IAAI,CAAC4D,UAAU,GAAG,CAAC;EACrB;EAEA,IAAIJ,QAAQ,GAAG;IACbK,cAAc,EAAE,KAAK;IACrBC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE,GAAG;IACVC,YAAY,EAAE,IAAI;IAClBX,cAAc,EAAE,IAAI;IACpBY,KAAK,EAAE,KAAK;IACZrD,WAAW,EAAE,IAAI;IACjBsD,gBAAgB,EAAE,IAAI;IACtBC,eAAe,EAAE;EACnB,CAAC;EAED,SAASC,UAAUA,CAACrE,EAAE,EAAE;IACtB,IAAIW,KAAK,GAAGX,EAAE,CAACW,KAAK,CAACC,IAAI;IACzB,IAAID,KAAK,CAACwC,SAAS,EAAEnD,EAAE,CAACsE,WAAW,CAACzE,SAAS,CAAC;IAC9C,IAAIc,KAAK,CAACJ,OAAO,CAACuD,cAAc,EAAES,eAAe,CAACvE,EAAE,CAAC;IACrD,KAAK,IAAIwE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7D,KAAK,CAACyC,MAAM,CAACqB,MAAM,EAAE,EAAED,CAAC,EAC1C7D,KAAK,CAACyC,MAAM,CAACoB,CAAC,CAAC,CAACE,KAAK,CAAC,CAAC;IACzB/D,KAAK,CAACyC,MAAM,CAACqB,MAAM,GAAG,CAAC;EACzB;EAEA,SAASF,eAAeA,CAACvE,EAAE,EAAE;IAC3BA,EAAE,CAAC2E,QAAQ,CAAC,UAASC,IAAI,EAAE;MACzB,IAAIC,GAAG,GAAGD,IAAI,CAACE,SAAS,IAAI,8BAA8B,CAACC,IAAI,CAACH,IAAI,CAACE,SAAS,CAAC;MAC/E,IAAID,GAAG,EAAE7E,EAAE,CAACgF,eAAe,CAACJ,IAAI,EAAE,MAAM,EAAEC,GAAG,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC;EACJ;EAEA,SAASI,UAAUA,CAACjF,EAAE,EAAEkF,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAErB,QAAQ,EAAE;IAC5D,IAAIsB,MAAM,GAAGjF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAAEiF,KAAK,GAAGD,MAAM;IAC1DA,MAAM,CAAC/E,SAAS,GAAG,gDAAgD,GAAG6E,QAAQ;IAC9E,IAAIC,QAAQ,EAAE;MACZE,KAAK,GAAGD,MAAM,CAAC5E,WAAW,CAACL,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC;MACzDiF,KAAK,CAAChF,SAAS,GAAG,wDAAwD;IAC5E;IAEA,IAAIyD,QAAQ,IAAI,KAAK,EAAEnE,UAAU,CAACoC,EAAE,CAACsD,KAAK,EAAE,WAAW,EAAE,UAASrF,CAAC,EAAE;MACnEsC,cAAc,CAACvC,EAAE,EAAEC,CAAC,EAAEiF,MAAM,EAAEI,KAAK,CAAC;IACtC,CAAC,CAAC;IAEF,OAAOD,MAAM;EACf;EAEA,SAASE,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAC5B,IAAID,CAAC,IAAI,OAAO,EAAE,OAAOA,CAAC,CAAC,KACtB,OAAOC,CAAC;EACf;EAEA,SAASC,WAAWA,CAACC,WAAW,EAAE;IAChC,IAAIC,KAAK,GAAG,EAAE;IACd,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,WAAW,CAAClB,MAAM,EAAE,EAAED,CAAC,EAAE;MAC3C,IAAIqB,GAAG,GAAGF,WAAW,CAACnB,CAAC,CAAC;QAAEI,IAAI,GAAGiB,GAAG,CAACC,IAAI,CAAClB,IAAI;MAC9C,CAACgB,KAAK,CAAChB,IAAI,CAAC,KAAKgB,KAAK,CAAChB,IAAI,CAAC,GAAG,EAAE,CAAC,EAAEmB,IAAI,CAACF,GAAG,CAAC;IAC/C;IACA,OAAOD,KAAK;EACd;EAEA,SAASI,iBAAiBA,CAACH,GAAG,EAAE;IAC9B,IAAIV,QAAQ,GAAGU,GAAG,CAACV,QAAQ;IAC3B,IAAI,CAACA,QAAQ,EAAEA,QAAQ,GAAG,OAAO;IACjC,IAAIc,GAAG,GAAG7F,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACvC4F,GAAG,CAAC3F,SAAS,GAAG,kDAAkD,GAAG6E,QAAQ;IAC7E,IAAI,OAAOU,GAAG,CAACK,WAAW,IAAI,WAAW,EAAE;MACzCD,GAAG,CAACE,SAAS,GAAGN,GAAG,CAACK,WAAW;IACjC,CAAC,MAAM;MACLD,GAAG,CAACxF,WAAW,CAACL,QAAQ,CAACgG,cAAc,CAACP,GAAG,CAACQ,OAAO,CAAC,CAAC;IACvD;IACA,OAAOJ,GAAG;EACZ;EAEA,SAASK,SAASA,CAACtG,EAAE,EAAEsD,cAAc,EAAE;IACrC,IAAI3C,KAAK,GAAGX,EAAE,CAACW,KAAK,CAACC,IAAI;IACzB,IAAI2F,EAAE,GAAG,EAAE5F,KAAK,CAACkD,UAAU;IAC3B,SAAS2C,KAAKA,CAAA,EAAG;MACfD,EAAE,GAAG,CAAC,CAAC;MACPvG,EAAE,CAACkB,GAAG,CAAC,QAAQ,EAAEsF,KAAK,CAAC;IACzB;IACAxG,EAAE,CAACgC,EAAE,CAAC,QAAQ,EAAEwE,KAAK,CAAC;IACtBlD,cAAc,CAACtD,EAAE,CAACyG,QAAQ,CAAC,CAAC,EAAE,UAASd,WAAW,EAAEe,IAAI,EAAE;MACxD1G,EAAE,CAACkB,GAAG,CAAC,QAAQ,EAAEsF,KAAK,CAAC;MACvB,IAAI7F,KAAK,CAACkD,UAAU,IAAI0C,EAAE,EAAE;MAC5B,IAAIG,IAAI,IAAIf,WAAW,YAAY/F,UAAU,EAAE+F,WAAW,GAAGe,IAAI;MACjE1G,EAAE,CAAC2G,SAAS,CAAC,YAAW;QAACC,aAAa,CAAC5G,EAAE,EAAE2F,WAAW,CAAC;MAAA,CAAC,CAAC;IAC3D,CAAC,EAAEhF,KAAK,CAAC4C,aAAa,EAAEvD,EAAE,CAAC;EAC7B;EAEA,SAAS6G,YAAYA,CAAC7G,EAAE,EAAE;IACxB,IAAIW,KAAK,GAAGX,EAAE,CAACW,KAAK,CAACC,IAAI;IACzB,IAAI,CAACD,KAAK,EAAE;IACZ,IAAIJ,OAAO,GAAGI,KAAK,CAACJ,OAAO;IAC3B;AACJ;AACA;AACA;IACI,IAAI+C,cAAc,GAAG/C,OAAO,CAAC+C,cAAc,IAAItD,EAAE,CAAC8G,SAAS,CAAClH,UAAU,CAACmH,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;IACzF,IAAI,CAACzD,cAAc,EAAE;IACrB,IAAI/C,OAAO,CAAC2D,KAAK,IAAIZ,cAAc,CAACY,KAAK,EAAE;MACzCoC,SAAS,CAACtG,EAAE,EAAEsD,cAAc,CAAC;IAC/B,CAAC,MAAM;MACL,IAAIqC,WAAW,GAAGrC,cAAc,CAACtD,EAAE,CAACyG,QAAQ,CAAC,CAAC,EAAE9F,KAAK,CAAC4C,aAAa,EAAEvD,EAAE,CAAC;MACxE,IAAI,CAAC2F,WAAW,EAAE;MAClB,IAAIA,WAAW,CAACqB,IAAI,EAAErB,WAAW,CAACqB,IAAI,CAAC,UAASC,MAAM,EAAE;QACtDjH,EAAE,CAAC2G,SAAS,CAAC,YAAW;UAACC,aAAa,CAAC5G,EAAE,EAAEiH,MAAM,CAAC;QAAA,CAAC,CAAC;MACtD,CAAC,CAAC,CAAC,KACEjH,EAAE,CAAC2G,SAAS,CAAC,YAAW;QAACC,aAAa,CAAC5G,EAAE,EAAE2F,WAAW,CAAC;MAAA,CAAC,CAAC;IAChE;EACF;EAEA,SAASiB,aAAaA,CAAC5G,EAAE,EAAEkH,oBAAoB,EAAE;IAC/C,IAAIvG,KAAK,GAAGX,EAAE,CAACW,KAAK,CAACC,IAAI;IACzB,IAAI,CAACD,KAAK,EAAE;IACZ,IAAIJ,OAAO,GAAGI,KAAK,CAACJ,OAAO;IAC3B8D,UAAU,CAACrE,EAAE,CAAC;IAEd,IAAI2F,WAAW,GAAGD,WAAW,CAACwB,oBAAoB,CAAC;IAEnD,KAAK,IAAItC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGe,WAAW,CAAClB,MAAM,EAAE,EAAEG,IAAI,EAAE;MACpD,IAAIuC,IAAI,GAAGxB,WAAW,CAACf,IAAI,CAAC;MAC5B,IAAI,CAACuC,IAAI,EAAE;MAEX,IAAIC,WAAW,GAAG,IAAI;MACtB,IAAIC,QAAQ,GAAG1G,KAAK,CAACwC,SAAS,IAAI/C,QAAQ,CAACkH,sBAAsB,CAAC,CAAC;MAEnE,KAAK,IAAI9C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,IAAI,CAAC1C,MAAM,EAAE,EAAED,CAAC,EAAE;QACpC,IAAIqB,GAAG,GAAGsB,IAAI,CAAC3C,CAAC,CAAC;QACjB,IAAIW,QAAQ,GAAGU,GAAG,CAACV,QAAQ;QAC3B,IAAI,CAACA,QAAQ,EAAEA,QAAQ,GAAG,OAAO;QACjCiC,WAAW,GAAG7B,cAAc,CAAC6B,WAAW,EAAEjC,QAAQ,CAAC;QAEnD,IAAI5E,OAAO,CAAC4D,gBAAgB,EAAE0B,GAAG,GAAGtF,OAAO,CAAC4D,gBAAgB,CAAC0B,GAAG,CAAC;QACjE,IAAIlF,KAAK,CAACwC,SAAS,EAAEkE,QAAQ,CAAC5G,WAAW,CAACuF,iBAAiB,CAACH,GAAG,CAAC,CAAC;QAEjE,IAAIA,GAAG,CAAC0B,EAAE,EAAE5G,KAAK,CAACyC,MAAM,CAAC2C,IAAI,CAAC/F,EAAE,CAACwH,QAAQ,CAAC3B,GAAG,CAACC,IAAI,EAAED,GAAG,CAAC0B,EAAE,EAAE;UAC1DjH,SAAS,EAAE,4CAA4C,GAAG6E,QAAQ;UAClEsC,YAAY,EAAE5B;QAChB,CAAC,CAAC,CAAC;MACL;MACA,IAAIlF,KAAK,CAACwC,SAAS,EACjBnD,EAAE,CAAC0H,eAAe,CAAC9C,IAAI,EAAE/E,SAAS,EAAEoF,UAAU,CAACjF,EAAE,EAAEqH,QAAQ,EAAED,WAAW,EAAED,IAAI,CAAC1C,MAAM,GAAG,CAAC,EAC1ClE,OAAO,CAACwD,QAAQ,CAAC,CAAC;MAEnE,IAAIxD,OAAO,CAACuD,cAAc,EACxB9D,EAAE,CAAC2H,YAAY,CAAC/C,IAAI,EAAE,MAAM,EAAE9E,YAAY,GAAGsH,WAAW,CAAC;IAC7D;IACA,IAAI7G,OAAO,CAAC6D,eAAe,EAAE7D,OAAO,CAAC6D,eAAe,CAAC8C,oBAAoB,EAAEvB,WAAW,EAAE3F,EAAE,CAAC;EAC7F;EAEA,SAAS4H,QAAQA,CAAC5H,EAAE,EAAE;IACpB,IAAIW,KAAK,GAAGX,EAAE,CAACW,KAAK,CAACC,IAAI;IACzB,IAAI,CAACD,KAAK,EAAE;IACZkH,YAAY,CAAClH,KAAK,CAACgD,OAAO,CAAC;IAC3BhD,KAAK,CAACgD,OAAO,GAAGrB,UAAU,CAAC,YAAU;MAACuE,YAAY,CAAC7G,EAAE,CAAC;IAAC,CAAC,EAAEW,KAAK,CAACJ,OAAO,CAACyD,KAAK,CAAC;EAChF;EAEA,SAAS8D,aAAaA,CAAC9H,EAAE,EAAE2F,WAAW,EAAE1F,CAAC,EAAE;IACzC,IAAI8H,MAAM,GAAG9H,CAAC,CAAC8H,MAAM,IAAI9H,CAAC,CAAC+H,UAAU;IACrC,IAAIvF,OAAO,GAAGrC,QAAQ,CAACkH,sBAAsB,CAAC,CAAC;IAC/C,KAAK,IAAI9C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,WAAW,CAAClB,MAAM,EAAED,CAAC,EAAE,EAAE;MAC3C,IAAIqB,GAAG,GAAGF,WAAW,CAACnB,CAAC,CAAC;MACxB/B,OAAO,CAAChC,WAAW,CAACuF,iBAAiB,CAACH,GAAG,CAAC,CAAC;IAC7C;IACAtD,cAAc,CAACvC,EAAE,EAAEC,CAAC,EAAEwC,OAAO,EAAEsF,MAAM,CAAC;EACxC;EAEA,SAASnE,WAAWA,CAAC5D,EAAE,EAAEC,CAAC,EAAE;IAC1B,IAAI8H,MAAM,GAAG9H,CAAC,CAAC8H,MAAM,IAAI9H,CAAC,CAAC+H,UAAU;IACrC,IAAI,CAAC,yBAAyB,CAACC,IAAI,CAACF,MAAM,CAACzH,SAAS,CAAC,EAAE;IACvD,IAAI4H,GAAG,GAAGH,MAAM,CAACI,qBAAqB,CAAC,CAAC;MAAEC,CAAC,GAAG,CAACF,GAAG,CAAC1G,IAAI,GAAG0G,GAAG,CAACG,KAAK,IAAI,CAAC;MAAEC,CAAC,GAAG,CAACJ,GAAG,CAAC/G,GAAG,GAAG+G,GAAG,CAACK,MAAM,IAAI,CAAC;IACxG,IAAIC,KAAK,GAAGxI,EAAE,CAACyI,WAAW,CAACzI,EAAE,CAAC0I,UAAU,CAAC;MAAClH,IAAI,EAAE4G,CAAC;MAAEjH,GAAG,EAAEmH;IAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;IAEtE,IAAI3C,WAAW,GAAG,EAAE;IACpB,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,KAAK,CAAC/D,MAAM,EAAE,EAAED,CAAC,EAAE;MACrC,IAAIqB,GAAG,GAAG2C,KAAK,CAAChE,CAAC,CAAC,CAACiD,YAAY;MAC/B,IAAI5B,GAAG,EAAEF,WAAW,CAACI,IAAI,CAACF,GAAG,CAAC;IAChC;IACA,IAAIF,WAAW,CAAClB,MAAM,EAAEqD,aAAa,CAAC9H,EAAE,EAAE2F,WAAW,EAAE1F,CAAC,CAAC;EAC3D;EAEAL,UAAU,CAAC+I,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,UAAS3I,EAAE,EAAE4I,GAAG,EAAEC,GAAG,EAAE;IAC5D,IAAIA,GAAG,IAAIA,GAAG,IAAIjJ,UAAU,CAACkJ,IAAI,EAAE;MACjCzE,UAAU,CAACrE,EAAE,CAAC;MACd,IAAIA,EAAE,CAACW,KAAK,CAACC,IAAI,CAACL,OAAO,CAAC0D,YAAY,KAAK,KAAK,EAC9CjE,EAAE,CAACkB,GAAG,CAAC,QAAQ,EAAE0G,QAAQ,CAAC;MAC5BhI,UAAU,CAACsB,GAAG,CAAClB,EAAE,CAACc,iBAAiB,CAAC,CAAC,EAAE,WAAW,EAAEd,EAAE,CAACW,KAAK,CAACC,IAAI,CAACgD,WAAW,CAAC;MAC9EiE,YAAY,CAAC7H,EAAE,CAACW,KAAK,CAACC,IAAI,CAAC+C,OAAO,CAAC;MACnC,OAAO3D,EAAE,CAACW,KAAK,CAACC,IAAI;IACtB;IAEA,IAAIgI,GAAG,EAAE;MACP,IAAIG,OAAO,GAAG/I,EAAE,CAACgJ,SAAS,CAAC,SAAS,CAAC;QAAEC,aAAa,GAAG,KAAK;MAC5D,KAAK,IAAIzE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuE,OAAO,CAACtE,MAAM,EAAE,EAAED,CAAC,EAAE,IAAIuE,OAAO,CAACvE,CAAC,CAAC,IAAI3E,SAAS,EAAEoJ,aAAa,GAAG,IAAI;MAC1F,IAAItI,KAAK,GAAGX,EAAE,CAACW,KAAK,CAACC,IAAI,GAAG,IAAIqC,SAAS,CAACjD,EAAE,EAAE4I,GAAG,EAAEK,aAAa,CAAC;MACjE,IAAItI,KAAK,CAACJ,OAAO,CAAC0D,YAAY,EAC5BjE,EAAE,CAACgC,EAAE,CAAC,QAAQ,EAAE4F,QAAQ,CAAC;MAC3B,IAAIjH,KAAK,CAACJ,OAAO,CAACwD,QAAQ,IAAI,KAAK,IAAIpD,KAAK,CAACJ,OAAO,CAACwD,QAAQ,IAAI,QAAQ,EACvEnE,UAAU,CAACoC,EAAE,CAAChC,EAAE,CAACc,iBAAiB,CAAC,CAAC,EAAE,WAAW,EAAEH,KAAK,CAACiD,WAAW,CAAC;MAEvEiD,YAAY,CAAC7G,EAAE,CAAC;IAClB;EACF,CAAC,CAAC;EAEFJ,UAAU,CAACsJ,eAAe,CAAC,aAAa,EAAE,YAAW;IACnDrC,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}