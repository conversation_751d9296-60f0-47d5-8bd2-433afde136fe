{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createGetUserAttributeVerificationCodeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createGetUserAttributeVerificationCodeClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Resends user's confirmation code when updating attributes while authenticated.\n *\n * @param input - The SendUserAttributeVerificationCodeInput object\n * @returns SendUserAttributeVerificationCodeOutput\n * @throws - {@link GetUserAttributeVerificationException}\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nconst sendUserAttributeVerificationCode = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (input) {\n    const {\n      userAttributeKey,\n      options\n    } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    const clientMetadata = options?.clientMetadata;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolEndpoint,\n      userPoolId\n    } = authConfig;\n    const {\n      tokens\n    } = yield fetchAuthSession({\n      forceRefresh: false\n    });\n    assertAuthTokens(tokens);\n    const getUserAttributeVerificationCode = createGetUserAttributeVerificationCodeClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const {\n      CodeDeliveryDetails\n    } = yield getUserAttributeVerificationCode({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.SendUserAttributeVerificationCode)\n    }, {\n      AccessToken: tokens.accessToken.toString(),\n      ClientMetadata: clientMetadata,\n      AttributeName: userAttributeKey\n    });\n    const {\n      DeliveryMedium,\n      AttributeName,\n      Destination\n    } = {\n      ...CodeDeliveryDetails\n    };\n    return {\n      destination: Destination,\n      deliveryMedium: DeliveryMedium,\n      attributeName: AttributeName\n    };\n  });\n  return function sendUserAttributeVerificationCode(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { sendUserAttributeVerificationCode };", "map": {"version": 3, "names": ["Amplify", "fetchAuthSession", "assertTokenProviderConfig", "AuthAction", "assertAuthTokens", "getRegionFromUserPoolId", "getAuthUserAgentValue", "createGetUserAttributeVerificationCodeClient", "createCognitoUserPoolEndpointResolver", "sendUserAttributeVerificationCode", "_ref", "_asyncToGenerator", "input", "userAttributeKey", "options", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "clientMetadata", "userPoolEndpoint", "userPoolId", "tokens", "forceRefresh", "getUserAttributeVerificationCode", "endpointResolver", "endpointOverride", "CodeDeliveryDetails", "region", "userAgentValue", "SendUserAttributeVerificationCode", "AccessToken", "accessToken", "toString", "ClientMetadata", "AttributeName", "DeliveryMedium", "Destination", "destination", "deliveryMedium", "attributeName", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/sendUserAttributeVerificationCode.mjs"], "sourcesContent": ["import { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createGetUserAttributeVerificationCodeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createGetUserAttributeVerificationCodeClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Resends user's confirmation code when updating attributes while authenticated.\n *\n * @param input - The SendUserAttributeVerificationCodeInput object\n * @returns SendUserAttributeVerificationCodeOutput\n * @throws - {@link GetUserAttributeVerificationException}\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nconst sendUserAttributeVerificationCode = async (input) => {\n    const { userAttributeKey, options } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    const clientMetadata = options?.clientMetadata;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolEndpoint, userPoolId } = authConfig;\n    const { tokens } = await fetchAuthSession({ forceRefresh: false });\n    assertAuthTokens(tokens);\n    const getUserAttributeVerificationCode = createGetUserAttributeVerificationCodeClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const { CodeDeliveryDetails } = await getUserAttributeVerificationCode({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.SendUserAttributeVerificationCode),\n    }, {\n        AccessToken: tokens.accessToken.toString(),\n        ClientMetadata: clientMetadata,\n        AttributeName: userAttributeKey,\n    });\n    const { DeliveryMedium, AttributeName, Destination } = {\n        ...CodeDeliveryDetails,\n    };\n    return {\n        destination: Destination,\n        deliveryMedium: DeliveryMedium,\n        attributeName: AttributeName,\n    };\n};\n\nexport { sendUserAttributeVerificationCode };\n"], "mappings": ";AAAA,SAASA,OAAO,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC7D,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,sCAAsC;AAC7C,OAAO,qBAAqB;AAC5B,SAASC,4CAA4C,QAAQ,uHAAuH;AACpL,SAASC,qCAAqC,QAAQ,wDAAwD;;AAE9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iCAAiC;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,KAAK,EAAK;IACvD,MAAM;MAAEC,gBAAgB;MAAEC;IAAQ,CAAC,GAAGF,KAAK;IAC3C,MAAMG,UAAU,GAAGf,OAAO,CAACgB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpD,MAAMC,cAAc,GAAGL,OAAO,EAAEK,cAAc;IAC9CjB,yBAAyB,CAACa,UAAU,CAAC;IACrC,MAAM;MAAEK,gBAAgB;MAAEC;IAAW,CAAC,GAAGN,UAAU;IACnD,MAAM;MAAEO;IAAO,CAAC,SAASrB,gBAAgB,CAAC;MAAEsB,YAAY,EAAE;IAAM,CAAC,CAAC;IAClEnB,gBAAgB,CAACkB,MAAM,CAAC;IACxB,MAAME,gCAAgC,GAAGjB,4CAA4C,CAAC;MAClFkB,gBAAgB,EAAEjB,qCAAqC,CAAC;QACpDkB,gBAAgB,EAAEN;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAM;MAAEO;IAAoB,CAAC,SAASH,gCAAgC,CAAC;MACnEI,MAAM,EAAEvB,uBAAuB,CAACgB,UAAU,CAAC;MAC3CQ,cAAc,EAAEvB,qBAAqB,CAACH,UAAU,CAAC2B,iCAAiC;IACtF,CAAC,EAAE;MACCC,WAAW,EAAET,MAAM,CAACU,WAAW,CAACC,QAAQ,CAAC,CAAC;MAC1CC,cAAc,EAAEf,cAAc;MAC9BgB,aAAa,EAAEtB;IACnB,CAAC,CAAC;IACF,MAAM;MAAEuB,cAAc;MAAED,aAAa;MAAEE;IAAY,CAAC,GAAG;MACnD,GAAGV;IACP,CAAC;IACD,OAAO;MACHW,WAAW,EAAED,WAAW;MACxBE,cAAc,EAAEH,cAAc;MAC9BI,aAAa,EAAEL;IACnB,CAAC;EACL,CAAC;EAAA,gBA7BK1B,iCAAiCA,CAAAgC,EAAA;IAAA,OAAA/B,IAAA,CAAAgC,KAAA,OAAAC,SAAA;EAAA;AAAA,GA6BtC;AAED,SAASlC,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}