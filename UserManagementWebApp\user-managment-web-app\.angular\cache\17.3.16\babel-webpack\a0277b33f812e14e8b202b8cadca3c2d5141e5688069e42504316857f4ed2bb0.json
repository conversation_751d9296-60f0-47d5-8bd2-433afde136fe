{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { handleCustomAuthFlowWithoutSRP, getActiveSignInUsername, getSignInResult, getSignInResultFromError } from '../utils/signInHelpers.mjs';\nimport { setActiveSignInState, resetActiveSignInState } from '../../../client/utils/store/signInStore.mjs';\nimport { cacheCognitoTokens } from '../tokenProvider/cacheTokens.mjs';\nimport '../utils/refreshAuthTokens.mjs';\nimport '../tokenProvider/errorHelpers.mjs';\nimport '../utils/types.mjs';\nimport { tokenOrchestrator } from '../tokenProvider/tokenProvider.mjs';\nimport { dispatchSignedInHubEvent } from '../utils/dispatchSignedInHubEvent.mjs';\nimport { retryOnResourceNotFoundException } from '../utils/retryOnResourceNotFoundException.mjs';\nimport { getNewDeviceMetadata } from '../utils/getNewDeviceMetadata.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Signs a user in using a custom authentication flow without password\n *\n * @param input -  The SignInWithCustomAuthInput object\n * @returns AuthSignInResult\n * @throws service: {@link InitiateAuthException } - Cognito service errors thrown during the sign-in process.\n * @throws validation: {@link AuthValidationErrorCode  } - Validation errors thrown when either username or password\n *  are not defined.\n * @throws SignInWithCustomAuthOutput - Thrown when the token provider config is invalid.\n */\nfunction signInWithCustomAuth(_x) {\n  return _signInWithCustomAuth.apply(this, arguments);\n}\nfunction _signInWithCustomAuth() {\n  _signInWithCustomAuth = _asyncToGenerator(function* (input) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      username,\n      password,\n      options\n    } = input;\n    const signInDetails = {\n      loginId: username,\n      authFlowType: 'CUSTOM_WITHOUT_SRP'\n    };\n    const metadata = options?.clientMetadata;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptySignInUsername);\n    assertValidationError(!password, AuthValidationErrorCode.CustomAuthSignInPassword);\n    try {\n      const {\n        ChallengeName: retriedChallengeName,\n        ChallengeParameters: retiredChallengeParameters,\n        AuthenticationResult,\n        Session\n      } = yield retryOnResourceNotFoundException(handleCustomAuthFlowWithoutSRP, [username, metadata, authConfig, tokenOrchestrator], username, tokenOrchestrator);\n      const activeUsername = getActiveSignInUsername(username);\n      // sets up local state used during the sign-in process\n      setActiveSignInState({\n        signInSession: Session,\n        username: activeUsername,\n        challengeName: retriedChallengeName,\n        signInDetails\n      });\n      if (AuthenticationResult) {\n        yield cacheCognitoTokens({\n          username: activeUsername,\n          ...AuthenticationResult,\n          NewDeviceMetadata: yield getNewDeviceMetadata({\n            userPoolId: authConfig.userPoolId,\n            userPoolEndpoint: authConfig.userPoolEndpoint,\n            newDeviceMetadata: AuthenticationResult.NewDeviceMetadata,\n            accessToken: AuthenticationResult.AccessToken\n          }),\n          signInDetails\n        });\n        resetActiveSignInState();\n        yield dispatchSignedInHubEvent();\n        return {\n          isSignedIn: true,\n          nextStep: {\n            signInStep: 'DONE'\n          }\n        };\n      }\n      return getSignInResult({\n        challengeName: retriedChallengeName,\n        challengeParameters: retiredChallengeParameters\n      });\n    } catch (error) {\n      resetActiveSignInState();\n      assertServiceError(error);\n      const result = getSignInResultFromError(error.name);\n      if (result) return result;\n      throw error;\n    }\n  });\n  return _signInWithCustomAuth.apply(this, arguments);\n}\nexport { signInWithCustomAuth };", "map": {"version": 3, "names": ["Amplify", "assertTokenProviderConfig", "AuthValidationErrorCode", "assertValidationError", "assertServiceError", "handleCustomAuthFlowWithoutSRP", "getActiveSignInUsername", "getSignInResult", "getSignInResultFromError", "setActiveSignInState", "resetActiveSignInState", "cacheCognitoTokens", "tokenOrchestrator", "dispatchSignedInHubEvent", "retryOnResourceNotFoundException", "getNewDeviceMetadata", "signInWithCustomAuth", "_x", "_signInWithCustomAuth", "apply", "arguments", "_asyncToGenerator", "input", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "username", "password", "options", "signInDetails", "loginId", "authFlowType", "metadata", "clientMetadata", "EmptySignInUsername", "CustomAuthSignInPassword", "ChallengeName", "retriedChallengeName", "ChallengeParameters", "retiredChallengeParameters", "AuthenticationResult", "Session", "activeUsername", "signInSession", "challenge<PERSON>ame", "NewDeviceMetadata", "userPoolId", "userPoolEndpoint", "newDeviceMetadata", "accessToken", "AccessToken", "isSignedIn", "nextStep", "signInStep", "challengeParameters", "error", "result", "name"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/signInWithCustomAuth.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { handleCustomAuthFlowWithoutSRP, getActiveSignInUsername, getSignInResult, getSignInResultFromError } from '../utils/signInHelpers.mjs';\nimport { setActiveSignInState, resetActiveSignInState } from '../../../client/utils/store/signInStore.mjs';\nimport { cacheCognitoTokens } from '../tokenProvider/cacheTokens.mjs';\nimport '../utils/refreshAuthTokens.mjs';\nimport '../tokenProvider/errorHelpers.mjs';\nimport '../utils/types.mjs';\nimport { tokenOrchestrator } from '../tokenProvider/tokenProvider.mjs';\nimport { dispatchSignedInHubEvent } from '../utils/dispatchSignedInHubEvent.mjs';\nimport { retryOnResourceNotFoundException } from '../utils/retryOnResourceNotFoundException.mjs';\nimport { getNewDeviceMetadata } from '../utils/getNewDeviceMetadata.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Signs a user in using a custom authentication flow without password\n *\n * @param input -  The SignInWithCustomAuthInput object\n * @returns AuthSignInResult\n * @throws service: {@link InitiateAuthException } - Cognito service errors thrown during the sign-in process.\n * @throws validation: {@link AuthValidationErrorCode  } - Validation errors thrown when either username or password\n *  are not defined.\n * @throws SignInWithCustomAuthOutput - Thrown when the token provider config is invalid.\n */\nasync function signInWithCustomAuth(input) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { username, password, options } = input;\n    const signInDetails = {\n        loginId: username,\n        authFlowType: 'CUSTOM_WITHOUT_SRP',\n    };\n    const metadata = options?.clientMetadata;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptySignInUsername);\n    assertValidationError(!password, AuthValidationErrorCode.CustomAuthSignInPassword);\n    try {\n        const { ChallengeName: retriedChallengeName, ChallengeParameters: retiredChallengeParameters, AuthenticationResult, Session, } = await retryOnResourceNotFoundException(handleCustomAuthFlowWithoutSRP, [username, metadata, authConfig, tokenOrchestrator], username, tokenOrchestrator);\n        const activeUsername = getActiveSignInUsername(username);\n        // sets up local state used during the sign-in process\n        setActiveSignInState({\n            signInSession: Session,\n            username: activeUsername,\n            challengeName: retriedChallengeName,\n            signInDetails,\n        });\n        if (AuthenticationResult) {\n            await cacheCognitoTokens({\n                username: activeUsername,\n                ...AuthenticationResult,\n                NewDeviceMetadata: await getNewDeviceMetadata({\n                    userPoolId: authConfig.userPoolId,\n                    userPoolEndpoint: authConfig.userPoolEndpoint,\n                    newDeviceMetadata: AuthenticationResult.NewDeviceMetadata,\n                    accessToken: AuthenticationResult.AccessToken,\n                }),\n                signInDetails,\n            });\n            resetActiveSignInState();\n            await dispatchSignedInHubEvent();\n            return {\n                isSignedIn: true,\n                nextStep: { signInStep: 'DONE' },\n            };\n        }\n        return getSignInResult({\n            challengeName: retriedChallengeName,\n            challengeParameters: retiredChallengeParameters,\n        });\n    }\n    catch (error) {\n        resetActiveSignInState();\n        assertServiceError(error);\n        const result = getSignInResultFromError(error.name);\n        if (result)\n            return result;\n        throw error;\n    }\n}\n\nexport { signInWithCustomAuth };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,yBAAyB,QAAQ,mCAAmC;AAC7E,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,8BAA8B,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,wBAAwB,QAAQ,4BAA4B;AAC/I,SAASC,oBAAoB,EAAEC,sBAAsB,QAAQ,6CAA6C;AAC1G,SAASC,kBAAkB,QAAQ,kCAAkC;AACrE,OAAO,gCAAgC;AACvC,OAAO,mCAAmC;AAC1C,OAAO,oBAAoB;AAC3B,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,SAASC,wBAAwB,QAAQ,uCAAuC;AAChF,SAASC,gCAAgC,QAAQ,+CAA+C;AAChG,SAASC,oBAAoB,QAAQ,mCAAmC;;AAExE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,SAUeC,oBAAoBA,CAAAC,EAAA;EAAA,OAAAC,qBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,sBAAA;EAAAA,qBAAA,GAAAG,iBAAA,CAAnC,WAAoCC,KAAK,EAAE;IACvC,MAAMC,UAAU,GAAGvB,OAAO,CAACwB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDzB,yBAAyB,CAACsB,UAAU,CAAC;IACrC,MAAM;MAAEI,QAAQ;MAAEC,QAAQ;MAAEC;IAAQ,CAAC,GAAGP,KAAK;IAC7C,MAAMQ,aAAa,GAAG;MAClBC,OAAO,EAAEJ,QAAQ;MACjBK,YAAY,EAAE;IAClB,CAAC;IACD,MAAMC,QAAQ,GAAGJ,OAAO,EAAEK,cAAc;IACxC/B,qBAAqB,CAAC,CAAC,CAACwB,QAAQ,EAAEzB,uBAAuB,CAACiC,mBAAmB,CAAC;IAC9EhC,qBAAqB,CAAC,CAACyB,QAAQ,EAAE1B,uBAAuB,CAACkC,wBAAwB,CAAC;IAClF,IAAI;MACA,MAAM;QAAEC,aAAa,EAAEC,oBAAoB;QAAEC,mBAAmB,EAAEC,0BAA0B;QAAEC,oBAAoB;QAAEC;MAAS,CAAC,SAAS5B,gCAAgC,CAACT,8BAA8B,EAAE,CAACsB,QAAQ,EAAEM,QAAQ,EAAEV,UAAU,EAAEX,iBAAiB,CAAC,EAAEe,QAAQ,EAAEf,iBAAiB,CAAC;MACzR,MAAM+B,cAAc,GAAGrC,uBAAuB,CAACqB,QAAQ,CAAC;MACxD;MACAlB,oBAAoB,CAAC;QACjBmC,aAAa,EAAEF,OAAO;QACtBf,QAAQ,EAAEgB,cAAc;QACxBE,aAAa,EAAEP,oBAAoB;QACnCR;MACJ,CAAC,CAAC;MACF,IAAIW,oBAAoB,EAAE;QACtB,MAAM9B,kBAAkB,CAAC;UACrBgB,QAAQ,EAAEgB,cAAc;UACxB,GAAGF,oBAAoB;UACvBK,iBAAiB,QAAQ/B,oBAAoB,CAAC;YAC1CgC,UAAU,EAAExB,UAAU,CAACwB,UAAU;YACjCC,gBAAgB,EAAEzB,UAAU,CAACyB,gBAAgB;YAC7CC,iBAAiB,EAAER,oBAAoB,CAACK,iBAAiB;YACzDI,WAAW,EAAET,oBAAoB,CAACU;UACtC,CAAC,CAAC;UACFrB;QACJ,CAAC,CAAC;QACFpB,sBAAsB,CAAC,CAAC;QACxB,MAAMG,wBAAwB,CAAC,CAAC;QAChC,OAAO;UACHuC,UAAU,EAAE,IAAI;UAChBC,QAAQ,EAAE;YAAEC,UAAU,EAAE;UAAO;QACnC,CAAC;MACL;MACA,OAAO/C,eAAe,CAAC;QACnBsC,aAAa,EAAEP,oBAAoB;QACnCiB,mBAAmB,EAAEf;MACzB,CAAC,CAAC;IACN,CAAC,CACD,OAAOgB,KAAK,EAAE;MACV9C,sBAAsB,CAAC,CAAC;MACxBN,kBAAkB,CAACoD,KAAK,CAAC;MACzB,MAAMC,MAAM,GAAGjD,wBAAwB,CAACgD,KAAK,CAACE,IAAI,CAAC;MACnD,IAAID,MAAM,EACN,OAAOA,MAAM;MACjB,MAAMD,KAAK;IACf;EACJ,CAAC;EAAA,OAAAtC,qBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}