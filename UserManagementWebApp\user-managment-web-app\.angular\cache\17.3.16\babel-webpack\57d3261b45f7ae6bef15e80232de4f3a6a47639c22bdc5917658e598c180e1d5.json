{"ast": null, "code": "const text = {\n  // default styles\n  color: {\n    value: '{colors.font.primary.value}'\n  },\n  // variations\n  primary: {\n    color: {\n      value: '{colors.font.primary.value}'\n    }\n  },\n  secondary: {\n    color: {\n      value: '{colors.font.secondary.value}'\n    }\n  },\n  tertiary: {\n    color: {\n      value: '{colors.font.tertiary.value}'\n    }\n  },\n  error: {\n    color: {\n      value: '{colors.font.error.value}'\n    }\n  },\n  warning: {\n    color: {\n      value: '{colors.font.warning.value}'\n    }\n  },\n  success: {\n    color: {\n      value: '{colors.font.success.value}'\n    }\n  },\n  info: {\n    color: {\n      value: '{colors.font.info.value}'\n    }\n  }\n};\nexport { text };", "map": {"version": 3, "names": ["text", "color", "value", "primary", "secondary", "tertiary", "error", "warning", "success", "info"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/text.mjs"], "sourcesContent": ["const text = {\n    // default styles\n    color: { value: '{colors.font.primary.value}' },\n    // variations\n    primary: {\n        color: { value: '{colors.font.primary.value}' },\n    },\n    secondary: {\n        color: { value: '{colors.font.secondary.value}' },\n    },\n    tertiary: {\n        color: { value: '{colors.font.tertiary.value}' },\n    },\n    error: {\n        color: { value: '{colors.font.error.value}' },\n    },\n    warning: {\n        color: { value: '{colors.font.warning.value}' },\n    },\n    success: {\n        color: { value: '{colors.font.success.value}' },\n    },\n    info: {\n        color: { value: '{colors.font.info.value}' },\n    },\n};\n\nexport { text };\n"], "mappings": "AAAA,MAAMA,IAAI,GAAG;EACT;EACAC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAA8B,CAAC;EAC/C;EACAC,OAAO,EAAE;IACLF,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA8B;EAClD,CAAC;EACDE,SAAS,EAAE;IACPH,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAgC;EACpD,CAAC;EACDG,QAAQ,EAAE;IACNJ,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA+B;EACnD,CAAC;EACDI,KAAK,EAAE;IACHL,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA4B;EAChD,CAAC;EACDK,OAAO,EAAE;IACLN,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA8B;EAClD,CAAC;EACDM,OAAO,EAAE;IACLP,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA8B;EAClD,CAAC;EACDO,IAAI,EAAE;IACFR,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA2B;EAC/C;AACJ,CAAC;AAED,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}