{"ast": null, "code": "const opacities = {\n  0: {\n    value: '0'\n  },\n  10: {\n    value: '0.1'\n  },\n  20: {\n    value: '0.2'\n  },\n  30: {\n    value: '0.3'\n  },\n  40: {\n    value: '0.4'\n  },\n  50: {\n    value: '0.5'\n  },\n  60: {\n    value: '0.6'\n  },\n  70: {\n    value: '0.7'\n  },\n  80: {\n    value: '0.8'\n  },\n  90: {\n    value: '0.9'\n  },\n  100: {\n    value: '1'\n  }\n};\nexport { opacities };", "map": {"version": 3, "names": ["opacities", "value"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/opacities.mjs"], "sourcesContent": ["const opacities = {\n    0: { value: '0' },\n    10: { value: '0.1' },\n    20: { value: '0.2' },\n    30: { value: '0.3' },\n    40: { value: '0.4' },\n    50: { value: '0.5' },\n    60: { value: '0.6' },\n    70: { value: '0.7' },\n    80: { value: '0.8' },\n    90: { value: '0.9' },\n    100: { value: '1' },\n};\n\nexport { opacities };\n"], "mappings": "AAAA,MAAMA,SAAS,GAAG;EACd,CAAC,EAAE;IAAEC,KAAK,EAAE;EAAI,CAAC;EACjB,EAAE,EAAE;IAAEA,KAAK,EAAE;EAAM,CAAC;EACpB,EAAE,EAAE;IAAEA,KAAK,EAAE;EAAM,CAAC;EACpB,EAAE,EAAE;IAAEA,KAAK,EAAE;EAAM,CAAC;EACpB,EAAE,EAAE;IAAEA,KAAK,EAAE;EAAM,CAAC;EACpB,EAAE,EAAE;IAAEA,KAAK,EAAE;EAAM,CAAC;EACpB,EAAE,EAAE;IAAEA,KAAK,EAAE;EAAM,CAAC;EACpB,EAAE,EAAE;IAAEA,KAAK,EAAE;EAAM,CAAC;EACpB,EAAE,EAAE;IAAEA,KAAK,EAAE;EAAM,CAAC;EACpB,EAAE,EAAE;IAAEA,KAAK,EAAE;EAAM,CAAC;EACpB,GAAG,EAAE;IAAEA,KAAK,EAAE;EAAI;AACtB,CAAC;AAED,SAASD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}