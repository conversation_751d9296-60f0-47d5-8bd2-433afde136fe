{"ast": null, "code": "import { createAssertionFunction, AmplifyError, AmplifyErrorCode } from '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass PasskeyError extends AmplifyError {\n  constructor(params) {\n    super(params);\n    // Hack for making the custom error class work when transpiled to es5\n    // TODO: Delete the following 2 lines after we change the build target to >= es2015\n    this.constructor = PasskeyError;\n    Object.setPrototypeOf(this, PasskeyError.prototype);\n  }\n}\nvar PasskeyErrorCode;\n(function (PasskeyErrorCode) {\n  // not supported\n  PasskeyErrorCode[\"PasskeyNotSupported\"] = \"PasskeyNotSupported\";\n  // duplicate passkey\n  PasskeyErrorCode[\"PasskeyAlreadyExists\"] = \"PasskeyAlreadyExists\";\n  // misconfigurations\n  PasskeyErrorCode[\"InvalidPasskeyRegistrationOptions\"] = \"InvalidPasskeyRegistrationOptions\";\n  PasskeyErrorCode[\"InvalidPasskeyAuthenticationOptions\"] = \"InvalidPasskeyAuthenticationOptions\";\n  PasskeyErrorCode[\"RelyingPartyMismatch\"] = \"RelyingPartyMismatch\";\n  // failed credential creation / retrieval\n  PasskeyErrorCode[\"PasskeyRegistrationFailed\"] = \"PasskeyRegistrationFailed\";\n  PasskeyErrorCode[\"PasskeyRetrievalFailed\"] = \"PasskeyRetrievalFailed\";\n  // cancel / aborts\n  PasskeyErrorCode[\"PasskeyRegistrationCanceled\"] = \"PasskeyRegistrationCanceled\";\n  PasskeyErrorCode[\"PasskeyAuthenticationCanceled\"] = \"PasskeyAuthenticationCanceled\";\n  PasskeyErrorCode[\"PasskeyOperationAborted\"] = \"PasskeyOperationAborted\";\n})(PasskeyErrorCode || (PasskeyErrorCode = {}));\nconst notSupportedRecoverySuggestion = 'Passkeys may not be supported on this device. Ensure your application is running in a secure context (HTTPS) and Web Authentication API is supported.';\nconst abortOrCancelRecoverySuggestion = 'User may have canceled the ceremony or another interruption has occurred. Check underlying error for details.';\nconst misconfigurationRecoverySuggestion = 'Ensure your user pool is configured to support the WEB_AUTHN as an authentication factor.';\nconst passkeyErrorMap = {\n  [PasskeyErrorCode.PasskeyNotSupported]: {\n    message: 'Passkeys may not be supported on this device.',\n    recoverySuggestion: notSupportedRecoverySuggestion\n  },\n  [PasskeyErrorCode.InvalidPasskeyRegistrationOptions]: {\n    message: 'Invalid passkey registration options.',\n    recoverySuggestion: misconfigurationRecoverySuggestion\n  },\n  [PasskeyErrorCode.InvalidPasskeyAuthenticationOptions]: {\n    message: 'Invalid passkey authentication options.',\n    recoverySuggestion: misconfigurationRecoverySuggestion\n  },\n  [PasskeyErrorCode.PasskeyRegistrationFailed]: {\n    message: 'Device failed to create passkey.',\n    recoverySuggestion: notSupportedRecoverySuggestion\n  },\n  [PasskeyErrorCode.PasskeyRetrievalFailed]: {\n    message: 'Device failed to retrieve passkey.',\n    recoverySuggestion: 'Passkeys may not be available on this device. Try an alternative authentication factor like PASSWORD, EMAIL_OTP, or SMS_OTP.'\n  },\n  [PasskeyErrorCode.PasskeyAlreadyExists]: {\n    message: 'Passkey already exists in authenticator.',\n    recoverySuggestion: 'Proceed with existing passkey or try again after deleting the credential.'\n  },\n  [PasskeyErrorCode.PasskeyRegistrationCanceled]: {\n    message: 'Passkey registration ceremony has been canceled.',\n    recoverySuggestion: abortOrCancelRecoverySuggestion\n  },\n  [PasskeyErrorCode.PasskeyAuthenticationCanceled]: {\n    message: 'Passkey authentication ceremony has been canceled.',\n    recoverySuggestion: abortOrCancelRecoverySuggestion\n  },\n  [PasskeyErrorCode.PasskeyOperationAborted]: {\n    message: 'Passkey operation has been aborted.',\n    recoverySuggestion: abortOrCancelRecoverySuggestion\n  },\n  [PasskeyErrorCode.RelyingPartyMismatch]: {\n    message: 'Relying party does not match current domain.',\n    recoverySuggestion: 'Ensure relying party identifier matches current domain.'\n  }\n};\nconst assertPasskeyError = createAssertionFunction(passkeyErrorMap, PasskeyError);\n/**\n * Handle Passkey Authentication Errors\n * https://w3c.github.io/webauthn/#sctn-get-request-exceptions\n *\n * @param err unknown\n * @returns PasskeyError\n */\nconst handlePasskeyAuthenticationError = err => {\n  if (err instanceof PasskeyError) {\n    return err;\n  }\n  if (err instanceof Error) {\n    if (err.name === 'NotAllowedError') {\n      const {\n        message,\n        recoverySuggestion\n      } = passkeyErrorMap[PasskeyErrorCode.PasskeyAuthenticationCanceled];\n      return new PasskeyError({\n        name: PasskeyErrorCode.PasskeyAuthenticationCanceled,\n        message,\n        recoverySuggestion,\n        underlyingError: err\n      });\n    }\n  }\n  return handlePasskeyError(err);\n};\n/**\n * Handle Passkey Registration Errors\n * https://w3c.github.io/webauthn/#sctn-create-request-exceptions\n *\n * @param err unknown\n * @returns PasskeyError\n */\nconst handlePasskeyRegistrationError = err => {\n  if (err instanceof PasskeyError) {\n    return err;\n  }\n  if (err instanceof Error) {\n    // Duplicate Passkey\n    if (err.name === 'InvalidStateError') {\n      const {\n        message,\n        recoverySuggestion\n      } = passkeyErrorMap[PasskeyErrorCode.PasskeyAlreadyExists];\n      return new PasskeyError({\n        name: PasskeyErrorCode.PasskeyAlreadyExists,\n        message,\n        recoverySuggestion,\n        underlyingError: err\n      });\n    }\n    // User Cancels Ceremony / Generic Catch All\n    if (err.name === 'NotAllowedError') {\n      const {\n        message,\n        recoverySuggestion\n      } = passkeyErrorMap[PasskeyErrorCode.PasskeyRegistrationCanceled];\n      return new PasskeyError({\n        name: PasskeyErrorCode.PasskeyRegistrationCanceled,\n        message,\n        recoverySuggestion,\n        underlyingError: err\n      });\n    }\n  }\n  return handlePasskeyError(err);\n};\n/**\n * Handles Overlapping Passkey Errors Between Registration & Authentication\n * https://w3c.github.io/webauthn/#sctn-create-request-exceptions\n * https://w3c.github.io/webauthn/#sctn-get-request-exceptions\n *\n * @param err unknown\n * @returns PasskeyError\n */\nconst handlePasskeyError = err => {\n  if (err instanceof Error) {\n    // Passkey Operation Aborted\n    if (err.name === 'AbortError') {\n      const {\n        message,\n        recoverySuggestion\n      } = passkeyErrorMap[PasskeyErrorCode.PasskeyOperationAborted];\n      return new PasskeyError({\n        name: PasskeyErrorCode.PasskeyOperationAborted,\n        message,\n        recoverySuggestion,\n        underlyingError: err\n      });\n    }\n    // Relying Party / Domain Mismatch\n    if (err.name === 'SecurityError') {\n      const {\n        message,\n        recoverySuggestion\n      } = passkeyErrorMap[PasskeyErrorCode.RelyingPartyMismatch];\n      return new PasskeyError({\n        name: PasskeyErrorCode.RelyingPartyMismatch,\n        message,\n        recoverySuggestion,\n        underlyingError: err\n      });\n    }\n  }\n  return new PasskeyError({\n    name: AmplifyErrorCode.Unknown,\n    message: 'An unknown error has occurred.',\n    underlyingError: err\n  });\n};\nexport { PasskeyError, PasskeyErrorCode, assertPasskeyError, handlePasskeyAuthenticationError, handlePasskeyRegistrationError };", "map": {"version": 3, "names": ["createAssertionFunction", "AmplifyError", "AmplifyErrorCode", "PasskeyError", "constructor", "params", "Object", "setPrototypeOf", "prototype", "PasskeyErrorCode", "notSupportedRecoverySuggestion", "abortOrCancelRecoverySuggestion", "misconfigurationRecoverySuggestion", "passkeyErrorMap", "PasskeyNotSupported", "message", "recoverySuggestion", "InvalidPasskeyRegistrationOptions", "InvalidPasskeyAuthenticationOptions", "PasskeyRegistrationFailed", "PasskeyRetrievalFailed", "PasskeyAlreadyExists", "PasskeyRegistrationCanceled", "PasskeyAuthenticationCanceled", "PasskeyOperationAborted", "RelyingPartyMismatch", "assertPasskeyError", "handlePasskeyAuthenticationError", "err", "Error", "name", "underlyingError", "handlePasskeyError", "handlePasskeyRegistrationError", "Unknown"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/utils/passkey/errors.mjs"], "sourcesContent": ["import { createAssertionFunction, AmplifyError, AmplifyErrorCode } from '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass PasskeyError extends AmplifyError {\n    constructor(params) {\n        super(params);\n        // Hack for making the custom error class work when transpiled to es5\n        // TODO: Delete the following 2 lines after we change the build target to >= es2015\n        this.constructor = PasskeyError;\n        Object.setPrototypeOf(this, PasskeyError.prototype);\n    }\n}\nvar PasskeyErrorCode;\n(function (PasskeyErrorCode) {\n    // not supported\n    PasskeyErrorCode[\"PasskeyNotSupported\"] = \"PasskeyNotSupported\";\n    // duplicate passkey\n    PasskeyErrorCode[\"PasskeyAlreadyExists\"] = \"PasskeyAlreadyExists\";\n    // misconfigurations\n    PasskeyErrorCode[\"InvalidPasskeyRegistrationOptions\"] = \"InvalidPasskeyRegistrationOptions\";\n    PasskeyErrorCode[\"InvalidPasskeyAuthenticationOptions\"] = \"InvalidPasskeyAuthenticationOptions\";\n    PasskeyErrorCode[\"RelyingPartyMismatch\"] = \"RelyingPartyMismatch\";\n    // failed credential creation / retrieval\n    PasskeyErrorCode[\"PasskeyRegistrationFailed\"] = \"PasskeyRegistrationFailed\";\n    PasskeyErrorCode[\"PasskeyRetrievalFailed\"] = \"PasskeyRetrievalFailed\";\n    // cancel / aborts\n    PasskeyErrorCode[\"PasskeyRegistrationCanceled\"] = \"PasskeyRegistrationCanceled\";\n    PasskeyErrorCode[\"PasskeyAuthenticationCanceled\"] = \"PasskeyAuthenticationCanceled\";\n    PasskeyErrorCode[\"PasskeyOperationAborted\"] = \"PasskeyOperationAborted\";\n})(PasskeyErrorCode || (PasskeyErrorCode = {}));\nconst notSupportedRecoverySuggestion = 'Passkeys may not be supported on this device. Ensure your application is running in a secure context (HTTPS) and Web Authentication API is supported.';\nconst abortOrCancelRecoverySuggestion = 'User may have canceled the ceremony or another interruption has occurred. Check underlying error for details.';\nconst misconfigurationRecoverySuggestion = 'Ensure your user pool is configured to support the WEB_AUTHN as an authentication factor.';\nconst passkeyErrorMap = {\n    [PasskeyErrorCode.PasskeyNotSupported]: {\n        message: 'Passkeys may not be supported on this device.',\n        recoverySuggestion: notSupportedRecoverySuggestion,\n    },\n    [PasskeyErrorCode.InvalidPasskeyRegistrationOptions]: {\n        message: 'Invalid passkey registration options.',\n        recoverySuggestion: misconfigurationRecoverySuggestion,\n    },\n    [PasskeyErrorCode.InvalidPasskeyAuthenticationOptions]: {\n        message: 'Invalid passkey authentication options.',\n        recoverySuggestion: misconfigurationRecoverySuggestion,\n    },\n    [PasskeyErrorCode.PasskeyRegistrationFailed]: {\n        message: 'Device failed to create passkey.',\n        recoverySuggestion: notSupportedRecoverySuggestion,\n    },\n    [PasskeyErrorCode.PasskeyRetrievalFailed]: {\n        message: 'Device failed to retrieve passkey.',\n        recoverySuggestion: 'Passkeys may not be available on this device. Try an alternative authentication factor like PASSWORD, EMAIL_OTP, or SMS_OTP.',\n    },\n    [PasskeyErrorCode.PasskeyAlreadyExists]: {\n        message: 'Passkey already exists in authenticator.',\n        recoverySuggestion: 'Proceed with existing passkey or try again after deleting the credential.',\n    },\n    [PasskeyErrorCode.PasskeyRegistrationCanceled]: {\n        message: 'Passkey registration ceremony has been canceled.',\n        recoverySuggestion: abortOrCancelRecoverySuggestion,\n    },\n    [PasskeyErrorCode.PasskeyAuthenticationCanceled]: {\n        message: 'Passkey authentication ceremony has been canceled.',\n        recoverySuggestion: abortOrCancelRecoverySuggestion,\n    },\n    [PasskeyErrorCode.PasskeyOperationAborted]: {\n        message: 'Passkey operation has been aborted.',\n        recoverySuggestion: abortOrCancelRecoverySuggestion,\n    },\n    [PasskeyErrorCode.RelyingPartyMismatch]: {\n        message: 'Relying party does not match current domain.',\n        recoverySuggestion: 'Ensure relying party identifier matches current domain.',\n    },\n};\nconst assertPasskeyError = createAssertionFunction(passkeyErrorMap, PasskeyError);\n/**\n * Handle Passkey Authentication Errors\n * https://w3c.github.io/webauthn/#sctn-get-request-exceptions\n *\n * @param err unknown\n * @returns PasskeyError\n */\nconst handlePasskeyAuthenticationError = (err) => {\n    if (err instanceof PasskeyError) {\n        return err;\n    }\n    if (err instanceof Error) {\n        if (err.name === 'NotAllowedError') {\n            const { message, recoverySuggestion } = passkeyErrorMap[PasskeyErrorCode.PasskeyAuthenticationCanceled];\n            return new PasskeyError({\n                name: PasskeyErrorCode.PasskeyAuthenticationCanceled,\n                message,\n                recoverySuggestion,\n                underlyingError: err,\n            });\n        }\n    }\n    return handlePasskeyError(err);\n};\n/**\n * Handle Passkey Registration Errors\n * https://w3c.github.io/webauthn/#sctn-create-request-exceptions\n *\n * @param err unknown\n * @returns PasskeyError\n */\nconst handlePasskeyRegistrationError = (err) => {\n    if (err instanceof PasskeyError) {\n        return err;\n    }\n    if (err instanceof Error) {\n        // Duplicate Passkey\n        if (err.name === 'InvalidStateError') {\n            const { message, recoverySuggestion } = passkeyErrorMap[PasskeyErrorCode.PasskeyAlreadyExists];\n            return new PasskeyError({\n                name: PasskeyErrorCode.PasskeyAlreadyExists,\n                message,\n                recoverySuggestion,\n                underlyingError: err,\n            });\n        }\n        // User Cancels Ceremony / Generic Catch All\n        if (err.name === 'NotAllowedError') {\n            const { message, recoverySuggestion } = passkeyErrorMap[PasskeyErrorCode.PasskeyRegistrationCanceled];\n            return new PasskeyError({\n                name: PasskeyErrorCode.PasskeyRegistrationCanceled,\n                message,\n                recoverySuggestion,\n                underlyingError: err,\n            });\n        }\n    }\n    return handlePasskeyError(err);\n};\n/**\n * Handles Overlapping Passkey Errors Between Registration & Authentication\n * https://w3c.github.io/webauthn/#sctn-create-request-exceptions\n * https://w3c.github.io/webauthn/#sctn-get-request-exceptions\n *\n * @param err unknown\n * @returns PasskeyError\n */\nconst handlePasskeyError = (err) => {\n    if (err instanceof Error) {\n        // Passkey Operation Aborted\n        if (err.name === 'AbortError') {\n            const { message, recoverySuggestion } = passkeyErrorMap[PasskeyErrorCode.PasskeyOperationAborted];\n            return new PasskeyError({\n                name: PasskeyErrorCode.PasskeyOperationAborted,\n                message,\n                recoverySuggestion,\n                underlyingError: err,\n            });\n        }\n        // Relying Party / Domain Mismatch\n        if (err.name === 'SecurityError') {\n            const { message, recoverySuggestion } = passkeyErrorMap[PasskeyErrorCode.RelyingPartyMismatch];\n            return new PasskeyError({\n                name: PasskeyErrorCode.RelyingPartyMismatch,\n                message,\n                recoverySuggestion,\n                underlyingError: err,\n            });\n        }\n    }\n    return new PasskeyError({\n        name: AmplifyErrorCode.Unknown,\n        message: 'An unknown error has occurred.',\n        underlyingError: err,\n    });\n};\n\nexport { PasskeyError, PasskeyErrorCode, assertPasskeyError, handlePasskeyAuthenticationError, handlePasskeyRegistrationError };\n"], "mappings": "AAAA,SAASA,uBAAuB,EAAEC,YAAY,EAAEC,gBAAgB,QAAQ,mCAAmC;;AAE3G;AACA;AACA,MAAMC,YAAY,SAASF,YAAY,CAAC;EACpCG,WAAWA,CAACC,MAAM,EAAE;IAChB,KAAK,CAACA,MAAM,CAAC;IACb;IACA;IACA,IAAI,CAACD,WAAW,GAAGD,YAAY;IAC/BG,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEJ,YAAY,CAACK,SAAS,CAAC;EACvD;AACJ;AACA,IAAIC,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EACzB;EACAA,gBAAgB,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EAC/D;EACAA,gBAAgB,CAAC,sBAAsB,CAAC,GAAG,sBAAsB;EACjE;EACAA,gBAAgB,CAAC,mCAAmC,CAAC,GAAG,mCAAmC;EAC3FA,gBAAgB,CAAC,qCAAqC,CAAC,GAAG,qCAAqC;EAC/FA,gBAAgB,CAAC,sBAAsB,CAAC,GAAG,sBAAsB;EACjE;EACAA,gBAAgB,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EAC3EA,gBAAgB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACrE;EACAA,gBAAgB,CAAC,6BAA6B,CAAC,GAAG,6BAA6B;EAC/EA,gBAAgB,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;EACnFA,gBAAgB,CAAC,yBAAyB,CAAC,GAAG,yBAAyB;AAC3E,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,MAAMC,8BAA8B,GAAG,uJAAuJ;AAC9L,MAAMC,+BAA+B,GAAG,+GAA+G;AACvJ,MAAMC,kCAAkC,GAAG,2FAA2F;AACtI,MAAMC,eAAe,GAAG;EACpB,CAACJ,gBAAgB,CAACK,mBAAmB,GAAG;IACpCC,OAAO,EAAE,+CAA+C;IACxDC,kBAAkB,EAAEN;EACxB,CAAC;EACD,CAACD,gBAAgB,CAACQ,iCAAiC,GAAG;IAClDF,OAAO,EAAE,uCAAuC;IAChDC,kBAAkB,EAAEJ;EACxB,CAAC;EACD,CAACH,gBAAgB,CAACS,mCAAmC,GAAG;IACpDH,OAAO,EAAE,yCAAyC;IAClDC,kBAAkB,EAAEJ;EACxB,CAAC;EACD,CAACH,gBAAgB,CAACU,yBAAyB,GAAG;IAC1CJ,OAAO,EAAE,kCAAkC;IAC3CC,kBAAkB,EAAEN;EACxB,CAAC;EACD,CAACD,gBAAgB,CAACW,sBAAsB,GAAG;IACvCL,OAAO,EAAE,oCAAoC;IAC7CC,kBAAkB,EAAE;EACxB,CAAC;EACD,CAACP,gBAAgB,CAACY,oBAAoB,GAAG;IACrCN,OAAO,EAAE,0CAA0C;IACnDC,kBAAkB,EAAE;EACxB,CAAC;EACD,CAACP,gBAAgB,CAACa,2BAA2B,GAAG;IAC5CP,OAAO,EAAE,kDAAkD;IAC3DC,kBAAkB,EAAEL;EACxB,CAAC;EACD,CAACF,gBAAgB,CAACc,6BAA6B,GAAG;IAC9CR,OAAO,EAAE,oDAAoD;IAC7DC,kBAAkB,EAAEL;EACxB,CAAC;EACD,CAACF,gBAAgB,CAACe,uBAAuB,GAAG;IACxCT,OAAO,EAAE,qCAAqC;IAC9CC,kBAAkB,EAAEL;EACxB,CAAC;EACD,CAACF,gBAAgB,CAACgB,oBAAoB,GAAG;IACrCV,OAAO,EAAE,8CAA8C;IACvDC,kBAAkB,EAAE;EACxB;AACJ,CAAC;AACD,MAAMU,kBAAkB,GAAG1B,uBAAuB,CAACa,eAAe,EAAEV,YAAY,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwB,gCAAgC,GAAIC,GAAG,IAAK;EAC9C,IAAIA,GAAG,YAAYzB,YAAY,EAAE;IAC7B,OAAOyB,GAAG;EACd;EACA,IAAIA,GAAG,YAAYC,KAAK,EAAE;IACtB,IAAID,GAAG,CAACE,IAAI,KAAK,iBAAiB,EAAE;MAChC,MAAM;QAAEf,OAAO;QAAEC;MAAmB,CAAC,GAAGH,eAAe,CAACJ,gBAAgB,CAACc,6BAA6B,CAAC;MACvG,OAAO,IAAIpB,YAAY,CAAC;QACpB2B,IAAI,EAAErB,gBAAgB,CAACc,6BAA6B;QACpDR,OAAO;QACPC,kBAAkB;QAClBe,eAAe,EAAEH;MACrB,CAAC,CAAC;IACN;EACJ;EACA,OAAOI,kBAAkB,CAACJ,GAAG,CAAC;AAClC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,8BAA8B,GAAIL,GAAG,IAAK;EAC5C,IAAIA,GAAG,YAAYzB,YAAY,EAAE;IAC7B,OAAOyB,GAAG;EACd;EACA,IAAIA,GAAG,YAAYC,KAAK,EAAE;IACtB;IACA,IAAID,GAAG,CAACE,IAAI,KAAK,mBAAmB,EAAE;MAClC,MAAM;QAAEf,OAAO;QAAEC;MAAmB,CAAC,GAAGH,eAAe,CAACJ,gBAAgB,CAACY,oBAAoB,CAAC;MAC9F,OAAO,IAAIlB,YAAY,CAAC;QACpB2B,IAAI,EAAErB,gBAAgB,CAACY,oBAAoB;QAC3CN,OAAO;QACPC,kBAAkB;QAClBe,eAAe,EAAEH;MACrB,CAAC,CAAC;IACN;IACA;IACA,IAAIA,GAAG,CAACE,IAAI,KAAK,iBAAiB,EAAE;MAChC,MAAM;QAAEf,OAAO;QAAEC;MAAmB,CAAC,GAAGH,eAAe,CAACJ,gBAAgB,CAACa,2BAA2B,CAAC;MACrG,OAAO,IAAInB,YAAY,CAAC;QACpB2B,IAAI,EAAErB,gBAAgB,CAACa,2BAA2B;QAClDP,OAAO;QACPC,kBAAkB;QAClBe,eAAe,EAAEH;MACrB,CAAC,CAAC;IACN;EACJ;EACA,OAAOI,kBAAkB,CAACJ,GAAG,CAAC;AAClC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,kBAAkB,GAAIJ,GAAG,IAAK;EAChC,IAAIA,GAAG,YAAYC,KAAK,EAAE;IACtB;IACA,IAAID,GAAG,CAACE,IAAI,KAAK,YAAY,EAAE;MAC3B,MAAM;QAAEf,OAAO;QAAEC;MAAmB,CAAC,GAAGH,eAAe,CAACJ,gBAAgB,CAACe,uBAAuB,CAAC;MACjG,OAAO,IAAIrB,YAAY,CAAC;QACpB2B,IAAI,EAAErB,gBAAgB,CAACe,uBAAuB;QAC9CT,OAAO;QACPC,kBAAkB;QAClBe,eAAe,EAAEH;MACrB,CAAC,CAAC;IACN;IACA;IACA,IAAIA,GAAG,CAACE,IAAI,KAAK,eAAe,EAAE;MAC9B,MAAM;QAAEf,OAAO;QAAEC;MAAmB,CAAC,GAAGH,eAAe,CAACJ,gBAAgB,CAACgB,oBAAoB,CAAC;MAC9F,OAAO,IAAItB,YAAY,CAAC;QACpB2B,IAAI,EAAErB,gBAAgB,CAACgB,oBAAoB;QAC3CV,OAAO;QACPC,kBAAkB;QAClBe,eAAe,EAAEH;MACrB,CAAC,CAAC;IACN;EACJ;EACA,OAAO,IAAIzB,YAAY,CAAC;IACpB2B,IAAI,EAAE5B,gBAAgB,CAACgC,OAAO;IAC9BnB,OAAO,EAAE,gCAAgC;IACzCgB,eAAe,EAAEH;EACrB,CAAC,CAAC;AACN,CAAC;AAED,SAASzB,YAAY,EAAEM,gBAAgB,EAAEiB,kBAAkB,EAAEC,gCAAgC,EAAEM,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}