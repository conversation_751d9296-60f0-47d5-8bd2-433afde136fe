{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AmplifyErrorCode } from '../../../types/errors.mjs';\nimport { isClockSkewError } from './isClockSkewError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Get retry decider function\n * @param errorParser Function to load JavaScript error from HTTP response\n */\nconst getRetryDecider = errorParser => (/*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (response, error) {\n    const parsedError = error ?? (yield errorParser(response)) ?? undefined;\n    const errorCode = parsedError?.code || parsedError?.name;\n    const statusCode = response?.statusCode;\n    const isRetryable = isConnectionError(error) || isThrottlingError(statusCode, errorCode) || isClockSkewError(errorCode) || isServerSideError(statusCode, errorCode);\n    return {\n      retryable: isRetryable\n    };\n  });\n  return function (_x, _x2) {\n    return _ref.apply(this, arguments);\n  };\n}());\n// reference: https://github.com/aws/aws-sdk-js-v3/blob/ab0e7be36e7e7f8a0c04834357aaad643c7912c3/packages/service-error-classification/src/constants.ts#L22-L37\nconst THROTTLING_ERROR_CODES = ['BandwidthLimitExceeded', 'EC2ThrottledException', 'LimitExceededException', 'PriorRequestNotComplete', 'ProvisionedThroughputExceededException', 'RequestLimitExceeded', 'RequestThrottled', 'RequestThrottledException', 'SlowDown', 'ThrottledException', 'Throttling', 'ThrottlingException', 'TooManyRequestsException'];\nconst TIMEOUT_ERROR_CODES = ['TimeoutError', 'RequestTimeout', 'RequestTimeoutException'];\nconst isThrottlingError = (statusCode, errorCode) => statusCode === 429 || !!errorCode && THROTTLING_ERROR_CODES.includes(errorCode);\nconst isConnectionError = error => [AmplifyErrorCode.NetworkError,\n// TODO(vNext): unify the error code `ERR_NETWORK` used by the Storage XHR handler\n'ERR_NETWORK'].includes(error?.name);\nconst isServerSideError = (statusCode, errorCode) => !!statusCode && [500, 502, 503, 504].includes(statusCode) || !!errorCode && TIMEOUT_ERROR_CODES.includes(errorCode);\nexport { getRetryDecider };", "map": {"version": 3, "names": ["AmplifyErrorCode", "isClockSkewError", "getRetryDecider", "error<PERSON><PERSON>er", "_ref", "_asyncToGenerator", "response", "error", "parsedError", "undefined", "errorCode", "code", "name", "statusCode", "isRetryable", "isConnectionError", "isThrottlingError", "isServerSideError", "retryable", "_x", "_x2", "apply", "arguments", "THROTTLING_ERROR_CODES", "TIMEOUT_ERROR_CODES", "includes", "NetworkError"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/retry/defaultRetryDecider.mjs"], "sourcesContent": ["import { AmplifyErrorCode } from '../../../types/errors.mjs';\nimport { isClockSkewError } from './isClockSkewError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Get retry decider function\n * @param errorParser Function to load JavaScript error from HTTP response\n */\nconst getRetryDecider = (errorParser) => async (response, error) => {\n    const parsedError = error ??\n        (await errorParser(response)) ??\n        undefined;\n    const errorCode = parsedError?.code || parsedError?.name;\n    const statusCode = response?.statusCode;\n    const isRetryable = isConnectionError(error) ||\n        isThrottlingError(statusCode, errorCode) ||\n        isClockSkewError(errorCode) ||\n        isServerSideError(statusCode, errorCode);\n    return {\n        retryable: isRetryable,\n    };\n};\n// reference: https://github.com/aws/aws-sdk-js-v3/blob/ab0e7be36e7e7f8a0c04834357aaad643c7912c3/packages/service-error-classification/src/constants.ts#L22-L37\nconst THROTTLING_ERROR_CODES = [\n    'BandwidthLimitExceeded',\n    'EC2ThrottledException',\n    'LimitExceededException',\n    'PriorRequestNotComplete',\n    'ProvisionedThroughputExceededException',\n    'RequestLimitExceeded',\n    'RequestThrottled',\n    'RequestThrottledException',\n    'SlowDown',\n    'ThrottledException',\n    'Throttling',\n    'ThrottlingException',\n    'TooManyRequestsException',\n];\nconst TIMEOUT_ERROR_CODES = [\n    'TimeoutError',\n    'RequestTimeout',\n    'RequestTimeoutException',\n];\nconst isThrottlingError = (statusCode, errorCode) => statusCode === 429 ||\n    (!!errorCode && THROTTLING_ERROR_CODES.includes(errorCode));\nconst isConnectionError = (error) => [\n    AmplifyErrorCode.NetworkError,\n    // TODO(vNext): unify the error code `ERR_NETWORK` used by the Storage XHR handler\n    'ERR_NETWORK',\n].includes(error?.name);\nconst isServerSideError = (statusCode, errorCode) => (!!statusCode && [500, 502, 503, 504].includes(statusCode)) ||\n    (!!errorCode && TIMEOUT_ERROR_CODES.includes(errorCode));\n\nexport { getRetryDecider };\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,wBAAwB;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAIC,WAAW;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAK,WAAOC,QAAQ,EAAEC,KAAK,EAAK;IAChE,MAAMC,WAAW,GAAGD,KAAK,WACdJ,WAAW,CAACG,QAAQ,CAAC,CAAC,IAC7BG,SAAS;IACb,MAAMC,SAAS,GAAGF,WAAW,EAAEG,IAAI,IAAIH,WAAW,EAAEI,IAAI;IACxD,MAAMC,UAAU,GAAGP,QAAQ,EAAEO,UAAU;IACvC,MAAMC,WAAW,GAAGC,iBAAiB,CAACR,KAAK,CAAC,IACxCS,iBAAiB,CAACH,UAAU,EAAEH,SAAS,CAAC,IACxCT,gBAAgB,CAACS,SAAS,CAAC,IAC3BO,iBAAiB,CAACJ,UAAU,EAAEH,SAAS,CAAC;IAC5C,OAAO;MACHQ,SAAS,EAAEJ;IACf,CAAC;EACL,CAAC;EAAA,iBAAAK,EAAA,EAAAC,GAAA;IAAA,OAAAhB,IAAA,CAAAiB,KAAA,OAAAC,SAAA;EAAA;AAAA;AACD;AACA,MAAMC,sBAAsB,GAAG,CAC3B,wBAAwB,EACxB,uBAAuB,EACvB,wBAAwB,EACxB,yBAAyB,EACzB,wCAAwC,EACxC,sBAAsB,EACtB,kBAAkB,EAClB,2BAA2B,EAC3B,UAAU,EACV,oBAAoB,EACpB,YAAY,EACZ,qBAAqB,EACrB,0BAA0B,CAC7B;AACD,MAAMC,mBAAmB,GAAG,CACxB,cAAc,EACd,gBAAgB,EAChB,yBAAyB,CAC5B;AACD,MAAMR,iBAAiB,GAAGA,CAACH,UAAU,EAAEH,SAAS,KAAKG,UAAU,KAAK,GAAG,IAClE,CAAC,CAACH,SAAS,IAAIa,sBAAsB,CAACE,QAAQ,CAACf,SAAS,CAAE;AAC/D,MAAMK,iBAAiB,GAAIR,KAAK,IAAK,CACjCP,gBAAgB,CAAC0B,YAAY;AAC7B;AACA,aAAa,CAChB,CAACD,QAAQ,CAAClB,KAAK,EAAEK,IAAI,CAAC;AACvB,MAAMK,iBAAiB,GAAGA,CAACJ,UAAU,EAAEH,SAAS,KAAM,CAAC,CAACG,UAAU,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACY,QAAQ,CAACZ,UAAU,CAAC,IAC1G,CAAC,CAACH,SAAS,IAAIc,mBAAmB,CAACC,QAAQ,CAACf,SAAS,CAAE;AAE5D,SAASR,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}