{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction urlSafeDecode(hex) {\n  const matchArr = hex.match(/.{2}/g) || [];\n  return matchArr.map(char => String.fromCharCode(parseInt(char, 16))).join('');\n}\nexport { urlSafeDecode };", "map": {"version": 3, "names": ["urlSafeDecode", "hex", "matchArr", "match", "map", "char", "String", "fromCharCode", "parseInt", "join"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/urlSafeDecode.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction urlSafeDecode(hex) {\n    const matchArr = hex.match(/.{2}/g) || [];\n    return matchArr.map(char => String.fromCharCode(parseInt(char, 16))).join('');\n}\n\nexport { urlSafeDecode };\n"], "mappings": "AAAA;AACA;AACA,SAASA,aAAaA,CAACC,GAAG,EAAE;EACxB,MAAMC,QAAQ,GAAGD,GAAG,CAACE,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE;EACzC,OAAOD,QAAQ,CAACE,GAAG,CAACC,IAAI,IAAIC,MAAM,CAACC,YAAY,CAACC,QAAQ,CAACH,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC,EAAE,CAAC;AACjF;AAEA,SAAST,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}