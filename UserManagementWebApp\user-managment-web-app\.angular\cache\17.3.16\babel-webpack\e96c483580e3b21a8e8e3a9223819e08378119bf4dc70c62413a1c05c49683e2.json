{"ast": null, "code": "import { defaultStorage } from '@aws-amplify/core';\nimport { DefaultOAuthStore } from '../signInWithRedirectStore.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst oAuthStore = new DefaultOAuthStore(defaultStorage);\nexport { oAuthStore };", "map": {"version": 3, "names": ["defaultStorage", "DefaultOAuthStore", "oAuthStore"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/oauth/oAuthStore.mjs"], "sourcesContent": ["import { defaultStorage } from '@aws-amplify/core';\nimport { DefaultOAuthStore } from '../signInWithRedirectStore.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst oAuthStore = new DefaultOAuthStore(defaultStorage);\n\nexport { oAuthStore };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mBAAmB;AAClD,SAASC,iBAAiB,QAAQ,gCAAgC;;AAElE;AACA;AACA,MAAMC,UAAU,GAAG,IAAID,iBAAiB,CAACD,cAAc,CAAC;AAExD,SAASE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}