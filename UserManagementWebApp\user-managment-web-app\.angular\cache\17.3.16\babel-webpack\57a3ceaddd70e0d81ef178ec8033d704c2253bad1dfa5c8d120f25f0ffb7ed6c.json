{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction isTokenExpired({\n  expiresAt,\n  clockDrift,\n  tolerance = 5000\n}) {\n  const currentTime = Date.now();\n  return currentTime + clockDrift + tolerance > expiresAt;\n}\nexport { isTokenExpired };", "map": {"version": 3, "names": ["isTokenExpired", "expiresAt", "clockDrift", "tolerance", "currentTime", "Date", "now"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/isTokenExpired.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction isTokenExpired({ expiresAt, clockDrift, tolerance = 5000, }) {\n    const currentTime = Date.now();\n    return currentTime + clockDrift + tolerance > expiresAt;\n}\n\nexport { isTokenExpired };\n"], "mappings": "AAAA;AACA;AACA,SAASA,cAAcA,CAAC;EAAEC,SAAS;EAAEC,UAAU;EAAEC,SAAS,GAAG;AAAM,CAAC,EAAE;EAClE,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAC9B,OAAOF,WAAW,GAAGF,UAAU,GAAGC,SAAS,GAAGF,SAAS;AAC3D;AAEA,SAASD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}