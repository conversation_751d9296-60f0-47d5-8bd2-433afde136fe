{"ast": null, "code": "const lineHeights = {\n  small: {\n    value: '1.25'\n  },\n  medium: {\n    value: '1.5'\n  },\n  large: {\n    value: '2'\n  }\n};\nexport { lineHeights };", "map": {"version": 3, "names": ["lineHeights", "small", "value", "medium", "large"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/lineHeights.mjs"], "sourcesContent": ["const lineHeights = {\n    small: { value: '1.25' },\n    medium: { value: '1.5' },\n    large: { value: '2' },\n};\n\nexport { lineHeights };\n"], "mappings": "AAAA,MAAMA,WAAW,GAAG;EAChBC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAO,CAAC;EACxBC,MAAM,EAAE;IAAED,KAAK,EAAE;EAAM,CAAC;EACxBE,KAAK,EAAE;IAAEF,KAAK,EAAE;EAAI;AACxB,CAAC;AAED,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}