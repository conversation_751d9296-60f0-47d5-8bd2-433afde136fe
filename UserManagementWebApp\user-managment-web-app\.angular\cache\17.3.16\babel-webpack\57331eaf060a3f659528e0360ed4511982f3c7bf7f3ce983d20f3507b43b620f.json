{"ast": null, "code": "import { createAssertionFunction } from '../errors/createAssertionFunction.mjs';\nimport '../types/errors.mjs';\nimport '../errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar I18nErrorCode;\n(function (I18nErrorCode) {\n  I18nErrorCode[\"NotConfigured\"] = \"NotConfigured\";\n})(I18nErrorCode || (I18nErrorCode = {}));\nconst i18nErrorMap = {\n  [I18nErrorCode.NotConfigured]: {\n    message: 'i18n is not configured.'\n  }\n};\nconst assert = createAssertionFunction(i18nErrorMap);\nexport { I18nErrorCode, assert };", "map": {"version": 3, "names": ["createAssertionFunction", "I18nErrorCode", "i18nErrorMap", "NotConfigured", "message", "assert"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/I18n/errorHelpers.mjs"], "sourcesContent": ["import { createAssertionFunction } from '../errors/createAssertionFunction.mjs';\nimport '../types/errors.mjs';\nimport '../errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar I18nErrorCode;\n(function (I18nErrorCode) {\n    I18nErrorCode[\"NotConfigured\"] = \"NotConfigured\";\n})(I18nErrorCode || (I18nErrorCode = {}));\nconst i18nErrorMap = {\n    [I18nErrorCode.NotConfigured]: {\n        message: 'i18n is not configured.',\n    },\n};\nconst assert = createAssertionFunction(i18nErrorMap);\n\nexport { I18nErrorCode, assert };\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,uCAAuC;AAC/E,OAAO,qBAAqB;AAC5B,OAAO,4BAA4B;;AAEnC;AACA;AACA,IAAIC,aAAa;AACjB,CAAC,UAAUA,aAAa,EAAE;EACtBA,aAAa,CAAC,eAAe,CAAC,GAAG,eAAe;AACpD,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC,MAAMC,YAAY,GAAG;EACjB,CAACD,aAAa,CAACE,aAAa,GAAG;IAC3BC,OAAO,EAAE;EACb;AACJ,CAAC;AACD,MAAMC,MAAM,GAAGL,uBAAuB,CAACE,YAAY,CAAC;AAEpD,SAASD,aAAa,EAAEI,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}