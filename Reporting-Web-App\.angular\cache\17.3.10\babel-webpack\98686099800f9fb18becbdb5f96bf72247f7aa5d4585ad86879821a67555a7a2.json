{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/SaltboxReporting/Reporting-Web-App/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { constants } from 'src/app/core/constants/constants';\nimport { CommunicationToken } from 'src/app/core/models/communication-service-type';\nimport { _isNumberValue } from '@angular/cdk/coercion';\nimport { UrlSub } from 'src/app/core/models/url-substitution';\nimport { Subscription } from 'rxjs';\nimport { take } from 'rxjs/operators';\nimport { ConnectorTypes, DynamicAssetTypes, ReportType } from 'src/app/core/enums/reporting.enums';\nimport { ActionableGridColumnType } from 'src/app/core/enums/actionable-grid-enums';\nimport { deepCopy } from 'src/app/core/functions/utility';\nimport { ColumnConfig } from 'src/app/core/models/column-config';\nimport { GridEventType, GridTargetType } from 'src/app/core/enums/grid-workflow';\nimport { FeatureFlag } from 'src/app/core/enums/feature-flag.enums';\nimport { DataView } from 'src/app/shared/models/data-view';\nimport { JsonDataTypes, JsonStringFormats } from 'src/app/shared/enums/json-schema.enum';\nimport { AssetDropdownGroup } from 'src/app/core/models/asset-dropdown-group';\nimport { NewDynamicAssetComponent } from '../../dynamic-form/new-dynamic-form/new-dynamic-asset/new-dynamic-asset.component';\nimport { DialogModule } from 'primeng/dialog';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { InputNumberModule } from 'primeng/inputnumber';\nimport { TableModule } from 'primeng/table';\nimport { LinkedChartsComponent } from '../linked-charts/linked-charts.component';\nimport { LinkedTilesComponent } from '../linked-tiles/linked-tiles.component';\nimport { ActionableGridConfigComponent } from '../actionable-grid-config/actionable-grid-config.component';\nimport { ConditionalFormsComponent } from '../conditional-forms/conditional-forms.component';\nimport { AccordionModule } from 'primeng/accordion';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { KeyFilterModule } from 'primeng/keyfilter';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { InputTextareaModule } from 'primeng/inputtextarea';\nimport { MessageModule } from 'primeng/message';\nimport { NgIf, NgFor, NgClass } from '@angular/common';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ButtonModule } from 'primeng/button';\nimport { ToolbarModule } from 'primeng/toolbar';\nimport { FormsModule } from '@angular/forms';\nimport { CalendarViewSettingsComponent } from '../calendar-view-settings/calendar-view-settings.component';\nimport { CalendarViewConfig } from 'src/app/core/models/calendar-view-config';\nimport { HelpLauncherComponent } from '../../shared/help-launcher/help-launcher.component';\nimport { HelpLinks } from 'src/app/core/enums/help-links.enum';\nimport { ConditionalFormattingComponent } from '../conditional-formatting/conditional-formatting.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/report-admin.service\";\nimport * as i2 from \"src/app/shared/services/datastores.service\";\nimport * as i3 from \"src/app/shared/services/notification.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"src/app/core/services/projects.service\";\nimport * as i6 from \"src/app/core/services/user.service\";\nimport * as i7 from \"src/app/core/services/user-favorite.service\";\nimport * as i8 from \"src/app/core/services/feature-flag.service\";\nimport * as i9 from \"src/app/core/services/crypto.service\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"primeng/toolbar\";\nimport * as i12 from \"primeng/button\";\nimport * as i13 from \"primeng/tooltip\";\nimport * as i14 from \"primeng/message\";\nimport * as i15 from \"primeng/inputtext\";\nimport * as i16 from \"primeng/inputtextarea\";\nimport * as i17 from \"primeng/keyfilter\";\nimport * as i18 from \"primeng/dropdown\";\nimport * as i19 from \"primeng/radiobutton\";\nimport * as i20 from \"primeng/checkbox\";\nimport * as i21 from \"primeng/accordion\";\nimport * as i22 from \"primeng/table\";\nimport * as i23 from \"primeng/inputnumber\";\nimport * as i24 from \"primeng/confirmdialog\";\nimport * as i25 from \"primeng/dialog\";\nimport * as i26 from \"src/app/core/services/communication.service\";\nconst _c0 = (a0, a1) => ({\n  \"border-400\": a0,\n  \"disabled\": a1\n});\nconst _c1 = a0 => ({\n  \"disabled\": a0\n});\nconst _c2 = () => ({\n  width: \"25vw\"\n});\nconst _c3 = () => ({\n  width: \"20vw\"\n});\nconst _c4 = () => ({\n  width: \"30vw\"\n});\nconst _c5 = (a0, a1) => ({\n  \"sb-icon-form\": a0,\n  \"sb-icon-screen\": a1\n});\nconst _c6 = a0 => ({\n  \"p-filled\": a0\n});\nconst _c7 = () => ({\n  \"width\": \"100%\"\n});\nconst _c8 = () => ({\n  \"width\": \"100px\"\n});\nconst _c9 = () => ({\n  updateOn: \"blur\"\n});\nfunction ReportSettingsComponent_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-help-launcher\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"helpLink\", ctx_r1.helpLink);\n    i0.ɵɵproperty(\"outlined\", false);\n  }\n}\nfunction ReportSettingsComponent_p_message_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-message\", 79);\n  }\n}\nfunction ReportSettingsComponent_div_29_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_3_0 = ctx_r1.parentGroup == null ? null : ctx_r1.parentGroup.dropdownDisplayName) !== null && tmp_3_0 !== undefined ? tmp_3_0 : \"None\", \" \");\n  }\n}\nfunction ReportSettingsComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"p-dropdown\", 80);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_div_29_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.parentGroup, $event) || (ctx_r1.parentGroup = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, ReportSettingsComponent_div_29_ng_template_2_Template, 1, 1, \"ng-template\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 82);\n    i0.ɵɵtext(4, \"Parent Group\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.parentGroup);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked)(\"options\", ctx_r1.groupTagOptions)(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, ctx_r1.projectIsLocked))(\"filter\", true);\n  }\n}\nfunction ReportSettingsComponent_div_30_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"p-radioButton\", 87);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_div_30_div_3_Template_p_radioButton_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedCategory, $event) || (ctx_r1.selectedCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 88);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputId\", category_r5.key)(\"value\", category_r5.key);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedCategory);\n    i0.ɵɵproperty(\"disabled\", category_r5.key === \"R\" || ctx_r1.projectIsLocked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", category_r5.key);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(category_r5.name);\n  }\n}\nfunction ReportSettingsComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"label\", 84);\n    i0.ɵɵtext(2, \"Report Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ReportSettingsComponent_div_30_div_3_Template, 4, 6, \"div\", 85);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.categories);\n  }\n}\nfunction ReportSettingsComponent_div_41_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"p-radioButton\", 92);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_div_41_div_3_Template_p_radioButton_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedConnectorType, $event) || (ctx_r1.selectedConnectorType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onClick\", function ReportSettingsComponent_div_41_div_3_Template_p_radioButton_onClick_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onConnectorTypeChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 93);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const connectorType_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputId\", connectorType_r7.key)(\"value\", connectorType_r7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedConnectorType);\n    i0.ɵɵproperty(\"disabled\", connectorType_r7.key === \"R\" || ctx_r1.projectIsLocked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", connectorType_r7.key);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(connectorType_r7.name);\n  }\n}\nfunction ReportSettingsComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"label\", 84);\n    i0.ɵɵtext(2, \"Connection Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ReportSettingsComponent_div_41_div_3_Template, 4, 6, \"div\", 90);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.connectorTypes);\n  }\n}\nfunction ReportSettingsComponent_ng_container_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 14)(2, \"p-dropdown\", 94);\n    i0.ɵɵlistener(\"onChange\", function ReportSettingsComponent_ng_container_43_Template_p_dropdown_onChange_2_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onConnectorChange());\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_ng_container_43_Template_p_dropdown_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedConnector, $event) || (ctx_r1.selectedConnector = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 95);\n    i0.ɵɵtext(4, \"Connection\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 14)(6, \"p-dropdown\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_ng_container_43_Template_p_dropdown_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedFunction, $event) || (ctx_r1.selectedFunction = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"label\", 97);\n    i0.ɵɵtext(8, \"Function\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r1.connectors);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedConnector);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked)(\"ngClass\", i0.ɵɵpureFunction1(8, _c1, ctx_r1.projectIsLocked));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r1.functions);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedFunction);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked)(\"ngClass\", i0.ɵɵpureFunction1(10, _c1, ctx_r1.projectIsLocked));\n  }\n}\nfunction ReportSettingsComponent_ng_container_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 14)(2, \"p-dropdown\", 98);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_ng_container_44_Template_p_dropdown_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedWorkflow, $event) || (ctx_r1.selectedWorkflow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r1.workflows);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedWorkflow);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked)(\"ngClass\", i0.ɵɵpureFunction1(4, _c1, ctx_r1.projectIsLocked));\n  }\n}\nfunction ReportSettingsComponent_ng_container_45_div_5_ng_template_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"p-button\", 108);\n    i0.ɵɵlistener(\"onClick\", function ReportSettingsComponent_ng_container_45_div_5_ng_template_2_div_3_Template_p_button_onClick_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const dataView_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.openAnalyticsFilterModal(ctx_r1.formMode.EDIT, dataView_r13));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p-button\", 109);\n    i0.ɵɵlistener(\"onClick\", function ReportSettingsComponent_ng_container_45_div_5_ng_template_2_div_3_Template_p_button_onClick_2_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const dataView_r13 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.deleteView(dataView_r13));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"text\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"text\", true);\n  }\n}\nfunction ReportSettingsComponent_ng_container_45_div_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 107)(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ReportSettingsComponent_ng_container_45_div_5_ng_template_2_div_3_Template, 3, 2, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataView_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(dataView_r13.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dataView_r13.viewId !== \"0\");\n  }\n}\nfunction ReportSettingsComponent_ng_container_45_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"p-dropdown\", 103);\n    i0.ɵɵlistener(\"onChange\", function ReportSettingsComponent_ng_container_45_div_5_Template_p_dropdown_onChange_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSelectedViewChange());\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_ng_container_45_div_5_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedDataView, $event) || (ctx_r1.selectedDataView = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, ReportSettingsComponent_ng_container_45_div_5_ng_template_2_Template, 4, 2, \"ng-template\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 105);\n    i0.ɵɵtext(4, \"View\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 48)(6, \"p-button\", 106);\n    i0.ɵɵlistener(\"onClick\", function ReportSettingsComponent_ng_container_45_div_5_Template_p_button_onClick_6_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openAnalyticsFilterModal(ctx_r1.formMode.CREATE));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r1.dataViews);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedDataView);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked)(\"filter\", true)(\"ngClass\", i0.ɵɵpureFunction1(7, _c1, ctx_r1.projectIsLocked));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"outlined\", true)(\"disabled\", ctx_r1.projectIsLocked);\n  }\n}\nfunction ReportSettingsComponent_ng_container_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 14)(2, \"p-dropdown\", 99);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_ng_container_45_Template_p_dropdown_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedDatastore, $event) || (ctx_r1.selectedDatastore = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function ReportSettingsComponent_ng_container_45_Template_p_dropdown_onChange_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSelectedDatastoreChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 100);\n    i0.ɵɵtext(4, \"Datastore\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, ReportSettingsComponent_ng_container_45_div_5_Template, 7, 9, \"div\", 101);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r1.datastores);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedDatastore);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked)(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, ctx_r1.projectIsLocked));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedDatastore);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 124);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c5, ctx_r1.selectedDynamicAsset.type === \"dynamicForm\", ctx_r1.selectedDynamicAsset.type === \"connectorApp\"));\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_2_span_1_Template, 2, 4, \"span\", 32);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.selectedDynamicAsset == null ? null : ctx_r1.selectedDynamicAsset.type) === \"dynamicForm\" || (ctx_r1.selectedDynamicAsset == null ? null : ctx_r1.selectedDynamicAsset.type) === \"connectorApp\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedDynamicAsset == null ? null : ctx_r1.selectedDynamicAsset.name, \" \");\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_3_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 126);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const group_r16 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", group_r16.label, \" \");\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_3_div_0_Template, 2, 1, \"div\", 125);\n  }\n  if (rf & 2) {\n    const group_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", group_r16.label);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 124);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const asset_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c5, asset_r17.type === \"dynamicForm\", asset_r17.type === \"connectorApp\"));\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵtemplate(1, ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_4_span_1_Template, 2, 4, \"span\", 32);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const asset_r17 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", asset_r17.type === \"dynamicForm\" || asset_r17.type === \"connectorApp\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", asset_r17.name, \" \");\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_46_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"p-dropdown\", 119);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_46_div_8_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedDynamicAsset, $event) || (ctx_r1.selectedDynamicAsset = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function ReportSettingsComponent_p_accordionTab_46_div_8_Template_p_dropdown_onChange_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onDynamicAssetChange());\n    });\n    i0.ɵɵtemplate(2, ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_2_Template, 3, 2, \"ng-template\", 81)(3, ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_3_Template, 1, 1, \"ng-template\", 120)(4, ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_4_Template, 3, 2, \"ng-template\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"label\", 121);\n    i0.ɵɵtext(6, \"Edit Mode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 48)(8, \"p-button\", 122);\n    i0.ɵɵlistener(\"onClick\", function ReportSettingsComponent_p_accordionTab_46_div_8_Template_p_button_onClick_8_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.getDatastoreAssets());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 48)(10, \"p-button\", 123);\n    i0.ɵɵlistener(\"onClick\", function ReportSettingsComponent_p_accordionTab_46_div_8_Template_p_button_onClick_10_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showDynamicAssetSettingsDialog());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r1.assetsInfoGroups);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedDynamicAsset);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked)(\"ngClass\", i0.ɵɵpureFunction1(8, _c1, ctx_r1.projectIsLocked));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"outlined\", true)(\"disabled\", ctx_r1.projectIsLocked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"outlined\", true)(\"disabled\", ctx_r1.projectIsLocked);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_46_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"p-dropdown\", 128);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_46_div_9_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.reportConfig.formatConfig.dynamicAssetInfo.idField, $event) || (ctx_r1.reportConfig.formatConfig.dynamicAssetInfo.idField = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 129);\n    i0.ɵɵtext(3, \"ID Field\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r1.idFieldOptions)(\"disabled\", ctx_r1.projectIsLocked);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.reportConfig.formatConfig.dynamicAssetInfo.idField);\n    i0.ɵɵproperty(\"autoDisplayFirst\", false)(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, ctx_r1.projectIsLocked));\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_46_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 111)(1, \"p-checkbox\", 130);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_46_div_14_Template_p_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.reportConfig.paginationAutoPaging, $event) || (ctx_r1.reportConfig.paginationAutoPaging = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.reportConfig.paginationAutoPaging);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_46_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"p-dropdown\", 131);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_46_div_15_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.reportConfig.defaultPageSize, $event) || (ctx_r1.reportConfig.defaultPageSize = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 132);\n    i0.ɵɵtext(3, \"Default Page Size\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r1.pageSizes)(\"disabled\", ctx_r1.projectIsLocked);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.reportConfig.defaultPageSize);\n    i0.ɵɵproperty(\"autoDisplayFirst\", false)(\"ngClass\", i0.ɵɵpureFunction1(5, _c1, ctx_r1.projectIsLocked));\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-accordionTab\", 110)(1, \"div\", 53)(2, \"div\", 111)(3, \"p-checkbox\", 112);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_46_Template_p_checkbox_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.reportConfig.allowAddNewRow, $event) || (ctx_r1.reportConfig.allowAddNewRow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 45)(5, \"p-dropdown\", 113);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_46_Template_p_dropdown_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedSavedEventWorkflow, $event) || (ctx_r1.selectedSavedEventWorkflow = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"label\", 114);\n    i0.ɵɵtext(7, \"Save Event Workflow\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, ReportSettingsComponent_p_accordionTab_46_div_8_Template, 11, 10, \"div\", 115)(9, ReportSettingsComponent_p_accordionTab_46_div_9_Template, 4, 7, \"div\", 115);\n    i0.ɵɵelementStart(10, \"app-conditional-forms\", 116);\n    i0.ɵɵtwoWayListener(\"reportConfigChange\", function ReportSettingsComponent_p_accordionTab_46_Template_app_conditional_forms_reportConfigChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.reportConfig, $event) || (ctx_r1.reportConfig = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"app-conditional-formatting\", 116);\n    i0.ɵɵtwoWayListener(\"reportConfigChange\", function ReportSettingsComponent_p_accordionTab_46_Template_app_conditional_formatting_reportConfigChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.reportConfig, $event) || (ctx_r1.reportConfig = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 111)(13, \"p-checkbox\", 117);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_46_Template_p_checkbox_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.reportConfig.enablePagination, $event) || (ctx_r1.reportConfig.enablePagination = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, ReportSettingsComponent_p_accordionTab_46_div_14_Template, 2, 2, \"div\", 118)(15, ReportSettingsComponent_p_accordionTab_46_div_15_Template, 4, 7, \"div\", 115);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selected\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.reportConfig.allowAddNewRow);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r1.workflows);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedSavedEventWorkflow);\n    i0.ɵɵproperty(\"showClear\", true)(\"disabled\", ctx_r1.projectIsLocked)(\"ngClass\", i0.ɵɵpureFunction1(26, _c1, ctx_r1.projectIsLocked));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedDataView);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.selectedDynamicAsset == null ? null : ctx_r1.selectedDynamicAsset.type) === \"connectorApp\");\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"reportConfig\", ctx_r1.reportConfig);\n    i0.ɵɵproperty(\"datastore\", ctx_r1.selectedDatastore)(\"projectId\", ctx_r1.projectId)(\"projectVersionId\", ctx_r1.projectVersionId)(\"disabled\", ctx_r1.projectIsLocked)(\"actionableGridConfig\", ctx_r1.selectedUserConfiguredFormat);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"reportConfig\", ctx_r1.reportConfig);\n    i0.ɵɵproperty(\"datastore\", ctx_r1.selectedDatastore)(\"projectId\", ctx_r1.projectId)(\"projectVersionId\", ctx_r1.projectVersionId)(\"disabled\", ctx_r1.projectIsLocked)(\"actionableGridConfig\", ctx_r1.selectedUserConfiguredFormat);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.reportConfig.enablePagination);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.reportConfig == null ? null : ctx_r1.reportConfig.enablePagination);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.reportConfig == null ? null : ctx_r1.reportConfig.enablePagination);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_47_p_dropdown_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dropdown\", 136);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_47_p_dropdown_3_Template_p_dropdown_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedQueryCategory, $event) || (ctx_r1.selectedQueryCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function ReportSettingsComponent_p_accordionTab_47_p_dropdown_3_Template_p_dropdown_onChange_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCategoryChange());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"options\", ctx_r1.queryCategory)(\"disabled\", ctx_r1.projectIsLocked);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedQueryCategory);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c1, ctx_r1.projectIsLocked));\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_47_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"input\", 137);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_47_div_6_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedQueryCategory, $event) || (ctx_r1.selectedQueryCategory = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 135);\n    i0.ɵɵtext(3, \"Query Category\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedQueryCategory);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_47_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"p-dropdown\", 138);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_47_div_7_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedQueryName, $event) || (ctx_r1.selectedQueryName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function ReportSettingsComponent_p_accordionTab_47_div_7_Template_p_dropdown_onChange_1_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.getColumnNames());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 139);\n    i0.ɵɵtext(3, \"Query Name\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked)(\"options\", ctx_r1.queryName);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedQueryName);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c1, ctx_r1.projectIsLocked));\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_47_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"input\", 140);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_47_div_8_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedQueryName, $event) || (ctx_r1.selectedQueryName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 139);\n    i0.ɵɵtext(3, \"Query Name\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedQueryName);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-accordionTab\", 133)(1, \"div\", 53)(2, \"div\", 14);\n    i0.ɵɵtemplate(3, ReportSettingsComponent_p_accordionTab_47_p_dropdown_3_Template, 1, 6, \"p-dropdown\", 134);\n    i0.ɵɵelementStart(4, \"label\", 135);\n    i0.ɵɵtext(5, \"Query Category\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ReportSettingsComponent_p_accordionTab_47_div_6_Template, 4, 2, \"div\", 20)(7, ReportSettingsComponent_p_accordionTab_47_div_7_Template, 4, 6, \"div\", 20)(8, ReportSettingsComponent_p_accordionTab_47_div_8_Template, 4, 2, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selected\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.categoryFunctionExists);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.categoryFunctionExists);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.queryFunctionExists);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.queryFunctionExists);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-accordionTab\", 141)(1, \"div\", 142)(2, \"p\");\n    i0.ɵɵtext(3, \" App roles with \\\"View\\\" level of access will provide read-only access to all visible fields in this grid. Field details for \\\"Edit\\\" and \\\"Edit & Delete\\\" access levels are managed below. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \" App permissions are managed within the project's \");\n    i0.ɵɵelementStart(6, \"a\", 143);\n    i0.ɵɵtext(7, \"App Settings \");\n    i0.ɵɵelement(8, \"i\", 144);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" page, in the Roles section. \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"app-actionable-grid-config\", 145, 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selected\", ctx_r1.showHelp);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"href\", ctx_r1.appSettingsUrl, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"columnsConfig\", ctx_r1.selectedUserConfiguredFormat)(\"projectVersionId\", ctx_r1.projectVersionId)(\"disabled\", ctx_r1.projectIsLocked);\n  }\n}\nfunction ReportSettingsComponent_Conditional_49_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-calendar-view-settings\", 147);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"calendarViewConfig\", ctx_r1.reportConfig == null ? null : ctx_r1.reportConfig.calendarViewConfig)(\"selectedDataView\", ctx_r1.selectedDataView)(\"baseSchema\", ctx_r1.selectedDatastore == null ? null : ctx_r1.selectedDatastore.baseSchema);\n  }\n}\nfunction ReportSettingsComponent_Conditional_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-accordionTab\", 36)(1, \"div\", 29)(2, \"div\", 22)(3, \"p-checkbox\", 146);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_Conditional_49_Template_p_checkbox_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.reportConfig.enableCalendarView, $event) || (ctx_r1.reportConfig.enableCalendarView = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function ReportSettingsComponent_Conditional_49_Template_p_checkbox_onChange_3_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onChangeCalendarView());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ReportSettingsComponent_Conditional_49_Conditional_4_Template, 1, 3, \"app-calendar-view-settings\", 147);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selected\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", ctx_r1.nameLabelMap == null ? null : ctx_r1.nameLabelMap.enableCalendarView);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.reportConfig.enableCalendarView);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked || !ctx_r1.hasDateColumn)(\"pTooltip\", ctx_r1.hasDateColumn ? null : \"No Date Field(s) in selected data\");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(4, (ctx_r1.reportConfig == null ? null : ctx_r1.reportConfig.enableCalendarView) && ctx_r1.selectedDatastore && ctx_r1.selectedDataView ? 4 : -1);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-accordionTab\", 148)(1, \"app-linked-tiles\", 149);\n    i0.ɵɵlistener(\"tilesListUpdated\", function ReportSettingsComponent_p_accordionTab_50_Template_app_linked_tiles_tilesListUpdated_1_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.tilesListUpdated($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selected\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"tilesConfig\", ctx_r1.tilesConfig)(\"projectId\", ctx_r1.projectId)(\"projectVersionId\", ctx_r1.projectVersionId)(\"datastoreId\", ctx_r1.selectedDatastore == null ? null : ctx_r1.selectedDatastore.id)(\"selectedDataViewId\", ctx_r1.selectedDataView == null ? null : ctx_r1.selectedDataView.viewId)(\"advancedJsonEditFlag\", ctx_r1.advancedJsonEditFlag)(\"disabled\", ctx_r1.projectIsLocked);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-accordionTab\", 150)(1, \"app-linked-charts\", 151);\n    i0.ɵɵlistener(\"linkedChartsUpdated\", function ReportSettingsComponent_p_accordionTab_51_Template_app_linked_charts_linkedChartsUpdated_1_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.chartListUpdated($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selected\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"chartConfigs\", ctx_r1.chartConfigs)(\"disabled\", ctx_r1.projectIsLocked);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_52_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"p-dropdown\", 157);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_52_div_6_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.chartSubType, $event) || (ctx_r1.chartSubType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 154);\n    i0.ɵɵtext(3, \"Sub Type\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r1.chartSubTypes);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.chartSubType);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked)(\"ngClass\", i0.ɵɵpureFunction1(4, _c1, ctx_r1.projectIsLocked));\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-accordionTab\", 152)(1, \"div\", 29)(2, \"div\", 14)(3, \"p-dropdown\", 153);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_52_Template_p_dropdown_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.chartType, $event) || (ctx_r1.chartType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function ReportSettingsComponent_p_accordionTab_52_Template_p_dropdown_onChange_3_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onChartChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"label\", 154);\n    i0.ɵɵtext(5, \"Chart Type\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ReportSettingsComponent_p_accordionTab_52_div_6_Template, 4, 6, \"div\", 20);\n    i0.ɵɵelementStart(7, \"div\", 14)(8, \"textarea\", 155);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_52_Template_textarea_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.chartConfigDetails, $event) || (ctx_r1.chartConfigDetails = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"label\", 154);\n    i0.ɵɵtext(10, \"Configuration Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"p-message\", 156);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selected\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"options\", ctx_r1.chartTypes);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.chartType);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked)(\"ngClass\", i0.ɵɵpureFunction1(9, _c1, ctx_r1.projectIsLocked));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.chartSubTypes);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.chartConfigDetails);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c6, (ctx_r1.chartConfigDetails == null ? null : ctx_r1.chartConfigDetails.length) > 0))(\"disabled\", ctx_r1.projectIsLocked);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_53_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2, \"Column\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Format Settings\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_53_ng_template_6_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 172)(1, \"p-inputNumber\", 173);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_53_ng_template_6_span_8_Template_p_inputNumber_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const rowData_r31 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(rowData_r31.decimalPlaces, $event) || (rowData_r31.decimalPlaces = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\");\n    i0.ɵɵtext(3, \"Dec. Points\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext();\n    const rowData_r31 = ctx_r32.$implicit;\n    const i_r34 = ctx_r32.rowIndex;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"name\", \"decimalPlaces\", i_r34, \"\");\n    i0.ɵɵproperty(\"showButtons\", true)(\"disabled\", (ctx_r1.selectedConnectorType == null ? null : ctx_r1.selectedConnectorType.key) === \"forms\" || ctx_r1.projectIsLocked);\n    i0.ɵɵtwoWayProperty(\"ngModel\", rowData_r31.decimalPlaces);\n    i0.ɵɵproperty(\"min\", 0)(\"max\", 100)(\"ngClass\", i0.ɵɵpureFunction1(8, _c1, (ctx_r1.selectedConnectorType == null ? null : ctx_r1.selectedConnectorType.key) === \"forms\"));\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_53_ng_template_6_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 174)(1, \"p-dropdown\", 175);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_53_ng_template_6_span_9_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const rowData_r31 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(rowData_r31.currency, $event) || (rowData_r31.currency = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\");\n    i0.ɵɵtext(3, \"Currency\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext();\n    const rowData_r31 = ctx_r32.$implicit;\n    const i_r34 = ctx_r32.rowIndex;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"name\", \"currency\", i_r34, \"\");\n    i0.ɵɵproperty(\"options\", ctx_r1.currencies);\n    i0.ɵɵtwoWayProperty(\"ngModel\", rowData_r31.currency);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked || (ctx_r1.selectedConnectorType == null ? null : ctx_r1.selectedConnectorType.key) === \"forms\")(\"ngClass\", i0.ɵɵpureFunction1(6, _c1, ctx_r1.projectIsLocked || (ctx_r1.selectedConnectorType == null ? null : ctx_r1.selectedConnectorType.key) === \"forms\"));\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_53_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 164)(1, \"td\", 165);\n    i0.ɵɵelement(2, \"input\", 166);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 167)(4, \"span\", 168)(5, \"p-dropdown\", 169);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_53_ng_template_6_Template_p_dropdown_ngModelChange_5_listener($event) {\n      const rowData_r31 = i0.ɵɵrestoreView(_r30).$implicit;\n      i0.ɵɵtwoWayBindingSet(rowData_r31.format, $event) || (rowData_r31.format = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function ReportSettingsComponent_p_accordionTab_53_ng_template_6_Template_p_dropdown_onChange_5_listener() {\n      const rowData_r31 = i0.ɵɵrestoreView(_r30).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.setDecimalPlaces(rowData_r31));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"label\");\n    i0.ɵɵtext(7, \"Format\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, ReportSettingsComponent_p_accordionTab_53_ng_template_6_span_8_Template, 4, 10, \"span\", 170)(9, ReportSettingsComponent_p_accordionTab_53_ng_template_6_span_9_Template, 4, 8, \"span\", 171);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const rowData_r31 = ctx.$implicit;\n    const i_r34 = ctx.rowIndex;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"pSelectableRow\", rowData_r31);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate2(\"name\", \"name\", i_r34, \"\", rowData_r31.name, \"\");\n    i0.ɵɵproperty(\"ngModel\", rowData_r31.displayName || rowData_r31.name)(\"disabled\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"format\", rowData_r31.name, \"\");\n    i0.ɵɵproperty(\"autoDisplayFirst\", false)(\"options\", ctx_r1.formats);\n    i0.ɵɵtwoWayProperty(\"ngModel\", rowData_r31.format);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c1, ctx_r1.projectIsLocked || (ctx_r1.selectedConnectorType == null ? null : ctx_r1.selectedConnectorType.key) === \"forms\"))(\"disabled\", ctx_r1.projectIsLocked || (ctx_r1.selectedConnectorType == null ? null : ctx_r1.selectedConnectorType.key) === \"forms\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (rowData_r31.format === \"numeric\" || rowData_r31.format === \"currency\") && (rowData_r31 == null ? null : rowData_r31.baseType) !== \"integer\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", rowData_r31.format === \"currency\");\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-accordionTab\", 158)(1, \"div\", 159)(2, \"div\", 160)(3, \"p-table\", 161, 2);\n    i0.ɵɵtemplate(5, ReportSettingsComponent_p_accordionTab_53_ng_template_5_Template, 5, 0, \"ng-template\", 162)(6, ReportSettingsComponent_p_accordionTab_53_ng_template_6_Template, 10, 17, \"ng-template\", 163);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selected\", ctx_r1.showHelp);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r1.formatColumns);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_54_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-toolbar\", 179);\n    i0.ɵɵelement(1, \"div\", 4);\n    i0.ɵɵelementStart(2, \"div\", 180)(3, \"p-button\", 181);\n    i0.ɵɵlistener(\"click\", function ReportSettingsComponent_p_accordionTab_54_ng_template_5_Template_p_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.clearSelectedDrillThroughs());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p-button\", 182);\n    i0.ɵɵlistener(\"click\", function ReportSettingsComponent_p_accordionTab_54_ng_template_5_Template_p_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addNew());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.selectedDrillThroughs.length <= 0)(\"outlined\", true)(\"rounded\", true)(\"disabled\", ctx_r1.projectIsLocked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"outlined\", true)(\"rounded\", true)(\"disabled\", ctx_r1.projectIsLocked);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_54_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 183)(1, \"th\", 184);\n    i0.ɵɵtext(2, \"Select\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 185);\n    i0.ɵɵtext(4, \"Display Column\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 186);\n    i0.ɵɵtext(6, \"URL Alias String\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 187);\n    i0.ɵɵtext(8, \"URL Substitutions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 188);\n    i0.ɵɵtext(10, \"Open In\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 189);\n    i0.ɵɵtext(12, \"Grid Generated Link\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"th\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_54_ng_template_7_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 202)(1, \"td\", 203)(2, \"div\", 204)(3, \"input\", 205);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_54_ng_template_7_ng_template_16_Template_input_ngModelChange_3_listener($event) {\n      const rowdata_r40 = i0.ɵɵrestoreView(_r39).$implicit;\n      i0.ɵɵtwoWayBindingSet(rowdata_r40.urlName, $event) || (rowdata_r40.urlName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"focusout\", function ReportSettingsComponent_p_accordionTab_54_ng_template_7_ng_template_16_Template_input_focusout_3_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const drillThrough_r38 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSubstitutionChange(drillThrough_r38));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"label\", 93);\n    i0.ɵɵtext(5, \"Parameter\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 48)(7, \"p-button\", 206);\n    i0.ɵɵlistener(\"onClick\", function ReportSettingsComponent_p_accordionTab_54_ng_template_7_ng_template_16_Template_p_button_onClick_7_listener() {\n      const j_r41 = i0.ɵɵrestoreView(_r39).rowIndex;\n      const ctx_r41 = i0.ɵɵnextContext();\n      const drillThrough_r38 = ctx_r41.$implicit;\n      const i_r43 = ctx_r41.rowIndex;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectUrlSubstitution(drillThrough_r38.urlSubstitution, i_r43, j_r41));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"span\", 48)(9, \"p-button\", 207);\n    i0.ɵɵlistener(\"onClick\", function ReportSettingsComponent_p_accordionTab_54_ng_template_7_ng_template_16_Template_p_button_onClick_9_listener() {\n      const j_r41 = i0.ɵɵrestoreView(_r39).rowIndex;\n      const drillThrough_r38 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteSubstitution(drillThrough_r38.urlSubstitution, j_r41));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"span\", 48)(11, \"p-button\", 208);\n    i0.ɵɵlistener(\"onClick\", function ReportSettingsComponent_p_accordionTab_54_ng_template_7_ng_template_16_Template_p_button_onClick_11_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const drillThrough_r38 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addSubstitution(drillThrough_r38.urlSubstitution));\n    });\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const rowdata_r40 = ctx.$implicit;\n    const j_r41 = ctx.rowIndex;\n    const i_r43 = i0.ɵɵnextContext().rowIndex;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(26, _c8));\n    i0.ɵɵpropertyInterpolate2(\"name\", \"urlSubstitution\", i_r43, \"\", j_r41, \"\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", rowdata_r40.urlName);\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(27, _c9))(\"disabled\", ctx_r1.projectIsLocked);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate2(\"for\", \"urlSubstitution\", i_r43, \"\", j_r41, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate2(\"name\", \"deleteSubstitution\", i_r43, \"\", j_r41, \"\");\n    i0.ɵɵproperty(\"outlined\", true)(\"disabled\", ctx_r1.projectIsLocked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate2(\"name\", \"deleteSubstitution\", i_r43, \"\", j_r41, \"\");\n    i0.ɵɵproperty(\"outlined\", true)(\"disabled\", ctx_r1.projectIsLocked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate2(\"name\", \"addSubstitution\", i_r43, \"\", j_r41, \"\");\n    i0.ɵɵproperty(\"outlined\", true)(\"disabled\", ctx_r1.projectIsLocked);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_54_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 190)(1, \"td\")(2, \"div\", 22)(3, \"p-checkbox\", 191);\n    i0.ɵɵlistener(\"onChange\", function ReportSettingsComponent_p_accordionTab_54_ng_template_7_Template_p_checkbox_onChange_3_listener($event) {\n      const drillThrough_r38 = i0.ɵɵrestoreView(_r37).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.drillThroughCheckedChange($event.checked, drillThrough_r38));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(4, \"td\", 192)(5, \"span\", 14)(6, \"p-dropdown\", 193);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_54_ng_template_7_Template_p_dropdown_ngModelChange_6_listener($event) {\n      const drillThrough_r38 = i0.ɵɵrestoreView(_r37).$implicit;\n      i0.ɵɵtwoWayBindingSet(drillThrough_r38.displayColumn, $event) || (drillThrough_r38.displayColumn = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"label\", 93);\n    i0.ɵɵtext(8, \"Display Columns\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"td\", 192)(10, \"span\", 14)(11, \"input\", 194);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_54_ng_template_7_Template_input_ngModelChange_11_listener($event) {\n      const drillThrough_r38 = i0.ɵɵrestoreView(_r37).$implicit;\n      i0.ɵɵtwoWayBindingSet(drillThrough_r38.urlAlias, $event) || (drillThrough_r38.urlAlias = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"focusout\", function ReportSettingsComponent_p_accordionTab_54_ng_template_7_Template_input_focusout_11_listener() {\n      const drillThrough_r38 = i0.ɵɵrestoreView(_r37).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onSubstitutionChange(drillThrough_r38));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"label\", 93)(13, \"p-message\", 195);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\", 196)(15, \"p-table\", 197);\n    i0.ɵɵtemplate(16, ReportSettingsComponent_p_accordionTab_54_ng_template_7_ng_template_16_Template, 12, 28, \"ng-template\", 163);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"td\", 192)(18, \"span\", 14)(19, \"p-dropdown\", 198);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_54_ng_template_7_Template_p_dropdown_ngModelChange_19_listener($event) {\n      const drillThrough_r38 = i0.ɵɵrestoreView(_r37).$implicit;\n      i0.ɵɵtwoWayBindingSet(drillThrough_r38.openIn, $event) || (drillThrough_r38.openIn = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"label\", 93);\n    i0.ɵɵtext(21, \"Value\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"td\", 199);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"td\", 200)(25, \"p-button\", 201);\n    i0.ɵɵlistener(\"click\", function ReportSettingsComponent_p_accordionTab_54_ng_template_7_Template_p_button_click_25_listener() {\n      const drillThrough_r38 = i0.ɵɵrestoreView(_r37).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteProduct(drillThrough_r38));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const drillThrough_r38 = ctx.$implicit;\n    const i_r43 = ctx.rowIndex;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"name\", \"drillThrough-\", i_r43, \"\");\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", ctx_r1.projectIsLocked);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(36, _c7));\n    i0.ɵɵpropertyInterpolate1(\"name\", \"displayColumn\", i_r43, \"\");\n    i0.ɵɵproperty(\"options\", ctx_r1.urlColumns);\n    i0.ɵɵtwoWayProperty(\"ngModel\", drillThrough_r38.displayColumn);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked)(\"ngClass\", i0.ɵɵpureFunction1(37, _c1, ctx_r1.projectIsLocked));\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"for\", \"displayColumn\", i_r43, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(39, _c7));\n    i0.ɵɵpropertyInterpolate1(\"name\", \"urlAlias\", i_r43, \"\");\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked);\n    i0.ɵɵtwoWayProperty(\"ngModel\", drillThrough_r38.urlAlias);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"for\", \"urlAlias\", i_r43, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", drillThrough_r38.urlSubstitution);\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(40, _c7));\n    i0.ɵɵpropertyInterpolate1(\"name\", \"openIn\", i_r43, \"\");\n    i0.ɵɵproperty(\"options\", ctx_r1.openInValues);\n    i0.ɵɵtwoWayProperty(\"ngModel\", drillThrough_r38.openIn);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked)(\"ngClass\", i0.ɵɵpureFunction1(41, _c1, ctx_r1.projectIsLocked));\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"for\", \"openIn\", i_r43, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(drillThrough_r38.gridGeneratedLink);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"text\", true)(\"disabled\", ctx_r1.projectIsLocked);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-accordionTab\", 176)(1, \"div\", 159)(2, \"div\", 160)(3, \"p-table\", 177, 2);\n    i0.ɵɵtemplate(5, ReportSettingsComponent_p_accordionTab_54_ng_template_5_Template, 5, 7, \"ng-template\", 178)(6, ReportSettingsComponent_p_accordionTab_54_ng_template_6_Template, 14, 0, \"ng-template\", 162)(7, ReportSettingsComponent_p_accordionTab_54_ng_template_7_Template, 26, 43, \"ng-template\", 163);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selected\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r1.drillThroughs);\n  }\n}\nfunction ReportSettingsComponent_Conditional_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-accordionTab\", 42)(1, \"div\", 53);\n    i0.ɵɵelement(2, \"div\", 22);\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"textarea\", 209);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_Conditional_55_Template_textarea_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.agentAssetConfig.instructions, $event) || (ctx_r1.agentAssetConfig.instructions = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"label\", 210);\n    i0.ɵɵtext(6, \"Agent Instructions\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 14)(8, \"textarea\", 211);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_Conditional_55_Template_textarea_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.agentAssetConfig.schema, $event) || (ctx_r1.agentAssetConfig.schema = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"label\", 212);\n    i0.ɵɵtext(10, \"Sample JSON\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 14)(12, \"input\", 213);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_Conditional_55_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.agentAssetConfig.workflowId, $event) || (ctx_r1.agentAssetConfig.workflowId = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"label\", 214);\n    i0.ɵɵtext(14, \"Workflow ID to Run\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 14)(16, \"input\", 215);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_Conditional_55_Template_input_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.agentAssetConfig.startMessage, $event) || (ctx_r1.agentAssetConfig.startMessage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 216);\n    i0.ɵɵtext(18, \"Chat Window Starting Message\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selected\", false);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"autoResize\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.agentAssetConfig.instructions);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"autoResize\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.agentAssetConfig.schema);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.agentAssetConfig.workflowId);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.agentAssetConfig.startMessage);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked);\n  }\n}\nfunction ReportSettingsComponent_p_button_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 217);\n    i0.ɵɵlistener(\"onClick\", function ReportSettingsComponent_p_button_72_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openAssetModal());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked);\n  }\n}\nfunction ReportSettingsComponent_p_accordionTab_91_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-accordionTab\", 218)(1, \"div\", 53)(2, \"div\", 14);\n    i0.ɵɵelement(3, \"p-confirmDialog\");\n    i0.ɵɵelementStart(4, \"input\", 219);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_p_accordionTab_91_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedEmailLimit, $event) || (ctx_r1.selectedEmailLimit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keypress\", function ReportSettingsComponent_p_accordionTab_91_Template_input_keypress_4_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.numberOnly($event));\n    })(\"blur\", function ReportSettingsComponent_p_accordionTab_91_Template_input_blur_4_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.checkTimeOutValid());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"label\", 220);\n    i0.ɵɵtext(6, \"Email per minute\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selected\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(5, _c2));\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedEmailLimit);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.projectIsLocked);\n  }\n}\nfunction ReportSettingsComponent_ng_template_96_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 221);\n    i0.ɵɵlistener(\"onClick\", function ReportSettingsComponent_ng_template_96_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteReport());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"p-button\", 222);\n    i0.ɵɵlistener(\"onClick\", function ReportSettingsComponent_ng_template_96_Template_p_button_onClick_1_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.displayModal = false);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disableButton);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"outlined\", true);\n  }\n}\nfunction ReportSettingsComponent_div_110_ng_template_2_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedColumn.label);\n  }\n}\nfunction ReportSettingsComponent_div_110_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ReportSettingsComponent_div_110_ng_template_2_div_0_Template, 3, 1, \"div\", 32);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedColumn);\n  }\n}\nfunction ReportSettingsComponent_div_110_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const columnName_r49 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", columnName_r49.label, \" \");\n  }\n}\nfunction ReportSettingsComponent_div_110_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"p-dropdown\", 223);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_div_110_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedColumn, $event) || (ctx_r1.selectedColumn = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, ReportSettingsComponent_div_110_ng_template_2_Template, 1, 1, \"ng-template\", 81)(3, ReportSettingsComponent_div_110_ng_template_3_Template, 2, 1, \"ng-template\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"label\");\n    i0.ɵɵtext(5, \"Column Name\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r1.urlColumns);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedColumn);\n    i0.ɵɵproperty(\"filter\", true)(\"showClear\", true);\n  }\n}\nfunction ReportSettingsComponent_div_111_ng_template_2_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedProjectVariable.label);\n  }\n}\nfunction ReportSettingsComponent_div_111_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ReportSettingsComponent_div_111_ng_template_2_div_0_Template, 3, 1, \"div\", 32);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedProjectVariable);\n  }\n}\nfunction ReportSettingsComponent_div_111_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"span\", 224);\n    i0.ɵɵelement(3, \"i\", 225);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const projectVariable_r51 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", projectVariable_r51.label, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pTooltip\", projectVariable_r51.title);\n  }\n}\nfunction ReportSettingsComponent_div_111_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"p-dropdown\", 223);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_div_111_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedProjectVariable, $event) || (ctx_r1.selectedProjectVariable = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtemplate(2, ReportSettingsComponent_div_111_ng_template_2_Template, 1, 1, \"ng-template\", 81)(3, ReportSettingsComponent_div_111_ng_template_3_Template, 4, 2, \"ng-template\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"label\");\n    i0.ɵɵtext(5, \"Project Variable\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r1.urlProjectVariables);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedProjectVariable);\n    i0.ɵɵproperty(\"filter\", true)(\"showClear\", true);\n  }\n}\nfunction ReportSettingsComponent_ng_template_112_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 226);\n    i0.ɵɵlistener(\"click\", function ReportSettingsComponent_ng_template_112_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyURLSubstitution());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"p-button\", 227);\n    i0.ɵɵlistener(\"click\", function ReportSettingsComponent_ng_template_112_Template_p_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.displayURLSubstitution = false);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"outlined\", true);\n  }\n}\nexport class ReportSettingsComponent {\n  get hasDateColumn() {\n    return this.selectedUserConfiguredFormat?.some(col => col.format.type === ActionableGridColumnType.Date || col.format.type === ActionableGridColumnType.DateTime) ?? false;\n  }\n  constructor(reportadminservice, datastoresService, messageSvc, confirmationService, renderer, ngZone, projectsService, userService, userFavoriteService, featureFlagService, changeDetectorRef, cryptoService, communicationService) {\n    this.reportadminservice = reportadminservice;\n    this.datastoresService = datastoresService;\n    this.messageSvc = messageSvc;\n    this.confirmationService = confirmationService;\n    this.renderer = renderer;\n    this.ngZone = ngZone;\n    this.projectsService = projectsService;\n    this.userService = userService;\n    this.userFavoriteService = userFavoriteService;\n    this.featureFlagService = featureFlagService;\n    this.changeDetectorRef = changeDetectorRef;\n    this.cryptoService = cryptoService;\n    this.communicationService = communicationService;\n    this.projectIsLocked = false;\n    this.reportConfigOut = new EventEmitter();\n    this.paramListUpdated = new EventEmitter();\n    // Used for html conditions\n    this.reportType = ReportType;\n    this.formMode = constants.Mode;\n    this.selectedConnectorType = null;\n    this.connectors = [];\n    this.workflows = [{\n      name: 'No Workflow Selected',\n      id: null,\n      workflowIdentity: null\n    }];\n    this.queryCategory = [];\n    this.queryName = [];\n    this.functions = [];\n    this.showReportTester = false;\n    this.showHistoryDialog = false;\n    this.isReportLoaded = false;\n    this.showEmailSettings = false;\n    this.isDleSpecsLoaded = false;\n    this.messagePermissionRole = ['Integration Operator'];\n    this.dirFilterRegExp = /^[^<>*:'\"?\\\\[\\]|/]+$/;\n    this.canDelete = false;\n    this.users = [];\n    this.selectedUser = '0';\n    this.allowAnonymous = false;\n    this.user = [];\n    this.isSaveDisabled = false;\n    this.tilesConfig = [];\n    this.chartConfigs = []; // for visualizations\n    this.actionableGridColumnType = ActionableGridColumnType;\n    this.dataViews = [];\n    // defaultFormatSetting is the exact list that we have on baseSchema\n    // the purpose is not to convert baseSchema to ActionableGridColumnConfig[] over and over\n    this.cachedFormatSettings = {};\n    // to cache the settings\n    this.cachedUserConfiguredFormats = [];\n    this.selectedUserConfiguredFormat = [];\n    this.reportSettings = {\n      queryCategory: '',\n      queryName: '',\n      connectionType: '',\n      projectId: '',\n      reportId: '',\n      reportType: '',\n      reportName: '',\n      description: '',\n      url: '',\n      htmlCode: '',\n      isActive: false,\n      tenantId: '',\n      reportGroupId: '',\n      companyName: '',\n      connectorconfig: {\n        connector: '',\n        remoteFunction: ''\n      },\n      workFLowsettings: {\n        workFlowName: ''\n      },\n      anonymousUser: {\n        email: '',\n        id: 0,\n        tenantId: 0,\n        userName: '',\n        userTypeId: 0,\n        userId: 0\n      },\n      gridEvents: [],\n      allowAnonymous: false,\n      selectedUser: 0,\n      connectionTimeOut: 0,\n      anonymousAccessId: '',\n      chartType: '',\n      chartSubType: '',\n      chartConfigDetails: '',\n      emailLimit: 0,\n      goldenArrowSetup: [],\n      categoryFunction: '',\n      formatConfig: {\n        id: 0,\n        configId: '',\n        reportId: '',\n        config: {}\n      },\n      dataSourceInfo: {\n        id: 0,\n        dataSource: '',\n        formName: '',\n        connectorInfo: {},\n        queryCategory: '',\n        queryName: ''\n      },\n      queryColumnsFunction: '',\n      allowAddNewRow: false,\n      enablePagination: true,\n      paginationAutoPaging: false,\n      defaultPageSize: 20,\n      enableCalendarView: false\n    };\n    this.defaultDrillThrough = {\n      displayColumn: '',\n      openIn: '0',\n      urlAlias: '',\n      urlSubstitution: [new UrlSub()],\n      gridGeneratedLink: ''\n    };\n    this.drillThroughs = [this.defaultDrillThrough];\n    this.selectedDrillThroughs = [];\n    this.openInValues = [{\n      code: 'newWindow',\n      name: 'New Window'\n    }, {\n      code: 'currentWindow',\n      name: 'Current Window'\n    }];\n    this.nameLabelMap = {\n      name: 'Report Name',\n      description: 'Description',\n      active: 'Active',\n      success: 'Success',\n      failed: 'Failed',\n      connectors: 'Connectors',\n      queryCategory: 'B1 Query Category',\n      queryName: 'B1 Query Name',\n      LinkToEmbed: 'Link to embed this report',\n      HtmlToPaste: 'HTML to paste in a website',\n      enableCalendarView: 'Enable calendar view'\n    };\n    this.fieldTooltipMap = {\n      name: '',\n      description: '',\n      active: 'Active workflows are executed when the Project Engine is running.',\n      apiEnabled: 'Allows Saltbox API Gateway to interact with this workflow.',\n      success: 'Message will be retained for X days upon success.',\n      failed: 'Message will be retained for X days upon failure'\n    };\n    this.columns = [];\n    this.formatColumns = [];\n    this.columnsToShow = [];\n    this.currencies = constants.currencies;\n    this.formats = constants.formats;\n    this.categories = [{\n      name: 'Grid',\n      key: 'grid'\n    }, {\n      name: 'Pivot',\n      key: 'pivot'\n    }];\n    this.connectorTypes = [{\n      name: 'Connector',\n      key: 'connector'\n    }, {\n      name: 'Saltbox Datastore',\n      key: 'forms'\n    }];\n    this.chartTypes = [{\n      name: 'Line',\n      key: 'line',\n      config: 'A line chart requires a dataset which includes a minimum of two columns for the x and y axis. The first column will contain the x axis data, all subsequent columns will contain y axis data. Labels will be defaulted based on the column names in the dataset.'\n    }, {\n      name: 'Bar',\n      key: 'bar',\n      subTypes: [{\n        name: 'Stacked',\n        key: 'stacked'\n      }, {\n        name: 'Grouped',\n        key: 'grouped'\n      }, {\n        name: 'Normalized',\n        key: 'normalized'\n      }],\n      config: 'A bar chart requires a dataset which includes a minimum of two columns for the x and y axis. The first column will contain the x axis data, all subsequent columns will contain y axis data. Labels will be defaulted based on the column names in the dataset.'\n    }, {\n      name: 'Column',\n      key: 'column',\n      subTypes: [{\n        name: 'Stacked',\n        key: 'stacked'\n      }, {\n        name: 'Grouped',\n        key: 'grouped'\n      }, {\n        name: 'Normalized',\n        key: 'normalized'\n      }],\n      config: 'A column chart requires a dataset which includes a minimum of two columns for the x and y axis. The first column will contain the x axis data, all subsequent columns will contain y axis data. Labels will be defaulted based on the column names in the dataset.'\n    }, {\n      name: 'Histogram',\n      key: 'histogram',\n      config: 'A histogram requires a dataset which includes a single data column. The x axis will plot the data in the dataset and the y access will plot the number of occurrences. Labels will be defaulted based on the column name in the dataset.'\n    }, {\n      name: 'Area',\n      key: 'area',\n      subTypes: [{\n        name: 'Series',\n        key: 'series'\n      }, {\n        name: 'Grouped',\n        key: 'grouped'\n      }, {\n        name: 'Normalized',\n        key: 'normalized'\n      }],\n      config: 'An Area chart requires a dataset which includes a minimum of two columns for the x and y axis. The first column will contain the x axis data, all subsequent columns will contain y axis data. Labels will be defaulted based on the column names in the dataset.'\n    }, {\n      name: 'Pie',\n      key: 'pie',\n      subTypes: [{\n        name: 'Regular',\n        key: 'regular'\n      }, {\n        name: 'Rose',\n        key: 'rose'\n      }],\n      config: 'A \"regular\" Pie chart requires a dataset that includes two columns.  The first column will be used for the slice labels and the second column will contain the values.\\n A \"rose\" pie chart requires a dataset that includes three columns. The first column will be used for slice labels, the second column for the values, and the third column for the size data.'\n    }, {\n      name: 'Treemap',\n      key: 'treemap',\n      config: 'A Treemap chart requires a dataset which includes a minimum of two columns. The first column will contain the labels and the second column will contain the numeric value. '\n    }];\n    this.standaloneChartfeatuerFlag = false;\n    this.datastores = [];\n    this.minimunCache = false;\n    this.categoryFunctionExists = false;\n    this.queryFunctionExists = false;\n    this.reportColumnCount = 0;\n    this.displayURLSubstitution = false;\n    this.urlSubType = 'column';\n    this.urlColumns = [];\n    this.urlProjectVariables = [];\n    this.dataViewDeleteMessage = '';\n    this.showDynamicAssetSettings = false;\n    this.interAppSubscription = new Subscription();\n    this.allReportFavorites = [];\n    this.assetsInfoGroups = [];\n    this.defaultAsset = {\n      name: \"Auto-Generated from Grid Data\",\n      id: null,\n      type: DynamicAssetTypes.Default,\n      description: ''\n    };\n    this.selectedDynamicAsset = this.defaultAsset;\n    this.idFieldOptions = [];\n    this.parentGroup = null;\n    this.existingAssetTagGroups = [];\n    this.groupTagFlag = false;\n    this.helpLink = HelpLinks.ActionableGrids;\n    this.gridAgentChatFlag = false;\n    this.pageSizes = [{\n      key: '20',\n      value: 20\n    }, {\n      key: '50',\n      value: 50\n    }, {\n      key: '100',\n      value: 100\n    }];\n    this.reportConfig = {\n      queryCategory: '',\n      queryName: '',\n      connectionType: '',\n      projectId: '',\n      reportId: '',\n      reportType: '',\n      reportName: '',\n      description: '',\n      url: '',\n      htmlCode: '',\n      isActive: false,\n      tenantId: '',\n      reportGroupId: '',\n      companyName: '',\n      connectorconfig: {\n        connector: '',\n        remoteFunction: ''\n      },\n      workFLowsettings: {\n        workFlowName: ''\n      },\n      anonymousUser: {\n        email: '',\n        id: 0,\n        tenantId: 0,\n        userName: '',\n        userTypeId: 0,\n        userId: 0\n      },\n      gridEvents: [],\n      allowAnonymous: false,\n      selectedUser: 0,\n      connectionTimeOut: 0,\n      anonymousAccessId: '',\n      chartType: '',\n      chartSubType: '',\n      chartConfigDetails: '',\n      emailLimit: 0,\n      categoryFunction: '',\n      queryColumnsFunction: '',\n      environment: '',\n      maxColumnCount: 0,\n      allowAddNewRow: false,\n      enablePagination: true,\n      paginationAutoPaging: false,\n      defaultPageSize: 20\n    };\n  }\n  ngOnInit() {\n    this.initSettings();\n  }\n  initSettings() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.subscribeToInterAppCommunication();\n      _this.tempReportConfig = JSON.stringify(_this.reportConfig);\n      _this.getFeatureAssignment();\n      yield _this.setFeatureFlags();\n      _this.getConnectors(_this.reportConfig?.projectId ? _this.reportConfig.projectVersionId : 0);\n      _this.getWorkFlows(_this.reportConfig?.projectId ? _this.reportConfig.projectVersionId : 0);\n      _this.getDatastores(_this.reportConfig?.projectId, _this.reportConfig?.projectVersionId);\n      _this.setGroupTagsSubscriptions();\n      _this.communicationService.getGroupTagsList(_this.projectVersionId);\n      _this.getVisualizations();\n      _this.selectedCategory = _this.reportConfig?.reportType ? _this.reportConfig.reportType : _this.categories[0];\n      if (_this.reportConfig.reportType === ReportType.ActionableGrid) {\n        _this.selectedConnectorType = _this.connectorTypes.find(p => p.key === ConnectorTypes.Datastore);\n      } else {\n        _this.selectedConnectorType = _this.connectorTypes.find(p => p.key === _this.reportConfig.connectionType);\n      }\n      if (_this.reportConfig?.queryCategory || _this.reportConfig?.queryCategory === '') {\n        _this.selectedQueryCategory = _this.reportConfig?.queryCategory === '' ? '0' : _this.reportConfig?.queryCategory;\n      }\n      if (_this.reportConfig?.queryName) {\n        _this.selectedQueryName = _this.reportConfig?.queryName;\n      }\n      if (_this.reportConfig?.connectionTimeOut) {\n        _this.selectedConnectionTimeOut = _this.reportConfig?.connectionTimeOut;\n      } else {\n        _this.selectedConnectionTimeOut = 300;\n      }\n      _this.linkToEmbed = _this.reportConfig?.url;\n      _this.htmlToPaste = _this.reportConfig?.htmlCode;\n      _this.selectedWorkflow = _this.reportConfig?.workFLowsettings.workFlowName;\n      _this.selectedConnector = _this.reportConfig?.connectorconfig.configName;\n      _this.allowAnonymous = _this.reportConfig.allowAnonymous;\n      _this.chartType = _this.reportConfig.chartType;\n      if (_this.chartType) {\n        const Info = _this.chartTypes.find(p => p.key === _this.chartType);\n        _this.chartSubTypes = Info.subTypes;\n        _this.chartSubType = _this.reportConfig.chartSubType;\n        _this.chartConfigDetails = Info.config;\n      }\n      if (_this.reportConfig?.emailLimit) {\n        _this.selectedEmailLimit = _this.reportConfig?.emailLimit;\n      } else {\n        _this.selectedEmailLimit = 10;\n      }\n      _this.getUsers(_this.reportConfig);\n      _this.userService.getUserRole(_this.projectVersionId).then(userRole => {\n        _this.canDelete = userRole === constants.userRoles.manager;\n        _this.hasManagerPermissions = userRole === constants.userRoles.manager;\n      }).catch(error => {\n        console.log('Error getting user role: ${error}');\n        throw error;\n      });\n      _this.setTempQueryDetails(_this.reportConfig);\n      // if connector type is not forms, load column formats(B1WebApi)\n      if (_this.selectedConnectorType?.key !== ConnectorTypes.Datastore) {\n        _this.getColumnNames();\n      }\n      _this.drillThroughs = _this.reportConfig.goldenArrowSetup;\n      _this.drillThroughs.forEach(drillThrough => {\n        _this.onSubstitutionChange(drillThrough);\n      });\n      _this.geProjectVariables();\n      _this.buildBreadcrumb();\n      _this.trackUserFavorites();\n      _this.trackReportParameterChange();\n      _this.appSettingsUrl = `/project/${_this.projectId}/${_this.projectVersionId}/appsettings`;\n    })();\n  }\n  ngOnDestroy() {\n    this.groupTagSub?.unsubscribe();\n    this.groupTagsSub?.unsubscribe();\n    this.interAppSubscription?.unsubscribe();\n    this.userFavoriteSub?.unsubscribe();\n    this.onReportParameterChange?.unsubscribe();\n  }\n  getConnectors(projectVersionId) {\n    this.reportadminservice.getConnectors(projectVersionId).subscribe({\n      next: response => {\n        if (response.length > 0) {\n          this.connectors = [];\n          response.forEach(value => {\n            this.connectors.push({\n              value: value.name,\n              label: value.name\n            });\n          });\n          if (this.selectedConnector && this.selectedConnector !== '0') {\n            this.onConnectorChange();\n          }\n        }\n      }\n    });\n  }\n  getWorkFlows(projectId) {\n    this.reportadminservice.getWorkflows(projectId).subscribe({\n      next: response => {\n        response?.forEach(workflow => {\n          this.workflows.push(workflow);\n        });\n        const gridSaveEvent = this.reportConfig.gridEvents?.find(gridEvent => {\n          return gridEvent.eventType === GridEventType.GridSave;\n        });\n        if (gridSaveEvent) {\n          this.selectedSavedEventWorkflow = this.workflows.find(workflow => {\n            return gridSaveEvent.targetId === workflow.workflowIdentity;\n          });\n        }\n      },\n      error: err => {\n        throw err;\n      }\n    });\n  }\n  setGroupTagsSubscriptions() {\n    this.groupTagsSub = this.communicationService.groupTags.subscribe(event => {\n      this.existingAssetTagGroups = event.data ?? [];\n      this.groupTagOptions = [{\n        label: 'None',\n        value: 'null'\n      }];\n      this.groupTagOptions.push(...this.existingAssetTagGroups.map(tag => {\n        return {\n          label: tag.dropdownSelectName,\n          value: tag\n        };\n      }));\n      this.communicationService.getAssetGroupTag(this.reportConfig.reportId);\n    });\n    this.groupTagSub = this.communicationService.groupTag.subscribe(event => {\n      this.parentGroup = this.existingAssetTagGroups.find(tag => tag.id === event.data);\n      if (!this.parentGroup) {\n        this.parentGroup = this.groupTagOptions[0].value;\n      }\n    });\n  }\n  onClickTestWorkflow() {\n    this.showReportTester = true;\n  }\n  onClickHistory() {\n    this.showHistoryDialog = true;\n  }\n  onClickSave() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.projectIsLocked) {\n        return;\n      }\n      if (_this2.reportConfig.reportName.length === 0) {\n        _this2.messageSvc.showError('Error', 'Report Name is required.');\n        return;\n      }\n      if (_this2.reportConfig.description.length === 0) {\n        _this2.messageSvc.showError('Error', 'Display Name is required.');\n        return;\n      }\n      // Setting actionable grid column's format\n      if (_this2.reportConfig.reportType === ReportType.ActionableGrid && _this2.selectedUserConfiguredFormat) {\n        _this2.reportColumnCount = _this2.selectedUserConfiguredFormat.length;\n        if (!_this2.validateActionableGridColumnConfig()) {\n          return;\n        }\n      }\n      if (_this2.reportColumnCount > _this2.reportConfig.maxColumnCount) {\n        _this2.messageSvc.showError('Error', 'The report source contains more than ' + _this2.reportConfig.maxColumnCount + ' columns, please reduce the number of columns');\n        return;\n      } else if (_this2.reportColumnCount === 0) {\n        // implemented to handle stored procedure and select * queries\n        _this2.messageSvc.showWarning('Warning', 'Report creation might fail if report source contains more than ' + _this2.reportConfig.maxColumnCount + ' columns.');\n      }\n      const isViewChanged = _this2.selectedDataView?.viewId !== _this2.reportConfig.dataStoreViewName;\n      const isConnectionTypeChanged = _this2.selectedConnectorType?.key !== _this2.reportConfig.connectionType;\n      _this2.reportConfig.dataStoreViewName = _this2.selectedConnectorType?.key !== ConnectorTypes.Datastore || !_this2.selectedDataView || _this2.selectedDataView.viewId === '0' ? null : _this2.selectedDataView.viewId;\n      _this2.reportConfig.workFLowsettings.workFlowName = _this2.selectedWorkflow;\n      _this2.reportConfig.connectorconfig.configName = _this2.selectedConnector;\n      _this2.reportConfig.connectorconfig.remoteFunction = _this2.selectedFunction;\n      _this2.reportConfig.queryCategory = _this2.selectedQueryCategory;\n      _this2.reportConfig.queryName = _this2.selectedQueryName;\n      _this2.reportConfig.reportType = _this2.selectedCategory;\n      _this2.reportConfig.connectionType = _this2.selectedConnectorType?.key;\n      _this2.reportConfig.allowAnonymous = _this2.allowAnonymous;\n      _this2.reportConfig.connectionTimeOut = _this2.selectedConnectionTimeOut;\n      _this2.reportConfig.formName = _this2.selectedDatastore?.name;\n      _this2.reportConfig.gridEvents = _this2.selectedSavedEventWorkflow ? [{\n        eventType: GridEventType.GridSave,\n        targetType: GridTargetType.Workflow,\n        targetId: _this2.selectedSavedEventWorkflow?.workflowIdentity,\n        targetRelativeId: _this2.selectedSavedEventWorkflow?.name\n      }] : [];\n      if (!_this2.validateDynamicAsset() || _this2.calendarViewFlag && _this2.reportConfig.enableCalendarView && !(yield _this2.validateCalendarView())) {\n        return;\n      }\n      let validation = true;\n      let chartvalidation = true;\n      let dateColumnCount = 0;\n      _this2.columns.forEach(element => {\n        if (element.currency) {\n          const currency = _this2.currencies.find(d => d.code.toString() === element.currency);\n          if (element.currency === 'USD' || element.currency === 'GBP' || element.currency === 'EUR') {\n            element.currencySymbol = currency.symbol_native;\n          } else {\n            element.currencySymbol = element.currency + ' ' + currency.symbol_native;\n          }\n        }\n        if (element.format === 'date') {\n          dateColumnCount += 3;\n        }\n      });\n      if (_this2.reportColumnCount + dateColumnCount > _this2.reportConfig.maxColumnCount) {\n        _this2.messageSvc.showError('Error', 'Setting a column as a date will result in more than ' + _this2.reportConfig.maxColumnCount + ' columns as the month, quarter and year components will be split out. Please reduce the number of columns in this report.');\n        return;\n      }\n      const tempColumn = _this2.columns;\n      const assignTempColumn = Object.assign({}, ...tempColumn.map(x => ({\n        [x.name]: x\n      })));\n      _this2.reportConfig.formatConfig.config = assignTempColumn;\n      if (_this2.selectedConnectorType?.key === ConnectorTypes.Connector && (constants.queryparameters.connector !== _this2.selectedConnector || constants.queryparameters.remoteFunction !== _this2.selectedFunction || constants.queryparameters.queryCategory !== _this2.selectedQueryCategory || constants.queryparameters.queryName !== _this2.selectedQueryName) || isViewChanged || isConnectionTypeChanged) {\n        _this2.reportConfig.params?.forEach(x => {\n          x.action = 'delete';\n        });\n      }\n      if (_this2.allowAnonymous) {\n        validation = _this2.selectedUser ? true : false;\n      } else {\n        _this2.selectedUser = '0';\n      }\n      if (_this2.reportConfig.reportType === ReportType.Chart) {\n        _this2.reportConfig.chartType = _this2.chartType;\n        _this2.reportConfig.chartSubType = _this2.chartSubType;\n        _this2.reportConfig.chartConfigDetails = _this2.chartConfigDetails;\n      } else {\n        _this2.reportConfig.chartType = null;\n        _this2.reportConfig.chartSubType = null;\n        _this2.reportConfig.chartConfigDetails = null;\n      }\n      if (_this2.reportConfig.reportType === ReportType.Chart) {\n        if (_this2.reportConfig.chartType) {\n          chartvalidation = true;\n          if (_this2.reportConfig.chartType !== 'line' && _this2.reportConfig.chartType !== 'histogram' && _this2.reportConfig.chartType !== 'treemap') {\n            chartvalidation = _this2.reportConfig.chartSubType ? true : false;\n          }\n        } else {\n          chartvalidation = false;\n        }\n      }\n      if (_this2.drillThroughs.length > 0 && _this2.drillThroughs[0].displayColumn !== '') {\n        _this2.reportConfig.goldenArrowSetup = _this2.drillThroughs;\n      }\n      if (validation && chartvalidation) {\n        if (_this2.selectedUser) {\n          _this2.reportConfig.anonymousUser = _this2.user.find(d => d.id.toString() === _this2.selectedUser);\n        }\n        _this2.isSaveDisabled = true;\n        _this2.reportadminservice.addReportSettings(_this2.projectId, _this2.projectVersionId, _this2.reportConfig).subscribe({\n          next: response => {\n            _this2.reportConfig = response;\n            _this2.reportadminservice.onParameterOptionsChanged.next({\n              dataView: _this2.selectedDataView,\n              datastore: _this2.selectedDatastore\n            });\n            _this2.reportConfigOut.emit(_this2.reportConfig);\n            _this2.setTempQueryDetails(_this2.reportConfig);\n            if (_this2.dataViewDeleteMessage) {\n              _this2.messageSvc.showSuccess('Success', _this2.dataViewDeleteMessage);\n              _this2.dataViewDeleteMessage = '';\n            } else {\n              _this2.messageSvc.showSuccess('Success', 'Saved Successfully', false, 2000);\n            }\n            _this2.isSaveDisabled = false;\n            _this2.showHelp = false;\n            constants.refreshReport = _this2.randomNumber(2, 4);\n            _this2.communicationService.next({\n              fromAppName: _this2.communicationService.currentAppName,\n              toAppName: _this2.communicationService.navigationAppName,\n              eventType: constants.EventType.REPORTS_UPDATED,\n              data: {}\n            });\n            _this2.emitParentGroupCommunicationEvent(_this2.parentGroup);\n          },\n          error: err => {\n            _this2.isSaveDisabled = false;\n            throw err;\n          }\n        });\n        if (_this2.gridAgentChatFlag) {\n          _this2.agentAssetConfig.projectId = _this2.projectId;\n          _this2.agentAssetConfig.projectVersionId = _this2.projectVersionId;\n          _this2.agentAssetConfig.datastoreId = _this2.selectedDatastore?.id;\n          _this2.reportadminservice.saveAgentAssetConfig(_this2.reportConfig.reportId, _this2.reportConfig.reportType, _this2.agentAssetConfig).subscribe({\n            next: response => {\n              if (!response) {\n                _this2.messageSvc.showError('Error', 'Failed to save agent chat settings.');\n              }\n            },\n            error: err => {\n              _this2.isSaveDisabled = false;\n              _this2.messageSvc.showError('Error', 'Failed to save agent chat settings.');\n            }\n          });\n        }\n      } else {\n        if (!validation) {\n          _this2.messageSvc.showError('error', 'Please select any user');\n        } else if (!chartvalidation) {\n          _this2.messageSvc.showError('error', 'Please select chart type/sub type');\n        }\n      }\n    })();\n  }\n  validateActionableGridColumnConfig() {\n    this.reportConfig.formatConfig.actionableGridColumnsConfig = this.selectedUserConfiguredFormat;\n    // validations for format columns\n    const emptyValidValues = this.getEmptyValidValues(this.reportConfig.formatConfig.actionableGridColumnsConfig);\n    if (emptyValidValues.length > 0) {\n      this.messageSvc.showError('Error', `No Values found for ${emptyValidValues[0].format.type} format!`);\n      return false;\n    }\n    return true;\n  }\n  getEmptyValidValues(actionableGridColumnConfig) {\n    return actionableGridColumnConfig?.filter(columnConfig => this.checkDropdown(columnConfig) || this.checkLink(columnConfig));\n  }\n  checkDropdown(columnConfig) {\n    return (columnConfig.format.type === ActionableGridColumnType.Checkbox && columnConfig.format.baseType === JsonDataTypes.String || columnConfig.format.type === ActionableGridColumnType.Symbol || columnConfig.format.type === ActionableGridColumnType.String && columnConfig.dropdown) && (!columnConfig.values || columnConfig.values.length === 0);\n  }\n  checkLink(columnConfig) {\n    return columnConfig.format.type === ActionableGridColumnType.Link && !columnConfig.link?.urlAlias;\n  }\n  validateCalendarView() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const viewHashId = _this3.selectedDataView.viewId === '0' ? 'none' : yield _this3.cryptoService.hashSHA256(_this3.selectedDataView.columnsToShow?.sort()?.toString());\n      if (_this3.reportConfig.calendarViewConfig.viewHashId !== viewHashId) {\n        _this3.messageSvc.showError('Selected view has been updated, please review calendar settings.');\n        _this3.reportConfig.calendarViewConfig.viewHashId = yield _this3.cryptoService.hashSHA256(_this3.selectedDataView.columnsToShow?.sort()?.toString());\n        return false;\n      }\n      if (_this3.reportConfig.calendarViewConfig.startDateCol === _this3.reportConfig.calendarViewConfig.endDateCol) {\n        _this3.messageSvc.showError('Start Date and End Date fields of Calendar View settings must not be the same, please review these settings.');\n        return false;\n      }\n      if (_this3.reportConfig.calendarViewConfig.titleCol?.length === 0) {\n        _this3.messageSvc.showError('Title/Description Fields is a required setting, please review Calendar View Settings.');\n        return false;\n      }\n      return true;\n    })();\n  }\n  validateDynamicAsset() {\n    if (this.reportConfig?.formatConfig?.dynamicAssetInfo?.type === DynamicAssetTypes.ConnectorApp && !this.reportConfig?.formatConfig?.dynamicAssetInfo?.idField) {\n      this.messageSvc.showError('Error', 'When a Connector Screen is selected for edit mode an Id Field must be chosen.');\n      return false;\n    }\n    return true;\n  }\n  getUsers(projectId) {\n    this.reportadminservice.getUsers(projectId).subscribe({\n      next: response => {\n        this.user = response;\n        for (const user of response) {\n          this.users.push({\n            label: user.firstName.trim() + ' ' + user.lastName.trim(),\n            value: user.id.toString()\n          });\n        }\n        this.users.sort((x, y) => x.label.localeCompare(y.label));\n        if (this.reportConfig.anonymousUser) {\n          this.selectedUser = this.reportConfig.anonymousUser.id > 0 ? this.reportConfig.anonymousUser.id.toString() : '0';\n        }\n      },\n      error: err => {\n        throw err;\n      }\n    });\n  }\n  copyToClipboard(item) {\n    if (this.projectIsLocked) {\n      return;\n    }\n    const listener = e => {\n      e.clipboardData.setData('text/plain', item);\n      e.preventDefault();\n    };\n    document.addEventListener('copy', listener);\n    document.execCommand('copy');\n    document.removeEventListener('copy', listener);\n    this.messageSvc.showSuccess('Copied to Clipboard', 'Copied to Clipboard');\n  }\n  onClickDelete() {\n    this.displayModal = true;\n  }\n  deleteReport() {\n    if (this.projectIsLocked) {\n      return;\n    }\n    this.reportadminservice.deleteReport(this.reportConfig.reportId).subscribe({\n      next: response => {\n        if (response) {\n          this.messageSvc.showSuccess('Deleted', 'Deleted Successfully');\n          window.location.assign('/reporting/report-setup/report/1/1/0');\n        } else {\n          this.messageSvc.showError('Error', 'Delete failed.');\n        }\n      },\n      error: err => {\n        this.messageSvc.showError('Error', err);\n        throw err;\n      }\n    });\n    this.displayModal = false;\n  }\n  onConnectorChange() {\n    if (this.projectIsLocked) {\n      return;\n    }\n    this.reportadminservice.getFunctions(this.projectVersionId, this.selectedConnector).subscribe({\n      next: response => {\n        this.functions = [];\n        if (response.length > 0) {\n          response.forEach(value => {\n            this.functions.push({\n              value: value.key,\n              label: value.name\n            });\n          });\n        }\n        if (this.reportConfig.queryColumnsFunction) {\n          const index = this.functions.findIndex(x => x.value === this.reportConfig.queryColumnsFunction);\n          if (index !== -1) {\n            this.functions = this.functions.filter(item => item.value !== this.reportConfig.queryColumnsFunction);\n          }\n        }\n        if (this.reportConfig.categoryFunction) {\n          const index = this.functions.findIndex(x => x.value === this.reportConfig.categoryFunction);\n          if (index !== -1) {\n            this.categoryFunctionExists = true;\n            this.functions = this.functions.filter(item => item.value !== this.reportConfig.categoryFunction);\n          } else {\n            this.categoryFunctionExists = false;\n          }\n        }\n        if (this.reportConfig.categoryFunction) {\n          const index = this.functions.findIndex(x => x.value === this.reportConfig.queryFunction);\n          if (index !== -1) {\n            this.queryFunctionExists = true;\n            this.functions = this.functions.filter(item => item.value !== this.reportConfig.queryFunction);\n          } else {\n            this.queryFunctionExists = false;\n          }\n        }\n        this.selectedFunction = this.functions[0].value;\n        if (this.categoryFunctionExists) {\n          this.getQueryCategories();\n        }\n      }\n    });\n  }\n  onConnectorTypeChange() {\n    if (this.projectIsLocked) {\n      return;\n    }\n    this.setFormatColumnsDataSource();\n  }\n  numberOnly(event) {\n    if (this.projectIsLocked) {\n      return;\n    }\n    const charCode = event.which ? event.which : event.keyCode;\n    if (charCode > 31 && (charCode < 48 || charCode > 57)) {\n      return false;\n    }\n    return true;\n  }\n  checkTimeOutValid() {\n    if (this.projectIsLocked) {\n      return;\n    }\n    const connectionTimeOutSelected = this.selectedConnectionTimeOut;\n    if (connectionTimeOutSelected >= 60) {\n      // empty\n    } else if (connectionTimeOutSelected < 60 && connectionTimeOutSelected >= 5) {\n      this.confirmLessThan60Seconds(connectionTimeOutSelected);\n    } else {\n      this.minimunCache = true;\n    }\n  }\n  confirmLessThan60Seconds(connectionTimeOutSelected) {\n    this.confirmationService.confirm({\n      message: '<p style=\"white-space: pre-line\">Reducing data caching refresh times below 60 seconds can result in system performance issues.\\n This is not recommended, do you wish to proceed?.</p>',\n      header: 'Are you sure that you want to proceed?',\n      icon: 'pi pi-exclamation-triangle',\n      acceptButtonStyleClass: 'p-button-outlined p-button-info',\n      rejectButtonStyleClass: 'p-button-outlined p-button-info',\n      accept: () => {\n        this.selectedConnectionTimeOut = connectionTimeOutSelected;\n      },\n      reject: () => {\n        this.selectedConnectionTimeOut = 300;\n        this.renderer.selectRootElement('#connectionTimeOut').focus();\n      }\n    });\n  }\n  hideModal() {\n    this.minimunCache = false;\n    this.selectedConnectionTimeOut = 60;\n  }\n  onChartChange() {\n    if (this.projectIsLocked) {\n      return;\n    }\n    const Info = this.chartTypes.find(p => p.key === this.chartType);\n    this.chartSubTypes = Info.subTypes;\n    this.chartConfigDetails = Info.config;\n    if (this.chartSubTypes?.length) {\n      this.chartSubType = this.chartSubTypes[0].key;\n    }\n  }\n  getFeatureAssignment() {\n    this.reportadminservice.getFeatureAssignment().subscribe({\n      next: response => {\n        if (response) {\n          const featureflag = response.find(s => s.name === constants.FeatureFlag.STAND_ALONE_CHART);\n          if (featureflag) {\n            this.standaloneChartfeatuerFlag = true;\n            this.categories.push({\n              name: 'Chart',\n              key: 'chart'\n            });\n          }\n        }\n      },\n      error: err => {\n        throw err;\n      }\n    });\n  }\n  // this should be the new way to do it.  will need to refactor the other feature flag calls\n  setFeatureFlags() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const featureFlags = yield _this4.featureFlagService.getFeatureFlags();\n      _this4.visualizationFlag = featureFlags.some(x => x.name === FeatureFlag.Visualizations);\n      _this4.gridChartsFlag = featureFlags.some(x => x.name === FeatureFlag.Visualizations); // (FeatureFlag.GridCharts);\n      _this4.groupTagFlag = featureFlags.some(x => x.name === FeatureFlag.AssetGroupTags);\n      _this4.calendarViewFlag = featureFlags.some(x => x.name === FeatureFlag.CalendarView);\n      _this4.advancedJsonEditFlag = featureFlags.some(x => x.name === FeatureFlag.AdvancedJsonEdit);\n      _this4.gridAgentChatFlag = featureFlags.some(x => x.name === FeatureFlag.GridAgentChat);\n    })();\n  }\n  getDatastores(projectId, projectVersionId) {\n    this.datastoresService.getDatastores(projectId, projectVersionId).pipe(take(1)).subscribe(datastores => {\n      this.datastores = datastores;\n      if (this.reportConfig.connectionType === ConnectorTypes.Datastore && this.reportConfig.formName) {\n        this.selectedDatastore = this.datastores?.find(x => x.name === this.reportConfig.formName);\n        this.prevDatastore = this.selectedDatastore;\n        this.getDataViewList();\n        if (!this.selectedDatastore) {\n          this.messageSvc.showError('Error', `Datastore not found!`);\n        }\n        this.getDatastoreAssets();\n      }\n    });\n  }\n  randomNumber(min, max) {\n    return Math.random() * (max - min) + min;\n  }\n  setParamAction() {\n    this.reportadminservice.getReportSettingsById(this.reportConfig.reportId).subscribe({\n      next: response => {\n        if (response.connectorconfig.connector !== this.selectedConnector || response.connectorconfig.remoteFunction !== this.selectedFunction || response.queryCategory !== this.selectedQueryCategory || response.queryName !== this.selectedQueryName) {\n          this.reportConfig.params?.forEach(x => {\n            x.action = 'delete';\n          });\n        }\n      },\n      error: err => {\n        throw err;\n      }\n    });\n  }\n  clearSelectedDrillThroughs() {\n    if (this.projectIsLocked) {\n      return;\n    }\n    this.selectedDrillThroughs?.forEach(s => {\n      const index = this.drillThroughs.findIndex(d => d === s);\n      if (index > -1) {\n        this.drillThroughs.splice(index, 1);\n      }\n    });\n    this.selectedDrillThroughs = [];\n  }\n  drillThroughCheckedChange(checked, drillThrough) {\n    if (this.projectIsLocked) {\n      return;\n    }\n    if (checked) {\n      this.selectedDrillThroughs.push(drillThrough);\n    } else {\n      const index = this.selectedDrillThroughs.findIndex(s => s === drillThrough);\n      if (index > -1) {\n        this.selectedDrillThroughs.splice(index, 1);\n      }\n    }\n  }\n  addNew() {\n    this.defaultDrillThrough = {\n      displayColumn: '',\n      openIn: '0',\n      urlAlias: '',\n      urlSubstitution: [new UrlSub()],\n      gridGeneratedLink: ''\n    };\n    this.drillThroughs.push(this.defaultDrillThrough);\n  }\n  deleteProduct(drillThrough) {\n    if (this.projectIsLocked) {\n      return;\n    }\n    const index = this.drillThroughs.findIndex(x => x === drillThrough);\n    this.drillThroughs.splice(index, 1);\n    this.messageSvc.showSuccess('Row deleted.');\n  }\n  addSubstitution(urlSubstitution) {\n    if (this.projectIsLocked) {\n      return;\n    }\n    urlSubstitution.push(new UrlSub());\n  }\n  deleteSubstitution(urlSubstitution, index) {\n    if (this.projectIsLocked) {\n      return;\n    }\n    urlSubstitution.splice(index, 1);\n  }\n  // method to update the generated link preview column when the drill through options are updated\n  onSubstitutionChange(drillThrough) {\n    if (this.projectIsLocked) {\n      return;\n    }\n    let generatedLink = drillThrough.urlAlias;\n    const params = generatedLink.split('{');\n    params?.forEach(x => {\n      if (x.length > 0) {\n        const index = x.split('}')[0];\n        if (_isNumberValue(index)) {\n          if (drillThrough.urlSubstitution.length > Number(index)) {\n            const actualValue = '{' + drillThrough.urlSubstitution[index].urlName + '}';\n            const tempValue = '{' + index + '}';\n            if (actualValue) generatedLink = generatedLink.replace(tempValue, actualValue);\n          }\n        }\n      }\n    });\n    drillThrough.gridGeneratedLink = generatedLink;\n  }\n  setTempQueryDetails(reportConfig) {\n    constants.queryparameters.connector = reportConfig.connectorconfig.configName;\n    constants.queryparameters.remoteFunction = reportConfig.connectorconfig.remoteFunction;\n    constants.queryparameters.queryCategory = reportConfig.queryCategory;\n    constants.queryparameters.queryName = reportConfig.queryName;\n  }\n  getQueryCategories() {\n    this.reportadminservice.getQueryCategories(this.customerId, this.projectId, this.projectVersionId, this.selectedConnector, this.reportConfig.categoryFunction).subscribe({\n      next: response => {\n        if (response) {\n          this.queryCategory = [];\n          let itemlabel = '';\n          let itemval = '';\n          response.forEach(item => {\n            itemlabel = item.categoryName;\n            if (!itemlabel) {\n              itemlabel = ' ';\n              itemval = '0';\n            } else {\n              itemval = item.categoryName;\n            }\n            this.queryCategory.push({\n              value: itemval,\n              label: itemlabel\n            });\n          });\n          if (this.reportConfig?.queryCategory || this.reportConfig?.queryCategory === '') {\n            this.getQueryNames();\n          }\n        }\n      }\n    });\n  }\n  getQueryNames() {\n    this.reportadminservice.getQueryNames(this.customerId, this.projectId, this.projectVersionId, this.selectedConnector, this.reportConfig.queryFunction, this.selectedQueryCategory).subscribe({\n      next: response => {\n        if (response) {\n          this.queryName = [];\n          response.forEach(item => {\n            this.queryName.push({\n              value: item.qName,\n              label: item.qName\n            });\n          });\n        }\n      }\n    });\n  }\n  onCategoryChange() {\n    if (this.projectIsLocked) {\n      return;\n    }\n    this.getQueryNames();\n  }\n  onSelectedDatastoreChange() {\n    if (this.projectIsLocked) {\n      return;\n    }\n    if (this.calendarViewFlag && this.reportConfig.enableCalendarView) {\n      this.confirmationService.confirm({\n        header: 'Calendar View Configured',\n        message: 'Changing the datastore will reset this configuration, would you like to continue?',\n        accept: () => {\n          this.prevDatastore = this.selectedDatastore;\n          this.getDataViewList();\n          this.getDatastoreAssets();\n          this.resetCalendarView();\n        },\n        reject: () => {\n          this.selectedDatastore = this.prevDatastore;\n        }\n      });\n      return;\n    }\n    this.prevDatastore = this.selectedDatastore;\n    this.getDataViewList();\n    this.getDatastoreAssets();\n    this.resetCalendarView();\n  }\n  setFormatColumnsDataSource() {\n    if (!this.selectedDataView) {\n      return;\n    }\n    // If it is not actionablegrid follow the old way\n    if (this.reportConfig.reportType !== ReportType.ActionableGrid) {\n      this.columnsToShow = this.selectedDataView.viewId === '0' ? [] : this.selectedDataView.columnsToShow;\n      this.getColumnNames();\n      return;\n    }\n    // Lazy loading default format settings based on base schema\n    this.selectedUserConfiguredFormat = this.cachedUserConfiguredFormats.find(ucf => ucf.datastoreId === this.selectedDatastore.id && ucf.dataViewId === this.selectedDataView.viewId)?.userConfiguredFormat;\n    if (!this.selectedUserConfiguredFormat && this.selectedDatastore.baseSchema?.properties) {\n      if (!this.cachedFormatSettings[this.selectedDatastore.id]) {\n        this.cachedFormatSettings[this.selectedDatastore.id] = this.reportadminservice.mapBaseSchemaToFormatSettings(this.selectedDatastore?.baseSchema);\n      }\n      // If there is already a customized setting for this report, load the setting, else load it based on baseSchema\n      if (this.selectedDatastore.name === this.reportConfig.formName && this.reportConfig.formatConfig.actionableGridColumnsConfig && (this.selectedDataView.viewId === this.reportConfig.dataStoreViewName || this.selectedDataView.viewId === '0' && !this.reportConfig.dataStoreViewName)) {\n        this.selectedUserConfiguredFormat = this.reportConfig?.formatConfig?.actionableGridColumnsConfig;\n        // Update the colums type and if there is no view selected, update the columns list with the latest baseSchema\n        this.selectedUserConfiguredFormat = this.updateColumnsWithBaseSchema(this.selectedDataView.viewId === '0', this.selectedUserConfiguredFormat, this.cachedFormatSettings[this.selectedDatastore.id]);\n      } else {\n        // if there is no view selected(0), show default setting\n        // note: We are lazy loading here and we need a deep copy of the default settings,\n        // notice:_.deepCloneObject dosn't preserve the object type\n        if (this.selectedDataView.viewId === '0') {\n          this.selectedUserConfiguredFormat = deepCopy(this.cachedFormatSettings[this.selectedDatastore.id]);\n          return;\n        }\n        this.selectedUserConfiguredFormat = [];\n        this.selectedDataView.columnsToShow?.forEach(col => {\n          const columnConfig = this.cachedFormatSettings[this.selectedDatastore.id].find(x => x.column === col);\n          if (columnConfig) {\n            this.selectedUserConfiguredFormat.push(deepCopy(columnConfig));\n          }\n        });\n      }\n      this.cachedUserConfiguredFormats.push({\n        datastoreId: this.selectedDatastore.id,\n        dataViewId: this.selectedDataView.viewId,\n        userConfiguredFormat: this.selectedUserConfiguredFormat\n      });\n    }\n  }\n  updateColumnsWithBaseSchema(updateColumnsList = false, formatSettings, defaultFormatSettings) {\n    // Just to make code easier to read\n    const stringFormats = [ActionableGridColumnType.Checkbox, ActionableGridColumnType.String, ActionableGridColumnType.Symbol];\n    formatSettings = formatSettings ?? [];\n    if (updateColumnsList) {\n      // Removing deleted columns\n      for (let i = formatSettings.length - 1; i >= 0; i--) {\n        const col = formatSettings[i];\n        const index = defaultFormatSettings.findIndex(x => x.column === col.column);\n        if (index === -1) {\n          formatSettings.splice(formatSettings.findIndex(sfs => sfs.column === col.column), 1);\n        }\n      }\n      // Adding new columns. Not: we can't use formatSettings = ...\n      formatSettings = formatSettings.concat(defaultFormatSettings.filter(value => !formatSettings.some(y => y.column === value.column)));\n    }\n    // Updating column types with the latest version of base schema\n    formatSettings.forEach(col => {\n      const defaultColConfig = defaultFormatSettings.find(x => x.column === col.column);\n      if (defaultColConfig) {\n        col.format.baseType = defaultColConfig.format.baseType;\n        col.format.baseFormat = defaultColConfig.format.baseFormat;\n        col.typeDescription = defaultColConfig.typeDescription;\n        col.format.currency = defaultColConfig.format.currency;\n        col.format.decimalPlaces = defaultColConfig.format.decimalPlaces;\n        col.displayName = defaultColConfig.displayName;\n        col.schemaProperty = defaultColConfig.schemaProperty;\n        /**\n         * If format type is link, do not overwrite\n         * If it is not string, it is not overridable. So overwrite\n         * If it is date, it is not overridable. So overwrite\n         * If it is string, it is overridable. So only overwrite if it is not one of the existing string format types.\n         * (consider type change from datastore)\n         */\n        if (col.format.type !== ActionableGridColumnType.Link && ![ActionableGridColumnType.DateTime, ActionableGridColumnType.Date].includes(col.format.type) && (defaultColConfig.format.baseType !== JsonDataTypes.String || !stringFormats.includes(col.format.type))) {\n          col.format.type = defaultColConfig.format.type;\n        }\n        if (defaultColConfig.schemaProperty?.typeProperties?.required) {\n          col.required = defaultColConfig.required;\n        }\n        if (col.required && col.column !== this.selectedDatastore.primaryKey) {\n          col.allowUserUpdate = true;\n        }\n        if (col.format.baseType === JsonDataTypes.Array) {\n          col.children = this.updateColumnsWithBaseSchema(updateColumnsList, col.children, defaultColConfig.children);\n        }\n      }\n    });\n    return formatSettings;\n  }\n  getColumnNames() {\n    // Actionable grid loads format settings differently, later we can follow the same method for other report types.\n    if (this.projectIsLocked || this.reportConfig.reportType === ReportType.ActionableGrid) {\n      return;\n    }\n    this.setDataSouce();\n    if (this.reportConfig.dataSourceInfo.dataSource) {\n      const savedColumnConfigList = this.getSavedColumnConfigList();\n      // columns is the full list of columns.\n      this.columns = [];\n      // formatColumns is what is bound to the html(basically list of the columns that we show to the user)\n      this.formatColumns = [];\n      //  urlColumns can be completely removed as we can use the same 'columns' list\n      this.urlColumns = [];\n      if (this.selectedConnectorType?.key === ConnectorTypes.Datastore) {\n        this.getColumnListFromBaseSchema(savedColumnConfigList);\n      } else {\n        this.columnsToShow = [];\n        this.getColumnListFromApi(savedColumnConfigList);\n      }\n    }\n  }\n  getSavedColumnConfigList() {\n    if (this.reportConfig.formatConfig?.config && (this.reportConfig.connectionType === ConnectorTypes.Datastore && this.reportConfig.formName === this.selectedDatastore?.name || this.reportConfig.connectionType === ConnectorTypes.Connector && this.reportConfig.queryName === this.selectedQueryName)) {\n      return Object.values(this.reportConfig.formatConfig.config);\n    }\n    return [];\n  }\n  getColumnListFromBaseSchema(savedColumnConfigList) {\n    if (this.selectedDatastore?.baseSchema?.properties) {\n      Object.keys(this.selectedDatastore.baseSchema.properties).forEach(e => {\n        const colName = e.toString();\n        // Note: to get the list of properties of each column: form.baseSchema.properties[e]\n        const colConfig = this.selectedDatastore.baseSchema.properties[e];\n        if (colConfig.type !== JsonDataTypes.Array && colConfig.type !== JsonDataTypes.Object) {\n          let column = savedColumnConfigList?.find(p => p.name === colName);\n          if (!column) {\n            column = new ColumnConfig(colName);\n            switch (colConfig.type) {\n              case JsonDataTypes.String:\n                column.format = colConfig.format === JsonStringFormats.DateTime || colConfig.format === JsonStringFormats.Date ? \"date\" : \"string\";\n                break;\n              case JsonDataTypes.Boolean:\n                column.format = \"string\";\n                break;\n              case JsonDataTypes.Integer:\n              case JsonDataTypes.Decimal:\n                column.format = colConfig?.typeProperties?.currency ? \"currency\" : \"numeric\";\n                break;\n              default:\n                column.format = colConfig.type;\n                break;\n            }\n            column.displayName = colConfig?.presentationProperties?.displayName;\n            column.decimalPlaces = colConfig?.presentationProperties?.decimalPlaces ?? 0;\n            column.currency = colConfig?.typeProperties?.currency;\n          }\n          column.baseType = colConfig.type;\n          column.baseFormat = colConfig.format;\n          const type = Object.keys(JsonDataTypes).filter(v => JsonDataTypes[v] === colConfig.type);\n          if (type.length > 0) {\n            column.typeDescription = colConfig.format === JsonStringFormats.DateTime || colConfig.format === JsonStringFormats.Date ? `Date` : type[0];\n          }\n          // if it is not in the list of columnsToShow do not show this column to user.\n          const showColumn = this.columnsToShow.length === 0 || this.columnsToShow?.find(x => x === colName);\n          if (showColumn || !this.selectedDataView) {\n            this.formatColumns.push(column);\n          }\n          this.columns.push(column);\n          this.urlColumns.push({\n            value: column.name,\n            label: column.name\n          });\n        }\n      });\n    }\n  }\n  getColumnListFromApi(savedColumnConfigList) {\n    this.reportadminservice.getReportColumns(this.projectId, this.projectVersionId, this.reportConfig).subscribe({\n      next: columnNameList => {\n        this.isSaveDisabled = false;\n        if (columnNameList) {\n          this.reportColumnCount = this.columnsToShow.length > 0 ? this.columnsToShow.length : columnNameList.length;\n          columnNameList.forEach(colName => {\n            // if it is not in the list of columnsToShow do not show this column to user.\n            const showColumn = this.columnsToShow.length === 0 || this.columnsToShow?.find(x => x.name === colName);\n            // if we didn't find the column in the saved list or this is the first time, create a new column.\n            const column = savedColumnConfigList?.find(p => p.name === colName) ?? new ColumnConfig(colName, colName.toLowerCase().includes('date') ? 'date' : 'string');\n            if (showColumn) {\n              this.formatColumns.push(column);\n            }\n            this.columns.push(column);\n            this.urlColumns.push({\n              value: column.name,\n              label: column.name\n            });\n          });\n        }\n      },\n      error: err => {\n        this.messageSvc.showError('Error', err.message);\n      }\n    });\n  }\n  setDecimalPlaces(rowData) {\n    if (this.projectIsLocked) {\n      return;\n    }\n    rowData.decimalPlaces = rowData.format === 'currency' ? 2 : 0;\n  }\n  setDataSouce() {\n    this.reportConfig.dataSourceInfo = {\n      id: 0,\n      dataSource: '',\n      formName: '',\n      connectorInfo: {},\n      queryCategory: '',\n      queryName: ''\n    };\n    this.reportConfig.dataSourceInfo.formName = this.selectedDatastore?.name;\n    this.reportConfig.dataSourceInfo.dataSource = this.selectedConnectorType ? this.selectedConnectorType.key : '';\n    this.reportConfig.dataSourceInfo.connectorInfo.configName = this.selectedConnector;\n    this.reportConfig.dataSourceInfo.connectorInfo.remoteFunction = this.reportConfig.queryColumnsFunction;\n    this.reportConfig.dataSourceInfo.queryCategory = this.selectedQueryCategory;\n    this.reportConfig.dataSourceInfo.queryName = this.selectedQueryName;\n  }\n  openAssetModal() {\n    if (this.projectIsLocked) {\n      return;\n    }\n    this.communicationService.next({\n      fromAppName: this.communicationService.currentAppName,\n      toAppName: this.communicationService.navigationAppName,\n      eventType: 'openAssetModal',\n      data: {\n        assetId: this.reportConfig.reportId,\n        assetType: 'Report',\n        assetName: this.reportConfig.reportName,\n        tenantId: this.reportConfig.tenantId,\n        projectId: this.reportConfig.projectId,\n        environmentName: this.reportConfig.environment,\n        projectName: null\n      }\n    });\n  }\n  geProjectVariables() {\n    this.reportadminservice.getProjectVariables(this.projectVersionId).subscribe({\n      next: response => {\n        if (response) {\n          this.projectVariables = response;\n          response.forEach(element => {\n            this.urlProjectVariables.push({\n              value: element.variable,\n              label: element.variable,\n              title: element.description\n            });\n          });\n        }\n      },\n      error: err => {\n        throw err;\n      }\n    });\n  }\n  deleteView(dataView) {\n    if (this.projectIsLocked) {\n      return;\n    }\n    this.confirmationService.confirm({\n      message: 'Are you sure that you want to delete this view?',\n      accept: () => {\n        const index = this.selectedDatastore.dataViews.indexOf(dataView);\n        const copiedSelectedDatastore = deepCopy(this.selectedDatastore);\n        copiedSelectedDatastore.dataViews.splice(index, 1);\n        this.datastoresService.updateDatastore(copiedSelectedDatastore.id, copiedSelectedDatastore).pipe(take(1)).subscribe({\n          next: response => {\n            this.dataViewDeleteMessage = 'Data view ' + dataView.name + ' deleted successfully';\n            this.selectedDatastore.dataViews.splice(index, 1);\n            this.getDataViewList();\n          },\n          error: exception => {\n            this.messageSvc.showError('Error', exception);\n          }\n        });\n      }\n    });\n  }\n  openAnalyticsFilterModal(formMode, dataView) {\n    if (this.projectIsLocked) {\n      return;\n    }\n    this.communicationService.next({\n      fromAppName: this.communicationService.currentAppName,\n      toAppName: this.communicationService.saltboxAnalyticsFilterAppName,\n      eventType: constants.EventType.OPEN_ANALTYICS_FILTER,\n      data: {\n        datastore: deepCopy(this.selectedDatastore),\n        dataView: deepCopy(dataView),\n        mode: formMode\n      }\n    });\n  }\n  getDataViewList() {\n    const defaultView = new DataView('No View Selected', '0');\n    this.dataViews = [defaultView, ...(this.selectedDatastore?.dataViews || [])];\n    this.selectSavedViewOnReportSettings();\n  }\n  selectSavedViewOnReportSettings() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this5.selectedDatastore) return;\n      // if there is already a saved selected view for this report, select the view else select 'No View Selected'\n      if (_this5.reportConfig?.formName === _this5.selectedDatastore.name && _this5.reportConfig.dataStoreViewName) {\n        const view = _this5.dataViews.find(f => f.viewId === _this5.reportConfig.dataStoreViewName);\n        // we can show a warning that we couldn't find the view\n        // if we couldn't find the view we gonna select 'No View Selected'\n        _this5.selectedDataView = view ?? _this5.dataViews[0];\n        _this5.prevDataView = _this5.selectedDataView;\n        // check viewHashId here to ensure data view and calendar view are still compatible\n        if (_this5.calendarViewFlag && _this5.reportConfig.enableCalendarView) {\n          const viewHashId = _this5.selectedDataView.viewId === '0' ? 'none' : yield _this5.cryptoService.hashSHA256(_this5.selectedDataView.columnsToShow?.sort()?.toString());\n          if (_this5.reportConfig.calendarViewConfig.viewHashId !== viewHashId) {\n            _this5.messageSvc.showWarning('Selected view has been updated, please review calendar settings.');\n            _this5.reportConfig.calendarViewConfig.viewHashId = yield _this5.cryptoService.hashSHA256(_this5.selectedDataView.columnsToShow?.sort()?.toString());\n          }\n        }\n        _this5.reportConfig.params.filter(param => !_this5.selectedDataView?.filterConditions.find(x => x.columnUID === param.columnUID)).forEach(param => {\n          param.action = 'delete';\n        });\n        _this5.paramListUpdated.emit(_this5.reportConfig.params);\n        _this5.reportadminservice.onParameterOptionsChanged.next({\n          dataView: _this5.selectedDataView,\n          datastore: _this5.selectedDatastore\n        });\n      } else {\n        // it doesn't detect the changes in some cases after angular 15! just added a changeDetectorRef to detect changes\n        _this5.changeDetectorRef.detectChanges();\n        _this5.selectedDataView = _this5.dataViews[0];\n        _this5.prevDataView = _this5.selectedDataView;\n      }\n      // Since we are manually setting the dropdown we need to set format columns too\n      _this5.setFormatColumnsDataSource();\n    })();\n  }\n  onSelectedViewChange() {\n    if (this.projectIsLocked) {\n      return;\n    }\n    if (this.calendarViewFlag && this.reportConfig.enableCalendarView) {\n      this.confirmationService.confirm({\n        header: 'Calendar View Configured',\n        message: 'Changing the data view will reset this configuration, would you like to continue?',\n        accept: () => {\n          this.prevDataView = this.selectedDataView;\n          this.setFormatColumnsDataSource();\n          this.resetCalendarView();\n        },\n        reject: () => {\n          this.selectedDataView = this.prevDataView;\n        }\n      });\n      return;\n    }\n    this.prevDataView = this.selectedDataView;\n    this.setFormatColumnsDataSource();\n    this.resetCalendarView();\n  }\n  resetCalendarView() {\n    this.reportConfig.enableCalendarView = false;\n    this.reportConfig.calendarViewConfig = null;\n  }\n  selectUrlSubstitution(urlSubstitution, rowIndex, urlSubstitutionIndex) {\n    if (this.projectIsLocked) {\n      return;\n    }\n    if (urlSubstitution[urlSubstitutionIndex].isProjectVariable) {\n      this.urlSubType = 'projectVariable';\n      this.selectedProjectVariable = this.urlProjectVariables.find(x => x.label === urlSubstitution[urlSubstitutionIndex].urlName);\n    } else {\n      this.urlSubType = 'column';\n      this.selectedColumn = this.urlColumns.find(x => x.label === urlSubstitution[urlSubstitutionIndex].urlName);\n    }\n    this.displayURLSubstitution = true;\n    this.selectedUrlSubstitution = urlSubstitution[urlSubstitutionIndex].urlName;\n    this.selectedRowIndex = rowIndex;\n    this.selectedUrlSubIndex = urlSubstitutionIndex;\n  }\n  applyURLSubstitution() {\n    if (this.projectIsLocked) {\n      return;\n    }\n    const selectedDrillThrough = this.drillThroughs[this.selectedRowIndex];\n    const selectedUrlSub = selectedDrillThrough.urlSubstitution[this.selectedUrlSubIndex];\n    selectedUrlSub.isProjectVariable = this.urlSubType !== 'column';\n    selectedUrlSub.urlName = (selectedUrlSub.isProjectVariable ? this.selectedProjectVariable : this.selectedColumn).value;\n    this.onSubstitutionChange(selectedDrillThrough);\n    this.displayURLSubstitution = false;\n  }\n  subscribeToInterAppCommunication() {\n    this.interAppSubscription = this.communicationService.getObservable().subscribe(event => {\n      this.ngZone.run(() => {\n        if (event.eventType === constants.EventType.SAVED_DATAVIEW_FILTER) {\n          if (event.data.dataView) {\n            this.updateDataViewList(event.data.dataView);\n          }\n        } else if (event.eventType === constants.EventType.ERROR_ANALTYICS_FILTER) {\n          if (event.data.error) {\n            this.messageSvc.showError('Error', event.data.error);\n            console.log(event.data.error);\n          }\n        }\n      });\n    });\n  }\n  updateDataViewList(dataView) {\n    if (!dataView) {\n      return;\n    }\n    if (!this.selectedDatastore?.dataViews) {\n      this.selectedDatastore.dataViews = [];\n    }\n    // finding the view in the list\n    const currentDataView = this.selectedDatastore?.dataViews.find(cfw => cfw.viewId === dataView.viewId);\n    // update existing view\n    if (currentDataView) {\n      currentDataView.conditionOperator = dataView.conditionOperator;\n      currentDataView.columnsToShow = dataView.columnsToShow;\n      currentDataView.filterConditions = dataView.filterConditions;\n      currentDataView.name = dataView.name;\n      // updating the list of columns\n      const cachedUserConfiguredFormats = this.cachedUserConfiguredFormats.find(ucf => ucf.datastoreId === this.selectedDatastore.id && ucf.dataViewId === dataView.viewId);\n      if (cachedUserConfiguredFormats) {\n        // removing deleted columns\n        const updatedColumns = cachedUserConfiguredFormats.userConfiguredFormat.filter(ucf => dataView.columnsToShow.some(cts => cts === ucf.column));\n        // finding the new columns\n        const newColumns = dataView.columnsToShow.filter(value => !cachedUserConfiguredFormats.userConfiguredFormat.some(ucf => ucf.column === value));\n        // merging the two arrays\n        cachedUserConfiguredFormats.userConfiguredFormat = deepCopy(updatedColumns.concat(this.cachedFormatSettings[this.selectedDatastore?.id]?.filter(value => newColumns.some(cts => cts === value.column))));\n      }\n      this.messageSvc.showSuccess('Success', 'Filter updated successfully.');\n      this.getDataViewList();\n      this.selectedDataView = currentDataView;\n    }\n    // add new view\n    else {\n      this.selectedDatastore?.dataViews.push(dataView);\n      this.dataViews.push(dataView);\n      this.selectedDataView = dataView;\n      this.messageSvc.showSuccess('Success', 'New filter successfully added.');\n    }\n    this.setFormatColumnsDataSource();\n  }\n  showDynamicAssetSettingsDialog() {\n    if (this.projectIsLocked || !this.validateActionableGridColumnConfig()) {\n      return;\n    }\n    this.showDynamicAssetSettings = true;\n  }\n  newDynamicAssetDialogChanged(visible) {\n    if (this.showDynamicAssetSettings !== visible) {\n      this.showDynamicAssetSettings = visible;\n    }\n    // to populate new form into list\n    this.getDatastoreAssets();\n  }\n  getDatastoreAssets() {\n    if (this.projectIsLocked) {\n      return;\n    }\n    this.datastoresService.getDatastoreAssets(this.selectedDatastore.id, Number(this.projectVersionId)).subscribe({\n      next: response => {\n        response.sort((a, b) => a.name.toLocaleLowerCase().charCodeAt(0) - b.name.toLocaleLowerCase().charCodeAt(0));\n        const datastoreForms = response.filter(asset => asset.type === DynamicAssetTypes.DynamicForm);\n        const datastoreConnectorApps = response.filter(asset => asset.type === DynamicAssetTypes.ConnectorApp);\n        // grouping the dynamic assets for the dropdown\n        this.assetsInfoGroups = [new AssetDropdownGroup('', DynamicAssetTypes.Default, [this.defaultAsset])];\n        if (datastoreForms.length > 0) {\n          this.assetsInfoGroups.push(new AssetDropdownGroup(\"Forms\", DynamicAssetTypes.DynamicForm, [...datastoreForms]));\n        }\n        if (datastoreConnectorApps.length > 0) {\n          this.assetsInfoGroups.push(new AssetDropdownGroup(\"Connector Screens\", DynamicAssetTypes.ConnectorApp, [...datastoreConnectorApps]));\n        }\n        // supporting backwards compatibility\n        const assetId = this.reportConfig.formatConfig?.dynamicAssetInfo?.id ?? this.reportConfig.formatConfig?.dynamicFormId;\n        if (assetId) {\n          this.selectedDynamicAsset = response.find(x => x.id === assetId);\n          this.populateAssetIdFieldOptions();\n        }\n      }\n    });\n  }\n  getProjectVersion() {\n    return this.projectsService.getProjectVersion(this.projectVersionId);\n  }\n  buildBreadcrumb() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      _this6.projectVersion = yield _this6.getProjectVersion();\n      if (_this6.projectVersion) {\n        const items = [{\n          label: `${_this6.projectVersion.project.name}: ${_this6.projectVersion.name}`,\n          routerLink: `/project/${_this6.projectId}/${_this6.projectVersionId}/settings`\n        }, {\n          label: 'Project Assets',\n          routerLink: `/project/${_this6.projectId}/${_this6.projectVersionId}/assets/manage`\n        }, {\n          label: _this6.reportConfig.reportName\n        }];\n        _this6.communicationService.updateBreadcrumb(items);\n      }\n    })();\n  }\n  setIsFavorite() {\n    this.isFavorite = this.allReportFavorites.some(f => f.assetId === this.reportConfig.reportId && !f.isApp);\n  }\n  onUpdateUserFavorite() {\n    const userFavorite = this.userFavoriteService.generateUserFavorite(this.reportConfig.projectId, this.reportConfig.projectVersionId, window.location.pathname, this.reportConfig.reportId, 'Report', !this.isFavorite, false);\n    this.communicationService.next({\n      fromAppName: this.communicationService.currentAppName,\n      toAppName: this.communicationService.navigationAppName,\n      eventType: 'upsertUserFavorite',\n      data: userFavorite\n    });\n  }\n  trackUserFavorites() {\n    this.userFavoriteSub = this.communicationService.userFavorite.subscribe(event => {\n      this.allReportFavorites = event.data;\n      this.setIsFavorite();\n    });\n  }\n  getVisualizations() {\n    this.tilesConfig = this.reportConfig?.formatConfig?.tilesConfig?.map(tileConfig => ({\n      id: tileConfig.tileId,\n      tileType: tileConfig.tileType,\n      name: tileConfig.name\n    }));\n    this.chartConfigs = this.reportConfig.formatConfig?.chartConfigs;\n  }\n  tilesListUpdated(tilesConfig) {\n    if (this.projectIsLocked) {\n      return;\n    }\n    this.tilesConfig = tilesConfig;\n    this.reportConfig.formatConfig.tilesConfig = tilesConfig.map(tile => ({\n      tileId: tile.id,\n      tileType: tile.tileType,\n      name: tile.name\n    }));\n  }\n  chartListUpdated(chartConfigs) {\n    if (this.projectIsLocked) {\n      return;\n    }\n    this.chartConfigs = chartConfigs;\n    this.reportConfig.formatConfig.chartConfigs = chartConfigs;\n  }\n  trackReportParameterChange() {\n    this.onReportParameterChange = this.reportadminservice.reportParameterChanged.subscribe(params => {\n      if (params) {\n        this.reportConfig.params = params;\n      }\n    });\n  }\n  getProfileProperties() {\n    this.userService.getCompleteUserProfilePropertyList().subscribe({});\n  }\n  onDynamicAssetChange() {\n    if (this.projectIsLocked) {\n      return;\n    }\n    this.reportConfig.formatConfig.dynamicAssetInfo = this.selectedDynamicAsset;\n    if (this.selectedDynamicAsset.type === DynamicAssetTypes.ConnectorApp) {\n      this.populateAssetIdFieldOptions();\n    }\n  }\n  populateAssetIdFieldOptions() {\n    this.idFieldOptions = this.selectedDatastore ? Object.keys(this.selectedDatastore.baseSchema.properties) : [];\n    if (this.reportConfig.formatConfig?.dynamicAssetInfo?.idField && this.idFieldOptions.includes(this.reportConfig.formatConfig?.dynamicAssetInfo?.idField) && this.selectedDynamicAsset.id === this.reportConfig.formatConfig?.dynamicAssetInfo?.id) {\n      return;\n    }\n    if (this.reportConfig.formatConfig?.dynamicAssetInfo) {\n      this.reportConfig.formatConfig.dynamicAssetInfo.idField = '';\n    }\n  }\n  emitParentGroupCommunicationEvent(parentGroup) {\n    const communicationEvent = parentGroup !== 'null' ? {\n      eventType: constants.EventType.UPDATE_PARENT_GROUP_TAG,\n      data: {\n        groupId: parentGroup.id,\n        assetId: this.reportConfig.reportId\n      }\n    } : {\n      eventType: constants.EventType.DEACTIVATE_ASSET_GROUP_TAG,\n      data: {\n        assetId: this.reportConfig.reportId\n      }\n    };\n    this.communicationService.next({\n      fromAppName: this.communicationService.currentAppName,\n      toAppName: this.communicationService.navigationAppName,\n      ...communicationEvent\n    });\n  }\n  onChangeCalendarView() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this7.reportConfig.calendarViewConfig) {\n        _this7.reportConfig.calendarViewConfig = new CalendarViewConfig();\n      }\n      _this7.reportConfig.calendarViewConfig.viewHashId = _this7.selectedDataView.columnsToShow?.length > 0 ? yield _this7.generateViewHashID() : 'none';\n    })();\n  }\n  generateViewHashID() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      if (_this8.selectedDataView.viewId === '0') {\n        return 'none';\n      }\n      return yield _this8.cryptoService.hashSHA256(_this8.selectedDataView.columnsToShow?.sort()?.toString());\n    })();\n  }\n  static {\n    this.ɵfac = function ReportSettingsComponent_Factory(t) {\n      return new (t || ReportSettingsComponent)(i0.ɵɵdirectiveInject(i1.ReportAdminService), i0.ɵɵdirectiveInject(i2.DatastoresService), i0.ɵɵdirectiveInject(i3.NotificationService), i0.ɵɵdirectiveInject(i4.ConfirmationService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i5.ProjectsService), i0.ɵɵdirectiveInject(i6.UserService), i0.ɵɵdirectiveInject(i7.UserFavoriteService), i0.ɵɵdirectiveInject(i8.FeatureFlagService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i9.CryptoService), i0.ɵɵdirectiveInject(CommunicationToken));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReportSettingsComponent,\n      selectors: [[\"app-report-settings\"]],\n      inputs: {\n        reportConfig: \"reportConfig\",\n        projectId: \"projectId\",\n        projectVersionId: \"projectVersionId\",\n        columnDefs: \"columnDefs\",\n        customerId: \"customerId\",\n        showHelp: \"showHelp\",\n        projectIsLocked: \"projectIsLocked\",\n        agentAssetConfig: \"agentAssetConfig\"\n      },\n      outputs: {\n        reportConfigOut: \"reportConfigOut\",\n        paramListUpdated: \"paramListUpdated\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 114,\n      vars: 95,\n      consts: [[\"form\", \"ngForm\"], [\"actionableGridConfigComponent\", \"\"], [\"dt\", \"\"], [\"styleClass\", \"flex-parent sticky\"], [1, \"p-toolbar-group-start\"], [1, \"pi\", \"sb-icon-nav-settings\"], [1, \"p-toolbar-group-end\", \"flex-child\"], [\"tooltipPosition\", \"top\", 3, \"onClick\", \"rounded\", \"icon\", \"pTooltip\"], [3, \"outlined\", \"helpLink\"], [\"tooltipPosition\", \"top\", 3, \"pTooltip\"], [\"icon\", \"pi pi-save\", \"name\", \"btnSave\", \"id\", \"btnSave\", 3, \"click\", \"rounded\", \"disabled\"], [1, \"card\", \"py-0\"], [\"severity\", \"info\", \"styleClass\", \"m-2\", \"text\", \"Review the settings found on this page and then save to finalize.\", 4, \"ngIf\"], [1, \"grid\", \"field\", \"mb-0\"], [1, \"col-12\", \"p-float-label\"], [\"id\", \"ReportName\", \"required\", \"\", \"maxlength\", \"100\", \"name\", \"name\", \"pInputText\", \"\", 3, \"ngModelChange\", \"ngModel\", \"pKeyFilter\", \"disabled\"], [\"for\", \"ReportName\"], [1, \"required-field\"], [\"id\", \"Description\", \"maxlength\", \"100\", \"name\", \"description\", \"pInputText\", \"\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"DisplayName\"], [\"class\", \"col-12 p-float-label\", 4, \"ngIf\"], [\"class\", \"col-12 radio-group mb-3\", 4, \"ngIf\"], [1, \"col-12\"], [\"id\", \"Active\", \"name\", \"active\", \"binary\", \"true\", 3, \"ngModelChange\", \"label\", \"ngModel\", \"disabled\"], [1, \"col-12\", \"mt-3\", \"p-float-label\", \"fake-field-readonly\"], [\"id\", \"reportIDFakeField\", 1, \"grid\", \"align-items-center\", \"mx-0\"], [\"for\", \"reportIDFakeField\"], [3, \"multiple\"], [\"header\", \"Connection Settings\", 3, \"selected\"], [1, \"grid\"], [\"class\", \"col-12 radio-group\", 4, \"ngIf\"], [1, \"col-12\", \"grid\", \"field\"], [4, \"ngIf\"], [\"header\", \"Grid Settings\", 3, \"selected\", 4, \"ngIf\"], [\"header\", \"Data Feed Settings\", 3, \"selected\", 4, \"ngIf\"], [\"header\", \"Format Columns/Data\", \"contentStyleClass\", \"grid field mx-0 max-w-unset block\", 3, \"selected\", 4, \"ngIf\"], [\"header\", \"Calendar View\", 3, \"selected\"], [\"header\", \"Visualization Details\", 3, \"selected\", 4, \"ngIf\"], [\"header\", \"Grid Chart Details\", 3, \"selected\", 4, \"ngIf\"], [\"header\", \"Chart Details\", 3, \"selected\", 4, \"ngIf\"], [\"header\", \"Format Columns/Data\", 3, \"selected\", 4, \"ngIf\"], [\"header\", \"Drill Through\", 3, \"selected\", 4, \"ngIf\"], [\"header\", \"Chat Agent\", 3, \"selected\"], [\"header\", \"Sharing\", 3, \"selected\"], [1, \"grid\", \"embedInputs\", \"field\"], [1, \"col-12\", \"p-float-label\", \"p-inputgroup\"], [\"pInputText\", \"\", \"type\", \"text\", \"id\", \"LinkToEmbed\", \"name\", \"LinkToEmbed\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [\"for\", \"LinkToEmbed\"], [1, \"p-inputgroup-addon\"], [1, \"pi\", \"pi-copy\", 3, \"click\"], [\"id\", \"HtmlToPaste\", \"type\", \"text\", \"pInputText\", \"\", \"name\", \"HtmlToPaste\", 3, \"ngModelChange\", \"disabled\", \"ngModel\"], [\"for\", \"HtmlToPaste\"], [\"header\", \"Security\", 3, \"selected\"], [1, \"grid\", \"field\"], [\"type\", \"button\", \"icon\", \"p-icon-group\", \"label\", \"Manage Report Access\", 3, \"disabled\", \"onClick\", 4, \"ngIf\"], [1, \"col-12\", \"mt-3\"], [\"binary\", \"true\", \"name\", \"allowAnonymous\", \"label\", \"Allow anonymous access\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"col-12\", \"p-float-label\", \"indented\", \"mt-1\", 3, \"ngClass\"], [\"inputId\", \"users\", \"name\", \"users\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"ngModelChange\", \"disabled\", \"ngClass\", \"options\", \"ngModel\", \"filter\"], [\"for\", \"users\"], [\"severity\", \"info\", \"text\", \"When a report is run anonymously the selected user profile and its corresponding security settings will be used to run the report.\"], [\"header\", \"Cache Settings\", 3, \"selected\"], [\"appendTo\", \"body\"], [\"id\", \"connectionTimeOut\", \"maxlength\", \"100\", \"name\", \"connectionTimeOut\", \"pInputText\", \"\", 3, \"ngModelChange\", \"keypress\", \"blur\", \"ngModel\", \"disabled\"], [\"for\", \"connectionTimeOut\"], [\"header\", \"Minimum Cache Refresh\", \"modal\", \"modal\", 3, \"visibleChange\", \"onHide\", \"visible\"], [\"type\", \"button\", \"label\", \"Ok\", \"icon\", \"pi pi-check\", 3, \"click\", \"outlined\"], [\"header\", \"Email Settings\", 3, \"selected\", 4, \"ngIf\"], [\"header\", \"Delete Report\", 3, \"visibleChange\", \"visible\", \"baseZIndex\", \"modal\"], [\"pTemplate\", \"footer\"], [\"header\", \"Select URL Substitution\", 3, \"visibleChange\", \"visible\", \"modal\"], [1, \"card\"], [1, \"col-6\"], [1, \"field-radiobutton\"], [\"name\", \"urlSubType\", \"value\", \"column\", \"inputId\", \"rdburlColumn\", 1, \"mb-1\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"rdbUrlColumn\"], [\"name\", \"urlSubType\", \"value\", \"projectVariable\", \"inputId\", \"rdburlProjectVariable\", 1, \"mb-1\", 3, \"ngModelChange\", \"ngModel\"], [\"for\", \"rdburlProjectVariable\"], [3, \"dialogVisibleChanged\", \"showDialog\", \"defaultName\", \"defaultTitle\", \"projectId\", \"projectVersionId\", \"actionableGridColumnsConfig\", \"datastoreId\", \"datastoreName\", \"dataViewId\"], [\"severity\", \"info\", \"styleClass\", \"m-2\", \"text\", \"Review the settings found on this page and then save to finalize.\"], [\"name\", \"parentGroup\", \"id\", \"parentGroup\", \"filterBy\", \"name\", \"appendTo\", \"body\", 3, \"ngModelChange\", \"ngModel\", \"disabled\", \"options\", \"ngClass\", \"filter\"], [\"pTemplate\", \"selectedItem\"], [\"for\", \"ParentGroup\"], [1, \"col-12\", \"radio-group\", \"mb-3\"], [1, \"text-xs\", \"text-primary-override\"], [\"class\", \"flex\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\"], [\"name\", \"category.name\", 1, \"mb-1\", 3, \"ngModelChange\", \"inputId\", \"value\", \"ngModel\", \"disabled\"], [1, \"mx-1\", 3, \"for\"], [1, \"col-12\", \"radio-group\"], [\"class\", \"mb-2 flex\", 4, \"ngFor\", \"ngForOf\"], [1, \"mb-2\", \"flex\"], [\"name\", \"connectorType\", 1, \"mb-1\", \"mr-1\", 3, \"ngModelChange\", \"onClick\", \"inputId\", \"value\", \"ngModel\", \"disabled\"], [3, \"for\"], [\"inputId\", \"connectors\", \"name\", \"connectors\", \"optionValue\", \"value\", 3, \"onChange\", \"ngModelChange\", \"options\", \"ngModel\", \"disabled\", \"ngClass\"], [\"for\", \"connectors\"], [\"inputId\", \"functions\", \"name\", \"functions\", \"optionValue\", \"value\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"disabled\", \"ngClass\"], [\"for\", \"functions\"], [\"inputId\", \"workflows\", \"name\", \"workflows\", \"optionLabel\", \"name\", \"optionValue\", \"id\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"disabled\", \"ngClass\"], [\"inputId\", \"forms\", \"name\", \"forms\", \"optionLabel\", \"name\", \"emptyMessage\", \"No datastores found\", 1, \"connection-settings-drop-down\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"disabled\", \"ngClass\"], [\"for\", \"forms\"], [\"class\", \"col-12 p-float-label p-inputgroup analytical-data-filter\", 4, \"ngIf\"], [1, \"col-12\", \"p-float-label\", \"p-inputgroup\", \"analytical-data-filter\"], [\"inputId\", \"formsFilter\", \"name\", \"formsFilter\", \"optionLabel\", \"name\", \"filterBy\", \"name\", 1, \"connection-settings-drop-down\", 3, \"onChange\", \"ngModelChange\", \"options\", \"ngModel\", \"disabled\", \"filter\", \"ngClass\"], [\"pTemplate\", \"item\"], [\"for\", \"formsFilter\"], [\"pRipple\", \"\", \"name\", \"addNew\", \"icon\", \"pi pi-plus\", \"pTooltip\", \"Add New View\", \"tooltipPosition\", \"top\", 3, \"onClick\", \"outlined\", \"disabled\"], [1, \"analytical-data-filter-dropdown\"], [\"pRipple\", \"\", \"name\", \"editView\", \"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit View\", \"tooltipPosition\", \"top\", 3, \"onClick\", \"text\"], [\"pRipple\", \"\", \"name\", \"deleteView\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete View\", \"tooltipPosition\", \"top\", \"severity\", \"danger\", 3, \"onClick\", \"text\"], [\"header\", \"Grid Settings\", 3, \"selected\"], [1, \"col-12\", \"p-field-checkbox\", \"my-2\"], [\"binary\", \"true\", \"name\", \"allowAddNewRow\", \"label\", \"Allow User to Add New Rows\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"id\", \"eventWorklow\", \"inputId\", \"ouwSettings\", \"name\", \"ouwSettings\", \"optionLabel\", \"name\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"showClear\", \"disabled\", \"ngClass\"], [\"for\", \"eventWorklow\"], [\"class\", \"col-12 p-float-label p-inputgroup\", 4, \"ngIf\"], [1, \"w-full\", 3, \"reportConfigChange\", \"reportConfig\", \"datastore\", \"projectId\", \"projectVersionId\", \"disabled\", \"actionableGridConfig\"], [\"binary\", \"true\", \"name\", \"enablePagination\", \"label\", \"Enable Pagination\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"class\", \"col-12 p-field-checkbox my-2\", 4, \"ngIf\"], [\"inputId\", \"inlineForm\", \"name\", \"inlineForm\", \"optionLabel\", \"name\", \"filter\", \"true\", \"filterBy\", \"name\", \"group\", \"true\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"disabled\", \"ngClass\"], [\"pTemplate\", \"group\"], [\"for\", \"inlineForm\"], [\"pRipple\", \"\", \"name\", \"refreshList\", \"icon\", \"pi p-icon-refresh\", \"pTooltip\", \"Refresh List\", \"tooltipPosition\", \"top\", 3, \"onClick\", \"outlined\", \"disabled\"], [\"pRipple\", \"\", \"name\", \"generateAsset\", \"icon\", \"pi pi-plus\", \"pTooltip\", \"Add New Asset\", \"tooltipPosition\", \"top\", 3, \"onClick\", \"outlined\", \"disabled\"], [1, \"sb\", 3, \"ngClass\"], [\"class\", \"font-bold\", 4, \"ngIf\"], [1, \"font-bold\"], [1, \"ml-4\"], [\"inputId\", \"idField\", \"name\", \"idField\", 3, \"ngModelChange\", \"options\", \"disabled\", \"ngModel\", \"autoDisplayFirst\", \"ngClass\"], [\"for\", \"idField\"], [\"binary\", \"true\", \"name\", \"paginationAutoPaging\", \"label\", \"Pagination Auto Paging\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"inputId\", \"defaultPageSize\", \"name\", \"defaultPageSize\", \"optionLabel\", \"key\", \"optionValue\", \"value\", 3, \"ngModelChange\", \"options\", \"disabled\", \"ngModel\", \"autoDisplayFirst\", \"ngClass\"], [\"for\", \"pageSize\"], [\"header\", \"Data Feed Settings\", 3, \"selected\"], [\"inputId\", \"queryCategory\", \"name\", \"queryCategory\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"options\", \"disabled\", \"ngModel\", \"ngClass\", \"ngModelChange\", \"onChange\", 4, \"ngIf\"], [\"for\", \"queryCategory\"], [\"inputId\", \"queryCategory\", \"name\", \"queryCategory\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"ngModelChange\", \"onChange\", \"options\", \"disabled\", \"ngModel\", \"ngClass\"], [\"id\", \"queryCategory\", \"maxlength\", \"100\", \"name\", \"queryCategory\", \"pInputText\", \"\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"inputId\", \"queryName\", \"name\", \"queryName\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"ngModelChange\", \"onChange\", \"disabled\", \"options\", \"ngModel\", \"ngClass\"], [\"for\", \"queryName\"], [\"id\", \"queryName\", \"maxlength\", \"100\", \"name\", \"queryName\", \"pInputText\", \"\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"header\", \"Format Columns/Data\", \"contentStyleClass\", \"grid field mx-0 max-w-unset block\", 3, \"selected\"], [1, \"section-description\"], [3, \"href\"], [1, \"sb\", \"sb-icon-link\"], [3, \"columnsConfig\", \"projectVersionId\", \"disabled\"], [\"id\", \"enableCalendarView\", \"name\", \"enableCalendarView\", \"binary\", \"true\", \"tooltipPosition\", \"top\", 1, \"mb-2\", 3, \"ngModelChange\", \"onChange\", \"label\", \"ngModel\", \"disabled\", \"pTooltip\"], [3, \"calendarViewConfig\", \"selectedDataView\", \"baseSchema\"], [\"header\", \"Visualization Details\", 3, \"selected\"], [3, \"tilesListUpdated\", \"tilesConfig\", \"projectId\", \"projectVersionId\", \"datastoreId\", \"selectedDataViewId\", \"advancedJsonEditFlag\", \"disabled\"], [\"header\", \"Grid Chart Details\", 3, \"selected\"], [3, \"linkedChartsUpdated\", \"chartConfigs\", \"disabled\"], [\"header\", \"Chart Details\", 3, \"selected\"], [\"inputId\", \"chartTypes\", \"optionLabel\", \"name\", \"optionValue\", \"key\", \"name\", \"chartType\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\", \"disabled\", \"ngClass\"], [\"for\", \"firstname\"], [\"id\", \"float-input\", \"rows\", \"5\", \"cols\", \"70\", \"pInputTextarea\", \"\", \"name\", \"chartConfigDetails\", 3, \"ngModelChange\", \"ngModel\", \"ngClass\", \"disabled\"], [\"severity\", \"warn\", \"text\", \"It is not recommended to exceed 20 rows per chart as exceeding this may cause display issues.\", \"styleClass\", \"max-w-30rem\"], [\"inputId\", \"chartSubTypes\", \"optionLabel\", \"name\", \"optionValue\", \"key\", \"name\", \"chartSubType\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"disabled\", \"ngClass\"], [\"header\", \"Format Columns/Data\", 3, \"selected\"], [1, \"grid\", \"field\", \"max-w-full\", \"m-0\"], [1, \"col-12\", \"p-0\"], [1, \"report-setup\", 3, \"value\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [3, \"pSelectableRow\"], [1, \"py-0\"], [\"pInputText\", \"\", \"type\", \"text\", 1, \"no-label\", \"w-10rem\", 3, \"name\", \"ngModel\", \"disabled\"], [1, \"p-0\", \"flex\", \"m-0\"], [1, \"p-float-label\", \"col-12\", \"w-10rem\"], [\"inputId\", \"format\", \"optionLabel\", \"name\", \"optionValue\", \"key\", \"appendTo\", \"body\", 3, \"ngModelChange\", \"onChange\", \"name\", \"autoDisplayFirst\", \"options\", \"ngModel\", \"ngClass\", \"disabled\"], [\"class\", \"p-float-label col-12 w-7rem\", 4, \"ngIf\"], [\"class\", \"p-float-label col-12 w-8rem\", 4, \"ngIf\"], [1, \"p-float-label\", \"col-12\", \"w-7rem\"], [\"mode\", \"decimal\", \"inputId\", \"minmax-buttons\", \"inputStyleClass\", \"min-w-0\", 3, \"ngModelChange\", \"name\", \"showButtons\", \"disabled\", \"ngModel\", \"min\", \"max\", \"ngClass\"], [1, \"p-float-label\", \"col-12\", \"w-8rem\"], [\"inputId\", \"format\", \"optionLabel\", \"code\", \"optionValue\", \"code\", \"appendTo\", \"body\", 3, \"ngModelChange\", \"name\", \"options\", \"ngModel\", \"disabled\", \"ngClass\"], [\"header\", \"Drill Through\", 3, \"selected\"], [\"styleClass\", \"p-datatable-header-no-background p-datatable-no-margin\", 1, \"drill__table\", \"flex\", 3, \"value\"], [\"pTemplate\", \"caption\"], [\"styleClass\", \"p-toolbar-secondary\"], [1, \"p-toolbar-group-end\"], [\"pRipple\", \"\", \"name\", \"clearSelectedDrillThroughs\", \"icon\", \"pi pi-trash\", \"severity\", \"danger\", 3, \"click\", \"disabled\", \"outlined\", \"rounded\"], [\"pRipple\", \"\", \"name\", \"addNew\", \"icon\", \"pi pi-plus\", 3, \"click\", \"outlined\", \"rounded\", \"disabled\"], [1, \"drill-head\"], [\"pSortableColumn\", \"displayColumn\", 1, \"w-3rem\", \"p-0\"], [\"pSortableColumn\", \"displayColumn\", 1, \"w-9rem\"], [\"pSortableColumn\", \"urlAlias\"], [\"pSortableColumn\", \"urlSubstitution\", 1, \"w-14rem\"], [\"pSortableColumn\", \"openIn\", 1, \"w-11rem\"], [\"pSortableColumn\", \"gridGeneratedLink\"], [1, \"drill-table-custom-tr\"], [1, \"p-element\", \"ng-untouched\", \"ng-pristine\", \"ng-valid\", 3, \"onChange\", \"binary\", \"name\", \"disabled\"], [1, \"p-0\"], [\"optionValue\", \"value\", \"optionLabel\", \"label\", \"appendTo\", \"body\", 3, \"ngModelChange\", \"name\", \"options\", \"ngModel\", \"disabled\", \"ngClass\"], [\"pInputText\", \"\", \"type\", \"text\", 3, \"ngModelChange\", \"focusout\", \"name\", \"disabled\", \"ngModel\"], [\"severity\", \"info\", \"text\", \"eg: http://xxxx.com?param={0}&param={1}\"], [1, \"drill-url-substitution\", \"p-0\"], [3, \"value\"], [\"appendTo\", \"body\", \"optionValue\", \"code\", \"optionLabel\", \"name\", 3, \"ngModelChange\", \"name\", \"options\", \"ngModel\", \"disabled\", \"ngClass\"], [1, \"drill-generated-link\", \"px-2\", \"py-1\"], [1, \"px-2\", \"py-1\"], [\"pRipple\", \"\", \"icon\", \"pi pi-trash\", \"severity\", \"danger\", 3, \"click\", \"text\", \"disabled\"], [1, \"drill-url-substitution-tr\"], [1, \"drill-url\", \"p-0\"], [1, \"col-12\", \"p-inputgroup\", \"p-float-label\"], [\"pInputText\", \"\", \"type\", \"text\", 3, \"ngModelChange\", \"focusout\", \"name\", \"ngModel\", \"ngModelOptions\", \"disabled\"], [\"pRipple\", \"\", \"icon\", \"pi pi-pencil\", 3, \"onClick\", \"name\", \"outlined\", \"disabled\"], [\"pRipple\", \"\", \"icon\", \"pi pi-trash\", 3, \"onClick\", \"name\", \"outlined\", \"disabled\"], [\"pRipple\", \"\", \"icon\", \"pi pi-plus\", 3, \"onClick\", \"name\", \"outlined\", \"disabled\"], [\"id\", \"agentAssetInstructions\", \"name\", \"agentAssetInstructions\", \"pInputTextarea\", \"\", 1, \"min-h-5rem\", 3, \"ngModelChange\", \"autoResize\", \"ngModel\", \"disabled\"], [\"for\", \"agentAssetInstructions\"], [\"id\", \"agentAssetSchema\", \"name\", \"agentAssetSchema\", \"pInputTextarea\", \"\", 1, \"min-h-5rem\", 3, \"ngModelChange\", \"autoResize\", \"ngModel\", \"disabled\"], [\"for\", \"agentAssetSchema\"], [\"id\", \"agentAssetWorkflowId\", \"maxlength\", \"50\", \"name\", \"agentAssetWorkflowId\", \"pInputText\", \"\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"agentAssetWorkflowId\"], [\"id\", \"agentAssetStartMessage\", \"maxlength\", \"50\", \"name\", \"agentAssetStartMessage\", \"pInputText\", \"\", 3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"for\", \"agentAssetStartMessage\"], [\"type\", \"button\", \"icon\", \"p-icon-group\", \"label\", \"Manage Report Access\", 3, \"onClick\", \"disabled\"], [\"header\", \"Email Settings\", 3, \"selected\"], [\"id\", \"emailLimit\", \"maxlength\", \"100\", \"name\", \"emailLimit\", \"pInputText\", \"\", 3, \"ngModelChange\", \"keypress\", \"blur\", \"ngModel\", \"disabled\"], [\"for\", \"emailLimit\"], [\"type\", \"button\", \"severity\", \"danger\", \"label\", \"Delete\", 3, \"onClick\", \"disabled\"], [\"type\", \"button\", \"severity\", \"secondary\", \"label\", \"Cancel\", 3, \"onClick\", \"outlined\"], [\"optionLabel\", \"label\", \"filterBy\", \"label\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"filter\", \"showClear\"], [2, \"float\", \"right\"], [\"tooltipPosition\", \"top\", \"tooltipZIndex\", \"99999\", 1, \"pi\", \"pi-info-circle\", 3, \"pTooltip\"], [\"pRipple\", \"\", \"type\", \"button\", \"label\", \"Save\", 3, \"click\"], [\"pRipple\", \"\", \"type\", \"button\", \"label\", \"Cancel\", \"severity\", \"secondary\", 3, \"click\", \"outlined\"]],\n      template: function ReportSettingsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"form\", null, 0)(2, \"p-toolbar\", 3)(3, \"div\", 4)(4, \"h3\");\n          i0.ɵɵelement(5, \"span\", 5);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-button\", 7);\n          i0.ɵɵlistener(\"onClick\", function ReportSettingsComponent_Template_p_button_onClick_8_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onUpdateUserFavorite());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, ReportSettingsComponent_Conditional_9_Template, 1, 2, \"app-help-launcher\", 8);\n          i0.ɵɵelementStart(10, \"span\", 9)(11, \"p-button\", 10);\n          i0.ɵɵlistener(\"click\", function ReportSettingsComponent_Template_p_button_click_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onClickSave());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"h2\");\n          i0.ɵɵtext(13, \"General Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 11);\n          i0.ɵɵtemplate(15, ReportSettingsComponent_p_message_15_Template, 1, 0, \"p-message\", 12);\n          i0.ɵɵelementStart(16, \"div\", 13)(17, \"div\", 14)(18, \"input\", 15);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_Template_input_ngModelChange_18_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.reportConfig.reportName, $event) || (ctx.reportConfig.reportName = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"label\", 16);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementStart(21, \"span\", 17);\n          i0.ɵɵtext(22, \"*\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 14)(24, \"input\", 18);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_Template_input_ngModelChange_24_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.reportConfig.description, $event) || (ctx.reportConfig.description = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"label\", 19);\n          i0.ɵɵtext(26, \"Display Name\");\n          i0.ɵɵelementStart(27, \"span\", 17);\n          i0.ɵɵtext(28, \"*\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(29, ReportSettingsComponent_div_29_Template, 5, 7, \"div\", 20)(30, ReportSettingsComponent_div_30_Template, 4, 1, \"div\", 21);\n          i0.ɵɵelementStart(31, \"div\", 22)(32, \"p-checkbox\", 23);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_Template_p_checkbox_ngModelChange_32_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.reportConfig.isActive, $event) || (ctx.reportConfig.isActive = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 24)(34, \"span\", 25);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"label\", 26);\n          i0.ɵɵtext(37, \"Report ID\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(38, \"p-accordion\", 27)(39, \"p-accordionTab\", 28)(40, \"div\", 29);\n          i0.ɵɵtemplate(41, ReportSettingsComponent_div_41_Template, 4, 1, \"div\", 30);\n          i0.ɵɵelementStart(42, \"div\", 31);\n          i0.ɵɵtemplate(43, ReportSettingsComponent_ng_container_43_Template, 9, 12, \"ng-container\", 32)(44, ReportSettingsComponent_ng_container_44_Template, 3, 6, \"ng-container\", 32)(45, ReportSettingsComponent_ng_container_45_Template, 6, 7, \"ng-container\", 32);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(46, ReportSettingsComponent_p_accordionTab_46_Template, 16, 28, \"p-accordionTab\", 33)(47, ReportSettingsComponent_p_accordionTab_47_Template, 9, 5, \"p-accordionTab\", 34)(48, ReportSettingsComponent_p_accordionTab_48_Template, 12, 5, \"p-accordionTab\", 35)(49, ReportSettingsComponent_Conditional_49_Template, 5, 6, \"p-accordionTab\", 36)(50, ReportSettingsComponent_p_accordionTab_50_Template, 2, 8, \"p-accordionTab\", 37)(51, ReportSettingsComponent_p_accordionTab_51_Template, 2, 3, \"p-accordionTab\", 38)(52, ReportSettingsComponent_p_accordionTab_52_Template, 12, 13, \"p-accordionTab\", 39)(53, ReportSettingsComponent_p_accordionTab_53_Template, 7, 2, \"p-accordionTab\", 40)(54, ReportSettingsComponent_p_accordionTab_54_Template, 8, 2, \"p-accordionTab\", 41)(55, ReportSettingsComponent_Conditional_55_Template, 19, 11, \"p-accordionTab\", 42);\n          i0.ɵɵelementStart(56, \"p-accordionTab\", 43)(57, \"div\", 44)(58, \"div\", 45)(59, \"input\", 46);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_Template_input_ngModelChange_59_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.reportConfig.url, $event) || (ctx.reportConfig.url = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"label\", 47);\n          i0.ɵɵtext(61);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"span\", 48)(63, \"i\", 49);\n          i0.ɵɵlistener(\"click\", function ReportSettingsComponent_Template_i_click_63_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.copyToClipboard(ctx.reportConfig.url));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(64, \"div\", 45)(65, \"input\", 50);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_Template_input_ngModelChange_65_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.reportConfig.htmlCode, $event) || (ctx.reportConfig.htmlCode = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"label\", 51);\n          i0.ɵɵtext(67);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"span\", 48)(69, \"i\", 49);\n          i0.ɵɵlistener(\"click\", function ReportSettingsComponent_Template_i_click_69_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.copyToClipboard(ctx.reportConfig.htmlCode));\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(70, \"p-accordionTab\", 52)(71, \"div\", 53);\n          i0.ɵɵtemplate(72, ReportSettingsComponent_p_button_72_Template, 1, 1, \"p-button\", 54);\n          i0.ɵɵelementStart(73, \"div\", 55)(74, \"p-checkbox\", 56);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_Template_p_checkbox_ngModelChange_74_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.allowAnonymous, $event) || (ctx.allowAnonymous = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 57)(76, \"p-dropdown\", 58);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_Template_p_dropdown_ngModelChange_76_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedUser, $event) || (ctx.selectedUser = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"label\", 59);\n          i0.ɵɵtext(78, \"Proxy User\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(79, \"p-message\", 60);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(80, \"p-accordionTab\", 61)(81, \"div\", 53)(82, \"div\", 14);\n          i0.ɵɵelement(83, \"p-confirmDialog\", 62);\n          i0.ɵɵelementStart(84, \"input\", 63);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_Template_input_ngModelChange_84_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedConnectionTimeOut, $event) || (ctx.selectedConnectionTimeOut = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keypress\", function ReportSettingsComponent_Template_input_keypress_84_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.numberOnly($event));\n          })(\"blur\", function ReportSettingsComponent_Template_input_blur_84_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.checkTimeOutValid());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"label\", 64);\n          i0.ɵɵtext(86, \"Data Cache Refresh Time (in seconds)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"p-dialog\", 65);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function ReportSettingsComponent_Template_p_dialog_visibleChange_87_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.minimunCache, $event) || (ctx.minimunCache = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onHide\", function ReportSettingsComponent_Template_p_dialog_onHide_87_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.hideModal());\n          });\n          i0.ɵɵtext(88, \" Minimum Cache Refresh time allowed is 5 seconds, It is not recommended to use times under 60 seconds. \");\n          i0.ɵɵelementStart(89, \"p-footer\")(90, \"p-button\", 66);\n          i0.ɵɵlistener(\"click\", function ReportSettingsComponent_Template_p_button_click_90_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.hideModal());\n          });\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(91, ReportSettingsComponent_p_accordionTab_91_Template, 7, 6, \"p-accordionTab\", 67);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(92, \"p-dialog\", 68);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function ReportSettingsComponent_Template_p_dialog_visibleChange_92_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.displayModal, $event) || (ctx.displayModal = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(93, \"div\", 29)(94, \"p\");\n          i0.ɵɵtext(95, \"Do you want to delete the report...?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(96, ReportSettingsComponent_ng_template_96_Template, 2, 2, \"ng-template\", 69);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"p-dialog\", 70);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function ReportSettingsComponent_Template_p_dialog_visibleChange_97_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.displayURLSubstitution, $event) || (ctx.displayURLSubstitution = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(98, \"div\", 71)(99, \"div\", 53)(100, \"div\", 72)(101, \"div\", 73)(102, \"p-radioButton\", 74);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_Template_p_radioButton_ngModelChange_102_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.urlSubType, $event) || (ctx.urlSubType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"label\", 75);\n          i0.ɵɵtext(104, \"Column\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(105, \"div\", 72)(106, \"div\", 73)(107, \"p-radioButton\", 76);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function ReportSettingsComponent_Template_p_radioButton_ngModelChange_107_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.urlSubType, $event) || (ctx.urlSubType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"label\", 77);\n          i0.ɵɵtext(109, \"Project Variable\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(110, ReportSettingsComponent_div_110_Template, 6, 4, \"div\", 20)(111, ReportSettingsComponent_div_111_Template, 6, 4, \"div\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(112, ReportSettingsComponent_ng_template_112_Template, 2, 1, \"ng-template\", 69);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"app-new-dynamic-asset\", 78);\n          i0.ɵɵlistener(\"dialogVisibleChanged\", function ReportSettingsComponent_Template_app_new_dynamic_asset_dialogVisibleChanged_113_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.newDynamicAssetDialogChanged($event));\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", ctx.reportConfig.reportType !== \"actionablegrid\" ? \"Report Settings\" : \"Grid Settings\", \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"rounded\", true)(\"icon\", ctx.isFavorite ? \"pi pi-star-fill\" : \"pi pi-star\")(\"pTooltip\", ctx.isFavorite ? \"Remove Favorite\" : \"Add Favorite\");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(9, ctx.reportConfig.reportType === \"actionablegrid\" ? 9 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate(\"pTooltip\", ctx.projectIsLocked ? \"Project is locked\" : \"Save\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"rounded\", true)(\"disabled\", ctx.isSaveDisabled || ctx.projectIsLocked);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.showHelp);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.reportConfig.reportName);\n          i0.ɵɵproperty(\"pKeyFilter\", ctx.dirFilterRegExp)(\"disabled\", ctx.projectIsLocked);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.reportConfig.reportType !== \"actionablegrid\" ? \"Report Name\" : \"Name\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.reportConfig.description);\n          i0.ɵɵproperty(\"disabled\", ctx.projectIsLocked);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.groupTagFlag);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.reportConfig.reportType !== \"actionablegrid\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.nameLabelMap == null ? null : ctx.nameLabelMap.active);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.reportConfig.isActive);\n          i0.ɵɵproperty(\"disabled\", ctx.projectIsLocked);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.reportConfig.reportId);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"multiple\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"selected\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.reportConfig.reportType !== \"actionablegrid\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", (ctx.selectedConnectorType == null ? null : ctx.selectedConnectorType.key) === \"connector\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.selectedConnectorType == null ? null : ctx.selectedConnectorType.key) === \"workflow\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.selectedConnectorType == null ? null : ctx.selectedConnectorType.key) === \"forms\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.reportConfig.reportType === \"actionablegrid\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.selectedConnectorType == null ? null : ctx.selectedConnectorType.key) !== \"forms\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.reportConfig.reportType === ctx.reportType.ActionableGrid && ctx.selectedUserConfiguredFormat);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(49, (ctx.reportConfig == null ? null : ctx.reportConfig.reportType) === ctx.reportType.ActionableGrid && ctx.calendarViewFlag ? 49 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.reportConfig.reportType === ctx.reportType.ActionableGrid && ctx.visualizationFlag && ctx.reportConfig.formatConfig);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.reportConfig.reportType === ctx.reportType.ActionableGrid && ctx.gridChartsFlag && ctx.reportConfig.formatConfig);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedCategory === \"chart\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.formatColumns.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.urlColumns.length > 0 && ctx.selectedCategory === \"grid\");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(55, ctx.reportConfig.reportType === \"actionablegrid\" && ctx.gridAgentChatFlag ? 55 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"selected\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", true);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.reportConfig.url);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.nameLabelMap.LinkToEmbed);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", true);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.reportConfig.htmlCode);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.nameLabelMap.HtmlToPaste);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"selected\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasManagerPermissions);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.allowAnonymous);\n          i0.ɵɵproperty(\"disabled\", ctx.projectIsLocked);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(86, _c0, !ctx.allowAnonymous, !ctx.allowAnonymous));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", !ctx.allowAnonymous || ctx.projectIsLocked)(\"ngClass\", i0.ɵɵpureFunction1(89, _c1, !ctx.allowAnonymous || ctx.projectIsLocked))(\"options\", ctx.users);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedUser);\n          i0.ɵɵproperty(\"filter\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"selected\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(91, _c2));\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedConnectionTimeOut);\n          i0.ɵɵproperty(\"disabled\", ctx.projectIsLocked);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(92, _c2));\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.minimunCache);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"outlined\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmailSettings);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(93, _c3));\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.displayModal);\n          i0.ɵɵproperty(\"baseZIndex\", 10000)(\"modal\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(94, _c4));\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.displayURLSubstitution);\n          i0.ɵɵproperty(\"modal\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.urlSubType);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.urlSubType);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.urlSubType === \"column\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.urlSubType === \"projectVariable\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"showDialog\", ctx.showDynamicAssetSettings)(\"defaultName\", ctx.reportConfig == null ? null : ctx.reportConfig.reportName)(\"defaultTitle\", ctx.reportConfig == null ? null : ctx.reportConfig.description)(\"projectId\", ctx.projectId)(\"projectVersionId\", ctx.projectVersionId)(\"actionableGridColumnsConfig\", ctx.reportConfig == null ? null : ctx.reportConfig.formatConfig == null ? null : ctx.reportConfig.formatConfig.actionableGridColumnsConfig)(\"datastoreId\", ctx.selectedDatastore == null ? null : ctx.selectedDatastore.id)(\"datastoreName\", ctx.selectedDatastore == null ? null : ctx.selectedDatastore.name)(\"dataViewId\", (ctx.selectedDataView == null ? null : ctx.selectedDataView.viewId) === \"0\" ? null : ctx.selectedDataView == null ? null : ctx.selectedDataView.viewId);\n        }\n      },\n      dependencies: [FormsModule, i10.ɵNgNoValidate, i10.DefaultValueAccessor, i10.NgControlStatus, i10.NgControlStatusGroup, i10.RequiredValidator, i10.MaxLengthValidator, i10.NgModel, i10.NgForm, ToolbarModule, i11.Toolbar, i4.Footer, i4.PrimeTemplate, ButtonModule, i12.Button, TooltipModule, i13.Tooltip, NgIf, MessageModule, i14.UIMessage, InputTextModule, i15.InputText, InputTextareaModule, i16.InputTextarea, KeyFilterModule, i17.KeyFilter, DropdownModule, i18.Dropdown, NgFor, RadioButtonModule, i19.RadioButton, CheckboxModule, i20.Checkbox, AccordionModule, i21.Accordion, i21.AccordionTab, NgClass, ConditionalFormsComponent, ConditionalFormattingComponent, ActionableGridConfigComponent, LinkedTilesComponent, LinkedChartsComponent, TableModule, i22.Table, i22.SortableColumn, i22.SelectableRow, InputNumberModule, i23.InputNumber, ConfirmDialogModule, i24.ConfirmDialog, DialogModule, i25.Dialog, NewDynamicAssetComponent, CalendarViewSettingsComponent, HelpLauncherComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "constants", "CommunicationToken", "_isNumberValue", "UrlSub", "Subscription", "take", "ConnectorTypes", "DynamicAssetTypes", "ReportType", "ActionableGridColumnType", "deepCopy", "ColumnConfig", "GridEventType", "GridTargetType", "FeatureFlag", "DataView", "JsonDataTypes", "JsonStringFormats", "AssetDropdownGroup", "NewDynamicAssetComponent", "DialogModule", "ConfirmDialogModule", "InputNumberModule", "TableModule", "LinkedChartsComponent", "LinkedTilesComponent", "ActionableGridConfigComponent", "ConditionalFormsComponent", "AccordionModule", "CheckboxModule", "RadioButtonModule", "DropdownModule", "KeyFilterModule", "InputTextModule", "InputTextareaModule", "MessageModule", "NgIf", "<PERSON><PERSON><PERSON>", "Ng<PERSON><PERSON>", "TooltipModule", "ButtonModule", "ToolbarModule", "FormsModule", "CalendarViewSettingsComponent", "CalendarViewConfig", "HelpLauncherComponent", "HelpLinks", "ConditionalFormattingComponent", "i0", "ɵɵelement", "ɵɵpropertyInterpolate", "ctx_r1", "helpLink", "ɵɵproperty", "ɵɵtext", "ɵɵtextInterpolate1", "tmp_3_0", "parentGroup", "dropdownDisplayName", "undefined", "ɵɵelementStart", "ɵɵtwoWayListener", "ReportSettingsComponent_div_29_Template_p_dropdown_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "ɵɵresetView", "ɵɵtemplate", "ReportSettingsComponent_div_29_ng_template_2_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtwoWayProperty", "projectIsLocked", "groupTagOptions", "ɵɵpureFunction1", "_c1", "ReportSettingsComponent_div_30_div_3_Template_p_radioButton_ngModelChange_1_listener", "_r4", "selectedCate<PERSON><PERSON>", "category_r5", "key", "ɵɵtextInterpolate", "name", "ReportSettingsComponent_div_30_div_3_Template", "categories", "ReportSettingsComponent_div_41_div_3_Template_p_radioButton_ngModelChange_1_listener", "_r6", "selectedConnectorType", "ɵɵlistener", "ReportSettingsComponent_div_41_div_3_Template_p_radioButton_onClick_1_listener", "onConnectorTypeChange", "connectorType_r7", "ReportSettingsComponent_div_41_div_3_Template", "connectorTypes", "ɵɵelementContainerStart", "ReportSettingsComponent_ng_container_43_Template_p_dropdown_onChange_2_listener", "_r8", "onConnectorChange", "ReportSettingsComponent_ng_container_43_Template_p_dropdown_ngModelChange_2_listener", "selectedConnector", "ReportSettingsComponent_ng_container_43_Template_p_dropdown_ngModelChange_6_listener", "selectedFunction", "connectors", "functions", "ReportSettingsComponent_ng_container_44_Template_p_dropdown_ngModelChange_2_listener", "_r9", "selectedWorkflow", "workflows", "ReportSettingsComponent_ng_container_45_div_5_ng_template_2_div_3_Template_p_button_onClick_1_listener", "_r12", "dataView_r13", "$implicit", "openAnalyticsFilterModal", "formMode", "EDIT", "ReportSettingsComponent_ng_container_45_div_5_ng_template_2_div_3_Template_p_button_onClick_2_listener", "deleteView", "ReportSettingsComponent_ng_container_45_div_5_ng_template_2_div_3_Template", "viewId", "ReportSettingsComponent_ng_container_45_div_5_Template_p_dropdown_onChange_1_listener", "_r11", "onSelectedViewChange", "ReportSettingsComponent_ng_container_45_div_5_Template_p_dropdown_ngModelChange_1_listener", "selected<PERSON><PERSON><PERSON>ie<PERSON>", "ReportSettingsComponent_ng_container_45_div_5_ng_template_2_Template", "ReportSettingsComponent_ng_container_45_div_5_Template_p_button_onClick_6_listener", "CREATE", "dataViews", "ReportSettingsComponent_ng_container_45_Template_p_dropdown_ngModelChange_2_listener", "_r10", "selectedDatastore", "ReportSettingsComponent_ng_container_45_Template_p_dropdown_onChange_2_listener", "onSelectedDatastoreChange", "ReportSettingsComponent_ng_container_45_div_5_Template", "datastores", "ɵɵpureFunction2", "_c5", "selectedDynamicAsset", "type", "ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_2_span_1_Template", "group_r16", "label", "ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_3_div_0_Template", "asset_r17", "ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_4_span_1_Template", "ReportSettingsComponent_p_accordionTab_46_div_8_Template_p_dropdown_ngModelChange_1_listener", "_r15", "ReportSettingsComponent_p_accordionTab_46_div_8_Template_p_dropdown_onChange_1_listener", "onDynamicAssetChange", "ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_2_Template", "ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_3_Template", "ReportSettingsComponent_p_accordionTab_46_div_8_ng_template_4_Template", "ReportSettingsComponent_p_accordionTab_46_div_8_Template_p_button_onClick_8_listener", "getDatastoreAssets", "ReportSettingsComponent_p_accordionTab_46_div_8_Template_p_button_onClick_10_listener", "showDynamicAssetSettingsDialog", "assetsInfoGroups", "ReportSettingsComponent_p_accordionTab_46_div_9_Template_p_dropdown_ngModelChange_1_listener", "_r18", "reportConfig", "formatConfig", "dynamicAssetInfo", "idField", "idFieldOptions", "ReportSettingsComponent_p_accordionTab_46_div_14_Template_p_checkbox_ngModelChange_1_listener", "_r19", "paginationAutoPaging", "ReportSettingsComponent_p_accordionTab_46_div_15_Template_p_dropdown_ngModelChange_1_listener", "_r20", "defaultPageSize", "pageSizes", "ReportSettingsComponent_p_accordionTab_46_Template_p_checkbox_ngModelChange_3_listener", "_r14", "allowAddNewRow", "ReportSettingsComponent_p_accordionTab_46_Template_p_dropdown_ngModelChange_5_listener", "selectedSavedEventWorkflow", "ReportSettingsComponent_p_accordionTab_46_div_8_Template", "ReportSettingsComponent_p_accordionTab_46_div_9_Template", "ReportSettingsComponent_p_accordionTab_46_Template_app_conditional_forms_reportConfigChange_10_listener", "ReportSettingsComponent_p_accordionTab_46_Template_app_conditional_formatting_reportConfigChange_11_listener", "ReportSettingsComponent_p_accordionTab_46_Template_p_checkbox_ngModelChange_13_listener", "enablePagination", "ReportSettingsComponent_p_accordionTab_46_div_14_Template", "ReportSettingsComponent_p_accordionTab_46_div_15_Template", "projectId", "projectVersionId", "selectedUserConfiguredFormat", "ReportSettingsComponent_p_accordionTab_47_p_dropdown_3_Template_p_dropdown_ngModelChange_0_listener", "_r21", "selected<PERSON><PERSON>yCategor<PERSON>", "ReportSettingsComponent_p_accordionTab_47_p_dropdown_3_Template_p_dropdown_onChange_0_listener", "onCategoryChange", "queryCategory", "ReportSettingsComponent_p_accordionTab_47_div_6_Template_input_ngModelChange_1_listener", "_r22", "ReportSettingsComponent_p_accordionTab_47_div_7_Template_p_dropdown_ngModelChange_1_listener", "_r23", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ReportSettingsComponent_p_accordionTab_47_div_7_Template_p_dropdown_onChange_1_listener", "getColumnNames", "queryName", "ReportSettingsComponent_p_accordionTab_47_div_8_Template_input_ngModelChange_1_listener", "_r24", "ReportSettingsComponent_p_accordionTab_47_p_dropdown_3_Template", "ReportSettingsComponent_p_accordionTab_47_div_6_Template", "ReportSettingsComponent_p_accordionTab_47_div_7_Template", "ReportSettingsComponent_p_accordionTab_47_div_8_Template", "categoryFunctionExists", "queryFunctionExists", "showHelp", "appSettingsUrl", "ɵɵsanitizeUrl", "calendarViewConfig", "baseSchema", "ReportSettingsComponent_Conditional_49_Template_p_checkbox_ngModelChange_3_listener", "_r25", "enableCalendarView", "ReportSettingsComponent_Conditional_49_Template_p_checkbox_onChange_3_listener", "onChangeCalendarView", "ReportSettingsComponent_Conditional_49_Conditional_4_Template", "nameLabelMap", "hasDateColumn", "ɵɵconditional", "ReportSettingsComponent_p_accordionTab_50_Template_app_linked_tiles_tilesListUpdated_1_listener", "_r26", "tilesListUpdated", "tilesConfig", "id", "advancedJsonEditFlag", "ReportSettingsComponent_p_accordionTab_51_Template_app_linked_charts_linkedChartsUpdated_1_listener", "_r27", "chartListUpdated", "chartConfigs", "ReportSettingsComponent_p_accordionTab_52_div_6_Template_p_dropdown_ngModelChange_1_listener", "_r29", "chartSubType", "chartSubTypes", "ReportSettingsComponent_p_accordionTab_52_Template_p_dropdown_ngModelChange_3_listener", "_r28", "chartType", "ReportSettingsComponent_p_accordionTab_52_Template_p_dropdown_onChange_3_listener", "onChartChange", "ReportSettingsComponent_p_accordionTab_52_div_6_Template", "ReportSettingsComponent_p_accordionTab_52_Template_textarea_ngModelChange_8_listener", "chartConfigDetails", "chartTypes", "_c6", "length", "ReportSettingsComponent_p_accordionTab_53_ng_template_6_span_8_Template_p_inputNumber_ngModelChange_1_listener", "_r32", "rowData_r31", "decimalPlaces", "ɵɵpropertyInterpolate1", "i_r34", "ReportSettingsComponent_p_accordionTab_53_ng_template_6_span_9_Template_p_dropdown_ngModelChange_1_listener", "_r35", "currency", "currencies", "ReportSettingsComponent_p_accordionTab_53_ng_template_6_Template_p_dropdown_ngModelChange_5_listener", "_r30", "format", "ReportSettingsComponent_p_accordionTab_53_ng_template_6_Template_p_dropdown_onChange_5_listener", "setDecimalPlaces", "ReportSettingsComponent_p_accordionTab_53_ng_template_6_span_8_Template", "ReportSettingsComponent_p_accordionTab_53_ng_template_6_span_9_Template", "ɵɵpropertyInterpolate2", "displayName", "formats", "baseType", "ReportSettingsComponent_p_accordionTab_53_ng_template_5_Template", "ReportSettingsComponent_p_accordionTab_53_ng_template_6_Template", "formatColumns", "ReportSettingsComponent_p_accordionTab_54_ng_template_5_Template_p_button_click_3_listener", "_r36", "clearSelectedDrillThroughs", "ReportSettingsComponent_p_accordionTab_54_ng_template_5_Template_p_button_click_4_listener", "addNew", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "ReportSettingsComponent_p_accordionTab_54_ng_template_7_ng_template_16_Template_input_ngModelChange_3_listener", "rowdata_r40", "_r39", "urlName", "ReportSettingsComponent_p_accordionTab_54_ng_template_7_ng_template_16_Template_input_focusout_3_listener", "drillThrough_r38", "onSubstitutionChange", "ReportSettingsComponent_p_accordionTab_54_ng_template_7_ng_template_16_Template_p_button_onClick_7_listener", "j_r41", "rowIndex", "ctx_r41", "i_r43", "selectUrlSubstitution", "urlSubstitution", "ReportSettingsComponent_p_accordionTab_54_ng_template_7_ng_template_16_Template_p_button_onClick_9_listener", "deleteSubstitution", "ReportSettingsComponent_p_accordionTab_54_ng_template_7_ng_template_16_Template_p_button_onClick_11_listener", "addSubstitution", "ɵɵstyleMap", "ɵɵpureFunction0", "_c8", "_c9", "ReportSettingsComponent_p_accordionTab_54_ng_template_7_Template_p_checkbox_onChange_3_listener", "_r37", "drillThroughCheckedChange", "checked", "ReportSettingsComponent_p_accordionTab_54_ng_template_7_Template_p_dropdown_ngModelChange_6_listener", "displayColumn", "ReportSettingsComponent_p_accordionTab_54_ng_template_7_Template_input_ngModelChange_11_listener", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ReportSettingsComponent_p_accordionTab_54_ng_template_7_Template_input_focusout_11_listener", "ReportSettingsComponent_p_accordionTab_54_ng_template_7_ng_template_16_Template", "ReportSettingsComponent_p_accordionTab_54_ng_template_7_Template_p_dropdown_ngModelChange_19_listener", "openIn", "ReportSettingsComponent_p_accordionTab_54_ng_template_7_Template_p_button_click_25_listener", "deleteProduct", "_c7", "urlColumns", "openInValues", "gridGeneratedLink", "ReportSettingsComponent_p_accordionTab_54_ng_template_5_Template", "ReportSettingsComponent_p_accordionTab_54_ng_template_6_Template", "ReportSettingsComponent_p_accordionTab_54_ng_template_7_Template", "drillThroughs", "ReportSettingsComponent_Conditional_55_Template_textarea_ngModelChange_4_listener", "_r44", "agentAssetConfig", "instructions", "ReportSettingsComponent_Conditional_55_Template_textarea_ngModelChange_8_listener", "schema", "ReportSettingsComponent_Conditional_55_Template_input_ngModelChange_12_listener", "workflowId", "ReportSettingsComponent_Conditional_55_Template_input_ngModelChange_16_listener", "startMessage", "ReportSettingsComponent_p_button_72_Template_p_button_onClick_0_listener", "_r45", "openAssetModal", "ReportSettingsComponent_p_accordionTab_91_Template_input_ngModelChange_4_listener", "_r46", "selectedEmailLimit", "ReportSettingsComponent_p_accordionTab_91_Template_input_keypress_4_listener", "numberOnly", "ReportSettingsComponent_p_accordionTab_91_Template_input_blur_4_listener", "checkTimeOutValid", "_c2", "ReportSettingsComponent_ng_template_96_Template_p_button_onClick_0_listener", "_r47", "deleteReport", "ReportSettingsComponent_ng_template_96_Template_p_button_onClick_1_listener", "displayModal", "disable<PERSON><PERSON><PERSON>", "selectedColumn", "ReportSettingsComponent_div_110_ng_template_2_div_0_Template", "columnName_r49", "ReportSettingsComponent_div_110_Template_p_dropdown_ngModelChange_1_listener", "_r48", "ReportSettingsComponent_div_110_ng_template_2_Template", "ReportSettingsComponent_div_110_ng_template_3_Template", "selectedProjectVariable", "ReportSettingsComponent_div_111_ng_template_2_div_0_Template", "projectVariable_r51", "title", "ReportSettingsComponent_div_111_Template_p_dropdown_ngModelChange_1_listener", "_r50", "ReportSettingsComponent_div_111_ng_template_2_Template", "ReportSettingsComponent_div_111_ng_template_3_Template", "urlProjectVariables", "ReportSettingsComponent_ng_template_112_Template_p_button_click_0_listener", "_r52", "applyURLSubstitution", "ReportSettingsComponent_ng_template_112_Template_p_button_click_1_listener", "displayURLSubstitution", "ReportSettingsComponent", "some", "col", "Date", "DateTime", "constructor", "reportadminservice", "datastoresService", "messageSvc", "confirmationService", "renderer", "ngZone", "projectsService", "userService", "userFavoriteService", "featureFlagService", "changeDetectorRef", "cryptoService", "communicationService", "reportConfigOut", "paramListUpdated", "reportType", "Mode", "workflowIdentity", "showReportTester", "showHistoryDialog", "isReportLoaded", "showEmailSettings", "isDleSpecsLoaded", "messagePermissionRole", "dirFilterRegExp", "canDelete", "users", "selected<PERSON>ser", "allowAnonymous", "user", "isSaveDisabled", "actionableGridColumnType", "cachedFormatSettings", "cachedUserConfiguredFormats", "reportSettings", "connectionType", "reportId", "reportName", "description", "url", "htmlCode", "isActive", "tenantId", "reportGroupId", "companyName", "connectorconfig", "connector", "remoteFunction", "workFLowsettings", "workFlowName", "anonymousUser", "email", "userName", "userTypeId", "userId", "gridEvents", "connectionTimeOut", "anonymousAccessId", "emailLimit", "goldenArrowSetup", "categoryFunction", "configId", "config", "dataSourceInfo", "dataSource", "formName", "connectorInfo", "queryColumnsFunction", "defaultDrillThrough", "code", "active", "success", "failed", "LinkToEmbed", "HtmlToPaste", "fieldTooltipMap", "api<PERSON><PERSON>bled", "columns", "columnsToShow", "subTypes", "standaloneChartfeatuerFlag", "minimunCache", "reportColumnCount", "urlSubType", "dataViewDeleteMessage", "showDynamicAssetSettings", "interAppSubscription", "allReportFavorites", "defaultAsset", "<PERSON><PERSON><PERSON>", "existingAssetTagGroups", "groupTagFlag", "ActionableGrids", "gridAgentChatFlag", "value", "environment", "maxColumnCount", "ngOnInit", "initSettings", "_this", "_asyncToGenerator", "subscribeToInterAppCommunication", "tempReportConfig", "JSON", "stringify", "getFeatureAssignment", "setFeatureFlags", "getConnectors", "getWorkFlows", "getDatastores", "setGroupTagsSubscriptions", "getGroupTagsList", "getVisualizations", "ActionableGrid", "find", "p", "Datastore", "selectedConnectionTimeOut", "linkToEmbed", "htmlToPaste", "config<PERSON><PERSON>", "Info", "getUsers", "getUserRole", "then", "userRole", "userRoles", "manager", "hasManagerPermissions", "catch", "error", "console", "log", "setTempQueryDetails", "for<PERSON>ach", "drillThrough", "geProjectVariables", "buildBreadcrumb", "trackUserFavorites", "trackReportParameterChange", "ngOnDestroy", "groupTagSub", "unsubscribe", "groupTagsSub", "userFavoriteSub", "onReportParameterChange", "subscribe", "next", "response", "push", "getWorkflows", "workflow", "gridSaveEvent", "gridEvent", "eventType", "GridSave", "targetId", "err", "groupTags", "event", "data", "map", "tag", "dropdownSelectName", "getAssetGroupTag", "groupTag", "onClickTestWorkflow", "onClickHistory", "onClickSave", "_this2", "showError", "validateActionableGridColumnConfig", "showWarning", "isViewChanged", "dataStoreViewName", "isConnectionTypeChanged", "targetType", "Workflow", "targetRelativeId", "validateDynamicAsset", "calendarViewFlag", "validateCalendar<PERSON>iew", "validation", "chartvalidation", "dateColumnCount", "element", "d", "toString", "currencySymbol", "symbol_native", "tempColumn", "assignTempColumn", "Object", "assign", "x", "Connector", "queryparameters", "params", "action", "Chart", "addReportSettings", "onParameterOptionsChanged", "dataView", "datastore", "emit", "showSuccess", "refreshReport", "randomNumber", "fromAppName", "currentAppName", "toAppName", "navigationAppName", "EventType", "REPORTS_UPDATED", "emitParentGroupCommunicationEvent", "datastoreId", "saveAgentAssetConfig", "actionableGridColumnsConfig", "emptyValidValues", "getEmptyValidValues", "actionableGridColumnConfig", "filter", "columnConfig", "checkDropdown", "checkLink", "Checkbox", "String", "Symbol", "dropdown", "values", "Link", "link", "_this3", "viewHashId", "hashSHA256", "sort", "startDateCol", "endDateCol", "titleCol", "ConnectorApp", "firstName", "trim", "lastName", "y", "localeCompare", "copyToClipboard", "item", "listener", "e", "clipboardData", "setData", "preventDefault", "document", "addEventListener", "execCommand", "removeEventListener", "onClickDelete", "window", "location", "getFunctions", "index", "findIndex", "queryFunction", "getQueryCategories", "setFormatColumnsDataSource", "charCode", "which", "keyCode", "connectionTimeOutSelected", "confirmLessThan60Seconds", "confirm", "message", "header", "icon", "acceptButtonStyleClass", "rejectButtonStyleClass", "accept", "reject", "selectRootElement", "focus", "hideModal", "featureflag", "s", "STAND_ALONE_CHART", "_this4", "featureFlags", "getFeatureFlags", "visualizationFlag", "Visualizations", "gridChartsFlag", "AssetGroupTags", "CalendarView", "AdvancedJsonEdit", "GridAgentChat", "pipe", "prevDatastore", "getDataViewList", "min", "max", "Math", "random", "setParamAction", "getReportSettingsById", "splice", "generatedLink", "split", "Number", "actualValue", "tempValue", "replace", "customerId", "itemlabel", "itemval", "categoryName", "getQueryNames", "qName", "resetCalendarView", "ucf", "dataViewId", "userConfiguredFormat", "properties", "mapBaseSchemaToFormatSettings", "updateColumnsWithBaseSchema", "column", "updateColumnsList", "formatSettings", "defaultFormatSettings", "stringFormats", "i", "sfs", "concat", "defaultColConfig", "baseFormat", "typeDescription", "schemaProperty", "includes", "typeProperties", "required", "<PERSON><PERSON><PERSON>", "allowUserUpdate", "Array", "children", "setDataSouce", "savedColumnConfigList", "getSavedColumnConfigList", "getColumnListFromBaseSchema", "getColumnListFromApi", "keys", "colName", "colConfig", "Boolean", "Integer", "Decimal", "presentationProperties", "v", "showColumn", "getReportColumns", "columnNameList", "toLowerCase", "rowData", "assetId", "assetType", "assetName", "environmentName", "projectName", "getProjectVariables", "projectVariables", "variable", "indexOf", "copiedSelectedDatastore", "updateDatastore", "exception", "saltboxAnalyticsFilterAppName", "OPEN_ANALTYICS_FILTER", "mode", "defaultView", "selectSavedViewOnReportSettings", "_this5", "view", "f", "prevDataView", "param", "filterConditions", "columnUID", "detectChanges", "urlSubstitutionIndex", "isProjectVariable", "selectedUrlSubstitution", "selectedRowIndex", "selectedUrlSubIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selected<PERSON>rl<PERSON><PERSON>", "getObservable", "run", "SAVED_DATAVIEW_FILTER", "updateDataViewList", "ERROR_ANALTYICS_FILTER", "currentDataView", "cfw", "conditionOperator", "updatedColumns", "cts", "newColumns", "newDynamicAssetDialogChanged", "visible", "a", "b", "toLocaleLowerCase", "charCodeAt", "datastoreForms", "asset", "DynamicForm", "datastoreConnectorApps", "dynamicFormId", "populateAssetIdFieldOptions", "getProjectVersion", "_this6", "projectVersion", "items", "project", "routerLink", "updateBreadcrumb", "setIsFavorite", "isFavorite", "isApp", "onUpdateUserFavorite", "userFavorite", "generateUserFavorite", "pathname", "tileConfig", "tileId", "tileType", "tile", "reportParameterChanged", "getProfileProperties", "getCompleteUserProfilePropertyList", "communicationEvent", "UPDATE_PARENT_GROUP_TAG", "groupId", "DEACTIVATE_ASSET_GROUP_TAG", "_this7", "generateViewHashID", "_this8", "ɵɵdirectiveInject", "i1", "ReportAdminService", "i2", "DatastoresService", "i3", "NotificationService", "i4", "ConfirmationService", "Renderer2", "NgZone", "i5", "ProjectsService", "i6", "UserService", "i7", "UserFavoriteService", "i8", "FeatureFlagService", "ChangeDetectorRef", "i9", "CryptoService", "selectors", "inputs", "columnDefs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ReportSettingsComponent_Template", "rf", "ctx", "ReportSettingsComponent_Template_p_button_onClick_8_listener", "_r1", "ReportSettingsComponent_Conditional_9_Template", "ReportSettingsComponent_Template_p_button_click_11_listener", "ReportSettingsComponent_p_message_15_Template", "ReportSettingsComponent_Template_input_ngModelChange_18_listener", "ReportSettingsComponent_Template_input_ngModelChange_24_listener", "ReportSettingsComponent_div_29_Template", "ReportSettingsComponent_div_30_Template", "ReportSettingsComponent_Template_p_checkbox_ngModelChange_32_listener", "ReportSettingsComponent_div_41_Template", "ReportSettingsComponent_ng_container_43_Template", "ReportSettingsComponent_ng_container_44_Template", "ReportSettingsComponent_ng_container_45_Template", "ReportSettingsComponent_p_accordionTab_46_Template", "ReportSettingsComponent_p_accordionTab_47_Template", "ReportSettingsComponent_p_accordionTab_48_Template", "ReportSettingsComponent_Conditional_49_Template", "ReportSettingsComponent_p_accordionTab_50_Template", "ReportSettingsComponent_p_accordionTab_51_Template", "ReportSettingsComponent_p_accordionTab_52_Template", "ReportSettingsComponent_p_accordionTab_53_Template", "ReportSettingsComponent_p_accordionTab_54_Template", "ReportSettingsComponent_Conditional_55_Template", "ReportSettingsComponent_Template_input_ngModelChange_59_listener", "ReportSettingsComponent_Template_i_click_63_listener", "ReportSettingsComponent_Template_input_ngModelChange_65_listener", "ReportSettingsComponent_Template_i_click_69_listener", "ReportSettingsComponent_p_button_72_Template", "ReportSettingsComponent_Template_p_checkbox_ngModelChange_74_listener", "ReportSettingsComponent_Template_p_dropdown_ngModelChange_76_listener", "ReportSettingsComponent_Template_input_ngModelChange_84_listener", "ReportSettingsComponent_Template_input_keypress_84_listener", "ReportSettingsComponent_Template_input_blur_84_listener", "ReportSettingsComponent_Template_p_dialog_visibleChange_87_listener", "ReportSettingsComponent_Template_p_dialog_onHide_87_listener", "ReportSettingsComponent_Template_p_button_click_90_listener", "ReportSettingsComponent_p_accordionTab_91_Template", "ReportSettingsComponent_Template_p_dialog_visibleChange_92_listener", "ReportSettingsComponent_ng_template_96_Template", "ReportSettingsComponent_Template_p_dialog_visibleChange_97_listener", "ReportSettingsComponent_Template_p_radioButton_ngModelChange_102_listener", "ReportSettingsComponent_Template_p_radioButton_ngModelChange_107_listener", "ReportSettingsComponent_div_110_Template", "ReportSettingsComponent_div_111_Template", "ReportSettingsComponent_ng_template_112_Template", "ReportSettingsComponent_Template_app_new_dynamic_asset_dialogVisibleChanged_113_listener", "_c0", "_c3", "_c4", "i10", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MaxLengthValidator", "NgModel", "NgForm", "i11", "<PERSON><PERSON><PERSON>", "Footer", "PrimeTemplate", "i12", "<PERSON><PERSON>", "i13", "<PERSON><PERSON><PERSON>", "i14", "UIMessage", "i15", "InputText", "i16", "InputTextarea", "i17", "<PERSON><PERSON><PERSON>er", "i18", "Dropdown", "i19", "RadioButton", "i20", "i21", "Accordion", "AccordionTab", "i22", "Table", "SortableColumn", "SelectableRow", "i23", "InputNumber", "i24", "ConfirmDialog", "i25", "Dialog", "styles"], "sources": ["C:\\Projects\\SaltboxReporting\\Reporting-Web-App\\src\\app\\report-setup\\report-settings\\report-settings.component.ts", "C:\\Projects\\SaltboxReporting\\Reporting-Web-App\\src\\app\\report-setup\\report-settings\\report-settings.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, EventEmitter, Inject, Input, NgZone, OnDestroy, OnInit, Output, Renderer2 } from '@angular/core';\r\nimport { Project } from 'src/app/core/models/project';\r\nimport { AnonymousUser, ReportSettings } from 'src/app/core/models/report-settings';\r\nimport { SampleDropDown } from 'src/app/core/models/sampleDropDown';\r\nimport { NotificationService } from 'src/app/shared/services/notification.service';\r\nimport { ReportAdminService } from 'src/app/core/services/report-admin.service';\r\nimport { constants } from 'src/app/core/constants/constants';\r\nimport { ConfirmationService, SelectItem, PrimeTemplate, Footer } from 'primeng/api';\r\nimport { DrillThrough } from 'src/app/core/models/drill-through';\r\nimport { CommunicationToken } from 'src/app/core/models/communication-service-type';\r\nimport { _isNumberValue } from '@angular/cdk/coercion';\r\nimport { UrlSub } from 'src/app/core/models/url-substitution';\r\nimport { Subscription } from 'rxjs';\r\nimport { take } from 'rxjs/operators';\r\nimport { ConnectorTypes, DynamicAssetTypes, ReportType } from 'src/app/core/enums/reporting.enums';\r\nimport { ActionableGridColumnType } from 'src/app/core/enums/actionable-grid-enums';\r\nimport { ActionableGridColumnConfig } from 'src/app/core/models/actionable-grid-column-config';\r\nimport { deepCopy } from 'src/app/core/functions/utility';\r\nimport { ColumnConfig } from 'src/app/core/models/column-config';\r\nimport { WorkflowInfo } from 'src/app/core/models/WorkflowInfo';\r\nimport { GridEventType, GridTargetType } from 'src/app/core/enums/grid-workflow';\r\nimport { ProjectsService } from 'src/app/core/services/projects.service';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ProjectVersion } from 'src/app/core/models/project-version';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { UserFavorite } from 'src/app/core/models/user-favorite';\r\nimport { UserFavoriteService } from 'src/app/core/services/user-favorite.service';\r\nimport { CommunicationService } from 'src/app/core/services/communication.service';\r\nimport { FeatureFlagService } from 'src/app/core/services/feature-flag.service';\r\nimport { FeatureFlag } from 'src/app/core/enums/feature-flag.enums';\r\nimport { ChartConfig } from 'src/app/core/models/chart-config';\r\nimport { BaseTile } from 'src/app/visualizations/models/base-tile';\r\nimport { TileTypes } from 'src/app/visualizations/enums/tiles.enums';\r\nimport { Datastore } from 'src/app/shared/models/datastore';\r\nimport { DataView } from 'src/app/shared/models/data-view';\r\nimport { JsonDataTypes, JsonStringFormats } from 'src/app/shared/enums/json-schema.enum';\r\nimport { DatastoresService } from 'src/app/shared/services/datastores.service';\r\nimport { ReportParametersModel } from 'src/app/core/models/report-parameter';\r\nimport { DynamicAssetInfo } from 'src/app/core/models/dynamic-asset-info';\r\nimport { AssetDropdownGroup } from 'src/app/core/models/asset-dropdown-group';\r\nimport { GroupTag } from 'src/app/core/models/group-tag';\r\nimport { NewDynamicAssetComponent } from '../../dynamic-form/new-dynamic-form/new-dynamic-asset/new-dynamic-asset.component';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { InputNumberModule } from 'primeng/inputnumber';\r\nimport { TableModule } from 'primeng/table';\r\nimport { LinkedChartsComponent } from '../linked-charts/linked-charts.component';\r\nimport { LinkedTilesComponent } from '../linked-tiles/linked-tiles.component';\r\nimport { ActionableGridConfigComponent } from '../actionable-grid-config/actionable-grid-config.component';\r\nimport { ConditionalFormsComponent } from '../conditional-forms/conditional-forms.component';\r\nimport { AccordionModule } from 'primeng/accordion';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { KeyFilterModule } from 'primeng/keyfilter';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { InputTextareaModule } from 'primeng/inputtextarea';\r\nimport { MessageModule } from 'primeng/message';\r\nimport { NgIf, NgFor, NgClass } from '@angular/common';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { ToolbarModule } from 'primeng/toolbar';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { CalendarViewSettingsComponent } from '../calendar-view-settings/calendar-view-settings.component';\r\nimport { CryptoService } from 'src/app/core/services/crypto.service';\r\nimport { CalendarViewConfig } from 'src/app/core/models/calendar-view-config';\r\nimport { HelpLauncherComponent } from '../../shared/help-launcher/help-launcher.component';\r\nimport { HelpLinks } from 'src/app/core/enums/help-links.enum';\r\nimport { AgentAssetConfig } from 'src/app/core/models/agent-asset-config';\r\nimport { ConditionalFormattingComponent } from '../conditional-formatting/conditional-formatting.component';\r\n\r\n@Component({\r\n  selector: 'app-report-settings',\r\n  templateUrl: './report-settings.component.html',\r\n  styleUrls: ['./report-settings.component.scss'],\r\n  standalone: true,\r\n  imports: [FormsModule, ToolbarModule, ButtonModule, TooltipModule, NgIf, MessageModule, InputTextModule, InputTextareaModule,\r\n    KeyFilterModule, DropdownModule, PrimeTemplate, NgFor, RadioButtonModule, CheckboxModule, AccordionModule,\r\n    NgClass, ConditionalFormsComponent, ConditionalFormattingComponent, ActionableGridConfigComponent, LinkedTilesComponent, LinkedChartsComponent,\r\n    TableModule, InputNumberModule, ConfirmDialogModule, DialogModule, Footer, NewDynamicAssetComponent,\r\n    CalendarViewSettingsComponent, HelpLauncherComponent]\r\n})\r\n\r\nexport class ReportSettingsComponent implements OnInit, OnDestroy {\r\n\r\n  @Input() reportConfig: ReportSettings;\r\n  @Input() projectId: string;\r\n  @Input() projectVersionId: string;\r\n  @Input() columnDefs: any[];\r\n  @Input() customerId: string;\r\n  @Input() showHelp: boolean;\r\n  @Input() projectIsLocked = false;\r\n  @Input() agentAssetConfig: AgentAssetConfig;\r\n\r\n  @Output() reportConfigOut = new EventEmitter<ReportSettings>();\r\n  @Output() paramListUpdated = new EventEmitter<ReportParametersModel[]>();\r\n\r\n  // Used for html conditions\r\n  reportType = ReportType;\r\n  formMode = constants.Mode;\r\n\r\n  reportId: string;\r\n  selectedCategory: string;\r\n  selectedConnectorType: any = null;\r\n  tempReportConfig: string;\r\n  project: Project[];\r\n  connectors: SelectItem[] = [];\r\n  selectedConnector: string;\r\n  workflows: WorkflowInfo[] = [{ name: 'No Workflow Selected', id: null, workflowIdentity: null }];\r\n  selectedSavedEventWorkflow: WorkflowInfo;\r\n  selectedWorkflow: string;\r\n  queryCategory: SelectItem[] = [];\r\n  selectedQueryCategory: string;\r\n  queryName: SelectItem[] = [];\r\n  selectedQueryName: string;\r\n  functions: SelectItem[] = [];\r\n  selectedFunction: string;\r\n  selectedDatastore: Datastore;\r\n  prevDatastore: Datastore;\r\n  selectedDataView: DataView;\r\n  prevDataView: DataView;\r\n  showReportTester = false;\r\n  showHistoryDialog = false;\r\n  isReportLoaded = false;\r\n  showEmailSettings = false;\r\n  isDleSpecsLoaded = false;\r\n  messagePermissionRole = ['Integration Operator'];\r\n  dirFilterRegExp = /^[^<>*:'\"?\\\\[\\]|/]+$/;\r\n  linkToEmbed: string;\r\n  htmlToPaste: string;\r\n  displayModal: boolean;\r\n  canDelete = false;\r\n  hasManagerPermissions: boolean;\r\n  users: SelectItem[] = [];\r\n  selectedUser = '0';\r\n  allowAnonymous = false;\r\n  user: AnonymousUser[] = [];\r\n  isSaveDisabled = false;\r\n  chartType: string;\r\n  chartSubType: string;\r\n  chartConfigDetails: string;\r\n  tilesConfig: BaseTile[] = [];\r\n  chartConfigs: ChartConfig[] = []; // for visualizations\r\n  actionableGridColumnType = ActionableGridColumnType;\r\n  dataViews: DataView[] = [];\r\n  // defaultFormatSetting is the exact list that we have on baseSchema\r\n  // the purpose is not to convert baseSchema to ActionableGridColumnConfig[] over and over\r\n  cachedFormatSettings: { [datastoreId: string]: ActionableGridColumnConfig[] } = {};\r\n  // to cache the settings\r\n  cachedUserConfiguredFormats: { datastoreId: string, dataViewId: string, userConfiguredFormat: ActionableGridColumnConfig[] }[] = [];\r\n  selectedUserConfiguredFormat: ActionableGridColumnConfig[] = [];\r\n\r\n  reportSettings: ReportSettings =\r\n    {\r\n      queryCategory: '',\r\n      queryName: '',\r\n      connectionType: '',\r\n      projectId: '',\r\n      reportId: '',\r\n      reportType: '',\r\n      reportName: '',\r\n      description: '',\r\n      url: '',\r\n      htmlCode: '',\r\n      isActive: false,\r\n      tenantId: '',\r\n      reportGroupId: '',\r\n      companyName: '',\r\n      connectorconfig: {\r\n        connector: '',\r\n        remoteFunction: ''\r\n      },\r\n      workFLowsettings: {\r\n        workFlowName: ''\r\n      },\r\n      anonymousUser: {\r\n        email: '',\r\n        id: 0,\r\n        tenantId: 0,\r\n        userName: '',\r\n        userTypeId: 0,\r\n        userId: 0\r\n      },\r\n      gridEvents: [],\r\n      allowAnonymous: false,\r\n      selectedUser: 0,\r\n      connectionTimeOut: 0,\r\n      anonymousAccessId: '',\r\n      chartType: '',\r\n      chartSubType: '',\r\n      chartConfigDetails: '',\r\n      emailLimit: 0,\r\n      goldenArrowSetup: [],\r\n      categoryFunction: '',\r\n      formatConfig: { id: 0, configId: '', reportId: '', config: {} },\r\n      dataSourceInfo: { id: 0, dataSource: '', formName: '', connectorInfo: {}, queryCategory: '', queryName: '' },\r\n      queryColumnsFunction: '',\r\n      allowAddNewRow: false,\r\n      enablePagination: true,\r\n      paginationAutoPaging: false,\r\n      defaultPageSize: 20,\r\n      enableCalendarView: false,\r\n    };\r\n\r\n  defaultDrillThrough: DrillThrough = {\r\n    displayColumn: '',\r\n    openIn: '0',\r\n    urlAlias: '',\r\n    urlSubstitution: [new UrlSub()],\r\n    gridGeneratedLink: ''\r\n  };\r\n  drillThroughs: DrillThrough[] = [this.defaultDrillThrough];\r\n  selectedDrillThroughs: DrillThrough[] = [];\r\n  openInValues: SampleDropDown[] = [\r\n    { code: 'newWindow', name: 'New Window' },\r\n    { code: 'currentWindow', name: 'Current Window' }\r\n  ];\r\n\r\n  nameLabelMap: { [key: string]: string; } = {\r\n    name: 'Report Name',\r\n    description: 'Description',\r\n    active: 'Active',\r\n    success: 'Success',\r\n    failed: 'Failed',\r\n    connectors: 'Connectors',\r\n    queryCategory: 'B1 Query Category',\r\n    queryName: 'B1 Query Name',\r\n    LinkToEmbed: 'Link to embed this report',\r\n    HtmlToPaste: 'HTML to paste in a website',\r\n    enableCalendarView: 'Enable calendar view'\r\n  };\r\n\r\n  fieldTooltipMap: { [key: string]: string; } = {\r\n    name: '',\r\n    description: '',\r\n    active: 'Active workflows are executed when the Project Engine is running.',\r\n    apiEnabled: 'Allows Saltbox API Gateway to interact with this workflow.',\r\n    success: 'Message will be retained for X days upon success.',\r\n    failed: 'Message will be retained for X days upon failure'\r\n  };\r\n  columns: ColumnConfig[] = [];\r\n  formatColumns: ColumnConfig[] = [];\r\n  columnsToShow: any[] = [];\r\n  currencies: any = constants.currencies;\r\n  formats = constants.formats;\r\n  categories: any[] = [{ name: 'Grid', key: 'grid' }, { name: 'Pivot', key: 'pivot' }];\r\n  connectorTypes: any[] = [{ name: 'Connector', key: 'connector' }, { name: 'Saltbox Datastore', key: 'forms' }];\r\n  chartTypes: any[] = [\r\n    {\r\n      name: 'Line', key: 'line',\r\n      config: 'A line chart requires a dataset which includes a minimum of two columns for the x and y axis. The first column will contain the x axis data, all subsequent columns will contain y axis data. Labels will be defaulted based on the column names in the dataset.'\r\n    },\r\n    {\r\n      name: 'Bar', key: 'bar',\r\n      subTypes: [{ name: 'Stacked', key: 'stacked' }, { name: 'Grouped', key: 'grouped' }, { name: 'Normalized', key: 'normalized' }],\r\n      config: 'A bar chart requires a dataset which includes a minimum of two columns for the x and y axis. The first column will contain the x axis data, all subsequent columns will contain y axis data. Labels will be defaulted based on the column names in the dataset.'\r\n    },\r\n    {\r\n      name: 'Column', key: 'column',\r\n      subTypes: [{ name: 'Stacked', key: 'stacked' }, { name: 'Grouped', key: 'grouped' }, { name: 'Normalized', key: 'normalized' }],\r\n      config: 'A column chart requires a dataset which includes a minimum of two columns for the x and y axis. The first column will contain the x axis data, all subsequent columns will contain y axis data. Labels will be defaulted based on the column names in the dataset.'\r\n    },\r\n    {\r\n      name: 'Histogram', key: 'histogram',\r\n      config: 'A histogram requires a dataset which includes a single data column. The x axis will plot the data in the dataset and the y access will plot the number of occurrences. Labels will be defaulted based on the column name in the dataset.'\r\n    },\r\n    {\r\n      name: 'Area', key: 'area',\r\n      subTypes: [{ name: 'Series', key: 'series' }, { name: 'Grouped', key: 'grouped' }, { name: 'Normalized', key: 'normalized' }],\r\n      config: 'An Area chart requires a dataset which includes a minimum of two columns for the x and y axis. The first column will contain the x axis data, all subsequent columns will contain y axis data. Labels will be defaulted based on the column names in the dataset.'\r\n    },\r\n    {\r\n      name: 'Pie', key: 'pie',\r\n      subTypes: [{ name: 'Regular', key: 'regular' }, { name: 'Rose', key: 'rose' }],\r\n      config: 'A \"regular\" Pie chart requires a dataset that includes two columns.  The first column will be used for the slice labels and the second column will contain the values.\\n A \"rose\" pie chart requires a dataset that includes three columns. The first column will be used for slice labels, the second column for the values, and the third column for the size data.'\r\n    },\r\n    {\r\n      name: 'Treemap', key: 'treemap',\r\n      config: 'A Treemap chart requires a dataset which includes a minimum of two columns. The first column will contain the labels and the second column will contain the numeric value. '\r\n    }\r\n  ];\r\n  chartSubTypes: any[];\r\n  standaloneChartfeatuerFlag = false;\r\n  datastores: Datastore[] = [];\r\n  selectedConnectionTimeOut: number;\r\n  selectedEmailLimit: number;\r\n  minimunCache = false;\r\n  categoryFunctionExists = false;\r\n  queryFunctionExists = false;\r\n  reportColumnCount = 0;\r\n  displayURLSubstitution = false;\r\n  urlSubType = 'column';\r\n  urlColumns: SelectItem[] = [];\r\n  urlProjectVariables: SelectItem[] = [];\r\n  selectedRowIndex: number;\r\n  selectedUrlSubIndex: number;\r\n  selectedUrlSubstitution: string;\r\n  selectedColumn: SelectItem;\r\n  selectedProjectVariable: SelectItem;\r\n  projectVariables: any[];\r\n  dataViewDeleteMessage = '';\r\n  showDynamicAssetSettings = false;\r\n  projectVersion: ProjectVersion;\r\n  private interAppSubscription: Subscription = new Subscription();\r\n  visualizationFlag: boolean;\r\n  gridChartsFlag: boolean;\r\n  isFavorite: boolean;\r\n  private userFavoriteSub: Subscription;\r\n  private allReportFavorites: UserFavorite[] = [];\r\n  private onReportParameterChange: Subscription;\r\n  \r\n  assetsInfoGroups: AssetDropdownGroup[] = [];\r\n  defaultAsset = { name: \"Auto-Generated from Grid Data\", id: null, type: DynamicAssetTypes.Default, description: '' };\r\n  selectedDynamicAsset: DynamicAssetInfo = this.defaultAsset;\r\n  idFieldOptions: string[] = [];\r\n  appSettingsUrl: string;\r\n\r\n  groupTagOptions: SelectItem[];\r\n  parentGroup = null;\r\n  existingAssetTagGroups: GroupTag[] = [];\r\n  groupTagFlag = false;\r\n  private groupTagsSub: Subscription;\r\n  private groupTagSub: Subscription;\r\n  calendarViewFlag: boolean;\r\n  helpLink = HelpLinks.ActionableGrids;\r\n  advancedJsonEditFlag: boolean;\r\n  \r\n  gridAgentChatFlag = false;\r\n\r\n  pageSizes: any[] = [{ key: '20', value: 20 }, { key: '50', value: 50 }, { key: '100', value: 100 }];\r\n\r\n  get hasDateColumn() {\r\n    return this.selectedUserConfiguredFormat?.some(col => (col.format.type === ActionableGridColumnType.Date || col.format.type === ActionableGridColumnType.DateTime)) ?? false;\r\n  }\r\n\r\n  constructor(\r\n    private reportadminservice: ReportAdminService,\r\n    private datastoresService: DatastoresService,\r\n    private messageSvc: NotificationService,\r\n    private confirmationService: ConfirmationService,\r\n    private renderer: Renderer2,\r\n    private ngZone: NgZone,\r\n    private projectsService: ProjectsService,\r\n    private userService: UserService,\r\n    private userFavoriteService: UserFavoriteService,\r\n    private featureFlagService: FeatureFlagService,\r\n    private changeDetectorRef: ChangeDetectorRef,\r\n    private cryptoService: CryptoService,\r\n    @Inject(CommunicationToken) private communicationService: CommunicationService) {\r\n    this.reportConfig = {\r\n      queryCategory: '',\r\n      queryName: '',\r\n      connectionType: '',\r\n      projectId: '',\r\n      reportId: '',\r\n      reportType: '',\r\n      reportName: '',\r\n      description: '',\r\n      url: '',\r\n      htmlCode: '',\r\n      isActive: false,\r\n      tenantId: '',\r\n      reportGroupId: '',\r\n      companyName: '',\r\n      connectorconfig: {\r\n        connector: '',\r\n        remoteFunction: ''\r\n      },\r\n      workFLowsettings: {\r\n        workFlowName: ''\r\n      },\r\n      anonymousUser: {\r\n        email: '',\r\n        id: 0,\r\n        tenantId: 0,\r\n        userName: '',\r\n        userTypeId: 0,\r\n        userId: 0\r\n      },\r\n      gridEvents: [],\r\n      allowAnonymous: false,\r\n      selectedUser: 0,\r\n      connectionTimeOut: 0,\r\n      anonymousAccessId: '',\r\n      chartType: '',\r\n      chartSubType: '',\r\n      chartConfigDetails: '',\r\n      emailLimit: 0,\r\n      categoryFunction: '',\r\n      queryColumnsFunction: '',\r\n      environment: '',\r\n      maxColumnCount: 0,\r\n      allowAddNewRow: false,\r\n      enablePagination: true,\r\n      paginationAutoPaging: false,\r\n      defaultPageSize: 20,\r\n    };\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.initSettings();\r\n  }\r\n\r\n  async initSettings() {\r\n    this.subscribeToInterAppCommunication();\r\n    this.tempReportConfig = JSON.stringify(this.reportConfig);\r\n    this.getFeatureAssignment();\r\n    await this.setFeatureFlags();\r\n    this.getConnectors(this.reportConfig?.projectId ? this.reportConfig.projectVersionId : 0);\r\n    this.getWorkFlows(this.reportConfig?.projectId ? this.reportConfig.projectVersionId : 0);\r\n    this.getDatastores(this.reportConfig?.projectId, this.reportConfig?.projectVersionId);\r\n    this.setGroupTagsSubscriptions();\r\n    this.communicationService.getGroupTagsList(this.projectVersionId);\r\n    this.getVisualizations();\r\n\r\n    this.selectedCategory = this.reportConfig?.reportType ? this.reportConfig.reportType : this.categories[0];\r\n    if (this.reportConfig.reportType === ReportType.ActionableGrid) {\r\n      this.selectedConnectorType = this.connectorTypes.find(p => p.key === ConnectorTypes.Datastore);\r\n    }\r\n    else {\r\n      this.selectedConnectorType = this.connectorTypes.find(p => p.key === this.reportConfig.connectionType);\r\n    }\r\n\r\n    if (this.reportConfig?.queryCategory || this.reportConfig?.queryCategory === '') {\r\n      this.selectedQueryCategory = this.reportConfig?.queryCategory === '' ? '0' : this.reportConfig?.queryCategory;\r\n    }\r\n    if (this.reportConfig?.queryName) {\r\n      this.selectedQueryName = this.reportConfig?.queryName;\r\n    }\r\n\r\n    if (this.reportConfig?.connectionTimeOut) {\r\n      this.selectedConnectionTimeOut = this.reportConfig?.connectionTimeOut;\r\n    } else {\r\n      this.selectedConnectionTimeOut = 300;\r\n    }\r\n    this.linkToEmbed = this.reportConfig?.url;\r\n    this.htmlToPaste = this.reportConfig?.htmlCode;\r\n\r\n    this.selectedWorkflow = this.reportConfig?.workFLowsettings.workFlowName;\r\n    this.selectedConnector = this.reportConfig?.connectorconfig.configName;\r\n    this.allowAnonymous = this.reportConfig.allowAnonymous;\r\n    this.chartType = this.reportConfig.chartType;\r\n    if (this.chartType) {\r\n      const Info = this.chartTypes.find(p => p.key === this.chartType);\r\n      this.chartSubTypes = Info.subTypes;\r\n      this.chartSubType = this.reportConfig.chartSubType;\r\n      this.chartConfigDetails = Info.config;\r\n    }\r\n    if (this.reportConfig?.emailLimit) {\r\n      this.selectedEmailLimit = this.reportConfig?.emailLimit;\r\n    } else {\r\n      this.selectedEmailLimit = 10;\r\n    }\r\n    this.getUsers(this.reportConfig);\r\n\r\n    this.userService.getUserRole(this.projectVersionId).then(userRole => {\r\n      this.canDelete = (userRole === constants.userRoles.manager);\r\n      this.hasManagerPermissions = (userRole === constants.userRoles.manager);\r\n    }).catch(error => {\r\n      console.log('Error getting user role: ${error}');\r\n      throw error;\r\n    });\r\n\r\n    this.setTempQueryDetails(this.reportConfig);\r\n\r\n    // if connector type is not forms, load column formats(B1WebApi)\r\n    if (this.selectedConnectorType?.key !== ConnectorTypes.Datastore) {\r\n      this.getColumnNames();\r\n    }\r\n    this.drillThroughs = this.reportConfig.goldenArrowSetup;\r\n    this.drillThroughs.forEach(drillThrough => {\r\n      this.onSubstitutionChange(drillThrough);\r\n    });\r\n    this.geProjectVariables();\r\n    this.buildBreadcrumb();\r\n    this.trackUserFavorites();\r\n    this.trackReportParameterChange();\r\n    this.appSettingsUrl = `/project/${this.projectId}/${this.projectVersionId}/appsettings`;\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.groupTagSub?.unsubscribe();\r\n    this.groupTagsSub?.unsubscribe();\r\n    this.interAppSubscription?.unsubscribe();\r\n    this.userFavoriteSub?.unsubscribe();\r\n    this.onReportParameterChange?.unsubscribe();\r\n  }\r\n\r\n  getConnectors(projectVersionId): void {\r\n    this.reportadminservice.getConnectors(projectVersionId).subscribe({\r\n      next: (response) => {\r\n        if (response.length > 0) {\r\n          this.connectors = [];\r\n          response.forEach(value => {\r\n            this.connectors.push({ value: value.name, label: value.name });\r\n          });\r\n          if (this.selectedConnector && this.selectedConnector !== '0') {\r\n            this.onConnectorChange();\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  getWorkFlows(projectId): any {\r\n    this.reportadminservice.getWorkflows(projectId).subscribe({\r\n      next: (response: WorkflowInfo[]) => {\r\n        response?.forEach(workflow => {\r\n          this.workflows.push(workflow);\r\n        })\r\n\r\n        const gridSaveEvent = this.reportConfig.gridEvents?.find(gridEvent => { return gridEvent.eventType === GridEventType.GridSave; });\r\n        if (gridSaveEvent) {\r\n          this.selectedSavedEventWorkflow = this.workflows.find(workflow => {\r\n            return gridSaveEvent.targetId === workflow.workflowIdentity;\r\n          });\r\n        }\r\n      },\r\n      error: err => {\r\n        throw err;\r\n      }\r\n    });\r\n  }\r\n\r\n  setGroupTagsSubscriptions() {\r\n    this.groupTagsSub = this.communicationService.groupTags.subscribe(event => {\r\n      this.existingAssetTagGroups = event.data ?? [];\r\n      this.groupTagOptions = [{ label: 'None', value: 'null' }];\r\n      this.groupTagOptions.push(...this.existingAssetTagGroups.map(tag => { return { label: tag.dropdownSelectName, value: tag } }));\r\n\r\n      this.communicationService.getAssetGroupTag(this.reportConfig.reportId);\r\n    });\r\n\r\n    this.groupTagSub = this.communicationService.groupTag.subscribe(event => {\r\n      this.parentGroup = this.existingAssetTagGroups.find(tag => tag.id === event.data);\r\n\r\n      if (!this.parentGroup) {\r\n        this.parentGroup = this.groupTagOptions[0].value;\r\n      }\r\n    });\r\n  }\r\n\r\n  onClickTestWorkflow(): void {\r\n    this.showReportTester = true;\r\n  }\r\n\r\n  onClickHistory(): void {\r\n    this.showHistoryDialog = true;\r\n  }\r\n\r\n  async onClickSave(): Promise<void> {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n    if (this.reportConfig.reportName.length === 0) {\r\n      this.messageSvc.showError('Error', 'Report Name is required.');\r\n      return;\r\n    }\r\n\r\n    if (this.reportConfig.description.length === 0) {\r\n      this.messageSvc.showError('Error', 'Display Name is required.');\r\n      return;\r\n    }\r\n\r\n    // Setting actionable grid column's format\r\n    if (this.reportConfig.reportType === ReportType.ActionableGrid && this.selectedUserConfiguredFormat) {\r\n      this.reportColumnCount = this.selectedUserConfiguredFormat.length;\r\n\r\n      if (!this.validateActionableGridColumnConfig()) {\r\n        return;\r\n      }\r\n    }\r\n\r\n    if (this.reportColumnCount > this.reportConfig.maxColumnCount) {\r\n      this.messageSvc.showError('Error', 'The report source contains more than ' + this.reportConfig.maxColumnCount +\r\n        ' columns, please reduce the number of columns');\r\n      return;\r\n    } else if (this.reportColumnCount === 0) {\r\n      // implemented to handle stored procedure and select * queries\r\n      this.messageSvc.showWarning('Warning', 'Report creation might fail if report source contains more than '\r\n        + this.reportConfig.maxColumnCount + ' columns.');\r\n    }\r\n    const isViewChanged = this.selectedDataView?.viewId !== this.reportConfig.dataStoreViewName;\r\n    const isConnectionTypeChanged = this.selectedConnectorType?.key !== this.reportConfig.connectionType;\r\n    this.reportConfig.dataStoreViewName =\r\n      this.selectedConnectorType?.key !== ConnectorTypes.Datastore || !this.selectedDataView || this.selectedDataView.viewId === '0'\r\n        ? null\r\n        : this.selectedDataView.viewId;\r\n    this.reportConfig.workFLowsettings.workFlowName = this.selectedWorkflow;\r\n    this.reportConfig.connectorconfig.configName = this.selectedConnector;\r\n    this.reportConfig.connectorconfig.remoteFunction = this.selectedFunction;\r\n\r\n    this.reportConfig.queryCategory = this.selectedQueryCategory;\r\n    this.reportConfig.queryName = this.selectedQueryName;\r\n    this.reportConfig.reportType = this.selectedCategory;\r\n    this.reportConfig.connectionType = this.selectedConnectorType?.key;\r\n\r\n    this.reportConfig.allowAnonymous = this.allowAnonymous;\r\n    this.reportConfig.connectionTimeOut = this.selectedConnectionTimeOut;\r\n    this.reportConfig.formName = this.selectedDatastore?.name;\r\n\r\n    this.reportConfig.gridEvents = this.selectedSavedEventWorkflow ? [{\r\n      eventType: GridEventType.GridSave, targetType: GridTargetType.Workflow,\r\n      targetId: this.selectedSavedEventWorkflow?.workflowIdentity, targetRelativeId: this.selectedSavedEventWorkflow?.name\r\n    }] : [];\r\n\r\n    if (!this.validateDynamicAsset() || (this.calendarViewFlag && this.reportConfig.enableCalendarView && !(await this.validateCalendarView()))) { return; }\r\n\r\n\r\n    let validation = true;\r\n    let chartvalidation = true;\r\n    let dateColumnCount = 0;\r\n\r\n    this.columns.forEach(element => {\r\n      if (element.currency) {\r\n        const currency = this.currencies.find(d => d.code.toString() === element.currency);\r\n\r\n        if (element.currency === 'USD' || element.currency === 'GBP' || element.currency === 'EUR') {\r\n          element.currencySymbol = currency.symbol_native;\r\n        } else {\r\n          element.currencySymbol = element.currency + ' ' + currency.symbol_native;\r\n        }\r\n      }\r\n\r\n      if (element.format === 'date') {\r\n        dateColumnCount += 3;\r\n      }\r\n    });\r\n\r\n    if (this.reportColumnCount + dateColumnCount > this.reportConfig.maxColumnCount) {\r\n      this.messageSvc.showError('Error', 'Setting a column as a date will result in more than ' + this.reportConfig.maxColumnCount +\r\n        ' columns as the month, quarter and year components will be split out. Please reduce the number of columns in this report.');\r\n      return;\r\n    }\r\n\r\n    const tempColumn = this.columns;\r\n    const assignTempColumn = Object.assign({}, ...tempColumn.map((x) => ({ [x.name]: x })));\r\n    this.reportConfig.formatConfig.config = assignTempColumn;\r\n\r\n    if (this.selectedConnectorType?.key === ConnectorTypes.Connector\r\n      && ((constants.queryparameters.connector !== this.selectedConnector)\r\n        || (constants.queryparameters.remoteFunction !== this.selectedFunction)\r\n        || (constants.queryparameters.queryCategory !== this.selectedQueryCategory)\r\n        || (constants.queryparameters.queryName !== this.selectedQueryName))\r\n      || isViewChanged || isConnectionTypeChanged) {\r\n      this.reportConfig.params?.forEach(x => { x.action = 'delete'; });\r\n    }\r\n\r\n    if (this.allowAnonymous) {\r\n      validation = this.selectedUser ? true : false;\r\n    } else {\r\n      this.selectedUser = '0';\r\n    }\r\n\r\n    if (this.reportConfig.reportType === ReportType.Chart) {\r\n      this.reportConfig.chartType = this.chartType;\r\n      this.reportConfig.chartSubType = this.chartSubType;\r\n      this.reportConfig.chartConfigDetails = this.chartConfigDetails;\r\n    }\r\n    else {\r\n      this.reportConfig.chartType = null;\r\n      this.reportConfig.chartSubType = null;\r\n      this.reportConfig.chartConfigDetails = null;\r\n    }\r\n    if (this.reportConfig.reportType === ReportType.Chart) {\r\n      if (this.reportConfig.chartType) {\r\n        chartvalidation = true;\r\n\r\n        if (this.reportConfig.chartType !== 'line'\r\n          && this.reportConfig.chartType !== 'histogram'\r\n          && this.reportConfig.chartType !== 'treemap') {\r\n          chartvalidation = (this.reportConfig.chartSubType ? true : false);\r\n        }\r\n      } else {\r\n        chartvalidation = false;\r\n      }\r\n    }\r\n\r\n    if (this.drillThroughs.length > 0 && this.drillThroughs[0].displayColumn !== '') {\r\n      this.reportConfig.goldenArrowSetup = this.drillThroughs;\r\n    }\r\n\r\n    if (validation && chartvalidation) {\r\n      if (this.selectedUser) {\r\n        this.reportConfig.anonymousUser = this.user.find(d => d.id.toString() === this.selectedUser);\r\n      }\r\n      this.isSaveDisabled = true;\r\n      this.reportadminservice.addReportSettings(this.projectId, this.projectVersionId, this.reportConfig).subscribe({\r\n        next: (response) => {\r\n          this.reportConfig = response;\r\n          this.reportadminservice.onParameterOptionsChanged.next({ dataView: this.selectedDataView, datastore: this.selectedDatastore });\r\n          this.reportConfigOut.emit(this.reportConfig);\r\n          this.setTempQueryDetails(this.reportConfig);\r\n          if (this.dataViewDeleteMessage) {\r\n            this.messageSvc.showSuccess('Success', this.dataViewDeleteMessage);\r\n            this.dataViewDeleteMessage = '';\r\n          }\r\n          else {\r\n            this.messageSvc.showSuccess('Success', 'Saved Successfully', false, 2000);\r\n          }\r\n          this.isSaveDisabled = false;\r\n          this.showHelp = false;\r\n          constants.refreshReport = this.randomNumber(2, 4);\r\n          this.communicationService.next({\r\n            fromAppName: this.communicationService.currentAppName,\r\n            toAppName: this.communicationService.navigationAppName,\r\n            eventType: constants.EventType.REPORTS_UPDATED,\r\n            data: {}\r\n          });\r\n\r\n          this.emitParentGroupCommunicationEvent(this.parentGroup);\r\n        },\r\n        error: err => {\r\n          this.isSaveDisabled = false;\r\n          throw err;\r\n        }\r\n      });\r\n\r\n      if(this.gridAgentChatFlag){\r\n        this.agentAssetConfig.projectId = this.projectId;\r\n        this.agentAssetConfig.projectVersionId = this.projectVersionId;\r\n        this.agentAssetConfig.datastoreId = this.selectedDatastore?.id;\r\n        this.reportadminservice.saveAgentAssetConfig(this.reportConfig.reportId, this.reportConfig.reportType, this.agentAssetConfig).subscribe({\r\n          next: (response) => {\r\n            if (!response) {\r\n              this.messageSvc.showError('Error', 'Failed to save agent chat settings.');\r\n            }\r\n          },\r\n          error: err => {\r\n            this.isSaveDisabled = false;\r\n            this.messageSvc.showError('Error', 'Failed to save agent chat settings.');\r\n          }\r\n\r\n        });\r\n      }\r\n    }\r\n    else {\r\n      if (!validation) {\r\n        this.messageSvc.showError('error', 'Please select any user');\r\n      }\r\n      else if (!chartvalidation) {\r\n        this.messageSvc.showError('error', 'Please select chart type/sub type');\r\n      }\r\n    }\r\n  }\r\n\r\n  validateActionableGridColumnConfig(): boolean {\r\n    this.reportConfig.formatConfig.actionableGridColumnsConfig = this.selectedUserConfiguredFormat;\r\n    // validations for format columns\r\n    const emptyValidValues = this.getEmptyValidValues(this.reportConfig.formatConfig.actionableGridColumnsConfig);\r\n    if (emptyValidValues.length > 0) {\r\n      this.messageSvc.showError('Error', `No Values found for ${emptyValidValues[0].format.type} format!`);\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n\r\n  getEmptyValidValues(actionableGridColumnConfig: ActionableGridColumnConfig[]): ActionableGridColumnConfig[] {\r\n    return actionableGridColumnConfig?.filter(columnConfig => this.checkDropdown(columnConfig) || this.checkLink(columnConfig));\r\n  }\r\n\r\n  checkDropdown(columnConfig: ActionableGridColumnConfig): boolean {\r\n    return ((columnConfig.format.type === ActionableGridColumnType.Checkbox && columnConfig.format.baseType === JsonDataTypes.String)\r\n      || columnConfig.format.type === ActionableGridColumnType.Symbol\r\n      || (columnConfig.format.type === ActionableGridColumnType.String && columnConfig.dropdown))\r\n      && (!columnConfig.values || columnConfig.values.length === 0);\r\n  }\r\n\r\n  checkLink(columnConfig: ActionableGridColumnConfig): boolean {\r\n    return columnConfig.format.type === ActionableGridColumnType.Link && !columnConfig.link?.urlAlias;\r\n  }\r\n\r\n  async validateCalendarView(): Promise<boolean> {\r\n    const viewHashId = this.selectedDataView.viewId === '0' ? 'none' : await this.cryptoService.hashSHA256(this.selectedDataView.columnsToShow?.sort()?.toString());\r\n    if (this.reportConfig.calendarViewConfig.viewHashId !== viewHashId) {\r\n      this.messageSvc.showError('Selected view has been updated, please review calendar settings.');\r\n      this.reportConfig.calendarViewConfig.viewHashId = await this.cryptoService.hashSHA256(this.selectedDataView.columnsToShow?.sort()?.toString());\r\n      return false;\r\n    }\r\n\r\n    if (this.reportConfig.calendarViewConfig.startDateCol === this.reportConfig.calendarViewConfig.endDateCol) {\r\n      this.messageSvc.showError('Start Date and End Date fields of Calendar View settings must not be the same, please review these settings.');\r\n      return false;\r\n    }\r\n\r\n    if (this.reportConfig.calendarViewConfig.titleCol?.length === 0) {\r\n      this.messageSvc.showError('Title/Description Fields is a required setting, please review Calendar View Settings.');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  validateDynamicAsset(): boolean {\r\n    if (this.reportConfig?.formatConfig?.dynamicAssetInfo?.type === DynamicAssetTypes.ConnectorApp\r\n      && !this.reportConfig?.formatConfig?.dynamicAssetInfo?.idField) {\r\n      this.messageSvc.showError('Error', 'When a Connector Screen is selected for edit mode an Id Field must be chosen.');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  getUsers(projectId): any {\r\n    this.reportadminservice.getUsers(projectId).subscribe({\r\n      next: (response) => {\r\n        this.user = response;\r\n        for (const user of response) {\r\n          this.users.push(\r\n            {\r\n              label: user.firstName.trim() + ' ' + user.lastName.trim(),\r\n              value: user.id.toString()\r\n            }\r\n          );\r\n        }\r\n        this.users.sort((x, y) => x.label.localeCompare(y.label));\r\n\r\n        if (this.reportConfig.anonymousUser) {\r\n          this.selectedUser = this.reportConfig.anonymousUser.id > 0 ? this.reportConfig.anonymousUser.id.toString() : '0';\r\n        }\r\n      },\r\n      error: err => {\r\n        throw err;\r\n      }\r\n    });\r\n  }\r\n\r\n  copyToClipboard(item): void {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n    const listener = (e: ClipboardEvent) => {\r\n      e.clipboardData.setData('text/plain', (item));\r\n      e.preventDefault();\r\n    };\r\n    document.addEventListener('copy', listener);\r\n    document.execCommand('copy');\r\n    document.removeEventListener('copy', listener);\r\n    this.messageSvc.showSuccess('Copied to Clipboard', 'Copied to Clipboard');\r\n  }\r\n\r\n  onClickDelete(): void {\r\n    this.displayModal = true;\r\n  }\r\n\r\n  deleteReport(): void {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n    this.reportadminservice.deleteReport(this.reportConfig.reportId).subscribe({\r\n      next: (response) => {\r\n        if (response) {\r\n          this.messageSvc.showSuccess('Deleted', 'Deleted Successfully');\r\n          window.location.assign('/reporting/report-setup/report/1/1/0');\r\n        }\r\n        else {\r\n          this.messageSvc.showError('Error', 'Delete failed.');\r\n        }\r\n      },\r\n      error: err => {\r\n        this.messageSvc.showError('Error', err);\r\n        throw err;\r\n      }\r\n    });\r\n    this.displayModal = false;\r\n  }\r\n\r\n  onConnectorChange() {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n    this.reportadminservice.getFunctions(this.projectVersionId, this.selectedConnector).subscribe({\r\n      next: (response) => {\r\n        this.functions = [];\r\n        if (response.length > 0) {\r\n          response.forEach(value => {\r\n            this.functions.push({ value: value.key, label: value.name });\r\n          });\r\n        }\r\n\r\n        if (this.reportConfig.queryColumnsFunction) {\r\n          const index = this.functions.findIndex(x => x.value === this.reportConfig.queryColumnsFunction);\r\n\r\n          if (index !== -1) {\r\n            this.functions = this.functions.filter(item => item.value !== this.reportConfig.queryColumnsFunction);\r\n          }\r\n        }\r\n\r\n        if (this.reportConfig.categoryFunction) {\r\n          const index = this.functions.findIndex(x => x.value === this.reportConfig.categoryFunction);\r\n\r\n          if (index !== -1) {\r\n            this.categoryFunctionExists = true;\r\n            this.functions = this.functions.filter(item => item.value !== this.reportConfig.categoryFunction);\r\n          } else {\r\n            this.categoryFunctionExists = false;\r\n          }\r\n        }\r\n\r\n        if (this.reportConfig.categoryFunction) {\r\n          const index = this.functions.findIndex(x => x.value === this.reportConfig.queryFunction);\r\n\r\n          if (index !== -1) {\r\n            this.queryFunctionExists = true;\r\n            this.functions = this.functions.filter(item => item.value !== this.reportConfig.queryFunction);\r\n          } else {\r\n            this.queryFunctionExists = false;\r\n          }\r\n        }\r\n\r\n        this.selectedFunction = this.functions[0].value;\r\n        if (this.categoryFunctionExists) {\r\n          this.getQueryCategories();\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  onConnectorTypeChange() {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n    this.setFormatColumnsDataSource();\r\n  }\r\n\r\n  numberOnly(event): boolean {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n    const charCode = (event.which) ? event.which : event.keyCode;\r\n    if (charCode > 31 && (charCode < 48 || charCode > 57)) {\r\n      return false;\r\n    }\r\n    return true;\r\n\r\n  }\r\n\r\n  checkTimeOutValid(): void {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n    const connectionTimeOutSelected = this.selectedConnectionTimeOut;\r\n    if (connectionTimeOutSelected >= 60) {\r\n      // empty\r\n    } else if (connectionTimeOutSelected < 60 && connectionTimeOutSelected >= 5) {\r\n      this.confirmLessThan60Seconds(connectionTimeOutSelected);\r\n    } else {\r\n      this.minimunCache = true;\r\n    }\r\n  }\r\n\r\n  confirmLessThan60Seconds(connectionTimeOutSelected) {\r\n    this.confirmationService.confirm({\r\n      message: '<p style=\"white-space: pre-line\">Reducing data caching refresh times below 60 seconds can result in system performance issues.\\n This is not recommended, do you wish to proceed?.</p>',\r\n      header: 'Are you sure that you want to proceed?',\r\n      icon: 'pi pi-exclamation-triangle',\r\n      acceptButtonStyleClass: 'p-button-outlined p-button-info',\r\n      rejectButtonStyleClass: 'p-button-outlined p-button-info',\r\n      accept: () => {\r\n        this.selectedConnectionTimeOut = connectionTimeOutSelected;\r\n      },\r\n      reject: () => {\r\n        this.selectedConnectionTimeOut = 300;\r\n        this.renderer.selectRootElement('#connectionTimeOut').focus();\r\n      }\r\n    });\r\n  }\r\n\r\n  hideModal() {\r\n    this.minimunCache = false;\r\n    this.selectedConnectionTimeOut = 60;\r\n  }\r\n\r\n  onChartChange() {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n\r\n    const Info = this.chartTypes.find(p => p.key === this.chartType);\r\n    this.chartSubTypes = Info.subTypes;\r\n    this.chartConfigDetails = Info.config;\r\n    if (this.chartSubTypes?.length) {\r\n      this.chartSubType = this.chartSubTypes[0].key;\r\n    }\r\n  }\r\n\r\n  getFeatureAssignment(): any {\r\n    this.reportadminservice.getFeatureAssignment().subscribe({\r\n      next: (response) => {\r\n        if (response) {\r\n          const featureflag = response.find(s => s.name === constants.FeatureFlag.STAND_ALONE_CHART);\r\n          if (featureflag) {\r\n            this.standaloneChartfeatuerFlag = true;\r\n            this.categories.push({ name: 'Chart', key: 'chart' });\r\n          }\r\n        }\r\n\r\n      },\r\n      error: err => {\r\n        throw err;\r\n      }\r\n    });\r\n  }\r\n\r\n  // this should be the new way to do it.  will need to refactor the other feature flag calls\r\n  async setFeatureFlags(): Promise<void> {\r\n    const featureFlags = await this.featureFlagService.getFeatureFlags();\r\n\r\n    this.visualizationFlag = featureFlags.some(x => x.name === FeatureFlag.Visualizations);\r\n    this.gridChartsFlag = featureFlags.some(x => x.name === FeatureFlag.Visualizations); // (FeatureFlag.GridCharts);\r\n    this.groupTagFlag = featureFlags.some(x => x.name === FeatureFlag.AssetGroupTags);\r\n    this.calendarViewFlag = featureFlags.some(x => x.name === FeatureFlag.CalendarView);\r\n    this.advancedJsonEditFlag = featureFlags.some(x => x.name === FeatureFlag.AdvancedJsonEdit);\r\n    this.gridAgentChatFlag = featureFlags.some(x => x.name === FeatureFlag.GridAgentChat);\r\n  }\r\n\r\n  getDatastores(projectId, projectVersionId): void {\r\n    this.datastoresService.getDatastores(projectId, projectVersionId).pipe(take(1)).subscribe(datastores => {\r\n      this.datastores = datastores;\r\n      if (this.reportConfig.connectionType === ConnectorTypes.Datastore && this.reportConfig.formName) {\r\n        this.selectedDatastore = this.datastores?.find(x => x.name === this.reportConfig.formName);\r\n        this.prevDatastore = this.selectedDatastore;\r\n        this.getDataViewList();\r\n        if (!this.selectedDatastore) {\r\n          this.messageSvc.showError('Error', `Datastore not found!`);\r\n        }\r\n\r\n        this.getDatastoreAssets();\r\n      }\r\n    });\r\n  }\r\n\r\n  randomNumber(min, max) {\r\n    return Math.random() * (max - min) + min;\r\n  }\r\n\r\n  setParamAction(): any {\r\n    this.reportadminservice.getReportSettingsById(this.reportConfig.reportId).subscribe({\r\n      next: (response) => {\r\n        if ((response.connectorconfig.connector !== this.selectedConnector) ||\r\n          (response.connectorconfig.remoteFunction !== this.selectedFunction) ||\r\n          (response.queryCategory !== this.selectedQueryCategory) ||\r\n          (response.queryName !== this.selectedQueryName)) {\r\n          this.reportConfig.params?.forEach(x => {\r\n            x.action = 'delete';\r\n          });\r\n        }\r\n      },\r\n      error: err => {\r\n        throw err;\r\n      }\r\n    });\r\n  }\r\n\r\n  clearSelectedDrillThroughs() {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n\r\n    this.selectedDrillThroughs?.forEach(s => {\r\n      const index = this.drillThroughs.findIndex(d => d === s);\r\n      if (index > -1) {\r\n        this.drillThroughs.splice(index, 1);\r\n      }\r\n    });\r\n    this.selectedDrillThroughs = [];\r\n  }\r\n\r\n  drillThroughCheckedChange(checked: boolean, drillThrough: DrillThrough) {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n\r\n    if (checked) {\r\n      this.selectedDrillThroughs.push(drillThrough);\r\n    } else {\r\n      const index = this.selectedDrillThroughs.findIndex(s => s === drillThrough);\r\n      if (index > -1) {\r\n        this.selectedDrillThroughs.splice(index, 1);\r\n      }\r\n    }\r\n  }\r\n\r\n  addNew() {\r\n    this.defaultDrillThrough = {\r\n      displayColumn: '',\r\n      openIn: '0',\r\n      urlAlias: '',\r\n      urlSubstitution: [new UrlSub()],\r\n      gridGeneratedLink: ''\r\n    };\r\n    this.drillThroughs.push(this.defaultDrillThrough);\r\n  }\r\n\r\n  deleteProduct(drillThrough: DrillThrough) {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n    const index = this.drillThroughs.findIndex(x => x === drillThrough);\r\n    this.drillThroughs.splice(index, 1);\r\n    this.messageSvc.showSuccess('Row deleted.');\r\n  }\r\n\r\n  addSubstitution(urlSubstitution: UrlSub[]) {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n    urlSubstitution.push(new UrlSub());\r\n  }\r\n\r\n  deleteSubstitution(urlSubstitution: UrlSub[], index) {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n    urlSubstitution.splice(index, 1);\r\n  }\r\n  // method to update the generated link preview column when the drill through options are updated\r\n  onSubstitutionChange(drillThrough: DrillThrough) {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n\r\n    let generatedLink = drillThrough.urlAlias;\r\n    const params = generatedLink.split('{');\r\n    params?.forEach(x => {\r\n      if (x.length > 0) {\r\n        const index = x.split('}')[0];\r\n        if (_isNumberValue(index)) {\r\n          if (drillThrough.urlSubstitution.length > Number(index)) {\r\n            const actualValue = '{' + drillThrough.urlSubstitution[index].urlName + '}';\r\n            const tempValue = '{' + index + '}';\r\n            if (actualValue)\r\n              generatedLink = generatedLink.replace(tempValue, actualValue);\r\n          }\r\n        }\r\n      }\r\n    });\r\n\r\n    drillThrough.gridGeneratedLink = generatedLink;\r\n  }\r\n\r\n  setTempQueryDetails(reportConfig: any) {\r\n    constants.queryparameters.connector = reportConfig.connectorconfig.configName;\r\n    constants.queryparameters.remoteFunction = reportConfig.connectorconfig.remoteFunction;\r\n    constants.queryparameters.queryCategory = reportConfig.queryCategory;\r\n    constants.queryparameters.queryName = reportConfig.queryName;\r\n  }\r\n\r\n  getQueryCategories(): void {\r\n    this.reportadminservice.getQueryCategories(this.customerId, this.projectId, this.projectVersionId,\r\n      this.selectedConnector, this.reportConfig.categoryFunction)\r\n      .subscribe({\r\n        next: (response) => {\r\n          if (response) {\r\n            this.queryCategory = [];\r\n            let itemlabel = '';\r\n            let itemval = '';\r\n            response.forEach(item => {\r\n              itemlabel = item.categoryName;\r\n              if (!itemlabel) {\r\n                itemlabel = ' ';\r\n                itemval = '0';\r\n              } else {\r\n                itemval = item.categoryName;\r\n              }\r\n\r\n              this.queryCategory.push({ value: itemval, label: itemlabel });\r\n            });\r\n\r\n            if (this.reportConfig?.queryCategory || this.reportConfig?.queryCategory === '') {\r\n              this.getQueryNames();\r\n            }\r\n          }\r\n        }\r\n      });\r\n  }\r\n\r\n  getQueryNames(): void {\r\n    this.reportadminservice.getQueryNames(this.customerId, this.projectId, this.projectVersionId, this.selectedConnector,\r\n      this.reportConfig.queryFunction, this.selectedQueryCategory).subscribe({\r\n        next: (response) => {\r\n          if (response) {\r\n            this.queryName = [];\r\n            response.forEach(item => {\r\n              this.queryName.push({ value: item.qName, label: item.qName });\r\n            });\r\n          }\r\n        }\r\n      });\r\n  }\r\n\r\n  onCategoryChange() {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n\r\n    this.getQueryNames();\r\n  }\r\n\r\n  onSelectedDatastoreChange() {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n\r\n    if (this.calendarViewFlag && this.reportConfig.enableCalendarView) {\r\n      this.confirmationService.confirm({\r\n        header: 'Calendar View Configured',\r\n        message: 'Changing the datastore will reset this configuration, would you like to continue?',\r\n        accept: () => {\r\n          this.prevDatastore = this.selectedDatastore;\r\n\r\n          this.getDataViewList();\r\n\r\n          this.getDatastoreAssets();\r\n\r\n          this.resetCalendarView();\r\n        },\r\n        reject: () => {\r\n          this.selectedDatastore = this.prevDatastore;\r\n        }\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.prevDatastore = this.selectedDatastore;\r\n\r\n    this.getDataViewList();\r\n\r\n    this.getDatastoreAssets();\r\n\r\n    this.resetCalendarView();\r\n  }\r\n\r\n  setFormatColumnsDataSource() {\r\n    if (!this.selectedDataView) {\r\n      return;\r\n    }\r\n\r\n    // If it is not actionablegrid follow the old way\r\n    if (this.reportConfig.reportType !== ReportType.ActionableGrid) {\r\n      this.columnsToShow = this.selectedDataView.viewId === '0' ? [] : this.selectedDataView.columnsToShow;\r\n      this.getColumnNames();\r\n      return;\r\n    }\r\n\r\n    // Lazy loading default format settings based on base schema\r\n    this.selectedUserConfiguredFormat = this.cachedUserConfiguredFormats\r\n      .find(ucf => ucf.datastoreId === this.selectedDatastore.id && ucf.dataViewId === this.selectedDataView.viewId)?.userConfiguredFormat;\r\n    if (!this.selectedUserConfiguredFormat && this.selectedDatastore.baseSchema?.properties) {\r\n      if (!this.cachedFormatSettings[this.selectedDatastore.id]) {\r\n        this.cachedFormatSettings[this.selectedDatastore.id] =\r\n          this.reportadminservice.mapBaseSchemaToFormatSettings(this.selectedDatastore?.baseSchema);\r\n      }\r\n\r\n      // If there is already a customized setting for this report, load the setting, else load it based on baseSchema\r\n      if (this.selectedDatastore.name === this.reportConfig.formName &&\r\n        this.reportConfig.formatConfig.actionableGridColumnsConfig &&\r\n        (this.selectedDataView.viewId === this.reportConfig.dataStoreViewName ||\r\n          (this.selectedDataView.viewId === '0' && !this.reportConfig.dataStoreViewName))) {\r\n        this.selectedUserConfiguredFormat = this.reportConfig?.formatConfig?.actionableGridColumnsConfig;\r\n\r\n        // Update the colums type and if there is no view selected, update the columns list with the latest baseSchema\r\n        this.selectedUserConfiguredFormat =\r\n          this.updateColumnsWithBaseSchema(\r\n            this.selectedDataView.viewId === '0',\r\n            this.selectedUserConfiguredFormat,\r\n            this.cachedFormatSettings[this.selectedDatastore.id]);\r\n      }\r\n      else {\r\n        // if there is no view selected(0), show default setting\r\n        // note: We are lazy loading here and we need a deep copy of the default settings,\r\n        // notice:_.deepCloneObject dosn't preserve the object type\r\n        if (this.selectedDataView.viewId === '0') {\r\n          this.selectedUserConfiguredFormat = deepCopy<ActionableGridColumnConfig[]>(\r\n            this.cachedFormatSettings[this.selectedDatastore.id]);\r\n          return;\r\n        }\r\n        this.selectedUserConfiguredFormat = [];\r\n        this.selectedDataView.columnsToShow?.forEach(col => {\r\n          const columnConfig = this.cachedFormatSettings[this.selectedDatastore.id].find(x => x.column === col);\r\n\r\n          if (columnConfig) {\r\n            this.selectedUserConfiguredFormat.push(deepCopy<ActionableGridColumnConfig>(columnConfig));\r\n          }\r\n        });\r\n      }\r\n\r\n      this.cachedUserConfiguredFormats.push(\r\n        {\r\n          datastoreId: this.selectedDatastore.id,\r\n          dataViewId: this.selectedDataView.viewId,\r\n          userConfiguredFormat: this.selectedUserConfiguredFormat\r\n        });\r\n    }\r\n  }\r\n\r\n  updateColumnsWithBaseSchema(updateColumnsList = false, formatSettings: ActionableGridColumnConfig[], defaultFormatSettings: ActionableGridColumnConfig[]): ActionableGridColumnConfig[] {\r\n    // Just to make code easier to read\r\n    const stringFormats = [ActionableGridColumnType.Checkbox, ActionableGridColumnType.String, ActionableGridColumnType.Symbol];\r\n    formatSettings = formatSettings ?? [];\r\n\r\n    if (updateColumnsList) {\r\n      // Removing deleted columns\r\n      for (let i = formatSettings.length - 1; i >= 0; i--) {\r\n        const col = formatSettings[i];\r\n        const index = defaultFormatSettings.findIndex(x => x.column === col.column);\r\n        if (index === -1) {\r\n          formatSettings.splice(formatSettings.findIndex(sfs => sfs.column === col.column), 1);\r\n        }\r\n      }\r\n\r\n      // Adding new columns. Not: we can't use formatSettings = ...\r\n      formatSettings = formatSettings.concat(defaultFormatSettings\r\n        .filter(value => !formatSettings.some(y => y.column === value.column)));\r\n    }\r\n\r\n    // Updating column types with the latest version of base schema\r\n    formatSettings.forEach(col => {\r\n      const defaultColConfig = defaultFormatSettings.find(x => x.column === col.column);\r\n\r\n      if (defaultColConfig) {\r\n        col.format.baseType = defaultColConfig.format.baseType;\r\n        col.format.baseFormat = defaultColConfig.format.baseFormat;\r\n        col.typeDescription = defaultColConfig.typeDescription;\r\n        col.format.currency = defaultColConfig.format.currency;\r\n        col.format.decimalPlaces = defaultColConfig.format.decimalPlaces;\r\n        col.displayName = defaultColConfig.displayName;\r\n        col.schemaProperty = defaultColConfig.schemaProperty;\r\n\r\n        /**\r\n         * If format type is link, do not overwrite\r\n         * If it is not string, it is not overridable. So overwrite\r\n         * If it is date, it is not overridable. So overwrite\r\n         * If it is string, it is overridable. So only overwrite if it is not one of the existing string format types.\r\n         * (consider type change from datastore)\r\n         */\r\n        if (col.format.type !== ActionableGridColumnType.Link &&\r\n          ![ActionableGridColumnType.DateTime, ActionableGridColumnType.Date].includes(col.format.type) &&\r\n          (defaultColConfig.format.baseType !== JsonDataTypes.String || !stringFormats.includes(col.format.type))) {\r\n          col.format.type = defaultColConfig.format.type;\r\n        }\r\n\r\n        if (defaultColConfig.schemaProperty?.typeProperties?.required) {\r\n          col.required = defaultColConfig.required;\r\n        }\r\n\r\n        if (col.required && col.column !== this.selectedDatastore.primaryKey) {\r\n          col.allowUserUpdate = true;\r\n        }\r\n\r\n        if (col.format.baseType === JsonDataTypes.Array) {\r\n          col.children = this.updateColumnsWithBaseSchema(updateColumnsList, col.children, defaultColConfig.children);\r\n        }\r\n      }\r\n    });\r\n\r\n    return formatSettings;\r\n  }\r\n\r\n  getColumnNames() {\r\n    // Actionable grid loads format settings differently, later we can follow the same method for other report types.\r\n    if (this.projectIsLocked || this.reportConfig.reportType === ReportType.ActionableGrid) {\r\n      return;\r\n    }\r\n\r\n    this.setDataSouce();\r\n    if (this.reportConfig.dataSourceInfo.dataSource) {\r\n      const savedColumnConfigList = this.getSavedColumnConfigList();\r\n\r\n      // columns is the full list of columns.\r\n      this.columns = [];\r\n      // formatColumns is what is bound to the html(basically list of the columns that we show to the user)\r\n      this.formatColumns = [];\r\n\r\n      //  urlColumns can be completely removed as we can use the same 'columns' list\r\n      this.urlColumns = [];\r\n\r\n      if (this.selectedConnectorType?.key === ConnectorTypes.Datastore) {\r\n        this.getColumnListFromBaseSchema(savedColumnConfigList);\r\n      } else {\r\n        this.columnsToShow = [];\r\n        this.getColumnListFromApi(savedColumnConfigList);\r\n      }\r\n    }\r\n  }\r\n\r\n  getSavedColumnConfigList() {\r\n    if (this.reportConfig.formatConfig?.config &&\r\n      ((this.reportConfig.connectionType === ConnectorTypes.Datastore &&\r\n        this.reportConfig.formName === this.selectedDatastore?.name) ||\r\n        (this.reportConfig.connectionType === ConnectorTypes.Connector &&\r\n          this.reportConfig.queryName === this.selectedQueryName))) {\r\n      return Object.values<ColumnConfig>(this.reportConfig.formatConfig.config);\r\n    }\r\n    return [];\r\n  }\r\n\r\n  getColumnListFromBaseSchema(savedColumnConfigList: ColumnConfig[]) {\r\n    if (this.selectedDatastore?.baseSchema?.properties) {\r\n      Object.keys(this.selectedDatastore.baseSchema.properties).forEach(e => {\r\n        const colName = e.toString();\r\n        // Note: to get the list of properties of each column: form.baseSchema.properties[e]\r\n        const colConfig = this.selectedDatastore.baseSchema.properties[e];\r\n\r\n        if (colConfig.type !== JsonDataTypes.Array && colConfig.type !== JsonDataTypes.Object) {\r\n          let column = savedColumnConfigList?.find(p => p.name === colName);\r\n\r\n          if (!column) {\r\n            column = new ColumnConfig(colName);\r\n\r\n            switch (colConfig.type) {\r\n              case JsonDataTypes.String:\r\n                column.format = colConfig.format === JsonStringFormats.DateTime || colConfig.format === JsonStringFormats.Date ? \"date\" : \"string\";\r\n                break;\r\n              case JsonDataTypes.Boolean:\r\n                column.format = \"string\";\r\n                break;\r\n              case JsonDataTypes.Integer:\r\n              case JsonDataTypes.Decimal:\r\n                column.format = colConfig?.typeProperties?.currency ? \"currency\" : \"numeric\";\r\n                break;\r\n              default:\r\n                column.format = colConfig.type;\r\n                break;\r\n            }\r\n\r\n            column.displayName = colConfig?.presentationProperties?.displayName;\r\n            column.decimalPlaces = colConfig?.presentationProperties?.decimalPlaces ?? 0;\r\n            column.currency = colConfig?.typeProperties?.currency;\r\n          }\r\n\r\n          column.baseType = colConfig.type;\r\n          column.baseFormat = colConfig.format;\r\n          const type = Object.keys(JsonDataTypes).filter(v => JsonDataTypes[v] === colConfig.type);\r\n\r\n          if (type.length > 0) {\r\n            column.typeDescription = (colConfig.format === JsonStringFormats.DateTime || colConfig.format === JsonStringFormats.Date) ? `Date` : type[0];\r\n          }\r\n\r\n          // if it is not in the list of columnsToShow do not show this column to user.\r\n          const showColumn: boolean = (this.columnsToShow.length === 0 || this.columnsToShow?.find(x => x === colName));\r\n\r\n          if (showColumn || !this.selectedDataView) {\r\n            this.formatColumns.push(column);\r\n          }\r\n\r\n          this.columns.push(column);\r\n          this.urlColumns.push({ value: column.name, label: column.name });\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  getColumnListFromApi(savedColumnConfigList: ColumnConfig[]) {\r\n    this.reportadminservice.getReportColumns(this.projectId, this.projectVersionId, this.reportConfig).subscribe({\r\n      next: (columnNameList) => {\r\n        this.isSaveDisabled = false;\r\n        if (columnNameList) {\r\n          this.reportColumnCount = (this.columnsToShow.length > 0) ? this.columnsToShow.length : columnNameList.length;\r\n\r\n          columnNameList.forEach(colName => {\r\n            // if it is not in the list of columnsToShow do not show this column to user.\r\n            const showColumn: boolean = (this.columnsToShow.length === 0 || this.columnsToShow?.find(x => x.name === colName));\r\n\r\n            // if we didn't find the column in the saved list or this is the first time, create a new column.\r\n            const column = savedColumnConfigList?.find(p => p.name === colName)\r\n              ?? new ColumnConfig(colName, (colName.toLowerCase().includes('date') ? 'date' : 'string'));\r\n\r\n            if (showColumn) {\r\n              this.formatColumns.push(column);\r\n            }\r\n            this.columns.push(column);\r\n            this.urlColumns.push({ value: column.name, label: column.name });\r\n          });\r\n        }\r\n      },\r\n      error: err => {\r\n        this.messageSvc.showError('Error', err.message);\r\n      }\r\n    });\r\n  }\r\n\r\n  setDecimalPlaces(rowData: ColumnConfig) {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n    rowData.decimalPlaces = rowData.format === 'currency' ? 2 : 0;\r\n  }\r\n\r\n  setDataSouce() {\r\n    this.reportConfig.dataSourceInfo = { id: 0, dataSource: '', formName: '', connectorInfo: {}, queryCategory: '', queryName: '' };\r\n    this.reportConfig.dataSourceInfo.formName = this.selectedDatastore?.name;\r\n    this.reportConfig.dataSourceInfo.dataSource = this.selectedConnectorType ? this.selectedConnectorType.key : '';\r\n    this.reportConfig.dataSourceInfo.connectorInfo.configName = this.selectedConnector;\r\n    this.reportConfig.dataSourceInfo.connectorInfo.remoteFunction = this.reportConfig.queryColumnsFunction;\r\n    this.reportConfig.dataSourceInfo.queryCategory = this.selectedQueryCategory;\r\n    this.reportConfig.dataSourceInfo.queryName = this.selectedQueryName;\r\n  }\r\n\r\n  openAssetModal() {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n\r\n    this.communicationService.next({\r\n      fromAppName: this.communicationService.currentAppName,\r\n      toAppName: this.communicationService.navigationAppName,\r\n      eventType: 'openAssetModal',\r\n      data: {\r\n        assetId: this.reportConfig.reportId,\r\n        assetType: 'Report',\r\n        assetName: this.reportConfig.reportName,\r\n        tenantId: this.reportConfig.tenantId,\r\n        projectId: this.reportConfig.projectId,\r\n        environmentName: this.reportConfig.environment,\r\n        projectName: null\r\n      }\r\n    });\r\n  }\r\n  geProjectVariables() {\r\n    this.reportadminservice.getProjectVariables(this.projectVersionId).subscribe({\r\n      next: (response) => {\r\n        if (response) {\r\n          this.projectVariables = response;\r\n          response.forEach(element => {\r\n            this.urlProjectVariables.push({ value: element.variable, label: element.variable, title: element.description });\r\n          });\r\n        }\r\n      },\r\n      error: err => {\r\n        throw err;\r\n      }\r\n    });\r\n  }\r\n\r\n  deleteView(dataView: DataView) {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n\r\n    this.confirmationService.confirm({\r\n      message: 'Are you sure that you want to delete this view?',\r\n      accept: () => {\r\n        const index = this.selectedDatastore.dataViews.indexOf(dataView);\r\n        const copiedSelectedDatastore = deepCopy<Datastore>(this.selectedDatastore);\r\n        copiedSelectedDatastore.dataViews.splice(index, 1);\r\n        this.datastoresService.updateDatastore(copiedSelectedDatastore.id, copiedSelectedDatastore)\r\n          .pipe(take(1)).subscribe({\r\n            next: (response) => {\r\n              this.dataViewDeleteMessage = 'Data view ' + dataView.name + ' deleted successfully';\r\n              this.selectedDatastore.dataViews.splice(index, 1);\r\n              this.getDataViewList();\r\n            },\r\n            error: exception => {\r\n              this.messageSvc.showError('Error', exception);\r\n            }\r\n          });\r\n      }\r\n    });\r\n\r\n  }\r\n\r\n  openAnalyticsFilterModal(formMode: string, dataView?: DataView) {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n\r\n    this.communicationService.next({\r\n      fromAppName: this.communicationService.currentAppName,\r\n      toAppName: this.communicationService.saltboxAnalyticsFilterAppName,\r\n      eventType: constants.EventType.OPEN_ANALTYICS_FILTER,\r\n      data: {\r\n        datastore: deepCopy<Datastore>(this.selectedDatastore),\r\n        dataView: deepCopy<DataView>(dataView),\r\n        mode: formMode\r\n      }\r\n    });\r\n  }\r\n\r\n  getDataViewList(): void {\r\n    const defaultView = new DataView('No View Selected', '0');\r\n    this.dataViews = [defaultView, ...(this.selectedDatastore?.dataViews || [])];\r\n    this.selectSavedViewOnReportSettings();\r\n  }\r\n\r\n  async selectSavedViewOnReportSettings() {\r\n    if (!this.selectedDatastore)\r\n      return;\r\n\r\n    // if there is already a saved selected view for this report, select the view else select 'No View Selected'\r\n    if (this.reportConfig?.formName === this.selectedDatastore.name && this.reportConfig.dataStoreViewName) {\r\n      const view = this.dataViews.find(f => f.viewId === this.reportConfig.dataStoreViewName);\r\n      // we can show a warning that we couldn't find the view\r\n      // if we couldn't find the view we gonna select 'No View Selected'\r\n      this.selectedDataView = view ?? this.dataViews[0];\r\n      this.prevDataView = this.selectedDataView;\r\n\r\n      // check viewHashId here to ensure data view and calendar view are still compatible\r\n      if (this.calendarViewFlag && this.reportConfig.enableCalendarView) {\r\n        const viewHashId = this.selectedDataView.viewId === '0' ? 'none' : await this.cryptoService.hashSHA256(this.selectedDataView.columnsToShow?.sort()?.toString());\r\n\r\n        if (this.reportConfig.calendarViewConfig.viewHashId !== viewHashId) {\r\n          this.messageSvc.showWarning('Selected view has been updated, please review calendar settings.');\r\n          this.reportConfig.calendarViewConfig.viewHashId = await this.cryptoService.hashSHA256(this.selectedDataView.columnsToShow?.sort()?.toString())\r\n        }\r\n      }\r\n\r\n      this.reportConfig.params.filter(param => !this.selectedDataView?.filterConditions.find(x => x.columnUID === param.columnUID))\r\n        .forEach(param => { param.action = 'delete'; });\r\n      this.paramListUpdated.emit(this.reportConfig.params);\r\n\r\n      this.reportadminservice.onParameterOptionsChanged.next({ dataView: this.selectedDataView, datastore: this.selectedDatastore });\r\n    }\r\n    else {\r\n      // it doesn't detect the changes in some cases after angular 15! just added a changeDetectorRef to detect changes\r\n      this.changeDetectorRef.detectChanges();\r\n      this.selectedDataView = this.dataViews[0];\r\n      this.prevDataView = this.selectedDataView;\r\n    }\r\n    // Since we are manually setting the dropdown we need to set format columns too\r\n    this.setFormatColumnsDataSource();\r\n  }\r\n\r\n  onSelectedViewChange(): any {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n\r\n    if (this.calendarViewFlag && this.reportConfig.enableCalendarView) {\r\n      this.confirmationService.confirm({\r\n        header: 'Calendar View Configured',\r\n        message: 'Changing the data view will reset this configuration, would you like to continue?',\r\n        accept: () => {\r\n          this.prevDataView = this.selectedDataView;\r\n          this.setFormatColumnsDataSource();\r\n          this.resetCalendarView();\r\n        },\r\n        reject: () => {\r\n          this.selectedDataView = this.prevDataView;\r\n        }\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.prevDataView = this.selectedDataView;\r\n    this.setFormatColumnsDataSource();\r\n    this.resetCalendarView();\r\n  }\r\n\r\n  private resetCalendarView() {\r\n    this.reportConfig.enableCalendarView = false;\r\n    this.reportConfig.calendarViewConfig = null;\r\n  }\r\n\r\n  selectUrlSubstitution(urlSubstitution: UrlSub, rowIndex, urlSubstitutionIndex) {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n\r\n    if (urlSubstitution[urlSubstitutionIndex].isProjectVariable) {\r\n      this.urlSubType = 'projectVariable';\r\n      this.selectedProjectVariable = this.urlProjectVariables.find(x => x.label === urlSubstitution[urlSubstitutionIndex].urlName);\r\n    }\r\n    else {\r\n      this.urlSubType = 'column';\r\n      this.selectedColumn = this.urlColumns.find(x => x.label === urlSubstitution[urlSubstitutionIndex].urlName);\r\n    }\r\n    this.displayURLSubstitution = true;\r\n    this.selectedUrlSubstitution = urlSubstitution[urlSubstitutionIndex].urlName;\r\n    this.selectedRowIndex = rowIndex;\r\n    this.selectedUrlSubIndex = urlSubstitutionIndex;\r\n  }\r\n\r\n  applyURLSubstitution() {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n    const selectedDrillThrough = this.drillThroughs[this.selectedRowIndex];\r\n    const selectedUrlSub = selectedDrillThrough.urlSubstitution[this.selectedUrlSubIndex];\r\n    selectedUrlSub.isProjectVariable = this.urlSubType !== 'column';\r\n    selectedUrlSub.urlName = (selectedUrlSub.isProjectVariable ? this.selectedProjectVariable : this.selectedColumn).value;\r\n    this.onSubstitutionChange(selectedDrillThrough);\r\n    this.displayURLSubstitution = false;\r\n  }\r\n\r\n  subscribeToInterAppCommunication(): any {\r\n    this.interAppSubscription = this.communicationService.getObservable().subscribe(event => {\r\n      this.ngZone.run(() => {\r\n        if (event.eventType === constants.EventType.SAVED_DATAVIEW_FILTER) {\r\n          if (event.data.dataView) {\r\n            this.updateDataViewList(event.data.dataView);\r\n          }\r\n        }\r\n        else if (event.eventType === constants.EventType.ERROR_ANALTYICS_FILTER) {\r\n          if (event.data.error) {\r\n            this.messageSvc.showError('Error', event.data.error);\r\n            console.log(event.data.error);\r\n          }\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  updateDataViewList(dataView: DataView) {\r\n    if (!dataView) {\r\n      return;\r\n    }\r\n\r\n    if (!this.selectedDatastore?.dataViews) {\r\n      this.selectedDatastore.dataViews = [];\r\n    }\r\n\r\n    // finding the view in the list\r\n    const currentDataView = this.selectedDatastore?.dataViews.find(cfw => cfw.viewId === dataView.viewId);\r\n\r\n    // update existing view\r\n    if (currentDataView) {\r\n      currentDataView.conditionOperator = dataView.conditionOperator;\r\n      currentDataView.columnsToShow = dataView.columnsToShow;\r\n      currentDataView.filterConditions = dataView.filterConditions;\r\n      currentDataView.name = dataView.name;\r\n\r\n      // updating the list of columns\r\n      const cachedUserConfiguredFormats = this.cachedUserConfiguredFormats\r\n        .find(ucf => ucf.datastoreId === this.selectedDatastore.id && ucf.dataViewId === dataView.viewId);\r\n      if (cachedUserConfiguredFormats) {\r\n        // removing deleted columns\r\n        const updatedColumns = cachedUserConfiguredFormats.userConfiguredFormat\r\n          .filter(ucf => dataView.columnsToShow.some(cts => cts === ucf.column));\r\n\r\n        // finding the new columns\r\n        const newColumns = dataView.columnsToShow\r\n          .filter(value => !cachedUserConfiguredFormats.userConfiguredFormat.some(ucf => ucf.column === value));\r\n\r\n        // merging the two arrays\r\n        cachedUserConfiguredFormats.userConfiguredFormat = deepCopy(updatedColumns.concat(\r\n          this.cachedFormatSettings[this.selectedDatastore?.id]?.filter(value => newColumns.some(cts => cts === value.column))));\r\n      }\r\n      this.messageSvc.showSuccess('Success', 'Filter updated successfully.');\r\n      this.getDataViewList();\r\n      this.selectedDataView = currentDataView;\r\n    }\r\n    // add new view\r\n    else {\r\n      this.selectedDatastore?.dataViews.push(dataView);\r\n      this.dataViews.push(dataView);\r\n      this.selectedDataView = dataView;\r\n      this.messageSvc.showSuccess('Success', 'New filter successfully added.');\r\n    }\r\n\r\n    this.setFormatColumnsDataSource();\r\n  }\r\n\r\n  showDynamicAssetSettingsDialog() {\r\n    if (this.projectIsLocked || !this.validateActionableGridColumnConfig()) {\r\n      return;\r\n    }\r\n\r\n    this.showDynamicAssetSettings = true;\r\n  }\r\n\r\n\r\n  newDynamicAssetDialogChanged(visible) {\r\n    if (this.showDynamicAssetSettings !== visible) {\r\n      this.showDynamicAssetSettings = visible;\r\n    }\r\n\r\n    // to populate new form into list\r\n    this.getDatastoreAssets();\r\n  }\r\n\r\n  getDatastoreAssets() {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n\r\n    this.datastoresService.getDatastoreAssets(this.selectedDatastore.id, Number(this.projectVersionId)).subscribe({\r\n      next: (response: DynamicAssetInfo[]) => {\r\n        response.sort((a: DynamicAssetInfo, b: DynamicAssetInfo) => a.name.toLocaleLowerCase().charCodeAt(0) - b.name.toLocaleLowerCase().charCodeAt(0));\r\n        const datastoreForms = response.filter(asset => asset.type === DynamicAssetTypes.DynamicForm);\r\n        const datastoreConnectorApps = response.filter(asset => asset.type === DynamicAssetTypes.ConnectorApp)\r\n\r\n        // grouping the dynamic assets for the dropdown\r\n        this.assetsInfoGroups = [new AssetDropdownGroup('', DynamicAssetTypes.Default, [this.defaultAsset])];\r\n\r\n        if (datastoreForms.length > 0) {\r\n          this.assetsInfoGroups.push(new AssetDropdownGroup(\"Forms\", DynamicAssetTypes.DynamicForm, [...datastoreForms]));\r\n        }\r\n\r\n        if (datastoreConnectorApps.length > 0) {\r\n          this.assetsInfoGroups.push(new AssetDropdownGroup(\"Connector Screens\", DynamicAssetTypes.ConnectorApp, [...datastoreConnectorApps]))\r\n        }\r\n\r\n        // supporting backwards compatibility\r\n        const assetId = this.reportConfig.formatConfig?.dynamicAssetInfo?.id ?? this.reportConfig.formatConfig?.dynamicFormId;\r\n        if (assetId) {\r\n          this.selectedDynamicAsset = response.find(x => x.id === assetId);\r\n          this.populateAssetIdFieldOptions();\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  getProjectVersion() {\r\n    return this.projectsService.getProjectVersion(this.projectVersionId);\r\n  }\r\n\r\n  async buildBreadcrumb() {\r\n    this.projectVersion = await this.getProjectVersion();\r\n    if (this.projectVersion) {\r\n      const items: MenuItem[] = [\r\n        {\r\n          label: `${this.projectVersion.project.name}: ${this.projectVersion.name}`,\r\n          routerLink: `/project/${this.projectId}/${this.projectVersionId}/settings`\r\n        },\r\n        {\r\n          label: 'Project Assets',\r\n          routerLink: `/project/${this.projectId}/${this.projectVersionId}/assets/manage`\r\n        },\r\n        { label: this.reportConfig.reportName }\r\n      ];\r\n\r\n      this.communicationService.updateBreadcrumb(items);\r\n    }\r\n  }\r\n\r\n  setIsFavorite() {\r\n    this.isFavorite = this.allReportFavorites.some(f => f.assetId === this.reportConfig.reportId && !f.isApp);\r\n  }\r\n\r\n  onUpdateUserFavorite(): void {\r\n    const userFavorite = this.userFavoriteService.generateUserFavorite(this.reportConfig.projectId,\r\n      this.reportConfig.projectVersionId,\r\n      window.location.pathname,\r\n      this.reportConfig.reportId,\r\n      'Report',\r\n      !this.isFavorite,\r\n      false);\r\n\r\n    this.communicationService.next({\r\n      fromAppName: this.communicationService.currentAppName,\r\n      toAppName: this.communicationService.navigationAppName,\r\n      eventType: 'upsertUserFavorite',\r\n      data: userFavorite\r\n    });\r\n  }\r\n\r\n  trackUserFavorites() {\r\n    this.userFavoriteSub = this.communicationService.userFavorite\r\n      .subscribe(event => {\r\n        this.allReportFavorites = event.data;\r\n        this.setIsFavorite();\r\n      });\r\n  }\r\n\r\n  getVisualizations() {\r\n    this.tilesConfig = this.reportConfig?.formatConfig?.tilesConfig?.map(tileConfig => ({\r\n      id: tileConfig.tileId,\r\n      tileType: tileConfig.tileType as TileTypes,\r\n      name: tileConfig.name,\r\n    }));\r\n\r\n    this.chartConfigs = this.reportConfig.formatConfig?.chartConfigs;\r\n  }\r\n\r\n  tilesListUpdated(tilesConfig: BaseTile[]) {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n\r\n    this.tilesConfig = tilesConfig;\r\n\r\n    this.reportConfig.formatConfig.tilesConfig = tilesConfig.map(tile => ({\r\n      tileId: tile.id,\r\n      tileType: tile.tileType,\r\n      name: tile.name,\r\n    }));\r\n  }\r\n\r\n  chartListUpdated(chartConfigs: ChartConfig[]) {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n\r\n    this.chartConfigs = chartConfigs;\r\n    this.reportConfig.formatConfig.chartConfigs = chartConfigs;\r\n  }\r\n\r\n  trackReportParameterChange() {\r\n    this.onReportParameterChange = this.reportadminservice.reportParameterChanged.subscribe(params => {\r\n      if (params) {\r\n        this.reportConfig.params = params;\r\n      }\r\n    });\r\n  }\r\n\r\n  getProfileProperties(): void {\r\n    this.userService.getCompleteUserProfilePropertyList().subscribe({});\r\n  }\r\n\r\n  onDynamicAssetChange() {\r\n    if (this.projectIsLocked) {\r\n      return;\r\n    }\r\n\r\n    this.reportConfig.formatConfig.dynamicAssetInfo = this.selectedDynamicAsset;\r\n\r\n    if (this.selectedDynamicAsset.type === DynamicAssetTypes.ConnectorApp) {\r\n      this.populateAssetIdFieldOptions();\r\n    }\r\n  }\r\n\r\n  populateAssetIdFieldOptions() {\r\n    this.idFieldOptions = this.selectedDatastore\r\n      ? Object.keys(this.selectedDatastore.baseSchema.properties)\r\n      : [];\r\n\r\n    if (this.reportConfig.formatConfig?.dynamicAssetInfo?.idField\r\n      && this.idFieldOptions.includes(this.reportConfig.formatConfig?.dynamicAssetInfo?.idField)\r\n      && this.selectedDynamicAsset.id === this.reportConfig.formatConfig?.dynamicAssetInfo?.id) {\r\n      return;\r\n    }\r\n\r\n    if (this.reportConfig.formatConfig?.dynamicAssetInfo) {\r\n      this.reportConfig.formatConfig.dynamicAssetInfo.idField = '';\r\n    }\r\n  }\r\n\r\n  emitParentGroupCommunicationEvent(parentGroup) {\r\n    const communicationEvent = parentGroup !== 'null'\r\n      ? {\r\n        eventType: constants.EventType.UPDATE_PARENT_GROUP_TAG,\r\n        data: { groupId: parentGroup.id, assetId: this.reportConfig.reportId }\r\n      }\r\n      : {\r\n        eventType: constants.EventType.DEACTIVATE_ASSET_GROUP_TAG,\r\n        data: { assetId: this.reportConfig.reportId }\r\n      };\r\n\r\n    this.communicationService.next({\r\n      fromAppName: this.communicationService.currentAppName,\r\n      toAppName: this.communicationService.navigationAppName,\r\n      ...communicationEvent\r\n    });\r\n  }\r\n\r\n  async onChangeCalendarView() {\r\n    if (!this.reportConfig.calendarViewConfig) {\r\n      this.reportConfig.calendarViewConfig = new CalendarViewConfig();\r\n    }\r\n\r\n    this.reportConfig.calendarViewConfig.viewHashId = this.selectedDataView.columnsToShow?.length > 0 ? await this.generateViewHashID() : 'none';\r\n  }\r\n\r\n  async generateViewHashID() {\r\n    if (this.selectedDataView.viewId === '0') { return 'none'; }\r\n\r\n    return await this.cryptoService.hashSHA256(this.selectedDataView.columnsToShow?.sort()?.toString())\r\n  }\r\n}\r\n", "<form #form=\"ngForm\">\r\n  <p-toolbar styleClass=\"flex-parent sticky\">\r\n    <div class=\"p-toolbar-group-start\">\r\n      <h3><span class=\"pi sb-icon-nav-settings\"></span> {{this.reportConfig.reportType !== 'actionablegrid'? 'Report\r\n        Settings' :\r\n        'Grid Settings'}}</h3>\r\n    </div>\r\n    <div class=\"p-toolbar-group-end flex-child\">\r\n      <p-button [rounded]=\"true\" [icon]=\"isFavorite ? 'pi pi-star-fill' : 'pi pi-star'\"\r\n        [pTooltip]=\"isFavorite ? 'Remove Favorite' : 'Add Favorite'\" tooltipPosition=\"top\"\r\n        (onClick)=\"onUpdateUserFavorite()\"></p-button>\r\n      @if (this.reportConfig.reportType === 'actionablegrid') {\r\n      <app-help-launcher [outlined]=\"false\" helpLink=\"{{helpLink}}\"></app-help-launcher>\r\n      }\r\n      <span pTooltip=\"{{projectIsLocked? 'Project is locked' : 'Save'}}\" tooltipPosition=\"top\">\r\n        <p-button [rounded]=\"true\" icon=\"pi pi-save\" name=\"btnSave\" id=\"btnSave\" (click)=\"onClickSave()\"\r\n          [disabled]='isSaveDisabled || projectIsLocked'></p-button>\r\n      </span>\r\n    </div>\r\n  </p-toolbar>\r\n\r\n  <h2>General Settings</h2>\r\n  <div class=\"card py-0\">\r\n    <p-message *ngIf=\"showHelp\" severity=\"info\" styleClass=\"m-2\"\r\n      text=\"Review the settings found on this page and then save to finalize.\"></p-message>\r\n    <div class=\"grid field mb-0\">\r\n      <div class=\"col-12 p-float-label\">\r\n        <input id=\"ReportName\" [(ngModel)]=\"reportConfig.reportName\" required maxlength=\"100\" name=\"name\" pInputText\r\n          [pKeyFilter]=\"dirFilterRegExp\" [disabled]=\"projectIsLocked\" />\r\n        <label for=\"ReportName\">{{this.reportConfig.reportType !== 'actionablegrid'? 'Report Name' : 'Name'}}<span\r\n            class=\"required-field\">*</span></label>\r\n      </div>\r\n      <div class=\"col-12 p-float-label\">\r\n        <input id=\"Description\" [(ngModel)]=\"reportConfig.description\" maxlength=\"100\" name=\"description\" pInputText\r\n          [disabled]=\"projectIsLocked\" />\r\n        <label for=\"DisplayName\">Display Name<span class=\"required-field\">*</span></label>\r\n      </div>\r\n      <div *ngIf=\"groupTagFlag\" class=\"col-12 p-float-label\">\r\n        <p-dropdown [(ngModel)]=\"parentGroup\" name=\"parentGroup\" [disabled]=\"projectIsLocked\"\r\n          [options]=\"groupTagOptions\" id=\"parentGroup\" [ngClass]=\"{'disabled':projectIsLocked}\" [filter]=\"true\"\r\n          filterBy=\"name\" appendTo=\"body\">\r\n          <ng-template pTemplate=\"selectedItem\">\r\n            {{ parentGroup?.dropdownDisplayName ?? 'None' }}\r\n          </ng-template>\r\n        </p-dropdown>\r\n        <label for=\"ParentGroup\">Parent Group</label>\r\n      </div>\r\n      <div *ngIf=\"this.reportConfig.reportType !== 'actionablegrid'\" class=\"col-12 radio-group mb-3\">\r\n        <label class=\"text-xs text-primary-override\">Report Type</label>\r\n        <div *ngFor=\"let category of categories\" class=\"flex\">\r\n          <p-radioButton [inputId]=\"category.key\" class=\"mb-1\" name=\"category.name\" [value]=\"category.key\"\r\n            [(ngModel)]=\"selectedCategory\" [disabled]=\"category.key === 'R' || projectIsLocked\"></p-radioButton>\r\n          <label [for]=\"category.key\" class=\"mx-1\">{{category.name}}</label>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-12\">\r\n        <p-checkbox id=\"Active\" name=\"active\" binary=\"true\" [label]=\"nameLabelMap?.active\"\r\n          [(ngModel)]=\"reportConfig.isActive\" [disabled]=\"projectIsLocked\">\r\n        </p-checkbox>\r\n      </div>\r\n\r\n      <div class=\"col-12 mt-3 p-float-label fake-field-readonly\">\r\n        <span id=\"reportIDFakeField\" class=\"grid align-items-center mx-0\">{{reportConfig.reportId}}</span>\r\n        <label for=\"reportIDFakeField\">Report ID</label>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <p-accordion [multiple]=\"true\">\r\n    <p-accordionTab [selected]=\"true\" header=\"Connection Settings\">\r\n      <div class=\"grid\">\r\n        <div *ngIf=\"this.reportConfig.reportType !== 'actionablegrid'\" class=\"col-12 radio-group\">\r\n          <label class=\"text-xs text-primary-override\">Connection Type</label>\r\n          <div *ngFor=\"let connectorType of connectorTypes\" class=\"mb-2 flex\">\r\n            <p-radioButton [inputId]=\"connectorType.key\" name=\"connectorType\" [value]=\"connectorType\" class=\"mb-1 mr-1\"\r\n              [(ngModel)]=\"selectedConnectorType\" [disabled]=\"connectorType.key === 'R' || projectIsLocked\"\r\n              (onClick)=\"onConnectorTypeChange()\"></p-radioButton>\r\n            <label [for]=\"connectorType.key\">{{connectorType.name}}</label>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-12 grid field\">\r\n          <ng-container *ngIf=\"selectedConnectorType?.key==='connector'\">\r\n            <div class=\"col-12 p-float-label\">\r\n              <p-dropdown inputId=\"connectors\" name=\"connectors\" [options]=\"connectors\" optionValue=\"value\"\r\n                (onChange)=\"onConnectorChange()\" [(ngModel)]=\"selectedConnector\" [disabled]=\"projectIsLocked\"\r\n                [ngClass]=\"{'disabled':projectIsLocked}\">\r\n              </p-dropdown>\r\n              <label for=\"connectors\">Connection</label>\r\n            </div>\r\n            <div class=\"col-12 p-float-label\">\r\n              <p-dropdown inputId=\"functions\" name=\"functions\" [options]=\"functions\" optionValue=\"value\"\r\n                [(ngModel)]=\"selectedFunction\" [disabled]=\"projectIsLocked\" [ngClass]=\"{'disabled':projectIsLocked}\">\r\n              </p-dropdown>\r\n              <label for=\"functions\">Function</label>\r\n            </div>\r\n          </ng-container>\r\n\r\n          <ng-container *ngIf=\"selectedConnectorType?.key==='workflow'\">\r\n            <div class=\"col-12 p-float-label\">\r\n              <p-dropdown inputId=\"workflows\" name=\"workflows\" [options]=\"workflows\" optionLabel=\"name\" optionValue=\"id\"\r\n                [(ngModel)]=\"selectedWorkflow\" [disabled]=\"projectIsLocked\" [ngClass]=\"{'disabled':projectIsLocked}\">\r\n              </p-dropdown>\r\n            </div>\r\n          </ng-container>\r\n\r\n          <ng-container *ngIf=\"selectedConnectorType?.key==='forms'\">\r\n            <div class=\"col-12 p-float-label\">\r\n              <p-dropdown inputId=\"forms\" name=\"forms\" [options]=\"datastores\" optionLabel=\"name\"\r\n                emptyMessage=\"No datastores found\" [(ngModel)]=\"selectedDatastore\" [disabled]=\"projectIsLocked\"\r\n                (onChange)=\"onSelectedDatastoreChange()\" class=\"connection-settings-drop-down\"\r\n                [ngClass]=\"{'disabled':projectIsLocked}\">\r\n              </p-dropdown>\r\n              <label for=\"forms\">Datastore</label>\r\n            </div>\r\n            <div *ngIf=\"selectedDatastore\" class=\"col-12 p-float-label p-inputgroup analytical-data-filter\">\r\n              <p-dropdown inputId=\"formsFilter\" name=\"formsFilter\" [options]=\"this.dataViews\" optionLabel=\"name\"\r\n                (onChange)=\"onSelectedViewChange()\" [(ngModel)]=\"this.selectedDataView\" [disabled]=\"projectIsLocked\"\r\n                [filter]=\"true\" filterBy=\"name\" class=\"connection-settings-drop-down\"\r\n                [ngClass]=\"{'disabled':projectIsLocked}\">\r\n                <ng-template let-dataView pTemplate=\"item\">\r\n                  <div class=\"analytical-data-filter-dropdown\">\r\n                    <div>{{dataView.name}}</div>\r\n                    <div *ngIf=\"dataView.viewId !== '0'\">\r\n                      <p-button pRipple name=\"editView\" icon=\"pi pi-pencil\" pTooltip=\"Edit View\" tooltipPosition=\"top\"\r\n                        [text]=\"true\" (onClick)=\"openAnalyticsFilterModal(formMode.EDIT, dataView)\"></p-button>\r\n                      <p-button pRipple name=\"deleteView\" icon=\"pi pi-trash\" pTooltip=\"Delete View\"\r\n                        tooltipPosition=\"top\" [text]=\"true\" severity=\"danger\"\r\n                        (onClick)=\"deleteView(dataView)\"></p-button>\r\n                    </div>\r\n                  </div>\r\n                </ng-template>\r\n              </p-dropdown>\r\n              <label for=\"formsFilter\">View</label>\r\n              <span class=\"p-inputgroup-addon\">\r\n                <p-button pRipple name=\"addNew\" icon=\"pi pi-plus\" pTooltip=\"Add New View\" tooltipPosition=\"top\"\r\n                  [outlined]=\"true\" (onClick)=\"openAnalyticsFilterModal(formMode.CREATE)\"\r\n                  [disabled]=\"projectIsLocked\"></p-button>\r\n              </span>\r\n            </div>\r\n          </ng-container>\r\n        </div>\r\n      </div>\r\n    </p-accordionTab>\r\n\r\n    <p-accordionTab *ngIf=\"this.reportConfig.reportType === 'actionablegrid'\" [selected]=\"false\" header=\"Grid Settings\">\r\n      <div class=\"grid field\">\r\n        <div class=\"col-12 p-field-checkbox my-2\">\r\n          <p-checkbox binary=\"true\" [(ngModel)]=\"reportConfig.allowAddNewRow\" name=\"allowAddNewRow\"\r\n                      label=\"Allow User to Add New Rows\" [disabled]=\"projectIsLocked\">\r\n          </p-checkbox>\r\n        </div>\r\n        <div class=\"col-12 p-float-label p-inputgroup\">\r\n          <p-dropdown id=\"eventWorklow\" inputId=\"ouwSettings\" name=\"ouwSettings\" [options]=\"workflows\"\r\n                      optionLabel=\"name\" [(ngModel)]=\"selectedSavedEventWorkflow\" [showClear]=\"true\" [disabled]=\"projectIsLocked\"\r\n                      [ngClass]=\"{'disabled':projectIsLocked}\">\r\n          </p-dropdown>\r\n          <label for=\"eventWorklow\">Save Event Workflow</label>\r\n        </div>\r\n        <div *ngIf=\"this.selectedDataView\" class=\"col-12 p-float-label p-inputgroup\">\r\n          <p-dropdown inputId=\"inlineForm\" name=\"inlineForm\" [options]=\"assetsInfoGroups\" optionLabel=\"name\"\r\n                      [(ngModel)]=\"selectedDynamicAsset\" filter=\"true\" filterBy=\"name\" group=\"true\"\r\n                      (onChange)=\"onDynamicAssetChange()\" [disabled]=\"projectIsLocked\" [ngClass]=\"{'disabled':projectIsLocked}\">\r\n            <ng-template pTemplate=\"selectedItem\">\r\n              <div>\r\n                <span *ngIf=\"selectedDynamicAsset?.type === 'dynamicForm' || selectedDynamicAsset?.type === 'connectorApp'\">\r\n                  <i class=\"sb\"\r\n                     [ngClass]=\"{'sb-icon-form': selectedDynamicAsset.type === 'dynamicForm', 'sb-icon-screen': selectedDynamicAsset.type === 'connectorApp'}\"></i>\r\n                </span>\r\n                {{ selectedDynamicAsset?.name }}\r\n              </div>\r\n            </ng-template>\r\n            <ng-template let-group pTemplate=\"group\">\r\n              <div *ngIf=\"group.label\" class=\"font-bold\">\r\n                {{group.label}}\r\n              </div>\r\n            </ng-template>\r\n            <ng-template let-asset pTemplate=\"item\">\r\n              <div class=\"ml-4\">\r\n                <span *ngIf=\"asset.type === 'dynamicForm' || asset.type === 'connectorApp'\">\r\n                  <i class=\"sb\"\r\n                     [ngClass]=\"{'sb-icon-form': asset.type === 'dynamicForm', 'sb-icon-screen': asset.type === 'connectorApp'}\"></i>\r\n                </span>\r\n                {{asset.name}}\r\n              </div>\r\n            </ng-template>\r\n          </p-dropdown>\r\n          <label for=\"inlineForm\">Edit Mode</label>\r\n          <span class=\"p-inputgroup-addon\">\r\n            <p-button [outlined]=\"true\" pRipple name=\"refreshList\" icon=\"pi p-icon-refresh\" [disabled]=\"projectIsLocked\"\r\n                      (onClick)=\"getDatastoreAssets()\" pTooltip=\"Refresh List\" tooltipPosition=\"top\"></p-button>\r\n          </span>\r\n          <span class=\"p-inputgroup-addon\">\r\n            <p-button [outlined]=\"true\" pRipple name=\"generateAsset\" icon=\"pi pi-plus\" [disabled]=\"projectIsLocked\"\r\n                      (onClick)=\"showDynamicAssetSettingsDialog()\" pTooltip=\"Add New Asset\" tooltipPosition=\"top\"></p-button>\r\n          </span>\r\n        </div>\r\n        <div *ngIf=\"selectedDynamicAsset?.type === 'connectorApp'\" class=\"col-12 p-float-label p-inputgroup\">\r\n          <p-dropdown inputId=\"idField\" name=\"idField\" [options]=\"idFieldOptions\" [disabled]=\"projectIsLocked\"\r\n                      [(ngModel)]=\"this.reportConfig.formatConfig.dynamicAssetInfo.idField\" [autoDisplayFirst]=\"false\"\r\n                      [ngClass]=\"{'disabled':projectIsLocked}\">\r\n          </p-dropdown>\r\n          <label for=\"idField\">ID Field</label>\r\n        </div>\r\n        <app-conditional-forms class=\"w-full\" [(reportConfig)]=\"reportConfig\" [datastore]=\"selectedDatastore\"\r\n                               [projectId]=\"projectId\" [projectVersionId]=\"projectVersionId\" [disabled]=\"projectIsLocked\"\r\n                               [actionableGridConfig]=\"selectedUserConfiguredFormat\"></app-conditional-forms>\r\n\r\n        <app-conditional-formatting class=\"w-full\" [(reportConfig)]=\"reportConfig\" [datastore]=\"selectedDatastore\"\r\n                               [projectId]=\"projectId\" [projectVersionId]=\"projectVersionId\" [disabled]=\"projectIsLocked\"\r\n                               [actionableGridConfig]=\"selectedUserConfiguredFormat\"></app-conditional-formatting>\r\n                               \r\n        <div class=\"col-12 p-field-checkbox my-2\">\r\n          <p-checkbox binary=\"true\" [(ngModel)]=\"reportConfig.enablePagination\" name=\"enablePagination\"\r\n                      label=\"Enable Pagination\" [disabled]=\"projectIsLocked\">\r\n          </p-checkbox>\r\n        </div>\r\n        <div *ngIf=\"reportConfig?.enablePagination\" class=\"col-12 p-field-checkbox my-2\">\r\n          <p-checkbox binary=\"true\" [(ngModel)]=\"reportConfig.paginationAutoPaging\" name=\"paginationAutoPaging\"\r\n                      label=\"Pagination Auto Paging\" [disabled]=\"projectIsLocked\">\r\n          </p-checkbox>\r\n        </div>\r\n        <div *ngIf=\"reportConfig?.enablePagination\" class=\"col-12 p-float-label p-inputgroup\">\r\n          <p-dropdown inputId=\"defaultPageSize\" name=\"defaultPageSize\" [options]=\"pageSizes\" [disabled]=\"projectIsLocked\"\r\n                      [(ngModel)]=\"this.reportConfig.defaultPageSize\" [autoDisplayFirst]=\"false\" optionLabel=\"key\" optionValue=\"value\"\r\n                      [ngClass]=\"{'disabled':projectIsLocked}\">\r\n          </p-dropdown>\r\n          <label for=\"pageSize\">Default Page Size</label>\r\n        </div>\r\n      </div>\r\n    </p-accordionTab>\r\n\r\n    <p-accordionTab [selected]=\"false\" header=\"Data Feed Settings\" *ngIf=\"selectedConnectorType?.key!=='forms'\">\r\n      <div class=\"grid field\">\r\n        <div class=\"col-12 p-float-label\">\r\n          <p-dropdown *ngIf=\"categoryFunctionExists\" inputId=\"queryCategory\" name=\"queryCategory\"\r\n            [options]=\"queryCategory\" optionLabel=\"label\" optionValue=\"value\" [disabled]=\"projectIsLocked\"\r\n            [(ngModel)]=\"selectedQueryCategory\" (onChange)=\"onCategoryChange()\"\r\n            [ngClass]=\"{'disabled':projectIsLocked}\">\r\n          </p-dropdown>\r\n          <label for=\"queryCategory\">Query Category</label>\r\n        </div>\r\n        <div class=\"col-12 p-float-label\" *ngIf=\"!categoryFunctionExists\">\r\n          <input id=\"queryCategory\" [(ngModel)]=\"selectedQueryCategory\" maxlength=\"100\" name=\"queryCategory\" pInputText\r\n            [disabled]=\"projectIsLocked\" />\r\n          <label for=\"queryCategory\">Query Category</label>\r\n        </div>\r\n        <div *ngIf=\"queryFunctionExists\" class=\"col-12 p-float-label\">\r\n          <p-dropdown inputId=\"queryName\" name=\"queryName\" [disabled]=\"projectIsLocked\" [options]=\"queryName\"\r\n            optionLabel=\"label\" optionValue=\"value\" [(ngModel)]=\"selectedQueryName\" (onChange)=\"getColumnNames()\"\r\n            [ngClass]=\"{'disabled':projectIsLocked}\">\r\n          </p-dropdown>\r\n          <label for=\"queryName\">Query Name</label>\r\n        </div>\r\n        <div class=\"col-12 p-float-label\" *ngIf=\"!queryFunctionExists\">\r\n          <input id=\"queryName\" [(ngModel)]=\"selectedQueryName\" maxlength=\"100\" name=\"queryName\" pInputText\r\n            [disabled]=\"projectIsLocked\" />\r\n          <label for=\"queryName\">Query Name</label>\r\n        </div>\r\n      </div>\r\n    </p-accordionTab>\r\n\r\n    <p-accordionTab\r\n      *ngIf=\"this.reportConfig.reportType === reportType.ActionableGrid && this.selectedUserConfiguredFormat\"\r\n      [selected]=\"showHelp\" header=\"Format Columns/Data\" contentStyleClass=\"grid field mx-0 max-w-unset block\">\r\n      <div class=\"section-description\">\r\n        <p>\r\n          App roles with \"View\" level of access will provide read-only access to all visible fields in this grid. Field\r\n          details for \"Edit\" and \"Edit & Delete\" access levels are managed below.\r\n        </p>\r\n\r\n        <p>\r\n          App permissions are managed within the project's <a [href]=\"appSettingsUrl\">App Settings <i\r\n              class=\"sb sb-icon-link\"></i></a> page, in the Roles section.\r\n        </p>\r\n      </div>\r\n      <app-actionable-grid-config #actionableGridConfigComponent [columnsConfig]=\"this.selectedUserConfiguredFormat\"\r\n        [projectVersionId]=\"projectVersionId\" [disabled]=\"projectIsLocked\"></app-actionable-grid-config>\r\n    </p-accordionTab>\r\n\r\n    @if (this.reportConfig?.reportType === reportType.ActionableGrid && calendarViewFlag)\r\n    {\r\n    <p-accordionTab [selected]=\"false\" header=\"Calendar View\">\r\n      <div class=\"grid\">\r\n        <div class=\"col-12\">\r\n          <p-checkbox id=\"enableCalendarView\" name=\"enableCalendarView\" binary=\"true\"\r\n            [label]=\"nameLabelMap?.enableCalendarView\" [(ngModel)]=\"reportConfig.enableCalendarView\"\r\n            [disabled]=\"projectIsLocked || !hasDateColumn\" (onChange)=\"onChangeCalendarView()\" class=\"mb-2\"\r\n            [pTooltip]=\"hasDateColumn ? null : 'No Date Field(s) in selected data'\" tooltipPosition=\"top\">\r\n          </p-checkbox>\r\n\r\n          @if (this.reportConfig?.enableCalendarView && this.selectedDatastore && this.selectedDataView) {\r\n          <app-calendar-view-settings [calendarViewConfig]=\"reportConfig?.calendarViewConfig\"\r\n            [selectedDataView]=\"selectedDataView\" [baseSchema]=\"selectedDatastore?.baseSchema\">\r\n          </app-calendar-view-settings>\r\n          }\r\n        </div>\r\n      </div>\r\n    </p-accordionTab>\r\n    }\r\n\r\n    <p-accordionTab\r\n      *ngIf=\"this.reportConfig.reportType === reportType.ActionableGrid && visualizationFlag && reportConfig.formatConfig\"\r\n      [selected]=\"false\" header=\"Visualization Details\">\r\n      <app-linked-tiles [tilesConfig]=\"tilesConfig\" [projectId]=\"projectId\" [projectVersionId]=\"projectVersionId\"\r\n        [datastoreId]=\"selectedDatastore?.id\" [selectedDataViewId]=\"selectedDataView?.viewId\"\r\n        [advancedJsonEditFlag]=\"advancedJsonEditFlag\" (tilesListUpdated)=\"tilesListUpdated($event)\"\r\n        [disabled]=\"projectIsLocked\"></app-linked-tiles>\r\n    </p-accordionTab>\r\n\r\n    <!-- this is the cross filter charts for actionable grid-->\r\n    <p-accordionTab\r\n      *ngIf=\"this.reportConfig.reportType === reportType.ActionableGrid && gridChartsFlag && reportConfig.formatConfig\"\r\n      [selected]=\"false\" header=\"Grid Chart Details\">\r\n      <app-linked-charts [chartConfigs]=\"chartConfigs\" (linkedChartsUpdated)=\"chartListUpdated($event)\"\r\n        [disabled]=\"projectIsLocked\">\r\n      </app-linked-charts>\r\n    </p-accordionTab>\r\n\r\n    <!-- this is the standlone chart type setup -->\r\n    <p-accordionTab [selected]=\"false\" header=\"Chart Details\" *ngIf=\"selectedCategory === 'chart'\">\r\n      <div class=\"grid\">\r\n        <div class=\"col-12 p-float-label\">\r\n          <p-dropdown inputId=\"chartTypes\" [options]=\"chartTypes\" optionLabel=\"name\" optionValue=\"key\"\r\n            [(ngModel)]=\"chartType\" (onChange)=\"onChartChange()\" name=\"chartType\" [disabled]=\"projectIsLocked\"\r\n            [ngClass]=\"{'disabled':projectIsLocked}\">\r\n          </p-dropdown>\r\n          <label for=\"firstname\">Chart Type</label>\r\n        </div>\r\n        <div class=\"col-12 p-float-label\" *ngIf=\"chartSubTypes\">\r\n          <p-dropdown inputId=\"chartSubTypes\" [options]=\"chartSubTypes\" optionLabel=\"name\" optionValue=\"key\"\r\n            [(ngModel)]=\"chartSubType\" name=\"chartSubType\" [disabled]=\"projectIsLocked\"\r\n            [ngClass]=\"{'disabled':projectIsLocked}\">\r\n          </p-dropdown>\r\n          <label for=\"firstname\">Sub Type</label>\r\n        </div>\r\n        <div class=\"col-12 p-float-label\">\r\n          <textarea id=\"float-input\" rows=\"5\" cols=\"70\" pInputTextarea [(ngModel)]=\"chartConfigDetails\"\r\n            name=\"chartConfigDetails\" [ngClass]=\"{ 'p-filled': chartConfigDetails?.length > 0}\"\r\n            [disabled]=\"projectIsLocked\"></textarea>\r\n          <label for=\"firstname\">Configuration Details</label>\r\n          <p-message severity=\"warn\"\r\n            text=\"It is not recommended to exceed 20 rows per chart as exceeding this may cause display issues.\"\r\n            styleClass=\"max-w-30rem\"></p-message>\r\n        </div>\r\n      </div>\r\n    </p-accordionTab>\r\n\r\n    <p-accordionTab [selected]=\"showHelp\" header=\"Format Columns/Data\" *ngIf=\"formatColumns.length > 0\">\r\n      <div class=\"grid field max-w-full m-0\">\r\n        <div class=\"col-12 p-0\">\r\n          <p-table class=\"report-setup\" #dt [value]=\"formatColumns\">\r\n            <ng-template pTemplate=\"header\">\r\n              <tr>\r\n                <th>Column</th>\r\n                <th>Format Settings</th>\r\n              </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"body\" let-rowData let-i=\"rowIndex\">\r\n              <tr [pSelectableRow]=\"rowData\">\r\n                <td class=\"py-0\">\r\n                  <input pInputText type=\"text\" name=\"name{{i}}{{rowData.name}}\" class=\"no-label w-10rem\"\r\n                    [ngModel]=\"rowData.displayName || rowData.name\" [disabled]=\"true\">\r\n                </td>\r\n                <td class=\"p-0 flex m-0\">\r\n                  <span class=\"p-float-label col-12 w-10rem\">\r\n                    <p-dropdown inputId=\"format\" name=\"format{{rowData.name}}\" [autoDisplayFirst]=\"false\"\r\n                      [options]=\"formats\" optionLabel=\"name\" optionValue=\"key\" [(ngModel)]=\"rowData.format\"\r\n                      (onChange)=\"setDecimalPlaces(rowData)\"\r\n                      [ngClass]=\"{'disabled': projectIsLocked || selectedConnectorType?.key === 'forms'}\"\r\n                      appendTo=\"body\" [disabled]=\"projectIsLocked || selectedConnectorType?.key === 'forms'\">\r\n                    </p-dropdown>\r\n                    <label>Format</label>\r\n                  </span>\r\n                  <span\r\n                    *ngIf=\"(rowData.format === 'numeric' || rowData.format === 'currency') && rowData?.baseType !== 'integer'\"\r\n                    class=\"p-float-label col-12 w-7rem\">\r\n                    <p-inputNumber mode=\"decimal\" name=\"decimalPlaces{{i}}\" [showButtons]=\"true\"\r\n                      [disabled]=\"selectedConnectorType?.key === 'forms' || projectIsLocked\"\r\n                      [(ngModel)]=\"rowData.decimalPlaces\" inputId=\"minmax-buttons\" [min]=\"0\" [max]=\"100\"\r\n                      inputStyleClass=\"min-w-0\" [ngClass]=\"{'disabled': selectedConnectorType?.key === 'forms'}\">\r\n                    </p-inputNumber>\r\n                    <label>Dec. Points</label>\r\n                  </span>\r\n                  <span class=\"p-float-label col-12 w-8rem\" *ngIf=\"rowData.format === 'currency'\">\r\n                    <p-dropdown inputId=\"format\" name=\"currency{{i}}\" [options]=\"currencies\" optionLabel=\"code\"\r\n                      optionValue=\"code\" [(ngModel)]=\"rowData.currency\" appendTo=\"body\"\r\n                      [disabled]=\"projectIsLocked || selectedConnectorType?.key === 'forms'\"\r\n                      [ngClass]=\"{'disabled': projectIsLocked || selectedConnectorType?.key === 'forms'}\">\r\n                    </p-dropdown>\r\n                    <label>Currency</label>\r\n                  </span>\r\n                </td>\r\n              </tr>\r\n            </ng-template>\r\n          </p-table>\r\n        </div>\r\n      </div>\r\n    </p-accordionTab>\r\n\r\n    <p-accordionTab [selected]=\"false\" header=\"Drill Through\"\r\n      *ngIf=\"urlColumns.length >0 && this.selectedCategory === 'grid'\">\r\n      <div class=\"grid field max-w-full m-0\">\r\n        <div class=\"col-12 p-0\">\r\n          <p-table #dt [value]=\"drillThroughs\" class=\"drill__table flex\"\r\n            styleClass=\"p-datatable-header-no-background p-datatable-no-margin\">\r\n            <ng-template pTemplate=\"caption\">\r\n              <p-toolbar styleClass=\"p-toolbar-secondary\">\r\n                <div class=\"p-toolbar-group-start\"></div>\r\n                <div class=\"p-toolbar-group-end\">\r\n                  <p-button [disabled]=\"this.selectedDrillThroughs.length <= 0\" pRipple\r\n                    name=\"clearSelectedDrillThroughs\" icon=\"pi pi-trash\" [outlined]=\"true\" [rounded]=\"true\"\r\n                    severity=\"danger\" (click)=\"clearSelectedDrillThroughs()\" [disabled]=\"projectIsLocked\"></p-button>\r\n                  <p-button pRipple name=\"addNew\" icon=\"pi pi-plus\" [outlined]=\"true\" [rounded]=\"true\"\r\n                    (click)=\"addNew()\" [disabled]=\"projectIsLocked\"></p-button>\r\n                </div>\r\n              </p-toolbar>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"header\">\r\n              <tr class=\"drill-head\">\r\n                <th pSortableColumn=\"displayColumn\" class=\"w-3rem p-0\">Select</th>\r\n                <th pSortableColumn=\"displayColumn\" class=\"w-9rem\">Display Column</th>\r\n                <th pSortableColumn=\"urlAlias\">URL Alias String</th>\r\n                <th pSortableColumn=\"urlSubstitution\" class=\"w-14rem\">URL Substitutions</th>\r\n                <th pSortableColumn=\"openIn\" class=\"w-11rem\">Open In</th>\r\n                <th pSortableColumn=\"gridGeneratedLink\">Grid Generated Link</th>\r\n                <th></th>\r\n              </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"body\" let-drillThrough let-i=\"rowIndex\">\r\n              <tr class=\"drill-table-custom-tr\">\r\n                <td>\r\n                  <div class=\"col-12\">\r\n                    <p-checkbox (onChange)=\"drillThroughCheckedChange($event.checked,drillThrough)\" [binary]=\"true\"\r\n                      class=\"p-element ng-untouched ng-pristine ng-valid\" name=\"drillThrough-{{i}}\"\r\n                      [disabled]=\"projectIsLocked\">\r\n                    </p-checkbox>\r\n                  </div>\r\n                </td>\r\n                <td class=\"p-0\">\r\n                  <span class=\"col-12 p-float-label\">\r\n                    <p-dropdown name=\"displayColumn{{i}}\" [options]=\"urlColumns\"\r\n                      [(ngModel)]=\"drillThrough.displayColumn\" [style]=\"{'width':'100%'}\" optionValue=\"value\"\r\n                      optionLabel=\"label\" appendTo=\"body\" [disabled]=\"projectIsLocked\"\r\n                      [ngClass]=\"{'disabled': projectIsLocked}\"></p-dropdown>\r\n                    <label for=\"displayColumn{{i}}\">Display Columns</label>\r\n                  </span>\r\n                </td>\r\n                <td class=\"p-0\">\r\n                  <span class=\"col-12 p-float-label\">\r\n                    <input pInputText type=\"text\" [style]=\"{'width':'100%'}\" name=\"urlAlias{{i}}\"\r\n                      [disabled]=\"projectIsLocked\" [(ngModel)]=\"drillThrough.urlAlias\"\r\n                      (focusout)=\"onSubstitutionChange(drillThrough)\">\r\n                    <label for=\"urlAlias{{i}}\"></label>\r\n                    <p-message severity=\"info\" text=\"eg: http://xxxx.com?param={0}&param={1}\"></p-message>\r\n                  </span>\r\n                </td>\r\n                <td class=\"drill-url-substitution p-0\">\r\n                  <p-table [value]=\"drillThrough.urlSubstitution\">\r\n                    <ng-template pTemplate=\"body\" let-rowdata let-j=\"rowIndex\">\r\n              <tr class=\"drill-url-substitution-tr\">\r\n                <td class=\"drill-url p-0\">\r\n                  <div class=\"col-12 p-inputgroup p-float-label\">\r\n                    <input pInputText type=\"text\" [style]=\"{'width':'100px'}\" name=\"urlSubstitution{{i}}{{j}}\"\r\n                      [(ngModel)]=\"rowdata.urlName\" [ngModelOptions]=\"{updateOn: 'blur'}\"\r\n                      (focusout)=\"onSubstitutionChange(drillThrough)\" [disabled]=\"projectIsLocked\">\r\n                    <label for=\"urlSubstitution{{i}}{{j}}\">Parameter</label>\r\n                    <span class=\"p-inputgroup-addon\">\r\n                      <p-button pRipple name=\"deleteSubstitution{{i}}{{j}}\" icon=\"pi pi-pencil\" [outlined]=\"true\"\r\n                        (onClick)=\"selectUrlSubstitution(drillThrough.urlSubstitution,i,j)\"\r\n                        [disabled]=\"projectIsLocked\"></p-button>\r\n                    </span>\r\n                    <span class=\"p-inputgroup-addon\">\r\n                      <p-button pRipple name=\"deleteSubstitution{{i}}{{j}}\" icon=\"pi pi-trash\" [outlined]=\"true\"\r\n                        (onClick)=\"deleteSubstitution(drillThrough.urlSubstitution,j)\"\r\n                        [disabled]=\"projectIsLocked\"></p-button>\r\n                    </span>\r\n                    <span class=\"p-inputgroup-addon\">\r\n                      <p-button pRipple name=\"addSubstitution{{i}}{{j}}\" icon=\"pi pi-plus\" [outlined]=\"true\"\r\n                        (onClick)=\"addSubstitution(drillThrough.urlSubstitution)\"\r\n                        [disabled]=\"projectIsLocked\"></p-button>\r\n                    </span>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n            </ng-template>\r\n          </p-table>\r\n          </td>\r\n          <td class=\"p-0\">\r\n            <span class=\"col-12 p-float-label\">\r\n              <p-dropdown name=\"openIn{{i}}\" appendTo=\"body\" [options]=\"openInValues\" [(ngModel)]=\"drillThrough.openIn\"\r\n                [style]=\"{'width':'100%'}\" optionValue=\"code\" optionLabel=\"name\" [disabled]=\"projectIsLocked\"\r\n                [ngClass]=\"{'disabled': projectIsLocked}\"></p-dropdown>\r\n              <label for=\"openIn{{i}}\">Value</label>\r\n            </span>\r\n          </td>\r\n          <td class=\"drill-generated-link px-2 py-1\">{{drillThrough.gridGeneratedLink}}</td>\r\n          <td class=\"px-2 py-1\">\r\n            <p-button pRipple icon=\"pi pi-trash\" [text]=\"true\" severity=\"danger\" (click)=\"deleteProduct(drillThrough)\"\r\n              [disabled]=\"projectIsLocked\"></p-button>\r\n          </td>\r\n          </tr>\r\n          </ng-template>\r\n          </p-table>\r\n        </div>\r\n      </div>\r\n    </p-accordionTab>\r\n    @if (this.reportConfig.reportType === 'actionablegrid' && gridAgentChatFlag ) {\r\n      <p-accordionTab [selected]=\"false\" header=\"Chat Agent\">\r\n        <div class=\"grid field\">\r\n          <div class=\"col-12\">\r\n            <!--<p-checkbox binary=\"true\" [(ngModel)]=\"allowAgentChat\" name=\"allowAgentChat\" label=\"Enable chat agent in this grid\"></p-checkbox>-->\r\n          </div>\r\n          <div class=\"col-12 p-float-label\">\r\n            <textarea id=\"agentAssetInstructions\" name=\"agentAssetInstructions\" pInputTextarea\r\n              [autoResize]=\"true\" class=\"min-h-5rem\"\r\n               [(ngModel)]=\"agentAssetConfig.instructions\" [disabled]=\"projectIsLocked\"></textarea>\r\n            <label for=\"agentAssetInstructions\">Agent Instructions</label>\r\n          </div>\r\n          <div class=\"col-12 p-float-label\">\r\n            <textarea id=\"agentAssetSchema\" name=\"agentAssetSchema\" pInputTextarea\r\n              [autoResize]=\"true\" class=\"min-h-5rem\"\r\n               [(ngModel)]=\"agentAssetConfig.schema\" [disabled]=\"projectIsLocked\"></textarea>\r\n            <label for=\"agentAssetSchema\">Sample JSON</label>\r\n          </div>\r\n          <div class=\"col-12 p-float-label\">\r\n            <input id=\"agentAssetWorkflowId\" [(ngModel)]=\"agentAssetConfig.workflowId\" maxlength=\"50\" name=\"agentAssetWorkflowId\"\r\n            pInputText [disabled]=\"projectIsLocked\" />\r\n          <label for=\"agentAssetWorkflowId\">Workflow ID to Run</label>\r\n          </div>\r\n          <div class=\"col-12 p-float-label\">\r\n            <input id=\"agentAssetStartMessage\" [(ngModel)]=\"agentAssetConfig.startMessage\" maxlength=\"50\" name=\"agentAssetStartMessage\"\r\n            pInputText [disabled]=\"projectIsLocked\" />\r\n          <label for=\"agentAssetStartMessage\">Chat Window Starting Message</label>\r\n          </div>\r\n        </div>\r\n      </p-accordionTab>\r\n    }\r\n    <p-accordionTab [selected]=\"false\" header=\"Sharing\">\r\n      <div class=\"grid embedInputs field\">\r\n        <div class=\"col-12 p-float-label p-inputgroup\">\r\n          <input [disabled]=\"true\" [(ngModel)]=\"reportConfig.url\" pInputText type=\"text\" id=\"LinkToEmbed\"\r\n            name=\"LinkToEmbed\" />\r\n          <label for=\"LinkToEmbed\">{{ nameLabelMap.LinkToEmbed }}</label>\r\n          <span class=\"p-inputgroup-addon\">\r\n            <i class=\"pi pi-copy\" (click)=\"copyToClipboard(reportConfig.url)\"></i>\r\n          </span>\r\n        </div>\r\n        <div class=\"col-12 p-float-label p-inputgroup\">\r\n          <input [disabled]=\"true\" id=\"HtmlToPaste\" type=\"text\" pInputText [(ngModel)]=\"reportConfig.htmlCode\"\r\n            name=\"HtmlToPaste\">\r\n          <label for=\"HtmlToPaste\">{{ nameLabelMap.HtmlToPaste }}</label>\r\n          <span class=\"p-inputgroup-addon\">\r\n            <i class=\"pi pi-copy\" (click)=\"copyToClipboard(reportConfig.htmlCode)\"></i>\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </p-accordionTab>\r\n\r\n    <p-accordionTab [selected]=\"false\" header=\"Security\">\r\n      <div class=\"grid field\">\r\n        <p-button *ngIf=\"hasManagerPermissions\" type=\"button\" (onClick)=\"openAssetModal()\" icon=\"p-icon-group\"\r\n          label=\"Manage Report Access\" [disabled]=\"projectIsLocked\"></p-button>\r\n        <div class=\"col-12 mt-3\">\r\n          <p-checkbox binary=\"true\" [(ngModel)]=\"allowAnonymous\" name=\"allowAnonymous\" label=\"Allow anonymous access\"\r\n            [disabled]=\"projectIsLocked\">\r\n          </p-checkbox>\r\n        </div>\r\n        <div class=\"col-12 p-float-label indented mt-1\"\r\n          [ngClass]=\"{ 'border-400' : !allowAnonymous, 'disabled' : !allowAnonymous }\">\r\n          <p-dropdown inputId=\"users\" name=\"users\" [disabled]=\"!allowAnonymous || projectIsLocked\"\r\n            [ngClass]=\"{ 'disabled' : !allowAnonymous || projectIsLocked }\" [options]=\"users\" optionLabel=\"label\"\r\n            optionValue=\"value\" [(ngModel)]=\"selectedUser\" [filter]=\"true\">\r\n          </p-dropdown>\r\n          <label for=\"users\">Proxy User</label>\r\n          <p-message severity=\"info\"\r\n            text=\"When a report is run anonymously the selected user profile and its corresponding security settings will be used to run the report.\"></p-message>\r\n        </div>\r\n      </div>\r\n    </p-accordionTab>\r\n\r\n    <p-accordionTab [selected]=\"false\" header=\"Cache Settings\">\r\n      <div class=\"grid field\">\r\n        <div class=\"col-12 p-float-label\">\r\n          <p-confirmDialog [style]=\"{width: '25vw'}\" appendTo=\"body\">\r\n          </p-confirmDialog>\r\n          <input id=\"connectionTimeOut\" [(ngModel)]=\"selectedConnectionTimeOut\" maxlength=\"100\" name=\"connectionTimeOut\"\r\n            pInputText (keypress)=\"numberOnly($event)\" (blur)=\"checkTimeOutValid()\" [disabled]=\"projectIsLocked\" />\r\n          <label for=\"connectionTimeOut\">Data Cache Refresh Time (in seconds)</label>\r\n          <p-dialog header=\"Minimum Cache Refresh\" [(visible)]=\"minimunCache\" [style]=\"{width: '25vw'}\"\r\n            (onHide)=\"hideModal()\" modal=\"modal\">\r\n            Minimum Cache Refresh time allowed is 5 seconds, It is not recommended to use times under 60 seconds.\r\n            <p-footer>\r\n              <p-button type=\"button\" (click)=\"hideModal()\" label=\"Ok\" icon=\"pi pi-check\" [outlined]=\"true\"></p-button>\r\n            </p-footer>\r\n          </p-dialog>\r\n        </div>\r\n      </div>\r\n    </p-accordionTab>\r\n\r\n    <p-accordionTab [selected]=\"false\" header=\"Email Settings\" *ngIf=\"showEmailSettings\">\r\n      <div class=\"grid field\">\r\n        <div class=\"col-12 p-float-label\">\r\n          <p-confirmDialog [style]=\"{width: '25vw'}\">\r\n          </p-confirmDialog>\r\n          <input id=\"emailLimit\" [(ngModel)]=\"selectedEmailLimit\" maxlength=\"100\" name=\"emailLimit\" pInputText\r\n            (keypress)=\"numberOnly($event)\" (blur)=\"checkTimeOutValid()\" [disabled]=\"projectIsLocked\" />\r\n          <label for=\"emailLimit\">Email per minute</label>\r\n        </div>\r\n      </div>\r\n    </p-accordionTab>\r\n\r\n  </p-accordion>\r\n\r\n</form>\r\n\r\n\r\n<p-dialog header=\"Delete Report\" [(visible)]=\"displayModal\" [style]=\"{width: '20vw'}\" [baseZIndex]=\"10000\"\r\n  [modal]=\"true\">\r\n  <div class=\"grid\">\r\n    <p>Do you want to delete the report...?</p>\r\n  </div>\r\n  <ng-template pTemplate=\"footer\">\r\n    <p-button type=\"button\" (onClick)=\"deleteReport()\" [disabled]=\"disableButton\" severity=\"danger\"\r\n      label=\"Delete\"></p-button>\r\n    <p-button type=\"button\" [outlined]=\"true\" severity=\"secondary\" label=\"Cancel\"\r\n      (onClick)=\"displayModal=false\"></p-button>\r\n  </ng-template>\r\n</p-dialog>\r\n\r\n<p-dialog header=\"Select URL Substitution\" [(visible)]=\"displayURLSubstitution\" [style]=\"{width: '30vw'}\"\r\n  [modal]=\"true\">\r\n  <div class=\"card\">\r\n    <div class=\"grid field\">\r\n      <div class=\"col-6\">\r\n        <div class=\"field-radiobutton\">\r\n          <p-radioButton name=\"urlSubType\" value=\"column\" [(ngModel)]=\"urlSubType\" inputId=\"rdburlColumn\" class=\"mb-1\">\r\n          </p-radioButton>\r\n          <label for=\"rdbUrlColumn\">Column</label>\r\n        </div>\r\n      </div>\r\n      <div class=\"col-6\">\r\n        <div class=\"field-radiobutton\">\r\n          <p-radioButton name=\"urlSubType\" value=\"projectVariable\" [(ngModel)]=\"urlSubType\" class=\"mb-1\"\r\n            inputId=\"rdburlProjectVariable\">\r\n          </p-radioButton>\r\n          <label for=\"rdburlProjectVariable\">Project Variable</label>\r\n        </div>\r\n      </div>\r\n      <div *ngIf=\"urlSubType==='column'\" class=\"col-12 p-float-label\">\r\n        <p-dropdown [options]=\"urlColumns\" [(ngModel)]=\"selectedColumn\" optionLabel=\"label\" [filter]=\"true\"\r\n          filterBy=\"label\" [showClear]=\"true\">\r\n          <ng-template pTemplate=\"selectedItem\">\r\n            <div *ngIf=\"selectedColumn\">\r\n              <div>{{selectedColumn.label}}</div>\r\n            </div>\r\n          </ng-template>\r\n          <ng-template let-columnName pTemplate=\"item\">\r\n            <div>\r\n              {{columnName.label}}\r\n            </div>\r\n          </ng-template>\r\n        </p-dropdown>\r\n        <label>Column Name</label>\r\n      </div>\r\n      <div *ngIf=\"urlSubType==='projectVariable'\" class=\"col-12 p-float-label\">\r\n        <p-dropdown [options]=\"urlProjectVariables\" [(ngModel)]=\"selectedProjectVariable\" optionLabel=\"label\"\r\n          [filter]=\"true\" filterBy=\"label\" [showClear]=\"true\">\r\n          <ng-template pTemplate=\"selectedItem\">\r\n            <div *ngIf=\"selectedProjectVariable\">\r\n              <div>{{selectedProjectVariable.label}}</div>\r\n            </div>\r\n          </ng-template>\r\n          <ng-template let-projectVariable pTemplate=\"item\">\r\n            <div>\r\n              {{projectVariable.label}}\r\n              <span style=\"float: right;\"><i [pTooltip]=\"projectVariable.title\" tooltipPosition=\"top\"\r\n                  tooltipZIndex=\"99999\" class=\"pi pi-info-circle\"></i></span>\r\n            </div>\r\n          </ng-template>\r\n        </p-dropdown>\r\n        <label>Project Variable</label>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <ng-template pTemplate=\"footer\">\r\n    <p-button pRipple type=\"button\" label=\"Save\" (click)=\"applyURLSubstitution()\"></p-button>\r\n    <p-button pRipple type=\"button\" label=\"Cancel\" [outlined]=\"true\" severity=\"secondary\"\r\n      (click)=\"displayURLSubstitution=false\"></p-button>\r\n  </ng-template>\r\n</p-dialog>\r\n<app-new-dynamic-asset [showDialog]=\"showDynamicAssetSettings\" [defaultName]=\"reportConfig?.reportName\"\r\n  [defaultTitle]=\"reportConfig?.description\" [projectId]=\"projectId\" [projectVersionId]=\"projectVersionId\"\r\n  [actionableGridColumnsConfig]=\"reportConfig?.formatConfig?.actionableGridColumnsConfig\"\r\n  [datastoreId]=\"selectedDatastore?.id\" [datastoreName]=\"selectedDatastore?.name\"\r\n  [dataViewId]=\"selectedDataView?.viewId === '0' ? null : selectedDataView?.viewId\"\r\n  (dialogVisibleChanged)=(newDynamicAssetDialogChanged($event))>\r\n</app-new-dynamic-asset>\r\n"], "mappings": ";AAAA,SAAuCA,YAAY,QAAqE,eAAe;AAMvI,SAASC,SAAS,QAAQ,kCAAkC;AAG5D,SAASC,kBAAkB,QAAQ,gDAAgD;AACnF,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,MAAM,QAAQ,sCAAsC;AAC7D,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,cAAc,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,oCAAoC;AAClG,SAASC,wBAAwB,QAAQ,0CAA0C;AAEnF,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,YAAY,QAAQ,mCAAmC;AAEhE,SAASC,aAAa,EAAEC,cAAc,QAAQ,kCAAkC;AAShF,SAASC,WAAW,QAAQ,uCAAuC;AAKnE,SAASC,QAAQ,QAAQ,iCAAiC;AAC1D,SAASC,aAAa,EAAEC,iBAAiB,QAAQ,uCAAuC;AAIxF,SAASC,kBAAkB,QAAQ,0CAA0C;AAE7E,SAASC,wBAAwB,QAAQ,mFAAmF;AAC5H,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,6BAA6B,QAAQ,4DAA4D;AAC1G,SAASC,yBAAyB,QAAQ,kDAAkD;AAC5F,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,IAAI,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AACtD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,6BAA6B,QAAQ,4DAA4D;AAE1G,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,SAAS,QAAQ,oCAAoC;AAE9D,SAASC,8BAA8B,QAAQ,4DAA4D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICzDrGC,EAAA,CAAAC,SAAA,2BAAkF;;;;IAA5CD,EAAA,CAAAE,qBAAA,aAAAC,MAAA,CAAAC,QAAA,CAAuB;IAA1CJ,EAAA,CAAAK,UAAA,mBAAkB;;;;;IAWvCL,EAAA,CAAAC,SAAA,oBACuF;;;;;IAkB/ED,EAAA,CAAAM,MAAA,GACF;;;;;IADEN,EAAA,CAAAO,kBAAA,OAAAC,OAAA,GAAAL,MAAA,CAAAM,WAAA,kBAAAN,MAAA,CAAAM,WAAA,CAAAC,mBAAA,cAAAF,OAAA,KAAAG,SAAA,GAAAH,OAAA,eACF;;;;;;IALFR,EADF,CAAAY,cAAA,cAAuD,qBAGnB;IAFtBZ,EAAA,CAAAa,gBAAA,2BAAAC,4EAAAC,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAd,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAM,WAAA,EAAAM,MAAA,MAAAZ,MAAA,CAAAM,WAAA,GAAAM,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAyB;IAGnCf,EAAA,CAAAqB,UAAA,IAAAC,qDAAA,0BAAsC;IAGxCtB,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,gBAAyB;IAAAZ,EAAA,CAAAM,MAAA,mBAAY;IACvCN,EADuC,CAAAuB,YAAA,EAAQ,EACzC;;;;IARQvB,EAAA,CAAAwB,SAAA,EAAyB;IAAzBxB,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAM,WAAA,CAAyB;IACmDT,EAD/B,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B,YAAAvB,MAAA,CAAAwB,eAAA,CACxD,YAAA3B,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EAA0D,gBAAgB;;;;;;IAWrG1B,EADF,CAAAY,cAAA,cAAsD,wBAEkC;IAApFZ,EAAA,CAAAa,gBAAA,2BAAAiB,qFAAAf,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAe,GAAA;MAAA,MAAA5B,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA6B,gBAAA,EAAAjB,MAAA,MAAAZ,MAAA,CAAA6B,gBAAA,GAAAjB,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA8B;IAAsDf,EAAA,CAAAuB,YAAA,EAAgB;IACtGvB,EAAA,CAAAY,cAAA,gBAAyC;IAAAZ,EAAA,CAAAM,MAAA,GAAiB;IAC5DN,EAD4D,CAAAuB,YAAA,EAAQ,EAC9D;;;;;IAHWvB,EAAA,CAAAwB,SAAA,EAAwB;IAAmCxB,EAA3D,CAAAK,UAAA,YAAA4B,WAAA,CAAAC,GAAA,CAAwB,UAAAD,WAAA,CAAAC,GAAA,CAAyD;IAC9FlC,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAA6B,gBAAA,CAA8B;IAAChC,EAAA,CAAAK,UAAA,aAAA4B,WAAA,CAAAC,GAAA,YAAA/B,MAAA,CAAAuB,eAAA,CAAoD;IAC9E1B,EAAA,CAAAwB,SAAA,EAAoB;IAApBxB,EAAA,CAAAK,UAAA,QAAA4B,WAAA,CAAAC,GAAA,CAAoB;IAAclC,EAAA,CAAAwB,SAAA,EAAiB;IAAjBxB,EAAA,CAAAmC,iBAAA,CAAAF,WAAA,CAAAG,IAAA,CAAiB;;;;;IAJ5DpC,EADF,CAAAY,cAAA,cAA+F,gBAChD;IAAAZ,EAAA,CAAAM,MAAA,kBAAW;IAAAN,EAAA,CAAAuB,YAAA,EAAQ;IAChEvB,EAAA,CAAAqB,UAAA,IAAAgB,6CAAA,kBAAsD;IAKxDrC,EAAA,CAAAuB,YAAA,EAAM;;;;IALsBvB,EAAA,CAAAwB,SAAA,GAAa;IAAbxB,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAAmC,UAAA,CAAa;;;;;;IAwBnCtC,EADF,CAAAY,cAAA,cAAoE,wBAG5B;IADpCZ,EAAA,CAAAa,gBAAA,2BAAA0B,qFAAAxB,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAwB,GAAA;MAAA,MAAArC,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAsC,qBAAA,EAAA1B,MAAA,MAAAZ,MAAA,CAAAsC,qBAAA,GAAA1B,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAmC;IACnCf,EAAA,CAAA0C,UAAA,qBAAAC,+EAAA;MAAA3C,EAAA,CAAAgB,aAAA,CAAAwB,GAAA;MAAA,MAAArC,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAWjB,MAAA,CAAAyC,qBAAA,EAAuB;IAAA,EAAC;IAAC5C,EAAA,CAAAuB,YAAA,EAAgB;IACtDvB,EAAA,CAAAY,cAAA,gBAAiC;IAAAZ,EAAA,CAAAM,MAAA,GAAsB;IACzDN,EADyD,CAAAuB,YAAA,EAAQ,EAC3D;;;;;IAJWvB,EAAA,CAAAwB,SAAA,EAA6B;IAAsBxB,EAAnD,CAAAK,UAAA,YAAAwC,gBAAA,CAAAX,GAAA,CAA6B,UAAAW,gBAAA,CAA6C;IACvF7C,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAsC,qBAAA,CAAmC;IAACzC,EAAA,CAAAK,UAAA,aAAAwC,gBAAA,CAAAX,GAAA,YAAA/B,MAAA,CAAAuB,eAAA,CAAyD;IAExF1B,EAAA,CAAAwB,SAAA,EAAyB;IAAzBxB,EAAA,CAAAK,UAAA,QAAAwC,gBAAA,CAAAX,GAAA,CAAyB;IAAClC,EAAA,CAAAwB,SAAA,EAAsB;IAAtBxB,EAAA,CAAAmC,iBAAA,CAAAU,gBAAA,CAAAT,IAAA,CAAsB;;;;;IALzDpC,EADF,CAAAY,cAAA,cAA0F,gBAC3C;IAAAZ,EAAA,CAAAM,MAAA,sBAAe;IAAAN,EAAA,CAAAuB,YAAA,EAAQ;IACpEvB,EAAA,CAAAqB,UAAA,IAAAyB,6CAAA,kBAAoE;IAMtE9C,EAAA,CAAAuB,YAAA,EAAM;;;;IAN2BvB,EAAA,CAAAwB,SAAA,GAAiB;IAAjBxB,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAA4C,cAAA,CAAiB;;;;;;IAQhD/C,EAAA,CAAAgD,uBAAA,GAA+D;IAE3DhD,EADF,CAAAY,cAAA,cAAkC,qBAGW;IADzCZ,EAAA,CAAA0C,UAAA,sBAAAO,gFAAA;MAAAjD,EAAA,CAAAgB,aAAA,CAAAkC,GAAA;MAAA,MAAA/C,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAYjB,MAAA,CAAAgD,iBAAA,EAAmB;IAAA,EAAC;IAACnD,EAAA,CAAAa,gBAAA,2BAAAuC,qFAAArC,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAkC,GAAA;MAAA,MAAA/C,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAkD,iBAAA,EAAAtC,MAAA,MAAAZ,MAAA,CAAAkD,iBAAA,GAAAtC,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA+B;IAElEf,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,gBAAwB;IAAAZ,EAAA,CAAAM,MAAA,iBAAU;IACpCN,EADoC,CAAAuB,YAAA,EAAQ,EACtC;IAEJvB,EADF,CAAAY,cAAA,cAAkC,qBAEuE;IAArGZ,EAAA,CAAAa,gBAAA,2BAAAyC,qFAAAvC,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAkC,GAAA;MAAA,MAAA/C,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAoD,gBAAA,EAAAxC,MAAA,MAAAZ,MAAA,CAAAoD,gBAAA,GAAAxC,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA8B;IAChCf,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,gBAAuB;IAAAZ,EAAA,CAAAM,MAAA,eAAQ;IACjCN,EADiC,CAAAuB,YAAA,EAAQ,EACnC;;;;;IAX+CvB,EAAA,CAAAwB,SAAA,GAAsB;IAAtBxB,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAAqD,UAAA,CAAsB;IACtCxD,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAkD,iBAAA,CAA+B;IAChErD,EADiE,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B,YAAA1B,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EACrD;IAKO1B,EAAA,CAAAwB,SAAA,GAAqB;IAArBxB,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAAsD,SAAA,CAAqB;IACpEzD,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAoD,gBAAA,CAA8B;IAA8BvD,EAA7B,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B,YAAA1B,EAAA,CAAA4B,eAAA,KAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EAAyC;;;;;;IAM1G1B,EAAA,CAAAgD,uBAAA,GAA8D;IAE1DhD,EADF,CAAAY,cAAA,cAAkC,qBAEuE;IAArGZ,EAAA,CAAAa,gBAAA,2BAAA6C,qFAAA3C,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA2C,GAAA;MAAA,MAAAxD,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAyD,gBAAA,EAAA7C,MAAA,MAAAZ,MAAA,CAAAyD,gBAAA,GAAA7C,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA8B;IAElCf,EADE,CAAAuB,YAAA,EAAa,EACT;;;;;IAH6CvB,EAAA,CAAAwB,SAAA,GAAqB;IAArBxB,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAA0D,SAAA,CAAqB;IACpE7D,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAyD,gBAAA,CAA8B;IAA8B5D,EAA7B,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B,YAAA1B,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EAAyC;;;;;;IAuB9F1B,EADF,CAAAY,cAAA,UAAqC,oBAE2C;IAA9DZ,EAAA,CAAA0C,UAAA,qBAAAoB,uGAAA;MAAA9D,EAAA,CAAAgB,aAAA,CAAA+C,IAAA;MAAA,MAAAC,YAAA,GAAAhE,EAAA,CAAAkB,aAAA,GAAA+C,SAAA;MAAA,MAAA9D,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAWjB,MAAA,CAAA+D,wBAAA,CAAA/D,MAAA,CAAAgE,QAAA,CAAAC,IAAA,EAAAJ,YAAA,CAAiD;IAAA,EAAC;IAAChE,EAAA,CAAAuB,YAAA,EAAW;IACzFvB,EAAA,CAAAY,cAAA,oBAEmC;IAAjCZ,EAAA,CAAA0C,UAAA,qBAAA2B,uGAAA;MAAArE,EAAA,CAAAgB,aAAA,CAAA+C,IAAA;MAAA,MAAAC,YAAA,GAAAhE,EAAA,CAAAkB,aAAA,GAAA+C,SAAA;MAAA,MAAA9D,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAWjB,MAAA,CAAAmE,UAAA,CAAAN,YAAA,CAAoB;IAAA,EAAC;IACpChE,EADqC,CAAAuB,YAAA,EAAW,EAC1C;;;IAJFvB,EAAA,CAAAwB,SAAA,EAAa;IAAbxB,EAAA,CAAAK,UAAA,cAAa;IAESL,EAAA,CAAAwB,SAAA,EAAa;IAAbxB,EAAA,CAAAK,UAAA,cAAa;;;;;IALvCL,EADF,CAAAY,cAAA,eAA6C,UACtC;IAAAZ,EAAA,CAAAM,MAAA,GAAiB;IAAAN,EAAA,CAAAuB,YAAA,EAAM;IAC5BvB,EAAA,CAAAqB,UAAA,IAAAkD,0EAAA,kBAAqC;IAOvCvE,EAAA,CAAAuB,YAAA,EAAM;;;;IARCvB,EAAA,CAAAwB,SAAA,GAAiB;IAAjBxB,EAAA,CAAAmC,iBAAA,CAAA6B,YAAA,CAAA5B,IAAA,CAAiB;IAChBpC,EAAA,CAAAwB,SAAA,EAA6B;IAA7BxB,EAAA,CAAAK,UAAA,SAAA2D,YAAA,CAAAQ,MAAA,SAA6B;;;;;;IAPzCxE,EADF,CAAAY,cAAA,eAAgG,sBAInD;IAFzCZ,EAAA,CAAA0C,UAAA,sBAAA+B,sFAAA;MAAAzE,EAAA,CAAAgB,aAAA,CAAA0D,IAAA;MAAA,MAAAvE,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAYjB,MAAA,CAAAwE,oBAAA,EAAsB;IAAA,EAAC;IAAC3E,EAAA,CAAAa,gBAAA,2BAAA+D,2FAAA7D,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA0D,IAAA;MAAA,MAAAvE,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA0E,gBAAA,EAAA9D,MAAA,MAAAZ,MAAA,CAAA0E,gBAAA,GAAA9D,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAmC;IAGvEf,EAAA,CAAAqB,UAAA,IAAAyD,oEAAA,2BAA2C;IAY7C9E,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,iBAAyB;IAAAZ,EAAA,CAAAM,MAAA,WAAI;IAAAN,EAAA,CAAAuB,YAAA,EAAQ;IAEnCvB,EADF,CAAAY,cAAA,eAAiC,oBAGA;IADXZ,EAAA,CAAA0C,UAAA,qBAAAqC,mFAAA;MAAA/E,EAAA,CAAAgB,aAAA,CAAA0D,IAAA;MAAA,MAAAvE,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAWjB,MAAA,CAAA+D,wBAAA,CAAA/D,MAAA,CAAAgE,QAAA,CAAAa,MAAA,CAAyC;IAAA,EAAC;IAG7EhF,EAFmC,CAAAuB,YAAA,EAAW,EACrC,EACH;;;;IAvBiDvB,EAAA,CAAAwB,SAAA,EAA0B;IAA1BxB,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAA8E,SAAA,CAA0B;IACzCjF,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAA0E,gBAAA,CAAmC;IAEvE7E,EAFwE,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B,gBACrF,YAAA1B,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EACyB;IAiBtC1B,EAAA,CAAAwB,SAAA,GAAiB;IACjBxB,EADA,CAAAK,UAAA,kBAAiB,aAAAF,MAAA,CAAAuB,eAAA,CACW;;;;;;IA/BpC1B,EAAA,CAAAgD,uBAAA,GAA2D;IAEvDhD,EADF,CAAAY,cAAA,cAAkC,qBAIW;IAFNZ,EAAA,CAAAa,gBAAA,2BAAAqE,qFAAAnE,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAmE,IAAA;MAAA,MAAAhF,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAiF,iBAAA,EAAArE,MAAA,MAAAZ,MAAA,CAAAiF,iBAAA,GAAArE,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA+B;IAClEf,EAAA,CAAA0C,UAAA,sBAAA2C,gFAAA;MAAArF,EAAA,CAAAgB,aAAA,CAAAmE,IAAA;MAAA,MAAAhF,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAYjB,MAAA,CAAAmF,yBAAA,EAA2B;IAAA,EAAC;IAE1CtF,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,iBAAmB;IAAAZ,EAAA,CAAAM,MAAA,gBAAS;IAC9BN,EAD8B,CAAAuB,YAAA,EAAQ,EAChC;IACNvB,EAAA,CAAAqB,UAAA,IAAAkE,sDAAA,mBAAgG;;;;;IAPrDvF,EAAA,CAAAwB,SAAA,GAAsB;IAAtBxB,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAAqF,UAAA,CAAsB;IAC1BxF,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAiF,iBAAA,CAA+B;IAElEpF,EAFmE,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B,YAAA1B,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EAEvD;IAItC1B,EAAA,CAAAwB,SAAA,GAAuB;IAAvBxB,EAAA,CAAAK,UAAA,SAAAF,MAAA,CAAAiF,iBAAA,CAAuB;;;;;IAkDzBpF,EAAA,CAAAY,cAAA,WAA4G;IAC1GZ,EAAA,CAAAC,SAAA,aACiJ;IACnJD,EAAA,CAAAuB,YAAA,EAAO;;;;IADFvB,EAAA,CAAAwB,SAAA,EAAyI;IAAzIxB,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAyF,eAAA,IAAAC,GAAA,EAAAvF,MAAA,CAAAwF,oBAAA,CAAAC,IAAA,oBAAAzF,MAAA,CAAAwF,oBAAA,CAAAC,IAAA,qBAAyI;;;;;IAHhJ5F,EAAA,CAAAY,cAAA,UAAK;IACHZ,EAAA,CAAAqB,UAAA,IAAAwE,6EAAA,mBAA4G;IAI5G7F,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAuB,YAAA,EAAM;;;;IALGvB,EAAA,CAAAwB,SAAA,EAAmG;IAAnGxB,EAAA,CAAAK,UAAA,UAAAF,MAAA,CAAAwF,oBAAA,kBAAAxF,MAAA,CAAAwF,oBAAA,CAAAC,IAAA,wBAAAzF,MAAA,CAAAwF,oBAAA,kBAAAxF,MAAA,CAAAwF,oBAAA,CAAAC,IAAA,qBAAmG;IAI1G5F,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAAO,kBAAA,MAAAJ,MAAA,CAAAwF,oBAAA,kBAAAxF,MAAA,CAAAwF,oBAAA,CAAAvD,IAAA,MACF;;;;;IAGApC,EAAA,CAAAY,cAAA,eAA2C;IACzCZ,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAuB,YAAA,EAAM;;;;IADJvB,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAAO,kBAAA,MAAAuF,SAAA,CAAAC,KAAA,MACF;;;;;IAFA/F,EAAA,CAAAqB,UAAA,IAAA2E,4EAAA,mBAA2C;;;;IAArChG,EAAA,CAAAK,UAAA,SAAAyF,SAAA,CAAAC,KAAA,CAAiB;;;;;IAMrB/F,EAAA,CAAAY,cAAA,WAA4E;IAC1EZ,EAAA,CAAAC,SAAA,aACmH;IACrHD,EAAA,CAAAuB,YAAA,EAAO;;;;IADFvB,EAAA,CAAAwB,SAAA,EAA2G;IAA3GxB,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAyF,eAAA,IAAAC,GAAA,EAAAO,SAAA,CAAAL,IAAA,oBAAAK,SAAA,CAAAL,IAAA,qBAA2G;;;;;IAHlH5F,EAAA,CAAAY,cAAA,eAAkB;IAChBZ,EAAA,CAAAqB,UAAA,IAAA6E,6EAAA,mBAA4E;IAI5ElG,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAuB,YAAA,EAAM;;;;IALGvB,EAAA,CAAAwB,SAAA,EAAmE;IAAnExB,EAAA,CAAAK,UAAA,SAAA4F,SAAA,CAAAL,IAAA,sBAAAK,SAAA,CAAAL,IAAA,oBAAmE;IAI1E5F,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAAO,kBAAA,MAAA0F,SAAA,CAAA7D,IAAA,MACF;;;;;;IAxBJpC,EADF,CAAAY,cAAA,cAA6E,sBAG2C;IAD1GZ,EAAA,CAAAa,gBAAA,2BAAAsF,6FAAApF,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAoF,IAAA;MAAA,MAAAjG,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAwF,oBAAA,EAAA5E,MAAA,MAAAZ,MAAA,CAAAwF,oBAAA,GAAA5E,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAkC;IAClCf,EAAA,CAAA0C,UAAA,sBAAA2D,wFAAA;MAAArG,EAAA,CAAAgB,aAAA,CAAAoF,IAAA;MAAA,MAAAjG,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAYjB,MAAA,CAAAmG,oBAAA,EAAsB;IAAA,EAAC;IAe7CtG,EAdA,CAAAqB,UAAA,IAAAkF,sEAAA,0BAAsC,IAAAC,sEAAA,2BASG,IAAAC,sEAAA,2BAKD;IAS1CzG,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,iBAAwB;IAAAZ,EAAA,CAAAM,MAAA,gBAAS;IAAAN,EAAA,CAAAuB,YAAA,EAAQ;IAEvCvB,EADF,CAAAY,cAAA,eAAiC,oBAE0D;IAA/EZ,EAAA,CAAA0C,UAAA,qBAAAgE,qFAAA;MAAA1G,EAAA,CAAAgB,aAAA,CAAAoF,IAAA;MAAA,MAAAjG,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAWjB,MAAA,CAAAwG,kBAAA,EAAoB;IAAA,EAAC;IAC5C3G,EAD2F,CAAAuB,YAAA,EAAW,EAC/F;IAELvB,EADF,CAAAY,cAAA,eAAiC,qBAEuE;IAA5FZ,EAAA,CAAA0C,UAAA,qBAAAkE,sFAAA;MAAA5G,EAAA,CAAAgB,aAAA,CAAAoF,IAAA;MAAA,MAAAjG,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAWjB,MAAA,CAAA0G,8BAAA,EAAgC;IAAA,EAAC;IAE1D7G,EAF0G,CAAAuB,YAAA,EAAW,EAC5G,EACH;;;;IApC+CvB,EAAA,CAAAwB,SAAA,EAA4B;IAA5BxB,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAA2G,gBAAA,CAA4B;IACnE9G,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAwF,oBAAA,CAAkC;IAC+B3F,EAA7B,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B,YAAA1B,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EAAyC;IA2BzG1B,EAAA,CAAAwB,SAAA,GAAiB;IAAqDxB,EAAtE,CAAAK,UAAA,kBAAiB,aAAAF,MAAA,CAAAuB,eAAA,CAAiF;IAIlG1B,EAAA,CAAAwB,SAAA,GAAiB;IAAgDxB,EAAjE,CAAAK,UAAA,kBAAiB,aAAAF,MAAA,CAAAuB,eAAA,CAA4E;;;;;;IAKzG1B,EADF,CAAAY,cAAA,cAAqG,sBAG9C;IADzCZ,EAAA,CAAAa,gBAAA,2BAAAkG,6FAAAhG,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAgG,IAAA;MAAA,MAAA7G,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA8G,YAAA,CAAAC,YAAA,CAAAC,gBAAA,CAAAC,OAAA,EAAArG,MAAA,MAAAZ,MAAA,CAAA8G,YAAA,CAAAC,YAAA,CAAAC,gBAAA,CAAAC,OAAA,GAAArG,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAqE;IAEjFf,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,iBAAqB;IAAAZ,EAAA,CAAAM,MAAA,eAAQ;IAC/BN,EAD+B,CAAAuB,YAAA,EAAQ,EACjC;;;;IALyCvB,EAAA,CAAAwB,SAAA,EAA0B;IAACxB,EAA3B,CAAAK,UAAA,YAAAF,MAAA,CAAAkH,cAAA,CAA0B,aAAAlH,MAAA,CAAAuB,eAAA,CAA6B;IACxF1B,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAA8G,YAAA,CAAAC,YAAA,CAAAC,gBAAA,CAAAC,OAAA,CAAqE;IACrEpH,EADsE,CAAAK,UAAA,2BAA0B,YAAAL,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EACxD;;;;;;IAkBpD1B,EADF,CAAAY,cAAA,eAAiF,sBAEP;IAD9CZ,EAAA,CAAAa,gBAAA,2BAAAyG,8FAAAvG,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAuG,IAAA;MAAA,MAAApH,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA8G,YAAA,CAAAO,oBAAA,EAAAzG,MAAA,MAAAZ,MAAA,CAAA8G,YAAA,CAAAO,oBAAA,GAAAzG,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA+C;IAG3Ef,EADE,CAAAuB,YAAA,EAAa,EACT;;;;IAHsBvB,EAAA,CAAAwB,SAAA,EAA+C;IAA/CxB,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAA8G,YAAA,CAAAO,oBAAA,CAA+C;IAC9BxH,EAAA,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B;;;;;;IAIvE1B,EADF,CAAAY,cAAA,cAAsF,sBAG/B;IADzCZ,EAAA,CAAAa,gBAAA,2BAAA4G,8FAAA1G,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA0G,IAAA;MAAA,MAAAvH,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA8G,YAAA,CAAAU,eAAA,EAAA5G,MAAA,MAAAZ,MAAA,CAAA8G,YAAA,CAAAU,eAAA,GAAA5G,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA+C;IAE3Df,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,iBAAsB;IAAAZ,EAAA,CAAAM,MAAA,wBAAiB;IACzCN,EADyC,CAAAuB,YAAA,EAAQ,EAC3C;;;;IALyDvB,EAAA,CAAAwB,SAAA,EAAqB;IAACxB,EAAtB,CAAAK,UAAA,YAAAF,MAAA,CAAAyH,SAAA,CAAqB,aAAAzH,MAAA,CAAAuB,eAAA,CAA6B;IACnG1B,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAA8G,YAAA,CAAAU,eAAA,CAA+C;IAC/C3H,EADgD,CAAAK,UAAA,2BAA0B,YAAAL,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EAClC;;;;;;IA7EpD1B,EAHN,CAAAY,cAAA,0BAAoH,cAC1F,eACoB,sBAEoC;IADlDZ,EAAA,CAAAa,gBAAA,2BAAAgH,uFAAA9G,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA8G,IAAA;MAAA,MAAA3H,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA8G,YAAA,CAAAc,cAAA,EAAAhH,MAAA,MAAAZ,MAAA,CAAA8G,YAAA,CAAAc,cAAA,GAAAhH,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAyC;IAGrEf,EADE,CAAAuB,YAAA,EAAa,EACT;IAEJvB,EADF,CAAAY,cAAA,cAA+C,sBAGQ;IADtBZ,EAAA,CAAAa,gBAAA,2BAAAmH,uFAAAjH,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA8G,IAAA;MAAA,MAAA3H,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA8H,0BAAA,EAAAlH,MAAA,MAAAZ,MAAA,CAAA8H,0BAAA,GAAAlH,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAwC;IAEvEf,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,iBAA0B;IAAAZ,EAAA,CAAAM,MAAA,0BAAmB;IAC/CN,EAD+C,CAAAuB,YAAA,EAAQ,EACjD;IAuCNvB,EAtCA,CAAAqB,UAAA,IAAA6G,wDAAA,qBAA6E,IAAAC,wDAAA,mBAsCwB;IAOrGnI,EAAA,CAAAY,cAAA,kCAE6E;IAFvCZ,EAAA,CAAAa,gBAAA,gCAAAuH,wGAAArH,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA8G,IAAA;MAAA,MAAA3H,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA8G,YAAA,EAAAlG,MAAA,MAAAZ,MAAA,CAAA8G,YAAA,GAAAlG,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA+B;IAEQf,EAAA,CAAAuB,YAAA,EAAwB;IAErGvB,EAAA,CAAAY,cAAA,uCAE6E;IAFlCZ,EAAA,CAAAa,gBAAA,gCAAAwH,6GAAAtH,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA8G,IAAA;MAAA,MAAA3H,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA8G,YAAA,EAAAlG,MAAA,MAAAZ,MAAA,CAAA8G,YAAA,GAAAlG,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA+B;IAEGf,EAAA,CAAAuB,YAAA,EAA6B;IAGxGvB,EADF,CAAAY,cAAA,gBAA0C,uBAE2B;IADzCZ,EAAA,CAAAa,gBAAA,2BAAAyH,wFAAAvH,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA8G,IAAA;MAAA,MAAA3H,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA8G,YAAA,CAAAsB,gBAAA,EAAAxH,MAAA,MAAAZ,MAAA,CAAA8G,YAAA,CAAAsB,gBAAA,GAAAxH,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA2C;IAGvEf,EADE,CAAAuB,YAAA,EAAa,EACT;IAMNvB,EALA,CAAAqB,UAAA,KAAAmH,yDAAA,mBAAiF,KAAAC,yDAAA,mBAKK;IAQ1FzI,EADE,CAAAuB,YAAA,EAAM,EACS;;;;IArFyDvB,EAAA,CAAAK,UAAA,mBAAkB;IAG5DL,EAAA,CAAAwB,SAAA,GAAyC;IAAzCxB,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAA8G,YAAA,CAAAc,cAAA,CAAyC;IACpB/H,EAAA,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B;IAIJ1B,EAAA,CAAAwB,SAAA,GAAqB;IAArBxB,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAA0D,SAAA,CAAqB;IAC7D7D,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAA8H,0BAAA,CAAwC;IAC3DjI,EAD4D,CAAAK,UAAA,mBAAkB,aAAAF,MAAA,CAAAuB,eAAA,CAA6B,YAAA1B,EAAA,CAAA4B,eAAA,KAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EACnE;IAIhD1B,EAAA,CAAAwB,SAAA,GAA2B;IAA3BxB,EAAA,CAAAK,UAAA,SAAAF,MAAA,CAAA0E,gBAAA,CAA2B;IAsC3B7E,EAAA,CAAAwB,SAAA,EAAmD;IAAnDxB,EAAA,CAAAK,UAAA,UAAAF,MAAA,CAAAwF,oBAAA,kBAAAxF,MAAA,CAAAwF,oBAAA,CAAAC,IAAA,qBAAmD;IAOnB5F,EAAA,CAAAwB,SAAA,EAA+B;IAA/BxB,EAAA,CAAAyB,gBAAA,iBAAAtB,MAAA,CAAA8G,YAAA,CAA+B;IAE9CjH,EAF+C,CAAAK,UAAA,cAAAF,MAAA,CAAAiF,iBAAA,CAA+B,cAAAjF,MAAA,CAAAuI,SAAA,CACvD,qBAAAvI,MAAA,CAAAwI,gBAAA,CAAsC,aAAAxI,MAAA,CAAAuB,eAAA,CAA6B,yBAAAvB,MAAA,CAAAyI,4BAAA,CACrC;IAEjC5I,EAAA,CAAAwB,SAAA,EAA+B;IAA/BxB,EAAA,CAAAyB,gBAAA,iBAAAtB,MAAA,CAAA8G,YAAA,CAA+B;IAEnDjH,EAFoD,CAAAK,UAAA,cAAAF,MAAA,CAAAiF,iBAAA,CAA+B,cAAAjF,MAAA,CAAAuI,SAAA,CAC5D,qBAAAvI,MAAA,CAAAwI,gBAAA,CAAsC,aAAAxI,MAAA,CAAAuB,eAAA,CAA6B,yBAAAvB,MAAA,CAAAyI,4BAAA,CACrC;IAGhD5I,EAAA,CAAAwB,SAAA,GAA2C;IAA3CxB,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAA8G,YAAA,CAAAsB,gBAAA,CAA2C;IAC/BvI,EAAA,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B;IAG9D1B,EAAA,CAAAwB,SAAA,EAAoC;IAApCxB,EAAA,CAAAK,UAAA,SAAAF,MAAA,CAAA8G,YAAA,kBAAA9G,MAAA,CAAA8G,YAAA,CAAAsB,gBAAA,CAAoC;IAKpCvI,EAAA,CAAAwB,SAAA,EAAoC;IAApCxB,EAAA,CAAAK,UAAA,SAAAF,MAAA,CAAA8G,YAAA,kBAAA9G,MAAA,CAAA8G,YAAA,CAAAsB,gBAAA,CAAoC;;;;;;IAaxCvI,EAAA,CAAAY,cAAA,sBAG2C;IADzCZ,EAAA,CAAAa,gBAAA,2BAAAgI,oGAAA9H,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA8H,IAAA;MAAA,MAAA3I,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA4I,qBAAA,EAAAhI,MAAA,MAAAZ,MAAA,CAAA4I,qBAAA,GAAAhI,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAmC;IAACf,EAAA,CAAA0C,UAAA,sBAAAsG,+FAAA;MAAAhJ,EAAA,CAAAgB,aAAA,CAAA8H,IAAA;MAAA,MAAA3I,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAYjB,MAAA,CAAA8I,gBAAA,EAAkB;IAAA,EAAC;IAErEjJ,EAAA,CAAAuB,YAAA,EAAa;;;;IAHuDvB,EAAlE,CAAAK,UAAA,YAAAF,MAAA,CAAA+I,aAAA,CAAyB,aAAA/I,MAAA,CAAAuB,eAAA,CAAqE;IAC9F1B,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAA4I,qBAAA,CAAmC;IACnC/I,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EAAwC;;;;;;IAK1C1B,EADF,CAAAY,cAAA,cAAkE,iBAE/B;IADPZ,EAAA,CAAAa,gBAAA,2BAAAsI,wFAAApI,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAoI,IAAA;MAAA,MAAAjJ,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA4I,qBAAA,EAAAhI,MAAA,MAAAZ,MAAA,CAAA4I,qBAAA,GAAAhI,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAmC;IAA7Df,EAAA,CAAAuB,YAAA,EACiC;IACjCvB,EAAA,CAAAY,cAAA,iBAA2B;IAAAZ,EAAA,CAAAM,MAAA,qBAAc;IAC3CN,EAD2C,CAAAuB,YAAA,EAAQ,EAC7C;;;;IAHsBvB,EAAA,CAAAwB,SAAA,EAAmC;IAAnCxB,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAA4I,qBAAA,CAAmC;IAC3D/I,EAAA,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B;;;;;;IAI9B1B,EADF,CAAAY,cAAA,cAA8D,sBAGjB;IADDZ,EAAA,CAAAa,gBAAA,2BAAAwI,6FAAAtI,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAsI,IAAA;MAAA,MAAAnJ,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAoJ,iBAAA,EAAAxI,MAAA,MAAAZ,MAAA,CAAAoJ,iBAAA,GAAAxI,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA+B;IAACf,EAAA,CAAA0C,UAAA,sBAAA8G,wFAAA;MAAAxJ,EAAA,CAAAgB,aAAA,CAAAsI,IAAA;MAAA,MAAAnJ,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAYjB,MAAA,CAAAsJ,cAAA,EAAgB;IAAA,EAAC;IAEvGzJ,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,iBAAuB;IAAAZ,EAAA,CAAAM,MAAA,iBAAU;IACnCN,EADmC,CAAAuB,YAAA,EAAQ,EACrC;;;;IAL6CvB,EAAA,CAAAwB,SAAA,EAA4B;IAACxB,EAA7B,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B,YAAAvB,MAAA,CAAAuJ,SAAA,CAAsB;IACzD1J,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAoJ,iBAAA,CAA+B;IACvEvJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EAAwC;;;;;;IAK1C1B,EADF,CAAAY,cAAA,cAA+D,iBAE5B;IADXZ,EAAA,CAAAa,gBAAA,2BAAA8I,wFAAA5I,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA4I,IAAA;MAAA,MAAAzJ,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAoJ,iBAAA,EAAAxI,MAAA,MAAAZ,MAAA,CAAAoJ,iBAAA,GAAAxI,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA+B;IAArDf,EAAA,CAAAuB,YAAA,EACiC;IACjCvB,EAAA,CAAAY,cAAA,iBAAuB;IAAAZ,EAAA,CAAAM,MAAA,iBAAU;IACnCN,EADmC,CAAAuB,YAAA,EAAQ,EACrC;;;;IAHkBvB,EAAA,CAAAwB,SAAA,EAA+B;IAA/BxB,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAoJ,iBAAA,CAA+B;IACnDvJ,EAAA,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B;;;;;IAtBhC1B,EAFJ,CAAAY,cAAA,0BAA4G,cAClF,cACY;IAChCZ,EAAA,CAAAqB,UAAA,IAAAwI,+DAAA,0BAG2C;IAE3C7J,EAAA,CAAAY,cAAA,iBAA2B;IAAAZ,EAAA,CAAAM,MAAA,qBAAc;IAC3CN,EAD2C,CAAAuB,YAAA,EAAQ,EAC7C;IAaNvB,EAZA,CAAAqB,UAAA,IAAAyI,wDAAA,kBAAkE,IAAAC,wDAAA,kBAKJ,IAAAC,wDAAA,kBAOC;IAMnEhK,EADE,CAAAuB,YAAA,EAAM,EACS;;;;IA5BDvB,EAAA,CAAAK,UAAA,mBAAkB;IAGfL,EAAA,CAAAwB,SAAA,GAA4B;IAA5BxB,EAAA,CAAAK,UAAA,SAAAF,MAAA,CAAA8J,sBAAA,CAA4B;IAORjK,EAAA,CAAAwB,SAAA,GAA6B;IAA7BxB,EAAA,CAAAK,UAAA,UAAAF,MAAA,CAAA8J,sBAAA,CAA6B;IAK1DjK,EAAA,CAAAwB,SAAA,EAAyB;IAAzBxB,EAAA,CAAAK,UAAA,SAAAF,MAAA,CAAA+J,mBAAA,CAAyB;IAOIlK,EAAA,CAAAwB,SAAA,EAA0B;IAA1BxB,EAAA,CAAAK,UAAA,UAAAF,MAAA,CAAA+J,mBAAA,CAA0B;;;;;IAY7DlK,EAJJ,CAAAY,cAAA,0BAE2G,eACxE,QAC5B;IACDZ,EAAA,CAAAM,MAAA,oMAEF;IAAAN,EAAA,CAAAuB,YAAA,EAAI;IAEJvB,EAAA,CAAAY,cAAA,QAAG;IACDZ,EAAA,CAAAM,MAAA,yDAAiD;IAAAN,EAAA,CAAAY,cAAA,aAA2B;IAAAZ,EAAA,CAAAM,MAAA,oBAAa;IAAAN,EAAA,CAAAC,SAAA,aACzD;IAAAD,EAAA,CAAAuB,YAAA,EAAI;IAACvB,EAAA,CAAAM,MAAA,oCACvC;IACFN,EADE,CAAAuB,YAAA,EAAI,EACA;IACNvB,EAAA,CAAAC,SAAA,0CACkG;IACpGD,EAAA,CAAAuB,YAAA,EAAiB;;;;IAdfvB,EAAA,CAAAK,UAAA,aAAAF,MAAA,CAAAgK,QAAA,CAAqB;IAQmCnK,EAAA,CAAAwB,SAAA,GAAuB;IAAvBxB,EAAA,CAAAK,UAAA,SAAAF,MAAA,CAAAiK,cAAA,EAAApK,EAAA,CAAAqK,aAAA,CAAuB;IAIpBrK,EAAA,CAAAwB,SAAA,GAAmD;IACtExB,EADmB,CAAAK,UAAA,kBAAAF,MAAA,CAAAyI,4BAAA,CAAmD,qBAAAzI,MAAA,CAAAwI,gBAAA,CACvE,aAAAxI,MAAA,CAAAuB,eAAA,CAA6B;;;;;IAehE1B,EAAA,CAAAC,SAAA,sCAE6B;;;;IADWD,EADZ,CAAAK,UAAA,uBAAAF,MAAA,CAAA8G,YAAA,kBAAA9G,MAAA,CAAA8G,YAAA,CAAAqD,kBAAA,CAAuD,qBAAAnK,MAAA,CAAA0E,gBAAA,CAC5C,eAAA1E,MAAA,CAAAiF,iBAAA,kBAAAjF,MAAA,CAAAiF,iBAAA,CAAAmF,UAAA,CAA6C;;;;;;IARpFvK,EAHN,CAAAY,cAAA,yBAA0D,cACtC,cACI,sBAI8E;IAFnDZ,EAAA,CAAAa,gBAAA,2BAAA2J,oFAAAzJ,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAyJ,IAAA;MAAA,MAAAtK,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA8G,YAAA,CAAAyD,kBAAA,EAAA3J,MAAA,MAAAZ,MAAA,CAAA8G,YAAA,CAAAyD,kBAAA,GAAA3J,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA6C;IACzCf,EAAA,CAAA0C,UAAA,sBAAAiI,+EAAA;MAAA3K,EAAA,CAAAgB,aAAA,CAAAyJ,IAAA;MAAA,MAAAtK,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAYjB,MAAA,CAAAyK,oBAAA,EAAsB;IAAA,EAAC;IAEpF5K,EAAA,CAAAuB,YAAA,EAAa;IAEbvB,EAAA,CAAAqB,UAAA,IAAAwJ,6DAAA,0CAAgG;IAOtG7K,EAFI,CAAAuB,YAAA,EAAM,EACF,EACS;;;;IAhBDvB,EAAA,CAAAK,UAAA,mBAAkB;IAI1BL,EAAA,CAAAwB,SAAA,GAA0C;IAA1CxB,EAAA,CAAAK,UAAA,UAAAF,MAAA,CAAA2K,YAAA,kBAAA3K,MAAA,CAAA2K,YAAA,CAAAJ,kBAAA,CAA0C;IAAC1K,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAA8G,YAAA,CAAAyD,kBAAA,CAA6C;IAExF1K,EADA,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,KAAAvB,MAAA,CAAA4K,aAAA,CAA8C,aAAA5K,MAAA,CAAA4K,aAAA,8CACyB;IAGzE/K,EAAA,CAAAwB,SAAA,EAIC;IAJDxB,EAAA,CAAAgL,aAAA,KAAA7K,MAAA,CAAA8G,YAAA,kBAAA9G,MAAA,CAAA8G,YAAA,CAAAyD,kBAAA,KAAAvK,MAAA,CAAAiF,iBAAA,IAAAjF,MAAA,CAAA0E,gBAAA,UAIC;;;;;;IASL7E,EAHF,CAAAY,cAAA,0BAEoD,4BAInB;IADiBZ,EAAA,CAAA0C,UAAA,8BAAAuI,gGAAAlK,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAkK,IAAA;MAAA,MAAA/K,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAoBjB,MAAA,CAAAgL,gBAAA,CAAApK,MAAA,CAAwB;IAAA,EAAC;IAE/Ff,EADiC,CAAAuB,YAAA,EAAmB,EACnC;;;;IALfvB,EAAA,CAAAK,UAAA,mBAAkB;IACAL,EAAA,CAAAwB,SAAA,EAA2B;IAG3CxB,EAHgB,CAAAK,UAAA,gBAAAF,MAAA,CAAAiL,WAAA,CAA2B,cAAAjL,MAAA,CAAAuI,SAAA,CAAwB,qBAAAvI,MAAA,CAAAwI,gBAAA,CAAsC,gBAAAxI,MAAA,CAAAiF,iBAAA,kBAAAjF,MAAA,CAAAiF,iBAAA,CAAAiG,EAAA,CACpE,uBAAAlL,MAAA,CAAA0E,gBAAA,kBAAA1E,MAAA,CAAA0E,gBAAA,CAAAL,MAAA,CAAgD,yBAAArE,MAAA,CAAAmL,oBAAA,CACxC,aAAAnL,MAAA,CAAAuB,eAAA,CACjB;;;;;;IAO9B1B,EAHF,CAAAY,cAAA,0BAEiD,6BAEhB;IADkBZ,EAAA,CAAA0C,UAAA,iCAAA6I,oGAAAxK,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAwK,IAAA;MAAA,MAAArL,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAuBjB,MAAA,CAAAsL,gBAAA,CAAA1K,MAAA,CAAwB;IAAA,EAAC;IAGnGf,EADE,CAAAuB,YAAA,EAAoB,EACL;;;;IAJfvB,EAAA,CAAAK,UAAA,mBAAkB;IACCL,EAAA,CAAAwB,SAAA,EAA6B;IAC9CxB,EADiB,CAAAK,UAAA,iBAAAF,MAAA,CAAAuL,YAAA,CAA6B,aAAAvL,MAAA,CAAAuB,eAAA,CAClB;;;;;;IAe1B1B,EADF,CAAAY,cAAA,cAAwD,sBAGX;IADzCZ,EAAA,CAAAa,gBAAA,2BAAA8K,6FAAA5K,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA4K,IAAA;MAAA,MAAAzL,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA0L,YAAA,EAAA9K,MAAA,MAAAZ,MAAA,CAAA0L,YAAA,GAAA9K,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA0B;IAE5Bf,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,iBAAuB;IAAAZ,EAAA,CAAAM,MAAA,eAAQ;IACjCN,EADiC,CAAAuB,YAAA,EAAQ,EACnC;;;;IALgCvB,EAAA,CAAAwB,SAAA,EAAyB;IAAzBxB,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAA2L,aAAA,CAAyB;IAC3D9L,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAA0L,YAAA,CAA0B;IAC1B7L,EAD+C,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B,YAAA1B,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EACnC;;;;;;IAT1C1B,EAHN,CAAAY,cAAA,0BAA+F,cAC3E,cACkB,sBAGW;IADzCZ,EAAA,CAAAa,gBAAA,2BAAAkL,uFAAAhL,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAgL,IAAA;MAAA,MAAA7L,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA8L,SAAA,EAAAlL,MAAA,MAAAZ,MAAA,CAAA8L,SAAA,GAAAlL,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAuB;IAACf,EAAA,CAAA0C,UAAA,sBAAAwJ,kFAAA;MAAAlM,EAAA,CAAAgB,aAAA,CAAAgL,IAAA;MAAA,MAAA7L,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAYjB,MAAA,CAAAgM,aAAA,EAAe;IAAA,EAAC;IAEtDnM,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,iBAAuB;IAAAZ,EAAA,CAAAM,MAAA,iBAAU;IACnCN,EADmC,CAAAuB,YAAA,EAAQ,EACrC;IACNvB,EAAA,CAAAqB,UAAA,IAAA+K,wDAAA,kBAAwD;IAQtDpM,EADF,CAAAY,cAAA,cAAkC,oBAGD;IAF8BZ,EAAA,CAAAa,gBAAA,2BAAAwL,qFAAAtL,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAgL,IAAA;MAAA,MAAA7L,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAmM,kBAAA,EAAAvL,MAAA,MAAAZ,MAAA,CAAAmM,kBAAA,GAAAvL,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAgC;IAE9Df,EAAA,CAAAuB,YAAA,EAAW;IAC1CvB,EAAA,CAAAY,cAAA,iBAAuB;IAAAZ,EAAA,CAAAM,MAAA,6BAAqB;IAAAN,EAAA,CAAAuB,YAAA,EAAQ;IACpDvB,EAAA,CAAAC,SAAA,sBAEuC;IAG7CD,EAFI,CAAAuB,YAAA,EAAM,EACF,EACS;;;;IA1BDvB,EAAA,CAAAK,UAAA,mBAAkB;IAGKL,EAAA,CAAAwB,SAAA,GAAsB;IAAtBxB,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAAoM,UAAA,CAAsB;IACrDvM,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAA8L,SAAA,CAAuB;IACvBjM,EADsE,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B,YAAA1B,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EAC1D;IAIT1B,EAAA,CAAAwB,SAAA,GAAmB;IAAnBxB,EAAA,CAAAK,UAAA,SAAAF,MAAA,CAAA2L,aAAA,CAAmB;IAQS9L,EAAA,CAAAwB,SAAA,GAAgC;IAAhCxB,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAmM,kBAAA,CAAgC;IAE3FtM,EAD0B,CAAAK,UAAA,YAAAL,EAAA,CAAA4B,eAAA,KAAA4K,GAAA,GAAArM,MAAA,CAAAmM,kBAAA,kBAAAnM,MAAA,CAAAmM,kBAAA,CAAAG,MAAA,OAAyD,aAAAtM,MAAA,CAAAuB,eAAA,CACvD;;;;;IAexB1B,EADF,CAAAY,cAAA,SAAI,SACE;IAAAZ,EAAA,CAAAM,MAAA,aAAM;IAAAN,EAAA,CAAAuB,YAAA,EAAK;IACfvB,EAAA,CAAAY,cAAA,SAAI;IAAAZ,EAAA,CAAAM,MAAA,sBAAe;IACrBN,EADqB,CAAAuB,YAAA,EAAK,EACrB;;;;;;IAqBCvB,EAHF,CAAAY,cAAA,gBAEsC,yBAIyD;IAD3FZ,EAAA,CAAAa,gBAAA,2BAAA6L,+GAAA3L,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA2L,IAAA;MAAA,MAAAC,WAAA,GAAA5M,EAAA,CAAAkB,aAAA,GAAA+C,SAAA;MAAAjE,EAAA,CAAAmB,kBAAA,CAAAyL,WAAA,CAAAC,aAAA,EAAA9L,MAAA,MAAA6L,WAAA,CAAAC,aAAA,GAAA9L,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAmC;IAErCf,EAAA,CAAAuB,YAAA,EAAgB;IAChBvB,EAAA,CAAAY,cAAA,YAAO;IAAAZ,EAAA,CAAAM,MAAA,kBAAW;IACpBN,EADoB,CAAAuB,YAAA,EAAQ,EACrB;;;;;;;IANyBvB,EAAA,CAAAwB,SAAA,EAAyB;IAAzBxB,EAAA,CAAA8M,sBAAA,0BAAAC,KAAA,KAAyB;IACrD/M,EADsD,CAAAK,UAAA,qBAAoB,cAAAF,MAAA,CAAAsC,qBAAA,kBAAAtC,MAAA,CAAAsC,qBAAA,CAAAP,GAAA,iBAAA/B,MAAA,CAAAuB,eAAA,CACJ;IACtE1B,EAAA,CAAAyB,gBAAA,YAAAmL,WAAA,CAAAC,aAAA,CAAmC;IACT7M,EADmC,CAAAK,UAAA,UAAS,YAAY,YAAAL,EAAA,CAAA4B,eAAA,IAAAC,GAAA,GAAA1B,MAAA,CAAAsC,qBAAA,kBAAAtC,MAAA,CAAAsC,qBAAA,CAAAP,GAAA,eACQ;;;;;;IAK5FlC,EADF,CAAAY,cAAA,gBAAgF,sBAIQ;IAFjEZ,EAAA,CAAAa,gBAAA,2BAAAmM,4GAAAjM,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAiM,IAAA;MAAA,MAAAL,WAAA,GAAA5M,EAAA,CAAAkB,aAAA,GAAA+C,SAAA;MAAAjE,EAAA,CAAAmB,kBAAA,CAAAyL,WAAA,CAAAM,QAAA,EAAAnM,MAAA,MAAA6L,WAAA,CAAAM,QAAA,GAAAnM,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA8B;IAGnDf,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,YAAO;IAAAZ,EAAA,CAAAM,MAAA,eAAQ;IACjBN,EADiB,CAAAuB,YAAA,EAAQ,EAClB;;;;;;;IANwBvB,EAAA,CAAAwB,SAAA,EAAoB;IAApBxB,EAAA,CAAA8M,sBAAA,qBAAAC,KAAA,KAAoB;IAAC/M,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAAgN,UAAA,CAAsB;IACnDnN,EAAA,CAAAyB,gBAAA,YAAAmL,WAAA,CAAAM,QAAA,CAA8B;IAEjDlN,EADA,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,KAAAvB,MAAA,CAAAsC,qBAAA,kBAAAtC,MAAA,CAAAsC,qBAAA,CAAAP,GAAA,cAAsE,YAAAlC,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,KAAAvB,MAAA,CAAAsC,qBAAA,kBAAAtC,MAAA,CAAAsC,qBAAA,CAAAP,GAAA,eACa;;;;;;IA5BzFlC,EADF,CAAAY,cAAA,cAA+B,cACZ;IACfZ,EAAA,CAAAC,SAAA,iBACoE;IACtED,EAAA,CAAAuB,YAAA,EAAK;IAGDvB,EAFJ,CAAAY,cAAA,cAAyB,gBACoB,sBAKgD;IAH9BZ,EAAA,CAAAa,gBAAA,2BAAAuM,qGAAArM,MAAA;MAAA,MAAA6L,WAAA,GAAA5M,EAAA,CAAAgB,aAAA,CAAAqM,IAAA,EAAApJ,SAAA;MAAAjE,EAAA,CAAAmB,kBAAA,CAAAyL,WAAA,CAAAU,MAAA,EAAAvM,MAAA,MAAA6L,WAAA,CAAAU,MAAA,GAAAvM,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA4B;IACrFf,EAAA,CAAA0C,UAAA,sBAAA6K,gGAAA;MAAA,MAAAX,WAAA,GAAA5M,EAAA,CAAAgB,aAAA,CAAAqM,IAAA,EAAApJ,SAAA;MAAA,MAAA9D,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAYjB,MAAA,CAAAqN,gBAAA,CAAAZ,WAAA,CAAyB;IAAA,EAAC;IAGxC5M,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,YAAO;IAAAZ,EAAA,CAAAM,MAAA,aAAM;IACfN,EADe,CAAAuB,YAAA,EAAQ,EAChB;IAWPvB,EAVA,CAAAqB,UAAA,IAAAoM,uEAAA,qBAEsC,IAAAC,uEAAA,oBAQ0C;IASpF1N,EADE,CAAAuB,YAAA,EAAK,EACF;;;;;;IAlCDvB,EAAA,CAAAK,UAAA,mBAAAuM,WAAA,CAA0B;IAEI5M,EAAA,CAAAwB,SAAA,GAAgC;IAAhCxB,EAAA,CAAA2N,sBAAA,iBAAAZ,KAAA,MAAAH,WAAA,CAAAxK,IAAA,KAAgC;IACZpC,EAAhD,CAAAK,UAAA,YAAAuM,WAAA,CAAAgB,WAAA,IAAAhB,WAAA,CAAAxK,IAAA,CAA+C,kBAAkB;IAIpCpC,EAAA,CAAAwB,SAAA,GAA6B;IAA7BxB,EAAA,CAAA8M,sBAAA,mBAAAF,WAAA,CAAAxK,IAAA,KAA6B;IACxDpC,EADyD,CAAAK,UAAA,2BAA0B,YAAAF,MAAA,CAAA0N,OAAA,CAChE;IAAsC7N,EAAA,CAAAyB,gBAAA,YAAAmL,WAAA,CAAAU,MAAA,CAA4B;IAGrEtN,EADhB,CAAAK,UAAA,YAAAL,EAAA,CAAA4B,eAAA,KAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,KAAAvB,MAAA,CAAAsC,qBAAA,kBAAAtC,MAAA,CAAAsC,qBAAA,CAAAP,GAAA,eAAmF,aAAA/B,MAAA,CAAAuB,eAAA,KAAAvB,MAAA,CAAAsC,qBAAA,kBAAAtC,MAAA,CAAAsC,qBAAA,CAAAP,GAAA,cACG;IAKvFlC,EAAA,CAAAwB,SAAA,GAAwG;IAAxGxB,EAAA,CAAAK,UAAA,UAAAuM,WAAA,CAAAU,MAAA,kBAAAV,WAAA,CAAAU,MAAA,qBAAAV,WAAA,kBAAAA,WAAA,CAAAkB,QAAA,gBAAwG;IAShE9N,EAAA,CAAAwB,SAAA,EAAmC;IAAnCxB,EAAA,CAAAK,UAAA,SAAAuM,WAAA,CAAAU,MAAA,gBAAmC;;;;;IAjCtFtN,EAHN,CAAAY,cAAA,0BAAoG,eAC3D,eACb,sBACoC;IAOxDZ,EANA,CAAAqB,UAAA,IAAA0M,gEAAA,2BAAgC,IAAAC,gEAAA,6BAM2B;IAwCnEhO,EAHM,CAAAuB,YAAA,EAAU,EACN,EACF,EACS;;;;IAlDDvB,EAAA,CAAAK,UAAA,aAAAF,MAAA,CAAAgK,QAAA,CAAqB;IAGGnK,EAAA,CAAAwB,SAAA,GAAuB;IAAvBxB,EAAA,CAAAK,UAAA,UAAAF,MAAA,CAAA8N,aAAA,CAAuB;;;;;;IAwDrDjO,EAAA,CAAAY,cAAA,qBAA4C;IAC1CZ,EAAA,CAAAC,SAAA,aAAyC;IAEvCD,EADF,CAAAY,cAAA,eAAiC,oBAGyD;IAApEZ,EAAA,CAAA0C,UAAA,mBAAAwL,2FAAA;MAAAlO,EAAA,CAAAgB,aAAA,CAAAmN,IAAA;MAAA,MAAAhO,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAASjB,MAAA,CAAAiO,0BAAA,EAA4B;IAAA,EAAC;IAA8BpO,EAAA,CAAAuB,YAAA,EAAW;IACnGvB,EAAA,CAAAY,cAAA,oBACkD;IAAhDZ,EAAA,CAAA0C,UAAA,mBAAA2L,2FAAA;MAAArO,EAAA,CAAAgB,aAAA,CAAAmN,IAAA;MAAA,MAAAhO,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAASjB,MAAA,CAAAmO,MAAA,EAAQ;IAAA,EAAC;IAExBtO,EAFsD,CAAAuB,YAAA,EAAW,EACzD,EACI;;;;IANEvB,EAAA,CAAAwB,SAAA,GAAmD;IAEFxB,EAFjD,CAAAK,UAAA,aAAAF,MAAA,CAAAoO,qBAAA,CAAA9B,MAAA,MAAmD,kBACW,iBAAiB,aAAAtM,MAAA,CAAAuB,eAAA,CACF;IACrC1B,EAAA,CAAAwB,SAAA,EAAiB;IAC9CxB,EAD6B,CAAAK,UAAA,kBAAiB,iBAAiB,aAAAF,MAAA,CAAAuB,eAAA,CACnC;;;;;IAMnD1B,EADF,CAAAY,cAAA,cAAuB,cACkC;IAAAZ,EAAA,CAAAM,MAAA,aAAM;IAAAN,EAAA,CAAAuB,YAAA,EAAK;IAClEvB,EAAA,CAAAY,cAAA,cAAmD;IAAAZ,EAAA,CAAAM,MAAA,qBAAc;IAAAN,EAAA,CAAAuB,YAAA,EAAK;IACtEvB,EAAA,CAAAY,cAAA,cAA+B;IAAAZ,EAAA,CAAAM,MAAA,uBAAgB;IAAAN,EAAA,CAAAuB,YAAA,EAAK;IACpDvB,EAAA,CAAAY,cAAA,cAAsD;IAAAZ,EAAA,CAAAM,MAAA,wBAAiB;IAAAN,EAAA,CAAAuB,YAAA,EAAK;IAC5EvB,EAAA,CAAAY,cAAA,cAA6C;IAAAZ,EAAA,CAAAM,MAAA,eAAO;IAAAN,EAAA,CAAAuB,YAAA,EAAK;IACzDvB,EAAA,CAAAY,cAAA,eAAwC;IAAAZ,EAAA,CAAAM,MAAA,2BAAmB;IAAAN,EAAA,CAAAuB,YAAA,EAAK;IAChEvB,EAAA,CAAAC,SAAA,UAAS;IACXD,EAAA,CAAAuB,YAAA,EAAK;;;;;;IAoCCvB,EAHN,CAAAY,cAAA,cAAsC,cACV,eACuB,iBAGkC;IAD7EZ,EAAA,CAAAa,gBAAA,2BAAA2N,+GAAAzN,MAAA;MAAA,MAAA0N,WAAA,GAAAzO,EAAA,CAAAgB,aAAA,CAAA0N,IAAA,EAAAzK,SAAA;MAAAjE,EAAA,CAAAmB,kBAAA,CAAAsN,WAAA,CAAAE,OAAA,EAAA5N,MAAA,MAAA0N,WAAA,CAAAE,OAAA,GAAA5N,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA6B;IAC7Bf,EAAA,CAAA0C,UAAA,sBAAAkM,0GAAA;MAAA5O,EAAA,CAAAgB,aAAA,CAAA0N,IAAA;MAAA,MAAAG,gBAAA,GAAA7O,EAAA,CAAAkB,aAAA,GAAA+C,SAAA;MAAA,MAAA9D,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAYjB,MAAA,CAAA2O,oBAAA,CAAAD,gBAAA,CAAkC;IAAA,EAAC;IAFjD7O,EAAA,CAAAuB,YAAA,EAE+E;IAC/EvB,EAAA,CAAAY,cAAA,gBAAuC;IAAAZ,EAAA,CAAAM,MAAA,gBAAS;IAAAN,EAAA,CAAAuB,YAAA,EAAQ;IAEtDvB,EADF,CAAAY,cAAA,eAAiC,oBAGA;IAD7BZ,EAAA,CAAA0C,UAAA,qBAAAqM,4GAAA;MAAA,MAAAC,KAAA,GAAAhP,EAAA,CAAAgB,aAAA,CAAA0N,IAAA,EAAAO,QAAA;MAAA,MAAAC,OAAA,GAAAlP,EAAA,CAAAkB,aAAA;MAAA,MAAA2N,gBAAA,GAAAK,OAAA,CAAAjL,SAAA;MAAA,MAAAkL,KAAA,GAAAD,OAAA,CAAAD,QAAA;MAAA,MAAA9O,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAWjB,MAAA,CAAAiP,qBAAA,CAAAP,gBAAA,CAAAQ,eAAA,EAAAF,KAAA,EAAAH,KAAA,CAAuD;IAAA,EAAC;IAEvEhP,EADiC,CAAAuB,YAAA,EAAW,EACrC;IAELvB,EADF,CAAAY,cAAA,eAAiC,oBAGA;IAD7BZ,EAAA,CAAA0C,UAAA,qBAAA4M,4GAAA;MAAA,MAAAN,KAAA,GAAAhP,EAAA,CAAAgB,aAAA,CAAA0N,IAAA,EAAAO,QAAA;MAAA,MAAAJ,gBAAA,GAAA7O,EAAA,CAAAkB,aAAA,GAAA+C,SAAA;MAAA,MAAA9D,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAWjB,MAAA,CAAAoP,kBAAA,CAAAV,gBAAA,CAAAQ,eAAA,EAAAL,KAAA,CAAkD;IAAA,EAAC;IAElEhP,EADiC,CAAAuB,YAAA,EAAW,EACrC;IAELvB,EADF,CAAAY,cAAA,gBAAiC,qBAGA;IAD7BZ,EAAA,CAAA0C,UAAA,qBAAA8M,6GAAA;MAAAxP,EAAA,CAAAgB,aAAA,CAAA0N,IAAA;MAAA,MAAAG,gBAAA,GAAA7O,EAAA,CAAAkB,aAAA,GAAA+C,SAAA;MAAA,MAAA9D,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAWjB,MAAA,CAAAsP,eAAA,CAAAZ,gBAAA,CAAAQ,eAAA,CAA6C;IAAA,EAAC;IAKnErP,EAJuC,CAAAuB,YAAA,EAAW,EACrC,EACH,EACH,EACF;;;;;;;IArB+BvB,EAAA,CAAAwB,SAAA,GAA2B;IAA3BxB,EAAA,CAAA0P,UAAA,CAAA1P,EAAA,CAAA2P,eAAA,KAAAC,GAAA,EAA2B;IAAC5P,EAAA,CAAA2N,sBAAA,4BAAAwB,KAAA,MAAAH,KAAA,KAAgC;IACxFhP,EAAA,CAAAyB,gBAAA,YAAAgN,WAAA,CAAAE,OAAA,CAA6B;IACmB3O,EADlB,CAAAK,UAAA,mBAAAL,EAAA,CAAA2P,eAAA,KAAAE,GAAA,EAAqC,aAAA1P,MAAA,CAAAuB,eAAA,CACS;IACvE1B,EAAA,CAAAwB,SAAA,EAA+B;IAA/BxB,EAAA,CAAA2N,sBAAA,2BAAAwB,KAAA,MAAAH,KAAA,KAA+B;IAElBhP,EAAA,CAAAwB,SAAA,GAAmC;IAAnCxB,EAAA,CAAA2N,sBAAA,+BAAAwB,KAAA,MAAAH,KAAA,KAAmC;IAEnDhP,EAFwE,CAAAK,UAAA,kBAAiB,aAAAF,MAAA,CAAAuB,eAAA,CAE7D;IAGZ1B,EAAA,CAAAwB,SAAA,GAAmC;IAAnCxB,EAAA,CAAA2N,sBAAA,+BAAAwB,KAAA,MAAAH,KAAA,KAAmC;IAEnDhP,EAFuE,CAAAK,UAAA,kBAAiB,aAAAF,MAAA,CAAAuB,eAAA,CAE5D;IAGZ1B,EAAA,CAAAwB,SAAA,GAAgC;IAAhCxB,EAAA,CAAA2N,sBAAA,4BAAAwB,KAAA,MAAAH,KAAA,KAAgC;IAEhDhP,EAFmE,CAAAK,UAAA,kBAAiB,aAAAF,MAAA,CAAAuB,eAAA,CAExD;;;;;;IA/ChC1B,EAHN,CAAAY,cAAA,cAAkC,SAC5B,cACkB,sBAGa;IAFnBZ,EAAA,CAAA0C,UAAA,sBAAAoN,gGAAA/O,MAAA;MAAA,MAAA8N,gBAAA,GAAA7O,EAAA,CAAAgB,aAAA,CAAA+O,IAAA,EAAA9L,SAAA;MAAA,MAAA9D,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAYjB,MAAA,CAAA6P,yBAAA,CAAAjP,MAAA,CAAAkP,OAAA,EAAApB,gBAAA,CAAsD;IAAA,EAAC;IAKnF7O,EAFI,CAAAuB,YAAA,EAAa,EACT,EACH;IAGDvB,EAFJ,CAAAY,cAAA,cAAgB,eACqB,sBAIW;IAF1CZ,EAAA,CAAAa,gBAAA,2BAAAqP,qGAAAnP,MAAA;MAAA,MAAA8N,gBAAA,GAAA7O,EAAA,CAAAgB,aAAA,CAAA+O,IAAA,EAAA9L,SAAA;MAAAjE,EAAA,CAAAmB,kBAAA,CAAA0N,gBAAA,CAAAsB,aAAA,EAAApP,MAAA,MAAA8N,gBAAA,CAAAsB,aAAA,GAAApP,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAwC;IAEEf,EAAA,CAAAuB,YAAA,EAAa;IACzDvB,EAAA,CAAAY,cAAA,gBAAgC;IAAAZ,EAAA,CAAAM,MAAA,sBAAe;IAEnDN,EAFmD,CAAAuB,YAAA,EAAQ,EAClD,EACJ;IAGDvB,EAFJ,CAAAY,cAAA,cAAgB,gBACqB,kBAGiB;IADnBZ,EAAA,CAAAa,gBAAA,2BAAAuP,iGAAArP,MAAA;MAAA,MAAA8N,gBAAA,GAAA7O,EAAA,CAAAgB,aAAA,CAAA+O,IAAA,EAAA9L,SAAA;MAAAjE,EAAA,CAAAmB,kBAAA,CAAA0N,gBAAA,CAAAwB,QAAA,EAAAtP,MAAA,MAAA8N,gBAAA,CAAAwB,QAAA,GAAAtP,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAmC;IAChEf,EAAA,CAAA0C,UAAA,sBAAA4N,4FAAA;MAAA,MAAAzB,gBAAA,GAAA7O,EAAA,CAAAgB,aAAA,CAAA+O,IAAA,EAAA9L,SAAA;MAAA,MAAA9D,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAYjB,MAAA,CAAA2O,oBAAA,CAAAD,gBAAA,CAAkC;IAAA,EAAC;IAFjD7O,EAAA,CAAAuB,YAAA,EAEkD;IAElDvB,EADA,CAAAC,SAAA,iBAAmC,sBACmD;IAE1FD,EADE,CAAAuB,YAAA,EAAO,EACJ;IAEHvB,EADF,CAAAY,cAAA,eAAuC,oBACW;IAC9CZ,EAAA,CAAAqB,UAAA,KAAAkP,+EAAA,6BAA2D;IA4BrEvQ,EADA,CAAAuB,YAAA,EAAU,EACL;IAGDvB,EAFJ,CAAAY,cAAA,eAAgB,gBACqB,uBAGW;IAF4BZ,EAAA,CAAAa,gBAAA,2BAAA2P,sGAAAzP,MAAA;MAAA,MAAA8N,gBAAA,GAAA7O,EAAA,CAAAgB,aAAA,CAAA+O,IAAA,EAAA9L,SAAA;MAAAjE,EAAA,CAAAmB,kBAAA,CAAA0N,gBAAA,CAAA4B,MAAA,EAAA1P,MAAA,MAAA8N,gBAAA,CAAA4B,MAAA,GAAA1P,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAiC;IAE7Df,EAAA,CAAAuB,YAAA,EAAa;IACzDvB,EAAA,CAAAY,cAAA,iBAAyB;IAAAZ,EAAA,CAAAM,MAAA,aAAK;IAElCN,EAFkC,CAAAuB,YAAA,EAAQ,EACjC,EACJ;IACLvB,EAAA,CAAAY,cAAA,eAA2C;IAAAZ,EAAA,CAAAM,MAAA,IAAkC;IAAAN,EAAA,CAAAuB,YAAA,EAAK;IAEhFvB,EADF,CAAAY,cAAA,eAAsB,qBAEW;IADsCZ,EAAA,CAAA0C,UAAA,mBAAAgO,4FAAA;MAAA,MAAA7B,gBAAA,GAAA7O,EAAA,CAAAgB,aAAA,CAAA+O,IAAA,EAAA9L,SAAA;MAAA,MAAA9D,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAASjB,MAAA,CAAAwQ,aAAA,CAAA9B,gBAAA,CAA2B;IAAA,EAAC;IAG5G7O,EAFiC,CAAAuB,YAAA,EAAW,EACvC,EACA;;;;;;IAnE2DvB,EAAA,CAAAwB,SAAA,GAAyB;IAAzBxB,EAAA,CAAA8M,sBAAA,0BAAAqC,KAAA,KAAyB;IAC7EnP,EAF8E,CAAAK,UAAA,gBAAe,aAAAF,MAAA,CAAAuB,eAAA,CAEjE;IAOa1B,EAAA,CAAAwB,SAAA,GAA0B;IAA1BxB,EAAA,CAAA0P,UAAA,CAAA1P,EAAA,CAAA2P,eAAA,KAAAiB,GAAA,EAA0B;IADzD5Q,EAAA,CAAA8M,sBAAA,0BAAAqC,KAAA,KAAyB;IAACnP,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAA0Q,UAAA,CAAsB;IAC1D7Q,EAAA,CAAAyB,gBAAA,YAAAoN,gBAAA,CAAAsB,aAAA,CAAwC;IAExCnQ,EADoC,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B,YAAA1B,EAAA,CAAA4B,eAAA,KAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EACvB;IACpC1B,EAAA,CAAAwB,SAAA,EAAwB;IAAxBxB,EAAA,CAAA8M,sBAAA,yBAAAqC,KAAA,KAAwB;IAKDnP,EAAA,CAAAwB,SAAA,GAA0B;IAA1BxB,EAAA,CAAA0P,UAAA,CAAA1P,EAAA,CAAA2P,eAAA,KAAAiB,GAAA,EAA0B;IAAC5Q,EAAA,CAAA8M,sBAAA,qBAAAqC,KAAA,KAAoB;IAC3EnP,EAAA,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B;IAAC1B,EAAA,CAAAyB,gBAAA,YAAAoN,gBAAA,CAAAwB,QAAA,CAAmC;IAE3DrQ,EAAA,CAAAwB,SAAA,EAAmB;IAAnBxB,EAAA,CAAA8M,sBAAA,oBAAAqC,KAAA,KAAmB;IAKnBnP,EAAA,CAAAwB,SAAA,GAAsC;IAAtCxB,EAAA,CAAAK,UAAA,UAAAwO,gBAAA,CAAAQ,eAAA,CAAsC;IAiCjDrP,EAAA,CAAAwB,SAAA,GAA0B;IAA1BxB,EAAA,CAAA0P,UAAA,CAAA1P,EAAA,CAAA2P,eAAA,KAAAiB,GAAA,EAA0B;IADhB5Q,EAAA,CAAA8M,sBAAA,mBAAAqC,KAAA,KAAkB;IAAiBnP,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAA2Q,YAAA,CAAwB;IAAC9Q,EAAA,CAAAyB,gBAAA,YAAAoN,gBAAA,CAAA4B,MAAA,CAAiC;IAEvGzQ,EADiE,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B,YAAA1B,EAAA,CAAA4B,eAAA,KAAAC,GAAA,EAAA1B,MAAA,CAAAuB,eAAA,EACpD;IACpC1B,EAAA,CAAAwB,SAAA,EAAiB;IAAjBxB,EAAA,CAAA8M,sBAAA,kBAAAqC,KAAA,KAAiB;IAGenP,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAAmC,iBAAA,CAAA0M,gBAAA,CAAAkC,iBAAA,CAAkC;IAEtC/Q,EAAA,CAAAwB,SAAA,GAAa;IAChDxB,EADmC,CAAAK,UAAA,cAAa,aAAAF,MAAA,CAAAuB,eAAA,CACpB;;;;;IA/FhC1B,EAJN,CAAAY,cAAA,0BACmE,eAC1B,eACb,sBAEgD;IAwBpEZ,EAvBA,CAAAqB,UAAA,IAAA2P,gEAAA,2BAAiC,IAAAC,gEAAA,4BAYD,IAAAC,gEAAA,6BAWgC;IA6ExElR,EAHM,CAAAuB,YAAA,EAAU,EACN,EACF,EACS;;;;IA1GDvB,EAAA,CAAAK,UAAA,mBAAkB;IAIfL,EAAA,CAAAwB,SAAA,GAAuB;IAAvBxB,EAAA,CAAAK,UAAA,UAAAF,MAAA,CAAAgR,aAAA,CAAuB;;;;;;IAyGtCnR,EADF,CAAAY,cAAA,yBAAuD,cAC7B;IACtBZ,EAAA,CAAAC,SAAA,cAEM;IAEJD,EADF,CAAAY,cAAA,cAAkC,oBAG4C;IAAzEZ,EAAA,CAAAa,gBAAA,2BAAAuQ,kFAAArQ,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAqQ,IAAA;MAAA,MAAAlR,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAmR,gBAAA,CAAAC,YAAA,EAAAxQ,MAAA,MAAAZ,MAAA,CAAAmR,gBAAA,CAAAC,YAAA,GAAAxQ,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA2C;IAA8Bf,EAAA,CAAAuB,YAAA,EAAW;IACvFvB,EAAA,CAAAY,cAAA,iBAAoC;IAAAZ,EAAA,CAAAM,MAAA,yBAAkB;IACxDN,EADwD,CAAAuB,YAAA,EAAQ,EAC1D;IAEJvB,EADF,CAAAY,cAAA,cAAkC,oBAGsC;IAAnEZ,EAAA,CAAAa,gBAAA,2BAAA2Q,kFAAAzQ,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAqQ,IAAA;MAAA,MAAAlR,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAmR,gBAAA,CAAAG,MAAA,EAAA1Q,MAAA,MAAAZ,MAAA,CAAAmR,gBAAA,CAAAG,MAAA,GAAA1Q,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAqC;IAA8Bf,EAAA,CAAAuB,YAAA,EAAW;IACjFvB,EAAA,CAAAY,cAAA,iBAA8B;IAAAZ,EAAA,CAAAM,MAAA,mBAAW;IAC3CN,EAD2C,CAAAuB,YAAA,EAAQ,EAC7C;IAEJvB,EADF,CAAAY,cAAA,eAAkC,kBAEU;IADTZ,EAAA,CAAAa,gBAAA,2BAAA6Q,gFAAA3Q,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAqQ,IAAA;MAAA,MAAAlR,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAmR,gBAAA,CAAAK,UAAA,EAAA5Q,MAAA,MAAAZ,MAAA,CAAAmR,gBAAA,CAAAK,UAAA,GAAA5Q,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAyC;IAA1Ef,EAAA,CAAAuB,YAAA,EAC0C;IAC5CvB,EAAA,CAAAY,cAAA,kBAAkC;IAAAZ,EAAA,CAAAM,MAAA,0BAAkB;IACpDN,EADoD,CAAAuB,YAAA,EAAQ,EACtD;IAEJvB,EADF,CAAAY,cAAA,eAAkC,kBAEU;IADPZ,EAAA,CAAAa,gBAAA,2BAAA+Q,gFAAA7Q,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAqQ,IAAA;MAAA,MAAAlR,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAmR,gBAAA,CAAAO,YAAA,EAAA9Q,MAAA,MAAAZ,MAAA,CAAAmR,gBAAA,CAAAO,YAAA,GAAA9Q,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA2C;IAA9Ef,EAAA,CAAAuB,YAAA,EAC0C;IAC5CvB,EAAA,CAAAY,cAAA,kBAAoC;IAAAZ,EAAA,CAAAM,MAAA,oCAA4B;IAGpEN,EAHoE,CAAAuB,YAAA,EAAQ,EAClE,EACF,EACS;;;;IA5BDvB,EAAA,CAAAK,UAAA,mBAAkB;IAO1BL,EAAA,CAAAwB,SAAA,GAAmB;IAAnBxB,EAAA,CAAAK,UAAA,oBAAmB;IAClBL,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAmR,gBAAA,CAAAC,YAAA,CAA2C;IAACvR,EAAA,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B;IAKzE1B,EAAA,CAAAwB,SAAA,GAAmB;IAAnBxB,EAAA,CAAAK,UAAA,oBAAmB;IAClBL,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAmR,gBAAA,CAAAG,MAAA,CAAqC;IAACzR,EAAA,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B;IAIpC1B,EAAA,CAAAwB,SAAA,GAAyC;IAAzCxB,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAmR,gBAAA,CAAAK,UAAA,CAAyC;IAC/D3R,EAAA,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B;IAIJ1B,EAAA,CAAAwB,SAAA,GAA2C;IAA3CxB,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAmR,gBAAA,CAAAO,YAAA,CAA2C;IACnE7R,EAAA,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B;;;;;;IA6B3C1B,EAAA,CAAAY,cAAA,oBAC4D;IADNZ,EAAA,CAAA0C,UAAA,qBAAAoP,yEAAA;MAAA9R,EAAA,CAAAgB,aAAA,CAAA+Q,IAAA;MAAA,MAAA5R,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAWjB,MAAA,CAAA6R,cAAA,EAAgB;IAAA,EAAC;IACtBhS,EAAA,CAAAuB,YAAA,EAAW;;;;IAAxCvB,EAAA,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B;;;;;;IAwC3D1B,EAFJ,CAAAY,cAAA,0BAAqF,cAC3D,cACY;IAChCZ,EAAA,CAAAC,SAAA,sBACkB;IAClBD,EAAA,CAAAY,cAAA,iBAC8F;IADvEZ,EAAA,CAAAa,gBAAA,2BAAAoR,kFAAAlR,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAkR,IAAA;MAAA,MAAA/R,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAgS,kBAAA,EAAApR,MAAA,MAAAZ,MAAA,CAAAgS,kBAAA,GAAApR,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAgC;IACrBf,EAAhC,CAAA0C,UAAA,sBAAA0P,6EAAArR,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAkR,IAAA;MAAA,MAAA/R,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAYjB,MAAA,CAAAkS,UAAA,CAAAtR,MAAA,CAAkB;IAAA,EAAC,kBAAAuR,yEAAA;MAAAtS,EAAA,CAAAgB,aAAA,CAAAkR,IAAA;MAAA,MAAA/R,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAASjB,MAAA,CAAAoS,iBAAA,EAAmB;IAAA,EAAC;IAD9DvS,EAAA,CAAAuB,YAAA,EAC8F;IAC9FvB,EAAA,CAAAY,cAAA,iBAAwB;IAAAZ,EAAA,CAAAM,MAAA,uBAAgB;IAG9CN,EAH8C,CAAAuB,YAAA,EAAQ,EAC5C,EACF,EACS;;;;IAVDvB,EAAA,CAAAK,UAAA,mBAAkB;IAGXL,EAAA,CAAAwB,SAAA,GAAyB;IAAzBxB,EAAA,CAAA0P,UAAA,CAAA1P,EAAA,CAAA2P,eAAA,IAAA6C,GAAA,EAAyB;IAEnBxS,EAAA,CAAAwB,SAAA,EAAgC;IAAhCxB,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAgS,kBAAA,CAAgC;IACQnS,EAAA,CAAAK,UAAA,aAAAF,MAAA,CAAAuB,eAAA,CAA4B;;;;;;IAiBjG1B,EAAA,CAAAY,cAAA,oBACiB;IADOZ,EAAA,CAAA0C,UAAA,qBAAA+P,4EAAA;MAAAzS,EAAA,CAAAgB,aAAA,CAAA0R,IAAA;MAAA,MAAAvS,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAWjB,MAAA,CAAAwS,YAAA,EAAc;IAAA,EAAC;IACjC3S,EAAA,CAAAuB,YAAA,EAAW;IAC5BvB,EAAA,CAAAY,cAAA,oBACiC;IAA/BZ,EAAA,CAAA0C,UAAA,qBAAAkQ,4EAAA;MAAA5S,EAAA,CAAAgB,aAAA,CAAA0R,IAAA;MAAA,MAAAvS,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAAjB,MAAA,CAAA0S,YAAA,GAAwB,KAAK;IAAA,EAAC;IAAC7S,EAAA,CAAAuB,YAAA,EAAW;;;;IAHOvB,EAAA,CAAAK,UAAA,aAAAF,MAAA,CAAA2S,aAAA,CAA0B;IAErD9S,EAAA,CAAAwB,SAAA,EAAiB;IAAjBxB,EAAA,CAAAK,UAAA,kBAAiB;;;;;IA6B/BL,EADF,CAAAY,cAAA,UAA4B,UACrB;IAAAZ,EAAA,CAAAM,MAAA,GAAwB;IAC/BN,EAD+B,CAAAuB,YAAA,EAAM,EAC/B;;;;IADCvB,EAAA,CAAAwB,SAAA,GAAwB;IAAxBxB,EAAA,CAAAmC,iBAAA,CAAAhC,MAAA,CAAA4S,cAAA,CAAAhN,KAAA,CAAwB;;;;;IAD/B/F,EAAA,CAAAqB,UAAA,IAAA2R,4DAAA,kBAA4B;;;;IAAtBhT,EAAA,CAAAK,UAAA,SAAAF,MAAA,CAAA4S,cAAA,CAAoB;;;;;IAK1B/S,EAAA,CAAAY,cAAA,UAAK;IACHZ,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAuB,YAAA,EAAM;;;;IADJvB,EAAA,CAAAwB,SAAA,EACF;IADExB,EAAA,CAAAO,kBAAA,MAAA0S,cAAA,CAAAlN,KAAA,MACF;;;;;;IAVJ/F,EADF,CAAAY,cAAA,cAAgE,sBAExB;IADHZ,EAAA,CAAAa,gBAAA,2BAAAqS,6EAAAnS,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAmS,IAAA;MAAA,MAAAhT,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAA4S,cAAA,EAAAhS,MAAA,MAAAZ,MAAA,CAAA4S,cAAA,GAAAhS,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAA4B;IAO7Df,EALA,CAAAqB,UAAA,IAAA+R,sDAAA,0BAAsC,IAAAC,sDAAA,2BAKO;IAK/CrT,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,YAAO;IAAAZ,EAAA,CAAAM,MAAA,kBAAW;IACpBN,EADoB,CAAAuB,YAAA,EAAQ,EACtB;;;;IAdQvB,EAAA,CAAAwB,SAAA,EAAsB;IAAtBxB,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAA0Q,UAAA,CAAsB;IAAC7Q,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAA4S,cAAA,CAA4B;IAC5C/S,EADiE,CAAAK,UAAA,gBAAe,mBAC9D;;;;;IAmB/BL,EADF,CAAAY,cAAA,UAAqC,UAC9B;IAAAZ,EAAA,CAAAM,MAAA,GAAiC;IACxCN,EADwC,CAAAuB,YAAA,EAAM,EACxC;;;;IADCvB,EAAA,CAAAwB,SAAA,GAAiC;IAAjCxB,EAAA,CAAAmC,iBAAA,CAAAhC,MAAA,CAAAmT,uBAAA,CAAAvN,KAAA,CAAiC;;;;;IADxC/F,EAAA,CAAAqB,UAAA,IAAAkS,4DAAA,kBAAqC;;;;IAA/BvT,EAAA,CAAAK,UAAA,SAAAF,MAAA,CAAAmT,uBAAA,CAA6B;;;;;IAKnCtT,EAAA,CAAAY,cAAA,UAAK;IACHZ,EAAA,CAAAM,MAAA,GACA;IAAAN,EAAA,CAAAY,cAAA,gBAA4B;IAAAZ,EAAA,CAAAC,SAAA,aAC4B;IAC1DD,EAD0D,CAAAuB,YAAA,EAAO,EAC3D;;;;IAHJvB,EAAA,CAAAwB,SAAA,EACA;IADAxB,EAAA,CAAAO,kBAAA,MAAAiT,mBAAA,CAAAzN,KAAA,MACA;IAA+B/F,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAAK,UAAA,aAAAmT,mBAAA,CAAAC,KAAA,CAAkC;;;;;;IAVvEzT,EADF,CAAAY,cAAA,cAAyE,sBAEjB;IADVZ,EAAA,CAAAa,gBAAA,2BAAA6S,6EAAA3S,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA2S,IAAA;MAAA,MAAAxT,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAAlB,EAAA,CAAAmB,kBAAA,CAAAhB,MAAA,CAAAmT,uBAAA,EAAAvS,MAAA,MAAAZ,MAAA,CAAAmT,uBAAA,GAAAvS,MAAA;MAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;IAAA,EAAqC;IAO/Ef,EALA,CAAAqB,UAAA,IAAAuS,sDAAA,0BAAsC,IAAAC,sDAAA,2BAKY;IAOpD7T,EAAA,CAAAuB,YAAA,EAAa;IACbvB,EAAA,CAAAY,cAAA,YAAO;IAAAZ,EAAA,CAAAM,MAAA,uBAAgB;IACzBN,EADyB,CAAAuB,YAAA,EAAQ,EAC3B;;;;IAhBQvB,EAAA,CAAAwB,SAAA,EAA+B;IAA/BxB,EAAA,CAAAK,UAAA,YAAAF,MAAA,CAAA2T,mBAAA,CAA+B;IAAC9T,EAAA,CAAAyB,gBAAA,YAAAtB,MAAA,CAAAmT,uBAAA,CAAqC;IAC9CtT,EAAjC,CAAAK,UAAA,gBAAe,mBAAoC;;;;;;IAmBzDL,EAAA,CAAAY,cAAA,oBAA8E;IAAjCZ,EAAA,CAAA0C,UAAA,mBAAAqR,2EAAA;MAAA/T,EAAA,CAAAgB,aAAA,CAAAgT,IAAA;MAAA,MAAA7T,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAASjB,MAAA,CAAA8T,oBAAA,EAAsB;IAAA,EAAC;IAACjU,EAAA,CAAAuB,YAAA,EAAW;IACzFvB,EAAA,CAAAY,cAAA,oBACyC;IAAvCZ,EAAA,CAAA0C,UAAA,mBAAAwR,2EAAA;MAAAlU,EAAA,CAAAgB,aAAA,CAAAgT,IAAA;MAAA,MAAA7T,MAAA,GAAAH,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAoB,WAAA,CAAAjB,MAAA,CAAAgU,sBAAA,GAAgC,KAAK;IAAA,EAAC;IAACnU,EAAA,CAAAuB,YAAA,EAAW;;;IADLvB,EAAA,CAAAwB,SAAA,EAAiB;IAAjBxB,EAAA,CAAAK,UAAA,kBAAiB;;;AD1lBpE,OAAM,MAAO+T,uBAAuB;EAwPlC,IAAIrJ,aAAaA,CAAA;IACf,OAAO,IAAI,CAACnC,4BAA4B,EAAEyL,IAAI,CAACC,GAAG,IAAKA,GAAG,CAAChH,MAAM,CAAC1H,IAAI,KAAKnI,wBAAwB,CAAC8W,IAAI,IAAID,GAAG,CAAChH,MAAM,CAAC1H,IAAI,KAAKnI,wBAAwB,CAAC+W,QAAS,CAAC,IAAI,KAAK;EAC9K;EAEAC,YACUC,kBAAsC,EACtCC,iBAAoC,EACpCC,UAA+B,EAC/BC,mBAAwC,EACxCC,QAAmB,EACnBC,MAAc,EACdC,eAAgC,EAChCC,WAAwB,EACxBC,mBAAwC,EACxCC,kBAAsC,EACtCC,iBAAoC,EACpCC,aAA4B,EACAC,oBAA0C;IAZtE,KAAAZ,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACe,KAAAC,oBAAoB,GAApBA,oBAAoB;IAjQjD,KAAA5T,eAAe,GAAG,KAAK;IAGtB,KAAA6T,eAAe,GAAG,IAAIxY,YAAY,EAAkB;IACpD,KAAAyY,gBAAgB,GAAG,IAAIzY,YAAY,EAA2B;IAExE;IACA,KAAA0Y,UAAU,GAAGjY,UAAU;IACvB,KAAA2G,QAAQ,GAAGnH,SAAS,CAAC0Y,IAAI;IAIzB,KAAAjT,qBAAqB,GAAQ,IAAI;IAGjC,KAAAe,UAAU,GAAiB,EAAE;IAE7B,KAAAK,SAAS,GAAmB,CAAC;MAAEzB,IAAI,EAAE,sBAAsB;MAAEiJ,EAAE,EAAE,IAAI;MAAEsK,gBAAgB,EAAE;IAAI,CAAE,CAAC;IAGhG,KAAAzM,aAAa,GAAiB,EAAE;IAEhC,KAAAQ,SAAS,GAAiB,EAAE;IAE5B,KAAAjG,SAAS,GAAiB,EAAE;IAM5B,KAAAmS,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,qBAAqB,GAAG,CAAC,sBAAsB,CAAC;IAChD,KAAAC,eAAe,GAAG,sBAAsB;IAIxC,KAAAC,SAAS,GAAG,KAAK;IAEjB,KAAAC,KAAK,GAAiB,EAAE;IACxB,KAAAC,YAAY,GAAG,GAAG;IAClB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,IAAI,GAAoB,EAAE;IAC1B,KAAAC,cAAc,GAAG,KAAK;IAItB,KAAApL,WAAW,GAAe,EAAE;IAC5B,KAAAM,YAAY,GAAkB,EAAE,CAAC,CAAC;IAClC,KAAA+K,wBAAwB,GAAGhZ,wBAAwB;IACnD,KAAAwH,SAAS,GAAe,EAAE;IAC1B;IACA;IACA,KAAAyR,oBAAoB,GAA4D,EAAE;IAClF;IACA,KAAAC,2BAA2B,GAAsG,EAAE;IACnI,KAAA/N,4BAA4B,GAAiC,EAAE;IAE/D,KAAAgO,cAAc,GACZ;MACE1N,aAAa,EAAE,EAAE;MACjBQ,SAAS,EAAE,EAAE;MACbmN,cAAc,EAAE,EAAE;MAClBnO,SAAS,EAAE,EAAE;MACboO,QAAQ,EAAE,EAAE;MACZrB,UAAU,EAAE,EAAE;MACdsB,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,EAAE;MACfC,GAAG,EAAE,EAAE;MACPC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,cAAc,EAAE;OACjB;MACDC,gBAAgB,EAAE;QAChBC,YAAY,EAAE;OACf;MACDC,aAAa,EAAE;QACbC,KAAK,EAAE,EAAE;QACTxM,EAAE,EAAE,CAAC;QACL+L,QAAQ,EAAE,CAAC;QACXU,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,CAAC;QACbC,MAAM,EAAE;OACT;MACDC,UAAU,EAAE,EAAE;MACd3B,cAAc,EAAE,KAAK;MACrBD,YAAY,EAAE,CAAC;MACf6B,iBAAiB,EAAE,CAAC;MACpBC,iBAAiB,EAAE,EAAE;MACrBlM,SAAS,EAAE,EAAE;MACbJ,YAAY,EAAE,EAAE;MAChBS,kBAAkB,EAAE,EAAE;MACtB8L,UAAU,EAAE,CAAC;MACbC,gBAAgB,EAAE,EAAE;MACpBC,gBAAgB,EAAE,EAAE;MACpBpR,YAAY,EAAE;QAAEmE,EAAE,EAAE,CAAC;QAAEkN,QAAQ,EAAE,EAAE;QAAEzB,QAAQ,EAAE,EAAE;QAAE0B,MAAM,EAAE;MAAE,CAAE;MAC/DC,cAAc,EAAE;QAAEpN,EAAE,EAAE,CAAC;QAAEqN,UAAU,EAAE,EAAE;QAAEC,QAAQ,EAAE,EAAE;QAAEC,aAAa,EAAE,EAAE;QAAE1P,aAAa,EAAE,EAAE;QAAEQ,SAAS,EAAE;MAAE,CAAE;MAC5GmP,oBAAoB,EAAE,EAAE;MACxB9Q,cAAc,EAAE,KAAK;MACrBQ,gBAAgB,EAAE,IAAI;MACtBf,oBAAoB,EAAE,KAAK;MAC3BG,eAAe,EAAE,EAAE;MACnB+C,kBAAkB,EAAE;KACrB;IAEH,KAAAoO,mBAAmB,GAAiB;MAClC3I,aAAa,EAAE,EAAE;MACjBM,MAAM,EAAE,GAAG;MACXJ,QAAQ,EAAE,EAAE;MACZhB,eAAe,EAAE,CAAC,IAAIlS,MAAM,EAAE,CAAC;MAC/B4T,iBAAiB,EAAE;KACpB;IACD,KAAAI,aAAa,GAAmB,CAAC,IAAI,CAAC2H,mBAAmB,CAAC;IAC1D,KAAAvK,qBAAqB,GAAmB,EAAE;IAC1C,KAAAuC,YAAY,GAAqB,CAC/B;MAAEiI,IAAI,EAAE,WAAW;MAAE3W,IAAI,EAAE;IAAY,CAAE,EACzC;MAAE2W,IAAI,EAAE,eAAe;MAAE3W,IAAI,EAAE;IAAgB,CAAE,CAClD;IAED,KAAA0I,YAAY,GAA+B;MACzC1I,IAAI,EAAE,aAAa;MACnB4U,WAAW,EAAE,aAAa;MAC1BgC,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,QAAQ;MAChB1V,UAAU,EAAE,YAAY;MACxB0F,aAAa,EAAE,mBAAmB;MAClCQ,SAAS,EAAE,eAAe;MAC1ByP,WAAW,EAAE,2BAA2B;MACxCC,WAAW,EAAE,4BAA4B;MACzC1O,kBAAkB,EAAE;KACrB;IAED,KAAA2O,eAAe,GAA+B;MAC5CjX,IAAI,EAAE,EAAE;MACR4U,WAAW,EAAE,EAAE;MACfgC,MAAM,EAAE,mEAAmE;MAC3EM,UAAU,EAAE,4DAA4D;MACxEL,OAAO,EAAE,mDAAmD;MAC5DC,MAAM,EAAE;KACT;IACD,KAAAK,OAAO,GAAmB,EAAE;IAC5B,KAAAtL,aAAa,GAAmB,EAAE;IAClC,KAAAuL,aAAa,GAAU,EAAE;IACzB,KAAArM,UAAU,GAAQnQ,SAAS,CAACmQ,UAAU;IACtC,KAAAU,OAAO,GAAG7Q,SAAS,CAAC6Q,OAAO;IAC3B,KAAAvL,UAAU,GAAU,CAAC;MAAEF,IAAI,EAAE,MAAM;MAAEF,GAAG,EAAE;IAAM,CAAE,EAAE;MAAEE,IAAI,EAAE,OAAO;MAAEF,GAAG,EAAE;IAAO,CAAE,CAAC;IACpF,KAAAa,cAAc,GAAU,CAAC;MAAEX,IAAI,EAAE,WAAW;MAAEF,GAAG,EAAE;IAAW,CAAE,EAAE;MAAEE,IAAI,EAAE,mBAAmB;MAAEF,GAAG,EAAE;IAAO,CAAE,CAAC;IAC9G,KAAAqK,UAAU,GAAU,CAClB;MACEnK,IAAI,EAAE,MAAM;MAAEF,GAAG,EAAE,MAAM;MACzBsW,MAAM,EAAE;KACT,EACD;MACEpW,IAAI,EAAE,KAAK;MAAEF,GAAG,EAAE,KAAK;MACvBuX,QAAQ,EAAE,CAAC;QAAErX,IAAI,EAAE,SAAS;QAAEF,GAAG,EAAE;MAAS,CAAE,EAAE;QAAEE,IAAI,EAAE,SAAS;QAAEF,GAAG,EAAE;MAAS,CAAE,EAAE;QAAEE,IAAI,EAAE,YAAY;QAAEF,GAAG,EAAE;MAAY,CAAE,CAAC;MAC/HsW,MAAM,EAAE;KACT,EACD;MACEpW,IAAI,EAAE,QAAQ;MAAEF,GAAG,EAAE,QAAQ;MAC7BuX,QAAQ,EAAE,CAAC;QAAErX,IAAI,EAAE,SAAS;QAAEF,GAAG,EAAE;MAAS,CAAE,EAAE;QAAEE,IAAI,EAAE,SAAS;QAAEF,GAAG,EAAE;MAAS,CAAE,EAAE;QAAEE,IAAI,EAAE,YAAY;QAAEF,GAAG,EAAE;MAAY,CAAE,CAAC;MAC/HsW,MAAM,EAAE;KACT,EACD;MACEpW,IAAI,EAAE,WAAW;MAAEF,GAAG,EAAE,WAAW;MACnCsW,MAAM,EAAE;KACT,EACD;MACEpW,IAAI,EAAE,MAAM;MAAEF,GAAG,EAAE,MAAM;MACzBuX,QAAQ,EAAE,CAAC;QAAErX,IAAI,EAAE,QAAQ;QAAEF,GAAG,EAAE;MAAQ,CAAE,EAAE;QAAEE,IAAI,EAAE,SAAS;QAAEF,GAAG,EAAE;MAAS,CAAE,EAAE;QAAEE,IAAI,EAAE,YAAY;QAAEF,GAAG,EAAE;MAAY,CAAE,CAAC;MAC7HsW,MAAM,EAAE;KACT,EACD;MACEpW,IAAI,EAAE,KAAK;MAAEF,GAAG,EAAE,KAAK;MACvBuX,QAAQ,EAAE,CAAC;QAAErX,IAAI,EAAE,SAAS;QAAEF,GAAG,EAAE;MAAS,CAAE,EAAE;QAAEE,IAAI,EAAE,MAAM;QAAEF,GAAG,EAAE;MAAM,CAAE,CAAC;MAC9EsW,MAAM,EAAE;KACT,EACD;MACEpW,IAAI,EAAE,SAAS;MAAEF,GAAG,EAAE,SAAS;MAC/BsW,MAAM,EAAE;KACT,CACF;IAED,KAAAkB,0BAA0B,GAAG,KAAK;IAClC,KAAAlU,UAAU,GAAgB,EAAE;IAG5B,KAAAmU,YAAY,GAAG,KAAK;IACpB,KAAA1P,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAA0P,iBAAiB,GAAG,CAAC;IACrB,KAAAzF,sBAAsB,GAAG,KAAK;IAC9B,KAAA0F,UAAU,GAAG,QAAQ;IACrB,KAAAhJ,UAAU,GAAiB,EAAE;IAC7B,KAAAiD,mBAAmB,GAAiB,EAAE;IAOtC,KAAAgG,qBAAqB,GAAG,EAAE;IAC1B,KAAAC,wBAAwB,GAAG,KAAK;IAExB,KAAAC,oBAAoB,GAAiB,IAAI5c,YAAY,EAAE;IAKvD,KAAA6c,kBAAkB,GAAmB,EAAE;IAG/C,KAAAnT,gBAAgB,GAAyB,EAAE;IAC3C,KAAAoT,YAAY,GAAG;MAAE9X,IAAI,EAAE,+BAA+B;MAAEiJ,EAAE,EAAE,IAAI;MAAEzF,IAAI,EAAErI,iBAAiB,CAAC4c,OAAO;MAAEnD,WAAW,EAAE;IAAE,CAAE;IACpH,KAAArR,oBAAoB,GAAqB,IAAI,CAACuU,YAAY;IAC1D,KAAA7S,cAAc,GAAa,EAAE;IAI7B,KAAA5G,WAAW,GAAG,IAAI;IAClB,KAAA2Z,sBAAsB,GAAe,EAAE;IACvC,KAAAC,YAAY,GAAG,KAAK;IAIpB,KAAAja,QAAQ,GAAGN,SAAS,CAACwa,eAAe;IAGpC,KAAAC,iBAAiB,GAAG,KAAK;IAEzB,KAAA3S,SAAS,GAAU,CAAC;MAAE1F,GAAG,EAAE,IAAI;MAAEsY,KAAK,EAAE;IAAE,CAAE,EAAE;MAAEtY,GAAG,EAAE,IAAI;MAAEsY,KAAK,EAAE;IAAE,CAAE,EAAE;MAAEtY,GAAG,EAAE,KAAK;MAAEsY,KAAK,EAAE;IAAG,CAAE,CAAC;IAoBjG,IAAI,CAACvT,YAAY,GAAG;MAClBiC,aAAa,EAAE,EAAE;MACjBQ,SAAS,EAAE,EAAE;MACbmN,cAAc,EAAE,EAAE;MAClBnO,SAAS,EAAE,EAAE;MACboO,QAAQ,EAAE,EAAE;MACZrB,UAAU,EAAE,EAAE;MACdsB,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,EAAE;MACfC,GAAG,EAAE,EAAE;MACPC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,cAAc,EAAE;OACjB;MACDC,gBAAgB,EAAE;QAChBC,YAAY,EAAE;OACf;MACDC,aAAa,EAAE;QACbC,KAAK,EAAE,EAAE;QACTxM,EAAE,EAAE,CAAC;QACL+L,QAAQ,EAAE,CAAC;QACXU,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,CAAC;QACbC,MAAM,EAAE;OACT;MACDC,UAAU,EAAE,EAAE;MACd3B,cAAc,EAAE,KAAK;MACrBD,YAAY,EAAE,CAAC;MACf6B,iBAAiB,EAAE,CAAC;MACpBC,iBAAiB,EAAE,EAAE;MACrBlM,SAAS,EAAE,EAAE;MACbJ,YAAY,EAAE,EAAE;MAChBS,kBAAkB,EAAE,EAAE;MACtB8L,UAAU,EAAE,CAAC;MACbE,gBAAgB,EAAE,EAAE;MACpBO,oBAAoB,EAAE,EAAE;MACxB4B,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,CAAC;MACjB3S,cAAc,EAAE,KAAK;MACrBQ,gBAAgB,EAAE,IAAI;MACtBf,oBAAoB,EAAE,KAAK;MAC3BG,eAAe,EAAE;KAClB;EACH;EAEAgT,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEMA,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChBD,KAAI,CAACE,gCAAgC,EAAE;MACvCF,KAAI,CAACG,gBAAgB,GAAGC,IAAI,CAACC,SAAS,CAACL,KAAI,CAAC5T,YAAY,CAAC;MACzD4T,KAAI,CAACM,oBAAoB,EAAE;MAC3B,MAAMN,KAAI,CAACO,eAAe,EAAE;MAC5BP,KAAI,CAACQ,aAAa,CAACR,KAAI,CAAC5T,YAAY,EAAEyB,SAAS,GAAGmS,KAAI,CAAC5T,YAAY,CAAC0B,gBAAgB,GAAG,CAAC,CAAC;MACzFkS,KAAI,CAACS,YAAY,CAACT,KAAI,CAAC5T,YAAY,EAAEyB,SAAS,GAAGmS,KAAI,CAAC5T,YAAY,CAAC0B,gBAAgB,GAAG,CAAC,CAAC;MACxFkS,KAAI,CAACU,aAAa,CAACV,KAAI,CAAC5T,YAAY,EAAEyB,SAAS,EAAEmS,KAAI,CAAC5T,YAAY,EAAE0B,gBAAgB,CAAC;MACrFkS,KAAI,CAACW,yBAAyB,EAAE;MAChCX,KAAI,CAACvF,oBAAoB,CAACmG,gBAAgB,CAACZ,KAAI,CAAClS,gBAAgB,CAAC;MACjEkS,KAAI,CAACa,iBAAiB,EAAE;MAExBb,KAAI,CAAC7Y,gBAAgB,GAAG6Y,KAAI,CAAC5T,YAAY,EAAEwO,UAAU,GAAGoF,KAAI,CAAC5T,YAAY,CAACwO,UAAU,GAAGoF,KAAI,CAACvY,UAAU,CAAC,CAAC,CAAC;MACzG,IAAIuY,KAAI,CAAC5T,YAAY,CAACwO,UAAU,KAAKjY,UAAU,CAACme,cAAc,EAAE;QAC9Dd,KAAI,CAACpY,qBAAqB,GAAGoY,KAAI,CAAC9X,cAAc,CAAC6Y,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3Z,GAAG,KAAK5E,cAAc,CAACwe,SAAS,CAAC;MAChG,CAAC,MACI;QACHjB,KAAI,CAACpY,qBAAqB,GAAGoY,KAAI,CAAC9X,cAAc,CAAC6Y,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3Z,GAAG,KAAK2Y,KAAI,CAAC5T,YAAY,CAAC4P,cAAc,CAAC;MACxG;MAEA,IAAIgE,KAAI,CAAC5T,YAAY,EAAEiC,aAAa,IAAI2R,KAAI,CAAC5T,YAAY,EAAEiC,aAAa,KAAK,EAAE,EAAE;QAC/E2R,KAAI,CAAC9R,qBAAqB,GAAG8R,KAAI,CAAC5T,YAAY,EAAEiC,aAAa,KAAK,EAAE,GAAG,GAAG,GAAG2R,KAAI,CAAC5T,YAAY,EAAEiC,aAAa;MAC/G;MACA,IAAI2R,KAAI,CAAC5T,YAAY,EAAEyC,SAAS,EAAE;QAChCmR,KAAI,CAACtR,iBAAiB,GAAGsR,KAAI,CAAC5T,YAAY,EAAEyC,SAAS;MACvD;MAEA,IAAImR,KAAI,CAAC5T,YAAY,EAAEiR,iBAAiB,EAAE;QACxC2C,KAAI,CAACkB,yBAAyB,GAAGlB,KAAI,CAAC5T,YAAY,EAAEiR,iBAAiB;MACvE,CAAC,MAAM;QACL2C,KAAI,CAACkB,yBAAyB,GAAG,GAAG;MACtC;MACAlB,KAAI,CAACmB,WAAW,GAAGnB,KAAI,CAAC5T,YAAY,EAAEgQ,GAAG;MACzC4D,KAAI,CAACoB,WAAW,GAAGpB,KAAI,CAAC5T,YAAY,EAAEiQ,QAAQ;MAE9C2D,KAAI,CAACjX,gBAAgB,GAAGiX,KAAI,CAAC5T,YAAY,EAAEyQ,gBAAgB,CAACC,YAAY;MACxEkD,KAAI,CAACxX,iBAAiB,GAAGwX,KAAI,CAAC5T,YAAY,EAAEsQ,eAAe,CAAC2E,UAAU;MACtErB,KAAI,CAACvE,cAAc,GAAGuE,KAAI,CAAC5T,YAAY,CAACqP,cAAc;MACtDuE,KAAI,CAAC5O,SAAS,GAAG4O,KAAI,CAAC5T,YAAY,CAACgF,SAAS;MAC5C,IAAI4O,KAAI,CAAC5O,SAAS,EAAE;QAClB,MAAMkQ,IAAI,GAAGtB,KAAI,CAACtO,UAAU,CAACqP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3Z,GAAG,KAAK2Y,KAAI,CAAC5O,SAAS,CAAC;QAChE4O,KAAI,CAAC/O,aAAa,GAAGqQ,IAAI,CAAC1C,QAAQ;QAClCoB,KAAI,CAAChP,YAAY,GAAGgP,KAAI,CAAC5T,YAAY,CAAC4E,YAAY;QAClDgP,KAAI,CAACvO,kBAAkB,GAAG6P,IAAI,CAAC3D,MAAM;MACvC;MACA,IAAIqC,KAAI,CAAC5T,YAAY,EAAEmR,UAAU,EAAE;QACjCyC,KAAI,CAAC1I,kBAAkB,GAAG0I,KAAI,CAAC5T,YAAY,EAAEmR,UAAU;MACzD,CAAC,MAAM;QACLyC,KAAI,CAAC1I,kBAAkB,GAAG,EAAE;MAC9B;MACA0I,KAAI,CAACuB,QAAQ,CAACvB,KAAI,CAAC5T,YAAY,CAAC;MAEhC4T,KAAI,CAAC5F,WAAW,CAACoH,WAAW,CAACxB,KAAI,CAAClS,gBAAgB,CAAC,CAAC2T,IAAI,CAACC,QAAQ,IAAG;QAClE1B,KAAI,CAAC1E,SAAS,GAAIoG,QAAQ,KAAKvf,SAAS,CAACwf,SAAS,CAACC,OAAQ;QAC3D5B,KAAI,CAAC6B,qBAAqB,GAAIH,QAAQ,KAAKvf,SAAS,CAACwf,SAAS,CAACC,OAAQ;MACzE,CAAC,CAAC,CAACE,KAAK,CAACC,KAAK,IAAG;QACfC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,MAAMF,KAAK;MACb,CAAC,CAAC;MAEF/B,KAAI,CAACkC,mBAAmB,CAAClC,KAAI,CAAC5T,YAAY,CAAC;MAE3C;MACA,IAAI4T,KAAI,CAACpY,qBAAqB,EAAEP,GAAG,KAAK5E,cAAc,CAACwe,SAAS,EAAE;QAChEjB,KAAI,CAACpR,cAAc,EAAE;MACvB;MACAoR,KAAI,CAAC1J,aAAa,GAAG0J,KAAI,CAAC5T,YAAY,CAACoR,gBAAgB;MACvDwC,KAAI,CAAC1J,aAAa,CAAC6L,OAAO,CAACC,YAAY,IAAG;QACxCpC,KAAI,CAAC/L,oBAAoB,CAACmO,YAAY,CAAC;MACzC,CAAC,CAAC;MACFpC,KAAI,CAACqC,kBAAkB,EAAE;MACzBrC,KAAI,CAACsC,eAAe,EAAE;MACtBtC,KAAI,CAACuC,kBAAkB,EAAE;MACzBvC,KAAI,CAACwC,0BAA0B,EAAE;MACjCxC,KAAI,CAACzQ,cAAc,GAAG,YAAYyQ,KAAI,CAACnS,SAAS,IAAImS,KAAI,CAAClS,gBAAgB,cAAc;IAAC;EAC1F;EAEA2U,WAAWA,CAAA;IACT,IAAI,CAACC,WAAW,EAAEC,WAAW,EAAE;IAC/B,IAAI,CAACC,YAAY,EAAED,WAAW,EAAE;IAChC,IAAI,CAACxD,oBAAoB,EAAEwD,WAAW,EAAE;IACxC,IAAI,CAACE,eAAe,EAAEF,WAAW,EAAE;IACnC,IAAI,CAACG,uBAAuB,EAAEH,WAAW,EAAE;EAC7C;EAEAnC,aAAaA,CAAC1S,gBAAgB;IAC5B,IAAI,CAAC+L,kBAAkB,CAAC2G,aAAa,CAAC1S,gBAAgB,CAAC,CAACiV,SAAS,CAAC;MAChEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACrR,MAAM,GAAG,CAAC,EAAE;UACvB,IAAI,CAACjJ,UAAU,GAAG,EAAE;UACpBsa,QAAQ,CAACd,OAAO,CAACxC,KAAK,IAAG;YACvB,IAAI,CAAChX,UAAU,CAACua,IAAI,CAAC;cAAEvD,KAAK,EAAEA,KAAK,CAACpY,IAAI;cAAE2D,KAAK,EAAEyU,KAAK,CAACpY;YAAI,CAAE,CAAC;UAChE,CAAC,CAAC;UACF,IAAI,IAAI,CAACiB,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,KAAK,GAAG,EAAE;YAC5D,IAAI,CAACF,iBAAiB,EAAE;UAC1B;QACF;MACF;KACD,CAAC;EACJ;EAEAmY,YAAYA,CAAC5S,SAAS;IACpB,IAAI,CAACgM,kBAAkB,CAACsJ,YAAY,CAACtV,SAAS,CAAC,CAACkV,SAAS,CAAC;MACxDC,IAAI,EAAGC,QAAwB,IAAI;QACjCA,QAAQ,EAAEd,OAAO,CAACiB,QAAQ,IAAG;UAC3B,IAAI,CAACpa,SAAS,CAACka,IAAI,CAACE,QAAQ,CAAC;QAC/B,CAAC,CAAC;QAEF,MAAMC,aAAa,GAAG,IAAI,CAACjX,YAAY,CAACgR,UAAU,EAAE2D,IAAI,CAACuC,SAAS,IAAG;UAAG,OAAOA,SAAS,CAACC,SAAS,KAAKxgB,aAAa,CAACygB,QAAQ;QAAE,CAAC,CAAC;QACjI,IAAIH,aAAa,EAAE;UACjB,IAAI,CAACjW,0BAA0B,GAAG,IAAI,CAACpE,SAAS,CAAC+X,IAAI,CAACqC,QAAQ,IAAG;YAC/D,OAAOC,aAAa,CAACI,QAAQ,KAAKL,QAAQ,CAACtI,gBAAgB;UAC7D,CAAC,CAAC;QACJ;MACF,CAAC;MACDiH,KAAK,EAAE2B,GAAG,IAAG;QACX,MAAMA,GAAG;MACX;KACD,CAAC;EACJ;EAEA/C,yBAAyBA,CAAA;IACvB,IAAI,CAACiC,YAAY,GAAG,IAAI,CAACnI,oBAAoB,CAACkJ,SAAS,CAACZ,SAAS,CAACa,KAAK,IAAG;MACxE,IAAI,CAACrE,sBAAsB,GAAGqE,KAAK,CAACC,IAAI,IAAI,EAAE;MAC9C,IAAI,CAAC/c,eAAe,GAAG,CAAC;QAAEoE,KAAK,EAAE,MAAM;QAAEyU,KAAK,EAAE;MAAM,CAAE,CAAC;MACzD,IAAI,CAAC7Y,eAAe,CAACoc,IAAI,CAAC,GAAG,IAAI,CAAC3D,sBAAsB,CAACuE,GAAG,CAACC,GAAG,IAAG;QAAG,OAAO;UAAE7Y,KAAK,EAAE6Y,GAAG,CAACC,kBAAkB;UAAErE,KAAK,EAAEoE;QAAG,CAAE;MAAC,CAAC,CAAC,CAAC;MAE9H,IAAI,CAACtJ,oBAAoB,CAACwJ,gBAAgB,CAAC,IAAI,CAAC7X,YAAY,CAAC6P,QAAQ,CAAC;IACxE,CAAC,CAAC;IAEF,IAAI,CAACyG,WAAW,GAAG,IAAI,CAACjI,oBAAoB,CAACyJ,QAAQ,CAACnB,SAAS,CAACa,KAAK,IAAG;MACtE,IAAI,CAAChe,WAAW,GAAG,IAAI,CAAC2Z,sBAAsB,CAACwB,IAAI,CAACgD,GAAG,IAAIA,GAAG,CAACvT,EAAE,KAAKoT,KAAK,CAACC,IAAI,CAAC;MAEjF,IAAI,CAAC,IAAI,CAACje,WAAW,EAAE;QACrB,IAAI,CAACA,WAAW,GAAG,IAAI,CAACkB,eAAe,CAAC,CAAC,CAAC,CAAC6Y,KAAK;MAClD;IACF,CAAC,CAAC;EACJ;EAEAwE,mBAAmBA,CAAA;IACjB,IAAI,CAACpJ,gBAAgB,GAAG,IAAI;EAC9B;EAEAqJ,cAAcA,CAAA;IACZ,IAAI,CAACpJ,iBAAiB,GAAG,IAAI;EAC/B;EAEMqJ,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAArE,iBAAA;MACf,IAAIqE,MAAI,CAACzd,eAAe,EAAE;QACxB;MACF;MACA,IAAIyd,MAAI,CAAClY,YAAY,CAAC8P,UAAU,CAACtK,MAAM,KAAK,CAAC,EAAE;QAC7C0S,MAAI,CAACvK,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAE,0BAA0B,CAAC;QAC9D;MACF;MAEA,IAAID,MAAI,CAAClY,YAAY,CAAC+P,WAAW,CAACvK,MAAM,KAAK,CAAC,EAAE;QAC9C0S,MAAI,CAACvK,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAE,2BAA2B,CAAC;QAC/D;MACF;MAEA;MACA,IAAID,MAAI,CAAClY,YAAY,CAACwO,UAAU,KAAKjY,UAAU,CAACme,cAAc,IAAIwD,MAAI,CAACvW,4BAA4B,EAAE;QACnGuW,MAAI,CAACvF,iBAAiB,GAAGuF,MAAI,CAACvW,4BAA4B,CAAC6D,MAAM;QAEjE,IAAI,CAAC0S,MAAI,CAACE,kCAAkC,EAAE,EAAE;UAC9C;QACF;MACF;MAEA,IAAIF,MAAI,CAACvF,iBAAiB,GAAGuF,MAAI,CAAClY,YAAY,CAACyT,cAAc,EAAE;QAC7DyE,MAAI,CAACvK,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAE,uCAAuC,GAAGD,MAAI,CAAClY,YAAY,CAACyT,cAAc,GAC3G,+CAA+C,CAAC;QAClD;MACF,CAAC,MAAM,IAAIyE,MAAI,CAACvF,iBAAiB,KAAK,CAAC,EAAE;QACvC;QACAuF,MAAI,CAACvK,UAAU,CAAC0K,WAAW,CAAC,SAAS,EAAE,iEAAiE,GACpGH,MAAI,CAAClY,YAAY,CAACyT,cAAc,GAAG,WAAW,CAAC;MACrD;MACA,MAAM6E,aAAa,GAAGJ,MAAI,CAACta,gBAAgB,EAAEL,MAAM,KAAK2a,MAAI,CAAClY,YAAY,CAACuY,iBAAiB;MAC3F,MAAMC,uBAAuB,GAAGN,MAAI,CAAC1c,qBAAqB,EAAEP,GAAG,KAAKid,MAAI,CAAClY,YAAY,CAAC4P,cAAc;MACpGsI,MAAI,CAAClY,YAAY,CAACuY,iBAAiB,GACjCL,MAAI,CAAC1c,qBAAqB,EAAEP,GAAG,KAAK5E,cAAc,CAACwe,SAAS,IAAI,CAACqD,MAAI,CAACta,gBAAgB,IAAIsa,MAAI,CAACta,gBAAgB,CAACL,MAAM,KAAK,GAAG,GAC1H,IAAI,GACJ2a,MAAI,CAACta,gBAAgB,CAACL,MAAM;MAClC2a,MAAI,CAAClY,YAAY,CAACyQ,gBAAgB,CAACC,YAAY,GAAGwH,MAAI,CAACvb,gBAAgB;MACvEub,MAAI,CAAClY,YAAY,CAACsQ,eAAe,CAAC2E,UAAU,GAAGiD,MAAI,CAAC9b,iBAAiB;MACrE8b,MAAI,CAAClY,YAAY,CAACsQ,eAAe,CAACE,cAAc,GAAG0H,MAAI,CAAC5b,gBAAgB;MAExE4b,MAAI,CAAClY,YAAY,CAACiC,aAAa,GAAGiW,MAAI,CAACpW,qBAAqB;MAC5DoW,MAAI,CAAClY,YAAY,CAACyC,SAAS,GAAGyV,MAAI,CAAC5V,iBAAiB;MACpD4V,MAAI,CAAClY,YAAY,CAACwO,UAAU,GAAG0J,MAAI,CAACnd,gBAAgB;MACpDmd,MAAI,CAAClY,YAAY,CAAC4P,cAAc,GAAGsI,MAAI,CAAC1c,qBAAqB,EAAEP,GAAG;MAElEid,MAAI,CAAClY,YAAY,CAACqP,cAAc,GAAG6I,MAAI,CAAC7I,cAAc;MACtD6I,MAAI,CAAClY,YAAY,CAACiR,iBAAiB,GAAGiH,MAAI,CAACpD,yBAAyB;MACpEoD,MAAI,CAAClY,YAAY,CAAC0R,QAAQ,GAAGwG,MAAI,CAAC/Z,iBAAiB,EAAEhD,IAAI;MAEzD+c,MAAI,CAAClY,YAAY,CAACgR,UAAU,GAAGkH,MAAI,CAAClX,0BAA0B,GAAG,CAAC;QAChEmW,SAAS,EAAExgB,aAAa,CAACygB,QAAQ;QAAEqB,UAAU,EAAE7hB,cAAc,CAAC8hB,QAAQ;QACtErB,QAAQ,EAAEa,MAAI,CAAClX,0BAA0B,EAAE0N,gBAAgB;QAAEiK,gBAAgB,EAAET,MAAI,CAAClX,0BAA0B,EAAE7F;OACjH,CAAC,GAAG,EAAE;MAEP,IAAI,CAAC+c,MAAI,CAACU,oBAAoB,EAAE,IAAKV,MAAI,CAACW,gBAAgB,IAAIX,MAAI,CAAClY,YAAY,CAACyD,kBAAkB,IAAI,QAAQyU,MAAI,CAACY,oBAAoB,EAAE,CAAE,EAAE;QAAE;MAAQ;MAGvJ,IAAIC,UAAU,GAAG,IAAI;MACrB,IAAIC,eAAe,GAAG,IAAI;MAC1B,IAAIC,eAAe,GAAG,CAAC;MAEvBf,MAAI,CAAC5F,OAAO,CAACyD,OAAO,CAACmD,OAAO,IAAG;QAC7B,IAAIA,OAAO,CAACjT,QAAQ,EAAE;UACpB,MAAMA,QAAQ,GAAGiS,MAAI,CAAChS,UAAU,CAACyO,IAAI,CAACwE,CAAC,IAAIA,CAAC,CAACrH,IAAI,CAACsH,QAAQ,EAAE,KAAKF,OAAO,CAACjT,QAAQ,CAAC;UAElF,IAAIiT,OAAO,CAACjT,QAAQ,KAAK,KAAK,IAAIiT,OAAO,CAACjT,QAAQ,KAAK,KAAK,IAAIiT,OAAO,CAACjT,QAAQ,KAAK,KAAK,EAAE;YAC1FiT,OAAO,CAACG,cAAc,GAAGpT,QAAQ,CAACqT,aAAa;UACjD,CAAC,MAAM;YACLJ,OAAO,CAACG,cAAc,GAAGH,OAAO,CAACjT,QAAQ,GAAG,GAAG,GAAGA,QAAQ,CAACqT,aAAa;UAC1E;QACF;QAEA,IAAIJ,OAAO,CAAC7S,MAAM,KAAK,MAAM,EAAE;UAC7B4S,eAAe,IAAI,CAAC;QACtB;MACF,CAAC,CAAC;MAEF,IAAIf,MAAI,CAACvF,iBAAiB,GAAGsG,eAAe,GAAGf,MAAI,CAAClY,YAAY,CAACyT,cAAc,EAAE;QAC/EyE,MAAI,CAACvK,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAE,sDAAsD,GAAGD,MAAI,CAAClY,YAAY,CAACyT,cAAc,GAC1H,2HAA2H,CAAC;QAC9H;MACF;MAEA,MAAM8F,UAAU,GAAGrB,MAAI,CAAC5F,OAAO;MAC/B,MAAMkH,gBAAgB,GAAGC,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE,GAAGH,UAAU,CAAC7B,GAAG,CAAEiC,CAAC,KAAM;QAAE,CAACA,CAAC,CAACxe,IAAI,GAAGwe;MAAC,CAAE,CAAC,CAAC,CAAC;MACvFzB,MAAI,CAAClY,YAAY,CAACC,YAAY,CAACsR,MAAM,GAAGiI,gBAAgB;MAExD,IAAItB,MAAI,CAAC1c,qBAAqB,EAAEP,GAAG,KAAK5E,cAAc,CAACujB,SAAS,KACzD7jB,SAAS,CAAC8jB,eAAe,CAACtJ,SAAS,KAAK2H,MAAI,CAAC9b,iBAAiB,IAC7DrG,SAAS,CAAC8jB,eAAe,CAACrJ,cAAc,KAAK0H,MAAI,CAAC5b,gBAAiB,IACnEvG,SAAS,CAAC8jB,eAAe,CAAC5X,aAAa,KAAKiW,MAAI,CAACpW,qBAAsB,IACvE/L,SAAS,CAAC8jB,eAAe,CAACpX,SAAS,KAAKyV,MAAI,CAAC5V,iBAAkB,CAAC,IACnEgW,aAAa,IAAIE,uBAAuB,EAAE;QAC7CN,MAAI,CAAClY,YAAY,CAAC8Z,MAAM,EAAE/D,OAAO,CAAC4D,CAAC,IAAG;UAAGA,CAAC,CAACI,MAAM,GAAG,QAAQ;QAAE,CAAC,CAAC;MAClE;MAEA,IAAI7B,MAAI,CAAC7I,cAAc,EAAE;QACvB0J,UAAU,GAAGb,MAAI,CAAC9I,YAAY,GAAG,IAAI,GAAG,KAAK;MAC/C,CAAC,MAAM;QACL8I,MAAI,CAAC9I,YAAY,GAAG,GAAG;MACzB;MAEA,IAAI8I,MAAI,CAAClY,YAAY,CAACwO,UAAU,KAAKjY,UAAU,CAACyjB,KAAK,EAAE;QACrD9B,MAAI,CAAClY,YAAY,CAACgF,SAAS,GAAGkT,MAAI,CAAClT,SAAS;QAC5CkT,MAAI,CAAClY,YAAY,CAAC4E,YAAY,GAAGsT,MAAI,CAACtT,YAAY;QAClDsT,MAAI,CAAClY,YAAY,CAACqF,kBAAkB,GAAG6S,MAAI,CAAC7S,kBAAkB;MAChE,CAAC,MACI;QACH6S,MAAI,CAAClY,YAAY,CAACgF,SAAS,GAAG,IAAI;QAClCkT,MAAI,CAAClY,YAAY,CAAC4E,YAAY,GAAG,IAAI;QACrCsT,MAAI,CAAClY,YAAY,CAACqF,kBAAkB,GAAG,IAAI;MAC7C;MACA,IAAI6S,MAAI,CAAClY,YAAY,CAACwO,UAAU,KAAKjY,UAAU,CAACyjB,KAAK,EAAE;QACrD,IAAI9B,MAAI,CAAClY,YAAY,CAACgF,SAAS,EAAE;UAC/BgU,eAAe,GAAG,IAAI;UAEtB,IAAId,MAAI,CAAClY,YAAY,CAACgF,SAAS,KAAK,MAAM,IACrCkT,MAAI,CAAClY,YAAY,CAACgF,SAAS,KAAK,WAAW,IAC3CkT,MAAI,CAAClY,YAAY,CAACgF,SAAS,KAAK,SAAS,EAAE;YAC9CgU,eAAe,GAAId,MAAI,CAAClY,YAAY,CAAC4E,YAAY,GAAG,IAAI,GAAG,KAAM;UACnE;QACF,CAAC,MAAM;UACLoU,eAAe,GAAG,KAAK;QACzB;MACF;MAEA,IAAId,MAAI,CAAChO,aAAa,CAAC1E,MAAM,GAAG,CAAC,IAAI0S,MAAI,CAAChO,aAAa,CAAC,CAAC,CAAC,CAAChB,aAAa,KAAK,EAAE,EAAE;QAC/EgP,MAAI,CAAClY,YAAY,CAACoR,gBAAgB,GAAG8G,MAAI,CAAChO,aAAa;MACzD;MAEA,IAAI6O,UAAU,IAAIC,eAAe,EAAE;QACjC,IAAId,MAAI,CAAC9I,YAAY,EAAE;UACrB8I,MAAI,CAAClY,YAAY,CAAC2Q,aAAa,GAAGuH,MAAI,CAAC5I,IAAI,CAACqF,IAAI,CAACwE,CAAC,IAAIA,CAAC,CAAC/U,EAAE,CAACgV,QAAQ,EAAE,KAAKlB,MAAI,CAAC9I,YAAY,CAAC;QAC9F;QACA8I,MAAI,CAAC3I,cAAc,GAAG,IAAI;QAC1B2I,MAAI,CAACzK,kBAAkB,CAACwM,iBAAiB,CAAC/B,MAAI,CAACzW,SAAS,EAAEyW,MAAI,CAACxW,gBAAgB,EAAEwW,MAAI,CAAClY,YAAY,CAAC,CAAC2W,SAAS,CAAC;UAC5GC,IAAI,EAAGC,QAAQ,IAAI;YACjBqB,MAAI,CAAClY,YAAY,GAAG6W,QAAQ;YAC5BqB,MAAI,CAACzK,kBAAkB,CAACyM,yBAAyB,CAACtD,IAAI,CAAC;cAAEuD,QAAQ,EAAEjC,MAAI,CAACta,gBAAgB;cAAEwc,SAAS,EAAElC,MAAI,CAAC/Z;YAAiB,CAAE,CAAC;YAC9H+Z,MAAI,CAAC5J,eAAe,CAAC+L,IAAI,CAACnC,MAAI,CAAClY,YAAY,CAAC;YAC5CkY,MAAI,CAACpC,mBAAmB,CAACoC,MAAI,CAAClY,YAAY,CAAC;YAC3C,IAAIkY,MAAI,CAACrF,qBAAqB,EAAE;cAC9BqF,MAAI,CAACvK,UAAU,CAAC2M,WAAW,CAAC,SAAS,EAAEpC,MAAI,CAACrF,qBAAqB,CAAC;cAClEqF,MAAI,CAACrF,qBAAqB,GAAG,EAAE;YACjC,CAAC,MACI;cACHqF,MAAI,CAACvK,UAAU,CAAC2M,WAAW,CAAC,SAAS,EAAE,oBAAoB,EAAE,KAAK,EAAE,IAAI,CAAC;YAC3E;YACApC,MAAI,CAAC3I,cAAc,GAAG,KAAK;YAC3B2I,MAAI,CAAChV,QAAQ,GAAG,KAAK;YACrBnN,SAAS,CAACwkB,aAAa,GAAGrC,MAAI,CAACsC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;YACjDtC,MAAI,CAAC7J,oBAAoB,CAACuI,IAAI,CAAC;cAC7B6D,WAAW,EAAEvC,MAAI,CAAC7J,oBAAoB,CAACqM,cAAc;cACrDC,SAAS,EAAEzC,MAAI,CAAC7J,oBAAoB,CAACuM,iBAAiB;cACtDzD,SAAS,EAAEphB,SAAS,CAAC8kB,SAAS,CAACC,eAAe;cAC9CrD,IAAI,EAAE;aACP,CAAC;YAEFS,MAAI,CAAC6C,iCAAiC,CAAC7C,MAAI,CAAC1e,WAAW,CAAC;UAC1D,CAAC;UACDmc,KAAK,EAAE2B,GAAG,IAAG;YACXY,MAAI,CAAC3I,cAAc,GAAG,KAAK;YAC3B,MAAM+H,GAAG;UACX;SACD,CAAC;QAEF,IAAGY,MAAI,CAAC5E,iBAAiB,EAAC;UACxB4E,MAAI,CAAC7N,gBAAgB,CAAC5I,SAAS,GAAGyW,MAAI,CAACzW,SAAS;UAChDyW,MAAI,CAAC7N,gBAAgB,CAAC3I,gBAAgB,GAAGwW,MAAI,CAACxW,gBAAgB;UAC9DwW,MAAI,CAAC7N,gBAAgB,CAAC2Q,WAAW,GAAG9C,MAAI,CAAC/Z,iBAAiB,EAAEiG,EAAE;UAC9D8T,MAAI,CAACzK,kBAAkB,CAACwN,oBAAoB,CAAC/C,MAAI,CAAClY,YAAY,CAAC6P,QAAQ,EAAEqI,MAAI,CAAClY,YAAY,CAACwO,UAAU,EAAE0J,MAAI,CAAC7N,gBAAgB,CAAC,CAACsM,SAAS,CAAC;YACtIC,IAAI,EAAGC,QAAQ,IAAI;cACjB,IAAI,CAACA,QAAQ,EAAE;gBACbqB,MAAI,CAACvK,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAE,qCAAqC,CAAC;cAC3E;YACF,CAAC;YACDxC,KAAK,EAAE2B,GAAG,IAAG;cACXY,MAAI,CAAC3I,cAAc,GAAG,KAAK;cAC3B2I,MAAI,CAACvK,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAE,qCAAqC,CAAC;YAC3E;WAED,CAAC;QACJ;MACF,CAAC,MACI;QACH,IAAI,CAACY,UAAU,EAAE;UACfb,MAAI,CAACvK,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAE,wBAAwB,CAAC;QAC9D,CAAC,MACI,IAAI,CAACa,eAAe,EAAE;UACzBd,MAAI,CAACvK,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAE,mCAAmC,CAAC;QACzE;MACF;IAAC;EACH;EAEAC,kCAAkCA,CAAA;IAChC,IAAI,CAACpY,YAAY,CAACC,YAAY,CAACib,2BAA2B,GAAG,IAAI,CAACvZ,4BAA4B;IAC9F;IACA,MAAMwZ,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAACpb,YAAY,CAACC,YAAY,CAACib,2BAA2B,CAAC;IAC7G,IAAIC,gBAAgB,CAAC3V,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAACmI,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAE,uBAAuBgD,gBAAgB,CAAC,CAAC,CAAC,CAAC9U,MAAM,CAAC1H,IAAI,UAAU,CAAC;MACpG,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;EAEAyc,mBAAmBA,CAACC,0BAAwD;IAC1E,OAAOA,0BAA0B,EAAEC,MAAM,CAACC,YAAY,IAAI,IAAI,CAACC,aAAa,CAACD,YAAY,CAAC,IAAI,IAAI,CAACE,SAAS,CAACF,YAAY,CAAC,CAAC;EAC7H;EAEAC,aAAaA,CAACD,YAAwC;IACpD,OAAO,CAAEA,YAAY,CAAClV,MAAM,CAAC1H,IAAI,KAAKnI,wBAAwB,CAACklB,QAAQ,IAAIH,YAAY,CAAClV,MAAM,CAACQ,QAAQ,KAAK9P,aAAa,CAAC4kB,MAAM,IAC3HJ,YAAY,CAAClV,MAAM,CAAC1H,IAAI,KAAKnI,wBAAwB,CAAColB,MAAM,IAC3DL,YAAY,CAAClV,MAAM,CAAC1H,IAAI,KAAKnI,wBAAwB,CAACmlB,MAAM,IAAIJ,YAAY,CAACM,QAAS,MACtF,CAACN,YAAY,CAACO,MAAM,IAAIP,YAAY,CAACO,MAAM,CAACtW,MAAM,KAAK,CAAC,CAAC;EACjE;EAEAiW,SAASA,CAACF,YAAwC;IAChD,OAAOA,YAAY,CAAClV,MAAM,CAAC1H,IAAI,KAAKnI,wBAAwB,CAACulB,IAAI,IAAI,CAACR,YAAY,CAACS,IAAI,EAAE5S,QAAQ;EACnG;EAEM0P,oBAAoBA,CAAA;IAAA,IAAAmD,MAAA;IAAA,OAAApI,iBAAA;MACxB,MAAMqI,UAAU,GAAGD,MAAI,CAACre,gBAAgB,CAACL,MAAM,KAAK,GAAG,GAAG,MAAM,SAAS0e,MAAI,CAAC7N,aAAa,CAAC+N,UAAU,CAACF,MAAI,CAACre,gBAAgB,CAAC2U,aAAa,EAAE6J,IAAI,EAAE,EAAEhD,QAAQ,EAAE,CAAC;MAC/J,IAAI6C,MAAI,CAACjc,YAAY,CAACqD,kBAAkB,CAAC6Y,UAAU,KAAKA,UAAU,EAAE;QAClED,MAAI,CAACtO,UAAU,CAACwK,SAAS,CAAC,kEAAkE,CAAC;QAC7F8D,MAAI,CAACjc,YAAY,CAACqD,kBAAkB,CAAC6Y,UAAU,SAASD,MAAI,CAAC7N,aAAa,CAAC+N,UAAU,CAACF,MAAI,CAACre,gBAAgB,CAAC2U,aAAa,EAAE6J,IAAI,EAAE,EAAEhD,QAAQ,EAAE,CAAC;QAC9I,OAAO,KAAK;MACd;MAEA,IAAI6C,MAAI,CAACjc,YAAY,CAACqD,kBAAkB,CAACgZ,YAAY,KAAKJ,MAAI,CAACjc,YAAY,CAACqD,kBAAkB,CAACiZ,UAAU,EAAE;QACzGL,MAAI,CAACtO,UAAU,CAACwK,SAAS,CAAC,8GAA8G,CAAC;QACzI,OAAO,KAAK;MACd;MAEA,IAAI8D,MAAI,CAACjc,YAAY,CAACqD,kBAAkB,CAACkZ,QAAQ,EAAE/W,MAAM,KAAK,CAAC,EAAE;QAC/DyW,MAAI,CAACtO,UAAU,CAACwK,SAAS,CAAC,uFAAuF,CAAC;QAClH,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;IAAC;EACd;EAEAS,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAC5Y,YAAY,EAAEC,YAAY,EAAEC,gBAAgB,EAAEvB,IAAI,KAAKrI,iBAAiB,CAACkmB,YAAY,IACzF,CAAC,IAAI,CAACxc,YAAY,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,OAAO,EAAE;MAChE,IAAI,CAACwN,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAE,+EAA+E,CAAC;MACnH,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb;EAEAhD,QAAQA,CAAC1T,SAAS;IAChB,IAAI,CAACgM,kBAAkB,CAAC0H,QAAQ,CAAC1T,SAAS,CAAC,CAACkV,SAAS,CAAC;MACpDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACvH,IAAI,GAAGuH,QAAQ;QACpB,KAAK,MAAMvH,IAAI,IAAIuH,QAAQ,EAAE;UAC3B,IAAI,CAAC1H,KAAK,CAAC2H,IAAI,CACb;YACEhY,KAAK,EAAEwQ,IAAI,CAACmN,SAAS,CAACC,IAAI,EAAE,GAAG,GAAG,GAAGpN,IAAI,CAACqN,QAAQ,CAACD,IAAI,EAAE;YACzDnJ,KAAK,EAAEjE,IAAI,CAAClL,EAAE,CAACgV,QAAQ;WACxB,CACF;QACH;QACA,IAAI,CAACjK,KAAK,CAACiN,IAAI,CAAC,CAACzC,CAAC,EAAEiD,CAAC,KAAKjD,CAAC,CAAC7a,KAAK,CAAC+d,aAAa,CAACD,CAAC,CAAC9d,KAAK,CAAC,CAAC;QAEzD,IAAI,IAAI,CAACkB,YAAY,CAAC2Q,aAAa,EAAE;UACnC,IAAI,CAACvB,YAAY,GAAG,IAAI,CAACpP,YAAY,CAAC2Q,aAAa,CAACvM,EAAE,GAAG,CAAC,GAAG,IAAI,CAACpE,YAAY,CAAC2Q,aAAa,CAACvM,EAAE,CAACgV,QAAQ,EAAE,GAAG,GAAG;QAClH;MACF,CAAC;MACDzD,KAAK,EAAE2B,GAAG,IAAG;QACX,MAAMA,GAAG;MACX;KACD,CAAC;EACJ;EAEAwF,eAAeA,CAACC,IAAI;IAClB,IAAI,IAAI,CAACtiB,eAAe,EAAE;MACxB;IACF;IACA,MAAMuiB,QAAQ,GAAIC,CAAiB,IAAI;MACrCA,CAAC,CAACC,aAAa,CAACC,OAAO,CAAC,YAAY,EAAGJ,IAAK,CAAC;MAC7CE,CAAC,CAACG,cAAc,EAAE;IACpB,CAAC;IACDC,QAAQ,CAACC,gBAAgB,CAAC,MAAM,EAAEN,QAAQ,CAAC;IAC3CK,QAAQ,CAACE,WAAW,CAAC,MAAM,CAAC;IAC5BF,QAAQ,CAACG,mBAAmB,CAAC,MAAM,EAAER,QAAQ,CAAC;IAC9C,IAAI,CAACrP,UAAU,CAAC2M,WAAW,CAAC,qBAAqB,EAAE,qBAAqB,CAAC;EAC3E;EAEAmD,aAAaA,CAAA;IACX,IAAI,CAAC7R,YAAY,GAAG,IAAI;EAC1B;EAEAF,YAAYA,CAAA;IACV,IAAI,IAAI,CAACjR,eAAe,EAAE;MACxB;IACF;IACA,IAAI,CAACgT,kBAAkB,CAAC/B,YAAY,CAAC,IAAI,CAAC1L,YAAY,CAAC6P,QAAQ,CAAC,CAAC8G,SAAS,CAAC;MACzEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAAClJ,UAAU,CAAC2M,WAAW,CAAC,SAAS,EAAE,sBAAsB,CAAC;UAC9DoD,MAAM,CAACC,QAAQ,CAACjE,MAAM,CAAC,sCAAsC,CAAC;QAChE,CAAC,MACI;UACH,IAAI,CAAC/L,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAE,gBAAgB,CAAC;QACtD;MACF,CAAC;MACDxC,KAAK,EAAE2B,GAAG,IAAG;QACX,IAAI,CAAC3J,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAEb,GAAG,CAAC;QACvC,MAAMA,GAAG;MACX;KACD,CAAC;IACF,IAAI,CAAC1L,YAAY,GAAG,KAAK;EAC3B;EAEA1P,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACzB,eAAe,EAAE;MACxB;IACF;IACA,IAAI,CAACgT,kBAAkB,CAACmQ,YAAY,CAAC,IAAI,CAAClc,gBAAgB,EAAE,IAAI,CAACtF,iBAAiB,CAAC,CAACua,SAAS,CAAC;MAC5FC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACra,SAAS,GAAG,EAAE;QACnB,IAAIqa,QAAQ,CAACrR,MAAM,GAAG,CAAC,EAAE;UACvBqR,QAAQ,CAACd,OAAO,CAACxC,KAAK,IAAG;YACvB,IAAI,CAAC/W,SAAS,CAACsa,IAAI,CAAC;cAAEvD,KAAK,EAAEA,KAAK,CAACtY,GAAG;cAAE6D,KAAK,EAAEyU,KAAK,CAACpY;YAAI,CAAE,CAAC;UAC9D,CAAC,CAAC;QACJ;QAEA,IAAI,IAAI,CAAC6E,YAAY,CAAC4R,oBAAoB,EAAE;UAC1C,MAAMiM,KAAK,GAAG,IAAI,CAACrhB,SAAS,CAACshB,SAAS,CAACnE,CAAC,IAAIA,CAAC,CAACpG,KAAK,KAAK,IAAI,CAACvT,YAAY,CAAC4R,oBAAoB,CAAC;UAE/F,IAAIiM,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAI,CAACrhB,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC8e,MAAM,CAACyB,IAAI,IAAIA,IAAI,CAACxJ,KAAK,KAAK,IAAI,CAACvT,YAAY,CAAC4R,oBAAoB,CAAC;UACvG;QACF;QAEA,IAAI,IAAI,CAAC5R,YAAY,CAACqR,gBAAgB,EAAE;UACtC,MAAMwM,KAAK,GAAG,IAAI,CAACrhB,SAAS,CAACshB,SAAS,CAACnE,CAAC,IAAIA,CAAC,CAACpG,KAAK,KAAK,IAAI,CAACvT,YAAY,CAACqR,gBAAgB,CAAC;UAE3F,IAAIwM,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAI,CAAC7a,sBAAsB,GAAG,IAAI;YAClC,IAAI,CAACxG,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC8e,MAAM,CAACyB,IAAI,IAAIA,IAAI,CAACxJ,KAAK,KAAK,IAAI,CAACvT,YAAY,CAACqR,gBAAgB,CAAC;UACnG,CAAC,MAAM;YACL,IAAI,CAACrO,sBAAsB,GAAG,KAAK;UACrC;QACF;QAEA,IAAI,IAAI,CAAChD,YAAY,CAACqR,gBAAgB,EAAE;UACtC,MAAMwM,KAAK,GAAG,IAAI,CAACrhB,SAAS,CAACshB,SAAS,CAACnE,CAAC,IAAIA,CAAC,CAACpG,KAAK,KAAK,IAAI,CAACvT,YAAY,CAAC+d,aAAa,CAAC;UAExF,IAAIF,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAI,CAAC5a,mBAAmB,GAAG,IAAI;YAC/B,IAAI,CAACzG,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC8e,MAAM,CAACyB,IAAI,IAAIA,IAAI,CAACxJ,KAAK,KAAK,IAAI,CAACvT,YAAY,CAAC+d,aAAa,CAAC;UAChG,CAAC,MAAM;YACL,IAAI,CAAC9a,mBAAmB,GAAG,KAAK;UAClC;QACF;QAEA,IAAI,CAAC3G,gBAAgB,GAAG,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC+W,KAAK;QAC/C,IAAI,IAAI,CAACvQ,sBAAsB,EAAE;UAC/B,IAAI,CAACgb,kBAAkB,EAAE;QAC3B;MACF;KACD,CAAC;EACJ;EAEAriB,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAAClB,eAAe,EAAE;MACxB;IACF;IACA,IAAI,CAACwjB,0BAA0B,EAAE;EACnC;EAEA7S,UAAUA,CAACoM,KAAK;IACd,IAAI,IAAI,CAAC/c,eAAe,EAAE;MACxB;IACF;IACA,MAAMyjB,QAAQ,GAAI1G,KAAK,CAAC2G,KAAK,GAAI3G,KAAK,CAAC2G,KAAK,GAAG3G,KAAK,CAAC4G,OAAO;IAC5D,IAAIF,QAAQ,GAAG,EAAE,KAAKA,QAAQ,GAAG,EAAE,IAAIA,QAAQ,GAAG,EAAE,CAAC,EAAE;MACrD,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EAEb;EAEA5S,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC7Q,eAAe,EAAE;MACxB;IACF;IACA,MAAM4jB,yBAAyB,GAAG,IAAI,CAACvJ,yBAAyB;IAChE,IAAIuJ,yBAAyB,IAAI,EAAE,EAAE;MACnC;IAAA,CACD,MAAM,IAAIA,yBAAyB,GAAG,EAAE,IAAIA,yBAAyB,IAAI,CAAC,EAAE;MAC3E,IAAI,CAACC,wBAAwB,CAACD,yBAAyB,CAAC;IAC1D,CAAC,MAAM;MACL,IAAI,CAAC3L,YAAY,GAAG,IAAI;IAC1B;EACF;EAEA4L,wBAAwBA,CAACD,yBAAyB;IAChD,IAAI,CAACzQ,mBAAmB,CAAC2Q,OAAO,CAAC;MAC/BC,OAAO,EAAE,wLAAwL;MACjMC,MAAM,EAAE,wCAAwC;MAChDC,IAAI,EAAE,4BAA4B;MAClCC,sBAAsB,EAAE,iCAAiC;MACzDC,sBAAsB,EAAE,iCAAiC;MACzDC,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAAC/J,yBAAyB,GAAGuJ,yBAAyB;MAC5D,CAAC;MACDS,MAAM,EAAEA,CAAA,KAAK;QACX,IAAI,CAAChK,yBAAyB,GAAG,GAAG;QACpC,IAAI,CAACjH,QAAQ,CAACkR,iBAAiB,CAAC,oBAAoB,CAAC,CAACC,KAAK,EAAE;MAC/D;KACD,CAAC;EACJ;EAEAC,SAASA,CAAA;IACP,IAAI,CAACvM,YAAY,GAAG,KAAK;IACzB,IAAI,CAACoC,yBAAyB,GAAG,EAAE;EACrC;EAEA5P,aAAaA,CAAA;IACX,IAAI,IAAI,CAACzK,eAAe,EAAE;MACxB;IACF;IAEA,MAAMya,IAAI,GAAG,IAAI,CAAC5P,UAAU,CAACqP,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3Z,GAAG,KAAK,IAAI,CAAC+J,SAAS,CAAC;IAChE,IAAI,CAACH,aAAa,GAAGqQ,IAAI,CAAC1C,QAAQ;IAClC,IAAI,CAACnN,kBAAkB,GAAG6P,IAAI,CAAC3D,MAAM;IACrC,IAAI,IAAI,CAAC1M,aAAa,EAAEW,MAAM,EAAE;MAC9B,IAAI,CAACZ,YAAY,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAAC5J,GAAG;IAC/C;EACF;EAEAiZ,oBAAoBA,CAAA;IAClB,IAAI,CAACzG,kBAAkB,CAACyG,oBAAoB,EAAE,CAACyC,SAAS,CAAC;MACvDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,EAAE;UACZ,MAAMqI,WAAW,GAAGrI,QAAQ,CAAClC,IAAI,CAACwK,CAAC,IAAIA,CAAC,CAAChkB,IAAI,KAAKpF,SAAS,CAACc,WAAW,CAACuoB,iBAAiB,CAAC;UAC1F,IAAIF,WAAW,EAAE;YACf,IAAI,CAACzM,0BAA0B,GAAG,IAAI;YACtC,IAAI,CAACpX,UAAU,CAACyb,IAAI,CAAC;cAAE3b,IAAI,EAAE,OAAO;cAAEF,GAAG,EAAE;YAAO,CAAE,CAAC;UACvD;QACF;MAEF,CAAC;MACD0a,KAAK,EAAE2B,GAAG,IAAG;QACX,MAAMA,GAAG;MACX;KACD,CAAC;EACJ;EAEA;EACMnD,eAAeA,CAAA;IAAA,IAAAkL,MAAA;IAAA,OAAAxL,iBAAA;MACnB,MAAMyL,YAAY,SAASD,MAAI,CAACnR,kBAAkB,CAACqR,eAAe,EAAE;MAEpEF,MAAI,CAACG,iBAAiB,GAAGF,YAAY,CAAClS,IAAI,CAACuM,CAAC,IAAIA,CAAC,CAACxe,IAAI,KAAKtE,WAAW,CAAC4oB,cAAc,CAAC;MACtFJ,MAAI,CAACK,cAAc,GAAGJ,YAAY,CAAClS,IAAI,CAACuM,CAAC,IAAIA,CAAC,CAACxe,IAAI,KAAKtE,WAAW,CAAC4oB,cAAc,CAAC,CAAC,CAAC;MACrFJ,MAAI,CAACjM,YAAY,GAAGkM,YAAY,CAAClS,IAAI,CAACuM,CAAC,IAAIA,CAAC,CAACxe,IAAI,KAAKtE,WAAW,CAAC8oB,cAAc,CAAC;MACjFN,MAAI,CAACxG,gBAAgB,GAAGyG,YAAY,CAAClS,IAAI,CAACuM,CAAC,IAAIA,CAAC,CAACxe,IAAI,KAAKtE,WAAW,CAAC+oB,YAAY,CAAC;MACnFP,MAAI,CAAChb,oBAAoB,GAAGib,YAAY,CAAClS,IAAI,CAACuM,CAAC,IAAIA,CAAC,CAACxe,IAAI,KAAKtE,WAAW,CAACgpB,gBAAgB,CAAC;MAC3FR,MAAI,CAAC/L,iBAAiB,GAAGgM,YAAY,CAAClS,IAAI,CAACuM,CAAC,IAAIA,CAAC,CAACxe,IAAI,KAAKtE,WAAW,CAACipB,aAAa,CAAC;IAAC;EACxF;EAEAxL,aAAaA,CAAC7S,SAAS,EAAEC,gBAAgB;IACvC,IAAI,CAACgM,iBAAiB,CAAC4G,aAAa,CAAC7S,SAAS,EAAEC,gBAAgB,CAAC,CAACqe,IAAI,CAAC3pB,IAAI,CAAC,CAAC,CAAC,CAAC,CAACugB,SAAS,CAACpY,UAAU,IAAG;MACrG,IAAI,CAACA,UAAU,GAAGA,UAAU;MAC5B,IAAI,IAAI,CAACyB,YAAY,CAAC4P,cAAc,KAAKvZ,cAAc,CAACwe,SAAS,IAAI,IAAI,CAAC7U,YAAY,CAAC0R,QAAQ,EAAE;QAC/F,IAAI,CAACvT,iBAAiB,GAAG,IAAI,CAACI,UAAU,EAAEoW,IAAI,CAACgF,CAAC,IAAIA,CAAC,CAACxe,IAAI,KAAK,IAAI,CAAC6E,YAAY,CAAC0R,QAAQ,CAAC;QAC1F,IAAI,CAACsO,aAAa,GAAG,IAAI,CAAC7hB,iBAAiB;QAC3C,IAAI,CAAC8hB,eAAe,EAAE;QACtB,IAAI,CAAC,IAAI,CAAC9hB,iBAAiB,EAAE;UAC3B,IAAI,CAACwP,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAE,sBAAsB,CAAC;QAC5D;QAEA,IAAI,CAACzY,kBAAkB,EAAE;MAC3B;IACF,CAAC,CAAC;EACJ;EAEA8a,YAAYA,CAAC0F,GAAG,EAAEC,GAAG;IACnB,OAAOC,IAAI,CAACC,MAAM,EAAE,IAAIF,GAAG,GAAGD,GAAG,CAAC,GAAGA,GAAG;EAC1C;EAEAI,cAAcA,CAAA;IACZ,IAAI,CAAC7S,kBAAkB,CAAC8S,qBAAqB,CAAC,IAAI,CAACvgB,YAAY,CAAC6P,QAAQ,CAAC,CAAC8G,SAAS,CAAC;MAClFC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAKA,QAAQ,CAACvG,eAAe,CAACC,SAAS,KAAK,IAAI,CAACnU,iBAAiB,IAC/Dya,QAAQ,CAACvG,eAAe,CAACE,cAAc,KAAK,IAAI,CAAClU,gBAAiB,IAClEua,QAAQ,CAAC5U,aAAa,KAAK,IAAI,CAACH,qBAAsB,IACtD+U,QAAQ,CAACpU,SAAS,KAAK,IAAI,CAACH,iBAAkB,EAAE;UACjD,IAAI,CAACtC,YAAY,CAAC8Z,MAAM,EAAE/D,OAAO,CAAC4D,CAAC,IAAG;YACpCA,CAAC,CAACI,MAAM,GAAG,QAAQ;UACrB,CAAC,CAAC;QACJ;MACF,CAAC;MACDpE,KAAK,EAAE2B,GAAG,IAAG;QACX,MAAMA,GAAG;MACX;KACD,CAAC;EACJ;EAEAnQ,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAAC1M,eAAe,EAAE;MACxB;IACF;IAEA,IAAI,CAAC6M,qBAAqB,EAAEyO,OAAO,CAACoJ,CAAC,IAAG;MACtC,MAAMtB,KAAK,GAAG,IAAI,CAAC3T,aAAa,CAAC4T,SAAS,CAAC3E,CAAC,IAAIA,CAAC,KAAKgG,CAAC,CAAC;MACxD,IAAItB,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAAC3T,aAAa,CAACsW,MAAM,CAAC3C,KAAK,EAAE,CAAC,CAAC;MACrC;IACF,CAAC,CAAC;IACF,IAAI,CAACvW,qBAAqB,GAAG,EAAE;EACjC;EAEAyB,yBAAyBA,CAACC,OAAgB,EAAEgN,YAA0B;IACpE,IAAI,IAAI,CAACvb,eAAe,EAAE;MACxB;IACF;IAEA,IAAIuO,OAAO,EAAE;MACX,IAAI,CAAC1B,qBAAqB,CAACwP,IAAI,CAACd,YAAY,CAAC;IAC/C,CAAC,MAAM;MACL,MAAM6H,KAAK,GAAG,IAAI,CAACvW,qBAAqB,CAACwW,SAAS,CAACqB,CAAC,IAAIA,CAAC,KAAKnJ,YAAY,CAAC;MAC3E,IAAI6H,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAACvW,qBAAqB,CAACkZ,MAAM,CAAC3C,KAAK,EAAE,CAAC,CAAC;MAC7C;IACF;EACF;EAEAxW,MAAMA,CAAA;IACJ,IAAI,CAACwK,mBAAmB,GAAG;MACzB3I,aAAa,EAAE,EAAE;MACjBM,MAAM,EAAE,GAAG;MACXJ,QAAQ,EAAE,EAAE;MACZhB,eAAe,EAAE,CAAC,IAAIlS,MAAM,EAAE,CAAC;MAC/B4T,iBAAiB,EAAE;KACpB;IACD,IAAI,CAACI,aAAa,CAAC4M,IAAI,CAAC,IAAI,CAACjF,mBAAmB,CAAC;EACnD;EAEAnI,aAAaA,CAACsM,YAA0B;IACtC,IAAI,IAAI,CAACvb,eAAe,EAAE;MACxB;IACF;IACA,MAAMojB,KAAK,GAAG,IAAI,CAAC3T,aAAa,CAAC4T,SAAS,CAACnE,CAAC,IAAIA,CAAC,KAAK3D,YAAY,CAAC;IACnE,IAAI,CAAC9L,aAAa,CAACsW,MAAM,CAAC3C,KAAK,EAAE,CAAC,CAAC;IACnC,IAAI,CAAClQ,UAAU,CAAC2M,WAAW,CAAC,cAAc,CAAC;EAC7C;EAEA9R,eAAeA,CAACJ,eAAyB;IACvC,IAAI,IAAI,CAAC3N,eAAe,EAAE;MACxB;IACF;IACA2N,eAAe,CAAC0O,IAAI,CAAC,IAAI5gB,MAAM,EAAE,CAAC;EACpC;EAEAoS,kBAAkBA,CAACF,eAAyB,EAAEyV,KAAK;IACjD,IAAI,IAAI,CAACpjB,eAAe,EAAE;MACxB;IACF;IACA2N,eAAe,CAACoY,MAAM,CAAC3C,KAAK,EAAE,CAAC,CAAC;EAClC;EACA;EACAhW,oBAAoBA,CAACmO,YAA0B;IAC7C,IAAI,IAAI,CAACvb,eAAe,EAAE;MACxB;IACF;IAEA,IAAIgmB,aAAa,GAAGzK,YAAY,CAAC5M,QAAQ;IACzC,MAAM0Q,MAAM,GAAG2G,aAAa,CAACC,KAAK,CAAC,GAAG,CAAC;IACvC5G,MAAM,EAAE/D,OAAO,CAAC4D,CAAC,IAAG;MAClB,IAAIA,CAAC,CAACnU,MAAM,GAAG,CAAC,EAAE;QAChB,MAAMqY,KAAK,GAAGlE,CAAC,CAAC+G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAIzqB,cAAc,CAAC4nB,KAAK,CAAC,EAAE;UACzB,IAAI7H,YAAY,CAAC5N,eAAe,CAAC5C,MAAM,GAAGmb,MAAM,CAAC9C,KAAK,CAAC,EAAE;YACvD,MAAM+C,WAAW,GAAG,GAAG,GAAG5K,YAAY,CAAC5N,eAAe,CAACyV,KAAK,CAAC,CAACnW,OAAO,GAAG,GAAG;YAC3E,MAAMmZ,SAAS,GAAG,GAAG,GAAGhD,KAAK,GAAG,GAAG;YACnC,IAAI+C,WAAW,EACbH,aAAa,GAAGA,aAAa,CAACK,OAAO,CAACD,SAAS,EAAED,WAAW,CAAC;UACjE;QACF;MACF;IACF,CAAC,CAAC;IAEF5K,YAAY,CAAClM,iBAAiB,GAAG2W,aAAa;EAChD;EAEA3K,mBAAmBA,CAAC9V,YAAiB;IACnCjK,SAAS,CAAC8jB,eAAe,CAACtJ,SAAS,GAAGvQ,YAAY,CAACsQ,eAAe,CAAC2E,UAAU;IAC7Elf,SAAS,CAAC8jB,eAAe,CAACrJ,cAAc,GAAGxQ,YAAY,CAACsQ,eAAe,CAACE,cAAc;IACtFza,SAAS,CAAC8jB,eAAe,CAAC5X,aAAa,GAAGjC,YAAY,CAACiC,aAAa;IACpElM,SAAS,CAAC8jB,eAAe,CAACpX,SAAS,GAAGzC,YAAY,CAACyC,SAAS;EAC9D;EAEAub,kBAAkBA,CAAA;IAChB,IAAI,CAACvQ,kBAAkB,CAACuQ,kBAAkB,CAAC,IAAI,CAAC+C,UAAU,EAAE,IAAI,CAACtf,SAAS,EAAE,IAAI,CAACC,gBAAgB,EAC/F,IAAI,CAACtF,iBAAiB,EAAE,IAAI,CAAC4D,YAAY,CAACqR,gBAAgB,CAAC,CAC1DsF,SAAS,CAAC;MACTC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAAC5U,aAAa,GAAG,EAAE;UACvB,IAAI+e,SAAS,GAAG,EAAE;UAClB,IAAIC,OAAO,GAAG,EAAE;UAChBpK,QAAQ,CAACd,OAAO,CAACgH,IAAI,IAAG;YACtBiE,SAAS,GAAGjE,IAAI,CAACmE,YAAY;YAC7B,IAAI,CAACF,SAAS,EAAE;cACdA,SAAS,GAAG,GAAG;cACfC,OAAO,GAAG,GAAG;YACf,CAAC,MAAM;cACLA,OAAO,GAAGlE,IAAI,CAACmE,YAAY;YAC7B;YAEA,IAAI,CAACjf,aAAa,CAAC6U,IAAI,CAAC;cAAEvD,KAAK,EAAE0N,OAAO;cAAEniB,KAAK,EAAEkiB;YAAS,CAAE,CAAC;UAC/D,CAAC,CAAC;UAEF,IAAI,IAAI,CAAChhB,YAAY,EAAEiC,aAAa,IAAI,IAAI,CAACjC,YAAY,EAAEiC,aAAa,KAAK,EAAE,EAAE;YAC/E,IAAI,CAACkf,aAAa,EAAE;UACtB;QACF;MACF;KACD,CAAC;EACN;EAEAA,aAAaA,CAAA;IACX,IAAI,CAAC1T,kBAAkB,CAAC0T,aAAa,CAAC,IAAI,CAACJ,UAAU,EAAE,IAAI,CAACtf,SAAS,EAAE,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAACtF,iBAAiB,EAClH,IAAI,CAAC4D,YAAY,CAAC+d,aAAa,EAAE,IAAI,CAACjc,qBAAqB,CAAC,CAAC6U,SAAS,CAAC;MACrEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAACpU,SAAS,GAAG,EAAE;UACnBoU,QAAQ,CAACd,OAAO,CAACgH,IAAI,IAAG;YACtB,IAAI,CAACta,SAAS,CAACqU,IAAI,CAAC;cAAEvD,KAAK,EAAEwJ,IAAI,CAACqE,KAAK;cAAEtiB,KAAK,EAAEie,IAAI,CAACqE;YAAK,CAAE,CAAC;UAC/D,CAAC,CAAC;QACJ;MACF;KACD,CAAC;EACN;EAEApf,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACvH,eAAe,EAAE;MACxB;IACF;IAEA,IAAI,CAAC0mB,aAAa,EAAE;EACtB;EAEA9iB,yBAAyBA,CAAA;IACvB,IAAI,IAAI,CAAC5D,eAAe,EAAE;MACxB;IACF;IAEA,IAAI,IAAI,CAACoe,gBAAgB,IAAI,IAAI,CAAC7Y,YAAY,CAACyD,kBAAkB,EAAE;MACjE,IAAI,CAACmK,mBAAmB,CAAC2Q,OAAO,CAAC;QAC/BE,MAAM,EAAE,0BAA0B;QAClCD,OAAO,EAAE,mFAAmF;QAC5FK,MAAM,EAAEA,CAAA,KAAK;UACX,IAAI,CAACmB,aAAa,GAAG,IAAI,CAAC7hB,iBAAiB;UAE3C,IAAI,CAAC8hB,eAAe,EAAE;UAEtB,IAAI,CAACvgB,kBAAkB,EAAE;UAEzB,IAAI,CAAC2hB,iBAAiB,EAAE;QAC1B,CAAC;QACDvC,MAAM,EAAEA,CAAA,KAAK;UACX,IAAI,CAAC3gB,iBAAiB,GAAG,IAAI,CAAC6hB,aAAa;QAC7C;OACD,CAAC;MACF;IACF;IAEA,IAAI,CAACA,aAAa,GAAG,IAAI,CAAC7hB,iBAAiB;IAE3C,IAAI,CAAC8hB,eAAe,EAAE;IAEtB,IAAI,CAACvgB,kBAAkB,EAAE;IAEzB,IAAI,CAAC2hB,iBAAiB,EAAE;EAC1B;EAEApD,0BAA0BA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACrgB,gBAAgB,EAAE;MAC1B;IACF;IAEA;IACA,IAAI,IAAI,CAACoC,YAAY,CAACwO,UAAU,KAAKjY,UAAU,CAACme,cAAc,EAAE;MAC9D,IAAI,CAACnC,aAAa,GAAG,IAAI,CAAC3U,gBAAgB,CAACL,MAAM,KAAK,GAAG,GAAG,EAAE,GAAG,IAAI,CAACK,gBAAgB,CAAC2U,aAAa;MACpG,IAAI,CAAC/P,cAAc,EAAE;MACrB;IACF;IAEA;IACA,IAAI,CAACb,4BAA4B,GAAG,IAAI,CAAC+N,2BAA2B,CACjEiF,IAAI,CAAC2M,GAAG,IAAIA,GAAG,CAACtG,WAAW,KAAK,IAAI,CAAC7c,iBAAiB,CAACiG,EAAE,IAAIkd,GAAG,CAACC,UAAU,KAAK,IAAI,CAAC3jB,gBAAgB,CAACL,MAAM,CAAC,EAAEikB,oBAAoB;IACtI,IAAI,CAAC,IAAI,CAAC7f,4BAA4B,IAAI,IAAI,CAACxD,iBAAiB,CAACmF,UAAU,EAAEme,UAAU,EAAE;MACvF,IAAI,CAAC,IAAI,CAAChS,oBAAoB,CAAC,IAAI,CAACtR,iBAAiB,CAACiG,EAAE,CAAC,EAAE;QACzD,IAAI,CAACqL,oBAAoB,CAAC,IAAI,CAACtR,iBAAiB,CAACiG,EAAE,CAAC,GAClD,IAAI,CAACqJ,kBAAkB,CAACiU,6BAA6B,CAAC,IAAI,CAACvjB,iBAAiB,EAAEmF,UAAU,CAAC;MAC7F;MAEA;MACA,IAAI,IAAI,CAACnF,iBAAiB,CAAChD,IAAI,KAAK,IAAI,CAAC6E,YAAY,CAAC0R,QAAQ,IAC5D,IAAI,CAAC1R,YAAY,CAACC,YAAY,CAACib,2BAA2B,KACzD,IAAI,CAACtd,gBAAgB,CAACL,MAAM,KAAK,IAAI,CAACyC,YAAY,CAACuY,iBAAiB,IAClE,IAAI,CAAC3a,gBAAgB,CAACL,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAACyC,YAAY,CAACuY,iBAAkB,CAAC,EAAE;QACnF,IAAI,CAAC5W,4BAA4B,GAAG,IAAI,CAAC3B,YAAY,EAAEC,YAAY,EAAEib,2BAA2B;QAEhG;QACA,IAAI,CAACvZ,4BAA4B,GAC/B,IAAI,CAACggB,2BAA2B,CAC9B,IAAI,CAAC/jB,gBAAgB,CAACL,MAAM,KAAK,GAAG,EACpC,IAAI,CAACoE,4BAA4B,EACjC,IAAI,CAAC8N,oBAAoB,CAAC,IAAI,CAACtR,iBAAiB,CAACiG,EAAE,CAAC,CAAC;MAC3D,CAAC,MACI;QACH;QACA;QACA;QACA,IAAI,IAAI,CAACxG,gBAAgB,CAACL,MAAM,KAAK,GAAG,EAAE;UACxC,IAAI,CAACoE,4BAA4B,GAAGlL,QAAQ,CAC1C,IAAI,CAACgZ,oBAAoB,CAAC,IAAI,CAACtR,iBAAiB,CAACiG,EAAE,CAAC,CAAC;UACvD;QACF;QACA,IAAI,CAACzC,4BAA4B,GAAG,EAAE;QACtC,IAAI,CAAC/D,gBAAgB,CAAC2U,aAAa,EAAEwD,OAAO,CAAC1I,GAAG,IAAG;UACjD,MAAMkO,YAAY,GAAG,IAAI,CAAC9L,oBAAoB,CAAC,IAAI,CAACtR,iBAAiB,CAACiG,EAAE,CAAC,CAACuQ,IAAI,CAACgF,CAAC,IAAIA,CAAC,CAACiI,MAAM,KAAKvU,GAAG,CAAC;UAErG,IAAIkO,YAAY,EAAE;YAChB,IAAI,CAAC5Z,4BAA4B,CAACmV,IAAI,CAACrgB,QAAQ,CAA6B8kB,YAAY,CAAC,CAAC;UAC5F;QACF,CAAC,CAAC;MACJ;MAEA,IAAI,CAAC7L,2BAA2B,CAACoH,IAAI,CACnC;QACEkE,WAAW,EAAE,IAAI,CAAC7c,iBAAiB,CAACiG,EAAE;QACtCmd,UAAU,EAAE,IAAI,CAAC3jB,gBAAgB,CAACL,MAAM;QACxCikB,oBAAoB,EAAE,IAAI,CAAC7f;OAC5B,CAAC;IACN;EACF;EAEAggB,2BAA2BA,CAACE,iBAAiB,GAAG,KAAK,EAAEC,cAA4C,EAAEC,qBAAmD;IACtJ;IACA,MAAMC,aAAa,GAAG,CAACxrB,wBAAwB,CAACklB,QAAQ,EAAEllB,wBAAwB,CAACmlB,MAAM,EAAEnlB,wBAAwB,CAAColB,MAAM,CAAC;IAC3HkG,cAAc,GAAGA,cAAc,IAAI,EAAE;IAErC,IAAID,iBAAiB,EAAE;MACrB;MACA,KAAK,IAAII,CAAC,GAAGH,cAAc,CAACtc,MAAM,GAAG,CAAC,EAAEyc,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACnD,MAAM5U,GAAG,GAAGyU,cAAc,CAACG,CAAC,CAAC;QAC7B,MAAMpE,KAAK,GAAGkE,qBAAqB,CAACjE,SAAS,CAACnE,CAAC,IAAIA,CAAC,CAACiI,MAAM,KAAKvU,GAAG,CAACuU,MAAM,CAAC;QAC3E,IAAI/D,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBiE,cAAc,CAACtB,MAAM,CAACsB,cAAc,CAAChE,SAAS,CAACoE,GAAG,IAAIA,GAAG,CAACN,MAAM,KAAKvU,GAAG,CAACuU,MAAM,CAAC,EAAE,CAAC,CAAC;QACtF;MACF;MAEA;MACAE,cAAc,GAAGA,cAAc,CAACK,MAAM,CAACJ,qBAAqB,CACzDzG,MAAM,CAAC/H,KAAK,IAAI,CAACuO,cAAc,CAAC1U,IAAI,CAACwP,CAAC,IAAIA,CAAC,CAACgF,MAAM,KAAKrO,KAAK,CAACqO,MAAM,CAAC,CAAC,CAAC;IAC3E;IAEA;IACAE,cAAc,CAAC/L,OAAO,CAAC1I,GAAG,IAAG;MAC3B,MAAM+U,gBAAgB,GAAGL,qBAAqB,CAACpN,IAAI,CAACgF,CAAC,IAAIA,CAAC,CAACiI,MAAM,KAAKvU,GAAG,CAACuU,MAAM,CAAC;MAEjF,IAAIQ,gBAAgB,EAAE;QACpB/U,GAAG,CAAChH,MAAM,CAACQ,QAAQ,GAAGub,gBAAgB,CAAC/b,MAAM,CAACQ,QAAQ;QACtDwG,GAAG,CAAChH,MAAM,CAACgc,UAAU,GAAGD,gBAAgB,CAAC/b,MAAM,CAACgc,UAAU;QAC1DhV,GAAG,CAACiV,eAAe,GAAGF,gBAAgB,CAACE,eAAe;QACtDjV,GAAG,CAAChH,MAAM,CAACJ,QAAQ,GAAGmc,gBAAgB,CAAC/b,MAAM,CAACJ,QAAQ;QACtDoH,GAAG,CAAChH,MAAM,CAACT,aAAa,GAAGwc,gBAAgB,CAAC/b,MAAM,CAACT,aAAa;QAChEyH,GAAG,CAAC1G,WAAW,GAAGyb,gBAAgB,CAACzb,WAAW;QAC9C0G,GAAG,CAACkV,cAAc,GAAGH,gBAAgB,CAACG,cAAc;QAEpD;;;;;;;QAOA,IAAIlV,GAAG,CAAChH,MAAM,CAAC1H,IAAI,KAAKnI,wBAAwB,CAACulB,IAAI,IACnD,CAAC,CAACvlB,wBAAwB,CAAC+W,QAAQ,EAAE/W,wBAAwB,CAAC8W,IAAI,CAAC,CAACkV,QAAQ,CAACnV,GAAG,CAAChH,MAAM,CAAC1H,IAAI,CAAC,KAC5FyjB,gBAAgB,CAAC/b,MAAM,CAACQ,QAAQ,KAAK9P,aAAa,CAAC4kB,MAAM,IAAI,CAACqG,aAAa,CAACQ,QAAQ,CAACnV,GAAG,CAAChH,MAAM,CAAC1H,IAAI,CAAC,CAAC,EAAE;UACzG0O,GAAG,CAAChH,MAAM,CAAC1H,IAAI,GAAGyjB,gBAAgB,CAAC/b,MAAM,CAAC1H,IAAI;QAChD;QAEA,IAAIyjB,gBAAgB,CAACG,cAAc,EAAEE,cAAc,EAAEC,QAAQ,EAAE;UAC7DrV,GAAG,CAACqV,QAAQ,GAAGN,gBAAgB,CAACM,QAAQ;QAC1C;QAEA,IAAIrV,GAAG,CAACqV,QAAQ,IAAIrV,GAAG,CAACuU,MAAM,KAAK,IAAI,CAACzjB,iBAAiB,CAACwkB,UAAU,EAAE;UACpEtV,GAAG,CAACuV,eAAe,GAAG,IAAI;QAC5B;QAEA,IAAIvV,GAAG,CAAChH,MAAM,CAACQ,QAAQ,KAAK9P,aAAa,CAAC8rB,KAAK,EAAE;UAC/CxV,GAAG,CAACyV,QAAQ,GAAG,IAAI,CAACnB,2BAA2B,CAACE,iBAAiB,EAAExU,GAAG,CAACyV,QAAQ,EAAEV,gBAAgB,CAACU,QAAQ,CAAC;QAC7G;MACF;IACF,CAAC,CAAC;IAEF,OAAOhB,cAAc;EACvB;EAEAtf,cAAcA,CAAA;IACZ;IACA,IAAI,IAAI,CAAC/H,eAAe,IAAI,IAAI,CAACuF,YAAY,CAACwO,UAAU,KAAKjY,UAAU,CAACme,cAAc,EAAE;MACtF;IACF;IAEA,IAAI,CAACqO,YAAY,EAAE;IACnB,IAAI,IAAI,CAAC/iB,YAAY,CAACwR,cAAc,CAACC,UAAU,EAAE;MAC/C,MAAMuR,qBAAqB,GAAG,IAAI,CAACC,wBAAwB,EAAE;MAE7D;MACA,IAAI,CAAC3Q,OAAO,GAAG,EAAE;MACjB;MACA,IAAI,CAACtL,aAAa,GAAG,EAAE;MAEvB;MACA,IAAI,CAAC4C,UAAU,GAAG,EAAE;MAEpB,IAAI,IAAI,CAACpO,qBAAqB,EAAEP,GAAG,KAAK5E,cAAc,CAACwe,SAAS,EAAE;QAChE,IAAI,CAACqO,2BAA2B,CAACF,qBAAqB,CAAC;MACzD,CAAC,MAAM;QACL,IAAI,CAACzQ,aAAa,GAAG,EAAE;QACvB,IAAI,CAAC4Q,oBAAoB,CAACH,qBAAqB,CAAC;MAClD;IACF;EACF;EAEAC,wBAAwBA,CAAA;IACtB,IAAI,IAAI,CAACjjB,YAAY,CAACC,YAAY,EAAEsR,MAAM,KACtC,IAAI,CAACvR,YAAY,CAAC4P,cAAc,KAAKvZ,cAAc,CAACwe,SAAS,IAC7D,IAAI,CAAC7U,YAAY,CAAC0R,QAAQ,KAAK,IAAI,CAACvT,iBAAiB,EAAEhD,IAAI,IAC1D,IAAI,CAAC6E,YAAY,CAAC4P,cAAc,KAAKvZ,cAAc,CAACujB,SAAS,IAC5D,IAAI,CAAC5Z,YAAY,CAACyC,SAAS,KAAK,IAAI,CAACH,iBAAkB,CAAC,EAAE;MAC9D,OAAOmX,MAAM,CAACqC,MAAM,CAAe,IAAI,CAAC9b,YAAY,CAACC,YAAY,CAACsR,MAAM,CAAC;IAC3E;IACA,OAAO,EAAE;EACX;EAEA2R,2BAA2BA,CAACF,qBAAqC;IAC/D,IAAI,IAAI,CAAC7kB,iBAAiB,EAAEmF,UAAU,EAAEme,UAAU,EAAE;MAClDhI,MAAM,CAAC2J,IAAI,CAAC,IAAI,CAACjlB,iBAAiB,CAACmF,UAAU,CAACme,UAAU,CAAC,CAAC1L,OAAO,CAACkH,CAAC,IAAG;QACpE,MAAMoG,OAAO,GAAGpG,CAAC,CAAC7D,QAAQ,EAAE;QAC5B;QACA,MAAMkK,SAAS,GAAG,IAAI,CAACnlB,iBAAiB,CAACmF,UAAU,CAACme,UAAU,CAACxE,CAAC,CAAC;QAEjE,IAAIqG,SAAS,CAAC3kB,IAAI,KAAK5H,aAAa,CAAC8rB,KAAK,IAAIS,SAAS,CAAC3kB,IAAI,KAAK5H,aAAa,CAAC0iB,MAAM,EAAE;UACrF,IAAImI,MAAM,GAAGoB,qBAAqB,EAAErO,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzZ,IAAI,KAAKkoB,OAAO,CAAC;UAEjE,IAAI,CAACzB,MAAM,EAAE;YACXA,MAAM,GAAG,IAAIlrB,YAAY,CAAC2sB,OAAO,CAAC;YAElC,QAAQC,SAAS,CAAC3kB,IAAI;cACpB,KAAK5H,aAAa,CAAC4kB,MAAM;gBACvBiG,MAAM,CAACvb,MAAM,GAAGid,SAAS,CAACjd,MAAM,KAAKrP,iBAAiB,CAACuW,QAAQ,IAAI+V,SAAS,CAACjd,MAAM,KAAKrP,iBAAiB,CAACsW,IAAI,GAAG,MAAM,GAAG,QAAQ;gBAClI;cACF,KAAKvW,aAAa,CAACwsB,OAAO;gBACxB3B,MAAM,CAACvb,MAAM,GAAG,QAAQ;gBACxB;cACF,KAAKtP,aAAa,CAACysB,OAAO;cAC1B,KAAKzsB,aAAa,CAAC0sB,OAAO;gBACxB7B,MAAM,CAACvb,MAAM,GAAGid,SAAS,EAAEb,cAAc,EAAExc,QAAQ,GAAG,UAAU,GAAG,SAAS;gBAC5E;cACF;gBACE2b,MAAM,CAACvb,MAAM,GAAGid,SAAS,CAAC3kB,IAAI;gBAC9B;YACJ;YAEAijB,MAAM,CAACjb,WAAW,GAAG2c,SAAS,EAAEI,sBAAsB,EAAE/c,WAAW;YACnEib,MAAM,CAAChc,aAAa,GAAG0d,SAAS,EAAEI,sBAAsB,EAAE9d,aAAa,IAAI,CAAC;YAC5Egc,MAAM,CAAC3b,QAAQ,GAAGqd,SAAS,EAAEb,cAAc,EAAExc,QAAQ;UACvD;UAEA2b,MAAM,CAAC/a,QAAQ,GAAGyc,SAAS,CAAC3kB,IAAI;UAChCijB,MAAM,CAACS,UAAU,GAAGiB,SAAS,CAACjd,MAAM;UACpC,MAAM1H,IAAI,GAAG8a,MAAM,CAAC2J,IAAI,CAACrsB,aAAa,CAAC,CAACukB,MAAM,CAACqI,CAAC,IAAI5sB,aAAa,CAAC4sB,CAAC,CAAC,KAAKL,SAAS,CAAC3kB,IAAI,CAAC;UAExF,IAAIA,IAAI,CAAC6G,MAAM,GAAG,CAAC,EAAE;YACnBoc,MAAM,CAACU,eAAe,GAAIgB,SAAS,CAACjd,MAAM,KAAKrP,iBAAiB,CAACuW,QAAQ,IAAI+V,SAAS,CAACjd,MAAM,KAAKrP,iBAAiB,CAACsW,IAAI,GAAI,MAAM,GAAG3O,IAAI,CAAC,CAAC,CAAC;UAC9I;UAEA;UACA,MAAMilB,UAAU,GAAa,IAAI,CAACrR,aAAa,CAAC/M,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC+M,aAAa,EAAEoC,IAAI,CAACgF,CAAC,IAAIA,CAAC,KAAK0J,OAAO,CAAE;UAE7G,IAAIO,UAAU,IAAI,CAAC,IAAI,CAAChmB,gBAAgB,EAAE;YACxC,IAAI,CAACoJ,aAAa,CAAC8P,IAAI,CAAC8K,MAAM,CAAC;UACjC;UAEA,IAAI,CAACtP,OAAO,CAACwE,IAAI,CAAC8K,MAAM,CAAC;UACzB,IAAI,CAAChY,UAAU,CAACkN,IAAI,CAAC;YAAEvD,KAAK,EAAEqO,MAAM,CAACzmB,IAAI;YAAE2D,KAAK,EAAE8iB,MAAM,CAACzmB;UAAI,CAAE,CAAC;QAClE;MACF,CAAC,CAAC;IACJ;EACF;EAEAgoB,oBAAoBA,CAACH,qBAAqC;IACxD,IAAI,CAACvV,kBAAkB,CAACoW,gBAAgB,CAAC,IAAI,CAACpiB,SAAS,EAAE,IAAI,CAACC,gBAAgB,EAAE,IAAI,CAAC1B,YAAY,CAAC,CAAC2W,SAAS,CAAC;MAC3GC,IAAI,EAAGkN,cAAc,IAAI;QACvB,IAAI,CAACvU,cAAc,GAAG,KAAK;QAC3B,IAAIuU,cAAc,EAAE;UAClB,IAAI,CAACnR,iBAAiB,GAAI,IAAI,CAACJ,aAAa,CAAC/M,MAAM,GAAG,CAAC,GAAI,IAAI,CAAC+M,aAAa,CAAC/M,MAAM,GAAGse,cAAc,CAACte,MAAM;UAE5Gse,cAAc,CAAC/N,OAAO,CAACsN,OAAO,IAAG;YAC/B;YACA,MAAMO,UAAU,GAAa,IAAI,CAACrR,aAAa,CAAC/M,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC+M,aAAa,EAAEoC,IAAI,CAACgF,CAAC,IAAIA,CAAC,CAACxe,IAAI,KAAKkoB,OAAO,CAAE;YAElH;YACA,MAAMzB,MAAM,GAAGoB,qBAAqB,EAAErO,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzZ,IAAI,KAAKkoB,OAAO,CAAC,IAC9D,IAAI3sB,YAAY,CAAC2sB,OAAO,EAAGA,OAAO,CAACU,WAAW,EAAE,CAACvB,QAAQ,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,QAAS,CAAC;YAE5F,IAAIoB,UAAU,EAAE;cACd,IAAI,CAAC5c,aAAa,CAAC8P,IAAI,CAAC8K,MAAM,CAAC;YACjC;YACA,IAAI,CAACtP,OAAO,CAACwE,IAAI,CAAC8K,MAAM,CAAC;YACzB,IAAI,CAAChY,UAAU,CAACkN,IAAI,CAAC;cAAEvD,KAAK,EAAEqO,MAAM,CAACzmB,IAAI;cAAE2D,KAAK,EAAE8iB,MAAM,CAACzmB;YAAI,CAAE,CAAC;UAClE,CAAC,CAAC;QACJ;MACF,CAAC;MACDwa,KAAK,EAAE2B,GAAG,IAAG;QACX,IAAI,CAAC3J,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAEb,GAAG,CAACkH,OAAO,CAAC;MACjD;KACD,CAAC;EACJ;EAEAjY,gBAAgBA,CAACyd,OAAqB;IACpC,IAAI,IAAI,CAACvpB,eAAe,EAAE;MACxB;IACF;IACAupB,OAAO,CAACpe,aAAa,GAAGoe,OAAO,CAAC3d,MAAM,KAAK,UAAU,GAAG,CAAC,GAAG,CAAC;EAC/D;EAEA0c,YAAYA,CAAA;IACV,IAAI,CAAC/iB,YAAY,CAACwR,cAAc,GAAG;MAAEpN,EAAE,EAAE,CAAC;MAAEqN,UAAU,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,aAAa,EAAE,EAAE;MAAE1P,aAAa,EAAE,EAAE;MAAEQ,SAAS,EAAE;IAAE,CAAE;IAC/H,IAAI,CAACzC,YAAY,CAACwR,cAAc,CAACE,QAAQ,GAAG,IAAI,CAACvT,iBAAiB,EAAEhD,IAAI;IACxE,IAAI,CAAC6E,YAAY,CAACwR,cAAc,CAACC,UAAU,GAAG,IAAI,CAACjW,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACP,GAAG,GAAG,EAAE;IAC9G,IAAI,CAAC+E,YAAY,CAACwR,cAAc,CAACG,aAAa,CAACsD,UAAU,GAAG,IAAI,CAAC7Y,iBAAiB;IAClF,IAAI,CAAC4D,YAAY,CAACwR,cAAc,CAACG,aAAa,CAACnB,cAAc,GAAG,IAAI,CAACxQ,YAAY,CAAC4R,oBAAoB;IACtG,IAAI,CAAC5R,YAAY,CAACwR,cAAc,CAACvP,aAAa,GAAG,IAAI,CAACH,qBAAqB;IAC3E,IAAI,CAAC9B,YAAY,CAACwR,cAAc,CAAC/O,SAAS,GAAG,IAAI,CAACH,iBAAiB;EACrE;EAEAyI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACtQ,eAAe,EAAE;MACxB;IACF;IAEA,IAAI,CAAC4T,oBAAoB,CAACuI,IAAI,CAAC;MAC7B6D,WAAW,EAAE,IAAI,CAACpM,oBAAoB,CAACqM,cAAc;MACrDC,SAAS,EAAE,IAAI,CAACtM,oBAAoB,CAACuM,iBAAiB;MACtDzD,SAAS,EAAE,gBAAgB;MAC3BM,IAAI,EAAE;QACJwM,OAAO,EAAE,IAAI,CAACjkB,YAAY,CAAC6P,QAAQ;QACnCqU,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE,IAAI,CAACnkB,YAAY,CAAC8P,UAAU;QACvCK,QAAQ,EAAE,IAAI,CAACnQ,YAAY,CAACmQ,QAAQ;QACpC1O,SAAS,EAAE,IAAI,CAACzB,YAAY,CAACyB,SAAS;QACtC2iB,eAAe,EAAE,IAAI,CAACpkB,YAAY,CAACwT,WAAW;QAC9C6Q,WAAW,EAAE;;KAEhB,CAAC;EACJ;EACApO,kBAAkBA,CAAA;IAChB,IAAI,CAACxI,kBAAkB,CAAC6W,mBAAmB,CAAC,IAAI,CAAC5iB,gBAAgB,CAAC,CAACiV,SAAS,CAAC;MAC3EC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAAC0N,gBAAgB,GAAG1N,QAAQ;UAChCA,QAAQ,CAACd,OAAO,CAACmD,OAAO,IAAG;YACzB,IAAI,CAACrM,mBAAmB,CAACiK,IAAI,CAAC;cAAEvD,KAAK,EAAE2F,OAAO,CAACsL,QAAQ;cAAE1lB,KAAK,EAAEoa,OAAO,CAACsL,QAAQ;cAAEhY,KAAK,EAAE0M,OAAO,CAACnJ;YAAW,CAAE,CAAC;UACjH,CAAC,CAAC;QACJ;MACF,CAAC;MACD4F,KAAK,EAAE2B,GAAG,IAAG;QACX,MAAMA,GAAG;MACX;KACD,CAAC;EACJ;EAEAja,UAAUA,CAAC8c,QAAkB;IAC3B,IAAI,IAAI,CAAC1f,eAAe,EAAE;MACxB;IACF;IAEA,IAAI,CAACmT,mBAAmB,CAAC2Q,OAAO,CAAC;MAC/BC,OAAO,EAAE,iDAAiD;MAC1DK,MAAM,EAAEA,CAAA,KAAK;QACX,MAAMhB,KAAK,GAAG,IAAI,CAAC1f,iBAAiB,CAACH,SAAS,CAACymB,OAAO,CAACtK,QAAQ,CAAC;QAChE,MAAMuK,uBAAuB,GAAGjuB,QAAQ,CAAY,IAAI,CAAC0H,iBAAiB,CAAC;QAC3EumB,uBAAuB,CAAC1mB,SAAS,CAACwiB,MAAM,CAAC3C,KAAK,EAAE,CAAC,CAAC;QAClD,IAAI,CAACnQ,iBAAiB,CAACiX,eAAe,CAACD,uBAAuB,CAACtgB,EAAE,EAAEsgB,uBAAuB,CAAC,CACxF3E,IAAI,CAAC3pB,IAAI,CAAC,CAAC,CAAC,CAAC,CAACugB,SAAS,CAAC;UACvBC,IAAI,EAAGC,QAAQ,IAAI;YACjB,IAAI,CAAChE,qBAAqB,GAAG,YAAY,GAAGsH,QAAQ,CAAChf,IAAI,GAAG,uBAAuB;YACnF,IAAI,CAACgD,iBAAiB,CAACH,SAAS,CAACwiB,MAAM,CAAC3C,KAAK,EAAE,CAAC,CAAC;YACjD,IAAI,CAACoC,eAAe,EAAE;UACxB,CAAC;UACDtK,KAAK,EAAEiP,SAAS,IAAG;YACjB,IAAI,CAACjX,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAEyM,SAAS,CAAC;UAC/C;SACD,CAAC;MACN;KACD,CAAC;EAEJ;EAEA3nB,wBAAwBA,CAACC,QAAgB,EAAEid,QAAmB;IAC5D,IAAI,IAAI,CAAC1f,eAAe,EAAE;MACxB;IACF;IAEA,IAAI,CAAC4T,oBAAoB,CAACuI,IAAI,CAAC;MAC7B6D,WAAW,EAAE,IAAI,CAACpM,oBAAoB,CAACqM,cAAc;MACrDC,SAAS,EAAE,IAAI,CAACtM,oBAAoB,CAACwW,6BAA6B;MAClE1N,SAAS,EAAEphB,SAAS,CAAC8kB,SAAS,CAACiK,qBAAqB;MACpDrN,IAAI,EAAE;QACJ2C,SAAS,EAAE3jB,QAAQ,CAAY,IAAI,CAAC0H,iBAAiB,CAAC;QACtDgc,QAAQ,EAAE1jB,QAAQ,CAAW0jB,QAAQ,CAAC;QACtC4K,IAAI,EAAE7nB;;KAET,CAAC;EACJ;EAEA+iB,eAAeA,CAAA;IACb,MAAM+E,WAAW,GAAG,IAAIluB,QAAQ,CAAC,kBAAkB,EAAE,GAAG,CAAC;IACzD,IAAI,CAACkH,SAAS,GAAG,CAACgnB,WAAW,EAAE,IAAI,IAAI,CAAC7mB,iBAAiB,EAAEH,SAAS,IAAI,EAAE,CAAC,CAAC;IAC5E,IAAI,CAACinB,+BAA+B,EAAE;EACxC;EAEMA,+BAA+BA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAArR,iBAAA;MACnC,IAAI,CAACqR,MAAI,CAAC/mB,iBAAiB,EACzB;MAEF;MACA,IAAI+mB,MAAI,CAACllB,YAAY,EAAE0R,QAAQ,KAAKwT,MAAI,CAAC/mB,iBAAiB,CAAChD,IAAI,IAAI+pB,MAAI,CAACllB,YAAY,CAACuY,iBAAiB,EAAE;QACtG,MAAM4M,IAAI,GAAGD,MAAI,CAAClnB,SAAS,CAAC2W,IAAI,CAACyQ,CAAC,IAAIA,CAAC,CAAC7nB,MAAM,KAAK2nB,MAAI,CAACllB,YAAY,CAACuY,iBAAiB,CAAC;QACvF;QACA;QACA2M,MAAI,CAACtnB,gBAAgB,GAAGunB,IAAI,IAAID,MAAI,CAAClnB,SAAS,CAAC,CAAC,CAAC;QACjDknB,MAAI,CAACG,YAAY,GAAGH,MAAI,CAACtnB,gBAAgB;QAEzC;QACA,IAAIsnB,MAAI,CAACrM,gBAAgB,IAAIqM,MAAI,CAACllB,YAAY,CAACyD,kBAAkB,EAAE;UACjE,MAAMyY,UAAU,GAAGgJ,MAAI,CAACtnB,gBAAgB,CAACL,MAAM,KAAK,GAAG,GAAG,MAAM,SAAS2nB,MAAI,CAAC9W,aAAa,CAAC+N,UAAU,CAAC+I,MAAI,CAACtnB,gBAAgB,CAAC2U,aAAa,EAAE6J,IAAI,EAAE,EAAEhD,QAAQ,EAAE,CAAC;UAE/J,IAAI8L,MAAI,CAACllB,YAAY,CAACqD,kBAAkB,CAAC6Y,UAAU,KAAKA,UAAU,EAAE;YAClEgJ,MAAI,CAACvX,UAAU,CAAC0K,WAAW,CAAC,kEAAkE,CAAC;YAC/F6M,MAAI,CAACllB,YAAY,CAACqD,kBAAkB,CAAC6Y,UAAU,SAASgJ,MAAI,CAAC9W,aAAa,CAAC+N,UAAU,CAAC+I,MAAI,CAACtnB,gBAAgB,CAAC2U,aAAa,EAAE6J,IAAI,EAAE,EAAEhD,QAAQ,EAAE,CAAC;UAChJ;QACF;QAEA8L,MAAI,CAACllB,YAAY,CAAC8Z,MAAM,CAACwB,MAAM,CAACgK,KAAK,IAAI,CAACJ,MAAI,CAACtnB,gBAAgB,EAAE2nB,gBAAgB,CAAC5Q,IAAI,CAACgF,CAAC,IAAIA,CAAC,CAAC6L,SAAS,KAAKF,KAAK,CAACE,SAAS,CAAC,CAAC,CAC1HzP,OAAO,CAACuP,KAAK,IAAG;UAAGA,KAAK,CAACvL,MAAM,GAAG,QAAQ;QAAE,CAAC,CAAC;QACjDmL,MAAI,CAAC3W,gBAAgB,CAAC8L,IAAI,CAAC6K,MAAI,CAACllB,YAAY,CAAC8Z,MAAM,CAAC;QAEpDoL,MAAI,CAACzX,kBAAkB,CAACyM,yBAAyB,CAACtD,IAAI,CAAC;UAAEuD,QAAQ,EAAE+K,MAAI,CAACtnB,gBAAgB;UAAEwc,SAAS,EAAE8K,MAAI,CAAC/mB;QAAiB,CAAE,CAAC;MAChI,CAAC,MACI;QACH;QACA+mB,MAAI,CAAC/W,iBAAiB,CAACsX,aAAa,EAAE;QACtCP,MAAI,CAACtnB,gBAAgB,GAAGsnB,MAAI,CAAClnB,SAAS,CAAC,CAAC,CAAC;QACzCknB,MAAI,CAACG,YAAY,GAAGH,MAAI,CAACtnB,gBAAgB;MAC3C;MACA;MACAsnB,MAAI,CAACjH,0BAA0B,EAAE;IAAC;EACpC;EAEAvgB,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACjD,eAAe,EAAE;MACxB;IACF;IAEA,IAAI,IAAI,CAACoe,gBAAgB,IAAI,IAAI,CAAC7Y,YAAY,CAACyD,kBAAkB,EAAE;MACjE,IAAI,CAACmK,mBAAmB,CAAC2Q,OAAO,CAAC;QAC/BE,MAAM,EAAE,0BAA0B;QAClCD,OAAO,EAAE,mFAAmF;QAC5FK,MAAM,EAAEA,CAAA,KAAK;UACX,IAAI,CAACwG,YAAY,GAAG,IAAI,CAACznB,gBAAgB;UACzC,IAAI,CAACqgB,0BAA0B,EAAE;UACjC,IAAI,CAACoD,iBAAiB,EAAE;QAC1B,CAAC;QACDvC,MAAM,EAAEA,CAAA,KAAK;UACX,IAAI,CAAClhB,gBAAgB,GAAG,IAAI,CAACynB,YAAY;QAC3C;OACD,CAAC;MACF;IACF;IAEA,IAAI,CAACA,YAAY,GAAG,IAAI,CAACznB,gBAAgB;IACzC,IAAI,CAACqgB,0BAA0B,EAAE;IACjC,IAAI,CAACoD,iBAAiB,EAAE;EAC1B;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,CAACrhB,YAAY,CAACyD,kBAAkB,GAAG,KAAK;IAC5C,IAAI,CAACzD,YAAY,CAACqD,kBAAkB,GAAG,IAAI;EAC7C;EAEA8E,qBAAqBA,CAACC,eAAuB,EAAEJ,QAAQ,EAAE0d,oBAAoB;IAC3E,IAAI,IAAI,CAACjrB,eAAe,EAAE;MACxB;IACF;IAEA,IAAI2N,eAAe,CAACsd,oBAAoB,CAAC,CAACC,iBAAiB,EAAE;MAC3D,IAAI,CAAC/S,UAAU,GAAG,iBAAiB;MACnC,IAAI,CAACvG,uBAAuB,GAAG,IAAI,CAACQ,mBAAmB,CAAC8H,IAAI,CAACgF,CAAC,IAAIA,CAAC,CAAC7a,KAAK,KAAKsJ,eAAe,CAACsd,oBAAoB,CAAC,CAAChe,OAAO,CAAC;IAC9H,CAAC,MACI;MACH,IAAI,CAACkL,UAAU,GAAG,QAAQ;MAC1B,IAAI,CAAC9G,cAAc,GAAG,IAAI,CAAClC,UAAU,CAAC+K,IAAI,CAACgF,CAAC,IAAIA,CAAC,CAAC7a,KAAK,KAAKsJ,eAAe,CAACsd,oBAAoB,CAAC,CAAChe,OAAO,CAAC;IAC5G;IACA,IAAI,CAACwF,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAAC0Y,uBAAuB,GAAGxd,eAAe,CAACsd,oBAAoB,CAAC,CAAChe,OAAO;IAC5E,IAAI,CAACme,gBAAgB,GAAG7d,QAAQ;IAChC,IAAI,CAAC8d,mBAAmB,GAAGJ,oBAAoB;EACjD;EAEA1Y,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACvS,eAAe,EAAE;MACxB;IACF;IACA,MAAMsrB,oBAAoB,GAAG,IAAI,CAAC7b,aAAa,CAAC,IAAI,CAAC2b,gBAAgB,CAAC;IACtE,MAAMG,cAAc,GAAGD,oBAAoB,CAAC3d,eAAe,CAAC,IAAI,CAAC0d,mBAAmB,CAAC;IACrFE,cAAc,CAACL,iBAAiB,GAAG,IAAI,CAAC/S,UAAU,KAAK,QAAQ;IAC/DoT,cAAc,CAACte,OAAO,GAAG,CAACse,cAAc,CAACL,iBAAiB,GAAG,IAAI,CAACtZ,uBAAuB,GAAG,IAAI,CAACP,cAAc,EAAEyH,KAAK;IACtH,IAAI,CAAC1L,oBAAoB,CAACke,oBAAoB,CAAC;IAC/C,IAAI,CAAC7Y,sBAAsB,GAAG,KAAK;EACrC;EAEA4G,gCAAgCA,CAAA;IAC9B,IAAI,CAACf,oBAAoB,GAAG,IAAI,CAAC1E,oBAAoB,CAAC4X,aAAa,EAAE,CAACtP,SAAS,CAACa,KAAK,IAAG;MACtF,IAAI,CAAC1J,MAAM,CAACoY,GAAG,CAAC,MAAK;QACnB,IAAI1O,KAAK,CAACL,SAAS,KAAKphB,SAAS,CAAC8kB,SAAS,CAACsL,qBAAqB,EAAE;UACjE,IAAI3O,KAAK,CAACC,IAAI,CAAC0C,QAAQ,EAAE;YACvB,IAAI,CAACiM,kBAAkB,CAAC5O,KAAK,CAACC,IAAI,CAAC0C,QAAQ,CAAC;UAC9C;QACF,CAAC,MACI,IAAI3C,KAAK,CAACL,SAAS,KAAKphB,SAAS,CAAC8kB,SAAS,CAACwL,sBAAsB,EAAE;UACvE,IAAI7O,KAAK,CAACC,IAAI,CAAC9B,KAAK,EAAE;YACpB,IAAI,CAAChI,UAAU,CAACwK,SAAS,CAAC,OAAO,EAAEX,KAAK,CAACC,IAAI,CAAC9B,KAAK,CAAC;YACpDC,OAAO,CAACC,GAAG,CAAC2B,KAAK,CAACC,IAAI,CAAC9B,KAAK,CAAC;UAC/B;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAyQ,kBAAkBA,CAACjM,QAAkB;IACnC,IAAI,CAACA,QAAQ,EAAE;MACb;IACF;IAEA,IAAI,CAAC,IAAI,CAAChc,iBAAiB,EAAEH,SAAS,EAAE;MACtC,IAAI,CAACG,iBAAiB,CAACH,SAAS,GAAG,EAAE;IACvC;IAEA;IACA,MAAMsoB,eAAe,GAAG,IAAI,CAACnoB,iBAAiB,EAAEH,SAAS,CAAC2W,IAAI,CAAC4R,GAAG,IAAIA,GAAG,CAAChpB,MAAM,KAAK4c,QAAQ,CAAC5c,MAAM,CAAC;IAErG;IACA,IAAI+oB,eAAe,EAAE;MACnBA,eAAe,CAACE,iBAAiB,GAAGrM,QAAQ,CAACqM,iBAAiB;MAC9DF,eAAe,CAAC/T,aAAa,GAAG4H,QAAQ,CAAC5H,aAAa;MACtD+T,eAAe,CAACf,gBAAgB,GAAGpL,QAAQ,CAACoL,gBAAgB;MAC5De,eAAe,CAACnrB,IAAI,GAAGgf,QAAQ,CAAChf,IAAI;MAEpC;MACA,MAAMuU,2BAA2B,GAAG,IAAI,CAACA,2BAA2B,CACjEiF,IAAI,CAAC2M,GAAG,IAAIA,GAAG,CAACtG,WAAW,KAAK,IAAI,CAAC7c,iBAAiB,CAACiG,EAAE,IAAIkd,GAAG,CAACC,UAAU,KAAKpH,QAAQ,CAAC5c,MAAM,CAAC;MACnG,IAAImS,2BAA2B,EAAE;QAC/B;QACA,MAAM+W,cAAc,GAAG/W,2BAA2B,CAAC8R,oBAAoB,CACpElG,MAAM,CAACgG,GAAG,IAAInH,QAAQ,CAAC5H,aAAa,CAACnF,IAAI,CAACsZ,GAAG,IAAIA,GAAG,KAAKpF,GAAG,CAACM,MAAM,CAAC,CAAC;QAExE;QACA,MAAM+E,UAAU,GAAGxM,QAAQ,CAAC5H,aAAa,CACtC+I,MAAM,CAAC/H,KAAK,IAAI,CAAC7D,2BAA2B,CAAC8R,oBAAoB,CAACpU,IAAI,CAACkU,GAAG,IAAIA,GAAG,CAACM,MAAM,KAAKrO,KAAK,CAAC,CAAC;QAEvG;QACA7D,2BAA2B,CAAC8R,oBAAoB,GAAG/qB,QAAQ,CAACgwB,cAAc,CAACtE,MAAM,CAC/E,IAAI,CAAC1S,oBAAoB,CAAC,IAAI,CAACtR,iBAAiB,EAAEiG,EAAE,CAAC,EAAEkX,MAAM,CAAC/H,KAAK,IAAIoT,UAAU,CAACvZ,IAAI,CAACsZ,GAAG,IAAIA,GAAG,KAAKnT,KAAK,CAACqO,MAAM,CAAC,CAAC,CAAC,CAAC;MAC1H;MACA,IAAI,CAACjU,UAAU,CAAC2M,WAAW,CAAC,SAAS,EAAE,8BAA8B,CAAC;MACtE,IAAI,CAAC2F,eAAe,EAAE;MACtB,IAAI,CAACriB,gBAAgB,GAAG0oB,eAAe;IACzC;IACA;IAAA,KACK;MACH,IAAI,CAACnoB,iBAAiB,EAAEH,SAAS,CAAC8Y,IAAI,CAACqD,QAAQ,CAAC;MAChD,IAAI,CAACnc,SAAS,CAAC8Y,IAAI,CAACqD,QAAQ,CAAC;MAC7B,IAAI,CAACvc,gBAAgB,GAAGuc,QAAQ;MAChC,IAAI,CAACxM,UAAU,CAAC2M,WAAW,CAAC,SAAS,EAAE,gCAAgC,CAAC;IAC1E;IAEA,IAAI,CAAC2D,0BAA0B,EAAE;EACnC;EAEAre,8BAA8BA,CAAA;IAC5B,IAAI,IAAI,CAACnF,eAAe,IAAI,CAAC,IAAI,CAAC2d,kCAAkC,EAAE,EAAE;MACtE;IACF;IAEA,IAAI,CAACtF,wBAAwB,GAAG,IAAI;EACtC;EAGA8T,4BAA4BA,CAACC,OAAO;IAClC,IAAI,IAAI,CAAC/T,wBAAwB,KAAK+T,OAAO,EAAE;MAC7C,IAAI,CAAC/T,wBAAwB,GAAG+T,OAAO;IACzC;IAEA;IACA,IAAI,CAACnnB,kBAAkB,EAAE;EAC3B;EAEAA,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACjF,eAAe,EAAE;MACxB;IACF;IAEA,IAAI,CAACiT,iBAAiB,CAAChO,kBAAkB,CAAC,IAAI,CAACvB,iBAAiB,CAACiG,EAAE,EAAEuc,MAAM,CAAC,IAAI,CAACjf,gBAAgB,CAAC,CAAC,CAACiV,SAAS,CAAC;MAC5GC,IAAI,EAAGC,QAA4B,IAAI;QACrCA,QAAQ,CAACuF,IAAI,CAAC,CAAC0K,CAAmB,EAAEC,CAAmB,KAAKD,CAAC,CAAC3rB,IAAI,CAAC6rB,iBAAiB,EAAE,CAACC,UAAU,CAAC,CAAC,CAAC,GAAGF,CAAC,CAAC5rB,IAAI,CAAC6rB,iBAAiB,EAAE,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;QAChJ,MAAMC,cAAc,GAAGrQ,QAAQ,CAACyE,MAAM,CAAC6L,KAAK,IAAIA,KAAK,CAACxoB,IAAI,KAAKrI,iBAAiB,CAAC8wB,WAAW,CAAC;QAC7F,MAAMC,sBAAsB,GAAGxQ,QAAQ,CAACyE,MAAM,CAAC6L,KAAK,IAAIA,KAAK,CAACxoB,IAAI,KAAKrI,iBAAiB,CAACkmB,YAAY,CAAC;QAEtG;QACA,IAAI,CAAC3c,gBAAgB,GAAG,CAAC,IAAI5I,kBAAkB,CAAC,EAAE,EAAEX,iBAAiB,CAAC4c,OAAO,EAAE,CAAC,IAAI,CAACD,YAAY,CAAC,CAAC,CAAC;QAEpG,IAAIiU,cAAc,CAAC1hB,MAAM,GAAG,CAAC,EAAE;UAC7B,IAAI,CAAC3F,gBAAgB,CAACiX,IAAI,CAAC,IAAI7f,kBAAkB,CAAC,OAAO,EAAEX,iBAAiB,CAAC8wB,WAAW,EAAE,CAAC,GAAGF,cAAc,CAAC,CAAC,CAAC;QACjH;QAEA,IAAIG,sBAAsB,CAAC7hB,MAAM,GAAG,CAAC,EAAE;UACrC,IAAI,CAAC3F,gBAAgB,CAACiX,IAAI,CAAC,IAAI7f,kBAAkB,CAAC,mBAAmB,EAAEX,iBAAiB,CAACkmB,YAAY,EAAE,CAAC,GAAG6K,sBAAsB,CAAC,CAAC,CAAC;QACtI;QAEA;QACA,MAAMpD,OAAO,GAAG,IAAI,CAACjkB,YAAY,CAACC,YAAY,EAAEC,gBAAgB,EAAEkE,EAAE,IAAI,IAAI,CAACpE,YAAY,CAACC,YAAY,EAAEqnB,aAAa;QACrH,IAAIrD,OAAO,EAAE;UACX,IAAI,CAACvlB,oBAAoB,GAAGmY,QAAQ,CAAClC,IAAI,CAACgF,CAAC,IAAIA,CAAC,CAACvV,EAAE,KAAK6f,OAAO,CAAC;UAChE,IAAI,CAACsD,2BAA2B,EAAE;QACpC;MACF;KACD,CAAC;EACJ;EAEAC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACzZ,eAAe,CAACyZ,iBAAiB,CAAC,IAAI,CAAC9lB,gBAAgB,CAAC;EACtE;EAEMwU,eAAeA,CAAA;IAAA,IAAAuR,MAAA;IAAA,OAAA5T,iBAAA;MACnB4T,MAAI,CAACC,cAAc,SAASD,MAAI,CAACD,iBAAiB,EAAE;MACpD,IAAIC,MAAI,CAACC,cAAc,EAAE;QACvB,MAAMC,KAAK,GAAe,CACxB;UACE7oB,KAAK,EAAE,GAAG2oB,MAAI,CAACC,cAAc,CAACE,OAAO,CAACzsB,IAAI,KAAKssB,MAAI,CAACC,cAAc,CAACvsB,IAAI,EAAE;UACzE0sB,UAAU,EAAE,YAAYJ,MAAI,CAAChmB,SAAS,IAAIgmB,MAAI,CAAC/lB,gBAAgB;SAChE,EACD;UACE5C,KAAK,EAAE,gBAAgB;UACvB+oB,UAAU,EAAE,YAAYJ,MAAI,CAAChmB,SAAS,IAAIgmB,MAAI,CAAC/lB,gBAAgB;SAChE,EACD;UAAE5C,KAAK,EAAE2oB,MAAI,CAACznB,YAAY,CAAC8P;QAAU,CAAE,CACxC;QAED2X,MAAI,CAACpZ,oBAAoB,CAACyZ,gBAAgB,CAACH,KAAK,CAAC;MACnD;IAAC;EACH;EAEAI,aAAaA,CAAA;IACX,IAAI,CAACC,UAAU,GAAG,IAAI,CAAChV,kBAAkB,CAAC5F,IAAI,CAACgY,CAAC,IAAIA,CAAC,CAACnB,OAAO,KAAK,IAAI,CAACjkB,YAAY,CAAC6P,QAAQ,IAAI,CAACuV,CAAC,CAAC6C,KAAK,CAAC;EAC3G;EAEAC,oBAAoBA,CAAA;IAClB,MAAMC,YAAY,GAAG,IAAI,CAACla,mBAAmB,CAACma,oBAAoB,CAAC,IAAI,CAACpoB,YAAY,CAACyB,SAAS,EAC5F,IAAI,CAACzB,YAAY,CAAC0B,gBAAgB,EAClCgc,MAAM,CAACC,QAAQ,CAAC0K,QAAQ,EACxB,IAAI,CAACroB,YAAY,CAAC6P,QAAQ,EAC1B,QAAQ,EACR,CAAC,IAAI,CAACmY,UAAU,EAChB,KAAK,CAAC;IAER,IAAI,CAAC3Z,oBAAoB,CAACuI,IAAI,CAAC;MAC7B6D,WAAW,EAAE,IAAI,CAACpM,oBAAoB,CAACqM,cAAc;MACrDC,SAAS,EAAE,IAAI,CAACtM,oBAAoB,CAACuM,iBAAiB;MACtDzD,SAAS,EAAE,oBAAoB;MAC/BM,IAAI,EAAE0Q;KACP,CAAC;EACJ;EAEAhS,kBAAkBA,CAAA;IAChB,IAAI,CAACM,eAAe,GAAG,IAAI,CAACpI,oBAAoB,CAAC8Z,YAAY,CAC1DxR,SAAS,CAACa,KAAK,IAAG;MACjB,IAAI,CAACxE,kBAAkB,GAAGwE,KAAK,CAACC,IAAI;MACpC,IAAI,CAACsQ,aAAa,EAAE;IACtB,CAAC,CAAC;EACN;EAEAtT,iBAAiBA,CAAA;IACf,IAAI,CAACtQ,WAAW,GAAG,IAAI,CAACnE,YAAY,EAAEC,YAAY,EAAEkE,WAAW,EAAEuT,GAAG,CAAC4Q,UAAU,KAAK;MAClFlkB,EAAE,EAAEkkB,UAAU,CAACC,MAAM;MACrBC,QAAQ,EAAEF,UAAU,CAACE,QAAqB;MAC1CrtB,IAAI,EAAEmtB,UAAU,CAACntB;KAClB,CAAC,CAAC;IAEH,IAAI,CAACsJ,YAAY,GAAG,IAAI,CAACzE,YAAY,CAACC,YAAY,EAAEwE,YAAY;EAClE;EAEAP,gBAAgBA,CAACC,WAAuB;IACtC,IAAI,IAAI,CAAC1J,eAAe,EAAE;MACxB;IACF;IAEA,IAAI,CAAC0J,WAAW,GAAGA,WAAW;IAE9B,IAAI,CAACnE,YAAY,CAACC,YAAY,CAACkE,WAAW,GAAGA,WAAW,CAACuT,GAAG,CAAC+Q,IAAI,KAAK;MACpEF,MAAM,EAAEE,IAAI,CAACrkB,EAAE;MACfokB,QAAQ,EAAEC,IAAI,CAACD,QAAQ;MACvBrtB,IAAI,EAAEstB,IAAI,CAACttB;KACZ,CAAC,CAAC;EACL;EAEAqJ,gBAAgBA,CAACC,YAA2B;IAC1C,IAAI,IAAI,CAAChK,eAAe,EAAE;MACxB;IACF;IAEA,IAAI,CAACgK,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACzE,YAAY,CAACC,YAAY,CAACwE,YAAY,GAAGA,YAAY;EAC5D;EAEA2R,0BAA0BA,CAAA;IACxB,IAAI,CAACM,uBAAuB,GAAG,IAAI,CAACjJ,kBAAkB,CAACib,sBAAsB,CAAC/R,SAAS,CAACmD,MAAM,IAAG;MAC/F,IAAIA,MAAM,EAAE;QACV,IAAI,CAAC9Z,YAAY,CAAC8Z,MAAM,GAAGA,MAAM;MACnC;IACF,CAAC,CAAC;EACJ;EAEA6O,oBAAoBA,CAAA;IAClB,IAAI,CAAC3a,WAAW,CAAC4a,kCAAkC,EAAE,CAACjS,SAAS,CAAC,EAAE,CAAC;EACrE;EAEAtX,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAAC5E,eAAe,EAAE;MACxB;IACF;IAEA,IAAI,CAACuF,YAAY,CAACC,YAAY,CAACC,gBAAgB,GAAG,IAAI,CAACxB,oBAAoB;IAE3E,IAAI,IAAI,CAACA,oBAAoB,CAACC,IAAI,KAAKrI,iBAAiB,CAACkmB,YAAY,EAAE;MACrE,IAAI,CAAC+K,2BAA2B,EAAE;IACpC;EACF;EAEAA,2BAA2BA,CAAA;IACzB,IAAI,CAACnnB,cAAc,GAAG,IAAI,CAACjC,iBAAiB,GACxCsb,MAAM,CAAC2J,IAAI,CAAC,IAAI,CAACjlB,iBAAiB,CAACmF,UAAU,CAACme,UAAU,CAAC,GACzD,EAAE;IAEN,IAAI,IAAI,CAACzhB,YAAY,CAACC,YAAY,EAAEC,gBAAgB,EAAEC,OAAO,IACxD,IAAI,CAACC,cAAc,CAACoiB,QAAQ,CAAC,IAAI,CAACxiB,YAAY,CAACC,YAAY,EAAEC,gBAAgB,EAAEC,OAAO,CAAC,IACvF,IAAI,CAACzB,oBAAoB,CAAC0F,EAAE,KAAK,IAAI,CAACpE,YAAY,CAACC,YAAY,EAAEC,gBAAgB,EAAEkE,EAAE,EAAE;MAC1F;IACF;IAEA,IAAI,IAAI,CAACpE,YAAY,CAACC,YAAY,EAAEC,gBAAgB,EAAE;MACpD,IAAI,CAACF,YAAY,CAACC,YAAY,CAACC,gBAAgB,CAACC,OAAO,GAAG,EAAE;IAC9D;EACF;EAEA4a,iCAAiCA,CAACvhB,WAAW;IAC3C,MAAMqvB,kBAAkB,GAAGrvB,WAAW,KAAK,MAAM,GAC7C;MACA2d,SAAS,EAAEphB,SAAS,CAAC8kB,SAAS,CAACiO,uBAAuB;MACtDrR,IAAI,EAAE;QAAEsR,OAAO,EAAEvvB,WAAW,CAAC4K,EAAE;QAAE6f,OAAO,EAAE,IAAI,CAACjkB,YAAY,CAAC6P;MAAQ;KACrE,GACC;MACAsH,SAAS,EAAEphB,SAAS,CAAC8kB,SAAS,CAACmO,0BAA0B;MACzDvR,IAAI,EAAE;QAAEwM,OAAO,EAAE,IAAI,CAACjkB,YAAY,CAAC6P;MAAQ;KAC5C;IAEH,IAAI,CAACxB,oBAAoB,CAACuI,IAAI,CAAC;MAC7B6D,WAAW,EAAE,IAAI,CAACpM,oBAAoB,CAACqM,cAAc;MACrDC,SAAS,EAAE,IAAI,CAACtM,oBAAoB,CAACuM,iBAAiB;MACtD,GAAGiO;KACJ,CAAC;EACJ;EAEMllB,oBAAoBA,CAAA;IAAA,IAAAslB,MAAA;IAAA,OAAApV,iBAAA;MACxB,IAAI,CAACoV,MAAI,CAACjpB,YAAY,CAACqD,kBAAkB,EAAE;QACzC4lB,MAAI,CAACjpB,YAAY,CAACqD,kBAAkB,GAAG,IAAI1K,kBAAkB,EAAE;MACjE;MAEAswB,MAAI,CAACjpB,YAAY,CAACqD,kBAAkB,CAAC6Y,UAAU,GAAG+M,MAAI,CAACrrB,gBAAgB,CAAC2U,aAAa,EAAE/M,MAAM,GAAG,CAAC,SAASyjB,MAAI,CAACC,kBAAkB,EAAE,GAAG,MAAM;IAAC;EAC/I;EAEMA,kBAAkBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAtV,iBAAA;MACtB,IAAIsV,MAAI,CAACvrB,gBAAgB,CAACL,MAAM,KAAK,GAAG,EAAE;QAAE,OAAO,MAAM;MAAE;MAE3D,aAAa4rB,MAAI,CAAC/a,aAAa,CAAC+N,UAAU,CAACgN,MAAI,CAACvrB,gBAAgB,CAAC2U,aAAa,EAAE6J,IAAI,EAAE,EAAEhD,QAAQ,EAAE,CAAC;IAAA;EACrG;;;uBAn1DWjM,uBAAuB,EAAApU,EAAA,CAAAqwB,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAvwB,EAAA,CAAAqwB,iBAAA,CAAAG,EAAA,CAAAC,iBAAA,GAAAzwB,EAAA,CAAAqwB,iBAAA,CAAAK,EAAA,CAAAC,mBAAA,GAAA3wB,EAAA,CAAAqwB,iBAAA,CAAAO,EAAA,CAAAC,mBAAA,GAAA7wB,EAAA,CAAAqwB,iBAAA,CAAArwB,EAAA,CAAA8wB,SAAA,GAAA9wB,EAAA,CAAAqwB,iBAAA,CAAArwB,EAAA,CAAA+wB,MAAA,GAAA/wB,EAAA,CAAAqwB,iBAAA,CAAAW,EAAA,CAAAC,eAAA,GAAAjxB,EAAA,CAAAqwB,iBAAA,CAAAa,EAAA,CAAAC,WAAA,GAAAnxB,EAAA,CAAAqwB,iBAAA,CAAAe,EAAA,CAAAC,mBAAA,GAAArxB,EAAA,CAAAqwB,iBAAA,CAAAiB,EAAA,CAAAC,kBAAA,GAAAvxB,EAAA,CAAAqwB,iBAAA,CAAArwB,EAAA,CAAAwxB,iBAAA,GAAAxxB,EAAA,CAAAqwB,iBAAA,CAAAoB,EAAA,CAAAC,aAAA,GAAA1xB,EAAA,CAAAqwB,iBAAA,CAyQxBpzB,kBAAkB;IAAA;EAAA;;;YAzQjBmX,uBAAuB;MAAAud,SAAA;MAAAC,MAAA;QAAA3qB,YAAA;QAAAyB,SAAA;QAAAC,gBAAA;QAAAkpB,UAAA;QAAA7J,UAAA;QAAA7d,QAAA;QAAAzI,eAAA;QAAA4P,gBAAA;MAAA;MAAAwgB,OAAA;QAAAvc,eAAA;QAAAC,gBAAA;MAAA;MAAAuc,UAAA;MAAAC,QAAA,GAAAhyB,EAAA,CAAAiyB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UChF9BvyB,EAHN,CAAAY,cAAA,oBAAqB,mBACwB,aACN,SAC7B;UAAAZ,EAAA,CAAAC,SAAA,cAA6C;UAACD,EAAA,CAAAM,MAAA,GAE/B;UACrBN,EADqB,CAAAuB,YAAA,EAAK,EACpB;UAEJvB,EADF,CAAAY,cAAA,aAA4C,kBAGL;UAAnCZ,EAAA,CAAA0C,UAAA,qBAAA+vB,6DAAA;YAAAzyB,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA,OAAA1yB,EAAA,CAAAoB,WAAA,CAAWoxB,GAAA,CAAArD,oBAAA,EAAsB;UAAA,EAAC;UAACnvB,EAAA,CAAAuB,YAAA,EAAW;UAChDvB,EAAA,CAAAqB,UAAA,IAAAsxB,8CAAA,+BAAyD;UAIvD3yB,EADF,CAAAY,cAAA,eAAyF,oBAEtC;UADwBZ,EAAA,CAAA0C,UAAA,mBAAAkwB,4DAAA;YAAA5yB,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA,OAAA1yB,EAAA,CAAAoB,WAAA,CAASoxB,GAAA,CAAAtT,WAAA,EAAa;UAAA,EAAC;UAItGlf,EAHuD,CAAAuB,YAAA,EAAW,EACvD,EACH,EACI;UAEZvB,EAAA,CAAAY,cAAA,UAAI;UAAAZ,EAAA,CAAAM,MAAA,wBAAgB;UAAAN,EAAA,CAAAuB,YAAA,EAAK;UACzBvB,EAAA,CAAAY,cAAA,eAAuB;UACrBZ,EAAA,CAAAqB,UAAA,KAAAwxB,6CAAA,wBAC2E;UAGvE7yB,EAFJ,CAAAY,cAAA,eAA6B,eACO,iBAEgC;UADzCZ,EAAA,CAAAa,gBAAA,2BAAAiyB,iEAAA/xB,MAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA1yB,EAAA,CAAAmB,kBAAA,CAAAqxB,GAAA,CAAAvrB,YAAA,CAAA8P,UAAA,EAAAhW,MAAA,MAAAyxB,GAAA,CAAAvrB,YAAA,CAAA8P,UAAA,GAAAhW,MAAA;YAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;UAAA,EAAqC;UAA5Df,EAAA,CAAAuB,YAAA,EACgE;UAChEvB,EAAA,CAAAY,cAAA,iBAAwB;UAAAZ,EAAA,CAAAM,MAAA,IAA6E;UAAAN,EAAA,CAAAY,cAAA,gBAC1E;UAAAZ,EAAA,CAAAM,MAAA,SAAC;UAC9BN,EAD8B,CAAAuB,YAAA,EAAO,EAAQ,EACvC;UAEJvB,EADF,CAAAY,cAAA,eAAkC,iBAEC;UADTZ,EAAA,CAAAa,gBAAA,2BAAAkyB,iEAAAhyB,MAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA1yB,EAAA,CAAAmB,kBAAA,CAAAqxB,GAAA,CAAAvrB,YAAA,CAAA+P,WAAA,EAAAjW,MAAA,MAAAyxB,GAAA,CAAAvrB,YAAA,CAAA+P,WAAA,GAAAjW,MAAA;YAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;UAAA,EAAsC;UAA9Df,EAAA,CAAAuB,YAAA,EACiC;UACjCvB,EAAA,CAAAY,cAAA,iBAAyB;UAAAZ,EAAA,CAAAM,MAAA,oBAAY;UAAAN,EAAA,CAAAY,cAAA,gBAA6B;UAAAZ,EAAA,CAAAM,MAAA,SAAC;UACrEN,EADqE,CAAAuB,YAAA,EAAO,EAAQ,EAC9E;UAWNvB,EAVA,CAAAqB,UAAA,KAAA2xB,uCAAA,kBAAuD,KAAAC,uCAAA,kBAUwC;UAS7FjzB,EADF,CAAAY,cAAA,eAAoB,sBAEiD;UAAjEZ,EAAA,CAAAa,gBAAA,2BAAAqyB,sEAAAnyB,MAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA1yB,EAAA,CAAAmB,kBAAA,CAAAqxB,GAAA,CAAAvrB,YAAA,CAAAkQ,QAAA,EAAApW,MAAA,MAAAyxB,GAAA,CAAAvrB,YAAA,CAAAkQ,QAAA,GAAApW,MAAA;YAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;UAAA,EAAmC;UAEvCf,EADE,CAAAuB,YAAA,EAAa,EACT;UAGJvB,EADF,CAAAY,cAAA,eAA2D,gBACS;UAAAZ,EAAA,CAAAM,MAAA,IAAyB;UAAAN,EAAA,CAAAuB,YAAA,EAAO;UAClGvB,EAAA,CAAAY,cAAA,iBAA+B;UAAAZ,EAAA,CAAAM,MAAA,iBAAS;UAG9CN,EAH8C,CAAAuB,YAAA,EAAQ,EAC5C,EACF,EACF;UAGFvB,EAFJ,CAAAY,cAAA,uBAA+B,0BACkC,eAC3C;UAChBZ,EAAA,CAAAqB,UAAA,KAAA8xB,uCAAA,kBAA0F;UAS1FnzB,EAAA,CAAAY,cAAA,eAA+B;UAyB7BZ,EAxBA,CAAAqB,UAAA,KAAA+xB,gDAAA,4BAA+D,KAAAC,gDAAA,2BAgBD,KAAAC,gDAAA,2BAQH;UAqCjEtzB,EAFI,CAAAuB,YAAA,EAAM,EACF,EACS;UA4WjBvB,EA1WA,CAAAqB,UAAA,KAAAkyB,kDAAA,+BAAoH,KAAAC,kDAAA,6BAuFR,KAAAC,kDAAA,8BAgCD,KAAAC,+CAAA,6BAiB1G,KAAAC,kDAAA,6BAsBmD,KAAAC,kDAAA,6BAUH,KAAAC,kDAAA,+BAO8C,KAAAC,kDAAA,6BA4BK,KAAAC,kDAAA,6BAqDjC,KAAAC,+CAAA,+BA0GY;UAkCzEh0B,EAHN,CAAAY,cAAA,0BAAoD,eACd,eACa,iBAEtB;UADEZ,EAAA,CAAAa,gBAAA,2BAAAozB,iEAAAlzB,MAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA1yB,EAAA,CAAAmB,kBAAA,CAAAqxB,GAAA,CAAAvrB,YAAA,CAAAgQ,GAAA,EAAAlW,MAAA,MAAAyxB,GAAA,CAAAvrB,YAAA,CAAAgQ,GAAA,GAAAlW,MAAA;YAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;UAAA,EAA8B;UAAvDf,EAAA,CAAAuB,YAAA,EACuB;UACvBvB,EAAA,CAAAY,cAAA,iBAAyB;UAAAZ,EAAA,CAAAM,MAAA,IAA8B;UAAAN,EAAA,CAAAuB,YAAA,EAAQ;UAE7DvB,EADF,CAAAY,cAAA,gBAAiC,aACmC;UAA5CZ,EAAA,CAAA0C,UAAA,mBAAAwxB,qDAAA;YAAAl0B,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA,OAAA1yB,EAAA,CAAAoB,WAAA,CAASoxB,GAAA,CAAAzO,eAAA,CAAAyO,GAAA,CAAAvrB,YAAA,CAAAgQ,GAAA,CAAiC;UAAA,EAAC;UAErEjX,EAFsE,CAAAuB,YAAA,EAAI,EACjE,EACH;UAEJvB,EADF,CAAAY,cAAA,eAA+C,iBAExB;UAD4CZ,EAAA,CAAAa,gBAAA,2BAAAszB,iEAAApzB,MAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA1yB,EAAA,CAAAmB,kBAAA,CAAAqxB,GAAA,CAAAvrB,YAAA,CAAAiQ,QAAA,EAAAnW,MAAA,MAAAyxB,GAAA,CAAAvrB,YAAA,CAAAiQ,QAAA,GAAAnW,MAAA;YAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;UAAA,EAAmC;UAApGf,EAAA,CAAAuB,YAAA,EACqB;UACrBvB,EAAA,CAAAY,cAAA,iBAAyB;UAAAZ,EAAA,CAAAM,MAAA,IAA8B;UAAAN,EAAA,CAAAuB,YAAA,EAAQ;UAE7DvB,EADF,CAAAY,cAAA,gBAAiC,aACwC;UAAjDZ,EAAA,CAAA0C,UAAA,mBAAA0xB,qDAAA;YAAAp0B,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA,OAAA1yB,EAAA,CAAAoB,WAAA,CAASoxB,GAAA,CAAAzO,eAAA,CAAAyO,GAAA,CAAAvrB,YAAA,CAAAiQ,QAAA,CAAsC;UAAA,EAAC;UAI9ElX,EAJ+E,CAAAuB,YAAA,EAAI,EACtE,EACH,EACF,EACS;UAGfvB,EADF,CAAAY,cAAA,0BAAqD,eAC3B;UACtBZ,EAAA,CAAAqB,UAAA,KAAAgzB,4CAAA,uBAC4D;UAE1Dr0B,EADF,CAAAY,cAAA,eAAyB,sBAEQ;UADLZ,EAAA,CAAAa,gBAAA,2BAAAyzB,sEAAAvzB,MAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA1yB,EAAA,CAAAmB,kBAAA,CAAAqxB,GAAA,CAAAlc,cAAA,EAAAvV,MAAA,MAAAyxB,GAAA,CAAAlc,cAAA,GAAAvV,MAAA;YAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;UAAA,EAA4B;UAGxDf,EADE,CAAAuB,YAAA,EAAa,EACT;UAGJvB,EAFF,CAAAY,cAAA,eAC+E,sBAGZ;UAA3CZ,EAAA,CAAAa,gBAAA,2BAAA0zB,sEAAAxzB,MAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA1yB,EAAA,CAAAmB,kBAAA,CAAAqxB,GAAA,CAAAnc,YAAA,EAAAtV,MAAA,MAAAyxB,GAAA,CAAAnc,YAAA,GAAAtV,MAAA;YAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;UAAA,EAA0B;UAChDf,EAAA,CAAAuB,YAAA,EAAa;UACbvB,EAAA,CAAAY,cAAA,iBAAmB;UAAAZ,EAAA,CAAAM,MAAA,kBAAU;UAAAN,EAAA,CAAAuB,YAAA,EAAQ;UACrCvB,EAAA,CAAAC,SAAA,qBACwJ;UAG9JD,EAFI,CAAAuB,YAAA,EAAM,EACF,EACS;UAIbvB,EAFJ,CAAAY,cAAA,0BAA2D,eACjC,eACY;UAChCZ,EAAA,CAAAC,SAAA,2BACkB;UAClBD,EAAA,CAAAY,cAAA,iBACyG;UAD3EZ,EAAA,CAAAa,gBAAA,2BAAA2zB,iEAAAzzB,MAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA1yB,EAAA,CAAAmB,kBAAA,CAAAqxB,GAAA,CAAAzW,yBAAA,EAAAhb,MAAA,MAAAyxB,GAAA,CAAAzW,yBAAA,GAAAhb,MAAA;YAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;UAAA,EAAuC;UACxBf,EAAhC,CAAA0C,UAAA,sBAAA+xB,4DAAA1zB,MAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA,OAAA1yB,EAAA,CAAAoB,WAAA,CAAYoxB,GAAA,CAAAngB,UAAA,CAAAtR,MAAA,CAAkB;UAAA,EAAC,kBAAA2zB,wDAAA;YAAA10B,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA,OAAA1yB,EAAA,CAAAoB,WAAA,CAASoxB,GAAA,CAAAjgB,iBAAA,EAAmB;UAAA,EAAC;UADzEvS,EAAA,CAAAuB,YAAA,EACyG;UACzGvB,EAAA,CAAAY,cAAA,iBAA+B;UAAAZ,EAAA,CAAAM,MAAA,4CAAoC;UAAAN,EAAA,CAAAuB,YAAA,EAAQ;UAC3EvB,EAAA,CAAAY,cAAA,oBACuC;UADEZ,EAAA,CAAAa,gBAAA,2BAAA8zB,oEAAA5zB,MAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA1yB,EAAA,CAAAmB,kBAAA,CAAAqxB,GAAA,CAAA7Y,YAAA,EAAA5Y,MAAA,MAAAyxB,GAAA,CAAA7Y,YAAA,GAAA5Y,MAAA;YAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;UAAA,EAA0B;UACjEf,EAAA,CAAA0C,UAAA,oBAAAkyB,6DAAA;YAAA50B,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA,OAAA1yB,EAAA,CAAAoB,WAAA,CAAUoxB,GAAA,CAAAtM,SAAA,EAAW;UAAA,EAAC;UACtBlmB,EAAA,CAAAM,MAAA,+GACA;UACEN,EADF,CAAAY,cAAA,gBAAU,oBACsF;UAAtEZ,EAAA,CAAA0C,UAAA,mBAAAmyB,4DAAA;YAAA70B,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA,OAAA1yB,EAAA,CAAAoB,WAAA,CAASoxB,GAAA,CAAAtM,SAAA,EAAW;UAAA,EAAC;UAKvDlmB,EALwG,CAAAuB,YAAA,EAAW,EAChG,EACF,EACP,EACF,EACS;UAEjBvB,EAAA,CAAAqB,UAAA,KAAAyzB,kDAAA,6BAAqF;UAczF90B,EAFE,CAAAuB,YAAA,EAAc,EAET;UAGPvB,EAAA,CAAAY,cAAA,oBACiB;UADgBZ,EAAA,CAAAa,gBAAA,2BAAAk0B,oEAAAh0B,MAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA1yB,EAAA,CAAAmB,kBAAA,CAAAqxB,GAAA,CAAA3f,YAAA,EAAA9R,MAAA,MAAAyxB,GAAA,CAAA3f,YAAA,GAAA9R,MAAA;YAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;UAAA,EAA0B;UAGvDf,EADF,CAAAY,cAAA,eAAkB,SACb;UAAAZ,EAAA,CAAAM,MAAA,4CAAoC;UACzCN,EADyC,CAAAuB,YAAA,EAAI,EACvC;UACNvB,EAAA,CAAAqB,UAAA,KAAA2zB,+CAAA,0BAAgC;UAMlCh1B,EAAA,CAAAuB,YAAA,EAAW;UAEXvB,EAAA,CAAAY,cAAA,oBACiB;UAD0BZ,EAAA,CAAAa,gBAAA,2BAAAo0B,oEAAAl0B,MAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA1yB,EAAA,CAAAmB,kBAAA,CAAAqxB,GAAA,CAAAre,sBAAA,EAAApT,MAAA,MAAAyxB,GAAA,CAAAre,sBAAA,GAAApT,MAAA;YAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;UAAA,EAAoC;UAMrEf,EAJR,CAAAY,cAAA,eAAkB,eACQ,gBACH,gBACc,0BACgF;UAA7DZ,EAAA,CAAAa,gBAAA,2BAAAq0B,0EAAAn0B,MAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA1yB,EAAA,CAAAmB,kBAAA,CAAAqxB,GAAA,CAAA3Y,UAAA,EAAA9Y,MAAA,MAAAyxB,GAAA,CAAA3Y,UAAA,GAAA9Y,MAAA;YAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;UAAA,EAAwB;UACxEf,EAAA,CAAAuB,YAAA,EAAgB;UAChBvB,EAAA,CAAAY,cAAA,kBAA0B;UAAAZ,EAAA,CAAAM,MAAA,eAAM;UAEpCN,EAFoC,CAAAuB,YAAA,EAAQ,EACpC,EACF;UAGFvB,EAFJ,CAAAY,cAAA,gBAAmB,gBACc,0BAEK;UADuBZ,EAAA,CAAAa,gBAAA,2BAAAs0B,0EAAAp0B,MAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA1yB,EAAA,CAAAmB,kBAAA,CAAAqxB,GAAA,CAAA3Y,UAAA,EAAA9Y,MAAA,MAAAyxB,GAAA,CAAA3Y,UAAA,GAAA9Y,MAAA;YAAA,OAAAf,EAAA,CAAAoB,WAAA,CAAAL,MAAA;UAAA,EAAwB;UAEjFf,EAAA,CAAAuB,YAAA,EAAgB;UAChBvB,EAAA,CAAAY,cAAA,kBAAmC;UAAAZ,EAAA,CAAAM,MAAA,yBAAgB;UAEvDN,EAFuD,CAAAuB,YAAA,EAAQ,EACvD,EACF;UAiBNvB,EAhBA,CAAAqB,UAAA,MAAA+zB,wCAAA,kBAAgE,MAAAC,wCAAA,kBAgBS;UAmB7Er1B,EADE,CAAAuB,YAAA,EAAM,EACF;UACNvB,EAAA,CAAAqB,UAAA,MAAAi0B,gDAAA,0BAAgC;UAKlCt1B,EAAA,CAAAuB,YAAA,EAAW;UACXvB,EAAA,CAAAY,cAAA,kCAKgE;UAA9DZ,EAAA,CAAA0C,UAAA,kCAAA6yB,yFAAAx0B,MAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAA0xB,GAAA;YAAA,OAAA1yB,EAAA,CAAAoB,WAAA,CAAwBoxB,GAAA,CAAA3E,4BAAA,CAAA9sB,MAAA,CAAoC;UAAA,EAAC;UAC/Df,EAAA,CAAAuB,YAAA,EAAwB;;;UAprBgCvB,EAAA,CAAAwB,SAAA,GAE/B;UAF+BxB,EAAA,CAAAO,kBAAA,MAAAiyB,GAAA,CAAAvrB,YAAA,CAAAwO,UAAA,gEAE/B;UAGTzV,EAAA,CAAAwB,SAAA,GAAgB;UACxBxB,EADQ,CAAAK,UAAA,iBAAgB,SAAAmyB,GAAA,CAAAvD,UAAA,oCAAuD,aAAAuD,GAAA,CAAAvD,UAAA,sCACnB;UAE9DjvB,EAAA,CAAAwB,SAAA,EAEC;UAFDxB,EAAA,CAAAgL,aAAA,IAAAwnB,GAAA,CAAAvrB,YAAA,CAAAwO,UAAA,+BAEC;UACKzV,EAAA,CAAAwB,SAAA,EAA4D;UAA5DxB,EAAA,CAAAE,qBAAA,aAAAsyB,GAAA,CAAA9wB,eAAA,gCAA4D;UACtD1B,EAAA,CAAAwB,SAAA,EAAgB;UACxBxB,EADQ,CAAAK,UAAA,iBAAgB,aAAAmyB,GAAA,CAAAhc,cAAA,IAAAgc,GAAA,CAAA9wB,eAAA,CACsB;UAOxC1B,EAAA,CAAAwB,SAAA,GAAc;UAAdxB,EAAA,CAAAK,UAAA,SAAAmyB,GAAA,CAAAroB,QAAA,CAAc;UAICnK,EAAA,CAAAwB,SAAA,GAAqC;UAArCxB,EAAA,CAAAyB,gBAAA,YAAA+wB,GAAA,CAAAvrB,YAAA,CAAA8P,UAAA,CAAqC;UAC3B/W,EAA/B,CAAAK,UAAA,eAAAmyB,GAAA,CAAAtc,eAAA,CAA8B,aAAAsc,GAAA,CAAA9wB,eAAA,CAA6B;UACrC1B,EAAA,CAAAwB,SAAA,GAA6E;UAA7ExB,EAAA,CAAAmC,iBAAA,CAAAqwB,GAAA,CAAAvrB,YAAA,CAAAwO,UAAA,+CAA6E;UAI7EzV,EAAA,CAAAwB,SAAA,GAAsC;UAAtCxB,EAAA,CAAAyB,gBAAA,YAAA+wB,GAAA,CAAAvrB,YAAA,CAAA+P,WAAA,CAAsC;UAC5DhX,EAAA,CAAAK,UAAA,aAAAmyB,GAAA,CAAA9wB,eAAA,CAA4B;UAG1B1B,EAAA,CAAAwB,SAAA,GAAkB;UAAlBxB,EAAA,CAAAK,UAAA,SAAAmyB,GAAA,CAAAnY,YAAA,CAAkB;UAUlBra,EAAA,CAAAwB,SAAA,EAAuD;UAAvDxB,EAAA,CAAAK,UAAA,SAAAmyB,GAAA,CAAAvrB,YAAA,CAAAwO,UAAA,sBAAuD;UASPzV,EAAA,CAAAwB,SAAA,GAA8B;UAA9BxB,EAAA,CAAAK,UAAA,UAAAmyB,GAAA,CAAA1nB,YAAA,kBAAA0nB,GAAA,CAAA1nB,YAAA,CAAAkO,MAAA,CAA8B;UAChFhZ,EAAA,CAAAyB,gBAAA,YAAA+wB,GAAA,CAAAvrB,YAAA,CAAAkQ,QAAA,CAAmC;UAACnX,EAAA,CAAAK,UAAA,aAAAmyB,GAAA,CAAA9wB,eAAA,CAA4B;UAKA1B,EAAA,CAAAwB,SAAA,GAAyB;UAAzBxB,EAAA,CAAAmC,iBAAA,CAAAqwB,GAAA,CAAAvrB,YAAA,CAAA6P,QAAA,CAAyB;UAKpF9W,EAAA,CAAAwB,SAAA,GAAiB;UAAjBxB,EAAA,CAAAK,UAAA,kBAAiB;UACZL,EAAA,CAAAwB,SAAA,EAAiB;UAAjBxB,EAAA,CAAAK,UAAA,kBAAiB;UAEvBL,EAAA,CAAAwB,SAAA,GAAuD;UAAvDxB,EAAA,CAAAK,UAAA,SAAAmyB,GAAA,CAAAvrB,YAAA,CAAAwO,UAAA,sBAAuD;UAU5CzV,EAAA,CAAAwB,SAAA,GAA8C;UAA9CxB,EAAA,CAAAK,UAAA,UAAAmyB,GAAA,CAAA/vB,qBAAA,kBAAA+vB,GAAA,CAAA/vB,qBAAA,CAAAP,GAAA,kBAA8C;UAgB9ClC,EAAA,CAAAwB,SAAA,EAA6C;UAA7CxB,EAAA,CAAAK,UAAA,UAAAmyB,GAAA,CAAA/vB,qBAAA,kBAAA+vB,GAAA,CAAA/vB,qBAAA,CAAAP,GAAA,iBAA6C;UAQ7ClC,EAAA,CAAAwB,SAAA,EAA0C;UAA1CxB,EAAA,CAAAK,UAAA,UAAAmyB,GAAA,CAAA/vB,qBAAA,kBAAA+vB,GAAA,CAAA/vB,qBAAA,CAAAP,GAAA,cAA0C;UAuC9ClC,EAAA,CAAAwB,SAAA,EAAuD;UAAvDxB,EAAA,CAAAK,UAAA,SAAAmyB,GAAA,CAAAvrB,YAAA,CAAAwO,UAAA,sBAAuD;UAuFRzV,EAAA,CAAAwB,SAAA,EAA0C;UAA1CxB,EAAA,CAAAK,UAAA,UAAAmyB,GAAA,CAAA/vB,qBAAA,kBAAA+vB,GAAA,CAAA/vB,qBAAA,CAAAP,GAAA,cAA0C;UA+BvGlC,EAAA,CAAAwB,SAAA,EAAqG;UAArGxB,EAAA,CAAAK,UAAA,SAAAmyB,GAAA,CAAAvrB,YAAA,CAAAwO,UAAA,KAAA+c,GAAA,CAAA/c,UAAA,CAAAkG,cAAA,IAAA6W,GAAA,CAAA5pB,4BAAA,CAAqG;UAiBxG5I,EAAA,CAAAwB,SAAA,EAmBC;UAnBDxB,EAAA,CAAAgL,aAAA,MAAAwnB,GAAA,CAAAvrB,YAAA,kBAAAurB,GAAA,CAAAvrB,YAAA,CAAAwO,UAAA,MAAA+c,GAAA,CAAA/c,UAAA,CAAAkG,cAAA,IAAA6W,GAAA,CAAA1S,gBAAA,WAmBC;UAGE9f,EAAA,CAAAwB,SAAA,EAAkH;UAAlHxB,EAAA,CAAAK,UAAA,SAAAmyB,GAAA,CAAAvrB,YAAA,CAAAwO,UAAA,KAAA+c,GAAA,CAAA/c,UAAA,CAAAkG,cAAA,IAAA6W,GAAA,CAAA/L,iBAAA,IAAA+L,GAAA,CAAAvrB,YAAA,CAAAC,YAAA,CAAkH;UAUlHlH,EAAA,CAAAwB,SAAA,EAA+G;UAA/GxB,EAAA,CAAAK,UAAA,SAAAmyB,GAAA,CAAAvrB,YAAA,CAAAwO,UAAA,KAAA+c,GAAA,CAAA/c,UAAA,CAAAkG,cAAA,IAAA6W,GAAA,CAAA7L,cAAA,IAAA6L,GAAA,CAAAvrB,YAAA,CAAAC,YAAA,CAA+G;UAQvDlH,EAAA,CAAAwB,SAAA,EAAkC;UAAlCxB,EAAA,CAAAK,UAAA,SAAAmyB,GAAA,CAAAxwB,gBAAA,aAAkC;UA4BzBhC,EAAA,CAAAwB,SAAA,EAA8B;UAA9BxB,EAAA,CAAAK,UAAA,SAAAmyB,GAAA,CAAAvkB,aAAA,CAAAxB,MAAA,KAA8B;UAqD/FzM,EAAA,CAAAwB,SAAA,EAA8D;UAA9DxB,EAAA,CAAAK,UAAA,SAAAmyB,GAAA,CAAA3hB,UAAA,CAAApE,MAAA,QAAA+lB,GAAA,CAAAxwB,gBAAA,YAA8D;UA0GjEhC,EAAA,CAAAwB,SAAA,EA8BC;UA9BDxB,EAAA,CAAAgL,aAAA,KAAAwnB,GAAA,CAAAvrB,YAAA,CAAAwO,UAAA,yBAAA+c,GAAA,CAAAjY,iBAAA,WA8BC;UACeva,EAAA,CAAAwB,SAAA,EAAkB;UAAlBxB,EAAA,CAAAK,UAAA,mBAAkB;UAGrBL,EAAA,CAAAwB,SAAA,GAAiB;UAAjBxB,EAAA,CAAAK,UAAA,kBAAiB;UAACL,EAAA,CAAAyB,gBAAA,YAAA+wB,GAAA,CAAAvrB,YAAA,CAAAgQ,GAAA,CAA8B;UAE9BjX,EAAA,CAAAwB,SAAA,GAA8B;UAA9BxB,EAAA,CAAAmC,iBAAA,CAAAqwB,GAAA,CAAA1nB,YAAA,CAAAqO,WAAA,CAA8B;UAMhDnZ,EAAA,CAAAwB,SAAA,GAAiB;UAAjBxB,EAAA,CAAAK,UAAA,kBAAiB;UAAyCL,EAAA,CAAAyB,gBAAA,YAAA+wB,GAAA,CAAAvrB,YAAA,CAAAiQ,QAAA,CAAmC;UAE3ElX,EAAA,CAAAwB,SAAA,GAA8B;UAA9BxB,EAAA,CAAAmC,iBAAA,CAAAqwB,GAAA,CAAA1nB,YAAA,CAAAsO,WAAA,CAA8B;UAQ7CpZ,EAAA,CAAAwB,SAAA,GAAkB;UAAlBxB,EAAA,CAAAK,UAAA,mBAAkB;UAEnBL,EAAA,CAAAwB,SAAA,GAA2B;UAA3BxB,EAAA,CAAAK,UAAA,SAAAmyB,GAAA,CAAA9V,qBAAA,CAA2B;UAGV1c,EAAA,CAAAwB,SAAA,GAA4B;UAA5BxB,EAAA,CAAAyB,gBAAA,YAAA+wB,GAAA,CAAAlc,cAAA,CAA4B;UACpDtW,EAAA,CAAAK,UAAA,aAAAmyB,GAAA,CAAA9wB,eAAA,CAA4B;UAI9B1B,EAAA,CAAAwB,SAAA,EAA4E;UAA5ExB,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAyF,eAAA,KAAA+vB,GAAA,GAAAhD,GAAA,CAAAlc,cAAA,GAAAkc,GAAA,CAAAlc,cAAA,EAA4E;UACnCtW,EAAA,CAAAwB,SAAA,EAA+C;UACtBxB,EADzB,CAAAK,UAAA,cAAAmyB,GAAA,CAAAlc,cAAA,IAAAkc,GAAA,CAAA9wB,eAAA,CAA+C,YAAA1B,EAAA,CAAA4B,eAAA,KAAAC,GAAA,GAAA2wB,GAAA,CAAAlc,cAAA,IAAAkc,GAAA,CAAA9wB,eAAA,EACvB,YAAA8wB,GAAA,CAAApc,KAAA,CAAkB;UAC7DpW,EAAA,CAAAyB,gBAAA,YAAA+wB,GAAA,CAAAnc,YAAA,CAA0B;UAACrW,EAAA,CAAAK,UAAA,gBAAe;UAStDL,EAAA,CAAAwB,SAAA,GAAkB;UAAlBxB,EAAA,CAAAK,UAAA,mBAAkB;UAGXL,EAAA,CAAAwB,SAAA,GAAyB;UAAzBxB,EAAA,CAAA0P,UAAA,CAAA1P,EAAA,CAAA2P,eAAA,KAAA6C,GAAA,EAAyB;UAEZxS,EAAA,CAAAwB,SAAA,EAAuC;UAAvCxB,EAAA,CAAAyB,gBAAA,YAAA+wB,GAAA,CAAAzW,yBAAA,CAAuC;UACK/b,EAAA,CAAAK,UAAA,aAAAmyB,GAAA,CAAA9wB,eAAA,CAA4B;UAElC1B,EAAA,CAAAwB,SAAA,GAAyB;UAAzBxB,EAAA,CAAA0P,UAAA,CAAA1P,EAAA,CAAA2P,eAAA,KAAA6C,GAAA,EAAyB;UAApDxS,EAAA,CAAAyB,gBAAA,YAAA+wB,GAAA,CAAA7Y,YAAA,CAA0B;UAIa3Z,EAAA,CAAAwB,SAAA,GAAiB;UAAjBxB,EAAA,CAAAK,UAAA,kBAAiB;UAO3CL,EAAA,CAAAwB,SAAA,EAAuB;UAAvBxB,EAAA,CAAAK,UAAA,SAAAmyB,GAAA,CAAAzc,iBAAA,CAAuB;UAiB3B/V,EAAA,CAAAwB,SAAA,EAAyB;UAAzBxB,EAAA,CAAA0P,UAAA,CAAA1P,EAAA,CAAA2P,eAAA,KAAA8lB,GAAA,EAAyB;UAApDz1B,EAAA,CAAAyB,gBAAA,YAAA+wB,GAAA,CAAA3f,YAAA,CAA0B;UACzD7S,EADoF,CAAAK,UAAA,qBAAoB,eAC1F;UAYgEL,EAAA,CAAAwB,SAAA,GAAyB;UAAzBxB,EAAA,CAAA0P,UAAA,CAAA1P,EAAA,CAAA2P,eAAA,KAAA+lB,GAAA,EAAyB;UAA9D11B,EAAA,CAAAyB,gBAAA,YAAA+wB,GAAA,CAAAre,sBAAA,CAAoC;UAC7EnU,EAAA,CAAAK,UAAA,eAAc;UAK0CL,EAAA,CAAAwB,SAAA,GAAwB;UAAxBxB,EAAA,CAAAyB,gBAAA,YAAA+wB,GAAA,CAAA3Y,UAAA,CAAwB;UAOf7Z,EAAA,CAAAwB,SAAA,GAAwB;UAAxBxB,EAAA,CAAAyB,gBAAA,YAAA+wB,GAAA,CAAA3Y,UAAA,CAAwB;UAM/E7Z,EAAA,CAAAwB,SAAA,GAA2B;UAA3BxB,EAAA,CAAAK,UAAA,SAAAmyB,GAAA,CAAA3Y,UAAA,cAA2B;UAgB3B7Z,EAAA,CAAAwB,SAAA,EAAoC;UAApCxB,EAAA,CAAAK,UAAA,SAAAmyB,GAAA,CAAA3Y,UAAA,uBAAoC;UA0BzB7Z,EAAA,CAAAwB,SAAA,GAAuC;UAI5DxB,EAJqB,CAAAK,UAAA,eAAAmyB,GAAA,CAAAzY,wBAAA,CAAuC,gBAAAyY,GAAA,CAAAvrB,YAAA,kBAAAurB,GAAA,CAAAvrB,YAAA,CAAA8P,UAAA,CAAyC,iBAAAyb,GAAA,CAAAvrB,YAAA,kBAAAurB,GAAA,CAAAvrB,YAAA,CAAA+P,WAAA,CAC3D,cAAAwb,GAAA,CAAA9pB,SAAA,CAAwB,qBAAA8pB,GAAA,CAAA7pB,gBAAA,CAAsC,gCAAA6pB,GAAA,CAAAvrB,YAAA,kBAAAurB,GAAA,CAAAvrB,YAAA,CAAAC,YAAA,kBAAAsrB,GAAA,CAAAvrB,YAAA,CAAAC,YAAA,CAAAib,2BAAA,CACjB,gBAAAqQ,GAAA,CAAAptB,iBAAA,kBAAAotB,GAAA,CAAAptB,iBAAA,CAAAiG,EAAA,CAClD,kBAAAmnB,GAAA,CAAAptB,iBAAA,kBAAAotB,GAAA,CAAAptB,iBAAA,CAAAhD,IAAA,CAA0C,gBAAAowB,GAAA,CAAA3tB,gBAAA,kBAAA2tB,GAAA,CAAA3tB,gBAAA,CAAAL,MAAA,mBAAAguB,GAAA,CAAA3tB,gBAAA,kBAAA2tB,GAAA,CAAA3tB,gBAAA,CAAAL,MAAA,CACE;;;qBDzmBvE9E,WAAW,EAAAi2B,GAAA,CAAAC,aAAA,EAAAD,GAAA,CAAAE,oBAAA,EAAAF,GAAA,CAAAG,eAAA,EAAAH,GAAA,CAAAI,oBAAA,EAAAJ,GAAA,CAAAK,iBAAA,EAAAL,GAAA,CAAAM,kBAAA,EAAAN,GAAA,CAAAO,OAAA,EAAAP,GAAA,CAAAQ,MAAA,EAAE12B,aAAa,EAAA22B,GAAA,CAAAC,OAAA,EAAAzF,EAAA,CAAA0F,MAAA,EAAA1F,EAAA,CAAA2F,aAAA,EAAE/2B,YAAY,EAAAg3B,GAAA,CAAAC,MAAA,EAAEl3B,aAAa,EAAAm3B,GAAA,CAAAC,OAAA,EAAEv3B,IAAI,EAAED,aAAa,EAAAy3B,GAAA,CAAAC,SAAA,EAAE53B,eAAe,EAAA63B,GAAA,CAAAC,SAAA,EAAE73B,mBAAmB,EAAA83B,GAAA,CAAAC,aAAA,EAC1Hj4B,eAAe,EAAAk4B,GAAA,CAAAC,SAAA,EAAEp4B,cAAc,EAAAq4B,GAAA,CAAAC,QAAA,EAAiBh4B,KAAK,EAAEP,iBAAiB,EAAAw4B,GAAA,CAAAC,WAAA,EAAE14B,cAAc,EAAA24B,GAAA,CAAA7U,QAAA,EAAE/jB,eAAe,EAAA64B,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,YAAA,EACzGr4B,OAAO,EAAEX,yBAAyB,EAAEoB,8BAA8B,EAAErB,6BAA6B,EAAED,oBAAoB,EAAED,qBAAqB,EAC9ID,WAAW,EAAAq5B,GAAA,CAAAC,KAAA,EAAAD,GAAA,CAAAE,cAAA,EAAAF,GAAA,CAAAG,aAAA,EAAEz5B,iBAAiB,EAAA05B,GAAA,CAAAC,WAAA,EAAE55B,mBAAmB,EAAA65B,GAAA,CAAAC,aAAA,EAAE/5B,YAAY,EAAAg6B,GAAA,CAAAC,MAAA,EAAUl6B,wBAAwB,EACnGwB,6BAA6B,EAAEE,qBAAqB;MAAAy4B,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}