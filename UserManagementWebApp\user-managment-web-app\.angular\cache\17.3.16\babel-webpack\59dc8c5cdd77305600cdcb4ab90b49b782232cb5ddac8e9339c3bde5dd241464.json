{"ast": null, "code": "const link = {\n  active: {\n    color: {\n      value: '{colors.font.active.value}'\n    }\n  },\n  color: {\n    value: '{colors.font.interactive.value}'\n  },\n  focus: {\n    color: {\n      value: '{colors.font.focus.value}'\n    }\n  },\n  hover: {\n    color: {\n      value: '{colors.font.hover.value}'\n    }\n  },\n  visited: {\n    color: {\n      value: '{colors.font.interactive.value}'\n    }\n  }\n};\nexport { link };", "map": {"version": 3, "names": ["link", "active", "color", "value", "focus", "hover", "visited"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/link.mjs"], "sourcesContent": ["const link = {\n    active: { color: { value: '{colors.font.active.value}' } },\n    color: { value: '{colors.font.interactive.value}' },\n    focus: { color: { value: '{colors.font.focus.value}' } },\n    hover: { color: { value: '{colors.font.hover.value}' } },\n    visited: { color: { value: '{colors.font.interactive.value}' } },\n};\n\nexport { link };\n"], "mappings": "AAAA,MAAMA,IAAI,GAAG;EACTC,MAAM,EAAE;IAAEC,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA6B;EAAE,CAAC;EAC1DD,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAkC,CAAC;EACnDC,KAAK,EAAE;IAAEF,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA4B;EAAE,CAAC;EACxDE,KAAK,EAAE;IAAEH,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA4B;EAAE,CAAC;EACxDG,OAAO,EAAE;IAAEJ,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAkC;EAAE;AACnE,CAAC;AAED,SAASH,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}