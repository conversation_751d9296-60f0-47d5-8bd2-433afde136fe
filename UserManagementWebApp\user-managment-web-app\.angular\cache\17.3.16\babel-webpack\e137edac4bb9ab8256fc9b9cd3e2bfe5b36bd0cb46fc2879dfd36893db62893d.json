{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction isAmplifyOutputs(config) {\n  // version format initially will be '1' but is expected to be something like x.y where x is major and y minor version\n  const {\n    version\n  } = config;\n  if (!version) {\n    return false;\n  }\n  return version.startsWith('1');\n}\nfunction parseStorage(amplifyOutputsStorageProperties) {\n  if (!amplifyOutputsStorageProperties) {\n    return undefined;\n  }\n  const {\n    bucket_name,\n    aws_region,\n    buckets\n  } = amplifyOutputsStorageProperties;\n  return {\n    S3: {\n      bucket: bucket_name,\n      region: aws_region,\n      buckets: buckets && createBucketInfoMap(buckets)\n    }\n  };\n}\nfunction parseAuth(amplifyOutputsAuthProperties) {\n  if (!amplifyOutputsAuthProperties) {\n    return undefined;\n  }\n  const {\n    user_pool_id,\n    user_pool_client_id,\n    identity_pool_id,\n    password_policy,\n    mfa_configuration,\n    mfa_methods,\n    unauthenticated_identities_enabled,\n    oauth,\n    username_attributes,\n    standard_required_attributes,\n    groups\n  } = amplifyOutputsAuthProperties;\n  const authConfig = {\n    Cognito: {\n      userPoolId: user_pool_id,\n      userPoolClientId: user_pool_client_id,\n      groups\n    }\n  };\n  if (identity_pool_id) {\n    authConfig.Cognito = {\n      ...authConfig.Cognito,\n      identityPoolId: identity_pool_id\n    };\n  }\n  if (password_policy) {\n    authConfig.Cognito.passwordFormat = {\n      requireLowercase: password_policy.require_lowercase,\n      requireNumbers: password_policy.require_numbers,\n      requireUppercase: password_policy.require_uppercase,\n      requireSpecialCharacters: password_policy.require_symbols,\n      minLength: password_policy.min_length ?? 6\n    };\n  }\n  if (mfa_configuration) {\n    authConfig.Cognito.mfa = {\n      status: getMfaStatus(mfa_configuration),\n      smsEnabled: mfa_methods?.includes('SMS'),\n      totpEnabled: mfa_methods?.includes('TOTP')\n    };\n  }\n  if (unauthenticated_identities_enabled) {\n    authConfig.Cognito.allowGuestAccess = unauthenticated_identities_enabled;\n  }\n  if (oauth) {\n    authConfig.Cognito.loginWith = {\n      oauth: {\n        domain: oauth.domain,\n        redirectSignIn: oauth.redirect_sign_in_uri,\n        redirectSignOut: oauth.redirect_sign_out_uri,\n        responseType: oauth.response_type === 'token' ? 'token' : 'code',\n        scopes: oauth.scopes,\n        providers: getOAuthProviders(oauth.identity_providers)\n      }\n    };\n  }\n  if (username_attributes) {\n    authConfig.Cognito.loginWith = {\n      ...authConfig.Cognito.loginWith,\n      email: username_attributes.includes('email'),\n      phone: username_attributes.includes('phone_number'),\n      // Signing in with a username is not currently supported in Gen2, this should always evaluate to false\n      username: username_attributes.includes('username')\n    };\n  }\n  if (standard_required_attributes) {\n    authConfig.Cognito.userAttributes = standard_required_attributes.reduce((acc, curr) => ({\n      ...acc,\n      [curr]: {\n        required: true\n      }\n    }), {});\n  }\n  return authConfig;\n}\nfunction parseAnalytics(amplifyOutputsAnalyticsProperties) {\n  if (!amplifyOutputsAnalyticsProperties?.amazon_pinpoint) {\n    return undefined;\n  }\n  const {\n    amazon_pinpoint\n  } = amplifyOutputsAnalyticsProperties;\n  return {\n    Pinpoint: {\n      appId: amazon_pinpoint.app_id,\n      region: amazon_pinpoint.aws_region\n    }\n  };\n}\nfunction parseGeo(amplifyOutputsAnalyticsProperties) {\n  if (!amplifyOutputsAnalyticsProperties) {\n    return undefined;\n  }\n  const {\n    aws_region,\n    geofence_collections,\n    maps,\n    search_indices\n  } = amplifyOutputsAnalyticsProperties;\n  return {\n    LocationService: {\n      region: aws_region,\n      searchIndices: search_indices,\n      geofenceCollections: geofence_collections,\n      maps\n    }\n  };\n}\nfunction parseData(amplifyOutputsDataProperties) {\n  if (!amplifyOutputsDataProperties) {\n    return undefined;\n  }\n  const {\n    aws_region,\n    default_authorization_type,\n    url,\n    api_key,\n    model_introspection\n  } = amplifyOutputsDataProperties;\n  const GraphQL = {\n    endpoint: url,\n    defaultAuthMode: getGraphQLAuthMode(default_authorization_type),\n    region: aws_region,\n    apiKey: api_key,\n    modelIntrospection: model_introspection\n  };\n  return {\n    GraphQL\n  };\n}\nfunction parseCustom(amplifyOutputsCustomProperties) {\n  if (!amplifyOutputsCustomProperties?.events) {\n    return undefined;\n  }\n  const {\n    url,\n    aws_region,\n    api_key,\n    default_authorization_type\n  } = amplifyOutputsCustomProperties.events;\n  const Events = {\n    endpoint: url,\n    defaultAuthMode: getGraphQLAuthMode(default_authorization_type),\n    region: aws_region,\n    apiKey: api_key\n  };\n  return {\n    Events\n  };\n}\nfunction parseNotifications(amplifyOutputsNotificationsProperties) {\n  if (!amplifyOutputsNotificationsProperties) {\n    return undefined;\n  }\n  const {\n    aws_region,\n    channels,\n    amazon_pinpoint_app_id\n  } = amplifyOutputsNotificationsProperties;\n  const hasInAppMessaging = channels.includes('IN_APP_MESSAGING');\n  const hasPushNotification = channels.includes('APNS') || channels.includes('FCM');\n  if (!(hasInAppMessaging || hasPushNotification)) {\n    return undefined;\n  }\n  // At this point, we know the Amplify outputs contains at least one supported channel\n  const notificationsConfig = {};\n  if (hasInAppMessaging) {\n    notificationsConfig.InAppMessaging = {\n      Pinpoint: {\n        appId: amazon_pinpoint_app_id,\n        region: aws_region\n      }\n    };\n  }\n  if (hasPushNotification) {\n    notificationsConfig.PushNotification = {\n      Pinpoint: {\n        appId: amazon_pinpoint_app_id,\n        region: aws_region\n      }\n    };\n  }\n  return notificationsConfig;\n}\nfunction parseAmplifyOutputs(amplifyOutputs) {\n  const resourcesConfig = {};\n  if (amplifyOutputs.storage) {\n    resourcesConfig.Storage = parseStorage(amplifyOutputs.storage);\n  }\n  if (amplifyOutputs.auth) {\n    resourcesConfig.Auth = parseAuth(amplifyOutputs.auth);\n  }\n  if (amplifyOutputs.analytics) {\n    resourcesConfig.Analytics = parseAnalytics(amplifyOutputs.analytics);\n  }\n  if (amplifyOutputs.geo) {\n    resourcesConfig.Geo = parseGeo(amplifyOutputs.geo);\n  }\n  if (amplifyOutputs.data) {\n    resourcesConfig.API = parseData(amplifyOutputs.data);\n  }\n  if (amplifyOutputs.custom) {\n    const customConfig = parseCustom(amplifyOutputs.custom);\n    if (customConfig && 'Events' in customConfig) {\n      resourcesConfig.API = {\n        ...resourcesConfig.API,\n        ...customConfig\n      };\n    }\n  }\n  if (amplifyOutputs.notifications) {\n    resourcesConfig.Notifications = parseNotifications(amplifyOutputs.notifications);\n  }\n  return resourcesConfig;\n}\nconst authModeNames = {\n  AMAZON_COGNITO_USER_POOLS: 'userPool',\n  API_KEY: 'apiKey',\n  AWS_IAM: 'iam',\n  AWS_LAMBDA: 'lambda',\n  OPENID_CONNECT: 'oidc'\n};\nfunction getGraphQLAuthMode(authType) {\n  return authModeNames[authType];\n}\nconst providerNames = {\n  GOOGLE: 'Google',\n  LOGIN_WITH_AMAZON: 'Amazon',\n  FACEBOOK: 'Facebook',\n  SIGN_IN_WITH_APPLE: 'Apple'\n};\nfunction getOAuthProviders(providers = []) {\n  return providers.reduce((oAuthProviders, provider) => {\n    if (providerNames[provider] !== undefined) {\n      oAuthProviders.push(providerNames[provider]);\n    }\n    return oAuthProviders;\n  }, []);\n}\nfunction getMfaStatus(mfaConfiguration) {\n  if (mfaConfiguration === 'OPTIONAL') return 'optional';\n  if (mfaConfiguration === 'REQUIRED') return 'on';\n  return 'off';\n}\nfunction createBucketInfoMap(buckets) {\n  const mappedBuckets = {};\n  buckets.forEach(({\n    name,\n    bucket_name: bucketName,\n    aws_region: region,\n    paths\n  }) => {\n    if (name in mappedBuckets) {\n      throw new Error(`Duplicate friendly name found: ${name}. Name must be unique.`);\n    }\n    const sanitizedPaths = paths ? Object.entries(paths).reduce((acc, [key, value]) => {\n      if (value !== undefined) {\n        acc[key] = value;\n      }\n      return acc;\n    }, {}) : undefined;\n    mappedBuckets[name] = {\n      bucketName,\n      region,\n      paths: sanitizedPaths\n    };\n  });\n  return mappedBuckets;\n}\nexport { isAmplifyOutputs, parseAmplifyOutputs, parseAnalytics };", "map": {"version": 3, "names": ["isAmplifyOutputs", "config", "version", "startsWith", "parseStorage", "amplifyOutputsStorageProperties", "undefined", "bucket_name", "aws_region", "buckets", "S3", "bucket", "region", "createBucketInfoMap", "parseAuth", "amplifyOutputsAuthProperties", "user_pool_id", "user_pool_client_id", "identity_pool_id", "password_policy", "mfa_configuration", "mfa_methods", "unauthenticated_identities_enabled", "o<PERSON>h", "username_attributes", "standard_required_attributes", "groups", "authConfig", "Cognito", "userPoolId", "userPoolClientId", "identityPoolId", "passwordFormat", "requireLowercase", "require_lowercase", "requireNumbers", "require_numbers", "requireUppercase", "require_uppercase", "requireSpecialCharacters", "require_symbols", "<PERSON><PERSON><PERSON><PERSON>", "min_length", "mfa", "status", "getMfaStatus", "smsEnabled", "includes", "totpEnabled", "allowGuestAccess", "loginWith", "domain", "redirectSignIn", "redirect_sign_in_uri", "redirectSignOut", "redirect_sign_out_uri", "responseType", "response_type", "scopes", "providers", "getOAuthProviders", "identity_providers", "email", "phone", "username", "userAttributes", "reduce", "acc", "curr", "required", "parseAnalytics", "amplifyOutputsAnalyticsProperties", "amazon_pinpoint", "Pinpoint", "appId", "app_id", "parseGeo", "geofence_collections", "maps", "search_indices", "LocationService", "searchIndices", "geofenceCollections", "parseData", "amplifyOutputsDataProperties", "default_authorization_type", "url", "api_key", "model_introspection", "GraphQL", "endpoint", "defaultAuthMode", "getGraphQLAuthMode", "<PERSON><PERSON><PERSON><PERSON>", "modelIntrospection", "parseCustom", "amplifyOutputsCustomProperties", "events", "Events", "parseNotifications", "amplifyOutputsNotificationsProperties", "channels", "amazon_pinpoint_app_id", "hasInAppMessaging", "hasPushNotification", "notificationsConfig", "InAppMessaging", "PushNotification", "parseAmplifyOutputs", "amplifyOutputs", "resourcesConfig", "storage", "Storage", "auth", "<PERSON><PERSON>", "analytics", "Analytics", "geo", "Geo", "data", "API", "custom", "customConfig", "notifications", "Notifications", "authModeNames", "AMAZON_COGNITO_USER_POOLS", "API_KEY", "AWS_IAM", "AWS_LAMBDA", "OPENID_CONNECT", "authType", "providerNames", "GOOGLE", "LOGIN_WITH_AMAZON", "FACEBOOK", "SIGN_IN_WITH_APPLE", "oAuthProviders", "provider", "push", "mfaConfiguration", "mappedBuckets", "for<PERSON>ach", "name", "bucketName", "paths", "Error", "sanitizedPaths", "Object", "entries", "key", "value"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/parseAmplifyOutputs.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction isAmplifyOutputs(config) {\n    // version format initially will be '1' but is expected to be something like x.y where x is major and y minor version\n    const { version } = config;\n    if (!version) {\n        return false;\n    }\n    return version.startsWith('1');\n}\nfunction parseStorage(amplifyOutputsStorageProperties) {\n    if (!amplifyOutputsStorageProperties) {\n        return undefined;\n    }\n    const { bucket_name, aws_region, buckets } = amplifyOutputsStorageProperties;\n    return {\n        S3: {\n            bucket: bucket_name,\n            region: aws_region,\n            buckets: buckets && createBucketInfoMap(buckets),\n        },\n    };\n}\nfunction parseAuth(amplifyOutputsAuthProperties) {\n    if (!amplifyOutputsAuthProperties) {\n        return undefined;\n    }\n    const { user_pool_id, user_pool_client_id, identity_pool_id, password_policy, mfa_configuration, mfa_methods, unauthenticated_identities_enabled, oauth, username_attributes, standard_required_attributes, groups, } = amplifyOutputsAuthProperties;\n    const authConfig = {\n        Cognito: {\n            userPoolId: user_pool_id,\n            userPoolClientId: user_pool_client_id,\n            groups,\n        },\n    };\n    if (identity_pool_id) {\n        authConfig.Cognito = {\n            ...authConfig.Cognito,\n            identityPoolId: identity_pool_id,\n        };\n    }\n    if (password_policy) {\n        authConfig.Cognito.passwordFormat = {\n            requireLowercase: password_policy.require_lowercase,\n            requireNumbers: password_policy.require_numbers,\n            requireUppercase: password_policy.require_uppercase,\n            requireSpecialCharacters: password_policy.require_symbols,\n            minLength: password_policy.min_length ?? 6,\n        };\n    }\n    if (mfa_configuration) {\n        authConfig.Cognito.mfa = {\n            status: getMfaStatus(mfa_configuration),\n            smsEnabled: mfa_methods?.includes('SMS'),\n            totpEnabled: mfa_methods?.includes('TOTP'),\n        };\n    }\n    if (unauthenticated_identities_enabled) {\n        authConfig.Cognito.allowGuestAccess = unauthenticated_identities_enabled;\n    }\n    if (oauth) {\n        authConfig.Cognito.loginWith = {\n            oauth: {\n                domain: oauth.domain,\n                redirectSignIn: oauth.redirect_sign_in_uri,\n                redirectSignOut: oauth.redirect_sign_out_uri,\n                responseType: oauth.response_type === 'token' ? 'token' : 'code',\n                scopes: oauth.scopes,\n                providers: getOAuthProviders(oauth.identity_providers),\n            },\n        };\n    }\n    if (username_attributes) {\n        authConfig.Cognito.loginWith = {\n            ...authConfig.Cognito.loginWith,\n            email: username_attributes.includes('email'),\n            phone: username_attributes.includes('phone_number'),\n            // Signing in with a username is not currently supported in Gen2, this should always evaluate to false\n            username: username_attributes.includes('username'),\n        };\n    }\n    if (standard_required_attributes) {\n        authConfig.Cognito.userAttributes = standard_required_attributes.reduce((acc, curr) => ({ ...acc, [curr]: { required: true } }), {});\n    }\n    return authConfig;\n}\nfunction parseAnalytics(amplifyOutputsAnalyticsProperties) {\n    if (!amplifyOutputsAnalyticsProperties?.amazon_pinpoint) {\n        return undefined;\n    }\n    const { amazon_pinpoint } = amplifyOutputsAnalyticsProperties;\n    return {\n        Pinpoint: {\n            appId: amazon_pinpoint.app_id,\n            region: amazon_pinpoint.aws_region,\n        },\n    };\n}\nfunction parseGeo(amplifyOutputsAnalyticsProperties) {\n    if (!amplifyOutputsAnalyticsProperties) {\n        return undefined;\n    }\n    const { aws_region, geofence_collections, maps, search_indices } = amplifyOutputsAnalyticsProperties;\n    return {\n        LocationService: {\n            region: aws_region,\n            searchIndices: search_indices,\n            geofenceCollections: geofence_collections,\n            maps,\n        },\n    };\n}\nfunction parseData(amplifyOutputsDataProperties) {\n    if (!amplifyOutputsDataProperties) {\n        return undefined;\n    }\n    const { aws_region, default_authorization_type, url, api_key, model_introspection, } = amplifyOutputsDataProperties;\n    const GraphQL = {\n        endpoint: url,\n        defaultAuthMode: getGraphQLAuthMode(default_authorization_type),\n        region: aws_region,\n        apiKey: api_key,\n        modelIntrospection: model_introspection,\n    };\n    return {\n        GraphQL,\n    };\n}\nfunction parseCustom(amplifyOutputsCustomProperties) {\n    if (!amplifyOutputsCustomProperties?.events) {\n        return undefined;\n    }\n    const { url, aws_region, api_key, default_authorization_type } = amplifyOutputsCustomProperties.events;\n    const Events = {\n        endpoint: url,\n        defaultAuthMode: getGraphQLAuthMode(default_authorization_type),\n        region: aws_region,\n        apiKey: api_key,\n    };\n    return {\n        Events,\n    };\n}\nfunction parseNotifications(amplifyOutputsNotificationsProperties) {\n    if (!amplifyOutputsNotificationsProperties) {\n        return undefined;\n    }\n    const { aws_region, channels, amazon_pinpoint_app_id } = amplifyOutputsNotificationsProperties;\n    const hasInAppMessaging = channels.includes('IN_APP_MESSAGING');\n    const hasPushNotification = channels.includes('APNS') || channels.includes('FCM');\n    if (!(hasInAppMessaging || hasPushNotification)) {\n        return undefined;\n    }\n    // At this point, we know the Amplify outputs contains at least one supported channel\n    const notificationsConfig = {};\n    if (hasInAppMessaging) {\n        notificationsConfig.InAppMessaging = {\n            Pinpoint: {\n                appId: amazon_pinpoint_app_id,\n                region: aws_region,\n            },\n        };\n    }\n    if (hasPushNotification) {\n        notificationsConfig.PushNotification = {\n            Pinpoint: {\n                appId: amazon_pinpoint_app_id,\n                region: aws_region,\n            },\n        };\n    }\n    return notificationsConfig;\n}\nfunction parseAmplifyOutputs(amplifyOutputs) {\n    const resourcesConfig = {};\n    if (amplifyOutputs.storage) {\n        resourcesConfig.Storage = parseStorage(amplifyOutputs.storage);\n    }\n    if (amplifyOutputs.auth) {\n        resourcesConfig.Auth = parseAuth(amplifyOutputs.auth);\n    }\n    if (amplifyOutputs.analytics) {\n        resourcesConfig.Analytics = parseAnalytics(amplifyOutputs.analytics);\n    }\n    if (amplifyOutputs.geo) {\n        resourcesConfig.Geo = parseGeo(amplifyOutputs.geo);\n    }\n    if (amplifyOutputs.data) {\n        resourcesConfig.API = parseData(amplifyOutputs.data);\n    }\n    if (amplifyOutputs.custom) {\n        const customConfig = parseCustom(amplifyOutputs.custom);\n        if (customConfig && 'Events' in customConfig) {\n            resourcesConfig.API = { ...resourcesConfig.API, ...customConfig };\n        }\n    }\n    if (amplifyOutputs.notifications) {\n        resourcesConfig.Notifications = parseNotifications(amplifyOutputs.notifications);\n    }\n    return resourcesConfig;\n}\nconst authModeNames = {\n    AMAZON_COGNITO_USER_POOLS: 'userPool',\n    API_KEY: 'apiKey',\n    AWS_IAM: 'iam',\n    AWS_LAMBDA: 'lambda',\n    OPENID_CONNECT: 'oidc',\n};\nfunction getGraphQLAuthMode(authType) {\n    return authModeNames[authType];\n}\nconst providerNames = {\n    GOOGLE: 'Google',\n    LOGIN_WITH_AMAZON: 'Amazon',\n    FACEBOOK: 'Facebook',\n    SIGN_IN_WITH_APPLE: 'Apple',\n};\nfunction getOAuthProviders(providers = []) {\n    return providers.reduce((oAuthProviders, provider) => {\n        if (providerNames[provider] !== undefined) {\n            oAuthProviders.push(providerNames[provider]);\n        }\n        return oAuthProviders;\n    }, []);\n}\nfunction getMfaStatus(mfaConfiguration) {\n    if (mfaConfiguration === 'OPTIONAL')\n        return 'optional';\n    if (mfaConfiguration === 'REQUIRED')\n        return 'on';\n    return 'off';\n}\nfunction createBucketInfoMap(buckets) {\n    const mappedBuckets = {};\n    buckets.forEach(({ name, bucket_name: bucketName, aws_region: region, paths }) => {\n        if (name in mappedBuckets) {\n            throw new Error(`Duplicate friendly name found: ${name}. Name must be unique.`);\n        }\n        const sanitizedPaths = paths\n            ? Object.entries(paths).reduce((acc, [key, value]) => {\n                if (value !== undefined) {\n                    acc[key] = value;\n                }\n                return acc;\n            }, {})\n            : undefined;\n        mappedBuckets[name] = {\n            bucketName,\n            region,\n            paths: sanitizedPaths,\n        };\n    });\n    return mappedBuckets;\n}\n\nexport { isAmplifyOutputs, parseAmplifyOutputs, parseAnalytics };\n"], "mappings": "AAAA;AACA;AACA,SAASA,gBAAgBA,CAACC,MAAM,EAAE;EAC9B;EACA,MAAM;IAAEC;EAAQ,CAAC,GAAGD,MAAM;EAC1B,IAAI,CAACC,OAAO,EAAE;IACV,OAAO,KAAK;EAChB;EACA,OAAOA,OAAO,CAACC,UAAU,CAAC,GAAG,CAAC;AAClC;AACA,SAASC,YAAYA,CAACC,+BAA+B,EAAE;EACnD,IAAI,CAACA,+BAA+B,EAAE;IAClC,OAAOC,SAAS;EACpB;EACA,MAAM;IAAEC,WAAW;IAAEC,UAAU;IAAEC;EAAQ,CAAC,GAAGJ,+BAA+B;EAC5E,OAAO;IACHK,EAAE,EAAE;MACAC,MAAM,EAAEJ,WAAW;MACnBK,MAAM,EAAEJ,UAAU;MAClBC,OAAO,EAAEA,OAAO,IAAII,mBAAmB,CAACJ,OAAO;IACnD;EACJ,CAAC;AACL;AACA,SAASK,SAASA,CAACC,4BAA4B,EAAE;EAC7C,IAAI,CAACA,4BAA4B,EAAE;IAC/B,OAAOT,SAAS;EACpB;EACA,MAAM;IAAEU,YAAY;IAAEC,mBAAmB;IAAEC,gBAAgB;IAAEC,eAAe;IAAEC,iBAAiB;IAAEC,WAAW;IAAEC,kCAAkC;IAAEC,KAAK;IAAEC,mBAAmB;IAAEC,4BAA4B;IAAEC;EAAQ,CAAC,GAAGX,4BAA4B;EACpP,MAAMY,UAAU,GAAG;IACfC,OAAO,EAAE;MACLC,UAAU,EAAEb,YAAY;MACxBc,gBAAgB,EAAEb,mBAAmB;MACrCS;IACJ;EACJ,CAAC;EACD,IAAIR,gBAAgB,EAAE;IAClBS,UAAU,CAACC,OAAO,GAAG;MACjB,GAAGD,UAAU,CAACC,OAAO;MACrBG,cAAc,EAAEb;IACpB,CAAC;EACL;EACA,IAAIC,eAAe,EAAE;IACjBQ,UAAU,CAACC,OAAO,CAACI,cAAc,GAAG;MAChCC,gBAAgB,EAAEd,eAAe,CAACe,iBAAiB;MACnDC,cAAc,EAAEhB,eAAe,CAACiB,eAAe;MAC/CC,gBAAgB,EAAElB,eAAe,CAACmB,iBAAiB;MACnDC,wBAAwB,EAAEpB,eAAe,CAACqB,eAAe;MACzDC,SAAS,EAAEtB,eAAe,CAACuB,UAAU,IAAI;IAC7C,CAAC;EACL;EACA,IAAItB,iBAAiB,EAAE;IACnBO,UAAU,CAACC,OAAO,CAACe,GAAG,GAAG;MACrBC,MAAM,EAAEC,YAAY,CAACzB,iBAAiB,CAAC;MACvC0B,UAAU,EAAEzB,WAAW,EAAE0B,QAAQ,CAAC,KAAK,CAAC;MACxCC,WAAW,EAAE3B,WAAW,EAAE0B,QAAQ,CAAC,MAAM;IAC7C,CAAC;EACL;EACA,IAAIzB,kCAAkC,EAAE;IACpCK,UAAU,CAACC,OAAO,CAACqB,gBAAgB,GAAG3B,kCAAkC;EAC5E;EACA,IAAIC,KAAK,EAAE;IACPI,UAAU,CAACC,OAAO,CAACsB,SAAS,GAAG;MAC3B3B,KAAK,EAAE;QACH4B,MAAM,EAAE5B,KAAK,CAAC4B,MAAM;QACpBC,cAAc,EAAE7B,KAAK,CAAC8B,oBAAoB;QAC1CC,eAAe,EAAE/B,KAAK,CAACgC,qBAAqB;QAC5CC,YAAY,EAAEjC,KAAK,CAACkC,aAAa,KAAK,OAAO,GAAG,OAAO,GAAG,MAAM;QAChEC,MAAM,EAAEnC,KAAK,CAACmC,MAAM;QACpBC,SAAS,EAAEC,iBAAiB,CAACrC,KAAK,CAACsC,kBAAkB;MACzD;IACJ,CAAC;EACL;EACA,IAAIrC,mBAAmB,EAAE;IACrBG,UAAU,CAACC,OAAO,CAACsB,SAAS,GAAG;MAC3B,GAAGvB,UAAU,CAACC,OAAO,CAACsB,SAAS;MAC/BY,KAAK,EAAEtC,mBAAmB,CAACuB,QAAQ,CAAC,OAAO,CAAC;MAC5CgB,KAAK,EAAEvC,mBAAmB,CAACuB,QAAQ,CAAC,cAAc,CAAC;MACnD;MACAiB,QAAQ,EAAExC,mBAAmB,CAACuB,QAAQ,CAAC,UAAU;IACrD,CAAC;EACL;EACA,IAAItB,4BAA4B,EAAE;IAC9BE,UAAU,CAACC,OAAO,CAACqC,cAAc,GAAGxC,4BAA4B,CAACyC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,MAAM;MAAE,GAAGD,GAAG;MAAE,CAACC,IAAI,GAAG;QAAEC,QAAQ,EAAE;MAAK;IAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACxI;EACA,OAAO1C,UAAU;AACrB;AACA,SAAS2C,cAAcA,CAACC,iCAAiC,EAAE;EACvD,IAAI,CAACA,iCAAiC,EAAEC,eAAe,EAAE;IACrD,OAAOlE,SAAS;EACpB;EACA,MAAM;IAAEkE;EAAgB,CAAC,GAAGD,iCAAiC;EAC7D,OAAO;IACHE,QAAQ,EAAE;MACNC,KAAK,EAAEF,eAAe,CAACG,MAAM;MAC7B/D,MAAM,EAAE4D,eAAe,CAAChE;IAC5B;EACJ,CAAC;AACL;AACA,SAASoE,QAAQA,CAACL,iCAAiC,EAAE;EACjD,IAAI,CAACA,iCAAiC,EAAE;IACpC,OAAOjE,SAAS;EACpB;EACA,MAAM;IAAEE,UAAU;IAAEqE,oBAAoB;IAAEC,IAAI;IAAEC;EAAe,CAAC,GAAGR,iCAAiC;EACpG,OAAO;IACHS,eAAe,EAAE;MACbpE,MAAM,EAAEJ,UAAU;MAClByE,aAAa,EAAEF,cAAc;MAC7BG,mBAAmB,EAAEL,oBAAoB;MACzCC;IACJ;EACJ,CAAC;AACL;AACA,SAASK,SAASA,CAACC,4BAA4B,EAAE;EAC7C,IAAI,CAACA,4BAA4B,EAAE;IAC/B,OAAO9E,SAAS;EACpB;EACA,MAAM;IAAEE,UAAU;IAAE6E,0BAA0B;IAAEC,GAAG;IAAEC,OAAO;IAAEC;EAAqB,CAAC,GAAGJ,4BAA4B;EACnH,MAAMK,OAAO,GAAG;IACZC,QAAQ,EAAEJ,GAAG;IACbK,eAAe,EAAEC,kBAAkB,CAACP,0BAA0B,CAAC;IAC/DzE,MAAM,EAAEJ,UAAU;IAClBqF,MAAM,EAAEN,OAAO;IACfO,kBAAkB,EAAEN;EACxB,CAAC;EACD,OAAO;IACHC;EACJ,CAAC;AACL;AACA,SAASM,WAAWA,CAACC,8BAA8B,EAAE;EACjD,IAAI,CAACA,8BAA8B,EAAEC,MAAM,EAAE;IACzC,OAAO3F,SAAS;EACpB;EACA,MAAM;IAAEgF,GAAG;IAAE9E,UAAU;IAAE+E,OAAO;IAAEF;EAA2B,CAAC,GAAGW,8BAA8B,CAACC,MAAM;EACtG,MAAMC,MAAM,GAAG;IACXR,QAAQ,EAAEJ,GAAG;IACbK,eAAe,EAAEC,kBAAkB,CAACP,0BAA0B,CAAC;IAC/DzE,MAAM,EAAEJ,UAAU;IAClBqF,MAAM,EAAEN;EACZ,CAAC;EACD,OAAO;IACHW;EACJ,CAAC;AACL;AACA,SAASC,kBAAkBA,CAACC,qCAAqC,EAAE;EAC/D,IAAI,CAACA,qCAAqC,EAAE;IACxC,OAAO9F,SAAS;EACpB;EACA,MAAM;IAAEE,UAAU;IAAE6F,QAAQ;IAAEC;EAAuB,CAAC,GAAGF,qCAAqC;EAC9F,MAAMG,iBAAiB,GAAGF,QAAQ,CAACtD,QAAQ,CAAC,kBAAkB,CAAC;EAC/D,MAAMyD,mBAAmB,GAAGH,QAAQ,CAACtD,QAAQ,CAAC,MAAM,CAAC,IAAIsD,QAAQ,CAACtD,QAAQ,CAAC,KAAK,CAAC;EACjF,IAAI,EAAEwD,iBAAiB,IAAIC,mBAAmB,CAAC,EAAE;IAC7C,OAAOlG,SAAS;EACpB;EACA;EACA,MAAMmG,mBAAmB,GAAG,CAAC,CAAC;EAC9B,IAAIF,iBAAiB,EAAE;IACnBE,mBAAmB,CAACC,cAAc,GAAG;MACjCjC,QAAQ,EAAE;QACNC,KAAK,EAAE4B,sBAAsB;QAC7B1F,MAAM,EAAEJ;MACZ;IACJ,CAAC;EACL;EACA,IAAIgG,mBAAmB,EAAE;IACrBC,mBAAmB,CAACE,gBAAgB,GAAG;MACnClC,QAAQ,EAAE;QACNC,KAAK,EAAE4B,sBAAsB;QAC7B1F,MAAM,EAAEJ;MACZ;IACJ,CAAC;EACL;EACA,OAAOiG,mBAAmB;AAC9B;AACA,SAASG,mBAAmBA,CAACC,cAAc,EAAE;EACzC,MAAMC,eAAe,GAAG,CAAC,CAAC;EAC1B,IAAID,cAAc,CAACE,OAAO,EAAE;IACxBD,eAAe,CAACE,OAAO,GAAG5G,YAAY,CAACyG,cAAc,CAACE,OAAO,CAAC;EAClE;EACA,IAAIF,cAAc,CAACI,IAAI,EAAE;IACrBH,eAAe,CAACI,IAAI,GAAGpG,SAAS,CAAC+F,cAAc,CAACI,IAAI,CAAC;EACzD;EACA,IAAIJ,cAAc,CAACM,SAAS,EAAE;IAC1BL,eAAe,CAACM,SAAS,GAAG9C,cAAc,CAACuC,cAAc,CAACM,SAAS,CAAC;EACxE;EACA,IAAIN,cAAc,CAACQ,GAAG,EAAE;IACpBP,eAAe,CAACQ,GAAG,GAAG1C,QAAQ,CAACiC,cAAc,CAACQ,GAAG,CAAC;EACtD;EACA,IAAIR,cAAc,CAACU,IAAI,EAAE;IACrBT,eAAe,CAACU,GAAG,GAAGrC,SAAS,CAAC0B,cAAc,CAACU,IAAI,CAAC;EACxD;EACA,IAAIV,cAAc,CAACY,MAAM,EAAE;IACvB,MAAMC,YAAY,GAAG3B,WAAW,CAACc,cAAc,CAACY,MAAM,CAAC;IACvD,IAAIC,YAAY,IAAI,QAAQ,IAAIA,YAAY,EAAE;MAC1CZ,eAAe,CAACU,GAAG,GAAG;QAAE,GAAGV,eAAe,CAACU,GAAG;QAAE,GAAGE;MAAa,CAAC;IACrE;EACJ;EACA,IAAIb,cAAc,CAACc,aAAa,EAAE;IAC9Bb,eAAe,CAACc,aAAa,GAAGzB,kBAAkB,CAACU,cAAc,CAACc,aAAa,CAAC;EACpF;EACA,OAAOb,eAAe;AAC1B;AACA,MAAMe,aAAa,GAAG;EAClBC,yBAAyB,EAAE,UAAU;EACrCC,OAAO,EAAE,QAAQ;EACjBC,OAAO,EAAE,KAAK;EACdC,UAAU,EAAE,QAAQ;EACpBC,cAAc,EAAE;AACpB,CAAC;AACD,SAAStC,kBAAkBA,CAACuC,QAAQ,EAAE;EAClC,OAAON,aAAa,CAACM,QAAQ,CAAC;AAClC;AACA,MAAMC,aAAa,GAAG;EAClBC,MAAM,EAAE,QAAQ;EAChBC,iBAAiB,EAAE,QAAQ;EAC3BC,QAAQ,EAAE,UAAU;EACpBC,kBAAkB,EAAE;AACxB,CAAC;AACD,SAAS5E,iBAAiBA,CAACD,SAAS,GAAG,EAAE,EAAE;EACvC,OAAOA,SAAS,CAACO,MAAM,CAAC,CAACuE,cAAc,EAAEC,QAAQ,KAAK;IAClD,IAAIN,aAAa,CAACM,QAAQ,CAAC,KAAKpI,SAAS,EAAE;MACvCmI,cAAc,CAACE,IAAI,CAACP,aAAa,CAACM,QAAQ,CAAC,CAAC;IAChD;IACA,OAAOD,cAAc;EACzB,CAAC,EAAE,EAAE,CAAC;AACV;AACA,SAAS5F,YAAYA,CAAC+F,gBAAgB,EAAE;EACpC,IAAIA,gBAAgB,KAAK,UAAU,EAC/B,OAAO,UAAU;EACrB,IAAIA,gBAAgB,KAAK,UAAU,EAC/B,OAAO,IAAI;EACf,OAAO,KAAK;AAChB;AACA,SAAS/H,mBAAmBA,CAACJ,OAAO,EAAE;EAClC,MAAMoI,aAAa,GAAG,CAAC,CAAC;EACxBpI,OAAO,CAACqI,OAAO,CAAC,CAAC;IAAEC,IAAI;IAAExI,WAAW,EAAEyI,UAAU;IAAExI,UAAU,EAAEI,MAAM;IAAEqI;EAAM,CAAC,KAAK;IAC9E,IAAIF,IAAI,IAAIF,aAAa,EAAE;MACvB,MAAM,IAAIK,KAAK,CAAC,kCAAkCH,IAAI,wBAAwB,CAAC;IACnF;IACA,MAAMI,cAAc,GAAGF,KAAK,GACtBG,MAAM,CAACC,OAAO,CAACJ,KAAK,CAAC,CAAC/E,MAAM,CAAC,CAACC,GAAG,EAAE,CAACmF,GAAG,EAAEC,KAAK,CAAC,KAAK;MAClD,IAAIA,KAAK,KAAKjJ,SAAS,EAAE;QACrB6D,GAAG,CAACmF,GAAG,CAAC,GAAGC,KAAK;MACpB;MACA,OAAOpF,GAAG;IACd,CAAC,EAAE,CAAC,CAAC,CAAC,GACJ7D,SAAS;IACfuI,aAAa,CAACE,IAAI,CAAC,GAAG;MAClBC,UAAU;MACVpI,MAAM;MACNqI,KAAK,EAAEE;IACX,CAAC;EACL,CAAC,CAAC;EACF,OAAON,aAAa;AACxB;AAEA,SAAS7I,gBAAgB,EAAE4G,mBAAmB,EAAEtC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}