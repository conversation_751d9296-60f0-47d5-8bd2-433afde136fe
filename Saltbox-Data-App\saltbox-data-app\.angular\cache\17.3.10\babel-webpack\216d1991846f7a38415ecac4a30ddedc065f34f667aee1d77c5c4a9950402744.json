{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/SaltboxActionableGrid/Saltbox-Data-App/saltbox-data-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { dateValueFormatter, formatCurrency, numericValueFormatter } from 'src/app/shared/utilities/format.functions';\nimport { firstValueFrom } from 'rxjs';\nimport { JsonDataTypes, JsonStringFormats } from 'src/app/shared/enums/json-schema.enum';\nimport { AggregationTypes, SummaryValueFormats } from 'src/app/visualizations/enums/tiles.enums';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/datastores.service\";\nexport class TableTileService {\n  constructor(datastoresService) {\n    this.datastoresService = datastoresService;\n  }\n  getGridOptions() {\n    return {\n      defaultColDef: {\n        flex: 1\n      },\n      getRowStyle: params => {\n        if (params.node.rowPinned) {\n          return {\n            fontWeight: 'bold'\n          };\n        }\n      }\n    };\n  }\n  getColumnDefinitions(model) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!model?.columns?.length) return [];\n      const datastore = yield firstValueFrom(_this.datastoresService.getDatastore(model.datastoreId));\n      if (!datastore?.baseSchema) return [];\n      return model.columns.map(col => _this.generateColumnDefinition(col, datastore.baseSchema, model));\n    })();\n  }\n  generateColumnDefinition(col, schema, model) {\n    const schemaCol = schema.properties[col.columnName];\n    const colDef = {\n      field: col.columnName,\n      headerName: col.displayName || schemaCol?.presentationProperties?.displayName || col.columnName,\n      enableRowGroup: true,\n      resizable: true,\n      filter: true,\n      floatingFilter: model.showFilterRow,\n      sortable: true,\n      minWidth: 200,\n      hide: !col.visible,\n      valueFormatter: params => this.formatValue(params, schemaCol?.type, schemaCol?.format, schemaCol?.typeProperties?.currency, schemaCol?.presentationProperties?.decimalPlaces, col.summaryRow?.valueFormat, col.summaryRow?.text, col.summaryRow?.decimalPlaces)\n    };\n    if (schemaCol?.type === JsonDataTypes.Boolean) {\n      colDef.cellRenderer = this.getCheckBoxCellRenderer;\n    }\n    if (col.linkTemplate) {\n      colDef.cellRenderer = params => this.getLinkCellRenderer(params, schemaCol, col);\n    }\n    // if it is numeric and the link text is not set, make the cell right align\n    if ([JsonDataTypes.Decimal, JsonDataTypes.Integer].includes(schemaCol?.type) && !(col.linkTemplate && col.linkText)) {\n      colDef.cellStyle = {\n        textAlign: 'right'\n      };\n      colDef.cellClass = 'sb-field-right-align';\n    }\n    return colDef;\n  }\n  getSummaryRow(data, columns) {\n    const summaryRow = {};\n    columns.forEach(column => {\n      let value;\n      if (column.summaryRow?.aggregation) {\n        switch (column.summaryRow.aggregation) {\n          case AggregationTypes.Sum:\n            summaryRow[column.columnName] = data.reduce((sum, row) => sum + (row[column.columnName] || 0), 0);\n            break;\n          case AggregationTypes.Average:\n            value = data.reduce((sum, row) => sum + (row[column.columnName] || 0), 0) / data.length;\n            summaryRow[column.columnName] = value == null || Number.isNaN(value) ? 0 : value;\n            break;\n          case AggregationTypes.Count:\n            summaryRow[column.columnName] = data.length;\n            break;\n        }\n      } else if (column.summaryRow?.text) {\n        summaryRow[column.columnName] = column.summaryRow.text;\n      } else {\n        summaryRow[column.columnName] = '';\n      }\n    });\n    return summaryRow;\n  }\n  getCheckBoxCellRenderer(params) {\n    const inputElement = document.createElement('input');\n    inputElement.type = 'checkbox';\n    inputElement.checked = !!params.value;\n    inputElement.disabled = true;\n    return inputElement;\n  }\n  getLinkCellRenderer(params, schemaCol, col) {\n    const linkElement = document.createElement('a');\n    linkElement.href = this.populateTemplateWithValues(col.linkTemplate, params.data);\n    linkElement.innerText = col.linkText || this.formatValue(params.value, schemaCol?.type, schemaCol?.format, schemaCol?.typeProperties?.currency, schemaCol?.presentationProperties?.decimalPlaces);\n    linkElement.target = '_blank';\n    return linkElement;\n  }\n  populateTemplateWithValues(template, data) {\n    for (const key in data) {\n      if (key in data) {\n        template = template.replace(`{${key}}`, data[key]);\n      }\n    }\n    return template;\n  }\n  formatValue(params, type, format, currency, decimalPlaces, sumValueFormat, sumText, sumDP) {\n    if (params.value === null || type === null) return params.value;\n    let formattedValue = params.value;\n    if (!params.node.rowPinned || sumValueFormat === SummaryValueFormats.Inherit) {\n      switch (type) {\n        case JsonDataTypes.String:\n          formattedValue = [JsonStringFormats.Date, JsonStringFormats.DateTime].includes(format) ? dateValueFormatter(formattedValue) : formattedValue;\n          break;\n        case JsonDataTypes.Decimal:\n        case JsonDataTypes.Integer:\n          formattedValue = currency ? formatCurrency(formattedValue, currency, decimalPlaces) : numericValueFormatter(formattedValue, decimalPlaces);\n          break;\n      }\n    }\n    // pinned row format (summary)\n    if (params.node.rowPinned) {\n      switch (sumValueFormat) {\n        case SummaryValueFormats.Numeric:\n          formattedValue = numericValueFormatter(formattedValue, sumDP ?? 0);\n          break;\n      }\n      if (sumText) {\n        formattedValue = sumText.replace('{value}', formattedValue);\n      }\n    }\n    return formattedValue;\n  }\n  static {\n    this.ɵfac = function TableTileService_Factory(t) {\n      return new (t || TableTileService)(i0.ɵɵinject(i1.DatastoresService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TableTileService,\n      factory: TableTileService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formatCurrency", "numericValueFormatter", "firstValueFrom", "JsonDataTypes", "JsonStringFormats", "AggregationTypes", "SummaryValueFormats", "TableTileService", "constructor", "datastoresService", "getGridOptions", "defaultColDef", "flex", "getRowStyle", "params", "node", "row<PERSON><PERSON><PERSON>", "fontWeight", "getColumnDefinitions", "model", "_this", "_asyncToGenerator", "columns", "length", "datastore", "getDatastore", "datastoreId", "baseSchema", "map", "col", "generateColumnDefinition", "schema", "schemaCol", "properties", "columnName", "colDef", "field", "headerName", "displayName", "presentationProperties", "enableRowGroup", "resizable", "filter", "floatingFilter", "showFilterRow", "sortable", "min<PERSON><PERSON><PERSON>", "hide", "visible", "valueFormatter", "formatValue", "type", "format", "typeProperties", "currency", "decimalPlaces", "summaryRow", "valueFormat", "text", "Boolean", "cell<PERSON><PERSON><PERSON>", "getCheckBoxCellRenderer", "linkTemplate", "getLinkCellRenderer", "Decimal", "Integer", "includes", "linkText", "cellStyle", "textAlign", "cellClass", "getSummaryRow", "data", "for<PERSON>ach", "column", "value", "aggregation", "Sum", "reduce", "sum", "row", "Average", "Number", "isNaN", "Count", "inputElement", "document", "createElement", "checked", "disabled", "linkElement", "href", "populateTemplateWithValues", "innerText", "target", "template", "key", "replace", "sumValueFormat", "sumText", "sumDP", "formattedValue", "Inherit", "String", "Date", "DateTime", "Numeric", "i0", "ɵɵinject", "i1", "DatastoresService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\visualizations\\tiles\\table-tile\\services\\table-tile.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { ColDef, GridOptions } from 'ag-grid-community';\r\nimport { DatastoresService } from 'src/app/core/services/datastores.service';\r\nimport { dateValueFormatter, formatCurrency, numericValueFormatter } from 'src/app/shared/utilities/format.functions';\r\nimport { TableConfig } from 'src/app/visualizations/models/table/table-config';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { ExtendedJsonSchema } from 'src/app/shared/models/extended-json-schema';\r\nimport { JsonDataTypes, JsonStringFormats } from 'src/app/shared/enums/json-schema.enum';\r\nimport { ColumnConfig } from 'src/app/visualizations/models/table/column-config';\r\nimport { AggregationTypes, SummaryValueFormats } from 'src/app/visualizations/enums/tiles.enums';\r\n\r\n@Injectable({\r\n    providedIn: 'root'\r\n})\r\nexport class TableTileService {\r\n\r\n    constructor(private datastoresService: DatastoresService) { }\r\n\r\n    getGridOptions(): GridOptions {\r\n        return {\r\n            defaultColDef: {\r\n                flex: 1,\r\n            },\r\n            getRowStyle: (params) => {\r\n                if (params.node.rowPinned) {\r\n                    return { fontWeight: 'bold' };\r\n                }\r\n            }\r\n        };\r\n    }\r\n\r\n    async getColumnDefinitions(model: TableConfig): Promise<ColDef[]> {\r\n        if (!model?.columns?.length) return [];\r\n\r\n        const datastore = await firstValueFrom(this.datastoresService.getDatastore(model.datastoreId));\r\n        if (!datastore?.baseSchema) return [];\r\n\r\n        return model.columns.map(col => this.generateColumnDefinition(col, datastore.baseSchema, model));\r\n    }\r\n\r\n    generateColumnDefinition(col: ColumnConfig, schema, model: TableConfig): ColDef {\r\n        const schemaCol = schema.properties[col.columnName];\r\n        const colDef: ColDef = {\r\n            field: col.columnName,\r\n            headerName: col.displayName || schemaCol?.presentationProperties?.displayName || col.columnName,\r\n            enableRowGroup: true,\r\n            resizable: true,\r\n            filter: true,\r\n            floatingFilter: model.showFilterRow,\r\n            sortable: true,\r\n            minWidth: 200,\r\n            hide: !col.visible,\r\n            valueFormatter: params => this.formatValue(\r\n                params,\r\n                schemaCol?.type,\r\n                schemaCol?.format,\r\n                schemaCol?.typeProperties?.currency,\r\n                schemaCol?.presentationProperties?.decimalPlaces,\r\n                col.summaryRow?.valueFormat,\r\n                col.summaryRow?.text,\r\n                col.summaryRow?.decimalPlaces\r\n            )\r\n        };\r\n\r\n        if (schemaCol?.type === JsonDataTypes.Boolean) {\r\n            colDef.cellRenderer = this.getCheckBoxCellRenderer;\r\n        }\r\n\r\n        if (col.linkTemplate) {\r\n            colDef.cellRenderer = params => this.getLinkCellRenderer(params, schemaCol, col);\r\n        }\r\n\r\n        // if it is numeric and the link text is not set, make the cell right align\r\n        if ([JsonDataTypes.Decimal, JsonDataTypes.Integer].includes(schemaCol?.type) &&\r\n            !(col.linkTemplate && col.linkText)) {\r\n            colDef.cellStyle = { textAlign: 'right' };\r\n            colDef.cellClass = 'sb-field-right-align';\r\n        }\r\n\r\n        return colDef;\r\n    }\r\n\r\n    getSummaryRow(data: any[], columns: ColumnConfig[]): any {\r\n        const summaryRow = {};\r\n\r\n        columns.forEach(column => {\r\n            let value;\r\n            if (column.summaryRow?.aggregation) {\r\n                switch (column.summaryRow.aggregation) {\r\n                    case AggregationTypes.Sum:\r\n                        summaryRow[column.columnName] = data.reduce((sum, row) => sum + (row[column.columnName] || 0), 0);\r\n                        break;\r\n                    case AggregationTypes.Average:\r\n                        value = data.reduce((sum, row) => sum + (row[column.columnName] || 0), 0) / data.length\r\n                        summaryRow[column.columnName] = value == null || Number.isNaN(value) ? 0 : value;\r\n                        break;\r\n                    case AggregationTypes.Count:\r\n                        summaryRow[column.columnName] = data.length;\r\n                        break;\r\n                }\r\n            } else if (column.summaryRow?.text) {\r\n                summaryRow[column.columnName] = column.summaryRow.text;\r\n            } else {\r\n                summaryRow[column.columnName] = '';\r\n            }\r\n        });\r\n\r\n        return summaryRow;\r\n    }\r\n\r\n    getCheckBoxCellRenderer(params) {\r\n        const inputElement = document.createElement('input');\r\n        inputElement.type = 'checkbox';\r\n        inputElement.checked = !!params.value;\r\n        inputElement.disabled = true;\r\n        return inputElement;\r\n    }\r\n\r\n    getLinkCellRenderer(params, schemaCol: ExtendedJsonSchema, col: ColumnConfig) {\r\n        const linkElement = document.createElement('a');\r\n        linkElement.href = this.populateTemplateWithValues(col.linkTemplate, params.data);\r\n        linkElement.innerText = col.linkText || this.formatValue(\r\n            params.value,\r\n            schemaCol?.type,\r\n            schemaCol?.format,\r\n            schemaCol?.typeProperties?.currency,\r\n            schemaCol?.presentationProperties?.decimalPlaces\r\n        );\r\n        linkElement.target = '_blank';\r\n        return linkElement;\r\n    }\r\n\r\n    populateTemplateWithValues(template: string, data: any): string {\r\n        for (const key in data) {\r\n            if (key in data) {\r\n                template = template.replace(`{${key}}`, data[key]);\r\n            }\r\n        }\r\n        return template;\r\n    }\r\n\r\n    formatValue(params, type: JsonDataTypes, format: JsonStringFormats, currency?: string, decimalPlaces?: number, sumValueFormat?: SummaryValueFormats, sumText?: string, sumDP?: number) {\r\n        if (params.value === null || type === null)\r\n            return params.value;\r\n\r\n        let formattedValue = params.value;\r\n        \r\n        if (!params.node.rowPinned || sumValueFormat === SummaryValueFormats.Inherit) {\r\n            switch (type) {\r\n                case JsonDataTypes.String:\r\n                    formattedValue = [JsonStringFormats.Date, JsonStringFormats.DateTime].includes(format) ? dateValueFormatter(formattedValue) : formattedValue;\r\n                    break;\r\n                case JsonDataTypes.Decimal:\r\n                case JsonDataTypes.Integer:\r\n                    formattedValue = currency ? formatCurrency(formattedValue, currency, decimalPlaces) : numericValueFormatter(formattedValue, decimalPlaces);\r\n                    break;\r\n            }\r\n        }\r\n\r\n        // pinned row format (summary)\r\n        if (params.node.rowPinned) {\r\n            switch (sumValueFormat) {\r\n                case SummaryValueFormats.Numeric:\r\n                    formattedValue = numericValueFormatter(formattedValue, sumDP ?? 0);\r\n                    break;\r\n            }\r\n\r\n            if (sumText) {\r\n                formattedValue = sumText.replace('{value}', formattedValue);\r\n            }\r\n        }\r\n\r\n        return formattedValue;\r\n    }\r\n}\r\n"], "mappings": ";AAGA,SAASA,kBAAkB,EAAEC,cAAc,EAAEC,qBAAqB,QAAQ,2CAA2C;AAErH,SAASC,cAAc,QAAQ,MAAM;AAErC,SAASC,aAAa,EAAEC,iBAAiB,QAAQ,uCAAuC;AAExF,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,0CAA0C;;;AAKhG,OAAM,MAAOC,gBAAgB;EAEzBC,YAAoBC,iBAAoC;IAApC,KAAAA,iBAAiB,GAAjBA,iBAAiB;EAAuB;EAE5DC,cAAcA,CAAA;IACV,OAAO;MACHC,aAAa,EAAE;QACXC,IAAI,EAAE;OACT;MACDC,WAAW,EAAGC,MAAM,IAAI;QACpB,IAAIA,MAAM,CAACC,IAAI,CAACC,SAAS,EAAE;UACvB,OAAO;YAAEC,UAAU,EAAE;UAAM,CAAE;QACjC;MACJ;KACH;EACL;EAEMC,oBAAoBA,CAACC,KAAkB;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACzC,IAAI,CAACF,KAAK,EAAEG,OAAO,EAAEC,MAAM,EAAE,OAAO,EAAE;MAEtC,MAAMC,SAAS,SAAStB,cAAc,CAACkB,KAAI,CAACX,iBAAiB,CAACgB,YAAY,CAACN,KAAK,CAACO,WAAW,CAAC,CAAC;MAC9F,IAAI,CAACF,SAAS,EAAEG,UAAU,EAAE,OAAO,EAAE;MAErC,OAAOR,KAAK,CAACG,OAAO,CAACM,GAAG,CAACC,GAAG,IAAIT,KAAI,CAACU,wBAAwB,CAACD,GAAG,EAAEL,SAAS,CAACG,UAAU,EAAER,KAAK,CAAC,CAAC;IAAC;EACrG;EAEAW,wBAAwBA,CAACD,GAAiB,EAAEE,MAAM,EAAEZ,KAAkB;IAClE,MAAMa,SAAS,GAAGD,MAAM,CAACE,UAAU,CAACJ,GAAG,CAACK,UAAU,CAAC;IACnD,MAAMC,MAAM,GAAW;MACnBC,KAAK,EAAEP,GAAG,CAACK,UAAU;MACrBG,UAAU,EAAER,GAAG,CAACS,WAAW,IAAIN,SAAS,EAAEO,sBAAsB,EAAED,WAAW,IAAIT,GAAG,CAACK,UAAU;MAC/FM,cAAc,EAAE,IAAI;MACpBC,SAAS,EAAE,IAAI;MACfC,MAAM,EAAE,IAAI;MACZC,cAAc,EAAExB,KAAK,CAACyB,aAAa;MACnCC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAE,CAAClB,GAAG,CAACmB,OAAO;MAClBC,cAAc,EAAEnC,MAAM,IAAI,IAAI,CAACoC,WAAW,CACtCpC,MAAM,EACNkB,SAAS,EAAEmB,IAAI,EACfnB,SAAS,EAAEoB,MAAM,EACjBpB,SAAS,EAAEqB,cAAc,EAAEC,QAAQ,EACnCtB,SAAS,EAAEO,sBAAsB,EAAEgB,aAAa,EAChD1B,GAAG,CAAC2B,UAAU,EAAEC,WAAW,EAC3B5B,GAAG,CAAC2B,UAAU,EAAEE,IAAI,EACpB7B,GAAG,CAAC2B,UAAU,EAAED,aAAa;KAEpC;IAED,IAAIvB,SAAS,EAAEmB,IAAI,KAAKhD,aAAa,CAACwD,OAAO,EAAE;MAC3CxB,MAAM,CAACyB,YAAY,GAAG,IAAI,CAACC,uBAAuB;IACtD;IAEA,IAAIhC,GAAG,CAACiC,YAAY,EAAE;MAClB3B,MAAM,CAACyB,YAAY,GAAG9C,MAAM,IAAI,IAAI,CAACiD,mBAAmB,CAACjD,MAAM,EAAEkB,SAAS,EAAEH,GAAG,CAAC;IACpF;IAEA;IACA,IAAI,CAAC1B,aAAa,CAAC6D,OAAO,EAAE7D,aAAa,CAAC8D,OAAO,CAAC,CAACC,QAAQ,CAAClC,SAAS,EAAEmB,IAAI,CAAC,IACxE,EAAEtB,GAAG,CAACiC,YAAY,IAAIjC,GAAG,CAACsC,QAAQ,CAAC,EAAE;MACrChC,MAAM,CAACiC,SAAS,GAAG;QAAEC,SAAS,EAAE;MAAO,CAAE;MACzClC,MAAM,CAACmC,SAAS,GAAG,sBAAsB;IAC7C;IAEA,OAAOnC,MAAM;EACjB;EAEAoC,aAAaA,CAACC,IAAW,EAAElD,OAAuB;IAC9C,MAAMkC,UAAU,GAAG,EAAE;IAErBlC,OAAO,CAACmD,OAAO,CAACC,MAAM,IAAG;MACrB,IAAIC,KAAK;MACT,IAAID,MAAM,CAAClB,UAAU,EAAEoB,WAAW,EAAE;QAChC,QAAQF,MAAM,CAAClB,UAAU,CAACoB,WAAW;UACjC,KAAKvE,gBAAgB,CAACwE,GAAG;YACrBrB,UAAU,CAACkB,MAAM,CAACxC,UAAU,CAAC,GAAGsC,IAAI,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAACN,MAAM,CAACxC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;YACjG;UACJ,KAAK7B,gBAAgB,CAAC4E,OAAO;YACzBN,KAAK,GAAGH,IAAI,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAACN,MAAM,CAACxC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGsC,IAAI,CAACjD,MAAM;YACvFiC,UAAU,CAACkB,MAAM,CAACxC,UAAU,CAAC,GAAGyC,KAAK,IAAI,IAAI,IAAIO,MAAM,CAACC,KAAK,CAACR,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;YAChF;UACJ,KAAKtE,gBAAgB,CAAC+E,KAAK;YACvB5B,UAAU,CAACkB,MAAM,CAACxC,UAAU,CAAC,GAAGsC,IAAI,CAACjD,MAAM;YAC3C;QACR;MACJ,CAAC,MAAM,IAAImD,MAAM,CAAClB,UAAU,EAAEE,IAAI,EAAE;QAChCF,UAAU,CAACkB,MAAM,CAACxC,UAAU,CAAC,GAAGwC,MAAM,CAAClB,UAAU,CAACE,IAAI;MAC1D,CAAC,MAAM;QACHF,UAAU,CAACkB,MAAM,CAACxC,UAAU,CAAC,GAAG,EAAE;MACtC;IACJ,CAAC,CAAC;IAEF,OAAOsB,UAAU;EACrB;EAEAK,uBAAuBA,CAAC/C,MAAM;IAC1B,MAAMuE,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACpDF,YAAY,CAAClC,IAAI,GAAG,UAAU;IAC9BkC,YAAY,CAACG,OAAO,GAAG,CAAC,CAAC1E,MAAM,CAAC6D,KAAK;IACrCU,YAAY,CAACI,QAAQ,GAAG,IAAI;IAC5B,OAAOJ,YAAY;EACvB;EAEAtB,mBAAmBA,CAACjD,MAAM,EAAEkB,SAA6B,EAAEH,GAAiB;IACxE,MAAM6D,WAAW,GAAGJ,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IAC/CG,WAAW,CAACC,IAAI,GAAG,IAAI,CAACC,0BAA0B,CAAC/D,GAAG,CAACiC,YAAY,EAAEhD,MAAM,CAAC0D,IAAI,CAAC;IACjFkB,WAAW,CAACG,SAAS,GAAGhE,GAAG,CAACsC,QAAQ,IAAI,IAAI,CAACjB,WAAW,CACpDpC,MAAM,CAAC6D,KAAK,EACZ3C,SAAS,EAAEmB,IAAI,EACfnB,SAAS,EAAEoB,MAAM,EACjBpB,SAAS,EAAEqB,cAAc,EAAEC,QAAQ,EACnCtB,SAAS,EAAEO,sBAAsB,EAAEgB,aAAa,CACnD;IACDmC,WAAW,CAACI,MAAM,GAAG,QAAQ;IAC7B,OAAOJ,WAAW;EACtB;EAEAE,0BAA0BA,CAACG,QAAgB,EAAEvB,IAAS;IAClD,KAAK,MAAMwB,GAAG,IAAIxB,IAAI,EAAE;MACpB,IAAIwB,GAAG,IAAIxB,IAAI,EAAE;QACbuB,QAAQ,GAAGA,QAAQ,CAACE,OAAO,CAAC,IAAID,GAAG,GAAG,EAAExB,IAAI,CAACwB,GAAG,CAAC,CAAC;MACtD;IACJ;IACA,OAAOD,QAAQ;EACnB;EAEA7C,WAAWA,CAACpC,MAAM,EAAEqC,IAAmB,EAAEC,MAAyB,EAAEE,QAAiB,EAAEC,aAAsB,EAAE2C,cAAoC,EAAEC,OAAgB,EAAEC,KAAc;IACjL,IAAItF,MAAM,CAAC6D,KAAK,KAAK,IAAI,IAAIxB,IAAI,KAAK,IAAI,EACtC,OAAOrC,MAAM,CAAC6D,KAAK;IAEvB,IAAI0B,cAAc,GAAGvF,MAAM,CAAC6D,KAAK;IAEjC,IAAI,CAAC7D,MAAM,CAACC,IAAI,CAACC,SAAS,IAAIkF,cAAc,KAAK5F,mBAAmB,CAACgG,OAAO,EAAE;MAC1E,QAAQnD,IAAI;QACR,KAAKhD,aAAa,CAACoG,MAAM;UACrBF,cAAc,GAAG,CAACjG,iBAAiB,CAACoG,IAAI,EAAEpG,iBAAiB,CAACqG,QAAQ,CAAC,CAACvC,QAAQ,CAACd,MAAM,CAAC,GAAGrD,kBAAkB,CAACsG,cAAc,CAAC,GAAGA,cAAc;UAC5I;QACJ,KAAKlG,aAAa,CAAC6D,OAAO;QAC1B,KAAK7D,aAAa,CAAC8D,OAAO;UACtBoC,cAAc,GAAG/C,QAAQ,GAAGtD,cAAc,CAACqG,cAAc,EAAE/C,QAAQ,EAAEC,aAAa,CAAC,GAAGtD,qBAAqB,CAACoG,cAAc,EAAE9C,aAAa,CAAC;UAC1I;MACR;IACJ;IAEA;IACA,IAAIzC,MAAM,CAACC,IAAI,CAACC,SAAS,EAAE;MACvB,QAAQkF,cAAc;QAClB,KAAK5F,mBAAmB,CAACoG,OAAO;UAC5BL,cAAc,GAAGpG,qBAAqB,CAACoG,cAAc,EAAED,KAAK,IAAI,CAAC,CAAC;UAClE;MACR;MAEA,IAAID,OAAO,EAAE;QACTE,cAAc,GAAGF,OAAO,CAACF,OAAO,CAAC,SAAS,EAAEI,cAAc,CAAC;MAC/D;IACJ;IAEA,OAAOA,cAAc;EACzB;;;uBA/JS9F,gBAAgB,EAAAoG,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,iBAAA;IAAA;EAAA;;;aAAhBvG,gBAAgB;MAAAwG,OAAA,EAAhBxG,gBAAgB,CAAAyG,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}