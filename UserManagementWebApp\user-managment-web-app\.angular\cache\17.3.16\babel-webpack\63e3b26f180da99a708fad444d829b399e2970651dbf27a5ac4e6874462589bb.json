{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport { AuthAction, assertTokenProviderConfig, AmplifyUrl } from '@aws-amplify/core/internals/utils';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { InitiateAuthException } from '../types/errors.mjs';\nimport { AuthErrorCodes } from '../../../common/AuthErrorStrings.mjs';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { USER_ALREADY_AUTHENTICATED_EXCEPTION } from '../../../errors/constants.mjs';\nimport { getCurrentUser } from '../apis/getCurrentUser.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { createInitiateAuthClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createInitiateAuthClient.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport { createRespondToAuthChallengeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createRespondToAuthChallengeClient.mjs';\nimport { createVerifySoftwareTokenClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createVerifySoftwareTokenClient.mjs';\nimport { createAssociateSoftwareTokenClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createAssociateSoftwareTokenClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { handleWebAuthnSignInResult } from '../../../client/flows/userAuth/handleWebAuthnSignInResult.mjs';\nimport { handlePasswordSRP } from '../../../client/flows/shared/handlePasswordSRP.mjs';\nimport { initiateSelectedChallenge } from '../../../client/flows/userAuth/handleSelectChallenge.mjs';\nimport { handleSelectChallengeWithPassword } from '../../../client/flows/userAuth/handleSelectChallengeWithPassword.mjs';\nimport { handleSelectChallengeWithPasswordSRP } from '../../../client/flows/userAuth/handleSelectChallengeWithPasswordSRP.mjs';\nimport '../../../client/utils/store/autoSignInStore.mjs';\nimport { signInStore } from '../../../client/utils/store/signInStore.mjs';\nimport { getAuthenticationHelper } from './srp/getAuthenticationHelper.mjs';\nimport './srp/constants.mjs';\nimport '@aws-crypto/sha256-js';\nimport { getUserContextData } from './userContextData.mjs';\nimport { handlePasswordVerifierChallenge } from './handlePasswordVerifierChallenge.mjs';\nimport { handleDeviceSRPAuth } from './handleDeviceSRPAuth.mjs';\nimport { retryOnResourceNotFoundException } from './retryOnResourceNotFoundException.mjs';\nimport { setActiveSignInUsername } from './setActiveSignInUsername.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst USER_ATTRIBUTES = 'userAttributes.';\nfunction isWebAuthnResultAuthSignInOutput(result) {\n  return 'isSignedIn' in result && 'nextStep' in result;\n}\nfunction handleCustomChallenge(_x) {\n  return _handleCustomChallenge.apply(this, arguments);\n}\nfunction _handleCustomChallenge() {\n  _handleCustomChallenge = _asyncToGenerator(function* ({\n    challengeResponse,\n    clientMetadata,\n    session,\n    username,\n    config,\n    tokenOrchestrator\n  }) {\n    const {\n      userPoolId,\n      userPoolClientId,\n      userPoolEndpoint\n    } = config;\n    const challengeResponses = {\n      USERNAME: username,\n      ANSWER: challengeResponse\n    };\n    const deviceMetadata = yield tokenOrchestrator?.getDeviceMetadata(username);\n    if (deviceMetadata && deviceMetadata.deviceKey) {\n      challengeResponses.DEVICE_KEY = deviceMetadata.deviceKey;\n    }\n    const UserContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const jsonReq = {\n      ChallengeName: 'CUSTOM_CHALLENGE',\n      ChallengeResponses: challengeResponses,\n      Session: session,\n      ClientMetadata: clientMetadata,\n      ClientId: userPoolClientId,\n      UserContextData\n    };\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const response = yield respondToAuthChallenge({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn)\n    }, jsonReq);\n    if (response.ChallengeName === 'DEVICE_SRP_AUTH') {\n      return handleDeviceSRPAuth({\n        username,\n        config,\n        clientMetadata,\n        session: response.Session,\n        tokenOrchestrator\n      });\n    }\n    return response;\n  });\n  return _handleCustomChallenge.apply(this, arguments);\n}\nfunction handleMFASetupChallenge(_x2) {\n  return _handleMFASetupChallenge.apply(this, arguments);\n}\nfunction _handleMFASetupChallenge() {\n  _handleMFASetupChallenge = _asyncToGenerator(function* ({\n    challengeResponse,\n    username,\n    clientMetadata,\n    session,\n    deviceName,\n    config\n  }) {\n    const {\n      userPoolId,\n      userPoolClientId,\n      userPoolEndpoint\n    } = config;\n    if (challengeResponse === 'EMAIL') {\n      return {\n        ChallengeName: 'MFA_SETUP',\n        Session: session,\n        ChallengeParameters: {\n          MFAS_CAN_SETUP: '[\"EMAIL_OTP\"]'\n        },\n        $metadata: {}\n      };\n    }\n    if (challengeResponse === 'TOTP') {\n      return {\n        ChallengeName: 'MFA_SETUP',\n        Session: session,\n        ChallengeParameters: {\n          MFAS_CAN_SETUP: '[\"SOFTWARE_TOKEN_MFA\"]'\n        },\n        $metadata: {}\n      };\n    }\n    const challengeResponses = {\n      USERNAME: username\n    };\n    const isTOTPCode = /^\\d+$/.test(challengeResponse);\n    if (isTOTPCode) {\n      const verifySoftwareToken = createVerifySoftwareTokenClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n          endpointOverride: userPoolEndpoint\n        })\n      });\n      const {\n        Session\n      } = yield verifySoftwareToken({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn)\n      }, {\n        UserCode: challengeResponse,\n        Session: session,\n        FriendlyDeviceName: deviceName\n      });\n      signInStore.dispatch({\n        type: 'SET_SIGN_IN_SESSION',\n        value: Session\n      });\n      const jsonReq = {\n        ChallengeName: 'MFA_SETUP',\n        ChallengeResponses: challengeResponses,\n        Session,\n        ClientMetadata: clientMetadata,\n        ClientId: userPoolClientId\n      };\n      const respondToAuthChallenge = createRespondToAuthChallengeClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n          endpointOverride: userPoolEndpoint\n        })\n      });\n      return respondToAuthChallenge({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn)\n      }, jsonReq);\n    }\n    const isEmail = challengeResponse.includes('@');\n    if (isEmail) {\n      challengeResponses.EMAIL = challengeResponse;\n      const jsonReq = {\n        ChallengeName: 'MFA_SETUP',\n        ChallengeResponses: challengeResponses,\n        Session: session,\n        ClientMetadata: clientMetadata,\n        ClientId: userPoolClientId\n      };\n      const respondToAuthChallenge = createRespondToAuthChallengeClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n          endpointOverride: userPoolEndpoint\n        })\n      });\n      return respondToAuthChallenge({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn)\n      }, jsonReq);\n    }\n    throw new AuthError({\n      name: AuthErrorCodes.SignInException,\n      message: `Cannot proceed with MFA setup using challengeResponse: ${challengeResponse}`,\n      recoverySuggestion: 'Try passing \"EMAIL\", \"TOTP\", a valid email, or OTP code as the challengeResponse.'\n    });\n  });\n  return _handleMFASetupChallenge.apply(this, arguments);\n}\nfunction handleSelectMFATypeChallenge(_x3) {\n  return _handleSelectMFATypeChallenge.apply(this, arguments);\n}\nfunction _handleSelectMFATypeChallenge() {\n  _handleSelectMFATypeChallenge = _asyncToGenerator(function* ({\n    challengeResponse,\n    username,\n    clientMetadata,\n    session,\n    config\n  }) {\n    const {\n      userPoolId,\n      userPoolClientId,\n      userPoolEndpoint\n    } = config;\n    assertValidationError(challengeResponse === 'TOTP' || challengeResponse === 'SMS' || challengeResponse === 'EMAIL', AuthValidationErrorCode.IncorrectMFAMethod);\n    const challengeResponses = {\n      USERNAME: username,\n      ANSWER: mapMfaType(challengeResponse)\n    };\n    const UserContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const jsonReq = {\n      ChallengeName: 'SELECT_MFA_TYPE',\n      ChallengeResponses: challengeResponses,\n      Session: session,\n      ClientMetadata: clientMetadata,\n      ClientId: userPoolClientId,\n      UserContextData\n    };\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    return respondToAuthChallenge({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn)\n    }, jsonReq);\n  });\n  return _handleSelectMFATypeChallenge.apply(this, arguments);\n}\nfunction handleCompleteNewPasswordChallenge(_x4) {\n  return _handleCompleteNewPasswordChallenge.apply(this, arguments);\n}\nfunction _handleCompleteNewPasswordChallenge() {\n  _handleCompleteNewPasswordChallenge = _asyncToGenerator(function* ({\n    challengeResponse,\n    clientMetadata,\n    session,\n    username,\n    requiredAttributes,\n    config\n  }) {\n    const {\n      userPoolId,\n      userPoolClientId,\n      userPoolEndpoint\n    } = config;\n    const challengeResponses = {\n      ...createAttributes(requiredAttributes),\n      NEW_PASSWORD: challengeResponse,\n      USERNAME: username\n    };\n    const UserContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const jsonReq = {\n      ChallengeName: 'NEW_PASSWORD_REQUIRED',\n      ChallengeResponses: challengeResponses,\n      ClientMetadata: clientMetadata,\n      Session: session,\n      ClientId: userPoolClientId,\n      UserContextData\n    };\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    return respondToAuthChallenge({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn)\n    }, jsonReq);\n  });\n  return _handleCompleteNewPasswordChallenge.apply(this, arguments);\n}\nfunction handleUserPasswordAuthFlow(_x5, _x6, _x7, _x8, _x9) {\n  return _handleUserPasswordAuthFlow.apply(this, arguments);\n}\nfunction _handleUserPasswordAuthFlow() {\n  _handleUserPasswordAuthFlow = _asyncToGenerator(function* (username, password, clientMetadata, config, tokenOrchestrator) {\n    const {\n      userPoolClientId,\n      userPoolId,\n      userPoolEndpoint\n    } = config;\n    const authParameters = {\n      USERNAME: username,\n      PASSWORD: password\n    };\n    const deviceMetadata = yield tokenOrchestrator.getDeviceMetadata(username);\n    if (deviceMetadata && deviceMetadata.deviceKey) {\n      authParameters.DEVICE_KEY = deviceMetadata.deviceKey;\n    }\n    const UserContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const jsonReq = {\n      AuthFlow: 'USER_PASSWORD_AUTH',\n      AuthParameters: authParameters,\n      ClientMetadata: clientMetadata,\n      ClientId: userPoolClientId,\n      UserContextData\n    };\n    const initiateAuth = createInitiateAuthClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const response = yield initiateAuth({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.SignIn)\n    }, jsonReq);\n    const activeUsername = response.ChallengeParameters?.USERNAME ?? response.ChallengeParameters?.USER_ID_FOR_SRP ?? username;\n    setActiveSignInUsername(activeUsername);\n    if (response.ChallengeName === 'DEVICE_SRP_AUTH') return handleDeviceSRPAuth({\n      username: activeUsername,\n      config,\n      clientMetadata,\n      session: response.Session,\n      tokenOrchestrator\n    });\n    return response;\n  });\n  return _handleUserPasswordAuthFlow.apply(this, arguments);\n}\nfunction handleUserSRPAuthFlow(_x10, _x11, _x12, _x13, _x14) {\n  return _handleUserSRPAuthFlow.apply(this, arguments);\n}\nfunction _handleUserSRPAuthFlow() {\n  _handleUserSRPAuthFlow = _asyncToGenerator(function* (username, password, clientMetadata, config, tokenOrchestrator) {\n    return handlePasswordSRP({\n      username,\n      password,\n      clientMetadata,\n      config,\n      tokenOrchestrator,\n      authFlow: 'USER_SRP_AUTH'\n    });\n  });\n  return _handleUserSRPAuthFlow.apply(this, arguments);\n}\nfunction handleCustomAuthFlowWithoutSRP(_x15, _x16, _x17, _x18) {\n  return _handleCustomAuthFlowWithoutSRP.apply(this, arguments);\n}\nfunction _handleCustomAuthFlowWithoutSRP() {\n  _handleCustomAuthFlowWithoutSRP = _asyncToGenerator(function* (username, clientMetadata, config, tokenOrchestrator) {\n    const {\n      userPoolClientId,\n      userPoolId,\n      userPoolEndpoint\n    } = config;\n    const authParameters = {\n      USERNAME: username\n    };\n    const deviceMetadata = yield tokenOrchestrator.getDeviceMetadata(username);\n    if (deviceMetadata && deviceMetadata.deviceKey) {\n      authParameters.DEVICE_KEY = deviceMetadata.deviceKey;\n    }\n    const UserContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const jsonReq = {\n      AuthFlow: 'CUSTOM_AUTH',\n      AuthParameters: authParameters,\n      ClientMetadata: clientMetadata,\n      ClientId: userPoolClientId,\n      UserContextData\n    };\n    const initiateAuth = createInitiateAuthClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const response = yield initiateAuth({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.SignIn)\n    }, jsonReq);\n    const activeUsername = response.ChallengeParameters?.USERNAME ?? username;\n    setActiveSignInUsername(activeUsername);\n    if (response.ChallengeName === 'DEVICE_SRP_AUTH') return handleDeviceSRPAuth({\n      username: activeUsername,\n      config,\n      clientMetadata,\n      session: response.Session,\n      tokenOrchestrator\n    });\n    return response;\n  });\n  return _handleCustomAuthFlowWithoutSRP.apply(this, arguments);\n}\nfunction handleCustomSRPAuthFlow(_x19, _x20, _x21, _x22, _x23) {\n  return _handleCustomSRPAuthFlow.apply(this, arguments);\n}\nfunction _handleCustomSRPAuthFlow() {\n  _handleCustomSRPAuthFlow = _asyncToGenerator(function* (username, password, clientMetadata, config, tokenOrchestrator) {\n    assertTokenProviderConfig(config);\n    const {\n      userPoolId,\n      userPoolClientId,\n      userPoolEndpoint\n    } = config;\n    const userPoolName = userPoolId?.split('_')[1] || '';\n    const authenticationHelper = yield getAuthenticationHelper(userPoolName);\n    const authParameters = {\n      USERNAME: username,\n      SRP_A: authenticationHelper.A.toString(16),\n      CHALLENGE_NAME: 'SRP_A'\n    };\n    const UserContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const jsonReq = {\n      AuthFlow: 'CUSTOM_AUTH',\n      AuthParameters: authParameters,\n      ClientMetadata: clientMetadata,\n      ClientId: userPoolClientId,\n      UserContextData\n    };\n    const initiateAuth = createInitiateAuthClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const {\n      ChallengeParameters: challengeParameters,\n      Session: session\n    } = yield initiateAuth({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.SignIn)\n    }, jsonReq);\n    const activeUsername = challengeParameters?.USERNAME ?? username;\n    setActiveSignInUsername(activeUsername);\n    return retryOnResourceNotFoundException(handlePasswordVerifierChallenge, [password, challengeParameters, clientMetadata, session, authenticationHelper, config, tokenOrchestrator], activeUsername, tokenOrchestrator);\n  });\n  return _handleCustomSRPAuthFlow.apply(this, arguments);\n}\nfunction getSignInResult(_x24) {\n  return _getSignInResult.apply(this, arguments);\n}\nfunction _getSignInResult() {\n  _getSignInResult = _asyncToGenerator(function* (params) {\n    const {\n      challengeName,\n      challengeParameters,\n      availableChallenges\n    } = params;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    switch (challengeName) {\n      case 'CUSTOM_CHALLENGE':\n        return {\n          isSignedIn: false,\n          nextStep: {\n            signInStep: 'CONFIRM_SIGN_IN_WITH_CUSTOM_CHALLENGE',\n            additionalInfo: challengeParameters\n          }\n        };\n      case 'MFA_SETUP':\n        {\n          const {\n            signInSession,\n            username\n          } = signInStore.getState();\n          const mfaSetupTypes = getMFATypes(parseMFATypes(challengeParameters.MFAS_CAN_SETUP)) || [];\n          const allowedMfaSetupTypes = getAllowedMfaSetupTypes(mfaSetupTypes);\n          const isTotpMfaSetupAvailable = allowedMfaSetupTypes.includes('TOTP');\n          const isEmailMfaSetupAvailable = allowedMfaSetupTypes.includes('EMAIL');\n          if (isTotpMfaSetupAvailable && isEmailMfaSetupAvailable) {\n            return {\n              isSignedIn: false,\n              nextStep: {\n                signInStep: 'CONTINUE_SIGN_IN_WITH_MFA_SETUP_SELECTION',\n                allowedMFATypes: allowedMfaSetupTypes\n              }\n            };\n          }\n          if (isEmailMfaSetupAvailable) {\n            return {\n              isSignedIn: false,\n              nextStep: {\n                signInStep: 'CONTINUE_SIGN_IN_WITH_EMAIL_SETUP'\n              }\n            };\n          }\n          if (isTotpMfaSetupAvailable) {\n            const associateSoftwareToken = createAssociateSoftwareTokenClient({\n              endpointResolver: createCognitoUserPoolEndpointResolver({\n                endpointOverride: authConfig.userPoolEndpoint\n              })\n            });\n            const {\n              Session,\n              SecretCode: secretCode\n            } = yield associateSoftwareToken({\n              region: getRegionFromUserPoolId(authConfig.userPoolId)\n            }, {\n              Session: signInSession\n            });\n            signInStore.dispatch({\n              type: 'SET_SIGN_IN_SESSION',\n              value: Session\n            });\n            return {\n              isSignedIn: false,\n              nextStep: {\n                signInStep: 'CONTINUE_SIGN_IN_WITH_TOTP_SETUP',\n                totpSetupDetails: getTOTPSetupDetails(secretCode, username)\n              }\n            };\n          }\n          throw new AuthError({\n            name: AuthErrorCodes.SignInException,\n            message: `Cannot initiate MFA setup from available types: ${mfaSetupTypes}`\n          });\n        }\n      case 'NEW_PASSWORD_REQUIRED':\n        return {\n          isSignedIn: false,\n          nextStep: {\n            signInStep: 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED',\n            missingAttributes: parseAttributes(challengeParameters.requiredAttributes)\n          }\n        };\n      case 'SELECT_MFA_TYPE':\n        return {\n          isSignedIn: false,\n          nextStep: {\n            signInStep: 'CONTINUE_SIGN_IN_WITH_MFA_SELECTION',\n            allowedMFATypes: getMFATypes(parseMFATypes(challengeParameters.MFAS_CAN_CHOOSE))\n          }\n        };\n      case 'SMS_OTP':\n      case 'SMS_MFA':\n        return {\n          isSignedIn: false,\n          nextStep: {\n            signInStep: 'CONFIRM_SIGN_IN_WITH_SMS_CODE',\n            codeDeliveryDetails: {\n              deliveryMedium: challengeParameters.CODE_DELIVERY_DELIVERY_MEDIUM,\n              destination: challengeParameters.CODE_DELIVERY_DESTINATION\n            }\n          }\n        };\n      case 'SOFTWARE_TOKEN_MFA':\n        return {\n          isSignedIn: false,\n          nextStep: {\n            signInStep: 'CONFIRM_SIGN_IN_WITH_TOTP_CODE'\n          }\n        };\n      case 'EMAIL_OTP':\n        return {\n          isSignedIn: false,\n          nextStep: {\n            signInStep: 'CONFIRM_SIGN_IN_WITH_EMAIL_CODE',\n            codeDeliveryDetails: {\n              deliveryMedium: challengeParameters.CODE_DELIVERY_DELIVERY_MEDIUM,\n              destination: challengeParameters.CODE_DELIVERY_DESTINATION\n            }\n          }\n        };\n      case 'WEB_AUTHN':\n        {\n          const result = yield handleWebAuthnSignInResult(challengeParameters);\n          if (isWebAuthnResultAuthSignInOutput(result)) {\n            return result;\n          }\n          return getSignInResult(result);\n        }\n      case 'PASSWORD':\n      case 'PASSWORD_SRP':\n        return {\n          isSignedIn: false,\n          nextStep: {\n            signInStep: 'CONFIRM_SIGN_IN_WITH_PASSWORD'\n          }\n        };\n      case 'SELECT_CHALLENGE':\n        return {\n          isSignedIn: false,\n          nextStep: {\n            signInStep: 'CONTINUE_SIGN_IN_WITH_FIRST_FACTOR_SELECTION',\n            availableChallenges\n          }\n        };\n    }\n    // TODO: remove this error message for production apps\n    throw new AuthError({\n      name: AuthErrorCodes.SignInException,\n      message: 'An error occurred during the sign in process. ' + `${challengeName} challengeName returned by the underlying service was not addressed.`\n    });\n  });\n  return _getSignInResult.apply(this, arguments);\n}\nfunction getTOTPSetupDetails(secretCode, username) {\n  return {\n    sharedSecret: secretCode,\n    getSetupUri: (appName, accountName) => {\n      const totpUri = `otpauth://totp/${appName}:${accountName ?? username}?secret=${secretCode}&issuer=${appName}`;\n      return new AmplifyUrl(totpUri);\n    }\n  };\n}\nfunction getSignInResultFromError(errorName) {\n  if (errorName === InitiateAuthException.PasswordResetRequiredException) {\n    return {\n      isSignedIn: false,\n      nextStep: {\n        signInStep: 'RESET_PASSWORD'\n      }\n    };\n  } else if (errorName === InitiateAuthException.UserNotConfirmedException) {\n    return {\n      isSignedIn: false,\n      nextStep: {\n        signInStep: 'CONFIRM_SIGN_UP'\n      }\n    };\n  }\n}\nfunction parseAttributes(attributes) {\n  if (!attributes) return [];\n  const parsedAttributes = JSON.parse(attributes).map(att => att.includes(USER_ATTRIBUTES) ? att.replace(USER_ATTRIBUTES, '') : att);\n  return parsedAttributes;\n}\nfunction createAttributes(attributes) {\n  if (!attributes) return {};\n  const newAttributes = {};\n  Object.entries(attributes).forEach(([key, value]) => {\n    if (value) newAttributes[`${USER_ATTRIBUTES}${key}`] = value;\n  });\n  return newAttributes;\n}\nfunction handleChallengeName(_x25, _x26, _x27, _x28, _x29, _x30, _x31, _x32) {\n  return _handleChallengeName.apply(this, arguments);\n}\nfunction _handleChallengeName() {\n  _handleChallengeName = _asyncToGenerator(function* (username, challengeName, session, challengeResponse, config, tokenOrchestrator, clientMetadata, options) {\n    const userAttributes = options?.userAttributes;\n    const deviceName = options?.friendlyDeviceName;\n    switch (challengeName) {\n      case 'WEB_AUTHN':\n      case 'SELECT_CHALLENGE':\n        if (challengeResponse === 'PASSWORD_SRP' || challengeResponse === 'PASSWORD') {\n          return {\n            ChallengeName: challengeResponse,\n            Session: session,\n            $metadata: {}\n          };\n        }\n        return initiateSelectedChallenge({\n          username,\n          session,\n          selectedChallenge: challengeResponse,\n          config,\n          clientMetadata\n        });\n      case 'SELECT_MFA_TYPE':\n        return handleSelectMFATypeChallenge({\n          challengeResponse,\n          clientMetadata,\n          session,\n          username,\n          config\n        });\n      case 'MFA_SETUP':\n        return handleMFASetupChallenge({\n          challengeResponse,\n          clientMetadata,\n          session,\n          username,\n          deviceName,\n          config\n        });\n      case 'NEW_PASSWORD_REQUIRED':\n        return handleCompleteNewPasswordChallenge({\n          challengeResponse,\n          clientMetadata,\n          session,\n          username,\n          requiredAttributes: userAttributes,\n          config\n        });\n      case 'CUSTOM_CHALLENGE':\n        return retryOnResourceNotFoundException(handleCustomChallenge, [{\n          challengeResponse,\n          clientMetadata,\n          session,\n          username,\n          config,\n          tokenOrchestrator\n        }], username, tokenOrchestrator);\n      case 'SMS_MFA':\n      case 'SOFTWARE_TOKEN_MFA':\n      case 'SMS_OTP':\n      case 'EMAIL_OTP':\n        return handleMFAChallenge({\n          challengeName,\n          challengeResponse,\n          clientMetadata,\n          session,\n          username,\n          config\n        });\n      case 'PASSWORD':\n        return handleSelectChallengeWithPassword(username, challengeResponse, clientMetadata, config, session);\n      case 'PASSWORD_SRP':\n        return handleSelectChallengeWithPasswordSRP(username, challengeResponse,\n        // This is the actual password\n        clientMetadata, config, session, tokenOrchestrator);\n    }\n    // TODO: remove this error message for production apps\n    throw new AuthError({\n      name: AuthErrorCodes.SignInException,\n      message: `An error occurred during the sign in process.\n\t\t${challengeName} challengeName returned by the underlying service was not addressed.`\n    });\n  });\n  return _handleChallengeName.apply(this, arguments);\n}\nfunction mapMfaType(mfa) {\n  let mfaType = 'SMS_MFA';\n  if (mfa === 'TOTP') mfaType = 'SOFTWARE_TOKEN_MFA';\n  if (mfa === 'EMAIL') mfaType = 'EMAIL_OTP';\n  return mfaType;\n}\nfunction getMFAType(type) {\n  if (type === 'SMS_MFA') return 'SMS';\n  if (type === 'SOFTWARE_TOKEN_MFA') return 'TOTP';\n  if (type === 'EMAIL_OTP') return 'EMAIL';\n  // TODO: log warning for unknown MFA type\n}\nfunction getMFATypes(types) {\n  if (!types) return undefined;\n  return types.map(getMFAType).filter(Boolean);\n}\nfunction parseMFATypes(mfa) {\n  if (!mfa) return [];\n  return JSON.parse(mfa);\n}\nfunction getAllowedMfaSetupTypes(availableMfaSetupTypes) {\n  return availableMfaSetupTypes.filter(authMfaType => authMfaType === 'EMAIL' || authMfaType === 'TOTP');\n}\nfunction assertUserNotAuthenticated() {\n  return _assertUserNotAuthenticated.apply(this, arguments);\n}\nfunction _assertUserNotAuthenticated() {\n  _assertUserNotAuthenticated = _asyncToGenerator(function* () {\n    let authUser;\n    try {\n      authUser = yield getCurrentUser();\n    } catch (error) {}\n    if (authUser && authUser.userId && authUser.username) {\n      throw new AuthError({\n        name: USER_ALREADY_AUTHENTICATED_EXCEPTION,\n        message: 'There is already a signed in user.',\n        recoverySuggestion: 'Call signOut before calling signIn again.'\n      });\n    }\n  });\n  return _assertUserNotAuthenticated.apply(this, arguments);\n}\nfunction getActiveSignInUsername(username) {\n  const state = signInStore.getState();\n  return state.username ?? username;\n}\nfunction handleMFAChallenge(_x33) {\n  return _handleMFAChallenge.apply(this, arguments);\n}\nfunction _handleMFAChallenge() {\n  _handleMFAChallenge = _asyncToGenerator(function* ({\n    challengeName,\n    challengeResponse,\n    clientMetadata,\n    session,\n    username,\n    config\n  }) {\n    const {\n      userPoolId,\n      userPoolClientId,\n      userPoolEndpoint\n    } = config;\n    const challengeResponses = {\n      USERNAME: username\n    };\n    if (challengeName === 'EMAIL_OTP') {\n      challengeResponses.EMAIL_OTP_CODE = challengeResponse;\n    }\n    if (challengeName === 'SMS_MFA') {\n      challengeResponses.SMS_MFA_CODE = challengeResponse;\n    }\n    if (challengeName === 'SMS_OTP') {\n      challengeResponses.SMS_OTP_CODE = challengeResponse;\n    }\n    if (challengeName === 'SOFTWARE_TOKEN_MFA') {\n      challengeResponses.SOFTWARE_TOKEN_MFA_CODE = challengeResponse;\n    }\n    const userContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const jsonReq = {\n      ChallengeName: challengeName,\n      ChallengeResponses: challengeResponses,\n      Session: session,\n      ClientMetadata: clientMetadata,\n      ClientId: userPoolClientId,\n      UserContextData: userContextData\n    };\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    return respondToAuthChallenge({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn)\n    }, jsonReq);\n  });\n  return _handleMFAChallenge.apply(this, arguments);\n}\nexport { assertUserNotAuthenticated, createAttributes, getActiveSignInUsername, getAllowedMfaSetupTypes, getMFAType, getMFATypes, getSignInResult, getSignInResultFromError, getTOTPSetupDetails, handleChallengeName, handleCompleteNewPasswordChallenge, handleCustomAuthFlowWithoutSRP, handleCustomChallenge, handleCustomSRPAuthFlow, handleMFAChallenge, handleMFASetupChallenge, handleSelectMFATypeChallenge, handleUserPasswordAuthFlow, handleUserSRPAuthFlow, mapMfaType, parseAttributes, parseMFATypes };", "map": {"version": 3, "names": ["Amplify", "AuthAction", "assertTokenProviderConfig", "AmplifyUrl", "<PERSON>th<PERSON><PERSON><PERSON>", "InitiateAuthException", "AuthErrorCodes", "AuthValidationErrorCode", "assertValidationError", "USER_ALREADY_AUTHENTICATED_EXCEPTION", "getCurrentUser", "getAuthUserAgentValue", "createInitiateAuthClient", "createRespondToAuthChallengeClient", "createVerifySoftwareTokenClient", "createAssociateSoftwareTokenClient", "createCognitoUserPoolEndpointResolver", "getRegionFromUserPoolId", "handleWebAuthnSignInResult", "handlePasswordSRP", "initiateSelectedChallenge", "handleSelectChallengeWithPassword", "handleSelectChallengeWithPasswordSRP", "signInStore", "getAuthenticationHelper", "getUserContextData", "handlePasswordVerifierChallenge", "handleDeviceSRPAuth", "retryOnResourceNotFoundException", "setActiveSignInUsername", "USER_ATTRIBUTES", "isWebAuthnResultAuthSignInOutput", "result", "handleCustomChallenge", "_x", "_handleCustomChallenge", "apply", "arguments", "_asyncToGenerator", "challengeResponse", "clientMetadata", "session", "username", "config", "tokenOrchestrator", "userPoolId", "userPoolClientId", "userPoolEndpoint", "challengeResponses", "USERNAME", "ANSWER", "deviceMetadata", "getDeviceMetadata", "deviceKey", "DEVICE_KEY", "UserContextData", "jsonReq", "ChallengeName", "ChallengeResponses", "Session", "ClientMetadata", "ClientId", "respondToAuthChallenge", "endpointResolver", "endpointOverride", "response", "region", "userAgentValue", "ConfirmSignIn", "handleMFASetupChallenge", "_x2", "_handleMFASetupChallenge", "deviceName", "ChallengeParameters", "MFAS_CAN_SETUP", "$metadata", "isTOTPCode", "test", "verifySoftwareToken", "UserCode", "FriendlyDeviceName", "dispatch", "type", "value", "isEmail", "includes", "EMAIL", "name", "SignInException", "message", "recoverySuggestion", "handleSelectMFATypeChallenge", "_x3", "_handleSelectMFATypeChallenge", "IncorrectMFAMethod", "mapMfaType", "handleCompleteNewPasswordChallenge", "_x4", "_handleCompleteNewPasswordChallenge", "requiredAttributes", "createAttributes", "NEW_PASSWORD", "handleUserPasswordAuthFlow", "_x5", "_x6", "_x7", "_x8", "_x9", "_handleUserPasswordAuthFlow", "password", "authParameters", "PASSWORD", "AuthFlow", "AuthParameters", "initiateAuth", "SignIn", "activeUsername", "USER_ID_FOR_SRP", "handleUserSRPAuthFlow", "_x10", "_x11", "_x12", "_x13", "_x14", "_handleUserSRPAuthFlow", "authFlow", "handleCustomAuthFlowWithoutSRP", "_x15", "_x16", "_x17", "_x18", "_handleCustomAuthFlowWithoutSRP", "handleCustomSRPAuthFlow", "_x19", "_x20", "_x21", "_x22", "_x23", "_handleCustomSRPAuthFlow", "userPoolName", "split", "authenticationHelper", "SRP_A", "A", "toString", "CHALLENGE_NAME", "challengeParameters", "getSignInResult", "_x24", "_getSignInResult", "params", "challenge<PERSON>ame", "availableChallenges", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "isSignedIn", "nextStep", "signInStep", "additionalInfo", "signInSession", "getState", "mfaSetupTypes", "getMFATypes", "parseMFATypes", "allowedMfaSetupTypes", "getAllowedMfaSetupTypes", "isTotpMfaSetupAvailable", "isEmailMfaSetupAvailable", "allowedMFATypes", "associateSoftwareToken", "SecretCode", "secretCode", "totpSetupDetails", "getTOTPSetupDetails", "missingAttributes", "parseAttributes", "MFAS_CAN_CHOOSE", "codeDeliveryDetails", "deliveryMedium", "CODE_DELIVERY_DELIVERY_MEDIUM", "destination", "CODE_DELIVERY_DESTINATION", "sharedSecret", "getSetupUri", "appName", "accountName", "totpUri", "getSignInResultFromError", "errorName", "PasswordResetRequiredException", "UserNotConfirmedException", "attributes", "parsedAttributes", "JSON", "parse", "map", "att", "replace", "newAttributes", "Object", "entries", "for<PERSON>ach", "key", "handleChallengeName", "_x25", "_x26", "_x27", "_x28", "_x29", "_x30", "_x31", "_x32", "_handleChallengeName", "options", "userAttributes", "friendlyDeviceName", "selected<PERSON>hall<PERSON><PERSON>", "handleMFAChallenge", "mfa", "mfaType", "getMFAType", "types", "undefined", "filter", "Boolean", "availableMfaSetupTypes", "authMfaType", "assertUserNotAuthenticated", "_assertUserNotAuthenticated", "authUser", "error", "userId", "getActiveSignInUsername", "state", "_x33", "_handleMFAChallenge", "EMAIL_OTP_CODE", "SMS_MFA_CODE", "SMS_OTP_CODE", "SOFTWARE_TOKEN_MFA_CODE", "userContextData"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/signInHelpers.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { AuthAction, assertTokenProviderConfig, AmplifyUrl } from '@aws-amplify/core/internals/utils';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { InitiateAuthException } from '../types/errors.mjs';\nimport { AuthErrorCodes } from '../../../common/AuthErrorStrings.mjs';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { USER_ALREADY_AUTHENTICATED_EXCEPTION } from '../../../errors/constants.mjs';\nimport { getCurrentUser } from '../apis/getCurrentUser.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { createInitiateAuthClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createInitiateAuthClient.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport { createRespondToAuthChallengeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createRespondToAuthChallengeClient.mjs';\nimport { createVerifySoftwareTokenClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createVerifySoftwareTokenClient.mjs';\nimport { createAssociateSoftwareTokenClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createAssociateSoftwareTokenClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { handleWebAuthnSignInResult } from '../../../client/flows/userAuth/handleWebAuthnSignInResult.mjs';\nimport { handlePasswordSRP } from '../../../client/flows/shared/handlePasswordSRP.mjs';\nimport { initiateSelectedChallenge } from '../../../client/flows/userAuth/handleSelectChallenge.mjs';\nimport { handleSelectChallengeWithPassword } from '../../../client/flows/userAuth/handleSelectChallengeWithPassword.mjs';\nimport { handleSelectChallengeWithPasswordSRP } from '../../../client/flows/userAuth/handleSelectChallengeWithPasswordSRP.mjs';\nimport '../../../client/utils/store/autoSignInStore.mjs';\nimport { signInStore } from '../../../client/utils/store/signInStore.mjs';\nimport { getAuthenticationHelper } from './srp/getAuthenticationHelper.mjs';\nimport './srp/constants.mjs';\nimport '@aws-crypto/sha256-js';\nimport { getUserContextData } from './userContextData.mjs';\nimport { handlePasswordVerifierChallenge } from './handlePasswordVerifierChallenge.mjs';\nimport { handleDeviceSRPAuth } from './handleDeviceSRPAuth.mjs';\nimport { retryOnResourceNotFoundException } from './retryOnResourceNotFoundException.mjs';\nimport { setActiveSignInUsername } from './setActiveSignInUsername.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst USER_ATTRIBUTES = 'userAttributes.';\nfunction isWebAuthnResultAuthSignInOutput(result) {\n    return 'isSignedIn' in result && 'nextStep' in result;\n}\nasync function handleCustomChallenge({ challengeResponse, clientMetadata, session, username, config, tokenOrchestrator, }) {\n    const { userPoolId, userPoolClientId, userPoolEndpoint } = config;\n    const challengeResponses = {\n        USERNAME: username,\n        ANSWER: challengeResponse,\n    };\n    const deviceMetadata = await tokenOrchestrator?.getDeviceMetadata(username);\n    if (deviceMetadata && deviceMetadata.deviceKey) {\n        challengeResponses.DEVICE_KEY = deviceMetadata.deviceKey;\n    }\n    const UserContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const jsonReq = {\n        ChallengeName: 'CUSTOM_CHALLENGE',\n        ChallengeResponses: challengeResponses,\n        Session: session,\n        ClientMetadata: clientMetadata,\n        ClientId: userPoolClientId,\n        UserContextData,\n    };\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const response = await respondToAuthChallenge({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn),\n    }, jsonReq);\n    if (response.ChallengeName === 'DEVICE_SRP_AUTH') {\n        return handleDeviceSRPAuth({\n            username,\n            config,\n            clientMetadata,\n            session: response.Session,\n            tokenOrchestrator,\n        });\n    }\n    return response;\n}\nasync function handleMFASetupChallenge({ challengeResponse, username, clientMetadata, session, deviceName, config, }) {\n    const { userPoolId, userPoolClientId, userPoolEndpoint } = config;\n    if (challengeResponse === 'EMAIL') {\n        return {\n            ChallengeName: 'MFA_SETUP',\n            Session: session,\n            ChallengeParameters: {\n                MFAS_CAN_SETUP: '[\"EMAIL_OTP\"]',\n            },\n            $metadata: {},\n        };\n    }\n    if (challengeResponse === 'TOTP') {\n        return {\n            ChallengeName: 'MFA_SETUP',\n            Session: session,\n            ChallengeParameters: {\n                MFAS_CAN_SETUP: '[\"SOFTWARE_TOKEN_MFA\"]',\n            },\n            $metadata: {},\n        };\n    }\n    const challengeResponses = {\n        USERNAME: username,\n    };\n    const isTOTPCode = /^\\d+$/.test(challengeResponse);\n    if (isTOTPCode) {\n        const verifySoftwareToken = createVerifySoftwareTokenClient({\n            endpointResolver: createCognitoUserPoolEndpointResolver({\n                endpointOverride: userPoolEndpoint,\n            }),\n        });\n        const { Session } = await verifySoftwareToken({\n            region: getRegionFromUserPoolId(userPoolId),\n            userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn),\n        }, {\n            UserCode: challengeResponse,\n            Session: session,\n            FriendlyDeviceName: deviceName,\n        });\n        signInStore.dispatch({\n            type: 'SET_SIGN_IN_SESSION',\n            value: Session,\n        });\n        const jsonReq = {\n            ChallengeName: 'MFA_SETUP',\n            ChallengeResponses: challengeResponses,\n            Session,\n            ClientMetadata: clientMetadata,\n            ClientId: userPoolClientId,\n        };\n        const respondToAuthChallenge = createRespondToAuthChallengeClient({\n            endpointResolver: createCognitoUserPoolEndpointResolver({\n                endpointOverride: userPoolEndpoint,\n            }),\n        });\n        return respondToAuthChallenge({\n            region: getRegionFromUserPoolId(userPoolId),\n            userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn),\n        }, jsonReq);\n    }\n    const isEmail = challengeResponse.includes('@');\n    if (isEmail) {\n        challengeResponses.EMAIL = challengeResponse;\n        const jsonReq = {\n            ChallengeName: 'MFA_SETUP',\n            ChallengeResponses: challengeResponses,\n            Session: session,\n            ClientMetadata: clientMetadata,\n            ClientId: userPoolClientId,\n        };\n        const respondToAuthChallenge = createRespondToAuthChallengeClient({\n            endpointResolver: createCognitoUserPoolEndpointResolver({\n                endpointOverride: userPoolEndpoint,\n            }),\n        });\n        return respondToAuthChallenge({\n            region: getRegionFromUserPoolId(userPoolId),\n            userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn),\n        }, jsonReq);\n    }\n    throw new AuthError({\n        name: AuthErrorCodes.SignInException,\n        message: `Cannot proceed with MFA setup using challengeResponse: ${challengeResponse}`,\n        recoverySuggestion: 'Try passing \"EMAIL\", \"TOTP\", a valid email, or OTP code as the challengeResponse.',\n    });\n}\nasync function handleSelectMFATypeChallenge({ challengeResponse, username, clientMetadata, session, config, }) {\n    const { userPoolId, userPoolClientId, userPoolEndpoint } = config;\n    assertValidationError(challengeResponse === 'TOTP' ||\n        challengeResponse === 'SMS' ||\n        challengeResponse === 'EMAIL', AuthValidationErrorCode.IncorrectMFAMethod);\n    const challengeResponses = {\n        USERNAME: username,\n        ANSWER: mapMfaType(challengeResponse),\n    };\n    const UserContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const jsonReq = {\n        ChallengeName: 'SELECT_MFA_TYPE',\n        ChallengeResponses: challengeResponses,\n        Session: session,\n        ClientMetadata: clientMetadata,\n        ClientId: userPoolClientId,\n        UserContextData,\n    };\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    return respondToAuthChallenge({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn),\n    }, jsonReq);\n}\nasync function handleCompleteNewPasswordChallenge({ challengeResponse, clientMetadata, session, username, requiredAttributes, config, }) {\n    const { userPoolId, userPoolClientId, userPoolEndpoint } = config;\n    const challengeResponses = {\n        ...createAttributes(requiredAttributes),\n        NEW_PASSWORD: challengeResponse,\n        USERNAME: username,\n    };\n    const UserContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const jsonReq = {\n        ChallengeName: 'NEW_PASSWORD_REQUIRED',\n        ChallengeResponses: challengeResponses,\n        ClientMetadata: clientMetadata,\n        Session: session,\n        ClientId: userPoolClientId,\n        UserContextData,\n    };\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    return respondToAuthChallenge({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn),\n    }, jsonReq);\n}\nasync function handleUserPasswordAuthFlow(username, password, clientMetadata, config, tokenOrchestrator) {\n    const { userPoolClientId, userPoolId, userPoolEndpoint } = config;\n    const authParameters = {\n        USERNAME: username,\n        PASSWORD: password,\n    };\n    const deviceMetadata = await tokenOrchestrator.getDeviceMetadata(username);\n    if (deviceMetadata && deviceMetadata.deviceKey) {\n        authParameters.DEVICE_KEY = deviceMetadata.deviceKey;\n    }\n    const UserContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const jsonReq = {\n        AuthFlow: 'USER_PASSWORD_AUTH',\n        AuthParameters: authParameters,\n        ClientMetadata: clientMetadata,\n        ClientId: userPoolClientId,\n        UserContextData,\n    };\n    const initiateAuth = createInitiateAuthClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const response = await initiateAuth({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.SignIn),\n    }, jsonReq);\n    const activeUsername = response.ChallengeParameters?.USERNAME ??\n        response.ChallengeParameters?.USER_ID_FOR_SRP ??\n        username;\n    setActiveSignInUsername(activeUsername);\n    if (response.ChallengeName === 'DEVICE_SRP_AUTH')\n        return handleDeviceSRPAuth({\n            username: activeUsername,\n            config,\n            clientMetadata,\n            session: response.Session,\n            tokenOrchestrator,\n        });\n    return response;\n}\nasync function handleUserSRPAuthFlow(username, password, clientMetadata, config, tokenOrchestrator) {\n    return handlePasswordSRP({\n        username,\n        password,\n        clientMetadata,\n        config,\n        tokenOrchestrator,\n        authFlow: 'USER_SRP_AUTH',\n    });\n}\nasync function handleCustomAuthFlowWithoutSRP(username, clientMetadata, config, tokenOrchestrator) {\n    const { userPoolClientId, userPoolId, userPoolEndpoint } = config;\n    const authParameters = {\n        USERNAME: username,\n    };\n    const deviceMetadata = await tokenOrchestrator.getDeviceMetadata(username);\n    if (deviceMetadata && deviceMetadata.deviceKey) {\n        authParameters.DEVICE_KEY = deviceMetadata.deviceKey;\n    }\n    const UserContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const jsonReq = {\n        AuthFlow: 'CUSTOM_AUTH',\n        AuthParameters: authParameters,\n        ClientMetadata: clientMetadata,\n        ClientId: userPoolClientId,\n        UserContextData,\n    };\n    const initiateAuth = createInitiateAuthClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const response = await initiateAuth({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.SignIn),\n    }, jsonReq);\n    const activeUsername = response.ChallengeParameters?.USERNAME ?? username;\n    setActiveSignInUsername(activeUsername);\n    if (response.ChallengeName === 'DEVICE_SRP_AUTH')\n        return handleDeviceSRPAuth({\n            username: activeUsername,\n            config,\n            clientMetadata,\n            session: response.Session,\n            tokenOrchestrator,\n        });\n    return response;\n}\nasync function handleCustomSRPAuthFlow(username, password, clientMetadata, config, tokenOrchestrator) {\n    assertTokenProviderConfig(config);\n    const { userPoolId, userPoolClientId, userPoolEndpoint } = config;\n    const userPoolName = userPoolId?.split('_')[1] || '';\n    const authenticationHelper = await getAuthenticationHelper(userPoolName);\n    const authParameters = {\n        USERNAME: username,\n        SRP_A: authenticationHelper.A.toString(16),\n        CHALLENGE_NAME: 'SRP_A',\n    };\n    const UserContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const jsonReq = {\n        AuthFlow: 'CUSTOM_AUTH',\n        AuthParameters: authParameters,\n        ClientMetadata: clientMetadata,\n        ClientId: userPoolClientId,\n        UserContextData,\n    };\n    const initiateAuth = createInitiateAuthClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const { ChallengeParameters: challengeParameters, Session: session } = await initiateAuth({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.SignIn),\n    }, jsonReq);\n    const activeUsername = challengeParameters?.USERNAME ?? username;\n    setActiveSignInUsername(activeUsername);\n    return retryOnResourceNotFoundException(handlePasswordVerifierChallenge, [\n        password,\n        challengeParameters,\n        clientMetadata,\n        session,\n        authenticationHelper,\n        config,\n        tokenOrchestrator,\n    ], activeUsername, tokenOrchestrator);\n}\nasync function getSignInResult(params) {\n    const { challengeName, challengeParameters, availableChallenges } = params;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    switch (challengeName) {\n        case 'CUSTOM_CHALLENGE':\n            return {\n                isSignedIn: false,\n                nextStep: {\n                    signInStep: 'CONFIRM_SIGN_IN_WITH_CUSTOM_CHALLENGE',\n                    additionalInfo: challengeParameters,\n                },\n            };\n        case 'MFA_SETUP': {\n            const { signInSession, username } = signInStore.getState();\n            const mfaSetupTypes = getMFATypes(parseMFATypes(challengeParameters.MFAS_CAN_SETUP)) || [];\n            const allowedMfaSetupTypes = getAllowedMfaSetupTypes(mfaSetupTypes);\n            const isTotpMfaSetupAvailable = allowedMfaSetupTypes.includes('TOTP');\n            const isEmailMfaSetupAvailable = allowedMfaSetupTypes.includes('EMAIL');\n            if (isTotpMfaSetupAvailable && isEmailMfaSetupAvailable) {\n                return {\n                    isSignedIn: false,\n                    nextStep: {\n                        signInStep: 'CONTINUE_SIGN_IN_WITH_MFA_SETUP_SELECTION',\n                        allowedMFATypes: allowedMfaSetupTypes,\n                    },\n                };\n            }\n            if (isEmailMfaSetupAvailable) {\n                return {\n                    isSignedIn: false,\n                    nextStep: {\n                        signInStep: 'CONTINUE_SIGN_IN_WITH_EMAIL_SETUP',\n                    },\n                };\n            }\n            if (isTotpMfaSetupAvailable) {\n                const associateSoftwareToken = createAssociateSoftwareTokenClient({\n                    endpointResolver: createCognitoUserPoolEndpointResolver({\n                        endpointOverride: authConfig.userPoolEndpoint,\n                    }),\n                });\n                const { Session, SecretCode: secretCode } = await associateSoftwareToken({ region: getRegionFromUserPoolId(authConfig.userPoolId) }, {\n                    Session: signInSession,\n                });\n                signInStore.dispatch({\n                    type: 'SET_SIGN_IN_SESSION',\n                    value: Session,\n                });\n                return {\n                    isSignedIn: false,\n                    nextStep: {\n                        signInStep: 'CONTINUE_SIGN_IN_WITH_TOTP_SETUP',\n                        totpSetupDetails: getTOTPSetupDetails(secretCode, username),\n                    },\n                };\n            }\n            throw new AuthError({\n                name: AuthErrorCodes.SignInException,\n                message: `Cannot initiate MFA setup from available types: ${mfaSetupTypes}`,\n            });\n        }\n        case 'NEW_PASSWORD_REQUIRED':\n            return {\n                isSignedIn: false,\n                nextStep: {\n                    signInStep: 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED',\n                    missingAttributes: parseAttributes(challengeParameters.requiredAttributes),\n                },\n            };\n        case 'SELECT_MFA_TYPE':\n            return {\n                isSignedIn: false,\n                nextStep: {\n                    signInStep: 'CONTINUE_SIGN_IN_WITH_MFA_SELECTION',\n                    allowedMFATypes: getMFATypes(parseMFATypes(challengeParameters.MFAS_CAN_CHOOSE)),\n                },\n            };\n        case 'SMS_OTP':\n        case 'SMS_MFA':\n            return {\n                isSignedIn: false,\n                nextStep: {\n                    signInStep: 'CONFIRM_SIGN_IN_WITH_SMS_CODE',\n                    codeDeliveryDetails: {\n                        deliveryMedium: challengeParameters.CODE_DELIVERY_DELIVERY_MEDIUM,\n                        destination: challengeParameters.CODE_DELIVERY_DESTINATION,\n                    },\n                },\n            };\n        case 'SOFTWARE_TOKEN_MFA':\n            return {\n                isSignedIn: false,\n                nextStep: {\n                    signInStep: 'CONFIRM_SIGN_IN_WITH_TOTP_CODE',\n                },\n            };\n        case 'EMAIL_OTP':\n            return {\n                isSignedIn: false,\n                nextStep: {\n                    signInStep: 'CONFIRM_SIGN_IN_WITH_EMAIL_CODE',\n                    codeDeliveryDetails: {\n                        deliveryMedium: challengeParameters.CODE_DELIVERY_DELIVERY_MEDIUM,\n                        destination: challengeParameters.CODE_DELIVERY_DESTINATION,\n                    },\n                },\n            };\n        case 'WEB_AUTHN': {\n            const result = await handleWebAuthnSignInResult(challengeParameters);\n            if (isWebAuthnResultAuthSignInOutput(result)) {\n                return result;\n            }\n            return getSignInResult(result);\n        }\n        case 'PASSWORD':\n        case 'PASSWORD_SRP':\n            return {\n                isSignedIn: false,\n                nextStep: {\n                    signInStep: 'CONFIRM_SIGN_IN_WITH_PASSWORD',\n                },\n            };\n        case 'SELECT_CHALLENGE':\n            return {\n                isSignedIn: false,\n                nextStep: {\n                    signInStep: 'CONTINUE_SIGN_IN_WITH_FIRST_FACTOR_SELECTION',\n                    availableChallenges,\n                },\n            };\n    }\n    // TODO: remove this error message for production apps\n    throw new AuthError({\n        name: AuthErrorCodes.SignInException,\n        message: 'An error occurred during the sign in process. ' +\n            `${challengeName} challengeName returned by the underlying service was not addressed.`,\n    });\n}\nfunction getTOTPSetupDetails(secretCode, username) {\n    return {\n        sharedSecret: secretCode,\n        getSetupUri: (appName, accountName) => {\n            const totpUri = `otpauth://totp/${appName}:${accountName ?? username}?secret=${secretCode}&issuer=${appName}`;\n            return new AmplifyUrl(totpUri);\n        },\n    };\n}\nfunction getSignInResultFromError(errorName) {\n    if (errorName === InitiateAuthException.PasswordResetRequiredException) {\n        return {\n            isSignedIn: false,\n            nextStep: { signInStep: 'RESET_PASSWORD' },\n        };\n    }\n    else if (errorName === InitiateAuthException.UserNotConfirmedException) {\n        return {\n            isSignedIn: false,\n            nextStep: { signInStep: 'CONFIRM_SIGN_UP' },\n        };\n    }\n}\nfunction parseAttributes(attributes) {\n    if (!attributes)\n        return [];\n    const parsedAttributes = JSON.parse(attributes).map(att => att.includes(USER_ATTRIBUTES) ? att.replace(USER_ATTRIBUTES, '') : att);\n    return parsedAttributes;\n}\nfunction createAttributes(attributes) {\n    if (!attributes)\n        return {};\n    const newAttributes = {};\n    Object.entries(attributes).forEach(([key, value]) => {\n        if (value)\n            newAttributes[`${USER_ATTRIBUTES}${key}`] = value;\n    });\n    return newAttributes;\n}\nasync function handleChallengeName(username, challengeName, session, challengeResponse, config, tokenOrchestrator, clientMetadata, options) {\n    const userAttributes = options?.userAttributes;\n    const deviceName = options?.friendlyDeviceName;\n    switch (challengeName) {\n        case 'WEB_AUTHN':\n        case 'SELECT_CHALLENGE':\n            if (challengeResponse === 'PASSWORD_SRP' ||\n                challengeResponse === 'PASSWORD') {\n                return {\n                    ChallengeName: challengeResponse,\n                    Session: session,\n                    $metadata: {},\n                };\n            }\n            return initiateSelectedChallenge({\n                username,\n                session,\n                selectedChallenge: challengeResponse,\n                config,\n                clientMetadata,\n            });\n        case 'SELECT_MFA_TYPE':\n            return handleSelectMFATypeChallenge({\n                challengeResponse,\n                clientMetadata,\n                session,\n                username,\n                config,\n            });\n        case 'MFA_SETUP':\n            return handleMFASetupChallenge({\n                challengeResponse,\n                clientMetadata,\n                session,\n                username,\n                deviceName,\n                config,\n            });\n        case 'NEW_PASSWORD_REQUIRED':\n            return handleCompleteNewPasswordChallenge({\n                challengeResponse,\n                clientMetadata,\n                session,\n                username,\n                requiredAttributes: userAttributes,\n                config,\n            });\n        case 'CUSTOM_CHALLENGE':\n            return retryOnResourceNotFoundException(handleCustomChallenge, [\n                {\n                    challengeResponse,\n                    clientMetadata,\n                    session,\n                    username,\n                    config,\n                    tokenOrchestrator,\n                },\n            ], username, tokenOrchestrator);\n        case 'SMS_MFA':\n        case 'SOFTWARE_TOKEN_MFA':\n        case 'SMS_OTP':\n        case 'EMAIL_OTP':\n            return handleMFAChallenge({\n                challengeName,\n                challengeResponse,\n                clientMetadata,\n                session,\n                username,\n                config,\n            });\n        case 'PASSWORD':\n            return handleSelectChallengeWithPassword(username, challengeResponse, clientMetadata, config, session);\n        case 'PASSWORD_SRP':\n            return handleSelectChallengeWithPasswordSRP(username, challengeResponse, // This is the actual password\n            clientMetadata, config, session, tokenOrchestrator);\n    }\n    // TODO: remove this error message for production apps\n    throw new AuthError({\n        name: AuthErrorCodes.SignInException,\n        message: `An error occurred during the sign in process.\n\t\t${challengeName} challengeName returned by the underlying service was not addressed.`,\n    });\n}\nfunction mapMfaType(mfa) {\n    let mfaType = 'SMS_MFA';\n    if (mfa === 'TOTP')\n        mfaType = 'SOFTWARE_TOKEN_MFA';\n    if (mfa === 'EMAIL')\n        mfaType = 'EMAIL_OTP';\n    return mfaType;\n}\nfunction getMFAType(type) {\n    if (type === 'SMS_MFA')\n        return 'SMS';\n    if (type === 'SOFTWARE_TOKEN_MFA')\n        return 'TOTP';\n    if (type === 'EMAIL_OTP')\n        return 'EMAIL';\n    // TODO: log warning for unknown MFA type\n}\nfunction getMFATypes(types) {\n    if (!types)\n        return undefined;\n    return types.map(getMFAType).filter(Boolean);\n}\nfunction parseMFATypes(mfa) {\n    if (!mfa)\n        return [];\n    return JSON.parse(mfa);\n}\nfunction getAllowedMfaSetupTypes(availableMfaSetupTypes) {\n    return availableMfaSetupTypes.filter(authMfaType => authMfaType === 'EMAIL' || authMfaType === 'TOTP');\n}\nasync function assertUserNotAuthenticated() {\n    let authUser;\n    try {\n        authUser = await getCurrentUser();\n    }\n    catch (error) { }\n    if (authUser && authUser.userId && authUser.username) {\n        throw new AuthError({\n            name: USER_ALREADY_AUTHENTICATED_EXCEPTION,\n            message: 'There is already a signed in user.',\n            recoverySuggestion: 'Call signOut before calling signIn again.',\n        });\n    }\n}\nfunction getActiveSignInUsername(username) {\n    const state = signInStore.getState();\n    return state.username ?? username;\n}\nasync function handleMFAChallenge({ challengeName, challengeResponse, clientMetadata, session, username, config, }) {\n    const { userPoolId, userPoolClientId, userPoolEndpoint } = config;\n    const challengeResponses = {\n        USERNAME: username,\n    };\n    if (challengeName === 'EMAIL_OTP') {\n        challengeResponses.EMAIL_OTP_CODE = challengeResponse;\n    }\n    if (challengeName === 'SMS_MFA') {\n        challengeResponses.SMS_MFA_CODE = challengeResponse;\n    }\n    if (challengeName === 'SMS_OTP') {\n        challengeResponses.SMS_OTP_CODE = challengeResponse;\n    }\n    if (challengeName === 'SOFTWARE_TOKEN_MFA') {\n        challengeResponses.SOFTWARE_TOKEN_MFA_CODE = challengeResponse;\n    }\n    const userContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const jsonReq = {\n        ChallengeName: challengeName,\n        ChallengeResponses: challengeResponses,\n        Session: session,\n        ClientMetadata: clientMetadata,\n        ClientId: userPoolClientId,\n        UserContextData: userContextData,\n    };\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    return respondToAuthChallenge({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn),\n    }, jsonReq);\n}\n\nexport { assertUserNotAuthenticated, createAttributes, getActiveSignInUsername, getAllowedMfaSetupTypes, getMFAType, getMFATypes, getSignInResult, getSignInResultFromError, getTOTPSetupDetails, handleChallengeName, handleCompleteNewPasswordChallenge, handleCustomAuthFlowWithoutSRP, handleCustomChallenge, handleCustomSRPAuthFlow, handleMFAChallenge, handleMFASetupChallenge, handleSelectMFATypeChallenge, handleUserPasswordAuthFlow, handleUserSRPAuthFlow, mapMfaType, parseAttributes, parseMFATypes };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,UAAU,EAAEC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACrG,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,qBAAqB,QAAQ,qBAAqB;AAC3D,SAASC,cAAc,QAAQ,sCAAsC;AACrE,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,oCAAoC,QAAQ,+BAA+B;AACpF,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,wBAAwB,QAAQ,mGAAmG;AAC5I,OAAO,wDAAwD;AAC/D,OAAO,wHAAwH;AAC/H,OAAO,8CAA8C;AACrD,OAAO,oFAAoF;AAC3F,SAASC,kCAAkC,QAAQ,6GAA6G;AAChK,SAASC,+BAA+B,QAAQ,0GAA0G;AAC1J,SAASC,kCAAkC,QAAQ,6GAA6G;AAChK,SAASC,qCAAqC,QAAQ,wDAAwD;AAC9G,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,0BAA0B,QAAQ,+DAA+D;AAC1G,SAASC,iBAAiB,QAAQ,oDAAoD;AACtF,SAASC,yBAAyB,QAAQ,0DAA0D;AACpG,SAASC,iCAAiC,QAAQ,sEAAsE;AACxH,SAASC,oCAAoC,QAAQ,yEAAyE;AAC9H,OAAO,iDAAiD;AACxD,SAASC,WAAW,QAAQ,6CAA6C;AACzE,SAASC,uBAAuB,QAAQ,mCAAmC;AAC3E,OAAO,qBAAqB;AAC5B,OAAO,uBAAuB;AAC9B,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,+BAA+B,QAAQ,uCAAuC;AACvF,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,gCAAgC,QAAQ,wCAAwC;AACzF,SAASC,uBAAuB,QAAQ,+BAA+B;;AAEvE;AACA;AACA,MAAMC,eAAe,GAAG,iBAAiB;AACzC,SAASC,gCAAgCA,CAACC,MAAM,EAAE;EAC9C,OAAO,YAAY,IAAIA,MAAM,IAAI,UAAU,IAAIA,MAAM;AACzD;AAAC,SACcC,qBAAqBA,CAAAC,EAAA;EAAA,OAAAC,sBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,uBAAA;EAAAA,sBAAA,GAAAG,iBAAA,CAApC,WAAqC;IAAEC,iBAAiB;IAAEC,cAAc;IAAEC,OAAO;IAAEC,QAAQ;IAAEC,MAAM;IAAEC;EAAmB,CAAC,EAAE;IACvH,MAAM;MAAEC,UAAU;MAAEC,gBAAgB;MAAEC;IAAiB,CAAC,GAAGJ,MAAM;IACjE,MAAMK,kBAAkB,GAAG;MACvBC,QAAQ,EAAEP,QAAQ;MAClBQ,MAAM,EAAEX;IACZ,CAAC;IACD,MAAMY,cAAc,SAASP,iBAAiB,EAAEQ,iBAAiB,CAACV,QAAQ,CAAC;IAC3E,IAAIS,cAAc,IAAIA,cAAc,CAACE,SAAS,EAAE;MAC5CL,kBAAkB,CAACM,UAAU,GAAGH,cAAc,CAACE,SAAS;IAC5D;IACA,MAAME,eAAe,GAAG9B,kBAAkB,CAAC;MACvCiB,QAAQ;MACRG,UAAU;MACVC;IACJ,CAAC,CAAC;IACF,MAAMU,OAAO,GAAG;MACZC,aAAa,EAAE,kBAAkB;MACjCC,kBAAkB,EAAEV,kBAAkB;MACtCW,OAAO,EAAElB,OAAO;MAChBmB,cAAc,EAAEpB,cAAc;MAC9BqB,QAAQ,EAAEf,gBAAgB;MAC1BS;IACJ,CAAC;IACD,MAAMO,sBAAsB,GAAGjD,kCAAkC,CAAC;MAC9DkD,gBAAgB,EAAE/C,qCAAqC,CAAC;QACpDgD,gBAAgB,EAAEjB;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMkB,QAAQ,SAASH,sBAAsB,CAAC;MAC1CI,MAAM,EAAEjD,uBAAuB,CAAC4B,UAAU,CAAC;MAC3CsB,cAAc,EAAExD,qBAAqB,CAACV,UAAU,CAACmE,aAAa;IAClE,CAAC,EAAEZ,OAAO,CAAC;IACX,IAAIS,QAAQ,CAACR,aAAa,KAAK,iBAAiB,EAAE;MAC9C,OAAO9B,mBAAmB,CAAC;QACvBe,QAAQ;QACRC,MAAM;QACNH,cAAc;QACdC,OAAO,EAAEwB,QAAQ,CAACN,OAAO;QACzBf;MACJ,CAAC,CAAC;IACN;IACA,OAAOqB,QAAQ;EACnB,CAAC;EAAA,OAAA9B,sBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SACcgC,uBAAuBA,CAAAC,GAAA;EAAA,OAAAC,wBAAA,CAAAnC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAkC,yBAAA;EAAAA,wBAAA,GAAAjC,iBAAA,CAAtC,WAAuC;IAAEC,iBAAiB;IAAEG,QAAQ;IAAEF,cAAc;IAAEC,OAAO;IAAE+B,UAAU;IAAE7B;EAAQ,CAAC,EAAE;IAClH,MAAM;MAAEE,UAAU;MAAEC,gBAAgB;MAAEC;IAAiB,CAAC,GAAGJ,MAAM;IACjE,IAAIJ,iBAAiB,KAAK,OAAO,EAAE;MAC/B,OAAO;QACHkB,aAAa,EAAE,WAAW;QAC1BE,OAAO,EAAElB,OAAO;QAChBgC,mBAAmB,EAAE;UACjBC,cAAc,EAAE;QACpB,CAAC;QACDC,SAAS,EAAE,CAAC;MAChB,CAAC;IACL;IACA,IAAIpC,iBAAiB,KAAK,MAAM,EAAE;MAC9B,OAAO;QACHkB,aAAa,EAAE,WAAW;QAC1BE,OAAO,EAAElB,OAAO;QAChBgC,mBAAmB,EAAE;UACjBC,cAAc,EAAE;QACpB,CAAC;QACDC,SAAS,EAAE,CAAC;MAChB,CAAC;IACL;IACA,MAAM3B,kBAAkB,GAAG;MACvBC,QAAQ,EAAEP;IACd,CAAC;IACD,MAAMkC,UAAU,GAAG,OAAO,CAACC,IAAI,CAACtC,iBAAiB,CAAC;IAClD,IAAIqC,UAAU,EAAE;MACZ,MAAME,mBAAmB,GAAGhE,+BAA+B,CAAC;QACxDiD,gBAAgB,EAAE/C,qCAAqC,CAAC;UACpDgD,gBAAgB,EAAEjB;QACtB,CAAC;MACL,CAAC,CAAC;MACF,MAAM;QAAEY;MAAQ,CAAC,SAASmB,mBAAmB,CAAC;QAC1CZ,MAAM,EAAEjD,uBAAuB,CAAC4B,UAAU,CAAC;QAC3CsB,cAAc,EAAExD,qBAAqB,CAACV,UAAU,CAACmE,aAAa;MAClE,CAAC,EAAE;QACCW,QAAQ,EAAExC,iBAAiB;QAC3BoB,OAAO,EAAElB,OAAO;QAChBuC,kBAAkB,EAAER;MACxB,CAAC,CAAC;MACFjD,WAAW,CAAC0D,QAAQ,CAAC;QACjBC,IAAI,EAAE,qBAAqB;QAC3BC,KAAK,EAAExB;MACX,CAAC,CAAC;MACF,MAAMH,OAAO,GAAG;QACZC,aAAa,EAAE,WAAW;QAC1BC,kBAAkB,EAAEV,kBAAkB;QACtCW,OAAO;QACPC,cAAc,EAAEpB,cAAc;QAC9BqB,QAAQ,EAAEf;MACd,CAAC;MACD,MAAMgB,sBAAsB,GAAGjD,kCAAkC,CAAC;QAC9DkD,gBAAgB,EAAE/C,qCAAqC,CAAC;UACpDgD,gBAAgB,EAAEjB;QACtB,CAAC;MACL,CAAC,CAAC;MACF,OAAOe,sBAAsB,CAAC;QAC1BI,MAAM,EAAEjD,uBAAuB,CAAC4B,UAAU,CAAC;QAC3CsB,cAAc,EAAExD,qBAAqB,CAACV,UAAU,CAACmE,aAAa;MAClE,CAAC,EAAEZ,OAAO,CAAC;IACf;IACA,MAAM4B,OAAO,GAAG7C,iBAAiB,CAAC8C,QAAQ,CAAC,GAAG,CAAC;IAC/C,IAAID,OAAO,EAAE;MACTpC,kBAAkB,CAACsC,KAAK,GAAG/C,iBAAiB;MAC5C,MAAMiB,OAAO,GAAG;QACZC,aAAa,EAAE,WAAW;QAC1BC,kBAAkB,EAAEV,kBAAkB;QACtCW,OAAO,EAAElB,OAAO;QAChBmB,cAAc,EAAEpB,cAAc;QAC9BqB,QAAQ,EAAEf;MACd,CAAC;MACD,MAAMgB,sBAAsB,GAAGjD,kCAAkC,CAAC;QAC9DkD,gBAAgB,EAAE/C,qCAAqC,CAAC;UACpDgD,gBAAgB,EAAEjB;QACtB,CAAC;MACL,CAAC,CAAC;MACF,OAAOe,sBAAsB,CAAC;QAC1BI,MAAM,EAAEjD,uBAAuB,CAAC4B,UAAU,CAAC;QAC3CsB,cAAc,EAAExD,qBAAqB,CAACV,UAAU,CAACmE,aAAa;MAClE,CAAC,EAAEZ,OAAO,CAAC;IACf;IACA,MAAM,IAAIpD,SAAS,CAAC;MAChBmF,IAAI,EAAEjF,cAAc,CAACkF,eAAe;MACpCC,OAAO,EAAE,0DAA0DlD,iBAAiB,EAAE;MACtFmD,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN,CAAC;EAAA,OAAAnB,wBAAA,CAAAnC,KAAA,OAAAC,SAAA;AAAA;AAAA,SACcsD,4BAA4BA,CAAAC,GAAA;EAAA,OAAAC,6BAAA,CAAAzD,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAwD,8BAAA;EAAAA,6BAAA,GAAAvD,iBAAA,CAA3C,WAA4C;IAAEC,iBAAiB;IAAEG,QAAQ;IAAEF,cAAc;IAAEC,OAAO;IAAEE;EAAQ,CAAC,EAAE;IAC3G,MAAM;MAAEE,UAAU;MAAEC,gBAAgB;MAAEC;IAAiB,CAAC,GAAGJ,MAAM;IACjEnC,qBAAqB,CAAC+B,iBAAiB,KAAK,MAAM,IAC9CA,iBAAiB,KAAK,KAAK,IAC3BA,iBAAiB,KAAK,OAAO,EAAEhC,uBAAuB,CAACuF,kBAAkB,CAAC;IAC9E,MAAM9C,kBAAkB,GAAG;MACvBC,QAAQ,EAAEP,QAAQ;MAClBQ,MAAM,EAAE6C,UAAU,CAACxD,iBAAiB;IACxC,CAAC;IACD,MAAMgB,eAAe,GAAG9B,kBAAkB,CAAC;MACvCiB,QAAQ;MACRG,UAAU;MACVC;IACJ,CAAC,CAAC;IACF,MAAMU,OAAO,GAAG;MACZC,aAAa,EAAE,iBAAiB;MAChCC,kBAAkB,EAAEV,kBAAkB;MACtCW,OAAO,EAAElB,OAAO;MAChBmB,cAAc,EAAEpB,cAAc;MAC9BqB,QAAQ,EAAEf,gBAAgB;MAC1BS;IACJ,CAAC;IACD,MAAMO,sBAAsB,GAAGjD,kCAAkC,CAAC;MAC9DkD,gBAAgB,EAAE/C,qCAAqC,CAAC;QACpDgD,gBAAgB,EAAEjB;MACtB,CAAC;IACL,CAAC,CAAC;IACF,OAAOe,sBAAsB,CAAC;MAC1BI,MAAM,EAAEjD,uBAAuB,CAAC4B,UAAU,CAAC;MAC3CsB,cAAc,EAAExD,qBAAqB,CAACV,UAAU,CAACmE,aAAa;IAClE,CAAC,EAAEZ,OAAO,CAAC;EACf,CAAC;EAAA,OAAAqC,6BAAA,CAAAzD,KAAA,OAAAC,SAAA;AAAA;AAAA,SACc2D,kCAAkCA,CAAAC,GAAA;EAAA,OAAAC,mCAAA,CAAA9D,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA6D,oCAAA;EAAAA,mCAAA,GAAA5D,iBAAA,CAAjD,WAAkD;IAAEC,iBAAiB;IAAEC,cAAc;IAAEC,OAAO;IAAEC,QAAQ;IAAEyD,kBAAkB;IAAExD;EAAQ,CAAC,EAAE;IACrI,MAAM;MAAEE,UAAU;MAAEC,gBAAgB;MAAEC;IAAiB,CAAC,GAAGJ,MAAM;IACjE,MAAMK,kBAAkB,GAAG;MACvB,GAAGoD,gBAAgB,CAACD,kBAAkB,CAAC;MACvCE,YAAY,EAAE9D,iBAAiB;MAC/BU,QAAQ,EAAEP;IACd,CAAC;IACD,MAAMa,eAAe,GAAG9B,kBAAkB,CAAC;MACvCiB,QAAQ;MACRG,UAAU;MACVC;IACJ,CAAC,CAAC;IACF,MAAMU,OAAO,GAAG;MACZC,aAAa,EAAE,uBAAuB;MACtCC,kBAAkB,EAAEV,kBAAkB;MACtCY,cAAc,EAAEpB,cAAc;MAC9BmB,OAAO,EAAElB,OAAO;MAChBoB,QAAQ,EAAEf,gBAAgB;MAC1BS;IACJ,CAAC;IACD,MAAMO,sBAAsB,GAAGjD,kCAAkC,CAAC;MAC9DkD,gBAAgB,EAAE/C,qCAAqC,CAAC;QACpDgD,gBAAgB,EAAEjB;MACtB,CAAC;IACL,CAAC,CAAC;IACF,OAAOe,sBAAsB,CAAC;MAC1BI,MAAM,EAAEjD,uBAAuB,CAAC4B,UAAU,CAAC;MAC3CsB,cAAc,EAAExD,qBAAqB,CAACV,UAAU,CAACmE,aAAa;IAClE,CAAC,EAAEZ,OAAO,CAAC;EACf,CAAC;EAAA,OAAA0C,mCAAA,CAAA9D,KAAA,OAAAC,SAAA;AAAA;AAAA,SACciE,0BAA0BA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,2BAAA,CAAAxE,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAuE,4BAAA;EAAAA,2BAAA,GAAAtE,iBAAA,CAAzC,WAA0CI,QAAQ,EAAEmE,QAAQ,EAAErE,cAAc,EAAEG,MAAM,EAAEC,iBAAiB,EAAE;IACrG,MAAM;MAAEE,gBAAgB;MAAED,UAAU;MAAEE;IAAiB,CAAC,GAAGJ,MAAM;IACjE,MAAMmE,cAAc,GAAG;MACnB7D,QAAQ,EAAEP,QAAQ;MAClBqE,QAAQ,EAAEF;IACd,CAAC;IACD,MAAM1D,cAAc,SAASP,iBAAiB,CAACQ,iBAAiB,CAACV,QAAQ,CAAC;IAC1E,IAAIS,cAAc,IAAIA,cAAc,CAACE,SAAS,EAAE;MAC5CyD,cAAc,CAACxD,UAAU,GAAGH,cAAc,CAACE,SAAS;IACxD;IACA,MAAME,eAAe,GAAG9B,kBAAkB,CAAC;MACvCiB,QAAQ;MACRG,UAAU;MACVC;IACJ,CAAC,CAAC;IACF,MAAMU,OAAO,GAAG;MACZwD,QAAQ,EAAE,oBAAoB;MAC9BC,cAAc,EAAEH,cAAc;MAC9BlD,cAAc,EAAEpB,cAAc;MAC9BqB,QAAQ,EAAEf,gBAAgB;MAC1BS;IACJ,CAAC;IACD,MAAM2D,YAAY,GAAGtG,wBAAwB,CAAC;MAC1CmD,gBAAgB,EAAE/C,qCAAqC,CAAC;QACpDgD,gBAAgB,EAAEjB;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMkB,QAAQ,SAASiD,YAAY,CAAC;MAChChD,MAAM,EAAEjD,uBAAuB,CAAC4B,UAAU,CAAC;MAC3CsB,cAAc,EAAExD,qBAAqB,CAACV,UAAU,CAACkH,MAAM;IAC3D,CAAC,EAAE3D,OAAO,CAAC;IACX,MAAM4D,cAAc,GAAGnD,QAAQ,CAACQ,mBAAmB,EAAExB,QAAQ,IACzDgB,QAAQ,CAACQ,mBAAmB,EAAE4C,eAAe,IAC7C3E,QAAQ;IACZb,uBAAuB,CAACuF,cAAc,CAAC;IACvC,IAAInD,QAAQ,CAACR,aAAa,KAAK,iBAAiB,EAC5C,OAAO9B,mBAAmB,CAAC;MACvBe,QAAQ,EAAE0E,cAAc;MACxBzE,MAAM;MACNH,cAAc;MACdC,OAAO,EAAEwB,QAAQ,CAACN,OAAO;MACzBf;IACJ,CAAC,CAAC;IACN,OAAOqB,QAAQ;EACnB,CAAC;EAAA,OAAA2C,2BAAA,CAAAxE,KAAA,OAAAC,SAAA;AAAA;AAAA,SACciF,qBAAqBA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,sBAAA,CAAAxF,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAuF,uBAAA;EAAAA,sBAAA,GAAAtF,iBAAA,CAApC,WAAqCI,QAAQ,EAAEmE,QAAQ,EAAErE,cAAc,EAAEG,MAAM,EAAEC,iBAAiB,EAAE;IAChG,OAAOzB,iBAAiB,CAAC;MACrBuB,QAAQ;MACRmE,QAAQ;MACRrE,cAAc;MACdG,MAAM;MACNC,iBAAiB;MACjBiF,QAAQ,EAAE;IACd,CAAC,CAAC;EACN,CAAC;EAAA,OAAAD,sBAAA,CAAAxF,KAAA,OAAAC,SAAA;AAAA;AAAA,SACcyF,8BAA8BA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,+BAAA,CAAA/F,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA8F,gCAAA;EAAAA,+BAAA,GAAA7F,iBAAA,CAA7C,WAA8CI,QAAQ,EAAEF,cAAc,EAAEG,MAAM,EAAEC,iBAAiB,EAAE;IAC/F,MAAM;MAAEE,gBAAgB;MAAED,UAAU;MAAEE;IAAiB,CAAC,GAAGJ,MAAM;IACjE,MAAMmE,cAAc,GAAG;MACnB7D,QAAQ,EAAEP;IACd,CAAC;IACD,MAAMS,cAAc,SAASP,iBAAiB,CAACQ,iBAAiB,CAACV,QAAQ,CAAC;IAC1E,IAAIS,cAAc,IAAIA,cAAc,CAACE,SAAS,EAAE;MAC5CyD,cAAc,CAACxD,UAAU,GAAGH,cAAc,CAACE,SAAS;IACxD;IACA,MAAME,eAAe,GAAG9B,kBAAkB,CAAC;MACvCiB,QAAQ;MACRG,UAAU;MACVC;IACJ,CAAC,CAAC;IACF,MAAMU,OAAO,GAAG;MACZwD,QAAQ,EAAE,aAAa;MACvBC,cAAc,EAAEH,cAAc;MAC9BlD,cAAc,EAAEpB,cAAc;MAC9BqB,QAAQ,EAAEf,gBAAgB;MAC1BS;IACJ,CAAC;IACD,MAAM2D,YAAY,GAAGtG,wBAAwB,CAAC;MAC1CmD,gBAAgB,EAAE/C,qCAAqC,CAAC;QACpDgD,gBAAgB,EAAEjB;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMkB,QAAQ,SAASiD,YAAY,CAAC;MAChChD,MAAM,EAAEjD,uBAAuB,CAAC4B,UAAU,CAAC;MAC3CsB,cAAc,EAAExD,qBAAqB,CAACV,UAAU,CAACkH,MAAM;IAC3D,CAAC,EAAE3D,OAAO,CAAC;IACX,MAAM4D,cAAc,GAAGnD,QAAQ,CAACQ,mBAAmB,EAAExB,QAAQ,IAAIP,QAAQ;IACzEb,uBAAuB,CAACuF,cAAc,CAAC;IACvC,IAAInD,QAAQ,CAACR,aAAa,KAAK,iBAAiB,EAC5C,OAAO9B,mBAAmB,CAAC;MACvBe,QAAQ,EAAE0E,cAAc;MACxBzE,MAAM;MACNH,cAAc;MACdC,OAAO,EAAEwB,QAAQ,CAACN,OAAO;MACzBf;IACJ,CAAC,CAAC;IACN,OAAOqB,QAAQ;EACnB,CAAC;EAAA,OAAAkE,+BAAA,CAAA/F,KAAA,OAAAC,SAAA;AAAA;AAAA,SACc+F,uBAAuBA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,wBAAA,CAAAtG,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAqG,yBAAA;EAAAA,wBAAA,GAAApG,iBAAA,CAAtC,WAAuCI,QAAQ,EAAEmE,QAAQ,EAAErE,cAAc,EAAEG,MAAM,EAAEC,iBAAiB,EAAE;IAClG1C,yBAAyB,CAACyC,MAAM,CAAC;IACjC,MAAM;MAAEE,UAAU;MAAEC,gBAAgB;MAAEC;IAAiB,CAAC,GAAGJ,MAAM;IACjE,MAAMgG,YAAY,GAAG9F,UAAU,EAAE+F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;IACpD,MAAMC,oBAAoB,SAASrH,uBAAuB,CAACmH,YAAY,CAAC;IACxE,MAAM7B,cAAc,GAAG;MACnB7D,QAAQ,EAAEP,QAAQ;MAClBoG,KAAK,EAAED,oBAAoB,CAACE,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;MAC1CC,cAAc,EAAE;IACpB,CAAC;IACD,MAAM1F,eAAe,GAAG9B,kBAAkB,CAAC;MACvCiB,QAAQ;MACRG,UAAU;MACVC;IACJ,CAAC,CAAC;IACF,MAAMU,OAAO,GAAG;MACZwD,QAAQ,EAAE,aAAa;MACvBC,cAAc,EAAEH,cAAc;MAC9BlD,cAAc,EAAEpB,cAAc;MAC9BqB,QAAQ,EAAEf,gBAAgB;MAC1BS;IACJ,CAAC;IACD,MAAM2D,YAAY,GAAGtG,wBAAwB,CAAC;MAC1CmD,gBAAgB,EAAE/C,qCAAqC,CAAC;QACpDgD,gBAAgB,EAAEjB;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAM;MAAE0B,mBAAmB,EAAEyE,mBAAmB;MAAEvF,OAAO,EAAElB;IAAQ,CAAC,SAASyE,YAAY,CAAC;MACtFhD,MAAM,EAAEjD,uBAAuB,CAAC4B,UAAU,CAAC;MAC3CsB,cAAc,EAAExD,qBAAqB,CAACV,UAAU,CAACkH,MAAM;IAC3D,CAAC,EAAE3D,OAAO,CAAC;IACX,MAAM4D,cAAc,GAAG8B,mBAAmB,EAAEjG,QAAQ,IAAIP,QAAQ;IAChEb,uBAAuB,CAACuF,cAAc,CAAC;IACvC,OAAOxF,gCAAgC,CAACF,+BAA+B,EAAE,CACrEmF,QAAQ,EACRqC,mBAAmB,EACnB1G,cAAc,EACdC,OAAO,EACPoG,oBAAoB,EACpBlG,MAAM,EACNC,iBAAiB,CACpB,EAAEwE,cAAc,EAAExE,iBAAiB,CAAC;EACzC,CAAC;EAAA,OAAA8F,wBAAA,CAAAtG,KAAA,OAAAC,SAAA;AAAA;AAAA,SACc8G,eAAeA,CAAAC,IAAA;EAAA,OAAAC,gBAAA,CAAAjH,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAgH,iBAAA;EAAAA,gBAAA,GAAA/G,iBAAA,CAA9B,WAA+BgH,MAAM,EAAE;IACnC,MAAM;MAAEC,aAAa;MAAEL,mBAAmB;MAAEM;IAAoB,CAAC,GAAGF,MAAM;IAC1E,MAAMG,UAAU,GAAGzJ,OAAO,CAAC0J,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpD1J,yBAAyB,CAACuJ,UAAU,CAAC;IACrC,QAAQF,aAAa;MACjB,KAAK,kBAAkB;QACnB,OAAO;UACHM,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE;YACNC,UAAU,EAAE,uCAAuC;YACnDC,cAAc,EAAEd;UACpB;QACJ,CAAC;MACL,KAAK,WAAW;QAAE;UACd,MAAM;YAAEe,aAAa;YAAEvH;UAAS,CAAC,GAAGnB,WAAW,CAAC2I,QAAQ,CAAC,CAAC;UAC1D,MAAMC,aAAa,GAAGC,WAAW,CAACC,aAAa,CAACnB,mBAAmB,CAACxE,cAAc,CAAC,CAAC,IAAI,EAAE;UAC1F,MAAM4F,oBAAoB,GAAGC,uBAAuB,CAACJ,aAAa,CAAC;UACnE,MAAMK,uBAAuB,GAAGF,oBAAoB,CAACjF,QAAQ,CAAC,MAAM,CAAC;UACrE,MAAMoF,wBAAwB,GAAGH,oBAAoB,CAACjF,QAAQ,CAAC,OAAO,CAAC;UACvE,IAAImF,uBAAuB,IAAIC,wBAAwB,EAAE;YACrD,OAAO;cACHZ,UAAU,EAAE,KAAK;cACjBC,QAAQ,EAAE;gBACNC,UAAU,EAAE,2CAA2C;gBACvDW,eAAe,EAAEJ;cACrB;YACJ,CAAC;UACL;UACA,IAAIG,wBAAwB,EAAE;YAC1B,OAAO;cACHZ,UAAU,EAAE,KAAK;cACjBC,QAAQ,EAAE;gBACNC,UAAU,EAAE;cAChB;YACJ,CAAC;UACL;UACA,IAAIS,uBAAuB,EAAE;YACzB,MAAMG,sBAAsB,GAAG5J,kCAAkC,CAAC;cAC9DgD,gBAAgB,EAAE/C,qCAAqC,CAAC;gBACpDgD,gBAAgB,EAAEyF,UAAU,CAAC1G;cACjC,CAAC;YACL,CAAC,CAAC;YACF,MAAM;cAAEY,OAAO;cAAEiH,UAAU,EAAEC;YAAW,CAAC,SAASF,sBAAsB,CAAC;cAAEzG,MAAM,EAAEjD,uBAAuB,CAACwI,UAAU,CAAC5G,UAAU;YAAE,CAAC,EAAE;cACjIc,OAAO,EAAEsG;YACb,CAAC,CAAC;YACF1I,WAAW,CAAC0D,QAAQ,CAAC;cACjBC,IAAI,EAAE,qBAAqB;cAC3BC,KAAK,EAAExB;YACX,CAAC,CAAC;YACF,OAAO;cACHkG,UAAU,EAAE,KAAK;cACjBC,QAAQ,EAAE;gBACNC,UAAU,EAAE,kCAAkC;gBAC9Ce,gBAAgB,EAAEC,mBAAmB,CAACF,UAAU,EAAEnI,QAAQ;cAC9D;YACJ,CAAC;UACL;UACA,MAAM,IAAItC,SAAS,CAAC;YAChBmF,IAAI,EAAEjF,cAAc,CAACkF,eAAe;YACpCC,OAAO,EAAE,mDAAmD0E,aAAa;UAC7E,CAAC,CAAC;QACN;MACA,KAAK,uBAAuB;QACxB,OAAO;UACHN,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE;YACNC,UAAU,EAAE,4CAA4C;YACxDiB,iBAAiB,EAAEC,eAAe,CAAC/B,mBAAmB,CAAC/C,kBAAkB;UAC7E;QACJ,CAAC;MACL,KAAK,iBAAiB;QAClB,OAAO;UACH0D,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE;YACNC,UAAU,EAAE,qCAAqC;YACjDW,eAAe,EAAEN,WAAW,CAACC,aAAa,CAACnB,mBAAmB,CAACgC,eAAe,CAAC;UACnF;QACJ,CAAC;MACL,KAAK,SAAS;MACd,KAAK,SAAS;QACV,OAAO;UACHrB,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE;YACNC,UAAU,EAAE,+BAA+B;YAC3CoB,mBAAmB,EAAE;cACjBC,cAAc,EAAElC,mBAAmB,CAACmC,6BAA6B;cACjEC,WAAW,EAAEpC,mBAAmB,CAACqC;YACrC;UACJ;QACJ,CAAC;MACL,KAAK,oBAAoB;QACrB,OAAO;UACH1B,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE;YACNC,UAAU,EAAE;UAChB;QACJ,CAAC;MACL,KAAK,WAAW;QACZ,OAAO;UACHF,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE;YACNC,UAAU,EAAE,iCAAiC;YAC7CoB,mBAAmB,EAAE;cACjBC,cAAc,EAAElC,mBAAmB,CAACmC,6BAA6B;cACjEC,WAAW,EAAEpC,mBAAmB,CAACqC;YACrC;UACJ;QACJ,CAAC;MACL,KAAK,WAAW;QAAE;UACd,MAAMvJ,MAAM,SAASd,0BAA0B,CAACgI,mBAAmB,CAAC;UACpE,IAAInH,gCAAgC,CAACC,MAAM,CAAC,EAAE;YAC1C,OAAOA,MAAM;UACjB;UACA,OAAOmH,eAAe,CAACnH,MAAM,CAAC;QAClC;MACA,KAAK,UAAU;MACf,KAAK,cAAc;QACf,OAAO;UACH6H,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE;YACNC,UAAU,EAAE;UAChB;QACJ,CAAC;MACL,KAAK,kBAAkB;QACnB,OAAO;UACHF,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE;YACNC,UAAU,EAAE,8CAA8C;YAC1DP;UACJ;QACJ,CAAC;IACT;IACA;IACA,MAAM,IAAIpJ,SAAS,CAAC;MAChBmF,IAAI,EAAEjF,cAAc,CAACkF,eAAe;MACpCC,OAAO,EAAE,gDAAgD,GACrD,GAAG8D,aAAa;IACxB,CAAC,CAAC;EACN,CAAC;EAAA,OAAAF,gBAAA,CAAAjH,KAAA,OAAAC,SAAA;AAAA;AACD,SAAS0I,mBAAmBA,CAACF,UAAU,EAAEnI,QAAQ,EAAE;EAC/C,OAAO;IACH8I,YAAY,EAAEX,UAAU;IACxBY,WAAW,EAAEA,CAACC,OAAO,EAAEC,WAAW,KAAK;MACnC,MAAMC,OAAO,GAAG,kBAAkBF,OAAO,IAAIC,WAAW,IAAIjJ,QAAQ,WAAWmI,UAAU,WAAWa,OAAO,EAAE;MAC7G,OAAO,IAAIvL,UAAU,CAACyL,OAAO,CAAC;IAClC;EACJ,CAAC;AACL;AACA,SAASC,wBAAwBA,CAACC,SAAS,EAAE;EACzC,IAAIA,SAAS,KAAKzL,qBAAqB,CAAC0L,8BAA8B,EAAE;IACpE,OAAO;MACHlC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE;QAAEC,UAAU,EAAE;MAAiB;IAC7C,CAAC;EACL,CAAC,MACI,IAAI+B,SAAS,KAAKzL,qBAAqB,CAAC2L,yBAAyB,EAAE;IACpE,OAAO;MACHnC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE;QAAEC,UAAU,EAAE;MAAkB;IAC9C,CAAC;EACL;AACJ;AACA,SAASkB,eAAeA,CAACgB,UAAU,EAAE;EACjC,IAAI,CAACA,UAAU,EACX,OAAO,EAAE;EACb,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACH,UAAU,CAAC,CAACI,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACjH,QAAQ,CAACvD,eAAe,CAAC,GAAGwK,GAAG,CAACC,OAAO,CAACzK,eAAe,EAAE,EAAE,CAAC,GAAGwK,GAAG,CAAC;EAClI,OAAOJ,gBAAgB;AAC3B;AACA,SAAS9F,gBAAgBA,CAAC6F,UAAU,EAAE;EAClC,IAAI,CAACA,UAAU,EACX,OAAO,CAAC,CAAC;EACb,MAAMO,aAAa,GAAG,CAAC,CAAC;EACxBC,MAAM,CAACC,OAAO,CAACT,UAAU,CAAC,CAACU,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEzH,KAAK,CAAC,KAAK;IACjD,IAAIA,KAAK,EACLqH,aAAa,CAAC,GAAG1K,eAAe,GAAG8K,GAAG,EAAE,CAAC,GAAGzH,KAAK;EACzD,CAAC,CAAC;EACF,OAAOqH,aAAa;AACxB;AAAC,SACcK,mBAAmBA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,oBAAA,CAAAlL,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAiL,qBAAA;EAAAA,oBAAA,GAAAhL,iBAAA,CAAlC,WAAmCI,QAAQ,EAAE6G,aAAa,EAAE9G,OAAO,EAAEF,iBAAiB,EAAEI,MAAM,EAAEC,iBAAiB,EAAEJ,cAAc,EAAE+K,OAAO,EAAE;IACxI,MAAMC,cAAc,GAAGD,OAAO,EAAEC,cAAc;IAC9C,MAAMhJ,UAAU,GAAG+I,OAAO,EAAEE,kBAAkB;IAC9C,QAAQlE,aAAa;MACjB,KAAK,WAAW;MAChB,KAAK,kBAAkB;QACnB,IAAIhH,iBAAiB,KAAK,cAAc,IACpCA,iBAAiB,KAAK,UAAU,EAAE;UAClC,OAAO;YACHkB,aAAa,EAAElB,iBAAiB;YAChCoB,OAAO,EAAElB,OAAO;YAChBkC,SAAS,EAAE,CAAC;UAChB,CAAC;QACL;QACA,OAAOvD,yBAAyB,CAAC;UAC7BsB,QAAQ;UACRD,OAAO;UACPiL,iBAAiB,EAAEnL,iBAAiB;UACpCI,MAAM;UACNH;QACJ,CAAC,CAAC;MACN,KAAK,iBAAiB;QAClB,OAAOmD,4BAA4B,CAAC;UAChCpD,iBAAiB;UACjBC,cAAc;UACdC,OAAO;UACPC,QAAQ;UACRC;QACJ,CAAC,CAAC;MACN,KAAK,WAAW;QACZ,OAAO0B,uBAAuB,CAAC;UAC3B9B,iBAAiB;UACjBC,cAAc;UACdC,OAAO;UACPC,QAAQ;UACR8B,UAAU;UACV7B;QACJ,CAAC,CAAC;MACN,KAAK,uBAAuB;QACxB,OAAOqD,kCAAkC,CAAC;UACtCzD,iBAAiB;UACjBC,cAAc;UACdC,OAAO;UACPC,QAAQ;UACRyD,kBAAkB,EAAEqH,cAAc;UAClC7K;QACJ,CAAC,CAAC;MACN,KAAK,kBAAkB;QACnB,OAAOf,gCAAgC,CAACK,qBAAqB,EAAE,CAC3D;UACIM,iBAAiB;UACjBC,cAAc;UACdC,OAAO;UACPC,QAAQ;UACRC,MAAM;UACNC;QACJ,CAAC,CACJ,EAAEF,QAAQ,EAAEE,iBAAiB,CAAC;MACnC,KAAK,SAAS;MACd,KAAK,oBAAoB;MACzB,KAAK,SAAS;MACd,KAAK,WAAW;QACZ,OAAO+K,kBAAkB,CAAC;UACtBpE,aAAa;UACbhH,iBAAiB;UACjBC,cAAc;UACdC,OAAO;UACPC,QAAQ;UACRC;QACJ,CAAC,CAAC;MACN,KAAK,UAAU;QACX,OAAOtB,iCAAiC,CAACqB,QAAQ,EAAEH,iBAAiB,EAAEC,cAAc,EAAEG,MAAM,EAAEF,OAAO,CAAC;MAC1G,KAAK,cAAc;QACf,OAAOnB,oCAAoC,CAACoB,QAAQ,EAAEH,iBAAiB;QAAE;QACzEC,cAAc,EAAEG,MAAM,EAAEF,OAAO,EAAEG,iBAAiB,CAAC;IAC3D;IACA;IACA,MAAM,IAAIxC,SAAS,CAAC;MAChBmF,IAAI,EAAEjF,cAAc,CAACkF,eAAe;MACpCC,OAAO,EAAE;AACjB,IAAI8D,aAAa;IACb,CAAC,CAAC;EACN,CAAC;EAAA,OAAA+D,oBAAA,CAAAlL,KAAA,OAAAC,SAAA;AAAA;AACD,SAAS0D,UAAUA,CAAC6H,GAAG,EAAE;EACrB,IAAIC,OAAO,GAAG,SAAS;EACvB,IAAID,GAAG,KAAK,MAAM,EACdC,OAAO,GAAG,oBAAoB;EAClC,IAAID,GAAG,KAAK,OAAO,EACfC,OAAO,GAAG,WAAW;EACzB,OAAOA,OAAO;AAClB;AACA,SAASC,UAAUA,CAAC5I,IAAI,EAAE;EACtB,IAAIA,IAAI,KAAK,SAAS,EAClB,OAAO,KAAK;EAChB,IAAIA,IAAI,KAAK,oBAAoB,EAC7B,OAAO,MAAM;EACjB,IAAIA,IAAI,KAAK,WAAW,EACpB,OAAO,OAAO;EAClB;AACJ;AACA,SAASkF,WAAWA,CAAC2D,KAAK,EAAE;EACxB,IAAI,CAACA,KAAK,EACN,OAAOC,SAAS;EACpB,OAAOD,KAAK,CAAC1B,GAAG,CAACyB,UAAU,CAAC,CAACG,MAAM,CAACC,OAAO,CAAC;AAChD;AACA,SAAS7D,aAAaA,CAACuD,GAAG,EAAE;EACxB,IAAI,CAACA,GAAG,EACJ,OAAO,EAAE;EACb,OAAOzB,IAAI,CAACC,KAAK,CAACwB,GAAG,CAAC;AAC1B;AACA,SAASrD,uBAAuBA,CAAC4D,sBAAsB,EAAE;EACrD,OAAOA,sBAAsB,CAACF,MAAM,CAACG,WAAW,IAAIA,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,MAAM,CAAC;AAC1G;AAAC,SACcC,0BAA0BA,CAAA;EAAA,OAAAC,2BAAA,CAAAlM,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAiM,4BAAA;EAAAA,2BAAA,GAAAhM,iBAAA,CAAzC,aAA4C;IACxC,IAAIiM,QAAQ;IACZ,IAAI;MACAA,QAAQ,SAAS7N,cAAc,CAAC,CAAC;IACrC,CAAC,CACD,OAAO8N,KAAK,EAAE,CAAE;IAChB,IAAID,QAAQ,IAAIA,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAAC7L,QAAQ,EAAE;MAClD,MAAM,IAAItC,SAAS,CAAC;QAChBmF,IAAI,EAAE9E,oCAAoC;QAC1CgF,OAAO,EAAE,oCAAoC;QAC7CC,kBAAkB,EAAE;MACxB,CAAC,CAAC;IACN;EACJ,CAAC;EAAA,OAAA4I,2BAAA,CAAAlM,KAAA,OAAAC,SAAA;AAAA;AACD,SAASqM,uBAAuBA,CAAChM,QAAQ,EAAE;EACvC,MAAMiM,KAAK,GAAGpN,WAAW,CAAC2I,QAAQ,CAAC,CAAC;EACpC,OAAOyE,KAAK,CAACjM,QAAQ,IAAIA,QAAQ;AACrC;AAAC,SACciL,kBAAkBA,CAAAiB,IAAA;EAAA,OAAAC,mBAAA,CAAAzM,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAwM,oBAAA;EAAAA,mBAAA,GAAAvM,iBAAA,CAAjC,WAAkC;IAAEiH,aAAa;IAAEhH,iBAAiB;IAAEC,cAAc;IAAEC,OAAO;IAAEC,QAAQ;IAAEC;EAAQ,CAAC,EAAE;IAChH,MAAM;MAAEE,UAAU;MAAEC,gBAAgB;MAAEC;IAAiB,CAAC,GAAGJ,MAAM;IACjE,MAAMK,kBAAkB,GAAG;MACvBC,QAAQ,EAAEP;IACd,CAAC;IACD,IAAI6G,aAAa,KAAK,WAAW,EAAE;MAC/BvG,kBAAkB,CAAC8L,cAAc,GAAGvM,iBAAiB;IACzD;IACA,IAAIgH,aAAa,KAAK,SAAS,EAAE;MAC7BvG,kBAAkB,CAAC+L,YAAY,GAAGxM,iBAAiB;IACvD;IACA,IAAIgH,aAAa,KAAK,SAAS,EAAE;MAC7BvG,kBAAkB,CAACgM,YAAY,GAAGzM,iBAAiB;IACvD;IACA,IAAIgH,aAAa,KAAK,oBAAoB,EAAE;MACxCvG,kBAAkB,CAACiM,uBAAuB,GAAG1M,iBAAiB;IAClE;IACA,MAAM2M,eAAe,GAAGzN,kBAAkB,CAAC;MACvCiB,QAAQ;MACRG,UAAU;MACVC;IACJ,CAAC,CAAC;IACF,MAAMU,OAAO,GAAG;MACZC,aAAa,EAAE8F,aAAa;MAC5B7F,kBAAkB,EAAEV,kBAAkB;MACtCW,OAAO,EAAElB,OAAO;MAChBmB,cAAc,EAAEpB,cAAc;MAC9BqB,QAAQ,EAAEf,gBAAgB;MAC1BS,eAAe,EAAE2L;IACrB,CAAC;IACD,MAAMpL,sBAAsB,GAAGjD,kCAAkC,CAAC;MAC9DkD,gBAAgB,EAAE/C,qCAAqC,CAAC;QACpDgD,gBAAgB,EAAEjB;MACtB,CAAC;IACL,CAAC,CAAC;IACF,OAAOe,sBAAsB,CAAC;MAC1BI,MAAM,EAAEjD,uBAAuB,CAAC4B,UAAU,CAAC;MAC3CsB,cAAc,EAAExD,qBAAqB,CAACV,UAAU,CAACmE,aAAa;IAClE,CAAC,EAAEZ,OAAO,CAAC;EACf,CAAC;EAAA,OAAAqL,mBAAA,CAAAzM,KAAA,OAAAC,SAAA;AAAA;AAED,SAASgM,0BAA0B,EAAEjI,gBAAgB,EAAEsI,uBAAuB,EAAEnE,uBAAuB,EAAEuD,UAAU,EAAE1D,WAAW,EAAEjB,eAAe,EAAE0C,wBAAwB,EAAEd,mBAAmB,EAAE8B,mBAAmB,EAAE7G,kCAAkC,EAAE8B,8BAA8B,EAAE7F,qBAAqB,EAAEmG,uBAAuB,EAAEuF,kBAAkB,EAAEtJ,uBAAuB,EAAEsB,4BAA4B,EAAEW,0BAA0B,EAAEgB,qBAAqB,EAAEvB,UAAU,EAAEkF,eAAe,EAAEZ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}