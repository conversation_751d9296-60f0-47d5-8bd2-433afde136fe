{"ast": null, "code": "import { convertArrayBufferToBase64Url } from '../../../foundation/convert/base64url/convertArrayBufferToBase64Url.mjs';\nimport { convertBase64UrlToArrayBuffer } from '../../../foundation/convert/base64url/convertBase64UrlToArrayBuffer.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Deserializes Public Key Credential Creation Options JSON\n * @param input PasskeyCreateOptionsJson\n * @returns PublicKeyCredentialCreationOptions\n */\nconst deserializeJsonToPkcCreationOptions = input => {\n  const userIdBuffer = convertBase64UrlToArrayBuffer(input.user.id);\n  const challengeBuffer = convertBase64UrlToArrayBuffer(input.challenge);\n  const excludeCredentialsWithBuffer = (input.excludeCredentials || []).map(excludeCred => ({\n    ...excludeCred,\n    id: convertBase64UrlToArrayBuffer(excludeCred.id)\n  }));\n  return {\n    ...input,\n    excludeCredentials: excludeCredentialsWithBuffer,\n    challenge: challengeBuffer,\n    user: {\n      ...input.user,\n      id: userIdBuffer\n    }\n  };\n};\n/**\n * Serializes a Public Key Credential With Attestation to JSON\n * @param input PasskeyCreateResult\n * @returns PasskeyCreateResultJson\n */\nconst serializePkcWithAttestationToJson = input => {\n  const response = {\n    clientDataJSON: convertArrayBufferToBase64Url(input.response.clientDataJSON),\n    attestationObject: convertArrayBufferToBase64Url(input.response.attestationObject),\n    transports: input.response.getTransports(),\n    publicKeyAlgorithm: input.response.getPublicKeyAlgorithm(),\n    authenticatorData: convertArrayBufferToBase64Url(input.response.getAuthenticatorData())\n  };\n  const publicKey = input.response.getPublicKey();\n  if (publicKey) {\n    response.publicKey = convertArrayBufferToBase64Url(publicKey);\n  }\n  const resultJson = {\n    type: input.type,\n    id: input.id,\n    rawId: convertArrayBufferToBase64Url(input.rawId),\n    clientExtensionResults: input.getClientExtensionResults(),\n    response\n  };\n  if (input.authenticatorAttachment) {\n    resultJson.authenticatorAttachment = input.authenticatorAttachment;\n  }\n  return resultJson;\n};\n/**\n * Deserializes Public Key Credential Get Options JSON\n * @param input PasskeyGetOptionsJson\n * @returns PublicKeyCredentialRequestOptions\n */\nconst deserializeJsonToPkcGetOptions = input => {\n  const challengeBuffer = convertBase64UrlToArrayBuffer(input.challenge);\n  const allowedCredentialsWithBuffer = (input.allowCredentials || []).map(allowedCred => ({\n    ...allowedCred,\n    id: convertBase64UrlToArrayBuffer(allowedCred.id)\n  }));\n  return {\n    ...input,\n    challenge: challengeBuffer,\n    allowCredentials: allowedCredentialsWithBuffer\n  };\n};\n/**\n * Serializes a Public Key Credential With Attestation to JSON\n * @param input PasskeyGetResult\n * @returns PasskeyGetResultJson\n */\nconst serializePkcWithAssertionToJson = input => {\n  const response = {\n    clientDataJSON: convertArrayBufferToBase64Url(input.response.clientDataJSON),\n    authenticatorData: convertArrayBufferToBase64Url(input.response.authenticatorData),\n    signature: convertArrayBufferToBase64Url(input.response.signature)\n  };\n  if (input.response.userHandle) {\n    response.userHandle = convertArrayBufferToBase64Url(input.response.userHandle);\n  }\n  const resultJson = {\n    id: input.id,\n    rawId: convertArrayBufferToBase64Url(input.rawId),\n    type: input.type,\n    clientExtensionResults: input.getClientExtensionResults(),\n    response\n  };\n  if (input.authenticatorAttachment) {\n    resultJson.authenticatorAttachment = input.authenticatorAttachment;\n  }\n  return resultJson;\n};\nexport { deserializeJsonToPkcCreationOptions, deserializeJsonToPkcGetOptions, serializePkcWithAssertionToJson, serializePkcWithAttestationToJson };", "map": {"version": 3, "names": ["convertArrayBufferToBase64Url", "convertBase64UrlToArrayBuffer", "deserializeJsonToPkcCreationOptions", "input", "userIdBuffer", "user", "id", "challengeBuffer", "challenge", "excludeCredentialsWithBuffer", "excludeCredentials", "map", "excludeCred", "serializePkcWithAttestationToJson", "response", "clientDataJSON", "attestationObject", "transports", "getTransports", "publicKeyAlgorithm", "getPublicKeyAlgorithm", "authenticatorData", "getAuthenticatorData", "public<PERSON>ey", "getPublicKey", "result<PERSON><PERSON>", "type", "rawId", "clientExtensionResults", "getClientExtensionResults", "authenticatorAttachment", "deserializeJsonToPkcGetOptions", "allowedCredentialsWithBuffer", "allowCredentials", "allowed<PERSON>red", "serializePkcWithAssertionToJson", "signature", "userHandle"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/utils/passkey/serde.mjs"], "sourcesContent": ["import { convertArrayBufferToBase64Url } from '../../../foundation/convert/base64url/convertArrayBufferToBase64Url.mjs';\nimport { convertBase64UrlToArrayBuffer } from '../../../foundation/convert/base64url/convertBase64UrlToArrayBuffer.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Deserializes Public Key Credential Creation Options JSON\n * @param input PasskeyCreateOptionsJson\n * @returns PublicKeyCredentialCreationOptions\n */\nconst deserializeJsonToPkcCreationOptions = (input) => {\n    const userIdBuffer = convertBase64UrlToArrayBuffer(input.user.id);\n    const challengeBuffer = convertBase64UrlToArrayBuffer(input.challenge);\n    const excludeCredentialsWithBuffer = (input.excludeCredentials || []).map(excludeCred => ({\n        ...excludeCred,\n        id: convertBase64UrlToArrayBuffer(excludeCred.id),\n    }));\n    return {\n        ...input,\n        excludeCredentials: excludeCredentialsWithBuffer,\n        challenge: challengeBuffer,\n        user: {\n            ...input.user,\n            id: userIdBuffer,\n        },\n    };\n};\n/**\n * Serializes a Public Key Credential With Attestation to JSON\n * @param input PasskeyCreateResult\n * @returns PasskeyCreateResultJson\n */\nconst serializePkcWithAttestationToJson = (input) => {\n    const response = {\n        clientDataJSON: convertArrayBufferToBase64Url(input.response.clientDataJSON),\n        attestationObject: convertArrayBufferToBase64Url(input.response.attestationObject),\n        transports: input.response.getTransports(),\n        publicKeyAlgorithm: input.response.getPublicKeyAlgorithm(),\n        authenticatorData: convertArrayBufferToBase64Url(input.response.getAuthenticatorData()),\n    };\n    const publicKey = input.response.getPublicKey();\n    if (publicKey) {\n        response.publicKey = convertArrayBufferToBase64Url(publicKey);\n    }\n    const resultJson = {\n        type: input.type,\n        id: input.id,\n        rawId: convertArrayBufferToBase64Url(input.rawId),\n        clientExtensionResults: input.getClientExtensionResults(),\n        response,\n    };\n    if (input.authenticatorAttachment) {\n        resultJson.authenticatorAttachment = input.authenticatorAttachment;\n    }\n    return resultJson;\n};\n/**\n * Deserializes Public Key Credential Get Options JSON\n * @param input PasskeyGetOptionsJson\n * @returns PublicKeyCredentialRequestOptions\n */\nconst deserializeJsonToPkcGetOptions = (input) => {\n    const challengeBuffer = convertBase64UrlToArrayBuffer(input.challenge);\n    const allowedCredentialsWithBuffer = (input.allowCredentials || []).map(allowedCred => ({\n        ...allowedCred,\n        id: convertBase64UrlToArrayBuffer(allowedCred.id),\n    }));\n    return {\n        ...input,\n        challenge: challengeBuffer,\n        allowCredentials: allowedCredentialsWithBuffer,\n    };\n};\n/**\n * Serializes a Public Key Credential With Attestation to JSON\n * @param input PasskeyGetResult\n * @returns PasskeyGetResultJson\n */\nconst serializePkcWithAssertionToJson = (input) => {\n    const response = {\n        clientDataJSON: convertArrayBufferToBase64Url(input.response.clientDataJSON),\n        authenticatorData: convertArrayBufferToBase64Url(input.response.authenticatorData),\n        signature: convertArrayBufferToBase64Url(input.response.signature),\n    };\n    if (input.response.userHandle) {\n        response.userHandle = convertArrayBufferToBase64Url(input.response.userHandle);\n    }\n    const resultJson = {\n        id: input.id,\n        rawId: convertArrayBufferToBase64Url(input.rawId),\n        type: input.type,\n        clientExtensionResults: input.getClientExtensionResults(),\n        response,\n    };\n    if (input.authenticatorAttachment) {\n        resultJson.authenticatorAttachment = input.authenticatorAttachment;\n    }\n    return resultJson;\n};\n\nexport { deserializeJsonToPkcCreationOptions, deserializeJsonToPkcGetOptions, serializePkcWithAssertionToJson, serializePkcWithAttestationToJson };\n"], "mappings": "AAAA,SAASA,6BAA6B,QAAQ,yEAAyE;AACvH,SAASC,6BAA6B,QAAQ,yEAAyE;;AAEvH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mCAAmC,GAAIC,KAAK,IAAK;EACnD,MAAMC,YAAY,GAAGH,6BAA6B,CAACE,KAAK,CAACE,IAAI,CAACC,EAAE,CAAC;EACjE,MAAMC,eAAe,GAAGN,6BAA6B,CAACE,KAAK,CAACK,SAAS,CAAC;EACtE,MAAMC,4BAA4B,GAAG,CAACN,KAAK,CAACO,kBAAkB,IAAI,EAAE,EAAEC,GAAG,CAACC,WAAW,KAAK;IACtF,GAAGA,WAAW;IACdN,EAAE,EAAEL,6BAA6B,CAACW,WAAW,CAACN,EAAE;EACpD,CAAC,CAAC,CAAC;EACH,OAAO;IACH,GAAGH,KAAK;IACRO,kBAAkB,EAAED,4BAA4B;IAChDD,SAAS,EAAED,eAAe;IAC1BF,IAAI,EAAE;MACF,GAAGF,KAAK,CAACE,IAAI;MACbC,EAAE,EAAEF;IACR;EACJ,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMS,iCAAiC,GAAIV,KAAK,IAAK;EACjD,MAAMW,QAAQ,GAAG;IACbC,cAAc,EAAEf,6BAA6B,CAACG,KAAK,CAACW,QAAQ,CAACC,cAAc,CAAC;IAC5EC,iBAAiB,EAAEhB,6BAA6B,CAACG,KAAK,CAACW,QAAQ,CAACE,iBAAiB,CAAC;IAClFC,UAAU,EAAEd,KAAK,CAACW,QAAQ,CAACI,aAAa,CAAC,CAAC;IAC1CC,kBAAkB,EAAEhB,KAAK,CAACW,QAAQ,CAACM,qBAAqB,CAAC,CAAC;IAC1DC,iBAAiB,EAAErB,6BAA6B,CAACG,KAAK,CAACW,QAAQ,CAACQ,oBAAoB,CAAC,CAAC;EAC1F,CAAC;EACD,MAAMC,SAAS,GAAGpB,KAAK,CAACW,QAAQ,CAACU,YAAY,CAAC,CAAC;EAC/C,IAAID,SAAS,EAAE;IACXT,QAAQ,CAACS,SAAS,GAAGvB,6BAA6B,CAACuB,SAAS,CAAC;EACjE;EACA,MAAME,UAAU,GAAG;IACfC,IAAI,EAAEvB,KAAK,CAACuB,IAAI;IAChBpB,EAAE,EAAEH,KAAK,CAACG,EAAE;IACZqB,KAAK,EAAE3B,6BAA6B,CAACG,KAAK,CAACwB,KAAK,CAAC;IACjDC,sBAAsB,EAAEzB,KAAK,CAAC0B,yBAAyB,CAAC,CAAC;IACzDf;EACJ,CAAC;EACD,IAAIX,KAAK,CAAC2B,uBAAuB,EAAE;IAC/BL,UAAU,CAACK,uBAAuB,GAAG3B,KAAK,CAAC2B,uBAAuB;EACtE;EACA,OAAOL,UAAU;AACrB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMM,8BAA8B,GAAI5B,KAAK,IAAK;EAC9C,MAAMI,eAAe,GAAGN,6BAA6B,CAACE,KAAK,CAACK,SAAS,CAAC;EACtE,MAAMwB,4BAA4B,GAAG,CAAC7B,KAAK,CAAC8B,gBAAgB,IAAI,EAAE,EAAEtB,GAAG,CAACuB,WAAW,KAAK;IACpF,GAAGA,WAAW;IACd5B,EAAE,EAAEL,6BAA6B,CAACiC,WAAW,CAAC5B,EAAE;EACpD,CAAC,CAAC,CAAC;EACH,OAAO;IACH,GAAGH,KAAK;IACRK,SAAS,EAAED,eAAe;IAC1B0B,gBAAgB,EAAED;EACtB,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMG,+BAA+B,GAAIhC,KAAK,IAAK;EAC/C,MAAMW,QAAQ,GAAG;IACbC,cAAc,EAAEf,6BAA6B,CAACG,KAAK,CAACW,QAAQ,CAACC,cAAc,CAAC;IAC5EM,iBAAiB,EAAErB,6BAA6B,CAACG,KAAK,CAACW,QAAQ,CAACO,iBAAiB,CAAC;IAClFe,SAAS,EAAEpC,6BAA6B,CAACG,KAAK,CAACW,QAAQ,CAACsB,SAAS;EACrE,CAAC;EACD,IAAIjC,KAAK,CAACW,QAAQ,CAACuB,UAAU,EAAE;IAC3BvB,QAAQ,CAACuB,UAAU,GAAGrC,6BAA6B,CAACG,KAAK,CAACW,QAAQ,CAACuB,UAAU,CAAC;EAClF;EACA,MAAMZ,UAAU,GAAG;IACfnB,EAAE,EAAEH,KAAK,CAACG,EAAE;IACZqB,KAAK,EAAE3B,6BAA6B,CAACG,KAAK,CAACwB,KAAK,CAAC;IACjDD,IAAI,EAAEvB,KAAK,CAACuB,IAAI;IAChBE,sBAAsB,EAAEzB,KAAK,CAAC0B,yBAAyB,CAAC,CAAC;IACzDf;EACJ,CAAC;EACD,IAAIX,KAAK,CAAC2B,uBAAuB,EAAE;IAC/BL,UAAU,CAACK,uBAAuB,GAAG3B,KAAK,CAAC2B,uBAAuB;EACtE;EACA,OAAOL,UAAU;AACrB,CAAC;AAED,SAASvB,mCAAmC,EAAE6B,8BAA8B,EAAEI,+BAA+B,EAAEtB,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}