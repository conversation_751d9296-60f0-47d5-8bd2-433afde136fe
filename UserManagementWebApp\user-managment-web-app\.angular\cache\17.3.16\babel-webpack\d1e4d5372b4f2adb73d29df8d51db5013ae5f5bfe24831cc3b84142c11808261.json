{"ast": null, "code": "import { validationErrorMap } from '../../common/AuthErrorStrings.mjs';\nimport { AuthError } from '../AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction assertValidationError(assertion, name) {\n  const {\n    message,\n    recoverySuggestion\n  } = validationErrorMap[name];\n  if (!assertion) {\n    throw new AuthError({\n      name,\n      message,\n      recoverySuggestion\n    });\n  }\n}\nexport { assertValidationError };", "map": {"version": 3, "names": ["validationErrorMap", "<PERSON>th<PERSON><PERSON><PERSON>", "assertValidationError", "assertion", "name", "message", "recoverySuggestion"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/errors/utils/assertValidationError.mjs"], "sourcesContent": ["import { validationErrorMap } from '../../common/AuthErrorStrings.mjs';\nimport { AuthError } from '../AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction assertValidationError(assertion, name) {\n    const { message, recoverySuggestion } = validationErrorMap[name];\n    if (!assertion) {\n        throw new AuthError({ name, message, recoverySuggestion });\n    }\n}\n\nexport { assertValidationError };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,SAAS,QAAQ,kBAAkB;;AAE5C;AACA;AACA,SAASC,qBAAqBA,CAACC,SAAS,EAAEC,IAAI,EAAE;EAC5C,MAAM;IAAEC,OAAO;IAAEC;EAAmB,CAAC,GAAGN,kBAAkB,CAACI,IAAI,CAAC;EAChE,IAAI,CAACD,SAAS,EAAE;IACZ,MAAM,IAAIF,SAAS,CAAC;MAAEG,IAAI;MAAEC,OAAO;MAAEC;IAAmB,CAAC,CAAC;EAC9D;AACJ;AAEA,SAASJ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}