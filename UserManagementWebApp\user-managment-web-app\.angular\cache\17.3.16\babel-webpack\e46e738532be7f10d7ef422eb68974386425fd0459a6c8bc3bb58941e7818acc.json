{"ast": null, "code": "const textfield = {\n  color: {\n    value: '{components.fieldcontrol.color.value}'\n  },\n  borderColor: {\n    value: '{components.fieldcontrol.borderColor.value}'\n  },\n  fontSize: {\n    value: '{components.fieldcontrol.fontSize.value}'\n  },\n  _focus: {\n    borderColor: {\n      value: '{components.fieldcontrol._focus.borderColor.value}'\n    }\n  }\n};\nexport { textfield };", "map": {"version": 3, "names": ["textfield", "color", "value", "borderColor", "fontSize", "_focus"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/textField.mjs"], "sourcesContent": ["const textfield = {\n    color: { value: '{components.fieldcontrol.color.value}' },\n    borderColor: { value: '{components.fieldcontrol.borderColor.value}' },\n    fontSize: { value: '{components.fieldcontrol.fontSize.value}' },\n    _focus: {\n        borderColor: {\n            value: '{components.fieldcontrol._focus.borderColor.value}',\n        },\n    },\n};\n\nexport { textfield };\n"], "mappings": "AAAA,MAAMA,SAAS,GAAG;EACdC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAwC,CAAC;EACzDC,WAAW,EAAE;IAAED,KAAK,EAAE;EAA8C,CAAC;EACrEE,QAAQ,EAAE;IAAEF,KAAK,EAAE;EAA2C,CAAC;EAC/DG,MAAM,EAAE;IACJF,WAAW,EAAE;MACTD,KAAK,EAAE;IACX;EACJ;AACJ,CAAC;AAED,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}