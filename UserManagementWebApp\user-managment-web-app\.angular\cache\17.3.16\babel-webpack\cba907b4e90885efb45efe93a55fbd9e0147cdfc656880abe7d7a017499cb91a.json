{"ast": null, "code": "import validate from './validate.js';\nfunction parse(uuid) {\n  if (!validate(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n  let v;\n  const arr = new Uint8Array(16); // Parse ########-....-....-....-............\n\n  arr[0] = (v = parseInt(uuid.slice(0, 8), 16)) >>> 24;\n  arr[1] = v >>> 16 & 0xff;\n  arr[2] = v >>> 8 & 0xff;\n  arr[3] = v & 0xff; // Parse ........-####-....-....-............\n\n  arr[4] = (v = parseInt(uuid.slice(9, 13), 16)) >>> 8;\n  arr[5] = v & 0xff; // Parse ........-....-####-....-............\n\n  arr[6] = (v = parseInt(uuid.slice(14, 18), 16)) >>> 8;\n  arr[7] = v & 0xff; // Parse ........-....-....-####-............\n\n  arr[8] = (v = parseInt(uuid.slice(19, 23), 16)) >>> 8;\n  arr[9] = v & 0xff; // Parse ........-....-....-....-############\n  // (Use \"/\" to avoid 32-bit truncation when bit-shifting high-order bytes)\n\n  arr[10] = (v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000 & 0xff;\n  arr[11] = v / 0x100000000 & 0xff;\n  arr[12] = v >>> 24 & 0xff;\n  arr[13] = v >>> 16 & 0xff;\n  arr[14] = v >>> 8 & 0xff;\n  arr[15] = v & 0xff;\n  return arr;\n}\nexport default parse;", "map": {"version": 3, "names": ["validate", "parse", "uuid", "TypeError", "v", "arr", "Uint8Array", "parseInt", "slice"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/uuid/dist/esm-browser/parse.js"], "sourcesContent": ["import validate from './validate.js';\n\nfunction parse(uuid) {\n  if (!validate(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  let v;\n  const arr = new Uint8Array(16); // Parse ########-....-....-....-............\n\n  arr[0] = (v = parseInt(uuid.slice(0, 8), 16)) >>> 24;\n  arr[1] = v >>> 16 & 0xff;\n  arr[2] = v >>> 8 & 0xff;\n  arr[3] = v & 0xff; // Parse ........-####-....-....-............\n\n  arr[4] = (v = parseInt(uuid.slice(9, 13), 16)) >>> 8;\n  arr[5] = v & 0xff; // Parse ........-....-####-....-............\n\n  arr[6] = (v = parseInt(uuid.slice(14, 18), 16)) >>> 8;\n  arr[7] = v & 0xff; // Parse ........-....-....-####-............\n\n  arr[8] = (v = parseInt(uuid.slice(19, 23), 16)) >>> 8;\n  arr[9] = v & 0xff; // Parse ........-....-....-....-############\n  // (Use \"/\" to avoid 32-bit truncation when bit-shifting high-order bytes)\n\n  arr[10] = (v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000 & 0xff;\n  arr[11] = v / 0x100000000 & 0xff;\n  arr[12] = v >>> 24 & 0xff;\n  arr[13] = v >>> 16 & 0xff;\n  arr[14] = v >>> 8 & 0xff;\n  arr[15] = v & 0xff;\n  return arr;\n}\n\nexport default parse;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AAEpC,SAASC,KAAKA,CAACC,IAAI,EAAE;EACnB,IAAI,CAACF,QAAQ,CAACE,IAAI,CAAC,EAAE;IACnB,MAAMC,SAAS,CAAC,cAAc,CAAC;EACjC;EAEA,IAAIC,CAAC;EACL,MAAMC,GAAG,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEhCD,GAAG,CAAC,CAAC,CAAC,GAAG,CAACD,CAAC,GAAGG,QAAQ,CAACL,IAAI,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE;EACpDH,GAAG,CAAC,CAAC,CAAC,GAAGD,CAAC,KAAK,EAAE,GAAG,IAAI;EACxBC,GAAG,CAAC,CAAC,CAAC,GAAGD,CAAC,KAAK,CAAC,GAAG,IAAI;EACvBC,GAAG,CAAC,CAAC,CAAC,GAAGD,CAAC,GAAG,IAAI,CAAC,CAAC;;EAEnBC,GAAG,CAAC,CAAC,CAAC,GAAG,CAACD,CAAC,GAAGG,QAAQ,CAACL,IAAI,CAACM,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC;EACpDH,GAAG,CAAC,CAAC,CAAC,GAAGD,CAAC,GAAG,IAAI,CAAC,CAAC;;EAEnBC,GAAG,CAAC,CAAC,CAAC,GAAG,CAACD,CAAC,GAAGG,QAAQ,CAACL,IAAI,CAACM,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC;EACrDH,GAAG,CAAC,CAAC,CAAC,GAAGD,CAAC,GAAG,IAAI,CAAC,CAAC;;EAEnBC,GAAG,CAAC,CAAC,CAAC,GAAG,CAACD,CAAC,GAAGG,QAAQ,CAACL,IAAI,CAACM,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC;EACrDH,GAAG,CAAC,CAAC,CAAC,GAAGD,CAAC,GAAG,IAAI,CAAC,CAAC;EACnB;;EAEAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAACD,CAAC,GAAGG,QAAQ,CAACL,IAAI,CAACM,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,aAAa,GAAG,IAAI;EACvEH,GAAG,CAAC,EAAE,CAAC,GAAGD,CAAC,GAAG,WAAW,GAAG,IAAI;EAChCC,GAAG,CAAC,EAAE,CAAC,GAAGD,CAAC,KAAK,EAAE,GAAG,IAAI;EACzBC,GAAG,CAAC,EAAE,CAAC,GAAGD,CAAC,KAAK,EAAE,GAAG,IAAI;EACzBC,GAAG,CAAC,EAAE,CAAC,GAAGD,CAAC,KAAK,CAAC,GAAG,IAAI;EACxBC,GAAG,CAAC,EAAE,CAAC,GAAGD,CAAC,GAAG,IAAI;EAClB,OAAOC,GAAG;AACZ;AAEA,eAAeJ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}