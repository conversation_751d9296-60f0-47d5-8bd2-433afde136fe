{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertAuthTokens } from '../../providers/cognito/utils/types.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../common/AuthErrorStrings.mjs';\nimport '../../errors/types/validation.mjs';\nimport '../../providers/cognito/types/errors.mjs';\nimport { createListWebAuthnCredentialsClient } from '../factories/serviceClients/cognitoIdentityProvider/createListWebAuthnCredentialsClient.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction listWebAuthnCredentials(_x, _x2) {\n  return _listWebAuthnCredentials.apply(this, arguments);\n}\nfunction _listWebAuthnCredentials() {\n  _listWebAuthnCredentials = _asyncToGenerator(function* (amplify, input) {\n    const authConfig = amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolEndpoint,\n      userPoolId\n    } = authConfig;\n    const {\n      tokens\n    } = yield amplify.Auth.fetchAuthSession();\n    assertAuthTokens(tokens);\n    const listWebAuthnCredentialsResult = createListWebAuthnCredentialsClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const {\n      Credentials: commandCredentials = [],\n      NextToken: nextToken\n    } = yield listWebAuthnCredentialsResult({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.ListWebAuthnCredentials)\n    }, {\n      AccessToken: tokens.accessToken.toString(),\n      MaxResults: input?.pageSize,\n      NextToken: input?.nextToken\n    });\n    const credentials = commandCredentials.map(item => ({\n      credentialId: item.CredentialId,\n      friendlyCredentialName: item.FriendlyCredentialName,\n      relyingPartyId: item.RelyingPartyId,\n      authenticatorAttachment: item.AuthenticatorAttachment,\n      authenticatorTransports: item.AuthenticatorTransports,\n      createdAt: item.CreatedAt ? new Date(item.CreatedAt * 1000) : undefined\n    }));\n    return {\n      credentials,\n      nextToken\n    };\n  });\n  return _listWebAuthnCredentials.apply(this, arguments);\n}\nexport { listWebAuthnCredentials };", "map": {"version": 3, "names": ["assertTokenProviderConfig", "AuthAction", "assertAuthTokens", "createCognitoUserPoolEndpointResolver", "getRegionFromUserPoolId", "getAuthUserAgentValue", "createListWebAuthnCredentialsClient", "listWebAuthnCredentials", "_x", "_x2", "_listWebAuthnCredentials", "apply", "arguments", "_asyncToGenerator", "amplify", "input", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userPoolEndpoint", "userPoolId", "tokens", "fetchAuthSession", "listWebAuthnCredentialsResult", "endpointResolver", "endpointOverride", "Credentials", "commandCredentials", "NextToken", "nextToken", "region", "userAgentValue", "ListWebAuthnCredentials", "AccessToken", "accessToken", "toString", "MaxResults", "pageSize", "credentials", "map", "item", "credentialId", "CredentialId", "friendlyCredentialName", "FriendlyCredentialName", "relyingPartyId", "RelyingPartyId", "authenticatorAttachment", "AuthenticatorAttachment", "authenticatorTransports", "AuthenticatorTransports", "createdAt", "CreatedAt", "Date", "undefined"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/apis/listWebAuthnCredentials.mjs"], "sourcesContent": ["import { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertAuthTokens } from '../../providers/cognito/utils/types.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../common/AuthErrorStrings.mjs';\nimport '../../errors/types/validation.mjs';\nimport '../../providers/cognito/types/errors.mjs';\nimport { createListWebAuthnCredentialsClient } from '../factories/serviceClients/cognitoIdentityProvider/createListWebAuthnCredentialsClient.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nasync function listWebAuthnCredentials(amplify, input) {\n    const authConfig = amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolEndpoint, userPoolId } = authConfig;\n    const { tokens } = await amplify.Auth.fetchAuthSession();\n    assertAuthTokens(tokens);\n    const listWebAuthnCredentialsResult = createListWebAuthnCredentialsClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const { Credentials: commandCredentials = [], NextToken: nextToken } = await listWebAuthnCredentialsResult({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ListWebAuthnCredentials),\n    }, {\n        AccessToken: tokens.accessToken.toString(),\n        MaxResults: input?.pageSize,\n        NextToken: input?.nextToken,\n    });\n    const credentials = commandCredentials.map(item => ({\n        credentialId: item.CredentialId,\n        friendlyCredentialName: item.FriendlyCredentialName,\n        relyingPartyId: item.RelyingPartyId,\n        authenticatorAttachment: item.AuthenticatorAttachment,\n        authenticatorTransports: item.AuthenticatorTransports,\n        createdAt: item.CreatedAt ? new Date(item.CreatedAt * 1000) : undefined,\n    }));\n    return {\n        credentials,\n        nextToken,\n    };\n}\n\nexport { listWebAuthnCredentials };\n"], "mappings": ";AAAA,SAASA,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,SAASC,qCAAqC,QAAQ,6EAA6E;AACnI,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,uGAAuG;AAC9G,OAAO,mEAAmE;AAC1E,OAAO,mCAAmC;AAC1C,OAAO,mCAAmC;AAC1C,OAAO,0CAA0C;AACjD,SAASC,mCAAmC,QAAQ,6FAA6F;;AAEjJ;AACA;AAAA,SACeC,uBAAuBA,CAAAC,EAAA,EAAAC,GAAA;EAAA,OAAAC,wBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,yBAAA;EAAAA,wBAAA,GAAAG,iBAAA,CAAtC,WAAuCC,OAAO,EAAEC,KAAK,EAAE;IACnD,MAAMC,UAAU,GAAGF,OAAO,CAACG,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDnB,yBAAyB,CAACgB,UAAU,CAAC;IACrC,MAAM;MAAEI,gBAAgB;MAAEC;IAAW,CAAC,GAAGL,UAAU;IACnD,MAAM;MAAEM;IAAO,CAAC,SAASR,OAAO,CAACI,IAAI,CAACK,gBAAgB,CAAC,CAAC;IACxDrB,gBAAgB,CAACoB,MAAM,CAAC;IACxB,MAAME,6BAA6B,GAAGlB,mCAAmC,CAAC;MACtEmB,gBAAgB,EAAEtB,qCAAqC,CAAC;QACpDuB,gBAAgB,EAAEN;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAM;MAAEO,WAAW,EAAEC,kBAAkB,GAAG,EAAE;MAAEC,SAAS,EAAEC;IAAU,CAAC,SAASN,6BAA6B,CAAC;MACvGO,MAAM,EAAE3B,uBAAuB,CAACiB,UAAU,CAAC;MAC3CW,cAAc,EAAE3B,qBAAqB,CAACJ,UAAU,CAACgC,uBAAuB;IAC5E,CAAC,EAAE;MACCC,WAAW,EAAEZ,MAAM,CAACa,WAAW,CAACC,QAAQ,CAAC,CAAC;MAC1CC,UAAU,EAAEtB,KAAK,EAAEuB,QAAQ;MAC3BT,SAAS,EAAEd,KAAK,EAAEe;IACtB,CAAC,CAAC;IACF,MAAMS,WAAW,GAAGX,kBAAkB,CAACY,GAAG,CAACC,IAAI,KAAK;MAChDC,YAAY,EAAED,IAAI,CAACE,YAAY;MAC/BC,sBAAsB,EAAEH,IAAI,CAACI,sBAAsB;MACnDC,cAAc,EAAEL,IAAI,CAACM,cAAc;MACnCC,uBAAuB,EAAEP,IAAI,CAACQ,uBAAuB;MACrDC,uBAAuB,EAAET,IAAI,CAACU,uBAAuB;MACrDC,SAAS,EAAEX,IAAI,CAACY,SAAS,GAAG,IAAIC,IAAI,CAACb,IAAI,CAACY,SAAS,GAAG,IAAI,CAAC,GAAGE;IAClE,CAAC,CAAC,CAAC;IACH,OAAO;MACHhB,WAAW;MACXT;IACJ,CAAC;EACL,CAAC;EAAA,OAAApB,wBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASL,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}