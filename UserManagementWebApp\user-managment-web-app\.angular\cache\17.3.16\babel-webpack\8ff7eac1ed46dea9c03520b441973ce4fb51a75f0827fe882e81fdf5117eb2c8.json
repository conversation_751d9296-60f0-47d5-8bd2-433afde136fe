{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertAuthTokens, assertDeviceMetadata } from '../utils/types.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport '../utils/refreshAuthTokens.mjs';\nimport '../tokenProvider/errorHelpers.mjs';\nimport { tokenOrchestrator } from '../tokenProvider/tokenProvider.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createUpdateDeviceStatusClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createUpdateDeviceStatusClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Marks device as remembered while authenticated.\n *\n * @throws - {@link UpdateDeviceStatusException} - Cognito service errors thrown when\n * setting device status to remembered using an invalid device key.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nfunction rememberDevice() {\n  return _rememberDevice.apply(this, arguments);\n}\nfunction _rememberDevice() {\n  _rememberDevice = _asyncToGenerator(function* () {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolEndpoint,\n      userPoolId\n    } = authConfig;\n    const {\n      tokens\n    } = yield fetchAuthSession();\n    assertAuthTokens(tokens);\n    const deviceMetadata = yield tokenOrchestrator?.getDeviceMetadata();\n    assertDeviceMetadata(deviceMetadata);\n    const updateDeviceStatus = createUpdateDeviceStatusClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    yield updateDeviceStatus({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.RememberDevice)\n    }, {\n      AccessToken: tokens.accessToken.toString(),\n      DeviceKey: deviceMetadata.deviceKey,\n      DeviceRememberedStatus: 'remembered'\n    });\n  });\n  return _rememberDevice.apply(this, arguments);\n}\nexport { rememberDevice };", "map": {"version": 3, "names": ["Amplify", "fetchAuthSession", "assertTokenProviderConfig", "AuthAction", "assertAuthTokens", "assertDeviceMetadata", "getRegionFromUserPoolId", "tokenOrchestrator", "getAuthUserAgentValue", "createUpdateDeviceStatusClient", "createCognitoUserPoolEndpointResolver", "rememberDevice", "_rememberDevice", "apply", "arguments", "_asyncToGenerator", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userPoolEndpoint", "userPoolId", "tokens", "deviceMetadata", "getDeviceMetadata", "updateDeviceStatus", "endpointResolver", "endpointOverride", "region", "userAgentValue", "RememberDevice", "AccessToken", "accessToken", "toString", "<PERSON><PERSON><PERSON><PERSON>", "deviceKey", "DeviceRememberedStatus"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/rememberDevice.mjs"], "sourcesContent": ["import { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertAuthTokens, assertDeviceMetadata } from '../utils/types.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport '../utils/refreshAuthTokens.mjs';\nimport '../tokenProvider/errorHelpers.mjs';\nimport { tokenOrchestrator } from '../tokenProvider/tokenProvider.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createUpdateDeviceStatusClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createUpdateDeviceStatusClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Marks device as remembered while authenticated.\n *\n * @throws - {@link UpdateDeviceStatusException} - Cognito service errors thrown when\n * setting device status to remembered using an invalid device key.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nasync function rememberDevice() {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolEndpoint, userPoolId } = authConfig;\n    const { tokens } = await fetchAuthSession();\n    assertAuthTokens(tokens);\n    const deviceMetadata = await tokenOrchestrator?.getDeviceMetadata();\n    assertDeviceMetadata(deviceMetadata);\n    const updateDeviceStatus = createUpdateDeviceStatusClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    await updateDeviceStatus({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.RememberDevice),\n    }, {\n        AccessToken: tokens.accessToken.toString(),\n        DeviceKey: deviceMetadata.deviceKey,\n        DeviceRememberedStatus: 'remembered',\n    });\n}\n\nexport { rememberDevice };\n"], "mappings": ";AAAA,SAASA,OAAO,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC7D,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,gBAAgB,EAAEC,oBAAoB,QAAQ,oBAAoB;AAC3E,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,OAAO,gCAAgC;AACvC,OAAO,mCAAmC;AAC1C,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,sCAAsC;AAC7C,OAAO,qBAAqB;AAC5B,SAASC,8BAA8B,QAAQ,yGAAyG;AACxJ,SAASC,qCAAqC,QAAQ,wDAAwD;;AAE9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA,SAOeC,cAAcA,CAAA;EAAA,OAAAC,eAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,gBAAA;EAAAA,eAAA,GAAAG,iBAAA,CAA7B,aAAgC;IAC5B,MAAMC,UAAU,GAAGhB,OAAO,CAACiB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDjB,yBAAyB,CAACc,UAAU,CAAC;IACrC,MAAM;MAAEI,gBAAgB;MAAEC;IAAW,CAAC,GAAGL,UAAU;IACnD,MAAM;MAAEM;IAAO,CAAC,SAASrB,gBAAgB,CAAC,CAAC;IAC3CG,gBAAgB,CAACkB,MAAM,CAAC;IACxB,MAAMC,cAAc,SAAShB,iBAAiB,EAAEiB,iBAAiB,CAAC,CAAC;IACnEnB,oBAAoB,CAACkB,cAAc,CAAC;IACpC,MAAME,kBAAkB,GAAGhB,8BAA8B,CAAC;MACtDiB,gBAAgB,EAAEhB,qCAAqC,CAAC;QACpDiB,gBAAgB,EAAEP;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMK,kBAAkB,CAAC;MACrBG,MAAM,EAAEtB,uBAAuB,CAACe,UAAU,CAAC;MAC3CQ,cAAc,EAAErB,qBAAqB,CAACL,UAAU,CAAC2B,cAAc;IACnE,CAAC,EAAE;MACCC,WAAW,EAAET,MAAM,CAACU,WAAW,CAACC,QAAQ,CAAC,CAAC;MAC1CC,SAAS,EAAEX,cAAc,CAACY,SAAS;MACnCC,sBAAsB,EAAE;IAC5B,CAAC,CAAC;EACN,CAAC;EAAA,OAAAxB,eAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}