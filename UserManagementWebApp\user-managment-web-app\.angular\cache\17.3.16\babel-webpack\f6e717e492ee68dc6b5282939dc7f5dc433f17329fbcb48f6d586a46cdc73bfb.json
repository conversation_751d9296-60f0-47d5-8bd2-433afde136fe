{"ast": null, "code": "const transforms = {\n  // TODO: make this more generic and cross-platform\n  slideX: {\n    small: {\n      value: 'translateX(0.5em)'\n    },\n    medium: {\n      value: 'translateX(1em)'\n    },\n    large: {\n      value: 'translateX(2em)'\n    }\n  }\n};\nexport { transforms };", "map": {"version": 3, "names": ["transforms", "slideX", "small", "value", "medium", "large"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/transforms.mjs"], "sourcesContent": ["const transforms = {\n    // TODO: make this more generic and cross-platform\n    slideX: {\n        small: { value: 'translateX(0.5em)' },\n        medium: { value: 'translateX(1em)' },\n        large: { value: 'translateX(2em)' },\n    },\n};\n\nexport { transforms };\n"], "mappings": "AAAA,MAAMA,UAAU,GAAG;EACf;EACAC,MAAM,EAAE;IACJC,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAoB,CAAC;IACrCC,MAAM,EAAE;MAAED,KAAK,EAAE;IAAkB,CAAC;IACpCE,KAAK,EAAE;MAAEF,KAAK,EAAE;IAAkB;EACtC;AACJ,CAAC;AAED,SAASH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}