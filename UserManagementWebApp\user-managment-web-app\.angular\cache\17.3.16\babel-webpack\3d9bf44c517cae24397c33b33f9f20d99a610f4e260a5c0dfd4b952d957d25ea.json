{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass BackgroundManagerNotOpenError extends Error {\n  constructor(message) {\n    super(`BackgroundManagerNotOpenError: ${message}`);\n  }\n}\nexport { BackgroundManagerNotOpenError };", "map": {"version": 3, "names": ["BackgroundManagerNotOpenError", "Error", "constructor", "message"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/BackgroundProcessManager/BackgroundManagerNotOpenError.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass BackgroundManagerNotOpenError extends Error {\n    constructor(message) {\n        super(`BackgroundManagerNotOpenError: ${message}`);\n    }\n}\n\nexport { BackgroundManagerNotOpenError };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,6BAA6B,SAASC,KAAK,CAAC;EAC9CC,WAAWA,CAACC,OAAO,EAAE;IACjB,KAAK,CAAC,kCAAkCA,OAAO,EAAE,CAAC;EACtD;AACJ;AAEA,SAASH,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}