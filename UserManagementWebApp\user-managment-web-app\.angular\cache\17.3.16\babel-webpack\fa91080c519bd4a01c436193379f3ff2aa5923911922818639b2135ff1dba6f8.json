{"ast": null, "code": "export default function invariant(condition, message) {\n  var booleanCondition = Boolean(condition); // istanbul ignore else (See transformation done in './resources/inlineInvariant.js')\n\n  if (!booleanCondition) {\n    throw new Error(message != null ? message : 'Unexpected invariant triggered.');\n  }\n}", "map": {"version": 3, "names": ["invariant", "condition", "message", "booleanCondition", "Boolean", "Error"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/graphql/jsutils/invariant.mjs"], "sourcesContent": ["export default function invariant(condition, message) {\n  var booleanCondition = Boolean(condition); // istanbul ignore else (See transformation done in './resources/inlineInvariant.js')\n\n  if (!booleanCondition) {\n    throw new Error(message != null ? message : 'Unexpected invariant triggered.');\n  }\n}\n"], "mappings": "AAAA,eAAe,SAASA,SAASA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACpD,IAAIC,gBAAgB,GAAGC,OAAO,CAACH,SAAS,CAAC,CAAC,CAAC;;EAE3C,IAAI,CAACE,gBAAgB,EAAE;IACrB,MAAM,IAAIE,KAAK,CAACH,OAAO,IAAI,IAAI,GAAGA,OAAO,GAAG,iCAAiC,CAAC;EAChF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}