{"ast": null, "code": "import { composeService<PERSON>pi } from '@aws-amplify/core/internals/aws-client-utils/composers';\nimport { cognitoUserPoolTransferHandler } from './shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport { createUserPoolSerializer } from './shared/serde/createUserPoolSerializer.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '@aws-amplify/core/internals/utils';\nimport { createEmptyResponseDeserializer } from './shared/serde/createEmptyResponseDeserializer.mjs';\nimport { DEFAULT_SERVICE_CLIENT_API_CONFIG } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createForgetDeviceClient = config => composeServiceApi(cognitoUserPoolTransferHandler, createUserPoolSerializer('ForgetDevice'), createEmptyResponseDeserializer(), {\n  ...DEFAULT_SERVICE_CLIENT_API_CONFIG,\n  ...config\n});\nexport { createForgetDeviceClient };", "map": {"version": 3, "names": ["composeServiceApi", "cognitoUserPoolTransferHandler", "createUserPoolSerializer", "createEmptyResponseDeserializer", "DEFAULT_SERVICE_CLIENT_API_CONFIG", "createForgetDeviceClient", "config"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/factories/serviceClients/cognitoIdentityProvider/createForgetDeviceClient.mjs"], "sourcesContent": ["import { composeService<PERSON>pi } from '@aws-amplify/core/internals/aws-client-utils/composers';\nimport { cognitoUserPoolTransferHandler } from './shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport { createUserPoolSerializer } from './shared/serde/createUserPoolSerializer.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '@aws-amplify/core/internals/utils';\nimport { createEmptyResponseDeserializer } from './shared/serde/createEmptyResponseDeserializer.mjs';\nimport { DEFAULT_SERVICE_CLIENT_API_CONFIG } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createForgetDeviceClient = (config) => composeServiceApi(cognitoUserPoolTransferHandler, createUserPoolSerializer('ForgetDevice'), createEmptyResponseDeserializer(), {\n    ...DEFAULT_SERVICE_CLIENT_API_CONFIG,\n    ...config,\n});\n\nexport { createForgetDeviceClient };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,wDAAwD;AAC1F,SAASC,8BAA8B,QAAQ,qDAAqD;AACpG,SAASC,wBAAwB,QAAQ,6CAA6C;AACtF,OAAO,8CAA8C;AACrD,OAAO,mCAAmC;AAC1C,SAASC,+BAA+B,QAAQ,oDAAoD;AACpG,SAASC,iCAAiC,QAAQ,iBAAiB;;AAEnE;AACA;AACA,MAAMC,wBAAwB,GAAIC,MAAM,IAAKN,iBAAiB,CAACC,8BAA8B,EAAEC,wBAAwB,CAAC,cAAc,CAAC,EAAEC,+BAA+B,CAAC,CAAC,EAAE;EACxK,GAAGC,iCAAiC;EACpC,GAAGE;AACP,CAAC,CAAC;AAEF,SAASD,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}