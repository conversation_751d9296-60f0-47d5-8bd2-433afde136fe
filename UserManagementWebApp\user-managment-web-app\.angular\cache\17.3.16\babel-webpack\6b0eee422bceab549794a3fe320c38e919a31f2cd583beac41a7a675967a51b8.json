{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst isWebWorker = () => {\n  if (typeof self === 'undefined') {\n    return false;\n  }\n  const selfContext = self;\n  return typeof selfContext.WorkerGlobalScope !== 'undefined' && self instanceof selfContext.WorkerGlobalScope;\n};\nexport { isWebWorker };", "map": {"version": 3, "names": ["isWebWorker", "self", "selfContext", "WorkerGlobalScope"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/isWebWorker.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst isWebWorker = () => {\n    if (typeof self === 'undefined') {\n        return false;\n    }\n    const selfContext = self;\n    return (typeof selfContext.WorkerGlobalScope !== 'undefined' &&\n        self instanceof selfContext.WorkerGlobalScope);\n};\n\nexport { isWebWorker };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,WAAW,GAAGA,CAAA,KAAM;EACtB,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAC7B,OAAO,KAAK;EAChB;EACA,MAAMC,WAAW,GAAGD,IAAI;EACxB,OAAQ,OAAOC,WAAW,CAACC,iBAAiB,KAAK,WAAW,IACxDF,IAAI,YAAYC,WAAW,CAACC,iBAAiB;AACrD,CAAC;AAED,SAASH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}