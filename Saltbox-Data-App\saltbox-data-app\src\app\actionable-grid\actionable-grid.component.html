<p-panel class="actionable-grid-d-container" [showHeader]="false" [ngClass]="sbiWrapperPad" styleClass="p-2 mb-0">
  <div class="sbi-actionable-grid-display">
    @if (this.reportInfo) {
    <app-actions-menu [previewMode]="isPreview" [reportInfo]="reportInfo"
      [checkHasPendingChanges]="hasPendingChanges() && !showFormlyDataEntryDialog"
      [checkHasChanges]="hasChanges() && !showFormlyDataEntryDialog" [checkHasSlicers]="hasSlicer()"
      (showFormEditor)="showFormEditor($event)" (undoLastChange)="undoLastChange()" (undoAll)="undoAll()"
      (saveChanges)="saveChanges()" (refreshGridData)="onRefreshGridData()" (sendEmailClick)="onSendEmail()"
      (showRefiner)="showRefiner()" (showValidationResults)="showValidationResults = true" [agGrid]="agGrid"
      [columnsToExport]="columnsToExport" [disabled]="projectIsLocked" [selectedView]="selectedView"
      (changeSelectedView)="switchView($event)"
      [validationResultsCount]="saveRecordsResponse?.failedValidations?.length > 9 ? '9+' : saveRecordsResponse?.failedValidations?.length"
      [hasFailures]="saveRecordsResponse?.hasFailures" [calendarViewFlag]="calendarViewFlag"
      (showAgentChat)="onShowAgentChat()" [agentChatFlag]="agentChatFlag" [layouts]="layouts" [showColumnsOptions]="gridColumnsOptionFlag"
      (columnsOptionsChange)="onColumnsOptionsChange($event)"
      [ngClass]="{'preview-actions-fix' : isPreview}">
    </app-actions-menu>
    }
    @if (datastore && reportInfo){
    <div>
      <app-refine-records [datastoreId]="datastore.id" [reportId]="reportId" [dataViewId]="reportInfo.dataStoreViewName"
        [refinerFields]="refinerFields" [showRefinerPanel]="showRefinerPanel" [datastore]="datastore"
        (hideSliderPanel)="onHideSliderPanel($event)"></app-refine-records>
    </div>
    }

    @if (this.showUserParameter) {
    <app-user-param-filter [userParameters]="userParameters" [datastore]="datastore" [reportId]="reportInfo?.reportId"
      [dataViewId]="reportInfo?.dataStoreViewName" (userEnteredParamsSubmitted)="onUserEnteredParamsSubmitted()">
    </app-user-param-filter>
    }

    <p-accordion [ngStyle]="{display: showActionableGrid ? 'block' : 'none'}" multiple="true" class="accordion-nb">
      @if (this.visualizationFlag && this.tilesConfigs?.length > 0) {
      <p-accordionTab [selected]="true" header="Visualizations">
        <app-visualization-panel [tilesConfigs]="tilesConfigs"></app-visualization-panel>
      </p-accordionTab>
      }

      @if (this.gridChartsFlag && this.chartConfigs?.length > 0) {
      <p-accordionTab [selected]="true" header="Chart">
        <div class="p-3">
          <div class="grid">
            @for (chart of chartConfigs; track chart) {
            <app-charts-panel class="col-12 sm:col-6 flex-grow-1" [chartConfig]="chart" [gridApi]="agGrid.api">
            </app-charts-panel>
            }
          </div>
        </div>
      </p-accordionTab>
      }
    </p-accordion>

    <div class="px-2">
      @if (this.projectIsLocked) {
      <p-message severity="light" styleClass="w-full max-w-full" text="Project is Locked"></p-message>
      }
    </div>
    @if (!this.projectIsLocked && this.showActionableGrid) {
    <div>
      <ag-grid-angular #agGrid [rowData]="reportData" [columnDefs]="reportInfo?.columnDefs" [animateRows]="true"
        [style.--ag-value-change-value-highlight-background-color]="flashingCellColor" [gridOptions]="gridOptions"
        (columnRowGroupChanged)="onColumnRowGroupChanged($event)" (gridReady)="onGridReady($event)"
        [suppressRowClickSelection]="false" class="ag-display-size" [theme]="theme"
        [groupHideOpenParents]="true" id="agGrid"
        [rowSelection]="'single'" [rowHeight]="40" [headerHeight]="40" suppressMenuHide="true"
        (cellFocused)="onCellFocused($event)" [enableCharts]="true" [chartThemeOverrides]="chartThemeOverrides"
        [getContextMenuItems]="contextMenuItems" (firstDataRendered)="onFirstDataRendered($event)"
        [ngClass]="{'hidden': selectedView !== 'Table', 'preview-height-fix' : isPreview}">
      </ag-grid-angular>
      @if (this.selectedView !== 'Table' && this.calendarOptions) {
      <app-sb-full-calendar #sbFullCalendar class="max-h-24rem" [data]="calendarData" [options]="calendarOptions"
        [selectedView]="selectedView" (eventClick)="showFormEditor(true, $event)"
        (dataUpdated)="onCalendarDataUpdated($event)"></app-sb-full-calendar>
      }
    </div>
    }
  </div>
</p-panel>

<p-sidebar [(visible)]="showDataEntryForm" appendTo="body" blockScroll="true" position="right"
  (onHide)="onDataEntryFormHide()" styleClass="sidebar-sm">
  <ng-template pTemplate="header">
    <h4><i class="sb sb-icon-form"></i> {{jsonSchemaParam.title}}</h4>
  </ng-template>
  @if (this.showDataEntryForm) {
  <app-dynamic-form-body [dynamicForm]="formAsset?.targetObj" [(data)]="jsonSchemaParam.data" [projectId]='projectId'
    [projectVersionId]='projectVersionId' [datastore]="datastore" [recordId]="selectedRecordId"
    (formOnSubmit)="onDataEntryFormSubmit($event)" [showFormOptions]="false" submitBtnIcon="pi pi-save"
    [userPermissionLevel]="userPermissionLevel" [showSubmitButton]="true">
  </app-dynamic-form-body>
  }

</p-sidebar>


<p-sidebar [(visible)]="showAgentChatSidebar" appendTo="body" blockScroll="true" position="right"
  (onHide)="onHideAgentChat()" styleClass="agent-sidebar min-w-min py-0">
  <ng-template pTemplate="header">
    <h4 class="m-0"><i class="fa-regular fa-comment"></i> Chat</h4>
  </ng-template>
  <app-chat [assetId]="reportId" #chat></app-chat>
</p-sidebar>


@if (this.showConnectorScreen) {
<app-iframe-dialog [idFieldValue]="this.formAsset?.targetObj?.connectorIdFieldValue"
  [src]="this.formAsset?.targetObj?.iframeUrl" [title]="this.formAsset?.targetObj?.iframeTitle"
  [(visible)]="showConnectorScreen" (dialogClosed)="connectorAppClose()">
</app-iframe-dialog>
}

<app-data-entry-form-dialog [(visible)]="showFormlyDataEntryDialog" [style]="{width: '50rem'}"
  [dynamicForm]="formAsset?.targetObj" [datastore]="datastore" [recordId]="selectedRecordId"
  [projectId]="fileUploadProjectId" [projectVersionId]="fileUploadProjectVersionId"
  [userPermissionLevel]="userPermissionLevel"
  (afterSave)="loadReportData(slicerParams, true)"></app-data-entry-form-dialog>

@if (this.showEmailForm) {
<app-email-grid [reportInfo]="reportInfo" [gridApi]="agGrid?.api" [columnsToExport]="columnsToExport"
  (displayChange)="onEmailDialogClose($event)"></app-email-grid>
}

<app-confirmation-dialog id="actionable-grid"></app-confirmation-dialog>

@if (this.showValidationResults) {
<app-validation-results-dialog [saveRecordsResponse]="saveRecordsResponse" [(visible)]="showValidationResults"
  [data]="reportData" [datastore]="datastore">
</app-validation-results-dialog>
}

@if (this.isShowSpinner) {
<p-blockUI [blocked]="isShowSpinner">
  <img class="ui-progress-spinner" [src]="'layout/images/salt-box-loading.gif' | assetUrl">
</p-blockUI>
}