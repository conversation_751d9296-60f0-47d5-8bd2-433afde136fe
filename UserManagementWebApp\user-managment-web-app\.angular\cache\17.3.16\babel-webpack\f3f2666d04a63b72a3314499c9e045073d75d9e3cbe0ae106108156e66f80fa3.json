{"ast": null, "code": "import { parseUrl } from \"@aws-sdk/url-parser\";\nimport { defaultRegionInfoProvider } from \"./endpoints\";\nexport var getRuntimeConfig = function (config) {\n  var _a, _b, _c, _d, _e;\n  return {\n    apiVersion: \"2016-11-28\",\n    disableHostPrefix: (_a = config === null || config === void 0 ? void 0 : config.disableHostPrefix) !== null && _a !== void 0 ? _a : false,\n    logger: (_b = config === null || config === void 0 ? void 0 : config.logger) !== null && _b !== void 0 ? _b : {},\n    regionInfoProvider: (_c = config === null || config === void 0 ? void 0 : config.regionInfoProvider) !== null && _c !== void 0 ? _c : defaultRegionInfoProvider,\n    serviceId: (_d = config === null || config === void 0 ? void 0 : config.serviceId) !== null && _d !== void 0 ? _d : \"Lex Runtime Service\",\n    urlParser: (_e = config === null || config === void 0 ? void 0 : config.urlParser) !== null && _e !== void 0 ? _e : parseUrl\n  };\n};", "map": {"version": 3, "names": ["parseUrl", "defaultRegionInfoProvider", "getRuntimeConfig", "config", "_a", "_b", "_c", "_d", "_e", "apiVersion", "disableHostPrefix", "logger", "regionInfoProvider", "serviceId", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-lex-runtime-service/dist-es/runtimeConfig.shared.js"], "sourcesContent": ["import { parseUrl } from \"@aws-sdk/url-parser\";\nimport { defaultRegionInfoProvider } from \"./endpoints\";\nexport var getRuntimeConfig = function (config) {\n    var _a, _b, _c, _d, _e;\n    return ({\n        apiVersion: \"2016-11-28\",\n        disableHostPrefix: (_a = config === null || config === void 0 ? void 0 : config.disableHostPrefix) !== null && _a !== void 0 ? _a : false,\n        logger: (_b = config === null || config === void 0 ? void 0 : config.logger) !== null && _b !== void 0 ? _b : {},\n        regionInfoProvider: (_c = config === null || config === void 0 ? void 0 : config.regionInfoProvider) !== null && _c !== void 0 ? _c : defaultRegionInfoProvider,\n        serviceId: (_d = config === null || config === void 0 ? void 0 : config.serviceId) !== null && _d !== void 0 ? _d : \"Lex Runtime Service\",\n        urlParser: (_e = config === null || config === void 0 ? void 0 : config.urlParser) !== null && _e !== void 0 ? _e : parseUrl,\n    });\n};\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,qBAAqB;AAC9C,SAASC,yBAAyB,QAAQ,aAAa;AACvD,OAAO,IAAIC,gBAAgB,GAAG,SAAAA,CAAUC,MAAM,EAAE;EAC5C,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACtB,OAAQ;IACJC,UAAU,EAAE,YAAY;IACxBC,iBAAiB,EAAE,CAACN,EAAE,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACO,iBAAiB,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,KAAK;IACzIO,MAAM,EAAE,CAACN,EAAE,GAAGF,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACQ,MAAM,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IAChHO,kBAAkB,EAAE,CAACN,EAAE,GAAGH,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACS,kBAAkB,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGL,yBAAyB;IAC/JY,SAAS,EAAE,CAACN,EAAE,GAAGJ,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACU,SAAS,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,qBAAqB;IACzIO,SAAS,EAAE,CAACN,EAAE,GAAGL,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACW,SAAS,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGR;EACxH,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}