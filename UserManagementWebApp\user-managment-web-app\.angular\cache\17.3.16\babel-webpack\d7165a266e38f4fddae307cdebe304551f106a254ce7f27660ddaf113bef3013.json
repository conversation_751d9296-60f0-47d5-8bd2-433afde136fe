{"ast": null, "code": "import { SHA256_ALGORITHM_IDENTIFIER } from '../constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns a string to be signed.\n *\n * @param date Current date in the format 'YYYYMMDDThhmmssZ'.\n * @param credentialScope String representing the credential scope with format 'YYYYMMDD/region/service/aws4_request'.\n * @param hashedRequest Hashed canonical request.\n *\n * @returns A string created by by concatenating the following strings, separated by newline characters:\n * - Algorithm\n * - RequestDateTime\n * - CredentialScope\n * - HashedCanonicalRequest\n *\n * @internal\n */\nconst getStringToSign = (date, credentialScope, hashedRequest) => [SHA256_ALGORITHM_IDENTIFIER, date, credentialScope, hashedRequest].join('\\n');\nexport { getStringToSign };", "map": {"version": 3, "names": ["SHA256_ALGORITHM_IDENTIFIER", "getStringToSign", "date", "credentialScope", "hashedRequest", "join"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/signer/signatureV4/utils/getStringToSign.mjs"], "sourcesContent": ["import { SHA256_ALGORITHM_IDENTIFIER } from '../constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns a string to be signed.\n *\n * @param date Current date in the format 'YYYYMMDDThhmmssZ'.\n * @param credentialScope String representing the credential scope with format 'YYYYMMDD/region/service/aws4_request'.\n * @param hashedRequest Hashed canonical request.\n *\n * @returns A string created by by concatenating the following strings, separated by newline characters:\n * - Algorithm\n * - RequestDateTime\n * - CredentialScope\n * - HashedCanonicalRequest\n *\n * @internal\n */\nconst getStringToSign = (date, credentialScope, hashedRequest) => [SHA256_ALGORITHM_IDENTIFIER, date, credentialScope, hashedRequest].join('\\n');\n\nexport { getStringToSign };\n"], "mappings": "AAAA,SAASA,2BAA2B,QAAQ,kBAAkB;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGA,CAACC,IAAI,EAAEC,eAAe,EAAEC,aAAa,KAAK,CAACJ,2BAA2B,EAAEE,IAAI,EAAEC,eAAe,EAAEC,aAAa,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;AAEhJ,SAASJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}