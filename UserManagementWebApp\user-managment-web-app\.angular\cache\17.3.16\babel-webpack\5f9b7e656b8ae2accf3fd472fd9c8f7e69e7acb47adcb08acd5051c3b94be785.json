{"ast": null, "code": "const AuthTokenStorageKeys = {\n  accessToken: 'accessToken',\n  idToken: 'idToken',\n  oidcProvider: 'oidcProvider',\n  clockDrift: 'clockDrift',\n  refreshToken: 'refreshToken',\n  deviceKey: 'deviceKey',\n  randomPasswordKey: 'randomPasswordKey',\n  deviceGroupKey: 'deviceGroupKey',\n  signInDetails: 'signInDetails',\n  oauthMetadata: 'oauthMetadata'\n};\nexport { AuthTokenStorageKeys };", "map": {"version": 3, "names": ["AuthTokenStorageKeys", "accessToken", "idToken", "oidcProvider", "clockDrift", "refreshToken", "deviceKey", "randomPassword<PERSON>ey", "deviceGroupKey", "signInDetails", "oauthMetadata"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/tokenProvider/types.mjs"], "sourcesContent": ["const AuthTokenStorageKeys = {\n    accessToken: 'accessToken',\n    idToken: 'idToken',\n    oidcProvider: 'oidcProvider',\n    clockDrift: 'clockDrift',\n    refreshToken: 'refreshToken',\n    deviceKey: 'deviceKey',\n    randomPasswordKey: 'randomPasswordKey',\n    deviceGroupKey: 'deviceGroupKey',\n    signInDetails: 'signInDetails',\n    oauthMetadata: 'oauthMetadata',\n};\n\nexport { AuthTokenStorageKeys };\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EACzBC,WAAW,EAAE,aAAa;EAC1BC,OAAO,EAAE,SAAS;EAClBC,YAAY,EAAE,cAAc;EAC5BC,UAAU,EAAE,YAAY;EACxBC,YAAY,EAAE,cAAc;EAC5BC,SAAS,EAAE,WAAW;EACtBC,iBAAiB,EAAE,mBAAmB;EACtCC,cAAc,EAAE,gBAAgB;EAChCC,aAAa,EAAE,eAAe;EAC9BC,aAAa,EAAE;AACnB,CAAC;AAED,SAASV,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}