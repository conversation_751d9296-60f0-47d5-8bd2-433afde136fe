{"ast": null, "code": "import { ConsoleLogger } from '@aws-amplify/core';\nimport { AuthErrorStrings } from './common/AuthErrorStrings.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// TODO: delete this module when the Auth class is removed.\nconst logger = new ConsoleLogger('AuthError');\nclass AuthError extends Error {\n  constructor(type) {\n    const {\n      message,\n      log\n    } = authErrorMessages[type];\n    super(message);\n    // Hack for making the custom error class work when transpiled to es5\n    // TODO: Delete the following 2 lines after we change the build target to >= es2015\n    this.constructor = AuthError;\n    Object.setPrototypeOf(this, AuthError.prototype);\n    this.name = 'AuthError';\n    this.log = log || message;\n    logger.error(this.log);\n  }\n}\nclass NoUserPoolError extends AuthError {\n  constructor(type) {\n    super(type);\n    // Hack for making the custom error class work when transpiled to es5\n    // TODO: Delete the following 2 lines after we change the build target to >= es2015\n    this.constructor = NoUserPoolError;\n    Object.setPrototypeOf(this, NoUserPoolError.prototype);\n    this.name = 'NoUserPoolError';\n  }\n}\nconst authErrorMessages = {\n  oauthSignInError: {\n    message: AuthErrorStrings.OAUTH_ERROR,\n    log: 'Make sure Cognito Hosted UI has been configured correctly'\n  },\n  noConfig: {\n    message: AuthErrorStrings.DEFAULT_MSG,\n    log: `\n            Error: Amplify has not been configured correctly.\n            This error is typically caused by one of the following scenarios:\n\n            1. Make sure you're passing the awsconfig object to Amplify.configure() in your app's entry point\n                See https://aws-amplify.github.io/docs/js/authentication#configure-your-app for more information\n            \n            2. There might be multiple conflicting versions of amplify packages in your node_modules.\n\t\t\t\tRefer to our docs site for help upgrading Amplify packages (https://docs.amplify.aws/lib/troubleshooting/upgrading/q/platform/js)\n        `\n  },\n  missingAuthConfig: {\n    message: AuthErrorStrings.DEFAULT_MSG,\n    log: `\n            Error: Amplify has not been configured correctly. \n            The configuration object is missing required auth properties.\n            This error is typically caused by one of the following scenarios:\n\n            1. Did you run \\`amplify push\\` after adding auth via \\`amplify add auth\\`?\n                See https://aws-amplify.github.io/docs/js/authentication#amplify-project-setup for more information\n\n            2. This could also be caused by multiple conflicting versions of amplify packages, see (https://docs.amplify.aws/lib/troubleshooting/upgrading/q/platform/js) for help upgrading Amplify packages.\n        `\n  },\n  emptyUsername: {\n    message: AuthErrorStrings.EMPTY_USERNAME\n  },\n  // TODO: should include a list of valid sign-in types\n  invalidUsername: {\n    message: AuthErrorStrings.INVALID_USERNAME\n  },\n  emptyPassword: {\n    message: AuthErrorStrings.EMPTY_PASSWORD\n  },\n  emptyCode: {\n    message: AuthErrorStrings.EMPTY_CODE\n  },\n  signUpError: {\n    message: AuthErrorStrings.SIGN_UP_ERROR,\n    log: 'The first parameter should either be non-null string or object'\n  },\n  noMFA: {\n    message: AuthErrorStrings.NO_MFA\n  },\n  invalidMFA: {\n    message: AuthErrorStrings.INVALID_MFA\n  },\n  emptyChallengeResponse: {\n    message: AuthErrorStrings.EMPTY_CHALLENGE\n  },\n  noUserSession: {\n    message: AuthErrorStrings.NO_USER_SESSION\n  },\n  deviceConfig: {\n    message: AuthErrorStrings.DEVICE_CONFIG\n  },\n  networkError: {\n    message: AuthErrorStrings.NETWORK_ERROR\n  },\n  autoSignInError: {\n    message: AuthErrorStrings.AUTOSIGNIN_ERROR\n  },\n  default: {\n    message: AuthErrorStrings.DEFAULT_MSG\n  }\n};\nexport { AuthError, NoUserPoolError, authErrorMessages };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AuthErrorStrings", "logger", "<PERSON>th<PERSON><PERSON><PERSON>", "Error", "constructor", "type", "message", "log", "authErrorMessages", "Object", "setPrototypeOf", "prototype", "name", "error", "NoUserPoolError", "oauthSignInError", "OAUTH_ERROR", "noConfig", "DEFAULT_MSG", "missingAuthConfig", "emptyUsername", "EMPTY_USERNAME", "invalidUsername", "INVALID_USERNAME", "emptyPassword", "EMPTY_PASSWORD", "emptyCode", "EMPTY_CODE", "signUpError", "SIGN_UP_ERROR", "noMFA", "NO_MFA", "invalidMFA", "INVALID_MFA", "emptyChallengeResponse", "EMPTY_CHALLENGE", "noUserSession", "NO_USER_SESSION", "deviceConfig", "DEVICE_CONFIG", "networkError", "NETWORK_ERROR", "autoSignInError", "AUTOSIGNIN_ERROR", "default"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/Errors.mjs"], "sourcesContent": ["import { ConsoleLogger } from '@aws-amplify/core';\nimport { AuthErrorStrings } from './common/AuthErrorStrings.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// TODO: delete this module when the Auth class is removed.\nconst logger = new ConsoleLogger('AuthError');\nclass AuthError extends Error {\n    constructor(type) {\n        const { message, log } = authErrorMessages[type];\n        super(message);\n        // Hack for making the custom error class work when transpiled to es5\n        // TODO: Delete the following 2 lines after we change the build target to >= es2015\n        this.constructor = AuthError;\n        Object.setPrototypeOf(this, AuthError.prototype);\n        this.name = 'AuthError';\n        this.log = log || message;\n        logger.error(this.log);\n    }\n}\nclass NoUserPoolError extends AuthError {\n    constructor(type) {\n        super(type);\n        // Hack for making the custom error class work when transpiled to es5\n        // TODO: Delete the following 2 lines after we change the build target to >= es2015\n        this.constructor = NoUserPoolError;\n        Object.setPrototypeOf(this, NoUserPoolError.prototype);\n        this.name = 'NoUserPoolError';\n    }\n}\nconst authErrorMessages = {\n    oauthSignInError: {\n        message: AuthErrorStrings.OAUTH_ERROR,\n        log: 'Make sure Cognito Hosted UI has been configured correctly',\n    },\n    noConfig: {\n        message: AuthErrorStrings.DEFAULT_MSG,\n        log: `\n            Error: Amplify has not been configured correctly.\n            This error is typically caused by one of the following scenarios:\n\n            1. Make sure you're passing the awsconfig object to Amplify.configure() in your app's entry point\n                See https://aws-amplify.github.io/docs/js/authentication#configure-your-app for more information\n            \n            2. There might be multiple conflicting versions of amplify packages in your node_modules.\n\t\t\t\tRefer to our docs site for help upgrading Amplify packages (https://docs.amplify.aws/lib/troubleshooting/upgrading/q/platform/js)\n        `,\n    },\n    missingAuthConfig: {\n        message: AuthErrorStrings.DEFAULT_MSG,\n        log: `\n            Error: Amplify has not been configured correctly. \n            The configuration object is missing required auth properties.\n            This error is typically caused by one of the following scenarios:\n\n            1. Did you run \\`amplify push\\` after adding auth via \\`amplify add auth\\`?\n                See https://aws-amplify.github.io/docs/js/authentication#amplify-project-setup for more information\n\n            2. This could also be caused by multiple conflicting versions of amplify packages, see (https://docs.amplify.aws/lib/troubleshooting/upgrading/q/platform/js) for help upgrading Amplify packages.\n        `,\n    },\n    emptyUsername: {\n        message: AuthErrorStrings.EMPTY_USERNAME,\n    },\n    // TODO: should include a list of valid sign-in types\n    invalidUsername: {\n        message: AuthErrorStrings.INVALID_USERNAME,\n    },\n    emptyPassword: {\n        message: AuthErrorStrings.EMPTY_PASSWORD,\n    },\n    emptyCode: {\n        message: AuthErrorStrings.EMPTY_CODE,\n    },\n    signUpError: {\n        message: AuthErrorStrings.SIGN_UP_ERROR,\n        log: 'The first parameter should either be non-null string or object',\n    },\n    noMFA: {\n        message: AuthErrorStrings.NO_MFA,\n    },\n    invalidMFA: {\n        message: AuthErrorStrings.INVALID_MFA,\n    },\n    emptyChallengeResponse: {\n        message: AuthErrorStrings.EMPTY_CHALLENGE,\n    },\n    noUserSession: {\n        message: AuthErrorStrings.NO_USER_SESSION,\n    },\n    deviceConfig: {\n        message: AuthErrorStrings.DEVICE_CONFIG,\n    },\n    networkError: {\n        message: AuthErrorStrings.NETWORK_ERROR,\n    },\n    autoSignInError: {\n        message: AuthErrorStrings.AUTOSIGNIN_ERROR,\n    },\n    default: {\n        message: AuthErrorStrings.DEFAULT_MSG,\n    },\n};\n\nexport { AuthError, NoUserPoolError, authErrorMessages };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,mBAAmB;AACjD,SAASC,gBAAgB,QAAQ,+BAA+B;;AAEhE;AACA;AACA;AACA,MAAMC,MAAM,GAAG,IAAIF,aAAa,CAAC,WAAW,CAAC;AAC7C,MAAMG,SAAS,SAASC,KAAK,CAAC;EAC1BC,WAAWA,CAACC,IAAI,EAAE;IACd,MAAM;MAAEC,OAAO;MAAEC;IAAI,CAAC,GAAGC,iBAAiB,CAACH,IAAI,CAAC;IAChD,KAAK,CAACC,OAAO,CAAC;IACd;IACA;IACA,IAAI,CAACF,WAAW,GAAGF,SAAS;IAC5BO,MAAM,CAACC,cAAc,CAAC,IAAI,EAAER,SAAS,CAACS,SAAS,CAAC;IAChD,IAAI,CAACC,IAAI,GAAG,WAAW;IACvB,IAAI,CAACL,GAAG,GAAGA,GAAG,IAAID,OAAO;IACzBL,MAAM,CAACY,KAAK,CAAC,IAAI,CAACN,GAAG,CAAC;EAC1B;AACJ;AACA,MAAMO,eAAe,SAASZ,SAAS,CAAC;EACpCE,WAAWA,CAACC,IAAI,EAAE;IACd,KAAK,CAACA,IAAI,CAAC;IACX;IACA;IACA,IAAI,CAACD,WAAW,GAAGU,eAAe;IAClCL,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEI,eAAe,CAACH,SAAS,CAAC;IACtD,IAAI,CAACC,IAAI,GAAG,iBAAiB;EACjC;AACJ;AACA,MAAMJ,iBAAiB,GAAG;EACtBO,gBAAgB,EAAE;IACdT,OAAO,EAAEN,gBAAgB,CAACgB,WAAW;IACrCT,GAAG,EAAE;EACT,CAAC;EACDU,QAAQ,EAAE;IACNX,OAAO,EAAEN,gBAAgB,CAACkB,WAAW;IACrCX,GAAG,EAAE;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,CAAC;EACDY,iBAAiB,EAAE;IACfb,OAAO,EAAEN,gBAAgB,CAACkB,WAAW;IACrCX,GAAG,EAAE;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,CAAC;EACDa,aAAa,EAAE;IACXd,OAAO,EAAEN,gBAAgB,CAACqB;EAC9B,CAAC;EACD;EACAC,eAAe,EAAE;IACbhB,OAAO,EAAEN,gBAAgB,CAACuB;EAC9B,CAAC;EACDC,aAAa,EAAE;IACXlB,OAAO,EAAEN,gBAAgB,CAACyB;EAC9B,CAAC;EACDC,SAAS,EAAE;IACPpB,OAAO,EAAEN,gBAAgB,CAAC2B;EAC9B,CAAC;EACDC,WAAW,EAAE;IACTtB,OAAO,EAAEN,gBAAgB,CAAC6B,aAAa;IACvCtB,GAAG,EAAE;EACT,CAAC;EACDuB,KAAK,EAAE;IACHxB,OAAO,EAAEN,gBAAgB,CAAC+B;EAC9B,CAAC;EACDC,UAAU,EAAE;IACR1B,OAAO,EAAEN,gBAAgB,CAACiC;EAC9B,CAAC;EACDC,sBAAsB,EAAE;IACpB5B,OAAO,EAAEN,gBAAgB,CAACmC;EAC9B,CAAC;EACDC,aAAa,EAAE;IACX9B,OAAO,EAAEN,gBAAgB,CAACqC;EAC9B,CAAC;EACDC,YAAY,EAAE;IACVhC,OAAO,EAAEN,gBAAgB,CAACuC;EAC9B,CAAC;EACDC,YAAY,EAAE;IACVlC,OAAO,EAAEN,gBAAgB,CAACyC;EAC9B,CAAC;EACDC,eAAe,EAAE;IACbpC,OAAO,EAAEN,gBAAgB,CAAC2C;EAC9B,CAAC;EACDC,OAAO,EAAE;IACLtC,OAAO,EAAEN,gBAAgB,CAACkB;EAC9B;AACJ,CAAC;AAED,SAAShB,SAAS,EAAEY,eAAe,EAAEN,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}