{"ast": null, "code": "const idDict = {\n  'Account recovery requires verified contact information': 'Pemulihan akun memerlukan informasi kontak terverifikasi',\n  'Authenticator App (TOTP)': 'Aplikasi Pengauntentikasi (TOTP)',\n  'Back to Sign In': '<PERSON><PERSON><PERSON> ke Masuk',\n  'Change Password': 'Ubah kata sandi',\n  Changing: 'Mengu<PERSON>',\n  Code: 'Kode',\n  'Confirm Email Code': 'Konfirmasi Kode Email',\n  'Confirm Password': 'Konfirmasi kata sandi',\n  'Confirm Sign Up': 'Konfirmasi Pendaftaran',\n  'Confirm SMS Code': 'Konfirmasi Kode SMS',\n  'Confirm TOTP Code': 'Konfirmasi Kode TOTP',\n  Confirm: 'Konfirmasi',\n  'Confirmation Code': 'Kode Konfirmasi',\n  Confirming: 'Mengkonfirmasi',\n  'Create a new account': 'Buat akun baru',\n  'Create Account': 'Buat Akun',\n  'Creating Account': 'Membuat Akun',\n  'Dismiss alert': 'Hentikan pemberitahuan',\n  Email: 'Email',\n  'Email Message': 'Pesan Email',\n  'Enter your code': 'Masukkan kode anda',\n  'Enter your Email': 'Masukkan email anda',\n  'Enter your phone number': 'Masukkan nomor telepon anda',\n  'Enter your username': 'Masukkan nama akun anda',\n  'Forgot your password?': 'Lupa kata sandi? ',\n  'Hide password': 'Sembunyikan kata sandi',\n  'It may take a minute to arrive': 'Mungkin perlu waktu satu menit untuk tiba',\n  Loading: 'Memuat',\n  'Multi-Factor Authentication': 'Autentikasi Multifaktor',\n  'Multi-Factor Authentication Setup': 'Pengaturan Autentikasi Multifaktor',\n  'New password': 'Kata sandi baru',\n  or: 'atau',\n  Password: 'Kata sandi',\n  'Phone Number': 'Nomor telepon',\n  'Resend Code': 'Kirim ulang kodenya',\n  'Reset your Password': 'Reset Kata Sandi',\n  'Reset your password': 'Ubah kata sandi anda',\n  'Select MFA Type': 'Pilih tipe MFA',\n  'Send code': 'Kirim kode',\n  'Send Code': 'Kirim Kode',\n  Sending: 'Mengirim',\n  'Setup Email': 'Atur Email',\n  'Setup TOTP': 'Siapkan TOTP',\n  'Show password': 'Tampilkan kata sandi',\n  'Sign in to your account': 'Masuk akun anda',\n  'Sign In with Amazon': 'Masuk dengan Amazon',\n  'Sign In with Apple': 'Masuk dengan Apple',\n  'Sign In with Facebook': 'Masuk dengan Facebook',\n  'Sign In with Google': 'Masuk dengan Google',\n  'Sign in': 'Masuk',\n  'Sign In': 'Masuk',\n  'Signing in': 'Memasuki',\n  Skip: 'Lewati',\n  Submit: 'Ajukan',\n  Submitting: 'Mengajukan',\n  'Text Message (SMS)': 'Pesan Teks (SMS)',\n  Username: 'Nama akun',\n  'Verify Contact': 'Verifikasi Kontak',\n  Verify: 'Verifikasi',\n  'We Sent A Code': 'Kami Mengirim Kode',\n  'We Texted You': 'Kami mengirim SMS kepada Anda',\n  'Your code is on the way. To log in, enter the code we sent you': 'Kode Anda segera hadir. Untuk masuk, masukkan kode yang kami kirimkan kepada Anda',\n  // Additional translations provided by customers\n  'An account with the given email already exists.': 'Akun dengan email tersebut sudah terdaftar.',\n  'Attempt limit exceeded, please try after some time.': 'Batas percobaan terlampaui, mohon coba lagi setelah beberapa waktu.',\n  'Cannot reset password for the user as there is no registered/verified email or phone_number': 'Tidak dapat mengatur ulang kata sandi karena tidak ada email terdaftar / terverifikasi atau nomor telepon',\n  Change: 'Ubah',\n  'Confirm a Code': 'Konfirmasi kode',\n  'Create account': 'Buat akun',\n  'Enter your password': 'Masukkan kata sandi anda',\n  'Forgot Password': 'Lupa kata sandi',\n  'Have an account? ': 'Sudah punya akun? ',\n  Hello: 'Halo',\n  'Incorrect username or password.': 'Nama akun atau kata sandi salah.',\n  'Invalid phone number format': 'Nomor telepon tidak sesuai dengan format.',\n  'Invalid verification code provided, please try again.': 'Kode verifikasi tidak sesuai, mohon coba lagi.',\n  'It may take a minute to arrive.': 'Mungkin perlu beberapa waktu untuk tiba.',\n  'Lost your code? ': 'Kode anda hilang?',\n  Name: 'Nama',\n  'Network error': 'Galat jaringan',\n  'No account? ': 'Tidak ada akun?',\n  'Password did not conform with policy: Password not long enough': 'Kata sandi tidak sesuai dengan aturan: Kata sandi kurang panjang',\n  'Resend a Code': 'Renvoyer un code',\n  'Reset password': 'Ubah kata sandi anda',\n  Send: 'Kirim',\n  'Sign In with AWS': 'Masuk dengan AWS',\n  'Sign Up with Amazon': 'Daftar dengan Amazon',\n  'Sign Up with AWS': 'Daftar dengan AWS',\n  'Sign Up with Facebook': 'Daftar dengan Facebook',\n  'Sign Up with Google': 'Daftar dengan Google',\n  SMS: 'SMS',\n  'User already exists': 'Akun sudah terdaftar',\n  'User does not exist.': 'Akun tidak terdaftar.',\n  'User is disabled.': 'Akun dinonaktifkan.',\n  'Username cannot be empty': 'Nama akun tidak boleh kosong',\n  'Username/client id combination not found.': 'Nama akun atau id tidak ditemukan.',\n  'We Emailed You': 'Kami mengirimkanmu email',\n  'Your code is on the way. To log in, enter the code we emailed to': 'Kode anda dalam pengiriman. Untuk masuk, masukkan kode yang kami emailkan ke',\n  'Your code is on the way. To log in, enter the code we texted to': 'Kode anda dalam pengiriman. Untuk masuk, masukkan kode yang kami tuliskan ke',\n  'Your passwords must match': 'Kata sandi harus sama'\n};\nexport { idDict };", "map": {"version": 3, "names": ["idDict", "Changing", "Code", "Confirm", "Confirming", "Email", "Loading", "or", "Password", "Sending", "<PERSON><PERSON>", "Submit", "Submitting", "Username", "Verify", "Change", "Hello", "Name", "Send", "SMS"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/i18n/dictionaries/authenticator/id.mjs"], "sourcesContent": ["const idDict = {\n    'Account recovery requires verified contact information': 'Pemulihan akun memerlukan informasi kontak terverifikasi',\n    'Authenticator App (TOTP)': 'Aplikasi Pengauntentikasi (TOTP)',\n    'Back to Sign In': '<PERSON><PERSON><PERSON> ke Masuk',\n    'Change Password': 'Ubah kata sandi',\n    Changing: 'Mengu<PERSON>',\n    Code: 'Kode',\n    'Confirm Email Code': 'Konfirmasi Kode Email',\n    'Confirm Password': 'Konfirmasi kata sandi',\n    'Confirm Sign Up': 'Konfirmasi Pendaftaran',\n    'Confirm SMS Code': 'Konfirmasi Kode SMS',\n    'Confirm TOTP Code': 'Konfirmasi Kode TOTP',\n    Confirm: 'Konfirmasi',\n    'Confirmation Code': 'Kode Konfirmasi',\n    Confirming: 'Mengkonfirmasi',\n    'Create a new account': 'Buat akun baru',\n    'Create Account': 'Buat Akun',\n    'Creating Account': 'Membuat Akun',\n    'Dismiss alert': 'Hentikan pemberitahuan',\n    Email: 'Email',\n    'Email Message': 'Pesan Email',\n    'Enter your code': 'Masukkan kode anda',\n    'Enter your Email': 'Masukkan email anda',\n    'Enter your phone number': 'Masukkan nomor telepon anda',\n    'Enter your username': 'Masukkan nama akun anda',\n    'Forgot your password?': 'Lupa kata sandi? ',\n    'Hide password': 'Sembunyikan kata sandi',\n    'It may take a minute to arrive': 'Mungkin perlu waktu satu menit untuk tiba',\n    Loading: 'Memuat',\n    'Multi-Factor Authentication': 'Autentikasi Multifaktor',\n    'Multi-Factor Authentication Setup': 'Pengaturan Autentikasi Multifaktor',\n    'New password': 'Kata sandi baru',\n    or: 'atau',\n    Password: 'Kata sandi',\n    'Phone Number': 'Nomor telepon',\n    'Resend Code': 'Kirim ulang kodenya',\n    'Reset your Password': 'Reset Kata Sandi',\n    'Reset your password': 'Ubah kata sandi anda',\n    'Select MFA Type': 'Pilih tipe MFA',\n    'Send code': 'Kirim kode',\n    'Send Code': 'Kirim Kode',\n    Sending: 'Mengirim',\n    'Setup Email': 'Atur Email',\n    'Setup TOTP': 'Siapkan TOTP',\n    'Show password': 'Tampilkan kata sandi',\n    'Sign in to your account': 'Masuk akun anda',\n    'Sign In with Amazon': 'Masuk dengan Amazon',\n    'Sign In with Apple': 'Masuk dengan Apple',\n    'Sign In with Facebook': 'Masuk dengan Facebook',\n    'Sign In with Google': 'Masuk dengan Google',\n    'Sign in': 'Masuk',\n    'Sign In': 'Masuk',\n    'Signing in': 'Memasuki',\n    Skip: 'Lewati',\n    Submit: 'Ajukan',\n    Submitting: 'Mengajukan',\n    'Text Message (SMS)': 'Pesan Teks (SMS)',\n    Username: 'Nama akun',\n    'Verify Contact': 'Verifikasi Kontak',\n    Verify: 'Verifikasi',\n    'We Sent A Code': 'Kami Mengirim Kode',\n    'We Texted You': 'Kami mengirim SMS kepada Anda',\n    'Your code is on the way. To log in, enter the code we sent you': 'Kode Anda segera hadir. Untuk masuk, masukkan kode yang kami kirimkan kepada Anda',\n    // Additional translations provided by customers\n    'An account with the given email already exists.': 'Akun dengan email tersebut sudah terdaftar.',\n    'Attempt limit exceeded, please try after some time.': 'Batas percobaan terlampaui, mohon coba lagi setelah beberapa waktu.',\n    'Cannot reset password for the user as there is no registered/verified email or phone_number': 'Tidak dapat mengatur ulang kata sandi karena tidak ada email terdaftar / terverifikasi atau nomor telepon',\n    Change: 'Ubah',\n    'Confirm a Code': 'Konfirmasi kode',\n    'Create account': 'Buat akun',\n    'Enter your password': 'Masukkan kata sandi anda',\n    'Forgot Password': 'Lupa kata sandi',\n    'Have an account? ': 'Sudah punya akun? ',\n    Hello: 'Halo',\n    'Incorrect username or password.': 'Nama akun atau kata sandi salah.',\n    'Invalid phone number format': 'Nomor telepon tidak sesuai dengan format.',\n    'Invalid verification code provided, please try again.': 'Kode verifikasi tidak sesuai, mohon coba lagi.',\n    'It may take a minute to arrive.': 'Mungkin perlu beberapa waktu untuk tiba.',\n    'Lost your code? ': 'Kode anda hilang?',\n    Name: 'Nama',\n    'Network error': 'Galat jaringan',\n    'No account? ': 'Tidak ada akun?',\n    'Password did not conform with policy: Password not long enough': 'Kata sandi tidak sesuai dengan aturan: Kata sandi kurang panjang',\n    'Resend a Code': 'Renvoyer un code',\n    'Reset password': 'Ubah kata sandi anda',\n    Send: 'Kirim',\n    'Sign In with AWS': 'Masuk dengan AWS',\n    'Sign Up with Amazon': 'Daftar dengan Amazon',\n    'Sign Up with AWS': 'Daftar dengan AWS',\n    'Sign Up with Facebook': 'Daftar dengan Facebook',\n    'Sign Up with Google': 'Daftar dengan Google',\n    SMS: 'SMS',\n    'User already exists': 'Akun sudah terdaftar',\n    'User does not exist.': 'Akun tidak terdaftar.',\n    'User is disabled.': 'Akun dinonaktifkan.',\n    'Username cannot be empty': 'Nama akun tidak boleh kosong',\n    'Username/client id combination not found.': 'Nama akun atau id tidak ditemukan.',\n    'We Emailed You': 'Kami mengirimkanmu email',\n    'Your code is on the way. To log in, enter the code we emailed to': 'Kode anda dalam pengiriman. Untuk masuk, masukkan kode yang kami emailkan ke',\n    'Your code is on the way. To log in, enter the code we texted to': 'Kode anda dalam pengiriman. Untuk masuk, masukkan kode yang kami tuliskan ke',\n    'Your passwords must match': 'Kata sandi harus sama',\n};\n\nexport { idDict };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACX,wDAAwD,EAAE,0DAA0D;EACpH,0BAA0B,EAAE,kCAAkC;EAC9D,iBAAiB,EAAE,kBAAkB;EACrC,iBAAiB,EAAE,iBAAiB;EACpCC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,MAAM;EACZ,oBAAoB,EAAE,uBAAuB;EAC7C,kBAAkB,EAAE,uBAAuB;EAC3C,iBAAiB,EAAE,wBAAwB;EAC3C,kBAAkB,EAAE,qBAAqB;EACzC,mBAAmB,EAAE,sBAAsB;EAC3CC,OAAO,EAAE,YAAY;EACrB,mBAAmB,EAAE,iBAAiB;EACtCC,UAAU,EAAE,gBAAgB;EAC5B,sBAAsB,EAAE,gBAAgB;EACxC,gBAAgB,EAAE,WAAW;EAC7B,kBAAkB,EAAE,cAAc;EAClC,eAAe,EAAE,wBAAwB;EACzCC,KAAK,EAAE,OAAO;EACd,eAAe,EAAE,aAAa;EAC9B,iBAAiB,EAAE,oBAAoB;EACvC,kBAAkB,EAAE,qBAAqB;EACzC,yBAAyB,EAAE,6BAA6B;EACxD,qBAAqB,EAAE,yBAAyB;EAChD,uBAAuB,EAAE,mBAAmB;EAC5C,eAAe,EAAE,wBAAwB;EACzC,gCAAgC,EAAE,2CAA2C;EAC7EC,OAAO,EAAE,QAAQ;EACjB,6BAA6B,EAAE,yBAAyB;EACxD,mCAAmC,EAAE,oCAAoC;EACzE,cAAc,EAAE,iBAAiB;EACjCC,EAAE,EAAE,MAAM;EACVC,QAAQ,EAAE,YAAY;EACtB,cAAc,EAAE,eAAe;EAC/B,aAAa,EAAE,qBAAqB;EACpC,qBAAqB,EAAE,kBAAkB;EACzC,qBAAqB,EAAE,sBAAsB;EAC7C,iBAAiB,EAAE,gBAAgB;EACnC,WAAW,EAAE,YAAY;EACzB,WAAW,EAAE,YAAY;EACzBC,OAAO,EAAE,UAAU;EACnB,aAAa,EAAE,YAAY;EAC3B,YAAY,EAAE,cAAc;EAC5B,eAAe,EAAE,sBAAsB;EACvC,yBAAyB,EAAE,iBAAiB;EAC5C,qBAAqB,EAAE,qBAAqB;EAC5C,oBAAoB,EAAE,oBAAoB;EAC1C,uBAAuB,EAAE,uBAAuB;EAChD,qBAAqB,EAAE,qBAAqB;EAC5C,SAAS,EAAE,OAAO;EAClB,SAAS,EAAE,OAAO;EAClB,YAAY,EAAE,UAAU;EACxBC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,YAAY;EACxB,oBAAoB,EAAE,kBAAkB;EACxCC,QAAQ,EAAE,WAAW;EACrB,gBAAgB,EAAE,mBAAmB;EACrCC,MAAM,EAAE,YAAY;EACpB,gBAAgB,EAAE,oBAAoB;EACtC,eAAe,EAAE,+BAA+B;EAChD,gEAAgE,EAAE,mFAAmF;EACrJ;EACA,iDAAiD,EAAE,6CAA6C;EAChG,qDAAqD,EAAE,qEAAqE;EAC5H,6FAA6F,EAAE,2GAA2G;EAC1MC,MAAM,EAAE,MAAM;EACd,gBAAgB,EAAE,iBAAiB;EACnC,gBAAgB,EAAE,WAAW;EAC7B,qBAAqB,EAAE,0BAA0B;EACjD,iBAAiB,EAAE,iBAAiB;EACpC,mBAAmB,EAAE,oBAAoB;EACzCC,KAAK,EAAE,MAAM;EACb,iCAAiC,EAAE,kCAAkC;EACrE,6BAA6B,EAAE,2CAA2C;EAC1E,uDAAuD,EAAE,gDAAgD;EACzG,iCAAiC,EAAE,0CAA0C;EAC7E,kBAAkB,EAAE,mBAAmB;EACvCC,IAAI,EAAE,MAAM;EACZ,eAAe,EAAE,gBAAgB;EACjC,cAAc,EAAE,iBAAiB;EACjC,gEAAgE,EAAE,kEAAkE;EACpI,eAAe,EAAE,kBAAkB;EACnC,gBAAgB,EAAE,sBAAsB;EACxCC,IAAI,EAAE,OAAO;EACb,kBAAkB,EAAE,kBAAkB;EACtC,qBAAqB,EAAE,sBAAsB;EAC7C,kBAAkB,EAAE,mBAAmB;EACvC,uBAAuB,EAAE,wBAAwB;EACjD,qBAAqB,EAAE,sBAAsB;EAC7CC,GAAG,EAAE,KAAK;EACV,qBAAqB,EAAE,sBAAsB;EAC7C,sBAAsB,EAAE,uBAAuB;EAC/C,mBAAmB,EAAE,qBAAqB;EAC1C,0BAA0B,EAAE,8BAA8B;EAC1D,2CAA2C,EAAE,oCAAoC;EACjF,gBAAgB,EAAE,0BAA0B;EAC5C,kEAAkE,EAAE,8EAA8E;EAClJ,iEAAiE,EAAE,8EAA8E;EACjJ,2BAA2B,EAAE;AACjC,CAAC;AAED,SAASnB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}