{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ConsoleLogger, getCredentialsForIdentity } from '@aws-amplify/core';\nimport { assertIdentityPoolIdConfig } from '@aws-amplify/core/internals/utils';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { getRegionFromIdentityPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertIdTokenInAuthTokens } from '../utils/types.mjs';\nimport { cognitoIdentityIdProvider } from './IdentityIdProvider.mjs';\nimport { formLoginsMap } from './utils.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('CognitoCredentialsProvider');\nconst CREDENTIALS_TTL = 50 * 60 * 1000; // 50 min, can be modified on config if required in the future\nclass CognitoAWSCredentialsAndIdentityIdProvider {\n  constructor(identityIdStore) {\n    this._nextCredentialsRefresh = 0;\n    this._identityIdStore = identityIdStore;\n  }\n  clearCredentialsAndIdentityId() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      logger.debug('Clearing out credentials and identityId');\n      _this._credentialsAndIdentityId = undefined;\n      yield _this._identityIdStore.clearIdentityId();\n    })();\n  }\n  clearCredentials() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      logger.debug('Clearing out in-memory credentials');\n      _this2._credentialsAndIdentityId = undefined;\n    })();\n  }\n  getCredentialsAndIdentityId(getCredentialsOptions) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const isAuthenticated = getCredentialsOptions.authenticated;\n      const {\n        tokens\n      } = getCredentialsOptions;\n      const {\n        authConfig\n      } = getCredentialsOptions;\n      try {\n        assertIdentityPoolIdConfig(authConfig?.Cognito);\n      } catch {\n        // No identity pool configured, skipping\n        return;\n      }\n      if (!isAuthenticated && !authConfig.Cognito.allowGuestAccess) {\n        // TODO(V6): return partial result like Native platforms\n        return;\n      }\n      const {\n        forceRefresh\n      } = getCredentialsOptions;\n      const tokenHasChanged = _this3.hasTokenChanged(tokens);\n      const identityId = yield cognitoIdentityIdProvider({\n        tokens,\n        authConfig: authConfig.Cognito,\n        identityIdStore: _this3._identityIdStore\n      });\n      // Clear cached credentials when forceRefresh is true OR the cache token has changed\n      if (forceRefresh || tokenHasChanged) {\n        _this3.clearCredentials();\n      }\n      if (!isAuthenticated) {\n        return _this3.getGuestCredentials(identityId, authConfig.Cognito);\n      } else {\n        assertIdTokenInAuthTokens(tokens);\n        return _this3.credsForOIDCTokens(authConfig.Cognito, tokens, identityId);\n      }\n    })();\n  }\n  getGuestCredentials(identityId, authConfig) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      // Return existing in-memory cached credentials only if it exists, is not past it's lifetime and is unauthenticated credentials\n      if (_this4._credentialsAndIdentityId && !_this4.isPastTTL() && _this4._credentialsAndIdentityId.isAuthenticatedCreds === false) {\n        logger.info('returning stored credentials as they neither past TTL nor expired.');\n        return _this4._credentialsAndIdentityId;\n      }\n      // Clear to discard if any authenticated credentials are set and start with a clean slate\n      _this4.clearCredentials();\n      const region = getRegionFromIdentityPoolId(authConfig.identityPoolId);\n      // use identityId to obtain guest credentials\n      // save credentials in-memory\n      // No logins params should be passed for guest creds:\n      // https://docs.aws.amazon.com/cognitoidentity/latest/APIReference/API_GetCredentialsForIdentity.html\n      let clientResult;\n      try {\n        clientResult = yield getCredentialsForIdentity({\n          region\n        }, {\n          IdentityId: identityId\n        });\n      } catch (e) {\n        assertServiceError(e);\n        throw new AuthError(e);\n      }\n      if (clientResult?.Credentials?.AccessKeyId && clientResult?.Credentials?.SecretKey) {\n        _this4._nextCredentialsRefresh = new Date().getTime() + CREDENTIALS_TTL;\n        const res = {\n          credentials: {\n            accessKeyId: clientResult.Credentials.AccessKeyId,\n            secretAccessKey: clientResult.Credentials.SecretKey,\n            sessionToken: clientResult.Credentials.SessionToken,\n            expiration: clientResult.Credentials.Expiration\n          },\n          identityId\n        };\n        if (clientResult.IdentityId) {\n          res.identityId = clientResult.IdentityId;\n          _this4._identityIdStore.storeIdentityId({\n            id: clientResult.IdentityId,\n            type: 'guest'\n          });\n        }\n        _this4._credentialsAndIdentityId = {\n          ...res,\n          isAuthenticatedCreds: false\n        };\n        return res;\n      } else {\n        throw new AuthError({\n          name: 'CredentialsNotFoundException',\n          message: `Cognito did not respond with either Credentials, AccessKeyId or SecretKey.`\n        });\n      }\n    })();\n  }\n  credsForOIDCTokens(authConfig, authTokens, identityId) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      if (_this5._credentialsAndIdentityId && !_this5.isPastTTL() && _this5._credentialsAndIdentityId.isAuthenticatedCreds === true) {\n        logger.debug('returning stored credentials as they neither past TTL nor expired.');\n        return _this5._credentialsAndIdentityId;\n      }\n      // Clear to discard if any unauthenticated credentials are set and start with a clean slate\n      _this5.clearCredentials();\n      const logins = authTokens.idToken ? formLoginsMap(authTokens.idToken.toString()) : {};\n      const region = getRegionFromIdentityPoolId(authConfig.identityPoolId);\n      let clientResult;\n      try {\n        clientResult = yield getCredentialsForIdentity({\n          region\n        }, {\n          IdentityId: identityId,\n          Logins: logins\n        });\n      } catch (e) {\n        assertServiceError(e);\n        throw new AuthError(e);\n      }\n      if (clientResult?.Credentials?.AccessKeyId && clientResult?.Credentials?.SecretKey) {\n        _this5._nextCredentialsRefresh = new Date().getTime() + CREDENTIALS_TTL;\n        const res = {\n          credentials: {\n            accessKeyId: clientResult.Credentials.AccessKeyId,\n            secretAccessKey: clientResult.Credentials.SecretKey,\n            sessionToken: clientResult.Credentials.SessionToken,\n            expiration: clientResult.Credentials.Expiration\n          },\n          identityId\n        };\n        if (clientResult.IdentityId) {\n          res.identityId = clientResult.IdentityId;\n          // note: the following call removes guest identityId from the persistent store (localStorage)\n          _this5._identityIdStore.storeIdentityId({\n            id: clientResult.IdentityId,\n            type: 'primary'\n          });\n        }\n        // Store the credentials in-memory along with the expiration\n        _this5._credentialsAndIdentityId = {\n          ...res,\n          isAuthenticatedCreds: true,\n          associatedIdToken: authTokens.idToken?.toString()\n        };\n        return res;\n      } else {\n        throw new AuthError({\n          name: 'CredentialsException',\n          message: `Cognito did not respond with either Credentials, AccessKeyId or SecretKey.`\n        });\n      }\n    })();\n  }\n  isPastTTL() {\n    return this._nextCredentialsRefresh === undefined ? true : this._nextCredentialsRefresh <= Date.now();\n  }\n  hasTokenChanged(tokens) {\n    return !!tokens && !!this._credentialsAndIdentityId?.associatedIdToken && tokens.idToken?.toString() !== this._credentialsAndIdentityId.associatedIdToken;\n  }\n}\nexport { CognitoAWSCredentialsAndIdentityIdProvider };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getCredentialsForIdentity", "assertIdentityPoolIdConfig", "<PERSON>th<PERSON><PERSON><PERSON>", "assertServiceError", "getRegionFromIdentityPoolId", "assertIdTokenInAuthTokens", "cognitoIdentityIdProvider", "formLoginsMap", "logger", "CREDENTIALS_TTL", "CognitoAWSCredentialsAndIdentityIdProvider", "constructor", "identityIdStore", "_nextCredentialsRefresh", "_identityIdStore", "clearCredentialsAndIdentityId", "_this", "_asyncToGenerator", "debug", "_credentialsAndIdentityId", "undefined", "clearIdentityId", "clearCredentials", "_this2", "getCredentialsAndIdentityId", "getCredentialsOptions", "_this3", "isAuthenticated", "authenticated", "tokens", "authConfig", "Cognito", "allowGuestAccess", "forceRefresh", "tokenHasChanged", "hasTokenChanged", "identityId", "getGuestCredentials", "credsForOIDCTokens", "_this4", "isPastTTL", "isAuthenticatedCreds", "info", "region", "identityPoolId", "clientResult", "IdentityId", "e", "Credentials", "AccessKeyId", "<PERSON><PERSON><PERSON>", "Date", "getTime", "res", "credentials", "accessKeyId", "secretAccessKey", "sessionToken", "SessionToken", "expiration", "Expiration", "storeIdentityId", "id", "type", "name", "message", "authTokens", "_this5", "logins", "idToken", "toString", "<PERSON><PERSON>", "associatedIdToken", "now"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/credentialsProvider/credentialsProvider.mjs"], "sourcesContent": ["import { ConsoleLogger, getCredentialsForIdentity } from '@aws-amplify/core';\nimport { assertIdentityPoolIdConfig } from '@aws-amplify/core/internals/utils';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { getRegionFromIdentityPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertIdTokenInAuthTokens } from '../utils/types.mjs';\nimport { cognitoIdentityIdProvider } from './IdentityIdProvider.mjs';\nimport { formLoginsMap } from './utils.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('CognitoCredentialsProvider');\nconst CREDENTIALS_TTL = 50 * 60 * 1000; // 50 min, can be modified on config if required in the future\nclass CognitoAWSCredentialsAndIdentityIdProvider {\n    constructor(identityIdStore) {\n        this._nextCredentialsRefresh = 0;\n        this._identityIdStore = identityIdStore;\n    }\n    async clearCredentialsAndIdentityId() {\n        logger.debug('Clearing out credentials and identityId');\n        this._credentialsAndIdentityId = undefined;\n        await this._identityIdStore.clearIdentityId();\n    }\n    async clearCredentials() {\n        logger.debug('Clearing out in-memory credentials');\n        this._credentialsAndIdentityId = undefined;\n    }\n    async getCredentialsAndIdentityId(getCredentialsOptions) {\n        const isAuthenticated = getCredentialsOptions.authenticated;\n        const { tokens } = getCredentialsOptions;\n        const { authConfig } = getCredentialsOptions;\n        try {\n            assertIdentityPoolIdConfig(authConfig?.Cognito);\n        }\n        catch {\n            // No identity pool configured, skipping\n            return;\n        }\n        if (!isAuthenticated && !authConfig.Cognito.allowGuestAccess) {\n            // TODO(V6): return partial result like Native platforms\n            return;\n        }\n        const { forceRefresh } = getCredentialsOptions;\n        const tokenHasChanged = this.hasTokenChanged(tokens);\n        const identityId = await cognitoIdentityIdProvider({\n            tokens,\n            authConfig: authConfig.Cognito,\n            identityIdStore: this._identityIdStore,\n        });\n        // Clear cached credentials when forceRefresh is true OR the cache token has changed\n        if (forceRefresh || tokenHasChanged) {\n            this.clearCredentials();\n        }\n        if (!isAuthenticated) {\n            return this.getGuestCredentials(identityId, authConfig.Cognito);\n        }\n        else {\n            assertIdTokenInAuthTokens(tokens);\n            return this.credsForOIDCTokens(authConfig.Cognito, tokens, identityId);\n        }\n    }\n    async getGuestCredentials(identityId, authConfig) {\n        // Return existing in-memory cached credentials only if it exists, is not past it's lifetime and is unauthenticated credentials\n        if (this._credentialsAndIdentityId &&\n            !this.isPastTTL() &&\n            this._credentialsAndIdentityId.isAuthenticatedCreds === false) {\n            logger.info('returning stored credentials as they neither past TTL nor expired.');\n            return this._credentialsAndIdentityId;\n        }\n        // Clear to discard if any authenticated credentials are set and start with a clean slate\n        this.clearCredentials();\n        const region = getRegionFromIdentityPoolId(authConfig.identityPoolId);\n        // use identityId to obtain guest credentials\n        // save credentials in-memory\n        // No logins params should be passed for guest creds:\n        // https://docs.aws.amazon.com/cognitoidentity/latest/APIReference/API_GetCredentialsForIdentity.html\n        let clientResult;\n        try {\n            clientResult = await getCredentialsForIdentity({ region }, {\n                IdentityId: identityId,\n            });\n        }\n        catch (e) {\n            assertServiceError(e);\n            throw new AuthError(e);\n        }\n        if (clientResult?.Credentials?.AccessKeyId &&\n            clientResult?.Credentials?.SecretKey) {\n            this._nextCredentialsRefresh = new Date().getTime() + CREDENTIALS_TTL;\n            const res = {\n                credentials: {\n                    accessKeyId: clientResult.Credentials.AccessKeyId,\n                    secretAccessKey: clientResult.Credentials.SecretKey,\n                    sessionToken: clientResult.Credentials.SessionToken,\n                    expiration: clientResult.Credentials.Expiration,\n                },\n                identityId,\n            };\n            if (clientResult.IdentityId) {\n                res.identityId = clientResult.IdentityId;\n                this._identityIdStore.storeIdentityId({\n                    id: clientResult.IdentityId,\n                    type: 'guest',\n                });\n            }\n            this._credentialsAndIdentityId = {\n                ...res,\n                isAuthenticatedCreds: false,\n            };\n            return res;\n        }\n        else {\n            throw new AuthError({\n                name: 'CredentialsNotFoundException',\n                message: `Cognito did not respond with either Credentials, AccessKeyId or SecretKey.`,\n            });\n        }\n    }\n    async credsForOIDCTokens(authConfig, authTokens, identityId) {\n        if (this._credentialsAndIdentityId &&\n            !this.isPastTTL() &&\n            this._credentialsAndIdentityId.isAuthenticatedCreds === true) {\n            logger.debug('returning stored credentials as they neither past TTL nor expired.');\n            return this._credentialsAndIdentityId;\n        }\n        // Clear to discard if any unauthenticated credentials are set and start with a clean slate\n        this.clearCredentials();\n        const logins = authTokens.idToken\n            ? formLoginsMap(authTokens.idToken.toString())\n            : {};\n        const region = getRegionFromIdentityPoolId(authConfig.identityPoolId);\n        let clientResult;\n        try {\n            clientResult = await getCredentialsForIdentity({ region }, {\n                IdentityId: identityId,\n                Logins: logins,\n            });\n        }\n        catch (e) {\n            assertServiceError(e);\n            throw new AuthError(e);\n        }\n        if (clientResult?.Credentials?.AccessKeyId &&\n            clientResult?.Credentials?.SecretKey) {\n            this._nextCredentialsRefresh = new Date().getTime() + CREDENTIALS_TTL;\n            const res = {\n                credentials: {\n                    accessKeyId: clientResult.Credentials.AccessKeyId,\n                    secretAccessKey: clientResult.Credentials.SecretKey,\n                    sessionToken: clientResult.Credentials.SessionToken,\n                    expiration: clientResult.Credentials.Expiration,\n                },\n                identityId,\n            };\n            if (clientResult.IdentityId) {\n                res.identityId = clientResult.IdentityId;\n                // note: the following call removes guest identityId from the persistent store (localStorage)\n                this._identityIdStore.storeIdentityId({\n                    id: clientResult.IdentityId,\n                    type: 'primary',\n                });\n            }\n            // Store the credentials in-memory along with the expiration\n            this._credentialsAndIdentityId = {\n                ...res,\n                isAuthenticatedCreds: true,\n                associatedIdToken: authTokens.idToken?.toString(),\n            };\n            return res;\n        }\n        else {\n            throw new AuthError({\n                name: 'CredentialsException',\n                message: `Cognito did not respond with either Credentials, AccessKeyId or SecretKey.`,\n            });\n        }\n    }\n    isPastTTL() {\n        return this._nextCredentialsRefresh === undefined\n            ? true\n            : this._nextCredentialsRefresh <= Date.now();\n    }\n    hasTokenChanged(tokens) {\n        return (!!tokens &&\n            !!this._credentialsAndIdentityId?.associatedIdToken &&\n            tokens.idToken?.toString() !==\n                this._credentialsAndIdentityId.associatedIdToken);\n    }\n}\n\nexport { CognitoAWSCredentialsAndIdentityIdProvider };\n"], "mappings": ";AAAA,SAASA,aAAa,EAAEC,yBAAyB,QAAQ,mBAAmB;AAC5E,SAASC,0BAA0B,QAAQ,mCAAmC;AAC9E,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,2BAA2B,QAAQ,+CAA+C;AAC3F,SAASC,yBAAyB,QAAQ,oBAAoB;AAC9D,SAASC,yBAAyB,QAAQ,0BAA0B;AACpE,SAASC,aAAa,QAAQ,aAAa;;AAE3C;AACA;AACA,MAAMC,MAAM,GAAG,IAAIT,aAAa,CAAC,4BAA4B,CAAC;AAC9D,MAAMU,eAAe,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AACxC,MAAMC,0CAA0C,CAAC;EAC7CC,WAAWA,CAACC,eAAe,EAAE;IACzB,IAAI,CAACC,uBAAuB,GAAG,CAAC;IAChC,IAAI,CAACC,gBAAgB,GAAGF,eAAe;EAC3C;EACMG,6BAA6BA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAClCT,MAAM,CAACU,KAAK,CAAC,yCAAyC,CAAC;MACvDF,KAAI,CAACG,yBAAyB,GAAGC,SAAS;MAC1C,MAAMJ,KAAI,CAACF,gBAAgB,CAACO,eAAe,CAAC,CAAC;IAAC;EAClD;EACMC,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAN,iBAAA;MACrBT,MAAM,CAACU,KAAK,CAAC,oCAAoC,CAAC;MAClDK,MAAI,CAACJ,yBAAyB,GAAGC,SAAS;IAAC;EAC/C;EACMI,2BAA2BA,CAACC,qBAAqB,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAT,iBAAA;MACrD,MAAMU,eAAe,GAAGF,qBAAqB,CAACG,aAAa;MAC3D,MAAM;QAAEC;MAAO,CAAC,GAAGJ,qBAAqB;MACxC,MAAM;QAAEK;MAAW,CAAC,GAAGL,qBAAqB;MAC5C,IAAI;QACAxB,0BAA0B,CAAC6B,UAAU,EAAEC,OAAO,CAAC;MACnD,CAAC,CACD,MAAM;QACF;QACA;MACJ;MACA,IAAI,CAACJ,eAAe,IAAI,CAACG,UAAU,CAACC,OAAO,CAACC,gBAAgB,EAAE;QAC1D;QACA;MACJ;MACA,MAAM;QAAEC;MAAa,CAAC,GAAGR,qBAAqB;MAC9C,MAAMS,eAAe,GAAGR,MAAI,CAACS,eAAe,CAACN,MAAM,CAAC;MACpD,MAAMO,UAAU,SAAS9B,yBAAyB,CAAC;QAC/CuB,MAAM;QACNC,UAAU,EAAEA,UAAU,CAACC,OAAO;QAC9BnB,eAAe,EAAEc,MAAI,CAACZ;MAC1B,CAAC,CAAC;MACF;MACA,IAAImB,YAAY,IAAIC,eAAe,EAAE;QACjCR,MAAI,CAACJ,gBAAgB,CAAC,CAAC;MAC3B;MACA,IAAI,CAACK,eAAe,EAAE;QAClB,OAAOD,MAAI,CAACW,mBAAmB,CAACD,UAAU,EAAEN,UAAU,CAACC,OAAO,CAAC;MACnE,CAAC,MACI;QACD1B,yBAAyB,CAACwB,MAAM,CAAC;QACjC,OAAOH,MAAI,CAACY,kBAAkB,CAACR,UAAU,CAACC,OAAO,EAAEF,MAAM,EAAEO,UAAU,CAAC;MAC1E;IAAC;EACL;EACMC,mBAAmBA,CAACD,UAAU,EAAEN,UAAU,EAAE;IAAA,IAAAS,MAAA;IAAA,OAAAtB,iBAAA;MAC9C;MACA,IAAIsB,MAAI,CAACpB,yBAAyB,IAC9B,CAACoB,MAAI,CAACC,SAAS,CAAC,CAAC,IACjBD,MAAI,CAACpB,yBAAyB,CAACsB,oBAAoB,KAAK,KAAK,EAAE;QAC/DjC,MAAM,CAACkC,IAAI,CAAC,oEAAoE,CAAC;QACjF,OAAOH,MAAI,CAACpB,yBAAyB;MACzC;MACA;MACAoB,MAAI,CAACjB,gBAAgB,CAAC,CAAC;MACvB,MAAMqB,MAAM,GAAGvC,2BAA2B,CAAC0B,UAAU,CAACc,cAAc,CAAC;MACrE;MACA;MACA;MACA;MACA,IAAIC,YAAY;MAChB,IAAI;QACAA,YAAY,SAAS7C,yBAAyB,CAAC;UAAE2C;QAAO,CAAC,EAAE;UACvDG,UAAU,EAAEV;QAChB,CAAC,CAAC;MACN,CAAC,CACD,OAAOW,CAAC,EAAE;QACN5C,kBAAkB,CAAC4C,CAAC,CAAC;QACrB,MAAM,IAAI7C,SAAS,CAAC6C,CAAC,CAAC;MAC1B;MACA,IAAIF,YAAY,EAAEG,WAAW,EAAEC,WAAW,IACtCJ,YAAY,EAAEG,WAAW,EAAEE,SAAS,EAAE;QACtCX,MAAI,CAAC1B,uBAAuB,GAAG,IAAIsC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG3C,eAAe;QACrE,MAAM4C,GAAG,GAAG;UACRC,WAAW,EAAE;YACTC,WAAW,EAAEV,YAAY,CAACG,WAAW,CAACC,WAAW;YACjDO,eAAe,EAAEX,YAAY,CAACG,WAAW,CAACE,SAAS;YACnDO,YAAY,EAAEZ,YAAY,CAACG,WAAW,CAACU,YAAY;YACnDC,UAAU,EAAEd,YAAY,CAACG,WAAW,CAACY;UACzC,CAAC;UACDxB;QACJ,CAAC;QACD,IAAIS,YAAY,CAACC,UAAU,EAAE;UACzBO,GAAG,CAACjB,UAAU,GAAGS,YAAY,CAACC,UAAU;UACxCP,MAAI,CAACzB,gBAAgB,CAAC+C,eAAe,CAAC;YAClCC,EAAE,EAAEjB,YAAY,CAACC,UAAU;YAC3BiB,IAAI,EAAE;UACV,CAAC,CAAC;QACN;QACAxB,MAAI,CAACpB,yBAAyB,GAAG;UAC7B,GAAGkC,GAAG;UACNZ,oBAAoB,EAAE;QAC1B,CAAC;QACD,OAAOY,GAAG;MACd,CAAC,MACI;QACD,MAAM,IAAInD,SAAS,CAAC;UAChB8D,IAAI,EAAE,8BAA8B;UACpCC,OAAO,EAAE;QACb,CAAC,CAAC;MACN;IAAC;EACL;EACM3B,kBAAkBA,CAACR,UAAU,EAAEoC,UAAU,EAAE9B,UAAU,EAAE;IAAA,IAAA+B,MAAA;IAAA,OAAAlD,iBAAA;MACzD,IAAIkD,MAAI,CAAChD,yBAAyB,IAC9B,CAACgD,MAAI,CAAC3B,SAAS,CAAC,CAAC,IACjB2B,MAAI,CAAChD,yBAAyB,CAACsB,oBAAoB,KAAK,IAAI,EAAE;QAC9DjC,MAAM,CAACU,KAAK,CAAC,oEAAoE,CAAC;QAClF,OAAOiD,MAAI,CAAChD,yBAAyB;MACzC;MACA;MACAgD,MAAI,CAAC7C,gBAAgB,CAAC,CAAC;MACvB,MAAM8C,MAAM,GAAGF,UAAU,CAACG,OAAO,GAC3B9D,aAAa,CAAC2D,UAAU,CAACG,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC,GAC5C,CAAC,CAAC;MACR,MAAM3B,MAAM,GAAGvC,2BAA2B,CAAC0B,UAAU,CAACc,cAAc,CAAC;MACrE,IAAIC,YAAY;MAChB,IAAI;QACAA,YAAY,SAAS7C,yBAAyB,CAAC;UAAE2C;QAAO,CAAC,EAAE;UACvDG,UAAU,EAAEV,UAAU;UACtBmC,MAAM,EAAEH;QACZ,CAAC,CAAC;MACN,CAAC,CACD,OAAOrB,CAAC,EAAE;QACN5C,kBAAkB,CAAC4C,CAAC,CAAC;QACrB,MAAM,IAAI7C,SAAS,CAAC6C,CAAC,CAAC;MAC1B;MACA,IAAIF,YAAY,EAAEG,WAAW,EAAEC,WAAW,IACtCJ,YAAY,EAAEG,WAAW,EAAEE,SAAS,EAAE;QACtCiB,MAAI,CAACtD,uBAAuB,GAAG,IAAIsC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAG3C,eAAe;QACrE,MAAM4C,GAAG,GAAG;UACRC,WAAW,EAAE;YACTC,WAAW,EAAEV,YAAY,CAACG,WAAW,CAACC,WAAW;YACjDO,eAAe,EAAEX,YAAY,CAACG,WAAW,CAACE,SAAS;YACnDO,YAAY,EAAEZ,YAAY,CAACG,WAAW,CAACU,YAAY;YACnDC,UAAU,EAAEd,YAAY,CAACG,WAAW,CAACY;UACzC,CAAC;UACDxB;QACJ,CAAC;QACD,IAAIS,YAAY,CAACC,UAAU,EAAE;UACzBO,GAAG,CAACjB,UAAU,GAAGS,YAAY,CAACC,UAAU;UACxC;UACAqB,MAAI,CAACrD,gBAAgB,CAAC+C,eAAe,CAAC;YAClCC,EAAE,EAAEjB,YAAY,CAACC,UAAU;YAC3BiB,IAAI,EAAE;UACV,CAAC,CAAC;QACN;QACA;QACAI,MAAI,CAAChD,yBAAyB,GAAG;UAC7B,GAAGkC,GAAG;UACNZ,oBAAoB,EAAE,IAAI;UAC1B+B,iBAAiB,EAAEN,UAAU,CAACG,OAAO,EAAEC,QAAQ,CAAC;QACpD,CAAC;QACD,OAAOjB,GAAG;MACd,CAAC,MACI;QACD,MAAM,IAAInD,SAAS,CAAC;UAChB8D,IAAI,EAAE,sBAAsB;UAC5BC,OAAO,EAAE;QACb,CAAC,CAAC;MACN;IAAC;EACL;EACAzB,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC3B,uBAAuB,KAAKO,SAAS,GAC3C,IAAI,GACJ,IAAI,CAACP,uBAAuB,IAAIsC,IAAI,CAACsB,GAAG,CAAC,CAAC;EACpD;EACAtC,eAAeA,CAACN,MAAM,EAAE;IACpB,OAAQ,CAAC,CAACA,MAAM,IACZ,CAAC,CAAC,IAAI,CAACV,yBAAyB,EAAEqD,iBAAiB,IACnD3C,MAAM,CAACwC,OAAO,EAAEC,QAAQ,CAAC,CAAC,KACtB,IAAI,CAACnD,yBAAyB,CAACqD,iBAAiB;EAC5D;AACJ;AAEA,SAAS9D,0CAA0C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}