{"ast": null, "code": "const fieldcontrol = {\n  borderStyle: {\n    value: 'solid'\n  },\n  borderColor: {\n    value: '{colors.border.primary.value}'\n  },\n  borderWidth: {\n    value: '{borderWidths.small.value}'\n  },\n  borderRadius: {\n    value: '{radii.small.value}'\n  },\n  color: {\n    value: '{colors.font.primary.value}'\n  },\n  paddingBlockStart: {\n    value: '{space.xs.value}'\n  },\n  paddingBlockEnd: {\n    value: '{space.xs.value}'\n  },\n  paddingInlineStart: {\n    value: '{space.medium.value}'\n  },\n  paddingInlineEnd: {\n    value: '{space.medium.value}'\n  },\n  fontSize: {\n    value: '{components.field.fontSize.value}'\n  },\n  lineHeight: {\n    value: '{lineHeights.medium.value}'\n  },\n  transitionDuration: {\n    value: '{time.medium.value}'\n  },\n  outlineColor: {\n    value: '{colors.transparent.value}'\n  },\n  outlineStyle: {\n    value: 'solid'\n  },\n  outlineWidth: {\n    value: '{outlineWidths.medium.value}'\n  },\n  outlineOffset: {\n    value: '{outlineOffsets.medium.value}'\n  },\n  small: {\n    fontSize: {\n      value: '{components.field.small.fontSize.value}'\n    },\n    paddingBlockStart: {\n      value: '{space.xxs.value}'\n    },\n    paddingBlockEnd: {\n      value: '{space.xxs.value}'\n    },\n    paddingInlineStart: {\n      value: '{space.small.value}'\n    },\n    paddingInlineEnd: {\n      value: '{space.small.value}'\n    }\n  },\n  large: {\n    fontSize: {\n      value: '{components.field.large.fontSize.value}'\n    },\n    paddingBlockStart: {\n      value: '{space.xs.value}'\n    },\n    paddingBlockEnd: {\n      value: '{space.xs.value}'\n    },\n    paddingInlineStart: {\n      value: '{space.medium.value}'\n    },\n    paddingInlineEnd: {\n      value: '{space.medium.value}'\n    }\n  },\n  quiet: {\n    borderStyle: {\n      value: 'none'\n    },\n    borderInlineStart: {\n      value: 'none'\n    },\n    borderInlineEnd: {\n      value: 'none'\n    },\n    borderBlockStart: {\n      value: 'none'\n    },\n    borderRadius: {\n      value: '0'\n    },\n    _focus: {\n      borderBlockEndColor: {\n        value: 'transparent'\n      },\n      boxShadow: {\n        value: '{components.fieldcontrol._focus.boxShadow.value}'\n      }\n    },\n    _error: {\n      borderBlockEndColor: {\n        value: '{colors.border.error.value}'\n      },\n      _focus: {\n        borderBlockEndColor: {\n          value: 'transparent'\n        },\n        boxShadow: {\n          value: '{components.fieldcontrol._error._focus.boxShadow.value}'\n        }\n      }\n    }\n  },\n  _focus: {\n    // These focus styles have been calibrated to create\n    // a highly visible focus indicator per WCAG 2.2 guidlines:\n    // See: https://www.w3.org/TR/WCAG22/#focus-appearance\n    //\n    // Key features:\n    // * Focus indicator area is at least the 2 CSS px perimeter around the component.\n    // * Contrast between focused and unfocused area of contrast has a ratio of 3:1\n    //\n    // IMPORTANT: Must recalibrate if `colors.border.focus` are changed\n    borderColor: {\n      value: '{colors.border.focus.value}'\n    },\n    boxShadow: {\n      value: {\n        offsetX: '0px',\n        offsetY: '0px',\n        blurRadius: '0px',\n        spreadRadius: '2px',\n        color: '{colors.border.focus.value}'\n      }\n    }\n  },\n  _disabled: {\n    color: {\n      value: '{colors.font.disabled.value}'\n    },\n    cursor: {\n      value: 'not-allowed'\n    },\n    borderColor: {\n      value: '{colors.transparent.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.disabled.value}'\n    }\n  },\n  _error: {\n    borderColor: {\n      value: '{colors.border.error.value}'\n    },\n    color: {\n      value: '{colors.font.error.value}'\n    },\n    _focus: {\n      boxShadow: {\n        value: {\n          offsetX: '0px',\n          offsetY: '0px',\n          blurRadius: '0px',\n          spreadRadius: '2px',\n          color: '{colors.border.error.value}'\n        }\n      }\n    }\n  },\n  info: {\n    _focus: {\n      boxShadow: {\n        value: {\n          offsetX: '0px',\n          offsetY: '0px',\n          blurRadius: '0px',\n          spreadRadius: '2px',\n          color: '{colors.blue.100.value}'\n        }\n      }\n    }\n  },\n  warning: {\n    _focus: {\n      boxShadow: {\n        value: {\n          offsetX: '0px',\n          offsetY: '0px',\n          blurRadius: '0px',\n          spreadRadius: '2px',\n          color: '{colors.orange.100.value}'\n        }\n      }\n    }\n  },\n  success: {\n    _focus: {\n      boxShadow: {\n        value: {\n          offsetX: '0px',\n          offsetY: '0px',\n          blurRadius: '0px',\n          spreadRadius: '2px',\n          color: '{colors.green.100.value}'\n        }\n      }\n    }\n  },\n  overlay: {\n    _focus: {\n      boxShadow: {\n        value: {\n          offsetX: '0px',\n          offsetY: '0px',\n          blurRadius: '0px',\n          spreadRadius: '2px',\n          color: '{colors.overlay.90.value}'\n        }\n      }\n    }\n  }\n};\nexport { fieldcontrol };", "map": {"version": 3, "names": ["fieldcontrol", "borderStyle", "value", "borderColor", "borderWidth", "borderRadius", "color", "paddingBlockStart", "paddingBlockEnd", "paddingInlineStart", "paddingInlineEnd", "fontSize", "lineHeight", "transitionDuration", "outlineColor", "outlineStyle", "outlineWidth", "outlineOffset", "small", "large", "quiet", "borderInlineStart", "borderInlineEnd", "borderBlockStart", "_focus", "borderBlockEndColor", "boxShadow", "_error", "offsetX", "offsetY", "blurRadius", "spreadRadius", "_disabled", "cursor", "backgroundColor", "info", "warning", "success", "overlay"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/fieldControl.mjs"], "sourcesContent": ["const fieldcontrol = {\n    borderStyle: { value: 'solid' },\n    borderColor: { value: '{colors.border.primary.value}' },\n    borderWidth: { value: '{borderWidths.small.value}' },\n    borderRadius: { value: '{radii.small.value}' },\n    color: { value: '{colors.font.primary.value}' },\n    paddingBlockStart: {\n        value: '{space.xs.value}',\n    },\n    paddingBlockEnd: {\n        value: '{space.xs.value}',\n    },\n    paddingInlineStart: {\n        value: '{space.medium.value}',\n    },\n    paddingInlineEnd: {\n        value: '{space.medium.value}',\n    },\n    fontSize: { value: '{components.field.fontSize.value}' },\n    lineHeight: { value: '{lineHeights.medium.value}' },\n    transitionDuration: { value: '{time.medium.value}' },\n    outlineColor: { value: '{colors.transparent.value}' },\n    outlineStyle: { value: 'solid' },\n    outlineWidth: { value: '{outlineWidths.medium.value}' },\n    outlineOffset: { value: '{outlineOffsets.medium.value}' },\n    small: {\n        fontSize: { value: '{components.field.small.fontSize.value}' },\n        paddingBlockStart: {\n            value: '{space.xxs.value}',\n        },\n        paddingBlockEnd: {\n            value: '{space.xxs.value}',\n        },\n        paddingInlineStart: {\n            value: '{space.small.value}',\n        },\n        paddingInlineEnd: {\n            value: '{space.small.value}',\n        },\n    },\n    large: {\n        fontSize: { value: '{components.field.large.fontSize.value}' },\n        paddingBlockStart: {\n            value: '{space.xs.value}',\n        },\n        paddingBlockEnd: {\n            value: '{space.xs.value}',\n        },\n        paddingInlineStart: {\n            value: '{space.medium.value}',\n        },\n        paddingInlineEnd: {\n            value: '{space.medium.value}',\n        },\n    },\n    quiet: {\n        borderStyle: { value: 'none' },\n        borderInlineStart: { value: 'none' },\n        borderInlineEnd: { value: 'none' },\n        borderBlockStart: { value: 'none' },\n        borderRadius: { value: '0' },\n        _focus: {\n            borderBlockEndColor: { value: 'transparent' },\n            boxShadow: {\n                value: '{components.fieldcontrol._focus.boxShadow.value}',\n            },\n        },\n        _error: {\n            borderBlockEndColor: { value: '{colors.border.error.value}' },\n            _focus: {\n                borderBlockEndColor: { value: 'transparent' },\n                boxShadow: {\n                    value: '{components.fieldcontrol._error._focus.boxShadow.value}',\n                },\n            },\n        },\n    },\n    _focus: {\n        // These focus styles have been calibrated to create\n        // a highly visible focus indicator per WCAG 2.2 guidlines:\n        // See: https://www.w3.org/TR/WCAG22/#focus-appearance\n        //\n        // Key features:\n        // * Focus indicator area is at least the 2 CSS px perimeter around the component.\n        // * Contrast between focused and unfocused area of contrast has a ratio of 3:1\n        //\n        // IMPORTANT: Must recalibrate if `colors.border.focus` are changed\n        borderColor: { value: '{colors.border.focus.value}' },\n        boxShadow: {\n            value: {\n                offsetX: '0px',\n                offsetY: '0px',\n                blurRadius: '0px',\n                spreadRadius: '2px',\n                color: '{colors.border.focus.value}',\n            },\n        },\n    },\n    _disabled: {\n        color: { value: '{colors.font.disabled.value}' },\n        cursor: { value: 'not-allowed' },\n        borderColor: { value: '{colors.transparent.value}' },\n        backgroundColor: { value: '{colors.background.disabled.value}' },\n    },\n    _error: {\n        borderColor: { value: '{colors.border.error.value}' },\n        color: { value: '{colors.font.error.value}' },\n        _focus: {\n            boxShadow: {\n                value: {\n                    offsetX: '0px',\n                    offsetY: '0px',\n                    blurRadius: '0px',\n                    spreadRadius: '2px',\n                    color: '{colors.border.error.value}',\n                },\n            },\n        },\n    },\n    info: {\n        _focus: {\n            boxShadow: {\n                value: {\n                    offsetX: '0px',\n                    offsetY: '0px',\n                    blurRadius: '0px',\n                    spreadRadius: '2px',\n                    color: '{colors.blue.100.value}',\n                },\n            },\n        },\n    },\n    warning: {\n        _focus: {\n            boxShadow: {\n                value: {\n                    offsetX: '0px',\n                    offsetY: '0px',\n                    blurRadius: '0px',\n                    spreadRadius: '2px',\n                    color: '{colors.orange.100.value}',\n                },\n            },\n        },\n    },\n    success: {\n        _focus: {\n            boxShadow: {\n                value: {\n                    offsetX: '0px',\n                    offsetY: '0px',\n                    blurRadius: '0px',\n                    spreadRadius: '2px',\n                    color: '{colors.green.100.value}',\n                },\n            },\n        },\n    },\n    overlay: {\n        _focus: {\n            boxShadow: {\n                value: {\n                    offsetX: '0px',\n                    offsetY: '0px',\n                    blurRadius: '0px',\n                    spreadRadius: '2px',\n                    color: '{colors.overlay.90.value}',\n                },\n            },\n        },\n    },\n};\n\nexport { fieldcontrol };\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG;EACjBC,WAAW,EAAE;IAAEC,KAAK,EAAE;EAAQ,CAAC;EAC/BC,WAAW,EAAE;IAAED,KAAK,EAAE;EAAgC,CAAC;EACvDE,WAAW,EAAE;IAAEF,KAAK,EAAE;EAA6B,CAAC;EACpDG,YAAY,EAAE;IAAEH,KAAK,EAAE;EAAsB,CAAC;EAC9CI,KAAK,EAAE;IAAEJ,KAAK,EAAE;EAA8B,CAAC;EAC/CK,iBAAiB,EAAE;IACfL,KAAK,EAAE;EACX,CAAC;EACDM,eAAe,EAAE;IACbN,KAAK,EAAE;EACX,CAAC;EACDO,kBAAkB,EAAE;IAChBP,KAAK,EAAE;EACX,CAAC;EACDQ,gBAAgB,EAAE;IACdR,KAAK,EAAE;EACX,CAAC;EACDS,QAAQ,EAAE;IAAET,KAAK,EAAE;EAAoC,CAAC;EACxDU,UAAU,EAAE;IAAEV,KAAK,EAAE;EAA6B,CAAC;EACnDW,kBAAkB,EAAE;IAAEX,KAAK,EAAE;EAAsB,CAAC;EACpDY,YAAY,EAAE;IAAEZ,KAAK,EAAE;EAA6B,CAAC;EACrDa,YAAY,EAAE;IAAEb,KAAK,EAAE;EAAQ,CAAC;EAChCc,YAAY,EAAE;IAAEd,KAAK,EAAE;EAA+B,CAAC;EACvDe,aAAa,EAAE;IAAEf,KAAK,EAAE;EAAgC,CAAC;EACzDgB,KAAK,EAAE;IACHP,QAAQ,EAAE;MAAET,KAAK,EAAE;IAA0C,CAAC;IAC9DK,iBAAiB,EAAE;MACfL,KAAK,EAAE;IACX,CAAC;IACDM,eAAe,EAAE;MACbN,KAAK,EAAE;IACX,CAAC;IACDO,kBAAkB,EAAE;MAChBP,KAAK,EAAE;IACX,CAAC;IACDQ,gBAAgB,EAAE;MACdR,KAAK,EAAE;IACX;EACJ,CAAC;EACDiB,KAAK,EAAE;IACHR,QAAQ,EAAE;MAAET,KAAK,EAAE;IAA0C,CAAC;IAC9DK,iBAAiB,EAAE;MACfL,KAAK,EAAE;IACX,CAAC;IACDM,eAAe,EAAE;MACbN,KAAK,EAAE;IACX,CAAC;IACDO,kBAAkB,EAAE;MAChBP,KAAK,EAAE;IACX,CAAC;IACDQ,gBAAgB,EAAE;MACdR,KAAK,EAAE;IACX;EACJ,CAAC;EACDkB,KAAK,EAAE;IACHnB,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;IAC9BmB,iBAAiB,EAAE;MAAEnB,KAAK,EAAE;IAAO,CAAC;IACpCoB,eAAe,EAAE;MAAEpB,KAAK,EAAE;IAAO,CAAC;IAClCqB,gBAAgB,EAAE;MAAErB,KAAK,EAAE;IAAO,CAAC;IACnCG,YAAY,EAAE;MAAEH,KAAK,EAAE;IAAI,CAAC;IAC5BsB,MAAM,EAAE;MACJC,mBAAmB,EAAE;QAAEvB,KAAK,EAAE;MAAc,CAAC;MAC7CwB,SAAS,EAAE;QACPxB,KAAK,EAAE;MACX;IACJ,CAAC;IACDyB,MAAM,EAAE;MACJF,mBAAmB,EAAE;QAAEvB,KAAK,EAAE;MAA8B,CAAC;MAC7DsB,MAAM,EAAE;QACJC,mBAAmB,EAAE;UAAEvB,KAAK,EAAE;QAAc,CAAC;QAC7CwB,SAAS,EAAE;UACPxB,KAAK,EAAE;QACX;MACJ;IACJ;EACJ,CAAC;EACDsB,MAAM,EAAE;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACArB,WAAW,EAAE;MAAED,KAAK,EAAE;IAA8B,CAAC;IACrDwB,SAAS,EAAE;MACPxB,KAAK,EAAE;QACH0B,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAE,KAAK;QACnBzB,KAAK,EAAE;MACX;IACJ;EACJ,CAAC;EACD0B,SAAS,EAAE;IACP1B,KAAK,EAAE;MAAEJ,KAAK,EAAE;IAA+B,CAAC;IAChD+B,MAAM,EAAE;MAAE/B,KAAK,EAAE;IAAc,CAAC;IAChCC,WAAW,EAAE;MAAED,KAAK,EAAE;IAA6B,CAAC;IACpDgC,eAAe,EAAE;MAAEhC,KAAK,EAAE;IAAqC;EACnE,CAAC;EACDyB,MAAM,EAAE;IACJxB,WAAW,EAAE;MAAED,KAAK,EAAE;IAA8B,CAAC;IACrDI,KAAK,EAAE;MAAEJ,KAAK,EAAE;IAA4B,CAAC;IAC7CsB,MAAM,EAAE;MACJE,SAAS,EAAE;QACPxB,KAAK,EAAE;UACH0B,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,KAAK;UACdC,UAAU,EAAE,KAAK;UACjBC,YAAY,EAAE,KAAK;UACnBzB,KAAK,EAAE;QACX;MACJ;IACJ;EACJ,CAAC;EACD6B,IAAI,EAAE;IACFX,MAAM,EAAE;MACJE,SAAS,EAAE;QACPxB,KAAK,EAAE;UACH0B,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,KAAK;UACdC,UAAU,EAAE,KAAK;UACjBC,YAAY,EAAE,KAAK;UACnBzB,KAAK,EAAE;QACX;MACJ;IACJ;EACJ,CAAC;EACD8B,OAAO,EAAE;IACLZ,MAAM,EAAE;MACJE,SAAS,EAAE;QACPxB,KAAK,EAAE;UACH0B,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,KAAK;UACdC,UAAU,EAAE,KAAK;UACjBC,YAAY,EAAE,KAAK;UACnBzB,KAAK,EAAE;QACX;MACJ;IACJ;EACJ,CAAC;EACD+B,OAAO,EAAE;IACLb,MAAM,EAAE;MACJE,SAAS,EAAE;QACPxB,KAAK,EAAE;UACH0B,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,KAAK;UACdC,UAAU,EAAE,KAAK;UACjBC,YAAY,EAAE,KAAK;UACnBzB,KAAK,EAAE;QACX;MACJ;IACJ;EACJ,CAAC;EACDgC,OAAO,EAAE;IACLd,MAAM,EAAE;MACJE,SAAS,EAAE;QACPxB,KAAK,EAAE;UACH0B,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,KAAK;UACdC,UAAU,EAAE,KAAK;UACjBC,YAAY,EAAE,KAAK;UACnBzB,KAAK,EAAE;QACX;MACJ;IACJ;EACJ;AACJ,CAAC;AAED,SAASN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}