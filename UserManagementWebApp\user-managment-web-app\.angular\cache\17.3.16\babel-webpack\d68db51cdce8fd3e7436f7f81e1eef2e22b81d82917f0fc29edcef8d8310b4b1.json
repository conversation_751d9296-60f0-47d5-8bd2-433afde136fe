{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst ADD_OAUTH_LISTENER = Symbol('oauth-listener');\nexport { ADD_OAUTH_LISTENER };", "map": {"version": 3, "names": ["ADD_OAUTH_LISTENER", "Symbol"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/singleton/constants.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst ADD_OAUTH_LISTENER = Symbol('oauth-listener');\n\nexport { ADD_OAUTH_LISTENER };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,kBAAkB,GAAGC,MAAM,CAAC,gBAAgB,CAAC;AAEnD,SAASD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}