{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { PlatformNotSupportedError } from '../errors/PlatformNotSupportedError.mjs';\nimport '../errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nclass KeyValueStorage {\n  constructor(storage) {\n    this.storage = storage;\n  }\n  /**\n   * This is used to set a specific item in storage\n   * @param {string} key - the key for the item\n   * @param {object} value - the value\n   * @returns {string} value that was set\n   */\n  setItem(key, value) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.storage) throw new PlatformNotSupportedError();\n      _this.storage.setItem(key, value);\n    })();\n  }\n  /**\n   * This is used to get a specific key from storage\n   * @param {string} key - the key for the item\n   * This is used to clear the storage\n   * @returns {string} the data item\n   */\n  getItem(key) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.storage) throw new PlatformNotSupportedError();\n      return _this2.storage.getItem(key);\n    })();\n  }\n  /**\n   * This is used to remove an item from storage\n   * @param {string} key - the key being set\n   * @returns {string} value - value that was deleted\n   */\n  removeItem(key) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.storage) throw new PlatformNotSupportedError();\n      _this3.storage.removeItem(key);\n    })();\n  }\n  /**\n   * This is used to clear the storage\n   * @returns {string} nothing\n   */\n  clear() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.storage) throw new PlatformNotSupportedError();\n      _this4.storage.clear();\n    })();\n  }\n}\nexport { KeyValueStorage };", "map": {"version": 3, "names": ["PlatformNotSupportedError", "KeyValueStorage", "constructor", "storage", "setItem", "key", "value", "_this", "_asyncToGenerator", "getItem", "_this2", "removeItem", "_this3", "clear", "_this4"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/storage/KeyValueStorage.mjs"], "sourcesContent": ["import { PlatformNotSupportedError } from '../errors/PlatformNotSupportedError.mjs';\nimport '../errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nclass KeyValueStorage {\n    constructor(storage) {\n        this.storage = storage;\n    }\n    /**\n     * This is used to set a specific item in storage\n     * @param {string} key - the key for the item\n     * @param {object} value - the value\n     * @returns {string} value that was set\n     */\n    async setItem(key, value) {\n        if (!this.storage)\n            throw new PlatformNotSupportedError();\n        this.storage.setItem(key, value);\n    }\n    /**\n     * This is used to get a specific key from storage\n     * @param {string} key - the key for the item\n     * This is used to clear the storage\n     * @returns {string} the data item\n     */\n    async getItem(key) {\n        if (!this.storage)\n            throw new PlatformNotSupportedError();\n        return this.storage.getItem(key);\n    }\n    /**\n     * This is used to remove an item from storage\n     * @param {string} key - the key being set\n     * @returns {string} value - value that was deleted\n     */\n    async removeItem(key) {\n        if (!this.storage)\n            throw new PlatformNotSupportedError();\n        this.storage.removeItem(key);\n    }\n    /**\n     * This is used to clear the storage\n     * @returns {string} nothing\n     */\n    async clear() {\n        if (!this.storage)\n            throw new PlatformNotSupportedError();\n        this.storage.clear();\n    }\n}\n\nexport { KeyValueStorage };\n"], "mappings": ";AAAA,SAASA,yBAAyB,QAAQ,yCAAyC;AACnF,OAAO,4BAA4B;;AAEnC;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;EACUC,OAAOA,CAACC,GAAG,EAAEC,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,IAAI,CAACD,KAAI,CAACJ,OAAO,EACb,MAAM,IAAIH,yBAAyB,CAAC,CAAC;MACzCO,KAAI,CAACJ,OAAO,CAACC,OAAO,CAACC,GAAG,EAAEC,KAAK,CAAC;IAAC;EACrC;EACA;AACJ;AACA;AACA;AACA;AACA;EACUG,OAAOA,CAACJ,GAAG,EAAE;IAAA,IAAAK,MAAA;IAAA,OAAAF,iBAAA;MACf,IAAI,CAACE,MAAI,CAACP,OAAO,EACb,MAAM,IAAIH,yBAAyB,CAAC,CAAC;MACzC,OAAOU,MAAI,CAACP,OAAO,CAACM,OAAO,CAACJ,GAAG,CAAC;IAAC;EACrC;EACA;AACJ;AACA;AACA;AACA;EACUM,UAAUA,CAACN,GAAG,EAAE;IAAA,IAAAO,MAAA;IAAA,OAAAJ,iBAAA;MAClB,IAAI,CAACI,MAAI,CAACT,OAAO,EACb,MAAM,IAAIH,yBAAyB,CAAC,CAAC;MACzCY,MAAI,CAACT,OAAO,CAACQ,UAAU,CAACN,GAAG,CAAC;IAAC;EACjC;EACA;AACJ;AACA;AACA;EACUQ,KAAKA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAN,iBAAA;MACV,IAAI,CAACM,MAAI,CAACX,OAAO,EACb,MAAM,IAAIH,yBAAyB,CAAC,CAAC;MACzCc,MAAI,CAACX,OAAO,CAACU,KAAK,CAAC,CAAC;IAAC;EACzB;AACJ;AAEA,SAASZ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}