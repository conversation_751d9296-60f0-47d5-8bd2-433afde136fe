{"ast": null, "code": "import { Sha256 } from '@aws-crypto/sha256-js';\nimport { toHex } from '@smithy/util-hex-encoding';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// TODO: V6 update to different crypto dependency?\n/**\n * Returns the hashed data a `Uint8Array`.\n *\n * @param key `SourceData` to be used as hashing key.\n * @param data Hashable `SourceData`.\n * @returns `Uint8Array` created from the data as input to a hash function.\n */\nconst getHashedData = (key, data) => {\n  const sha256 = new Sha256(key ?? undefined);\n  sha256.update(data);\n  // TODO: V6 flip to async digest\n  const hashedData = sha256.digestSync();\n  return hashedData;\n};\n/**\n * Returns the hashed data as a hex string.\n *\n * @param key `SourceData` to be used as hashing key.\n * @param data Hashable `SourceData`.\n * @returns String using lowercase hexadecimal characters created from the data as input to a hash function.\n *\n * @internal\n */\nconst getHashedDataAsHex = (key, data) => {\n  const hashedData = getHashedData(key, data);\n  return toHex(hashedData);\n};\nexport { getHashedData, getHashedDataAsHex };", "map": {"version": 3, "names": ["Sha256", "toHex", "getHashedData", "key", "data", "sha256", "undefined", "update", "hashedData", "digestSync", "getHashedDataAsHex"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/signer/signatureV4/utils/dataHashHelpers.mjs"], "sourcesContent": ["import { Sha256 } from '@aws-crypto/sha256-js';\nimport { toHex } from '@smithy/util-hex-encoding';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// TODO: V6 update to different crypto dependency?\n/**\n * Returns the hashed data a `Uint8Array`.\n *\n * @param key `SourceData` to be used as hashing key.\n * @param data Hashable `SourceData`.\n * @returns `Uint8Array` created from the data as input to a hash function.\n */\nconst getHashedData = (key, data) => {\n    const sha256 = new Sha256(key ?? undefined);\n    sha256.update(data);\n    // TODO: V6 flip to async digest\n    const hashedData = sha256.digestSync();\n    return hashedData;\n};\n/**\n * Returns the hashed data as a hex string.\n *\n * @param key `SourceData` to be used as hashing key.\n * @param data Hashable `SourceData`.\n * @returns String using lowercase hexadecimal characters created from the data as input to a hash function.\n *\n * @internal\n */\nconst getHashedDataAsHex = (key, data) => {\n    const hashedData = getHashedData(key, data);\n    return toHex(hashedData);\n};\n\nexport { getHashedData, getHashedDataAsHex };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,KAAK,QAAQ,2BAA2B;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAGA,CAACC,GAAG,EAAEC,IAAI,KAAK;EACjC,MAAMC,MAAM,GAAG,IAAIL,MAAM,CAACG,GAAG,IAAIG,SAAS,CAAC;EAC3CD,MAAM,CAACE,MAAM,CAACH,IAAI,CAAC;EACnB;EACA,MAAMI,UAAU,GAAGH,MAAM,CAACI,UAAU,CAAC,CAAC;EACtC,OAAOD,UAAU;AACrB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,kBAAkB,GAAGA,CAACP,GAAG,EAAEC,IAAI,KAAK;EACtC,MAAMI,UAAU,GAAGN,aAAa,CAACC,GAAG,EAAEC,IAAI,CAAC;EAC3C,OAAOH,KAAK,CAACO,UAAU,CAAC;AAC5B,CAAC;AAED,SAASN,aAAa,EAAEQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}