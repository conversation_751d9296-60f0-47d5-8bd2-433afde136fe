{"ast": null, "code": "import { amplifyUuid } from '../../../utils/amplifyUuid/index.mjs';\nimport { getCacheKey } from './getCacheKey.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createdEndpointIds = {};\n/**\n * Creates an endpoint id and guarantees multiple creations for a category returns the same uuid.\n *\n * @internal\n */\nconst createEndpointId = (appId, category) => {\n  const cacheKey = getCacheKey(appId, category);\n  if (!createdEndpointIds[cacheKey]) {\n    createdEndpointIds[cacheKey] = amplifyUuid();\n  }\n  return createdEndpointIds[cacheKey];\n};\n/**\n * Clears a created endpoint id for a category.\n *\n * @internal\n */\nconst clearCreatedEndpointId = (appId, category) => {\n  const cacheKey = getCacheKey(appId, category);\n  delete createdEndpointIds[cacheKey];\n};\nexport { clearCreatedEndpointId, createEndpointId };", "map": {"version": 3, "names": ["amplifyUuid", "get<PERSON><PERSON><PERSON><PERSON>", "createdEndpointIds", "createEndpointId", "appId", "category", "cache<PERSON>ey", "clearCreatedEndpointId"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/providers/pinpoint/utils/createEndpointId.mjs"], "sourcesContent": ["import { amplifyUuid } from '../../../utils/amplifyUuid/index.mjs';\nimport { getCacheKey } from './getCacheKey.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createdEndpointIds = {};\n/**\n * Creates an endpoint id and guarantees multiple creations for a category returns the same uuid.\n *\n * @internal\n */\nconst createEndpointId = (appId, category) => {\n    const cacheKey = getCacheKey(appId, category);\n    if (!createdEndpointIds[cacheKey]) {\n        createdEndpointIds[cacheKey] = amplifyUuid();\n    }\n    return createdEndpointIds[cacheKey];\n};\n/**\n * Clears a created endpoint id for a category.\n *\n * @internal\n */\nconst clearCreatedEndpointId = (appId, category) => {\n    const cacheKey = getCacheKey(appId, category);\n    delete createdEndpointIds[cacheKey];\n};\n\nexport { clearCreatedEndpointId, createEndpointId };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,sCAAsC;AAClE,SAASC,WAAW,QAAQ,mBAAmB;;AAE/C;AACA;AACA,MAAMC,kBAAkB,GAAG,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;EAC1C,MAAMC,QAAQ,GAAGL,WAAW,CAACG,KAAK,EAAEC,QAAQ,CAAC;EAC7C,IAAI,CAACH,kBAAkB,CAACI,QAAQ,CAAC,EAAE;IAC/BJ,kBAAkB,CAACI,QAAQ,CAAC,GAAGN,WAAW,CAAC,CAAC;EAChD;EACA,OAAOE,kBAAkB,CAACI,QAAQ,CAAC;AACvC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAGA,CAACH,KAAK,EAAEC,QAAQ,KAAK;EAChD,MAAMC,QAAQ,GAAGL,WAAW,CAACG,KAAK,EAAEC,QAAQ,CAAC;EAC7C,OAAOH,kBAAkB,CAACI,QAAQ,CAAC;AACvC,CAAC;AAED,SAASC,sBAAsB,EAAEJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}