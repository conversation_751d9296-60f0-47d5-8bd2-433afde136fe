{"ast": null, "code": "import { StorageCache } from './StorageCache.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst Cache = new StorageCache();\nexport { Cache };", "map": {"version": 3, "names": ["StorageCache", "<PERSON><PERSON>"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Cache/index.mjs"], "sourcesContent": ["import { StorageCache } from './StorageCache.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst Cache = new StorageCache();\n\nexport { Cache };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;;AAEjD;AACA;AACA,MAAMC,KAAK,GAAG,IAAID,YAAY,CAAC,CAAC;AAEhC,SAASC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}