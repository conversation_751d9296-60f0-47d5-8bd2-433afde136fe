{"ast": null, "code": "const dialcodeselect = {\n  height: {\n    value: '{space.relative.full.value}'\n  }\n};\nexport { dialcodeselect };", "map": {"version": 3, "names": ["dialcodeselect", "height", "value"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/dialCodeSelect.mjs"], "sourcesContent": ["const dialcodeselect = {\n    height: {\n        value: '{space.relative.full.value}',\n    },\n};\n\nexport { dialcodeselect };\n"], "mappings": "AAAA,MAAMA,cAAc,GAAG;EACnBC,MAAM,EAAE;IACJC,KAAK,EAAE;EACX;AACJ,CAAC;AAED,SAASF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}