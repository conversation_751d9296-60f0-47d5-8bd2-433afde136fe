{"ast": null, "code": "const phonenumberfield = {\n  color: {\n    value: '{components.fieldcontrol.color}'\n  },\n  borderColor: {\n    value: '{components.fieldcontrol.borderColor}'\n  },\n  fontSize: {\n    value: '{components.fieldcontrol.fontSize}'\n  },\n  _focus: {\n    borderColor: {\n      value: '{components.fieldcontrol._focus.borderColor}'\n    }\n  }\n};\nexport { phonenumberfield };", "map": {"version": 3, "names": ["phonenumberfield", "color", "value", "borderColor", "fontSize", "_focus"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/phoneNumberField.mjs"], "sourcesContent": ["const phonenumberfield = {\n    color: { value: '{components.fieldcontrol.color}' },\n    borderColor: { value: '{components.fieldcontrol.borderColor}' },\n    fontSize: { value: '{components.fieldcontrol.fontSize}' },\n    _focus: {\n        borderColor: { value: '{components.fieldcontrol._focus.borderColor}' },\n    },\n};\n\nexport { phonenumberfield };\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAG;EACrBC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAkC,CAAC;EACnDC,WAAW,EAAE;IAAED,KAAK,EAAE;EAAwC,CAAC;EAC/DE,QAAQ,EAAE;IAAEF,KAAK,EAAE;EAAqC,CAAC;EACzDG,MAAM,EAAE;IACJF,WAAW,EAAE;MAAED,KAAK,EAAE;IAA+C;EACzE;AACJ,CAAC;AAED,SAASF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}