{"ast": null, "code": "const checkbox = {\n  cursor: {\n    value: 'pointer'\n  },\n  alignItems: {\n    value: 'center'\n  },\n  _disabled: {\n    cursor: {\n      value: 'not-allowed'\n    }\n  },\n  button: {\n    position: {\n      value: 'relative'\n    },\n    alignItems: {\n      value: 'center'\n    },\n    justifyContent: {\n      value: 'center'\n    },\n    color: {\n      value: '{colors.font.inverse.value}'\n    },\n    before: {\n      width: {\n        value: '100%'\n      },\n      height: {\n        value: '100%'\n      },\n      borderWidth: {\n        value: '{borderWidths.medium.value}'\n      },\n      borderRadius: {\n        value: '20%'\n      },\n      borderStyle: {\n        value: 'solid'\n      },\n      borderColor: {\n        value: '{colors.border.primary.value}'\n      }\n    },\n    _focus: {\n      outlineColor: {\n        value: '{colors.transparent.value}'\n      },\n      outlineStyle: {\n        value: 'solid'\n      },\n      outlineWidth: {\n        value: '{outlineWidths.medium.value}'\n      },\n      outlineOffset: {\n        value: '{outlineOffsets.medium.value}'\n      },\n      borderColor: {\n        value: '{colors.border.focus.value}'\n      },\n      boxShadow: {\n        value: '{components.fieldcontrol._focus.boxShadow.value}'\n      }\n    },\n    _disabled: {\n      borderColor: {\n        value: '{colors.border.disabled.value}'\n      }\n    },\n    _error: {\n      borderColor: {\n        value: '{colors.border.error.value}'\n      },\n      _focus: {\n        borderColor: {\n          value: '{colors.border.error.value}'\n        },\n        boxShadow: {\n          value: '{components.fieldcontrol._error._focus.boxShadow.value}'\n        }\n      }\n    }\n  },\n  icon: {\n    backgroundColor: {\n      value: '{colors.primary.80.value}'\n    },\n    borderRadius: {\n      value: '20%'\n    },\n    opacity: {\n      value: '{opacities.0.value}'\n    },\n    transform: {\n      value: 'scale(0)'\n    },\n    transitionProperty: {\n      value: 'all'\n    },\n    transitionDuration: {\n      value: '{time.short.value}'\n    },\n    transitionTimingFunction: {\n      value: 'ease-in-out'\n    },\n    _checked: {\n      opacity: {\n        value: '{opacities.100.value}'\n      },\n      transform: {\n        value: 'scale(1)'\n      },\n      _disabled: {\n        backgroundColor: {\n          value: '{colors.background.disabled.value}'\n        }\n      }\n    },\n    _indeterminate: {\n      opacity: {\n        value: '{opacities.100.value}'\n      },\n      transform: {\n        value: 'scale(1)'\n      },\n      _disabled: {\n        backgroundColor: {\n          value: '{colors.background.disabled.value}'\n        }\n      }\n    }\n  },\n  label: {\n    color: {\n      value: '{components.text.color.value}'\n    },\n    _disabled: {\n      color: {\n        value: '{colors.font.disabled.value}'\n      }\n    }\n  }\n};\nexport { checkbox };", "map": {"version": 3, "names": ["checkbox", "cursor", "value", "alignItems", "_disabled", "button", "position", "justifyContent", "color", "before", "width", "height", "borderWidth", "borderRadius", "borderStyle", "borderColor", "_focus", "outlineColor", "outlineStyle", "outlineWidth", "outlineOffset", "boxShadow", "_error", "icon", "backgroundColor", "opacity", "transform", "transitionProperty", "transitionDuration", "transitionTimingFunction", "_checked", "_indeterminate", "label"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/checkbox.mjs"], "sourcesContent": ["const checkbox = {\n    cursor: { value: 'pointer' },\n    alignItems: { value: 'center' },\n    _disabled: {\n        cursor: {\n            value: 'not-allowed',\n        },\n    },\n    button: {\n        position: { value: 'relative' },\n        alignItems: { value: 'center' },\n        justifyContent: { value: 'center' },\n        color: { value: '{colors.font.inverse.value}' },\n        before: {\n            width: { value: '100%' },\n            height: { value: '100%' },\n            borderWidth: { value: '{borderWidths.medium.value}' },\n            borderRadius: { value: '20%' },\n            borderStyle: { value: 'solid' },\n            borderColor: { value: '{colors.border.primary.value}' },\n        },\n        _focus: {\n            outlineColor: { value: '{colors.transparent.value}' },\n            outlineStyle: { value: 'solid' },\n            outlineWidth: { value: '{outlineWidths.medium.value}' },\n            outlineOffset: { value: '{outlineOffsets.medium.value}' },\n            borderColor: { value: '{colors.border.focus.value}' },\n            boxShadow: { value: '{components.fieldcontrol._focus.boxShadow.value}' },\n        },\n        _disabled: {\n            borderColor: { value: '{colors.border.disabled.value}' },\n        },\n        _error: {\n            borderColor: { value: '{colors.border.error.value}' },\n            _focus: {\n                borderColor: { value: '{colors.border.error.value}' },\n                boxShadow: {\n                    value: '{components.fieldcontrol._error._focus.boxShadow.value}',\n                },\n            },\n        },\n    },\n    icon: {\n        backgroundColor: { value: '{colors.primary.80.value}' },\n        borderRadius: { value: '20%' },\n        opacity: { value: '{opacities.0.value}' },\n        transform: { value: 'scale(0)' },\n        transitionProperty: { value: 'all' },\n        transitionDuration: { value: '{time.short.value}' },\n        transitionTimingFunction: { value: 'ease-in-out' },\n        _checked: {\n            opacity: { value: '{opacities.100.value}' },\n            transform: { value: 'scale(1)' },\n            _disabled: {\n                backgroundColor: { value: '{colors.background.disabled.value}' },\n            },\n        },\n        _indeterminate: {\n            opacity: { value: '{opacities.100.value}' },\n            transform: { value: 'scale(1)' },\n            _disabled: {\n                backgroundColor: { value: '{colors.background.disabled.value}' },\n            },\n        },\n    },\n    label: {\n        color: { value: '{components.text.color.value}' },\n        _disabled: {\n            color: {\n                value: '{colors.font.disabled.value}',\n            },\n        },\n    },\n};\n\nexport { checkbox };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAG;EACbC,MAAM,EAAE;IAAEC,KAAK,EAAE;EAAU,CAAC;EAC5BC,UAAU,EAAE;IAAED,KAAK,EAAE;EAAS,CAAC;EAC/BE,SAAS,EAAE;IACPH,MAAM,EAAE;MACJC,KAAK,EAAE;IACX;EACJ,CAAC;EACDG,MAAM,EAAE;IACJC,QAAQ,EAAE;MAAEJ,KAAK,EAAE;IAAW,CAAC;IAC/BC,UAAU,EAAE;MAAED,KAAK,EAAE;IAAS,CAAC;IAC/BK,cAAc,EAAE;MAAEL,KAAK,EAAE;IAAS,CAAC;IACnCM,KAAK,EAAE;MAAEN,KAAK,EAAE;IAA8B,CAAC;IAC/CO,MAAM,EAAE;MACJC,KAAK,EAAE;QAAER,KAAK,EAAE;MAAO,CAAC;MACxBS,MAAM,EAAE;QAAET,KAAK,EAAE;MAAO,CAAC;MACzBU,WAAW,EAAE;QAAEV,KAAK,EAAE;MAA8B,CAAC;MACrDW,YAAY,EAAE;QAAEX,KAAK,EAAE;MAAM,CAAC;MAC9BY,WAAW,EAAE;QAAEZ,KAAK,EAAE;MAAQ,CAAC;MAC/Ba,WAAW,EAAE;QAAEb,KAAK,EAAE;MAAgC;IAC1D,CAAC;IACDc,MAAM,EAAE;MACJC,YAAY,EAAE;QAAEf,KAAK,EAAE;MAA6B,CAAC;MACrDgB,YAAY,EAAE;QAAEhB,KAAK,EAAE;MAAQ,CAAC;MAChCiB,YAAY,EAAE;QAAEjB,KAAK,EAAE;MAA+B,CAAC;MACvDkB,aAAa,EAAE;QAAElB,KAAK,EAAE;MAAgC,CAAC;MACzDa,WAAW,EAAE;QAAEb,KAAK,EAAE;MAA8B,CAAC;MACrDmB,SAAS,EAAE;QAAEnB,KAAK,EAAE;MAAmD;IAC3E,CAAC;IACDE,SAAS,EAAE;MACPW,WAAW,EAAE;QAAEb,KAAK,EAAE;MAAiC;IAC3D,CAAC;IACDoB,MAAM,EAAE;MACJP,WAAW,EAAE;QAAEb,KAAK,EAAE;MAA8B,CAAC;MACrDc,MAAM,EAAE;QACJD,WAAW,EAAE;UAAEb,KAAK,EAAE;QAA8B,CAAC;QACrDmB,SAAS,EAAE;UACPnB,KAAK,EAAE;QACX;MACJ;IACJ;EACJ,CAAC;EACDqB,IAAI,EAAE;IACFC,eAAe,EAAE;MAAEtB,KAAK,EAAE;IAA4B,CAAC;IACvDW,YAAY,EAAE;MAAEX,KAAK,EAAE;IAAM,CAAC;IAC9BuB,OAAO,EAAE;MAAEvB,KAAK,EAAE;IAAsB,CAAC;IACzCwB,SAAS,EAAE;MAAExB,KAAK,EAAE;IAAW,CAAC;IAChCyB,kBAAkB,EAAE;MAAEzB,KAAK,EAAE;IAAM,CAAC;IACpC0B,kBAAkB,EAAE;MAAE1B,KAAK,EAAE;IAAqB,CAAC;IACnD2B,wBAAwB,EAAE;MAAE3B,KAAK,EAAE;IAAc,CAAC;IAClD4B,QAAQ,EAAE;MACNL,OAAO,EAAE;QAAEvB,KAAK,EAAE;MAAwB,CAAC;MAC3CwB,SAAS,EAAE;QAAExB,KAAK,EAAE;MAAW,CAAC;MAChCE,SAAS,EAAE;QACPoB,eAAe,EAAE;UAAEtB,KAAK,EAAE;QAAqC;MACnE;IACJ,CAAC;IACD6B,cAAc,EAAE;MACZN,OAAO,EAAE;QAAEvB,KAAK,EAAE;MAAwB,CAAC;MAC3CwB,SAAS,EAAE;QAAExB,KAAK,EAAE;MAAW,CAAC;MAChCE,SAAS,EAAE;QACPoB,eAAe,EAAE;UAAEtB,KAAK,EAAE;QAAqC;MACnE;IACJ;EACJ,CAAC;EACD8B,KAAK,EAAE;IACHxB,KAAK,EAAE;MAAEN,KAAK,EAAE;IAAgC,CAAC;IACjDE,SAAS,EAAE;MACPI,KAAK,EAAE;QACHN,KAAK,EAAE;MACX;IACJ;EACJ;AACJ,CAAC;AAED,SAASF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}