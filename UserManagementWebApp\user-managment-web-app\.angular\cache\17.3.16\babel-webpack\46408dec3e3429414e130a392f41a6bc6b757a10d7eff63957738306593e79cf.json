{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { toAttributeType } from '../utils/apiHelpers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createUpdateUserAttributesClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createUpdateUserAttributesClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Updates user's attributes while authenticated.\n *\n * @param input - The UpdateUserAttributesInput object\n * @returns UpdateUserAttributesOutput\n * @throws - {@link UpdateUserAttributesException}\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nconst updateUserAttributes = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (input) {\n    const {\n      userAttributes,\n      options\n    } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    const clientMetadata = options?.clientMetadata;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolEndpoint,\n      userPoolId\n    } = authConfig;\n    const {\n      tokens\n    } = yield fetchAuthSession({\n      forceRefresh: false\n    });\n    assertAuthTokens(tokens);\n    const updateUserAttributesClient = createUpdateUserAttributesClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const {\n      CodeDeliveryDetailsList\n    } = yield updateUserAttributesClient({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.UpdateUserAttributes)\n    }, {\n      AccessToken: tokens.accessToken.toString(),\n      ClientMetadata: clientMetadata,\n      UserAttributes: toAttributeType(userAttributes)\n    });\n    return {\n      ...getConfirmedAttributes(userAttributes),\n      ...getUnConfirmedAttributes(CodeDeliveryDetailsList)\n    };\n  });\n  return function updateUserAttributes(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nfunction getConfirmedAttributes(attributes) {\n  const confirmedAttributes = {};\n  Object.keys(attributes)?.forEach(key => {\n    confirmedAttributes[key] = {\n      isUpdated: true,\n      nextStep: {\n        updateAttributeStep: 'DONE'\n      }\n    };\n  });\n  return confirmedAttributes;\n}\nfunction getUnConfirmedAttributes(codeDeliveryDetailsList) {\n  const unConfirmedAttributes = {};\n  codeDeliveryDetailsList?.forEach(codeDeliveryDetails => {\n    const {\n      AttributeName,\n      DeliveryMedium,\n      Destination\n    } = codeDeliveryDetails;\n    if (AttributeName) unConfirmedAttributes[AttributeName] = {\n      isUpdated: false,\n      nextStep: {\n        updateAttributeStep: 'CONFIRM_ATTRIBUTE_WITH_CODE',\n        codeDeliveryDetails: {\n          attributeName: AttributeName,\n          deliveryMedium: DeliveryMedium,\n          destination: Destination\n        }\n      }\n    };\n  });\n  return unConfirmedAttributes;\n}\nexport { updateUserAttributes };", "map": {"version": 3, "names": ["Amplify", "fetchAuthSession", "assertTokenProviderConfig", "AuthAction", "assertAuthTokens", "getRegionFromUserPoolId", "toAttributeType", "getAuthUserAgentValue", "createUpdateUserAttributesClient", "createCognitoUserPoolEndpointResolver", "updateUserAttributes", "_ref", "_asyncToGenerator", "input", "userAttributes", "options", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "clientMetadata", "userPoolEndpoint", "userPoolId", "tokens", "forceRefresh", "updateUserAttributesClient", "endpointResolver", "endpointOverride", "CodeDeliveryDetailsList", "region", "userAgentValue", "UpdateUserAttributes", "AccessToken", "accessToken", "toString", "ClientMetadata", "UserAttributes", "getConfirmedAttributes", "getUnConfirmedAttributes", "_x", "apply", "arguments", "attributes", "confirmedAttributes", "Object", "keys", "for<PERSON>ach", "key", "isUpdated", "nextStep", "updateAttributeStep", "codeDeliveryDetailsList", "unConfirmedAttributes", "codeDeliveryDetails", "AttributeName", "DeliveryMedium", "Destination", "attributeName", "deliveryMedium", "destination"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/updateUserAttributes.mjs"], "sourcesContent": ["import { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { toAttributeType } from '../utils/apiHelpers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createUpdateUserAttributesClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createUpdateUserAttributesClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Updates user's attributes while authenticated.\n *\n * @param input - The UpdateUserAttributesInput object\n * @returns UpdateUserAttributesOutput\n * @throws - {@link UpdateUserAttributesException}\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nconst updateUserAttributes = async (input) => {\n    const { userAttributes, options } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    const clientMetadata = options?.clientMetadata;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolEndpoint, userPoolId } = authConfig;\n    const { tokens } = await fetchAuthSession({ forceRefresh: false });\n    assertAuthTokens(tokens);\n    const updateUserAttributesClient = createUpdateUserAttributesClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const { CodeDeliveryDetailsList } = await updateUserAttributesClient({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.UpdateUserAttributes),\n    }, {\n        AccessToken: tokens.accessToken.toString(),\n        ClientMetadata: clientMetadata,\n        UserAttributes: toAttributeType(userAttributes),\n    });\n    return {\n        ...getConfirmedAttributes(userAttributes),\n        ...getUnConfirmedAttributes(CodeDeliveryDetailsList),\n    };\n};\nfunction getConfirmedAttributes(attributes) {\n    const confirmedAttributes = {};\n    Object.keys(attributes)?.forEach(key => {\n        confirmedAttributes[key] = {\n            isUpdated: true,\n            nextStep: {\n                updateAttributeStep: 'DONE',\n            },\n        };\n    });\n    return confirmedAttributes;\n}\nfunction getUnConfirmedAttributes(codeDeliveryDetailsList) {\n    const unConfirmedAttributes = {};\n    codeDeliveryDetailsList?.forEach(codeDeliveryDetails => {\n        const { AttributeName, DeliveryMedium, Destination } = codeDeliveryDetails;\n        if (AttributeName)\n            unConfirmedAttributes[AttributeName] = {\n                isUpdated: false,\n                nextStep: {\n                    updateAttributeStep: 'CONFIRM_ATTRIBUTE_WITH_CODE',\n                    codeDeliveryDetails: {\n                        attributeName: AttributeName,\n                        deliveryMedium: DeliveryMedium,\n                        destination: Destination,\n                    },\n                },\n            };\n    });\n    return unConfirmedAttributes;\n}\n\nexport { updateUserAttributes };\n"], "mappings": ";AAAA,SAASA,OAAO,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC7D,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,sCAAsC;AAC7C,OAAO,qBAAqB;AAC5B,SAASC,gCAAgC,QAAQ,2GAA2G;AAC5J,SAASC,qCAAqC,QAAQ,wDAAwD;;AAE9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,KAAK,EAAK;IAC1C,MAAM;MAAEC,cAAc;MAAEC;IAAQ,CAAC,GAAGF,KAAK;IACzC,MAAMG,UAAU,GAAGhB,OAAO,CAACiB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpD,MAAMC,cAAc,GAAGL,OAAO,EAAEK,cAAc;IAC9ClB,yBAAyB,CAACc,UAAU,CAAC;IACrC,MAAM;MAAEK,gBAAgB;MAAEC;IAAW,CAAC,GAAGN,UAAU;IACnD,MAAM;MAAEO;IAAO,CAAC,SAAStB,gBAAgB,CAAC;MAAEuB,YAAY,EAAE;IAAM,CAAC,CAAC;IAClEpB,gBAAgB,CAACmB,MAAM,CAAC;IACxB,MAAME,0BAA0B,GAAGjB,gCAAgC,CAAC;MAChEkB,gBAAgB,EAAEjB,qCAAqC,CAAC;QACpDkB,gBAAgB,EAAEN;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAM;MAAEO;IAAwB,CAAC,SAASH,0BAA0B,CAAC;MACjEI,MAAM,EAAExB,uBAAuB,CAACiB,UAAU,CAAC;MAC3CQ,cAAc,EAAEvB,qBAAqB,CAACJ,UAAU,CAAC4B,oBAAoB;IACzE,CAAC,EAAE;MACCC,WAAW,EAAET,MAAM,CAACU,WAAW,CAACC,QAAQ,CAAC,CAAC;MAC1CC,cAAc,EAAEf,cAAc;MAC9BgB,cAAc,EAAE9B,eAAe,CAACQ,cAAc;IAClD,CAAC,CAAC;IACF,OAAO;MACH,GAAGuB,sBAAsB,CAACvB,cAAc,CAAC;MACzC,GAAGwB,wBAAwB,CAACV,uBAAuB;IACvD,CAAC;EACL,CAAC;EAAA,gBAzBKlB,oBAAoBA,CAAA6B,EAAA;IAAA,OAAA5B,IAAA,CAAA6B,KAAA,OAAAC,SAAA;EAAA;AAAA,GAyBzB;AACD,SAASJ,sBAAsBA,CAACK,UAAU,EAAE;EACxC,MAAMC,mBAAmB,GAAG,CAAC,CAAC;EAC9BC,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,EAAEI,OAAO,CAACC,GAAG,IAAI;IACpCJ,mBAAmB,CAACI,GAAG,CAAC,GAAG;MACvBC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE;QACNC,mBAAmB,EAAE;MACzB;IACJ,CAAC;EACL,CAAC,CAAC;EACF,OAAOP,mBAAmB;AAC9B;AACA,SAASL,wBAAwBA,CAACa,uBAAuB,EAAE;EACvD,MAAMC,qBAAqB,GAAG,CAAC,CAAC;EAChCD,uBAAuB,EAAEL,OAAO,CAACO,mBAAmB,IAAI;IACpD,MAAM;MAAEC,aAAa;MAAEC,cAAc;MAAEC;IAAY,CAAC,GAAGH,mBAAmB;IAC1E,IAAIC,aAAa,EACbF,qBAAqB,CAACE,aAAa,CAAC,GAAG;MACnCN,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE;QACNC,mBAAmB,EAAE,6BAA6B;QAClDG,mBAAmB,EAAE;UACjBI,aAAa,EAAEH,aAAa;UAC5BI,cAAc,EAAEH,cAAc;UAC9BI,WAAW,EAAEH;QACjB;MACJ;IACJ,CAAC;EACT,CAAC,CAAC;EACF,OAAOJ,qBAAqB;AAChC;AAEA,SAAS1C,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}