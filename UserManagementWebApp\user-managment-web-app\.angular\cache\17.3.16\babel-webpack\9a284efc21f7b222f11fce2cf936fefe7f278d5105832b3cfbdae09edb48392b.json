{"ast": null, "code": "import { UnverifiedContactMethodType } from './user.mjs';\nconst isUnverifiedContactMethodType = value => Object.values(UnverifiedContactMethodType).findIndex(val => val === value) >= 0;\nexport { isUnverifiedContactMethodType };", "map": {"version": 3, "names": ["UnverifiedContactMethodType", "isUnverifiedContactMethodType", "value", "Object", "values", "findIndex", "val"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/types/authenticator/utils.mjs"], "sourcesContent": ["import { UnverifiedContactMethodType } from './user.mjs';\n\nconst isUnverifiedContactMethodType = (value) => Object.values(UnverifiedContactMethodType).findIndex((val) => val === value) >= 0;\n\nexport { isUnverifiedContactMethodType };\n"], "mappings": "AAAA,SAASA,2BAA2B,QAAQ,YAAY;AAExD,MAAMC,6BAA6B,GAAIC,KAAK,IAAKC,MAAM,CAACC,MAAM,CAACJ,2BAA2B,CAAC,CAACK,SAAS,CAAEC,GAAG,IAAKA,GAAG,KAAKJ,KAAK,CAAC,IAAI,CAAC;AAElI,SAASD,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}