{"ast": null, "code": "import { ConsoleLogger } from './Logger/ConsoleLogger.mjs';\nimport { AmplifyError } from './errors/AmplifyError.mjs';\nimport './types/errors.mjs';\nimport './errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('parseAWSExports');\nconst authTypeMapping = {\n  API_KEY: 'apiKey',\n  AWS_IAM: 'iam',\n  AMAZON_COGNITO_USER_POOLS: 'userPool',\n  OPENID_CONNECT: 'oidc',\n  NONE: 'none',\n  AWS_LAMBDA: 'lambda',\n  // `LAMBDA` is an incorrect value that was added during the v6 rewrite.\n  // Keeping it as a valid value until v7 to prevent breaking customers who might\n  // be relying on it as a workaround.\n  // ref: https://github.com/aws-amplify/amplify-js/pull/12922\n  // TODO: @v7 remove next line\n  LAMBDA: 'lambda'\n};\n/**\n * Converts the object imported from `aws-exports.js` or `amplifyconfiguration.json` files generated by\n * the Amplify CLI into an object that conforms to the {@link ResourcesConfig}.\n *\n * @param config A configuration object imported  from `aws-exports.js` or `amplifyconfiguration.json`.\n *\n * @returns An object that conforms to the {@link ResourcesConfig} .\n */\nconst parseAWSExports = (config = {}) => {\n  if (!Object.prototype.hasOwnProperty.call(config, 'aws_project_region')) {\n    throw new AmplifyError({\n      name: 'InvalidParameterException',\n      message: 'Invalid config parameter.',\n      recoverySuggestion: 'Ensure passing the config object imported from  `amplifyconfiguration.json`.'\n    });\n  }\n  const {\n    aws_appsync_apiKey,\n    aws_appsync_authenticationType,\n    aws_appsync_graphqlEndpoint,\n    aws_appsync_region,\n    aws_bots_config,\n    aws_cognito_identity_pool_id,\n    aws_cognito_sign_up_verification_method,\n    aws_cognito_mfa_configuration,\n    aws_cognito_mfa_types,\n    aws_cognito_password_protection_settings,\n    aws_cognito_verification_mechanisms,\n    aws_cognito_signup_attributes,\n    aws_cognito_social_providers,\n    aws_cognito_username_attributes,\n    aws_mandatory_sign_in,\n    aws_mobile_analytics_app_id,\n    aws_mobile_analytics_app_region,\n    aws_user_files_s3_bucket,\n    aws_user_files_s3_bucket_region,\n    aws_user_files_s3_dangerously_connect_to_http_endpoint_for_testing,\n    aws_user_pools_id,\n    aws_user_pools_web_client_id,\n    geo,\n    oauth,\n    predictions,\n    aws_cloud_logic_custom,\n    Notifications,\n    modelIntrospection\n  } = config;\n  const amplifyConfig = {};\n  // Analytics\n  if (aws_mobile_analytics_app_id) {\n    amplifyConfig.Analytics = {\n      Pinpoint: {\n        appId: aws_mobile_analytics_app_id,\n        region: aws_mobile_analytics_app_region\n      }\n    };\n  }\n  // Notifications\n  const {\n    InAppMessaging,\n    Push\n  } = Notifications ?? {};\n  if (InAppMessaging?.AWSPinpoint || Push?.AWSPinpoint) {\n    if (InAppMessaging?.AWSPinpoint) {\n      const {\n        appId,\n        region\n      } = InAppMessaging.AWSPinpoint;\n      amplifyConfig.Notifications = {\n        InAppMessaging: {\n          Pinpoint: {\n            appId,\n            region\n          }\n        }\n      };\n    }\n    if (Push?.AWSPinpoint) {\n      const {\n        appId,\n        region\n      } = Push.AWSPinpoint;\n      amplifyConfig.Notifications = {\n        ...amplifyConfig.Notifications,\n        PushNotification: {\n          Pinpoint: {\n            appId,\n            region\n          }\n        }\n      };\n    }\n  }\n  // Interactions\n  if (Array.isArray(aws_bots_config)) {\n    amplifyConfig.Interactions = {\n      LexV1: Object.fromEntries(aws_bots_config.map(bot => [bot.name, bot]))\n    };\n  }\n  // API\n  if (aws_appsync_graphqlEndpoint) {\n    const defaultAuthMode = authTypeMapping[aws_appsync_authenticationType];\n    if (!defaultAuthMode) {\n      logger.debug(`Invalid authentication type ${aws_appsync_authenticationType}. Falling back to IAM.`);\n    }\n    amplifyConfig.API = {\n      GraphQL: {\n        endpoint: aws_appsync_graphqlEndpoint,\n        apiKey: aws_appsync_apiKey,\n        region: aws_appsync_region,\n        defaultAuthMode: defaultAuthMode ?? 'iam'\n      }\n    };\n    if (modelIntrospection) {\n      amplifyConfig.API.GraphQL.modelIntrospection = modelIntrospection;\n    }\n  }\n  // Auth\n  const mfaConfig = aws_cognito_mfa_configuration ? {\n    status: aws_cognito_mfa_configuration && aws_cognito_mfa_configuration.toLowerCase(),\n    totpEnabled: aws_cognito_mfa_types?.includes('TOTP') ?? false,\n    smsEnabled: aws_cognito_mfa_types?.includes('SMS') ?? false\n  } : undefined;\n  const passwordFormatConfig = aws_cognito_password_protection_settings ? {\n    minLength: aws_cognito_password_protection_settings.passwordPolicyMinLength,\n    requireLowercase: aws_cognito_password_protection_settings.passwordPolicyCharacters?.includes('REQUIRES_LOWERCASE') ?? false,\n    requireUppercase: aws_cognito_password_protection_settings.passwordPolicyCharacters?.includes('REQUIRES_UPPERCASE') ?? false,\n    requireNumbers: aws_cognito_password_protection_settings.passwordPolicyCharacters?.includes('REQUIRES_NUMBERS') ?? false,\n    requireSpecialCharacters: aws_cognito_password_protection_settings.passwordPolicyCharacters?.includes('REQUIRES_SYMBOLS') ?? false\n  } : undefined;\n  const mergedUserAttributes = Array.from(new Set([...(aws_cognito_verification_mechanisms ?? []), ...(aws_cognito_signup_attributes ?? [])]));\n  const userAttributes = mergedUserAttributes.reduce((attributes, key) => ({\n    ...attributes,\n    // All user attributes generated by the CLI are required\n    [key.toLowerCase()]: {\n      required: true\n    }\n  }), {});\n  const loginWithEmailEnabled = aws_cognito_username_attributes?.includes('EMAIL') ?? false;\n  const loginWithPhoneEnabled = aws_cognito_username_attributes?.includes('PHONE_NUMBER') ?? false;\n  if (aws_cognito_identity_pool_id || aws_user_pools_id) {\n    amplifyConfig.Auth = {\n      Cognito: {\n        identityPoolId: aws_cognito_identity_pool_id,\n        allowGuestAccess: aws_mandatory_sign_in !== 'enable',\n        signUpVerificationMethod: aws_cognito_sign_up_verification_method,\n        userAttributes,\n        userPoolClientId: aws_user_pools_web_client_id,\n        userPoolId: aws_user_pools_id,\n        mfa: mfaConfig,\n        passwordFormat: passwordFormatConfig,\n        loginWith: {\n          username: !(loginWithEmailEnabled || loginWithPhoneEnabled),\n          email: loginWithEmailEnabled,\n          phone: loginWithPhoneEnabled\n        }\n      }\n    };\n  }\n  const hasOAuthConfig = oauth ? Object.keys(oauth).length > 0 : false;\n  const hasSocialProviderConfig = aws_cognito_social_providers ? aws_cognito_social_providers.length > 0 : false;\n  if (amplifyConfig.Auth && hasOAuthConfig) {\n    amplifyConfig.Auth.Cognito.loginWith = {\n      ...amplifyConfig.Auth.Cognito.loginWith,\n      oauth: {\n        ...getOAuthConfig(oauth),\n        ...(hasSocialProviderConfig && {\n          providers: parseSocialProviders(aws_cognito_social_providers)\n        })\n      }\n    };\n  }\n  // Storage\n  if (aws_user_files_s3_bucket) {\n    amplifyConfig.Storage = {\n      S3: {\n        bucket: aws_user_files_s3_bucket,\n        region: aws_user_files_s3_bucket_region,\n        dangerouslyConnectToHttpEndpointForTesting: aws_user_files_s3_dangerously_connect_to_http_endpoint_for_testing\n      }\n    };\n  }\n  // Geo\n  if (geo) {\n    const {\n      amazon_location_service\n    } = geo;\n    amplifyConfig.Geo = {\n      LocationService: {\n        maps: amazon_location_service.maps,\n        geofenceCollections: amazon_location_service.geofenceCollections,\n        searchIndices: amazon_location_service.search_indices,\n        region: amazon_location_service.region\n      }\n    };\n  }\n  // REST API\n  if (aws_cloud_logic_custom) {\n    amplifyConfig.API = {\n      ...amplifyConfig.API,\n      REST: aws_cloud_logic_custom.reduce((acc, api) => {\n        const {\n          name,\n          endpoint,\n          region,\n          service\n        } = api;\n        return {\n          ...acc,\n          [name]: {\n            endpoint,\n            ...(service ? {\n              service\n            } : undefined),\n            ...(region ? {\n              region\n            } : undefined)\n          }\n        };\n      }, {})\n    };\n  }\n  // Predictions\n  if (predictions) {\n    // map VoiceId from speechGenerator defaults to voiceId\n    const {\n      VoiceId: voiceId\n    } = predictions?.convert?.speechGenerator?.defaults ?? {};\n    amplifyConfig.Predictions = voiceId ? {\n      ...predictions,\n      convert: {\n        ...predictions.convert,\n        speechGenerator: {\n          ...predictions.convert.speechGenerator,\n          defaults: {\n            voiceId\n          }\n        }\n      }\n    } : predictions;\n  }\n  return amplifyConfig;\n};\nconst getRedirectUrl = redirectStr => redirectStr?.split(',') ?? [];\nconst getOAuthConfig = ({\n  domain,\n  scope,\n  redirectSignIn,\n  redirectSignOut,\n  responseType\n}) => ({\n  domain,\n  scopes: scope,\n  redirectSignIn: getRedirectUrl(redirectSignIn),\n  redirectSignOut: getRedirectUrl(redirectSignOut),\n  responseType\n});\nconst parseSocialProviders = aws_cognito_social_providers => {\n  return aws_cognito_social_providers.map(provider => {\n    const updatedProvider = provider.toLowerCase();\n    return updatedProvider.charAt(0).toUpperCase() + updatedProvider.slice(1);\n  });\n};\nexport { parseAWSExports };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AmplifyError", "logger", "authTypeMapping", "API_KEY", "AWS_IAM", "AMAZON_COGNITO_USER_POOLS", "OPENID_CONNECT", "NONE", "AWS_LAMBDA", "LAMBDA", "parseAWSExports", "config", "Object", "prototype", "hasOwnProperty", "call", "name", "message", "recoverySuggestion", "aws_appsync_apiKey", "aws_appsync_authenticationType", "aws_appsync_graphqlEndpoint", "aws_appsync_region", "aws_bots_config", "aws_cognito_identity_pool_id", "aws_cognito_sign_up_verification_method", "aws_cognito_mfa_configuration", "aws_cognito_mfa_types", "aws_cognito_password_protection_settings", "aws_cognito_verification_mechanisms", "aws_cognito_signup_attributes", "aws_cognito_social_providers", "aws_cognito_username_attributes", "aws_mandatory_sign_in", "aws_mobile_analytics_app_id", "aws_mobile_analytics_app_region", "aws_user_files_s3_bucket", "aws_user_files_s3_bucket_region", "aws_user_files_s3_dangerously_connect_to_http_endpoint_for_testing", "aws_user_pools_id", "aws_user_pools_web_client_id", "geo", "o<PERSON>h", "predictions", "aws_cloud_logic_custom", "Notifications", "modelIntrospection", "amplifyConfig", "Analytics", "Pinpoint", "appId", "region", "InAppMessaging", "<PERSON><PERSON>", "AWSPinpoint", "PushNotification", "Array", "isArray", "Interactions", "LexV1", "fromEntries", "map", "bot", "defaultAuthMode", "debug", "API", "GraphQL", "endpoint", "<PERSON><PERSON><PERSON><PERSON>", "mfaConfig", "status", "toLowerCase", "totpEnabled", "includes", "smsEnabled", "undefined", "passwordFormatConfig", "<PERSON><PERSON><PERSON><PERSON>", "passwordPolicyMinLength", "requireLowercase", "passwordPolicyCharacters", "requireUppercase", "requireNumbers", "requireSpecialCharacters", "mergedUserAttributes", "from", "Set", "userAttributes", "reduce", "attributes", "key", "required", "loginWithEmailEnabled", "loginWithPhoneEnabled", "<PERSON><PERSON>", "Cognito", "identityPoolId", "allowGuestAccess", "signUpVerificationMethod", "userPoolClientId", "userPoolId", "mfa", "passwordFormat", "loginWith", "username", "email", "phone", "hasOAuthConfig", "keys", "length", "hasSocialProviderConfig", "getOAuthConfig", "providers", "parseSocialProviders", "Storage", "S3", "bucket", "dangerouslyConnectToHttpEndpointForTesting", "amazon_location_service", "Geo", "LocationService", "maps", "geofenceCollections", "searchIndices", "search_indices", "REST", "acc", "api", "service", "VoiceId", "voiceId", "convert", "speechGenerator", "defaults", "Predictions", "getRedirectUrl", "redirectStr", "split", "domain", "scope", "redirectSignIn", "redirectSignOut", "responseType", "scopes", "provider", "updatedProvider", "char<PERSON>t", "toUpperCase", "slice"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/parseAWSExports.mjs"], "sourcesContent": ["import { ConsoleLogger } from './Logger/ConsoleLogger.mjs';\nimport { AmplifyError } from './errors/AmplifyError.mjs';\nimport './types/errors.mjs';\nimport './errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('parseAWSExports');\nconst authTypeMapping = {\n    API_KEY: 'apiKey',\n    AWS_IAM: 'iam',\n    AMAZON_COGNITO_USER_POOLS: 'userPool',\n    OPENID_CONNECT: 'oidc',\n    NONE: 'none',\n    AWS_LAMBDA: 'lambda',\n    // `LAMBDA` is an incorrect value that was added during the v6 rewrite.\n    // Keeping it as a valid value until v7 to prevent breaking customers who might\n    // be relying on it as a workaround.\n    // ref: https://github.com/aws-amplify/amplify-js/pull/12922\n    // TODO: @v7 remove next line\n    LAMBDA: 'lambda',\n};\n/**\n * Converts the object imported from `aws-exports.js` or `amplifyconfiguration.json` files generated by\n * the Amplify CLI into an object that conforms to the {@link ResourcesConfig}.\n *\n * @param config A configuration object imported  from `aws-exports.js` or `amplifyconfiguration.json`.\n *\n * @returns An object that conforms to the {@link ResourcesConfig} .\n */\nconst parseAWSExports = (config = {}) => {\n    if (!Object.prototype.hasOwnProperty.call(config, 'aws_project_region')) {\n        throw new AmplifyError({\n            name: 'InvalidParameterException',\n            message: 'Invalid config parameter.',\n            recoverySuggestion: 'Ensure passing the config object imported from  `amplifyconfiguration.json`.',\n        });\n    }\n    const { aws_appsync_apiKey, aws_appsync_authenticationType, aws_appsync_graphqlEndpoint, aws_appsync_region, aws_bots_config, aws_cognito_identity_pool_id, aws_cognito_sign_up_verification_method, aws_cognito_mfa_configuration, aws_cognito_mfa_types, aws_cognito_password_protection_settings, aws_cognito_verification_mechanisms, aws_cognito_signup_attributes, aws_cognito_social_providers, aws_cognito_username_attributes, aws_mandatory_sign_in, aws_mobile_analytics_app_id, aws_mobile_analytics_app_region, aws_user_files_s3_bucket, aws_user_files_s3_bucket_region, aws_user_files_s3_dangerously_connect_to_http_endpoint_for_testing, aws_user_pools_id, aws_user_pools_web_client_id, geo, oauth, predictions, aws_cloud_logic_custom, Notifications, modelIntrospection, } = config;\n    const amplifyConfig = {};\n    // Analytics\n    if (aws_mobile_analytics_app_id) {\n        amplifyConfig.Analytics = {\n            Pinpoint: {\n                appId: aws_mobile_analytics_app_id,\n                region: aws_mobile_analytics_app_region,\n            },\n        };\n    }\n    // Notifications\n    const { InAppMessaging, Push } = Notifications ?? {};\n    if (InAppMessaging?.AWSPinpoint || Push?.AWSPinpoint) {\n        if (InAppMessaging?.AWSPinpoint) {\n            const { appId, region } = InAppMessaging.AWSPinpoint;\n            amplifyConfig.Notifications = {\n                InAppMessaging: {\n                    Pinpoint: {\n                        appId,\n                        region,\n                    },\n                },\n            };\n        }\n        if (Push?.AWSPinpoint) {\n            const { appId, region } = Push.AWSPinpoint;\n            amplifyConfig.Notifications = {\n                ...amplifyConfig.Notifications,\n                PushNotification: {\n                    Pinpoint: {\n                        appId,\n                        region,\n                    },\n                },\n            };\n        }\n    }\n    // Interactions\n    if (Array.isArray(aws_bots_config)) {\n        amplifyConfig.Interactions = {\n            LexV1: Object.fromEntries(aws_bots_config.map(bot => [bot.name, bot])),\n        };\n    }\n    // API\n    if (aws_appsync_graphqlEndpoint) {\n        const defaultAuthMode = authTypeMapping[aws_appsync_authenticationType];\n        if (!defaultAuthMode) {\n            logger.debug(`Invalid authentication type ${aws_appsync_authenticationType}. Falling back to IAM.`);\n        }\n        amplifyConfig.API = {\n            GraphQL: {\n                endpoint: aws_appsync_graphqlEndpoint,\n                apiKey: aws_appsync_apiKey,\n                region: aws_appsync_region,\n                defaultAuthMode: defaultAuthMode ?? 'iam',\n            },\n        };\n        if (modelIntrospection) {\n            amplifyConfig.API.GraphQL.modelIntrospection = modelIntrospection;\n        }\n    }\n    // Auth\n    const mfaConfig = aws_cognito_mfa_configuration\n        ? {\n            status: aws_cognito_mfa_configuration &&\n                aws_cognito_mfa_configuration.toLowerCase(),\n            totpEnabled: aws_cognito_mfa_types?.includes('TOTP') ?? false,\n            smsEnabled: aws_cognito_mfa_types?.includes('SMS') ?? false,\n        }\n        : undefined;\n    const passwordFormatConfig = aws_cognito_password_protection_settings\n        ? {\n            minLength: aws_cognito_password_protection_settings.passwordPolicyMinLength,\n            requireLowercase: aws_cognito_password_protection_settings.passwordPolicyCharacters?.includes('REQUIRES_LOWERCASE') ?? false,\n            requireUppercase: aws_cognito_password_protection_settings.passwordPolicyCharacters?.includes('REQUIRES_UPPERCASE') ?? false,\n            requireNumbers: aws_cognito_password_protection_settings.passwordPolicyCharacters?.includes('REQUIRES_NUMBERS') ?? false,\n            requireSpecialCharacters: aws_cognito_password_protection_settings.passwordPolicyCharacters?.includes('REQUIRES_SYMBOLS') ?? false,\n        }\n        : undefined;\n    const mergedUserAttributes = Array.from(new Set([\n        ...(aws_cognito_verification_mechanisms ?? []),\n        ...(aws_cognito_signup_attributes ?? []),\n    ]));\n    const userAttributes = mergedUserAttributes.reduce((attributes, key) => ({\n        ...attributes,\n        // All user attributes generated by the CLI are required\n        [key.toLowerCase()]: { required: true },\n    }), {});\n    const loginWithEmailEnabled = aws_cognito_username_attributes?.includes('EMAIL') ?? false;\n    const loginWithPhoneEnabled = aws_cognito_username_attributes?.includes('PHONE_NUMBER') ?? false;\n    if (aws_cognito_identity_pool_id || aws_user_pools_id) {\n        amplifyConfig.Auth = {\n            Cognito: {\n                identityPoolId: aws_cognito_identity_pool_id,\n                allowGuestAccess: aws_mandatory_sign_in !== 'enable',\n                signUpVerificationMethod: aws_cognito_sign_up_verification_method,\n                userAttributes,\n                userPoolClientId: aws_user_pools_web_client_id,\n                userPoolId: aws_user_pools_id,\n                mfa: mfaConfig,\n                passwordFormat: passwordFormatConfig,\n                loginWith: {\n                    username: !(loginWithEmailEnabled || loginWithPhoneEnabled),\n                    email: loginWithEmailEnabled,\n                    phone: loginWithPhoneEnabled,\n                },\n            },\n        };\n    }\n    const hasOAuthConfig = oauth ? Object.keys(oauth).length > 0 : false;\n    const hasSocialProviderConfig = aws_cognito_social_providers\n        ? aws_cognito_social_providers.length > 0\n        : false;\n    if (amplifyConfig.Auth && hasOAuthConfig) {\n        amplifyConfig.Auth.Cognito.loginWith = {\n            ...amplifyConfig.Auth.Cognito.loginWith,\n            oauth: {\n                ...getOAuthConfig(oauth),\n                ...(hasSocialProviderConfig && {\n                    providers: parseSocialProviders(aws_cognito_social_providers),\n                }),\n            },\n        };\n    }\n    // Storage\n    if (aws_user_files_s3_bucket) {\n        amplifyConfig.Storage = {\n            S3: {\n                bucket: aws_user_files_s3_bucket,\n                region: aws_user_files_s3_bucket_region,\n                dangerouslyConnectToHttpEndpointForTesting: aws_user_files_s3_dangerously_connect_to_http_endpoint_for_testing,\n            },\n        };\n    }\n    // Geo\n    if (geo) {\n        const { amazon_location_service } = geo;\n        amplifyConfig.Geo = {\n            LocationService: {\n                maps: amazon_location_service.maps,\n                geofenceCollections: amazon_location_service.geofenceCollections,\n                searchIndices: amazon_location_service.search_indices,\n                region: amazon_location_service.region,\n            },\n        };\n    }\n    // REST API\n    if (aws_cloud_logic_custom) {\n        amplifyConfig.API = {\n            ...amplifyConfig.API,\n            REST: aws_cloud_logic_custom.reduce((acc, api) => {\n                const { name, endpoint, region, service } = api;\n                return {\n                    ...acc,\n                    [name]: {\n                        endpoint,\n                        ...(service ? { service } : undefined),\n                        ...(region ? { region } : undefined),\n                    },\n                };\n            }, {}),\n        };\n    }\n    // Predictions\n    if (predictions) {\n        // map VoiceId from speechGenerator defaults to voiceId\n        const { VoiceId: voiceId } = predictions?.convert?.speechGenerator?.defaults ?? {};\n        amplifyConfig.Predictions = voiceId\n            ? {\n                ...predictions,\n                convert: {\n                    ...predictions.convert,\n                    speechGenerator: {\n                        ...predictions.convert.speechGenerator,\n                        defaults: { voiceId },\n                    },\n                },\n            }\n            : predictions;\n    }\n    return amplifyConfig;\n};\nconst getRedirectUrl = (redirectStr) => redirectStr?.split(',') ?? [];\nconst getOAuthConfig = ({ domain, scope, redirectSignIn, redirectSignOut, responseType, }) => ({\n    domain,\n    scopes: scope,\n    redirectSignIn: getRedirectUrl(redirectSignIn),\n    redirectSignOut: getRedirectUrl(redirectSignOut),\n    responseType,\n});\nconst parseSocialProviders = (aws_cognito_social_providers) => {\n    return aws_cognito_social_providers.map((provider) => {\n        const updatedProvider = provider.toLowerCase();\n        return updatedProvider.charAt(0).toUpperCase() + updatedProvider.slice(1);\n    });\n};\n\nexport { parseAWSExports };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,YAAY,QAAQ,2BAA2B;AACxD,OAAO,oBAAoB;AAC3B,OAAO,2BAA2B;;AAElC;AACA;AACA,MAAMC,MAAM,GAAG,IAAIF,aAAa,CAAC,iBAAiB,CAAC;AACnD,MAAMG,eAAe,GAAG;EACpBC,OAAO,EAAE,QAAQ;EACjBC,OAAO,EAAE,KAAK;EACdC,yBAAyB,EAAE,UAAU;EACrCC,cAAc,EAAE,MAAM;EACtBC,IAAI,EAAE,MAAM;EACZC,UAAU,EAAE,QAAQ;EACpB;EACA;EACA;EACA;EACA;EACAC,MAAM,EAAE;AACZ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;EACrC,IAAI,CAACC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAE,oBAAoB,CAAC,EAAE;IACrE,MAAM,IAAIX,YAAY,CAAC;MACnBgB,IAAI,EAAE,2BAA2B;MACjCC,OAAO,EAAE,2BAA2B;MACpCC,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EACA,MAAM;IAAEC,kBAAkB;IAAEC,8BAA8B;IAAEC,2BAA2B;IAAEC,kBAAkB;IAAEC,eAAe;IAAEC,4BAA4B;IAAEC,uCAAuC;IAAEC,6BAA6B;IAAEC,qBAAqB;IAAEC,wCAAwC;IAAEC,mCAAmC;IAAEC,6BAA6B;IAAEC,4BAA4B;IAAEC,+BAA+B;IAAEC,qBAAqB;IAAEC,2BAA2B;IAAEC,+BAA+B;IAAEC,wBAAwB;IAAEC,+BAA+B;IAAEC,kEAAkE;IAAEC,iBAAiB;IAAEC,4BAA4B;IAAEC,GAAG;IAAEC,KAAK;IAAEC,WAAW;IAAEC,sBAAsB;IAAEC,aAAa;IAAEC;EAAoB,CAAC,GAAGnC,MAAM;EAC3wB,MAAMoC,aAAa,GAAG,CAAC,CAAC;EACxB;EACA,IAAIb,2BAA2B,EAAE;IAC7Ba,aAAa,CAACC,SAAS,GAAG;MACtBC,QAAQ,EAAE;QACNC,KAAK,EAAEhB,2BAA2B;QAClCiB,MAAM,EAAEhB;MACZ;IACJ,CAAC;EACL;EACA;EACA,MAAM;IAAEiB,cAAc;IAAEC;EAAK,CAAC,GAAGR,aAAa,IAAI,CAAC,CAAC;EACpD,IAAIO,cAAc,EAAEE,WAAW,IAAID,IAAI,EAAEC,WAAW,EAAE;IAClD,IAAIF,cAAc,EAAEE,WAAW,EAAE;MAC7B,MAAM;QAAEJ,KAAK;QAAEC;MAAO,CAAC,GAAGC,cAAc,CAACE,WAAW;MACpDP,aAAa,CAACF,aAAa,GAAG;QAC1BO,cAAc,EAAE;UACZH,QAAQ,EAAE;YACNC,KAAK;YACLC;UACJ;QACJ;MACJ,CAAC;IACL;IACA,IAAIE,IAAI,EAAEC,WAAW,EAAE;MACnB,MAAM;QAAEJ,KAAK;QAAEC;MAAO,CAAC,GAAGE,IAAI,CAACC,WAAW;MAC1CP,aAAa,CAACF,aAAa,GAAG;QAC1B,GAAGE,aAAa,CAACF,aAAa;QAC9BU,gBAAgB,EAAE;UACdN,QAAQ,EAAE;YACNC,KAAK;YACLC;UACJ;QACJ;MACJ,CAAC;IACL;EACJ;EACA;EACA,IAAIK,KAAK,CAACC,OAAO,CAAClC,eAAe,CAAC,EAAE;IAChCwB,aAAa,CAACW,YAAY,GAAG;MACzBC,KAAK,EAAE/C,MAAM,CAACgD,WAAW,CAACrC,eAAe,CAACsC,GAAG,CAACC,GAAG,IAAI,CAACA,GAAG,CAAC9C,IAAI,EAAE8C,GAAG,CAAC,CAAC;IACzE,CAAC;EACL;EACA;EACA,IAAIzC,2BAA2B,EAAE;IAC7B,MAAM0C,eAAe,GAAG7D,eAAe,CAACkB,8BAA8B,CAAC;IACvE,IAAI,CAAC2C,eAAe,EAAE;MAClB9D,MAAM,CAAC+D,KAAK,CAAC,+BAA+B5C,8BAA8B,wBAAwB,CAAC;IACvG;IACA2B,aAAa,CAACkB,GAAG,GAAG;MAChBC,OAAO,EAAE;QACLC,QAAQ,EAAE9C,2BAA2B;QACrC+C,MAAM,EAAEjD,kBAAkB;QAC1BgC,MAAM,EAAE7B,kBAAkB;QAC1ByC,eAAe,EAAEA,eAAe,IAAI;MACxC;IACJ,CAAC;IACD,IAAIjB,kBAAkB,EAAE;MACpBC,aAAa,CAACkB,GAAG,CAACC,OAAO,CAACpB,kBAAkB,GAAGA,kBAAkB;IACrE;EACJ;EACA;EACA,MAAMuB,SAAS,GAAG3C,6BAA6B,GACzC;IACE4C,MAAM,EAAE5C,6BAA6B,IACjCA,6BAA6B,CAAC6C,WAAW,CAAC,CAAC;IAC/CC,WAAW,EAAE7C,qBAAqB,EAAE8C,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK;IAC7DC,UAAU,EAAE/C,qBAAqB,EAAE8C,QAAQ,CAAC,KAAK,CAAC,IAAI;EAC1D,CAAC,GACCE,SAAS;EACf,MAAMC,oBAAoB,GAAGhD,wCAAwC,GAC/D;IACEiD,SAAS,EAAEjD,wCAAwC,CAACkD,uBAAuB;IAC3EC,gBAAgB,EAAEnD,wCAAwC,CAACoD,wBAAwB,EAAEP,QAAQ,CAAC,oBAAoB,CAAC,IAAI,KAAK;IAC5HQ,gBAAgB,EAAErD,wCAAwC,CAACoD,wBAAwB,EAAEP,QAAQ,CAAC,oBAAoB,CAAC,IAAI,KAAK;IAC5HS,cAAc,EAAEtD,wCAAwC,CAACoD,wBAAwB,EAAEP,QAAQ,CAAC,kBAAkB,CAAC,IAAI,KAAK;IACxHU,wBAAwB,EAAEvD,wCAAwC,CAACoD,wBAAwB,EAAEP,QAAQ,CAAC,kBAAkB,CAAC,IAAI;EACjI,CAAC,GACCE,SAAS;EACf,MAAMS,oBAAoB,GAAG5B,KAAK,CAAC6B,IAAI,CAAC,IAAIC,GAAG,CAAC,CAC5C,IAAIzD,mCAAmC,IAAI,EAAE,CAAC,EAC9C,IAAIC,6BAA6B,IAAI,EAAE,CAAC,CAC3C,CAAC,CAAC;EACH,MAAMyD,cAAc,GAAGH,oBAAoB,CAACI,MAAM,CAAC,CAACC,UAAU,EAAEC,GAAG,MAAM;IACrE,GAAGD,UAAU;IACb;IACA,CAACC,GAAG,CAACnB,WAAW,CAAC,CAAC,GAAG;MAAEoB,QAAQ,EAAE;IAAK;EAC1C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACP,MAAMC,qBAAqB,GAAG5D,+BAA+B,EAAEyC,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK;EACzF,MAAMoB,qBAAqB,GAAG7D,+BAA+B,EAAEyC,QAAQ,CAAC,cAAc,CAAC,IAAI,KAAK;EAChG,IAAIjD,4BAA4B,IAAIe,iBAAiB,EAAE;IACnDQ,aAAa,CAAC+C,IAAI,GAAG;MACjBC,OAAO,EAAE;QACLC,cAAc,EAAExE,4BAA4B;QAC5CyE,gBAAgB,EAAEhE,qBAAqB,KAAK,QAAQ;QACpDiE,wBAAwB,EAAEzE,uCAAuC;QACjE8D,cAAc;QACdY,gBAAgB,EAAE3D,4BAA4B;QAC9C4D,UAAU,EAAE7D,iBAAiB;QAC7B8D,GAAG,EAAEhC,SAAS;QACdiC,cAAc,EAAE1B,oBAAoB;QACpC2B,SAAS,EAAE;UACPC,QAAQ,EAAE,EAAEZ,qBAAqB,IAAIC,qBAAqB,CAAC;UAC3DY,KAAK,EAAEb,qBAAqB;UAC5Bc,KAAK,EAAEb;QACX;MACJ;IACJ,CAAC;EACL;EACA,MAAMc,cAAc,GAAGjE,KAAK,GAAG9B,MAAM,CAACgG,IAAI,CAAClE,KAAK,CAAC,CAACmE,MAAM,GAAG,CAAC,GAAG,KAAK;EACpE,MAAMC,uBAAuB,GAAG/E,4BAA4B,GACtDA,4BAA4B,CAAC8E,MAAM,GAAG,CAAC,GACvC,KAAK;EACX,IAAI9D,aAAa,CAAC+C,IAAI,IAAIa,cAAc,EAAE;IACtC5D,aAAa,CAAC+C,IAAI,CAACC,OAAO,CAACQ,SAAS,GAAG;MACnC,GAAGxD,aAAa,CAAC+C,IAAI,CAACC,OAAO,CAACQ,SAAS;MACvC7D,KAAK,EAAE;QACH,GAAGqE,cAAc,CAACrE,KAAK,CAAC;QACxB,IAAIoE,uBAAuB,IAAI;UAC3BE,SAAS,EAAEC,oBAAoB,CAAClF,4BAA4B;QAChE,CAAC;MACL;IACJ,CAAC;EACL;EACA;EACA,IAAIK,wBAAwB,EAAE;IAC1BW,aAAa,CAACmE,OAAO,GAAG;MACpBC,EAAE,EAAE;QACAC,MAAM,EAAEhF,wBAAwB;QAChCe,MAAM,EAAEd,+BAA+B;QACvCgF,0CAA0C,EAAE/E;MAChD;IACJ,CAAC;EACL;EACA;EACA,IAAIG,GAAG,EAAE;IACL,MAAM;MAAE6E;IAAwB,CAAC,GAAG7E,GAAG;IACvCM,aAAa,CAACwE,GAAG,GAAG;MAChBC,eAAe,EAAE;QACbC,IAAI,EAAEH,uBAAuB,CAACG,IAAI;QAClCC,mBAAmB,EAAEJ,uBAAuB,CAACI,mBAAmB;QAChEC,aAAa,EAAEL,uBAAuB,CAACM,cAAc;QACrDzE,MAAM,EAAEmE,uBAAuB,CAACnE;MACpC;IACJ,CAAC;EACL;EACA;EACA,IAAIP,sBAAsB,EAAE;IACxBG,aAAa,CAACkB,GAAG,GAAG;MAChB,GAAGlB,aAAa,CAACkB,GAAG;MACpB4D,IAAI,EAAEjF,sBAAsB,CAAC4C,MAAM,CAAC,CAACsC,GAAG,EAAEC,GAAG,KAAK;QAC9C,MAAM;UAAE/G,IAAI;UAAEmD,QAAQ;UAAEhB,MAAM;UAAE6E;QAAQ,CAAC,GAAGD,GAAG;QAC/C,OAAO;UACH,GAAGD,GAAG;UACN,CAAC9G,IAAI,GAAG;YACJmD,QAAQ;YACR,IAAI6D,OAAO,GAAG;cAAEA;YAAQ,CAAC,GAAGrD,SAAS,CAAC;YACtC,IAAIxB,MAAM,GAAG;cAAEA;YAAO,CAAC,GAAGwB,SAAS;UACvC;QACJ,CAAC;MACL,CAAC,EAAE,CAAC,CAAC;IACT,CAAC;EACL;EACA;EACA,IAAIhC,WAAW,EAAE;IACb;IACA,MAAM;MAAEsF,OAAO,EAAEC;IAAQ,CAAC,GAAGvF,WAAW,EAAEwF,OAAO,EAAEC,eAAe,EAAEC,QAAQ,IAAI,CAAC,CAAC;IAClFtF,aAAa,CAACuF,WAAW,GAAGJ,OAAO,GAC7B;MACE,GAAGvF,WAAW;MACdwF,OAAO,EAAE;QACL,GAAGxF,WAAW,CAACwF,OAAO;QACtBC,eAAe,EAAE;UACb,GAAGzF,WAAW,CAACwF,OAAO,CAACC,eAAe;UACtCC,QAAQ,EAAE;YAAEH;UAAQ;QACxB;MACJ;IACJ,CAAC,GACCvF,WAAW;EACrB;EACA,OAAOI,aAAa;AACxB,CAAC;AACD,MAAMwF,cAAc,GAAIC,WAAW,IAAKA,WAAW,EAAEC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE;AACrE,MAAM1B,cAAc,GAAGA,CAAC;EAAE2B,MAAM;EAAEC,KAAK;EAAEC,cAAc;EAAEC,eAAe;EAAEC;AAAc,CAAC,MAAM;EAC3FJ,MAAM;EACNK,MAAM,EAAEJ,KAAK;EACbC,cAAc,EAAEL,cAAc,CAACK,cAAc,CAAC;EAC9CC,eAAe,EAAEN,cAAc,CAACM,eAAe,CAAC;EAChDC;AACJ,CAAC,CAAC;AACF,MAAM7B,oBAAoB,GAAIlF,4BAA4B,IAAK;EAC3D,OAAOA,4BAA4B,CAAC8B,GAAG,CAAEmF,QAAQ,IAAK;IAClD,MAAMC,eAAe,GAAGD,QAAQ,CAACzE,WAAW,CAAC,CAAC;IAC9C,OAAO0E,eAAe,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,eAAe,CAACG,KAAK,CAAC,CAAC,CAAC;EAC7E,CAAC,CAAC;AACN,CAAC;AAED,SAAS1I,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}