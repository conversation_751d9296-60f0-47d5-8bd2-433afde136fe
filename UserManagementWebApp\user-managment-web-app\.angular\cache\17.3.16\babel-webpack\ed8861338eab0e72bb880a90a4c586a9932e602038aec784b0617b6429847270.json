{"ast": null, "code": "import { getSignedHeaders } from './utils/getSignedHeaders.mjs';\nimport { getSigningValues } from './utils/getSigningValues.mjs';\nimport { HOST_HEADER, AMZ_DATE_HEADER, TOKEN_HEADER, SHA256_ALGORITHM_IDENTIFIER, AUTH_HEADER } from './constants.mjs';\nimport { getSignature } from './utils/getSignature.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Given a `HttpRequest`, returns a Signature Version 4 signed `HttpRequest`.\n *\n * @param request `HttpRequest` to be signed.\n * @param signRequestOptions `SignRequestOptions` object containing values used to construct the signature.\n * @returns A `HttpRequest` with authentication headers which can grant temporary access to AWS resources.\n */\nconst signRequest = (request, options) => {\n  const signingValues = getSigningValues(options);\n  const {\n    accessKeyId,\n    credentialScope,\n    longDate,\n    sessionToken\n  } = signingValues;\n  // create the request to sign\n  const headers = {\n    ...request.headers\n  };\n  headers[HOST_HEADER] = request.url.host;\n  headers[AMZ_DATE_HEADER] = longDate;\n  if (sessionToken) {\n    headers[TOKEN_HEADER] = sessionToken;\n  }\n  const requestToSign = {\n    ...request,\n    headers\n  };\n  // calculate and add the signature to the request\n  const signature = getSignature(requestToSign, signingValues);\n  const credentialEntry = `Credential=${accessKeyId}/${credentialScope}`;\n  const signedHeadersEntry = `SignedHeaders=${getSignedHeaders(headers)}`;\n  const signatureEntry = `Signature=${signature}`;\n  headers[AUTH_HEADER] = `${SHA256_ALGORITHM_IDENTIFIER} ${credentialEntry}, ${signedHeadersEntry}, ${signatureEntry}`;\n  return requestToSign;\n};\nexport { signRequest };", "map": {"version": 3, "names": ["getSignedHeaders", "getSigningValues", "HOST_HEADER", "AMZ_DATE_HEADER", "TOKEN_HEADER", "SHA256_ALGORITHM_IDENTIFIER", "AUTH_HEADER", "getSignature", "signRequest", "request", "options", "<PERSON><PERSON><PERSON><PERSON>", "accessKeyId", "credentialScope", "longDate", "sessionToken", "headers", "url", "host", "requestToSign", "signature", "credentialEntry", "signedHeadersEntry", "signatureEntry"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/signer/signatureV4/signRequest.mjs"], "sourcesContent": ["import { getSignedHeaders } from './utils/getSignedHeaders.mjs';\nimport { getSigningValues } from './utils/getSigningValues.mjs';\nimport { HOST_HEADER, AMZ_DATE_HEADER, TOKEN_HEADER, SHA256_ALGORITHM_IDENTIFIER, AUTH_HEADER } from './constants.mjs';\nimport { getSignature } from './utils/getSignature.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Given a `HttpRequest`, returns a Signature Version 4 signed `HttpRequest`.\n *\n * @param request `HttpRequest` to be signed.\n * @param signRequestOptions `SignRequestOptions` object containing values used to construct the signature.\n * @returns A `HttpRequest` with authentication headers which can grant temporary access to AWS resources.\n */\nconst signRequest = (request, options) => {\n    const signingValues = getSigningValues(options);\n    const { accessKeyId, credentialScope, longDate, sessionToken } = signingValues;\n    // create the request to sign\n    const headers = { ...request.headers };\n    headers[HOST_HEADER] = request.url.host;\n    headers[AMZ_DATE_HEADER] = longDate;\n    if (sessionToken) {\n        headers[TOKEN_HEADER] = sessionToken;\n    }\n    const requestToSign = { ...request, headers };\n    // calculate and add the signature to the request\n    const signature = getSignature(requestToSign, signingValues);\n    const credentialEntry = `Credential=${accessKeyId}/${credentialScope}`;\n    const signedHeadersEntry = `SignedHeaders=${getSignedHeaders(headers)}`;\n    const signatureEntry = `Signature=${signature}`;\n    headers[AUTH_HEADER] =\n        `${SHA256_ALGORITHM_IDENTIFIER} ${credentialEntry}, ${signedHeadersEntry}, ${signatureEntry}`;\n    return requestToSign;\n};\n\nexport { signRequest };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,WAAW,EAAEC,eAAe,EAAEC,YAAY,EAAEC,2BAA2B,EAAEC,WAAW,QAAQ,iBAAiB;AACtH,SAASC,YAAY,QAAQ,0BAA0B;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAGA,CAACC,OAAO,EAAEC,OAAO,KAAK;EACtC,MAAMC,aAAa,GAAGV,gBAAgB,CAACS,OAAO,CAAC;EAC/C,MAAM;IAAEE,WAAW;IAAEC,eAAe;IAAEC,QAAQ;IAAEC;EAAa,CAAC,GAAGJ,aAAa;EAC9E;EACA,MAAMK,OAAO,GAAG;IAAE,GAAGP,OAAO,CAACO;EAAQ,CAAC;EACtCA,OAAO,CAACd,WAAW,CAAC,GAAGO,OAAO,CAACQ,GAAG,CAACC,IAAI;EACvCF,OAAO,CAACb,eAAe,CAAC,GAAGW,QAAQ;EACnC,IAAIC,YAAY,EAAE;IACdC,OAAO,CAACZ,YAAY,CAAC,GAAGW,YAAY;EACxC;EACA,MAAMI,aAAa,GAAG;IAAE,GAAGV,OAAO;IAAEO;EAAQ,CAAC;EAC7C;EACA,MAAMI,SAAS,GAAGb,YAAY,CAACY,aAAa,EAAER,aAAa,CAAC;EAC5D,MAAMU,eAAe,GAAG,cAAcT,WAAW,IAAIC,eAAe,EAAE;EACtE,MAAMS,kBAAkB,GAAG,iBAAiBtB,gBAAgB,CAACgB,OAAO,CAAC,EAAE;EACvE,MAAMO,cAAc,GAAG,aAAaH,SAAS,EAAE;EAC/CJ,OAAO,CAACV,WAAW,CAAC,GAChB,GAAGD,2BAA2B,IAAIgB,eAAe,KAAKC,kBAAkB,KAAKC,cAAc,EAAE;EACjG,OAAOJ,aAAa;AACxB,CAAC;AAED,SAASX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}