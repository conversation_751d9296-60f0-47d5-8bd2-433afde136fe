{"ast": null, "code": "import { windowExists } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction webDetect() {\n  return windowExists();\n}\nexport { webDetect };", "map": {"version": 3, "names": ["windowExists", "webDetect"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Platform/detection/Web.mjs"], "sourcesContent": ["import { windowExists } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction webDetect() {\n    return windowExists();\n}\n\nexport { webDetect };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,eAAe;;AAE5C;AACA;AACA,SAASC,SAASA,CAAA,EAAG;EACjB,OAAOD,YAAY,CAAC,CAAC;AACzB;AAEA,SAASC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}