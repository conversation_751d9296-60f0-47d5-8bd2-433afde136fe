{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/SaltboxActionableGrid/Saltbox-Data-App/saltbox-data-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { NgIf, NgClass } from '@angular/common';\nimport { deepCopy } from 'src/app/shared/utilities/copy.functions';\nimport { JSONSchemaParam } from 'src/app/core/models/json-schema-param';\nimport { CRUDActions, ModifierSourceType } from 'src/app/core/enums/shared';\nimport { HttpErrorResponse } from '@angular/common/http';\nimport { UserActivity } from 'src/app/shared/models/user-activity';\nimport { CommunicationToken } from 'src/app/core/models/communication-service-type';\nimport { DynamicFormLayoutAreas, DynamicFormPostSaveActions } from '../enums/dynamic-form';\nimport { RecordsSaveEvent } from 'src/app/shared/models/records-save-event';\nimport { AssetEventType } from 'src/app/shared/enums/asset-workflow';\nimport { getDefaultValue } from 'src/app/shared/utilities/datatype.functions';\nimport { DB_PRIMARY_KEY } from 'src/app/shared/constants/record.const';\nimport { firstValueFrom } from 'rxjs';\nimport { UserPermissionLevels } from 'src/app/core/enums/shared';\nimport { AssetUrlPipe } from '../../shared/pipes/asset-url.pipe';\nimport { BlockUIModule } from 'primeng/blockui';\nimport { ValidationResultsDialogComponent } from '../../business-validation-results/validation-results-dialog.component';\nimport { PageErrorBlockComponent } from '../../shared/page-error-block/page-error-block.component';\nimport { ButtonModule } from 'primeng/button';\nimport { SignatureButtonComponent } from '../../signature/signature-button/signature-button.component';\nimport { SignaturesRendererComponent } from '../../signature/signatures-renderer/signatures-renderer.component';\nimport { DynamicFormBodyComponent } from '../dynamic-form-body/dynamic-form-body.component';\nimport { DynamicFormSectionComponent } from '../dynamic-form-section/dynamic-form-section.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/datastores.service\";\nimport * as i3 from \"src/app/core/services/change-tracking.service\";\nimport * as i4 from \"../services/dynamic-forms.service\";\nimport * as i5 from \"src/app/core/services/notification.service\";\nimport * as i6 from \"src/app/core/services/user-activity.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"src/app/core/services/post-save-events.service\";\nimport * as i9 from \"src/app/core/services/projects.service\";\nimport * as i10 from \"primeng/button\";\nimport * as i11 from \"primeng/blockui\";\nimport * as i12 from \"src/app/core/models/communication-service-type\";\nconst _c0 = a0 => ({\n  \"max-h-unset\": a0\n});\nconst _c1 = a0 => ({\n  \"card\": a0\n});\nconst _c2 = a0 => ({\n  \"mr-1\": a0\n});\nconst _c3 = a0 => ({\n  \"mt-3\": a0\n});\nconst _c4 = a0 => [a0];\nfunction DataEntryFormComponent_div_2_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵelement(1, \"app-dynamic-form-section\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c2, (ctx_r0.dynamicForm == null ? null : ctx_r0.dynamicForm.layout == null ? null : ctx_r0.dynamicForm.layout.headerLeft == null ? null : ctx_r0.dynamicForm.layout.headerLeft.visible) && (ctx_r0.dynamicForm == null ? null : ctx_r0.dynamicForm.layout == null ? null : ctx_r0.dynamicForm.layout.headerRight == null ? null : ctx_r0.dynamicForm.layout.headerRight.visible)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dynamicForm\", ctx_r0.dynamicForm)(\"layoutArea\", ctx_r0.DynamicFormAreas.HeaderLeft);\n  }\n}\nfunction DataEntryFormComponent_div_2_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"app-dynamic-form-section\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dynamicForm\", ctx_r0.dynamicForm)(\"layoutArea\", ctx_r0.DynamicFormAreas.HeaderRight);\n  }\n}\nfunction DataEntryFormComponent_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, DataEntryFormComponent_div_2_div_1_div_1_Template, 2, 5, \"div\", 17)(2, DataEntryFormComponent_div_2_div_1_div_2_Template, 2, 2, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.dynamicForm == null ? null : ctx_r0.dynamicForm.layout == null ? null : ctx_r0.dynamicForm.layout.headerLeft == null ? null : ctx_r0.dynamicForm.layout.headerLeft.visible);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.dynamicForm == null ? null : ctx_r0.dynamicForm.layout == null ? null : ctx_r0.dynamicForm.layout.headerRight == null ? null : ctx_r0.dynamicForm.layout.headerRight.visible);\n  }\n}\nfunction DataEntryFormComponent_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelement(1, \"app-dynamic-form-section\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dynamicForm\", ctx_r0.dynamicForm)(\"layoutArea\", ctx_r0.DynamicFormAreas.Instruction);\n  }\n}\nfunction DataEntryFormComponent_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"h3\", 24);\n    i0.ɵɵtext(2, \"Error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Unable to submit the form due to validation errors. \");\n    i0.ɵɵelementStart(5, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function DataEntryFormComponent_div_2_div_3_Template_a_click_5_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      ctx_r0.showValidationResults = true;\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵtext(6, \"Review errors\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \". \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DataEntryFormComponent_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"app-dynamic-form-body\", 27);\n    i0.ɵɵlistener(\"formOnSubmit\", function DataEntryFormComponent_div_2_div_4_Template_app_dynamic_form_body_formOnSubmit_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.formOnSubmit($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c3, !(ctx_r0.dynamicForm == null ? null : ctx_r0.dynamicForm.layout == null ? null : ctx_r0.dynamicForm.layout.headerLeft == null ? null : ctx_r0.dynamicForm.layout.headerLeft.visible) && !(ctx_r0.dynamicForm == null ? null : ctx_r0.dynamicForm.layout == null ? null : ctx_r0.dynamicForm.layout.headerRight == null ? null : ctx_r0.dynamicForm.layout.headerRight.visible)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"triggerSaveEvent\", ctx_r0.triggerSaveEvent)(\"dynamicForm\", ctx_r0.dynamicForm)(\"data\", ctx_r0.jsonSchemaParam.data)(\"projectId\", ctx_r0.projectId)(\"projectVersionId\", ctx_r0.projectVersionId)(\"datastore\", ctx_r0.datastore)(\"recordId\", ctx_r0.recordId)(\"isPreview\", ctx_r0.isPreview)(\"showSubmitButton\", false)(\"showFormOptions\", !ctx_r0.formBodyOnly)(\"userPermissionLevel\", ctx_r0.userPermissionLevel);\n  }\n}\nfunction DataEntryFormComponent_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"app-dynamic-form-section\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dynamicForm\", ctx_r0.dynamicForm)(\"layoutArea\", ctx_r0.DynamicFormAreas.Footer);\n  }\n}\nfunction DataEntryFormComponent_div_2_app_signatures_renderer_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-signatures-renderer\", 29);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"signatures\", ctx_r0.signatures);\n  }\n}\nfunction DataEntryFormComponent_div_2_div_7_app_signature_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-signature-button\", 33);\n    i0.ɵɵtwoWayListener(\"signatureChange\", function DataEntryFormComponent_div_2_div_7_app_signature_button_1_Template_app_signature_button_signatureChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r0.selectedSignature, $event) || (ctx_r0.selectedSignature = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"signatureChange\", function DataEntryFormComponent_div_2_div_7_app_signature_button_1_Template_app_signature_button_signatureChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.signatureChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵtwoWayProperty(\"signature\", ctx_r0.selectedSignature);\n  }\n}\nfunction DataEntryFormComponent_div_2_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtemplate(1, DataEntryFormComponent_div_2_div_7_app_signature_button_1_Template, 1, 1, \"app-signature-button\", 31);\n    i0.ɵɵelementStart(2, \"p-button\", 32);\n    i0.ɵɵlistener(\"onClick\", function DataEntryFormComponent_div_2_div_7_Template_p_button_onClick_2_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.submit());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.dynamicForm == null ? null : ctx_r0.dynamicForm.additionalSettings == null ? null : ctx_r0.dynamicForm.additionalSettings.enableSignature);\n  }\n}\nfunction DataEntryFormComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtemplate(1, DataEntryFormComponent_div_2_div_1_Template, 3, 2, \"div\", 9)(2, DataEntryFormComponent_div_2_div_2_Template, 2, 2, \"div\", 10)(3, DataEntryFormComponent_div_2_div_3_Template, 8, 0, \"div\", 11)(4, DataEntryFormComponent_div_2_div_4_Template, 2, 14, \"div\", 12)(5, DataEntryFormComponent_div_2_div_5_Template, 2, 2, \"div\", 13)(6, DataEntryFormComponent_div_2_app_signatures_renderer_6_Template, 1, 1, \"app-signatures-renderer\", 14)(7, DataEntryFormComponent_div_2_div_7_Template, 3, 1, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c1, !ctx_r0.formBodyOnly));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.formBodyOnly);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.formBodyOnly && (ctx_r0.dynamicForm == null ? null : ctx_r0.dynamicForm.layout == null ? null : ctx_r0.dynamicForm.layout.instruction.visible));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showSaveErrorsBlock);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.datastore);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.formBodyOnly && (ctx_r0.dynamicForm == null ? null : ctx_r0.dynamicForm.layout == null ? null : ctx_r0.dynamicForm.layout.footer == null ? null : ctx_r0.dynamicForm.layout.footer.visible));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.formBodyOnly && (ctx_r0.signatures == null ? null : ctx_r0.signatures.length));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.formBodyOnly && !ctx_r0.isPreview && ctx_r0.dynamicForm);\n  }\n}\nfunction DataEntryFormComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"h3\");\n    i0.ɵɵtext(2, \"Warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Data submitted successfully. However, there were some validation warnings.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Choose from the following actions:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ul\", 35)(8, \"li\")(9, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function DataEntryFormComponent_div_3_Template_a_click_9_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      ctx_r0.showValidationResults = true;\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵtext(10, \"View Warnings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"li\")(12, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function DataEntryFormComponent_div_3_Template_a_click_12_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      ctx_r0.editLastEntry();\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵtext(13, \"Review Data\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"li\")(15, \"a\", 25);\n    i0.ɵɵlistener(\"click\", function DataEntryFormComponent_div_3_Template_a_click_15_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      ctx_r0.handleContinue();\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵtext(16, \"Continue\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction DataEntryFormComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"img\", 37);\n    i0.ɵɵpipe(2, \"assetUrl\");\n    i0.ɵɵelement(3, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 2, \"layout/images/gfx-form-sent.svg\"), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHtml\", ctx_r0.dynamicForm.additionalSettings == null ? null : ctx_r0.dynamicForm.additionalSettings.actionContent, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction DataEntryFormComponent_app_page_error_block_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-page-error-block\", 39);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"title\", ctx_r0.pageLoadErrorObj == null ? null : ctx_r0.pageLoadErrorObj.title)(\"detailsHtml\", ctx_r0.pageLoadErrorObj == null ? null : ctx_r0.pageLoadErrorObj.details)(\"moreDetailsHtml\", ctx_r0.pageLoadErrorObj == null ? null : ctx_r0.pageLoadErrorObj.moreDetails)(\"showBackButton\", !ctx_r0.formBodyOnly && !ctx_r0.isPreview && !ctx_r0.isEmbeddedView);\n  }\n}\nfunction DataEntryFormComponent_app_validation_results_dialog_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-validation-results-dialog\", 40);\n    i0.ɵɵtwoWayListener(\"visibleChange\", function DataEntryFormComponent_app_validation_results_dialog_6_Template_app_validation_results_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.showValidationResults, $event) || (ctx_r0.showValidationResults = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"visibleChange\", function DataEntryFormComponent_app_validation_results_dialog_6_Template_app_validation_results_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.validationDialogVisibaleChanged($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"saveRecordsResponse\", ctx_r0.saveRecordsResponse);\n    i0.ɵɵtwoWayProperty(\"visible\", ctx_r0.showValidationResults);\n    i0.ɵɵproperty(\"data\", i0.ɵɵpureFunction1(5, _c4, ctx_r0.jsonSchemaParam.data))(\"datastore\", ctx_r0.datastore)(\"masterKeyColumn\", ctx_r0.masterKeyColumn);\n  }\n}\nfunction DataEntryFormComponent_p_blockUI_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-blockUI\", 41);\n    i0.ɵɵelement(1, \"img\", 42);\n    i0.ɵɵpipe(2, \"assetUrl\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"blocked\", ctx_r0.showSpinner);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 2, \"layout/images/salt-box-loading.gif\"), i0.ɵɵsanitizeUrl);\n  }\n}\nexport class DataEntryFormComponent {\n  constructor(activatedRoute, router, datastoresService, changeTrackingService, dynamicFormService, notificationService, userActivityService, location, postSaveEventService, projectsService, communicationService) {\n    this.activatedRoute = activatedRoute;\n    this.router = router;\n    this.datastoresService = datastoresService;\n    this.changeTrackingService = changeTrackingService;\n    this.dynamicFormService = dynamicFormService;\n    this.notificationService = notificationService;\n    this.userActivityService = userActivityService;\n    this.location = location;\n    this.postSaveEventService = postSaveEventService;\n    this.projectsService = projectsService;\n    this.communicationService = communicationService;\n    this.recordId = '';\n    this.triggerSaveEvent = new EventEmitter();\n    this.isPreview = false;\n    this.formBodyOnly = false;\n    this.userPermissionLevel = UserPermissionLevels.EditAndDelete;\n    this.afterSave = new EventEmitter();\n    this.beforeSave = new EventEmitter();\n    /**\n     * Event emitted when the form has finished loading.\n     * The boolean value indicates if the form was loaded successfully or with an error.\n     * - `true`: Form loaded without any errors.\n     * - `false`: Form loaded with errors.\n     */\n    this.formLoad = new EventEmitter();\n    this.isChildComponent = false;\n    this.jsonSchemaParam = new JSONSchemaParam();\n    this.isSubmitted = false;\n    this.signatureUpdated = false;\n    this.showSaveErrorsBlock = false;\n    this.showWarningsBlock = false;\n    this.showValidationResults = false;\n    this.showSpinner = false;\n    this.showDynamicForm = false;\n    this.masterKeyColumn = \"\";\n    this.isEmbeddedView = false;\n    this.isOTC = false;\n    // used in the html\n    this.DynamicFormPostSaveActions = DynamicFormPostSaveActions;\n    this.DynamicFormAreas = DynamicFormLayoutAreas;\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.showSpinner = true;\n      // Hide navigation if this is an embedded view\n      _this.isEmbeddedView = _this.router?.url?.includes('embeddedview');\n      _this.isOTC = _this.router?.url?.includes('otc');\n      if (_this.isEmbeddedView) _this.hideNavigationApp();\n      if (_this.dynamicForm) {\n        _this.isChildComponent = true;\n        yield _this.setFormDetails();\n        _this.showDynamicForm = !_this.pageLoadErrorObj;\n        _this.showSpinner = false;\n        _this.formLoad.emit(_this.showDynamicForm);\n        return;\n      }\n      if (_this.activatedRoute) {\n        _this.activatedRoute.params.subscribe( /*#__PURE__*/function () {\n          var _ref = _asyncToGenerator(function* (params) {\n            _this.projectId = params.projectId;\n            _this.projectVersionId = params.projectVersionId;\n            _this.recordId = params.recordId;\n            // Check if the route variable is coming from an embedded iFrame request (URL variable: primaryKey)\n            _this.queryParamPrimaryKey = _this.activatedRoute.snapshot.queryParams.primaryKey;\n            try {\n              // Logging user activity\n              if (!_this.isPreview && !_this.isOTC) {\n                _this.projectVersion = yield _this.getProjectVersion();\n                _this.userActivityService.addUserActivity(new UserActivity({\n                  projectId: _this.projectId,\n                  projectVersionId: _this.projectVersionId,\n                  url: window.location.pathname,\n                  dynamicFormId: params.dynamicFormId\n                }));\n              }\n              yield _this.getDynamicForm(params.dynamicFormId);\n              if (_this.dynamicForm) yield _this.setFormDetails();\n              if (!_this.pageLoadErrorObj) {\n                _this.changeTrackingService.initBatchChange(params.projectId, params.projectVersionId);\n                _this.showDynamicForm = true;\n              }\n              _this.formLoad.emit(_this.showDynamicForm);\n            } catch (ex) {\n              _this.pageLoadErrorObj = _this.getPageErrorDetails('Other', ex);\n            }\n            _this.showSpinner = false;\n          });\n          return function (_x) {\n            return _ref.apply(this, arguments);\n          };\n        }());\n      }\n    })();\n  }\n  getDynamicForm(dynamicFormId) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (dynamicFormId) {\n          const dynamicForm = yield firstValueFrom(_this2.dynamicFormService.getDynamicForm(_this2.projectId, _this2.projectVersionId, dynamicFormId));\n          _this2.dynamicForm = dynamicForm;\n          _this2.buildBreadcrumb();\n        }\n        if (!_this2.dynamicForm) _this2.pageLoadErrorObj = _this2.getPageErrorDetails('Form');\n      } catch (ex) {\n        _this2.pageLoadErrorObj = _this2.getPageErrorDetails('Other', ex);\n      }\n    })();\n  }\n  getDatastore() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.dynamicForm.datastoreId) {\n        if (!_this3.isPreview) {\n          _this3.notificationService.showError('Error', `No datastore is selected! Please check the form's configuration.`);\n        }\n        return;\n      }\n      try {\n        const datastore = yield firstValueFrom(_this3.datastoresService.getDatastore(_this3.dynamicForm.datastoreId));\n        if (!datastore) {\n          _this3.pageLoadErrorObj = _this3.getPageErrorDetails('Datastore');\n          return;\n        }\n        _this3.datastore = datastore;\n      } catch (ex) {\n        _this3.pageLoadErrorObj = _this3.getPageErrorDetails('Other', ex);\n      }\n    })();\n  }\n  setFormData() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.datastore) return;\n      // setting master key column for validation results if it is not being set\n      if (!_this4.masterKeyColumn) {\n        _this4.masterKeyColumn = _this4.datastore?.primaryKey;\n        if (!_this4.masterKeyColumn) {\n          const fieldsConfig = _this4.dynamicFormService.getFieldsConfig(_this4.dynamicForm);\n          _this4.masterKeyColumn = fieldsConfig?.length ? fieldsConfig[0]?.key?.toString() : '';\n        }\n      }\n      if (_this4.recordId || _this4.queryParamPrimaryKey) {\n        _this4.jsonSchemaParam.action = CRUDActions.Update;\n        try {\n          const data = yield firstValueFrom(_this4.queryParamPrimaryKey ? _this4.datastoresService.getRecordByPrimaryKey(_this4.dynamicForm.datastoreId, _this4.queryParamPrimaryKey) : _this4.datastoresService.getRecord(_this4.dynamicForm.datastoreId, _this4.recordId));\n          if (data) {\n            // Set the record id if the form is loaded with the primary key\n            if (_this4.queryParamPrimaryKey) _this4.recordId = data[DB_PRIMARY_KEY];\n            _this4.jsonSchemaParam.data = data;\n            _this4.jsonSchemaParam.originalData = deepCopy(data);\n            if (data.__sbmeta?.Signatures?.length) {\n              _this4.selectedSignature = data.__sbmeta?.Signatures[0];\n              _this4.signatures = data.__sbmeta?.Signatures;\n            }\n            return;\n          }\n          _this4.pageLoadErrorObj = _this4.getPageErrorDetails('Data');\n        } catch (ex) {\n          _this4.pageLoadErrorObj = _this4.getPageErrorDetails('Other', ex);\n        }\n      }\n    })();\n  }\n  setFormDetails() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      yield _this5.getDatastore();\n      yield _this5.setFormData();\n    })();\n  }\n  formOnSubmit(data) {\n    this.setDataEntryChanges(data);\n    this.saveChanges();\n  }\n  saveChanges() {\n    this.beforeSave.emit();\n    this.showSpinner = true;\n    this.datastoresService.saveRecords(this.changeTrackingService.batchChange, this.datastore.id).subscribe({\n      next: saveRecordsResponse => {\n        this.saveRecordsResponse = saveRecordsResponse;\n        this.showValidationResults = saveRecordsResponse?.failedValidations?.length > 0;\n        this.showSaveErrorsBlock = saveRecordsResponse?.hasFailures;\n        this.showWarningsBlock = this.showValidationResults && !saveRecordsResponse?.hasFailures;\n        if (!saveRecordsResponse.hasFailures) this.notificationService.showSuccess('Success!', 'Form has been submitted successfully.');\n        if (!this.showValidationResults) this.doPostSaveActions();\n        this.showSpinner = false;\n      },\n      error: err => {\n        this.showSpinner = false;\n        const primaryKeyName = this.dynamicFormService.getFieldsConfig(this.dynamicForm)[0]?.props?.label ?? this.masterKeyColumn;\n        if (err.error?.toString().includes('DuplicateKey')) {\n          this.notificationService.showError('Error', `Duplicate value for primary key: ${primaryKeyName}. Please use a unique value.`);\n        } else {\n          this.notificationService.showError('Error', err.error ?? err.message);\n        }\n        this.afterSave.emit(false);\n      }\n    });\n  }\n  doPostSaveActions() {\n    // If it is a child component, we just emit the after save and parent component knows what to do\n    if (this.isChildComponent) {\n      this.afterSave.emit(true);\n      return;\n    }\n    this.saveRecordsResponse = null;\n    switch (this.dynamicForm.additionalSettings?.postSaveAction) {\n      case DynamicFormPostSaveActions.Redirect:\n        window.location.href = this.dynamicForm.additionalSettings.actionContent;\n        break;\n      case DynamicFormPostSaveActions.Reload:\n        this.reloadForm();\n        break;\n      default:\n        this.changeTrackingService.resetBatchChangeVariables();\n        this.showDynamicForm = false;\n        this.isSubmitted = true;\n    }\n  }\n  handleContinue() {\n    // We should reset the data as the form is being reloaded\n    this.jsonSchemaParam = new JSONSchemaParam();\n    this.showWarningsBlock = false;\n    this.doPostSaveActions();\n  }\n  editLastEntry() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this6.saveRecordsResponse?.recordIds?.length) {\n        return;\n      }\n      _this6.showSpinner = true;\n      _this6.recordId = _this6.saveRecordsResponse.recordIds[0];\n      _this6.changeTrackingService.resetBatchChangeVariables();\n      yield _this6.setFormData();\n      _this6.showSpinner = false;\n      _this6.saveRecordsResponse = null;\n      _this6.showWarningsBlock = false;\n    })();\n  }\n  addInvokeWorkflowEvent() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const saveFormEvent = _this7.dynamicForm.additionalSettings?.formEvents.find(formEvent => formEvent.eventType === AssetEventType.FormSave);\n      if (!saveFormEvent) {\n        return;\n      }\n      _this7.postSaveEventService.addInvokeWorkflowEvent({\n        recordsSaveEvent: new RecordsSaveEvent(_this7.dynamicForm.id, _this7.dynamicForm.name, ModifierSourceType.Form, _this7.datastore.name, _this7.dynamicForm.datastoreId),\n        assetEvent: _this7.dynamicForm.additionalSettings?.formEvents.find(formEvent => formEvent.eventType === AssetEventType.FormSave)\n      });\n    })();\n  }\n  setDataEntryChanges(event) {\n    const data = event.data;\n    if (!data) {\n      return;\n    }\n    if (event.sendEmail) {\n      this.setEmailSetup();\n    }\n    if (this.dynamicForm.additionalSettings?.invokeWorkflow) {\n      this.addInvokeWorkflowEvent();\n    }\n    this.jsonSchemaParam.data = data;\n    let id;\n    switch (this.jsonSchemaParam.action) {\n      case CRUDActions.Update:\n        {\n          id = this.jsonSchemaParam.originalData[DB_PRIMARY_KEY];\n          this.dynamicForm.fieldsConfig.forEach(field => {\n            const fieldName = field.key.toString();\n            const datastoreField = this.datastore.baseSchema.properties[fieldName];\n            if (datastoreField) {\n              let fieldValue = data[fieldName];\n              if (fieldValue != this.jsonSchemaParam.originalData[fieldName]) {\n                fieldValue = fieldValue == null ? getDefaultValue(datastoreField.type, datastoreField.format) : fieldValue;\n                this.changeTrackingService.trackChange(id, fieldName, this.jsonSchemaParam.originalData[fieldName], fieldValue, CRUDActions.Update);\n              }\n            }\n          });\n        }\n        break;\n      case CRUDActions.Create:\n        {\n          id = \"__0\";\n          this.dynamicForm.fieldsConfig.forEach(field => {\n            const fieldName = field.key.toString();\n            const datastoreField = this.datastore.baseSchema.properties[fieldName];\n            if (datastoreField) {\n              const fieldValue = data[fieldName] !== undefined && data[fieldName] !== null ? data[fieldName] : getDefaultValue(datastoreField.type, datastoreField.format);\n              this.changeTrackingService.trackChange(id, fieldName, undefined, fieldValue, CRUDActions.Create);\n            }\n          });\n        }\n        break;\n    }\n    this.changeTrackingService.trackAttachments(id, event.aliasIds);\n    // updating the signature if it has been changed\n    if (this.signatureUpdated) this.changeTrackingService.trackSignatures(id, this.signatures);\n    // we need to replace the original data with what is currently saved in the batch changes, then with the next save we can differniate the changes\n    this.jsonSchemaParam.originalData = deepCopy(data);\n  }\n  submit() {\n    this.triggerSaveEvent.emit();\n  }\n  hideNavigationApp() {\n    this.communicationService.next({\n      fromAppName: this.communicationService.currentAppName,\n      toAppName: this.communicationService.navigationAppName,\n      eventType: 'HideNavigationBar',\n      data: {}\n    });\n  }\n  reloadForm() {\n    // if the route is different change the route\n    if (this.activatedRoute.snapshot.params['recordId']) {\n      this.location.go(`/saltboxdataapp/data-entry/${this.projectId}/${this.projectVersionId}/Forms/${this.dynamicForm.id}`);\n      return;\n    }\n    this.recordId = undefined;\n    this.signatureUpdated = false;\n    this.selectedSignature = undefined;\n    this.signatures = [];\n    this.changeTrackingService.resetBatchChangeVariables();\n    this.dynamicFormService.resetDataEntryForm.next();\n    window.scrollTo(0, 0);\n  }\n  setEmailSetup() {\n    const emailSetup = {\n      subject: 'Link to your responses',\n      message: this.createEmailMessage()\n    };\n    this.postSaveEventService.addFormEmailEvent(emailSetup);\n  }\n  createEmailMessage() {\n    const emailMessage = this.dynamicForm.additionalSettings.emailMessage.replace(/<p>/g, '').replace(/<\\/p>/g, '\\n') + `\\n${window.location.href}/{{recordId}}` + '\\n\\nThanks and Regards,\\nSaltbox';\n    return emailMessage;\n  }\n  signatureChange(signature) {\n    this.signatureUpdated = true;\n    this.signatures = signature ? [signature] : [];\n  }\n  validationDialogVisibaleChanged(visible) {\n    // if this is a child component and user closes the warnings we will emit the after save event\n    if (this.isChildComponent && !visible && this.showWarningsBlock) {\n      this.afterSave.emit(true);\n      return;\n    }\n  }\n  getProjectVersion() {\n    return this.projectsService.getProjectVersion(`${this.projectVersionId}`);\n  }\n  buildBreadcrumb() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      if (_this8.projectVersion) {\n        const items = [{\n          label: 'My Apps',\n          routerLink: '/dashboard-v3'\n        }, {\n          label: _this8.projectVersion.appManifest?.name,\n          routerLink: `/app-view/${_this8.dynamicForm.projectId}/${_this8.dynamicForm.projectVersionId}`\n        }, {\n          label: _this8.dynamicForm?.name\n        }];\n        _this8.communicationService.updateBreadcrumb(items);\n      }\n    })();\n  }\n  getPageErrorDetails(type, ex = null) {\n    switch (type) {\n      case 'Data':\n        return {\n          title: 'Data not found!',\n          details: `<p>Record with the ${this.queryParamPrimaryKey ? 'primary key' : 'record id'}:'<b>${this.queryParamPrimaryKey ?? this.recordId}</b>' \n          was not found.</p><p>The link you used is broken or the data may have been deleted.</p>`,\n          moreDetails: `<p><b>Error:</b> Record with the ${this.queryParamPrimaryKey ? 'primary key' : 'record id'}:'<b>${this.queryParamPrimaryKey ?? this.recordId}\n          </b>'was not found in the datastore '<b>${this.datastore?.description}</b>'.</p>`\n        };\n      case 'Datastore':\n        return {\n          title: 'Datastore not found!',\n          details: `<p>The link you used is broken or the datastore may have been deleted.</p>`\n        };\n      case 'Form':\n        return {\n          title: 'Form not found!',\n          details: `<p>The link you used is broken or the form may have been deleted.</p>`\n        };\n      case 'Other':\n        return {\n          title: 'Uh oh!',\n          details: '<p>' + (ex instanceof HttpErrorResponse ? ex.statusText : ex?.error ?? ex?.message) + '</p>'\n        };\n    }\n  }\n  static {\n    this.ɵfac = function DataEntryFormComponent_Factory(t) {\n      return new (t || DataEntryFormComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.DatastoresService), i0.ɵɵdirectiveInject(i3.ChangeTrackingService), i0.ɵɵdirectiveInject(i4.DynamicFormsService), i0.ɵɵdirectiveInject(i5.NotificationService), i0.ɵɵdirectiveInject(i6.UserActivityService), i0.ɵɵdirectiveInject(i7.Location), i0.ɵɵdirectiveInject(i8.PostSaveEventService), i0.ɵɵdirectiveInject(i9.ProjectsService), i0.ɵɵdirectiveInject(CommunicationToken));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DataEntryFormComponent,\n      selectors: [[\"app-form-renderer\"]],\n      inputs: {\n        dynamicForm: \"dynamicForm\",\n        datastore: \"datastore\",\n        recordId: \"recordId\",\n        triggerSaveEvent: \"triggerSaveEvent\",\n        projectId: \"projectId\",\n        projectVersionId: \"projectVersionId\",\n        isPreview: \"isPreview\",\n        formBodyOnly: \"formBodyOnly\",\n        userPermissionLevel: \"userPermissionLevel\"\n      },\n      outputs: {\n        afterSave: \"afterSave\",\n        beforeSave: \"beforeSave\",\n        formLoad: \"formLoad\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 8,\n      vars: 9,\n      consts: [[1, \"form-designer\"], [1, \"grid\", \"form-layout\", 3, \"ngClass\"], [\"class\", \"col-12 data-entry-form\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"notice-bold warning col-12\", 4, \"ngIf\"], [\"class\", \"data-entry-confirmation-message col-12\", 4, \"ngIf\"], [3, \"title\", \"detailsHtml\", \"moreDetailsHtml\", \"showBackButton\", 4, \"ngIf\"], [\"singleRecordMode\", \"true\", 3, \"saveRecordsResponse\", \"visible\", \"data\", \"datastore\", \"masterKeyColumn\", \"visibleChange\", 4, \"ngIf\"], [\"styleClass\", \"z-5\", 3, \"blocked\", 4, \"ngIf\"], [1, \"col-12\", \"data-entry-form\", 3, \"ngClass\"], [\"class\", \"grid header-wrap m-0\", 4, \"ngIf\"], [\"class\", \"\", 4, \"ngIf\"], [\"class\", \"notice-bold danger ml-2\", 4, \"ngIf\"], [\"class\", \"card-container md:h-auto\", \"style\", \"min-height: 8rem\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"my-2\", 4, \"ngIf\"], [3, \"signatures\", 4, \"ngIf\"], [\"class\", \"flex justify-content-end\", 4, \"ngIf\"], [1, \"grid\", \"header-wrap\", \"m-0\"], [\"class\", \"col form-layout-header-text-editor\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"col form-layout-header-text-editor\", 4, \"ngIf\"], [1, \"col\", \"form-layout-header-text-editor\", 3, \"ngClass\"], [\"isPreview\", \"true\", 3, \"dynamicForm\", \"layoutArea\"], [1, \"col\", \"form-layout-header-text-editor\"], [1, \"\"], [1, \"notice-bold\", \"danger\", \"ml-2\"], [1, \"mb-0\", \"mt-2\"], [\"href\", \"#\", 3, \"click\"], [1, \"card-container\", \"md:h-auto\", 2, \"min-height\", \"8rem\", 3, \"ngClass\"], [3, \"formOnSubmit\", \"triggerSaveEvent\", \"dynamicForm\", \"data\", \"projectId\", \"projectVersionId\", \"datastore\", \"recordId\", \"isPreview\", \"showSubmitButton\", \"showFormOptions\", \"userPermissionLevel\"], [1, \"my-2\"], [3, \"signatures\"], [1, \"flex\", \"justify-content-end\"], [3, \"signature\", \"signatureChange\", 4, \"ngIf\"], [\"pRipple\", \"\", \"icon\", \"pi pi-send\", \"type\", \"button\", \"label\", \"Submit\", 3, \"onClick\"], [3, \"signatureChange\", \"signature\"], [1, \"notice-bold\", \"warning\", \"col-12\"], [1, \"font-medium\"], [1, \"data-entry-confirmation-message\", \"col-12\"], [\"alt\", \"\", \"width\", \"100%\", 3, \"src\"], [1, \"mt-4\", 3, \"innerHtml\"], [3, \"title\", \"detailsHtml\", \"moreDetailsHtml\", \"showBackButton\"], [\"singleRecordMode\", \"true\", 3, \"visibleChange\", \"saveRecordsResponse\", \"visible\", \"data\", \"datastore\", \"masterKeyColumn\"], [\"styleClass\", \"z-5\", 3, \"blocked\"], [1, \"ui-progress-spinner\", 3, \"src\"]],\n      template: function DataEntryFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, DataEntryFormComponent_div_2_Template, 8, 10, \"div\", 2)(3, DataEntryFormComponent_div_3_Template, 17, 0, \"div\", 3)(4, DataEntryFormComponent_div_4_Template, 4, 4, \"div\", 4)(5, DataEntryFormComponent_app_page_error_block_5_Template, 1, 4, \"app-page-error-block\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, DataEntryFormComponent_app_validation_results_dialog_6_Template, 1, 7, \"app-validation-results-dialog\", 6)(7, DataEntryFormComponent_p_blockUI_7_Template, 3, 4, \"p-blockUI\", 7);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, ctx.isEmbeddedView));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showDynamicForm && !ctx.showWarningsBlock);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showWarningsBlock);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.formBodyOnly && ctx.isSubmitted && (ctx.dynamicForm.additionalSettings == null ? null : ctx.dynamicForm.additionalSettings.postSaveAction) === ctx.DynamicFormPostSaveActions.ConfirmationMessage);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.pageLoadErrorObj);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showValidationResults);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSpinner);\n        }\n      },\n      dependencies: [NgIf, NgClass, DynamicFormSectionComponent, DynamicFormBodyComponent, SignaturesRendererComponent, SignatureButtonComponent, ButtonModule, i10.Button, PageErrorBlockComponent, ValidationResultsDialogComponent, BlockUIModule, i11.BlockUI, AssetUrlPipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "NgIf", "Ng<PERSON><PERSON>", "deepCopy", "JSONSchemaParam", "CRUDActions", "ModifierSourceType", "HttpErrorResponse", "UserActivity", "CommunicationToken", "DynamicFormLayoutAreas", "DynamicFormPostSaveActions", "RecordsSaveEvent", "AssetEventType", "getDefaultValue", "DB_PRIMARY_KEY", "firstValueFrom", "UserPermissionLevels", "AssetUrlPipe", "BlockUIModule", "ValidationResultsDialogComponent", "PageErrorBlockComponent", "ButtonModule", "SignatureButtonComponent", "SignaturesRendererComponent", "DynamicFormBodyComponent", "DynamicFormSectionComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "_c2", "ctx_r0", "dynamicForm", "layout", "headerLeft", "visible", "headerRight", "ɵɵadvance", "DynamicFormAreas", "HeaderLeft", "HeaderRight", "ɵɵtemplate", "DataEntryFormComponent_div_2_div_1_div_1_Template", "DataEntryFormComponent_div_2_div_1_div_2_Template", "Instruction", "ɵɵtext", "ɵɵlistener", "DataEntryFormComponent_div_2_div_3_Template_a_click_5_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "showValidationResults", "ɵɵresetView", "preventDefault", "DataEntryFormComponent_div_2_div_4_Template_app_dynamic_form_body_formOnSubmit_1_listener", "_r3", "formOnSubmit", "_c3", "triggerSaveEvent", "jsonSchemaParam", "data", "projectId", "projectVersionId", "datastore", "recordId", "isPreview", "formBodyOnly", "userPermissionLevel", "Footer", "signatures", "ɵɵtwoWayListener", "DataEntryFormComponent_div_2_div_7_app_signature_button_1_Template_app_signature_button_signatureChange_0_listener", "_r5", "ɵɵtwoWayBindingSet", "selectedSignature", "signatureChange", "ɵɵtwoWayProperty", "DataEntryFormComponent_div_2_div_7_app_signature_button_1_Template", "DataEntryFormComponent_div_2_div_7_Template_p_button_onClick_2_listener", "_r4", "submit", "additionalSettings", "enableSignature", "DataEntryFormComponent_div_2_div_1_Template", "DataEntryFormComponent_div_2_div_2_Template", "DataEntryFormComponent_div_2_div_3_Template", "DataEntryFormComponent_div_2_div_4_Template", "DataEntryFormComponent_div_2_div_5_Template", "DataEntryFormComponent_div_2_app_signatures_renderer_6_Template", "DataEntryFormComponent_div_2_div_7_Template", "_c1", "instruction", "showSaveErrorsBlock", "footer", "length", "DataEntryFormComponent_div_3_Template_a_click_9_listener", "_r6", "DataEntryFormComponent_div_3_Template_a_click_12_listener", "editLastEntry", "DataEntryFormComponent_div_3_Template_a_click_15_listener", "handleContinue", "ɵɵpipeBind1", "ɵɵsanitizeUrl", "actionContent", "ɵɵsanitizeHtml", "pageLoadErrorObj", "title", "details", "moreDetails", "isEmbeddedView", "DataEntryFormComponent_app_validation_results_dialog_6_Template_app_validation_results_dialog_visibleChange_0_listener", "_r7", "validationDialogVisibaleChanged", "saveRecordsResponse", "_c4", "masterKeyColumn", "showSpinner", "DataEntryFormComponent", "constructor", "activatedRoute", "router", "datastoresService", "changeTrackingService", "dynamicFormService", "notificationService", "userActivityService", "location", "postSaveEventService", "projectsService", "communicationService", "EditAndDelete", "afterSave", "beforeSave", "formLoad", "isChildComponent", "isSubmitted", "signatureUpdated", "showWarningsBlock", "showDynamicForm", "isOTC", "ngOnInit", "_this", "_asyncToGenerator", "url", "includes", "hideNavigationApp", "setFormDetails", "emit", "params", "subscribe", "_ref", "queryParamPrimaryKey", "snapshot", "queryParams", "<PERSON><PERSON><PERSON>", "projectVersion", "getProjectVersion", "addUserActivity", "window", "pathname", "dynamicFormId", "getDynamicForm", "initBatchChange", "ex", "getPageErrorDetails", "_x", "apply", "arguments", "_this2", "buildBreadcrumb", "getDatastore", "_this3", "datastoreId", "showError", "setFormData", "_this4", "fieldsConfig", "getFieldsConfig", "key", "toString", "action", "Update", "getRecordByPrimaryKey", "getRecord", "originalData", "__sbmeta", "Signatures", "_this5", "setDataEntryChanges", "saveChanges", "saveRecords", "batchChange", "id", "next", "failedValidations", "hasFailures", "showSuccess", "doPostSaveActions", "error", "err", "primaryKeyName", "props", "label", "message", "postSaveAction", "Redirect", "href", "Reload", "reloadForm", "resetBatchChangeVariables", "_this6", "recordIds", "addInvokeWorkflowEvent", "_this7", "saveFormEvent", "formEvents", "find", "formEvent", "eventType", "FormSave", "recordsSaveEvent", "name", "Form", "assetEvent", "event", "sendEmail", "setEmailSetup", "invokeWorkflow", "for<PERSON>ach", "field", "fieldName", "datastoreField", "baseSchema", "properties", "fieldValue", "type", "format", "trackChange", "Create", "undefined", "trackAttachments", "aliasIds", "trackSignatures", "fromAppName", "currentAppName", "toAppName", "navigationAppName", "go", "resetDataEntryForm", "scrollTo", "emailSetup", "subject", "createEmailMessage", "addFormEmailEvent", "emailMessage", "replace", "signature", "_this8", "items", "routerLink", "appManifest", "updateBreadcrumb", "description", "statusText", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "DatastoresService", "i3", "ChangeTrackingService", "i4", "DynamicFormsService", "i5", "NotificationService", "i6", "UserActivityService", "i7", "Location", "i8", "PostSaveEventService", "i9", "ProjectsService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DataEntryFormComponent_Template", "rf", "ctx", "DataEntryFormComponent_div_2_Template", "DataEntryFormComponent_div_3_Template", "DataEntryFormComponent_div_4_Template", "DataEntryFormComponent_app_page_error_block_5_Template", "DataEntryFormComponent_app_validation_results_dialog_6_Template", "DataEntryFormComponent_p_blockUI_7_Template", "_c0", "ConfirmationMessage", "i10", "<PERSON><PERSON>", "i11", "BlockUI", "encapsulation"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\dynamic-forms\\data-entry-form\\data-entry-form.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\dynamic-forms\\data-entry-form\\data-entry-form.component.html"], "sourcesContent": ["import { Component, OnInit, Input, Inject, Output, EventEmitter } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Location, NgIf, NgClass } from '@angular/common';\r\nimport { NotificationService } from 'src/app/core/services/notification.service';\r\nimport { ChangeTrackingService } from 'src/app/core/services/change-tracking.service';\r\nimport { deepCopy } from 'src/app/shared/utilities/copy.functions';\r\nimport { JSONSchemaParam } from 'src/app/core/models/json-schema-param';\r\nimport { CRUDActions, ModifierSourceType } from 'src/app/core/enums/shared';\r\nimport { DynamicForm } from '../models/dynamic-form';\r\nimport { Datastore } from 'src/app/shared/models/datastore';\r\nimport { HttpErrorResponse } from '@angular/common/http';\r\nimport { UserActivity } from 'src/app/shared/models/user-activity';\r\nimport { UserActivityService } from 'src/app/core/services/user-activity.service';\r\nimport { DynamicFormsService } from '../services/dynamic-forms.service';\r\nimport { DatastoresService } from 'src/app/core/services/datastores.service';\r\nimport { CommunicationServiceType, CommunicationToken } from 'src/app/core/models/communication-service-type';\r\nimport { DynamicFormLayoutAreas, DynamicFormPostSaveActions } from '../enums/dynamic-form';\r\nimport { EmailSetup } from 'src/app/core/models/email-setup';\r\nimport { RecordsSaveEvent } from 'src/app/shared/models/records-save-event';\r\nimport { AssetEventType } from 'src/app/shared/enums/asset-workflow';\r\nimport { getDefaultValue } from 'src/app/shared/utilities/datatype.functions';\r\nimport { PostSaveEventService } from 'src/app/core/services/post-save-events.service';\r\nimport { DB_PRIMARY_KEY } from 'src/app/shared/constants/record.const';\r\nimport { SbData } from 'src/app/core/models/sb-data';\r\nimport { SignatureInfo } from 'src/app/core/models/signature-info';\r\nimport { SaveRecordsResponse } from 'src/app/core/models/save-records-response';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { UserPermissionLevels } from 'src/app/core/enums/shared';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ProjectsService } from 'src/app/core/services/projects.service';\r\nimport { ProjectVersion } from 'src/app/core/models/project-version';\r\nimport { AssetUrlPipe } from '../../shared/pipes/asset-url.pipe';\r\nimport { BlockUIModule } from 'primeng/blockui';\r\nimport { ValidationResultsDialogComponent } from '../../business-validation-results/validation-results-dialog.component';\r\nimport { PageErrorBlockComponent } from '../../shared/page-error-block/page-error-block.component';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { SignatureButtonComponent } from '../../signature/signature-button/signature-button.component';\r\nimport { SignaturesRendererComponent } from '../../signature/signatures-renderer/signatures-renderer.component';\r\nimport { DynamicFormBodyComponent } from '../dynamic-form-body/dynamic-form-body.component';\r\nimport { DynamicFormSectionComponent } from '../dynamic-form-section/dynamic-form-section.component';\r\n\r\n@Component({\r\n    selector: 'app-form-renderer',\r\n    templateUrl: './data-entry-form.component.html',\r\n    standalone: true,\r\n    imports: [NgIf, NgClass, DynamicFormSectionComponent, DynamicFormBodyComponent, SignaturesRendererComponent, SignatureButtonComponent, ButtonModule, PageErrorBlockComponent, ValidationResultsDialogComponent, BlockUIModule, AssetUrlPipe]\r\n})\r\nexport class DataEntryFormComponent implements OnInit {\r\n\r\n  @Input() dynamicForm: DynamicForm;\r\n  @Input() datastore: Datastore;\r\n  @Input() recordId = '';\r\n  @Input() triggerSaveEvent = new EventEmitter<void>();\r\n  @Input() projectId: number;\r\n  @Input() projectVersionId: number;\r\n  @Input() isPreview = false;\r\n  @Input() formBodyOnly = false;\r\n  @Input() userPermissionLevel: UserPermissionLevels = UserPermissionLevels.EditAndDelete;\r\n\r\n  @Output() afterSave: EventEmitter<boolean> = new EventEmitter();\r\n  @Output() beforeSave: EventEmitter<void> = new EventEmitter();\r\n\r\n  /**\r\n   * Event emitted when the form has finished loading.\r\n   * The boolean value indicates if the form was loaded successfully or with an error.\r\n   * - `true`: Form loaded without any errors.\r\n   * - `false`: Form loaded with errors.\r\n   */\r\n  @Output() formLoad: EventEmitter<boolean> = new EventEmitter();\r\n\r\n  isChildComponent = false;\r\n  jsonSchemaParam = new JSONSchemaParam();\r\n  submittedFormData;\r\n  savedDynamicFormSubscription;\r\n  isSubmitted = false;\r\n  queryParamPrimaryKey: any;\r\n  selectedSignature: SignatureInfo;\r\n  projectVersion: ProjectVersion;\r\n  signatures: SignatureInfo[];\r\n  signatureUpdated = false;\r\n  showSaveErrorsBlock = false;\r\n  showWarningsBlock = false;\r\n  showValidationResults = false;\r\n  showSpinner = false;\r\n  showDynamicForm = false;\r\n  pageLoadErrorObj: { title: string, details: string, moreDetails?: string };\r\n  saveRecordsResponse: SaveRecordsResponse;\r\n  masterKeyColumn = \"\";\r\n  isEmbeddedView = false;\r\n  isOTC = false;\r\n\r\n  // used in the html\r\n  protected readonly DynamicFormPostSaveActions = DynamicFormPostSaveActions;\r\n  protected readonly DynamicFormAreas = DynamicFormLayoutAreas;\r\n\r\n  constructor(\r\n    private activatedRoute: ActivatedRoute,\r\n    private router: Router,\r\n    private datastoresService: DatastoresService,\r\n    private changeTrackingService: ChangeTrackingService,\r\n    private dynamicFormService: DynamicFormsService,\r\n    private notificationService: NotificationService,\r\n    private userActivityService: UserActivityService,\r\n    private location: Location,\r\n    private postSaveEventService: PostSaveEventService,\r\n    private projectsService: ProjectsService,\r\n    @Inject(CommunicationToken) private communicationService: CommunicationServiceType) {\r\n  }\r\n\r\n  async ngOnInit() {\r\n    this.showSpinner = true;\r\n    \r\n    // Hide navigation if this is an embedded view\r\n    this.isEmbeddedView = this.router?.url?.includes('embeddedview');\r\n    this.isOTC = this.router?.url?.includes('otc');\r\n    if (this.isEmbeddedView)\r\n      this.hideNavigationApp();\r\n\r\n\r\n    if (this.dynamicForm) {\r\n      this.isChildComponent = true;\r\n      await this.setFormDetails();\r\n\r\n      this.showDynamicForm = !this.pageLoadErrorObj;\r\n      this.showSpinner = false;\r\n      this.formLoad.emit(this.showDynamicForm);\r\n\r\n      return;\r\n    }\r\n\r\n    if (this.activatedRoute) {\r\n      this.activatedRoute.params.subscribe(async params => {\r\n        this.projectId = params.projectId;\r\n        this.projectVersionId = params.projectVersionId;\r\n        this.recordId = params.recordId;\r\n\r\n        // Check if the route variable is coming from an embedded iFrame request (URL variable: primaryKey)\r\n        this.queryParamPrimaryKey = this.activatedRoute.snapshot.queryParams.primaryKey;\r\n\r\n        try {\r\n          // Logging user activity\r\n          if (!this.isPreview && !this.isOTC) {\r\n            this.projectVersion = await this.getProjectVersion();\r\n            this.userActivityService.addUserActivity(\r\n              new UserActivity({ projectId: this.projectId, projectVersionId: this.projectVersionId, url: window.location.pathname, dynamicFormId: params.dynamicFormId })\r\n            );\r\n          }\r\n\r\n          await this.getDynamicForm(params.dynamicFormId);\r\n\r\n          if (this.dynamicForm)\r\n            await this.setFormDetails();\r\n\r\n          if (!this.pageLoadErrorObj) {\r\n            this.changeTrackingService.initBatchChange(params.projectId, params.projectVersionId);\r\n            this.showDynamicForm = true;\r\n          }\r\n\r\n          this.formLoad.emit(this.showDynamicForm);\r\n        } catch (ex) {\r\n          this.pageLoadErrorObj = this.getPageErrorDetails('Other', ex);\r\n        }\r\n\r\n        this.showSpinner = false;\r\n      });\r\n    }\r\n  }\r\n\r\n  async getDynamicForm(dynamicFormId: string) {\r\n    try {\r\n      if (dynamicFormId) {\r\n        const dynamicForm = await firstValueFrom(\r\n          this.dynamicFormService.getDynamicForm(this.projectId, this.projectVersionId, dynamicFormId)\r\n        );\r\n\r\n        this.dynamicForm = dynamicForm;\r\n        this.buildBreadcrumb();\r\n      }\r\n\r\n      if (!this.dynamicForm)\r\n        this.pageLoadErrorObj = this.getPageErrorDetails('Form');      \r\n    } catch (ex) {\r\n      this.pageLoadErrorObj = this.getPageErrorDetails('Other', ex);\r\n    }\r\n  }\r\n\r\n  async getDatastore() {\r\n    if (!this.dynamicForm.datastoreId) {\r\n      if (!this.isPreview) {\r\n        this.notificationService.showError('Error', `No datastore is selected! Please check the form's configuration.`);\r\n      }\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const datastore = await firstValueFrom(this.datastoresService.getDatastore(this.dynamicForm.datastoreId));\r\n      if (!datastore) {\r\n        this.pageLoadErrorObj = this.getPageErrorDetails('Datastore');\r\n        return;\r\n      }\r\n\r\n      this.datastore = datastore;\r\n    } catch (ex) {\r\n      this.pageLoadErrorObj = this.getPageErrorDetails('Other', ex);\r\n    }\r\n  }\r\n\r\n  async setFormData() {\r\n    if (!this.datastore)\r\n      return;\r\n\r\n    // setting master key column for validation results if it is not being set\r\n    if (!this.masterKeyColumn) {\r\n      this.masterKeyColumn = this.datastore?.primaryKey;\r\n      if (!this.masterKeyColumn) {\r\n        const fieldsConfig = this.dynamicFormService.getFieldsConfig(this.dynamicForm);\r\n        this.masterKeyColumn = fieldsConfig?.length ? fieldsConfig[0]?.key?.toString() : '';\r\n      }\r\n    }\r\n\r\n    if (this.recordId || this.queryParamPrimaryKey) {\r\n      this.jsonSchemaParam.action = CRUDActions.Update;\r\n\r\n      try {\r\n        const data = await firstValueFrom(\r\n          this.queryParamPrimaryKey\r\n            ? this.datastoresService.getRecordByPrimaryKey(this.dynamicForm.datastoreId, this.queryParamPrimaryKey)\r\n            : this.datastoresService.getRecord(this.dynamicForm.datastoreId, this.recordId)\r\n        );\r\n\r\n        if (data) {\r\n          // Set the record id if the form is loaded with the primary key\r\n          if (this.queryParamPrimaryKey)\r\n            this.recordId = data[DB_PRIMARY_KEY];\r\n\r\n          this.jsonSchemaParam.data = data;\r\n          this.jsonSchemaParam.originalData = deepCopy(data);\r\n\r\n          if (data.__sbmeta?.Signatures?.length) {\r\n            this.selectedSignature = data.__sbmeta?.Signatures[0];\r\n            this.signatures = data.__sbmeta?.Signatures;\r\n          }\r\n\r\n          return;\r\n        }\r\n\r\n        this.pageLoadErrorObj = this.getPageErrorDetails('Data');\r\n      } catch (ex) {\r\n        this.pageLoadErrorObj = this.getPageErrorDetails('Other', ex);\r\n      }\r\n    }\r\n  }\r\n\r\n  async setFormDetails() {\r\n    await this.getDatastore();\r\n    await this.setFormData();\r\n  }\r\n\r\n  formOnSubmit(data: any) {\r\n    this.setDataEntryChanges(data);\r\n    this.saveChanges();\r\n  }\r\n\r\n  saveChanges() {\r\n    this.beforeSave.emit();\r\n\r\n    this.showSpinner = true;\r\n    this.datastoresService.saveRecords(this.changeTrackingService.batchChange, this.datastore.id)\r\n      .subscribe({\r\n        next: (saveRecordsResponse: SaveRecordsResponse) => {\r\n          this.saveRecordsResponse = saveRecordsResponse;\r\n          this.showValidationResults = saveRecordsResponse?.failedValidations?.length > 0;\r\n          this.showSaveErrorsBlock = saveRecordsResponse?.hasFailures;\r\n          this.showWarningsBlock = this.showValidationResults && !saveRecordsResponse?.hasFailures;\r\n\r\n          if (!saveRecordsResponse.hasFailures)\r\n            this.notificationService.showSuccess('Success!', 'Form has been submitted successfully.');\r\n\r\n          if (!this.showValidationResults)\r\n            this.doPostSaveActions();\r\n\r\n          this.showSpinner = false;\r\n        },\r\n        error: err => {\r\n          this.showSpinner = false;\r\n          const primaryKeyName = this.dynamicFormService.getFieldsConfig(this.dynamicForm)[0]?.props?.label ?? this.masterKeyColumn;\r\n\r\n          if (err.error?.toString().includes('DuplicateKey')) {\r\n            this.notificationService.showError('Error', `Duplicate value for primary key: ${primaryKeyName}. Please use a unique value.`)\r\n          } else {\r\n            this.notificationService.showError('Error', err.error ?? err.message);\r\n          }\r\n\r\n          this.afterSave.emit(false);\r\n        }\r\n      });\r\n  }\r\n\r\n  doPostSaveActions() {\r\n    // If it is a child component, we just emit the after save and parent component knows what to do\r\n    if (this.isChildComponent) {\r\n      this.afterSave.emit(true);\r\n      return;\r\n    }\r\n\r\n    this.saveRecordsResponse = null;\r\n\r\n    switch (this.dynamicForm.additionalSettings?.postSaveAction) {\r\n      case DynamicFormPostSaveActions.Redirect:\r\n        window.location.href = this.dynamicForm.additionalSettings.actionContent;\r\n        break;\r\n      case DynamicFormPostSaveActions.Reload:\r\n        this.reloadForm();\r\n        break;\r\n      default:\r\n        this.changeTrackingService.resetBatchChangeVariables();\r\n        this.showDynamicForm = false;\r\n        this.isSubmitted = true;\r\n    }\r\n  }\r\n\r\n  handleContinue() {\r\n    // We should reset the data as the form is being reloaded\r\n    this.jsonSchemaParam = new JSONSchemaParam();\r\n\r\n    this.showWarningsBlock = false;\r\n    this.doPostSaveActions();\r\n  }\r\n\r\n  async editLastEntry() {\r\n    if (!this.saveRecordsResponse?.recordIds?.length) {\r\n      return;\r\n    }\r\n\r\n    this.showSpinner = true;\r\n    this.recordId = this.saveRecordsResponse.recordIds[0];\r\n    this.changeTrackingService.resetBatchChangeVariables();\r\n    await this.setFormData();\r\n\r\n    this.showSpinner = false;\r\n    this.saveRecordsResponse = null;\r\n    this.showWarningsBlock = false;\r\n  }\r\n\r\n  async addInvokeWorkflowEvent() {\r\n    const saveFormEvent = this.dynamicForm.additionalSettings?.formEvents\r\n      .find(formEvent => formEvent.eventType === AssetEventType.FormSave);\r\n    if (!saveFormEvent) {\r\n      return;\r\n    }\r\n\r\n    this.postSaveEventService.addInvokeWorkflowEvent({\r\n      recordsSaveEvent: new RecordsSaveEvent(\r\n        this.dynamicForm.id, this.dynamicForm.name, ModifierSourceType.Form, this.datastore.name,\r\n        this.dynamicForm.datastoreId),\r\n      assetEvent: this.dynamicForm.additionalSettings?.formEvents.find(formEvent => formEvent.eventType === AssetEventType.FormSave)\r\n    });\r\n  }\r\n\r\n  private setDataEntryChanges(event: any) {\r\n    const data = event.data as SbData;\r\n    if (!data) {\r\n      return;\r\n    }\r\n\r\n    if (event.sendEmail) {\r\n      this.setEmailSetup();\r\n    }\r\n\r\n    if (this.dynamicForm.additionalSettings?.invokeWorkflow) {\r\n      this.addInvokeWorkflowEvent();\r\n    }\r\n\r\n    this.jsonSchemaParam.data = data;\r\n\r\n    let id: string;\r\n    switch (this.jsonSchemaParam.action) {\r\n      case CRUDActions.Update:\r\n        {\r\n          id = this.jsonSchemaParam.originalData[DB_PRIMARY_KEY];\r\n\r\n          this.dynamicForm.fieldsConfig.forEach(field => {\r\n            const fieldName = field.key.toString();\r\n            const datastoreField = this.datastore.baseSchema.properties[fieldName];\r\n\r\n            if (datastoreField) {\r\n              let fieldValue = data[fieldName];\r\n\r\n              if (fieldValue != this.jsonSchemaParam.originalData[fieldName]) {\r\n                fieldValue = fieldValue == null ? getDefaultValue(datastoreField.type, datastoreField.format) : fieldValue;\r\n                this.changeTrackingService.trackChange(id, fieldName, this.jsonSchemaParam.originalData[fieldName], fieldValue, CRUDActions.Update);\r\n              }\r\n            }\r\n          });\r\n        }\r\n        break;\r\n      case CRUDActions.Create:\r\n        {\r\n          id = \"__0\";\r\n\r\n          this.dynamicForm.fieldsConfig.forEach(field => {\r\n            const fieldName = field.key.toString();\r\n            const datastoreField = this.datastore.baseSchema.properties[fieldName];\r\n\r\n            if (datastoreField) {\r\n              const fieldValue = (data[fieldName] !== undefined && data[fieldName] !== null) ? data[fieldName] : getDefaultValue(datastoreField.type, datastoreField.format);\r\n              this.changeTrackingService.trackChange(id, fieldName, undefined, fieldValue, CRUDActions.Create);\r\n            }\r\n          });\r\n        }\r\n        break;\r\n    }\r\n\r\n    this.changeTrackingService.trackAttachments(id, event.aliasIds);\r\n\r\n    // updating the signature if it has been changed\r\n    if (this.signatureUpdated)\r\n      this.changeTrackingService.trackSignatures(id, this.signatures);\r\n\r\n    // we need to replace the original data with what is currently saved in the batch changes, then with the next save we can differniate the changes\r\n    this.jsonSchemaParam.originalData = deepCopy(data);\r\n  }\r\n\r\n  submit() {\r\n    this.triggerSaveEvent.emit();\r\n  }\r\n\r\n  hideNavigationApp() {\r\n    this.communicationService.next({\r\n      fromAppName: this.communicationService.currentAppName,\r\n      toAppName: this.communicationService.navigationAppName,\r\n      eventType: 'HideNavigationBar',\r\n      data: {}\r\n    });\r\n  }\r\n\r\n  reloadForm() {\r\n    // if the route is different change the route\r\n    if (this.activatedRoute.snapshot.params['recordId']) {\r\n      this.location.go(`/saltboxdataapp/data-entry/${this.projectId}/${this.projectVersionId}/Forms/${this.dynamicForm.id}`);\r\n      return;\r\n    }\r\n\r\n    this.recordId = undefined;\r\n    this.signatureUpdated = false;\r\n    this.selectedSignature = undefined;\r\n    this.signatures = [];\r\n\r\n    this.changeTrackingService.resetBatchChangeVariables();\r\n    this.dynamicFormService.resetDataEntryForm.next();\r\n\r\n    window.scrollTo(0, 0);\r\n  }\r\n\r\n  setEmailSetup() {\r\n    const emailSetup: EmailSetup = {\r\n      subject: 'Link to your responses',\r\n      message: this.createEmailMessage()\r\n    };\r\n\r\n    this.postSaveEventService.addFormEmailEvent(emailSetup);\r\n  }\r\n\r\n  createEmailMessage() {\r\n    const emailMessage = this.dynamicForm.additionalSettings.emailMessage.replace(/<p>/g, '').replace(/<\\/p>/g, '\\n') +\r\n      `\\n${window.location.href}/{{recordId}}` +\r\n      '\\n\\nThanks and Regards,\\nSaltbox';\r\n\r\n    return emailMessage;\r\n  }\r\n\r\n  signatureChange(signature) {\r\n    this.signatureUpdated = true;\r\n    this.signatures = signature ? [signature] : [];\r\n  }\r\n\r\n  validationDialogVisibaleChanged(visible: boolean) {\r\n    // if this is a child component and user closes the warnings we will emit the after save event\r\n    if (this.isChildComponent && !visible && this.showWarningsBlock) {\r\n      this.afterSave.emit(true);\r\n      return;\r\n    }\r\n  }\r\n  \r\n\r\n  getProjectVersion() {\r\n    return this.projectsService.getProjectVersion(`${this.projectVersionId}`);\r\n  }\r\n\r\n  async buildBreadcrumb() {\r\n    if (this.projectVersion) {\r\n      const items: MenuItem[] = [\r\n        { label: 'My Apps', routerLink: '/dashboard-v3' },\r\n        {\r\n          label: this.projectVersion.appManifest?.name,\r\n          routerLink: `/app-view/${this.dynamicForm.projectId}/${this.dynamicForm.projectVersionId}`\r\n        },\r\n        { label: this.dynamicForm?.name }\r\n      ];\r\n      this.communicationService.updateBreadcrumb(items);\r\n    }\r\n  }\r\n\r\n  private getPageErrorDetails(type: 'Data' | 'Datastore' | 'Form' | 'Other', ex = null) {\r\n    switch (type) {\r\n      case 'Data':\r\n        return {\r\n          title: 'Data not found!',\r\n          details: `<p>Record with the ${this.queryParamPrimaryKey ? 'primary key' : 'record id'}:'<b>${this.queryParamPrimaryKey ?? this.recordId}</b>' \r\n          was not found.</p><p>The link you used is broken or the data may have been deleted.</p>`,\r\n          moreDetails: `<p><b>Error:</b> Record with the ${this.queryParamPrimaryKey ? 'primary key' : 'record id'}:'<b>${this.queryParamPrimaryKey ?? this.recordId}\r\n          </b>'was not found in the datastore '<b>${this.datastore?.description}</b>'.</p>`\r\n        };\r\n      case 'Datastore':\r\n        return {\r\n          title: 'Datastore not found!',\r\n          details: `<p>The link you used is broken or the datastore may have been deleted.</p>`\r\n        };\r\n      case 'Form':\r\n        return {\r\n          title: 'Form not found!',\r\n          details: `<p>The link you used is broken or the form may have been deleted.</p>`\r\n        };\r\n      case 'Other':\r\n        return {\r\n          title: 'Uh oh!',\r\n          details: '<p>' + (ex instanceof HttpErrorResponse ? ex.statusText : ex?.error ?? ex?.message) + '</p>'\r\n        };\r\n    }\r\n  }\r\n}\r\n", "<div class=\"form-designer\">\r\n  <div class=\"grid form-layout\" [ngClass]=\"{'max-h-unset':isEmbeddedView}\">\r\n    <div *ngIf=\"showDynamicForm && !showWarningsBlock\" class=\"col-12 data-entry-form\" [ngClass]=\"{'card': !formBodyOnly}\">\r\n      <div *ngIf=\"!formBodyOnly\" class=\"grid header-wrap m-0\">\r\n        <div *ngIf=\"dynamicForm?.layout?.headerLeft?.visible\"\r\n          [ngClass]=\"{'mr-1': dynamicForm?.layout?.headerLeft?.visible && dynamicForm?.layout?.headerRight?.visible}\"\r\n          class=\"col form-layout-header-text-editor\">\r\n          <app-dynamic-form-section [dynamicForm]=\"dynamicForm\" [layoutArea]=\"DynamicFormAreas.HeaderLeft\"\r\n            isPreview=\"true\">\r\n          </app-dynamic-form-section>\r\n        </div>\r\n        <div *ngIf=\"dynamicForm?.layout?.headerRight?.visible\" class=\"col form-layout-header-text-editor\">\r\n          <app-dynamic-form-section [dynamicForm]=\"dynamicForm\" [layoutArea]=\"DynamicFormAreas.HeaderRight\"\r\n            isPreview=\"true\">\r\n          </app-dynamic-form-section>\r\n        </div>\r\n      </div>\r\n\r\n      <div *ngIf=\"!formBodyOnly && dynamicForm?.layout?.instruction.visible\" class=\"\">\r\n        <app-dynamic-form-section [dynamicForm]=\"dynamicForm\" [layoutArea]=\"DynamicFormAreas.Instruction\"\r\n          isPreview=\"true\">\r\n        </app-dynamic-form-section>\r\n      </div>\r\n      \r\n      <div *ngIf=\"showSaveErrorsBlock\" class=\"notice-bold danger ml-2\">\r\n        <h3 class=\"mb-0 mt-2\">Error</h3>\r\n        <p>Unable to submit the form due to validation errors. <a href=\"#\"\r\n            (click)=\"showValidationResults = true; $event.preventDefault()\">Review errors</a>. </p>\r\n      </div>\r\n\r\n      <div *ngIf=\"datastore\" class=\"card-container md:h-auto\" style=\"min-height: 8rem\"\r\n        [ngClass]=\"{'mt-3': !dynamicForm?.layout?.headerLeft?.visible && !dynamicForm?.layout?.headerRight?.visible}\">\r\n        <app-dynamic-form-body [triggerSaveEvent]=\"triggerSaveEvent\" [dynamicForm]=\"dynamicForm\"\r\n          [data]=\"jsonSchemaParam.data\" [projectId]='projectId' [projectVersionId]='projectVersionId'\r\n          [datastore]=\"datastore\" [recordId]=\"recordId\" [isPreview]=\"isPreview\" (formOnSubmit)=\"formOnSubmit($event)\"\r\n          [showSubmitButton]=\"false\" [showFormOptions]=\"!formBodyOnly\" [userPermissionLevel]=\"userPermissionLevel\">\r\n        </app-dynamic-form-body>\r\n      </div>      \r\n\r\n      <div *ngIf=\"!formBodyOnly && dynamicForm?.layout?.footer?.visible\" class=\"my-2\">\r\n        <app-dynamic-form-section [dynamicForm]=\"dynamicForm\" [layoutArea]=\"DynamicFormAreas.Footer\" \r\n          isPreview=\"true\">\r\n        </app-dynamic-form-section>\r\n      </div>\r\n\r\n      <app-signatures-renderer *ngIf=\"!formBodyOnly && signatures?.length\"\r\n        [signatures]=\"signatures\"></app-signatures-renderer>\r\n        \r\n      <div *ngIf=\"!formBodyOnly && !isPreview && dynamicForm\" class=\"flex justify-content-end\">\r\n        <app-signature-button *ngIf=\"dynamicForm?.additionalSettings?.enableSignature\" [(signature)]=\"selectedSignature\"\r\n          (signatureChange)=\"signatureChange($event)\"></app-signature-button>\r\n        <p-button pRipple icon=\"pi pi-send\" type=\"button\" label=\"Submit\" (onClick)=\"submit()\"></p-button>\r\n      </div>\r\n    </div>\r\n\r\n    <div *ngIf=\"showWarningsBlock\" class=\"notice-bold warning col-12\">\r\n      <h3>Warning</h3>\r\n      <p>Data submitted successfully. However, there were some validation warnings.</p>\r\n      <p>Choose from the following actions:</p>\r\n      <ul class=\"font-medium\">\r\n        <li><a href=\"#\" (click)=\"showValidationResults = true; $event.preventDefault()\">View Warnings</a></li>\r\n        <li><a href=\"#\" (click)=\"editLastEntry(); $event.preventDefault()\">Review Data</a></li>\r\n        <li><a href=\"#\" (click)=\"handleContinue(); $event.preventDefault()\">Continue</a></li>\r\n      </ul>\r\n    </div>\r\n\r\n    <div class=\"data-entry-confirmation-message col-12\"\r\n      *ngIf=\"!formBodyOnly && isSubmitted && dynamicForm.additionalSettings?.postSaveAction === DynamicFormPostSaveActions.ConfirmationMessage\">\r\n      <img [src]=\"'layout/images/gfx-form-sent.svg' | assetUrl\" alt=\"\" width=\"100%\">\r\n      <div [innerHtml]=\"dynamicForm.additionalSettings?.actionContent\" class=\"mt-4\"></div>\r\n    </div>\r\n\r\n    <app-page-error-block *ngIf=\"pageLoadErrorObj\" [title]=\"pageLoadErrorObj?.title\"\r\n      [detailsHtml]=\"pageLoadErrorObj?.details\" [moreDetailsHtml]=\"pageLoadErrorObj?.moreDetails\"\r\n      [showBackButton]=\"!formBodyOnly && !isPreview && !isEmbeddedView\"></app-page-error-block>\r\n  </div>\r\n</div>\r\n<app-validation-results-dialog *ngIf=\"showValidationResults\" [saveRecordsResponse]=\"saveRecordsResponse\"\r\n  [(visible)]=\"showValidationResults\" (visibleChange)=\"validationDialogVisibaleChanged($event)\"\r\n  [data]=\"[jsonSchemaParam.data]\" [datastore]=\"datastore\" singleRecordMode=\"true\" [masterKeyColumn]=\"masterKeyColumn\">\r\n</app-validation-results-dialog>\r\n<p-blockUI *ngIf=\"showSpinner\" [blocked]=\"showSpinner\" styleClass=\"z-5\">\r\n  <img class=\"ui-progress-spinner\" [src]=\"'layout/images/salt-box-loading.gif' | assetUrl\">\r\n</p-blockUI>"], "mappings": ";AAAA,SAAmDA,YAAY,QAAQ,eAAe;AAEtF,SAAmBC,IAAI,EAAEC,OAAO,QAAQ,iBAAiB;AAGzD,SAASC,QAAQ,QAAQ,yCAAyC;AAClE,SAASC,eAAe,QAAQ,uCAAuC;AACvE,SAASC,WAAW,EAAEC,kBAAkB,QAAQ,2BAA2B;AAG3E,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,YAAY,QAAQ,qCAAqC;AAIlE,SAAmCC,kBAAkB,QAAQ,gDAAgD;AAC7G,SAASC,sBAAsB,EAAEC,0BAA0B,QAAQ,uBAAuB;AAE1F,SAASC,gBAAgB,QAAQ,0CAA0C;AAC3E,SAASC,cAAc,QAAQ,qCAAqC;AACpE,SAASC,eAAe,QAAQ,6CAA6C;AAE7E,SAASC,cAAc,QAAQ,uCAAuC;AAItE,SAASC,cAAc,QAAQ,MAAM;AACrC,SAASC,oBAAoB,QAAQ,2BAA2B;AAIhE,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,gCAAgC,QAAQ,uEAAuE;AACxH,SAASC,uBAAuB,QAAQ,0DAA0D;AAClG,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,wBAAwB,QAAQ,6DAA6D;AACtG,SAASC,2BAA2B,QAAQ,mEAAmE;AAC/G,SAASC,wBAAwB,QAAQ,kDAAkD;AAC3F,SAASC,2BAA2B,QAAQ,wDAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICnC5FC,EAAA,CAAAC,cAAA,cAE6C;IAC3CD,EAAA,CAAAE,SAAA,mCAE2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;;;;IALJH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAK,eAAA,IAAAC,GAAA,GAAAC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,MAAA,kBAAAF,MAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAC,UAAA,kBAAAH,MAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,MAAAJ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,MAAA,kBAAAF,MAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAG,WAAA,kBAAAL,MAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAG,WAAA,CAAAD,OAAA,GAA2G;IAEjFX,EAAA,CAAAa,SAAA,EAA2B;IAACb,EAA5B,CAAAI,UAAA,gBAAAG,MAAA,CAAAC,WAAA,CAA2B,eAAAD,MAAA,CAAAO,gBAAA,CAAAC,UAAA,CAA2C;;;;;IAIlGf,EAAA,CAAAC,cAAA,cAAkG;IAChGD,EAAA,CAAAE,SAAA,mCAE2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;;;;IAHsBH,EAAA,CAAAa,SAAA,EAA2B;IAACb,EAA5B,CAAAI,UAAA,gBAAAG,MAAA,CAAAC,WAAA,CAA2B,eAAAD,MAAA,CAAAO,gBAAA,CAAAE,WAAA,CAA4C;;;;;IATrGhB,EAAA,CAAAC,cAAA,cAAwD;IAQtDD,EAPA,CAAAiB,UAAA,IAAAC,iDAAA,kBAE6C,IAAAC,iDAAA,kBAKqD;IAKpGnB,EAAA,CAAAG,YAAA,EAAM;;;;IAZEH,EAAA,CAAAa,SAAA,EAA8C;IAA9Cb,EAAA,CAAAI,UAAA,SAAAG,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,MAAA,kBAAAF,MAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAC,UAAA,kBAAAH,MAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,CAA8C;IAO9CX,EAAA,CAAAa,SAAA,EAA+C;IAA/Cb,EAAA,CAAAI,UAAA,SAAAG,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,MAAA,kBAAAF,MAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAG,WAAA,kBAAAL,MAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAG,WAAA,CAAAD,OAAA,CAA+C;;;;;IAOvDX,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,SAAA,mCAE2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;;;;IAHsBH,EAAA,CAAAa,SAAA,EAA2B;IAACb,EAA5B,CAAAI,UAAA,gBAAAG,MAAA,CAAAC,WAAA,CAA2B,eAAAD,MAAA,CAAAO,gBAAA,CAAAM,WAAA,CAA4C;;;;;;IAMjGpB,EADF,CAAAC,cAAA,cAAiE,aACzC;IAAAD,EAAA,CAAAqB,MAAA,YAAK;IAAArB,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAqB,MAAA,2DAAoD;IAAArB,EAAA,CAAAC,cAAA,YACa;IAAhED,EAAA,CAAAsB,UAAA,mBAAAC,+DAAAC,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAC,GAAA;MAAA,MAAAnB,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAApB,MAAA,CAAAqB,qBAAA,GAAiC,IAAI;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAAEL,MAAA,CAAAM,cAAA,EAAuB;IAAA,EAAC;IAAC9B,EAAA,CAAAqB,MAAA,oBAAa;IAAArB,EAAA,CAAAG,YAAA,EAAI;IAAAH,EAAA,CAAAqB,MAAA,SAAE;IACzFrB,EADyF,CAAAG,YAAA,EAAI,EACvF;;;;;;IAIJH,EAFF,CAAAC,cAAA,cACgH,gCAIH;IADnCD,EAAA,CAAAsB,UAAA,0BAAAS,0FAAAP,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAO,GAAA;MAAA,MAAAzB,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA6B,WAAA,CAAgBtB,MAAA,CAAA0B,YAAA,CAAAT,MAAA,CAAoB;IAAA,EAAC;IAG/GxB,EADE,CAAAG,YAAA,EAAwB,EACpB;;;;IANJH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAK,eAAA,KAAA6B,GAAA,IAAA3B,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,MAAA,kBAAAF,MAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAC,UAAA,kBAAAH,MAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,OAAAJ,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,MAAA,kBAAAF,MAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAG,WAAA,kBAAAL,MAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAG,WAAA,CAAAD,OAAA,GAA6G;IACtFX,EAAA,CAAAa,SAAA,EAAqC;IAGGb,EAHxC,CAAAI,UAAA,qBAAAG,MAAA,CAAA4B,gBAAA,CAAqC,gBAAA5B,MAAA,CAAAC,WAAA,CAA4B,SAAAD,MAAA,CAAA6B,eAAA,CAAAC,IAAA,CACzD,cAAA9B,MAAA,CAAA+B,SAAA,CAAwB,qBAAA/B,MAAA,CAAAgC,gBAAA,CAAsC,cAAAhC,MAAA,CAAAiC,SAAA,CACpE,aAAAjC,MAAA,CAAAkC,QAAA,CAAsB,cAAAlC,MAAA,CAAAmC,SAAA,CAAwB,2BAC3C,qBAAAnC,MAAA,CAAAoC,YAAA,CAAkC,wBAAApC,MAAA,CAAAqC,mBAAA,CAA4C;;;;;IAI5G5C,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAE,SAAA,mCAE2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;;;;IAHsBH,EAAA,CAAAa,SAAA,EAA2B;IAACb,EAA5B,CAAAI,UAAA,gBAAAG,MAAA,CAAAC,WAAA,CAA2B,eAAAD,MAAA,CAAAO,gBAAA,CAAA+B,MAAA,CAAuC;;;;;IAK9F7C,EAAA,CAAAE,SAAA,kCACsD;;;;IAApDF,EAAA,CAAAI,UAAA,eAAAG,MAAA,CAAAuC,UAAA,CAAyB;;;;;;IAGzB9C,EAAA,CAAAC,cAAA,+BAC8C;IADiCD,EAAA,CAAA+C,gBAAA,6BAAAC,mHAAAxB,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAwB,GAAA;MAAA,MAAA1C,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAA3B,EAAA,CAAAkD,kBAAA,CAAA3C,MAAA,CAAA4C,iBAAA,EAAA3B,MAAA,MAAAjB,MAAA,CAAA4C,iBAAA,GAAA3B,MAAA;MAAA,OAAAxB,EAAA,CAAA6B,WAAA,CAAAL,MAAA;IAAA,EAAiC;IAC9GxB,EAAA,CAAAsB,UAAA,6BAAA0B,mHAAAxB,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAwB,GAAA;MAAA,MAAA1C,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA6B,WAAA,CAAmBtB,MAAA,CAAA6C,eAAA,CAAA5B,MAAA,CAAuB;IAAA,EAAC;IAACxB,EAAA,CAAAG,YAAA,EAAuB;;;;IADUH,EAAA,CAAAqD,gBAAA,cAAA9C,MAAA,CAAA4C,iBAAA,CAAiC;;;;;;IADlHnD,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAiB,UAAA,IAAAqC,kEAAA,mCAC8C;IAC9CtD,EAAA,CAAAC,cAAA,mBAAsF;IAArBD,EAAA,CAAAsB,UAAA,qBAAAiC,wEAAA;MAAAvD,EAAA,CAAAyB,aAAA,CAAA+B,GAAA;MAAA,MAAAjD,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA6B,WAAA,CAAWtB,MAAA,CAAAkD,MAAA,EAAQ;IAAA,EAAC;IACvFzD,EADwF,CAAAG,YAAA,EAAW,EAC7F;;;;IAHmBH,EAAA,CAAAa,SAAA,EAAsD;IAAtDb,EAAA,CAAAI,UAAA,SAAAG,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAkD,kBAAA,kBAAAnD,MAAA,CAAAC,WAAA,CAAAkD,kBAAA,CAAAC,eAAA,CAAsD;;;;;IA/CjF3D,EAAA,CAAAC,cAAA,aAAsH;IA8CpHD,EA7CA,CAAAiB,UAAA,IAAA2C,2CAAA,iBAAwD,IAAAC,2CAAA,kBAewB,IAAAC,2CAAA,kBAMf,IAAAC,2CAAA,mBAO+C,IAAAC,2CAAA,kBAQhC,IAAAC,+DAAA,sCAOpD,IAAAC,2CAAA,kBAE6D;IAK3FlE,EAAA,CAAAG,YAAA,EAAM;;;;IAnD4EH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAK,eAAA,IAAA8D,GAAA,GAAA5D,MAAA,CAAAoC,YAAA,EAAmC;IAC7G3C,EAAA,CAAAa,SAAA,EAAmB;IAAnBb,EAAA,CAAAI,UAAA,UAAAG,MAAA,CAAAoC,YAAA,CAAmB;IAenB3C,EAAA,CAAAa,SAAA,EAA+D;IAA/Db,EAAA,CAAAI,UAAA,UAAAG,MAAA,CAAAoC,YAAA,KAAApC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,MAAA,kBAAAF,MAAA,CAAAC,WAAA,CAAAC,MAAA,CAAA2D,WAAA,CAAAzD,OAAA,EAA+D;IAM/DX,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAAI,UAAA,SAAAG,MAAA,CAAA8D,mBAAA,CAAyB;IAMzBrE,EAAA,CAAAa,SAAA,EAAe;IAAfb,EAAA,CAAAI,UAAA,SAAAG,MAAA,CAAAiC,SAAA,CAAe;IASfxC,EAAA,CAAAa,SAAA,EAA2D;IAA3Db,EAAA,CAAAI,UAAA,UAAAG,MAAA,CAAAoC,YAAA,KAAApC,MAAA,CAAAC,WAAA,kBAAAD,MAAA,CAAAC,WAAA,CAAAC,MAAA,kBAAAF,MAAA,CAAAC,WAAA,CAAAC,MAAA,CAAA6D,MAAA,kBAAA/D,MAAA,CAAAC,WAAA,CAAAC,MAAA,CAAA6D,MAAA,CAAA3D,OAAA,EAA2D;IAMvCX,EAAA,CAAAa,SAAA,EAAyC;IAAzCb,EAAA,CAAAI,UAAA,UAAAG,MAAA,CAAAoC,YAAA,KAAApC,MAAA,CAAAuC,UAAA,kBAAAvC,MAAA,CAAAuC,UAAA,CAAAyB,MAAA,EAAyC;IAG7DvE,EAAA,CAAAa,SAAA,EAAgD;IAAhDb,EAAA,CAAAI,UAAA,UAAAG,MAAA,CAAAoC,YAAA,KAAApC,MAAA,CAAAmC,SAAA,IAAAnC,MAAA,CAAAC,WAAA,CAAgD;;;;;;IAQtDR,EADF,CAAAC,cAAA,cAAkE,SAC5D;IAAAD,EAAA,CAAAqB,MAAA,cAAO;IAAArB,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAqB,MAAA,iFAA0E;IAAArB,EAAA,CAAAG,YAAA,EAAI;IACjFH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAqB,MAAA,yCAAkC;IAAArB,EAAA,CAAAG,YAAA,EAAI;IAEnCH,EADN,CAAAC,cAAA,aAAwB,SAClB,YAA4E;IAAhED,EAAA,CAAAsB,UAAA,mBAAAkD,yDAAAhD,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAgD,GAAA;MAAA,MAAAlE,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAApB,MAAA,CAAAqB,qBAAA,GAAiC,IAAI;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAAEL,MAAA,CAAAM,cAAA,EAAuB;IAAA,EAAC;IAAC9B,EAAA,CAAAqB,MAAA,qBAAa;IAAIrB,EAAJ,CAAAG,YAAA,EAAI,EAAK;IAClGH,EAAJ,CAAAC,cAAA,UAAI,aAA+D;IAAnDD,EAAA,CAAAsB,UAAA,mBAAAoD,0DAAAlD,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAgD,GAAA;MAAA,MAAAlE,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAASpB,MAAA,CAAAoE,aAAA,EAAe;MAAA,OAAA3E,EAAA,CAAA6B,WAAA,CAAEL,MAAA,CAAAM,cAAA,EAAuB;IAAA,EAAC;IAAC9B,EAAA,CAAAqB,MAAA,mBAAW;IAAIrB,EAAJ,CAAAG,YAAA,EAAI,EAAK;IACnFH,EAAJ,CAAAC,cAAA,UAAI,aAAgE;IAApDD,EAAA,CAAAsB,UAAA,mBAAAsD,0DAAApD,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAgD,GAAA;MAAA,MAAAlE,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAASpB,MAAA,CAAAsE,cAAA,EAAgB;MAAA,OAAA7E,EAAA,CAAA6B,WAAA,CAAEL,MAAA,CAAAM,cAAA,EAAuB;IAAA,EAAC;IAAC9B,EAAA,CAAAqB,MAAA,gBAAQ;IAEhFrB,EAFgF,CAAAG,YAAA,EAAI,EAAK,EAClF,EACD;;;;;IAENH,EAAA,CAAAC,cAAA,cAC4I;IAC1ID,EAAA,CAAAE,SAAA,cAA8E;;IAC9EF,EAAA,CAAAE,SAAA,cAAoF;IACtFF,EAAA,CAAAG,YAAA,EAAM;;;;IAFCH,EAAA,CAAAa,SAAA,EAAoD;IAApDb,EAAA,CAAAI,UAAA,QAAAJ,EAAA,CAAA8E,WAAA,2CAAA9E,EAAA,CAAA+E,aAAA,CAAoD;IACpD/E,EAAA,CAAAa,SAAA,GAA2D;IAA3Db,EAAA,CAAAI,UAAA,cAAAG,MAAA,CAAAC,WAAA,CAAAkD,kBAAA,kBAAAnD,MAAA,CAAAC,WAAA,CAAAkD,kBAAA,CAAAsB,aAAA,EAAAhF,EAAA,CAAAiF,cAAA,CAA2D;;;;;IAGlEjF,EAAA,CAAAE,SAAA,+BAE2F;;;;IAAzFF,EAF6C,CAAAI,UAAA,UAAAG,MAAA,CAAA2E,gBAAA,kBAAA3E,MAAA,CAAA2E,gBAAA,CAAAC,KAAA,CAAiC,gBAAA5E,MAAA,CAAA2E,gBAAA,kBAAA3E,MAAA,CAAA2E,gBAAA,CAAAE,OAAA,CACrC,oBAAA7E,MAAA,CAAA2E,gBAAA,kBAAA3E,MAAA,CAAA2E,gBAAA,CAAAG,WAAA,CAAkD,oBAAA9E,MAAA,CAAAoC,YAAA,KAAApC,MAAA,CAAAmC,SAAA,KAAAnC,MAAA,CAAA+E,cAAA,CAC1B;;;;;;IAGvEtF,EAAA,CAAAC,cAAA,wCAEsH;IADpHD,EAAA,CAAA+C,gBAAA,2BAAAwC,uHAAA/D,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAA+D,GAAA;MAAA,MAAAjF,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAA3B,EAAA,CAAAkD,kBAAA,CAAA3C,MAAA,CAAAqB,qBAAA,EAAAJ,MAAA,MAAAjB,MAAA,CAAAqB,qBAAA,GAAAJ,MAAA;MAAA,OAAAxB,EAAA,CAAA6B,WAAA,CAAAL,MAAA;IAAA,EAAmC;IAACxB,EAAA,CAAAsB,UAAA,2BAAAiE,uHAAA/D,MAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAA+D,GAAA;MAAA,MAAAjF,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA6B,WAAA,CAAiBtB,MAAA,CAAAkF,+BAAA,CAAAjE,MAAA,CAAuC;IAAA,EAAC;IAE/FxB,EAAA,CAAAG,YAAA,EAAgC;;;;IAH6BH,EAAA,CAAAI,UAAA,wBAAAG,MAAA,CAAAmF,mBAAA,CAA2C;IACtG1F,EAAA,CAAAqD,gBAAA,YAAA9C,MAAA,CAAAqB,qBAAA,CAAmC;IAC6C5B,EAAhF,CAAAI,UAAA,SAAAJ,EAAA,CAAAK,eAAA,IAAAsF,GAAA,EAAApF,MAAA,CAAA6B,eAAA,CAAAC,IAAA,EAA+B,cAAA9B,MAAA,CAAAiC,SAAA,CAAwB,oBAAAjC,MAAA,CAAAqF,eAAA,CAA4D;;;;;IAErH5F,EAAA,CAAAC,cAAA,oBAAwE;IACtED,EAAA,CAAAE,SAAA,cAAyF;;IAC3FF,EAAA,CAAAG,YAAA,EAAY;;;;IAFmBH,EAAA,CAAAI,UAAA,YAAAG,MAAA,CAAAsF,WAAA,CAAuB;IACnB7F,EAAA,CAAAa,SAAA,EAAuD;IAAvDb,EAAA,CAAAI,UAAA,QAAAJ,EAAA,CAAA8E,WAAA,8CAAA9E,EAAA,CAAA+E,aAAA,CAAuD;;;ADnC1F,OAAM,MAAOe,sBAAsB;EAgDjCC,YACUC,cAA8B,EAC9BC,MAAc,EACdC,iBAAoC,EACpCC,qBAA4C,EAC5CC,kBAAuC,EACvCC,mBAAwC,EACxCC,mBAAwC,EACxCC,QAAkB,EAClBC,oBAA0C,EAC1CC,eAAgC,EACJC,oBAA8C;IAV1E,KAAAV,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACa,KAAAC,oBAAoB,GAApBA,oBAAoB;IAvDjD,KAAAjE,QAAQ,GAAG,EAAE;IACb,KAAAN,gBAAgB,GAAG,IAAI9D,YAAY,EAAQ;IAG3C,KAAAqE,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,mBAAmB,GAAyBtD,oBAAoB,CAACqH,aAAa;IAE7E,KAAAC,SAAS,GAA0B,IAAIvI,YAAY,EAAE;IACrD,KAAAwI,UAAU,GAAuB,IAAIxI,YAAY,EAAE;IAE7D;;;;;;IAMU,KAAAyI,QAAQ,GAA0B,IAAIzI,YAAY,EAAE;IAE9D,KAAA0I,gBAAgB,GAAG,KAAK;IACxB,KAAA3E,eAAe,GAAG,IAAI3D,eAAe,EAAE;IAGvC,KAAAuI,WAAW,GAAG,KAAK;IAKnB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAA5C,mBAAmB,GAAG,KAAK;IAC3B,KAAA6C,iBAAiB,GAAG,KAAK;IACzB,KAAAtF,qBAAqB,GAAG,KAAK;IAC7B,KAAAiE,WAAW,GAAG,KAAK;IACnB,KAAAsB,eAAe,GAAG,KAAK;IAGvB,KAAAvB,eAAe,GAAG,EAAE;IACpB,KAAAN,cAAc,GAAG,KAAK;IACtB,KAAA8B,KAAK,GAAG,KAAK;IAEb;IACmB,KAAApI,0BAA0B,GAAGA,0BAA0B;IACvD,KAAA8B,gBAAgB,GAAG/B,sBAAsB;EAc5D;EAEMsI,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAACzB,WAAW,GAAG,IAAI;MAEvB;MACAyB,KAAI,CAAChC,cAAc,GAAGgC,KAAI,CAACrB,MAAM,EAAEuB,GAAG,EAAEC,QAAQ,CAAC,cAAc,CAAC;MAChEH,KAAI,CAACF,KAAK,GAAGE,KAAI,CAACrB,MAAM,EAAEuB,GAAG,EAAEC,QAAQ,CAAC,KAAK,CAAC;MAC9C,IAAIH,KAAI,CAAChC,cAAc,EACrBgC,KAAI,CAACI,iBAAiB,EAAE;MAG1B,IAAIJ,KAAI,CAAC9G,WAAW,EAAE;QACpB8G,KAAI,CAACP,gBAAgB,GAAG,IAAI;QAC5B,MAAMO,KAAI,CAACK,cAAc,EAAE;QAE3BL,KAAI,CAACH,eAAe,GAAG,CAACG,KAAI,CAACpC,gBAAgB;QAC7CoC,KAAI,CAACzB,WAAW,GAAG,KAAK;QACxByB,KAAI,CAACR,QAAQ,CAACc,IAAI,CAACN,KAAI,CAACH,eAAe,CAAC;QAExC;MACF;MAEA,IAAIG,KAAI,CAACtB,cAAc,EAAE;QACvBsB,KAAI,CAACtB,cAAc,CAAC6B,MAAM,CAACC,SAAS;UAAA,IAAAC,IAAA,GAAAR,iBAAA,CAAC,WAAMM,MAAM,EAAG;YAClDP,KAAI,CAAChF,SAAS,GAAGuF,MAAM,CAACvF,SAAS;YACjCgF,KAAI,CAAC/E,gBAAgB,GAAGsF,MAAM,CAACtF,gBAAgB;YAC/C+E,KAAI,CAAC7E,QAAQ,GAAGoF,MAAM,CAACpF,QAAQ;YAE/B;YACA6E,KAAI,CAACU,oBAAoB,GAAGV,KAAI,CAACtB,cAAc,CAACiC,QAAQ,CAACC,WAAW,CAACC,UAAU;YAE/E,IAAI;cACF;cACA,IAAI,CAACb,KAAI,CAAC5E,SAAS,IAAI,CAAC4E,KAAI,CAACF,KAAK,EAAE;gBAClCE,KAAI,CAACc,cAAc,SAASd,KAAI,CAACe,iBAAiB,EAAE;gBACpDf,KAAI,CAAChB,mBAAmB,CAACgC,eAAe,CACtC,IAAIzJ,YAAY,CAAC;kBAAEyD,SAAS,EAAEgF,KAAI,CAAChF,SAAS;kBAAEC,gBAAgB,EAAE+E,KAAI,CAAC/E,gBAAgB;kBAAEiF,GAAG,EAAEe,MAAM,CAAChC,QAAQ,CAACiC,QAAQ;kBAAEC,aAAa,EAAEZ,MAAM,CAACY;gBAAa,CAAE,CAAC,CAC7J;cACH;cAEA,MAAMnB,KAAI,CAACoB,cAAc,CAACb,MAAM,CAACY,aAAa,CAAC;cAE/C,IAAInB,KAAI,CAAC9G,WAAW,EAClB,MAAM8G,KAAI,CAACK,cAAc,EAAE;cAE7B,IAAI,CAACL,KAAI,CAACpC,gBAAgB,EAAE;gBAC1BoC,KAAI,CAACnB,qBAAqB,CAACwC,eAAe,CAACd,MAAM,CAACvF,SAAS,EAAEuF,MAAM,CAACtF,gBAAgB,CAAC;gBACrF+E,KAAI,CAACH,eAAe,GAAG,IAAI;cAC7B;cAEAG,KAAI,CAACR,QAAQ,CAACc,IAAI,CAACN,KAAI,CAACH,eAAe,CAAC;YAC1C,CAAC,CAAC,OAAOyB,EAAE,EAAE;cACXtB,KAAI,CAACpC,gBAAgB,GAAGoC,KAAI,CAACuB,mBAAmB,CAAC,OAAO,EAAED,EAAE,CAAC;YAC/D;YAEAtB,KAAI,CAACzB,WAAW,GAAG,KAAK;UAC1B,CAAC;UAAA,iBAAAiD,EAAA;YAAA,OAAAf,IAAA,CAAAgB,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;MACJ;IAAC;EACH;EAEMN,cAAcA,CAACD,aAAqB;IAAA,IAAAQ,MAAA;IAAA,OAAA1B,iBAAA;MACxC,IAAI;QACF,IAAIkB,aAAa,EAAE;UACjB,MAAMjI,WAAW,SAASnB,cAAc,CACtC4J,MAAI,CAAC7C,kBAAkB,CAACsC,cAAc,CAACO,MAAI,CAAC3G,SAAS,EAAE2G,MAAI,CAAC1G,gBAAgB,EAAEkG,aAAa,CAAC,CAC7F;UAEDQ,MAAI,CAACzI,WAAW,GAAGA,WAAW;UAC9ByI,MAAI,CAACC,eAAe,EAAE;QACxB;QAEA,IAAI,CAACD,MAAI,CAACzI,WAAW,EACnByI,MAAI,CAAC/D,gBAAgB,GAAG+D,MAAI,CAACJ,mBAAmB,CAAC,MAAM,CAAC;MAC5D,CAAC,CAAC,OAAOD,EAAE,EAAE;QACXK,MAAI,CAAC/D,gBAAgB,GAAG+D,MAAI,CAACJ,mBAAmB,CAAC,OAAO,EAAED,EAAE,CAAC;MAC/D;IAAC;EACH;EAEMO,YAAYA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA7B,iBAAA;MAChB,IAAI,CAAC6B,MAAI,CAAC5I,WAAW,CAAC6I,WAAW,EAAE;QACjC,IAAI,CAACD,MAAI,CAAC1G,SAAS,EAAE;UACnB0G,MAAI,CAAC/C,mBAAmB,CAACiD,SAAS,CAAC,OAAO,EAAE,kEAAkE,CAAC;QACjH;QACA;MACF;MAEA,IAAI;QACF,MAAM9G,SAAS,SAASnD,cAAc,CAAC+J,MAAI,CAAClD,iBAAiB,CAACiD,YAAY,CAACC,MAAI,CAAC5I,WAAW,CAAC6I,WAAW,CAAC,CAAC;QACzG,IAAI,CAAC7G,SAAS,EAAE;UACd4G,MAAI,CAAClE,gBAAgB,GAAGkE,MAAI,CAACP,mBAAmB,CAAC,WAAW,CAAC;UAC7D;QACF;QAEAO,MAAI,CAAC5G,SAAS,GAAGA,SAAS;MAC5B,CAAC,CAAC,OAAOoG,EAAE,EAAE;QACXQ,MAAI,CAAClE,gBAAgB,GAAGkE,MAAI,CAACP,mBAAmB,CAAC,OAAO,EAAED,EAAE,CAAC;MAC/D;IAAC;EACH;EAEMW,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAjC,iBAAA;MACf,IAAI,CAACiC,MAAI,CAAChH,SAAS,EACjB;MAEF;MACA,IAAI,CAACgH,MAAI,CAAC5D,eAAe,EAAE;QACzB4D,MAAI,CAAC5D,eAAe,GAAG4D,MAAI,CAAChH,SAAS,EAAE2F,UAAU;QACjD,IAAI,CAACqB,MAAI,CAAC5D,eAAe,EAAE;UACzB,MAAM6D,YAAY,GAAGD,MAAI,CAACpD,kBAAkB,CAACsD,eAAe,CAACF,MAAI,CAAChJ,WAAW,CAAC;UAC9EgJ,MAAI,CAAC5D,eAAe,GAAG6D,YAAY,EAAElF,MAAM,GAAGkF,YAAY,CAAC,CAAC,CAAC,EAAEE,GAAG,EAAEC,QAAQ,EAAE,GAAG,EAAE;QACrF;MACF;MAEA,IAAIJ,MAAI,CAAC/G,QAAQ,IAAI+G,MAAI,CAACxB,oBAAoB,EAAE;QAC9CwB,MAAI,CAACpH,eAAe,CAACyH,MAAM,GAAGnL,WAAW,CAACoL,MAAM;QAEhD,IAAI;UACF,MAAMzH,IAAI,SAAShD,cAAc,CAC/BmK,MAAI,CAACxB,oBAAoB,GACrBwB,MAAI,CAACtD,iBAAiB,CAAC6D,qBAAqB,CAACP,MAAI,CAAChJ,WAAW,CAAC6I,WAAW,EAAEG,MAAI,CAACxB,oBAAoB,CAAC,GACrGwB,MAAI,CAACtD,iBAAiB,CAAC8D,SAAS,CAACR,MAAI,CAAChJ,WAAW,CAAC6I,WAAW,EAAEG,MAAI,CAAC/G,QAAQ,CAAC,CAClF;UAED,IAAIJ,IAAI,EAAE;YACR;YACA,IAAImH,MAAI,CAACxB,oBAAoB,EAC3BwB,MAAI,CAAC/G,QAAQ,GAAGJ,IAAI,CAACjD,cAAc,CAAC;YAEtCoK,MAAI,CAACpH,eAAe,CAACC,IAAI,GAAGA,IAAI;YAChCmH,MAAI,CAACpH,eAAe,CAAC6H,YAAY,GAAGzL,QAAQ,CAAC6D,IAAI,CAAC;YAElD,IAAIA,IAAI,CAAC6H,QAAQ,EAAEC,UAAU,EAAE5F,MAAM,EAAE;cACrCiF,MAAI,CAACrG,iBAAiB,GAAGd,IAAI,CAAC6H,QAAQ,EAAEC,UAAU,CAAC,CAAC,CAAC;cACrDX,MAAI,CAAC1G,UAAU,GAAGT,IAAI,CAAC6H,QAAQ,EAAEC,UAAU;YAC7C;YAEA;UACF;UAEAX,MAAI,CAACtE,gBAAgB,GAAGsE,MAAI,CAACX,mBAAmB,CAAC,MAAM,CAAC;QAC1D,CAAC,CAAC,OAAOD,EAAE,EAAE;UACXY,MAAI,CAACtE,gBAAgB,GAAGsE,MAAI,CAACX,mBAAmB,CAAC,OAAO,EAAED,EAAE,CAAC;QAC/D;MACF;IAAC;EACH;EAEMjB,cAAcA,CAAA;IAAA,IAAAyC,MAAA;IAAA,OAAA7C,iBAAA;MAClB,MAAM6C,MAAI,CAACjB,YAAY,EAAE;MACzB,MAAMiB,MAAI,CAACb,WAAW,EAAE;IAAC;EAC3B;EAEAtH,YAAYA,CAACI,IAAS;IACpB,IAAI,CAACgI,mBAAmB,CAAChI,IAAI,CAAC;IAC9B,IAAI,CAACiI,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAACzD,UAAU,CAACe,IAAI,EAAE;IAEtB,IAAI,CAAC/B,WAAW,GAAG,IAAI;IACvB,IAAI,CAACK,iBAAiB,CAACqE,WAAW,CAAC,IAAI,CAACpE,qBAAqB,CAACqE,WAAW,EAAE,IAAI,CAAChI,SAAS,CAACiI,EAAE,CAAC,CAC1F3C,SAAS,CAAC;MACT4C,IAAI,EAAGhF,mBAAwC,IAAI;QACjD,IAAI,CAACA,mBAAmB,GAAGA,mBAAmB;QAC9C,IAAI,CAAC9D,qBAAqB,GAAG8D,mBAAmB,EAAEiF,iBAAiB,EAAEpG,MAAM,GAAG,CAAC;QAC/E,IAAI,CAACF,mBAAmB,GAAGqB,mBAAmB,EAAEkF,WAAW;QAC3D,IAAI,CAAC1D,iBAAiB,GAAG,IAAI,CAACtF,qBAAqB,IAAI,CAAC8D,mBAAmB,EAAEkF,WAAW;QAExF,IAAI,CAAClF,mBAAmB,CAACkF,WAAW,EAClC,IAAI,CAACvE,mBAAmB,CAACwE,WAAW,CAAC,UAAU,EAAE,uCAAuC,CAAC;QAE3F,IAAI,CAAC,IAAI,CAACjJ,qBAAqB,EAC7B,IAAI,CAACkJ,iBAAiB,EAAE;QAE1B,IAAI,CAACjF,WAAW,GAAG,KAAK;MAC1B,CAAC;MACDkF,KAAK,EAAEC,GAAG,IAAG;QACX,IAAI,CAACnF,WAAW,GAAG,KAAK;QACxB,MAAMoF,cAAc,GAAG,IAAI,CAAC7E,kBAAkB,CAACsD,eAAe,CAAC,IAAI,CAAClJ,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE0K,KAAK,EAAEC,KAAK,IAAI,IAAI,CAACvF,eAAe;QAEzH,IAAIoF,GAAG,CAACD,KAAK,EAAEnB,QAAQ,EAAE,CAACnC,QAAQ,CAAC,cAAc,CAAC,EAAE;UAClD,IAAI,CAACpB,mBAAmB,CAACiD,SAAS,CAAC,OAAO,EAAE,oCAAoC2B,cAAc,8BAA8B,CAAC;QAC/H,CAAC,MAAM;UACL,IAAI,CAAC5E,mBAAmB,CAACiD,SAAS,CAAC,OAAO,EAAE0B,GAAG,CAACD,KAAK,IAAIC,GAAG,CAACI,OAAO,CAAC;QACvE;QAEA,IAAI,CAACxE,SAAS,CAACgB,IAAI,CAAC,KAAK,CAAC;MAC5B;KACD,CAAC;EACN;EAEAkD,iBAAiBA,CAAA;IACf;IACA,IAAI,IAAI,CAAC/D,gBAAgB,EAAE;MACzB,IAAI,CAACH,SAAS,CAACgB,IAAI,CAAC,IAAI,CAAC;MACzB;IACF;IAEA,IAAI,CAAClC,mBAAmB,GAAG,IAAI;IAE/B,QAAQ,IAAI,CAAClF,WAAW,CAACkD,kBAAkB,EAAE2H,cAAc;MACzD,KAAKrM,0BAA0B,CAACsM,QAAQ;QACtC/C,MAAM,CAAChC,QAAQ,CAACgF,IAAI,GAAG,IAAI,CAAC/K,WAAW,CAACkD,kBAAkB,CAACsB,aAAa;QACxE;MACF,KAAKhG,0BAA0B,CAACwM,MAAM;QACpC,IAAI,CAACC,UAAU,EAAE;QACjB;MACF;QACE,IAAI,CAACtF,qBAAqB,CAACuF,yBAAyB,EAAE;QACtD,IAAI,CAACvE,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACH,WAAW,GAAG,IAAI;IAC3B;EACF;EAEAnC,cAAcA,CAAA;IACZ;IACA,IAAI,CAACzC,eAAe,GAAG,IAAI3D,eAAe,EAAE;IAE5C,IAAI,CAACyI,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAC4D,iBAAiB,EAAE;EAC1B;EAEMnG,aAAaA,CAAA;IAAA,IAAAgH,MAAA;IAAA,OAAApE,iBAAA;MACjB,IAAI,CAACoE,MAAI,CAACjG,mBAAmB,EAAEkG,SAAS,EAAErH,MAAM,EAAE;QAChD;MACF;MAEAoH,MAAI,CAAC9F,WAAW,GAAG,IAAI;MACvB8F,MAAI,CAAClJ,QAAQ,GAAGkJ,MAAI,CAACjG,mBAAmB,CAACkG,SAAS,CAAC,CAAC,CAAC;MACrDD,MAAI,CAACxF,qBAAqB,CAACuF,yBAAyB,EAAE;MACtD,MAAMC,MAAI,CAACpC,WAAW,EAAE;MAExBoC,MAAI,CAAC9F,WAAW,GAAG,KAAK;MACxB8F,MAAI,CAACjG,mBAAmB,GAAG,IAAI;MAC/BiG,MAAI,CAACzE,iBAAiB,GAAG,KAAK;IAAC;EACjC;EAEM2E,sBAAsBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAvE,iBAAA;MAC1B,MAAMwE,aAAa,GAAGD,MAAI,CAACtL,WAAW,CAACkD,kBAAkB,EAAEsI,UAAU,CAClEC,IAAI,CAACC,SAAS,IAAIA,SAAS,CAACC,SAAS,KAAKjN,cAAc,CAACkN,QAAQ,CAAC;MACrE,IAAI,CAACL,aAAa,EAAE;QAClB;MACF;MAEAD,MAAI,CAACtF,oBAAoB,CAACqF,sBAAsB,CAAC;QAC/CQ,gBAAgB,EAAE,IAAIpN,gBAAgB,CACpC6M,MAAI,CAACtL,WAAW,CAACiK,EAAE,EAAEqB,MAAI,CAACtL,WAAW,CAAC8L,IAAI,EAAE3N,kBAAkB,CAAC4N,IAAI,EAAET,MAAI,CAACtJ,SAAS,CAAC8J,IAAI,EACxFR,MAAI,CAACtL,WAAW,CAAC6I,WAAW,CAAC;QAC/BmD,UAAU,EAAEV,MAAI,CAACtL,WAAW,CAACkD,kBAAkB,EAAEsI,UAAU,CAACC,IAAI,CAACC,SAAS,IAAIA,SAAS,CAACC,SAAS,KAAKjN,cAAc,CAACkN,QAAQ;OAC9H,CAAC;IAAC;EACL;EAEQ/B,mBAAmBA,CAACoC,KAAU;IACpC,MAAMpK,IAAI,GAAGoK,KAAK,CAACpK,IAAc;IACjC,IAAI,CAACA,IAAI,EAAE;MACT;IACF;IAEA,IAAIoK,KAAK,CAACC,SAAS,EAAE;MACnB,IAAI,CAACC,aAAa,EAAE;IACtB;IAEA,IAAI,IAAI,CAACnM,WAAW,CAACkD,kBAAkB,EAAEkJ,cAAc,EAAE;MACvD,IAAI,CAACf,sBAAsB,EAAE;IAC/B;IAEA,IAAI,CAACzJ,eAAe,CAACC,IAAI,GAAGA,IAAI;IAEhC,IAAIoI,EAAU;IACd,QAAQ,IAAI,CAACrI,eAAe,CAACyH,MAAM;MACjC,KAAKnL,WAAW,CAACoL,MAAM;QACrB;UACEW,EAAE,GAAG,IAAI,CAACrI,eAAe,CAAC6H,YAAY,CAAC7K,cAAc,CAAC;UAEtD,IAAI,CAACoB,WAAW,CAACiJ,YAAY,CAACoD,OAAO,CAACC,KAAK,IAAG;YAC5C,MAAMC,SAAS,GAAGD,KAAK,CAACnD,GAAG,CAACC,QAAQ,EAAE;YACtC,MAAMoD,cAAc,GAAG,IAAI,CAACxK,SAAS,CAACyK,UAAU,CAACC,UAAU,CAACH,SAAS,CAAC;YAEtE,IAAIC,cAAc,EAAE;cAClB,IAAIG,UAAU,GAAG9K,IAAI,CAAC0K,SAAS,CAAC;cAEhC,IAAII,UAAU,IAAI,IAAI,CAAC/K,eAAe,CAAC6H,YAAY,CAAC8C,SAAS,CAAC,EAAE;gBAC9DI,UAAU,GAAGA,UAAU,IAAI,IAAI,GAAGhO,eAAe,CAAC6N,cAAc,CAACI,IAAI,EAAEJ,cAAc,CAACK,MAAM,CAAC,GAAGF,UAAU;gBAC1G,IAAI,CAAChH,qBAAqB,CAACmH,WAAW,CAAC7C,EAAE,EAAEsC,SAAS,EAAE,IAAI,CAAC3K,eAAe,CAAC6H,YAAY,CAAC8C,SAAS,CAAC,EAAEI,UAAU,EAAEzO,WAAW,CAACoL,MAAM,CAAC;cACrI;YACF;UACF,CAAC,CAAC;QACJ;QACA;MACF,KAAKpL,WAAW,CAAC6O,MAAM;QACrB;UACE9C,EAAE,GAAG,KAAK;UAEV,IAAI,CAACjK,WAAW,CAACiJ,YAAY,CAACoD,OAAO,CAACC,KAAK,IAAG;YAC5C,MAAMC,SAAS,GAAGD,KAAK,CAACnD,GAAG,CAACC,QAAQ,EAAE;YACtC,MAAMoD,cAAc,GAAG,IAAI,CAACxK,SAAS,CAACyK,UAAU,CAACC,UAAU,CAACH,SAAS,CAAC;YAEtE,IAAIC,cAAc,EAAE;cAClB,MAAMG,UAAU,GAAI9K,IAAI,CAAC0K,SAAS,CAAC,KAAKS,SAAS,IAAInL,IAAI,CAAC0K,SAAS,CAAC,KAAK,IAAI,GAAI1K,IAAI,CAAC0K,SAAS,CAAC,GAAG5N,eAAe,CAAC6N,cAAc,CAACI,IAAI,EAAEJ,cAAc,CAACK,MAAM,CAAC;cAC9J,IAAI,CAAClH,qBAAqB,CAACmH,WAAW,CAAC7C,EAAE,EAAEsC,SAAS,EAAES,SAAS,EAAEL,UAAU,EAAEzO,WAAW,CAAC6O,MAAM,CAAC;YAClG;UACF,CAAC,CAAC;QACJ;QACA;IACJ;IAEA,IAAI,CAACpH,qBAAqB,CAACsH,gBAAgB,CAAChD,EAAE,EAAEgC,KAAK,CAACiB,QAAQ,CAAC;IAE/D;IACA,IAAI,IAAI,CAACzG,gBAAgB,EACvB,IAAI,CAACd,qBAAqB,CAACwH,eAAe,CAAClD,EAAE,EAAE,IAAI,CAAC3H,UAAU,CAAC;IAEjE;IACA,IAAI,CAACV,eAAe,CAAC6H,YAAY,GAAGzL,QAAQ,CAAC6D,IAAI,CAAC;EACpD;EAEAoB,MAAMA,CAAA;IACJ,IAAI,CAACtB,gBAAgB,CAACyF,IAAI,EAAE;EAC9B;EAEAF,iBAAiBA,CAAA;IACf,IAAI,CAAChB,oBAAoB,CAACgE,IAAI,CAAC;MAC7BkD,WAAW,EAAE,IAAI,CAAClH,oBAAoB,CAACmH,cAAc;MACrDC,SAAS,EAAE,IAAI,CAACpH,oBAAoB,CAACqH,iBAAiB;MACtD5B,SAAS,EAAE,mBAAmB;MAC9B9J,IAAI,EAAE;KACP,CAAC;EACJ;EAEAoJ,UAAUA,CAAA;IACR;IACA,IAAI,IAAI,CAACzF,cAAc,CAACiC,QAAQ,CAACJ,MAAM,CAAC,UAAU,CAAC,EAAE;MACnD,IAAI,CAACtB,QAAQ,CAACyH,EAAE,CAAC,8BAA8B,IAAI,CAAC1L,SAAS,IAAI,IAAI,CAACC,gBAAgB,UAAU,IAAI,CAAC/B,WAAW,CAACiK,EAAE,EAAE,CAAC;MACtH;IACF;IAEA,IAAI,CAAChI,QAAQ,GAAG+K,SAAS;IACzB,IAAI,CAACvG,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC9D,iBAAiB,GAAGqK,SAAS;IAClC,IAAI,CAAC1K,UAAU,GAAG,EAAE;IAEpB,IAAI,CAACqD,qBAAqB,CAACuF,yBAAyB,EAAE;IACtD,IAAI,CAACtF,kBAAkB,CAAC6H,kBAAkB,CAACvD,IAAI,EAAE;IAEjDnC,MAAM,CAAC2F,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB;EAEAvB,aAAaA,CAAA;IACX,MAAMwB,UAAU,GAAe;MAC7BC,OAAO,EAAE,wBAAwB;MACjChD,OAAO,EAAE,IAAI,CAACiD,kBAAkB;KACjC;IAED,IAAI,CAAC7H,oBAAoB,CAAC8H,iBAAiB,CAACH,UAAU,CAAC;EACzD;EAEAE,kBAAkBA,CAAA;IAChB,MAAME,YAAY,GAAG,IAAI,CAAC/N,WAAW,CAACkD,kBAAkB,CAAC6K,YAAY,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,GAC/G,KAAKjG,MAAM,CAAChC,QAAQ,CAACgF,IAAI,eAAe,GACxC,kCAAkC;IAEpC,OAAOgD,YAAY;EACrB;EAEAnL,eAAeA,CAACqL,SAAS;IACvB,IAAI,CAACxH,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACnE,UAAU,GAAG2L,SAAS,GAAG,CAACA,SAAS,CAAC,GAAG,EAAE;EAChD;EAEAhJ,+BAA+BA,CAAC9E,OAAgB;IAC9C;IACA,IAAI,IAAI,CAACoG,gBAAgB,IAAI,CAACpG,OAAO,IAAI,IAAI,CAACuG,iBAAiB,EAAE;MAC/D,IAAI,CAACN,SAAS,CAACgB,IAAI,CAAC,IAAI,CAAC;MACzB;IACF;EACF;EAGAS,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC5B,eAAe,CAAC4B,iBAAiB,CAAC,GAAG,IAAI,CAAC9F,gBAAgB,EAAE,CAAC;EAC3E;EAEM2G,eAAeA,CAAA;IAAA,IAAAwF,MAAA;IAAA,OAAAnH,iBAAA;MACnB,IAAImH,MAAI,CAACtG,cAAc,EAAE;QACvB,MAAMuG,KAAK,GAAe,CACxB;UAAExD,KAAK,EAAE,SAAS;UAAEyD,UAAU,EAAE;QAAe,CAAE,EACjD;UACEzD,KAAK,EAAEuD,MAAI,CAACtG,cAAc,CAACyG,WAAW,EAAEvC,IAAI;UAC5CsC,UAAU,EAAE,aAAaF,MAAI,CAAClO,WAAW,CAAC8B,SAAS,IAAIoM,MAAI,CAAClO,WAAW,CAAC+B,gBAAgB;SACzF,EACD;UAAE4I,KAAK,EAAEuD,MAAI,CAAClO,WAAW,EAAE8L;QAAI,CAAE,CAClC;QACDoC,MAAI,CAAChI,oBAAoB,CAACoI,gBAAgB,CAACH,KAAK,CAAC;MACnD;IAAC;EACH;EAEQ9F,mBAAmBA,CAACuE,IAA6C,EAAExE,EAAE,GAAG,IAAI;IAClF,QAAQwE,IAAI;MACV,KAAK,MAAM;QACT,OAAO;UACLjI,KAAK,EAAE,iBAAiB;UACxBC,OAAO,EAAE,sBAAsB,IAAI,CAAC4C,oBAAoB,GAAG,aAAa,GAAG,WAAW,QAAQ,IAAI,CAACA,oBAAoB,IAAI,IAAI,CAACvF,QAAQ;kGAChD;UACxF4C,WAAW,EAAE,oCAAoC,IAAI,CAAC2C,oBAAoB,GAAG,aAAa,GAAG,WAAW,QAAQ,IAAI,CAACA,oBAAoB,IAAI,IAAI,CAACvF,QAAQ;oDAChH,IAAI,CAACD,SAAS,EAAEuM,WAAW;SACtE;MACH,KAAK,WAAW;QACd,OAAO;UACL5J,KAAK,EAAE,sBAAsB;UAC7BC,OAAO,EAAE;SACV;MACH,KAAK,MAAM;QACT,OAAO;UACLD,KAAK,EAAE,iBAAiB;UACxBC,OAAO,EAAE;SACV;MACH,KAAK,OAAO;QACV,OAAO;UACLD,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE,KAAK,IAAIwD,EAAE,YAAYhK,iBAAiB,GAAGgK,EAAE,CAACoG,UAAU,GAAGpG,EAAE,EAAEmC,KAAK,IAAInC,EAAE,EAAEwC,OAAO,CAAC,GAAG;SACjG;IACL;EACF;;;uBAleWtF,sBAAsB,EAAA9F,EAAA,CAAAiP,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAnP,EAAA,CAAAiP,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAApP,EAAA,CAAAiP,iBAAA,CAAAI,EAAA,CAAAC,iBAAA,GAAAtP,EAAA,CAAAiP,iBAAA,CAAAM,EAAA,CAAAC,qBAAA,GAAAxP,EAAA,CAAAiP,iBAAA,CAAAQ,EAAA,CAAAC,mBAAA,GAAA1P,EAAA,CAAAiP,iBAAA,CAAAU,EAAA,CAAAC,mBAAA,GAAA5P,EAAA,CAAAiP,iBAAA,CAAAY,EAAA,CAAAC,mBAAA,GAAA9P,EAAA,CAAAiP,iBAAA,CAAAc,EAAA,CAAAC,QAAA,GAAAhQ,EAAA,CAAAiP,iBAAA,CAAAgB,EAAA,CAAAC,oBAAA,GAAAlQ,EAAA,CAAAiP,iBAAA,CAAAkB,EAAA,CAAAC,eAAA,GAAApQ,EAAA,CAAAiP,iBAAA,CA2DvBnQ,kBAAkB;IAAA;EAAA;;;YA3DjBgH,sBAAsB;MAAAuK,SAAA;MAAAC,MAAA;QAAA9P,WAAA;QAAAgC,SAAA;QAAAC,QAAA;QAAAN,gBAAA;QAAAG,SAAA;QAAAC,gBAAA;QAAAG,SAAA;QAAAC,YAAA;QAAAC,mBAAA;MAAA;MAAA2N,OAAA;QAAA3J,SAAA;QAAAC,UAAA;QAAAC,QAAA;MAAA;MAAA0J,UAAA;MAAAC,QAAA,GAAAzQ,EAAA,CAAA0Q,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC9CjChR,EADF,CAAAC,cAAA,aAA2B,aACgD;UAuEvED,EAtEA,CAAAiB,UAAA,IAAAiQ,qCAAA,kBAAsH,IAAAC,qCAAA,kBAqDpD,IAAAC,qCAAA,iBAY0E,IAAAC,sDAAA,kCAOxE;UAExErR,EADE,CAAAG,YAAA,EAAM,EACF;UAKNH,EAJA,CAAAiB,UAAA,IAAAqQ,+DAAA,2CAEsH,IAAAC,2CAAA,uBAE9C;;;UAhFxCvR,EAAA,CAAAa,SAAA,EAA0C;UAA1Cb,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAK,eAAA,IAAAmR,GAAA,EAAAP,GAAA,CAAA3L,cAAA,EAA0C;UAChEtF,EAAA,CAAAa,SAAA,EAA2C;UAA3Cb,EAAA,CAAAI,UAAA,SAAA6Q,GAAA,CAAA9J,eAAA,KAAA8J,GAAA,CAAA/J,iBAAA,CAA2C;UAqD3ClH,EAAA,CAAAa,SAAA,EAAuB;UAAvBb,EAAA,CAAAI,UAAA,SAAA6Q,GAAA,CAAA/J,iBAAA,CAAuB;UAY1BlH,EAAA,CAAAa,SAAA,EAAuI;UAAvIb,EAAA,CAAAI,UAAA,UAAA6Q,GAAA,CAAAtO,YAAA,IAAAsO,GAAA,CAAAjK,WAAA,KAAAiK,GAAA,CAAAzQ,WAAA,CAAAkD,kBAAA,kBAAAuN,GAAA,CAAAzQ,WAAA,CAAAkD,kBAAA,CAAA2H,cAAA,MAAA4F,GAAA,CAAAjS,0BAAA,CAAAyS,mBAAA,CAAuI;UAKnHzR,EAAA,CAAAa,SAAA,EAAsB;UAAtBb,EAAA,CAAAI,UAAA,SAAA6Q,GAAA,CAAA/L,gBAAA,CAAsB;UAKjBlF,EAAA,CAAAa,SAAA,EAA2B;UAA3Bb,EAAA,CAAAI,UAAA,SAAA6Q,GAAA,CAAArP,qBAAA,CAA2B;UAI/C5B,EAAA,CAAAa,SAAA,EAAiB;UAAjBb,EAAA,CAAAI,UAAA,SAAA6Q,GAAA,CAAApL,WAAA,CAAiB;;;qBDpCfvH,IAAI,EAAEC,OAAO,EAAEwB,2BAA2B,EAAED,wBAAwB,EAAED,2BAA2B,EAAED,wBAAwB,EAAED,YAAY,EAAA+R,GAAA,CAAAC,MAAA,EAAEjS,uBAAuB,EAAED,gCAAgC,EAAED,aAAa,EAAAoS,GAAA,CAAAC,OAAA,EAAEtS,YAAY;MAAAuS,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}