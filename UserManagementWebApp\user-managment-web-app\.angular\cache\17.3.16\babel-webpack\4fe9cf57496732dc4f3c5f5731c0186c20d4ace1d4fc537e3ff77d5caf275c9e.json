{"ast": null, "code": "import { windowExists, globalExists, keyPrefixMatch } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with next 13.4 / react 18.2\nfunction nextWebDetect() {\n  return windowExists() && window.next && typeof window.next === 'object';\n}\nfunction nextSSRDetect() {\n  return globalExists() && (keyPrefixMatch(global, '__next') || keyPrefixMatch(global, '__NEXT'));\n}\nexport { nextSSRDetect, nextWebDetect };", "map": {"version": 3, "names": ["windowExists", "globalExists", "keyPrefixMatch", "nextWebDetect", "window", "next", "nextSSRDetect", "global"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Platform/detection/Next.mjs"], "sourcesContent": ["import { windowExists, globalExists, keyPrefixMatch } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with next 13.4 / react 18.2\nfunction nextWebDetect() {\n    return (windowExists() &&\n        window.next &&\n        typeof window.next === 'object');\n}\nfunction nextSSRDetect() {\n    return (globalExists() &&\n        (keyPrefixMatch(global, '__next') || keyPrefixMatch(global, '__NEXT')));\n}\n\nexport { nextSSRDetect, nextWebDetect };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,YAAY,EAAEC,cAAc,QAAQ,eAAe;;AAE1E;AACA;AACA;AACA,SAASC,aAAaA,CAAA,EAAG;EACrB,OAAQH,YAAY,CAAC,CAAC,IAClBI,MAAM,CAACC,IAAI,IACX,OAAOD,MAAM,CAACC,IAAI,KAAK,QAAQ;AACvC;AACA,SAASC,aAAaA,CAAA,EAAG;EACrB,OAAQL,YAAY,CAAC,CAAC,KACjBC,cAAc,CAACK,MAAM,EAAE,QAAQ,CAAC,IAAIL,cAAc,CAACK,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC9E;AAEA,SAASD,aAAa,EAAEH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}