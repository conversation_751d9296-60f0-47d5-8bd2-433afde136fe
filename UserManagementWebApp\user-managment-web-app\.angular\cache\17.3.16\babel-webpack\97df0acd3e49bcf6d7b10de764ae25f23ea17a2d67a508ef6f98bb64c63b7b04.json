{"ast": null, "code": "const nlDict = {\n  'Account recovery requires verified contact information': 'Accountherstel vereist geverifieerde contactgegevens',\n  'Authenticator App (TOTP)': 'Authenticator-app (TOTP)',\n  'Back to Sign In': 'Terug naar inloggen',\n  'Change Password': 'Wachtwoord wijzigen',\n  Changing: 'Wordt aangepast',\n  Code: 'Code',\n  'Confirm Email Code': 'E-mailcode bevestigen',\n  'Confirm Password': 'Bevestig Wachtwoord',\n  'Confirm Sign Up': 'Bevestig inschrijving',\n  'Confirm SMS Code': 'Bevestig SMS Code',\n  'Confirm TOTP Code': 'Bevestig TOTP Code',\n  Confirm: 'Bevestig',\n  'Confirmation Code': 'Bevestigingscode',\n  Confirming: 'Bevestigen',\n  'Create a new account': 'Nieuw account aanmaken',\n  'Create Account': 'Account aanmaken',\n  'Creating Account': 'Account wordt aangemaakt',\n  'Dismiss alert': 'Waarschuwing sluiten',\n  Email: 'E-mail',\n  'Email Message': 'E-mailbericht',\n  'Enter your code': 'Vul je code in',\n  'Enter your Email': 'Vul je e-mail in',\n  'Enter your Password': 'Vul je wachtwoord in',\n  'Enter your phone number': 'Vul je telefoonnummer in',\n  'Enter your username': 'Vul je gebruikersnaam in',\n  'Enter your Username': 'Vul je gebruikersnaam in',\n  'Forgot your password?': 'Wachtwoord vergeten? ',\n  'Hide password': 'Verberg wachtwoord',\n  'It may take a minute to arrive': 'Het kan even duren voordat deze aankomt',\n  Loading: 'Laden',\n  'Multi-Factor Authentication': 'Multi-Factor Authentication',\n  'Multi-Factor Authentication Setup': 'Multi-Factor Authentication instellen',\n  'New password': 'Nieuw wachtwoord',\n  'New Password': 'Nieuw Wachtwoord',\n  or: 'of',\n  Password: 'Wachtwoord',\n  'Phone Number': 'Telefoonnummer',\n  'Please confirm your Password': 'Bevestig je wachtwoord',\n  'Resend Code': 'Verstuur code nogmaals',\n  'Reset Password': 'Wachtwoord resetten',\n  'Reset your password': 'Reset je wachtwoord',\n  'Reset your Password': 'Wachtwoord resetten',\n  'Select MFA Type': 'MFA-type kiezen',\n  'Send code': 'Verstuur code',\n  'Send Code': 'Verstuur Code',\n  Sending: 'Versturen',\n  'Setup Email': 'E-mailadres instellen',\n  'Setup TOTP': 'TOTP Instellingen',\n  'Show password': 'Toon wachtwoord',\n  'Sign in to your account': 'Inloggen op je account',\n  'Sign In with Amazon': 'Inloggen met Amazon',\n  'Sign In with Apple': 'Inloggen met Apple',\n  'Sign In with Facebook': 'Inloggen met Facebook',\n  'Sign In with Google': 'Inloggen met Google',\n  'Sign in': 'Inloggen',\n  'Sign In': 'Inloggen',\n  'Signing in': 'Inloggen',\n  Skip: 'Overslaan',\n  Submit: 'Versturen',\n  Submitting: 'Wordt verstuurd',\n  'Text Message (SMS)': 'Tekstbericht (sms)',\n  Username: 'Gebruikersnaam',\n  'Verify Contact': 'Verifieer Contact',\n  Verify: 'Verifieer',\n  'We Emailed You': 'We hebben u een e-mail gestuurd',\n  'We Sent A Code': 'We hebben een code gestuurd',\n  'We Texted You': 'We hebben u een sms gestuurd',\n  'Your code is on the way. To log in, enter the code we emailed to': 'Uw code is onderweg. Om in te loggen, voer de code in die we gemaild hebben naar',\n  'Your code is on the way. To log in, enter the code we sent you': 'Uw code is onderweg. Om in te loggen, voer de code in die we u hebben gestuurd',\n  'Your code is on the way. To log in, enter the code we texted to': 'Uw code is onderweg. Om in te loggen, voer de code in die we hebben gestuurd naar',\n  'Your passwords must match': 'Je wachtwoorden moeten overeenkomen'\n};\nexport { nlDict };", "map": {"version": 3, "names": ["nlDict", "Changing", "Code", "Confirm", "Confirming", "Email", "Loading", "or", "Password", "Sending", "<PERSON><PERSON>", "Submit", "Submitting", "Username", "Verify"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/i18n/dictionaries/authenticator/nl.mjs"], "sourcesContent": ["const nlDict = {\n    'Account recovery requires verified contact information': 'Accountherstel vereist geverifieerde contactgegevens',\n    'Authenticator App (TOTP)': 'Authenticator-app (TOTP)',\n    'Back to Sign In': 'Terug naar inloggen',\n    'Change Password': 'Wachtwoord wijzigen',\n    Changing: 'Wordt aangepast',\n    Code: 'Code',\n    'Confirm Email Code': 'E-mailcode bevestigen',\n    'Confirm Password': 'Bevestig Wachtwoord',\n    'Confirm Sign Up': 'Bevestig inschrijving',\n    'Confirm SMS Code': 'Bevestig SMS Code',\n    'Confirm TOTP Code': 'Bevestig TOTP Code',\n    Confirm: 'Bevestig',\n    'Confirmation Code': 'Bevestigingscode',\n    Confirming: 'Bevestigen',\n    'Create a new account': 'Nieuw account aanmaken',\n    'Create Account': 'Account aanmaken',\n    'Creating Account': 'Account wordt aangemaakt',\n    'Dismiss alert': 'Waarschuwing sluiten',\n    Email: 'E-mail',\n    'Email Message': 'E-mailbericht',\n    'Enter your code': 'Vul je code in',\n    'Enter your Email': 'Vul je e-mail in',\n    'Enter your Password': 'Vul je wachtwoord in',\n    'Enter your phone number': 'Vul je telefoonnummer in',\n    'Enter your username': 'Vul je gebruikersnaam in',\n    'Enter your Username': 'Vul je gebruikersnaam in',\n    'Forgot your password?': 'Wachtwoord vergeten? ',\n    'Hide password': 'Verberg wachtwoord',\n    'It may take a minute to arrive': 'Het kan even duren voordat deze aankomt',\n    Loading: 'Laden',\n    'Multi-Factor Authentication': 'Multi-Factor Authentication',\n    'Multi-Factor Authentication Setup': 'Multi-Factor Authentication instellen',\n    'New password': 'Nieuw wachtwoord',\n    'New Password': 'Nieuw Wachtwoord',\n    or: 'of',\n    Password: 'Wachtwoord',\n    'Phone Number': 'Telefoonnummer',\n    'Please confirm your Password': 'Bevestig je wachtwoord',\n    'Resend Code': 'Verstuur code nogmaals',\n    'Reset Password': 'Wachtwoord resetten',\n    'Reset your password': 'Reset je wachtwoord',\n    'Reset your Password': 'Wachtwoord resetten',\n    'Select MFA Type': 'MFA-type kiezen',\n    'Send code': 'Verstuur code',\n    'Send Code': 'Verstuur Code',\n    Sending: 'Versturen',\n    'Setup Email': 'E-mailadres instellen',\n    'Setup TOTP': 'TOTP Instellingen',\n    'Show password': 'Toon wachtwoord',\n    'Sign in to your account': 'Inloggen op je account',\n    'Sign In with Amazon': 'Inloggen met Amazon',\n    'Sign In with Apple': 'Inloggen met Apple',\n    'Sign In with Facebook': 'Inloggen met Facebook',\n    'Sign In with Google': 'Inloggen met Google',\n    'Sign in': 'Inloggen',\n    'Sign In': 'Inloggen',\n    'Signing in': 'Inloggen',\n    Skip: 'Overslaan',\n    Submit: 'Versturen',\n    Submitting: 'Wordt verstuurd',\n    'Text Message (SMS)': 'Tekstbericht (sms)',\n    Username: 'Gebruikersnaam',\n    'Verify Contact': 'Verifieer Contact',\n    Verify: 'Verifieer',\n    'We Emailed You': 'We hebben u een e-mail gestuurd',\n    'We Sent A Code': 'We hebben een code gestuurd',\n    'We Texted You': 'We hebben u een sms gestuurd',\n    'Your code is on the way. To log in, enter the code we emailed to': 'Uw code is onderweg. Om in te loggen, voer de code in die we gemaild hebben naar',\n    'Your code is on the way. To log in, enter the code we sent you': 'Uw code is onderweg. Om in te loggen, voer de code in die we u hebben gestuurd',\n    'Your code is on the way. To log in, enter the code we texted to': 'Uw code is onderweg. Om in te loggen, voer de code in die we hebben gestuurd naar',\n    'Your passwords must match': 'Je wachtwoorden moeten overeenkomen',\n};\n\nexport { nlDict };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACX,wDAAwD,EAAE,sDAAsD;EAChH,0BAA0B,EAAE,0BAA0B;EACtD,iBAAiB,EAAE,qBAAqB;EACxC,iBAAiB,EAAE,qBAAqB;EACxCC,QAAQ,EAAE,iBAAiB;EAC3BC,IAAI,EAAE,MAAM;EACZ,oBAAoB,EAAE,uBAAuB;EAC7C,kBAAkB,EAAE,qBAAqB;EACzC,iBAAiB,EAAE,uBAAuB;EAC1C,kBAAkB,EAAE,mBAAmB;EACvC,mBAAmB,EAAE,oBAAoB;EACzCC,OAAO,EAAE,UAAU;EACnB,mBAAmB,EAAE,kBAAkB;EACvCC,UAAU,EAAE,YAAY;EACxB,sBAAsB,EAAE,wBAAwB;EAChD,gBAAgB,EAAE,kBAAkB;EACpC,kBAAkB,EAAE,0BAA0B;EAC9C,eAAe,EAAE,sBAAsB;EACvCC,KAAK,EAAE,QAAQ;EACf,eAAe,EAAE,eAAe;EAChC,iBAAiB,EAAE,gBAAgB;EACnC,kBAAkB,EAAE,kBAAkB;EACtC,qBAAqB,EAAE,sBAAsB;EAC7C,yBAAyB,EAAE,0BAA0B;EACrD,qBAAqB,EAAE,0BAA0B;EACjD,qBAAqB,EAAE,0BAA0B;EACjD,uBAAuB,EAAE,uBAAuB;EAChD,eAAe,EAAE,oBAAoB;EACrC,gCAAgC,EAAE,yCAAyC;EAC3EC,OAAO,EAAE,OAAO;EAChB,6BAA6B,EAAE,6BAA6B;EAC5D,mCAAmC,EAAE,uCAAuC;EAC5E,cAAc,EAAE,kBAAkB;EAClC,cAAc,EAAE,kBAAkB;EAClCC,EAAE,EAAE,IAAI;EACRC,QAAQ,EAAE,YAAY;EACtB,cAAc,EAAE,gBAAgB;EAChC,8BAA8B,EAAE,wBAAwB;EACxD,aAAa,EAAE,wBAAwB;EACvC,gBAAgB,EAAE,qBAAqB;EACvC,qBAAqB,EAAE,qBAAqB;EAC5C,qBAAqB,EAAE,qBAAqB;EAC5C,iBAAiB,EAAE,iBAAiB;EACpC,WAAW,EAAE,eAAe;EAC5B,WAAW,EAAE,eAAe;EAC5BC,OAAO,EAAE,WAAW;EACpB,aAAa,EAAE,uBAAuB;EACtC,YAAY,EAAE,mBAAmB;EACjC,eAAe,EAAE,iBAAiB;EAClC,yBAAyB,EAAE,wBAAwB;EACnD,qBAAqB,EAAE,qBAAqB;EAC5C,oBAAoB,EAAE,oBAAoB;EAC1C,uBAAuB,EAAE,uBAAuB;EAChD,qBAAqB,EAAE,qBAAqB;EAC5C,SAAS,EAAE,UAAU;EACrB,SAAS,EAAE,UAAU;EACrB,YAAY,EAAE,UAAU;EACxBC,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE,WAAW;EACnBC,UAAU,EAAE,iBAAiB;EAC7B,oBAAoB,EAAE,oBAAoB;EAC1CC,QAAQ,EAAE,gBAAgB;EAC1B,gBAAgB,EAAE,mBAAmB;EACrCC,MAAM,EAAE,WAAW;EACnB,gBAAgB,EAAE,iCAAiC;EACnD,gBAAgB,EAAE,6BAA6B;EAC/C,eAAe,EAAE,8BAA8B;EAC/C,kEAAkE,EAAE,kFAAkF;EACtJ,gEAAgE,EAAE,gFAAgF;EAClJ,iEAAiE,EAAE,mFAAmF;EACtJ,2BAA2B,EAAE;AACjC,CAAC;AAED,SAASd,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}