{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/IntegrationWebApp/integration-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { constants } from '../constants/constants';\nimport { AppService } from '../services/app.service';\nimport { BreadcrumbService } from '../services/breadcrumb.service';\nimport { ProjectsService } from '../services/projects.service';\nimport { RouterInfoService } from '../services/router-info.service';\nimport { CompanyService } from '../services/company.service ';\nimport { firstValueFrom } from 'rxjs';\nimport { TenantFeature } from '../enums/tenant-feature.enum';\nexport const appAuthGuard = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (route) {\n    const appService = inject(AppService);\n    const projectsService = inject(ProjectsService);\n    const breadcrumbService = inject(BreadcrumbService);\n    const routerInfoService = inject(RouterInfoService);\n    const router = inject(Router);\n    const companyService = inject(CompanyService);\n    const routerParams = routerInfoService.collectRouteParams(route.root);\n    const versionId = +routerParams.versionId;\n    const assetId = routerParams.workflowId;\n    let appGroupRoles = appService.cachedAppGroupRoles;\n    if (!appGroupRoles?.length) {\n      appGroupRoles = yield firstValueFrom(appService.fetchAppGroupRolesForCurrentUserTenant());\n    }\n    let app = null;\n    if (assetId) {\n      app = yield getApp(breadcrumbService, projectsService, versionId);\n    }\n    const hasAccess = yield checkAccess(companyService, router, appGroupRoles, versionId, assetId, app);\n    return hasAccess;\n  });\n  return function appAuthGuard(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nfunction getApp(_x2, _x3, _x4) {\n  return _getApp.apply(this, arguments);\n}\nfunction _getApp() {\n  _getApp = _asyncToGenerator(function* (breadcrumbService, projectsService, versionId) {\n    let currentProjectVersion = versionId ? breadcrumbService?.currentProjectVersion : null;\n    let app = null;\n    if (!currentProjectVersion || currentProjectVersion.id !== versionId) {\n      currentProjectVersion = yield firstValueFrom(projectsService.getProjectVersion(versionId));\n      breadcrumbService.currentProjectVersion = currentProjectVersion;\n    }\n    if (currentProjectVersion?.isApp) {\n      app = currentProjectVersion.appManifest;\n    }\n    return app;\n  });\n  return _getApp.apply(this, arguments);\n}\nfunction checkAccess(_x5, _x6, _x7, _x8, _x9, _x10) {\n  return _checkAccess.apply(this, arguments);\n}\nfunction _checkAccess() {\n  _checkAccess = _asyncToGenerator(function* (companyService, router, appGroupRoles, versionId, assetId, app) {\n    let filteredAppGroupRoles = [...appGroupRoles];\n    if (versionId) {\n      filteredAppGroupRoles = filteredAppGroupRoles.filter(appGroupRole => appGroupRole.projectVersionId === versionId);\n    }\n    if (assetId) {\n      const defaultMenuDetails = app?.menus?.find(m => m.name === 'Default');\n      const roles = defaultMenuDetails?.roles ?? [];\n      const assetRoles = roles.filter(role => role.assets.some(asset => asset.identifier === assetId && asset.permission !== constants.appPermissions.nonePermission.name));\n      filteredAppGroupRoles = filteredAppGroupRoles.filter(appGroupRole => assetRoles.some(role => role.code === appGroupRole.roleCode));\n    }\n    let hasAccess = filteredAppGroupRoles.length > 0;\n    if (hasAccess) {\n      // If the company info is not in cache or is an empty object({}), get it from the API.\n      if (!companyService.getCompanyInformationFromCache()?.id) {\n        yield firstValueFrom(companyService.getCompany());\n      }\n      hasAccess = companyService.hasTenantFeatures(TenantFeature.APP_DASHBOARD);\n    }\n    if (!hasAccess) {\n      router.navigateByUrl('/accessDenied');\n    }\n    return hasAccess;\n  });\n  return _checkAccess.apply(this, arguments);\n}", "map": {"version": 3, "names": ["inject", "Router", "constants", "AppService", "BreadcrumbService", "ProjectsService", "RouterInfoService", "CompanyService", "firstValueFrom", "TenantFeature", "appAuthGuard", "_ref", "_asyncToGenerator", "route", "appService", "projectsService", "breadcrumbService", "routerInfoService", "router", "companyService", "routerParams", "collectRouteParams", "root", "versionId", "assetId", "workflowId", "appGroupRoles", "cachedAppGroupRoles", "length", "fetchAppGroupRolesForCurrentUserTenant", "app", "getApp", "hasAccess", "checkAccess", "_x", "apply", "arguments", "_x2", "_x3", "_x4", "_getApp", "currentProjectVersion", "id", "getProjectVersion", "isApp", "appManifest", "_x5", "_x6", "_x7", "_x8", "_x9", "_x10", "_checkAccess", "filteredAppGroupRoles", "filter", "appGroupRole", "projectVersionId", "defaultMenuDetails", "menus", "find", "m", "name", "roles", "assetRoles", "role", "assets", "some", "asset", "identifier", "permission", "appPermissions", "nonePermission", "code", "roleCode", "getCompanyInformationFromCache", "getCompany", "hasTenantFeatures", "APP_DASHBOARD", "navigateByUrl"], "sources": ["C:\\Projects\\IntegrationPlatform\\IntegrationWebApp\\integration-web-app\\src\\app\\core\\guards\\app-auth.guard.ts"], "sourcesContent": ["import { inject } from '@angular/core';\r\nimport { ActivatedRouteSnapshot, CanActivateFn, Router } from '@angular/router';\r\nimport { constants } from '../constants/constants';\r\nimport { AppGroupRole } from '../models/app-group-role';\r\nimport { Application } from '../models/application';\r\nimport { AppService } from '../services/app.service';\r\nimport { BreadcrumbService } from '../services/breadcrumb.service';\r\nimport { ProjectsService } from '../services/projects.service';\r\nimport { RouterInfoService } from '../services/router-info.service';\r\nimport { CompanyService } from '../services/company.service ';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { TenantFeature } from '../enums/tenant-feature.enum';\r\n\r\nexport const appAuthGuard: CanActivateFn = async (route: ActivatedRouteSnapshot) => {\r\n  const appService = inject(AppService);\r\n  const projectsService = inject(ProjectsService);\r\n  const breadcrumbService = inject(BreadcrumbService);\r\n  const routerInfoService = inject(RouterInfoService);\r\n  const router = inject(Router);\r\n  const companyService = inject(CompanyService);\r\n  const routerParams = routerInfoService\r\n    .collectRouteParams(route.root);\r\n  const versionId = +routerParams.versionId;\r\n  const assetId = routerParams.workflowId;\r\n  let appGroupRoles = appService.cachedAppGroupRoles;\r\n  if (!appGroupRoles?.length) {\r\n    appGroupRoles = await firstValueFrom(appService.fetchAppGroupRolesForCurrentUserTenant());\r\n  }\r\n\r\n  let app: Application = null;\r\n\r\n  if (assetId) {\r\n    app = await getApp(breadcrumbService, projectsService, versionId);\r\n  }\r\n\r\n  const hasAccess = await checkAccess(companyService, router, appGroupRoles, versionId, assetId, app);\r\n  return hasAccess;\r\n};\r\n\r\nasync function getApp(breadcrumbService: BreadcrumbService, projectsService: ProjectsService, versionId: number) {\r\n  let currentProjectVersion = versionId ? breadcrumbService?.currentProjectVersion : null;\r\n  let app: Application = null;\r\n\r\n  if (!currentProjectVersion || currentProjectVersion.id !== versionId) {\r\n    currentProjectVersion = await firstValueFrom(projectsService.getProjectVersion(versionId));\r\n    breadcrumbService.currentProjectVersion = currentProjectVersion;\r\n  }\r\n\r\n  if (currentProjectVersion?.isApp) {\r\n    app = currentProjectVersion.appManifest;\r\n  }\r\n\r\n  return app;\r\n}\r\n\r\nasync function checkAccess(companyService: CompanyService, router: Router, appGroupRoles: AppGroupRole[], versionId?: number, assetId?: string, app?: Application) {\r\n  let filteredAppGroupRoles = [...appGroupRoles];\r\n  if (versionId) {\r\n    filteredAppGroupRoles = filteredAppGroupRoles\r\n      .filter(appGroupRole => appGroupRole.projectVersionId === versionId);\r\n  }\r\n\r\n  if (assetId) {\r\n    const defaultMenuDetails = app?.menus?.find(m => m.name === 'Default');\r\n    const roles = defaultMenuDetails?.roles ?? [];\r\n    const assetRoles = roles\r\n      .filter(role => role.assets\r\n        .some(asset => asset.identifier === assetId &&\r\n          asset.permission !== constants.appPermissions.nonePermission.name));\r\n    filteredAppGroupRoles = filteredAppGroupRoles\r\n      .filter(appGroupRole => assetRoles.some(role => role.code === appGroupRole.roleCode));\r\n  }\r\n\r\n  let hasAccess = filteredAppGroupRoles.length > 0;\r\n  if (hasAccess) {\r\n    // If the company info is not in cache or is an empty object({}), get it from the API.\r\n    if(!companyService.getCompanyInformationFromCache()?.id) {\r\n      await firstValueFrom(companyService.getCompany());\r\n    }\r\n\r\n    hasAccess = companyService.hasTenantFeatures(TenantFeature.APP_DASHBOARD);\r\n  }\r\n\r\n  if (!hasAccess) {\r\n    router.navigateByUrl('/accessDenied');\r\n  }\r\n\r\n  return hasAccess;\r\n}\r\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAAgDC,MAAM,QAAQ,iBAAiB;AAC/E,SAASC,SAAS,QAAQ,wBAAwB;AAGlD,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,cAAc,QAAQ,MAAM;AACrC,SAASC,aAAa,QAAQ,8BAA8B;AAE5D,OAAO,MAAMC,YAAY;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAkB,WAAOC,KAA6B,EAAI;IACjF,MAAMC,UAAU,GAAGd,MAAM,CAACG,UAAU,CAAC;IACrC,MAAMY,eAAe,GAAGf,MAAM,CAACK,eAAe,CAAC;IAC/C,MAAMW,iBAAiB,GAAGhB,MAAM,CAACI,iBAAiB,CAAC;IACnD,MAAMa,iBAAiB,GAAGjB,MAAM,CAACM,iBAAiB,CAAC;IACnD,MAAMY,MAAM,GAAGlB,MAAM,CAACC,MAAM,CAAC;IAC7B,MAAMkB,cAAc,GAAGnB,MAAM,CAACO,cAAc,CAAC;IAC7C,MAAMa,YAAY,GAAGH,iBAAiB,CACnCI,kBAAkB,CAACR,KAAK,CAACS,IAAI,CAAC;IACjC,MAAMC,SAAS,GAAG,CAACH,YAAY,CAACG,SAAS;IACzC,MAAMC,OAAO,GAAGJ,YAAY,CAACK,UAAU;IACvC,IAAIC,aAAa,GAAGZ,UAAU,CAACa,mBAAmB;IAClD,IAAI,CAACD,aAAa,EAAEE,MAAM,EAAE;MAC1BF,aAAa,SAASlB,cAAc,CAACM,UAAU,CAACe,sCAAsC,EAAE,CAAC;IAC3F;IAEA,IAAIC,GAAG,GAAgB,IAAI;IAE3B,IAAIN,OAAO,EAAE;MACXM,GAAG,SAASC,MAAM,CAACf,iBAAiB,EAAED,eAAe,EAAEQ,SAAS,CAAC;IACnE;IAEA,MAAMS,SAAS,SAASC,WAAW,CAACd,cAAc,EAAED,MAAM,EAAEQ,aAAa,EAAEH,SAAS,EAAEC,OAAO,EAAEM,GAAG,CAAC;IACnG,OAAOE,SAAS;EAClB,CAAC;EAAA,gBAxBYtB,YAAYA,CAAAwB,EAAA;IAAA,OAAAvB,IAAA,CAAAwB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAwBxB;AAAC,SAEaL,MAAMA,CAAAM,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,OAAA,CAAAL,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAI,QAAA;EAAAA,OAAA,GAAA5B,iBAAA,CAArB,WAAsBI,iBAAoC,EAAED,eAAgC,EAAEQ,SAAiB;IAC7G,IAAIkB,qBAAqB,GAAGlB,SAAS,GAAGP,iBAAiB,EAAEyB,qBAAqB,GAAG,IAAI;IACvF,IAAIX,GAAG,GAAgB,IAAI;IAE3B,IAAI,CAACW,qBAAqB,IAAIA,qBAAqB,CAACC,EAAE,KAAKnB,SAAS,EAAE;MACpEkB,qBAAqB,SAASjC,cAAc,CAACO,eAAe,CAAC4B,iBAAiB,CAACpB,SAAS,CAAC,CAAC;MAC1FP,iBAAiB,CAACyB,qBAAqB,GAAGA,qBAAqB;IACjE;IAEA,IAAIA,qBAAqB,EAAEG,KAAK,EAAE;MAChCd,GAAG,GAAGW,qBAAqB,CAACI,WAAW;IACzC;IAEA,OAAOf,GAAG;EACZ,CAAC;EAAA,OAAAU,OAAA,CAAAL,KAAA,OAAAC,SAAA;AAAA;AAAA,SAEcH,WAAWA,CAAAa,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,IAAA;EAAA,OAAAC,YAAA,CAAAjB,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAgB,aAAA;EAAAA,YAAA,GAAAxC,iBAAA,CAA1B,WAA2BO,cAA8B,EAAED,MAAc,EAAEQ,aAA6B,EAAEH,SAAkB,EAAEC,OAAgB,EAAEM,GAAiB;IAC/J,IAAIuB,qBAAqB,GAAG,CAAC,GAAG3B,aAAa,CAAC;IAC9C,IAAIH,SAAS,EAAE;MACb8B,qBAAqB,GAAGA,qBAAqB,CAC1CC,MAAM,CAACC,YAAY,IAAIA,YAAY,CAACC,gBAAgB,KAAKjC,SAAS,CAAC;IACxE;IAEA,IAAIC,OAAO,EAAE;MACX,MAAMiC,kBAAkB,GAAG3B,GAAG,EAAE4B,KAAK,EAAEC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,SAAS,CAAC;MACtE,MAAMC,KAAK,GAAGL,kBAAkB,EAAEK,KAAK,IAAI,EAAE;MAC7C,MAAMC,UAAU,GAAGD,KAAK,CACrBR,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACC,MAAM,CACxBC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAK5C,OAAO,IACzC2C,KAAK,CAACE,UAAU,KAAKnE,SAAS,CAACoE,cAAc,CAACC,cAAc,CAACV,IAAI,CAAC,CAAC;MACzER,qBAAqB,GAAGA,qBAAqB,CAC1CC,MAAM,CAACC,YAAY,IAAIQ,UAAU,CAACG,IAAI,CAACF,IAAI,IAAIA,IAAI,CAACQ,IAAI,KAAKjB,YAAY,CAACkB,QAAQ,CAAC,CAAC;IACzF;IAEA,IAAIzC,SAAS,GAAGqB,qBAAqB,CAACzB,MAAM,GAAG,CAAC;IAChD,IAAII,SAAS,EAAE;MACb;MACA,IAAG,CAACb,cAAc,CAACuD,8BAA8B,EAAE,EAAEhC,EAAE,EAAE;QACvD,MAAMlC,cAAc,CAACW,cAAc,CAACwD,UAAU,EAAE,CAAC;MACnD;MAEA3C,SAAS,GAAGb,cAAc,CAACyD,iBAAiB,CAACnE,aAAa,CAACoE,aAAa,CAAC;IAC3E;IAEA,IAAI,CAAC7C,SAAS,EAAE;MACdd,MAAM,CAAC4D,aAAa,CAAC,eAAe,CAAC;IACvC;IAEA,OAAO9C,SAAS;EAClB,CAAC;EAAA,OAAAoB,YAAA,CAAAjB,KAAA,OAAAC,SAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}