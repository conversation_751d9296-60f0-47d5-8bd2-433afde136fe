{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst textEncoder = {\n  convert(input) {\n    return new TextEncoder().encode(input);\n  }\n};\nexport { textEncoder };", "map": {"version": 3, "names": ["textEncoder", "convert", "input", "TextEncoder", "encode"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/textEncoder/index.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst textEncoder = {\n    convert(input) {\n        return new TextEncoder().encode(input);\n    },\n};\n\nexport { textEncoder };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,WAAW,GAAG;EAChBC,OAAOA,CAACC,KAAK,EAAE;IACX,OAAO,IAAIC,WAAW,CAAC,CAAC,CAACC,MAAM,CAACF,KAAK,CAAC;EAC1C;AACJ,CAAC;AAED,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}