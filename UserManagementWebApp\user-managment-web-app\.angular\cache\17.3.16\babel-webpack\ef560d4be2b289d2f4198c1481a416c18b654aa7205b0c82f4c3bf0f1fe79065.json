{"ast": null, "code": "import { isBrowser } from '../isBrowser.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst stateChangeListeners = new Set();\nclass SessionListener {\n  constructor() {\n    this.listenerActive = false;\n    this.handleVisibilityChange = this.handleVisibilityChange.bind(this);\n    // Setup state listeners\n    if (isBrowser()) {\n      document.addEventListener('visibilitychange', this.handleVisibilityChange, false);\n      this.listenerActive = true;\n    }\n  }\n  addStateChangeListener(listener, notifyOnAdd = false) {\n    // No-op if document listener is not active\n    if (!this.listenerActive) {\n      return;\n    }\n    stateChangeListeners.add(listener);\n    // Notify new handlers of the current status on add\n    if (notifyOnAdd) {\n      listener(this.getSessionState());\n    }\n  }\n  removeStateChangeListener(handler) {\n    // No-op if document listener is not active\n    if (!this.listenerActive) {\n      return;\n    }\n    stateChangeListeners.delete(handler);\n  }\n  handleVisibilityChange() {\n    this.notifyHandlers();\n  }\n  notifyHandlers() {\n    const sessionState = this.getSessionState();\n    stateChangeListeners.forEach(listener => {\n      listener(sessionState);\n    });\n  }\n  getSessionState() {\n    if (isBrowser() && document.visibilityState !== 'hidden') {\n      return 'started';\n    }\n    // If, for any reason, document is undefined the session will never start\n    return 'ended';\n  }\n}\nexport { SessionListener };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "stateChangeListeners", "Set", "SessionListener", "constructor", "listenerActive", "handleVisibilityChange", "bind", "document", "addEventListener", "addStateChangeListener", "listener", "notifyOnAdd", "add", "getSessionState", "removeStateChangeListener", "handler", "delete", "notifyHandlers", "sessionState", "for<PERSON>ach", "visibilityState"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/sessionListener/SessionListener.mjs"], "sourcesContent": ["import { isBrowser } from '../isBrowser.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst stateChangeListeners = new Set();\nclass SessionListener {\n    constructor() {\n        this.listenerActive = false;\n        this.handleVisibilityChange = this.handleVisibilityChange.bind(this);\n        // Setup state listeners\n        if (isBrowser()) {\n            document.addEventListener('visibilitychange', this.handleVisibilityChange, false);\n            this.listenerActive = true;\n        }\n    }\n    addStateChangeListener(listener, notifyOnAdd = false) {\n        // No-op if document listener is not active\n        if (!this.listenerActive) {\n            return;\n        }\n        stateChangeListeners.add(listener);\n        // Notify new handlers of the current status on add\n        if (notifyOnAdd) {\n            listener(this.getSessionState());\n        }\n    }\n    removeStateChangeListener(handler) {\n        // No-op if document listener is not active\n        if (!this.listenerActive) {\n            return;\n        }\n        stateChangeListeners.delete(handler);\n    }\n    handleVisibilityChange() {\n        this.notifyHandlers();\n    }\n    notifyHandlers() {\n        const sessionState = this.getSessionState();\n        stateChangeListeners.forEach(listener => {\n            listener(sessionState);\n        });\n    }\n    getSessionState() {\n        if (isBrowser() && document.visibilityState !== 'hidden') {\n            return 'started';\n        }\n        // If, for any reason, document is undefined the session will never start\n        return 'ended';\n    }\n}\n\nexport { SessionListener };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,kBAAkB;;AAE5C;AACA;AACA,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC;AACtC,MAAMC,eAAe,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACC,IAAI,CAAC,IAAI,CAAC;IACpE;IACA,IAAIP,SAAS,CAAC,CAAC,EAAE;MACbQ,QAAQ,CAACC,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAACH,sBAAsB,EAAE,KAAK,CAAC;MACjF,IAAI,CAACD,cAAc,GAAG,IAAI;IAC9B;EACJ;EACAK,sBAAsBA,CAACC,QAAQ,EAAEC,WAAW,GAAG,KAAK,EAAE;IAClD;IACA,IAAI,CAAC,IAAI,CAACP,cAAc,EAAE;MACtB;IACJ;IACAJ,oBAAoB,CAACY,GAAG,CAACF,QAAQ,CAAC;IAClC;IACA,IAAIC,WAAW,EAAE;MACbD,QAAQ,CAAC,IAAI,CAACG,eAAe,CAAC,CAAC,CAAC;IACpC;EACJ;EACAC,yBAAyBA,CAACC,OAAO,EAAE;IAC/B;IACA,IAAI,CAAC,IAAI,CAACX,cAAc,EAAE;MACtB;IACJ;IACAJ,oBAAoB,CAACgB,MAAM,CAACD,OAAO,CAAC;EACxC;EACAV,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAACY,cAAc,CAAC,CAAC;EACzB;EACAA,cAAcA,CAAA,EAAG;IACb,MAAMC,YAAY,GAAG,IAAI,CAACL,eAAe,CAAC,CAAC;IAC3Cb,oBAAoB,CAACmB,OAAO,CAACT,QAAQ,IAAI;MACrCA,QAAQ,CAACQ,YAAY,CAAC;IAC1B,CAAC,CAAC;EACN;EACAL,eAAeA,CAAA,EAAG;IACd,IAAId,SAAS,CAAC,CAAC,IAAIQ,QAAQ,CAACa,eAAe,KAAK,QAAQ,EAAE;MACtD,OAAO,SAAS;IACpB;IACA;IACA,OAAO,OAAO;EAClB;AACJ;AAEA,SAASlB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}