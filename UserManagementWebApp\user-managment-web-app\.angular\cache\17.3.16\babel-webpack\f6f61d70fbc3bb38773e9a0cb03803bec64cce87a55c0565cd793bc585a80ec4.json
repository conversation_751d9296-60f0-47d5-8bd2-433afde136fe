{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { createMachine, sendUpdate } from 'xstate';\nimport { signInWithRedirect, confirmSignIn, resetPassword, fetchUserAttributes } from 'aws-amplify/auth';\nimport { runValidators } from '../../../validators/index.mjs';\nimport ACTIONS from '../actions.mjs';\nimport { defaultServices } from '../defaultServices.mjs';\nimport GUARDS from '../guards.mjs';\nimport { getFederatedSignInState, getConfirmSignInFormValuesKey } from './utils.mjs';\nconst handleSignInResponse = {\n  onDone: [{\n    cond: 'hasCompletedSignIn',\n    actions: 'setNextSignInStep',\n    target: '#signInActor.fetchUserAttributes'\n  }, {\n    cond: 'shouldConfirmSignInWithNewPassword',\n    actions: ['setMissingAttributes', 'setNextSignInStep'],\n    target: '#signInActor.forceChangePassword'\n  }, {\n    cond: 'shouldResetPasswordFromSignIn',\n    actions: 'setNextSignInStep',\n    target: '#signInActor.resetPassword'\n  }, {\n    cond: 'shouldConfirmSignUpFromSignIn',\n    actions: 'setNextSignInStep',\n    target: '#signInActor.resendSignUpCode'\n  }, {\n    actions: ['setChallengeName', 'setMissingAttributes', 'setNextSignInStep', 'setTotpSecretCode', 'setAllowedMfaTypes'],\n    target: '#signInActor.init'\n  }],\n  onError: {\n    actions: 'setRemoteError',\n    target: 'edit'\n  }\n};\nconst handleFetchUserAttributesResponse = {\n  onDone: [{\n    cond: 'shouldVerifyAttribute',\n    actions: ['setShouldVerifyUserAttributeStep', 'setUnverifiedUserAttributes'],\n    target: '#signInActor.resolved'\n  }, {\n    actions: 'setConfirmAttributeCompleteStep',\n    target: '#signInActor.resolved'\n  }],\n  onError: {\n    actions: 'setConfirmAttributeCompleteStep',\n    target: '#signInActor.resolved'\n  }\n};\nconst getDefaultConfirmSignInState = exit => ({\n  initial: 'edit',\n  exit,\n  states: {\n    edit: {\n      entry: 'sendUpdate',\n      on: {\n        SUBMIT: {\n          actions: 'handleSubmit',\n          target: 'submit'\n        },\n        SIGN_IN: '#signInActor.signIn',\n        CHANGE: {\n          actions: 'handleInput'\n        }\n      }\n    },\n    submit: {\n      tags: 'pending',\n      entry: ['sendUpdate', 'clearError'],\n      invoke: {\n        src: 'confirmSignIn',\n        ...handleSignInResponse\n      }\n    }\n  }\n});\nfunction signInActor({\n  services\n}) {\n  return createMachine({\n    id: 'signInActor',\n    initial: 'init',\n    predictableActionArguments: true,\n    states: {\n      init: {\n        always: [{\n          cond: 'shouldConfirmSignIn',\n          target: 'confirmSignIn'\n        }, {\n          cond: 'shouldSetupTotp',\n          target: 'setupTotp'\n        }, {\n          cond: 'shouldSetupEmail',\n          target: 'setupEmail'\n        }, {\n          cond: 'shouldSelectMfaType',\n          target: 'selectMfaType'\n        }, {\n          cond: ({\n            step\n          }) => step === 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED',\n          actions: 'setActorDoneData',\n          target: 'forceChangePassword'\n        }, {\n          target: 'signIn'\n        }]\n      },\n      federatedSignIn: getFederatedSignInState('signIn'),\n      fetchUserAttributes: {\n        invoke: {\n          src: 'fetchUserAttributes',\n          ...handleFetchUserAttributesResponse\n        }\n      },\n      resendSignUpCode: {\n        invoke: {\n          src: 'handleResendSignUpCode',\n          onDone: {\n            actions: 'setCodeDeliveryDetails',\n            target: '#signInActor.resolved'\n          },\n          onError: {\n            actions: 'setRemoteError',\n            target: '#signInActor.signIn'\n          }\n        }\n      },\n      resetPassword: {\n        invoke: {\n          src: 'resetPassword',\n          onDone: [{\n            actions: 'setCodeDeliveryDetails',\n            target: '#signInActor.resolved'\n          }],\n          onError: {\n            actions: ['setRemoteError', 'sendUpdate']\n          }\n        }\n      },\n      signIn: {\n        initial: 'edit',\n        exit: 'clearTouched',\n        states: {\n          edit: {\n            entry: 'sendUpdate',\n            on: {\n              CHANGE: {\n                actions: 'handleInput'\n              },\n              FEDERATED_SIGN_IN: {\n                target: '#signInActor.federatedSignIn'\n              },\n              SUBMIT: {\n                actions: 'handleSubmit',\n                target: 'submit'\n              }\n            }\n          },\n          submit: {\n            tags: 'pending',\n            entry: ['clearError', 'sendUpdate', 'setUsernameSignIn'],\n            exit: 'clearFormValues',\n            invoke: {\n              src: 'handleSignIn',\n              ...handleSignInResponse\n            }\n          }\n        }\n      },\n      confirmSignIn: getDefaultConfirmSignInState(['clearChallengeName', 'clearFormValues', 'clearError', 'clearTouched']),\n      forceChangePassword: {\n        entry: 'sendUpdate',\n        type: 'parallel',\n        exit: ['clearFormValues', 'clearError', 'clearTouched'],\n        states: {\n          validation: {\n            initial: 'pending',\n            states: {\n              pending: {\n                invoke: {\n                  src: 'validateFields',\n                  onDone: {\n                    target: 'valid',\n                    actions: 'clearValidationError'\n                  },\n                  onError: {\n                    target: 'invalid',\n                    actions: 'setFieldErrors'\n                  }\n                }\n              },\n              valid: {\n                entry: 'sendUpdate'\n              },\n              invalid: {\n                entry: 'sendUpdate'\n              }\n            },\n            on: {\n              SIGN_IN: {\n                actions: 'setSignInStep',\n                target: '#signInActor.resolved'\n              },\n              CHANGE: {\n                actions: 'handleInput',\n                target: '.pending'\n              },\n              BLUR: {\n                actions: 'handleBlur',\n                target: '.pending'\n              }\n            }\n          },\n          submit: {\n            initial: 'edit',\n            entry: 'clearError',\n            states: {\n              edit: {\n                entry: 'sendUpdate',\n                on: {\n                  SUBMIT: {\n                    actions: 'handleSubmit',\n                    target: 'validate'\n                  }\n                }\n              },\n              validate: {\n                entry: 'sendUpdate',\n                invoke: {\n                  src: 'validateFields',\n                  onDone: {\n                    actions: 'clearValidationError',\n                    target: 'pending'\n                  },\n                  onError: {\n                    actions: 'setFieldErrors',\n                    target: 'edit'\n                  }\n                }\n              },\n              pending: {\n                tags: 'pending',\n                entry: ['sendUpdate', 'clearError'],\n                invoke: {\n                  src: 'handleForceChangePassword',\n                  ...handleSignInResponse\n                }\n              }\n            }\n          }\n        }\n      },\n      setupTotp: getDefaultConfirmSignInState(['clearFormValues', 'clearError', 'clearTouched']),\n      setupEmail: getDefaultConfirmSignInState(['clearFormValues', 'clearError', 'clearTouched']),\n      selectMfaType: getDefaultConfirmSignInState(['clearFormValues', 'clearError', 'clearTouched']),\n      resolved: {\n        type: 'final',\n        data: context => ({\n          codeDeliveryDetails: context.codeDeliveryDetails,\n          remoteError: context.remoteError,\n          step: context.step,\n          unverifiedUserAttributes: context.unverifiedUserAttributes,\n          username: context.username\n        })\n      }\n    }\n  }, {\n    // sendUpdate is a HOC\n    actions: {\n      ...ACTIONS,\n      sendUpdate: sendUpdate()\n    },\n    guards: GUARDS,\n    services: {\n      fetchUserAttributes() {\n        return _asyncToGenerator(function* () {\n          return fetchUserAttributes();\n        })();\n      },\n      resetPassword({\n        username\n      }) {\n        return resetPassword({\n          username\n        });\n      },\n      handleResendSignUpCode({\n        username\n      }) {\n        return services.handleResendSignUpCode({\n          username\n        });\n      },\n      handleSignIn({\n        formValues,\n        username\n      }) {\n        const {\n          password\n        } = formValues;\n        return services.handleSignIn({\n          username,\n          password\n        });\n      },\n      confirmSignIn({\n        formValues,\n        step\n      }) {\n        const formValuesKey = getConfirmSignInFormValuesKey(step);\n        const {\n          [formValuesKey]: challengeResponse\n        } = formValues;\n        return services.handleConfirmSignIn({\n          challengeResponse\n        });\n      },\n      handleForceChangePassword({\n        formValues\n      }) {\n        return _asyncToGenerator(function* () {\n          let {\n            password: challengeResponse,\n            phone_number,\n            country_code,\n            // destructure and toss UI confirm_password field\n            // to prevent error from sending to confirmSignIn\n            confirm_password,\n            ...userAttributes\n          } = formValues;\n          let phoneNumberWithCountryCode;\n          if (phone_number) {\n            phoneNumberWithCountryCode = `${country_code}${phone_number}`.replace(/[^A-Z0-9+]/gi, '');\n            userAttributes = {\n              ...userAttributes,\n              phone_number: phoneNumberWithCountryCode\n            };\n          }\n          const input = {\n            challengeResponse,\n            options: {\n              userAttributes\n            }\n          };\n          return confirmSignIn(input);\n        })();\n      },\n      signInWithRedirect(_, {\n        data\n      }) {\n        return signInWithRedirect(data);\n      },\n      validateFields(context) {\n        return _asyncToGenerator(function* () {\n          return runValidators(context.formValues, context.touched, context.passwordSettings, [defaultServices.validateFormPassword, defaultServices.validateConfirmPassword]);\n        })();\n      }\n    }\n  });\n}\nexport { signInActor };", "map": {"version": 3, "names": ["createMachine", "sendUpdate", "signInWithRedirect", "confirmSignIn", "resetPassword", "fetchUserAttributes", "runValidators", "ACTIONS", "defaultServices", "GUARDS", "getFederatedSignInState", "getConfirmSignInFormValuesKey", "handleSignInResponse", "onDone", "cond", "actions", "target", "onError", "handleFetchUserAttributesResponse", "getDefaultConfirmSignInState", "exit", "initial", "states", "edit", "entry", "on", "SUBMIT", "SIGN_IN", "CHANGE", "submit", "tags", "invoke", "src", "signInActor", "services", "id", "predictableActionArguments", "init", "always", "step", "federatedSignIn", "resendSignUpCode", "signIn", "FEDERATED_SIGN_IN", "forceChangePassword", "type", "validation", "pending", "valid", "invalid", "BLUR", "validate", "setupTotp", "setupEmail", "selectMfaType", "resolved", "data", "context", "codeDeliveryDetails", "remoteError", "unverifiedUserAttributes", "username", "guards", "_asyncToGenerator", "handleResendSignUpCode", "handleSignIn", "formValues", "password", "formValuesKey", "challengeResponse", "handleConfirmSignIn", "handleForceChangePassword", "phone_number", "country_code", "confirm_password", "userAttributes", "phoneNumberWithCountryCode", "replace", "input", "options", "_", "validateFields", "touched", "passwordSettings", "validateFormPassword", "validateConfirmPassword"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/machines/authenticator/actors/signIn.mjs"], "sourcesContent": ["import { createMachine, sendUpdate } from 'xstate';\nimport { signInWithRedirect, confirmSignIn, resetPassword, fetchUserAttributes } from 'aws-amplify/auth';\nimport { runValidators } from '../../../validators/index.mjs';\nimport ACTIONS from '../actions.mjs';\nimport { defaultServices } from '../defaultServices.mjs';\nimport GUARDS from '../guards.mjs';\nimport { getFederatedSignInState, getConfirmSignInFormValuesKey } from './utils.mjs';\n\nconst handleSignInResponse = {\n    onDone: [\n        {\n            cond: 'hasCompletedSignIn',\n            actions: 'setNextSignInStep',\n            target: '#signInActor.fetchUserAttributes',\n        },\n        {\n            cond: 'shouldConfirmSignInWithNewPassword',\n            actions: ['setMissingAttributes', 'setNextSignInStep'],\n            target: '#signInActor.forceChangePassword',\n        },\n        {\n            cond: 'shouldResetPasswordFromSignIn',\n            actions: 'setNextSignInStep',\n            target: '#signInActor.resetPassword',\n        },\n        {\n            cond: 'shouldConfirmSignUpFromSignIn',\n            actions: 'setNextSignInStep',\n            target: '#signInActor.resendSignUpCode',\n        },\n        {\n            actions: [\n                'setChallengeName',\n                'setMissingAttributes',\n                'setNextSignInStep',\n                'setTotpSecretCode',\n                'setAllowedMfaTypes',\n            ],\n            target: '#signInActor.init',\n        },\n    ],\n    onError: { actions: 'setRemoteError', target: 'edit' },\n};\nconst handleFetchUserAttributesResponse = {\n    onDone: [\n        {\n            cond: 'shouldVerifyAttribute',\n            actions: [\n                'setShouldVerifyUserAttributeStep',\n                'setUnverifiedUserAttributes',\n            ],\n            target: '#signInActor.resolved',\n        },\n        {\n            actions: 'setConfirmAttributeCompleteStep',\n            target: '#signInActor.resolved',\n        },\n    ],\n    onError: {\n        actions: 'setConfirmAttributeCompleteStep',\n        target: '#signInActor.resolved',\n    },\n};\nconst getDefaultConfirmSignInState = (exit) => ({\n    initial: 'edit',\n    exit,\n    states: {\n        edit: {\n            entry: 'sendUpdate',\n            on: {\n                SUBMIT: { actions: 'handleSubmit', target: 'submit' },\n                SIGN_IN: '#signInActor.signIn',\n                CHANGE: { actions: 'handleInput' },\n            },\n        },\n        submit: {\n            tags: 'pending',\n            entry: ['sendUpdate', 'clearError'],\n            invoke: { src: 'confirmSignIn', ...handleSignInResponse },\n        },\n    },\n});\nfunction signInActor({ services }) {\n    return createMachine({\n        id: 'signInActor',\n        initial: 'init',\n        predictableActionArguments: true,\n        states: {\n            init: {\n                always: [\n                    {\n                        cond: 'shouldConfirmSignIn',\n                        target: 'confirmSignIn',\n                    },\n                    {\n                        cond: 'shouldSetupTotp',\n                        target: 'setupTotp',\n                    },\n                    {\n                        cond: 'shouldSetupEmail',\n                        target: 'setupEmail',\n                    },\n                    {\n                        cond: 'shouldSelectMfaType',\n                        target: 'selectMfaType',\n                    },\n                    {\n                        cond: ({ step }) => step === 'CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED',\n                        actions: 'setActorDoneData',\n                        target: 'forceChangePassword',\n                    },\n                    { target: 'signIn' },\n                ],\n            },\n            federatedSignIn: getFederatedSignInState('signIn'),\n            fetchUserAttributes: {\n                invoke: {\n                    src: 'fetchUserAttributes',\n                    ...handleFetchUserAttributesResponse,\n                },\n            },\n            resendSignUpCode: {\n                invoke: {\n                    src: 'handleResendSignUpCode',\n                    onDone: {\n                        actions: 'setCodeDeliveryDetails',\n                        target: '#signInActor.resolved',\n                    },\n                    onError: {\n                        actions: 'setRemoteError',\n                        target: '#signInActor.signIn',\n                    },\n                },\n            },\n            resetPassword: {\n                invoke: {\n                    src: 'resetPassword',\n                    onDone: [\n                        {\n                            actions: 'setCodeDeliveryDetails',\n                            target: '#signInActor.resolved',\n                        },\n                    ],\n                    onError: { actions: ['setRemoteError', 'sendUpdate'] },\n                },\n            },\n            signIn: {\n                initial: 'edit',\n                exit: 'clearTouched',\n                states: {\n                    edit: {\n                        entry: 'sendUpdate',\n                        on: {\n                            CHANGE: { actions: 'handleInput' },\n                            FEDERATED_SIGN_IN: { target: '#signInActor.federatedSignIn' },\n                            SUBMIT: { actions: 'handleSubmit', target: 'submit' },\n                        },\n                    },\n                    submit: {\n                        tags: 'pending',\n                        entry: ['clearError', 'sendUpdate', 'setUsernameSignIn'],\n                        exit: 'clearFormValues',\n                        invoke: { src: 'handleSignIn', ...handleSignInResponse },\n                    },\n                },\n            },\n            confirmSignIn: getDefaultConfirmSignInState([\n                'clearChallengeName',\n                'clearFormValues',\n                'clearError',\n                'clearTouched',\n            ]),\n            forceChangePassword: {\n                entry: 'sendUpdate',\n                type: 'parallel',\n                exit: ['clearFormValues', 'clearError', 'clearTouched'],\n                states: {\n                    validation: {\n                        initial: 'pending',\n                        states: {\n                            pending: {\n                                invoke: {\n                                    src: 'validateFields',\n                                    onDone: {\n                                        target: 'valid',\n                                        actions: 'clearValidationError',\n                                    },\n                                    onError: {\n                                        target: 'invalid',\n                                        actions: 'setFieldErrors',\n                                    },\n                                },\n                            },\n                            valid: { entry: 'sendUpdate' },\n                            invalid: { entry: 'sendUpdate' },\n                        },\n                        on: {\n                            SIGN_IN: {\n                                actions: 'setSignInStep',\n                                target: '#signInActor.resolved',\n                            },\n                            CHANGE: {\n                                actions: 'handleInput',\n                                target: '.pending',\n                            },\n                            BLUR: {\n                                actions: 'handleBlur',\n                                target: '.pending',\n                            },\n                        },\n                    },\n                    submit: {\n                        initial: 'edit',\n                        entry: 'clearError',\n                        states: {\n                            edit: {\n                                entry: 'sendUpdate',\n                                on: {\n                                    SUBMIT: { actions: 'handleSubmit', target: 'validate' },\n                                },\n                            },\n                            validate: {\n                                entry: 'sendUpdate',\n                                invoke: {\n                                    src: 'validateFields',\n                                    onDone: {\n                                        actions: 'clearValidationError',\n                                        target: 'pending',\n                                    },\n                                    onError: { actions: 'setFieldErrors', target: 'edit' },\n                                },\n                            },\n                            pending: {\n                                tags: 'pending',\n                                entry: ['sendUpdate', 'clearError'],\n                                invoke: {\n                                    src: 'handleForceChangePassword',\n                                    ...handleSignInResponse,\n                                },\n                            },\n                        },\n                    },\n                },\n            },\n            setupTotp: getDefaultConfirmSignInState([\n                'clearFormValues',\n                'clearError',\n                'clearTouched',\n            ]),\n            setupEmail: getDefaultConfirmSignInState([\n                'clearFormValues',\n                'clearError',\n                'clearTouched',\n            ]),\n            selectMfaType: getDefaultConfirmSignInState([\n                'clearFormValues',\n                'clearError',\n                'clearTouched',\n            ]),\n            resolved: {\n                type: 'final',\n                data: (context) => ({\n                    codeDeliveryDetails: context.codeDeliveryDetails,\n                    remoteError: context.remoteError,\n                    step: context.step,\n                    unverifiedUserAttributes: context.unverifiedUserAttributes,\n                    username: context.username,\n                }),\n            },\n        },\n    }, {\n        // sendUpdate is a HOC\n        actions: { ...ACTIONS, sendUpdate: sendUpdate() },\n        guards: GUARDS,\n        services: {\n            async fetchUserAttributes() {\n                return fetchUserAttributes();\n            },\n            resetPassword({ username }) {\n                return resetPassword({ username });\n            },\n            handleResendSignUpCode({ username }) {\n                return services.handleResendSignUpCode({ username });\n            },\n            handleSignIn({ formValues, username }) {\n                const { password } = formValues;\n                return services.handleSignIn({ username, password });\n            },\n            confirmSignIn({ formValues, step }) {\n                const formValuesKey = getConfirmSignInFormValuesKey(step);\n                const { [formValuesKey]: challengeResponse } = formValues;\n                return services.handleConfirmSignIn({ challengeResponse });\n            },\n            async handleForceChangePassword({ formValues }) {\n                let { password: challengeResponse, phone_number, country_code, \n                // destructure and toss UI confirm_password field\n                // to prevent error from sending to confirmSignIn\n                confirm_password, ...userAttributes } = formValues;\n                let phoneNumberWithCountryCode;\n                if (phone_number) {\n                    phoneNumberWithCountryCode =\n                        `${country_code}${phone_number}`.replace(/[^A-Z0-9+]/gi, '');\n                    userAttributes = {\n                        ...userAttributes,\n                        phone_number: phoneNumberWithCountryCode,\n                    };\n                }\n                const input = {\n                    challengeResponse,\n                    options: { userAttributes },\n                };\n                return confirmSignIn(input);\n            },\n            signInWithRedirect(_, { data }) {\n                return signInWithRedirect(data);\n            },\n            async validateFields(context) {\n                return runValidators(context.formValues, context.touched, context.passwordSettings, [\n                    defaultServices.validateFormPassword,\n                    defaultServices.validateConfirmPassword,\n                ]);\n            },\n        },\n    });\n}\n\nexport { signInActor };\n"], "mappings": ";AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,QAAQ;AAClD,SAASC,kBAAkB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,mBAAmB,QAAQ,kBAAkB;AACxG,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,OAAOC,OAAO,MAAM,gBAAgB;AACpC,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAOC,MAAM,MAAM,eAAe;AAClC,SAASC,uBAAuB,EAAEC,6BAA6B,QAAQ,aAAa;AAEpF,MAAMC,oBAAoB,GAAG;EACzBC,MAAM,EAAE,CACJ;IACIC,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,EAAE,mBAAmB;IAC5BC,MAAM,EAAE;EACZ,CAAC,EACD;IACIF,IAAI,EAAE,oCAAoC;IAC1CC,OAAO,EAAE,CAAC,sBAAsB,EAAE,mBAAmB,CAAC;IACtDC,MAAM,EAAE;EACZ,CAAC,EACD;IACIF,IAAI,EAAE,+BAA+B;IACrCC,OAAO,EAAE,mBAAmB;IAC5BC,MAAM,EAAE;EACZ,CAAC,EACD;IACIF,IAAI,EAAE,+BAA+B;IACrCC,OAAO,EAAE,mBAAmB;IAC5BC,MAAM,EAAE;EACZ,CAAC,EACD;IACID,OAAO,EAAE,CACL,kBAAkB,EAClB,sBAAsB,EACtB,mBAAmB,EACnB,mBAAmB,EACnB,oBAAoB,CACvB;IACDC,MAAM,EAAE;EACZ,CAAC,CACJ;EACDC,OAAO,EAAE;IAAEF,OAAO,EAAE,gBAAgB;IAAEC,MAAM,EAAE;EAAO;AACzD,CAAC;AACD,MAAME,iCAAiC,GAAG;EACtCL,MAAM,EAAE,CACJ;IACIC,IAAI,EAAE,uBAAuB;IAC7BC,OAAO,EAAE,CACL,kCAAkC,EAClC,6BAA6B,CAChC;IACDC,MAAM,EAAE;EACZ,CAAC,EACD;IACID,OAAO,EAAE,iCAAiC;IAC1CC,MAAM,EAAE;EACZ,CAAC,CACJ;EACDC,OAAO,EAAE;IACLF,OAAO,EAAE,iCAAiC;IAC1CC,MAAM,EAAE;EACZ;AACJ,CAAC;AACD,MAAMG,4BAA4B,GAAIC,IAAI,KAAM;EAC5CC,OAAO,EAAE,MAAM;EACfD,IAAI;EACJE,MAAM,EAAE;IACJC,IAAI,EAAE;MACFC,KAAK,EAAE,YAAY;MACnBC,EAAE,EAAE;QACAC,MAAM,EAAE;UAAEX,OAAO,EAAE,cAAc;UAAEC,MAAM,EAAE;QAAS,CAAC;QACrDW,OAAO,EAAE,qBAAqB;QAC9BC,MAAM,EAAE;UAAEb,OAAO,EAAE;QAAc;MACrC;IACJ,CAAC;IACDc,MAAM,EAAE;MACJC,IAAI,EAAE,SAAS;MACfN,KAAK,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;MACnCO,MAAM,EAAE;QAAEC,GAAG,EAAE,eAAe;QAAE,GAAGpB;MAAqB;IAC5D;EACJ;AACJ,CAAC,CAAC;AACF,SAASqB,WAAWA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAC/B,OAAOlC,aAAa,CAAC;IACjBmC,EAAE,EAAE,aAAa;IACjBd,OAAO,EAAE,MAAM;IACfe,0BAA0B,EAAE,IAAI;IAChCd,MAAM,EAAE;MACJe,IAAI,EAAE;QACFC,MAAM,EAAE,CACJ;UACIxB,IAAI,EAAE,qBAAqB;UAC3BE,MAAM,EAAE;QACZ,CAAC,EACD;UACIF,IAAI,EAAE,iBAAiB;UACvBE,MAAM,EAAE;QACZ,CAAC,EACD;UACIF,IAAI,EAAE,kBAAkB;UACxBE,MAAM,EAAE;QACZ,CAAC,EACD;UACIF,IAAI,EAAE,qBAAqB;UAC3BE,MAAM,EAAE;QACZ,CAAC,EACD;UACIF,IAAI,EAAEA,CAAC;YAAEyB;UAAK,CAAC,KAAKA,IAAI,KAAK,4CAA4C;UACzExB,OAAO,EAAE,kBAAkB;UAC3BC,MAAM,EAAE;QACZ,CAAC,EACD;UAAEA,MAAM,EAAE;QAAS,CAAC;MAE5B,CAAC;MACDwB,eAAe,EAAE9B,uBAAuB,CAAC,QAAQ,CAAC;MAClDL,mBAAmB,EAAE;QACjB0B,MAAM,EAAE;UACJC,GAAG,EAAE,qBAAqB;UAC1B,GAAGd;QACP;MACJ,CAAC;MACDuB,gBAAgB,EAAE;QACdV,MAAM,EAAE;UACJC,GAAG,EAAE,wBAAwB;UAC7BnB,MAAM,EAAE;YACJE,OAAO,EAAE,wBAAwB;YACjCC,MAAM,EAAE;UACZ,CAAC;UACDC,OAAO,EAAE;YACLF,OAAO,EAAE,gBAAgB;YACzBC,MAAM,EAAE;UACZ;QACJ;MACJ,CAAC;MACDZ,aAAa,EAAE;QACX2B,MAAM,EAAE;UACJC,GAAG,EAAE,eAAe;UACpBnB,MAAM,EAAE,CACJ;YACIE,OAAO,EAAE,wBAAwB;YACjCC,MAAM,EAAE;UACZ,CAAC,CACJ;UACDC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC,gBAAgB,EAAE,YAAY;UAAE;QACzD;MACJ,CAAC;MACD2B,MAAM,EAAE;QACJrB,OAAO,EAAE,MAAM;QACfD,IAAI,EAAE,cAAc;QACpBE,MAAM,EAAE;UACJC,IAAI,EAAE;YACFC,KAAK,EAAE,YAAY;YACnBC,EAAE,EAAE;cACAG,MAAM,EAAE;gBAAEb,OAAO,EAAE;cAAc,CAAC;cAClC4B,iBAAiB,EAAE;gBAAE3B,MAAM,EAAE;cAA+B,CAAC;cAC7DU,MAAM,EAAE;gBAAEX,OAAO,EAAE,cAAc;gBAAEC,MAAM,EAAE;cAAS;YACxD;UACJ,CAAC;UACDa,MAAM,EAAE;YACJC,IAAI,EAAE,SAAS;YACfN,KAAK,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,mBAAmB,CAAC;YACxDJ,IAAI,EAAE,iBAAiB;YACvBW,MAAM,EAAE;cAAEC,GAAG,EAAE,cAAc;cAAE,GAAGpB;YAAqB;UAC3D;QACJ;MACJ,CAAC;MACDT,aAAa,EAAEgB,4BAA4B,CAAC,CACxC,oBAAoB,EACpB,iBAAiB,EACjB,YAAY,EACZ,cAAc,CACjB,CAAC;MACFyB,mBAAmB,EAAE;QACjBpB,KAAK,EAAE,YAAY;QACnBqB,IAAI,EAAE,UAAU;QAChBzB,IAAI,EAAE,CAAC,iBAAiB,EAAE,YAAY,EAAE,cAAc,CAAC;QACvDE,MAAM,EAAE;UACJwB,UAAU,EAAE;YACRzB,OAAO,EAAE,SAAS;YAClBC,MAAM,EAAE;cACJyB,OAAO,EAAE;gBACLhB,MAAM,EAAE;kBACJC,GAAG,EAAE,gBAAgB;kBACrBnB,MAAM,EAAE;oBACJG,MAAM,EAAE,OAAO;oBACfD,OAAO,EAAE;kBACb,CAAC;kBACDE,OAAO,EAAE;oBACLD,MAAM,EAAE,SAAS;oBACjBD,OAAO,EAAE;kBACb;gBACJ;cACJ,CAAC;cACDiC,KAAK,EAAE;gBAAExB,KAAK,EAAE;cAAa,CAAC;cAC9ByB,OAAO,EAAE;gBAAEzB,KAAK,EAAE;cAAa;YACnC,CAAC;YACDC,EAAE,EAAE;cACAE,OAAO,EAAE;gBACLZ,OAAO,EAAE,eAAe;gBACxBC,MAAM,EAAE;cACZ,CAAC;cACDY,MAAM,EAAE;gBACJb,OAAO,EAAE,aAAa;gBACtBC,MAAM,EAAE;cACZ,CAAC;cACDkC,IAAI,EAAE;gBACFnC,OAAO,EAAE,YAAY;gBACrBC,MAAM,EAAE;cACZ;YACJ;UACJ,CAAC;UACDa,MAAM,EAAE;YACJR,OAAO,EAAE,MAAM;YACfG,KAAK,EAAE,YAAY;YACnBF,MAAM,EAAE;cACJC,IAAI,EAAE;gBACFC,KAAK,EAAE,YAAY;gBACnBC,EAAE,EAAE;kBACAC,MAAM,EAAE;oBAAEX,OAAO,EAAE,cAAc;oBAAEC,MAAM,EAAE;kBAAW;gBAC1D;cACJ,CAAC;cACDmC,QAAQ,EAAE;gBACN3B,KAAK,EAAE,YAAY;gBACnBO,MAAM,EAAE;kBACJC,GAAG,EAAE,gBAAgB;kBACrBnB,MAAM,EAAE;oBACJE,OAAO,EAAE,sBAAsB;oBAC/BC,MAAM,EAAE;kBACZ,CAAC;kBACDC,OAAO,EAAE;oBAAEF,OAAO,EAAE,gBAAgB;oBAAEC,MAAM,EAAE;kBAAO;gBACzD;cACJ,CAAC;cACD+B,OAAO,EAAE;gBACLjB,IAAI,EAAE,SAAS;gBACfN,KAAK,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;gBACnCO,MAAM,EAAE;kBACJC,GAAG,EAAE,2BAA2B;kBAChC,GAAGpB;gBACP;cACJ;YACJ;UACJ;QACJ;MACJ,CAAC;MACDwC,SAAS,EAAEjC,4BAA4B,CAAC,CACpC,iBAAiB,EACjB,YAAY,EACZ,cAAc,CACjB,CAAC;MACFkC,UAAU,EAAElC,4BAA4B,CAAC,CACrC,iBAAiB,EACjB,YAAY,EACZ,cAAc,CACjB,CAAC;MACFmC,aAAa,EAAEnC,4BAA4B,CAAC,CACxC,iBAAiB,EACjB,YAAY,EACZ,cAAc,CACjB,CAAC;MACFoC,QAAQ,EAAE;QACNV,IAAI,EAAE,OAAO;QACbW,IAAI,EAAGC,OAAO,KAAM;UAChBC,mBAAmB,EAAED,OAAO,CAACC,mBAAmB;UAChDC,WAAW,EAAEF,OAAO,CAACE,WAAW;UAChCpB,IAAI,EAAEkB,OAAO,CAAClB,IAAI;UAClBqB,wBAAwB,EAAEH,OAAO,CAACG,wBAAwB;UAC1DC,QAAQ,EAAEJ,OAAO,CAACI;QACtB,CAAC;MACL;IACJ;EACJ,CAAC,EAAE;IACC;IACA9C,OAAO,EAAE;MAAE,GAAGR,OAAO;MAAEN,UAAU,EAAEA,UAAU,CAAC;IAAE,CAAC;IACjD6D,MAAM,EAAErD,MAAM;IACdyB,QAAQ,EAAE;MACA7B,mBAAmBA,CAAA,EAAG;QAAA,OAAA0D,iBAAA;UACxB,OAAO1D,mBAAmB,CAAC,CAAC;QAAC;MACjC,CAAC;MACDD,aAAaA,CAAC;QAAEyD;MAAS,CAAC,EAAE;QACxB,OAAOzD,aAAa,CAAC;UAAEyD;QAAS,CAAC,CAAC;MACtC,CAAC;MACDG,sBAAsBA,CAAC;QAAEH;MAAS,CAAC,EAAE;QACjC,OAAO3B,QAAQ,CAAC8B,sBAAsB,CAAC;UAAEH;QAAS,CAAC,CAAC;MACxD,CAAC;MACDI,YAAYA,CAAC;QAAEC,UAAU;QAAEL;MAAS,CAAC,EAAE;QACnC,MAAM;UAAEM;QAAS,CAAC,GAAGD,UAAU;QAC/B,OAAOhC,QAAQ,CAAC+B,YAAY,CAAC;UAAEJ,QAAQ;UAAEM;QAAS,CAAC,CAAC;MACxD,CAAC;MACDhE,aAAaA,CAAC;QAAE+D,UAAU;QAAE3B;MAAK,CAAC,EAAE;QAChC,MAAM6B,aAAa,GAAGzD,6BAA6B,CAAC4B,IAAI,CAAC;QACzD,MAAM;UAAE,CAAC6B,aAAa,GAAGC;QAAkB,CAAC,GAAGH,UAAU;QACzD,OAAOhC,QAAQ,CAACoC,mBAAmB,CAAC;UAAED;QAAkB,CAAC,CAAC;MAC9D,CAAC;MACKE,yBAAyBA,CAAC;QAAEL;MAAW,CAAC,EAAE;QAAA,OAAAH,iBAAA;UAC5C,IAAI;YAAEI,QAAQ,EAAEE,iBAAiB;YAAEG,YAAY;YAAEC,YAAY;YAC7D;YACA;YACAC,gBAAgB;YAAE,GAAGC;UAAe,CAAC,GAAGT,UAAU;UAClD,IAAIU,0BAA0B;UAC9B,IAAIJ,YAAY,EAAE;YACdI,0BAA0B,GACtB,GAAGH,YAAY,GAAGD,YAAY,EAAE,CAACK,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;YAChEF,cAAc,GAAG;cACb,GAAGA,cAAc;cACjBH,YAAY,EAAEI;YAClB,CAAC;UACL;UACA,MAAME,KAAK,GAAG;YACVT,iBAAiB;YACjBU,OAAO,EAAE;cAAEJ;YAAe;UAC9B,CAAC;UACD,OAAOxE,aAAa,CAAC2E,KAAK,CAAC;QAAC;MAChC,CAAC;MACD5E,kBAAkBA,CAAC8E,CAAC,EAAE;QAAExB;MAAK,CAAC,EAAE;QAC5B,OAAOtD,kBAAkB,CAACsD,IAAI,CAAC;MACnC,CAAC;MACKyB,cAAcA,CAACxB,OAAO,EAAE;QAAA,OAAAM,iBAAA;UAC1B,OAAOzD,aAAa,CAACmD,OAAO,CAACS,UAAU,EAAET,OAAO,CAACyB,OAAO,EAAEzB,OAAO,CAAC0B,gBAAgB,EAAE,CAChF3E,eAAe,CAAC4E,oBAAoB,EACpC5E,eAAe,CAAC6E,uBAAuB,CAC1C,CAAC;QAAC;MACP;IACJ;EACJ,CAAC,CAAC;AACN;AAEA,SAASpD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}