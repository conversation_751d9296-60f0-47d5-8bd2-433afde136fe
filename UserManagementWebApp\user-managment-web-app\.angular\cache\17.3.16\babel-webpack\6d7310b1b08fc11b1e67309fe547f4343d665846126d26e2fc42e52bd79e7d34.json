{"ast": null, "code": "const badge = {\n  // Default styles\n  color: {\n    value: '{colors.font.primary.value}'\n  },\n  lineHeight: {\n    value: 1\n  },\n  fontWeight: {\n    value: '{fontWeights.semibold.value}'\n  },\n  fontSize: {\n    value: '{fontSizes.small.value}'\n  },\n  textAlign: {\n    value: 'center'\n  },\n  paddingVertical: {\n    value: '{space.xs.value}'\n  },\n  paddingHorizontal: {\n    value: '{space.small.value}'\n  },\n  backgroundColor: {\n    value: '{colors.background.tertiary.value}'\n  },\n  // An arbitrarily large value to ensure that the left and right sides of the badge are perfectly rounded for any size variation\n  borderRadius: {\n    value: '{radii.xl.value}'\n  },\n  // Variations\n  info: {\n    color: {\n      value: '{colors.font.info.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.info.value}'\n    }\n  },\n  warning: {\n    color: {\n      value: '{colors.font.warning.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.warning.value}'\n    }\n  },\n  success: {\n    color: {\n      value: '{colors.font.success.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.success.value}'\n    }\n  },\n  error: {\n    color: {\n      value: '{colors.font.error.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.error.value}'\n    }\n  },\n  // Sizes\n  small: {\n    fontSize: {\n      value: '{fontSizes.xs.value}'\n    },\n    paddingVertical: {\n      value: '{space.xxs.value}'\n    },\n    paddingHorizontal: {\n      value: '{space.xs.value}'\n    }\n  },\n  // medium is the default size\n  large: {\n    fontSize: {\n      value: '{fontSizes.medium.value}'\n    },\n    paddingVertical: {\n      value: '{space.small.value}'\n    },\n    paddingHorizontal: {\n      value: '{space.medium.value}'\n    }\n  }\n};\nexport { badge };", "map": {"version": 3, "names": ["badge", "color", "value", "lineHeight", "fontWeight", "fontSize", "textAlign", "paddingVertical", "paddingHorizontal", "backgroundColor", "borderRadius", "info", "warning", "success", "error", "small", "large"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/badge.mjs"], "sourcesContent": ["const badge = {\n    // Default styles\n    color: { value: '{colors.font.primary.value}' },\n    lineHeight: { value: 1 },\n    fontWeight: { value: '{fontWeights.semibold.value}' },\n    fontSize: { value: '{fontSizes.small.value}' },\n    textAlign: { value: 'center' },\n    paddingVertical: { value: '{space.xs.value}' },\n    paddingHorizontal: { value: '{space.small.value}' },\n    backgroundColor: { value: '{colors.background.tertiary.value}' },\n    // An arbitrarily large value to ensure that the left and right sides of the badge are perfectly rounded for any size variation\n    borderRadius: { value: '{radii.xl.value}' },\n    // Variations\n    info: {\n        color: { value: '{colors.font.info.value}' },\n        backgroundColor: { value: '{colors.background.info.value}' },\n    },\n    warning: {\n        color: { value: '{colors.font.warning.value}' },\n        backgroundColor: { value: '{colors.background.warning.value}' },\n    },\n    success: {\n        color: { value: '{colors.font.success.value}' },\n        backgroundColor: { value: '{colors.background.success.value}' },\n    },\n    error: {\n        color: { value: '{colors.font.error.value}' },\n        backgroundColor: { value: '{colors.background.error.value}' },\n    },\n    // Sizes\n    small: {\n        fontSize: { value: '{fontSizes.xs.value}' },\n        paddingVertical: { value: '{space.xxs.value}' },\n        paddingHorizontal: { value: '{space.xs.value}' },\n    },\n    // medium is the default size\n    large: {\n        fontSize: { value: '{fontSizes.medium.value}' },\n        paddingVertical: { value: '{space.small.value}' },\n        paddingHorizontal: { value: '{space.medium.value}' },\n    },\n};\n\nexport { badge };\n"], "mappings": "AAAA,MAAMA,KAAK,GAAG;EACV;EACAC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAA8B,CAAC;EAC/CC,UAAU,EAAE;IAAED,KAAK,EAAE;EAAE,CAAC;EACxBE,UAAU,EAAE;IAAEF,KAAK,EAAE;EAA+B,CAAC;EACrDG,QAAQ,EAAE;IAAEH,KAAK,EAAE;EAA0B,CAAC;EAC9CI,SAAS,EAAE;IAAEJ,KAAK,EAAE;EAAS,CAAC;EAC9BK,eAAe,EAAE;IAAEL,KAAK,EAAE;EAAmB,CAAC;EAC9CM,iBAAiB,EAAE;IAAEN,KAAK,EAAE;EAAsB,CAAC;EACnDO,eAAe,EAAE;IAAEP,KAAK,EAAE;EAAqC,CAAC;EAChE;EACAQ,YAAY,EAAE;IAAER,KAAK,EAAE;EAAmB,CAAC;EAC3C;EACAS,IAAI,EAAE;IACFV,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA2B,CAAC;IAC5CO,eAAe,EAAE;MAAEP,KAAK,EAAE;IAAiC;EAC/D,CAAC;EACDU,OAAO,EAAE;IACLX,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA8B,CAAC;IAC/CO,eAAe,EAAE;MAAEP,KAAK,EAAE;IAAoC;EAClE,CAAC;EACDW,OAAO,EAAE;IACLZ,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA8B,CAAC;IAC/CO,eAAe,EAAE;MAAEP,KAAK,EAAE;IAAoC;EAClE,CAAC;EACDY,KAAK,EAAE;IACHb,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA4B,CAAC;IAC7CO,eAAe,EAAE;MAAEP,KAAK,EAAE;IAAkC;EAChE,CAAC;EACD;EACAa,KAAK,EAAE;IACHV,QAAQ,EAAE;MAAEH,KAAK,EAAE;IAAuB,CAAC;IAC3CK,eAAe,EAAE;MAAEL,KAAK,EAAE;IAAoB,CAAC;IAC/CM,iBAAiB,EAAE;MAAEN,KAAK,EAAE;IAAmB;EACnD,CAAC;EACD;EACAc,KAAK,EAAE;IACHX,QAAQ,EAAE;MAAEH,KAAK,EAAE;IAA2B,CAAC;IAC/CK,eAAe,EAAE;MAAEL,KAAK,EAAE;IAAsB,CAAC;IACjDM,iBAAiB,EAAE;MAAEN,KAAK,EAAE;IAAuB;EACvD;AACJ,CAAC;AAED,SAASF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}