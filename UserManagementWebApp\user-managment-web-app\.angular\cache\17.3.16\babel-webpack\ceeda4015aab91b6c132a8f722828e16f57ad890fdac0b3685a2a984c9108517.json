{"ast": null, "code": "import { KeyValueStorage } from './KeyValueStorage.mjs';\nimport { getSessionStorageWithFallback } from './utils.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nclass SessionStorage extends KeyValueStorage {\n  constructor() {\n    super(getSessionStorageWithFallback());\n  }\n}\nexport { SessionStorage };", "map": {"version": 3, "names": ["KeyValueStorage", "getSessionStorageWithFallback", "SessionStorage", "constructor"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/storage/SessionStorage.mjs"], "sourcesContent": ["import { KeyValueStorage } from './KeyValueStorage.mjs';\nimport { getSessionStorageWithFallback } from './utils.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nclass SessionStorage extends KeyValueStorage {\n    constructor() {\n        super(getSessionStorageWithFallback());\n    }\n}\n\nexport { SessionStorage };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,uBAAuB;AACvD,SAASC,6BAA6B,QAAQ,aAAa;;AAE3D;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,SAASF,eAAe,CAAC;EACzCG,WAAWA,CAAA,EAAG;IACV,KAAK,CAACF,6BAA6B,CAAC,CAAC,CAAC;EAC1C;AACJ;AAEA,SAASC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}