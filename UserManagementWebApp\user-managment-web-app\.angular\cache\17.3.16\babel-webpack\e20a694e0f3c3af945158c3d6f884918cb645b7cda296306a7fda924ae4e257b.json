{"ast": null, "code": "import { AmplifyUrl } from '@aws-amplify/core/internals/utils';\nimport { cognitoUserPoolEndpointResolver } from '../../../foundation/cognitoUserPoolEndpointResolver.mjs';\nconst createCognitoUserPoolEndpointResolver = ({\n  endpointOverride\n}) => input => {\n  if (endpointOverride) {\n    return {\n      url: new AmplifyUrl(endpointOverride)\n    };\n  }\n  return cognitoUserPoolEndpointResolver(input);\n};\nexport { createCognitoUserPoolEndpointResolver };", "map": {"version": 3, "names": ["AmplifyUrl", "cognitoUserPoolEndpointResolver", "createCognitoUserPoolEndpointResolver", "endpointOverride", "input", "url"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs"], "sourcesContent": ["import { AmplifyUrl } from '@aws-amplify/core/internals/utils';\nimport { cognitoUserPoolEndpointResolver } from '../../../foundation/cognitoUserPoolEndpointResolver.mjs';\n\nconst createCognitoUserPoolEndpointResolver = ({ endpointOverride }) => (input) => {\n    if (endpointOverride) {\n        return { url: new AmplifyUrl(endpointOverride) };\n    }\n    return cognitoUserPoolEndpointResolver(input);\n};\n\nexport { createCognitoUserPoolEndpointResolver };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,+BAA+B,QAAQ,yDAAyD;AAEzG,MAAMC,qCAAqC,GAAGA,CAAC;EAAEC;AAAiB,CAAC,KAAMC,KAAK,IAAK;EAC/E,IAAID,gBAAgB,EAAE;IAClB,OAAO;MAAEE,GAAG,EAAE,IAAIL,UAAU,CAACG,gBAAgB;IAAE,CAAC;EACpD;EACA,OAAOF,+BAA+B,CAACG,KAAK,CAAC;AACjD,CAAC;AAED,SAASF,qCAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}