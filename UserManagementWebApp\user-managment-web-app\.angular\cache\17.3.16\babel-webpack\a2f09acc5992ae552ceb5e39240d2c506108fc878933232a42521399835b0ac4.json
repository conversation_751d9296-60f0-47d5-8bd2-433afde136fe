{"ast": null, "code": "import { Framework } from '../types.mjs';\nimport { reactWebDetect, reactSSRDetect } from './React.mjs';\nimport { vueWebDetect, vueSSRDetect } from './Vue.mjs';\nimport { svelteWebDetect, svelteSSRDetect } from './Svelte.mjs';\nimport { nextWebDetect, nextSSRDetect } from './Next.mjs';\nimport { nuxtWebDetect, nuxtSSRDetect } from './Nuxt.mjs';\nimport { angularWebDetect, angularSSRDetect } from './Angular.mjs';\nimport { reactNativeDetect } from './ReactNative.mjs';\nimport { expoDetect } from './Expo.mjs';\nimport { webDetect } from './Web.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// These are in the order of detection where when both are detectable, the early Framework will be reported\nconst detectionMap = [\n// First, detect mobile\n{\n  platform: Framework.Expo,\n  detectionMethod: expoDetect\n}, {\n  platform: Framework.ReactNative,\n  detectionMethod: reactNativeDetect\n},\n// Next, detect web frameworks\n{\n  platform: Framework.NextJs,\n  detectionMethod: nextWebDetect\n}, {\n  platform: Framework.Nuxt,\n  detectionMethod: nuxtWebDetect\n}, {\n  platform: Framework.Angular,\n  detectionMethod: angularWebDetect\n}, {\n  platform: Framework.React,\n  detectionMethod: reactWebDetect\n}, {\n  platform: Framework.VueJs,\n  detectionMethod: vueWebDetect\n}, {\n  platform: Framework.Svelte,\n  detectionMethod: svelteWebDetect\n}, {\n  platform: Framework.WebUnknown,\n  detectionMethod: webDetect\n},\n// Last, detect ssr frameworks\n{\n  platform: Framework.NextJsSSR,\n  detectionMethod: nextSSRDetect\n}, {\n  platform: Framework.NuxtSSR,\n  detectionMethod: nuxtSSRDetect\n}, {\n  platform: Framework.ReactSSR,\n  detectionMethod: reactSSRDetect\n}, {\n  platform: Framework.VueJsSSR,\n  detectionMethod: vueSSRDetect\n}, {\n  platform: Framework.AngularSSR,\n  detectionMethod: angularSSRDetect\n}, {\n  platform: Framework.SvelteSSR,\n  detectionMethod: svelteSSRDetect\n}];\nfunction detect() {\n  return detectionMap.find(detectionEntry => detectionEntry.detectionMethod())?.platform || Framework.ServerSideUnknown;\n}\nexport { detect };", "map": {"version": 3, "names": ["Framework", "reactWebDetect", "reactSSRDetect", "vueWebDetect", "vueSSRDetect", "svelteWebDetect", "svelteSSRDetect", "nextWebDetect", "nextSSRDetect", "nuxtWebDetect", "nuxtSSRDetect", "angularWebDetect", "angularSSRDetect", "reactNativeDetect", "expoDetect", "webDetect", "detectionMap", "platform", "Expo", "detectionMethod", "ReactNative", "NextJs", "<PERSON><PERSON><PERSON>", "Angular", "React", "<PERSON><PERSON><PERSON><PERSON>", "Svelte", "WebUnknown", "NextJsSSR", "NuxtSSR", "ReactSSR", "VueJsSSR", "AngularSSR", "SvelteSSR", "detect", "find", "detectionEntry", "ServerSideUnknown"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Platform/detection/index.mjs"], "sourcesContent": ["import { Framework } from '../types.mjs';\nimport { reactWebDetect, reactSSRDetect } from './React.mjs';\nimport { vueWebDetect, vueSSRDetect } from './Vue.mjs';\nimport { svelteWebDetect, svelteSSRDetect } from './Svelte.mjs';\nimport { nextWebDetect, nextSSRDetect } from './Next.mjs';\nimport { nuxtWebDetect, nuxtSSRDetect } from './Nuxt.mjs';\nimport { angularWebDetect, angularSSRDetect } from './Angular.mjs';\nimport { reactNativeDetect } from './ReactNative.mjs';\nimport { expoDetect } from './Expo.mjs';\nimport { webDetect } from './Web.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// These are in the order of detection where when both are detectable, the early Framework will be reported\nconst detectionMap = [\n    // First, detect mobile\n    { platform: Framework.Expo, detectionMethod: expoDetect },\n    { platform: Framework.ReactNative, detectionMethod: reactNativeDetect },\n    // Next, detect web frameworks\n    { platform: Framework.NextJs, detectionMethod: nextWebDetect },\n    { platform: Framework.Nuxt, detectionMethod: nuxtWebDetect },\n    { platform: Framework.Angular, detectionMethod: angularWebDetect },\n    { platform: Framework.React, detectionMethod: reactWebDetect },\n    { platform: Framework.VueJs, detectionMethod: vueWebDetect },\n    { platform: Framework.Svelte, detectionMethod: svelteWebDetect },\n    { platform: Framework.WebUnknown, detectionMethod: webDetect },\n    // Last, detect ssr frameworks\n    { platform: Framework.NextJsSSR, detectionMethod: nextSSRDetect },\n    { platform: Framework.NuxtSSR, detectionMethod: nuxtSSRDetect },\n    { platform: Framework.ReactSSR, detectionMethod: reactSSRDetect },\n    { platform: Framework.VueJsSSR, detectionMethod: vueSSRDetect },\n    { platform: Framework.AngularSSR, detectionMethod: angularSSRDetect },\n    { platform: Framework.SvelteSSR, detectionMethod: svelteSSRDetect },\n];\nfunction detect() {\n    return (detectionMap.find(detectionEntry => detectionEntry.detectionMethod())\n        ?.platform || Framework.ServerSideUnknown);\n}\n\nexport { detect };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,cAAc;AACxC,SAASC,cAAc,EAAEC,cAAc,QAAQ,aAAa;AAC5D,SAASC,YAAY,EAAEC,YAAY,QAAQ,WAAW;AACtD,SAASC,eAAe,EAAEC,eAAe,QAAQ,cAAc;AAC/D,SAASC,aAAa,EAAEC,aAAa,QAAQ,YAAY;AACzD,SAASC,aAAa,EAAEC,aAAa,QAAQ,YAAY;AACzD,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,eAAe;AAClE,SAASC,iBAAiB,QAAQ,mBAAmB;AACrD,SAASC,UAAU,QAAQ,YAAY;AACvC,SAASC,SAAS,QAAQ,WAAW;;AAErC;AACA;AACA;AACA,MAAMC,YAAY,GAAG;AACjB;AACA;EAAEC,QAAQ,EAAEjB,SAAS,CAACkB,IAAI;EAAEC,eAAe,EAAEL;AAAW,CAAC,EACzD;EAAEG,QAAQ,EAAEjB,SAAS,CAACoB,WAAW;EAAED,eAAe,EAAEN;AAAkB,CAAC;AACvE;AACA;EAAEI,QAAQ,EAAEjB,SAAS,CAACqB,MAAM;EAAEF,eAAe,EAAEZ;AAAc,CAAC,EAC9D;EAAEU,QAAQ,EAAEjB,SAAS,CAACsB,IAAI;EAAEH,eAAe,EAAEV;AAAc,CAAC,EAC5D;EAAEQ,QAAQ,EAAEjB,SAAS,CAACuB,OAAO;EAAEJ,eAAe,EAAER;AAAiB,CAAC,EAClE;EAAEM,QAAQ,EAAEjB,SAAS,CAACwB,KAAK;EAAEL,eAAe,EAAElB;AAAe,CAAC,EAC9D;EAAEgB,QAAQ,EAAEjB,SAAS,CAACyB,KAAK;EAAEN,eAAe,EAAEhB;AAAa,CAAC,EAC5D;EAAEc,QAAQ,EAAEjB,SAAS,CAAC0B,MAAM;EAAEP,eAAe,EAAEd;AAAgB,CAAC,EAChE;EAAEY,QAAQ,EAAEjB,SAAS,CAAC2B,UAAU;EAAER,eAAe,EAAEJ;AAAU,CAAC;AAC9D;AACA;EAAEE,QAAQ,EAAEjB,SAAS,CAAC4B,SAAS;EAAET,eAAe,EAAEX;AAAc,CAAC,EACjE;EAAES,QAAQ,EAAEjB,SAAS,CAAC6B,OAAO;EAAEV,eAAe,EAAET;AAAc,CAAC,EAC/D;EAAEO,QAAQ,EAAEjB,SAAS,CAAC8B,QAAQ;EAAEX,eAAe,EAAEjB;AAAe,CAAC,EACjE;EAAEe,QAAQ,EAAEjB,SAAS,CAAC+B,QAAQ;EAAEZ,eAAe,EAAEf;AAAa,CAAC,EAC/D;EAAEa,QAAQ,EAAEjB,SAAS,CAACgC,UAAU;EAAEb,eAAe,EAAEP;AAAiB,CAAC,EACrE;EAAEK,QAAQ,EAAEjB,SAAS,CAACiC,SAAS;EAAEd,eAAe,EAAEb;AAAgB,CAAC,CACtE;AACD,SAAS4B,MAAMA,CAAA,EAAG;EACd,OAAQlB,YAAY,CAACmB,IAAI,CAACC,cAAc,IAAIA,cAAc,CAACjB,eAAe,CAAC,CAAC,CAAC,EACvEF,QAAQ,IAAIjB,SAAS,CAACqC,iBAAiB;AACjD;AAEA,SAASH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}