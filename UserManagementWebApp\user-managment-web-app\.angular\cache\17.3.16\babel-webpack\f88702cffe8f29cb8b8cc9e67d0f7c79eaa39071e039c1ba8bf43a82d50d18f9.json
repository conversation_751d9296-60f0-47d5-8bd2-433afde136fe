{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { noCase } from \"no-case\";\nexport function pascalCaseTransform(input, index) {\n  var firstChar = input.charAt(0);\n  var lowerChars = input.substr(1).toLowerCase();\n  if (index > 0 && firstChar >= \"0\" && firstChar <= \"9\") {\n    return \"_\" + firstChar + lowerChars;\n  }\n  return \"\" + firstChar.toUpperCase() + lowerChars;\n}\nexport function pascalCaseTransformMerge(input) {\n  return input.charAt(0).toUpperCase() + input.slice(1).toLowerCase();\n}\nexport function pascalCase(input, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return noCase(input, __assign({\n    delimiter: \"\",\n    transform: pascalCaseTransform\n  }, options));\n}", "map": {"version": 3, "names": ["__assign", "noCase", "pascalCaseTransform", "input", "index", "firstChar", "char<PERSON>t", "lowerChars", "substr", "toLowerCase", "toUpperCase", "pascalCaseTransformMerge", "slice", "pascalCase", "options", "delimiter", "transform"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/pascal-case/dist.es2015/index.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { noCase } from \"no-case\";\nexport function pascalCaseTransform(input, index) {\n    var firstChar = input.charAt(0);\n    var lowerChars = input.substr(1).toLowerCase();\n    if (index > 0 && firstChar >= \"0\" && firstChar <= \"9\") {\n        return \"_\" + firstChar + lowerChars;\n    }\n    return \"\" + firstChar.toUpperCase() + lowerChars;\n}\nexport function pascalCaseTransformMerge(input) {\n    return input.charAt(0).toUpperCase() + input.slice(1).toLowerCase();\n}\nexport function pascalCase(input, options) {\n    if (options === void 0) { options = {}; }\n    return noCase(input, __assign({ delimiter: \"\", transform: pascalCaseTransform }, options));\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,MAAM,QAAQ,SAAS;AAChC,OAAO,SAASC,mBAAmBA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAC9C,IAAIC,SAAS,GAAGF,KAAK,CAACG,MAAM,CAAC,CAAC,CAAC;EAC/B,IAAIC,UAAU,GAAGJ,KAAK,CAACK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAC9C,IAAIL,KAAK,GAAG,CAAC,IAAIC,SAAS,IAAI,GAAG,IAAIA,SAAS,IAAI,GAAG,EAAE;IACnD,OAAO,GAAG,GAAGA,SAAS,GAAGE,UAAU;EACvC;EACA,OAAO,EAAE,GAAGF,SAAS,CAACK,WAAW,CAAC,CAAC,GAAGH,UAAU;AACpD;AACA,OAAO,SAASI,wBAAwBA,CAACR,KAAK,EAAE;EAC5C,OAAOA,KAAK,CAACG,MAAM,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,GAAGP,KAAK,CAACS,KAAK,CAAC,CAAC,CAAC,CAACH,WAAW,CAAC,CAAC;AACvE;AACA,OAAO,SAASI,UAAUA,CAACV,KAAK,EAAEW,OAAO,EAAE;EACvC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC,CAAC;EAAE;EACxC,OAAOb,MAAM,CAACE,KAAK,EAAEH,QAAQ,CAAC;IAAEe,SAAS,EAAE,EAAE;IAAEC,SAAS,EAAEd;EAAoB,CAAC,EAAEY,OAAO,CAAC,CAAC;AAC9F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}