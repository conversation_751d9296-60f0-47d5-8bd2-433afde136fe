{"ast": null, "code": "import { defaultStorage } from '@aws-amplify/core';\nimport { refreshAuthTokens } from '../utils/refreshAuthTokens.mjs';\nimport { DefaultTokenStore } from './TokenStore.mjs';\nimport { TokenOrchestrator } from './TokenOrchestrator.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass CognitoUserPoolsTokenProvider {\n  constructor() {\n    this.authTokenStore = new DefaultTokenStore();\n    this.authTokenStore.setKeyValueStorage(defaultStorage);\n    this.tokenOrchestrator = new TokenOrchestrator();\n    this.tokenOrchestrator.setAuthTokenStore(this.authTokenStore);\n    this.tokenOrchestrator.setTokenRefresher(refreshAuthTokens);\n  }\n  getTokens({\n    forceRefresh\n  } = {\n    forceRefresh: false\n  }) {\n    return this.tokenOrchestrator.getTokens({\n      forceRefresh\n    });\n  }\n  setKeyValueStorage(keyValueStorage) {\n    this.authTokenStore.setKeyValueStorage(keyValueStorage);\n  }\n  setAuthConfig(authConfig) {\n    this.authTokenStore.setAuthConfig(authConfig);\n    this.tokenOrchestrator.setAuthConfig(authConfig);\n  }\n}\nexport { CognitoUserPoolsTokenProvider };", "map": {"version": 3, "names": ["defaultStorage", "refreshAuthTokens", "DefaultTokenStore", "TokenOrchestrator", "CognitoUserPoolsTokenProvider", "constructor", "authTokenStore", "setKeyValueStorage", "tokenOrchestrator", "setAuthTokenStore", "setTokenRefresher", "getTokens", "forceRefresh", "keyValueStorage", "setAuthConfig", "authConfig"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/tokenProvider/CognitoUserPoolsTokenProvider.mjs"], "sourcesContent": ["import { defaultStorage } from '@aws-amplify/core';\nimport { refreshAuthTokens } from '../utils/refreshAuthTokens.mjs';\nimport { DefaultTokenStore } from './TokenStore.mjs';\nimport { TokenOrchestrator } from './TokenOrchestrator.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass CognitoUserPoolsTokenProvider {\n    constructor() {\n        this.authTokenStore = new DefaultTokenStore();\n        this.authTokenStore.setKeyValueStorage(defaultStorage);\n        this.tokenOrchestrator = new TokenOrchestrator();\n        this.tokenOrchestrator.setAuthTokenStore(this.authTokenStore);\n        this.tokenOrchestrator.setTokenRefresher(refreshAuthTokens);\n    }\n    getTokens({ forceRefresh } = { forceRefresh: false }) {\n        return this.tokenOrchestrator.getTokens({ forceRefresh });\n    }\n    setKeyValueStorage(keyValueStorage) {\n        this.authTokenStore.setKeyValueStorage(keyValueStorage);\n    }\n    setAuthConfig(authConfig) {\n        this.authTokenStore.setAuthConfig(authConfig);\n        this.tokenOrchestrator.setAuthConfig(authConfig);\n    }\n}\n\nexport { CognitoUserPoolsTokenProvider };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mBAAmB;AAClD,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,SAASC,iBAAiB,QAAQ,yBAAyB;;AAE3D;AACA;AACA,MAAMC,6BAA6B,CAAC;EAChCC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,cAAc,GAAG,IAAIJ,iBAAiB,CAAC,CAAC;IAC7C,IAAI,CAACI,cAAc,CAACC,kBAAkB,CAACP,cAAc,CAAC;IACtD,IAAI,CAACQ,iBAAiB,GAAG,IAAIL,iBAAiB,CAAC,CAAC;IAChD,IAAI,CAACK,iBAAiB,CAACC,iBAAiB,CAAC,IAAI,CAACH,cAAc,CAAC;IAC7D,IAAI,CAACE,iBAAiB,CAACE,iBAAiB,CAACT,iBAAiB,CAAC;EAC/D;EACAU,SAASA,CAAC;IAAEC;EAAa,CAAC,GAAG;IAAEA,YAAY,EAAE;EAAM,CAAC,EAAE;IAClD,OAAO,IAAI,CAACJ,iBAAiB,CAACG,SAAS,CAAC;MAAEC;IAAa,CAAC,CAAC;EAC7D;EACAL,kBAAkBA,CAACM,eAAe,EAAE;IAChC,IAAI,CAACP,cAAc,CAACC,kBAAkB,CAACM,eAAe,CAAC;EAC3D;EACAC,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI,CAACT,cAAc,CAACQ,aAAa,CAACC,UAAU,CAAC;IAC7C,IAAI,CAACP,iBAAiB,CAACM,aAAa,CAACC,UAAU,CAAC;EACpD;AACJ;AAEA,SAASX,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}