{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst parseMetadata = response => {\n  const {\n    headers,\n    statusCode\n  } = response;\n  return {\n    ...(isMetadataBearer(response) ? response.$metadata : {}),\n    httpStatusCode: statusCode,\n    requestId: headers['x-amzn-requestid'] ?? headers['x-amzn-request-id'] ?? headers['x-amz-request-id'],\n    extendedRequestId: headers['x-amz-id-2'],\n    cfId: headers['x-amz-cf-id']\n  };\n};\nconst isMetadataBearer = response => typeof response?.$metadata === 'object';\nexport { parseMetadata };", "map": {"version": 3, "names": ["parseMetadata", "response", "headers", "statusCode", "isMetadataBearer", "$metadata", "httpStatusCode", "requestId", "extendedRequestId", "cfId"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/serde/responseInfo.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst parseMetadata = (response) => {\n    const { headers, statusCode } = response;\n    return {\n        ...(isMetadataBearer(response) ? response.$metadata : {}),\n        httpStatusCode: statusCode,\n        requestId: headers['x-amzn-requestid'] ??\n            headers['x-amzn-request-id'] ??\n            headers['x-amz-request-id'],\n        extendedRequestId: headers['x-amz-id-2'],\n        cfId: headers['x-amz-cf-id'],\n    };\n};\nconst isMetadataBearer = (response) => typeof response?.$metadata === 'object';\n\nexport { parseMetadata };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,aAAa,GAAIC,QAAQ,IAAK;EAChC,MAAM;IAAEC,OAAO;IAAEC;EAAW,CAAC,GAAGF,QAAQ;EACxC,OAAO;IACH,IAAIG,gBAAgB,CAACH,QAAQ,CAAC,GAAGA,QAAQ,CAACI,SAAS,GAAG,CAAC,CAAC,CAAC;IACzDC,cAAc,EAAEH,UAAU;IAC1BI,SAAS,EAAEL,OAAO,CAAC,kBAAkB,CAAC,IAClCA,OAAO,CAAC,mBAAmB,CAAC,IAC5BA,OAAO,CAAC,kBAAkB,CAAC;IAC/BM,iBAAiB,EAAEN,OAAO,CAAC,YAAY,CAAC;IACxCO,IAAI,EAAEP,OAAO,CAAC,aAAa;EAC/B,CAAC;AACL,CAAC;AACD,MAAME,gBAAgB,GAAIH,QAAQ,IAAK,OAAOA,QAAQ,EAAEI,SAAS,KAAK,QAAQ;AAE9E,SAASL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}