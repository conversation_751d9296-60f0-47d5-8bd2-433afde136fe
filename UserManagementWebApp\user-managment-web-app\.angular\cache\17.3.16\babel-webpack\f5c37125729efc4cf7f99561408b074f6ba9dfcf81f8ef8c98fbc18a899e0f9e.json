{"ast": null, "code": "import { fromUtf8 as jsFromUtf8, toUtf8 as jsToUtf8 } from \"./pureJs\";\nimport { fromUtf8 as textEncoderFromUtf8, toUtf8 as textEncoderToUtf8 } from \"./whatwgEncodingApi\";\nexport var fromUtf8 = function (input) {\n  return typeof TextEncoder === \"function\" ? textEncoderFromUtf8(input) : jsFromUtf8(input);\n};\nexport var toUtf8 = function (input) {\n  return typeof TextDecoder === \"function\" ? textEncoderToUtf8(input) : jsToUtf8(input);\n};", "map": {"version": 3, "names": ["fromUtf8", "jsFromUtf8", "toUtf8", "jsToUtf8", "textEncoderFromUtf8", "textEncoderToUtf8", "input", "TextEncoder", "TextDecoder"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-lex-runtime-service/node_modules/@aws-sdk/util-utf8-browser/dist-es/index.js"], "sourcesContent": ["import { fromUtf8 as jsFromUtf8, toUtf8 as jsToUtf8 } from \"./pureJs\";\nimport { fromUtf8 as textEncoderFromUtf8, toUtf8 as textEncoderToUtf8 } from \"./whatwgEncodingApi\";\nexport var fromUtf8 = function (input) {\n    return typeof TextEncoder === \"function\" ? textEncoderFromUtf8(input) : jsFromUtf8(input);\n};\nexport var toUtf8 = function (input) {\n    return typeof TextDecoder === \"function\" ? textEncoderToUtf8(input) : jsToUtf8(input);\n};\n"], "mappings": "AAAA,SAASA,QAAQ,IAAIC,UAAU,EAAEC,MAAM,IAAIC,QAAQ,QAAQ,UAAU;AACrE,SAASH,QAAQ,IAAII,mBAAmB,EAAEF,MAAM,IAAIG,iBAAiB,QAAQ,qBAAqB;AAClG,OAAO,IAAIL,QAAQ,GAAG,SAAAA,CAAUM,KAAK,EAAE;EACnC,OAAO,OAAOC,WAAW,KAAK,UAAU,GAAGH,mBAAmB,CAACE,KAAK,CAAC,GAAGL,UAAU,CAACK,KAAK,CAAC;AAC7F,CAAC;AACD,OAAO,IAAIJ,MAAM,GAAG,SAAAA,CAAUI,KAAK,EAAE;EACjC,OAAO,OAAOE,WAAW,KAAK,UAAU,GAAGH,iBAAiB,CAACC,KAAK,CAAC,GAAGH,QAAQ,CAACG,KAAK,CAAC;AACzF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}