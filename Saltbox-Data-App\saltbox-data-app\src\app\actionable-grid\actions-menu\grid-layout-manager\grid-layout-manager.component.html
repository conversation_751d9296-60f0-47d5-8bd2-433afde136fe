<p-button [rounded]="true" (onClick)="menu_layout.toggle($event)" [disabled]="disabled" pTooltip="Layout"
  tooltipPosition="top" styleClass="min-w-0">
  <span><i class="pi pi-th-large vertical-align-bottom"></i></span>
  @if (selectedLayout) {
  <span class="ml-2">{{selectedLayout.name}}</span>
  }
  <span><i class="pi pi-angle-down vertical-align-middle ml-1"></i></span>
</p-button>

<p-menu #menu_layout [popup]="true" [model]="layoutMenuItems" appendTo="body" styleClass="grid-layouts-menu">
  <ng-template pTemplate="item" let-item>
    <div [pTooltip]="item.state && item.state.disabled ? 'The view has changed. This layout is no longer valid' : null"
      tooltipPosition="top"
      class="p-ripple cursor-pointer p-element flex align-items-center justify-content-between p-menu-item-link p-3 py-2">
      <div class="flex align-items-center">
        <span class="text-sm pr-3" [ngClass]="{
            'font-bold': item.state?.layout && item.state?.layout.id === selectedLayout?.id,
            'text-gray-400': item.state && item.state.disabled
          }">
          @if (!item.icon) {
          <i [ngClass]="{
                  'pi-check': item.state?.layout.id === selectedLayout?.id,
                  'pi-fw': !selectedLayout || item.state?.layout.id !== selectedLayout?.id
                }" class="mr-2 pi text-xs"></i>
          }
          {{item.label}}
          @if (item.state?.layout?.isDefault){<p-tag value="default" class="ml-2"></p-tag>}
        </span>
      </div>
      @if (item.state?.layout?.editable) {
      <div class="flex align-items-center">
        <p-button size="small" type="button" [disabled]="item.state && item.state.disabled"
          styleClass="m-0 mr-1" icon="pi pi-pencil text-sm" [text]="true"
          (onClick)="openSaveLayoutDialog('edit', item.state?.layout, $event)"></p-button>
        <p-button size="small" type="button" styleClass="m-0" icon="pi pi-trash text-sm"
          [text]="true" severity="danger" (onClick)="deleteLayout(item.state?.layout, $event)"></p-button>
      </div>
      }
    </div>
  </ng-template>
</p-menu>

<app-save-grid-layout-dialog [(visible)]="showSaveLayoutDialog" [formMode]="layoutFormMode" [layout]="layoutToEdit"
  [disableUserParams]="disableUserParams" [disableSlicerSettings]="disableSlicerSettings" [layouts]="layouts"
  (saveLayout)="onSaveLayout($event)">
</app-save-grid-layout-dialog>

<p-confirmDialog #cd>
  <ng-template pTemplate="footer">
    <p-button label="Delete" (onClick)="cd.accept()" severity="danger" styleClass="border-none"></p-button>
    <p-button label="Cancel" (onClick)="cd.reject()" severity="secondary" [outlined]="true"></p-button>
  </ng-template>
</p-confirmDialog>