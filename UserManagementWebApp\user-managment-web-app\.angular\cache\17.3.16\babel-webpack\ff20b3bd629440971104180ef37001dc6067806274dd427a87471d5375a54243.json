{"ast": null, "code": "import { ConsoleLogger } from '../Logger/ConsoleLogger.mjs';\nimport { NO_HUBCALLBACK_PROVIDED_EXCEPTION } from '../constants.mjs';\nimport { AmplifyError } from '../errors/AmplifyError.mjs';\nimport '../types/errors.mjs';\nimport '../errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst AMPLIFY_SYMBOL = typeof Symbol !== 'undefined' ? Symbol('amplify_default') : '@@amplify_default';\nconst logger = new ConsoleLogger('Hub');\nclass HubClass {\n  constructor(name) {\n    this.listeners = new Map();\n    this.protectedChannels = ['core', 'auth', 'api', 'analytics', 'interactions', 'pubsub', 'storage', 'ui', 'xr'];\n    this.name = name;\n  }\n  /**\n   * Used internally to remove a Hub listener.\n   *\n   * @remarks\n   * This private method is for internal use only. Instead of calling Hub.remove, call the result of Hub.listen.\n   */\n  _remove(channel, listener) {\n    const holder = this.listeners.get(channel);\n    if (!holder) {\n      logger.warn(`No listeners for ${channel}`);\n      return;\n    }\n    this.listeners.set(channel, [...holder.filter(({\n      callback\n    }) => callback !== listener)]);\n  }\n  dispatch(channel, payload, source, ampSymbol) {\n    if (typeof channel === 'string' && this.protectedChannels.indexOf(channel) > -1) {\n      const hasAccess = ampSymbol === AMPLIFY_SYMBOL;\n      if (!hasAccess) {\n        logger.warn(`WARNING: ${channel} is protected and dispatching on it can have unintended consequences`);\n      }\n    }\n    const capsule = {\n      channel,\n      payload: {\n        ...payload\n      },\n      source,\n      patternInfo: []\n    };\n    try {\n      this._toListeners(capsule);\n    } catch (e) {\n      logger.error(e);\n    }\n  }\n  listen(channel, callback, listenerName = 'noname') {\n    let cb;\n    if (typeof callback !== 'function') {\n      throw new AmplifyError({\n        name: NO_HUBCALLBACK_PROVIDED_EXCEPTION,\n        message: 'No callback supplied to Hub'\n      });\n    } else {\n      // Needs to be casted as a more generic type\n      cb = callback;\n    }\n    let holder = this.listeners.get(channel);\n    if (!holder) {\n      holder = [];\n      this.listeners.set(channel, holder);\n    }\n    holder.push({\n      name: listenerName,\n      callback: cb\n    });\n    return () => {\n      this._remove(channel, cb);\n    };\n  }\n  _toListeners(capsule) {\n    const {\n      channel,\n      payload\n    } = capsule;\n    const holder = this.listeners.get(channel);\n    if (holder) {\n      holder.forEach(listener => {\n        logger.debug(`Dispatching to ${channel} with `, payload);\n        try {\n          listener.callback(capsule);\n        } catch (e) {\n          logger.error(e);\n        }\n      });\n    }\n  }\n}\n/* We export a __default__ instance of HubClass to use it as a\npseudo Singleton for the main messaging bus, however you can still create\nyour own instance of HubClass() for a separate \"private bus\" of events. */\nconst Hub = new HubClass('__default__');\n/**\n * @internal\n *\n * Internal hub used for core Amplify functionality. Not intended for use outside of Amplify.\n *\n */\nconst HubInternal = new HubClass('internal-hub');\nexport { AMPLIFY_SYMBOL, Hub, HubClass, HubInternal };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NO_HUBCALLBACK_PROVIDED_EXCEPTION", "AmplifyError", "AMPLIFY_SYMBOL", "Symbol", "logger", "HubClass", "constructor", "name", "listeners", "Map", "protectedChannels", "_remove", "channel", "listener", "holder", "get", "warn", "set", "filter", "callback", "dispatch", "payload", "source", "ampSymbol", "indexOf", "hasAccess", "capsule", "patternInfo", "_toListeners", "e", "error", "listen", "listenerName", "cb", "message", "push", "for<PERSON>ach", "debug", "<PERSON><PERSON>", "HubInternal"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Hub/index.mjs"], "sourcesContent": ["import { ConsoleLogger } from '../Logger/ConsoleLogger.mjs';\nimport { NO_HUBCALLBACK_PROVIDED_EXCEPTION } from '../constants.mjs';\nimport { AmplifyError } from '../errors/AmplifyError.mjs';\nimport '../types/errors.mjs';\nimport '../errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst AMPLIFY_SYMBOL = (typeof Symbol !== 'undefined'\n    ? Symbol('amplify_default')\n    : '@@amplify_default');\nconst logger = new ConsoleLogger('Hub');\nclass HubClass {\n    constructor(name) {\n        this.listeners = new Map();\n        this.protectedChannels = [\n            'core',\n            'auth',\n            'api',\n            'analytics',\n            'interactions',\n            'pubsub',\n            'storage',\n            'ui',\n            'xr',\n        ];\n        this.name = name;\n    }\n    /**\n     * Used internally to remove a Hub listener.\n     *\n     * @remarks\n     * This private method is for internal use only. Instead of calling Hub.remove, call the result of Hub.listen.\n     */\n    _remove(channel, listener) {\n        const holder = this.listeners.get(channel);\n        if (!holder) {\n            logger.warn(`No listeners for ${channel}`);\n            return;\n        }\n        this.listeners.set(channel, [\n            ...holder.filter(({ callback }) => callback !== listener),\n        ]);\n    }\n    dispatch(channel, payload, source, ampSymbol) {\n        if (typeof channel === 'string' &&\n            this.protectedChannels.indexOf(channel) > -1) {\n            const hasAccess = ampSymbol === AMPLIFY_SYMBOL;\n            if (!hasAccess) {\n                logger.warn(`WARNING: ${channel} is protected and dispatching on it can have unintended consequences`);\n            }\n        }\n        const capsule = {\n            channel,\n            payload: { ...payload },\n            source,\n            patternInfo: [],\n        };\n        try {\n            this._toListeners(capsule);\n        }\n        catch (e) {\n            logger.error(e);\n        }\n    }\n    listen(channel, callback, listenerName = 'noname') {\n        let cb;\n        if (typeof callback !== 'function') {\n            throw new AmplifyError({\n                name: NO_HUBCALLBACK_PROVIDED_EXCEPTION,\n                message: 'No callback supplied to Hub',\n            });\n        }\n        else {\n            // Needs to be casted as a more generic type\n            cb = callback;\n        }\n        let holder = this.listeners.get(channel);\n        if (!holder) {\n            holder = [];\n            this.listeners.set(channel, holder);\n        }\n        holder.push({\n            name: listenerName,\n            callback: cb,\n        });\n        return () => {\n            this._remove(channel, cb);\n        };\n    }\n    _toListeners(capsule) {\n        const { channel, payload } = capsule;\n        const holder = this.listeners.get(channel);\n        if (holder) {\n            holder.forEach(listener => {\n                logger.debug(`Dispatching to ${channel} with `, payload);\n                try {\n                    listener.callback(capsule);\n                }\n                catch (e) {\n                    logger.error(e);\n                }\n            });\n        }\n    }\n}\n/* We export a __default__ instance of HubClass to use it as a\npseudo Singleton for the main messaging bus, however you can still create\nyour own instance of HubClass() for a separate \"private bus\" of events. */\nconst Hub = new HubClass('__default__');\n/**\n * @internal\n *\n * Internal hub used for core Amplify functionality. Not intended for use outside of Amplify.\n *\n */\nconst HubInternal = new HubClass('internal-hub');\n\nexport { AMPLIFY_SYMBOL, Hub, HubClass, HubInternal };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,iCAAiC,QAAQ,kBAAkB;AACpE,SAASC,YAAY,QAAQ,4BAA4B;AACzD,OAAO,qBAAqB;AAC5B,OAAO,4BAA4B;;AAEnC;AACA;AACA,MAAMC,cAAc,GAAI,OAAOC,MAAM,KAAK,WAAW,GAC/CA,MAAM,CAAC,iBAAiB,CAAC,GACzB,mBAAoB;AAC1B,MAAMC,MAAM,GAAG,IAAIL,aAAa,CAAC,KAAK,CAAC;AACvC,MAAMM,QAAQ,CAAC;EACXC,WAAWA,CAACC,IAAI,EAAE;IACd,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,iBAAiB,GAAG,CACrB,MAAM,EACN,MAAM,EACN,KAAK,EACL,WAAW,EACX,cAAc,EACd,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,IAAI,CACP;IACD,IAAI,CAACH,IAAI,GAAGA,IAAI;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,OAAOA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IACvB,MAAMC,MAAM,GAAG,IAAI,CAACN,SAAS,CAACO,GAAG,CAACH,OAAO,CAAC;IAC1C,IAAI,CAACE,MAAM,EAAE;MACTV,MAAM,CAACY,IAAI,CAAC,oBAAoBJ,OAAO,EAAE,CAAC;MAC1C;IACJ;IACA,IAAI,CAACJ,SAAS,CAACS,GAAG,CAACL,OAAO,EAAE,CACxB,GAAGE,MAAM,CAACI,MAAM,CAAC,CAAC;MAAEC;IAAS,CAAC,KAAKA,QAAQ,KAAKN,QAAQ,CAAC,CAC5D,CAAC;EACN;EACAO,QAAQA,CAACR,OAAO,EAAES,OAAO,EAAEC,MAAM,EAAEC,SAAS,EAAE;IAC1C,IAAI,OAAOX,OAAO,KAAK,QAAQ,IAC3B,IAAI,CAACF,iBAAiB,CAACc,OAAO,CAACZ,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MAC9C,MAAMa,SAAS,GAAGF,SAAS,KAAKrB,cAAc;MAC9C,IAAI,CAACuB,SAAS,EAAE;QACZrB,MAAM,CAACY,IAAI,CAAC,YAAYJ,OAAO,sEAAsE,CAAC;MAC1G;IACJ;IACA,MAAMc,OAAO,GAAG;MACZd,OAAO;MACPS,OAAO,EAAE;QAAE,GAAGA;MAAQ,CAAC;MACvBC,MAAM;MACNK,WAAW,EAAE;IACjB,CAAC;IACD,IAAI;MACA,IAAI,CAACC,YAAY,CAACF,OAAO,CAAC;IAC9B,CAAC,CACD,OAAOG,CAAC,EAAE;MACNzB,MAAM,CAAC0B,KAAK,CAACD,CAAC,CAAC;IACnB;EACJ;EACAE,MAAMA,CAACnB,OAAO,EAAEO,QAAQ,EAAEa,YAAY,GAAG,QAAQ,EAAE;IAC/C,IAAIC,EAAE;IACN,IAAI,OAAOd,QAAQ,KAAK,UAAU,EAAE;MAChC,MAAM,IAAIlB,YAAY,CAAC;QACnBM,IAAI,EAAEP,iCAAiC;QACvCkC,OAAO,EAAE;MACb,CAAC,CAAC;IACN,CAAC,MACI;MACD;MACAD,EAAE,GAAGd,QAAQ;IACjB;IACA,IAAIL,MAAM,GAAG,IAAI,CAACN,SAAS,CAACO,GAAG,CAACH,OAAO,CAAC;IACxC,IAAI,CAACE,MAAM,EAAE;MACTA,MAAM,GAAG,EAAE;MACX,IAAI,CAACN,SAAS,CAACS,GAAG,CAACL,OAAO,EAAEE,MAAM,CAAC;IACvC;IACAA,MAAM,CAACqB,IAAI,CAAC;MACR5B,IAAI,EAAEyB,YAAY;MAClBb,QAAQ,EAAEc;IACd,CAAC,CAAC;IACF,OAAO,MAAM;MACT,IAAI,CAACtB,OAAO,CAACC,OAAO,EAAEqB,EAAE,CAAC;IAC7B,CAAC;EACL;EACAL,YAAYA,CAACF,OAAO,EAAE;IAClB,MAAM;MAAEd,OAAO;MAAES;IAAQ,CAAC,GAAGK,OAAO;IACpC,MAAMZ,MAAM,GAAG,IAAI,CAACN,SAAS,CAACO,GAAG,CAACH,OAAO,CAAC;IAC1C,IAAIE,MAAM,EAAE;MACRA,MAAM,CAACsB,OAAO,CAACvB,QAAQ,IAAI;QACvBT,MAAM,CAACiC,KAAK,CAAC,kBAAkBzB,OAAO,QAAQ,EAAES,OAAO,CAAC;QACxD,IAAI;UACAR,QAAQ,CAACM,QAAQ,CAACO,OAAO,CAAC;QAC9B,CAAC,CACD,OAAOG,CAAC,EAAE;UACNzB,MAAM,CAAC0B,KAAK,CAACD,CAAC,CAAC;QACnB;MACJ,CAAC,CAAC;IACN;EACJ;AACJ;AACA;AACA;AACA;AACA,MAAMS,GAAG,GAAG,IAAIjC,QAAQ,CAAC,aAAa,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkC,WAAW,GAAG,IAAIlC,QAAQ,CAAC,cAAc,CAAC;AAEhD,SAASH,cAAc,EAAEoC,GAAG,EAAEjC,QAAQ,EAAEkC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}