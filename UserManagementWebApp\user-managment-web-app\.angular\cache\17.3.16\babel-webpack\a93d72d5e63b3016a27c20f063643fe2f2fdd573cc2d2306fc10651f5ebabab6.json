{"ast": null, "code": "import { Amplify } from '../Amplify.mjs';\nimport { fetchAuthSession as fetchAuthSession$1 } from './internal/fetchAuthSession.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Fetch the auth session including the tokens and credentials if they are available. By default it\n * does not refresh the auth tokens or credentials if they are loaded in storage already. You can force a refresh\n * with `{ forceRefresh: true }` input.\n *\n * @param options - Options configuring the fetch behavior.\n * @throws {@link AuthError} - Throws error when session information cannot be refreshed.\n * @returns Promise<AuthSession>\n */\nconst fetchAuthSession = options => {\n  return fetchAuthSession$1(Amplify, options);\n};\nexport { fetchAuthSession };", "map": {"version": 3, "names": ["Amplify", "fetchAuthSession", "fetchAuthSession$1", "options"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/singleton/apis/fetchAuthSession.mjs"], "sourcesContent": ["import { Amplify } from '../Amplify.mjs';\nimport { fetchAuthSession as fetchAuthSession$1 } from './internal/fetchAuthSession.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Fetch the auth session including the tokens and credentials if they are available. By default it\n * does not refresh the auth tokens or credentials if they are loaded in storage already. You can force a refresh\n * with `{ forceRefresh: true }` input.\n *\n * @param options - Options configuring the fetch behavior.\n * @throws {@link AuthError} - Throws error when session information cannot be refreshed.\n * @returns Promise<AuthSession>\n */\nconst fetchAuthSession = (options) => {\n    return fetchAuthSession$1(Amplify, options);\n};\n\nexport { fetchAuthSession };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,gBAAgB;AACxC,SAASC,gBAAgB,IAAIC,kBAAkB,QAAQ,iCAAiC;;AAExF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,gBAAgB,GAAIE,OAAO,IAAK;EAClC,OAAOD,kBAAkB,CAACF,OAAO,EAAEG,OAAO,CAAC;AAC/C,CAAC;AAED,SAASF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}