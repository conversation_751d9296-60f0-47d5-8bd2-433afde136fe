{"ast": null, "code": "export class ColDefOptions {\n  constructor(init) {\n    this.bypassObjects = true; // For actionable grids because we don't support objects now, but it is being supported in forms\n    Object.assign(this, init);\n  }\n}", "map": {"version": 3, "names": ["ColDefOptions", "constructor", "init", "bypassObjects", "Object", "assign"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\model\\col-def-options.ts"], "sourcesContent": ["import { ColDef, ColGroupDef, GridOptions } from \"ag-grid-community\";\r\nimport { ActionableGridColumnConfig } from \"src/app/core/models/actionable-grid-column-config\";\r\nimport { ExtendedJsonSchema } from \"src/app/shared/models/extended-json-schema\";\r\n\r\nexport class ColDefOptions {\r\n    public columnConfig: ActionableGridColumnConfig;\r\n    public colDef: ColDef;\r\n    public baseProperty?: ExtendedJsonSchema;\r\n    public gridOptions?: GridOptions;\r\n    public projectVariables?: any[];\r\n    public projectVersionId?: number;\r\n    public projectId?: number;\r\n    public allowAddNewRow?: boolean;\r\n    public parentColumn?: ActionableGridColumnConfig;\r\n    public baseSchema?: ExtendedJsonSchema;\r\n    public bypassObjects = true; // For actionable grids because we don't support objects now, but it is being supported in forms\r\n\r\n    constructor(init?: Partial<ColDefOptions>) {\r\n        Object.assign(this, init);\r\n    }\r\n}\r\n"], "mappings": "AAIA,OAAM,MAAOA,aAAa;EAatBC,YAAYC,IAA6B;IAFlC,KAAAC,aAAa,GAAG,IAAI,CAAC,CAAC;IAGzBC,MAAM,CAACC,MAAM,CAAC,IAAI,EAAEH,IAAI,CAAC;EAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}