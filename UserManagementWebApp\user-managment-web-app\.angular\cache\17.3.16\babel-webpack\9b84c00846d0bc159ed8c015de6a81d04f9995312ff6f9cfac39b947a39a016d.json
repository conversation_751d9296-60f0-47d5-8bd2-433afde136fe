{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst fetchAuthSession = (amplify, options) => {\n  return amplify.Auth.fetchAuthSession(options);\n};\nexport { fetchAuthSession };", "map": {"version": 3, "names": ["fetchAuthSession", "amplify", "options", "<PERSON><PERSON>"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/singleton/apis/internal/fetchAuthSession.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst fetchAuthSession = (amplify, options) => {\n    return amplify.Auth.fetchAuthSession(options);\n};\n\nexport { fetchAuthSession };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,gBAAgB,GAAGA,CAACC,OAAO,EAAEC,OAAO,KAAK;EAC3C,OAAOD,OAAO,CAACE,IAAI,CAACH,gBAAgB,CAACE,OAAO,CAAC;AACjD,CAAC;AAED,SAASF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}