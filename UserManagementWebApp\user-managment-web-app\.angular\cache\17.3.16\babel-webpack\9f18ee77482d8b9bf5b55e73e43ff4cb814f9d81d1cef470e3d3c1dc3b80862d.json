{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Hub } from '@aws-amplify/core';\nimport { isBrowser, assertTokenProviderConfig, isTokenExpired, AMPLIFY_SYMBOL, AmplifyErrorCode } from '@aws-amplify/core/internals/utils';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { oAuthStore } from '../utils/oauth/oAuthStore.mjs';\nimport { addInflightPromise } from '../utils/oauth/inflightPromise.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass TokenOrchestrator {\n  constructor() {\n    var _this = this;\n    this.waitForInflightOAuth = isBrowser() ? /*#__PURE__*/_asyncToGenerator(function* () {\n      if (!(yield oAuthStore.loadOAuthInFlight())) {\n        return;\n      }\n      if (_this.inflightPromise) {\n        return _this.inflightPromise;\n      }\n      // when there is valid oauth config and there is an inflight oauth flow, try\n      // to block async calls that require fetching tokens before the oauth flow completes\n      // e.g. getCurrentUser, fetchAuthSession etc.\n      _this.inflightPromise = new Promise((resolve, _reject) => {\n        addInflightPromise(resolve);\n      });\n      return _this.inflightPromise;\n    }) : /*#__PURE__*/_asyncToGenerator(function* () {\n      // no-op for non-browser environments\n    });\n  }\n  setAuthConfig(authConfig) {\n    oAuthStore.setAuthConfig(authConfig.Cognito);\n    this.authConfig = authConfig;\n  }\n  setTokenRefresher(tokenRefresher) {\n    this.tokenRefresher = tokenRefresher;\n  }\n  setAuthTokenStore(tokenStore) {\n    this.tokenStore = tokenStore;\n  }\n  getTokenStore() {\n    if (!this.tokenStore) {\n      throw new AuthError({\n        name: 'EmptyTokenStoreException',\n        message: 'TokenStore not set'\n      });\n    }\n    return this.tokenStore;\n  }\n  getTokenRefresher() {\n    if (!this.tokenRefresher) {\n      throw new AuthError({\n        name: 'EmptyTokenRefresherException',\n        message: 'TokenRefresher not set'\n      });\n    }\n    return this.tokenRefresher;\n  }\n  getTokens(options) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      let tokens;\n      try {\n        assertTokenProviderConfig(_this2.authConfig?.Cognito);\n      } catch (_err) {\n        // Token provider not configured\n        return null;\n      }\n      yield _this2.waitForInflightOAuth();\n      _this2.inflightPromise = undefined;\n      tokens = yield _this2.getTokenStore().loadTokens();\n      const username = yield _this2.getTokenStore().getLastAuthUser();\n      if (tokens === null) {\n        return null;\n      }\n      const idTokenExpired = !!tokens?.idToken && isTokenExpired({\n        expiresAt: (tokens.idToken?.payload?.exp ?? 0) * 1000,\n        clockDrift: tokens.clockDrift ?? 0\n      });\n      const accessTokenExpired = isTokenExpired({\n        expiresAt: (tokens.accessToken?.payload?.exp ?? 0) * 1000,\n        clockDrift: tokens.clockDrift ?? 0\n      });\n      if (options?.forceRefresh || idTokenExpired || accessTokenExpired) {\n        tokens = yield _this2.refreshTokens({\n          tokens,\n          username\n        });\n        if (tokens === null) {\n          return null;\n        }\n      }\n      return {\n        accessToken: tokens?.accessToken,\n        idToken: tokens?.idToken,\n        signInDetails: tokens?.signInDetails\n      };\n    })();\n  }\n  refreshTokens({\n    tokens,\n    username\n  }) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const {\n          signInDetails\n        } = tokens;\n        const newTokens = yield _this3.getTokenRefresher()({\n          tokens,\n          authConfig: _this3.authConfig,\n          username\n        });\n        newTokens.signInDetails = signInDetails;\n        yield _this3.setTokens({\n          tokens: newTokens\n        });\n        Hub.dispatch('auth', {\n          event: 'tokenRefresh'\n        }, 'Auth', AMPLIFY_SYMBOL);\n        return newTokens;\n      } catch (err) {\n        return _this3.handleErrors(err);\n      }\n    })();\n  }\n  handleErrors(err) {\n    assertServiceError(err);\n    if (err.name !== AmplifyErrorCode.NetworkError) {\n      // TODO(v6): Check errors on client\n      this.clearTokens();\n    }\n    Hub.dispatch('auth', {\n      event: 'tokenRefresh_failure',\n      data: {\n        error: err\n      }\n    }, 'Auth', AMPLIFY_SYMBOL);\n    if (err.name.startsWith('NotAuthorizedException')) {\n      return null;\n    }\n    throw err;\n  }\n  setTokens({\n    tokens\n  }) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      return _this4.getTokenStore().storeTokens(tokens);\n    })();\n  }\n  clearTokens() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      return _this5.getTokenStore().clearTokens();\n    })();\n  }\n  getDeviceMetadata(username) {\n    return this.getTokenStore().getDeviceMetadata(username);\n  }\n  clearDeviceMetadata(username) {\n    return this.getTokenStore().clearDeviceMetadata(username);\n  }\n  setOAuthMetadata(metadata) {\n    return this.getTokenStore().setOAuthMetadata(metadata);\n  }\n  getOAuthMetadata() {\n    return this.getTokenStore().getOAuthMetadata();\n  }\n}\nexport { TokenOrchestrator };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "assertTokenProviderConfig", "isTokenExpired", "AMPLIFY_SYMBOL", "AmplifyErrorCode", "assertServiceError", "<PERSON>th<PERSON><PERSON><PERSON>", "oAuthStore", "addInflightPromise", "TokenOrchestrator", "constructor", "_this", "waitForInflightOAuth", "_asyncToGenerator", "loadOAuthInFlight", "inflightPromise", "Promise", "resolve", "_reject", "setAuthConfig", "authConfig", "Cognito", "setTokenRefresher", "tokenRefresher", "setAuthTokenStore", "tokenStore", "getTokenStore", "name", "message", "getTokenRefresher", "getTokens", "options", "_this2", "tokens", "_err", "undefined", "loadTokens", "username", "getLastAuthUser", "idTokenExpired", "idToken", "expiresAt", "payload", "exp", "clockDrift", "accessTokenExpired", "accessToken", "forceRefresh", "refreshTokens", "signInDetails", "_this3", "newTokens", "setTokens", "dispatch", "event", "err", "handleErrors", "NetworkError", "clearTokens", "data", "error", "startsWith", "_this4", "storeTokens", "_this5", "getDeviceMetadata", "clearDeviceMetadata", "setOAuthMetadata", "metadata", "getOAuthMetadata"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/tokenProvider/TokenOrchestrator.mjs"], "sourcesContent": ["import { Hub } from '@aws-amplify/core';\nimport { isBrowser, assertTokenProviderConfig, isTokenExpired, AMPLIFY_SYMBOL, AmplifyErrorCode } from '@aws-amplify/core/internals/utils';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { oAuthStore } from '../utils/oauth/oAuthStore.mjs';\nimport { addInflightPromise } from '../utils/oauth/inflightPromise.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass TokenOrchestrator {\n    constructor() {\n        this.waitForInflightOAuth = isBrowser()\n            ? async () => {\n                if (!(await oAuthStore.loadOAuthInFlight())) {\n                    return;\n                }\n                if (this.inflightPromise) {\n                    return this.inflightPromise;\n                }\n                // when there is valid oauth config and there is an inflight oauth flow, try\n                // to block async calls that require fetching tokens before the oauth flow completes\n                // e.g. getCurrentUser, fetchAuthSession etc.\n                this.inflightPromise = new Promise((resolve, _reject) => {\n                    addInflightPromise(resolve);\n                });\n                return this.inflightPromise;\n            }\n            : async () => {\n                // no-op for non-browser environments\n            };\n    }\n    setAuthConfig(authConfig) {\n        oAuthStore.setAuthConfig(authConfig.Cognito);\n        this.authConfig = authConfig;\n    }\n    setTokenRefresher(tokenRefresher) {\n        this.tokenRefresher = tokenRefresher;\n    }\n    setAuthTokenStore(tokenStore) {\n        this.tokenStore = tokenStore;\n    }\n    getTokenStore() {\n        if (!this.tokenStore) {\n            throw new AuthError({\n                name: 'EmptyTokenStoreException',\n                message: 'TokenStore not set',\n            });\n        }\n        return this.tokenStore;\n    }\n    getTokenRefresher() {\n        if (!this.tokenRefresher) {\n            throw new AuthError({\n                name: 'EmptyTokenRefresherException',\n                message: 'TokenRefresher not set',\n            });\n        }\n        return this.tokenRefresher;\n    }\n    async getTokens(options) {\n        let tokens;\n        try {\n            assertTokenProviderConfig(this.authConfig?.Cognito);\n        }\n        catch (_err) {\n            // Token provider not configured\n            return null;\n        }\n        await this.waitForInflightOAuth();\n        this.inflightPromise = undefined;\n        tokens = await this.getTokenStore().loadTokens();\n        const username = await this.getTokenStore().getLastAuthUser();\n        if (tokens === null) {\n            return null;\n        }\n        const idTokenExpired = !!tokens?.idToken &&\n            isTokenExpired({\n                expiresAt: (tokens.idToken?.payload?.exp ?? 0) * 1000,\n                clockDrift: tokens.clockDrift ?? 0,\n            });\n        const accessTokenExpired = isTokenExpired({\n            expiresAt: (tokens.accessToken?.payload?.exp ?? 0) * 1000,\n            clockDrift: tokens.clockDrift ?? 0,\n        });\n        if (options?.forceRefresh || idTokenExpired || accessTokenExpired) {\n            tokens = await this.refreshTokens({\n                tokens,\n                username,\n            });\n            if (tokens === null) {\n                return null;\n            }\n        }\n        return {\n            accessToken: tokens?.accessToken,\n            idToken: tokens?.idToken,\n            signInDetails: tokens?.signInDetails,\n        };\n    }\n    async refreshTokens({ tokens, username, }) {\n        try {\n            const { signInDetails } = tokens;\n            const newTokens = await this.getTokenRefresher()({\n                tokens,\n                authConfig: this.authConfig,\n                username,\n            });\n            newTokens.signInDetails = signInDetails;\n            await this.setTokens({ tokens: newTokens });\n            Hub.dispatch('auth', { event: 'tokenRefresh' }, 'Auth', AMPLIFY_SYMBOL);\n            return newTokens;\n        }\n        catch (err) {\n            return this.handleErrors(err);\n        }\n    }\n    handleErrors(err) {\n        assertServiceError(err);\n        if (err.name !== AmplifyErrorCode.NetworkError) {\n            // TODO(v6): Check errors on client\n            this.clearTokens();\n        }\n        Hub.dispatch('auth', {\n            event: 'tokenRefresh_failure',\n            data: { error: err },\n        }, 'Auth', AMPLIFY_SYMBOL);\n        if (err.name.startsWith('NotAuthorizedException')) {\n            return null;\n        }\n        throw err;\n    }\n    async setTokens({ tokens }) {\n        return this.getTokenStore().storeTokens(tokens);\n    }\n    async clearTokens() {\n        return this.getTokenStore().clearTokens();\n    }\n    getDeviceMetadata(username) {\n        return this.getTokenStore().getDeviceMetadata(username);\n    }\n    clearDeviceMetadata(username) {\n        return this.getTokenStore().clearDeviceMetadata(username);\n    }\n    setOAuthMetadata(metadata) {\n        return this.getTokenStore().setOAuthMetadata(metadata);\n    }\n    getOAuthMetadata() {\n        return this.getTokenStore().getOAuthMetadata();\n    }\n}\n\nexport { TokenOrchestrator };\n"], "mappings": ";AAAA,SAASA,GAAG,QAAQ,mBAAmB;AACvC,SAASC,SAAS,EAAEC,yBAAyB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,mCAAmC;AAC1I,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,kBAAkB,QAAQ,oCAAoC;;AAEvE;AACA;AACA,MAAMC,iBAAiB,CAAC;EACpBC,WAAWA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACV,IAAI,CAACC,oBAAoB,GAAGZ,SAAS,CAAC,CAAC,gBAAAa,iBAAA,CACjC,aAAY;MACV,IAAI,QAAQN,UAAU,CAACO,iBAAiB,CAAC,CAAC,CAAC,EAAE;QACzC;MACJ;MACA,IAAIH,KAAI,CAACI,eAAe,EAAE;QACtB,OAAOJ,KAAI,CAACI,eAAe;MAC/B;MACA;MACA;MACA;MACAJ,KAAI,CAACI,eAAe,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,OAAO,KAAK;QACrDV,kBAAkB,CAACS,OAAO,CAAC;MAC/B,CAAC,CAAC;MACF,OAAON,KAAI,CAACI,eAAe;IAC/B,CAAC,iBAAAF,iBAAA,CACC,aAAY;MACV;IAAA,CACH;EACT;EACAM,aAAaA,CAACC,UAAU,EAAE;IACtBb,UAAU,CAACY,aAAa,CAACC,UAAU,CAACC,OAAO,CAAC;IAC5C,IAAI,CAACD,UAAU,GAAGA,UAAU;EAChC;EACAE,iBAAiBA,CAACC,cAAc,EAAE;IAC9B,IAAI,CAACA,cAAc,GAAGA,cAAc;EACxC;EACAC,iBAAiBA,CAACC,UAAU,EAAE;IAC1B,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACAC,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACD,UAAU,EAAE;MAClB,MAAM,IAAInB,SAAS,CAAC;QAChBqB,IAAI,EAAE,0BAA0B;QAChCC,OAAO,EAAE;MACb,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAACH,UAAU;EAC1B;EACAI,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACN,cAAc,EAAE;MACtB,MAAM,IAAIjB,SAAS,CAAC;QAChBqB,IAAI,EAAE,8BAA8B;QACpCC,OAAO,EAAE;MACb,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAACL,cAAc;EAC9B;EACMO,SAASA,CAACC,OAAO,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAnB,iBAAA;MACrB,IAAIoB,MAAM;MACV,IAAI;QACAhC,yBAAyB,CAAC+B,MAAI,CAACZ,UAAU,EAAEC,OAAO,CAAC;MACvD,CAAC,CACD,OAAOa,IAAI,EAAE;QACT;QACA,OAAO,IAAI;MACf;MACA,MAAMF,MAAI,CAACpB,oBAAoB,CAAC,CAAC;MACjCoB,MAAI,CAACjB,eAAe,GAAGoB,SAAS;MAChCF,MAAM,SAASD,MAAI,CAACN,aAAa,CAAC,CAAC,CAACU,UAAU,CAAC,CAAC;MAChD,MAAMC,QAAQ,SAASL,MAAI,CAACN,aAAa,CAAC,CAAC,CAACY,eAAe,CAAC,CAAC;MAC7D,IAAIL,MAAM,KAAK,IAAI,EAAE;QACjB,OAAO,IAAI;MACf;MACA,MAAMM,cAAc,GAAG,CAAC,CAACN,MAAM,EAAEO,OAAO,IACpCtC,cAAc,CAAC;QACXuC,SAAS,EAAE,CAACR,MAAM,CAACO,OAAO,EAAEE,OAAO,EAAEC,GAAG,IAAI,CAAC,IAAI,IAAI;QACrDC,UAAU,EAAEX,MAAM,CAACW,UAAU,IAAI;MACrC,CAAC,CAAC;MACN,MAAMC,kBAAkB,GAAG3C,cAAc,CAAC;QACtCuC,SAAS,EAAE,CAACR,MAAM,CAACa,WAAW,EAAEJ,OAAO,EAAEC,GAAG,IAAI,CAAC,IAAI,IAAI;QACzDC,UAAU,EAAEX,MAAM,CAACW,UAAU,IAAI;MACrC,CAAC,CAAC;MACF,IAAIb,OAAO,EAAEgB,YAAY,IAAIR,cAAc,IAAIM,kBAAkB,EAAE;QAC/DZ,MAAM,SAASD,MAAI,CAACgB,aAAa,CAAC;UAC9Bf,MAAM;UACNI;QACJ,CAAC,CAAC;QACF,IAAIJ,MAAM,KAAK,IAAI,EAAE;UACjB,OAAO,IAAI;QACf;MACJ;MACA,OAAO;QACHa,WAAW,EAAEb,MAAM,EAAEa,WAAW;QAChCN,OAAO,EAAEP,MAAM,EAAEO,OAAO;QACxBS,aAAa,EAAEhB,MAAM,EAAEgB;MAC3B,CAAC;IAAC;EACN;EACMD,aAAaA,CAAC;IAAEf,MAAM;IAAEI;EAAU,CAAC,EAAE;IAAA,IAAAa,MAAA;IAAA,OAAArC,iBAAA;MACvC,IAAI;QACA,MAAM;UAAEoC;QAAc,CAAC,GAAGhB,MAAM;QAChC,MAAMkB,SAAS,SAASD,MAAI,CAACrB,iBAAiB,CAAC,CAAC,CAAC;UAC7CI,MAAM;UACNb,UAAU,EAAE8B,MAAI,CAAC9B,UAAU;UAC3BiB;QACJ,CAAC,CAAC;QACFc,SAAS,CAACF,aAAa,GAAGA,aAAa;QACvC,MAAMC,MAAI,CAACE,SAAS,CAAC;UAAEnB,MAAM,EAAEkB;QAAU,CAAC,CAAC;QAC3CpD,GAAG,CAACsD,QAAQ,CAAC,MAAM,EAAE;UAAEC,KAAK,EAAE;QAAe,CAAC,EAAE,MAAM,EAAEnD,cAAc,CAAC;QACvE,OAAOgD,SAAS;MACpB,CAAC,CACD,OAAOI,GAAG,EAAE;QACR,OAAOL,MAAI,CAACM,YAAY,CAACD,GAAG,CAAC;MACjC;IAAC;EACL;EACAC,YAAYA,CAACD,GAAG,EAAE;IACdlD,kBAAkB,CAACkD,GAAG,CAAC;IACvB,IAAIA,GAAG,CAAC5B,IAAI,KAAKvB,gBAAgB,CAACqD,YAAY,EAAE;MAC5C;MACA,IAAI,CAACC,WAAW,CAAC,CAAC;IACtB;IACA3D,GAAG,CAACsD,QAAQ,CAAC,MAAM,EAAE;MACjBC,KAAK,EAAE,sBAAsB;MAC7BK,IAAI,EAAE;QAAEC,KAAK,EAAEL;MAAI;IACvB,CAAC,EAAE,MAAM,EAAEpD,cAAc,CAAC;IAC1B,IAAIoD,GAAG,CAAC5B,IAAI,CAACkC,UAAU,CAAC,wBAAwB,CAAC,EAAE;MAC/C,OAAO,IAAI;IACf;IACA,MAAMN,GAAG;EACb;EACMH,SAASA,CAAC;IAAEnB;EAAO,CAAC,EAAE;IAAA,IAAA6B,MAAA;IAAA,OAAAjD,iBAAA;MACxB,OAAOiD,MAAI,CAACpC,aAAa,CAAC,CAAC,CAACqC,WAAW,CAAC9B,MAAM,CAAC;IAAC;EACpD;EACMyB,WAAWA,CAAA,EAAG;IAAA,IAAAM,MAAA;IAAA,OAAAnD,iBAAA;MAChB,OAAOmD,MAAI,CAACtC,aAAa,CAAC,CAAC,CAACgC,WAAW,CAAC,CAAC;IAAC;EAC9C;EACAO,iBAAiBA,CAAC5B,QAAQ,EAAE;IACxB,OAAO,IAAI,CAACX,aAAa,CAAC,CAAC,CAACuC,iBAAiB,CAAC5B,QAAQ,CAAC;EAC3D;EACA6B,mBAAmBA,CAAC7B,QAAQ,EAAE;IAC1B,OAAO,IAAI,CAACX,aAAa,CAAC,CAAC,CAACwC,mBAAmB,CAAC7B,QAAQ,CAAC;EAC7D;EACA8B,gBAAgBA,CAACC,QAAQ,EAAE;IACvB,OAAO,IAAI,CAAC1C,aAAa,CAAC,CAAC,CAACyC,gBAAgB,CAACC,QAAQ,CAAC;EAC1D;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC3C,aAAa,CAAC,CAAC,CAAC2C,gBAAgB,CAAC,CAAC;EAClD;AACJ;AAEA,SAAS5D,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}