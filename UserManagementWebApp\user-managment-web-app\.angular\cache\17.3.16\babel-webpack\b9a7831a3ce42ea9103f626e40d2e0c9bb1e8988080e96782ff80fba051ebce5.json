{"ast": null, "code": "export { Hub } from './Hub/index.mjs';\nexport { decodeJWT } from './singleton/Auth/utils/index.mjs';\nexport { Amplify, AmplifyClass as AmplifyClassV6 } from './singleton/Amplify.mjs';\nexport { fetchAuthSession } from './singleton/apis/fetchAuthSession.mjs';\nexport { clearCredentials } from './singleton/apis/clearCredentials.mjs';\nexport { getId } from './awsClients/cognitoIdentity/getId.mjs';\nexport { getCredentialsForIdentity } from './awsClients/cognitoIdentity/getCredentialsForIdentity.mjs';\nexport { defaultStorage, sessionStorage, sharedInMemoryStorage, syncSessionStorage } from './storage/index.mjs';\nexport { Cache } from './Cache/index.mjs';\nexport { I18n } from './I18n/index.mjs';\nexport { ConsoleLogger } from './Logger/ConsoleLogger.mjs';\nexport { ServiceWorkerClass as ServiceWorker } from './ServiceWorker/ServiceWorker.mjs';\nexport { CookieStorage } from './storage/CookieStorage.mjs';", "map": {"version": 3, "names": ["<PERSON><PERSON>", "decodeJWT", "Amplify", "AmplifyClass", "AmplifyClassV6", "fetchAuthSession", "clearCredentials", "getId", "getCredentialsForIdentity", "defaultStorage", "sessionStorage", "sharedInMemoryStorage", "syncSessionStorage", "<PERSON><PERSON>", "I18n", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ServiceWorkerClass", "ServiceWorker", "Cookie<PERSON>torage"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/index.mjs"], "sourcesContent": ["export { Hub } from './Hub/index.mjs';\nexport { decodeJWT } from './singleton/Auth/utils/index.mjs';\nexport { Amplify, AmplifyClass as AmplifyClassV6 } from './singleton/Amplify.mjs';\nexport { fetchAuthSession } from './singleton/apis/fetchAuthSession.mjs';\nexport { clearCredentials } from './singleton/apis/clearCredentials.mjs';\nexport { getId } from './awsClients/cognitoIdentity/getId.mjs';\nexport { getCredentialsForIdentity } from './awsClients/cognitoIdentity/getCredentialsForIdentity.mjs';\nexport { defaultStorage, sessionStorage, sharedInMemoryStorage, syncSessionStorage } from './storage/index.mjs';\nexport { Cache } from './Cache/index.mjs';\nexport { I18n } from './I18n/index.mjs';\nexport { ConsoleLogger } from './Logger/ConsoleLogger.mjs';\nexport { ServiceWorkerClass as ServiceWorker } from './ServiceWorker/ServiceWorker.mjs';\nexport { CookieStorage } from './storage/CookieStorage.mjs';\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,iBAAiB;AACrC,SAASC,SAAS,QAAQ,kCAAkC;AAC5D,SAASC,OAAO,EAAEC,YAAY,IAAIC,cAAc,QAAQ,yBAAyB;AACjF,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,KAAK,QAAQ,wCAAwC;AAC9D,SAASC,yBAAyB,QAAQ,4DAA4D;AACtG,SAASC,cAAc,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,kBAAkB,QAAQ,qBAAqB;AAC/G,SAASC,KAAK,QAAQ,mBAAmB;AACzC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,kBAAkB,IAAIC,aAAa,QAAQ,mCAAmC;AACvF,SAASC,aAAa,QAAQ,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}