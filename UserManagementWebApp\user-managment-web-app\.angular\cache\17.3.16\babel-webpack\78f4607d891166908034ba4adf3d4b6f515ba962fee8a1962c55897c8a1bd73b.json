{"ast": null, "code": "import '../../types/authenticator/user.mjs';\nimport { authFieldsWithDefaults } from '../../types/authenticator/attributes.mjs';\nconst getFormDataFromEvent = event => {\n  const formData = new FormData(event.target);\n  return Object.fromEntries(formData);\n};\nconst setFormOrder = (formOverrides, fieldNames) => {\n  let orderedKeys = [];\n  if (formOverrides) {\n    orderedKeys = Object.keys(formOverrides).reduce((prev, key) => {\n      // reduce to array that can be sorted\n      prev.push([key, formOverrides[key]?.order]);\n      return prev;\n    }, []).sort((a, b) =>\n    //sort them based on order\n    a[1] - b[1]) // returned just key\n    .filter(a => a[1] !== undefined).map(a => a[0]);\n  }\n  // remove duplicates\n  return Array.from(new Set([...orderedKeys, ...fieldNames]));\n};\nconst isAuthFieldWithDefaults = field => {\n  return authFieldsWithDefaults.includes(field);\n};\nconst isArray = val => {\n  return Array.isArray(val);\n};\nconst getErrors = errors => {\n  if (!errors) return null;\n  if (isArray(errors)) {\n    return errors;\n  } else {\n    return [errors];\n  }\n};\nexport { getErrors, getFormDataFromEvent, isAuthFieldWithDefaults, setFormOrder };", "map": {"version": 3, "names": ["authFieldsWithDefaults", "getFormDataFromEvent", "event", "formData", "FormData", "target", "Object", "fromEntries", "setFormOrder", "formOverrides", "fieldNames", "orderedKeys", "keys", "reduce", "prev", "key", "push", "order", "sort", "a", "b", "filter", "undefined", "map", "Array", "from", "Set", "isAuthFieldWithDefaults", "field", "includes", "isArray", "val", "getErrors", "errors"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/helpers/authenticator/form.mjs"], "sourcesContent": ["import '../../types/authenticator/user.mjs';\nimport { authFieldsWithDefaults } from '../../types/authenticator/attributes.mjs';\n\nconst getFormDataFromEvent = (event) => {\n    const formData = new FormData(event.target);\n    return Object.fromEntries(formData);\n};\nconst setFormOrder = (formOverrides, fieldNames) => {\n    let orderedKeys = [];\n    if (formOverrides) {\n        orderedKeys = Object.keys(formOverrides)\n            .reduce((prev, key) => {\n            // reduce to array that can be sorted\n            prev.push([key, formOverrides[key]?.order]);\n            return prev;\n        }, [])\n            .sort((a, b) => \n        //sort them based on order\n        a[1] - b[1]) // returned just key\n            .filter((a) => a[1] !== undefined)\n            .map((a) => a[0]);\n    }\n    // remove duplicates\n    return Array.from(new Set([...orderedKeys, ...fieldNames]));\n};\nconst isAuthFieldWithDefaults = (field) => {\n    return authFieldsWithDefaults.includes(field);\n};\nconst isArray = (val) => {\n    return Array.isArray(val);\n};\nconst getErrors = (errors) => {\n    if (!errors)\n        return null;\n    if (isArray(errors)) {\n        return errors;\n    }\n    else {\n        return [errors];\n    }\n};\n\nexport { getErrors, getFormDataFromEvent, isAuthFieldWithDefaults, setFormOrder };\n"], "mappings": "AAAA,OAAO,oCAAoC;AAC3C,SAASA,sBAAsB,QAAQ,0CAA0C;AAEjF,MAAMC,oBAAoB,GAAIC,KAAK,IAAK;EACpC,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC;EAC3C,OAAOC,MAAM,CAACC,WAAW,CAACJ,QAAQ,CAAC;AACvC,CAAC;AACD,MAAMK,YAAY,GAAGA,CAACC,aAAa,EAAEC,UAAU,KAAK;EAChD,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAIF,aAAa,EAAE;IACfE,WAAW,GAAGL,MAAM,CAACM,IAAI,CAACH,aAAa,CAAC,CACnCI,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;MACvB;MACAD,IAAI,CAACE,IAAI,CAAC,CAACD,GAAG,EAAEN,aAAa,CAACM,GAAG,CAAC,EAAEE,KAAK,CAAC,CAAC;MAC3C,OAAOH,IAAI;IACf,CAAC,EAAE,EAAE,CAAC,CACDI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC;IACf;IACAD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAAA,CACRC,MAAM,CAAEF,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,KAAKG,SAAS,CAAC,CACjCC,GAAG,CAAEJ,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB;EACA;EACA,OAAOK,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,CAAC,GAAGf,WAAW,EAAE,GAAGD,UAAU,CAAC,CAAC,CAAC;AAC/D,CAAC;AACD,MAAMiB,uBAAuB,GAAIC,KAAK,IAAK;EACvC,OAAO5B,sBAAsB,CAAC6B,QAAQ,CAACD,KAAK,CAAC;AACjD,CAAC;AACD,MAAME,OAAO,GAAIC,GAAG,IAAK;EACrB,OAAOP,KAAK,CAACM,OAAO,CAACC,GAAG,CAAC;AAC7B,CAAC;AACD,MAAMC,SAAS,GAAIC,MAAM,IAAK;EAC1B,IAAI,CAACA,MAAM,EACP,OAAO,IAAI;EACf,IAAIH,OAAO,CAACG,MAAM,CAAC,EAAE;IACjB,OAAOA,MAAM;EACjB,CAAC,MACI;IACD,OAAO,CAACA,MAAM,CAAC;EACnB;AACJ,CAAC;AAED,SAASD,SAAS,EAAE/B,oBAAoB,EAAE0B,uBAAuB,EAAEnB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}