{"ast": null, "code": "import { recursiveComponentCSS } from './createComponentCSS.mjs';\nfunction createGlobalCSS(css) {\n  let cssText = ``;\n  for (const [selector, styles] of Object.entries(css)) {\n    cssText += recursiveComponentCSS(selector, styles);\n  }\n  return cssText;\n}\nexport { createGlobalCSS };", "map": {"version": 3, "names": ["recursiveComponentCSS", "createGlobalCSS", "css", "cssText", "selector", "styles", "Object", "entries"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/createTheme/createGlobalCSS.mjs"], "sourcesContent": ["import { recursiveComponentCSS } from './createComponentCSS.mjs';\n\nfunction createGlobalCSS(css) {\n    let cssText = ``;\n    for (const [selector, styles] of Object.entries(css)) {\n        cssText += recursiveComponentCSS(selector, styles);\n    }\n    return cssText;\n}\n\nexport { createGlobalCSS };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,0BAA0B;AAEhE,SAASC,eAAeA,CAACC,GAAG,EAAE;EAC1B,IAAIC,OAAO,GAAG,EAAE;EAChB,KAAK,MAAM,CAACC,QAAQ,EAAEC,MAAM,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACL,GAAG,CAAC,EAAE;IAClDC,OAAO,IAAIH,qBAAqB,CAACI,QAAQ,EAAEC,MAAM,CAAC;EACtD;EACA,OAAOF,OAAO;AAClB;AAEA,SAASF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}