{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { getId } from '@aws-amplify/core';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { getRegionFromIdentityPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { formLoginsMap } from './utils.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Provides a Cognito identityId\n *\n * @param tokens - The AuthTokens received after SignIn\n * @returns string\n * @throws configuration exceptions: `InvalidIdentityPoolIdException`\n *  - Auth errors that may arise from misconfiguration.\n * @throws service exceptions: {@link GetIdException }\n */\nfunction cognitoIdentityIdProvider(_x) {\n  return _cognitoIdentityIdProvider.apply(this, arguments);\n}\nfunction _cognitoIdentityIdProvider() {\n  _cognitoIdentityIdProvider = _asyncToGenerator(function* ({\n    tokens,\n    authConfig,\n    identityIdStore\n  }) {\n    identityIdStore.setAuthConfig({\n      Cognito: authConfig\n    });\n    // will return null only if there is no identityId cached or if there is an error retrieving it\n    const identityId = yield identityIdStore.loadIdentityId();\n    if (identityId) {\n      return identityId.id;\n    }\n    const logins = tokens?.idToken ? formLoginsMap(tokens.idToken.toString()) : {};\n    const generatedIdentityId = yield generateIdentityId(logins, authConfig);\n    // Store generated identityId\n    identityIdStore.storeIdentityId({\n      id: generatedIdentityId,\n      type: tokens ? 'primary' : 'guest'\n    });\n    return generatedIdentityId;\n  });\n  return _cognitoIdentityIdProvider.apply(this, arguments);\n}\nfunction generateIdentityId(_x2, _x3) {\n  return _generateIdentityId.apply(this, arguments);\n}\nfunction _generateIdentityId() {\n  _generateIdentityId = _asyncToGenerator(function* (logins, authConfig) {\n    const identityPoolId = authConfig?.identityPoolId;\n    const region = getRegionFromIdentityPoolId(identityPoolId);\n    // IdentityId is absent so get it using IdentityPoolId with Cognito's GetId API\n    let idResult;\n    // for a first-time user, this will return a brand new identity\n    // for a returning user, this will retrieve the previous identity assocaited with the logins\n    try {\n      idResult = (yield getId({\n        region\n      }, {\n        IdentityPoolId: identityPoolId,\n        Logins: logins\n      })).IdentityId;\n    } catch (e) {\n      assertServiceError(e);\n      throw new AuthError(e);\n    }\n    if (!idResult) {\n      throw new AuthError({\n        name: 'GetIdResponseException',\n        message: 'Received undefined response from getId operation',\n        recoverySuggestion: 'Make sure to pass a valid identityPoolId in the configuration.'\n      });\n    }\n    return idResult;\n  });\n  return _generateIdentityId.apply(this, arguments);\n}\nexport { cognitoIdentityIdProvider };", "map": {"version": 3, "names": ["getId", "<PERSON>th<PERSON><PERSON><PERSON>", "assertServiceError", "getRegionFromIdentityPoolId", "formLoginsMap", "cognitoIdentityIdProvider", "_x", "_cognitoIdentityIdProvider", "apply", "arguments", "_asyncToGenerator", "tokens", "authConfig", "identityIdStore", "setAuthConfig", "Cognito", "identityId", "loadIdentityId", "id", "logins", "idToken", "toString", "generatedIdentityId", "generateIdentityId", "storeIdentityId", "type", "_x2", "_x3", "_generateIdentityId", "identityPoolId", "region", "idResult", "IdentityPoolId", "<PERSON><PERSON>", "IdentityId", "e", "name", "message", "recoverySuggestion"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/credentialsProvider/IdentityIdProvider.mjs"], "sourcesContent": ["import { getId } from '@aws-amplify/core';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { getRegionFromIdentityPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { formLoginsMap } from './utils.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Provides a Cognito identityId\n *\n * @param tokens - The AuthTokens received after SignIn\n * @returns string\n * @throws configuration exceptions: `InvalidIdentityPoolIdException`\n *  - Auth errors that may arise from misconfiguration.\n * @throws service exceptions: {@link GetIdException }\n */\nasync function cognitoIdentityIdProvider({ tokens, authConfig, identityIdStore, }) {\n    identityIdStore.setAuthConfig({ Cognito: authConfig });\n    // will return null only if there is no identityId cached or if there is an error retrieving it\n    const identityId = await identityIdStore.loadIdentityId();\n    if (identityId) {\n        return identityId.id;\n    }\n    const logins = tokens?.idToken\n        ? formLoginsMap(tokens.idToken.toString())\n        : {};\n    const generatedIdentityId = await generateIdentityId(logins, authConfig);\n    // Store generated identityId\n    identityIdStore.storeIdentityId({\n        id: generatedIdentityId,\n        type: tokens ? 'primary' : 'guest',\n    });\n    return generatedIdentityId;\n}\nasync function generateIdentityId(logins, authConfig) {\n    const identityPoolId = authConfig?.identityPoolId;\n    const region = getRegionFromIdentityPoolId(identityPoolId);\n    // IdentityId is absent so get it using IdentityPoolId with Cognito's GetId API\n    let idResult;\n    // for a first-time user, this will return a brand new identity\n    // for a returning user, this will retrieve the previous identity assocaited with the logins\n    try {\n        idResult = (await getId({\n            region,\n        }, {\n            IdentityPoolId: identityPoolId,\n            Logins: logins,\n        })).IdentityId;\n    }\n    catch (e) {\n        assertServiceError(e);\n        throw new AuthError(e);\n    }\n    if (!idResult) {\n        throw new AuthError({\n            name: 'GetIdResponseException',\n            message: 'Received undefined response from getId operation',\n            recoverySuggestion: 'Make sure to pass a valid identityPoolId in the configuration.',\n        });\n    }\n    return idResult;\n}\n\nexport { cognitoIdentityIdProvider };\n"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,mBAAmB;AACzC,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,2BAA2B,QAAQ,+CAA+C;AAC3F,SAASC,aAAa,QAAQ,aAAa;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SASeC,yBAAyBA,CAAAC,EAAA;EAAA,OAAAC,0BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,2BAAA;EAAAA,0BAAA,GAAAG,iBAAA,CAAxC,WAAyC;IAAEC,MAAM;IAAEC,UAAU;IAAEC;EAAiB,CAAC,EAAE;IAC/EA,eAAe,CAACC,aAAa,CAAC;MAAEC,OAAO,EAAEH;IAAW,CAAC,CAAC;IACtD;IACA,MAAMI,UAAU,SAASH,eAAe,CAACI,cAAc,CAAC,CAAC;IACzD,IAAID,UAAU,EAAE;MACZ,OAAOA,UAAU,CAACE,EAAE;IACxB;IACA,MAAMC,MAAM,GAAGR,MAAM,EAAES,OAAO,GACxBhB,aAAa,CAACO,MAAM,CAACS,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC,GACxC,CAAC,CAAC;IACR,MAAMC,mBAAmB,SAASC,kBAAkB,CAACJ,MAAM,EAAEP,UAAU,CAAC;IACxE;IACAC,eAAe,CAACW,eAAe,CAAC;MAC5BN,EAAE,EAAEI,mBAAmB;MACvBG,IAAI,EAAEd,MAAM,GAAG,SAAS,GAAG;IAC/B,CAAC,CAAC;IACF,OAAOW,mBAAmB;EAC9B,CAAC;EAAA,OAAAf,0BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SACcc,kBAAkBA,CAAAG,GAAA,EAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAApB,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAmB,oBAAA;EAAAA,mBAAA,GAAAlB,iBAAA,CAAjC,WAAkCS,MAAM,EAAEP,UAAU,EAAE;IAClD,MAAMiB,cAAc,GAAGjB,UAAU,EAAEiB,cAAc;IACjD,MAAMC,MAAM,GAAG3B,2BAA2B,CAAC0B,cAAc,CAAC;IAC1D;IACA,IAAIE,QAAQ;IACZ;IACA;IACA,IAAI;MACAA,QAAQ,GAAG,OAAO/B,KAAK,CAAC;QACpB8B;MACJ,CAAC,EAAE;QACCE,cAAc,EAAEH,cAAc;QAC9BI,MAAM,EAAEd;MACZ,CAAC,CAAC,EAAEe,UAAU;IAClB,CAAC,CACD,OAAOC,CAAC,EAAE;MACNjC,kBAAkB,CAACiC,CAAC,CAAC;MACrB,MAAM,IAAIlC,SAAS,CAACkC,CAAC,CAAC;IAC1B;IACA,IAAI,CAACJ,QAAQ,EAAE;MACX,MAAM,IAAI9B,SAAS,CAAC;QAChBmC,IAAI,EAAE,wBAAwB;QAC9BC,OAAO,EAAE,kDAAkD;QAC3DC,kBAAkB,EAAE;MACxB,CAAC,CAAC;IACN;IACA,OAAOP,QAAQ;EACnB,CAAC;EAAA,OAAAH,mBAAA,CAAApB,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}