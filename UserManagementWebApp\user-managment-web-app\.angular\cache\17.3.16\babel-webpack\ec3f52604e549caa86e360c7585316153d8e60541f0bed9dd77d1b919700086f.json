{"ast": null, "code": "import { documentExists, processExists, windowExists } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with @angular/core 16.0.0\nfunction angularWebDetect() {\n  const angularVersionSetInDocument = <PERSON>olean(documentExists() && document.querySelector('[ng-version]'));\n  const angularContentSetInWindow = Boolean(windowExists() && typeof window.ng !== 'undefined');\n  return angularVersionSetInDocument || angularContentSetInWindow;\n}\nfunction angularSSRDetect() {\n  return processExists() && typeof process.env === 'object' && process.env.npm_lifecycle_script?.startsWith('ng ') || false;\n}\nexport { angularSSRDetect, angularWebDetect };", "map": {"version": 3, "names": ["documentExists", "processExists", "windowExists", "angularWebDetect", "angularVersionSetInDocument", "Boolean", "document", "querySelector", "angularContentSetInWindow", "window", "ng", "angularSSRDetect", "process", "env", "npm_lifecycle_script", "startsWith"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Platform/detection/Angular.mjs"], "sourcesContent": ["import { documentExists, processExists, windowExists } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with @angular/core 16.0.0\nfunction angularWebDetect() {\n    const angularVersionSetInDocument = Boolean(documentExists() && document.querySelector('[ng-version]'));\n    const angularContentSetInWindow = Boolean(windowExists() && typeof window.ng !== 'undefined');\n    return angularVersionSetInDocument || angularContentSetInWindow;\n}\nfunction angularSSRDetect() {\n    return ((processExists() &&\n        typeof process.env === 'object' &&\n        process.env.npm_lifecycle_script?.startsWith('ng ')) ||\n        false);\n}\n\nexport { angularSSRDetect, angularWebDetect };\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,aAAa,EAAEC,YAAY,QAAQ,eAAe;;AAE3E;AACA;AACA;AACA,SAASC,gBAAgBA,CAAA,EAAG;EACxB,MAAMC,2BAA2B,GAAGC,OAAO,CAACL,cAAc,CAAC,CAAC,IAAIM,QAAQ,CAACC,aAAa,CAAC,cAAc,CAAC,CAAC;EACvG,MAAMC,yBAAyB,GAAGH,OAAO,CAACH,YAAY,CAAC,CAAC,IAAI,OAAOO,MAAM,CAACC,EAAE,KAAK,WAAW,CAAC;EAC7F,OAAON,2BAA2B,IAAII,yBAAyB;AACnE;AACA,SAASG,gBAAgBA,CAAA,EAAG;EACxB,OAASV,aAAa,CAAC,CAAC,IACpB,OAAOW,OAAO,CAACC,GAAG,KAAK,QAAQ,IAC/BD,OAAO,CAACC,GAAG,CAACC,oBAAoB,EAAEC,UAAU,CAAC,KAAK,CAAC,IACnD,KAAK;AACb;AAEA,SAASJ,gBAAgB,EAAER,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}