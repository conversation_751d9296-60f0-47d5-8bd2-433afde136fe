{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { createMachine, sendUpdate } from 'xstate';\nimport { confirmUserAttribute, sendUserAttributeVerificationCode } from 'aws-amplify/auth';\nimport { runValidators } from '../../../validators/index.mjs';\nimport ACTIONS from '../actions.mjs';\nimport { defaultServices } from '../defaultServices.mjs';\nfunction verifyUserAttributesActor() {\n  return createMachine({\n    id: 'verifyUserAttributesActor',\n    initial: 'selectUserAttributes',\n    predictableActionArguments: true,\n    states: {\n      selectUserAttributes: {\n        initial: 'edit',\n        exit: ['clearError', 'clearTouched', 'sendUpdate'],\n        states: {\n          edit: {\n            entry: 'sendUpdate',\n            on: {\n              SUBMIT: {\n                actions: 'handleSubmit',\n                target: 'submit'\n              },\n              SKIP: {\n                target: '#verifyUserAttributesActor.resolved'\n              },\n              CHANGE: {\n                actions: 'handleInput'\n              }\n            }\n          },\n          submit: {\n            tags: 'pending',\n            entry: ['clearError', 'sendUpdate'],\n            invoke: {\n              src: 'sendUserAttributeVerificationCode',\n              onDone: {\n                actions: ['setSelectedUserAttribute', 'setCodeDeliveryDetails'],\n                target: '#verifyUserAttributesActor.confirmVerifyUserAttribute'\n              },\n              onError: {\n                actions: 'setRemoteError',\n                target: 'edit'\n              }\n            }\n          }\n        }\n      },\n      confirmVerifyUserAttribute: {\n        initial: 'edit',\n        exit: ['clearError', 'clearFormValues', 'clearTouched'],\n        states: {\n          edit: {\n            entry: 'sendUpdate',\n            on: {\n              SUBMIT: {\n                actions: 'handleSubmit',\n                target: 'submit'\n              },\n              SKIP: '#verifyUserAttributesActor.resolved',\n              CHANGE: {\n                actions: 'handleInput'\n              }\n            }\n          },\n          submit: {\n            tags: 'pending',\n            entry: ['clearError', 'sendUpdate'],\n            invoke: {\n              src: 'confirmVerifyUserAttribute',\n              onDone: {\n                actions: ['setConfirmAttributeCompleteStep', 'clearSelectedUserAttribute'],\n                target: '#verifyUserAttributesActor.resolved'\n              },\n              onError: {\n                actions: 'setRemoteError',\n                target: 'edit'\n              }\n            }\n          }\n        }\n      },\n      resolved: {\n        type: 'final',\n        data: ({\n          step\n        }) => ({\n          step\n        })\n      }\n    }\n  }, {\n    // sendUpdate is a HOC\n    actions: {\n      ...ACTIONS,\n      sendUpdate: sendUpdate()\n    },\n    services: {\n      sendUserAttributeVerificationCode({\n        formValues: {\n          unverifiedAttr\n        }\n      }) {\n        const input = {\n          userAttributeKey: unverifiedAttr\n        };\n        return sendUserAttributeVerificationCode(input);\n      },\n      confirmVerifyUserAttribute({\n        formValues: {\n          confirmation_code: confirmationCode\n        },\n        selectedUserAttribute\n      }) {\n        return _asyncToGenerator(function* () {\n          const input = {\n            confirmationCode,\n            userAttributeKey: selectedUserAttribute\n          };\n          return confirmUserAttribute(input);\n        })();\n      },\n      validateFields(context) {\n        return _asyncToGenerator(function* () {\n          return runValidators(context.formValues, context.touched, context.passwordSettings, [defaultServices.validateFormPassword, defaultServices.validateConfirmPassword]);\n        })();\n      }\n    }\n  });\n}\nexport { verifyUserAttributesActor };", "map": {"version": 3, "names": ["createMachine", "sendUpdate", "confirmUserAttribute", "sendUserAttributeVerificationCode", "runValidators", "ACTIONS", "defaultServices", "verifyUserAttributesActor", "id", "initial", "predictableActionArguments", "states", "selectUserAttributes", "exit", "edit", "entry", "on", "SUBMIT", "actions", "target", "SKIP", "CHANGE", "submit", "tags", "invoke", "src", "onDone", "onError", "confirmVerifyUserAttribute", "resolved", "type", "data", "step", "services", "formValues", "unverifiedAttr", "input", "userAttributeKey", "confirmation_code", "confirmationCode", "selectedUserAttribute", "_asyncToGenerator", "validateFields", "context", "touched", "passwordSettings", "validateFormPassword", "validateConfirmPassword"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/machines/authenticator/actors/verifyUserAttributes.mjs"], "sourcesContent": ["import { createMachine, sendUpdate } from 'xstate';\nimport { confirmUserAttribute, sendUserAttributeVerificationCode } from 'aws-amplify/auth';\nimport { runValidators } from '../../../validators/index.mjs';\nimport ACTIONS from '../actions.mjs';\nimport { defaultServices } from '../defaultServices.mjs';\n\nfunction verifyUserAttributesActor() {\n    return createMachine({\n        id: 'verifyUserAttributesActor',\n        initial: 'selectUserAttributes',\n        predictableActionArguments: true,\n        states: {\n            selectUserAttributes: {\n                initial: 'edit',\n                exit: ['clearError', 'clearTouched', 'sendUpdate'],\n                states: {\n                    edit: {\n                        entry: 'sendUpdate',\n                        on: {\n                            SUBMIT: { actions: 'handleSubmit', target: 'submit' },\n                            SKIP: { target: '#verifyUserAttributesActor.resolved' },\n                            CHANGE: { actions: 'handleInput' },\n                        },\n                    },\n                    submit: {\n                        tags: 'pending',\n                        entry: ['clearError', 'sendUpdate'],\n                        invoke: {\n                            src: 'sendUserAttributeVerificationCode',\n                            onDone: {\n                                actions: [\n                                    'setSelectedUserAttribute',\n                                    'setCodeDeliveryDetails',\n                                ],\n                                target: '#verifyUserAttributesActor.confirmVerifyUserAttribute',\n                            },\n                            onError: {\n                                actions: 'setRemoteError',\n                                target: 'edit',\n                            },\n                        },\n                    },\n                },\n            },\n            confirmVerifyUserAttribute: {\n                initial: 'edit',\n                exit: ['clearError', 'clearFormValues', 'clearTouched'],\n                states: {\n                    edit: {\n                        entry: 'sendUpdate',\n                        on: {\n                            SUBMIT: { actions: 'handleSubmit', target: 'submit' },\n                            SKIP: '#verifyUserAttributesActor.resolved',\n                            CHANGE: { actions: 'handleInput' },\n                        },\n                    },\n                    submit: {\n                        tags: 'pending',\n                        entry: ['clearError', 'sendUpdate'],\n                        invoke: {\n                            src: 'confirmVerifyUserAttribute',\n                            onDone: {\n                                actions: [\n                                    'setConfirmAttributeCompleteStep',\n                                    'clearSelectedUserAttribute',\n                                ],\n                                target: '#verifyUserAttributesActor.resolved',\n                            },\n                            onError: {\n                                actions: 'setRemoteError',\n                                target: 'edit',\n                            },\n                        },\n                    },\n                },\n            },\n            resolved: { type: 'final', data: ({ step }) => ({ step }) },\n        },\n    }, {\n        // sendUpdate is a HOC\n        actions: { ...ACTIONS, sendUpdate: sendUpdate() },\n        services: {\n            sendUserAttributeVerificationCode({ formValues: { unverifiedAttr } }) {\n                const input = {\n                    userAttributeKey: unverifiedAttr,\n                };\n                return sendUserAttributeVerificationCode(input);\n            },\n            async confirmVerifyUserAttribute({ formValues: { confirmation_code: confirmationCode }, selectedUserAttribute, }) {\n                const input = {\n                    confirmationCode,\n                    userAttributeKey: selectedUserAttribute,\n                };\n                return confirmUserAttribute(input);\n            },\n            async validateFields(context) {\n                return runValidators(context.formValues, context.touched, context.passwordSettings, [\n                    defaultServices.validateFormPassword,\n                    defaultServices.validateConfirmPassword,\n                ]);\n            },\n        },\n    });\n}\n\nexport { verifyUserAttributesActor };\n"], "mappings": ";AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,QAAQ;AAClD,SAASC,oBAAoB,EAAEC,iCAAiC,QAAQ,kBAAkB;AAC1F,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,OAAOC,OAAO,MAAM,gBAAgB;AACpC,SAASC,eAAe,QAAQ,wBAAwB;AAExD,SAASC,yBAAyBA,CAAA,EAAG;EACjC,OAAOP,aAAa,CAAC;IACjBQ,EAAE,EAAE,2BAA2B;IAC/BC,OAAO,EAAE,sBAAsB;IAC/BC,0BAA0B,EAAE,IAAI;IAChCC,MAAM,EAAE;MACJC,oBAAoB,EAAE;QAClBH,OAAO,EAAE,MAAM;QACfI,IAAI,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,YAAY,CAAC;QAClDF,MAAM,EAAE;UACJG,IAAI,EAAE;YACFC,KAAK,EAAE,YAAY;YACnBC,EAAE,EAAE;cACAC,MAAM,EAAE;gBAAEC,OAAO,EAAE,cAAc;gBAAEC,MAAM,EAAE;cAAS,CAAC;cACrDC,IAAI,EAAE;gBAAED,MAAM,EAAE;cAAsC,CAAC;cACvDE,MAAM,EAAE;gBAAEH,OAAO,EAAE;cAAc;YACrC;UACJ,CAAC;UACDI,MAAM,EAAE;YACJC,IAAI,EAAE,SAAS;YACfR,KAAK,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;YACnCS,MAAM,EAAE;cACJC,GAAG,EAAE,mCAAmC;cACxCC,MAAM,EAAE;gBACJR,OAAO,EAAE,CACL,0BAA0B,EAC1B,wBAAwB,CAC3B;gBACDC,MAAM,EAAE;cACZ,CAAC;cACDQ,OAAO,EAAE;gBACLT,OAAO,EAAE,gBAAgB;gBACzBC,MAAM,EAAE;cACZ;YACJ;UACJ;QACJ;MACJ,CAAC;MACDS,0BAA0B,EAAE;QACxBnB,OAAO,EAAE,MAAM;QACfI,IAAI,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,cAAc,CAAC;QACvDF,MAAM,EAAE;UACJG,IAAI,EAAE;YACFC,KAAK,EAAE,YAAY;YACnBC,EAAE,EAAE;cACAC,MAAM,EAAE;gBAAEC,OAAO,EAAE,cAAc;gBAAEC,MAAM,EAAE;cAAS,CAAC;cACrDC,IAAI,EAAE,qCAAqC;cAC3CC,MAAM,EAAE;gBAAEH,OAAO,EAAE;cAAc;YACrC;UACJ,CAAC;UACDI,MAAM,EAAE;YACJC,IAAI,EAAE,SAAS;YACfR,KAAK,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;YACnCS,MAAM,EAAE;cACJC,GAAG,EAAE,4BAA4B;cACjCC,MAAM,EAAE;gBACJR,OAAO,EAAE,CACL,iCAAiC,EACjC,4BAA4B,CAC/B;gBACDC,MAAM,EAAE;cACZ,CAAC;cACDQ,OAAO,EAAE;gBACLT,OAAO,EAAE,gBAAgB;gBACzBC,MAAM,EAAE;cACZ;YACJ;UACJ;QACJ;MACJ,CAAC;MACDU,QAAQ,EAAE;QAAEC,IAAI,EAAE,OAAO;QAAEC,IAAI,EAAEA,CAAC;UAAEC;QAAK,CAAC,MAAM;UAAEA;QAAK,CAAC;MAAE;IAC9D;EACJ,CAAC,EAAE;IACC;IACAd,OAAO,EAAE;MAAE,GAAGb,OAAO;MAAEJ,UAAU,EAAEA,UAAU,CAAC;IAAE,CAAC;IACjDgC,QAAQ,EAAE;MACN9B,iCAAiCA,CAAC;QAAE+B,UAAU,EAAE;UAAEC;QAAe;MAAE,CAAC,EAAE;QAClE,MAAMC,KAAK,GAAG;UACVC,gBAAgB,EAAEF;QACtB,CAAC;QACD,OAAOhC,iCAAiC,CAACiC,KAAK,CAAC;MACnD,CAAC;MACKR,0BAA0BA,CAAC;QAAEM,UAAU,EAAE;UAAEI,iBAAiB,EAAEC;QAAiB,CAAC;QAAEC;MAAuB,CAAC,EAAE;QAAA,OAAAC,iBAAA;UAC9G,MAAML,KAAK,GAAG;YACVG,gBAAgB;YAChBF,gBAAgB,EAAEG;UACtB,CAAC;UACD,OAAOtC,oBAAoB,CAACkC,KAAK,CAAC;QAAC;MACvC,CAAC;MACKM,cAAcA,CAACC,OAAO,EAAE;QAAA,OAAAF,iBAAA;UAC1B,OAAOrC,aAAa,CAACuC,OAAO,CAACT,UAAU,EAAES,OAAO,CAACC,OAAO,EAAED,OAAO,CAACE,gBAAgB,EAAE,CAChFvC,eAAe,CAACwC,oBAAoB,EACpCxC,eAAe,CAACyC,uBAAuB,CAC1C,CAAC;QAAC;MACP;IACJ;EACJ,CAAC,CAAC;AACN;AAEA,SAASxC,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}