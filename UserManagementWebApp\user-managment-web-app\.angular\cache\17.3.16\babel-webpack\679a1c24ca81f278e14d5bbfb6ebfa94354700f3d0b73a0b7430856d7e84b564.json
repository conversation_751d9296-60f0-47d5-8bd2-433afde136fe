{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ConsoleLogger } from '../../../Logger/ConsoleLogger.mjs';\nimport '../../../types/errors.mjs';\nimport '../../../errors/errorHelpers.mjs';\nimport '../../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../../utils/retry/retry.mjs';\nimport '../../../parseAWSExports.mjs';\nimport 'uuid';\nimport '../../../singleton/Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport '../../../Platform/index.mjs';\nimport '../../../Platform/types.mjs';\nimport '../../../BackgroundProcessManager/types.mjs';\nimport '../../../Reachability/Reachability.mjs';\nimport '../../../Hub/index.mjs';\nimport '../../../utils/sessionListener/index.mjs';\nimport '../../../awsClients/pinpoint/base.mjs';\nimport { putEvents } from '../../../awsClients/pinpoint/putEvents.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('PinpointEventBuffer');\nconst RETRYABLE_CODES = [429, 500];\nconst ACCEPTED_CODES = [202];\nclass PinpointEventBuffer {\n  constructor(config) {\n    this._interval = undefined;\n    this._pause = false;\n    this._flush = false;\n    this._buffer = [];\n    this._config = config;\n    this._sendBatch = this._sendBatch.bind(this);\n    this._startLoop();\n  }\n  push(event) {\n    if (this._buffer.length >= this._config.bufferSize) {\n      logger.debug('Exceeded Pinpoint event buffer limits, event dropped.', {\n        eventId: event.eventId\n      });\n      return;\n    }\n    this._buffer.push({\n      [event.eventId]: event\n    });\n  }\n  pause() {\n    this._pause = true;\n  }\n  resume() {\n    this._pause = false;\n  }\n  flush() {\n    this._flush = true;\n  }\n  identityHasChanged(identityId) {\n    return this._config.identityId !== identityId;\n  }\n  flushAll() {\n    this._putEvents(this._buffer.splice(0, this._buffer.length));\n  }\n  _startLoop() {\n    if (this._interval) {\n      clearInterval(this._interval);\n    }\n    const {\n      flushInterval\n    } = this._config;\n    this._interval = setInterval(this._sendBatch, flushInterval);\n  }\n  _sendBatch() {\n    const bufferLength = this._buffer.length;\n    if (this._flush && !bufferLength && this._interval) {\n      clearInterval(this._interval);\n    }\n    if (this._pause || !bufferLength || false) {\n      return;\n    }\n    const {\n      flushSize\n    } = this._config;\n    const batchSize = Math.min(flushSize, bufferLength);\n    const bufferSubset = this._buffer.splice(0, batchSize);\n    this._putEvents(bufferSubset);\n  }\n  _putEvents(buffer) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const eventMap = _this._bufferToMap(buffer);\n      const batchEventParams = _this._generateBatchEventParams(eventMap);\n      try {\n        const {\n          credentials,\n          region,\n          userAgentValue\n        } = _this._config;\n        const data = yield putEvents({\n          credentials,\n          region,\n          userAgentValue\n        }, batchEventParams);\n        _this._processPutEventsSuccessResponse(data, eventMap);\n      } catch (err) {\n        _this._handlePutEventsFailure(err, eventMap);\n      }\n    })();\n  }\n  _generateBatchEventParams(eventMap) {\n    const batchItem = {};\n    Object.values(eventMap).forEach(item => {\n      const {\n        event,\n        timestamp,\n        endpointId,\n        eventId,\n        session\n      } = item;\n      const {\n        name,\n        attributes,\n        metrics\n      } = event;\n      batchItem[endpointId] = {\n        Endpoint: {\n          ...batchItem[endpointId]?.Endpoint\n        },\n        Events: {\n          ...batchItem[endpointId]?.Events,\n          [eventId]: {\n            EventType: name,\n            Timestamp: new Date(timestamp).toISOString(),\n            Attributes: attributes,\n            Metrics: metrics,\n            Session: session\n          }\n        }\n      };\n    });\n    return {\n      ApplicationId: this._config.appId,\n      EventsRequest: {\n        BatchItem: batchItem\n      }\n    };\n  }\n  _handlePutEventsFailure(err, eventMap) {\n    logger.debug('putEvents call to Pinpoint failed.', err);\n    const statusCode = err.$metadata && err.$metadata.httpStatusCode;\n    if (RETRYABLE_CODES.includes(statusCode)) {\n      const retryableEvents = Object.values(eventMap);\n      this._retry(retryableEvents);\n    }\n  }\n  _processPutEventsSuccessResponse(data, eventMap) {\n    const {\n      Results = {}\n    } = data.EventsResponse ?? {};\n    const retryableEvents = [];\n    Object.entries(Results).forEach(([_, endpointValues]) => {\n      const responses = endpointValues.EventsItemResponse ?? {};\n      Object.entries(responses).forEach(([eventId, eventValues]) => {\n        const eventObject = eventMap[eventId];\n        if (!eventObject) {\n          return;\n        }\n        const {\n          StatusCode,\n          Message\n        } = eventValues ?? {};\n        if (StatusCode && ACCEPTED_CODES.includes(StatusCode)) {\n          return;\n        }\n        if (StatusCode && RETRYABLE_CODES.includes(StatusCode)) {\n          retryableEvents.push(eventObject);\n          return;\n        }\n        const {\n          name\n        } = eventObject.event;\n        logger.warn('Pinpoint event failed to send.', {\n          eventId,\n          name,\n          message: Message\n        });\n      });\n    });\n    if (retryableEvents.length) {\n      this._retry(retryableEvents);\n    }\n  }\n  _retry(retryableEvents) {\n    // retryable events that haven't reached the resendLimit\n    const eligibleEvents = [];\n    retryableEvents.forEach(bufferedEvent => {\n      const {\n        eventId\n      } = bufferedEvent;\n      const {\n        name\n      } = bufferedEvent.event;\n      if (bufferedEvent.resendLimit-- > 0) {\n        logger.debug('Resending event.', {\n          eventId,\n          name,\n          remainingAttempts: bufferedEvent.resendLimit\n        });\n        eligibleEvents.push({\n          [eventId]: bufferedEvent\n        });\n        return;\n      }\n      logger.debug('No retry attempts remaining for event.', {\n        eventId,\n        name\n      });\n    });\n    // add the events to the front of the buffer\n    this._buffer.unshift(...eligibleEvents);\n  }\n  _bufferToMap(buffer) {\n    return buffer.reduce((acc, curVal) => {\n      const [[key, value]] = Object.entries(curVal);\n      acc[key] = value;\n      return acc;\n    }, {});\n  }\n}\nexport { PinpointEventBuffer };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "putEvents", "logger", "RETRYABLE_CODES", "ACCEPTED_CODES", "PinpointEventBuffer", "constructor", "config", "_interval", "undefined", "_pause", "_flush", "_buffer", "_config", "_sendBatch", "bind", "_startLoop", "push", "event", "length", "bufferSize", "debug", "eventId", "pause", "resume", "flush", "identityHasChanged", "identityId", "flushAll", "_putEvents", "splice", "clearInterval", "flushInterval", "setInterval", "bufferLength", "flushSize", "batchSize", "Math", "min", "bufferSubset", "buffer", "_this", "_asyncToGenerator", "eventMap", "_bufferToMap", "batchEventParams", "_generateBatchEventParams", "credentials", "region", "userAgentValue", "data", "_processPutEventsSuccessResponse", "err", "_handlePutEventsFailure", "batchItem", "Object", "values", "for<PERSON>ach", "item", "timestamp", "endpointId", "session", "name", "attributes", "metrics", "Endpoint", "Events", "EventType", "Timestamp", "Date", "toISOString", "Attributes", "Metrics", "Session", "ApplicationId", "appId", "EventsRequest", "BatchItem", "statusCode", "$metadata", "httpStatusCode", "includes", "retryableEvents", "_retry", "Results", "EventsResponse", "entries", "_", "endpoint<PERSON><PERSON><PERSON>", "responses", "EventsItemResponse", "eventValues", "eventObject", "StatusCode", "Message", "warn", "message", "eligibleEvents", "bufferedEvent", "resendLimit", "remainingAttempts", "unshift", "reduce", "acc", "curVal", "key", "value"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/providers/pinpoint/utils/PinpointEventBuffer.mjs"], "sourcesContent": ["import { ConsoleLogger } from '../../../Logger/ConsoleLogger.mjs';\nimport '../../../types/errors.mjs';\nimport '../../../errors/errorHelpers.mjs';\nimport '../../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../../utils/retry/retry.mjs';\nimport '../../../parseAWSExports.mjs';\nimport 'uuid';\nimport '../../../singleton/Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport '../../../Platform/index.mjs';\nimport '../../../Platform/types.mjs';\nimport '../../../BackgroundProcessManager/types.mjs';\nimport '../../../Reachability/Reachability.mjs';\nimport '../../../Hub/index.mjs';\nimport '../../../utils/sessionListener/index.mjs';\nimport '../../../awsClients/pinpoint/base.mjs';\nimport { putEvents } from '../../../awsClients/pinpoint/putEvents.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('PinpointEventBuffer');\nconst RETRYABLE_CODES = [429, 500];\nconst ACCEPTED_CODES = [202];\nclass PinpointEventBuffer {\n    constructor(config) {\n        this._interval = undefined;\n        this._pause = false;\n        this._flush = false;\n        this._buffer = [];\n        this._config = config;\n        this._sendBatch = this._sendBatch.bind(this);\n        this._startLoop();\n    }\n    push(event) {\n        if (this._buffer.length >= this._config.bufferSize) {\n            logger.debug('Exceeded Pinpoint event buffer limits, event dropped.', {\n                eventId: event.eventId,\n            });\n            return;\n        }\n        this._buffer.push({ [event.eventId]: event });\n    }\n    pause() {\n        this._pause = true;\n    }\n    resume() {\n        this._pause = false;\n    }\n    flush() {\n        this._flush = true;\n    }\n    identityHasChanged(identityId) {\n        return this._config.identityId !== identityId;\n    }\n    flushAll() {\n        this._putEvents(this._buffer.splice(0, this._buffer.length));\n    }\n    _startLoop() {\n        if (this._interval) {\n            clearInterval(this._interval);\n        }\n        const { flushInterval } = this._config;\n        this._interval = setInterval(this._sendBatch, flushInterval);\n    }\n    _sendBatch() {\n        const bufferLength = this._buffer.length;\n        if (this._flush && !bufferLength && this._interval) {\n            clearInterval(this._interval);\n        }\n        if (this._pause || !bufferLength || false) {\n            return;\n        }\n        const { flushSize } = this._config;\n        const batchSize = Math.min(flushSize, bufferLength);\n        const bufferSubset = this._buffer.splice(0, batchSize);\n        this._putEvents(bufferSubset);\n    }\n    async _putEvents(buffer) {\n        const eventMap = this._bufferToMap(buffer);\n        const batchEventParams = this._generateBatchEventParams(eventMap);\n        try {\n            const { credentials, region, userAgentValue } = this._config;\n            const data = await putEvents({\n                credentials,\n                region,\n                userAgentValue,\n            }, batchEventParams);\n            this._processPutEventsSuccessResponse(data, eventMap);\n        }\n        catch (err) {\n            this._handlePutEventsFailure(err, eventMap);\n        }\n    }\n    _generateBatchEventParams(eventMap) {\n        const batchItem = {};\n        Object.values(eventMap).forEach(item => {\n            const { event, timestamp, endpointId, eventId, session } = item;\n            const { name, attributes, metrics } = event;\n            batchItem[endpointId] = {\n                Endpoint: {\n                    ...batchItem[endpointId]?.Endpoint,\n                },\n                Events: {\n                    ...batchItem[endpointId]?.Events,\n                    [eventId]: {\n                        EventType: name,\n                        Timestamp: new Date(timestamp).toISOString(),\n                        Attributes: attributes,\n                        Metrics: metrics,\n                        Session: session,\n                    },\n                },\n            };\n        });\n        return {\n            ApplicationId: this._config.appId,\n            EventsRequest: {\n                BatchItem: batchItem,\n            },\n        };\n    }\n    _handlePutEventsFailure(err, eventMap) {\n        logger.debug('putEvents call to Pinpoint failed.', err);\n        const statusCode = err.$metadata && err.$metadata.httpStatusCode;\n        if (RETRYABLE_CODES.includes(statusCode)) {\n            const retryableEvents = Object.values(eventMap);\n            this._retry(retryableEvents);\n        }\n    }\n    _processPutEventsSuccessResponse(data, eventMap) {\n        const { Results = {} } = data.EventsResponse ?? {};\n        const retryableEvents = [];\n        Object.entries(Results).forEach(([_, endpointValues]) => {\n            const responses = endpointValues.EventsItemResponse ?? {};\n            Object.entries(responses).forEach(([eventId, eventValues]) => {\n                const eventObject = eventMap[eventId];\n                if (!eventObject) {\n                    return;\n                }\n                const { StatusCode, Message } = eventValues ?? {};\n                if (StatusCode && ACCEPTED_CODES.includes(StatusCode)) {\n                    return;\n                }\n                if (StatusCode && RETRYABLE_CODES.includes(StatusCode)) {\n                    retryableEvents.push(eventObject);\n                    return;\n                }\n                const { name } = eventObject.event;\n                logger.warn('Pinpoint event failed to send.', {\n                    eventId,\n                    name,\n                    message: Message,\n                });\n            });\n        });\n        if (retryableEvents.length) {\n            this._retry(retryableEvents);\n        }\n    }\n    _retry(retryableEvents) {\n        // retryable events that haven't reached the resendLimit\n        const eligibleEvents = [];\n        retryableEvents.forEach((bufferedEvent) => {\n            const { eventId } = bufferedEvent;\n            const { name } = bufferedEvent.event;\n            if (bufferedEvent.resendLimit-- > 0) {\n                logger.debug('Resending event.', {\n                    eventId,\n                    name,\n                    remainingAttempts: bufferedEvent.resendLimit,\n                });\n                eligibleEvents.push({ [eventId]: bufferedEvent });\n                return;\n            }\n            logger.debug('No retry attempts remaining for event.', {\n                eventId,\n                name,\n            });\n        });\n        // add the events to the front of the buffer\n        this._buffer.unshift(...eligibleEvents);\n    }\n    _bufferToMap(buffer) {\n        return buffer.reduce((acc, curVal) => {\n            const [[key, value]] = Object.entries(curVal);\n            acc[key] = value;\n            return acc;\n        }, {});\n    }\n}\n\nexport { PinpointEventBuffer };\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,mCAAmC;AACjE,OAAO,2BAA2B;AAClC,OAAO,kCAAkC;AACzC,OAAO,gDAAgD;AACvD,OAAO,gCAAgC;AACvC,OAAO,8BAA8B;AACrC,OAAO,MAAM;AACb,OAAO,gDAAgD;AACvD,OAAO,uBAAuB;AAC9B,OAAO,2BAA2B;AAClC,OAAO,6BAA6B;AACpC,OAAO,6BAA6B;AACpC,OAAO,6CAA6C;AACpD,OAAO,wCAAwC;AAC/C,OAAO,wBAAwB;AAC/B,OAAO,0CAA0C;AACjD,OAAO,uCAAuC;AAC9C,SAASC,SAAS,QAAQ,4CAA4C;;AAEtE;AACA;AACA,MAAMC,MAAM,GAAG,IAAIF,aAAa,CAAC,qBAAqB,CAAC;AACvD,MAAMG,eAAe,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AAClC,MAAMC,cAAc,GAAG,CAAC,GAAG,CAAC;AAC5B,MAAMC,mBAAmB,CAAC;EACtBC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,SAAS,GAAGC,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACC,OAAO,GAAGN,MAAM;IACrB,IAAI,CAACO,UAAU,GAAG,IAAI,CAACA,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;IAC5C,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,IAAIA,CAACC,KAAK,EAAE;IACR,IAAI,IAAI,CAACN,OAAO,CAACO,MAAM,IAAI,IAAI,CAACN,OAAO,CAACO,UAAU,EAAE;MAChDlB,MAAM,CAACmB,KAAK,CAAC,uDAAuD,EAAE;QAClEC,OAAO,EAAEJ,KAAK,CAACI;MACnB,CAAC,CAAC;MACF;IACJ;IACA,IAAI,CAACV,OAAO,CAACK,IAAI,CAAC;MAAE,CAACC,KAAK,CAACI,OAAO,GAAGJ;IAAM,CAAC,CAAC;EACjD;EACAK,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACb,MAAM,GAAG,IAAI;EACtB;EACAc,MAAMA,CAAA,EAAG;IACL,IAAI,CAACd,MAAM,GAAG,KAAK;EACvB;EACAe,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACd,MAAM,GAAG,IAAI;EACtB;EACAe,kBAAkBA,CAACC,UAAU,EAAE;IAC3B,OAAO,IAAI,CAACd,OAAO,CAACc,UAAU,KAAKA,UAAU;EACjD;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,UAAU,CAAC,IAAI,CAACjB,OAAO,CAACkB,MAAM,CAAC,CAAC,EAAE,IAAI,CAAClB,OAAO,CAACO,MAAM,CAAC,CAAC;EAChE;EACAH,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACR,SAAS,EAAE;MAChBuB,aAAa,CAAC,IAAI,CAACvB,SAAS,CAAC;IACjC;IACA,MAAM;MAAEwB;IAAc,CAAC,GAAG,IAAI,CAACnB,OAAO;IACtC,IAAI,CAACL,SAAS,GAAGyB,WAAW,CAAC,IAAI,CAACnB,UAAU,EAAEkB,aAAa,CAAC;EAChE;EACAlB,UAAUA,CAAA,EAAG;IACT,MAAMoB,YAAY,GAAG,IAAI,CAACtB,OAAO,CAACO,MAAM;IACxC,IAAI,IAAI,CAACR,MAAM,IAAI,CAACuB,YAAY,IAAI,IAAI,CAAC1B,SAAS,EAAE;MAChDuB,aAAa,CAAC,IAAI,CAACvB,SAAS,CAAC;IACjC;IACA,IAAI,IAAI,CAACE,MAAM,IAAI,CAACwB,YAAY,IAAI,KAAK,EAAE;MACvC;IACJ;IACA,MAAM;MAAEC;IAAU,CAAC,GAAG,IAAI,CAACtB,OAAO;IAClC,MAAMuB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACH,SAAS,EAAED,YAAY,CAAC;IACnD,MAAMK,YAAY,GAAG,IAAI,CAAC3B,OAAO,CAACkB,MAAM,CAAC,CAAC,EAAEM,SAAS,CAAC;IACtD,IAAI,CAACP,UAAU,CAACU,YAAY,CAAC;EACjC;EACMV,UAAUA,CAACW,MAAM,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACrB,MAAMC,QAAQ,GAAGF,KAAI,CAACG,YAAY,CAACJ,MAAM,CAAC;MAC1C,MAAMK,gBAAgB,GAAGJ,KAAI,CAACK,yBAAyB,CAACH,QAAQ,CAAC;MACjE,IAAI;QACA,MAAM;UAAEI,WAAW;UAAEC,MAAM;UAAEC;QAAe,CAAC,GAAGR,KAAI,CAAC5B,OAAO;QAC5D,MAAMqC,IAAI,SAASjD,SAAS,CAAC;UACzB8C,WAAW;UACXC,MAAM;UACNC;QACJ,CAAC,EAAEJ,gBAAgB,CAAC;QACpBJ,KAAI,CAACU,gCAAgC,CAACD,IAAI,EAAEP,QAAQ,CAAC;MACzD,CAAC,CACD,OAAOS,GAAG,EAAE;QACRX,KAAI,CAACY,uBAAuB,CAACD,GAAG,EAAET,QAAQ,CAAC;MAC/C;IAAC;EACL;EACAG,yBAAyBA,CAACH,QAAQ,EAAE;IAChC,MAAMW,SAAS,GAAG,CAAC,CAAC;IACpBC,MAAM,CAACC,MAAM,CAACb,QAAQ,CAAC,CAACc,OAAO,CAACC,IAAI,IAAI;MACpC,MAAM;QAAExC,KAAK;QAAEyC,SAAS;QAAEC,UAAU;QAAEtC,OAAO;QAAEuC;MAAQ,CAAC,GAAGH,IAAI;MAC/D,MAAM;QAAEI,IAAI;QAAEC,UAAU;QAAEC;MAAQ,CAAC,GAAG9C,KAAK;MAC3CoC,SAAS,CAACM,UAAU,CAAC,GAAG;QACpBK,QAAQ,EAAE;UACN,GAAGX,SAAS,CAACM,UAAU,CAAC,EAAEK;QAC9B,CAAC;QACDC,MAAM,EAAE;UACJ,GAAGZ,SAAS,CAACM,UAAU,CAAC,EAAEM,MAAM;UAChC,CAAC5C,OAAO,GAAG;YACP6C,SAAS,EAAEL,IAAI;YACfM,SAAS,EAAE,IAAIC,IAAI,CAACV,SAAS,CAAC,CAACW,WAAW,CAAC,CAAC;YAC5CC,UAAU,EAAER,UAAU;YACtBS,OAAO,EAAER,OAAO;YAChBS,OAAO,EAAEZ;UACb;QACJ;MACJ,CAAC;IACL,CAAC,CAAC;IACF,OAAO;MACHa,aAAa,EAAE,IAAI,CAAC7D,OAAO,CAAC8D,KAAK;MACjCC,aAAa,EAAE;QACXC,SAAS,EAAEvB;MACf;IACJ,CAAC;EACL;EACAD,uBAAuBA,CAACD,GAAG,EAAET,QAAQ,EAAE;IACnCzC,MAAM,CAACmB,KAAK,CAAC,oCAAoC,EAAE+B,GAAG,CAAC;IACvD,MAAM0B,UAAU,GAAG1B,GAAG,CAAC2B,SAAS,IAAI3B,GAAG,CAAC2B,SAAS,CAACC,cAAc;IAChE,IAAI7E,eAAe,CAAC8E,QAAQ,CAACH,UAAU,CAAC,EAAE;MACtC,MAAMI,eAAe,GAAG3B,MAAM,CAACC,MAAM,CAACb,QAAQ,CAAC;MAC/C,IAAI,CAACwC,MAAM,CAACD,eAAe,CAAC;IAChC;EACJ;EACA/B,gCAAgCA,CAACD,IAAI,EAAEP,QAAQ,EAAE;IAC7C,MAAM;MAAEyC,OAAO,GAAG,CAAC;IAAE,CAAC,GAAGlC,IAAI,CAACmC,cAAc,IAAI,CAAC,CAAC;IAClD,MAAMH,eAAe,GAAG,EAAE;IAC1B3B,MAAM,CAAC+B,OAAO,CAACF,OAAO,CAAC,CAAC3B,OAAO,CAAC,CAAC,CAAC8B,CAAC,EAAEC,cAAc,CAAC,KAAK;MACrD,MAAMC,SAAS,GAAGD,cAAc,CAACE,kBAAkB,IAAI,CAAC,CAAC;MACzDnC,MAAM,CAAC+B,OAAO,CAACG,SAAS,CAAC,CAAChC,OAAO,CAAC,CAAC,CAACnC,OAAO,EAAEqE,WAAW,CAAC,KAAK;QAC1D,MAAMC,WAAW,GAAGjD,QAAQ,CAACrB,OAAO,CAAC;QACrC,IAAI,CAACsE,WAAW,EAAE;UACd;QACJ;QACA,MAAM;UAAEC,UAAU;UAAEC;QAAQ,CAAC,GAAGH,WAAW,IAAI,CAAC,CAAC;QACjD,IAAIE,UAAU,IAAIzF,cAAc,CAAC6E,QAAQ,CAACY,UAAU,CAAC,EAAE;UACnD;QACJ;QACA,IAAIA,UAAU,IAAI1F,eAAe,CAAC8E,QAAQ,CAACY,UAAU,CAAC,EAAE;UACpDX,eAAe,CAACjE,IAAI,CAAC2E,WAAW,CAAC;UACjC;QACJ;QACA,MAAM;UAAE9B;QAAK,CAAC,GAAG8B,WAAW,CAAC1E,KAAK;QAClChB,MAAM,CAAC6F,IAAI,CAAC,gCAAgC,EAAE;UAC1CzE,OAAO;UACPwC,IAAI;UACJkC,OAAO,EAAEF;QACb,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAIZ,eAAe,CAAC/D,MAAM,EAAE;MACxB,IAAI,CAACgE,MAAM,CAACD,eAAe,CAAC;IAChC;EACJ;EACAC,MAAMA,CAACD,eAAe,EAAE;IACpB;IACA,MAAMe,cAAc,GAAG,EAAE;IACzBf,eAAe,CAACzB,OAAO,CAAEyC,aAAa,IAAK;MACvC,MAAM;QAAE5E;MAAQ,CAAC,GAAG4E,aAAa;MACjC,MAAM;QAAEpC;MAAK,CAAC,GAAGoC,aAAa,CAAChF,KAAK;MACpC,IAAIgF,aAAa,CAACC,WAAW,EAAE,GAAG,CAAC,EAAE;QACjCjG,MAAM,CAACmB,KAAK,CAAC,kBAAkB,EAAE;UAC7BC,OAAO;UACPwC,IAAI;UACJsC,iBAAiB,EAAEF,aAAa,CAACC;QACrC,CAAC,CAAC;QACFF,cAAc,CAAChF,IAAI,CAAC;UAAE,CAACK,OAAO,GAAG4E;QAAc,CAAC,CAAC;QACjD;MACJ;MACAhG,MAAM,CAACmB,KAAK,CAAC,wCAAwC,EAAE;QACnDC,OAAO;QACPwC;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF;IACA,IAAI,CAAClD,OAAO,CAACyF,OAAO,CAAC,GAAGJ,cAAc,CAAC;EAC3C;EACArD,YAAYA,CAACJ,MAAM,EAAE;IACjB,OAAOA,MAAM,CAAC8D,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAK;MAClC,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,CAAC,GAAGnD,MAAM,CAAC+B,OAAO,CAACkB,MAAM,CAAC;MAC7CD,GAAG,CAACE,GAAG,CAAC,GAAGC,KAAK;MAChB,OAAOH,GAAG;IACd,CAAC,EAAE,CAAC,CAAC,CAAC;EACV;AACJ;AAEA,SAASlG,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}