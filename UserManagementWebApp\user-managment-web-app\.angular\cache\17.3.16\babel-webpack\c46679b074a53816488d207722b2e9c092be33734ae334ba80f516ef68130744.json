{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { DEFAULT_RETRY_ATTEMPTS, AMZ_SDK_REQUEST_HEADER } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Middleware injects `amz-sdk-request` header to indicate the retry state at the time an HTTP request is made.\n * This middleware should co-exist with retryMiddleware as it relies on the retryAttempts value in middleware context\n * set by the retry middleware.\n *\n * Example header: `amz-sdk-request: attempt=1; max=3`.\n *\n * This middleware is standalone because of extra headers may conflict with custom endpoint settings(e.g. CORS), we will\n * NOT use this middleware for API categories.\n */\nconst amzSdkRequestHeaderMiddlewareFactory = ({\n  maxAttempts = DEFAULT_RETRY_ATTEMPTS\n}) => (next, context) => {\n  return /*#__PURE__*/function () {\n    var _amzSdkRequestHeaderMiddleware = _asyncToGenerator(function* (request) {\n      const attemptsCount = context.attemptsCount ?? 0;\n      request.headers[AMZ_SDK_REQUEST_HEADER] = `attempt=${attemptsCount + 1}; max=${maxAttempts}`;\n      return next(request);\n    });\n    function amzSdkRequestHeaderMiddleware(_x) {\n      return _amzSdkRequestHeaderMiddleware.apply(this, arguments);\n    }\n    return amzSdkRequestHeaderMiddleware;\n  }();\n};\nexport { amzSdkRequestHeaderMiddlewareFactory };", "map": {"version": 3, "names": ["DEFAULT_RETRY_ATTEMPTS", "AMZ_SDK_REQUEST_HEADER", "amzSdkRequestHeaderMiddlewareFactory", "maxAttempts", "next", "context", "_amzSdkRequestHeaderMiddleware", "_asyncToGenerator", "request", "attemptsCount", "headers", "amzSdkRequestHeaderMiddleware", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/retry/amzSdkRequestHeaderMiddleware.mjs"], "sourcesContent": ["import { DEFAULT_RETRY_ATTEMPTS, AMZ_SDK_REQUEST_HEADER } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Middleware injects `amz-sdk-request` header to indicate the retry state at the time an HTTP request is made.\n * This middleware should co-exist with retryMiddleware as it relies on the retryAttempts value in middleware context\n * set by the retry middleware.\n *\n * Example header: `amz-sdk-request: attempt=1; max=3`.\n *\n * This middleware is standalone because of extra headers may conflict with custom endpoint settings(e.g. CORS), we will\n * NOT use this middleware for API categories.\n */\nconst amzSdkRequestHeaderMiddlewareFactory = ({ maxAttempts = DEFAULT_RETRY_ATTEMPTS }) => (next, context) => {\n    return async function amzSdkRequestHeaderMiddleware(request) {\n        const attemptsCount = context.attemptsCount ?? 0;\n        request.headers[AMZ_SDK_REQUEST_HEADER] =\n            `attempt=${attemptsCount + 1}; max=${maxAttempts}`;\n        return next(request);\n    };\n};\n\nexport { amzSdkRequestHeaderMiddlewareFactory };\n"], "mappings": ";AAAA,SAASA,sBAAsB,EAAEC,sBAAsB,QAAQ,iBAAiB;;AAEhF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oCAAoC,GAAGA,CAAC;EAAEC,WAAW,GAAGH;AAAuB,CAAC,KAAK,CAACI,IAAI,EAAEC,OAAO,KAAK;EAC1G;IAAA,IAAAC,8BAAA,GAAAC,iBAAA,CAAO,WAA6CC,OAAO,EAAE;MACzD,MAAMC,aAAa,GAAGJ,OAAO,CAACI,aAAa,IAAI,CAAC;MAChDD,OAAO,CAACE,OAAO,CAACT,sBAAsB,CAAC,GACnC,WAAWQ,aAAa,GAAG,CAAC,SAASN,WAAW,EAAE;MACtD,OAAOC,IAAI,CAACI,OAAO,CAAC;IACxB,CAAC;IAAA,SALqBG,6BAA6BA,CAAAC,EAAA;MAAA,OAAAN,8BAAA,CAAAO,KAAA,OAAAC,SAAA;IAAA;IAAA,OAA7BH,6BAA6B;EAAA;AAMvD,CAAC;AAED,SAAST,oCAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}