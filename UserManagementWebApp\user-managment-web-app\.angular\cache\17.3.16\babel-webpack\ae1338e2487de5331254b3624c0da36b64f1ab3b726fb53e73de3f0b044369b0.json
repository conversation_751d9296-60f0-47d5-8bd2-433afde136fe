{"ast": null, "code": "const togglebutton = {\n  borderColor: {\n    value: '{colors.border.primary.value}'\n  },\n  color: {\n    value: '{colors.font.primary.value}'\n  },\n  _hover: {\n    backgroundColor: {\n      value: '{colors.overlay.10.value}'\n    }\n  },\n  _focus: {\n    borderColor: {\n      value: '{colors.border.focus.value}'\n    },\n    color: {\n      value: '{colors.font.primary.value}'\n    }\n  },\n  _active: {\n    backgroundColor: {\n      value: '{colors.transparent.value}'\n    }\n  },\n  _disabled: {\n    backgroundColor: {\n      value: '{colors.transparent.value}'\n    },\n    borderColor: {\n      value: '{colors.border.disabled.value}'\n    },\n    color: {\n      value: '{colors.font.disabled.value}'\n    }\n  },\n  _pressed: {\n    borderColor: {\n      value: '{colors.border.pressed.value}'\n    },\n    color: {\n      value: '{colors.font.primary.value}'\n    },\n    backgroundColor: {\n      value: '{colors.overlay.20.value}'\n    },\n    _hover: {\n      backgroundColor: {\n        value: '{colors.overlay.30.value}'\n      }\n    }\n  },\n  primary: {\n    backgroundColor: {\n      value: '{colors.transparent.value}'\n    },\n    borderWidth: {\n      value: '{borderWidths.small.value}'\n    },\n    _focus: {\n      borderColor: {\n        value: '{colors.border.focus.value}'\n      },\n      backgroundColor: {\n        value: '{colors.transparent.value}'\n      },\n      boxShadow: {\n        value: '{components.fieldcontrol._focus.boxShadow.value}'\n      },\n      color: {\n        value: '{colors.font.primary.value}'\n      }\n    },\n    _hover: {\n      backgroundColor: {\n        value: '{colors.overlay.10.value}'\n      },\n      color: {\n        value: '{colors.font.primary.value}'\n      }\n    },\n    _disabled: {\n      borderColor: {\n        value: '{colors.border.disabled.value}'\n      },\n      backgroundColor: {\n        value: '{colors.background.disabled.value}'\n      },\n      color: {\n        value: '{colors.font.disabled.value}'\n      }\n    },\n    _pressed: {\n      backgroundColor: {\n        value: '{colors.primary.80.value}'\n      },\n      borderColor: {\n        value: '{colors.primary.80.value}'\n      },\n      color: {\n        value: '{colors.background.primary.value}'\n      },\n      _focus: {\n        backgroundColor: {\n          value: '{colors.border.focus.value}'\n        },\n        borderColor: {\n          value: '{colors.border.focus.value}'\n        },\n        color: {\n          value: '{colors.background.primary.value}'\n        }\n      },\n      _hover: {\n        borderColor: {\n          value: '{colors.primary.60.value}'\n        },\n        backgroundColor: {\n          value: '{colors.primary.60.value}'\n        },\n        boxShadow: {\n          value: '{colors.primary.60.value}'\n        },\n        color: {\n          value: '{colors.background.primary.value}'\n        }\n      }\n    }\n  },\n  link: {\n    backgroundColor: {\n      value: '{colors.transparent.value}'\n    },\n    color: {\n      value: '{colors.overlay.50.value}'\n    },\n    _hover: {\n      backgroundColor: {\n        value: '{colors.transparent.value}'\n      },\n      color: {\n        value: '{colors.overlay.50.value}'\n      }\n    },\n    _focus: {\n      backgroundColor: {\n        value: '{colors.transparent.value}'\n      },\n      color: {\n        value: '{colors.overlay.50.value}'\n      }\n    },\n    _disabled: {\n      backgroundColor: {\n        value: '{colors.transparent.value}'\n      },\n      color: {\n        value: '{colors.font.disabled.value}'\n      }\n    },\n    _pressed: {\n      backgroundColor: {\n        value: '{colors.transparent.value}'\n      },\n      color: {\n        value: '{colors.overlay.90.value}'\n      },\n      _focus: {\n        backgroundColor: {\n          value: '{colors.transparent.value}'\n        },\n        color: {\n          value: '{colors.overlay.90.value}'\n        }\n      },\n      _hover: {\n        color: {\n          value: '{colors.overlay.90.value}'\n        },\n        backgroundColor: {\n          value: '{colors.transparent.value}'\n        }\n      }\n    }\n  }\n};\nexport { togglebutton };", "map": {"version": 3, "names": ["to<PERSON><PERSON><PERSON>", "borderColor", "value", "color", "_hover", "backgroundColor", "_focus", "_active", "_disabled", "_pressed", "primary", "borderWidth", "boxShadow", "link"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/toggleButton.mjs"], "sourcesContent": ["const togglebutton = {\n    borderColor: { value: '{colors.border.primary.value}' },\n    color: { value: '{colors.font.primary.value}' },\n    _hover: {\n        backgroundColor: { value: '{colors.overlay.10.value}' },\n    },\n    _focus: {\n        borderColor: { value: '{colors.border.focus.value}' },\n        color: { value: '{colors.font.primary.value}' },\n    },\n    _active: {\n        backgroundColor: { value: '{colors.transparent.value}' },\n    },\n    _disabled: {\n        backgroundColor: { value: '{colors.transparent.value}' },\n        borderColor: { value: '{colors.border.disabled.value}' },\n        color: { value: '{colors.font.disabled.value}' },\n    },\n    _pressed: {\n        borderColor: { value: '{colors.border.pressed.value}' },\n        color: { value: '{colors.font.primary.value}' },\n        backgroundColor: { value: '{colors.overlay.20.value}' },\n        _hover: {\n            backgroundColor: { value: '{colors.overlay.30.value}' },\n        },\n    },\n    primary: {\n        backgroundColor: { value: '{colors.transparent.value}' },\n        borderWidth: { value: '{borderWidths.small.value}' },\n        _focus: {\n            borderColor: { value: '{colors.border.focus.value}' },\n            backgroundColor: { value: '{colors.transparent.value}' },\n            boxShadow: { value: '{components.fieldcontrol._focus.boxShadow.value}' },\n            color: { value: '{colors.font.primary.value}' },\n        },\n        _hover: {\n            backgroundColor: { value: '{colors.overlay.10.value}' },\n            color: { value: '{colors.font.primary.value}' },\n        },\n        _disabled: {\n            borderColor: { value: '{colors.border.disabled.value}' },\n            backgroundColor: { value: '{colors.background.disabled.value}' },\n            color: { value: '{colors.font.disabled.value}' },\n        },\n        _pressed: {\n            backgroundColor: { value: '{colors.primary.80.value}' },\n            borderColor: { value: '{colors.primary.80.value}' },\n            color: { value: '{colors.background.primary.value}' },\n            _focus: {\n                backgroundColor: {\n                    value: '{colors.border.focus.value}',\n                },\n                borderColor: { value: '{colors.border.focus.value}' },\n                color: { value: '{colors.background.primary.value}' },\n            },\n            _hover: {\n                borderColor: { value: '{colors.primary.60.value}' },\n                backgroundColor: {\n                    value: '{colors.primary.60.value}',\n                },\n                boxShadow: { value: '{colors.primary.60.value}' },\n                color: { value: '{colors.background.primary.value}' },\n            },\n        },\n    },\n    link: {\n        backgroundColor: { value: '{colors.transparent.value}' },\n        color: { value: '{colors.overlay.50.value}' },\n        _hover: {\n            backgroundColor: { value: '{colors.transparent.value}' },\n            color: { value: '{colors.overlay.50.value}' },\n        },\n        _focus: {\n            backgroundColor: { value: '{colors.transparent.value}' },\n            color: { value: '{colors.overlay.50.value}' },\n        },\n        _disabled: {\n            backgroundColor: { value: '{colors.transparent.value}' },\n            color: { value: '{colors.font.disabled.value}' },\n        },\n        _pressed: {\n            backgroundColor: { value: '{colors.transparent.value}' },\n            color: { value: '{colors.overlay.90.value}' },\n            _focus: {\n                backgroundColor: { value: '{colors.transparent.value}' },\n                color: { value: '{colors.overlay.90.value}' },\n            },\n            _hover: {\n                color: { value: '{colors.overlay.90.value}' },\n                backgroundColor: { value: '{colors.transparent.value}' },\n            },\n        },\n    },\n};\n\nexport { togglebutton };\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG;EACjBC,WAAW,EAAE;IAAEC,KAAK,EAAE;EAAgC,CAAC;EACvDC,KAAK,EAAE;IAAED,KAAK,EAAE;EAA8B,CAAC;EAC/CE,MAAM,EAAE;IACJC,eAAe,EAAE;MAAEH,KAAK,EAAE;IAA4B;EAC1D,CAAC;EACDI,MAAM,EAAE;IACJL,WAAW,EAAE;MAAEC,KAAK,EAAE;IAA8B,CAAC;IACrDC,KAAK,EAAE;MAAED,KAAK,EAAE;IAA8B;EAClD,CAAC;EACDK,OAAO,EAAE;IACLF,eAAe,EAAE;MAAEH,KAAK,EAAE;IAA6B;EAC3D,CAAC;EACDM,SAAS,EAAE;IACPH,eAAe,EAAE;MAAEH,KAAK,EAAE;IAA6B,CAAC;IACxDD,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAiC,CAAC;IACxDC,KAAK,EAAE;MAAED,KAAK,EAAE;IAA+B;EACnD,CAAC;EACDO,QAAQ,EAAE;IACNR,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAgC,CAAC;IACvDC,KAAK,EAAE;MAAED,KAAK,EAAE;IAA8B,CAAC;IAC/CG,eAAe,EAAE;MAAEH,KAAK,EAAE;IAA4B,CAAC;IACvDE,MAAM,EAAE;MACJC,eAAe,EAAE;QAAEH,KAAK,EAAE;MAA4B;IAC1D;EACJ,CAAC;EACDQ,OAAO,EAAE;IACLL,eAAe,EAAE;MAAEH,KAAK,EAAE;IAA6B,CAAC;IACxDS,WAAW,EAAE;MAAET,KAAK,EAAE;IAA6B,CAAC;IACpDI,MAAM,EAAE;MACJL,WAAW,EAAE;QAAEC,KAAK,EAAE;MAA8B,CAAC;MACrDG,eAAe,EAAE;QAAEH,KAAK,EAAE;MAA6B,CAAC;MACxDU,SAAS,EAAE;QAAEV,KAAK,EAAE;MAAmD,CAAC;MACxEC,KAAK,EAAE;QAAED,KAAK,EAAE;MAA8B;IAClD,CAAC;IACDE,MAAM,EAAE;MACJC,eAAe,EAAE;QAAEH,KAAK,EAAE;MAA4B,CAAC;MACvDC,KAAK,EAAE;QAAED,KAAK,EAAE;MAA8B;IAClD,CAAC;IACDM,SAAS,EAAE;MACPP,WAAW,EAAE;QAAEC,KAAK,EAAE;MAAiC,CAAC;MACxDG,eAAe,EAAE;QAAEH,KAAK,EAAE;MAAqC,CAAC;MAChEC,KAAK,EAAE;QAAED,KAAK,EAAE;MAA+B;IACnD,CAAC;IACDO,QAAQ,EAAE;MACNJ,eAAe,EAAE;QAAEH,KAAK,EAAE;MAA4B,CAAC;MACvDD,WAAW,EAAE;QAAEC,KAAK,EAAE;MAA4B,CAAC;MACnDC,KAAK,EAAE;QAAED,KAAK,EAAE;MAAoC,CAAC;MACrDI,MAAM,EAAE;QACJD,eAAe,EAAE;UACbH,KAAK,EAAE;QACX,CAAC;QACDD,WAAW,EAAE;UAAEC,KAAK,EAAE;QAA8B,CAAC;QACrDC,KAAK,EAAE;UAAED,KAAK,EAAE;QAAoC;MACxD,CAAC;MACDE,MAAM,EAAE;QACJH,WAAW,EAAE;UAAEC,KAAK,EAAE;QAA4B,CAAC;QACnDG,eAAe,EAAE;UACbH,KAAK,EAAE;QACX,CAAC;QACDU,SAAS,EAAE;UAAEV,KAAK,EAAE;QAA4B,CAAC;QACjDC,KAAK,EAAE;UAAED,KAAK,EAAE;QAAoC;MACxD;IACJ;EACJ,CAAC;EACDW,IAAI,EAAE;IACFR,eAAe,EAAE;MAAEH,KAAK,EAAE;IAA6B,CAAC;IACxDC,KAAK,EAAE;MAAED,KAAK,EAAE;IAA4B,CAAC;IAC7CE,MAAM,EAAE;MACJC,eAAe,EAAE;QAAEH,KAAK,EAAE;MAA6B,CAAC;MACxDC,KAAK,EAAE;QAAED,KAAK,EAAE;MAA4B;IAChD,CAAC;IACDI,MAAM,EAAE;MACJD,eAAe,EAAE;QAAEH,KAAK,EAAE;MAA6B,CAAC;MACxDC,KAAK,EAAE;QAAED,KAAK,EAAE;MAA4B;IAChD,CAAC;IACDM,SAAS,EAAE;MACPH,eAAe,EAAE;QAAEH,KAAK,EAAE;MAA6B,CAAC;MACxDC,KAAK,EAAE;QAAED,KAAK,EAAE;MAA+B;IACnD,CAAC;IACDO,QAAQ,EAAE;MACNJ,eAAe,EAAE;QAAEH,KAAK,EAAE;MAA6B,CAAC;MACxDC,KAAK,EAAE;QAAED,KAAK,EAAE;MAA4B,CAAC;MAC7CI,MAAM,EAAE;QACJD,eAAe,EAAE;UAAEH,KAAK,EAAE;QAA6B,CAAC;QACxDC,KAAK,EAAE;UAAED,KAAK,EAAE;QAA4B;MAChD,CAAC;MACDE,MAAM,EAAE;QACJD,KAAK,EAAE;UAAED,KAAK,EAAE;QAA4B,CAAC;QAC7CG,eAAe,EAAE;UAAEH,KAAK,EAAE;QAA6B;MAC3D;IACJ;EACJ;AACJ,CAAC;AAED,SAASF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}