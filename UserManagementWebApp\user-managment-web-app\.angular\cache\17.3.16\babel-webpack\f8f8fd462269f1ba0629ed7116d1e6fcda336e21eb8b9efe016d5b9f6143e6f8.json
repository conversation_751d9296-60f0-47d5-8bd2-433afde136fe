{"ast": null, "code": "/*\n * Copyright 2017 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"). You may not use this file except in compliance with\n * the License. A copy of the License is located at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR\n * CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions\n * and limitations under the License.\n */\n\nconst defaults = {\n  opening_character: '{',\n  closing_character: '}',\n  separator: '.'\n};\nmodule.exports = defaults;", "map": {"version": 3, "names": ["defaults", "opening_character", "closing_character", "separator", "module", "exports"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/style-dictionary/lib/utils/references/defaults.js"], "sourcesContent": ["/*\n * Copyright 2017 Amazon.com, Inc. or its affiliates. All Rights Reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"). You may not use this file except in compliance with\n * the License. A copy of the License is located at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * or in the \"license\" file accompanying this file. This file is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR\n * CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions\n * and limitations under the License.\n */\n\nconst defaults = {\n  opening_character: '{',\n  closing_character: '}',\n  separator: '.'\n};\n\nmodule.exports = defaults;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,QAAQ,GAAG;EACfC,iBAAiB,EAAE,GAAG;EACtBC,iBAAiB,EAAE,GAAG;EACtBC,SAAS,EAAE;AACb,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}