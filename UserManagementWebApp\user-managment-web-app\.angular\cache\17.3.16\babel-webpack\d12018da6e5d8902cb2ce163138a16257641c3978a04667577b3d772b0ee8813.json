{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertAuthTokens } from '../../providers/cognito/utils/types.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../utils/getAuthUserAgentValue.mjs';\nimport { registerPasskey } from '../utils/passkey/registerPasskey.mjs';\nimport '../utils/passkey/errors.mjs';\nimport { assertValidCredentialCreationOptions } from '../utils/passkey/types/shared.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../common/AuthErrorStrings.mjs';\nimport '../../errors/types/validation.mjs';\nimport '../../providers/cognito/types/errors.mjs';\nimport { createStartWebAuthnRegistrationClient } from '../../foundation/factories/serviceClients/cognitoIdentityProvider/createStartWebAuthnRegistrationClient.mjs';\nimport { createCompleteWebAuthnRegistrationClient } from '../../foundation/factories/serviceClients/cognitoIdentityProvider/createCompleteWebAuthnRegistrationClient.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Registers a new passkey for an authenticated user\n *\n * @returns Promise<void>\n * @throws - {@link PasskeyError}:\n * - Thrown when intermediate state is invalid\n * @throws - {@link AuthError}:\n * - Thrown when user is unauthenticated\n * @throws - {@link StartWebAuthnRegistrationException}\n * - Thrown due to a service error retrieving WebAuthn registration options\n * @throws - {@link CompleteWebAuthnRegistrationException}\n * - Thrown due to a service error when verifying WebAuthn registration result\n */\nfunction associateWebAuthnCredential() {\n  return _associateWebAuthnCredential.apply(this, arguments);\n}\nfunction _associateWebAuthnCredential() {\n  _associateWebAuthnCredential = _asyncToGenerator(function* () {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolEndpoint,\n      userPoolId\n    } = authConfig;\n    const {\n      tokens\n    } = yield fetchAuthSession();\n    assertAuthTokens(tokens);\n    const startWebAuthnRegistration = createStartWebAuthnRegistrationClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const {\n      CredentialCreationOptions: credentialCreationOptions\n    } = yield startWebAuthnRegistration({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.StartWebAuthnRegistration)\n    }, {\n      AccessToken: tokens.accessToken.toString()\n    });\n    assertValidCredentialCreationOptions(credentialCreationOptions);\n    const cred = yield registerPasskey(credentialCreationOptions);\n    const completeWebAuthnRegistration = createCompleteWebAuthnRegistrationClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    yield completeWebAuthnRegistration({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.CompleteWebAuthnRegistration)\n    }, {\n      AccessToken: tokens.accessToken.toString(),\n      Credential: cred\n    });\n  });\n  return _associateWebAuthnCredential.apply(this, arguments);\n}\nexport { associateWebAuthnCredential };", "map": {"version": 3, "names": ["Amplify", "fetchAuthSession", "assertTokenProviderConfig", "AuthAction", "assertAuthTokens", "createCognitoUserPoolEndpointResolver", "getRegionFromUserPoolId", "getAuthUserAgentValue", "registerPasskey", "assertValidCredentialCreationOptions", "createStartWebAuthnRegistrationClient", "createCompleteWebAuthnRegistrationClient", "associateWebAuthnCredential", "_associateWebAuthnCredential", "apply", "arguments", "_asyncToGenerator", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userPoolEndpoint", "userPoolId", "tokens", "startWebAuthnRegistration", "endpointResolver", "endpointOverride", "CredentialCreationOptions", "credentialCreationOptions", "region", "userAgentValue", "StartWebAuthnRegistration", "AccessToken", "accessToken", "toString", "cred", "completeWebAuthnRegistration", "CompleteWebAuthnRegistration", "Credential"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/apis/associateWebAuthnCredential.mjs"], "sourcesContent": ["import { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertAuthTokens } from '../../providers/cognito/utils/types.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../utils/getAuthUserAgentValue.mjs';\nimport { registerPasskey } from '../utils/passkey/registerPasskey.mjs';\nimport '../utils/passkey/errors.mjs';\nimport { assertValidCredentialCreationOptions } from '../utils/passkey/types/shared.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../common/AuthErrorStrings.mjs';\nimport '../../errors/types/validation.mjs';\nimport '../../providers/cognito/types/errors.mjs';\nimport { createStartWebAuthnRegistrationClient } from '../../foundation/factories/serviceClients/cognitoIdentityProvider/createStartWebAuthnRegistrationClient.mjs';\nimport { createCompleteWebAuthnRegistrationClient } from '../../foundation/factories/serviceClients/cognitoIdentityProvider/createCompleteWebAuthnRegistrationClient.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Registers a new passkey for an authenticated user\n *\n * @returns Promise<void>\n * @throws - {@link PasskeyError}:\n * - Thrown when intermediate state is invalid\n * @throws - {@link AuthError}:\n * - Thrown when user is unauthenticated\n * @throws - {@link StartWebAuthnRegistrationException}\n * - Thrown due to a service error retrieving WebAuthn registration options\n * @throws - {@link CompleteWebAuthnRegistrationException}\n * - Thrown due to a service error when verifying WebAuthn registration result\n */\nasync function associateWebAuthnCredential() {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolEndpoint, userPoolId } = authConfig;\n    const { tokens } = await fetchAuthSession();\n    assertAuthTokens(tokens);\n    const startWebAuthnRegistration = createStartWebAuthnRegistrationClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const { CredentialCreationOptions: credentialCreationOptions } = await startWebAuthnRegistration({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.StartWebAuthnRegistration),\n    }, {\n        AccessToken: tokens.accessToken.toString(),\n    });\n    assertValidCredentialCreationOptions(credentialCreationOptions);\n    const cred = await registerPasskey(credentialCreationOptions);\n    const completeWebAuthnRegistration = createCompleteWebAuthnRegistrationClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    await completeWebAuthnRegistration({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.CompleteWebAuthnRegistration),\n    }, {\n        AccessToken: tokens.accessToken.toString(),\n        Credential: cred,\n    });\n}\n\nexport { associateWebAuthnCredential };\n"], "mappings": ";AAAA,SAASA,OAAO,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC7D,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,SAASC,qCAAqC,QAAQ,6EAA6E;AACnI,SAASC,uBAAuB,QAAQ,4CAA4C;AACpF,SAASC,qBAAqB,QAAQ,uCAAuC;AAC7E,SAASC,eAAe,QAAQ,sCAAsC;AACtE,OAAO,6BAA6B;AACpC,SAASC,oCAAoC,QAAQ,mCAAmC;AACxF,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,qHAAqH;AAC5H,OAAO,iFAAiF;AACxF,OAAO,mCAAmC;AAC1C,OAAO,mCAAmC;AAC1C,OAAO,0CAA0C;AACjD,SAASC,qCAAqC,QAAQ,6GAA6G;AACnK,SAASC,wCAAwC,QAAQ,gHAAgH;;AAEzK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,SAaeC,2BAA2BA,CAAA;EAAA,OAAAC,4BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,6BAAA;EAAAA,4BAAA,GAAAG,iBAAA,CAA1C,aAA6C;IACzC,MAAMC,UAAU,GAAGjB,OAAO,CAACkB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDlB,yBAAyB,CAACe,UAAU,CAAC;IACrC,MAAM;MAAEI,gBAAgB;MAAEC;IAAW,CAAC,GAAGL,UAAU;IACnD,MAAM;MAAEM;IAAO,CAAC,SAAStB,gBAAgB,CAAC,CAAC;IAC3CG,gBAAgB,CAACmB,MAAM,CAAC;IACxB,MAAMC,yBAAyB,GAAGd,qCAAqC,CAAC;MACpEe,gBAAgB,EAAEpB,qCAAqC,CAAC;QACpDqB,gBAAgB,EAAEL;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAM;MAAEM,yBAAyB,EAAEC;IAA0B,CAAC,SAASJ,yBAAyB,CAAC;MAC7FK,MAAM,EAAEvB,uBAAuB,CAACgB,UAAU,CAAC;MAC3CQ,cAAc,EAAEvB,qBAAqB,CAACJ,UAAU,CAAC4B,yBAAyB;IAC9E,CAAC,EAAE;MACCC,WAAW,EAAET,MAAM,CAACU,WAAW,CAACC,QAAQ,CAAC;IAC7C,CAAC,CAAC;IACFzB,oCAAoC,CAACmB,yBAAyB,CAAC;IAC/D,MAAMO,IAAI,SAAS3B,eAAe,CAACoB,yBAAyB,CAAC;IAC7D,MAAMQ,4BAA4B,GAAGzB,wCAAwC,CAAC;MAC1Ec,gBAAgB,EAAEpB,qCAAqC,CAAC;QACpDqB,gBAAgB,EAAEL;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMe,4BAA4B,CAAC;MAC/BP,MAAM,EAAEvB,uBAAuB,CAACgB,UAAU,CAAC;MAC3CQ,cAAc,EAAEvB,qBAAqB,CAACJ,UAAU,CAACkC,4BAA4B;IACjF,CAAC,EAAE;MACCL,WAAW,EAAET,MAAM,CAACU,WAAW,CAACC,QAAQ,CAAC,CAAC;MAC1CI,UAAU,EAAEH;IAChB,CAAC,CAAC;EACN,CAAC;EAAA,OAAAtB,4BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASH,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}