{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { amplifyUuid } from '../../../utils/amplifyUuid/index.mjs';\nimport '../../../Cache/index.mjs';\nimport { resolveEndpointId } from '../utils/resolveEndpointId.mjs';\nimport '../../../utils/sessionListener/index.mjs';\nimport { RESEND_LIMIT, BUFFER_SIZE, FLUSH_INTERVAL, FLUSH_SIZE } from '../utils/constants.mjs';\nimport { getEventBuffer } from '../utils/getEventBuffer.mjs';\nimport { SESSION_STOP_EVENT, SESSION_START_EVENT } from '../../../utils/sessionListener/constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nlet session;\n/**\n * @internal\n */\nconst record = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* ({\n    appId,\n    category,\n    channelType,\n    credentials,\n    event,\n    identityId,\n    region,\n    userAgentValue,\n    bufferSize,\n    flushInterval,\n    flushSize,\n    resendLimit\n  }) {\n    let eventSession = session;\n    const currentTime = new Date();\n    const timestampISOString = currentTime.toISOString();\n    const eventId = amplifyUuid();\n    // Prepare event buffer if required\n    const buffer = getEventBuffer({\n      appId,\n      region,\n      credentials,\n      bufferSize: bufferSize ?? BUFFER_SIZE,\n      flushInterval: flushInterval ?? FLUSH_INTERVAL,\n      flushSize: flushSize ?? FLUSH_SIZE,\n      resendLimit: resendLimit ?? RESEND_LIMIT,\n      identityId,\n      userAgentValue\n    });\n    const endpointId = yield resolveEndpointId({\n      appId,\n      category,\n      channelType,\n      credentials,\n      identityId,\n      region,\n      userAgentValue\n    });\n    // Generate session if required\n    if (!eventSession || event.name === SESSION_START_EVENT) {\n      const sessionId = amplifyUuid();\n      session = {\n        Id: sessionId,\n        StartTimestamp: timestampISOString\n      };\n      eventSession = session;\n    }\n    // Terminate session when required\n    if (session && event.name === SESSION_STOP_EVENT) {\n      eventSession = {\n        ...session,\n        StopTimestamp: timestampISOString,\n        Duration: currentTime.getTime() - new Date(session.StartTimestamp).getTime()\n      };\n      session = undefined;\n    }\n    // Push event to buffer\n    buffer.push({\n      eventId,\n      endpointId,\n      event,\n      session: eventSession,\n      timestamp: timestampISOString,\n      resendLimit: resendLimit ?? RESEND_LIMIT\n    });\n  });\n  return function record(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { record };", "map": {"version": 3, "names": ["amplifyUuid", "resolveEndpointId", "RESEND_LIMIT", "BUFFER_SIZE", "FLUSH_INTERVAL", "FLUSH_SIZE", "getEventBuffer", "SESSION_STOP_EVENT", "SESSION_START_EVENT", "session", "record", "_ref", "_asyncToGenerator", "appId", "category", "channelType", "credentials", "event", "identityId", "region", "userAgentValue", "bufferSize", "flushInterval", "flushSize", "resendLimit", "eventSession", "currentTime", "Date", "timestampISOString", "toISOString", "eventId", "buffer", "endpointId", "name", "sessionId", "Id", "StartTimestamp", "StopTimestamp", "Duration", "getTime", "undefined", "push", "timestamp", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/providers/pinpoint/apis/record.mjs"], "sourcesContent": ["import { amplifyUuid } from '../../../utils/amplifyUuid/index.mjs';\nimport '../../../Cache/index.mjs';\nimport { resolveEndpointId } from '../utils/resolveEndpointId.mjs';\nimport '../../../utils/sessionListener/index.mjs';\nimport { RESEND_LIMIT, BUFFER_SIZE, FLUSH_INTERVAL, FLUSH_SIZE } from '../utils/constants.mjs';\nimport { getEventBuffer } from '../utils/getEventBuffer.mjs';\nimport { SESSION_STOP_EVENT, SESSION_START_EVENT } from '../../../utils/sessionListener/constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nlet session;\n/**\n * @internal\n */\nconst record = async ({ appId, category, channelType, credentials, event, identityId, region, userAgentValue, bufferSize, flushInterval, flushSize, resendLimit, }) => {\n    let eventSession = session;\n    const currentTime = new Date();\n    const timestampISOString = currentTime.toISOString();\n    const eventId = amplifyUuid();\n    // Prepare event buffer if required\n    const buffer = getEventBuffer({\n        appId,\n        region,\n        credentials,\n        bufferSize: bufferSize ?? BUFFER_SIZE,\n        flushInterval: flushInterval ?? FLUSH_INTERVAL,\n        flushSize: flushSize ?? FLUSH_SIZE,\n        resendLimit: resendLimit ?? RESEND_LIMIT,\n        identityId,\n        userAgentValue,\n    });\n    const endpointId = await resolveEndpointId({\n        appId,\n        category,\n        channelType,\n        credentials,\n        identityId,\n        region,\n        userAgentValue,\n    });\n    // Generate session if required\n    if (!eventSession || event.name === SESSION_START_EVENT) {\n        const sessionId = amplifyUuid();\n        session = {\n            Id: sessionId,\n            StartTimestamp: timestampISOString,\n        };\n        eventSession = session;\n    }\n    // Terminate session when required\n    if (session && event.name === SESSION_STOP_EVENT) {\n        eventSession = {\n            ...session,\n            StopTimestamp: timestampISOString,\n            Duration: currentTime.getTime() - new Date(session.StartTimestamp).getTime(),\n        };\n        session = undefined;\n    }\n    // Push event to buffer\n    buffer.push({\n        eventId,\n        endpointId,\n        event,\n        session: eventSession,\n        timestamp: timestampISOString,\n        resendLimit: resendLimit ?? RESEND_LIMIT,\n    });\n};\n\nexport { record };\n"], "mappings": ";AAAA,SAASA,WAAW,QAAQ,sCAAsC;AAClE,OAAO,0BAA0B;AACjC,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,OAAO,0CAA0C;AACjD,SAASC,YAAY,EAAEC,WAAW,EAAEC,cAAc,EAAEC,UAAU,QAAQ,wBAAwB;AAC9F,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,kBAAkB,EAAEC,mBAAmB,QAAQ,8CAA8C;;AAEtG;AACA;AACA,IAAIC,OAAO;AACX;AACA;AACA;AACA,MAAMC,MAAM;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAO;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,WAAW;IAAEC,KAAK;IAAEC,UAAU;IAAEC,MAAM;IAAEC,cAAc;IAAEC,UAAU;IAAEC,aAAa;IAAEC,SAAS;IAAEC;EAAa,CAAC,EAAK;IACnK,IAAIC,YAAY,GAAGhB,OAAO;IAC1B,MAAMiB,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC;IAC9B,MAAMC,kBAAkB,GAAGF,WAAW,CAACG,WAAW,CAAC,CAAC;IACpD,MAAMC,OAAO,GAAG9B,WAAW,CAAC,CAAC;IAC7B;IACA,MAAM+B,MAAM,GAAGzB,cAAc,CAAC;MAC1BO,KAAK;MACLM,MAAM;MACNH,WAAW;MACXK,UAAU,EAAEA,UAAU,IAAIlB,WAAW;MACrCmB,aAAa,EAAEA,aAAa,IAAIlB,cAAc;MAC9CmB,SAAS,EAAEA,SAAS,IAAIlB,UAAU;MAClCmB,WAAW,EAAEA,WAAW,IAAItB,YAAY;MACxCgB,UAAU;MACVE;IACJ,CAAC,CAAC;IACF,MAAMY,UAAU,SAAS/B,iBAAiB,CAAC;MACvCY,KAAK;MACLC,QAAQ;MACRC,WAAW;MACXC,WAAW;MACXE,UAAU;MACVC,MAAM;MACNC;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACK,YAAY,IAAIR,KAAK,CAACgB,IAAI,KAAKzB,mBAAmB,EAAE;MACrD,MAAM0B,SAAS,GAAGlC,WAAW,CAAC,CAAC;MAC/BS,OAAO,GAAG;QACN0B,EAAE,EAAED,SAAS;QACbE,cAAc,EAAER;MACpB,CAAC;MACDH,YAAY,GAAGhB,OAAO;IAC1B;IACA;IACA,IAAIA,OAAO,IAAIQ,KAAK,CAACgB,IAAI,KAAK1B,kBAAkB,EAAE;MAC9CkB,YAAY,GAAG;QACX,GAAGhB,OAAO;QACV4B,aAAa,EAAET,kBAAkB;QACjCU,QAAQ,EAAEZ,WAAW,CAACa,OAAO,CAAC,CAAC,GAAG,IAAIZ,IAAI,CAAClB,OAAO,CAAC2B,cAAc,CAAC,CAACG,OAAO,CAAC;MAC/E,CAAC;MACD9B,OAAO,GAAG+B,SAAS;IACvB;IACA;IACAT,MAAM,CAACU,IAAI,CAAC;MACRX,OAAO;MACPE,UAAU;MACVf,KAAK;MACLR,OAAO,EAAEgB,YAAY;MACrBiB,SAAS,EAAEd,kBAAkB;MAC7BJ,WAAW,EAAEA,WAAW,IAAItB;IAChC,CAAC,CAAC;EACN,CAAC;EAAA,gBArDKQ,MAAMA,CAAAiC,EAAA;IAAA,OAAAhC,IAAA,CAAAiC,KAAA,OAAAC,SAAA;EAAA;AAAA,GAqDX;AAED,SAASnC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}