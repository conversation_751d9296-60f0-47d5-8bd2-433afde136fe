{"ast": null, "code": "import { HEX_TO_SHORT } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Converts a hexadecimal encoded string to a Uint8Array of bytes.\n *\n * @param encoded The hexadecimal encoded string\n */\nconst getBytesFromHex = encoded => {\n  if (encoded.length % 2 !== 0) {\n    throw new Error('Hex encoded strings must have an even number length');\n  }\n  const out = new Uint8Array(encoded.length / 2);\n  for (let i = 0; i < encoded.length; i += 2) {\n    const encodedByte = encoded.slice(i, i + 2).toLowerCase();\n    if (encodedByte in HEX_TO_SHORT) {\n      out[i / 2] = HEX_TO_SHORT[encodedByte];\n    } else {\n      throw new Error(`Cannot decode unrecognized sequence ${encodedByte} as hexadecimal`);\n    }\n  }\n  return out;\n};\nexport { getBytesFromHex };", "map": {"version": 3, "names": ["HEX_TO_SHORT", "getBytesFromHex", "encoded", "length", "Error", "out", "Uint8Array", "i", "encodedByte", "slice", "toLowerCase"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/srp/getBytesFromHex.mjs"], "sourcesContent": ["import { HEX_TO_SHORT } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Converts a hexadecimal encoded string to a Uint8Array of bytes.\n *\n * @param encoded The hexadecimal encoded string\n */\nconst getBytesFromHex = (encoded) => {\n    if (encoded.length % 2 !== 0) {\n        throw new Error('Hex encoded strings must have an even number length');\n    }\n    const out = new Uint8Array(encoded.length / 2);\n    for (let i = 0; i < encoded.length; i += 2) {\n        const encodedByte = encoded.slice(i, i + 2).toLowerCase();\n        if (encodedByte in HEX_TO_SHORT) {\n            out[i / 2] = HEX_TO_SHORT[encodedByte];\n        }\n        else {\n            throw new Error(`Cannot decode unrecognized sequence ${encodedByte} as hexadecimal`);\n        }\n    }\n    return out;\n};\n\nexport { getBytesFromHex };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAIC,OAAO,IAAK;EACjC,IAAIA,OAAO,CAACC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IAC1B,MAAM,IAAIC,KAAK,CAAC,qDAAqD,CAAC;EAC1E;EACA,MAAMC,GAAG,GAAG,IAAIC,UAAU,CAACJ,OAAO,CAACC,MAAM,GAAG,CAAC,CAAC;EAC9C,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,OAAO,CAACC,MAAM,EAAEI,CAAC,IAAI,CAAC,EAAE;IACxC,MAAMC,WAAW,GAAGN,OAAO,CAACO,KAAK,CAACF,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;IACzD,IAAIF,WAAW,IAAIR,YAAY,EAAE;MAC7BK,GAAG,CAACE,CAAC,GAAG,CAAC,CAAC,GAAGP,YAAY,CAACQ,WAAW,CAAC;IAC1C,CAAC,MACI;MACD,MAAM,IAAIJ,KAAK,CAAC,uCAAuCI,WAAW,iBAAiB,CAAC;IACxF;EACJ;EACA,OAAOH,GAAG;AACd,CAAC;AAED,SAASJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}