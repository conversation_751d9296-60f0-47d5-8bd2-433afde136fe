{"ast": null, "code": "const fontWeights = {\n  hairline: {\n    value: 100\n  },\n  thin: {\n    value: 200\n  },\n  light: {\n    value: 300\n  },\n  normal: {\n    value: 400\n  },\n  medium: {\n    value: 500\n  },\n  semibold: {\n    value: 600\n  },\n  bold: {\n    value: 700\n  },\n  extrabold: {\n    value: 800\n  },\n  black: {\n    value: 900\n  }\n};\nexport { fontWeights };", "map": {"version": 3, "names": ["fontWeights", "hairline", "value", "thin", "light", "normal", "medium", "semibold", "bold", "extrabold", "black"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/fontWeights.mjs"], "sourcesContent": ["const fontWeights = {\n    hairline: { value: 100 },\n    thin: { value: 200 },\n    light: { value: 300 },\n    normal: { value: 400 },\n    medium: { value: 500 },\n    semibold: { value: 600 },\n    bold: { value: 700 },\n    extrabold: { value: 800 },\n    black: { value: 900 },\n};\n\nexport { fontWeights };\n"], "mappings": "AAAA,MAAMA,WAAW,GAAG;EAChBC,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAI,CAAC;EACxBC,IAAI,EAAE;IAAED,KAAK,EAAE;EAAI,CAAC;EACpBE,KAAK,EAAE;IAAEF,KAAK,EAAE;EAAI,CAAC;EACrBG,MAAM,EAAE;IAAEH,KAAK,EAAE;EAAI,CAAC;EACtBI,MAAM,EAAE;IAAEJ,KAAK,EAAE;EAAI,CAAC;EACtBK,QAAQ,EAAE;IAAEL,KAAK,EAAE;EAAI,CAAC;EACxBM,IAAI,EAAE;IAAEN,KAAK,EAAE;EAAI,CAAC;EACpBO,SAAS,EAAE;IAAEP,KAAK,EAAE;EAAI,CAAC;EACzBQ,KAAK,EAAE;IAAER,KAAK,EAAE;EAAI;AACxB,CAAC;AAED,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}