{"ast": null, "code": "import { actions, createMachine, assign, spawn, forwardTo } from 'xstate';\nimport '@aws-amplify/core/internals/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { isEmptyObject } from '../../utils/utils.mjs';\nimport ACTIONS from './actions.mjs';\nimport GUARDS from './guards.mjs';\nimport { forgotPasswordActor } from './actors/forgotPassword.mjs';\nimport { signInActor } from './actors/signIn.mjs';\nimport { signUpActor } from './actors/signUp.mjs';\nimport { signOutActor } from './actors/signOut.mjs';\nimport { verifyUserAttributesActor } from './actors/verifyUserAttributes.mjs';\nimport { defaultServices } from './defaultServices.mjs';\nconst getActorContext = (context, defaultStep) => ({\n  ...context.actorDoneData,\n  step: context?.actorDoneData?.step ?? defaultStep,\n  // initialize empty objects on actor start\n  formValues: {},\n  touched: {},\n  validationError: {},\n  // values included on `context.config` that should be available in actors\n  formFields: context.config?.formFields,\n  loginMechanisms: context.config?.loginMechanisms,\n  passwordSettings: context.config?.passwordSettings,\n  signUpAttributes: context.config?.signUpAttributes,\n  socialProviders: context.config?.socialProviders\n});\nconst {\n  choose,\n  stop\n} = actions;\nconst stopActor = machineId => stop(machineId);\n// setup step waits for ui to emit INIT action to proceed to configure\nconst LEGACY_WAIT_CONFIG = {\n  on: {\n    INIT: {\n      actions: 'configure',\n      target: 'getConfig'\n    },\n    SIGN_OUT: '#authenticator.signOut'\n  }\n};\n// setup step proceeds directly to configure\nconst NEXT_WAIT_CONFIG = {\n  always: {\n    actions: 'configure',\n    target: 'getConfig'\n  }\n};\nfunction createAuthenticatorMachine(options) {\n  const {\n    useNextWaitConfig,\n    ...overrideConfigServices\n  } = options ?? {};\n  const initConfig = useNextWaitConfig ? NEXT_WAIT_CONFIG : LEGACY_WAIT_CONFIG;\n  return createMachine({\n    id: 'authenticator',\n    initial: 'idle',\n    context: {\n      user: undefined,\n      config: {},\n      services: defaultServices,\n      actorRef: undefined,\n      hasSetup: false\n    },\n    predictableActionArguments: true,\n    states: {\n      // See: https://xstate.js.org/docs/guides/communication.html#invoking-promises\n      idle: {\n        invoke: {\n          src: 'handleGetCurrentUser',\n          onDone: {\n            actions: 'setUser',\n            target: 'setup'\n          },\n          onError: {\n            target: 'setup'\n          }\n        }\n      },\n      setup: {\n        initial: 'initConfig',\n        states: {\n          initConfig,\n          getConfig: {\n            invoke: {\n              src: 'getAmplifyConfig',\n              onDone: [{\n                actions: ['applyAmplifyConfig', 'setHasSetup'],\n                cond: 'hasUser',\n                target: '#authenticator.authenticated'\n              }, {\n                actions: ['applyAmplifyConfig', 'setHasSetup'],\n                target: 'goToInitialState'\n              }]\n            }\n          },\n          goToInitialState: {\n            always: [{\n              cond: 'isInitialStateSignUp',\n              target: '#authenticator.signUpActor'\n            }, {\n              cond: 'isInitialStateResetPassword',\n              target: '#authenticator.forgotPasswordActor'\n            }, {\n              target: '#authenticator.signInActor'\n            }]\n          }\n        }\n      },\n      getCurrentUser: {\n        invoke: {\n          src: 'handleGetCurrentUser',\n          onDone: {\n            actions: 'setUser',\n            target: '#authenticator.authenticated'\n          },\n          onError: {\n            target: '#authenticator.setup'\n          }\n        }\n      },\n      signInActor: {\n        initial: 'spawnActor',\n        states: {\n          spawnActor: {\n            always: {\n              actions: 'spawnSignInActor',\n              target: 'runActor'\n            }\n          },\n          runActor: {\n            entry: 'clearActorDoneData',\n            exit: stopActor('signInActor')\n          }\n        },\n        on: {\n          FORGOT_PASSWORD: 'forgotPasswordActor',\n          SIGN_IN: 'signInActor',\n          SIGN_UP: 'signUpActor',\n          'done.invoke.signInActor': [{\n            cond: 'hasCompletedAttributeConfirmation',\n            target: '#authenticator.getCurrentUser'\n          }, {\n            cond: 'isShouldConfirmUserAttributeStep',\n            actions: 'setActorDoneData',\n            target: '#authenticator.verifyUserAttributesActor'\n          }, {\n            cond: 'isResetPasswordStep',\n            actions: 'setActorDoneData',\n            target: '#authenticator.forgotPasswordActor'\n          }, {\n            cond: 'isConfirmSignUpStep',\n            actions: 'setActorDoneData',\n            target: '#authenticator.signUpActor'\n          }]\n        }\n      },\n      signUpActor: {\n        initial: 'spawnActor',\n        states: {\n          spawnActor: {\n            always: {\n              actions: 'spawnSignUpActor',\n              target: 'runActor'\n            }\n          },\n          runActor: {\n            entry: 'clearActorDoneData',\n            exit: stopActor('signUpActor')\n          }\n        },\n        on: {\n          SIGN_IN: 'signInActor',\n          'done.invoke.signUpActor': [{\n            cond: 'hasCompletedAttributeConfirmation',\n            target: '#authenticator.getCurrentUser'\n          }, {\n            cond: 'isShouldConfirmUserAttributeStep',\n            actions: 'setActorDoneData',\n            target: '#authenticator.verifyUserAttributesActor'\n          }, {\n            cond: 'isConfirmUserAttributeStep',\n            target: '#authenticator.verifyUserAttributesActor'\n          }, {\n            actions: 'setActorDoneData',\n            target: '#authenticator.signInActor'\n          }]\n        }\n      },\n      forgotPasswordActor: {\n        initial: 'spawnActor',\n        states: {\n          spawnActor: {\n            always: {\n              actions: 'spawnForgotPasswordActor',\n              target: 'runActor'\n            }\n          },\n          runActor: {\n            entry: 'clearActorDoneData',\n            exit: stopActor('forgotPasswordActor')\n          }\n        },\n        on: {\n          SIGN_IN: 'signInActor',\n          'done.invoke.forgotPasswordActor': [{\n            target: '#authenticator.signInActor'\n          }]\n        }\n      },\n      verifyUserAttributesActor: {\n        initial: 'spawnActor',\n        states: {\n          spawnActor: {\n            always: {\n              actions: 'spawnVerifyUserAttributesActor',\n              target: 'runActor'\n            }\n          },\n          runActor: {\n            entry: 'clearActorDoneData',\n            exit: stopActor('verifyUserAttributesActor')\n          }\n        },\n        on: {\n          'done.invoke.verifyUserAttributesActor': [{\n            actions: 'setActorDoneData',\n            target: '#authenticator.getCurrentUser'\n          }]\n        }\n      },\n      authenticated: {\n        initial: 'idle',\n        states: {\n          idle: {\n            on: {\n              TOKEN_REFRESH: 'refreshUser'\n            }\n          },\n          refreshUser: {\n            invoke: {\n              src: '#authenticator.getCurrentUser',\n              onDone: {\n                actions: 'setUser',\n                target: 'idle'\n              },\n              onError: {\n                target: '#authenticator.signOut'\n              }\n            }\n          }\n        },\n        on: {\n          SIGN_OUT: 'signOut'\n        }\n      },\n      signOut: {\n        initial: 'spawnActor',\n        states: {\n          spawnActor: {\n            always: {\n              actions: 'spawnSignOutActor',\n              target: 'runActor'\n            }\n          },\n          runActor: {\n            entry: 'clearActorDoneData',\n            exit: stopActor('signOutActor')\n          }\n        },\n        on: {\n          'done.invoke.signOutActor': {\n            actions: 'clearUser',\n            target: 'setup.getConfig'\n          }\n        }\n      }\n    },\n    on: {\n      SIGN_IN_WITH_REDIRECT: {\n        target: '#authenticator.getCurrentUser'\n      },\n      CHANGE: {\n        actions: 'forwardToActor'\n      },\n      BLUR: {\n        actions: 'forwardToActor'\n      },\n      SUBMIT: {\n        actions: 'forwardToActor'\n      },\n      FEDERATED_SIGN_IN: {\n        actions: 'forwardToActor'\n      },\n      RESEND: {\n        actions: 'forwardToActor'\n      },\n      SIGN_IN: {\n        actions: 'forwardToActor'\n      },\n      SKIP: {\n        actions: 'forwardToActor'\n      }\n    }\n  }, {\n    actions: {\n      ...ACTIONS,\n      forwardToActor: choose([{\n        cond: 'hasActor',\n        actions: forwardTo(({\n          actorRef\n        }) => actorRef)\n      }]),\n      setActorDoneData: assign({\n        actorDoneData: (_, event) => ({\n          challengeName: event.data.challengeName,\n          codeDeliveryDetails: event.data.codeDeliveryDetails,\n          missingAttributes: event.data.missingAttributes,\n          remoteError: event.data.remoteError,\n          username: event.data.username,\n          step: event.data.step,\n          totpSecretCode: event.data.totpSecretCode,\n          unverifiedUserAttributes: event.data.unverifiedUserAttributes,\n          allowedMfaTypes: event.data.allowedMfaTypes\n        })\n      }),\n      applyAmplifyConfig: assign({\n        config(context, {\n          data: cliConfig\n        }) {\n          // Prefer explicitly configured settings over default CLI values\\\n          const {\n            loginMechanisms = cliConfig.loginMechanisms ?? [],\n            signUpAttributes = cliConfig.signUpAttributes ?? [],\n            socialProviders = cliConfig.socialProviders ?? [],\n            initialState,\n            formFields: _formFields,\n            passwordSettings = cliConfig.passwordFormat ?? {}\n          } = context.config;\n          // By default, Cognito assumes `username`, so there isn't a different username attribute like `email`.\n          // We explicitly add it as a login mechanism to be consistent with Admin UI's language.\n          if (loginMechanisms.length === 0) {\n            loginMechanisms.push('username');\n          }\n          const formFields = convertFormFields(_formFields) ?? {};\n          return {\n            formFields,\n            initialState,\n            loginMechanisms,\n            passwordSettings,\n            signUpAttributes,\n            socialProviders\n          };\n        }\n      }),\n      spawnSignInActor: assign({\n        actorRef: (context, _) => {\n          const {\n            services\n          } = context;\n          const actor = signInActor({\n            services\n          }).withContext(getActorContext(context, 'SIGN_IN'));\n          return spawn(actor, {\n            name: 'signInActor'\n          });\n        }\n      }),\n      spawnSignUpActor: assign({\n        actorRef: (context, _) => {\n          const {\n            services\n          } = context;\n          const actor = signUpActor({\n            services\n          }).withContext(getActorContext(context, 'SIGN_UP'));\n          return spawn(actor, {\n            name: 'signUpActor'\n          });\n        }\n      }),\n      spawnForgotPasswordActor: assign({\n        actorRef: (context, _) => {\n          const {\n            services\n          } = context;\n          const actor = forgotPasswordActor({\n            services\n          }).withContext(getActorContext(context, 'FORGOT_PASSWORD'));\n          return spawn(actor, {\n            name: 'forgotPasswordActor'\n          });\n        }\n      }),\n      spawnVerifyUserAttributesActor: assign({\n        actorRef: context => {\n          const actor = verifyUserAttributesActor().withContext(getActorContext(context));\n          return spawn(actor, {\n            name: 'verifyUserAttributesActor'\n          });\n        }\n      }),\n      spawnSignOutActor: assign({\n        actorRef: context => {\n          const actor = signOutActor().withContext({\n            user: context?.user\n          });\n          return spawn(actor, {\n            name: 'signOutActor'\n          });\n        }\n      }),\n      configure: assign((_, event) => {\n        const {\n          services: customServices,\n          ...config\n        } = !isEmptyObject(overrideConfigServices) ? overrideConfigServices : event.data ?? {};\n        return {\n          services: {\n            ...defaultServices,\n            ...customServices\n          },\n          config\n        };\n      }),\n      setHasSetup: assign({\n        hasSetup: true\n      })\n    },\n    guards: {\n      ...GUARDS,\n      hasActor: ({\n        actorRef\n      }) => !!actorRef,\n      isInitialStateSignUp: ({\n        config\n      }) => config.initialState === 'signUp',\n      isInitialStateResetPassword: ({\n        config\n      }) => config.initialState === 'forgotPassword',\n      shouldSetup: ({\n        hasSetup\n      }) => !hasSetup,\n      hasUser: ({\n        user\n      }) => {\n        return !!user;\n      }\n    },\n    services: {\n      getAmplifyConfig: ({\n        services\n      }) => services.getAmplifyConfig(),\n      handleGetCurrentUser: ({\n        services\n      }) => services.getCurrentUser()\n    }\n  });\n}\nfunction convertFormFields(formFields) {\n  if (formFields) {\n    Object.keys(formFields).forEach(component => {\n      Object.keys(formFields[component]).forEach(inputName => {\n        let ff = formFields[component][inputName];\n        ff.required = ff.isRequired;\n      });\n    });\n  }\n  return formFields;\n}\nexport { createAuthenticatorMachine };", "map": {"version": 3, "names": ["actions", "createMachine", "assign", "spawn", "forwardTo", "isEmptyObject", "ACTIONS", "GUARDS", "forgotPasswordActor", "signInActor", "signUpActor", "signOutActor", "verifyUserAttributesActor", "defaultServices", "getActorContext", "context", "defaultStep", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "step", "formValues", "touched", "validationError", "formFields", "config", "loginMechanisms", "passwordSettings", "signUpAttributes", "socialProviders", "choose", "stop", "stopActor", "machineId", "LEGACY_WAIT_CONFIG", "on", "INIT", "target", "SIGN_OUT", "NEXT_WAIT_CONFIG", "always", "createAuthenticatorMachine", "options", "useNextWaitConfig", "overrideConfigServices", "initConfig", "id", "initial", "user", "undefined", "services", "<PERSON><PERSON><PERSON>", "hasSetup", "predictableActionArguments", "states", "idle", "invoke", "src", "onDone", "onError", "setup", "getConfig", "cond", "goToInitialState", "getCurrentUser", "spawnActor", "runActor", "entry", "exit", "FORGOT_PASSWORD", "SIGN_IN", "SIGN_UP", "authenticated", "TOKEN_REFRESH", "refreshUser", "signOut", "SIGN_IN_WITH_REDIRECT", "CHANGE", "BLUR", "SUBMIT", "FEDERATED_SIGN_IN", "RESEND", "SKIP", "forwardTo<PERSON>ctor", "setActorDoneData", "_", "event", "challenge<PERSON>ame", "data", "codeDeliveryDetails", "missingAttributes", "remoteError", "username", "totpSecretCode", "unverifiedUserAttributes", "allowedMfaTypes", "applyAmplifyConfig", "cliConfig", "initialState", "_formFields", "passwordFormat", "length", "push", "convertFormFields", "spawnSignInActor", "actor", "withContext", "name", "spawnSignUpActor", "spawnForgotPasswordActor", "spawnVerifyUserAttributesActor", "spawnSignOutActor", "configure", "customServices", "setHasSetup", "guards", "has<PERSON><PERSON>", "isInitialStateSignUp", "isInitialStateResetPassword", "shouldSetup", "<PERSON><PERSON>ser", "getAmplifyConfig", "handleGetCurrentUser", "Object", "keys", "for<PERSON>ach", "component", "inputName", "ff", "required", "isRequired"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/machines/authenticator/index.mjs"], "sourcesContent": ["import { actions, createMachine, assign, spawn, forwardTo } from 'xstate';\nimport '@aws-amplify/core/internals/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { isEmptyObject } from '../../utils/utils.mjs';\nimport ACTIONS from './actions.mjs';\nimport GUARDS from './guards.mjs';\nimport { forgotPasswordActor } from './actors/forgotPassword.mjs';\nimport { signInActor } from './actors/signIn.mjs';\nimport { signUpActor } from './actors/signUp.mjs';\nimport { signOutActor } from './actors/signOut.mjs';\nimport { verifyUserAttributesActor } from './actors/verifyUserAttributes.mjs';\nimport { defaultServices } from './defaultServices.mjs';\n\nconst getActorContext = (context, defaultStep) => ({\n    ...context.actorDoneData,\n    step: context?.actorDoneData?.step ?? defaultStep,\n    // initialize empty objects on actor start\n    formValues: {},\n    touched: {},\n    validationError: {},\n    // values included on `context.config` that should be available in actors\n    formFields: context.config?.formFields,\n    loginMechanisms: context.config?.loginMechanisms,\n    passwordSettings: context.config?.passwordSettings,\n    signUpAttributes: context.config?.signUpAttributes,\n    socialProviders: context.config?.socialProviders,\n});\nconst { choose, stop } = actions;\nconst stopActor = (machineId) => stop(machineId);\n// setup step waits for ui to emit INIT action to proceed to configure\nconst LEGACY_WAIT_CONFIG = {\n    on: {\n        INIT: {\n            actions: 'configure',\n            target: 'getConfig',\n        },\n        SIGN_OUT: '#authenticator.signOut',\n    },\n};\n// setup step proceeds directly to configure\nconst NEXT_WAIT_CONFIG = {\n    always: { actions: 'configure', target: 'getConfig' },\n};\nfunction createAuthenticatorMachine(options) {\n    const { useNextWaitConfig, ...overrideConfigServices } = options ?? {};\n    const initConfig = useNextWaitConfig ? NEXT_WAIT_CONFIG : LEGACY_WAIT_CONFIG;\n    return createMachine({\n        id: 'authenticator',\n        initial: 'idle',\n        context: {\n            user: undefined,\n            config: {},\n            services: defaultServices,\n            actorRef: undefined,\n            hasSetup: false,\n        },\n        predictableActionArguments: true,\n        states: {\n            // See: https://xstate.js.org/docs/guides/communication.html#invoking-promises\n            idle: {\n                invoke: {\n                    src: 'handleGetCurrentUser',\n                    onDone: { actions: 'setUser', target: 'setup' },\n                    onError: { target: 'setup' },\n                },\n            },\n            setup: {\n                initial: 'initConfig',\n                states: {\n                    initConfig,\n                    getConfig: {\n                        invoke: {\n                            src: 'getAmplifyConfig',\n                            onDone: [\n                                {\n                                    actions: ['applyAmplifyConfig', 'setHasSetup'],\n                                    cond: 'hasUser',\n                                    target: '#authenticator.authenticated',\n                                },\n                                {\n                                    actions: ['applyAmplifyConfig', 'setHasSetup'],\n                                    target: 'goToInitialState',\n                                },\n                            ],\n                        },\n                    },\n                    goToInitialState: {\n                        always: [\n                            {\n                                cond: 'isInitialStateSignUp',\n                                target: '#authenticator.signUpActor',\n                            },\n                            {\n                                cond: 'isInitialStateResetPassword',\n                                target: '#authenticator.forgotPasswordActor',\n                            },\n                            { target: '#authenticator.signInActor' },\n                        ],\n                    },\n                },\n            },\n            getCurrentUser: {\n                invoke: {\n                    src: 'handleGetCurrentUser',\n                    onDone: {\n                        actions: 'setUser',\n                        target: '#authenticator.authenticated',\n                    },\n                    onError: { target: '#authenticator.setup' },\n                },\n            },\n            signInActor: {\n                initial: 'spawnActor',\n                states: {\n                    spawnActor: {\n                        always: { actions: 'spawnSignInActor', target: 'runActor' },\n                    },\n                    runActor: {\n                        entry: 'clearActorDoneData',\n                        exit: stopActor('signInActor'),\n                    },\n                },\n                on: {\n                    FORGOT_PASSWORD: 'forgotPasswordActor',\n                    SIGN_IN: 'signInActor',\n                    SIGN_UP: 'signUpActor',\n                    'done.invoke.signInActor': [\n                        {\n                            cond: 'hasCompletedAttributeConfirmation',\n                            target: '#authenticator.getCurrentUser',\n                        },\n                        {\n                            cond: 'isShouldConfirmUserAttributeStep',\n                            actions: 'setActorDoneData',\n                            target: '#authenticator.verifyUserAttributesActor',\n                        },\n                        {\n                            cond: 'isResetPasswordStep',\n                            actions: 'setActorDoneData',\n                            target: '#authenticator.forgotPasswordActor',\n                        },\n                        {\n                            cond: 'isConfirmSignUpStep',\n                            actions: 'setActorDoneData',\n                            target: '#authenticator.signUpActor',\n                        },\n                    ],\n                },\n            },\n            signUpActor: {\n                initial: 'spawnActor',\n                states: {\n                    spawnActor: {\n                        always: { actions: 'spawnSignUpActor', target: 'runActor' },\n                    },\n                    runActor: {\n                        entry: 'clearActorDoneData',\n                        exit: stopActor('signUpActor'),\n                    },\n                },\n                on: {\n                    SIGN_IN: 'signInActor',\n                    'done.invoke.signUpActor': [\n                        {\n                            cond: 'hasCompletedAttributeConfirmation',\n                            target: '#authenticator.getCurrentUser',\n                        },\n                        {\n                            cond: 'isShouldConfirmUserAttributeStep',\n                            actions: 'setActorDoneData',\n                            target: '#authenticator.verifyUserAttributesActor',\n                        },\n                        {\n                            cond: 'isConfirmUserAttributeStep',\n                            target: '#authenticator.verifyUserAttributesActor',\n                        },\n                        {\n                            actions: 'setActorDoneData',\n                            target: '#authenticator.signInActor',\n                        },\n                    ],\n                },\n            },\n            forgotPasswordActor: {\n                initial: 'spawnActor',\n                states: {\n                    spawnActor: {\n                        always: {\n                            actions: 'spawnForgotPasswordActor',\n                            target: 'runActor',\n                        },\n                    },\n                    runActor: {\n                        entry: 'clearActorDoneData',\n                        exit: stopActor('forgotPasswordActor'),\n                    },\n                },\n                on: {\n                    SIGN_IN: 'signInActor',\n                    'done.invoke.forgotPasswordActor': [\n                        { target: '#authenticator.signInActor' },\n                    ],\n                },\n            },\n            verifyUserAttributesActor: {\n                initial: 'spawnActor',\n                states: {\n                    spawnActor: {\n                        always: {\n                            actions: 'spawnVerifyUserAttributesActor',\n                            target: 'runActor',\n                        },\n                    },\n                    runActor: {\n                        entry: 'clearActorDoneData',\n                        exit: stopActor('verifyUserAttributesActor'),\n                    },\n                },\n                on: {\n                    'done.invoke.verifyUserAttributesActor': [\n                        {\n                            actions: 'setActorDoneData',\n                            target: '#authenticator.getCurrentUser',\n                        },\n                    ],\n                },\n            },\n            authenticated: {\n                initial: 'idle',\n                states: {\n                    idle: { on: { TOKEN_REFRESH: 'refreshUser' } },\n                    refreshUser: {\n                        invoke: {\n                            src: '#authenticator.getCurrentUser',\n                            onDone: { actions: 'setUser', target: 'idle' },\n                            onError: { target: '#authenticator.signOut' },\n                        },\n                    },\n                },\n                on: { SIGN_OUT: 'signOut' },\n            },\n            signOut: {\n                initial: 'spawnActor',\n                states: {\n                    spawnActor: {\n                        always: { actions: 'spawnSignOutActor', target: 'runActor' },\n                    },\n                    runActor: {\n                        entry: 'clearActorDoneData',\n                        exit: stopActor('signOutActor'),\n                    },\n                },\n                on: {\n                    'done.invoke.signOutActor': {\n                        actions: 'clearUser',\n                        target: 'setup.getConfig',\n                    },\n                },\n            },\n        },\n        on: {\n            SIGN_IN_WITH_REDIRECT: { target: '#authenticator.getCurrentUser' },\n            CHANGE: { actions: 'forwardToActor' },\n            BLUR: { actions: 'forwardToActor' },\n            SUBMIT: { actions: 'forwardToActor' },\n            FEDERATED_SIGN_IN: { actions: 'forwardToActor' },\n            RESEND: { actions: 'forwardToActor' },\n            SIGN_IN: { actions: 'forwardToActor' },\n            SKIP: { actions: 'forwardToActor' },\n        },\n    }, {\n        actions: {\n            ...ACTIONS,\n            forwardToActor: choose([\n                { cond: 'hasActor', actions: forwardTo(({ actorRef }) => actorRef) },\n            ]),\n            setActorDoneData: assign({\n                actorDoneData: (_, event) => ({\n                    challengeName: event.data.challengeName,\n                    codeDeliveryDetails: event.data.codeDeliveryDetails,\n                    missingAttributes: event.data.missingAttributes,\n                    remoteError: event.data.remoteError,\n                    username: event.data.username,\n                    step: event.data.step,\n                    totpSecretCode: event.data.totpSecretCode,\n                    unverifiedUserAttributes: event.data.unverifiedUserAttributes,\n                    allowedMfaTypes: event.data.allowedMfaTypes,\n                }),\n            }),\n            applyAmplifyConfig: assign({\n                config(context, { data: cliConfig }) {\n                    // Prefer explicitly configured settings over default CLI values\\\n                    const { loginMechanisms = cliConfig.loginMechanisms ?? [], signUpAttributes = cliConfig.signUpAttributes ?? [], socialProviders = cliConfig.socialProviders ?? [], initialState, formFields: _formFields, passwordSettings = cliConfig.passwordFormat ??\n                        {}, } = context.config;\n                    // By default, Cognito assumes `username`, so there isn't a different username attribute like `email`.\n                    // We explicitly add it as a login mechanism to be consistent with Admin UI's language.\n                    if (loginMechanisms.length === 0) {\n                        loginMechanisms.push('username');\n                    }\n                    const formFields = convertFormFields(_formFields) ?? {};\n                    return {\n                        formFields,\n                        initialState,\n                        loginMechanisms,\n                        passwordSettings,\n                        signUpAttributes,\n                        socialProviders,\n                    };\n                },\n            }),\n            spawnSignInActor: assign({\n                actorRef: (context, _) => {\n                    const { services } = context;\n                    const actor = signInActor({ services }).withContext(getActorContext(context, 'SIGN_IN'));\n                    return spawn(actor, { name: 'signInActor' });\n                },\n            }),\n            spawnSignUpActor: assign({\n                actorRef: (context, _) => {\n                    const { services } = context;\n                    const actor = signUpActor({ services }).withContext(getActorContext(context, 'SIGN_UP'));\n                    return spawn(actor, { name: 'signUpActor' });\n                },\n            }),\n            spawnForgotPasswordActor: assign({\n                actorRef: (context, _) => {\n                    const { services } = context;\n                    const actor = forgotPasswordActor({ services }).withContext(getActorContext(context, 'FORGOT_PASSWORD'));\n                    return spawn(actor, { name: 'forgotPasswordActor' });\n                },\n            }),\n            spawnVerifyUserAttributesActor: assign({\n                actorRef: (context) => {\n                    const actor = verifyUserAttributesActor().withContext(getActorContext(context));\n                    return spawn(actor, { name: 'verifyUserAttributesActor' });\n                },\n            }),\n            spawnSignOutActor: assign({\n                actorRef: (context) => {\n                    const actor = signOutActor().withContext({ user: context?.user });\n                    return spawn(actor, { name: 'signOutActor' });\n                },\n            }),\n            configure: assign((_, event) => {\n                const { services: customServices, ...config } = !isEmptyObject(overrideConfigServices)\n                    ? overrideConfigServices\n                    : event.data ?? {};\n                return {\n                    services: { ...defaultServices, ...customServices },\n                    config,\n                };\n            }),\n            setHasSetup: assign({ hasSetup: true }),\n        },\n        guards: {\n            ...GUARDS,\n            hasActor: ({ actorRef }) => !!actorRef,\n            isInitialStateSignUp: ({ config }) => config.initialState === 'signUp',\n            isInitialStateResetPassword: ({ config }) => config.initialState === 'forgotPassword',\n            shouldSetup: ({ hasSetup }) => !hasSetup,\n            hasUser: ({ user }) => {\n                return !!user;\n            },\n        },\n        services: {\n            getAmplifyConfig: ({ services }) => services.getAmplifyConfig(),\n            handleGetCurrentUser: ({ services }) => services.getCurrentUser(),\n        },\n    });\n}\nfunction convertFormFields(formFields) {\n    if (formFields) {\n        Object.keys(formFields).forEach((component) => {\n            Object.keys(formFields[component]).forEach((inputName) => {\n                let ff = formFields[component][inputName];\n                ff.required = ff.isRequired;\n            });\n        });\n    }\n    return formFields;\n}\n\nexport { createAuthenticatorMachine };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,QAAQ,QAAQ;AACzE,OAAO,mCAAmC;AAC1C,OAAO,wCAAwC;AAC/C,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,MAAM,MAAM,cAAc;AACjC,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,yBAAyB,QAAQ,mCAAmC;AAC7E,SAASC,eAAe,QAAQ,uBAAuB;AAEvD,MAAMC,eAAe,GAAGA,CAACC,OAAO,EAAEC,WAAW,MAAM;EAC/C,GAAGD,OAAO,CAACE,aAAa;EACxBC,IAAI,EAAEH,OAAO,EAAEE,aAAa,EAAEC,IAAI,IAAIF,WAAW;EACjD;EACAG,UAAU,EAAE,CAAC,CAAC;EACdC,OAAO,EAAE,CAAC,CAAC;EACXC,eAAe,EAAE,CAAC,CAAC;EACnB;EACAC,UAAU,EAAEP,OAAO,CAACQ,MAAM,EAAED,UAAU;EACtCE,eAAe,EAAET,OAAO,CAACQ,MAAM,EAAEC,eAAe;EAChDC,gBAAgB,EAAEV,OAAO,CAACQ,MAAM,EAAEE,gBAAgB;EAClDC,gBAAgB,EAAEX,OAAO,CAACQ,MAAM,EAAEG,gBAAgB;EAClDC,eAAe,EAAEZ,OAAO,CAACQ,MAAM,EAAEI;AACrC,CAAC,CAAC;AACF,MAAM;EAAEC,MAAM;EAAEC;AAAK,CAAC,GAAG7B,OAAO;AAChC,MAAM8B,SAAS,GAAIC,SAAS,IAAKF,IAAI,CAACE,SAAS,CAAC;AAChD;AACA,MAAMC,kBAAkB,GAAG;EACvBC,EAAE,EAAE;IACAC,IAAI,EAAE;MACFlC,OAAO,EAAE,WAAW;MACpBmC,MAAM,EAAE;IACZ,CAAC;IACDC,QAAQ,EAAE;EACd;AACJ,CAAC;AACD;AACA,MAAMC,gBAAgB,GAAG;EACrBC,MAAM,EAAE;IAAEtC,OAAO,EAAE,WAAW;IAAEmC,MAAM,EAAE;EAAY;AACxD,CAAC;AACD,SAASI,0BAA0BA,CAACC,OAAO,EAAE;EACzC,MAAM;IAAEC,iBAAiB;IAAE,GAAGC;EAAuB,CAAC,GAAGF,OAAO,IAAI,CAAC,CAAC;EACtE,MAAMG,UAAU,GAAGF,iBAAiB,GAAGJ,gBAAgB,GAAGL,kBAAkB;EAC5E,OAAO/B,aAAa,CAAC;IACjB2C,EAAE,EAAE,eAAe;IACnBC,OAAO,EAAE,MAAM;IACf9B,OAAO,EAAE;MACL+B,IAAI,EAAEC,SAAS;MACfxB,MAAM,EAAE,CAAC,CAAC;MACVyB,QAAQ,EAAEnC,eAAe;MACzBoC,QAAQ,EAAEF,SAAS;MACnBG,QAAQ,EAAE;IACd,CAAC;IACDC,0BAA0B,EAAE,IAAI;IAChCC,MAAM,EAAE;MACJ;MACAC,IAAI,EAAE;QACFC,MAAM,EAAE;UACJC,GAAG,EAAE,sBAAsB;UAC3BC,MAAM,EAAE;YAAExD,OAAO,EAAE,SAAS;YAAEmC,MAAM,EAAE;UAAQ,CAAC;UAC/CsB,OAAO,EAAE;YAAEtB,MAAM,EAAE;UAAQ;QAC/B;MACJ,CAAC;MACDuB,KAAK,EAAE;QACHb,OAAO,EAAE,YAAY;QACrBO,MAAM,EAAE;UACJT,UAAU;UACVgB,SAAS,EAAE;YACPL,MAAM,EAAE;cACJC,GAAG,EAAE,kBAAkB;cACvBC,MAAM,EAAE,CACJ;gBACIxD,OAAO,EAAE,CAAC,oBAAoB,EAAE,aAAa,CAAC;gBAC9C4D,IAAI,EAAE,SAAS;gBACfzB,MAAM,EAAE;cACZ,CAAC,EACD;gBACInC,OAAO,EAAE,CAAC,oBAAoB,EAAE,aAAa,CAAC;gBAC9CmC,MAAM,EAAE;cACZ,CAAC;YAET;UACJ,CAAC;UACD0B,gBAAgB,EAAE;YACdvB,MAAM,EAAE,CACJ;cACIsB,IAAI,EAAE,sBAAsB;cAC5BzB,MAAM,EAAE;YACZ,CAAC,EACD;cACIyB,IAAI,EAAE,6BAA6B;cACnCzB,MAAM,EAAE;YACZ,CAAC,EACD;cAAEA,MAAM,EAAE;YAA6B,CAAC;UAEhD;QACJ;MACJ,CAAC;MACD2B,cAAc,EAAE;QACZR,MAAM,EAAE;UACJC,GAAG,EAAE,sBAAsB;UAC3BC,MAAM,EAAE;YACJxD,OAAO,EAAE,SAAS;YAClBmC,MAAM,EAAE;UACZ,CAAC;UACDsB,OAAO,EAAE;YAAEtB,MAAM,EAAE;UAAuB;QAC9C;MACJ,CAAC;MACD1B,WAAW,EAAE;QACToC,OAAO,EAAE,YAAY;QACrBO,MAAM,EAAE;UACJW,UAAU,EAAE;YACRzB,MAAM,EAAE;cAAEtC,OAAO,EAAE,kBAAkB;cAAEmC,MAAM,EAAE;YAAW;UAC9D,CAAC;UACD6B,QAAQ,EAAE;YACNC,KAAK,EAAE,oBAAoB;YAC3BC,IAAI,EAAEpC,SAAS,CAAC,aAAa;UACjC;QACJ,CAAC;QACDG,EAAE,EAAE;UACAkC,eAAe,EAAE,qBAAqB;UACtCC,OAAO,EAAE,aAAa;UACtBC,OAAO,EAAE,aAAa;UACtB,yBAAyB,EAAE,CACvB;YACIT,IAAI,EAAE,mCAAmC;YACzCzB,MAAM,EAAE;UACZ,CAAC,EACD;YACIyB,IAAI,EAAE,kCAAkC;YACxC5D,OAAO,EAAE,kBAAkB;YAC3BmC,MAAM,EAAE;UACZ,CAAC,EACD;YACIyB,IAAI,EAAE,qBAAqB;YAC3B5D,OAAO,EAAE,kBAAkB;YAC3BmC,MAAM,EAAE;UACZ,CAAC,EACD;YACIyB,IAAI,EAAE,qBAAqB;YAC3B5D,OAAO,EAAE,kBAAkB;YAC3BmC,MAAM,EAAE;UACZ,CAAC;QAET;MACJ,CAAC;MACDzB,WAAW,EAAE;QACTmC,OAAO,EAAE,YAAY;QACrBO,MAAM,EAAE;UACJW,UAAU,EAAE;YACRzB,MAAM,EAAE;cAAEtC,OAAO,EAAE,kBAAkB;cAAEmC,MAAM,EAAE;YAAW;UAC9D,CAAC;UACD6B,QAAQ,EAAE;YACNC,KAAK,EAAE,oBAAoB;YAC3BC,IAAI,EAAEpC,SAAS,CAAC,aAAa;UACjC;QACJ,CAAC;QACDG,EAAE,EAAE;UACAmC,OAAO,EAAE,aAAa;UACtB,yBAAyB,EAAE,CACvB;YACIR,IAAI,EAAE,mCAAmC;YACzCzB,MAAM,EAAE;UACZ,CAAC,EACD;YACIyB,IAAI,EAAE,kCAAkC;YACxC5D,OAAO,EAAE,kBAAkB;YAC3BmC,MAAM,EAAE;UACZ,CAAC,EACD;YACIyB,IAAI,EAAE,4BAA4B;YAClCzB,MAAM,EAAE;UACZ,CAAC,EACD;YACInC,OAAO,EAAE,kBAAkB;YAC3BmC,MAAM,EAAE;UACZ,CAAC;QAET;MACJ,CAAC;MACD3B,mBAAmB,EAAE;QACjBqC,OAAO,EAAE,YAAY;QACrBO,MAAM,EAAE;UACJW,UAAU,EAAE;YACRzB,MAAM,EAAE;cACJtC,OAAO,EAAE,0BAA0B;cACnCmC,MAAM,EAAE;YACZ;UACJ,CAAC;UACD6B,QAAQ,EAAE;YACNC,KAAK,EAAE,oBAAoB;YAC3BC,IAAI,EAAEpC,SAAS,CAAC,qBAAqB;UACzC;QACJ,CAAC;QACDG,EAAE,EAAE;UACAmC,OAAO,EAAE,aAAa;UACtB,iCAAiC,EAAE,CAC/B;YAAEjC,MAAM,EAAE;UAA6B,CAAC;QAEhD;MACJ,CAAC;MACDvB,yBAAyB,EAAE;QACvBiC,OAAO,EAAE,YAAY;QACrBO,MAAM,EAAE;UACJW,UAAU,EAAE;YACRzB,MAAM,EAAE;cACJtC,OAAO,EAAE,gCAAgC;cACzCmC,MAAM,EAAE;YACZ;UACJ,CAAC;UACD6B,QAAQ,EAAE;YACNC,KAAK,EAAE,oBAAoB;YAC3BC,IAAI,EAAEpC,SAAS,CAAC,2BAA2B;UAC/C;QACJ,CAAC;QACDG,EAAE,EAAE;UACA,uCAAuC,EAAE,CACrC;YACIjC,OAAO,EAAE,kBAAkB;YAC3BmC,MAAM,EAAE;UACZ,CAAC;QAET;MACJ,CAAC;MACDmC,aAAa,EAAE;QACXzB,OAAO,EAAE,MAAM;QACfO,MAAM,EAAE;UACJC,IAAI,EAAE;YAAEpB,EAAE,EAAE;cAAEsC,aAAa,EAAE;YAAc;UAAE,CAAC;UAC9CC,WAAW,EAAE;YACTlB,MAAM,EAAE;cACJC,GAAG,EAAE,+BAA+B;cACpCC,MAAM,EAAE;gBAAExD,OAAO,EAAE,SAAS;gBAAEmC,MAAM,EAAE;cAAO,CAAC;cAC9CsB,OAAO,EAAE;gBAAEtB,MAAM,EAAE;cAAyB;YAChD;UACJ;QACJ,CAAC;QACDF,EAAE,EAAE;UAAEG,QAAQ,EAAE;QAAU;MAC9B,CAAC;MACDqC,OAAO,EAAE;QACL5B,OAAO,EAAE,YAAY;QACrBO,MAAM,EAAE;UACJW,UAAU,EAAE;YACRzB,MAAM,EAAE;cAAEtC,OAAO,EAAE,mBAAmB;cAAEmC,MAAM,EAAE;YAAW;UAC/D,CAAC;UACD6B,QAAQ,EAAE;YACNC,KAAK,EAAE,oBAAoB;YAC3BC,IAAI,EAAEpC,SAAS,CAAC,cAAc;UAClC;QACJ,CAAC;QACDG,EAAE,EAAE;UACA,0BAA0B,EAAE;YACxBjC,OAAO,EAAE,WAAW;YACpBmC,MAAM,EAAE;UACZ;QACJ;MACJ;IACJ,CAAC;IACDF,EAAE,EAAE;MACAyC,qBAAqB,EAAE;QAAEvC,MAAM,EAAE;MAAgC,CAAC;MAClEwC,MAAM,EAAE;QAAE3E,OAAO,EAAE;MAAiB,CAAC;MACrC4E,IAAI,EAAE;QAAE5E,OAAO,EAAE;MAAiB,CAAC;MACnC6E,MAAM,EAAE;QAAE7E,OAAO,EAAE;MAAiB,CAAC;MACrC8E,iBAAiB,EAAE;QAAE9E,OAAO,EAAE;MAAiB,CAAC;MAChD+E,MAAM,EAAE;QAAE/E,OAAO,EAAE;MAAiB,CAAC;MACrCoE,OAAO,EAAE;QAAEpE,OAAO,EAAE;MAAiB,CAAC;MACtCgF,IAAI,EAAE;QAAEhF,OAAO,EAAE;MAAiB;IACtC;EACJ,CAAC,EAAE;IACCA,OAAO,EAAE;MACL,GAAGM,OAAO;MACV2E,cAAc,EAAErD,MAAM,CAAC,CACnB;QAAEgC,IAAI,EAAE,UAAU;QAAE5D,OAAO,EAAEI,SAAS,CAAC,CAAC;UAAE6C;QAAS,CAAC,KAAKA,QAAQ;MAAE,CAAC,CACvE,CAAC;MACFiC,gBAAgB,EAAEhF,MAAM,CAAC;QACrBe,aAAa,EAAEA,CAACkE,CAAC,EAAEC,KAAK,MAAM;UAC1BC,aAAa,EAAED,KAAK,CAACE,IAAI,CAACD,aAAa;UACvCE,mBAAmB,EAAEH,KAAK,CAACE,IAAI,CAACC,mBAAmB;UACnDC,iBAAiB,EAAEJ,KAAK,CAACE,IAAI,CAACE,iBAAiB;UAC/CC,WAAW,EAAEL,KAAK,CAACE,IAAI,CAACG,WAAW;UACnCC,QAAQ,EAAEN,KAAK,CAACE,IAAI,CAACI,QAAQ;UAC7BxE,IAAI,EAAEkE,KAAK,CAACE,IAAI,CAACpE,IAAI;UACrByE,cAAc,EAAEP,KAAK,CAACE,IAAI,CAACK,cAAc;UACzCC,wBAAwB,EAAER,KAAK,CAACE,IAAI,CAACM,wBAAwB;UAC7DC,eAAe,EAAET,KAAK,CAACE,IAAI,CAACO;QAChC,CAAC;MACL,CAAC,CAAC;MACFC,kBAAkB,EAAE5F,MAAM,CAAC;QACvBqB,MAAMA,CAACR,OAAO,EAAE;UAAEuE,IAAI,EAAES;QAAU,CAAC,EAAE;UACjC;UACA,MAAM;YAAEvE,eAAe,GAAGuE,SAAS,CAACvE,eAAe,IAAI,EAAE;YAAEE,gBAAgB,GAAGqE,SAAS,CAACrE,gBAAgB,IAAI,EAAE;YAAEC,eAAe,GAAGoE,SAAS,CAACpE,eAAe,IAAI,EAAE;YAAEqE,YAAY;YAAE1E,UAAU,EAAE2E,WAAW;YAAExE,gBAAgB,GAAGsE,SAAS,CAACG,cAAc,IACjP,CAAC;UAAG,CAAC,GAAGnF,OAAO,CAACQ,MAAM;UAC1B;UACA;UACA,IAAIC,eAAe,CAAC2E,MAAM,KAAK,CAAC,EAAE;YAC9B3E,eAAe,CAAC4E,IAAI,CAAC,UAAU,CAAC;UACpC;UACA,MAAM9E,UAAU,GAAG+E,iBAAiB,CAACJ,WAAW,CAAC,IAAI,CAAC,CAAC;UACvD,OAAO;YACH3E,UAAU;YACV0E,YAAY;YACZxE,eAAe;YACfC,gBAAgB;YAChBC,gBAAgB;YAChBC;UACJ,CAAC;QACL;MACJ,CAAC,CAAC;MACF2E,gBAAgB,EAAEpG,MAAM,CAAC;QACrB+C,QAAQ,EAAEA,CAAClC,OAAO,EAAEoE,CAAC,KAAK;UACtB,MAAM;YAAEnC;UAAS,CAAC,GAAGjC,OAAO;UAC5B,MAAMwF,KAAK,GAAG9F,WAAW,CAAC;YAAEuC;UAAS,CAAC,CAAC,CAACwD,WAAW,CAAC1F,eAAe,CAACC,OAAO,EAAE,SAAS,CAAC,CAAC;UACxF,OAAOZ,KAAK,CAACoG,KAAK,EAAE;YAAEE,IAAI,EAAE;UAAc,CAAC,CAAC;QAChD;MACJ,CAAC,CAAC;MACFC,gBAAgB,EAAExG,MAAM,CAAC;QACrB+C,QAAQ,EAAEA,CAAClC,OAAO,EAAEoE,CAAC,KAAK;UACtB,MAAM;YAAEnC;UAAS,CAAC,GAAGjC,OAAO;UAC5B,MAAMwF,KAAK,GAAG7F,WAAW,CAAC;YAAEsC;UAAS,CAAC,CAAC,CAACwD,WAAW,CAAC1F,eAAe,CAACC,OAAO,EAAE,SAAS,CAAC,CAAC;UACxF,OAAOZ,KAAK,CAACoG,KAAK,EAAE;YAAEE,IAAI,EAAE;UAAc,CAAC,CAAC;QAChD;MACJ,CAAC,CAAC;MACFE,wBAAwB,EAAEzG,MAAM,CAAC;QAC7B+C,QAAQ,EAAEA,CAAClC,OAAO,EAAEoE,CAAC,KAAK;UACtB,MAAM;YAAEnC;UAAS,CAAC,GAAGjC,OAAO;UAC5B,MAAMwF,KAAK,GAAG/F,mBAAmB,CAAC;YAAEwC;UAAS,CAAC,CAAC,CAACwD,WAAW,CAAC1F,eAAe,CAACC,OAAO,EAAE,iBAAiB,CAAC,CAAC;UACxG,OAAOZ,KAAK,CAACoG,KAAK,EAAE;YAAEE,IAAI,EAAE;UAAsB,CAAC,CAAC;QACxD;MACJ,CAAC,CAAC;MACFG,8BAA8B,EAAE1G,MAAM,CAAC;QACnC+C,QAAQ,EAAGlC,OAAO,IAAK;UACnB,MAAMwF,KAAK,GAAG3F,yBAAyB,CAAC,CAAC,CAAC4F,WAAW,CAAC1F,eAAe,CAACC,OAAO,CAAC,CAAC;UAC/E,OAAOZ,KAAK,CAACoG,KAAK,EAAE;YAAEE,IAAI,EAAE;UAA4B,CAAC,CAAC;QAC9D;MACJ,CAAC,CAAC;MACFI,iBAAiB,EAAE3G,MAAM,CAAC;QACtB+C,QAAQ,EAAGlC,OAAO,IAAK;UACnB,MAAMwF,KAAK,GAAG5F,YAAY,CAAC,CAAC,CAAC6F,WAAW,CAAC;YAAE1D,IAAI,EAAE/B,OAAO,EAAE+B;UAAK,CAAC,CAAC;UACjE,OAAO3C,KAAK,CAACoG,KAAK,EAAE;YAAEE,IAAI,EAAE;UAAe,CAAC,CAAC;QACjD;MACJ,CAAC,CAAC;MACFK,SAAS,EAAE5G,MAAM,CAAC,CAACiF,CAAC,EAAEC,KAAK,KAAK;QAC5B,MAAM;UAAEpC,QAAQ,EAAE+D,cAAc;UAAE,GAAGxF;QAAO,CAAC,GAAG,CAAClB,aAAa,CAACqC,sBAAsB,CAAC,GAChFA,sBAAsB,GACtB0C,KAAK,CAACE,IAAI,IAAI,CAAC,CAAC;QACtB,OAAO;UACHtC,QAAQ,EAAE;YAAE,GAAGnC,eAAe;YAAE,GAAGkG;UAAe,CAAC;UACnDxF;QACJ,CAAC;MACL,CAAC,CAAC;MACFyF,WAAW,EAAE9G,MAAM,CAAC;QAAEgD,QAAQ,EAAE;MAAK,CAAC;IAC1C,CAAC;IACD+D,MAAM,EAAE;MACJ,GAAG1G,MAAM;MACT2G,QAAQ,EAAEA,CAAC;QAAEjE;MAAS,CAAC,KAAK,CAAC,CAACA,QAAQ;MACtCkE,oBAAoB,EAAEA,CAAC;QAAE5F;MAAO,CAAC,KAAKA,MAAM,CAACyE,YAAY,KAAK,QAAQ;MACtEoB,2BAA2B,EAAEA,CAAC;QAAE7F;MAAO,CAAC,KAAKA,MAAM,CAACyE,YAAY,KAAK,gBAAgB;MACrFqB,WAAW,EAAEA,CAAC;QAAEnE;MAAS,CAAC,KAAK,CAACA,QAAQ;MACxCoE,OAAO,EAAEA,CAAC;QAAExE;MAAK,CAAC,KAAK;QACnB,OAAO,CAAC,CAACA,IAAI;MACjB;IACJ,CAAC;IACDE,QAAQ,EAAE;MACNuE,gBAAgB,EAAEA,CAAC;QAAEvE;MAAS,CAAC,KAAKA,QAAQ,CAACuE,gBAAgB,CAAC,CAAC;MAC/DC,oBAAoB,EAAEA,CAAC;QAAExE;MAAS,CAAC,KAAKA,QAAQ,CAACc,cAAc,CAAC;IACpE;EACJ,CAAC,CAAC;AACN;AACA,SAASuC,iBAAiBA,CAAC/E,UAAU,EAAE;EACnC,IAAIA,UAAU,EAAE;IACZmG,MAAM,CAACC,IAAI,CAACpG,UAAU,CAAC,CAACqG,OAAO,CAAEC,SAAS,IAAK;MAC3CH,MAAM,CAACC,IAAI,CAACpG,UAAU,CAACsG,SAAS,CAAC,CAAC,CAACD,OAAO,CAAEE,SAAS,IAAK;QACtD,IAAIC,EAAE,GAAGxG,UAAU,CAACsG,SAAS,CAAC,CAACC,SAAS,CAAC;QACzCC,EAAE,CAACC,QAAQ,GAAGD,EAAE,CAACE,UAAU;MAC/B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA,OAAO1G,UAAU;AACrB;AAEA,SAASiB,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}