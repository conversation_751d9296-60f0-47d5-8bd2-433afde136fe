{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nclass AuthClass {\n  /**\n   * Configure Auth category\n   *\n   * @internal\n   *\n   * @param authResourcesConfig - Resources configurations required by Auth providers.\n   * @param authOptions - Client options used by library\n   *\n   * @returns void\n   */\n  configure(authResourcesConfig, authOptions) {\n    this.authConfig = authResourcesConfig;\n    this.authOptions = authOptions;\n  }\n  /**\n   * Fetch the auth tokens, and the temporary AWS credentials and identity if they are configured. By default it\n   * does not refresh the auth tokens or credentials if they are loaded in storage already. You can force a refresh\n   * with `{ forceRefresh: true }` input.\n   *\n   * @param options - Options configuring the fetch behavior.\n   *\n   * @returns Promise of current auth session {@link AuthSession}.\n   */\n  fetchAuthSession(options = {}) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      let credentialsAndIdentityId;\n      let userSub;\n      // Get tokens will throw if session cannot be refreshed (network or service error) or return null if not available\n      const tokens = yield _this.getTokens(options);\n      if (tokens) {\n        userSub = tokens.accessToken?.payload?.sub;\n        // getCredentialsAndIdentityId will throw if cannot get credentials (network or service error)\n        credentialsAndIdentityId = yield _this.authOptions?.credentialsProvider?.getCredentialsAndIdentityId({\n          authConfig: _this.authConfig,\n          tokens,\n          authenticated: true,\n          forceRefresh: options.forceRefresh\n        });\n      } else {\n        // getCredentialsAndIdentityId will throw if cannot get credentials (network or service error)\n        credentialsAndIdentityId = yield _this.authOptions?.credentialsProvider?.getCredentialsAndIdentityId({\n          authConfig: _this.authConfig,\n          authenticated: false,\n          forceRefresh: options.forceRefresh\n        });\n      }\n      return {\n        tokens,\n        credentials: credentialsAndIdentityId?.credentials,\n        identityId: credentialsAndIdentityId?.identityId,\n        userSub\n      };\n    })();\n  }\n  clearCredentials() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      yield _this2.authOptions?.credentialsProvider?.clearCredentialsAndIdentityId();\n    })();\n  }\n  getTokens(options) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      return (yield _this3.authOptions?.tokenProvider?.getTokens(options)) ?? undefined;\n    })();\n  }\n}\nexport { AuthClass };", "map": {"version": 3, "names": ["AuthClass", "configure", "authResourcesConfig", "authOptions", "authConfig", "fetchAuthSession", "options", "_this", "_asyncToGenerator", "credentialsAndIdentityId", "userSub", "tokens", "getTokens", "accessToken", "payload", "sub", "<PERSON><PERSON><PERSON><PERSON>", "getCredentialsAndIdentityId", "authenticated", "forceRefresh", "credentials", "identityId", "clearCredentials", "_this2", "clearCredentialsAndIdentityId", "_this3", "tokenProvider", "undefined"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/singleton/Auth/index.mjs"], "sourcesContent": ["class AuthClass {\n    /**\n     * Configure Auth category\n     *\n     * @internal\n     *\n     * @param authResourcesConfig - Resources configurations required by Auth providers.\n     * @param authOptions - Client options used by library\n     *\n     * @returns void\n     */\n    configure(authResourcesConfig, authOptions) {\n        this.authConfig = authResourcesConfig;\n        this.authOptions = authOptions;\n    }\n    /**\n     * Fetch the auth tokens, and the temporary AWS credentials and identity if they are configured. By default it\n     * does not refresh the auth tokens or credentials if they are loaded in storage already. You can force a refresh\n     * with `{ forceRefresh: true }` input.\n     *\n     * @param options - Options configuring the fetch behavior.\n     *\n     * @returns Promise of current auth session {@link AuthSession}.\n     */\n    async fetchAuthSession(options = {}) {\n        let credentialsAndIdentityId;\n        let userSub;\n        // Get tokens will throw if session cannot be refreshed (network or service error) or return null if not available\n        const tokens = await this.getTokens(options);\n        if (tokens) {\n            userSub = tokens.accessToken?.payload?.sub;\n            // getCredentialsAndIdentityId will throw if cannot get credentials (network or service error)\n            credentialsAndIdentityId =\n                await this.authOptions?.credentialsProvider?.getCredentialsAndIdentityId({\n                    authConfig: this.authConfig,\n                    tokens,\n                    authenticated: true,\n                    forceRefresh: options.forceRefresh,\n                });\n        }\n        else {\n            // getCredentialsAndIdentityId will throw if cannot get credentials (network or service error)\n            credentialsAndIdentityId =\n                await this.authOptions?.credentialsProvider?.getCredentialsAndIdentityId({\n                    authConfig: this.authConfig,\n                    authenticated: false,\n                    forceRefresh: options.forceRefresh,\n                });\n        }\n        return {\n            tokens,\n            credentials: credentialsAndIdentityId?.credentials,\n            identityId: credentialsAndIdentityId?.identityId,\n            userSub,\n        };\n    }\n    async clearCredentials() {\n        await this.authOptions?.credentialsProvider?.clearCredentialsAndIdentityId();\n    }\n    async getTokens(options) {\n        return ((await this.authOptions?.tokenProvider?.getTokens(options)) ?? undefined);\n    }\n}\n\nexport { AuthClass };\n"], "mappings": ";AAAA,MAAMA,SAAS,CAAC;EACZ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,SAASA,CAACC,mBAAmB,EAAEC,WAAW,EAAE;IACxC,IAAI,CAACC,UAAU,GAAGF,mBAAmB;IACrC,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUE,gBAAgBA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACjC,IAAIC,wBAAwB;MAC5B,IAAIC,OAAO;MACX;MACA,MAAMC,MAAM,SAASJ,KAAI,CAACK,SAAS,CAACN,OAAO,CAAC;MAC5C,IAAIK,MAAM,EAAE;QACRD,OAAO,GAAGC,MAAM,CAACE,WAAW,EAAEC,OAAO,EAAEC,GAAG;QAC1C;QACAN,wBAAwB,SACdF,KAAI,CAACJ,WAAW,EAAEa,mBAAmB,EAAEC,2BAA2B,CAAC;UACrEb,UAAU,EAAEG,KAAI,CAACH,UAAU;UAC3BO,MAAM;UACNO,aAAa,EAAE,IAAI;UACnBC,YAAY,EAAEb,OAAO,CAACa;QAC1B,CAAC,CAAC;MACV,CAAC,MACI;QACD;QACAV,wBAAwB,SACdF,KAAI,CAACJ,WAAW,EAAEa,mBAAmB,EAAEC,2BAA2B,CAAC;UACrEb,UAAU,EAAEG,KAAI,CAACH,UAAU;UAC3Bc,aAAa,EAAE,KAAK;UACpBC,YAAY,EAAEb,OAAO,CAACa;QAC1B,CAAC,CAAC;MACV;MACA,OAAO;QACHR,MAAM;QACNS,WAAW,EAAEX,wBAAwB,EAAEW,WAAW;QAClDC,UAAU,EAAEZ,wBAAwB,EAAEY,UAAU;QAChDX;MACJ,CAAC;IAAC;EACN;EACMY,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAf,iBAAA;MACrB,MAAMe,MAAI,CAACpB,WAAW,EAAEa,mBAAmB,EAAEQ,6BAA6B,CAAC,CAAC;IAAC;EACjF;EACMZ,SAASA,CAACN,OAAO,EAAE;IAAA,IAAAmB,MAAA;IAAA,OAAAjB,iBAAA;MACrB,OAAQ,OAAOiB,MAAI,CAACtB,WAAW,EAAEuB,aAAa,EAAEd,SAAS,CAACN,OAAO,CAAC,KAAKqB,SAAS;IAAE;EACtF;AACJ;AAEA,SAAS3B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}