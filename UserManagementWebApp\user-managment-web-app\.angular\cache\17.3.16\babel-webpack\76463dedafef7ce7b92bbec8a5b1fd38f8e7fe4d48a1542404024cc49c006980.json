{"ast": null, "code": "import { Amplify } from 'aws-amplify';\nimport '@aws-amplify/core/internals/utils';\nimport 'aws-amplify/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport '../../types/authenticator/user.mjs';\nimport '../../types/authenticator/attributes.mjs';\nimport { hasSpecialChars } from '../authenticator/utils.mjs';\n\n// gets password requirement from Amplify.configure data\nconst getPasswordRequirement = () => {\n  const config = Amplify.getConfig();\n  const passwordSettings = config?.Auth?.Cognito.passwordFormat;\n  if (!passwordSettings) {\n    return null;\n  }\n  return {\n    minLength: passwordSettings.minLength,\n    needsLowerCase: passwordSettings.requireLowercase ?? false,\n    needsUpperCase: passwordSettings.requireUppercase ?? false,\n    needsNumber: passwordSettings.requireNumbers ?? false,\n    needsSpecialChar: passwordSettings.requireSpecialCharacters ?? false\n  };\n};\nconst getHasMinLength = minLength => ({\n  validationMode: 'onTouched',\n  validator: field => field.length >= minLength,\n  message: `Password must have at least ${minLength} characters`\n});\nconst hasLowerCase = {\n  validationMode: 'onTouched',\n  validator: field => /[a-z]/.test(field),\n  message: 'Password must have lower case letters'\n};\nconst hasUpperCase = {\n  validationMode: 'onTouched',\n  validator: field => /[A-Z]/.test(field),\n  message: 'Password must have upper case letters'\n};\nconst hasNumber = {\n  validationMode: 'onTouched',\n  validator: field => /[0-9]/.test(field),\n  message: 'Password must have numbers'\n};\nconst hasSpecialChar = {\n  validationMode: 'onTouched',\n  validator: field => hasSpecialChars(field),\n  message: 'Password must have special characters'\n};\nconst getMatchesConfirmPassword = password => {\n  return {\n    validationMode: 'onTouched',\n    validator: confirmPassword => password === confirmPassword,\n    message: 'Your passwords must match'\n  };\n};\nconst getDefaultPasswordValidators = () => {\n  const requirement = getPasswordRequirement();\n  if (!requirement) return [];\n  const validators = [];\n  const {\n    minLength,\n    needsLowerCase,\n    needsUpperCase,\n    needsNumber,\n    needsSpecialChar\n  } = requirement;\n  if (minLength) {\n    validators.push(getHasMinLength(minLength));\n  }\n  if (needsLowerCase) {\n    validators.push(hasLowerCase);\n  }\n  if (needsUpperCase) {\n    validators.push(hasUpperCase);\n  }\n  if (needsNumber) {\n    validators.push(hasNumber);\n  }\n  if (needsSpecialChar) {\n    validators.push(hasSpecialChar);\n  }\n  return validators;\n};\nconst getDefaultConfirmPasswordValidators = password => {\n  return [getMatchesConfirmPassword(password)];\n};\n/*\n * `shouldValidate` determines whether validator should be run, based on validation mode,\n * input event type, and whether it has been blurred yet.\n */\nconst shouldValidate = ({\n  validationMode,\n  eventType,\n  hasBlurred\n}) => {\n  switch (validationMode) {\n    case 'onBlur':\n      {\n        // only run validator on blur event\n        return eventType === 'blur';\n      }\n    case 'onChange':\n      {\n        // only run validator on change event\n        return eventType === 'change';\n      }\n    case 'onTouched':\n      {\n        /**\n         * run validator on first blur event, and then every subsequent\n         * blur/change event.\n         */\n        return eventType === 'blur' || hasBlurred;\n      }\n  }\n};\n// `runFieldValidator` runs all validators, and returns error messages.\nconst runFieldValidators = ({\n  value,\n  validators,\n  eventType,\n  hasBlurred\n}) => {\n  if (!value) return [];\n  return validators.reduce((prevErrors, validatorSpec) => {\n    const {\n      validator,\n      validationMode,\n      message\n    } = validatorSpec;\n    if (shouldValidate({\n      validationMode,\n      eventType,\n      hasBlurred\n    })) {\n      const hasError = !validator(value);\n      return hasError ? [...prevErrors, message] : prevErrors;\n    }\n    return prevErrors;\n  }, []);\n};\nexport { getDefaultConfirmPasswordValidators, getDefaultPasswordValidators, getHasMinLength, getMatchesConfirmPassword, getPasswordRequirement, hasLowerCase, hasNumber, hasSpecialChar, hasUpperCase, runFieldValidators, shouldValidate };", "map": {"version": 3, "names": ["Amplify", "hasSpecialChars", "getPasswordRequirement", "config", "getConfig", "passwordSettings", "<PERSON><PERSON>", "Cognito", "passwordFormat", "<PERSON><PERSON><PERSON><PERSON>", "needsLowerCase", "requireLowercase", "needsUpperCase", "requireUppercase", "needsNumber", "requireNumbers", "needsSpecialChar", "requireSpecialCharacters", "getHasMinLength", "validationMode", "validator", "field", "length", "message", "hasLowerCase", "test", "hasUpperCase", "hasNumber", "hasSpecialChar", "getMatchesConfirmPassword", "password", "confirmPassword", "getDefaultPasswordValidators", "requirement", "validators", "push", "getDefaultConfirmPasswordValidators", "shouldValidate", "eventType", "hasBlurred", "runFieldValidators", "value", "reduce", "prevErrors", "validatorSpec", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/helpers/accountSettings/validator.mjs"], "sourcesContent": ["import { Amplify } from 'aws-amplify';\nimport '@aws-amplify/core/internals/utils';\nimport 'aws-amplify/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport '../../types/authenticator/user.mjs';\nimport '../../types/authenticator/attributes.mjs';\nimport { hasSpecialChars } from '../authenticator/utils.mjs';\n\n// gets password requirement from Amplify.configure data\nconst getPasswordRequirement = () => {\n    const config = Amplify.getConfig();\n    const passwordSettings = config?.Auth?.Cognito\n        .passwordFormat;\n    if (!passwordSettings) {\n        return null;\n    }\n    return {\n        minLength: passwordSettings.minLength,\n        needsLowerCase: passwordSettings.requireLowercase ?? false,\n        needsUpperCase: passwordSettings.requireUppercase ?? false,\n        needsNumber: passwordSettings.requireNumbers ?? false,\n        needsSpecialChar: passwordSettings.requireSpecialCharacters ?? false,\n    };\n};\nconst getHasMinLength = (minLength) => ({\n    validationMode: 'onTouched',\n    validator: (field) => field.length >= minLength,\n    message: `Password must have at least ${minLength} characters`,\n});\nconst hasLowerCase = {\n    validationMode: 'onTouched',\n    validator: (field) => /[a-z]/.test(field),\n    message: 'Password must have lower case letters',\n};\nconst hasUpperCase = {\n    validationMode: 'onTouched',\n    validator: (field) => /[A-Z]/.test(field),\n    message: 'Password must have upper case letters',\n};\nconst hasNumber = {\n    validationMode: 'onTouched',\n    validator: (field) => /[0-9]/.test(field),\n    message: 'Password must have numbers',\n};\nconst hasSpecialChar = {\n    validationMode: 'onTouched',\n    validator: (field) => hasSpecialChars(field),\n    message: 'Password must have special characters',\n};\nconst getMatchesConfirmPassword = (password) => {\n    return {\n        validationMode: 'onTouched',\n        validator: (confirmPassword) => password === confirmPassword,\n        message: 'Your passwords must match',\n    };\n};\nconst getDefaultPasswordValidators = () => {\n    const requirement = getPasswordRequirement();\n    if (!requirement)\n        return [];\n    const validators = [];\n    const { minLength, needsLowerCase, needsUpperCase, needsNumber, needsSpecialChar, } = requirement;\n    if (minLength) {\n        validators.push(getHasMinLength(minLength));\n    }\n    if (needsLowerCase) {\n        validators.push(hasLowerCase);\n    }\n    if (needsUpperCase) {\n        validators.push(hasUpperCase);\n    }\n    if (needsNumber) {\n        validators.push(hasNumber);\n    }\n    if (needsSpecialChar) {\n        validators.push(hasSpecialChar);\n    }\n    return validators;\n};\nconst getDefaultConfirmPasswordValidators = (password) => {\n    return [getMatchesConfirmPassword(password)];\n};\n/*\n * `shouldValidate` determines whether validator should be run, based on validation mode,\n * input event type, and whether it has been blurred yet.\n */\nconst shouldValidate = ({ validationMode, eventType, hasBlurred, }) => {\n    switch (validationMode) {\n        case 'onBlur': {\n            // only run validator on blur event\n            return eventType === 'blur';\n        }\n        case 'onChange': {\n            // only run validator on change event\n            return eventType === 'change';\n        }\n        case 'onTouched': {\n            /**\n             * run validator on first blur event, and then every subsequent\n             * blur/change event.\n             */\n            return eventType === 'blur' || hasBlurred;\n        }\n    }\n};\n// `runFieldValidator` runs all validators, and returns error messages.\nconst runFieldValidators = ({ value, validators, eventType, hasBlurred, }) => {\n    if (!value)\n        return [];\n    return validators.reduce((prevErrors, validatorSpec) => {\n        const { validator, validationMode, message } = validatorSpec;\n        if (shouldValidate({ validationMode, eventType, hasBlurred })) {\n            const hasError = !validator(value);\n            return hasError ? [...prevErrors, message] : prevErrors;\n        }\n        return prevErrors;\n    }, []);\n};\n\nexport { getDefaultConfirmPasswordValidators, getDefaultPasswordValidators, getHasMinLength, getMatchesConfirmPassword, getPasswordRequirement, hasLowerCase, hasNumber, hasSpecialChar, hasUpperCase, runFieldValidators, shouldValidate };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,aAAa;AACrC,OAAO,mCAAmC;AAC1C,OAAO,mBAAmB;AAC1B,OAAO,wCAAwC;AAC/C,OAAO,oCAAoC;AAC3C,OAAO,0CAA0C;AACjD,SAASC,eAAe,QAAQ,4BAA4B;;AAE5D;AACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;EACjC,MAAMC,MAAM,GAAGH,OAAO,CAACI,SAAS,CAAC,CAAC;EAClC,MAAMC,gBAAgB,GAAGF,MAAM,EAAEG,IAAI,EAAEC,OAAO,CACzCC,cAAc;EACnB,IAAI,CAACH,gBAAgB,EAAE;IACnB,OAAO,IAAI;EACf;EACA,OAAO;IACHI,SAAS,EAAEJ,gBAAgB,CAACI,SAAS;IACrCC,cAAc,EAAEL,gBAAgB,CAACM,gBAAgB,IAAI,KAAK;IAC1DC,cAAc,EAAEP,gBAAgB,CAACQ,gBAAgB,IAAI,KAAK;IAC1DC,WAAW,EAAET,gBAAgB,CAACU,cAAc,IAAI,KAAK;IACrDC,gBAAgB,EAAEX,gBAAgB,CAACY,wBAAwB,IAAI;EACnE,CAAC;AACL,CAAC;AACD,MAAMC,eAAe,GAAIT,SAAS,KAAM;EACpCU,cAAc,EAAE,WAAW;EAC3BC,SAAS,EAAGC,KAAK,IAAKA,KAAK,CAACC,MAAM,IAAIb,SAAS;EAC/Cc,OAAO,EAAE,+BAA+Bd,SAAS;AACrD,CAAC,CAAC;AACF,MAAMe,YAAY,GAAG;EACjBL,cAAc,EAAE,WAAW;EAC3BC,SAAS,EAAGC,KAAK,IAAK,OAAO,CAACI,IAAI,CAACJ,KAAK,CAAC;EACzCE,OAAO,EAAE;AACb,CAAC;AACD,MAAMG,YAAY,GAAG;EACjBP,cAAc,EAAE,WAAW;EAC3BC,SAAS,EAAGC,KAAK,IAAK,OAAO,CAACI,IAAI,CAACJ,KAAK,CAAC;EACzCE,OAAO,EAAE;AACb,CAAC;AACD,MAAMI,SAAS,GAAG;EACdR,cAAc,EAAE,WAAW;EAC3BC,SAAS,EAAGC,KAAK,IAAK,OAAO,CAACI,IAAI,CAACJ,KAAK,CAAC;EACzCE,OAAO,EAAE;AACb,CAAC;AACD,MAAMK,cAAc,GAAG;EACnBT,cAAc,EAAE,WAAW;EAC3BC,SAAS,EAAGC,KAAK,IAAKpB,eAAe,CAACoB,KAAK,CAAC;EAC5CE,OAAO,EAAE;AACb,CAAC;AACD,MAAMM,yBAAyB,GAAIC,QAAQ,IAAK;EAC5C,OAAO;IACHX,cAAc,EAAE,WAAW;IAC3BC,SAAS,EAAGW,eAAe,IAAKD,QAAQ,KAAKC,eAAe;IAC5DR,OAAO,EAAE;EACb,CAAC;AACL,CAAC;AACD,MAAMS,4BAA4B,GAAGA,CAAA,KAAM;EACvC,MAAMC,WAAW,GAAG/B,sBAAsB,CAAC,CAAC;EAC5C,IAAI,CAAC+B,WAAW,EACZ,OAAO,EAAE;EACb,MAAMC,UAAU,GAAG,EAAE;EACrB,MAAM;IAAEzB,SAAS;IAAEC,cAAc;IAAEE,cAAc;IAAEE,WAAW;IAAEE;EAAkB,CAAC,GAAGiB,WAAW;EACjG,IAAIxB,SAAS,EAAE;IACXyB,UAAU,CAACC,IAAI,CAACjB,eAAe,CAACT,SAAS,CAAC,CAAC;EAC/C;EACA,IAAIC,cAAc,EAAE;IAChBwB,UAAU,CAACC,IAAI,CAACX,YAAY,CAAC;EACjC;EACA,IAAIZ,cAAc,EAAE;IAChBsB,UAAU,CAACC,IAAI,CAACT,YAAY,CAAC;EACjC;EACA,IAAIZ,WAAW,EAAE;IACboB,UAAU,CAACC,IAAI,CAACR,SAAS,CAAC;EAC9B;EACA,IAAIX,gBAAgB,EAAE;IAClBkB,UAAU,CAACC,IAAI,CAACP,cAAc,CAAC;EACnC;EACA,OAAOM,UAAU;AACrB,CAAC;AACD,MAAME,mCAAmC,GAAIN,QAAQ,IAAK;EACtD,OAAO,CAACD,yBAAyB,CAACC,QAAQ,CAAC,CAAC;AAChD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMO,cAAc,GAAGA,CAAC;EAAElB,cAAc;EAAEmB,SAAS;EAAEC;AAAY,CAAC,KAAK;EACnE,QAAQpB,cAAc;IAClB,KAAK,QAAQ;MAAE;QACX;QACA,OAAOmB,SAAS,KAAK,MAAM;MAC/B;IACA,KAAK,UAAU;MAAE;QACb;QACA,OAAOA,SAAS,KAAK,QAAQ;MACjC;IACA,KAAK,WAAW;MAAE;QACd;AACZ;AACA;AACA;QACY,OAAOA,SAAS,KAAK,MAAM,IAAIC,UAAU;MAC7C;EACJ;AACJ,CAAC;AACD;AACA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,KAAK;EAAEP,UAAU;EAAEI,SAAS;EAAEC;AAAY,CAAC,KAAK;EAC1E,IAAI,CAACE,KAAK,EACN,OAAO,EAAE;EACb,OAAOP,UAAU,CAACQ,MAAM,CAAC,CAACC,UAAU,EAAEC,aAAa,KAAK;IACpD,MAAM;MAAExB,SAAS;MAAED,cAAc;MAAEI;IAAQ,CAAC,GAAGqB,aAAa;IAC5D,IAAIP,cAAc,CAAC;MAAElB,cAAc;MAAEmB,SAAS;MAAEC;IAAW,CAAC,CAAC,EAAE;MAC3D,MAAMM,QAAQ,GAAG,CAACzB,SAAS,CAACqB,KAAK,CAAC;MAClC,OAAOI,QAAQ,GAAG,CAAC,GAAGF,UAAU,EAAEpB,OAAO,CAAC,GAAGoB,UAAU;IAC3D;IACA,OAAOA,UAAU;EACrB,CAAC,EAAE,EAAE,CAAC;AACV,CAAC;AAED,SAASP,mCAAmC,EAAEJ,4BAA4B,EAAEd,eAAe,EAAEW,yBAAyB,EAAE3B,sBAAsB,EAAEsB,YAAY,EAAEG,SAAS,EAAEC,cAAc,EAAEF,YAAY,EAAEc,kBAAkB,EAAEH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}