{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Hub } from '@aws-amplify/core';\nimport { AMPLIFY_SYMBOL } from '@aws-amplify/core/internals/utils';\nimport { getCurrentUser } from '../apis/getCurrentUser.mjs';\nimport { USER_UNAUTHENTICATED_EXCEPTION, UNEXPECTED_SIGN_IN_INTERRUPTION_EXCEPTION } from '../../../errors/constants.mjs';\nimport { AuthError } from '../../../errors/AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst ERROR_MESSAGE = 'Unable to get user session following successful sign-in.';\nconst dispatchSignedInHubEvent = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* () {\n    try {\n      Hub.dispatch('auth', {\n        event: 'signedIn',\n        data: yield getCurrentUser()\n      }, 'Auth', AMPLIFY_SYMBOL);\n    } catch (error) {\n      if (error.name === USER_UNAUTHENTICATED_EXCEPTION) {\n        throw new AuthError({\n          name: UNEXPECTED_SIGN_IN_INTERRUPTION_EXCEPTION,\n          message: ERROR_MESSAGE,\n          recoverySuggestion: 'This most likely is due to auth tokens not being persisted. If you are using cookie store, please ensure cookies can be correctly set from your server.'\n        });\n      }\n      throw error;\n    }\n  });\n  return function dispatchSignedInHubEvent() {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { ERROR_MESSAGE, dispatchSignedInHubEvent };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "AMPLIFY_SYMBOL", "getCurrentUser", "USER_UNAUTHENTICATED_EXCEPTION", "UNEXPECTED_SIGN_IN_INTERRUPTION_EXCEPTION", "<PERSON>th<PERSON><PERSON><PERSON>", "ERROR_MESSAGE", "dispatchSignedInHubEvent", "_ref", "_asyncToGenerator", "dispatch", "event", "data", "error", "name", "message", "recoverySuggestion", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/dispatchSignedInHubEvent.mjs"], "sourcesContent": ["import { Hub } from '@aws-amplify/core';\nimport { AMPLIFY_SYMBOL } from '@aws-amplify/core/internals/utils';\nimport { getCurrentUser } from '../apis/getCurrentUser.mjs';\nimport { USER_UNAUTHENTICATED_EXCEPTION, UNEXPECTED_SIGN_IN_INTERRUPTION_EXCEPTION } from '../../../errors/constants.mjs';\nimport { AuthError } from '../../../errors/AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst ERROR_MESSAGE = 'Unable to get user session following successful sign-in.';\nconst dispatchSignedInHubEvent = async () => {\n    try {\n        Hub.dispatch('auth', {\n            event: 'signedIn',\n            data: await getCurrentUser(),\n        }, 'Auth', AMPLIFY_SYMBOL);\n    }\n    catch (error) {\n        if (error.name === USER_UNAUTHENTICATED_EXCEPTION) {\n            throw new AuthError({\n                name: UNEXPECTED_SIGN_IN_INTERRUPTION_EXCEPTION,\n                message: ERROR_MESSAGE,\n                recoverySuggestion: 'This most likely is due to auth tokens not being persisted. If you are using cookie store, please ensure cookies can be correctly set from your server.',\n            });\n        }\n        throw error;\n    }\n};\n\nexport { ERROR_MESSAGE, dispatchSignedInHubEvent };\n"], "mappings": ";AAAA,SAASA,GAAG,QAAQ,mBAAmB;AACvC,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,8BAA8B,EAAEC,yCAAyC,QAAQ,+BAA+B;AACzH,SAASC,SAAS,QAAQ,+BAA+B;;AAEzD;AACA;AACA,MAAMC,aAAa,GAAG,0DAA0D;AAChF,MAAMC,wBAAwB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;IACzC,IAAI;MACAT,GAAG,CAACU,QAAQ,CAAC,MAAM,EAAE;QACjBC,KAAK,EAAE,UAAU;QACjBC,IAAI,QAAQV,cAAc,CAAC;MAC/B,CAAC,EAAE,MAAM,EAAED,cAAc,CAAC;IAC9B,CAAC,CACD,OAAOY,KAAK,EAAE;MACV,IAAIA,KAAK,CAACC,IAAI,KAAKX,8BAA8B,EAAE;QAC/C,MAAM,IAAIE,SAAS,CAAC;UAChBS,IAAI,EAAEV,yCAAyC;UAC/CW,OAAO,EAAET,aAAa;UACtBU,kBAAkB,EAAE;QACxB,CAAC,CAAC;MACN;MACA,MAAMH,KAAK;IACf;EACJ,CAAC;EAAA,gBAjBKN,wBAAwBA,CAAA;IAAA,OAAAC,IAAA,CAAAS,KAAA,OAAAC,SAAA;EAAA;AAAA,GAiB7B;AAED,SAASZ,aAAa,EAAEC,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}