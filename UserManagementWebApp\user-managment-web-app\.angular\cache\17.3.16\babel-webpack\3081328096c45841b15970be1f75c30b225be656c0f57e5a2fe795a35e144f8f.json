{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Default cache config\n */\nconst defaultConfig = {\n  keyPrefix: 'aws-amplify-cache',\n  capacityInBytes: 1048576,\n  itemMaxSize: 210000,\n  defaultTTL: *********,\n  defaultPriority: 5,\n  warningThreshold: 0.8\n};\nconst currentSizeKey = 'CurSize';\nexport { currentSizeKey, defaultConfig };", "map": {"version": 3, "names": ["defaultConfig", "keyPrefix", "capacityInBytes", "itemMaxSize", "defaultTTL", "defaultPriority", "warningThreshold", "currentSizeKey"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Cache/constants.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Default cache config\n */\nconst defaultConfig = {\n    keyPrefix: 'aws-amplify-cache',\n    capacityInBytes: 1048576,\n    itemMaxSize: 210000,\n    defaultTTL: *********,\n    defaultPriority: 5,\n    warningThreshold: 0.8,\n};\nconst currentSizeKey = 'CurSize';\n\nexport { currentSizeKey, defaultConfig };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,MAAMA,aAAa,GAAG;EAClBC,SAAS,EAAE,mBAAmB;EAC9BC,eAAe,EAAE,OAAO;EACxBC,WAAW,EAAE,MAAM;EACnBC,UAAU,EAAE,SAAS;EACrBC,eAAe,EAAE,CAAC;EAClBC,gBAAgB,EAAE;AACtB,CAAC;AACD,MAAMC,cAAc,GAAG,SAAS;AAEhC,SAASA,cAAc,EAAEP,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}