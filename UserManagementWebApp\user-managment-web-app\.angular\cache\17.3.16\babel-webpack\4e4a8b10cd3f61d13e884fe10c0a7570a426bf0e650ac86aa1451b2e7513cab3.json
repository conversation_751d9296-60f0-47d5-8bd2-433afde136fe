{"ast": null, "code": "import { AmplifyErrorCode } from '@aws-amplify/core/internals/utils';\nimport { Hub } from 'aws-amplify/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { isFunction } from '../../utils/utils.mjs';\n\n/**\n * Handles Amplify JS Auth hub events, by forwarding hub events as appropriate\n * xstate events.\n */\nconst defaultAuthHubHandler = ({\n  payload\n}, service, options) => {\n  const {\n    data,\n    event\n  } = payload;\n  const {\n    send\n  } = service;\n  const {\n    onSignIn,\n    onSignOut\n  } = options ?? {};\n  switch (event) {\n    case 'signedIn':\n      {\n        if (isFunction(onSignIn)) {\n          onSignIn(payload);\n        }\n        break;\n      }\n    case 'signInWithRedirect':\n      {\n        send('SIGN_IN_WITH_REDIRECT');\n        break;\n      }\n    case 'signedOut':\n      {\n        if (isFunction(onSignOut)) {\n          onSignOut();\n        }\n        send('SIGN_OUT');\n        break;\n      }\n    case 'tokenRefresh_failure':\n      {\n        if (data?.error?.name === AmplifyErrorCode.NetworkError) {\n          return;\n        }\n        send('SIGN_OUT');\n        break;\n      }\n  }\n};\n/**\n * Listens to external auth Hub events and sends corresponding event to\n * the `service.send` of interest\n *\n * @param service - contains state machine `send` function\n * @param handler - auth event handler\n * @returns function that unsubscribes to the hub evenmt\n */\nconst listenToAuthHub = (service, handler = defaultAuthHubHandler) => {\n  const eventHandler = data => handler(data, service);\n  return Hub.listen('auth', eventHandler, 'authenticator-hub-handler');\n};\nexport { defaultAuthHubHandler, listenToAuthHub };", "map": {"version": 3, "names": ["AmplifyErrorCode", "<PERSON><PERSON>", "isFunction", "defaultAuthHubHandler", "payload", "service", "options", "data", "event", "send", "onSignIn", "onSignOut", "error", "name", "NetworkError", "listenToAuthHub", "handler", "<PERSON><PERSON><PERSON><PERSON>", "listen"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/helpers/authenticator/defaultAuthHubHandler.mjs"], "sourcesContent": ["import { AmplifyErrorCode } from '@aws-amplify/core/internals/utils';\nimport { Hub } from 'aws-amplify/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { isFunction } from '../../utils/utils.mjs';\n\n/**\n * Handles Amplify JS Auth hub events, by forwarding hub events as appropriate\n * xstate events.\n */\nconst defaultAuthHubHandler = ({ payload }, service, options) => {\n    const { data, event } = payload;\n    const { send } = service;\n    const { onSignIn, onSignOut } = options ?? {};\n    switch (event) {\n        case 'signedIn': {\n            if (isFunction(onSignIn)) {\n                onSignIn(payload);\n            }\n            break;\n        }\n        case 'signInWithRedirect': {\n            send('SIGN_IN_WITH_REDIRECT');\n            break;\n        }\n        case 'signedOut': {\n            if (isFunction(onSignOut)) {\n                onSignOut();\n            }\n            send('SIGN_OUT');\n            break;\n        }\n        case 'tokenRefresh_failure': {\n            if (data?.error?.name === AmplifyErrorCode.NetworkError) {\n                return;\n            }\n            send('SIGN_OUT');\n            break;\n        }\n    }\n};\n/**\n * Listens to external auth Hub events and sends corresponding event to\n * the `service.send` of interest\n *\n * @param service - contains state machine `send` function\n * @param handler - auth event handler\n * @returns function that unsubscribes to the hub evenmt\n */\nconst listenToAuthHub = (service, handler = defaultAuthHubHandler) => {\n    const eventHandler = (data) => handler(data, service);\n    return Hub.listen('auth', eventHandler, 'authenticator-hub-handler');\n};\n\nexport { defaultAuthHubHandler, listenToAuthHub };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,GAAG,QAAQ,mBAAmB;AACvC,OAAO,wCAAwC;AAC/C,SAASC,UAAU,QAAQ,uBAAuB;;AAElD;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAGA,CAAC;EAAEC;AAAQ,CAAC,EAAEC,OAAO,EAAEC,OAAO,KAAK;EAC7D,MAAM;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAGJ,OAAO;EAC/B,MAAM;IAAEK;EAAK,CAAC,GAAGJ,OAAO;EACxB,MAAM;IAAEK,QAAQ;IAAEC;EAAU,CAAC,GAAGL,OAAO,IAAI,CAAC,CAAC;EAC7C,QAAQE,KAAK;IACT,KAAK,UAAU;MAAE;QACb,IAAIN,UAAU,CAACQ,QAAQ,CAAC,EAAE;UACtBA,QAAQ,CAACN,OAAO,CAAC;QACrB;QACA;MACJ;IACA,KAAK,oBAAoB;MAAE;QACvBK,IAAI,CAAC,uBAAuB,CAAC;QAC7B;MACJ;IACA,KAAK,WAAW;MAAE;QACd,IAAIP,UAAU,CAACS,SAAS,CAAC,EAAE;UACvBA,SAAS,CAAC,CAAC;QACf;QACAF,IAAI,CAAC,UAAU,CAAC;QAChB;MACJ;IACA,KAAK,sBAAsB;MAAE;QACzB,IAAIF,IAAI,EAAEK,KAAK,EAAEC,IAAI,KAAKb,gBAAgB,CAACc,YAAY,EAAE;UACrD;QACJ;QACAL,IAAI,CAAC,UAAU,CAAC;QAChB;MACJ;EACJ;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,eAAe,GAAGA,CAACV,OAAO,EAAEW,OAAO,GAAGb,qBAAqB,KAAK;EAClE,MAAMc,YAAY,GAAIV,IAAI,IAAKS,OAAO,CAACT,IAAI,EAAEF,OAAO,CAAC;EACrD,OAAOJ,GAAG,CAACiB,MAAM,CAAC,MAAM,EAAED,YAAY,EAAE,2BAA2B,CAAC;AACxE,CAAC;AAED,SAASd,qBAAqB,EAAEY,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}