{"ast": null, "code": "/**\n * Takes a set of keys and a color name and returns an object of design tokens,\n * used for applying a primary color at the theme level to our tokens.\n *\n * createColorPalette({keys: ['10','20',...], value: 'red'})\n * returns {\n *   10: { value: '{colors.red.10.value}' },\n *   20: { value: '{colors.red.20.value}' },\n *   ...\n * }\n */\nfunction createColorPalette({\n  keys,\n  value\n}) {\n  return keys.reduce((acc, key) => {\n    return {\n      ...acc,\n      [key]: {\n        value: `{colors.${value}.${key}.value}`\n      }\n    };\n  }, {});\n}\nexport { createColorPalette };", "map": {"version": 3, "names": ["createColorPalette", "keys", "value", "reduce", "acc", "key"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/createTheme/createColorPalette.mjs"], "sourcesContent": ["/**\n * Takes a set of keys and a color name and returns an object of design tokens,\n * used for applying a primary color at the theme level to our tokens.\n *\n * createColorPalette({keys: ['10','20',...], value: 'red'})\n * returns {\n *   10: { value: '{colors.red.10.value}' },\n *   20: { value: '{colors.red.20.value}' },\n *   ...\n * }\n */\nfunction createColorPalette({ keys, value }) {\n    return keys.reduce((acc, key) => {\n        return {\n            ...acc,\n            [key]: { value: `{colors.${value}.${key}.value}` },\n        };\n    }, {});\n}\n\nexport { createColorPalette };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,kBAAkBA,CAAC;EAAEC,IAAI;EAAEC;AAAM,CAAC,EAAE;EACzC,OAAOD,IAAI,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IAC7B,OAAO;MACH,GAAGD,GAAG;MACN,CAACC,GAAG,GAAG;QAAEH,KAAK,EAAE,WAAWA,KAAK,IAAIG,GAAG;MAAU;IACrD,CAAC;EACL,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;AAEA,SAASL,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}