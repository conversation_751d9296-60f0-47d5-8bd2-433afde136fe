{"ast": null, "code": "import { isClockSkewed } from './isClockSkewed.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns the difference between clock time and the current system time if clock is skewed.\n *\n * @param clockTimeInMilliseconds Clock time in milliseconds.\n * @param currentSystemClockOffset Current system clock offset in milliseconds.\n *\n * @internal\n */\nconst getUpdatedSystemClockOffset = (clockTimeInMilliseconds, currentSystemClockOffset) => {\n  if (isClockSkewed(clockTimeInMilliseconds, currentSystemClockOffset)) {\n    return clockTimeInMilliseconds - Date.now();\n  }\n  return currentSystemClockOffset;\n};\nexport { getUpdatedSystemClockOffset };", "map": {"version": 3, "names": ["isClockSkewed", "getUpdatedSystemClockOffset", "clockTimeInMilliseconds", "currentSystemClockOffset", "Date", "now"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/utils/getUpdatedSystemClockOffset.mjs"], "sourcesContent": ["import { isClockSkewed } from './isClockSkewed.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns the difference between clock time and the current system time if clock is skewed.\n *\n * @param clockTimeInMilliseconds Clock time in milliseconds.\n * @param currentSystemClockOffset Current system clock offset in milliseconds.\n *\n * @internal\n */\nconst getUpdatedSystemClockOffset = (clockTimeInMilliseconds, currentSystemClockOffset) => {\n    if (isClockSkewed(clockTimeInMilliseconds, currentSystemClockOffset)) {\n        return clockTimeInMilliseconds - Date.now();\n    }\n    return currentSystemClockOffset;\n};\n\nexport { getUpdatedSystemClockOffset };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,2BAA2B,GAAGA,CAACC,uBAAuB,EAAEC,wBAAwB,KAAK;EACvF,IAAIH,aAAa,CAACE,uBAAuB,EAAEC,wBAAwB,CAAC,EAAE;IAClE,OAAOD,uBAAuB,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC;EAC/C;EACA,OAAOF,wBAAwB;AACnC,CAAC;AAED,SAASF,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}