{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Cache the payload of a response body. It allows multiple calls to the body,\n * for example, when reading the body in both retry decider and error deserializer.\n * Caching body is allowed here because we call the body accessor(blob(), json(),\n * etc.) when body is small or streaming implementation is not available(RN).\n *\n * @internal\n */\nconst withMemoization = payloadAccessor => {\n  let cached;\n  return () => {\n    if (!cached) {\n      // Explicitly not awaiting. Intermediate await would add overhead and\n      // introduce a possible race in the event that this wrapper is called\n      // again before the first `payloadAccessor` call resolves.\n      cached = payloadAccessor();\n    }\n    return cached;\n  };\n};\nexport { withMemoization };", "map": {"version": 3, "names": ["withMemoization", "payloadAccessor", "cached"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/utils/memoization.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Cache the payload of a response body. It allows multiple calls to the body,\n * for example, when reading the body in both retry decider and error deserializer.\n * Caching body is allowed here because we call the body accessor(blob(), json(),\n * etc.) when body is small or streaming implementation is not available(RN).\n *\n * @internal\n */\nconst withMemoization = (payloadAccessor) => {\n    let cached;\n    return () => {\n        if (!cached) {\n            // Explicitly not awaiting. Intermediate await would add overhead and\n            // introduce a possible race in the event that this wrapper is called\n            // again before the first `payloadAccessor` call resolves.\n            cached = payloadAccessor();\n        }\n        return cached;\n    };\n};\n\nexport { withMemoization };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,eAAe,GAAIC,eAAe,IAAK;EACzC,IAAIC,MAAM;EACV,OAAO,MAAM;IACT,IAAI,CAACA,MAAM,EAAE;MACT;MACA;MACA;MACAA,MAAM,GAAGD,eAAe,CAAC,CAAC;IAC9B;IACA,OAAOC,MAAM;EACjB,CAAC;AACL,CAAC;AAED,SAASF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}