{"ast": null, "code": "// This alphabet uses `A-Za-z0-9_-` symbols.\n// The order of characters is optimized for better gzip and brotli compression.\n// Same as in non-secure/index.js\nlet urlAlphabet = 'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict';\nexport { urlAlphabet };", "map": {"version": 3, "names": ["url<PERSON>l<PERSON><PERSON>"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/nanoid/url-alphabet/index.js"], "sourcesContent": ["// This alphabet uses `A-Za-z0-9_-` symbols.\n// The order of characters is optimized for better gzip and brotli compression.\n// Same as in non-secure/index.js\nlet urlAlphabet =\n  'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict'\n\nexport { urlAlphabet }\n"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,WAAW,GACb,kEAAkE;AAEpE,SAASA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}