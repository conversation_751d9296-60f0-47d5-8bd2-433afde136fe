{"ast": null, "code": "import { defaultStorage } from '@aws-amplify/core';\nimport { DefaultIdentityIdStore } from './IdentityIdStore.mjs';\nimport { CognitoAWSCredentialsAndIdentityIdProvider } from './credentialsProvider.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Cognito specific implmentation of the CredentialsProvider interface\n * that manages setting and getting of AWS Credentials.\n *\n * @throws configuration expections: `InvalidIdentityPoolIdException`\n *  - Auth errors that may arise from misconfiguration.\n * @throws service expections: {@link GetCredentialsForIdentityException}, {@link GetIdException}\n *\n */\nconst cognitoCredentialsProvider = new CognitoAWSCredentialsAndIdentityIdProvider(new DefaultIdentityIdStore(defaultStorage));\nexport { CognitoAWSCredentialsAndIdentityIdProvider, DefaultIdentityIdStore, cognitoCredentialsProvider };", "map": {"version": 3, "names": ["defaultStorage", "DefaultIdentityIdStore", "CognitoAWSCredentialsAndIdentityIdProvider", "cognitoCredentialsProvider"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/credentialsProvider/index.mjs"], "sourcesContent": ["import { defaultStorage } from '@aws-amplify/core';\nimport { DefaultIdentityIdStore } from './IdentityIdStore.mjs';\nimport { CognitoAWSCredentialsAndIdentityIdProvider } from './credentialsProvider.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Cognito specific implmentation of the CredentialsProvider interface\n * that manages setting and getting of AWS Credentials.\n *\n * @throws configuration expections: `InvalidIdentityPoolIdException`\n *  - Auth errors that may arise from misconfiguration.\n * @throws service expections: {@link GetCredentialsForIdentityException}, {@link GetIdException}\n *\n */\nconst cognitoCredentialsProvider = new CognitoAWSCredentialsAndIdentityIdProvider(new DefaultIdentityIdStore(defaultStorage));\n\nexport { CognitoAWSCredentialsAndIdentityIdProvider, DefaultIdentityIdStore, cognitoCredentialsProvider };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mBAAmB;AAClD,SAASC,sBAAsB,QAAQ,uBAAuB;AAC9D,SAASC,0CAA0C,QAAQ,2BAA2B;;AAEtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,GAAG,IAAID,0CAA0C,CAAC,IAAID,sBAAsB,CAACD,cAAc,CAAC,CAAC;AAE7H,SAASE,0CAA0C,EAAED,sBAAsB,EAAEE,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}