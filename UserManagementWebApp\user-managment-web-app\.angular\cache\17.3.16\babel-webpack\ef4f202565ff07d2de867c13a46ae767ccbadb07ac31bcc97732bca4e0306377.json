{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { parseMetadata } from './responseInfo.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Utility functions for serializing and deserializing of JSON protocol in general(including: REST-JSON, JSON-RPC, etc.)\n */\n/**\n * Error parser for AWS JSON protocol.\n */\nconst parseJsonError = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (response) {\n    if (!response || response.statusCode < 300) {\n      return;\n    }\n    const body = yield parseJsonBody(response);\n    const sanitizeErrorCode = rawValue => {\n      const [cleanValue] = rawValue.toString().split(/[,:]+/);\n      if (cleanValue.includes('#')) {\n        return cleanValue.split('#')[1];\n      }\n      return cleanValue;\n    };\n    const code = sanitizeErrorCode(response.headers['x-amzn-errortype'] ?? body.code ?? body.__type ?? 'UnknownError');\n    const message = body.message ?? body.Message ?? 'Unknown error';\n    const error = new Error(message);\n    return Object.assign(error, {\n      name: code,\n      $metadata: parseMetadata(response)\n    });\n  });\n  return function parseJsonError(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\n/**\n * Parse JSON response body to JavaScript object.\n */\nconst parseJsonBody = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* (response) {\n    if (!response.body) {\n      throw new Error('Missing response payload');\n    }\n    const output = yield response.body.json();\n    return Object.assign(output, {\n      $metadata: parseMetadata(response)\n    });\n  });\n  return function parseJsonBody(_x2) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nexport { parseJsonBody, parseJsonError };", "map": {"version": 3, "names": ["parseMetadata", "parseJsonError", "_ref", "_asyncToGenerator", "response", "statusCode", "body", "parseJsonBody", "sanitizeErrorCode", "rawValue", "cleanValue", "toString", "split", "includes", "code", "headers", "__type", "message", "Message", "error", "Error", "Object", "assign", "name", "$metadata", "_x", "apply", "arguments", "_ref2", "output", "json", "_x2"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/serde/json.mjs"], "sourcesContent": ["import { parseMetadata } from './responseInfo.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Utility functions for serializing and deserializing of JSON protocol in general(including: REST-JSON, JSON-RPC, etc.)\n */\n/**\n * Error parser for AWS JSON protocol.\n */\nconst parseJsonError = async (response) => {\n    if (!response || response.statusCode < 300) {\n        return;\n    }\n    const body = await parseJsonBody(response);\n    const sanitizeErrorCode = (rawValue) => {\n        const [cleanValue] = rawValue.toString().split(/[,:]+/);\n        if (cleanValue.includes('#')) {\n            return cleanValue.split('#')[1];\n        }\n        return cleanValue;\n    };\n    const code = sanitizeErrorCode(response.headers['x-amzn-errortype'] ??\n        body.code ??\n        body.__type ??\n        'UnknownError');\n    const message = body.message ?? body.Message ?? 'Unknown error';\n    const error = new Error(message);\n    return Object.assign(error, {\n        name: code,\n        $metadata: parseMetadata(response),\n    });\n};\n/**\n * Parse JSON response body to JavaScript object.\n */\nconst parseJsonBody = async (response) => {\n    if (!response.body) {\n        throw new Error('Missing response payload');\n    }\n    const output = await response.body.json();\n    return Object.assign(output, {\n        $metadata: parseMetadata(response),\n    });\n};\n\nexport { parseJsonBody, parseJsonError };\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,oBAAoB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,QAAQ,EAAK;IACvC,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACC,UAAU,GAAG,GAAG,EAAE;MACxC;IACJ;IACA,MAAMC,IAAI,SAASC,aAAa,CAACH,QAAQ,CAAC;IAC1C,MAAMI,iBAAiB,GAAIC,QAAQ,IAAK;MACpC,MAAM,CAACC,UAAU,CAAC,GAAGD,QAAQ,CAACE,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC;MACvD,IAAIF,UAAU,CAACG,QAAQ,CAAC,GAAG,CAAC,EAAE;QAC1B,OAAOH,UAAU,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACnC;MACA,OAAOF,UAAU;IACrB,CAAC;IACD,MAAMI,IAAI,GAAGN,iBAAiB,CAACJ,QAAQ,CAACW,OAAO,CAAC,kBAAkB,CAAC,IAC/DT,IAAI,CAACQ,IAAI,IACTR,IAAI,CAACU,MAAM,IACX,cAAc,CAAC;IACnB,MAAMC,OAAO,GAAGX,IAAI,CAACW,OAAO,IAAIX,IAAI,CAACY,OAAO,IAAI,eAAe;IAC/D,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACH,OAAO,CAAC;IAChC,OAAOI,MAAM,CAACC,MAAM,CAACH,KAAK,EAAE;MACxBI,IAAI,EAAET,IAAI;MACVU,SAAS,EAAExB,aAAa,CAACI,QAAQ;IACrC,CAAC,CAAC;EACN,CAAC;EAAA,gBAtBKH,cAAcA,CAAAwB,EAAA;IAAA,OAAAvB,IAAA,CAAAwB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAsBnB;AACD;AACA;AACA;AACA,MAAMpB,aAAa;EAAA,IAAAqB,KAAA,GAAAzB,iBAAA,CAAG,WAAOC,QAAQ,EAAK;IACtC,IAAI,CAACA,QAAQ,CAACE,IAAI,EAAE;MAChB,MAAM,IAAIc,KAAK,CAAC,0BAA0B,CAAC;IAC/C;IACA,MAAMS,MAAM,SAASzB,QAAQ,CAACE,IAAI,CAACwB,IAAI,CAAC,CAAC;IACzC,OAAOT,MAAM,CAACC,MAAM,CAACO,MAAM,EAAE;MACzBL,SAAS,EAAExB,aAAa,CAACI,QAAQ;IACrC,CAAC,CAAC;EACN,CAAC;EAAA,gBARKG,aAAaA,CAAAwB,GAAA;IAAA,OAAAH,KAAA,CAAAF,KAAA,OAAAC,SAAA;EAAA;AAAA,GAQlB;AAED,SAASpB,aAAa,EAAEN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}