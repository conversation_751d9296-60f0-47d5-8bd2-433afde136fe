{"ast": null, "code": "import { AuthValidationErrorCode } from '../errors/types/validation.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst validationErrorMap = {\n  [AuthValidationErrorCode.EmptyChallengeResponse]: {\n    message: 'challengeResponse is required to confirmSignIn'\n  },\n  [AuthValidationErrorCode.EmptyConfirmResetPasswordUsername]: {\n    message: 'username is required to confirmResetPassword'\n  },\n  [AuthValidationErrorCode.EmptyConfirmSignUpCode]: {\n    message: 'code is required to confirmSignUp'\n  },\n  [AuthValidationErrorCode.EmptyConfirmSignUpUsername]: {\n    message: 'username is required to confirmSignUp'\n  },\n  [AuthValidationErrorCode.EmptyConfirmResetPasswordConfirmationCode]: {\n    message: 'confirmationCode is required to confirmResetPassword'\n  },\n  [AuthValidationErrorCode.EmptyConfirmResetPasswordNewPassword]: {\n    message: 'newPassword is required to confirmResetPassword'\n  },\n  [AuthValidationErrorCode.EmptyResendSignUpCodeUsername]: {\n    message: 'username is required to confirmSignUp'\n  },\n  [AuthValidationErrorCode.EmptyResetPasswordUsername]: {\n    message: 'username is required to resetPassword'\n  },\n  [AuthValidationErrorCode.EmptySignInPassword]: {\n    message: 'password is required to signIn'\n  },\n  [AuthValidationErrorCode.EmptySignInUsername]: {\n    message: 'username is required to signIn'\n  },\n  [AuthValidationErrorCode.EmptySignUpPassword]: {\n    message: 'password is required to signUp'\n  },\n  [AuthValidationErrorCode.EmptySignUpUsername]: {\n    message: 'username is required to signUp'\n  },\n  [AuthValidationErrorCode.CustomAuthSignInPassword]: {\n    message: 'A password is not needed when signing in with CUSTOM_WITHOUT_SRP',\n    recoverySuggestion: 'Do not include a password in your signIn call.'\n  },\n  [AuthValidationErrorCode.IncorrectMFAMethod]: {\n    message: 'Incorrect MFA method was chosen. It should be either SMS, TOTP, or EMAIL',\n    recoverySuggestion: 'Try to pass SMS, TOTP, or EMAIL as the challengeResponse'\n  },\n  [AuthValidationErrorCode.EmptyVerifyTOTPSetupCode]: {\n    message: 'code is required to verifyTotpSetup'\n  },\n  [AuthValidationErrorCode.EmptyUpdatePassword]: {\n    message: 'oldPassword and newPassword are required to changePassword'\n  },\n  [AuthValidationErrorCode.EmptyConfirmUserAttributeCode]: {\n    message: 'confirmation code is required to confirmUserAttribute'\n  }\n};\n// TODO: delete this code when the Auth class is removed.\nvar AuthErrorStrings;\n(function (AuthErrorStrings) {\n  AuthErrorStrings[\"DEFAULT_MSG\"] = \"Authentication Error\";\n  AuthErrorStrings[\"EMPTY_EMAIL\"] = \"Email cannot be empty\";\n  AuthErrorStrings[\"EMPTY_PHONE\"] = \"Phone number cannot be empty\";\n  AuthErrorStrings[\"EMPTY_USERNAME\"] = \"Username cannot be empty\";\n  AuthErrorStrings[\"INVALID_USERNAME\"] = \"The username should either be a string or one of the sign in types\";\n  AuthErrorStrings[\"EMPTY_PASSWORD\"] = \"Password cannot be empty\";\n  AuthErrorStrings[\"EMPTY_CODE\"] = \"Confirmation code cannot be empty\";\n  AuthErrorStrings[\"SIGN_UP_ERROR\"] = \"Error creating account\";\n  AuthErrorStrings[\"NO_MFA\"] = \"No valid MFA method provided\";\n  AuthErrorStrings[\"INVALID_MFA\"] = \"Invalid MFA type\";\n  AuthErrorStrings[\"EMPTY_CHALLENGE\"] = \"Challenge response cannot be empty\";\n  AuthErrorStrings[\"NO_USER_SESSION\"] = \"Failed to get the session because the user is empty\";\n  AuthErrorStrings[\"NETWORK_ERROR\"] = \"Network Error\";\n  AuthErrorStrings[\"DEVICE_CONFIG\"] = \"Device tracking has not been configured in this User Pool\";\n  AuthErrorStrings[\"AUTOSIGNIN_ERROR\"] = \"Please use your credentials to sign in\";\n  AuthErrorStrings[\"OAUTH_ERROR\"] = \"Couldn't finish OAuth flow, check your User Pool HostedUI settings\";\n})(AuthErrorStrings || (AuthErrorStrings = {}));\nvar AuthErrorCodes;\n(function (AuthErrorCodes) {\n  AuthErrorCodes[\"SignInException\"] = \"SignInException\";\n  AuthErrorCodes[\"OAuthSignInError\"] = \"OAuthSignInException\";\n})(AuthErrorCodes || (AuthErrorCodes = {}));\nexport { AuthErrorCodes, AuthErrorStrings, validationErrorMap };", "map": {"version": 3, "names": ["AuthValidationErrorCode", "validationErrorMap", "EmptyChallengeResponse", "message", "EmptyConfirmResetPasswordUsername", "EmptyConfirmSignUpCode", "EmptyConfirmSignUpUsername", "EmptyConfirmResetPasswordConfirmationCode", "EmptyConfirmResetPasswordNewPassword", "EmptyResendSignUpCodeUsername", "EmptyResetPasswordUsername", "EmptySignInPassword", "EmptySignInUsername", "EmptySignUpPassword", "EmptySignUpUsername", "CustomAuthSignInPassword", "recoverySuggestion", "IncorrectMFAMethod", "EmptyVerifyTOTPSetupCode", "EmptyUpdatePassword", "EmptyConfirmUserAttributeCode", "AuthErrorStrings", "AuthErrorCodes"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/common/AuthErrorStrings.mjs"], "sourcesContent": ["import { AuthValidationErrorCode } from '../errors/types/validation.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst validationErrorMap = {\n    [AuthValidationErrorCode.EmptyChallengeResponse]: {\n        message: 'challengeResponse is required to confirmSignIn',\n    },\n    [AuthValidationErrorCode.EmptyConfirmResetPasswordUsername]: {\n        message: 'username is required to confirmResetPassword',\n    },\n    [AuthValidationErrorCode.EmptyConfirmSignUpCode]: {\n        message: 'code is required to confirmSignUp',\n    },\n    [AuthValidationErrorCode.EmptyConfirmSignUpUsername]: {\n        message: 'username is required to confirmSignUp',\n    },\n    [AuthValidationErrorCode.EmptyConfirmResetPasswordConfirmationCode]: {\n        message: 'confirmationCode is required to confirmResetPassword',\n    },\n    [AuthValidationErrorCode.EmptyConfirmResetPasswordNewPassword]: {\n        message: 'newPassword is required to confirmResetPassword',\n    },\n    [AuthValidationErrorCode.EmptyResendSignUpCodeUsername]: {\n        message: 'username is required to confirmSignUp',\n    },\n    [AuthValidationErrorCode.EmptyResetPasswordUsername]: {\n        message: 'username is required to resetPassword',\n    },\n    [AuthValidationErrorCode.EmptySignInPassword]: {\n        message: 'password is required to signIn',\n    },\n    [AuthValidationErrorCode.EmptySignInUsername]: {\n        message: 'username is required to signIn',\n    },\n    [AuthValidationErrorCode.EmptySignUpPassword]: {\n        message: 'password is required to signUp',\n    },\n    [AuthValidationErrorCode.EmptySignUpUsername]: {\n        message: 'username is required to signUp',\n    },\n    [AuthValidationErrorCode.CustomAuthSignInPassword]: {\n        message: 'A password is not needed when signing in with CUSTOM_WITHOUT_SRP',\n        recoverySuggestion: 'Do not include a password in your signIn call.',\n    },\n    [AuthValidationErrorCode.IncorrectMFAMethod]: {\n        message: 'Incorrect MFA method was chosen. It should be either SMS, TOTP, or EMAIL',\n        recoverySuggestion: 'Try to pass SMS, TOTP, or EMAIL as the challengeResponse',\n    },\n    [AuthValidationErrorCode.EmptyVerifyTOTPSetupCode]: {\n        message: 'code is required to verifyTotpSetup',\n    },\n    [AuthValidationErrorCode.EmptyUpdatePassword]: {\n        message: 'oldPassword and newPassword are required to changePassword',\n    },\n    [AuthValidationErrorCode.EmptyConfirmUserAttributeCode]: {\n        message: 'confirmation code is required to confirmUserAttribute',\n    },\n};\n// TODO: delete this code when the Auth class is removed.\nvar AuthErrorStrings;\n(function (AuthErrorStrings) {\n    AuthErrorStrings[\"DEFAULT_MSG\"] = \"Authentication Error\";\n    AuthErrorStrings[\"EMPTY_EMAIL\"] = \"Email cannot be empty\";\n    AuthErrorStrings[\"EMPTY_PHONE\"] = \"Phone number cannot be empty\";\n    AuthErrorStrings[\"EMPTY_USERNAME\"] = \"Username cannot be empty\";\n    AuthErrorStrings[\"INVALID_USERNAME\"] = \"The username should either be a string or one of the sign in types\";\n    AuthErrorStrings[\"EMPTY_PASSWORD\"] = \"Password cannot be empty\";\n    AuthErrorStrings[\"EMPTY_CODE\"] = \"Confirmation code cannot be empty\";\n    AuthErrorStrings[\"SIGN_UP_ERROR\"] = \"Error creating account\";\n    AuthErrorStrings[\"NO_MFA\"] = \"No valid MFA method provided\";\n    AuthErrorStrings[\"INVALID_MFA\"] = \"Invalid MFA type\";\n    AuthErrorStrings[\"EMPTY_CHALLENGE\"] = \"Challenge response cannot be empty\";\n    AuthErrorStrings[\"NO_USER_SESSION\"] = \"Failed to get the session because the user is empty\";\n    AuthErrorStrings[\"NETWORK_ERROR\"] = \"Network Error\";\n    AuthErrorStrings[\"DEVICE_CONFIG\"] = \"Device tracking has not been configured in this User Pool\";\n    AuthErrorStrings[\"AUTOSIGNIN_ERROR\"] = \"Please use your credentials to sign in\";\n    AuthErrorStrings[\"OAUTH_ERROR\"] = \"Couldn't finish OAuth flow, check your User Pool HostedUI settings\";\n})(AuthErrorStrings || (AuthErrorStrings = {}));\nvar AuthErrorCodes;\n(function (AuthErrorCodes) {\n    AuthErrorCodes[\"SignInException\"] = \"SignInException\";\n    AuthErrorCodes[\"OAuthSignInError\"] = \"OAuthSignInException\";\n})(AuthErrorCodes || (AuthErrorCodes = {}));\n\nexport { AuthErrorCodes, AuthErrorStrings, validationErrorMap };\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,gCAAgC;;AAExE;AACA;AACA,MAAMC,kBAAkB,GAAG;EACvB,CAACD,uBAAuB,CAACE,sBAAsB,GAAG;IAC9CC,OAAO,EAAE;EACb,CAAC;EACD,CAACH,uBAAuB,CAACI,iCAAiC,GAAG;IACzDD,OAAO,EAAE;EACb,CAAC;EACD,CAACH,uBAAuB,CAACK,sBAAsB,GAAG;IAC9CF,OAAO,EAAE;EACb,CAAC;EACD,CAACH,uBAAuB,CAACM,0BAA0B,GAAG;IAClDH,OAAO,EAAE;EACb,CAAC;EACD,CAACH,uBAAuB,CAACO,yCAAyC,GAAG;IACjEJ,OAAO,EAAE;EACb,CAAC;EACD,CAACH,uBAAuB,CAACQ,oCAAoC,GAAG;IAC5DL,OAAO,EAAE;EACb,CAAC;EACD,CAACH,uBAAuB,CAACS,6BAA6B,GAAG;IACrDN,OAAO,EAAE;EACb,CAAC;EACD,CAACH,uBAAuB,CAACU,0BAA0B,GAAG;IAClDP,OAAO,EAAE;EACb,CAAC;EACD,CAACH,uBAAuB,CAACW,mBAAmB,GAAG;IAC3CR,OAAO,EAAE;EACb,CAAC;EACD,CAACH,uBAAuB,CAACY,mBAAmB,GAAG;IAC3CT,OAAO,EAAE;EACb,CAAC;EACD,CAACH,uBAAuB,CAACa,mBAAmB,GAAG;IAC3CV,OAAO,EAAE;EACb,CAAC;EACD,CAACH,uBAAuB,CAACc,mBAAmB,GAAG;IAC3CX,OAAO,EAAE;EACb,CAAC;EACD,CAACH,uBAAuB,CAACe,wBAAwB,GAAG;IAChDZ,OAAO,EAAE,kEAAkE;IAC3Ea,kBAAkB,EAAE;EACxB,CAAC;EACD,CAAChB,uBAAuB,CAACiB,kBAAkB,GAAG;IAC1Cd,OAAO,EAAE,0EAA0E;IACnFa,kBAAkB,EAAE;EACxB,CAAC;EACD,CAAChB,uBAAuB,CAACkB,wBAAwB,GAAG;IAChDf,OAAO,EAAE;EACb,CAAC;EACD,CAACH,uBAAuB,CAACmB,mBAAmB,GAAG;IAC3ChB,OAAO,EAAE;EACb,CAAC;EACD,CAACH,uBAAuB,CAACoB,6BAA6B,GAAG;IACrDjB,OAAO,EAAE;EACb;AACJ,CAAC;AACD;AACA,IAAIkB,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EACzBA,gBAAgB,CAAC,aAAa,CAAC,GAAG,sBAAsB;EACxDA,gBAAgB,CAAC,aAAa,CAAC,GAAG,uBAAuB;EACzDA,gBAAgB,CAAC,aAAa,CAAC,GAAG,8BAA8B;EAChEA,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,0BAA0B;EAC/DA,gBAAgB,CAAC,kBAAkB,CAAC,GAAG,oEAAoE;EAC3GA,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,0BAA0B;EAC/DA,gBAAgB,CAAC,YAAY,CAAC,GAAG,mCAAmC;EACpEA,gBAAgB,CAAC,eAAe,CAAC,GAAG,wBAAwB;EAC5DA,gBAAgB,CAAC,QAAQ,CAAC,GAAG,8BAA8B;EAC3DA,gBAAgB,CAAC,aAAa,CAAC,GAAG,kBAAkB;EACpDA,gBAAgB,CAAC,iBAAiB,CAAC,GAAG,oCAAoC;EAC1EA,gBAAgB,CAAC,iBAAiB,CAAC,GAAG,qDAAqD;EAC3FA,gBAAgB,CAAC,eAAe,CAAC,GAAG,eAAe;EACnDA,gBAAgB,CAAC,eAAe,CAAC,GAAG,2DAA2D;EAC/FA,gBAAgB,CAAC,kBAAkB,CAAC,GAAG,wCAAwC;EAC/EA,gBAAgB,CAAC,aAAa,CAAC,GAAG,oEAAoE;AAC1G,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,IAAIC,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAAC,iBAAiB,CAAC,GAAG,iBAAiB;EACrDA,cAAc,CAAC,kBAAkB,CAAC,GAAG,sBAAsB;AAC/D,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAE3C,SAASA,cAAc,EAAED,gBAAgB,EAAEpB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}