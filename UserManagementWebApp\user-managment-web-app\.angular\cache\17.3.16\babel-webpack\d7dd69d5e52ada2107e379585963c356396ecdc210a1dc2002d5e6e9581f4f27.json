{"ast": null, "code": "const storagemanager = {\n  dropzone: {\n    backgroundColor: {\n      value: '{colors.background.primary}'\n    },\n    borderRadius: {\n      value: '{radii.small}'\n    },\n    borderColor: {\n      value: '{colors.border.primary}'\n    },\n    borderStyle: {\n      value: 'dashed'\n    },\n    borderWidth: {\n      value: '{borderWidths.small}'\n    },\n    gap: {\n      value: '{space.small}'\n    },\n    paddingBlock: {\n      value: '{space.xl}'\n    },\n    paddingInline: {\n      value: '{space.large}'\n    },\n    textAlign: {\n      value: 'center'\n    },\n    _active: {\n      backgroundColor: {\n        value: '{colors.primary.10}'\n      },\n      borderRadius: {\n        value: '{components.storagemanager.dropzone.borderRadius}'\n      },\n      borderColor: {\n        value: '{colors.border.pressed}'\n      },\n      borderStyle: {\n        value: '{components.storagemanager.dropzone.borderStyle}'\n      },\n      borderWidth: {\n        value: '{borderWidths.medium}'\n      }\n    },\n    icon: {\n      color: {\n        value: '{colors.border.primary}'\n      },\n      fontSize: {\n        value: '{fontSizes.xxl}'\n      }\n    },\n    text: {\n      color: {\n        value: '{colors.font.tertiary}'\n      },\n      fontSize: {\n        value: '{fontSizes.medium}'\n      },\n      fontWeight: {\n        value: '{fontWeights.bold}'\n      }\n    }\n  },\n  file: {\n    backgroundColor: {\n      value: '{colors.background.primary}'\n    },\n    borderRadius: {\n      value: '{radii.small}'\n    },\n    borderColor: {\n      value: '{colors.border.primary}'\n    },\n    borderStyle: {\n      value: 'solid'\n    },\n    borderWidth: {\n      value: '{borderWidths.small}'\n    },\n    paddingBlock: {\n      value: '{space.xs}'\n    },\n    paddingInline: {\n      value: '{space.small}'\n    },\n    gap: {\n      value: '{space.small}'\n    },\n    alignItems: {\n      value: 'baseline'\n    },\n    name: {\n      fontSize: {\n        value: '{fontSizes.medium}'\n      },\n      fontWeight: {\n        value: '{fontWeights.bold}'\n      },\n      color: {\n        value: '{colors.font.primary}'\n      }\n    },\n    size: {\n      fontSize: {\n        value: '{fontSizes.small}'\n      },\n      fontWeight: {\n        value: '{fontWeights.normal}'\n      },\n      color: {\n        value: '{colors.font.tertiary}'\n      }\n    },\n    image: {\n      width: {\n        value: '{space.xxl}'\n      },\n      height: {\n        value: '{space.xxl}'\n      },\n      backgroundColor: {\n        value: '{colors.background.secondary}'\n      },\n      color: {\n        value: '{colors.font.tertiary}'\n      },\n      borderRadius: {\n        value: '{radii.small}'\n      }\n    }\n  },\n  filelist: {\n    flexDirection: {\n      value: 'column'\n    },\n    gap: {\n      value: '{space.small}'\n    }\n  },\n  loader: {\n    strokeLinecap: {\n      value: 'round'\n    },\n    strokeEmpty: {\n      value: '{colors.border.secondary}'\n    },\n    strokeFilled: {\n      value: '{components.loader.strokeFilled}'\n    },\n    strokeWidth: {\n      value: '{borderWidths.large}'\n    }\n  },\n  previewer: {\n    backgroundColor: {\n      value: '{colors.background.primary}'\n    },\n    borderColor: {\n      value: '{colors.border.primary}'\n    },\n    borderStyle: {\n      value: 'solid'\n    },\n    borderWidth: {\n      value: '{borderWidths.small}'\n    },\n    borderRadius: {\n      value: '{radii.small}'\n    },\n    paddingBlock: {\n      value: '{space.zero}'\n    },\n    paddingInline: {\n      value: '{space.zero}'\n    },\n    maxHeight: {\n      value: '40rem'\n    },\n    maxWidth: {\n      value: 'auto'\n    },\n    text: {\n      fontSize: {\n        value: '{fontSizes.medium}'\n      },\n      fontWeight: {\n        value: '{fontWeights.bold}'\n      },\n      color: {\n        value: '{colors.font.primary}'\n      }\n    },\n    body: {\n      paddingBlock: {\n        value: '{space.medium}'\n      },\n      paddingInline: {\n        value: '{space.medium}'\n      },\n      gap: {\n        value: '{space.small}'\n      }\n    },\n    footer: {\n      justifyContent: {\n        value: 'flex-end'\n      }\n    }\n  }\n};\nexport { storagemanager };", "map": {"version": 3, "names": ["storagemanager", "dropzone", "backgroundColor", "value", "borderRadius", "borderColor", "borderStyle", "borderWidth", "gap", "paddingBlock", "paddingInline", "textAlign", "_active", "icon", "color", "fontSize", "text", "fontWeight", "file", "alignItems", "name", "size", "image", "width", "height", "filelist", "flexDirection", "loader", "strokeLinecap", "strokeEmpty", "strokeFilled", "strokeWidth", "previewer", "maxHeight", "max<PERSON><PERSON><PERSON>", "body", "footer", "justifyContent"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/storagemanager.mjs"], "sourcesContent": ["const storagemanager = {\n    dropzone: {\n        backgroundColor: { value: '{colors.background.primary}' },\n        borderRadius: { value: '{radii.small}' },\n        borderColor: { value: '{colors.border.primary}' },\n        borderStyle: { value: 'dashed' },\n        borderWidth: { value: '{borderWidths.small}' },\n        gap: { value: '{space.small}' },\n        paddingBlock: { value: '{space.xl}' },\n        paddingInline: { value: '{space.large}' },\n        textAlign: { value: 'center' },\n        _active: {\n            backgroundColor: { value: '{colors.primary.10}' },\n            borderRadius: {\n                value: '{components.storagemanager.dropzone.borderRadius}',\n            },\n            borderColor: { value: '{colors.border.pressed}' },\n            borderStyle: {\n                value: '{components.storagemanager.dropzone.borderStyle}',\n            },\n            borderWidth: { value: '{borderWidths.medium}' },\n        },\n        icon: {\n            color: { value: '{colors.border.primary}' },\n            fontSize: { value: '{fontSizes.xxl}' },\n        },\n        text: {\n            color: { value: '{colors.font.tertiary}' },\n            fontSize: { value: '{fontSizes.medium}' },\n            fontWeight: { value: '{fontWeights.bold}' },\n        },\n    },\n    file: {\n        backgroundColor: { value: '{colors.background.primary}' },\n        borderRadius: { value: '{radii.small}' },\n        borderColor: { value: '{colors.border.primary}' },\n        borderStyle: { value: 'solid' },\n        borderWidth: { value: '{borderWidths.small}' },\n        paddingBlock: { value: '{space.xs}' },\n        paddingInline: { value: '{space.small}' },\n        gap: { value: '{space.small}' },\n        alignItems: { value: 'baseline' },\n        name: {\n            fontSize: { value: '{fontSizes.medium}' },\n            fontWeight: { value: '{fontWeights.bold}' },\n            color: { value: '{colors.font.primary}' },\n        },\n        size: {\n            fontSize: { value: '{fontSizes.small}' },\n            fontWeight: { value: '{fontWeights.normal}' },\n            color: { value: '{colors.font.tertiary}' },\n        },\n        image: {\n            width: { value: '{space.xxl}' },\n            height: { value: '{space.xxl}' },\n            backgroundColor: { value: '{colors.background.secondary}' },\n            color: { value: '{colors.font.tertiary}' },\n            borderRadius: { value: '{radii.small}' },\n        },\n    },\n    filelist: {\n        flexDirection: { value: 'column' },\n        gap: { value: '{space.small}' },\n    },\n    loader: {\n        strokeLinecap: { value: 'round' },\n        strokeEmpty: { value: '{colors.border.secondary}' },\n        strokeFilled: { value: '{components.loader.strokeFilled}' },\n        strokeWidth: { value: '{borderWidths.large}' },\n    },\n    previewer: {\n        backgroundColor: { value: '{colors.background.primary}' },\n        borderColor: { value: '{colors.border.primary}' },\n        borderStyle: { value: 'solid' },\n        borderWidth: { value: '{borderWidths.small}' },\n        borderRadius: { value: '{radii.small}' },\n        paddingBlock: { value: '{space.zero}' },\n        paddingInline: { value: '{space.zero}' },\n        maxHeight: { value: '40rem' },\n        maxWidth: { value: 'auto' },\n        text: {\n            fontSize: { value: '{fontSizes.medium}' },\n            fontWeight: { value: '{fontWeights.bold}' },\n            color: { value: '{colors.font.primary}' },\n        },\n        body: {\n            paddingBlock: { value: '{space.medium}' },\n            paddingInline: { value: '{space.medium}' },\n            gap: { value: '{space.small}' },\n        },\n        footer: {\n            justifyContent: { value: 'flex-end' },\n        },\n    },\n};\n\nexport { storagemanager };\n"], "mappings": "AAAA,MAAMA,cAAc,GAAG;EACnBC,QAAQ,EAAE;IACNC,eAAe,EAAE;MAAEC,KAAK,EAAE;IAA8B,CAAC;IACzDC,YAAY,EAAE;MAAED,KAAK,EAAE;IAAgB,CAAC;IACxCE,WAAW,EAAE;MAAEF,KAAK,EAAE;IAA0B,CAAC;IACjDG,WAAW,EAAE;MAAEH,KAAK,EAAE;IAAS,CAAC;IAChCI,WAAW,EAAE;MAAEJ,KAAK,EAAE;IAAuB,CAAC;IAC9CK,GAAG,EAAE;MAAEL,KAAK,EAAE;IAAgB,CAAC;IAC/BM,YAAY,EAAE;MAAEN,KAAK,EAAE;IAAa,CAAC;IACrCO,aAAa,EAAE;MAAEP,KAAK,EAAE;IAAgB,CAAC;IACzCQ,SAAS,EAAE;MAAER,KAAK,EAAE;IAAS,CAAC;IAC9BS,OAAO,EAAE;MACLV,eAAe,EAAE;QAAEC,KAAK,EAAE;MAAsB,CAAC;MACjDC,YAAY,EAAE;QACVD,KAAK,EAAE;MACX,CAAC;MACDE,WAAW,EAAE;QAAEF,KAAK,EAAE;MAA0B,CAAC;MACjDG,WAAW,EAAE;QACTH,KAAK,EAAE;MACX,CAAC;MACDI,WAAW,EAAE;QAAEJ,KAAK,EAAE;MAAwB;IAClD,CAAC;IACDU,IAAI,EAAE;MACFC,KAAK,EAAE;QAAEX,KAAK,EAAE;MAA0B,CAAC;MAC3CY,QAAQ,EAAE;QAAEZ,KAAK,EAAE;MAAkB;IACzC,CAAC;IACDa,IAAI,EAAE;MACFF,KAAK,EAAE;QAAEX,KAAK,EAAE;MAAyB,CAAC;MAC1CY,QAAQ,EAAE;QAAEZ,KAAK,EAAE;MAAqB,CAAC;MACzCc,UAAU,EAAE;QAAEd,KAAK,EAAE;MAAqB;IAC9C;EACJ,CAAC;EACDe,IAAI,EAAE;IACFhB,eAAe,EAAE;MAAEC,KAAK,EAAE;IAA8B,CAAC;IACzDC,YAAY,EAAE;MAAED,KAAK,EAAE;IAAgB,CAAC;IACxCE,WAAW,EAAE;MAAEF,KAAK,EAAE;IAA0B,CAAC;IACjDG,WAAW,EAAE;MAAEH,KAAK,EAAE;IAAQ,CAAC;IAC/BI,WAAW,EAAE;MAAEJ,KAAK,EAAE;IAAuB,CAAC;IAC9CM,YAAY,EAAE;MAAEN,KAAK,EAAE;IAAa,CAAC;IACrCO,aAAa,EAAE;MAAEP,KAAK,EAAE;IAAgB,CAAC;IACzCK,GAAG,EAAE;MAAEL,KAAK,EAAE;IAAgB,CAAC;IAC/BgB,UAAU,EAAE;MAAEhB,KAAK,EAAE;IAAW,CAAC;IACjCiB,IAAI,EAAE;MACFL,QAAQ,EAAE;QAAEZ,KAAK,EAAE;MAAqB,CAAC;MACzCc,UAAU,EAAE;QAAEd,KAAK,EAAE;MAAqB,CAAC;MAC3CW,KAAK,EAAE;QAAEX,KAAK,EAAE;MAAwB;IAC5C,CAAC;IACDkB,IAAI,EAAE;MACFN,QAAQ,EAAE;QAAEZ,KAAK,EAAE;MAAoB,CAAC;MACxCc,UAAU,EAAE;QAAEd,KAAK,EAAE;MAAuB,CAAC;MAC7CW,KAAK,EAAE;QAAEX,KAAK,EAAE;MAAyB;IAC7C,CAAC;IACDmB,KAAK,EAAE;MACHC,KAAK,EAAE;QAAEpB,KAAK,EAAE;MAAc,CAAC;MAC/BqB,MAAM,EAAE;QAAErB,KAAK,EAAE;MAAc,CAAC;MAChCD,eAAe,EAAE;QAAEC,KAAK,EAAE;MAAgC,CAAC;MAC3DW,KAAK,EAAE;QAAEX,KAAK,EAAE;MAAyB,CAAC;MAC1CC,YAAY,EAAE;QAAED,KAAK,EAAE;MAAgB;IAC3C;EACJ,CAAC;EACDsB,QAAQ,EAAE;IACNC,aAAa,EAAE;MAAEvB,KAAK,EAAE;IAAS,CAAC;IAClCK,GAAG,EAAE;MAAEL,KAAK,EAAE;IAAgB;EAClC,CAAC;EACDwB,MAAM,EAAE;IACJC,aAAa,EAAE;MAAEzB,KAAK,EAAE;IAAQ,CAAC;IACjC0B,WAAW,EAAE;MAAE1B,KAAK,EAAE;IAA4B,CAAC;IACnD2B,YAAY,EAAE;MAAE3B,KAAK,EAAE;IAAmC,CAAC;IAC3D4B,WAAW,EAAE;MAAE5B,KAAK,EAAE;IAAuB;EACjD,CAAC;EACD6B,SAAS,EAAE;IACP9B,eAAe,EAAE;MAAEC,KAAK,EAAE;IAA8B,CAAC;IACzDE,WAAW,EAAE;MAAEF,KAAK,EAAE;IAA0B,CAAC;IACjDG,WAAW,EAAE;MAAEH,KAAK,EAAE;IAAQ,CAAC;IAC/BI,WAAW,EAAE;MAAEJ,KAAK,EAAE;IAAuB,CAAC;IAC9CC,YAAY,EAAE;MAAED,KAAK,EAAE;IAAgB,CAAC;IACxCM,YAAY,EAAE;MAAEN,KAAK,EAAE;IAAe,CAAC;IACvCO,aAAa,EAAE;MAAEP,KAAK,EAAE;IAAe,CAAC;IACxC8B,SAAS,EAAE;MAAE9B,KAAK,EAAE;IAAQ,CAAC;IAC7B+B,QAAQ,EAAE;MAAE/B,KAAK,EAAE;IAAO,CAAC;IAC3Ba,IAAI,EAAE;MACFD,QAAQ,EAAE;QAAEZ,KAAK,EAAE;MAAqB,CAAC;MACzCc,UAAU,EAAE;QAAEd,KAAK,EAAE;MAAqB,CAAC;MAC3CW,KAAK,EAAE;QAAEX,KAAK,EAAE;MAAwB;IAC5C,CAAC;IACDgC,IAAI,EAAE;MACF1B,YAAY,EAAE;QAAEN,KAAK,EAAE;MAAiB,CAAC;MACzCO,aAAa,EAAE;QAAEP,KAAK,EAAE;MAAiB,CAAC;MAC1CK,GAAG,EAAE;QAAEL,KAAK,EAAE;MAAgB;IAClC,CAAC;IACDiC,MAAM,EAAE;MACJC,cAAc,EAAE;QAAElC,KAAK,EAAE;MAAW;IACxC;EACJ;AACJ,CAAC;AAED,SAASH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}