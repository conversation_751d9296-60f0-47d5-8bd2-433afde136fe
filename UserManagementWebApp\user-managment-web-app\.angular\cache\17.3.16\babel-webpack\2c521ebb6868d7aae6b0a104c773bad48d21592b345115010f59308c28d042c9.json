{"ast": null, "code": "const alert = {\n  // Default styles\n  alignItems: {\n    value: 'center'\n  },\n  justifyContent: {\n    value: 'space-between'\n  },\n  color: {\n    value: '{colors.font.primary.value}'\n  },\n  backgroundColor: {\n    value: '{colors.background.tertiary.value}'\n  },\n  paddingBlock: {\n    value: '{space.small.value}'\n  },\n  paddingInline: {\n    value: '{space.medium.value}'\n  },\n  icon: {\n    size: {\n      value: '{fontSizes.xl.value}'\n    }\n  },\n  heading: {\n    fontSize: {\n      value: '{fontSizes.medium.value}'\n    },\n    fontWeight: {\n      value: '{fontWeights.bold.value}'\n    }\n  },\n  // Variations\n  info: {\n    color: {\n      value: '{colors.font.info.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.info.value}'\n    }\n  },\n  error: {\n    color: {\n      value: '{colors.font.error.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.error.value}'\n    }\n  },\n  warning: {\n    color: {\n      value: '{colors.font.warning.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.warning.value}'\n    }\n  },\n  success: {\n    color: {\n      value: '{colors.font.success.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.success.value}'\n    }\n  }\n};\nexport { alert };", "map": {"version": 3, "names": ["alert", "alignItems", "value", "justifyContent", "color", "backgroundColor", "paddingBlock", "paddingInline", "icon", "size", "heading", "fontSize", "fontWeight", "info", "error", "warning", "success"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/alert.mjs"], "sourcesContent": ["const alert = {\n    // Default styles\n    alignItems: { value: 'center' },\n    justifyContent: { value: 'space-between' },\n    color: { value: '{colors.font.primary.value}' },\n    backgroundColor: { value: '{colors.background.tertiary.value}' },\n    paddingBlock: { value: '{space.small.value}' },\n    paddingInline: { value: '{space.medium.value}' },\n    icon: {\n        size: { value: '{fontSizes.xl.value}' },\n    },\n    heading: {\n        fontSize: { value: '{fontSizes.medium.value}' },\n        fontWeight: { value: '{fontWeights.bold.value}' },\n    },\n    // Variations\n    info: {\n        color: { value: '{colors.font.info.value}' },\n        backgroundColor: { value: '{colors.background.info.value}' },\n    },\n    error: {\n        color: { value: '{colors.font.error.value}' },\n        backgroundColor: { value: '{colors.background.error.value}' },\n    },\n    warning: {\n        color: { value: '{colors.font.warning.value}' },\n        backgroundColor: { value: '{colors.background.warning.value}' },\n    },\n    success: {\n        color: { value: '{colors.font.success.value}' },\n        backgroundColor: { value: '{colors.background.success.value}' },\n    },\n};\n\nexport { alert };\n"], "mappings": "AAAA,MAAMA,KAAK,GAAG;EACV;EACAC,UAAU,EAAE;IAAEC,KAAK,EAAE;EAAS,CAAC;EAC/BC,cAAc,EAAE;IAAED,KAAK,EAAE;EAAgB,CAAC;EAC1CE,KAAK,EAAE;IAAEF,KAAK,EAAE;EAA8B,CAAC;EAC/CG,eAAe,EAAE;IAAEH,KAAK,EAAE;EAAqC,CAAC;EAChEI,YAAY,EAAE;IAAEJ,KAAK,EAAE;EAAsB,CAAC;EAC9CK,aAAa,EAAE;IAAEL,KAAK,EAAE;EAAuB,CAAC;EAChDM,IAAI,EAAE;IACFC,IAAI,EAAE;MAAEP,KAAK,EAAE;IAAuB;EAC1C,CAAC;EACDQ,OAAO,EAAE;IACLC,QAAQ,EAAE;MAAET,KAAK,EAAE;IAA2B,CAAC;IAC/CU,UAAU,EAAE;MAAEV,KAAK,EAAE;IAA2B;EACpD,CAAC;EACD;EACAW,IAAI,EAAE;IACFT,KAAK,EAAE;MAAEF,KAAK,EAAE;IAA2B,CAAC;IAC5CG,eAAe,EAAE;MAAEH,KAAK,EAAE;IAAiC;EAC/D,CAAC;EACDY,KAAK,EAAE;IACHV,KAAK,EAAE;MAAEF,KAAK,EAAE;IAA4B,CAAC;IAC7CG,eAAe,EAAE;MAAEH,KAAK,EAAE;IAAkC;EAChE,CAAC;EACDa,OAAO,EAAE;IACLX,KAAK,EAAE;MAAEF,KAAK,EAAE;IAA8B,CAAC;IAC/CG,eAAe,EAAE;MAAEH,KAAK,EAAE;IAAoC;EAClE,CAAC;EACDc,OAAO,EAAE;IACLZ,KAAK,EAAE;MAAEF,KAAK,EAAE;IAA8B,CAAC;IAC/CG,eAAe,EAAE;MAAEH,KAAK,EAAE;IAAoC;EAClE;AACJ,CAAC;AAED,SAASF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}