import { ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { AgGridAngular } from 'ag-grid-angular';
import { ProcessCellForExportParams } from 'ag-grid-community';
import { Subscription } from 'rxjs';
import { UserFavorite } from 'src/app/core/models/user-favorite';
import { CommunicationService } from 'src/app/core/services/communication.service';
import { UserActivityService } from 'src/app/core/services/user-activity.service';
import { ReportInfo } from 'src/app/core/models/report-info';
import { formatCurrency } from 'src/app/shared/utilities/format.functions';
import { MenuItem } from 'primeng/api';
import { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';
import { MenuModule } from 'primeng/menu';
import { ButtonModule } from 'primeng/button';
import { BadgeModule } from 'primeng/badge';
import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { TooltipModule } from 'primeng/tooltip';
import { ToolbarModule } from 'primeng/toolbar';
import { SplitButtonModule } from 'primeng/splitbutton';
import { ActionableGridViewModeLabels, ActionableGridViewModes } from 'src/app/core/enums/actionable-grid';
import { GridLayoutManagerComponent } from './grid-layout-manager/grid-layout-manager.component';
import { GridLayout } from '../model/grid-layout';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-actions-menu',
  templateUrl: './actions-menu.component.html',
  styleUrls: ['./actions-menu.component.scss'],
  standalone: true,
  imports: [FormsModule, ToolbarModule, TooltipModule, BadgeModule, NgClass, ButtonModule, MenuModule, SplitButtonModule, NgIf, GridLayoutManagerComponent, ToggleButtonModule]
})
export class ActionsMenuComponent implements OnInit, OnDestroy {

  @Input() previewMode: boolean;
  @Input() reportInfo: ReportInfo;
  @Input() checkHasChanges: boolean;
  @Input() checkHasSlicers: boolean;
  @Input() checkHasPendingChanges: boolean;
  @Input() agGrid: AgGridAngular;
  @Input() columnsToExport: string[];
  @Input() validationResultsCount: any;
  @Input() hasFailures = false;
  @Input() disabled = false;
  @Input() selectedView = ActionableGridViewModes.Month;
  @Input() calendarViewFlag = false;
  @Input() agentChatFlag = false;
  @Input() showColumnsOptions = false;
  @Input() layouts: GridLayout[];

  @Output() showFormEditor: EventEmitter<any> = new EventEmitter();
  @Output() undoLastChange: EventEmitter<any> = new EventEmitter();
  @Output() undoAll: EventEmitter<any> = new EventEmitter();
  @Output() saveChanges: EventEmitter<any> = new EventEmitter();
  @Output() refreshGridData: EventEmitter<any> = new EventEmitter();
  @Output() sendEmailClick: EventEmitter<any> = new EventEmitter();
  @Output() showRefiner: EventEmitter<any> = new EventEmitter();
  @Output() showValidationResults: EventEmitter<any> = new EventEmitter();
  @Output() changeSelectedView: EventEmitter<string> = new EventEmitter();
  @Output() showAgentChat: EventEmitter<string> = new EventEmitter();
  @Output() columnsOptionsChange: EventEmitter<boolean> = new EventEmitter();
  @Output() themeChange: EventEmitter<string> = new EventEmitter();

  mobileActions: MenuItem[];

  exportItems = [
    {
      value: null,
      styleClass: 'hidden'
    },
    {
      label: 'Send Email', icon: 'pi pi-envelope', command: () => {
        this.onClickSendEmail();
      }
    },
    {
      label: 'Export as Excel', icon: 'pi pi-file-excel', command: () => {
        this.onExcelExportClick();
      },
    },
    {
      label: 'Export as CSV', icon: 'pi pi-file', command: () => {
        this.onCsvExportClick();
      }
    }
  ];

  calendarViews = [];
  undoActions = [];
  isFavorite: boolean;
  selectedViewIcon: string = 'fa-regular fa-calendar';
  slectedLayout: GridLayout | undefined;
  columnsOptions = false;

  private userFavoriteSub: Subscription;
  private allReportFavorites: UserFavorite[] = [];

  constructor(
    private communicationService: CommunicationService,
    private changeDetectorRef: ChangeDetectorRef,
    private userActivityService: UserActivityService
  ) { }

  ngOnInit(): void {
    this.userFavoriteSub = this.communicationService.userFavorite
      .subscribe(event => {
        this.allReportFavorites = event.data;
        this.setIsFavorite();
      });
    this.communicationService.refreshUserFavoriteList();

    this.mobileActions = [
      {
        label: 'Favorite',
        icon: 'pi pi-star',
        command: () => {
          this.onUpdateUserFavorite();
        }
      },
      {
        label: 'Add',
        icon: 'pi pi-plus',
        command: () => {
          this.onClickShowFormEditor(false);
        }
      },
      {
        label: 'Undo',
        icon: 'sb sb-icon-undo',
        command: () => {
          this.onClickUndoLastChange();
        }
      },
      {
        label: 'Undo All',
        icon: 'pi pi-times',
        command: () => {
          this.onClickUndoAll();
        }
      },
      {
        label: 'Save',
        icon: 'pi pi-save',
        command: () => {
          this.onClickSaveChanges();
        }
      },
      {
        label: 'Refresh',
        icon: 'pi pi-sync',
        command: () => {
          this.onClickRefreshGridData();
        }
      },
      {
        label: 'Send Email',
        icon: 'pi pi-envelope',
        command: () => {
          this.onClickSendEmail();
        }
      },
      {
        label: 'Export to Excel',
        icon: 'pi pi-file-excel',
        command: () => {
          this.onExcelExportClick();
        }
      },
      {
        label: 'Export to CSV',
        icon: 'pi pi-file',
        command: () => {
          this.onCsvExportClick();
        }
      },
    ];

    this.calendarViews = [
      {
        value: null,
        styleClass: 'hidden'
      },
      {
        value: ActionableGridViewModes.Table, label: ActionableGridViewModeLabels.Table, icon: 'pi pi-list',
        command: () => this.onChangeSelectedView(ActionableGridViewModes.Table, 'pi pi-list')
      },
      {
        value: ActionableGridViewModes.Year, label: ActionableGridViewModeLabels.Year, icon: 'fa-solid fa-calendar',
        command: () => this.onChangeSelectedView(ActionableGridViewModes.Year, 'fa-solid fa-calendar')
      },
      {
        value: ActionableGridViewModes.Month, label: ActionableGridViewModeLabels.Month, icon: 'fa-regular fa-calendar',
        command: () => this.onChangeSelectedView(ActionableGridViewModes.Month, 'fa-regular fa-calendar')
      },
      {
        value: ActionableGridViewModes.Week, label: ActionableGridViewModeLabels.Week, icon: 'fa-solid fa-calendar-week',
        command: () => this.onChangeSelectedView(ActionableGridViewModes.Week, 'fa-solid fa-calendar-week')
      },
    ];

    this.undoActions = [
      {
        label: 'Undo', icon: 'sb sb-icon-undo', command: () => this.onClickUndoLastChange()
      },
      {
        label: 'Undo All', icon: 'pi pi-times', command: () => this.onClickUndoAll()
      },
    ];
  }

  ngOnDestroy() {
    if (this.userFavoriteSub) {
      this.userFavoriteSub.unsubscribe();
    }
  }

  onClickShowFormEditor(arg: boolean): void {
    if (this.disabled) return;

    this.showFormEditor.emit(arg);
  }

  onClickUndoLastChange(): void {
    if (this.disabled) return;

    this.undoLastChange.emit();
  }

  onClickUndoAll(): void {
    if (this.disabled) return;

    this.undoAll.emit();
  }

  onClickSaveChanges(): void {
    if (this.disabled) return;

    this.saveChanges.emit();
  }

  onClickRefreshGridData(): void {
    if (this.disabled) return;

    this.refreshGridData.emit();
  }

  onClickShowRefiner(): void {
    if (this.disabled) return;

    this.showRefiner.emit();
  }

  onClickShowValidationResults(): void {
    this.showValidationResults.emit();
  }

  onClickShowAgent(): void {
    this.showAgentChat.emit();
  }

  setIsFavorite() {
    this.isFavorite = this.allReportFavorites.some(f => f.reportId === this.reportInfo?.reportId && f.isApp);
    // for some weird reason angular doesn't detect the changes
    this.changeDetectorRef.detectChanges();
  }

  onUpdateUserFavorite(): void {
    let userFavorite: UserFavorite = this.allReportFavorites?.find(favorite =>
      favorite.projectId.toString() === this.reportInfo.projectId && favorite.reportId === this.reportInfo.reportId);

    if (userFavorite) {
      userFavorite.active = false;
      userFavorite.isApp = window.location.pathname.includes('app-view') ? true : false;
    }
    else {
      userFavorite = this.addNewUserFavorite();
    }
    this.userActivityService.upsertUserFavorite(userFavorite);
    // because of the delay we set isFavorite temporarily, after updating is done it will update again.
    this.isFavorite = userFavorite.active;
  }

  addNewUserFavorite() {
    const userFavorite: UserFavorite = {
      projectId: +this.reportInfo.projectId,
      url: window.location.pathname,
      reportId: this.reportInfo.reportId,
      reportName: this.reportInfo.reportName,
      projectVersionId: +this.reportInfo.projectVersionId,
      active: true,
      isApp: window.location.pathname.includes('app-view') ? true : false
    };
    return userFavorite;
  }

  onClickSendEmail(): void {
    if (this.disabled) return;

    this.sendEmailClick.emit();
  }

  onExcelExportClick() {
    if (this.disabled) return;

    this.agGrid.api.exportDataAsExcel({
      processCellCallback: (params) => {
        return this.processCellsForExport(params);
      },
      columnKeys: this.columnsToExport, fileName: this.reportInfo.reportName, sheetName: this.reportInfo.reportName
    });
  }

  onCsvExportClick() {
    if (this.disabled) return;

    this.agGrid.api.exportDataAsCsv({
      processCellCallback: (params) => {
        return this.processCellsForExport(params);
      },
      columnKeys: this.columnsToExport, fileName: this.reportInfo.reportName
    });
  }

  processCellsForExport(params: ProcessCellForExportParams) {
    const exportParamsColDef = params.column.getColDef();
    const actionableGridColDef = this.reportInfo.formatConfig?.actionableGridColumnsConfig.find(x => x.column === exportParamsColDef.field);
    if (actionableGridColDef?.format.type === EditorColumnType.Currency) {
      exportParamsColDef.valueFormatter = formatCurrency(
        params.value,
        actionableGridColDef.format.currency,
        actionableGridColDef.format.decimalPlaces);
      return exportParamsColDef.valueFormatter;
    }

    return params.value;
  }

  onChangeSelectedView(view: ActionableGridViewModes, icon: string) {
    this.changeSelectedView.emit(view);
    this.selectedViewIcon = icon;
    this.selectedView = view;


    // Resize columns if no layout is selected
    setTimeout(() => {
      if (this.slectedLayout === undefined) {
        this.agGrid.api?.autoSizeAllColumns();
      }
    }, 0);
  }

  onSelectedLayoutChange(layout: GridLayout) {
    this.columnsOptions = layout?.agGridSettings?.sideBar === 'columns';
    this.slectedLayout = layout;
  }

  onColumnsOptionsChange(event: any) {
    this.columnsOptionsChange.emit(event.checked);
  }
}
