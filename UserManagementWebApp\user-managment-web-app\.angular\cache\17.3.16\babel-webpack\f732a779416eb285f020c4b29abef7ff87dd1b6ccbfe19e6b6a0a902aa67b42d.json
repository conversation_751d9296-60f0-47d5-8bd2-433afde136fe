{"ast": null, "code": "import { base64Encoder } from '@aws-amplify/core/internals/utils';\nimport { getRandomBytes } from './getRandomBytes.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Helper function to generate a random string\n * @returns {string} a random value.\n *\n * @internal\n */\nconst getRandomString = () => base64Encoder.convert(getRandomBytes(40));\nexport { getRandomString };", "map": {"version": 3, "names": ["base64Encoder", "getRandomBytes", "getRandomString", "convert"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/srp/getRandomString.mjs"], "sourcesContent": ["import { base64Encoder } from '@aws-amplify/core/internals/utils';\nimport { getRandomBytes } from './getRandomBytes.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Helper function to generate a random string\n * @returns {string} a random value.\n *\n * @internal\n */\nconst getRandomString = () => base64Encoder.convert(getRandomBytes(40));\n\nexport { getRandomString };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,mCAAmC;AACjE,SAASC,cAAc,QAAQ,sBAAsB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGA,CAAA,KAAMF,aAAa,CAACG,OAAO,CAACF,cAAc,CAAC,EAAE,CAAC,CAAC;AAEvE,SAASC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}