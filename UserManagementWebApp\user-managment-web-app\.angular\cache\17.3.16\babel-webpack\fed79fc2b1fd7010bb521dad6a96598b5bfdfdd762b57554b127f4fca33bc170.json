{"ast": null, "code": "const divider = {\n  borderStyle: {\n    value: 'solid'\n  },\n  borderColor: {\n    value: '{colors.border.primary.value}'\n  },\n  borderWidth: {\n    value: '{borderWidths.medium.value}'\n  },\n  label: {\n    color: {\n      value: '{colors.font.tertiary.value}'\n    },\n    paddingInline: {\n      value: '{space.medium.value}'\n    },\n    fontSize: {\n      value: '{fontSizes.small.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.primary.value}'\n    }\n  },\n  small: {\n    borderWidth: {\n      value: '{borderWidths.small.value}'\n    }\n  },\n  large: {\n    borderWidth: {\n      value: '{borderWidths.large.value}'\n    }\n  },\n  opacity: {\n    value: '{opacities.60.value}'\n  }\n};\nexport { divider };", "map": {"version": 3, "names": ["divider", "borderStyle", "value", "borderColor", "borderWidth", "label", "color", "paddingInline", "fontSize", "backgroundColor", "small", "large", "opacity"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/divider.mjs"], "sourcesContent": ["const divider = {\n    borderStyle: { value: 'solid' },\n    borderColor: { value: '{colors.border.primary.value}' },\n    borderWidth: { value: '{borderWidths.medium.value}' },\n    label: {\n        color: { value: '{colors.font.tertiary.value}' },\n        paddingInline: { value: '{space.medium.value}' },\n        fontSize: { value: '{fontSizes.small.value}' },\n        backgroundColor: { value: '{colors.background.primary.value}' },\n    },\n    small: {\n        borderWidth: { value: '{borderWidths.small.value}' },\n    },\n    large: {\n        borderWidth: { value: '{borderWidths.large.value}' },\n    },\n    opacity: {\n        value: '{opacities.60.value}',\n    },\n};\n\nexport { divider };\n"], "mappings": "AAAA,MAAMA,OAAO,GAAG;EACZC,WAAW,EAAE;IAAEC,KAAK,EAAE;EAAQ,CAAC;EAC/BC,WAAW,EAAE;IAAED,KAAK,EAAE;EAAgC,CAAC;EACvDE,WAAW,EAAE;IAAEF,KAAK,EAAE;EAA8B,CAAC;EACrDG,KAAK,EAAE;IACHC,KAAK,EAAE;MAAEJ,KAAK,EAAE;IAA+B,CAAC;IAChDK,aAAa,EAAE;MAAEL,KAAK,EAAE;IAAuB,CAAC;IAChDM,QAAQ,EAAE;MAAEN,KAAK,EAAE;IAA0B,CAAC;IAC9CO,eAAe,EAAE;MAAEP,KAAK,EAAE;IAAoC;EAClE,CAAC;EACDQ,KAAK,EAAE;IACHN,WAAW,EAAE;MAAEF,KAAK,EAAE;IAA6B;EACvD,CAAC;EACDS,KAAK,EAAE;IACHP,WAAW,EAAE;MAAEF,KAAK,EAAE;IAA6B;EACvD,CAAC;EACDU,OAAO,EAAE;IACLV,KAAK,EAAE;EACX;AACJ,CAAC;AAED,SAASF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}