{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ConsoleLogger } from '../Logger/ConsoleLogger.mjs';\nimport { KeyValueStorage } from '../storage/KeyValueStorage.mjs';\nimport { getLocalStorageWithFallback } from '../storage/utils.mjs';\nimport { defaultConfig } from './constants.mjs';\nimport { StorageCacheCommon } from './StorageCacheCommon.mjs';\nimport { getCurrentSizeKey, getCurrentTime } from './utils/cacheHelpers.mjs';\nimport './utils/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('StorageCache');\n/**\n * Customized storage based on the SessionStorage or LocalStorage with LRU implemented\n */\nclass StorageCache extends StorageCacheCommon {\n  /**\n   * initialize the cache\n   * @param config - the configuration of the cache\n   */\n  constructor(config) {\n    const storage = getLocalStorageWithFallback();\n    super({\n      config,\n      keyValueStorage: new KeyValueStorage(storage)\n    });\n    this.storage = storage;\n    this.getItem = this.getItem.bind(this);\n    this.setItem = this.setItem.bind(this);\n    this.removeItem = this.removeItem.bind(this);\n  }\n  getAllCacheKeys(options) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const {\n        omitSizeKey\n      } = options ?? {};\n      const keys = [];\n      for (let i = 0; i < _this.storage.length; i++) {\n        const key = _this.storage.key(i);\n        if (omitSizeKey && key === getCurrentSizeKey(_this.config.keyPrefix)) {\n          continue;\n        }\n        if (key?.startsWith(_this.config.keyPrefix)) {\n          keys.push(key.substring(_this.config.keyPrefix.length));\n        }\n      }\n      return keys;\n    })();\n  }\n  /**\n   * Return a new instance of cache with customized configuration.\n   * @param {Object} config - the customized configuration\n   * @return {Object} - the new instance of Cache\n   */\n  createInstance(config) {\n    if (!config.keyPrefix || config.keyPrefix === defaultConfig.keyPrefix) {\n      logger.error('invalid keyPrefix, setting keyPrefix with timeStamp');\n      config.keyPrefix = getCurrentTime.toString();\n    }\n    return new StorageCache(config);\n  }\n}\nexport { StorageCache };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "KeyValueStorage", "getLocalStorageWithFallback", "defaultConfig", "StorageCacheCommon", "getCurrentSizeKey", "getCurrentTime", "logger", "StorageCache", "constructor", "config", "storage", "keyValueStorage", "getItem", "bind", "setItem", "removeItem", "getAllCache<PERSON><PERSON>s", "options", "_this", "_asyncToGenerator", "omitS<PERSON><PERSON>ey", "keys", "i", "length", "key", "keyPrefix", "startsWith", "push", "substring", "createInstance", "error", "toString"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Cache/StorageCache.mjs"], "sourcesContent": ["import { ConsoleLogger } from '../Logger/ConsoleLogger.mjs';\nimport { KeyValueStorage } from '../storage/KeyValueStorage.mjs';\nimport { getLocalStorageWithFallback } from '../storage/utils.mjs';\nimport { defaultConfig } from './constants.mjs';\nimport { StorageCacheCommon } from './StorageCacheCommon.mjs';\nimport { getCurrentSizeKey, getCurrentTime } from './utils/cacheHelpers.mjs';\nimport './utils/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('StorageCache');\n/**\n * Customized storage based on the SessionStorage or LocalStorage with LRU implemented\n */\nclass StorageCache extends StorageCacheCommon {\n    /**\n     * initialize the cache\n     * @param config - the configuration of the cache\n     */\n    constructor(config) {\n        const storage = getLocalStorageWithFallback();\n        super({ config, keyValueStorage: new KeyValueStorage(storage) });\n        this.storage = storage;\n        this.getItem = this.getItem.bind(this);\n        this.setItem = this.setItem.bind(this);\n        this.removeItem = this.removeItem.bind(this);\n    }\n    async getAllCacheKeys(options) {\n        const { omitSizeKey } = options ?? {};\n        const keys = [];\n        for (let i = 0; i < this.storage.length; i++) {\n            const key = this.storage.key(i);\n            if (omitSizeKey && key === getCurrentSizeKey(this.config.keyPrefix)) {\n                continue;\n            }\n            if (key?.startsWith(this.config.keyPrefix)) {\n                keys.push(key.substring(this.config.keyPrefix.length));\n            }\n        }\n        return keys;\n    }\n    /**\n     * Return a new instance of cache with customized configuration.\n     * @param {Object} config - the customized configuration\n     * @return {Object} - the new instance of Cache\n     */\n    createInstance(config) {\n        if (!config.keyPrefix || config.keyPrefix === defaultConfig.keyPrefix) {\n            logger.error('invalid keyPrefix, setting keyPrefix with timeStamp');\n            config.keyPrefix = getCurrentTime.toString();\n        }\n        return new StorageCache(config);\n    }\n}\n\nexport { StorageCache };\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,2BAA2B,QAAQ,sBAAsB;AAClE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,iBAAiB,EAAEC,cAAc,QAAQ,0BAA0B;AAC5E,OAAO,0BAA0B;;AAEjC;AACA;AACA,MAAMC,MAAM,GAAG,IAAIP,aAAa,CAAC,cAAc,CAAC;AAChD;AACA;AACA;AACA,MAAMQ,YAAY,SAASJ,kBAAkB,CAAC;EAC1C;AACJ;AACA;AACA;EACIK,WAAWA,CAACC,MAAM,EAAE;IAChB,MAAMC,OAAO,GAAGT,2BAA2B,CAAC,CAAC;IAC7C,KAAK,CAAC;MAAEQ,MAAM;MAAEE,eAAe,EAAE,IAAIX,eAAe,CAACU,OAAO;IAAE,CAAC,CAAC;IAChE,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,OAAO,GAAG,IAAI,CAACA,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACC,OAAO,GAAG,IAAI,CAACA,OAAO,CAACD,IAAI,CAAC,IAAI,CAAC;IACtC,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,CAACF,IAAI,CAAC,IAAI,CAAC;EAChD;EACMG,eAAeA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC3B,MAAM;QAAEC;MAAY,CAAC,GAAGH,OAAO,IAAI,CAAC,CAAC;MACrC,MAAMI,IAAI,GAAG,EAAE;MACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAI,CAACR,OAAO,CAACa,MAAM,EAAED,CAAC,EAAE,EAAE;QAC1C,MAAME,GAAG,GAAGN,KAAI,CAACR,OAAO,CAACc,GAAG,CAACF,CAAC,CAAC;QAC/B,IAAIF,WAAW,IAAII,GAAG,KAAKpB,iBAAiB,CAACc,KAAI,CAACT,MAAM,CAACgB,SAAS,CAAC,EAAE;UACjE;QACJ;QACA,IAAID,GAAG,EAAEE,UAAU,CAACR,KAAI,CAACT,MAAM,CAACgB,SAAS,CAAC,EAAE;UACxCJ,IAAI,CAACM,IAAI,CAACH,GAAG,CAACI,SAAS,CAACV,KAAI,CAACT,MAAM,CAACgB,SAAS,CAACF,MAAM,CAAC,CAAC;QAC1D;MACJ;MACA,OAAOF,IAAI;IAAC;EAChB;EACA;AACJ;AACA;AACA;AACA;EACIQ,cAAcA,CAACpB,MAAM,EAAE;IACnB,IAAI,CAACA,MAAM,CAACgB,SAAS,IAAIhB,MAAM,CAACgB,SAAS,KAAKvB,aAAa,CAACuB,SAAS,EAAE;MACnEnB,MAAM,CAACwB,KAAK,CAAC,qDAAqD,CAAC;MACnErB,MAAM,CAACgB,SAAS,GAAGpB,cAAc,CAAC0B,QAAQ,CAAC,CAAC;IAChD;IACA,OAAO,IAAIxB,YAAY,CAACE,MAAM,CAAC;EACnC;AACJ;AAEA,SAASF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}