{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/SaltboxActionableGrid/Saltbox-Data-App/saltbox-data-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { FileUploadDetails } from '../../shared/models/file-upload-details';\nimport moment from 'moment/moment';\nimport { HttpEventType } from '@angular/common/http';\nimport { FileUploadModule } from 'primeng/fileupload';\nimport { UserPermissionLevels } from 'src/app/core/enums/shared';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ButtonModule } from 'primeng/button';\nimport { TableModule } from 'primeng/table';\nimport { SharedModule } from 'primeng/api';\nimport { NgClass } from '@angular/common';\nimport { PanelModule } from 'primeng/panel';\nimport { SkeletonModule } from 'primeng/skeleton';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/notification.service\";\nimport * as i2 from \"primeng/fileupload\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/button\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/tooltip\";\nimport * as i7 from \"primeng/panel\";\nimport * as i8 from \"primeng/skeleton\";\nconst _c0 = [\"fileUploader\"];\nfunction FileUploadComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtext(1, \"You can upload files with the button or drag-and-drop.\");\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵtext(3, \" Once uploaded, make sure you click the Save button on the grid to attach the files.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FileUploadComponent_ng_template_4_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtext(1, \" Drag & Drop Files here\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FileUploadComponent_ng_template_4_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵtext(1, \"Loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FileUploadComponent_ng_template_4_Conditional_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 14);\n    i0.ɵɵtext(2, \"File\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 15);\n    i0.ɵɵtext(4, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 16);\n    i0.ɵɵtext(6, \"Uploaded On\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 17);\n    i0.ɵɵtext(8, \"Uploaded By\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"th\", 17);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FileUploadComponent_ng_template_4_Conditional_2_ng_template_3_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 22);\n    i0.ɵɵlistener(\"onClick\", function FileUploadComponent_ng_template_4_Conditional_2_ng_template_3_Conditional_12_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const file_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onRemoveFileClick(file_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"text\", true);\n  }\n}\nfunction FileUploadComponent_ng_template_4_Conditional_2_ng_template_3_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 23);\n    i0.ɵɵlistener(\"onClick\", function FileUploadComponent_ng_template_4_Conditional_2_ng_template_3_Conditional_13_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const file_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onPreviewFileClick(file_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"outlined\", true);\n  }\n}\nfunction FileUploadComponent_ng_template_4_Conditional_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 18)(11, \"p-button\", 19);\n    i0.ɵɵlistener(\"onClick\", function FileUploadComponent_ng_template_4_Conditional_2_ng_template_3_Template_p_button_onClick_11_listener() {\n      const file_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onDownloadFileClick(file_r3));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, FileUploadComponent_ng_template_4_Conditional_2_ng_template_3_Conditional_12_Template, 1, 1, \"p-button\", 20)(13, FileUploadComponent_ng_template_4_Conditional_2_ng_template_3_Conditional_13_Template, 1, 1, \"p-button\", 21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r3.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r3.fileShareFileType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r3.uploadedDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(file_r3.uploadedByName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"outlined\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(12, ctx_r3.userPermissionLevel !== \"View\" ? 12 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(13, ctx_r3.imageFileTypes.includes(file_r3.fileShareFileType) ? 13 : -1);\n  }\n}\nfunction FileUploadComponent_ng_template_4_Conditional_2_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Files: \", ctx_r3.uploadedFiles.length, \"\");\n  }\n}\nfunction FileUploadComponent_ng_template_4_Conditional_2_Conditional_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function FileUploadComponent_ng_template_4_Conditional_2_Conditional_5_ng_template_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.previewAttachmentInfo = undefined);\n    });\n    i0.ɵɵelement(1, \"span\", 30);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FileUploadComponent_ng_template_4_Conditional_2_Conditional_5_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-skeleton\", 27);\n  }\n}\nfunction FileUploadComponent_ng_template_4_Conditional_2_Conditional_5_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 28);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"src\", ctx_r3.previewAttachmentInfo.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction FileUploadComponent_ng_template_4_Conditional_2_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-panel\", 13);\n    i0.ɵɵtemplate(1, FileUploadComponent_ng_template_4_Conditional_2_Conditional_5_ng_template_1_Template, 2, 0, \"ng-template\", 25);\n    i0.ɵɵelementStart(2, \"p\", 26);\n    i0.ɵɵtemplate(3, FileUploadComponent_ng_template_4_Conditional_2_Conditional_5_Conditional_3_Template, 1, 0, \"p-skeleton\", 27)(4, FileUploadComponent_ng_template_4_Conditional_2_Conditional_5_Conditional_4_Template, 1, 1, \"img\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"header\", \"Preview (\" + ctx_r3.previewAttachmentInfo.fileName + \")\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(3, !ctx_r3.previewAttachmentInfo.url ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(4, ctx_r3.previewAttachmentInfo.url && ctx_r3.imageFileTypes.includes(ctx_r3.previewAttachmentInfo.fileType) ? 4 : -1);\n  }\n}\nfunction FileUploadComponent_ng_template_4_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"p-table\", 9);\n    i0.ɵɵtemplate(2, FileUploadComponent_ng_template_4_Conditional_2_ng_template_2_Template, 10, 0, \"ng-template\", 10)(3, FileUploadComponent_ng_template_4_Conditional_2_ng_template_3_Template, 14, 7, \"ng-template\", 11)(4, FileUploadComponent_ng_template_4_Conditional_2_ng_template_4_Template, 2, 1, \"ng-template\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, FileUploadComponent_ng_template_4_Conditional_2_Conditional_5_Template, 5, 3, \"p-panel\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r3.uploadedFiles)(\"tableStyle\", ctx_r3.tableStyle);\n    i0.ɵɵadvance(4);\n    i0.ɵɵconditional(5, ctx_r3.previewAttachmentInfo ? 5 : -1);\n  }\n}\nfunction FileUploadComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FileUploadComponent_ng_template_4_Conditional_0_Template, 2, 0, \"div\", 7)(1, FileUploadComponent_ng_template_4_Conditional_1_Template, 2, 0, \"div\", 7)(2, FileUploadComponent_ng_template_4_Conditional_2_Template, 6, 3, \"div\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, !ctx_r3.uploadedFiles.length && !ctx_r3.loadingFiles ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r3.loadingFiles && !ctx_r3.uploadedFiles.length ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(2, ctx_r3.uploadedFiles.length ? 2 : -1);\n  }\n}\nfunction FileUploadComponent_ng_template_5_Template(rf, ctx) {}\nexport class FileUploadComponent {\n  constructor(messageSvc) {\n    this.messageSvc = messageSvc;\n    this.uploadedFiles = [];\n    this.maximumFileSize = 5242880; // Maximum file size for each file\n    this.multipleAllowed = true; // Defines the number of uploads allowed to upload\n    this.fileToAccept = '.pdf,.xls,.xlsx,.doc,.csv,.docx,.txt,.ppt,.pptx,.jpeg,.jpg,.gif,.png'; // File Types to Accept\n    this.fileUploadContainerClass = 'default-fileUpload'; // Customized Default container DIV wrapper-class\n    this.projectId = '';\n    this.versionId = '';\n    this.dataStoreName = '';\n    this.recordId = '';\n    this.dateFormat = 'YYYY/MM/DD';\n    this.aliasIds = [];\n    this.isPreview = false;\n    this.userPermissionLevel = UserPermissionLevels.EditAndDelete;\n    this.fileUploadEvent = new EventEmitter();\n    this.getFilesUploaded = new EventEmitter();\n    this.loadingFiles = false;\n    this.tableStyle = {\n      'table-layout': 'auto',\n      width: '100%'\n    };\n    this.imageFileTypes = ['image/jpg', 'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/tiff', 'image/webp', 'image/svg+xml', 'image/x-icon', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp', 'svg+xml', 'x-icon', 'svg', 'ico'];\n  }\n  ngOnInit() {\n    if (this.aliasIds) {\n      this.getFilesFromServer();\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes.aliasIds && !changes.aliasIds?.isFirstChange()) {\n      this.getFilesFromServer();\n    }\n  }\n  onFileSelectClick(event) {\n    setTimeout(() => {}, 10000);\n    const currentFiles = event.currentFiles;\n    this.processSelectedFiles(currentFiles).then(res => {\n      this.aliasIds = this.uploadedFiles.map(file => {\n        return file.aliasId;\n      });\n      this.fileUploadEvent.emit(this.aliasIds);\n      this.fileUploader.clear();\n    });\n  }\n  processSelectedFiles(files) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      for (const file of files) {\n        const fileUploadDetails = new FileUploadDetails(_this.projectId, _this.versionId, _this.recordId, _this.dataStoreName, file.name, file);\n        yield _this.saveFile(fileUploadDetails);\n      }\n    })();\n  }\n  saveFile(fileUploadDetails) {\n    return new Promise(resolve => {\n      this.fileService.uploadFile(fileUploadDetails).subscribe({\n        next: fileInfo => {\n          fileInfo.uploadedDate = moment(fileInfo.uploadedDate, this.dateFormat, false).format(this.dateFormat).toString();\n          this.uploadedFiles.push(fileInfo);\n          resolve();\n        },\n        error: error => {\n          console.log(error);\n          resolve();\n        }\n      });\n    });\n  }\n  getFilesFromServer() {\n    var _this2 = this;\n    this.loadingFiles = true;\n    const fileUploadDetails = new FileUploadDetails(this.projectId, this.versionId, this.recordId, this.dataStoreName);\n    fileUploadDetails.aliasIds = this.aliasIds;\n    // start rewriting some things here\n    const getFilesCall = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* () {\n        if (_this2.recordId) {\n          yield _this2.getFileInfo(fileUploadDetails);\n        }\n      });\n      return function getFilesCall() {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    getFilesCall().then(res => {\n      this.loadingFiles = false;\n    });\n  }\n  getFileInfo(fileUploadDetails) {\n    return new Promise(resolve => {\n      this.fileService.getFiles(fileUploadDetails).subscribe({\n        next: filesInfo => {\n          filesInfo.forEach(fileUpload => {\n            fileUpload.uploadedDate = moment(fileUpload.uploadedDate, this.dateFormat, false).format(this.dateFormat).toString();\n          });\n          this.uploadedFiles = filesInfo;\n          resolve();\n        },\n        error: error => {\n          console.log(`File Request Error: ${error.statusText}...`);\n          resolve();\n        }\n      });\n    });\n  }\n  onDownloadFileClick(attchmentInfo) {\n    const fileUploadDetails = new FileUploadDetails(this.projectId, this.versionId, this.recordId, this.dataStoreName, attchmentInfo.name);\n    fileUploadDetails.aliasId = attchmentInfo.aliasId;\n    this.fileService.getFileContent(fileUploadDetails).subscribe({\n      next: event => {\n        if (event.type === HttpEventType.Response) {\n          const targetFileElement = document.createElement('a');\n          targetFileElement.setAttribute('style', 'display:none;');\n          document.body.appendChild(targetFileElement);\n          targetFileElement.href = URL.createObjectURL(event.body);\n          targetFileElement.download = `${attchmentInfo.name}.${attchmentInfo.fileShareFileType}`;\n          targetFileElement.target = '_blank';\n          targetFileElement.click();\n          document.body.removeChild(targetFileElement);\n        }\n      },\n      error: error => {\n        console.log(`File Download Error: ${error.status}: Unable to complete request...`);\n      }\n    });\n  }\n  onPreviewFileClick(attchmentInfo) {\n    const fileUploadDetails = new FileUploadDetails(this.projectId, this.versionId, this.recordId, this.dataStoreName, attchmentInfo.name);\n    fileUploadDetails.aliasId = attchmentInfo.aliasId;\n    this.previewAttachmentInfo = {\n      url: undefined,\n      fileType: attchmentInfo.fileShareFileType,\n      fileName: attchmentInfo.name\n    };\n    this.fileService.getFileContent(fileUploadDetails).subscribe({\n      next: event => {\n        if (event.type === HttpEventType.Response) {\n          const url = URL.createObjectURL(event.body);\n          this.previewAttachmentInfo.url = url;\n        }\n      },\n      error: error => {\n        console.log(`File Download Error: ${error.status}: Unable to complete request...`);\n      }\n    });\n  }\n  onRemoveFileClick(fileInfo) {\n    const fileUploadDetails = new FileUploadDetails(this.projectId, this.versionId, this.recordId, this.dataStoreName, fileInfo.name);\n    fileUploadDetails.aliasId = fileInfo.aliasId;\n    this.fileService.deleteFile(fileUploadDetails).subscribe({\n      next: () => {\n        const fileIndex = this.uploadedFiles.findIndex(file => file.aliasId === fileInfo.aliasId);\n        if (fileIndex >= 0) {\n          this.uploadedFiles.splice(fileIndex, 1);\n          this.fileUploadEvent.emit(this.uploadedFiles.map(f => f.aliasId));\n        }\n      },\n      error: () => {\n        this.messageSvc.showError('Error', `There was a problem deleting '${fileInfo.name}' from the server`);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function FileUploadComponent_Factory(t) {\n      return new (t || FileUploadComponent)(i0.ɵɵdirectiveInject(i1.NotificationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FileUploadComponent,\n      selectors: [[\"app-file-upload\"]],\n      viewQuery: function FileUploadComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileUploader = _t.first);\n        }\n      },\n      inputs: {\n        uploadedFiles: \"uploadedFiles\",\n        maximumFileSize: \"maximumFileSize\",\n        multipleAllowed: \"multipleAllowed\",\n        fileToAccept: \"fileToAccept\",\n        fileUploadContainerClass: \"fileUploadContainerClass\",\n        projectId: \"projectId\",\n        versionId: \"versionId\",\n        dataStoreName: \"dataStoreName\",\n        recordId: \"recordId\",\n        dateFormat: \"dateFormat\",\n        aliasIds: \"aliasIds\",\n        fileService: \"fileService\",\n        isPreview: \"isPreview\",\n        userPermissionLevel: \"userPermissionLevel\"\n      },\n      outputs: {\n        fileUploadEvent: \"fileUploadEvent\",\n        getFilesUploaded: \"getFilesUploaded\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 7,\n      consts: [[\"fileUploader\", \"\"], [3, \"ngClass\"], [\"name\", \"fileUploader\", \"mode\", \"advanced\", \"chooseLabel\", \"Upload a File\", \"chooseIcon\", \"pi pi-upload\", 1, \"customFileUploader\", 3, \"onSelect\", \"disabled\", \"multiple\", \"maxFileSize\", \"accept\", \"showUploadButton\", \"showCancelButton\"], [\"pTemplate\", \"toolbar\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"file\"], [1, \"fileUpload-toolbar-text\"], [1, \"fileUpload-content-template\"], [1, \"fileUpload-datatable\", \"pt-1\"], [\"styleClass\", \"p-datatable-striped\", 3, \"value\", \"tableStyle\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"footer\"], [1, \"attachment-preview-panel\", 3, \"header\"], [2, \"width\", \"40%\"], [2, \"width\", \"5%\"], [2, \"width\", \"15%\"], [2, \"width\", \"20%\"], [1, \"flex\"], [\"icon\", \"pi pi-download\", \"pTooltip\", \"Download\", 3, \"onClick\", \"outlined\"], [\"severity\", \"danger\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"text\"], [\"icon\", \"pi pi-eye\", \"pTooltip\", \"Preview\", 3, \"outlined\"], [\"severity\", \"danger\", \"icon\", \"pi pi-trash\", \"pTooltip\", \"Delete\", 3, \"onClick\", \"text\"], [\"icon\", \"pi pi-eye\", \"pTooltip\", \"Preview\", 3, \"onClick\", \"outlined\"], [1, \"template-footer-text\"], [\"pTemplate\", \"icons\"], [1, \"m-0\"], [\"width\", \"100%\", \"height\", \"150px\"], [\"alt\", \"Preview\", 1, \"max-w-full\", \"h-auto\", 3, \"src\"], [1, \"p-panel-header-icon\", \"p-link\", 3, \"click\"], [1, \"pi\", \"pi-times\"]],\n      template: function FileUploadComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"p-fileUpload\", 2, 0);\n          i0.ɵɵlistener(\"onSelect\", function FileUploadComponent_Template_p_fileUpload_onSelect_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelectClick($event));\n          });\n          i0.ɵɵtemplate(3, FileUploadComponent_ng_template_3_Template, 4, 0, \"ng-template\", 3)(4, FileUploadComponent_ng_template_4_Template, 3, 3, \"ng-template\", 4)(5, FileUploadComponent_ng_template_5_Template, 0, 0, \"ng-template\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.fileUploadContainerClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.isPreview || ctx.userPermissionLevel === \"View\")(\"multiple\", ctx.multipleAllowed)(\"maxFileSize\", ctx.maximumFileSize)(\"accept\", ctx.fileToAccept)(\"showUploadButton\", false)(\"showCancelButton\", false);\n        }\n      },\n      dependencies: [NgClass, FileUploadModule, i2.FileUpload, i3.PrimeTemplate, i4.Button, SharedModule, TableModule, i5.Table, ButtonModule, TooltipModule, i6.Tooltip, PanelModule, i7.Panel, SkeletonModule, i8.Skeleton],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "FileUploadDetails", "moment", "HttpEventType", "FileUploadModule", "UserPermissionLevels", "TooltipModule", "ButtonModule", "TableModule", "SharedModule", "Ng<PERSON><PERSON>", "PanelModule", "SkeletonModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "FileUploadComponent_ng_template_4_Conditional_2_ng_template_3_Conditional_12_Template_p_button_onClick_0_listener", "ɵɵrestoreView", "_r5", "file_r3", "ɵɵnextContext", "$implicit", "ctx_r3", "ɵɵresetView", "onRemoveFileClick", "ɵɵproperty", "FileUploadComponent_ng_template_4_Conditional_2_ng_template_3_Conditional_13_Template_p_button_onClick_0_listener", "_r6", "onPreviewFileClick", "FileUploadComponent_ng_template_4_Conditional_2_ng_template_3_Template_p_button_onClick_11_listener", "_r2", "onDownloadFileClick", "ɵɵtemplate", "FileUploadComponent_ng_template_4_Conditional_2_ng_template_3_Conditional_12_Template", "FileUploadComponent_ng_template_4_Conditional_2_ng_template_3_Conditional_13_Template", "ɵɵadvance", "ɵɵtextInterpolate", "name", "fileShareFileType", "uploadedDate", "uploadedByName", "ɵɵconditional", "userPermissionLevel", "imageFileTypes", "includes", "ɵɵtextInterpolate1", "uploadedFiles", "length", "FileUploadComponent_ng_template_4_Conditional_2_Conditional_5_ng_template_1_Template_button_click_0_listener", "_r7", "previewAttachmentInfo", "undefined", "url", "ɵɵsanitizeUrl", "FileUploadComponent_ng_template_4_Conditional_2_Conditional_5_ng_template_1_Template", "FileUploadComponent_ng_template_4_Conditional_2_Conditional_5_Conditional_3_Template", "FileUploadComponent_ng_template_4_Conditional_2_Conditional_5_Conditional_4_Template", "fileName", "fileType", "FileUploadComponent_ng_template_4_Conditional_2_ng_template_2_Template", "FileUploadComponent_ng_template_4_Conditional_2_ng_template_3_Template", "FileUploadComponent_ng_template_4_Conditional_2_ng_template_4_Template", "FileUploadComponent_ng_template_4_Conditional_2_Conditional_5_Template", "tableStyle", "FileUploadComponent_ng_template_4_Conditional_0_Template", "FileUploadComponent_ng_template_4_Conditional_1_Template", "FileUploadComponent_ng_template_4_Conditional_2_Template", "loadingFiles", "FileUploadComponent", "constructor", "messageSvc", "maximumFileSize", "multipleAllowed", "fileToAccept", "fileUploadContainerClass", "projectId", "versionId", "dataStoreName", "recordId", "dateFormat", "aliasIds", "isPreview", "EditAndDelete", "fileUploadEvent", "getFilesUploaded", "width", "ngOnInit", "getFilesFromServer", "ngOnChanges", "changes", "isFirstChange", "onFileSelectClick", "event", "setTimeout", "currentFiles", "processSelectedFiles", "then", "res", "map", "file", "aliasId", "emit", "fileUploader", "clear", "files", "_this", "_asyncToGenerator", "fileUploadDetails", "saveFile", "Promise", "resolve", "fileService", "uploadFile", "subscribe", "next", "fileInfo", "format", "toString", "push", "error", "console", "log", "_this2", "getFilesCall", "_ref", "getFileInfo", "apply", "arguments", "getFiles", "filesInfo", "for<PERSON>ach", "fileUpload", "statusText", "attchmentInfo", "getFileContent", "type", "Response", "targetFileElement", "document", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "href", "URL", "createObjectURL", "download", "target", "click", "<PERSON><PERSON><PERSON><PERSON>", "status", "deleteFile", "fileIndex", "findIndex", "splice", "f", "showError", "ɵɵdirectiveInject", "i1", "NotificationService", "selectors", "viewQuery", "FileUploadComponent_Query", "rf", "ctx", "FileUploadComponent_Template_p_fileUpload_onSelect_1_listener", "$event", "_r1", "FileUploadComponent_ng_template_3_Template", "FileUploadComponent_ng_template_4_Template", "FileUploadComponent_ng_template_5_Template", "i2", "FileUpload", "i3", "PrimeTemplate", "i4", "<PERSON><PERSON>", "i5", "Table", "i6", "<PERSON><PERSON><PERSON>", "i7", "Panel", "i8", "Skeleton", "styles"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\shared\\file-upload\\file-upload.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\shared\\file-upload\\file-upload.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output, ViewChild, OnChanges, SimpleChanges } from '@angular/core';\r\nimport { FileInfo } from '../models/file-info';\r\nimport { FileUploadDetails } from '../../shared/models/file-upload-details';\r\nimport moment from 'moment/moment';\r\nimport { HttpEventType } from '@angular/common/http';\r\nimport { FileUpload, FileUploadModule } from 'primeng/fileupload';\r\nimport { NotificationService } from 'src/app/core/services/notification.service';\r\nimport { FileUploadService } from './services/file-upload.service';\r\nimport { UserPermissionLevels } from 'src/app/core/enums/shared';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { TableModule } from 'primeng/table';\r\nimport { SharedModule } from 'primeng/api';\r\nimport { NgClass, NgTemplateOutlet } from '@angular/common';\r\nimport { PanelModule } from 'primeng/panel';\r\nimport { SkeletonModule } from 'primeng/skeleton';\r\n\r\n@Component({\r\n  selector: 'app-file-upload',\r\n  templateUrl: './file-upload.component.html',\r\n  styleUrls: ['./file-upload.component.scss'],\r\n  standalone: true,\r\n  imports: [NgClass, FileUploadModule, SharedModule, TableModule, ButtonModule, TooltipModule, PanelModule, NgTemplateOutlet, SkeletonModule]\r\n})\r\nexport class FileUploadComponent implements OnInit, OnChanges {\r\n\r\n  @Input() uploadedFiles: FileInfo[] = [];\r\n  @Input() maximumFileSize = 5242880; // Maximum file size for each file\r\n  @Input() multipleAllowed = true; // Defines the number of uploads allowed to upload\r\n  @Input() fileToAccept = '.pdf,.xls,.xlsx,.doc,.csv,.docx,.txt,.ppt,.pptx,.jpeg,.jpg,.gif,.png'; // File Types to Accept\r\n  @Input() fileUploadContainerClass = 'default-fileUpload';  // Customized Default container DIV wrapper-class\r\n  @Input() projectId = '';\r\n  @Input() versionId = '';\r\n  @Input() dataStoreName = '';\r\n  @Input() recordId = '';\r\n  @Input() dateFormat = 'YYYY/MM/DD';\r\n  @Input() aliasIds: string[] = [];\r\n  @Input() fileService: FileUploadService;\r\n  @Input() isPreview = false;\r\n  @Input() userPermissionLevel: UserPermissionLevels = UserPermissionLevels.EditAndDelete;\r\n\r\n  @Output() fileUploadEvent: EventEmitter<any> = new EventEmitter();\r\n  @Output() getFilesUploaded: EventEmitter<FileInfo[]> = new EventEmitter();\r\n\r\n  @ViewChild('fileUploader') fileUploader: FileUpload;\r\n\r\n  loadingFiles = false;\r\n  tableStyle = { 'table-layout': 'auto', width: '100%' };\r\n  previewAttachmentInfo: { url: string, fileType: string, fileName: string };\r\n  imageFileTypes = ['image/jpg', 'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/tiff', 'image/webp', 'image/svg+xml', 'image/x-icon', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp', 'svg+xml', 'x-icon', 'svg', 'ico'];\r\n\r\n  constructor(private messageSvc: NotificationService) { }\r\n\r\n  ngOnInit(): void {\r\n    if (this.aliasIds) {\r\n      this.getFilesFromServer();\r\n    }\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes.aliasIds && !changes.aliasIds?.isFirstChange()) {\r\n      this.getFilesFromServer();\r\n    }\r\n  }\r\n\r\n  onFileSelectClick(event): void {\r\n    setTimeout(() => {\r\n      \r\n    }, 10000);\r\n    const currentFiles: File[] = event.currentFiles;\r\n    this.processSelectedFiles(currentFiles).then(res => {\r\n      this.aliasIds = this.uploadedFiles.map(file => {\r\n        return file.aliasId;\r\n      });\r\n\r\n      this.fileUploadEvent.emit(this.aliasIds);\r\n      this.fileUploader.clear();\r\n    });\r\n  }\r\n\r\n  async processSelectedFiles(files: File[]) {\r\n    for (const file of files) {\r\n      const fileUploadDetails = new FileUploadDetails(this.projectId, this.versionId, this.recordId, this.dataStoreName, file.name, file);\r\n      await this.saveFile(fileUploadDetails);\r\n    }\r\n  }\r\n\r\n  saveFile(fileUploadDetails: FileUploadDetails) {\r\n    return new Promise<void>(resolve => {\r\n      this.fileService.uploadFile(fileUploadDetails)\r\n        .subscribe({\r\n          next: (fileInfo: FileInfo) => {\r\n            fileInfo.uploadedDate = moment(fileInfo.uploadedDate, this.dateFormat, false).format(this.dateFormat).toString();\r\n            this.uploadedFiles.push(fileInfo);\r\n            resolve();\r\n          },\r\n          error: (error) => {\r\n            console.log(error);\r\n            resolve();\r\n          }\r\n        });\r\n    });\r\n  }\r\n\r\n  getFilesFromServer(): void {\r\n    this.loadingFiles = true;\r\n    const fileUploadDetails = new FileUploadDetails(this.projectId, this.versionId, this.recordId, this.dataStoreName);\r\n    fileUploadDetails.aliasIds = this.aliasIds;\r\n\r\n    // start rewriting some things here\r\n    const getFilesCall = async () => {\r\n      if (this.recordId) {\r\n        await this.getFileInfo(fileUploadDetails);\r\n      }\r\n    };\r\n\r\n    getFilesCall().then(res => {\r\n      this.loadingFiles = false;\r\n    });\r\n  }\r\n\r\n  getFileInfo(fileUploadDetails: FileUploadDetails) {\r\n    return new Promise<void>(resolve => {\r\n      this.fileService.getFiles(fileUploadDetails)\r\n        .subscribe({\r\n          next: (filesInfo: FileInfo[]) => {\r\n            filesInfo.forEach(fileUpload => {\r\n              fileUpload.uploadedDate = moment(fileUpload.uploadedDate, this.dateFormat, false).format(this.dateFormat).toString();\r\n            });\r\n\r\n            this.uploadedFiles = filesInfo;\r\n            resolve();\r\n          },\r\n          error: (error) => {\r\n            console.log(`File Request Error: ${error.statusText}...`);\r\n            resolve();\r\n          }\r\n        });\r\n    });\r\n  }\r\n\r\n  onDownloadFileClick(attchmentInfo: FileInfo) {\r\n    const fileUploadDetails = new FileUploadDetails(this.projectId, this.versionId, this.recordId, this.dataStoreName, attchmentInfo.name);\r\n    fileUploadDetails.aliasId = attchmentInfo.aliasId;\r\n\r\n    this.fileService.getFileContent(fileUploadDetails)\r\n      .subscribe({\r\n        next: (event) => {\r\n          if (event.type === HttpEventType.Response) {\r\n            const targetFileElement = document.createElement('a');\r\n            targetFileElement.setAttribute('style', 'display:none;');\r\n            document.body.appendChild(targetFileElement);\r\n            targetFileElement.href = URL.createObjectURL(event.body);\r\n            targetFileElement.download = `${attchmentInfo.name}.${attchmentInfo.fileShareFileType}`;\r\n            targetFileElement.target = '_blank';\r\n            targetFileElement.click();\r\n            document.body.removeChild(targetFileElement);\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.log(`File Download Error: ${error.status}: Unable to complete request...`);\r\n        }\r\n      });\r\n  }\r\n\r\n  onPreviewFileClick(attchmentInfo: FileInfo) {\r\n    const fileUploadDetails = new FileUploadDetails(this.projectId, this.versionId, this.recordId, this.dataStoreName, attchmentInfo.name);\r\n    fileUploadDetails.aliasId = attchmentInfo.aliasId;\r\n    this.previewAttachmentInfo = { url: undefined, fileType: attchmentInfo.fileShareFileType, fileName: attchmentInfo.name };\r\n\r\n    this.fileService.getFileContent(fileUploadDetails)\r\n      .subscribe({\r\n        next: (event) => {\r\n          if (event.type === HttpEventType.Response) {\r\n            const url = URL.createObjectURL(event.body);\r\n            this.previewAttachmentInfo.url = url;\r\n          }\r\n        },\r\n        error: (error) => {\r\n          console.log(`File Download Error: ${error.status}: Unable to complete request...`);\r\n        }\r\n      });\r\n  }\r\n\r\n  onRemoveFileClick(fileInfo: FileInfo) {\r\n    const fileUploadDetails = new FileUploadDetails(this.projectId, this.versionId, this.recordId, this.dataStoreName, fileInfo.name);\r\n    fileUploadDetails.aliasId = fileInfo.aliasId;\r\n\r\n    this.fileService.deleteFile(fileUploadDetails)\r\n      .subscribe({\r\n        next: () => {\r\n          const fileIndex = this.uploadedFiles.findIndex(file => file.aliasId === fileInfo.aliasId);\r\n\r\n          if (fileIndex >= 0) {\r\n            this.uploadedFiles.splice(fileIndex, 1);\r\n            this.fileUploadEvent.emit(this.uploadedFiles.map(f => f.aliasId));\r\n          }\r\n        },\r\n        error: () => {\r\n          this.messageSvc.showError('Error', `There was a problem deleting '${fileInfo.name}' from the server`);\r\n        }\r\n      });\r\n  }\r\n}\r\n", "<div [ngClass]=\"fileUploadContainerClass\">\r\n  <p-fileUpload [disabled]=\"isPreview || userPermissionLevel === 'View'\" name=\"fileUploader\" mode=\"advanced\"\r\n    class=\"customFileUploader\" #fileUploader [multiple]=\"multipleAllowed\" [maxFileSize]=\"maximumFileSize\"\r\n    [accept]=\"fileToAccept\" (onSelect)=\"onFileSelectClick($event)\" chooseLabel=\"Upload a File\" chooseIcon=\"pi pi-upload\"\r\n    [showUploadButton]=\"false\" [showCancelButton]=\"false\">\r\n    <ng-template pTemplate=\"toolbar\">\r\n      <div class=\"fileUpload-toolbar-text\">You can upload files with the button or drag-and-drop.<br />\r\n        Once uploaded, make sure you click the Save button on the grid to attach the files.</div>\r\n    </ng-template>\r\n    <ng-template pTemplate=\"content\">\r\n      @if(!uploadedFiles.length && !loadingFiles){\r\n      <div class=\"fileUpload-content-template\">\r\n        Drag & Drop Files here</div>\r\n      }\r\n      @if(loadingFiles && !uploadedFiles.length){\r\n      <div class=\"fileUpload-content-template\">Loading...</div>\r\n      }\r\n      @if(uploadedFiles.length){\r\n      <div class=\"fileUpload-datatable pt-1\">\r\n        <p-table [value]=\"uploadedFiles\" styleClass=\"p-datatable-striped\" [tableStyle]=\"tableStyle\">\r\n          <ng-template pTemplate=\"header\">\r\n            <tr>\r\n              <th style=\"width: 40%;\">File</th>\r\n              <th style=\"width: 5%;\">Type</th>\r\n              <th style=\"width: 15%;\">Uploaded On</th>\r\n              <th style=\"width: 20%;\">Uploaded By</th>\r\n              <th style=\"width: 20%;\"></th>\r\n            </tr>\r\n          </ng-template>\r\n          <ng-template pTemplate=\"body\" let-file>\r\n            <tr>\r\n              <td>{{file.name}}</td>\r\n              <td>{{file.fileShareFileType}}</td>\r\n              <td>{{file.uploadedDate}}</td>\r\n              <td>{{file.uploadedByName}}</td>\r\n              <td>\r\n                <span class=\"flex\">\r\n                  <p-button icon=\"pi pi-download\" [outlined]=\"true\" (onClick)=\"onDownloadFileClick(file)\"\r\n                    pTooltip=\"Download\"></p-button>\r\n                  @if (userPermissionLevel !== 'View') {\r\n                  <p-button [text]=\"true\" severity=\"danger\" icon=\"pi pi-trash\" (onClick)=\"onRemoveFileClick(file)\"\r\n                    pTooltip=\"Delete\"></p-button>\r\n                  }\r\n                  @if(imageFileTypes.includes(file.fileShareFileType)){\r\n                  <p-button icon=\"pi pi-eye\" [outlined]=\"true\" (onClick)=\"onPreviewFileClick(file)\"\r\n                    pTooltip=\"Preview\"></p-button>\r\n                  }\r\n                </span>\r\n              </td>\r\n            </tr>\r\n          </ng-template>\r\n          <ng-template pTemplate=\"footer\">\r\n            <div class=\"template-footer-text\">Files: {{uploadedFiles.length}}</div>\r\n          </ng-template>\r\n        </p-table>\r\n        @if (previewAttachmentInfo) {\r\n        <p-panel [header]=\"'Preview (' + previewAttachmentInfo.fileName + ')'\" class=\"attachment-preview-panel\">\r\n          <ng-template pTemplate=\"icons\">\r\n            <button class=\"p-panel-header-icon p-link\" (click)=\"previewAttachmentInfo = undefined\">\r\n              <span class=\"pi pi-times\"></span>\r\n            </button>\r\n          </ng-template>\r\n          <p class=\"m-0\">\r\n            @if(!previewAttachmentInfo.url){\r\n            <p-skeleton width=\"100%\" height=\"150px\" />\r\n            }\r\n            @if(previewAttachmentInfo.url && imageFileTypes.includes(previewAttachmentInfo.fileType)){\r\n            <img [src]=\"previewAttachmentInfo.url\" alt=\"Preview\" class=\"max-w-full h-auto\" />\r\n            }\r\n            <!-- @todo: Add support for other file types -->\r\n          </p>\r\n        </p-panel>\r\n        }\r\n      </div>\r\n      }\r\n    </ng-template>\r\n    <ng-template let-file pTemplate='file'></ng-template>\r\n  </p-fileUpload>\r\n</div>"], "mappings": ";AAAA,SAAoBA,YAAY,QAAoE,eAAe;AAEnH,SAASC,iBAAiB,QAAQ,yCAAyC;AAC3E,OAAOC,MAAM,MAAM,eAAe;AAClC,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAAqBC,gBAAgB,QAAQ,oBAAoB;AAGjE,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,aAAa;AAC1C,SAASC,OAAO,QAA0B,iBAAiB;AAC3D,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,kBAAkB;;;;;;;;;;;;;ICT3CC,EAAA,CAAAC,cAAA,aAAqC;IAAAD,EAAA,CAAAE,MAAA,6DAAsD;IAAAF,EAAA,CAAAG,SAAA,SAAM;IAC/FH,EAAA,CAAAE,MAAA,2FAAmF;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;;IAI3FJ,EAAA,CAAAC,cAAA,aAAyC;IACvCD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;;IAG9BJ,EAAA,CAAAC,cAAA,aAAyC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;;IAOjDJ,EADF,CAAAC,cAAA,SAAI,aACsB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACjCJ,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAI,YAAA,EAAK;IAChCJ,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACxCJ,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACxCJ,EAAA,CAAAG,SAAA,aAA6B;IAC/BH,EAAA,CAAAI,YAAA,EAAK;;;;;;IAaCJ,EAAA,CAAAC,cAAA,mBACoB;IADyCD,EAAA,CAAAK,UAAA,qBAAAC,kHAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAT,EAAA,CAAAU,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAWD,MAAA,CAAAE,iBAAA,CAAAL,OAAA,CAAuB;IAAA,EAAC;IAC5ET,EAAA,CAAAI,YAAA,EAAW;;;IADrBJ,EAAA,CAAAe,UAAA,cAAa;;;;;;IAIvBf,EAAA,CAAAC,cAAA,mBACqB;IADwBD,EAAA,CAAAK,UAAA,qBAAAW,kHAAA;MAAAhB,EAAA,CAAAO,aAAA,CAAAU,GAAA;MAAA,MAAAR,OAAA,GAAAT,EAAA,CAAAU,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAWD,MAAA,CAAAM,kBAAA,CAAAT,OAAA,CAAwB;IAAA,EAAC;IAC5DT,EAAA,CAAAI,YAAA,EAAW;;;IADLJ,EAAA,CAAAe,UAAA,kBAAiB;;;;;;IAbhDf,EADF,CAAAC,cAAA,SAAI,SACE;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACtBJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAI,YAAA,EAAK;IACnCJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAI,YAAA,EAAK;IAC9BJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAI,YAAA,EAAK;IAG5BJ,EAFJ,CAAAC,cAAA,SAAI,gBACiB,oBAEK;IAD4BD,EAAA,CAAAK,UAAA,qBAAAc,oGAAA;MAAA,MAAAV,OAAA,GAAAT,EAAA,CAAAO,aAAA,CAAAa,GAAA,EAAAT,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAWD,MAAA,CAAAS,mBAAA,CAAAZ,OAAA,CAAyB;IAAA,EAAC;IACjET,EAAA,CAAAI,YAAA,EAAW;IAKjCJ,EAJA,CAAAsB,UAAA,KAAAC,qFAAA,uBAAsC,KAAAC,qFAAA,uBAIe;IAM3DxB,EAFI,CAAAI,YAAA,EAAO,EACJ,EACF;;;;;IAlBCJ,EAAA,CAAAyB,SAAA,GAAa;IAAbzB,EAAA,CAAA0B,iBAAA,CAAAjB,OAAA,CAAAkB,IAAA,CAAa;IACb3B,EAAA,CAAAyB,SAAA,GAA0B;IAA1BzB,EAAA,CAAA0B,iBAAA,CAAAjB,OAAA,CAAAmB,iBAAA,CAA0B;IAC1B5B,EAAA,CAAAyB,SAAA,GAAqB;IAArBzB,EAAA,CAAA0B,iBAAA,CAAAjB,OAAA,CAAAoB,YAAA,CAAqB;IACrB7B,EAAA,CAAAyB,SAAA,GAAuB;IAAvBzB,EAAA,CAAA0B,iBAAA,CAAAjB,OAAA,CAAAqB,cAAA,CAAuB;IAGS9B,EAAA,CAAAyB,SAAA,GAAiB;IAAjBzB,EAAA,CAAAe,UAAA,kBAAiB;IAEjDf,EAAA,CAAAyB,SAAA,EAGC;IAHDzB,EAAA,CAAA+B,aAAA,KAAAnB,MAAA,CAAAoB,mBAAA,sBAGC;IACDhC,EAAA,CAAAyB,SAAA,EAGC;IAHDzB,EAAA,CAAA+B,aAAA,KAAAnB,MAAA,CAAAqB,cAAA,CAAAC,QAAA,CAAAzB,OAAA,CAAAmB,iBAAA,YAGC;;;;;IAMP5B,EAAA,CAAAC,cAAA,cAAkC;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;IAArCJ,EAAA,CAAAyB,SAAA,EAA+B;IAA/BzB,EAAA,CAAAmC,kBAAA,YAAAvB,MAAA,CAAAwB,aAAA,CAAAC,MAAA,KAA+B;;;;;;IAMjErC,EAAA,CAAAC,cAAA,iBAAuF;IAA5CD,EAAA,CAAAK,UAAA,mBAAAiC,6GAAA;MAAAtC,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA3B,MAAA,GAAAZ,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAAD,MAAA,CAAA4B,qBAAA,GAAiCC,SAAS;IAAA,EAAC;IACpFzC,EAAA,CAAAG,SAAA,eAAiC;IACnCH,EAAA,CAAAI,YAAA,EAAS;;;;;IAITJ,EAAA,CAAAG,SAAA,qBAA0C;;;;;IAG1CH,EAAA,CAAAG,SAAA,cAAiF;;;;IAA5EH,EAAA,CAAAe,UAAA,QAAAH,MAAA,CAAA4B,qBAAA,CAAAE,GAAA,EAAA1C,EAAA,CAAA2C,aAAA,CAAiC;;;;;IAX1C3C,EAAA,CAAAC,cAAA,kBAAwG;IACtGD,EAAA,CAAAsB,UAAA,IAAAsB,oFAAA,0BAA+B;IAK/B5C,EAAA,CAAAC,cAAA,YAAe;IAIbD,EAHA,CAAAsB,UAAA,IAAAuB,oFAAA,yBAAgC,IAAAC,oFAAA,kBAG0D;IAK9F9C,EADE,CAAAI,YAAA,EAAI,EACI;;;;IAfDJ,EAAA,CAAAe,UAAA,yBAAAH,MAAA,CAAA4B,qBAAA,CAAAO,QAAA,OAA6D;IAOlE/C,EAAA,CAAAyB,SAAA,GAEC;IAFDzB,EAAA,CAAA+B,aAAA,KAAAnB,MAAA,CAAA4B,qBAAA,CAAAE,GAAA,UAEC;IACD1C,EAAA,CAAAyB,SAAA,EAEC;IAFDzB,EAAA,CAAA+B,aAAA,IAAAnB,MAAA,CAAA4B,qBAAA,CAAAE,GAAA,IAAA9B,MAAA,CAAAqB,cAAA,CAAAC,QAAA,CAAAtB,MAAA,CAAA4B,qBAAA,CAAAQ,QAAA,WAEC;;;;;IAjDLhD,EADF,CAAAC,cAAA,aAAuC,iBACuD;IAgC1FD,EA/BA,CAAAsB,UAAA,IAAA2B,sEAAA,2BAAgC,IAAAC,sEAAA,2BASO,IAAAC,sEAAA,0BAsBP;IAGlCnD,EAAA,CAAAI,YAAA,EAAU;IACVJ,EAAA,CAAAsB,UAAA,IAAA8B,sEAAA,sBAA6B;IAkB/BpD,EAAA,CAAAI,YAAA,EAAM;;;;IAtDKJ,EAAA,CAAAyB,SAAA,EAAuB;IAAkCzB,EAAzD,CAAAe,UAAA,UAAAH,MAAA,CAAAwB,aAAA,CAAuB,eAAAxB,MAAA,CAAAyC,UAAA,CAA2D;IAoC3FrD,EAAA,CAAAyB,SAAA,GAiBC;IAjBDzB,EAAA,CAAA+B,aAAA,IAAAnB,MAAA,CAAA4B,qBAAA,UAiBC;;;;;IAvDHxC,EAPA,CAAAsB,UAAA,IAAAgC,wDAAA,iBAA4C,IAAAC,wDAAA,iBAID,IAAAC,wDAAA,iBAGjB;;;;IAP1BxD,EAAA,CAAA+B,aAAA,KAAAnB,MAAA,CAAAwB,aAAA,CAAAC,MAAA,KAAAzB,MAAA,CAAA6C,YAAA,UAGC;IACDzD,EAAA,CAAAyB,SAAA,EAEC;IAFDzB,EAAA,CAAA+B,aAAA,IAAAnB,MAAA,CAAA6C,YAAA,KAAA7C,MAAA,CAAAwB,aAAA,CAAAC,MAAA,UAEC;IACDrC,EAAA,CAAAyB,SAAA,EAyDC;IAzDDzB,EAAA,CAAA+B,aAAA,IAAAnB,MAAA,CAAAwB,aAAA,CAAAC,MAAA,UAyDC;;;;ADlDP,OAAM,MAAOqB,mBAAmB;EA2B9BC,YAAoBC,UAA+B;IAA/B,KAAAA,UAAU,GAAVA,UAAU;IAzBrB,KAAAxB,aAAa,GAAe,EAAE;IAC9B,KAAAyB,eAAe,GAAG,OAAO,CAAC,CAAC;IAC3B,KAAAC,eAAe,GAAG,IAAI,CAAC,CAAC;IACxB,KAAAC,YAAY,GAAG,sEAAsE,CAAC,CAAC;IACvF,KAAAC,wBAAwB,GAAG,oBAAoB,CAAC,CAAE;IAClD,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,UAAU,GAAG,YAAY;IACzB,KAAAC,QAAQ,GAAa,EAAE;IAEvB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAvC,mBAAmB,GAAyBxC,oBAAoB,CAACgF,aAAa;IAE7E,KAAAC,eAAe,GAAsB,IAAItF,YAAY,EAAE;IACvD,KAAAuF,gBAAgB,GAA6B,IAAIvF,YAAY,EAAE;IAIzE,KAAAsE,YAAY,GAAG,KAAK;IACpB,KAAAJ,UAAU,GAAG;MAAE,cAAc,EAAE,MAAM;MAAEsB,KAAK,EAAE;IAAM,CAAE;IAEtD,KAAA1C,cAAc,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,eAAe,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC;EAEhL;EAEvD2C,QAAQA,CAAA;IACN,IAAI,IAAI,CAACN,QAAQ,EAAE;MACjB,IAAI,CAACO,kBAAkB,EAAE;IAC3B;EACF;EAEAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAACT,QAAQ,IAAI,CAACS,OAAO,CAACT,QAAQ,EAAEU,aAAa,EAAE,EAAE;MAC1D,IAAI,CAACH,kBAAkB,EAAE;IAC3B;EACF;EAEAI,iBAAiBA,CAACC,KAAK;IACrBC,UAAU,CAAC,MAAK,CAEhB,CAAC,EAAE,KAAK,CAAC;IACT,MAAMC,YAAY,GAAWF,KAAK,CAACE,YAAY;IAC/C,IAAI,CAACC,oBAAoB,CAACD,YAAY,CAAC,CAACE,IAAI,CAACC,GAAG,IAAG;MACjD,IAAI,CAACjB,QAAQ,GAAG,IAAI,CAAClC,aAAa,CAACoD,GAAG,CAACC,IAAI,IAAG;QAC5C,OAAOA,IAAI,CAACC,OAAO;MACrB,CAAC,CAAC;MAEF,IAAI,CAACjB,eAAe,CAACkB,IAAI,CAAC,IAAI,CAACrB,QAAQ,CAAC;MACxC,IAAI,CAACsB,YAAY,CAACC,KAAK,EAAE;IAC3B,CAAC,CAAC;EACJ;EAEMR,oBAAoBA,CAACS,KAAa;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtC,KAAK,MAAMP,IAAI,IAAIK,KAAK,EAAE;QACxB,MAAMG,iBAAiB,GAAG,IAAI7G,iBAAiB,CAAC2G,KAAI,CAAC9B,SAAS,EAAE8B,KAAI,CAAC7B,SAAS,EAAE6B,KAAI,CAAC3B,QAAQ,EAAE2B,KAAI,CAAC5B,aAAa,EAAEsB,IAAI,CAAC9D,IAAI,EAAE8D,IAAI,CAAC;QACnI,MAAMM,KAAI,CAACG,QAAQ,CAACD,iBAAiB,CAAC;MACxC;IAAC;EACH;EAEAC,QAAQA,CAACD,iBAAoC;IAC3C,OAAO,IAAIE,OAAO,CAAOC,OAAO,IAAG;MACjC,IAAI,CAACC,WAAW,CAACC,UAAU,CAACL,iBAAiB,CAAC,CAC3CM,SAAS,CAAC;QACTC,IAAI,EAAGC,QAAkB,IAAI;UAC3BA,QAAQ,CAAC5E,YAAY,GAAGxC,MAAM,CAACoH,QAAQ,CAAC5E,YAAY,EAAE,IAAI,CAACwC,UAAU,EAAE,KAAK,CAAC,CAACqC,MAAM,CAAC,IAAI,CAACrC,UAAU,CAAC,CAACsC,QAAQ,EAAE;UAChH,IAAI,CAACvE,aAAa,CAACwE,IAAI,CAACH,QAAQ,CAAC;UACjCL,OAAO,EAAE;QACX,CAAC;QACDS,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;UAClBT,OAAO,EAAE;QACX;OACD,CAAC;IACN,CAAC,CAAC;EACJ;EAEAvB,kBAAkBA,CAAA;IAAA,IAAAmC,MAAA;IAChB,IAAI,CAACvD,YAAY,GAAG,IAAI;IACxB,MAAMwC,iBAAiB,GAAG,IAAI7G,iBAAiB,CAAC,IAAI,CAAC6E,SAAS,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACD,aAAa,CAAC;IAClH8B,iBAAiB,CAAC3B,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAE1C;IACA,MAAM2C,YAAY;MAAA,IAAAC,IAAA,GAAAlB,iBAAA,CAAG,aAAW;QAC9B,IAAIgB,MAAI,CAAC5C,QAAQ,EAAE;UACjB,MAAM4C,MAAI,CAACG,WAAW,CAAClB,iBAAiB,CAAC;QAC3C;MACF,CAAC;MAAA,gBAJKgB,YAAYA,CAAA;QAAA,OAAAC,IAAA,CAAAE,KAAA,OAAAC,SAAA;MAAA;IAAA,GAIjB;IAEDJ,YAAY,EAAE,CAAC3B,IAAI,CAACC,GAAG,IAAG;MACxB,IAAI,CAAC9B,YAAY,GAAG,KAAK;IAC3B,CAAC,CAAC;EACJ;EAEA0D,WAAWA,CAAClB,iBAAoC;IAC9C,OAAO,IAAIE,OAAO,CAAOC,OAAO,IAAG;MACjC,IAAI,CAACC,WAAW,CAACiB,QAAQ,CAACrB,iBAAiB,CAAC,CACzCM,SAAS,CAAC;QACTC,IAAI,EAAGe,SAAqB,IAAI;UAC9BA,SAAS,CAACC,OAAO,CAACC,UAAU,IAAG;YAC7BA,UAAU,CAAC5F,YAAY,GAAGxC,MAAM,CAACoI,UAAU,CAAC5F,YAAY,EAAE,IAAI,CAACwC,UAAU,EAAE,KAAK,CAAC,CAACqC,MAAM,CAAC,IAAI,CAACrC,UAAU,CAAC,CAACsC,QAAQ,EAAE;UACtH,CAAC,CAAC;UAEF,IAAI,CAACvE,aAAa,GAAGmF,SAAS;UAC9BnB,OAAO,EAAE;QACX,CAAC;QACDS,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACC,GAAG,CAAC,uBAAuBF,KAAK,CAACa,UAAU,KAAK,CAAC;UACzDtB,OAAO,EAAE;QACX;OACD,CAAC;IACN,CAAC,CAAC;EACJ;EAEA/E,mBAAmBA,CAACsG,aAAuB;IACzC,MAAM1B,iBAAiB,GAAG,IAAI7G,iBAAiB,CAAC,IAAI,CAAC6E,SAAS,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACD,aAAa,EAAEwD,aAAa,CAAChG,IAAI,CAAC;IACtIsE,iBAAiB,CAACP,OAAO,GAAGiC,aAAa,CAACjC,OAAO;IAEjD,IAAI,CAACW,WAAW,CAACuB,cAAc,CAAC3B,iBAAiB,CAAC,CAC/CM,SAAS,CAAC;MACTC,IAAI,EAAGtB,KAAK,IAAI;QACd,IAAIA,KAAK,CAAC2C,IAAI,KAAKvI,aAAa,CAACwI,QAAQ,EAAE;UACzC,MAAMC,iBAAiB,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACrDF,iBAAiB,CAACG,YAAY,CAAC,OAAO,EAAE,eAAe,CAAC;UACxDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACL,iBAAiB,CAAC;UAC5CA,iBAAiB,CAACM,IAAI,GAAGC,GAAG,CAACC,eAAe,CAACrD,KAAK,CAACiD,IAAI,CAAC;UACxDJ,iBAAiB,CAACS,QAAQ,GAAG,GAAGb,aAAa,CAAChG,IAAI,IAAIgG,aAAa,CAAC/F,iBAAiB,EAAE;UACvFmG,iBAAiB,CAACU,MAAM,GAAG,QAAQ;UACnCV,iBAAiB,CAACW,KAAK,EAAE;UACzBV,QAAQ,CAACG,IAAI,CAACQ,WAAW,CAACZ,iBAAiB,CAAC;QAC9C;MACF,CAAC;MACDlB,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACC,GAAG,CAAC,wBAAwBF,KAAK,CAAC+B,MAAM,iCAAiC,CAAC;MACpF;KACD,CAAC;EACN;EAEA1H,kBAAkBA,CAACyG,aAAuB;IACxC,MAAM1B,iBAAiB,GAAG,IAAI7G,iBAAiB,CAAC,IAAI,CAAC6E,SAAS,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACD,aAAa,EAAEwD,aAAa,CAAChG,IAAI,CAAC;IACtIsE,iBAAiB,CAACP,OAAO,GAAGiC,aAAa,CAACjC,OAAO;IACjD,IAAI,CAAClD,qBAAqB,GAAG;MAAEE,GAAG,EAAED,SAAS;MAAEO,QAAQ,EAAE2E,aAAa,CAAC/F,iBAAiB;MAAEmB,QAAQ,EAAE4E,aAAa,CAAChG;IAAI,CAAE;IAExH,IAAI,CAAC0E,WAAW,CAACuB,cAAc,CAAC3B,iBAAiB,CAAC,CAC/CM,SAAS,CAAC;MACTC,IAAI,EAAGtB,KAAK,IAAI;QACd,IAAIA,KAAK,CAAC2C,IAAI,KAAKvI,aAAa,CAACwI,QAAQ,EAAE;UACzC,MAAMpF,GAAG,GAAG4F,GAAG,CAACC,eAAe,CAACrD,KAAK,CAACiD,IAAI,CAAC;UAC3C,IAAI,CAAC3F,qBAAqB,CAACE,GAAG,GAAGA,GAAG;QACtC;MACF,CAAC;MACDmE,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACC,GAAG,CAAC,wBAAwBF,KAAK,CAAC+B,MAAM,iCAAiC,CAAC;MACpF;KACD,CAAC;EACN;EAEA9H,iBAAiBA,CAAC2F,QAAkB;IAClC,MAAMR,iBAAiB,GAAG,IAAI7G,iBAAiB,CAAC,IAAI,CAAC6E,SAAS,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACD,aAAa,EAAEsC,QAAQ,CAAC9E,IAAI,CAAC;IACjIsE,iBAAiB,CAACP,OAAO,GAAGe,QAAQ,CAACf,OAAO;IAE5C,IAAI,CAACW,WAAW,CAACwC,UAAU,CAAC5C,iBAAiB,CAAC,CAC3CM,SAAS,CAAC;MACTC,IAAI,EAAEA,CAAA,KAAK;QACT,MAAMsC,SAAS,GAAG,IAAI,CAAC1G,aAAa,CAAC2G,SAAS,CAACtD,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKe,QAAQ,CAACf,OAAO,CAAC;QAEzF,IAAIoD,SAAS,IAAI,CAAC,EAAE;UAClB,IAAI,CAAC1G,aAAa,CAAC4G,MAAM,CAACF,SAAS,EAAE,CAAC,CAAC;UACvC,IAAI,CAACrE,eAAe,CAACkB,IAAI,CAAC,IAAI,CAACvD,aAAa,CAACoD,GAAG,CAACyD,CAAC,IAAIA,CAAC,CAACvD,OAAO,CAAC,CAAC;QACnE;MACF,CAAC;MACDmB,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACjD,UAAU,CAACsF,SAAS,CAAC,OAAO,EAAE,iCAAiCzC,QAAQ,CAAC9E,IAAI,mBAAmB,CAAC;MACvG;KACD,CAAC;EACN;;;uBAlLW+B,mBAAmB,EAAA1D,EAAA,CAAAmJ,iBAAA,CAAAC,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAnB3F,mBAAmB;MAAA4F,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCvB9BzJ,EADF,CAAAC,cAAA,aAA0C,yBAIgB;UAD9BD,EAAA,CAAAK,UAAA,sBAAAsJ,8DAAAC,MAAA;YAAA5J,EAAA,CAAAO,aAAA,CAAAsJ,GAAA;YAAA,OAAA7J,EAAA,CAAAa,WAAA,CAAY6I,GAAA,CAAAzE,iBAAA,CAAA2E,MAAA,CAAyB;UAAA,EAAC;UAyE9D5J,EAvEA,CAAAsB,UAAA,IAAAwI,0CAAA,yBAAiC,IAAAC,0CAAA,yBAIA,IAAAC,0CAAA,yBAmEM;UAE3ChK,EADE,CAAAI,YAAA,EAAe,EACX;;;UA9EDJ,EAAA,CAAAe,UAAA,YAAA2I,GAAA,CAAA1F,wBAAA,CAAoC;UACzBhE,EAAA,CAAAyB,SAAA,EAAwD;UAGzCzB,EAHf,CAAAe,UAAA,aAAA2I,GAAA,CAAAnF,SAAA,IAAAmF,GAAA,CAAA1H,mBAAA,YAAwD,aAAA0H,GAAA,CAAA5F,eAAA,CACC,gBAAA4F,GAAA,CAAA7F,eAAA,CAAgC,WAAA6F,GAAA,CAAA3F,YAAA,CAC9E,2BACG,2BAA2B;;;qBDkB7ClE,OAAO,EAAEN,gBAAgB,EAAA0K,EAAA,CAAAC,UAAA,EAAAC,EAAA,CAAAC,aAAA,EAAAC,EAAA,CAAAC,MAAA,EAAE1K,YAAY,EAAED,WAAW,EAAA4K,EAAA,CAAAC,KAAA,EAAE9K,YAAY,EAAED,aAAa,EAAAgL,EAAA,CAAAC,OAAA,EAAE5K,WAAW,EAAA6K,EAAA,CAAAC,KAAA,EAAoB7K,cAAc,EAAA8K,EAAA,CAAAC,QAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}