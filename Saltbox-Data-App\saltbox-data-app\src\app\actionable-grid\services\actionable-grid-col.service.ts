import { Injectable } from '@angular/core';
import { ColDef, GridOptions } from 'ag-grid-community';
import { ActionableGridColumnConfig } from '../../core/models/actionable-grid-column-config';
import { sortingDateComparator } from 'src/app/shared/utilities/compare.functions';
import { dateValueFormatter, formatCurrency, numericValueFormatter } from 'src/app/shared/utilities/format.functions';
import { AgGridCheckBoxRendererComponent } from '../../core/ag-grid/renderers/ag-grid-checkbox-renderer.component';
import { AgGridDropDownRendererComponent } from '../../core/ag-grid/renderers/ag-grid-dropdown-renderer.component';
import { ActionableGridColumnFilters } from '../../core/enums/actionable-grid-column-filters';
import { AgGridEditableRendererComponent } from '../../core/ag-grid/renderers/ag-grid-editable-renderer.component';
import { AgGridMessageSymbolRendererComponent } from '../../core/ag-grid/renderers/ag-grid-symbol-renderer';
import { AgGridRowActionsRendererComponent } from '../../core/ag-grid/renderers/ag-grid-row-actions-renderer.component';
import { ExtendedJsonSchema } from 'src/app/shared/models/extended-json-schema';
import { JsonDataTypes, JsonStringFormats } from 'src/app/shared/enums/json-schema.enum';
import { AgGridHeaderCheckBoxRendererComponent } from '../../core/ag-grid/renderers/headers/ag-grid-header-checkbox-renderer.component';
import { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';
import { AgGridHyperlinkRendererComponent } from '../../core/ag-grid/renderers/ag-grid-hyperlink-renderer.component';
import { ProjectsService } from '../../core/services/projects.service';
import { AgGridMasterDetailRendererComponent } from '../../core/ag-grid/renderers/ag-grid-master-detail-renderer.component';
import { ActionableGridMasterDetailConfigOptions } from '../../core/models/actionable-grid-master-detail-config-options';
import { ColDefOptions } from 'src/app/actionable-grid/model/col-def-options';
import { UserPermissionLevels } from 'src/app/core/enums/shared';
import { FormlyFieldConfig } from '@ngx-formly/core';
import { FormlyRendererTypes } from 'src/app/sb-formly-renderer/enums/formly-renderer-types.enum';
import { ActionableGridColumnFormat } from 'src/app/core/models/actionable-grid-column-format';
import { ICustomCellRendererParams } from 'src/app/core/ag-grid/renderers/ag-grid-custom-cell-renderer-params';
import { AgGridButtonRendererComponent } from 'src/app/core/ag-grid/renderers/ag-grid-button-renderer.component';
import { OptionValue } from 'src/app/shared/models/option-value';
import { IRowActionsParamsContext } from 'src/app/core/models/row-actions-params-context';
import { AgGridHeaderFormulaRendererComponent } from 'src/app/core/ag-grid/renderers/headers/ag-grid-header-formula-renderer.component';
import { ActionableGridService } from './actionable-grid.service';
import { AgGridDateFilterComponent } from 'src/app/core/ag-grid/filter/ag-grid-date-filter.component';

@Injectable({
  providedIn: 'root'
})

export class ActionableGridColService {

  private actionableGridColumnTypeColDef = {
    [EditorColumnType.String]: (options: ColDefOptions) => this.setStringColDef(options),
    [EditorColumnType.Date]: (options: ColDefOptions) => this.setDateColDef(options),
    [EditorColumnType.DateTime]: (options: ColDefOptions) => this.setDateColDef(options),
    [EditorColumnType.Numeric]: (options: ColDefOptions) => this.setNumericColDef(options),
    [EditorColumnType.Currency]: (options: ColDefOptions) => this.setNumericColDef(options),
    [EditorColumnType.Symbol]: (options: ColDefOptions) => this.setSymbolColDef(options),
    [EditorColumnType.Checkbox]: (options: ColDefOptions) => this.setCheckboxColDef(options),
    [EditorColumnType.Collection]: (options: ColDefOptions) => this.setArrayColDef(options),
    [EditorColumnType.Link]: (options: ColDefOptions) => this.setLinkColDef(options),
    [EditorColumnType.Object]: (options: ColDefOptions) => this.setButtonColDef(options)
  };

  constructor(
    private projectsService: ProjectsService, private actionableGridService: ActionableGridService) {
  }

  async getColumnDefs(
    columnConfigList: ActionableGridColumnConfig[],
    baseSchema: ExtendedJsonSchema,
    gridOptions: GridOptions,
    showFloatingFilter: boolean,
    projectVersionId: number,
    allowAddNewRow: boolean,
    parentColumn: ActionableGridColumnConfig = null,
    userPermissionLevel?: UserPermissionLevels,
    bypassObjects = true): Promise<ColDef[]> {

    if (!columnConfigList) {
      return null;
    }

    let projectVariables;
    if (columnConfigList?.some(columnConfig => columnConfig.format.type === EditorColumnType.Link)) {
      projectVariables = await this.projectsService.getProjectVariablesAsync(projectVersionId);
    }

    const columnDefs: ColDef[] = [{ field: '_id', hide: true, suppressColumnsToolPanel: true }]; // adding the hidden primary key to the collection

    // check if there is any array in the collection, set the grid as master detail
    const hasArrays = columnConfigList.some(x => x.format.type === EditorColumnType.Collection);

    if (hasArrays) {
      columnDefs.push(this.getMasterDetailColumn());
    }

    for (const col of columnConfigList) {
      // Bypass objects if the bypassObjects flag is set
      if (bypassObjects && col.format.type === EditorColumnType.Object)
        continue;

      const baseProperty = baseSchema?.properties[col.column];
      col.format.baseType = baseProperty?.type;
      col.format.baseFormat = baseProperty?.format;

      if (userPermissionLevel === UserPermissionLevels.View || baseProperty.presentationProperties?.enableFormula) {
        col.allowUserUpdate = false;
      }

      const isArray = baseProperty.type === JsonDataTypes.Collection;
      const isObject = baseProperty.type === JsonDataTypes.Object;
      const colDef: ColDef = {
        field: col.column,
        colId: col.column,
        headerName: col.displayName || col.column,
        rowGroup: col.groupByHeader && !isArray && !isObject,
        enableRowGroup: true && !isArray && !isObject,
        filter: !isArray && !isObject,
        floatingFilter: showFloatingFilter,
        sortable: !isArray && !isObject,
        minWidth: 50,
        hide: isArray,
        suppressColumnsToolPanel: isArray || isObject,
      };

      if (baseProperty.presentationProperties?.enableFormula)
        colDef.headerComponent = AgGridHeaderFormulaRendererComponent;

      // override allow user update if the parent collection is not editable and this column is not a collection
      if (parentColumn?.allowUserUpdate === false && !isArray)
        col.allowUserUpdate = false;

      const colDefOptions = new ColDefOptions({ columnConfig: col, colDef, baseProperty, gridOptions, projectVariables, projectVersionId, allowAddNewRow, parentColumn, baseSchema, bypassObjects, showFloatingFilter });
      this.actionableGridColumnTypeColDef[col.format.type](colDefOptions);

      columnDefs.push(colDef);
      if (col.format.type === EditorColumnType.Date || col.format.type === EditorColumnType.DateTime) {
        columnDefs.push(...this.getExtendedDateColumns(colDefOptions));
      }
    }

    if (parentColumn == null || parentColumn.allowUserUpdate)
      columnDefs.push(this.getRowActionsTools());  // Returned to last column position as per Pat 22.11.24

    return columnDefs;
  }

  async getColumnDefsByFormlyFieldsConfig(
    fieldsConfig: FormlyFieldConfig[],
    baseSchema: ExtendedJsonSchema,
    showFloatingFilter: boolean,
    projectVersionId: number,
    userPermissionLevel?: UserPermissionLevels): Promise<ColDef[]> {

    if (!fieldsConfig)
      return null;

    // adding the hidden primary key to the collection
    const columnDefs: ColDef[] = [{ field: '_id', hide: true, suppressColumnsToolPanel: true }];

    fieldsConfig.forEach(field => {
      const baseProperty = baseSchema?.properties[field.key as string];
      const agConfigCol = new ActionableGridColumnConfig(field.key as string)
      agConfigCol.format = this.getAGColFormatByFormlyField(baseProperty, field);
      agConfigCol.displayName = field.props.label;
      agConfigCol.enableLookup = field.props.enableLookup;

      //dropdowns
      agConfigCol.dropdown = (field.props?.options as any[])?.length > 0;
      if (agConfigCol.dropdown)
        agConfigCol.values = (field.props?.options as any[]).map(x => { return new OptionValue(x, x); });

      agConfigCol.allowUserUpdate = field.props?.readonly !== true && field.props?.disabled !== true && !baseProperty.presentationProperties?.enableFormula;
      agConfigCol.required = field.props?.required;

      if (userPermissionLevel === UserPermissionLevels.View) {
        agConfigCol.allowUserUpdate = false;
      }

      const colDef: ColDef = {
        field: agConfigCol.column,
        headerName: agConfigCol.displayName || agConfigCol.column,
        rowGroup: agConfigCol.groupByHeader,
        enableRowGroup: true,
        filter: true,
        floatingFilter: showFloatingFilter,
        sortable: true,
        minWidth: 100,
        hide: agConfigCol.groupByHeader
      };

      const colDefOptions = new ColDefOptions({ columnConfig: agConfigCol, colDef, baseProperty, projectVersionId, baseSchema });
      if ([EditorColumnType.Object, EditorColumnType.Collection].includes(agConfigCol.format.type))
        this.setButtonColDef(colDefOptions);
      else
        this.actionableGridColumnTypeColDef[agConfigCol.format.type](colDefOptions);

      if (colDef.cellRendererParams as ICustomCellRendererParams)
        (colDef.cellRendererParams as ICustomCellRendererParams).trackChange = false;

      columnDefs.push(colDef);
    });

    if (userPermissionLevel != UserPermissionLevels.View)
      columnDefs.push(this.getRowActionsTools());

    return columnDefs;
  }

  getFormlyFieldsConfigByGridConfig(config: ActionableGridColumnConfig[]): FormlyFieldConfig[] {
    if (!config)
      return [];

    const fields: FormlyFieldConfig[] = [];

    for (const fieldConfig of config) {
      const field: FormlyFieldConfig = this.mapToFormlyField(fieldConfig);
      fields.push(field);
    }

    return fields;
  }

  mapBaseSchemaToActionableGridConfig(baseSchema: ExtendedJsonSchema): ActionableGridColumnConfig[] {
    const formatSettings: ActionableGridColumnConfig[] = [];

    Object.keys(baseSchema.properties).forEach(property => {
      const colConfig = new ActionableGridColumnConfig(property.toString());
      const presentationProperties = baseSchema.properties[property].presentationProperties;
      const typeProperties = baseSchema.properties[property].typeProperties;

      colConfig.format.baseType = baseSchema.properties[property].type;
      colConfig.format.baseFormat = baseSchema.properties[property].format;
      colConfig.displayName = presentationProperties?.displayName;
      colConfig.enableLookup = presentationProperties?.enableLookup;
      colConfig.required = typeProperties?.required;

      // setting default formats
      switch (colConfig.format.baseType) {
        case JsonDataTypes.String:
          if (colConfig.format.baseFormat === JsonStringFormats.DateTime) {
            colConfig.format.type = presentationProperties.displayTime ? EditorColumnType.DateTime : EditorColumnType.Date;
          } else {
            colConfig.format.type = EditorColumnType.String;
          }
          break;
        case JsonDataTypes.Boolean:
          colConfig.format.type = EditorColumnType.Checkbox;
          break;
        case JsonDataTypes.Decimal:
        case JsonDataTypes.Integer:
          colConfig.format.decimalPlaces = colConfig.format.baseType === JsonDataTypes.Integer ? 0 : presentationProperties?.decimalPlaces;
          colConfig.format.type = typeProperties?.currency ? EditorColumnType.Currency : EditorColumnType.Numeric;
          if (typeProperties?.currency) {
            colConfig.format.currency = typeProperties.currency;
          }
          break;
        case JsonDataTypes.Collection:
          colConfig.format.type = EditorColumnType.Collection;
          colConfig.children = this.mapBaseSchemaToActionableGridConfig(baseSchema.properties[property].items);
          break;
        case JsonDataTypes.Object:
          colConfig.format.type = EditorColumnType.Object;
          break;
        default:
          colConfig.format.type = EditorColumnType.String;
          break;
      }

      formatSettings.push(colConfig);
    });

    return formatSettings;
  }

  private mapToFormlyField(fieldConfig: ActionableGridColumnConfig): FormlyFieldConfig {
    const type = this.getFormlyRendererType(fieldConfig);
    const props: any = {
      label: fieldConfig.displayName || fieldConfig.column,
      required: fieldConfig.required,
      enableLookup: fieldConfig.enableLookup,
    };

    // setting properties based on type
    switch (fieldConfig.format.type) {
      case EditorColumnType.DateTime:
        props.displayTime = true;
        break;
      case EditorColumnType.Currency:
        props.currency = fieldConfig.format?.currency;
        props.decimalPlaces = fieldConfig.format?.decimalPlaces;
        break;
      case EditorColumnType.Numeric:
        props.decimalPlaces = fieldConfig.format?.decimalPlaces;
        break;
    }

    if (fieldConfig.dropdown)
      props.options = fieldConfig.values.map(validValue => ({ label: validValue.key, value: validValue.value }));

    if (fieldConfig.defaultValue)
      props.defaultValue = { isUserProfileValue: fieldConfig.defaultValue.isUserProfileValue, value: fieldConfig.defaultValue.value };

    props.readonly = !fieldConfig.allowUserUpdate;


    const field: FormlyFieldConfig = { key: fieldConfig.column, type, props };

    if (fieldConfig.format.type === EditorColumnType.Collection)
      field.fieldArray = { fieldGroup: this.getFormlyFieldsConfigByGridConfig(fieldConfig.children) };

    if (fieldConfig.format.type === EditorColumnType.Object)
      field.fieldGroup = this.getFormlyFieldsConfigByGridConfig(fieldConfig.children);

    return field;
  }

  private getFormlyRendererType(fieldConfig: ActionableGridColumnConfig): FormlyRendererTypes {
    switch (fieldConfig.format.type) {
      case EditorColumnType.String:
        return fieldConfig.dropdown ? FormlyRendererTypes.Dropdown : FormlyRendererTypes.InputText;
      case EditorColumnType.Link:
      case EditorColumnType.Symbol:
        return FormlyRendererTypes.InputText;
      case EditorColumnType.Date:
      case EditorColumnType.DateTime:
        return FormlyRendererTypes.Calendar;
      case EditorColumnType.Numeric:
      case EditorColumnType.Currency:
        return FormlyRendererTypes.InputNumber;
      case EditorColumnType.Checkbox:
        return FormlyRendererTypes.Checkbox;
      case EditorColumnType.Collection:
        return FormlyRendererTypes.CollectionGrid;
      case EditorColumnType.Object:
        return FormlyRendererTypes.Object;
      default:
        return FormlyRendererTypes.None; // Default type
    }
  }

  private getAGColFormatByFormlyField(baseProperty: ExtendedJsonSchema, field: FormlyFieldConfig): ActionableGridColumnFormat {
    const colFormat = new ActionableGridColumnFormat();
    colFormat.baseType = baseProperty?.type;
    colFormat.baseFormat = baseProperty?.format;

    switch (field.type) {
      case FormlyRendererTypes.InputText:
      case FormlyRendererTypes.Dropdown:
      case FormlyRendererTypes.InputMask:
      case FormlyRendererTypes.InputTextarea:
        colFormat.type = EditorColumnType.String;
        break;
      case FormlyRendererTypes.Calendar:
        colFormat.type = field.props?.displayTime ? EditorColumnType.DateTime : EditorColumnType.Date;
        break;
      case FormlyRendererTypes.Checkbox:
        colFormat.type = EditorColumnType.Checkbox
        break;

      case FormlyRendererTypes.InputNumber:
        colFormat.type = field.props?.currency ? EditorColumnType.Currency : EditorColumnType.Numeric;
        colFormat.currency = field.props?.currency;
        colFormat.decimalPlaces = field.props?.decimalPlaces;
        break;
      case FormlyRendererTypes.Object:
        colFormat.type = EditorColumnType.Object;
        break;
      case FormlyRendererTypes.CollectionGrid:
      case FormlyRendererTypes.CollectionForm:
      case FormlyRendererTypes.CollectionRepeat:
        colFormat.type = EditorColumnType.Collection;
        break;
    }
    return colFormat;
  }

  // Returns the tools for edit, delete and file-upload
  private getRowActionsTools(): ColDef {
    const colDef: ColDef = {
      field: '__rowActions',
      headerName: '',
      enableRowGroup: false,
      filter: false,
      floatingFilter: false,
      chartDataType: 'series',
      sortable: false,
      width: 93,
      minWidth: 50,
      resizable: false,
      pinned: 'left',
      suppressHeaderMenuButton: true,
      suppressHeaderContextMenu: true,
      suppressMovable: true,
      suppressColumnsToolPanel: true,
      menuTabs: []
    };

    colDef.cellRenderer = AgGridRowActionsRendererComponent;
    colDef.cellRendererParams = {
      projectId: this.actionableGridService.projectIdSubject.getValue(),
      versionId: this.actionableGridService.versionIdSubject.getValue(),
      dataStoreName: this.actionableGridService.dataStoreNameSubject.getValue()
    };

    return colDef;
  }

  // Returns master detail arrow.
  private getMasterDetailColumn(): ColDef {
    return {
      floatingFilter: false,
      sortable: false,
      resizable: false,
      maxWidth: 40,
      rowGroup: false,
      enableRowGroup: false,
      filter: false,
      suppressHeaderMenuButton: true,
      cellRenderer: 'agGroupCellRenderer',
      suppressColumnsToolPanel: true,
      suppressMovable: true,
      cellRendererParams: {
        suppressCount: true,
      }
    };
  }

  // Start of ColDef functions for Column Type.
  private setStringColDef(options: ColDefOptions): void {
    options.colDef.headerClass = (params) => options.columnConfig.required ? 'show-required' : '';
    options.colDef.filter = ActionableGridColumnFilters.Text;
    options.colDef.chartDataType = 'category';
    options.colDef.filterParams = { defaultOption: 'startsWith' };

    const cellRendererParams = {
      format: options.columnConfig.format,
      allowUserUpdate: options.columnConfig.allowUserUpdate,
      required: options.columnConfig.required,
      fieldBaseSchema: options.baseProperty,
      baseSchema: options.baseSchema,
      fieldName: options.colDef.field,
      fieldDisplayName: options.colDef.headerName,
      enableLookup: options.columnConfig.enableLookup,
      projectVersionId: options.projectVersionId,
    };

    if (options.columnConfig.allowUserUpdate) {
      if (options.columnConfig.dropdown) {
        options.colDef.cellRenderer = AgGridDropDownRendererComponent;
        options.colDef.cellRendererParams = { ...cellRendererParams, allowedValues: options.columnConfig.values };
      } else {
        options.colDef.cellRenderer = AgGridEditableRendererComponent;
        options.colDef.cellRendererParams = cellRendererParams;
      }
    }
  }

  private setDateColDef(options: ColDefOptions): void {
    this.setAgGridColDefRendererAndStyle(options);
    options.colDef.filter = AgGridDateFilterComponent;
    options.colDef.filterParams = {
      showTime: options.columnConfig.format.type === EditorColumnType.DateTime,
    };
    options.colDef.comparator = sortingDateComparator;
    options.colDef.valueFormatter = (params) => dateValueFormatter(params.value, options.columnConfig.format.type === EditorColumnType.DateTime);
  }

  getExtendedDateColumns(sourceDateColDefOptions: ColDefOptions): ColDef[] {
    const colId = sourceDateColDefOptions.colDef.colId;
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    const commonColDef = {
      enableRowGroup: true,
      floatingFilter: sourceDateColDefOptions.showFloatingFilter,
      sortable: true,
      width: 100,
      hide: true,
    };

    const sortComparator = (a: string, b: string) => {
      return months.indexOf(a) - months.indexOf(b);
    };

    return [
      {
        ...commonColDef,
        headerName: sourceDateColDefOptions.colDef.headerName + ' (Year)',
        colId: `${colId}_year`,
        valueGetter: (params) => {
          const date = new Date(params.data?.[colId]);
          return isNaN(date.getTime()) ? null : date.getFullYear();
        },
      },
      {
        ...commonColDef,
        headerName: sourceDateColDefOptions.colDef.headerName + ' (Month)',
        colId: `${colId}_month`,
        valueGetter: (params) => {
          const date = new Date(params.data?.[colId]);
          const monthIndex = isNaN(date.getTime()) ? null : date.getMonth();
          return monthIndex != null
            ? months[monthIndex]
            : null;
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          comparator: sortComparator,
        },
        comparator: sortComparator,
      },
      {
        ...commonColDef,
        headerName: sourceDateColDefOptions.colDef.headerName + ' (Quarter)',
        colId: `${colId}_quarter`,
        valueGetter: (params) => {
          const date = new Date(params.data?.[colId]);
          if (isNaN(date.getTime())) return null;
          const quarter = Math.floor(date.getMonth() / 3) + 1;
          return `Q${quarter}`;
        },
        filter: 'agSetColumnFilter',
      }
    ];
  }

  private setNumericColDef(options: ColDefOptions): void {
    this.setAgGridColDefRendererAndStyle(options);
    options.colDef.filter = ActionableGridColumnFilters.Number;
    options.colDef.valueGetter = (params) => this.numericValueGetter((params.data?.[params.column?.getColId()]));

    if (options.columnConfig.format.type === EditorColumnType.Currency) {
      options.colDef.valueFormatter = (params) => formatCurrency(params.value, options.columnConfig.format.currency,
        options.columnConfig.format.decimalPlaces);
    } else {
      options.colDef.valueFormatter = (params) => numericValueFormatter(params.value, options.columnConfig.format.decimalPlaces);
    }
  }

  private setSymbolColDef(options: ColDefOptions): void {
    options.colDef.headerClass = (params) => options.columnConfig.required ? 'show-required' : '';
    options.colDef.cellRenderer = AgGridMessageSymbolRendererComponent;
    options.colDef.cellRendererParams = {
      allowedValues: options.columnConfig.values, allowUserUpdate: options.columnConfig.allowUserUpdate,
      format: options.columnConfig.format, required: options.columnConfig.required,
      fieldBaseSchema: options.baseProperty,
    };
  }

  private setCheckboxColDef(options: ColDefOptions): void {
    options.colDef.cellRenderer = AgGridCheckBoxRendererComponent;
    options.colDef.headerClass = (params) => options.columnConfig.required ? 'show-required' : '';
    if (options.columnConfig.allowUserUpdate)
      options.colDef.headerComponent = AgGridHeaderCheckBoxRendererComponent;

    options.colDef.cellRendererParams = {
      allowedValues: options.columnConfig.values,
      allowUserUpdate: options.columnConfig.allowUserUpdate,
      format: options.columnConfig.format,
      required: options.columnConfig.required,
      fieldBaseSchema: options.baseProperty,
    };
  }

  private async setArrayColDef(options: ColDefOptions) {
    options.gridOptions.detailCellRenderer = AgGridMasterDetailRendererComponent;
    options.gridOptions.masterDetail = true;
    const childGridOptions = {
      detailRowAutoHeight: true,
      defaultColDef: {
        resizable: true
      },
      popupParent: document.querySelector('body'),
      context: {
        getMainGridId: options.gridOptions.context?.getMainGridId,
        getMasterRecordParams: options.gridOptions.context?.getMasterRecordParams,
      } as IRowActionsParamsContext
    };
    const columnDefs =
      await this.getColumnDefs(options.columnConfig.children,
        options.baseProperty.items,
        childGridOptions,
        false,
        options.projectVersionId,
        options.allowAddNewRow,
        options.columnConfig,
        null,
        options.bypassObjects
      );

    if (!options.gridOptions.detailCellRendererParams?.configs?.length)
      options.gridOptions.detailCellRendererParams = { configs: [] };

    options.gridOptions.detailCellRendererParams.configs.push(
      new ActionableGridMasterDetailConfigOptions(
        options.columnConfig,
        options.baseProperty,
        childGridOptions,
        options.allowAddNewRow,
        columnDefs,
        options.projectVersionId,
        options.projectId,
        options.gridOptions.context?.getMainGridId,
        options.gridOptions.context?.getMasterRecordParams));
  }

  private setLinkColDef(options: ColDefOptions): void {
    options.colDef.cellRenderer = AgGridHyperlinkRendererComponent;
    options.colDef.cellRendererParams = {
      allowedValues: options.columnConfig.values, allowUserUpdate: options.columnConfig.allowUserUpdate,
      link: options.columnConfig.link, format: options.columnConfig.format, required: options.columnConfig.required,
      projectVariables: options.projectVariables, fieldBaseSchema: options.baseProperty,
    };
  }

  private numericValueGetter(paramsValue: any) {
    if (paramsValue !== undefined) {
      return !isNaN(Number(paramsValue)) ? Number(paramsValue) : paramsValue;
    }
  }

  private setButtonColDef(options: ColDefOptions): void {
    const cellRendererParams = {
      format: options.columnConfig.format,
      allowUserUpdate: options.columnConfig.allowUserUpdate,
      required: options.columnConfig.required,
      fieldBaseSchema: options.baseProperty,
    };

    options.colDef.headerClass = (params) => options.columnConfig.required ? 'show-required' : '';
    options.colDef.cellRenderer = AgGridButtonRendererComponent;
    options.colDef.cellRendererParams = { ...cellRendererParams };
  }

  private setAgGridColDefRendererAndStyle(options: ColDefOptions) {
    const cellRendererParams = {
      format: options.columnConfig.format,
      allowUserUpdate: options.columnConfig.allowUserUpdate,
      required: options.columnConfig.required,
      fieldBaseSchema: options.baseProperty,
    };

    if (options.columnConfig.allowUserUpdate) {
      options.colDef.headerClass = (params) => (options.columnConfig.allowUserUpdate && options.columnConfig.required) ? 'show-required' : '';
      options.colDef.cellRenderer = AgGridEditableRendererComponent;
      options.colDef.cellRendererParams = cellRendererParams;
      options.colDef.cellClass = '';
    } else {
      options.colDef.cellClass = 'sb-field-right-align';
    }
  }
}
