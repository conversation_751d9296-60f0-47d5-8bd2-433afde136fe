{"ast": null, "code": "// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nexport function isEmptyData(data) {\n  if (typeof data === \"string\") {\n    return data.length === 0;\n  }\n  return data.byteLength === 0;\n}", "map": {"version": 3, "names": ["isEmptyData", "data", "length", "byteLength"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-crypto/util/build/module/isEmptyData.js"], "sourcesContent": ["// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nexport function isEmptyData(data) {\n    if (typeof data === \"string\") {\n        return data.length === 0;\n    }\n    return data.byteLength === 0;\n}\n"], "mappings": "AAAA;AACA;AACA,OAAO,SAASA,WAAWA,CAACC,IAAI,EAAE;EAC9B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOA,IAAI,CAACC,MAAM,KAAK,CAAC;EAC5B;EACA,OAAOD,IAAI,CAACE,UAAU,KAAK,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}