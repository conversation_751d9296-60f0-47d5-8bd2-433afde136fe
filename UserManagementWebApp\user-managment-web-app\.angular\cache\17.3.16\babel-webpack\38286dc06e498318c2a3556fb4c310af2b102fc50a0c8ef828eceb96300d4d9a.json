{"ast": null, "code": "const radii = {\n  xs: {\n    value: '0.125rem'\n  },\n  small: {\n    value: '0.25rem'\n  },\n  medium: {\n    value: '0.5rem'\n  },\n  large: {\n    value: '1rem'\n  },\n  xl: {\n    value: '2rem'\n  },\n  xxl: {\n    value: '4rem'\n  },\n  xxxl: {\n    value: '8rem'\n  }\n};\nexport { radii };", "map": {"version": 3, "names": ["radii", "xs", "value", "small", "medium", "large", "xl", "xxl", "xxxl"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/radii.mjs"], "sourcesContent": ["const radii = {\n    xs: { value: '0.125rem' },\n    small: { value: '0.25rem' },\n    medium: { value: '0.5rem' },\n    large: { value: '1rem' },\n    xl: { value: '2rem' },\n    xxl: { value: '4rem' },\n    xxxl: { value: '8rem' },\n};\n\nexport { radii };\n"], "mappings": "AAAA,MAAMA,KAAK,GAAG;EACVC,EAAE,EAAE;IAAEC,KAAK,EAAE;EAAW,CAAC;EACzBC,KAAK,EAAE;IAAED,KAAK,EAAE;EAAU,CAAC;EAC3BE,MAAM,EAAE;IAAEF,KAAK,EAAE;EAAS,CAAC;EAC3BG,KAAK,EAAE;IAAEH,KAAK,EAAE;EAAO,CAAC;EACxBI,EAAE,EAAE;IAAEJ,KAAK,EAAE;EAAO,CAAC;EACrBK,GAAG,EAAE;IAAEL,KAAK,EAAE;EAAO,CAAC;EACtBM,IAAI,EAAE;IAAEN,KAAK,EAAE;EAAO;AAC1B,CAAC;AAED,SAASF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}