{"ast": null, "code": "import { AmplifyError } from '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass AuthError extends AmplifyError {\n  constructor(params) {\n    super(params);\n    // Hack for making the custom error class work when transpiled to es5\n    // TODO: Delete the following 2 lines after we change the build target to >= es2015\n    this.constructor = AuthError;\n    Object.setPrototypeOf(this, AuthError.prototype);\n  }\n}\nexport { AuthError };", "map": {"version": 3, "names": ["AmplifyError", "<PERSON>th<PERSON><PERSON><PERSON>", "constructor", "params", "Object", "setPrototypeOf", "prototype"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/errors/AuthError.mjs"], "sourcesContent": ["import { AmplifyError } from '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass AuthError extends AmplifyError {\n    constructor(params) {\n        super(params);\n        // Hack for making the custom error class work when transpiled to es5\n        // TODO: Delete the following 2 lines after we change the build target to >= es2015\n        this.constructor = AuthError;\n        Object.setPrototypeOf(this, AuthError.prototype);\n    }\n}\n\nexport { AuthError };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mCAAmC;;AAEhE;AACA;AACA,MAAMC,SAAS,SAASD,YAAY,CAAC;EACjCE,WAAWA,CAACC,MAAM,EAAE;IAChB,KAAK,CAACA,MAAM,CAAC;IACb;IACA;IACA,IAAI,CAACD,WAAW,GAAGD,SAAS;IAC5BG,MAAM,CAACC,cAAc,CAAC,IAAI,EAAEJ,SAAS,CAACK,SAAS,CAAC;EACpD;AACJ;AAEA,SAASL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}