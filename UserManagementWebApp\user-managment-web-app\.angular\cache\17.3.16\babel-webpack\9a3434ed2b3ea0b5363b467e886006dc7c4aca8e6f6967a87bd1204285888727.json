{"ast": null, "code": "import { has } from './utils.mjs';\nimport { OPTIONS, REFERENCE_REGEX } from '../theme/createTheme/constants.mjs';\n\n/**\n * Checks if the value uses a value reference.\n * @param {string} value\n * @returns {boolean} - True, if the value uses a value reference\n */\nfunction usesReference(value) {\n  const regex = new RegExp(REFERENCE_REGEX);\n  if (typeof value === 'string') {\n    return regex.test(value);\n  }\n  if (typeof value === 'object') {\n    let hasReference = false;\n    // iterate over each property in the object,\n    // if any element passes the regex test,\n    // the whole thing should be true\n    for (const key in value) {\n      if (has(value, key)) {\n        const element = value[key];\n        let reference = usesReference(element);\n        if (reference) {\n          hasReference = true;\n          break;\n        }\n      }\n    }\n    return hasReference;\n  }\n  return false;\n}\nfunction resolveReference(path, obj) {\n  let ref = obj;\n  if (!Array.isArray(path)) {\n    return;\n  }\n  for (let i = 0; i < path.length; i++) {\n    // Check for undefined as 0 is a valid, truthy value\n    if (typeof ref[path[i]] !== 'undefined') {\n      ref = ref[path[i]];\n    } else {\n      // set the reference as undefined if we don't find anything\n      ref = undefined;\n      break;\n    }\n  }\n  return ref;\n}\n/**\n * Returns the path from a path name be splitting the name by a given separator.\n */\nfunction getPathFromName(pathName) {\n  if (typeof pathName !== 'string') {\n    throw new Error('Getting path from name failed. Name must be a string');\n  }\n  return pathName.split(OPTIONS.separator);\n}\n/**\n * Returns the paths name be joining its parts with a given separator.\n */\nfunction getName(path) {\n  if (!path || !(path instanceof Array)) {\n    throw new Error('Getting name for path failed. Path must be an array');\n  }\n  return path.join(OPTIONS.separator);\n}\nexport { getName, getPathFromName, resolveReference, usesReference };", "map": {"version": 3, "names": ["has", "OPTIONS", "REFERENCE_REGEX", "usesReference", "value", "regex", "RegExp", "test", "hasReference", "key", "element", "reference", "resolveReference", "path", "obj", "ref", "Array", "isArray", "i", "length", "undefined", "getPathFromName", "pathName", "Error", "split", "separator", "getName", "join"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/utils/references.mjs"], "sourcesContent": ["import { has } from './utils.mjs';\nimport { OPTIONS, REFERENCE_REGEX } from '../theme/createTheme/constants.mjs';\n\n/**\n * Checks if the value uses a value reference.\n * @param {string} value\n * @returns {boolean} - True, if the value uses a value reference\n */\nfunction usesReference(value) {\n    const regex = new RegExp(REFERENCE_REGEX);\n    if (typeof value === 'string') {\n        return regex.test(value);\n    }\n    if (typeof value === 'object') {\n        let hasReference = false;\n        // iterate over each property in the object,\n        // if any element passes the regex test,\n        // the whole thing should be true\n        for (const key in value) {\n            if (has(value, key)) {\n                const element = value[key];\n                let reference = usesReference(element);\n                if (reference) {\n                    hasReference = true;\n                    break;\n                }\n            }\n        }\n        return hasReference;\n    }\n    return false;\n}\nfunction resolveReference(path, obj) {\n    let ref = obj;\n    if (!Array.isArray(path)) {\n        return;\n    }\n    for (let i = 0; i < path.length; i++) {\n        // Check for undefined as 0 is a valid, truthy value\n        if (typeof ref[path[i]] !== 'undefined') {\n            ref = ref[path[i]];\n        }\n        else {\n            // set the reference as undefined if we don't find anything\n            ref = undefined;\n            break;\n        }\n    }\n    return ref;\n}\n/**\n * Returns the path from a path name be splitting the name by a given separator.\n */\nfunction getPathFromName(pathName) {\n    if (typeof pathName !== 'string') {\n        throw new Error('Getting path from name failed. Name must be a string');\n    }\n    return pathName.split(OPTIONS.separator);\n}\n/**\n * Returns the paths name be joining its parts with a given separator.\n */\nfunction getName(path) {\n    if (!path || !(path instanceof Array)) {\n        throw new Error('Getting name for path failed. Path must be an array');\n    }\n    return path.join(OPTIONS.separator);\n}\n\nexport { getName, getPathFromName, resolveReference, usesReference };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,aAAa;AACjC,SAASC,OAAO,EAAEC,eAAe,QAAQ,oCAAoC;;AAE7E;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC1B,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAACJ,eAAe,CAAC;EACzC,IAAI,OAAOE,KAAK,KAAK,QAAQ,EAAE;IAC3B,OAAOC,KAAK,CAACE,IAAI,CAACH,KAAK,CAAC;EAC5B;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC3B,IAAII,YAAY,GAAG,KAAK;IACxB;IACA;IACA;IACA,KAAK,MAAMC,GAAG,IAAIL,KAAK,EAAE;MACrB,IAAIJ,GAAG,CAACI,KAAK,EAAEK,GAAG,CAAC,EAAE;QACjB,MAAMC,OAAO,GAAGN,KAAK,CAACK,GAAG,CAAC;QAC1B,IAAIE,SAAS,GAAGR,aAAa,CAACO,OAAO,CAAC;QACtC,IAAIC,SAAS,EAAE;UACXH,YAAY,GAAG,IAAI;UACnB;QACJ;MACJ;IACJ;IACA,OAAOA,YAAY;EACvB;EACA,OAAO,KAAK;AAChB;AACA,SAASI,gBAAgBA,CAACC,IAAI,EAAEC,GAAG,EAAE;EACjC,IAAIC,GAAG,GAAGD,GAAG;EACb,IAAI,CAACE,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAE;IACtB;EACJ;EACA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;IAClC;IACA,IAAI,OAAOH,GAAG,CAACF,IAAI,CAACK,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;MACrCH,GAAG,GAAGA,GAAG,CAACF,IAAI,CAACK,CAAC,CAAC,CAAC;IACtB,CAAC,MACI;MACD;MACAH,GAAG,GAAGK,SAAS;MACf;IACJ;EACJ;EACA,OAAOL,GAAG;AACd;AACA;AACA;AACA;AACA,SAASM,eAAeA,CAACC,QAAQ,EAAE;EAC/B,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC9B,MAAM,IAAIC,KAAK,CAAC,sDAAsD,CAAC;EAC3E;EACA,OAAOD,QAAQ,CAACE,KAAK,CAACvB,OAAO,CAACwB,SAAS,CAAC;AAC5C;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACb,IAAI,EAAE;EACnB,IAAI,CAACA,IAAI,IAAI,EAAEA,IAAI,YAAYG,KAAK,CAAC,EAAE;IACnC,MAAM,IAAIO,KAAK,CAAC,qDAAqD,CAAC;EAC1E;EACA,OAAOV,IAAI,CAACc,IAAI,CAAC1B,OAAO,CAACwB,SAAS,CAAC;AACvC;AAEA,SAASC,OAAO,EAAEL,eAAe,EAAET,gBAAgB,EAAET,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}