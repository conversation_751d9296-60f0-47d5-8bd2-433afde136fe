{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { decodeJWT, AmplifyError } from '@aws-amplify/core/internals/utils';\nimport { tokenOrchestrator } from './tokenProvider.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction cacheCognitoTokens(_x) {\n  return _cacheCognitoTokens.apply(this, arguments);\n}\nfunction _cacheCognitoTokens() {\n  _cacheCognitoTokens = _asyncToGenerator(function* (AuthenticationResult) {\n    if (AuthenticationResult.AccessToken) {\n      const accessToken = decodeJWT(AuthenticationResult.AccessToken);\n      const accessTokenIssuedAtInMillis = (accessToken.payload.iat || 0) * 1000;\n      const currentTime = new Date().getTime();\n      const clockDrift = accessTokenIssuedAtInMillis > 0 ? accessTokenIssuedAtInMillis - currentTime : 0;\n      let idToken;\n      let refreshToken;\n      let deviceMetadata;\n      if (AuthenticationResult.RefreshToken) {\n        refreshToken = AuthenticationResult.RefreshToken;\n      }\n      if (AuthenticationResult.IdToken) {\n        idToken = decodeJWT(AuthenticationResult.IdToken);\n      }\n      if (AuthenticationResult?.NewDeviceMetadata) {\n        deviceMetadata = AuthenticationResult.NewDeviceMetadata;\n      }\n      const tokens = {\n        accessToken,\n        idToken,\n        refreshToken,\n        clockDrift,\n        deviceMetadata,\n        username: AuthenticationResult.username\n      };\n      if (AuthenticationResult?.signInDetails) {\n        tokens.signInDetails = AuthenticationResult.signInDetails;\n      }\n      yield tokenOrchestrator.setTokens({\n        tokens\n      });\n    } else {\n      // This would be a service error\n      throw new AmplifyError({\n        message: 'Invalid tokens',\n        name: 'InvalidTokens',\n        recoverySuggestion: 'Check Cognito UserPool settings'\n      });\n    }\n  });\n  return _cacheCognitoTokens.apply(this, arguments);\n}\nexport { cacheCognitoTokens };", "map": {"version": 3, "names": ["decodeJWT", "AmplifyError", "tokenOrchestrator", "cacheCognitoTokens", "_x", "_cacheCognitoTokens", "apply", "arguments", "_asyncToGenerator", "AuthenticationResult", "AccessToken", "accessToken", "accessTokenIssuedAtInMillis", "payload", "iat", "currentTime", "Date", "getTime", "clockDrift", "idToken", "refreshToken", "deviceMetadata", "RefreshToken", "IdToken", "NewDeviceMetadata", "tokens", "username", "signInDetails", "setTokens", "message", "name", "recoverySuggestion"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/tokenProvider/cacheTokens.mjs"], "sourcesContent": ["import { decodeJWT, AmplifyError } from '@aws-amplify/core/internals/utils';\nimport { tokenOrchestrator } from './tokenProvider.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nasync function cacheCognitoTokens(AuthenticationResult) {\n    if (AuthenticationResult.AccessToken) {\n        const accessToken = decodeJWT(AuthenticationResult.AccessToken);\n        const accessTokenIssuedAtInMillis = (accessToken.payload.iat || 0) * 1000;\n        const currentTime = new Date().getTime();\n        const clockDrift = accessTokenIssuedAtInMillis > 0\n            ? accessTokenIssuedAtInMillis - currentTime\n            : 0;\n        let idToken;\n        let refreshToken;\n        let deviceMetadata;\n        if (AuthenticationResult.RefreshToken) {\n            refreshToken = AuthenticationResult.RefreshToken;\n        }\n        if (AuthenticationResult.IdToken) {\n            idToken = decodeJWT(AuthenticationResult.IdToken);\n        }\n        if (AuthenticationResult?.NewDeviceMetadata) {\n            deviceMetadata = AuthenticationResult.NewDeviceMetadata;\n        }\n        const tokens = {\n            accessToken,\n            idToken,\n            refreshToken,\n            clockDrift,\n            deviceMetadata,\n            username: AuthenticationResult.username,\n        };\n        if (AuthenticationResult?.signInDetails) {\n            tokens.signInDetails = AuthenticationResult.signInDetails;\n        }\n        await tokenOrchestrator.setTokens({\n            tokens,\n        });\n    }\n    else {\n        // This would be a service error\n        throw new AmplifyError({\n            message: 'Invalid tokens',\n            name: 'InvalidTokens',\n            recoverySuggestion: 'Check Cognito UserPool settings',\n        });\n    }\n}\n\nexport { cacheCognitoTokens };\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,YAAY,QAAQ,mCAAmC;AAC3E,SAASC,iBAAiB,QAAQ,qBAAqB;;AAEvD;AACA;AAAA,SACeC,kBAAkBA,CAAAC,EAAA;EAAA,OAAAC,mBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,oBAAA;EAAAA,mBAAA,GAAAG,iBAAA,CAAjC,WAAkCC,oBAAoB,EAAE;IACpD,IAAIA,oBAAoB,CAACC,WAAW,EAAE;MAClC,MAAMC,WAAW,GAAGX,SAAS,CAACS,oBAAoB,CAACC,WAAW,CAAC;MAC/D,MAAME,2BAA2B,GAAG,CAACD,WAAW,CAACE,OAAO,CAACC,GAAG,IAAI,CAAC,IAAI,IAAI;MACzE,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MACxC,MAAMC,UAAU,GAAGN,2BAA2B,GAAG,CAAC,GAC5CA,2BAA2B,GAAGG,WAAW,GACzC,CAAC;MACP,IAAII,OAAO;MACX,IAAIC,YAAY;MAChB,IAAIC,cAAc;MAClB,IAAIZ,oBAAoB,CAACa,YAAY,EAAE;QACnCF,YAAY,GAAGX,oBAAoB,CAACa,YAAY;MACpD;MACA,IAAIb,oBAAoB,CAACc,OAAO,EAAE;QAC9BJ,OAAO,GAAGnB,SAAS,CAACS,oBAAoB,CAACc,OAAO,CAAC;MACrD;MACA,IAAId,oBAAoB,EAAEe,iBAAiB,EAAE;QACzCH,cAAc,GAAGZ,oBAAoB,CAACe,iBAAiB;MAC3D;MACA,MAAMC,MAAM,GAAG;QACXd,WAAW;QACXQ,OAAO;QACPC,YAAY;QACZF,UAAU;QACVG,cAAc;QACdK,QAAQ,EAAEjB,oBAAoB,CAACiB;MACnC,CAAC;MACD,IAAIjB,oBAAoB,EAAEkB,aAAa,EAAE;QACrCF,MAAM,CAACE,aAAa,GAAGlB,oBAAoB,CAACkB,aAAa;MAC7D;MACA,MAAMzB,iBAAiB,CAAC0B,SAAS,CAAC;QAC9BH;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD;MACA,MAAM,IAAIxB,YAAY,CAAC;QACnB4B,OAAO,EAAE,gBAAgB;QACzBC,IAAI,EAAE,eAAe;QACrBC,kBAAkB,EAAE;MACxB,CAAC,CAAC;IACN;EACJ,CAAC;EAAA,OAAA1B,mBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}