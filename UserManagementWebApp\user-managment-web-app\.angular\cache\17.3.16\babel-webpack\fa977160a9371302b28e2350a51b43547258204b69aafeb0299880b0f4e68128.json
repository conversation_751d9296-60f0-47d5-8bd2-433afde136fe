{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\nimport { GetMapStyleDescriptorRequestFilterSensitiveLog, GetMapStyleDescriptorResponseFilterSensitiveLog } from \"../models/models_0\";\nimport { deserializeAws_restJson1GetMapStyleDescriptorCommand, serializeAws_restJson1GetMapStyleDescriptorCommand } from \"../protocols/Aws_restJson1\";\nvar GetMapStyleDescriptorCommand = function (_super) {\n  __extends(GetMapStyleDescriptorCommand, _super);\n  function GetMapStyleDescriptorCommand(input) {\n    var _this = _super.call(this) || this;\n    _this.input = input;\n    return _this;\n  }\n  GetMapStyleDescriptorCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n    this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n    var stack = clientStack.concat(this.middlewareStack);\n    var logger = configuration.logger;\n    var clientName = \"LocationClient\";\n    var commandName = \"GetMapStyleDescriptorCommand\";\n    var handlerExecutionContext = {\n      logger: logger,\n      clientName: clientName,\n      commandName: commandName,\n      inputFilterSensitiveLog: GetMapStyleDescriptorRequestFilterSensitiveLog,\n      outputFilterSensitiveLog: GetMapStyleDescriptorResponseFilterSensitiveLog\n    };\n    var requestHandler = configuration.requestHandler;\n    return stack.resolve(function (request) {\n      return requestHandler.handle(request.request, options || {});\n    }, handlerExecutionContext);\n  };\n  GetMapStyleDescriptorCommand.prototype.serialize = function (input, context) {\n    return serializeAws_restJson1GetMapStyleDescriptorCommand(input, context);\n  };\n  GetMapStyleDescriptorCommand.prototype.deserialize = function (output, context) {\n    return deserializeAws_restJson1GetMapStyleDescriptorCommand(output, context);\n  };\n  return GetMapStyleDescriptorCommand;\n}($Command);\nexport { GetMapStyleDescriptorCommand };", "map": {"version": 3, "names": ["__extends", "getSerdePlugin", "Command", "$Command", "GetMapStyleDescriptorRequestFilterSensitiveLog", "GetMapStyleDescriptorResponseFilterSensitiveLog", "deserializeAws_restJson1GetMapStyleDescriptorCommand", "serializeAws_restJson1GetMapStyleDescriptorCommand", "GetMapStyleDescriptorCommand", "_super", "input", "_this", "call", "prototype", "resolveMiddleware", "clientStack", "configuration", "options", "middlewareStack", "use", "serialize", "deserialize", "stack", "concat", "logger", "clientName", "commandName", "handlerExecutionContext", "inputFilterSensitiveLog", "outputFilterSensitiveLog", "requestHandler", "resolve", "request", "handle", "context", "output"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-location/dist-es/commands/GetMapStyleDescriptorCommand.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\nimport { GetMapStyleDescriptorRequestFilterSensitiveLog, GetMapStyleDescriptorResponseFilterSensitiveLog, } from \"../models/models_0\";\nimport { deserializeAws_restJson1GetMapStyleDescriptorCommand, serializeAws_restJson1GetMapStyleDescriptorCommand, } from \"../protocols/Aws_restJson1\";\nvar GetMapStyleDescriptorCommand = (function (_super) {\n    __extends(GetMapStyleDescriptorCommand, _super);\n    function GetMapStyleDescriptorCommand(input) {\n        var _this = _super.call(this) || this;\n        _this.input = input;\n        return _this;\n    }\n    GetMapStyleDescriptorCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n        this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n        var stack = clientStack.concat(this.middlewareStack);\n        var logger = configuration.logger;\n        var clientName = \"LocationClient\";\n        var commandName = \"GetMapStyleDescriptorCommand\";\n        var handlerExecutionContext = {\n            logger: logger,\n            clientName: clientName,\n            commandName: commandName,\n            inputFilterSensitiveLog: GetMapStyleDescriptorRequestFilterSensitiveLog,\n            outputFilterSensitiveLog: GetMapStyleDescriptorResponseFilterSensitiveLog,\n        };\n        var requestHandler = configuration.requestHandler;\n        return stack.resolve(function (request) {\n            return requestHandler.handle(request.request, options || {});\n        }, handlerExecutionContext);\n    };\n    GetMapStyleDescriptorCommand.prototype.serialize = function (input, context) {\n        return serializeAws_restJson1GetMapStyleDescriptorCommand(input, context);\n    };\n    GetMapStyleDescriptorCommand.prototype.deserialize = function (output, context) {\n        return deserializeAws_restJson1GetMapStyleDescriptorCommand(output, context);\n    };\n    return GetMapStyleDescriptorCommand;\n}($Command));\nexport { GetMapStyleDescriptorCommand };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,OAAO,IAAIC,QAAQ,QAAQ,wBAAwB;AAC5D,SAASC,8CAA8C,EAAEC,+CAA+C,QAAS,oBAAoB;AACrI,SAASC,oDAAoD,EAAEC,kDAAkD,QAAS,4BAA4B;AACtJ,IAAIC,4BAA4B,GAAI,UAAUC,MAAM,EAAE;EAClDT,SAAS,CAACQ,4BAA4B,EAAEC,MAAM,CAAC;EAC/C,SAASD,4BAA4BA,CAACE,KAAK,EAAE;IACzC,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACD,KAAK,GAAGA,KAAK;IACnB,OAAOC,KAAK;EAChB;EACAH,4BAA4B,CAACK,SAAS,CAACC,iBAAiB,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAEC,OAAO,EAAE;IACtG,IAAI,CAACC,eAAe,CAACC,GAAG,CAAClB,cAAc,CAACe,aAAa,EAAE,IAAI,CAACI,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;IACzF,IAAIC,KAAK,GAAGP,WAAW,CAACQ,MAAM,CAAC,IAAI,CAACL,eAAe,CAAC;IACpD,IAAIM,MAAM,GAAGR,aAAa,CAACQ,MAAM;IACjC,IAAIC,UAAU,GAAG,gBAAgB;IACjC,IAAIC,WAAW,GAAG,8BAA8B;IAChD,IAAIC,uBAAuB,GAAG;MAC1BH,MAAM,EAAEA,MAAM;MACdC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA,WAAW;MACxBE,uBAAuB,EAAExB,8CAA8C;MACvEyB,wBAAwB,EAAExB;IAC9B,CAAC;IACD,IAAIyB,cAAc,GAAGd,aAAa,CAACc,cAAc;IACjD,OAAOR,KAAK,CAACS,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpC,OAAOF,cAAc,CAACG,MAAM,CAACD,OAAO,CAACA,OAAO,EAAEf,OAAO,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC,EAAEU,uBAAuB,CAAC;EAC/B,CAAC;EACDnB,4BAA4B,CAACK,SAAS,CAACO,SAAS,GAAG,UAAUV,KAAK,EAAEwB,OAAO,EAAE;IACzE,OAAO3B,kDAAkD,CAACG,KAAK,EAAEwB,OAAO,CAAC;EAC7E,CAAC;EACD1B,4BAA4B,CAACK,SAAS,CAACQ,WAAW,GAAG,UAAUc,MAAM,EAAED,OAAO,EAAE;IAC5E,OAAO5B,oDAAoD,CAAC6B,MAAM,EAAED,OAAO,CAAC;EAChF,CAAC;EACD,OAAO1B,4BAA4B;AACvC,CAAC,CAACL,QAAQ,CAAE;AACZ,SAASK,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}