{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createDeleteUserAttributesClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createDeleteUserAttributesClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Deletes user attributes.\n *\n * @param input -  The DeleteUserAttributesInput object\n * @throws  -{@link DeleteUserAttributesException } - Thrown due to invalid attribute.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nfunction deleteUserAttributes(_x) {\n  return _deleteUserAttributes.apply(this, arguments);\n}\nfunction _deleteUserAttributes() {\n  _deleteUserAttributes = _asyncToGenerator(function* (input) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userAttributeKeys\n    } = input;\n    const {\n      userPoolEndpoint,\n      userPoolId\n    } = authConfig;\n    const {\n      tokens\n    } = yield fetchAuthSession({\n      forceRefresh: false\n    });\n    assertAuthTokens(tokens);\n    const deleteUserAttributesClient = createDeleteUserAttributesClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    yield deleteUserAttributesClient({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.DeleteUserAttributes)\n    }, {\n      AccessToken: tokens.accessToken.toString(),\n      UserAttributeNames: userAttributeKeys\n    });\n  });\n  return _deleteUserAttributes.apply(this, arguments);\n}\nexport { deleteUserAttributes };", "map": {"version": 3, "names": ["Amplify", "fetchAuthSession", "assertTokenProviderConfig", "AuthAction", "getRegionFromUserPoolId", "assertAuthTokens", "getAuthUserAgentValue", "createDeleteUserAttributesClient", "createCognitoUserPoolEndpointResolver", "deleteUserAttributes", "_x", "_deleteUserAttributes", "apply", "arguments", "_asyncToGenerator", "input", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userAttributeKeys", "userPoolEndpoint", "userPoolId", "tokens", "forceRefresh", "deleteUserAttributesClient", "endpointResolver", "endpointOverride", "region", "userAgentValue", "DeleteUserAttributes", "AccessToken", "accessToken", "toString", "UserAttributeNames"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/deleteUserAttributes.mjs"], "sourcesContent": ["import { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createDeleteUserAttributesClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createDeleteUserAttributesClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Deletes user attributes.\n *\n * @param input -  The DeleteUserAttributesInput object\n * @throws  -{@link DeleteUserAttributesException } - Thrown due to invalid attribute.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nasync function deleteUserAttributes(input) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userAttributeKeys } = input;\n    const { userPoolEndpoint, userPoolId } = authConfig;\n    const { tokens } = await fetchAuthSession({ forceRefresh: false });\n    assertAuthTokens(tokens);\n    const deleteUserAttributesClient = createDeleteUserAttributesClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    await deleteUserAttributesClient({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.DeleteUserAttributes),\n    }, {\n        AccessToken: tokens.accessToken.toString(),\n        UserAttributeNames: userAttributeKeys,\n    });\n}\n\nexport { deleteUserAttributes };\n"], "mappings": ";AAAA,SAASA,OAAO,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC7D,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,sCAAsC;AAC7C,OAAO,qBAAqB;AAC5B,SAASC,gCAAgC,QAAQ,2GAA2G;AAC5J,SAASC,qCAAqC,QAAQ,wDAAwD;;AAE9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANA,SAOeC,oBAAoBA,CAAAC,EAAA;EAAA,OAAAC,qBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,sBAAA;EAAAA,qBAAA,GAAAG,iBAAA,CAAnC,WAAoCC,KAAK,EAAE;IACvC,MAAMC,UAAU,GAAGhB,OAAO,CAACiB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDjB,yBAAyB,CAACc,UAAU,CAAC;IACrC,MAAM;MAAEI;IAAkB,CAAC,GAAGL,KAAK;IACnC,MAAM;MAAEM,gBAAgB;MAAEC;IAAW,CAAC,GAAGN,UAAU;IACnD,MAAM;MAAEO;IAAO,CAAC,SAAStB,gBAAgB,CAAC;MAAEuB,YAAY,EAAE;IAAM,CAAC,CAAC;IAClEnB,gBAAgB,CAACkB,MAAM,CAAC;IACxB,MAAME,0BAA0B,GAAGlB,gCAAgC,CAAC;MAChEmB,gBAAgB,EAAElB,qCAAqC,CAAC;QACpDmB,gBAAgB,EAAEN;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMI,0BAA0B,CAAC;MAC7BG,MAAM,EAAExB,uBAAuB,CAACkB,UAAU,CAAC;MAC3CO,cAAc,EAAEvB,qBAAqB,CAACH,UAAU,CAAC2B,oBAAoB;IACzE,CAAC,EAAE;MACCC,WAAW,EAAER,MAAM,CAACS,WAAW,CAACC,QAAQ,CAAC,CAAC;MAC1CC,kBAAkB,EAAEd;IACxB,CAAC,CAAC;EACN,CAAC;EAAA,OAAAT,qBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}