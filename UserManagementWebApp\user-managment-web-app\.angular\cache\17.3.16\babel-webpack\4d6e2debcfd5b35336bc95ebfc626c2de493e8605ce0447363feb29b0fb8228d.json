{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { composeTransferHandler } from '@aws-amplify/core/internals/aws-client-utils/composers';\nimport { unauthenticatedHandler } from '@aws-amplify/core/internals/aws-client-utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * A Cognito Identity-specific middleware that disables caching for all requests.\n */\nconst disableCacheMiddlewareFactory = () => (next, _) => (/*#__PURE__*/function () {\n  var _disableCacheMiddleware = _asyncToGenerator(function* (request) {\n    request.headers['cache-control'] = 'no-store';\n    return next(request);\n  });\n  function disableCacheMiddleware(_x) {\n    return _disableCacheMiddleware.apply(this, arguments);\n  }\n  return disableCacheMiddleware;\n}());\n/**\n * A Cognito Identity-specific transfer handler that does NOT sign requests, and\n * disables caching.\n *\n * @internal\n */\nconst cognitoUserPoolTransferHandler = composeTransferHandler(unauthenticatedHandler, [disableCacheMiddlewareFactory]);\nexport { cognitoUserPoolTransferHandler };", "map": {"version": 3, "names": ["composeTransferHandler", "unauthentica<PERSON><PERSON><PERSON><PERSON>", "disableCacheMiddlewareFactory", "next", "_", "_disableCacheMiddleware", "_asyncToGenerator", "request", "headers", "disableCacheMiddleware", "_x", "apply", "arguments", "cognitoUserPoolTransferHandler"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs"], "sourcesContent": ["import { composeTransferHandler } from '@aws-amplify/core/internals/aws-client-utils/composers';\nimport { unauthenticatedHandler } from '@aws-amplify/core/internals/aws-client-utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * A Cognito Identity-specific middleware that disables caching for all requests.\n */\nconst disableCacheMiddlewareFactory = () => (next, _) => async function disableCacheMiddleware(request) {\n    request.headers['cache-control'] = 'no-store';\n    return next(request);\n};\n/**\n * A Cognito Identity-specific transfer handler that does NOT sign requests, and\n * disables caching.\n *\n * @internal\n */\nconst cognitoUserPoolTransferHandler = composeTransferHandler(unauthenticatedHandler, [disableCacheMiddlewareFactory]);\n\nexport { cognitoUserPoolTransferHandler };\n"], "mappings": ";AAAA,SAASA,sBAAsB,QAAQ,wDAAwD;AAC/F,SAASC,sBAAsB,QAAQ,8CAA8C;;AAErF;AACA;AACA;AACA;AACA;AACA,MAAMC,6BAA6B,GAAGA,CAAA,KAAM,CAACC,IAAI,EAAEC,CAAC;EAAA,IAAAC,uBAAA,GAAAC,iBAAA,CAAK,WAAsCC,OAAO,EAAE;IACpGA,OAAO,CAACC,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU;IAC7C,OAAOL,IAAI,CAACI,OAAO,CAAC;EACxB,CAAC;EAAA,SAHuEE,sBAAsBA,CAAAC,EAAA;IAAA,OAAAL,uBAAA,CAAAM,KAAA,OAAAC,SAAA;EAAA;EAAA,OAAtBH,sBAAsB;AAAA,IAG7F;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,8BAA8B,GAAGb,sBAAsB,CAACC,sBAAsB,EAAE,CAACC,6BAA6B,CAAC,CAAC;AAEtH,SAASW,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}