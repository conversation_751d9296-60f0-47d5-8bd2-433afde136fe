{"ast": null, "code": "import { assertPasskeyError, PasskeyErrorCode } from '../errors.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction assertValidCredentialCreationOptions(credentialCreationOptions) {\n  assertPasskeyError([!!credentialCreationOptions, !!credentialCreationOptions?.challenge, !!credentialCreationOptions?.user, !!credentialCreationOptions?.rp, !!credentialCreationOptions?.pubKeyCredParams].every(Boolean), PasskeyErrorCode.InvalidPasskeyRegistrationOptions);\n}\nexport { assertValidCredentialCreationOptions };", "map": {"version": 3, "names": ["assertPasskeyError", "PasskeyErrorCode", "assertValidCredentialCreationOptions", "credentialCreationOptions", "challenge", "user", "rp", "pubKeyCredParams", "every", "Boolean", "InvalidPasskeyRegistrationOptions"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/utils/passkey/types/shared.mjs"], "sourcesContent": ["import { assertPasskeyError, PasskeyErrorCode } from '../errors.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction assertValidCredentialCreationOptions(credentialCreationOptions) {\n    assertPasskeyError([\n        !!credentialCreationOptions,\n        !!credentialCreationOptions?.challenge,\n        !!credentialCreationOptions?.user,\n        !!credentialCreationOptions?.rp,\n        !!credentialCreationOptions?.pubKeyCredParams,\n    ].every(Boolean), PasskeyErrorCode.InvalidPasskeyRegistrationOptions);\n}\n\nexport { assertValidCredentialCreationOptions };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,gBAAgB,QAAQ,eAAe;;AAEpE;AACA;AACA,SAASC,oCAAoCA,CAACC,yBAAyB,EAAE;EACrEH,kBAAkB,CAAC,CACf,CAAC,CAACG,yBAAyB,EAC3B,CAAC,CAACA,yBAAyB,EAAEC,SAAS,EACtC,CAAC,CAACD,yBAAyB,EAAEE,IAAI,EACjC,CAAC,CAACF,yBAAyB,EAAEG,EAAE,EAC/B,CAAC,CAACH,yBAAyB,EAAEI,gBAAgB,CAChD,CAACC,KAAK,CAACC,OAAO,CAAC,EAAER,gBAAgB,CAACS,iCAAiC,CAAC;AACzE;AAEA,SAASR,oCAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}