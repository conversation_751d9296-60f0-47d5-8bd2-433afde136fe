{"ast": null, "code": "import { from, Observable } from 'rxjs';\nimport '../types/errors.mjs';\nimport '../errors/errorHelpers.mjs';\nimport '../utils/getClientInfo/getClientInfo.mjs';\nimport { isWebWorker } from '../utils/isWebWorker.mjs';\nimport '../utils/retry/retry.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass Reachability {\n  networkMonitor(_) {\n    const globalObj = isWebWorker() ? self : typeof window !== 'undefined' && window;\n    if (!globalObj) {\n      return from([{\n        online: true\n      }]);\n    }\n    return new Observable(observer => {\n      observer.next({\n        online: globalObj.navigator.onLine\n      });\n      const notifyOnline = () => {\n        observer.next({\n          online: true\n        });\n      };\n      const notifyOffline = () => {\n        observer.next({\n          online: false\n        });\n      };\n      globalObj.addEventListener('online', notifyOnline);\n      globalObj.addEventListener('offline', notifyOffline);\n      Reachability._observers.push(observer);\n      return () => {\n        globalObj.removeEventListener('online', notifyOnline);\n        globalObj.removeEventListener('offline', notifyOffline);\n        Reachability._observers = Reachability._observers.filter(_observer => _observer !== observer);\n      };\n    });\n  }\n  // expose observers to simulate offline mode for integration testing\n  static _observerOverride(status) {\n    for (const observer of this._observers) {\n      if (observer.closed) {\n        this._observers = this._observers.filter(_observer => _observer !== observer);\n        continue;\n      }\n      observer?.next && observer.next(status);\n    }\n  }\n}\nReachability._observers = [];\nexport { Reachability };", "map": {"version": 3, "names": ["from", "Observable", "isWebWorker", "Reachability", "networkMonitor", "_", "globalObj", "self", "window", "online", "observer", "next", "navigator", "onLine", "notifyOnline", "notifyOffline", "addEventListener", "_observers", "push", "removeEventListener", "filter", "_observer", "_observerOverride", "status", "closed"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Reachability/Reachability.mjs"], "sourcesContent": ["import { from, Observable } from 'rxjs';\nimport '../types/errors.mjs';\nimport '../errors/errorHelpers.mjs';\nimport '../utils/getClientInfo/getClientInfo.mjs';\nimport { isWebWorker } from '../utils/isWebWorker.mjs';\nimport '../utils/retry/retry.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass Reachability {\n    networkMonitor(_) {\n        const globalObj = isWebWorker()\n            ? self\n            : typeof window !== 'undefined' && window;\n        if (!globalObj) {\n            return from([{ online: true }]);\n        }\n        return new Observable(observer => {\n            observer.next({ online: globalObj.navigator.onLine });\n            const notifyOnline = () => {\n                observer.next({ online: true });\n            };\n            const notifyOffline = () => {\n                observer.next({ online: false });\n            };\n            globalObj.addEventListener('online', notifyOnline);\n            globalObj.addEventListener('offline', notifyOffline);\n            Reachability._observers.push(observer);\n            return () => {\n                globalObj.removeEventListener('online', notifyOnline);\n                globalObj.removeEventListener('offline', notifyOffline);\n                Reachability._observers = Reachability._observers.filter(_observer => _observer !== observer);\n            };\n        });\n    }\n    // expose observers to simulate offline mode for integration testing\n    static _observerOverride(status) {\n        for (const observer of this._observers) {\n            if (observer.closed) {\n                this._observers = this._observers.filter(_observer => _observer !== observer);\n                continue;\n            }\n            observer?.next && observer.next(status);\n        }\n    }\n}\nReachability._observers = [];\n\nexport { Reachability };\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,UAAU,QAAQ,MAAM;AACvC,OAAO,qBAAqB;AAC5B,OAAO,4BAA4B;AACnC,OAAO,0CAA0C;AACjD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,OAAO,0BAA0B;;AAEjC;AACA;AACA,MAAMC,YAAY,CAAC;EACfC,cAAcA,CAACC,CAAC,EAAE;IACd,MAAMC,SAAS,GAAGJ,WAAW,CAAC,CAAC,GACzBK,IAAI,GACJ,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM;IAC7C,IAAI,CAACF,SAAS,EAAE;MACZ,OAAON,IAAI,CAAC,CAAC;QAAES,MAAM,EAAE;MAAK,CAAC,CAAC,CAAC;IACnC;IACA,OAAO,IAAIR,UAAU,CAACS,QAAQ,IAAI;MAC9BA,QAAQ,CAACC,IAAI,CAAC;QAAEF,MAAM,EAAEH,SAAS,CAACM,SAAS,CAACC;MAAO,CAAC,CAAC;MACrD,MAAMC,YAAY,GAAGA,CAAA,KAAM;QACvBJ,QAAQ,CAACC,IAAI,CAAC;UAAEF,MAAM,EAAE;QAAK,CAAC,CAAC;MACnC,CAAC;MACD,MAAMM,aAAa,GAAGA,CAAA,KAAM;QACxBL,QAAQ,CAACC,IAAI,CAAC;UAAEF,MAAM,EAAE;QAAM,CAAC,CAAC;MACpC,CAAC;MACDH,SAAS,CAACU,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;MAClDR,SAAS,CAACU,gBAAgB,CAAC,SAAS,EAAED,aAAa,CAAC;MACpDZ,YAAY,CAACc,UAAU,CAACC,IAAI,CAACR,QAAQ,CAAC;MACtC,OAAO,MAAM;QACTJ,SAAS,CAACa,mBAAmB,CAAC,QAAQ,EAAEL,YAAY,CAAC;QACrDR,SAAS,CAACa,mBAAmB,CAAC,SAAS,EAAEJ,aAAa,CAAC;QACvDZ,YAAY,CAACc,UAAU,GAAGd,YAAY,CAACc,UAAU,CAACG,MAAM,CAACC,SAAS,IAAIA,SAAS,KAAKX,QAAQ,CAAC;MACjG,CAAC;IACL,CAAC,CAAC;EACN;EACA;EACA,OAAOY,iBAAiBA,CAACC,MAAM,EAAE;IAC7B,KAAK,MAAMb,QAAQ,IAAI,IAAI,CAACO,UAAU,EAAE;MACpC,IAAIP,QAAQ,CAACc,MAAM,EAAE;QACjB,IAAI,CAACP,UAAU,GAAG,IAAI,CAACA,UAAU,CAACG,MAAM,CAACC,SAAS,IAAIA,SAAS,KAAKX,QAAQ,CAAC;QAC7E;MACJ;MACAA,QAAQ,EAAEC,IAAI,IAAID,QAAQ,CAACC,IAAI,CAACY,MAAM,CAAC;IAC3C;EACJ;AACJ;AACApB,YAAY,CAACc,UAAU,GAAG,EAAE;AAE5B,SAASd,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}