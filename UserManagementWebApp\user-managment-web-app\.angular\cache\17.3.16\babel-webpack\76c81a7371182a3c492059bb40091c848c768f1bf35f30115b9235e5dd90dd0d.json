{"ast": null, "code": "/* eslint-disable */\n// @ts-nocheck -> BigInteger is already a vended utility\n// A small implementation of BigInteger based on http://www-cs-students.stanford.edu/~tjw/jsbn/\n//\n// All public methods have been removed except the following:\n//   new BigInteger(a, b) (only radix 2, 4, 8, 16 and 32 supported)\n//   toString (only radix 2, 4, 8, 16 and 32 supported)\n//   negate\n//   abs\n//   compareTo\n//   bitLength\n//   mod\n//   equals\n//   add\n//   subtract\n//   multiply\n//   divide\n//   modPow\n/*\n * Copyright (c) 2003-2005  Tom Wu\n * All Rights Reserved.\n *\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n *\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS-IS\" AND WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY\n * WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.\n *\n * IN NO EVENT SHALL TOM WU BE LIABLE FOR ANY SPECIAL, INCIDENTAL,\n * INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND, OR ANY DAMAGES WHATSOEVER\n * RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER OR NOT ADVISED OF\n * THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF LIABILITY, ARISING OUT\n * OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n *\n * In addition, the following condition applies:\n *\n * All redistributions must retain an intact copy of this copyright notice\n * and disclaimer.\n */\n// (public) Constructor\nfunction BigInteger(a, b) {\n  if (a != null) this.fromString(a, b);\n}\n// return new, unset BigInteger\nfunction nbi() {\n  return new BigInteger(null, null);\n}\n// Bits per digit\nlet dbits;\n// JavaScript engine analysis\nconst canary = 0xdeadbeefcafe;\nconst j_lm = (canary & 0xffffff) === 0xefcafe;\n// am: Compute w_j += (x*this_i), propagate carries,\n// c is initial carry, returns final carry.\n// c < 3*dvalue, x < 2*dvalue, this_i < dvalue\n// We need to select the fastest one that works in this environment.\n// am1: use a single mult and divide to get the high bits,\n// max digit bits should be 26 because\n// max internal value = 2*dvalue^2-2*dvalue (< 2^53)\nfunction am1(i, x, w, j, c, n) {\n  while (--n >= 0) {\n    const v = x * this[i++] + w[j] + c;\n    c = Math.floor(v / 0x4000000);\n    w[j++] = v & 0x3ffffff;\n  }\n  return c;\n}\n// am2 avoids a big mult-and-extract completely.\n// Max digit bits should be <= 30 because we do bitwise ops\n// on values up to 2*hdvalue^2-hdvalue-1 (< 2^31)\nfunction am2(i, x, w, j, c, n) {\n  const xl = x & 0x7fff;\n  const xh = x >> 15;\n  while (--n >= 0) {\n    let l = this[i] & 0x7fff;\n    const h = this[i++] >> 15;\n    const m = xh * l + h * xl;\n    l = xl * l + ((m & 0x7fff) << 15) + w[j] + (c & 0x3fffffff);\n    c = (l >>> 30) + (m >>> 15) + xh * h + (c >>> 30);\n    w[j++] = l & 0x3fffffff;\n  }\n  return c;\n}\n// Alternately, set max digit bits to 28 since some\n// browsers slow down when dealing with 32-bit numbers.\nfunction am3(i, x, w, j, c, n) {\n  const xl = x & 0x3fff;\n  const xh = x >> 14;\n  while (--n >= 0) {\n    let l = this[i] & 0x3fff;\n    const h = this[i++] >> 14;\n    const m = xh * l + h * xl;\n    l = xl * l + ((m & 0x3fff) << 14) + w[j] + c;\n    c = (l >> 28) + (m >> 14) + xh * h;\n    w[j++] = l & 0xfffffff;\n  }\n  return c;\n}\nconst inBrowser = typeof navigator !== 'undefined';\nif (inBrowser && j_lm && navigator.appName === 'Microsoft Internet Explorer') {\n  BigInteger.prototype.am = am2;\n  dbits = 30;\n} else if (inBrowser && j_lm && navigator.appName !== 'Netscape') {\n  BigInteger.prototype.am = am1;\n  dbits = 26;\n} else {\n  // Mozilla/Netscape seems to prefer am3\n  BigInteger.prototype.am = am3;\n  dbits = 28;\n}\nBigInteger.prototype.DB = dbits;\nBigInteger.prototype.DM = (1 << dbits) - 1;\nBigInteger.prototype.DV = 1 << dbits;\nconst BI_FP = 52;\nBigInteger.prototype.FV = Math.pow(2, BI_FP);\nBigInteger.prototype.F1 = BI_FP - dbits;\nBigInteger.prototype.F2 = 2 * dbits - BI_FP;\n// Digit conversions\nconst BI_RM = '0123456789abcdefghijklmnopqrstuvwxyz';\nconst BI_RC = [];\nlet rr, vv;\nrr = '0'.charCodeAt(0);\nfor (vv = 0; vv <= 9; ++vv) BI_RC[rr++] = vv;\nrr = 'a'.charCodeAt(0);\nfor (vv = 10; vv < 36; ++vv) BI_RC[rr++] = vv;\nrr = 'A'.charCodeAt(0);\nfor (vv = 10; vv < 36; ++vv) BI_RC[rr++] = vv;\nfunction int2char(n) {\n  return BI_RM.charAt(n);\n}\nfunction intAt(s, i) {\n  const c = BI_RC[s.charCodeAt(i)];\n  return c == null ? -1 : c;\n}\n// (protected) copy this to r\nfunction bnpCopyTo(r) {\n  for (let i = this.t - 1; i >= 0; --i) r[i] = this[i];\n  r.t = this.t;\n  r.s = this.s;\n}\n// (protected) set from integer value x, -DV <= x < DV\nfunction bnpFromInt(x) {\n  this.t = 1;\n  this.s = x < 0 ? -1 : 0;\n  if (x > 0) this[0] = x;else if (x < -1) this[0] = x + this.DV;else this.t = 0;\n}\n// return bigint initialized to value\nfunction nbv(i) {\n  const r = nbi();\n  r.fromInt(i);\n  return r;\n}\n// (protected) set from string and radix\nfunction bnpFromString(s, b) {\n  let k;\n  if (b === 16) k = 4;else if (b === 8) k = 3;else if (b === 2) k = 1;else if (b === 32) k = 5;else if (b === 4) k = 2;else throw new Error('Only radix 2, 4, 8, 16, 32 are supported');\n  this.t = 0;\n  this.s = 0;\n  let i = s.length;\n  let mi = false;\n  let sh = 0;\n  while (--i >= 0) {\n    const x = intAt(s, i);\n    if (x < 0) {\n      if (s.charAt(i) === '-') mi = true;\n      continue;\n    }\n    mi = false;\n    if (sh === 0) this[this.t++] = x;else if (sh + k > this.DB) {\n      this[this.t - 1] |= (x & (1 << this.DB - sh) - 1) << sh;\n      this[this.t++] = x >> this.DB - sh;\n    } else this[this.t - 1] |= x << sh;\n    sh += k;\n    if (sh >= this.DB) sh -= this.DB;\n  }\n  this.clamp();\n  if (mi) BigInteger.ZERO.subTo(this, this);\n}\n// (protected) clamp off excess high words\nfunction bnpClamp() {\n  const c = this.s & this.DM;\n  while (this.t > 0 && this[this.t - 1] == c) --this.t;\n}\n// (public) return string representation in given radix\nfunction bnToString(b) {\n  if (this.s < 0) return '-' + this.negate().toString(b);\n  let k;\n  if (b == 16) k = 4;else if (b === 8) k = 3;else if (b === 2) k = 1;else if (b === 32) k = 5;else if (b === 4) k = 2;else throw new Error('Only radix 2, 4, 8, 16, 32 are supported');\n  const km = (1 << k) - 1;\n  let d;\n  let m = false;\n  let r = '';\n  let i = this.t;\n  let p = this.DB - i * this.DB % k;\n  if (i-- > 0) {\n    if (p < this.DB && (d = this[i] >> p) > 0) {\n      m = true;\n      r = int2char(d);\n    }\n    while (i >= 0) {\n      if (p < k) {\n        d = (this[i] & (1 << p) - 1) << k - p;\n        d |= this[--i] >> (p += this.DB - k);\n      } else {\n        d = this[i] >> (p -= k) & km;\n        if (p <= 0) {\n          p += this.DB;\n          --i;\n        }\n      }\n      if (d > 0) m = true;\n      if (m) r += int2char(d);\n    }\n  }\n  return m ? r : '0';\n}\n// (public) -this\nfunction bnNegate() {\n  const r = nbi();\n  BigInteger.ZERO.subTo(this, r);\n  return r;\n}\n// (public) |this|\nfunction bnAbs() {\n  return this.s < 0 ? this.negate() : this;\n}\n// (public) return + if this > a, - if this < a, 0 if equal\nfunction bnCompareTo(a) {\n  let r = this.s - a.s;\n  if (r != 0) return r;\n  let i = this.t;\n  r = i - a.t;\n  if (r != 0) return this.s < 0 ? -r : r;\n  while (--i >= 0) if ((r = this[i] - a[i]) != 0) return r;\n  return 0;\n}\n// returns bit length of the integer x\nfunction nbits(x) {\n  let r = 1;\n  let t;\n  if ((t = x >>> 16) !== 0) {\n    x = t;\n    r += 16;\n  }\n  if ((t = x >> 8) !== 0) {\n    x = t;\n    r += 8;\n  }\n  if ((t = x >> 4) !== 0) {\n    x = t;\n    r += 4;\n  }\n  if ((t = x >> 2) !== 0) {\n    x = t;\n    r += 2;\n  }\n  if ((t = x >> 1) !== 0) {\n    x = t;\n    r += 1;\n  }\n  return r;\n}\n// (public) return the number of bits in \"this\"\nfunction bnBitLength() {\n  if (this.t <= 0) return 0;\n  return this.DB * (this.t - 1) + nbits(this[this.t - 1] ^ this.s & this.DM);\n}\n// (protected) r = this << n*DB\nfunction bnpDLShiftTo(n, r) {\n  let i;\n  for (i = this.t - 1; i >= 0; --i) r[i + n] = this[i];\n  for (i = n - 1; i >= 0; --i) r[i] = 0;\n  r.t = this.t + n;\n  r.s = this.s;\n}\n// (protected) r = this >> n*DB\nfunction bnpDRShiftTo(n, r) {\n  for (let i = n; i < this.t; ++i) r[i - n] = this[i];\n  r.t = Math.max(this.t - n, 0);\n  r.s = this.s;\n}\n// (protected) r = this << n\nfunction bnpLShiftTo(n, r) {\n  const bs = n % this.DB;\n  const cbs = this.DB - bs;\n  const bm = (1 << cbs) - 1;\n  const ds = Math.floor(n / this.DB);\n  let c = this.s << bs & this.DM;\n  let i;\n  for (i = this.t - 1; i >= 0; --i) {\n    r[i + ds + 1] = this[i] >> cbs | c;\n    c = (this[i] & bm) << bs;\n  }\n  for (i = ds - 1; i >= 0; --i) r[i] = 0;\n  r[ds] = c;\n  r.t = this.t + ds + 1;\n  r.s = this.s;\n  r.clamp();\n}\n// (protected) r = this >> n\nfunction bnpRShiftTo(n, r) {\n  r.s = this.s;\n  const ds = Math.floor(n / this.DB);\n  if (ds >= this.t) {\n    r.t = 0;\n    return;\n  }\n  const bs = n % this.DB;\n  const cbs = this.DB - bs;\n  const bm = (1 << bs) - 1;\n  r[0] = this[ds] >> bs;\n  for (let i = ds + 1; i < this.t; ++i) {\n    r[i - ds - 1] |= (this[i] & bm) << cbs;\n    r[i - ds] = this[i] >> bs;\n  }\n  if (bs > 0) r[this.t - ds - 1] |= (this.s & bm) << cbs;\n  r.t = this.t - ds;\n  r.clamp();\n}\n// (protected) r = this - a\nfunction bnpSubTo(a, r) {\n  let i = 0;\n  let c = 0;\n  const m = Math.min(a.t, this.t);\n  while (i < m) {\n    c += this[i] - a[i];\n    r[i++] = c & this.DM;\n    c >>= this.DB;\n  }\n  if (a.t < this.t) {\n    c -= a.s;\n    while (i < this.t) {\n      c += this[i];\n      r[i++] = c & this.DM;\n      c >>= this.DB;\n    }\n    c += this.s;\n  } else {\n    c += this.s;\n    while (i < a.t) {\n      c -= a[i];\n      r[i++] = c & this.DM;\n      c >>= this.DB;\n    }\n    c -= a.s;\n  }\n  r.s = c < 0 ? -1 : 0;\n  if (c < -1) r[i++] = this.DV + c;else if (c > 0) r[i++] = c;\n  r.t = i;\n  r.clamp();\n}\n// (protected) r = this * a, r != this,a (HAC 14.12)\n// \"this\" should be the larger one if appropriate.\nfunction bnpMultiplyTo(a, r) {\n  const x = this.abs();\n  const y = a.abs();\n  let i = x.t;\n  r.t = i + y.t;\n  while (--i >= 0) r[i] = 0;\n  for (i = 0; i < y.t; ++i) r[i + x.t] = x.am(0, y[i], r, i, 0, x.t);\n  r.s = 0;\n  r.clamp();\n  if (this.s !== a.s) BigInteger.ZERO.subTo(r, r);\n}\n// (protected) r = this^2, r != this (HAC 14.16)\nfunction bnpSquareTo(r) {\n  const x = this.abs();\n  let i = r.t = 2 * x.t;\n  while (--i >= 0) r[i] = 0;\n  for (i = 0; i < x.t - 1; ++i) {\n    const c = x.am(i, x[i], r, 2 * i, 0, 1);\n    if ((r[i + x.t] += x.am(i + 1, 2 * x[i], r, 2 * i + 1, c, x.t - i - 1)) >= x.DV) {\n      r[i + x.t] -= x.DV;\n      r[i + x.t + 1] = 1;\n    }\n  }\n  if (r.t > 0) r[r.t - 1] += x.am(i, x[i], r, 2 * i, 0, 1);\n  r.s = 0;\n  r.clamp();\n}\n// (protected) divide this by m, quotient and remainder to q, r (HAC 14.20)\n// r != q, this != m.  q or r may be null.\nfunction bnpDivRemTo(m, q, r) {\n  const pm = m.abs();\n  if (pm.t <= 0) return;\n  const pt = this.abs();\n  if (pt.t < pm.t) {\n    if (q != null) q.fromInt(0);\n    if (r != null) this.copyTo(r);\n    return;\n  }\n  if (r === null) r = nbi();\n  const y = nbi();\n  const ts = this.s;\n  const ms = m.s;\n  const nsh = this.DB - nbits(pm[pm.t - 1]);\n  // normalize modulus\n  if (nsh > 0) {\n    pm.lShiftTo(nsh, y);\n    pt.lShiftTo(nsh, r);\n  } else {\n    pm.copyTo(y);\n    pt.copyTo(r);\n  }\n  const ys = y.t;\n  const y0 = y[ys - 1];\n  if (y0 === 0) return;\n  const yt = y0 * (1 << this.F1) + (ys > 1 ? y[ys - 2] >> this.F2 : 0);\n  const d1 = this.FV / yt;\n  const d2 = (1 << this.F1) / yt;\n  const e = 1 << this.F2;\n  let i = r.t;\n  let j = i - ys;\n  const t = q === null ? nbi() : q;\n  y.dlShiftTo(j, t);\n  if (r.compareTo(t) >= 0) {\n    r[r.t++] = 1;\n    r.subTo(t, r);\n  }\n  BigInteger.ONE.dlShiftTo(ys, t);\n  t.subTo(y, y);\n  // \"negative\" y so we can replace sub with am later\n  while (y.t < ys) y[y.t++] = 0;\n  while (--j >= 0) {\n    // Estimate quotient digit\n    let qd = r[--i] === y0 ? this.DM : Math.floor(r[i] * d1 + (r[i - 1] + e) * d2);\n    if ((r[i] += y.am(0, qd, r, j, 0, ys)) < qd) {\n      // Try it out\n      y.dlShiftTo(j, t);\n      r.subTo(t, r);\n      while (r[i] < --qd) r.subTo(t, r);\n    }\n  }\n  if (q !== null) {\n    r.drShiftTo(ys, q);\n    if (ts !== ms) BigInteger.ZERO.subTo(q, q);\n  }\n  r.t = ys;\n  r.clamp();\n  if (nsh > 0) r.rShiftTo(nsh, r);\n  // Denormalize remainder\n  if (ts < 0) BigInteger.ZERO.subTo(r, r);\n}\n// (public) this mod a\nfunction bnMod(a) {\n  const r = nbi();\n  this.abs().divRemTo(a, null, r);\n  if (this.s < 0 && r.compareTo(BigInteger.ZERO) > 0) a.subTo(r, r);\n  return r;\n}\n// (protected) return \"-1/this % 2^DB\"; useful for Mont. reduction\n// justification:\n//         xy == 1 (mod m)\n//         xy =  1+km\n//   xy(2-xy) = (1+km)(1-km)\n// x[y(2-xy)] = 1-k^2m^2\n// x[y(2-xy)] == 1 (mod m^2)\n// if y is 1/x mod m, then y(2-xy) is 1/x mod m^2\n// should reduce x and y(2-xy) by m^2 at each step to keep size bounded.\n// JS multiply \"overflows\" differently from C/C++, so care is needed here.\nfunction bnpInvDigit() {\n  if (this.t < 1) return 0;\n  const x = this[0];\n  if ((x & 1) === 0) return 0;\n  let y = x & 3;\n  // y == 1/x mod 2^2\n  y = y * (2 - (x & 0xf) * y) & 0xf;\n  // y == 1/x mod 2^4\n  y = y * (2 - (x & 0xff) * y) & 0xff;\n  // y == 1/x mod 2^8\n  y = y * (2 - ((x & 0xffff) * y & 0xffff)) & 0xffff;\n  // y == 1/x mod 2^16\n  // last step - calculate inverse mod DV directly;\n  // assumes 16 < DB <= 32 and assumes ability to handle 48-bit ints\n  y = y * (2 - x * y % this.DV) % this.DV;\n  // y == 1/x mod 2^dbits\n  // we really want the negative inverse, and -DV < y < DV\n  return y > 0 ? this.DV - y : -y;\n}\nfunction bnEquals(a) {\n  return this.compareTo(a) === 0;\n}\n// (protected) r = this + a\nfunction bnpAddTo(a, r) {\n  let i = 0;\n  let c = 0;\n  const m = Math.min(a.t, this.t);\n  while (i < m) {\n    c += this[i] + a[i];\n    r[i++] = c & this.DM;\n    c >>= this.DB;\n  }\n  if (a.t < this.t) {\n    c += a.s;\n    while (i < this.t) {\n      c += this[i];\n      r[i++] = c & this.DM;\n      c >>= this.DB;\n    }\n    c += this.s;\n  } else {\n    c += this.s;\n    while (i < a.t) {\n      c += a[i];\n      r[i++] = c & this.DM;\n      c >>= this.DB;\n    }\n    c += a.s;\n  }\n  r.s = c < 0 ? -1 : 0;\n  if (c > 0) r[i++] = c;else if (c < -1) r[i++] = this.DV + c;\n  r.t = i;\n  r.clamp();\n}\n// (public) this + a\nfunction bnAdd(a) {\n  const r = nbi();\n  this.addTo(a, r);\n  return r;\n}\n// (public) this - a\nfunction bnSubtract(a) {\n  const r = nbi();\n  this.subTo(a, r);\n  return r;\n}\n// (public) this * a\nfunction bnMultiply(a) {\n  const r = nbi();\n  this.multiplyTo(a, r);\n  return r;\n}\n// (public) this / a\nfunction bnDivide(a) {\n  const r = nbi();\n  this.divRemTo(a, r, null);\n  return r;\n}\n// Montgomery reduction\nfunction Montgomery(m) {\n  this.m = m;\n  this.mp = m.invDigit();\n  this.mpl = this.mp & 0x7fff;\n  this.mph = this.mp >> 15;\n  this.um = (1 << m.DB - 15) - 1;\n  this.mt2 = 2 * m.t;\n}\n// xR mod m\nfunction montConvert(x) {\n  const r = nbi();\n  x.abs().dlShiftTo(this.m.t, r);\n  r.divRemTo(this.m, null, r);\n  if (x.s < 0 && r.compareTo(BigInteger.ZERO) > 0) this.m.subTo(r, r);\n  return r;\n}\n// x/R mod m\nfunction montRevert(x) {\n  const r = nbi();\n  x.copyTo(r);\n  this.reduce(r);\n  return r;\n}\n// x = x/R mod m (HAC 14.32)\nfunction montReduce(x) {\n  while (x.t <= this.mt2)\n  // pad x so am has enough room later\n  x[x.t++] = 0;\n  for (let i = 0; i < this.m.t; ++i) {\n    // faster way of calculating u0 = x[i]*mp mod DV\n    let j = x[i] & 0x7fff;\n    const u0 = j * this.mpl + ((j * this.mph + (x[i] >> 15) * this.mpl & this.um) << 15) & x.DM;\n    // use am to combine the multiply-shift-add into one call\n    j = i + this.m.t;\n    x[j] += this.m.am(0, u0, x, i, 0, this.m.t);\n    // propagate carry\n    while (x[j] >= x.DV) {\n      x[j] -= x.DV;\n      x[++j]++;\n    }\n  }\n  x.clamp();\n  x.drShiftTo(this.m.t, x);\n  if (x.compareTo(this.m) >= 0) x.subTo(this.m, x);\n}\n// r = \"x^2/R mod m\"; x != r\nfunction montSqrTo(x, r) {\n  x.squareTo(r);\n  this.reduce(r);\n}\n// r = \"xy/R mod m\"; x,y != r\nfunction montMulTo(x, y, r) {\n  x.multiplyTo(y, r);\n  this.reduce(r);\n}\nMontgomery.prototype.convert = montConvert;\nMontgomery.prototype.revert = montRevert;\nMontgomery.prototype.reduce = montReduce;\nMontgomery.prototype.mulTo = montMulTo;\nMontgomery.prototype.sqrTo = montSqrTo;\n// (public) this^e % m (HAC 14.85)\nfunction bnModPow(e, m, callback) {\n  let i = e.bitLength();\n  let k;\n  let r = nbv(1);\n  const z = new Montgomery(m);\n  if (i <= 0) return r;else if (i < 18) k = 1;else if (i < 48) k = 3;else if (i < 144) k = 4;else if (i < 768) k = 5;else k = 6;\n  // precomputation\n  const g = [];\n  let n = 3;\n  const k1 = k - 1;\n  const km = (1 << k) - 1;\n  g[1] = z.convert(this);\n  if (k > 1) {\n    const g2 = nbi();\n    z.sqrTo(g[1], g2);\n    while (n <= km) {\n      g[n] = nbi();\n      z.mulTo(g2, g[n - 2], g[n]);\n      n += 2;\n    }\n  }\n  let j = e.t - 1;\n  let w;\n  let is1 = true;\n  let r2 = nbi();\n  let t;\n  i = nbits(e[j]) - 1;\n  while (j >= 0) {\n    if (i >= k1) w = e[j] >> i - k1 & km;else {\n      w = (e[j] & (1 << i + 1) - 1) << k1 - i;\n      if (j > 0) w |= e[j - 1] >> this.DB + i - k1;\n    }\n    n = k;\n    while ((w & 1) === 0) {\n      w >>= 1;\n      --n;\n    }\n    if ((i -= n) < 0) {\n      i += this.DB;\n      --j;\n    }\n    if (is1) {\n      // ret == 1, don't bother squaring or multiplying it\n      g[w].copyTo(r);\n      is1 = false;\n    } else {\n      while (n > 1) {\n        z.sqrTo(r, r2);\n        z.sqrTo(r2, r);\n        n -= 2;\n      }\n      if (n > 0) z.sqrTo(r, r2);else {\n        t = r;\n        r = r2;\n        r2 = t;\n      }\n      z.mulTo(r2, g[w], r);\n    }\n    while (j >= 0 && (e[j] & 1 << i) === 0) {\n      z.sqrTo(r, r2);\n      t = r;\n      r = r2;\n      r2 = t;\n      if (--i < 0) {\n        i = this.DB - 1;\n        --j;\n      }\n    }\n  }\n  const result = z.revert(r);\n  callback(null, result);\n  return result;\n}\n// protected\nBigInteger.prototype.copyTo = bnpCopyTo;\nBigInteger.prototype.fromInt = bnpFromInt;\nBigInteger.prototype.fromString = bnpFromString;\nBigInteger.prototype.clamp = bnpClamp;\nBigInteger.prototype.dlShiftTo = bnpDLShiftTo;\nBigInteger.prototype.drShiftTo = bnpDRShiftTo;\nBigInteger.prototype.lShiftTo = bnpLShiftTo;\nBigInteger.prototype.rShiftTo = bnpRShiftTo;\nBigInteger.prototype.subTo = bnpSubTo;\nBigInteger.prototype.multiplyTo = bnpMultiplyTo;\nBigInteger.prototype.squareTo = bnpSquareTo;\nBigInteger.prototype.divRemTo = bnpDivRemTo;\nBigInteger.prototype.invDigit = bnpInvDigit;\nBigInteger.prototype.addTo = bnpAddTo;\n// public\nBigInteger.prototype.toString = bnToString;\nBigInteger.prototype.negate = bnNegate;\nBigInteger.prototype.abs = bnAbs;\nBigInteger.prototype.compareTo = bnCompareTo;\nBigInteger.prototype.bitLength = bnBitLength;\nBigInteger.prototype.mod = bnMod;\nBigInteger.prototype.equals = bnEquals;\nBigInteger.prototype.add = bnAdd;\nBigInteger.prototype.subtract = bnSubtract;\nBigInteger.prototype.multiply = bnMultiply;\nBigInteger.prototype.divide = bnDivide;\nBigInteger.prototype.modPow = bnModPow;\n// \"constants\"\nBigInteger.ZERO = nbv(0);\nBigInteger.ONE = nbv(1);\nexport { BigInteger as default };", "map": {"version": 3, "names": ["BigInteger", "a", "b", "fromString", "nbi", "dbits", "canary", "j_lm", "am1", "i", "x", "w", "j", "c", "n", "v", "Math", "floor", "am2", "xl", "xh", "l", "h", "m", "am3", "inBrowser", "navigator", "appName", "prototype", "am", "DB", "DM", "DV", "BI_FP", "FV", "pow", "F1", "F2", "BI_RM", "BI_RC", "rr", "vv", "charCodeAt", "int2char", "char<PERSON>t", "intAt", "s", "bnpCopyTo", "r", "t", "bnpFromInt", "nbv", "fromInt", "bnpFromString", "k", "Error", "length", "mi", "sh", "clamp", "ZERO", "subTo", "bnpClamp", "bnToString", "negate", "toString", "km", "d", "p", "bnNegate", "bnAbs", "bnCompareTo", "nbits", "bnBitLength", "bnpDLShiftTo", "bnpDRShiftTo", "max", "bnpLShiftTo", "bs", "cbs", "bm", "ds", "bnpRShiftTo", "bnpSubTo", "min", "bnpMultiplyTo", "abs", "y", "bnpSquareTo", "bnpDivRemTo", "q", "pm", "pt", "copyTo", "ts", "ms", "nsh", "lShiftTo", "ys", "y0", "yt", "d1", "d2", "e", "dlShiftTo", "compareTo", "ONE", "qd", "drShiftTo", "rShiftTo", "bnMod", "divRemTo", "bnpInvDigit", "bnEquals", "bnpAddTo", "bnAdd", "addTo", "bnSubtract", "bnMultiply", "multiplyTo", "bnDivide", "<PERSON>", "mp", "invDigit", "mpl", "mph", "um", "mt2", "montConvert", "montRevert", "reduce", "montReduce", "u0", "montSqrTo", "squareTo", "montMulTo", "convert", "revert", "mulTo", "sqrTo", "bnModPow", "callback", "bitLength", "z", "g", "k1", "g2", "is1", "r2", "result", "mod", "equals", "add", "subtract", "multiply", "divide", "modPow", "default"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/srp/BigInteger/BigInteger.mjs"], "sourcesContent": ["/* eslint-disable */\n// @ts-nocheck -> BigInteger is already a vended utility\n// A small implementation of BigInteger based on http://www-cs-students.stanford.edu/~tjw/jsbn/\n//\n// All public methods have been removed except the following:\n//   new BigInteger(a, b) (only radix 2, 4, 8, 16 and 32 supported)\n//   toString (only radix 2, 4, 8, 16 and 32 supported)\n//   negate\n//   abs\n//   compareTo\n//   bitLength\n//   mod\n//   equals\n//   add\n//   subtract\n//   multiply\n//   divide\n//   modPow\n/*\n * Copyright (c) 2003-2005  Tom Wu\n * All Rights Reserved.\n *\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * \"Software\"), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n *\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS-IS\" AND WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS, IMPLIED OR OTHERWISE, INCLUDING WITHOUT LIMITATION, ANY\n * WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.\n *\n * IN NO EVENT SHALL TOM WU BE LIABLE FOR ANY SPECIAL, INCIDENTAL,\n * INDIRECT OR CONSEQUENTIAL DAMAGES OF ANY KIND, OR ANY DAMAGES WHATSOEVER\n * RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER OR NOT ADVISED OF\n * THE POSSIBILITY OF DAMAGE, AND ON ANY THEORY OF LIABILITY, ARISING OUT\n * OF OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n *\n * In addition, the following condition applies:\n *\n * All redistributions must retain an intact copy of this copyright notice\n * and disclaimer.\n */\n// (public) Constructor\nfunction BigInteger(a, b) {\n    if (a != null)\n        this.fromString(a, b);\n}\n// return new, unset BigInteger\nfunction nbi() {\n    return new BigInteger(null, null);\n}\n// Bits per digit\nlet dbits;\n// JavaScript engine analysis\nconst canary = 0xdeadbeefcafe;\nconst j_lm = (canary & 0xffffff) === 0xefcafe;\n// am: Compute w_j += (x*this_i), propagate carries,\n// c is initial carry, returns final carry.\n// c < 3*dvalue, x < 2*dvalue, this_i < dvalue\n// We need to select the fastest one that works in this environment.\n// am1: use a single mult and divide to get the high bits,\n// max digit bits should be 26 because\n// max internal value = 2*dvalue^2-2*dvalue (< 2^53)\nfunction am1(i, x, w, j, c, n) {\n    while (--n >= 0) {\n        const v = x * this[i++] + w[j] + c;\n        c = Math.floor(v / 0x4000000);\n        w[j++] = v & 0x3ffffff;\n    }\n    return c;\n}\n// am2 avoids a big mult-and-extract completely.\n// Max digit bits should be <= 30 because we do bitwise ops\n// on values up to 2*hdvalue^2-hdvalue-1 (< 2^31)\nfunction am2(i, x, w, j, c, n) {\n    const xl = x & 0x7fff;\n    const xh = x >> 15;\n    while (--n >= 0) {\n        let l = this[i] & 0x7fff;\n        const h = this[i++] >> 15;\n        const m = xh * l + h * xl;\n        l = xl * l + ((m & 0x7fff) << 15) + w[j] + (c & 0x3fffffff);\n        c = (l >>> 30) + (m >>> 15) + xh * h + (c >>> 30);\n        w[j++] = l & 0x3fffffff;\n    }\n    return c;\n}\n// Alternately, set max digit bits to 28 since some\n// browsers slow down when dealing with 32-bit numbers.\nfunction am3(i, x, w, j, c, n) {\n    const xl = x & 0x3fff;\n    const xh = x >> 14;\n    while (--n >= 0) {\n        let l = this[i] & 0x3fff;\n        const h = this[i++] >> 14;\n        const m = xh * l + h * xl;\n        l = xl * l + ((m & 0x3fff) << 14) + w[j] + c;\n        c = (l >> 28) + (m >> 14) + xh * h;\n        w[j++] = l & 0xfffffff;\n    }\n    return c;\n}\nconst inBrowser = typeof navigator !== 'undefined';\nif (inBrowser && j_lm && navigator.appName === 'Microsoft Internet Explorer') {\n    BigInteger.prototype.am = am2;\n    dbits = 30;\n}\nelse if (inBrowser && j_lm && navigator.appName !== 'Netscape') {\n    BigInteger.prototype.am = am1;\n    dbits = 26;\n}\nelse {\n    // Mozilla/Netscape seems to prefer am3\n    BigInteger.prototype.am = am3;\n    dbits = 28;\n}\nBigInteger.prototype.DB = dbits;\nBigInteger.prototype.DM = (1 << dbits) - 1;\nBigInteger.prototype.DV = 1 << dbits;\nconst BI_FP = 52;\nBigInteger.prototype.FV = Math.pow(2, BI_FP);\nBigInteger.prototype.F1 = BI_FP - dbits;\nBigInteger.prototype.F2 = 2 * dbits - BI_FP;\n// Digit conversions\nconst BI_RM = '0123456789abcdefghijklmnopqrstuvwxyz';\nconst BI_RC = [];\nlet rr, vv;\nrr = '0'.charCodeAt(0);\nfor (vv = 0; vv <= 9; ++vv)\n    BI_RC[rr++] = vv;\nrr = 'a'.charCodeAt(0);\nfor (vv = 10; vv < 36; ++vv)\n    BI_RC[rr++] = vv;\nrr = 'A'.charCodeAt(0);\nfor (vv = 10; vv < 36; ++vv)\n    BI_RC[rr++] = vv;\nfunction int2char(n) {\n    return BI_RM.charAt(n);\n}\nfunction intAt(s, i) {\n    const c = BI_RC[s.charCodeAt(i)];\n    return c == null ? -1 : c;\n}\n// (protected) copy this to r\nfunction bnpCopyTo(r) {\n    for (let i = this.t - 1; i >= 0; --i)\n        r[i] = this[i];\n    r.t = this.t;\n    r.s = this.s;\n}\n// (protected) set from integer value x, -DV <= x < DV\nfunction bnpFromInt(x) {\n    this.t = 1;\n    this.s = x < 0 ? -1 : 0;\n    if (x > 0)\n        this[0] = x;\n    else if (x < -1)\n        this[0] = x + this.DV;\n    else\n        this.t = 0;\n}\n// return bigint initialized to value\nfunction nbv(i) {\n    const r = nbi();\n    r.fromInt(i);\n    return r;\n}\n// (protected) set from string and radix\nfunction bnpFromString(s, b) {\n    let k;\n    if (b === 16)\n        k = 4;\n    else if (b === 8)\n        k = 3;\n    else if (b === 2)\n        k = 1;\n    else if (b === 32)\n        k = 5;\n    else if (b === 4)\n        k = 2;\n    else\n        throw new Error('Only radix 2, 4, 8, 16, 32 are supported');\n    this.t = 0;\n    this.s = 0;\n    let i = s.length;\n    let mi = false;\n    let sh = 0;\n    while (--i >= 0) {\n        const x = intAt(s, i);\n        if (x < 0) {\n            if (s.charAt(i) === '-')\n                mi = true;\n            continue;\n        }\n        mi = false;\n        if (sh === 0)\n            this[this.t++] = x;\n        else if (sh + k > this.DB) {\n            this[this.t - 1] |= (x & ((1 << (this.DB - sh)) - 1)) << sh;\n            this[this.t++] = x >> (this.DB - sh);\n        }\n        else\n            this[this.t - 1] |= x << sh;\n        sh += k;\n        if (sh >= this.DB)\n            sh -= this.DB;\n    }\n    this.clamp();\n    if (mi)\n        BigInteger.ZERO.subTo(this, this);\n}\n// (protected) clamp off excess high words\nfunction bnpClamp() {\n    const c = this.s & this.DM;\n    while (this.t > 0 && this[this.t - 1] == c)\n        --this.t;\n}\n// (public) return string representation in given radix\nfunction bnToString(b) {\n    if (this.s < 0)\n        return '-' + this.negate().toString(b);\n    let k;\n    if (b == 16)\n        k = 4;\n    else if (b === 8)\n        k = 3;\n    else if (b === 2)\n        k = 1;\n    else if (b === 32)\n        k = 5;\n    else if (b === 4)\n        k = 2;\n    else\n        throw new Error('Only radix 2, 4, 8, 16, 32 are supported');\n    const km = (1 << k) - 1;\n    let d;\n    let m = false;\n    let r = '';\n    let i = this.t;\n    let p = this.DB - ((i * this.DB) % k);\n    if (i-- > 0) {\n        if (p < this.DB && (d = this[i] >> p) > 0) {\n            m = true;\n            r = int2char(d);\n        }\n        while (i >= 0) {\n            if (p < k) {\n                d = (this[i] & ((1 << p) - 1)) << (k - p);\n                d |= this[--i] >> (p += this.DB - k);\n            }\n            else {\n                d = (this[i] >> (p -= k)) & km;\n                if (p <= 0) {\n                    p += this.DB;\n                    --i;\n                }\n            }\n            if (d > 0)\n                m = true;\n            if (m)\n                r += int2char(d);\n        }\n    }\n    return m ? r : '0';\n}\n// (public) -this\nfunction bnNegate() {\n    const r = nbi();\n    BigInteger.ZERO.subTo(this, r);\n    return r;\n}\n// (public) |this|\nfunction bnAbs() {\n    return this.s < 0 ? this.negate() : this;\n}\n// (public) return + if this > a, - if this < a, 0 if equal\nfunction bnCompareTo(a) {\n    let r = this.s - a.s;\n    if (r != 0)\n        return r;\n    let i = this.t;\n    r = i - a.t;\n    if (r != 0)\n        return this.s < 0 ? -r : r;\n    while (--i >= 0)\n        if ((r = this[i] - a[i]) != 0)\n            return r;\n    return 0;\n}\n// returns bit length of the integer x\nfunction nbits(x) {\n    let r = 1;\n    let t;\n    if ((t = x >>> 16) !== 0) {\n        x = t;\n        r += 16;\n    }\n    if ((t = x >> 8) !== 0) {\n        x = t;\n        r += 8;\n    }\n    if ((t = x >> 4) !== 0) {\n        x = t;\n        r += 4;\n    }\n    if ((t = x >> 2) !== 0) {\n        x = t;\n        r += 2;\n    }\n    if ((t = x >> 1) !== 0) {\n        x = t;\n        r += 1;\n    }\n    return r;\n}\n// (public) return the number of bits in \"this\"\nfunction bnBitLength() {\n    if (this.t <= 0)\n        return 0;\n    return this.DB * (this.t - 1) + nbits(this[this.t - 1] ^ (this.s & this.DM));\n}\n// (protected) r = this << n*DB\nfunction bnpDLShiftTo(n, r) {\n    let i;\n    for (i = this.t - 1; i >= 0; --i)\n        r[i + n] = this[i];\n    for (i = n - 1; i >= 0; --i)\n        r[i] = 0;\n    r.t = this.t + n;\n    r.s = this.s;\n}\n// (protected) r = this >> n*DB\nfunction bnpDRShiftTo(n, r) {\n    for (let i = n; i < this.t; ++i)\n        r[i - n] = this[i];\n    r.t = Math.max(this.t - n, 0);\n    r.s = this.s;\n}\n// (protected) r = this << n\nfunction bnpLShiftTo(n, r) {\n    const bs = n % this.DB;\n    const cbs = this.DB - bs;\n    const bm = (1 << cbs) - 1;\n    const ds = Math.floor(n / this.DB);\n    let c = (this.s << bs) & this.DM;\n    let i;\n    for (i = this.t - 1; i >= 0; --i) {\n        r[i + ds + 1] = (this[i] >> cbs) | c;\n        c = (this[i] & bm) << bs;\n    }\n    for (i = ds - 1; i >= 0; --i)\n        r[i] = 0;\n    r[ds] = c;\n    r.t = this.t + ds + 1;\n    r.s = this.s;\n    r.clamp();\n}\n// (protected) r = this >> n\nfunction bnpRShiftTo(n, r) {\n    r.s = this.s;\n    const ds = Math.floor(n / this.DB);\n    if (ds >= this.t) {\n        r.t = 0;\n        return;\n    }\n    const bs = n % this.DB;\n    const cbs = this.DB - bs;\n    const bm = (1 << bs) - 1;\n    r[0] = this[ds] >> bs;\n    for (let i = ds + 1; i < this.t; ++i) {\n        r[i - ds - 1] |= (this[i] & bm) << cbs;\n        r[i - ds] = this[i] >> bs;\n    }\n    if (bs > 0)\n        r[this.t - ds - 1] |= (this.s & bm) << cbs;\n    r.t = this.t - ds;\n    r.clamp();\n}\n// (protected) r = this - a\nfunction bnpSubTo(a, r) {\n    let i = 0;\n    let c = 0;\n    const m = Math.min(a.t, this.t);\n    while (i < m) {\n        c += this[i] - a[i];\n        r[i++] = c & this.DM;\n        c >>= this.DB;\n    }\n    if (a.t < this.t) {\n        c -= a.s;\n        while (i < this.t) {\n            c += this[i];\n            r[i++] = c & this.DM;\n            c >>= this.DB;\n        }\n        c += this.s;\n    }\n    else {\n        c += this.s;\n        while (i < a.t) {\n            c -= a[i];\n            r[i++] = c & this.DM;\n            c >>= this.DB;\n        }\n        c -= a.s;\n    }\n    r.s = c < 0 ? -1 : 0;\n    if (c < -1)\n        r[i++] = this.DV + c;\n    else if (c > 0)\n        r[i++] = c;\n    r.t = i;\n    r.clamp();\n}\n// (protected) r = this * a, r != this,a (HAC 14.12)\n// \"this\" should be the larger one if appropriate.\nfunction bnpMultiplyTo(a, r) {\n    const x = this.abs();\n    const y = a.abs();\n    let i = x.t;\n    r.t = i + y.t;\n    while (--i >= 0)\n        r[i] = 0;\n    for (i = 0; i < y.t; ++i)\n        r[i + x.t] = x.am(0, y[i], r, i, 0, x.t);\n    r.s = 0;\n    r.clamp();\n    if (this.s !== a.s)\n        BigInteger.ZERO.subTo(r, r);\n}\n// (protected) r = this^2, r != this (HAC 14.16)\nfunction bnpSquareTo(r) {\n    const x = this.abs();\n    let i = (r.t = 2 * x.t);\n    while (--i >= 0)\n        r[i] = 0;\n    for (i = 0; i < x.t - 1; ++i) {\n        const c = x.am(i, x[i], r, 2 * i, 0, 1);\n        if ((r[i + x.t] += x.am(i + 1, 2 * x[i], r, 2 * i + 1, c, x.t - i - 1)) >=\n            x.DV) {\n            r[i + x.t] -= x.DV;\n            r[i + x.t + 1] = 1;\n        }\n    }\n    if (r.t > 0)\n        r[r.t - 1] += x.am(i, x[i], r, 2 * i, 0, 1);\n    r.s = 0;\n    r.clamp();\n}\n// (protected) divide this by m, quotient and remainder to q, r (HAC 14.20)\n// r != q, this != m.  q or r may be null.\nfunction bnpDivRemTo(m, q, r) {\n    const pm = m.abs();\n    if (pm.t <= 0)\n        return;\n    const pt = this.abs();\n    if (pt.t < pm.t) {\n        if (q != null)\n            q.fromInt(0);\n        if (r != null)\n            this.copyTo(r);\n        return;\n    }\n    if (r === null)\n        r = nbi();\n    const y = nbi();\n    const ts = this.s;\n    const ms = m.s;\n    const nsh = this.DB - nbits(pm[pm.t - 1]);\n    // normalize modulus\n    if (nsh > 0) {\n        pm.lShiftTo(nsh, y);\n        pt.lShiftTo(nsh, r);\n    }\n    else {\n        pm.copyTo(y);\n        pt.copyTo(r);\n    }\n    const ys = y.t;\n    const y0 = y[ys - 1];\n    if (y0 === 0)\n        return;\n    const yt = y0 * (1 << this.F1) + (ys > 1 ? y[ys - 2] >> this.F2 : 0);\n    const d1 = this.FV / yt;\n    const d2 = (1 << this.F1) / yt;\n    const e = 1 << this.F2;\n    let i = r.t;\n    let j = i - ys;\n    const t = q === null ? nbi() : q;\n    y.dlShiftTo(j, t);\n    if (r.compareTo(t) >= 0) {\n        r[r.t++] = 1;\n        r.subTo(t, r);\n    }\n    BigInteger.ONE.dlShiftTo(ys, t);\n    t.subTo(y, y);\n    // \"negative\" y so we can replace sub with am later\n    while (y.t < ys)\n        y[y.t++] = 0;\n    while (--j >= 0) {\n        // Estimate quotient digit\n        let qd = r[--i] === y0 ? this.DM : Math.floor(r[i] * d1 + (r[i - 1] + e) * d2);\n        if ((r[i] += y.am(0, qd, r, j, 0, ys)) < qd) {\n            // Try it out\n            y.dlShiftTo(j, t);\n            r.subTo(t, r);\n            while (r[i] < --qd)\n                r.subTo(t, r);\n        }\n    }\n    if (q !== null) {\n        r.drShiftTo(ys, q);\n        if (ts !== ms)\n            BigInteger.ZERO.subTo(q, q);\n    }\n    r.t = ys;\n    r.clamp();\n    if (nsh > 0)\n        r.rShiftTo(nsh, r);\n    // Denormalize remainder\n    if (ts < 0)\n        BigInteger.ZERO.subTo(r, r);\n}\n// (public) this mod a\nfunction bnMod(a) {\n    const r = nbi();\n    this.abs().divRemTo(a, null, r);\n    if (this.s < 0 && r.compareTo(BigInteger.ZERO) > 0)\n        a.subTo(r, r);\n    return r;\n}\n// (protected) return \"-1/this % 2^DB\"; useful for Mont. reduction\n// justification:\n//         xy == 1 (mod m)\n//         xy =  1+km\n//   xy(2-xy) = (1+km)(1-km)\n// x[y(2-xy)] = 1-k^2m^2\n// x[y(2-xy)] == 1 (mod m^2)\n// if y is 1/x mod m, then y(2-xy) is 1/x mod m^2\n// should reduce x and y(2-xy) by m^2 at each step to keep size bounded.\n// JS multiply \"overflows\" differently from C/C++, so care is needed here.\nfunction bnpInvDigit() {\n    if (this.t < 1)\n        return 0;\n    const x = this[0];\n    if ((x & 1) === 0)\n        return 0;\n    let y = x & 3;\n    // y == 1/x mod 2^2\n    y = (y * (2 - (x & 0xf) * y)) & 0xf;\n    // y == 1/x mod 2^4\n    y = (y * (2 - (x & 0xff) * y)) & 0xff;\n    // y == 1/x mod 2^8\n    y = (y * (2 - (((x & 0xffff) * y) & 0xffff))) & 0xffff;\n    // y == 1/x mod 2^16\n    // last step - calculate inverse mod DV directly;\n    // assumes 16 < DB <= 32 and assumes ability to handle 48-bit ints\n    y = (y * (2 - ((x * y) % this.DV))) % this.DV;\n    // y == 1/x mod 2^dbits\n    // we really want the negative inverse, and -DV < y < DV\n    return y > 0 ? this.DV - y : -y;\n}\nfunction bnEquals(a) {\n    return this.compareTo(a) === 0;\n}\n// (protected) r = this + a\nfunction bnpAddTo(a, r) {\n    let i = 0;\n    let c = 0;\n    const m = Math.min(a.t, this.t);\n    while (i < m) {\n        c += this[i] + a[i];\n        r[i++] = c & this.DM;\n        c >>= this.DB;\n    }\n    if (a.t < this.t) {\n        c += a.s;\n        while (i < this.t) {\n            c += this[i];\n            r[i++] = c & this.DM;\n            c >>= this.DB;\n        }\n        c += this.s;\n    }\n    else {\n        c += this.s;\n        while (i < a.t) {\n            c += a[i];\n            r[i++] = c & this.DM;\n            c >>= this.DB;\n        }\n        c += a.s;\n    }\n    r.s = c < 0 ? -1 : 0;\n    if (c > 0)\n        r[i++] = c;\n    else if (c < -1)\n        r[i++] = this.DV + c;\n    r.t = i;\n    r.clamp();\n}\n// (public) this + a\nfunction bnAdd(a) {\n    const r = nbi();\n    this.addTo(a, r);\n    return r;\n}\n// (public) this - a\nfunction bnSubtract(a) {\n    const r = nbi();\n    this.subTo(a, r);\n    return r;\n}\n// (public) this * a\nfunction bnMultiply(a) {\n    const r = nbi();\n    this.multiplyTo(a, r);\n    return r;\n}\n// (public) this / a\nfunction bnDivide(a) {\n    const r = nbi();\n    this.divRemTo(a, r, null);\n    return r;\n}\n// Montgomery reduction\nfunction Montgomery(m) {\n    this.m = m;\n    this.mp = m.invDigit();\n    this.mpl = this.mp & 0x7fff;\n    this.mph = this.mp >> 15;\n    this.um = (1 << (m.DB - 15)) - 1;\n    this.mt2 = 2 * m.t;\n}\n// xR mod m\nfunction montConvert(x) {\n    const r = nbi();\n    x.abs().dlShiftTo(this.m.t, r);\n    r.divRemTo(this.m, null, r);\n    if (x.s < 0 && r.compareTo(BigInteger.ZERO) > 0)\n        this.m.subTo(r, r);\n    return r;\n}\n// x/R mod m\nfunction montRevert(x) {\n    const r = nbi();\n    x.copyTo(r);\n    this.reduce(r);\n    return r;\n}\n// x = x/R mod m (HAC 14.32)\nfunction montReduce(x) {\n    while (x.t <= this.mt2)\n        // pad x so am has enough room later\n        x[x.t++] = 0;\n    for (let i = 0; i < this.m.t; ++i) {\n        // faster way of calculating u0 = x[i]*mp mod DV\n        let j = x[i] & 0x7fff;\n        const u0 = (j * this.mpl +\n            (((j * this.mph + (x[i] >> 15) * this.mpl) & this.um) << 15)) &\n            x.DM;\n        // use am to combine the multiply-shift-add into one call\n        j = i + this.m.t;\n        x[j] += this.m.am(0, u0, x, i, 0, this.m.t);\n        // propagate carry\n        while (x[j] >= x.DV) {\n            x[j] -= x.DV;\n            x[++j]++;\n        }\n    }\n    x.clamp();\n    x.drShiftTo(this.m.t, x);\n    if (x.compareTo(this.m) >= 0)\n        x.subTo(this.m, x);\n}\n// r = \"x^2/R mod m\"; x != r\nfunction montSqrTo(x, r) {\n    x.squareTo(r);\n    this.reduce(r);\n}\n// r = \"xy/R mod m\"; x,y != r\nfunction montMulTo(x, y, r) {\n    x.multiplyTo(y, r);\n    this.reduce(r);\n}\nMontgomery.prototype.convert = montConvert;\nMontgomery.prototype.revert = montRevert;\nMontgomery.prototype.reduce = montReduce;\nMontgomery.prototype.mulTo = montMulTo;\nMontgomery.prototype.sqrTo = montSqrTo;\n// (public) this^e % m (HAC 14.85)\nfunction bnModPow(e, m, callback) {\n    let i = e.bitLength();\n    let k;\n    let r = nbv(1);\n    const z = new Montgomery(m);\n    if (i <= 0)\n        return r;\n    else if (i < 18)\n        k = 1;\n    else if (i < 48)\n        k = 3;\n    else if (i < 144)\n        k = 4;\n    else if (i < 768)\n        k = 5;\n    else\n        k = 6;\n    // precomputation\n    const g = [];\n    let n = 3;\n    const k1 = k - 1;\n    const km = (1 << k) - 1;\n    g[1] = z.convert(this);\n    if (k > 1) {\n        const g2 = nbi();\n        z.sqrTo(g[1], g2);\n        while (n <= km) {\n            g[n] = nbi();\n            z.mulTo(g2, g[n - 2], g[n]);\n            n += 2;\n        }\n    }\n    let j = e.t - 1;\n    let w;\n    let is1 = true;\n    let r2 = nbi();\n    let t;\n    i = nbits(e[j]) - 1;\n    while (j >= 0) {\n        if (i >= k1)\n            w = (e[j] >> (i - k1)) & km;\n        else {\n            w = (e[j] & ((1 << (i + 1)) - 1)) << (k1 - i);\n            if (j > 0)\n                w |= e[j - 1] >> (this.DB + i - k1);\n        }\n        n = k;\n        while ((w & 1) === 0) {\n            w >>= 1;\n            --n;\n        }\n        if ((i -= n) < 0) {\n            i += this.DB;\n            --j;\n        }\n        if (is1) {\n            // ret == 1, don't bother squaring or multiplying it\n            g[w].copyTo(r);\n            is1 = false;\n        }\n        else {\n            while (n > 1) {\n                z.sqrTo(r, r2);\n                z.sqrTo(r2, r);\n                n -= 2;\n            }\n            if (n > 0)\n                z.sqrTo(r, r2);\n            else {\n                t = r;\n                r = r2;\n                r2 = t;\n            }\n            z.mulTo(r2, g[w], r);\n        }\n        while (j >= 0 && (e[j] & (1 << i)) === 0) {\n            z.sqrTo(r, r2);\n            t = r;\n            r = r2;\n            r2 = t;\n            if (--i < 0) {\n                i = this.DB - 1;\n                --j;\n            }\n        }\n    }\n    const result = z.revert(r);\n    callback(null, result);\n    return result;\n}\n// protected\nBigInteger.prototype.copyTo = bnpCopyTo;\nBigInteger.prototype.fromInt = bnpFromInt;\nBigInteger.prototype.fromString = bnpFromString;\nBigInteger.prototype.clamp = bnpClamp;\nBigInteger.prototype.dlShiftTo = bnpDLShiftTo;\nBigInteger.prototype.drShiftTo = bnpDRShiftTo;\nBigInteger.prototype.lShiftTo = bnpLShiftTo;\nBigInteger.prototype.rShiftTo = bnpRShiftTo;\nBigInteger.prototype.subTo = bnpSubTo;\nBigInteger.prototype.multiplyTo = bnpMultiplyTo;\nBigInteger.prototype.squareTo = bnpSquareTo;\nBigInteger.prototype.divRemTo = bnpDivRemTo;\nBigInteger.prototype.invDigit = bnpInvDigit;\nBigInteger.prototype.addTo = bnpAddTo;\n// public\nBigInteger.prototype.toString = bnToString;\nBigInteger.prototype.negate = bnNegate;\nBigInteger.prototype.abs = bnAbs;\nBigInteger.prototype.compareTo = bnCompareTo;\nBigInteger.prototype.bitLength = bnBitLength;\nBigInteger.prototype.mod = bnMod;\nBigInteger.prototype.equals = bnEquals;\nBigInteger.prototype.add = bnAdd;\nBigInteger.prototype.subtract = bnSubtract;\nBigInteger.prototype.multiply = bnMultiply;\nBigInteger.prototype.divide = bnDivide;\nBigInteger.prototype.modPow = bnModPow;\n// \"constants\"\nBigInteger.ZERO = nbv(0);\nBigInteger.ONE = nbv(1);\n\nexport { BigInteger as default };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACtB,IAAID,CAAC,IAAI,IAAI,EACT,IAAI,CAACE,UAAU,CAACF,CAAC,EAAEC,CAAC,CAAC;AAC7B;AACA;AACA,SAASE,GAAGA,CAAA,EAAG;EACX,OAAO,IAAIJ,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC;AACrC;AACA;AACA,IAAIK,KAAK;AACT;AACA,MAAMC,MAAM,GAAG,cAAc;AAC7B,MAAMC,IAAI,GAAG,CAACD,MAAM,GAAG,QAAQ,MAAM,QAAQ;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC3B,OAAO,EAAEA,CAAC,IAAI,CAAC,EAAE;IACb,MAAMC,CAAC,GAAGL,CAAC,GAAG,IAAI,CAACD,CAAC,EAAE,CAAC,GAAGE,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC;IAClCA,CAAC,GAAGG,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,SAAS,CAAC;IAC7BJ,CAAC,CAACC,CAAC,EAAE,CAAC,GAAGG,CAAC,GAAG,SAAS;EAC1B;EACA,OAAOF,CAAC;AACZ;AACA;AACA;AACA;AACA,SAASK,GAAGA,CAACT,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC3B,MAAMK,EAAE,GAAGT,CAAC,GAAG,MAAM;EACrB,MAAMU,EAAE,GAAGV,CAAC,IAAI,EAAE;EAClB,OAAO,EAAEI,CAAC,IAAI,CAAC,EAAE;IACb,IAAIO,CAAC,GAAG,IAAI,CAACZ,CAAC,CAAC,GAAG,MAAM;IACxB,MAAMa,CAAC,GAAG,IAAI,CAACb,CAAC,EAAE,CAAC,IAAI,EAAE;IACzB,MAAMc,CAAC,GAAGH,EAAE,GAAGC,CAAC,GAAGC,CAAC,GAAGH,EAAE;IACzBE,CAAC,GAAGF,EAAE,GAAGE,CAAC,IAAI,CAACE,CAAC,GAAG,MAAM,KAAK,EAAE,CAAC,GAAGZ,CAAC,CAACC,CAAC,CAAC,IAAIC,CAAC,GAAG,UAAU,CAAC;IAC3DA,CAAC,GAAG,CAACQ,CAAC,KAAK,EAAE,KAAKE,CAAC,KAAK,EAAE,CAAC,GAAGH,EAAE,GAAGE,CAAC,IAAIT,CAAC,KAAK,EAAE,CAAC;IACjDF,CAAC,CAACC,CAAC,EAAE,CAAC,GAAGS,CAAC,GAAG,UAAU;EAC3B;EACA,OAAOR,CAAC;AACZ;AACA;AACA;AACA,SAASW,GAAGA,CAACf,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC3B,MAAMK,EAAE,GAAGT,CAAC,GAAG,MAAM;EACrB,MAAMU,EAAE,GAAGV,CAAC,IAAI,EAAE;EAClB,OAAO,EAAEI,CAAC,IAAI,CAAC,EAAE;IACb,IAAIO,CAAC,GAAG,IAAI,CAACZ,CAAC,CAAC,GAAG,MAAM;IACxB,MAAMa,CAAC,GAAG,IAAI,CAACb,CAAC,EAAE,CAAC,IAAI,EAAE;IACzB,MAAMc,CAAC,GAAGH,EAAE,GAAGC,CAAC,GAAGC,CAAC,GAAGH,EAAE;IACzBE,CAAC,GAAGF,EAAE,GAAGE,CAAC,IAAI,CAACE,CAAC,GAAG,MAAM,KAAK,EAAE,CAAC,GAAGZ,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC;IAC5CA,CAAC,GAAG,CAACQ,CAAC,IAAI,EAAE,KAAKE,CAAC,IAAI,EAAE,CAAC,GAAGH,EAAE,GAAGE,CAAC;IAClCX,CAAC,CAACC,CAAC,EAAE,CAAC,GAAGS,CAAC,GAAG,SAAS;EAC1B;EACA,OAAOR,CAAC;AACZ;AACA,MAAMY,SAAS,GAAG,OAAOC,SAAS,KAAK,WAAW;AAClD,IAAID,SAAS,IAAIlB,IAAI,IAAImB,SAAS,CAACC,OAAO,KAAK,6BAA6B,EAAE;EAC1E3B,UAAU,CAAC4B,SAAS,CAACC,EAAE,GAAGX,GAAG;EAC7Bb,KAAK,GAAG,EAAE;AACd,CAAC,MACI,IAAIoB,SAAS,IAAIlB,IAAI,IAAImB,SAAS,CAACC,OAAO,KAAK,UAAU,EAAE;EAC5D3B,UAAU,CAAC4B,SAAS,CAACC,EAAE,GAAGrB,GAAG;EAC7BH,KAAK,GAAG,EAAE;AACd,CAAC,MACI;EACD;EACAL,UAAU,CAAC4B,SAAS,CAACC,EAAE,GAAGL,GAAG;EAC7BnB,KAAK,GAAG,EAAE;AACd;AACAL,UAAU,CAAC4B,SAAS,CAACE,EAAE,GAAGzB,KAAK;AAC/BL,UAAU,CAAC4B,SAAS,CAACG,EAAE,GAAG,CAAC,CAAC,IAAI1B,KAAK,IAAI,CAAC;AAC1CL,UAAU,CAAC4B,SAAS,CAACI,EAAE,GAAG,CAAC,IAAI3B,KAAK;AACpC,MAAM4B,KAAK,GAAG,EAAE;AAChBjC,UAAU,CAAC4B,SAAS,CAACM,EAAE,GAAGlB,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAEF,KAAK,CAAC;AAC5CjC,UAAU,CAAC4B,SAAS,CAACQ,EAAE,GAAGH,KAAK,GAAG5B,KAAK;AACvCL,UAAU,CAAC4B,SAAS,CAACS,EAAE,GAAG,CAAC,GAAGhC,KAAK,GAAG4B,KAAK;AAC3C;AACA,MAAMK,KAAK,GAAG,sCAAsC;AACpD,MAAMC,KAAK,GAAG,EAAE;AAChB,IAAIC,EAAE,EAAEC,EAAE;AACVD,EAAE,GAAG,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;AACtB,KAAKD,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAE,EAAEA,EAAE,EACtBF,KAAK,CAACC,EAAE,EAAE,CAAC,GAAGC,EAAE;AACpBD,EAAE,GAAG,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;AACtB,KAAKD,EAAE,GAAG,EAAE,EAAEA,EAAE,GAAG,EAAE,EAAE,EAAEA,EAAE,EACvBF,KAAK,CAACC,EAAE,EAAE,CAAC,GAAGC,EAAE;AACpBD,EAAE,GAAG,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;AACtB,KAAKD,EAAE,GAAG,EAAE,EAAEA,EAAE,GAAG,EAAE,EAAE,EAAEA,EAAE,EACvBF,KAAK,CAACC,EAAE,EAAE,CAAC,GAAGC,EAAE;AACpB,SAASE,QAAQA,CAAC7B,CAAC,EAAE;EACjB,OAAOwB,KAAK,CAACM,MAAM,CAAC9B,CAAC,CAAC;AAC1B;AACA,SAAS+B,KAAKA,CAACC,CAAC,EAAErC,CAAC,EAAE;EACjB,MAAMI,CAAC,GAAG0B,KAAK,CAACO,CAAC,CAACJ,UAAU,CAACjC,CAAC,CAAC,CAAC;EAChC,OAAOI,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,GAAGA,CAAC;AAC7B;AACA;AACA,SAASkC,SAASA,CAACC,CAAC,EAAE;EAClB,KAAK,IAAIvC,CAAC,GAAG,IAAI,CAACwC,CAAC,GAAG,CAAC,EAAExC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAChCuC,CAAC,CAACvC,CAAC,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC;EAClBuC,CAAC,CAACC,CAAC,GAAG,IAAI,CAACA,CAAC;EACZD,CAAC,CAACF,CAAC,GAAG,IAAI,CAACA,CAAC;AAChB;AACA;AACA,SAASI,UAAUA,CAACxC,CAAC,EAAE;EACnB,IAAI,CAACuC,CAAC,GAAG,CAAC;EACV,IAAI,CAACH,CAAC,GAAGpC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACvB,IAAIA,CAAC,GAAG,CAAC,EACL,IAAI,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,KACX,IAAIA,CAAC,GAAG,CAAC,CAAC,EACX,IAAI,CAAC,CAAC,CAAC,GAAGA,CAAC,GAAG,IAAI,CAACsB,EAAE,CAAC,KAEtB,IAAI,CAACiB,CAAC,GAAG,CAAC;AAClB;AACA;AACA,SAASE,GAAGA,CAAC1C,CAAC,EAAE;EACZ,MAAMuC,CAAC,GAAG5C,GAAG,CAAC,CAAC;EACf4C,CAAC,CAACI,OAAO,CAAC3C,CAAC,CAAC;EACZ,OAAOuC,CAAC;AACZ;AACA;AACA,SAASK,aAAaA,CAACP,CAAC,EAAE5C,CAAC,EAAE;EACzB,IAAIoD,CAAC;EACL,IAAIpD,CAAC,KAAK,EAAE,EACRoD,CAAC,GAAG,CAAC,CAAC,KACL,IAAIpD,CAAC,KAAK,CAAC,EACZoD,CAAC,GAAG,CAAC,CAAC,KACL,IAAIpD,CAAC,KAAK,CAAC,EACZoD,CAAC,GAAG,CAAC,CAAC,KACL,IAAIpD,CAAC,KAAK,EAAE,EACboD,CAAC,GAAG,CAAC,CAAC,KACL,IAAIpD,CAAC,KAAK,CAAC,EACZoD,CAAC,GAAG,CAAC,CAAC,KAEN,MAAM,IAAIC,KAAK,CAAC,0CAA0C,CAAC;EAC/D,IAAI,CAACN,CAAC,GAAG,CAAC;EACV,IAAI,CAACH,CAAC,GAAG,CAAC;EACV,IAAIrC,CAAC,GAAGqC,CAAC,CAACU,MAAM;EAChB,IAAIC,EAAE,GAAG,KAAK;EACd,IAAIC,EAAE,GAAG,CAAC;EACV,OAAO,EAAEjD,CAAC,IAAI,CAAC,EAAE;IACb,MAAMC,CAAC,GAAGmC,KAAK,CAACC,CAAC,EAAErC,CAAC,CAAC;IACrB,IAAIC,CAAC,GAAG,CAAC,EAAE;MACP,IAAIoC,CAAC,CAACF,MAAM,CAACnC,CAAC,CAAC,KAAK,GAAG,EACnBgD,EAAE,GAAG,IAAI;MACb;IACJ;IACAA,EAAE,GAAG,KAAK;IACV,IAAIC,EAAE,KAAK,CAAC,EACR,IAAI,CAAC,IAAI,CAACT,CAAC,EAAE,CAAC,GAAGvC,CAAC,CAAC,KAClB,IAAIgD,EAAE,GAAGJ,CAAC,GAAG,IAAI,CAACxB,EAAE,EAAE;MACvB,IAAI,CAAC,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,IAAI,CAACvC,CAAC,GAAI,CAAC,CAAC,IAAK,IAAI,CAACoB,EAAE,GAAG4B,EAAG,IAAI,CAAE,KAAKA,EAAE;MAC3D,IAAI,CAAC,IAAI,CAACT,CAAC,EAAE,CAAC,GAAGvC,CAAC,IAAK,IAAI,CAACoB,EAAE,GAAG4B,EAAG;IACxC,CAAC,MAEG,IAAI,CAAC,IAAI,CAACT,CAAC,GAAG,CAAC,CAAC,IAAIvC,CAAC,IAAIgD,EAAE;IAC/BA,EAAE,IAAIJ,CAAC;IACP,IAAII,EAAE,IAAI,IAAI,CAAC5B,EAAE,EACb4B,EAAE,IAAI,IAAI,CAAC5B,EAAE;EACrB;EACA,IAAI,CAAC6B,KAAK,CAAC,CAAC;EACZ,IAAIF,EAAE,EACFzD,UAAU,CAAC4D,IAAI,CAACC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;AACzC;AACA;AACA,SAASC,QAAQA,CAAA,EAAG;EAChB,MAAMjD,CAAC,GAAG,IAAI,CAACiC,CAAC,GAAG,IAAI,CAACf,EAAE;EAC1B,OAAO,IAAI,CAACkB,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAACA,CAAC,GAAG,CAAC,CAAC,IAAIpC,CAAC,EACtC,EAAE,IAAI,CAACoC,CAAC;AAChB;AACA;AACA,SAASc,UAAUA,CAAC7D,CAAC,EAAE;EACnB,IAAI,IAAI,CAAC4C,CAAC,GAAG,CAAC,EACV,OAAO,GAAG,GAAG,IAAI,CAACkB,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC/D,CAAC,CAAC;EAC1C,IAAIoD,CAAC;EACL,IAAIpD,CAAC,IAAI,EAAE,EACPoD,CAAC,GAAG,CAAC,CAAC,KACL,IAAIpD,CAAC,KAAK,CAAC,EACZoD,CAAC,GAAG,CAAC,CAAC,KACL,IAAIpD,CAAC,KAAK,CAAC,EACZoD,CAAC,GAAG,CAAC,CAAC,KACL,IAAIpD,CAAC,KAAK,EAAE,EACboD,CAAC,GAAG,CAAC,CAAC,KACL,IAAIpD,CAAC,KAAK,CAAC,EACZoD,CAAC,GAAG,CAAC,CAAC,KAEN,MAAM,IAAIC,KAAK,CAAC,0CAA0C,CAAC;EAC/D,MAAMW,EAAE,GAAG,CAAC,CAAC,IAAIZ,CAAC,IAAI,CAAC;EACvB,IAAIa,CAAC;EACL,IAAI5C,CAAC,GAAG,KAAK;EACb,IAAIyB,CAAC,GAAG,EAAE;EACV,IAAIvC,CAAC,GAAG,IAAI,CAACwC,CAAC;EACd,IAAImB,CAAC,GAAG,IAAI,CAACtC,EAAE,GAAKrB,CAAC,GAAG,IAAI,CAACqB,EAAE,GAAIwB,CAAE;EACrC,IAAI7C,CAAC,EAAE,GAAG,CAAC,EAAE;IACT,IAAI2D,CAAC,GAAG,IAAI,CAACtC,EAAE,IAAI,CAACqC,CAAC,GAAG,IAAI,CAAC1D,CAAC,CAAC,IAAI2D,CAAC,IAAI,CAAC,EAAE;MACvC7C,CAAC,GAAG,IAAI;MACRyB,CAAC,GAAGL,QAAQ,CAACwB,CAAC,CAAC;IACnB;IACA,OAAO1D,CAAC,IAAI,CAAC,EAAE;MACX,IAAI2D,CAAC,GAAGd,CAAC,EAAE;QACPa,CAAC,GAAG,CAAC,IAAI,CAAC1D,CAAC,CAAC,GAAI,CAAC,CAAC,IAAI2D,CAAC,IAAI,CAAE,KAAMd,CAAC,GAAGc,CAAE;QACzCD,CAAC,IAAI,IAAI,CAAC,EAAE1D,CAAC,CAAC,KAAK2D,CAAC,IAAI,IAAI,CAACtC,EAAE,GAAGwB,CAAC,CAAC;MACxC,CAAC,MACI;QACDa,CAAC,GAAI,IAAI,CAAC1D,CAAC,CAAC,KAAK2D,CAAC,IAAId,CAAC,CAAC,GAAIY,EAAE;QAC9B,IAAIE,CAAC,IAAI,CAAC,EAAE;UACRA,CAAC,IAAI,IAAI,CAACtC,EAAE;UACZ,EAAErB,CAAC;QACP;MACJ;MACA,IAAI0D,CAAC,GAAG,CAAC,EACL5C,CAAC,GAAG,IAAI;MACZ,IAAIA,CAAC,EACDyB,CAAC,IAAIL,QAAQ,CAACwB,CAAC,CAAC;IACxB;EACJ;EACA,OAAO5C,CAAC,GAAGyB,CAAC,GAAG,GAAG;AACtB;AACA;AACA,SAASqB,QAAQA,CAAA,EAAG;EAChB,MAAMrB,CAAC,GAAG5C,GAAG,CAAC,CAAC;EACfJ,UAAU,CAAC4D,IAAI,CAACC,KAAK,CAAC,IAAI,EAAEb,CAAC,CAAC;EAC9B,OAAOA,CAAC;AACZ;AACA;AACA,SAASsB,KAAKA,CAAA,EAAG;EACb,OAAO,IAAI,CAACxB,CAAC,GAAG,CAAC,GAAG,IAAI,CAACkB,MAAM,CAAC,CAAC,GAAG,IAAI;AAC5C;AACA;AACA,SAASO,WAAWA,CAACtE,CAAC,EAAE;EACpB,IAAI+C,CAAC,GAAG,IAAI,CAACF,CAAC,GAAG7C,CAAC,CAAC6C,CAAC;EACpB,IAAIE,CAAC,IAAI,CAAC,EACN,OAAOA,CAAC;EACZ,IAAIvC,CAAC,GAAG,IAAI,CAACwC,CAAC;EACdD,CAAC,GAAGvC,CAAC,GAAGR,CAAC,CAACgD,CAAC;EACX,IAAID,CAAC,IAAI,CAAC,EACN,OAAO,IAAI,CAACF,CAAC,GAAG,CAAC,GAAG,CAACE,CAAC,GAAGA,CAAC;EAC9B,OAAO,EAAEvC,CAAC,IAAI,CAAC,EACX,IAAI,CAACuC,CAAC,GAAG,IAAI,CAACvC,CAAC,CAAC,GAAGR,CAAC,CAACQ,CAAC,CAAC,KAAK,CAAC,EACzB,OAAOuC,CAAC;EAChB,OAAO,CAAC;AACZ;AACA;AACA,SAASwB,KAAKA,CAAC9D,CAAC,EAAE;EACd,IAAIsC,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC;EACL,IAAI,CAACA,CAAC,GAAGvC,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE;IACtBA,CAAC,GAAGuC,CAAC;IACLD,CAAC,IAAI,EAAE;EACX;EACA,IAAI,CAACC,CAAC,GAAGvC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;IACpBA,CAAC,GAAGuC,CAAC;IACLD,CAAC,IAAI,CAAC;EACV;EACA,IAAI,CAACC,CAAC,GAAGvC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;IACpBA,CAAC,GAAGuC,CAAC;IACLD,CAAC,IAAI,CAAC;EACV;EACA,IAAI,CAACC,CAAC,GAAGvC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;IACpBA,CAAC,GAAGuC,CAAC;IACLD,CAAC,IAAI,CAAC;EACV;EACA,IAAI,CAACC,CAAC,GAAGvC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;IACpBA,CAAC,GAAGuC,CAAC;IACLD,CAAC,IAAI,CAAC;EACV;EACA,OAAOA,CAAC;AACZ;AACA;AACA,SAASyB,WAAWA,CAAA,EAAG;EACnB,IAAI,IAAI,CAACxB,CAAC,IAAI,CAAC,EACX,OAAO,CAAC;EACZ,OAAO,IAAI,CAACnB,EAAE,IAAI,IAAI,CAACmB,CAAC,GAAG,CAAC,CAAC,GAAGuB,KAAK,CAAC,IAAI,CAAC,IAAI,CAACvB,CAAC,GAAG,CAAC,CAAC,GAAI,IAAI,CAACH,CAAC,GAAG,IAAI,CAACf,EAAG,CAAC;AAChF;AACA;AACA,SAAS2C,YAAYA,CAAC5D,CAAC,EAAEkC,CAAC,EAAE;EACxB,IAAIvC,CAAC;EACL,KAAKA,CAAC,GAAG,IAAI,CAACwC,CAAC,GAAG,CAAC,EAAExC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAC5BuC,CAAC,CAACvC,CAAC,GAAGK,CAAC,CAAC,GAAG,IAAI,CAACL,CAAC,CAAC;EACtB,KAAKA,CAAC,GAAGK,CAAC,GAAG,CAAC,EAAEL,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EACvBuC,CAAC,CAACvC,CAAC,CAAC,GAAG,CAAC;EACZuC,CAAC,CAACC,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGnC,CAAC;EAChBkC,CAAC,CAACF,CAAC,GAAG,IAAI,CAACA,CAAC;AAChB;AACA;AACA,SAAS6B,YAAYA,CAAC7D,CAAC,EAAEkC,CAAC,EAAE;EACxB,KAAK,IAAIvC,CAAC,GAAGK,CAAC,EAAEL,CAAC,GAAG,IAAI,CAACwC,CAAC,EAAE,EAAExC,CAAC,EAC3BuC,CAAC,CAACvC,CAAC,GAAGK,CAAC,CAAC,GAAG,IAAI,CAACL,CAAC,CAAC;EACtBuC,CAAC,CAACC,CAAC,GAAGjC,IAAI,CAAC4D,GAAG,CAAC,IAAI,CAAC3B,CAAC,GAAGnC,CAAC,EAAE,CAAC,CAAC;EAC7BkC,CAAC,CAACF,CAAC,GAAG,IAAI,CAACA,CAAC;AAChB;AACA;AACA,SAAS+B,WAAWA,CAAC/D,CAAC,EAAEkC,CAAC,EAAE;EACvB,MAAM8B,EAAE,GAAGhE,CAAC,GAAG,IAAI,CAACgB,EAAE;EACtB,MAAMiD,GAAG,GAAG,IAAI,CAACjD,EAAE,GAAGgD,EAAE;EACxB,MAAME,EAAE,GAAG,CAAC,CAAC,IAAID,GAAG,IAAI,CAAC;EACzB,MAAME,EAAE,GAAGjE,IAAI,CAACC,KAAK,CAACH,CAAC,GAAG,IAAI,CAACgB,EAAE,CAAC;EAClC,IAAIjB,CAAC,GAAI,IAAI,CAACiC,CAAC,IAAIgC,EAAE,GAAI,IAAI,CAAC/C,EAAE;EAChC,IAAItB,CAAC;EACL,KAAKA,CAAC,GAAG,IAAI,CAACwC,CAAC,GAAG,CAAC,EAAExC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;IAC9BuC,CAAC,CAACvC,CAAC,GAAGwE,EAAE,GAAG,CAAC,CAAC,GAAI,IAAI,CAACxE,CAAC,CAAC,IAAIsE,GAAG,GAAIlE,CAAC;IACpCA,CAAC,GAAG,CAAC,IAAI,CAACJ,CAAC,CAAC,GAAGuE,EAAE,KAAKF,EAAE;EAC5B;EACA,KAAKrE,CAAC,GAAGwE,EAAE,GAAG,CAAC,EAAExE,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EACxBuC,CAAC,CAACvC,CAAC,CAAC,GAAG,CAAC;EACZuC,CAAC,CAACiC,EAAE,CAAC,GAAGpE,CAAC;EACTmC,CAAC,CAACC,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGgC,EAAE,GAAG,CAAC;EACrBjC,CAAC,CAACF,CAAC,GAAG,IAAI,CAACA,CAAC;EACZE,CAAC,CAACW,KAAK,CAAC,CAAC;AACb;AACA;AACA,SAASuB,WAAWA,CAACpE,CAAC,EAAEkC,CAAC,EAAE;EACvBA,CAAC,CAACF,CAAC,GAAG,IAAI,CAACA,CAAC;EACZ,MAAMmC,EAAE,GAAGjE,IAAI,CAACC,KAAK,CAACH,CAAC,GAAG,IAAI,CAACgB,EAAE,CAAC;EAClC,IAAImD,EAAE,IAAI,IAAI,CAAChC,CAAC,EAAE;IACdD,CAAC,CAACC,CAAC,GAAG,CAAC;IACP;EACJ;EACA,MAAM6B,EAAE,GAAGhE,CAAC,GAAG,IAAI,CAACgB,EAAE;EACtB,MAAMiD,GAAG,GAAG,IAAI,CAACjD,EAAE,GAAGgD,EAAE;EACxB,MAAME,EAAE,GAAG,CAAC,CAAC,IAAIF,EAAE,IAAI,CAAC;EACxB9B,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAACiC,EAAE,CAAC,IAAIH,EAAE;EACrB,KAAK,IAAIrE,CAAC,GAAGwE,EAAE,GAAG,CAAC,EAAExE,CAAC,GAAG,IAAI,CAACwC,CAAC,EAAE,EAAExC,CAAC,EAAE;IAClCuC,CAAC,CAACvC,CAAC,GAAGwE,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAACxE,CAAC,CAAC,GAAGuE,EAAE,KAAKD,GAAG;IACtC/B,CAAC,CAACvC,CAAC,GAAGwE,EAAE,CAAC,GAAG,IAAI,CAACxE,CAAC,CAAC,IAAIqE,EAAE;EAC7B;EACA,IAAIA,EAAE,GAAG,CAAC,EACN9B,CAAC,CAAC,IAAI,CAACC,CAAC,GAAGgC,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAACnC,CAAC,GAAGkC,EAAE,KAAKD,GAAG;EAC9C/B,CAAC,CAACC,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGgC,EAAE;EACjBjC,CAAC,CAACW,KAAK,CAAC,CAAC;AACb;AACA;AACA,SAASwB,QAAQA,CAAClF,CAAC,EAAE+C,CAAC,EAAE;EACpB,IAAIvC,CAAC,GAAG,CAAC;EACT,IAAII,CAAC,GAAG,CAAC;EACT,MAAMU,CAAC,GAAGP,IAAI,CAACoE,GAAG,CAACnF,CAAC,CAACgD,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC;EAC/B,OAAOxC,CAAC,GAAGc,CAAC,EAAE;IACVV,CAAC,IAAI,IAAI,CAACJ,CAAC,CAAC,GAAGR,CAAC,CAACQ,CAAC,CAAC;IACnBuC,CAAC,CAACvC,CAAC,EAAE,CAAC,GAAGI,CAAC,GAAG,IAAI,CAACkB,EAAE;IACpBlB,CAAC,KAAK,IAAI,CAACiB,EAAE;EACjB;EACA,IAAI7B,CAAC,CAACgD,CAAC,GAAG,IAAI,CAACA,CAAC,EAAE;IACdpC,CAAC,IAAIZ,CAAC,CAAC6C,CAAC;IACR,OAAOrC,CAAC,GAAG,IAAI,CAACwC,CAAC,EAAE;MACfpC,CAAC,IAAI,IAAI,CAACJ,CAAC,CAAC;MACZuC,CAAC,CAACvC,CAAC,EAAE,CAAC,GAAGI,CAAC,GAAG,IAAI,CAACkB,EAAE;MACpBlB,CAAC,KAAK,IAAI,CAACiB,EAAE;IACjB;IACAjB,CAAC,IAAI,IAAI,CAACiC,CAAC;EACf,CAAC,MACI;IACDjC,CAAC,IAAI,IAAI,CAACiC,CAAC;IACX,OAAOrC,CAAC,GAAGR,CAAC,CAACgD,CAAC,EAAE;MACZpC,CAAC,IAAIZ,CAAC,CAACQ,CAAC,CAAC;MACTuC,CAAC,CAACvC,CAAC,EAAE,CAAC,GAAGI,CAAC,GAAG,IAAI,CAACkB,EAAE;MACpBlB,CAAC,KAAK,IAAI,CAACiB,EAAE;IACjB;IACAjB,CAAC,IAAIZ,CAAC,CAAC6C,CAAC;EACZ;EACAE,CAAC,CAACF,CAAC,GAAGjC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACpB,IAAIA,CAAC,GAAG,CAAC,CAAC,EACNmC,CAAC,CAACvC,CAAC,EAAE,CAAC,GAAG,IAAI,CAACuB,EAAE,GAAGnB,CAAC,CAAC,KACpB,IAAIA,CAAC,GAAG,CAAC,EACVmC,CAAC,CAACvC,CAAC,EAAE,CAAC,GAAGI,CAAC;EACdmC,CAAC,CAACC,CAAC,GAAGxC,CAAC;EACPuC,CAAC,CAACW,KAAK,CAAC,CAAC;AACb;AACA;AACA;AACA,SAAS0B,aAAaA,CAACpF,CAAC,EAAE+C,CAAC,EAAE;EACzB,MAAMtC,CAAC,GAAG,IAAI,CAAC4E,GAAG,CAAC,CAAC;EACpB,MAAMC,CAAC,GAAGtF,CAAC,CAACqF,GAAG,CAAC,CAAC;EACjB,IAAI7E,CAAC,GAAGC,CAAC,CAACuC,CAAC;EACXD,CAAC,CAACC,CAAC,GAAGxC,CAAC,GAAG8E,CAAC,CAACtC,CAAC;EACb,OAAO,EAAExC,CAAC,IAAI,CAAC,EACXuC,CAAC,CAACvC,CAAC,CAAC,GAAG,CAAC;EACZ,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8E,CAAC,CAACtC,CAAC,EAAE,EAAExC,CAAC,EACpBuC,CAAC,CAACvC,CAAC,GAAGC,CAAC,CAACuC,CAAC,CAAC,GAAGvC,CAAC,CAACmB,EAAE,CAAC,CAAC,EAAE0D,CAAC,CAAC9E,CAAC,CAAC,EAAEuC,CAAC,EAAEvC,CAAC,EAAE,CAAC,EAAEC,CAAC,CAACuC,CAAC,CAAC;EAC5CD,CAAC,CAACF,CAAC,GAAG,CAAC;EACPE,CAAC,CAACW,KAAK,CAAC,CAAC;EACT,IAAI,IAAI,CAACb,CAAC,KAAK7C,CAAC,CAAC6C,CAAC,EACd9C,UAAU,CAAC4D,IAAI,CAACC,KAAK,CAACb,CAAC,EAAEA,CAAC,CAAC;AACnC;AACA;AACA,SAASwC,WAAWA,CAACxC,CAAC,EAAE;EACpB,MAAMtC,CAAC,GAAG,IAAI,CAAC4E,GAAG,CAAC,CAAC;EACpB,IAAI7E,CAAC,GAAIuC,CAAC,CAACC,CAAC,GAAG,CAAC,GAAGvC,CAAC,CAACuC,CAAE;EACvB,OAAO,EAAExC,CAAC,IAAI,CAAC,EACXuC,CAAC,CAACvC,CAAC,CAAC,GAAG,CAAC;EACZ,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,CAACuC,CAAC,GAAG,CAAC,EAAE,EAAExC,CAAC,EAAE;IAC1B,MAAMI,CAAC,GAAGH,CAAC,CAACmB,EAAE,CAACpB,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,EAAEuC,CAAC,EAAE,CAAC,GAAGvC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvC,IAAI,CAACuC,CAAC,CAACvC,CAAC,GAAGC,CAAC,CAACuC,CAAC,CAAC,IAAIvC,CAAC,CAACmB,EAAE,CAACpB,CAAC,GAAG,CAAC,EAAE,CAAC,GAAGC,CAAC,CAACD,CAAC,CAAC,EAAEuC,CAAC,EAAE,CAAC,GAAGvC,CAAC,GAAG,CAAC,EAAEI,CAAC,EAAEH,CAAC,CAACuC,CAAC,GAAGxC,CAAC,GAAG,CAAC,CAAC,KAClEC,CAAC,CAACsB,EAAE,EAAE;MACNgB,CAAC,CAACvC,CAAC,GAAGC,CAAC,CAACuC,CAAC,CAAC,IAAIvC,CAAC,CAACsB,EAAE;MAClBgB,CAAC,CAACvC,CAAC,GAAGC,CAAC,CAACuC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACtB;EACJ;EACA,IAAID,CAAC,CAACC,CAAC,GAAG,CAAC,EACPD,CAAC,CAACA,CAAC,CAACC,CAAC,GAAG,CAAC,CAAC,IAAIvC,CAAC,CAACmB,EAAE,CAACpB,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,EAAEuC,CAAC,EAAE,CAAC,GAAGvC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC/CuC,CAAC,CAACF,CAAC,GAAG,CAAC;EACPE,CAAC,CAACW,KAAK,CAAC,CAAC;AACb;AACA;AACA;AACA,SAAS8B,WAAWA,CAAClE,CAAC,EAAEmE,CAAC,EAAE1C,CAAC,EAAE;EAC1B,MAAM2C,EAAE,GAAGpE,CAAC,CAAC+D,GAAG,CAAC,CAAC;EAClB,IAAIK,EAAE,CAAC1C,CAAC,IAAI,CAAC,EACT;EACJ,MAAM2C,EAAE,GAAG,IAAI,CAACN,GAAG,CAAC,CAAC;EACrB,IAAIM,EAAE,CAAC3C,CAAC,GAAG0C,EAAE,CAAC1C,CAAC,EAAE;IACb,IAAIyC,CAAC,IAAI,IAAI,EACTA,CAAC,CAACtC,OAAO,CAAC,CAAC,CAAC;IAChB,IAAIJ,CAAC,IAAI,IAAI,EACT,IAAI,CAAC6C,MAAM,CAAC7C,CAAC,CAAC;IAClB;EACJ;EACA,IAAIA,CAAC,KAAK,IAAI,EACVA,CAAC,GAAG5C,GAAG,CAAC,CAAC;EACb,MAAMmF,CAAC,GAAGnF,GAAG,CAAC,CAAC;EACf,MAAM0F,EAAE,GAAG,IAAI,CAAChD,CAAC;EACjB,MAAMiD,EAAE,GAAGxE,CAAC,CAACuB,CAAC;EACd,MAAMkD,GAAG,GAAG,IAAI,CAAClE,EAAE,GAAG0C,KAAK,CAACmB,EAAE,CAACA,EAAE,CAAC1C,CAAC,GAAG,CAAC,CAAC,CAAC;EACzC;EACA,IAAI+C,GAAG,GAAG,CAAC,EAAE;IACTL,EAAE,CAACM,QAAQ,CAACD,GAAG,EAAET,CAAC,CAAC;IACnBK,EAAE,CAACK,QAAQ,CAACD,GAAG,EAAEhD,CAAC,CAAC;EACvB,CAAC,MACI;IACD2C,EAAE,CAACE,MAAM,CAACN,CAAC,CAAC;IACZK,EAAE,CAACC,MAAM,CAAC7C,CAAC,CAAC;EAChB;EACA,MAAMkD,EAAE,GAAGX,CAAC,CAACtC,CAAC;EACd,MAAMkD,EAAE,GAAGZ,CAAC,CAACW,EAAE,GAAG,CAAC,CAAC;EACpB,IAAIC,EAAE,KAAK,CAAC,EACR;EACJ,MAAMC,EAAE,GAAGD,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC/D,EAAE,CAAC,IAAI8D,EAAE,GAAG,CAAC,GAAGX,CAAC,CAACW,EAAE,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC7D,EAAE,GAAG,CAAC,CAAC;EACpE,MAAMgE,EAAE,GAAG,IAAI,CAACnE,EAAE,GAAGkE,EAAE;EACvB,MAAME,EAAE,GAAG,CAAC,CAAC,IAAI,IAAI,CAAClE,EAAE,IAAIgE,EAAE;EAC9B,MAAMG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAClE,EAAE;EACtB,IAAI5B,CAAC,GAAGuC,CAAC,CAACC,CAAC;EACX,IAAIrC,CAAC,GAAGH,CAAC,GAAGyF,EAAE;EACd,MAAMjD,CAAC,GAAGyC,CAAC,KAAK,IAAI,GAAGtF,GAAG,CAAC,CAAC,GAAGsF,CAAC;EAChCH,CAAC,CAACiB,SAAS,CAAC5F,CAAC,EAAEqC,CAAC,CAAC;EACjB,IAAID,CAAC,CAACyD,SAAS,CAACxD,CAAC,CAAC,IAAI,CAAC,EAAE;IACrBD,CAAC,CAACA,CAAC,CAACC,CAAC,EAAE,CAAC,GAAG,CAAC;IACZD,CAAC,CAACa,KAAK,CAACZ,CAAC,EAAED,CAAC,CAAC;EACjB;EACAhD,UAAU,CAAC0G,GAAG,CAACF,SAAS,CAACN,EAAE,EAAEjD,CAAC,CAAC;EAC/BA,CAAC,CAACY,KAAK,CAAC0B,CAAC,EAAEA,CAAC,CAAC;EACb;EACA,OAAOA,CAAC,CAACtC,CAAC,GAAGiD,EAAE,EACXX,CAAC,CAACA,CAAC,CAACtC,CAAC,EAAE,CAAC,GAAG,CAAC;EAChB,OAAO,EAAErC,CAAC,IAAI,CAAC,EAAE;IACb;IACA,IAAI+F,EAAE,GAAG3D,CAAC,CAAC,EAAEvC,CAAC,CAAC,KAAK0F,EAAE,GAAG,IAAI,CAACpE,EAAE,GAAGf,IAAI,CAACC,KAAK,CAAC+B,CAAC,CAACvC,CAAC,CAAC,GAAG4F,EAAE,GAAG,CAACrD,CAAC,CAACvC,CAAC,GAAG,CAAC,CAAC,GAAG8F,CAAC,IAAID,EAAE,CAAC;IAC9E,IAAI,CAACtD,CAAC,CAACvC,CAAC,CAAC,IAAI8E,CAAC,CAAC1D,EAAE,CAAC,CAAC,EAAE8E,EAAE,EAAE3D,CAAC,EAAEpC,CAAC,EAAE,CAAC,EAAEsF,EAAE,CAAC,IAAIS,EAAE,EAAE;MACzC;MACApB,CAAC,CAACiB,SAAS,CAAC5F,CAAC,EAAEqC,CAAC,CAAC;MACjBD,CAAC,CAACa,KAAK,CAACZ,CAAC,EAAED,CAAC,CAAC;MACb,OAAOA,CAAC,CAACvC,CAAC,CAAC,GAAG,EAAEkG,EAAE,EACd3D,CAAC,CAACa,KAAK,CAACZ,CAAC,EAAED,CAAC,CAAC;IACrB;EACJ;EACA,IAAI0C,CAAC,KAAK,IAAI,EAAE;IACZ1C,CAAC,CAAC4D,SAAS,CAACV,EAAE,EAAER,CAAC,CAAC;IAClB,IAAII,EAAE,KAAKC,EAAE,EACT/F,UAAU,CAAC4D,IAAI,CAACC,KAAK,CAAC6B,CAAC,EAAEA,CAAC,CAAC;EACnC;EACA1C,CAAC,CAACC,CAAC,GAAGiD,EAAE;EACRlD,CAAC,CAACW,KAAK,CAAC,CAAC;EACT,IAAIqC,GAAG,GAAG,CAAC,EACPhD,CAAC,CAAC6D,QAAQ,CAACb,GAAG,EAAEhD,CAAC,CAAC;EACtB;EACA,IAAI8C,EAAE,GAAG,CAAC,EACN9F,UAAU,CAAC4D,IAAI,CAACC,KAAK,CAACb,CAAC,EAAEA,CAAC,CAAC;AACnC;AACA;AACA,SAAS8D,KAAKA,CAAC7G,CAAC,EAAE;EACd,MAAM+C,CAAC,GAAG5C,GAAG,CAAC,CAAC;EACf,IAAI,CAACkF,GAAG,CAAC,CAAC,CAACyB,QAAQ,CAAC9G,CAAC,EAAE,IAAI,EAAE+C,CAAC,CAAC;EAC/B,IAAI,IAAI,CAACF,CAAC,GAAG,CAAC,IAAIE,CAAC,CAACyD,SAAS,CAACzG,UAAU,CAAC4D,IAAI,CAAC,GAAG,CAAC,EAC9C3D,CAAC,CAAC4D,KAAK,CAACb,CAAC,EAAEA,CAAC,CAAC;EACjB,OAAOA,CAAC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgE,WAAWA,CAAA,EAAG;EACnB,IAAI,IAAI,CAAC/D,CAAC,GAAG,CAAC,EACV,OAAO,CAAC;EACZ,MAAMvC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;EACjB,IAAI,CAACA,CAAC,GAAG,CAAC,MAAM,CAAC,EACb,OAAO,CAAC;EACZ,IAAI6E,CAAC,GAAG7E,CAAC,GAAG,CAAC;EACb;EACA6E,CAAC,GAAIA,CAAC,IAAI,CAAC,GAAG,CAAC7E,CAAC,GAAG,GAAG,IAAI6E,CAAC,CAAC,GAAI,GAAG;EACnC;EACAA,CAAC,GAAIA,CAAC,IAAI,CAAC,GAAG,CAAC7E,CAAC,GAAG,IAAI,IAAI6E,CAAC,CAAC,GAAI,IAAI;EACrC;EACAA,CAAC,GAAIA,CAAC,IAAI,CAAC,IAAK,CAAC7E,CAAC,GAAG,MAAM,IAAI6E,CAAC,GAAI,MAAM,CAAC,CAAC,GAAI,MAAM;EACtD;EACA;EACA;EACAA,CAAC,GAAIA,CAAC,IAAI,CAAC,GAAK7E,CAAC,GAAG6E,CAAC,GAAI,IAAI,CAACvD,EAAG,CAAC,GAAI,IAAI,CAACA,EAAE;EAC7C;EACA;EACA,OAAOuD,CAAC,GAAG,CAAC,GAAG,IAAI,CAACvD,EAAE,GAAGuD,CAAC,GAAG,CAACA,CAAC;AACnC;AACA,SAAS0B,QAAQA,CAAChH,CAAC,EAAE;EACjB,OAAO,IAAI,CAACwG,SAAS,CAACxG,CAAC,CAAC,KAAK,CAAC;AAClC;AACA;AACA,SAASiH,QAAQA,CAACjH,CAAC,EAAE+C,CAAC,EAAE;EACpB,IAAIvC,CAAC,GAAG,CAAC;EACT,IAAII,CAAC,GAAG,CAAC;EACT,MAAMU,CAAC,GAAGP,IAAI,CAACoE,GAAG,CAACnF,CAAC,CAACgD,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC;EAC/B,OAAOxC,CAAC,GAAGc,CAAC,EAAE;IACVV,CAAC,IAAI,IAAI,CAACJ,CAAC,CAAC,GAAGR,CAAC,CAACQ,CAAC,CAAC;IACnBuC,CAAC,CAACvC,CAAC,EAAE,CAAC,GAAGI,CAAC,GAAG,IAAI,CAACkB,EAAE;IACpBlB,CAAC,KAAK,IAAI,CAACiB,EAAE;EACjB;EACA,IAAI7B,CAAC,CAACgD,CAAC,GAAG,IAAI,CAACA,CAAC,EAAE;IACdpC,CAAC,IAAIZ,CAAC,CAAC6C,CAAC;IACR,OAAOrC,CAAC,GAAG,IAAI,CAACwC,CAAC,EAAE;MACfpC,CAAC,IAAI,IAAI,CAACJ,CAAC,CAAC;MACZuC,CAAC,CAACvC,CAAC,EAAE,CAAC,GAAGI,CAAC,GAAG,IAAI,CAACkB,EAAE;MACpBlB,CAAC,KAAK,IAAI,CAACiB,EAAE;IACjB;IACAjB,CAAC,IAAI,IAAI,CAACiC,CAAC;EACf,CAAC,MACI;IACDjC,CAAC,IAAI,IAAI,CAACiC,CAAC;IACX,OAAOrC,CAAC,GAAGR,CAAC,CAACgD,CAAC,EAAE;MACZpC,CAAC,IAAIZ,CAAC,CAACQ,CAAC,CAAC;MACTuC,CAAC,CAACvC,CAAC,EAAE,CAAC,GAAGI,CAAC,GAAG,IAAI,CAACkB,EAAE;MACpBlB,CAAC,KAAK,IAAI,CAACiB,EAAE;IACjB;IACAjB,CAAC,IAAIZ,CAAC,CAAC6C,CAAC;EACZ;EACAE,CAAC,CAACF,CAAC,GAAGjC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACpB,IAAIA,CAAC,GAAG,CAAC,EACLmC,CAAC,CAACvC,CAAC,EAAE,CAAC,GAAGI,CAAC,CAAC,KACV,IAAIA,CAAC,GAAG,CAAC,CAAC,EACXmC,CAAC,CAACvC,CAAC,EAAE,CAAC,GAAG,IAAI,CAACuB,EAAE,GAAGnB,CAAC;EACxBmC,CAAC,CAACC,CAAC,GAAGxC,CAAC;EACPuC,CAAC,CAACW,KAAK,CAAC,CAAC;AACb;AACA;AACA,SAASwD,KAAKA,CAAClH,CAAC,EAAE;EACd,MAAM+C,CAAC,GAAG5C,GAAG,CAAC,CAAC;EACf,IAAI,CAACgH,KAAK,CAACnH,CAAC,EAAE+C,CAAC,CAAC;EAChB,OAAOA,CAAC;AACZ;AACA;AACA,SAASqE,UAAUA,CAACpH,CAAC,EAAE;EACnB,MAAM+C,CAAC,GAAG5C,GAAG,CAAC,CAAC;EACf,IAAI,CAACyD,KAAK,CAAC5D,CAAC,EAAE+C,CAAC,CAAC;EAChB,OAAOA,CAAC;AACZ;AACA;AACA,SAASsE,UAAUA,CAACrH,CAAC,EAAE;EACnB,MAAM+C,CAAC,GAAG5C,GAAG,CAAC,CAAC;EACf,IAAI,CAACmH,UAAU,CAACtH,CAAC,EAAE+C,CAAC,CAAC;EACrB,OAAOA,CAAC;AACZ;AACA;AACA,SAASwE,QAAQA,CAACvH,CAAC,EAAE;EACjB,MAAM+C,CAAC,GAAG5C,GAAG,CAAC,CAAC;EACf,IAAI,CAAC2G,QAAQ,CAAC9G,CAAC,EAAE+C,CAAC,EAAE,IAAI,CAAC;EACzB,OAAOA,CAAC;AACZ;AACA;AACA,SAASyE,UAAUA,CAAClG,CAAC,EAAE;EACnB,IAAI,CAACA,CAAC,GAAGA,CAAC;EACV,IAAI,CAACmG,EAAE,GAAGnG,CAAC,CAACoG,QAAQ,CAAC,CAAC;EACtB,IAAI,CAACC,GAAG,GAAG,IAAI,CAACF,EAAE,GAAG,MAAM;EAC3B,IAAI,CAACG,GAAG,GAAG,IAAI,CAACH,EAAE,IAAI,EAAE;EACxB,IAAI,CAACI,EAAE,GAAG,CAAC,CAAC,IAAKvG,CAAC,CAACO,EAAE,GAAG,EAAG,IAAI,CAAC;EAChC,IAAI,CAACiG,GAAG,GAAG,CAAC,GAAGxG,CAAC,CAAC0B,CAAC;AACtB;AACA;AACA,SAAS+E,WAAWA,CAACtH,CAAC,EAAE;EACpB,MAAMsC,CAAC,GAAG5C,GAAG,CAAC,CAAC;EACfM,CAAC,CAAC4E,GAAG,CAAC,CAAC,CAACkB,SAAS,CAAC,IAAI,CAACjF,CAAC,CAAC0B,CAAC,EAAED,CAAC,CAAC;EAC9BA,CAAC,CAAC+D,QAAQ,CAAC,IAAI,CAACxF,CAAC,EAAE,IAAI,EAAEyB,CAAC,CAAC;EAC3B,IAAItC,CAAC,CAACoC,CAAC,GAAG,CAAC,IAAIE,CAAC,CAACyD,SAAS,CAACzG,UAAU,CAAC4D,IAAI,CAAC,GAAG,CAAC,EAC3C,IAAI,CAACrC,CAAC,CAACsC,KAAK,CAACb,CAAC,EAAEA,CAAC,CAAC;EACtB,OAAOA,CAAC;AACZ;AACA;AACA,SAASiF,UAAUA,CAACvH,CAAC,EAAE;EACnB,MAAMsC,CAAC,GAAG5C,GAAG,CAAC,CAAC;EACfM,CAAC,CAACmF,MAAM,CAAC7C,CAAC,CAAC;EACX,IAAI,CAACkF,MAAM,CAAClF,CAAC,CAAC;EACd,OAAOA,CAAC;AACZ;AACA;AACA,SAASmF,UAAUA,CAACzH,CAAC,EAAE;EACnB,OAAOA,CAAC,CAACuC,CAAC,IAAI,IAAI,CAAC8E,GAAG;EAClB;EACArH,CAAC,CAACA,CAAC,CAACuC,CAAC,EAAE,CAAC,GAAG,CAAC;EAChB,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACc,CAAC,CAAC0B,CAAC,EAAE,EAAExC,CAAC,EAAE;IAC/B;IACA,IAAIG,CAAC,GAAGF,CAAC,CAACD,CAAC,CAAC,GAAG,MAAM;IACrB,MAAM2H,EAAE,GAAIxH,CAAC,GAAG,IAAI,CAACgH,GAAG,IACnB,CAAEhH,CAAC,GAAG,IAAI,CAACiH,GAAG,GAAG,CAACnH,CAAC,CAACD,CAAC,CAAC,IAAI,EAAE,IAAI,IAAI,CAACmH,GAAG,GAAI,IAAI,CAACE,EAAE,KAAK,EAAE,CAAC,GAC5DpH,CAAC,CAACqB,EAAE;IACR;IACAnB,CAAC,GAAGH,CAAC,GAAG,IAAI,CAACc,CAAC,CAAC0B,CAAC;IAChBvC,CAAC,CAACE,CAAC,CAAC,IAAI,IAAI,CAACW,CAAC,CAACM,EAAE,CAAC,CAAC,EAAEuG,EAAE,EAAE1H,CAAC,EAAED,CAAC,EAAE,CAAC,EAAE,IAAI,CAACc,CAAC,CAAC0B,CAAC,CAAC;IAC3C;IACA,OAAOvC,CAAC,CAACE,CAAC,CAAC,IAAIF,CAAC,CAACsB,EAAE,EAAE;MACjBtB,CAAC,CAACE,CAAC,CAAC,IAAIF,CAAC,CAACsB,EAAE;MACZtB,CAAC,CAAC,EAAEE,CAAC,CAAC,EAAE;IACZ;EACJ;EACAF,CAAC,CAACiD,KAAK,CAAC,CAAC;EACTjD,CAAC,CAACkG,SAAS,CAAC,IAAI,CAACrF,CAAC,CAAC0B,CAAC,EAAEvC,CAAC,CAAC;EACxB,IAAIA,CAAC,CAAC+F,SAAS,CAAC,IAAI,CAAClF,CAAC,CAAC,IAAI,CAAC,EACxBb,CAAC,CAACmD,KAAK,CAAC,IAAI,CAACtC,CAAC,EAAEb,CAAC,CAAC;AAC1B;AACA;AACA,SAAS2H,SAASA,CAAC3H,CAAC,EAAEsC,CAAC,EAAE;EACrBtC,CAAC,CAAC4H,QAAQ,CAACtF,CAAC,CAAC;EACb,IAAI,CAACkF,MAAM,CAAClF,CAAC,CAAC;AAClB;AACA;AACA,SAASuF,SAASA,CAAC7H,CAAC,EAAE6E,CAAC,EAAEvC,CAAC,EAAE;EACxBtC,CAAC,CAAC6G,UAAU,CAAChC,CAAC,EAAEvC,CAAC,CAAC;EAClB,IAAI,CAACkF,MAAM,CAAClF,CAAC,CAAC;AAClB;AACAyE,UAAU,CAAC7F,SAAS,CAAC4G,OAAO,GAAGR,WAAW;AAC1CP,UAAU,CAAC7F,SAAS,CAAC6G,MAAM,GAAGR,UAAU;AACxCR,UAAU,CAAC7F,SAAS,CAACsG,MAAM,GAAGC,UAAU;AACxCV,UAAU,CAAC7F,SAAS,CAAC8G,KAAK,GAAGH,SAAS;AACtCd,UAAU,CAAC7F,SAAS,CAAC+G,KAAK,GAAGN,SAAS;AACtC;AACA,SAASO,QAAQA,CAACrC,CAAC,EAAEhF,CAAC,EAAEsH,QAAQ,EAAE;EAC9B,IAAIpI,CAAC,GAAG8F,CAAC,CAACuC,SAAS,CAAC,CAAC;EACrB,IAAIxF,CAAC;EACL,IAAIN,CAAC,GAAGG,GAAG,CAAC,CAAC,CAAC;EACd,MAAM4F,CAAC,GAAG,IAAItB,UAAU,CAAClG,CAAC,CAAC;EAC3B,IAAId,CAAC,IAAI,CAAC,EACN,OAAOuC,CAAC,CAAC,KACR,IAAIvC,CAAC,GAAG,EAAE,EACX6C,CAAC,GAAG,CAAC,CAAC,KACL,IAAI7C,CAAC,GAAG,EAAE,EACX6C,CAAC,GAAG,CAAC,CAAC,KACL,IAAI7C,CAAC,GAAG,GAAG,EACZ6C,CAAC,GAAG,CAAC,CAAC,KACL,IAAI7C,CAAC,GAAG,GAAG,EACZ6C,CAAC,GAAG,CAAC,CAAC,KAENA,CAAC,GAAG,CAAC;EACT;EACA,MAAM0F,CAAC,GAAG,EAAE;EACZ,IAAIlI,CAAC,GAAG,CAAC;EACT,MAAMmI,EAAE,GAAG3F,CAAC,GAAG,CAAC;EAChB,MAAMY,EAAE,GAAG,CAAC,CAAC,IAAIZ,CAAC,IAAI,CAAC;EACvB0F,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAACP,OAAO,CAAC,IAAI,CAAC;EACtB,IAAIlF,CAAC,GAAG,CAAC,EAAE;IACP,MAAM4F,EAAE,GAAG9I,GAAG,CAAC,CAAC;IAChB2I,CAAC,CAACJ,KAAK,CAACK,CAAC,CAAC,CAAC,CAAC,EAAEE,EAAE,CAAC;IACjB,OAAOpI,CAAC,IAAIoD,EAAE,EAAE;MACZ8E,CAAC,CAAClI,CAAC,CAAC,GAAGV,GAAG,CAAC,CAAC;MACZ2I,CAAC,CAACL,KAAK,CAACQ,EAAE,EAAEF,CAAC,CAAClI,CAAC,GAAG,CAAC,CAAC,EAAEkI,CAAC,CAAClI,CAAC,CAAC,CAAC;MAC3BA,CAAC,IAAI,CAAC;IACV;EACJ;EACA,IAAIF,CAAC,GAAG2F,CAAC,CAACtD,CAAC,GAAG,CAAC;EACf,IAAItC,CAAC;EACL,IAAIwI,GAAG,GAAG,IAAI;EACd,IAAIC,EAAE,GAAGhJ,GAAG,CAAC,CAAC;EACd,IAAI6C,CAAC;EACLxC,CAAC,GAAG+D,KAAK,CAAC+B,CAAC,CAAC3F,CAAC,CAAC,CAAC,GAAG,CAAC;EACnB,OAAOA,CAAC,IAAI,CAAC,EAAE;IACX,IAAIH,CAAC,IAAIwI,EAAE,EACPtI,CAAC,GAAI4F,CAAC,CAAC3F,CAAC,CAAC,IAAKH,CAAC,GAAGwI,EAAG,GAAI/E,EAAE,CAAC,KAC3B;MACDvD,CAAC,GAAG,CAAC4F,CAAC,CAAC3F,CAAC,CAAC,GAAI,CAAC,CAAC,IAAKH,CAAC,GAAG,CAAE,IAAI,CAAE,KAAMwI,EAAE,GAAGxI,CAAE;MAC7C,IAAIG,CAAC,GAAG,CAAC,EACLD,CAAC,IAAI4F,CAAC,CAAC3F,CAAC,GAAG,CAAC,CAAC,IAAK,IAAI,CAACkB,EAAE,GAAGrB,CAAC,GAAGwI,EAAG;IAC3C;IACAnI,CAAC,GAAGwC,CAAC;IACL,OAAO,CAAC3C,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;MAClBA,CAAC,KAAK,CAAC;MACP,EAAEG,CAAC;IACP;IACA,IAAI,CAACL,CAAC,IAAIK,CAAC,IAAI,CAAC,EAAE;MACdL,CAAC,IAAI,IAAI,CAACqB,EAAE;MACZ,EAAElB,CAAC;IACP;IACA,IAAIuI,GAAG,EAAE;MACL;MACAH,CAAC,CAACrI,CAAC,CAAC,CAACkF,MAAM,CAAC7C,CAAC,CAAC;MACdmG,GAAG,GAAG,KAAK;IACf,CAAC,MACI;MACD,OAAOrI,CAAC,GAAG,CAAC,EAAE;QACViI,CAAC,CAACJ,KAAK,CAAC3F,CAAC,EAAEoG,EAAE,CAAC;QACdL,CAAC,CAACJ,KAAK,CAACS,EAAE,EAAEpG,CAAC,CAAC;QACdlC,CAAC,IAAI,CAAC;MACV;MACA,IAAIA,CAAC,GAAG,CAAC,EACLiI,CAAC,CAACJ,KAAK,CAAC3F,CAAC,EAAEoG,EAAE,CAAC,CAAC,KACd;QACDnG,CAAC,GAAGD,CAAC;QACLA,CAAC,GAAGoG,EAAE;QACNA,EAAE,GAAGnG,CAAC;MACV;MACA8F,CAAC,CAACL,KAAK,CAACU,EAAE,EAAEJ,CAAC,CAACrI,CAAC,CAAC,EAAEqC,CAAC,CAAC;IACxB;IACA,OAAOpC,CAAC,IAAI,CAAC,IAAI,CAAC2F,CAAC,CAAC3F,CAAC,CAAC,GAAI,CAAC,IAAIH,CAAE,MAAM,CAAC,EAAE;MACtCsI,CAAC,CAACJ,KAAK,CAAC3F,CAAC,EAAEoG,EAAE,CAAC;MACdnG,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGoG,EAAE;MACNA,EAAE,GAAGnG,CAAC;MACN,IAAI,EAAExC,CAAC,GAAG,CAAC,EAAE;QACTA,CAAC,GAAG,IAAI,CAACqB,EAAE,GAAG,CAAC;QACf,EAAElB,CAAC;MACP;IACJ;EACJ;EACA,MAAMyI,MAAM,GAAGN,CAAC,CAACN,MAAM,CAACzF,CAAC,CAAC;EAC1B6F,QAAQ,CAAC,IAAI,EAAEQ,MAAM,CAAC;EACtB,OAAOA,MAAM;AACjB;AACA;AACArJ,UAAU,CAAC4B,SAAS,CAACiE,MAAM,GAAG9C,SAAS;AACvC/C,UAAU,CAAC4B,SAAS,CAACwB,OAAO,GAAGF,UAAU;AACzClD,UAAU,CAAC4B,SAAS,CAACzB,UAAU,GAAGkD,aAAa;AAC/CrD,UAAU,CAAC4B,SAAS,CAAC+B,KAAK,GAAGG,QAAQ;AACrC9D,UAAU,CAAC4B,SAAS,CAAC4E,SAAS,GAAG9B,YAAY;AAC7C1E,UAAU,CAAC4B,SAAS,CAACgF,SAAS,GAAGjC,YAAY;AAC7C3E,UAAU,CAAC4B,SAAS,CAACqE,QAAQ,GAAGpB,WAAW;AAC3C7E,UAAU,CAAC4B,SAAS,CAACiF,QAAQ,GAAG3B,WAAW;AAC3ClF,UAAU,CAAC4B,SAAS,CAACiC,KAAK,GAAGsB,QAAQ;AACrCnF,UAAU,CAAC4B,SAAS,CAAC2F,UAAU,GAAGlC,aAAa;AAC/CrF,UAAU,CAAC4B,SAAS,CAAC0G,QAAQ,GAAG9C,WAAW;AAC3CxF,UAAU,CAAC4B,SAAS,CAACmF,QAAQ,GAAGtB,WAAW;AAC3CzF,UAAU,CAAC4B,SAAS,CAAC+F,QAAQ,GAAGX,WAAW;AAC3ChH,UAAU,CAAC4B,SAAS,CAACwF,KAAK,GAAGF,QAAQ;AACrC;AACAlH,UAAU,CAAC4B,SAAS,CAACqC,QAAQ,GAAGF,UAAU;AAC1C/D,UAAU,CAAC4B,SAAS,CAACoC,MAAM,GAAGK,QAAQ;AACtCrE,UAAU,CAAC4B,SAAS,CAAC0D,GAAG,GAAGhB,KAAK;AAChCtE,UAAU,CAAC4B,SAAS,CAAC6E,SAAS,GAAGlC,WAAW;AAC5CvE,UAAU,CAAC4B,SAAS,CAACkH,SAAS,GAAGrE,WAAW;AAC5CzE,UAAU,CAAC4B,SAAS,CAAC0H,GAAG,GAAGxC,KAAK;AAChC9G,UAAU,CAAC4B,SAAS,CAAC2H,MAAM,GAAGtC,QAAQ;AACtCjH,UAAU,CAAC4B,SAAS,CAAC4H,GAAG,GAAGrC,KAAK;AAChCnH,UAAU,CAAC4B,SAAS,CAAC6H,QAAQ,GAAGpC,UAAU;AAC1CrH,UAAU,CAAC4B,SAAS,CAAC8H,QAAQ,GAAGpC,UAAU;AAC1CtH,UAAU,CAAC4B,SAAS,CAAC+H,MAAM,GAAGnC,QAAQ;AACtCxH,UAAU,CAAC4B,SAAS,CAACgI,MAAM,GAAGhB,QAAQ;AACtC;AACA5I,UAAU,CAAC4D,IAAI,GAAGT,GAAG,CAAC,CAAC,CAAC;AACxBnD,UAAU,CAAC0G,GAAG,GAAGvD,GAAG,CAAC,CAAC,CAAC;AAEvB,SAASnD,UAAU,IAAI6J,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}