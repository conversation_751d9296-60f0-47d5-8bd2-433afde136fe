{"ast": null, "code": "import { getDnsSuffix } from '@aws-amplify/core/internals/aws-client-utils';\nimport { AmplifyUrl } from '@aws-amplify/core/internals/utils';\nimport { COGNITO_IDP_SERVICE_NAME } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst cognitoUserPoolEndpointResolver = ({\n  region\n}) => ({\n  url: new AmplifyUrl(`https://${COGNITO_IDP_SERVICE_NAME}.${region}.${getDnsSuffix(region)}`)\n});\nexport { cognitoUserPoolEndpointResolver };", "map": {"version": 3, "names": ["getDnsSuffix", "AmplifyUrl", "COGNITO_IDP_SERVICE_NAME", "cognitoUserPoolEndpointResolver", "region", "url"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/cognitoUserPoolEndpointResolver.mjs"], "sourcesContent": ["import { getDnsSuffix } from '@aws-amplify/core/internals/aws-client-utils';\nimport { AmplifyUrl } from '@aws-amplify/core/internals/utils';\nimport { COGNITO_IDP_SERVICE_NAME } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst cognitoUserPoolEndpointResolver = ({ region, }) => ({\n    url: new AmplifyUrl(`https://${COGNITO_IDP_SERVICE_NAME}.${region}.${getDnsSuffix(region)}`),\n});\n\nexport { cognitoUserPoolEndpointResolver };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,8CAA8C;AAC3E,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,wBAAwB,QAAQ,iBAAiB;;AAE1D;AACA;AACA,MAAMC,+BAA+B,GAAGA,CAAC;EAAEC;AAAQ,CAAC,MAAM;EACtDC,GAAG,EAAE,IAAIJ,UAAU,CAAC,WAAWC,wBAAwB,IAAIE,MAAM,IAAIJ,YAAY,CAACI,MAAM,CAAC,EAAE;AAC/F,CAAC,CAAC;AAEF,SAASD,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}