{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { deDupeAsyncFunction, assertTokenProviderConfig, decodeJWT } from '@aws-amplify/core/internals/utils';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokensWithRefreshToken } from './types.mjs';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { createInitiateAuthClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createInitiateAuthClient.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getUserContextData } from './userContextData.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst refreshAuthTokensFunction = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* ({\n    tokens,\n    authConfig,\n    username\n  }) {\n    assertTokenProviderConfig(authConfig?.Cognito);\n    const {\n      userPoolId,\n      userPoolClientId,\n      userPoolEndpoint\n    } = authConfig.Cognito;\n    const region = getRegionFromUserPoolId(userPoolId);\n    assertAuthTokensWithRefreshToken(tokens);\n    const refreshTokenString = tokens.refreshToken;\n    const AuthParameters = {\n      REFRESH_TOKEN: refreshTokenString\n    };\n    if (tokens.deviceMetadata?.deviceKey) {\n      AuthParameters.DEVICE_KEY = tokens.deviceMetadata.deviceKey;\n    }\n    const UserContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const initiateAuth = createInitiateAuthClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const {\n      AuthenticationResult\n    } = yield initiateAuth({\n      region\n    }, {\n      ClientId: userPoolClientId,\n      AuthFlow: 'REFRESH_TOKEN_AUTH',\n      AuthParameters,\n      UserContextData\n    });\n    const accessToken = decodeJWT(AuthenticationResult?.AccessToken ?? '');\n    const idToken = AuthenticationResult?.IdToken ? decodeJWT(AuthenticationResult.IdToken) : undefined;\n    const {\n      iat\n    } = accessToken.payload;\n    // This should never happen. If it does, it's a bug from the service.\n    if (!iat) {\n      throw new AuthError({\n        name: 'iatNotFoundException',\n        message: 'iat not found in access token'\n      });\n    }\n    const clockDrift = iat * 1000 - new Date().getTime();\n    return {\n      accessToken,\n      idToken,\n      clockDrift,\n      refreshToken: refreshTokenString,\n      username\n    };\n  });\n  return function refreshAuthTokensFunction(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nconst refreshAuthTokens = deDupeAsyncFunction(refreshAuthTokensFunction);\nconst refreshAuthTokensWithoutDedupe = refreshAuthTokensFunction;\nexport { refreshAuthTokens, refreshAuthTokensWithoutDedupe };", "map": {"version": 3, "names": ["deDupeAsyncFunction", "assertTokenProviderConfig", "decodeJWT", "getRegionFromUserPoolId", "assertAuthTokensWithRefreshToken", "<PERSON>th<PERSON><PERSON><PERSON>", "createInitiateAuthClient", "createCognitoUserPoolEndpointResolver", "getUserContextData", "refreshAuthTokensFunction", "_ref", "_asyncToGenerator", "tokens", "authConfig", "username", "Cognito", "userPoolId", "userPoolClientId", "userPoolEndpoint", "region", "refreshTokenString", "refreshToken", "AuthParameters", "REFRESH_TOKEN", "deviceMetadata", "deviceKey", "DEVICE_KEY", "UserContextData", "initiateAuth", "endpointResolver", "endpointOverride", "AuthenticationResult", "ClientId", "AuthFlow", "accessToken", "AccessToken", "idToken", "IdToken", "undefined", "iat", "payload", "name", "message", "clockDrift", "Date", "getTime", "_x", "apply", "arguments", "refreshAuthTokens", "refreshAuthTokensWithoutDedupe"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/refreshAuthTokens.mjs"], "sourcesContent": ["import { deDupeAsyncFunction, assertTokenProviderConfig, decodeJWT } from '@aws-amplify/core/internals/utils';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokensWithRefreshToken } from './types.mjs';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { createInitiateAuthClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createInitiateAuthClient.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getUserContextData } from './userContextData.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst refreshAuthTokensFunction = async ({ tokens, authConfig, username, }) => {\n    assertTokenProviderConfig(authConfig?.Cognito);\n    const { userPoolId, userPoolClientId, userPoolEndpoint } = authConfig.Cognito;\n    const region = getRegionFromUserPoolId(userPoolId);\n    assertAuthTokensWithRefreshToken(tokens);\n    const refreshTokenString = tokens.refreshToken;\n    const AuthParameters = {\n        REFRESH_TOKEN: refreshTokenString,\n    };\n    if (tokens.deviceMetadata?.deviceKey) {\n        AuthParameters.DEVICE_KEY = tokens.deviceMetadata.deviceKey;\n    }\n    const UserContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const initiateAuth = createInitiateAuthClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const { AuthenticationResult } = await initiateAuth({ region }, {\n        ClientId: userPoolClientId,\n        AuthFlow: 'REFRESH_TOKEN_AUTH',\n        AuthParameters,\n        UserContextData,\n    });\n    const accessToken = decodeJWT(AuthenticationResult?.AccessToken ?? '');\n    const idToken = AuthenticationResult?.IdToken\n        ? decodeJWT(AuthenticationResult.IdToken)\n        : undefined;\n    const { iat } = accessToken.payload;\n    // This should never happen. If it does, it's a bug from the service.\n    if (!iat) {\n        throw new AuthError({\n            name: 'iatNotFoundException',\n            message: 'iat not found in access token',\n        });\n    }\n    const clockDrift = iat * 1000 - new Date().getTime();\n    return {\n        accessToken,\n        idToken,\n        clockDrift,\n        refreshToken: refreshTokenString,\n        username,\n    };\n};\nconst refreshAuthTokens = deDupeAsyncFunction(refreshAuthTokensFunction);\nconst refreshAuthTokensWithoutDedupe = refreshAuthTokensFunction;\n\nexport { refreshAuthTokens, refreshAuthTokensWithoutDedupe };\n"], "mappings": ";AAAA,SAASA,mBAAmB,EAAEC,yBAAyB,EAAEC,SAAS,QAAQ,mCAAmC;AAC7G,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,gCAAgC,QAAQ,aAAa;AAC9D,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,wBAAwB,QAAQ,mGAAmG;AAC5I,OAAO,wDAAwD;AAC/D,OAAO,wHAAwH;AAC/H,OAAO,8CAA8C;AACrD,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,sCAAsC;AAC7C,OAAO,qBAAqB;AAC5B,SAASC,qCAAqC,QAAQ,wDAAwD;AAC9G,SAASC,kBAAkB,QAAQ,uBAAuB;;AAE1D;AACA;AACA,MAAMC,yBAAyB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAO;IAAEC,MAAM;IAAEC,UAAU;IAAEC;EAAU,CAAC,EAAK;IAC3Eb,yBAAyB,CAACY,UAAU,EAAEE,OAAO,CAAC;IAC9C,MAAM;MAAEC,UAAU;MAAEC,gBAAgB;MAAEC;IAAiB,CAAC,GAAGL,UAAU,CAACE,OAAO;IAC7E,MAAMI,MAAM,GAAGhB,uBAAuB,CAACa,UAAU,CAAC;IAClDZ,gCAAgC,CAACQ,MAAM,CAAC;IACxC,MAAMQ,kBAAkB,GAAGR,MAAM,CAACS,YAAY;IAC9C,MAAMC,cAAc,GAAG;MACnBC,aAAa,EAAEH;IACnB,CAAC;IACD,IAAIR,MAAM,CAACY,cAAc,EAAEC,SAAS,EAAE;MAClCH,cAAc,CAACI,UAAU,GAAGd,MAAM,CAACY,cAAc,CAACC,SAAS;IAC/D;IACA,MAAME,eAAe,GAAGnB,kBAAkB,CAAC;MACvCM,QAAQ;MACRE,UAAU;MACVC;IACJ,CAAC,CAAC;IACF,MAAMW,YAAY,GAAGtB,wBAAwB,CAAC;MAC1CuB,gBAAgB,EAAEtB,qCAAqC,CAAC;QACpDuB,gBAAgB,EAAEZ;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAM;MAAEa;IAAqB,CAAC,SAASH,YAAY,CAAC;MAAET;IAAO,CAAC,EAAE;MAC5Da,QAAQ,EAAEf,gBAAgB;MAC1BgB,QAAQ,EAAE,oBAAoB;MAC9BX,cAAc;MACdK;IACJ,CAAC,CAAC;IACF,MAAMO,WAAW,GAAGhC,SAAS,CAAC6B,oBAAoB,EAAEI,WAAW,IAAI,EAAE,CAAC;IACtE,MAAMC,OAAO,GAAGL,oBAAoB,EAAEM,OAAO,GACvCnC,SAAS,CAAC6B,oBAAoB,CAACM,OAAO,CAAC,GACvCC,SAAS;IACf,MAAM;MAAEC;IAAI,CAAC,GAAGL,WAAW,CAACM,OAAO;IACnC;IACA,IAAI,CAACD,GAAG,EAAE;MACN,MAAM,IAAIlC,SAAS,CAAC;QAChBoC,IAAI,EAAE,sBAAsB;QAC5BC,OAAO,EAAE;MACb,CAAC,CAAC;IACN;IACA,MAAMC,UAAU,GAAGJ,GAAG,GAAG,IAAI,GAAG,IAAIK,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACpD,OAAO;MACHX,WAAW;MACXE,OAAO;MACPO,UAAU;MACVtB,YAAY,EAAED,kBAAkB;MAChCN;IACJ,CAAC;EACL,CAAC;EAAA,gBAhDKL,yBAAyBA,CAAAqC,EAAA;IAAA,OAAApC,IAAA,CAAAqC,KAAA,OAAAC,SAAA;EAAA;AAAA,GAgD9B;AACD,MAAMC,iBAAiB,GAAGjD,mBAAmB,CAACS,yBAAyB,CAAC;AACxE,MAAMyC,8BAA8B,GAAGzC,yBAAyB;AAEhE,SAASwC,iBAAiB,EAAEC,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}