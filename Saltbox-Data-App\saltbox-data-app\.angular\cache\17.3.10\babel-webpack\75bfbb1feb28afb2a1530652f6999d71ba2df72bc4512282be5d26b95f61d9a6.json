{"ast": null, "code": "export var Features;\n(function (Features) {\n  Features[\"Visualizations\"] = \"Visualizations\";\n  Features[\"GridCharts\"] = \"GridCharts\";\n  Features[\"Reports\"] = \"ReportAssets\";\n  Features[\"AssetGroupTags\"] = \"asset-group-tags\";\n  Features[\"CalendarView\"] = \"calendar_view\";\n  Features[\"HelpLauncher\"] = \"help-launcher\";\n  Features[\"AdvancedJsonEdit\"] = \"sxp-extensibility-advanced-json\";\n  Features[\"AgentChat\"] = \"Grid Agent Chat\";\n  Features[\"GridColumnsOptions\"] = \"grid_columns_options\";\n})(Features || (Features = {}));", "map": {"version": 3, "names": ["Features"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\shared\\enums\\features.enum.ts"], "sourcesContent": ["export enum Features {\r\n    Visualizations = 'Visualizations',\r\n    GridCharts = 'GridCharts', // maybe this should be visualization-tiles / visualization-grids\r\n    Reports = 'ReportAssets',\r\n    AssetGroupTags = 'asset-group-tags',\r\n    CalendarView = 'calendar_view',\r\n    HelpLauncher = 'help-launcher',\r\n    AdvancedJsonEdit = 'sxp-extensibility-advanced-json',\r\n    AgentChat = 'Grid Agent Chat',\r\n    GridColumnsOptions = 'grid_columns_options'\r\n}\r\n"], "mappings": "AAAA,WAAYA,QAUX;AAVD,WAAYA,QAAQ;EAChBA,QAAA,qCAAiC;EACjCA,QAAA,6BAAyB;EACzBA,QAAA,4BAAwB;EACxBA,QAAA,uCAAmC;EACnCA,QAAA,kCAA8B;EAC9BA,QAAA,kCAA8B;EAC9BA,QAAA,wDAAoD;EACpDA,QAAA,iCAA6B;EAC7BA,QAAA,+CAA2C;AAC/C,CAAC,EAVWA,QAAQ,KAARA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}