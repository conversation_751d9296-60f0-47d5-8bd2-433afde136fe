{"ast": null, "code": "const pagination = {\n  current: {\n    alignItems: {\n      value: 'center'\n    },\n    justifyContent: {\n      value: 'center'\n    },\n    color: {\n      value: '{colors.font.inverse.value}'\n    },\n    fontSize: {\n      value: '{fontSizes.small.value}'\n    },\n    backgroundColor: {\n      value: '{colors.overlay.40.value}'\n    }\n  },\n  button: {\n    color: {\n      value: '{colors.font.primary.value}'\n    },\n    paddingInlineStart: {\n      value: '{space.xxs.value}'\n    },\n    paddingInlineEnd: {\n      value: '{space.xxs.value}'\n    },\n    transitionProperty: {\n      value: 'background-color'\n    },\n    transitionDuration: {\n      value: '{time.medium.value}'\n    },\n    hover: {\n      backgroundColor: {\n        value: '{colors.overlay.10.value}'\n      },\n      color: {\n        value: '{colors.font.primary.value}'\n      }\n    },\n    disabled: {\n      color: {\n        value: '{colors.font.disabled.value}'\n      }\n    }\n  },\n  ellipsis: {\n    alignItems: {\n      value: 'baseline'\n    },\n    justifyContent: {\n      value: 'center'\n    },\n    paddingInlineStart: {\n      value: '{space.xs.value}'\n    },\n    paddingInlineEnd: {\n      value: '{space.xs.value}'\n    }\n  },\n  itemContainer: {\n    marginLeft: {\n      value: '{space.xxxs.value}'\n    },\n    marginRight: {\n      value: '{space.xxxs.value}'\n    }\n  },\n  itemShared: {\n    height: {\n      value: '{fontSizes.xxl.value}'\n    },\n    minWidth: {\n      value: '{fontSizes.xxl.value}'\n    },\n    borderRadius: {\n      value: '{fontSizes.medium.value}'\n    }\n  }\n};\nexport { pagination };", "map": {"version": 3, "names": ["pagination", "current", "alignItems", "value", "justifyContent", "color", "fontSize", "backgroundColor", "button", "paddingInlineStart", "paddingInlineEnd", "transitionProperty", "transitionDuration", "hover", "disabled", "ellipsis", "itemContainer", "marginLeft", "marginRight", "itemShared", "height", "min<PERSON><PERSON><PERSON>", "borderRadius"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/pagination.mjs"], "sourcesContent": ["const pagination = {\n    current: {\n        alignItems: { value: 'center' },\n        justifyContent: { value: 'center' },\n        color: { value: '{colors.font.inverse.value}' },\n        fontSize: { value: '{fontSizes.small.value}' },\n        backgroundColor: { value: '{colors.overlay.40.value}' },\n    },\n    button: {\n        color: { value: '{colors.font.primary.value}' },\n        paddingInlineStart: { value: '{space.xxs.value}' },\n        paddingInlineEnd: { value: '{space.xxs.value}' },\n        transitionProperty: { value: 'background-color' },\n        transitionDuration: { value: '{time.medium.value}' },\n        hover: {\n            backgroundColor: { value: '{colors.overlay.10.value}' },\n            color: { value: '{colors.font.primary.value}' },\n        },\n        disabled: {\n            color: { value: '{colors.font.disabled.value}' },\n        },\n    },\n    ellipsis: {\n        alignItems: { value: 'baseline' },\n        justifyContent: { value: 'center' },\n        paddingInlineStart: { value: '{space.xs.value}' },\n        paddingInlineEnd: { value: '{space.xs.value}' },\n    },\n    itemContainer: {\n        marginLeft: { value: '{space.xxxs.value}' },\n        marginRight: { value: '{space.xxxs.value}' },\n    },\n    itemShared: {\n        height: { value: '{fontSizes.xxl.value}' },\n        minWidth: { value: '{fontSizes.xxl.value}' },\n        borderRadius: { value: '{fontSizes.medium.value}' },\n    },\n};\n\nexport { pagination };\n"], "mappings": "AAAA,MAAMA,UAAU,GAAG;EACfC,OAAO,EAAE;IACLC,UAAU,EAAE;MAAEC,KAAK,EAAE;IAAS,CAAC;IAC/BC,cAAc,EAAE;MAAED,KAAK,EAAE;IAAS,CAAC;IACnCE,KAAK,EAAE;MAAEF,KAAK,EAAE;IAA8B,CAAC;IAC/CG,QAAQ,EAAE;MAAEH,KAAK,EAAE;IAA0B,CAAC;IAC9CI,eAAe,EAAE;MAAEJ,KAAK,EAAE;IAA4B;EAC1D,CAAC;EACDK,MAAM,EAAE;IACJH,KAAK,EAAE;MAAEF,KAAK,EAAE;IAA8B,CAAC;IAC/CM,kBAAkB,EAAE;MAAEN,KAAK,EAAE;IAAoB,CAAC;IAClDO,gBAAgB,EAAE;MAAEP,KAAK,EAAE;IAAoB,CAAC;IAChDQ,kBAAkB,EAAE;MAAER,KAAK,EAAE;IAAmB,CAAC;IACjDS,kBAAkB,EAAE;MAAET,KAAK,EAAE;IAAsB,CAAC;IACpDU,KAAK,EAAE;MACHN,eAAe,EAAE;QAAEJ,KAAK,EAAE;MAA4B,CAAC;MACvDE,KAAK,EAAE;QAAEF,KAAK,EAAE;MAA8B;IAClD,CAAC;IACDW,QAAQ,EAAE;MACNT,KAAK,EAAE;QAAEF,KAAK,EAAE;MAA+B;IACnD;EACJ,CAAC;EACDY,QAAQ,EAAE;IACNb,UAAU,EAAE;MAAEC,KAAK,EAAE;IAAW,CAAC;IACjCC,cAAc,EAAE;MAAED,KAAK,EAAE;IAAS,CAAC;IACnCM,kBAAkB,EAAE;MAAEN,KAAK,EAAE;IAAmB,CAAC;IACjDO,gBAAgB,EAAE;MAAEP,KAAK,EAAE;IAAmB;EAClD,CAAC;EACDa,aAAa,EAAE;IACXC,UAAU,EAAE;MAAEd,KAAK,EAAE;IAAqB,CAAC;IAC3Ce,WAAW,EAAE;MAAEf,KAAK,EAAE;IAAqB;EAC/C,CAAC;EACDgB,UAAU,EAAE;IACRC,MAAM,EAAE;MAAEjB,KAAK,EAAE;IAAwB,CAAC;IAC1CkB,QAAQ,EAAE;MAAElB,KAAK,EAAE;IAAwB,CAAC;IAC5CmB,YAAY,EAAE;MAAEnB,KAAK,EAAE;IAA2B;EACtD;AACJ,CAAC;AAED,SAASH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}