{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns expected date strings to be used in signing.\n *\n * @param date JavaScript `Date` object.\n * @returns `FormattedDates` object containing the following:\n * - longDate: A date string in 'YYYYMMDDThhmmssZ' format\n * - shortDate: A date string in 'YYYYMMDD' format\n *\n * @internal\n */\nconst getFormattedDates = date => {\n  const longDate = date.toISOString().replace(/[:-]|\\.\\d{3}/g, '');\n  return {\n    longDate,\n    shortDate: longDate.slice(0, 8)\n  };\n};\nexport { getFormattedDates };", "map": {"version": 3, "names": ["getFormattedDates", "date", "longDate", "toISOString", "replace", "shortDate", "slice"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/signer/signatureV4/utils/getFormattedDates.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns expected date strings to be used in signing.\n *\n * @param date JavaScript `Date` object.\n * @returns `FormattedDates` object containing the following:\n * - longDate: A date string in 'YYYYMMDDThhmmssZ' format\n * - shortDate: A date string in 'YYYYMMDD' format\n *\n * @internal\n */\nconst getFormattedDates = (date) => {\n    const longDate = date.toISOString().replace(/[:-]|\\.\\d{3}/g, '');\n    return {\n        longDate,\n        shortDate: longDate.slice(0, 8),\n    };\n};\n\nexport { getFormattedDates };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,iBAAiB,GAAIC,IAAI,IAAK;EAChC,MAAMC,QAAQ,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;EAChE,OAAO;IACHF,QAAQ;IACRG,SAAS,EAAEH,QAAQ,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC;EAClC,CAAC;AACL,CAAC;AAED,SAASN,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}