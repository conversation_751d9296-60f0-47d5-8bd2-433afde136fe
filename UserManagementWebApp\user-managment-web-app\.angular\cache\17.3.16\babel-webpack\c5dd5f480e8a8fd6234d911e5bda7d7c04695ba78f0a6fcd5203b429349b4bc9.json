{"ast": null, "code": "const message = {\n  // Default styles\n  alignItems: {\n    value: 'center'\n  },\n  backgroundColor: {\n    value: '{colors.background.tertiary.value}'\n  },\n  borderColor: {\n    value: 'transparent'\n  },\n  borderStyle: {\n    value: 'solid'\n  },\n  borderWidth: {\n    value: '{borderWidths.small.value}'\n  },\n  borderRadius: {\n    value: '{radii.xs.value}'\n  },\n  color: {\n    value: '{colors.font.primary.value}'\n  },\n  justifyContent: {\n    value: 'flex-start'\n  },\n  paddingBlock: {\n    value: '{space.small.value}'\n  },\n  paddingInline: {\n    value: '{space.medium.value}'\n  },\n  lineHeight: {\n    value: '{lineHeights.small.value}'\n  },\n  icon: {\n    size: {\n      value: '{fontSizes.xl.value}'\n    }\n  },\n  heading: {\n    fontSize: {\n      value: '{fontSizes.medium.value}'\n    },\n    fontWeight: {\n      value: '{fontWeights.bold.value}'\n    }\n  },\n  dismiss: {\n    gap: {\n      value: '{space.xxs.value}'\n    }\n  },\n  // Variations\n  plain: {\n    color: {\n      value: '{colors.font.primary.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.primary.value}'\n    },\n    borderColor: {\n      value: 'transparent'\n    },\n    info: {\n      color: {\n        value: '{colors.font.info.value}'\n      },\n      backgroundColor: {\n        value: '{colors.background.primary.value}'\n      },\n      borderColor: {\n        value: 'transparent'\n      }\n    },\n    error: {\n      color: {\n        value: '{colors.font.error.value}'\n      },\n      backgroundColor: {\n        value: '{colors.background.primary.value}'\n      },\n      borderColor: {\n        value: 'transparent'\n      }\n    },\n    success: {\n      color: {\n        value: '{colors.font.success.value}'\n      },\n      backgroundColor: {\n        value: '{colors.background.primary.value}'\n      },\n      borderColor: {\n        value: 'transparent'\n      }\n    },\n    warning: {\n      color: {\n        value: '{colors.font.warning.value}'\n      },\n      backgroundColor: {\n        value: '{colors.background.primary.value}'\n      },\n      borderColor: {\n        value: 'transparent'\n      }\n    }\n  },\n  outlined: {\n    color: {\n      value: '{colors.font.primary.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.primary.value}'\n    },\n    borderColor: {\n      value: '{colors.border.primary.value}'\n    },\n    info: {\n      color: {\n        value: '{colors.font.info.value}'\n      },\n      backgroundColor: {\n        value: '{colors.background.primary.value}'\n      },\n      borderColor: {\n        value: '{colors.border.info.value}'\n      }\n    },\n    error: {\n      color: {\n        value: '{colors.font.error.value}'\n      },\n      backgroundColor: {\n        value: '{colors.background.primary.value}'\n      },\n      borderColor: {\n        value: '{colors.border.error.value}'\n      }\n    },\n    success: {\n      color: {\n        value: '{colors.font.success.value}'\n      },\n      backgroundColor: {\n        value: '{colors.background.primary.value}'\n      },\n      borderColor: {\n        value: '{colors.border.success.value}'\n      }\n    },\n    warning: {\n      color: {\n        value: '{colors.font.warning.value}'\n      },\n      backgroundColor: {\n        value: '{colors.background.primary.value}'\n      },\n      borderColor: {\n        value: '{colors.border.warning.value}'\n      }\n    }\n  },\n  filled: {\n    color: {\n      value: '{colors.font.primary.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.secondary.value}'\n    },\n    borderColor: {\n      value: 'transparent'\n    },\n    info: {\n      color: {\n        value: '{colors.font.info.value}'\n      },\n      backgroundColor: {\n        value: '{colors.background.info.value}'\n      },\n      borderColor: {\n        value: 'transparent'\n      }\n    },\n    error: {\n      color: {\n        value: '{colors.font.error.value}'\n      },\n      backgroundColor: {\n        value: '{colors.background.error.value}'\n      },\n      borderColor: {\n        value: 'transparent'\n      }\n    },\n    success: {\n      color: {\n        value: '{colors.font.success.value}'\n      },\n      backgroundColor: {\n        value: '{colors.background.success.value}'\n      },\n      borderColor: {\n        value: 'transparent'\n      }\n    },\n    warning: {\n      color: {\n        value: '{colors.font.warning.value}'\n      },\n      backgroundColor: {\n        value: '{colors.background.warning.value}'\n      },\n      borderColor: {\n        value: 'transparent'\n      }\n    }\n  }\n};\nexport { message };", "map": {"version": 3, "names": ["message", "alignItems", "value", "backgroundColor", "borderColor", "borderStyle", "borderWidth", "borderRadius", "color", "justifyContent", "paddingBlock", "paddingInline", "lineHeight", "icon", "size", "heading", "fontSize", "fontWeight", "dismiss", "gap", "plain", "info", "error", "success", "warning", "outlined", "filled"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/message.mjs"], "sourcesContent": ["const message = {\n    // Default styles\n    alignItems: { value: 'center' },\n    backgroundColor: { value: '{colors.background.tertiary.value}' },\n    borderColor: { value: 'transparent' },\n    borderStyle: { value: 'solid' },\n    borderWidth: { value: '{borderWidths.small.value}' },\n    borderRadius: { value: '{radii.xs.value}' },\n    color: { value: '{colors.font.primary.value}' },\n    justifyContent: { value: 'flex-start' },\n    paddingBlock: { value: '{space.small.value}' },\n    paddingInline: { value: '{space.medium.value}' },\n    lineHeight: { value: '{lineHeights.small.value}' },\n    icon: {\n        size: { value: '{fontSizes.xl.value}' },\n    },\n    heading: {\n        fontSize: { value: '{fontSizes.medium.value}' },\n        fontWeight: { value: '{fontWeights.bold.value}' },\n    },\n    dismiss: {\n        gap: { value: '{space.xxs.value}' },\n    },\n    // Variations\n    plain: {\n        color: { value: '{colors.font.primary.value}' },\n        backgroundColor: { value: '{colors.background.primary.value}' },\n        borderColor: { value: 'transparent' },\n        info: {\n            color: { value: '{colors.font.info.value}' },\n            backgroundColor: { value: '{colors.background.primary.value}' },\n            borderColor: { value: 'transparent' },\n        },\n        error: {\n            color: { value: '{colors.font.error.value}' },\n            backgroundColor: { value: '{colors.background.primary.value}' },\n            borderColor: { value: 'transparent' },\n        },\n        success: {\n            color: { value: '{colors.font.success.value}' },\n            backgroundColor: { value: '{colors.background.primary.value}' },\n            borderColor: { value: 'transparent' },\n        },\n        warning: {\n            color: { value: '{colors.font.warning.value}' },\n            backgroundColor: { value: '{colors.background.primary.value}' },\n            borderColor: { value: 'transparent' },\n        },\n    },\n    outlined: {\n        color: { value: '{colors.font.primary.value}' },\n        backgroundColor: { value: '{colors.background.primary.value}' },\n        borderColor: { value: '{colors.border.primary.value}' },\n        info: {\n            color: { value: '{colors.font.info.value}' },\n            backgroundColor: { value: '{colors.background.primary.value}' },\n            borderColor: { value: '{colors.border.info.value}' },\n        },\n        error: {\n            color: { value: '{colors.font.error.value}' },\n            backgroundColor: { value: '{colors.background.primary.value}' },\n            borderColor: { value: '{colors.border.error.value}' },\n        },\n        success: {\n            color: { value: '{colors.font.success.value}' },\n            backgroundColor: { value: '{colors.background.primary.value}' },\n            borderColor: { value: '{colors.border.success.value}' },\n        },\n        warning: {\n            color: { value: '{colors.font.warning.value}' },\n            backgroundColor: { value: '{colors.background.primary.value}' },\n            borderColor: { value: '{colors.border.warning.value}' },\n        },\n    },\n    filled: {\n        color: { value: '{colors.font.primary.value}' },\n        backgroundColor: { value: '{colors.background.secondary.value}' },\n        borderColor: { value: 'transparent' },\n        info: {\n            color: { value: '{colors.font.info.value}' },\n            backgroundColor: { value: '{colors.background.info.value}' },\n            borderColor: { value: 'transparent' },\n        },\n        error: {\n            color: { value: '{colors.font.error.value}' },\n            backgroundColor: { value: '{colors.background.error.value}' },\n            borderColor: { value: 'transparent' },\n        },\n        success: {\n            color: { value: '{colors.font.success.value}' },\n            backgroundColor: { value: '{colors.background.success.value}' },\n            borderColor: { value: 'transparent' },\n        },\n        warning: {\n            color: { value: '{colors.font.warning.value}' },\n            backgroundColor: { value: '{colors.background.warning.value}' },\n            borderColor: { value: 'transparent' },\n        },\n    },\n};\n\nexport { message };\n"], "mappings": "AAAA,MAAMA,OAAO,GAAG;EACZ;EACAC,UAAU,EAAE;IAAEC,KAAK,EAAE;EAAS,CAAC;EAC/BC,eAAe,EAAE;IAAED,KAAK,EAAE;EAAqC,CAAC;EAChEE,WAAW,EAAE;IAAEF,KAAK,EAAE;EAAc,CAAC;EACrCG,WAAW,EAAE;IAAEH,KAAK,EAAE;EAAQ,CAAC;EAC/BI,WAAW,EAAE;IAAEJ,KAAK,EAAE;EAA6B,CAAC;EACpDK,YAAY,EAAE;IAAEL,KAAK,EAAE;EAAmB,CAAC;EAC3CM,KAAK,EAAE;IAAEN,KAAK,EAAE;EAA8B,CAAC;EAC/CO,cAAc,EAAE;IAAEP,KAAK,EAAE;EAAa,CAAC;EACvCQ,YAAY,EAAE;IAAER,KAAK,EAAE;EAAsB,CAAC;EAC9CS,aAAa,EAAE;IAAET,KAAK,EAAE;EAAuB,CAAC;EAChDU,UAAU,EAAE;IAAEV,KAAK,EAAE;EAA4B,CAAC;EAClDW,IAAI,EAAE;IACFC,IAAI,EAAE;MAAEZ,KAAK,EAAE;IAAuB;EAC1C,CAAC;EACDa,OAAO,EAAE;IACLC,QAAQ,EAAE;MAAEd,KAAK,EAAE;IAA2B,CAAC;IAC/Ce,UAAU,EAAE;MAAEf,KAAK,EAAE;IAA2B;EACpD,CAAC;EACDgB,OAAO,EAAE;IACLC,GAAG,EAAE;MAAEjB,KAAK,EAAE;IAAoB;EACtC,CAAC;EACD;EACAkB,KAAK,EAAE;IACHZ,KAAK,EAAE;MAAEN,KAAK,EAAE;IAA8B,CAAC;IAC/CC,eAAe,EAAE;MAAED,KAAK,EAAE;IAAoC,CAAC;IAC/DE,WAAW,EAAE;MAAEF,KAAK,EAAE;IAAc,CAAC;IACrCmB,IAAI,EAAE;MACFb,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA2B,CAAC;MAC5CC,eAAe,EAAE;QAAED,KAAK,EAAE;MAAoC,CAAC;MAC/DE,WAAW,EAAE;QAAEF,KAAK,EAAE;MAAc;IACxC,CAAC;IACDoB,KAAK,EAAE;MACHd,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA4B,CAAC;MAC7CC,eAAe,EAAE;QAAED,KAAK,EAAE;MAAoC,CAAC;MAC/DE,WAAW,EAAE;QAAEF,KAAK,EAAE;MAAc;IACxC,CAAC;IACDqB,OAAO,EAAE;MACLf,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA8B,CAAC;MAC/CC,eAAe,EAAE;QAAED,KAAK,EAAE;MAAoC,CAAC;MAC/DE,WAAW,EAAE;QAAEF,KAAK,EAAE;MAAc;IACxC,CAAC;IACDsB,OAAO,EAAE;MACLhB,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA8B,CAAC;MAC/CC,eAAe,EAAE;QAAED,KAAK,EAAE;MAAoC,CAAC;MAC/DE,WAAW,EAAE;QAAEF,KAAK,EAAE;MAAc;IACxC;EACJ,CAAC;EACDuB,QAAQ,EAAE;IACNjB,KAAK,EAAE;MAAEN,KAAK,EAAE;IAA8B,CAAC;IAC/CC,eAAe,EAAE;MAAED,KAAK,EAAE;IAAoC,CAAC;IAC/DE,WAAW,EAAE;MAAEF,KAAK,EAAE;IAAgC,CAAC;IACvDmB,IAAI,EAAE;MACFb,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA2B,CAAC;MAC5CC,eAAe,EAAE;QAAED,KAAK,EAAE;MAAoC,CAAC;MAC/DE,WAAW,EAAE;QAAEF,KAAK,EAAE;MAA6B;IACvD,CAAC;IACDoB,KAAK,EAAE;MACHd,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA4B,CAAC;MAC7CC,eAAe,EAAE;QAAED,KAAK,EAAE;MAAoC,CAAC;MAC/DE,WAAW,EAAE;QAAEF,KAAK,EAAE;MAA8B;IACxD,CAAC;IACDqB,OAAO,EAAE;MACLf,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA8B,CAAC;MAC/CC,eAAe,EAAE;QAAED,KAAK,EAAE;MAAoC,CAAC;MAC/DE,WAAW,EAAE;QAAEF,KAAK,EAAE;MAAgC;IAC1D,CAAC;IACDsB,OAAO,EAAE;MACLhB,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA8B,CAAC;MAC/CC,eAAe,EAAE;QAAED,KAAK,EAAE;MAAoC,CAAC;MAC/DE,WAAW,EAAE;QAAEF,KAAK,EAAE;MAAgC;IAC1D;EACJ,CAAC;EACDwB,MAAM,EAAE;IACJlB,KAAK,EAAE;MAAEN,KAAK,EAAE;IAA8B,CAAC;IAC/CC,eAAe,EAAE;MAAED,KAAK,EAAE;IAAsC,CAAC;IACjEE,WAAW,EAAE;MAAEF,KAAK,EAAE;IAAc,CAAC;IACrCmB,IAAI,EAAE;MACFb,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA2B,CAAC;MAC5CC,eAAe,EAAE;QAAED,KAAK,EAAE;MAAiC,CAAC;MAC5DE,WAAW,EAAE;QAAEF,KAAK,EAAE;MAAc;IACxC,CAAC;IACDoB,KAAK,EAAE;MACHd,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA4B,CAAC;MAC7CC,eAAe,EAAE;QAAED,KAAK,EAAE;MAAkC,CAAC;MAC7DE,WAAW,EAAE;QAAEF,KAAK,EAAE;MAAc;IACxC,CAAC;IACDqB,OAAO,EAAE;MACLf,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA8B,CAAC;MAC/CC,eAAe,EAAE;QAAED,KAAK,EAAE;MAAoC,CAAC;MAC/DE,WAAW,EAAE;QAAEF,KAAK,EAAE;MAAc;IACxC,CAAC;IACDsB,OAAO,EAAE;MACLhB,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA8B,CAAC;MAC/CC,eAAe,EAAE;QAAED,KAAK,EAAE;MAAoC,CAAC;MAC/DE,WAAW,EAAE;QAAEF,KAAK,EAAE;MAAc;IACxC;EACJ;AACJ,CAAC;AAED,SAASF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}