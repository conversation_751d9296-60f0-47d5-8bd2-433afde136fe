{"ast": null, "code": "import { ConsoleLogger } from '../Logger/ConsoleLogger.mjs';\nimport { InMemoryStorage } from './InMemoryStorage.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n * @returns Either a reference to window.localStorage or an in-memory storage as fallback\n */\nconst logger = new ConsoleLogger('CoreStorageUtils');\nconst getLocalStorageWithFallback = () => {\n  try {\n    // Attempt to use localStorage directly\n    if (typeof window !== 'undefined' && window.localStorage) {\n      return window.localStorage;\n    }\n  } catch (e) {\n    // Handle any errors related to localStorage access\n    logger.info('localStorage not found. InMemoryStorage is used as a fallback.');\n  }\n  // Return in-memory storage as a fallback if localStorage is not accessible\n  return new InMemoryStorage();\n};\n/**\n * @internal\n * @returns Either a reference to window.sessionStorage or an in-memory storage as fallback\n */\nconst getSessionStorageWithFallback = () => {\n  try {\n    // Attempt to use sessionStorage directly\n    if (typeof window !== 'undefined' && window.sessionStorage) {\n      // Verify we can actually use it by testing access\n      window.sessionStorage.getItem('test');\n      return window.sessionStorage;\n    }\n    throw new Error('sessionStorage is not defined');\n  } catch (e) {\n    // Handle any errors related to sessionStorage access\n    logger.info('sessionStorage not found. InMemoryStorage is used as a fallback.');\n    return new InMemoryStorage();\n  }\n};\nexport { getLocalStorageWithFallback, getSessionStorageWithFallback };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "InMemoryStorage", "logger", "getLocalStorageWithFallback", "window", "localStorage", "e", "info", "getSessionStorageWithFallback", "sessionStorage", "getItem", "Error"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/storage/utils.mjs"], "sourcesContent": ["import { ConsoleLogger } from '../Logger/ConsoleLogger.mjs';\nimport { InMemoryStorage } from './InMemoryStorage.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n * @returns Either a reference to window.localStorage or an in-memory storage as fallback\n */\nconst logger = new ConsoleLogger('CoreStorageUtils');\nconst getLocalStorageWithFallback = () => {\n    try {\n        // Attempt to use localStorage directly\n        if (typeof window !== 'undefined' && window.localStorage) {\n            return window.localStorage;\n        }\n    }\n    catch (e) {\n        // Handle any errors related to localStorage access\n        logger.info('localStorage not found. InMemoryStorage is used as a fallback.');\n    }\n    // Return in-memory storage as a fallback if localStorage is not accessible\n    return new InMemoryStorage();\n};\n/**\n * @internal\n * @returns Either a reference to window.sessionStorage or an in-memory storage as fallback\n */\nconst getSessionStorageWithFallback = () => {\n    try {\n        // Attempt to use sessionStorage directly\n        if (typeof window !== 'undefined' && window.sessionStorage) {\n            // Verify we can actually use it by testing access\n            window.sessionStorage.getItem('test');\n            return window.sessionStorage;\n        }\n        throw new Error('sessionStorage is not defined');\n    }\n    catch (e) {\n        // Handle any errors related to sessionStorage access\n        logger.info('sessionStorage not found. InMemoryStorage is used as a fallback.');\n        return new InMemoryStorage();\n    }\n};\n\nexport { getLocalStorageWithFallback, getSessionStorageWithFallback };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,eAAe,QAAQ,uBAAuB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG,IAAIF,aAAa,CAAC,kBAAkB,CAAC;AACpD,MAAMG,2BAA2B,GAAGA,CAAA,KAAM;EACtC,IAAI;IACA;IACA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,YAAY,EAAE;MACtD,OAAOD,MAAM,CAACC,YAAY;IAC9B;EACJ,CAAC,CACD,OAAOC,CAAC,EAAE;IACN;IACAJ,MAAM,CAACK,IAAI,CAAC,gEAAgE,CAAC;EACjF;EACA;EACA,OAAO,IAAIN,eAAe,CAAC,CAAC;AAChC,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMO,6BAA6B,GAAGA,CAAA,KAAM;EACxC,IAAI;IACA;IACA,IAAI,OAAOJ,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACK,cAAc,EAAE;MACxD;MACAL,MAAM,CAACK,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC;MACrC,OAAON,MAAM,CAACK,cAAc;IAChC;IACA,MAAM,IAAIE,KAAK,CAAC,+BAA+B,CAAC;EACpD,CAAC,CACD,OAAOL,CAAC,EAAE;IACN;IACAJ,MAAM,CAACK,IAAI,CAAC,kEAAkE,CAAC;IAC/E,OAAO,IAAIN,eAAe,CAAC,CAAC;EAChC;AACJ,CAAC;AAED,SAASE,2BAA2B,EAAEK,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}