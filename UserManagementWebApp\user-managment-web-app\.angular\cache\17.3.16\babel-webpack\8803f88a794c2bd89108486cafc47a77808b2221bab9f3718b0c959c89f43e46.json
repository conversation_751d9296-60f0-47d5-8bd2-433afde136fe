{"ast": null, "code": "import { getCredentialScope } from './getCredentialScope.mjs';\nimport { getFormattedDates } from './getFormattedDates.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Extracts common values used for signing both requests and urls.\n *\n * @param options `SignRequestOptions` object containing values used to construct the signature.\n * @returns Common `SigningValues` used for signing.\n *\n * @internal\n */\nconst getSigningValues = ({\n  credentials,\n  signingDate = new Date(),\n  signingRegion,\n  signingService,\n  uriEscapePath = true\n}) => {\n  // get properties from credentials\n  const {\n    accessKeyId,\n    secretAccessKey,\n    sessionToken\n  } = credentials;\n  // get formatted dates for signing\n  const {\n    longDate,\n    shortDate\n  } = getFormattedDates(signingDate);\n  // copy header and set signing properties\n  const credentialScope = getCredentialScope(shortDate, signingRegion, signingService);\n  return {\n    accessKeyId,\n    credentialScope,\n    longDate,\n    secretAccessKey,\n    sessionToken,\n    shortDate,\n    signingRegion,\n    signingService,\n    uriEscapePath\n  };\n};\nexport { getSigningValues };", "map": {"version": 3, "names": ["getCredentialScope", "getFormattedDates", "getSigningValues", "credentials", "signingDate", "Date", "signingRegion", "signingService", "uriEscapePath", "accessKeyId", "secretAccessKey", "sessionToken", "longDate", "shortDate", "credentialScope"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/signer/signatureV4/utils/getSigningValues.mjs"], "sourcesContent": ["import { getCredentialScope } from './getCredentialScope.mjs';\nimport { getFormattedDates } from './getFormattedDates.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Extracts common values used for signing both requests and urls.\n *\n * @param options `SignRequestOptions` object containing values used to construct the signature.\n * @returns Common `SigningValues` used for signing.\n *\n * @internal\n */\nconst getSigningValues = ({ credentials, signingDate = new Date(), signingRegion, signingService, uriEscapePath = true, }) => {\n    // get properties from credentials\n    const { accessKeyId, secretAccessKey, sessionToken } = credentials;\n    // get formatted dates for signing\n    const { longDate, shortDate } = getFormattedDates(signingDate);\n    // copy header and set signing properties\n    const credentialScope = getCredentialScope(shortDate, signingRegion, signingService);\n    return {\n        accessKeyId,\n        credentialScope,\n        longDate,\n        secretAccessKey,\n        sessionToken,\n        shortDate,\n        signingRegion,\n        signingService,\n        uriEscapePath,\n    };\n};\n\nexport { getSigningValues };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,iBAAiB,QAAQ,yBAAyB;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,WAAW;EAAEC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC;EAAEC,aAAa;EAAEC,cAAc;EAAEC,aAAa,GAAG;AAAM,CAAC,KAAK;EAC1H;EACA,MAAM;IAAEC,WAAW;IAAEC,eAAe;IAAEC;EAAa,CAAC,GAAGR,WAAW;EAClE;EACA,MAAM;IAAES,QAAQ;IAAEC;EAAU,CAAC,GAAGZ,iBAAiB,CAACG,WAAW,CAAC;EAC9D;EACA,MAAMU,eAAe,GAAGd,kBAAkB,CAACa,SAAS,EAAEP,aAAa,EAAEC,cAAc,CAAC;EACpF,OAAO;IACHE,WAAW;IACXK,eAAe;IACfF,QAAQ;IACRF,eAAe;IACfC,YAAY;IACZE,SAAS;IACTP,aAAa;IACbC,cAAc;IACdC;EACJ,CAAC;AACL,CAAC;AAED,SAASN,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}