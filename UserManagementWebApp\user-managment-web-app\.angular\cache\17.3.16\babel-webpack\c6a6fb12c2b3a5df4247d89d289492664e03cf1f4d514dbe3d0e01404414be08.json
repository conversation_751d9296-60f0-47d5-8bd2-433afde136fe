{"ast": null, "code": "import { assertPasskeyError, PasskeyErrorCode } from '../errors.mjs';\nexport { assertValidCredentialCreationOptions } from './shared.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction assertCredentialIsPkcWithAuthenticatorAttestationResponse(credential) {\n  assertPasskeyError(credential && credential instanceof PublicKeyCredential && credential.response instanceof AuthenticatorAttestationResponse, PasskeyErrorCode.PasskeyRegistrationFailed);\n}\nfunction assertCredentialIsPkcWithAuthenticatorAssertionResponse(credential) {\n  assertPasskeyError(credential && credential instanceof PublicKeyCredential && credential.response instanceof AuthenticatorAssertionResponse, PasskeyErrorCode.PasskeyRetrievalFailed);\n}\nexport { assertCredentialIsPkcWithAuthenticatorAssertionResponse, assertCredentialIsPkcWithAuthenticatorAttestationResponse };", "map": {"version": 3, "names": ["assertPasskeyError", "PasskeyErrorCode", "assertValidCredentialCreationOptions", "assertCredentialIsPkcWithAuthenticatorAttestationResponse", "credential", "PublicKeyCredential", "response", "AuthenticatorAttestationResponse", "PasskeyRegistrationFailed", "assertCredentialIsPkcWithAuthenticatorAssertionResponse", "AuthenticatorAssertionResponse", "PasskeyRetrievalFailed"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/utils/passkey/types/index.mjs"], "sourcesContent": ["import { assertPasskeyError, PasskeyErrorCode } from '../errors.mjs';\nexport { assertValidCredentialCreationOptions } from './shared.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction assertCredentialIsPkcWithAuthenticatorAttestationResponse(credential) {\n    assertPasskeyError(credential &&\n        credential instanceof PublicKeyCredential &&\n        credential.response instanceof AuthenticatorAttestationResponse, PasskeyErrorCode.PasskeyRegistrationFailed);\n}\nfunction assertCredentialIsPkcWithAuthenticatorAssertionResponse(credential) {\n    assertPasskeyError(credential &&\n        credential instanceof PublicKeyCredential &&\n        credential.response instanceof AuthenticatorAssertionResponse, PasskeyErrorCode.PasskeyRetrievalFailed);\n}\n\nexport { assertCredentialIsPkcWithAuthenticatorAssertionResponse, assertCredentialIsPkcWithAuthenticatorAttestationResponse };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,gBAAgB,QAAQ,eAAe;AACpE,SAASC,oCAAoC,QAAQ,cAAc;;AAEnE;AACA;AACA,SAASC,yDAAyDA,CAACC,UAAU,EAAE;EAC3EJ,kBAAkB,CAACI,UAAU,IACzBA,UAAU,YAAYC,mBAAmB,IACzCD,UAAU,CAACE,QAAQ,YAAYC,gCAAgC,EAAEN,gBAAgB,CAACO,yBAAyB,CAAC;AACpH;AACA,SAASC,uDAAuDA,CAACL,UAAU,EAAE;EACzEJ,kBAAkB,CAACI,UAAU,IACzBA,UAAU,YAAYC,mBAAmB,IACzCD,UAAU,CAACE,QAAQ,YAAYI,8BAA8B,EAAET,gBAAgB,CAACU,sBAAsB,CAAC;AAC/G;AAEA,SAASF,uDAAuD,EAAEN,yDAAyD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}