{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { GetLabelDetectionRequest, GetLabelDetectionResponse } from \"../models/models_0\";\nimport { deserializeAws_json1_1GetLabelDetectionCommand, serializeAws_json1_1GetLabelDetectionCommand } from \"../protocols/Aws_json1_1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Gets the label detection results of a Amazon Rekognition Video analysis started by <a>StartLabelDetection</a>.  </p>\n *\n *          <p>The label detection operation is started by a call to <a>StartLabelDetection</a>\n *       which returns a job identifier (<code>JobId</code>). When the label detection operation finishes, Amazon Rekognition publishes a completion status to\n *       the Amazon Simple Notification Service topic registered in the initial call to <code>StartlabelDetection</code>. To get the results\n *       of the label detection operation, first check that the status value published to the Amazon SNS topic is <code>SUCCEEDED</code>.\n *       If so, call  <a>GetLabelDetection</a> and pass the job identifier\n *       (<code>JobId</code>) from the initial call to <code>StartLabelDetection</code>.</p>\n *          <p>\n *             <code>GetLabelDetection</code> returns an array of detected labels (<code>Labels</code>) sorted by the time\n *        the labels were detected. You can also sort by the label name by specifying <code>NAME</code> for the\n *        <code>SortBy</code> input parameter.</p>\n *          <p>The labels returned include the label name, the percentage confidence in the accuracy of the detected label,\n *         and the time the label was detected in the video.</p>\n *          <p>The returned labels also include bounding box information for common objects, a\n *        hierarchical taxonomy of detected labels, and the version of the label model used for detection.</p>\n *\n *          <p>Use MaxResults parameter to limit the number of labels returned. If there are more results than\n *     specified in <code>MaxResults</code>, the value of <code>NextToken</code> in the operation response contains a pagination token for getting the next set\n *     of results. To get the next page of results, call <code>GetlabelDetection</code> and populate the <code>NextToken</code> request parameter with the token\n *      value returned from the previous call to <code>GetLabelDetection</code>.</p>\n */\nvar GetLabelDetectionCommand = /** @class */function (_super) {\n  __extends(GetLabelDetectionCommand, _super);\n  // Start section: command_properties\n  // End section: command_properties\n  function GetLabelDetectionCommand(input) {\n    var _this =\n    // Start section: command_constructor\n    _super.call(this) || this;\n    _this.input = input;\n    return _this;\n    // End section: command_constructor\n  }\n  /**\n   * @internal\n   */\n  GetLabelDetectionCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n    this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n    var stack = clientStack.concat(this.middlewareStack);\n    var logger = configuration.logger;\n    var clientName = \"RekognitionClient\";\n    var commandName = \"GetLabelDetectionCommand\";\n    var handlerExecutionContext = {\n      logger: logger,\n      clientName: clientName,\n      commandName: commandName,\n      inputFilterSensitiveLog: GetLabelDetectionRequest.filterSensitiveLog,\n      outputFilterSensitiveLog: GetLabelDetectionResponse.filterSensitiveLog\n    };\n    var requestHandler = configuration.requestHandler;\n    return stack.resolve(function (request) {\n      return requestHandler.handle(request.request, options || {});\n    }, handlerExecutionContext);\n  };\n  GetLabelDetectionCommand.prototype.serialize = function (input, context) {\n    return serializeAws_json1_1GetLabelDetectionCommand(input, context);\n  };\n  GetLabelDetectionCommand.prototype.deserialize = function (output, context) {\n    return deserializeAws_json1_1GetLabelDetectionCommand(output, context);\n  };\n  return GetLabelDetectionCommand;\n}($Command);\nexport { GetLabelDetectionCommand };", "map": {"version": 3, "names": ["__extends", "GetLabelDetectionRequest", "GetLabelDetectionResponse", "deserializeAws_json1_1GetLabelDetectionCommand", "serializeAws_json1_1GetLabelDetectionCommand", "getSerdePlugin", "Command", "$Command", "GetLabelDetectionCommand", "_super", "input", "_this", "call", "prototype", "resolveMiddleware", "clientStack", "configuration", "options", "middlewareStack", "use", "serialize", "deserialize", "stack", "concat", "logger", "clientName", "commandName", "handlerExecutionContext", "inputFilterSensitiveLog", "filterSensitiveLog", "outputFilterSensitiveLog", "requestHandler", "resolve", "request", "handle", "context", "output"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-rekognition/dist/es/commands/GetLabelDetectionCommand.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { GetLabelDetectionRequest, GetLabelDetectionResponse } from \"../models/models_0\";\nimport { deserializeAws_json1_1GetLabelDetectionCommand, serializeAws_json1_1GetLabelDetectionCommand, } from \"../protocols/Aws_json1_1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Gets the label detection results of a Amazon Rekognition Video analysis started by <a>StartLabelDetection</a>.  </p>\n *\n *          <p>The label detection operation is started by a call to <a>StartLabelDetection</a>\n *       which returns a job identifier (<code>JobId</code>). When the label detection operation finishes, Amazon Rekognition publishes a completion status to\n *       the Amazon Simple Notification Service topic registered in the initial call to <code>StartlabelDetection</code>. To get the results\n *       of the label detection operation, first check that the status value published to the Amazon SNS topic is <code>SUCCEEDED</code>.\n *       If so, call  <a>GetLabelDetection</a> and pass the job identifier\n *       (<code>JobId</code>) from the initial call to <code>StartLabelDetection</code>.</p>\n *          <p>\n *             <code>GetLabelDetection</code> returns an array of detected labels (<code>Labels</code>) sorted by the time\n *        the labels were detected. You can also sort by the label name by specifying <code>NAME</code> for the\n *        <code>SortBy</code> input parameter.</p>\n *          <p>The labels returned include the label name, the percentage confidence in the accuracy of the detected label,\n *         and the time the label was detected in the video.</p>\n *          <p>The returned labels also include bounding box information for common objects, a\n *        hierarchical taxonomy of detected labels, and the version of the label model used for detection.</p>\n *\n *          <p>Use MaxResults parameter to limit the number of labels returned. If there are more results than\n *     specified in <code>MaxResults</code>, the value of <code>NextToken</code> in the operation response contains a pagination token for getting the next set\n *     of results. To get the next page of results, call <code>GetlabelDetection</code> and populate the <code>NextToken</code> request parameter with the token\n *      value returned from the previous call to <code>GetLabelDetection</code>.</p>\n */\nvar GetLabelDetectionCommand = /** @class */ (function (_super) {\n    __extends(GetLabelDetectionCommand, _super);\n    // Start section: command_properties\n    // End section: command_properties\n    function GetLabelDetectionCommand(input) {\n        var _this = \n        // Start section: command_constructor\n        _super.call(this) || this;\n        _this.input = input;\n        return _this;\n        // End section: command_constructor\n    }\n    /**\n     * @internal\n     */\n    GetLabelDetectionCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n        this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n        var stack = clientStack.concat(this.middlewareStack);\n        var logger = configuration.logger;\n        var clientName = \"RekognitionClient\";\n        var commandName = \"GetLabelDetectionCommand\";\n        var handlerExecutionContext = {\n            logger: logger,\n            clientName: clientName,\n            commandName: commandName,\n            inputFilterSensitiveLog: GetLabelDetectionRequest.filterSensitiveLog,\n            outputFilterSensitiveLog: GetLabelDetectionResponse.filterSensitiveLog,\n        };\n        var requestHandler = configuration.requestHandler;\n        return stack.resolve(function (request) {\n            return requestHandler.handle(request.request, options || {});\n        }, handlerExecutionContext);\n    };\n    GetLabelDetectionCommand.prototype.serialize = function (input, context) {\n        return serializeAws_json1_1GetLabelDetectionCommand(input, context);\n    };\n    GetLabelDetectionCommand.prototype.deserialize = function (output, context) {\n        return deserializeAws_json1_1GetLabelDetectionCommand(output, context);\n    };\n    return GetLabelDetectionCommand;\n}($Command));\nexport { GetLabelDetectionCommand };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,wBAAwB,EAAEC,yBAAyB,QAAQ,oBAAoB;AACxF,SAASC,8CAA8C,EAAEC,4CAA4C,QAAS,0BAA0B;AACxI,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,OAAO,IAAIC,QAAQ,QAAQ,wBAAwB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,wBAAwB,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC5DT,SAAS,CAACQ,wBAAwB,EAAEC,MAAM,CAAC;EAC3C;EACA;EACA,SAASD,wBAAwBA,CAACE,KAAK,EAAE;IACrC,IAAIC,KAAK;IACT;IACAF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACzBD,KAAK,CAACD,KAAK,GAAGA,KAAK;IACnB,OAAOC,KAAK;IACZ;EACJ;EACA;AACJ;AACA;EACIH,wBAAwB,CAACK,SAAS,CAACC,iBAAiB,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAEC,OAAO,EAAE;IAClG,IAAI,CAACC,eAAe,CAACC,GAAG,CAACd,cAAc,CAACW,aAAa,EAAE,IAAI,CAACI,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;IACzF,IAAIC,KAAK,GAAGP,WAAW,CAACQ,MAAM,CAAC,IAAI,CAACL,eAAe,CAAC;IACpD,IAAIM,MAAM,GAAGR,aAAa,CAACQ,MAAM;IACjC,IAAIC,UAAU,GAAG,mBAAmB;IACpC,IAAIC,WAAW,GAAG,0BAA0B;IAC5C,IAAIC,uBAAuB,GAAG;MAC1BH,MAAM,EAAEA,MAAM;MACdC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA,WAAW;MACxBE,uBAAuB,EAAE3B,wBAAwB,CAAC4B,kBAAkB;MACpEC,wBAAwB,EAAE5B,yBAAyB,CAAC2B;IACxD,CAAC;IACD,IAAIE,cAAc,GAAGf,aAAa,CAACe,cAAc;IACjD,OAAOT,KAAK,CAACU,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpC,OAAOF,cAAc,CAACG,MAAM,CAACD,OAAO,CAACA,OAAO,EAAEhB,OAAO,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC,EAAEU,uBAAuB,CAAC;EAC/B,CAAC;EACDnB,wBAAwB,CAACK,SAAS,CAACO,SAAS,GAAG,UAAUV,KAAK,EAAEyB,OAAO,EAAE;IACrE,OAAO/B,4CAA4C,CAACM,KAAK,EAAEyB,OAAO,CAAC;EACvE,CAAC;EACD3B,wBAAwB,CAACK,SAAS,CAACQ,WAAW,GAAG,UAAUe,MAAM,EAAED,OAAO,EAAE;IACxE,OAAOhC,8CAA8C,CAACiC,MAAM,EAAED,OAAO,CAAC;EAC1E,CAAC;EACD,OAAO3B,wBAAwB;AACnC,CAAC,CAACD,QAAQ,CAAE;AACZ,SAASC,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}