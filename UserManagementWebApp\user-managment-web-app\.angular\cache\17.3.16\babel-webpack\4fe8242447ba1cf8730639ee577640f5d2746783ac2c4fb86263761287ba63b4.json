{"ast": null, "code": "import { retryMiddlewareFactory } from '../../middleware/retry/retryMiddleware.mjs';\nimport '../../../types/errors.mjs';\nimport '../../../errors/errorHelpers.mjs';\nimport '../../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../../utils/retry/retry.mjs';\nimport { amzSdkInvocationIdHeaderMiddlewareFactory } from '../../middleware/retry/amzSdkInvocationIdHeaderMiddleware.mjs';\nimport { amzSdkRequestHeaderMiddlewareFactory } from '../../middleware/retry/amzSdkRequestHeaderMiddleware.mjs';\nimport { userAgentMiddlewareFactory } from '../../middleware/userAgent/middleware.mjs';\nimport { composeTransferHandler } from '../../internal/composeTransferHandler.mjs';\nimport { fetchTransferHandler } from '../fetch.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst unauthenticatedHandler = composeTransferHandler(fetchTransferHandler, [userAgentMiddlewareFactory, amzSdkInvocationIdHeaderMiddlewareFactory, retryMiddlewareFactory, amzSdkRequestHeaderMiddlewareFactory]);\nexport { unauthenticatedHandler };", "map": {"version": 3, "names": ["retryMiddlewareFactory", "amzSdkInvocationIdHeaderMiddlewareFactory", "amzSdkRequestHeaderMiddlewareFactory", "userAgentMiddlewareFactory", "composeTransferHandler", "fetchTransferHandler", "unauthentica<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/handlers/aws/unauthenticated.mjs"], "sourcesContent": ["import { retryMiddlewareFactory } from '../../middleware/retry/retryMiddleware.mjs';\nimport '../../../types/errors.mjs';\nimport '../../../errors/errorHelpers.mjs';\nimport '../../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../../utils/retry/retry.mjs';\nimport { amzSdkInvocationIdHeaderMiddlewareFactory } from '../../middleware/retry/amzSdkInvocationIdHeaderMiddleware.mjs';\nimport { amzSdkRequestHeaderMiddlewareFactory } from '../../middleware/retry/amzSdkRequestHeaderMiddleware.mjs';\nimport { userAgentMiddlewareFactory } from '../../middleware/userAgent/middleware.mjs';\nimport { composeTransferHandler } from '../../internal/composeTransferHandler.mjs';\nimport { fetchTransferHandler } from '../fetch.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst unauthenticatedHandler = composeTransferHandler(fetchTransferHandler, [\n    userAgentMiddlewareFactory,\n    amzSdkInvocationIdHeaderMiddlewareFactory,\n    retryMiddlewareFactory,\n    amzSdkRequestHeaderMiddlewareFactory,\n]);\n\nexport { unauthenticatedHandler };\n"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,4CAA4C;AACnF,OAAO,2BAA2B;AAClC,OAAO,kCAAkC;AACzC,OAAO,gDAAgD;AACvD,OAAO,gCAAgC;AACvC,SAASC,yCAAyC,QAAQ,+DAA+D;AACzH,SAASC,oCAAoC,QAAQ,0DAA0D;AAC/G,SAASC,0BAA0B,QAAQ,2CAA2C;AACtF,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,oBAAoB,QAAQ,cAAc;;AAEnD;AACA;AACA,MAAMC,sBAAsB,GAAGF,sBAAsB,CAACC,oBAAoB,EAAE,CACxEF,0BAA0B,EAC1BF,yCAAyC,EACzCD,sBAAsB,EACtBE,oCAAoC,CACvC,CAAC;AAEF,SAASI,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}