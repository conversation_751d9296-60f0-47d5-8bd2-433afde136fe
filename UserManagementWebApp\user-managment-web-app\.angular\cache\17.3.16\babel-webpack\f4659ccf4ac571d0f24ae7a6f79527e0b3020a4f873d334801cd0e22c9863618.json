{"ast": null, "code": "import objectValues from \"../polyfills/objectValues.mjs\";\nimport keyMap from \"../jsutils/keyMap.mjs\";\nimport inspect from \"../jsutils/inspect.mjs\";\nimport invariant from \"../jsutils/invariant.mjs\";\nimport { Kind } from \"../language/kinds.mjs\";\nimport { isLeafType, isInputObjectType, isListType, isNonNullType } from \"../type/definition.mjs\";\n/**\n * Produces a JavaScript value given a GraphQL Value AST.\n *\n * A GraphQL type must be provided, which will be used to interpret different\n * GraphQL Value literals.\n *\n * Returns `undefined` when the value could not be validly coerced according to\n * the provided type.\n *\n * | GraphQL Value        | JSON Value    |\n * | -------------------- | ------------- |\n * | Input Object         | Object        |\n * | List                 | Array         |\n * | Boolean              | Boolean       |\n * | String               | String        |\n * | Int / Float          | Number        |\n * | Enum Value           | Mixed         |\n * | NullValue            | null          |\n *\n */\n\nexport function valueFromAST(valueNode, type, variables) {\n  if (!valueNode) {\n    // When there is no node, then there is also no value.\n    // Importantly, this is different from returning the value null.\n    return;\n  }\n  if (valueNode.kind === Kind.VARIABLE) {\n    var variableName = valueNode.name.value;\n    if (variables == null || variables[variableName] === undefined) {\n      // No valid return value.\n      return;\n    }\n    var variableValue = variables[variableName];\n    if (variableValue === null && isNonNullType(type)) {\n      return; // Invalid: intentionally return no value.\n    } // Note: This does no further checking that this variable is correct.\n    // This assumes that this query has been validated and the variable\n    // usage here is of the correct type.\n\n    return variableValue;\n  }\n  if (isNonNullType(type)) {\n    if (valueNode.kind === Kind.NULL) {\n      return; // Invalid: intentionally return no value.\n    }\n    return valueFromAST(valueNode, type.ofType, variables);\n  }\n  if (valueNode.kind === Kind.NULL) {\n    // This is explicitly returning the value null.\n    return null;\n  }\n  if (isListType(type)) {\n    var itemType = type.ofType;\n    if (valueNode.kind === Kind.LIST) {\n      var coercedValues = [];\n      for (var _i2 = 0, _valueNode$values2 = valueNode.values; _i2 < _valueNode$values2.length; _i2++) {\n        var itemNode = _valueNode$values2[_i2];\n        if (isMissingVariable(itemNode, variables)) {\n          // If an array contains a missing variable, it is either coerced to\n          // null or if the item type is non-null, it considered invalid.\n          if (isNonNullType(itemType)) {\n            return; // Invalid: intentionally return no value.\n          }\n          coercedValues.push(null);\n        } else {\n          var itemValue = valueFromAST(itemNode, itemType, variables);\n          if (itemValue === undefined) {\n            return; // Invalid: intentionally return no value.\n          }\n          coercedValues.push(itemValue);\n        }\n      }\n      return coercedValues;\n    }\n    var coercedValue = valueFromAST(valueNode, itemType, variables);\n    if (coercedValue === undefined) {\n      return; // Invalid: intentionally return no value.\n    }\n    return [coercedValue];\n  }\n  if (isInputObjectType(type)) {\n    if (valueNode.kind !== Kind.OBJECT) {\n      return; // Invalid: intentionally return no value.\n    }\n    var coercedObj = Object.create(null);\n    var fieldNodes = keyMap(valueNode.fields, function (field) {\n      return field.name.value;\n    });\n    for (var _i4 = 0, _objectValues2 = objectValues(type.getFields()); _i4 < _objectValues2.length; _i4++) {\n      var field = _objectValues2[_i4];\n      var fieldNode = fieldNodes[field.name];\n      if (!fieldNode || isMissingVariable(fieldNode.value, variables)) {\n        if (field.defaultValue !== undefined) {\n          coercedObj[field.name] = field.defaultValue;\n        } else if (isNonNullType(field.type)) {\n          return; // Invalid: intentionally return no value.\n        }\n        continue;\n      }\n      var fieldValue = valueFromAST(fieldNode.value, field.type, variables);\n      if (fieldValue === undefined) {\n        return; // Invalid: intentionally return no value.\n      }\n      coercedObj[field.name] = fieldValue;\n    }\n    return coercedObj;\n  } // istanbul ignore else (See: 'https://github.com/graphql/graphql-js/issues/2618')\n\n  if (isLeafType(type)) {\n    // Scalars and Enums fulfill parsing a literal value via parseLiteral().\n    // Invalid values represent a failure to parse correctly, in which case\n    // no value is returned.\n    var result;\n    try {\n      result = type.parseLiteral(valueNode, variables);\n    } catch (_error) {\n      return; // Invalid: intentionally return no value.\n    }\n    if (result === undefined) {\n      return; // Invalid: intentionally return no value.\n    }\n    return result;\n  } // istanbul ignore next (Not reachable. All possible input types have been considered)\n\n  false || invariant(0, 'Unexpected input type: ' + inspect(type));\n} // Returns true if the provided valueNode is a variable which is not defined\n// in the set of variables.\n\nfunction isMissingVariable(valueNode, variables) {\n  return valueNode.kind === Kind.VARIABLE && (variables == null || variables[valueNode.name.value] === undefined);\n}", "map": {"version": 3, "names": ["objectValues", "keyMap", "inspect", "invariant", "Kind", "isLeafType", "isInputObjectType", "isListType", "isNonNullType", "valueFromAST", "valueNode", "type", "variables", "kind", "VARIABLE", "variableName", "name", "value", "undefined", "variableValue", "NULL", "ofType", "itemType", "LIST", "coer<PERSON><PERSON><PERSON><PERSON>", "_i2", "_valueNode$values2", "values", "length", "itemNode", "isMissingVariable", "push", "itemValue", "coerced<PERSON><PERSON><PERSON>", "OBJECT", "coerced<PERSON><PERSON>j", "Object", "create", "fieldNodes", "fields", "field", "_i4", "_objectValues2", "getFields", "fieldNode", "defaultValue", "fieldValue", "result", "parseLiteral", "_error"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/graphql/utilities/valueFromAST.mjs"], "sourcesContent": ["import objectValues from \"../polyfills/objectValues.mjs\";\nimport keyMap from \"../jsutils/keyMap.mjs\";\nimport inspect from \"../jsutils/inspect.mjs\";\nimport invariant from \"../jsutils/invariant.mjs\";\nimport { Kind } from \"../language/kinds.mjs\";\nimport { isLeafType, isInputObjectType, isListType, isNonNullType } from \"../type/definition.mjs\";\n/**\n * Produces a JavaScript value given a GraphQL Value AST.\n *\n * A GraphQL type must be provided, which will be used to interpret different\n * GraphQL Value literals.\n *\n * Returns `undefined` when the value could not be validly coerced according to\n * the provided type.\n *\n * | GraphQL Value        | JSON Value    |\n * | -------------------- | ------------- |\n * | Input Object         | Object        |\n * | List                 | Array         |\n * | Boolean              | Boolean       |\n * | String               | String        |\n * | Int / Float          | Number        |\n * | Enum Value           | Mixed         |\n * | NullValue            | null          |\n *\n */\n\nexport function valueFromAST(valueNode, type, variables) {\n  if (!valueNode) {\n    // When there is no node, then there is also no value.\n    // Importantly, this is different from returning the value null.\n    return;\n  }\n\n  if (valueNode.kind === Kind.VARIABLE) {\n    var variableName = valueNode.name.value;\n\n    if (variables == null || variables[variableName] === undefined) {\n      // No valid return value.\n      return;\n    }\n\n    var variableValue = variables[variableName];\n\n    if (variableValue === null && isNonNullType(type)) {\n      return; // Invalid: intentionally return no value.\n    } // Note: This does no further checking that this variable is correct.\n    // This assumes that this query has been validated and the variable\n    // usage here is of the correct type.\n\n\n    return variableValue;\n  }\n\n  if (isNonNullType(type)) {\n    if (valueNode.kind === Kind.NULL) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    return valueFromAST(valueNode, type.ofType, variables);\n  }\n\n  if (valueNode.kind === Kind.NULL) {\n    // This is explicitly returning the value null.\n    return null;\n  }\n\n  if (isListType(type)) {\n    var itemType = type.ofType;\n\n    if (valueNode.kind === Kind.LIST) {\n      var coercedValues = [];\n\n      for (var _i2 = 0, _valueNode$values2 = valueNode.values; _i2 < _valueNode$values2.length; _i2++) {\n        var itemNode = _valueNode$values2[_i2];\n\n        if (isMissingVariable(itemNode, variables)) {\n          // If an array contains a missing variable, it is either coerced to\n          // null or if the item type is non-null, it considered invalid.\n          if (isNonNullType(itemType)) {\n            return; // Invalid: intentionally return no value.\n          }\n\n          coercedValues.push(null);\n        } else {\n          var itemValue = valueFromAST(itemNode, itemType, variables);\n\n          if (itemValue === undefined) {\n            return; // Invalid: intentionally return no value.\n          }\n\n          coercedValues.push(itemValue);\n        }\n      }\n\n      return coercedValues;\n    }\n\n    var coercedValue = valueFromAST(valueNode, itemType, variables);\n\n    if (coercedValue === undefined) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    return [coercedValue];\n  }\n\n  if (isInputObjectType(type)) {\n    if (valueNode.kind !== Kind.OBJECT) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    var coercedObj = Object.create(null);\n    var fieldNodes = keyMap(valueNode.fields, function (field) {\n      return field.name.value;\n    });\n\n    for (var _i4 = 0, _objectValues2 = objectValues(type.getFields()); _i4 < _objectValues2.length; _i4++) {\n      var field = _objectValues2[_i4];\n      var fieldNode = fieldNodes[field.name];\n\n      if (!fieldNode || isMissingVariable(fieldNode.value, variables)) {\n        if (field.defaultValue !== undefined) {\n          coercedObj[field.name] = field.defaultValue;\n        } else if (isNonNullType(field.type)) {\n          return; // Invalid: intentionally return no value.\n        }\n\n        continue;\n      }\n\n      var fieldValue = valueFromAST(fieldNode.value, field.type, variables);\n\n      if (fieldValue === undefined) {\n        return; // Invalid: intentionally return no value.\n      }\n\n      coercedObj[field.name] = fieldValue;\n    }\n\n    return coercedObj;\n  } // istanbul ignore else (See: 'https://github.com/graphql/graphql-js/issues/2618')\n\n\n  if (isLeafType(type)) {\n    // Scalars and Enums fulfill parsing a literal value via parseLiteral().\n    // Invalid values represent a failure to parse correctly, in which case\n    // no value is returned.\n    var result;\n\n    try {\n      result = type.parseLiteral(valueNode, variables);\n    } catch (_error) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    if (result === undefined) {\n      return; // Invalid: intentionally return no value.\n    }\n\n    return result;\n  } // istanbul ignore next (Not reachable. All possible input types have been considered)\n\n\n  false || invariant(0, 'Unexpected input type: ' + inspect(type));\n} // Returns true if the provided valueNode is a variable which is not defined\n// in the set of variables.\n\nfunction isMissingVariable(valueNode, variables) {\n  return valueNode.kind === Kind.VARIABLE && (variables == null || variables[valueNode.name.value] === undefined);\n}\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,OAAO,MAAM,wBAAwB;AAC5C,OAAOC,SAAS,MAAM,0BAA0B;AAChD,SAASC,IAAI,QAAQ,uBAAuB;AAC5C,SAASC,UAAU,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,aAAa,QAAQ,wBAAwB;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,YAAYA,CAACC,SAAS,EAAEC,IAAI,EAAEC,SAAS,EAAE;EACvD,IAAI,CAACF,SAAS,EAAE;IACd;IACA;IACA;EACF;EAEA,IAAIA,SAAS,CAACG,IAAI,KAAKT,IAAI,CAACU,QAAQ,EAAE;IACpC,IAAIC,YAAY,GAAGL,SAAS,CAACM,IAAI,CAACC,KAAK;IAEvC,IAAIL,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACG,YAAY,CAAC,KAAKG,SAAS,EAAE;MAC9D;MACA;IACF;IAEA,IAAIC,aAAa,GAAGP,SAAS,CAACG,YAAY,CAAC;IAE3C,IAAII,aAAa,KAAK,IAAI,IAAIX,aAAa,CAACG,IAAI,CAAC,EAAE;MACjD,OAAO,CAAC;IACV,CAAC,CAAC;IACF;IACA;;IAGA,OAAOQ,aAAa;EACtB;EAEA,IAAIX,aAAa,CAACG,IAAI,CAAC,EAAE;IACvB,IAAID,SAAS,CAACG,IAAI,KAAKT,IAAI,CAACgB,IAAI,EAAE;MAChC,OAAO,CAAC;IACV;IAEA,OAAOX,YAAY,CAACC,SAAS,EAAEC,IAAI,CAACU,MAAM,EAAET,SAAS,CAAC;EACxD;EAEA,IAAIF,SAAS,CAACG,IAAI,KAAKT,IAAI,CAACgB,IAAI,EAAE;IAChC;IACA,OAAO,IAAI;EACb;EAEA,IAAIb,UAAU,CAACI,IAAI,CAAC,EAAE;IACpB,IAAIW,QAAQ,GAAGX,IAAI,CAACU,MAAM;IAE1B,IAAIX,SAAS,CAACG,IAAI,KAAKT,IAAI,CAACmB,IAAI,EAAE;MAChC,IAAIC,aAAa,GAAG,EAAE;MAEtB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEC,kBAAkB,GAAGhB,SAAS,CAACiB,MAAM,EAAEF,GAAG,GAAGC,kBAAkB,CAACE,MAAM,EAAEH,GAAG,EAAE,EAAE;QAC/F,IAAII,QAAQ,GAAGH,kBAAkB,CAACD,GAAG,CAAC;QAEtC,IAAIK,iBAAiB,CAACD,QAAQ,EAAEjB,SAAS,CAAC,EAAE;UAC1C;UACA;UACA,IAAIJ,aAAa,CAACc,QAAQ,CAAC,EAAE;YAC3B,OAAO,CAAC;UACV;UAEAE,aAAa,CAACO,IAAI,CAAC,IAAI,CAAC;QAC1B,CAAC,MAAM;UACL,IAAIC,SAAS,GAAGvB,YAAY,CAACoB,QAAQ,EAAEP,QAAQ,EAAEV,SAAS,CAAC;UAE3D,IAAIoB,SAAS,KAAKd,SAAS,EAAE;YAC3B,OAAO,CAAC;UACV;UAEAM,aAAa,CAACO,IAAI,CAACC,SAAS,CAAC;QAC/B;MACF;MAEA,OAAOR,aAAa;IACtB;IAEA,IAAIS,YAAY,GAAGxB,YAAY,CAACC,SAAS,EAAEY,QAAQ,EAAEV,SAAS,CAAC;IAE/D,IAAIqB,YAAY,KAAKf,SAAS,EAAE;MAC9B,OAAO,CAAC;IACV;IAEA,OAAO,CAACe,YAAY,CAAC;EACvB;EAEA,IAAI3B,iBAAiB,CAACK,IAAI,CAAC,EAAE;IAC3B,IAAID,SAAS,CAACG,IAAI,KAAKT,IAAI,CAAC8B,MAAM,EAAE;MAClC,OAAO,CAAC;IACV;IAEA,IAAIC,UAAU,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACpC,IAAIC,UAAU,GAAGrC,MAAM,CAACS,SAAS,CAAC6B,MAAM,EAAE,UAAUC,KAAK,EAAE;MACzD,OAAOA,KAAK,CAACxB,IAAI,CAACC,KAAK;IACzB,CAAC,CAAC;IAEF,KAAK,IAAIwB,GAAG,GAAG,CAAC,EAAEC,cAAc,GAAG1C,YAAY,CAACW,IAAI,CAACgC,SAAS,CAAC,CAAC,CAAC,EAAEF,GAAG,GAAGC,cAAc,CAACd,MAAM,EAAEa,GAAG,EAAE,EAAE;MACrG,IAAID,KAAK,GAAGE,cAAc,CAACD,GAAG,CAAC;MAC/B,IAAIG,SAAS,GAAGN,UAAU,CAACE,KAAK,CAACxB,IAAI,CAAC;MAEtC,IAAI,CAAC4B,SAAS,IAAId,iBAAiB,CAACc,SAAS,CAAC3B,KAAK,EAAEL,SAAS,CAAC,EAAE;QAC/D,IAAI4B,KAAK,CAACK,YAAY,KAAK3B,SAAS,EAAE;UACpCiB,UAAU,CAACK,KAAK,CAACxB,IAAI,CAAC,GAAGwB,KAAK,CAACK,YAAY;QAC7C,CAAC,MAAM,IAAIrC,aAAa,CAACgC,KAAK,CAAC7B,IAAI,CAAC,EAAE;UACpC,OAAO,CAAC;QACV;QAEA;MACF;MAEA,IAAImC,UAAU,GAAGrC,YAAY,CAACmC,SAAS,CAAC3B,KAAK,EAAEuB,KAAK,CAAC7B,IAAI,EAAEC,SAAS,CAAC;MAErE,IAAIkC,UAAU,KAAK5B,SAAS,EAAE;QAC5B,OAAO,CAAC;MACV;MAEAiB,UAAU,CAACK,KAAK,CAACxB,IAAI,CAAC,GAAG8B,UAAU;IACrC;IAEA,OAAOX,UAAU;EACnB,CAAC,CAAC;;EAGF,IAAI9B,UAAU,CAACM,IAAI,CAAC,EAAE;IACpB;IACA;IACA;IACA,IAAIoC,MAAM;IAEV,IAAI;MACFA,MAAM,GAAGpC,IAAI,CAACqC,YAAY,CAACtC,SAAS,EAAEE,SAAS,CAAC;IAClD,CAAC,CAAC,OAAOqC,MAAM,EAAE;MACf,OAAO,CAAC;IACV;IAEA,IAAIF,MAAM,KAAK7B,SAAS,EAAE;MACxB,OAAO,CAAC;IACV;IAEA,OAAO6B,MAAM;EACf,CAAC,CAAC;;EAGF,KAAK,IAAI5C,SAAS,CAAC,CAAC,EAAE,yBAAyB,GAAGD,OAAO,CAACS,IAAI,CAAC,CAAC;AAClE,CAAC,CAAC;AACF;;AAEA,SAASmB,iBAAiBA,CAACpB,SAAS,EAAEE,SAAS,EAAE;EAC/C,OAAOF,SAAS,CAACG,IAAI,KAAKT,IAAI,CAACU,QAAQ,KAAKF,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACF,SAAS,CAACM,IAAI,CAACC,KAAK,CAAC,KAAKC,SAAS,CAAC;AACjH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}