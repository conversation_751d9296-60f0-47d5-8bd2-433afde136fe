{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AmplifyUrl, USER_AGENT_HEADER, urlSafeDecode, AMPLIFY_SYMBOL } from '@aws-amplify/core/internals/utils';\nimport { decodeJWT, Hub } from '@aws-amplify/core';\nimport { cacheCognitoTokens } from '../../tokenProvider/cacheTokens.mjs';\nimport { dispatchSignedInHubEvent } from '../dispatchSignedInHubEvent.mjs';\nimport '../refreshAuthTokens.mjs';\nimport '../../tokenProvider/errorHelpers.mjs';\nimport { oAuthStore } from './oAuthStore.mjs';\nimport { resolveAndClearInflightPromises } from './inflightPromise.mjs';\nimport { tokenOrchestrator } from '../../tokenProvider/tokenProvider.mjs';\nimport { createOAuthError } from './createOAuthError.mjs';\nimport { validateState } from './validateState.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst completeOAuthFlow = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* ({\n    currentUrl,\n    userAgentValue,\n    clientId,\n    redirectUri,\n    responseType,\n    domain,\n    preferPrivateSession\n  }) {\n    const urlParams = new AmplifyUrl(currentUrl);\n    const error = urlParams.searchParams.get('error');\n    const errorMessage = urlParams.searchParams.get('error_description');\n    if (error) {\n      throw createOAuthError(errorMessage ?? error);\n    }\n    if (responseType === 'code') {\n      return handleCodeFlow({\n        currentUrl,\n        userAgentValue,\n        clientId,\n        redirectUri,\n        domain,\n        preferPrivateSession\n      });\n    }\n    return handleImplicitFlow({\n      currentUrl,\n      redirectUri,\n      preferPrivateSession\n    });\n  });\n  return function completeOAuthFlow(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nconst handleCodeFlow = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(function* ({\n    currentUrl,\n    userAgentValue,\n    clientId,\n    redirectUri,\n    domain,\n    preferPrivateSession\n  }) {\n    /* Convert URL into an object with parameters as keys\n    { redirect_uri: 'http://localhost:3000/', response_type: 'code', ...} */\n    const url = new AmplifyUrl(currentUrl);\n    const code = url.searchParams.get('code');\n    const state = url.searchParams.get('state');\n    // if `code` or `state` is not presented in the redirect url, most likely\n    // that the end user cancelled the inflight oauth flow by:\n    // 1. clicking the back button of browser\n    // 2. closing the provider hosted UI page and coming back to the app\n    if (!code || !state) {\n      throw createOAuthError('User cancelled OAuth flow.');\n    }\n    // may throw error is being caught in attemptCompleteOAuthFlow.ts\n    const validatedState = yield validateState(state);\n    const oAuthTokenEndpoint = 'https://' + domain + '/oauth2/token';\n    // TODO(v6): check hub events\n    // dispatchAuthEvent(\n    // \t'codeFlow',\n    // \t{},\n    // \t`Retrieving tokens from ${oAuthTokenEndpoint}`\n    // );\n    const codeVerifier = yield oAuthStore.loadPKCE();\n    const oAuthTokenBody = {\n      grant_type: 'authorization_code',\n      code,\n      client_id: clientId,\n      redirect_uri: redirectUri,\n      ...(codeVerifier ? {\n        code_verifier: codeVerifier\n      } : {})\n    };\n    const body = Object.entries(oAuthTokenBody).map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(v)}`).join('&');\n    const {\n      access_token,\n      refresh_token: refreshToken,\n      id_token,\n      error,\n      error_message: errorMessage,\n      token_type,\n      expires_in\n    } = yield (yield fetch(oAuthTokenEndpoint, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n        [USER_AGENT_HEADER]: userAgentValue\n      },\n      body\n    })).json();\n    if (error) {\n      // error is being caught in attemptCompleteOAuthFlow.ts\n      throw createOAuthError(errorMessage ?? error);\n    }\n    const username = (access_token && decodeJWT(access_token).payload.username) ?? 'username';\n    yield cacheCognitoTokens({\n      username,\n      AccessToken: access_token,\n      IdToken: id_token,\n      RefreshToken: refreshToken\n    });\n    return completeFlow({\n      redirectUri,\n      state: validatedState,\n      preferPrivateSession\n    });\n  });\n  return function handleCodeFlow(_x2) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nconst handleImplicitFlow = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(function* ({\n    currentUrl,\n    redirectUri,\n    preferPrivateSession\n  }) {\n    // hash is `null` if `#` doesn't exist on URL\n    const url = new AmplifyUrl(currentUrl);\n    const {\n      id_token,\n      access_token,\n      state,\n      token_type,\n      expires_in,\n      error_description,\n      error\n    } = (url.hash ?? '#').substring(1) // Remove # from returned code\n    .split('&').map(pairings => pairings.split('=')).reduce((accum, [k, v]) => ({\n      ...accum,\n      [k]: v\n    }), {\n      id_token: undefined,\n      access_token: undefined,\n      state: undefined,\n      token_type: undefined,\n      expires_in: undefined,\n      error_description: undefined,\n      error: undefined\n    });\n    if (error) {\n      throw createOAuthError(error_description ?? error);\n    }\n    if (!access_token) {\n      // error is being caught in attemptCompleteOAuthFlow.ts\n      throw createOAuthError('No access token returned from OAuth flow.');\n    }\n    const validatedState = yield validateState(state);\n    const username = (access_token && decodeJWT(access_token).payload.username) ?? 'username';\n    yield cacheCognitoTokens({\n      username,\n      AccessToken: access_token,\n      IdToken: id_token\n    });\n    return completeFlow({\n      redirectUri,\n      state: validatedState,\n      preferPrivateSession\n    });\n  });\n  return function handleImplicitFlow(_x3) {\n    return _ref3.apply(this, arguments);\n  };\n}();\nconst completeFlow = /*#__PURE__*/function () {\n  var _ref4 = _asyncToGenerator(function* ({\n    redirectUri,\n    state,\n    preferPrivateSession\n  }) {\n    yield tokenOrchestrator.setOAuthMetadata({\n      oauthSignIn: true\n    });\n    yield oAuthStore.clearOAuthData();\n    yield oAuthStore.storeOAuthSignIn(true, preferPrivateSession);\n    // this should be called before any call that involves `fetchAuthSession`\n    // e.g. `getCurrentUser()` below, so it allows every inflight async calls to\n    //  `fetchAuthSession` can be resolved\n    resolveAndClearInflightPromises();\n    // clear history before sending out final Hub events\n    clearHistory(redirectUri);\n    if (isCustomState(state)) {\n      Hub.dispatch('auth', {\n        event: 'customOAuthState',\n        data: urlSafeDecode(getCustomState(state))\n      }, 'Auth', AMPLIFY_SYMBOL);\n    }\n    Hub.dispatch('auth', {\n      event: 'signInWithRedirect'\n    }, 'Auth', AMPLIFY_SYMBOL);\n    yield dispatchSignedInHubEvent();\n  });\n  return function completeFlow(_x4) {\n    return _ref4.apply(this, arguments);\n  };\n}();\nconst isCustomState = state => {\n  return /-/.test(state);\n};\nconst getCustomState = state => {\n  return state.split('-').splice(1).join('-');\n};\nconst clearHistory = redirectUri => {\n  if (typeof window !== 'undefined' && typeof window.history !== 'undefined') {\n    window.history.replaceState(window.history.state, '', redirectUri);\n  }\n};\nexport { completeOAuthFlow };", "map": {"version": 3, "names": ["AmplifyUrl", "USER_AGENT_HEADER", "urlSafeDecode", "AMPLIFY_SYMBOL", "decodeJWT", "<PERSON><PERSON>", "cacheCognitoTokens", "dispatchSignedInHubEvent", "oAuthStore", "resolveAndClearInflightPromises", "tokenOrchestrator", "createOAuthError", "validateState", "completeOAuthFlow", "_ref", "_asyncToGenerator", "currentUrl", "userAgentValue", "clientId", "redirectUri", "responseType", "domain", "preferPrivateSession", "urlParams", "error", "searchParams", "get", "errorMessage", "handleCodeFlow", "handleImplicitFlow", "_x", "apply", "arguments", "_ref2", "url", "code", "state", "validatedState", "oAuthTokenEndpoint", "codeVerifier", "loadPKCE", "oAuthTokenBody", "grant_type", "client_id", "redirect_uri", "code_verifier", "body", "Object", "entries", "map", "k", "v", "encodeURIComponent", "join", "access_token", "refresh_token", "refreshToken", "id_token", "error_message", "token_type", "expires_in", "fetch", "method", "headers", "json", "username", "payload", "AccessToken", "IdToken", "RefreshToken", "completeFlow", "_x2", "_ref3", "error_description", "hash", "substring", "split", "pairings", "reduce", "accum", "undefined", "_x3", "_ref4", "setOAuthMetadata", "oauthSignIn", "clearOAuthData", "storeOAuthSignIn", "clearHistory", "isCustomState", "dispatch", "event", "data", "getCustomState", "_x4", "test", "splice", "window", "history", "replaceState"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/oauth/completeOAuthFlow.mjs"], "sourcesContent": ["import { AmplifyUrl, USER_AGENT_HEADER, urlSafeDecode, AMPLIFY_SYMBOL } from '@aws-amplify/core/internals/utils';\nimport { decodeJWT, Hub } from '@aws-amplify/core';\nimport { cacheCognitoTokens } from '../../tokenProvider/cacheTokens.mjs';\nimport { dispatchSignedInHubEvent } from '../dispatchSignedInHubEvent.mjs';\nimport '../refreshAuthTokens.mjs';\nimport '../../tokenProvider/errorHelpers.mjs';\nimport { oAuthStore } from './oAuthStore.mjs';\nimport { resolveAndClearInflightPromises } from './inflightPromise.mjs';\nimport { tokenOrchestrator } from '../../tokenProvider/tokenProvider.mjs';\nimport { createOAuthError } from './createOAuthError.mjs';\nimport { validateState } from './validateState.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst completeOAuthFlow = async ({ currentUrl, userAgentValue, clientId, redirectUri, responseType, domain, preferPrivateSession, }) => {\n    const urlParams = new AmplifyUrl(currentUrl);\n    const error = urlParams.searchParams.get('error');\n    const errorMessage = urlParams.searchParams.get('error_description');\n    if (error) {\n        throw createOAuthError(errorMessage ?? error);\n    }\n    if (responseType === 'code') {\n        return handleCodeFlow({\n            currentUrl,\n            userAgentValue,\n            clientId,\n            redirectUri,\n            domain,\n            preferPrivateSession,\n        });\n    }\n    return handleImplicitFlow({\n        currentUrl,\n        redirectUri,\n        preferPrivateSession,\n    });\n};\nconst handleCodeFlow = async ({ currentUrl, userAgentValue, clientId, redirectUri, domain, preferPrivateSession, }) => {\n    /* Convert URL into an object with parameters as keys\n{ redirect_uri: 'http://localhost:3000/', response_type: 'code', ...} */\n    const url = new AmplifyUrl(currentUrl);\n    const code = url.searchParams.get('code');\n    const state = url.searchParams.get('state');\n    // if `code` or `state` is not presented in the redirect url, most likely\n    // that the end user cancelled the inflight oauth flow by:\n    // 1. clicking the back button of browser\n    // 2. closing the provider hosted UI page and coming back to the app\n    if (!code || !state) {\n        throw createOAuthError('User cancelled OAuth flow.');\n    }\n    // may throw error is being caught in attemptCompleteOAuthFlow.ts\n    const validatedState = await validateState(state);\n    const oAuthTokenEndpoint = 'https://' + domain + '/oauth2/token';\n    // TODO(v6): check hub events\n    // dispatchAuthEvent(\n    // \t'codeFlow',\n    // \t{},\n    // \t`Retrieving tokens from ${oAuthTokenEndpoint}`\n    // );\n    const codeVerifier = await oAuthStore.loadPKCE();\n    const oAuthTokenBody = {\n        grant_type: 'authorization_code',\n        code,\n        client_id: clientId,\n        redirect_uri: redirectUri,\n        ...(codeVerifier ? { code_verifier: codeVerifier } : {}),\n    };\n    const body = Object.entries(oAuthTokenBody)\n        .map(([k, v]) => `${encodeURIComponent(k)}=${encodeURIComponent(v)}`)\n        .join('&');\n    const { access_token, refresh_token: refreshToken, id_token, error, error_message: errorMessage, token_type, expires_in, } = await (await fetch(oAuthTokenEndpoint, {\n        method: 'POST',\n        headers: {\n            'Content-Type': 'application/x-www-form-urlencoded',\n            [USER_AGENT_HEADER]: userAgentValue,\n        },\n        body,\n    })).json();\n    if (error) {\n        // error is being caught in attemptCompleteOAuthFlow.ts\n        throw createOAuthError(errorMessage ?? error);\n    }\n    const username = (access_token && decodeJWT(access_token).payload.username) ?? 'username';\n    await cacheCognitoTokens({\n        username,\n        AccessToken: access_token,\n        IdToken: id_token,\n        RefreshToken: refreshToken});\n    return completeFlow({\n        redirectUri,\n        state: validatedState,\n        preferPrivateSession,\n    });\n};\nconst handleImplicitFlow = async ({ currentUrl, redirectUri, preferPrivateSession, }) => {\n    // hash is `null` if `#` doesn't exist on URL\n    const url = new AmplifyUrl(currentUrl);\n    const { id_token, access_token, state, token_type, expires_in, error_description, error, } = (url.hash ?? '#')\n        .substring(1) // Remove # from returned code\n        .split('&')\n        .map(pairings => pairings.split('='))\n        .reduce((accum, [k, v]) => ({ ...accum, [k]: v }), {\n        id_token: undefined,\n        access_token: undefined,\n        state: undefined,\n        token_type: undefined,\n        expires_in: undefined,\n        error_description: undefined,\n        error: undefined,\n    });\n    if (error) {\n        throw createOAuthError(error_description ?? error);\n    }\n    if (!access_token) {\n        // error is being caught in attemptCompleteOAuthFlow.ts\n        throw createOAuthError('No access token returned from OAuth flow.');\n    }\n    const validatedState = await validateState(state);\n    const username = (access_token && decodeJWT(access_token).payload.username) ?? 'username';\n    await cacheCognitoTokens({\n        username,\n        AccessToken: access_token,\n        IdToken: id_token});\n    return completeFlow({\n        redirectUri,\n        state: validatedState,\n        preferPrivateSession,\n    });\n};\nconst completeFlow = async ({ redirectUri, state, preferPrivateSession, }) => {\n    await tokenOrchestrator.setOAuthMetadata({\n        oauthSignIn: true,\n    });\n    await oAuthStore.clearOAuthData();\n    await oAuthStore.storeOAuthSignIn(true, preferPrivateSession);\n    // this should be called before any call that involves `fetchAuthSession`\n    // e.g. `getCurrentUser()` below, so it allows every inflight async calls to\n    //  `fetchAuthSession` can be resolved\n    resolveAndClearInflightPromises();\n    // clear history before sending out final Hub events\n    clearHistory(redirectUri);\n    if (isCustomState(state)) {\n        Hub.dispatch('auth', {\n            event: 'customOAuthState',\n            data: urlSafeDecode(getCustomState(state)),\n        }, 'Auth', AMPLIFY_SYMBOL);\n    }\n    Hub.dispatch('auth', { event: 'signInWithRedirect' }, 'Auth', AMPLIFY_SYMBOL);\n    await dispatchSignedInHubEvent();\n};\nconst isCustomState = (state) => {\n    return /-/.test(state);\n};\nconst getCustomState = (state) => {\n    return state.split('-').splice(1).join('-');\n};\nconst clearHistory = (redirectUri) => {\n    if (typeof window !== 'undefined' && typeof window.history !== 'undefined') {\n        window.history.replaceState(window.history.state, '', redirectUri);\n    }\n};\n\nexport { completeOAuthFlow };\n"], "mappings": ";AAAA,SAASA,UAAU,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,cAAc,QAAQ,mCAAmC;AAChH,SAASC,SAAS,EAAEC,GAAG,QAAQ,mBAAmB;AAClD,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,SAASC,wBAAwB,QAAQ,iCAAiC;AAC1E,OAAO,0BAA0B;AACjC,OAAO,sCAAsC;AAC7C,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,+BAA+B,QAAQ,uBAAuB;AACvE,SAASC,iBAAiB,QAAQ,uCAAuC;AACzE,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,aAAa,QAAQ,qBAAqB;;AAEnD;AACA;AACA,MAAMC,iBAAiB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAO;IAAEC,UAAU;IAAEC,cAAc;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,YAAY;IAAEC,MAAM;IAAEC;EAAsB,CAAC,EAAK;IACpI,MAAMC,SAAS,GAAG,IAAIvB,UAAU,CAACgB,UAAU,CAAC;IAC5C,MAAMQ,KAAK,GAAGD,SAAS,CAACE,YAAY,CAACC,GAAG,CAAC,OAAO,CAAC;IACjD,MAAMC,YAAY,GAAGJ,SAAS,CAACE,YAAY,CAACC,GAAG,CAAC,mBAAmB,CAAC;IACpE,IAAIF,KAAK,EAAE;MACP,MAAMb,gBAAgB,CAACgB,YAAY,IAAIH,KAAK,CAAC;IACjD;IACA,IAAIJ,YAAY,KAAK,MAAM,EAAE;MACzB,OAAOQ,cAAc,CAAC;QAClBZ,UAAU;QACVC,cAAc;QACdC,QAAQ;QACRC,WAAW;QACXE,MAAM;QACNC;MACJ,CAAC,CAAC;IACN;IACA,OAAOO,kBAAkB,CAAC;MACtBb,UAAU;MACVG,WAAW;MACXG;IACJ,CAAC,CAAC;EACN,CAAC;EAAA,gBAtBKT,iBAAiBA,CAAAiB,EAAA;IAAA,OAAAhB,IAAA,CAAAiB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAsBtB;AACD,MAAMJ,cAAc;EAAA,IAAAK,KAAA,GAAAlB,iBAAA,CAAG,WAAO;IAAEC,UAAU;IAAEC,cAAc;IAAEC,QAAQ;IAAEC,WAAW;IAAEE,MAAM;IAAEC;EAAsB,CAAC,EAAK;IACnH;AACJ;IACI,MAAMY,GAAG,GAAG,IAAIlC,UAAU,CAACgB,UAAU,CAAC;IACtC,MAAMmB,IAAI,GAAGD,GAAG,CAACT,YAAY,CAACC,GAAG,CAAC,MAAM,CAAC;IACzC,MAAMU,KAAK,GAAGF,GAAG,CAACT,YAAY,CAACC,GAAG,CAAC,OAAO,CAAC;IAC3C;IACA;IACA;IACA;IACA,IAAI,CAACS,IAAI,IAAI,CAACC,KAAK,EAAE;MACjB,MAAMzB,gBAAgB,CAAC,4BAA4B,CAAC;IACxD;IACA;IACA,MAAM0B,cAAc,SAASzB,aAAa,CAACwB,KAAK,CAAC;IACjD,MAAME,kBAAkB,GAAG,UAAU,GAAGjB,MAAM,GAAG,eAAe;IAChE;IACA;IACA;IACA;IACA;IACA;IACA,MAAMkB,YAAY,SAAS/B,UAAU,CAACgC,QAAQ,CAAC,CAAC;IAChD,MAAMC,cAAc,GAAG;MACnBC,UAAU,EAAE,oBAAoB;MAChCP,IAAI;MACJQ,SAAS,EAAEzB,QAAQ;MACnB0B,YAAY,EAAEzB,WAAW;MACzB,IAAIoB,YAAY,GAAG;QAAEM,aAAa,EAAEN;MAAa,CAAC,GAAG,CAAC,CAAC;IAC3D,CAAC;IACD,MAAMO,IAAI,GAAGC,MAAM,CAACC,OAAO,CAACP,cAAc,CAAC,CACtCQ,GAAG,CAAC,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,KAAK,GAAGC,kBAAkB,CAACF,CAAC,CAAC,IAAIE,kBAAkB,CAACD,CAAC,CAAC,EAAE,CAAC,CACpEE,IAAI,CAAC,GAAG,CAAC;IACd,MAAM;MAAEC,YAAY;MAAEC,aAAa,EAAEC,YAAY;MAAEC,QAAQ;MAAEjC,KAAK;MAAEkC,aAAa,EAAE/B,YAAY;MAAEgC,UAAU;MAAEC;IAAY,CAAC,SAAS,OAAOC,KAAK,CAACvB,kBAAkB,EAAE;MAChKwB,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACL,cAAc,EAAE,mCAAmC;QACnD,CAAC9D,iBAAiB,GAAGgB;MACzB,CAAC;MACD6B;IACJ,CAAC,CAAC,EAAEkB,IAAI,CAAC,CAAC;IACV,IAAIxC,KAAK,EAAE;MACP;MACA,MAAMb,gBAAgB,CAACgB,YAAY,IAAIH,KAAK,CAAC;IACjD;IACA,MAAMyC,QAAQ,GAAG,CAACX,YAAY,IAAIlD,SAAS,CAACkD,YAAY,CAAC,CAACY,OAAO,CAACD,QAAQ,KAAK,UAAU;IACzF,MAAM3D,kBAAkB,CAAC;MACrB2D,QAAQ;MACRE,WAAW,EAAEb,YAAY;MACzBc,OAAO,EAAEX,QAAQ;MACjBY,YAAY,EAAEb;IAAY,CAAC,CAAC;IAChC,OAAOc,YAAY,CAAC;MAChBnD,WAAW;MACXiB,KAAK,EAAEC,cAAc;MACrBf;IACJ,CAAC,CAAC;EACN,CAAC;EAAA,gBAxDKM,cAAcA,CAAA2C,GAAA;IAAA,OAAAtC,KAAA,CAAAF,KAAA,OAAAC,SAAA;EAAA;AAAA,GAwDnB;AACD,MAAMH,kBAAkB;EAAA,IAAA2C,KAAA,GAAAzD,iBAAA,CAAG,WAAO;IAAEC,UAAU;IAAEG,WAAW;IAAEG;EAAsB,CAAC,EAAK;IACrF;IACA,MAAMY,GAAG,GAAG,IAAIlC,UAAU,CAACgB,UAAU,CAAC;IACtC,MAAM;MAAEyC,QAAQ;MAAEH,YAAY;MAAElB,KAAK;MAAEuB,UAAU;MAAEC,UAAU;MAAEa,iBAAiB;MAAEjD;IAAO,CAAC,GAAG,CAACU,GAAG,CAACwC,IAAI,IAAI,GAAG,EACxGC,SAAS,CAAC,CAAC,CAAC,CAAC;IAAA,CACbC,KAAK,CAAC,GAAG,CAAC,CACV3B,GAAG,CAAC4B,QAAQ,IAAIA,QAAQ,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC,CACpCE,MAAM,CAAC,CAACC,KAAK,EAAE,CAAC7B,CAAC,EAAEC,CAAC,CAAC,MAAM;MAAE,GAAG4B,KAAK;MAAE,CAAC7B,CAAC,GAAGC;IAAE,CAAC,CAAC,EAAE;MACnDM,QAAQ,EAAEuB,SAAS;MACnB1B,YAAY,EAAE0B,SAAS;MACvB5C,KAAK,EAAE4C,SAAS;MAChBrB,UAAU,EAAEqB,SAAS;MACrBpB,UAAU,EAAEoB,SAAS;MACrBP,iBAAiB,EAAEO,SAAS;MAC5BxD,KAAK,EAAEwD;IACX,CAAC,CAAC;IACF,IAAIxD,KAAK,EAAE;MACP,MAAMb,gBAAgB,CAAC8D,iBAAiB,IAAIjD,KAAK,CAAC;IACtD;IACA,IAAI,CAAC8B,YAAY,EAAE;MACf;MACA,MAAM3C,gBAAgB,CAAC,2CAA2C,CAAC;IACvE;IACA,MAAM0B,cAAc,SAASzB,aAAa,CAACwB,KAAK,CAAC;IACjD,MAAM6B,QAAQ,GAAG,CAACX,YAAY,IAAIlD,SAAS,CAACkD,YAAY,CAAC,CAACY,OAAO,CAACD,QAAQ,KAAK,UAAU;IACzF,MAAM3D,kBAAkB,CAAC;MACrB2D,QAAQ;MACRE,WAAW,EAAEb,YAAY;MACzBc,OAAO,EAAEX;IAAQ,CAAC,CAAC;IACvB,OAAOa,YAAY,CAAC;MAChBnD,WAAW;MACXiB,KAAK,EAAEC,cAAc;MACrBf;IACJ,CAAC,CAAC;EACN,CAAC;EAAA,gBAlCKO,kBAAkBA,CAAAoD,GAAA;IAAA,OAAAT,KAAA,CAAAzC,KAAA,OAAAC,SAAA;EAAA;AAAA,GAkCvB;AACD,MAAMsC,YAAY;EAAA,IAAAY,KAAA,GAAAnE,iBAAA,CAAG,WAAO;IAAEI,WAAW;IAAEiB,KAAK;IAAEd;EAAsB,CAAC,EAAK;IAC1E,MAAMZ,iBAAiB,CAACyE,gBAAgB,CAAC;MACrCC,WAAW,EAAE;IACjB,CAAC,CAAC;IACF,MAAM5E,UAAU,CAAC6E,cAAc,CAAC,CAAC;IACjC,MAAM7E,UAAU,CAAC8E,gBAAgB,CAAC,IAAI,EAAEhE,oBAAoB,CAAC;IAC7D;IACA;IACA;IACAb,+BAA+B,CAAC,CAAC;IACjC;IACA8E,YAAY,CAACpE,WAAW,CAAC;IACzB,IAAIqE,aAAa,CAACpD,KAAK,CAAC,EAAE;MACtB/B,GAAG,CAACoF,QAAQ,CAAC,MAAM,EAAE;QACjBC,KAAK,EAAE,kBAAkB;QACzBC,IAAI,EAAEzF,aAAa,CAAC0F,cAAc,CAACxD,KAAK,CAAC;MAC7C,CAAC,EAAE,MAAM,EAAEjC,cAAc,CAAC;IAC9B;IACAE,GAAG,CAACoF,QAAQ,CAAC,MAAM,EAAE;MAAEC,KAAK,EAAE;IAAqB,CAAC,EAAE,MAAM,EAAEvF,cAAc,CAAC;IAC7E,MAAMI,wBAAwB,CAAC,CAAC;EACpC,CAAC;EAAA,gBApBK+D,YAAYA,CAAAuB,GAAA;IAAA,OAAAX,KAAA,CAAAnD,KAAA,OAAAC,SAAA;EAAA;AAAA,GAoBjB;AACD,MAAMwD,aAAa,GAAIpD,KAAK,IAAK;EAC7B,OAAO,GAAG,CAAC0D,IAAI,CAAC1D,KAAK,CAAC;AAC1B,CAAC;AACD,MAAMwD,cAAc,GAAIxD,KAAK,IAAK;EAC9B,OAAOA,KAAK,CAACwC,KAAK,CAAC,GAAG,CAAC,CAACmB,MAAM,CAAC,CAAC,CAAC,CAAC1C,IAAI,CAAC,GAAG,CAAC;AAC/C,CAAC;AACD,MAAMkC,YAAY,GAAIpE,WAAW,IAAK;EAClC,IAAI,OAAO6E,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,OAAO,KAAK,WAAW,EAAE;IACxED,MAAM,CAACC,OAAO,CAACC,YAAY,CAACF,MAAM,CAACC,OAAO,CAAC7D,KAAK,EAAE,EAAE,EAAEjB,WAAW,CAAC;EACtE;AACJ,CAAC;AAED,SAASN,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}