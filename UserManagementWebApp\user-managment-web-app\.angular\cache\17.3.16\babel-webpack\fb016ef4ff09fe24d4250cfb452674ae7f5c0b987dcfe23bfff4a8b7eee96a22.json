{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { PutUsersRequest } from \"../models/models_0\";\nimport { deserializeAws_restJson1PutUsersCommand, serializeAws_restJson1PutUsersCommand } from \"../protocols/Aws_restJson1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Adds one or more users to a Users dataset. For more information see\n *       <a>importing-users</a>.</p>\n */\nvar PutUsersCommand = /** @class */function (_super) {\n  __extends(PutUsersCommand, _super);\n  // Start section: command_properties\n  // End section: command_properties\n  function PutUsersCommand(input) {\n    var _this =\n    // Start section: command_constructor\n    _super.call(this) || this;\n    _this.input = input;\n    return _this;\n    // End section: command_constructor\n  }\n  /**\n   * @internal\n   */\n  PutUsersCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n    this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n    var stack = clientStack.concat(this.middlewareStack);\n    var logger = configuration.logger;\n    var clientName = \"PersonalizeEventsClient\";\n    var commandName = \"PutUsersCommand\";\n    var handlerExecutionContext = {\n      logger: logger,\n      clientName: clientName,\n      commandName: commandName,\n      inputFilterSensitiveLog: PutUsersRequest.filterSensitiveLog,\n      outputFilterSensitiveLog: function (output) {\n        return output;\n      }\n    };\n    var requestHandler = configuration.requestHandler;\n    return stack.resolve(function (request) {\n      return requestHandler.handle(request.request, options || {});\n    }, handlerExecutionContext);\n  };\n  PutUsersCommand.prototype.serialize = function (input, context) {\n    return serializeAws_restJson1PutUsersCommand(input, context);\n  };\n  PutUsersCommand.prototype.deserialize = function (output, context) {\n    return deserializeAws_restJson1PutUsersCommand(output, context);\n  };\n  return PutUsersCommand;\n}($Command);\nexport { PutUsersCommand };", "map": {"version": 3, "names": ["__extends", "PutUsersRequest", "deserializeAws_restJson1PutUsersCommand", "serializeAws_restJson1PutUsersCommand", "getSerdePlugin", "Command", "$Command", "PutUsersCommand", "_super", "input", "_this", "call", "prototype", "resolveMiddleware", "clientStack", "configuration", "options", "middlewareStack", "use", "serialize", "deserialize", "stack", "concat", "logger", "clientName", "commandName", "handlerExecutionContext", "inputFilterSensitiveLog", "filterSensitiveLog", "outputFilterSensitiveLog", "output", "requestHandler", "resolve", "request", "handle", "context"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-personalize-events/dist/es/commands/PutUsersCommand.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { PutUsersRequest } from \"../models/models_0\";\nimport { deserializeAws_restJson1PutUsersCommand, serializeAws_restJson1PutUsersCommand, } from \"../protocols/Aws_restJson1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Adds one or more users to a Users dataset. For more information see\n *       <a>importing-users</a>.</p>\n */\nvar PutUsersCommand = /** @class */ (function (_super) {\n    __extends(PutUsersCommand, _super);\n    // Start section: command_properties\n    // End section: command_properties\n    function PutUsersCommand(input) {\n        var _this = \n        // Start section: command_constructor\n        _super.call(this) || this;\n        _this.input = input;\n        return _this;\n        // End section: command_constructor\n    }\n    /**\n     * @internal\n     */\n    PutUsersCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n        this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n        var stack = clientStack.concat(this.middlewareStack);\n        var logger = configuration.logger;\n        var clientName = \"PersonalizeEventsClient\";\n        var commandName = \"PutUsersCommand\";\n        var handlerExecutionContext = {\n            logger: logger,\n            clientName: clientName,\n            commandName: commandName,\n            inputFilterSensitiveLog: PutUsersRequest.filterSensitiveLog,\n            outputFilterSensitiveLog: function (output) { return output; },\n        };\n        var requestHandler = configuration.requestHandler;\n        return stack.resolve(function (request) {\n            return requestHandler.handle(request.request, options || {});\n        }, handlerExecutionContext);\n    };\n    PutUsersCommand.prototype.serialize = function (input, context) {\n        return serializeAws_restJson1PutUsersCommand(input, context);\n    };\n    PutUsersCommand.prototype.deserialize = function (output, context) {\n        return deserializeAws_restJson1PutUsersCommand(output, context);\n    };\n    return PutUsersCommand;\n}($Command));\nexport { PutUsersCommand };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,uCAAuC,EAAEC,qCAAqC,QAAS,4BAA4B;AAC5H,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,OAAO,IAAIC,QAAQ,QAAQ,wBAAwB;AAC5D;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,aAAe,UAAUC,MAAM,EAAE;EACnDR,SAAS,CAACO,eAAe,EAAEC,MAAM,CAAC;EAClC;EACA;EACA,SAASD,eAAeA,CAACE,KAAK,EAAE;IAC5B,IAAIC,KAAK;IACT;IACAF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACzBD,KAAK,CAACD,KAAK,GAAGA,KAAK;IACnB,OAAOC,KAAK;IACZ;EACJ;EACA;AACJ;AACA;EACIH,eAAe,CAACK,SAAS,CAACC,iBAAiB,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAEC,OAAO,EAAE;IACzF,IAAI,CAACC,eAAe,CAACC,GAAG,CAACd,cAAc,CAACW,aAAa,EAAE,IAAI,CAACI,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;IACzF,IAAIC,KAAK,GAAGP,WAAW,CAACQ,MAAM,CAAC,IAAI,CAACL,eAAe,CAAC;IACpD,IAAIM,MAAM,GAAGR,aAAa,CAACQ,MAAM;IACjC,IAAIC,UAAU,GAAG,yBAAyB;IAC1C,IAAIC,WAAW,GAAG,iBAAiB;IACnC,IAAIC,uBAAuB,GAAG;MAC1BH,MAAM,EAAEA,MAAM;MACdC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA,WAAW;MACxBE,uBAAuB,EAAE1B,eAAe,CAAC2B,kBAAkB;MAC3DC,wBAAwB,EAAE,SAAAA,CAAUC,MAAM,EAAE;QAAE,OAAOA,MAAM;MAAE;IACjE,CAAC;IACD,IAAIC,cAAc,GAAGhB,aAAa,CAACgB,cAAc;IACjD,OAAOV,KAAK,CAACW,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpC,OAAOF,cAAc,CAACG,MAAM,CAACD,OAAO,CAACA,OAAO,EAAEjB,OAAO,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC,EAAEU,uBAAuB,CAAC;EAC/B,CAAC;EACDnB,eAAe,CAACK,SAAS,CAACO,SAAS,GAAG,UAAUV,KAAK,EAAE0B,OAAO,EAAE;IAC5D,OAAOhC,qCAAqC,CAACM,KAAK,EAAE0B,OAAO,CAAC;EAChE,CAAC;EACD5B,eAAe,CAACK,SAAS,CAACQ,WAAW,GAAG,UAAUU,MAAM,EAAEK,OAAO,EAAE;IAC/D,OAAOjC,uCAAuC,CAAC4B,MAAM,EAAEK,OAAO,CAAC;EACnE,CAAC;EACD,OAAO5B,eAAe;AAC1B,CAAC,CAACD,QAAQ,CAAE;AACZ,SAASC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}