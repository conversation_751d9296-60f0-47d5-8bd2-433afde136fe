{"ast": null, "code": "const avatar = {\n  // Default styles\n  color: {\n    value: '{colors.font.tertiary.value}'\n  },\n  lineHeight: {\n    value: 1\n  },\n  fontWeight: {\n    value: '{fontWeights.semibold.value}'\n  },\n  fontSize: {\n    value: '{fontSizes.small.value}'\n  },\n  textAlign: {\n    value: 'center'\n  },\n  width: {\n    value: '{fontSizes.xxl.value}'\n  },\n  height: {\n    value: '{fontSizes.xxl.value}'\n  },\n  backgroundColor: {\n    value: '{colors.background.tertiary}'\n  },\n  borderRadius: {\n    value: '100%'\n  },\n  borderColor: {\n    value: '{colors.border.primary.value}'\n  },\n  borderWidth: {\n    value: '{borderWidths.medium.value}'\n  },\n  // Color Theme Variations\n  info: {\n    color: {\n      value: '{colors.font.info.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.info.value}'\n    },\n    borderColor: {\n      value: '{colors.border.info.value}'\n    }\n  },\n  warning: {\n    color: {\n      value: '{colors.font.warning.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.warning.value}'\n    },\n    borderColor: {\n      value: '{colors.border.warning.value}'\n    }\n  },\n  success: {\n    color: {\n      value: '{colors.font.success.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.success.value}'\n    },\n    borderColor: {\n      value: '{colors.border.success.value}'\n    }\n  },\n  error: {\n    color: {\n      value: '{colors.font.error.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.error.value}'\n    },\n    borderColor: {\n      value: '{colors.border.error.value}'\n    }\n  },\n  // Sizes\n  small: {\n    fontSize: {\n      value: '{fontSizes.xs.value}'\n    },\n    width: {\n      value: '{fontSizes.xl.value}'\n    },\n    height: {\n      value: '{fontSizes.xl.value}'\n    }\n  },\n  // medium is the default size\n  large: {\n    fontSize: {\n      value: '{fontSizes.medium.value}'\n    },\n    width: {\n      value: '{fontSizes.xxxl.value}'\n    },\n    height: {\n      value: '{fontSizes.xxxl.value}'\n    }\n  }\n};\nexport { avatar };", "map": {"version": 3, "names": ["avatar", "color", "value", "lineHeight", "fontWeight", "fontSize", "textAlign", "width", "height", "backgroundColor", "borderRadius", "borderColor", "borderWidth", "info", "warning", "success", "error", "small", "large"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/avatar.mjs"], "sourcesContent": ["const avatar = {\n    // Default styles\n    color: { value: '{colors.font.tertiary.value}' },\n    lineHeight: { value: 1 },\n    fontWeight: { value: '{fontWeights.semibold.value}' },\n    fontSize: { value: '{fontSizes.small.value}' },\n    textAlign: { value: 'center' },\n    width: { value: '{fontSizes.xxl.value}' },\n    height: { value: '{fontSizes.xxl.value}' },\n    backgroundColor: { value: '{colors.background.tertiary}' },\n    borderRadius: { value: '100%' },\n    borderColor: { value: '{colors.border.primary.value}' },\n    borderWidth: { value: '{borderWidths.medium.value}' },\n    // Color Theme Variations\n    info: {\n        color: { value: '{colors.font.info.value}' },\n        backgroundColor: { value: '{colors.background.info.value}' },\n        borderColor: { value: '{colors.border.info.value}' },\n    },\n    warning: {\n        color: { value: '{colors.font.warning.value}' },\n        backgroundColor: { value: '{colors.background.warning.value}' },\n        borderColor: { value: '{colors.border.warning.value}' },\n    },\n    success: {\n        color: { value: '{colors.font.success.value}' },\n        backgroundColor: { value: '{colors.background.success.value}' },\n        borderColor: { value: '{colors.border.success.value}' },\n    },\n    error: {\n        color: { value: '{colors.font.error.value}' },\n        backgroundColor: { value: '{colors.background.error.value}' },\n        borderColor: { value: '{colors.border.error.value}' },\n    },\n    // Sizes\n    small: {\n        fontSize: { value: '{fontSizes.xs.value}' },\n        width: { value: '{fontSizes.xl.value}' },\n        height: { value: '{fontSizes.xl.value}' },\n    },\n    // medium is the default size\n    large: {\n        fontSize: { value: '{fontSizes.medium.value}' },\n        width: { value: '{fontSizes.xxxl.value}' },\n        height: { value: '{fontSizes.xxxl.value}' },\n    },\n};\n\nexport { avatar };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACX;EACAC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAA+B,CAAC;EAChDC,UAAU,EAAE;IAAED,KAAK,EAAE;EAAE,CAAC;EACxBE,UAAU,EAAE;IAAEF,KAAK,EAAE;EAA+B,CAAC;EACrDG,QAAQ,EAAE;IAAEH,KAAK,EAAE;EAA0B,CAAC;EAC9CI,SAAS,EAAE;IAAEJ,KAAK,EAAE;EAAS,CAAC;EAC9BK,KAAK,EAAE;IAAEL,KAAK,EAAE;EAAwB,CAAC;EACzCM,MAAM,EAAE;IAAEN,KAAK,EAAE;EAAwB,CAAC;EAC1CO,eAAe,EAAE;IAAEP,KAAK,EAAE;EAA+B,CAAC;EAC1DQ,YAAY,EAAE;IAAER,KAAK,EAAE;EAAO,CAAC;EAC/BS,WAAW,EAAE;IAAET,KAAK,EAAE;EAAgC,CAAC;EACvDU,WAAW,EAAE;IAAEV,KAAK,EAAE;EAA8B,CAAC;EACrD;EACAW,IAAI,EAAE;IACFZ,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA2B,CAAC;IAC5CO,eAAe,EAAE;MAAEP,KAAK,EAAE;IAAiC,CAAC;IAC5DS,WAAW,EAAE;MAAET,KAAK,EAAE;IAA6B;EACvD,CAAC;EACDY,OAAO,EAAE;IACLb,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA8B,CAAC;IAC/CO,eAAe,EAAE;MAAEP,KAAK,EAAE;IAAoC,CAAC;IAC/DS,WAAW,EAAE;MAAET,KAAK,EAAE;IAAgC;EAC1D,CAAC;EACDa,OAAO,EAAE;IACLd,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA8B,CAAC;IAC/CO,eAAe,EAAE;MAAEP,KAAK,EAAE;IAAoC,CAAC;IAC/DS,WAAW,EAAE;MAAET,KAAK,EAAE;IAAgC;EAC1D,CAAC;EACDc,KAAK,EAAE;IACHf,KAAK,EAAE;MAAEC,KAAK,EAAE;IAA4B,CAAC;IAC7CO,eAAe,EAAE;MAAEP,KAAK,EAAE;IAAkC,CAAC;IAC7DS,WAAW,EAAE;MAAET,KAAK,EAAE;IAA8B;EACxD,CAAC;EACD;EACAe,KAAK,EAAE;IACHZ,QAAQ,EAAE;MAAEH,KAAK,EAAE;IAAuB,CAAC;IAC3CK,KAAK,EAAE;MAAEL,KAAK,EAAE;IAAuB,CAAC;IACxCM,MAAM,EAAE;MAAEN,KAAK,EAAE;IAAuB;EAC5C,CAAC;EACD;EACAgB,KAAK,EAAE;IACHb,QAAQ,EAAE;MAAEH,KAAK,EAAE;IAA2B,CAAC;IAC/CK,KAAK,EAAE;MAAEL,KAAK,EAAE;IAAyB,CAAC;IAC1CM,MAAM,EAAE;MAAEN,KAAK,EAAE;IAAyB;EAC9C;AACJ,CAAC;AAED,SAASF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}