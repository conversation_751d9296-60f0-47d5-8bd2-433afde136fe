import { SideBarDef } from "ag-grid-community";

export interface GridLayout {
    id?: string;
    layoutHashId?: string;
    tenantId?: number;
    projectId?: number;
    projectVersionId?: number;
    gridId?: string;
    name?: string;
    createdBy?: string;
    modifiedBy?: string;
    isPublic?: boolean;
    isDefault?: boolean;
    createdAt?: Date;
    modifiedAt?: Date;
    agGridSettings?: GridLayoutGridSettings;
    hasUserParams?: boolean;
    userParams?: string; //Json string
    hasRecordRefiners?: boolean;
    recordRefiners?: string; //Json string
    editable?: boolean;
    isCurrentLayout?: boolean;
}

export interface GridLayoutGridSettings {
    columnState?: string;
    filterModel?: string;
    pivotMode?: boolean;
    sideBar?: string | boolean | SideBarDef | string[];
}
