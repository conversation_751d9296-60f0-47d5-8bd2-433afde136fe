{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Cache } from '../../../Cache/index.mjs';\nimport { getCacheKey } from './getCacheKey.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Writes an endpoint id to a long-lived cache.\n *\n * @internal\n */\nconst cacheEndpointId = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (appId, category, endpointId) {\n    const cacheKey = getCacheKey(appId, category);\n    // Set a longer TTL to avoid endpoint id being deleted after the default TTL (3 days)\n    // Also set its priority to the highest to reduce its chance of being deleted when cache is full\n    const ttl = 1000 * 60 * 60 * 24 * 365 * 100; // 100 years\n    const expiration = new Date().getTime() + ttl;\n    return Cache.setItem(cacheKey, endpointId, {\n      expires: expiration,\n      priority: 1\n    });\n  });\n  return function cacheEndpointId(_x, _x2, _x3) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { cacheEndpointId };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON>", "cacheEndpointId", "_ref", "_asyncToGenerator", "appId", "category", "endpointId", "cache<PERSON>ey", "ttl", "expiration", "Date", "getTime", "setItem", "expires", "priority", "_x", "_x2", "_x3", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/providers/pinpoint/utils/cacheEndpointId.mjs"], "sourcesContent": ["import { Cache } from '../../../Cache/index.mjs';\nimport { getCacheKey } from './getCacheKey.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Writes an endpoint id to a long-lived cache.\n *\n * @internal\n */\nconst cacheEndpointId = async (appId, category, endpointId) => {\n    const cacheKey = getCacheKey(appId, category);\n    // Set a longer TTL to avoid endpoint id being deleted after the default TTL (3 days)\n    // Also set its priority to the highest to reduce its chance of being deleted when cache is full\n    const ttl = 1000 * 60 * 60 * 24 * 365 * 100; // 100 years\n    const expiration = new Date().getTime() + ttl;\n    return Cache.setItem(cacheKey, endpointId, {\n        expires: expiration,\n        priority: 1,\n    });\n};\n\nexport { cacheEndpointId };\n"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,0BAA0B;AAChD,SAASC,WAAW,QAAQ,mBAAmB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAK;IAC3D,MAAMC,QAAQ,GAAGP,WAAW,CAACI,KAAK,EAAEC,QAAQ,CAAC;IAC7C;IACA;IACA,MAAMG,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;IAC7C,MAAMC,UAAU,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGH,GAAG;IAC7C,OAAOT,KAAK,CAACa,OAAO,CAACL,QAAQ,EAAED,UAAU,EAAE;MACvCO,OAAO,EAAEJ,UAAU;MACnBK,QAAQ,EAAE;IACd,CAAC,CAAC;EACN,CAAC;EAAA,gBAVKb,eAAeA,CAAAc,EAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAf,IAAA,CAAAgB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAUpB;AAED,SAASlB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}