body.saltbox-data-app.sbdapp{

    // XX-Large devices (larger desktops, 1400px and up)
    @media (min-width: 1400px) { 
           
    }
    
    // X-Large devices (large desktops, 1200px and up)
    @media (min-width: 1200px) { 
        app-form-renderer {
            .form-designer .form-layout .card{
                max-width: calc(100vw - 33rem);
            }
        }
    }
    @media (min-width:1920px) {
  
    }
    
    // Large devices (desktops, 992px and up)
    @media (min-width: 992px) { 
    
       .p-breadcrumb .p-menuitem-text {
           vertical-align: middle;
           max-width: 250px;
           text-overflow: ellipsis !important;
           display: inline-block;
           white-space: nowrap;
           height: 1rem;
           line-height: 1rem;
           overflow: hidden;
         }
    
    }
    
    // Medium devices (tablets, 768px and up)

    @media (max-width: 1200px){
        
    }
    @media (min-width: 768px) { 

    }
    
    @media (max-width: 768px) { 

    }
    
    @media (min-width: 767px) and (orientation : landscape) { 
    
    }

    @media (min-width: 691px) {
        .preview-height-fix {
            max-height: 93vh !important;
            height: 93vh !important;
        }
    }

    @media (max-width: 1024px) {

    }

    @media (max-width: 950px) {
        app-dynamic-forms-template{
            display: none;
        }

        .notice-card.not-supported{
            display: flex;
        }
    }

    // Small devices (landscape phones, 576px and below)
    
    
    @media (max-width: 690px) {

        /*Scroll on JSF Modals*/

        .json-scroller{
            max-height: 70vh;
        }
    
        .sbi-actionable-grid-display .ag-display-size {
            height: calc(100vh - 9rem);
        }
        
        .horizontal-sub-nav .db-app-card.tiny{
            margin: 0.8em 0;
            width: 3.5em;
            height: 2.5em;
        }
        
        .horizontal-sub-nav span{
            color: var(--InverseText);
        }

        .horizontal-sub-nav .p-button-icon{
            color: var(--primary);
        }
        
        .layout-main .horizontal-sub-nav{
            padding: 0 0.5rem;
        }

        .mobile-app-menu{
            position: absolute;
            top: 7rem;
            right: 0;
            width: 100vw;
            display: block;
    
            .p-button{
                border: 1px solid var(--InverseText) !important;
                color: var(--InverseText) !important;
                background-color: var(--BaseText) !important;
                position: absolute;
                right: 5.5rem;
                top: -0.125rem;
            }
            .report-title{
                color: var(--InverseText);
                margin: 0;
                font-size: 0.8rem;
                position: absolute;
                left: 7.9rem;
                top: 0.9rem;
                .pi{
                    font-size: 0.8rem;
                }
            }
        }
        
        .desktop-grid-tb{
            display: none;
        }
        
        .preview-actions-fix {
            .mobile-app-menu .p-button{
                z-index: 1;
                right: initial;
                top: -6.6rem;
                left: 0.5rem;
            }
        }

        .preview-height-fix {
            max-height: 99vh !important;
            height: 99vh !important;
        }
            
            //this needs to move to navigation once code cleanup on that project occurs
            .p-breadcrumb{
               max-height: 2.7rem;
            }
            /**/
       
            .tb-menu-desktop, .ag-header-row.ag-header-row-column-filter{
               display: none;
            }
            .mobile-menu-tb{
               display: inline-flex;
            }
       
            .ag-theme-material .ag-header{
               height: 43px !important;
               min-height: 43px !important;
            }

        }
    
    @media (max-width: 576px) { 

        .sidebar-lg, .sidebar-md, .sidebar-sm{
            width: 100vw;
        }
    
        .sbda-settings-menu.flex-column{
            flex-direction: row !important;
        }
    
        .appBuilderParent{
           margin-left: 0;
        }

        .sbi-view-pad-none {
            margin-left: 0;
          }
    
        .sbda-parent{
            .tab-view {
                &.col{
                max-width: initial;
                }
            }
        }
    
        .sbda-settings-menu.w-7rem{
            width: 100% !important;
            justify-content: center;
            margin-bottom: -1.1rem;
            padding-top: 1rem;
            padding-right: 0 !important;
    
            p-button{
                display: flex;
                justify-content: center;
                max-width: fit-content;
    
                .p-button{
                    max-width: 3.5rem !important;
                    max-height: 2.5rem !important;
                    padding: 0.5rem;
                }
            }
        }
    
        .tenant-analytics .admin-card.transaction-cycle{
            margin-left: 0px;
            margin-top: 10px;
            width: 100%;
            right: 1.5%;
            margin-left: auto;
            margin-right: auto;
            max-width: 100%;
          }
          .tenant-analytics .admin-card.failed-transactions{
            width: 100%;
            margin-right: auto;
            max-width: 100%
            ;
        }
          .tenant-analytics-grid{
            display: flex;
            flex-direction: column;
            margin-top: 30px;
            margin-bottom: 10px;
            margin-left: auto;
            margin-right: auto;
            width: 100%;
          }
        .menu-layout-static .tabbed-sub-nav {
            top: 136px;
        }
    
        .layout-mobile-active .layout-mask{
            display: block;
            z-index: 997;
            opacity: 0.5;
        }
    
        .rotate-device-portrait{
            display: none;
          }
          
          .rotate-device-landscape{
            display: flex;
          }
    
        .layout-container .layout-main .layout-content{
            margin-top: 27px;
            padding: 5px;
        }
    
            .p-toast-top-right{
                top: 5px;
                left: 3%;
            }
    
            .p-toast{
                width: 95%;
            }
    
            .layout-topbar-left h1 {
                display: none;
            }
            app-breadcrumb {
                display: none;
            }
    
            .p-toolbar.flex-parent{
                flex-direction: column;
            }
    
            .flex-parent .p-toolbar-group-left{
                text-align: center;
            }
    
            .lock-container { display:none}

            .section-description {
                width: 35ch;
            }
    
            .p-tree .p-tree-container{
                width: 320px;
            }
    
            .p-dialog{
                max-height: 95%;
            }
    
            app-info-tooltip {
                display: none;
            }
    
            .table-global-filter input {
                width: 100%;
            }
    
            .confirm-text-container {
                width: 100%;
                margin: 0;
            }
    
            .project-contents .as-content .as-slection:last-child {
                margin-left: 0;
            }
    
            /* fun table things on mobile */
            .p-table {
                    .p-table-thead > tr > th,
                    .p-table-tfoot > tr > td {
                        display: none !important;
                    }
        
                    .p-table-tbody > tr > td {
                        text-align: left;
                        display: block;
                        border: 0 none !important;
                        width: 100% !important;
                        float: left;
                        clear: left;
                        border: 0 none;
        
                        .p-column-title {
                            padding: .4em;
                            min-width: 30%;
                            width: 100%;
                            display: inline-block;
                            margin: -.4em 1em -.4em -.4em;
                            font-weight: bold;
                        }
                    }
                
            }
    
        .api-gateway-locked-container{
            width: 100%;
        }
    
        /*Reporting Mobile Display Adjustments*/
    
        .mobile-app-menu{
            top: 6.5rem;
    
            .report-title{
                left: 4.1rem;
                top: 0.7rem;
            }
        }
    
        .layout-menu-static-inactive .sbi-view-pad, .sbi-view-pad {
            padding: 0;
        }
    
       .p-toolbar h3{
        margin: 0;
       }
    
        .p-panel .p-panel-header{
            padding: 0;
        }
        
     }
    
    @media (max-width: 400px){
        #single-spa-application\:\@vision33\/navigation-app .layout-topbar .layout-topbar-wrapper .layout-topbar-right .layout-topbar-actions nav-app-profile > li, #single-spa-application\:\@vision33\/navigation-app .layout-topbar .layout-topbar-wrapper .layout-topbar-right .layout-topbar-actions > li {
            padding: 0 5px !important;
        }
    
        #single-spa-application\:\@vision33\/navigation-app .topbar-menu .global-env-dropdown .p-dropdown.p-component{
            max-width: 95px;
        }
     }
    
    
    /* MOBILE LANDSCAPE */
    @media (max-width: 650px) and (orientation : landscape) {
    
        .menu-layout-static.layout-menu-static-inactive .layout-topbar-left h1{
            display: none !important;
        }
    }
    
    @media only screen and (max-width: 950px) and (orientation : landscape) {
    
        /*Fix for scroll visability on lanscape*/
        .sbi-actionable-grid-display .ag-display-size {
            height: calc(100vh - 9em);
        }
        /**/
    
        .rotate-device-portrait{
            display: flex;
          }
          
          .rotate-device-landscape{
            display: none;
          }
    
        .p-dropdown{
            min-width: auto;
        }
    
       .p-toolbar h3{
        margin: 0;
       }
    
        .p-panel .p-panel-header{
            padding: 0;
        }
    
    }
    

    @media (max-height: 700px) {
            app-dynamic-form-general-settings, app-dynamic-form-form-elements{
                max-height: calc(100vh - 20rem);
                overflow-x: auto;
            }

            .drawer .draggable-assets {
                max-height: none;
                overflow-x: initial;
            }
    }
    
    
    } //Closing for body class wrapper. Custom CSS must be added above this line