{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst INIT_N = 'FFFFFFFFFFFFFFFFC90FDAA22168C234C4C6628B80DC1CD1' + '29024E088A67CC74020BBEA63B139B22514A08798E3404DD' + 'EF9519B3CD3A431B302B0A6DF25F14374FE1356D6D51C245' + 'E485B576625E7EC6F44C42E9A637ED6B0BFF5CB6F406B7ED' + 'EE386BFB5A899FA5AE9F24117C4B1FE649286651ECE45B3D' + 'C2007CB8A163BF0598DA48361C55D39A69163FA8FD24CF5F' + '83655D23DCA3AD961C62F356208552BB9ED529077096966D' + '670C354E4ABC9804F1746C08CA18217C32905E462E36CE3B' + 'E39E772C180E86039B2783A2EC07A28FB5C55DF06F4C52C9' + 'DE2BCBF6955817183995497CEA956AE515D2261898FA0510' + '15728E5A8AAAC42DAD33170D04507A33A85521ABDF1CBA64' + 'ECFB850458DBEF0A8AEA71575D060C7DB3970F85A6E1E4C7' + 'ABF5AE8CDB0933D71E8C94E04A25619DCEE3D2261AD2EE6B' + 'F12FFA06D98A0864D87602733EC86A64521F2B18177B200C' + 'BBE117577A615D6C770988C0BAD946E208E24FA074E5AB31' + '43DB5BFCE0FD108E4B82D120A93AD2CAFFFFFFFFFFFFFFFF';\nconst SHORT_TO_HEX = {};\nconst HEX_TO_SHORT = {};\nfor (let i = 0; i < 256; i++) {\n  let encodedByte = i.toString(16).toLowerCase();\n  if (encodedByte.length === 1) {\n    encodedByte = `0${encodedByte}`;\n  }\n  SHORT_TO_HEX[i] = encodedByte;\n  HEX_TO_SHORT[encodedByte] = i;\n}\nexport { HEX_TO_SHORT, INIT_N, SHORT_TO_HEX };", "map": {"version": 3, "names": ["INIT_N", "SHORT_TO_HEX", "HEX_TO_SHORT", "i", "encodedByte", "toString", "toLowerCase", "length"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/srp/constants.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst INIT_N = 'FFFFFFFFFFFFFFFFC90FDAA22168C234C4C6628B80DC1CD1' +\n    '29024E088A67CC74020BBEA63B139B22514A08798E3404DD' +\n    'EF9519B3CD3A431B302B0A6DF25F14374FE1356D6D51C245' +\n    'E485B576625E7EC6F44C42E9A637ED6B0BFF5CB6F406B7ED' +\n    'EE386BFB5A899FA5AE9F24117C4B1FE649286651ECE45B3D' +\n    'C2007CB8A163BF0598DA48361C55D39A69163FA8FD24CF5F' +\n    '83655D23DCA3AD961C62F356208552BB9ED529077096966D' +\n    '670C354E4ABC9804F1746C08CA18217C32905E462E36CE3B' +\n    'E39E772C180E86039B2783A2EC07A28FB5C55DF06F4C52C9' +\n    'DE2BCBF6955817183995497CEA956AE515D2261898FA0510' +\n    '15728E5A8AAAC42DAD33170D04507A33A85521ABDF1CBA64' +\n    'ECFB850458DBEF0A8AEA71575D060C7DB3970F85A6E1E4C7' +\n    'ABF5AE8CDB0933D71E8C94E04A25619DCEE3D2261AD2EE6B' +\n    'F12FFA06D98A0864D87602733EC86A64521F2B18177B200C' +\n    'BBE117577A615D6C770988C0BAD946E208E24FA074E5AB31' +\n    '43DB5BFCE0FD108E4B82D120A93AD2CAFFFFFFFFFFFFFFFF';\nconst SHORT_TO_HEX = {};\nconst HEX_TO_SHORT = {};\nfor (let i = 0; i < 256; i++) {\n    let encodedByte = i.toString(16).toLowerCase();\n    if (encodedByte.length === 1) {\n        encodedByte = `0${encodedByte}`;\n    }\n    SHORT_TO_HEX[i] = encodedByte;\n    HEX_TO_SHORT[encodedByte] = i;\n}\n\nexport { HEX_TO_SHORT, INIT_N, SHORT_TO_HEX };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,MAAM,GAAG,kDAAkD,GAC7D,kDAAkD,GAClD,kDAAkD,GAClD,kDAAkD,GAClD,kDAAkD,GAClD,kDAAkD,GAClD,kDAAkD,GAClD,kDAAkD,GAClD,kDAAkD,GAClD,kDAAkD,GAClD,kDAAkD,GAClD,kDAAkD,GAClD,kDAAkD,GAClD,kDAAkD,GAClD,kDAAkD,GAClD,kDAAkD;AACtD,MAAMC,YAAY,GAAG,CAAC,CAAC;AACvB,MAAMC,YAAY,GAAG,CAAC,CAAC;AACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;EAC1B,IAAIC,WAAW,GAAGD,CAAC,CAACE,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;EAC9C,IAAIF,WAAW,CAACG,MAAM,KAAK,CAAC,EAAE;IAC1BH,WAAW,GAAG,IAAIA,WAAW,EAAE;EACnC;EACAH,YAAY,CAACE,CAAC,CAAC,GAAGC,WAAW;EAC7BF,YAAY,CAACE,WAAW,CAAC,GAAGD,CAAC;AACjC;AAEA,SAASD,YAAY,EAAEF,MAAM,EAAEC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}