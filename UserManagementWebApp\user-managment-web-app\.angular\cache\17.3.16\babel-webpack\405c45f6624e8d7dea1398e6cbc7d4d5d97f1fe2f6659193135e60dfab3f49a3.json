{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst MONTH_NAMES = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\nconst WEEK_NAMES = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\nconst getNowString = () => {\n  const now = new Date();\n  const weekDay = WEEK_NAMES[now.getUTCDay()];\n  const month = MONTH_NAMES[now.getUTCMonth()];\n  const day = now.getUTCDate();\n  let hours = now.getUTCHours();\n  if (hours < 10) {\n    hours = `0${hours}`;\n  }\n  let minutes = now.getUTCMinutes();\n  if (minutes < 10) {\n    minutes = `0${minutes}`;\n  }\n  let seconds = now.getUTCSeconds();\n  if (seconds < 10) {\n    seconds = `0${seconds}`;\n  }\n  const year = now.getUTCFullYear();\n  // ddd MMM D HH:mm:ss UTC YYYY\n  const dateNow = `${weekDay} ${month} ${day} ${hours}:${minutes}:${seconds} UTC ${year}`;\n  return dateNow;\n};\nexport { getNowString };", "map": {"version": 3, "names": ["MONTH_NAMES", "WEEK_NAMES", "getNowString", "now", "Date", "weekDay", "getUTCDay", "month", "getUTCMonth", "day", "getUTCDate", "hours", "getUTCHours", "minutes", "getUTCMinutes", "seconds", "getUTCSeconds", "year", "getUTCFullYear", "dateNow"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/srp/getNowString.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst MONTH_NAMES = [\n    'Jan',\n    'Feb',\n    'Mar',\n    'Apr',\n    'May',\n    'Jun',\n    'Jul',\n    'Aug',\n    'Sep',\n    'Oct',\n    'Nov',\n    'Dec',\n];\nconst WEEK_NAMES = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\nconst getNowString = () => {\n    const now = new Date();\n    const weekDay = WEEK_NAMES[now.getUTCDay()];\n    const month = MONTH_NAMES[now.getUTCMonth()];\n    const day = now.getUTCDate();\n    let hours = now.getUTCHours();\n    if (hours < 10) {\n        hours = `0${hours}`;\n    }\n    let minutes = now.getUTCMinutes();\n    if (minutes < 10) {\n        minutes = `0${minutes}`;\n    }\n    let seconds = now.getUTCSeconds();\n    if (seconds < 10) {\n        seconds = `0${seconds}`;\n    }\n    const year = now.getUTCFullYear();\n    // ddd MMM D HH:mm:ss UTC YYYY\n    const dateNow = `${weekDay} ${month} ${day} ${hours}:${minutes}:${seconds} UTC ${year}`;\n    return dateNow;\n};\n\nexport { getNowString };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,WAAW,GAAG,CAChB,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACR;AACD,MAAMC,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;AACpE,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;EACtB,MAAMC,OAAO,GAAGJ,UAAU,CAACE,GAAG,CAACG,SAAS,CAAC,CAAC,CAAC;EAC3C,MAAMC,KAAK,GAAGP,WAAW,CAACG,GAAG,CAACK,WAAW,CAAC,CAAC,CAAC;EAC5C,MAAMC,GAAG,GAAGN,GAAG,CAACO,UAAU,CAAC,CAAC;EAC5B,IAAIC,KAAK,GAAGR,GAAG,CAACS,WAAW,CAAC,CAAC;EAC7B,IAAID,KAAK,GAAG,EAAE,EAAE;IACZA,KAAK,GAAG,IAAIA,KAAK,EAAE;EACvB;EACA,IAAIE,OAAO,GAAGV,GAAG,CAACW,aAAa,CAAC,CAAC;EACjC,IAAID,OAAO,GAAG,EAAE,EAAE;IACdA,OAAO,GAAG,IAAIA,OAAO,EAAE;EAC3B;EACA,IAAIE,OAAO,GAAGZ,GAAG,CAACa,aAAa,CAAC,CAAC;EACjC,IAAID,OAAO,GAAG,EAAE,EAAE;IACdA,OAAO,GAAG,IAAIA,OAAO,EAAE;EAC3B;EACA,MAAME,IAAI,GAAGd,GAAG,CAACe,cAAc,CAAC,CAAC;EACjC;EACA,MAAMC,OAAO,GAAG,GAAGd,OAAO,IAAIE,KAAK,IAAIE,GAAG,IAAIE,KAAK,IAAIE,OAAO,IAAIE,OAAO,QAAQE,IAAI,EAAE;EACvF,OAAOE,OAAO;AAClB,CAAC;AAED,SAASjB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}