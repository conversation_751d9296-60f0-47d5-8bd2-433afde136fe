{"ast": null, "code": "// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nexport { convertToBuffer } from \"./convertToBuffer\";\nexport { isEmptyData } from \"./isEmptyData\";\nexport { numToUint8 } from \"./numToUint8\";\nexport { uint32ArrayFrom } from './uint32ArrayFrom';", "map": {"version": 3, "names": ["convertToBuffer", "isEmptyData", "numToUint8", "uint32ArrayFrom"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-crypto/util/build/module/index.js"], "sourcesContent": ["// Copyright Amazon.com Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nexport { convertToBuffer } from \"./convertToBuffer\";\nexport { isEmptyData } from \"./isEmptyData\";\nexport { numToUint8 } from \"./numToUint8\";\nexport { uint32ArrayFrom } from './uint32ArrayFrom';\n"], "mappings": "AAAA;AACA;AACA,SAASA,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,eAAe,QAAQ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}