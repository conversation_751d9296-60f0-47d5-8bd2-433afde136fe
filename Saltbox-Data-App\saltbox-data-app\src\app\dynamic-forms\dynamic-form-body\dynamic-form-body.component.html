<div class="grid data-entry-form">
  <div class="col-12 card-container">
    <app-sb-formly-renderer [fieldsConfig]="fieldsConfig" [baseSchema]="datastore?.baseSchema ?? schema"
      [projectVersionId]="projectVersionId" [(model)]="data" (modelChange)="dataChange($event)"
      [trackDataChangesForFormulas]="true" [triggerResetFormEvent]="triggerResetFormEvent"
      [masterRecordParams]="masterRecordParams" [partialObject]="partialObject"
      [partialObjectPath]="partialObjectPath"></app-sb-formly-renderer>
  </div>
  <div *ngIf="showFormOptions" class="col-12 px-3">
    <app-file-upload *ngIf="!dynamicForm?.additionalSettings?.hideAttachments"
      fileUploadContainerClass="embedded-upload-form" (fileUploadEvent)="onFileUploadEvent($event)"
      [projectId]="projectId" [versionId]="projectVersionId" [dataStoreName]="datastore.name" [recordId]="recordId"
      [fileService]="attachmentService" [aliasIds]="data?.__sbmeta?.Attachments" [isPreview]="isPreview"
      class="form-viewer-file-upload">
    </app-file-upload>
    <div *ngIf="!recordId && dynamicForm?.additionalSettings?.allowEmail" class="mt-3">
      <p-checkbox label="Send me a copy of my responses" [(ngModel)]="emailOnSubmit" [binary]="true"
        [disabled]="isPreview"></p-checkbox>
    </div>
  </div>
  <div class="fixed bottom-0 right-0 mr-4 mb-2" *ngIf="showSubmitButton">
    <p-button pRipple [icon]="submitBtnIcon ?? 'pi pi-send'" type="button" label="Submit"
      (onClick)="submitForm()"></p-button>
  </div>
</div>
<p-blockUI *ngIf="showSpinner" [blocked]="showSpinner" styleClass="z-5">
  <img class="ui-progress-spinner" [src]="'layout/images/salt-box-loading.gif' | assetUrl">
</p-blockUI>