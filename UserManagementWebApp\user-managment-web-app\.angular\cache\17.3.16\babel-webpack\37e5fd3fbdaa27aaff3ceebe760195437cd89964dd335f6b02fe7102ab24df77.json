{"ast": null, "code": "// TODO: update the design tokens to use an array\n// export interface FontDesignToken {\n//   value: Array<string>\n// }\nconst fonts = {\n  default: {\n    variable: {\n      value: `'InterVariable', 'Inter var', 'Inter', -apple-system, BlinkMacSystemFont,\n        'Helvetica Neue', 'Segoe UI', Oxygen, Ubuntu, Cantarell, 'Open Sans',\n        sans-serif`\n    },\n    static: {\n      value: `'Inter', -apple-system, BlinkMacSystemFont, 'Helvetica Neue',\n        'Segoe UI', Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif`\n    }\n  }\n};\nexport { fonts };", "map": {"version": 3, "names": ["fonts", "default", "variable", "value", "static"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/fonts.mjs"], "sourcesContent": ["// TODO: update the design tokens to use an array\n// export interface FontDesignToken {\n//   value: Array<string>\n// }\nconst fonts = {\n    default: {\n        variable: {\n            value: `'InterVariable', 'Inter var', 'Inter', -apple-system, BlinkMacSystemFont,\n        'Helvetica Neue', 'Segoe UI', Oxygen, Ubuntu, Cantarell, 'Open Sans',\n        sans-serif`,\n        },\n        static: {\n            value: `'Inter', -apple-system, BlinkMacSystemFont, 'Helvetica Neue',\n        'Segoe UI', Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif`,\n        },\n    },\n};\n\nexport { fonts };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,MAAMA,KAAK,GAAG;EACVC,OAAO,EAAE;IACLC,QAAQ,EAAE;MACNC,KAAK,EAAE;AACnB;AACA;IACQ,CAAC;IACDC,MAAM,EAAE;MACJD,KAAK,EAAE;AACnB;IACQ;EACJ;AACJ,CAAC;AAED,SAASH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}