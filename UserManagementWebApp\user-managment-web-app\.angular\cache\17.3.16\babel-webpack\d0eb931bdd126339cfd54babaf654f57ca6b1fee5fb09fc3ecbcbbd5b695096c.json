{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { assertTokenProviderConfig, fetchAuthSession, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { getRegionFromUserPoolId } from '../../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokens } from '../../utils/types.mjs';\nimport { toAuthUserAttribute } from '../../utils/apiHelpers.mjs';\nimport { getAuthUserAgentValue } from '../../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../../common/AuthErrorStrings.mjs';\nimport '../../../../errors/types/validation.mjs';\nimport '../../types/errors.mjs';\nimport { createGetUserClient } from '../../../../foundation/factories/serviceClients/cognitoIdentityProvider/createGetUserClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst fetchUserAttributes = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (amplify) {\n    const authConfig = amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolEndpoint,\n      userPoolId\n    } = authConfig;\n    const {\n      tokens\n    } = yield fetchAuthSession(amplify, {\n      forceRefresh: false\n    });\n    assertAuthTokens(tokens);\n    const getUser = createGetUserClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const {\n      UserAttributes\n    } = yield getUser({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.FetchUserAttributes)\n    }, {\n      AccessToken: tokens.accessToken.toString()\n    });\n    return toAuthUserAttribute(UserAttributes);\n  });\n  return function fetchUserAttributes(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { fetchUserAttributes };", "map": {"version": 3, "names": ["assertTokenProviderConfig", "fetchAuthSession", "AuthAction", "getRegionFromUserPoolId", "assertAuthTokens", "toAuthUserAttribute", "getAuthUserAgentValue", "createGetUserClient", "createCognitoUserPoolEndpointResolver", "fetchUserAttributes", "_ref", "_asyncToGenerator", "amplify", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userPoolEndpoint", "userPoolId", "tokens", "forceRefresh", "getUser", "endpointResolver", "endpointOverride", "UserAttributes", "region", "userAgentValue", "FetchUserAttributes", "AccessToken", "accessToken", "toString", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/internal/fetchUserAttributes.mjs"], "sourcesContent": ["import { assertTokenProviderConfig, fetchAuthSession, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { getRegionFromUserPoolId } from '../../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokens } from '../../utils/types.mjs';\nimport { toAuthUserAttribute } from '../../utils/apiHelpers.mjs';\nimport { getAuthUserAgentValue } from '../../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../../common/AuthErrorStrings.mjs';\nimport '../../../../errors/types/validation.mjs';\nimport '../../types/errors.mjs';\nimport { createGetUserClient } from '../../../../foundation/factories/serviceClients/cognitoIdentityProvider/createGetUserClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst fetchUserAttributes = async (amplify) => {\n    const authConfig = amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolEndpoint, userPoolId } = authConfig;\n    const { tokens } = await fetchAuthSession(amplify, {\n        forceRefresh: false,\n    });\n    assertAuthTokens(tokens);\n    const getUser = createGetUserClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const { UserAttributes } = await getUser({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.FetchUserAttributes),\n    }, {\n        AccessToken: tokens.accessToken.toString(),\n    });\n    return toAuthUserAttribute(UserAttributes);\n};\n\nexport { fetchUserAttributes };\n"], "mappings": ";AAAA,SAASA,yBAAyB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,mCAAmC;AAC3G,SAASC,uBAAuB,QAAQ,kDAAkD;AAC1F,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,2HAA2H;AAClI,OAAO,uFAAuF;AAC9F,OAAO,yCAAyC;AAChD,OAAO,yCAAyC;AAChD,OAAO,wBAAwB;AAC/B,SAASC,mBAAmB,QAAQ,iGAAiG;AACrI,SAASC,qCAAqC,QAAQ,2DAA2D;;AAEjH;AACA;AACA,MAAMC,mBAAmB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,OAAO,EAAK;IAC3C,MAAMC,UAAU,GAAGD,OAAO,CAACE,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDhB,yBAAyB,CAACa,UAAU,CAAC;IACrC,MAAM;MAAEI,gBAAgB;MAAEC;IAAW,CAAC,GAAGL,UAAU;IACnD,MAAM;MAAEM;IAAO,CAAC,SAASlB,gBAAgB,CAACW,OAAO,EAAE;MAC/CQ,YAAY,EAAE;IAClB,CAAC,CAAC;IACFhB,gBAAgB,CAACe,MAAM,CAAC;IACxB,MAAME,OAAO,GAAGd,mBAAmB,CAAC;MAChCe,gBAAgB,EAAEd,qCAAqC,CAAC;QACpDe,gBAAgB,EAAEN;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAM;MAAEO;IAAe,CAAC,SAASH,OAAO,CAAC;MACrCI,MAAM,EAAEtB,uBAAuB,CAACe,UAAU,CAAC;MAC3CQ,cAAc,EAAEpB,qBAAqB,CAACJ,UAAU,CAACyB,mBAAmB;IACxE,CAAC,EAAE;MACCC,WAAW,EAAET,MAAM,CAACU,WAAW,CAACC,QAAQ,CAAC;IAC7C,CAAC,CAAC;IACF,OAAOzB,mBAAmB,CAACmB,cAAc,CAAC;EAC9C,CAAC;EAAA,gBApBKf,mBAAmBA,CAAAsB,EAAA;IAAA,OAAArB,IAAA,CAAAsB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAoBxB;AAED,SAASxB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}