{"ast": null, "code": "import { DefaultStorage } from './DefaultStorage.mjs';\nimport { InMemoryStorage } from './InMemoryStorage.mjs';\nimport { KeyValueStorage } from './KeyValueStorage.mjs';\nimport { SessionStorage } from './SessionStorage.mjs';\nimport { SyncSessionStorage } from './SyncSessionStorage.mjs';\nexport { CookieStorage } from './CookieStorage.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst defaultStorage = new DefaultStorage();\nconst sessionStorage = new SessionStorage();\nconst syncSessionStorage = new SyncSessionStorage();\nconst sharedInMemoryStorage = new KeyValueStorage(new InMemoryStorage());\nexport { defaultStorage, sessionStorage, sharedInMemoryStorage, syncSessionStorage };", "map": {"version": 3, "names": ["DefaultStorage", "InMemoryStorage", "KeyValueStorage", "SessionStorage", "SyncSessionStorage", "Cookie<PERSON>torage", "defaultStorage", "sessionStorage", "syncSessionStorage", "sharedInMemoryStorage"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/storage/index.mjs"], "sourcesContent": ["import { DefaultStorage } from './DefaultStorage.mjs';\nimport { InMemoryStorage } from './InMemoryStorage.mjs';\nimport { KeyValueStorage } from './KeyValueStorage.mjs';\nimport { SessionStorage } from './SessionStorage.mjs';\nimport { SyncSessionStorage } from './SyncSessionStorage.mjs';\nexport { CookieStorage } from './CookieStorage.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst defaultStorage = new DefaultStorage();\nconst sessionStorage = new SessionStorage();\nconst syncSessionStorage = new SyncSessionStorage();\nconst sharedInMemoryStorage = new KeyValueStorage(new InMemoryStorage());\n\nexport { defaultStorage, sessionStorage, sharedInMemoryStorage, syncSessionStorage };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,sBAAsB;AACrD,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,aAAa,QAAQ,qBAAqB;;AAEnD;AACA;AACA,MAAMC,cAAc,GAAG,IAAIN,cAAc,CAAC,CAAC;AAC3C,MAAMO,cAAc,GAAG,IAAIJ,cAAc,CAAC,CAAC;AAC3C,MAAMK,kBAAkB,GAAG,IAAIJ,kBAAkB,CAAC,CAAC;AACnD,MAAMK,qBAAqB,GAAG,IAAIP,eAAe,CAAC,IAAID,eAAe,CAAC,CAAC,CAAC;AAExE,SAASK,cAAc,EAAEC,cAAc,EAAEE,qBAAqB,EAAED,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}