{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { GetRecordsInput, GetRecordsOutput } from \"../models/models_0\";\nimport { deserializeAws_json1_1GetRecordsCommand, serializeAws_json1_1GetRecordsCommand } from \"../protocols/Aws_json1_1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Gets data records from a Kinesis data stream's shard.</p>\n *         <p>Specify a shard iterator using the <code>ShardIterator</code> parameter. The shard\n *             iterator specifies the position in the shard from which you want to start reading data\n *             records sequentially. If there are no records available in the portion of the shard that\n *             the iterator points to, <a>GetRecords</a> returns an empty list. It might\n *             take multiple calls to get to a portion of the shard that contains records.</p>\n *         <p>You can scale by provisioning multiple shards per stream while considering service\n *             limits (for more information, see <a href=\"https://docs.aws.amazon.com/kinesis/latest/dev/service-sizes-and-limits.html\">Amazon Kinesis Data Streams\n *                 Limits</a> in the <i>Amazon Kinesis Data Streams Developer\n *                 Guide</i>). Your application should have one thread per shard, each reading\n *             continuously from its stream. To read from a stream continually, call <a>GetRecords</a> in a loop. Use <a>GetShardIterator</a> to get the\n *             shard iterator to specify in the first <a>GetRecords</a> call. <a>GetRecords</a> returns a new shard iterator in\n *                 <code>NextShardIterator</code>. Specify the shard iterator returned in\n *                 <code>NextShardIterator</code> in subsequent calls to <a>GetRecords</a>.\n *             If the shard has been closed, the shard iterator can't return more data and <a>GetRecords</a> returns <code>null</code> in <code>NextShardIterator</code>.\n *             You can terminate the loop when the shard is closed, or when the shard iterator reaches\n *             the record with the sequence number or other attribute that marks it as the last record\n *             to process.</p>\n *         <p>Each data record can be up to 1 MiB in size, and each shard can read up to 2 MiB\n *             per second. You can ensure that your calls don't exceed the maximum supported size or\n *             throughput by using the <code>Limit</code> parameter to specify the maximum number of\n *             records that <a>GetRecords</a> can return. Consider your average record size\n *             when determining this limit. The maximum number of records that can be returned per call\n *             is 10,000.</p>\n *\n *         <p>The size of the data returned by <a>GetRecords</a> varies depending on\n *             the utilization of the shard. The maximum size of data that <a>GetRecords</a>\n *             can return is 10 MiB. If a call returns this amount of data, subsequent calls made\n *             within the next 5 seconds throw <code>ProvisionedThroughputExceededException</code>. If\n *             there is insufficient provisioned throughput on the stream, subsequent calls made within\n *             the next 1 second throw <code>ProvisionedThroughputExceededException</code>. <a>GetRecords</a> doesn't return any data when it throws an exception. For this\n *             reason, we recommend that you wait 1 second between calls to <a>GetRecords</a>. However, it's possible that the application will get exceptions for longer than 1\n *             second.</p>\n *         <p>To detect whether the application is falling behind in processing, you can use the\n *                 <code>MillisBehindLatest</code> response attribute. You can also monitor the stream\n *             using CloudWatch metrics and other mechanisms (see <a href=\"https://docs.aws.amazon.com/kinesis/latest/dev/monitoring.html\">Monitoring</a> in the <i>Amazon\n *                 Kinesis Data Streams Developer Guide</i>).</p>\n *         <p>Each Amazon Kinesis record includes a value,\n *                 <code>ApproximateArrivalTimestamp</code>, that is set when a stream successfully\n *             receives and stores a record. This is commonly referred to as a server-side time stamp,\n *             whereas a client-side time stamp is set when a data producer creates or sends the record\n *             to a stream (a data producer is any data source putting data records into a stream, for\n *             example with <a>PutRecords</a>). The time stamp has millisecond precision.\n *             There are no guarantees about the time stamp accuracy, or that the time stamp is always\n *             increasing. For example, records in a shard or across a stream might have time stamps\n *             that are out of order.</p>\n *         <p>This operation has a limit of five transactions per second per shard.</p>\n */\nvar GetRecordsCommand = /** @class */function (_super) {\n  __extends(GetRecordsCommand, _super);\n  // Start section: command_properties\n  // End section: command_properties\n  function GetRecordsCommand(input) {\n    var _this =\n    // Start section: command_constructor\n    _super.call(this) || this;\n    _this.input = input;\n    return _this;\n    // End section: command_constructor\n  }\n  /**\n   * @internal\n   */\n  GetRecordsCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n    this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n    var stack = clientStack.concat(this.middlewareStack);\n    var logger = configuration.logger;\n    var clientName = \"KinesisClient\";\n    var commandName = \"GetRecordsCommand\";\n    var handlerExecutionContext = {\n      logger: logger,\n      clientName: clientName,\n      commandName: commandName,\n      inputFilterSensitiveLog: GetRecordsInput.filterSensitiveLog,\n      outputFilterSensitiveLog: GetRecordsOutput.filterSensitiveLog\n    };\n    var requestHandler = configuration.requestHandler;\n    return stack.resolve(function (request) {\n      return requestHandler.handle(request.request, options || {});\n    }, handlerExecutionContext);\n  };\n  GetRecordsCommand.prototype.serialize = function (input, context) {\n    return serializeAws_json1_1GetRecordsCommand(input, context);\n  };\n  GetRecordsCommand.prototype.deserialize = function (output, context) {\n    return deserializeAws_json1_1GetRecordsCommand(output, context);\n  };\n  return GetRecordsCommand;\n}($Command);\nexport { GetRecordsCommand };", "map": {"version": 3, "names": ["__extends", "GetRecordsInput", "GetRecordsOutput", "deserializeAws_json1_1GetRecordsCommand", "serializeAws_json1_1GetRecordsCommand", "getSerdePlugin", "Command", "$Command", "GetRecordsCommand", "_super", "input", "_this", "call", "prototype", "resolveMiddleware", "clientStack", "configuration", "options", "middlewareStack", "use", "serialize", "deserialize", "stack", "concat", "logger", "clientName", "commandName", "handlerExecutionContext", "inputFilterSensitiveLog", "filterSensitiveLog", "outputFilterSensitiveLog", "requestHandler", "resolve", "request", "handle", "context", "output"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-kinesis/dist/es/commands/GetRecordsCommand.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { GetRecordsInput, GetRecordsOutput } from \"../models/models_0\";\nimport { deserializeAws_json1_1GetRecordsCommand, serializeAws_json1_1GetRecordsCommand, } from \"../protocols/Aws_json1_1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Gets data records from a Kinesis data stream's shard.</p>\n *         <p>Specify a shard iterator using the <code>ShardIterator</code> parameter. The shard\n *             iterator specifies the position in the shard from which you want to start reading data\n *             records sequentially. If there are no records available in the portion of the shard that\n *             the iterator points to, <a>GetRecords</a> returns an empty list. It might\n *             take multiple calls to get to a portion of the shard that contains records.</p>\n *         <p>You can scale by provisioning multiple shards per stream while considering service\n *             limits (for more information, see <a href=\"https://docs.aws.amazon.com/kinesis/latest/dev/service-sizes-and-limits.html\">Amazon Kinesis Data Streams\n *                 Limits</a> in the <i>Amazon Kinesis Data Streams Developer\n *                 Guide</i>). Your application should have one thread per shard, each reading\n *             continuously from its stream. To read from a stream continually, call <a>GetRecords</a> in a loop. Use <a>GetShardIterator</a> to get the\n *             shard iterator to specify in the first <a>GetRecords</a> call. <a>GetRecords</a> returns a new shard iterator in\n *                 <code>NextShardIterator</code>. Specify the shard iterator returned in\n *                 <code>NextShardIterator</code> in subsequent calls to <a>GetRecords</a>.\n *             If the shard has been closed, the shard iterator can't return more data and <a>GetRecords</a> returns <code>null</code> in <code>NextShardIterator</code>.\n *             You can terminate the loop when the shard is closed, or when the shard iterator reaches\n *             the record with the sequence number or other attribute that marks it as the last record\n *             to process.</p>\n *         <p>Each data record can be up to 1 MiB in size, and each shard can read up to 2 MiB\n *             per second. You can ensure that your calls don't exceed the maximum supported size or\n *             throughput by using the <code>Limit</code> parameter to specify the maximum number of\n *             records that <a>GetRecords</a> can return. Consider your average record size\n *             when determining this limit. The maximum number of records that can be returned per call\n *             is 10,000.</p>\n *\n *         <p>The size of the data returned by <a>GetRecords</a> varies depending on\n *             the utilization of the shard. The maximum size of data that <a>GetRecords</a>\n *             can return is 10 MiB. If a call returns this amount of data, subsequent calls made\n *             within the next 5 seconds throw <code>ProvisionedThroughputExceededException</code>. If\n *             there is insufficient provisioned throughput on the stream, subsequent calls made within\n *             the next 1 second throw <code>ProvisionedThroughputExceededException</code>. <a>GetRecords</a> doesn't return any data when it throws an exception. For this\n *             reason, we recommend that you wait 1 second between calls to <a>GetRecords</a>. However, it's possible that the application will get exceptions for longer than 1\n *             second.</p>\n *         <p>To detect whether the application is falling behind in processing, you can use the\n *                 <code>MillisBehindLatest</code> response attribute. You can also monitor the stream\n *             using CloudWatch metrics and other mechanisms (see <a href=\"https://docs.aws.amazon.com/kinesis/latest/dev/monitoring.html\">Monitoring</a> in the <i>Amazon\n *                 Kinesis Data Streams Developer Guide</i>).</p>\n *         <p>Each Amazon Kinesis record includes a value,\n *                 <code>ApproximateArrivalTimestamp</code>, that is set when a stream successfully\n *             receives and stores a record. This is commonly referred to as a server-side time stamp,\n *             whereas a client-side time stamp is set when a data producer creates or sends the record\n *             to a stream (a data producer is any data source putting data records into a stream, for\n *             example with <a>PutRecords</a>). The time stamp has millisecond precision.\n *             There are no guarantees about the time stamp accuracy, or that the time stamp is always\n *             increasing. For example, records in a shard or across a stream might have time stamps\n *             that are out of order.</p>\n *         <p>This operation has a limit of five transactions per second per shard.</p>\n */\nvar GetRecordsCommand = /** @class */ (function (_super) {\n    __extends(GetRecordsCommand, _super);\n    // Start section: command_properties\n    // End section: command_properties\n    function GetRecordsCommand(input) {\n        var _this = \n        // Start section: command_constructor\n        _super.call(this) || this;\n        _this.input = input;\n        return _this;\n        // End section: command_constructor\n    }\n    /**\n     * @internal\n     */\n    GetRecordsCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n        this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n        var stack = clientStack.concat(this.middlewareStack);\n        var logger = configuration.logger;\n        var clientName = \"KinesisClient\";\n        var commandName = \"GetRecordsCommand\";\n        var handlerExecutionContext = {\n            logger: logger,\n            clientName: clientName,\n            commandName: commandName,\n            inputFilterSensitiveLog: GetRecordsInput.filterSensitiveLog,\n            outputFilterSensitiveLog: GetRecordsOutput.filterSensitiveLog,\n        };\n        var requestHandler = configuration.requestHandler;\n        return stack.resolve(function (request) {\n            return requestHandler.handle(request.request, options || {});\n        }, handlerExecutionContext);\n    };\n    GetRecordsCommand.prototype.serialize = function (input, context) {\n        return serializeAws_json1_1GetRecordsCommand(input, context);\n    };\n    GetRecordsCommand.prototype.deserialize = function (output, context) {\n        return deserializeAws_json1_1GetRecordsCommand(output, context);\n    };\n    return GetRecordsCommand;\n}($Command));\nexport { GetRecordsCommand };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,oBAAoB;AACtE,SAASC,uCAAuC,EAAEC,qCAAqC,QAAS,0BAA0B;AAC1H,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,OAAO,IAAIC,QAAQ,QAAQ,wBAAwB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,iBAAiB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACrDT,SAAS,CAACQ,iBAAiB,EAAEC,MAAM,CAAC;EACpC;EACA;EACA,SAASD,iBAAiBA,CAACE,KAAK,EAAE;IAC9B,IAAIC,KAAK;IACT;IACAF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACzBD,KAAK,CAACD,KAAK,GAAGA,KAAK;IACnB,OAAOC,KAAK;IACZ;EACJ;EACA;AACJ;AACA;EACIH,iBAAiB,CAACK,SAAS,CAACC,iBAAiB,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAEC,OAAO,EAAE;IAC3F,IAAI,CAACC,eAAe,CAACC,GAAG,CAACd,cAAc,CAACW,aAAa,EAAE,IAAI,CAACI,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;IACzF,IAAIC,KAAK,GAAGP,WAAW,CAACQ,MAAM,CAAC,IAAI,CAACL,eAAe,CAAC;IACpD,IAAIM,MAAM,GAAGR,aAAa,CAACQ,MAAM;IACjC,IAAIC,UAAU,GAAG,eAAe;IAChC,IAAIC,WAAW,GAAG,mBAAmB;IACrC,IAAIC,uBAAuB,GAAG;MAC1BH,MAAM,EAAEA,MAAM;MACdC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA,WAAW;MACxBE,uBAAuB,EAAE3B,eAAe,CAAC4B,kBAAkB;MAC3DC,wBAAwB,EAAE5B,gBAAgB,CAAC2B;IAC/C,CAAC;IACD,IAAIE,cAAc,GAAGf,aAAa,CAACe,cAAc;IACjD,OAAOT,KAAK,CAACU,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpC,OAAOF,cAAc,CAACG,MAAM,CAACD,OAAO,CAACA,OAAO,EAAEhB,OAAO,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC,EAAEU,uBAAuB,CAAC;EAC/B,CAAC;EACDnB,iBAAiB,CAACK,SAAS,CAACO,SAAS,GAAG,UAAUV,KAAK,EAAEyB,OAAO,EAAE;IAC9D,OAAO/B,qCAAqC,CAACM,KAAK,EAAEyB,OAAO,CAAC;EAChE,CAAC;EACD3B,iBAAiB,CAACK,SAAS,CAACQ,WAAW,GAAG,UAAUe,MAAM,EAAED,OAAO,EAAE;IACjE,OAAOhC,uCAAuC,CAACiC,MAAM,EAAED,OAAO,CAAC;EACnE,CAAC;EACD,OAAO3B,iBAAiB;AAC5B,CAAC,CAACD,QAAQ,CAAE;AACZ,SAASC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}