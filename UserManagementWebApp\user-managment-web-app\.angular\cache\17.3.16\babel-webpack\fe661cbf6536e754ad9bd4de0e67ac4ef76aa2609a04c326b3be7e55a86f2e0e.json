{"ast": null, "code": "export const toUtf8 = input => {\n  if (typeof input === \"string\") {\n    return input;\n  }\n  if (typeof input !== \"object\" || typeof input.byteOffset !== \"number\" || typeof input.byteLength !== \"number\") {\n    throw new Error(\"@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.\");\n  }\n  return new TextDecoder(\"utf-8\").decode(input);\n};", "map": {"version": 3, "names": ["toUtf8", "input", "byteOffset", "byteLength", "Error", "TextDecoder", "decode"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@smithy/util-utf8/dist-es/toUtf8.browser.js"], "sourcesContent": ["export const toUtf8 = (input) => {\n    if (typeof input === \"string\") {\n        return input;\n    }\n    if (typeof input !== \"object\" || typeof input.byteOffset !== \"number\" || typeof input.byteLength !== \"number\") {\n        throw new Error(\"@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.\");\n    }\n    return new TextDecoder(\"utf-8\").decode(input);\n};\n"], "mappings": "AAAA,OAAO,MAAMA,MAAM,GAAIC,KAAK,IAAK;EAC7B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC3B,OAAOA,KAAK;EAChB;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAACC,UAAU,KAAK,QAAQ,IAAI,OAAOD,KAAK,CAACE,UAAU,KAAK,QAAQ,EAAE;IAC3G,MAAM,IAAIC,KAAK,CAAC,8EAA8E,CAAC;EACnG;EACA,OAAO,IAAIC,WAAW,CAAC,OAAO,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;AACjD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}