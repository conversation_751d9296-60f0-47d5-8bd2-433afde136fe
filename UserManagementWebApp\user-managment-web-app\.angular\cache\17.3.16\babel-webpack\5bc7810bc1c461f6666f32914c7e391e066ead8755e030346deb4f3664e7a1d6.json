{"ast": null, "code": "const searchfield = {\n  color: {\n    value: '{components.fieldcontrol.color.value}'\n  },\n  button: {\n    color: {\n      value: '{components.button.color.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.primary.value}'\n    },\n    _active: {\n      backgroundColor: {\n        value: '{components.button._active.backgroundColor.value}'\n      },\n      borderColor: {\n        value: '{components.button._active.borderColor.value}'\n      },\n      color: {\n        value: '{components.button._active.color.value}'\n      }\n    },\n    _disabled: {\n      backgroundColor: {\n        value: '{components.button._disabled.backgroundColor.value}'\n      },\n      borderColor: {\n        value: '{components.button._disabled.borderColor.value}'\n      },\n      color: {\n        value: '{components.button._disabled.color.value}'\n      }\n    },\n    _focus: {\n      backgroundColor: {\n        value: '{components.button._focus.backgroundColor.value}'\n      },\n      borderColor: {\n        value: '{components.button._focus.borderColor.value}'\n      },\n      color: {\n        value: '{components.button._focus.color.value}'\n      }\n    },\n    _hover: {\n      backgroundColor: {\n        value: '{components.button._hover.backgroundColor.value}'\n      },\n      borderColor: {\n        value: '{components.button._hover.borderColor.value}'\n      },\n      color: {\n        value: '{components.button._hover.color.value}'\n      }\n    }\n  }\n};\nexport { searchfield };", "map": {"version": 3, "names": ["searchfield", "color", "value", "button", "backgroundColor", "_active", "borderColor", "_disabled", "_focus", "_hover"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/searchField.mjs"], "sourcesContent": ["const searchfield = {\n    color: { value: '{components.fieldcontrol.color.value}' },\n    button: {\n        color: { value: '{components.button.color.value}' },\n        backgroundColor: { value: '{colors.background.primary.value}' },\n        _active: {\n            backgroundColor: {\n                value: '{components.button._active.backgroundColor.value}',\n            },\n            borderColor: { value: '{components.button._active.borderColor.value}' },\n            color: { value: '{components.button._active.color.value}' },\n        },\n        _disabled: {\n            backgroundColor: {\n                value: '{components.button._disabled.backgroundColor.value}',\n            },\n            borderColor: {\n                value: '{components.button._disabled.borderColor.value}',\n            },\n            color: { value: '{components.button._disabled.color.value}' },\n        },\n        _focus: {\n            backgroundColor: {\n                value: '{components.button._focus.backgroundColor.value}',\n            },\n            borderColor: { value: '{components.button._focus.borderColor.value}' },\n            color: { value: '{components.button._focus.color.value}' },\n        },\n        _hover: {\n            backgroundColor: {\n                value: '{components.button._hover.backgroundColor.value}',\n            },\n            borderColor: { value: '{components.button._hover.borderColor.value}' },\n            color: { value: '{components.button._hover.color.value}' },\n        },\n    },\n};\n\nexport { searchfield };\n"], "mappings": "AAAA,MAAMA,WAAW,GAAG;EAChBC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAwC,CAAC;EACzDC,MAAM,EAAE;IACJF,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAkC,CAAC;IACnDE,eAAe,EAAE;MAAEF,KAAK,EAAE;IAAoC,CAAC;IAC/DG,OAAO,EAAE;MACLD,eAAe,EAAE;QACbF,KAAK,EAAE;MACX,CAAC;MACDI,WAAW,EAAE;QAAEJ,KAAK,EAAE;MAAgD,CAAC;MACvED,KAAK,EAAE;QAAEC,KAAK,EAAE;MAA0C;IAC9D,CAAC;IACDK,SAAS,EAAE;MACPH,eAAe,EAAE;QACbF,KAAK,EAAE;MACX,CAAC;MACDI,WAAW,EAAE;QACTJ,KAAK,EAAE;MACX,CAAC;MACDD,KAAK,EAAE;QAAEC,KAAK,EAAE;MAA4C;IAChE,CAAC;IACDM,MAAM,EAAE;MACJJ,eAAe,EAAE;QACbF,KAAK,EAAE;MACX,CAAC;MACDI,WAAW,EAAE;QAAEJ,KAAK,EAAE;MAA+C,CAAC;MACtED,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAyC;IAC7D,CAAC;IACDO,MAAM,EAAE;MACJL,eAAe,EAAE;QACbF,KAAK,EAAE;MACX,CAAC;MACDI,WAAW,EAAE;QAAEJ,KAAK,EAAE;MAA+C,CAAC;MACtED,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAyC;IAC7D;EACJ;AACJ,CAAC;AAED,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}