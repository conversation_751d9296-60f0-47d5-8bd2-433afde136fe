{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ConsoleLogger } from '../../Logger/ConsoleLogger.mjs';\nimport { isNonRetryableError } from './isNonRetryableError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('retryUtil');\n/**\n * @private\n * Internal use of Amplify only\n */\nfunction retry(_x, _x2, _x3, _x4) {\n  return _retry.apply(this, arguments);\n}\nfunction _retry() {\n  _retry = _asyncToGenerator(function* (functionToRetry, args, delayFn, onTerminate) {\n    if (typeof functionToRetry !== 'function') {\n      throw Error('functionToRetry must be a function');\n    }\n    // TODO(eslint): remove this linter suppression with refactoring.\n    // eslint-disable-next-line no-async-promise-executor\n    return new Promise(/*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (resolve, reject) {\n        let attempt = 0;\n        let terminated = false;\n        let timeout;\n        let wakeUp = () => {\n          // no-op\n        }; // will be replaced with a resolver()\n        // used after the loop if terminated while waiting for a timer.\n        let lastError;\n        onTerminate && onTerminate.then(() => {\n          // signal not to try anymore.\n          terminated = true;\n          // stop sleeping if we're sleeping.\n          clearTimeout(timeout);\n          wakeUp();\n        });\n        // TODO(eslint): remove this linter suppression with refactoring.\n        // eslint-disable-next-line no-unmodified-loop-condition\n        while (!terminated) {\n          attempt++;\n          logger.debug(`${functionToRetry.name} attempt #${attempt} with this vars: ${JSON.stringify(args)}`);\n          try {\n            resolve(yield functionToRetry(...args));\n            return;\n          } catch (err) {\n            lastError = err;\n            logger.debug(`error on ${functionToRetry.name}`, err);\n            if (isNonRetryableError(err)) {\n              logger.debug(`${functionToRetry.name} non retryable error`, err);\n              reject(err);\n              return;\n            }\n            const retryIn = delayFn(attempt, args, err);\n            logger.debug(`${functionToRetry.name} retrying in ${retryIn} ms`);\n            // we check `terminated` again here because it could have flipped\n            // in the time it took `functionToRetry` to return.\n            if (retryIn === false || terminated) {\n              reject(err);\n              return;\n            } else {\n              yield new Promise(_resolve => {\n                wakeUp = _resolve; // export wakeUp for onTerminate handling\n                timeout = setTimeout(wakeUp, retryIn);\n              });\n            }\n          }\n        }\n        // reached if terminated while waiting for a timer.\n        reject(lastError);\n      });\n      return function (_x5, _x6) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  });\n  return _retry.apply(this, arguments);\n}\nexport { retry };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isNonRetryableError", "logger", "retry", "_x", "_x2", "_x3", "_x4", "_retry", "apply", "arguments", "_asyncToGenerator", "functionToRetry", "args", "delayFn", "onTerminate", "Error", "Promise", "_ref", "resolve", "reject", "attempt", "terminated", "timeout", "wakeUp", "lastError", "then", "clearTimeout", "debug", "name", "JSON", "stringify", "err", "retryIn", "_resolve", "setTimeout", "_x5", "_x6"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/retry/retry.mjs"], "sourcesContent": ["import { ConsoleLogger } from '../../Logger/ConsoleLogger.mjs';\nimport { isNonRetryableError } from './isNonRetryableError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('retryUtil');\n/**\n * @private\n * Internal use of Amplify only\n */\nasync function retry(functionToRetry, args, delayFn, onTerminate) {\n    if (typeof functionToRetry !== 'function') {\n        throw Error('functionToRetry must be a function');\n    }\n    // TODO(eslint): remove this linter suppression with refactoring.\n    // eslint-disable-next-line no-async-promise-executor\n    return new Promise(async (resolve, reject) => {\n        let attempt = 0;\n        let terminated = false;\n        let timeout;\n        let wakeUp = () => {\n            // no-op\n        }; // will be replaced with a resolver()\n        // used after the loop if terminated while waiting for a timer.\n        let lastError;\n        onTerminate &&\n            onTerminate.then(() => {\n                // signal not to try anymore.\n                terminated = true;\n                // stop sleeping if we're sleeping.\n                clearTimeout(timeout);\n                wakeUp();\n            });\n        // TODO(eslint): remove this linter suppression with refactoring.\n        // eslint-disable-next-line no-unmodified-loop-condition\n        while (!terminated) {\n            attempt++;\n            logger.debug(`${functionToRetry.name} attempt #${attempt} with this vars: ${JSON.stringify(args)}`);\n            try {\n                resolve(await functionToRetry(...args));\n                return;\n            }\n            catch (err) {\n                lastError = err;\n                logger.debug(`error on ${functionToRetry.name}`, err);\n                if (isNonRetryableError(err)) {\n                    logger.debug(`${functionToRetry.name} non retryable error`, err);\n                    reject(err);\n                    return;\n                }\n                const retryIn = delayFn(attempt, args, err);\n                logger.debug(`${functionToRetry.name} retrying in ${retryIn} ms`);\n                // we check `terminated` again here because it could have flipped\n                // in the time it took `functionToRetry` to return.\n                if (retryIn === false || terminated) {\n                    reject(err);\n                    return;\n                }\n                else {\n                    await new Promise(_resolve => {\n                        wakeUp = _resolve; // export wakeUp for onTerminate handling\n                        timeout = setTimeout(wakeUp, retryIn);\n                    });\n                }\n            }\n        }\n        // reached if terminated while waiting for a timer.\n        reject(lastError);\n    });\n}\n\nexport { retry };\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,gCAAgC;AAC9D,SAASC,mBAAmB,QAAQ,2BAA2B;;AAE/D;AACA;AACA,MAAMC,MAAM,GAAG,IAAIF,aAAa,CAAC,WAAW,CAAC;AAC7C;AACA;AACA;AACA;AAHA,SAIeG,KAAKA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,MAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,OAAA;EAAAA,MAAA,GAAAG,iBAAA,CAApB,WAAqBC,eAAe,EAAEC,IAAI,EAAEC,OAAO,EAAEC,WAAW,EAAE;IAC9D,IAAI,OAAOH,eAAe,KAAK,UAAU,EAAE;MACvC,MAAMI,KAAK,CAAC,oCAAoC,CAAC;IACrD;IACA;IACA;IACA,OAAO,IAAIC,OAAO;MAAA,IAAAC,IAAA,GAAAP,iBAAA,CAAC,WAAOQ,OAAO,EAAEC,MAAM,EAAK;QAC1C,IAAIC,OAAO,GAAG,CAAC;QACf,IAAIC,UAAU,GAAG,KAAK;QACtB,IAAIC,OAAO;QACX,IAAIC,MAAM,GAAGA,CAAA,KAAM;UACf;QAAA,CACH,CAAC,CAAC;QACH;QACA,IAAIC,SAAS;QACbV,WAAW,IACPA,WAAW,CAACW,IAAI,CAAC,MAAM;UACnB;UACAJ,UAAU,GAAG,IAAI;UACjB;UACAK,YAAY,CAACJ,OAAO,CAAC;UACrBC,MAAM,CAAC,CAAC;QACZ,CAAC,CAAC;QACN;QACA;QACA,OAAO,CAACF,UAAU,EAAE;UAChBD,OAAO,EAAE;UACTnB,MAAM,CAAC0B,KAAK,CAAC,GAAGhB,eAAe,CAACiB,IAAI,aAAaR,OAAO,oBAAoBS,IAAI,CAACC,SAAS,CAAClB,IAAI,CAAC,EAAE,CAAC;UACnG,IAAI;YACAM,OAAO,OAAOP,eAAe,CAAC,GAAGC,IAAI,CAAC,CAAC;YACvC;UACJ,CAAC,CACD,OAAOmB,GAAG,EAAE;YACRP,SAAS,GAAGO,GAAG;YACf9B,MAAM,CAAC0B,KAAK,CAAC,YAAYhB,eAAe,CAACiB,IAAI,EAAE,EAAEG,GAAG,CAAC;YACrD,IAAI/B,mBAAmB,CAAC+B,GAAG,CAAC,EAAE;cAC1B9B,MAAM,CAAC0B,KAAK,CAAC,GAAGhB,eAAe,CAACiB,IAAI,sBAAsB,EAAEG,GAAG,CAAC;cAChEZ,MAAM,CAACY,GAAG,CAAC;cACX;YACJ;YACA,MAAMC,OAAO,GAAGnB,OAAO,CAACO,OAAO,EAAER,IAAI,EAAEmB,GAAG,CAAC;YAC3C9B,MAAM,CAAC0B,KAAK,CAAC,GAAGhB,eAAe,CAACiB,IAAI,gBAAgBI,OAAO,KAAK,CAAC;YACjE;YACA;YACA,IAAIA,OAAO,KAAK,KAAK,IAAIX,UAAU,EAAE;cACjCF,MAAM,CAACY,GAAG,CAAC;cACX;YACJ,CAAC,MACI;cACD,MAAM,IAAIf,OAAO,CAACiB,QAAQ,IAAI;gBAC1BV,MAAM,GAAGU,QAAQ,CAAC,CAAC;gBACnBX,OAAO,GAAGY,UAAU,CAACX,MAAM,EAAES,OAAO,CAAC;cACzC,CAAC,CAAC;YACN;UACJ;QACJ;QACA;QACAb,MAAM,CAACK,SAAS,CAAC;MACrB,CAAC;MAAA,iBAAAW,GAAA,EAAAC,GAAA;QAAA,OAAAnB,IAAA,CAAAT,KAAA,OAAAC,SAAA;MAAA;IAAA,IAAC;EACN,CAAC;EAAA,OAAAF,MAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASP,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}