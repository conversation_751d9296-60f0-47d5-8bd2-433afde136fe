{"ast": null, "code": "const switchfield = {\n  // States\n  _disabled: {\n    opacity: {\n      value: '{opacities.60.value}'\n    }\n  },\n  _focused: {\n    shadow: {\n      value: '{components.fieldcontrol._focus.boxShadow.value}'\n    }\n  },\n  // Sizes\n  fontSize: {\n    value: '{fontSizes.medium.value}'\n  },\n  large: {\n    fontSize: {\n      value: '{fontSizes.large.value}'\n    }\n  },\n  small: {\n    fontSize: {\n      value: '{fontSizes.small.value}'\n    }\n  },\n  // Child elements\n  label: {\n    padding: {\n      value: '{space.xs.value}'\n    }\n  },\n  thumb: {\n    backgroundColor: {\n      value: '{colors.background.primary.value}'\n    },\n    borderColor: {\n      value: 'transparent'\n    },\n    borderWidth: {\n      value: '{borderWidths.small.value}'\n    },\n    borderStyle: {\n      value: 'solid'\n    },\n    borderRadius: {\n      value: '{radii.xxxl.value}'\n    },\n    checked: {\n      transform: {\n        value: '{transforms.slideX.medium.value}'\n      }\n    },\n    transition: {\n      duration: {\n        value: '{time.medium.value}'\n      }\n    },\n    width: {\n      value: '{space.relative.medium.value}'\n    }\n  },\n  track: {\n    backgroundColor: {\n      value: '{colors.background.quaternary.value}'\n    },\n    borderRadius: {\n      value: '{radii.xxxl.value}'\n    },\n    checked: {\n      backgroundColor: {\n        value: '{colors.primary.80.value}'\n      }\n    },\n    height: {\n      value: '{space.relative.medium.value}'\n    },\n    padding: {\n      value: '{outlineWidths.medium.value}'\n    },\n    transition: {\n      duration: {\n        value: '{time.short.value}'\n      }\n    },\n    width: {\n      value: '{space.relative.xl.value}'\n    },\n    _error: {\n      backgroundColor: {\n        value: '{colors.background.error.value}'\n      }\n    }\n  }\n};\nexport { switchfield };", "map": {"version": 3, "names": ["switchfield", "_disabled", "opacity", "value", "_focused", "shadow", "fontSize", "large", "small", "label", "padding", "thumb", "backgroundColor", "borderColor", "borderWidth", "borderStyle", "borderRadius", "checked", "transform", "transition", "duration", "width", "track", "height", "_error"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/switchField.mjs"], "sourcesContent": ["const switchfield = {\n    // States\n    _disabled: {\n        opacity: { value: '{opacities.60.value}' },\n    },\n    _focused: {\n        shadow: {\n            value: '{components.fieldcontrol._focus.boxShadow.value}',\n        },\n    },\n    // Sizes\n    fontSize: { value: '{fontSizes.medium.value}' },\n    large: {\n        fontSize: { value: '{fontSizes.large.value}' },\n    },\n    small: {\n        fontSize: { value: '{fontSizes.small.value}' },\n    },\n    // Child elements\n    label: {\n        padding: { value: '{space.xs.value}' },\n    },\n    thumb: {\n        backgroundColor: { value: '{colors.background.primary.value}' },\n        borderColor: { value: 'transparent' },\n        borderWidth: { value: '{borderWidths.small.value}' },\n        borderStyle: { value: 'solid' },\n        borderRadius: { value: '{radii.xxxl.value}' },\n        checked: {\n            transform: { value: '{transforms.slideX.medium.value}' },\n        },\n        transition: {\n            duration: { value: '{time.medium.value}' },\n        },\n        width: { value: '{space.relative.medium.value}' },\n    },\n    track: {\n        backgroundColor: { value: '{colors.background.quaternary.value}' },\n        borderRadius: { value: '{radii.xxxl.value}' },\n        checked: {\n            backgroundColor: { value: '{colors.primary.80.value}' },\n        },\n        height: { value: '{space.relative.medium.value}' },\n        padding: { value: '{outlineWidths.medium.value}' },\n        transition: {\n            duration: { value: '{time.short.value}' },\n        },\n        width: { value: '{space.relative.xl.value}' },\n        _error: {\n            backgroundColor: { value: '{colors.background.error.value}' },\n        },\n    },\n};\n\nexport { switchfield };\n"], "mappings": "AAAA,MAAMA,WAAW,GAAG;EAChB;EACAC,SAAS,EAAE;IACPC,OAAO,EAAE;MAAEC,KAAK,EAAE;IAAuB;EAC7C,CAAC;EACDC,QAAQ,EAAE;IACNC,MAAM,EAAE;MACJF,KAAK,EAAE;IACX;EACJ,CAAC;EACD;EACAG,QAAQ,EAAE;IAAEH,KAAK,EAAE;EAA2B,CAAC;EAC/CI,KAAK,EAAE;IACHD,QAAQ,EAAE;MAAEH,KAAK,EAAE;IAA0B;EACjD,CAAC;EACDK,KAAK,EAAE;IACHF,QAAQ,EAAE;MAAEH,KAAK,EAAE;IAA0B;EACjD,CAAC;EACD;EACAM,KAAK,EAAE;IACHC,OAAO,EAAE;MAAEP,KAAK,EAAE;IAAmB;EACzC,CAAC;EACDQ,KAAK,EAAE;IACHC,eAAe,EAAE;MAAET,KAAK,EAAE;IAAoC,CAAC;IAC/DU,WAAW,EAAE;MAAEV,KAAK,EAAE;IAAc,CAAC;IACrCW,WAAW,EAAE;MAAEX,KAAK,EAAE;IAA6B,CAAC;IACpDY,WAAW,EAAE;MAAEZ,KAAK,EAAE;IAAQ,CAAC;IAC/Ba,YAAY,EAAE;MAAEb,KAAK,EAAE;IAAqB,CAAC;IAC7Cc,OAAO,EAAE;MACLC,SAAS,EAAE;QAAEf,KAAK,EAAE;MAAmC;IAC3D,CAAC;IACDgB,UAAU,EAAE;MACRC,QAAQ,EAAE;QAAEjB,KAAK,EAAE;MAAsB;IAC7C,CAAC;IACDkB,KAAK,EAAE;MAAElB,KAAK,EAAE;IAAgC;EACpD,CAAC;EACDmB,KAAK,EAAE;IACHV,eAAe,EAAE;MAAET,KAAK,EAAE;IAAuC,CAAC;IAClEa,YAAY,EAAE;MAAEb,KAAK,EAAE;IAAqB,CAAC;IAC7Cc,OAAO,EAAE;MACLL,eAAe,EAAE;QAAET,KAAK,EAAE;MAA4B;IAC1D,CAAC;IACDoB,MAAM,EAAE;MAAEpB,KAAK,EAAE;IAAgC,CAAC;IAClDO,OAAO,EAAE;MAAEP,KAAK,EAAE;IAA+B,CAAC;IAClDgB,UAAU,EAAE;MACRC,QAAQ,EAAE;QAAEjB,KAAK,EAAE;MAAqB;IAC5C,CAAC;IACDkB,KAAK,EAAE;MAAElB,KAAK,EAAE;IAA4B,CAAC;IAC7CqB,MAAM,EAAE;MACJZ,eAAe,EAAE;QAAET,KAAK,EAAE;MAAkC;IAChE;EACJ;AACJ,CAAC;AAED,SAASH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}