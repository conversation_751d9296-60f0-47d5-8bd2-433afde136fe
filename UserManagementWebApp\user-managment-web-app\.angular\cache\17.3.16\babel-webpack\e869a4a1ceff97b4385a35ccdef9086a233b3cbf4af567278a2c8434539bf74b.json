{"ast": null, "code": "import { createMachine } from 'xstate';\nimport { signOut } from 'aws-amplify/auth';\nconst signOutActor = () => {\n  return createMachine({\n    initial: 'pending',\n    id: 'signOutActor',\n    predictableActionArguments: true,\n    states: {\n      pending: {\n        tags: 'pending',\n        invoke: {\n          src: 'signOut',\n          onDone: 'resolved',\n          onError: 'rejected'\n        }\n      },\n      resolved: {\n        type: 'final'\n      },\n      rejected: {\n        type: 'final'\n      }\n    }\n  }, {\n    services: {\n      signOut: () => signOut()\n    }\n  });\n};\nexport { signOutActor };", "map": {"version": 3, "names": ["createMachine", "signOut", "signOutActor", "initial", "id", "predictableActionArguments", "states", "pending", "tags", "invoke", "src", "onDone", "onError", "resolved", "type", "rejected", "services"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/machines/authenticator/actors/signOut.mjs"], "sourcesContent": ["import { createMachine } from 'xstate';\nimport { signOut } from 'aws-amplify/auth';\n\nconst signOutActor = () => {\n    return createMachine({\n        initial: 'pending',\n        id: 'signOutActor',\n        predictableActionArguments: true,\n        states: {\n            pending: {\n                tags: 'pending',\n                invoke: {\n                    src: 'signOut',\n                    onDone: 'resolved',\n                    onError: 'rejected',\n                },\n            },\n            resolved: { type: 'final' },\n            rejected: { type: 'final' },\n        },\n    }, {\n        services: {\n            signOut: () => signOut(),\n        },\n    });\n};\n\nexport { signOutActor };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,QAAQ;AACtC,SAASC,OAAO,QAAQ,kBAAkB;AAE1C,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,OAAOF,aAAa,CAAC;IACjBG,OAAO,EAAE,SAAS;IAClBC,EAAE,EAAE,cAAc;IAClBC,0BAA0B,EAAE,IAAI;IAChCC,MAAM,EAAE;MACJC,OAAO,EAAE;QACLC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;UACJC,GAAG,EAAE,SAAS;UACdC,MAAM,EAAE,UAAU;UAClBC,OAAO,EAAE;QACb;MACJ,CAAC;MACDC,QAAQ,EAAE;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAC3BC,QAAQ,EAAE;QAAED,IAAI,EAAE;MAAQ;IAC9B;EACJ,CAAC,EAAE;IACCE,QAAQ,EAAE;MACNf,OAAO,EAAEA,CAAA,KAAMA,OAAO,CAAC;IAC3B;EACJ,CAAC,CAAC;AACN,CAAC;AAED,SAASC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}