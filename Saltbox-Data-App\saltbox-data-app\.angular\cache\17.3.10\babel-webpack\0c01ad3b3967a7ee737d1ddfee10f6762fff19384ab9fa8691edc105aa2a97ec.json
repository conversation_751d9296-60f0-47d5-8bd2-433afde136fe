{"ast": null, "code": "import { DynamicFormLayout<PERSON>reas } from '../enums/dynamic-form';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ButtonModule } from 'primeng/button';\nimport { NgIf } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/dynamic-forms.service\";\nimport * as i2 from \"@angular/platform-browser\";\nimport * as i3 from \"primeng/button\";\nimport * as i4 from \"primeng/tooltip\";\nfunction DynamicFormSectionComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 7)(4, \"p-button\", 8);\n    i0.ɵɵlistener(\"onClick\", function DynamicFormSectionComponent_div_1_div_1_Template_p_button_onClick_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editSection());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-button\", 9);\n    i0.ɵɵlistener(\"onClick\", function DynamicFormSectionComponent_div_1_div_1_Template_p_button_onClick_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteSection());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.areaName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled)(\"text\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled)(\"text\", true);\n  }\n}\nfunction DynamicFormSectionComponent_div_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.areaName, \" Content\");\n  }\n}\nfunction DynamicFormSectionComponent_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.domSanitizer.bypassSecurityTrustHtml(ctx_r1.layoutSection == null ? null : ctx_r1.layoutSection.content), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction DynamicFormSectionComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, DynamicFormSectionComponent_div_1_div_1_Template, 6, 5, \"div\", 3)(2, DynamicFormSectionComponent_div_1_span_2_Template, 2, 1, \"span\", 4)(3, DynamicFormSectionComponent_div_1_div_3_Template, 2, 1, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isPreview);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r1.layoutSection == null ? null : ctx_r1.layoutSection.content));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.layoutSection == null ? null : ctx_r1.layoutSection.content);\n  }\n}\nexport class DynamicFormSectionComponent {\n  //Note: we use the sanitizer in the html\n  constructor(dynamicFormsService, domSanitizer) {\n    this.dynamicFormsService = dynamicFormsService;\n    this.domSanitizer = domSanitizer;\n    this.disabled = false;\n  }\n  get layoutSection() {\n    if (this.dynamicForm?.layout && this.layoutArea) {\n      return this.dynamicForm.layout[this.layoutArea];\n    }\n    return undefined;\n  }\n  get areaName() {\n    switch (this.layoutSection?.area) {\n      case DynamicFormLayoutAreas.Footer:\n        return 'Footer';\n      case DynamicFormLayoutAreas.Instruction:\n        return 'Instruction';\n      case DynamicFormLayoutAreas.HeaderLeft:\n        return 'Left Header';\n      case DynamicFormLayoutAreas.HeaderRight:\n        return 'Right Header';\n      default:\n        return undefined;\n    }\n  }\n  editSection() {\n    if (this.disabled) {\n      return;\n    }\n    if (this.layoutSection) this.dynamicFormsService.showLayoutOptions.next(this.layoutSection);\n  }\n  deleteSection() {\n    if (this.disabled) {\n      return;\n    }\n    if (this.layoutSection) this.layoutSection.visible = false;\n  }\n  static {\n    this.ɵfac = function DynamicFormSectionComponent_Factory(t) {\n      return new (t || DynamicFormSectionComponent)(i0.ɵɵdirectiveInject(i1.DynamicFormsService), i0.ɵɵdirectiveInject(i2.DomSanitizer));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DynamicFormSectionComponent,\n      selectors: [[\"app-dynamic-form-section\"]],\n      inputs: {\n        layoutArea: \"layoutArea\",\n        class: \"class\",\n        isPreview: \"isPreview\",\n        dynamicForm: \"dynamicForm\",\n        disabled: \"disabled\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 1,\n      consts: [[1, \"w-full\"], [\"class\", \"template-content\", 4, \"ngIf\"], [1, \"template-content\"], [\"class\", \"section-controls\", 4, \"ngIf\"], [\"class\", \"text-300 text-xl\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"section-controls\"], [1, \"form-section-btns\"], [\"icon\", \"pi pi-pencil\", \"pTooltip\", \"Edit\", \"styleClass\", \"px-0\", 3, \"onClick\", \"disabled\", \"text\"], [\"icon\", \"pi pi-trash\", \"pTooltip\", \"Remove\", \"severity\", \"danger\", \"styleClass\", \"px-0\", 3, \"onClick\", \"disabled\", \"text\"], [1, \"text-300\", \"text-xl\"], [1, \"section-content\", \"ql-editor\", 3, \"innerHTML\"]],\n      template: function DynamicFormSectionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, DynamicFormSectionComponent_div_1_Template, 4, 3, \"div\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.layoutSection == null ? null : ctx.layoutSection.visible);\n        }\n      },\n      dependencies: [NgIf, ButtonModule, i3.Button, TooltipModule, i4.Tooltip],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["DynamicFormLayoutAreas", "TooltipModule", "ButtonModule", "NgIf", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DynamicFormSectionComponent_div_1_div_1_Template_p_button_onClick_4_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "editSection", "DynamicFormSectionComponent_div_1_div_1_Template_p_button_onClick_5_listener", "deleteSection", "ɵɵadvance", "ɵɵtextInterpolate", "areaName", "ɵɵproperty", "disabled", "ɵɵtextInterpolate1", "ɵɵelement", "domSani<PERSON>zer", "bypassSecurityTrustHtml", "layoutSection", "content", "ɵɵsanitizeHtml", "ɵɵtemplate", "DynamicFormSectionComponent_div_1_div_1_Template", "DynamicFormSectionComponent_div_1_span_2_Template", "DynamicFormSectionComponent_div_1_div_3_Template", "isPreview", "DynamicFormSectionComponent", "constructor", "dynamicFormsService", "dynamicForm", "layout", "layoutArea", "undefined", "area", "Footer", "Instruction", "HeaderLeft", "HeaderRight", "showLayoutOptions", "next", "visible", "ɵɵdirectiveInject", "i1", "DynamicFormsService", "i2", "Dom<PERSON><PERSON><PERSON>zer", "selectors", "inputs", "class", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "DynamicFormSectionComponent_Template", "rf", "ctx", "DynamicFormSectionComponent_div_1_Template", "i3", "<PERSON><PERSON>", "i4", "<PERSON><PERSON><PERSON>", "encapsulation"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\dynamic-forms\\dynamic-form-section\\dynamic-form-section.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\dynamic-forms\\dynamic-form-section\\dynamic-form-section.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\r\nimport { DynamicFormSection } from '../models/dynamic-form-section';\r\nimport { DynamicForm } from '../models/dynamic-form';\r\nimport { DynamicFormsService } from '../services/dynamic-forms.service';\r\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\r\nimport { DynamicFormLayoutAreas } from '../enums/dynamic-form';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { NgIf } from '@angular/common';\r\n\r\n@Component({\r\n    selector: 'app-dynamic-form-section',\r\n    templateUrl: './dynamic-form-section.component.html',\r\n    standalone: true,\r\n    imports: [NgIf, ButtonModule, TooltipModule]\r\n})\r\nexport class DynamicFormSectionComponent {\r\n  @Input() layoutArea: DynamicFormLayoutAreas;\r\n  @Input() class: string;\r\n  @Input() isPreview: boolean;\r\n  @Input() dynamicForm: DynamicForm;\r\n  @Input() disabled = false;\r\n\r\n  safeHtmlContent: SafeHtml;\r\n\r\n  //Note: we use the sanitizer in the html\r\n  constructor(\r\n    private dynamicFormsService: DynamicFormsService,\r\n    protected domSanitizer: DomSanitizer) { }\r\n\r\n  get layoutSection(): DynamicFormSection {\r\n    if (this.dynamicForm?.layout && this.layoutArea) {\r\n      return this.dynamicForm.layout[this.layoutArea];\r\n    }\r\n\r\n    return undefined;\r\n  }\r\n\r\n  get areaName(): string {\r\n    switch (this.layoutSection?.area) {\r\n      case DynamicFormLayoutAreas.Footer:\r\n        return 'Footer';\r\n      case DynamicFormLayoutAreas.Instruction:\r\n        return 'Instruction';\r\n      case DynamicFormLayoutAreas.HeaderLeft:\r\n        return 'Left Header';\r\n      case DynamicFormLayoutAreas.HeaderRight:\r\n        return 'Right Header';\r\n      default:\r\n        return undefined;\r\n    }\r\n  }\r\n\r\n  editSection() {\r\n    if (this.disabled) {\r\n      return;\r\n    }\r\n    if (this.layoutSection)\r\n      this.dynamicFormsService.showLayoutOptions.next(this.layoutSection)\r\n  }\r\n\r\n  deleteSection() {\r\n    if (this.disabled) {\r\n      return;\r\n    }\r\n    if (this.layoutSection)\r\n      this.layoutSection.visible = false\r\n  }\r\n}\r\n", "<div class=\"w-full\">\r\n  <div *ngIf=\"layoutSection?.visible\" class=\"template-content\">\r\n    <div *ngIf=\"isPreview\" class=\"section-controls\">\r\n      <span>{{areaName}}</span>\r\n      <div class=\"form-section-btns\">\r\n        <p-button icon=\"pi pi-pencil\" [disabled]=\"disabled\" pTooltip=\"Edit\" [text]=\"true\" styleClass=\"px-0\" (onClick)=\"editSection()\"></p-button>\r\n        <p-button icon=\"pi pi-trash\" [disabled]=\"disabled\" pTooltip=\"Remove\" [text]=\"true\" severity=\"danger\" styleClass=\"px-0\" (onClick)=\"deleteSection()\"></p-button>\r\n      </div>\r\n    </div>\r\n    <span *ngIf=\"!layoutSection?.content\" class=\"text-300 text-xl\">{{areaName}} Content</span>\r\n    <div *ngIf=\"layoutSection?.content\">\r\n      <div class=\"section-content ql-editor\" [innerHTML]=\"this.domSanitizer.bypassSecurityTrustHtml(this.layoutSection?.content)\"></div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": "AAKA,SAASA,sBAAsB,QAAQ,uBAAuB;AAC9D,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,IAAI,QAAQ,iBAAiB;;;;;;;;;ICLhCC,EADF,CAAAC,cAAA,aAAgD,WACxC;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEvBH,EADF,CAAAC,cAAA,aAA+B,kBACiG;IAA1BD,EAAA,CAAAI,UAAA,qBAAAC,6EAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAWF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAACX,EAAA,CAAAG,YAAA,EAAW;IACzIH,EAAA,CAAAC,cAAA,kBAAmJ;IAA5BD,EAAA,CAAAI,UAAA,qBAAAQ,6EAAA;MAAAZ,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAWF,MAAA,CAAAK,aAAA,EAAe;IAAA,EAAC;IAEtJb,EAFuJ,CAAAG,YAAA,EAAW,EAC1J,EACF;;;;IALEH,EAAA,CAAAc,SAAA,GAAY;IAAZd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,QAAA,CAAY;IAEchB,EAAA,CAAAc,SAAA,GAAqB;IAAiBd,EAAtC,CAAAiB,UAAA,aAAAT,MAAA,CAAAU,QAAA,CAAqB,cAA8B;IACpDlB,EAAA,CAAAc,SAAA,EAAqB;IAAmBd,EAAxC,CAAAiB,UAAA,aAAAT,MAAA,CAAAU,QAAA,CAAqB,cAAgC;;;;;IAGtFlB,EAAA,CAAAC,cAAA,eAA+D;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA3BH,EAAA,CAAAc,SAAA,EAAoB;IAApBd,EAAA,CAAAmB,kBAAA,KAAAX,MAAA,CAAAQ,QAAA,aAAoB;;;;;IACnFhB,EAAA,CAAAC,cAAA,UAAoC;IAClCD,EAAA,CAAAoB,SAAA,cAAkI;IACpIpB,EAAA,CAAAG,YAAA,EAAM;;;;IADmCH,EAAA,CAAAc,SAAA,EAAoF;IAApFd,EAAA,CAAAiB,UAAA,cAAAT,MAAA,CAAAa,YAAA,CAAAC,uBAAA,CAAAd,MAAA,CAAAe,aAAA,kBAAAf,MAAA,CAAAe,aAAA,CAAAC,OAAA,GAAAxB,EAAA,CAAAyB,cAAA,CAAoF;;;;;IAV/HzB,EAAA,CAAAC,cAAA,aAA6D;IAS3DD,EARA,CAAA0B,UAAA,IAAAC,gDAAA,iBAAgD,IAAAC,iDAAA,kBAOe,IAAAC,gDAAA,iBAC3B;IAGtC7B,EAAA,CAAAG,YAAA,EAAM;;;;IAXEH,EAAA,CAAAc,SAAA,EAAe;IAAfd,EAAA,CAAAiB,UAAA,SAAAT,MAAA,CAAAsB,SAAA,CAAe;IAOd9B,EAAA,CAAAc,SAAA,EAA6B;IAA7Bd,EAAA,CAAAiB,UAAA,WAAAT,MAAA,CAAAe,aAAA,kBAAAf,MAAA,CAAAe,aAAA,CAAAC,OAAA,EAA6B;IAC9BxB,EAAA,CAAAc,SAAA,EAA4B;IAA5Bd,EAAA,CAAAiB,UAAA,SAAAT,MAAA,CAAAe,aAAA,kBAAAf,MAAA,CAAAe,aAAA,CAAAC,OAAA,CAA4B;;;ADMtC,OAAM,MAAOO,2BAA2B;EAStC;EACAC,YACUC,mBAAwC,EACtCZ,YAA0B;IAD5B,KAAAY,mBAAmB,GAAnBA,mBAAmB;IACjB,KAAAZ,YAAY,GAAZA,YAAY;IAPf,KAAAH,QAAQ,GAAG,KAAK;EAOiB;EAE1C,IAAIK,aAAaA,CAAA;IACf,IAAI,IAAI,CAACW,WAAW,EAAEC,MAAM,IAAI,IAAI,CAACC,UAAU,EAAE;MAC/C,OAAO,IAAI,CAACF,WAAW,CAACC,MAAM,CAAC,IAAI,CAACC,UAAU,CAAC;IACjD;IAEA,OAAOC,SAAS;EAClB;EAEA,IAAIrB,QAAQA,CAAA;IACV,QAAQ,IAAI,CAACO,aAAa,EAAEe,IAAI;MAC9B,KAAK1C,sBAAsB,CAAC2C,MAAM;QAChC,OAAO,QAAQ;MACjB,KAAK3C,sBAAsB,CAAC4C,WAAW;QACrC,OAAO,aAAa;MACtB,KAAK5C,sBAAsB,CAAC6C,UAAU;QACpC,OAAO,aAAa;MACtB,KAAK7C,sBAAsB,CAAC8C,WAAW;QACrC,OAAO,cAAc;MACvB;QACE,OAAOL,SAAS;IACpB;EACF;EAEA1B,WAAWA,CAAA;IACT,IAAI,IAAI,CAACO,QAAQ,EAAE;MACjB;IACF;IACA,IAAI,IAAI,CAACK,aAAa,EACpB,IAAI,CAACU,mBAAmB,CAACU,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAACrB,aAAa,CAAC;EACvE;EAEAV,aAAaA,CAAA;IACX,IAAI,IAAI,CAACK,QAAQ,EAAE;MACjB;IACF;IACA,IAAI,IAAI,CAACK,aAAa,EACpB,IAAI,CAACA,aAAa,CAACsB,OAAO,GAAG,KAAK;EACtC;;;uBAnDWd,2BAA2B,EAAA/B,EAAA,CAAA8C,iBAAA,CAAAC,EAAA,CAAAC,mBAAA,GAAAhD,EAAA,CAAA8C,iBAAA,CAAAG,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA3BnB,2BAA2B;MAAAoB,SAAA;MAAAC,MAAA;QAAAhB,UAAA;QAAAiB,KAAA;QAAAvB,SAAA;QAAAI,WAAA;QAAAhB,QAAA;MAAA;MAAAoC,UAAA;MAAAC,QAAA,GAAAvD,EAAA,CAAAwD,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBxC9D,EAAA,CAAAC,cAAA,aAAoB;UAClBD,EAAA,CAAA0B,UAAA,IAAAsC,0CAAA,iBAA6D;UAa/DhE,EAAA,CAAAG,YAAA,EAAM;;;UAbEH,EAAA,CAAAc,SAAA,EAA4B;UAA5Bd,EAAA,CAAAiB,UAAA,SAAA8C,GAAA,CAAAxC,aAAA,kBAAAwC,GAAA,CAAAxC,aAAA,CAAAsB,OAAA,CAA4B;;;qBDatB9C,IAAI,EAAED,YAAY,EAAAmE,EAAA,CAAAC,MAAA,EAAErE,aAAa,EAAAsE,EAAA,CAAAC,OAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}