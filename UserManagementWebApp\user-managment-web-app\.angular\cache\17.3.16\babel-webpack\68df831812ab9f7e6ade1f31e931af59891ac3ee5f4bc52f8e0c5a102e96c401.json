{"ast": null, "code": "/* AUTO-GENERATED. DO NOT MODIFY. */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n\n\n CSS Beautifier\n---------------\n\n    Written by Harutyun Amirjanyan, (<EMAIL>)\n\n    Based on code initially developed by: Einar Lielmanis, <<EMAIL>>\n        https://beautifier.io/\n\n    Usage:\n        css_beautify(source_text);\n        css_beautify(source_text, options);\n\n    The options are (default in brackets):\n        indent_size (4)                         — indentation size,\n        indent_char (space)                     — character to indent with,\n        selector_separator_newline (true)       - separate selectors with newline or\n                                                  not (e.g. \"a,\\nbr\" or \"a, br\")\n        end_with_newline (false)                - end with a newline\n        newline_between_rules (true)            - add a new line after every css rule\n        space_around_selector_separator (false) - ensure space around selector separators:\n                                                  '>', '+', '~' (e.g. \"a>b\" -> \"a > b\")\n    e.g\n\n    css_beautify(css_source_text, {\n      'indent_size': 1,\n      'indent_char': '\\t',\n      'selector_separator': ' ',\n      'end_with_newline': false,\n      'newline_between_rules': true,\n      'space_around_selector_separator': true\n    });\n*/\n\n// http://www.w3.org/TR/CSS21/syndata.html#tokenization\n// http://www.w3.org/TR/css3-syntax/\n\n(function () {\n  /* GENERATED_BUILD_OUTPUT */\n  var legacy_beautify_css;\n  /******/\n  (function () {\n    // webpackBootstrap\n    /******/\n    \"use strict\";\n\n    /******/\n    var __webpack_modules__ = [\n      /* 0 */\n      /* 1 */\n\n      /* 3 */\n      /* 4 */\n      /* 5 */\n\n      /* 7 */\n\n      /* 9 */\n      /* 10 */\n      /* 11 */\n      /* 12 */\n\n      /* 14 */\n    ,, (/* 2 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      function OutputLine(parent) {\n        this.__parent = parent;\n        this.__character_count = 0;\n        // use indent_count as a marker for this.__lines that have preserved indentation\n        this.__indent_count = -1;\n        this.__alignment_count = 0;\n        this.__wrap_point_index = 0;\n        this.__wrap_point_character_count = 0;\n        this.__wrap_point_indent_count = -1;\n        this.__wrap_point_alignment_count = 0;\n        this.__items = [];\n      }\n      OutputLine.prototype.clone_empty = function () {\n        var line = new OutputLine(this.__parent);\n        line.set_indent(this.__indent_count, this.__alignment_count);\n        return line;\n      };\n      OutputLine.prototype.item = function (index) {\n        if (index < 0) {\n          return this.__items[this.__items.length + index];\n        } else {\n          return this.__items[index];\n        }\n      };\n      OutputLine.prototype.has_match = function (pattern) {\n        for (var lastCheckedOutput = this.__items.length - 1; lastCheckedOutput >= 0; lastCheckedOutput--) {\n          if (this.__items[lastCheckedOutput].match(pattern)) {\n            return true;\n          }\n        }\n        return false;\n      };\n      OutputLine.prototype.set_indent = function (indent, alignment) {\n        if (this.is_empty()) {\n          this.__indent_count = indent || 0;\n          this.__alignment_count = alignment || 0;\n          this.__character_count = this.__parent.get_indent_size(this.__indent_count, this.__alignment_count);\n        }\n      };\n      OutputLine.prototype._set_wrap_point = function () {\n        if (this.__parent.wrap_line_length) {\n          this.__wrap_point_index = this.__items.length;\n          this.__wrap_point_character_count = this.__character_count;\n          this.__wrap_point_indent_count = this.__parent.next_line.__indent_count;\n          this.__wrap_point_alignment_count = this.__parent.next_line.__alignment_count;\n        }\n      };\n      OutputLine.prototype._should_wrap = function () {\n        return this.__wrap_point_index && this.__character_count > this.__parent.wrap_line_length && this.__wrap_point_character_count > this.__parent.next_line.__character_count;\n      };\n      OutputLine.prototype._allow_wrap = function () {\n        if (this._should_wrap()) {\n          this.__parent.add_new_line();\n          var next = this.__parent.current_line;\n          next.set_indent(this.__wrap_point_indent_count, this.__wrap_point_alignment_count);\n          next.__items = this.__items.slice(this.__wrap_point_index);\n          this.__items = this.__items.slice(0, this.__wrap_point_index);\n          next.__character_count += this.__character_count - this.__wrap_point_character_count;\n          this.__character_count = this.__wrap_point_character_count;\n          if (next.__items[0] === \" \") {\n            next.__items.splice(0, 1);\n            next.__character_count -= 1;\n          }\n          return true;\n        }\n        return false;\n      };\n      OutputLine.prototype.is_empty = function () {\n        return this.__items.length === 0;\n      };\n      OutputLine.prototype.last = function () {\n        if (!this.is_empty()) {\n          return this.__items[this.__items.length - 1];\n        } else {\n          return null;\n        }\n      };\n      OutputLine.prototype.push = function (item) {\n        this.__items.push(item);\n        var last_newline_index = item.lastIndexOf('\\n');\n        if (last_newline_index !== -1) {\n          this.__character_count = item.length - last_newline_index;\n        } else {\n          this.__character_count += item.length;\n        }\n      };\n      OutputLine.prototype.pop = function () {\n        var item = null;\n        if (!this.is_empty()) {\n          item = this.__items.pop();\n          this.__character_count -= item.length;\n        }\n        return item;\n      };\n      OutputLine.prototype._remove_indent = function () {\n        if (this.__indent_count > 0) {\n          this.__indent_count -= 1;\n          this.__character_count -= this.__parent.indent_size;\n        }\n      };\n      OutputLine.prototype._remove_wrap_indent = function () {\n        if (this.__wrap_point_indent_count > 0) {\n          this.__wrap_point_indent_count -= 1;\n        }\n      };\n      OutputLine.prototype.trim = function () {\n        while (this.last() === ' ') {\n          this.__items.pop();\n          this.__character_count -= 1;\n        }\n      };\n      OutputLine.prototype.toString = function () {\n        var result = '';\n        if (this.is_empty()) {\n          if (this.__parent.indent_empty_lines) {\n            result = this.__parent.get_indent_string(this.__indent_count);\n          }\n        } else {\n          result = this.__parent.get_indent_string(this.__indent_count, this.__alignment_count);\n          result += this.__items.join('');\n        }\n        return result;\n      };\n      function IndentStringCache(options, baseIndentString) {\n        this.__cache = [''];\n        this.__indent_size = options.indent_size;\n        this.__indent_string = options.indent_char;\n        if (!options.indent_with_tabs) {\n          this.__indent_string = new Array(options.indent_size + 1).join(options.indent_char);\n        }\n\n        // Set to null to continue support for auto detection of base indent\n        baseIndentString = baseIndentString || '';\n        if (options.indent_level > 0) {\n          baseIndentString = new Array(options.indent_level + 1).join(this.__indent_string);\n        }\n        this.__base_string = baseIndentString;\n        this.__base_string_length = baseIndentString.length;\n      }\n      IndentStringCache.prototype.get_indent_size = function (indent, column) {\n        var result = this.__base_string_length;\n        column = column || 0;\n        if (indent < 0) {\n          result = 0;\n        }\n        result += indent * this.__indent_size;\n        result += column;\n        return result;\n      };\n      IndentStringCache.prototype.get_indent_string = function (indent_level, column) {\n        var result = this.__base_string;\n        column = column || 0;\n        if (indent_level < 0) {\n          indent_level = 0;\n          result = '';\n        }\n        column += indent_level * this.__indent_size;\n        this.__ensure_cache(column);\n        result += this.__cache[column];\n        return result;\n      };\n      IndentStringCache.prototype.__ensure_cache = function (column) {\n        while (column >= this.__cache.length) {\n          this.__add_column();\n        }\n      };\n      IndentStringCache.prototype.__add_column = function () {\n        var column = this.__cache.length;\n        var indent = 0;\n        var result = '';\n        if (this.__indent_size && column >= this.__indent_size) {\n          indent = Math.floor(column / this.__indent_size);\n          column -= indent * this.__indent_size;\n          result = new Array(indent + 1).join(this.__indent_string);\n        }\n        if (column) {\n          result += new Array(column + 1).join(' ');\n        }\n        this.__cache.push(result);\n      };\n      function Output(options, baseIndentString) {\n        this.__indent_cache = new IndentStringCache(options, baseIndentString);\n        this.raw = false;\n        this._end_with_newline = options.end_with_newline;\n        this.indent_size = options.indent_size;\n        this.wrap_line_length = options.wrap_line_length;\n        this.indent_empty_lines = options.indent_empty_lines;\n        this.__lines = [];\n        this.previous_line = null;\n        this.current_line = null;\n        this.next_line = new OutputLine(this);\n        this.space_before_token = false;\n        this.non_breaking_space = false;\n        this.previous_token_wrapped = false;\n        // initialize\n        this.__add_outputline();\n      }\n      Output.prototype.__add_outputline = function () {\n        this.previous_line = this.current_line;\n        this.current_line = this.next_line.clone_empty();\n        this.__lines.push(this.current_line);\n      };\n      Output.prototype.get_line_number = function () {\n        return this.__lines.length;\n      };\n      Output.prototype.get_indent_string = function (indent, column) {\n        return this.__indent_cache.get_indent_string(indent, column);\n      };\n      Output.prototype.get_indent_size = function (indent, column) {\n        return this.__indent_cache.get_indent_size(indent, column);\n      };\n      Output.prototype.is_empty = function () {\n        return !this.previous_line && this.current_line.is_empty();\n      };\n      Output.prototype.add_new_line = function (force_newline) {\n        // never newline at the start of file\n        // otherwise, newline only if we didn't just add one or we're forced\n        if (this.is_empty() || !force_newline && this.just_added_newline()) {\n          return false;\n        }\n\n        // if raw output is enabled, don't print additional newlines,\n        // but still return True as though you had\n        if (!this.raw) {\n          this.__add_outputline();\n        }\n        return true;\n      };\n      Output.prototype.get_code = function (eol) {\n        this.trim(true);\n\n        // handle some edge cases where the last tokens\n        // has text that ends with newline(s)\n        var last_item = this.current_line.pop();\n        if (last_item) {\n          if (last_item[last_item.length - 1] === '\\n') {\n            last_item = last_item.replace(/\\n+$/g, '');\n          }\n          this.current_line.push(last_item);\n        }\n        if (this._end_with_newline) {\n          this.__add_outputline();\n        }\n        var sweet_code = this.__lines.join('\\n');\n        if (eol !== '\\n') {\n          sweet_code = sweet_code.replace(/[\\n]/g, eol);\n        }\n        return sweet_code;\n      };\n      Output.prototype.set_wrap_point = function () {\n        this.current_line._set_wrap_point();\n      };\n      Output.prototype.set_indent = function (indent, alignment) {\n        indent = indent || 0;\n        alignment = alignment || 0;\n\n        // Next line stores alignment values\n        this.next_line.set_indent(indent, alignment);\n\n        // Never indent your first output indent at the start of the file\n        if (this.__lines.length > 1) {\n          this.current_line.set_indent(indent, alignment);\n          return true;\n        }\n        this.current_line.set_indent();\n        return false;\n      };\n      Output.prototype.add_raw_token = function (token) {\n        for (var x = 0; x < token.newlines; x++) {\n          this.__add_outputline();\n        }\n        this.current_line.set_indent(-1);\n        this.current_line.push(token.whitespace_before);\n        this.current_line.push(token.text);\n        this.space_before_token = false;\n        this.non_breaking_space = false;\n        this.previous_token_wrapped = false;\n      };\n      Output.prototype.add_token = function (printable_token) {\n        this.__add_space_before_token();\n        this.current_line.push(printable_token);\n        this.space_before_token = false;\n        this.non_breaking_space = false;\n        this.previous_token_wrapped = this.current_line._allow_wrap();\n      };\n      Output.prototype.__add_space_before_token = function () {\n        if (this.space_before_token && !this.just_added_newline()) {\n          if (!this.non_breaking_space) {\n            this.set_wrap_point();\n          }\n          this.current_line.push(' ');\n        }\n      };\n      Output.prototype.remove_indent = function (index) {\n        var output_length = this.__lines.length;\n        while (index < output_length) {\n          this.__lines[index]._remove_indent();\n          index++;\n        }\n        this.current_line._remove_wrap_indent();\n      };\n      Output.prototype.trim = function (eat_newlines) {\n        eat_newlines = eat_newlines === undefined ? false : eat_newlines;\n        this.current_line.trim();\n        while (eat_newlines && this.__lines.length > 1 && this.current_line.is_empty()) {\n          this.__lines.pop();\n          this.current_line = this.__lines[this.__lines.length - 1];\n          this.current_line.trim();\n        }\n        this.previous_line = this.__lines.length > 1 ? this.__lines[this.__lines.length - 2] : null;\n      };\n      Output.prototype.just_added_newline = function () {\n        return this.current_line.is_empty();\n      };\n      Output.prototype.just_added_blankline = function () {\n        return this.is_empty() || this.current_line.is_empty() && this.previous_line.is_empty();\n      };\n      Output.prototype.ensure_empty_line_above = function (starts_with, ends_with) {\n        var index = this.__lines.length - 2;\n        while (index >= 0) {\n          var potentialEmptyLine = this.__lines[index];\n          if (potentialEmptyLine.is_empty()) {\n            break;\n          } else if (potentialEmptyLine.item(0).indexOf(starts_with) !== 0 && potentialEmptyLine.item(-1) !== ends_with) {\n            this.__lines.splice(index + 1, 0, new OutputLine(this));\n            this.previous_line = this.__lines[this.__lines.length - 2];\n            break;\n          }\n          index--;\n        }\n      };\n      module.exports.Output = Output;\n\n      /***/\n    }),,,, (/* 6 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      function Options(options, merge_child_field) {\n        this.raw_options = _mergeOpts(options, merge_child_field);\n\n        // Support passing the source text back with no change\n        this.disabled = this._get_boolean('disabled');\n        this.eol = this._get_characters('eol', 'auto');\n        this.end_with_newline = this._get_boolean('end_with_newline');\n        this.indent_size = this._get_number('indent_size', 4);\n        this.indent_char = this._get_characters('indent_char', ' ');\n        this.indent_level = this._get_number('indent_level');\n        this.preserve_newlines = this._get_boolean('preserve_newlines', true);\n        this.max_preserve_newlines = this._get_number('max_preserve_newlines', 32786);\n        if (!this.preserve_newlines) {\n          this.max_preserve_newlines = 0;\n        }\n        this.indent_with_tabs = this._get_boolean('indent_with_tabs', this.indent_char === '\\t');\n        if (this.indent_with_tabs) {\n          this.indent_char = '\\t';\n\n          // indent_size behavior changed after 1.8.6\n          // It used to be that indent_size would be\n          // set to 1 for indent_with_tabs. That is no longer needed and\n          // actually doesn't make sense - why not use spaces? Further,\n          // that might produce unexpected behavior - tabs being used\n          // for single-column alignment. So, when indent_with_tabs is true\n          // and indent_size is 1, reset indent_size to 4.\n          if (this.indent_size === 1) {\n            this.indent_size = 4;\n          }\n        }\n\n        // Backwards compat with 1.3.x\n        this.wrap_line_length = this._get_number('wrap_line_length', this._get_number('max_char'));\n        this.indent_empty_lines = this._get_boolean('indent_empty_lines');\n\n        // valid templating languages ['django', 'erb', 'handlebars', 'php', 'smarty', 'angular']\n        // For now, 'auto' = all off for javascript, all except angular on for html (and inline javascript/css).\n        // other values ignored\n        this.templating = this._get_selection_list('templating', ['auto', 'none', 'angular', 'django', 'erb', 'handlebars', 'php', 'smarty'], ['auto']);\n      }\n      Options.prototype._get_array = function (name, default_value) {\n        var option_value = this.raw_options[name];\n        var result = default_value || [];\n        if (typeof option_value === 'object') {\n          if (option_value !== null && typeof option_value.concat === 'function') {\n            result = option_value.concat();\n          }\n        } else if (typeof option_value === 'string') {\n          result = option_value.split(/[^a-zA-Z0-9_\\/\\-]+/);\n        }\n        return result;\n      };\n      Options.prototype._get_boolean = function (name, default_value) {\n        var option_value = this.raw_options[name];\n        var result = option_value === undefined ? !!default_value : !!option_value;\n        return result;\n      };\n      Options.prototype._get_characters = function (name, default_value) {\n        var option_value = this.raw_options[name];\n        var result = default_value || '';\n        if (typeof option_value === 'string') {\n          result = option_value.replace(/\\\\r/, '\\r').replace(/\\\\n/, '\\n').replace(/\\\\t/, '\\t');\n        }\n        return result;\n      };\n      Options.prototype._get_number = function (name, default_value) {\n        var option_value = this.raw_options[name];\n        default_value = parseInt(default_value, 10);\n        if (isNaN(default_value)) {\n          default_value = 0;\n        }\n        var result = parseInt(option_value, 10);\n        if (isNaN(result)) {\n          result = default_value;\n        }\n        return result;\n      };\n      Options.prototype._get_selection = function (name, selection_list, default_value) {\n        var result = this._get_selection_list(name, selection_list, default_value);\n        if (result.length !== 1) {\n          throw new Error(\"Invalid Option Value: The option '\" + name + \"' can only be one of the following values:\\n\" + selection_list + \"\\nYou passed in: '\" + this.raw_options[name] + \"'\");\n        }\n        return result[0];\n      };\n      Options.prototype._get_selection_list = function (name, selection_list, default_value) {\n        if (!selection_list || selection_list.length === 0) {\n          throw new Error(\"Selection list cannot be empty.\");\n        }\n        default_value = default_value || [selection_list[0]];\n        if (!this._is_valid_selection(default_value, selection_list)) {\n          throw new Error(\"Invalid Default Value!\");\n        }\n        var result = this._get_array(name, default_value);\n        if (!this._is_valid_selection(result, selection_list)) {\n          throw new Error(\"Invalid Option Value: The option '\" + name + \"' can contain only the following values:\\n\" + selection_list + \"\\nYou passed in: '\" + this.raw_options[name] + \"'\");\n        }\n        return result;\n      };\n      Options.prototype._is_valid_selection = function (result, selection_list) {\n        return result.length && selection_list.length && !result.some(function (item) {\n          return selection_list.indexOf(item) === -1;\n        });\n      };\n\n      // merges child options up with the parent options object\n      // Example: obj = {a: 1, b: {a: 2}}\n      //          mergeOpts(obj, 'b')\n      //\n      //          Returns: {a: 2}\n      function _mergeOpts(allOptions, childFieldName) {\n        var finalOpts = {};\n        allOptions = _normalizeOpts(allOptions);\n        var name;\n        for (name in allOptions) {\n          if (name !== childFieldName) {\n            finalOpts[name] = allOptions[name];\n          }\n        }\n\n        //merge in the per type settings for the childFieldName\n        if (childFieldName && allOptions[childFieldName]) {\n          for (name in allOptions[childFieldName]) {\n            finalOpts[name] = allOptions[childFieldName][name];\n          }\n        }\n        return finalOpts;\n      }\n      function _normalizeOpts(options) {\n        var convertedOpts = {};\n        var key;\n        for (key in options) {\n          var newKey = key.replace(/-/g, \"_\");\n          convertedOpts[newKey] = options[key];\n        }\n        return convertedOpts;\n      }\n      module.exports.Options = Options;\n      module.exports.normalizeOpts = _normalizeOpts;\n      module.exports.mergeOpts = _mergeOpts;\n\n      /***/\n    }),, (/* 8 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var regexp_has_sticky = RegExp.prototype.hasOwnProperty('sticky');\n      function InputScanner(input_string) {\n        this.__input = input_string || '';\n        this.__input_length = this.__input.length;\n        this.__position = 0;\n      }\n      InputScanner.prototype.restart = function () {\n        this.__position = 0;\n      };\n      InputScanner.prototype.back = function () {\n        if (this.__position > 0) {\n          this.__position -= 1;\n        }\n      };\n      InputScanner.prototype.hasNext = function () {\n        return this.__position < this.__input_length;\n      };\n      InputScanner.prototype.next = function () {\n        var val = null;\n        if (this.hasNext()) {\n          val = this.__input.charAt(this.__position);\n          this.__position += 1;\n        }\n        return val;\n      };\n      InputScanner.prototype.peek = function (index) {\n        var val = null;\n        index = index || 0;\n        index += this.__position;\n        if (index >= 0 && index < this.__input_length) {\n          val = this.__input.charAt(index);\n        }\n        return val;\n      };\n\n      // This is a JavaScript only helper function (not in python)\n      // Javascript doesn't have a match method\n      // and not all implementation support \"sticky\" flag.\n      // If they do not support sticky then both this.match() and this.test() method\n      // must get the match and check the index of the match.\n      // If sticky is supported and set, this method will use it.\n      // Otherwise it will check that global is set, and fall back to the slower method.\n      InputScanner.prototype.__match = function (pattern, index) {\n        pattern.lastIndex = index;\n        var pattern_match = pattern.exec(this.__input);\n        if (pattern_match && !(regexp_has_sticky && pattern.sticky)) {\n          if (pattern_match.index !== index) {\n            pattern_match = null;\n          }\n        }\n        return pattern_match;\n      };\n      InputScanner.prototype.test = function (pattern, index) {\n        index = index || 0;\n        index += this.__position;\n        if (index >= 0 && index < this.__input_length) {\n          return !!this.__match(pattern, index);\n        } else {\n          return false;\n        }\n      };\n      InputScanner.prototype.testChar = function (pattern, index) {\n        // test one character regex match\n        var val = this.peek(index);\n        pattern.lastIndex = 0;\n        return val !== null && pattern.test(val);\n      };\n      InputScanner.prototype.match = function (pattern) {\n        var pattern_match = this.__match(pattern, this.__position);\n        if (pattern_match) {\n          this.__position += pattern_match[0].length;\n        } else {\n          pattern_match = null;\n        }\n        return pattern_match;\n      };\n      InputScanner.prototype.read = function (starting_pattern, until_pattern, until_after) {\n        var val = '';\n        var match;\n        if (starting_pattern) {\n          match = this.match(starting_pattern);\n          if (match) {\n            val += match[0];\n          }\n        }\n        if (until_pattern && (match || !starting_pattern)) {\n          val += this.readUntil(until_pattern, until_after);\n        }\n        return val;\n      };\n      InputScanner.prototype.readUntil = function (pattern, until_after) {\n        var val = '';\n        var match_index = this.__position;\n        pattern.lastIndex = this.__position;\n        var pattern_match = pattern.exec(this.__input);\n        if (pattern_match) {\n          match_index = pattern_match.index;\n          if (until_after) {\n            match_index += pattern_match[0].length;\n          }\n        } else {\n          match_index = this.__input_length;\n        }\n        val = this.__input.substring(this.__position, match_index);\n        this.__position = match_index;\n        return val;\n      };\n      InputScanner.prototype.readUntilAfter = function (pattern) {\n        return this.readUntil(pattern, true);\n      };\n      InputScanner.prototype.get_regexp = function (pattern, match_from) {\n        var result = null;\n        var flags = 'g';\n        if (match_from && regexp_has_sticky) {\n          flags = 'y';\n        }\n        // strings are converted to regexp\n        if (typeof pattern === \"string\" && pattern !== '') {\n          // result = new RegExp(pattern.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'), flags);\n          result = new RegExp(pattern, flags);\n        } else if (pattern) {\n          result = new RegExp(pattern.source, flags);\n        }\n        return result;\n      };\n      InputScanner.prototype.get_literal_regexp = function (literal_string) {\n        return RegExp(literal_string.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'));\n      };\n\n      /* css beautifier legacy helpers */\n      InputScanner.prototype.peekUntilAfter = function (pattern) {\n        var start = this.__position;\n        var val = this.readUntilAfter(pattern);\n        this.__position = start;\n        return val;\n      };\n      InputScanner.prototype.lookBack = function (testVal) {\n        var start = this.__position - 1;\n        return start >= testVal.length && this.__input.substring(start - testVal.length, start).toLowerCase() === testVal;\n      };\n      module.exports.InputScanner = InputScanner;\n\n      /***/\n    }),,,,, (/* 13 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      function Directives(start_block_pattern, end_block_pattern) {\n        start_block_pattern = typeof start_block_pattern === 'string' ? start_block_pattern : start_block_pattern.source;\n        end_block_pattern = typeof end_block_pattern === 'string' ? end_block_pattern : end_block_pattern.source;\n        this.__directives_block_pattern = new RegExp(start_block_pattern + / beautify( \\w+[:]\\w+)+ /.source + end_block_pattern, 'g');\n        this.__directive_pattern = / (\\w+)[:](\\w+)/g;\n        this.__directives_end_ignore_pattern = new RegExp(start_block_pattern + /\\sbeautify\\signore:end\\s/.source + end_block_pattern, 'g');\n      }\n      Directives.prototype.get_directives = function (text) {\n        if (!text.match(this.__directives_block_pattern)) {\n          return null;\n        }\n        var directives = {};\n        this.__directive_pattern.lastIndex = 0;\n        var directive_match = this.__directive_pattern.exec(text);\n        while (directive_match) {\n          directives[directive_match[1]] = directive_match[2];\n          directive_match = this.__directive_pattern.exec(text);\n        }\n        return directives;\n      };\n      Directives.prototype.readIgnored = function (input) {\n        return input.readUntilAfter(this.__directives_end_ignore_pattern);\n      };\n      module.exports.Directives = Directives;\n\n      /***/\n    }),, (/* 15 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var Beautifier = __webpack_require__(16).Beautifier,\n        Options = __webpack_require__(17).Options;\n      function css_beautify(source_text, options) {\n        var beautifier = new Beautifier(source_text, options);\n        return beautifier.beautify();\n      }\n      module.exports = css_beautify;\n      module.exports.defaultOptions = function () {\n        return new Options();\n      };\n\n      /***/\n    }), (/* 16 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var Options = __webpack_require__(17).Options;\n      var Output = __webpack_require__(2).Output;\n      var InputScanner = __webpack_require__(8).InputScanner;\n      var Directives = __webpack_require__(13).Directives;\n      var directives_core = new Directives(/\\/\\*/, /\\*\\//);\n      var lineBreak = /\\r\\n|[\\r\\n]/;\n      var allLineBreaks = /\\r\\n|[\\r\\n]/g;\n\n      // tokenizer\n      var whitespaceChar = /\\s/;\n      var whitespacePattern = /(?:\\s|\\n)+/g;\n      var block_comment_pattern = /\\/\\*(?:[\\s\\S]*?)((?:\\*\\/)|$)/g;\n      var comment_pattern = /\\/\\/(?:[^\\n\\r\\u2028\\u2029]*)/g;\n      function Beautifier(source_text, options) {\n        this._source_text = source_text || '';\n        // Allow the setting of language/file-type specific options\n        // with inheritance of overall settings\n        this._options = new Options(options);\n        this._ch = null;\n        this._input = null;\n\n        // https://developer.mozilla.org/en-US/docs/Web/CSS/At-rule\n        this.NESTED_AT_RULE = {\n          \"page\": true,\n          \"font-face\": true,\n          \"keyframes\": true,\n          // also in CONDITIONAL_GROUP_RULE below\n          \"media\": true,\n          \"supports\": true,\n          \"document\": true\n        };\n        this.CONDITIONAL_GROUP_RULE = {\n          \"media\": true,\n          \"supports\": true,\n          \"document\": true\n        };\n        this.NON_SEMICOLON_NEWLINE_PROPERTY = [\"grid-template-areas\", \"grid-template\"];\n      }\n      Beautifier.prototype.eatString = function (endChars) {\n        var result = '';\n        this._ch = this._input.next();\n        while (this._ch) {\n          result += this._ch;\n          if (this._ch === \"\\\\\") {\n            result += this._input.next();\n          } else if (endChars.indexOf(this._ch) !== -1 || this._ch === \"\\n\") {\n            break;\n          }\n          this._ch = this._input.next();\n        }\n        return result;\n      };\n\n      // Skips any white space in the source text from the current position.\n      // When allowAtLeastOneNewLine is true, will output new lines for each\n      // newline character found; if the user has preserve_newlines off, only\n      // the first newline will be output\n      Beautifier.prototype.eatWhitespace = function (allowAtLeastOneNewLine) {\n        var result = whitespaceChar.test(this._input.peek());\n        var newline_count = 0;\n        while (whitespaceChar.test(this._input.peek())) {\n          this._ch = this._input.next();\n          if (allowAtLeastOneNewLine && this._ch === '\\n') {\n            if (newline_count === 0 || newline_count < this._options.max_preserve_newlines) {\n              newline_count++;\n              this._output.add_new_line(true);\n            }\n          }\n        }\n        return result;\n      };\n\n      // Nested pseudo-class if we are insideRule\n      // and the next special character found opens\n      // a new block\n      Beautifier.prototype.foundNestedPseudoClass = function () {\n        var openParen = 0;\n        var i = 1;\n        var ch = this._input.peek(i);\n        while (ch) {\n          if (ch === \"{\") {\n            return true;\n          } else if (ch === '(') {\n            // pseudoclasses can contain ()\n            openParen += 1;\n          } else if (ch === ')') {\n            if (openParen === 0) {\n              return false;\n            }\n            openParen -= 1;\n          } else if (ch === \";\" || ch === \"}\") {\n            return false;\n          }\n          i++;\n          ch = this._input.peek(i);\n        }\n        return false;\n      };\n      Beautifier.prototype.print_string = function (output_string) {\n        this._output.set_indent(this._indentLevel);\n        this._output.non_breaking_space = true;\n        this._output.add_token(output_string);\n      };\n      Beautifier.prototype.preserveSingleSpace = function (isAfterSpace) {\n        if (isAfterSpace) {\n          this._output.space_before_token = true;\n        }\n      };\n      Beautifier.prototype.indent = function () {\n        this._indentLevel++;\n      };\n      Beautifier.prototype.outdent = function () {\n        if (this._indentLevel > 0) {\n          this._indentLevel--;\n        }\n      };\n\n      /*_____________________--------------------_____________________*/\n\n      Beautifier.prototype.beautify = function () {\n        if (this._options.disabled) {\n          return this._source_text;\n        }\n        var source_text = this._source_text;\n        var eol = this._options.eol;\n        if (eol === 'auto') {\n          eol = '\\n';\n          if (source_text && lineBreak.test(source_text || '')) {\n            eol = source_text.match(lineBreak)[0];\n          }\n        }\n\n        // HACK: newline parsing inconsistent. This brute force normalizes the this._input.\n        source_text = source_text.replace(allLineBreaks, '\\n');\n\n        // reset\n        var baseIndentString = source_text.match(/^[\\t ]*/)[0];\n        this._output = new Output(this._options, baseIndentString);\n        this._input = new InputScanner(source_text);\n        this._indentLevel = 0;\n        this._nestedLevel = 0;\n        this._ch = null;\n        var parenLevel = 0;\n        var insideRule = false;\n        // This is the value side of a property value pair (blue in the following ex)\n        // label { content: blue }\n        var insidePropertyValue = false;\n        var enteringConditionalGroup = false;\n        var insideNonNestedAtRule = false;\n        var insideScssMap = false;\n        var topCharacter = this._ch;\n        var insideNonSemiColonValues = false;\n        var whitespace;\n        var isAfterSpace;\n        var previous_ch;\n        while (true) {\n          whitespace = this._input.read(whitespacePattern);\n          isAfterSpace = whitespace !== '';\n          previous_ch = topCharacter;\n          this._ch = this._input.next();\n          if (this._ch === '\\\\' && this._input.hasNext()) {\n            this._ch += this._input.next();\n          }\n          topCharacter = this._ch;\n          if (!this._ch) {\n            break;\n          } else if (this._ch === '/' && this._input.peek() === '*') {\n            // /* css comment */\n            // Always start block comments on a new line.\n            // This handles scenarios where a block comment immediately\n            // follows a property definition on the same line or where\n            // minified code is being beautified.\n            this._output.add_new_line();\n            this._input.back();\n            var comment = this._input.read(block_comment_pattern);\n\n            // Handle ignore directive\n            var directives = directives_core.get_directives(comment);\n            if (directives && directives.ignore === 'start') {\n              comment += directives_core.readIgnored(this._input);\n            }\n            this.print_string(comment);\n\n            // Ensures any new lines following the comment are preserved\n            this.eatWhitespace(true);\n\n            // Block comments are followed by a new line so they don't\n            // share a line with other properties\n            this._output.add_new_line();\n          } else if (this._ch === '/' && this._input.peek() === '/') {\n            // // single line comment\n            // Preserves the space before a comment\n            // on the same line as a rule\n            this._output.space_before_token = true;\n            this._input.back();\n            this.print_string(this._input.read(comment_pattern));\n\n            // Ensures any new lines following the comment are preserved\n            this.eatWhitespace(true);\n          } else if (this._ch === '$') {\n            this.preserveSingleSpace(isAfterSpace);\n            this.print_string(this._ch);\n\n            // strip trailing space, if present, for hash property checks\n            var variable = this._input.peekUntilAfter(/[: ,;{}()[\\]\\/='\"]/g);\n            if (variable.match(/[ :]$/)) {\n              // we have a variable or pseudo-class, add it and insert one space before continuing\n              variable = this.eatString(\": \").replace(/\\s+$/, '');\n              this.print_string(variable);\n              this._output.space_before_token = true;\n            }\n\n            // might be sass variable\n            if (parenLevel === 0 && variable.indexOf(':') !== -1) {\n              insidePropertyValue = true;\n              this.indent();\n            }\n          } else if (this._ch === '@') {\n            this.preserveSingleSpace(isAfterSpace);\n\n            // deal with less property mixins @{...}\n            if (this._input.peek() === '{') {\n              this.print_string(this._ch + this.eatString('}'));\n            } else {\n              this.print_string(this._ch);\n\n              // strip trailing space, if present, for hash property checks\n              var variableOrRule = this._input.peekUntilAfter(/[: ,;{}()[\\]\\/='\"]/g);\n              if (variableOrRule.match(/[ :]$/)) {\n                // we have a variable or pseudo-class, add it and insert one space before continuing\n                variableOrRule = this.eatString(\": \").replace(/\\s+$/, '');\n                this.print_string(variableOrRule);\n                this._output.space_before_token = true;\n              }\n\n              // might be less variable\n              if (parenLevel === 0 && variableOrRule.indexOf(':') !== -1) {\n                insidePropertyValue = true;\n                this.indent();\n\n                // might be a nesting at-rule\n              } else if (variableOrRule in this.NESTED_AT_RULE) {\n                this._nestedLevel += 1;\n                if (variableOrRule in this.CONDITIONAL_GROUP_RULE) {\n                  enteringConditionalGroup = true;\n                }\n\n                // might be a non-nested at-rule\n              } else if (parenLevel === 0 && !insidePropertyValue) {\n                insideNonNestedAtRule = true;\n              }\n            }\n          } else if (this._ch === '#' && this._input.peek() === '{') {\n            this.preserveSingleSpace(isAfterSpace);\n            this.print_string(this._ch + this.eatString('}'));\n          } else if (this._ch === '{') {\n            if (insidePropertyValue) {\n              insidePropertyValue = false;\n              this.outdent();\n            }\n\n            // non nested at rule becomes nested\n            insideNonNestedAtRule = false;\n\n            // when entering conditional groups, only rulesets are allowed\n            if (enteringConditionalGroup) {\n              enteringConditionalGroup = false;\n              insideRule = this._indentLevel >= this._nestedLevel;\n            } else {\n              // otherwise, declarations are also allowed\n              insideRule = this._indentLevel >= this._nestedLevel - 1;\n            }\n            if (this._options.newline_between_rules && insideRule) {\n              if (this._output.previous_line && this._output.previous_line.item(-1) !== '{') {\n                this._output.ensure_empty_line_above('/', ',');\n              }\n            }\n            this._output.space_before_token = true;\n\n            // The difference in print_string and indent order is necessary to indent the '{' correctly\n            if (this._options.brace_style === 'expand') {\n              this._output.add_new_line();\n              this.print_string(this._ch);\n              this.indent();\n              this._output.set_indent(this._indentLevel);\n            } else {\n              // inside mixin and first param is object\n              if (previous_ch === '(') {\n                this._output.space_before_token = false;\n              } else if (previous_ch !== ',') {\n                this.indent();\n              }\n              this.print_string(this._ch);\n            }\n            this.eatWhitespace(true);\n            this._output.add_new_line();\n          } else if (this._ch === '}') {\n            this.outdent();\n            this._output.add_new_line();\n            if (previous_ch === '{') {\n              this._output.trim(true);\n            }\n            if (insidePropertyValue) {\n              this.outdent();\n              insidePropertyValue = false;\n            }\n            this.print_string(this._ch);\n            insideRule = false;\n            if (this._nestedLevel) {\n              this._nestedLevel--;\n            }\n            this.eatWhitespace(true);\n            this._output.add_new_line();\n            if (this._options.newline_between_rules && !this._output.just_added_blankline()) {\n              if (this._input.peek() !== '}') {\n                this._output.add_new_line(true);\n              }\n            }\n            if (this._input.peek() === ')') {\n              this._output.trim(true);\n              if (this._options.brace_style === \"expand\") {\n                this._output.add_new_line(true);\n              }\n            }\n          } else if (this._ch === \":\") {\n            for (var i = 0; i < this.NON_SEMICOLON_NEWLINE_PROPERTY.length; i++) {\n              if (this._input.lookBack(this.NON_SEMICOLON_NEWLINE_PROPERTY[i])) {\n                insideNonSemiColonValues = true;\n                break;\n              }\n            }\n            if ((insideRule || enteringConditionalGroup) && !(this._input.lookBack(\"&\") || this.foundNestedPseudoClass()) && !this._input.lookBack(\"(\") && !insideNonNestedAtRule && parenLevel === 0) {\n              // 'property: value' delimiter\n              // which could be in a conditional group query\n\n              this.print_string(':');\n              if (!insidePropertyValue) {\n                insidePropertyValue = true;\n                this._output.space_before_token = true;\n                this.eatWhitespace(true);\n                this.indent();\n              }\n            } else {\n              // sass/less parent reference don't use a space\n              // sass nested pseudo-class don't use a space\n\n              // preserve space before pseudoclasses/pseudoelements, as it means \"in any child\"\n              if (this._input.lookBack(\" \")) {\n                this._output.space_before_token = true;\n              }\n              if (this._input.peek() === \":\") {\n                // pseudo-element\n                this._ch = this._input.next();\n                this.print_string(\"::\");\n              } else {\n                // pseudo-class\n                this.print_string(':');\n              }\n            }\n          } else if (this._ch === '\"' || this._ch === '\\'') {\n            var preserveQuoteSpace = previous_ch === '\"' || previous_ch === '\\'';\n            this.preserveSingleSpace(preserveQuoteSpace || isAfterSpace);\n            this.print_string(this._ch + this.eatString(this._ch));\n            this.eatWhitespace(true);\n          } else if (this._ch === ';') {\n            insideNonSemiColonValues = false;\n            if (parenLevel === 0) {\n              if (insidePropertyValue) {\n                this.outdent();\n                insidePropertyValue = false;\n              }\n              insideNonNestedAtRule = false;\n              this.print_string(this._ch);\n              this.eatWhitespace(true);\n\n              // This maintains single line comments on the same\n              // line. Block comments are also affected, but\n              // a new line is always output before one inside\n              // that section\n              if (this._input.peek() !== '/') {\n                this._output.add_new_line();\n              }\n            } else {\n              this.print_string(this._ch);\n              this.eatWhitespace(true);\n              this._output.space_before_token = true;\n            }\n          } else if (this._ch === '(') {\n            // may be a url\n            if (this._input.lookBack(\"url\")) {\n              this.print_string(this._ch);\n              this.eatWhitespace();\n              parenLevel++;\n              this.indent();\n              this._ch = this._input.next();\n              if (this._ch === ')' || this._ch === '\"' || this._ch === '\\'') {\n                this._input.back();\n              } else if (this._ch) {\n                this.print_string(this._ch + this.eatString(')'));\n                if (parenLevel) {\n                  parenLevel--;\n                  this.outdent();\n                }\n              }\n            } else {\n              var space_needed = false;\n              if (this._input.lookBack(\"with\")) {\n                // look back is not an accurate solution, we need tokens to confirm without whitespaces\n                space_needed = true;\n              }\n              this.preserveSingleSpace(isAfterSpace || space_needed);\n              this.print_string(this._ch);\n\n              // handle scss/sass map\n              if (insidePropertyValue && previous_ch === \"$\" && this._options.selector_separator_newline) {\n                this._output.add_new_line();\n                insideScssMap = true;\n              } else {\n                this.eatWhitespace();\n                parenLevel++;\n                this.indent();\n              }\n            }\n          } else if (this._ch === ')') {\n            if (parenLevel) {\n              parenLevel--;\n              this.outdent();\n            }\n            if (insideScssMap && this._input.peek() === \";\" && this._options.selector_separator_newline) {\n              insideScssMap = false;\n              this.outdent();\n              this._output.add_new_line();\n            }\n            this.print_string(this._ch);\n          } else if (this._ch === ',') {\n            this.print_string(this._ch);\n            this.eatWhitespace(true);\n            if (this._options.selector_separator_newline && (!insidePropertyValue || insideScssMap) && parenLevel === 0 && !insideNonNestedAtRule) {\n              this._output.add_new_line();\n            } else {\n              this._output.space_before_token = true;\n            }\n          } else if ((this._ch === '>' || this._ch === '+' || this._ch === '~') && !insidePropertyValue && parenLevel === 0) {\n            //handle combinator spacing\n            if (this._options.space_around_combinator) {\n              this._output.space_before_token = true;\n              this.print_string(this._ch);\n              this._output.space_before_token = true;\n            } else {\n              this.print_string(this._ch);\n              this.eatWhitespace();\n              // squash extra whitespace\n              if (this._ch && whitespaceChar.test(this._ch)) {\n                this._ch = '';\n              }\n            }\n          } else if (this._ch === ']') {\n            this.print_string(this._ch);\n          } else if (this._ch === '[') {\n            this.preserveSingleSpace(isAfterSpace);\n            this.print_string(this._ch);\n          } else if (this._ch === '=') {\n            // no whitespace before or after\n            this.eatWhitespace();\n            this.print_string('=');\n            if (whitespaceChar.test(this._ch)) {\n              this._ch = '';\n            }\n          } else if (this._ch === '!' && !this._input.lookBack(\"\\\\\")) {\n            // !important\n            this._output.space_before_token = true;\n            this.print_string(this._ch);\n          } else {\n            var preserveAfterSpace = previous_ch === '\"' || previous_ch === '\\'';\n            this.preserveSingleSpace(preserveAfterSpace || isAfterSpace);\n            this.print_string(this._ch);\n            if (!this._output.just_added_newline() && this._input.peek() === '\\n' && insideNonSemiColonValues) {\n              this._output.add_new_line();\n            }\n          }\n        }\n        var sweetCode = this._output.get_code(eol);\n        return sweetCode;\n      };\n      module.exports.Beautifier = Beautifier;\n\n      /***/\n    }), (/* 17 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var BaseOptions = __webpack_require__(6).Options;\n      function Options(options) {\n        BaseOptions.call(this, options, 'css');\n        this.selector_separator_newline = this._get_boolean('selector_separator_newline', true);\n        this.newline_between_rules = this._get_boolean('newline_between_rules', true);\n        var space_around_selector_separator = this._get_boolean('space_around_selector_separator');\n        this.space_around_combinator = this._get_boolean('space_around_combinator') || space_around_selector_separator;\n        var brace_style_split = this._get_selection_list('brace_style', ['collapse', 'expand', 'end-expand', 'none', 'preserve-inline']);\n        this.brace_style = 'collapse';\n        for (var bs = 0; bs < brace_style_split.length; bs++) {\n          if (brace_style_split[bs] !== 'expand') {\n            // default to collapse, as only collapse|expand is implemented for now\n            this.brace_style = 'collapse';\n          } else {\n            this.brace_style = brace_style_split[bs];\n          }\n        }\n      }\n      Options.prototype = new BaseOptions();\n      module.exports.Options = Options;\n\n      /***/\n    }\n    /******/)];\n    /************************************************************************/\n    /******/ // The module cache\n    /******/\n    var __webpack_module_cache__ = {};\n    /******/\n    /******/ // The require function\n    /******/\n    function __webpack_require__(moduleId) {\n      /******/ // Check if module is in cache\n      /******/var cachedModule = __webpack_module_cache__[moduleId];\n      /******/\n      if (cachedModule !== undefined) {\n        /******/return cachedModule.exports;\n        /******/\n      }\n      /******/ // Create a new module (and put it into the cache)\n      /******/\n      var module = __webpack_module_cache__[moduleId] = {\n        /******/ // no module.id needed\n        /******/ // no module.loaded needed\n        /******/exports: {}\n        /******/\n      };\n      /******/\n      /******/ // Execute the module function\n      /******/\n      __webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n      /******/\n      /******/ // Return the exports of the module\n      /******/\n      return module.exports;\n      /******/\n    }\n    /******/\n    /************************************************************************/\n    /******/\n    /******/ // startup\n    /******/ // Load entry module and return exports\n    /******/ // This entry module is referenced by other modules so it can't be inlined\n    /******/\n    var __webpack_exports__ = __webpack_require__(15);\n    /******/\n    legacy_beautify_css = __webpack_exports__;\n    /******/\n    /******/\n  })();\n  var css_beautify = legacy_beautify_css;\n  /* Footer */\n  if (typeof define === \"function\" && define.amd) {\n    // Add support for AMD ( https://github.com/amdjs/amdjs-api/wiki/AMD#defineamd-property- )\n    define([], function () {\n      return {\n        css_beautify: css_beautify\n      };\n    });\n  } else if (typeof exports !== \"undefined\") {\n    // Add support for CommonJS. Just put this file somewhere on your require.paths\n    // and you will be able to `var html_beautify = require(\"beautify\").html_beautify`.\n    exports.css_beautify = css_beautify;\n  } else if (typeof window !== \"undefined\") {\n    // If we're running a web page and don't have either of the above, add our one global\n    window.css_beautify = css_beautify;\n  } else if (typeof global !== \"undefined\") {\n    // If we don't even have window, try global.\n    global.css_beautify = css_beautify;\n  }\n})();", "map": {"version": 3, "names": ["legacy_beautify_css", "__webpack_modules__", "module", "OutputLine", "parent", "__parent", "__character_count", "__indent_count", "__alignment_count", "__wrap_point_index", "__wrap_point_character_count", "__wrap_point_indent_count", "__wrap_point_alignment_count", "__items", "prototype", "clone_empty", "line", "set_indent", "item", "index", "length", "has_match", "pattern", "lastCheckedOutput", "match", "indent", "alignment", "is_empty", "get_indent_size", "_set_wrap_point", "wrap_line_length", "next_line", "_should_wrap", "_allow_wrap", "add_new_line", "next", "current_line", "slice", "splice", "last", "push", "last_newline_index", "lastIndexOf", "pop", "_remove_indent", "indent_size", "_remove_wrap_indent", "trim", "toString", "result", "indent_empty_lines", "get_indent_string", "join", "IndentStringCache", "options", "baseIndentString", "__cache", "__indent_size", "__indent_string", "indent_char", "indent_with_tabs", "Array", "indent_level", "__base_string", "__base_string_length", "column", "__ensure_cache", "__add_column", "Math", "floor", "Output", "__indent_cache", "raw", "_end_with_newline", "end_with_newline", "__lines", "previous_line", "space_before_token", "non_breaking_space", "previous_token_wrapped", "__add_outputline", "get_line_number", "force_newline", "just_added_newline", "get_code", "eol", "last_item", "replace", "sweet_code", "set_wrap_point", "add_raw_token", "token", "x", "newlines", "whitespace_before", "text", "add_token", "printable_token", "__add_space_before_token", "remove_indent", "output_length", "eat_newlines", "undefined", "just_added_blankline", "ensure_empty_line_above", "starts_with", "ends_with", "potentialEmptyLine", "indexOf", "exports", "Options", "merge_child_field", "raw_options", "_mergeOpts", "disabled", "_get_boolean", "_get_characters", "_get_number", "preserve_newlines", "max_preserve_newlines", "templating", "_get_selection_list", "_get_array", "name", "default_value", "option_value", "concat", "split", "parseInt", "isNaN", "_get_selection", "selection_list", "Error", "_is_valid_selection", "some", "allOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalOpts", "_normalizeOpts", "convertedOpts", "key", "new<PERSON>ey", "normalizeOpts", "mergeOpts", "regexp_has_sticky", "RegExp", "hasOwnProperty", "InputScanner", "input_string", "__input", "__input_length", "__position", "restart", "back", "hasNext", "val", "char<PERSON>t", "peek", "__match", "lastIndex", "pattern_match", "exec", "sticky", "test", "testChar", "read", "starting_pattern", "until_pattern", "until_after", "readUntil", "match_index", "substring", "readUntilAfter", "get_regexp", "match_from", "flags", "source", "get_literal_regexp", "literal_string", "peekUntilAfter", "start", "lookBack", "testVal", "toLowerCase", "Directives", "start_block_pattern", "end_block_pattern", "__directives_block_pattern", "__directive_pattern", "__directives_end_ignore_pattern", "get_directives", "directives", "directive_match", "readIgnored", "input", "__unused_webpack_exports", "__webpack_require__", "Beautifier", "css_beautify", "source_text", "beautifier", "beautify", "defaultOptions", "directives_core", "lineBreak", "allLineBreaks", "whitespaceChar", "whitespacePattern", "block_comment_pattern", "comment_pattern", "_source_text", "_options", "_ch", "_input", "NESTED_AT_RULE", "CONDITIONAL_GROUP_RULE", "NON_SEMICOLON_NEWLINE_PROPERTY", "eatString", "endChars", "eatWhitespace", "allowAtLeastOneNewLine", "newline_count", "_output", "foundNestedPseudoClass", "openParen", "i", "ch", "print_string", "output_string", "_indentLevel", "preserveSingleSpace", "isAfterSpace", "outdent", "_nestedLevel", "parenLevel", "insideRule", "insidePropertyValue", "enteringConditionalGroup", "insideNonNestedAtRule", "insideScssMap", "topCharacter", "insideNonSemiColonValues", "whitespace", "previous_ch", "comment", "ignore", "variable", "variableOrRule", "newline_between_rules", "brace_style", "preserveQuoteSpace", "space_needed", "selector_separator_newline", "space_around_combinator", "preserveAfterSpace", "sweetCode", "BaseOptions", "call", "space_around_selector_separator", "brace_style_split", "bs", "__webpack_module_cache__", "moduleId", "cachedModule", "__webpack_exports__", "define", "amd", "window", "global"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/js-beautify/js/lib/beautify-css.js"], "sourcesContent": ["/* AUTO-GENERATED. DO NOT MODIFY. */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n\n\n CSS Beautifier\n---------------\n\n    Written by Harutyun Amirjanyan, (<EMAIL>)\n\n    Based on code initially developed by: Einar Lielmanis, <<EMAIL>>\n        https://beautifier.io/\n\n    Usage:\n        css_beautify(source_text);\n        css_beautify(source_text, options);\n\n    The options are (default in brackets):\n        indent_size (4)                         — indentation size,\n        indent_char (space)                     — character to indent with,\n        selector_separator_newline (true)       - separate selectors with newline or\n                                                  not (e.g. \"a,\\nbr\" or \"a, br\")\n        end_with_newline (false)                - end with a newline\n        newline_between_rules (true)            - add a new line after every css rule\n        space_around_selector_separator (false) - ensure space around selector separators:\n                                                  '>', '+', '~' (e.g. \"a>b\" -> \"a > b\")\n    e.g\n\n    css_beautify(css_source_text, {\n      'indent_size': 1,\n      'indent_char': '\\t',\n      'selector_separator': ' ',\n      'end_with_newline': false,\n      'newline_between_rules': true,\n      'space_around_selector_separator': true\n    });\n*/\n\n// http://www.w3.org/TR/CSS21/syndata.html#tokenization\n// http://www.w3.org/TR/css3-syntax/\n\n(function() {\n\n/* GENERATED_BUILD_OUTPUT */\nvar legacy_beautify_css;\n/******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ([\n/* 0 */,\n/* 1 */,\n/* 2 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nfunction OutputLine(parent) {\n  this.__parent = parent;\n  this.__character_count = 0;\n  // use indent_count as a marker for this.__lines that have preserved indentation\n  this.__indent_count = -1;\n  this.__alignment_count = 0;\n  this.__wrap_point_index = 0;\n  this.__wrap_point_character_count = 0;\n  this.__wrap_point_indent_count = -1;\n  this.__wrap_point_alignment_count = 0;\n\n  this.__items = [];\n}\n\nOutputLine.prototype.clone_empty = function() {\n  var line = new OutputLine(this.__parent);\n  line.set_indent(this.__indent_count, this.__alignment_count);\n  return line;\n};\n\nOutputLine.prototype.item = function(index) {\n  if (index < 0) {\n    return this.__items[this.__items.length + index];\n  } else {\n    return this.__items[index];\n  }\n};\n\nOutputLine.prototype.has_match = function(pattern) {\n  for (var lastCheckedOutput = this.__items.length - 1; lastCheckedOutput >= 0; lastCheckedOutput--) {\n    if (this.__items[lastCheckedOutput].match(pattern)) {\n      return true;\n    }\n  }\n  return false;\n};\n\nOutputLine.prototype.set_indent = function(indent, alignment) {\n  if (this.is_empty()) {\n    this.__indent_count = indent || 0;\n    this.__alignment_count = alignment || 0;\n    this.__character_count = this.__parent.get_indent_size(this.__indent_count, this.__alignment_count);\n  }\n};\n\nOutputLine.prototype._set_wrap_point = function() {\n  if (this.__parent.wrap_line_length) {\n    this.__wrap_point_index = this.__items.length;\n    this.__wrap_point_character_count = this.__character_count;\n    this.__wrap_point_indent_count = this.__parent.next_line.__indent_count;\n    this.__wrap_point_alignment_count = this.__parent.next_line.__alignment_count;\n  }\n};\n\nOutputLine.prototype._should_wrap = function() {\n  return this.__wrap_point_index &&\n    this.__character_count > this.__parent.wrap_line_length &&\n    this.__wrap_point_character_count > this.__parent.next_line.__character_count;\n};\n\nOutputLine.prototype._allow_wrap = function() {\n  if (this._should_wrap()) {\n    this.__parent.add_new_line();\n    var next = this.__parent.current_line;\n    next.set_indent(this.__wrap_point_indent_count, this.__wrap_point_alignment_count);\n    next.__items = this.__items.slice(this.__wrap_point_index);\n    this.__items = this.__items.slice(0, this.__wrap_point_index);\n\n    next.__character_count += this.__character_count - this.__wrap_point_character_count;\n    this.__character_count = this.__wrap_point_character_count;\n\n    if (next.__items[0] === \" \") {\n      next.__items.splice(0, 1);\n      next.__character_count -= 1;\n    }\n    return true;\n  }\n  return false;\n};\n\nOutputLine.prototype.is_empty = function() {\n  return this.__items.length === 0;\n};\n\nOutputLine.prototype.last = function() {\n  if (!this.is_empty()) {\n    return this.__items[this.__items.length - 1];\n  } else {\n    return null;\n  }\n};\n\nOutputLine.prototype.push = function(item) {\n  this.__items.push(item);\n  var last_newline_index = item.lastIndexOf('\\n');\n  if (last_newline_index !== -1) {\n    this.__character_count = item.length - last_newline_index;\n  } else {\n    this.__character_count += item.length;\n  }\n};\n\nOutputLine.prototype.pop = function() {\n  var item = null;\n  if (!this.is_empty()) {\n    item = this.__items.pop();\n    this.__character_count -= item.length;\n  }\n  return item;\n};\n\n\nOutputLine.prototype._remove_indent = function() {\n  if (this.__indent_count > 0) {\n    this.__indent_count -= 1;\n    this.__character_count -= this.__parent.indent_size;\n  }\n};\n\nOutputLine.prototype._remove_wrap_indent = function() {\n  if (this.__wrap_point_indent_count > 0) {\n    this.__wrap_point_indent_count -= 1;\n  }\n};\nOutputLine.prototype.trim = function() {\n  while (this.last() === ' ') {\n    this.__items.pop();\n    this.__character_count -= 1;\n  }\n};\n\nOutputLine.prototype.toString = function() {\n  var result = '';\n  if (this.is_empty()) {\n    if (this.__parent.indent_empty_lines) {\n      result = this.__parent.get_indent_string(this.__indent_count);\n    }\n  } else {\n    result = this.__parent.get_indent_string(this.__indent_count, this.__alignment_count);\n    result += this.__items.join('');\n  }\n  return result;\n};\n\nfunction IndentStringCache(options, baseIndentString) {\n  this.__cache = [''];\n  this.__indent_size = options.indent_size;\n  this.__indent_string = options.indent_char;\n  if (!options.indent_with_tabs) {\n    this.__indent_string = new Array(options.indent_size + 1).join(options.indent_char);\n  }\n\n  // Set to null to continue support for auto detection of base indent\n  baseIndentString = baseIndentString || '';\n  if (options.indent_level > 0) {\n    baseIndentString = new Array(options.indent_level + 1).join(this.__indent_string);\n  }\n\n  this.__base_string = baseIndentString;\n  this.__base_string_length = baseIndentString.length;\n}\n\nIndentStringCache.prototype.get_indent_size = function(indent, column) {\n  var result = this.__base_string_length;\n  column = column || 0;\n  if (indent < 0) {\n    result = 0;\n  }\n  result += indent * this.__indent_size;\n  result += column;\n  return result;\n};\n\nIndentStringCache.prototype.get_indent_string = function(indent_level, column) {\n  var result = this.__base_string;\n  column = column || 0;\n  if (indent_level < 0) {\n    indent_level = 0;\n    result = '';\n  }\n  column += indent_level * this.__indent_size;\n  this.__ensure_cache(column);\n  result += this.__cache[column];\n  return result;\n};\n\nIndentStringCache.prototype.__ensure_cache = function(column) {\n  while (column >= this.__cache.length) {\n    this.__add_column();\n  }\n};\n\nIndentStringCache.prototype.__add_column = function() {\n  var column = this.__cache.length;\n  var indent = 0;\n  var result = '';\n  if (this.__indent_size && column >= this.__indent_size) {\n    indent = Math.floor(column / this.__indent_size);\n    column -= indent * this.__indent_size;\n    result = new Array(indent + 1).join(this.__indent_string);\n  }\n  if (column) {\n    result += new Array(column + 1).join(' ');\n  }\n\n  this.__cache.push(result);\n};\n\nfunction Output(options, baseIndentString) {\n  this.__indent_cache = new IndentStringCache(options, baseIndentString);\n  this.raw = false;\n  this._end_with_newline = options.end_with_newline;\n  this.indent_size = options.indent_size;\n  this.wrap_line_length = options.wrap_line_length;\n  this.indent_empty_lines = options.indent_empty_lines;\n  this.__lines = [];\n  this.previous_line = null;\n  this.current_line = null;\n  this.next_line = new OutputLine(this);\n  this.space_before_token = false;\n  this.non_breaking_space = false;\n  this.previous_token_wrapped = false;\n  // initialize\n  this.__add_outputline();\n}\n\nOutput.prototype.__add_outputline = function() {\n  this.previous_line = this.current_line;\n  this.current_line = this.next_line.clone_empty();\n  this.__lines.push(this.current_line);\n};\n\nOutput.prototype.get_line_number = function() {\n  return this.__lines.length;\n};\n\nOutput.prototype.get_indent_string = function(indent, column) {\n  return this.__indent_cache.get_indent_string(indent, column);\n};\n\nOutput.prototype.get_indent_size = function(indent, column) {\n  return this.__indent_cache.get_indent_size(indent, column);\n};\n\nOutput.prototype.is_empty = function() {\n  return !this.previous_line && this.current_line.is_empty();\n};\n\nOutput.prototype.add_new_line = function(force_newline) {\n  // never newline at the start of file\n  // otherwise, newline only if we didn't just add one or we're forced\n  if (this.is_empty() ||\n    (!force_newline && this.just_added_newline())) {\n    return false;\n  }\n\n  // if raw output is enabled, don't print additional newlines,\n  // but still return True as though you had\n  if (!this.raw) {\n    this.__add_outputline();\n  }\n  return true;\n};\n\nOutput.prototype.get_code = function(eol) {\n  this.trim(true);\n\n  // handle some edge cases where the last tokens\n  // has text that ends with newline(s)\n  var last_item = this.current_line.pop();\n  if (last_item) {\n    if (last_item[last_item.length - 1] === '\\n') {\n      last_item = last_item.replace(/\\n+$/g, '');\n    }\n    this.current_line.push(last_item);\n  }\n\n  if (this._end_with_newline) {\n    this.__add_outputline();\n  }\n\n  var sweet_code = this.__lines.join('\\n');\n\n  if (eol !== '\\n') {\n    sweet_code = sweet_code.replace(/[\\n]/g, eol);\n  }\n  return sweet_code;\n};\n\nOutput.prototype.set_wrap_point = function() {\n  this.current_line._set_wrap_point();\n};\n\nOutput.prototype.set_indent = function(indent, alignment) {\n  indent = indent || 0;\n  alignment = alignment || 0;\n\n  // Next line stores alignment values\n  this.next_line.set_indent(indent, alignment);\n\n  // Never indent your first output indent at the start of the file\n  if (this.__lines.length > 1) {\n    this.current_line.set_indent(indent, alignment);\n    return true;\n  }\n\n  this.current_line.set_indent();\n  return false;\n};\n\nOutput.prototype.add_raw_token = function(token) {\n  for (var x = 0; x < token.newlines; x++) {\n    this.__add_outputline();\n  }\n  this.current_line.set_indent(-1);\n  this.current_line.push(token.whitespace_before);\n  this.current_line.push(token.text);\n  this.space_before_token = false;\n  this.non_breaking_space = false;\n  this.previous_token_wrapped = false;\n};\n\nOutput.prototype.add_token = function(printable_token) {\n  this.__add_space_before_token();\n  this.current_line.push(printable_token);\n  this.space_before_token = false;\n  this.non_breaking_space = false;\n  this.previous_token_wrapped = this.current_line._allow_wrap();\n};\n\nOutput.prototype.__add_space_before_token = function() {\n  if (this.space_before_token && !this.just_added_newline()) {\n    if (!this.non_breaking_space) {\n      this.set_wrap_point();\n    }\n    this.current_line.push(' ');\n  }\n};\n\nOutput.prototype.remove_indent = function(index) {\n  var output_length = this.__lines.length;\n  while (index < output_length) {\n    this.__lines[index]._remove_indent();\n    index++;\n  }\n  this.current_line._remove_wrap_indent();\n};\n\nOutput.prototype.trim = function(eat_newlines) {\n  eat_newlines = (eat_newlines === undefined) ? false : eat_newlines;\n\n  this.current_line.trim();\n\n  while (eat_newlines && this.__lines.length > 1 &&\n    this.current_line.is_empty()) {\n    this.__lines.pop();\n    this.current_line = this.__lines[this.__lines.length - 1];\n    this.current_line.trim();\n  }\n\n  this.previous_line = this.__lines.length > 1 ?\n    this.__lines[this.__lines.length - 2] : null;\n};\n\nOutput.prototype.just_added_newline = function() {\n  return this.current_line.is_empty();\n};\n\nOutput.prototype.just_added_blankline = function() {\n  return this.is_empty() ||\n    (this.current_line.is_empty() && this.previous_line.is_empty());\n};\n\nOutput.prototype.ensure_empty_line_above = function(starts_with, ends_with) {\n  var index = this.__lines.length - 2;\n  while (index >= 0) {\n    var potentialEmptyLine = this.__lines[index];\n    if (potentialEmptyLine.is_empty()) {\n      break;\n    } else if (potentialEmptyLine.item(0).indexOf(starts_with) !== 0 &&\n      potentialEmptyLine.item(-1) !== ends_with) {\n      this.__lines.splice(index + 1, 0, new OutputLine(this));\n      this.previous_line = this.__lines[this.__lines.length - 2];\n      break;\n    }\n    index--;\n  }\n};\n\nmodule.exports.Output = Output;\n\n\n/***/ }),\n/* 3 */,\n/* 4 */,\n/* 5 */,\n/* 6 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nfunction Options(options, merge_child_field) {\n  this.raw_options = _mergeOpts(options, merge_child_field);\n\n  // Support passing the source text back with no change\n  this.disabled = this._get_boolean('disabled');\n\n  this.eol = this._get_characters('eol', 'auto');\n  this.end_with_newline = this._get_boolean('end_with_newline');\n  this.indent_size = this._get_number('indent_size', 4);\n  this.indent_char = this._get_characters('indent_char', ' ');\n  this.indent_level = this._get_number('indent_level');\n\n  this.preserve_newlines = this._get_boolean('preserve_newlines', true);\n  this.max_preserve_newlines = this._get_number('max_preserve_newlines', 32786);\n  if (!this.preserve_newlines) {\n    this.max_preserve_newlines = 0;\n  }\n\n  this.indent_with_tabs = this._get_boolean('indent_with_tabs', this.indent_char === '\\t');\n  if (this.indent_with_tabs) {\n    this.indent_char = '\\t';\n\n    // indent_size behavior changed after 1.8.6\n    // It used to be that indent_size would be\n    // set to 1 for indent_with_tabs. That is no longer needed and\n    // actually doesn't make sense - why not use spaces? Further,\n    // that might produce unexpected behavior - tabs being used\n    // for single-column alignment. So, when indent_with_tabs is true\n    // and indent_size is 1, reset indent_size to 4.\n    if (this.indent_size === 1) {\n      this.indent_size = 4;\n    }\n  }\n\n  // Backwards compat with 1.3.x\n  this.wrap_line_length = this._get_number('wrap_line_length', this._get_number('max_char'));\n\n  this.indent_empty_lines = this._get_boolean('indent_empty_lines');\n\n  // valid templating languages ['django', 'erb', 'handlebars', 'php', 'smarty', 'angular']\n  // For now, 'auto' = all off for javascript, all except angular on for html (and inline javascript/css).\n  // other values ignored\n  this.templating = this._get_selection_list('templating', ['auto', 'none', 'angular', 'django', 'erb', 'handlebars', 'php', 'smarty'], ['auto']);\n}\n\nOptions.prototype._get_array = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  var result = default_value || [];\n  if (typeof option_value === 'object') {\n    if (option_value !== null && typeof option_value.concat === 'function') {\n      result = option_value.concat();\n    }\n  } else if (typeof option_value === 'string') {\n    result = option_value.split(/[^a-zA-Z0-9_\\/\\-]+/);\n  }\n  return result;\n};\n\nOptions.prototype._get_boolean = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  var result = option_value === undefined ? !!default_value : !!option_value;\n  return result;\n};\n\nOptions.prototype._get_characters = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  var result = default_value || '';\n  if (typeof option_value === 'string') {\n    result = option_value.replace(/\\\\r/, '\\r').replace(/\\\\n/, '\\n').replace(/\\\\t/, '\\t');\n  }\n  return result;\n};\n\nOptions.prototype._get_number = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  default_value = parseInt(default_value, 10);\n  if (isNaN(default_value)) {\n    default_value = 0;\n  }\n  var result = parseInt(option_value, 10);\n  if (isNaN(result)) {\n    result = default_value;\n  }\n  return result;\n};\n\nOptions.prototype._get_selection = function(name, selection_list, default_value) {\n  var result = this._get_selection_list(name, selection_list, default_value);\n  if (result.length !== 1) {\n    throw new Error(\n      \"Invalid Option Value: The option '\" + name + \"' can only be one of the following values:\\n\" +\n      selection_list + \"\\nYou passed in: '\" + this.raw_options[name] + \"'\");\n  }\n\n  return result[0];\n};\n\n\nOptions.prototype._get_selection_list = function(name, selection_list, default_value) {\n  if (!selection_list || selection_list.length === 0) {\n    throw new Error(\"Selection list cannot be empty.\");\n  }\n\n  default_value = default_value || [selection_list[0]];\n  if (!this._is_valid_selection(default_value, selection_list)) {\n    throw new Error(\"Invalid Default Value!\");\n  }\n\n  var result = this._get_array(name, default_value);\n  if (!this._is_valid_selection(result, selection_list)) {\n    throw new Error(\n      \"Invalid Option Value: The option '\" + name + \"' can contain only the following values:\\n\" +\n      selection_list + \"\\nYou passed in: '\" + this.raw_options[name] + \"'\");\n  }\n\n  return result;\n};\n\nOptions.prototype._is_valid_selection = function(result, selection_list) {\n  return result.length && selection_list.length &&\n    !result.some(function(item) { return selection_list.indexOf(item) === -1; });\n};\n\n\n// merges child options up with the parent options object\n// Example: obj = {a: 1, b: {a: 2}}\n//          mergeOpts(obj, 'b')\n//\n//          Returns: {a: 2}\nfunction _mergeOpts(allOptions, childFieldName) {\n  var finalOpts = {};\n  allOptions = _normalizeOpts(allOptions);\n  var name;\n\n  for (name in allOptions) {\n    if (name !== childFieldName) {\n      finalOpts[name] = allOptions[name];\n    }\n  }\n\n  //merge in the per type settings for the childFieldName\n  if (childFieldName && allOptions[childFieldName]) {\n    for (name in allOptions[childFieldName]) {\n      finalOpts[name] = allOptions[childFieldName][name];\n    }\n  }\n  return finalOpts;\n}\n\nfunction _normalizeOpts(options) {\n  var convertedOpts = {};\n  var key;\n\n  for (key in options) {\n    var newKey = key.replace(/-/g, \"_\");\n    convertedOpts[newKey] = options[key];\n  }\n  return convertedOpts;\n}\n\nmodule.exports.Options = Options;\nmodule.exports.normalizeOpts = _normalizeOpts;\nmodule.exports.mergeOpts = _mergeOpts;\n\n\n/***/ }),\n/* 7 */,\n/* 8 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar regexp_has_sticky = RegExp.prototype.hasOwnProperty('sticky');\n\nfunction InputScanner(input_string) {\n  this.__input = input_string || '';\n  this.__input_length = this.__input.length;\n  this.__position = 0;\n}\n\nInputScanner.prototype.restart = function() {\n  this.__position = 0;\n};\n\nInputScanner.prototype.back = function() {\n  if (this.__position > 0) {\n    this.__position -= 1;\n  }\n};\n\nInputScanner.prototype.hasNext = function() {\n  return this.__position < this.__input_length;\n};\n\nInputScanner.prototype.next = function() {\n  var val = null;\n  if (this.hasNext()) {\n    val = this.__input.charAt(this.__position);\n    this.__position += 1;\n  }\n  return val;\n};\n\nInputScanner.prototype.peek = function(index) {\n  var val = null;\n  index = index || 0;\n  index += this.__position;\n  if (index >= 0 && index < this.__input_length) {\n    val = this.__input.charAt(index);\n  }\n  return val;\n};\n\n// This is a JavaScript only helper function (not in python)\n// Javascript doesn't have a match method\n// and not all implementation support \"sticky\" flag.\n// If they do not support sticky then both this.match() and this.test() method\n// must get the match and check the index of the match.\n// If sticky is supported and set, this method will use it.\n// Otherwise it will check that global is set, and fall back to the slower method.\nInputScanner.prototype.__match = function(pattern, index) {\n  pattern.lastIndex = index;\n  var pattern_match = pattern.exec(this.__input);\n\n  if (pattern_match && !(regexp_has_sticky && pattern.sticky)) {\n    if (pattern_match.index !== index) {\n      pattern_match = null;\n    }\n  }\n\n  return pattern_match;\n};\n\nInputScanner.prototype.test = function(pattern, index) {\n  index = index || 0;\n  index += this.__position;\n\n  if (index >= 0 && index < this.__input_length) {\n    return !!this.__match(pattern, index);\n  } else {\n    return false;\n  }\n};\n\nInputScanner.prototype.testChar = function(pattern, index) {\n  // test one character regex match\n  var val = this.peek(index);\n  pattern.lastIndex = 0;\n  return val !== null && pattern.test(val);\n};\n\nInputScanner.prototype.match = function(pattern) {\n  var pattern_match = this.__match(pattern, this.__position);\n  if (pattern_match) {\n    this.__position += pattern_match[0].length;\n  } else {\n    pattern_match = null;\n  }\n  return pattern_match;\n};\n\nInputScanner.prototype.read = function(starting_pattern, until_pattern, until_after) {\n  var val = '';\n  var match;\n  if (starting_pattern) {\n    match = this.match(starting_pattern);\n    if (match) {\n      val += match[0];\n    }\n  }\n  if (until_pattern && (match || !starting_pattern)) {\n    val += this.readUntil(until_pattern, until_after);\n  }\n  return val;\n};\n\nInputScanner.prototype.readUntil = function(pattern, until_after) {\n  var val = '';\n  var match_index = this.__position;\n  pattern.lastIndex = this.__position;\n  var pattern_match = pattern.exec(this.__input);\n  if (pattern_match) {\n    match_index = pattern_match.index;\n    if (until_after) {\n      match_index += pattern_match[0].length;\n    }\n  } else {\n    match_index = this.__input_length;\n  }\n\n  val = this.__input.substring(this.__position, match_index);\n  this.__position = match_index;\n  return val;\n};\n\nInputScanner.prototype.readUntilAfter = function(pattern) {\n  return this.readUntil(pattern, true);\n};\n\nInputScanner.prototype.get_regexp = function(pattern, match_from) {\n  var result = null;\n  var flags = 'g';\n  if (match_from && regexp_has_sticky) {\n    flags = 'y';\n  }\n  // strings are converted to regexp\n  if (typeof pattern === \"string\" && pattern !== '') {\n    // result = new RegExp(pattern.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'), flags);\n    result = new RegExp(pattern, flags);\n  } else if (pattern) {\n    result = new RegExp(pattern.source, flags);\n  }\n  return result;\n};\n\nInputScanner.prototype.get_literal_regexp = function(literal_string) {\n  return RegExp(literal_string.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'));\n};\n\n/* css beautifier legacy helpers */\nInputScanner.prototype.peekUntilAfter = function(pattern) {\n  var start = this.__position;\n  var val = this.readUntilAfter(pattern);\n  this.__position = start;\n  return val;\n};\n\nInputScanner.prototype.lookBack = function(testVal) {\n  var start = this.__position - 1;\n  return start >= testVal.length && this.__input.substring(start - testVal.length, start)\n    .toLowerCase() === testVal;\n};\n\nmodule.exports.InputScanner = InputScanner;\n\n\n/***/ }),\n/* 9 */,\n/* 10 */,\n/* 11 */,\n/* 12 */,\n/* 13 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nfunction Directives(start_block_pattern, end_block_pattern) {\n  start_block_pattern = typeof start_block_pattern === 'string' ? start_block_pattern : start_block_pattern.source;\n  end_block_pattern = typeof end_block_pattern === 'string' ? end_block_pattern : end_block_pattern.source;\n  this.__directives_block_pattern = new RegExp(start_block_pattern + / beautify( \\w+[:]\\w+)+ /.source + end_block_pattern, 'g');\n  this.__directive_pattern = / (\\w+)[:](\\w+)/g;\n\n  this.__directives_end_ignore_pattern = new RegExp(start_block_pattern + /\\sbeautify\\signore:end\\s/.source + end_block_pattern, 'g');\n}\n\nDirectives.prototype.get_directives = function(text) {\n  if (!text.match(this.__directives_block_pattern)) {\n    return null;\n  }\n\n  var directives = {};\n  this.__directive_pattern.lastIndex = 0;\n  var directive_match = this.__directive_pattern.exec(text);\n\n  while (directive_match) {\n    directives[directive_match[1]] = directive_match[2];\n    directive_match = this.__directive_pattern.exec(text);\n  }\n\n  return directives;\n};\n\nDirectives.prototype.readIgnored = function(input) {\n  return input.readUntilAfter(this.__directives_end_ignore_pattern);\n};\n\n\nmodule.exports.Directives = Directives;\n\n\n/***/ }),\n/* 14 */,\n/* 15 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar Beautifier = (__webpack_require__(16).Beautifier),\n  Options = (__webpack_require__(17).Options);\n\nfunction css_beautify(source_text, options) {\n  var beautifier = new Beautifier(source_text, options);\n  return beautifier.beautify();\n}\n\nmodule.exports = css_beautify;\nmodule.exports.defaultOptions = function() {\n  return new Options();\n};\n\n\n/***/ }),\n/* 16 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar Options = (__webpack_require__(17).Options);\nvar Output = (__webpack_require__(2).Output);\nvar InputScanner = (__webpack_require__(8).InputScanner);\nvar Directives = (__webpack_require__(13).Directives);\n\nvar directives_core = new Directives(/\\/\\*/, /\\*\\//);\n\nvar lineBreak = /\\r\\n|[\\r\\n]/;\nvar allLineBreaks = /\\r\\n|[\\r\\n]/g;\n\n// tokenizer\nvar whitespaceChar = /\\s/;\nvar whitespacePattern = /(?:\\s|\\n)+/g;\nvar block_comment_pattern = /\\/\\*(?:[\\s\\S]*?)((?:\\*\\/)|$)/g;\nvar comment_pattern = /\\/\\/(?:[^\\n\\r\\u2028\\u2029]*)/g;\n\nfunction Beautifier(source_text, options) {\n  this._source_text = source_text || '';\n  // Allow the setting of language/file-type specific options\n  // with inheritance of overall settings\n  this._options = new Options(options);\n  this._ch = null;\n  this._input = null;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/At-rule\n  this.NESTED_AT_RULE = {\n    \"page\": true,\n    \"font-face\": true,\n    \"keyframes\": true,\n    // also in CONDITIONAL_GROUP_RULE below\n    \"media\": true,\n    \"supports\": true,\n    \"document\": true\n  };\n  this.CONDITIONAL_GROUP_RULE = {\n    \"media\": true,\n    \"supports\": true,\n    \"document\": true\n  };\n  this.NON_SEMICOLON_NEWLINE_PROPERTY = [\n    \"grid-template-areas\",\n    \"grid-template\"\n  ];\n\n}\n\nBeautifier.prototype.eatString = function(endChars) {\n  var result = '';\n  this._ch = this._input.next();\n  while (this._ch) {\n    result += this._ch;\n    if (this._ch === \"\\\\\") {\n      result += this._input.next();\n    } else if (endChars.indexOf(this._ch) !== -1 || this._ch === \"\\n\") {\n      break;\n    }\n    this._ch = this._input.next();\n  }\n  return result;\n};\n\n// Skips any white space in the source text from the current position.\n// When allowAtLeastOneNewLine is true, will output new lines for each\n// newline character found; if the user has preserve_newlines off, only\n// the first newline will be output\nBeautifier.prototype.eatWhitespace = function(allowAtLeastOneNewLine) {\n  var result = whitespaceChar.test(this._input.peek());\n  var newline_count = 0;\n  while (whitespaceChar.test(this._input.peek())) {\n    this._ch = this._input.next();\n    if (allowAtLeastOneNewLine && this._ch === '\\n') {\n      if (newline_count === 0 || newline_count < this._options.max_preserve_newlines) {\n        newline_count++;\n        this._output.add_new_line(true);\n      }\n    }\n  }\n  return result;\n};\n\n// Nested pseudo-class if we are insideRule\n// and the next special character found opens\n// a new block\nBeautifier.prototype.foundNestedPseudoClass = function() {\n  var openParen = 0;\n  var i = 1;\n  var ch = this._input.peek(i);\n  while (ch) {\n    if (ch === \"{\") {\n      return true;\n    } else if (ch === '(') {\n      // pseudoclasses can contain ()\n      openParen += 1;\n    } else if (ch === ')') {\n      if (openParen === 0) {\n        return false;\n      }\n      openParen -= 1;\n    } else if (ch === \";\" || ch === \"}\") {\n      return false;\n    }\n    i++;\n    ch = this._input.peek(i);\n  }\n  return false;\n};\n\nBeautifier.prototype.print_string = function(output_string) {\n  this._output.set_indent(this._indentLevel);\n  this._output.non_breaking_space = true;\n  this._output.add_token(output_string);\n};\n\nBeautifier.prototype.preserveSingleSpace = function(isAfterSpace) {\n  if (isAfterSpace) {\n    this._output.space_before_token = true;\n  }\n};\n\nBeautifier.prototype.indent = function() {\n  this._indentLevel++;\n};\n\nBeautifier.prototype.outdent = function() {\n  if (this._indentLevel > 0) {\n    this._indentLevel--;\n  }\n};\n\n/*_____________________--------------------_____________________*/\n\nBeautifier.prototype.beautify = function() {\n  if (this._options.disabled) {\n    return this._source_text;\n  }\n\n  var source_text = this._source_text;\n  var eol = this._options.eol;\n  if (eol === 'auto') {\n    eol = '\\n';\n    if (source_text && lineBreak.test(source_text || '')) {\n      eol = source_text.match(lineBreak)[0];\n    }\n  }\n\n\n  // HACK: newline parsing inconsistent. This brute force normalizes the this._input.\n  source_text = source_text.replace(allLineBreaks, '\\n');\n\n  // reset\n  var baseIndentString = source_text.match(/^[\\t ]*/)[0];\n\n  this._output = new Output(this._options, baseIndentString);\n  this._input = new InputScanner(source_text);\n  this._indentLevel = 0;\n  this._nestedLevel = 0;\n\n  this._ch = null;\n  var parenLevel = 0;\n\n  var insideRule = false;\n  // This is the value side of a property value pair (blue in the following ex)\n  // label { content: blue }\n  var insidePropertyValue = false;\n  var enteringConditionalGroup = false;\n  var insideNonNestedAtRule = false;\n  var insideScssMap = false;\n  var topCharacter = this._ch;\n  var insideNonSemiColonValues = false;\n  var whitespace;\n  var isAfterSpace;\n  var previous_ch;\n\n  while (true) {\n    whitespace = this._input.read(whitespacePattern);\n    isAfterSpace = whitespace !== '';\n    previous_ch = topCharacter;\n    this._ch = this._input.next();\n    if (this._ch === '\\\\' && this._input.hasNext()) {\n      this._ch += this._input.next();\n    }\n    topCharacter = this._ch;\n\n    if (!this._ch) {\n      break;\n    } else if (this._ch === '/' && this._input.peek() === '*') {\n      // /* css comment */\n      // Always start block comments on a new line.\n      // This handles scenarios where a block comment immediately\n      // follows a property definition on the same line or where\n      // minified code is being beautified.\n      this._output.add_new_line();\n      this._input.back();\n\n      var comment = this._input.read(block_comment_pattern);\n\n      // Handle ignore directive\n      var directives = directives_core.get_directives(comment);\n      if (directives && directives.ignore === 'start') {\n        comment += directives_core.readIgnored(this._input);\n      }\n\n      this.print_string(comment);\n\n      // Ensures any new lines following the comment are preserved\n      this.eatWhitespace(true);\n\n      // Block comments are followed by a new line so they don't\n      // share a line with other properties\n      this._output.add_new_line();\n    } else if (this._ch === '/' && this._input.peek() === '/') {\n      // // single line comment\n      // Preserves the space before a comment\n      // on the same line as a rule\n      this._output.space_before_token = true;\n      this._input.back();\n      this.print_string(this._input.read(comment_pattern));\n\n      // Ensures any new lines following the comment are preserved\n      this.eatWhitespace(true);\n    } else if (this._ch === '$') {\n      this.preserveSingleSpace(isAfterSpace);\n\n      this.print_string(this._ch);\n\n      // strip trailing space, if present, for hash property checks\n      var variable = this._input.peekUntilAfter(/[: ,;{}()[\\]\\/='\"]/g);\n\n      if (variable.match(/[ :]$/)) {\n        // we have a variable or pseudo-class, add it and insert one space before continuing\n        variable = this.eatString(\": \").replace(/\\s+$/, '');\n        this.print_string(variable);\n        this._output.space_before_token = true;\n      }\n\n      // might be sass variable\n      if (parenLevel === 0 && variable.indexOf(':') !== -1) {\n        insidePropertyValue = true;\n        this.indent();\n      }\n    } else if (this._ch === '@') {\n      this.preserveSingleSpace(isAfterSpace);\n\n      // deal with less property mixins @{...}\n      if (this._input.peek() === '{') {\n        this.print_string(this._ch + this.eatString('}'));\n      } else {\n        this.print_string(this._ch);\n\n        // strip trailing space, if present, for hash property checks\n        var variableOrRule = this._input.peekUntilAfter(/[: ,;{}()[\\]\\/='\"]/g);\n\n        if (variableOrRule.match(/[ :]$/)) {\n          // we have a variable or pseudo-class, add it and insert one space before continuing\n          variableOrRule = this.eatString(\": \").replace(/\\s+$/, '');\n          this.print_string(variableOrRule);\n          this._output.space_before_token = true;\n        }\n\n        // might be less variable\n        if (parenLevel === 0 && variableOrRule.indexOf(':') !== -1) {\n          insidePropertyValue = true;\n          this.indent();\n\n          // might be a nesting at-rule\n        } else if (variableOrRule in this.NESTED_AT_RULE) {\n          this._nestedLevel += 1;\n          if (variableOrRule in this.CONDITIONAL_GROUP_RULE) {\n            enteringConditionalGroup = true;\n          }\n\n          // might be a non-nested at-rule\n        } else if (parenLevel === 0 && !insidePropertyValue) {\n          insideNonNestedAtRule = true;\n        }\n      }\n    } else if (this._ch === '#' && this._input.peek() === '{') {\n      this.preserveSingleSpace(isAfterSpace);\n      this.print_string(this._ch + this.eatString('}'));\n    } else if (this._ch === '{') {\n      if (insidePropertyValue) {\n        insidePropertyValue = false;\n        this.outdent();\n      }\n\n      // non nested at rule becomes nested\n      insideNonNestedAtRule = false;\n\n      // when entering conditional groups, only rulesets are allowed\n      if (enteringConditionalGroup) {\n        enteringConditionalGroup = false;\n        insideRule = (this._indentLevel >= this._nestedLevel);\n      } else {\n        // otherwise, declarations are also allowed\n        insideRule = (this._indentLevel >= this._nestedLevel - 1);\n      }\n      if (this._options.newline_between_rules && insideRule) {\n        if (this._output.previous_line && this._output.previous_line.item(-1) !== '{') {\n          this._output.ensure_empty_line_above('/', ',');\n        }\n      }\n\n      this._output.space_before_token = true;\n\n      // The difference in print_string and indent order is necessary to indent the '{' correctly\n      if (this._options.brace_style === 'expand') {\n        this._output.add_new_line();\n        this.print_string(this._ch);\n        this.indent();\n        this._output.set_indent(this._indentLevel);\n      } else {\n        // inside mixin and first param is object\n        if (previous_ch === '(') {\n          this._output.space_before_token = false;\n        } else if (previous_ch !== ',') {\n          this.indent();\n        }\n        this.print_string(this._ch);\n      }\n\n      this.eatWhitespace(true);\n      this._output.add_new_line();\n    } else if (this._ch === '}') {\n      this.outdent();\n      this._output.add_new_line();\n      if (previous_ch === '{') {\n        this._output.trim(true);\n      }\n\n      if (insidePropertyValue) {\n        this.outdent();\n        insidePropertyValue = false;\n      }\n      this.print_string(this._ch);\n      insideRule = false;\n      if (this._nestedLevel) {\n        this._nestedLevel--;\n      }\n\n      this.eatWhitespace(true);\n      this._output.add_new_line();\n\n      if (this._options.newline_between_rules && !this._output.just_added_blankline()) {\n        if (this._input.peek() !== '}') {\n          this._output.add_new_line(true);\n        }\n      }\n      if (this._input.peek() === ')') {\n        this._output.trim(true);\n        if (this._options.brace_style === \"expand\") {\n          this._output.add_new_line(true);\n        }\n      }\n    } else if (this._ch === \":\") {\n\n      for (var i = 0; i < this.NON_SEMICOLON_NEWLINE_PROPERTY.length; i++) {\n        if (this._input.lookBack(this.NON_SEMICOLON_NEWLINE_PROPERTY[i])) {\n          insideNonSemiColonValues = true;\n          break;\n        }\n      }\n\n      if ((insideRule || enteringConditionalGroup) && !(this._input.lookBack(\"&\") || this.foundNestedPseudoClass()) && !this._input.lookBack(\"(\") && !insideNonNestedAtRule && parenLevel === 0) {\n        // 'property: value' delimiter\n        // which could be in a conditional group query\n\n        this.print_string(':');\n        if (!insidePropertyValue) {\n          insidePropertyValue = true;\n          this._output.space_before_token = true;\n          this.eatWhitespace(true);\n          this.indent();\n        }\n      } else {\n        // sass/less parent reference don't use a space\n        // sass nested pseudo-class don't use a space\n\n        // preserve space before pseudoclasses/pseudoelements, as it means \"in any child\"\n        if (this._input.lookBack(\" \")) {\n          this._output.space_before_token = true;\n        }\n        if (this._input.peek() === \":\") {\n          // pseudo-element\n          this._ch = this._input.next();\n          this.print_string(\"::\");\n        } else {\n          // pseudo-class\n          this.print_string(':');\n        }\n      }\n    } else if (this._ch === '\"' || this._ch === '\\'') {\n      var preserveQuoteSpace = previous_ch === '\"' || previous_ch === '\\'';\n      this.preserveSingleSpace(preserveQuoteSpace || isAfterSpace);\n      this.print_string(this._ch + this.eatString(this._ch));\n      this.eatWhitespace(true);\n    } else if (this._ch === ';') {\n      insideNonSemiColonValues = false;\n      if (parenLevel === 0) {\n        if (insidePropertyValue) {\n          this.outdent();\n          insidePropertyValue = false;\n        }\n        insideNonNestedAtRule = false;\n        this.print_string(this._ch);\n        this.eatWhitespace(true);\n\n        // This maintains single line comments on the same\n        // line. Block comments are also affected, but\n        // a new line is always output before one inside\n        // that section\n        if (this._input.peek() !== '/') {\n          this._output.add_new_line();\n        }\n      } else {\n        this.print_string(this._ch);\n        this.eatWhitespace(true);\n        this._output.space_before_token = true;\n      }\n    } else if (this._ch === '(') { // may be a url\n      if (this._input.lookBack(\"url\")) {\n        this.print_string(this._ch);\n        this.eatWhitespace();\n        parenLevel++;\n        this.indent();\n        this._ch = this._input.next();\n        if (this._ch === ')' || this._ch === '\"' || this._ch === '\\'') {\n          this._input.back();\n        } else if (this._ch) {\n          this.print_string(this._ch + this.eatString(')'));\n          if (parenLevel) {\n            parenLevel--;\n            this.outdent();\n          }\n        }\n      } else {\n        var space_needed = false;\n        if (this._input.lookBack(\"with\")) {\n          // look back is not an accurate solution, we need tokens to confirm without whitespaces\n          space_needed = true;\n        }\n        this.preserveSingleSpace(isAfterSpace || space_needed);\n        this.print_string(this._ch);\n\n        // handle scss/sass map\n        if (insidePropertyValue && previous_ch === \"$\" && this._options.selector_separator_newline) {\n          this._output.add_new_line();\n          insideScssMap = true;\n        } else {\n          this.eatWhitespace();\n          parenLevel++;\n          this.indent();\n        }\n      }\n    } else if (this._ch === ')') {\n      if (parenLevel) {\n        parenLevel--;\n        this.outdent();\n      }\n      if (insideScssMap && this._input.peek() === \";\" && this._options.selector_separator_newline) {\n        insideScssMap = false;\n        this.outdent();\n        this._output.add_new_line();\n      }\n      this.print_string(this._ch);\n    } else if (this._ch === ',') {\n      this.print_string(this._ch);\n      this.eatWhitespace(true);\n      if (this._options.selector_separator_newline && (!insidePropertyValue || insideScssMap) && parenLevel === 0 && !insideNonNestedAtRule) {\n        this._output.add_new_line();\n      } else {\n        this._output.space_before_token = true;\n      }\n    } else if ((this._ch === '>' || this._ch === '+' || this._ch === '~') && !insidePropertyValue && parenLevel === 0) {\n      //handle combinator spacing\n      if (this._options.space_around_combinator) {\n        this._output.space_before_token = true;\n        this.print_string(this._ch);\n        this._output.space_before_token = true;\n      } else {\n        this.print_string(this._ch);\n        this.eatWhitespace();\n        // squash extra whitespace\n        if (this._ch && whitespaceChar.test(this._ch)) {\n          this._ch = '';\n        }\n      }\n    } else if (this._ch === ']') {\n      this.print_string(this._ch);\n    } else if (this._ch === '[') {\n      this.preserveSingleSpace(isAfterSpace);\n      this.print_string(this._ch);\n    } else if (this._ch === '=') { // no whitespace before or after\n      this.eatWhitespace();\n      this.print_string('=');\n      if (whitespaceChar.test(this._ch)) {\n        this._ch = '';\n      }\n    } else if (this._ch === '!' && !this._input.lookBack(\"\\\\\")) { // !important\n      this._output.space_before_token = true;\n      this.print_string(this._ch);\n    } else {\n      var preserveAfterSpace = previous_ch === '\"' || previous_ch === '\\'';\n      this.preserveSingleSpace(preserveAfterSpace || isAfterSpace);\n      this.print_string(this._ch);\n\n      if (!this._output.just_added_newline() && this._input.peek() === '\\n' && insideNonSemiColonValues) {\n        this._output.add_new_line();\n      }\n    }\n  }\n\n  var sweetCode = this._output.get_code(eol);\n\n  return sweetCode;\n};\n\nmodule.exports.Beautifier = Beautifier;\n\n\n/***/ }),\n/* 17 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar BaseOptions = (__webpack_require__(6).Options);\n\nfunction Options(options) {\n  BaseOptions.call(this, options, 'css');\n\n  this.selector_separator_newline = this._get_boolean('selector_separator_newline', true);\n  this.newline_between_rules = this._get_boolean('newline_between_rules', true);\n  var space_around_selector_separator = this._get_boolean('space_around_selector_separator');\n  this.space_around_combinator = this._get_boolean('space_around_combinator') || space_around_selector_separator;\n\n  var brace_style_split = this._get_selection_list('brace_style', ['collapse', 'expand', 'end-expand', 'none', 'preserve-inline']);\n  this.brace_style = 'collapse';\n  for (var bs = 0; bs < brace_style_split.length; bs++) {\n    if (brace_style_split[bs] !== 'expand') {\n      // default to collapse, as only collapse|expand is implemented for now\n      this.brace_style = 'collapse';\n    } else {\n      this.brace_style = brace_style_split[bs];\n    }\n  }\n}\nOptions.prototype = new BaseOptions();\n\n\n\nmodule.exports.Options = Options;\n\n\n/***/ })\n/******/ \t]);\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t\n/******/ \t// startup\n/******/ \t// Load entry module and return exports\n/******/ \t// This entry module is referenced by other modules so it can't be inlined\n/******/ \tvar __webpack_exports__ = __webpack_require__(15);\n/******/ \tlegacy_beautify_css = __webpack_exports__;\n/******/ \t\n/******/ })()\n;\nvar css_beautify = legacy_beautify_css;\n/* Footer */\nif (typeof define === \"function\" && define.amd) {\n    // Add support for AMD ( https://github.com/amdjs/amdjs-api/wiki/AMD#defineamd-property- )\n    define([], function() {\n        return {\n            css_beautify: css_beautify\n        };\n    });\n} else if (typeof exports !== \"undefined\") {\n    // Add support for CommonJS. Just put this file somewhere on your require.paths\n    // and you will be able to `var html_beautify = require(\"beautify\").html_beautify`.\n    exports.css_beautify = css_beautify;\n} else if (typeof window !== \"undefined\") {\n    // If we're running a web page and don't have either of the above, add our one global\n    window.css_beautify = css_beautify;\n} else if (typeof global !== \"undefined\") {\n    // If we don't even have window, try global.\n    global.css_beautify = css_beautify;\n}\n\n}());\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEC,aAAW;EAEZ;EACA,IAAIA,mBAAmB;EACvB;EAAS,CAAC,YAAW;IAAE;IACvB;IAAU,YAAY;;IACtB;IAAU,IAAIC,mBAAmB,GAAI;MACrC;MACA;;MA0aA;MACA;MACA;;MAwMA;;MAuMA;MACA;MACA;MACA;;MAqEA;IAAA,IAl4BA;IACA,KAAO,UAASC,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,SAASC,UAAUA,CAACC,MAAM,EAAE;QAC1B,IAAI,CAACC,QAAQ,GAAGD,MAAM;QACtB,IAAI,CAACE,iBAAiB,GAAG,CAAC;QAC1B;QACA,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAACC,iBAAiB,GAAG,CAAC;QAC1B,IAAI,CAACC,kBAAkB,GAAG,CAAC;QAC3B,IAAI,CAACC,4BAA4B,GAAG,CAAC;QACrC,IAAI,CAACC,yBAAyB,GAAG,CAAC,CAAC;QACnC,IAAI,CAACC,4BAA4B,GAAG,CAAC;QAErC,IAAI,CAACC,OAAO,GAAG,EAAE;MACnB;MAEAV,UAAU,CAACW,SAAS,CAACC,WAAW,GAAG,YAAW;QAC5C,IAAIC,IAAI,GAAG,IAAIb,UAAU,CAAC,IAAI,CAACE,QAAQ,CAAC;QACxCW,IAAI,CAACC,UAAU,CAAC,IAAI,CAACV,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC;QAC5D,OAAOQ,IAAI;MACb,CAAC;MAEDb,UAAU,CAACW,SAAS,CAACI,IAAI,GAAG,UAASC,KAAK,EAAE;QAC1C,IAAIA,KAAK,GAAG,CAAC,EAAE;UACb,OAAO,IAAI,CAACN,OAAO,CAAC,IAAI,CAACA,OAAO,CAACO,MAAM,GAAGD,KAAK,CAAC;QAClD,CAAC,MAAM;UACL,OAAO,IAAI,CAACN,OAAO,CAACM,KAAK,CAAC;QAC5B;MACF,CAAC;MAEDhB,UAAU,CAACW,SAAS,CAACO,SAAS,GAAG,UAASC,OAAO,EAAE;QACjD,KAAK,IAAIC,iBAAiB,GAAG,IAAI,CAACV,OAAO,CAACO,MAAM,GAAG,CAAC,EAAEG,iBAAiB,IAAI,CAAC,EAAEA,iBAAiB,EAAE,EAAE;UACjG,IAAI,IAAI,CAACV,OAAO,CAACU,iBAAiB,CAAC,CAACC,KAAK,CAACF,OAAO,CAAC,EAAE;YAClD,OAAO,IAAI;UACb;QACF;QACA,OAAO,KAAK;MACd,CAAC;MAEDnB,UAAU,CAACW,SAAS,CAACG,UAAU,GAAG,UAASQ,MAAM,EAAEC,SAAS,EAAE;QAC5D,IAAI,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;UACnB,IAAI,CAACpB,cAAc,GAAGkB,MAAM,IAAI,CAAC;UACjC,IAAI,CAACjB,iBAAiB,GAAGkB,SAAS,IAAI,CAAC;UACvC,IAAI,CAACpB,iBAAiB,GAAG,IAAI,CAACD,QAAQ,CAACuB,eAAe,CAAC,IAAI,CAACrB,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC;QACrG;MACF,CAAC;MAEDL,UAAU,CAACW,SAAS,CAACe,eAAe,GAAG,YAAW;QAChD,IAAI,IAAI,CAACxB,QAAQ,CAACyB,gBAAgB,EAAE;UAClC,IAAI,CAACrB,kBAAkB,GAAG,IAAI,CAACI,OAAO,CAACO,MAAM;UAC7C,IAAI,CAACV,4BAA4B,GAAG,IAAI,CAACJ,iBAAiB;UAC1D,IAAI,CAACK,yBAAyB,GAAG,IAAI,CAACN,QAAQ,CAAC0B,SAAS,CAACxB,cAAc;UACvE,IAAI,CAACK,4BAA4B,GAAG,IAAI,CAACP,QAAQ,CAAC0B,SAAS,CAACvB,iBAAiB;QAC/E;MACF,CAAC;MAEDL,UAAU,CAACW,SAAS,CAACkB,YAAY,GAAG,YAAW;QAC7C,OAAO,IAAI,CAACvB,kBAAkB,IAC5B,IAAI,CAACH,iBAAiB,GAAG,IAAI,CAACD,QAAQ,CAACyB,gBAAgB,IACvD,IAAI,CAACpB,4BAA4B,GAAG,IAAI,CAACL,QAAQ,CAAC0B,SAAS,CAACzB,iBAAiB;MACjF,CAAC;MAEDH,UAAU,CAACW,SAAS,CAACmB,WAAW,GAAG,YAAW;QAC5C,IAAI,IAAI,CAACD,YAAY,CAAC,CAAC,EAAE;UACvB,IAAI,CAAC3B,QAAQ,CAAC6B,YAAY,CAAC,CAAC;UAC5B,IAAIC,IAAI,GAAG,IAAI,CAAC9B,QAAQ,CAAC+B,YAAY;UACrCD,IAAI,CAAClB,UAAU,CAAC,IAAI,CAACN,yBAAyB,EAAE,IAAI,CAACC,4BAA4B,CAAC;UAClFuB,IAAI,CAACtB,OAAO,GAAG,IAAI,CAACA,OAAO,CAACwB,KAAK,CAAC,IAAI,CAAC5B,kBAAkB,CAAC;UAC1D,IAAI,CAACI,OAAO,GAAG,IAAI,CAACA,OAAO,CAACwB,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC5B,kBAAkB,CAAC;UAE7D0B,IAAI,CAAC7B,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACI,4BAA4B;UACpF,IAAI,CAACJ,iBAAiB,GAAG,IAAI,CAACI,4BAA4B;UAE1D,IAAIyB,IAAI,CAACtB,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAC3BsB,IAAI,CAACtB,OAAO,CAACyB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;YACzBH,IAAI,CAAC7B,iBAAiB,IAAI,CAAC;UAC7B;UACA,OAAO,IAAI;QACb;QACA,OAAO,KAAK;MACd,CAAC;MAEDH,UAAU,CAACW,SAAS,CAACa,QAAQ,GAAG,YAAW;QACzC,OAAO,IAAI,CAACd,OAAO,CAACO,MAAM,KAAK,CAAC;MAClC,CAAC;MAEDjB,UAAU,CAACW,SAAS,CAACyB,IAAI,GAAG,YAAW;QACrC,IAAI,CAAC,IAAI,CAACZ,QAAQ,CAAC,CAAC,EAAE;UACpB,OAAO,IAAI,CAACd,OAAO,CAAC,IAAI,CAACA,OAAO,CAACO,MAAM,GAAG,CAAC,CAAC;QAC9C,CAAC,MAAM;UACL,OAAO,IAAI;QACb;MACF,CAAC;MAEDjB,UAAU,CAACW,SAAS,CAAC0B,IAAI,GAAG,UAAStB,IAAI,EAAE;QACzC,IAAI,CAACL,OAAO,CAAC2B,IAAI,CAACtB,IAAI,CAAC;QACvB,IAAIuB,kBAAkB,GAAGvB,IAAI,CAACwB,WAAW,CAAC,IAAI,CAAC;QAC/C,IAAID,kBAAkB,KAAK,CAAC,CAAC,EAAE;UAC7B,IAAI,CAACnC,iBAAiB,GAAGY,IAAI,CAACE,MAAM,GAAGqB,kBAAkB;QAC3D,CAAC,MAAM;UACL,IAAI,CAACnC,iBAAiB,IAAIY,IAAI,CAACE,MAAM;QACvC;MACF,CAAC;MAEDjB,UAAU,CAACW,SAAS,CAAC6B,GAAG,GAAG,YAAW;QACpC,IAAIzB,IAAI,GAAG,IAAI;QACf,IAAI,CAAC,IAAI,CAACS,QAAQ,CAAC,CAAC,EAAE;UACpBT,IAAI,GAAG,IAAI,CAACL,OAAO,CAAC8B,GAAG,CAAC,CAAC;UACzB,IAAI,CAACrC,iBAAiB,IAAIY,IAAI,CAACE,MAAM;QACvC;QACA,OAAOF,IAAI;MACb,CAAC;MAGDf,UAAU,CAACW,SAAS,CAAC8B,cAAc,GAAG,YAAW;QAC/C,IAAI,IAAI,CAACrC,cAAc,GAAG,CAAC,EAAE;UAC3B,IAAI,CAACA,cAAc,IAAI,CAAC;UACxB,IAAI,CAACD,iBAAiB,IAAI,IAAI,CAACD,QAAQ,CAACwC,WAAW;QACrD;MACF,CAAC;MAED1C,UAAU,CAACW,SAAS,CAACgC,mBAAmB,GAAG,YAAW;QACpD,IAAI,IAAI,CAACnC,yBAAyB,GAAG,CAAC,EAAE;UACtC,IAAI,CAACA,yBAAyB,IAAI,CAAC;QACrC;MACF,CAAC;MACDR,UAAU,CAACW,SAAS,CAACiC,IAAI,GAAG,YAAW;QACrC,OAAO,IAAI,CAACR,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;UAC1B,IAAI,CAAC1B,OAAO,CAAC8B,GAAG,CAAC,CAAC;UAClB,IAAI,CAACrC,iBAAiB,IAAI,CAAC;QAC7B;MACF,CAAC;MAEDH,UAAU,CAACW,SAAS,CAACkC,QAAQ,GAAG,YAAW;QACzC,IAAIC,MAAM,GAAG,EAAE;QACf,IAAI,IAAI,CAACtB,QAAQ,CAAC,CAAC,EAAE;UACnB,IAAI,IAAI,CAACtB,QAAQ,CAAC6C,kBAAkB,EAAE;YACpCD,MAAM,GAAG,IAAI,CAAC5C,QAAQ,CAAC8C,iBAAiB,CAAC,IAAI,CAAC5C,cAAc,CAAC;UAC/D;QACF,CAAC,MAAM;UACL0C,MAAM,GAAG,IAAI,CAAC5C,QAAQ,CAAC8C,iBAAiB,CAAC,IAAI,CAAC5C,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC;UACrFyC,MAAM,IAAI,IAAI,CAACpC,OAAO,CAACuC,IAAI,CAAC,EAAE,CAAC;QACjC;QACA,OAAOH,MAAM;MACf,CAAC;MAED,SAASI,iBAAiBA,CAACC,OAAO,EAAEC,gBAAgB,EAAE;QACpD,IAAI,CAACC,OAAO,GAAG,CAAC,EAAE,CAAC;QACnB,IAAI,CAACC,aAAa,GAAGH,OAAO,CAACT,WAAW;QACxC,IAAI,CAACa,eAAe,GAAGJ,OAAO,CAACK,WAAW;QAC1C,IAAI,CAACL,OAAO,CAACM,gBAAgB,EAAE;UAC7B,IAAI,CAACF,eAAe,GAAG,IAAIG,KAAK,CAACP,OAAO,CAACT,WAAW,GAAG,CAAC,CAAC,CAACO,IAAI,CAACE,OAAO,CAACK,WAAW,CAAC;QACrF;;QAEA;QACAJ,gBAAgB,GAAGA,gBAAgB,IAAI,EAAE;QACzC,IAAID,OAAO,CAACQ,YAAY,GAAG,CAAC,EAAE;UAC5BP,gBAAgB,GAAG,IAAIM,KAAK,CAACP,OAAO,CAACQ,YAAY,GAAG,CAAC,CAAC,CAACV,IAAI,CAAC,IAAI,CAACM,eAAe,CAAC;QACnF;QAEA,IAAI,CAACK,aAAa,GAAGR,gBAAgB;QACrC,IAAI,CAACS,oBAAoB,GAAGT,gBAAgB,CAACnC,MAAM;MACrD;MAEAiC,iBAAiB,CAACvC,SAAS,CAACc,eAAe,GAAG,UAASH,MAAM,EAAEwC,MAAM,EAAE;QACrE,IAAIhB,MAAM,GAAG,IAAI,CAACe,oBAAoB;QACtCC,MAAM,GAAGA,MAAM,IAAI,CAAC;QACpB,IAAIxC,MAAM,GAAG,CAAC,EAAE;UACdwB,MAAM,GAAG,CAAC;QACZ;QACAA,MAAM,IAAIxB,MAAM,GAAG,IAAI,CAACgC,aAAa;QACrCR,MAAM,IAAIgB,MAAM;QAChB,OAAOhB,MAAM;MACf,CAAC;MAEDI,iBAAiB,CAACvC,SAAS,CAACqC,iBAAiB,GAAG,UAASW,YAAY,EAAEG,MAAM,EAAE;QAC7E,IAAIhB,MAAM,GAAG,IAAI,CAACc,aAAa;QAC/BE,MAAM,GAAGA,MAAM,IAAI,CAAC;QACpB,IAAIH,YAAY,GAAG,CAAC,EAAE;UACpBA,YAAY,GAAG,CAAC;UAChBb,MAAM,GAAG,EAAE;QACb;QACAgB,MAAM,IAAIH,YAAY,GAAG,IAAI,CAACL,aAAa;QAC3C,IAAI,CAACS,cAAc,CAACD,MAAM,CAAC;QAC3BhB,MAAM,IAAI,IAAI,CAACO,OAAO,CAACS,MAAM,CAAC;QAC9B,OAAOhB,MAAM;MACf,CAAC;MAEDI,iBAAiB,CAACvC,SAAS,CAACoD,cAAc,GAAG,UAASD,MAAM,EAAE;QAC5D,OAAOA,MAAM,IAAI,IAAI,CAACT,OAAO,CAACpC,MAAM,EAAE;UACpC,IAAI,CAAC+C,YAAY,CAAC,CAAC;QACrB;MACF,CAAC;MAEDd,iBAAiB,CAACvC,SAAS,CAACqD,YAAY,GAAG,YAAW;QACpD,IAAIF,MAAM,GAAG,IAAI,CAACT,OAAO,CAACpC,MAAM;QAChC,IAAIK,MAAM,GAAG,CAAC;QACd,IAAIwB,MAAM,GAAG,EAAE;QACf,IAAI,IAAI,CAACQ,aAAa,IAAIQ,MAAM,IAAI,IAAI,CAACR,aAAa,EAAE;UACtDhC,MAAM,GAAG2C,IAAI,CAACC,KAAK,CAACJ,MAAM,GAAG,IAAI,CAACR,aAAa,CAAC;UAChDQ,MAAM,IAAIxC,MAAM,GAAG,IAAI,CAACgC,aAAa;UACrCR,MAAM,GAAG,IAAIY,KAAK,CAACpC,MAAM,GAAG,CAAC,CAAC,CAAC2B,IAAI,CAAC,IAAI,CAACM,eAAe,CAAC;QAC3D;QACA,IAAIO,MAAM,EAAE;UACVhB,MAAM,IAAI,IAAIY,KAAK,CAACI,MAAM,GAAG,CAAC,CAAC,CAACb,IAAI,CAAC,GAAG,CAAC;QAC3C;QAEA,IAAI,CAACI,OAAO,CAAChB,IAAI,CAACS,MAAM,CAAC;MAC3B,CAAC;MAED,SAASqB,MAAMA,CAAChB,OAAO,EAAEC,gBAAgB,EAAE;QACzC,IAAI,CAACgB,cAAc,GAAG,IAAIlB,iBAAiB,CAACC,OAAO,EAAEC,gBAAgB,CAAC;QACtE,IAAI,CAACiB,GAAG,GAAG,KAAK;QAChB,IAAI,CAACC,iBAAiB,GAAGnB,OAAO,CAACoB,gBAAgB;QACjD,IAAI,CAAC7B,WAAW,GAAGS,OAAO,CAACT,WAAW;QACtC,IAAI,CAACf,gBAAgB,GAAGwB,OAAO,CAACxB,gBAAgB;QAChD,IAAI,CAACoB,kBAAkB,GAAGI,OAAO,CAACJ,kBAAkB;QACpD,IAAI,CAACyB,OAAO,GAAG,EAAE;QACjB,IAAI,CAACC,aAAa,GAAG,IAAI;QACzB,IAAI,CAACxC,YAAY,GAAG,IAAI;QACxB,IAAI,CAACL,SAAS,GAAG,IAAI5B,UAAU,CAAC,IAAI,CAAC;QACrC,IAAI,CAAC0E,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACC,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACC,sBAAsB,GAAG,KAAK;QACnC;QACA,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACzB;MAEAV,MAAM,CAACxD,SAAS,CAACkE,gBAAgB,GAAG,YAAW;QAC7C,IAAI,CAACJ,aAAa,GAAG,IAAI,CAACxC,YAAY;QACtC,IAAI,CAACA,YAAY,GAAG,IAAI,CAACL,SAAS,CAAChB,WAAW,CAAC,CAAC;QAChD,IAAI,CAAC4D,OAAO,CAACnC,IAAI,CAAC,IAAI,CAACJ,YAAY,CAAC;MACtC,CAAC;MAEDkC,MAAM,CAACxD,SAAS,CAACmE,eAAe,GAAG,YAAW;QAC5C,OAAO,IAAI,CAACN,OAAO,CAACvD,MAAM;MAC5B,CAAC;MAEDkD,MAAM,CAACxD,SAAS,CAACqC,iBAAiB,GAAG,UAAS1B,MAAM,EAAEwC,MAAM,EAAE;QAC5D,OAAO,IAAI,CAACM,cAAc,CAACpB,iBAAiB,CAAC1B,MAAM,EAAEwC,MAAM,CAAC;MAC9D,CAAC;MAEDK,MAAM,CAACxD,SAAS,CAACc,eAAe,GAAG,UAASH,MAAM,EAAEwC,MAAM,EAAE;QAC1D,OAAO,IAAI,CAACM,cAAc,CAAC3C,eAAe,CAACH,MAAM,EAAEwC,MAAM,CAAC;MAC5D,CAAC;MAEDK,MAAM,CAACxD,SAAS,CAACa,QAAQ,GAAG,YAAW;QACrC,OAAO,CAAC,IAAI,CAACiD,aAAa,IAAI,IAAI,CAACxC,YAAY,CAACT,QAAQ,CAAC,CAAC;MAC5D,CAAC;MAED2C,MAAM,CAACxD,SAAS,CAACoB,YAAY,GAAG,UAASgD,aAAa,EAAE;QACtD;QACA;QACA,IAAI,IAAI,CAACvD,QAAQ,CAAC,CAAC,IAChB,CAACuD,aAAa,IAAI,IAAI,CAACC,kBAAkB,CAAC,CAAE,EAAE;UAC/C,OAAO,KAAK;QACd;;QAEA;QACA;QACA,IAAI,CAAC,IAAI,CAACX,GAAG,EAAE;UACb,IAAI,CAACQ,gBAAgB,CAAC,CAAC;QACzB;QACA,OAAO,IAAI;MACb,CAAC;MAEDV,MAAM,CAACxD,SAAS,CAACsE,QAAQ,GAAG,UAASC,GAAG,EAAE;QACxC,IAAI,CAACtC,IAAI,CAAC,IAAI,CAAC;;QAEf;QACA;QACA,IAAIuC,SAAS,GAAG,IAAI,CAAClD,YAAY,CAACO,GAAG,CAAC,CAAC;QACvC,IAAI2C,SAAS,EAAE;UACb,IAAIA,SAAS,CAACA,SAAS,CAAClE,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;YAC5CkE,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;UAC5C;UACA,IAAI,CAACnD,YAAY,CAACI,IAAI,CAAC8C,SAAS,CAAC;QACnC;QAEA,IAAI,IAAI,CAACb,iBAAiB,EAAE;UAC1B,IAAI,CAACO,gBAAgB,CAAC,CAAC;QACzB;QAEA,IAAIQ,UAAU,GAAG,IAAI,CAACb,OAAO,CAACvB,IAAI,CAAC,IAAI,CAAC;QAExC,IAAIiC,GAAG,KAAK,IAAI,EAAE;UAChBG,UAAU,GAAGA,UAAU,CAACD,OAAO,CAAC,OAAO,EAAEF,GAAG,CAAC;QAC/C;QACA,OAAOG,UAAU;MACnB,CAAC;MAEDlB,MAAM,CAACxD,SAAS,CAAC2E,cAAc,GAAG,YAAW;QAC3C,IAAI,CAACrD,YAAY,CAACP,eAAe,CAAC,CAAC;MACrC,CAAC;MAEDyC,MAAM,CAACxD,SAAS,CAACG,UAAU,GAAG,UAASQ,MAAM,EAAEC,SAAS,EAAE;QACxDD,MAAM,GAAGA,MAAM,IAAI,CAAC;QACpBC,SAAS,GAAGA,SAAS,IAAI,CAAC;;QAE1B;QACA,IAAI,CAACK,SAAS,CAACd,UAAU,CAACQ,MAAM,EAAEC,SAAS,CAAC;;QAE5C;QACA,IAAI,IAAI,CAACiD,OAAO,CAACvD,MAAM,GAAG,CAAC,EAAE;UAC3B,IAAI,CAACgB,YAAY,CAACnB,UAAU,CAACQ,MAAM,EAAEC,SAAS,CAAC;UAC/C,OAAO,IAAI;QACb;QAEA,IAAI,CAACU,YAAY,CAACnB,UAAU,CAAC,CAAC;QAC9B,OAAO,KAAK;MACd,CAAC;MAEDqD,MAAM,CAACxD,SAAS,CAAC4E,aAAa,GAAG,UAASC,KAAK,EAAE;QAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,QAAQ,EAAED,CAAC,EAAE,EAAE;UACvC,IAAI,CAACZ,gBAAgB,CAAC,CAAC;QACzB;QACA,IAAI,CAAC5C,YAAY,CAACnB,UAAU,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAACmB,YAAY,CAACI,IAAI,CAACmD,KAAK,CAACG,iBAAiB,CAAC;QAC/C,IAAI,CAAC1D,YAAY,CAACI,IAAI,CAACmD,KAAK,CAACI,IAAI,CAAC;QAClC,IAAI,CAAClB,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACC,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACC,sBAAsB,GAAG,KAAK;MACrC,CAAC;MAEDT,MAAM,CAACxD,SAAS,CAACkF,SAAS,GAAG,UAASC,eAAe,EAAE;QACrD,IAAI,CAACC,wBAAwB,CAAC,CAAC;QAC/B,IAAI,CAAC9D,YAAY,CAACI,IAAI,CAACyD,eAAe,CAAC;QACvC,IAAI,CAACpB,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACC,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAAC3C,YAAY,CAACH,WAAW,CAAC,CAAC;MAC/D,CAAC;MAEDqC,MAAM,CAACxD,SAAS,CAACoF,wBAAwB,GAAG,YAAW;QACrD,IAAI,IAAI,CAACrB,kBAAkB,IAAI,CAAC,IAAI,CAACM,kBAAkB,CAAC,CAAC,EAAE;UACzD,IAAI,CAAC,IAAI,CAACL,kBAAkB,EAAE;YAC5B,IAAI,CAACW,cAAc,CAAC,CAAC;UACvB;UACA,IAAI,CAACrD,YAAY,CAACI,IAAI,CAAC,GAAG,CAAC;QAC7B;MACF,CAAC;MAED8B,MAAM,CAACxD,SAAS,CAACqF,aAAa,GAAG,UAAShF,KAAK,EAAE;QAC/C,IAAIiF,aAAa,GAAG,IAAI,CAACzB,OAAO,CAACvD,MAAM;QACvC,OAAOD,KAAK,GAAGiF,aAAa,EAAE;UAC5B,IAAI,CAACzB,OAAO,CAACxD,KAAK,CAAC,CAACyB,cAAc,CAAC,CAAC;UACpCzB,KAAK,EAAE;QACT;QACA,IAAI,CAACiB,YAAY,CAACU,mBAAmB,CAAC,CAAC;MACzC,CAAC;MAEDwB,MAAM,CAACxD,SAAS,CAACiC,IAAI,GAAG,UAASsD,YAAY,EAAE;QAC7CA,YAAY,GAAIA,YAAY,KAAKC,SAAS,GAAI,KAAK,GAAGD,YAAY;QAElE,IAAI,CAACjE,YAAY,CAACW,IAAI,CAAC,CAAC;QAExB,OAAOsD,YAAY,IAAI,IAAI,CAAC1B,OAAO,CAACvD,MAAM,GAAG,CAAC,IAC5C,IAAI,CAACgB,YAAY,CAACT,QAAQ,CAAC,CAAC,EAAE;UAC9B,IAAI,CAACgD,OAAO,CAAChC,GAAG,CAAC,CAAC;UAClB,IAAI,CAACP,YAAY,GAAG,IAAI,CAACuC,OAAO,CAAC,IAAI,CAACA,OAAO,CAACvD,MAAM,GAAG,CAAC,CAAC;UACzD,IAAI,CAACgB,YAAY,CAACW,IAAI,CAAC,CAAC;QAC1B;QAEA,IAAI,CAAC6B,aAAa,GAAG,IAAI,CAACD,OAAO,CAACvD,MAAM,GAAG,CAAC,GAC1C,IAAI,CAACuD,OAAO,CAAC,IAAI,CAACA,OAAO,CAACvD,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;MAChD,CAAC;MAEDkD,MAAM,CAACxD,SAAS,CAACqE,kBAAkB,GAAG,YAAW;QAC/C,OAAO,IAAI,CAAC/C,YAAY,CAACT,QAAQ,CAAC,CAAC;MACrC,CAAC;MAED2C,MAAM,CAACxD,SAAS,CAACyF,oBAAoB,GAAG,YAAW;QACjD,OAAO,IAAI,CAAC5E,QAAQ,CAAC,CAAC,IACnB,IAAI,CAACS,YAAY,CAACT,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACiD,aAAa,CAACjD,QAAQ,CAAC,CAAE;MACnE,CAAC;MAED2C,MAAM,CAACxD,SAAS,CAAC0F,uBAAuB,GAAG,UAASC,WAAW,EAAEC,SAAS,EAAE;QAC1E,IAAIvF,KAAK,GAAG,IAAI,CAACwD,OAAO,CAACvD,MAAM,GAAG,CAAC;QACnC,OAAOD,KAAK,IAAI,CAAC,EAAE;UACjB,IAAIwF,kBAAkB,GAAG,IAAI,CAAChC,OAAO,CAACxD,KAAK,CAAC;UAC5C,IAAIwF,kBAAkB,CAAChF,QAAQ,CAAC,CAAC,EAAE;YACjC;UACF,CAAC,MAAM,IAAIgF,kBAAkB,CAACzF,IAAI,CAAC,CAAC,CAAC,CAAC0F,OAAO,CAACH,WAAW,CAAC,KAAK,CAAC,IAC9DE,kBAAkB,CAACzF,IAAI,CAAC,CAAC,CAAC,CAAC,KAAKwF,SAAS,EAAE;YAC3C,IAAI,CAAC/B,OAAO,CAACrC,MAAM,CAACnB,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,IAAIhB,UAAU,CAAC,IAAI,CAAC,CAAC;YACvD,IAAI,CAACyE,aAAa,GAAG,IAAI,CAACD,OAAO,CAAC,IAAI,CAACA,OAAO,CAACvD,MAAM,GAAG,CAAC,CAAC;YAC1D;UACF;UACAD,KAAK,EAAE;QACT;MACF,CAAC;MAEDjB,MAAM,CAAC2G,OAAO,CAACvC,MAAM,GAAGA,MAAM;;MAG9B;IAAM,CAAC,OAIP;IACA,KAAO,UAASpE,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,SAAS4G,OAAOA,CAACxD,OAAO,EAAEyD,iBAAiB,EAAE;QAC3C,IAAI,CAACC,WAAW,GAAGC,UAAU,CAAC3D,OAAO,EAAEyD,iBAAiB,CAAC;;QAEzD;QACA,IAAI,CAACG,QAAQ,GAAG,IAAI,CAACC,YAAY,CAAC,UAAU,CAAC;QAE7C,IAAI,CAAC9B,GAAG,GAAG,IAAI,CAAC+B,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC;QAC9C,IAAI,CAAC1C,gBAAgB,GAAG,IAAI,CAACyC,YAAY,CAAC,kBAAkB,CAAC;QAC7D,IAAI,CAACtE,WAAW,GAAG,IAAI,CAACwE,WAAW,CAAC,aAAa,EAAE,CAAC,CAAC;QACrD,IAAI,CAAC1D,WAAW,GAAG,IAAI,CAACyD,eAAe,CAAC,aAAa,EAAE,GAAG,CAAC;QAC3D,IAAI,CAACtD,YAAY,GAAG,IAAI,CAACuD,WAAW,CAAC,cAAc,CAAC;QAEpD,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACH,YAAY,CAAC,mBAAmB,EAAE,IAAI,CAAC;QACrE,IAAI,CAACI,qBAAqB,GAAG,IAAI,CAACF,WAAW,CAAC,uBAAuB,EAAE,KAAK,CAAC;QAC7E,IAAI,CAAC,IAAI,CAACC,iBAAiB,EAAE;UAC3B,IAAI,CAACC,qBAAqB,GAAG,CAAC;QAChC;QAEA,IAAI,CAAC3D,gBAAgB,GAAG,IAAI,CAACuD,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAACxD,WAAW,KAAK,IAAI,CAAC;QACxF,IAAI,IAAI,CAACC,gBAAgB,EAAE;UACzB,IAAI,CAACD,WAAW,GAAG,IAAI;;UAEvB;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,IAAI,CAACd,WAAW,KAAK,CAAC,EAAE;YAC1B,IAAI,CAACA,WAAW,GAAG,CAAC;UACtB;QACF;;QAEA;QACA,IAAI,CAACf,gBAAgB,GAAG,IAAI,CAACuF,WAAW,CAAC,kBAAkB,EAAE,IAAI,CAACA,WAAW,CAAC,UAAU,CAAC,CAAC;QAE1F,IAAI,CAACnE,kBAAkB,GAAG,IAAI,CAACiE,YAAY,CAAC,oBAAoB,CAAC;;QAEjE;QACA;QACA;QACA,IAAI,CAACK,UAAU,GAAG,IAAI,CAACC,mBAAmB,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;MACjJ;MAEAX,OAAO,CAAChG,SAAS,CAAC4G,UAAU,GAAG,UAASC,IAAI,EAAEC,aAAa,EAAE;QAC3D,IAAIC,YAAY,GAAG,IAAI,CAACb,WAAW,CAACW,IAAI,CAAC;QACzC,IAAI1E,MAAM,GAAG2E,aAAa,IAAI,EAAE;QAChC,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;UACpC,IAAIA,YAAY,KAAK,IAAI,IAAI,OAAOA,YAAY,CAACC,MAAM,KAAK,UAAU,EAAE;YACtE7E,MAAM,GAAG4E,YAAY,CAACC,MAAM,CAAC,CAAC;UAChC;QACF,CAAC,MAAM,IAAI,OAAOD,YAAY,KAAK,QAAQ,EAAE;UAC3C5E,MAAM,GAAG4E,YAAY,CAACE,KAAK,CAAC,oBAAoB,CAAC;QACnD;QACA,OAAO9E,MAAM;MACf,CAAC;MAED6D,OAAO,CAAChG,SAAS,CAACqG,YAAY,GAAG,UAASQ,IAAI,EAAEC,aAAa,EAAE;QAC7D,IAAIC,YAAY,GAAG,IAAI,CAACb,WAAW,CAACW,IAAI,CAAC;QACzC,IAAI1E,MAAM,GAAG4E,YAAY,KAAKvB,SAAS,GAAG,CAAC,CAACsB,aAAa,GAAG,CAAC,CAACC,YAAY;QAC1E,OAAO5E,MAAM;MACf,CAAC;MAED6D,OAAO,CAAChG,SAAS,CAACsG,eAAe,GAAG,UAASO,IAAI,EAAEC,aAAa,EAAE;QAChE,IAAIC,YAAY,GAAG,IAAI,CAACb,WAAW,CAACW,IAAI,CAAC;QACzC,IAAI1E,MAAM,GAAG2E,aAAa,IAAI,EAAE;QAChC,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;UACpC5E,MAAM,GAAG4E,YAAY,CAACtC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;QACtF;QACA,OAAOtC,MAAM;MACf,CAAC;MAED6D,OAAO,CAAChG,SAAS,CAACuG,WAAW,GAAG,UAASM,IAAI,EAAEC,aAAa,EAAE;QAC5D,IAAIC,YAAY,GAAG,IAAI,CAACb,WAAW,CAACW,IAAI,CAAC;QACzCC,aAAa,GAAGI,QAAQ,CAACJ,aAAa,EAAE,EAAE,CAAC;QAC3C,IAAIK,KAAK,CAACL,aAAa,CAAC,EAAE;UACxBA,aAAa,GAAG,CAAC;QACnB;QACA,IAAI3E,MAAM,GAAG+E,QAAQ,CAACH,YAAY,EAAE,EAAE,CAAC;QACvC,IAAII,KAAK,CAAChF,MAAM,CAAC,EAAE;UACjBA,MAAM,GAAG2E,aAAa;QACxB;QACA,OAAO3E,MAAM;MACf,CAAC;MAED6D,OAAO,CAAChG,SAAS,CAACoH,cAAc,GAAG,UAASP,IAAI,EAAEQ,cAAc,EAAEP,aAAa,EAAE;QAC/E,IAAI3E,MAAM,GAAG,IAAI,CAACwE,mBAAmB,CAACE,IAAI,EAAEQ,cAAc,EAAEP,aAAa,CAAC;QAC1E,IAAI3E,MAAM,CAAC7B,MAAM,KAAK,CAAC,EAAE;UACvB,MAAM,IAAIgH,KAAK,CACb,oCAAoC,GAAGT,IAAI,GAAG,8CAA8C,GAC5FQ,cAAc,GAAG,oBAAoB,GAAG,IAAI,CAACnB,WAAW,CAACW,IAAI,CAAC,GAAG,GAAG,CAAC;QACzE;QAEA,OAAO1E,MAAM,CAAC,CAAC,CAAC;MAClB,CAAC;MAGD6D,OAAO,CAAChG,SAAS,CAAC2G,mBAAmB,GAAG,UAASE,IAAI,EAAEQ,cAAc,EAAEP,aAAa,EAAE;QACpF,IAAI,CAACO,cAAc,IAAIA,cAAc,CAAC/G,MAAM,KAAK,CAAC,EAAE;UAClD,MAAM,IAAIgH,KAAK,CAAC,iCAAiC,CAAC;QACpD;QAEAR,aAAa,GAAGA,aAAa,IAAI,CAACO,cAAc,CAAC,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,CAACE,mBAAmB,CAACT,aAAa,EAAEO,cAAc,CAAC,EAAE;UAC5D,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;QAC3C;QAEA,IAAInF,MAAM,GAAG,IAAI,CAACyE,UAAU,CAACC,IAAI,EAAEC,aAAa,CAAC;QACjD,IAAI,CAAC,IAAI,CAACS,mBAAmB,CAACpF,MAAM,EAAEkF,cAAc,CAAC,EAAE;UACrD,MAAM,IAAIC,KAAK,CACb,oCAAoC,GAAGT,IAAI,GAAG,4CAA4C,GAC1FQ,cAAc,GAAG,oBAAoB,GAAG,IAAI,CAACnB,WAAW,CAACW,IAAI,CAAC,GAAG,GAAG,CAAC;QACzE;QAEA,OAAO1E,MAAM;MACf,CAAC;MAED6D,OAAO,CAAChG,SAAS,CAACuH,mBAAmB,GAAG,UAASpF,MAAM,EAAEkF,cAAc,EAAE;QACvE,OAAOlF,MAAM,CAAC7B,MAAM,IAAI+G,cAAc,CAAC/G,MAAM,IAC3C,CAAC6B,MAAM,CAACqF,IAAI,CAAC,UAASpH,IAAI,EAAE;UAAE,OAAOiH,cAAc,CAACvB,OAAO,CAAC1F,IAAI,CAAC,KAAK,CAAC,CAAC;QAAE,CAAC,CAAC;MAChF,CAAC;;MAGD;MACA;MACA;MACA;MACA;MACA,SAAS+F,UAAUA,CAACsB,UAAU,EAAEC,cAAc,EAAE;QAC9C,IAAIC,SAAS,GAAG,CAAC,CAAC;QAClBF,UAAU,GAAGG,cAAc,CAACH,UAAU,CAAC;QACvC,IAAIZ,IAAI;QAER,KAAKA,IAAI,IAAIY,UAAU,EAAE;UACvB,IAAIZ,IAAI,KAAKa,cAAc,EAAE;YAC3BC,SAAS,CAACd,IAAI,CAAC,GAAGY,UAAU,CAACZ,IAAI,CAAC;UACpC;QACF;;QAEA;QACA,IAAIa,cAAc,IAAID,UAAU,CAACC,cAAc,CAAC,EAAE;UAChD,KAAKb,IAAI,IAAIY,UAAU,CAACC,cAAc,CAAC,EAAE;YACvCC,SAAS,CAACd,IAAI,CAAC,GAAGY,UAAU,CAACC,cAAc,CAAC,CAACb,IAAI,CAAC;UACpD;QACF;QACA,OAAOc,SAAS;MAClB;MAEA,SAASC,cAAcA,CAACpF,OAAO,EAAE;QAC/B,IAAIqF,aAAa,GAAG,CAAC,CAAC;QACtB,IAAIC,GAAG;QAEP,KAAKA,GAAG,IAAItF,OAAO,EAAE;UACnB,IAAIuF,MAAM,GAAGD,GAAG,CAACrD,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;UACnCoD,aAAa,CAACE,MAAM,CAAC,GAAGvF,OAAO,CAACsF,GAAG,CAAC;QACtC;QACA,OAAOD,aAAa;MACtB;MAEAzI,MAAM,CAAC2G,OAAO,CAACC,OAAO,GAAGA,OAAO;MAChC5G,MAAM,CAAC2G,OAAO,CAACiC,aAAa,GAAGJ,cAAc;MAC7CxI,MAAM,CAAC2G,OAAO,CAACkC,SAAS,GAAG9B,UAAU;;MAGrC;IAAM,CAAC,KAEP;IACA,KAAO,UAAS/G,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAI8I,iBAAiB,GAAGC,MAAM,CAACnI,SAAS,CAACoI,cAAc,CAAC,QAAQ,CAAC;MAEjE,SAASC,YAAYA,CAACC,YAAY,EAAE;QAClC,IAAI,CAACC,OAAO,GAAGD,YAAY,IAAI,EAAE;QACjC,IAAI,CAACE,cAAc,GAAG,IAAI,CAACD,OAAO,CAACjI,MAAM;QACzC,IAAI,CAACmI,UAAU,GAAG,CAAC;MACrB;MAEAJ,YAAY,CAACrI,SAAS,CAAC0I,OAAO,GAAG,YAAW;QAC1C,IAAI,CAACD,UAAU,GAAG,CAAC;MACrB,CAAC;MAEDJ,YAAY,CAACrI,SAAS,CAAC2I,IAAI,GAAG,YAAW;QACvC,IAAI,IAAI,CAACF,UAAU,GAAG,CAAC,EAAE;UACvB,IAAI,CAACA,UAAU,IAAI,CAAC;QACtB;MACF,CAAC;MAEDJ,YAAY,CAACrI,SAAS,CAAC4I,OAAO,GAAG,YAAW;QAC1C,OAAO,IAAI,CAACH,UAAU,GAAG,IAAI,CAACD,cAAc;MAC9C,CAAC;MAEDH,YAAY,CAACrI,SAAS,CAACqB,IAAI,GAAG,YAAW;QACvC,IAAIwH,GAAG,GAAG,IAAI;QACd,IAAI,IAAI,CAACD,OAAO,CAAC,CAAC,EAAE;UAClBC,GAAG,GAAG,IAAI,CAACN,OAAO,CAACO,MAAM,CAAC,IAAI,CAACL,UAAU,CAAC;UAC1C,IAAI,CAACA,UAAU,IAAI,CAAC;QACtB;QACA,OAAOI,GAAG;MACZ,CAAC;MAEDR,YAAY,CAACrI,SAAS,CAAC+I,IAAI,GAAG,UAAS1I,KAAK,EAAE;QAC5C,IAAIwI,GAAG,GAAG,IAAI;QACdxI,KAAK,GAAGA,KAAK,IAAI,CAAC;QAClBA,KAAK,IAAI,IAAI,CAACoI,UAAU;QACxB,IAAIpI,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACmI,cAAc,EAAE;UAC7CK,GAAG,GAAG,IAAI,CAACN,OAAO,CAACO,MAAM,CAACzI,KAAK,CAAC;QAClC;QACA,OAAOwI,GAAG;MACZ,CAAC;;MAED;MACA;MACA;MACA;MACA;MACA;MACA;MACAR,YAAY,CAACrI,SAAS,CAACgJ,OAAO,GAAG,UAASxI,OAAO,EAAEH,KAAK,EAAE;QACxDG,OAAO,CAACyI,SAAS,GAAG5I,KAAK;QACzB,IAAI6I,aAAa,GAAG1I,OAAO,CAAC2I,IAAI,CAAC,IAAI,CAACZ,OAAO,CAAC;QAE9C,IAAIW,aAAa,IAAI,EAAEhB,iBAAiB,IAAI1H,OAAO,CAAC4I,MAAM,CAAC,EAAE;UAC3D,IAAIF,aAAa,CAAC7I,KAAK,KAAKA,KAAK,EAAE;YACjC6I,aAAa,GAAG,IAAI;UACtB;QACF;QAEA,OAAOA,aAAa;MACtB,CAAC;MAEDb,YAAY,CAACrI,SAAS,CAACqJ,IAAI,GAAG,UAAS7I,OAAO,EAAEH,KAAK,EAAE;QACrDA,KAAK,GAAGA,KAAK,IAAI,CAAC;QAClBA,KAAK,IAAI,IAAI,CAACoI,UAAU;QAExB,IAAIpI,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACmI,cAAc,EAAE;UAC7C,OAAO,CAAC,CAAC,IAAI,CAACQ,OAAO,CAACxI,OAAO,EAAEH,KAAK,CAAC;QACvC,CAAC,MAAM;UACL,OAAO,KAAK;QACd;MACF,CAAC;MAEDgI,YAAY,CAACrI,SAAS,CAACsJ,QAAQ,GAAG,UAAS9I,OAAO,EAAEH,KAAK,EAAE;QACzD;QACA,IAAIwI,GAAG,GAAG,IAAI,CAACE,IAAI,CAAC1I,KAAK,CAAC;QAC1BG,OAAO,CAACyI,SAAS,GAAG,CAAC;QACrB,OAAOJ,GAAG,KAAK,IAAI,IAAIrI,OAAO,CAAC6I,IAAI,CAACR,GAAG,CAAC;MAC1C,CAAC;MAEDR,YAAY,CAACrI,SAAS,CAACU,KAAK,GAAG,UAASF,OAAO,EAAE;QAC/C,IAAI0I,aAAa,GAAG,IAAI,CAACF,OAAO,CAACxI,OAAO,EAAE,IAAI,CAACiI,UAAU,CAAC;QAC1D,IAAIS,aAAa,EAAE;UACjB,IAAI,CAACT,UAAU,IAAIS,aAAa,CAAC,CAAC,CAAC,CAAC5I,MAAM;QAC5C,CAAC,MAAM;UACL4I,aAAa,GAAG,IAAI;QACtB;QACA,OAAOA,aAAa;MACtB,CAAC;MAEDb,YAAY,CAACrI,SAAS,CAACuJ,IAAI,GAAG,UAASC,gBAAgB,EAAEC,aAAa,EAAEC,WAAW,EAAE;QACnF,IAAIb,GAAG,GAAG,EAAE;QACZ,IAAInI,KAAK;QACT,IAAI8I,gBAAgB,EAAE;UACpB9I,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC8I,gBAAgB,CAAC;UACpC,IAAI9I,KAAK,EAAE;YACTmI,GAAG,IAAInI,KAAK,CAAC,CAAC,CAAC;UACjB;QACF;QACA,IAAI+I,aAAa,KAAK/I,KAAK,IAAI,CAAC8I,gBAAgB,CAAC,EAAE;UACjDX,GAAG,IAAI,IAAI,CAACc,SAAS,CAACF,aAAa,EAAEC,WAAW,CAAC;QACnD;QACA,OAAOb,GAAG;MACZ,CAAC;MAEDR,YAAY,CAACrI,SAAS,CAAC2J,SAAS,GAAG,UAASnJ,OAAO,EAAEkJ,WAAW,EAAE;QAChE,IAAIb,GAAG,GAAG,EAAE;QACZ,IAAIe,WAAW,GAAG,IAAI,CAACnB,UAAU;QACjCjI,OAAO,CAACyI,SAAS,GAAG,IAAI,CAACR,UAAU;QACnC,IAAIS,aAAa,GAAG1I,OAAO,CAAC2I,IAAI,CAAC,IAAI,CAACZ,OAAO,CAAC;QAC9C,IAAIW,aAAa,EAAE;UACjBU,WAAW,GAAGV,aAAa,CAAC7I,KAAK;UACjC,IAAIqJ,WAAW,EAAE;YACfE,WAAW,IAAIV,aAAa,CAAC,CAAC,CAAC,CAAC5I,MAAM;UACxC;QACF,CAAC,MAAM;UACLsJ,WAAW,GAAG,IAAI,CAACpB,cAAc;QACnC;QAEAK,GAAG,GAAG,IAAI,CAACN,OAAO,CAACsB,SAAS,CAAC,IAAI,CAACpB,UAAU,EAAEmB,WAAW,CAAC;QAC1D,IAAI,CAACnB,UAAU,GAAGmB,WAAW;QAC7B,OAAOf,GAAG;MACZ,CAAC;MAEDR,YAAY,CAACrI,SAAS,CAAC8J,cAAc,GAAG,UAAStJ,OAAO,EAAE;QACxD,OAAO,IAAI,CAACmJ,SAAS,CAACnJ,OAAO,EAAE,IAAI,CAAC;MACtC,CAAC;MAED6H,YAAY,CAACrI,SAAS,CAAC+J,UAAU,GAAG,UAASvJ,OAAO,EAAEwJ,UAAU,EAAE;QAChE,IAAI7H,MAAM,GAAG,IAAI;QACjB,IAAI8H,KAAK,GAAG,GAAG;QACf,IAAID,UAAU,IAAI9B,iBAAiB,EAAE;UACnC+B,KAAK,GAAG,GAAG;QACb;QACA;QACA,IAAI,OAAOzJ,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,EAAE,EAAE;UACjD;UACA2B,MAAM,GAAG,IAAIgG,MAAM,CAAC3H,OAAO,EAAEyJ,KAAK,CAAC;QACrC,CAAC,MAAM,IAAIzJ,OAAO,EAAE;UAClB2B,MAAM,GAAG,IAAIgG,MAAM,CAAC3H,OAAO,CAAC0J,MAAM,EAAED,KAAK,CAAC;QAC5C;QACA,OAAO9H,MAAM;MACf,CAAC;MAEDkG,YAAY,CAACrI,SAAS,CAACmK,kBAAkB,GAAG,UAASC,cAAc,EAAE;QACnE,OAAOjC,MAAM,CAACiC,cAAc,CAAC3F,OAAO,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;MACzE,CAAC;;MAED;MACA4D,YAAY,CAACrI,SAAS,CAACqK,cAAc,GAAG,UAAS7J,OAAO,EAAE;QACxD,IAAI8J,KAAK,GAAG,IAAI,CAAC7B,UAAU;QAC3B,IAAII,GAAG,GAAG,IAAI,CAACiB,cAAc,CAACtJ,OAAO,CAAC;QACtC,IAAI,CAACiI,UAAU,GAAG6B,KAAK;QACvB,OAAOzB,GAAG;MACZ,CAAC;MAEDR,YAAY,CAACrI,SAAS,CAACuK,QAAQ,GAAG,UAASC,OAAO,EAAE;QAClD,IAAIF,KAAK,GAAG,IAAI,CAAC7B,UAAU,GAAG,CAAC;QAC/B,OAAO6B,KAAK,IAAIE,OAAO,CAAClK,MAAM,IAAI,IAAI,CAACiI,OAAO,CAACsB,SAAS,CAACS,KAAK,GAAGE,OAAO,CAAClK,MAAM,EAAEgK,KAAK,CAAC,CACpFG,WAAW,CAAC,CAAC,KAAKD,OAAO;MAC9B,CAAC;MAEDpL,MAAM,CAAC2G,OAAO,CAACsC,YAAY,GAAGA,YAAY;;MAG1C;IAAM,CAAC,QAKP;IACA,KAAO,UAASjJ,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,SAASsL,UAAUA,CAACC,mBAAmB,EAAEC,iBAAiB,EAAE;QAC1DD,mBAAmB,GAAG,OAAOA,mBAAmB,KAAK,QAAQ,GAAGA,mBAAmB,GAAGA,mBAAmB,CAACT,MAAM;QAChHU,iBAAiB,GAAG,OAAOA,iBAAiB,KAAK,QAAQ,GAAGA,iBAAiB,GAAGA,iBAAiB,CAACV,MAAM;QACxG,IAAI,CAACW,0BAA0B,GAAG,IAAI1C,MAAM,CAACwC,mBAAmB,GAAG,yBAAyB,CAACT,MAAM,GAAGU,iBAAiB,EAAE,GAAG,CAAC;QAC7H,IAAI,CAACE,mBAAmB,GAAG,iBAAiB;QAE5C,IAAI,CAACC,+BAA+B,GAAG,IAAI5C,MAAM,CAACwC,mBAAmB,GAAG,0BAA0B,CAACT,MAAM,GAAGU,iBAAiB,EAAE,GAAG,CAAC;MACrI;MAEAF,UAAU,CAAC1K,SAAS,CAACgL,cAAc,GAAG,UAAS/F,IAAI,EAAE;QACnD,IAAI,CAACA,IAAI,CAACvE,KAAK,CAAC,IAAI,CAACmK,0BAA0B,CAAC,EAAE;UAChD,OAAO,IAAI;QACb;QAEA,IAAII,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,CAACH,mBAAmB,CAAC7B,SAAS,GAAG,CAAC;QACtC,IAAIiC,eAAe,GAAG,IAAI,CAACJ,mBAAmB,CAAC3B,IAAI,CAAClE,IAAI,CAAC;QAEzD,OAAOiG,eAAe,EAAE;UACtBD,UAAU,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC,GAAGA,eAAe,CAAC,CAAC,CAAC;UACnDA,eAAe,GAAG,IAAI,CAACJ,mBAAmB,CAAC3B,IAAI,CAAClE,IAAI,CAAC;QACvD;QAEA,OAAOgG,UAAU;MACnB,CAAC;MAEDP,UAAU,CAAC1K,SAAS,CAACmL,WAAW,GAAG,UAASC,KAAK,EAAE;QACjD,OAAOA,KAAK,CAACtB,cAAc,CAAC,IAAI,CAACiB,+BAA+B,CAAC;MACnE,CAAC;MAGD3L,MAAM,CAAC2G,OAAO,CAAC2E,UAAU,GAAGA,UAAU;;MAGtC;IAAM,CAAC,KAEP;IACA,KAAO,UAAStL,MAAM,EAAEiM,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAIC,UAAU,GAAID,mBAAmB,CAAC,EAAE,CAAC,CAACC,UAAW;QACnDvF,OAAO,GAAIsF,mBAAmB,CAAC,EAAE,CAAC,CAACtF,OAAQ;MAE7C,SAASwF,YAAYA,CAACC,WAAW,EAAEjJ,OAAO,EAAE;QAC1C,IAAIkJ,UAAU,GAAG,IAAIH,UAAU,CAACE,WAAW,EAAEjJ,OAAO,CAAC;QACrD,OAAOkJ,UAAU,CAACC,QAAQ,CAAC,CAAC;MAC9B;MAEAvM,MAAM,CAAC2G,OAAO,GAAGyF,YAAY;MAC7BpM,MAAM,CAAC2G,OAAO,CAAC6F,cAAc,GAAG,YAAW;QACzC,OAAO,IAAI5F,OAAO,CAAC,CAAC;MACtB,CAAC;;MAGD;IAAM,CAAC,IACP;IACA,KAAO,UAAS5G,MAAM,EAAEiM,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAItF,OAAO,GAAIsF,mBAAmB,CAAC,EAAE,CAAC,CAACtF,OAAQ;MAC/C,IAAIxC,MAAM,GAAI8H,mBAAmB,CAAC,CAAC,CAAC,CAAC9H,MAAO;MAC5C,IAAI6E,YAAY,GAAIiD,mBAAmB,CAAC,CAAC,CAAC,CAACjD,YAAa;MACxD,IAAIqC,UAAU,GAAIY,mBAAmB,CAAC,EAAE,CAAC,CAACZ,UAAW;MAErD,IAAImB,eAAe,GAAG,IAAInB,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC;MAEpD,IAAIoB,SAAS,GAAG,aAAa;MAC7B,IAAIC,aAAa,GAAG,cAAc;;MAElC;MACA,IAAIC,cAAc,GAAG,IAAI;MACzB,IAAIC,iBAAiB,GAAG,aAAa;MACrC,IAAIC,qBAAqB,GAAG,+BAA+B;MAC3D,IAAIC,eAAe,GAAG,+BAA+B;MAErD,SAASZ,UAAUA,CAACE,WAAW,EAAEjJ,OAAO,EAAE;QACxC,IAAI,CAAC4J,YAAY,GAAGX,WAAW,IAAI,EAAE;QACrC;QACA;QACA,IAAI,CAACY,QAAQ,GAAG,IAAIrG,OAAO,CAACxD,OAAO,CAAC;QACpC,IAAI,CAAC8J,GAAG,GAAG,IAAI;QACf,IAAI,CAACC,MAAM,GAAG,IAAI;;QAElB;QACA,IAAI,CAACC,cAAc,GAAG;UACpB,MAAM,EAAE,IAAI;UACZ,WAAW,EAAE,IAAI;UACjB,WAAW,EAAE,IAAI;UACjB;UACA,OAAO,EAAE,IAAI;UACb,UAAU,EAAE,IAAI;UAChB,UAAU,EAAE;QACd,CAAC;QACD,IAAI,CAACC,sBAAsB,GAAG;UAC5B,OAAO,EAAE,IAAI;UACb,UAAU,EAAE,IAAI;UAChB,UAAU,EAAE;QACd,CAAC;QACD,IAAI,CAACC,8BAA8B,GAAG,CACpC,qBAAqB,EACrB,eAAe,CAChB;MAEH;MAEAnB,UAAU,CAACvL,SAAS,CAAC2M,SAAS,GAAG,UAASC,QAAQ,EAAE;QAClD,IAAIzK,MAAM,GAAG,EAAE;QACf,IAAI,CAACmK,GAAG,GAAG,IAAI,CAACC,MAAM,CAAClL,IAAI,CAAC,CAAC;QAC7B,OAAO,IAAI,CAACiL,GAAG,EAAE;UACfnK,MAAM,IAAI,IAAI,CAACmK,GAAG;UAClB,IAAI,IAAI,CAACA,GAAG,KAAK,IAAI,EAAE;YACrBnK,MAAM,IAAI,IAAI,CAACoK,MAAM,CAAClL,IAAI,CAAC,CAAC;UAC9B,CAAC,MAAM,IAAIuL,QAAQ,CAAC9G,OAAO,CAAC,IAAI,CAACwG,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAACA,GAAG,KAAK,IAAI,EAAE;YACjE;UACF;UACA,IAAI,CAACA,GAAG,GAAG,IAAI,CAACC,MAAM,CAAClL,IAAI,CAAC,CAAC;QAC/B;QACA,OAAOc,MAAM;MACf,CAAC;;MAED;MACA;MACA;MACA;MACAoJ,UAAU,CAACvL,SAAS,CAAC6M,aAAa,GAAG,UAASC,sBAAsB,EAAE;QACpE,IAAI3K,MAAM,GAAG6J,cAAc,CAAC3C,IAAI,CAAC,IAAI,CAACkD,MAAM,CAACxD,IAAI,CAAC,CAAC,CAAC;QACpD,IAAIgE,aAAa,GAAG,CAAC;QACrB,OAAOf,cAAc,CAAC3C,IAAI,CAAC,IAAI,CAACkD,MAAM,CAACxD,IAAI,CAAC,CAAC,CAAC,EAAE;UAC9C,IAAI,CAACuD,GAAG,GAAG,IAAI,CAACC,MAAM,CAAClL,IAAI,CAAC,CAAC;UAC7B,IAAIyL,sBAAsB,IAAI,IAAI,CAACR,GAAG,KAAK,IAAI,EAAE;YAC/C,IAAIS,aAAa,KAAK,CAAC,IAAIA,aAAa,GAAG,IAAI,CAACV,QAAQ,CAAC5F,qBAAqB,EAAE;cAC9EsG,aAAa,EAAE;cACf,IAAI,CAACC,OAAO,CAAC5L,YAAY,CAAC,IAAI,CAAC;YACjC;UACF;QACF;QACA,OAAOe,MAAM;MACf,CAAC;;MAED;MACA;MACA;MACAoJ,UAAU,CAACvL,SAAS,CAACiN,sBAAsB,GAAG,YAAW;QACvD,IAAIC,SAAS,GAAG,CAAC;QACjB,IAAIC,CAAC,GAAG,CAAC;QACT,IAAIC,EAAE,GAAG,IAAI,CAACb,MAAM,CAACxD,IAAI,CAACoE,CAAC,CAAC;QAC5B,OAAOC,EAAE,EAAE;UACT,IAAIA,EAAE,KAAK,GAAG,EAAE;YACd,OAAO,IAAI;UACb,CAAC,MAAM,IAAIA,EAAE,KAAK,GAAG,EAAE;YACrB;YACAF,SAAS,IAAI,CAAC;UAChB,CAAC,MAAM,IAAIE,EAAE,KAAK,GAAG,EAAE;YACrB,IAAIF,SAAS,KAAK,CAAC,EAAE;cACnB,OAAO,KAAK;YACd;YACAA,SAAS,IAAI,CAAC;UAChB,CAAC,MAAM,IAAIE,EAAE,KAAK,GAAG,IAAIA,EAAE,KAAK,GAAG,EAAE;YACnC,OAAO,KAAK;UACd;UACAD,CAAC,EAAE;UACHC,EAAE,GAAG,IAAI,CAACb,MAAM,CAACxD,IAAI,CAACoE,CAAC,CAAC;QAC1B;QACA,OAAO,KAAK;MACd,CAAC;MAED5B,UAAU,CAACvL,SAAS,CAACqN,YAAY,GAAG,UAASC,aAAa,EAAE;QAC1D,IAAI,CAACN,OAAO,CAAC7M,UAAU,CAAC,IAAI,CAACoN,YAAY,CAAC;QAC1C,IAAI,CAACP,OAAO,CAAChJ,kBAAkB,GAAG,IAAI;QACtC,IAAI,CAACgJ,OAAO,CAAC9H,SAAS,CAACoI,aAAa,CAAC;MACvC,CAAC;MAED/B,UAAU,CAACvL,SAAS,CAACwN,mBAAmB,GAAG,UAASC,YAAY,EAAE;QAChE,IAAIA,YAAY,EAAE;UAChB,IAAI,CAACT,OAAO,CAACjJ,kBAAkB,GAAG,IAAI;QACxC;MACF,CAAC;MAEDwH,UAAU,CAACvL,SAAS,CAACW,MAAM,GAAG,YAAW;QACvC,IAAI,CAAC4M,YAAY,EAAE;MACrB,CAAC;MAEDhC,UAAU,CAACvL,SAAS,CAAC0N,OAAO,GAAG,YAAW;QACxC,IAAI,IAAI,CAACH,YAAY,GAAG,CAAC,EAAE;UACzB,IAAI,CAACA,YAAY,EAAE;QACrB;MACF,CAAC;;MAED;;MAEAhC,UAAU,CAACvL,SAAS,CAAC2L,QAAQ,GAAG,YAAW;QACzC,IAAI,IAAI,CAACU,QAAQ,CAACjG,QAAQ,EAAE;UAC1B,OAAO,IAAI,CAACgG,YAAY;QAC1B;QAEA,IAAIX,WAAW,GAAG,IAAI,CAACW,YAAY;QACnC,IAAI7H,GAAG,GAAG,IAAI,CAAC8H,QAAQ,CAAC9H,GAAG;QAC3B,IAAIA,GAAG,KAAK,MAAM,EAAE;UAClBA,GAAG,GAAG,IAAI;UACV,IAAIkH,WAAW,IAAIK,SAAS,CAACzC,IAAI,CAACoC,WAAW,IAAI,EAAE,CAAC,EAAE;YACpDlH,GAAG,GAAGkH,WAAW,CAAC/K,KAAK,CAACoL,SAAS,CAAC,CAAC,CAAC,CAAC;UACvC;QACF;;QAGA;QACAL,WAAW,GAAGA,WAAW,CAAChH,OAAO,CAACsH,aAAa,EAAE,IAAI,CAAC;;QAEtD;QACA,IAAItJ,gBAAgB,GAAGgJ,WAAW,CAAC/K,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAEtD,IAAI,CAACsM,OAAO,GAAG,IAAIxJ,MAAM,CAAC,IAAI,CAAC6I,QAAQ,EAAE5J,gBAAgB,CAAC;QAC1D,IAAI,CAAC8J,MAAM,GAAG,IAAIlE,YAAY,CAACoD,WAAW,CAAC;QAC3C,IAAI,CAAC8B,YAAY,GAAG,CAAC;QACrB,IAAI,CAACI,YAAY,GAAG,CAAC;QAErB,IAAI,CAACrB,GAAG,GAAG,IAAI;QACf,IAAIsB,UAAU,GAAG,CAAC;QAElB,IAAIC,UAAU,GAAG,KAAK;QACtB;QACA;QACA,IAAIC,mBAAmB,GAAG,KAAK;QAC/B,IAAIC,wBAAwB,GAAG,KAAK;QACpC,IAAIC,qBAAqB,GAAG,KAAK;QACjC,IAAIC,aAAa,GAAG,KAAK;QACzB,IAAIC,YAAY,GAAG,IAAI,CAAC5B,GAAG;QAC3B,IAAI6B,wBAAwB,GAAG,KAAK;QACpC,IAAIC,UAAU;QACd,IAAIX,YAAY;QAChB,IAAIY,WAAW;QAEf,OAAO,IAAI,EAAE;UACXD,UAAU,GAAG,IAAI,CAAC7B,MAAM,CAAChD,IAAI,CAAC0C,iBAAiB,CAAC;UAChDwB,YAAY,GAAGW,UAAU,KAAK,EAAE;UAChCC,WAAW,GAAGH,YAAY;UAC1B,IAAI,CAAC5B,GAAG,GAAG,IAAI,CAACC,MAAM,CAAClL,IAAI,CAAC,CAAC;UAC7B,IAAI,IAAI,CAACiL,GAAG,KAAK,IAAI,IAAI,IAAI,CAACC,MAAM,CAAC3D,OAAO,CAAC,CAAC,EAAE;YAC9C,IAAI,CAAC0D,GAAG,IAAI,IAAI,CAACC,MAAM,CAAClL,IAAI,CAAC,CAAC;UAChC;UACA6M,YAAY,GAAG,IAAI,CAAC5B,GAAG;UAEvB,IAAI,CAAC,IAAI,CAACA,GAAG,EAAE;YACb;UACF,CAAC,MAAM,IAAI,IAAI,CAACA,GAAG,KAAK,GAAG,IAAI,IAAI,CAACC,MAAM,CAACxD,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;YACzD;YACA;YACA;YACA;YACA;YACA,IAAI,CAACiE,OAAO,CAAC5L,YAAY,CAAC,CAAC;YAC3B,IAAI,CAACmL,MAAM,CAAC5D,IAAI,CAAC,CAAC;YAElB,IAAI2F,OAAO,GAAG,IAAI,CAAC/B,MAAM,CAAChD,IAAI,CAAC2C,qBAAqB,CAAC;;YAErD;YACA,IAAIjB,UAAU,GAAGY,eAAe,CAACb,cAAc,CAACsD,OAAO,CAAC;YACxD,IAAIrD,UAAU,IAAIA,UAAU,CAACsD,MAAM,KAAK,OAAO,EAAE;cAC/CD,OAAO,IAAIzC,eAAe,CAACV,WAAW,CAAC,IAAI,CAACoB,MAAM,CAAC;YACrD;YAEA,IAAI,CAACc,YAAY,CAACiB,OAAO,CAAC;;YAE1B;YACA,IAAI,CAACzB,aAAa,CAAC,IAAI,CAAC;;YAExB;YACA;YACA,IAAI,CAACG,OAAO,CAAC5L,YAAY,CAAC,CAAC;UAC7B,CAAC,MAAM,IAAI,IAAI,CAACkL,GAAG,KAAK,GAAG,IAAI,IAAI,CAACC,MAAM,CAACxD,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;YACzD;YACA;YACA;YACA,IAAI,CAACiE,OAAO,CAACjJ,kBAAkB,GAAG,IAAI;YACtC,IAAI,CAACwI,MAAM,CAAC5D,IAAI,CAAC,CAAC;YAClB,IAAI,CAAC0E,YAAY,CAAC,IAAI,CAACd,MAAM,CAAChD,IAAI,CAAC4C,eAAe,CAAC,CAAC;;YAEpD;YACA,IAAI,CAACU,aAAa,CAAC,IAAI,CAAC;UAC1B,CAAC,MAAM,IAAI,IAAI,CAACP,GAAG,KAAK,GAAG,EAAE;YAC3B,IAAI,CAACkB,mBAAmB,CAACC,YAAY,CAAC;YAEtC,IAAI,CAACJ,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;;YAE3B;YACA,IAAIkC,QAAQ,GAAG,IAAI,CAACjC,MAAM,CAAClC,cAAc,CAAC,qBAAqB,CAAC;YAEhE,IAAImE,QAAQ,CAAC9N,KAAK,CAAC,OAAO,CAAC,EAAE;cAC3B;cACA8N,QAAQ,GAAG,IAAI,CAAC7B,SAAS,CAAC,IAAI,CAAC,CAAClI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;cACnD,IAAI,CAAC4I,YAAY,CAACmB,QAAQ,CAAC;cAC3B,IAAI,CAACxB,OAAO,CAACjJ,kBAAkB,GAAG,IAAI;YACxC;;YAEA;YACA,IAAI6J,UAAU,KAAK,CAAC,IAAIY,QAAQ,CAAC1I,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;cACpDgI,mBAAmB,GAAG,IAAI;cAC1B,IAAI,CAACnN,MAAM,CAAC,CAAC;YACf;UACF,CAAC,MAAM,IAAI,IAAI,CAAC2L,GAAG,KAAK,GAAG,EAAE;YAC3B,IAAI,CAACkB,mBAAmB,CAACC,YAAY,CAAC;;YAEtC;YACA,IAAI,IAAI,CAAClB,MAAM,CAACxD,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;cAC9B,IAAI,CAACsE,YAAY,CAAC,IAAI,CAACf,GAAG,GAAG,IAAI,CAACK,SAAS,CAAC,GAAG,CAAC,CAAC;YACnD,CAAC,MAAM;cACL,IAAI,CAACU,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;;cAE3B;cACA,IAAImC,cAAc,GAAG,IAAI,CAAClC,MAAM,CAAClC,cAAc,CAAC,qBAAqB,CAAC;cAEtE,IAAIoE,cAAc,CAAC/N,KAAK,CAAC,OAAO,CAAC,EAAE;gBACjC;gBACA+N,cAAc,GAAG,IAAI,CAAC9B,SAAS,CAAC,IAAI,CAAC,CAAClI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;gBACzD,IAAI,CAAC4I,YAAY,CAACoB,cAAc,CAAC;gBACjC,IAAI,CAACzB,OAAO,CAACjJ,kBAAkB,GAAG,IAAI;cACxC;;cAEA;cACA,IAAI6J,UAAU,KAAK,CAAC,IAAIa,cAAc,CAAC3I,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC1DgI,mBAAmB,GAAG,IAAI;gBAC1B,IAAI,CAACnN,MAAM,CAAC,CAAC;;gBAEb;cACF,CAAC,MAAM,IAAI8N,cAAc,IAAI,IAAI,CAACjC,cAAc,EAAE;gBAChD,IAAI,CAACmB,YAAY,IAAI,CAAC;gBACtB,IAAIc,cAAc,IAAI,IAAI,CAAChC,sBAAsB,EAAE;kBACjDsB,wBAAwB,GAAG,IAAI;gBACjC;;gBAEA;cACF,CAAC,MAAM,IAAIH,UAAU,KAAK,CAAC,IAAI,CAACE,mBAAmB,EAAE;gBACnDE,qBAAqB,GAAG,IAAI;cAC9B;YACF;UACF,CAAC,MAAM,IAAI,IAAI,CAAC1B,GAAG,KAAK,GAAG,IAAI,IAAI,CAACC,MAAM,CAACxD,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;YACzD,IAAI,CAACyE,mBAAmB,CAACC,YAAY,CAAC;YACtC,IAAI,CAACJ,YAAY,CAAC,IAAI,CAACf,GAAG,GAAG,IAAI,CAACK,SAAS,CAAC,GAAG,CAAC,CAAC;UACnD,CAAC,MAAM,IAAI,IAAI,CAACL,GAAG,KAAK,GAAG,EAAE;YAC3B,IAAIwB,mBAAmB,EAAE;cACvBA,mBAAmB,GAAG,KAAK;cAC3B,IAAI,CAACJ,OAAO,CAAC,CAAC;YAChB;;YAEA;YACAM,qBAAqB,GAAG,KAAK;;YAE7B;YACA,IAAID,wBAAwB,EAAE;cAC5BA,wBAAwB,GAAG,KAAK;cAChCF,UAAU,GAAI,IAAI,CAACN,YAAY,IAAI,IAAI,CAACI,YAAa;YACvD,CAAC,MAAM;cACL;cACAE,UAAU,GAAI,IAAI,CAACN,YAAY,IAAI,IAAI,CAACI,YAAY,GAAG,CAAE;YAC3D;YACA,IAAI,IAAI,CAACtB,QAAQ,CAACqC,qBAAqB,IAAIb,UAAU,EAAE;cACrD,IAAI,IAAI,CAACb,OAAO,CAAClJ,aAAa,IAAI,IAAI,CAACkJ,OAAO,CAAClJ,aAAa,CAAC1D,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC7E,IAAI,CAAC4M,OAAO,CAACtH,uBAAuB,CAAC,GAAG,EAAE,GAAG,CAAC;cAChD;YACF;YAEA,IAAI,CAACsH,OAAO,CAACjJ,kBAAkB,GAAG,IAAI;;YAEtC;YACA,IAAI,IAAI,CAACsI,QAAQ,CAACsC,WAAW,KAAK,QAAQ,EAAE;cAC1C,IAAI,CAAC3B,OAAO,CAAC5L,YAAY,CAAC,CAAC;cAC3B,IAAI,CAACiM,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;cAC3B,IAAI,CAAC3L,MAAM,CAAC,CAAC;cACb,IAAI,CAACqM,OAAO,CAAC7M,UAAU,CAAC,IAAI,CAACoN,YAAY,CAAC;YAC5C,CAAC,MAAM;cACL;cACA,IAAIc,WAAW,KAAK,GAAG,EAAE;gBACvB,IAAI,CAACrB,OAAO,CAACjJ,kBAAkB,GAAG,KAAK;cACzC,CAAC,MAAM,IAAIsK,WAAW,KAAK,GAAG,EAAE;gBAC9B,IAAI,CAAC1N,MAAM,CAAC,CAAC;cACf;cACA,IAAI,CAAC0M,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;YAC7B;YAEA,IAAI,CAACO,aAAa,CAAC,IAAI,CAAC;YACxB,IAAI,CAACG,OAAO,CAAC5L,YAAY,CAAC,CAAC;UAC7B,CAAC,MAAM,IAAI,IAAI,CAACkL,GAAG,KAAK,GAAG,EAAE;YAC3B,IAAI,CAACoB,OAAO,CAAC,CAAC;YACd,IAAI,CAACV,OAAO,CAAC5L,YAAY,CAAC,CAAC;YAC3B,IAAIiN,WAAW,KAAK,GAAG,EAAE;cACvB,IAAI,CAACrB,OAAO,CAAC/K,IAAI,CAAC,IAAI,CAAC;YACzB;YAEA,IAAI6L,mBAAmB,EAAE;cACvB,IAAI,CAACJ,OAAO,CAAC,CAAC;cACdI,mBAAmB,GAAG,KAAK;YAC7B;YACA,IAAI,CAACT,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;YAC3BuB,UAAU,GAAG,KAAK;YAClB,IAAI,IAAI,CAACF,YAAY,EAAE;cACrB,IAAI,CAACA,YAAY,EAAE;YACrB;YAEA,IAAI,CAACd,aAAa,CAAC,IAAI,CAAC;YACxB,IAAI,CAACG,OAAO,CAAC5L,YAAY,CAAC,CAAC;YAE3B,IAAI,IAAI,CAACiL,QAAQ,CAACqC,qBAAqB,IAAI,CAAC,IAAI,CAAC1B,OAAO,CAACvH,oBAAoB,CAAC,CAAC,EAAE;cAC/E,IAAI,IAAI,CAAC8G,MAAM,CAACxD,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC9B,IAAI,CAACiE,OAAO,CAAC5L,YAAY,CAAC,IAAI,CAAC;cACjC;YACF;YACA,IAAI,IAAI,CAACmL,MAAM,CAACxD,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;cAC9B,IAAI,CAACiE,OAAO,CAAC/K,IAAI,CAAC,IAAI,CAAC;cACvB,IAAI,IAAI,CAACoK,QAAQ,CAACsC,WAAW,KAAK,QAAQ,EAAE;gBAC1C,IAAI,CAAC3B,OAAO,CAAC5L,YAAY,CAAC,IAAI,CAAC;cACjC;YACF;UACF,CAAC,MAAM,IAAI,IAAI,CAACkL,GAAG,KAAK,GAAG,EAAE;YAE3B,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACT,8BAA8B,CAACpM,MAAM,EAAE6M,CAAC,EAAE,EAAE;cACnE,IAAI,IAAI,CAACZ,MAAM,CAAChC,QAAQ,CAAC,IAAI,CAACmC,8BAA8B,CAACS,CAAC,CAAC,CAAC,EAAE;gBAChEgB,wBAAwB,GAAG,IAAI;gBAC/B;cACF;YACF;YAEA,IAAI,CAACN,UAAU,IAAIE,wBAAwB,KAAK,EAAE,IAAI,CAACxB,MAAM,CAAChC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC0C,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACV,MAAM,CAAChC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAACyD,qBAAqB,IAAIJ,UAAU,KAAK,CAAC,EAAE;cACzL;cACA;;cAEA,IAAI,CAACP,YAAY,CAAC,GAAG,CAAC;cACtB,IAAI,CAACS,mBAAmB,EAAE;gBACxBA,mBAAmB,GAAG,IAAI;gBAC1B,IAAI,CAACd,OAAO,CAACjJ,kBAAkB,GAAG,IAAI;gBACtC,IAAI,CAAC8I,aAAa,CAAC,IAAI,CAAC;gBACxB,IAAI,CAAClM,MAAM,CAAC,CAAC;cACf;YACF,CAAC,MAAM;cACL;cACA;;cAEA;cACA,IAAI,IAAI,CAAC4L,MAAM,CAAChC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAC7B,IAAI,CAACyC,OAAO,CAACjJ,kBAAkB,GAAG,IAAI;cACxC;cACA,IAAI,IAAI,CAACwI,MAAM,CAACxD,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC9B;gBACA,IAAI,CAACuD,GAAG,GAAG,IAAI,CAACC,MAAM,CAAClL,IAAI,CAAC,CAAC;gBAC7B,IAAI,CAACgM,YAAY,CAAC,IAAI,CAAC;cACzB,CAAC,MAAM;gBACL;gBACA,IAAI,CAACA,YAAY,CAAC,GAAG,CAAC;cACxB;YACF;UACF,CAAC,MAAM,IAAI,IAAI,CAACf,GAAG,KAAK,GAAG,IAAI,IAAI,CAACA,GAAG,KAAK,IAAI,EAAE;YAChD,IAAIsC,kBAAkB,GAAGP,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,IAAI;YACpE,IAAI,CAACb,mBAAmB,CAACoB,kBAAkB,IAAInB,YAAY,CAAC;YAC5D,IAAI,CAACJ,YAAY,CAAC,IAAI,CAACf,GAAG,GAAG,IAAI,CAACK,SAAS,CAAC,IAAI,CAACL,GAAG,CAAC,CAAC;YACtD,IAAI,CAACO,aAAa,CAAC,IAAI,CAAC;UAC1B,CAAC,MAAM,IAAI,IAAI,CAACP,GAAG,KAAK,GAAG,EAAE;YAC3B6B,wBAAwB,GAAG,KAAK;YAChC,IAAIP,UAAU,KAAK,CAAC,EAAE;cACpB,IAAIE,mBAAmB,EAAE;gBACvB,IAAI,CAACJ,OAAO,CAAC,CAAC;gBACdI,mBAAmB,GAAG,KAAK;cAC7B;cACAE,qBAAqB,GAAG,KAAK;cAC7B,IAAI,CAACX,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;cAC3B,IAAI,CAACO,aAAa,CAAC,IAAI,CAAC;;cAExB;cACA;cACA;cACA;cACA,IAAI,IAAI,CAACN,MAAM,CAACxD,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC9B,IAAI,CAACiE,OAAO,CAAC5L,YAAY,CAAC,CAAC;cAC7B;YACF,CAAC,MAAM;cACL,IAAI,CAACiM,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;cAC3B,IAAI,CAACO,aAAa,CAAC,IAAI,CAAC;cACxB,IAAI,CAACG,OAAO,CAACjJ,kBAAkB,GAAG,IAAI;YACxC;UACF,CAAC,MAAM,IAAI,IAAI,CAACuI,GAAG,KAAK,GAAG,EAAE;YAAE;YAC7B,IAAI,IAAI,CAACC,MAAM,CAAChC,QAAQ,CAAC,KAAK,CAAC,EAAE;cAC/B,IAAI,CAAC8C,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;cAC3B,IAAI,CAACO,aAAa,CAAC,CAAC;cACpBe,UAAU,EAAE;cACZ,IAAI,CAACjN,MAAM,CAAC,CAAC;cACb,IAAI,CAAC2L,GAAG,GAAG,IAAI,CAACC,MAAM,CAAClL,IAAI,CAAC,CAAC;cAC7B,IAAI,IAAI,CAACiL,GAAG,KAAK,GAAG,IAAI,IAAI,CAACA,GAAG,KAAK,GAAG,IAAI,IAAI,CAACA,GAAG,KAAK,IAAI,EAAE;gBAC7D,IAAI,CAACC,MAAM,CAAC5D,IAAI,CAAC,CAAC;cACpB,CAAC,MAAM,IAAI,IAAI,CAAC2D,GAAG,EAAE;gBACnB,IAAI,CAACe,YAAY,CAAC,IAAI,CAACf,GAAG,GAAG,IAAI,CAACK,SAAS,CAAC,GAAG,CAAC,CAAC;gBACjD,IAAIiB,UAAU,EAAE;kBACdA,UAAU,EAAE;kBACZ,IAAI,CAACF,OAAO,CAAC,CAAC;gBAChB;cACF;YACF,CAAC,MAAM;cACL,IAAImB,YAAY,GAAG,KAAK;cACxB,IAAI,IAAI,CAACtC,MAAM,CAAChC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBAChC;gBACAsE,YAAY,GAAG,IAAI;cACrB;cACA,IAAI,CAACrB,mBAAmB,CAACC,YAAY,IAAIoB,YAAY,CAAC;cACtD,IAAI,CAACxB,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;;cAE3B;cACA,IAAIwB,mBAAmB,IAAIO,WAAW,KAAK,GAAG,IAAI,IAAI,CAAChC,QAAQ,CAACyC,0BAA0B,EAAE;gBAC1F,IAAI,CAAC9B,OAAO,CAAC5L,YAAY,CAAC,CAAC;gBAC3B6M,aAAa,GAAG,IAAI;cACtB,CAAC,MAAM;gBACL,IAAI,CAACpB,aAAa,CAAC,CAAC;gBACpBe,UAAU,EAAE;gBACZ,IAAI,CAACjN,MAAM,CAAC,CAAC;cACf;YACF;UACF,CAAC,MAAM,IAAI,IAAI,CAAC2L,GAAG,KAAK,GAAG,EAAE;YAC3B,IAAIsB,UAAU,EAAE;cACdA,UAAU,EAAE;cACZ,IAAI,CAACF,OAAO,CAAC,CAAC;YAChB;YACA,IAAIO,aAAa,IAAI,IAAI,CAAC1B,MAAM,CAACxD,IAAI,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAACsD,QAAQ,CAACyC,0BAA0B,EAAE;cAC3Fb,aAAa,GAAG,KAAK;cACrB,IAAI,CAACP,OAAO,CAAC,CAAC;cACd,IAAI,CAACV,OAAO,CAAC5L,YAAY,CAAC,CAAC;YAC7B;YACA,IAAI,CAACiM,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;UAC7B,CAAC,MAAM,IAAI,IAAI,CAACA,GAAG,KAAK,GAAG,EAAE;YAC3B,IAAI,CAACe,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;YAC3B,IAAI,CAACO,aAAa,CAAC,IAAI,CAAC;YACxB,IAAI,IAAI,CAACR,QAAQ,CAACyC,0BAA0B,KAAK,CAAChB,mBAAmB,IAAIG,aAAa,CAAC,IAAIL,UAAU,KAAK,CAAC,IAAI,CAACI,qBAAqB,EAAE;cACrI,IAAI,CAAChB,OAAO,CAAC5L,YAAY,CAAC,CAAC;YAC7B,CAAC,MAAM;cACL,IAAI,CAAC4L,OAAO,CAACjJ,kBAAkB,GAAG,IAAI;YACxC;UACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAACuI,GAAG,KAAK,GAAG,IAAI,IAAI,CAACA,GAAG,KAAK,GAAG,IAAI,IAAI,CAACA,GAAG,KAAK,GAAG,KAAK,CAACwB,mBAAmB,IAAIF,UAAU,KAAK,CAAC,EAAE;YACjH;YACA,IAAI,IAAI,CAACvB,QAAQ,CAAC0C,uBAAuB,EAAE;cACzC,IAAI,CAAC/B,OAAO,CAACjJ,kBAAkB,GAAG,IAAI;cACtC,IAAI,CAACsJ,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;cAC3B,IAAI,CAACU,OAAO,CAACjJ,kBAAkB,GAAG,IAAI;YACxC,CAAC,MAAM;cACL,IAAI,CAACsJ,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;cAC3B,IAAI,CAACO,aAAa,CAAC,CAAC;cACpB;cACA,IAAI,IAAI,CAACP,GAAG,IAAIN,cAAc,CAAC3C,IAAI,CAAC,IAAI,CAACiD,GAAG,CAAC,EAAE;gBAC7C,IAAI,CAACA,GAAG,GAAG,EAAE;cACf;YACF;UACF,CAAC,MAAM,IAAI,IAAI,CAACA,GAAG,KAAK,GAAG,EAAE;YAC3B,IAAI,CAACe,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;UAC7B,CAAC,MAAM,IAAI,IAAI,CAACA,GAAG,KAAK,GAAG,EAAE;YAC3B,IAAI,CAACkB,mBAAmB,CAACC,YAAY,CAAC;YACtC,IAAI,CAACJ,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;UAC7B,CAAC,MAAM,IAAI,IAAI,CAACA,GAAG,KAAK,GAAG,EAAE;YAAE;YAC7B,IAAI,CAACO,aAAa,CAAC,CAAC;YACpB,IAAI,CAACQ,YAAY,CAAC,GAAG,CAAC;YACtB,IAAIrB,cAAc,CAAC3C,IAAI,CAAC,IAAI,CAACiD,GAAG,CAAC,EAAE;cACjC,IAAI,CAACA,GAAG,GAAG,EAAE;YACf;UACF,CAAC,MAAM,IAAI,IAAI,CAACA,GAAG,KAAK,GAAG,IAAI,CAAC,IAAI,CAACC,MAAM,CAAChC,QAAQ,CAAC,IAAI,CAAC,EAAE;YAAE;YAC5D,IAAI,CAACyC,OAAO,CAACjJ,kBAAkB,GAAG,IAAI;YACtC,IAAI,CAACsJ,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;UAC7B,CAAC,MAAM;YACL,IAAI0C,kBAAkB,GAAGX,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,IAAI;YACpE,IAAI,CAACb,mBAAmB,CAACwB,kBAAkB,IAAIvB,YAAY,CAAC;YAC5D,IAAI,CAACJ,YAAY,CAAC,IAAI,CAACf,GAAG,CAAC;YAE3B,IAAI,CAAC,IAAI,CAACU,OAAO,CAAC3I,kBAAkB,CAAC,CAAC,IAAI,IAAI,CAACkI,MAAM,CAACxD,IAAI,CAAC,CAAC,KAAK,IAAI,IAAIoF,wBAAwB,EAAE;cACjG,IAAI,CAACnB,OAAO,CAAC5L,YAAY,CAAC,CAAC;YAC7B;UACF;QACF;QAEA,IAAI6N,SAAS,GAAG,IAAI,CAACjC,OAAO,CAAC1I,QAAQ,CAACC,GAAG,CAAC;QAE1C,OAAO0K,SAAS;MAClB,CAAC;MAED7P,MAAM,CAAC2G,OAAO,CAACwF,UAAU,GAAGA,UAAU;;MAGtC;IAAM,CAAC,IACP;IACA,KAAO,UAASnM,MAAM,EAAEiM,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAI4D,WAAW,GAAI5D,mBAAmB,CAAC,CAAC,CAAC,CAACtF,OAAQ;MAElD,SAASA,OAAOA,CAACxD,OAAO,EAAE;QACxB0M,WAAW,CAACC,IAAI,CAAC,IAAI,EAAE3M,OAAO,EAAE,KAAK,CAAC;QAEtC,IAAI,CAACsM,0BAA0B,GAAG,IAAI,CAACzI,YAAY,CAAC,4BAA4B,EAAE,IAAI,CAAC;QACvF,IAAI,CAACqI,qBAAqB,GAAG,IAAI,CAACrI,YAAY,CAAC,uBAAuB,EAAE,IAAI,CAAC;QAC7E,IAAI+I,+BAA+B,GAAG,IAAI,CAAC/I,YAAY,CAAC,iCAAiC,CAAC;QAC1F,IAAI,CAAC0I,uBAAuB,GAAG,IAAI,CAAC1I,YAAY,CAAC,yBAAyB,CAAC,IAAI+I,+BAA+B;QAE9G,IAAIC,iBAAiB,GAAG,IAAI,CAAC1I,mBAAmB,CAAC,aAAa,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;QAChI,IAAI,CAACgI,WAAW,GAAG,UAAU;QAC7B,KAAK,IAAIW,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGD,iBAAiB,CAAC/O,MAAM,EAAEgP,EAAE,EAAE,EAAE;UACpD,IAAID,iBAAiB,CAACC,EAAE,CAAC,KAAK,QAAQ,EAAE;YACtC;YACA,IAAI,CAACX,WAAW,GAAG,UAAU;UAC/B,CAAC,MAAM;YACL,IAAI,CAACA,WAAW,GAAGU,iBAAiB,CAACC,EAAE,CAAC;UAC1C;QACF;MACF;MACAtJ,OAAO,CAAChG,SAAS,GAAG,IAAIkP,WAAW,CAAC,CAAC;MAIrC9P,MAAM,CAAC2G,OAAO,CAACC,OAAO,GAAGA,OAAO;;MAGhC;IAAM;IACN,UAAY;IACZ;IACA,SAAU;IACV;IAAU,IAAIuJ,wBAAwB,GAAG,CAAC,CAAC;IAC3C;IACA,SAAU;IACV;IAAU,SAASjE,mBAAmBA,CAACkE,QAAQ,EAAE;MACjD,SAAW;MACX,QAAW,IAAIC,YAAY,GAAGF,wBAAwB,CAACC,QAAQ,CAAC;MAChE;MAAW,IAAIC,YAAY,KAAKjK,SAAS,EAAE;QAC3C,QAAY,OAAOiK,YAAY,CAAC1J,OAAO;QACvC;MAAW;MACX,SAAW;MACX;MAAW,IAAI3G,MAAM,GAAGmQ,wBAAwB,CAACC,QAAQ,CAAC,GAAG;QAC7D,SAAY;QACZ,SAAY;QACZ,QAAYzJ,OAAO,EAAE,CAAC;QACtB;MAAW,CAAC;MACZ;MACA,SAAW;MACX;MAAW5G,mBAAmB,CAACqQ,QAAQ,CAAC,CAACpQ,MAAM,EAAEA,MAAM,CAAC2G,OAAO,EAAEuF,mBAAmB,CAAC;MACrF;MACA,SAAW;MACX;MAAW,OAAOlM,MAAM,CAAC2G,OAAO;MAChC;IAAU;IACV;IACA;IACA;IACA,SAAU;IACV,SAAU;IACV,SAAU;IACV;IAAU,IAAI2J,mBAAmB,GAAGpE,mBAAmB,CAAC,EAAE,CAAC;IAC3D;IAAUpM,mBAAmB,GAAGwQ,mBAAmB;IACnD;IACA;EAAS,CAAC,EAAE,CAAC;EAEb,IAAIlE,YAAY,GAAGtM,mBAAmB;EACtC;EACA,IAAI,OAAOyQ,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IAC5C;IACAD,MAAM,CAAC,EAAE,EAAE,YAAW;MAClB,OAAO;QACHnE,YAAY,EAAEA;MAClB,CAAC;IACL,CAAC,CAAC;EACN,CAAC,MAAM,IAAI,OAAOzF,OAAO,KAAK,WAAW,EAAE;IACvC;IACA;IACAA,OAAO,CAACyF,YAAY,GAAGA,YAAY;EACvC,CAAC,MAAM,IAAI,OAAOqE,MAAM,KAAK,WAAW,EAAE;IACtC;IACAA,MAAM,CAACrE,YAAY,GAAGA,YAAY;EACtC,CAAC,MAAM,IAAI,OAAOsE,MAAM,KAAK,WAAW,EAAE;IACtC;IACAA,MAAM,CAACtE,YAAY,GAAGA,YAAY;EACtC;AAEA,CAAC,EAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}