{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../types/errors.mjs';\nimport { createVerifySoftwareTokenClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createVerifySoftwareTokenClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Verifies an OTP code retrieved from an associated authentication app.\n *\n * @param input - The VerifyTOTPSetupInput\n * @throws  -{@link VerifySoftwareTokenException }:\n * Thrown due to an invalid MFA token.\n * @throws  -{@link AuthValidationErrorCode }:\n * Thrown when `code` is not defined.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nfunction verifyTOTPSetup(_x) {\n  return _verifyTOTPSetup.apply(this, arguments);\n}\nfunction _verifyTOTPSetup() {\n  _verifyTOTPSetup = _asyncToGenerator(function* (input) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolEndpoint,\n      userPoolId\n    } = authConfig;\n    const {\n      code,\n      options\n    } = input;\n    assertValidationError(!!code, AuthValidationErrorCode.EmptyVerifyTOTPSetupCode);\n    const {\n      tokens\n    } = yield fetchAuthSession({\n      forceRefresh: false\n    });\n    assertAuthTokens(tokens);\n    const verifySoftwareToken = createVerifySoftwareTokenClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    yield verifySoftwareToken({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.VerifyTOTPSetup)\n    }, {\n      AccessToken: tokens.accessToken.toString(),\n      UserCode: code,\n      FriendlyDeviceName: options?.friendlyDeviceName\n    });\n  });\n  return _verifyTOTPSetup.apply(this, arguments);\n}\nexport { verifyTOTPSetup };", "map": {"version": 3, "names": ["Amplify", "fetchAuthSession", "assertTokenProviderConfig", "AuthAction", "AuthValidationErrorCode", "assertValidationError", "getRegionFromUserPoolId", "assertAuthTokens", "getAuthUserAgentValue", "createVerifySoftwareTokenClient", "createCognitoUserPoolEndpointResolver", "verifyTOTPSetup", "_x", "_verifyTOTPSetup", "apply", "arguments", "_asyncToGenerator", "input", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userPoolEndpoint", "userPoolId", "code", "options", "EmptyVerifyTOTPSetupCode", "tokens", "forceRefresh", "verifySoftwareToken", "endpointResolver", "endpointOverride", "region", "userAgentValue", "VerifyTOTPSetup", "AccessToken", "accessToken", "toString", "UserCode", "FriendlyDeviceName", "friendlyDeviceName"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/verifyTOTPSetup.mjs"], "sourcesContent": ["import { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../types/errors.mjs';\nimport { createVerifySoftwareTokenClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createVerifySoftwareTokenClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Verifies an OTP code retrieved from an associated authentication app.\n *\n * @param input - The VerifyTOTPSetupInput\n * @throws  -{@link VerifySoftwareTokenException }:\n * Thrown due to an invalid MFA token.\n * @throws  -{@link AuthValidationErrorCode }:\n * Thrown when `code` is not defined.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nasync function verifyTOTPSetup(input) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolEndpoint, userPoolId } = authConfig;\n    const { code, options } = input;\n    assertValidationError(!!code, AuthValidationErrorCode.EmptyVerifyTOTPSetupCode);\n    const { tokens } = await fetchAuthSession({ forceRefresh: false });\n    assertAuthTokens(tokens);\n    const verifySoftwareToken = createVerifySoftwareTokenClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    await verifySoftwareToken({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.VerifyTOTPSetup),\n    }, {\n        AccessToken: tokens.accessToken.toString(),\n        UserCode: code,\n        FriendlyDeviceName: options?.friendlyDeviceName,\n    });\n}\n\nexport { verifyTOTPSetup };\n"], "mappings": ";AAAA,SAASA,OAAO,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC7D,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,qBAAqB;AAC5B,SAASC,+BAA+B,QAAQ,0GAA0G;AAC1J,SAASC,qCAAqC,QAAQ,wDAAwD;;AAE9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,SAUeC,eAAeA,CAAAC,EAAA;EAAA,OAAAC,gBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,iBAAA;EAAAA,gBAAA,GAAAG,iBAAA,CAA9B,WAA+BC,KAAK,EAAE;IAClC,MAAMC,UAAU,GAAGlB,OAAO,CAACmB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDnB,yBAAyB,CAACgB,UAAU,CAAC;IACrC,MAAM;MAAEI,gBAAgB;MAAEC;IAAW,CAAC,GAAGL,UAAU;IACnD,MAAM;MAAEM,IAAI;MAAEC;IAAQ,CAAC,GAAGR,KAAK;IAC/BZ,qBAAqB,CAAC,CAAC,CAACmB,IAAI,EAAEpB,uBAAuB,CAACsB,wBAAwB,CAAC;IAC/E,MAAM;MAAEC;IAAO,CAAC,SAAS1B,gBAAgB,CAAC;MAAE2B,YAAY,EAAE;IAAM,CAAC,CAAC;IAClErB,gBAAgB,CAACoB,MAAM,CAAC;IACxB,MAAME,mBAAmB,GAAGpB,+BAA+B,CAAC;MACxDqB,gBAAgB,EAAEpB,qCAAqC,CAAC;QACpDqB,gBAAgB,EAAET;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMO,mBAAmB,CAAC;MACtBG,MAAM,EAAE1B,uBAAuB,CAACiB,UAAU,CAAC;MAC3CU,cAAc,EAAEzB,qBAAqB,CAACL,UAAU,CAAC+B,eAAe;IACpE,CAAC,EAAE;MACCC,WAAW,EAAER,MAAM,CAACS,WAAW,CAACC,QAAQ,CAAC,CAAC;MAC1CC,QAAQ,EAAEd,IAAI;MACde,kBAAkB,EAAEd,OAAO,EAAEe;IACjC,CAAC,CAAC;EACN,CAAC;EAAA,OAAA3B,gBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}