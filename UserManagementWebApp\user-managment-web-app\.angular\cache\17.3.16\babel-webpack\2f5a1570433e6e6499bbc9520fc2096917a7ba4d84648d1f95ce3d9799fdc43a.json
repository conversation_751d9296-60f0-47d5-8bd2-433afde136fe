{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { parseJsonError, parseJsonBody } from '@aws-amplify/core/internals/aws-client-utils';\nimport { assertServiceError } from '../../../../../../errors/utils/assertServiceError.mjs';\nimport { AuthError } from '../../../../../../errors/AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createUserPoolDeserializer = () => (/*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (response) {\n    if (response.statusCode >= 300) {\n      const error = yield parseJsonError(response);\n      assertServiceError(error);\n      throw new AuthError({\n        name: error.name,\n        message: error.message,\n        metadata: error.$metadata\n      });\n    }\n    return parseJsonBody(response);\n  });\n  return function (_x) {\n    return _ref.apply(this, arguments);\n  };\n}());\nexport { createUserPoolDeserializer };", "map": {"version": 3, "names": ["parseJsonError", "parseJsonBody", "assertServiceError", "<PERSON>th<PERSON><PERSON><PERSON>", "createUserPoolDeserializer", "_ref", "_asyncToGenerator", "response", "statusCode", "error", "name", "message", "metadata", "$metadata", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/factories/serviceClients/cognitoIdentityProvider/shared/serde/createUserPoolDeserializer.mjs"], "sourcesContent": ["import { parseJsonError, parseJsonBody } from '@aws-amplify/core/internals/aws-client-utils';\nimport { assertServiceError } from '../../../../../../errors/utils/assertServiceError.mjs';\nimport { AuthError } from '../../../../../../errors/AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createUserPoolDeserializer = () => async (response) => {\n    if (response.statusCode >= 300) {\n        const error = await parseJsonError(response);\n        assertServiceError(error);\n        throw new AuthError({\n            name: error.name,\n            message: error.message,\n            metadata: error.$metadata,\n        });\n    }\n    return parseJsonBody(response);\n};\n\nexport { createUserPoolDeserializer };\n"], "mappings": ";AAAA,SAASA,cAAc,EAAEC,aAAa,QAAQ,8CAA8C;AAC5F,SAASC,kBAAkB,QAAQ,uDAAuD;AAC1F,SAASC,SAAS,QAAQ,wCAAwC;;AAElE;AACA;AACA,MAAMC,0BAA0B,GAAGA,CAAA;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAM,WAAOC,QAAQ,EAAK;IACzD,IAAIA,QAAQ,CAACC,UAAU,IAAI,GAAG,EAAE;MAC5B,MAAMC,KAAK,SAAST,cAAc,CAACO,QAAQ,CAAC;MAC5CL,kBAAkB,CAACO,KAAK,CAAC;MACzB,MAAM,IAAIN,SAAS,CAAC;QAChBO,IAAI,EAAED,KAAK,CAACC,IAAI;QAChBC,OAAO,EAAEF,KAAK,CAACE,OAAO;QACtBC,QAAQ,EAAEH,KAAK,CAACI;MACpB,CAAC,CAAC;IACN;IACA,OAAOZ,aAAa,CAACM,QAAQ,CAAC;EAClC,CAAC;EAAA,iBAAAO,EAAA;IAAA,OAAAT,IAAA,CAAAU,KAAA,OAAAC,SAAA;EAAA;AAAA;AAED,SAASZ,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}