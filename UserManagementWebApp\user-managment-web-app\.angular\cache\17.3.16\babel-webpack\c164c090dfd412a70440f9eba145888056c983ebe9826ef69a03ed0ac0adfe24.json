{"ast": null, "code": "import { getDnsSuffix } from '../../clients/endpoints/getDnsSuffix.mjs';\nimport { jitteredBackoff } from '../../clients/middleware/retry/jitteredBackoff.mjs';\nimport { getRetryDecider } from '../../clients/middleware/retry/defaultRetryDecider.mjs';\nimport '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\nimport '../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../utils/retry/retry.mjs';\nimport '../../parseAWSExports.mjs';\nimport 'uuid';\nimport { AmplifyUrl } from '../../utils/amplifyUrl/index.mjs';\nimport '../../singleton/Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport { getAmplifyUserAgent } from '../../Platform/index.mjs';\nimport '../../Platform/types.mjs';\nimport '../../BackgroundProcessManager/types.mjs';\nimport '../../Reachability/Reachability.mjs';\nimport '../../Hub/index.mjs';\nimport '../../utils/sessionListener/index.mjs';\nimport { parseJsonError } from '../../clients/serde/json.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * The service name used to sign requests if the API requires authentication.\n */\nconst SERVICE_NAME = 'mobiletargeting';\n/**\n * The endpoint resolver function that returns the endpoint URL for a given region.\n */\nconst endpointResolver = ({\n  region\n}) => ({\n  url: new AmplifyUrl(`https://pinpoint.${region}.${getDnsSuffix(region)}`)\n});\n/**\n * @internal\n */\nconst defaultConfig = {\n  service: SERVICE_NAME,\n  endpointResolver,\n  retryDecider: getRetryDecider(parseJsonError),\n  computeDelay: jitteredBackoff,\n  userAgentValue: getAmplifyUserAgent()\n};\n/**\n * @internal\n */\nconst getSharedHeaders = () => ({\n  'content-type': 'application/json'\n});\nexport { defaultConfig, getSharedHeaders };", "map": {"version": 3, "names": ["getDnsSuffix", "jittered<PERSON><PERSON>off", "getRetryDecider", "AmplifyUrl", "getAmplifyUserAgent", "parseJsonError", "SERVICE_NAME", "endpointResolver", "region", "url", "defaultConfig", "service", "retryDecider", "computeDelay", "userAgentValue", "getSharedHeaders"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/awsClients/pinpoint/base.mjs"], "sourcesContent": ["import { getDnsSuffix } from '../../clients/endpoints/getDnsSuffix.mjs';\nimport { jitteredBackoff } from '../../clients/middleware/retry/jitteredBackoff.mjs';\nimport { getRetryDecider } from '../../clients/middleware/retry/defaultRetryDecider.mjs';\nimport '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\nimport '../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../utils/retry/retry.mjs';\nimport '../../parseAWSExports.mjs';\nimport 'uuid';\nimport { AmplifyUrl } from '../../utils/amplifyUrl/index.mjs';\nimport '../../singleton/Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport { getAmplifyUserAgent } from '../../Platform/index.mjs';\nimport '../../Platform/types.mjs';\nimport '../../BackgroundProcessManager/types.mjs';\nimport '../../Reachability/Reachability.mjs';\nimport '../../Hub/index.mjs';\nimport '../../utils/sessionListener/index.mjs';\nimport { parseJsonError } from '../../clients/serde/json.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * The service name used to sign requests if the API requires authentication.\n */\nconst SERVICE_NAME = 'mobiletargeting';\n/**\n * The endpoint resolver function that returns the endpoint URL for a given region.\n */\nconst endpointResolver = ({ region }) => ({\n    url: new AmplifyUrl(`https://pinpoint.${region}.${getDnsSuffix(region)}`),\n});\n/**\n * @internal\n */\nconst defaultConfig = {\n    service: SERVICE_NAME,\n    endpointResolver,\n    retryDecider: getRetryDecider(parseJsonError),\n    computeDelay: jitteredBackoff,\n    userAgentValue: getAmplifyUserAgent(),\n};\n/**\n * @internal\n */\nconst getSharedHeaders = () => ({\n    'content-type': 'application/json',\n});\n\nexport { defaultConfig, getSharedHeaders };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,0CAA0C;AACvE,SAASC,eAAe,QAAQ,oDAAoD;AACpF,SAASC,eAAe,QAAQ,wDAAwD;AACxF,OAAO,wBAAwB;AAC/B,OAAO,+BAA+B;AACtC,OAAO,6CAA6C;AACpD,OAAO,6BAA6B;AACpC,OAAO,2BAA2B;AAClC,OAAO,MAAM;AACb,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,OAAO,6CAA6C;AACpD,OAAO,uBAAuB;AAC9B,OAAO,2BAA2B;AAClC,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,OAAO,0BAA0B;AACjC,OAAO,0CAA0C;AACjD,OAAO,qCAAqC;AAC5C,OAAO,qBAAqB;AAC5B,OAAO,uCAAuC;AAC9C,SAASC,cAAc,QAAQ,8BAA8B;;AAE7D;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,iBAAiB;AACtC;AACA;AACA;AACA,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC;AAAO,CAAC,MAAM;EACtCC,GAAG,EAAE,IAAIN,UAAU,CAAC,oBAAoBK,MAAM,IAAIR,YAAY,CAACQ,MAAM,CAAC,EAAE;AAC5E,CAAC,CAAC;AACF;AACA;AACA;AACA,MAAME,aAAa,GAAG;EAClBC,OAAO,EAAEL,YAAY;EACrBC,gBAAgB;EAChBK,YAAY,EAAEV,eAAe,CAACG,cAAc,CAAC;EAC7CQ,YAAY,EAAEZ,eAAe;EAC7Ba,cAAc,EAAEV,mBAAmB,CAAC;AACxC,CAAC;AACD;AACA;AACA;AACA,MAAMW,gBAAgB,GAAGA,CAAA,MAAO;EAC5B,cAAc,EAAE;AACpB,CAAC,CAAC;AAEF,SAASL,aAAa,EAAEK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}