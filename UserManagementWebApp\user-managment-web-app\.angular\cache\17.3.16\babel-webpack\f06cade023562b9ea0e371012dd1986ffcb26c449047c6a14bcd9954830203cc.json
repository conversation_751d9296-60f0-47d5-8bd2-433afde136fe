{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { createMachine, sendUpdate } from 'xstate';\nimport { signInWithRedirect, fetchUserAttributes, autoSignIn } from 'aws-amplify/auth';\nimport { getSignUpInput } from '../utils.mjs';\nimport { runValidators } from '../../../validators/index.mjs';\nimport ACTIONS from '../actions.mjs';\nimport GUARDS from '../guards.mjs';\nimport { getFederatedSignInState } from './utils.mjs';\nconst handleResetPasswordResponse = {\n  onDone: [{\n    actions: 'setCodeDeliveryDetails',\n    target: '#signUpActor.resolved'\n  }],\n  onError: {\n    actions: ['setRemoteError', 'sendUpdate']\n  }\n};\nconst handleAutoSignInResponse = {\n  onDone: [{\n    cond: 'hasCompletedSignIn',\n    actions: 'setNextSignInStep',\n    target: '#signUpActor.fetchUserAttributes'\n  }, {\n    cond: 'shouldConfirmSignInWithNewPassword',\n    actions: 'setNextSignInStep',\n    target: '#signUpActor.resolved'\n  }, {\n    cond: 'shouldResetPasswordFromSignIn',\n    actions: 'setNextSignInStep',\n    target: '#signUpActor.resetPassword'\n  }, {\n    cond: 'shouldConfirmSignUpFromSignIn',\n    actions: 'setNextSignInStep',\n    target: '#signUpActor.resendSignUpCode'\n  }, {\n    actions: ['setNextSignInStep', 'setChallengeName', 'setMissingAttributes', 'setTotpSecretCode', 'setAllowedMfaTypes'],\n    target: '#signUpActor.resolved'\n  }],\n  onError: {\n    actions: 'setRemoteError',\n    target: '#signUpActor.resolved'\n  }\n};\nconst handleFetchUserAttributesResponse = {\n  onDone: [{\n    cond: 'shouldVerifyAttribute',\n    actions: ['setShouldVerifyUserAttributeStep', 'setUnverifiedUserAttributes'],\n    target: '#signUpActor.resolved'\n  }, {\n    actions: 'setConfirmAttributeCompleteStep',\n    target: '#signUpActor.resolved'\n  }],\n  onError: {\n    actions: 'setConfirmAttributeCompleteStep',\n    target: '#signUpActor.resolved'\n  }\n};\nfunction signUpActor({\n  services\n}) {\n  return createMachine({\n    id: 'signUpActor',\n    initial: 'init',\n    predictableActionArguments: true,\n    states: {\n      init: {\n        always: [{\n          cond: 'shouldConfirmSignUp',\n          target: 'confirmSignUp'\n        }, {\n          target: 'signUp'\n        }]\n      },\n      autoSignIn: {\n        tags: 'pending',\n        invoke: {\n          src: 'autoSignIn',\n          ...handleAutoSignInResponse\n        }\n      },\n      fetchUserAttributes: {\n        invoke: {\n          src: 'fetchUserAttributes',\n          ...handleFetchUserAttributesResponse\n        }\n      },\n      federatedSignIn: getFederatedSignInState('signUp'),\n      resetPassword: {\n        invoke: {\n          src: 'resetPassword',\n          ...handleResetPasswordResponse\n        }\n      },\n      resendSignUpCode: {\n        tags: 'pending',\n        entry: 'sendUpdate',\n        exit: 'sendUpdate',\n        invoke: {\n          src: 'resendSignUpCode',\n          onDone: {\n            actions: ['setCodeDeliveryDetails', 'sendUpdate'],\n            target: '#signUpActor.confirmSignUp'\n          },\n          onError: [{\n            cond: 'isUserAlreadyConfirmed',\n            target: '#signUpActor.resolved'\n          }, {\n            actions: ['setRemoteError', 'sendUpdate']\n          }]\n        }\n      },\n      signUp: {\n        type: 'parallel',\n        exit: 'clearTouched',\n        on: {\n          FEDERATED_SIGN_IN: {\n            target: 'federatedSignIn'\n          }\n        },\n        states: {\n          validation: {\n            initial: 'pending',\n            states: {\n              pending: {\n                invoke: {\n                  src: 'validateSignUp',\n                  onDone: {\n                    actions: 'clearValidationError',\n                    target: 'valid'\n                  },\n                  onError: {\n                    actions: 'setFieldErrors',\n                    target: 'invalid'\n                  }\n                }\n              },\n              valid: {\n                entry: 'sendUpdate'\n              },\n              invalid: {\n                entry: 'sendUpdate'\n              }\n            },\n            on: {\n              BLUR: {\n                actions: 'handleBlur',\n                target: '.pending'\n              },\n              CHANGE: {\n                actions: 'handleInput',\n                target: '.pending'\n              }\n            }\n          },\n          submission: {\n            initial: 'idle',\n            states: {\n              idle: {\n                entry: ['sendUpdate'],\n                on: {\n                  SUBMIT: {\n                    actions: 'handleSubmit',\n                    target: 'validate'\n                  }\n                }\n              },\n              validate: {\n                entry: 'sendUpdate',\n                invoke: {\n                  src: 'validateSignUp',\n                  onDone: {\n                    target: 'handleSignUp',\n                    actions: 'clearValidationError'\n                  },\n                  onError: {\n                    actions: 'setFieldErrors',\n                    target: 'idle'\n                  }\n                }\n              },\n              handleSignUp: {\n                tags: 'pending',\n                entry: ['setUsernameSignUp', 'clearError'],\n                exit: 'sendUpdate',\n                invoke: {\n                  src: 'handleSignUp',\n                  onDone: [{\n                    cond: 'hasCompletedSignUp',\n                    actions: 'setNextSignUpStep',\n                    target: '#signUpActor.resolved'\n                  }, {\n                    cond: 'shouldAutoSignIn',\n                    actions: 'setNextSignUpStep',\n                    target: '#signUpActor.autoSignIn'\n                  }, {\n                    actions: ['setCodeDeliveryDetails', 'setNextSignUpStep'],\n                    target: '#signUpActor.init'\n                  }],\n                  onError: {\n                    actions: ['sendUpdate', 'setRemoteError'],\n                    target: 'idle'\n                  }\n                }\n              }\n            }\n          }\n        }\n      },\n      confirmSignUp: {\n        initial: 'edit',\n        entry: 'sendUpdate',\n        states: {\n          edit: {\n            on: {\n              SUBMIT: {\n                actions: 'handleSubmit',\n                target: 'submit'\n              },\n              CHANGE: {\n                actions: 'handleInput'\n              },\n              BLUR: {\n                actions: 'handleBlur'\n              },\n              RESEND: '#signUpActor.resendSignUpCode'\n            }\n          },\n          submit: {\n            tags: 'pending',\n            entry: ['clearError', 'sendUpdate'],\n            invoke: {\n              src: 'confirmSignUp',\n              onDone: [{\n                cond: 'shouldAutoSignIn',\n                actions: ['setNextSignUpStep', 'clearFormValues'],\n                target: '#signUpActor.autoSignIn'\n              }, {\n                actions: 'setNextSignUpStep',\n                target: '#signUpActor.init'\n              }],\n              onError: {\n                actions: ['setRemoteError', 'sendUpdate'],\n                target: 'edit'\n              }\n            }\n          }\n        }\n      },\n      resolved: {\n        type: 'final',\n        data: context => ({\n          challengeName: context.challengeName,\n          missingAttributes: context.missingAttributes,\n          remoteError: context.remoteError,\n          step: context.step,\n          totpSecretCode: context.totpSecretCode,\n          username: context.username,\n          unverifiedUserAttributes: context.unverifiedUserAttributes,\n          allowedMfaTypes: context.allowedMfaTypes\n        })\n      }\n    }\n  }, {\n    // sendUpdate is a HOC\n    actions: {\n      ...ACTIONS,\n      sendUpdate: sendUpdate()\n    },\n    guards: GUARDS,\n    services: {\n      autoSignIn() {\n        return autoSignIn();\n      },\n      fetchUserAttributes() {\n        return _asyncToGenerator(function* () {\n          return fetchUserAttributes();\n        })();\n      },\n      confirmSignUp({\n        formValues,\n        username\n      }) {\n        const {\n          confirmation_code: confirmationCode\n        } = formValues;\n        const input = {\n          username,\n          confirmationCode\n        };\n        return services.handleConfirmSignUp(input);\n      },\n      resendSignUpCode({\n        username\n      }) {\n        return services.handleResendSignUpCode({\n          username\n        });\n      },\n      signInWithRedirect(_, {\n        data\n      }) {\n        return signInWithRedirect(data);\n      },\n      handleSignUp(context) {\n        const {\n          formValues,\n          loginMechanisms,\n          username\n        } = context;\n        const loginMechanism = loginMechanisms[0];\n        const input = getSignUpInput(username, formValues, loginMechanism);\n        return services.handleSignUp(input);\n      },\n      validateSignUp(context) {\n        return _asyncToGenerator(function* () {\n          // This needs to exist in the machine to reference new `services`\n          return runValidators(context.formValues, context.touched, context.passwordSettings, [\n          // Validation of password\n          services.validateFormPassword,\n          // Validation for default form fields\n          services.validateConfirmPassword, services.validatePreferredUsername,\n          // Validation for any custom Sign Up fields\n          services.validateCustomSignUp]);\n        })();\n      }\n    }\n  });\n}\nexport { signUpActor };", "map": {"version": 3, "names": ["createMachine", "sendUpdate", "signInWithRedirect", "fetchUserAttributes", "autoSignIn", "getSignUpInput", "runValidators", "ACTIONS", "GUARDS", "getFederatedSignInState", "handleResetPasswordResponse", "onDone", "actions", "target", "onError", "handleAutoSignInResponse", "cond", "handleFetchUserAttributesResponse", "signUpActor", "services", "id", "initial", "predictableActionArguments", "states", "init", "always", "tags", "invoke", "src", "federatedSignIn", "resetPassword", "resendSignUpCode", "entry", "exit", "signUp", "type", "on", "FEDERATED_SIGN_IN", "validation", "pending", "valid", "invalid", "BLUR", "CHANGE", "submission", "idle", "SUBMIT", "validate", "handleSignUp", "confirmSignUp", "edit", "RESEND", "submit", "resolved", "data", "context", "challenge<PERSON>ame", "missingAttributes", "remoteError", "step", "totpSecretCode", "username", "unverifiedUserAttributes", "allowedMfaTypes", "guards", "_asyncToGenerator", "formValues", "confirmation_code", "confirmationCode", "input", "handleConfirmSignUp", "handleResendSignUpCode", "_", "loginMechanisms", "loginMechanism", "validateSignUp", "touched", "passwordSettings", "validateFormPassword", "validateConfirmPassword", "validatePreferredUsername", "validateCustomSignUp"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/machines/authenticator/actors/signUp.mjs"], "sourcesContent": ["import { createMachine, sendUpdate } from 'xstate';\nimport { signInWithRedirect, fetchUserAttributes, autoSignIn } from 'aws-amplify/auth';\nimport { getSignUpInput } from '../utils.mjs';\nimport { runValidators } from '../../../validators/index.mjs';\nimport ACTIONS from '../actions.mjs';\nimport GUARDS from '../guards.mjs';\nimport { getFederatedSignInState } from './utils.mjs';\n\nconst handleResetPasswordResponse = {\n    onDone: [\n        { actions: 'setCodeDeliveryDetails', target: '#signUpActor.resolved' },\n    ],\n    onError: { actions: ['setRemoteError', 'sendUpdate'] },\n};\nconst handleAutoSignInResponse = {\n    onDone: [\n        {\n            cond: 'hasCompletedSignIn',\n            actions: 'setNextSignInStep',\n            target: '#signUpActor.fetchUserAttributes',\n        },\n        {\n            cond: 'shouldConfirmSignInWithNewPassword',\n            actions: 'setNextSignInStep',\n            target: '#signUpActor.resolved',\n        },\n        {\n            cond: 'shouldResetPasswordFromSignIn',\n            actions: 'setNextSignInStep',\n            target: '#signUpActor.resetPassword',\n        },\n        {\n            cond: 'shouldConfirmSignUpFromSignIn',\n            actions: 'setNextSignInStep',\n            target: '#signUpActor.resendSignUpCode',\n        },\n        {\n            actions: [\n                'setNextSignInStep',\n                'setChallengeName',\n                'setMissingAttributes',\n                'setTotpSecretCode',\n                'setAllowedMfaTypes',\n            ],\n            target: '#signUpActor.resolved',\n        },\n    ],\n    onError: {\n        actions: 'setRemoteError',\n        target: '#signUpActor.resolved',\n    },\n};\nconst handleFetchUserAttributesResponse = {\n    onDone: [\n        {\n            cond: 'shouldVerifyAttribute',\n            actions: [\n                'setShouldVerifyUserAttributeStep',\n                'setUnverifiedUserAttributes',\n            ],\n            target: '#signUpActor.resolved',\n        },\n        {\n            actions: 'setConfirmAttributeCompleteStep',\n            target: '#signUpActor.resolved',\n        },\n    ],\n    onError: {\n        actions: 'setConfirmAttributeCompleteStep',\n        target: '#signUpActor.resolved',\n    },\n};\nfunction signUpActor({ services }) {\n    return createMachine({\n        id: 'signUpActor',\n        initial: 'init',\n        predictableActionArguments: true,\n        states: {\n            init: {\n                always: [\n                    { cond: 'shouldConfirmSignUp', target: 'confirmSignUp' },\n                    { target: 'signUp' },\n                ],\n            },\n            autoSignIn: {\n                tags: 'pending',\n                invoke: { src: 'autoSignIn', ...handleAutoSignInResponse },\n            },\n            fetchUserAttributes: {\n                invoke: {\n                    src: 'fetchUserAttributes',\n                    ...handleFetchUserAttributesResponse,\n                },\n            },\n            federatedSignIn: getFederatedSignInState('signUp'),\n            resetPassword: {\n                invoke: { src: 'resetPassword', ...handleResetPasswordResponse },\n            },\n            resendSignUpCode: {\n                tags: 'pending',\n                entry: 'sendUpdate',\n                exit: 'sendUpdate',\n                invoke: {\n                    src: 'resendSignUpCode',\n                    onDone: {\n                        actions: ['setCodeDeliveryDetails', 'sendUpdate'],\n                        target: '#signUpActor.confirmSignUp',\n                    },\n                    onError: [\n                        {\n                            cond: 'isUserAlreadyConfirmed',\n                            target: '#signUpActor.resolved',\n                        },\n                        { actions: ['setRemoteError', 'sendUpdate'] },\n                    ],\n                },\n            },\n            signUp: {\n                type: 'parallel',\n                exit: 'clearTouched',\n                on: {\n                    FEDERATED_SIGN_IN: { target: 'federatedSignIn' },\n                },\n                states: {\n                    validation: {\n                        initial: 'pending',\n                        states: {\n                            pending: {\n                                invoke: {\n                                    src: 'validateSignUp',\n                                    onDone: {\n                                        actions: 'clearValidationError',\n                                        target: 'valid',\n                                    },\n                                    onError: { actions: 'setFieldErrors', target: 'invalid' },\n                                },\n                            },\n                            valid: { entry: 'sendUpdate' },\n                            invalid: { entry: 'sendUpdate' },\n                        },\n                        on: {\n                            BLUR: { actions: 'handleBlur', target: '.pending' },\n                            CHANGE: { actions: 'handleInput', target: '.pending' },\n                        },\n                    },\n                    submission: {\n                        initial: 'idle',\n                        states: {\n                            idle: {\n                                entry: ['sendUpdate'],\n                                on: {\n                                    SUBMIT: { actions: 'handleSubmit', target: 'validate' },\n                                },\n                            },\n                            validate: {\n                                entry: 'sendUpdate',\n                                invoke: {\n                                    src: 'validateSignUp',\n                                    onDone: {\n                                        target: 'handleSignUp',\n                                        actions: 'clearValidationError',\n                                    },\n                                    onError: { actions: 'setFieldErrors', target: 'idle' },\n                                },\n                            },\n                            handleSignUp: {\n                                tags: 'pending',\n                                entry: ['setUsernameSignUp', 'clearError'],\n                                exit: 'sendUpdate',\n                                invoke: {\n                                    src: 'handleSignUp',\n                                    onDone: [\n                                        {\n                                            cond: 'hasCompletedSignUp',\n                                            actions: 'setNextSignUpStep',\n                                            target: '#signUpActor.resolved',\n                                        },\n                                        {\n                                            cond: 'shouldAutoSignIn',\n                                            actions: 'setNextSignUpStep',\n                                            target: '#signUpActor.autoSignIn',\n                                        },\n                                        {\n                                            actions: [\n                                                'setCodeDeliveryDetails',\n                                                'setNextSignUpStep',\n                                            ],\n                                            target: '#signUpActor.init',\n                                        },\n                                    ],\n                                    onError: {\n                                        actions: ['sendUpdate', 'setRemoteError'],\n                                        target: 'idle',\n                                    },\n                                },\n                            },\n                        },\n                    },\n                },\n            },\n            confirmSignUp: {\n                initial: 'edit',\n                entry: 'sendUpdate',\n                states: {\n                    edit: {\n                        on: {\n                            SUBMIT: { actions: 'handleSubmit', target: 'submit' },\n                            CHANGE: { actions: 'handleInput' },\n                            BLUR: { actions: 'handleBlur' },\n                            RESEND: '#signUpActor.resendSignUpCode',\n                        },\n                    },\n                    submit: {\n                        tags: 'pending',\n                        entry: ['clearError', 'sendUpdate'],\n                        invoke: {\n                            src: 'confirmSignUp',\n                            onDone: [\n                                {\n                                    cond: 'shouldAutoSignIn',\n                                    actions: ['setNextSignUpStep', 'clearFormValues'],\n                                    target: '#signUpActor.autoSignIn',\n                                },\n                                {\n                                    actions: 'setNextSignUpStep',\n                                    target: '#signUpActor.init',\n                                },\n                            ],\n                            onError: {\n                                actions: ['setRemoteError', 'sendUpdate'],\n                                target: 'edit',\n                            },\n                        },\n                    },\n                },\n            },\n            resolved: {\n                type: 'final',\n                data: (context) => ({\n                    challengeName: context.challengeName,\n                    missingAttributes: context.missingAttributes,\n                    remoteError: context.remoteError,\n                    step: context.step,\n                    totpSecretCode: context.totpSecretCode,\n                    username: context.username,\n                    unverifiedUserAttributes: context.unverifiedUserAttributes,\n                    allowedMfaTypes: context.allowedMfaTypes,\n                }),\n            },\n        },\n    }, {\n        // sendUpdate is a HOC\n        actions: { ...ACTIONS, sendUpdate: sendUpdate() },\n        guards: GUARDS,\n        services: {\n            autoSignIn() {\n                return autoSignIn();\n            },\n            async fetchUserAttributes() {\n                return fetchUserAttributes();\n            },\n            confirmSignUp({ formValues, username }) {\n                const { confirmation_code: confirmationCode } = formValues;\n                const input = { username, confirmationCode };\n                return services.handleConfirmSignUp(input);\n            },\n            resendSignUpCode({ username }) {\n                return services.handleResendSignUpCode({ username });\n            },\n            signInWithRedirect(_, { data }) {\n                return signInWithRedirect(data);\n            },\n            handleSignUp(context) {\n                const { formValues, loginMechanisms, username } = context;\n                const loginMechanism = loginMechanisms[0];\n                const input = getSignUpInput(username, formValues, loginMechanism);\n                return services.handleSignUp(input);\n            },\n            async validateSignUp(context) {\n                // This needs to exist in the machine to reference new `services`\n                return runValidators(context.formValues, context.touched, context.passwordSettings, [\n                    // Validation of password\n                    services.validateFormPassword,\n                    // Validation for default form fields\n                    services.validateConfirmPassword,\n                    services.validatePreferredUsername,\n                    // Validation for any custom Sign Up fields\n                    services.validateCustomSignUp,\n                ]);\n            },\n        },\n    });\n}\n\nexport { signUpActor };\n"], "mappings": ";AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,QAAQ;AAClD,SAASC,kBAAkB,EAAEC,mBAAmB,EAAEC,UAAU,QAAQ,kBAAkB;AACtF,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,OAAOC,OAAO,MAAM,gBAAgB;AACpC,OAAOC,MAAM,MAAM,eAAe;AAClC,SAASC,uBAAuB,QAAQ,aAAa;AAErD,MAAMC,2BAA2B,GAAG;EAChCC,MAAM,EAAE,CACJ;IAAEC,OAAO,EAAE,wBAAwB;IAAEC,MAAM,EAAE;EAAwB,CAAC,CACzE;EACDC,OAAO,EAAE;IAAEF,OAAO,EAAE,CAAC,gBAAgB,EAAE,YAAY;EAAE;AACzD,CAAC;AACD,MAAMG,wBAAwB,GAAG;EAC7BJ,MAAM,EAAE,CACJ;IACIK,IAAI,EAAE,oBAAoB;IAC1BJ,OAAO,EAAE,mBAAmB;IAC5BC,MAAM,EAAE;EACZ,CAAC,EACD;IACIG,IAAI,EAAE,oCAAoC;IAC1CJ,OAAO,EAAE,mBAAmB;IAC5BC,MAAM,EAAE;EACZ,CAAC,EACD;IACIG,IAAI,EAAE,+BAA+B;IACrCJ,OAAO,EAAE,mBAAmB;IAC5BC,MAAM,EAAE;EACZ,CAAC,EACD;IACIG,IAAI,EAAE,+BAA+B;IACrCJ,OAAO,EAAE,mBAAmB;IAC5BC,MAAM,EAAE;EACZ,CAAC,EACD;IACID,OAAO,EAAE,CACL,mBAAmB,EACnB,kBAAkB,EAClB,sBAAsB,EACtB,mBAAmB,EACnB,oBAAoB,CACvB;IACDC,MAAM,EAAE;EACZ,CAAC,CACJ;EACDC,OAAO,EAAE;IACLF,OAAO,EAAE,gBAAgB;IACzBC,MAAM,EAAE;EACZ;AACJ,CAAC;AACD,MAAMI,iCAAiC,GAAG;EACtCN,MAAM,EAAE,CACJ;IACIK,IAAI,EAAE,uBAAuB;IAC7BJ,OAAO,EAAE,CACL,kCAAkC,EAClC,6BAA6B,CAChC;IACDC,MAAM,EAAE;EACZ,CAAC,EACD;IACID,OAAO,EAAE,iCAAiC;IAC1CC,MAAM,EAAE;EACZ,CAAC,CACJ;EACDC,OAAO,EAAE;IACLF,OAAO,EAAE,iCAAiC;IAC1CC,MAAM,EAAE;EACZ;AACJ,CAAC;AACD,SAASK,WAAWA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAC/B,OAAOnB,aAAa,CAAC;IACjBoB,EAAE,EAAE,aAAa;IACjBC,OAAO,EAAE,MAAM;IACfC,0BAA0B,EAAE,IAAI;IAChCC,MAAM,EAAE;MACJC,IAAI,EAAE;QACFC,MAAM,EAAE,CACJ;UAAET,IAAI,EAAE,qBAAqB;UAAEH,MAAM,EAAE;QAAgB,CAAC,EACxD;UAAEA,MAAM,EAAE;QAAS,CAAC;MAE5B,CAAC;MACDT,UAAU,EAAE;QACRsB,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;UAAEC,GAAG,EAAE,YAAY;UAAE,GAAGb;QAAyB;MAC7D,CAAC;MACDZ,mBAAmB,EAAE;QACjBwB,MAAM,EAAE;UACJC,GAAG,EAAE,qBAAqB;UAC1B,GAAGX;QACP;MACJ,CAAC;MACDY,eAAe,EAAEpB,uBAAuB,CAAC,QAAQ,CAAC;MAClDqB,aAAa,EAAE;QACXH,MAAM,EAAE;UAAEC,GAAG,EAAE,eAAe;UAAE,GAAGlB;QAA4B;MACnE,CAAC;MACDqB,gBAAgB,EAAE;QACdL,IAAI,EAAE,SAAS;QACfM,KAAK,EAAE,YAAY;QACnBC,IAAI,EAAE,YAAY;QAClBN,MAAM,EAAE;UACJC,GAAG,EAAE,kBAAkB;UACvBjB,MAAM,EAAE;YACJC,OAAO,EAAE,CAAC,wBAAwB,EAAE,YAAY,CAAC;YACjDC,MAAM,EAAE;UACZ,CAAC;UACDC,OAAO,EAAE,CACL;YACIE,IAAI,EAAE,wBAAwB;YAC9BH,MAAM,EAAE;UACZ,CAAC,EACD;YAAED,OAAO,EAAE,CAAC,gBAAgB,EAAE,YAAY;UAAE,CAAC;QAErD;MACJ,CAAC;MACDsB,MAAM,EAAE;QACJC,IAAI,EAAE,UAAU;QAChBF,IAAI,EAAE,cAAc;QACpBG,EAAE,EAAE;UACAC,iBAAiB,EAAE;YAAExB,MAAM,EAAE;UAAkB;QACnD,CAAC;QACDU,MAAM,EAAE;UACJe,UAAU,EAAE;YACRjB,OAAO,EAAE,SAAS;YAClBE,MAAM,EAAE;cACJgB,OAAO,EAAE;gBACLZ,MAAM,EAAE;kBACJC,GAAG,EAAE,gBAAgB;kBACrBjB,MAAM,EAAE;oBACJC,OAAO,EAAE,sBAAsB;oBAC/BC,MAAM,EAAE;kBACZ,CAAC;kBACDC,OAAO,EAAE;oBAAEF,OAAO,EAAE,gBAAgB;oBAAEC,MAAM,EAAE;kBAAU;gBAC5D;cACJ,CAAC;cACD2B,KAAK,EAAE;gBAAER,KAAK,EAAE;cAAa,CAAC;cAC9BS,OAAO,EAAE;gBAAET,KAAK,EAAE;cAAa;YACnC,CAAC;YACDI,EAAE,EAAE;cACAM,IAAI,EAAE;gBAAE9B,OAAO,EAAE,YAAY;gBAAEC,MAAM,EAAE;cAAW,CAAC;cACnD8B,MAAM,EAAE;gBAAE/B,OAAO,EAAE,aAAa;gBAAEC,MAAM,EAAE;cAAW;YACzD;UACJ,CAAC;UACD+B,UAAU,EAAE;YACRvB,OAAO,EAAE,MAAM;YACfE,MAAM,EAAE;cACJsB,IAAI,EAAE;gBACFb,KAAK,EAAE,CAAC,YAAY,CAAC;gBACrBI,EAAE,EAAE;kBACAU,MAAM,EAAE;oBAAElC,OAAO,EAAE,cAAc;oBAAEC,MAAM,EAAE;kBAAW;gBAC1D;cACJ,CAAC;cACDkC,QAAQ,EAAE;gBACNf,KAAK,EAAE,YAAY;gBACnBL,MAAM,EAAE;kBACJC,GAAG,EAAE,gBAAgB;kBACrBjB,MAAM,EAAE;oBACJE,MAAM,EAAE,cAAc;oBACtBD,OAAO,EAAE;kBACb,CAAC;kBACDE,OAAO,EAAE;oBAAEF,OAAO,EAAE,gBAAgB;oBAAEC,MAAM,EAAE;kBAAO;gBACzD;cACJ,CAAC;cACDmC,YAAY,EAAE;gBACVtB,IAAI,EAAE,SAAS;gBACfM,KAAK,EAAE,CAAC,mBAAmB,EAAE,YAAY,CAAC;gBAC1CC,IAAI,EAAE,YAAY;gBAClBN,MAAM,EAAE;kBACJC,GAAG,EAAE,cAAc;kBACnBjB,MAAM,EAAE,CACJ;oBACIK,IAAI,EAAE,oBAAoB;oBAC1BJ,OAAO,EAAE,mBAAmB;oBAC5BC,MAAM,EAAE;kBACZ,CAAC,EACD;oBACIG,IAAI,EAAE,kBAAkB;oBACxBJ,OAAO,EAAE,mBAAmB;oBAC5BC,MAAM,EAAE;kBACZ,CAAC,EACD;oBACID,OAAO,EAAE,CACL,wBAAwB,EACxB,mBAAmB,CACtB;oBACDC,MAAM,EAAE;kBACZ,CAAC,CACJ;kBACDC,OAAO,EAAE;oBACLF,OAAO,EAAE,CAAC,YAAY,EAAE,gBAAgB,CAAC;oBACzCC,MAAM,EAAE;kBACZ;gBACJ;cACJ;YACJ;UACJ;QACJ;MACJ,CAAC;MACDoC,aAAa,EAAE;QACX5B,OAAO,EAAE,MAAM;QACfW,KAAK,EAAE,YAAY;QACnBT,MAAM,EAAE;UACJ2B,IAAI,EAAE;YACFd,EAAE,EAAE;cACAU,MAAM,EAAE;gBAAElC,OAAO,EAAE,cAAc;gBAAEC,MAAM,EAAE;cAAS,CAAC;cACrD8B,MAAM,EAAE;gBAAE/B,OAAO,EAAE;cAAc,CAAC;cAClC8B,IAAI,EAAE;gBAAE9B,OAAO,EAAE;cAAa,CAAC;cAC/BuC,MAAM,EAAE;YACZ;UACJ,CAAC;UACDC,MAAM,EAAE;YACJ1B,IAAI,EAAE,SAAS;YACfM,KAAK,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;YACnCL,MAAM,EAAE;cACJC,GAAG,EAAE,eAAe;cACpBjB,MAAM,EAAE,CACJ;gBACIK,IAAI,EAAE,kBAAkB;gBACxBJ,OAAO,EAAE,CAAC,mBAAmB,EAAE,iBAAiB,CAAC;gBACjDC,MAAM,EAAE;cACZ,CAAC,EACD;gBACID,OAAO,EAAE,mBAAmB;gBAC5BC,MAAM,EAAE;cACZ,CAAC,CACJ;cACDC,OAAO,EAAE;gBACLF,OAAO,EAAE,CAAC,gBAAgB,EAAE,YAAY,CAAC;gBACzCC,MAAM,EAAE;cACZ;YACJ;UACJ;QACJ;MACJ,CAAC;MACDwC,QAAQ,EAAE;QACNlB,IAAI,EAAE,OAAO;QACbmB,IAAI,EAAGC,OAAO,KAAM;UAChBC,aAAa,EAAED,OAAO,CAACC,aAAa;UACpCC,iBAAiB,EAAEF,OAAO,CAACE,iBAAiB;UAC5CC,WAAW,EAAEH,OAAO,CAACG,WAAW;UAChCC,IAAI,EAAEJ,OAAO,CAACI,IAAI;UAClBC,cAAc,EAAEL,OAAO,CAACK,cAAc;UACtCC,QAAQ,EAAEN,OAAO,CAACM,QAAQ;UAC1BC,wBAAwB,EAAEP,OAAO,CAACO,wBAAwB;UAC1DC,eAAe,EAAER,OAAO,CAACQ;QAC7B,CAAC;MACL;IACJ;EACJ,CAAC,EAAE;IACC;IACAnD,OAAO,EAAE;MAAE,GAAGL,OAAO;MAAEN,UAAU,EAAEA,UAAU,CAAC;IAAE,CAAC;IACjD+D,MAAM,EAAExD,MAAM;IACdW,QAAQ,EAAE;MACNf,UAAUA,CAAA,EAAG;QACT,OAAOA,UAAU,CAAC,CAAC;MACvB,CAAC;MACKD,mBAAmBA,CAAA,EAAG;QAAA,OAAA8D,iBAAA;UACxB,OAAO9D,mBAAmB,CAAC,CAAC;QAAC;MACjC,CAAC;MACD8C,aAAaA,CAAC;QAAEiB,UAAU;QAAEL;MAAS,CAAC,EAAE;QACpC,MAAM;UAAEM,iBAAiB,EAAEC;QAAiB,CAAC,GAAGF,UAAU;QAC1D,MAAMG,KAAK,GAAG;UAAER,QAAQ;UAAEO;QAAiB,CAAC;QAC5C,OAAOjD,QAAQ,CAACmD,mBAAmB,CAACD,KAAK,CAAC;MAC9C,CAAC;MACDtC,gBAAgBA,CAAC;QAAE8B;MAAS,CAAC,EAAE;QAC3B,OAAO1C,QAAQ,CAACoD,sBAAsB,CAAC;UAAEV;QAAS,CAAC,CAAC;MACxD,CAAC;MACD3D,kBAAkBA,CAACsE,CAAC,EAAE;QAAElB;MAAK,CAAC,EAAE;QAC5B,OAAOpD,kBAAkB,CAACoD,IAAI,CAAC;MACnC,CAAC;MACDN,YAAYA,CAACO,OAAO,EAAE;QAClB,MAAM;UAAEW,UAAU;UAAEO,eAAe;UAAEZ;QAAS,CAAC,GAAGN,OAAO;QACzD,MAAMmB,cAAc,GAAGD,eAAe,CAAC,CAAC,CAAC;QACzC,MAAMJ,KAAK,GAAGhE,cAAc,CAACwD,QAAQ,EAAEK,UAAU,EAAEQ,cAAc,CAAC;QAClE,OAAOvD,QAAQ,CAAC6B,YAAY,CAACqB,KAAK,CAAC;MACvC,CAAC;MACKM,cAAcA,CAACpB,OAAO,EAAE;QAAA,OAAAU,iBAAA;UAC1B;UACA,OAAO3D,aAAa,CAACiD,OAAO,CAACW,UAAU,EAAEX,OAAO,CAACqB,OAAO,EAAErB,OAAO,CAACsB,gBAAgB,EAAE;UAChF;UACA1D,QAAQ,CAAC2D,oBAAoB;UAC7B;UACA3D,QAAQ,CAAC4D,uBAAuB,EAChC5D,QAAQ,CAAC6D,yBAAyB;UAClC;UACA7D,QAAQ,CAAC8D,oBAAoB,CAChC,CAAC;QAAC;MACP;IACJ;EACJ,CAAC,CAAC;AACN;AAEA,SAAS/D,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}