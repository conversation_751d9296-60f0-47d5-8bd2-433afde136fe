{"ast": null, "code": "const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default {\n  randomUUID\n};", "map": {"version": 3, "names": ["randomUUID", "crypto", "bind"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/uuid/dist/esm-browser/native.js"], "sourcesContent": ["const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default {\n  randomUUID\n};"], "mappings": "AAAA,MAAMA,UAAU,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACD,UAAU,IAAIC,MAAM,CAACD,UAAU,CAACE,IAAI,CAACD,MAAM,CAAC;AACvG,eAAe;EACbD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}