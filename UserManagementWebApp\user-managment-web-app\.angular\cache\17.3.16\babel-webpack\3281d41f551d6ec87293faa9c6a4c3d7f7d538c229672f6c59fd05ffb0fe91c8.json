{"ast": null, "code": "import { translate } from '../../../i18n/translations.mjs';\nconst getPrimaryAlias = state => {\n  const loginMechanisms = state?.context.config?.loginMechanisms;\n  /**\n   * @migration this is where we grab only the first index of `aws_cognito_username_attributes`\n   */\n  const [primaryAlias] = loginMechanisms ?? ['username'];\n  return primaryAlias;\n};\n/** Applies translations to label and placeholder */\nconst applyTranslation = formFields => {\n  const newFormFields = {\n    ...formFields\n  };\n  for (const [name, options] of Object.entries(formFields)) {\n    const {\n      label,\n      placeholder\n    } = options;\n    newFormFields[name] = {\n      ...options,\n      label: label ? translate(label) : undefined,\n      placeholder: placeholder ? translate(placeholder) : undefined\n    };\n  }\n  return newFormFields;\n};\n/** Sorts formFields according to their `order`.  */\nconst sortFormFields = formFields => {\n  return Object.entries(formFields).sort((a, b) => {\n    const orderA = a[1].order || Number.MAX_VALUE;\n    const orderB = b[1].order || Number.MAX_VALUE;\n    return orderA - orderB;\n  }).filter(formFieldEntry => formFieldEntry[1] !== undefined);\n};\nexport { applyTranslation, getPrimaryAlias, sortFormFields };", "map": {"version": 3, "names": ["translate", "getPrimaryAlias", "state", "loginMechanisms", "context", "config", "<PERSON><PERSON><PERSON><PERSON>", "applyTranslation", "formFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "options", "Object", "entries", "label", "placeholder", "undefined", "sortF<PERSON><PERSON>ields", "sort", "a", "b", "orderA", "order", "Number", "MAX_VALUE", "orderB", "filter", "formFieldEntry"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/helpers/authenticator/formFields/utils.mjs"], "sourcesContent": ["import { translate } from '../../../i18n/translations.mjs';\n\nconst getPrimaryAlias = (state) => {\n    const loginMechanisms = state?.context.config?.loginMechanisms;\n    /**\n     * @migration this is where we grab only the first index of `aws_cognito_username_attributes`\n     */\n    const [primaryAlias] = loginMechanisms ?? ['username'];\n    return primaryAlias;\n};\n/** Applies translations to label and placeholder */\nconst applyTranslation = (formFields) => {\n    const newFormFields = { ...formFields };\n    for (const [name, options] of Object.entries(formFields)) {\n        const { label, placeholder } = options;\n        newFormFields[name] = {\n            ...options,\n            label: label ? translate(label) : undefined,\n            placeholder: placeholder ? translate(placeholder) : undefined,\n        };\n    }\n    return newFormFields;\n};\n/** Sorts formFields according to their `order`.  */\nconst sortFormFields = (formFields) => {\n    return Object.entries(formFields)\n        .sort((a, b) => {\n        const orderA = a[1].order || Number.MAX_VALUE;\n        const orderB = b[1].order || Number.MAX_VALUE;\n        return orderA - orderB;\n    })\n        .filter((formFieldEntry) => formFieldEntry[1] !== undefined);\n};\n\nexport { applyTranslation, getPrimaryAlias, sortFormFields };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gCAAgC;AAE1D,MAAMC,eAAe,GAAIC,KAAK,IAAK;EAC/B,MAAMC,eAAe,GAAGD,KAAK,EAAEE,OAAO,CAACC,MAAM,EAAEF,eAAe;EAC9D;AACJ;AACA;EACI,MAAM,CAACG,YAAY,CAAC,GAAGH,eAAe,IAAI,CAAC,UAAU,CAAC;EACtD,OAAOG,YAAY;AACvB,CAAC;AACD;AACA,MAAMC,gBAAgB,GAAIC,UAAU,IAAK;EACrC,MAAMC,aAAa,GAAG;IAAE,GAAGD;EAAW,CAAC;EACvC,KAAK,MAAM,CAACE,IAAI,EAAEC,OAAO,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACL,UAAU,CAAC,EAAE;IACtD,MAAM;MAAEM,KAAK;MAAEC;IAAY,CAAC,GAAGJ,OAAO;IACtCF,aAAa,CAACC,IAAI,CAAC,GAAG;MAClB,GAAGC,OAAO;MACVG,KAAK,EAAEA,KAAK,GAAGd,SAAS,CAACc,KAAK,CAAC,GAAGE,SAAS;MAC3CD,WAAW,EAAEA,WAAW,GAAGf,SAAS,CAACe,WAAW,CAAC,GAAGC;IACxD,CAAC;EACL;EACA,OAAOP,aAAa;AACxB,CAAC;AACD;AACA,MAAMQ,cAAc,GAAIT,UAAU,IAAK;EACnC,OAAOI,MAAM,CAACC,OAAO,CAACL,UAAU,CAAC,CAC5BU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAChB,MAAMC,MAAM,GAAGF,CAAC,CAAC,CAAC,CAAC,CAACG,KAAK,IAAIC,MAAM,CAACC,SAAS;IAC7C,MAAMC,MAAM,GAAGL,CAAC,CAAC,CAAC,CAAC,CAACE,KAAK,IAAIC,MAAM,CAACC,SAAS;IAC7C,OAAOH,MAAM,GAAGI,MAAM;EAC1B,CAAC,CAAC,CACGC,MAAM,CAAEC,cAAc,IAAKA,cAAc,CAAC,CAAC,CAAC,KAAKX,SAAS,CAAC;AACpE,CAAC;AAED,SAAST,gBAAgB,EAAEN,eAAe,EAAEgB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}