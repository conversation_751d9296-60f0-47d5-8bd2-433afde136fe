{"ast": null, "code": "/**\n * Format bytes as human-readable text.\n *\n * @param bytes Number of bytes.\n * @param si True to use metric (SI) units, aka powers of 1000. False to use\n *           binary (IEC), aka powers of 1024.\n * @param dp Number of decimal places to display.\n *\n * @return Formatted string.\n */\nfunction humanFileSize(bytes, si = false, dp = 1) {\n  const thresh = si ? 1000 : 1024;\n  if (Math.abs(bytes) < thresh) {\n    return `${bytes} B`;\n  }\n  const units = si ? ['kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'] : ['KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB'];\n  let unit = -1;\n  const range = 10 ** dp;\n  do {\n    bytes /= thresh;\n    ++unit;\n  } while (Math.round(Math.abs(bytes) * range) / range >= thresh && unit < units.length - 1);\n  return bytes.toFixed(dp) + ' ' + units[unit];\n}\nexport { humanFileSize };", "map": {"version": 3, "names": ["humanFileSize", "bytes", "si", "dp", "thresh", "Math", "abs", "units", "unit", "range", "round", "length", "toFixed"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/utils/humanFileSize.mjs"], "sourcesContent": ["/**\n * Format bytes as human-readable text.\n *\n * @param bytes Number of bytes.\n * @param si True to use metric (SI) units, aka powers of 1000. False to use\n *           binary (IEC), aka powers of 1024.\n * @param dp Number of decimal places to display.\n *\n * @return Formatted string.\n */\nfunction humanFileSize(bytes, si = false, dp = 1) {\n    const thresh = si ? 1000 : 1024;\n    if (Math.abs(bytes) < thresh) {\n        return `${bytes} B`;\n    }\n    const units = si\n        ? ['kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']\n        : ['KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB'];\n    let unit = -1;\n    const range = 10 ** dp;\n    do {\n        bytes /= thresh;\n        ++unit;\n    } while (Math.round(Math.abs(bytes) * range) / range >= thresh &&\n        unit < units.length - 1);\n    return bytes.toFixed(dp) + ' ' + units[unit];\n}\n\nexport { humanFileSize };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAaA,CAACC,KAAK,EAAEC,EAAE,GAAG,KAAK,EAAEC,EAAE,GAAG,CAAC,EAAE;EAC9C,MAAMC,MAAM,GAAGF,EAAE,GAAG,IAAI,GAAG,IAAI;EAC/B,IAAIG,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC,GAAGG,MAAM,EAAE;IAC1B,OAAO,GAAGH,KAAK,IAAI;EACvB;EACA,MAAMM,KAAK,GAAGL,EAAE,GACV,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,GAChD,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9D,IAAIM,IAAI,GAAG,CAAC,CAAC;EACb,MAAMC,KAAK,GAAG,EAAE,IAAIN,EAAE;EACtB,GAAG;IACCF,KAAK,IAAIG,MAAM;IACf,EAAEI,IAAI;EACV,CAAC,QAAQH,IAAI,CAACK,KAAK,CAACL,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC,GAAGQ,KAAK,CAAC,GAAGA,KAAK,IAAIL,MAAM,IAC1DI,IAAI,GAAGD,KAAK,CAACI,MAAM,GAAG,CAAC;EAC3B,OAAOV,KAAK,CAACW,OAAO,CAACT,EAAE,CAAC,GAAG,GAAG,GAAGI,KAAK,CAACC,IAAI,CAAC;AAChD;AAEA,SAASR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}