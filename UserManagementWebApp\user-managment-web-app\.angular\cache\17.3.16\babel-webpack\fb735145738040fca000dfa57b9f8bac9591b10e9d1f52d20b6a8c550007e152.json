{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction urlSafeEncode(str) {\n  return str.split('').map(char => char.charCodeAt(0).toString(16).padStart(2, '0')).join('');\n}\nexport { urlSafeEncode };", "map": {"version": 3, "names": ["urlSafeEncode", "str", "split", "map", "char", "charCodeAt", "toString", "padStart", "join"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/urlSafeEncode.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction urlSafeEncode(str) {\n    return str\n        .split('')\n        .map(char => char.charCodeAt(0).toString(16).padStart(2, '0'))\n        .join('');\n}\n\nexport { urlSafeEncode };\n"], "mappings": "AAAA;AACA;AACA,SAASA,aAAaA,CAACC,GAAG,EAAE;EACxB,OAAOA,GAAG,CACLC,KAAK,CAAC,EAAE,CAAC,CACTC,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAC7DC,IAAI,CAAC,EAAE,CAAC;AACjB;AAEA,SAASR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}