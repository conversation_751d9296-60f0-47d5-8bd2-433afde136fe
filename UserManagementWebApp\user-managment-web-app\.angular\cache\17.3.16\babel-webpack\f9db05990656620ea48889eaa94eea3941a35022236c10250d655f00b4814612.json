{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AuthAction } from '@aws-amplify/core/internals/utils';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../../../providers/cognito/types/errors.mjs';\nimport { createRespondToAuthChallengeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createRespondToAuthChallengeClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { getUserContextData } from '../../../providers/cognito/utils/userContextData.mjs';\nimport { setActiveSignInUsername } from '../../../providers/cognito/utils/setActiveSignInUsername.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Handles the SELECT_CHALLENGE response specifically for Password authentication.\n * This function combines the SELECT_CHALLENGE flow with standard password authentication.\n *\n * @param {string} username - The username for authentication\n * @param {string} password - The user's password\n * @param {ClientMetadata} [clientMetadata] - Optional metadata to be sent with auth requests\n * @param {CognitoUserPoolConfig} config - Cognito User Pool configuration\n * @param {string} session - The current authentication session token\n *\n * @returns {Promise<RespondToAuthChallengeCommandOutput>} The challenge response\n */\nfunction handleSelectChallengeWithPassword(_x, _x2, _x3, _x4, _x5) {\n  return _handleSelectChallengeWithPassword.apply(this, arguments);\n}\nfunction _handleSelectChallengeWithPassword() {\n  _handleSelectChallengeWithPassword = _asyncToGenerator(function* (username, password, clientMetadata, config, session) {\n    const {\n      userPoolId,\n      userPoolClientId,\n      userPoolEndpoint\n    } = config;\n    const authParameters = {\n      ANSWER: 'PASSWORD',\n      USERNAME: username,\n      PASSWORD: password\n    };\n    const userContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const response = yield respondToAuthChallenge({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn)\n    }, {\n      ChallengeName: 'SELECT_CHALLENGE',\n      ChallengeResponses: authParameters,\n      ClientId: userPoolClientId,\n      ClientMetadata: clientMetadata,\n      Session: session,\n      UserContextData: userContextData\n    });\n    const activeUsername = response.ChallengeParameters?.USERNAME ?? username;\n    setActiveSignInUsername(activeUsername);\n    return response;\n  });\n  return _handleSelectChallengeWithPassword.apply(this, arguments);\n}\nexport { handleSelectChallengeWithPassword };", "map": {"version": 3, "names": ["AuthAction", "createRespondToAuthChallengeClient", "createCognitoUserPoolEndpointResolver", "getRegionFromUserPoolId", "getAuthUserAgentValue", "getUserContextData", "setActiveSignInUsername", "handleSelectChallengeWithPassword", "_x", "_x2", "_x3", "_x4", "_x5", "_handleSelectChallengeWithPassword", "apply", "arguments", "_asyncToGenerator", "username", "password", "clientMetadata", "config", "session", "userPoolId", "userPoolClientId", "userPoolEndpoint", "authParameters", "ANSWER", "USERNAME", "PASSWORD", "userContextData", "respondToAuthChallenge", "endpointResolver", "endpointOverride", "response", "region", "userAgentValue", "ConfirmSignIn", "ChallengeName", "ChallengeResponses", "ClientId", "ClientMetadata", "Session", "UserContextData", "activeUsername", "ChallengeParameters"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/flows/userAuth/handleSelectChallengeWithPassword.mjs"], "sourcesContent": ["import { AuthAction } from '@aws-amplify/core/internals/utils';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../../../providers/cognito/types/errors.mjs';\nimport { createRespondToAuthChallengeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createRespondToAuthChallengeClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { getUserContextData } from '../../../providers/cognito/utils/userContextData.mjs';\nimport { setActiveSignInUsername } from '../../../providers/cognito/utils/setActiveSignInUsername.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Handles the SELECT_CHALLENGE response specifically for Password authentication.\n * This function combines the SELECT_CHALLENGE flow with standard password authentication.\n *\n * @param {string} username - The username for authentication\n * @param {string} password - The user's password\n * @param {ClientMetadata} [clientMetadata] - Optional metadata to be sent with auth requests\n * @param {CognitoUserPoolConfig} config - Cognito User Pool configuration\n * @param {string} session - The current authentication session token\n *\n * @returns {Promise<RespondToAuthChallengeCommandOutput>} The challenge response\n */\nasync function handleSelectChallengeWithPassword(username, password, clientMetadata, config, session) {\n    const { userPoolId, userPoolClientId, userPoolEndpoint } = config;\n    const authParameters = {\n        ANSWER: 'PASSWORD',\n        USERNAME: username,\n        PASSWORD: password,\n    };\n    const userContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const response = await respondToAuthChallenge({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn),\n    }, {\n        ChallengeName: 'SELECT_CHALLENGE',\n        ChallengeResponses: authParameters,\n        ClientId: userPoolClientId,\n        ClientMetadata: clientMetadata,\n        Session: session,\n        UserContextData: userContextData,\n    });\n    const activeUsername = response.ChallengeParameters?.USERNAME ?? username;\n    setActiveSignInUsername(activeUsername);\n    return response;\n}\n\nexport { handleSelectChallengeWithPassword };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,mCAAmC;AAC9D,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,sCAAsC;AAC7C,OAAO,6CAA6C;AACpD,SAASC,kCAAkC,QAAQ,6GAA6G;AAChK,SAASC,qCAAqC,QAAQ,gFAAgF;AACtI,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,uBAAuB,QAAQ,8DAA8D;;AAEtG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,SAYeC,iCAAiCA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,kCAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,mCAAA;EAAAA,kCAAA,GAAAG,iBAAA,CAAhD,WAAiDC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,MAAM,EAAEC,OAAO,EAAE;IAClG,MAAM;MAAEC,UAAU;MAAEC,gBAAgB;MAAEC;IAAiB,CAAC,GAAGJ,MAAM;IACjE,MAAMK,cAAc,GAAG;MACnBC,MAAM,EAAE,UAAU;MAClBC,QAAQ,EAAEV,QAAQ;MAClBW,QAAQ,EAAEV;IACd,CAAC;IACD,MAAMW,eAAe,GAAGxB,kBAAkB,CAAC;MACvCY,QAAQ;MACRK,UAAU;MACVC;IACJ,CAAC,CAAC;IACF,MAAMO,sBAAsB,GAAG7B,kCAAkC,CAAC;MAC9D8B,gBAAgB,EAAE7B,qCAAqC,CAAC;QACpD8B,gBAAgB,EAAER;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMS,QAAQ,SAASH,sBAAsB,CAAC;MAC1CI,MAAM,EAAE/B,uBAAuB,CAACmB,UAAU,CAAC;MAC3Ca,cAAc,EAAE/B,qBAAqB,CAACJ,UAAU,CAACoC,aAAa;IAClE,CAAC,EAAE;MACCC,aAAa,EAAE,kBAAkB;MACjCC,kBAAkB,EAAEb,cAAc;MAClCc,QAAQ,EAAEhB,gBAAgB;MAC1BiB,cAAc,EAAErB,cAAc;MAC9BsB,OAAO,EAAEpB,OAAO;MAChBqB,eAAe,EAAEb;IACrB,CAAC,CAAC;IACF,MAAMc,cAAc,GAAGV,QAAQ,CAACW,mBAAmB,EAAEjB,QAAQ,IAAIV,QAAQ;IACzEX,uBAAuB,CAACqC,cAAc,CAAC;IACvC,OAAOV,QAAQ;EACnB,CAAC;EAAA,OAAApB,kCAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASR,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}