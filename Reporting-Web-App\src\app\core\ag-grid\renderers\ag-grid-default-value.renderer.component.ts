import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';
import { ProfileProperty } from '../../models/profile-property';
import { UserService } from '../../services/user.service';
import { ActionableGridColumnConfig } from '../../models/actionable-grid-column-config';
import { DefaultValue } from '../../models/default-value';
import { ActionableGridColumnType } from '../../enums/actionable-grid-enums';
import { JsonStringFormats } from 'src/app/shared/enums/json-schema.enum';
import { deepCopy } from '../../functions/utility';
import { Button } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { Header, PrimeTemplate } from 'primeng/api';
import { DialogModule } from 'primeng/dialog';
import { FormsModule } from '@angular/forms';
import { NgIf } from '@angular/common';
import { InputTextModule } from 'primeng/inputtext';

@Component({
    selector: 'app-default-value-renderer',
    templateUrl: './ag-grid-default-value.renderer.component.html',
    standalone: true,
    imports: [NgIf, FormsModule, DialogModule, Header, CheckboxModule, DropdownModule, PrimeTemplate, Button, InputTextModule]
})
export class AgGridDefaultValueRendererComponent implements ICellRendererAngularComp {

  dialogVisible = false;
  profileProperties: ProfileProperty[];
  defaultValue: DefaultValue;
  columnConfig: ActionableGridColumnConfig;

  get displayValue() {
    return this.columnConfig?.defaultValue?.isUserProfileValue
      ? 'User Profile Value'
      : this.columnConfig?.defaultValue?.value;
  }

  get defaultValueInputText(): string {
    return this.defaultValue.isUserProfileValue ? '' : this.defaultValue.value;
  }

  set defaultValueInputText(value: string) {
    this.defaultValue.value = value;
  }

  get showRenderer(): boolean {
    // The default value option for date fields is hidden for now.
    return this.columnConfig.format.type !== ActionableGridColumnType.Object
      && this.columnConfig.format.type !== ActionableGridColumnType.Collection
      && this.columnConfig.format.baseFormat !== JsonStringFormats.DateTime
      && this.columnConfig.format.baseFormat !== JsonStringFormats.Date 
      && !this.columnConfig.schemaProperty?.presentationProperties?.enableFormula;
  }

  agInit(cellRendererParams: ICellRendererParams): void {
    this.columnConfig = cellRendererParams.data;
  }

  constructor(private userService: UserService) { }

  refresh(): boolean {
    return false;
  }

  getProfileProperties(): void {
    this.userService.getCompleteUserProfilePropertyList().subscribe({
      next: (userProfileProperties: ProfileProperty[]) => {
        this.profileProperties = userProfileProperties;
      }
    });
  }

  showDialog(visible: boolean): void {
    if (!this.profileProperties) {
      this.getProfileProperties();
    }
    this.dialogVisible = visible;
    this.defaultValue = visible ? deepCopy<DefaultValue>(this.columnConfig?.defaultValue) ?? new DefaultValue() : this.defaultValue;
  }

  reset(): void {
    this.columnConfig.defaultValue = new DefaultValue();
  }

  resetValue() {
    this.defaultValue.value = '';
  }

  save(): void {
    this.columnConfig.defaultValue = this.defaultValue;
    this.dialogVisible = false;
  }
}