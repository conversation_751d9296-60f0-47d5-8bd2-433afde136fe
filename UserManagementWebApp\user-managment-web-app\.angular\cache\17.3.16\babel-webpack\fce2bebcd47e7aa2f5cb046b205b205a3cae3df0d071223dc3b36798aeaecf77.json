{"ast": null, "code": "const esDict = {\n  'Account recovery requires verified contact information': 'La recuperación de la cuenta requiere información de contacto verificada',\n  'Authenticator App (TOTP)': 'Aplicación de autenticación (TOTP)',\n  'Back to Sign In': 'Volver a inicio de sesión',\n  'Change Password': 'Cambiar contraseña',\n  Changing: 'Cambiando',\n  Code: 'Código',\n  'Code *': 'Código *',\n  'Confirm Email Code': 'Confirmar el código de correo electrónico',\n  'Confirm Password': 'Confirmar contraseña',\n  'Confirm Sign Up': 'Confirmar registro',\n  'Confirm SMS Code': 'Confirmar el código de SMS',\n  'Confirm TOTP Code': 'Confirmar código TOTP',\n  Confirm: 'Confirmar',\n  'Confirmation Code': 'Código de confirmación',\n  Confirming: 'Confirmando',\n  'Create a new account': 'Crear una cuenta nueva',\n  'Create Account': 'Crear cuenta',\n  'Creating Account': 'Creando cuenta',\n  'Dismiss alert': 'Descartar alerta',\n  Email: 'Email',\n  'Email Message': 'Mensaje de correo electrónico',\n  'Enter your code': 'Ingrese el código',\n  'Enter your Email': 'Escriba su Email',\n  'Enter your email': 'Escriba su email',\n  'Enter your Password': 'Escriba su Contraseña',\n  'Enter your phone number': 'Ingrese el número de teléfono',\n  'Enter your username': 'Ingrese el nombre de usuario',\n  'Forgot your password?': '¿Olvidó su contraseña?',\n  'Hide password': 'Ocultar contraseña',\n  'It may take a minute to arrive': 'Es posible que tarde un minuto en llegar',\n  Loading: 'Cargando',\n  'Multi-Factor Authentication': 'Autenticación multifactor',\n  'Multi-Factor Authentication Setup': 'Configuración de autenticación multifactor',\n  'New password': 'Nueva contraseña',\n  or: 'o',\n  Password: 'Contraseña',\n  'Phone Number': 'Número de teléfono',\n  'Resend Code': 'Reenviar código',\n  'Reset your password': 'Restablecer su contraseña',\n  'Reset your Password': 'Restablecer su Contraseña',\n  'Select MFA Type': 'Seleccionar el tipo de MFA',\n  'Send code': 'Enviar código',\n  'Send Code': 'Enviar código',\n  Sending: 'Enviando',\n  'Setup Email': 'Configurar correo electrónico',\n  'Setup TOTP': 'Configurar TOTP',\n  'Show password': 'Mostrar contraseña',\n  'Sign in to your account': 'Iniciar sesión en tu cuenta',\n  'Sign In with Amazon': 'Iniciar Sesión con Amazon',\n  'Sign In with Apple': 'Iniciar Sesión con Apple',\n  'Sign In with Facebook': 'Iniciar Sesión con Facebook',\n  'Sign In with Google': 'Iniciar Sesión con Google',\n  'Sign in': 'Iniciar sesión',\n  'Sign In': 'Iniciar Sesión',\n  'Signing in': 'Iniciando sesión',\n  Skip: 'Omitir',\n  Submit: 'Enviar',\n  Submitting: 'Enviando',\n  'Text Message (SMS)': 'Mensaje de texto (SMS)',\n  Username: 'Nombre de usuario',\n  'Verify Contact': 'Verificar contacto',\n  Verify: 'Verificar',\n  'We Emailed You': 'Le hemos enviado un correo electrónico',\n  'We Sent A Code': 'Hemos enviado un código',\n  'We Texted You': 'Le hemos enviado un mensaje de texto',\n  'Your code is on the way. To log in, enter the code we emailed to': 'El código está en camino. Para iniciar sesión, escriba el código que hemos enviado por correo electrónico a',\n  'Your code is on the way. To log in, enter the code we sent you': 'El código está en camino. Para iniciar sesión, escriba el código que le hemos enviado',\n  'Your code is on the way. To log in, enter the code we texted to': 'El código está en camino. Para iniciar sesión, escriba el código que hemos enviado por mensaje de texto a',\n  // Additional translations provided by customers\n  'An account with the given email already exists.': 'Ya existe una cuenta con el correo ingresado.',\n  'Confirm a Code': 'Confirmar un código',\n  'Confirm Sign In': 'Confirmar inicio de sesión',\n  'Forgot Password': 'Olvidé mi contraseña',\n  'Incorrect username or password.': 'Nombre de usuario o contraseña incorrecta',\n  'Enter your Family Name': 'Escriba su apellido',\n  'Enter your Given Name': 'Escriba su nombre',\n  'Given Name': 'Nombre',\n  'Family Name': 'Apellido',\n  'Reset Password': 'Restablecer contraseña',\n  'Please confirm your Password': 'Confirme su contraseña',\n  'Invalid password format': 'Formato de contraseña inválido',\n  'Invalid phone number format': 'Formato de número de teléfono inválido',\n  'Loading...': 'Cargando...',\n  'New Password': 'Nueva contraseña',\n  'Resend a Code': 'Reenviar un código',\n  'Sign Out': 'Cerrar sesión',\n  'Sign Up with Amazon': 'Crear cuenta con Amazon',\n  'Sign Up with Apple': 'Crear cuenta con Apple',\n  'Sign Up with Facebook': 'Crear cuenta con Facebook',\n  'Sign Up with Google': 'Crear cuenta con Google',\n  'Sign Up': 'Crear cuenta',\n  'User already exists': 'El usuario ya existe',\n  'User does not exist': 'El usuario no existe',\n  'Username/client id combination not found.': 'El usuario no existe',\n  'Username cannot be empty': 'El nombre de usuario no puede estar vacío',\n  'Your passwords must match': 'Las contraseñas deben coincidir',\n  'Password must have at least 8 characters': 'La contraseña debe tener al menos 8 caracteres',\n  'Password did not conform with policy: Password must have uppercase characters': 'La contraseña debe tener al menos un carácter en mayúscula',\n  'Password did not conform with policy: Password must have numeric characters': 'La contraseña debe tener al menos un carácter numérico',\n  'Password did not conform with policy: Password must have symbol characters': 'La contraseña debe tener al menos un símbolo',\n  'Password did not conform with policy: Password must have lowercase characters': 'La contraseña debe tener al menos un carácter en minúsculas',\n  'Invalid verification code provided, please try again.': 'Código de verificación no válido, inténtelo de nuevo.',\n  'Attempt limit exceeded, please try after some time.': 'Número máximo de intentos excedido, por favor inténtelo de nuevo más tarde.',\n  'A network error has occurred.': 'Se ha producido un error de red.'\n};\nexport { esDict };", "map": {"version": 3, "names": ["esDict", "Changing", "Code", "Confirm", "Confirming", "Email", "Loading", "or", "Password", "Sending", "<PERSON><PERSON>", "Submit", "Submitting", "Username", "Verify"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/i18n/dictionaries/authenticator/es.mjs"], "sourcesContent": ["const esDict = {\n    'Account recovery requires verified contact information': 'La recuperación de la cuenta requiere información de contacto verificada',\n    'Authenticator App (TOTP)': 'Aplicación de autenticación (TOTP)',\n    'Back to Sign In': 'Volver a inicio de sesión',\n    'Change Password': 'Cambiar contraseña',\n    Changing: 'Cambiando',\n    Code: 'Código',\n    'Code *': 'Código *',\n    'Confirm Email Code': 'Confirmar el código de correo electrónico',\n    'Confirm Password': 'Confirmar contraseña',\n    'Confirm Sign Up': 'Confirmar registro',\n    'Confirm SMS Code': 'Confirmar el código de SMS',\n    'Confirm TOTP Code': 'Confirmar código TOTP',\n    Confirm: 'Confirmar',\n    'Confirmation Code': 'Código de confirmación',\n    Confirming: 'Confirmando',\n    'Create a new account': 'Crear una cuenta nueva',\n    'Create Account': 'Crear cuenta',\n    'Creating Account': 'Creando cuenta',\n    'Dismiss alert': 'Descartar alerta',\n    Email: 'Email',\n    'Email Message': 'Mensaje de correo electrónico',\n    'Enter your code': 'Ingrese el código',\n    'Enter your Email': 'Escriba su Email',\n    'Enter your email': 'Escriba su email',\n    'Enter your Password': 'Escriba su Contraseña',\n    'Enter your phone number': 'Ingrese el número de teléfono',\n    'Enter your username': 'Ingrese el nombre de usuario',\n    'Forgot your password?': '¿Olvidó su contraseña?',\n    'Hide password': 'Ocultar contraseña',\n    'It may take a minute to arrive': 'Es posible que tarde un minuto en llegar',\n    Loading: 'Cargando',\n    'Multi-Factor Authentication': 'Autenticación multifactor',\n    'Multi-Factor Authentication Setup': 'Configuración de autenticación multifactor',\n    'New password': 'Nueva contraseña',\n    or: 'o',\n    Password: 'Contraseña',\n    'Phone Number': 'Número de teléfono',\n    'Resend Code': 'Reenviar código',\n    'Reset your password': 'Restablecer su contraseña',\n    'Reset your Password': 'Restablecer su Contraseña',\n    'Select MFA Type': 'Seleccionar el tipo de MFA',\n    'Send code': 'Enviar código',\n    'Send Code': 'Enviar código',\n    Sending: 'Enviando',\n    'Setup Email': 'Configurar correo electrónico',\n    'Setup TOTP': 'Configurar TOTP',\n    'Show password': 'Mostrar contraseña',\n    'Sign in to your account': 'Iniciar sesión en tu cuenta',\n    'Sign In with Amazon': 'Iniciar Sesión con Amazon',\n    'Sign In with Apple': 'Iniciar Sesión con Apple',\n    'Sign In with Facebook': 'Iniciar Sesión con Facebook',\n    'Sign In with Google': 'Iniciar Sesión con Google',\n    'Sign in': 'Iniciar sesión',\n    'Sign In': 'Iniciar Sesión',\n    'Signing in': 'Iniciando sesión',\n    Skip: 'Omitir',\n    Submit: 'Enviar',\n    Submitting: 'Enviando',\n    'Text Message (SMS)': 'Mensaje de texto (SMS)',\n    Username: 'Nombre de usuario',\n    'Verify Contact': 'Verificar contacto',\n    Verify: 'Verificar',\n    'We Emailed You': 'Le hemos enviado un correo electrónico',\n    'We Sent A Code': 'Hemos enviado un código',\n    'We Texted You': 'Le hemos enviado un mensaje de texto',\n    'Your code is on the way. To log in, enter the code we emailed to': 'El código está en camino. Para iniciar sesión, escriba el código que hemos enviado por correo electrónico a',\n    'Your code is on the way. To log in, enter the code we sent you': 'El código está en camino. Para iniciar sesión, escriba el código que le hemos enviado',\n    'Your code is on the way. To log in, enter the code we texted to': 'El código está en camino. Para iniciar sesión, escriba el código que hemos enviado por mensaje de texto a',\n    // Additional translations provided by customers\n    'An account with the given email already exists.': 'Ya existe una cuenta con el correo ingresado.',\n    'Confirm a Code': 'Confirmar un código',\n    'Confirm Sign In': 'Confirmar inicio de sesión',\n    'Forgot Password': 'Olvidé mi contraseña',\n    'Incorrect username or password.': 'Nombre de usuario o contraseña incorrecta',\n    'Enter your Family Name': 'Escriba su apellido',\n    'Enter your Given Name': 'Escriba su nombre',\n    'Given Name': 'Nombre',\n    'Family Name': 'Apellido',\n    'Reset Password': 'Restablecer contraseña',\n    'Please confirm your Password': 'Confirme su contraseña',\n    'Invalid password format': 'Formato de contraseña inválido',\n    'Invalid phone number format': 'Formato de número de teléfono inválido',\n    'Loading...': 'Cargando...',\n    'New Password': 'Nueva contraseña',\n    'Resend a Code': 'Reenviar un código',\n    'Sign Out': 'Cerrar sesión',\n    'Sign Up with Amazon': 'Crear cuenta con Amazon',\n    'Sign Up with Apple': 'Crear cuenta con Apple',\n    'Sign Up with Facebook': 'Crear cuenta con Facebook',\n    'Sign Up with Google': 'Crear cuenta con Google',\n    'Sign Up': 'Crear cuenta',\n    'User already exists': 'El usuario ya existe',\n    'User does not exist': 'El usuario no existe',\n    'Username/client id combination not found.': 'El usuario no existe',\n    'Username cannot be empty': 'El nombre de usuario no puede estar vacío',\n    'Your passwords must match': 'Las contraseñas deben coincidir',\n    'Password must have at least 8 characters': 'La contraseña debe tener al menos 8 caracteres',\n    'Password did not conform with policy: Password must have uppercase characters': 'La contraseña debe tener al menos un carácter en mayúscula',\n    'Password did not conform with policy: Password must have numeric characters': 'La contraseña debe tener al menos un carácter numérico',\n    'Password did not conform with policy: Password must have symbol characters': 'La contraseña debe tener al menos un símbolo',\n    'Password did not conform with policy: Password must have lowercase characters': 'La contraseña debe tener al menos un carácter en minúsculas',\n    'Invalid verification code provided, please try again.': 'Código de verificación no válido, inténtelo de nuevo.',\n    'Attempt limit exceeded, please try after some time.': 'Número máximo de intentos excedido, por favor inténtelo de nuevo más tarde.',\n    'A network error has occurred.': 'Se ha producido un error de red.',\n};\n\nexport { esDict };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACX,wDAAwD,EAAE,0EAA0E;EACpI,0BAA0B,EAAE,oCAAoC;EAChE,iBAAiB,EAAE,2BAA2B;EAC9C,iBAAiB,EAAE,oBAAoB;EACvCC,QAAQ,EAAE,WAAW;EACrBC,IAAI,EAAE,QAAQ;EACd,QAAQ,EAAE,UAAU;EACpB,oBAAoB,EAAE,2CAA2C;EACjE,kBAAkB,EAAE,sBAAsB;EAC1C,iBAAiB,EAAE,oBAAoB;EACvC,kBAAkB,EAAE,4BAA4B;EAChD,mBAAmB,EAAE,uBAAuB;EAC5CC,OAAO,EAAE,WAAW;EACpB,mBAAmB,EAAE,wBAAwB;EAC7CC,UAAU,EAAE,aAAa;EACzB,sBAAsB,EAAE,wBAAwB;EAChD,gBAAgB,EAAE,cAAc;EAChC,kBAAkB,EAAE,gBAAgB;EACpC,eAAe,EAAE,kBAAkB;EACnCC,KAAK,EAAE,OAAO;EACd,eAAe,EAAE,+BAA+B;EAChD,iBAAiB,EAAE,mBAAmB;EACtC,kBAAkB,EAAE,kBAAkB;EACtC,kBAAkB,EAAE,kBAAkB;EACtC,qBAAqB,EAAE,uBAAuB;EAC9C,yBAAyB,EAAE,+BAA+B;EAC1D,qBAAqB,EAAE,8BAA8B;EACrD,uBAAuB,EAAE,wBAAwB;EACjD,eAAe,EAAE,oBAAoB;EACrC,gCAAgC,EAAE,0CAA0C;EAC5EC,OAAO,EAAE,UAAU;EACnB,6BAA6B,EAAE,2BAA2B;EAC1D,mCAAmC,EAAE,4CAA4C;EACjF,cAAc,EAAE,kBAAkB;EAClCC,EAAE,EAAE,GAAG;EACPC,QAAQ,EAAE,YAAY;EACtB,cAAc,EAAE,oBAAoB;EACpC,aAAa,EAAE,iBAAiB;EAChC,qBAAqB,EAAE,2BAA2B;EAClD,qBAAqB,EAAE,2BAA2B;EAClD,iBAAiB,EAAE,4BAA4B;EAC/C,WAAW,EAAE,eAAe;EAC5B,WAAW,EAAE,eAAe;EAC5BC,OAAO,EAAE,UAAU;EACnB,aAAa,EAAE,+BAA+B;EAC9C,YAAY,EAAE,iBAAiB;EAC/B,eAAe,EAAE,oBAAoB;EACrC,yBAAyB,EAAE,6BAA6B;EACxD,qBAAqB,EAAE,2BAA2B;EAClD,oBAAoB,EAAE,0BAA0B;EAChD,uBAAuB,EAAE,6BAA6B;EACtD,qBAAqB,EAAE,2BAA2B;EAClD,SAAS,EAAE,gBAAgB;EAC3B,SAAS,EAAE,gBAAgB;EAC3B,YAAY,EAAE,kBAAkB;EAChCC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,UAAU;EACtB,oBAAoB,EAAE,wBAAwB;EAC9CC,QAAQ,EAAE,mBAAmB;EAC7B,gBAAgB,EAAE,oBAAoB;EACtCC,MAAM,EAAE,WAAW;EACnB,gBAAgB,EAAE,wCAAwC;EAC1D,gBAAgB,EAAE,yBAAyB;EAC3C,eAAe,EAAE,sCAAsC;EACvD,kEAAkE,EAAE,6GAA6G;EACjL,gEAAgE,EAAE,uFAAuF;EACzJ,iEAAiE,EAAE,2GAA2G;EAC9K;EACA,iDAAiD,EAAE,+CAA+C;EAClG,gBAAgB,EAAE,qBAAqB;EACvC,iBAAiB,EAAE,4BAA4B;EAC/C,iBAAiB,EAAE,sBAAsB;EACzC,iCAAiC,EAAE,2CAA2C;EAC9E,wBAAwB,EAAE,qBAAqB;EAC/C,uBAAuB,EAAE,mBAAmB;EAC5C,YAAY,EAAE,QAAQ;EACtB,aAAa,EAAE,UAAU;EACzB,gBAAgB,EAAE,wBAAwB;EAC1C,8BAA8B,EAAE,wBAAwB;EACxD,yBAAyB,EAAE,gCAAgC;EAC3D,6BAA6B,EAAE,wCAAwC;EACvE,YAAY,EAAE,aAAa;EAC3B,cAAc,EAAE,kBAAkB;EAClC,eAAe,EAAE,oBAAoB;EACrC,UAAU,EAAE,eAAe;EAC3B,qBAAqB,EAAE,yBAAyB;EAChD,oBAAoB,EAAE,wBAAwB;EAC9C,uBAAuB,EAAE,2BAA2B;EACpD,qBAAqB,EAAE,yBAAyB;EAChD,SAAS,EAAE,cAAc;EACzB,qBAAqB,EAAE,sBAAsB;EAC7C,qBAAqB,EAAE,sBAAsB;EAC7C,2CAA2C,EAAE,sBAAsB;EACnE,0BAA0B,EAAE,2CAA2C;EACvE,2BAA2B,EAAE,iCAAiC;EAC9D,0CAA0C,EAAE,gDAAgD;EAC5F,+EAA+E,EAAE,4DAA4D;EAC7I,6EAA6E,EAAE,wDAAwD;EACvI,4EAA4E,EAAE,8CAA8C;EAC5H,+EAA+E,EAAE,6DAA6D;EAC9I,uDAAuD,EAAE,uDAAuD;EAChH,qDAAqD,EAAE,6EAA6E;EACpI,+BAA+B,EAAE;AACrC,CAAC;AAED,SAASd,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}