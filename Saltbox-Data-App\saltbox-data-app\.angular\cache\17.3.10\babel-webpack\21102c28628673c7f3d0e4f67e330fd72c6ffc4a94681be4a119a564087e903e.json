{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { formatCurrency } from 'src/app/shared/utilities/format.functions';\nimport { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';\nimport { MenuModule } from 'primeng/menu';\nimport { ButtonModule } from 'primeng/button';\nimport { BadgeModule } from 'primeng/badge';\nimport { NgClass, NgIf } from '@angular/common';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ToolbarModule } from 'primeng/toolbar';\nimport { SplitButtonModule } from 'primeng/splitbutton';\nimport { ActionableGridViewModeLabels, ActionableGridViewModes } from 'src/app/core/enums/actionable-grid';\nimport { GridLayoutManagerComponent } from './grid-layout-manager/grid-layout-manager.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/communication.service\";\nimport * as i2 from \"src/app/core/services/user-activity.service\";\nimport * as i3 from \"primeng/toolbar\";\nimport * as i4 from \"primeng/tooltip\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/menu\";\nimport * as i8 from \"primeng/splitbutton\";\nconst _c0 = () => ({\n  width: \"auto\"\n});\nconst _c1 = a0 => ({\n  \"bg-blue-100\": a0\n});\nconst _c2 = a0 => ({\n  \"vertical-align-top\": a0\n});\nconst _c3 = a0 => ({\n  \"font-bold\": a0\n});\nfunction ActionsMenuComponent_Conditional_1_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 29);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_7_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onClickShowValidationResults());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"badge\", ctx_r3.validationResultsCount);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 30);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_8_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onUpdateUserFavorite());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"icon\", ctx_r3.isFavorite ? \"pi pi-star-fill\" : \"pi pi-star\")(\"pTooltip\", ctx_r3.isFavorite ? \"Remove Favorite\" : \"Add Favorite\");\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 31);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_10_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onClickShowFormEditor(false));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !(ctx_r3.reportInfo == null ? null : ctx_r3.reportInfo.allowAddNewRow) || ctx_r3.disabled);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 32);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_11_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onClickShowFormEditor(false));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !(ctx_r3.reportInfo == null ? null : ctx_r3.reportInfo.allowAddNewRow) || ctx_r3.disabled);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 33);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_13_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onClickShowAgent());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_15_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"span\", 40);\n    i0.ɵɵelement(2, \"i\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 42);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c1, item_r10.value === ctx_r3.selectedView));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(item_r10.icon);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c2, item_r10.icon === \"sb sb-icon-slice\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c3, item_r10.value === ctx_r3.selectedView));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r10.label);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-menu\", 25, 2);\n    i0.ɵɵtemplate(2, ActionsMenuComponent_Conditional_1_Conditional_15_ng_template_2_Template, 5, 12, \"ng-template\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-button\", 35);\n    i0.ɵɵlistener(\"click\", function ActionsMenuComponent_Conditional_1_Conditional_15_Template_p_button_click_3_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const calendar_view_r11 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(calendar_view_r11.toggle($event));\n    });\n    i0.ɵɵelementStart(4, \"span\", 36);\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵelementStart(6, \"span\", 37);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"popup\", true)(\"model\", ctx_r3.calendarViews);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"rounded\", true)(\"disabled\", ctx_r3.disabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"\", ctx_r3.selectedViewIcon, \" vertical-align-middle\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.selectedView);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 43);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_16_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onClickShowRefiner());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.disabled);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_p_toggleswitch_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-toggleswitch\", 44);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ActionsMenuComponent_Conditional_1_p_toggleswitch_26_Template_p_toggleswitch_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.pivotMode, $event) || (ctx_r3.pivotMode = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.pivotMode);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-toolbar\", 10)(1, \"div\", 11)(2, \"h3\");\n    i0.ɵɵelement(3, \"i\", 7);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 12)(6, \"span\", 13);\n    i0.ɵɵtemplate(7, ActionsMenuComponent_Conditional_1_Conditional_7_Template, 1, 1, \"p-button\", 14)(8, ActionsMenuComponent_Conditional_1_Conditional_8_Template, 1, 2, \"p-button\", 15);\n    i0.ɵɵelementStart(9, \"span\", 16);\n    i0.ɵɵtemplate(10, ActionsMenuComponent_Conditional_1_Conditional_10_Template, 1, 1, \"p-button\", 17)(11, ActionsMenuComponent_Conditional_1_Conditional_11_Template, 1, 1);\n    i0.ɵɵelementStart(12, \"p-splitButton\", 18);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Template_p_splitButton_onClick_12_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.undoActions[0] == null ? null : ctx_r3.undoActions[0].command());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, ActionsMenuComponent_Conditional_1_Conditional_13_Template, 1, 0, \"p-button\", 19);\n    i0.ɵɵelementStart(14, \"app-grid-layout-manager\", 20);\n    i0.ɵɵlistener(\"selectedLayoutChange\", function ActionsMenuComponent_Conditional_1_Template_app_grid_layout_manager_selectedLayoutChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSelectedLayoutChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, ActionsMenuComponent_Conditional_1_Conditional_15_Template, 9, 8)(16, ActionsMenuComponent_Conditional_1_Conditional_16_Template, 1, 1, \"p-button\", 21);\n    i0.ɵɵelementStart(17, \"p-button\", 22);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Template_p_button_onClick_17_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const menu_download_r13 = i0.ɵɵreference(23);\n      return i0.ɵɵresetView(menu_download_r13.toggle($event));\n    });\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵelement(19, \"i\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵelement(21, \"i\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(22, \"p-menu\", 25, 1);\n    i0.ɵɵelementStart(24, \"p-button\", 26);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Template_p_button_onClick_24_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClickRefreshGridData());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p-button\", 27);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Template_p_button_onClick_25_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClickSaveChanges());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, ActionsMenuComponent_Conditional_1_p_toggleswitch_26_Template, 1, 1, \"p-toggleswitch\", 28);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(24, _c0));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.reportInfo == null ? null : ctx_r3.reportInfo.description, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(7, ctx_r3.validationResultsCount ? 7 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(8, !ctx_r3.previewMode ? 8 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"pTooltip\", ctx_r3.disabled ? \"Project is locked\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(10, !(ctx_r3.reportInfo == null ? null : ctx_r3.reportInfo.allowAddNewRow) || ctx_r3.disabled ? 10 : 11);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"icon\", ctx_r3.undoActions[0] == null ? null : ctx_r3.undoActions[0].icon)(\"disabled\", !ctx_r3.checkHasChanges || ctx_r3.disabled)(\"model\", ctx_r3.undoActions);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(13, ctx_r3.agentChatFlag ? 13 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"reportInfo\", ctx_r3.reportInfo)(\"agGrid\", ctx_r3.agGrid)(\"disabled\", ctx_r3.disabled)(\"layouts\", ctx_r3.layouts);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(15, ctx_r3.calendarViewFlag && (ctx_r3.reportInfo == null ? null : ctx_r3.reportInfo.enableCalendarView) ? 15 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(16, ctx_r3.checkHasSlicers ? 16 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rounded\", true)(\"disabled\", ctx_r3.disabled);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"popup\", true)(\"model\", ctx_r3.exportItems);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.checkHasChanges || ctx_r3.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.enablePivoting);\n  }\n}\nexport class ActionsMenuComponent {\n  constructor(communicationService, changeDetectorRef, userActivityService) {\n    this.communicationService = communicationService;\n    this.changeDetectorRef = changeDetectorRef;\n    this.userActivityService = userActivityService;\n    this.hasFailures = false;\n    this.disabled = false;\n    this.selectedView = ActionableGridViewModes.Month;\n    this.calendarViewFlag = false;\n    this.agentChatFlag = false;\n    this.enablePivoting = false;\n    this.pivotMode = false;\n    this.showFormEditor = new EventEmitter();\n    this.undoLastChange = new EventEmitter();\n    this.undoAll = new EventEmitter();\n    this.saveChanges = new EventEmitter();\n    this.refreshGridData = new EventEmitter();\n    this.sendEmailClick = new EventEmitter();\n    this.showRefiner = new EventEmitter();\n    this.showValidationResults = new EventEmitter();\n    this.changeSelectedView = new EventEmitter();\n    this.showAgentChat = new EventEmitter();\n    this.exportItems = [{\n      value: null,\n      styleClass: 'hidden'\n    }, {\n      label: 'Send Email',\n      icon: 'pi pi-envelope',\n      command: () => {\n        this.onClickSendEmail();\n      }\n    }, {\n      label: 'Export as Excel',\n      icon: 'pi pi-file-excel',\n      command: () => {\n        this.onExcelExportClick();\n      }\n    }, {\n      label: 'Export as CSV',\n      icon: 'pi pi-file',\n      command: () => {\n        this.onCsvExportClick();\n      }\n    }];\n    this.calendarViews = [];\n    this.undoActions = [];\n    this.selectedViewIcon = 'fa-regular fa-calendar';\n    this.allReportFavorites = [];\n  }\n  ngOnInit() {\n    this.userFavoriteSub = this.communicationService.userFavorite.subscribe(event => {\n      this.allReportFavorites = event.data;\n      this.setIsFavorite();\n    });\n    this.communicationService.refreshUserFavoriteList();\n    this.mobileActions = [{\n      label: 'Favorite',\n      icon: 'pi pi-star',\n      command: () => {\n        this.onUpdateUserFavorite();\n      }\n    }, {\n      label: 'Add',\n      icon: 'pi pi-plus',\n      command: () => {\n        this.onClickShowFormEditor(false);\n      }\n    }, {\n      label: 'Undo',\n      icon: 'sb sb-icon-undo',\n      command: () => {\n        this.onClickUndoLastChange();\n      }\n    }, {\n      label: 'Undo All',\n      icon: 'pi pi-times',\n      command: () => {\n        this.onClickUndoAll();\n      }\n    }, {\n      label: 'Save',\n      icon: 'pi pi-save',\n      command: () => {\n        this.onClickSaveChanges();\n      }\n    }, {\n      label: 'Refresh',\n      icon: 'pi pi-sync',\n      command: () => {\n        this.onClickRefreshGridData();\n      }\n    }, {\n      label: 'Send Email',\n      icon: 'pi pi-envelope',\n      command: () => {\n        this.onClickSendEmail();\n      }\n    }, {\n      label: 'Export to Excel',\n      icon: 'pi pi-file-excel',\n      command: () => {\n        this.onExcelExportClick();\n      }\n    }, {\n      label: 'Export to CSV',\n      icon: 'pi pi-file',\n      command: () => {\n        this.onCsvExportClick();\n      }\n    }];\n    this.calendarViews = [{\n      value: null,\n      styleClass: 'hidden'\n    }, {\n      value: ActionableGridViewModes.Table,\n      label: ActionableGridViewModeLabels.Table,\n      icon: 'pi pi-list',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Table, 'pi pi-list')\n    }, {\n      value: ActionableGridViewModes.Year,\n      label: ActionableGridViewModeLabels.Year,\n      icon: 'fa-solid fa-calendar',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Year, 'fa-solid fa-calendar')\n    }, {\n      value: ActionableGridViewModes.Month,\n      label: ActionableGridViewModeLabels.Month,\n      icon: 'fa-regular fa-calendar',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Month, 'fa-regular fa-calendar')\n    }, {\n      value: ActionableGridViewModes.Week,\n      label: ActionableGridViewModeLabels.Week,\n      icon: 'fa-solid fa-calendar-week',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Week, 'fa-solid fa-calendar-week')\n    }];\n    this.undoActions = [{\n      label: 'Undo',\n      icon: 'sb sb-icon-undo',\n      command: () => this.onClickUndoLastChange()\n    }, {\n      label: 'Undo All',\n      icon: 'pi pi-times',\n      command: () => this.onClickUndoAll()\n    }];\n  }\n  ngOnDestroy() {\n    if (this.userFavoriteSub) {\n      this.userFavoriteSub.unsubscribe();\n    }\n  }\n  onClickShowFormEditor(arg) {\n    if (this.disabled) return;\n    this.showFormEditor.emit(arg);\n  }\n  onClickUndoLastChange() {\n    if (this.disabled) return;\n    this.undoLastChange.emit();\n  }\n  onClickUndoAll() {\n    if (this.disabled) return;\n    this.undoAll.emit();\n  }\n  onClickSaveChanges() {\n    if (this.disabled) return;\n    this.saveChanges.emit();\n  }\n  onClickRefreshGridData() {\n    if (this.disabled) return;\n    this.refreshGridData.emit();\n  }\n  onClickShowRefiner() {\n    if (this.disabled) return;\n    this.showRefiner.emit();\n  }\n  onClickShowValidationResults() {\n    this.showValidationResults.emit();\n  }\n  onClickShowAgent() {\n    this.showAgentChat.emit();\n  }\n  setIsFavorite() {\n    this.isFavorite = this.allReportFavorites.some(f => f.reportId === this.reportInfo?.reportId && f.isApp);\n    // for some weird reason angular doesn't detect the changes\n    this.changeDetectorRef.detectChanges();\n  }\n  onUpdateUserFavorite() {\n    let userFavorite = this.allReportFavorites?.find(favorite => favorite.projectId.toString() === this.reportInfo.projectId && favorite.reportId === this.reportInfo.reportId);\n    if (userFavorite) {\n      userFavorite.active = false;\n      userFavorite.isApp = window.location.pathname.includes('app-view') ? true : false;\n    } else {\n      userFavorite = this.addNewUserFavorite();\n    }\n    this.userActivityService.upsertUserFavorite(userFavorite);\n    // because of the delay we set isFavorite temporarily, after updating is done it will update again.\n    this.isFavorite = userFavorite.active;\n  }\n  addNewUserFavorite() {\n    const userFavorite = {\n      projectId: +this.reportInfo.projectId,\n      url: window.location.pathname,\n      reportId: this.reportInfo.reportId,\n      reportName: this.reportInfo.reportName,\n      projectVersionId: +this.reportInfo.projectVersionId,\n      active: true,\n      isApp: window.location.pathname.includes('app-view') ? true : false\n    };\n    return userFavorite;\n  }\n  onClickSendEmail() {\n    if (this.disabled) return;\n    this.sendEmailClick.emit();\n  }\n  onExcelExportClick() {\n    if (this.disabled) return;\n    this.agGrid.api.exportDataAsExcel({\n      processCellCallback: params => {\n        return this.processCellsForExport(params);\n      },\n      columnKeys: this.columnsToExport,\n      fileName: this.reportInfo.reportName,\n      sheetName: this.reportInfo.reportName\n    });\n  }\n  onCsvExportClick() {\n    if (this.disabled) return;\n    this.agGrid.api.exportDataAsCsv({\n      processCellCallback: params => {\n        return this.processCellsForExport(params);\n      },\n      columnKeys: this.columnsToExport,\n      fileName: this.reportInfo.reportName\n    });\n  }\n  processCellsForExport(params) {\n    const exportParamsColDef = params.column.getColDef();\n    const actionableGridColDef = this.reportInfo.formatConfig?.actionableGridColumnsConfig.find(x => x.column === exportParamsColDef.field);\n    if (actionableGridColDef?.format.type === EditorColumnType.Currency) {\n      exportParamsColDef.valueFormatter = formatCurrency(params.value, actionableGridColDef.format.currency, actionableGridColDef.format.decimalPlaces);\n      return exportParamsColDef.valueFormatter;\n    }\n    return params.value;\n  }\n  onChangeSelectedView(view, icon) {\n    this.changeSelectedView.emit(view);\n    this.selectedViewIcon = icon;\n    this.selectedView = view;\n    // Resize columns if no layout is selected\n    setTimeout(() => {\n      if (this.slectedLayout === undefined) {\n        this.agGrid.api?.autoSizeAllColumns();\n      }\n    }, 0);\n  }\n  onSelectedLayoutChange(layout) {\n    this.slectedLayout = layout;\n  }\n  static {\n    this.ɵfac = function ActionsMenuComponent_Factory(t) {\n      return new (t || ActionsMenuComponent)(i0.ɵɵdirectiveInject(i1.CommunicationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.UserActivityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActionsMenuComponent,\n      selectors: [[\"app-actions-menu\"]],\n      inputs: {\n        previewMode: \"previewMode\",\n        reportInfo: \"reportInfo\",\n        checkHasChanges: \"checkHasChanges\",\n        checkHasSlicers: \"checkHasSlicers\",\n        checkHasPendingChanges: \"checkHasPendingChanges\",\n        agGrid: \"agGrid\",\n        columnsToExport: \"columnsToExport\",\n        validationResultsCount: \"validationResultsCount\",\n        hasFailures: \"hasFailures\",\n        disabled: \"disabled\",\n        selectedView: \"selectedView\",\n        calendarViewFlag: \"calendarViewFlag\",\n        agentChatFlag: \"agentChatFlag\",\n        enablePivoting: \"enablePivoting\",\n        pivotMode: \"pivotMode\",\n        layouts: \"layouts\"\n      },\n      outputs: {\n        showFormEditor: \"showFormEditor\",\n        undoLastChange: \"undoLastChange\",\n        undoAll: \"undoAll\",\n        saveChanges: \"saveChanges\",\n        refreshGridData: \"refreshGridData\",\n        sendEmailClick: \"sendEmailClick\",\n        showRefiner: \"showRefiner\",\n        showValidationResults: \"showValidationResults\",\n        changeSelectedView: \"changeSelectedView\",\n        showAgentChat: \"showAgentChat\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 9,\n      vars: 4,\n      consts: [[\"menu\", \"\"], [\"menu_download\", \"\"], [\"calendar_view\", \"\"], [1, \"grid-toolbar\"], [1, \"desktop-grid-tb\", 3, \"style\"], [1, \"mobile-app-menu\"], [1, \"report-title\"], [1, \"pi\", \"pi-chart-line\"], [\"appendTo\", \"body\", 3, \"model\", \"popup\"], [\"type\", \"button\", \"icon\", \"pi pi-ellipsis-h\", 1, \"mobile-menu-tb\", 3, \"onClick\"], [1, \"desktop-grid-tb\"], [1, \"p-toolbar-group-start\"], [1, \"p-toolbar-group-end\"], [1, \"tb-menu-desktop\", \"inline-flex\", \"align-items-center\"], [\"icon\", \"fa-solid fa-exclamation\", \"severity\", \"warning\", \"pTooltip\", \"Show Errors/Warnings\", \"tooltipPosition\", \"top\", \"styleClass\", \"p-overlay-badge mr-3\", \"badgeClass\", \"p-badge-warning\", 3, \"badge\"], [\"tooltipPosition\", \"top\", 3, \"icon\", \"pTooltip\"], [\"tooltipPosition\", \"top\", 1, \"inline-flex\", \"align-items-center\", 3, \"pTooltip\"], [\"pTooltip\", \"Add Row (permission required)\", \"tooltipPosition\", \"top\", \"icon\", \"pi pi-plus\", 3, \"disabled\"], [\"appendTo\", \"body\", 3, \"onClick\", \"icon\", \"disabled\", \"model\"], [\"icon\", \"fa-regular fa-comment\", \"pTooltip\", \"Launch Agent Chat\"], [3, \"selectedLayoutChange\", \"reportInfo\", \"agGrid\", \"disabled\", \"layouts\"], [\"icon\", \"pi pi-filter\", \"pTooltip\", \"Filter Your Data\", \"tooltipPosition\", \"top\", 3, \"disabled\"], [\"pTooltip\", \"Export\", \"tooltipPosition\", \"top\", \"styleClass\", \"min-w-0\", 3, \"onClick\", \"rounded\", \"disabled\"], [1, \"pi\", \"pi-file-export\", \"vertical-align-bottom\"], [1, \"pi\", \"pi-angle-down\", \"vertical-align-middle\", \"ml-1\"], [\"appendTo\", \"body\", 3, \"popup\", \"model\"], [\"icon\", \"pi pi-refresh\", \"pTooltip\", \"Refresh\", \"tooltipPosition\", \"top\", 3, \"onClick\", \"disabled\"], [\"icon\", \"pi pi-save\", \"pTooltip\", \"Save\", \"tooltipPosition\", \"top\", 3, \"onClick\", \"disabled\"], [3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"icon\", \"fa-solid fa-exclamation\", \"severity\", \"warning\", \"pTooltip\", \"Show Errors/Warnings\", \"tooltipPosition\", \"top\", \"styleClass\", \"p-overlay-badge mr-3\", \"badgeClass\", \"p-badge-warning\", 3, \"onClick\", \"badge\"], [\"tooltipPosition\", \"top\", 3, \"onClick\", \"icon\", \"pTooltip\"], [\"pTooltip\", \"Add Row (permission required)\", \"tooltipPosition\", \"top\", \"icon\", \"pi pi-plus\", 3, \"onClick\", \"disabled\"], [\"icon\", \"pi pi-plus\", 3, \"onClick\", \"disabled\"], [\"icon\", \"fa-regular fa-comment\", \"pTooltip\", \"Launch Agent Chat\", 3, \"onClick\"], [\"pTemplate\", \"item\"], [\"pTooltip\", \"Switch View\", \"tooltipPosition\", \"top\", \"styleClass\", \"min-w-0\", 3, \"click\", \"rounded\", \"disabled\"], [1, \"inline-flex\", \"align-items-center\"], [1, \"ml-2\"], [1, \"pi\", \"pi-angle-down\", \"vertical-align-middle\", \"ml-2\"], [\"pRipple\", \"\", 1, \"p-ripple\", \"cursor-pointer\", \"p-element\", \"flex\", \"items-center\", \"p-menu-item-link\", \"p-3\", 3, \"ngClass\"], [1, \"text-base\", \"text-base-text\"], [1, \"p-menuitem-icon\", 3, \"ngClass\"], [1, \"ml-2\", \"p-menuitem-text\", \"text-sm\", \"text-base-text\", \"vertical-align-middle\", \"line-height-3\", 3, \"ngClass\"], [\"icon\", \"pi pi-filter\", \"pTooltip\", \"Filter Your Data\", \"tooltipPosition\", \"top\", 3, \"onClick\", \"disabled\"], [3, \"ngModelChange\", \"ngModel\"]],\n      template: function ActionsMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 3);\n          i0.ɵɵtemplate(1, ActionsMenuComponent_Conditional_1_Template, 27, 25, \"p-toolbar\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 5)(3, \"span\", 6);\n          i0.ɵɵelement(4, \"i\", 7);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"p-menu\", 8, 0);\n          i0.ɵɵelementStart(8, \"p-button\", 9);\n          i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Template_p_button_onClick_8_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const menu_r15 = i0.ɵɵreference(7);\n            return i0.ɵɵresetView(menu_r15.toggle($event));\n          });\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, (ctx.calendarViews == null ? null : ctx.calendarViews.length) > 0 && (ctx.undoActions == null ? null : ctx.undoActions.length) > 0 ? 1 : -1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.reportInfo == null ? null : ctx.reportInfo.description, \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"model\", ctx.mobileActions)(\"popup\", true);\n        }\n      },\n      dependencies: [ToolbarModule, i3.Toolbar, TooltipModule, i4.Tooltip, BadgeModule, i5.PrimeTemplate, NgClass, ButtonModule, i6.Button, MenuModule, i7.Menu, SplitButtonModule, i8.SplitButton, NgIf, GridLayoutManagerComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "formatCurrency", "EditorColumnType", "MenuModule", "ButtonModule", "BadgeModule", "Ng<PERSON><PERSON>", "NgIf", "TooltipModule", "ToolbarModule", "SplitButtonModule", "ActionableGridViewModeLabels", "ActionableGridViewModes", "GridLayoutManagerComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "ActionsMenuComponent_Conditional_1_Conditional_7_Template_p_button_onClick_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "onClickShowValidationResults", "ɵɵelementEnd", "ɵɵproperty", "validationResultsCount", "ActionsMenuComponent_Conditional_1_Conditional_8_Template_p_button_onClick_0_listener", "_r5", "onUpdateUserFavorite", "isFavorite", "ActionsMenuComponent_Conditional_1_Conditional_10_Template_p_button_onClick_0_listener", "_r6", "onClickShowFormEditor", "reportInfo", "allowAddNewRow", "disabled", "ActionsMenuComponent_Conditional_1_Conditional_11_Template_p_button_onClick_0_listener", "_r7", "ActionsMenuComponent_Conditional_1_Conditional_13_Template_p_button_onClick_0_listener", "_r8", "onClickShowAgent", "ɵɵelement", "ɵɵtext", "ɵɵpureFunction1", "_c1", "item_r10", "value", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵadvance", "ɵɵclassMap", "icon", "_c2", "_c3", "ɵɵtextInterpolate", "label", "ɵɵtemplate", "ActionsMenuComponent_Conditional_1_Conditional_15_ng_template_2_Template", "ActionsMenuComponent_Conditional_1_Conditional_15_Template_p_button_click_3_listener", "$event", "_r9", "calendar_view_r11", "ɵɵreference", "toggle", "calendarViews", "ɵɵclassMapInterpolate1", "selectedViewIcon", "ActionsMenuComponent_Conditional_1_Conditional_16_Template_p_button_onClick_0_listener", "_r12", "onClickShowRefiner", "ɵɵtwoWayListener", "ActionsMenuComponent_Conditional_1_p_toggleswitch_26_Template_p_toggleswitch_ngModelChange_0_listener", "_r14", "ɵɵtwoWayBindingSet", "pivotMode", "ɵɵtwoWayProperty", "ActionsMenuComponent_Conditional_1_Conditional_7_Template", "ActionsMenuComponent_Conditional_1_Conditional_8_Template", "ActionsMenuComponent_Conditional_1_Conditional_10_Template", "ActionsMenuComponent_Conditional_1_Conditional_11_Template", "ActionsMenuComponent_Conditional_1_Template_p_splitButton_onClick_12_listener", "_r2", "undoActions", "command", "ActionsMenuComponent_Conditional_1_Conditional_13_Template", "ActionsMenuComponent_Conditional_1_Template_app_grid_layout_manager_selectedLayoutChange_14_listener", "onSelectedLayoutChange", "ActionsMenuComponent_Conditional_1_Conditional_15_Template", "ActionsMenuComponent_Conditional_1_Conditional_16_Template", "ActionsMenuComponent_Conditional_1_Template_p_button_onClick_17_listener", "menu_download_r13", "ActionsMenuComponent_Conditional_1_Template_p_button_onClick_24_listener", "onClickRefreshGridData", "ActionsMenuComponent_Conditional_1_Template_p_button_onClick_25_listener", "onClickSaveChanges", "ActionsMenuComponent_Conditional_1_p_toggleswitch_26_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵtextInterpolate1", "description", "ɵɵconditional", "previewMode", "ɵɵpropertyInterpolate", "checkHasChanges", "agentChatFlag", "agGrid", "layouts", "calendarViewFlag", "enableCalendarView", "checkHasSlicers", "exportItems", "enablePivoting", "ActionsMenuComponent", "constructor", "communicationService", "changeDetectorRef", "userActivityService", "hasFailures", "Month", "showFormEditor", "undoLastChange", "undoAll", "saveChanges", "refreshGridData", "sendEmailClick", "showRefiner", "showValidationResults", "changeSelectedView", "showAgentChat", "styleClass", "onClickSendEmail", "onExcelExportClick", "onCsvExportClick", "allReportFavorites", "ngOnInit", "userFavoriteSub", "userFavorite", "subscribe", "event", "data", "setIsFavorite", "refreshUserFavoriteList", "mobileActions", "onClickUndoLastChange", "onClickUndoAll", "Table", "onChangeSelectedView", "Year", "Week", "ngOnDestroy", "unsubscribe", "arg", "emit", "some", "f", "reportId", "isApp", "detectChanges", "find", "favorite", "projectId", "toString", "active", "window", "location", "pathname", "includes", "addNewUserFavorite", "upsertUserFavorite", "url", "reportName", "projectVersionId", "api", "exportDataAsExcel", "processCellCallback", "params", "processCellsForExport", "columnKeys", "columnsToExport", "fileName", "sheetName", "exportDataAsCsv", "exportParamsColDef", "column", "getColDef", "actionableGridColDef", "formatConfig", "actionableGridColumnsConfig", "x", "field", "format", "type", "<PERSON><PERSON><PERSON><PERSON>", "valueFormatter", "currency", "decimalPlaces", "view", "setTimeout", "s<PERSON><PERSON><PERSON><PERSON>", "undefined", "autoSizeAllColumns", "layout", "ɵɵdirectiveInject", "i1", "CommunicationService", "ChangeDetectorRef", "i2", "UserActivityService", "selectors", "inputs", "checkHasPendingChanges", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ActionsMenuComponent_Template", "rf", "ctx", "ActionsMenuComponent_Conditional_1_Template", "ActionsMenuComponent_Template_p_button_onClick_8_listener", "_r1", "menu_r15", "length", "i3", "<PERSON><PERSON><PERSON>", "i4", "<PERSON><PERSON><PERSON>", "i5", "PrimeTemplate", "i6", "<PERSON><PERSON>", "i7", "<PERSON><PERSON>", "i8", "SplitButton", "styles"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\actions-menu\\actions-menu.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\actions-menu\\actions-menu.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';\r\nimport { AgGridAngular } from 'ag-grid-angular';\r\nimport { ProcessCellForExportParams } from 'ag-grid-community';\r\nimport { Subscription } from 'rxjs';\r\nimport { UserFavorite } from 'src/app/core/models/user-favorite';\r\nimport { CommunicationService } from 'src/app/core/services/communication.service';\r\nimport { UserActivityService } from 'src/app/core/services/user-activity.service';\r\nimport { ReportInfo } from 'src/app/core/models/report-info';\r\nimport { formatCurrency } from 'src/app/shared/utilities/format.functions';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';\r\nimport { MenuModule } from 'primeng/menu';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { BadgeModule } from 'primeng/badge';\r\nimport { Ng<PERSON><PERSON>, NgIf } from '@angular/common';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { ToolbarModule } from 'primeng/toolbar';\r\nimport { SplitButtonModule } from 'primeng/splitbutton';\r\nimport { ActionableGridViewModeLabels, ActionableGridViewModes } from 'src/app/core/enums/actionable-grid';\r\nimport { GridLayoutManagerComponent } from './grid-layout-manager/grid-layout-manager.component';\r\nimport { GridLayout } from '../model/grid-layout';\r\n\r\n@Component({\r\n  selector: 'app-actions-menu',\r\n  templateUrl: './actions-menu.component.html',\r\n  styleUrls: ['./actions-menu.component.scss'],\r\n  standalone: true,\r\n  imports: [ToolbarModule, TooltipModule, BadgeModule, NgClass, ButtonModule, MenuModule, SplitButtonModule, NgIf, GridLayoutManagerComponent]\r\n})\r\nexport class ActionsMenuComponent implements OnInit, OnDestroy {\r\n\r\n  @Input() previewMode: boolean;\r\n  @Input() reportInfo: ReportInfo;\r\n  @Input() checkHasChanges: boolean;\r\n  @Input() checkHasSlicers: boolean;\r\n  @Input() checkHasPendingChanges: boolean;\r\n  @Input() agGrid: AgGridAngular;\r\n  @Input() columnsToExport: string[];\r\n  @Input() validationResultsCount: any;\r\n  @Input() hasFailures = false;\r\n  @Input() disabled = false;\r\n  @Input() selectedView = ActionableGridViewModes.Month;\r\n  @Input() calendarViewFlag = false;\r\n  @Input() agentChatFlag = false;\r\n  @Input() enablePivoting = false;\r\n  @Input() pivotMode = false;\r\n  @Input() layouts: GridLayout[];\r\n\r\n  @Output() showFormEditor: EventEmitter<any> = new EventEmitter();\r\n  @Output() undoLastChange: EventEmitter<any> = new EventEmitter();\r\n  @Output() undoAll: EventEmitter<any> = new EventEmitter();\r\n  @Output() saveChanges: EventEmitter<any> = new EventEmitter();\r\n  @Output() refreshGridData: EventEmitter<any> = new EventEmitter();\r\n  @Output() sendEmailClick: EventEmitter<any> = new EventEmitter();\r\n  @Output() showRefiner: EventEmitter<any> = new EventEmitter();\r\n  @Output() showValidationResults: EventEmitter<any> = new EventEmitter();\r\n  @Output() changeSelectedView: EventEmitter<string> = new EventEmitter();\r\n  @Output() showAgentChat: EventEmitter<string> = new EventEmitter();\r\n\r\n  mobileActions: MenuItem[];\r\n\r\n  exportItems = [\r\n    {\r\n      value: null,\r\n      styleClass: 'hidden'\r\n    },\r\n    {\r\n      label: 'Send Email', icon: 'pi pi-envelope', command: () => {\r\n        this.onClickSendEmail();\r\n      }\r\n    },\r\n    {\r\n      label: 'Export as Excel', icon: 'pi pi-file-excel', command: () => {\r\n        this.onExcelExportClick();\r\n      },\r\n    },\r\n    {\r\n      label: 'Export as CSV', icon: 'pi pi-file', command: () => {\r\n        this.onCsvExportClick();\r\n      }\r\n    }\r\n  ];\r\n\r\n  calendarViews = [];\r\n  undoActions = [];\r\n  isFavorite: boolean;\r\n  selectedViewIcon: string = 'fa-regular fa-calendar';\r\n  slectedLayout: GridLayout | undefined;\r\n\r\n  private userFavoriteSub: Subscription;\r\n  private allReportFavorites: UserFavorite[] = [];\r\n\r\n  constructor(\r\n    private communicationService: CommunicationService,\r\n    private changeDetectorRef: ChangeDetectorRef,\r\n    private userActivityService: UserActivityService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.userFavoriteSub = this.communicationService.userFavorite\r\n      .subscribe(event => {\r\n        this.allReportFavorites = event.data;\r\n        this.setIsFavorite();\r\n      });\r\n    this.communicationService.refreshUserFavoriteList();\r\n\r\n    this.mobileActions = [\r\n      {\r\n        label: 'Favorite',\r\n        icon: 'pi pi-star',\r\n        command: () => {\r\n          this.onUpdateUserFavorite();\r\n        }\r\n      },\r\n      {\r\n        label: 'Add',\r\n        icon: 'pi pi-plus',\r\n        command: () => {\r\n          this.onClickShowFormEditor(false);\r\n        }\r\n      },\r\n      {\r\n        label: 'Undo',\r\n        icon: 'sb sb-icon-undo',\r\n        command: () => {\r\n          this.onClickUndoLastChange();\r\n        }\r\n      },\r\n      {\r\n        label: 'Undo All',\r\n        icon: 'pi pi-times',\r\n        command: () => {\r\n          this.onClickUndoAll();\r\n        }\r\n      },\r\n      {\r\n        label: 'Save',\r\n        icon: 'pi pi-save',\r\n        command: () => {\r\n          this.onClickSaveChanges();\r\n        }\r\n      },\r\n      {\r\n        label: 'Refresh',\r\n        icon: 'pi pi-sync',\r\n        command: () => {\r\n          this.onClickRefreshGridData();\r\n        }\r\n      },\r\n      {\r\n        label: 'Send Email',\r\n        icon: 'pi pi-envelope',\r\n        command: () => {\r\n          this.onClickSendEmail();\r\n        }\r\n      },\r\n      {\r\n        label: 'Export to Excel',\r\n        icon: 'pi pi-file-excel',\r\n        command: () => {\r\n          this.onExcelExportClick();\r\n        }\r\n      },\r\n      {\r\n        label: 'Export to CSV',\r\n        icon: 'pi pi-file',\r\n        command: () => {\r\n          this.onCsvExportClick();\r\n        }\r\n      },\r\n    ];\r\n\r\n    this.calendarViews = [\r\n      {\r\n        value: null,\r\n        styleClass: 'hidden'\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Table, label: ActionableGridViewModeLabels.Table, icon: 'pi pi-list',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Table, 'pi pi-list')\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Year, label: ActionableGridViewModeLabels.Year, icon: 'fa-solid fa-calendar',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Year, 'fa-solid fa-calendar')\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Month, label: ActionableGridViewModeLabels.Month, icon: 'fa-regular fa-calendar',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Month, 'fa-regular fa-calendar')\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Week, label: ActionableGridViewModeLabels.Week, icon: 'fa-solid fa-calendar-week',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Week, 'fa-solid fa-calendar-week')\r\n      },\r\n    ];\r\n\r\n    this.undoActions = [\r\n      {\r\n        label: 'Undo', icon: 'sb sb-icon-undo', command: () => this.onClickUndoLastChange()\r\n      },\r\n      {\r\n        label: 'Undo All', icon: 'pi pi-times', command: () => this.onClickUndoAll()\r\n      },\r\n    ];\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.userFavoriteSub) {\r\n      this.userFavoriteSub.unsubscribe();\r\n    }\r\n  }\r\n\r\n  onClickShowFormEditor(arg: boolean): void {\r\n    if (this.disabled) return;\r\n\r\n    this.showFormEditor.emit(arg);\r\n  }\r\n\r\n  onClickUndoLastChange(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.undoLastChange.emit();\r\n  }\r\n\r\n  onClickUndoAll(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.undoAll.emit();\r\n  }\r\n\r\n  onClickSaveChanges(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.saveChanges.emit();\r\n  }\r\n\r\n  onClickRefreshGridData(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.refreshGridData.emit();\r\n  }\r\n\r\n  onClickShowRefiner(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.showRefiner.emit();\r\n  }\r\n\r\n  onClickShowValidationResults(): void {\r\n    this.showValidationResults.emit();\r\n  }\r\n\r\n  onClickShowAgent(): void {\r\n    this.showAgentChat.emit();\r\n  }\r\n\r\n  setIsFavorite() {\r\n    this.isFavorite = this.allReportFavorites.some(f => f.reportId === this.reportInfo?.reportId && f.isApp);\r\n    // for some weird reason angular doesn't detect the changes\r\n    this.changeDetectorRef.detectChanges();\r\n  }\r\n\r\n  onUpdateUserFavorite(): void {\r\n    let userFavorite: UserFavorite = this.allReportFavorites?.find(favorite =>\r\n      favorite.projectId.toString() === this.reportInfo.projectId && favorite.reportId === this.reportInfo.reportId);\r\n\r\n    if (userFavorite) {\r\n      userFavorite.active = false;\r\n      userFavorite.isApp = window.location.pathname.includes('app-view') ? true : false;\r\n    }\r\n    else {\r\n      userFavorite = this.addNewUserFavorite();\r\n    }\r\n    this.userActivityService.upsertUserFavorite(userFavorite);\r\n    // because of the delay we set isFavorite temporarily, after updating is done it will update again.\r\n    this.isFavorite = userFavorite.active;\r\n  }\r\n\r\n  addNewUserFavorite() {\r\n    const userFavorite: UserFavorite = {\r\n      projectId: +this.reportInfo.projectId,\r\n      url: window.location.pathname,\r\n      reportId: this.reportInfo.reportId,\r\n      reportName: this.reportInfo.reportName,\r\n      projectVersionId: +this.reportInfo.projectVersionId,\r\n      active: true,\r\n      isApp: window.location.pathname.includes('app-view') ? true : false\r\n    };\r\n    return userFavorite;\r\n  }\r\n\r\n  onClickSendEmail(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.sendEmailClick.emit();\r\n  }\r\n\r\n  onExcelExportClick() {\r\n    if (this.disabled) return;\r\n\r\n    this.agGrid.api.exportDataAsExcel({\r\n      processCellCallback: (params) => {\r\n        return this.processCellsForExport(params);\r\n      },\r\n      columnKeys: this.columnsToExport, fileName: this.reportInfo.reportName, sheetName: this.reportInfo.reportName\r\n    });\r\n  }\r\n\r\n  onCsvExportClick() {\r\n    if (this.disabled) return;\r\n\r\n    this.agGrid.api.exportDataAsCsv({\r\n      processCellCallback: (params) => {\r\n        return this.processCellsForExport(params);\r\n      },\r\n      columnKeys: this.columnsToExport, fileName: this.reportInfo.reportName\r\n    });\r\n  }\r\n\r\n  processCellsForExport(params: ProcessCellForExportParams) {\r\n    const exportParamsColDef = params.column.getColDef();\r\n    const actionableGridColDef = this.reportInfo.formatConfig?.actionableGridColumnsConfig.find(x => x.column === exportParamsColDef.field);\r\n    if (actionableGridColDef?.format.type === EditorColumnType.Currency) {\r\n      exportParamsColDef.valueFormatter = formatCurrency(\r\n        params.value,\r\n        actionableGridColDef.format.currency,\r\n        actionableGridColDef.format.decimalPlaces);\r\n      return exportParamsColDef.valueFormatter;\r\n    }\r\n\r\n    return params.value;\r\n  }\r\n\r\n  onChangeSelectedView(view: ActionableGridViewModes, icon: string) {\r\n    this.changeSelectedView.emit(view);\r\n    this.selectedViewIcon = icon;\r\n    this.selectedView = view;\r\n\r\n\r\n    // Resize columns if no layout is selected\r\n    setTimeout(() => {\r\n      if (this.slectedLayout === undefined) {\r\n        this.agGrid.api?.autoSizeAllColumns();\r\n      }\r\n    }, 0);\r\n  }\r\n\r\n  onSelectedLayoutChange(layout: GridLayout) {\r\n    this.slectedLayout = layout;\r\n  }\r\n}\r\n", "<div class=\"grid-toolbar\">\r\n  @if(this.calendarViews?.length > 0 && this.undoActions?.length > 0) {\r\n  <p-toolbar [style]=\"{width: 'auto'}\" class=\"desktop-grid-tb\">\r\n    <div class=\"p-toolbar-group-start\">\r\n      <h3><i class=\"pi pi-chart-line\"></i> {{reportInfo?.description}}</h3>\r\n    </div>\r\n    <div class=\"p-toolbar-group-end\">\r\n      <span class=\"tb-menu-desktop inline-flex align-items-center\">\r\n        @if (this.validationResultsCount) {\r\n        <p-button icon=\"fa-solid fa-exclamation\" severity=\"warning\" pTooltip=\"Show Errors/Warnings\"\r\n          tooltipPosition=\"top\" (onClick)=\"onClickShowValidationResults()\" styleClass=\"p-overlay-badge mr-3\"\r\n          [badge]=\"validationResultsCount\" badgeClass=\"p-badge-warning\"></p-button>\r\n        }\r\n\r\n        @if (!this.previewMode) {\r\n        <p-button [icon]=\"isFavorite ? 'pi pi-star-fill' : 'pi pi-star'\"\r\n          [pTooltip]=\"isFavorite ? 'Remove Favorite' : 'Add Favorite'\" tooltipPosition=\"top\"\r\n          (onClick)=\"onUpdateUserFavorite()\"></p-button>\r\n        }\r\n\r\n        <span pTooltip=\"{{disabled? 'Project is locked' : ''}}\" tooltipPosition=\"top\"\r\n          class=\"inline-flex align-items-center\">\r\n\r\n          @if (!this.reportInfo?.allowAddNewRow || disabled) {\r\n          <p-button pTooltip=\"Add Row (permission required)\" tooltipPosition=\"top\" icon=\"pi pi-plus\"\r\n            (onClick)=\"onClickShowFormEditor(false)\"\r\n            [disabled]=\"!this.reportInfo?.allowAddNewRow || disabled\"></p-button>\r\n          } @else {\r\n          <p-button icon=\"pi pi-plus\" (onClick)=\"onClickShowFormEditor(false)\"\r\n            [disabled]=\"!this.reportInfo?.allowAddNewRow || disabled\"></p-button>\r\n          }\r\n\r\n          <p-splitButton [icon]=\"undoActions[0]?.icon\" (onClick)=\"undoActions[0]?.command()\"\r\n            [disabled]=\"!checkHasChanges || disabled\" appendTo=\"body\" [model]=\"undoActions\"></p-splitButton>\r\n\r\n          @if (this.agentChatFlag) {\r\n          <p-button icon=\"fa-regular fa-comment\" pTooltip=\"Launch Agent Chat\" (onClick)=\"onClickShowAgent()\"></p-button>\r\n          }\r\n\r\n          <app-grid-layout-manager [reportInfo]=\"reportInfo\" [agGrid]=\"agGrid\" [disabled]=\"disabled\" [layouts]=\"layouts\"\r\n            (selectedLayoutChange)=\"onSelectedLayoutChange($event)\">\r\n          </app-grid-layout-manager>\r\n\r\n          @if (calendarViewFlag && reportInfo?.enableCalendarView) {\r\n          <p-menu #calendar_view [popup]=\"true\" [model]=\"this.calendarViews\" appendTo=\"body\">\r\n            <ng-template pTemplate=\"item\" let-item>\r\n              <div pRipple class=\"p-ripple cursor-pointer p-element flex items-center p-menu-item-link p-3\"\r\n                [ngClass]=\"{'bg-blue-100': (item.value === selectedView)}\">\r\n                <span class=\"text-base text-base-text\">\r\n                  <i class=\"p-menuitem-icon\" [class]=\"item.icon\"\r\n                    [ngClass]=\"{'vertical-align-top': (item.icon === 'sb sb-icon-slice')}\"></i>\r\n                </span>\r\n                <span class=\"ml-2 p-menuitem-text text-sm text-base-text vertical-align-middle line-height-3\"\r\n                  [ngClass]=\"{'font-bold': (item.value === selectedView)}\">{{ item.label }}</span>\r\n              </div>\r\n            </ng-template>\r\n          </p-menu>\r\n          <p-button [rounded]=\"true\" (click)=\"calendar_view.toggle($event)\" [disabled]=\"disabled\" pTooltip=\"Switch View\"\r\n            tooltipPosition=\"top\" styleClass=\"min-w-0\">\r\n            <span class=\"inline-flex align-items-center\">\r\n              <i class=\"{{selectedViewIcon}} vertical-align-middle\"></i>\r\n              <span class=\"ml-2\">{{selectedView}}</span>\r\n              <i class=\"pi pi-angle-down vertical-align-middle ml-2\"></i>\r\n            </span>\r\n          </p-button>\r\n          }\r\n\r\n          @if (checkHasSlicers) {\r\n          <p-button icon=\"pi pi-filter\" (onClick)=\"onClickShowRefiner()\" [disabled]=\"disabled\"\r\n            pTooltip=\"Filter Your Data\" tooltipPosition=\"top\"></p-button>\r\n          }\r\n\r\n          <p-button [rounded]=\"true\" (onClick)=\"menu_download.toggle($event)\" [disabled]=\"disabled\" pTooltip=\"Export\"\r\n            tooltipPosition=\"top\" styleClass=\"min-w-0\">\r\n            <span><i class=\"pi pi-file-export vertical-align-bottom\"></i></span>\r\n            <span><i class=\"pi pi-angle-down vertical-align-middle ml-1\"></i></span>\r\n          </p-button>\r\n          <p-menu #menu_download [popup]=\"true\" [model]=\"this.exportItems\" appendTo=\"body\"></p-menu>\r\n\r\n          <p-button icon=\"pi pi-refresh\" (onClick)=\"onClickRefreshGridData()\" [disabled]=\"disabled\" pTooltip=\"Refresh\"\r\n            tooltipPosition=\"top\"></p-button>\r\n\r\n          <p-button icon=\"pi pi-save\" (onClick)=\"onClickSaveChanges()\" [disabled]=\"!checkHasChanges || disabled\"\r\n            pTooltip=\"Save\" tooltipPosition=\"top\"></p-button>\r\n          <p-toggleswitch *ngIf=\"enablePivoting\" [(ngModel)]=\"pivotMode\" />\r\n        </span>\r\n      </span>\r\n    </div>\r\n  </p-toolbar>\r\n  }\r\n</div>\r\n<div class=\"mobile-app-menu\">\r\n  <span class=\"report-title\"><i class=\"pi pi-chart-line\"></i> {{reportInfo?.description}}</span>\r\n  <p-menu #menu [model]=\"mobileActions\" [popup]=\"true\" appendTo=\"body\"></p-menu>\r\n  <p-button class=\"mobile-menu-tb\" type=\"button\" (onClick)=\"menu.toggle($event)\" icon=\"pi pi-ellipsis-h\"></p-button>\r\n</div>"], "mappings": "AAAA,SAAuCA,YAAY,QAA0C,eAAe;AAQ5G,SAASC,cAAc,QAAQ,2CAA2C;AAE1E,SAASC,gBAAgB,QAAQ,8CAA8C;AAC/E,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,OAAO,EAAEC,IAAI,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,4BAA4B,EAAEC,uBAAuB,QAAQ,oCAAoC;AAC1G,SAASC,0BAA0B,QAAQ,qDAAqD;;;;;;;;;;;;;;;;;;;;;;;;;ICVxFC,EAAA,CAAAC,cAAA,mBAEgE;IADxCD,EAAA,CAAAE,UAAA,qBAAAC,sFAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAG,4BAAA,EAA8B;IAAA,EAAC;IACFT,EAAA,CAAAU,YAAA,EAAW;;;;IAAzEV,EAAA,CAAAW,UAAA,UAAAL,MAAA,CAAAM,sBAAA,CAAgC;;;;;;IAIlCZ,EAAA,CAAAC,cAAA,mBAEqC;IAAnCD,EAAA,CAAAE,UAAA,qBAAAW,sFAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAS,oBAAA,EAAsB;IAAA,EAAC;IAACf,EAAA,CAAAU,YAAA,EAAW;;;;IAD9CV,EADQ,CAAAW,UAAA,SAAAL,MAAA,CAAAU,UAAA,oCAAsD,aAAAV,MAAA,CAAAU,UAAA,sCACF;;;;;;IAQ5DhB,EAAA,CAAAC,cAAA,mBAE4D;IAD1DD,EAAA,CAAAE,UAAA,qBAAAe,uFAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAc,GAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAa,qBAAA,CAAsB,KAAK,CAAC;IAAA,EAAC;IACkBnB,EAAA,CAAAU,YAAA,EAAW;;;;IAArEV,EAAA,CAAAW,UAAA,eAAAL,MAAA,CAAAc,UAAA,kBAAAd,MAAA,CAAAc,UAAA,CAAAC,cAAA,KAAAf,MAAA,CAAAgB,QAAA,CAAyD;;;;;;IAE3DtB,EAAA,CAAAC,cAAA,mBAC4D;IADhCD,EAAA,CAAAE,UAAA,qBAAAqB,uFAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAa,qBAAA,CAAsB,KAAK,CAAC;IAAA,EAAC;IACRnB,EAAA,CAAAU,YAAA,EAAW;;;;IAArEV,EAAA,CAAAW,UAAA,eAAAL,MAAA,CAAAc,UAAA,kBAAAd,MAAA,CAAAc,UAAA,CAAAC,cAAA,KAAAf,MAAA,CAAAgB,QAAA,CAAyD;;;;;;IAO3DtB,EAAA,CAAAC,cAAA,mBAAmG;IAA/BD,EAAA,CAAAE,UAAA,qBAAAuB,uFAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAsB,GAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAqB,gBAAA,EAAkB;IAAA,EAAC;IAAC3B,EAAA,CAAAU,YAAA,EAAW;;;;;IAYxGV,EAFF,CAAAC,cAAA,cAC6D,eACpB;IACrCD,EAAA,CAAA4B,SAAA,YAC6E;IAC/E5B,EAAA,CAAAU,YAAA,EAAO;IACPV,EAAA,CAAAC,cAAA,eAC2D;IAAAD,EAAA,CAAA6B,MAAA,GAAgB;IAC7E7B,EAD6E,CAAAU,YAAA,EAAO,EAC9E;;;;;IAPJV,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAA8B,eAAA,IAAAC,GAAA,EAAAC,QAAA,CAAAC,KAAA,KAAA3B,MAAA,CAAA4B,YAAA,EAA0D;IAE7BlC,EAAA,CAAAmC,SAAA,GAAmB;IAAnBnC,EAAA,CAAAoC,UAAA,CAAAJ,QAAA,CAAAK,IAAA,CAAmB;IAC5CrC,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAA8B,eAAA,IAAAQ,GAAA,EAAAN,QAAA,CAAAK,IAAA,yBAAsE;IAGxErC,EAAA,CAAAmC,SAAA,EAAwD;IAAxDnC,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAA8B,eAAA,KAAAS,GAAA,EAAAP,QAAA,CAAAC,KAAA,KAAA3B,MAAA,CAAA4B,YAAA,EAAwD;IAAClC,EAAA,CAAAmC,SAAA,EAAgB;IAAhBnC,EAAA,CAAAwC,iBAAA,CAAAR,QAAA,CAAAS,KAAA,CAAgB;;;;;;IATjFzC,EAAA,CAAAC,cAAA,oBAAmF;IACjFD,EAAA,CAAA0C,UAAA,IAAAC,wEAAA,2BAAuC;IAWzC3C,EAAA,CAAAU,YAAA,EAAS;IACTV,EAAA,CAAAC,cAAA,mBAC6C;IADlBD,EAAA,CAAAE,UAAA,mBAAA0C,qFAAAC,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAA0C,GAAA;MAAA,MAAAC,iBAAA,GAAA/C,EAAA,CAAAgD,WAAA;MAAA,OAAAhD,EAAA,CAAAQ,WAAA,CAASuC,iBAAA,CAAAE,MAAA,CAAAJ,MAAA,CAA4B;IAAA,EAAC;IAE/D7C,EAAA,CAAAC,cAAA,eAA6C;IAC3CD,EAAA,CAAA4B,SAAA,QAA0D;IAC1D5B,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAA6B,MAAA,GAAgB;IAAA7B,EAAA,CAAAU,YAAA,EAAO;IAC1CV,EAAA,CAAA4B,SAAA,YAA2D;IAE/D5B,EADE,CAAAU,YAAA,EAAO,EACE;;;;IApB2BV,EAAf,CAAAW,UAAA,eAAc,UAAAL,MAAA,CAAA4C,aAAA,CAA6B;IAaxDlD,EAAA,CAAAmC,SAAA,GAAgB;IAAwCnC,EAAxD,CAAAW,UAAA,iBAAgB,aAAAL,MAAA,CAAAgB,QAAA,CAA6D;IAGhFtB,EAAA,CAAAmC,SAAA,GAAkD;IAAlDnC,EAAA,CAAAmD,sBAAA,KAAA7C,MAAA,CAAA8C,gBAAA,2BAAkD;IAClCpD,EAAA,CAAAmC,SAAA,GAAgB;IAAhBnC,EAAA,CAAAwC,iBAAA,CAAAlC,MAAA,CAAA4B,YAAA,CAAgB;;;;;;IAOvClC,EAAA,CAAAC,cAAA,mBACoD;IADtBD,EAAA,CAAAE,UAAA,qBAAAmD,uFAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAhD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAiD,kBAAA,EAAoB;IAAA,EAAC;IACVvD,EAAA,CAAAU,YAAA,EAAW;;;;IADAV,EAAA,CAAAW,UAAA,aAAAL,MAAA,CAAAgB,QAAA,CAAqB;;;;;;IAgBpFtB,EAAA,CAAAC,cAAA,yBAAiE;IAA1BD,EAAA,CAAAwD,gBAAA,2BAAAC,sGAAAZ,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAAsD,IAAA;MAAA,MAAApD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA2D,kBAAA,CAAArD,MAAA,CAAAsD,SAAA,EAAAf,MAAA,MAAAvC,MAAA,CAAAsD,SAAA,GAAAf,MAAA;MAAA,OAAA7C,EAAA,CAAAQ,WAAA,CAAAqC,MAAA;IAAA,EAAuB;IAA9D7C,EAAA,CAAAU,YAAA,EAAiE;;;;IAA1BV,EAAA,CAAA6D,gBAAA,YAAAvD,MAAA,CAAAsD,SAAA,CAAuB;;;;;;IAhFlE5D,EAFJ,CAAAC,cAAA,oBAA6D,cACxB,SAC7B;IAAAD,EAAA,CAAA4B,SAAA,WAAgC;IAAC5B,EAAA,CAAA6B,MAAA,GAA2B;IAClE7B,EADkE,CAAAU,YAAA,EAAK,EACjE;IAEJV,EADF,CAAAC,cAAA,cAAiC,eAC8B;IAO3DD,EANA,CAAA0C,UAAA,IAAAoB,yDAAA,uBAAmC,IAAAC,yDAAA,uBAMV;IAMzB/D,EAAA,CAAAC,cAAA,eACyC;IAMrCD,EAJF,CAAA0C,UAAA,KAAAsB,0DAAA,uBAAoD,KAAAC,0DAAA,OAI3C;IAKTjE,EAAA,CAAAC,cAAA,yBACkF;IADrCD,EAAA,CAAAE,UAAA,qBAAAgE,8EAAA;MAAAlE,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAA8D,WAAA,CAAuB,CAAC,mBAAA9D,MAAA,CAAA8D,WAAA,CAAD,CAAC,EAAAC,OAAA;IAAA,EAAa;IACArE,EAAA,CAAAU,YAAA,EAAgB;IAElGV,EAAA,CAAA0C,UAAA,KAAA4B,0DAAA,uBAA0B;IAI1BtE,EAAA,CAAAC,cAAA,mCAC0D;IAAxDD,EAAA,CAAAE,UAAA,kCAAAqE,qGAAA1B,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAwBF,MAAA,CAAAkE,sBAAA,CAAA3B,MAAA,CAA8B;IAAA,EAAC;IACzD7C,EAAA,CAAAU,YAAA,EAA0B;IA0B1BV,EAxBA,CAAA0C,UAAA,KAAA+B,0DAAA,OAA0D,KAAAC,0DAAA,uBAwBnC;IAKvB1E,EAAA,CAAAC,cAAA,oBAC6C;IADlBD,EAAA,CAAAE,UAAA,qBAAAyE,yEAAA9B,MAAA;MAAA7C,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAAS,iBAAA,GAAA5E,EAAA,CAAAgD,WAAA;MAAA,OAAAhD,EAAA,CAAAQ,WAAA,CAAWoE,iBAAA,CAAA3B,MAAA,CAAAJ,MAAA,CAA4B;IAAA,EAAC;IAEjE7C,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAA4B,SAAA,aAAuD;IAAA5B,EAAA,CAAAU,YAAA,EAAO;IACpEV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAA4B,SAAA,aAA2D;IACnE5B,EADmE,CAAAU,YAAA,EAAO,EAC/D;IACXV,EAAA,CAAA4B,SAAA,qBAA0F;IAE1F5B,EAAA,CAAAC,cAAA,oBACwB;IADOD,EAAA,CAAAE,UAAA,qBAAA2E,yEAAA;MAAA7E,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAwE,sBAAA,EAAwB;IAAA,EAAC;IAC3C9E,EAAA,CAAAU,YAAA,EAAW;IAEnCV,EAAA,CAAAC,cAAA,oBACwC;IADZD,EAAA,CAAAE,UAAA,qBAAA6E,yEAAA;MAAA/E,EAAA,CAAAI,aAAA,CAAA+D,GAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAA0E,kBAAA,EAAoB;IAAA,EAAC;IACpBhF,EAAA,CAAAU,YAAA,EAAW;IACnDV,EAAA,CAAA0C,UAAA,KAAAuC,6DAAA,6BAAiE;IAIzEjF,EAHM,CAAAU,YAAA,EAAO,EACF,EACH,EACI;;;;IAtFDV,EAAA,CAAAkF,UAAA,CAAAlF,EAAA,CAAAmF,eAAA,KAAAC,GAAA,EAAyB;IAEKpF,EAAA,CAAAmC,SAAA,GAA2B;IAA3BnC,EAAA,CAAAqF,kBAAA,MAAA/E,MAAA,CAAAc,UAAA,kBAAAd,MAAA,CAAAc,UAAA,CAAAkE,WAAA,KAA2B;IAI9DtF,EAAA,CAAAmC,SAAA,GAIC;IAJDnC,EAAA,CAAAuF,aAAA,IAAAjF,MAAA,CAAAM,sBAAA,UAIC;IAEDZ,EAAA,CAAAmC,SAAA,EAIC;IAJDnC,EAAA,CAAAuF,aAAA,KAAAjF,MAAA,CAAAkF,WAAA,UAIC;IAEKxF,EAAA,CAAAmC,SAAA,EAAiD;IAAjDnC,EAAA,CAAAyF,qBAAA,aAAAnF,MAAA,CAAAgB,QAAA,4BAAiD;IAGrDtB,EAAA,CAAAmC,SAAA,EAOC;IAPDnC,EAAA,CAAAuF,aAAA,OAAAjF,MAAA,CAAAc,UAAA,kBAAAd,MAAA,CAAAc,UAAA,CAAAC,cAAA,KAAAf,MAAA,CAAAgB,QAAA,WAOC;IAEctB,EAAA,CAAAmC,SAAA,GAA6B;IACgBnC,EAD7C,CAAAW,UAAA,SAAAL,MAAA,CAAA8D,WAAA,qBAAA9D,MAAA,CAAA8D,WAAA,IAAA/B,IAAA,CAA6B,cAAA/B,MAAA,CAAAoF,eAAA,IAAApF,MAAA,CAAAgB,QAAA,CACD,UAAAhB,MAAA,CAAA8D,WAAA,CAAsC;IAEjFpE,EAAA,CAAAmC,SAAA,EAEC;IAFDnC,EAAA,CAAAuF,aAAA,KAAAjF,MAAA,CAAAqF,aAAA,WAEC;IAEwB3F,EAAA,CAAAmC,SAAA,EAAyB;IAAyCnC,EAAlE,CAAAW,UAAA,eAAAL,MAAA,CAAAc,UAAA,CAAyB,WAAAd,MAAA,CAAAsF,MAAA,CAAkB,aAAAtF,MAAA,CAAAgB,QAAA,CAAsB,YAAAhB,MAAA,CAAAuF,OAAA,CAAoB;IAI9G7F,EAAA,CAAAmC,SAAA,EAsBC;IAtBDnC,EAAA,CAAAuF,aAAA,KAAAjF,MAAA,CAAAwF,gBAAA,KAAAxF,MAAA,CAAAc,UAAA,kBAAAd,MAAA,CAAAc,UAAA,CAAA2E,kBAAA,YAsBC;IAED/F,EAAA,CAAAmC,SAAA,EAGC;IAHDnC,EAAA,CAAAuF,aAAA,KAAAjF,MAAA,CAAA0F,eAAA,WAGC;IAEShG,EAAA,CAAAmC,SAAA,EAAgB;IAA0CnC,EAA1D,CAAAW,UAAA,iBAAgB,aAAAL,MAAA,CAAAgB,QAAA,CAA+D;IAKlEtB,EAAA,CAAAmC,SAAA,GAAc;IAACnC,EAAf,CAAAW,UAAA,eAAc,UAAAL,MAAA,CAAA2F,WAAA,CAA2B;IAEIjG,EAAA,CAAAmC,SAAA,GAAqB;IAArBnC,EAAA,CAAAW,UAAA,aAAAL,MAAA,CAAAgB,QAAA,CAAqB;IAG5BtB,EAAA,CAAAmC,SAAA,EAAyC;IAAzCnC,EAAA,CAAAW,UAAA,cAAAL,MAAA,CAAAoF,eAAA,IAAApF,MAAA,CAAAgB,QAAA,CAAyC;IAErFtB,EAAA,CAAAmC,SAAA,EAAoB;IAApBnC,EAAA,CAAAW,UAAA,SAAAL,MAAA,CAAA4F,cAAA,CAAoB;;;ADvD/C,OAAM,MAAOC,oBAAoB;EA+D/BC,YACUC,oBAA0C,EAC1CC,iBAAoC,EACpCC,mBAAwC;IAFxC,KAAAF,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAxDpB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAlF,QAAQ,GAAG,KAAK;IAChB,KAAAY,YAAY,GAAGpC,uBAAuB,CAAC2G,KAAK;IAC5C,KAAAX,gBAAgB,GAAG,KAAK;IACxB,KAAAH,aAAa,GAAG,KAAK;IACrB,KAAAO,cAAc,GAAG,KAAK;IACtB,KAAAtC,SAAS,GAAG,KAAK;IAGhB,KAAA8C,cAAc,GAAsB,IAAIxH,YAAY,EAAE;IACtD,KAAAyH,cAAc,GAAsB,IAAIzH,YAAY,EAAE;IACtD,KAAA0H,OAAO,GAAsB,IAAI1H,YAAY,EAAE;IAC/C,KAAA2H,WAAW,GAAsB,IAAI3H,YAAY,EAAE;IACnD,KAAA4H,eAAe,GAAsB,IAAI5H,YAAY,EAAE;IACvD,KAAA6H,cAAc,GAAsB,IAAI7H,YAAY,EAAE;IACtD,KAAA8H,WAAW,GAAsB,IAAI9H,YAAY,EAAE;IACnD,KAAA+H,qBAAqB,GAAsB,IAAI/H,YAAY,EAAE;IAC7D,KAAAgI,kBAAkB,GAAyB,IAAIhI,YAAY,EAAE;IAC7D,KAAAiI,aAAa,GAAyB,IAAIjI,YAAY,EAAE;IAIlE,KAAA+G,WAAW,GAAG,CACZ;MACEhE,KAAK,EAAE,IAAI;MACXmF,UAAU,EAAE;KACb,EACD;MACE3E,KAAK,EAAE,YAAY;MAAEJ,IAAI,EAAE,gBAAgB;MAAEgC,OAAO,EAAEA,CAAA,KAAK;QACzD,IAAI,CAACgD,gBAAgB,EAAE;MACzB;KACD,EACD;MACE5E,KAAK,EAAE,iBAAiB;MAAEJ,IAAI,EAAE,kBAAkB;MAAEgC,OAAO,EAAEA,CAAA,KAAK;QAChE,IAAI,CAACiD,kBAAkB,EAAE;MAC3B;KACD,EACD;MACE7E,KAAK,EAAE,eAAe;MAAEJ,IAAI,EAAE,YAAY;MAAEgC,OAAO,EAAEA,CAAA,KAAK;QACxD,IAAI,CAACkD,gBAAgB,EAAE;MACzB;KACD,CACF;IAED,KAAArE,aAAa,GAAG,EAAE;IAClB,KAAAkB,WAAW,GAAG,EAAE;IAEhB,KAAAhB,gBAAgB,GAAW,wBAAwB;IAI3C,KAAAoE,kBAAkB,GAAmB,EAAE;EAM3C;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,GAAG,IAAI,CAACrB,oBAAoB,CAACsB,YAAY,CAC1DC,SAAS,CAACC,KAAK,IAAG;MACjB,IAAI,CAACL,kBAAkB,GAAGK,KAAK,CAACC,IAAI;MACpC,IAAI,CAACC,aAAa,EAAE;IACtB,CAAC,CAAC;IACJ,IAAI,CAAC1B,oBAAoB,CAAC2B,uBAAuB,EAAE;IAEnD,IAAI,CAACC,aAAa,GAAG,CACnB;MACExF,KAAK,EAAE,UAAU;MACjBJ,IAAI,EAAE,YAAY;MAClBgC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACtD,oBAAoB,EAAE;MAC7B;KACD,EACD;MACE0B,KAAK,EAAE,KAAK;MACZJ,IAAI,EAAE,YAAY;MAClBgC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAAClD,qBAAqB,CAAC,KAAK,CAAC;MACnC;KACD,EACD;MACEsB,KAAK,EAAE,MAAM;MACbJ,IAAI,EAAE,iBAAiB;MACvBgC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAAC6D,qBAAqB,EAAE;MAC9B;KACD,EACD;MACEzF,KAAK,EAAE,UAAU;MACjBJ,IAAI,EAAE,aAAa;MACnBgC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAAC8D,cAAc,EAAE;MACvB;KACD,EACD;MACE1F,KAAK,EAAE,MAAM;MACbJ,IAAI,EAAE,YAAY;MAClBgC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACW,kBAAkB,EAAE;MAC3B;KACD,EACD;MACEvC,KAAK,EAAE,SAAS;MAChBJ,IAAI,EAAE,YAAY;MAClBgC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACS,sBAAsB,EAAE;MAC/B;KACD,EACD;MACErC,KAAK,EAAE,YAAY;MACnBJ,IAAI,EAAE,gBAAgB;MACtBgC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACgD,gBAAgB,EAAE;MACzB;KACD,EACD;MACE5E,KAAK,EAAE,iBAAiB;MACxBJ,IAAI,EAAE,kBAAkB;MACxBgC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACiD,kBAAkB,EAAE;MAC3B;KACD,EACD;MACE7E,KAAK,EAAE,eAAe;MACtBJ,IAAI,EAAE,YAAY;MAClBgC,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACkD,gBAAgB,EAAE;MACzB;KACD,CACF;IAED,IAAI,CAACrE,aAAa,GAAG,CACnB;MACEjB,KAAK,EAAE,IAAI;MACXmF,UAAU,EAAE;KACb,EACD;MACEnF,KAAK,EAAEnC,uBAAuB,CAACsI,KAAK;MAAE3F,KAAK,EAAE5C,4BAA4B,CAACuI,KAAK;MAAE/F,IAAI,EAAE,YAAY;MACnGgC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACgE,oBAAoB,CAACvI,uBAAuB,CAACsI,KAAK,EAAE,YAAY;KACrF,EACD;MACEnG,KAAK,EAAEnC,uBAAuB,CAACwI,IAAI;MAAE7F,KAAK,EAAE5C,4BAA4B,CAACyI,IAAI;MAAEjG,IAAI,EAAE,sBAAsB;MAC3GgC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACgE,oBAAoB,CAACvI,uBAAuB,CAACwI,IAAI,EAAE,sBAAsB;KAC9F,EACD;MACErG,KAAK,EAAEnC,uBAAuB,CAAC2G,KAAK;MAAEhE,KAAK,EAAE5C,4BAA4B,CAAC4G,KAAK;MAAEpE,IAAI,EAAE,wBAAwB;MAC/GgC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACgE,oBAAoB,CAACvI,uBAAuB,CAAC2G,KAAK,EAAE,wBAAwB;KACjG,EACD;MACExE,KAAK,EAAEnC,uBAAuB,CAACyI,IAAI;MAAE9F,KAAK,EAAE5C,4BAA4B,CAAC0I,IAAI;MAAElG,IAAI,EAAE,2BAA2B;MAChHgC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACgE,oBAAoB,CAACvI,uBAAuB,CAACyI,IAAI,EAAE,2BAA2B;KACnG,CACF;IAED,IAAI,CAACnE,WAAW,GAAG,CACjB;MACE3B,KAAK,EAAE,MAAM;MAAEJ,IAAI,EAAE,iBAAiB;MAAEgC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC6D,qBAAqB;KAClF,EACD;MACEzF,KAAK,EAAE,UAAU;MAAEJ,IAAI,EAAE,aAAa;MAAEgC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC8D,cAAc;KAC3E,CACF;EACH;EAEAK,WAAWA,CAAA;IACT,IAAI,IAAI,CAACd,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACe,WAAW,EAAE;IACpC;EACF;EAEAtH,qBAAqBA,CAACuH,GAAY;IAChC,IAAI,IAAI,CAACpH,QAAQ,EAAE;IAEnB,IAAI,CAACoF,cAAc,CAACiC,IAAI,CAACD,GAAG,CAAC;EAC/B;EAEAR,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAAC5G,QAAQ,EAAE;IAEnB,IAAI,CAACqF,cAAc,CAACgC,IAAI,EAAE;EAC5B;EAEAR,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC7G,QAAQ,EAAE;IAEnB,IAAI,CAACsF,OAAO,CAAC+B,IAAI,EAAE;EACrB;EAEA3D,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC1D,QAAQ,EAAE;IAEnB,IAAI,CAACuF,WAAW,CAAC8B,IAAI,EAAE;EACzB;EAEA7D,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAACxD,QAAQ,EAAE;IAEnB,IAAI,CAACwF,eAAe,CAAC6B,IAAI,EAAE;EAC7B;EAEApF,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACjC,QAAQ,EAAE;IAEnB,IAAI,CAAC0F,WAAW,CAAC2B,IAAI,EAAE;EACzB;EAEAlI,4BAA4BA,CAAA;IAC1B,IAAI,CAACwG,qBAAqB,CAAC0B,IAAI,EAAE;EACnC;EAEAhH,gBAAgBA,CAAA;IACd,IAAI,CAACwF,aAAa,CAACwB,IAAI,EAAE;EAC3B;EAEAZ,aAAaA,CAAA;IACX,IAAI,CAAC/G,UAAU,GAAG,IAAI,CAACwG,kBAAkB,CAACoB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAK,IAAI,CAAC1H,UAAU,EAAE0H,QAAQ,IAAID,CAAC,CAACE,KAAK,CAAC;IACxG;IACA,IAAI,CAACzC,iBAAiB,CAAC0C,aAAa,EAAE;EACxC;EAEAjI,oBAAoBA,CAAA;IAClB,IAAI4G,YAAY,GAAiB,IAAI,CAACH,kBAAkB,EAAEyB,IAAI,CAACC,QAAQ,IACrEA,QAAQ,CAACC,SAAS,CAACC,QAAQ,EAAE,KAAK,IAAI,CAAChI,UAAU,CAAC+H,SAAS,IAAID,QAAQ,CAACJ,QAAQ,KAAK,IAAI,CAAC1H,UAAU,CAAC0H,QAAQ,CAAC;IAEhH,IAAInB,YAAY,EAAE;MAChBA,YAAY,CAAC0B,MAAM,GAAG,KAAK;MAC3B1B,YAAY,CAACoB,KAAK,GAAGO,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK;IACnF,CAAC,MACI;MACH9B,YAAY,GAAG,IAAI,CAAC+B,kBAAkB,EAAE;IAC1C;IACA,IAAI,CAACnD,mBAAmB,CAACoD,kBAAkB,CAAChC,YAAY,CAAC;IACzD;IACA,IAAI,CAAC3G,UAAU,GAAG2G,YAAY,CAAC0B,MAAM;EACvC;EAEAK,kBAAkBA,CAAA;IAChB,MAAM/B,YAAY,GAAiB;MACjCwB,SAAS,EAAE,CAAC,IAAI,CAAC/H,UAAU,CAAC+H,SAAS;MACrCS,GAAG,EAAEN,MAAM,CAACC,QAAQ,CAACC,QAAQ;MAC7BV,QAAQ,EAAE,IAAI,CAAC1H,UAAU,CAAC0H,QAAQ;MAClCe,UAAU,EAAE,IAAI,CAACzI,UAAU,CAACyI,UAAU;MACtCC,gBAAgB,EAAE,CAAC,IAAI,CAAC1I,UAAU,CAAC0I,gBAAgB;MACnDT,MAAM,EAAE,IAAI;MACZN,KAAK,EAAEO,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG;KAC/D;IACD,OAAO9B,YAAY;EACrB;EAEAN,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC/F,QAAQ,EAAE;IAEnB,IAAI,CAACyF,cAAc,CAAC4B,IAAI,EAAE;EAC5B;EAEArB,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAChG,QAAQ,EAAE;IAEnB,IAAI,CAACsE,MAAM,CAACmE,GAAG,CAACC,iBAAiB,CAAC;MAChCC,mBAAmB,EAAGC,MAAM,IAAI;QAC9B,OAAO,IAAI,CAACC,qBAAqB,CAACD,MAAM,CAAC;MAC3C,CAAC;MACDE,UAAU,EAAE,IAAI,CAACC,eAAe;MAAEC,QAAQ,EAAE,IAAI,CAAClJ,UAAU,CAACyI,UAAU;MAAEU,SAAS,EAAE,IAAI,CAACnJ,UAAU,CAACyI;KACpG,CAAC;EACJ;EAEAtC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACjG,QAAQ,EAAE;IAEnB,IAAI,CAACsE,MAAM,CAACmE,GAAG,CAACS,eAAe,CAAC;MAC9BP,mBAAmB,EAAGC,MAAM,IAAI;QAC9B,OAAO,IAAI,CAACC,qBAAqB,CAACD,MAAM,CAAC;MAC3C,CAAC;MACDE,UAAU,EAAE,IAAI,CAACC,eAAe;MAAEC,QAAQ,EAAE,IAAI,CAAClJ,UAAU,CAACyI;KAC7D,CAAC;EACJ;EAEAM,qBAAqBA,CAACD,MAAkC;IACtD,MAAMO,kBAAkB,GAAGP,MAAM,CAACQ,MAAM,CAACC,SAAS,EAAE;IACpD,MAAMC,oBAAoB,GAAG,IAAI,CAACxJ,UAAU,CAACyJ,YAAY,EAAEC,2BAA2B,CAAC7B,IAAI,CAAC8B,CAAC,IAAIA,CAAC,CAACL,MAAM,KAAKD,kBAAkB,CAACO,KAAK,CAAC;IACvI,IAAIJ,oBAAoB,EAAEK,MAAM,CAACC,IAAI,KAAK9L,gBAAgB,CAAC+L,QAAQ,EAAE;MACnEV,kBAAkB,CAACW,cAAc,GAAGjM,cAAc,CAChD+K,MAAM,CAACjI,KAAK,EACZ2I,oBAAoB,CAACK,MAAM,CAACI,QAAQ,EACpCT,oBAAoB,CAACK,MAAM,CAACK,aAAa,CAAC;MAC5C,OAAOb,kBAAkB,CAACW,cAAc;IAC1C;IAEA,OAAOlB,MAAM,CAACjI,KAAK;EACrB;EAEAoG,oBAAoBA,CAACkD,IAA6B,EAAElJ,IAAY;IAC9D,IAAI,CAAC6E,kBAAkB,CAACyB,IAAI,CAAC4C,IAAI,CAAC;IAClC,IAAI,CAACnI,gBAAgB,GAAGf,IAAI;IAC5B,IAAI,CAACH,YAAY,GAAGqJ,IAAI;IAGxB;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACC,aAAa,KAAKC,SAAS,EAAE;QACpC,IAAI,CAAC9F,MAAM,CAACmE,GAAG,EAAE4B,kBAAkB,EAAE;MACvC;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEAnH,sBAAsBA,CAACoH,MAAkB;IACvC,IAAI,CAACH,aAAa,GAAGG,MAAM;EAC7B;;;uBA/TWzF,oBAAoB,EAAAnG,EAAA,CAAA6L,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAA/L,EAAA,CAAA6L,iBAAA,CAAA7L,EAAA,CAAAgM,iBAAA,GAAAhM,EAAA,CAAA6L,iBAAA,CAAAI,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAApB/F,oBAAoB;MAAAgG,SAAA;MAAAC,MAAA;QAAA5G,WAAA;QAAApE,UAAA;QAAAsE,eAAA;QAAAM,eAAA;QAAAqG,sBAAA;QAAAzG,MAAA;QAAAyE,eAAA;QAAAzJ,sBAAA;QAAA4F,WAAA;QAAAlF,QAAA;QAAAY,YAAA;QAAA4D,gBAAA;QAAAH,aAAA;QAAAO,cAAA;QAAAtC,SAAA;QAAAiC,OAAA;MAAA;MAAAyG,OAAA;QAAA5F,cAAA;QAAAC,cAAA;QAAAC,OAAA;QAAAC,WAAA;QAAAC,eAAA;QAAAC,cAAA;QAAAC,WAAA;QAAAC,qBAAA;QAAAC,kBAAA;QAAAC,aAAA;MAAA;MAAAoF,UAAA;MAAAC,QAAA,GAAAxM,EAAA,CAAAyM,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC7BjC/M,EAAA,CAAAC,cAAA,aAA0B;UACxBD,EAAA,CAAA0C,UAAA,IAAAuK,2CAAA,yBAAqE;UAyFvEjN,EAAA,CAAAU,YAAA,EAAM;UAEJV,EADF,CAAAC,cAAA,aAA6B,cACA;UAAAD,EAAA,CAAA4B,SAAA,WAAgC;UAAC5B,EAAA,CAAA6B,MAAA,GAA2B;UAAA7B,EAAA,CAAAU,YAAA,EAAO;UAC9FV,EAAA,CAAA4B,SAAA,mBAA8E;UAC9E5B,EAAA,CAAAC,cAAA,kBAAuG;UAAxDD,EAAA,CAAAE,UAAA,qBAAAgN,0DAAArK,MAAA;YAAA7C,EAAA,CAAAI,aAAA,CAAA+M,GAAA;YAAA,MAAAC,QAAA,GAAApN,EAAA,CAAAgD,WAAA;YAAA,OAAAhD,EAAA,CAAAQ,WAAA,CAAW4M,QAAA,CAAAnK,MAAA,CAAAJ,MAAA,CAAmB;UAAA,EAAC;UAChF7C,EADyG,CAAAU,YAAA,EAAW,EAC9G;;;UA9FJV,EAAA,CAAAmC,SAAA,EAwFC;UAxFDnC,EAAA,CAAAuF,aAAA,KAAAyH,GAAA,CAAA9J,aAAA,kBAAA8J,GAAA,CAAA9J,aAAA,CAAAmK,MAAA,UAAAL,GAAA,CAAA5I,WAAA,kBAAA4I,GAAA,CAAA5I,WAAA,CAAAiJ,MAAA,eAwFC;UAG2DrN,EAAA,CAAAmC,SAAA,GAA2B;UAA3BnC,EAAA,CAAAqF,kBAAA,MAAA2H,GAAA,CAAA5L,UAAA,kBAAA4L,GAAA,CAAA5L,UAAA,CAAAkE,WAAA,KAA2B;UACzEtF,EAAA,CAAAmC,SAAA,EAAuB;UAACnC,EAAxB,CAAAW,UAAA,UAAAqM,GAAA,CAAA/E,aAAA,CAAuB,eAAe;;;qBDlE1CtI,aAAa,EAAA2N,EAAA,CAAAC,OAAA,EAAE7N,aAAa,EAAA8N,EAAA,CAAAC,OAAA,EAAElO,WAAW,EAAAmO,EAAA,CAAAC,aAAA,EAAEnO,OAAO,EAAEF,YAAY,EAAAsO,EAAA,CAAAC,MAAA,EAAExO,UAAU,EAAAyO,EAAA,CAAAC,IAAA,EAAEnO,iBAAiB,EAAAoO,EAAA,CAAAC,WAAA,EAAExO,IAAI,EAAEM,0BAA0B;MAAAmO,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}