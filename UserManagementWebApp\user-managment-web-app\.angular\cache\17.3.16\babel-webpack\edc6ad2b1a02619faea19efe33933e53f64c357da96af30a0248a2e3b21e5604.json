{"ast": null, "code": "/** Array of auth fields that we supply defaults with */\nconst signUpFieldsWithDefault = ['birthdate', 'email', 'family_name', 'given_name', 'middle_name', 'name', 'nickname', 'phone_number', 'preferred_username', 'profile', 'website'];\n/** Array of auth fields that we do not supply defaults with */\nconst signUpFieldsWithoutDefault = ['address', 'gender', 'locale', 'picture', 'updated_at', 'zoneinfo'];\n/** Array of known login mechanisms */\nconst LoginMechanismArray = ['username', 'email', 'phone_number'];\nconst authFieldsWithDefaults = [...LoginMechanismArray, ...signUpFieldsWithDefault, 'confirmation_code', 'password', 'confirm_password'];\nconst isAuthFieldsWithDefaults = field => {\n  return authFieldsWithDefaults.includes(field);\n};\nexport { LoginMechanismArray, authFieldsWithDefaults, isAuthFieldsWithDefaults, signUpFieldsWithDefault, signUpFieldsWithoutDefault };", "map": {"version": 3, "names": ["signUpFieldsWithDefault", "signUpFieldsWithoutDefault", "LoginMechanismArray", "authFieldsWithDefaults", "isAuthFieldsWithDefaults", "field", "includes"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/types/authenticator/attributes.mjs"], "sourcesContent": ["/** Array of auth fields that we supply defaults with */\nconst signUpFieldsWithDefault = [\n    'birthdate',\n    'email',\n    'family_name',\n    'given_name',\n    'middle_name',\n    'name',\n    'nickname',\n    'phone_number',\n    'preferred_username',\n    'profile',\n    'website',\n];\n/** Array of auth fields that we do not supply defaults with */\nconst signUpFieldsWithoutDefault = [\n    'address',\n    'gender',\n    'locale',\n    'picture',\n    'updated_at',\n    'zoneinfo',\n];\n/** Array of known login mechanisms */\nconst LoginMechanismArray = [\n    'username',\n    'email',\n    'phone_number',\n];\nconst authFieldsWithDefaults = [\n    ...LoginMechanismArray,\n    ...signUpFieldsWithDefault,\n    'confirmation_code',\n    'password',\n    'confirm_password',\n];\nconst isAuthFieldsWithDefaults = (field) => {\n    return authFieldsWithDefaults.includes(field);\n};\n\nexport { LoginMechanismArray, authFieldsWithDefaults, isAuthFieldsWithDefaults, signUpFieldsWithDefault, signUpFieldsWithoutDefault };\n"], "mappings": "AAAA;AACA,MAAMA,uBAAuB,GAAG,CAC5B,WAAW,EACX,OAAO,EACP,aAAa,EACb,YAAY,EACZ,aAAa,EACb,MAAM,EACN,UAAU,EACV,cAAc,EACd,oBAAoB,EACpB,SAAS,EACT,SAAS,CACZ;AACD;AACA,MAAMC,0BAA0B,GAAG,CAC/B,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,UAAU,CACb;AACD;AACA,MAAMC,mBAAmB,GAAG,CACxB,UAAU,EACV,OAAO,EACP,cAAc,CACjB;AACD,MAAMC,sBAAsB,GAAG,CAC3B,GAAGD,mBAAmB,EACtB,GAAGF,uBAAuB,EAC1B,mBAAmB,EACnB,UAAU,EACV,kBAAkB,CACrB;AACD,MAAMI,wBAAwB,GAAIC,KAAK,IAAK;EACxC,OAAOF,sBAAsB,CAACG,QAAQ,CAACD,KAAK,CAAC;AACjD,CAAC;AAED,SAASH,mBAAmB,EAAEC,sBAAsB,EAAEC,wBAAwB,EAAEJ,uBAAuB,EAAEC,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}