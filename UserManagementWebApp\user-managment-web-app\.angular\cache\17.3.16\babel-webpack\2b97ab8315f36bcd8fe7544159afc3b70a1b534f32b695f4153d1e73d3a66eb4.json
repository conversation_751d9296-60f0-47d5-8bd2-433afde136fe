{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport Js<PERSON>ookie from 'js-cookie';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass CookieStorage {\n  constructor(data = {}) {\n    const {\n      path,\n      domain,\n      expires,\n      sameSite,\n      secure\n    } = data;\n    this.domain = domain;\n    this.path = path || '/';\n    this.expires = Object.prototype.hasOwnProperty.call(data, 'expires') ? expires : 365;\n    this.secure = Object.prototype.hasOwnProperty.call(data, 'secure') ? secure : true;\n    if (Object.prototype.hasOwnProperty.call(data, 'sameSite')) {\n      if (!sameSite || !['strict', 'lax', 'none'].includes(sameSite)) {\n        throw new Error('The sameSite value of cookieStorage must be \"lax\", \"strict\" or \"none\".');\n      }\n      if (sameSite === 'none' && !this.secure) {\n        throw new Error('sameSite = None requires the Secure attribute in latest browser versions.');\n      }\n      this.sameSite = sameSite;\n    }\n  }\n  setItem(key, value) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      JsCookie.set(key, value, _this.getData());\n    })();\n  }\n  getItem(key) {\n    return _asyncToGenerator(function* () {\n      const item = JsCookie.get(key);\n      return item ?? null;\n    })();\n  }\n  removeItem(key) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      JsCookie.remove(key, _this2.getData());\n    })();\n  }\n  clear() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const cookie = JsCookie.get();\n      const promises = Object.keys(cookie).map(key => _this3.removeItem(key));\n      yield Promise.all(promises);\n    })();\n  }\n  getData() {\n    return {\n      path: this.path,\n      expires: this.expires,\n      domain: this.domain,\n      secure: this.secure,\n      ...(this.sameSite && {\n        sameSite: this.sameSite\n      })\n    };\n  }\n}\nexport { CookieStorage };", "map": {"version": 3, "names": ["JsCookie", "Cookie<PERSON>torage", "constructor", "data", "path", "domain", "expires", "sameSite", "secure", "Object", "prototype", "hasOwnProperty", "call", "includes", "Error", "setItem", "key", "value", "_this", "_asyncToGenerator", "set", "getData", "getItem", "item", "get", "removeItem", "_this2", "remove", "clear", "_this3", "cookie", "promises", "keys", "map", "Promise", "all"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/storage/CookieStorage.mjs"], "sourcesContent": ["import JsCookie from 'js-cookie';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass CookieStorage {\n    constructor(data = {}) {\n        const { path, domain, expires, sameSite, secure } = data;\n        this.domain = domain;\n        this.path = path || '/';\n        this.expires = Object.prototype.hasOwnProperty.call(data, 'expires')\n            ? expires\n            : 365;\n        this.secure = Object.prototype.hasOwnProperty.call(data, 'secure')\n            ? secure\n            : true;\n        if (Object.prototype.hasOwnProperty.call(data, 'sameSite')) {\n            if (!sameSite || !['strict', 'lax', 'none'].includes(sameSite)) {\n                throw new Error('The sameSite value of cookieStorage must be \"lax\", \"strict\" or \"none\".');\n            }\n            if (sameSite === 'none' && !this.secure) {\n                throw new Error('sameSite = None requires the Secure attribute in latest browser versions.');\n            }\n            this.sameSite = sameSite;\n        }\n    }\n    async setItem(key, value) {\n        JsCookie.set(key, value, this.getData());\n    }\n    async getItem(key) {\n        const item = JsCookie.get(key);\n        return item ?? null;\n    }\n    async removeItem(key) {\n        JsCookie.remove(key, this.getData());\n    }\n    async clear() {\n        const cookie = JsCookie.get();\n        const promises = Object.keys(cookie).map(key => this.removeItem(key));\n        await Promise.all(promises);\n    }\n    getData() {\n        return {\n            path: this.path,\n            expires: this.expires,\n            domain: this.domain,\n            secure: this.secure,\n            ...(this.sameSite && { sameSite: this.sameSite }),\n        };\n    }\n}\n\nexport { CookieStorage };\n"], "mappings": ";AAAA,OAAOA,QAAQ,MAAM,WAAW;;AAEhC;AACA;AACA,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAACC,IAAI,GAAG,CAAC,CAAC,EAAE;IACnB,MAAM;MAAEC,IAAI;MAAEC,MAAM;MAAEC,OAAO;MAAEC,QAAQ;MAAEC;IAAO,CAAC,GAAGL,IAAI;IACxD,IAAI,CAACE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACD,IAAI,GAAGA,IAAI,IAAI,GAAG;IACvB,IAAI,CAACE,OAAO,GAAGG,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACT,IAAI,EAAE,SAAS,CAAC,GAC9DG,OAAO,GACP,GAAG;IACT,IAAI,CAACE,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACT,IAAI,EAAE,QAAQ,CAAC,GAC5DK,MAAM,GACN,IAAI;IACV,IAAIC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACT,IAAI,EAAE,UAAU,CAAC,EAAE;MACxD,IAAI,CAACI,QAAQ,IAAI,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,CAACM,QAAQ,CAACN,QAAQ,CAAC,EAAE;QAC5D,MAAM,IAAIO,KAAK,CAAC,wEAAwE,CAAC;MAC7F;MACA,IAAIP,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;QACrC,MAAM,IAAIM,KAAK,CAAC,2EAA2E,CAAC;MAChG;MACA,IAAI,CAACP,QAAQ,GAAGA,QAAQ;IAC5B;EACJ;EACMQ,OAAOA,CAACC,GAAG,EAAEC,KAAK,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtBnB,QAAQ,CAACoB,GAAG,CAACJ,GAAG,EAAEC,KAAK,EAAEC,KAAI,CAACG,OAAO,CAAC,CAAC,CAAC;IAAC;EAC7C;EACMC,OAAOA,CAACN,GAAG,EAAE;IAAA,OAAAG,iBAAA;MACf,MAAMI,IAAI,GAAGvB,QAAQ,CAACwB,GAAG,CAACR,GAAG,CAAC;MAC9B,OAAOO,IAAI,IAAI,IAAI;IAAC;EACxB;EACME,UAAUA,CAACT,GAAG,EAAE;IAAA,IAAAU,MAAA;IAAA,OAAAP,iBAAA;MAClBnB,QAAQ,CAAC2B,MAAM,CAACX,GAAG,EAAEU,MAAI,CAACL,OAAO,CAAC,CAAC,CAAC;IAAC;EACzC;EACMO,KAAKA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAV,iBAAA;MACV,MAAMW,MAAM,GAAG9B,QAAQ,CAACwB,GAAG,CAAC,CAAC;MAC7B,MAAMO,QAAQ,GAAGtB,MAAM,CAACuB,IAAI,CAACF,MAAM,CAAC,CAACG,GAAG,CAACjB,GAAG,IAAIa,MAAI,CAACJ,UAAU,CAACT,GAAG,CAAC,CAAC;MACrE,MAAMkB,OAAO,CAACC,GAAG,CAACJ,QAAQ,CAAC;IAAC;EAChC;EACAV,OAAOA,CAAA,EAAG;IACN,OAAO;MACHjB,IAAI,EAAE,IAAI,CAACA,IAAI;MACfE,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBD,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBG,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB,IAAI,IAAI,CAACD,QAAQ,IAAI;QAAEA,QAAQ,EAAE,IAAI,CAACA;MAAS,CAAC;IACpD,CAAC;EACL;AACJ;AAEA,SAASN,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}