{"ast": null, "code": "const field = {\n  // default styles\n  gap: {\n    value: '{space.xs.value}'\n  },\n  fontSize: {\n    value: '{fontSizes.medium.value}'\n  },\n  flexDirection: {\n    value: 'column'\n  },\n  // Adjust base fontSize and gap for small and large sizes\n  small: {\n    gap: {\n      value: '{space.xxxs.value}'\n    },\n    fontSize: {\n      value: '{fontSizes.small.value}'\n    }\n  },\n  large: {\n    gap: {\n      value: '{space.small.value}'\n    },\n    fontSize: {\n      value: '{fontSizes.large.value}'\n    }\n  },\n  label: {\n    color: {\n      value: '{colors.font.secondary.value}'\n    }\n  }\n};\nexport { field };", "map": {"version": 3, "names": ["field", "gap", "value", "fontSize", "flexDirection", "small", "large", "label", "color"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/field.mjs"], "sourcesContent": ["const field = {\n    // default styles\n    gap: { value: '{space.xs.value}' },\n    fontSize: { value: '{fontSizes.medium.value}' },\n    flexDirection: { value: 'column' },\n    // Adjust base fontSize and gap for small and large sizes\n    small: {\n        gap: { value: '{space.xxxs.value}' },\n        fontSize: { value: '{fontSizes.small.value}' },\n    },\n    large: {\n        gap: { value: '{space.small.value}' },\n        fontSize: { value: '{fontSizes.large.value}' },\n    },\n    label: {\n        color: { value: '{colors.font.secondary.value}' },\n    },\n};\n\nexport { field };\n"], "mappings": "AAAA,MAAMA,KAAK,GAAG;EACV;EACAC,GAAG,EAAE;IAAEC,KAAK,EAAE;EAAmB,CAAC;EAClCC,QAAQ,EAAE;IAAED,KAAK,EAAE;EAA2B,CAAC;EAC/CE,aAAa,EAAE;IAAEF,KAAK,EAAE;EAAS,CAAC;EAClC;EACAG,KAAK,EAAE;IACHJ,GAAG,EAAE;MAAEC,KAAK,EAAE;IAAqB,CAAC;IACpCC,QAAQ,EAAE;MAAED,KAAK,EAAE;IAA0B;EACjD,CAAC;EACDI,KAAK,EAAE;IACHL,GAAG,EAAE;MAAEC,KAAK,EAAE;IAAsB,CAAC;IACrCC,QAAQ,EAAE;MAAED,KAAK,EAAE;IAA0B;EACjD,CAAC;EACDK,KAAK,EAAE;IACHC,KAAK,EAAE;MAAEN,KAAK,EAAE;IAAgC;EACpD;AACJ,CAAC;AAED,SAASF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}