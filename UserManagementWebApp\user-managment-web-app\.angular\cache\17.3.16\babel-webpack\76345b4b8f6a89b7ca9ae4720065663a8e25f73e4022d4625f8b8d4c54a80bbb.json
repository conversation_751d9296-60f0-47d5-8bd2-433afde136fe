{"ast": null, "code": "'use strict';\n\nvar callBound = require('call-bound');\nvar hasToStringTag = require('has-tostringtag/shams')();\nvar hasOwn = require('hasown');\nvar gOPD = require('gopd');\n\n/** @type {import('.')} */\nvar fn;\nif (hasToStringTag) {\n  /** @type {(receiver: ThisParameterType<typeof RegExp.prototype.exec>, ...args: Parameters<typeof RegExp.prototype.exec>) => ReturnType<typeof RegExp.prototype.exec>} */\n  var $exec = callBound('RegExp.prototype.exec');\n  /** @type {object} */\n  var isRegexMarker = {};\n  var throwRegexMarker = function () {\n    throw isRegexMarker;\n  };\n  /** @type {{ toString(): never, valueOf(): never, [Symbol.toPrimitive]?(): never }} */\n  var badStringifier = {\n    toString: throwRegexMarker,\n    valueOf: throwRegexMarker\n  };\n  if (typeof Symbol.toPrimitive === 'symbol') {\n    badStringifier[Symbol.toPrimitive] = throwRegexMarker;\n  }\n\n  /** @type {import('.')} */\n  // @ts-expect-error TS can't figure out that the $exec call always throws\n  // eslint-disable-next-line consistent-return\n  fn = function isRegex(value) {\n    if (!value || typeof value !== 'object') {\n      return false;\n    }\n\n    // eslint-disable-next-line no-extra-parens\n    var descriptor = /** @type {NonNullable<typeof gOPD>} */gOPD(/** @type {{ lastIndex?: unknown }} */value, 'lastIndex');\n    var hasLastIndexDataProperty = descriptor && hasOwn(descriptor, 'value');\n    if (!hasLastIndexDataProperty) {\n      return false;\n    }\n    try {\n      // eslint-disable-next-line no-extra-parens\n      $exec(value, /** @type {string} */ /** @type {unknown} */badStringifier);\n    } catch (e) {\n      return e === isRegexMarker;\n    }\n  };\n} else {\n  /** @type {(receiver: ThisParameterType<typeof Object.prototype.toString>, ...args: Parameters<typeof Object.prototype.toString>) => ReturnType<typeof Object.prototype.toString>} */\n  var $toString = callBound('Object.prototype.toString');\n  /** @const @type {'[object RegExp]'} */\n  var regexClass = '[object RegExp]';\n\n  /** @type {import('.')} */\n  fn = function isRegex(value) {\n    // In older browsers, typeof regex incorrectly returns 'function'\n    if (!value || typeof value !== 'object' && typeof value !== 'function') {\n      return false;\n    }\n    return $toString(value) === regexClass;\n  };\n}\nmodule.exports = fn;", "map": {"version": 3, "names": ["callBound", "require", "hasToStringTag", "hasOwn", "gOPD", "fn", "$exec", "isRegexMarker", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "badStringifier", "toString", "valueOf", "Symbol", "toPrimitive", "isRegex", "value", "descriptor", "hasLastIndexDataProperty", "e", "$toString", "regexClass", "module", "exports"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/is-regex/index.js"], "sourcesContent": ["'use strict';\n\nvar callBound = require('call-bound');\nvar hasToStringTag = require('has-tostringtag/shams')();\nvar hasOwn = require('hasown');\nvar gOPD = require('gopd');\n\n/** @type {import('.')} */\nvar fn;\n\nif (hasToStringTag) {\n\t/** @type {(receiver: ThisParameterType<typeof RegExp.prototype.exec>, ...args: Parameters<typeof RegExp.prototype.exec>) => ReturnType<typeof RegExp.prototype.exec>} */\n\tvar $exec = callBound('RegExp.prototype.exec');\n\t/** @type {object} */\n\tvar isRegexMarker = {};\n\n\tvar throwRegexMarker = function () {\n\t\tthrow isRegexMarker;\n\t};\n\t/** @type {{ toString(): never, valueOf(): never, [Symbol.toPrimitive]?(): never }} */\n\tvar badStringifier = {\n\t\ttoString: throwRegexMarker,\n\t\tvalueOf: throwRegexMarker\n\t};\n\n\tif (typeof Symbol.toPrimitive === 'symbol') {\n\t\tbadStringifier[Symbol.toPrimitive] = throwRegexMarker;\n\t}\n\n\t/** @type {import('.')} */\n\t// @ts-expect-error TS can't figure out that the $exec call always throws\n\t// eslint-disable-next-line consistent-return\n\tfn = function isRegex(value) {\n\t\tif (!value || typeof value !== 'object') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// eslint-disable-next-line no-extra-parens\n\t\tvar descriptor = /** @type {NonNullable<typeof gOPD>} */ (gOPD)(/** @type {{ lastIndex?: unknown }} */ (value), 'lastIndex');\n\t\tvar hasLastIndexDataProperty = descriptor && hasOwn(descriptor, 'value');\n\t\tif (!hasLastIndexDataProperty) {\n\t\t\treturn false;\n\t\t}\n\n\t\ttry {\n\t\t\t// eslint-disable-next-line no-extra-parens\n\t\t\t$exec(value, /** @type {string} */ (/** @type {unknown} */ (badStringifier)));\n\t\t} catch (e) {\n\t\t\treturn e === isRegexMarker;\n\t\t}\n\t};\n} else {\n\t/** @type {(receiver: ThisParameterType<typeof Object.prototype.toString>, ...args: Parameters<typeof Object.prototype.toString>) => ReturnType<typeof Object.prototype.toString>} */\n\tvar $toString = callBound('Object.prototype.toString');\n\t/** @const @type {'[object RegExp]'} */\n\tvar regexClass = '[object RegExp]';\n\n\t/** @type {import('.')} */\n\tfn = function isRegex(value) {\n\t\t// In older browsers, typeof regex incorrectly returns 'function'\n\t\tif (!value || (typeof value !== 'object' && typeof value !== 'function')) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn $toString(value) === regexClass;\n\t};\n}\n\nmodule.exports = fn;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,SAAS,GAAGC,OAAO,CAAC,YAAY,CAAC;AACrC,IAAIC,cAAc,GAAGD,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC;AACvD,IAAIE,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;AAC9B,IAAIG,IAAI,GAAGH,OAAO,CAAC,MAAM,CAAC;;AAE1B;AACA,IAAII,EAAE;AAEN,IAAIH,cAAc,EAAE;EACnB;EACA,IAAII,KAAK,GAAGN,SAAS,CAAC,uBAAuB,CAAC;EAC9C;EACA,IAAIO,aAAa,GAAG,CAAC,CAAC;EAEtB,IAAIC,gBAAgB,GAAG,SAAAA,CAAA,EAAY;IAClC,MAAMD,aAAa;EACpB,CAAC;EACD;EACA,IAAIE,cAAc,GAAG;IACpBC,QAAQ,EAAEF,gBAAgB;IAC1BG,OAAO,EAAEH;EACV,CAAC;EAED,IAAI,OAAOI,MAAM,CAACC,WAAW,KAAK,QAAQ,EAAE;IAC3CJ,cAAc,CAACG,MAAM,CAACC,WAAW,CAAC,GAAGL,gBAAgB;EACtD;;EAEA;EACA;EACA;EACAH,EAAE,GAAG,SAASS,OAAOA,CAACC,KAAK,EAAE;IAC5B,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACxC,OAAO,KAAK;IACb;;IAEA;IACA,IAAIC,UAAU,GAAG,uCAAyCZ,IAAI,CAAE,sCAAwCW,KAAK,EAAG,WAAW,CAAC;IAC5H,IAAIE,wBAAwB,GAAGD,UAAU,IAAIb,MAAM,CAACa,UAAU,EAAE,OAAO,CAAC;IACxE,IAAI,CAACC,wBAAwB,EAAE;MAC9B,OAAO,KAAK;IACb;IAEA,IAAI;MACH;MACAX,KAAK,CAACS,KAAK,EAAE,sBAAuB,sBAAwBN,cAAgB,CAAC;IAC9E,CAAC,CAAC,OAAOS,CAAC,EAAE;MACX,OAAOA,CAAC,KAAKX,aAAa;IAC3B;EACD,CAAC;AACF,CAAC,MAAM;EACN;EACA,IAAIY,SAAS,GAAGnB,SAAS,CAAC,2BAA2B,CAAC;EACtD;EACA,IAAIoB,UAAU,GAAG,iBAAiB;;EAElC;EACAf,EAAE,GAAG,SAASS,OAAOA,CAACC,KAAK,EAAE;IAC5B;IACA,IAAI,CAACA,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,UAAW,EAAE;MACzE,OAAO,KAAK;IACb;IAEA,OAAOI,SAAS,CAACJ,KAAK,CAAC,KAAKK,UAAU;EACvC,CAAC;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGjB,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}