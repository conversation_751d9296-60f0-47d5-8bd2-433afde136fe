{"ast": null, "code": "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n  if (native.randomUUID && !buf && !options) {\n    return native.randomUUID();\n  }\n  options = options || {};\n  const rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n    return buf;\n  }\n  return unsafeStringify(rnds);\n}\nexport default v4;", "map": {"version": 3, "names": ["native", "rng", "unsafeStringify", "v4", "options", "buf", "offset", "randomUUID", "rnds", "random", "i"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/uuid/dist/esm-browser/v4.js"], "sourcesContent": ["import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\n\nfunction v4(options, buf, offset) {\n  if (native.randomUUID && !buf && !options) {\n    return native.randomUUID();\n  }\n\n  options = options || {};\n  const rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return unsafeStringify(rnds);\n}\n\nexport default v4;"], "mappings": "AAAA,OAAOA,MAAM,MAAM,aAAa;AAChC,OAAOC,GAAG,MAAM,UAAU;AAC1B,SAASC,eAAe,QAAQ,gBAAgB;AAEhD,SAASC,EAAEA,CAACC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAChC,IAAIN,MAAM,CAACO,UAAU,IAAI,CAACF,GAAG,IAAI,CAACD,OAAO,EAAE;IACzC,OAAOJ,MAAM,CAACO,UAAU,CAAC,CAAC;EAC5B;EAEAH,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,MAAMI,IAAI,GAAGJ,OAAO,CAACK,MAAM,IAAI,CAACL,OAAO,CAACH,GAAG,IAAIA,GAAG,EAAE,CAAC,CAAC,CAAC;;EAEvDO,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI;EAC/BA,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;;EAEjC,IAAIH,GAAG,EAAE;IACPC,MAAM,GAAGA,MAAM,IAAI,CAAC;IAEpB,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;MAC3BL,GAAG,CAACC,MAAM,GAAGI,CAAC,CAAC,GAAGF,IAAI,CAACE,CAAC,CAAC;IAC3B;IAEA,OAAOL,GAAG;EACZ;EAEA,OAAOH,eAAe,CAACM,IAAI,CAAC;AAC9B;AAEA,eAAeL,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}