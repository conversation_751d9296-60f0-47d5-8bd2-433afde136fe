{"ast": null, "code": "import '../../../client/utils/store/autoSignInStore.mjs';\nimport { signInStore } from '../../../client/utils/store/signInStore.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction setActiveSignInUsername(username) {\n  const {\n    dispatch\n  } = signInStore;\n  dispatch({\n    type: 'SET_USERNAME',\n    value: username\n  });\n}\nexport { setActiveSignInUsername };", "map": {"version": 3, "names": ["signInStore", "setActiveSignInUsername", "username", "dispatch", "type", "value"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/setActiveSignInUsername.mjs"], "sourcesContent": ["import '../../../client/utils/store/autoSignInStore.mjs';\nimport { signInStore } from '../../../client/utils/store/signInStore.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction setActiveSignInUsername(username) {\n    const { dispatch } = signInStore;\n    dispatch({ type: 'SET_USERNAME', value: username });\n}\n\nexport { setActiveSignInUsername };\n"], "mappings": "AAAA,OAAO,iDAAiD;AACxD,SAASA,WAAW,QAAQ,6CAA6C;;AAEzE;AACA;AACA,SAASC,uBAAuBA,CAACC,QAAQ,EAAE;EACvC,MAAM;IAAEC;EAAS,CAAC,GAAGH,WAAW;EAChCG,QAAQ,CAAC;IAAEC,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAEH;EAAS,CAAC,CAAC;AACvD;AAEA,SAASD,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}