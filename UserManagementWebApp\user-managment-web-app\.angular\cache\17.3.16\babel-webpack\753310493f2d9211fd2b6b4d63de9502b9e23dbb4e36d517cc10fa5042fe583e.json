{"ast": null, "code": "import { MAX_DELAY_MS } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @private\n * Internal use of Amplify only\n */\nfunction jitteredBackoff(maxDelayMs = MAX_DELAY_MS) {\n  const BASE_TIME_MS = 100;\n  const JITTER_FACTOR = 100;\n  return attempt => {\n    const delay = 2 ** attempt * BASE_TIME_MS + JITTER_FACTOR * Math.random();\n    return delay > maxDelayMs ? false : delay;\n  };\n}\nexport { jitteredBackoff };", "map": {"version": 3, "names": ["MAX_DELAY_MS", "jittered<PERSON><PERSON>off", "max<PERSON>elay<PERSON>", "BASE_TIME_MS", "JITTER_FACTOR", "attempt", "delay", "Math", "random"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/retry/jitteredBackoff.mjs"], "sourcesContent": ["import { MAX_DELAY_MS } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @private\n * Internal use of Amplify only\n */\nfunction jitteredBackoff(maxDelayMs = MAX_DELAY_MS) {\n    const BASE_TIME_MS = 100;\n    const JITTER_FACTOR = 100;\n    return attempt => {\n        const delay = 2 ** attempt * BASE_TIME_MS + JITTER_FACTOR * Math.random();\n        return delay > maxDelayMs ? false : delay;\n    };\n}\n\nexport { jitteredBackoff };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,UAAU,GAAGF,YAAY,EAAE;EAChD,MAAMG,YAAY,GAAG,GAAG;EACxB,MAAMC,aAAa,GAAG,GAAG;EACzB,OAAOC,OAAO,IAAI;IACd,MAAMC,KAAK,GAAG,CAAC,IAAID,OAAO,GAAGF,YAAY,GAAGC,aAAa,GAAGG,IAAI,CAACC,MAAM,CAAC,CAAC;IACzE,OAAOF,KAAK,GAAGJ,UAAU,GAAG,KAAK,GAAGI,KAAK;EAC7C,CAAC;AACL;AAEA,SAASL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}