{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Standard session start & stop event names\nconst SESSION_START_EVENT = '_session.start';\nconst SESSION_STOP_EVENT = '_session.stop';\nexport { SESSION_START_EVENT, SESSION_STOP_EVENT };", "map": {"version": 3, "names": ["SESSION_START_EVENT", "SESSION_STOP_EVENT"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/sessionListener/constants.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Standard session start & stop event names\nconst SESSION_START_EVENT = '_session.start';\nconst SESSION_STOP_EVENT = '_session.stop';\n\nexport { SESSION_START_EVENT, SESSION_STOP_EVENT };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,mBAAmB,GAAG,gBAAgB;AAC5C,MAAMC,kBAAkB,GAAG,eAAe;AAE1C,SAASD,mBAAmB,EAAEC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}