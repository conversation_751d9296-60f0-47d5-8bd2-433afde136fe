{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction, HubInternal } from '@aws-amplify/core/internals/utils';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { getUserContextData } from '../utils/userContextData.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../types/errors.mjs';\nimport { createConfirmSignUpClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createConfirmSignUpClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { autoSignInStore } from '../../../client/utils/store/autoSignInStore.mjs';\nimport '../../../client/utils/store/signInStore.mjs';\nimport { resetAutoSignIn } from './autoSignIn.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Confirms a new user account.\n *\n * @param input -  The ConfirmSignUpInput object.\n * @returns ConfirmSignUpOutput\n * @throws -{@link ConfirmSignUpException }\n * Thrown due to an invalid confirmation code.\n * @throws -{@link AuthValidationErrorCode }\n * Thrown due to an empty confirmation code\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nfunction confirmSignUp(_x) {\n  return _confirmSignUp.apply(this, arguments);\n}\nfunction _confirmSignUp() {\n  _confirmSignUp = _asyncToGenerator(function* (input) {\n    const {\n      username,\n      confirmationCode,\n      options\n    } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolId,\n      userPoolClientId,\n      userPoolEndpoint\n    } = authConfig;\n    const clientMetadata = options?.clientMetadata;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptyConfirmSignUpUsername);\n    assertValidationError(!!confirmationCode, AuthValidationErrorCode.EmptyConfirmSignUpCode);\n    const UserContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const confirmSignUpClient = createConfirmSignUpClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const {\n      Session: session\n    } = yield confirmSignUpClient({\n      region: getRegionFromUserPoolId(authConfig.userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignUp)\n    }, {\n      Username: username,\n      ConfirmationCode: confirmationCode,\n      ClientMetadata: clientMetadata,\n      ForceAliasCreation: options?.forceAliasCreation,\n      ClientId: authConfig.userPoolClientId,\n      UserContextData\n    });\n    return new Promise((resolve, reject) => {\n      try {\n        const signUpOut = {\n          isSignUpComplete: true,\n          nextStep: {\n            signUpStep: 'DONE'\n          }\n        };\n        const autoSignInStoreState = autoSignInStore.getState();\n        if (!autoSignInStoreState.active || autoSignInStoreState.username !== username) {\n          resolve(signUpOut);\n          resetAutoSignIn();\n          return;\n        }\n        autoSignInStore.dispatch({\n          type: 'SET_SESSION',\n          value: session\n        });\n        const stopListener = HubInternal.listen('auth-internal', ({\n          payload\n        }) => {\n          switch (payload.event) {\n            case 'autoSignIn':\n              resolve({\n                isSignUpComplete: true,\n                nextStep: {\n                  signUpStep: 'COMPLETE_AUTO_SIGN_IN'\n                }\n              });\n              stopListener();\n          }\n        });\n        HubInternal.dispatch('auth-internal', {\n          event: 'confirmSignUp',\n          data: signUpOut\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  });\n  return _confirmSignUp.apply(this, arguments);\n}\nexport { confirmSignUp };", "map": {"version": 3, "names": ["Amplify", "assertTokenProviderConfig", "AuthAction", "HubInternal", "assertValidationError", "AuthValidationErrorCode", "getRegionFromUserPoolId", "getAuthUserAgentValue", "getUserContextData", "createConfirmSignUpClient", "createCognitoUserPoolEndpointResolver", "autoSignInStore", "resetAutoSignIn", "confirmSignUp", "_x", "_confirmSignUp", "apply", "arguments", "_asyncToGenerator", "input", "username", "confirmationCode", "options", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userPoolId", "userPoolClientId", "userPoolEndpoint", "clientMetadata", "EmptyConfirmSignUpUsername", "EmptyConfirmSignUpCode", "UserContextData", "confirmSignUpClient", "endpointResolver", "endpointOverride", "Session", "session", "region", "userAgentValue", "ConfirmSignUp", "Username", "ConfirmationCode", "ClientMetadata", "ForceAliasCreation", "forceAliasCreation", "ClientId", "Promise", "resolve", "reject", "signUpOut", "isSignUpComplete", "nextStep", "signUpStep", "autoSignInStoreState", "getState", "active", "dispatch", "type", "value", "stopListener", "listen", "payload", "event", "data", "error"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/confirmSignUp.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction, HubInternal } from '@aws-amplify/core/internals/utils';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { getUserContextData } from '../utils/userContextData.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../types/errors.mjs';\nimport { createConfirmSignUpClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createConfirmSignUpClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { autoSignInStore } from '../../../client/utils/store/autoSignInStore.mjs';\nimport '../../../client/utils/store/signInStore.mjs';\nimport { resetAutoSignIn } from './autoSignIn.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Confirms a new user account.\n *\n * @param input -  The ConfirmSignUpInput object.\n * @returns ConfirmSignUpOutput\n * @throws -{@link ConfirmSignUpException }\n * Thrown due to an invalid confirmation code.\n * @throws -{@link AuthValidationErrorCode }\n * Thrown due to an empty confirmation code\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nasync function confirmSignUp(input) {\n    const { username, confirmationCode, options } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolId, userPoolClientId, userPoolEndpoint } = authConfig;\n    const clientMetadata = options?.clientMetadata;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptyConfirmSignUpUsername);\n    assertValidationError(!!confirmationCode, AuthValidationErrorCode.EmptyConfirmSignUpCode);\n    const UserContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const confirmSignUpClient = createConfirmSignUpClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const { Session: session } = await confirmSignUpClient({\n        region: getRegionFromUserPoolId(authConfig.userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignUp),\n    }, {\n        Username: username,\n        ConfirmationCode: confirmationCode,\n        ClientMetadata: clientMetadata,\n        ForceAliasCreation: options?.forceAliasCreation,\n        ClientId: authConfig.userPoolClientId,\n        UserContextData,\n    });\n    return new Promise((resolve, reject) => {\n        try {\n            const signUpOut = {\n                isSignUpComplete: true,\n                nextStep: {\n                    signUpStep: 'DONE',\n                },\n            };\n            const autoSignInStoreState = autoSignInStore.getState();\n            if (!autoSignInStoreState.active ||\n                autoSignInStoreState.username !== username) {\n                resolve(signUpOut);\n                resetAutoSignIn();\n                return;\n            }\n            autoSignInStore.dispatch({ type: 'SET_SESSION', value: session });\n            const stopListener = HubInternal.listen('auth-internal', ({ payload }) => {\n                switch (payload.event) {\n                    case 'autoSignIn':\n                        resolve({\n                            isSignUpComplete: true,\n                            nextStep: {\n                                signUpStep: 'COMPLETE_AUTO_SIGN_IN',\n                            },\n                        });\n                        stopListener();\n                }\n            });\n            HubInternal.dispatch('auth-internal', {\n                event: 'confirmSignUp',\n                data: signUpOut,\n            });\n        }\n        catch (error) {\n            reject(error);\n        }\n    });\n}\n\nexport { confirmSignUp };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,yBAAyB,EAAEC,UAAU,EAAEC,WAAW,QAAQ,mCAAmC;AACtG,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,qBAAqB;AAC5B,SAASC,yBAAyB,QAAQ,oGAAoG;AAC9I,SAASC,qCAAqC,QAAQ,wDAAwD;AAC9G,SAASC,eAAe,QAAQ,iDAAiD;AACjF,OAAO,6CAA6C;AACpD,SAASC,eAAe,QAAQ,kBAAkB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,SAWeC,aAAaA,CAAAC,EAAA;EAAA,OAAAC,cAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,eAAA;EAAAA,cAAA,GAAAG,iBAAA,CAA5B,WAA6BC,KAAK,EAAE;IAChC,MAAM;MAAEC,QAAQ;MAAEC,gBAAgB;MAAEC;IAAQ,CAAC,GAAGH,KAAK;IACrD,MAAMI,UAAU,GAAGvB,OAAO,CAACwB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDzB,yBAAyB,CAACsB,UAAU,CAAC;IACrC,MAAM;MAAEI,UAAU;MAAEC,gBAAgB;MAAEC;IAAiB,CAAC,GAAGN,UAAU;IACrE,MAAMO,cAAc,GAAGR,OAAO,EAAEQ,cAAc;IAC9C1B,qBAAqB,CAAC,CAAC,CAACgB,QAAQ,EAAEf,uBAAuB,CAAC0B,0BAA0B,CAAC;IACrF3B,qBAAqB,CAAC,CAAC,CAACiB,gBAAgB,EAAEhB,uBAAuB,CAAC2B,sBAAsB,CAAC;IACzF,MAAMC,eAAe,GAAGzB,kBAAkB,CAAC;MACvCY,QAAQ;MACRO,UAAU;MACVC;IACJ,CAAC,CAAC;IACF,MAAMM,mBAAmB,GAAGzB,yBAAyB,CAAC;MAClD0B,gBAAgB,EAAEzB,qCAAqC,CAAC;QACpD0B,gBAAgB,EAAEP;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAM;MAAEQ,OAAO,EAAEC;IAAQ,CAAC,SAASJ,mBAAmB,CAAC;MACnDK,MAAM,EAAEjC,uBAAuB,CAACiB,UAAU,CAACI,UAAU,CAAC;MACtDa,cAAc,EAAEjC,qBAAqB,CAACL,UAAU,CAACuC,aAAa;IAClE,CAAC,EAAE;MACCC,QAAQ,EAAEtB,QAAQ;MAClBuB,gBAAgB,EAAEtB,gBAAgB;MAClCuB,cAAc,EAAEd,cAAc;MAC9Be,kBAAkB,EAAEvB,OAAO,EAAEwB,kBAAkB;MAC/CC,QAAQ,EAAExB,UAAU,CAACK,gBAAgB;MACrCK;IACJ,CAAC,CAAC;IACF,OAAO,IAAIe,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC,IAAI;QACA,MAAMC,SAAS,GAAG;UACdC,gBAAgB,EAAE,IAAI;UACtBC,QAAQ,EAAE;YACNC,UAAU,EAAE;UAChB;QACJ,CAAC;QACD,MAAMC,oBAAoB,GAAG5C,eAAe,CAAC6C,QAAQ,CAAC,CAAC;QACvD,IAAI,CAACD,oBAAoB,CAACE,MAAM,IAC5BF,oBAAoB,CAACnC,QAAQ,KAAKA,QAAQ,EAAE;UAC5C6B,OAAO,CAACE,SAAS,CAAC;UAClBvC,eAAe,CAAC,CAAC;UACjB;QACJ;QACAD,eAAe,CAAC+C,QAAQ,CAAC;UAAEC,IAAI,EAAE,aAAa;UAAEC,KAAK,EAAEtB;QAAQ,CAAC,CAAC;QACjE,MAAMuB,YAAY,GAAG1D,WAAW,CAAC2D,MAAM,CAAC,eAAe,EAAE,CAAC;UAAEC;QAAQ,CAAC,KAAK;UACtE,QAAQA,OAAO,CAACC,KAAK;YACjB,KAAK,YAAY;cACbf,OAAO,CAAC;gBACJG,gBAAgB,EAAE,IAAI;gBACtBC,QAAQ,EAAE;kBACNC,UAAU,EAAE;gBAChB;cACJ,CAAC,CAAC;cACFO,YAAY,CAAC,CAAC;UACtB;QACJ,CAAC,CAAC;QACF1D,WAAW,CAACuD,QAAQ,CAAC,eAAe,EAAE;UAClCM,KAAK,EAAE,eAAe;UACtBC,IAAI,EAAEd;QACV,CAAC,CAAC;MACN,CAAC,CACD,OAAOe,KAAK,EAAE;QACVhB,MAAM,CAACgB,KAAK,CAAC;MACjB;IACJ,CAAC,CAAC;EACN,CAAC;EAAA,OAAAnD,cAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}