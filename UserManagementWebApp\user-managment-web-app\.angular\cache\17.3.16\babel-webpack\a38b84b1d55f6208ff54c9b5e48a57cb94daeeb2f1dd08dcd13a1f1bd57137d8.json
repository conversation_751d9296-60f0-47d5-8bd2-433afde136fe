{"ast": null, "code": "'use strict';\n\nvar isCallable = require('is-callable');\nvar toStr = Object.prototype.toString;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\n/** @type {<This, A extends readonly unknown[]>(arr: A, iterator: (this: This | void, value: A[number], index: number, arr: A) => void, receiver: This | undefined) => void} */\nvar forEachArray = function forEachArray(array, iterator, receiver) {\n  for (var i = 0, len = array.length; i < len; i++) {\n    if (hasOwnProperty.call(array, i)) {\n      if (receiver == null) {\n        iterator(array[i], i, array);\n      } else {\n        iterator.call(receiver, array[i], i, array);\n      }\n    }\n  }\n};\n\n/** @type {<This, S extends string>(string: S, iterator: (this: This | void, value: S[number], index: number, string: S) => void, receiver: This | undefined) => void} */\nvar forEachString = function forEachString(string, iterator, receiver) {\n  for (var i = 0, len = string.length; i < len; i++) {\n    // no such thing as a sparse string.\n    if (receiver == null) {\n      iterator(string.charAt(i), i, string);\n    } else {\n      iterator.call(receiver, string.charAt(i), i, string);\n    }\n  }\n};\n\n/** @type {<This, O>(obj: O, iterator: (this: This | void, value: O[keyof O], index: keyof O, obj: O) => void, receiver: This | undefined) => void} */\nvar forEachObject = function forEachObject(object, iterator, receiver) {\n  for (var k in object) {\n    if (hasOwnProperty.call(object, k)) {\n      if (receiver == null) {\n        iterator(object[k], k, object);\n      } else {\n        iterator.call(receiver, object[k], k, object);\n      }\n    }\n  }\n};\n\n/** @type {(x: unknown) => x is readonly unknown[]} */\nfunction isArray(x) {\n  return toStr.call(x) === '[object Array]';\n}\n\n/** @type {import('.')._internal} */\nmodule.exports = function forEach(list, iterator, thisArg) {\n  if (!isCallable(iterator)) {\n    throw new TypeError('iterator must be a function');\n  }\n  var receiver;\n  if (arguments.length >= 3) {\n    receiver = thisArg;\n  }\n  if (isArray(list)) {\n    forEachArray(list, iterator, receiver);\n  } else if (typeof list === 'string') {\n    forEachString(list, iterator, receiver);\n  } else {\n    forEachObject(list, iterator, receiver);\n  }\n};", "map": {"version": 3, "names": ["isCallable", "require", "toStr", "Object", "prototype", "toString", "hasOwnProperty", "forEachArray", "array", "iterator", "receiver", "i", "len", "length", "call", "forEachString", "string", "char<PERSON>t", "forEachObject", "object", "k", "isArray", "x", "module", "exports", "for<PERSON>ach", "list", "thisArg", "TypeError", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/for-each/index.js"], "sourcesContent": ["'use strict';\n\nvar isCallable = require('is-callable');\n\nvar toStr = Object.prototype.toString;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\n/** @type {<This, A extends readonly unknown[]>(arr: A, iterator: (this: This | void, value: A[number], index: number, arr: A) => void, receiver: This | undefined) => void} */\nvar forEachArray = function forEachArray(array, iterator, receiver) {\n    for (var i = 0, len = array.length; i < len; i++) {\n        if (hasOwnProperty.call(array, i)) {\n            if (receiver == null) {\n                iterator(array[i], i, array);\n            } else {\n                iterator.call(receiver, array[i], i, array);\n            }\n        }\n    }\n};\n\n/** @type {<This, S extends string>(string: S, iterator: (this: This | void, value: S[number], index: number, string: S) => void, receiver: This | undefined) => void} */\nvar forEachString = function forEachString(string, iterator, receiver) {\n    for (var i = 0, len = string.length; i < len; i++) {\n        // no such thing as a sparse string.\n        if (receiver == null) {\n            iterator(string.charAt(i), i, string);\n        } else {\n            iterator.call(receiver, string.charAt(i), i, string);\n        }\n    }\n};\n\n/** @type {<This, O>(obj: O, iterator: (this: This | void, value: O[keyof O], index: keyof O, obj: O) => void, receiver: This | undefined) => void} */\nvar forEachObject = function forEachObject(object, iterator, receiver) {\n    for (var k in object) {\n        if (hasOwnProperty.call(object, k)) {\n            if (receiver == null) {\n                iterator(object[k], k, object);\n            } else {\n                iterator.call(receiver, object[k], k, object);\n            }\n        }\n    }\n};\n\n/** @type {(x: unknown) => x is readonly unknown[]} */\nfunction isArray(x) {\n    return toStr.call(x) === '[object Array]';\n}\n\n/** @type {import('.')._internal} */\nmodule.exports = function forEach(list, iterator, thisArg) {\n    if (!isCallable(iterator)) {\n        throw new TypeError('iterator must be a function');\n    }\n\n    var receiver;\n    if (arguments.length >= 3) {\n        receiver = thisArg;\n    }\n\n    if (isArray(list)) {\n        forEachArray(list, iterator, receiver);\n    } else if (typeof list === 'string') {\n        forEachString(list, iterator, receiver);\n    } else {\n        forEachObject(list, iterator, receiver);\n    }\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,aAAa,CAAC;AAEvC,IAAIC,KAAK,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ;AACrC,IAAIC,cAAc,GAAGH,MAAM,CAACC,SAAS,CAACE,cAAc;;AAEpD;AACA,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EAChE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGJ,KAAK,CAACK,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC9C,IAAIL,cAAc,CAACQ,IAAI,CAACN,KAAK,EAAEG,CAAC,CAAC,EAAE;MAC/B,IAAID,QAAQ,IAAI,IAAI,EAAE;QAClBD,QAAQ,CAACD,KAAK,CAACG,CAAC,CAAC,EAAEA,CAAC,EAAEH,KAAK,CAAC;MAChC,CAAC,MAAM;QACHC,QAAQ,CAACK,IAAI,CAACJ,QAAQ,EAAEF,KAAK,CAACG,CAAC,CAAC,EAAEA,CAAC,EAAEH,KAAK,CAAC;MAC/C;IACJ;EACJ;AACJ,CAAC;;AAED;AACA,IAAIO,aAAa,GAAG,SAASA,aAAaA,CAACC,MAAM,EAAEP,QAAQ,EAAEC,QAAQ,EAAE;EACnE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGI,MAAM,CAACH,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC/C;IACA,IAAID,QAAQ,IAAI,IAAI,EAAE;MAClBD,QAAQ,CAACO,MAAM,CAACC,MAAM,CAACN,CAAC,CAAC,EAAEA,CAAC,EAAEK,MAAM,CAAC;IACzC,CAAC,MAAM;MACHP,QAAQ,CAACK,IAAI,CAACJ,QAAQ,EAAEM,MAAM,CAACC,MAAM,CAACN,CAAC,CAAC,EAAEA,CAAC,EAAEK,MAAM,CAAC;IACxD;EACJ;AACJ,CAAC;;AAED;AACA,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,MAAM,EAAEV,QAAQ,EAAEC,QAAQ,EAAE;EACnE,KAAK,IAAIU,CAAC,IAAID,MAAM,EAAE;IAClB,IAAIb,cAAc,CAACQ,IAAI,CAACK,MAAM,EAAEC,CAAC,CAAC,EAAE;MAChC,IAAIV,QAAQ,IAAI,IAAI,EAAE;QAClBD,QAAQ,CAACU,MAAM,CAACC,CAAC,CAAC,EAAEA,CAAC,EAAED,MAAM,CAAC;MAClC,CAAC,MAAM;QACHV,QAAQ,CAACK,IAAI,CAACJ,QAAQ,EAAES,MAAM,CAACC,CAAC,CAAC,EAAEA,CAAC,EAAED,MAAM,CAAC;MACjD;IACJ;EACJ;AACJ,CAAC;;AAED;AACA,SAASE,OAAOA,CAACC,CAAC,EAAE;EAChB,OAAOpB,KAAK,CAACY,IAAI,CAACQ,CAAC,CAAC,KAAK,gBAAgB;AAC7C;;AAEA;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,OAAOA,CAACC,IAAI,EAAEjB,QAAQ,EAAEkB,OAAO,EAAE;EACvD,IAAI,CAAC3B,UAAU,CAACS,QAAQ,CAAC,EAAE;IACvB,MAAM,IAAImB,SAAS,CAAC,6BAA6B,CAAC;EACtD;EAEA,IAAIlB,QAAQ;EACZ,IAAImB,SAAS,CAAChB,MAAM,IAAI,CAAC,EAAE;IACvBH,QAAQ,GAAGiB,OAAO;EACtB;EAEA,IAAIN,OAAO,CAACK,IAAI,CAAC,EAAE;IACfnB,YAAY,CAACmB,IAAI,EAAEjB,QAAQ,EAAEC,QAAQ,CAAC;EAC1C,CAAC,MAAM,IAAI,OAAOgB,IAAI,KAAK,QAAQ,EAAE;IACjCX,aAAa,CAACW,IAAI,EAAEjB,QAAQ,EAAEC,QAAQ,CAAC;EAC3C,CAAC,MAAM;IACHQ,aAAa,CAACQ,IAAI,EAAEjB,QAAQ,EAAEC,QAAQ,CAAC;EAC3C;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}