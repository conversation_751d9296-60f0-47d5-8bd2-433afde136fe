{"ast": null, "code": "import { base64Encoder } from '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// https://datatracker.ietf.org/doc/html/rfc4648#page-7\n/**\n * Converts an ArrayBuffer to a base64url encoded string\n * @param buffer - the ArrayBuffer instance of a Uint8Array\n * @returns string - a base64url encoded string\n */\nconst convertArrayBufferToBase64Url = buffer => {\n  return base64Encoder.convert(new Uint8Array(buffer), {\n    urlSafe: true,\n    skipPadding: true\n  });\n};\nexport { convertArrayBufferToBase64Url };", "map": {"version": 3, "names": ["base64Encoder", "convertArrayBufferToBase64Url", "buffer", "convert", "Uint8Array", "urlSafe", "skipPadding"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/convert/base64url/convertArrayBufferToBase64Url.mjs"], "sourcesContent": ["import { base64Encoder } from '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// https://datatracker.ietf.org/doc/html/rfc4648#page-7\n/**\n * Converts an ArrayBuffer to a base64url encoded string\n * @param buffer - the ArrayBuffer instance of a Uint8Array\n * @returns string - a base64url encoded string\n */\nconst convertArrayBufferToBase64Url = (buffer) => {\n    return base64Encoder.convert(new Uint8Array(buffer), {\n        urlSafe: true,\n        skipPadding: true,\n    });\n};\n\nexport { convertArrayBufferToBase64Url };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,mCAAmC;;AAEjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,6BAA6B,GAAIC,MAAM,IAAK;EAC9C,OAAOF,aAAa,CAACG,OAAO,CAAC,IAAIC,UAAU,CAACF,MAAM,CAAC,EAAE;IACjDG,OAAO,EAAE,IAAI;IACbC,WAAW,EAAE;EACjB,CAAC,CAAC;AACN,CAAC;AAED,SAASL,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}