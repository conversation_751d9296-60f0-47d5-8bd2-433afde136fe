{"ast": null, "code": "const icon = {\n  lineHeight: {\n    value: 1\n  },\n  height: {\n    value: '1em'\n  } // Should match height of parent container font-size\n};\nexport { icon };", "map": {"version": 3, "names": ["icon", "lineHeight", "value", "height"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/icon.mjs"], "sourcesContent": ["const icon = {\n    lineHeight: { value: 1 },\n    height: { value: '1em' }, // Should match height of parent container font-size\n};\n\nexport { icon };\n"], "mappings": "AAAA,MAAMA,IAAI,GAAG;EACTC,UAAU,EAAE;IAAEC,KAAK,EAAE;EAAE,CAAC;EACxBC,MAAM,EAAE;IAAED,KAAK,EAAE;EAAM,CAAC,CAAE;AAC9B,CAAC;AAED,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}