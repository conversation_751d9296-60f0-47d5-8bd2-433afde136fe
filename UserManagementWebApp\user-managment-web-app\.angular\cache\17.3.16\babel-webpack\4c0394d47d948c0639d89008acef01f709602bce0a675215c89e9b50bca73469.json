{"ast": null, "code": "import { PlatformNotSupportedError } from '../errors/PlatformNotSupportedError.mjs';\nimport '../errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nclass SyncKeyValueStorage {\n  constructor(storage) {\n    this._storage = storage;\n  }\n  get storage() {\n    if (!this._storage) throw new PlatformNotSupportedError();\n    return this._storage;\n  }\n  /**\n   * This is used to set a specific item in storage\n   * @param {string} key - the key for the item\n   * @param {object} value - the value\n   * @returns {string} value that was set\n   */\n  setItem(key, value) {\n    this.storage.setItem(key, value);\n  }\n  /**\n   * This is used to get a specific key from storage\n   * @param {string} key - the key for the item\n   * This is used to clear the storage\n   * @returns {string} the data item\n   */\n  getItem(key) {\n    return this.storage.getItem(key);\n  }\n  /**\n   * This is used to remove an item from storage\n   * @param {string} key - the key being set\n   * @returns {string} value - value that was deleted\n   */\n  removeItem(key) {\n    this.storage.removeItem(key);\n  }\n  /**\n   * This is used to clear the storage\n   * @returns {string} nothing\n   */\n  clear() {\n    this.storage.clear();\n  }\n}\nexport { SyncKeyValueStorage };", "map": {"version": 3, "names": ["PlatformNotSupportedError", "SyncKeyValueStorage", "constructor", "storage", "_storage", "setItem", "key", "value", "getItem", "removeItem", "clear"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/storage/SyncKeyValueStorage.mjs"], "sourcesContent": ["import { PlatformNotSupportedError } from '../errors/PlatformNotSupportedError.mjs';\nimport '../errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nclass SyncKeyValueStorage {\n    constructor(storage) {\n        this._storage = storage;\n    }\n    get storage() {\n        if (!this._storage)\n            throw new PlatformNotSupportedError();\n        return this._storage;\n    }\n    /**\n     * This is used to set a specific item in storage\n     * @param {string} key - the key for the item\n     * @param {object} value - the value\n     * @returns {string} value that was set\n     */\n    setItem(key, value) {\n        this.storage.setItem(key, value);\n    }\n    /**\n     * This is used to get a specific key from storage\n     * @param {string} key - the key for the item\n     * This is used to clear the storage\n     * @returns {string} the data item\n     */\n    getItem(key) {\n        return this.storage.getItem(key);\n    }\n    /**\n     * This is used to remove an item from storage\n     * @param {string} key - the key being set\n     * @returns {string} value - value that was deleted\n     */\n    removeItem(key) {\n        this.storage.removeItem(key);\n    }\n    /**\n     * This is used to clear the storage\n     * @returns {string} nothing\n     */\n    clear() {\n        this.storage.clear();\n    }\n}\n\nexport { SyncKeyValueStorage };\n"], "mappings": "AAAA,SAASA,yBAAyB,QAAQ,yCAAyC;AACnF,OAAO,4BAA4B;;AAEnC;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;EACtBC,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAACC,QAAQ,GAAGD,OAAO;EAC3B;EACA,IAAIA,OAAOA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACC,QAAQ,EACd,MAAM,IAAIJ,yBAAyB,CAAC,CAAC;IACzC,OAAO,IAAI,CAACI,QAAQ;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACC,GAAG,EAAEC,KAAK,EAAE;IAChB,IAAI,CAACJ,OAAO,CAACE,OAAO,CAACC,GAAG,EAAEC,KAAK,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACF,GAAG,EAAE;IACT,OAAO,IAAI,CAACH,OAAO,CAACK,OAAO,CAACF,GAAG,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;EACIG,UAAUA,CAACH,GAAG,EAAE;IACZ,IAAI,CAACH,OAAO,CAACM,UAAU,CAACH,GAAG,CAAC;EAChC;EACA;AACJ;AACA;AACA;EACII,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACP,OAAO,CAACO,KAAK,CAAC,CAAC;EACxB;AACJ;AAEA,SAAST,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}