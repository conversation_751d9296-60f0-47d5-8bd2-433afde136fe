{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst PROVIDER_NAME = 'pinpoint';\n/**\n * Returns a unique cache key for a particular category/appId combination.\n *\n * @internal\n */\nconst getCacheKey = (appId, category) => `${category}:${PROVIDER_NAME}:${appId}`;\nexport { getCacheKey };", "map": {"version": 3, "names": ["PROVIDER_NAME", "get<PERSON><PERSON><PERSON><PERSON>", "appId", "category"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/providers/pinpoint/utils/getCacheKey.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst PROVIDER_NAME = 'pinpoint';\n/**\n * Returns a unique cache key for a particular category/appId combination.\n *\n * @internal\n */\nconst getCacheKey = (appId, category) => `${category}:${PROVIDER_NAME}:${appId}`;\n\nexport { getCacheKey };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,aAAa,GAAG,UAAU;AAChC;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK,GAAGA,QAAQ,IAAIH,aAAa,IAAIE,KAAK,EAAE;AAEhF,SAASD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}