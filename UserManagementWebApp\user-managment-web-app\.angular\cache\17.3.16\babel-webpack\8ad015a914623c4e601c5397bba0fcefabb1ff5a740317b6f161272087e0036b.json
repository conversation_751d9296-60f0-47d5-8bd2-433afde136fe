{"ast": null, "code": "import BigInteger from '../BigInteger/BigInteger.mjs';\nimport { getHashFromHex } from '../getHashFromHex.mjs';\nimport { getPaddedHex } from '../getPaddedHex.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nconst calculateU = ({\n  A,\n  B\n}) => {\n  const U = new BigInteger(getHashFromHex(getPaddedHex(A) + getPaddedHex(B)), 16);\n  if (U.equals(BigInteger.ZERO)) {\n    throw new Error('U cannot be zero.');\n  }\n  return U;\n};\nexport { calculateU };", "map": {"version": 3, "names": ["BigInteger", "getHashFromHex", "getPaddedHex", "calculateU", "A", "B", "U", "equals", "ZERO", "Error"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/srp/calculate/calculateU.mjs"], "sourcesContent": ["import BigInteger from '../BigInteger/BigInteger.mjs';\nimport { getHashFromHex } from '../getHashFromHex.mjs';\nimport { getPaddedHex } from '../getPaddedHex.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nconst calculateU = ({ A, B, }) => {\n    const U = new BigInteger(getHashFromHex(getPaddedHex(A) + getPaddedHex(B)), 16);\n    if (U.equals(BigInteger.ZERO)) {\n        throw new Error('U cannot be zero.');\n    }\n    return U;\n};\n\nexport { calculateU };\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,8BAA8B;AACrD,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,YAAY,QAAQ,qBAAqB;;AAElD;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAGA,CAAC;EAAEC,CAAC;EAAEC;AAAG,CAAC,KAAK;EAC9B,MAAMC,CAAC,GAAG,IAAIN,UAAU,CAACC,cAAc,CAACC,YAAY,CAACE,CAAC,CAAC,GAAGF,YAAY,CAACG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EAC/E,IAAIC,CAAC,CAACC,MAAM,CAACP,UAAU,CAACQ,IAAI,CAAC,EAAE;IAC3B,MAAM,IAAIC,KAAK,CAAC,mBAAmB,CAAC;EACxC;EACA,OAAOH,CAAC;AACZ,CAAC;AAED,SAASH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}