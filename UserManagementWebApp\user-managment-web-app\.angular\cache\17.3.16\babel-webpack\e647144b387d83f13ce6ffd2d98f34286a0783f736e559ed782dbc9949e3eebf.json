{"ast": null, "code": "const accordion = {\n  backgroundColor: {\n    value: '{colors.background.primary.value}'\n  },\n  item: {\n    borderColor: {\n      value: '{colors.border.secondary.value}'\n    },\n    borderWidth: {\n      value: '{borderWidths.small.value}'\n    },\n    borderStyle: {\n      value: 'solid'\n    },\n    borderRadius: {\n      value: '{radii.small.value}'\n    },\n    trigger: {\n      alignItems: {\n        value: 'center'\n      },\n      backgroundColor: {\n        value: '{colors.background.primary.value}'\n      },\n      color: {\n        value: 'inherit'\n      },\n      gap: {\n        value: '{space.small.value}'\n      },\n      justifyContent: {\n        value: 'space-between'\n      },\n      paddingBlock: {\n        value: '{space.xs.value}'\n      },\n      paddingInline: {\n        value: '{space.small.value}'\n      },\n      _hover: {\n        color: {\n          value: 'inherit'\n        },\n        backgroundColor: {\n          value: '{colors.overlay.5.value}'\n        }\n      },\n      _focus: {\n        borderColor: {\n          value: '{colors.border.focus.value}'\n        },\n        boxShadow: {\n          value: {\n            offsetX: '0',\n            offsetY: '0',\n            blurRadius: '0',\n            spreadRadius: '2px',\n            color: '{colors.border.focus.value}'\n          }\n        }\n      }\n    },\n    content: {\n      color: {\n        value: 'inherit'\n      },\n      paddingInline: {\n        value: '{space.small.value}'\n      },\n      paddingBlockEnd: {\n        value: '{space.small.value}'\n      },\n      paddingBlockStart: {\n        value: '{space.xxxs.value}'\n      }\n    },\n    icon: {\n      color: {\n        value: '{colors.font.tertiary.value}'\n      },\n      transitionDuration: {\n        value: '{time.medium.value}'\n      },\n      transitionTimingFunction: {\n        value: 'cubic-bezier(0.87, 0, 0.13, 1)'\n      }\n    }\n  }\n};\nexport { accordion };", "map": {"version": 3, "names": ["accordion", "backgroundColor", "value", "item", "borderColor", "borderWidth", "borderStyle", "borderRadius", "trigger", "alignItems", "color", "gap", "justifyContent", "paddingBlock", "paddingInline", "_hover", "_focus", "boxShadow", "offsetX", "offsetY", "blurRadius", "spreadRadius", "content", "paddingBlockEnd", "paddingBlockStart", "icon", "transitionDuration", "transitionTimingFunction"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/accordion.mjs"], "sourcesContent": ["const accordion = {\n    backgroundColor: { value: '{colors.background.primary.value}' },\n    item: {\n        borderColor: { value: '{colors.border.secondary.value}' },\n        borderWidth: { value: '{borderWidths.small.value}' },\n        borderStyle: { value: 'solid' },\n        borderRadius: { value: '{radii.small.value}' },\n        trigger: {\n            alignItems: { value: 'center' },\n            backgroundColor: { value: '{colors.background.primary.value}' },\n            color: { value: 'inherit' },\n            gap: { value: '{space.small.value}' },\n            justifyContent: { value: 'space-between' },\n            paddingBlock: { value: '{space.xs.value}' },\n            paddingInline: { value: '{space.small.value}' },\n            _hover: {\n                color: { value: 'inherit' },\n                backgroundColor: { value: '{colors.overlay.5.value}' },\n            },\n            _focus: {\n                borderColor: { value: '{colors.border.focus.value}' },\n                boxShadow: {\n                    value: {\n                        offsetX: '0',\n                        offsetY: '0',\n                        blurRadius: '0',\n                        spreadRadius: '2px',\n                        color: '{colors.border.focus.value}',\n                    },\n                },\n            },\n        },\n        content: {\n            color: { value: 'inherit' },\n            paddingInline: { value: '{space.small.value}' },\n            paddingBlockEnd: { value: '{space.small.value}' },\n            paddingBlockStart: { value: '{space.xxxs.value}' },\n        },\n        icon: {\n            color: { value: '{colors.font.tertiary.value}' },\n            transitionDuration: { value: '{time.medium.value}' },\n            transitionTimingFunction: { value: 'cubic-bezier(0.87, 0, 0.13, 1)' },\n        },\n    },\n};\n\nexport { accordion };\n"], "mappings": "AAAA,MAAMA,SAAS,GAAG;EACdC,eAAe,EAAE;IAAEC,KAAK,EAAE;EAAoC,CAAC;EAC/DC,IAAI,EAAE;IACFC,WAAW,EAAE;MAAEF,KAAK,EAAE;IAAkC,CAAC;IACzDG,WAAW,EAAE;MAAEH,KAAK,EAAE;IAA6B,CAAC;IACpDI,WAAW,EAAE;MAAEJ,KAAK,EAAE;IAAQ,CAAC;IAC/BK,YAAY,EAAE;MAAEL,KAAK,EAAE;IAAsB,CAAC;IAC9CM,OAAO,EAAE;MACLC,UAAU,EAAE;QAAEP,KAAK,EAAE;MAAS,CAAC;MAC/BD,eAAe,EAAE;QAAEC,KAAK,EAAE;MAAoC,CAAC;MAC/DQ,KAAK,EAAE;QAAER,KAAK,EAAE;MAAU,CAAC;MAC3BS,GAAG,EAAE;QAAET,KAAK,EAAE;MAAsB,CAAC;MACrCU,cAAc,EAAE;QAAEV,KAAK,EAAE;MAAgB,CAAC;MAC1CW,YAAY,EAAE;QAAEX,KAAK,EAAE;MAAmB,CAAC;MAC3CY,aAAa,EAAE;QAAEZ,KAAK,EAAE;MAAsB,CAAC;MAC/Ca,MAAM,EAAE;QACJL,KAAK,EAAE;UAAER,KAAK,EAAE;QAAU,CAAC;QAC3BD,eAAe,EAAE;UAAEC,KAAK,EAAE;QAA2B;MACzD,CAAC;MACDc,MAAM,EAAE;QACJZ,WAAW,EAAE;UAAEF,KAAK,EAAE;QAA8B,CAAC;QACrDe,SAAS,EAAE;UACPf,KAAK,EAAE;YACHgB,OAAO,EAAE,GAAG;YACZC,OAAO,EAAE,GAAG;YACZC,UAAU,EAAE,GAAG;YACfC,YAAY,EAAE,KAAK;YACnBX,KAAK,EAAE;UACX;QACJ;MACJ;IACJ,CAAC;IACDY,OAAO,EAAE;MACLZ,KAAK,EAAE;QAAER,KAAK,EAAE;MAAU,CAAC;MAC3BY,aAAa,EAAE;QAAEZ,KAAK,EAAE;MAAsB,CAAC;MAC/CqB,eAAe,EAAE;QAAErB,KAAK,EAAE;MAAsB,CAAC;MACjDsB,iBAAiB,EAAE;QAAEtB,KAAK,EAAE;MAAqB;IACrD,CAAC;IACDuB,IAAI,EAAE;MACFf,KAAK,EAAE;QAAER,KAAK,EAAE;MAA+B,CAAC;MAChDwB,kBAAkB,EAAE;QAAExB,KAAK,EAAE;MAAsB,CAAC;MACpDyB,wBAAwB,EAAE;QAAEzB,KAAK,EAAE;MAAiC;IACxE;EACJ;AACJ,CAAC;AAED,SAASF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}