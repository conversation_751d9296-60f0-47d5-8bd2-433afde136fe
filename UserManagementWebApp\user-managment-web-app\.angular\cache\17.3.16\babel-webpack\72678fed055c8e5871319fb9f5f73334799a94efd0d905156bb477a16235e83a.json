{"ast": null, "code": "const frDict = {\n  'Account recovery requires verified contact information': 'La récupération du compte nécessite des informations de contact vérifiées',\n  'Authenticator App (TOTP)': 'Application d’authentification (TOTP)',\n  'Back to Sign In': 'Retour à la connexion',\n  'Change Password': 'Modifier le mot de passe',\n  Changing: 'Modification en cours',\n  Code: 'Code',\n  'Confirm Email Code': 'Confirmer le code e-mail',\n  'Confirm Password': 'Confirmez le mot de passe',\n  'Confirm Sign Up': \"Confirmer l'inscription\",\n  'Confirm SMS Code': 'Confirmer le code SMS',\n  'Confirm TOTP Code': 'Confirmer le code TOTP',\n  Confirm: 'Confirmer',\n  'Confirmation Code': 'Code de confirmation',\n  Confirming: 'Confirmation',\n  'Create a new account': 'Créer un nouveau compte',\n  'Create Account': 'Créer un compte',\n  'Creating Account': `Création d'un compte`,\n  'Dismiss alert': `Supprimer l'alerte`,\n  Email: 'Email',\n  'Email Message': 'Message de l’e-mail',\n  'Enter your code': 'Saisissez cotre code de confirmation',\n  'Enter your Email': 'Saisissez votre adresse e-mail',\n  'Enter your email': 'Saisissez votre adresse e-mail',\n  'Enter your phone number': 'Saisissez votre numéro de téléphone',\n  'Enter your username': \"Saisissez votre nom d'utilisateur\",\n  'Forgot your password?': 'Mot de passe oublié ? ',\n  'Hide password': 'Masquer le mot de passe',\n  'It may take a minute to arrive': 'Cela peut prendre une minute',\n  Loading: 'Chargement en cours',\n  'Multi-Factor Authentication': 'Authentification multifactorielle',\n  'Multi-Factor Authentication Setup': 'Configuration de l’authentification multifactorielle',\n  'New password': 'Nouveau mot de passe',\n  or: 'ou',\n  Password: 'Mot de passe',\n  'Phone Number': 'Numéro de téléphone',\n  'Resend Code': 'Renvoyer le code',\n  'Reset your Password': 'Réinitialiser votre mot de passe',\n  'Reset your password': 'Réinitialisez votre mot de passe',\n  'Select MFA Type': 'Sélectionner le type de MFA',\n  'Send code': 'Envoyer le code',\n  'Send Code': \"M'envoyer un code\",\n  Sending: 'Envoi en cours',\n  'Setup Email': 'E-mail de configuration',\n  'Setup TOTP': 'Configuration de TOTP',\n  'Show password': 'Afficher le mot de passe',\n  'Sign in to your account': 'Connexion à votre compte',\n  'Sign In with Amazon': 'Se connecter avec Amazon',\n  'Sign In with Apple': 'Se connecter avec Apple',\n  'Sign In with Facebook': 'Se connecter avec Facebook',\n  'Sign In with Google': 'Se connecter avec Google',\n  'Sign in': 'Se connecter',\n  'Sign In': 'Se connecter',\n  'Signing in': 'Connexion en cours',\n  Skip: 'Passer',\n  Submit: 'Soumettre',\n  Submitting: 'Envoi en cours',\n  'Text Message (SMS)': 'Message texte (SMS)',\n  Username: \"Nom d'utilisateur\",\n  'Verify Contact': 'Vérifier le contact',\n  Verify: 'Vérifier',\n  'We Sent A Code': 'Nous avons envoyé un code',\n  'We Texted You': 'Nous vous avons envoyé un SMS',\n  'Your code is on the way. To log in, enter the code we sent you': `Votre code est en cours d'envoi. Pour vous connecter, saisissez le code que nous vous avons envoyé`,\n  // Additional translations provided by customers\n  'Add your Profile': 'Ajoutez votre profil',\n  'Add your Website': 'Ajoutez votre site web',\n  'An account with the given email already exists.': 'Un utilisateur avec cette adresse email existe déjà.',\n  Birthdate: 'Date de naissance',\n  Change: 'Modifier',\n  'Confirm a Code': 'Confirmer un code',\n  'Confirm Sign In': 'Confirmer la connexion',\n  'Create account': 'Créer un compte',\n  'Enter your Birthdate': 'Saisissez votre date de naissance',\n  'Enter your Confirmation Code': 'Saisissez votre code de confirmation',\n  'Enter your Family Name': 'Saisissez votre nom de famille',\n  'Enter your Given Name': 'Saisissez votre prénom',\n  'Enter your Middle Name': 'Saisissez votre deuxième prénom',\n  'Enter your Name': 'Saisissez votre nom',\n  'Enter your Nickname': 'Saisissez votre surnom',\n  'Enter your Password': 'Saisissez votre mot de passe',\n  'Enter your Phone Number': 'Saisissez votre numéro de téléphone',\n  'Enter your Preferred Username': \"Saisissez votre nom d'utilisateur\",\n  'Enter your password': 'Saisissez votre mot de passe',\n  'Given Name': 'Prénom',\n  'Family Name': 'Nom de famille',\n  'Forgot Password': 'Mot de passe oublié',\n  'Forgot Password?': 'Mot de passe oublié ?',\n  'Incorrect username or password.': 'Identifiant ou mot de passe incorrect.',\n  'Have an account? ': 'Déjà un compte ? ',\n  Hello: 'Bonjour',\n  'Incorrect username or password': 'Identifiant ou mot de passe incorrect',\n  'Invalid password format': 'Format de mot de passe invalide',\n  'Invalid phone number format': `Format de numéro de téléphone invalide. Veuillez utiliser un format +***********`,\n  'Loading...': 'Chargement...',\n  'Lost your code? ': 'Vous avez perdu votre code ? ',\n  'Network error': 'Erreur réseau',\n  'New Password': 'Nouveau mot de passe',\n  Name: 'Nom',\n  'No account? ': 'Pas de compte ? ',\n  'Please confirm your Password': 'Confirmez votre mot de passe',\n  'Preferred Username': \"Nom d'utilisateur préféré\",\n  Profile: 'Profil',\n  'Resend a Code': 'Renvoyer un code',\n  'Reset password': 'Réinitialiser le mot de passe',\n  'Reset Password': 'Réinitialiser le mot de passe',\n  Send: 'Envoyer',\n  'Sign In with AWS': 'Se connecter avec AWS',\n  'Sign Out': 'Déconnexion',\n  'Sign Up': \"S'inscrire\",\n  SMS: 'SMS',\n  'User already exists': \"L'utilisateur existe déjà\",\n  'User does not exist': \"L'utilisateur n'existe pas\",\n  'Username cannot be empty': \"Le nom d'utilisateur doit être renseigné\",\n  'Username/client id combination not found.': \"L'utilisateur n'existe pas\",\n  'We Emailed You': 'Nous vous avons envoyé un code',\n  'Your code is on the way. To log in, enter the code we emailed to': 'Votre code est en route. Pour vous connecter entrez le code reçu sur cette adresse email',\n  'Your code is on the way. To log in, enter the code we texted to': 'Votre code est en route. Pour vous connecter entrez le code reçu sur ce numéro de téléphone',\n  'Your passwords must match': 'Vos mots de passe doivent être identiques',\n  'It may take a minute to arrive.': 'Cela peut prendre quelques minutes.',\n  Website: 'Site web',\n  'Password must have at least 8 characters': 'Le mot de passe doit comporter au moins 8 caractères',\n  'Password did not conform with policy: Password must have uppercase characters': 'Le mot de passe doit comporter des caractères majuscules',\n  'Password did not conform with policy: Password must have numeric characters': 'Le mot de passe doit comporter des caractères numériques',\n  'Password did not conform with policy: Password must have symbol characters': 'Le mot de passe doit comporter des symboles',\n  'Password did not conform with policy: Password must have lowercase characters': 'Le mot de passe doit comporter des caractères minuscules',\n  'Invalid verification code provided, please try again.': 'Code de vérification invalide, veuillez réessayer.',\n  'Attempt limit exceeded, please try after some time.': 'Nombre maximum de tentatives dépassé, veuillez réessayer plus tard.',\n  'A network error has occurred.': \"Une erreur de réseau s'est produite.\"\n};\nexport { frDict };", "map": {"version": 3, "names": ["frDict", "Changing", "Code", "Confirm", "Confirming", "Email", "Loading", "or", "Password", "Sending", "<PERSON><PERSON>", "Submit", "Submitting", "Username", "Verify", "Birthdate", "Change", "Hello", "Name", "Profile", "Send", "SMS", "Website"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/i18n/dictionaries/authenticator/fr.mjs"], "sourcesContent": ["const frDict = {\n    'Account recovery requires verified contact information': 'La récupération du compte nécessite des informations de contact vérifiées',\n    'Authenticator App (TOTP)': 'Application d’authentification (TOTP)',\n    'Back to Sign In': 'Retour à la connexion',\n    'Change Password': 'Modifier le mot de passe',\n    Changing: 'Modification en cours',\n    Code: 'Code',\n    'Confirm Email Code': 'Confirmer le code e-mail',\n    'Confirm Password': 'Confirmez le mot de passe',\n    'Confirm Sign Up': \"Confirmer l'inscription\",\n    'Confirm SMS Code': 'Confirmer le code SMS',\n    'Confirm TOTP Code': 'Confirmer le code TOTP',\n    Confirm: 'Confirmer',\n    'Confirmation Code': 'Code de confirmation',\n    Confirming: 'Confirmation',\n    'Create a new account': 'Créer un nouveau compte',\n    'Create Account': 'Créer un compte',\n    'Creating Account': `Création d'un compte`,\n    'Dismiss alert': `Supprimer l'alerte`,\n    Email: 'Email',\n    'Email Message': 'Message de l’e-mail',\n    'Enter your code': 'Saisissez cotre code de confirmation',\n    'Enter your Email': 'Saisissez votre adresse e-mail',\n    'Enter your email': 'Saisissez votre adresse e-mail',\n    'Enter your phone number': 'Saisissez votre numéro de téléphone',\n    'Enter your username': \"Saisissez votre nom d'utilisateur\",\n    'Forgot your password?': 'Mot de passe oublié ? ',\n    'Hide password': 'Masquer le mot de passe',\n    'It may take a minute to arrive': 'Cela peut prendre une minute',\n    Loading: 'Chargement en cours',\n    'Multi-Factor Authentication': 'Authentification multifactorielle',\n    'Multi-Factor Authentication Setup': 'Configuration de l’authentification multifactorielle',\n    'New password': 'Nouveau mot de passe',\n    or: 'ou',\n    Password: 'Mot de passe',\n    'Phone Number': 'Numéro de téléphone',\n    'Resend Code': 'Renvoyer le code',\n    'Reset your Password': 'Réinitialiser votre mot de passe',\n    'Reset your password': 'Réinitialisez votre mot de passe',\n    'Select MFA Type': 'Sélectionner le type de MFA',\n    'Send code': 'Envoyer le code',\n    'Send Code': \"M'envoyer un code\",\n    Sending: 'Envoi en cours',\n    'Setup Email': 'E-mail de configuration',\n    'Setup TOTP': 'Configuration de TOTP',\n    'Show password': 'Afficher le mot de passe',\n    'Sign in to your account': 'Connexion à votre compte',\n    'Sign In with Amazon': 'Se connecter avec Amazon',\n    'Sign In with Apple': 'Se connecter avec Apple',\n    'Sign In with Facebook': 'Se connecter avec Facebook',\n    'Sign In with Google': 'Se connecter avec Google',\n    'Sign in': 'Se connecter',\n    'Sign In': 'Se connecter',\n    'Signing in': 'Connexion en cours',\n    Skip: 'Passer',\n    Submit: 'Soumettre',\n    Submitting: 'Envoi en cours',\n    'Text Message (SMS)': 'Message texte (SMS)',\n    Username: \"Nom d'utilisateur\",\n    'Verify Contact': 'Vérifier le contact',\n    Verify: 'Vérifier',\n    'We Sent A Code': 'Nous avons envoyé un code',\n    'We Texted You': 'Nous vous avons envoyé un SMS',\n    'Your code is on the way. To log in, enter the code we sent you': `Votre code est en cours d'envoi. Pour vous connecter, saisissez le code que nous vous avons envoyé`,\n    // Additional translations provided by customers\n    'Add your Profile': 'Ajoutez votre profil',\n    'Add your Website': 'Ajoutez votre site web',\n    'An account with the given email already exists.': 'Un utilisateur avec cette adresse email existe déjà.',\n    Birthdate: 'Date de naissance',\n    Change: 'Modifier',\n    'Confirm a Code': 'Confirmer un code',\n    'Confirm Sign In': 'Confirmer la connexion',\n    'Create account': 'Créer un compte',\n    'Enter your Birthdate': 'Saisissez votre date de naissance',\n    'Enter your Confirmation Code': 'Saisissez votre code de confirmation',\n    'Enter your Family Name': 'Saisissez votre nom de famille',\n    'Enter your Given Name': 'Saisissez votre prénom',\n    'Enter your Middle Name': 'Saisissez votre deuxième prénom',\n    'Enter your Name': 'Saisissez votre nom',\n    'Enter your Nickname': 'Saisissez votre surnom',\n    'Enter your Password': 'Saisissez votre mot de passe',\n    'Enter your Phone Number': 'Saisissez votre numéro de téléphone',\n    'Enter your Preferred Username': \"Saisissez votre nom d'utilisateur\",\n    'Enter your password': 'Saisissez votre mot de passe',\n    'Given Name': 'Prénom',\n    'Family Name': 'Nom de famille',\n    'Forgot Password': 'Mot de passe oublié',\n    'Forgot Password?': 'Mot de passe oublié ?',\n    'Incorrect username or password.': 'Identifiant ou mot de passe incorrect.',\n    'Have an account? ': 'Déjà un compte ? ',\n    Hello: 'Bonjour',\n    'Incorrect username or password': 'Identifiant ou mot de passe incorrect',\n    'Invalid password format': 'Format de mot de passe invalide',\n    'Invalid phone number format': `Format de numéro de téléphone invalide. Veuillez utiliser un format +***********`,\n    'Loading...': 'Chargement...',\n    'Lost your code? ': 'Vous avez perdu votre code ? ',\n    'Network error': 'Erreur réseau',\n    'New Password': 'Nouveau mot de passe',\n    Name: 'Nom',\n    'No account? ': 'Pas de compte ? ',\n    'Please confirm your Password': 'Confirmez votre mot de passe',\n    'Preferred Username': \"Nom d'utilisateur préféré\",\n    Profile: 'Profil',\n    'Resend a Code': 'Renvoyer un code',\n    'Reset password': 'Réinitialiser le mot de passe',\n    'Reset Password': 'Réinitialiser le mot de passe',\n    Send: 'Envoyer',\n    'Sign In with AWS': 'Se connecter avec AWS',\n    'Sign Out': 'Déconnexion',\n    'Sign Up': \"S'inscrire\",\n    SMS: 'SMS',\n    'User already exists': \"L'utilisateur existe déjà\",\n    'User does not exist': \"L'utilisateur n'existe pas\",\n    'Username cannot be empty': \"Le nom d'utilisateur doit être renseigné\",\n    'Username/client id combination not found.': \"L'utilisateur n'existe pas\",\n    'We Emailed You': 'Nous vous avons envoyé un code',\n    'Your code is on the way. To log in, enter the code we emailed to': 'Votre code est en route. Pour vous connecter entrez le code reçu sur cette adresse email',\n    'Your code is on the way. To log in, enter the code we texted to': 'Votre code est en route. Pour vous connecter entrez le code reçu sur ce numéro de téléphone',\n    'Your passwords must match': 'Vos mots de passe doivent être identiques',\n    'It may take a minute to arrive.': 'Cela peut prendre quelques minutes.',\n    Website: 'Site web',\n    'Password must have at least 8 characters': 'Le mot de passe doit comporter au moins 8 caractères',\n    'Password did not conform with policy: Password must have uppercase characters': 'Le mot de passe doit comporter des caractères majuscules',\n    'Password did not conform with policy: Password must have numeric characters': 'Le mot de passe doit comporter des caractères numériques',\n    'Password did not conform with policy: Password must have symbol characters': 'Le mot de passe doit comporter des symboles',\n    'Password did not conform with policy: Password must have lowercase characters': 'Le mot de passe doit comporter des caractères minuscules',\n    'Invalid verification code provided, please try again.': 'Code de vérification invalide, veuillez réessayer.',\n    'Attempt limit exceeded, please try after some time.': 'Nombre maximum de tentatives dépassé, veuillez réessayer plus tard.',\n    'A network error has occurred.': \"Une erreur de réseau s'est produite.\",\n};\n\nexport { frDict };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACX,wDAAwD,EAAE,2EAA2E;EACrI,0BAA0B,EAAE,uCAAuC;EACnE,iBAAiB,EAAE,uBAAuB;EAC1C,iBAAiB,EAAE,0BAA0B;EAC7CC,QAAQ,EAAE,uBAAuB;EACjCC,IAAI,EAAE,MAAM;EACZ,oBAAoB,EAAE,0BAA0B;EAChD,kBAAkB,EAAE,2BAA2B;EAC/C,iBAAiB,EAAE,yBAAyB;EAC5C,kBAAkB,EAAE,uBAAuB;EAC3C,mBAAmB,EAAE,wBAAwB;EAC7CC,OAAO,EAAE,WAAW;EACpB,mBAAmB,EAAE,sBAAsB;EAC3CC,UAAU,EAAE,cAAc;EAC1B,sBAAsB,EAAE,yBAAyB;EACjD,gBAAgB,EAAE,iBAAiB;EACnC,kBAAkB,EAAE,sBAAsB;EAC1C,eAAe,EAAE,oBAAoB;EACrCC,KAAK,EAAE,OAAO;EACd,eAAe,EAAE,qBAAqB;EACtC,iBAAiB,EAAE,sCAAsC;EACzD,kBAAkB,EAAE,gCAAgC;EACpD,kBAAkB,EAAE,gCAAgC;EACpD,yBAAyB,EAAE,qCAAqC;EAChE,qBAAqB,EAAE,mCAAmC;EAC1D,uBAAuB,EAAE,wBAAwB;EACjD,eAAe,EAAE,yBAAyB;EAC1C,gCAAgC,EAAE,8BAA8B;EAChEC,OAAO,EAAE,qBAAqB;EAC9B,6BAA6B,EAAE,mCAAmC;EAClE,mCAAmC,EAAE,sDAAsD;EAC3F,cAAc,EAAE,sBAAsB;EACtCC,EAAE,EAAE,IAAI;EACRC,QAAQ,EAAE,cAAc;EACxB,cAAc,EAAE,qBAAqB;EACrC,aAAa,EAAE,kBAAkB;EACjC,qBAAqB,EAAE,kCAAkC;EACzD,qBAAqB,EAAE,kCAAkC;EACzD,iBAAiB,EAAE,6BAA6B;EAChD,WAAW,EAAE,iBAAiB;EAC9B,WAAW,EAAE,mBAAmB;EAChCC,OAAO,EAAE,gBAAgB;EACzB,aAAa,EAAE,yBAAyB;EACxC,YAAY,EAAE,uBAAuB;EACrC,eAAe,EAAE,0BAA0B;EAC3C,yBAAyB,EAAE,0BAA0B;EACrD,qBAAqB,EAAE,0BAA0B;EACjD,oBAAoB,EAAE,yBAAyB;EAC/C,uBAAuB,EAAE,4BAA4B;EACrD,qBAAqB,EAAE,0BAA0B;EACjD,SAAS,EAAE,cAAc;EACzB,SAAS,EAAE,cAAc;EACzB,YAAY,EAAE,oBAAoB;EAClCC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,WAAW;EACnBC,UAAU,EAAE,gBAAgB;EAC5B,oBAAoB,EAAE,qBAAqB;EAC3CC,QAAQ,EAAE,mBAAmB;EAC7B,gBAAgB,EAAE,qBAAqB;EACvCC,MAAM,EAAE,UAAU;EAClB,gBAAgB,EAAE,2BAA2B;EAC7C,eAAe,EAAE,+BAA+B;EAChD,gEAAgE,EAAE,oGAAoG;EACtK;EACA,kBAAkB,EAAE,sBAAsB;EAC1C,kBAAkB,EAAE,wBAAwB;EAC5C,iDAAiD,EAAE,sDAAsD;EACzGC,SAAS,EAAE,mBAAmB;EAC9BC,MAAM,EAAE,UAAU;EAClB,gBAAgB,EAAE,mBAAmB;EACrC,iBAAiB,EAAE,wBAAwB;EAC3C,gBAAgB,EAAE,iBAAiB;EACnC,sBAAsB,EAAE,mCAAmC;EAC3D,8BAA8B,EAAE,sCAAsC;EACtE,wBAAwB,EAAE,gCAAgC;EAC1D,uBAAuB,EAAE,wBAAwB;EACjD,wBAAwB,EAAE,iCAAiC;EAC3D,iBAAiB,EAAE,qBAAqB;EACxC,qBAAqB,EAAE,wBAAwB;EAC/C,qBAAqB,EAAE,8BAA8B;EACrD,yBAAyB,EAAE,qCAAqC;EAChE,+BAA+B,EAAE,mCAAmC;EACpE,qBAAqB,EAAE,8BAA8B;EACrD,YAAY,EAAE,QAAQ;EACtB,aAAa,EAAE,gBAAgB;EAC/B,iBAAiB,EAAE,qBAAqB;EACxC,kBAAkB,EAAE,uBAAuB;EAC3C,iCAAiC,EAAE,wCAAwC;EAC3E,mBAAmB,EAAE,mBAAmB;EACxCC,KAAK,EAAE,SAAS;EAChB,gCAAgC,EAAE,uCAAuC;EACzE,yBAAyB,EAAE,iCAAiC;EAC5D,6BAA6B,EAAE,kFAAkF;EACjH,YAAY,EAAE,eAAe;EAC7B,kBAAkB,EAAE,+BAA+B;EACnD,eAAe,EAAE,eAAe;EAChC,cAAc,EAAE,sBAAsB;EACtCC,IAAI,EAAE,KAAK;EACX,cAAc,EAAE,kBAAkB;EAClC,8BAA8B,EAAE,8BAA8B;EAC9D,oBAAoB,EAAE,2BAA2B;EACjDC,OAAO,EAAE,QAAQ;EACjB,eAAe,EAAE,kBAAkB;EACnC,gBAAgB,EAAE,+BAA+B;EACjD,gBAAgB,EAAE,+BAA+B;EACjDC,IAAI,EAAE,SAAS;EACf,kBAAkB,EAAE,uBAAuB;EAC3C,UAAU,EAAE,aAAa;EACzB,SAAS,EAAE,YAAY;EACvBC,GAAG,EAAE,KAAK;EACV,qBAAqB,EAAE,2BAA2B;EAClD,qBAAqB,EAAE,4BAA4B;EACnD,0BAA0B,EAAE,0CAA0C;EACtE,2CAA2C,EAAE,4BAA4B;EACzE,gBAAgB,EAAE,gCAAgC;EAClD,kEAAkE,EAAE,0FAA0F;EAC9J,iEAAiE,EAAE,6FAA6F;EAChK,2BAA2B,EAAE,2CAA2C;EACxE,iCAAiC,EAAE,qCAAqC;EACxEC,OAAO,EAAE,UAAU;EACnB,0CAA0C,EAAE,sDAAsD;EAClG,+EAA+E,EAAE,0DAA0D;EAC3I,6EAA6E,EAAE,0DAA0D;EACzI,4EAA4E,EAAE,6CAA6C;EAC3H,+EAA+E,EAAE,0DAA0D;EAC3I,uDAAuD,EAAE,oDAAoD;EAC7G,qDAAqD,EAAE,qEAAqE;EAC5H,+BAA+B,EAAE;AACrC,CAAC;AAED,SAAStB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}