{"ast": null, "code": "import { ALLOWED_SPECIAL_CHARACTERS, emailRegex } from './constants.mjs';\n\n// replaces all characters in a string with '*', except for the first and last char\nconst censorAllButFirstAndLast = value => {\n  const split = value.trim().split('');\n  for (let i = 0; i < split.length; i++) {\n    if (i > 0 && i < split.length - 1) {\n      split[i] = '*';\n    }\n  }\n  return split.join('');\n};\n// censors all but the last four characters of a phone number\nconst censorPhoneNumber = val => {\n  if (val.length < 4) {\n    return val;\n  }\n  const split = val.split('');\n  for (let i = 0; i < split.length - 4; i++) {\n    split[i] = '*';\n  }\n  return split.join('');\n};\n// censors all but the first and last of the name of an email and keeps domain\nconst censorEmail = val => {\n  const splitEmail = val.split('@');\n  const censoredName = censorAllButFirstAndLast(splitEmail[0]);\n  return `${censoredName}@${splitEmail[1]}`;\n};\n// based on the ContactMethod type, returns a censored contact value\nconst censorContactMethod = (type, value) => {\n  return type === 'Phone Number' ? censorPhoneNumber(value) : censorEmail(value);\n};\nconst hasSpecialChars = password => ALLOWED_SPECIAL_CHARACTERS.some(char => password.includes(char));\nconst getTotpCodeURL = (issuer, username, secret) => encodeURI(`otpauth://totp/${issuer}:${username}?secret=${secret}&issuer=${issuer}`);\nfunction trimValues(values, ...ignored) {\n  return Object.entries(values).reduce((acc, [name, value]) => ({\n    ...acc,\n    [name]: ignored.includes(name) ? value : value?.trim()\n  }), {});\n}\nconst isValidEmail = value => {\n  if (!value) return false;\n  return emailRegex.test(value);\n};\nexport { censorAllButFirstAndLast, censorContactMethod, censorEmail, censorPhoneNumber, getTotpCodeURL, hasSpecialChars, isValidEmail, trimValues };", "map": {"version": 3, "names": ["ALLOWED_SPECIAL_CHARACTERS", "emailRegex", "censorAllButFirstAndLast", "value", "split", "trim", "i", "length", "join", "censorPhoneNumber", "val", "censorEmail", "splitEmail", "censoredName", "censorContactMethod", "type", "hasSpecialChars", "password", "some", "char", "includes", "getTotpCodeURL", "issuer", "username", "secret", "encodeURI", "trimValues", "values", "ignored", "Object", "entries", "reduce", "acc", "name", "isValidEmail", "test"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/helpers/authenticator/utils.mjs"], "sourcesContent": ["import { ALLOWED_SPECIAL_CHARACTERS, emailRegex } from './constants.mjs';\n\n// replaces all characters in a string with '*', except for the first and last char\nconst censorAllButFirstAndLast = (value) => {\n    const split = value.trim().split('');\n    for (let i = 0; i < split.length; i++) {\n        if (i > 0 && i < split.length - 1) {\n            split[i] = '*';\n        }\n    }\n    return split.join('');\n};\n// censors all but the last four characters of a phone number\nconst censorPhoneNumber = (val) => {\n    if (val.length < 4) {\n        return val;\n    }\n    const split = val.split('');\n    for (let i = 0; i < split.length - 4; i++) {\n        split[i] = '*';\n    }\n    return split.join('');\n};\n// censors all but the first and last of the name of an email and keeps domain\nconst censorEmail = (val) => {\n    const splitEmail = val.split('@');\n    const censoredName = censorAllButFirstAndLast(splitEmail[0]);\n    return `${censoredName}@${splitEmail[1]}`;\n};\n// based on the ContactMethod type, returns a censored contact value\nconst censorContactMethod = (type, value) => {\n    return type === 'Phone Number'\n        ? censorPhoneNumber(value)\n        : censorEmail(value);\n};\nconst hasSpecialChars = (password) => ALLOWED_SPECIAL_CHARACTERS.some((char) => password.includes(char));\nconst getTotpCodeURL = (issuer, username, secret) => encodeURI(`otpauth://totp/${issuer}:${username}?secret=${secret}&issuer=${issuer}`);\nfunction trimValues(values, ...ignored) {\n    return Object.entries(values).reduce((acc, [name, value]) => ({\n        ...acc,\n        [name]: ignored.includes(name) ? value : value?.trim(),\n    }), {});\n}\nconst isValidEmail = (value) => {\n    if (!value)\n        return false;\n    return emailRegex.test(value);\n};\n\nexport { censorAllButFirstAndLast, censorContactMethod, censorEmail, censorPhoneNumber, getTotpCodeURL, hasSpecialChars, isValidEmail, trimValues };\n"], "mappings": "AAAA,SAASA,0BAA0B,EAAEC,UAAU,QAAQ,iBAAiB;;AAExE;AACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;EACxC,MAAMC,KAAK,GAAGD,KAAK,CAACE,IAAI,CAAC,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC;EACpC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAGF,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;MAC/BH,KAAK,CAACE,CAAC,CAAC,GAAG,GAAG;IAClB;EACJ;EACA,OAAOF,KAAK,CAACI,IAAI,CAAC,EAAE,CAAC;AACzB,CAAC;AACD;AACA,MAAMC,iBAAiB,GAAIC,GAAG,IAAK;EAC/B,IAAIA,GAAG,CAACH,MAAM,GAAG,CAAC,EAAE;IAChB,OAAOG,GAAG;EACd;EACA,MAAMN,KAAK,GAAGM,GAAG,CAACN,KAAK,CAAC,EAAE,CAAC;EAC3B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,EAAE,EAAE;IACvCF,KAAK,CAACE,CAAC,CAAC,GAAG,GAAG;EAClB;EACA,OAAOF,KAAK,CAACI,IAAI,CAAC,EAAE,CAAC;AACzB,CAAC;AACD;AACA,MAAMG,WAAW,GAAID,GAAG,IAAK;EACzB,MAAME,UAAU,GAAGF,GAAG,CAACN,KAAK,CAAC,GAAG,CAAC;EACjC,MAAMS,YAAY,GAAGX,wBAAwB,CAACU,UAAU,CAAC,CAAC,CAAC,CAAC;EAC5D,OAAO,GAAGC,YAAY,IAAID,UAAU,CAAC,CAAC,CAAC,EAAE;AAC7C,CAAC;AACD;AACA,MAAME,mBAAmB,GAAGA,CAACC,IAAI,EAAEZ,KAAK,KAAK;EACzC,OAAOY,IAAI,KAAK,cAAc,GACxBN,iBAAiB,CAACN,KAAK,CAAC,GACxBQ,WAAW,CAACR,KAAK,CAAC;AAC5B,CAAC;AACD,MAAMa,eAAe,GAAIC,QAAQ,IAAKjB,0BAA0B,CAACkB,IAAI,CAAEC,IAAI,IAAKF,QAAQ,CAACG,QAAQ,CAACD,IAAI,CAAC,CAAC;AACxG,MAAME,cAAc,GAAGA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,KAAKC,SAAS,CAAC,kBAAkBH,MAAM,IAAIC,QAAQ,WAAWC,MAAM,WAAWF,MAAM,EAAE,CAAC;AACxI,SAASI,UAAUA,CAACC,MAAM,EAAE,GAAGC,OAAO,EAAE;EACpC,OAAOC,MAAM,CAACC,OAAO,CAACH,MAAM,CAAC,CAACI,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,IAAI,EAAE9B,KAAK,CAAC,MAAM;IAC1D,GAAG6B,GAAG;IACN,CAACC,IAAI,GAAGL,OAAO,CAACR,QAAQ,CAACa,IAAI,CAAC,GAAG9B,KAAK,GAAGA,KAAK,EAAEE,IAAI,CAAC;EACzD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACX;AACA,MAAM6B,YAAY,GAAI/B,KAAK,IAAK;EAC5B,IAAI,CAACA,KAAK,EACN,OAAO,KAAK;EAChB,OAAOF,UAAU,CAACkC,IAAI,CAAChC,KAAK,CAAC;AACjC,CAAC;AAED,SAASD,wBAAwB,EAAEY,mBAAmB,EAAEH,WAAW,EAAEF,iBAAiB,EAAEY,cAAc,EAAEL,eAAe,EAAEkB,YAAY,EAAER,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}