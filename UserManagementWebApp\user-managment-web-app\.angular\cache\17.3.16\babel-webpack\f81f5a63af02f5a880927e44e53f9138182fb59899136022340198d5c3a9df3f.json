{"ast": null, "code": "import { AuthError } from '../../errors/AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction getRegionFromUserPoolId(userPoolId) {\n  const region = userPoolId?.split('_')[0];\n  if (!userPoolId || userPoolId.indexOf('_') < 0 || !region || typeof region !== 'string') throw new AuthError({\n    name: 'InvalidUserPoolId',\n    message: 'Invalid user pool id provided.'\n  });\n  return region;\n}\nfunction getRegionFromIdentityPoolId(identityPoolId) {\n  if (!identityPoolId || !identityPoolId.includes(':')) {\n    throw new AuthError({\n      name: 'InvalidIdentityPoolIdException',\n      message: 'Invalid identity pool id provided.',\n      recoverySuggestion: 'Make sure a valid identityPoolId is given in the config.'\n    });\n  }\n  return identityPoolId.split(':')[0];\n}\nexport { getRegionFromIdentityPoolId, getRegionFromUserPoolId };", "map": {"version": 3, "names": ["<PERSON>th<PERSON><PERSON><PERSON>", "getRegionFromUserPoolId", "userPoolId", "region", "split", "indexOf", "name", "message", "getRegionFromIdentityPoolId", "identityPoolId", "includes", "recoverySuggestion"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/parsers/regionParsers.mjs"], "sourcesContent": ["import { AuthError } from '../../errors/AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction getRegionFromUserPoolId(userPoolId) {\n    const region = userPoolId?.split('_')[0];\n    if (!userPoolId ||\n        userPoolId.indexOf('_') < 0 ||\n        !region ||\n        typeof region !== 'string')\n        throw new AuthError({\n            name: 'InvalidUserPoolId',\n            message: 'Invalid user pool id provided.',\n        });\n    return region;\n}\nfunction getRegionFromIdentityPoolId(identityPoolId) {\n    if (!identityPoolId || !identityPoolId.includes(':')) {\n        throw new AuthError({\n            name: 'InvalidIdentityPoolIdException',\n            message: 'Invalid identity pool id provided.',\n            recoverySuggestion: 'Make sure a valid identityPoolId is given in the config.',\n        });\n    }\n    return identityPoolId.split(':')[0];\n}\n\nexport { getRegionFromIdentityPoolId, getRegionFromUserPoolId };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,4BAA4B;;AAEtD;AACA;AACA,SAASC,uBAAuBA,CAACC,UAAU,EAAE;EACzC,MAAMC,MAAM,GAAGD,UAAU,EAAEE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACxC,IAAI,CAACF,UAAU,IACXA,UAAU,CAACG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAC3B,CAACF,MAAM,IACP,OAAOA,MAAM,KAAK,QAAQ,EAC1B,MAAM,IAAIH,SAAS,CAAC;IAChBM,IAAI,EAAE,mBAAmB;IACzBC,OAAO,EAAE;EACb,CAAC,CAAC;EACN,OAAOJ,MAAM;AACjB;AACA,SAASK,2BAA2BA,CAACC,cAAc,EAAE;EACjD,IAAI,CAACA,cAAc,IAAI,CAACA,cAAc,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;IAClD,MAAM,IAAIV,SAAS,CAAC;MAChBM,IAAI,EAAE,gCAAgC;MACtCC,OAAO,EAAE,oCAAoC;MAC7CI,kBAAkB,EAAE;IACxB,CAAC,CAAC;EACN;EACA,OAAOF,cAAc,CAACL,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC;AAEA,SAASI,2BAA2B,EAAEP,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}