{"ast": null, "code": "const outlineWidths = {\n  small: {\n    value: '1px'\n  },\n  medium: {\n    value: '2px'\n  },\n  large: {\n    value: '3px'\n  }\n};\nexport { outlineWidths };", "map": {"version": 3, "names": ["outlineWidths", "small", "value", "medium", "large"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/outlineWidths.mjs"], "sourcesContent": ["const outlineWidths = {\n    small: { value: '1px' },\n    medium: { value: '2px' },\n    large: { value: '3px' },\n};\n\nexport { outlineWidths };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAG;EAClBC,KAAK,EAAE;IAAEC,KAAK,EAAE;EAAM,CAAC;EACvBC,MAAM,EAAE;IAAED,KAAK,EAAE;EAAM,CAAC;EACxBE,KAAK,EAAE;IAAEF,KAAK,EAAE;EAAM;AAC1B,CAAC;AAED,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}