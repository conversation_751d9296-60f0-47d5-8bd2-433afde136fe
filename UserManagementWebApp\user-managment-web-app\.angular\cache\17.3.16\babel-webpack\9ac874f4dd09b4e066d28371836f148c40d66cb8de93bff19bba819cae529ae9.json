{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BackgroundManagerNotOpenError } from './BackgroundManagerNotOpenError.mjs';\nimport { BackgroundProcessManagerState } from './types.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @private For internal Amplify use.\n *\n * Creates a new scope for promises, observables, and other types of work or\n * processes that may be running in the background. This manager provides\n * an singular entrypoint to request termination and await completion.\n *\n * As work completes on its own prior to close, the manager removes them\n * from the registry to avoid holding references to completed jobs.\n */\nclass BackgroundProcessManager {\n  constructor() {\n    /**\n     * A string indicating whether the manager is accepting new work (\"Open\"),\n     * waiting for work to complete (\"Closing\"), or fully done with all\n     * submitted work and *not* accepting new jobs (\"Closed\").\n     */\n    this._state = BackgroundProcessManagerState.Open;\n    /**\n     * The list of outstanding jobs we'll need to wait for upon `close()`\n     */\n    this.jobs = new Set();\n  }\n  add(jobOrDescription, optionalDescription) {\n    let job;\n    let description;\n    if (typeof jobOrDescription === 'string') {\n      job = undefined;\n      description = jobOrDescription;\n    } else {\n      job = jobOrDescription;\n      description = optionalDescription;\n    }\n    const error = this.closedFailure(description);\n    if (error) return error;\n    if (job === undefined) {\n      return this.addHook(description);\n    } else if (typeof job === 'function') {\n      return this.addFunction(job, description);\n    } else if (job instanceof BackgroundProcessManager) {\n      this.addManager(job, description);\n    } else {\n      throw new Error('If `job` is provided, it must be an Observable, Function, or BackgroundProcessManager.');\n    }\n  }\n  /**\n   * Adds a **cleaner** function that doesn't immediately get executed.\n   * Instead, the caller gets a **terminate** function back. The *cleaner* is\n   * invoked only once the mananger *closes* or the returned **terminate**\n   * function is called.\n   *\n   * @param clean The cleanup function.\n   * @param description Optional description to help identify pending jobs.\n   * @returns A terminate function.\n   */\n  addCleaner(clean, description) {\n    const {\n      resolve,\n      onTerminate\n    } = this.addHook(description);\n    const proxy = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* () {\n        yield clean();\n        resolve();\n      });\n      return function proxy() {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    onTerminate.then(proxy);\n    return proxy;\n  }\n  addFunction(job, description) {\n    // the function we call when we want to try to terminate this job.\n    let terminate;\n    // the promise the job can opt into listening to for termination.\n    const onTerminate = new Promise(resolve => {\n      terminate = resolve;\n    });\n    // finally! start the job.\n    const jobResult = job(onTerminate);\n    // depending on what the job gives back, register the result\n    // so we can monitor for completion.\n    if (typeof jobResult?.then === 'function') {\n      this.registerPromise(jobResult, terminate, description);\n    }\n    // At the end of the day, or you know, method call, it doesn't matter\n    // what the return value is at all; we just pass it through to the\n    // caller.\n    return jobResult;\n  }\n  addManager(manager, description) {\n    this.addCleaner(/*#__PURE__*/_asyncToGenerator(function* () {\n      return manager.close();\n    }), description);\n  }\n  /**\n   * Creates and registers a fabricated job for processes that need to operate\n   * with callbacks/hooks. The returned `resolve` and `reject`\n   * functions can be used to signal the job is done successfully or not.\n   * The returned `onTerminate` is a promise that will resolve when the\n   * manager is requesting the termination of the job.\n   *\n   * @param description Optional description to help identify pending jobs.\n   * @returns `{ resolve, reject, onTerminate }`\n   */\n  addHook(description) {\n    // the resolve/reject functions we'll provide to the caller to signal\n    // the state of the job.\n    let promiseResolve;\n    let promiseReject;\n    // the underlying promise we'll use to manage it, pretty much like\n    // any other promise.\n    const promise = new Promise((resolve, reject) => {\n      promiseResolve = resolve;\n      promiseReject = reject;\n    });\n    // the function we call when we want to try to terminate this job.\n    let terminate;\n    // the promise the job can opt into listening to for termination.\n    const onTerminate = new Promise(resolve => {\n      terminate = resolve;\n    });\n    this.registerPromise(promise, terminate, description);\n    return {\n      resolve: promiseResolve,\n      reject: promiseReject,\n      onTerminate\n    };\n  }\n  /**\n   * Adds a Promise based job to the list of jobs for monitoring and listens\n   * for either a success or failure, upon which the job is considered \"done\"\n   * and removed from the registry.\n   *\n   * @param promise A promise that is on its way to being returned to a\n   * caller, which needs to be tracked as a background job.\n   * @param terminate The termination function to register, which can be\n   * invoked to request the job stop.\n   * @param description Optional description to help identify pending jobs.\n   */\n  registerPromise(promise, terminate, description) {\n    const jobEntry = {\n      promise,\n      terminate,\n      description\n    };\n    this.jobs.add(jobEntry);\n    // in all of my testing, it is safe to multi-subscribe to a promise.\n    // so, rather than create another layer of promising, we're just going\n    // to hook into the promise we already have, and when it's done\n    // (successfully or not), we no longer need to wait for it upon close.\n    //\n    // sorry this is a bit hand-wavy:\n    //\n    // i believe we use `.then` and `.catch` instead of `.finally` because\n    // `.finally` is invoked in a different order in the sequence, and this\n    // breaks assumptions throughout and causes failures.\n    promise.then(() => {\n      this.jobs.delete(jobEntry);\n    }).catch(() => {\n      this.jobs.delete(jobEntry);\n    });\n  }\n  /**\n   * The number of jobs being waited on.\n   *\n   * We don't use this for anything. It's just informational for the caller,\n   * and can be used in logging and testing.\n   *\n   * @returns the number of jobs.\n   */\n  get length() {\n    return this.jobs.size;\n  }\n  /**\n   * The execution state of the manager. One of:\n   *\n   * 1. \"Open\" -> Accepting new jobs\n   * 1. \"Closing\" -> Not accepting new work. Waiting for jobs to complete.\n   * 1. \"Closed\" -> Not accepting new work. All submitted jobs are complete.\n   */\n  get state() {\n    return this._state;\n  }\n  /**\n   * The registered `description` of all still-pending jobs.\n   *\n   * @returns descriptions as an array.\n   */\n  get pending() {\n    return Array.from(this.jobs).map(job => job.description);\n  }\n  /**\n   * Whether the manager is accepting new jobs.\n   */\n  get isOpen() {\n    return this._state === BackgroundProcessManagerState.Open;\n  }\n  /**\n   * Whether the manager is rejecting new work, but still waiting for\n   * submitted work to complete.\n   */\n  get isClosing() {\n    return this._state === BackgroundProcessManagerState.Closing;\n  }\n  /**\n   * Whether the manager is rejecting work and done waiting for submitted\n   * work to complete.\n   */\n  get isClosed() {\n    return this._state === BackgroundProcessManagerState.Closed;\n  }\n  closedFailure(description) {\n    if (!this.isOpen) {\n      return Promise.reject(new BackgroundManagerNotOpenError([`The manager is ${this.state}.`, `You tried to add \"${description}\".`, `Pending jobs: [\\n${this.pending.map(t => '    ' + t).join(',\\n')}\\n]`].join('\\n')));\n    }\n  }\n  /**\n   * Signals jobs to stop (for those that accept interruptions) and waits\n   * for confirmation that jobs have stopped.\n   *\n   * This immediately puts the manager into a closing state and just begins\n   * to reject new work. After all work in the manager is complete, the\n   * manager goes into a `Completed` state and `close()` returns.\n   *\n   * This call is idempotent.\n   *\n   * If the manager is already closing or closed, `finalCleaup` is not executed.\n   *\n   * @param onClosed\n   * @returns The settled results of each still-running job's promise. If the\n   * manager is already closed, this will contain the results as of when the\n   * manager's `close()` was called in an `Open` state.\n   */\n  close() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.isOpen) {\n        _this._state = BackgroundProcessManagerState.Closing;\n        for (const job of Array.from(_this.jobs)) {\n          try {\n            job.terminate();\n          } catch (error) {\n            // Due to potential races with a job's natural completion, it's\n            // reasonable to expect the termination call to fail. Hence,\n            // not logging as an error.\n            // eslint-disable-next-line no-console\n            console.warn(`Failed to send termination signal to job. Error: ${error.message}`, job);\n          }\n        }\n        // Use `allSettled()` because we want to wait for all to finish. We do\n        // not want to stop waiting if there is a failure.\n        _this._closingPromise = Promise.allSettled(Array.from(_this.jobs).map(j => j.promise));\n        yield _this._closingPromise;\n        _this._state = BackgroundProcessManagerState.Closed;\n      }\n      return _this._closingPromise;\n    })();\n  }\n  /**\n   * Signals the manager to start accepting work (again) and returns once\n   * the manager is ready to do so.\n   *\n   * If the state is already `Open`, this call is a no-op.\n   *\n   * If the state is `Closed`, this call simply updates state and returns.\n   *\n   * If the state is `Closing`, this call waits for completion before it\n   * updates the state and returns.\n   */\n  open() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (_this2.isClosing) {\n        yield _this2.close();\n      }\n      _this2._state = BackgroundProcessManagerState.Open;\n    })();\n  }\n}\nexport { BackgroundProcessManager };", "map": {"version": 3, "names": ["BackgroundManagerNotOpenError", "BackgroundProcessManagerState", "BackgroundProcessManager", "constructor", "_state", "Open", "jobs", "Set", "add", "jobOrDescription", "optionalDescription", "job", "description", "undefined", "error", "closedFailure", "addHook", "addFunction", "addManager", "Error", "add<PERSON><PERSON><PERSON>", "clean", "resolve", "onTerminate", "proxy", "_ref", "_asyncToGenerator", "apply", "arguments", "then", "terminate", "Promise", "jobResult", "registerPromise", "manager", "close", "promiseResolve", "promiseReject", "promise", "reject", "jobEntry", "delete", "catch", "length", "size", "state", "pending", "Array", "from", "map", "isOpen", "isClosing", "Closing", "isClosed", "Closed", "t", "join", "_this", "console", "warn", "message", "_closingPromise", "allSettled", "j", "open", "_this2"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/BackgroundProcessManager/BackgroundProcessManager.mjs"], "sourcesContent": ["import { BackgroundManagerNotOpenError } from './BackgroundManagerNotOpenError.mjs';\nimport { BackgroundProcessManagerState } from './types.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @private For internal Amplify use.\n *\n * Creates a new scope for promises, observables, and other types of work or\n * processes that may be running in the background. This manager provides\n * an singular entrypoint to request termination and await completion.\n *\n * As work completes on its own prior to close, the manager removes them\n * from the registry to avoid holding references to completed jobs.\n */\nclass BackgroundProcessManager {\n    constructor() {\n        /**\n         * A string indicating whether the manager is accepting new work (\"Open\"),\n         * waiting for work to complete (\"Closing\"), or fully done with all\n         * submitted work and *not* accepting new jobs (\"Closed\").\n         */\n        this._state = BackgroundProcessManagerState.Open;\n        /**\n         * The list of outstanding jobs we'll need to wait for upon `close()`\n         */\n        this.jobs = new Set();\n    }\n    add(jobOrDescription, optionalDescription) {\n        let job;\n        let description;\n        if (typeof jobOrDescription === 'string') {\n            job = undefined;\n            description = jobOrDescription;\n        }\n        else {\n            job = jobOrDescription;\n            description = optionalDescription;\n        }\n        const error = this.closedFailure(description);\n        if (error)\n            return error;\n        if (job === undefined) {\n            return this.addHook(description);\n        }\n        else if (typeof job === 'function') {\n            return this.addFunction(job, description);\n        }\n        else if (job instanceof BackgroundProcessManager) {\n            this.addManager(job, description);\n        }\n        else {\n            throw new Error('If `job` is provided, it must be an Observable, Function, or BackgroundProcessManager.');\n        }\n    }\n    /**\n     * Adds a **cleaner** function that doesn't immediately get executed.\n     * Instead, the caller gets a **terminate** function back. The *cleaner* is\n     * invoked only once the mananger *closes* or the returned **terminate**\n     * function is called.\n     *\n     * @param clean The cleanup function.\n     * @param description Optional description to help identify pending jobs.\n     * @returns A terminate function.\n     */\n    addCleaner(clean, description) {\n        const { resolve, onTerminate } = this.addHook(description);\n        const proxy = async () => {\n            await clean();\n            resolve();\n        };\n        onTerminate.then(proxy);\n        return proxy;\n    }\n    addFunction(job, description) {\n        // the function we call when we want to try to terminate this job.\n        let terminate;\n        // the promise the job can opt into listening to for termination.\n        const onTerminate = new Promise(resolve => {\n            terminate = resolve;\n        });\n        // finally! start the job.\n        const jobResult = job(onTerminate);\n        // depending on what the job gives back, register the result\n        // so we can monitor for completion.\n        if (typeof jobResult?.then === 'function') {\n            this.registerPromise(jobResult, terminate, description);\n        }\n        // At the end of the day, or you know, method call, it doesn't matter\n        // what the return value is at all; we just pass it through to the\n        // caller.\n        return jobResult;\n    }\n    addManager(manager, description) {\n        this.addCleaner(async () => manager.close(), description);\n    }\n    /**\n     * Creates and registers a fabricated job for processes that need to operate\n     * with callbacks/hooks. The returned `resolve` and `reject`\n     * functions can be used to signal the job is done successfully or not.\n     * The returned `onTerminate` is a promise that will resolve when the\n     * manager is requesting the termination of the job.\n     *\n     * @param description Optional description to help identify pending jobs.\n     * @returns `{ resolve, reject, onTerminate }`\n     */\n    addHook(description) {\n        // the resolve/reject functions we'll provide to the caller to signal\n        // the state of the job.\n        let promiseResolve;\n        let promiseReject;\n        // the underlying promise we'll use to manage it, pretty much like\n        // any other promise.\n        const promise = new Promise((resolve, reject) => {\n            promiseResolve = resolve;\n            promiseReject = reject;\n        });\n        // the function we call when we want to try to terminate this job.\n        let terminate;\n        // the promise the job can opt into listening to for termination.\n        const onTerminate = new Promise(resolve => {\n            terminate = resolve;\n        });\n        this.registerPromise(promise, terminate, description);\n        return {\n            resolve: promiseResolve,\n            reject: promiseReject,\n            onTerminate,\n        };\n    }\n    /**\n     * Adds a Promise based job to the list of jobs for monitoring and listens\n     * for either a success or failure, upon which the job is considered \"done\"\n     * and removed from the registry.\n     *\n     * @param promise A promise that is on its way to being returned to a\n     * caller, which needs to be tracked as a background job.\n     * @param terminate The termination function to register, which can be\n     * invoked to request the job stop.\n     * @param description Optional description to help identify pending jobs.\n     */\n    registerPromise(promise, terminate, description) {\n        const jobEntry = { promise, terminate, description };\n        this.jobs.add(jobEntry);\n        // in all of my testing, it is safe to multi-subscribe to a promise.\n        // so, rather than create another layer of promising, we're just going\n        // to hook into the promise we already have, and when it's done\n        // (successfully or not), we no longer need to wait for it upon close.\n        //\n        // sorry this is a bit hand-wavy:\n        //\n        // i believe we use `.then` and `.catch` instead of `.finally` because\n        // `.finally` is invoked in a different order in the sequence, and this\n        // breaks assumptions throughout and causes failures.\n        promise\n            .then(() => {\n            this.jobs.delete(jobEntry);\n        })\n            .catch(() => {\n            this.jobs.delete(jobEntry);\n        });\n    }\n    /**\n     * The number of jobs being waited on.\n     *\n     * We don't use this for anything. It's just informational for the caller,\n     * and can be used in logging and testing.\n     *\n     * @returns the number of jobs.\n     */\n    get length() {\n        return this.jobs.size;\n    }\n    /**\n     * The execution state of the manager. One of:\n     *\n     * 1. \"Open\" -> Accepting new jobs\n     * 1. \"Closing\" -> Not accepting new work. Waiting for jobs to complete.\n     * 1. \"Closed\" -> Not accepting new work. All submitted jobs are complete.\n     */\n    get state() {\n        return this._state;\n    }\n    /**\n     * The registered `description` of all still-pending jobs.\n     *\n     * @returns descriptions as an array.\n     */\n    get pending() {\n        return Array.from(this.jobs).map(job => job.description);\n    }\n    /**\n     * Whether the manager is accepting new jobs.\n     */\n    get isOpen() {\n        return this._state === BackgroundProcessManagerState.Open;\n    }\n    /**\n     * Whether the manager is rejecting new work, but still waiting for\n     * submitted work to complete.\n     */\n    get isClosing() {\n        return this._state === BackgroundProcessManagerState.Closing;\n    }\n    /**\n     * Whether the manager is rejecting work and done waiting for submitted\n     * work to complete.\n     */\n    get isClosed() {\n        return this._state === BackgroundProcessManagerState.Closed;\n    }\n    closedFailure(description) {\n        if (!this.isOpen) {\n            return Promise.reject(new BackgroundManagerNotOpenError([\n                `The manager is ${this.state}.`,\n                `You tried to add \"${description}\".`,\n                `Pending jobs: [\\n${this.pending\n                    .map(t => '    ' + t)\n                    .join(',\\n')}\\n]`,\n            ].join('\\n')));\n        }\n    }\n    /**\n     * Signals jobs to stop (for those that accept interruptions) and waits\n     * for confirmation that jobs have stopped.\n     *\n     * This immediately puts the manager into a closing state and just begins\n     * to reject new work. After all work in the manager is complete, the\n     * manager goes into a `Completed` state and `close()` returns.\n     *\n     * This call is idempotent.\n     *\n     * If the manager is already closing or closed, `finalCleaup` is not executed.\n     *\n     * @param onClosed\n     * @returns The settled results of each still-running job's promise. If the\n     * manager is already closed, this will contain the results as of when the\n     * manager's `close()` was called in an `Open` state.\n     */\n    async close() {\n        if (this.isOpen) {\n            this._state = BackgroundProcessManagerState.Closing;\n            for (const job of Array.from(this.jobs)) {\n                try {\n                    job.terminate();\n                }\n                catch (error) {\n                    // Due to potential races with a job's natural completion, it's\n                    // reasonable to expect the termination call to fail. Hence,\n                    // not logging as an error.\n                    // eslint-disable-next-line no-console\n                    console.warn(`Failed to send termination signal to job. Error: ${error.message}`, job);\n                }\n            }\n            // Use `allSettled()` because we want to wait for all to finish. We do\n            // not want to stop waiting if there is a failure.\n            this._closingPromise = Promise.allSettled(Array.from(this.jobs).map(j => j.promise));\n            await this._closingPromise;\n            this._state = BackgroundProcessManagerState.Closed;\n        }\n        return this._closingPromise;\n    }\n    /**\n     * Signals the manager to start accepting work (again) and returns once\n     * the manager is ready to do so.\n     *\n     * If the state is already `Open`, this call is a no-op.\n     *\n     * If the state is `Closed`, this call simply updates state and returns.\n     *\n     * If the state is `Closing`, this call waits for completion before it\n     * updates the state and returns.\n     */\n    async open() {\n        if (this.isClosing) {\n            await this.close();\n        }\n        this._state = BackgroundProcessManagerState.Open;\n    }\n}\n\nexport { BackgroundProcessManager };\n"], "mappings": ";AAAA,SAASA,6BAA6B,QAAQ,qCAAqC;AACnF,SAASC,6BAA6B,QAAQ,aAAa;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,CAAC;EAC3BC,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,MAAM,GAAGH,6BAA6B,CAACI,IAAI;IAChD;AACR;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,IAAIC,GAAG,CAAC,CAAC;EACzB;EACAC,GAAGA,CAACC,gBAAgB,EAAEC,mBAAmB,EAAE;IACvC,IAAIC,GAAG;IACP,IAAIC,WAAW;IACf,IAAI,OAAOH,gBAAgB,KAAK,QAAQ,EAAE;MACtCE,GAAG,GAAGE,SAAS;MACfD,WAAW,GAAGH,gBAAgB;IAClC,CAAC,MACI;MACDE,GAAG,GAAGF,gBAAgB;MACtBG,WAAW,GAAGF,mBAAmB;IACrC;IACA,MAAMI,KAAK,GAAG,IAAI,CAACC,aAAa,CAACH,WAAW,CAAC;IAC7C,IAAIE,KAAK,EACL,OAAOA,KAAK;IAChB,IAAIH,GAAG,KAAKE,SAAS,EAAE;MACnB,OAAO,IAAI,CAACG,OAAO,CAACJ,WAAW,CAAC;IACpC,CAAC,MACI,IAAI,OAAOD,GAAG,KAAK,UAAU,EAAE;MAChC,OAAO,IAAI,CAACM,WAAW,CAACN,GAAG,EAAEC,WAAW,CAAC;IAC7C,CAAC,MACI,IAAID,GAAG,YAAYT,wBAAwB,EAAE;MAC9C,IAAI,CAACgB,UAAU,CAACP,GAAG,EAAEC,WAAW,CAAC;IACrC,CAAC,MACI;MACD,MAAM,IAAIO,KAAK,CAAC,wFAAwF,CAAC;IAC7G;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,UAAUA,CAACC,KAAK,EAAET,WAAW,EAAE;IAC3B,MAAM;MAAEU,OAAO;MAAEC;IAAY,CAAC,GAAG,IAAI,CAACP,OAAO,CAACJ,WAAW,CAAC;IAC1D,MAAMY,KAAK;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;QACtB,MAAML,KAAK,CAAC,CAAC;QACbC,OAAO,CAAC,CAAC;MACb,CAAC;MAAA,gBAHKE,KAAKA,CAAA;QAAA,OAAAC,IAAA,CAAAE,KAAA,OAAAC,SAAA;MAAA;IAAA,GAGV;IACDL,WAAW,CAACM,IAAI,CAACL,KAAK,CAAC;IACvB,OAAOA,KAAK;EAChB;EACAP,WAAWA,CAACN,GAAG,EAAEC,WAAW,EAAE;IAC1B;IACA,IAAIkB,SAAS;IACb;IACA,MAAMP,WAAW,GAAG,IAAIQ,OAAO,CAACT,OAAO,IAAI;MACvCQ,SAAS,GAAGR,OAAO;IACvB,CAAC,CAAC;IACF;IACA,MAAMU,SAAS,GAAGrB,GAAG,CAACY,WAAW,CAAC;IAClC;IACA;IACA,IAAI,OAAOS,SAAS,EAAEH,IAAI,KAAK,UAAU,EAAE;MACvC,IAAI,CAACI,eAAe,CAACD,SAAS,EAAEF,SAAS,EAAElB,WAAW,CAAC;IAC3D;IACA;IACA;IACA;IACA,OAAOoB,SAAS;EACpB;EACAd,UAAUA,CAACgB,OAAO,EAAEtB,WAAW,EAAE;IAC7B,IAAI,CAACQ,UAAU,cAAAM,iBAAA,CAAC;MAAA,OAAYQ,OAAO,CAACC,KAAK,CAAC,CAAC;IAAA,IAAEvB,WAAW,CAAC;EAC7D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACII,OAAOA,CAACJ,WAAW,EAAE;IACjB;IACA;IACA,IAAIwB,cAAc;IAClB,IAAIC,aAAa;IACjB;IACA;IACA,MAAMC,OAAO,GAAG,IAAIP,OAAO,CAAC,CAACT,OAAO,EAAEiB,MAAM,KAAK;MAC7CH,cAAc,GAAGd,OAAO;MACxBe,aAAa,GAAGE,MAAM;IAC1B,CAAC,CAAC;IACF;IACA,IAAIT,SAAS;IACb;IACA,MAAMP,WAAW,GAAG,IAAIQ,OAAO,CAACT,OAAO,IAAI;MACvCQ,SAAS,GAAGR,OAAO;IACvB,CAAC,CAAC;IACF,IAAI,CAACW,eAAe,CAACK,OAAO,EAAER,SAAS,EAAElB,WAAW,CAAC;IACrD,OAAO;MACHU,OAAO,EAAEc,cAAc;MACvBG,MAAM,EAAEF,aAAa;MACrBd;IACJ,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIU,eAAeA,CAACK,OAAO,EAAER,SAAS,EAAElB,WAAW,EAAE;IAC7C,MAAM4B,QAAQ,GAAG;MAAEF,OAAO;MAAER,SAAS;MAAElB;IAAY,CAAC;IACpD,IAAI,CAACN,IAAI,CAACE,GAAG,CAACgC,QAAQ,CAAC;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAF,OAAO,CACFT,IAAI,CAAC,MAAM;MACZ,IAAI,CAACvB,IAAI,CAACmC,MAAM,CAACD,QAAQ,CAAC;IAC9B,CAAC,CAAC,CACGE,KAAK,CAAC,MAAM;MACb,IAAI,CAACpC,IAAI,CAACmC,MAAM,CAACD,QAAQ,CAAC;IAC9B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIG,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACrC,IAAI,CAACsC,IAAI;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACzC,MAAM;EACtB;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI0C,OAAOA,CAAA,EAAG;IACV,OAAOC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1C,IAAI,CAAC,CAAC2C,GAAG,CAACtC,GAAG,IAAIA,GAAG,CAACC,WAAW,CAAC;EAC5D;EACA;AACJ;AACA;EACI,IAAIsC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC9C,MAAM,KAAKH,6BAA6B,CAACI,IAAI;EAC7D;EACA;AACJ;AACA;AACA;EACI,IAAI8C,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC/C,MAAM,KAAKH,6BAA6B,CAACmD,OAAO;EAChE;EACA;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACjD,MAAM,KAAKH,6BAA6B,CAACqD,MAAM;EAC/D;EACAvC,aAAaA,CAACH,WAAW,EAAE;IACvB,IAAI,CAAC,IAAI,CAACsC,MAAM,EAAE;MACd,OAAOnB,OAAO,CAACQ,MAAM,CAAC,IAAIvC,6BAA6B,CAAC,CACpD,kBAAkB,IAAI,CAAC6C,KAAK,GAAG,EAC/B,qBAAqBjC,WAAW,IAAI,EACpC,oBAAoB,IAAI,CAACkC,OAAO,CAC3BG,GAAG,CAACM,CAAC,IAAI,MAAM,GAAGA,CAAC,CAAC,CACpBC,IAAI,CAAC,KAAK,CAAC,KAAK,CACxB,CAACA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAClB;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUrB,KAAKA,CAAA,EAAG;IAAA,IAAAsB,KAAA;IAAA,OAAA/B,iBAAA;MACV,IAAI+B,KAAI,CAACP,MAAM,EAAE;QACbO,KAAI,CAACrD,MAAM,GAAGH,6BAA6B,CAACmD,OAAO;QACnD,KAAK,MAAMzC,GAAG,IAAIoC,KAAK,CAACC,IAAI,CAACS,KAAI,CAACnD,IAAI,CAAC,EAAE;UACrC,IAAI;YACAK,GAAG,CAACmB,SAAS,CAAC,CAAC;UACnB,CAAC,CACD,OAAOhB,KAAK,EAAE;YACV;YACA;YACA;YACA;YACA4C,OAAO,CAACC,IAAI,CAAC,oDAAoD7C,KAAK,CAAC8C,OAAO,EAAE,EAAEjD,GAAG,CAAC;UAC1F;QACJ;QACA;QACA;QACA8C,KAAI,CAACI,eAAe,GAAG9B,OAAO,CAAC+B,UAAU,CAACf,KAAK,CAACC,IAAI,CAACS,KAAI,CAACnD,IAAI,CAAC,CAAC2C,GAAG,CAACc,CAAC,IAAIA,CAAC,CAACzB,OAAO,CAAC,CAAC;QACpF,MAAMmB,KAAI,CAACI,eAAe;QAC1BJ,KAAI,CAACrD,MAAM,GAAGH,6BAA6B,CAACqD,MAAM;MACtD;MACA,OAAOG,KAAI,CAACI,eAAe;IAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUG,IAAIA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAvC,iBAAA;MACT,IAAIuC,MAAI,CAACd,SAAS,EAAE;QAChB,MAAMc,MAAI,CAAC9B,KAAK,CAAC,CAAC;MACtB;MACA8B,MAAI,CAAC7D,MAAM,GAAGH,6BAA6B,CAACI,IAAI;IAAC;EACrD;AACJ;AAEA,SAASH,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}