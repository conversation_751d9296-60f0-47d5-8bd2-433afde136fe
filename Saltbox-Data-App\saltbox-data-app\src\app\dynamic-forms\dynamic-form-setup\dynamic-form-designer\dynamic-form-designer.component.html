<form #form="ngForm">
  <div *ngIf="dynamicForm" class="grid form-designer">
    <div class="sm:col-4 col-12 drawer flex flex-column ml-2">
      <div class="admin-step-nav">
        <p-stepper [(activeStep)]="activeIndex">
          <p-stepperPanel header="General Settings"></p-stepperPanel>
          <p-stepperPanel header="Form Elements"></p-stepperPanel>
          <p-stepperPanel header="Advanced Settings"></p-stepperPanel>
        </p-stepper>
      </div>
      <!--General Settings Wizard-->
      <app-dynamic-form-general-settings [(dynamicForm)]="dynamicForm" [ngClass]="{'hidden': activeIndex !== 0}"
        [projectId]="projectId" [projectVersionId]="projectVersionId"
        [disabled]="projectIsLocked"></app-dynamic-form-general-settings>

      <!--Form Elements Wizard-->
      <app-dynamic-form-form-elements [(dynamicForm)]="dynamicForm" [(draggedField)]="draggedField"
        [ngClass]="{'hidden': activeIndex !== 1}" [disabled]="projectIsLocked"></app-dynamic-form-form-elements>

      <!--Advanced Settings Wizard-->
      <app-dynamic-form-advanced-settings [(dynamicForm)]="dynamicForm" [ngClass]="{'hidden': activeIndex !== 2}"
        [projectId]="projectId" [projectVersionId]="projectVersionId"
        [disabled]="projectIsLocked"></app-dynamic-form-advanced-settings>

      <div class="admin-btns my-2">
        <p-button [disabled]="projectIsLocked" [outlined]="true" class=" m-0" label="Next Step" *ngIf="activeIndex < 2"
          (onClick)="nextStep()"></p-button>
        <p-button [disabled]="projectIsLocked" class="m-0" label="Save & Preview" *ngIf="activeIndex === 2"
          (onClick)="saveFormAndPreview()"></p-button>
        <p-button [disabled]="projectIsLocked" [outlined]="true" class=" m-0" label="Previous Step"
          *ngIf="activeIndex > 0" (onClick)="previousStep()"></p-button>
      </div>
    </div>

    <!--Layout preview-->
    <app-form-layout-preview [(dynamicForm)]="dynamicForm" class="col form-layout" [(draggedField)]="draggedField"
      [disabled]="projectIsLocked"></app-form-layout-preview>
  </div>
</form>