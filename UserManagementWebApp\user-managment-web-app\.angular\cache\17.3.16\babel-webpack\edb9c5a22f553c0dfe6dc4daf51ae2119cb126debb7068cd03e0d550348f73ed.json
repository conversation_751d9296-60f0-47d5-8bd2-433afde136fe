{"ast": null, "code": "const highlightmatch = {\n  highlighted: {\n    fontWeight: {\n      value: '{fontWeights.bold}'\n    }\n  }\n};\nexport { highlightmatch };", "map": {"version": 3, "names": ["highlightmatch", "highlighted", "fontWeight", "value"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/highlightMatch.mjs"], "sourcesContent": ["const highlightmatch = {\n    highlighted: {\n        fontWeight: { value: '{fontWeights.bold}' },\n    },\n};\n\nexport { highlightmatch };\n"], "mappings": "AAAA,MAAMA,cAAc,GAAG;EACnBC,WAAW,EAAE;IACTC,UAAU,EAAE;MAAEC,KAAK,EAAE;IAAqB;EAC9C;AACJ,CAAC;AAED,SAASH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}