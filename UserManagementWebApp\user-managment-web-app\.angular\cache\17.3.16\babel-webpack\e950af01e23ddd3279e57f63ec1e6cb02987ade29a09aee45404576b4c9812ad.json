{"ast": null, "code": "import { ConsoleLogger } from 'aws-amplify/utils';\nconst getLogger = category => new ConsoleLogger(`AmplifyUI:${category}`);\nexport { getLogger };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "category"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/helpers/utils.mjs"], "sourcesContent": ["import { ConsoleLogger } from 'aws-amplify/utils';\n\nconst getLogger = (category) => new ConsoleLogger(`AmplifyUI:${category}`);\n\nexport { getLogger };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,mBAAmB;AAEjD,MAAMC,SAAS,GAAIC,QAAQ,IAAK,IAAIF,aAAa,CAAC,aAAaE,QAAQ,EAAE,CAAC;AAE1E,SAASD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}