{"ast": null, "code": "import { SHORT_TO_HEX } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Converts a Uint8Array of binary data to a hexadecimal encoded string.\n *\n * @param bytes The binary data to encode\n */\nconst getHexFromBytes = bytes => {\n  let out = '';\n  for (let i = 0; i < bytes.byteLength; i++) {\n    out += SHORT_TO_HEX[bytes[i]];\n  }\n  return out;\n};\nexport { getHexFromBytes };", "map": {"version": 3, "names": ["SHORT_TO_HEX", "getHexFromBytes", "bytes", "out", "i", "byteLength"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/srp/getHexFromBytes.mjs"], "sourcesContent": ["import { SHORT_TO_HEX } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Converts a Uint8Array of binary data to a hexadecimal encoded string.\n *\n * @param bytes The binary data to encode\n */\nconst getHexFromBytes = (bytes) => {\n    let out = '';\n    for (let i = 0; i < bytes.byteLength; i++) {\n        out += SHORT_TO_HEX[bytes[i]];\n    }\n    return out;\n};\n\nexport { getHexFromBytes };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAIC,KAAK,IAAK;EAC/B,IAAIC,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,UAAU,EAAED,CAAC,EAAE,EAAE;IACvCD,GAAG,IAAIH,YAAY,CAACE,KAAK,CAACE,CAAC,CAAC,CAAC;EACjC;EACA,OAAOD,GAAG;AACd,CAAC;AAED,SAASF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}