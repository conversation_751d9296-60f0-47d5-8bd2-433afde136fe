{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// via https://github.com/aws/aws-sdk-js-v3/blob/ab0e7be36e7e7f8a0c04834357aaad643c7912c3/packages/service-error-classification/src/constants.ts#L8\nconst CLOCK_SKEW_ERROR_CODES = ['AuthFailure', 'InvalidSignatureException', 'RequestExpired', 'RequestInTheFuture', 'RequestTimeTooSkewed', 'SignatureDoesNotMatch', 'BadRequestException' // API Gateway\n];\n/**\n * Given an error code, returns true if it is related to a clock skew error.\n *\n * @param errorCode String representation of some error.\n * @returns True if given error is present in `CLOCK_SKEW_ERROR_CODES`, false otherwise.\n *\n * @internal\n */\nconst isClockSkewError = errorCode => !!errorCode && CLOCK_SKEW_ERROR_CODES.includes(errorCode);\nexport { isClockSkewError };", "map": {"version": 3, "names": ["CLOCK_SKEW_ERROR_CODES", "isClockSkewError", "errorCode", "includes"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/retry/isClockSkewError.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// via https://github.com/aws/aws-sdk-js-v3/blob/ab0e7be36e7e7f8a0c04834357aaad643c7912c3/packages/service-error-classification/src/constants.ts#L8\nconst CLOCK_SKEW_ERROR_CODES = [\n    'AuthFailure',\n    'InvalidSignatureException',\n    'RequestExpired',\n    'RequestInTheFuture',\n    'RequestTimeTooSkewed',\n    'SignatureDoesNotMatch',\n    'BadRequestException', // API Gateway\n];\n/**\n * Given an error code, returns true if it is related to a clock skew error.\n *\n * @param errorCode String representation of some error.\n * @returns True if given error is present in `CLOCK_SKEW_ERROR_CODES`, false otherwise.\n *\n * @internal\n */\nconst isClockSkewError = (errorCode) => !!errorCode && CLOCK_SKEW_ERROR_CODES.includes(errorCode);\n\nexport { isClockSkewError };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,sBAAsB,GAAG,CAC3B,aAAa,EACb,2BAA2B,EAC3B,gBAAgB,EAChB,oBAAoB,EACpB,sBAAsB,EACtB,uBAAuB,EACvB,qBAAqB,CAAE;AAAA,CAC1B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAIC,SAAS,IAAK,CAAC,CAACA,SAAS,IAAIF,sBAAsB,CAACG,QAAQ,CAACD,SAAS,CAAC;AAEjG,SAASD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}