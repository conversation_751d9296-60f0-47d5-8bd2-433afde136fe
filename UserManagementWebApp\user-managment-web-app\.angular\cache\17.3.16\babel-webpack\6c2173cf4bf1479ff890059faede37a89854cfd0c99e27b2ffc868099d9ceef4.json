{"ast": null, "code": "// This file replaces `index.js` in bundlers like webpack or Rollup,\n// according to `browser` config in `package.json`.\n\nimport { urlAlphabet } from './url-alphabet/index.js';\nlet random = bytes => crypto.getRandomValues(new Uint8Array(bytes));\nlet customRandom = (alphabet, defaultSize, getRandom) => {\n  // First, a bitmask is necessary to generate the ID. The bitmask makes bytes\n  // values closer to the alphabet size. The bitmask calculates the closest\n  // `2^31 - 1` number, which exceeds the alphabet size.\n  // For example, the bitmask for the alphabet size 30 is 31 (00011111).\n  // `Math.clz32` is not used, because it is not available in browsers.\n  let mask = (2 << Math.log(alphabet.length - 1) / Math.LN2) - 1;\n  // Though, the bitmask solution is not perfect since the bytes exceeding\n  // the alphabet size are refused. Therefore, to reliably generate the ID,\n  // the random bytes redundancy has to be satisfied.\n\n  // Note: every hardware random generator call is performance expensive,\n  // because the system call for entropy collection takes a lot of time.\n  // So, to avoid additional system calls, extra bytes are requested in advance.\n\n  // Next, a step determines how many random bytes to generate.\n  // The number of random bytes gets decided upon the ID size, mask,\n  // alphabet size, and magic number 1.6 (using 1.6 peaks at performance\n  // according to benchmarks).\n\n  // `-~f => Math.ceil(f)` if f is a float\n  // `-~i => i + 1` if i is an integer\n  let step = -~(1.6 * mask * defaultSize / alphabet.length);\n  return (size = defaultSize) => {\n    let id = '';\n    while (true) {\n      let bytes = getRandom(step);\n      // A compact alternative for `for (var i = 0; i < step; i++)`.\n      let j = step | 0;\n      while (j--) {\n        // Adding `|| ''` refuses a random byte that exceeds the alphabet size.\n        id += alphabet[bytes[j] & mask] || '';\n        if (id.length === size) return id;\n      }\n    }\n  };\n};\nlet customAlphabet = (alphabet, size = 21) => customRandom(alphabet, size, random);\nlet nanoid = (size = 21) => crypto.getRandomValues(new Uint8Array(size)).reduce((id, byte) => {\n  // It is incorrect to use bytes exceeding the alphabet size.\n  // The following mask reduces the random byte in the 0-255 value\n  // range to the 0-63 value range. Therefore, adding hacks, such\n  // as empty string fallback or magic numbers, is unneccessary because\n  // the bitmask trims bytes down to the alphabet size.\n  byte &= 63;\n  if (byte < 36) {\n    // `0-9a-z`\n    id += byte.toString(36);\n  } else if (byte < 62) {\n    // `A-Z`\n    id += (byte - 26).toString(36).toUpperCase();\n  } else if (byte > 62) {\n    id += '-';\n  } else {\n    id += '_';\n  }\n  return id;\n}, '');\nexport { nanoid, customAlphabet, customRandom, urlAlphabet, random };", "map": {"version": 3, "names": ["url<PERSON>l<PERSON><PERSON>", "random", "bytes", "crypto", "getRandomValues", "Uint8Array", "customRandom", "alphabet", "defaultSize", "getRandom", "mask", "Math", "log", "length", "LN2", "step", "size", "id", "j", "customAlphabet", "nanoid", "reduce", "byte", "toString", "toUpperCase"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/nanoid/index.browser.js"], "sourcesContent": ["// This file replaces `index.js` in bundlers like webpack or Rollup,\n// according to `browser` config in `package.json`.\n\nimport { url<PERSON>lphabet } from './url-alphabet/index.js'\n\nlet random = bytes => crypto.getRandomValues(new Uint8Array(bytes))\n\nlet customRandom = (alphabet, defaultSize, getRandom) => {\n  // First, a bitmask is necessary to generate the ID. The bitmask makes bytes\n  // values closer to the alphabet size. The bitmask calculates the closest\n  // `2^31 - 1` number, which exceeds the alphabet size.\n  // For example, the bitmask for the alphabet size 30 is 31 (00011111).\n  // `Math.clz32` is not used, because it is not available in browsers.\n  let mask = (2 << (Math.log(alphabet.length - 1) / Math.LN2)) - 1\n  // Though, the bitmask solution is not perfect since the bytes exceeding\n  // the alphabet size are refused. Therefore, to reliably generate the ID,\n  // the random bytes redundancy has to be satisfied.\n\n  // Note: every hardware random generator call is performance expensive,\n  // because the system call for entropy collection takes a lot of time.\n  // So, to avoid additional system calls, extra bytes are requested in advance.\n\n  // Next, a step determines how many random bytes to generate.\n  // The number of random bytes gets decided upon the ID size, mask,\n  // alphabet size, and magic number 1.6 (using 1.6 peaks at performance\n  // according to benchmarks).\n\n  // `-~f => Math.ceil(f)` if f is a float\n  // `-~i => i + 1` if i is an integer\n  let step = -~((1.6 * mask * defaultSize) / alphabet.length)\n\n  return (size = defaultSize) => {\n    let id = ''\n    while (true) {\n      let bytes = getRandom(step)\n      // A compact alternative for `for (var i = 0; i < step; i++)`.\n      let j = step | 0\n      while (j--) {\n        // Adding `|| ''` refuses a random byte that exceeds the alphabet size.\n        id += alphabet[bytes[j] & mask] || ''\n        if (id.length === size) return id\n      }\n    }\n  }\n}\n\nlet customAlphabet = (alphabet, size = 21) =>\n  customRandom(alphabet, size, random)\n\nlet nanoid = (size = 21) =>\n  crypto.getRandomValues(new Uint8Array(size)).reduce((id, byte) => {\n    // It is incorrect to use bytes exceeding the alphabet size.\n    // The following mask reduces the random byte in the 0-255 value\n    // range to the 0-63 value range. Therefore, adding hacks, such\n    // as empty string fallback or magic numbers, is unneccessary because\n    // the bitmask trims bytes down to the alphabet size.\n    byte &= 63\n    if (byte < 36) {\n      // `0-9a-z`\n      id += byte.toString(36)\n    } else if (byte < 62) {\n      // `A-Z`\n      id += (byte - 26).toString(36).toUpperCase()\n    } else if (byte > 62) {\n      id += '-'\n    } else {\n      id += '_'\n    }\n    return id\n  }, '')\n\nexport { nanoid, customAlphabet, customRandom, urlAlphabet, random }\n"], "mappings": "AAAA;AACA;;AAEA,SAASA,WAAW,QAAQ,yBAAyB;AAErD,IAAIC,MAAM,GAAGC,KAAK,IAAIC,MAAM,CAACC,eAAe,CAAC,IAAIC,UAAU,CAACH,KAAK,CAAC,CAAC;AAEnE,IAAII,YAAY,GAAGA,CAACC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,KAAK;EACvD;EACA;EACA;EACA;EACA;EACA,IAAIC,IAAI,GAAG,CAAC,CAAC,IAAKC,IAAI,CAACC,GAAG,CAACL,QAAQ,CAACM,MAAM,GAAG,CAAC,CAAC,GAAGF,IAAI,CAACG,GAAI,IAAI,CAAC;EAChE;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;EACA;EACA,IAAIC,IAAI,GAAG,CAAC,EAAG,GAAG,GAAGL,IAAI,GAAGF,WAAW,GAAID,QAAQ,CAACM,MAAM,CAAC;EAE3D,OAAO,CAACG,IAAI,GAAGR,WAAW,KAAK;IAC7B,IAAIS,EAAE,GAAG,EAAE;IACX,OAAO,IAAI,EAAE;MACX,IAAIf,KAAK,GAAGO,SAAS,CAACM,IAAI,CAAC;MAC3B;MACA,IAAIG,CAAC,GAAGH,IAAI,GAAG,CAAC;MAChB,OAAOG,CAAC,EAAE,EAAE;QACV;QACAD,EAAE,IAAIV,QAAQ,CAACL,KAAK,CAACgB,CAAC,CAAC,GAAGR,IAAI,CAAC,IAAI,EAAE;QACrC,IAAIO,EAAE,CAACJ,MAAM,KAAKG,IAAI,EAAE,OAAOC,EAAE;MACnC;IACF;EACF,CAAC;AACH,CAAC;AAED,IAAIE,cAAc,GAAGA,CAACZ,QAAQ,EAAES,IAAI,GAAG,EAAE,KACvCV,YAAY,CAACC,QAAQ,EAAES,IAAI,EAAEf,MAAM,CAAC;AAEtC,IAAImB,MAAM,GAAGA,CAACJ,IAAI,GAAG,EAAE,KACrBb,MAAM,CAACC,eAAe,CAAC,IAAIC,UAAU,CAACW,IAAI,CAAC,CAAC,CAACK,MAAM,CAAC,CAACJ,EAAE,EAAEK,IAAI,KAAK;EAChE;EACA;EACA;EACA;EACA;EACAA,IAAI,IAAI,EAAE;EACV,IAAIA,IAAI,GAAG,EAAE,EAAE;IACb;IACAL,EAAE,IAAIK,IAAI,CAACC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC,MAAM,IAAID,IAAI,GAAG,EAAE,EAAE;IACpB;IACAL,EAAE,IAAI,CAACK,IAAI,GAAG,EAAE,EAAEC,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;EAC9C,CAAC,MAAM,IAAIF,IAAI,GAAG,EAAE,EAAE;IACpBL,EAAE,IAAI,GAAG;EACX,CAAC,MAAM;IACLA,EAAE,IAAI,GAAG;EACX;EACA,OAAOA,EAAE;AACX,CAAC,EAAE,EAAE,CAAC;AAER,SAASG,MAAM,EAAED,cAAc,EAAEb,YAAY,EAAEN,WAAW,EAAEC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}