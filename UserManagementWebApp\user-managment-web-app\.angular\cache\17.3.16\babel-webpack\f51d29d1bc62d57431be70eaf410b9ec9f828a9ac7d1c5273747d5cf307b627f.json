{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport { sleep } from \"./utils/sleep\";\nimport { WaiterState } from \"./waiter\";\n/**\n * Reference: https://awslabs.github.io/smithy/1.0/spec/waiters.html#waiter-retries\n */\nvar exponentialBackoffWithJitter = function (minDelay, maxDelay, attemptCeiling, attempt) {\n  if (attempt > attemptCeiling) return maxDelay;\n  var delay = minDelay * Math.pow(2, attempt - 1);\n  return randomInRange(minDelay, delay);\n};\nvar randomInRange = function (min, max) {\n  return min + Math.random() * (max - min);\n};\n/**\n * Function that runs polling as part of waiters. This will make one inital attempt and then\n * subsequent attempts with an increasing delay.\n * @param params options passed to the waiter.\n * @param client AWS SDK Client\n * @param input client input\n * @param stateChecker function that checks the acceptor states on each poll.\n */\nexport var runPolling = function (_a, input, acceptorChecks) {\n  var minDelay = _a.minDelay,\n    maxDelay = _a.maxDelay,\n    maxWaitTime = _a.maxWaitTime,\n    abortController = _a.abortController,\n    client = _a.client;\n  return __awaiter(void 0, void 0, void 0, function () {\n    var state, currentAttempt, waitUntil, attemptCeiling, delay, state_1;\n    var _b;\n    return __generator(this, function (_c) {\n      switch (_c.label) {\n        case 0:\n          return [4 /*yield*/, acceptorChecks(client, input)];\n        case 1:\n          state = _c.sent().state;\n          if (state !== WaiterState.RETRY) {\n            return [2 /*return*/, {\n              state: state\n            }];\n          }\n          currentAttempt = 1;\n          waitUntil = Date.now() + maxWaitTime * 1000;\n          attemptCeiling = Math.log(maxDelay / minDelay) / Math.log(2) + 1;\n          _c.label = 2;\n        case 2:\n          if (!true) return [3 /*break*/, 5];\n          if ((_b = abortController === null || abortController === void 0 ? void 0 : abortController.signal) === null || _b === void 0 ? void 0 : _b.aborted) {\n            return [2 /*return*/, {\n              state: WaiterState.ABORTED\n            }];\n          }\n          delay = exponentialBackoffWithJitter(minDelay, maxDelay, attemptCeiling, currentAttempt);\n          // Resolve the promise explicitly at timeout or aborted. Otherwise this while loop will keep making API call until\n          // `acceptorCheck` returns non-retry status, even with the Promise.race() outside.\n          if (Date.now() + delay * 1000 > waitUntil) {\n            return [2 /*return*/, {\n              state: WaiterState.TIMEOUT\n            }];\n          }\n          return [4 /*yield*/, sleep(delay)];\n        case 3:\n          _c.sent();\n          return [4 /*yield*/, acceptorChecks(client, input)];\n        case 4:\n          state_1 = _c.sent().state;\n          if (state_1 !== WaiterState.RETRY) {\n            return [2 /*return*/, {\n              state: state_1\n            }];\n          }\n          currentAttempt += 1;\n          return [3 /*break*/, 2];\n        case 5:\n          return [2 /*return*/];\n      }\n    });\n  });\n};", "map": {"version": 3, "names": ["__awaiter", "__generator", "sleep", "WaiterState", "exponentialBackoffWithJitter", "min<PERSON>elay", "max<PERSON><PERSON><PERSON>", "attemptCeiling", "attempt", "delay", "Math", "pow", "randomInRange", "min", "max", "random", "runPolling", "_a", "input", "acceptorChecks", "maxWaitTime", "abortController", "client", "state", "currentAttempt", "waitUntil", "state_1", "_b", "_c", "label", "sent", "RETRY", "Date", "now", "log", "signal", "aborted", "ABORTED", "TIMEOUT"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/util-waiter/dist/es/poller.js"], "sourcesContent": ["import { __awaiter, __generator } from \"tslib\";\nimport { sleep } from \"./utils/sleep\";\nimport { WaiterState } from \"./waiter\";\n/**\n * Reference: https://awslabs.github.io/smithy/1.0/spec/waiters.html#waiter-retries\n */\nvar exponentialBackoffWithJitter = function (minDelay, maxDelay, attemptCeiling, attempt) {\n    if (attempt > attemptCeiling)\n        return maxDelay;\n    var delay = minDelay * Math.pow(2, (attempt - 1));\n    return randomInRange(minDelay, delay);\n};\nvar randomInRange = function (min, max) { return min + Math.random() * (max - min); };\n/**\n * Function that runs polling as part of waiters. This will make one inital attempt and then\n * subsequent attempts with an increasing delay.\n * @param params options passed to the waiter.\n * @param client AWS SDK Client\n * @param input client input\n * @param stateChecker function that checks the acceptor states on each poll.\n */\nexport var runPolling = function (_a, input, acceptorChecks) {\n    var minDelay = _a.minDelay, maxDelay = _a.maxDelay, maxWaitTime = _a.maxWaitTime, abortController = _a.abortController, client = _a.client;\n    return __awaiter(void 0, void 0, void 0, function () {\n        var state, currentAttempt, waitUntil, attemptCeiling, delay, state_1;\n        var _b;\n        return __generator(this, function (_c) {\n            switch (_c.label) {\n                case 0: return [4 /*yield*/, acceptorChecks(client, input)];\n                case 1:\n                    state = (_c.sent()).state;\n                    if (state !== WaiterState.RETRY) {\n                        return [2 /*return*/, { state: state }];\n                    }\n                    currentAttempt = 1;\n                    waitUntil = Date.now() + maxWaitTime * 1000;\n                    attemptCeiling = Math.log(maxDelay / minDelay) / Math.log(2) + 1;\n                    _c.label = 2;\n                case 2:\n                    if (!true) return [3 /*break*/, 5];\n                    if ((_b = abortController === null || abortController === void 0 ? void 0 : abortController.signal) === null || _b === void 0 ? void 0 : _b.aborted) {\n                        return [2 /*return*/, { state: WaiterState.ABORTED }];\n                    }\n                    delay = exponentialBackoffWithJitter(minDelay, maxDelay, attemptCeiling, currentAttempt);\n                    // Resolve the promise explicitly at timeout or aborted. Otherwise this while loop will keep making API call until\n                    // `acceptorCheck` returns non-retry status, even with the Promise.race() outside.\n                    if (Date.now() + delay * 1000 > waitUntil) {\n                        return [2 /*return*/, { state: WaiterState.TIMEOUT }];\n                    }\n                    return [4 /*yield*/, sleep(delay)];\n                case 3:\n                    _c.sent();\n                    return [4 /*yield*/, acceptorChecks(client, input)];\n                case 4:\n                    state_1 = (_c.sent()).state;\n                    if (state_1 !== WaiterState.RETRY) {\n                        return [2 /*return*/, { state: state_1 }];\n                    }\n                    currentAttempt += 1;\n                    return [3 /*break*/, 2];\n                case 5: return [2 /*return*/];\n            }\n        });\n    });\n};\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC9C,SAASC,KAAK,QAAQ,eAAe;AACrC,SAASC,WAAW,QAAQ,UAAU;AACtC;AACA;AACA;AACA,IAAIC,4BAA4B,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,OAAO,EAAE;EACtF,IAAIA,OAAO,GAAGD,cAAc,EACxB,OAAOD,QAAQ;EACnB,IAAIG,KAAK,GAAGJ,QAAQ,GAAGK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAGH,OAAO,GAAG,CAAE,CAAC;EACjD,OAAOI,aAAa,CAACP,QAAQ,EAAEI,KAAK,CAAC;AACzC,CAAC;AACD,IAAIG,aAAa,GAAG,SAAAA,CAAUC,GAAG,EAAEC,GAAG,EAAE;EAAE,OAAOD,GAAG,GAAGH,IAAI,CAACK,MAAM,CAAC,CAAC,IAAID,GAAG,GAAGD,GAAG,CAAC;AAAE,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIG,UAAU,GAAG,SAAAA,CAAUC,EAAE,EAAEC,KAAK,EAAEC,cAAc,EAAE;EACzD,IAAId,QAAQ,GAAGY,EAAE,CAACZ,QAAQ;IAAEC,QAAQ,GAAGW,EAAE,CAACX,QAAQ;IAAEc,WAAW,GAAGH,EAAE,CAACG,WAAW;IAAEC,eAAe,GAAGJ,EAAE,CAACI,eAAe;IAAEC,MAAM,GAAGL,EAAE,CAACK,MAAM;EAC1I,OAAOtB,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;IACjD,IAAIuB,KAAK,EAAEC,cAAc,EAAEC,SAAS,EAAElB,cAAc,EAAEE,KAAK,EAAEiB,OAAO;IACpE,IAAIC,EAAE;IACN,OAAO1B,WAAW,CAAC,IAAI,EAAE,UAAU2B,EAAE,EAAE;MACnC,QAAQA,EAAE,CAACC,KAAK;QACZ,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,CAAC,WAAWV,cAAc,CAACG,MAAM,EAAEJ,KAAK,CAAC,CAAC;QAC3D,KAAK,CAAC;UACFK,KAAK,GAAIK,EAAE,CAACE,IAAI,CAAC,CAAC,CAAEP,KAAK;UACzB,IAAIA,KAAK,KAAKpB,WAAW,CAAC4B,KAAK,EAAE;YAC7B,OAAO,CAAC,CAAC,CAAC,YAAY;cAAER,KAAK,EAAEA;YAAM,CAAC,CAAC;UAC3C;UACAC,cAAc,GAAG,CAAC;UAClBC,SAAS,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGb,WAAW,GAAG,IAAI;UAC3Cb,cAAc,GAAGG,IAAI,CAACwB,GAAG,CAAC5B,QAAQ,GAAGD,QAAQ,CAAC,GAAGK,IAAI,CAACwB,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;UAChEN,EAAE,CAACC,KAAK,GAAG,CAAC;QAChB,KAAK,CAAC;UACF,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;UAClC,IAAI,CAACF,EAAE,GAAGN,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACc,MAAM,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,OAAO,EAAE;YACjJ,OAAO,CAAC,CAAC,CAAC,YAAY;cAAEb,KAAK,EAAEpB,WAAW,CAACkC;YAAQ,CAAC,CAAC;UACzD;UACA5B,KAAK,GAAGL,4BAA4B,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEiB,cAAc,CAAC;UACxF;UACA;UACA,IAAIQ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGxB,KAAK,GAAG,IAAI,GAAGgB,SAAS,EAAE;YACvC,OAAO,CAAC,CAAC,CAAC,YAAY;cAAEF,KAAK,EAAEpB,WAAW,CAACmC;YAAQ,CAAC,CAAC;UACzD;UACA,OAAO,CAAC,CAAC,CAAC,WAAWpC,KAAK,CAACO,KAAK,CAAC,CAAC;QACtC,KAAK,CAAC;UACFmB,EAAE,CAACE,IAAI,CAAC,CAAC;UACT,OAAO,CAAC,CAAC,CAAC,WAAWX,cAAc,CAACG,MAAM,EAAEJ,KAAK,CAAC,CAAC;QACvD,KAAK,CAAC;UACFQ,OAAO,GAAIE,EAAE,CAACE,IAAI,CAAC,CAAC,CAAEP,KAAK;UAC3B,IAAIG,OAAO,KAAKvB,WAAW,CAAC4B,KAAK,EAAE;YAC/B,OAAO,CAAC,CAAC,CAAC,YAAY;cAAER,KAAK,EAAEG;YAAQ,CAAC,CAAC;UAC7C;UACAF,cAAc,IAAI,CAAC;UACnB,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QAC3B,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,CAAC,WAAW;MACjC;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}