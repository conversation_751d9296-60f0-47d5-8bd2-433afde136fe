{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport merge from 'lodash/merge.js';\nimport '@aws-amplify/core/internals/utils';\nimport '../utils/setUserAgent/constants.mjs';\nimport { isEmpty } from '../utils/utils.mjs';\n\n// Runs all validators given. Resolves if there are no error. Rejects otherwise.\nconst runValidators = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (formData, touchData, passwordSettings, validators) {\n    const errors = yield Promise.all(validators.map(validator => validator(formData, touchData, passwordSettings)));\n    const mergedError = merge({}, ...errors);\n    if (isEmpty(mergedError)) {\n      // no errors were found\n      return Promise.resolve();\n    } else {\n      return Promise.reject(mergedError);\n    }\n  });\n  return function runValidators(_x, _x2, _x3, _x4) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { runValidators };", "map": {"version": 3, "names": ["merge", "isEmpty", "runValidators", "_ref", "_asyncToGenerator", "formData", "touchData", "passwordSettings", "validators", "errors", "Promise", "all", "map", "validator", "mergedError", "resolve", "reject", "_x", "_x2", "_x3", "_x4", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/validators/index.mjs"], "sourcesContent": ["import merge from 'lodash/merge.js';\nimport '@aws-amplify/core/internals/utils';\nimport '../utils/setUserAgent/constants.mjs';\nimport { isEmpty } from '../utils/utils.mjs';\n\n// Runs all validators given. Resolves if there are no error. Rejects otherwise.\nconst runValidators = async (formData, touchData, passwordSettings, validators) => {\n    const errors = await Promise.all(validators.map((validator) => validator(formData, touchData, passwordSettings)));\n    const mergedError = merge({}, ...errors);\n    if (isEmpty(mergedError)) {\n        // no errors were found\n        return Promise.resolve();\n    }\n    else {\n        return Promise.reject(mergedError);\n    }\n};\n\nexport { runValidators };\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,iBAAiB;AACnC,OAAO,mCAAmC;AAC1C,OAAO,qCAAqC;AAC5C,SAASC,OAAO,QAAQ,oBAAoB;;AAE5C;AACA,MAAMC,aAAa;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,QAAQ,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,UAAU,EAAK;IAC/E,MAAMC,MAAM,SAASC,OAAO,CAACC,GAAG,CAACH,UAAU,CAACI,GAAG,CAAEC,SAAS,IAAKA,SAAS,CAACR,QAAQ,EAAEC,SAAS,EAAEC,gBAAgB,CAAC,CAAC,CAAC;IACjH,MAAMO,WAAW,GAAGd,KAAK,CAAC,CAAC,CAAC,EAAE,GAAGS,MAAM,CAAC;IACxC,IAAIR,OAAO,CAACa,WAAW,CAAC,EAAE;MACtB;MACA,OAAOJ,OAAO,CAACK,OAAO,CAAC,CAAC;IAC5B,CAAC,MACI;MACD,OAAOL,OAAO,CAACM,MAAM,CAACF,WAAW,CAAC;IACtC;EACJ,CAAC;EAAA,gBAVKZ,aAAaA,CAAAe,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAjB,IAAA,CAAAkB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAUlB;AAED,SAASpB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}