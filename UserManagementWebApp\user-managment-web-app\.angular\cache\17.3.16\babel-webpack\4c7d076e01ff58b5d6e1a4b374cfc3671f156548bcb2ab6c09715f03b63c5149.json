{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Hub } from '@aws-amplify/core';\nimport { AMPLIFY_SYMBOL } from '@aws-amplify/core/internals/utils';\nimport { oAuthStore } from './oAuthStore.mjs';\nimport { resolveAndClearInflightPromises } from './inflightPromise.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst handleFailure = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (error) {\n    resolveAndClearInflightPromises();\n    yield oAuthStore.clearOAuthInflightData();\n    Hub.dispatch('auth', {\n      event: 'signInWithRedirect_failure',\n      data: {\n        error\n      }\n    }, 'Auth', AMPLIFY_SYMBOL);\n  });\n  return function handleFailure(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { handleFailure };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "AMPLIFY_SYMBOL", "oAuthStore", "resolveAndClearInflightPromises", "handleFailure", "_ref", "_asyncToGenerator", "error", "clearOAuthInflightData", "dispatch", "event", "data", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/oauth/handleFailure.mjs"], "sourcesContent": ["import { Hub } from '@aws-amplify/core';\nimport { AMPLIFY_SYMBOL } from '@aws-amplify/core/internals/utils';\nimport { oAuthStore } from './oAuthStore.mjs';\nimport { resolveAndClearInflightPromises } from './inflightPromise.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst handleFailure = async (error) => {\n    resolveAndClearInflightPromises();\n    await oAuthStore.clearOAuthInflightData();\n    Hub.dispatch('auth', { event: 'signInWithRedirect_failure', data: { error } }, 'Auth', AMPLIFY_SYMBOL);\n};\n\nexport { handleFailure };\n"], "mappings": ";AAAA,SAASA,GAAG,QAAQ,mBAAmB;AACvC,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,+BAA+B,QAAQ,uBAAuB;;AAEvE;AACA;AACA,MAAMC,aAAa;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,KAAK,EAAK;IACnCJ,+BAA+B,CAAC,CAAC;IACjC,MAAMD,UAAU,CAACM,sBAAsB,CAAC,CAAC;IACzCR,GAAG,CAACS,QAAQ,CAAC,MAAM,EAAE;MAAEC,KAAK,EAAE,4BAA4B;MAAEC,IAAI,EAAE;QAAEJ;MAAM;IAAE,CAAC,EAAE,MAAM,EAAEN,cAAc,CAAC;EAC1G,CAAC;EAAA,gBAJKG,aAAaA,CAAAQ,EAAA;IAAA,OAAAP,IAAA,CAAAQ,KAAA,OAAAC,SAAA;EAAA;AAAA,GAIlB;AAED,SAASV,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}