{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Cache } from '../../../Cache/index.mjs';\nimport { getCacheKey } from './getCacheKey.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns an endpoint id from cache or `undefined` if not found.\n *\n * @internal\n */\nconst getEndpointId = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (appId, category) {\n    const cacheKey = getCacheKey(appId, category);\n    const cachedEndpointId = yield Cache.getItem(cacheKey);\n    return cachedEndpointId ?? undefined;\n  });\n  return function getEndpointId(_x, _x2) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { getEndpointId };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON>", "getEndpointId", "_ref", "_asyncToGenerator", "appId", "category", "cache<PERSON>ey", "cachedEndpointId", "getItem", "undefined", "_x", "_x2", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/providers/pinpoint/utils/getEndpointId.mjs"], "sourcesContent": ["import { Cache } from '../../../Cache/index.mjs';\nimport { getCacheKey } from './getCacheKey.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns an endpoint id from cache or `undefined` if not found.\n *\n * @internal\n */\nconst getEndpointId = async (appId, category) => {\n    const cacheKey = getCacheKey(appId, category);\n    const cachedEndpointId = await Cache.getItem(cacheKey);\n    return cachedEndpointId ?? undefined;\n};\n\nexport { getEndpointId };\n"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,0BAA0B;AAChD,SAASC,WAAW,QAAQ,mBAAmB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,KAAK,EAAEC,QAAQ,EAAK;IAC7C,MAAMC,QAAQ,GAAGN,WAAW,CAACI,KAAK,EAAEC,QAAQ,CAAC;IAC7C,MAAME,gBAAgB,SAASR,KAAK,CAACS,OAAO,CAACF,QAAQ,CAAC;IACtD,OAAOC,gBAAgB,IAAIE,SAAS;EACxC,CAAC;EAAA,gBAJKR,aAAaA,CAAAS,EAAA,EAAAC,GAAA;IAAA,OAAAT,IAAA,CAAAU,KAAA,OAAAC,SAAA;EAAA;AAAA,GAIlB;AAED,SAASZ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}