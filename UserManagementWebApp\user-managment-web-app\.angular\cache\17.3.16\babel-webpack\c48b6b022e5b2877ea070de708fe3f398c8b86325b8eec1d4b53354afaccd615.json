{"ast": null, "code": "import { Sha256 } from '@aws-crypto/sha256-js';\nimport { getHexFromBytes } from './getHexFromBytes.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Calculate a hash from a `SourceData`\n * @param {SourceData} data Value to hash.\n * @returns {string} Hex-encoded hash.\n * @private\n */\nconst getHashFromData = data => {\n  const sha256 = new Sha256();\n  sha256.update(data);\n  const hashedData = sha256.digestSync();\n  const hashHexFromUint8 = getHexFromBytes(hashedData);\n  return new Array(64 - hashHexFromUint8.length).join('0') + hashHexFromUint8;\n};\nexport { getHashFromData };", "map": {"version": 3, "names": ["Sha256", "getHexFromBytes", "getHashFromData", "data", "sha256", "update", "hashedData", "digestSync", "hashHexFromUint8", "Array", "length", "join"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/srp/getHashFromData.mjs"], "sourcesContent": ["import { Sha256 } from '@aws-crypto/sha256-js';\nimport { getHexFromBytes } from './getHexFromBytes.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Calculate a hash from a `SourceData`\n * @param {SourceData} data Value to hash.\n * @returns {string} Hex-encoded hash.\n * @private\n */\nconst getHashFromData = (data) => {\n    const sha256 = new Sha256();\n    sha256.update(data);\n    const hashedData = sha256.digestSync();\n    const hashHexFromUint8 = getHexFromBytes(hashedData);\n    return new Array(64 - hashHexFromUint8.length).join('0') + hashHexFromUint8;\n};\n\nexport { getHashFromData };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,eAAe,QAAQ,uBAAuB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAIC,IAAI,IAAK;EAC9B,MAAMC,MAAM,GAAG,IAAIJ,MAAM,CAAC,CAAC;EAC3BI,MAAM,CAACC,MAAM,CAACF,IAAI,CAAC;EACnB,MAAMG,UAAU,GAAGF,MAAM,CAACG,UAAU,CAAC,CAAC;EACtC,MAAMC,gBAAgB,GAAGP,eAAe,CAACK,UAAU,CAAC;EACpD,OAAO,IAAIG,KAAK,CAAC,EAAE,GAAGD,gBAAgB,CAACE,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAAGH,gBAAgB;AAC/E,CAAC;AAED,SAASN,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}