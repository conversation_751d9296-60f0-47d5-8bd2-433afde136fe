{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/SaltboxActionableGrid/Saltbox-Data-App/saltbox-data-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate, __param } from \"tslib\";\nimport { Component, Input, Inject, Output, EventEmitter } from '@angular/core';\nimport { NgIf, NgClass } from '@angular/common';\nimport { deepCopy } from 'src/app/shared/utilities/copy.functions';\nimport { JSONSchemaParam } from 'src/app/core/models/json-schema-param';\nimport { CRUDActions, ModifierSourceType } from 'src/app/core/enums/shared';\nimport { HttpErrorResponse } from '@angular/common/http';\nimport { UserActivity } from 'src/app/shared/models/user-activity';\nimport { CommunicationToken } from 'src/app/core/models/communication-service-type';\nimport { DynamicFormLayoutAreas, DynamicFormPostSaveActions } from '../enums/dynamic-form';\nimport { RecordsSaveEvent } from 'src/app/shared/models/records-save-event';\nimport { AssetEventType } from 'src/app/shared/enums/asset-workflow';\nimport { getDefaultValue } from 'src/app/shared/utilities/datatype.functions';\nimport { DB_PRIMARY_KEY, SB_META_COL } from 'src/app/shared/constants/record.const';\nimport { firstValueFrom } from 'rxjs';\nimport { UserPermissionLevels } from 'src/app/core/enums/shared';\nimport { AssetUrlPipe } from '../../shared/pipes/asset-url.pipe';\nimport { BlockUIModule } from 'primeng/blockui';\nimport { ValidationResultsDialogComponent } from '../../business-validation-results/validation-results-dialog.component';\nimport { PageErrorBlockComponent } from '../../shared/page-error-block/page-error-block.component';\nimport { ButtonModule } from 'primeng/button';\nimport { SignatureButtonComponent } from '../../signature/signature-button/signature-button.component';\nimport { SignaturesRendererComponent } from '../../signature/signatures-renderer/signatures-renderer.component';\nimport { DynamicFormBodyComponent } from '../dynamic-form-body/dynamic-form-body.component';\nimport { DynamicFormSectionComponent } from '../dynamic-form-section/dynamic-form-section.component';\nlet DataEntryFormComponent = class DataEntryFormComponent {\n  constructor(activatedRoute, router, datastoresService, changeTrackingService, dynamicFormService, notificationService, userActivityService, location, postSaveEventService, projectsService, communicationService) {\n    this.activatedRoute = activatedRoute;\n    this.router = router;\n    this.datastoresService = datastoresService;\n    this.changeTrackingService = changeTrackingService;\n    this.dynamicFormService = dynamicFormService;\n    this.notificationService = notificationService;\n    this.userActivityService = userActivityService;\n    this.location = location;\n    this.postSaveEventService = postSaveEventService;\n    this.projectsService = projectsService;\n    this.communicationService = communicationService;\n    this.recordId = '';\n    this.triggerSaveEvent = new EventEmitter();\n    this.isPreview = false;\n    this.formBodyOnly = false;\n    this.userPermissionLevel = UserPermissionLevels.EditAndDelete;\n    this.afterSave = new EventEmitter();\n    this.beforeSave = new EventEmitter();\n    /**\n     * Event emitted when the form has finished loading.\n     * The boolean value indicates if the form was loaded successfully or with an error.\n     * - `true`: Form loaded without any errors.\n     * - `false`: Form loaded with errors.\n     */\n    this.formLoad = new EventEmitter();\n    this.isChildComponent = false;\n    this.jsonSchemaParam = new JSONSchemaParam();\n    this.isSubmitted = false;\n    this.signatureUpdated = false;\n    this.showSaveErrorsBlock = false;\n    this.showWarningsBlock = false;\n    this.showValidationResults = false;\n    this.showSpinner = false;\n    this.showDynamicForm = false;\n    this.masterKeyColumn = \"\";\n    this.isEmbeddedView = false;\n    this.isOTC = false;\n    // used in the html\n    this.DynamicFormPostSaveActions = DynamicFormPostSaveActions;\n    this.DynamicFormAreas = DynamicFormLayoutAreas;\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.showSpinner = true;\n      // Hide navigation if this is an embedded view\n      _this.isEmbeddedView = _this.router?.url?.includes('embeddedview');\n      _this.isOTC = _this.router?.url?.includes('otc');\n      if (_this.isEmbeddedView) _this.hideNavigationApp();\n      if (_this.dynamicForm) {\n        _this.isChildComponent = true;\n        yield _this.setFormDetails();\n        _this.showDynamicForm = !_this.pageLoadErrorObj;\n        _this.showSpinner = false;\n        _this.formLoad.emit(_this.showDynamicForm);\n        return;\n      }\n      if (_this.activatedRoute) {\n        _this.activatedRoute.params.subscribe( /*#__PURE__*/function () {\n          var _ref = _asyncToGenerator(function* (params) {\n            _this.projectId = params.projectId;\n            _this.projectVersionId = params.projectVersionId;\n            _this.recordId = params.recordId;\n            // Check if the route variable is coming from an embedded iFrame request (URL variable: primaryKey)\n            _this.queryParamPrimaryKey = _this.activatedRoute.snapshot.queryParams.primaryKey;\n            try {\n              // Logging user activity\n              if (!_this.isPreview && !_this.isOTC) {\n                _this.projectVersion = yield _this.getProjectVersion();\n                _this.userActivityService.addUserActivity(new UserActivity({\n                  projectId: _this.projectId,\n                  projectVersionId: _this.projectVersionId,\n                  url: window.location.pathname,\n                  dynamicFormId: params.dynamicFormId\n                }));\n              }\n              yield _this.getDynamicForm(params.dynamicFormId);\n              if (_this.dynamicForm) yield _this.setFormDetails();\n              _this.setFormInitialData();\n              if (!_this.pageLoadErrorObj) {\n                _this.changeTrackingService.initBatchChange(params.projectId, params.projectVersionId);\n                _this.showDynamicForm = true;\n              }\n              _this.formLoad.emit(_this.showDynamicForm);\n            } catch (ex) {\n              _this.pageLoadErrorObj = _this.getPageErrorDetails('Other', ex);\n            }\n            _this.showSpinner = false;\n          });\n          return function (_x) {\n            return _ref.apply(this, arguments);\n          };\n        }());\n      }\n    })();\n  }\n  setFormInitialData() {\n    if (!this.datastore || !this.activatedRoute?.snapshot?.queryParams?.initialData) return;\n    // Get the data query parameter if it exists\n    const initialDataParam = this.activatedRoute.snapshot.queryParams.initialData;\n    // Try to parse the data parameter as JSON if it exists\n    if (initialDataParam) {\n      try {\n        const queryParamData = JSON.parse(decodeURIComponent(initialDataParam));\n        const fixedData = {\n          [DB_PRIMARY_KEY]: undefined,\n          ...this.datastoresService.fixDataWithSchema(this.datastore.baseSchema, queryParamData),\n          [SB_META_COL]: undefined\n        };\n        console.log('fixedData', fixedData);\n        this.jsonSchemaParam.data = fixedData;\n      } catch (error) {\n        console.error('Error parsing data query parameter:', error);\n      }\n    }\n  }\n  getDynamicForm(dynamicFormId) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        if (dynamicFormId) {\n          const dynamicForm = yield firstValueFrom(_this2.dynamicFormService.getDynamicForm(_this2.projectId, _this2.projectVersionId, dynamicFormId));\n          _this2.dynamicForm = dynamicForm;\n          _this2.buildBreadcrumb();\n        }\n        if (!_this2.dynamicForm) _this2.pageLoadErrorObj = _this2.getPageErrorDetails('Form');\n      } catch (ex) {\n        _this2.pageLoadErrorObj = _this2.getPageErrorDetails('Other', ex);\n      }\n    })();\n  }\n  getDatastore() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.dynamicForm.datastoreId) {\n        if (!_this3.isPreview) {\n          _this3.notificationService.showError('Error', `No datastore is selected! Please check the form's configuration.`);\n        }\n        return;\n      }\n      try {\n        const datastore = yield firstValueFrom(_this3.datastoresService.getDatastore(_this3.dynamicForm.datastoreId));\n        if (!datastore) {\n          _this3.pageLoadErrorObj = _this3.getPageErrorDetails('Datastore');\n          return;\n        }\n        _this3.datastore = datastore;\n      } catch (ex) {\n        _this3.pageLoadErrorObj = _this3.getPageErrorDetails('Other', ex);\n      }\n    })();\n  }\n  setFormData() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.datastore) return;\n      // setting master key column for validation results if it is not being set\n      if (!_this4.masterKeyColumn) {\n        _this4.masterKeyColumn = _this4.datastore?.primaryKey;\n        if (!_this4.masterKeyColumn) {\n          const fieldsConfig = _this4.dynamicFormService.getFieldsConfig(_this4.dynamicForm);\n          _this4.masterKeyColumn = fieldsConfig?.length ? fieldsConfig[0]?.key?.toString() : '';\n        }\n      }\n      if (_this4.recordId || _this4.queryParamPrimaryKey) {\n        _this4.jsonSchemaParam.action = CRUDActions.Update;\n        try {\n          const data = yield firstValueFrom(_this4.queryParamPrimaryKey ? _this4.datastoresService.getRecordByPrimaryKey(_this4.dynamicForm.datastoreId, _this4.queryParamPrimaryKey) : _this4.datastoresService.getRecord(_this4.dynamicForm.datastoreId, _this4.recordId));\n          if (data) {\n            // Set the record id if the form is loaded with the primary key\n            if (_this4.queryParamPrimaryKey) _this4.recordId = data[DB_PRIMARY_KEY];\n            _this4.jsonSchemaParam.data = data;\n            _this4.jsonSchemaParam.originalData = deepCopy(data);\n            if (data.__sbmeta?.Signatures?.length) {\n              _this4.selectedSignature = data.__sbmeta?.Signatures[0];\n              _this4.signatures = data.__sbmeta?.Signatures;\n            }\n            return;\n          }\n          _this4.pageLoadErrorObj = _this4.getPageErrorDetails('Data');\n        } catch (ex) {\n          _this4.pageLoadErrorObj = _this4.getPageErrorDetails('Other', ex);\n        }\n      }\n    })();\n  }\n  setFormDetails() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      yield _this5.getDatastore();\n      yield _this5.setFormData();\n    })();\n  }\n  formOnSubmit(data) {\n    this.setDataEntryChanges(data);\n    this.saveChanges();\n  }\n  saveChanges() {\n    this.beforeSave.emit();\n    this.showSpinner = true;\n    this.datastoresService.saveRecords(this.changeTrackingService.batchChange, this.datastore.id).subscribe({\n      next: saveRecordsResponse => {\n        this.saveRecordsResponse = saveRecordsResponse;\n        this.showValidationResults = saveRecordsResponse?.failedValidations?.length > 0;\n        this.showSaveErrorsBlock = saveRecordsResponse?.hasFailures;\n        this.showWarningsBlock = this.showValidationResults && !saveRecordsResponse?.hasFailures;\n        if (!saveRecordsResponse.hasFailures) this.notificationService.showSuccess('Success!', 'Form has been submitted successfully.');\n        if (!this.showValidationResults) this.doPostSaveActions();\n        this.showSpinner = false;\n      },\n      error: err => {\n        this.showSpinner = false;\n        const primaryKeyName = this.dynamicFormService.getFieldsConfig(this.dynamicForm)[0]?.props?.label ?? this.masterKeyColumn;\n        if (err.error?.toString().includes('DuplicateKey')) {\n          this.notificationService.showError('Error', `Duplicate value for primary key: ${primaryKeyName}. Please use a unique value.`);\n        } else {\n          this.notificationService.showError('Error', err.error ?? err.message);\n        }\n        this.afterSave.emit(false);\n      }\n    });\n  }\n  doPostSaveActions() {\n    // If it is a child component, we just emit the after save and parent component knows what to do\n    if (this.isChildComponent) {\n      this.afterSave.emit(true);\n      return;\n    }\n    this.saveRecordsResponse = null;\n    switch (this.dynamicForm.additionalSettings?.postSaveAction) {\n      case DynamicFormPostSaveActions.Redirect:\n        window.location.href = this.dynamicForm.additionalSettings.actionContent;\n        break;\n      case DynamicFormPostSaveActions.Reload:\n        this.reloadForm();\n        break;\n      default:\n        this.changeTrackingService.resetBatchChangeVariables();\n        this.showDynamicForm = false;\n        this.isSubmitted = true;\n    }\n  }\n  handleContinue() {\n    // We should reset the data as the form is being reloaded\n    this.jsonSchemaParam = new JSONSchemaParam();\n    this.showWarningsBlock = false;\n    this.doPostSaveActions();\n  }\n  editLastEntry() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this6.saveRecordsResponse?.recordIds?.length) {\n        return;\n      }\n      _this6.showSpinner = true;\n      _this6.recordId = _this6.saveRecordsResponse.recordIds[0];\n      _this6.changeTrackingService.resetBatchChangeVariables();\n      yield _this6.setFormData();\n      _this6.showSpinner = false;\n      _this6.saveRecordsResponse = null;\n      _this6.showWarningsBlock = false;\n    })();\n  }\n  addInvokeWorkflowEvent() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const saveFormEvent = _this7.dynamicForm.additionalSettings?.formEvents.find(formEvent => formEvent.eventType === AssetEventType.FormSave);\n      if (!saveFormEvent) {\n        return;\n      }\n      _this7.postSaveEventService.addInvokeWorkflowEvent({\n        recordsSaveEvent: new RecordsSaveEvent(_this7.dynamicForm.id, _this7.dynamicForm.name, ModifierSourceType.Form, _this7.datastore.name, _this7.dynamicForm.datastoreId),\n        assetEvent: _this7.dynamicForm.additionalSettings?.formEvents.find(formEvent => formEvent.eventType === AssetEventType.FormSave)\n      });\n    })();\n  }\n  setDataEntryChanges(event) {\n    const data = event.data;\n    if (!data) {\n      return;\n    }\n    if (event.sendEmail) {\n      this.setEmailSetup();\n    }\n    if (this.dynamicForm.additionalSettings?.invokeWorkflow) {\n      this.addInvokeWorkflowEvent();\n    }\n    this.jsonSchemaParam.data = data;\n    let id;\n    switch (this.jsonSchemaParam.action) {\n      case CRUDActions.Update:\n        {\n          id = this.jsonSchemaParam.originalData[DB_PRIMARY_KEY];\n          this.dynamicForm.fieldsConfig.forEach(field => {\n            const fieldName = field.key.toString();\n            const datastoreField = this.datastore.baseSchema.properties[fieldName];\n            if (datastoreField) {\n              let fieldValue = data[fieldName];\n              if (fieldValue != this.jsonSchemaParam.originalData[fieldName]) {\n                fieldValue = fieldValue == null ? getDefaultValue(datastoreField.type, datastoreField.format) : fieldValue;\n                this.changeTrackingService.trackChange(id, fieldName, this.jsonSchemaParam.originalData[fieldName], fieldValue, CRUDActions.Update);\n              }\n            }\n          });\n        }\n        break;\n      case CRUDActions.Create:\n        {\n          id = \"__0\";\n          this.dynamicForm.fieldsConfig.forEach(field => {\n            const fieldName = field.key.toString();\n            const datastoreField = this.datastore.baseSchema.properties[fieldName];\n            if (datastoreField) {\n              const fieldValue = data[fieldName] !== undefined && data[fieldName] !== null ? data[fieldName] : getDefaultValue(datastoreField.type, datastoreField.format);\n              this.changeTrackingService.trackChange(id, fieldName, undefined, fieldValue, CRUDActions.Create);\n            }\n          });\n        }\n        break;\n    }\n    this.changeTrackingService.trackAttachments(id, event.aliasIds);\n    // updating the signature if it has been changed\n    if (this.signatureUpdated) this.changeTrackingService.trackSignatures(id, this.signatures);\n    // we need to replace the original data with what is currently saved in the batch changes, then with the next save we can differniate the changes\n    this.jsonSchemaParam.originalData = deepCopy(data);\n  }\n  submit() {\n    this.triggerSaveEvent.emit();\n  }\n  hideNavigationApp() {\n    this.communicationService.next({\n      fromAppName: this.communicationService.currentAppName,\n      toAppName: this.communicationService.navigationAppName,\n      eventType: 'HideNavigationBar',\n      data: {}\n    });\n  }\n  reloadForm() {\n    // if the route is different change the route\n    if (this.activatedRoute.snapshot.params['recordId']) {\n      this.location.go(`/saltboxdataapp/data-entry/${this.projectId}/${this.projectVersionId}/Forms/${this.dynamicForm.id}`);\n      return;\n    }\n    this.recordId = undefined;\n    this.signatureUpdated = false;\n    this.selectedSignature = undefined;\n    this.signatures = [];\n    this.changeTrackingService.resetBatchChangeVariables();\n    this.dynamicFormService.resetDataEntryForm.next();\n    window.scrollTo(0, 0);\n  }\n  setEmailSetup() {\n    const emailSetup = {\n      subject: 'Link to your responses',\n      message: this.createEmailMessage()\n    };\n    this.postSaveEventService.addFormEmailEvent(emailSetup);\n  }\n  createEmailMessage() {\n    const emailMessage = this.dynamicForm.additionalSettings.emailMessage.replace(/<p>/g, '').replace(/<\\/p>/g, '\\n') + `\\n${window.location.href}/{{recordId}}` + '\\n\\nThanks and Regards,\\nSaltbox';\n    return emailMessage;\n  }\n  signatureChange(signature) {\n    this.signatureUpdated = true;\n    this.signatures = signature ? [signature] : [];\n  }\n  validationDialogVisibaleChanged(visible) {\n    // if this is a child component and user closes the warnings we will emit the after save event\n    if (this.isChildComponent && !visible && this.showWarningsBlock) {\n      this.afterSave.emit(true);\n      return;\n    }\n  }\n  getProjectVersion() {\n    return this.projectsService.getProjectVersion(`${this.projectVersionId}`);\n  }\n  buildBreadcrumb() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      if (_this8.projectVersion) {\n        const items = [{\n          label: 'My Apps',\n          routerLink: '/dashboard-v3'\n        }, {\n          label: _this8.projectVersion.appManifest?.name,\n          routerLink: `/app-view/${_this8.dynamicForm.projectId}/${_this8.dynamicForm.projectVersionId}`\n        }, {\n          label: _this8.dynamicForm?.name\n        }];\n        _this8.communicationService.updateBreadcrumb(items);\n      }\n    })();\n  }\n  getPageErrorDetails(type, ex = null) {\n    switch (type) {\n      case 'Data':\n        return {\n          title: 'Data not found!',\n          details: `<p>Record with the ${this.queryParamPrimaryKey ? 'primary key' : 'record id'}:'<b>${this.queryParamPrimaryKey ?? this.recordId}</b>' \n          was not found.</p><p>The link you used is broken or the data may have been deleted.</p>`,\n          moreDetails: `<p><b>Error:</b> Record with the ${this.queryParamPrimaryKey ? 'primary key' : 'record id'}:'<b>${this.queryParamPrimaryKey ?? this.recordId}\n          </b>'was not found in the datastore '<b>${this.datastore?.description}</b>'.</p>`\n        };\n      case 'Datastore':\n        return {\n          title: 'Datastore not found!',\n          details: `<p>The link you used is broken or the datastore may have been deleted.</p>`\n        };\n      case 'Form':\n        return {\n          title: 'Form not found!',\n          details: `<p>The link you used is broken or the form may have been deleted.</p>`\n        };\n      case 'Other':\n        return {\n          title: 'Uh oh!',\n          details: '<p>' + (ex instanceof HttpErrorResponse ? ex.statusText : ex?.error ?? ex?.message) + '</p>'\n        };\n    }\n  }\n};\n__decorate([Input()], DataEntryFormComponent.prototype, \"dynamicForm\", void 0);\n__decorate([Input()], DataEntryFormComponent.prototype, \"datastore\", void 0);\n__decorate([Input()], DataEntryFormComponent.prototype, \"recordId\", void 0);\n__decorate([Input()], DataEntryFormComponent.prototype, \"triggerSaveEvent\", void 0);\n__decorate([Input()], DataEntryFormComponent.prototype, \"projectId\", void 0);\n__decorate([Input()], DataEntryFormComponent.prototype, \"projectVersionId\", void 0);\n__decorate([Input()], DataEntryFormComponent.prototype, \"isPreview\", void 0);\n__decorate([Input()], DataEntryFormComponent.prototype, \"formBodyOnly\", void 0);\n__decorate([Input()], DataEntryFormComponent.prototype, \"userPermissionLevel\", void 0);\n__decorate([Output()], DataEntryFormComponent.prototype, \"afterSave\", void 0);\n__decorate([Output()], DataEntryFormComponent.prototype, \"beforeSave\", void 0);\n__decorate([Output()], DataEntryFormComponent.prototype, \"formLoad\", void 0);\nDataEntryFormComponent = __decorate([Component({\n  selector: 'app-form-renderer',\n  templateUrl: './data-entry-form.component.html',\n  standalone: true,\n  imports: [NgIf, NgClass, DynamicFormSectionComponent, DynamicFormBodyComponent, SignaturesRendererComponent, SignatureButtonComponent, ButtonModule, PageErrorBlockComponent, ValidationResultsDialogComponent, BlockUIModule, AssetUrlPipe]\n}), __param(10, Inject(CommunicationToken))], DataEntryFormComponent);\nexport { DataEntryFormComponent };", "map": {"version": 3, "names": ["Component", "Input", "Inject", "Output", "EventEmitter", "NgIf", "Ng<PERSON><PERSON>", "deepCopy", "JSONSchemaParam", "CRUDActions", "ModifierSourceType", "HttpErrorResponse", "UserActivity", "CommunicationToken", "DynamicFormLayoutAreas", "DynamicFormPostSaveActions", "RecordsSaveEvent", "AssetEventType", "getDefaultValue", "DB_PRIMARY_KEY", "SB_META_COL", "firstValueFrom", "UserPermissionLevels", "AssetUrlPipe", "BlockUIModule", "ValidationResultsDialogComponent", "PageErrorBlockComponent", "ButtonModule", "SignatureButtonComponent", "SignaturesRendererComponent", "DynamicFormBodyComponent", "DynamicFormSectionComponent", "DataEntryFormComponent", "constructor", "activatedRoute", "router", "datastoresService", "changeTrackingService", "dynamicFormService", "notificationService", "userActivityService", "location", "postSaveEventService", "projectsService", "communicationService", "recordId", "triggerSaveEvent", "isPreview", "formBodyOnly", "userPermissionLevel", "EditAndDelete", "afterSave", "beforeSave", "formLoad", "isChildComponent", "jsonSchemaParam", "isSubmitted", "signatureUpdated", "showSaveErrorsBlock", "showWarningsBlock", "showValidationResults", "showSpinner", "showDynamicForm", "masterKeyColumn", "isEmbeddedView", "isOTC", "DynamicFormAreas", "ngOnInit", "_this", "_asyncToGenerator", "url", "includes", "hideNavigationApp", "dynamicForm", "setFormDetails", "pageLoadErrorObj", "emit", "params", "subscribe", "_ref", "projectId", "projectVersionId", "queryParamPrimaryKey", "snapshot", "queryParams", "<PERSON><PERSON><PERSON>", "projectVersion", "getProjectVersion", "addUserActivity", "window", "pathname", "dynamicFormId", "getDynamicForm", "setFormInitialData", "initBatchChange", "ex", "getPageErrorDetails", "_x", "apply", "arguments", "datastore", "initialData", "initialDataParam", "queryParamData", "JSON", "parse", "decodeURIComponent", "fixedData", "undefined", "fixDataWithSchema", "baseSchema", "console", "log", "data", "error", "_this2", "buildBreadcrumb", "getDatastore", "_this3", "datastoreId", "showError", "setFormData", "_this4", "fieldsConfig", "getFieldsConfig", "length", "key", "toString", "action", "Update", "getRecordByPrimaryKey", "getRecord", "originalData", "__sbmeta", "Signatures", "selectedSignature", "signatures", "_this5", "formOnSubmit", "setDataEntryChanges", "saveChanges", "saveRecords", "batchChange", "id", "next", "saveRecordsResponse", "failedValidations", "hasFailures", "showSuccess", "doPostSaveActions", "err", "primaryKeyName", "props", "label", "message", "additionalSettings", "postSaveAction", "Redirect", "href", "actionContent", "Reload", "reloadForm", "resetBatchChangeVariables", "handleContinue", "editLastEntry", "_this6", "recordIds", "addInvokeWorkflowEvent", "_this7", "saveFormEvent", "formEvents", "find", "formEvent", "eventType", "FormSave", "recordsSaveEvent", "name", "Form", "assetEvent", "event", "sendEmail", "setEmailSetup", "invokeWorkflow", "for<PERSON>ach", "field", "fieldName", "datastoreField", "properties", "fieldValue", "type", "format", "trackChange", "Create", "trackAttachments", "aliasIds", "trackSignatures", "submit", "fromAppName", "currentAppName", "toAppName", "navigationAppName", "go", "resetDataEntryForm", "scrollTo", "emailSetup", "subject", "createEmailMessage", "addFormEmailEvent", "emailMessage", "replace", "signatureChange", "signature", "validationDialogVisibaleChanged", "visible", "_this8", "items", "routerLink", "appManifest", "updateBreadcrumb", "title", "details", "moreDetails", "description", "statusText", "__decorate", "selector", "templateUrl", "standalone", "imports", "__param"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\dynamic-forms\\data-entry-form\\data-entry-form.component.ts"], "sourcesContent": ["import { Component, OnInit, Input, Inject, Output, EventEmitter } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Location, NgIf, NgClass } from '@angular/common';\r\nimport { NotificationService } from 'src/app/core/services/notification.service';\r\nimport { ChangeTrackingService } from 'src/app/core/services/change-tracking.service';\r\nimport { deepCopy } from 'src/app/shared/utilities/copy.functions';\r\nimport { JSONSchemaParam } from 'src/app/core/models/json-schema-param';\r\nimport { CRUDActions, ModifierSourceType } from 'src/app/core/enums/shared';\r\nimport { DynamicForm } from '../models/dynamic-form';\r\nimport { Datastore } from 'src/app/shared/models/datastore';\r\nimport { HttpErrorResponse } from '@angular/common/http';\r\nimport { UserActivity } from 'src/app/shared/models/user-activity';\r\nimport { UserActivityService } from 'src/app/core/services/user-activity.service';\r\nimport { DynamicFormsService } from '../services/dynamic-forms.service';\r\nimport { DatastoresService } from 'src/app/core/services/datastores.service';\r\nimport { CommunicationServiceType, CommunicationToken } from 'src/app/core/models/communication-service-type';\r\nimport { DynamicFormLayoutAreas, DynamicFormPostSaveActions } from '../enums/dynamic-form';\r\nimport { EmailSetup } from 'src/app/core/models/email-setup';\r\nimport { RecordsSaveEvent } from 'src/app/shared/models/records-save-event';\r\nimport { AssetEventType } from 'src/app/shared/enums/asset-workflow';\r\nimport { getDefaultValue } from 'src/app/shared/utilities/datatype.functions';\r\nimport { PostSaveEventService } from 'src/app/core/services/post-save-events.service';\r\nimport { DB_PRIMARY_KEY, SB_META_COL } from 'src/app/shared/constants/record.const';\r\nimport { SbData } from 'src/app/core/models/sb-data';\r\nimport { SignatureInfo } from 'src/app/core/models/signature-info';\r\nimport { SaveRecordsResponse } from 'src/app/core/models/save-records-response';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { UserPermissionLevels } from 'src/app/core/enums/shared';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ProjectsService } from 'src/app/core/services/projects.service';\r\nimport { ProjectVersion } from 'src/app/core/models/project-version';\r\nimport { AssetUrlPipe } from '../../shared/pipes/asset-url.pipe';\r\nimport { BlockUIModule } from 'primeng/blockui';\r\nimport { ValidationResultsDialogComponent } from '../../business-validation-results/validation-results-dialog.component';\r\nimport { PageErrorBlockComponent } from '../../shared/page-error-block/page-error-block.component';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { SignatureButtonComponent } from '../../signature/signature-button/signature-button.component';\r\nimport { SignaturesRendererComponent } from '../../signature/signatures-renderer/signatures-renderer.component';\r\nimport { DynamicFormBodyComponent } from '../dynamic-form-body/dynamic-form-body.component';\r\nimport { DynamicFormSectionComponent } from '../dynamic-form-section/dynamic-form-section.component';\r\n\r\n@Component({\r\n  selector: 'app-form-renderer',\r\n  templateUrl: './data-entry-form.component.html',\r\n  standalone: true,\r\n  imports: [NgIf, NgClass, DynamicFormSectionComponent, DynamicFormBodyComponent, SignaturesRendererComponent, SignatureButtonComponent, ButtonModule, PageErrorBlockComponent, ValidationResultsDialogComponent, BlockUIModule, AssetUrlPipe]\r\n})\r\nexport class DataEntryFormComponent implements OnInit {\r\n\r\n  @Input() dynamicForm: DynamicForm;\r\n  @Input() datastore: Datastore;\r\n  @Input() recordId = '';\r\n  @Input() triggerSaveEvent = new EventEmitter<void>();\r\n  @Input() projectId: number;\r\n  @Input() projectVersionId: number;\r\n  @Input() isPreview = false;\r\n  @Input() formBodyOnly = false;\r\n  @Input() userPermissionLevel: UserPermissionLevels = UserPermissionLevels.EditAndDelete;\r\n\r\n  @Output() afterSave: EventEmitter<boolean> = new EventEmitter();\r\n  @Output() beforeSave: EventEmitter<void> = new EventEmitter();\r\n\r\n  /**\r\n   * Event emitted when the form has finished loading.\r\n   * The boolean value indicates if the form was loaded successfully or with an error.\r\n   * - `true`: Form loaded without any errors.\r\n   * - `false`: Form loaded with errors.\r\n   */\r\n  @Output() formLoad: EventEmitter<boolean> = new EventEmitter();\r\n\r\n  isChildComponent = false;\r\n  jsonSchemaParam = new JSONSchemaParam();\r\n  submittedFormData;\r\n  savedDynamicFormSubscription;\r\n  isSubmitted = false;\r\n  queryParamPrimaryKey: any;\r\n  selectedSignature: SignatureInfo;\r\n  projectVersion: ProjectVersion;\r\n  signatures: SignatureInfo[];\r\n  signatureUpdated = false;\r\n  showSaveErrorsBlock = false;\r\n  showWarningsBlock = false;\r\n  showValidationResults = false;\r\n  showSpinner = false;\r\n  showDynamicForm = false;\r\n  pageLoadErrorObj: { title: string, details: string, moreDetails?: string };\r\n  saveRecordsResponse: SaveRecordsResponse;\r\n  masterKeyColumn = \"\";\r\n  isEmbeddedView = false;\r\n  isOTC = false;\r\n  initialData: any;\r\n\r\n  // used in the html\r\n  protected readonly DynamicFormPostSaveActions = DynamicFormPostSaveActions;\r\n  protected readonly DynamicFormAreas = DynamicFormLayoutAreas;\r\n\r\n  constructor(\r\n    private activatedRoute: ActivatedRoute,\r\n    private router: Router,\r\n    private datastoresService: DatastoresService,\r\n    private changeTrackingService: ChangeTrackingService,\r\n    private dynamicFormService: DynamicFormsService,\r\n    private notificationService: NotificationService,\r\n    private userActivityService: UserActivityService,\r\n    private location: Location,\r\n    private postSaveEventService: PostSaveEventService,\r\n    private projectsService: ProjectsService,\r\n    @Inject(CommunicationToken) private communicationService: CommunicationServiceType) {\r\n  }\r\n\r\n  async ngOnInit() {\r\n    this.showSpinner = true;\r\n\r\n    // Hide navigation if this is an embedded view\r\n    this.isEmbeddedView = this.router?.url?.includes('embeddedview');\r\n    this.isOTC = this.router?.url?.includes('otc');\r\n    if (this.isEmbeddedView)\r\n      this.hideNavigationApp();\r\n\r\n\r\n    if (this.dynamicForm) {\r\n      this.isChildComponent = true;\r\n      await this.setFormDetails();\r\n\r\n      this.showDynamicForm = !this.pageLoadErrorObj;\r\n      this.showSpinner = false;\r\n      this.formLoad.emit(this.showDynamicForm);\r\n\r\n      return;\r\n    }\r\n\r\n    if (this.activatedRoute) {\r\n      this.activatedRoute.params.subscribe(async params => {\r\n        this.projectId = params.projectId;\r\n        this.projectVersionId = params.projectVersionId;\r\n        this.recordId = params.recordId;\r\n\r\n        // Check if the route variable is coming from an embedded iFrame request (URL variable: primaryKey)\r\n        this.queryParamPrimaryKey = this.activatedRoute.snapshot.queryParams.primaryKey;\r\n\r\n        try {\r\n          // Logging user activity\r\n          if (!this.isPreview && !this.isOTC) {\r\n            this.projectVersion = await this.getProjectVersion();\r\n            this.userActivityService.addUserActivity(\r\n              new UserActivity({ projectId: this.projectId, projectVersionId: this.projectVersionId, url: window.location.pathname, dynamicFormId: params.dynamicFormId })\r\n            );\r\n          }\r\n\r\n          await this.getDynamicForm(params.dynamicFormId);\r\n\r\n          if (this.dynamicForm)\r\n            await this.setFormDetails();\r\n\r\n          this.setFormInitialData();\r\n\r\n          if (!this.pageLoadErrorObj) {\r\n            this.changeTrackingService.initBatchChange(params.projectId, params.projectVersionId);\r\n            this.showDynamicForm = true;\r\n          }\r\n\r\n          this.formLoad.emit(this.showDynamicForm);\r\n        } catch (ex) {\r\n          this.pageLoadErrorObj = this.getPageErrorDetails('Other', ex);\r\n        }\r\n\r\n        this.showSpinner = false;\r\n      });\r\n    }\r\n  }\r\n\r\n  setFormInitialData() {\r\n\r\n    if (!this.datastore || !this.activatedRoute?.snapshot?.queryParams?.initialData) return;\r\n\r\n    // Get the data query parameter if it exists\r\n    const initialDataParam = this.activatedRoute.snapshot.queryParams.initialData;\r\n\r\n    // Try to parse the data parameter as JSON if it exists\r\n    if (initialDataParam) {\r\n      try {\r\n        const queryParamData = JSON.parse(decodeURIComponent(initialDataParam));\r\n        const fixedData = { [DB_PRIMARY_KEY]: undefined, ...this.datastoresService.fixDataWithSchema(this.datastore.baseSchema, queryParamData), [SB_META_COL]: undefined };\r\n        console.log('fixedData', fixedData);\r\n        this.jsonSchemaParam.data = fixedData as SbData;\r\n      } catch (error) {\r\n        console.error('Error parsing data query parameter:', error);\r\n      }\r\n    }\r\n  }\r\n\r\n  async getDynamicForm(dynamicFormId: string) {\r\n    try {\r\n      if (dynamicFormId) {\r\n        const dynamicForm = await firstValueFrom(\r\n          this.dynamicFormService.getDynamicForm(this.projectId, this.projectVersionId, dynamicFormId)\r\n        );\r\n\r\n        this.dynamicForm = dynamicForm;\r\n        this.buildBreadcrumb();\r\n      }\r\n\r\n      if (!this.dynamicForm)\r\n        this.pageLoadErrorObj = this.getPageErrorDetails('Form');\r\n    } catch (ex) {\r\n      this.pageLoadErrorObj = this.getPageErrorDetails('Other', ex);\r\n    }\r\n  }\r\n\r\n  async getDatastore() {\r\n    if (!this.dynamicForm.datastoreId) {\r\n      if (!this.isPreview) {\r\n        this.notificationService.showError('Error', `No datastore is selected! Please check the form's configuration.`);\r\n      }\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const datastore = await firstValueFrom(this.datastoresService.getDatastore(this.dynamicForm.datastoreId));\r\n      if (!datastore) {\r\n        this.pageLoadErrorObj = this.getPageErrorDetails('Datastore');\r\n        return;\r\n      }\r\n\r\n      this.datastore = datastore;\r\n    } catch (ex) {\r\n      this.pageLoadErrorObj = this.getPageErrorDetails('Other', ex);\r\n    }\r\n  }\r\n\r\n  async setFormData() {\r\n    if (!this.datastore)\r\n      return;\r\n\r\n    // setting master key column for validation results if it is not being set\r\n    if (!this.masterKeyColumn) {\r\n      this.masterKeyColumn = this.datastore?.primaryKey;\r\n      if (!this.masterKeyColumn) {\r\n        const fieldsConfig = this.dynamicFormService.getFieldsConfig(this.dynamicForm);\r\n        this.masterKeyColumn = fieldsConfig?.length ? fieldsConfig[0]?.key?.toString() : '';\r\n      }\r\n    }\r\n\r\n    if (this.recordId || this.queryParamPrimaryKey) {\r\n      this.jsonSchemaParam.action = CRUDActions.Update;\r\n\r\n      try {\r\n        const data = await firstValueFrom(\r\n          this.queryParamPrimaryKey\r\n            ? this.datastoresService.getRecordByPrimaryKey(this.dynamicForm.datastoreId, this.queryParamPrimaryKey)\r\n            : this.datastoresService.getRecord(this.dynamicForm.datastoreId, this.recordId)\r\n        );\r\n\r\n        if (data) {\r\n          // Set the record id if the form is loaded with the primary key\r\n          if (this.queryParamPrimaryKey)\r\n            this.recordId = data[DB_PRIMARY_KEY];\r\n\r\n          this.jsonSchemaParam.data = data;\r\n          this.jsonSchemaParam.originalData = deepCopy(data);\r\n\r\n          if (data.__sbmeta?.Signatures?.length) {\r\n            this.selectedSignature = data.__sbmeta?.Signatures[0];\r\n            this.signatures = data.__sbmeta?.Signatures;\r\n          }\r\n\r\n          return;\r\n        }\r\n\r\n        this.pageLoadErrorObj = this.getPageErrorDetails('Data');\r\n      } catch (ex) {\r\n        this.pageLoadErrorObj = this.getPageErrorDetails('Other', ex);\r\n      }\r\n    }\r\n  }\r\n\r\n  async setFormDetails() {\r\n    await this.getDatastore();\r\n    await this.setFormData();\r\n  }\r\n\r\n  formOnSubmit(data: any) {\r\n    this.setDataEntryChanges(data);\r\n    this.saveChanges();\r\n  }\r\n\r\n  saveChanges() {\r\n    this.beforeSave.emit();\r\n\r\n    this.showSpinner = true;\r\n    this.datastoresService.saveRecords(this.changeTrackingService.batchChange, this.datastore.id)\r\n      .subscribe({\r\n        next: (saveRecordsResponse: SaveRecordsResponse) => {\r\n          this.saveRecordsResponse = saveRecordsResponse;\r\n          this.showValidationResults = saveRecordsResponse?.failedValidations?.length > 0;\r\n          this.showSaveErrorsBlock = saveRecordsResponse?.hasFailures;\r\n          this.showWarningsBlock = this.showValidationResults && !saveRecordsResponse?.hasFailures;\r\n\r\n          if (!saveRecordsResponse.hasFailures)\r\n            this.notificationService.showSuccess('Success!', 'Form has been submitted successfully.');\r\n\r\n          if (!this.showValidationResults)\r\n            this.doPostSaveActions();\r\n\r\n          this.showSpinner = false;\r\n        },\r\n        error: err => {\r\n          this.showSpinner = false;\r\n          const primaryKeyName = this.dynamicFormService.getFieldsConfig(this.dynamicForm)[0]?.props?.label ?? this.masterKeyColumn;\r\n\r\n          if (err.error?.toString().includes('DuplicateKey')) {\r\n            this.notificationService.showError('Error', `Duplicate value for primary key: ${primaryKeyName}. Please use a unique value.`)\r\n          } else {\r\n            this.notificationService.showError('Error', err.error ?? err.message);\r\n          }\r\n\r\n          this.afterSave.emit(false);\r\n        }\r\n      });\r\n  }\r\n\r\n  doPostSaveActions() {\r\n    // If it is a child component, we just emit the after save and parent component knows what to do\r\n    if (this.isChildComponent) {\r\n      this.afterSave.emit(true);\r\n      return;\r\n    }\r\n\r\n    this.saveRecordsResponse = null;\r\n\r\n    switch (this.dynamicForm.additionalSettings?.postSaveAction) {\r\n      case DynamicFormPostSaveActions.Redirect:\r\n        window.location.href = this.dynamicForm.additionalSettings.actionContent;\r\n        break;\r\n      case DynamicFormPostSaveActions.Reload:\r\n        this.reloadForm();\r\n        break;\r\n      default:\r\n        this.changeTrackingService.resetBatchChangeVariables();\r\n        this.showDynamicForm = false;\r\n        this.isSubmitted = true;\r\n    }\r\n  }\r\n\r\n  handleContinue() {\r\n    // We should reset the data as the form is being reloaded\r\n    this.jsonSchemaParam = new JSONSchemaParam();\r\n\r\n    this.showWarningsBlock = false;\r\n    this.doPostSaveActions();\r\n  }\r\n\r\n  async editLastEntry() {\r\n    if (!this.saveRecordsResponse?.recordIds?.length) {\r\n      return;\r\n    }\r\n\r\n    this.showSpinner = true;\r\n    this.recordId = this.saveRecordsResponse.recordIds[0];\r\n    this.changeTrackingService.resetBatchChangeVariables();\r\n    await this.setFormData();\r\n\r\n    this.showSpinner = false;\r\n    this.saveRecordsResponse = null;\r\n    this.showWarningsBlock = false;\r\n  }\r\n\r\n  async addInvokeWorkflowEvent() {\r\n    const saveFormEvent = this.dynamicForm.additionalSettings?.formEvents\r\n      .find(formEvent => formEvent.eventType === AssetEventType.FormSave);\r\n    if (!saveFormEvent) {\r\n      return;\r\n    }\r\n\r\n    this.postSaveEventService.addInvokeWorkflowEvent({\r\n      recordsSaveEvent: new RecordsSaveEvent(\r\n        this.dynamicForm.id, this.dynamicForm.name, ModifierSourceType.Form, this.datastore.name,\r\n        this.dynamicForm.datastoreId),\r\n      assetEvent: this.dynamicForm.additionalSettings?.formEvents.find(formEvent => formEvent.eventType === AssetEventType.FormSave)\r\n    });\r\n  }\r\n\r\n  private setDataEntryChanges(event: any) {\r\n    const data = event.data as SbData;\r\n    if (!data) {\r\n      return;\r\n    }\r\n\r\n    if (event.sendEmail) {\r\n      this.setEmailSetup();\r\n    }\r\n\r\n    if (this.dynamicForm.additionalSettings?.invokeWorkflow) {\r\n      this.addInvokeWorkflowEvent();\r\n    }\r\n\r\n    this.jsonSchemaParam.data = data;\r\n\r\n    let id: string;\r\n    switch (this.jsonSchemaParam.action) {\r\n      case CRUDActions.Update:\r\n        {\r\n          id = this.jsonSchemaParam.originalData[DB_PRIMARY_KEY];\r\n\r\n          this.dynamicForm.fieldsConfig.forEach(field => {\r\n            const fieldName = field.key.toString();\r\n            const datastoreField = this.datastore.baseSchema.properties[fieldName];\r\n\r\n            if (datastoreField) {\r\n              let fieldValue = data[fieldName];\r\n\r\n              if (fieldValue != this.jsonSchemaParam.originalData[fieldName]) {\r\n                fieldValue = fieldValue == null ? getDefaultValue(datastoreField.type, datastoreField.format) : fieldValue;\r\n                this.changeTrackingService.trackChange(id, fieldName, this.jsonSchemaParam.originalData[fieldName], fieldValue, CRUDActions.Update);\r\n              }\r\n            }\r\n          });\r\n        }\r\n        break;\r\n      case CRUDActions.Create:\r\n        {\r\n          id = \"__0\";\r\n\r\n          this.dynamicForm.fieldsConfig.forEach(field => {\r\n            const fieldName = field.key.toString();\r\n            const datastoreField = this.datastore.baseSchema.properties[fieldName];\r\n\r\n            if (datastoreField) {\r\n              const fieldValue = (data[fieldName] !== undefined && data[fieldName] !== null) ? data[fieldName] : getDefaultValue(datastoreField.type, datastoreField.format);\r\n              this.changeTrackingService.trackChange(id, fieldName, undefined, fieldValue, CRUDActions.Create);\r\n            }\r\n          });\r\n        }\r\n        break;\r\n    }\r\n\r\n    this.changeTrackingService.trackAttachments(id, event.aliasIds);\r\n\r\n    // updating the signature if it has been changed\r\n    if (this.signatureUpdated)\r\n      this.changeTrackingService.trackSignatures(id, this.signatures);\r\n\r\n    // we need to replace the original data with what is currently saved in the batch changes, then with the next save we can differniate the changes\r\n    this.jsonSchemaParam.originalData = deepCopy(data);\r\n  }\r\n\r\n  submit() {\r\n    this.triggerSaveEvent.emit();\r\n  }\r\n\r\n  hideNavigationApp() {\r\n    this.communicationService.next({\r\n      fromAppName: this.communicationService.currentAppName,\r\n      toAppName: this.communicationService.navigationAppName,\r\n      eventType: 'HideNavigationBar',\r\n      data: {}\r\n    });\r\n  }\r\n\r\n  reloadForm() {\r\n    // if the route is different change the route\r\n    if (this.activatedRoute.snapshot.params['recordId']) {\r\n      this.location.go(`/saltboxdataapp/data-entry/${this.projectId}/${this.projectVersionId}/Forms/${this.dynamicForm.id}`);\r\n      return;\r\n    }\r\n\r\n    this.recordId = undefined;\r\n    this.signatureUpdated = false;\r\n    this.selectedSignature = undefined;\r\n    this.signatures = [];\r\n\r\n    this.changeTrackingService.resetBatchChangeVariables();\r\n    this.dynamicFormService.resetDataEntryForm.next();\r\n\r\n    window.scrollTo(0, 0);\r\n  }\r\n\r\n  setEmailSetup() {\r\n    const emailSetup: EmailSetup = {\r\n      subject: 'Link to your responses',\r\n      message: this.createEmailMessage()\r\n    };\r\n\r\n    this.postSaveEventService.addFormEmailEvent(emailSetup);\r\n  }\r\n\r\n  createEmailMessage() {\r\n    const emailMessage = this.dynamicForm.additionalSettings.emailMessage.replace(/<p>/g, '').replace(/<\\/p>/g, '\\n') +\r\n      `\\n${window.location.href}/{{recordId}}` +\r\n      '\\n\\nThanks and Regards,\\nSaltbox';\r\n\r\n    return emailMessage;\r\n  }\r\n\r\n  signatureChange(signature) {\r\n    this.signatureUpdated = true;\r\n    this.signatures = signature ? [signature] : [];\r\n  }\r\n\r\n  validationDialogVisibaleChanged(visible: boolean) {\r\n    // if this is a child component and user closes the warnings we will emit the after save event\r\n    if (this.isChildComponent && !visible && this.showWarningsBlock) {\r\n      this.afterSave.emit(true);\r\n      return;\r\n    }\r\n  }\r\n\r\n\r\n  getProjectVersion() {\r\n    return this.projectsService.getProjectVersion(`${this.projectVersionId}`);\r\n  }\r\n\r\n  async buildBreadcrumb() {\r\n    if (this.projectVersion) {\r\n      const items: MenuItem[] = [\r\n        { label: 'My Apps', routerLink: '/dashboard-v3' },\r\n        {\r\n          label: this.projectVersion.appManifest?.name,\r\n          routerLink: `/app-view/${this.dynamicForm.projectId}/${this.dynamicForm.projectVersionId}`\r\n        },\r\n        { label: this.dynamicForm?.name }\r\n      ];\r\n      this.communicationService.updateBreadcrumb(items);\r\n    }\r\n  }\r\n\r\n  private getPageErrorDetails(type: 'Data' | 'Datastore' | 'Form' | 'Other', ex = null) {\r\n    switch (type) {\r\n      case 'Data':\r\n        return {\r\n          title: 'Data not found!',\r\n          details: `<p>Record with the ${this.queryParamPrimaryKey ? 'primary key' : 'record id'}:'<b>${this.queryParamPrimaryKey ?? this.recordId}</b>' \r\n          was not found.</p><p>The link you used is broken or the data may have been deleted.</p>`,\r\n          moreDetails: `<p><b>Error:</b> Record with the ${this.queryParamPrimaryKey ? 'primary key' : 'record id'}:'<b>${this.queryParamPrimaryKey ?? this.recordId}\r\n          </b>'was not found in the datastore '<b>${this.datastore?.description}</b>'.</p>`\r\n        };\r\n      case 'Datastore':\r\n        return {\r\n          title: 'Datastore not found!',\r\n          details: `<p>The link you used is broken or the datastore may have been deleted.</p>`\r\n        };\r\n      case 'Form':\r\n        return {\r\n          title: 'Form not found!',\r\n          details: `<p>The link you used is broken or the form may have been deleted.</p>`\r\n        };\r\n      case 'Other':\r\n        return {\r\n          title: 'Uh oh!',\r\n          details: '<p>' + (ex instanceof HttpErrorResponse ? ex.statusText : ex?.error ?? ex?.message) + '</p>'\r\n        };\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAUC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,YAAY,QAAQ,eAAe;AAEtF,SAAmBC,IAAI,EAAEC,OAAO,QAAQ,iBAAiB;AAGzD,SAASC,QAAQ,QAAQ,yCAAyC;AAClE,SAASC,eAAe,QAAQ,uCAAuC;AACvE,SAASC,WAAW,EAAEC,kBAAkB,QAAQ,2BAA2B;AAG3E,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,YAAY,QAAQ,qCAAqC;AAIlE,SAAmCC,kBAAkB,QAAQ,gDAAgD;AAC7G,SAASC,sBAAsB,EAAEC,0BAA0B,QAAQ,uBAAuB;AAE1F,SAASC,gBAAgB,QAAQ,0CAA0C;AAC3E,SAASC,cAAc,QAAQ,qCAAqC;AACpE,SAASC,eAAe,QAAQ,6CAA6C;AAE7E,SAASC,cAAc,EAAEC,WAAW,QAAQ,uCAAuC;AAInF,SAASC,cAAc,QAAQ,MAAM;AACrC,SAASC,oBAAoB,QAAQ,2BAA2B;AAIhE,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,gCAAgC,QAAQ,uEAAuE;AACxH,SAASC,uBAAuB,QAAQ,0DAA0D;AAClG,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,wBAAwB,QAAQ,6DAA6D;AACtG,SAASC,2BAA2B,QAAQ,mEAAmE;AAC/G,SAASC,wBAAwB,QAAQ,kDAAkD;AAC3F,SAASC,2BAA2B,QAAQ,wDAAwD;AAQ7F,IAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAiDjCC,YACUC,cAA8B,EAC9BC,MAAc,EACdC,iBAAoC,EACpCC,qBAA4C,EAC5CC,kBAAuC,EACvCC,mBAAwC,EACxCC,mBAAwC,EACxCC,QAAkB,EAClBC,oBAA0C,EAC1CC,eAAgC,EACZC,oBAAsD;IAV1E,KAAAV,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACa,KAAAC,oBAAoB,GAApBA,oBAAoB;IAxDjD,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,gBAAgB,GAAG,IAAI1C,YAAY,EAAQ;IAG3C,KAAA2C,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,mBAAmB,GAAyB3B,oBAAoB,CAAC4B,aAAa;IAE7E,KAAAC,SAAS,GAA0B,IAAI/C,YAAY,EAAE;IACrD,KAAAgD,UAAU,GAAuB,IAAIhD,YAAY,EAAE;IAE7D;;;;;;IAMU,KAAAiD,QAAQ,GAA0B,IAAIjD,YAAY,EAAE;IAE9D,KAAAkD,gBAAgB,GAAG,KAAK;IACxB,KAAAC,eAAe,GAAG,IAAI/C,eAAe,EAAE;IAGvC,KAAAgD,WAAW,GAAG,KAAK;IAKnB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,mBAAmB,GAAG,KAAK;IAC3B,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,eAAe,GAAG,KAAK;IAGvB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,KAAK,GAAG,KAAK;IAGb;IACmB,KAAAlD,0BAA0B,GAAGA,0BAA0B;IACvD,KAAAmD,gBAAgB,GAAGpD,sBAAsB;EAc5D;EAEMqD,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZD,KAAI,CAACP,WAAW,GAAG,IAAI;MAEvB;MACAO,KAAI,CAACJ,cAAc,GAAGI,KAAI,CAACjC,MAAM,EAAEmC,GAAG,EAAEC,QAAQ,CAAC,cAAc,CAAC;MAChEH,KAAI,CAACH,KAAK,GAAGG,KAAI,CAACjC,MAAM,EAAEmC,GAAG,EAAEC,QAAQ,CAAC,KAAK,CAAC;MAC9C,IAAIH,KAAI,CAACJ,cAAc,EACrBI,KAAI,CAACI,iBAAiB,EAAE;MAG1B,IAAIJ,KAAI,CAACK,WAAW,EAAE;QACpBL,KAAI,CAACd,gBAAgB,GAAG,IAAI;QAC5B,MAAMc,KAAI,CAACM,cAAc,EAAE;QAE3BN,KAAI,CAACN,eAAe,GAAG,CAACM,KAAI,CAACO,gBAAgB;QAC7CP,KAAI,CAACP,WAAW,GAAG,KAAK;QACxBO,KAAI,CAACf,QAAQ,CAACuB,IAAI,CAACR,KAAI,CAACN,eAAe,CAAC;QAExC;MACF;MAEA,IAAIM,KAAI,CAAClC,cAAc,EAAE;QACvBkC,KAAI,CAAClC,cAAc,CAAC2C,MAAM,CAACC,SAAS;UAAA,IAAAC,IAAA,GAAAV,iBAAA,CAAC,WAAMQ,MAAM,EAAG;YAClDT,KAAI,CAACY,SAAS,GAAGH,MAAM,CAACG,SAAS;YACjCZ,KAAI,CAACa,gBAAgB,GAAGJ,MAAM,CAACI,gBAAgB;YAC/Cb,KAAI,CAACvB,QAAQ,GAAGgC,MAAM,CAAChC,QAAQ;YAE/B;YACAuB,KAAI,CAACc,oBAAoB,GAAGd,KAAI,CAAClC,cAAc,CAACiD,QAAQ,CAACC,WAAW,CAACC,UAAU;YAE/E,IAAI;cACF;cACA,IAAI,CAACjB,KAAI,CAACrB,SAAS,IAAI,CAACqB,KAAI,CAACH,KAAK,EAAE;gBAClCG,KAAI,CAACkB,cAAc,SAASlB,KAAI,CAACmB,iBAAiB,EAAE;gBACpDnB,KAAI,CAAC5B,mBAAmB,CAACgD,eAAe,CACtC,IAAI5E,YAAY,CAAC;kBAAEoE,SAAS,EAAEZ,KAAI,CAACY,SAAS;kBAAEC,gBAAgB,EAAEb,KAAI,CAACa,gBAAgB;kBAAEX,GAAG,EAAEmB,MAAM,CAAChD,QAAQ,CAACiD,QAAQ;kBAAEC,aAAa,EAAEd,MAAM,CAACc;gBAAa,CAAE,CAAC,CAC7J;cACH;cAEA,MAAMvB,KAAI,CAACwB,cAAc,CAACf,MAAM,CAACc,aAAa,CAAC;cAE/C,IAAIvB,KAAI,CAACK,WAAW,EAClB,MAAML,KAAI,CAACM,cAAc,EAAE;cAE7BN,KAAI,CAACyB,kBAAkB,EAAE;cAEzB,IAAI,CAACzB,KAAI,CAACO,gBAAgB,EAAE;gBAC1BP,KAAI,CAAC/B,qBAAqB,CAACyD,eAAe,CAACjB,MAAM,CAACG,SAAS,EAAEH,MAAM,CAACI,gBAAgB,CAAC;gBACrFb,KAAI,CAACN,eAAe,GAAG,IAAI;cAC7B;cAEAM,KAAI,CAACf,QAAQ,CAACuB,IAAI,CAACR,KAAI,CAACN,eAAe,CAAC;YAC1C,CAAC,CAAC,OAAOiC,EAAE,EAAE;cACX3B,KAAI,CAACO,gBAAgB,GAAGP,KAAI,CAAC4B,mBAAmB,CAAC,OAAO,EAAED,EAAE,CAAC;YAC/D;YAEA3B,KAAI,CAACP,WAAW,GAAG,KAAK;UAC1B,CAAC;UAAA,iBAAAoC,EAAA;YAAA,OAAAlB,IAAA,CAAAmB,KAAA,OAAAC,SAAA;UAAA;QAAA,IAAC;MACJ;IAAC;EACH;EAEAN,kBAAkBA,CAAA;IAEhB,IAAI,CAAC,IAAI,CAACO,SAAS,IAAI,CAAC,IAAI,CAAClE,cAAc,EAAEiD,QAAQ,EAAEC,WAAW,EAAEiB,WAAW,EAAE;IAEjF;IACA,MAAMC,gBAAgB,GAAG,IAAI,CAACpE,cAAc,CAACiD,QAAQ,CAACC,WAAW,CAACiB,WAAW;IAE7E;IACA,IAAIC,gBAAgB,EAAE;MACpB,IAAI;QACF,MAAMC,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACC,kBAAkB,CAACJ,gBAAgB,CAAC,CAAC;QACvE,MAAMK,SAAS,GAAG;UAAE,CAACxF,cAAc,GAAGyF,SAAS;UAAE,GAAG,IAAI,CAACxE,iBAAiB,CAACyE,iBAAiB,CAAC,IAAI,CAACT,SAAS,CAACU,UAAU,EAAEP,cAAc,CAAC;UAAE,CAACnF,WAAW,GAAGwF;QAAS,CAAE;QACnKG,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEL,SAAS,CAAC;QACnC,IAAI,CAACpD,eAAe,CAAC0D,IAAI,GAAGN,SAAmB;MACjD,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdH,OAAO,CAACG,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC7D;IACF;EACF;EAEMtB,cAAcA,CAACD,aAAqB;IAAA,IAAAwB,MAAA;IAAA,OAAA9C,iBAAA;MACxC,IAAI;QACF,IAAIsB,aAAa,EAAE;UACjB,MAAMlB,WAAW,SAASpD,cAAc,CACtC8F,MAAI,CAAC7E,kBAAkB,CAACsD,cAAc,CAACuB,MAAI,CAACnC,SAAS,EAAEmC,MAAI,CAAClC,gBAAgB,EAAEU,aAAa,CAAC,CAC7F;UAEDwB,MAAI,CAAC1C,WAAW,GAAGA,WAAW;UAC9B0C,MAAI,CAACC,eAAe,EAAE;QACxB;QAEA,IAAI,CAACD,MAAI,CAAC1C,WAAW,EACnB0C,MAAI,CAACxC,gBAAgB,GAAGwC,MAAI,CAACnB,mBAAmB,CAAC,MAAM,CAAC;MAC5D,CAAC,CAAC,OAAOD,EAAE,EAAE;QACXoB,MAAI,CAACxC,gBAAgB,GAAGwC,MAAI,CAACnB,mBAAmB,CAAC,OAAO,EAAED,EAAE,CAAC;MAC/D;IAAC;EACH;EAEMsB,YAAYA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAjD,iBAAA;MAChB,IAAI,CAACiD,MAAI,CAAC7C,WAAW,CAAC8C,WAAW,EAAE;QACjC,IAAI,CAACD,MAAI,CAACvE,SAAS,EAAE;UACnBuE,MAAI,CAAC/E,mBAAmB,CAACiF,SAAS,CAAC,OAAO,EAAE,kEAAkE,CAAC;QACjH;QACA;MACF;MAEA,IAAI;QACF,MAAMpB,SAAS,SAAS/E,cAAc,CAACiG,MAAI,CAAClF,iBAAiB,CAACiF,YAAY,CAACC,MAAI,CAAC7C,WAAW,CAAC8C,WAAW,CAAC,CAAC;QACzG,IAAI,CAACnB,SAAS,EAAE;UACdkB,MAAI,CAAC3C,gBAAgB,GAAG2C,MAAI,CAACtB,mBAAmB,CAAC,WAAW,CAAC;UAC7D;QACF;QAEAsB,MAAI,CAAClB,SAAS,GAAGA,SAAS;MAC5B,CAAC,CAAC,OAAOL,EAAE,EAAE;QACXuB,MAAI,CAAC3C,gBAAgB,GAAG2C,MAAI,CAACtB,mBAAmB,CAAC,OAAO,EAAED,EAAE,CAAC;MAC/D;IAAC;EACH;EAEM0B,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAArD,iBAAA;MACf,IAAI,CAACqD,MAAI,CAACtB,SAAS,EACjB;MAEF;MACA,IAAI,CAACsB,MAAI,CAAC3D,eAAe,EAAE;QACzB2D,MAAI,CAAC3D,eAAe,GAAG2D,MAAI,CAACtB,SAAS,EAAEf,UAAU;QACjD,IAAI,CAACqC,MAAI,CAAC3D,eAAe,EAAE;UACzB,MAAM4D,YAAY,GAAGD,MAAI,CAACpF,kBAAkB,CAACsF,eAAe,CAACF,MAAI,CAACjD,WAAW,CAAC;UAC9EiD,MAAI,CAAC3D,eAAe,GAAG4D,YAAY,EAAEE,MAAM,GAAGF,YAAY,CAAC,CAAC,CAAC,EAAEG,GAAG,EAAEC,QAAQ,EAAE,GAAG,EAAE;QACrF;MACF;MAEA,IAAIL,MAAI,CAAC7E,QAAQ,IAAI6E,MAAI,CAACxC,oBAAoB,EAAE;QAC9CwC,MAAI,CAACnE,eAAe,CAACyE,MAAM,GAAGvH,WAAW,CAACwH,MAAM;QAEhD,IAAI;UACF,MAAMhB,IAAI,SAAS5F,cAAc,CAC/BqG,MAAI,CAACxC,oBAAoB,GACrBwC,MAAI,CAACtF,iBAAiB,CAAC8F,qBAAqB,CAACR,MAAI,CAACjD,WAAW,CAAC8C,WAAW,EAAEG,MAAI,CAACxC,oBAAoB,CAAC,GACrGwC,MAAI,CAACtF,iBAAiB,CAAC+F,SAAS,CAACT,MAAI,CAACjD,WAAW,CAAC8C,WAAW,EAAEG,MAAI,CAAC7E,QAAQ,CAAC,CAClF;UAED,IAAIoE,IAAI,EAAE;YACR;YACA,IAAIS,MAAI,CAACxC,oBAAoB,EAC3BwC,MAAI,CAAC7E,QAAQ,GAAGoE,IAAI,CAAC9F,cAAc,CAAC;YAEtCuG,MAAI,CAACnE,eAAe,CAAC0D,IAAI,GAAGA,IAAI;YAChCS,MAAI,CAACnE,eAAe,CAAC6E,YAAY,GAAG7H,QAAQ,CAAC0G,IAAI,CAAC;YAElD,IAAIA,IAAI,CAACoB,QAAQ,EAAEC,UAAU,EAAET,MAAM,EAAE;cACrCH,MAAI,CAACa,iBAAiB,GAAGtB,IAAI,CAACoB,QAAQ,EAAEC,UAAU,CAAC,CAAC,CAAC;cACrDZ,MAAI,CAACc,UAAU,GAAGvB,IAAI,CAACoB,QAAQ,EAAEC,UAAU;YAC7C;YAEA;UACF;UAEAZ,MAAI,CAAC/C,gBAAgB,GAAG+C,MAAI,CAAC1B,mBAAmB,CAAC,MAAM,CAAC;QAC1D,CAAC,CAAC,OAAOD,EAAE,EAAE;UACX2B,MAAI,CAAC/C,gBAAgB,GAAG+C,MAAI,CAAC1B,mBAAmB,CAAC,OAAO,EAAED,EAAE,CAAC;QAC/D;MACF;IAAC;EACH;EAEMrB,cAAcA,CAAA;IAAA,IAAA+D,MAAA;IAAA,OAAApE,iBAAA;MAClB,MAAMoE,MAAI,CAACpB,YAAY,EAAE;MACzB,MAAMoB,MAAI,CAAChB,WAAW,EAAE;IAAC;EAC3B;EAEAiB,YAAYA,CAACzB,IAAS;IACpB,IAAI,CAAC0B,mBAAmB,CAAC1B,IAAI,CAAC;IAC9B,IAAI,CAAC2B,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAACxF,UAAU,CAACwB,IAAI,EAAE;IAEtB,IAAI,CAACf,WAAW,GAAG,IAAI;IACvB,IAAI,CAACzB,iBAAiB,CAACyG,WAAW,CAAC,IAAI,CAACxG,qBAAqB,CAACyG,WAAW,EAAE,IAAI,CAAC1C,SAAS,CAAC2C,EAAE,CAAC,CAC1FjE,SAAS,CAAC;MACTkE,IAAI,EAAGC,mBAAwC,IAAI;QACjD,IAAI,CAACA,mBAAmB,GAAGA,mBAAmB;QAC9C,IAAI,CAACrF,qBAAqB,GAAGqF,mBAAmB,EAAEC,iBAAiB,EAAErB,MAAM,GAAG,CAAC;QAC/E,IAAI,CAACnE,mBAAmB,GAAGuF,mBAAmB,EAAEE,WAAW;QAC3D,IAAI,CAACxF,iBAAiB,GAAG,IAAI,CAACC,qBAAqB,IAAI,CAACqF,mBAAmB,EAAEE,WAAW;QAExF,IAAI,CAACF,mBAAmB,CAACE,WAAW,EAClC,IAAI,CAAC5G,mBAAmB,CAAC6G,WAAW,CAAC,UAAU,EAAE,uCAAuC,CAAC;QAE3F,IAAI,CAAC,IAAI,CAACxF,qBAAqB,EAC7B,IAAI,CAACyF,iBAAiB,EAAE;QAE1B,IAAI,CAACxF,WAAW,GAAG,KAAK;MAC1B,CAAC;MACDqD,KAAK,EAAEoC,GAAG,IAAG;QACX,IAAI,CAACzF,WAAW,GAAG,KAAK;QACxB,MAAM0F,cAAc,GAAG,IAAI,CAACjH,kBAAkB,CAACsF,eAAe,CAAC,IAAI,CAACnD,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE+E,KAAK,EAAEC,KAAK,IAAI,IAAI,CAAC1F,eAAe;QAEzH,IAAIuF,GAAG,CAACpC,KAAK,EAAEa,QAAQ,EAAE,CAACxD,QAAQ,CAAC,cAAc,CAAC,EAAE;UAClD,IAAI,CAAChC,mBAAmB,CAACiF,SAAS,CAAC,OAAO,EAAE,oCAAoC+B,cAAc,8BAA8B,CAAC;QAC/H,CAAC,MAAM;UACL,IAAI,CAAChH,mBAAmB,CAACiF,SAAS,CAAC,OAAO,EAAE8B,GAAG,CAACpC,KAAK,IAAIoC,GAAG,CAACI,OAAO,CAAC;QACvE;QAEA,IAAI,CAACvG,SAAS,CAACyB,IAAI,CAAC,KAAK,CAAC;MAC5B;KACD,CAAC;EACN;EAEAyE,iBAAiBA,CAAA;IACf;IACA,IAAI,IAAI,CAAC/F,gBAAgB,EAAE;MACzB,IAAI,CAACH,SAAS,CAACyB,IAAI,CAAC,IAAI,CAAC;MACzB;IACF;IAEA,IAAI,CAACqE,mBAAmB,GAAG,IAAI;IAE/B,QAAQ,IAAI,CAACxE,WAAW,CAACkF,kBAAkB,EAAEC,cAAc;MACzD,KAAK7I,0BAA0B,CAAC8I,QAAQ;QACtCpE,MAAM,CAAChD,QAAQ,CAACqH,IAAI,GAAG,IAAI,CAACrF,WAAW,CAACkF,kBAAkB,CAACI,aAAa;QACxE;MACF,KAAKhJ,0BAA0B,CAACiJ,MAAM;QACpC,IAAI,CAACC,UAAU,EAAE;QACjB;MACF;QACE,IAAI,CAAC5H,qBAAqB,CAAC6H,yBAAyB,EAAE;QACtD,IAAI,CAACpG,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACN,WAAW,GAAG,IAAI;IAC3B;EACF;EAEA2G,cAAcA,CAAA;IACZ;IACA,IAAI,CAAC5G,eAAe,GAAG,IAAI/C,eAAe,EAAE;IAE5C,IAAI,CAACmD,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAC0F,iBAAiB,EAAE;EAC1B;EAEMe,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAhG,iBAAA;MACjB,IAAI,CAACgG,MAAI,CAACpB,mBAAmB,EAAEqB,SAAS,EAAEzC,MAAM,EAAE;QAChD;MACF;MAEAwC,MAAI,CAACxG,WAAW,GAAG,IAAI;MACvBwG,MAAI,CAACxH,QAAQ,GAAGwH,MAAI,CAACpB,mBAAmB,CAACqB,SAAS,CAAC,CAAC,CAAC;MACrDD,MAAI,CAAChI,qBAAqB,CAAC6H,yBAAyB,EAAE;MACtD,MAAMG,MAAI,CAAC5C,WAAW,EAAE;MAExB4C,MAAI,CAACxG,WAAW,GAAG,KAAK;MACxBwG,MAAI,CAACpB,mBAAmB,GAAG,IAAI;MAC/BoB,MAAI,CAAC1G,iBAAiB,GAAG,KAAK;IAAC;EACjC;EAEM4G,sBAAsBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAnG,iBAAA;MAC1B,MAAMoG,aAAa,GAAGD,MAAI,CAAC/F,WAAW,CAACkF,kBAAkB,EAAEe,UAAU,CAClEC,IAAI,CAACC,SAAS,IAAIA,SAAS,CAACC,SAAS,KAAK5J,cAAc,CAAC6J,QAAQ,CAAC;MACrE,IAAI,CAACL,aAAa,EAAE;QAClB;MACF;MAEAD,MAAI,CAAC9H,oBAAoB,CAAC6H,sBAAsB,CAAC;QAC/CQ,gBAAgB,EAAE,IAAI/J,gBAAgB,CACpCwJ,MAAI,CAAC/F,WAAW,CAACsE,EAAE,EAAEyB,MAAI,CAAC/F,WAAW,CAACuG,IAAI,EAAEtK,kBAAkB,CAACuK,IAAI,EAAET,MAAI,CAACpE,SAAS,CAAC4E,IAAI,EACxFR,MAAI,CAAC/F,WAAW,CAAC8C,WAAW,CAAC;QAC/B2D,UAAU,EAAEV,MAAI,CAAC/F,WAAW,CAACkF,kBAAkB,EAAEe,UAAU,CAACC,IAAI,CAACC,SAAS,IAAIA,SAAS,CAACC,SAAS,KAAK5J,cAAc,CAAC6J,QAAQ;OAC9H,CAAC;IAAC;EACL;EAEQnC,mBAAmBA,CAACwC,KAAU;IACpC,MAAMlE,IAAI,GAAGkE,KAAK,CAAClE,IAAc;IACjC,IAAI,CAACA,IAAI,EAAE;MACT;IACF;IAEA,IAAIkE,KAAK,CAACC,SAAS,EAAE;MACnB,IAAI,CAACC,aAAa,EAAE;IACtB;IAEA,IAAI,IAAI,CAAC5G,WAAW,CAACkF,kBAAkB,EAAE2B,cAAc,EAAE;MACvD,IAAI,CAACf,sBAAsB,EAAE;IAC/B;IAEA,IAAI,CAAChH,eAAe,CAAC0D,IAAI,GAAGA,IAAI;IAEhC,IAAI8B,EAAU;IACd,QAAQ,IAAI,CAACxF,eAAe,CAACyE,MAAM;MACjC,KAAKvH,WAAW,CAACwH,MAAM;QACrB;UACEc,EAAE,GAAG,IAAI,CAACxF,eAAe,CAAC6E,YAAY,CAACjH,cAAc,CAAC;UAEtD,IAAI,CAACsD,WAAW,CAACkD,YAAY,CAAC4D,OAAO,CAACC,KAAK,IAAG;YAC5C,MAAMC,SAAS,GAAGD,KAAK,CAAC1D,GAAG,CAACC,QAAQ,EAAE;YACtC,MAAM2D,cAAc,GAAG,IAAI,CAACtF,SAAS,CAACU,UAAU,CAAC6E,UAAU,CAACF,SAAS,CAAC;YAEtE,IAAIC,cAAc,EAAE;cAClB,IAAIE,UAAU,GAAG3E,IAAI,CAACwE,SAAS,CAAC;cAEhC,IAAIG,UAAU,IAAI,IAAI,CAACrI,eAAe,CAAC6E,YAAY,CAACqD,SAAS,CAAC,EAAE;gBAC9DG,UAAU,GAAGA,UAAU,IAAI,IAAI,GAAG1K,eAAe,CAACwK,cAAc,CAACG,IAAI,EAAEH,cAAc,CAACI,MAAM,CAAC,GAAGF,UAAU;gBAC1G,IAAI,CAACvJ,qBAAqB,CAAC0J,WAAW,CAAChD,EAAE,EAAE0C,SAAS,EAAE,IAAI,CAAClI,eAAe,CAAC6E,YAAY,CAACqD,SAAS,CAAC,EAAEG,UAAU,EAAEnL,WAAW,CAACwH,MAAM,CAAC;cACrI;YACF;UACF,CAAC,CAAC;QACJ;QACA;MACF,KAAKxH,WAAW,CAACuL,MAAM;QACrB;UACEjD,EAAE,GAAG,KAAK;UAEV,IAAI,CAACtE,WAAW,CAACkD,YAAY,CAAC4D,OAAO,CAACC,KAAK,IAAG;YAC5C,MAAMC,SAAS,GAAGD,KAAK,CAAC1D,GAAG,CAACC,QAAQ,EAAE;YACtC,MAAM2D,cAAc,GAAG,IAAI,CAACtF,SAAS,CAACU,UAAU,CAAC6E,UAAU,CAACF,SAAS,CAAC;YAEtE,IAAIC,cAAc,EAAE;cAClB,MAAME,UAAU,GAAI3E,IAAI,CAACwE,SAAS,CAAC,KAAK7E,SAAS,IAAIK,IAAI,CAACwE,SAAS,CAAC,KAAK,IAAI,GAAIxE,IAAI,CAACwE,SAAS,CAAC,GAAGvK,eAAe,CAACwK,cAAc,CAACG,IAAI,EAAEH,cAAc,CAACI,MAAM,CAAC;cAC9J,IAAI,CAACzJ,qBAAqB,CAAC0J,WAAW,CAAChD,EAAE,EAAE0C,SAAS,EAAE7E,SAAS,EAAEgF,UAAU,EAAEnL,WAAW,CAACuL,MAAM,CAAC;YAClG;UACF,CAAC,CAAC;QACJ;QACA;IACJ;IAEA,IAAI,CAAC3J,qBAAqB,CAAC4J,gBAAgB,CAAClD,EAAE,EAAEoC,KAAK,CAACe,QAAQ,CAAC;IAE/D;IACA,IAAI,IAAI,CAACzI,gBAAgB,EACvB,IAAI,CAACpB,qBAAqB,CAAC8J,eAAe,CAACpD,EAAE,EAAE,IAAI,CAACP,UAAU,CAAC;IAEjE;IACA,IAAI,CAACjF,eAAe,CAAC6E,YAAY,GAAG7H,QAAQ,CAAC0G,IAAI,CAAC;EACpD;EAEAmF,MAAMA,CAAA;IACJ,IAAI,CAACtJ,gBAAgB,CAAC8B,IAAI,EAAE;EAC9B;EAEAJ,iBAAiBA,CAAA;IACf,IAAI,CAAC5B,oBAAoB,CAACoG,IAAI,CAAC;MAC7BqD,WAAW,EAAE,IAAI,CAACzJ,oBAAoB,CAAC0J,cAAc;MACrDC,SAAS,EAAE,IAAI,CAAC3J,oBAAoB,CAAC4J,iBAAiB;MACtD3B,SAAS,EAAE,mBAAmB;MAC9B5D,IAAI,EAAE;KACP,CAAC;EACJ;EAEAgD,UAAUA,CAAA;IACR;IACA,IAAI,IAAI,CAAC/H,cAAc,CAACiD,QAAQ,CAACN,MAAM,CAAC,UAAU,CAAC,EAAE;MACnD,IAAI,CAACpC,QAAQ,CAACgK,EAAE,CAAC,8BAA8B,IAAI,CAACzH,SAAS,IAAI,IAAI,CAACC,gBAAgB,UAAU,IAAI,CAACR,WAAW,CAACsE,EAAE,EAAE,CAAC;MACtH;IACF;IAEA,IAAI,CAAClG,QAAQ,GAAG+D,SAAS;IACzB,IAAI,CAACnD,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC8E,iBAAiB,GAAG3B,SAAS;IAClC,IAAI,CAAC4B,UAAU,GAAG,EAAE;IAEpB,IAAI,CAACnG,qBAAqB,CAAC6H,yBAAyB,EAAE;IACtD,IAAI,CAAC5H,kBAAkB,CAACoK,kBAAkB,CAAC1D,IAAI,EAAE;IAEjDvD,MAAM,CAACkH,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB;EAEAtB,aAAaA,CAAA;IACX,MAAMuB,UAAU,GAAe;MAC7BC,OAAO,EAAE,wBAAwB;MACjCnD,OAAO,EAAE,IAAI,CAACoD,kBAAkB;KACjC;IAED,IAAI,CAACpK,oBAAoB,CAACqK,iBAAiB,CAACH,UAAU,CAAC;EACzD;EAEAE,kBAAkBA,CAAA;IAChB,MAAME,YAAY,GAAG,IAAI,CAACvI,WAAW,CAACkF,kBAAkB,CAACqD,YAAY,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,GAC/G,KAAKxH,MAAM,CAAChD,QAAQ,CAACqH,IAAI,eAAe,GACxC,kCAAkC;IAEpC,OAAOkD,YAAY;EACrB;EAEAE,eAAeA,CAACC,SAAS;IACvB,IAAI,CAAC1J,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC+E,UAAU,GAAG2E,SAAS,GAAG,CAACA,SAAS,CAAC,GAAG,EAAE;EAChD;EAEAC,+BAA+BA,CAACC,OAAgB;IAC9C;IACA,IAAI,IAAI,CAAC/J,gBAAgB,IAAI,CAAC+J,OAAO,IAAI,IAAI,CAAC1J,iBAAiB,EAAE;MAC/D,IAAI,CAACR,SAAS,CAACyB,IAAI,CAAC,IAAI,CAAC;MACzB;IACF;EACF;EAGAW,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC5C,eAAe,CAAC4C,iBAAiB,CAAC,GAAG,IAAI,CAACN,gBAAgB,EAAE,CAAC;EAC3E;EAEMmC,eAAeA,CAAA;IAAA,IAAAkG,MAAA;IAAA,OAAAjJ,iBAAA;MACnB,IAAIiJ,MAAI,CAAChI,cAAc,EAAE;QACvB,MAAMiI,KAAK,GAAe,CACxB;UAAE9D,KAAK,EAAE,SAAS;UAAE+D,UAAU,EAAE;QAAe,CAAE,EACjD;UACE/D,KAAK,EAAE6D,MAAI,CAAChI,cAAc,CAACmI,WAAW,EAAEzC,IAAI;UAC5CwC,UAAU,EAAE,aAAaF,MAAI,CAAC7I,WAAW,CAACO,SAAS,IAAIsI,MAAI,CAAC7I,WAAW,CAACQ,gBAAgB;SACzF,EACD;UAAEwE,KAAK,EAAE6D,MAAI,CAAC7I,WAAW,EAAEuG;QAAI,CAAE,CAClC;QACDsC,MAAI,CAAC1K,oBAAoB,CAAC8K,gBAAgB,CAACH,KAAK,CAAC;MACnD;IAAC;EACH;EAEQvH,mBAAmBA,CAAC6F,IAA6C,EAAE9F,EAAE,GAAG,IAAI;IAClF,QAAQ8F,IAAI;MACV,KAAK,MAAM;QACT,OAAO;UACL8B,KAAK,EAAE,iBAAiB;UACxBC,OAAO,EAAE,sBAAsB,IAAI,CAAC1I,oBAAoB,GAAG,aAAa,GAAG,WAAW,QAAQ,IAAI,CAACA,oBAAoB,IAAI,IAAI,CAACrC,QAAQ;kGAChD;UACxFgL,WAAW,EAAE,oCAAoC,IAAI,CAAC3I,oBAAoB,GAAG,aAAa,GAAG,WAAW,QAAQ,IAAI,CAACA,oBAAoB,IAAI,IAAI,CAACrC,QAAQ;oDAChH,IAAI,CAACuD,SAAS,EAAE0H,WAAW;SACtE;MACH,KAAK,WAAW;QACd,OAAO;UACLH,KAAK,EAAE,sBAAsB;UAC7BC,OAAO,EAAE;SACV;MACH,KAAK,MAAM;QACT,OAAO;UACLD,KAAK,EAAE,iBAAiB;UACxBC,OAAO,EAAE;SACV;MACH,KAAK,OAAO;QACV,OAAO;UACLD,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE,KAAK,IAAI7H,EAAE,YAAYpF,iBAAiB,GAAGoF,EAAE,CAACgI,UAAU,GAAGhI,EAAE,EAAEmB,KAAK,IAAInB,EAAE,EAAE2D,OAAO,CAAC,GAAG;SACjG;IACL;EACF;CACD;AAxfUsE,UAAA,EAAR/N,KAAK,EAAE,C,0DAA0B;AACzB+N,UAAA,EAAR/N,KAAK,EAAE,C,wDAAsB;AACrB+N,UAAA,EAAR/N,KAAK,EAAE,C,uDAAe;AACd+N,UAAA,EAAR/N,KAAK,EAAE,C,+DAA6C;AAC5C+N,UAAA,EAAR/N,KAAK,EAAE,C,wDAAmB;AAClB+N,UAAA,EAAR/N,KAAK,EAAE,C,+DAA0B;AACzB+N,UAAA,EAAR/N,KAAK,EAAE,C,wDAAmB;AAClB+N,UAAA,EAAR/N,KAAK,EAAE,C,2DAAsB;AACrB+N,UAAA,EAAR/N,KAAK,EAAE,C,kEAAgF;AAE9E+N,UAAA,EAAT7N,MAAM,EAAE,C,wDAAuD;AACtD6N,UAAA,EAAT7N,MAAM,EAAE,C,yDAAqD;AAQpD6N,UAAA,EAAT7N,MAAM,EAAE,C,uDAAsD;AArBpD6B,sBAAsB,GAAAgM,UAAA,EANlChO,SAAS,CAAC;EACTiO,QAAQ,EAAE,mBAAmB;EAC7BC,WAAW,EAAE,kCAAkC;EAC/CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC/N,IAAI,EAAEC,OAAO,EAAEyB,2BAA2B,EAAED,wBAAwB,EAAED,2BAA2B,EAAED,wBAAwB,EAAED,YAAY,EAAED,uBAAuB,EAAED,gCAAgC,EAAED,aAAa,EAAED,YAAY;CAC5O,CAAC,EA6DG8M,OAAA,KAAAnO,MAAM,CAACW,kBAAkB,CAAC,E,EA5DlBmB,sBAAsB,CA0flC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}