{"ast": null, "code": "//we are reusing the types from the nested components but new tokens need to be created that reference the previous tokens so that they can inherit the needed values but can be overwritten and only effect the collection component.\n//only a subset of the design tokens of the nested components are being exposed, this can be expanded later.\nconst collection = {\n  pagination: {\n    current: {\n      color: {\n        value: '{components.pagination.current.color}'\n      },\n      backgroundColor: {\n        value: '{components.pagination.current.backgroundColor}'\n      }\n    },\n    button: {\n      color: {\n        value: '{components.pagination.button.color}'\n      },\n      _hover: {\n        backgroundColor: {\n          value: '{components.pagination.button.hover.backgroundColor}'\n        },\n        color: {\n          value: '{components.pagination.button.hover.color}'\n        }\n      },\n      _disabled: {\n        color: {\n          value: '{components.pagination.button.disabled.color}'\n        }\n      }\n    }\n  },\n  search: {\n    input: {\n      color: {\n        value: '{components.searchfield.color}'\n      }\n    },\n    button: {\n      color: {\n        value: '{components.searchfield.button.color}'\n      },\n      _active: {\n        backgroundColor: {\n          value: '{components.searchfield.button._active.backgroundColor}'\n        },\n        borderColor: {\n          value: '{components.searchfield.button._active.borderColor}'\n        },\n        color: {\n          value: '{components.searchfield.button._active.color}'\n        }\n      },\n      _disabled: {\n        backgroundColor: {\n          value: '{components.searchfield.button._disabled.backgroundColor}'\n        },\n        borderColor: {\n          value: '{components.searchfield.button._disabled.borderColor}'\n        },\n        color: {\n          value: '{components.searchfield.button._disabled.color}'\n        }\n      },\n      _focus: {\n        backgroundColor: {\n          value: '{components.searchfield.button._focus.backgroundColor}'\n        },\n        borderColor: {\n          value: '{components.searchfield.button._focus.borderColor}'\n        },\n        color: {\n          value: '{components.searchfield.button._focus.color}'\n        }\n      },\n      _hover: {\n        backgroundColor: {\n          value: '{components.searchfield.button._hover.backgroundColor}'\n        },\n        borderColor: {\n          value: '{components.searchfield.button._hover.borderColor}'\n        },\n        color: {\n          value: '{components.searchfield.button._hover.color}'\n        }\n      }\n    }\n  }\n};\nexport { collection };", "map": {"version": 3, "names": ["collection", "pagination", "current", "color", "value", "backgroundColor", "button", "_hover", "_disabled", "search", "input", "_active", "borderColor", "_focus"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/collection.mjs"], "sourcesContent": ["//we are reusing the types from the nested components but new tokens need to be created that reference the previous tokens so that they can inherit the needed values but can be overwritten and only effect the collection component.\n//only a subset of the design tokens of the nested components are being exposed, this can be expanded later.\nconst collection = {\n    pagination: {\n        current: {\n            color: { value: '{components.pagination.current.color}' },\n            backgroundColor: {\n                value: '{components.pagination.current.backgroundColor}',\n            },\n        },\n        button: {\n            color: { value: '{components.pagination.button.color}' },\n            _hover: {\n                backgroundColor: {\n                    value: '{components.pagination.button.hover.backgroundColor}',\n                },\n                color: { value: '{components.pagination.button.hover.color}' },\n            },\n            _disabled: {\n                color: { value: '{components.pagination.button.disabled.color}' },\n            },\n        },\n    },\n    search: {\n        input: {\n            color: { value: '{components.searchfield.color}' },\n        },\n        button: {\n            color: { value: '{components.searchfield.button.color}' },\n            _active: {\n                backgroundColor: {\n                    value: '{components.searchfield.button._active.backgroundColor}',\n                },\n                borderColor: {\n                    value: '{components.searchfield.button._active.borderColor}',\n                },\n                color: { value: '{components.searchfield.button._active.color}' },\n            },\n            _disabled: {\n                backgroundColor: {\n                    value: '{components.searchfield.button._disabled.backgroundColor}',\n                },\n                borderColor: {\n                    value: '{components.searchfield.button._disabled.borderColor}',\n                },\n                color: {\n                    value: '{components.searchfield.button._disabled.color}',\n                },\n            },\n            _focus: {\n                backgroundColor: {\n                    value: '{components.searchfield.button._focus.backgroundColor}',\n                },\n                borderColor: {\n                    value: '{components.searchfield.button._focus.borderColor}',\n                },\n                color: { value: '{components.searchfield.button._focus.color}' },\n            },\n            _hover: {\n                backgroundColor: {\n                    value: '{components.searchfield.button._hover.backgroundColor}',\n                },\n                borderColor: {\n                    value: '{components.searchfield.button._hover.borderColor}',\n                },\n                color: { value: '{components.searchfield.button._hover.color}' },\n            },\n        },\n    },\n};\n\nexport { collection };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,UAAU,GAAG;EACfC,UAAU,EAAE;IACRC,OAAO,EAAE;MACLC,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAwC,CAAC;MACzDC,eAAe,EAAE;QACbD,KAAK,EAAE;MACX;IACJ,CAAC;IACDE,MAAM,EAAE;MACJH,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAuC,CAAC;MACxDG,MAAM,EAAE;QACJF,eAAe,EAAE;UACbD,KAAK,EAAE;QACX,CAAC;QACDD,KAAK,EAAE;UAAEC,KAAK,EAAE;QAA6C;MACjE,CAAC;MACDI,SAAS,EAAE;QACPL,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAgD;MACpE;IACJ;EACJ,CAAC;EACDK,MAAM,EAAE;IACJC,KAAK,EAAE;MACHP,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAiC;IACrD,CAAC;IACDE,MAAM,EAAE;MACJH,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAwC,CAAC;MACzDO,OAAO,EAAE;QACLN,eAAe,EAAE;UACbD,KAAK,EAAE;QACX,CAAC;QACDQ,WAAW,EAAE;UACTR,KAAK,EAAE;QACX,CAAC;QACDD,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAgD;MACpE,CAAC;MACDI,SAAS,EAAE;QACPH,eAAe,EAAE;UACbD,KAAK,EAAE;QACX,CAAC;QACDQ,WAAW,EAAE;UACTR,KAAK,EAAE;QACX,CAAC;QACDD,KAAK,EAAE;UACHC,KAAK,EAAE;QACX;MACJ,CAAC;MACDS,MAAM,EAAE;QACJR,eAAe,EAAE;UACbD,KAAK,EAAE;QACX,CAAC;QACDQ,WAAW,EAAE;UACTR,KAAK,EAAE;QACX,CAAC;QACDD,KAAK,EAAE;UAAEC,KAAK,EAAE;QAA+C;MACnE,CAAC;MACDG,MAAM,EAAE;QACJF,eAAe,EAAE;UACbD,KAAK,EAAE;QACX,CAAC;QACDQ,WAAW,EAAE;UACTR,KAAK,EAAE;QACX,CAAC;QACDD,KAAK,EAAE;UAAEC,KAAK,EAAE;QAA+C;MACnE;IACJ;EACJ;AACJ,CAAC;AAED,SAASJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}