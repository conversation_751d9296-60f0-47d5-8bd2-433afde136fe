{"ast": null, "code": "const authenticator = {\n  maxWidth: {\n    value: '60rem'\n  },\n  modal: {\n    width: {\n      value: '{space.relative.full}'\n    },\n    height: {\n      value: '{space.relative.full}'\n    },\n    backgroundColor: {\n      value: '{colors.overlay.50.value}'\n    },\n    top: {\n      value: '{space.zero}'\n    },\n    left: {\n      value: '{space.zero}'\n    }\n  },\n  container: {\n    widthMax: {\n      value: '30rem'\n    }\n  },\n  router: {\n    borderWidth: {\n      value: '{borderWidths.small.value}'\n    },\n    borderStyle: {\n      value: 'solid'\n    },\n    borderColor: {\n      value: '{colors.border.primary.value}'\n    },\n    backgroundColor: {\n      value: '{colors.background.primary.value}'\n    },\n    boxShadow: {\n      value: '{shadows.medium.value}'\n    }\n  },\n  footer: {\n    paddingBottom: {\n      value: '{space.medium.value}'\n    }\n  },\n  form: {\n    padding: {\n      value: '{space.xl.value}'\n    }\n  },\n  state: {\n    inactive: {\n      backgroundColor: {\n        value: '{colors.background.secondary.value}'\n      }\n    }\n  },\n  orContainer: {\n    color: {\n      value: '{colors.neutral.80.value}'\n    },\n    orLine: {\n      backgroundColor: {\n        value: '{colors.background.primary.value}'\n      }\n    }\n  }\n};\nexport { authenticator };", "map": {"version": 3, "names": ["authenticator", "max<PERSON><PERSON><PERSON>", "value", "modal", "width", "height", "backgroundColor", "top", "left", "container", "widthMax", "router", "borderWidth", "borderStyle", "borderColor", "boxShadow", "footer", "paddingBottom", "form", "padding", "state", "inactive", "orContainer", "color", "orLine"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/authenticator.mjs"], "sourcesContent": ["const authenticator = {\n    maxWidth: { value: '60rem' },\n    modal: {\n        width: { value: '{space.relative.full}' },\n        height: { value: '{space.relative.full}' },\n        backgroundColor: { value: '{colors.overlay.50.value}' },\n        top: { value: '{space.zero}' },\n        left: { value: '{space.zero}' },\n    },\n    container: {\n        widthMax: { value: '30rem' },\n    },\n    router: {\n        borderWidth: { value: '{borderWidths.small.value}' },\n        borderStyle: { value: 'solid' },\n        borderColor: { value: '{colors.border.primary.value}' },\n        backgroundColor: { value: '{colors.background.primary.value}' },\n        boxShadow: { value: '{shadows.medium.value}' },\n    },\n    footer: {\n        paddingBottom: { value: '{space.medium.value}' },\n    },\n    form: {\n        padding: { value: '{space.xl.value}' },\n    },\n    state: {\n        inactive: {\n            backgroundColor: { value: '{colors.background.secondary.value}' },\n        },\n    },\n    orContainer: {\n        color: { value: '{colors.neutral.80.value}' },\n        orLine: {\n            backgroundColor: { value: '{colors.background.primary.value}' },\n        },\n    },\n};\n\nexport { authenticator };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAG;EAClBC,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAQ,CAAC;EAC5BC,KAAK,EAAE;IACHC,KAAK,EAAE;MAAEF,KAAK,EAAE;IAAwB,CAAC;IACzCG,MAAM,EAAE;MAAEH,KAAK,EAAE;IAAwB,CAAC;IAC1CI,eAAe,EAAE;MAAEJ,KAAK,EAAE;IAA4B,CAAC;IACvDK,GAAG,EAAE;MAAEL,KAAK,EAAE;IAAe,CAAC;IAC9BM,IAAI,EAAE;MAAEN,KAAK,EAAE;IAAe;EAClC,CAAC;EACDO,SAAS,EAAE;IACPC,QAAQ,EAAE;MAAER,KAAK,EAAE;IAAQ;EAC/B,CAAC;EACDS,MAAM,EAAE;IACJC,WAAW,EAAE;MAAEV,KAAK,EAAE;IAA6B,CAAC;IACpDW,WAAW,EAAE;MAAEX,KAAK,EAAE;IAAQ,CAAC;IAC/BY,WAAW,EAAE;MAAEZ,KAAK,EAAE;IAAgC,CAAC;IACvDI,eAAe,EAAE;MAAEJ,KAAK,EAAE;IAAoC,CAAC;IAC/Da,SAAS,EAAE;MAAEb,KAAK,EAAE;IAAyB;EACjD,CAAC;EACDc,MAAM,EAAE;IACJC,aAAa,EAAE;MAAEf,KAAK,EAAE;IAAuB;EACnD,CAAC;EACDgB,IAAI,EAAE;IACFC,OAAO,EAAE;MAAEjB,KAAK,EAAE;IAAmB;EACzC,CAAC;EACDkB,KAAK,EAAE;IACHC,QAAQ,EAAE;MACNf,eAAe,EAAE;QAAEJ,KAAK,EAAE;MAAsC;IACpE;EACJ,CAAC;EACDoB,WAAW,EAAE;IACTC,KAAK,EAAE;MAAErB,KAAK,EAAE;IAA4B,CAAC;IAC7CsB,MAAM,EAAE;MACJlB,eAAe,EAAE;QAAEJ,KAAK,EAAE;MAAoC;IAClE;EACJ;AACJ,CAAC;AAED,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}