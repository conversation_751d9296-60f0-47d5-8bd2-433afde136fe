{"ast": null, "code": "// generated by genversion\nconst version = '6.14.3';\nexport { version };", "map": {"version": 3, "names": ["version"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Platform/version.mjs"], "sourcesContent": ["// generated by genversion\nconst version = '6.14.3';\n\nexport { version };\n"], "mappings": "AAAA;AACA,MAAMA,OAAO,GAAG,QAAQ;AAExB,SAASA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}