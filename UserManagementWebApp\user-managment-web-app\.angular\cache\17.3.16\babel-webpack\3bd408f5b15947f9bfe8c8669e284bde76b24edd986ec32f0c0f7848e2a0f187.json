{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction getUserContextData({\n  username,\n  userPoolId,\n  userPoolClientId\n}) {\n  if (typeof window === 'undefined') {\n    return undefined;\n  }\n  const amazonCognitoAdvancedSecurityData = window.AmazonCognitoAdvancedSecurityData;\n  if (typeof amazonCognitoAdvancedSecurityData === 'undefined') {\n    return undefined;\n  }\n  const advancedSecurityData = amazonCognitoAdvancedSecurityData.getData(username, userPoolId, userPoolClientId);\n  if (advancedSecurityData) {\n    const userContextData = {\n      EncodedData: advancedSecurityData\n    };\n    return userContextData;\n  }\n  return {};\n}\nexport { getUserContextData };", "map": {"version": 3, "names": ["getUserContextData", "username", "userPoolId", "userPoolClientId", "window", "undefined", "amazonCognitoAdvancedSecurityData", "AmazonCognitoAdvancedSecurityData", "advancedSecurityData", "getData", "userContextData", "EncodedData"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/userContextData.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction getUserContextData({ username, userPoolId, userPoolClientId, }) {\n    if (typeof window === 'undefined') {\n        return undefined;\n    }\n    const amazonCognitoAdvancedSecurityData = window\n        .AmazonCognitoAdvancedSecurityData;\n    if (typeof amazonCognitoAdvancedSecurityData === 'undefined') {\n        return undefined;\n    }\n    const advancedSecurityData = amazonCognitoAdvancedSecurityData.getData(username, userPoolId, userPoolClientId);\n    if (advancedSecurityData) {\n        const userContextData = {\n            EncodedData: advancedSecurityData,\n        };\n        return userContextData;\n    }\n    return {};\n}\n\nexport { getUserContextData };\n"], "mappings": "AAAA;AACA;AACA,SAASA,kBAAkBA,CAAC;EAAEC,QAAQ;EAAEC,UAAU;EAAEC;AAAkB,CAAC,EAAE;EACrE,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAOC,SAAS;EACpB;EACA,MAAMC,iCAAiC,GAAGF,MAAM,CAC3CG,iCAAiC;EACtC,IAAI,OAAOD,iCAAiC,KAAK,WAAW,EAAE;IAC1D,OAAOD,SAAS;EACpB;EACA,MAAMG,oBAAoB,GAAGF,iCAAiC,CAACG,OAAO,CAACR,QAAQ,EAAEC,UAAU,EAAEC,gBAAgB,CAAC;EAC9G,IAAIK,oBAAoB,EAAE;IACtB,MAAME,eAAe,GAAG;MACpBC,WAAW,EAAEH;IACjB,CAAC;IACD,OAAOE,eAAe;EAC1B;EACA,OAAO,CAAC,CAAC;AACb;AAEA,SAASV,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}