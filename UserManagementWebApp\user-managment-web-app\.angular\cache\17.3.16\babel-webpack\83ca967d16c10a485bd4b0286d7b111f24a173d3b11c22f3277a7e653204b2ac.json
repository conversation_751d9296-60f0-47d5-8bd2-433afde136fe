{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { AuthErrorCodes } from '../../../common/AuthErrorStrings.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../../../providers/cognito/types/errors.mjs';\nimport { createRespondToAuthChallengeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createRespondToAuthChallengeClient.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { cacheCognitoTokens } from '../../../providers/cognito/tokenProvider/cacheTokens.mjs';\nimport { dispatchSignedInHubEvent } from '../../../providers/cognito/utils/dispatchSignedInHubEvent.mjs';\nimport '../../utils/store/autoSignInStore.mjs';\nimport { signInStore, setActiveSignInState } from '../../utils/store/signInStore.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { assertPasskeyError, PasskeyErrorCode } from '../../utils/passkey/errors.mjs';\nimport { getPasskey } from '../../utils/passkey/getPasskey.mjs';\nimport { getNewDeviceMetadata } from '../../../providers/cognito/utils/getNewDeviceMetadata.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction handleWebAuthnSignInResult(_x) {\n  return _handleWebAuthnSignInResult.apply(this, arguments);\n}\nfunction _handleWebAuthnSignInResult() {\n  _handleWebAuthnSignInResult = _asyncToGenerator(function* (challengeParameters) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      username,\n      signInSession,\n      signInDetails,\n      challengeName\n    } = signInStore.getState();\n    if (challengeName !== 'WEB_AUTHN' || !username) {\n      throw new AuthError({\n        name: AuthErrorCodes.SignInException,\n        message: 'Unable to proceed due to invalid sign in state.'\n      });\n    }\n    const {\n      CREDENTIAL_REQUEST_OPTIONS: credentialRequestOptions\n    } = challengeParameters;\n    assertPasskeyError(!!credentialRequestOptions, PasskeyErrorCode.InvalidPasskeyAuthenticationOptions);\n    const cred = yield getPasskey(JSON.parse(credentialRequestOptions));\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: authConfig.userPoolEndpoint\n      })\n    });\n    const {\n      ChallengeName: nextChallengeName,\n      ChallengeParameters: nextChallengeParameters,\n      AuthenticationResult: authenticationResult,\n      Session: nextSession\n    } = yield respondToAuthChallenge({\n      region: getRegionFromUserPoolId(authConfig.userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn)\n    }, {\n      ChallengeName: 'WEB_AUTHN',\n      ChallengeResponses: {\n        USERNAME: username,\n        CREDENTIAL: JSON.stringify(cred)\n      },\n      ClientId: authConfig.userPoolClientId,\n      Session: signInSession\n    });\n    setActiveSignInState({\n      signInSession: nextSession,\n      username,\n      challengeName: nextChallengeName,\n      signInDetails\n    });\n    if (authenticationResult) {\n      yield cacheCognitoTokens({\n        ...authenticationResult,\n        username,\n        NewDeviceMetadata: yield getNewDeviceMetadata({\n          userPoolId: authConfig.userPoolId,\n          userPoolEndpoint: authConfig.userPoolEndpoint,\n          newDeviceMetadata: authenticationResult.NewDeviceMetadata,\n          accessToken: authenticationResult.AccessToken\n        }),\n        signInDetails\n      });\n      signInStore.dispatch({\n        type: 'RESET_STATE'\n      });\n      yield dispatchSignedInHubEvent();\n      return {\n        isSignedIn: true,\n        nextStep: {\n          signInStep: 'DONE'\n        }\n      };\n    }\n    if (nextChallengeName === 'WEB_AUTHN') {\n      throw new AuthError({\n        name: AuthErrorCodes.SignInException,\n        message: 'Sequential WEB_AUTHN challenges returned from underlying service cannot be handled.'\n      });\n    }\n    return {\n      challengeName: nextChallengeName,\n      challengeParameters: nextChallengeParameters\n    };\n  });\n  return _handleWebAuthnSignInResult.apply(this, arguments);\n}\nexport { handleWebAuthnSignInResult };", "map": {"version": 3, "names": ["Amplify", "assertTokenProviderConfig", "AuthAction", "AuthErrorCodes", "<PERSON>th<PERSON><PERSON><PERSON>", "createRespondToAuthChallengeClient", "getRegionFromUserPoolId", "createCognitoUserPoolEndpointResolver", "cacheCognitoTokens", "dispatchSignedInHubEvent", "signInStore", "setActiveSignInState", "getAuthUserAgentValue", "assertPasskeyError", "PasskeyErrorCode", "<PERSON><PERSON><PERSON><PERSON>", "getNewDeviceMetadata", "handleWebAuthnSignInResult", "_x", "_handleWebAuthnSignInResult", "apply", "arguments", "_asyncToGenerator", "challengeParameters", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "username", "signInSession", "signInDetails", "challenge<PERSON>ame", "getState", "name", "SignInException", "message", "CREDENTIAL_REQUEST_OPTIONS", "credentialRequestOptions", "InvalidPasskeyAuthenticationOptions", "cred", "JSON", "parse", "respondToAuthChallenge", "endpointResolver", "endpointOverride", "userPoolEndpoint", "ChallengeName", "nextChallengeName", "ChallengeParameters", "nextChallengeParameters", "AuthenticationResult", "authenticationResult", "Session", "nextSession", "region", "userPoolId", "userAgentValue", "ConfirmSignIn", "ChallengeResponses", "USERNAME", "CREDENTIAL", "stringify", "ClientId", "userPoolClientId", "NewDeviceMetadata", "newDeviceMetadata", "accessToken", "AccessToken", "dispatch", "type", "isSignedIn", "nextStep", "signInStep"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/flows/userAuth/handleWebAuthnSignInResult.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { AuthErrorCodes } from '../../../common/AuthErrorStrings.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../../../providers/cognito/types/errors.mjs';\nimport { createRespondToAuthChallengeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createRespondToAuthChallengeClient.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { cacheCognitoTokens } from '../../../providers/cognito/tokenProvider/cacheTokens.mjs';\nimport { dispatchSignedInHubEvent } from '../../../providers/cognito/utils/dispatchSignedInHubEvent.mjs';\nimport '../../utils/store/autoSignInStore.mjs';\nimport { signInStore, setActiveSignInState } from '../../utils/store/signInStore.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { assertPasskeyError, PasskeyErrorCode } from '../../utils/passkey/errors.mjs';\nimport { getPasskey } from '../../utils/passkey/getPasskey.mjs';\nimport { getNewDeviceMetadata } from '../../../providers/cognito/utils/getNewDeviceMetadata.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nasync function handleWebAuthnSignInResult(challengeParameters) {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { username, signInSession, signInDetails, challengeName } = signInStore.getState();\n    if (challengeName !== 'WEB_AUTHN' || !username) {\n        throw new AuthError({\n            name: AuthErrorCodes.SignInException,\n            message: 'Unable to proceed due to invalid sign in state.',\n        });\n    }\n    const { CREDENTIAL_REQUEST_OPTIONS: credentialRequestOptions } = challengeParameters;\n    assertPasskeyError(!!credentialRequestOptions, PasskeyErrorCode.InvalidPasskeyAuthenticationOptions);\n    const cred = await getPasskey(JSON.parse(credentialRequestOptions));\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: authConfig.userPoolEndpoint,\n        }),\n    });\n    const { ChallengeName: nextChallengeName, ChallengeParameters: nextChallengeParameters, AuthenticationResult: authenticationResult, Session: nextSession, } = await respondToAuthChallenge({\n        region: getRegionFromUserPoolId(authConfig.userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn),\n    }, {\n        ChallengeName: 'WEB_AUTHN',\n        ChallengeResponses: {\n            USERNAME: username,\n            CREDENTIAL: JSON.stringify(cred),\n        },\n        ClientId: authConfig.userPoolClientId,\n        Session: signInSession,\n    });\n    setActiveSignInState({\n        signInSession: nextSession,\n        username,\n        challengeName: nextChallengeName,\n        signInDetails,\n    });\n    if (authenticationResult) {\n        await cacheCognitoTokens({\n            ...authenticationResult,\n            username,\n            NewDeviceMetadata: await getNewDeviceMetadata({\n                userPoolId: authConfig.userPoolId,\n                userPoolEndpoint: authConfig.userPoolEndpoint,\n                newDeviceMetadata: authenticationResult.NewDeviceMetadata,\n                accessToken: authenticationResult.AccessToken,\n            }),\n            signInDetails,\n        });\n        signInStore.dispatch({ type: 'RESET_STATE' });\n        await dispatchSignedInHubEvent();\n        return {\n            isSignedIn: true,\n            nextStep: { signInStep: 'DONE' },\n        };\n    }\n    if (nextChallengeName === 'WEB_AUTHN') {\n        throw new AuthError({\n            name: AuthErrorCodes.SignInException,\n            message: 'Sequential WEB_AUTHN challenges returned from underlying service cannot be handled.',\n        });\n    }\n    return {\n        challengeName: nextChallengeName,\n        challengeParameters: nextChallengeParameters,\n    };\n}\n\nexport { handleWebAuthnSignInResult };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,cAAc,QAAQ,sCAAsC;AACrE,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,SAASC,SAAS,QAAQ,+BAA+B;AACzD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,6CAA6C;AACpD,SAASC,kCAAkC,QAAQ,6GAA6G;AAChK,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,qCAAqC,QAAQ,gFAAgF;AACtI,SAASC,kBAAkB,QAAQ,0DAA0D;AAC7F,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,OAAO,uCAAuC;AAC9C,SAASC,WAAW,EAAEC,oBAAoB,QAAQ,mCAAmC;AACrF,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,kBAAkB,EAAEC,gBAAgB,QAAQ,gCAAgC;AACrF,SAASC,UAAU,QAAQ,oCAAoC;AAC/D,SAASC,oBAAoB,QAAQ,2DAA2D;;AAEhG;AACA;AAAA,SACeC,0BAA0BA,CAAAC,EAAA;EAAA,OAAAC,2BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,4BAAA;EAAAA,2BAAA,GAAAG,iBAAA,CAAzC,WAA0CC,mBAAmB,EAAE;IAC3D,MAAMC,UAAU,GAAGxB,OAAO,CAACyB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpD1B,yBAAyB,CAACuB,UAAU,CAAC;IACrC,MAAM;MAAEI,QAAQ;MAAEC,aAAa;MAAEC,aAAa;MAAEC;IAAc,CAAC,GAAGrB,WAAW,CAACsB,QAAQ,CAAC,CAAC;IACxF,IAAID,aAAa,KAAK,WAAW,IAAI,CAACH,QAAQ,EAAE;MAC5C,MAAM,IAAIxB,SAAS,CAAC;QAChB6B,IAAI,EAAE9B,cAAc,CAAC+B,eAAe;QACpCC,OAAO,EAAE;MACb,CAAC,CAAC;IACN;IACA,MAAM;MAAEC,0BAA0B,EAAEC;IAAyB,CAAC,GAAGd,mBAAmB;IACpFV,kBAAkB,CAAC,CAAC,CAACwB,wBAAwB,EAAEvB,gBAAgB,CAACwB,mCAAmC,CAAC;IACpG,MAAMC,IAAI,SAASxB,UAAU,CAACyB,IAAI,CAACC,KAAK,CAACJ,wBAAwB,CAAC,CAAC;IACnE,MAAMK,sBAAsB,GAAGrC,kCAAkC,CAAC;MAC9DsC,gBAAgB,EAAEpC,qCAAqC,CAAC;QACpDqC,gBAAgB,EAAEpB,UAAU,CAACqB;MACjC,CAAC;IACL,CAAC,CAAC;IACF,MAAM;MAAEC,aAAa,EAAEC,iBAAiB;MAAEC,mBAAmB,EAAEC,uBAAuB;MAAEC,oBAAoB,EAAEC,oBAAoB;MAAEC,OAAO,EAAEC;IAAa,CAAC,SAASX,sBAAsB,CAAC;MACvLY,MAAM,EAAEhD,uBAAuB,CAACkB,UAAU,CAAC+B,UAAU,CAAC;MACtDC,cAAc,EAAE5C,qBAAqB,CAACV,UAAU,CAACuD,aAAa;IAClE,CAAC,EAAE;MACCX,aAAa,EAAE,WAAW;MAC1BY,kBAAkB,EAAE;QAChBC,QAAQ,EAAE/B,QAAQ;QAClBgC,UAAU,EAAEpB,IAAI,CAACqB,SAAS,CAACtB,IAAI;MACnC,CAAC;MACDuB,QAAQ,EAAEtC,UAAU,CAACuC,gBAAgB;MACrCX,OAAO,EAAEvB;IACb,CAAC,CAAC;IACFlB,oBAAoB,CAAC;MACjBkB,aAAa,EAAEwB,WAAW;MAC1BzB,QAAQ;MACRG,aAAa,EAAEgB,iBAAiB;MAChCjB;IACJ,CAAC,CAAC;IACF,IAAIqB,oBAAoB,EAAE;MACtB,MAAM3C,kBAAkB,CAAC;QACrB,GAAG2C,oBAAoB;QACvBvB,QAAQ;QACRoC,iBAAiB,QAAQhD,oBAAoB,CAAC;UAC1CuC,UAAU,EAAE/B,UAAU,CAAC+B,UAAU;UACjCV,gBAAgB,EAAErB,UAAU,CAACqB,gBAAgB;UAC7CoB,iBAAiB,EAAEd,oBAAoB,CAACa,iBAAiB;UACzDE,WAAW,EAAEf,oBAAoB,CAACgB;QACtC,CAAC,CAAC;QACFrC;MACJ,CAAC,CAAC;MACFpB,WAAW,CAAC0D,QAAQ,CAAC;QAAEC,IAAI,EAAE;MAAc,CAAC,CAAC;MAC7C,MAAM5D,wBAAwB,CAAC,CAAC;MAChC,OAAO;QACH6D,UAAU,EAAE,IAAI;QAChBC,QAAQ,EAAE;UAAEC,UAAU,EAAE;QAAO;MACnC,CAAC;IACL;IACA,IAAIzB,iBAAiB,KAAK,WAAW,EAAE;MACnC,MAAM,IAAI3C,SAAS,CAAC;QAChB6B,IAAI,EAAE9B,cAAc,CAAC+B,eAAe;QACpCC,OAAO,EAAE;MACb,CAAC,CAAC;IACN;IACA,OAAO;MACHJ,aAAa,EAAEgB,iBAAiB;MAChCxB,mBAAmB,EAAE0B;IACzB,CAAC;EACL,CAAC;EAAA,OAAA9B,2BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}