{"ast": null, "code": "import { borderWidths } from './borderWidths.mjs';\nimport { colors } from './colors.mjs';\nimport { components } from './components/index.mjs';\nimport { fonts } from './fonts.mjs';\nimport { fontSizes } from './fontSizes.mjs';\nimport { fontWeights } from './fontWeights.mjs';\nimport { lineHeights } from './lineHeights.mjs';\nimport { opacities } from './opacities.mjs';\nimport { outlineOffsets } from './outlineOffsets.mjs';\nimport { outlineWidths } from './outlineWidths.mjs';\nimport { radii } from './radii.mjs';\nimport { shadows } from './shadows.mjs';\nimport { space } from './space.mjs';\nimport { time } from './time.mjs';\nimport { transforms } from './transforms.mjs';\nconst tokens = {\n  components,\n  borderWidths,\n  colors,\n  fonts,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  opacities,\n  outlineOffsets,\n  outlineWidths,\n  radii,\n  shadows,\n  space,\n  time,\n  transforms\n};\nconst reactNativeTokens = {\n  colors,\n  borderWidths,\n  fontSizes: {\n    xxs: fontSizes.xxs,\n    xs: fontSizes.xs,\n    small: fontSizes.small,\n    medium: fontSizes.medium,\n    large: fontSizes.large,\n    xl: fontSizes.xl,\n    xxl: fontSizes.xxl,\n    xxxl: fontSizes.xxxl\n  },\n  fontWeights,\n  opacities,\n  // React Native doesn't need the relative space values\n  space: {\n    // use `space.xxxs` to output a value of `2` and avoid odd space numbers\n    xxs: space.xxxs,\n    xs: space.xs,\n    small: space.small,\n    medium: space.medium,\n    large: space.large,\n    xl: space.xl,\n    xxl: space.xxl,\n    xxxl: space.xxxl\n  },\n  radii,\n  time\n};\nexport { reactNativeTokens, tokens };", "map": {"version": 3, "names": ["borderWidths", "colors", "components", "fonts", "fontSizes", "fontWeights", "lineHeights", "opacities", "outlineOffsets", "outlineWidths", "radii", "shadows", "space", "time", "transforms", "tokens", "reactNativeTokens", "xxs", "xs", "small", "medium", "large", "xl", "xxl", "xxxl", "xxxs"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/index.mjs"], "sourcesContent": ["import { borderWidths } from './borderWidths.mjs';\nimport { colors } from './colors.mjs';\nimport { components } from './components/index.mjs';\nimport { fonts } from './fonts.mjs';\nimport { fontSizes } from './fontSizes.mjs';\nimport { fontWeights } from './fontWeights.mjs';\nimport { lineHeights } from './lineHeights.mjs';\nimport { opacities } from './opacities.mjs';\nimport { outlineOffsets } from './outlineOffsets.mjs';\nimport { outlineWidths } from './outlineWidths.mjs';\nimport { radii } from './radii.mjs';\nimport { shadows } from './shadows.mjs';\nimport { space } from './space.mjs';\nimport { time } from './time.mjs';\nimport { transforms } from './transforms.mjs';\n\nconst tokens = {\n    components,\n    borderWidths,\n    colors,\n    fonts,\n    fontSizes,\n    fontWeights,\n    lineHeights,\n    opacities,\n    outlineOffsets,\n    outlineWidths,\n    radii,\n    shadows,\n    space,\n    time,\n    transforms,\n};\nconst reactNativeTokens = {\n    colors,\n    borderWidths,\n    fontSizes: {\n        xxs: fontSizes.xxs,\n        xs: fontSizes.xs,\n        small: fontSizes.small,\n        medium: fontSizes.medium,\n        large: fontSizes.large,\n        xl: fontSizes.xl,\n        xxl: fontSizes.xxl,\n        xxxl: fontSizes.xxxl,\n    },\n    fontWeights,\n    opacities,\n    // React Native doesn't need the relative space values\n    space: {\n        // use `space.xxxs` to output a value of `2` and avoid odd space numbers\n        xxs: space.xxxs,\n        xs: space.xs,\n        small: space.small,\n        medium: space.medium,\n        large: space.large,\n        xl: space.xl,\n        xxl: space.xxl,\n        xxxl: space.xxxl,\n    },\n    radii,\n    time,\n};\n\nexport { reactNativeTokens, tokens };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,MAAM,QAAQ,cAAc;AACrC,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,UAAU,QAAQ,kBAAkB;AAE7C,MAAMC,MAAM,GAAG;EACXb,UAAU;EACVF,YAAY;EACZC,MAAM;EACNE,KAAK;EACLC,SAAS;EACTC,WAAW;EACXC,WAAW;EACXC,SAAS;EACTC,cAAc;EACdC,aAAa;EACbC,KAAK;EACLC,OAAO;EACPC,KAAK;EACLC,IAAI;EACJC;AACJ,CAAC;AACD,MAAME,iBAAiB,GAAG;EACtBf,MAAM;EACND,YAAY;EACZI,SAAS,EAAE;IACPa,GAAG,EAAEb,SAAS,CAACa,GAAG;IAClBC,EAAE,EAAEd,SAAS,CAACc,EAAE;IAChBC,KAAK,EAAEf,SAAS,CAACe,KAAK;IACtBC,MAAM,EAAEhB,SAAS,CAACgB,MAAM;IACxBC,KAAK,EAAEjB,SAAS,CAACiB,KAAK;IACtBC,EAAE,EAAElB,SAAS,CAACkB,EAAE;IAChBC,GAAG,EAAEnB,SAAS,CAACmB,GAAG;IAClBC,IAAI,EAAEpB,SAAS,CAACoB;EACpB,CAAC;EACDnB,WAAW;EACXE,SAAS;EACT;EACAK,KAAK,EAAE;IACH;IACAK,GAAG,EAAEL,KAAK,CAACa,IAAI;IACfP,EAAE,EAAEN,KAAK,CAACM,EAAE;IACZC,KAAK,EAAEP,KAAK,CAACO,KAAK;IAClBC,MAAM,EAAER,KAAK,CAACQ,MAAM;IACpBC,KAAK,EAAET,KAAK,CAACS,KAAK;IAClBC,EAAE,EAAEV,KAAK,CAACU,EAAE;IACZC,GAAG,EAAEX,KAAK,CAACW,GAAG;IACdC,IAAI,EAAEZ,KAAK,CAACY;EAChB,CAAC;EACDd,KAAK;EACLG;AACJ,CAAC;AAED,SAASG,iBAAiB,EAAED,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}