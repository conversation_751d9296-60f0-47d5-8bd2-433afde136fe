{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { authenticatedHandler } from '../../clients/handlers/aws/authenticated.mjs';\nimport { composeServiceApi } from '../../clients/internal/composeServiceApi.mjs';\nimport { extendedEncodeURIComponent } from '../../clients/middleware/signing/utils/extendedEncodeURIComponent.mjs';\nimport { parseMetadata } from '../../clients/serde/responseInfo.mjs';\nimport { parseJsonError, parseJsonBody } from '../../clients/serde/json.mjs';\nimport { AmplifyUrl } from '../../utils/amplifyUrl/index.mjs';\nimport { defaultConfig, getSharedHeaders } from './base.mjs';\nimport { assert, PinpointValidationErrorCode } from './errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst putEventsSerializer = ({\n  ApplicationId,\n  EventsRequest\n}, endpoint) => {\n  assert(!!ApplicationId, PinpointValidationErrorCode.NoAppId);\n  const headers = getSharedHeaders();\n  const url = new AmplifyUrl(endpoint.url);\n  url.pathname = `v1/apps/${extendedEncodeURIComponent(ApplicationId)}/events`;\n  const body = JSON.stringify(EventsRequest ?? {});\n  return {\n    method: 'POST',\n    headers,\n    url,\n    body\n  };\n};\nconst putEventsDeserializer = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (response) {\n    if (response.statusCode >= 300) {\n      const error = yield parseJsonError(response);\n      throw error;\n    } else {\n      const {\n        Results\n      } = yield parseJsonBody(response);\n      return {\n        EventsResponse: {\n          Results\n        },\n        $metadata: parseMetadata(response)\n      };\n    }\n  });\n  return function putEventsDeserializer(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\n/**\n * @internal\n */\nconst putEvents = composeServiceApi(authenticatedHandler, putEventsSerializer, putEventsDeserializer, defaultConfig);\nexport { putEvents };", "map": {"version": 3, "names": ["authenticated<PERSON><PERSON><PERSON>", "composeServiceApi", "extendedEncodeURIComponent", "parseMetadata", "parseJsonError", "parseJsonBody", "AmplifyUrl", "defaultConfig", "getSharedHeaders", "assert", "PinpointValidationErrorCode", "putEventsSerializer", "ApplicationId", "EventsRequest", "endpoint", "NoAppId", "headers", "url", "pathname", "body", "JSON", "stringify", "method", "putEventsDeserializer", "_ref", "_asyncToGenerator", "response", "statusCode", "error", "Results", "EventsResponse", "$metadata", "_x", "apply", "arguments", "putEvents"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/awsClients/pinpoint/putEvents.mjs"], "sourcesContent": ["import { authenticated<PERSON><PERSON><PERSON> } from '../../clients/handlers/aws/authenticated.mjs';\nimport { composeServiceApi } from '../../clients/internal/composeServiceApi.mjs';\nimport { extendedEncodeURIComponent } from '../../clients/middleware/signing/utils/extendedEncodeURIComponent.mjs';\nimport { parseMetadata } from '../../clients/serde/responseInfo.mjs';\nimport { parseJsonError, parseJsonBody } from '../../clients/serde/json.mjs';\nimport { AmplifyUrl } from '../../utils/amplifyUrl/index.mjs';\nimport { defaultConfig, getSharedHeaders } from './base.mjs';\nimport { assert, PinpointValidationErrorCode } from './errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst putEventsSerializer = ({ ApplicationId, EventsRequest }, endpoint) => {\n    assert(!!ApplicationId, PinpointValidationErrorCode.NoAppId);\n    const headers = getSharedHeaders();\n    const url = new AmplifyUrl(endpoint.url);\n    url.pathname = `v1/apps/${extendedEncodeURIComponent(ApplicationId)}/events`;\n    const body = JSON.stringify(EventsRequest ?? {});\n    return { method: 'POST', headers, url, body };\n};\nconst putEventsDeserializer = async (response) => {\n    if (response.statusCode >= 300) {\n        const error = await parseJsonError(response);\n        throw error;\n    }\n    else {\n        const { Results } = await parseJsonBody(response);\n        return {\n            EventsResponse: { Results },\n            $metadata: parseMetadata(response),\n        };\n    }\n};\n/**\n * @internal\n */\nconst putEvents = composeServiceApi(authenticatedHandler, putEventsSerializer, putEventsDeserializer, defaultConfig);\n\nexport { putEvents };\n"], "mappings": ";AAAA,SAASA,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,iBAAiB,QAAQ,8CAA8C;AAChF,SAASC,0BAA0B,QAAQ,uEAAuE;AAClH,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,cAAc,EAAEC,aAAa,QAAQ,8BAA8B;AAC5E,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,YAAY;AAC5D,SAASC,MAAM,EAAEC,2BAA2B,QAAQ,oBAAoB;;AAExE;AACA;AACA,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,aAAa;EAAEC;AAAc,CAAC,EAAEC,QAAQ,KAAK;EACxEL,MAAM,CAAC,CAAC,CAACG,aAAa,EAAEF,2BAA2B,CAACK,OAAO,CAAC;EAC5D,MAAMC,OAAO,GAAGR,gBAAgB,CAAC,CAAC;EAClC,MAAMS,GAAG,GAAG,IAAIX,UAAU,CAACQ,QAAQ,CAACG,GAAG,CAAC;EACxCA,GAAG,CAACC,QAAQ,GAAG,WAAWhB,0BAA0B,CAACU,aAAa,CAAC,SAAS;EAC5E,MAAMO,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACR,aAAa,IAAI,CAAC,CAAC,CAAC;EAChD,OAAO;IAAES,MAAM,EAAE,MAAM;IAAEN,OAAO;IAAEC,GAAG;IAAEE;EAAK,CAAC;AACjD,CAAC;AACD,MAAMI,qBAAqB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,QAAQ,EAAK;IAC9C,IAAIA,QAAQ,CAACC,UAAU,IAAI,GAAG,EAAE;MAC5B,MAAMC,KAAK,SAASxB,cAAc,CAACsB,QAAQ,CAAC;MAC5C,MAAME,KAAK;IACf,CAAC,MACI;MACD,MAAM;QAAEC;MAAQ,CAAC,SAASxB,aAAa,CAACqB,QAAQ,CAAC;MACjD,OAAO;QACHI,cAAc,EAAE;UAAED;QAAQ,CAAC;QAC3BE,SAAS,EAAE5B,aAAa,CAACuB,QAAQ;MACrC,CAAC;IACL;EACJ,CAAC;EAAA,gBAZKH,qBAAqBA,CAAAS,EAAA;IAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;EAAA;AAAA,GAY1B;AACD;AACA;AACA;AACA,MAAMC,SAAS,GAAGlC,iBAAiB,CAACD,oBAAoB,EAAEW,mBAAmB,EAAEY,qBAAqB,EAAEhB,aAAa,CAAC;AAEpH,SAAS4B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}