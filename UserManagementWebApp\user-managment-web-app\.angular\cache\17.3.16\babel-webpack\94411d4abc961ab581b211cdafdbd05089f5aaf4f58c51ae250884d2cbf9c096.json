{"ast": null, "code": "import { globalExists } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with expo 48 / react-native 0.71.3\nfunction expoDetect() {\n  return globalExists() && typeof global.expo !== 'undefined';\n}\nexport { expoDetect };", "map": {"version": 3, "names": ["globalExists", "expoDetect", "global", "expo"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Platform/detection/Expo.mjs"], "sourcesContent": ["import { globalExists } from './helpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Tested with expo 48 / react-native 0.71.3\nfunction expoDetect() {\n    return globalExists() && typeof global.expo !== 'undefined';\n}\n\nexport { expoDetect };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,eAAe;;AAE5C;AACA;AACA;AACA,SAASC,UAAUA,CAAA,EAAG;EAClB,OAAOD,YAAY,CAAC,CAAC,IAAI,OAAOE,MAAM,CAACC,IAAI,KAAK,WAAW;AAC/D;AAEA,SAASF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}