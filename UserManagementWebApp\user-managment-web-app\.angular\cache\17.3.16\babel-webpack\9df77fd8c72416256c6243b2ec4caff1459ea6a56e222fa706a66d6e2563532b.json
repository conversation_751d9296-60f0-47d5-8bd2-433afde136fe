{"ast": null, "code": "const krDict = {\n  'Account recovery requires verified contact information': '계정 복구를 위해 연락처 확인이 필요합니다',\n  'Authenticator App (TOTP)': '인증 앱(TOTP)',\n  'Back to Sign In': '로그인으로 돌아가기',\n  'Change Password': '비밀번호 변경하기',\n  Changing: '변경중',\n  Code: '코드',\n  'Confirm Email Code': '이메일 코드 확인',\n  'Confirm Password': '비밀번호 재확인',\n  'Confirm Sign Up': '회원가입 확인',\n  'Confirm SMS Code': '휴대폰 본인 확인',\n  'Confirm TOTP Code': 'TOTP 인증번호 확인',\n  Confirm: '확인',\n  'Confirmation Code': '인증번호',\n  Confirming: '확인중',\n  'Create a new account': '회원가입',\n  'Create Account': '회원가입',\n  'Creating Account': '회원가입중',\n  'Dismiss alert': '알림 무시',\n  Email: '이메일',\n  'Email Message': '이메일 메시지',\n  'Enter your Birthdate': '생년월일 입력',\n  'Enter your code': '인증번호를 입력해주세요',\n  'Enter your Confirmation Code': '확인 코드 입력',\n  'Enter your Email': '이메일 입력',\n  'Enter your Family Name': '성 입력',\n  'Enter your Given Name': '사용장 이름 입력',\n  'Enter your Name': '이름 입력',\n  'Enter your Nickname': '닉네임 입력',\n  'Enter your Password': '비밀번호 입력',\n  'Enter your phone number': '전화번호 입력',\n  'Enter your Preferred Username': '선호하는 아이디 입력',\n  'Enter your username': '아이디를 입력해주세요',\n  'Forgot password?': '비밀번호를 잊으셨나요?',\n  'Hide password': '비밀번호 숨기기',\n  'It may take a minute to arrive': '도착하는 데 1분 정도 걸릴 수 있습니다',\n  Loading: '로딩중',\n  'Multi-Factor Authentication': '다중 인증',\n  'Multi-Factor Authentication Setup': '다중 인증 설정',\n  'New password': '새 비밀번호',\n  or: '또는',\n  Password: '비밀번호',\n  'Phone Number': '전화번호',\n  'Please confirm your Password': '비밀번호를 확인해 주세요.',\n  'Resend Code': '인증번호 재전송',\n  'Reset your password': '비밀번호 재설정',\n  'Reset your Password': '비밀번호 재설정',\n  'Select MFA Type': 'MFA 유형 선택',\n  'Send code': '인증코드 보내기',\n  'Send Code': '코드 전송',\n  Sending: '전송중',\n  'Setup Email': '이메일 설정',\n  'Setup TOTP': 'TOTP 설정하기',\n  'Show password': '비밀번호 보이기',\n  'Sign in to your account': '로그인',\n  'Sign In with Amazon': 'Amazon 로그인',\n  'Sign In with Apple': 'Apple 로그인',\n  'Sign In with Facebook': 'Facebook 로그인',\n  'Sign In with Google': 'Google 로그인',\n  'Sign in': '로그인',\n  'Sign In': '로그인',\n  'Signing in': '로그인중',\n  Skip: '다음에 하기',\n  Submit: '확인',\n  Submitting: '확인중',\n  'Text Message (SMS)': '문자 메시지(SMS)',\n  Username: '아이디',\n  'Verify Contact': '연락처 확인',\n  Verify: '인증',\n  'We Emailed You': '이메일을 보냄',\n  'We Sent A Code': '코드를 보냄',\n  'We Texted You': '문자 메시지를 보냄',\n  'Your code is on the way. To log in, enter the code we emailed to': '코드가 전송 중입니다. 로그인하려면 이메일로 전송한 코드를 입력하세요',\n  'Your code is on the way. To log in, enter the code we sent you': '코드가 전송 중입니다. 로그인하려면 전송한 코드를 입력하세요',\n  'Your code is on the way. To log in, enter the code we texted to': '코드가 전송 중입니다. 로그인하려면 문자 메시지로 전송한 코드를 입력하세요',\n  // Additional translations provided by customers\n  Birthdate: '생년월일',\n  'Family Name': '성',\n  'Forgot your password?': '비밀번호를 잊으셨나요?',\n  'Given Name': '이름',\n  Name: '성함',\n  Nickname: '닉네임',\n  'Preferred Username': '닉네임',\n  Profile: '프로필',\n  'Reset Password': '비밀번호 재설정',\n  Website: '웹사이트'\n};\nexport { krDict };", "map": {"version": 3, "names": ["krDict", "Changing", "Code", "Confirm", "Confirming", "Email", "Loading", "or", "Password", "Sending", "<PERSON><PERSON>", "Submit", "Submitting", "Username", "Verify", "Birthdate", "Name", "Nickname", "Profile", "Website"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/i18n/dictionaries/authenticator/kr.mjs"], "sourcesContent": ["const krDict = {\n    'Account recovery requires verified contact information': '계정 복구를 위해 연락처 확인이 필요합니다',\n    'Authenticator App (TOTP)': '인증 앱(TOTP)',\n    'Back to Sign In': '로그인으로 돌아가기',\n    'Change Password': '비밀번호 변경하기',\n    Changing: '변경중',\n    Code: '코드',\n    'Confirm Email Code': '이메일 코드 확인',\n    'Confirm Password': '비밀번호 재확인',\n    'Confirm Sign Up': '회원가입 확인',\n    'Confirm SMS Code': '휴대폰 본인 확인',\n    'Confirm TOTP Code': 'TOTP 인증번호 확인',\n    Confirm: '확인',\n    'Confirmation Code': '인증번호',\n    Confirming: '확인중',\n    'Create a new account': '회원가입',\n    'Create Account': '회원가입',\n    'Creating Account': '회원가입중',\n    'Dismiss alert': '알림 무시',\n    Email: '이메일',\n    'Email Message': '이메일 메시지',\n    'Enter your Birthdate': '생년월일 입력',\n    'Enter your code': '인증번호를 입력해주세요',\n    'Enter your Confirmation Code': '확인 코드 입력',\n    'Enter your Email': '이메일 입력',\n    'Enter your Family Name': '성 입력',\n    'Enter your Given Name': '사용장 이름 입력',\n    'Enter your Name': '이름 입력',\n    'Enter your Nickname': '닉네임 입력',\n    'Enter your Password': '비밀번호 입력',\n    'Enter your phone number': '전화번호 입력',\n    'Enter your Preferred Username': '선호하는 아이디 입력',\n    'Enter your username': '아이디를 입력해주세요',\n    'Forgot password?': '비밀번호를 잊으셨나요?',\n    'Hide password': '비밀번호 숨기기',\n    'It may take a minute to arrive': '도착하는 데 1분 정도 걸릴 수 있습니다',\n    Loading: '로딩중',\n    'Multi-Factor Authentication': '다중 인증',\n    'Multi-Factor Authentication Setup': '다중 인증 설정',\n    'New password': '새 비밀번호',\n    or: '또는',\n    Password: '비밀번호',\n    'Phone Number': '전화번호',\n    'Please confirm your Password': '비밀번호를 확인해 주세요.',\n    'Resend Code': '인증번호 재전송',\n    'Reset your password': '비밀번호 재설정',\n    'Reset your Password': '비밀번호 재설정',\n    'Select MFA Type': 'MFA 유형 선택',\n    'Send code': '인증코드 보내기',\n    'Send Code': '코드 전송',\n    Sending: '전송중',\n    'Setup Email': '이메일 설정',\n    'Setup TOTP': 'TOTP 설정하기',\n    'Show password': '비밀번호 보이기',\n    'Sign in to your account': '로그인',\n    'Sign In with Amazon': 'Amazon 로그인',\n    'Sign In with Apple': 'Apple 로그인',\n    'Sign In with Facebook': 'Facebook 로그인',\n    'Sign In with Google': 'Google 로그인',\n    'Sign in': '로그인',\n    'Sign In': '로그인',\n    'Signing in': '로그인중',\n    Skip: '다음에 하기',\n    Submit: '확인',\n    Submitting: '확인중',\n    'Text Message (SMS)': '문자 메시지(SMS)',\n    Username: '아이디',\n    'Verify Contact': '연락처 확인',\n    Verify: '인증',\n    'We Emailed You': '이메일을 보냄',\n    'We Sent A Code': '코드를 보냄',\n    'We Texted You': '문자 메시지를 보냄',\n    'Your code is on the way. To log in, enter the code we emailed to': '코드가 전송 중입니다. 로그인하려면 이메일로 전송한 코드를 입력하세요',\n    'Your code is on the way. To log in, enter the code we sent you': '코드가 전송 중입니다. 로그인하려면 전송한 코드를 입력하세요',\n    'Your code is on the way. To log in, enter the code we texted to': '코드가 전송 중입니다. 로그인하려면 문자 메시지로 전송한 코드를 입력하세요',\n    // Additional translations provided by customers\n    Birthdate: '생년월일',\n    'Family Name': '성',\n    'Forgot your password?': '비밀번호를 잊으셨나요?',\n    'Given Name': '이름',\n    Name: '성함',\n    Nickname: '닉네임',\n    'Preferred Username': '닉네임',\n    Profile: '프로필',\n    'Reset Password': '비밀번호 재설정',\n    Website: '웹사이트',\n};\n\nexport { krDict };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACX,wDAAwD,EAAE,yBAAyB;EACnF,0BAA0B,EAAE,YAAY;EACxC,iBAAiB,EAAE,YAAY;EAC/B,iBAAiB,EAAE,WAAW;EAC9BC,QAAQ,EAAE,KAAK;EACfC,IAAI,EAAE,IAAI;EACV,oBAAoB,EAAE,WAAW;EACjC,kBAAkB,EAAE,UAAU;EAC9B,iBAAiB,EAAE,SAAS;EAC5B,kBAAkB,EAAE,WAAW;EAC/B,mBAAmB,EAAE,cAAc;EACnCC,OAAO,EAAE,IAAI;EACb,mBAAmB,EAAE,MAAM;EAC3BC,UAAU,EAAE,KAAK;EACjB,sBAAsB,EAAE,MAAM;EAC9B,gBAAgB,EAAE,MAAM;EACxB,kBAAkB,EAAE,OAAO;EAC3B,eAAe,EAAE,OAAO;EACxBC,KAAK,EAAE,KAAK;EACZ,eAAe,EAAE,SAAS;EAC1B,sBAAsB,EAAE,SAAS;EACjC,iBAAiB,EAAE,cAAc;EACjC,8BAA8B,EAAE,UAAU;EAC1C,kBAAkB,EAAE,QAAQ;EAC5B,wBAAwB,EAAE,MAAM;EAChC,uBAAuB,EAAE,WAAW;EACpC,iBAAiB,EAAE,OAAO;EAC1B,qBAAqB,EAAE,QAAQ;EAC/B,qBAAqB,EAAE,SAAS;EAChC,yBAAyB,EAAE,SAAS;EACpC,+BAA+B,EAAE,aAAa;EAC9C,qBAAqB,EAAE,aAAa;EACpC,kBAAkB,EAAE,cAAc;EAClC,eAAe,EAAE,UAAU;EAC3B,gCAAgC,EAAE,wBAAwB;EAC1DC,OAAO,EAAE,KAAK;EACd,6BAA6B,EAAE,OAAO;EACtC,mCAAmC,EAAE,UAAU;EAC/C,cAAc,EAAE,QAAQ;EACxBC,EAAE,EAAE,IAAI;EACRC,QAAQ,EAAE,MAAM;EAChB,cAAc,EAAE,MAAM;EACtB,8BAA8B,EAAE,gBAAgB;EAChD,aAAa,EAAE,UAAU;EACzB,qBAAqB,EAAE,UAAU;EACjC,qBAAqB,EAAE,UAAU;EACjC,iBAAiB,EAAE,WAAW;EAC9B,WAAW,EAAE,UAAU;EACvB,WAAW,EAAE,OAAO;EACpBC,OAAO,EAAE,KAAK;EACd,aAAa,EAAE,QAAQ;EACvB,YAAY,EAAE,WAAW;EACzB,eAAe,EAAE,UAAU;EAC3B,yBAAyB,EAAE,KAAK;EAChC,qBAAqB,EAAE,YAAY;EACnC,oBAAoB,EAAE,WAAW;EACjC,uBAAuB,EAAE,cAAc;EACvC,qBAAqB,EAAE,YAAY;EACnC,SAAS,EAAE,KAAK;EAChB,SAAS,EAAE,KAAK;EAChB,YAAY,EAAE,MAAM;EACpBC,IAAI,EAAE,QAAQ;EACdC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,KAAK;EACjB,oBAAoB,EAAE,aAAa;EACnCC,QAAQ,EAAE,KAAK;EACf,gBAAgB,EAAE,QAAQ;EAC1BC,MAAM,EAAE,IAAI;EACZ,gBAAgB,EAAE,SAAS;EAC3B,gBAAgB,EAAE,QAAQ;EAC1B,eAAe,EAAE,YAAY;EAC7B,kEAAkE,EAAE,wCAAwC;EAC5G,gEAAgE,EAAE,mCAAmC;EACrG,iEAAiE,EAAE,2CAA2C;EAC9G;EACAC,SAAS,EAAE,MAAM;EACjB,aAAa,EAAE,GAAG;EAClB,uBAAuB,EAAE,cAAc;EACvC,YAAY,EAAE,IAAI;EAClBC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,KAAK;EACf,oBAAoB,EAAE,KAAK;EAC3BC,OAAO,EAAE,KAAK;EACd,gBAAgB,EAAE,UAAU;EAC5BC,OAAO,EAAE;AACb,CAAC;AAED,SAASnB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}