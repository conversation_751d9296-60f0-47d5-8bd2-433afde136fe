{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { dotCase } from \"dot-case\";\nexport function paramCase(input, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return dotCase(input, __assign({\n    delimiter: \"-\"\n  }, options));\n}", "map": {"version": 3, "names": ["__assign", "dotCase", "paramCase", "input", "options", "delimiter"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/param-case/dist.es2015/index.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { dotCase } from \"dot-case\";\nexport function paramCase(input, options) {\n    if (options === void 0) { options = {}; }\n    return dotCase(input, __assign({ delimiter: \"-\" }, options));\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,OAAO,QAAQ,UAAU;AAClC,OAAO,SAASC,SAASA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACtC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IAAEA,OAAO,GAAG,CAAC,CAAC;EAAE;EACxC,OAAOH,OAAO,CAACE,KAAK,EAAEH,QAAQ,CAAC;IAAEK,SAAS,EAAE;EAAI,CAAC,EAAED,OAAO,CAAC,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}