{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nclass InMemoryStorage {\n  constructor() {\n    this.storage = new Map();\n  }\n  get length() {\n    return this.storage.size;\n  }\n  key(index) {\n    if (index > this.length - 1) {\n      return null;\n    }\n    return Array.from(this.storage.keys())[index];\n  }\n  setItem(key, value) {\n    this.storage.set(key, value);\n  }\n  getItem(key) {\n    return this.storage.get(key) ?? null;\n  }\n  removeItem(key) {\n    this.storage.delete(key);\n  }\n  clear() {\n    this.storage.clear();\n  }\n}\nexport { InMemoryStorage };", "map": {"version": 3, "names": ["InMemoryStorage", "constructor", "storage", "Map", "length", "size", "key", "index", "Array", "from", "keys", "setItem", "value", "set", "getItem", "get", "removeItem", "delete", "clear"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/storage/InMemoryStorage.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nclass InMemoryStorage {\n    constructor() {\n        this.storage = new Map();\n    }\n    get length() {\n        return this.storage.size;\n    }\n    key(index) {\n        if (index > this.length - 1) {\n            return null;\n        }\n        return Array.from(this.storage.keys())[index];\n    }\n    setItem(key, value) {\n        this.storage.set(key, value);\n    }\n    getItem(key) {\n        return this.storage.get(key) ?? null;\n    }\n    removeItem(key) {\n        this.storage.delete(key);\n    }\n    clear() {\n        this.storage.clear();\n    }\n}\n\nexport { InMemoryStorage };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,MAAMA,eAAe,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC5B;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACF,OAAO,CAACG,IAAI;EAC5B;EACAC,GAAGA,CAACC,KAAK,EAAE;IACP,IAAIA,KAAK,GAAG,IAAI,CAACH,MAAM,GAAG,CAAC,EAAE;MACzB,OAAO,IAAI;IACf;IACA,OAAOI,KAAK,CAACC,IAAI,CAAC,IAAI,CAACP,OAAO,CAACQ,IAAI,CAAC,CAAC,CAAC,CAACH,KAAK,CAAC;EACjD;EACAI,OAAOA,CAACL,GAAG,EAAEM,KAAK,EAAE;IAChB,IAAI,CAACV,OAAO,CAACW,GAAG,CAACP,GAAG,EAAEM,KAAK,CAAC;EAChC;EACAE,OAAOA,CAACR,GAAG,EAAE;IACT,OAAO,IAAI,CAACJ,OAAO,CAACa,GAAG,CAACT,GAAG,CAAC,IAAI,IAAI;EACxC;EACAU,UAAUA,CAACV,GAAG,EAAE;IACZ,IAAI,CAACJ,OAAO,CAACe,MAAM,CAACX,GAAG,CAAC;EAC5B;EACAY,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAChB,OAAO,CAACgB,KAAK,CAAC,CAAC;EACxB;AACJ;AAEA,SAASlB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}