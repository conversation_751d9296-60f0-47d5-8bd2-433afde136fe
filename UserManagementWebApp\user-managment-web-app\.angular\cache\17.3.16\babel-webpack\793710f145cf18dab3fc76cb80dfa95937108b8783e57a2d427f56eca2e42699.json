{"ast": null, "code": "import { getAtob } from '../../globalHelpers/index.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst base64Decoder = {\n  convert(input, options) {\n    let inputStr = input;\n    // urlSafe character replacement options conform to the base64 url spec\n    // https://datatracker.ietf.org/doc/html/rfc4648#page-7\n    if (options?.urlSafe) {\n      inputStr = inputStr.replace(/-/g, '+').replace(/_/g, '/');\n    }\n    return getAtob()(inputStr);\n  }\n};\nexport { base64Decoder };", "map": {"version": 3, "names": ["getAtob", "base64Decoder", "convert", "input", "options", "inputStr", "urlSafe", "replace"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/convert/base64/base64Decoder.mjs"], "sourcesContent": ["import { getAtob } from '../../globalHelpers/index.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst base64Decoder = {\n    convert(input, options) {\n        let inputStr = input;\n        // urlSafe character replacement options conform to the base64 url spec\n        // https://datatracker.ietf.org/doc/html/rfc4648#page-7\n        if (options?.urlSafe) {\n            inputStr = inputStr.replace(/-/g, '+').replace(/_/g, '/');\n        }\n        return getAtob()(inputStr);\n    },\n};\n\nexport { base64Decoder };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,+BAA+B;;AAEvD;AACA;AACA,MAAMC,aAAa,GAAG;EAClBC,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;IACpB,IAAIC,QAAQ,GAAGF,KAAK;IACpB;IACA;IACA,IAAIC,OAAO,EAAEE,OAAO,EAAE;MAClBD,QAAQ,GAAGA,QAAQ,CAACE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC7D;IACA,OAAOP,OAAO,CAAC,CAAC,CAACK,QAAQ,CAAC;EAC9B;AACJ,CAAC;AAED,SAASJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}