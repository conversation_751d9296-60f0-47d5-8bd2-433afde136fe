import { Injectable } from '@angular/core';
import { ColDef, GridOptions } from 'ag-grid-community';
import { DatastoresService } from 'src/app/core/services/datastores.service';
import { dateValueFormatter, formatCurrency, numericValueFormatter } from 'src/app/shared/utilities/format.functions';
import { TableConfig } from 'src/app/visualizations/models/table/table-config';
import { firstValueFrom } from 'rxjs';
import { ExtendedJsonSchema } from 'src/app/shared/models/extended-json-schema';
import { JsonDataTypes, JsonStringFormats } from 'src/app/shared/enums/json-schema.enum';
import { ColumnConfig } from 'src/app/visualizations/models/table/column-config';
import { AggregationTypes, SummaryValueFormats } from 'src/app/visualizations/enums/tiles.enums';

@Injectable({
    providedIn: 'root'
})
export class TableTileService {

    constructor(private datastoresService: DatastoresService) { }

    getGridOptions(): GridOptions {
        return {
            defaultColDef: {
                flex: 1,
            },
            getRowStyle: (params) => {
                if (params.node.rowPinned) {
                    return { fontWeight: 'bold' };
                }
            }
        };
    }

    async getColumnDefinitions(model: TableConfig): Promise<ColDef[]> {
        if (!model?.columns?.length) return [];

        const datastore = await firstValueFrom(this.datastoresService.getDatastore(model.datastoreId));
        if (!datastore?.baseSchema) return [];

        return model.columns.map(col => this.generateColumnDefinition(col, datastore.baseSchema, model));
    }

    generateColumnDefinition(col: ColumnConfig, schema, model: TableConfig): ColDef {
        const schemaCol = schema.properties[col.columnName];
        const colDef: ColDef = {
            field: col.columnName,
            headerName: col.displayName || schemaCol?.presentationProperties?.displayName || col.columnName,
            enableRowGroup: true,
            resizable: true,
            filter: true,
            floatingFilter: model.showFilterRow,
            sortable: true,
            minWidth: 200,
            hide: !col.visible,
            valueFormatter: params => this.formatValue(
                params,
                schemaCol?.type,
                schemaCol?.format,
                schemaCol?.typeProperties?.currency,
                schemaCol?.presentationProperties?.decimalPlaces,
                col.summaryRow?.valueFormat,
                col.summaryRow?.text,
                col.summaryRow?.decimalPlaces
            )
        };

        if (schemaCol?.type === JsonDataTypes.Boolean) {
            colDef.cellRenderer = this.getCheckBoxCellRenderer;
        }

        if (col.linkTemplate) {
            colDef.cellRenderer = params => this.getLinkCellRenderer(params, schemaCol, col);
        }

        // if it is numeric and the link text is not set, make the cell right align
        if ([JsonDataTypes.Decimal, JsonDataTypes.Integer].includes(schemaCol?.type) &&
            !(col.linkTemplate && col.linkText)) {
            colDef.cellStyle = { textAlign: 'right' };
            colDef.cellClass = 'sb-field-right-align';
        }

        return colDef;
    }

    getSummaryRow(data: any[], columns: ColumnConfig[]): any {
        const summaryRow = {};

        columns.forEach(column => {
            let value;
            if (column.summaryRow?.aggregation) {
                switch (column.summaryRow.aggregation) {
                    case AggregationTypes.Sum:
                        summaryRow[column.columnName] = data.reduce((sum, row) => sum + (row[column.columnName] || 0), 0);
                        break;
                    case AggregationTypes.Average:
                        value = data.reduce((sum, row) => sum + (row[column.columnName] || 0), 0) / data.length
                        summaryRow[column.columnName] = value == null || Number.isNaN(value) ? 0 : value;
                        break;
                    case AggregationTypes.Count:
                        summaryRow[column.columnName] = data.length;
                        break;
                }
            } else if (column.summaryRow?.text) {
                summaryRow[column.columnName] = column.summaryRow.text;
            } else {
                summaryRow[column.columnName] = '';
            }
        });

        return summaryRow;
    }

    getCheckBoxCellRenderer(params) {
        const inputElement = document.createElement('input');
        inputElement.type = 'checkbox';
        inputElement.checked = !!params.value;
        inputElement.disabled = true;
        return inputElement;
    }

    getLinkCellRenderer(params, schemaCol: ExtendedJsonSchema, col: ColumnConfig) {
        const linkElement = document.createElement('a');
        linkElement.href = this.populateTemplateWithValues(col.linkTemplate, params.data);
        linkElement.innerText = col.linkText || this.formatValue(
            params.value,
            schemaCol?.type,
            schemaCol?.format,
            schemaCol?.typeProperties?.currency,
            schemaCol?.presentationProperties?.decimalPlaces
        );
        linkElement.target = '_blank';
        return linkElement;
    }

    populateTemplateWithValues(template: string, data: any): string {
        for (const key in data) {
            if (key in data) {
                template = template.replace(`{${key}}`, data[key]);
            }
        }
        return template;
    }

    formatValue(params, type: JsonDataTypes, format: JsonStringFormats, currency?: string, decimalPlaces?: number, sumValueFormat?: SummaryValueFormats, sumText?: string, sumDP?: number) {
        if (params.value === null || type === null)
            return params.value;

        let formattedValue = params.value;
        
        if (!params.node.rowPinned || sumValueFormat === SummaryValueFormats.Inherit) {
            switch (type) {
                case JsonDataTypes.String:
                    formattedValue = [JsonStringFormats.Date, JsonStringFormats.DateTime].includes(format) ? dateValueFormatter(formattedValue) : formattedValue;
                    break;
                case JsonDataTypes.Decimal:
                case JsonDataTypes.Integer:
                    formattedValue = currency ? formatCurrency(formattedValue, currency, decimalPlaces) : numericValueFormatter(formattedValue, decimalPlaces);
                    break;
            }
        }

        // pinned row format (summary)
        if (params.node.rowPinned) {
            switch (sumValueFormat) {
                case SummaryValueFormats.Numeric:
                    formattedValue = numericValueFormatter(formattedValue, sumDP ?? 0);
                    break;
            }

            if (sumText) {
                formattedValue = sumText.replace('{value}', formattedValue);
            }
        }

        return formattedValue;
    }
}
