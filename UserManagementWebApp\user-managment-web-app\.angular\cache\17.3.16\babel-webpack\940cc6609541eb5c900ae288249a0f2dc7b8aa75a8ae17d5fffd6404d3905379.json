{"ast": null, "code": "import { defaultTheme } from '../defaultTheme.mjs';\nimport { deepExtend, setupTokens, flattenProperties, setupToken } from './utils.mjs';\nimport { createComponentCSS } from './createComponentCSS.mjs';\nimport '@aws-amplify/core/internals/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { isString } from '../../utils/utils.mjs';\nimport { createColorPalette } from './createColorPalette.mjs';\nimport { createAnimationCSS } from './createAnimationCSS.mjs';\n\n/**\n * This will be used like `const myTheme = createTheme({})`\n * `myTheme` can then be passed to a Provider or the generated CSS\n * can be passed to a stylesheet at build-time or run-time.\n * const myTheme = createTheme({})\n * const myOtherTheme = createTheme({}, myTheme);\n */\nfunction createTheme(theme, DefaultTheme = defaultTheme) {\n  // merge theme and DefaultTheme to get a complete theme\n  // deepExtend is an internal Style Dictionary method\n  // that performs a deep merge on n objects. We could change\n  // this to another 3p deep merge solution too.\n  const mergedTheme = deepExtend([{}, DefaultTheme, {\n    ...theme,\n    components: {}\n  }]);\n  const {\n    primaryColor,\n    secondaryColor\n  } = mergedTheme;\n  // apply primaryColor and secondaryColor if present\n  if (isString(primaryColor)) {\n    mergedTheme.tokens.colors.primary = createColorPalette({\n      keys: Object.keys(mergedTheme.tokens.colors[primaryColor]),\n      value: primaryColor\n    });\n  }\n  if (isString(secondaryColor)) {\n    mergedTheme.tokens.colors.secondary = createColorPalette({\n      keys: Object.keys(mergedTheme.tokens.colors[secondaryColor]),\n      value: secondaryColor\n    });\n  }\n  // Setting up the tokens. This is similar to what Style Dictionary\n  // does. At the end of this, each token should have:\n  // - CSS variable name of itself\n  // - its value (reference to another CSS variable or raw value)\n  const tokens = setupTokens({\n    tokens: mergedTheme.tokens,\n    setupToken\n  }); // Setting the type here because setupTokens is recursive\n  const {\n    breakpoints,\n    name\n  } = mergedTheme;\n  // flattenProperties is another internal Style Dictionary function\n  // that creates an array of all tokens.\n  let cssText = `[data-amplify-theme=\"${name}\"] {\\n` + flattenProperties(tokens).map(token => `${token.name}: ${token.value};`).join('\\n') + `\\n}\\n`;\n  if (theme?.components) {\n    cssText += createComponentCSS({\n      theme: {\n        ...mergedTheme,\n        tokens\n      },\n      components: theme.components\n    });\n  }\n  let overrides = [];\n  if (mergedTheme.animations) {\n    cssText += createAnimationCSS({\n      animations: mergedTheme.animations,\n      tokens\n    });\n  }\n  /**\n   * For each override, we setup the tokens and then generate the CSS.\n   * This allows us to have one single CSS string for all possible overrides\n   * and avoid re-renders in React, but also support other frameworks as well.\n   */\n  if (mergedTheme.overrides) {\n    overrides = mergedTheme.overrides.map(override => {\n      const overrideTokens = setupTokens({\n        tokens: override.tokens,\n        setupToken\n      });\n      const customProperties = flattenProperties(overrideTokens).map(token => `${token.name}: ${token.value};`).join('\\n');\n      // Overrides can have a selector, media query, breakpoint, or color mode\n      // for creating the selector\n      if ('selector' in override) {\n        cssText += `\\n${override.selector} {\\n${customProperties}\\n}\\n`;\n      }\n      if ('mediaQuery' in override) {\n        cssText += `\\n@media (${override.mediaQuery}) {\n  [data-amplify-theme=\"${name}\"] {\n    ${customProperties}\n  }\n}\\n`;\n      }\n      if ('breakpoint' in override) {\n        const breakpoint = mergedTheme.breakpoints.values[override.breakpoint];\n        cssText += `\\n@media (min-width: ${breakpoint}px) {\n  [data-amplify-theme=\"${name}\"] {\n    ${customProperties}\n  }\n}\\n`;\n      }\n      if ('colorMode' in override) {\n        cssText += `\\n@media (prefers-color-scheme: ${override.colorMode}) {\n          [data-amplify-theme=\"${name}\"][data-amplify-color-mode=\"system\"] {\n            ${customProperties}\n            color-scheme: ${override.colorMode};\n          }\n        }\\n`;\n        cssText += `\\n[data-amplify-theme=\"${name}\"][data-amplify-color-mode=\"${override.colorMode}\"] {\n          ${customProperties}\n          color-scheme: ${override.colorMode};\n        }\\n`;\n      }\n      return {\n        ...override,\n        tokens: overrideTokens\n      };\n    });\n  }\n  return {\n    tokens,\n    breakpoints,\n    name,\n    cssText,\n    containerProps: ({\n      colorMode\n    } = {}) => {\n      return {\n        'data-amplify-theme': name,\n        'data-amplify-color-mode': colorMode\n      };\n    },\n    // keep overrides separate from base theme\n    // this allows web platforms to use plain CSS scoped to a\n    // selector and only override the CSS vars needed. This\n    // means we could generate CSS at build-time in a postcss\n    // plugin, or do it at runtime and inject the CSS into a\n    // style tag.\n    // This also allows RN to dynamically switch themes in a\n    // provider.\n    overrides\n  };\n}\nexport { createTheme };", "map": {"version": 3, "names": ["defaultTheme", "deepExtend", "setupTokens", "flattenProperties", "setupToken", "createComponentCSS", "isString", "createColorPalette", "createAnimationCSS", "createTheme", "theme", "DefaultTheme", "mergedTheme", "components", "primaryColor", "secondaryColor", "tokens", "colors", "primary", "keys", "Object", "value", "secondary", "breakpoints", "name", "cssText", "map", "token", "join", "overrides", "animations", "override", "overrideTokens", "customProperties", "selector", "mediaQuery", "breakpoint", "values", "colorMode", "containerProps"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/createTheme/createTheme.mjs"], "sourcesContent": ["import { defaultTheme } from '../defaultTheme.mjs';\nimport { deepExtend, setupTokens, flattenProperties, setupToken } from './utils.mjs';\nimport { createComponentCSS } from './createComponentCSS.mjs';\nimport '@aws-amplify/core/internals/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { isString } from '../../utils/utils.mjs';\nimport { createColorPalette } from './createColorPalette.mjs';\nimport { createAnimationCSS } from './createAnimationCSS.mjs';\n\n/**\n * This will be used like `const myTheme = createTheme({})`\n * `myTheme` can then be passed to a Provider or the generated CSS\n * can be passed to a stylesheet at build-time or run-time.\n * const myTheme = createTheme({})\n * const myOtherTheme = createTheme({}, myTheme);\n */\nfunction createTheme(theme, DefaultTheme = defaultTheme) {\n    // merge theme and DefaultTheme to get a complete theme\n    // deepExtend is an internal Style Dictionary method\n    // that performs a deep merge on n objects. We could change\n    // this to another 3p deep merge solution too.\n    const mergedTheme = deepExtend([\n        {},\n        DefaultTheme,\n        {\n            ...theme,\n            components: {},\n        },\n    ]);\n    const { primaryColor, secondaryColor } = mergedTheme;\n    // apply primaryColor and secondaryColor if present\n    if (isString(primaryColor)) {\n        mergedTheme.tokens.colors.primary = createColorPalette({\n            keys: Object.keys(mergedTheme.tokens.colors[primaryColor]),\n            value: primaryColor,\n        });\n    }\n    if (isString(secondaryColor)) {\n        mergedTheme.tokens.colors.secondary = createColorPalette({\n            keys: Object.keys(mergedTheme.tokens.colors[secondaryColor]),\n            value: secondaryColor,\n        });\n    }\n    // Setting up the tokens. This is similar to what Style Dictionary\n    // does. At the end of this, each token should have:\n    // - CSS variable name of itself\n    // - its value (reference to another CSS variable or raw value)\n    const tokens = setupTokens({\n        tokens: mergedTheme.tokens,\n        setupToken,\n    }); // Setting the type here because setupTokens is recursive\n    const { breakpoints, name } = mergedTheme;\n    // flattenProperties is another internal Style Dictionary function\n    // that creates an array of all tokens.\n    let cssText = `[data-amplify-theme=\"${name}\"] {\\n` +\n        flattenProperties(tokens)\n            .map((token) => `${token.name}: ${token.value};`)\n            .join('\\n') +\n        `\\n}\\n`;\n    if (theme?.components) {\n        cssText += createComponentCSS({\n            theme: {\n                ...mergedTheme,\n                tokens,\n            },\n            components: theme.components,\n        });\n    }\n    let overrides = [];\n    if (mergedTheme.animations) {\n        cssText += createAnimationCSS({\n            animations: mergedTheme.animations,\n            tokens,\n        });\n    }\n    /**\n     * For each override, we setup the tokens and then generate the CSS.\n     * This allows us to have one single CSS string for all possible overrides\n     * and avoid re-renders in React, but also support other frameworks as well.\n     */\n    if (mergedTheme.overrides) {\n        overrides = mergedTheme.overrides.map((override) => {\n            const overrideTokens = setupTokens({\n                tokens: override.tokens,\n                setupToken,\n            });\n            const customProperties = flattenProperties(overrideTokens)\n                .map((token) => `${token.name}: ${token.value};`)\n                .join('\\n');\n            // Overrides can have a selector, media query, breakpoint, or color mode\n            // for creating the selector\n            if ('selector' in override) {\n                cssText += `\\n${override.selector} {\\n${customProperties}\\n}\\n`;\n            }\n            if ('mediaQuery' in override) {\n                cssText += `\\n@media (${override.mediaQuery}) {\n  [data-amplify-theme=\"${name}\"] {\n    ${customProperties}\n  }\n}\\n`;\n            }\n            if ('breakpoint' in override) {\n                const breakpoint = mergedTheme.breakpoints.values[override.breakpoint];\n                cssText += `\\n@media (min-width: ${breakpoint}px) {\n  [data-amplify-theme=\"${name}\"] {\n    ${customProperties}\n  }\n}\\n`;\n            }\n            if ('colorMode' in override) {\n                cssText += `\\n@media (prefers-color-scheme: ${override.colorMode}) {\n          [data-amplify-theme=\"${name}\"][data-amplify-color-mode=\"system\"] {\n            ${customProperties}\n            color-scheme: ${override.colorMode};\n          }\n        }\\n`;\n                cssText += `\\n[data-amplify-theme=\"${name}\"][data-amplify-color-mode=\"${override.colorMode}\"] {\n          ${customProperties}\n          color-scheme: ${override.colorMode};\n        }\\n`;\n            }\n            return {\n                ...override,\n                tokens: overrideTokens,\n            };\n        });\n    }\n    return {\n        tokens,\n        breakpoints,\n        name,\n        cssText,\n        containerProps: ({ colorMode } = {}) => {\n            return {\n                'data-amplify-theme': name,\n                'data-amplify-color-mode': colorMode,\n            };\n        },\n        // keep overrides separate from base theme\n        // this allows web platforms to use plain CSS scoped to a\n        // selector and only override the CSS vars needed. This\n        // means we could generate CSS at build-time in a postcss\n        // plugin, or do it at runtime and inject the CSS into a\n        // style tag.\n        // This also allows RN to dynamically switch themes in a\n        // provider.\n        overrides,\n    };\n}\n\nexport { createTheme };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,qBAAqB;AAClD,SAASC,UAAU,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,aAAa;AACpF,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,OAAO,mCAAmC;AAC1C,OAAO,wCAAwC;AAC/C,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,kBAAkB,QAAQ,0BAA0B;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAEC,YAAY,GAAGX,YAAY,EAAE;EACrD;EACA;EACA;EACA;EACA,MAAMY,WAAW,GAAGX,UAAU,CAAC,CAC3B,CAAC,CAAC,EACFU,YAAY,EACZ;IACI,GAAGD,KAAK;IACRG,UAAU,EAAE,CAAC;EACjB,CAAC,CACJ,CAAC;EACF,MAAM;IAAEC,YAAY;IAAEC;EAAe,CAAC,GAAGH,WAAW;EACpD;EACA,IAAIN,QAAQ,CAACQ,YAAY,CAAC,EAAE;IACxBF,WAAW,CAACI,MAAM,CAACC,MAAM,CAACC,OAAO,GAAGX,kBAAkB,CAAC;MACnDY,IAAI,EAAEC,MAAM,CAACD,IAAI,CAACP,WAAW,CAACI,MAAM,CAACC,MAAM,CAACH,YAAY,CAAC,CAAC;MAC1DO,KAAK,EAAEP;IACX,CAAC,CAAC;EACN;EACA,IAAIR,QAAQ,CAACS,cAAc,CAAC,EAAE;IAC1BH,WAAW,CAACI,MAAM,CAACC,MAAM,CAACK,SAAS,GAAGf,kBAAkB,CAAC;MACrDY,IAAI,EAAEC,MAAM,CAACD,IAAI,CAACP,WAAW,CAACI,MAAM,CAACC,MAAM,CAACF,cAAc,CAAC,CAAC;MAC5DM,KAAK,EAAEN;IACX,CAAC,CAAC;EACN;EACA;EACA;EACA;EACA;EACA,MAAMC,MAAM,GAAGd,WAAW,CAAC;IACvBc,MAAM,EAAEJ,WAAW,CAACI,MAAM;IAC1BZ;EACJ,CAAC,CAAC,CAAC,CAAC;EACJ,MAAM;IAAEmB,WAAW;IAAEC;EAAK,CAAC,GAAGZ,WAAW;EACzC;EACA;EACA,IAAIa,OAAO,GAAG,wBAAwBD,IAAI,QAAQ,GAC9CrB,iBAAiB,CAACa,MAAM,CAAC,CACpBU,GAAG,CAAEC,KAAK,IAAK,GAAGA,KAAK,CAACH,IAAI,KAAKG,KAAK,CAACN,KAAK,GAAG,CAAC,CAChDO,IAAI,CAAC,IAAI,CAAC,GACf,OAAO;EACX,IAAIlB,KAAK,EAAEG,UAAU,EAAE;IACnBY,OAAO,IAAIpB,kBAAkB,CAAC;MAC1BK,KAAK,EAAE;QACH,GAAGE,WAAW;QACdI;MACJ,CAAC;MACDH,UAAU,EAAEH,KAAK,CAACG;IACtB,CAAC,CAAC;EACN;EACA,IAAIgB,SAAS,GAAG,EAAE;EAClB,IAAIjB,WAAW,CAACkB,UAAU,EAAE;IACxBL,OAAO,IAAIjB,kBAAkB,CAAC;MAC1BsB,UAAU,EAAElB,WAAW,CAACkB,UAAU;MAClCd;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIJ,WAAW,CAACiB,SAAS,EAAE;IACvBA,SAAS,GAAGjB,WAAW,CAACiB,SAAS,CAACH,GAAG,CAAEK,QAAQ,IAAK;MAChD,MAAMC,cAAc,GAAG9B,WAAW,CAAC;QAC/Bc,MAAM,EAAEe,QAAQ,CAACf,MAAM;QACvBZ;MACJ,CAAC,CAAC;MACF,MAAM6B,gBAAgB,GAAG9B,iBAAiB,CAAC6B,cAAc,CAAC,CACrDN,GAAG,CAAEC,KAAK,IAAK,GAAGA,KAAK,CAACH,IAAI,KAAKG,KAAK,CAACN,KAAK,GAAG,CAAC,CAChDO,IAAI,CAAC,IAAI,CAAC;MACf;MACA;MACA,IAAI,UAAU,IAAIG,QAAQ,EAAE;QACxBN,OAAO,IAAI,KAAKM,QAAQ,CAACG,QAAQ,OAAOD,gBAAgB,OAAO;MACnE;MACA,IAAI,YAAY,IAAIF,QAAQ,EAAE;QAC1BN,OAAO,IAAI,aAAaM,QAAQ,CAACI,UAAU;AAC3D,yBAAyBX,IAAI;AAC7B,MAAMS,gBAAgB;AACtB;AACA,IAAI;MACQ;MACA,IAAI,YAAY,IAAIF,QAAQ,EAAE;QAC1B,MAAMK,UAAU,GAAGxB,WAAW,CAACW,WAAW,CAACc,MAAM,CAACN,QAAQ,CAACK,UAAU,CAAC;QACtEX,OAAO,IAAI,wBAAwBW,UAAU;AAC7D,yBAAyBZ,IAAI;AAC7B,MAAMS,gBAAgB;AACtB;AACA,IAAI;MACQ;MACA,IAAI,WAAW,IAAIF,QAAQ,EAAE;QACzBN,OAAO,IAAI,mCAAmCM,QAAQ,CAACO,SAAS;AAChF,iCAAiCd,IAAI;AACrC,cAAcS,gBAAgB;AAC9B,4BAA4BF,QAAQ,CAACO,SAAS;AAC9C;AACA,YAAY;QACIb,OAAO,IAAI,0BAA0BD,IAAI,+BAA+BO,QAAQ,CAACO,SAAS;AAC1G,YAAYL,gBAAgB;AAC5B,0BAA0BF,QAAQ,CAACO,SAAS;AAC5C,YAAY;MACA;MACA,OAAO;QACH,GAAGP,QAAQ;QACXf,MAAM,EAAEgB;MACZ,CAAC;IACL,CAAC,CAAC;EACN;EACA,OAAO;IACHhB,MAAM;IACNO,WAAW;IACXC,IAAI;IACJC,OAAO;IACPc,cAAc,EAAEA,CAAC;MAAED;IAAU,CAAC,GAAG,CAAC,CAAC,KAAK;MACpC,OAAO;QACH,oBAAoB,EAAEd,IAAI;QAC1B,yBAAyB,EAAEc;MAC/B,CAAC;IACL,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAT;EACJ,CAAC;AACL;AAEA,SAASpB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}