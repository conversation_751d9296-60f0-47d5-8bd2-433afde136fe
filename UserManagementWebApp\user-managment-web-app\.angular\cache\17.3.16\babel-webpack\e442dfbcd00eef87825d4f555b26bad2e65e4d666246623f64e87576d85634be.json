{"ast": null, "code": "import { tokens } from './tokens/index.mjs';\nimport { breakpoints } from './breakpoints.mjs';\nconst defaultTheme = {\n  tokens,\n  breakpoints,\n  name: 'default-theme'\n};\nexport { defaultTheme };", "map": {"version": 3, "names": ["tokens", "breakpoints", "defaultTheme", "name"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/defaultTheme.mjs"], "sourcesContent": ["import { tokens } from './tokens/index.mjs';\nimport { breakpoints } from './breakpoints.mjs';\n\nconst defaultTheme = {\n    tokens,\n    breakpoints,\n    name: 'default-theme',\n};\n\nexport { defaultTheme };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,WAAW,QAAQ,mBAAmB;AAE/C,MAAMC,YAAY,GAAG;EACjBF,MAAM;EACNC,WAAW;EACXE,IAAI,EAAE;AACV,CAAC;AAED,SAASD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}