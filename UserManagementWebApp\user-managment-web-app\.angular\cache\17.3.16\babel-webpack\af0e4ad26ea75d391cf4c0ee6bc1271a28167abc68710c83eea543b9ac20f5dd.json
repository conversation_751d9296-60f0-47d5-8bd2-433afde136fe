{"ast": null, "code": "import { base64Decoder } from '../../../utils/convert/base64/base64Decoder.mjs';\nimport '../../../types/errors.mjs';\nimport '../../../errors/errorHelpers.mjs';\nimport { assert, AuthConfigurationErrorCode } from './errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction assertTokenProviderConfig(cognitoConfig) {\n  let assertionValid = true; // assume valid until otherwise proveed\n  if (!cognitoConfig) {\n    assertionValid = false;\n  } else {\n    assertionValid = !!cognitoConfig.userPoolId && !!cognitoConfig.userPoolClientId;\n  }\n  assert(assertionValid, AuthConfigurationErrorCode.AuthUserPoolException);\n}\nfunction assertOAuthConfig(cognitoConfig) {\n  const validOAuthConfig = !!cognitoConfig?.loginWith?.oauth?.domain && !!cognitoConfig?.loginWith?.oauth?.redirectSignOut && !!cognitoConfig?.loginWith?.oauth?.redirectSignIn && !!cognitoConfig?.loginWith?.oauth?.responseType;\n  assert(validOAuthConfig, AuthConfigurationErrorCode.OAuthNotConfigureException);\n}\nfunction assertIdentityPoolIdConfig(cognitoConfig) {\n  const validConfig = !!cognitoConfig?.identityPoolId;\n  assert(validConfig, AuthConfigurationErrorCode.InvalidIdentityPoolIdException);\n}\n/**\n * Decodes payload of JWT token\n *\n * @param {String} token A string representing a token to be decoded\n * @throws {@link Error} - Throws error when token is invalid or payload malformed.\n */\nfunction decodeJWT(token) {\n  const tokenParts = token.split('.');\n  if (tokenParts.length !== 3) {\n    throw new Error('Invalid token');\n  }\n  try {\n    const base64WithUrlSafe = tokenParts[1];\n    const base64 = base64WithUrlSafe.replace(/-/g, '+').replace(/_/g, '/');\n    const jsonStr = decodeURIComponent(base64Decoder.convert(base64).split('').map(char => `%${`00${char.charCodeAt(0).toString(16)}`.slice(-2)}`).join(''));\n    const payload = JSON.parse(jsonStr);\n    return {\n      toString: () => token,\n      payload\n    };\n  } catch (err) {\n    throw new Error('Invalid token payload');\n  }\n}\nexport { assertIdentityPoolIdConfig, assertOAuthConfig, assertTokenProviderConfig, decodeJWT };", "map": {"version": 3, "names": ["base64Decoder", "assert", "AuthConfigurationErrorCode", "assertTokenProviderConfig", "cognitoConfig", "assertionValid", "userPoolId", "userPoolClientId", "AuthUserPoolException", "assertOAuthConfig", "validOAuthConfig", "loginWith", "o<PERSON>h", "domain", "redirectSignOut", "redirectSignIn", "responseType", "OAuthNotConfigureException", "assertIdentityPoolIdConfig", "validConfig", "identityPoolId", "InvalidIdentityPoolIdException", "decodeJWT", "token", "tokenParts", "split", "length", "Error", "base64WithUrlSafe", "base64", "replace", "jsonStr", "decodeURIComponent", "convert", "map", "char", "charCodeAt", "toString", "slice", "join", "payload", "JSON", "parse", "err"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/singleton/Auth/utils/index.mjs"], "sourcesContent": ["import { base64Decoder } from '../../../utils/convert/base64/base64Decoder.mjs';\nimport '../../../types/errors.mjs';\nimport '../../../errors/errorHelpers.mjs';\nimport { assert, AuthConfigurationErrorCode } from './errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction assertTokenProviderConfig(cognitoConfig) {\n    let assertionValid = true; // assume valid until otherwise proveed\n    if (!cognitoConfig) {\n        assertionValid = false;\n    }\n    else {\n        assertionValid =\n            !!cognitoConfig.userPoolId && !!cognitoConfig.userPoolClientId;\n    }\n    assert(assertionValid, AuthConfigurationErrorCode.AuthUserPoolException);\n}\nfunction assertOAuthConfig(cognitoConfig) {\n    const validOAuthConfig = !!cognitoConfig?.loginWith?.oauth?.domain &&\n        !!cognitoConfig?.loginWith?.oauth?.redirectSignOut &&\n        !!cognitoConfig?.loginWith?.oauth?.redirectSignIn &&\n        !!cognitoConfig?.loginWith?.oauth?.responseType;\n    assert(validOAuthConfig, AuthConfigurationErrorCode.OAuthNotConfigureException);\n}\nfunction assertIdentityPoolIdConfig(cognitoConfig) {\n    const validConfig = !!cognitoConfig?.identityPoolId;\n    assert(validConfig, AuthConfigurationErrorCode.InvalidIdentityPoolIdException);\n}\n/**\n * Decodes payload of JWT token\n *\n * @param {String} token A string representing a token to be decoded\n * @throws {@link Error} - Throws error when token is invalid or payload malformed.\n */\nfunction decodeJWT(token) {\n    const tokenParts = token.split('.');\n    if (tokenParts.length !== 3) {\n        throw new Error('Invalid token');\n    }\n    try {\n        const base64WithUrlSafe = tokenParts[1];\n        const base64 = base64WithUrlSafe.replace(/-/g, '+').replace(/_/g, '/');\n        const jsonStr = decodeURIComponent(base64Decoder\n            .convert(base64)\n            .split('')\n            .map(char => `%${`00${char.charCodeAt(0).toString(16)}`.slice(-2)}`)\n            .join(''));\n        const payload = JSON.parse(jsonStr);\n        return {\n            toString: () => token,\n            payload,\n        };\n    }\n    catch (err) {\n        throw new Error('Invalid token payload');\n    }\n}\n\nexport { assertIdentityPoolIdConfig, assertOAuthConfig, assertTokenProviderConfig, decodeJWT };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iDAAiD;AAC/E,OAAO,2BAA2B;AAClC,OAAO,kCAAkC;AACzC,SAASC,MAAM,EAAEC,0BAA0B,QAAQ,oBAAoB;;AAEvE;AACA;AACA,SAASC,yBAAyBA,CAACC,aAAa,EAAE;EAC9C,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAC;EAC3B,IAAI,CAACD,aAAa,EAAE;IAChBC,cAAc,GAAG,KAAK;EAC1B,CAAC,MACI;IACDA,cAAc,GACV,CAAC,CAACD,aAAa,CAACE,UAAU,IAAI,CAAC,CAACF,aAAa,CAACG,gBAAgB;EACtE;EACAN,MAAM,CAACI,cAAc,EAAEH,0BAA0B,CAACM,qBAAqB,CAAC;AAC5E;AACA,SAASC,iBAAiBA,CAACL,aAAa,EAAE;EACtC,MAAMM,gBAAgB,GAAG,CAAC,CAACN,aAAa,EAAEO,SAAS,EAAEC,KAAK,EAAEC,MAAM,IAC9D,CAAC,CAACT,aAAa,EAAEO,SAAS,EAAEC,KAAK,EAAEE,eAAe,IAClD,CAAC,CAACV,aAAa,EAAEO,SAAS,EAAEC,KAAK,EAAEG,cAAc,IACjD,CAAC,CAACX,aAAa,EAAEO,SAAS,EAAEC,KAAK,EAAEI,YAAY;EACnDf,MAAM,CAACS,gBAAgB,EAAER,0BAA0B,CAACe,0BAA0B,CAAC;AACnF;AACA,SAASC,0BAA0BA,CAACd,aAAa,EAAE;EAC/C,MAAMe,WAAW,GAAG,CAAC,CAACf,aAAa,EAAEgB,cAAc;EACnDnB,MAAM,CAACkB,WAAW,EAAEjB,0BAA0B,CAACmB,8BAA8B,CAAC;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAE;EACtB,MAAMC,UAAU,GAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC;EACnC,IAAID,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;IACzB,MAAM,IAAIC,KAAK,CAAC,eAAe,CAAC;EACpC;EACA,IAAI;IACA,MAAMC,iBAAiB,GAAGJ,UAAU,CAAC,CAAC,CAAC;IACvC,MAAMK,MAAM,GAAGD,iBAAiB,CAACE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IACtE,MAAMC,OAAO,GAAGC,kBAAkB,CAAChC,aAAa,CAC3CiC,OAAO,CAACJ,MAAM,CAAC,CACfJ,KAAK,CAAC,EAAE,CAAC,CACTS,GAAG,CAACC,IAAI,IAAI,IAAI,KAAKA,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CACnEC,IAAI,CAAC,EAAE,CAAC,CAAC;IACd,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACX,OAAO,CAAC;IACnC,OAAO;MACHM,QAAQ,EAAEA,CAAA,KAAMd,KAAK;MACrBiB;IACJ,CAAC;EACL,CAAC,CACD,OAAOG,GAAG,EAAE;IACR,MAAM,IAAIhB,KAAK,CAAC,uBAAuB,CAAC;EAC5C;AACJ;AAEA,SAAST,0BAA0B,EAAET,iBAAiB,EAAEN,yBAAyB,EAAEmB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}