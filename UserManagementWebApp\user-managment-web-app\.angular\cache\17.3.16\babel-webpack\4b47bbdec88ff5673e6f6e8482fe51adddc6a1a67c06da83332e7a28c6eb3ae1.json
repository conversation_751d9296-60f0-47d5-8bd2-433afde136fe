{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createListDevicesClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createListDevicesClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Cognito Documentation for max device\n// https://docs.aws.amazon.com/cognito-user-identity-pools/latest/APIReference/API_ListDevices.html#API_ListDevices_RequestSyntax\nconst MAX_DEVICES = 60;\n/**\n * Fetches devices that have been remembered using {@link rememberDevice}\n * for the currently authenticated user.\n *\n * @returns FetchDevicesOutput\n * @throws {@link ListDevicesException}\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nfunction fetchDevices() {\n  return _fetchDevices.apply(this, arguments);\n}\nfunction _fetchDevices() {\n  _fetchDevices = _asyncToGenerator(function* () {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolEndpoint,\n      userPoolId\n    } = authConfig;\n    const {\n      tokens\n    } = yield fetchAuthSession();\n    assertAuthTokens(tokens);\n    const listDevices = createListDevicesClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const response = yield listDevices({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.FetchDevices)\n    }, {\n      AccessToken: tokens.accessToken.toString(),\n      Limit: MAX_DEVICES\n    });\n    return parseDevicesResponse(response.Devices ?? []);\n  });\n  return _fetchDevices.apply(this, arguments);\n}\nconst parseDevicesResponse = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (devices) {\n    return devices.map(({\n      DeviceKey: id = '',\n      DeviceAttributes = [],\n      DeviceCreateDate,\n      DeviceLastModifiedDate,\n      DeviceLastAuthenticatedDate\n    }) => {\n      let deviceName;\n      const attributes = DeviceAttributes.reduce((attrs, {\n        Name,\n        Value\n      }) => {\n        if (Name && Value) {\n          if (Name === 'device_name') deviceName = Value;\n          attrs[Name] = Value;\n        }\n        return attrs;\n      }, {});\n      const result = {\n        id,\n        name: deviceName,\n        attributes,\n        createDate: DeviceCreateDate ? new Date(DeviceCreateDate * 1000) : undefined,\n        lastModifiedDate: DeviceLastModifiedDate ? new Date(DeviceLastModifiedDate * 1000) : undefined,\n        lastAuthenticatedDate: DeviceLastAuthenticatedDate ? new Date(DeviceLastAuthenticatedDate * 1000) : undefined\n      };\n      return result;\n    });\n  });\n  return function parseDevicesResponse(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { fetchDevices };", "map": {"version": 3, "names": ["Amplify", "fetchAuthSession", "assertTokenProviderConfig", "AuthAction", "assertAuthTokens", "getRegionFromUserPoolId", "getAuthUserAgentValue", "createListDevicesClient", "createCognitoUserPoolEndpointResolver", "MAX_DEVICES", "fetchDevices", "_fetchDevices", "apply", "arguments", "_asyncToGenerator", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userPoolEndpoint", "userPoolId", "tokens", "listDevices", "endpointResolver", "endpointOverride", "response", "region", "userAgentValue", "FetchDevices", "AccessToken", "accessToken", "toString", "Limit", "parseDevicesResponse", "Devices", "_ref", "devices", "map", "<PERSON><PERSON><PERSON><PERSON>", "id", "DeviceAttributes", "DeviceCreateDate", "DeviceLastModifiedDate", "DeviceLastAuthenticatedDate", "deviceName", "attributes", "reduce", "attrs", "Name", "Value", "result", "name", "createDate", "Date", "undefined", "lastModifiedDate", "lastAuthenticatedDate", "_x"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/fetchDevices.mjs"], "sourcesContent": ["import { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createListDevicesClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createListDevicesClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Cognito Documentation for max device\n// https://docs.aws.amazon.com/cognito-user-identity-pools/latest/APIReference/API_ListDevices.html#API_ListDevices_RequestSyntax\nconst MAX_DEVICES = 60;\n/**\n * Fetches devices that have been remembered using {@link rememberDevice}\n * for the currently authenticated user.\n *\n * @returns FetchDevicesOutput\n * @throws {@link ListDevicesException}\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nasync function fetchDevices() {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolEndpoint, userPoolId } = authConfig;\n    const { tokens } = await fetchAuthSession();\n    assertAuthTokens(tokens);\n    const listDevices = createListDevicesClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const response = await listDevices({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.FetchDevices),\n    }, {\n        AccessToken: tokens.accessToken.toString(),\n        Limit: MAX_DEVICES,\n    });\n    return parseDevicesResponse(response.Devices ?? []);\n}\nconst parseDevicesResponse = async (devices) => {\n    return devices.map(({ DeviceKey: id = '', DeviceAttributes = [], DeviceCreateDate, DeviceLastModifiedDate, DeviceLastAuthenticatedDate, }) => {\n        let deviceName;\n        const attributes = DeviceAttributes.reduce((attrs, { Name, Value }) => {\n            if (Name && Value) {\n                if (Name === 'device_name')\n                    deviceName = Value;\n                attrs[Name] = Value;\n            }\n            return attrs;\n        }, {});\n        const result = {\n            id,\n            name: deviceName,\n            attributes,\n            createDate: DeviceCreateDate\n                ? new Date(DeviceCreateDate * 1000)\n                : undefined,\n            lastModifiedDate: DeviceLastModifiedDate\n                ? new Date(DeviceLastModifiedDate * 1000)\n                : undefined,\n            lastAuthenticatedDate: DeviceLastAuthenticatedDate\n                ? new Date(DeviceLastAuthenticatedDate * 1000)\n                : undefined,\n        };\n        return result;\n    });\n};\n\nexport { fetchDevices };\n"], "mappings": ";AAAA,SAASA,OAAO,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC7D,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,sCAAsC;AAC7C,OAAO,qBAAqB;AAC5B,SAASC,uBAAuB,QAAQ,kGAAkG;AAC1I,SAASC,qCAAqC,QAAQ,wDAAwD;;AAE9G;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG,EAAE;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAQeC,YAAYA,CAAA;EAAA,OAAAC,aAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,cAAA;EAAAA,aAAA,GAAAG,iBAAA,CAA3B,aAA8B;IAC1B,MAAMC,UAAU,GAAGf,OAAO,CAACgB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDhB,yBAAyB,CAACa,UAAU,CAAC;IACrC,MAAM;MAAEI,gBAAgB;MAAEC;IAAW,CAAC,GAAGL,UAAU;IACnD,MAAM;MAAEM;IAAO,CAAC,SAASpB,gBAAgB,CAAC,CAAC;IAC3CG,gBAAgB,CAACiB,MAAM,CAAC;IACxB,MAAMC,WAAW,GAAGf,uBAAuB,CAAC;MACxCgB,gBAAgB,EAAEf,qCAAqC,CAAC;QACpDgB,gBAAgB,EAAEL;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMM,QAAQ,SAASH,WAAW,CAAC;MAC/BI,MAAM,EAAErB,uBAAuB,CAACe,UAAU,CAAC;MAC3CO,cAAc,EAAErB,qBAAqB,CAACH,UAAU,CAACyB,YAAY;IACjE,CAAC,EAAE;MACCC,WAAW,EAAER,MAAM,CAACS,WAAW,CAACC,QAAQ,CAAC,CAAC;MAC1CC,KAAK,EAAEvB;IACX,CAAC,CAAC;IACF,OAAOwB,oBAAoB,CAACR,QAAQ,CAACS,OAAO,IAAI,EAAE,CAAC;EACvD,CAAC;EAAA,OAAAvB,aAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AACD,MAAMoB,oBAAoB;EAAA,IAAAE,IAAA,GAAArB,iBAAA,CAAG,WAAOsB,OAAO,EAAK;IAC5C,OAAOA,OAAO,CAACC,GAAG,CAAC,CAAC;MAAEC,SAAS,EAAEC,EAAE,GAAG,EAAE;MAAEC,gBAAgB,GAAG,EAAE;MAAEC,gBAAgB;MAAEC,sBAAsB;MAAEC;IAA6B,CAAC,KAAK;MAC1I,IAAIC,UAAU;MACd,MAAMC,UAAU,GAAGL,gBAAgB,CAACM,MAAM,CAAC,CAACC,KAAK,EAAE;QAAEC,IAAI;QAAEC;MAAM,CAAC,KAAK;QACnE,IAAID,IAAI,IAAIC,KAAK,EAAE;UACf,IAAID,IAAI,KAAK,aAAa,EACtBJ,UAAU,GAAGK,KAAK;UACtBF,KAAK,CAACC,IAAI,CAAC,GAAGC,KAAK;QACvB;QACA,OAAOF,KAAK;MAChB,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,MAAMG,MAAM,GAAG;QACXX,EAAE;QACFY,IAAI,EAAEP,UAAU;QAChBC,UAAU;QACVO,UAAU,EAAEX,gBAAgB,GACtB,IAAIY,IAAI,CAACZ,gBAAgB,GAAG,IAAI,CAAC,GACjCa,SAAS;QACfC,gBAAgB,EAAEb,sBAAsB,GAClC,IAAIW,IAAI,CAACX,sBAAsB,GAAG,IAAI,CAAC,GACvCY,SAAS;QACfE,qBAAqB,EAAEb,2BAA2B,GAC5C,IAAIU,IAAI,CAACV,2BAA2B,GAAG,IAAI,CAAC,GAC5CW;MACV,CAAC;MACD,OAAOJ,MAAM;IACjB,CAAC,CAAC;EACN,CAAC;EAAA,gBA3BKjB,oBAAoBA,CAAAwB,EAAA;IAAA,OAAAtB,IAAA,CAAAvB,KAAA,OAAAC,SAAA;EAAA;AAAA,GA2BzB;AAED,SAASH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}