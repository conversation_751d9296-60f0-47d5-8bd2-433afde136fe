{"ast": null, "code": "import { __assign, __awaiter, __generator, __read, __spread, __values } from \"tslib\";\nimport { DescribeProjectVersionsCommand } from \"../commands/DescribeProjectVersionsCommand\";\nimport { WaiterState, createWaiter } from \"@aws-sdk/util-waiter\";\nvar checkState = function (client, input) {\n  return __awaiter(void 0, void 0, void 0, function () {\n    var result_1, returnComparator, allStringEq_5, _a, _b, element_4, returnComparator, _c, _d, anyStringEq_4, exception_1;\n    var e_1, _e, e_2, _f;\n    return __generator(this, function (_g) {\n      switch (_g.label) {\n        case 0:\n          _g.trys.push([0, 2,, 3]);\n          return [4 /*yield*/, client.send(new DescribeProjectVersionsCommand(input))];\n        case 1:\n          result_1 = _g.sent();\n          try {\n            returnComparator = function () {\n              var flat_1 = [].concat.apply([], __spread(result_1.ProjectVersionDescriptions));\n              var projection_3 = flat_1.map(function (element_2) {\n                return element_2.Status;\n              });\n              return projection_3;\n            };\n            allStringEq_5 = returnComparator().length > 0;\n            try {\n              for (_a = __values(returnComparator()), _b = _a.next(); !_b.done; _b = _a.next()) {\n                element_4 = _b.value;\n                allStringEq_5 = allStringEq_5 && element_4 == \"RUNNING\";\n              }\n            } catch (e_1_1) {\n              e_1 = {\n                error: e_1_1\n              };\n            } finally {\n              try {\n                if (_b && !_b.done && (_e = _a.return)) _e.call(_a);\n              } finally {\n                if (e_1) throw e_1.error;\n              }\n            }\n            if (allStringEq_5) {\n              return [2 /*return*/, {\n                state: WaiterState.SUCCESS\n              }];\n            }\n          } catch (e) {}\n          try {\n            returnComparator = function () {\n              var flat_1 = [].concat.apply([], __spread(result_1.ProjectVersionDescriptions));\n              var projection_3 = flat_1.map(function (element_2) {\n                return element_2.Status;\n              });\n              return projection_3;\n            };\n            try {\n              for (_c = __values(returnComparator()), _d = _c.next(); !_d.done; _d = _c.next()) {\n                anyStringEq_4 = _d.value;\n                if (anyStringEq_4 == \"FAILED\") {\n                  return [2 /*return*/, {\n                    state: WaiterState.FAILURE\n                  }];\n                }\n              }\n            } catch (e_2_1) {\n              e_2 = {\n                error: e_2_1\n              };\n            } finally {\n              try {\n                if (_d && !_d.done && (_f = _c.return)) _f.call(_c);\n              } finally {\n                if (e_2) throw e_2.error;\n              }\n            }\n          } catch (e) {}\n          return [3 /*break*/, 3];\n        case 2:\n          exception_1 = _g.sent();\n          return [3 /*break*/, 3];\n        case 3:\n          return [2 /*return*/, {\n            state: WaiterState.RETRY\n          }];\n      }\n    });\n  });\n};\n/**\n * Wait until the ProjectVersion is running.\n *  @param params : Waiter configuration options.\n *  @param input : the input to DescribeProjectVersionsCommand for polling.\n */\nexport var waitForProjectVersionRunning = function (params, input) {\n  return __awaiter(void 0, void 0, void 0, function () {\n    var serviceDefaults;\n    return __generator(this, function (_a) {\n      serviceDefaults = {\n        minDelay: 30,\n        maxDelay: 120\n      };\n      return [2 /*return*/, createWaiter(__assign(__assign({}, serviceDefaults), params), input, checkState)];\n    });\n  });\n};", "map": {"version": 3, "names": ["__assign", "__awaiter", "__generator", "__read", "__spread", "__values", "DescribeProjectVersionsCommand", "WaiterState", "<PERSON><PERSON><PERSON><PERSON>", "checkState", "client", "input", "result_1", "returnComparator", "allStringEq_5", "_a", "_b", "element_4", "_c", "_d", "anyStringEq_4", "exception_1", "e_1", "_e", "e_2", "_f", "_g", "label", "trys", "push", "send", "sent", "flat_1", "concat", "apply", "ProjectVersionDescriptions", "projection_3", "map", "element_2", "Status", "length", "next", "done", "value", "e_1_1", "error", "return", "call", "state", "SUCCESS", "e", "FAILURE", "e_2_1", "RETRY", "waitForProjectVersionRunning", "params", "serviceDefaults", "min<PERSON>elay", "max<PERSON><PERSON><PERSON>"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-rekognition/dist/es/waiters/waitForProjectVersionRunning.js"], "sourcesContent": ["import { __assign, __awaiter, __generator, __read, __spread, __values } from \"tslib\";\nimport { DescribeProjectVersionsCommand, } from \"../commands/DescribeProjectVersionsCommand\";\nimport { WaiterState, createWaiter } from \"@aws-sdk/util-waiter\";\nvar checkState = function (client, input) { return __awaiter(void 0, void 0, void 0, function () {\n    var result_1, returnComparator, allStringEq_5, _a, _b, element_4, returnComparator, _c, _d, anyStringEq_4, exception_1;\n    var e_1, _e, e_2, _f;\n    return __generator(this, function (_g) {\n        switch (_g.label) {\n            case 0:\n                _g.trys.push([0, 2, , 3]);\n                return [4 /*yield*/, client.send(new DescribeProjectVersionsCommand(input))];\n            case 1:\n                result_1 = _g.sent();\n                try {\n                    returnComparator = function () {\n                        var flat_1 = [].concat.apply([], __spread(result_1.ProjectVersionDescriptions));\n                        var projection_3 = flat_1.map(function (element_2) {\n                            return element_2.Status;\n                        });\n                        return projection_3;\n                    };\n                    allStringEq_5 = returnComparator().length > 0;\n                    try {\n                        for (_a = __values(returnComparator()), _b = _a.next(); !_b.done; _b = _a.next()) {\n                            element_4 = _b.value;\n                            allStringEq_5 = allStringEq_5 && element_4 == \"RUNNING\";\n                        }\n                    }\n                    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                    finally {\n                        try {\n                            if (_b && !_b.done && (_e = _a.return)) _e.call(_a);\n                        }\n                        finally { if (e_1) throw e_1.error; }\n                    }\n                    if (allStringEq_5) {\n                        return [2 /*return*/, { state: WaiterState.SUCCESS }];\n                    }\n                }\n                catch (e) { }\n                try {\n                    returnComparator = function () {\n                        var flat_1 = [].concat.apply([], __spread(result_1.ProjectVersionDescriptions));\n                        var projection_3 = flat_1.map(function (element_2) {\n                            return element_2.Status;\n                        });\n                        return projection_3;\n                    };\n                    try {\n                        for (_c = __values(returnComparator()), _d = _c.next(); !_d.done; _d = _c.next()) {\n                            anyStringEq_4 = _d.value;\n                            if (anyStringEq_4 == \"FAILED\") {\n                                return [2 /*return*/, { state: WaiterState.FAILURE }];\n                            }\n                        }\n                    }\n                    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n                    finally {\n                        try {\n                            if (_d && !_d.done && (_f = _c.return)) _f.call(_c);\n                        }\n                        finally { if (e_2) throw e_2.error; }\n                    }\n                }\n                catch (e) { }\n                return [3 /*break*/, 3];\n            case 2:\n                exception_1 = _g.sent();\n                return [3 /*break*/, 3];\n            case 3: return [2 /*return*/, { state: WaiterState.RETRY }];\n        }\n    });\n}); };\n/**\n * Wait until the ProjectVersion is running.\n *  @param params : Waiter configuration options.\n *  @param input : the input to DescribeProjectVersionsCommand for polling.\n */\nexport var waitForProjectVersionRunning = function (params, input) { return __awaiter(void 0, void 0, void 0, function () {\n    var serviceDefaults;\n    return __generator(this, function (_a) {\n        serviceDefaults = { minDelay: 30, maxDelay: 120 };\n        return [2 /*return*/, createWaiter(__assign(__assign({}, serviceDefaults), params), input, checkState)];\n    });\n}); };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,OAAO;AACpF,SAASC,8BAA8B,QAAS,4CAA4C;AAC5F,SAASC,WAAW,EAAEC,YAAY,QAAQ,sBAAsB;AAChE,IAAIC,UAAU,GAAG,SAAAA,CAAUC,MAAM,EAAEC,KAAK,EAAE;EAAE,OAAOV,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;IAC7F,IAAIW,QAAQ,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,EAAE,EAAEC,EAAE,EAAEC,SAAS,EAAEJ,gBAAgB,EAAEK,EAAE,EAAEC,EAAE,EAAEC,aAAa,EAAEC,WAAW;IACtH,IAAIC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE;IACpB,OAAOvB,WAAW,CAAC,IAAI,EAAE,UAAUwB,EAAE,EAAE;MACnC,QAAQA,EAAE,CAACC,KAAK;QACZ,KAAK,CAAC;UACFD,EAAE,CAACE,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAI,CAAC,CAAC,CAAC;UACzB,OAAO,CAAC,CAAC,CAAC,WAAWnB,MAAM,CAACoB,IAAI,CAAC,IAAIxB,8BAA8B,CAACK,KAAK,CAAC,CAAC,CAAC;QAChF,KAAK,CAAC;UACFC,QAAQ,GAAGc,EAAE,CAACK,IAAI,CAAC,CAAC;UACpB,IAAI;YACAlB,gBAAgB,GAAG,SAAAA,CAAA,EAAY;cAC3B,IAAImB,MAAM,GAAG,EAAE,CAACC,MAAM,CAACC,KAAK,CAAC,EAAE,EAAE9B,QAAQ,CAACQ,QAAQ,CAACuB,0BAA0B,CAAC,CAAC;cAC/E,IAAIC,YAAY,GAAGJ,MAAM,CAACK,GAAG,CAAC,UAAUC,SAAS,EAAE;gBAC/C,OAAOA,SAAS,CAACC,MAAM;cAC3B,CAAC,CAAC;cACF,OAAOH,YAAY;YACvB,CAAC;YACDtB,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC2B,MAAM,GAAG,CAAC;YAC7C,IAAI;cACA,KAAKzB,EAAE,GAAGV,QAAQ,CAACQ,gBAAgB,CAAC,CAAC,CAAC,EAAEG,EAAE,GAAGD,EAAE,CAAC0B,IAAI,CAAC,CAAC,EAAE,CAACzB,EAAE,CAAC0B,IAAI,EAAE1B,EAAE,GAAGD,EAAE,CAAC0B,IAAI,CAAC,CAAC,EAAE;gBAC9ExB,SAAS,GAAGD,EAAE,CAAC2B,KAAK;gBACpB7B,aAAa,GAAGA,aAAa,IAAIG,SAAS,IAAI,SAAS;cAC3D;YACJ,CAAC,CACD,OAAO2B,KAAK,EAAE;cAAEtB,GAAG,GAAG;gBAAEuB,KAAK,EAAED;cAAM,CAAC;YAAE,CAAC,SACjC;cACJ,IAAI;gBACA,IAAI5B,EAAE,IAAI,CAACA,EAAE,CAAC0B,IAAI,KAAKnB,EAAE,GAAGR,EAAE,CAAC+B,MAAM,CAAC,EAAEvB,EAAE,CAACwB,IAAI,CAAChC,EAAE,CAAC;cACvD,CAAC,SACO;gBAAE,IAAIO,GAAG,EAAE,MAAMA,GAAG,CAACuB,KAAK;cAAE;YACxC;YACA,IAAI/B,aAAa,EAAE;cACf,OAAO,CAAC,CAAC,CAAC,YAAY;gBAAEkC,KAAK,EAAEzC,WAAW,CAAC0C;cAAQ,CAAC,CAAC;YACzD;UACJ,CAAC,CACD,OAAOC,CAAC,EAAE,CAAE;UACZ,IAAI;YACArC,gBAAgB,GAAG,SAAAA,CAAA,EAAY;cAC3B,IAAImB,MAAM,GAAG,EAAE,CAACC,MAAM,CAACC,KAAK,CAAC,EAAE,EAAE9B,QAAQ,CAACQ,QAAQ,CAACuB,0BAA0B,CAAC,CAAC;cAC/E,IAAIC,YAAY,GAAGJ,MAAM,CAACK,GAAG,CAAC,UAAUC,SAAS,EAAE;gBAC/C,OAAOA,SAAS,CAACC,MAAM;cAC3B,CAAC,CAAC;cACF,OAAOH,YAAY;YACvB,CAAC;YACD,IAAI;cACA,KAAKlB,EAAE,GAAGb,QAAQ,CAACQ,gBAAgB,CAAC,CAAC,CAAC,EAAEM,EAAE,GAAGD,EAAE,CAACuB,IAAI,CAAC,CAAC,EAAE,CAACtB,EAAE,CAACuB,IAAI,EAAEvB,EAAE,GAAGD,EAAE,CAACuB,IAAI,CAAC,CAAC,EAAE;gBAC9ErB,aAAa,GAAGD,EAAE,CAACwB,KAAK;gBACxB,IAAIvB,aAAa,IAAI,QAAQ,EAAE;kBAC3B,OAAO,CAAC,CAAC,CAAC,YAAY;oBAAE4B,KAAK,EAAEzC,WAAW,CAAC4C;kBAAQ,CAAC,CAAC;gBACzD;cACJ;YACJ,CAAC,CACD,OAAOC,KAAK,EAAE;cAAE5B,GAAG,GAAG;gBAAEqB,KAAK,EAAEO;cAAM,CAAC;YAAE,CAAC,SACjC;cACJ,IAAI;gBACA,IAAIjC,EAAE,IAAI,CAACA,EAAE,CAACuB,IAAI,KAAKjB,EAAE,GAAGP,EAAE,CAAC4B,MAAM,CAAC,EAAErB,EAAE,CAACsB,IAAI,CAAC7B,EAAE,CAAC;cACvD,CAAC,SACO;gBAAE,IAAIM,GAAG,EAAE,MAAMA,GAAG,CAACqB,KAAK;cAAE;YACxC;UACJ,CAAC,CACD,OAAOK,CAAC,EAAE,CAAE;UACZ,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QAC3B,KAAK,CAAC;UACF7B,WAAW,GAAGK,EAAE,CAACK,IAAI,CAAC,CAAC;UACvB,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;QAC3B,KAAK,CAAC;UAAE,OAAO,CAAC,CAAC,CAAC,YAAY;YAAEiB,KAAK,EAAEzC,WAAW,CAAC8C;UAAM,CAAC,CAAC;MAC/D;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AAAE,CAAC;AACL;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,4BAA4B,GAAG,SAAAA,CAAUC,MAAM,EAAE5C,KAAK,EAAE;EAAE,OAAOV,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;IACtH,IAAIuD,eAAe;IACnB,OAAOtD,WAAW,CAAC,IAAI,EAAE,UAAUa,EAAE,EAAE;MACnCyC,eAAe,GAAG;QAAEC,QAAQ,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAC;MACjD,OAAO,CAAC,CAAC,CAAC,YAAYlD,YAAY,CAACR,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEwD,eAAe,CAAC,EAAED,MAAM,CAAC,EAAE5C,KAAK,EAAEF,UAAU,CAAC,CAAC;IAC3G,CAAC,CAAC;EACN,CAAC,CAAC;AAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}