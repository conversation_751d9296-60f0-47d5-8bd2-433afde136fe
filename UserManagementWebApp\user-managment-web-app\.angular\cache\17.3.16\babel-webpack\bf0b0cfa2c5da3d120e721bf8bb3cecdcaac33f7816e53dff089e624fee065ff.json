{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * All possible states a `BackgroundProcessManager` instance can be in.\n */\nvar BackgroundProcessManagerState;\n(function (BackgroundProcessManagerState) {\n  /**\n   * Accepting new jobs.\n   */\n  BackgroundProcessManagerState[\"Open\"] = \"Open\";\n  /**\n   * Not accepting new jobs. Waiting for submitted jobs to complete.\n   */\n  BackgroundProcessManagerState[\"Closing\"] = \"Closing\";\n  /**\n   * Not accepting new jobs. All submitted jobs are complete.\n   */\n  BackgroundProcessManagerState[\"Closed\"] = \"Closed\";\n})(BackgroundProcessManagerState || (BackgroundProcessManagerState = {}));\nexport { BackgroundProcessManagerState };", "map": {"version": 3, "names": ["BackgroundProcessManagerState"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/BackgroundProcessManager/types.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * All possible states a `BackgroundProcessManager` instance can be in.\n */\nvar BackgroundProcessManagerState;\n(function (BackgroundProcessManagerState) {\n    /**\n     * Accepting new jobs.\n     */\n    BackgroundProcessManagerState[\"Open\"] = \"Open\";\n    /**\n     * Not accepting new jobs. Waiting for submitted jobs to complete.\n     */\n    BackgroundProcessManagerState[\"Closing\"] = \"Closing\";\n    /**\n     * Not accepting new jobs. All submitted jobs are complete.\n     */\n    BackgroundProcessManagerState[\"Closed\"] = \"Closed\";\n})(BackgroundProcessManagerState || (BackgroundProcessManagerState = {}));\n\nexport { BackgroundProcessManagerState };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,IAAIA,6BAA6B;AACjC,CAAC,UAAUA,6BAA6B,EAAE;EACtC;AACJ;AACA;EACIA,6BAA6B,CAAC,MAAM,CAAC,GAAG,MAAM;EAC9C;AACJ;AACA;EACIA,6BAA6B,CAAC,SAAS,CAAC,GAAG,SAAS;EACpD;AACJ;AACA;EACIA,6BAA6B,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACtD,CAAC,EAAEA,6BAA6B,KAAKA,6BAA6B,GAAG,CAAC,CAAC,CAAC,CAAC;AAEzE,SAASA,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}