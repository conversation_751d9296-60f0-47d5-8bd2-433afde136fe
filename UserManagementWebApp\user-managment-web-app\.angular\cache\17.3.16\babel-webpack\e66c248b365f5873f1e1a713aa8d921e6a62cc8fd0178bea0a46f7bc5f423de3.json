{"ast": null, "code": "import { AmplifyErrorCode } from '@aws-amplify/core/internals/utils';\nimport { AuthError } from '../AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction assertServiceError(error) {\n  if (!error || error.name === 'Error' || error instanceof TypeError) {\n    throw new AuthError({\n      name: AmplifyErrorCode.Unknown,\n      message: 'An unknown error has occurred.',\n      underlyingError: error\n    });\n  }\n}\nexport { assertServiceError };", "map": {"version": 3, "names": ["AmplifyErrorCode", "<PERSON>th<PERSON><PERSON><PERSON>", "assertServiceError", "error", "name", "TypeError", "Unknown", "message", "underlyingError"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/errors/utils/assertServiceError.mjs"], "sourcesContent": ["import { AmplifyErrorCode } from '@aws-amplify/core/internals/utils';\nimport { AuthError } from '../AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction assertServiceError(error) {\n    if (!error ||\n        error.name === 'Error' ||\n        error instanceof TypeError) {\n        throw new AuthError({\n            name: AmplifyErrorCode.Unknown,\n            message: 'An unknown error has occurred.',\n            underlyingError: error,\n        });\n    }\n}\n\nexport { assertServiceError };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,SAAS,QAAQ,kBAAkB;;AAE5C;AACA;AACA,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EAC/B,IAAI,CAACA,KAAK,IACNA,KAAK,CAACC,IAAI,KAAK,OAAO,IACtBD,KAAK,YAAYE,SAAS,EAAE;IAC5B,MAAM,IAAIJ,SAAS,CAAC;MAChBG,IAAI,EAAEJ,gBAAgB,CAACM,OAAO;MAC9BC,OAAO,EAAE,gCAAgC;MACzCC,eAAe,EAAEL;IACrB,CAAC,CAAC;EACN;AACJ;AAEA,SAASD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}