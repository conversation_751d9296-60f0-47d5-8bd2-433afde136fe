{"ast": null, "code": "const tabs = {\n  backgroundColor: {\n    value: 'transparent'\n  },\n  borderColor: {\n    value: '{colors.border.secondary.value}'\n  },\n  borderStyle: {\n    value: 'solid'\n  },\n  borderWidth: {\n    value: '{borderWidths.medium.value}'\n  },\n  gap: {\n    value: '0'\n  },\n  item: {\n    backgroundColor: {\n      value: 'transparent'\n    },\n    borderColor: {\n      value: '{colors.border.secondary.value}'\n    },\n    borderStyle: {\n      value: 'solid'\n    },\n    borderWidth: {\n      value: '{borderWidths.medium.value}'\n    },\n    color: {\n      value: '{colors.font.secondary.value}'\n    },\n    fontSize: {\n      value: '{fontSizes.medium.value}'\n    },\n    fontWeight: {\n      value: '{fontWeights.bold.value}'\n    },\n    paddingVertical: {\n      value: '{space.small.value}'\n    },\n    paddingHorizontal: {\n      value: '{space.medium.value}'\n    },\n    textAlign: {\n      value: 'center'\n    },\n    transitionDuration: {\n      value: '{time.medium.value}'\n    },\n    _hover: {\n      backgroundColor: {\n        value: 'transparent'\n      },\n      borderColor: {\n        value: '{colors.border.focus.value}'\n      },\n      boxShadow: {\n        value: 'none'\n      },\n      color: {\n        value: '{colors.font.hover.value}'\n      }\n    },\n    _focus: {\n      backgroundColor: {\n        value: 'transparent'\n      },\n      borderColor: {\n        value: '{colors.border.focus.value}'\n      },\n      boxShadow: {\n        value: {\n          offsetX: '0px',\n          offsetY: '0px',\n          blurRadius: '0px',\n          spreadRadius: '{borderWidths.medium}',\n          color: '{colors.border.focus.value}'\n        }\n      },\n      color: {\n        value: '{colors.font.focus.value}'\n      }\n    },\n    _active: {\n      backgroundColor: {\n        value: 'transparent'\n      },\n      borderColor: {\n        value: '{colors.font.interactive.value}'\n      },\n      boxShadow: {\n        value: 'none'\n      },\n      color: {\n        value: '{colors.font.interactive.value}'\n      }\n    },\n    _disabled: {\n      backgroundColor: {\n        value: 'transparent'\n      },\n      borderColor: {\n        value: '{colors.border.tertiary.value}'\n      },\n      boxShadow: {\n        value: 'none'\n      },\n      color: {\n        value: '{colors.font.disabled.value}'\n      }\n    }\n  },\n  panel: {\n    backgroundColor: {\n      value: 'transparent'\n    },\n    paddingInline: {\n      value: '0'\n    },\n    paddingBlock: {\n      value: '{space.small.value}'\n    }\n  }\n};\nexport { tabs };", "map": {"version": 3, "names": ["tabs", "backgroundColor", "value", "borderColor", "borderStyle", "borderWidth", "gap", "item", "color", "fontSize", "fontWeight", "paddingVertical", "paddingHorizontal", "textAlign", "transitionDuration", "_hover", "boxShadow", "_focus", "offsetX", "offsetY", "blurRadius", "spreadRadius", "_active", "_disabled", "panel", "paddingInline", "paddingBlock"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/tabs.mjs"], "sourcesContent": ["const tabs = {\n    backgroundColor: { value: 'transparent' },\n    borderColor: { value: '{colors.border.secondary.value}' },\n    borderStyle: { value: 'solid' },\n    borderWidth: { value: '{borderWidths.medium.value}' },\n    gap: { value: '0' },\n    item: {\n        backgroundColor: { value: 'transparent' },\n        borderColor: { value: '{colors.border.secondary.value}' },\n        borderStyle: { value: 'solid' },\n        borderWidth: { value: '{borderWidths.medium.value}' },\n        color: { value: '{colors.font.secondary.value}' },\n        fontSize: { value: '{fontSizes.medium.value}' },\n        fontWeight: { value: '{fontWeights.bold.value}' },\n        paddingVertical: { value: '{space.small.value}' },\n        paddingHorizontal: { value: '{space.medium.value}' },\n        textAlign: { value: 'center' },\n        transitionDuration: { value: '{time.medium.value}' },\n        _hover: {\n            backgroundColor: { value: 'transparent' },\n            borderColor: { value: '{colors.border.focus.value}' },\n            boxShadow: { value: 'none' },\n            color: { value: '{colors.font.hover.value}' },\n        },\n        _focus: {\n            backgroundColor: { value: 'transparent' },\n            borderColor: { value: '{colors.border.focus.value}' },\n            boxShadow: {\n                value: {\n                    offsetX: '0px',\n                    offsetY: '0px',\n                    blurRadius: '0px',\n                    spreadRadius: '{borderWidths.medium}',\n                    color: '{colors.border.focus.value}',\n                },\n            },\n            color: { value: '{colors.font.focus.value}' },\n        },\n        _active: {\n            backgroundColor: { value: 'transparent' },\n            borderColor: { value: '{colors.font.interactive.value}' },\n            boxShadow: { value: 'none' },\n            color: { value: '{colors.font.interactive.value}' },\n        },\n        _disabled: {\n            backgroundColor: { value: 'transparent' },\n            borderColor: { value: '{colors.border.tertiary.value}' },\n            boxShadow: { value: 'none' },\n            color: { value: '{colors.font.disabled.value}' },\n        },\n    },\n    panel: {\n        backgroundColor: { value: 'transparent' },\n        paddingInline: { value: '0' },\n        paddingBlock: { value: '{space.small.value}' },\n    },\n};\n\nexport { tabs };\n"], "mappings": "AAAA,MAAMA,IAAI,GAAG;EACTC,eAAe,EAAE;IAAEC,KAAK,EAAE;EAAc,CAAC;EACzCC,WAAW,EAAE;IAAED,KAAK,EAAE;EAAkC,CAAC;EACzDE,WAAW,EAAE;IAAEF,KAAK,EAAE;EAAQ,CAAC;EAC/BG,WAAW,EAAE;IAAEH,KAAK,EAAE;EAA8B,CAAC;EACrDI,GAAG,EAAE;IAAEJ,KAAK,EAAE;EAAI,CAAC;EACnBK,IAAI,EAAE;IACFN,eAAe,EAAE;MAAEC,KAAK,EAAE;IAAc,CAAC;IACzCC,WAAW,EAAE;MAAED,KAAK,EAAE;IAAkC,CAAC;IACzDE,WAAW,EAAE;MAAEF,KAAK,EAAE;IAAQ,CAAC;IAC/BG,WAAW,EAAE;MAAEH,KAAK,EAAE;IAA8B,CAAC;IACrDM,KAAK,EAAE;MAAEN,KAAK,EAAE;IAAgC,CAAC;IACjDO,QAAQ,EAAE;MAAEP,KAAK,EAAE;IAA2B,CAAC;IAC/CQ,UAAU,EAAE;MAAER,KAAK,EAAE;IAA2B,CAAC;IACjDS,eAAe,EAAE;MAAET,KAAK,EAAE;IAAsB,CAAC;IACjDU,iBAAiB,EAAE;MAAEV,KAAK,EAAE;IAAuB,CAAC;IACpDW,SAAS,EAAE;MAAEX,KAAK,EAAE;IAAS,CAAC;IAC9BY,kBAAkB,EAAE;MAAEZ,KAAK,EAAE;IAAsB,CAAC;IACpDa,MAAM,EAAE;MACJd,eAAe,EAAE;QAAEC,KAAK,EAAE;MAAc,CAAC;MACzCC,WAAW,EAAE;QAAED,KAAK,EAAE;MAA8B,CAAC;MACrDc,SAAS,EAAE;QAAEd,KAAK,EAAE;MAAO,CAAC;MAC5BM,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA4B;IAChD,CAAC;IACDe,MAAM,EAAE;MACJhB,eAAe,EAAE;QAAEC,KAAK,EAAE;MAAc,CAAC;MACzCC,WAAW,EAAE;QAAED,KAAK,EAAE;MAA8B,CAAC;MACrDc,SAAS,EAAE;QACPd,KAAK,EAAE;UACHgB,OAAO,EAAE,KAAK;UACdC,OAAO,EAAE,KAAK;UACdC,UAAU,EAAE,KAAK;UACjBC,YAAY,EAAE,uBAAuB;UACrCb,KAAK,EAAE;QACX;MACJ,CAAC;MACDA,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA4B;IAChD,CAAC;IACDoB,OAAO,EAAE;MACLrB,eAAe,EAAE;QAAEC,KAAK,EAAE;MAAc,CAAC;MACzCC,WAAW,EAAE;QAAED,KAAK,EAAE;MAAkC,CAAC;MACzDc,SAAS,EAAE;QAAEd,KAAK,EAAE;MAAO,CAAC;MAC5BM,KAAK,EAAE;QAAEN,KAAK,EAAE;MAAkC;IACtD,CAAC;IACDqB,SAAS,EAAE;MACPtB,eAAe,EAAE;QAAEC,KAAK,EAAE;MAAc,CAAC;MACzCC,WAAW,EAAE;QAAED,KAAK,EAAE;MAAiC,CAAC;MACxDc,SAAS,EAAE;QAAEd,KAAK,EAAE;MAAO,CAAC;MAC5BM,KAAK,EAAE;QAAEN,KAAK,EAAE;MAA+B;IACnD;EACJ,CAAC;EACDsB,KAAK,EAAE;IACHvB,eAAe,EAAE;MAAEC,KAAK,EAAE;IAAc,CAAC;IACzCuB,aAAa,EAAE;MAAEvB,KAAK,EAAE;IAAI,CAAC;IAC7BwB,YAAY,EAAE;MAAExB,KAAK,EAAE;IAAsB;EACjD;AACJ,CAAC;AAED,SAASF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}