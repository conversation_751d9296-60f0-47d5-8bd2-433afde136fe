{"ast": null, "code": "const ruDict = {\n  'Account recovery requires verified contact information': 'Восстановление учетной записи требует проверки контактной информации',\n  'Authenticator App (TOTP)': 'Приложение для аутентификации (TOTP)',\n  'Back to Sign In': 'Назад, чтобы войти',\n  'Change Password': 'изменять пароль',\n  Changing: 'Изменение',\n  Code: 'Код',\n  'Confirm Email Code': 'Подтвердите код электронной почты',\n  'Confirm Password': 'Подтверждение пароля',\n  'Confirm Sign Up': 'Подтверждение зарегистрироваться',\n  'Confirm SMS Code': 'Подтверждение CMC-Код',\n  'Confirm TOTP Code': 'Подтверждение TOTP-Код',\n  Confirm: 'Подтверждать',\n  'Confirmation Code': 'код подтверждения',\n  Confirming: 'подтверждение',\n  'Create a new account': 'Создавать новую учетную запись',\n  'Create Account': 'Создать учетную запись',\n  'Creating Account': 'создание учетная запись',\n  'Dismiss alert': 'Закрыть оповещение',\n  Email: 'электронная почта',\n  'Email Message': 'Сообщение по электронной почте',\n  'Enter your code': 'ввести ваш Код',\n  'Enter your Email': 'ввести ваш электронная почта',\n  'Enter your phone number': 'ввести ваш номер телефона',\n  'Enter your username': 'ввести ваш имя пользователя',\n  'Forgot your password?': 'Забыли ваш пароль?',\n  'Hide password': 'Скрывать пароль',\n  'It may take a minute to arrive': 'Доставка может занять некоторое время',\n  Loading: 'Загрузка',\n  'Multi-Factor Authentication': 'Многофакторная аутентификация',\n  'Multi-Factor Authentication Setup': 'Настройка многофакторной аутентификации',\n  'New password': 'Новый пароль',\n  or: 'или',\n  Password: 'Пароль',\n  'Phone Number': 'Номер телефона',\n  'Resend Code': 'Отправь еще раз Код',\n  'Reset your password': 'сброс ваш пароль',\n  'Reset your Password': 'сброс ваш Пароль',\n  'Select MFA Type': 'Выберите тип МФА',\n  'Send code': 'Отправлять Код',\n  'Send Code': 'Отправлять Код',\n  Sending: 'отправка',\n  'Setup Email': 'Настроить электронную почту',\n  'Setup TOTP': 'Настраивать TOTP',\n  'Show password': 'Показывать пароль',\n  'Sign in to your account': 'знак в свой аккаунт',\n  'Sign In with Amazon': 'знак в с Amazon',\n  'Sign In with Apple': 'знак в с Apple',\n  'Sign In with Facebook': 'знак в с Facebook',\n  'Sign In with Google': 'знак в с Google',\n  'Sign in': 'знак в',\n  'Sign In': 'знак в',\n  'Signing in': 'подписание в',\n  Skip: 'Пропускать',\n  Submit: 'Представлять на рассмотрение',\n  Submitting: 'Представив',\n  'Text Message (SMS)': 'Текстовое сообщение (SMS)',\n  Username: 'Имя пользователя',\n  'Verify Contact': 'Проверить контакт',\n  Verify: 'Проверить',\n  'We Emailed You': 'Мы отправили вам электронное письмо',\n  'We Sent A Code': 'Мы отправили код',\n  'We Texted You': 'Мы отправили вам текстовое сообщение',\n  'Your code is on the way. To log in, enter the code we emailed to': 'Ваш код отправлен. Чтобы войти в систему, введите код, который мы отправили по электронной почте',\n  'Your code is on the way. To log in, enter the code we sent you': 'Ваш код отправлен. Чтобы войти в систему, введите код, который мы послали вам',\n  'Your code is on the way. To log in, enter the code we texted to': 'Ваш код отправлен. Чтобы войти в систему, введите код, который мы отправили текстовым сообщением'\n};\nexport { ruDict };", "map": {"version": 3, "names": ["ruDict", "Changing", "Code", "Confirm", "Confirming", "Email", "Loading", "or", "Password", "Sending", "<PERSON><PERSON>", "Submit", "Submitting", "Username", "Verify"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/i18n/dictionaries/authenticator/ru.mjs"], "sourcesContent": ["const ruDict = {\n    'Account recovery requires verified contact information': 'Восстановление учетной записи требует проверки контактной информации',\n    'Authenticator App (TOTP)': 'Приложение для аутентификации (TOTP)',\n    'Back to Sign In': 'Назад, чтобы войти',\n    'Change Password': 'изменять пароль',\n    Changing: 'Изменение',\n    Code: 'Код',\n    'Confirm Email Code': 'Подтвердите код электронной почты',\n    'Confirm Password': 'Подтверждение пароля',\n    'Confirm Sign Up': 'Подтверждение зарегистрироваться',\n    'Confirm SMS Code': 'Подтверждение CMC-Код',\n    'Confirm TOTP Code': 'Подтверждение TOTP-Код',\n    Confirm: 'Подтверждать',\n    'Confirmation Code': 'код подтверждения',\n    Confirming: 'подтверждение',\n    'Create a new account': 'Создавать новую учетную запись',\n    'Create Account': 'Создать учетную запись',\n    'Creating Account': 'создание учетная запись',\n    'Dismiss alert': 'Закрыть оповещение',\n    Email: 'электронная почта',\n    'Email Message': 'Сообщение по электронной почте',\n    'Enter your code': 'ввести ваш Код',\n    'Enter your Email': 'ввести ваш электронная почта',\n    'Enter your phone number': 'ввести ваш номер телефона',\n    'Enter your username': 'ввести ваш имя пользователя',\n    'Forgot your password?': 'Забыли ваш пароль?',\n    'Hide password': 'Скрывать пароль',\n    'It may take a minute to arrive': 'Доставка может занять некоторое время',\n    Loading: 'Загрузка',\n    'Multi-Factor Authentication': 'Многофакторная аутентификация',\n    'Multi-Factor Authentication Setup': 'Настройка многофакторной аутентификации',\n    'New password': 'Новый пароль',\n    or: 'или',\n    Password: 'Пароль',\n    'Phone Number': 'Номер телефона',\n    'Resend Code': 'Отправь еще раз Код',\n    'Reset your password': 'сброс ваш пароль',\n    'Reset your Password': 'сброс ваш Пароль',\n    'Select MFA Type': 'Выберите тип МФА',\n    'Send code': 'Отправлять Код',\n    'Send Code': 'Отправлять Код',\n    Sending: 'отправка',\n    'Setup Email': 'Настроить электронную почту',\n    'Setup TOTP': 'Настраивать TOTP',\n    'Show password': 'Показывать пароль',\n    'Sign in to your account': 'знак в свой аккаунт',\n    'Sign In with Amazon': 'знак в с Amazon',\n    'Sign In with Apple': 'знак в с Apple',\n    'Sign In with Facebook': 'знак в с Facebook',\n    'Sign In with Google': 'знак в с Google',\n    'Sign in': 'знак в',\n    'Sign In': 'знак в',\n    'Signing in': 'подписание в',\n    Skip: 'Пропускать',\n    Submit: 'Представлять на рассмотрение',\n    Submitting: 'Представив',\n    'Text Message (SMS)': 'Текстовое сообщение (SMS)',\n    Username: 'Имя пользователя',\n    'Verify Contact': 'Проверить контакт',\n    Verify: 'Проверить',\n    'We Emailed You': 'Мы отправили вам электронное письмо',\n    'We Sent A Code': 'Мы отправили код',\n    'We Texted You': 'Мы отправили вам текстовое сообщение',\n    'Your code is on the way. To log in, enter the code we emailed to': 'Ваш код отправлен. Чтобы войти в систему, введите код, который мы отправили по электронной почте',\n    'Your code is on the way. To log in, enter the code we sent you': 'Ваш код отправлен. Чтобы войти в систему, введите код, который мы послали вам',\n    'Your code is on the way. To log in, enter the code we texted to': 'Ваш код отправлен. Чтобы войти в систему, введите код, который мы отправили текстовым сообщением',\n};\n\nexport { ruDict };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACX,wDAAwD,EAAE,sEAAsE;EAChI,0BAA0B,EAAE,sCAAsC;EAClE,iBAAiB,EAAE,oBAAoB;EACvC,iBAAiB,EAAE,iBAAiB;EACpCC,QAAQ,EAAE,WAAW;EACrBC,IAAI,EAAE,KAAK;EACX,oBAAoB,EAAE,mCAAmC;EACzD,kBAAkB,EAAE,sBAAsB;EAC1C,iBAAiB,EAAE,kCAAkC;EACrD,kBAAkB,EAAE,uBAAuB;EAC3C,mBAAmB,EAAE,wBAAwB;EAC7CC,OAAO,EAAE,cAAc;EACvB,mBAAmB,EAAE,mBAAmB;EACxCC,UAAU,EAAE,eAAe;EAC3B,sBAAsB,EAAE,gCAAgC;EACxD,gBAAgB,EAAE,wBAAwB;EAC1C,kBAAkB,EAAE,yBAAyB;EAC7C,eAAe,EAAE,oBAAoB;EACrCC,KAAK,EAAE,mBAAmB;EAC1B,eAAe,EAAE,gCAAgC;EACjD,iBAAiB,EAAE,gBAAgB;EACnC,kBAAkB,EAAE,8BAA8B;EAClD,yBAAyB,EAAE,2BAA2B;EACtD,qBAAqB,EAAE,6BAA6B;EACpD,uBAAuB,EAAE,oBAAoB;EAC7C,eAAe,EAAE,iBAAiB;EAClC,gCAAgC,EAAE,uCAAuC;EACzEC,OAAO,EAAE,UAAU;EACnB,6BAA6B,EAAE,+BAA+B;EAC9D,mCAAmC,EAAE,yCAAyC;EAC9E,cAAc,EAAE,cAAc;EAC9BC,EAAE,EAAE,KAAK;EACTC,QAAQ,EAAE,QAAQ;EAClB,cAAc,EAAE,gBAAgB;EAChC,aAAa,EAAE,qBAAqB;EACpC,qBAAqB,EAAE,kBAAkB;EACzC,qBAAqB,EAAE,kBAAkB;EACzC,iBAAiB,EAAE,kBAAkB;EACrC,WAAW,EAAE,gBAAgB;EAC7B,WAAW,EAAE,gBAAgB;EAC7BC,OAAO,EAAE,UAAU;EACnB,aAAa,EAAE,6BAA6B;EAC5C,YAAY,EAAE,kBAAkB;EAChC,eAAe,EAAE,mBAAmB;EACpC,yBAAyB,EAAE,qBAAqB;EAChD,qBAAqB,EAAE,iBAAiB;EACxC,oBAAoB,EAAE,gBAAgB;EACtC,uBAAuB,EAAE,mBAAmB;EAC5C,qBAAqB,EAAE,iBAAiB;EACxC,SAAS,EAAE,QAAQ;EACnB,SAAS,EAAE,QAAQ;EACnB,YAAY,EAAE,cAAc;EAC5BC,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,8BAA8B;EACtCC,UAAU,EAAE,YAAY;EACxB,oBAAoB,EAAE,2BAA2B;EACjDC,QAAQ,EAAE,kBAAkB;EAC5B,gBAAgB,EAAE,mBAAmB;EACrCC,MAAM,EAAE,WAAW;EACnB,gBAAgB,EAAE,qCAAqC;EACvD,gBAAgB,EAAE,kBAAkB;EACpC,eAAe,EAAE,sCAAsC;EACvD,kEAAkE,EAAE,kGAAkG;EACtK,gEAAgE,EAAE,+EAA+E;EACjJ,iEAAiE,EAAE;AACvE,CAAC;AAED,SAASd,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}