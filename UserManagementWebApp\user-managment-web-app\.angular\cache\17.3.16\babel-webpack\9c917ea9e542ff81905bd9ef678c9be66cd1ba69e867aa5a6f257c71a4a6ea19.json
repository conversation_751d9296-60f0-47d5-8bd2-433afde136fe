{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '@aws-amplify/core/internals/utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createRespondToAuthChallengeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createRespondToAuthChallengeClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertDeviceMetadata } from './types.mjs';\nimport { getAuthenticationHelper } from './srp/getAuthenticationHelper.mjs';\nimport './srp/constants.mjs';\nimport { getNowString } from './srp/getNowString.mjs';\nimport { getSignatureString } from './srp/getSignatureString.mjs';\nimport BigInteger from './srp/BigInteger/BigInteger.mjs';\nimport { getUserContextData } from './userContextData.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nfunction handleDeviceSRPAuth(_x) {\n  return _handleDeviceSRPAuth.apply(this, arguments);\n}\nfunction _handleDeviceSRPAuth() {\n  _handleDeviceSRPAuth = _asyncToGenerator(function* ({\n    username,\n    config,\n    clientMetadata,\n    session,\n    tokenOrchestrator\n  }) {\n    const {\n      userPoolId,\n      userPoolEndpoint\n    } = config;\n    const clientId = config.userPoolClientId;\n    const deviceMetadata = yield tokenOrchestrator?.getDeviceMetadata(username);\n    assertDeviceMetadata(deviceMetadata);\n    const authenticationHelper = yield getAuthenticationHelper(deviceMetadata.deviceGroupKey);\n    const challengeResponses = {\n      USERNAME: username,\n      SRP_A: authenticationHelper.A.toString(16),\n      DEVICE_KEY: deviceMetadata.deviceKey\n    };\n    const jsonReqResponseChallenge = {\n      ChallengeName: 'DEVICE_SRP_AUTH',\n      ClientId: clientId,\n      ChallengeResponses: challengeResponses,\n      ClientMetadata: clientMetadata,\n      Session: session\n    };\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const {\n      ChallengeParameters: respondedChallengeParameters,\n      Session\n    } = yield respondToAuthChallenge({\n      region: getRegionFromUserPoolId(userPoolId)\n    }, jsonReqResponseChallenge);\n    return handleDevicePasswordVerifier(username, respondedChallengeParameters, clientMetadata, Session, authenticationHelper, config, tokenOrchestrator);\n  });\n  return _handleDeviceSRPAuth.apply(this, arguments);\n}\nfunction handleDevicePasswordVerifier(_x2, _x3, _x4, _x5, _x6, _x7, _x8) {\n  return _handleDevicePasswordVerifier.apply(this, arguments);\n}\nfunction _handleDevicePasswordVerifier() {\n  _handleDevicePasswordVerifier = _asyncToGenerator(function* (username, challengeParameters, clientMetadata, session, authenticationHelper, {\n    userPoolId,\n    userPoolClientId,\n    userPoolEndpoint\n  }, tokenOrchestrator) {\n    const deviceMetadata = yield tokenOrchestrator?.getDeviceMetadata(username);\n    assertDeviceMetadata(deviceMetadata);\n    const serverBValue = new BigInteger(challengeParameters?.SRP_B, 16);\n    const salt = new BigInteger(challengeParameters?.SALT, 16);\n    const {\n      deviceKey\n    } = deviceMetadata;\n    const {\n      deviceGroupKey\n    } = deviceMetadata;\n    const hkdf = yield authenticationHelper.getPasswordAuthenticationKey({\n      username: deviceMetadata.deviceKey,\n      password: deviceMetadata.randomPassword,\n      serverBValue,\n      salt\n    });\n    const dateNow = getNowString();\n    const challengeResponses = {\n      USERNAME: challengeParameters?.USERNAME ?? username,\n      PASSWORD_CLAIM_SECRET_BLOCK: challengeParameters?.SECRET_BLOCK,\n      TIMESTAMP: dateNow,\n      PASSWORD_CLAIM_SIGNATURE: getSignatureString({\n        username: deviceKey,\n        userPoolName: deviceGroupKey,\n        challengeParameters,\n        dateNow,\n        hkdf\n      }),\n      DEVICE_KEY: deviceKey\n    };\n    const UserContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const jsonReqResponseChallenge = {\n      ChallengeName: 'DEVICE_PASSWORD_VERIFIER',\n      ClientId: userPoolClientId,\n      ChallengeResponses: challengeResponses,\n      Session: session,\n      ClientMetadata: clientMetadata,\n      UserContextData\n    };\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    return respondToAuthChallenge({\n      region: getRegionFromUserPoolId(userPoolId)\n    }, jsonReqResponseChallenge);\n  });\n  return _handleDevicePasswordVerifier.apply(this, arguments);\n}\nexport { handleDeviceSRPAuth };", "map": {"version": 3, "names": ["createRespondToAuthChallengeClient", "createCognitoUserPoolEndpointResolver", "getRegionFromUserPoolId", "assertDeviceMetadata", "getAuthenticationHelper", "getNowString", "getSignatureString", "BigInteger", "getUserContextData", "handleDeviceSRPAuth", "_x", "_handleDeviceSRPAuth", "apply", "arguments", "_asyncToGenerator", "username", "config", "clientMetadata", "session", "tokenOrchestrator", "userPoolId", "userPoolEndpoint", "clientId", "userPoolClientId", "deviceMetadata", "getDeviceMetadata", "authenticationHelper", "deviceGroupKey", "challengeResponses", "USERNAME", "SRP_A", "A", "toString", "DEVICE_KEY", "deviceKey", "jsonReqResponseChallenge", "ChallengeName", "ClientId", "ChallengeResponses", "ClientMetadata", "Session", "respondToAuthChallenge", "endpointResolver", "endpointOverride", "ChallengeParameters", "respondedChallengeParameters", "region", "handleDevicePasswordVerifier", "_x2", "_x3", "_x4", "_x5", "_x6", "_x7", "_x8", "_handleDevicePasswordVerifier", "challengeParameters", "serverBValue", "SRP_B", "salt", "SALT", "hkdf", "getPasswordAuthenticationKey", "password", "randomPassword", "dateNow", "PASSWORD_CLAIM_SECRET_BLOCK", "SECRET_BLOCK", "TIMESTAMP", "PASSWORD_CLAIM_SIGNATURE", "userPoolName", "UserContextData"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/handleDeviceSRPAuth.mjs"], "sourcesContent": ["import '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '@aws-amplify/core/internals/utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../types/errors.mjs';\nimport { createRespondToAuthChallengeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createRespondToAuthChallengeClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertDeviceMetadata } from './types.mjs';\nimport { getAuthenticationHelper } from './srp/getAuthenticationHelper.mjs';\nimport './srp/constants.mjs';\nimport { getNowString } from './srp/getNowString.mjs';\nimport { getSignatureString } from './srp/getSignatureString.mjs';\nimport BigInteger from './srp/BigInteger/BigInteger.mjs';\nimport { getUserContextData } from './userContextData.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nasync function handleDeviceSRPAuth({ username, config, clientMetadata, session, tokenOrchestrator, }) {\n    const { userPoolId, userPoolEndpoint } = config;\n    const clientId = config.userPoolClientId;\n    const deviceMetadata = await tokenOrchestrator?.getDeviceMetadata(username);\n    assertDeviceMetadata(deviceMetadata);\n    const authenticationHelper = await getAuthenticationHelper(deviceMetadata.deviceGroupKey);\n    const challengeResponses = {\n        USERNAME: username,\n        SRP_A: authenticationHelper.A.toString(16),\n        DEVICE_KEY: deviceMetadata.deviceKey,\n    };\n    const jsonReqResponseChallenge = {\n        ChallengeName: 'DEVICE_SRP_AUTH',\n        ClientId: clientId,\n        ChallengeResponses: challengeResponses,\n        ClientMetadata: clientMetadata,\n        Session: session,\n    };\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const { ChallengeParameters: respondedChallengeParameters, Session } = await respondToAuthChallenge({ region: getRegionFromUserPoolId(userPoolId) }, jsonReqResponseChallenge);\n    return handleDevicePasswordVerifier(username, respondedChallengeParameters, clientMetadata, Session, authenticationHelper, config, tokenOrchestrator);\n}\nasync function handleDevicePasswordVerifier(username, challengeParameters, clientMetadata, session, authenticationHelper, { userPoolId, userPoolClientId, userPoolEndpoint }, tokenOrchestrator) {\n    const deviceMetadata = await tokenOrchestrator?.getDeviceMetadata(username);\n    assertDeviceMetadata(deviceMetadata);\n    const serverBValue = new BigInteger(challengeParameters?.SRP_B, 16);\n    const salt = new BigInteger(challengeParameters?.SALT, 16);\n    const { deviceKey } = deviceMetadata;\n    const { deviceGroupKey } = deviceMetadata;\n    const hkdf = await authenticationHelper.getPasswordAuthenticationKey({\n        username: deviceMetadata.deviceKey,\n        password: deviceMetadata.randomPassword,\n        serverBValue,\n        salt,\n    });\n    const dateNow = getNowString();\n    const challengeResponses = {\n        USERNAME: challengeParameters?.USERNAME ?? username,\n        PASSWORD_CLAIM_SECRET_BLOCK: challengeParameters?.SECRET_BLOCK,\n        TIMESTAMP: dateNow,\n        PASSWORD_CLAIM_SIGNATURE: getSignatureString({\n            username: deviceKey,\n            userPoolName: deviceGroupKey,\n            challengeParameters,\n            dateNow,\n            hkdf,\n        }),\n        DEVICE_KEY: deviceKey,\n    };\n    const UserContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const jsonReqResponseChallenge = {\n        ChallengeName: 'DEVICE_PASSWORD_VERIFIER',\n        ClientId: userPoolClientId,\n        ChallengeResponses: challengeResponses,\n        Session: session,\n        ClientMetadata: clientMetadata,\n        UserContextData,\n    };\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    return respondToAuthChallenge({ region: getRegionFromUserPoolId(userPoolId) }, jsonReqResponseChallenge);\n}\n\nexport { handleDeviceSRPAuth };\n"], "mappings": ";AAAA,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,mCAAmC;AAC1C,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,sCAAsC;AAC7C,OAAO,qBAAqB;AAC5B,SAASA,kCAAkC,QAAQ,6GAA6G;AAChK,SAASC,qCAAqC,QAAQ,wDAAwD;AAC9G,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,oBAAoB,QAAQ,aAAa;AAClD,SAASC,uBAAuB,QAAQ,mCAAmC;AAC3E,OAAO,qBAAqB;AAC5B,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,OAAOC,UAAU,MAAM,iCAAiC;AACxD,SAASC,kBAAkB,QAAQ,uBAAuB;;AAE1D;AACA;AAAA,SACeC,mBAAmBA,CAAAC,EAAA;EAAA,OAAAC,oBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,qBAAA;EAAAA,oBAAA,GAAAG,iBAAA,CAAlC,WAAmC;IAAEC,QAAQ;IAAEC,MAAM;IAAEC,cAAc;IAAEC,OAAO;IAAEC;EAAmB,CAAC,EAAE;IAClG,MAAM;MAAEC,UAAU;MAAEC;IAAiB,CAAC,GAAGL,MAAM;IAC/C,MAAMM,QAAQ,GAAGN,MAAM,CAACO,gBAAgB;IACxC,MAAMC,cAAc,SAASL,iBAAiB,EAAEM,iBAAiB,CAACV,QAAQ,CAAC;IAC3EZ,oBAAoB,CAACqB,cAAc,CAAC;IACpC,MAAME,oBAAoB,SAAStB,uBAAuB,CAACoB,cAAc,CAACG,cAAc,CAAC;IACzF,MAAMC,kBAAkB,GAAG;MACvBC,QAAQ,EAAEd,QAAQ;MAClBe,KAAK,EAAEJ,oBAAoB,CAACK,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;MAC1CC,UAAU,EAAET,cAAc,CAACU;IAC/B,CAAC;IACD,MAAMC,wBAAwB,GAAG;MAC7BC,aAAa,EAAE,iBAAiB;MAChCC,QAAQ,EAAEf,QAAQ;MAClBgB,kBAAkB,EAAEV,kBAAkB;MACtCW,cAAc,EAAEtB,cAAc;MAC9BuB,OAAO,EAAEtB;IACb,CAAC;IACD,MAAMuB,sBAAsB,GAAGzC,kCAAkC,CAAC;MAC9D0C,gBAAgB,EAAEzC,qCAAqC,CAAC;QACpD0C,gBAAgB,EAAEtB;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAM;MAAEuB,mBAAmB,EAAEC,4BAA4B;MAAEL;IAAQ,CAAC,SAASC,sBAAsB,CAAC;MAAEK,MAAM,EAAE5C,uBAAuB,CAACkB,UAAU;IAAE,CAAC,EAAEe,wBAAwB,CAAC;IAC9K,OAAOY,4BAA4B,CAAChC,QAAQ,EAAE8B,4BAA4B,EAAE5B,cAAc,EAAEuB,OAAO,EAAEd,oBAAoB,EAAEV,MAAM,EAAEG,iBAAiB,CAAC;EACzJ,CAAC;EAAA,OAAAR,oBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SACckC,4BAA4BA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,6BAAA,CAAA3C,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA0C,8BAAA;EAAAA,6BAAA,GAAAzC,iBAAA,CAA3C,WAA4CC,QAAQ,EAAEyC,mBAAmB,EAAEvC,cAAc,EAAEC,OAAO,EAAEQ,oBAAoB,EAAE;IAAEN,UAAU;IAAEG,gBAAgB;IAAEF;EAAiB,CAAC,EAAEF,iBAAiB,EAAE;IAC7L,MAAMK,cAAc,SAASL,iBAAiB,EAAEM,iBAAiB,CAACV,QAAQ,CAAC;IAC3EZ,oBAAoB,CAACqB,cAAc,CAAC;IACpC,MAAMiC,YAAY,GAAG,IAAIlD,UAAU,CAACiD,mBAAmB,EAAEE,KAAK,EAAE,EAAE,CAAC;IACnE,MAAMC,IAAI,GAAG,IAAIpD,UAAU,CAACiD,mBAAmB,EAAEI,IAAI,EAAE,EAAE,CAAC;IAC1D,MAAM;MAAE1B;IAAU,CAAC,GAAGV,cAAc;IACpC,MAAM;MAAEG;IAAe,CAAC,GAAGH,cAAc;IACzC,MAAMqC,IAAI,SAASnC,oBAAoB,CAACoC,4BAA4B,CAAC;MACjE/C,QAAQ,EAAES,cAAc,CAACU,SAAS;MAClC6B,QAAQ,EAAEvC,cAAc,CAACwC,cAAc;MACvCP,YAAY;MACZE;IACJ,CAAC,CAAC;IACF,MAAMM,OAAO,GAAG5D,YAAY,CAAC,CAAC;IAC9B,MAAMuB,kBAAkB,GAAG;MACvBC,QAAQ,EAAE2B,mBAAmB,EAAE3B,QAAQ,IAAId,QAAQ;MACnDmD,2BAA2B,EAAEV,mBAAmB,EAAEW,YAAY;MAC9DC,SAAS,EAAEH,OAAO;MAClBI,wBAAwB,EAAE/D,kBAAkB,CAAC;QACzCS,QAAQ,EAAEmB,SAAS;QACnBoC,YAAY,EAAE3C,cAAc;QAC5B6B,mBAAmB;QACnBS,OAAO;QACPJ;MACJ,CAAC,CAAC;MACF5B,UAAU,EAAEC;IAChB,CAAC;IACD,MAAMqC,eAAe,GAAG/D,kBAAkB,CAAC;MACvCO,QAAQ;MACRK,UAAU;MACVG;IACJ,CAAC,CAAC;IACF,MAAMY,wBAAwB,GAAG;MAC7BC,aAAa,EAAE,0BAA0B;MACzCC,QAAQ,EAAEd,gBAAgB;MAC1Be,kBAAkB,EAAEV,kBAAkB;MACtCY,OAAO,EAAEtB,OAAO;MAChBqB,cAAc,EAAEtB,cAAc;MAC9BsD;IACJ,CAAC;IACD,MAAM9B,sBAAsB,GAAGzC,kCAAkC,CAAC;MAC9D0C,gBAAgB,EAAEzC,qCAAqC,CAAC;QACpD0C,gBAAgB,EAAEtB;MACtB,CAAC;IACL,CAAC,CAAC;IACF,OAAOoB,sBAAsB,CAAC;MAAEK,MAAM,EAAE5C,uBAAuB,CAACkB,UAAU;IAAE,CAAC,EAAEe,wBAAwB,CAAC;EAC5G,CAAC;EAAA,OAAAoB,6BAAA,CAAA3C,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}