{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { StartDeliveryStreamEncryptionInput, StartDeliveryStreamEncryptionOutput } from \"../models/models_0\";\nimport { deserializeAws_json1_1StartDeliveryStreamEncryptionCommand, serializeAws_json1_1StartDeliveryStreamEncryptionCommand } from \"../protocols/Aws_json1_1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Enables server-side encryption (SSE) for the delivery stream. </p>\n *          <p>This operation is asynchronous. It returns immediately. When you invoke it, Kinesis Data\n *          Firehose first sets the encryption status of the stream to <code>ENABLING</code>, and then\n *          to <code>ENABLED</code>. The encryption status of a delivery stream is the\n *             <code>Status</code> property in <a>DeliveryStreamEncryptionConfiguration</a>.\n *          If the operation fails, the encryption status changes to <code>ENABLING_FAILED</code>. You\n *          can continue to read and write data to your delivery stream while the encryption status is\n *             <code>ENABLING</code>, but the data is not encrypted. It can take up to 5 seconds after\n *          the encryption status changes to <code>ENABLED</code> before all records written to the\n *          delivery stream are encrypted. To find out whether a record or a batch of records was\n *          encrypted, check the response elements <a>PutRecordOutput$Encrypted</a> and\n *             <a>PutRecordBatchOutput$Encrypted</a>, respectively.</p>\n *          <p>To check the encryption status of a delivery stream, use <a>DescribeDeliveryStream</a>.</p>\n *          <p>Even if encryption is currently enabled for a delivery stream, you can still invoke this\n *          operation on it to change the ARN of the CMK or both its type and ARN. If you invoke this\n *          method to change the CMK, and the old CMK is of type <code>CUSTOMER_MANAGED_CMK</code>,\n *          Kinesis Data Firehose schedules the grant it had on the old CMK for retirement. If the new\n *          CMK is of type <code>CUSTOMER_MANAGED_CMK</code>, Kinesis Data Firehose creates a grant\n *          that enables it to use the new CMK to encrypt and decrypt data and to manage the\n *          grant.</p>\n *          <p>If a delivery stream already has encryption enabled and then you invoke this operation\n *          to change the ARN of the CMK or both its type and ARN and you get\n *             <code>ENABLING_FAILED</code>, this only means that the attempt to change the CMK failed.\n *          In this case, encryption remains enabled with the old CMK.</p>\n *          <p>If the encryption status of your delivery stream is <code>ENABLING_FAILED</code>, you\n *          can invoke this operation again with a valid CMK. The CMK must be enabled and the key\n *          policy mustn't explicitly deny the permission for Kinesis Data Firehose to invoke KMS\n *          encrypt and decrypt operations.</p>\n *          <p>You can enable SSE for a delivery stream only if it's a delivery stream that uses\n *             <code>DirectPut</code> as its source. </p>\n *          <p>The <code>StartDeliveryStreamEncryption</code> and\n *             <code>StopDeliveryStreamEncryption</code> operations have a combined limit of 25 calls\n *          per delivery stream per 24 hours. For example, you reach the limit if you call\n *             <code>StartDeliveryStreamEncryption</code> 13 times and\n *             <code>StopDeliveryStreamEncryption</code> 12 times for the same delivery stream in a\n *          24-hour period.</p>\n */\nvar StartDeliveryStreamEncryptionCommand = /** @class */function (_super) {\n  __extends(StartDeliveryStreamEncryptionCommand, _super);\n  // Start section: command_properties\n  // End section: command_properties\n  function StartDeliveryStreamEncryptionCommand(input) {\n    var _this =\n    // Start section: command_constructor\n    _super.call(this) || this;\n    _this.input = input;\n    return _this;\n    // End section: command_constructor\n  }\n  /**\n   * @internal\n   */\n  StartDeliveryStreamEncryptionCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n    this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n    var stack = clientStack.concat(this.middlewareStack);\n    var logger = configuration.logger;\n    var clientName = \"FirehoseClient\";\n    var commandName = \"StartDeliveryStreamEncryptionCommand\";\n    var handlerExecutionContext = {\n      logger: logger,\n      clientName: clientName,\n      commandName: commandName,\n      inputFilterSensitiveLog: StartDeliveryStreamEncryptionInput.filterSensitiveLog,\n      outputFilterSensitiveLog: StartDeliveryStreamEncryptionOutput.filterSensitiveLog\n    };\n    var requestHandler = configuration.requestHandler;\n    return stack.resolve(function (request) {\n      return requestHandler.handle(request.request, options || {});\n    }, handlerExecutionContext);\n  };\n  StartDeliveryStreamEncryptionCommand.prototype.serialize = function (input, context) {\n    return serializeAws_json1_1StartDeliveryStreamEncryptionCommand(input, context);\n  };\n  StartDeliveryStreamEncryptionCommand.prototype.deserialize = function (output, context) {\n    return deserializeAws_json1_1StartDeliveryStreamEncryptionCommand(output, context);\n  };\n  return StartDeliveryStreamEncryptionCommand;\n}($Command);\nexport { StartDeliveryStreamEncryptionCommand };", "map": {"version": 3, "names": ["__extends", "StartDeliveryStreamEncryptionInput", "StartDeliveryStreamEncryptionOutput", "deserializeAws_json1_1StartDeliveryStreamEncryptionCommand", "serializeAws_json1_1StartDeliveryStreamEncryptionCommand", "getSerdePlugin", "Command", "$Command", "StartDeliveryStreamEncryptionCommand", "_super", "input", "_this", "call", "prototype", "resolveMiddleware", "clientStack", "configuration", "options", "middlewareStack", "use", "serialize", "deserialize", "stack", "concat", "logger", "clientName", "commandName", "handlerExecutionContext", "inputFilterSensitiveLog", "filterSensitiveLog", "outputFilterSensitiveLog", "requestHandler", "resolve", "request", "handle", "context", "output"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-firehose/dist/es/commands/StartDeliveryStreamEncryptionCommand.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { StartDeliveryStreamEncryptionInput, StartDeliveryStreamEncryptionOutput } from \"../models/models_0\";\nimport { deserializeAws_json1_1StartDeliveryStreamEncryptionCommand, serializeAws_json1_1StartDeliveryStreamEncryptionCommand, } from \"../protocols/Aws_json1_1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Enables server-side encryption (SSE) for the delivery stream. </p>\n *          <p>This operation is asynchronous. It returns immediately. When you invoke it, Kinesis Data\n *          Firehose first sets the encryption status of the stream to <code>ENABLING</code>, and then\n *          to <code>ENABLED</code>. The encryption status of a delivery stream is the\n *             <code>Status</code> property in <a>DeliveryStreamEncryptionConfiguration</a>.\n *          If the operation fails, the encryption status changes to <code>ENABLING_FAILED</code>. You\n *          can continue to read and write data to your delivery stream while the encryption status is\n *             <code>ENABLING</code>, but the data is not encrypted. It can take up to 5 seconds after\n *          the encryption status changes to <code>ENABLED</code> before all records written to the\n *          delivery stream are encrypted. To find out whether a record or a batch of records was\n *          encrypted, check the response elements <a>PutRecordOutput$Encrypted</a> and\n *             <a>PutRecordBatchOutput$Encrypted</a>, respectively.</p>\n *          <p>To check the encryption status of a delivery stream, use <a>DescribeDeliveryStream</a>.</p>\n *          <p>Even if encryption is currently enabled for a delivery stream, you can still invoke this\n *          operation on it to change the ARN of the CMK or both its type and ARN. If you invoke this\n *          method to change the CMK, and the old CMK is of type <code>CUSTOMER_MANAGED_CMK</code>,\n *          Kinesis Data Firehose schedules the grant it had on the old CMK for retirement. If the new\n *          CMK is of type <code>CUSTOMER_MANAGED_CMK</code>, Kinesis Data Firehose creates a grant\n *          that enables it to use the new CMK to encrypt and decrypt data and to manage the\n *          grant.</p>\n *          <p>If a delivery stream already has encryption enabled and then you invoke this operation\n *          to change the ARN of the CMK or both its type and ARN and you get\n *             <code>ENABLING_FAILED</code>, this only means that the attempt to change the CMK failed.\n *          In this case, encryption remains enabled with the old CMK.</p>\n *          <p>If the encryption status of your delivery stream is <code>ENABLING_FAILED</code>, you\n *          can invoke this operation again with a valid CMK. The CMK must be enabled and the key\n *          policy mustn't explicitly deny the permission for Kinesis Data Firehose to invoke KMS\n *          encrypt and decrypt operations.</p>\n *          <p>You can enable SSE for a delivery stream only if it's a delivery stream that uses\n *             <code>DirectPut</code> as its source. </p>\n *          <p>The <code>StartDeliveryStreamEncryption</code> and\n *             <code>StopDeliveryStreamEncryption</code> operations have a combined limit of 25 calls\n *          per delivery stream per 24 hours. For example, you reach the limit if you call\n *             <code>StartDeliveryStreamEncryption</code> 13 times and\n *             <code>StopDeliveryStreamEncryption</code> 12 times for the same delivery stream in a\n *          24-hour period.</p>\n */\nvar StartDeliveryStreamEncryptionCommand = /** @class */ (function (_super) {\n    __extends(StartDeliveryStreamEncryptionCommand, _super);\n    // Start section: command_properties\n    // End section: command_properties\n    function StartDeliveryStreamEncryptionCommand(input) {\n        var _this = \n        // Start section: command_constructor\n        _super.call(this) || this;\n        _this.input = input;\n        return _this;\n        // End section: command_constructor\n    }\n    /**\n     * @internal\n     */\n    StartDeliveryStreamEncryptionCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n        this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n        var stack = clientStack.concat(this.middlewareStack);\n        var logger = configuration.logger;\n        var clientName = \"FirehoseClient\";\n        var commandName = \"StartDeliveryStreamEncryptionCommand\";\n        var handlerExecutionContext = {\n            logger: logger,\n            clientName: clientName,\n            commandName: commandName,\n            inputFilterSensitiveLog: StartDeliveryStreamEncryptionInput.filterSensitiveLog,\n            outputFilterSensitiveLog: StartDeliveryStreamEncryptionOutput.filterSensitiveLog,\n        };\n        var requestHandler = configuration.requestHandler;\n        return stack.resolve(function (request) {\n            return requestHandler.handle(request.request, options || {});\n        }, handlerExecutionContext);\n    };\n    StartDeliveryStreamEncryptionCommand.prototype.serialize = function (input, context) {\n        return serializeAws_json1_1StartDeliveryStreamEncryptionCommand(input, context);\n    };\n    StartDeliveryStreamEncryptionCommand.prototype.deserialize = function (output, context) {\n        return deserializeAws_json1_1StartDeliveryStreamEncryptionCommand(output, context);\n    };\n    return StartDeliveryStreamEncryptionCommand;\n}($Command));\nexport { StartDeliveryStreamEncryptionCommand };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,kCAAkC,EAAEC,mCAAmC,QAAQ,oBAAoB;AAC5G,SAASC,0DAA0D,EAAEC,wDAAwD,QAAS,0BAA0B;AAChK,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,OAAO,IAAIC,QAAQ,QAAQ,wBAAwB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,oCAAoC,GAAG,aAAe,UAAUC,MAAM,EAAE;EACxET,SAAS,CAACQ,oCAAoC,EAAEC,MAAM,CAAC;EACvD;EACA;EACA,SAASD,oCAAoCA,CAACE,KAAK,EAAE;IACjD,IAAIC,KAAK;IACT;IACAF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACzBD,KAAK,CAACD,KAAK,GAAGA,KAAK;IACnB,OAAOC,KAAK;IACZ;EACJ;EACA;AACJ;AACA;EACIH,oCAAoC,CAACK,SAAS,CAACC,iBAAiB,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAEC,OAAO,EAAE;IAC9G,IAAI,CAACC,eAAe,CAACC,GAAG,CAACd,cAAc,CAACW,aAAa,EAAE,IAAI,CAACI,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;IACzF,IAAIC,KAAK,GAAGP,WAAW,CAACQ,MAAM,CAAC,IAAI,CAACL,eAAe,CAAC;IACpD,IAAIM,MAAM,GAAGR,aAAa,CAACQ,MAAM;IACjC,IAAIC,UAAU,GAAG,gBAAgB;IACjC,IAAIC,WAAW,GAAG,sCAAsC;IACxD,IAAIC,uBAAuB,GAAG;MAC1BH,MAAM,EAAEA,MAAM;MACdC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA,WAAW;MACxBE,uBAAuB,EAAE3B,kCAAkC,CAAC4B,kBAAkB;MAC9EC,wBAAwB,EAAE5B,mCAAmC,CAAC2B;IAClE,CAAC;IACD,IAAIE,cAAc,GAAGf,aAAa,CAACe,cAAc;IACjD,OAAOT,KAAK,CAACU,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpC,OAAOF,cAAc,CAACG,MAAM,CAACD,OAAO,CAACA,OAAO,EAAEhB,OAAO,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC,EAAEU,uBAAuB,CAAC;EAC/B,CAAC;EACDnB,oCAAoC,CAACK,SAAS,CAACO,SAAS,GAAG,UAAUV,KAAK,EAAEyB,OAAO,EAAE;IACjF,OAAO/B,wDAAwD,CAACM,KAAK,EAAEyB,OAAO,CAAC;EACnF,CAAC;EACD3B,oCAAoC,CAACK,SAAS,CAACQ,WAAW,GAAG,UAAUe,MAAM,EAAED,OAAO,EAAE;IACpF,OAAOhC,0DAA0D,CAACiC,MAAM,EAAED,OAAO,CAAC;EACtF,CAAC;EACD,OAAO3B,oCAAoC;AAC/C,CAAC,CAACD,QAAQ,CAAE;AACZ,SAASC,oCAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}