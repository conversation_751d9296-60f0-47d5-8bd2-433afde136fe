{"ast": null, "code": "import { parseAWSExports } from '../parseAWSExports.mjs';\nimport { isAmplifyOutputs, parseAmplifyOutputs } from '../parseAmplifyOutputs.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Parses the variety of configuration shapes that Amplify can accept into a ResourcesConfig.\n *\n * @param amplifyConfig An Amplify configuration object conforming to one of the supported schemas.\n * @return A ResourcesConfig for the provided configuration object.\n */\nconst parseAmplifyConfig = amplifyConfig => {\n  if (Object.keys(amplifyConfig).some(key => key.startsWith('aws_'))) {\n    return parseAWSExports(amplifyConfig);\n  } else if (isAmplifyOutputs(amplifyConfig)) {\n    return parseAmplifyOutputs(amplifyConfig);\n  } else {\n    return amplifyConfig;\n  }\n};\nexport { parseAmplifyConfig };", "map": {"version": 3, "names": ["parseAWSExports", "isAmplifyOutputs", "parseAmplifyOutputs", "parseAmplifyConfig", "amplifyConfig", "Object", "keys", "some", "key", "startsWith"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/parseAmplifyConfig.mjs"], "sourcesContent": ["import { parseAWSExports } from '../parseAWSExports.mjs';\nimport { isAmplifyOutputs, parseAmplifyOutputs } from '../parseAmplifyOutputs.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Parses the variety of configuration shapes that Amplify can accept into a ResourcesConfig.\n *\n * @param amplifyConfig An Amplify configuration object conforming to one of the supported schemas.\n * @return A ResourcesConfig for the provided configuration object.\n */\nconst parseAmplifyConfig = (amplifyConfig) => {\n    if (Object.keys(amplifyConfig).some(key => key.startsWith('aws_'))) {\n        return parseAWSExports(amplifyConfig);\n    }\n    else if (isAmplifyOutputs(amplifyConfig)) {\n        return parseAmplifyOutputs(amplifyConfig);\n    }\n    else {\n        return amplifyConfig;\n    }\n};\n\nexport { parseAmplifyConfig };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,wBAAwB;AACxD,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,4BAA4B;;AAElF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAIC,aAAa,IAAK;EAC1C,IAAIC,MAAM,CAACC,IAAI,CAACF,aAAa,CAAC,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE;IAChE,OAAOT,eAAe,CAACI,aAAa,CAAC;EACzC,CAAC,MACI,IAAIH,gBAAgB,CAACG,aAAa,CAAC,EAAE;IACtC,OAAOF,mBAAmB,CAACE,aAAa,CAAC;EAC7C,CAAC,MACI;IACD,OAAOA,aAAa;EACxB;AACJ,CAAC;AAED,SAASD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}