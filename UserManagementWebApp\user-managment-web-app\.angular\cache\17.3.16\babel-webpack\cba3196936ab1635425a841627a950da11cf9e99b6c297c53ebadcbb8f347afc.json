{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport AuthenticationHelper from './AuthenticationHelper/AuthenticationHelper.mjs';\nimport BigInteger from './BigInteger/BigInteger.mjs';\nimport { calculateA } from './calculate/calculateA.mjs';\nimport { INIT_N } from './constants.mjs';\nimport '@aws-crypto/sha256-js';\nimport { getHexFromBytes } from './getHexFromBytes.mjs';\nimport { getRandomBytes } from './getRandomBytes.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns a new {@link AuthenticationHelper} instance with randomly generated BigInteger seed\n *\n * @param userPoolName Cognito user pool name.\n * @returns An {@link AuthenticationHelper} instance.\n *\n * @internal\n */\nconst getAuthenticationHelper = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (userPoolName) {\n    const N = new BigInteger(INIT_N, 16);\n    const g = new BigInteger('2', 16);\n    const a = generateRandomBigInteger();\n    const A = yield calculateA({\n      a,\n      g,\n      N\n    });\n    return new AuthenticationHelper({\n      userPoolName,\n      a,\n      g,\n      A,\n      N\n    });\n  });\n  return function getAuthenticationHelper(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\n/**\n * Generates a random BigInteger.\n *\n * @returns {BigInteger} a random value.\n */\nconst generateRandomBigInteger = () => {\n  // This will be interpreted as a postive 128-bit integer\n  const hexRandom = getHexFromBytes(getRandomBytes(128));\n  // There is no need to do randomBigInt.mod(this.N - 1) as N (3072-bit) is > 128 bytes (1024-bit)\n  return new BigInteger(hexRandom, 16);\n};\nexport { getAuthenticationHelper };", "map": {"version": 3, "names": ["AuthenticationHelper", "BigInteger", "calculateA", "INIT_N", "getHexFromBytes", "getRandomBytes", "getAuthenticationHelper", "_ref", "_asyncToGenerator", "userPoolName", "N", "g", "a", "generateRandomBigInteger", "A", "_x", "apply", "arguments", "hexRandom"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/srp/getAuthenticationHelper.mjs"], "sourcesContent": ["import AuthenticationHelper from './AuthenticationHelper/AuthenticationHelper.mjs';\nimport BigInteger from './BigInteger/BigInteger.mjs';\nimport { calculateA } from './calculate/calculateA.mjs';\nimport { INIT_N } from './constants.mjs';\nimport '@aws-crypto/sha256-js';\nimport { getHexFromBytes } from './getHexFromBytes.mjs';\nimport { getRandomBytes } from './getRandomBytes.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns a new {@link AuthenticationHelper} instance with randomly generated BigInteger seed\n *\n * @param userPoolName Cognito user pool name.\n * @returns An {@link AuthenticationHelper} instance.\n *\n * @internal\n */\nconst getAuthenticationHelper = async (userPoolName) => {\n    const N = new BigInteger(INIT_N, 16);\n    const g = new BigInteger('2', 16);\n    const a = generateRandomBigInteger();\n    const A = await calculateA({ a, g, N });\n    return new AuthenticationHelper({ userPoolName, a, g, A, N });\n};\n/**\n * Generates a random BigInteger.\n *\n * @returns {BigInteger} a random value.\n */\nconst generateRandomBigInteger = () => {\n    // This will be interpreted as a postive 128-bit integer\n    const hexRandom = getHexFromBytes(getRandomBytes(128));\n    // There is no need to do randomBigInt.mod(this.N - 1) as N (3072-bit) is > 128 bytes (1024-bit)\n    return new BigInteger(hexRandom, 16);\n};\n\nexport { getAuthenticationHelper };\n"], "mappings": ";AAAA,OAAOA,oBAAoB,MAAM,iDAAiD;AAClF,OAAOC,UAAU,MAAM,6BAA6B;AACpD,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,MAAM,QAAQ,iBAAiB;AACxC,OAAO,uBAAuB;AAC9B,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,cAAc,QAAQ,sBAAsB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,YAAY,EAAK;IACpD,MAAMC,CAAC,GAAG,IAAIT,UAAU,CAACE,MAAM,EAAE,EAAE,CAAC;IACpC,MAAMQ,CAAC,GAAG,IAAIV,UAAU,CAAC,GAAG,EAAE,EAAE,CAAC;IACjC,MAAMW,CAAC,GAAGC,wBAAwB,CAAC,CAAC;IACpC,MAAMC,CAAC,SAASZ,UAAU,CAAC;MAAEU,CAAC;MAAED,CAAC;MAAED;IAAE,CAAC,CAAC;IACvC,OAAO,IAAIV,oBAAoB,CAAC;MAAES,YAAY;MAAEG,CAAC;MAAED,CAAC;MAAEG,CAAC;MAAEJ;IAAE,CAAC,CAAC;EACjE,CAAC;EAAA,gBANKJ,uBAAuBA,CAAAS,EAAA;IAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;EAAA;AAAA,GAM5B;AACD;AACA;AACA;AACA;AACA;AACA,MAAMJ,wBAAwB,GAAGA,CAAA,KAAM;EACnC;EACA,MAAMK,SAAS,GAAGd,eAAe,CAACC,cAAc,CAAC,GAAG,CAAC,CAAC;EACtD;EACA,OAAO,IAAIJ,UAAU,CAACiB,SAAS,EAAE,EAAE,CAAC;AACxC,CAAC;AAED,SAASZ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}