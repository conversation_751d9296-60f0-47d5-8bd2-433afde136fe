{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/SaltboxActionableGrid/Saltbox-Data-App/saltbox-data-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { SBFormlyRendererComponent } from 'src/app/sb-formly-renderer/sb-formly-renderer.component';\nimport { from } from 'rxjs';\nimport { UserPermissionLevels } from 'src/app/core/enums/shared';\nimport { DB_PRIMARY_KEY } from 'src/app/shared/constants/record.const';\nimport { AssetUrlPipe } from '../../shared/pipes/asset-url.pipe';\nimport { BlockUIModule } from 'primeng/blockui';\nimport { ButtonModule } from 'primeng/button';\nimport { FormsModule } from '@angular/forms';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { FileUploadComponent } from '../../shared/file-upload/file-upload.component';\nimport { NgIf } from '@angular/common';\nimport { SBFormlyRendererComponent as SBFormlyRendererComponent_1 } from '../../sb-formly-renderer/sb-formly-renderer.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/attachment.service\";\nimport * as i2 from \"src/app/core/services/notification.service\";\nimport * as i3 from \"../services/dynamic-forms.service\";\nimport * as i4 from \"primeng/checkbox\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/blockui\";\nfunction DynamicFormBodyComponent_div_3_app_file_upload_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-file-upload\", 9);\n    i0.ɵɵlistener(\"fileUploadEvent\", function DynamicFormBodyComponent_div_3_app_file_upload_1_Template_app_file_upload_fileUploadEvent_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFileUploadEvent($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"projectId\", ctx_r1.projectId)(\"versionId\", ctx_r1.projectVersionId)(\"dataStoreName\", ctx_r1.datastore.name)(\"recordId\", ctx_r1.recordId)(\"fileService\", ctx_r1.attachmentService)(\"aliasIds\", ctx_r1.data == null ? null : ctx_r1.data.__sbmeta == null ? null : ctx_r1.data.__sbmeta.Attachments)(\"isPreview\", ctx_r1.isPreview);\n  }\n}\nfunction DynamicFormBodyComponent_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"p-checkbox\", 11);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function DynamicFormBodyComponent_div_3_div_2_Template_p_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.emailOnSubmit, $event) || (ctx_r1.emailOnSubmit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.emailOnSubmit);\n    i0.ɵɵproperty(\"binary\", true)(\"disabled\", ctx_r1.isPreview);\n  }\n}\nfunction DynamicFormBodyComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, DynamicFormBodyComponent_div_3_app_file_upload_1_Template, 1, 7, \"app-file-upload\", 7)(2, DynamicFormBodyComponent_div_3_div_2_Template, 2, 3, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r1.dynamicForm == null ? null : ctx_r1.dynamicForm.additionalSettings == null ? null : ctx_r1.dynamicForm.additionalSettings.hideAttachments));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.recordId && (ctx_r1.dynamicForm == null ? null : ctx_r1.dynamicForm.additionalSettings == null ? null : ctx_r1.dynamicForm.additionalSettings.allowEmail));\n  }\n}\nfunction DynamicFormBodyComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"p-button\", 13);\n    i0.ɵɵlistener(\"onClick\", function DynamicFormBodyComponent_div_4_Template_p_button_onClick_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.submitForm());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"icon\", (tmp_1_0 = ctx_r1.submitBtnIcon) !== null && tmp_1_0 !== undefined ? tmp_1_0 : \"pi pi-send\");\n  }\n}\nfunction DynamicFormBodyComponent_p_blockUI_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-blockUI\", 14);\n    i0.ɵɵelement(1, \"img\", 15);\n    i0.ɵɵpipe(2, \"assetUrl\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"blocked\", ctx_r1.showSpinner);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 2, \"layout/images/salt-box-loading.gif\"), i0.ɵɵsanitizeUrl);\n  }\n}\nexport class DynamicFormBodyComponent {\n  constructor(attachmentService, notificationService, dynamicFormsService) {\n    this.attachmentService = attachmentService;\n    this.notificationService = notificationService;\n    this.dynamicFormsService = dynamicFormsService;\n    this.isPreview = false;\n    this.showFormOptions = true;\n    this.showSubmitButton = true;\n    this.userPermissionLevel = UserPermissionLevels.EditAndDelete;\n    // If this form is a part of a complex object and there is no other form as main form(for example in grids master detail), \n    // the user should set partialObject to true to calculate the formula properly \n    this.partialObject = false;\n    this.formOnSubmit = new EventEmitter(); // Complete validated form data\n    this.emailOnSubmit = false;\n    this.triggerResetFormEvent = new EventEmitter();\n    this.showSpinner = false;\n  }\n  ngAfterViewInit() {\n    var _this = this;\n    this.resetDataEntryFormSubscription = this.dynamicFormsService.resetDataEntryForm.subscribe( /*#__PURE__*/_asyncToGenerator(function* () {\n      _this.setDefaultValues();\n      _this.triggerResetFormEvent.next(_this.data);\n    }));\n    if (!this.recordId && this.userPermissionLevel !== UserPermissionLevels.View) this.setDefaultValues();\n  }\n  ngOnInit() {\n    this.setFieldsConfig();\n    if (this.isPreview) return;\n    // Listen for the save trigger event\n    this.saveEventSubscription = this.triggerSaveEvent?.subscribe(() => {\n      this.submitForm();\n    });\n  }\n  setFieldsConfig() {\n    const fieldsConfig = this.dynamicFormsService.getFieldsConfig(this.dynamicForm);\n    if (!fieldsConfig) {\n      this.notificationService.showError(\"Fields Config Error\", \"Failed to parse JSON string!\");\n      this.fieldsConfig = [];\n      return;\n    }\n    // set fields editibility\n    let fieldsAreEditable = null;\n    if (this.userPermissionLevel === UserPermissionLevels.View) fieldsAreEditable = false; // when user doesn't have permission to edit the form \n    else if (!this.recordId && !this.isPreview) fieldsAreEditable = true; // all fields are editable in insert mode\n    if (fieldsAreEditable != null) fieldsConfig.forEach(field => {\n      field.props.readonly = !fieldsAreEditable;\n      field.props.disabled = !fieldsAreEditable;\n    });\n    this.fieldsConfig = fieldsConfig;\n  }\n  setDefaultValues() {\n    this.showSpinner = true;\n    const id = this.data ? this.data[DB_PRIMARY_KEY] : undefined;\n    from(this.dynamicFormsService.getDefaultValues(this.fieldsConfig, this.schema ?? this.datastore?.baseSchema)).subscribe({\n      next: newData => {\n        // NOTE: We need to update the properties of the existing model object directly\n        // to preserve references, particularly important for nested objects that may be\n        // bound elsewhere in the application. Creating a new object would disrupt these bindings\n        // and could lead to inconsistent states or UI updates. this.data should NOT be null unless this is a preview form!!\n        if (!this.data) this.data = {};\n        //reseting the object\n        Object.keys(this.data).forEach(key => {\n          this.data[key] = undefined;\n        });\n        //copying the values\n        Object.keys(newData).forEach(key => {\n          this.data[key] = newData[key];\n        });\n        this.data[DB_PRIMARY_KEY] = id;\n        this.formlyRenderer.setFormModel(this.data);\n      },\n      error: error => {\n        this.notificationService.showError('Something went wrong!', error?.error ?? error?.message);\n        this.showSpinner = false;\n      },\n      complete: () => {\n        this.showSpinner = false;\n      }\n    });\n  }\n  ngOnDestroy() {\n    if (this.resetDataEntryFormSubscription) {\n      this.resetDataEntryFormSubscription.unsubscribe();\n    }\n    if (this.saveEventSubscription) {\n      this.saveEventSubscription.unsubscribe();\n    }\n  }\n  onFileUploadEvent(aliasIds) {\n    this.aliasIds = aliasIds;\n  }\n  submitForm() {\n    const validity = this.formlyRenderer.validateForm();\n    if (!validity.isValid) {\n      this.notificationService.showError('Validation Failed', validity.errorMessage || 'Please check the required fields!');\n      return;\n    }\n    // if user didn't make any changes(updatedData is null), just return the data with local updates like default values\n    this.formOnSubmit.emit({\n      data: this.updatedData ?? this.data,\n      aliasIds: this.aliasIds ?? this.data?.__sbmeta?.Attachments,\n      sendEmail: this.emailOnSubmit\n    });\n  }\n  dataChange(data) {\n    this.updatedData = data;\n  }\n  onValidationErrorsChanged(errors) {\n    this.errors = errors;\n  }\n  static {\n    this.ɵfac = function DynamicFormBodyComponent_Factory(t) {\n      return new (t || DynamicFormBodyComponent)(i0.ɵɵdirectiveInject(i1.AttachmentService), i0.ɵɵdirectiveInject(i2.NotificationService), i0.ɵɵdirectiveInject(i3.DynamicFormsService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DynamicFormBodyComponent,\n      selectors: [[\"app-dynamic-form-body\"]],\n      viewQuery: function DynamicFormBodyComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(SBFormlyRendererComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.formlyRenderer = _t.first);\n        }\n      },\n      inputs: {\n        isPreview: \"isPreview\",\n        data: \"data\",\n        projectId: \"projectId\",\n        datastore: \"datastore\",\n        schema: \"schema\",\n        projectVersionId: \"projectVersionId\",\n        recordId: \"recordId\",\n        submitBtnIcon: \"submitBtnIcon\",\n        dynamicForm: \"dynamicForm\",\n        showFormOptions: \"showFormOptions\",\n        showSubmitButton: \"showSubmitButton\",\n        userPermissionLevel: \"userPermissionLevel\",\n        triggerSaveEvent: \"triggerSaveEvent\",\n        partialObject: \"partialObject\",\n        partialObjectPath: \"partialObjectPath\",\n        masterRecordParams: \"masterRecordParams\"\n      },\n      outputs: {\n        formOnSubmit: \"formOnSubmit\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 12,\n      consts: [[1, \"grid\", \"data-entry-form\"], [1, \"col-12\", \"card-container\"], [3, \"modelChange\", \"fieldsConfig\", \"baseSchema\", \"projectVersionId\", \"model\", \"trackDataChangesForFormulas\", \"triggerResetFormEvent\", \"masterRecordParams\", \"partialObject\", \"partialObjectPath\"], [\"class\", \"col-12 px-3\", 4, \"ngIf\"], [\"class\", \"fixed bottom-0 right-0 mr-4 mb-2\", 4, \"ngIf\"], [\"styleClass\", \"z-5\", 3, \"blocked\", 4, \"ngIf\"], [1, \"col-12\", \"px-3\"], [\"fileUploadContainerClass\", \"embedded-upload-form\", \"class\", \"form-viewer-file-upload\", 3, \"projectId\", \"versionId\", \"dataStoreName\", \"recordId\", \"fileService\", \"aliasIds\", \"isPreview\", \"fileUploadEvent\", 4, \"ngIf\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [\"fileUploadContainerClass\", \"embedded-upload-form\", 1, \"form-viewer-file-upload\", 3, \"fileUploadEvent\", \"projectId\", \"versionId\", \"dataStoreName\", \"recordId\", \"fileService\", \"aliasIds\", \"isPreview\"], [1, \"mt-3\"], [\"label\", \"Send me a copy of my responses\", 3, \"ngModelChange\", \"ngModel\", \"binary\", \"disabled\"], [1, \"fixed\", \"bottom-0\", \"right-0\", \"mr-4\", \"mb-2\"], [\"pRipple\", \"\", \"type\", \"button\", \"label\", \"Submit\", 3, \"onClick\", \"icon\"], [\"styleClass\", \"z-5\", 3, \"blocked\"], [1, \"ui-progress-spinner\", 3, \"src\"]],\n      template: function DynamicFormBodyComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"app-sb-formly-renderer\", 2);\n          i0.ɵɵtwoWayListener(\"modelChange\", function DynamicFormBodyComponent_Template_app_sb_formly_renderer_modelChange_2_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.data, $event) || (ctx.data = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"modelChange\", function DynamicFormBodyComponent_Template_app_sb_formly_renderer_modelChange_2_listener($event) {\n            return ctx.dataChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(3, DynamicFormBodyComponent_div_3_Template, 3, 2, \"div\", 3)(4, DynamicFormBodyComponent_div_4_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, DynamicFormBodyComponent_p_blockUI_5_Template, 3, 4, \"p-blockUI\", 5);\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"fieldsConfig\", ctx.fieldsConfig)(\"baseSchema\", (tmp_1_0 = ctx.datastore == null ? null : ctx.datastore.baseSchema) !== null && tmp_1_0 !== undefined ? tmp_1_0 : ctx.schema)(\"projectVersionId\", ctx.projectVersionId);\n          i0.ɵɵtwoWayProperty(\"model\", ctx.data);\n          i0.ɵɵproperty(\"trackDataChangesForFormulas\", true)(\"triggerResetFormEvent\", ctx.triggerResetFormEvent)(\"masterRecordParams\", ctx.masterRecordParams)(\"partialObject\", ctx.partialObject)(\"partialObjectPath\", ctx.partialObjectPath);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showFormOptions);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSubmitButton);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSpinner);\n        }\n      },\n      dependencies: [SBFormlyRendererComponent_1, NgIf, FileUploadComponent, CheckboxModule, i4.Checkbox, FormsModule, i5.NgControlStatus, i5.NgModel, ButtonModule, i6.Button, BlockUIModule, i7.BlockUI, AssetUrlPipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "SBFormlyRendererComponent", "from", "UserPermissionLevels", "DB_PRIMARY_KEY", "AssetUrlPipe", "BlockUIModule", "ButtonModule", "FormsModule", "CheckboxModule", "FileUploadComponent", "NgIf", "SBFormlyRendererComponent_1", "i0", "ɵɵelementStart", "ɵɵlistener", "DynamicFormBodyComponent_div_3_app_file_upload_1_Template_app_file_upload_fileUploadEvent_0_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onFileUploadEvent", "ɵɵelementEnd", "ɵɵproperty", "projectId", "projectVersionId", "datastore", "name", "recordId", "attachmentService", "data", "__sbmeta", "Attachments", "isPreview", "ɵɵtwoWayListener", "DynamicFormBodyComponent_div_3_div_2_Template_p_checkbox_ngModelChange_1_listener", "_r3", "ɵɵtwoWayBindingSet", "emailOnSubmit", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵtemplate", "DynamicFormBodyComponent_div_3_app_file_upload_1_Template", "DynamicFormBodyComponent_div_3_div_2_Template", "dynamicForm", "additionalSettings", "hideAttachments", "allowEmail", "DynamicFormBodyComponent_div_4_Template_p_button_onClick_1_listener", "_r4", "submitForm", "tmp_1_0", "submitBtnIcon", "undefined", "ɵɵelement", "showSpinner", "ɵɵpipeBind1", "ɵɵsanitizeUrl", "DynamicFormBodyComponent", "constructor", "notificationService", "dynamicFormsService", "showFormOptions", "showSubmitButton", "userPermissionLevel", "EditAndDelete", "partialObject", "formOnSubmit", "triggerResetFormEvent", "ngAfterViewInit", "_this", "resetDataEntryFormSubscription", "resetDataEntryForm", "subscribe", "_asyncToGenerator", "set<PERSON>efault<PERSON><PERSON><PERSON>", "next", "View", "ngOnInit", "setFieldsConfig", "saveEventSubscription", "triggerSaveEvent", "fieldsConfig", "getFieldsConfig", "showError", "fieldsAreEditable", "for<PERSON>ach", "field", "props", "readonly", "disabled", "id", "getDefaultValues", "schema", "baseSchema", "newData", "Object", "keys", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setFormModel", "error", "message", "complete", "ngOnDestroy", "unsubscribe", "aliasIds", "validity", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "emit", "updatedData", "sendEmail", "dataChange", "onValidationErrorsChanged", "errors", "ɵɵdirectiveInject", "i1", "AttachmentService", "i2", "NotificationService", "i3", "DynamicFormsService", "selectors", "viewQuery", "DynamicFormBodyComponent_Query", "rf", "ctx", "DynamicFormBodyComponent_Template_app_sb_formly_renderer_modelChange_2_listener", "DynamicFormBodyComponent_div_3_Template", "DynamicFormBodyComponent_div_4_Template", "DynamicFormBodyComponent_p_blockUI_5_Template", "masterRecordParams", "partialObjectPath", "i4", "Checkbox", "i5", "NgControlStatus", "NgModel", "i6", "<PERSON><PERSON>", "i7", "BlockUI", "encapsulation"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\dynamic-forms\\dynamic-form-body\\dynamic-form-body.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\dynamic-forms\\dynamic-form-body\\dynamic-form-body.component.html"], "sourcesContent": ["import { AfterViewInit, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';\r\nimport { NotificationService } from 'src/app/core/services/notification.service';\r\nimport { AttachmentService } from '../../core/services/attachment.service';\r\nimport { DynamicForm } from '../models/dynamic-form';\r\nimport { Datastore } from 'src/app/shared/models/datastore';\r\nimport { DynamicFormsService } from '../services/dynamic-forms.service';\r\nimport { SBFormlyRendererComponent } from 'src/app/sb-formly-renderer/sb-formly-renderer.component';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { from, Subscription } from 'rxjs';\r\nimport { UserPermissionLevels } from 'src/app/core/enums/shared';\r\nimport { ExtendedJsonSchema } from 'src/app/shared/models/extended-json-schema';\r\nimport { DB_PRIMARY_KEY } from 'src/app/shared/constants/record.const';\r\nimport { MasterRecordParams } from 'src/app/core/models/master-record-params';\r\nimport { AssetUrlPipe } from '../../shared/pipes/asset-url.pipe';\r\nimport { BlockUIModule } from 'primeng/blockui';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { FileUploadComponent } from '../../shared/file-upload/file-upload.component';\r\nimport { NgIf } from '@angular/common';\r\nimport { SBFormlyRendererComponent as SBFormlyRendererComponent_1 } from '../../sb-formly-renderer/sb-formly-renderer.component';\r\n\r\n@Component({\r\n    selector: 'app-dynamic-form-body',\r\n    templateUrl: './dynamic-form-body.component.html',\r\n    standalone: true,\r\n    imports: [SBFormlyRendererComponent_1, NgIf, FileUploadComponent, CheckboxModule, FormsModule, ButtonModule, BlockUIModule, AssetUrlPipe]\r\n})\r\nexport class DynamicFormBodyComponent implements OnInit, AfterViewInit, OnDestroy {\r\n\r\n  @Input() isPreview = false;\r\n  @Input() data: any; // The form data\r\n  @Input() projectId: string;\r\n  @Input() datastore: Datastore;\r\n  // in case there is no datastore and user want use this component with ExtendedJsonSchema(like in master details)\r\n  @Input() schema: ExtendedJsonSchema;\r\n  @Input() projectVersionId: string;\r\n  @Input() recordId: string;\r\n  @Input() submitBtnIcon: string;\r\n  @Input() dynamicForm: DynamicForm;\r\n  @Input() showFormOptions = true;\r\n  @Input() showSubmitButton = true;\r\n  @Input() userPermissionLevel = UserPermissionLevels.EditAndDelete;\r\n  @Input() triggerSaveEvent: EventEmitter<any>;\r\n\r\n  // If this form is a part of a complex object and there is no other form as main form(for example in grids master detail), \r\n  // the user should set partialObject to true to calculate the formula properly \r\n  @Input() partialObject = false;\r\n  @Input() partialObjectPath: string;\r\n  @Input() masterRecordParams: MasterRecordParams;\r\n\r\n  @Output() public formOnSubmit = new EventEmitter<any>(); // Complete validated form data\r\n  @ViewChild(SBFormlyRendererComponent) formlyRenderer: SBFormlyRendererComponent;\r\n\r\n  updatedData: any;\r\n  aliasIds: string[];\r\n  emailOnSubmit = false;\r\n  errors: any[];\r\n  triggerResetFormEvent = new EventEmitter<any>();\r\n  fieldsConfig: FormlyFieldConfig[];\r\n  showSpinner = false;\r\n  private saveEventSubscription: Subscription;\r\n  private resetDataEntryFormSubscription: Subscription;\r\n\r\n  constructor(\r\n    public attachmentService: AttachmentService,\r\n    private notificationService: NotificationService,\r\n    private dynamicFormsService: DynamicFormsService) { }\r\n\r\n  ngAfterViewInit() {\r\n    this.resetDataEntryFormSubscription = this.dynamicFormsService.resetDataEntryForm.subscribe(async () => {\r\n      this.setDefaultValues();\r\n      this.triggerResetFormEvent.next(this.data);\r\n    });\r\n\r\n    if (!this.recordId && this.userPermissionLevel !== UserPermissionLevels.View)\r\n      this.setDefaultValues();\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.setFieldsConfig();\r\n\r\n    if (this.isPreview)\r\n      return;\r\n\r\n    // Listen for the save trigger event\r\n    this.saveEventSubscription = this.triggerSaveEvent?.subscribe(() => {\r\n      this.submitForm();\r\n    });\r\n  }\r\n\r\n  setFieldsConfig() {\r\n    const fieldsConfig = this.dynamicFormsService.getFieldsConfig(this.dynamicForm)\r\n\r\n    if (!fieldsConfig) {\r\n      this.notificationService.showError(\"Fields Config Error\", \"Failed to parse JSON string!\");\r\n      this.fieldsConfig = [];\r\n      return;\r\n    }\r\n\r\n    // set fields editibility\r\n    let fieldsAreEditable: boolean | null = null;\r\n\r\n    if (this.userPermissionLevel === UserPermissionLevels.View)\r\n      fieldsAreEditable = false; // when user doesn't have permission to edit the form \r\n    else if (!this.recordId && !this.isPreview)\r\n      fieldsAreEditable = true; // all fields are editable in insert mode\r\n\r\n    if (fieldsAreEditable != null)\r\n      fieldsConfig.forEach(field => {\r\n        field.props.readonly = !fieldsAreEditable;\r\n        field.props.disabled = !fieldsAreEditable;\r\n      });\r\n\r\n    this.fieldsConfig = fieldsConfig;\r\n  }\r\n\r\n  setDefaultValues() {\r\n    this.showSpinner = true;\r\n\r\n    const id = this.data ? this.data[DB_PRIMARY_KEY] : undefined;\r\n    from(this.dynamicFormsService.getDefaultValues(this.fieldsConfig, this.schema ?? this.datastore?.baseSchema))\r\n      .subscribe({\r\n        next: (newData) => {\r\n          // NOTE: We need to update the properties of the existing model object directly\r\n          // to preserve references, particularly important for nested objects that may be\r\n          // bound elsewhere in the application. Creating a new object would disrupt these bindings\r\n          // and could lead to inconsistent states or UI updates. this.data should NOT be null unless this is a preview form!!\r\n\r\n          if (!this.data)\r\n            this.data = {};\r\n\r\n          //reseting the object\r\n          Object.keys(this.data).forEach(key => {\r\n            this.data[key] = undefined;\r\n          });\r\n\r\n          //copying the values\r\n          Object.keys(newData).forEach(key => {\r\n            this.data[key] = newData[key];\r\n          });\r\n          this.data[DB_PRIMARY_KEY] = id;\r\n\r\n          this.formlyRenderer.setFormModel(this.data);\r\n        },\r\n        error: (error) => {\r\n          this.notificationService.showError('Something went wrong!', error?.error ?? error?.message);\r\n          this.showSpinner = false;\r\n        },\r\n        complete: () => {\r\n          this.showSpinner = false;\r\n        }\r\n      });\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.resetDataEntryFormSubscription) {\r\n      this.resetDataEntryFormSubscription.unsubscribe();\r\n    }\r\n\r\n    if (this.saveEventSubscription) {\r\n      this.saveEventSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  onFileUploadEvent(aliasIds: string[]) {\r\n    this.aliasIds = aliasIds;\r\n  }\r\n\r\n  submitForm() {\r\n    const validity = this.formlyRenderer.validateForm();\r\n    if (!validity.isValid) {\r\n      this.notificationService.showError('Validation Failed', validity.errorMessage || 'Please check the required fields!');\r\n      return;\r\n    }\r\n\r\n    // if user didn't make any changes(updatedData is null), just return the data with local updates like default values\r\n    this.formOnSubmit.emit({ data: this.updatedData ?? this.data, aliasIds: this.aliasIds ?? this.data?.__sbmeta?.Attachments, sendEmail: this.emailOnSubmit });\r\n  }\r\n\r\n  dataChange(data: any) {\r\n    this.updatedData = data;\r\n  }\r\n\r\n  onValidationErrorsChanged(errors: any[]) {\r\n    this.errors = errors;\r\n  }\r\n}\r\n", "<div class=\"grid data-entry-form\">\r\n  <div class=\"col-12 card-container\">\r\n    <app-sb-formly-renderer [fieldsConfig]=\"fieldsConfig\" [baseSchema]=\"datastore?.baseSchema ?? schema\"\r\n      [projectVersionId]=\"projectVersionId\" [(model)]=\"data\" (modelChange)=\"dataChange($event)\"\r\n      [trackDataChangesForFormulas]=\"true\" [triggerResetFormEvent]=\"triggerResetFormEvent\"\r\n      [masterRecordParams]=\"masterRecordParams\" [partialObject]=\"partialObject\"\r\n      [partialObjectPath]=\"partialObjectPath\"></app-sb-formly-renderer>\r\n  </div>\r\n  <div *ngIf=\"showFormOptions\" class=\"col-12 px-3\">\r\n    <app-file-upload *ngIf=\"!dynamicForm?.additionalSettings?.hideAttachments\"\r\n      fileUploadContainerClass=\"embedded-upload-form\" (fileUploadEvent)=\"onFileUploadEvent($event)\"\r\n      [projectId]=\"projectId\" [versionId]=\"projectVersionId\" [dataStoreName]=\"datastore.name\" [recordId]=\"recordId\"\r\n      [fileService]=\"attachmentService\" [aliasIds]=\"data?.__sbmeta?.Attachments\" [isPreview]=\"isPreview\"\r\n      class=\"form-viewer-file-upload\">\r\n    </app-file-upload>\r\n    <div *ngIf=\"!recordId && dynamicForm?.additionalSettings?.allowEmail\" class=\"mt-3\">\r\n      <p-checkbox label=\"Send me a copy of my responses\" [(ngModel)]=\"emailOnSubmit\" [binary]=\"true\"\r\n        [disabled]=\"isPreview\"></p-checkbox>\r\n    </div>\r\n  </div>\r\n  <div class=\"fixed bottom-0 right-0 mr-4 mb-2\" *ngIf=\"showSubmitButton\">\r\n    <p-button pRipple [icon]=\"submitBtnIcon ?? 'pi pi-send'\" type=\"button\" label=\"Submit\"\r\n      (onClick)=\"submitForm()\"></p-button>\r\n  </div>\r\n</div>\r\n<p-blockUI *ngIf=\"showSpinner\" [blocked]=\"showSpinner\" styleClass=\"z-5\">\r\n  <img class=\"ui-progress-spinner\" [src]=\"'layout/images/salt-box-loading.gif' | assetUrl\">\r\n</p-blockUI>"], "mappings": ";AAAA,SAAmCA,YAAY,QAAqD,eAAe;AAMnH,SAASC,yBAAyB,QAAQ,yDAAyD;AAEnG,SAASC,IAAI,QAAsB,MAAM;AACzC,SAASC,oBAAoB,QAAQ,2BAA2B;AAEhE,SAASC,cAAc,QAAQ,uCAAuC;AAEtE,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,mBAAmB,QAAQ,gDAAgD;AACpF,SAASC,IAAI,QAAQ,iBAAiB;AACtC,SAASV,yBAAyB,IAAIW,2BAA2B,QAAQ,uDAAuD;;;;;;;;;;;;ICX5HC,EAAA,CAAAC,cAAA,yBAIkC;IAHgBD,EAAA,CAAAE,UAAA,6BAAAC,qGAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAmBF,MAAA,CAAAG,iBAAA,CAAAN,MAAA,CAAyB;IAAA,EAAC;IAI/FJ,EAAA,CAAAW,YAAA,EAAkB;;;;IAF2DX,EAD3E,CAAAY,UAAA,cAAAL,MAAA,CAAAM,SAAA,CAAuB,cAAAN,MAAA,CAAAO,gBAAA,CAA+B,kBAAAP,MAAA,CAAAQ,SAAA,CAAAC,IAAA,CAAiC,aAAAT,MAAA,CAAAU,QAAA,CAAsB,gBAAAV,MAAA,CAAAW,iBAAA,CAC5E,aAAAX,MAAA,CAAAY,IAAA,kBAAAZ,MAAA,CAAAY,IAAA,CAAAC,QAAA,kBAAAb,MAAA,CAAAY,IAAA,CAAAC,QAAA,CAAAC,WAAA,CAAyC,cAAAd,MAAA,CAAAe,SAAA,CAAwB;;;;;;IAIlGtB,EADF,CAAAC,cAAA,cAAmF,qBAExD;IAD0BD,EAAA,CAAAuB,gBAAA,2BAAAC,kFAAApB,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAA0B,kBAAA,CAAAnB,MAAA,CAAAoB,aAAA,EAAAvB,MAAA,MAAAG,MAAA,CAAAoB,aAAA,GAAAvB,MAAA;MAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;IAAA,EAA2B;IAEhFJ,EAD2B,CAAAW,YAAA,EAAa,EAClC;;;;IAF+CX,EAAA,CAAA4B,SAAA,EAA2B;IAA3B5B,EAAA,CAAA6B,gBAAA,YAAAtB,MAAA,CAAAoB,aAAA,CAA2B;IAC5E3B,EAD6E,CAAAY,UAAA,gBAAe,aAAAL,MAAA,CAAAe,SAAA,CACtE;;;;;IAT5BtB,EAAA,CAAAC,cAAA,aAAiD;IAO/CD,EANA,CAAA8B,UAAA,IAAAC,yDAAA,6BAIkC,IAAAC,6CAAA,iBAEiD;IAIrFhC,EAAA,CAAAW,YAAA,EAAM;;;;IAVcX,EAAA,CAAA4B,SAAA,EAAuD;IAAvD5B,EAAA,CAAAY,UAAA,WAAAL,MAAA,CAAA0B,WAAA,kBAAA1B,MAAA,CAAA0B,WAAA,CAAAC,kBAAA,kBAAA3B,MAAA,CAAA0B,WAAA,CAAAC,kBAAA,CAAAC,eAAA,EAAuD;IAMnEnC,EAAA,CAAA4B,SAAA,EAA8D;IAA9D5B,EAAA,CAAAY,UAAA,UAAAL,MAAA,CAAAU,QAAA,KAAAV,MAAA,CAAA0B,WAAA,kBAAA1B,MAAA,CAAA0B,WAAA,CAAAC,kBAAA,kBAAA3B,MAAA,CAAA0B,WAAA,CAAAC,kBAAA,CAAAE,UAAA,EAA8D;;;;;;IAMpEpC,EADF,CAAAC,cAAA,cAAuE,mBAE1C;IAAzBD,EAAA,CAAAE,UAAA,qBAAAmC,oEAAA;MAAArC,EAAA,CAAAK,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAWF,MAAA,CAAAgC,UAAA,EAAY;IAAA,EAAC;IAC5BvC,EAD6B,CAAAW,YAAA,EAAW,EAClC;;;;;IAFcX,EAAA,CAAA4B,SAAA,EAAsC;IAAtC5B,EAAA,CAAAY,UAAA,UAAA4B,OAAA,GAAAjC,MAAA,CAAAkC,aAAA,cAAAD,OAAA,KAAAE,SAAA,GAAAF,OAAA,gBAAsC;;;;;IAI5DxC,EAAA,CAAAC,cAAA,oBAAwE;IACtED,EAAA,CAAA2C,SAAA,cAAyF;;IAC3F3C,EAAA,CAAAW,YAAA,EAAY;;;;IAFmBX,EAAA,CAAAY,UAAA,YAAAL,MAAA,CAAAqC,WAAA,CAAuB;IACnB5C,EAAA,CAAA4B,SAAA,EAAuD;IAAvD5B,EAAA,CAAAY,UAAA,QAAAZ,EAAA,CAAA6C,WAAA,8CAAA7C,EAAA,CAAA8C,aAAA,CAAuD;;;ADE1F,OAAM,MAAOC,wBAAwB;EAoCnCC,YACS9B,iBAAoC,EACnC+B,mBAAwC,EACxCC,mBAAwC;IAFzC,KAAAhC,iBAAiB,GAAjBA,iBAAiB;IAChB,KAAA+B,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IArCpB,KAAA5B,SAAS,GAAG,KAAK;IAUjB,KAAA6B,eAAe,GAAG,IAAI;IACtB,KAAAC,gBAAgB,GAAG,IAAI;IACvB,KAAAC,mBAAmB,GAAG/D,oBAAoB,CAACgE,aAAa;IAGjE;IACA;IACS,KAAAC,aAAa,GAAG,KAAK;IAIb,KAAAC,YAAY,GAAG,IAAIrE,YAAY,EAAO,CAAC,CAAC;IAKzD,KAAAwC,aAAa,GAAG,KAAK;IAErB,KAAA8B,qBAAqB,GAAG,IAAItE,YAAY,EAAO;IAE/C,KAAAyD,WAAW,GAAG,KAAK;EAOmC;EAEtDc,eAAeA,CAAA;IAAA,IAAAC,KAAA;IACb,IAAI,CAACC,8BAA8B,GAAG,IAAI,CAACV,mBAAmB,CAACW,kBAAkB,CAACC,SAAS,eAAAC,iBAAA,CAAC,aAAW;MACrGJ,KAAI,CAACK,gBAAgB,EAAE;MACvBL,KAAI,CAACF,qBAAqB,CAACQ,IAAI,CAACN,KAAI,CAACxC,IAAI,CAAC;IAC5C,CAAC,EAAC;IAEF,IAAI,CAAC,IAAI,CAACF,QAAQ,IAAI,IAAI,CAACoC,mBAAmB,KAAK/D,oBAAoB,CAAC4E,IAAI,EAC1E,IAAI,CAACF,gBAAgB,EAAE;EAC3B;EAEAG,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,EAAE;IAEtB,IAAI,IAAI,CAAC9C,SAAS,EAChB;IAEF;IACA,IAAI,CAAC+C,qBAAqB,GAAG,IAAI,CAACC,gBAAgB,EAAER,SAAS,CAAC,MAAK;MACjE,IAAI,CAACvB,UAAU,EAAE;IACnB,CAAC,CAAC;EACJ;EAEA6B,eAAeA,CAAA;IACb,MAAMG,YAAY,GAAG,IAAI,CAACrB,mBAAmB,CAACsB,eAAe,CAAC,IAAI,CAACvC,WAAW,CAAC;IAE/E,IAAI,CAACsC,YAAY,EAAE;MACjB,IAAI,CAACtB,mBAAmB,CAACwB,SAAS,CAAC,qBAAqB,EAAE,8BAA8B,CAAC;MACzF,IAAI,CAACF,YAAY,GAAG,EAAE;MACtB;IACF;IAEA;IACA,IAAIG,iBAAiB,GAAmB,IAAI;IAE5C,IAAI,IAAI,CAACrB,mBAAmB,KAAK/D,oBAAoB,CAAC4E,IAAI,EACxDQ,iBAAiB,GAAG,KAAK,CAAC,CAAC;IAAA,KACxB,IAAI,CAAC,IAAI,CAACzD,QAAQ,IAAI,CAAC,IAAI,CAACK,SAAS,EACxCoD,iBAAiB,GAAG,IAAI,CAAC,CAAC;IAE5B,IAAIA,iBAAiB,IAAI,IAAI,EAC3BH,YAAY,CAACI,OAAO,CAACC,KAAK,IAAG;MAC3BA,KAAK,CAACC,KAAK,CAACC,QAAQ,GAAG,CAACJ,iBAAiB;MACzCE,KAAK,CAACC,KAAK,CAACE,QAAQ,GAAG,CAACL,iBAAiB;IAC3C,CAAC,CAAC;IAEJ,IAAI,CAACH,YAAY,GAAGA,YAAY;EAClC;EAEAP,gBAAgBA,CAAA;IACd,IAAI,CAACpB,WAAW,GAAG,IAAI;IAEvB,MAAMoC,EAAE,GAAG,IAAI,CAAC7D,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC5B,cAAc,CAAC,GAAGmD,SAAS;IAC5DrD,IAAI,CAAC,IAAI,CAAC6D,mBAAmB,CAAC+B,gBAAgB,CAAC,IAAI,CAACV,YAAY,EAAE,IAAI,CAACW,MAAM,IAAI,IAAI,CAACnE,SAAS,EAAEoE,UAAU,CAAC,CAAC,CAC1GrB,SAAS,CAAC;MACTG,IAAI,EAAGmB,OAAO,IAAI;QAChB;QACA;QACA;QACA;QAEA,IAAI,CAAC,IAAI,CAACjE,IAAI,EACZ,IAAI,CAACA,IAAI,GAAG,EAAE;QAEhB;QACAkE,MAAM,CAACC,IAAI,CAAC,IAAI,CAACnE,IAAI,CAAC,CAACwD,OAAO,CAACY,GAAG,IAAG;UACnC,IAAI,CAACpE,IAAI,CAACoE,GAAG,CAAC,GAAG7C,SAAS;QAC5B,CAAC,CAAC;QAEF;QACA2C,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACT,OAAO,CAACY,GAAG,IAAG;UACjC,IAAI,CAACpE,IAAI,CAACoE,GAAG,CAAC,GAAGH,OAAO,CAACG,GAAG,CAAC;QAC/B,CAAC,CAAC;QACF,IAAI,CAACpE,IAAI,CAAC5B,cAAc,CAAC,GAAGyF,EAAE;QAE9B,IAAI,CAACQ,cAAc,CAACC,YAAY,CAAC,IAAI,CAACtE,IAAI,CAAC;MAC7C,CAAC;MACDuE,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACzC,mBAAmB,CAACwB,SAAS,CAAC,uBAAuB,EAAEiB,KAAK,EAAEA,KAAK,IAAIA,KAAK,EAAEC,OAAO,CAAC;QAC3F,IAAI,CAAC/C,WAAW,GAAG,KAAK;MAC1B,CAAC;MACDgD,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAChD,WAAW,GAAG,KAAK;MAC1B;KACD,CAAC;EACN;EAEAiD,WAAWA,CAAA;IACT,IAAI,IAAI,CAACjC,8BAA8B,EAAE;MACvC,IAAI,CAACA,8BAA8B,CAACkC,WAAW,EAAE;IACnD;IAEA,IAAI,IAAI,CAACzB,qBAAqB,EAAE;MAC9B,IAAI,CAACA,qBAAqB,CAACyB,WAAW,EAAE;IAC1C;EACF;EAEApF,iBAAiBA,CAACqF,QAAkB;IAClC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B;EAEAxD,UAAUA,CAAA;IACR,MAAMyD,QAAQ,GAAG,IAAI,CAACR,cAAc,CAACS,YAAY,EAAE;IACnD,IAAI,CAACD,QAAQ,CAACE,OAAO,EAAE;MACrB,IAAI,CAACjD,mBAAmB,CAACwB,SAAS,CAAC,mBAAmB,EAAEuB,QAAQ,CAACG,YAAY,IAAI,mCAAmC,CAAC;MACrH;IACF;IAEA;IACA,IAAI,CAAC3C,YAAY,CAAC4C,IAAI,CAAC;MAAEjF,IAAI,EAAE,IAAI,CAACkF,WAAW,IAAI,IAAI,CAAClF,IAAI;MAAE4E,QAAQ,EAAE,IAAI,CAACA,QAAQ,IAAI,IAAI,CAAC5E,IAAI,EAAEC,QAAQ,EAAEC,WAAW;MAAEiF,SAAS,EAAE,IAAI,CAAC3E;IAAa,CAAE,CAAC;EAC7J;EAEA4E,UAAUA,CAACpF,IAAS;IAClB,IAAI,CAACkF,WAAW,GAAGlF,IAAI;EACzB;EAEAqF,yBAAyBA,CAACC,MAAa;IACrC,IAAI,CAACA,MAAM,GAAGA,MAAM;EACtB;;;uBA9JW1D,wBAAwB,EAAA/C,EAAA,CAAA0G,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAA5G,EAAA,CAAA0G,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAA9G,EAAA,CAAA0G,iBAAA,CAAAK,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAAxBjE,wBAAwB;MAAAkE,SAAA;MAAAC,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAwBxBhI,yBAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UClDlCY,EAFJ,CAAAC,cAAA,aAAkC,aACG,gCAKS;UAHFD,EAAA,CAAAuB,gBAAA,yBAAA+F,gFAAAlH,MAAA;YAAAJ,EAAA,CAAA0B,kBAAA,CAAA2F,GAAA,CAAAlG,IAAA,EAAAf,MAAA,MAAAiH,GAAA,CAAAlG,IAAA,GAAAf,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAgB;UAACJ,EAAA,CAAAE,UAAA,yBAAAoH,gFAAAlH,MAAA;YAAA,OAAeiH,GAAA,CAAAd,UAAA,CAAAnG,MAAA,CAAkB;UAAA,EAAC;UAI7FJ,EAD4C,CAAAW,YAAA,EAAyB,EAC/D;UAaNX,EAZA,CAAA8B,UAAA,IAAAyF,uCAAA,iBAAiD,IAAAC,uCAAA,iBAYsB;UAIzExH,EAAA,CAAAW,YAAA,EAAM;UACNX,EAAA,CAAA8B,UAAA,IAAA2F,6CAAA,uBAAwE;;;;UAvB5CzH,EAAA,CAAA4B,SAAA,GAA6B;UACnD5B,EADsB,CAAAY,UAAA,iBAAAyG,GAAA,CAAA9C,YAAA,CAA6B,gBAAA/B,OAAA,GAAA6E,GAAA,CAAAtG,SAAA,kBAAAsG,GAAA,CAAAtG,SAAA,CAAAoE,UAAA,cAAA3C,OAAA,KAAAE,SAAA,GAAAF,OAAA,GAAA6E,GAAA,CAAAnC,MAAA,CAA+C,qBAAAmC,GAAA,CAAAvG,gBAAA,CAC7D;UAACd,EAAA,CAAA6B,gBAAA,UAAAwF,GAAA,CAAAlG,IAAA,CAAgB;UAGtDnB,EAFA,CAAAY,UAAA,qCAAoC,0BAAAyG,GAAA,CAAA5D,qBAAA,CAAgD,uBAAA4D,GAAA,CAAAK,kBAAA,CAC3C,kBAAAL,GAAA,CAAA9D,aAAA,CAAgC,sBAAA8D,GAAA,CAAAM,iBAAA,CAClC;UAErC3H,EAAA,CAAA4B,SAAA,EAAqB;UAArB5B,EAAA,CAAAY,UAAA,SAAAyG,GAAA,CAAAlE,eAAA,CAAqB;UAYoBnD,EAAA,CAAA4B,SAAA,EAAsB;UAAtB5B,EAAA,CAAAY,UAAA,SAAAyG,GAAA,CAAAjE,gBAAA,CAAsB;UAK3DpD,EAAA,CAAA4B,SAAA,EAAiB;UAAjB5B,EAAA,CAAAY,UAAA,SAAAyG,GAAA,CAAAzE,WAAA,CAAiB;;;qBDCf7C,2BAA2B,EAAED,IAAI,EAAED,mBAAmB,EAAED,cAAc,EAAAgI,EAAA,CAAAC,QAAA,EAAElI,WAAW,EAAAmI,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAEtI,YAAY,EAAAuI,EAAA,CAAAC,MAAA,EAAEzI,aAAa,EAAA0I,EAAA,CAAAC,OAAA,EAAE5I,YAAY;MAAA6I,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}