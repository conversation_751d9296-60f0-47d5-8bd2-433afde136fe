{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst cognitoHostedUIIdentityProviderMap = {\n  Google: 'Google',\n  Facebook: 'Facebook',\n  Amazon: 'LoginWithAmazon',\n  Apple: 'SignInWithApple'\n};\nexport { cognitoHostedUIIdentityProviderMap };", "map": {"version": 3, "names": ["cognitoHostedUIIdentityProviderMap", "Google", "Facebook", "Amazon", "Apple"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/types/models.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst cognitoHostedUIIdentityProviderMap = {\n    Google: 'Google',\n    Facebook: 'Facebook',\n    Amazon: 'LoginWithAmazon',\n    Apple: 'SignInWithApple',\n};\n\nexport { cognitoHostedUIIdentityProviderMap };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,kCAAkC,GAAG;EACvCC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,iBAAiB;EACzBC,KAAK,EAAE;AACX,CAAC;AAED,SAASJ,kCAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}