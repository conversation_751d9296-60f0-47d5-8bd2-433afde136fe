{"ast": null, "code": "import { AmplifyError } from './AmplifyError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createAssertionFunction = (errorMap, AssertionError = AmplifyError) => (assertion, name, additionalContext) => {\n  const {\n    message,\n    recoverySuggestion\n  } = errorMap[name];\n  if (!assertion) {\n    throw new AssertionError({\n      name,\n      message: additionalContext ? `${message} ${additionalContext}` : message,\n      recoverySuggestion\n    });\n  }\n};\nexport { createAssertionFunction };", "map": {"version": 3, "names": ["AmplifyError", "createAssertionFunction", "errorMap", "AssertionError", "assertion", "name", "additionalContext", "message", "recoverySuggestion"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/errors/createAssertionFunction.mjs"], "sourcesContent": ["import { AmplifyError } from './AmplifyError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createAssertionFunction = (errorMap, AssertionError = AmplifyError) => (assertion, name, additionalContext) => {\n    const { message, recoverySuggestion } = errorMap[name];\n    if (!assertion) {\n        throw new AssertionError({\n            name,\n            message: additionalContext\n                ? `${message} ${additionalContext}`\n                : message,\n            recoverySuggestion,\n        });\n    }\n};\n\nexport { createAssertionFunction };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;;AAEjD;AACA;AACA,MAAMC,uBAAuB,GAAGA,CAACC,QAAQ,EAAEC,cAAc,GAAGH,YAAY,KAAK,CAACI,SAAS,EAAEC,IAAI,EAAEC,iBAAiB,KAAK;EACjH,MAAM;IAAEC,OAAO;IAAEC;EAAmB,CAAC,GAAGN,QAAQ,CAACG,IAAI,CAAC;EACtD,IAAI,CAACD,SAAS,EAAE;IACZ,MAAM,IAAID,cAAc,CAAC;MACrBE,IAAI;MACJE,OAAO,EAAED,iBAAiB,GACpB,GAAGC,OAAO,IAAID,iBAAiB,EAAE,GACjCC,OAAO;MACbC;IACJ,CAAC,CAAC;EACN;AACJ,CAAC;AAED,SAASP,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}