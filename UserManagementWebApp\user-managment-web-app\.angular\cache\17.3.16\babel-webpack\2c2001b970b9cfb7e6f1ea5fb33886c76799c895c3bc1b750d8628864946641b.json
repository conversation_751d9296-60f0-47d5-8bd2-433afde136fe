{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport { BLOCK_SIZE } from \"./constants\";\nimport { RawSha256 } from \"./RawSha256\";\nimport { isEmptyData, convertToBuffer } from \"@aws-crypto/util\";\nvar Sha256 = /** @class */function () {\n  function Sha256(secret) {\n    this.secret = secret;\n    this.hash = new RawSha256();\n    this.reset();\n  }\n  Sha256.prototype.update = function (toHash) {\n    if (isEmptyData(toHash) || this.error) {\n      return;\n    }\n    try {\n      this.hash.update(convertToBuffer(toHash));\n    } catch (e) {\n      this.error = e;\n    }\n  };\n  /* This synchronous method keeps compatibility\n   * with the v2 aws-sdk.\n   */\n  Sha256.prototype.digestSync = function () {\n    if (this.error) {\n      throw this.error;\n    }\n    if (this.outer) {\n      if (!this.outer.finished) {\n        this.outer.update(this.hash.digest());\n      }\n      return this.outer.digest();\n    }\n    return this.hash.digest();\n  };\n  /* The underlying digest method here is synchronous.\n   * To keep the same interface with the other hash functions\n   * the default is to expose this as an async method.\n   * However, it can sometimes be useful to have a sync method.\n   */\n  Sha256.prototype.digest = function () {\n    return __awaiter(this, void 0, void 0, function () {\n      return __generator(this, function (_a) {\n        return [2 /*return*/, this.digestSync()];\n      });\n    });\n  };\n  Sha256.prototype.reset = function () {\n    this.hash = new RawSha256();\n    if (this.secret) {\n      this.outer = new RawSha256();\n      var inner = bufferFromSecret(this.secret);\n      var outer = new Uint8Array(BLOCK_SIZE);\n      outer.set(inner);\n      for (var i = 0; i < BLOCK_SIZE; i++) {\n        inner[i] ^= 0x36;\n        outer[i] ^= 0x5c;\n      }\n      this.hash.update(inner);\n      this.outer.update(outer);\n      // overwrite the copied key in memory\n      for (var i = 0; i < inner.byteLength; i++) {\n        inner[i] = 0;\n      }\n    }\n  };\n  return Sha256;\n}();\nexport { Sha256 };\nfunction bufferFromSecret(secret) {\n  var input = convertToBuffer(secret);\n  if (input.byteLength > BLOCK_SIZE) {\n    var bufferHash = new RawSha256();\n    bufferHash.update(input);\n    input = bufferHash.digest();\n  }\n  var buffer = new Uint8Array(BLOCK_SIZE);\n  buffer.set(input);\n  return buffer;\n}", "map": {"version": 3, "names": ["__awaiter", "__generator", "BLOCK_SIZE", "RawSha256", "isEmptyData", "convertToBuffer", "Sha256", "secret", "hash", "reset", "prototype", "update", "toHash", "error", "e", "digestSync", "outer", "finished", "digest", "_a", "inner", "bufferFromSecret", "Uint8Array", "set", "i", "byteLength", "input", "bufferHash", "buffer"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-crypto/sha256-js/build/module/jsSha256.js"], "sourcesContent": ["import { __awaiter, __generator } from \"tslib\";\nimport { BLOCK_SIZE } from \"./constants\";\nimport { RawSha256 } from \"./RawSha256\";\nimport { isEmptyData, convertToBuffer } from \"@aws-crypto/util\";\nvar Sha256 = /** @class */ (function () {\n    function Sha256(secret) {\n        this.secret = secret;\n        this.hash = new RawSha256();\n        this.reset();\n    }\n    Sha256.prototype.update = function (toHash) {\n        if (isEmptyData(toHash) || this.error) {\n            return;\n        }\n        try {\n            this.hash.update(convertToBuffer(toHash));\n        }\n        catch (e) {\n            this.error = e;\n        }\n    };\n    /* This synchronous method keeps compatibility\n     * with the v2 aws-sdk.\n     */\n    Sha256.prototype.digestSync = function () {\n        if (this.error) {\n            throw this.error;\n        }\n        if (this.outer) {\n            if (!this.outer.finished) {\n                this.outer.update(this.hash.digest());\n            }\n            return this.outer.digest();\n        }\n        return this.hash.digest();\n    };\n    /* The underlying digest method here is synchronous.\n     * To keep the same interface with the other hash functions\n     * the default is to expose this as an async method.\n     * However, it can sometimes be useful to have a sync method.\n     */\n    Sha256.prototype.digest = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                return [2 /*return*/, this.digestSync()];\n            });\n        });\n    };\n    Sha256.prototype.reset = function () {\n        this.hash = new RawSha256();\n        if (this.secret) {\n            this.outer = new RawSha256();\n            var inner = bufferFromSecret(this.secret);\n            var outer = new Uint8Array(BLOCK_SIZE);\n            outer.set(inner);\n            for (var i = 0; i < BLOCK_SIZE; i++) {\n                inner[i] ^= 0x36;\n                outer[i] ^= 0x5c;\n            }\n            this.hash.update(inner);\n            this.outer.update(outer);\n            // overwrite the copied key in memory\n            for (var i = 0; i < inner.byteLength; i++) {\n                inner[i] = 0;\n            }\n        }\n    };\n    return Sha256;\n}());\nexport { Sha256 };\nfunction bufferFromSecret(secret) {\n    var input = convertToBuffer(secret);\n    if (input.byteLength > BLOCK_SIZE) {\n        var bufferHash = new RawSha256();\n        bufferHash.update(input);\n        input = bufferHash.digest();\n    }\n    var buffer = new Uint8Array(BLOCK_SIZE);\n    buffer.set(input);\n    return buffer;\n}\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC9C,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAC/D,IAAIC,MAAM,GAAG,aAAe,YAAY;EACpC,SAASA,MAAMA,CAACC,MAAM,EAAE;IACpB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,IAAI,GAAG,IAAIL,SAAS,CAAC,CAAC;IAC3B,IAAI,CAACM,KAAK,CAAC,CAAC;EAChB;EACAH,MAAM,CAACI,SAAS,CAACC,MAAM,GAAG,UAAUC,MAAM,EAAE;IACxC,IAAIR,WAAW,CAACQ,MAAM,CAAC,IAAI,IAAI,CAACC,KAAK,EAAE;MACnC;IACJ;IACA,IAAI;MACA,IAAI,CAACL,IAAI,CAACG,MAAM,CAACN,eAAe,CAACO,MAAM,CAAC,CAAC;IAC7C,CAAC,CACD,OAAOE,CAAC,EAAE;MACN,IAAI,CAACD,KAAK,GAAGC,CAAC;IAClB;EACJ,CAAC;EACD;AACJ;AACA;EACIR,MAAM,CAACI,SAAS,CAACK,UAAU,GAAG,YAAY;IACtC,IAAI,IAAI,CAACF,KAAK,EAAE;MACZ,MAAM,IAAI,CAACA,KAAK;IACpB;IACA,IAAI,IAAI,CAACG,KAAK,EAAE;MACZ,IAAI,CAAC,IAAI,CAACA,KAAK,CAACC,QAAQ,EAAE;QACtB,IAAI,CAACD,KAAK,CAACL,MAAM,CAAC,IAAI,CAACH,IAAI,CAACU,MAAM,CAAC,CAAC,CAAC;MACzC;MACA,OAAO,IAAI,CAACF,KAAK,CAACE,MAAM,CAAC,CAAC;IAC9B;IACA,OAAO,IAAI,CAACV,IAAI,CAACU,MAAM,CAAC,CAAC;EAC7B,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIZ,MAAM,CAACI,SAAS,CAACQ,MAAM,GAAG,YAAY;IAClC,OAAOlB,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,OAAOC,WAAW,CAAC,IAAI,EAAE,UAAUkB,EAAE,EAAE;QACnC,OAAO,CAAC,CAAC,CAAC,YAAY,IAAI,CAACJ,UAAU,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACDT,MAAM,CAACI,SAAS,CAACD,KAAK,GAAG,YAAY;IACjC,IAAI,CAACD,IAAI,GAAG,IAAIL,SAAS,CAAC,CAAC;IAC3B,IAAI,IAAI,CAACI,MAAM,EAAE;MACb,IAAI,CAACS,KAAK,GAAG,IAAIb,SAAS,CAAC,CAAC;MAC5B,IAAIiB,KAAK,GAAGC,gBAAgB,CAAC,IAAI,CAACd,MAAM,CAAC;MACzC,IAAIS,KAAK,GAAG,IAAIM,UAAU,CAACpB,UAAU,CAAC;MACtCc,KAAK,CAACO,GAAG,CAACH,KAAK,CAAC;MAChB,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,UAAU,EAAEsB,CAAC,EAAE,EAAE;QACjCJ,KAAK,CAACI,CAAC,CAAC,IAAI,IAAI;QAChBR,KAAK,CAACQ,CAAC,CAAC,IAAI,IAAI;MACpB;MACA,IAAI,CAAChB,IAAI,CAACG,MAAM,CAACS,KAAK,CAAC;MACvB,IAAI,CAACJ,KAAK,CAACL,MAAM,CAACK,KAAK,CAAC;MACxB;MACA,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACK,UAAU,EAAED,CAAC,EAAE,EAAE;QACvCJ,KAAK,CAACI,CAAC,CAAC,GAAG,CAAC;MAChB;IACJ;EACJ,CAAC;EACD,OAAOlB,MAAM;AACjB,CAAC,CAAC,CAAE;AACJ,SAASA,MAAM;AACf,SAASe,gBAAgBA,CAACd,MAAM,EAAE;EAC9B,IAAImB,KAAK,GAAGrB,eAAe,CAACE,MAAM,CAAC;EACnC,IAAImB,KAAK,CAACD,UAAU,GAAGvB,UAAU,EAAE;IAC/B,IAAIyB,UAAU,GAAG,IAAIxB,SAAS,CAAC,CAAC;IAChCwB,UAAU,CAAChB,MAAM,CAACe,KAAK,CAAC;IACxBA,KAAK,GAAGC,UAAU,CAACT,MAAM,CAAC,CAAC;EAC/B;EACA,IAAIU,MAAM,GAAG,IAAIN,UAAU,CAACpB,UAAU,CAAC;EACvC0B,MAAM,CAACL,GAAG,CAACG,KAAK,CAAC;EACjB,OAAOE,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}