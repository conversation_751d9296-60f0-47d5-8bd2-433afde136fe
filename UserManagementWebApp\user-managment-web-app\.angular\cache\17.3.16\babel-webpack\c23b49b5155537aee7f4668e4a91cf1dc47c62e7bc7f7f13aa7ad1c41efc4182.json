{"ast": null, "code": "import { AiAction, Category, AuthAction, StorageAction, InAppMessagingAction, GeoAction } from '@aws-amplify/core/internals/utils';\nconst AI_INPUT_BASE = {\n  category: Category.AI,\n  apis: [AiAction.CreateConversation, AiAction.DeleteConversation, AiAction.ListConversations, AiAction.UpdateConversation, AiAction.OnMessage, AiAction.SendMessage, AiAction.Generation]\n};\nconst ACCOUNT_SETTINGS_INPUT_BASE = {\n  apis: [AuthAction.DeleteUser, AuthAction.UpdatePassword],\n  category: Category.Auth\n};\nconst AUTHENTICATOR_INPUT_BASE = {\n  apis: [AuthAction.SignUp, AuthAction.ConfirmSignUp, AuthAction.ResendSignUpCode, AuthAction.SignIn, AuthAction.ConfirmSignIn, AuthAction.FetchUserAttributes, AuthAction.SignOut, AuthAction.ResetPassword, AuthAction.ConfirmResetPassword, AuthAction.SignInWithRedirect],\n  category: Category.Auth\n};\nconst FILE_UPLOADER_BASE_INPUT = {\n  apis: [StorageAction.UploadData],\n  category: Category.Storage\n};\nconst IN_APP_MESSAGING_INPUT_BASE = {\n  apis: [InAppMessagingAction.NotifyMessageInteraction],\n  category: Category.InAppMessaging\n};\nconst LOCATION_SEARCH_INPUT_BASE = {\n  category: Category.Geo,\n  apis: [GeoAction.SearchByText, GeoAction.SearchForSuggestions, GeoAction.SearchByPlaceId]\n};\nconst MAP_VIEW_INPUT_BASE = {\n  category: Category.Geo,\n  apis: []\n};\nconst STORAGE_MANAGER_INPUT_BASE = {\n  apis: [StorageAction.UploadData],\n  category: Category.Storage\n};\nconst STORAGE_BROWSER_INPUT_BASE = {\n  apis: [StorageAction.UploadData, StorageAction.Copy, StorageAction.GetUrl, StorageAction.List, StorageAction.Remove, StorageAction.GetDataAccess, StorageAction.ListCallerAccessGrants],\n  category: Category.Storage\n};\nexport { ACCOUNT_SETTINGS_INPUT_BASE, AI_INPUT_BASE, AUTHENTICATOR_INPUT_BASE, FILE_UPLOADER_BASE_INPUT, IN_APP_MESSAGING_INPUT_BASE, LOCATION_SEARCH_INPUT_BASE, MAP_VIEW_INPUT_BASE, STORAGE_BROWSER_INPUT_BASE, STORAGE_MANAGER_INPUT_BASE };", "map": {"version": 3, "names": ["AiAction", "Category", "AuthAction", "StorageAction", "InAppMessagingAction", "GeoAction", "AI_INPUT_BASE", "category", "AI", "apis", "CreateConversation", "DeleteConversation", "ListConversations", "UpdateConversation", "OnMessage", "SendMessage", "Generation", "ACCOUNT_SETTINGS_INPUT_BASE", "DeleteUser", "UpdatePassword", "<PERSON><PERSON>", "AUTHENTICATOR_INPUT_BASE", "SignUp", "ConfirmSignUp", "ResendSignUpCode", "SignIn", "ConfirmSignIn", "FetchUserAttributes", "SignOut", "ResetPassword", "ConfirmResetPassword", "SignInWithRedirect", "FILE_UPLOADER_BASE_INPUT", "UploadData", "Storage", "IN_APP_MESSAGING_INPUT_BASE", "NotifyMessageInteraction", "InAppMessaging", "LOCATION_SEARCH_INPUT_BASE", "Geo", "SearchByText", "SearchForSuggestions", "SearchByPlaceId", "MAP_VIEW_INPUT_BASE", "STORAGE_MANAGER_INPUT_BASE", "STORAGE_BROWSER_INPUT_BASE", "Copy", "GetUrl", "List", "Remove", "GetDataAccess", "ListCallerAccessGrants"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/utils/setUserAgent/constants.mjs"], "sourcesContent": ["import { AiAction, Category, AuthAction, StorageAction, InAppMessagingAction, GeoAction } from '@aws-amplify/core/internals/utils';\n\nconst AI_INPUT_BASE = {\n    category: Category.AI,\n    apis: [\n        AiAction.CreateConversation,\n        AiAction.DeleteConversation,\n        AiAction.ListConversations,\n        AiAction.UpdateConversation,\n        AiAction.OnMessage,\n        AiAction.SendMessage,\n        AiAction.Generation,\n    ],\n};\nconst ACCOUNT_SETTINGS_INPUT_BASE = {\n    apis: [AuthAction.DeleteUser, AuthAction.UpdatePassword],\n    category: Category.Auth,\n};\nconst AUTHENTICATOR_INPUT_BASE = {\n    apis: [\n        AuthAction.SignUp,\n        AuthAction.ConfirmSignUp,\n        AuthAction.ResendSignUpCode,\n        AuthAction.SignIn,\n        AuthAction.ConfirmSignIn,\n        AuthAction.FetchUserAttributes,\n        AuthAction.SignOut,\n        AuthAction.ResetPassword,\n        AuthAction.ConfirmResetPassword,\n        AuthAction.SignInWithRedirect,\n    ],\n    category: Category.Auth,\n};\nconst FILE_UPLOADER_BASE_INPUT = {\n    apis: [StorageAction.UploadData],\n    category: Category.Storage,\n};\nconst IN_APP_MESSAGING_INPUT_BASE = {\n    apis: [InAppMessagingAction.NotifyMessageInteraction],\n    category: Category.InAppMessaging,\n};\nconst LOCATION_SEARCH_INPUT_BASE = {\n    category: Category.Geo,\n    apis: [\n        GeoAction.SearchByText,\n        GeoAction.SearchForSuggestions,\n        GeoAction.SearchByPlaceId,\n    ],\n};\nconst MAP_VIEW_INPUT_BASE = {\n    category: Category.Geo,\n    apis: [],\n};\nconst STORAGE_MANAGER_INPUT_BASE = {\n    apis: [StorageAction.UploadData],\n    category: Category.Storage,\n};\nconst STORAGE_BROWSER_INPUT_BASE = {\n    apis: [\n        StorageAction.UploadData,\n        StorageAction.Copy,\n        StorageAction.GetUrl,\n        StorageAction.List,\n        StorageAction.Remove,\n        StorageAction.GetDataAccess,\n        StorageAction.ListCallerAccessGrants,\n    ],\n    category: Category.Storage,\n};\n\nexport { ACCOUNT_SETTINGS_INPUT_BASE, AI_INPUT_BASE, AUTHENTICATOR_INPUT_BASE, FILE_UPLOADER_BASE_INPUT, IN_APP_MESSAGING_INPUT_BASE, LOCATION_SEARCH_INPUT_BASE, MAP_VIEW_INPUT_BASE, STORAGE_BROWSER_INPUT_BASE, STORAGE_MANAGER_INPUT_BASE };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,aAAa,EAAEC,oBAAoB,EAAEC,SAAS,QAAQ,mCAAmC;AAElI,MAAMC,aAAa,GAAG;EAClBC,QAAQ,EAAEN,QAAQ,CAACO,EAAE;EACrBC,IAAI,EAAE,CACFT,QAAQ,CAACU,kBAAkB,EAC3BV,QAAQ,CAACW,kBAAkB,EAC3BX,QAAQ,CAACY,iBAAiB,EAC1BZ,QAAQ,CAACa,kBAAkB,EAC3Bb,QAAQ,CAACc,SAAS,EAClBd,QAAQ,CAACe,WAAW,EACpBf,QAAQ,CAACgB,UAAU;AAE3B,CAAC;AACD,MAAMC,2BAA2B,GAAG;EAChCR,IAAI,EAAE,CAACP,UAAU,CAACgB,UAAU,EAAEhB,UAAU,CAACiB,cAAc,CAAC;EACxDZ,QAAQ,EAAEN,QAAQ,CAACmB;AACvB,CAAC;AACD,MAAMC,wBAAwB,GAAG;EAC7BZ,IAAI,EAAE,CACFP,UAAU,CAACoB,MAAM,EACjBpB,UAAU,CAACqB,aAAa,EACxBrB,UAAU,CAACsB,gBAAgB,EAC3BtB,UAAU,CAACuB,MAAM,EACjBvB,UAAU,CAACwB,aAAa,EACxBxB,UAAU,CAACyB,mBAAmB,EAC9BzB,UAAU,CAAC0B,OAAO,EAClB1B,UAAU,CAAC2B,aAAa,EACxB3B,UAAU,CAAC4B,oBAAoB,EAC/B5B,UAAU,CAAC6B,kBAAkB,CAChC;EACDxB,QAAQ,EAAEN,QAAQ,CAACmB;AACvB,CAAC;AACD,MAAMY,wBAAwB,GAAG;EAC7BvB,IAAI,EAAE,CAACN,aAAa,CAAC8B,UAAU,CAAC;EAChC1B,QAAQ,EAAEN,QAAQ,CAACiC;AACvB,CAAC;AACD,MAAMC,2BAA2B,GAAG;EAChC1B,IAAI,EAAE,CAACL,oBAAoB,CAACgC,wBAAwB,CAAC;EACrD7B,QAAQ,EAAEN,QAAQ,CAACoC;AACvB,CAAC;AACD,MAAMC,0BAA0B,GAAG;EAC/B/B,QAAQ,EAAEN,QAAQ,CAACsC,GAAG;EACtB9B,IAAI,EAAE,CACFJ,SAAS,CAACmC,YAAY,EACtBnC,SAAS,CAACoC,oBAAoB,EAC9BpC,SAAS,CAACqC,eAAe;AAEjC,CAAC;AACD,MAAMC,mBAAmB,GAAG;EACxBpC,QAAQ,EAAEN,QAAQ,CAACsC,GAAG;EACtB9B,IAAI,EAAE;AACV,CAAC;AACD,MAAMmC,0BAA0B,GAAG;EAC/BnC,IAAI,EAAE,CAACN,aAAa,CAAC8B,UAAU,CAAC;EAChC1B,QAAQ,EAAEN,QAAQ,CAACiC;AACvB,CAAC;AACD,MAAMW,0BAA0B,GAAG;EAC/BpC,IAAI,EAAE,CACFN,aAAa,CAAC8B,UAAU,EACxB9B,aAAa,CAAC2C,IAAI,EAClB3C,aAAa,CAAC4C,MAAM,EACpB5C,aAAa,CAAC6C,IAAI,EAClB7C,aAAa,CAAC8C,MAAM,EACpB9C,aAAa,CAAC+C,aAAa,EAC3B/C,aAAa,CAACgD,sBAAsB,CACvC;EACD5C,QAAQ,EAAEN,QAAQ,CAACiC;AACvB,CAAC;AAED,SAASjB,2BAA2B,EAAEX,aAAa,EAAEe,wBAAwB,EAAEW,wBAAwB,EAAEG,2BAA2B,EAAEG,0BAA0B,EAAEK,mBAAmB,EAAEE,0BAA0B,EAAED,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}