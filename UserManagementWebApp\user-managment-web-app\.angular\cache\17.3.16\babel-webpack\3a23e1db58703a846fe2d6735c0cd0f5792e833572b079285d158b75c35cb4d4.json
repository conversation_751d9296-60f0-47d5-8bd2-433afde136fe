{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { authenticated<PERSON>andler } from '../../clients/handlers/aws/authenticated.mjs';\nimport { composeServiceApi } from '../../clients/internal/composeServiceApi.mjs';\nimport { extendedEncodeURIComponent } from '../../clients/middleware/signing/utils/extendedEncodeURIComponent.mjs';\nimport { parseMetadata } from '../../clients/serde/responseInfo.mjs';\nimport { parseJsonError, parseJsonBody } from '../../clients/serde/json.mjs';\nimport { AmplifyUrl } from '../../utils/amplifyUrl/index.mjs';\nimport { defaultConfig, getSharedHeaders } from './base.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst updateEndpointSerializer = ({\n  ApplicationId = '',\n  EndpointId = '',\n  EndpointRequest\n}, endpoint) => {\n  const headers = getSharedHeaders();\n  const url = new AmplifyUrl(endpoint.url);\n  url.pathname = `v1/apps/${extendedEncodeURIComponent(ApplicationId)}/endpoints/${extendedEncodeURIComponent(EndpointId)}`;\n  const body = JSON.stringify(EndpointRequest);\n  return {\n    method: 'PUT',\n    headers,\n    url,\n    body\n  };\n};\nconst updateEndpointDeserializer = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (response) {\n    if (response.statusCode >= 300) {\n      const error = yield parseJsonError(response);\n      throw error;\n    } else {\n      const {\n        Message,\n        RequestID\n      } = yield parseJsonBody(response);\n      return {\n        MessageBody: {\n          Message,\n          RequestID\n        },\n        $metadata: parseMetadata(response)\n      };\n    }\n  });\n  return function updateEndpointDeserializer(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\n/**\n * @internal\n */\nconst updateEndpoint = composeServiceApi(authenticatedHandler, updateEndpointSerializer, updateEndpointDeserializer, defaultConfig);\nexport { updateEndpoint };", "map": {"version": 3, "names": ["authenticated<PERSON><PERSON><PERSON>", "composeServiceApi", "extendedEncodeURIComponent", "parseMetadata", "parseJsonError", "parseJsonBody", "AmplifyUrl", "defaultConfig", "getSharedHeaders", "updateEndpointSerializer", "ApplicationId", "EndpointId", "EndpointRequest", "endpoint", "headers", "url", "pathname", "body", "JSON", "stringify", "method", "updateEndpointDeserializer", "_ref", "_asyncToGenerator", "response", "statusCode", "error", "Message", "RequestID", "MessageBody", "$metadata", "_x", "apply", "arguments", "updateEndpoint"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/awsClients/pinpoint/updateEndpoint.mjs"], "sourcesContent": ["import { authenticated<PERSON><PERSON><PERSON> } from '../../clients/handlers/aws/authenticated.mjs';\nimport { composeServiceApi } from '../../clients/internal/composeServiceApi.mjs';\nimport { extendedEncodeURIComponent } from '../../clients/middleware/signing/utils/extendedEncodeURIComponent.mjs';\nimport { parseMetadata } from '../../clients/serde/responseInfo.mjs';\nimport { parseJsonError, parseJsonBody } from '../../clients/serde/json.mjs';\nimport { AmplifyUrl } from '../../utils/amplifyUrl/index.mjs';\nimport { defaultConfig, getSharedHeaders } from './base.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst updateEndpointSerializer = ({ ApplicationId = '', EndpointId = '', EndpointRequest }, endpoint) => {\n    const headers = getSharedHeaders();\n    const url = new AmplifyUrl(endpoint.url);\n    url.pathname = `v1/apps/${extendedEncodeURIComponent(ApplicationId)}/endpoints/${extendedEncodeURIComponent(EndpointId)}`;\n    const body = JSON.stringify(EndpointRequest);\n    return { method: 'PUT', headers, url, body };\n};\nconst updateEndpointDeserializer = async (response) => {\n    if (response.statusCode >= 300) {\n        const error = await parseJsonError(response);\n        throw error;\n    }\n    else {\n        const { Message, RequestID } = await parseJsonBody(response);\n        return {\n            MessageBody: {\n                Message,\n                RequestID,\n            },\n            $metadata: parseMetadata(response),\n        };\n    }\n};\n/**\n * @internal\n */\nconst updateEndpoint = composeServiceApi(authenticatedHandler, updateEndpointSerializer, updateEndpointDeserializer, defaultConfig);\n\nexport { updateEndpoint };\n"], "mappings": ";AAAA,SAASA,oBAAoB,QAAQ,8CAA8C;AACnF,SAASC,iBAAiB,QAAQ,8CAA8C;AAChF,SAASC,0BAA0B,QAAQ,uEAAuE;AAClH,SAASC,aAAa,QAAQ,sCAAsC;AACpE,SAASC,cAAc,EAAEC,aAAa,QAAQ,8BAA8B;AAC5E,SAASC,UAAU,QAAQ,kCAAkC;AAC7D,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,YAAY;;AAE5D;AACA;AACA,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,aAAa,GAAG,EAAE;EAAEC,UAAU,GAAG,EAAE;EAAEC;AAAgB,CAAC,EAAEC,QAAQ,KAAK;EACrG,MAAMC,OAAO,GAAGN,gBAAgB,CAAC,CAAC;EAClC,MAAMO,GAAG,GAAG,IAAIT,UAAU,CAACO,QAAQ,CAACE,GAAG,CAAC;EACxCA,GAAG,CAACC,QAAQ,GAAG,WAAWd,0BAA0B,CAACQ,aAAa,CAAC,cAAcR,0BAA0B,CAACS,UAAU,CAAC,EAAE;EACzH,MAAMM,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACP,eAAe,CAAC;EAC5C,OAAO;IAAEQ,MAAM,EAAE,KAAK;IAAEN,OAAO;IAAEC,GAAG;IAAEE;EAAK,CAAC;AAChD,CAAC;AACD,MAAMI,0BAA0B;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,QAAQ,EAAK;IACnD,IAAIA,QAAQ,CAACC,UAAU,IAAI,GAAG,EAAE;MAC5B,MAAMC,KAAK,SAAStB,cAAc,CAACoB,QAAQ,CAAC;MAC5C,MAAME,KAAK;IACf,CAAC,MACI;MACD,MAAM;QAAEC,OAAO;QAAEC;MAAU,CAAC,SAASvB,aAAa,CAACmB,QAAQ,CAAC;MAC5D,OAAO;QACHK,WAAW,EAAE;UACTF,OAAO;UACPC;QACJ,CAAC;QACDE,SAAS,EAAE3B,aAAa,CAACqB,QAAQ;MACrC,CAAC;IACL;EACJ,CAAC;EAAA,gBAfKH,0BAA0BA,CAAAU,EAAA;IAAA,OAAAT,IAAA,CAAAU,KAAA,OAAAC,SAAA;EAAA;AAAA,GAe/B;AACD;AACA;AACA;AACA,MAAMC,cAAc,GAAGjC,iBAAiB,CAACD,oBAAoB,EAAES,wBAAwB,EAAEY,0BAA0B,EAAEd,aAAa,CAAC;AAEnI,SAAS2B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}