{"ast": null, "code": "import inspect from \"../jsutils/inspect.mjs\";\nimport isAsyncIterable from \"../jsutils/isAsyncIterable.mjs\";\nimport { addPath, pathToArray } from \"../jsutils/Path.mjs\";\nimport { GraphQLError } from \"../error/GraphQLError.mjs\";\nimport { locatedError } from \"../error/locatedError.mjs\";\nimport { getArgumentValues } from \"../execution/values.mjs\";\nimport { assertValidExecutionArguments, buildExecutionContext, buildResolveInfo, collectFields, execute, getFieldDef } from \"../execution/execute.mjs\";\nimport { getOperationRootType } from \"../utilities/getOperationRootType.mjs\";\nimport mapAsyncIterator from \"./mapAsyncIterator.mjs\";\nexport function subscribe(argsOrSchema, document, rootValue, contextValue, variableValues, operationName, fieldResolver, subscribeFieldResolver) {\n  /* eslint-enable no-redeclare */\n  // Extract arguments from object args if provided.\n  return arguments.length === 1 ? subscribeImpl(argsOrSchema) : subscribeImpl({\n    schema: argsOrSchema,\n    document: document,\n    rootValue: rootValue,\n    contextValue: contextValue,\n    variableValues: variableValues,\n    operationName: operationName,\n    fieldResolver: fieldResolver,\n    subscribeFieldResolver: subscribeFieldResolver\n  });\n}\n/**\n * This function checks if the error is a GraphQLError. If it is, report it as\n * an ExecutionResult, containing only errors and no data. Otherwise treat the\n * error as a system-class error and re-throw it.\n */\n\nfunction reportGraphQLError(error) {\n  if (error instanceof GraphQLError) {\n    return {\n      errors: [error]\n    };\n  }\n  throw error;\n}\nfunction subscribeImpl(args) {\n  var schema = args.schema,\n    document = args.document,\n    rootValue = args.rootValue,\n    contextValue = args.contextValue,\n    variableValues = args.variableValues,\n    operationName = args.operationName,\n    fieldResolver = args.fieldResolver,\n    subscribeFieldResolver = args.subscribeFieldResolver;\n  var sourcePromise = createSourceEventStream(schema, document, rootValue, contextValue, variableValues, operationName, subscribeFieldResolver); // For each payload yielded from a subscription, map it over the normal\n  // GraphQL `execute` function, with `payload` as the rootValue.\n  // This implements the \"MapSourceToResponseEvent\" algorithm described in\n  // the GraphQL specification. The `execute` function provides the\n  // \"ExecuteSubscriptionEvent\" algorithm, as it is nearly identical to the\n  // \"ExecuteQuery\" algorithm, for which `execute` is also used.\n\n  var mapSourceToResponse = function mapSourceToResponse(payload) {\n    return execute({\n      schema: schema,\n      document: document,\n      rootValue: payload,\n      contextValue: contextValue,\n      variableValues: variableValues,\n      operationName: operationName,\n      fieldResolver: fieldResolver\n    });\n  }; // Resolve the Source Stream, then map every source value to a\n  // ExecutionResult value as described above.\n\n  return sourcePromise.then(function (resultOrStream) {\n    return (\n      // Note: Flow can't refine isAsyncIterable, so explicit casts are used.\n      isAsyncIterable(resultOrStream) ? mapAsyncIterator(resultOrStream, mapSourceToResponse, reportGraphQLError) : resultOrStream\n    );\n  });\n}\n/**\n * Implements the \"CreateSourceEventStream\" algorithm described in the\n * GraphQL specification, resolving the subscription source event stream.\n *\n * Returns a Promise which resolves to either an AsyncIterable (if successful)\n * or an ExecutionResult (error). The promise will be rejected if the schema or\n * other arguments to this function are invalid, or if the resolved event stream\n * is not an async iterable.\n *\n * If the client-provided arguments to this function do not result in a\n * compliant subscription, a GraphQL Response (ExecutionResult) with\n * descriptive errors and no data will be returned.\n *\n * If the the source stream could not be created due to faulty subscription\n * resolver logic or underlying systems, the promise will resolve to a single\n * ExecutionResult containing `errors` and no `data`.\n *\n * If the operation succeeded, the promise resolves to the AsyncIterable for the\n * event stream returned by the resolver.\n *\n * A Source Event Stream represents a sequence of events, each of which triggers\n * a GraphQL execution for that event.\n *\n * This may be useful when hosting the stateful subscription service in a\n * different process or machine than the stateless GraphQL execution engine,\n * or otherwise separating these two steps. For more on this, see the\n * \"Supporting Subscriptions at Scale\" information in the GraphQL specification.\n */\n\nexport function createSourceEventStream(schema, document, rootValue, contextValue, variableValues, operationName, fieldResolver) {\n  // If arguments are missing or incorrectly typed, this is an internal\n  // developer mistake which should throw an early error.\n  assertValidExecutionArguments(schema, document, variableValues);\n  return new Promise(function (resolve) {\n    // If a valid context cannot be created due to incorrect arguments,\n    // this will throw an error.\n    var exeContext = buildExecutionContext(schema, document, rootValue, contextValue, variableValues, operationName, fieldResolver);\n    resolve(\n    // Return early errors if execution context failed.\n    Array.isArray(exeContext) ? {\n      errors: exeContext\n    } : executeSubscription(exeContext));\n  }).catch(reportGraphQLError);\n}\nfunction executeSubscription(exeContext) {\n  var schema = exeContext.schema,\n    operation = exeContext.operation,\n    variableValues = exeContext.variableValues,\n    rootValue = exeContext.rootValue;\n  var type = getOperationRootType(schema, operation);\n  var fields = collectFields(exeContext, type, operation.selectionSet, Object.create(null), Object.create(null));\n  var responseNames = Object.keys(fields);\n  var responseName = responseNames[0];\n  var fieldNodes = fields[responseName];\n  var fieldNode = fieldNodes[0];\n  var fieldName = fieldNode.name.value;\n  var fieldDef = getFieldDef(schema, type, fieldName);\n  if (!fieldDef) {\n    throw new GraphQLError(\"The subscription field \\\"\".concat(fieldName, \"\\\" is not defined.\"), fieldNodes);\n  }\n  var path = addPath(undefined, responseName, type.name);\n  var info = buildResolveInfo(exeContext, fieldDef, fieldNodes, type, path); // Coerce to Promise for easier error handling and consistent return type.\n\n  return new Promise(function (resolveResult) {\n    var _fieldDef$subscribe;\n\n    // Implements the \"ResolveFieldEventStream\" algorithm from GraphQL specification.\n    // It differs from \"ResolveFieldValue\" due to providing a different `resolveFn`.\n    // Build a JS object of arguments from the field.arguments AST, using the\n    // variables scope to fulfill any variable references.\n    var args = getArgumentValues(fieldDef, fieldNodes[0], variableValues); // The resolve function's optional third argument is a context value that\n    // is provided to every resolve function within an execution. It is commonly\n    // used to represent an authenticated user, or request-specific caches.\n\n    var contextValue = exeContext.contextValue; // Call the `subscribe()` resolver or the default resolver to produce an\n    // AsyncIterable yielding raw payloads.\n\n    var resolveFn = (_fieldDef$subscribe = fieldDef.subscribe) !== null && _fieldDef$subscribe !== void 0 ? _fieldDef$subscribe : exeContext.fieldResolver;\n    resolveResult(resolveFn(rootValue, args, contextValue, info));\n  }).then(function (eventStream) {\n    if (eventStream instanceof Error) {\n      throw locatedError(eventStream, fieldNodes, pathToArray(path));\n    } // Assert field returned an event stream, otherwise yield an error.\n\n    if (!isAsyncIterable(eventStream)) {\n      throw new Error('Subscription field must return Async Iterable. ' + \"Received: \".concat(inspect(eventStream), \".\"));\n    }\n    return eventStream;\n  }, function (error) {\n    throw locatedError(error, fieldNodes, pathToArray(path));\n  });\n}", "map": {"version": 3, "names": ["inspect", "isAsyncIterable", "addPath", "pathToArray", "GraphQLError", "locatedError", "getArgumentValues", "assertValidExecutionArguments", "buildExecutionContext", "buildResolveInfo", "collectFields", "execute", "getFieldDef", "getOperationRootType", "mapAsyncIterator", "subscribe", "argsOrSchema", "document", "rootValue", "contextValue", "variableValues", "operationName", "fieldResolver", "subscribeFieldResolver", "arguments", "length", "subscribeImpl", "schema", "reportGraphQLError", "error", "errors", "args", "sourcePromise", "createSourceEventStream", "mapSourceToResponse", "payload", "then", "resultOrStream", "Promise", "resolve", "exeContext", "Array", "isArray", "executeSubscription", "catch", "operation", "type", "fields", "selectionSet", "Object", "create", "responseNames", "keys", "responseName", "fieldNodes", "fieldNode", "fieldName", "name", "value", "fieldDef", "concat", "path", "undefined", "info", "resolveResult", "_fieldDef$subscribe", "resolveFn", "eventStream", "Error"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/graphql/subscription/subscribe.mjs"], "sourcesContent": ["import inspect from \"../jsutils/inspect.mjs\";\nimport isAsyncIterable from \"../jsutils/isAsyncIterable.mjs\";\nimport { addPath, pathToArray } from \"../jsutils/Path.mjs\";\nimport { GraphQLError } from \"../error/GraphQLError.mjs\";\nimport { locatedError } from \"../error/locatedError.mjs\";\nimport { getArgumentValues } from \"../execution/values.mjs\";\nimport { assertValidExecutionArguments, buildExecutionContext, buildResolveInfo, collectFields, execute, getFieldDef } from \"../execution/execute.mjs\";\nimport { getOperationRootType } from \"../utilities/getOperationRootType.mjs\";\nimport mapAsyncIterator from \"./mapAsyncIterator.mjs\";\nexport function subscribe(argsOrSchema, document, rootValue, contextValue, variableValues, operationName, fieldResolver, subscribeFieldResolver) {\n  /* eslint-enable no-redeclare */\n  // Extract arguments from object args if provided.\n  return arguments.length === 1 ? subscribeImpl(argsOrSchema) : subscribeImpl({\n    schema: argsOrSchema,\n    document: document,\n    rootValue: rootValue,\n    contextValue: contextValue,\n    variableValues: variableValues,\n    operationName: operationName,\n    fieldResolver: fieldResolver,\n    subscribeFieldResolver: subscribeFieldResolver\n  });\n}\n/**\n * This function checks if the error is a GraphQLError. If it is, report it as\n * an ExecutionResult, containing only errors and no data. Otherwise treat the\n * error as a system-class error and re-throw it.\n */\n\nfunction reportGraphQLError(error) {\n  if (error instanceof GraphQLError) {\n    return {\n      errors: [error]\n    };\n  }\n\n  throw error;\n}\n\nfunction subscribeImpl(args) {\n  var schema = args.schema,\n      document = args.document,\n      rootValue = args.rootValue,\n      contextValue = args.contextValue,\n      variableValues = args.variableValues,\n      operationName = args.operationName,\n      fieldResolver = args.fieldResolver,\n      subscribeFieldResolver = args.subscribeFieldResolver;\n  var sourcePromise = createSourceEventStream(schema, document, rootValue, contextValue, variableValues, operationName, subscribeFieldResolver); // For each payload yielded from a subscription, map it over the normal\n  // GraphQL `execute` function, with `payload` as the rootValue.\n  // This implements the \"MapSourceToResponseEvent\" algorithm described in\n  // the GraphQL specification. The `execute` function provides the\n  // \"ExecuteSubscriptionEvent\" algorithm, as it is nearly identical to the\n  // \"ExecuteQuery\" algorithm, for which `execute` is also used.\n\n  var mapSourceToResponse = function mapSourceToResponse(payload) {\n    return execute({\n      schema: schema,\n      document: document,\n      rootValue: payload,\n      contextValue: contextValue,\n      variableValues: variableValues,\n      operationName: operationName,\n      fieldResolver: fieldResolver\n    });\n  }; // Resolve the Source Stream, then map every source value to a\n  // ExecutionResult value as described above.\n\n\n  return sourcePromise.then(function (resultOrStream) {\n    return (// Note: Flow can't refine isAsyncIterable, so explicit casts are used.\n      isAsyncIterable(resultOrStream) ? mapAsyncIterator(resultOrStream, mapSourceToResponse, reportGraphQLError) : resultOrStream\n    );\n  });\n}\n/**\n * Implements the \"CreateSourceEventStream\" algorithm described in the\n * GraphQL specification, resolving the subscription source event stream.\n *\n * Returns a Promise which resolves to either an AsyncIterable (if successful)\n * or an ExecutionResult (error). The promise will be rejected if the schema or\n * other arguments to this function are invalid, or if the resolved event stream\n * is not an async iterable.\n *\n * If the client-provided arguments to this function do not result in a\n * compliant subscription, a GraphQL Response (ExecutionResult) with\n * descriptive errors and no data will be returned.\n *\n * If the the source stream could not be created due to faulty subscription\n * resolver logic or underlying systems, the promise will resolve to a single\n * ExecutionResult containing `errors` and no `data`.\n *\n * If the operation succeeded, the promise resolves to the AsyncIterable for the\n * event stream returned by the resolver.\n *\n * A Source Event Stream represents a sequence of events, each of which triggers\n * a GraphQL execution for that event.\n *\n * This may be useful when hosting the stateful subscription service in a\n * different process or machine than the stateless GraphQL execution engine,\n * or otherwise separating these two steps. For more on this, see the\n * \"Supporting Subscriptions at Scale\" information in the GraphQL specification.\n */\n\n\nexport function createSourceEventStream(schema, document, rootValue, contextValue, variableValues, operationName, fieldResolver) {\n  // If arguments are missing or incorrectly typed, this is an internal\n  // developer mistake which should throw an early error.\n  assertValidExecutionArguments(schema, document, variableValues);\n  return new Promise(function (resolve) {\n    // If a valid context cannot be created due to incorrect arguments,\n    // this will throw an error.\n    var exeContext = buildExecutionContext(schema, document, rootValue, contextValue, variableValues, operationName, fieldResolver);\n    resolve( // Return early errors if execution context failed.\n    Array.isArray(exeContext) ? {\n      errors: exeContext\n    } : executeSubscription(exeContext));\n  }).catch(reportGraphQLError);\n}\n\nfunction executeSubscription(exeContext) {\n  var schema = exeContext.schema,\n      operation = exeContext.operation,\n      variableValues = exeContext.variableValues,\n      rootValue = exeContext.rootValue;\n  var type = getOperationRootType(schema, operation);\n  var fields = collectFields(exeContext, type, operation.selectionSet, Object.create(null), Object.create(null));\n  var responseNames = Object.keys(fields);\n  var responseName = responseNames[0];\n  var fieldNodes = fields[responseName];\n  var fieldNode = fieldNodes[0];\n  var fieldName = fieldNode.name.value;\n  var fieldDef = getFieldDef(schema, type, fieldName);\n\n  if (!fieldDef) {\n    throw new GraphQLError(\"The subscription field \\\"\".concat(fieldName, \"\\\" is not defined.\"), fieldNodes);\n  }\n\n  var path = addPath(undefined, responseName, type.name);\n  var info = buildResolveInfo(exeContext, fieldDef, fieldNodes, type, path); // Coerce to Promise for easier error handling and consistent return type.\n\n  return new Promise(function (resolveResult) {\n    var _fieldDef$subscribe;\n\n    // Implements the \"ResolveFieldEventStream\" algorithm from GraphQL specification.\n    // It differs from \"ResolveFieldValue\" due to providing a different `resolveFn`.\n    // Build a JS object of arguments from the field.arguments AST, using the\n    // variables scope to fulfill any variable references.\n    var args = getArgumentValues(fieldDef, fieldNodes[0], variableValues); // The resolve function's optional third argument is a context value that\n    // is provided to every resolve function within an execution. It is commonly\n    // used to represent an authenticated user, or request-specific caches.\n\n    var contextValue = exeContext.contextValue; // Call the `subscribe()` resolver or the default resolver to produce an\n    // AsyncIterable yielding raw payloads.\n\n    var resolveFn = (_fieldDef$subscribe = fieldDef.subscribe) !== null && _fieldDef$subscribe !== void 0 ? _fieldDef$subscribe : exeContext.fieldResolver;\n    resolveResult(resolveFn(rootValue, args, contextValue, info));\n  }).then(function (eventStream) {\n    if (eventStream instanceof Error) {\n      throw locatedError(eventStream, fieldNodes, pathToArray(path));\n    } // Assert field returned an event stream, otherwise yield an error.\n\n\n    if (!isAsyncIterable(eventStream)) {\n      throw new Error('Subscription field must return Async Iterable. ' + \"Received: \".concat(inspect(eventStream), \".\"));\n    }\n\n    return eventStream;\n  }, function (error) {\n    throw locatedError(error, fieldNodes, pathToArray(path));\n  });\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,wBAAwB;AAC5C,OAAOC,eAAe,MAAM,gCAAgC;AAC5D,SAASC,OAAO,EAAEC,WAAW,QAAQ,qBAAqB;AAC1D,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,6BAA6B,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,EAAEC,WAAW,QAAQ,0BAA0B;AACtJ,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,OAAOC,gBAAgB,MAAM,wBAAwB;AACrD,OAAO,SAASC,SAASA,CAACC,YAAY,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,cAAc,EAAEC,aAAa,EAAEC,aAAa,EAAEC,sBAAsB,EAAE;EAC/I;EACA;EACA,OAAOC,SAAS,CAACC,MAAM,KAAK,CAAC,GAAGC,aAAa,CAACV,YAAY,CAAC,GAAGU,aAAa,CAAC;IAC1EC,MAAM,EAAEX,YAAY;IACpBC,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAEA,SAAS;IACpBC,YAAY,EAAEA,YAAY;IAC1BC,cAAc,EAAEA,cAAc;IAC9BC,aAAa,EAAEA,aAAa;IAC5BC,aAAa,EAAEA,aAAa;IAC5BC,sBAAsB,EAAEA;EAC1B,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASK,kBAAkBA,CAACC,KAAK,EAAE;EACjC,IAAIA,KAAK,YAAYzB,YAAY,EAAE;IACjC,OAAO;MACL0B,MAAM,EAAE,CAACD,KAAK;IAChB,CAAC;EACH;EAEA,MAAMA,KAAK;AACb;AAEA,SAASH,aAAaA,CAACK,IAAI,EAAE;EAC3B,IAAIJ,MAAM,GAAGI,IAAI,CAACJ,MAAM;IACpBV,QAAQ,GAAGc,IAAI,CAACd,QAAQ;IACxBC,SAAS,GAAGa,IAAI,CAACb,SAAS;IAC1BC,YAAY,GAAGY,IAAI,CAACZ,YAAY;IAChCC,cAAc,GAAGW,IAAI,CAACX,cAAc;IACpCC,aAAa,GAAGU,IAAI,CAACV,aAAa;IAClCC,aAAa,GAAGS,IAAI,CAACT,aAAa;IAClCC,sBAAsB,GAAGQ,IAAI,CAACR,sBAAsB;EACxD,IAAIS,aAAa,GAAGC,uBAAuB,CAACN,MAAM,EAAEV,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,cAAc,EAAEC,aAAa,EAAEE,sBAAsB,CAAC,CAAC,CAAC;EAC/I;EACA;EACA;EACA;EACA;;EAEA,IAAIW,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,OAAO,EAAE;IAC9D,OAAOxB,OAAO,CAAC;MACbgB,MAAM,EAAEA,MAAM;MACdV,QAAQ,EAAEA,QAAQ;MAClBC,SAAS,EAAEiB,OAAO;MAClBhB,YAAY,EAAEA,YAAY;MAC1BC,cAAc,EAAEA,cAAc;MAC9BC,aAAa,EAAEA,aAAa;MAC5BC,aAAa,EAAEA;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;EACH;;EAGA,OAAOU,aAAa,CAACI,IAAI,CAAC,UAAUC,cAAc,EAAE;IAClD;MAAQ;MACNpC,eAAe,CAACoC,cAAc,CAAC,GAAGvB,gBAAgB,CAACuB,cAAc,EAAEH,mBAAmB,EAAEN,kBAAkB,CAAC,GAAGS;IAAc;EAEhI,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,OAAO,SAASJ,uBAAuBA,CAACN,MAAM,EAAEV,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,cAAc,EAAEC,aAAa,EAAEC,aAAa,EAAE;EAC/H;EACA;EACAf,6BAA6B,CAACoB,MAAM,EAAEV,QAAQ,EAAEG,cAAc,CAAC;EAC/D,OAAO,IAAIkB,OAAO,CAAC,UAAUC,OAAO,EAAE;IACpC;IACA;IACA,IAAIC,UAAU,GAAGhC,qBAAqB,CAACmB,MAAM,EAAEV,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,cAAc,EAAEC,aAAa,EAAEC,aAAa,CAAC;IAC/HiB,OAAO;IAAE;IACTE,KAAK,CAACC,OAAO,CAACF,UAAU,CAAC,GAAG;MAC1BV,MAAM,EAAEU;IACV,CAAC,GAAGG,mBAAmB,CAACH,UAAU,CAAC,CAAC;EACtC,CAAC,CAAC,CAACI,KAAK,CAAChB,kBAAkB,CAAC;AAC9B;AAEA,SAASe,mBAAmBA,CAACH,UAAU,EAAE;EACvC,IAAIb,MAAM,GAAGa,UAAU,CAACb,MAAM;IAC1BkB,SAAS,GAAGL,UAAU,CAACK,SAAS;IAChCzB,cAAc,GAAGoB,UAAU,CAACpB,cAAc;IAC1CF,SAAS,GAAGsB,UAAU,CAACtB,SAAS;EACpC,IAAI4B,IAAI,GAAGjC,oBAAoB,CAACc,MAAM,EAAEkB,SAAS,CAAC;EAClD,IAAIE,MAAM,GAAGrC,aAAa,CAAC8B,UAAU,EAAEM,IAAI,EAAED,SAAS,CAACG,YAAY,EAAEC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,EAAED,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CAAC;EAC9G,IAAIC,aAAa,GAAGF,MAAM,CAACG,IAAI,CAACL,MAAM,CAAC;EACvC,IAAIM,YAAY,GAAGF,aAAa,CAAC,CAAC,CAAC;EACnC,IAAIG,UAAU,GAAGP,MAAM,CAACM,YAAY,CAAC;EACrC,IAAIE,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC;EAC7B,IAAIE,SAAS,GAAGD,SAAS,CAACE,IAAI,CAACC,KAAK;EACpC,IAAIC,QAAQ,GAAG/C,WAAW,CAACe,MAAM,EAAEmB,IAAI,EAAEU,SAAS,CAAC;EAEnD,IAAI,CAACG,QAAQ,EAAE;IACb,MAAM,IAAIvD,YAAY,CAAC,2BAA2B,CAACwD,MAAM,CAACJ,SAAS,EAAE,oBAAoB,CAAC,EAAEF,UAAU,CAAC;EACzG;EAEA,IAAIO,IAAI,GAAG3D,OAAO,CAAC4D,SAAS,EAAET,YAAY,EAAEP,IAAI,CAACW,IAAI,CAAC;EACtD,IAAIM,IAAI,GAAGtD,gBAAgB,CAAC+B,UAAU,EAAEmB,QAAQ,EAAEL,UAAU,EAAER,IAAI,EAAEe,IAAI,CAAC,CAAC,CAAC;;EAE3E,OAAO,IAAIvB,OAAO,CAAC,UAAU0B,aAAa,EAAE;IAC1C,IAAIC,mBAAmB;;IAEvB;IACA;IACA;IACA;IACA,IAAIlC,IAAI,GAAGzB,iBAAiB,CAACqD,QAAQ,EAAEL,UAAU,CAAC,CAAC,CAAC,EAAElC,cAAc,CAAC,CAAC,CAAC;IACvE;IACA;;IAEA,IAAID,YAAY,GAAGqB,UAAU,CAACrB,YAAY,CAAC,CAAC;IAC5C;;IAEA,IAAI+C,SAAS,GAAG,CAACD,mBAAmB,GAAGN,QAAQ,CAAC5C,SAAS,MAAM,IAAI,IAAIkD,mBAAmB,KAAK,KAAK,CAAC,GAAGA,mBAAmB,GAAGzB,UAAU,CAAClB,aAAa;IACtJ0C,aAAa,CAACE,SAAS,CAAChD,SAAS,EAAEa,IAAI,EAAEZ,YAAY,EAAE4C,IAAI,CAAC,CAAC;EAC/D,CAAC,CAAC,CAAC3B,IAAI,CAAC,UAAU+B,WAAW,EAAE;IAC7B,IAAIA,WAAW,YAAYC,KAAK,EAAE;MAChC,MAAM/D,YAAY,CAAC8D,WAAW,EAAEb,UAAU,EAAEnD,WAAW,CAAC0D,IAAI,CAAC,CAAC;IAChE,CAAC,CAAC;;IAGF,IAAI,CAAC5D,eAAe,CAACkE,WAAW,CAAC,EAAE;MACjC,MAAM,IAAIC,KAAK,CAAC,iDAAiD,GAAG,YAAY,CAACR,MAAM,CAAC5D,OAAO,CAACmE,WAAW,CAAC,EAAE,GAAG,CAAC,CAAC;IACrH;IAEA,OAAOA,WAAW;EACpB,CAAC,EAAE,UAAUtC,KAAK,EAAE;IAClB,MAAMxB,YAAY,CAACwB,KAAK,EAAEyB,UAAU,EAAEnD,WAAW,CAAC0D,IAAI,CAAC,CAAC;EAC1D,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}