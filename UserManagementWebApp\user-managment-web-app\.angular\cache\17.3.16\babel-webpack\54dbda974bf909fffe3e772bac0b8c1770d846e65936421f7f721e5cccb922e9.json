{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport '@aws-amplify/core/internals/utils';\nimport '../../providers/cognito/utils/types.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../common/AuthErrorStrings.mjs';\nimport '../../errors/types/validation.mjs';\nimport '../../providers/cognito/types/errors.mjs';\nimport { deleteWebAuthnCredential as deleteWebAuthnCredential$1 } from '../../foundation/apis/deleteWebAuthnCredential.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Delete a registered credential for an authenticated user by credentialId\n * @param {DeleteWebAuthnCredentialInput} input The delete input parameters including the credentialId\n * @returns Promise<void>\n * @throws - {@link AuthError}:\n * - Thrown when user is unauthenticated\n * @throws - {@link DeleteWebAuthnCredentialException}\n * - Thrown due to a service error when deleting a WebAuthn credential\n */\nfunction deleteWebAuthnCredential(_x) {\n  return _deleteWebAuthnCredential.apply(this, arguments);\n}\nfunction _deleteWebAuthnCredential() {\n  _deleteWebAuthnCredential = _asyncToGenerator(function* (input) {\n    return deleteWebAuthnCredential$1(Amplify, input);\n  });\n  return _deleteWebAuthnCredential.apply(this, arguments);\n}\nexport { deleteWebAuthnCredential };", "map": {"version": 3, "names": ["Amplify", "deleteWebAuthnCredential", "deleteWebAuthnCredential$1", "_x", "_deleteWebAuthnCredential", "apply", "arguments", "_asyncToGenerator", "input"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/apis/deleteWebAuthnCredential.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport '@aws-amplify/core/internals/utils';\nimport '../../providers/cognito/utils/types.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../common/AuthErrorStrings.mjs';\nimport '../../errors/types/validation.mjs';\nimport '../../providers/cognito/types/errors.mjs';\nimport { deleteWebAuthnCredential as deleteWebAuthnCredential$1 } from '../../foundation/apis/deleteWebAuthnCredential.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Delete a registered credential for an authenticated user by credentialId\n * @param {DeleteWebAuthnCredentialInput} input The delete input parameters including the credentialId\n * @returns Promise<void>\n * @throws - {@link AuthError}:\n * - Thrown when user is unauthenticated\n * @throws - {@link DeleteWebAuthnCredentialException}\n * - Thrown due to a service error when deleting a WebAuthn credential\n */\nasync function deleteWebAuthnCredential(input) {\n    return deleteWebAuthnCredential$1(Amplify, input);\n}\n\nexport { deleteWebAuthnCredential };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,OAAO,mCAAmC;AAC1C,OAAO,yCAAyC;AAChD,OAAO,8CAA8C;AACrD,OAAO,wDAAwD;AAC/D,OAAO,qHAAqH;AAC5H,OAAO,iFAAiF;AACxF,OAAO,mCAAmC;AAC1C,OAAO,mCAAmC;AAC1C,OAAO,0CAA0C;AACjD,SAASC,wBAAwB,IAAIC,0BAA0B,QAAQ,oDAAoD;;AAE3H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SASeD,wBAAwBA,CAAAE,EAAA;EAAA,OAAAC,yBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,0BAAA;EAAAA,yBAAA,GAAAG,iBAAA,CAAvC,WAAwCC,KAAK,EAAE;IAC3C,OAAON,0BAA0B,CAACF,OAAO,EAAEQ,KAAK,CAAC;EACrD,CAAC;EAAA,OAAAJ,yBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASL,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}