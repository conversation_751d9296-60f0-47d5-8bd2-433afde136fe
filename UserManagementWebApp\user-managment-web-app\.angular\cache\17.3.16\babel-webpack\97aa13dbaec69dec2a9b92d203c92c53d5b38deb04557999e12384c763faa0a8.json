{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Transforms a user attributes object into an array of AttributeType objects.\n * @param attributes user attributes to be mapped to AttributeType objects.\n * @returns an array of AttributeType objects.\n */\nfunction toAttributeType(attributes) {\n  return Object.entries(attributes).map(([key, value]) => ({\n    Name: key,\n    Value: value\n  }));\n}\n/**\n * Transforms an array of AttributeType objects into a user attributes object.\n *\n * @param attributes - an array of AttributeType objects.\n * @returns AuthUserAttributes object.\n */\nfunction toAuthUserAttribute(attributes) {\n  const userAttributes = {};\n  attributes?.forEach(attribute => {\n    if (attribute.Name) userAttributes[attribute.Name] = attribute.Value;\n  });\n  return userAttributes;\n}\nexport { toAttributeType, toAuthUserAttribute };", "map": {"version": 3, "names": ["toAttributeType", "attributes", "Object", "entries", "map", "key", "value", "Name", "Value", "toAuthUserAttribute", "userAttributes", "for<PERSON>ach", "attribute"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/apiHelpers.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Transforms a user attributes object into an array of AttributeType objects.\n * @param attributes user attributes to be mapped to AttributeType objects.\n * @returns an array of AttributeType objects.\n */\nfunction toAttributeType(attributes) {\n    return Object.entries(attributes).map(([key, value]) => ({\n        Name: key,\n        Value: value,\n    }));\n}\n/**\n * Transforms an array of AttributeType objects into a user attributes object.\n *\n * @param attributes - an array of AttributeType objects.\n * @returns AuthUserAttributes object.\n */\nfunction toAuthUserAttribute(attributes) {\n    const userAttributes = {};\n    attributes?.forEach(attribute => {\n        if (attribute.Name)\n            userAttributes[attribute.Name] = attribute.Value;\n    });\n    return userAttributes;\n}\n\nexport { toAttributeType, toAuthUserAttribute };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,eAAeA,CAACC,UAAU,EAAE;EACjC,OAAOC,MAAM,CAACC,OAAO,CAACF,UAAU,CAAC,CAACG,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,MAAM;IACrDC,IAAI,EAAEF,GAAG;IACTG,KAAK,EAAEF;EACX,CAAC,CAAC,CAAC;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,mBAAmBA,CAACR,UAAU,EAAE;EACrC,MAAMS,cAAc,GAAG,CAAC,CAAC;EACzBT,UAAU,EAAEU,OAAO,CAACC,SAAS,IAAI;IAC7B,IAAIA,SAAS,CAACL,IAAI,EACdG,cAAc,CAACE,SAAS,CAACL,IAAI,CAAC,GAAGK,SAAS,CAACJ,KAAK;EACxD,CAAC,CAAC;EACF,OAAOE,cAAc;AACzB;AAEA,SAASV,eAAe,EAAES,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}