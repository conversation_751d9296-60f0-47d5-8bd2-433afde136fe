{"ast": null, "code": "import { authErrorMessages } from '../../../../Errors.mjs';\nimport { AuthErrorCodes } from '../../../../common/AuthErrorStrings.mjs';\nimport { AuthError } from '../../../../errors/AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createOAuthError = (message, recoverySuggestion) => new AuthError({\n  message: message ?? 'An error has occurred during the oauth process.',\n  name: AuthErrorCodes.OAuthSignInError,\n  recoverySuggestion: recoverySuggestion ?? authErrorMessages.oauthSignInError.log\n});\nexport { createOAuthError };", "map": {"version": 3, "names": ["authErrorMessages", "AuthErrorCodes", "<PERSON>th<PERSON><PERSON><PERSON>", "createOAuthError", "message", "recoverySuggestion", "name", "OAuthSignInError", "oauthSignInError", "log"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/oauth/createOAuthError.mjs"], "sourcesContent": ["import { authErrorMessages } from '../../../../Errors.mjs';\nimport { AuthErrorCodes } from '../../../../common/AuthErrorStrings.mjs';\nimport { AuthError } from '../../../../errors/AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createOAuthError = (message, recoverySuggestion) => new AuthError({\n    message: message ?? 'An error has occurred during the oauth process.',\n    name: AuthErrorCodes.OAuthSignInError,\n    recoverySuggestion: recoverySuggestion ?? authErrorMessages.oauthSignInError.log,\n});\n\nexport { createOAuthError };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,SAAS,QAAQ,kCAAkC;;AAE5D;AACA;AACA,MAAMC,gBAAgB,GAAGA,CAACC,OAAO,EAAEC,kBAAkB,KAAK,IAAIH,SAAS,CAAC;EACpEE,OAAO,EAAEA,OAAO,IAAI,iDAAiD;EACrEE,IAAI,EAAEL,cAAc,CAACM,gBAAgB;EACrCF,kBAAkB,EAAEA,kBAAkB,IAAIL,iBAAiB,CAACQ,gBAAgB,CAACC;AACjF,CAAC,CAAC;AAEF,SAASN,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}