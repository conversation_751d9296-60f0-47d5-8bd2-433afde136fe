{"ast": null, "code": "/**\n * @deprecated - will be removed in a future major version\n *\n * Some libraries may not follow Node ES module spec and could be loaded as CommonJS modules,\n * To ensure the interoperability between ESM and CJS, modules from those libraries have to be loaded via namespace import\n * And sanitized by the function below because unlike ESM namespace, CJS namespace set `module.exports` object on the `default` key\n * https://nodejs.org/api/esm.html#interoperability-with-commonjs\n */\nconst sanitizeNamespaceImport = namespaceModule => {\n  const sanitizedNamespaceModule = {\n    default: undefined,\n    ...namespaceModule\n  };\n  return sanitizedNamespaceModule.default ?? sanitizedNamespaceModule;\n};\n/**\n * Checks if `value` is an Object (non-primitive, non-array, non-function)\n * Will return false for Arrays and functions\n *\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is an object, `false` otherwise\n */\nfunction isObject(value) {\n  return value != null && !Array.isArray(value) && typeof value === 'object';\n}\n/**\n * Checks if `value` is a string primitive or object\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is a string, `false` otherwise\n */\nfunction isString(value) {\n  return typeof value === 'string' || typeof value === 'object' && Object.prototype.toString.call(value) === '[object String]';\n}\n/**\n * Checks if `value` is a Map\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is a Map, `false` otherwise\n */\nfunction isMap(value) {\n  return isObject(value) && Object.prototype.toString.call(value) === '[object Map]';\n}\n/**\n * Checks if `value` is a Set\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is a Set, `false` otherwise\n */\nfunction isSet(value) {\n  return isObject(value) && Object.prototype.toString.call(value) === '[object Set]';\n}\n/**\n * Checks if `value` is undefined\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is undefined, `false` otherwise\n */\nfunction isUndefined(value) {\n  return value === undefined;\n}\n/**\n * Checks if `value` is nullish\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is nullish, `false` otherwise\n */\nfunction isNil(value) {\n  return value == null;\n}\n/**\n * Checks if `value` is empty\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is empty, `false` otherwise\n */\nfunction isEmpty(value) {\n  if (value === null || value === undefined) return true;\n  if (isObject(value) && (isMap(value) || isSet(value))) {\n    return !value.size;\n  }\n  if (isObject(value) && (isString(value) || Array.isArray(value))) {\n    return !value.length;\n  }\n  for (const key in value) {\n    if (has(value, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Checks if `value` is an empty array\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is a empty, `false` otherwise\n */\nfunction isEmptyArray(value) {\n  return Array.isArray(value) && isEmpty(value);\n}\n/**\n * Checks if all members of the `values` param are empty arrays\n *\n * @param {unknown} value The values to check\n * @returns {boolean} Returns `true` if all members of `values` are empty, `false` otherwise\n */\nfunction areEmptyArrays(...values) {\n  return values.every(isEmptyArray);\n}\n/**\n * Checks if `value` is an empty object\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is empty, `false` otherwise\n */\nfunction isEmptyObject(value) {\n  return isObject(value) && isEmpty(value);\n}\n/**\n * Checks if all members of the `values` param are empty objects\n *\n * @param {unknown} values The values to check\n * @returns {boolean} Returns `true` if all members of the `values` param are empty, `false` otherwise\n */\nfunction areEmptyObjects(...values) {\n  return values.every(isEmptyObject);\n}\n/**\n * Capitalizes `value` and its return type\n *\n * @param {string} value string to capitalize\n * @returns {string} capitalized string\n */\nfunction capitalize(value) {\n  return isString(value) ? value.charAt(0).toUpperCase() + value.slice(1) : '';\n}\n/**\n * Checks if `key` is a direct property of `value`\n *\n * @param {unknown} value `object` potentially containing property\n * @param {string} key property key\n * @returns whether `key` param is a property of the `obj` param\n */\nfunction has(value, key) {\n  return value != null && Object.prototype.hasOwnProperty.call(value, key);\n}\n/**\n * Checks if `value` is a function\n *\n * @param {unknown} value param to check\n * @returns {boolean} whether `value` is a function\n */\nfunction isFunction(value) {\n  return typeof value === 'function';\n}\n/**\n * This helper function creates modifier class names that are used for our flat BEM styling\n * it takes in a base and modifier and returns the modified class if a modifier was passed in and null otherwise\n * @param base The base class of the output\n * @param modifier The modifier to add onto the base\n * @returns the modified class name or empty string\n */\nconst classNameModifier = (base, modifier) => {\n  return modifier ? `${base}--${modifier}` : '';\n};\n/**\n * This helper function creates modified class names that are used for our flat BEM styling\n * it takes in a base, modifier, and flag and returns the modified class name if the flag is true and null if the flag is false\n * @param base\n * @param modifier\n * @param flag\n * @returns the modified class name or empty string\n */\nconst classNameModifierByFlag = (base, modifier, flag) => {\n  return flag ? `${base}--${modifier}` : '';\n};\n/**\n * Similar to `Array.join`, with an optional callback/template param\n * for formatting returned string values\n *\n * @param {string[]} values string array\n * @param {(value: string) => string} template callback format param\n * @returns formatted string array\n */\nfunction templateJoin(values, template) {\n  return values.reduce((acc, curr, index) => `${acc}${isString(curr) ? template(curr, index, values) : ''}`, '');\n}\n/**\n * A function that does nothing\n *\n * @param {any[]} _ accepts any parameters\n * @returns nothing\n */\nfunction noop(..._) {\n  return;\n}\n/**\n * @param {string} groupName name of group\n * @param events string values related to group\n */\nfunction groupLog(groupName, ...events) {\n  const hasEvents = !!events?.length;\n  if (hasEvents) {\n    // eslint-disable-next-line no-console\n    console.groupCollapsed(groupName);\n    events?.forEach(event => {\n      // eslint-disable-next-line no-console\n      console.log(event);\n    });\n    // eslint-disable-next-line no-console\n    console.groupEnd();\n  } else {\n    // eslint-disable-next-line no-console\n    console.log(groupName);\n  }\n}\n/**\n * Splits an object into 2 objects based on a predicate\n *\n * @param {object} obj an object to split into two\n * @param {function} predicate function to determin where an element should go\n * @returns\n */\nfunction splitObject(obj, predicate) {\n  const left = {};\n  const right = {};\n  Object.entries(obj).forEach(([key, value]) => {\n    if (predicate(key)) {\n      left[key] = value;\n    } else {\n      right[key] = value;\n    }\n  });\n  return [left, right];\n}\nconst cloneDeep = obj => {\n  if (obj === null || obj === undefined || typeof obj !== 'object') {\n    return obj;\n  }\n  if (obj instanceof Array) {\n    return obj.reduce((arr, item, i) => {\n      arr[i] = cloneDeep(item);\n      return arr;\n    }, []);\n  }\n  if (obj instanceof Object) {\n    return Object.keys(obj || {}).reduce((cpObj, key) => {\n      cpObj[key] = cloneDeep(obj[key]);\n      return cpObj;\n    }, {});\n  }\n};\nexport { areEmptyArrays, areEmptyObjects, capitalize, classNameModifier, classNameModifierByFlag, cloneDeep, groupLog, has, isEmpty, isEmptyObject, isFunction, isMap, isNil, isObject, isSet, isString, isUndefined, noop, sanitizeNamespaceImport, splitObject, templateJoin };", "map": {"version": 3, "names": ["sanitizeNamespaceImport", "namespaceModule", "sanitizedNamespaceModule", "default", "undefined", "isObject", "value", "Array", "isArray", "isString", "Object", "prototype", "toString", "call", "isMap", "isSet", "isUndefined", "isNil", "isEmpty", "size", "length", "key", "has", "isEmptyArray", "areEmptyArrays", "values", "every", "isEmptyObject", "areEmptyObjects", "capitalize", "char<PERSON>t", "toUpperCase", "slice", "hasOwnProperty", "isFunction", "classNameModifier", "base", "modifier", "classNameModifierByFlag", "flag", "templateJoin", "template", "reduce", "acc", "curr", "index", "noop", "_", "groupLog", "groupName", "events", "hasEvents", "console", "groupCollapsed", "for<PERSON>ach", "event", "log", "groupEnd", "splitObject", "obj", "predicate", "left", "right", "entries", "cloneDeep", "arr", "item", "i", "keys", "cpObj"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/utils/utils.mjs"], "sourcesContent": ["/**\n * @deprecated - will be removed in a future major version\n *\n * Some libraries may not follow Node ES module spec and could be loaded as CommonJS modules,\n * To ensure the interoperability between ESM and CJS, modules from those libraries have to be loaded via namespace import\n * And sanitized by the function below because unlike ESM namespace, CJS namespace set `module.exports` object on the `default` key\n * https://nodejs.org/api/esm.html#interoperability-with-commonjs\n */\nconst sanitizeNamespaceImport = (namespaceModule) => {\n    const sanitizedNamespaceModule = { default: undefined, ...namespaceModule };\n    return sanitizedNamespaceModule.default ?? sanitizedNamespaceModule;\n};\n/**\n * Checks if `value` is an Object (non-primitive, non-array, non-function)\n * Will return false for Arrays and functions\n *\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is an object, `false` otherwise\n */\nfunction isObject(value) {\n    return value != null && !Array.isArray(value) && typeof value === 'object';\n}\n/**\n * Checks if `value` is a string primitive or object\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is a string, `false` otherwise\n */\nfunction isString(value) {\n    return (typeof value === 'string' ||\n        (typeof value === 'object' &&\n            Object.prototype.toString.call(value) === '[object String]'));\n}\n/**\n * Checks if `value` is a Map\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is a Map, `false` otherwise\n */\nfunction isMap(value) {\n    return (isObject(value) && Object.prototype.toString.call(value) === '[object Map]');\n}\n/**\n * Checks if `value` is a Set\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is a Set, `false` otherwise\n */\nfunction isSet(value) {\n    return (isObject(value) && Object.prototype.toString.call(value) === '[object Set]');\n}\n/**\n * Checks if `value` is undefined\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is undefined, `false` otherwise\n */\nfunction isUndefined(value) {\n    return value === undefined;\n}\n/**\n * Checks if `value` is nullish\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is nullish, `false` otherwise\n */\nfunction isNil(value) {\n    return value == null;\n}\n/**\n * Checks if `value` is empty\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is empty, `false` otherwise\n */\nfunction isEmpty(value) {\n    if (value === null || value === undefined)\n        return true;\n    if (isObject(value) && (isMap(value) || isSet(value))) {\n        return !value.size;\n    }\n    if (isObject(value) && (isString(value) || Array.isArray(value))) {\n        return !value.length;\n    }\n    for (const key in value) {\n        if (has(value, key)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Checks if `value` is an empty array\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is a empty, `false` otherwise\n */\nfunction isEmptyArray(value) {\n    return Array.isArray(value) && isEmpty(value);\n}\n/**\n * Checks if all members of the `values` param are empty arrays\n *\n * @param {unknown} value The values to check\n * @returns {boolean} Returns `true` if all members of `values` are empty, `false` otherwise\n */\nfunction areEmptyArrays(...values) {\n    return values.every(isEmptyArray);\n}\n/**\n * Checks if `value` is an empty object\n *\n * @param {unknown} value The value to check\n * @returns {boolean} Returns `true` if `value` is empty, `false` otherwise\n */\nfunction isEmptyObject(value) {\n    return isObject(value) && isEmpty(value);\n}\n/**\n * Checks if all members of the `values` param are empty objects\n *\n * @param {unknown} values The values to check\n * @returns {boolean} Returns `true` if all members of the `values` param are empty, `false` otherwise\n */\nfunction areEmptyObjects(...values) {\n    return values.every(isEmptyObject);\n}\n/**\n * Capitalizes `value` and its return type\n *\n * @param {string} value string to capitalize\n * @returns {string} capitalized string\n */\nfunction capitalize(value) {\n    return (isString(value) ? value.charAt(0).toUpperCase() + value.slice(1) : '');\n}\n/**\n * Checks if `key` is a direct property of `value`\n *\n * @param {unknown} value `object` potentially containing property\n * @param {string} key property key\n * @returns whether `key` param is a property of the `obj` param\n */\nfunction has(value, key) {\n    return value != null && Object.prototype.hasOwnProperty.call(value, key);\n}\n/**\n * Checks if `value` is a function\n *\n * @param {unknown} value param to check\n * @returns {boolean} whether `value` is a function\n */\nfunction isFunction(value) {\n    return typeof value === 'function';\n}\n/**\n * This helper function creates modifier class names that are used for our flat BEM styling\n * it takes in a base and modifier and returns the modified class if a modifier was passed in and null otherwise\n * @param base The base class of the output\n * @param modifier The modifier to add onto the base\n * @returns the modified class name or empty string\n */\nconst classNameModifier = (base, modifier) => {\n    return modifier ? `${base}--${modifier}` : '';\n};\n/**\n * This helper function creates modified class names that are used for our flat BEM styling\n * it takes in a base, modifier, and flag and returns the modified class name if the flag is true and null if the flag is false\n * @param base\n * @param modifier\n * @param flag\n * @returns the modified class name or empty string\n */\nconst classNameModifierByFlag = (base, modifier, flag) => {\n    return flag ? `${base}--${modifier}` : '';\n};\n/**\n * Similar to `Array.join`, with an optional callback/template param\n * for formatting returned string values\n *\n * @param {string[]} values string array\n * @param {(value: string) => string} template callback format param\n * @returns formatted string array\n */\nfunction templateJoin(values, template) {\n    return values.reduce((acc, curr, index) => `${acc}${isString(curr) ? template(curr, index, values) : ''}`, '');\n}\n/**\n * A function that does nothing\n *\n * @param {any[]} _ accepts any parameters\n * @returns nothing\n */\nfunction noop(..._) {\n    return;\n}\n/**\n * @param {string} groupName name of group\n * @param events string values related to group\n */\nfunction groupLog(groupName, ...events) {\n    const hasEvents = !!events?.length;\n    if (hasEvents) {\n        // eslint-disable-next-line no-console\n        console.groupCollapsed(groupName);\n        events?.forEach((event) => {\n            // eslint-disable-next-line no-console\n            console.log(event);\n        });\n        // eslint-disable-next-line no-console\n        console.groupEnd();\n    }\n    else {\n        // eslint-disable-next-line no-console\n        console.log(groupName);\n    }\n}\n/**\n * Splits an object into 2 objects based on a predicate\n *\n * @param {object} obj an object to split into two\n * @param {function} predicate function to determin where an element should go\n * @returns\n */\nfunction splitObject(obj, predicate) {\n    const left = {};\n    const right = {};\n    Object.entries(obj).forEach(([key, value]) => {\n        if (predicate(key)) {\n            left[key] = value;\n        }\n        else {\n            right[key] = value;\n        }\n    });\n    return [left, right];\n}\nconst cloneDeep = (obj) => {\n    if (obj === null || obj === undefined || typeof obj !== 'object') {\n        return obj;\n    }\n    if (obj instanceof Array) {\n        return obj.reduce((arr, item, i) => {\n            arr[i] = cloneDeep(item);\n            return arr;\n        }, []);\n    }\n    if (obj instanceof Object) {\n        return Object.keys(obj || {}).reduce((cpObj, key) => {\n            cpObj[key] = cloneDeep(obj[key]);\n            return cpObj;\n        }, {});\n    }\n};\n\nexport { areEmptyArrays, areEmptyObjects, capitalize, classNameModifier, classNameModifierByFlag, cloneDeep, groupLog, has, isEmpty, isEmptyObject, isFunction, isMap, isNil, isObject, isSet, isString, isUndefined, noop, sanitizeNamespaceImport, splitObject, templateJoin };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,uBAAuB,GAAIC,eAAe,IAAK;EACjD,MAAMC,wBAAwB,GAAG;IAAEC,OAAO,EAAEC,SAAS;IAAE,GAAGH;EAAgB,CAAC;EAC3E,OAAOC,wBAAwB,CAACC,OAAO,IAAID,wBAAwB;AACvE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAOA,KAAK,IAAI,IAAI,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAI,OAAOA,KAAK,KAAK,QAAQ;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACH,KAAK,EAAE;EACrB,OAAQ,OAAOA,KAAK,KAAK,QAAQ,IAC5B,OAAOA,KAAK,KAAK,QAAQ,IACtBI,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAK,CAAC,KAAK,iBAAkB;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,KAAKA,CAACR,KAAK,EAAE;EAClB,OAAQD,QAAQ,CAACC,KAAK,CAAC,IAAII,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAK,CAAC,KAAK,cAAc;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,KAAKA,CAACT,KAAK,EAAE;EAClB,OAAQD,QAAQ,CAACC,KAAK,CAAC,IAAII,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,KAAK,CAAC,KAAK,cAAc;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,WAAWA,CAACV,KAAK,EAAE;EACxB,OAAOA,KAAK,KAAKF,SAAS;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,KAAKA,CAACX,KAAK,EAAE;EAClB,OAAOA,KAAK,IAAI,IAAI;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,OAAOA,CAACZ,KAAK,EAAE;EACpB,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKF,SAAS,EACrC,OAAO,IAAI;EACf,IAAIC,QAAQ,CAACC,KAAK,CAAC,KAAKQ,KAAK,CAACR,KAAK,CAAC,IAAIS,KAAK,CAACT,KAAK,CAAC,CAAC,EAAE;IACnD,OAAO,CAACA,KAAK,CAACa,IAAI;EACtB;EACA,IAAId,QAAQ,CAACC,KAAK,CAAC,KAAKG,QAAQ,CAACH,KAAK,CAAC,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,CAAC,EAAE;IAC9D,OAAO,CAACA,KAAK,CAACc,MAAM;EACxB;EACA,KAAK,MAAMC,GAAG,IAAIf,KAAK,EAAE;IACrB,IAAIgB,GAAG,CAAChB,KAAK,EAAEe,GAAG,CAAC,EAAE;MACjB,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,YAAYA,CAACjB,KAAK,EAAE;EACzB,OAAOC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAIY,OAAO,CAACZ,KAAK,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkB,cAAcA,CAAC,GAAGC,MAAM,EAAE;EAC/B,OAAOA,MAAM,CAACC,KAAK,CAACH,YAAY,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,aAAaA,CAACrB,KAAK,EAAE;EAC1B,OAAOD,QAAQ,CAACC,KAAK,CAAC,IAAIY,OAAO,CAACZ,KAAK,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsB,eAAeA,CAAC,GAAGH,MAAM,EAAE;EAChC,OAAOA,MAAM,CAACC,KAAK,CAACC,aAAa,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,UAAUA,CAACvB,KAAK,EAAE;EACvB,OAAQG,QAAQ,CAACH,KAAK,CAAC,GAAGA,KAAK,CAACwB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGzB,KAAK,CAAC0B,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASV,GAAGA,CAAChB,KAAK,EAAEe,GAAG,EAAE;EACrB,OAAOf,KAAK,IAAI,IAAI,IAAII,MAAM,CAACC,SAAS,CAACsB,cAAc,CAACpB,IAAI,CAACP,KAAK,EAAEe,GAAG,CAAC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,UAAUA,CAAC5B,KAAK,EAAE;EACvB,OAAO,OAAOA,KAAK,KAAK,UAAU;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6B,iBAAiB,GAAGA,CAACC,IAAI,EAAEC,QAAQ,KAAK;EAC1C,OAAOA,QAAQ,GAAG,GAAGD,IAAI,KAAKC,QAAQ,EAAE,GAAG,EAAE;AACjD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,GAAGA,CAACF,IAAI,EAAEC,QAAQ,EAAEE,IAAI,KAAK;EACtD,OAAOA,IAAI,GAAG,GAAGH,IAAI,KAAKC,QAAQ,EAAE,GAAG,EAAE;AAC7C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,YAAYA,CAACf,MAAM,EAAEgB,QAAQ,EAAE;EACpC,OAAOhB,MAAM,CAACiB,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,EAAEC,KAAK,KAAK,GAAGF,GAAG,GAAGlC,QAAQ,CAACmC,IAAI,CAAC,GAAGH,QAAQ,CAACG,IAAI,EAAEC,KAAK,EAAEpB,MAAM,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;AAClH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqB,IAAIA,CAAC,GAAGC,CAAC,EAAE;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,SAAS,EAAE,GAAGC,MAAM,EAAE;EACpC,MAAMC,SAAS,GAAG,CAAC,CAACD,MAAM,EAAE9B,MAAM;EAClC,IAAI+B,SAAS,EAAE;IACX;IACAC,OAAO,CAACC,cAAc,CAACJ,SAAS,CAAC;IACjCC,MAAM,EAAEI,OAAO,CAAEC,KAAK,IAAK;MACvB;MACAH,OAAO,CAACI,GAAG,CAACD,KAAK,CAAC;IACtB,CAAC,CAAC;IACF;IACAH,OAAO,CAACK,QAAQ,CAAC,CAAC;EACtB,CAAC,MACI;IACD;IACAL,OAAO,CAACI,GAAG,CAACP,SAAS,CAAC;EAC1B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,WAAWA,CAACC,GAAG,EAAEC,SAAS,EAAE;EACjC,MAAMC,IAAI,GAAG,CAAC,CAAC;EACf,MAAMC,KAAK,GAAG,CAAC,CAAC;EAChBpD,MAAM,CAACqD,OAAO,CAACJ,GAAG,CAAC,CAACL,OAAO,CAAC,CAAC,CAACjC,GAAG,EAAEf,KAAK,CAAC,KAAK;IAC1C,IAAIsD,SAAS,CAACvC,GAAG,CAAC,EAAE;MAChBwC,IAAI,CAACxC,GAAG,CAAC,GAAGf,KAAK;IACrB,CAAC,MACI;MACDwD,KAAK,CAACzC,GAAG,CAAC,GAAGf,KAAK;IACtB;EACJ,CAAC,CAAC;EACF,OAAO,CAACuD,IAAI,EAAEC,KAAK,CAAC;AACxB;AACA,MAAME,SAAS,GAAIL,GAAG,IAAK;EACvB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKvD,SAAS,IAAI,OAAOuD,GAAG,KAAK,QAAQ,EAAE;IAC9D,OAAOA,GAAG;EACd;EACA,IAAIA,GAAG,YAAYpD,KAAK,EAAE;IACtB,OAAOoD,GAAG,CAACjB,MAAM,CAAC,CAACuB,GAAG,EAAEC,IAAI,EAAEC,CAAC,KAAK;MAChCF,GAAG,CAACE,CAAC,CAAC,GAAGH,SAAS,CAACE,IAAI,CAAC;MACxB,OAAOD,GAAG;IACd,CAAC,EAAE,EAAE,CAAC;EACV;EACA,IAAIN,GAAG,YAAYjD,MAAM,EAAE;IACvB,OAAOA,MAAM,CAAC0D,IAAI,CAACT,GAAG,IAAI,CAAC,CAAC,CAAC,CAACjB,MAAM,CAAC,CAAC2B,KAAK,EAAEhD,GAAG,KAAK;MACjDgD,KAAK,CAAChD,GAAG,CAAC,GAAG2C,SAAS,CAACL,GAAG,CAACtC,GAAG,CAAC,CAAC;MAChC,OAAOgD,KAAK;IAChB,CAAC,EAAE,CAAC,CAAC,CAAC;EACV;AACJ,CAAC;AAED,SAAS7C,cAAc,EAAEI,eAAe,EAAEC,UAAU,EAAEM,iBAAiB,EAAEG,uBAAuB,EAAE0B,SAAS,EAAEhB,QAAQ,EAAE1B,GAAG,EAAEJ,OAAO,EAAES,aAAa,EAAEO,UAAU,EAAEpB,KAAK,EAAEG,KAAK,EAAEZ,QAAQ,EAAEU,KAAK,EAAEN,QAAQ,EAAEO,WAAW,EAAE8B,IAAI,EAAE9C,uBAAuB,EAAE0D,WAAW,EAAElB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}