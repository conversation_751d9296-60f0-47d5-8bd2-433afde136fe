{"ast": null, "code": "import '../../../types/authenticator/user.mjs';\nimport { isAuthFieldsWithDefaults } from '../../../types/authenticator/attributes.mjs';\nimport { getActorContext } from '../actor.mjs';\nimport { defaultFormFieldOptions } from '../constants.mjs';\nimport { defaultFormFieldsGetters, getAliasDefaultFormField } from './defaults.mjs';\nimport { applyTranslation, sortFormFields } from './utils.mjs';\n\n// Gets the default formFields for given route\nconst getDefaultFormFields = (route, state) => {\n  const formFieldGetter = defaultFormFieldsGetters[route];\n  return formFieldGetter(state);\n};\n// Gets custom formFields, and applies default values\nconst getCustomFormFields = (route, state) => {\n  const customFormFields = getActorContext(state)?.formFields?.[route];\n  if (!customFormFields || Object.keys(customFormFields).length === 0) {\n    return {};\n  }\n  return Object.entries(customFormFields).reduce((acc, [fieldName, customOptions]) => {\n    if ((route === 'signIn' || route === 'forgotPassword') && fieldName === 'username') {\n      // Unlike other screens, `signIn` and `forgotPassword` screens default login\n      // alias field names to \"username\", even if it's a phone number or email.\n      // In this case, we get the default formFieldOptions based on loginMechanism.\n      const defaultOptions = getAliasDefaultFormField(state);\n      // apply default to fill any gaps that are not present in customOptions\n      const mergedOptions = {\n        ...defaultOptions,\n        ...customOptions\n      };\n      return {\n        ...acc,\n        [fieldName]: mergedOptions\n      };\n    } else if (isAuthFieldsWithDefaults(fieldName)) {\n      // if this field is a known auth attribute that we have defaults for,\n      // apply defaults to customOptions.\n      const defaultOptions = defaultFormFieldOptions[fieldName];\n      const mergedOptions = {\n        ...defaultOptions,\n        ...customOptions\n      };\n      return {\n        ...acc,\n        [fieldName]: mergedOptions\n      };\n    } else {\n      // if this is not a known field, use customOptions as is.\n      return {\n        ...acc,\n        [fieldName]: customOptions\n      };\n    }\n  }, {});\n};\nconst getFormFields = (route, state) => {\n  const defaultFormFields = getDefaultFormFields(route, state);\n  const customFormFields = getCustomFormFields(route, state);\n  const formFields = {\n    ...defaultFormFields,\n    ...customFormFields\n  };\n  delete formFields['QR'];\n  return applyTranslation(formFields);\n};\nconst removeOrderKeys = formFields => formFields.map(field => {\n  const key = field[0];\n  // Drop order key to prevent passing to form field UI components\n  const values = {\n    ...field[1],\n    order: undefined\n  };\n  return [key, values];\n});\n/** Calls `getFormFields` above, then sorts it into an indexed array */\nconst getSortedFormFields = (route, state) => {\n  const formFields = getFormFields(route, state);\n  return removeOrderKeys(sortFormFields(formFields));\n};\nexport { getCustomFormFields, getDefaultFormFields, getFormFields, getSortedFormFields, removeOrderKeys };", "map": {"version": 3, "names": ["isAuthFieldsWithDefaults", "getActorContext", "defaultFormFieldOptions", "defaultFormFieldsGetters", "getAliasDefaultFormField", "applyTranslation", "sortF<PERSON><PERSON>ields", "getDefaultFormFields", "route", "state", "formFieldGetter", "getCustom<PERSON>orm<PERSON><PERSON>s", "customFormFields", "formFields", "Object", "keys", "length", "entries", "reduce", "acc", "fieldName", "customOptions", "defaultOptions", "mergedOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON>s", "defaultFormFields", "remove<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "field", "key", "values", "order", "undefined", "getS<PERSON><PERSON><PERSON><PERSON><PERSON>s"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/helpers/authenticator/formFields/formFields.mjs"], "sourcesContent": ["import '../../../types/authenticator/user.mjs';\nimport { isAuthFieldsWithDefaults } from '../../../types/authenticator/attributes.mjs';\nimport { getActorContext } from '../actor.mjs';\nimport { defaultFormFieldOptions } from '../constants.mjs';\nimport { defaultFormFieldsGetters, getAliasDefaultFormField } from './defaults.mjs';\nimport { applyTranslation, sortFormFields } from './utils.mjs';\n\n// Gets the default formFields for given route\nconst getDefaultFormFields = (route, state) => {\n    const formFieldGetter = defaultFormFieldsGetters[route];\n    return formFieldGetter(state);\n};\n// Gets custom formFields, and applies default values\nconst getCustomFormFields = (route, state) => {\n    const customFormFields = getActorContext(state)?.formFields?.[route];\n    if (!customFormFields || Object.keys(customFormFields).length === 0) {\n        return {};\n    }\n    return Object.entries(customFormFields).reduce((acc, [fieldName, customOptions]) => {\n        if ((route === 'signIn' || route === 'forgotPassword') &&\n            fieldName === 'username') {\n            // Unlike other screens, `signIn` and `forgotPassword` screens default login\n            // alias field names to \"username\", even if it's a phone number or email.\n            // In this case, we get the default formFieldOptions based on loginMechanism.\n            const defaultOptions = getAliasDefaultFormField(state);\n            // apply default to fill any gaps that are not present in customOptions\n            const mergedOptions = { ...defaultOptions, ...customOptions };\n            return { ...acc, [fieldName]: mergedOptions };\n        }\n        else if (isAuthFieldsWithDefaults(fieldName)) {\n            // if this field is a known auth attribute that we have defaults for,\n            // apply defaults to customOptions.\n            const defaultOptions = defaultFormFieldOptions[fieldName];\n            const mergedOptions = { ...defaultOptions, ...customOptions };\n            return { ...acc, [fieldName]: mergedOptions };\n        }\n        else {\n            // if this is not a known field, use customOptions as is.\n            return { ...acc, [fieldName]: customOptions };\n        }\n    }, {});\n};\nconst getFormFields = (route, state) => {\n    const defaultFormFields = getDefaultFormFields(route, state);\n    const customFormFields = getCustomFormFields(route, state);\n    const formFields = { ...defaultFormFields, ...customFormFields };\n    delete formFields['QR'];\n    return applyTranslation(formFields);\n};\nconst removeOrderKeys = (formFields) => formFields.map((field) => {\n    const key = field[0];\n    // Drop order key to prevent passing to form field UI components\n    const values = { ...field[1], order: undefined };\n    return [key, values];\n});\n/** Calls `getFormFields` above, then sorts it into an indexed array */\nconst getSortedFormFields = (route, state) => {\n    const formFields = getFormFields(route, state);\n    return removeOrderKeys(sortFormFields(formFields));\n};\n\nexport { getCustomFormFields, getDefaultFormFields, getFormFields, getSortedFormFields, removeOrderKeys };\n"], "mappings": "AAAA,OAAO,uCAAuC;AAC9C,SAASA,wBAAwB,QAAQ,6CAA6C;AACtF,SAASC,eAAe,QAAQ,cAAc;AAC9C,SAASC,uBAAuB,QAAQ,kBAAkB;AAC1D,SAASC,wBAAwB,EAAEC,wBAAwB,QAAQ,gBAAgB;AACnF,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,aAAa;;AAE9D;AACA,MAAMC,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;EAC3C,MAAMC,eAAe,GAAGP,wBAAwB,CAACK,KAAK,CAAC;EACvD,OAAOE,eAAe,CAACD,KAAK,CAAC;AACjC,CAAC;AACD;AACA,MAAME,mBAAmB,GAAGA,CAACH,KAAK,EAAEC,KAAK,KAAK;EAC1C,MAAMG,gBAAgB,GAAGX,eAAe,CAACQ,KAAK,CAAC,EAAEI,UAAU,GAAGL,KAAK,CAAC;EACpE,IAAI,CAACI,gBAAgB,IAAIE,MAAM,CAACC,IAAI,CAACH,gBAAgB,CAAC,CAACI,MAAM,KAAK,CAAC,EAAE;IACjE,OAAO,CAAC,CAAC;EACb;EACA,OAAOF,MAAM,CAACG,OAAO,CAACL,gBAAgB,CAAC,CAACM,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,SAAS,EAAEC,aAAa,CAAC,KAAK;IAChF,IAAI,CAACb,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,gBAAgB,KACjDY,SAAS,KAAK,UAAU,EAAE;MAC1B;MACA;MACA;MACA,MAAME,cAAc,GAAGlB,wBAAwB,CAACK,KAAK,CAAC;MACtD;MACA,MAAMc,aAAa,GAAG;QAAE,GAAGD,cAAc;QAAE,GAAGD;MAAc,CAAC;MAC7D,OAAO;QAAE,GAAGF,GAAG;QAAE,CAACC,SAAS,GAAGG;MAAc,CAAC;IACjD,CAAC,MACI,IAAIvB,wBAAwB,CAACoB,SAAS,CAAC,EAAE;MAC1C;MACA;MACA,MAAME,cAAc,GAAGpB,uBAAuB,CAACkB,SAAS,CAAC;MACzD,MAAMG,aAAa,GAAG;QAAE,GAAGD,cAAc;QAAE,GAAGD;MAAc,CAAC;MAC7D,OAAO;QAAE,GAAGF,GAAG;QAAE,CAACC,SAAS,GAAGG;MAAc,CAAC;IACjD,CAAC,MACI;MACD;MACA,OAAO;QAAE,GAAGJ,GAAG;QAAE,CAACC,SAAS,GAAGC;MAAc,CAAC;IACjD;EACJ,CAAC,EAAE,CAAC,CAAC,CAAC;AACV,CAAC;AACD,MAAMG,aAAa,GAAGA,CAAChB,KAAK,EAAEC,KAAK,KAAK;EACpC,MAAMgB,iBAAiB,GAAGlB,oBAAoB,CAACC,KAAK,EAAEC,KAAK,CAAC;EAC5D,MAAMG,gBAAgB,GAAGD,mBAAmB,CAACH,KAAK,EAAEC,KAAK,CAAC;EAC1D,MAAMI,UAAU,GAAG;IAAE,GAAGY,iBAAiB;IAAE,GAAGb;EAAiB,CAAC;EAChE,OAAOC,UAAU,CAAC,IAAI,CAAC;EACvB,OAAOR,gBAAgB,CAACQ,UAAU,CAAC;AACvC,CAAC;AACD,MAAMa,eAAe,GAAIb,UAAU,IAAKA,UAAU,CAACc,GAAG,CAAEC,KAAK,IAAK;EAC9D,MAAMC,GAAG,GAAGD,KAAK,CAAC,CAAC,CAAC;EACpB;EACA,MAAME,MAAM,GAAG;IAAE,GAAGF,KAAK,CAAC,CAAC,CAAC;IAAEG,KAAK,EAAEC;EAAU,CAAC;EAChD,OAAO,CAACH,GAAG,EAAEC,MAAM,CAAC;AACxB,CAAC,CAAC;AACF;AACA,MAAMG,mBAAmB,GAAGA,CAACzB,KAAK,EAAEC,KAAK,KAAK;EAC1C,MAAMI,UAAU,GAAGW,aAAa,CAAChB,KAAK,EAAEC,KAAK,CAAC;EAC9C,OAAOiB,eAAe,CAACpB,cAAc,CAACO,UAAU,CAAC,CAAC;AACtD,CAAC;AAED,SAASF,mBAAmB,EAAEJ,oBAAoB,EAAEiB,aAAa,EAAES,mBAAmB,EAAEP,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}