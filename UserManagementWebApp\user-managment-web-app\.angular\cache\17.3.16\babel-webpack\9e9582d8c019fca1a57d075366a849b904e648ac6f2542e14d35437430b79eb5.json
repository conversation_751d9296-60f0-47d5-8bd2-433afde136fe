{"ast": null, "code": "import { KEY_TYPE_IDENTIFIER, SIGNATURE_IDENTIFIER } from '../constants.mjs';\nimport { getHashedData } from './dataHashHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns a signing key to be used for signing requests.\n *\n * @param secretAccessKey AWS secret access key from credentials.\n * @param date Current date in the format 'YYYYMMDD'.\n * @param region AWS region in which the service resides.\n * @param service Service to which the signed request is being sent.\n *\n * @returns `Uint8Array` calculated from its composite parts.\n *\n * @internal\n */\nconst getSigningKey = (secretAccessKey, date, region, service) => {\n  const key = `${SIGNATURE_IDENTIFIER}${secretAccessKey}`;\n  const dateKey = getHashedData(key, date);\n  const regionKey = getHashedData(dateKey, region);\n  const serviceKey = getHashedData(regionKey, service);\n  const signingKey = getHashedData(serviceKey, KEY_TYPE_IDENTIFIER);\n  return signingKey;\n};\nexport { getSigningKey };", "map": {"version": 3, "names": ["KEY_TYPE_IDENTIFIER", "SIGNATURE_IDENTIFIER", "getHashedData", "getSigningKey", "secretAccessKey", "date", "region", "service", "key", "<PERSON><PERSON><PERSON>", "regionKey", "serviceKey", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/signer/signatureV4/utils/getSigningKey.mjs"], "sourcesContent": ["import { KEY_TYPE_IDENTIFIER, SIGNATURE_IDENTIFIER } from '../constants.mjs';\nimport { getHashedData } from './dataHashHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns a signing key to be used for signing requests.\n *\n * @param secretAccessKey AWS secret access key from credentials.\n * @param date Current date in the format 'YYYYMMDD'.\n * @param region AWS region in which the service resides.\n * @param service Service to which the signed request is being sent.\n *\n * @returns `Uint8Array` calculated from its composite parts.\n *\n * @internal\n */\nconst getSigningKey = (secretAccessKey, date, region, service) => {\n    const key = `${SIGNATURE_IDENTIFIER}${secretAccessKey}`;\n    const dateKey = getHashedData(key, date);\n    const regionKey = getHashedData(dateKey, region);\n    const serviceKey = getHashedData(regionKey, service);\n    const signingKey = getHashedData(serviceKey, KEY_TYPE_IDENTIFIER);\n    return signingKey;\n};\n\nexport { getSigningKey };\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,oBAAoB,QAAQ,kBAAkB;AAC5E,SAASC,aAAa,QAAQ,uBAAuB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAGA,CAACC,eAAe,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,KAAK;EAC9D,MAAMC,GAAG,GAAG,GAAGP,oBAAoB,GAAGG,eAAe,EAAE;EACvD,MAAMK,OAAO,GAAGP,aAAa,CAACM,GAAG,EAAEH,IAAI,CAAC;EACxC,MAAMK,SAAS,GAAGR,aAAa,CAACO,OAAO,EAAEH,MAAM,CAAC;EAChD,MAAMK,UAAU,GAAGT,aAAa,CAACQ,SAAS,EAAEH,OAAO,CAAC;EACpD,MAAMK,UAAU,GAAGV,aAAa,CAACS,UAAU,EAAEX,mBAAmB,CAAC;EACjE,OAAOY,UAAU;AACrB,CAAC;AAED,SAAST,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}