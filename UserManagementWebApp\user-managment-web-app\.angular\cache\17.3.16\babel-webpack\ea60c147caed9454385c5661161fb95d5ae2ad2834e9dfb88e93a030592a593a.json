{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst isNonRetryableError = obj => {\n  const key = 'nonRetryable';\n  return obj && obj[key];\n};\nexport { isNonRetryableError };", "map": {"version": 3, "names": ["isNonRetryableError", "obj", "key"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/retry/isNonRetryableError.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst isNonRetryableError = (obj) => {\n    const key = 'nonRetryable';\n    return obj && obj[key];\n};\n\nexport { isNonRetryableError };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,mBAAmB,GAAIC,GAAG,IAAK;EACjC,MAAMC,GAAG,GAAG,cAAc;EAC1B,OAAOD,GAAG,IAAIA,GAAG,CAACC,GAAG,CAAC;AAC1B,CAAC;AAED,SAASF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}