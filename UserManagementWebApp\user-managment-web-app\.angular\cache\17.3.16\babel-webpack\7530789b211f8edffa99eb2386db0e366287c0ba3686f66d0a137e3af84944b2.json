{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AuthError } from '../../../../../errors/AuthError.mjs';\nimport { textEncoder } from '../../textEncoder/index.mjs';\nimport BigInteger from '../BigInteger/BigInteger.mjs';\nimport { calculateS } from '../calculate/calculateS.mjs';\nimport { calculateU } from '../calculate/calculateU.mjs';\nimport { getBytesFromHex } from '../getBytesFromHex.mjs';\nimport { getHashFromData } from '../getHashFromData.mjs';\nimport { getHashFromHex } from '../getHashFromHex.mjs';\nimport { getHexFromBytes } from '../getHexFromBytes.mjs';\nimport { getHkdfKey } from '../getHkdfKey.mjs';\nimport { getPaddedHex } from '../getPaddedHex.mjs';\nimport { getRandomBytes } from '../getRandomBytes.mjs';\nimport { getRandomString } from '../getRandomString.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/** @class */\nclass AuthenticationHelper {\n  constructor({\n    userPoolName,\n    a,\n    g,\n    A,\n    N\n  }) {\n    this.encoder = textEncoder;\n    this.userPoolName = userPoolName;\n    this.a = a;\n    this.g = g;\n    this.A = A;\n    this.N = N;\n    this.k = new BigInteger(getHashFromHex(`${getPaddedHex(N)}${getPaddedHex(g)}`), 16);\n  }\n  /**\n   * @returns {string} Generated random value included in password hash.\n   */\n  getRandomPassword() {\n    if (!this.randomPassword) {\n      throw new AuthError({\n        name: 'EmptyBigIntegerRandomPassword',\n        message: 'random password is empty'\n      });\n    }\n    return this.randomPassword;\n  }\n  /**\n   * @returns {string} Generated random value included in devices hash.\n   */\n  getSaltToHashDevices() {\n    if (!this.saltToHashDevices) {\n      throw new AuthError({\n        name: 'EmptyBigIntegersaltToHashDevices',\n        message: 'saltToHashDevices is empty'\n      });\n    }\n    return this.saltToHashDevices;\n  }\n  /**\n   * @returns {string} Value used to verify devices.\n   */\n  getVerifierDevices() {\n    if (!this.verifierDevices) {\n      throw new AuthError({\n        name: 'EmptyBigIntegerVerifierDevices',\n        message: 'verifyDevices is empty'\n      });\n    }\n    return this.verifierDevices;\n  }\n  /**\n   * Generate salts and compute verifier.\n   *\n   * @param {string} deviceGroupKey Devices to generate verifier for.\n   * @param {string} username User to generate verifier for.\n   *\n   * @returns {Promise<void>}\n   */\n  generateHashDevice(deviceGroupKey, username) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.randomPassword = getRandomString();\n      const combinedString = `${deviceGroupKey}${username}:${_this.randomPassword}`;\n      const hashedString = getHashFromData(combinedString);\n      const hexRandom = getHexFromBytes(getRandomBytes(16));\n      // The random hex will be unambiguously represented as a postive integer\n      _this.saltToHashDevices = getPaddedHex(new BigInteger(hexRandom, 16));\n      return new Promise((resolve, reject) => {\n        _this.g.modPow(new BigInteger(getHashFromHex(_this.saltToHashDevices + hashedString), 16), _this.N, (err, result) => {\n          if (err) {\n            reject(err);\n            return;\n          }\n          _this.verifierDevices = getPaddedHex(result);\n          resolve();\n        });\n      });\n    })();\n  }\n  /**\n   * Calculates the final HKDF key based on computed S value, computed U value and the key\n   *\n   * @param {String} username Username.\n   * @param {String} password Password.\n   * @param {AuthBigInteger} B Server B value.\n   * @param {AuthBigInteger} salt Generated salt.\n   */\n  getPasswordAuthenticationKey({\n    username,\n    password,\n    serverBValue,\n    salt\n  }) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (serverBValue.mod(_this2.N).equals(BigInteger.ZERO)) {\n        throw new Error('B cannot be zero.');\n      }\n      const U = calculateU({\n        A: _this2.A,\n        B: serverBValue\n      });\n      const usernamePassword = `${_this2.userPoolName}${username}:${password}`;\n      const usernamePasswordHash = getHashFromData(usernamePassword);\n      const x = new BigInteger(getHashFromHex(getPaddedHex(salt) + usernamePasswordHash), 16);\n      const S = yield calculateS({\n        a: _this2.a,\n        g: _this2.g,\n        k: _this2.k,\n        x,\n        B: serverBValue,\n        N: _this2.N,\n        U\n      });\n      const context = _this2.encoder.convert('Caldera Derived Key');\n      const spacer = _this2.encoder.convert(String.fromCharCode(1));\n      const info = new Uint8Array(context.byteLength + spacer.byteLength);\n      info.set(context, 0);\n      info.set(spacer, context.byteLength);\n      const hkdfKey = getHkdfKey(getBytesFromHex(getPaddedHex(S)), getBytesFromHex(getPaddedHex(U)), info);\n      return hkdfKey;\n    })();\n  }\n}\nexport { AuthenticationHelper as default };", "map": {"version": 3, "names": ["<PERSON>th<PERSON><PERSON><PERSON>", "textEncoder", "BigInteger", "calculateS", "calculateU", "getBytesFromHex", "getHashFromData", "getHashFromHex", "getHexFromBytes", "getHkdfKey", "getPaddedHex", "getRandomBytes", "getRandomString", "AuthenticationHelper", "constructor", "userPoolName", "a", "g", "A", "N", "encoder", "k", "getRandomPassword", "randomPassword", "name", "message", "getSaltToHashDevices", "saltToHashDevices", "getVerifierDevices", "verifierDevices", "generateHashDevice", "deviceGroupKey", "username", "_this", "_asyncToGenerator", "combinedString", "hashedString", "hexRandom", "Promise", "resolve", "reject", "modPow", "err", "result", "getPasswordAuthenticationKey", "password", "serverBValue", "salt", "_this2", "mod", "equals", "ZERO", "Error", "U", "B", "usernamePassword", "usernamePasswordHash", "x", "S", "context", "convert", "spacer", "String", "fromCharCode", "info", "Uint8Array", "byteLength", "set", "hkdfKey", "default"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/srp/AuthenticationHelper/AuthenticationHelper.mjs"], "sourcesContent": ["import { AuthError } from '../../../../../errors/AuthError.mjs';\nimport { textEncoder } from '../../textEncoder/index.mjs';\nimport BigInteger from '../BigInteger/BigInteger.mjs';\nimport { calculateS } from '../calculate/calculateS.mjs';\nimport { calculateU } from '../calculate/calculateU.mjs';\nimport { getBytesFromHex } from '../getBytesFromHex.mjs';\nimport { getHashFromData } from '../getHashFromData.mjs';\nimport { getHashFromHex } from '../getHashFromHex.mjs';\nimport { getHexFromBytes } from '../getHexFromBytes.mjs';\nimport { getHkdfKey } from '../getHkdfKey.mjs';\nimport { getPaddedHex } from '../getPaddedHex.mjs';\nimport { getRandomBytes } from '../getRandomBytes.mjs';\nimport { getRandomString } from '../getRandomString.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/** @class */\nclass AuthenticationHelper {\n    constructor({ userPoolName, a, g, A, N, }) {\n        this.encoder = textEncoder;\n        this.userPoolName = userPoolName;\n        this.a = a;\n        this.g = g;\n        this.A = A;\n        this.N = N;\n        this.k = new BigInteger(getHashFromHex(`${getPaddedHex(N)}${getPaddedHex(g)}`), 16);\n    }\n    /**\n     * @returns {string} Generated random value included in password hash.\n     */\n    getRandomPassword() {\n        if (!this.randomPassword) {\n            throw new AuthError({\n                name: 'EmptyBigIntegerRandomPassword',\n                message: 'random password is empty',\n            });\n        }\n        return this.randomPassword;\n    }\n    /**\n     * @returns {string} Generated random value included in devices hash.\n     */\n    getSaltToHashDevices() {\n        if (!this.saltToHashDevices) {\n            throw new AuthError({\n                name: 'EmptyBigIntegersaltToHashDevices',\n                message: 'saltToHashDevices is empty',\n            });\n        }\n        return this.saltToHashDevices;\n    }\n    /**\n     * @returns {string} Value used to verify devices.\n     */\n    getVerifierDevices() {\n        if (!this.verifierDevices) {\n            throw new AuthError({\n                name: 'EmptyBigIntegerVerifierDevices',\n                message: 'verifyDevices is empty',\n            });\n        }\n        return this.verifierDevices;\n    }\n    /**\n     * Generate salts and compute verifier.\n     *\n     * @param {string} deviceGroupKey Devices to generate verifier for.\n     * @param {string} username User to generate verifier for.\n     *\n     * @returns {Promise<void>}\n     */\n    async generateHashDevice(deviceGroupKey, username) {\n        this.randomPassword = getRandomString();\n        const combinedString = `${deviceGroupKey}${username}:${this.randomPassword}`;\n        const hashedString = getHashFromData(combinedString);\n        const hexRandom = getHexFromBytes(getRandomBytes(16));\n        // The random hex will be unambiguously represented as a postive integer\n        this.saltToHashDevices = getPaddedHex(new BigInteger(hexRandom, 16));\n        return new Promise((resolve, reject) => {\n            this.g.modPow(new BigInteger(getHashFromHex(this.saltToHashDevices + hashedString), 16), this.N, (err, result) => {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                this.verifierDevices = getPaddedHex(result);\n                resolve();\n            });\n        });\n    }\n    /**\n     * Calculates the final HKDF key based on computed S value, computed U value and the key\n     *\n     * @param {String} username Username.\n     * @param {String} password Password.\n     * @param {AuthBigInteger} B Server B value.\n     * @param {AuthBigInteger} salt Generated salt.\n     */\n    async getPasswordAuthenticationKey({ username, password, serverBValue, salt, }) {\n        if (serverBValue.mod(this.N).equals(BigInteger.ZERO)) {\n            throw new Error('B cannot be zero.');\n        }\n        const U = calculateU({\n            A: this.A,\n            B: serverBValue,\n        });\n        const usernamePassword = `${this.userPoolName}${username}:${password}`;\n        const usernamePasswordHash = getHashFromData(usernamePassword);\n        const x = new BigInteger(getHashFromHex(getPaddedHex(salt) + usernamePasswordHash), 16);\n        const S = await calculateS({\n            a: this.a,\n            g: this.g,\n            k: this.k,\n            x,\n            B: serverBValue,\n            N: this.N,\n            U,\n        });\n        const context = this.encoder.convert('Caldera Derived Key');\n        const spacer = this.encoder.convert(String.fromCharCode(1));\n        const info = new Uint8Array(context.byteLength + spacer.byteLength);\n        info.set(context, 0);\n        info.set(spacer, context.byteLength);\n        const hkdfKey = getHkdfKey(getBytesFromHex(getPaddedHex(S)), getBytesFromHex(getPaddedHex(U)), info);\n        return hkdfKey;\n    }\n}\n\nexport { AuthenticationHelper as default };\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,qCAAqC;AAC/D,SAASC,WAAW,QAAQ,6BAA6B;AACzD,OAAOC,UAAU,MAAM,8BAA8B;AACrD,SAASC,UAAU,QAAQ,6BAA6B;AACxD,SAASC,UAAU,QAAQ,6BAA6B;AACxD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,eAAe,QAAQ,wBAAwB;;AAExD;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACvBC,WAAWA,CAAC;IAAEC,YAAY;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEC;EAAG,CAAC,EAAE;IACvC,IAAI,CAACC,OAAO,GAAGnB,WAAW;IAC1B,IAAI,CAACc,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACE,CAAC,GAAG,IAAInB,UAAU,CAACK,cAAc,CAAC,GAAGG,YAAY,CAACS,CAAC,CAAC,GAAGT,YAAY,CAACO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EACvF;EACA;AACJ;AACA;EACIK,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE;MACtB,MAAM,IAAIvB,SAAS,CAAC;QAChBwB,IAAI,EAAE,+BAA+B;QACrCC,OAAO,EAAE;MACb,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAACF,cAAc;EAC9B;EACA;AACJ;AACA;EACIG,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACC,iBAAiB,EAAE;MACzB,MAAM,IAAI3B,SAAS,CAAC;QAChBwB,IAAI,EAAE,kCAAkC;QACxCC,OAAO,EAAE;MACb,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAACE,iBAAiB;EACjC;EACA;AACJ;AACA;EACIC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MACvB,MAAM,IAAI7B,SAAS,CAAC;QAChBwB,IAAI,EAAE,gCAAgC;QACtCC,OAAO,EAAE;MACb,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAACI,eAAe;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUC,kBAAkBA,CAACC,cAAc,EAAEC,QAAQ,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC/CD,KAAI,CAACV,cAAc,GAAGX,eAAe,CAAC,CAAC;MACvC,MAAMuB,cAAc,GAAG,GAAGJ,cAAc,GAAGC,QAAQ,IAAIC,KAAI,CAACV,cAAc,EAAE;MAC5E,MAAMa,YAAY,GAAG9B,eAAe,CAAC6B,cAAc,CAAC;MACpD,MAAME,SAAS,GAAG7B,eAAe,CAACG,cAAc,CAAC,EAAE,CAAC,CAAC;MACrD;MACAsB,KAAI,CAACN,iBAAiB,GAAGjB,YAAY,CAAC,IAAIR,UAAU,CAACmC,SAAS,EAAE,EAAE,CAAC,CAAC;MACpE,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACpCP,KAAI,CAAChB,CAAC,CAACwB,MAAM,CAAC,IAAIvC,UAAU,CAACK,cAAc,CAAC0B,KAAI,CAACN,iBAAiB,GAAGS,YAAY,CAAC,EAAE,EAAE,CAAC,EAAEH,KAAI,CAACd,CAAC,EAAE,CAACuB,GAAG,EAAEC,MAAM,KAAK;UAC9G,IAAID,GAAG,EAAE;YACLF,MAAM,CAACE,GAAG,CAAC;YACX;UACJ;UACAT,KAAI,CAACJ,eAAe,GAAGnB,YAAY,CAACiC,MAAM,CAAC;UAC3CJ,OAAO,CAAC,CAAC;QACb,CAAC,CAAC;MACN,CAAC,CAAC;IAAC;EACP;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUK,4BAA4BA,CAAC;IAAEZ,QAAQ;IAAEa,QAAQ;IAAEC,YAAY;IAAEC;EAAM,CAAC,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAd,iBAAA;MAC5E,IAAIY,YAAY,CAACG,GAAG,CAACD,MAAI,CAAC7B,CAAC,CAAC,CAAC+B,MAAM,CAAChD,UAAU,CAACiD,IAAI,CAAC,EAAE;QAClD,MAAM,IAAIC,KAAK,CAAC,mBAAmB,CAAC;MACxC;MACA,MAAMC,CAAC,GAAGjD,UAAU,CAAC;QACjBc,CAAC,EAAE8B,MAAI,CAAC9B,CAAC;QACToC,CAAC,EAAER;MACP,CAAC,CAAC;MACF,MAAMS,gBAAgB,GAAG,GAAGP,MAAI,CAACjC,YAAY,GAAGiB,QAAQ,IAAIa,QAAQ,EAAE;MACtE,MAAMW,oBAAoB,GAAGlD,eAAe,CAACiD,gBAAgB,CAAC;MAC9D,MAAME,CAAC,GAAG,IAAIvD,UAAU,CAACK,cAAc,CAACG,YAAY,CAACqC,IAAI,CAAC,GAAGS,oBAAoB,CAAC,EAAE,EAAE,CAAC;MACvF,MAAME,CAAC,SAASvD,UAAU,CAAC;QACvBa,CAAC,EAAEgC,MAAI,CAAChC,CAAC;QACTC,CAAC,EAAE+B,MAAI,CAAC/B,CAAC;QACTI,CAAC,EAAE2B,MAAI,CAAC3B,CAAC;QACToC,CAAC;QACDH,CAAC,EAAER,YAAY;QACf3B,CAAC,EAAE6B,MAAI,CAAC7B,CAAC;QACTkC;MACJ,CAAC,CAAC;MACF,MAAMM,OAAO,GAAGX,MAAI,CAAC5B,OAAO,CAACwC,OAAO,CAAC,qBAAqB,CAAC;MAC3D,MAAMC,MAAM,GAAGb,MAAI,CAAC5B,OAAO,CAACwC,OAAO,CAACE,MAAM,CAACC,YAAY,CAAC,CAAC,CAAC,CAAC;MAC3D,MAAMC,IAAI,GAAG,IAAIC,UAAU,CAACN,OAAO,CAACO,UAAU,GAAGL,MAAM,CAACK,UAAU,CAAC;MACnEF,IAAI,CAACG,GAAG,CAACR,OAAO,EAAE,CAAC,CAAC;MACpBK,IAAI,CAACG,GAAG,CAACN,MAAM,EAAEF,OAAO,CAACO,UAAU,CAAC;MACpC,MAAME,OAAO,GAAG3D,UAAU,CAACJ,eAAe,CAACK,YAAY,CAACgD,CAAC,CAAC,CAAC,EAAErD,eAAe,CAACK,YAAY,CAAC2C,CAAC,CAAC,CAAC,EAAEW,IAAI,CAAC;MACpG,OAAOI,OAAO;IAAC;EACnB;AACJ;AAEA,SAASvD,oBAAoB,IAAIwD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}