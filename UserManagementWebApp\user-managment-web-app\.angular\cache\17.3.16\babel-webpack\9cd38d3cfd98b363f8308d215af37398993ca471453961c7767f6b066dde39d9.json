{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { completeOAuthSignOut } from './completeOAuthSignOut.mjs';\nimport { oAuthSignOutRedirect } from './oAuthSignOutRedirect.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst handleOAuthSignOut = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (cognitoConfig, store, tokenOrchestrator, redirectUrl) {\n    const {\n      isOAuthSignIn\n    } = yield store.loadOAuthSignIn();\n    const oauthMetadata = yield tokenOrchestrator.getOAuthMetadata();\n    // Clear everything before attempting to visted logout endpoint since the current application\n    // state could be wiped away on redirect\n    yield completeOAuthSignOut(store);\n    // The isOAuthSignIn flag is propagated by the oAuthToken store which manages oauth keys in local storage only.\n    // These keys are used to determine if a user is in an inflight or signedIn oauth states.\n    // However, this behavior represents an issue when 2 apps share the same set of tokens in Cookie storage because the app that didn't\n    // start the OAuth will not have access to the oauth keys.\n    // A heuristic solution is to add oauth metadata to the tokenOrchestrator which will have access to the underlying\n    // storage mechanism that is used by Amplify.\n    if (isOAuthSignIn || oauthMetadata?.oauthSignIn) {\n      // On web, this will always end up being a void action\n      return oAuthSignOutRedirect(cognitoConfig, false, redirectUrl);\n    }\n  });\n  return function handleOAuthSignOut(_x, _x2, _x3, _x4) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { handleOAuthSignOut };", "map": {"version": 3, "names": ["completeOAuthSignOut", "oAuthSignOutRedirect", "handleOAuthSignOut", "_ref", "_asyncToGenerator", "cognitoConfig", "store", "tokenOrchestrator", "redirectUrl", "isOAuthSignIn", "loadOAuthSignIn", "oauthMetadata", "getOAuthMetadata", "oauthSignIn", "_x", "_x2", "_x3", "_x4", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/oauth/handleOAuthSignOut.mjs"], "sourcesContent": ["import { completeOAuthSignOut } from './completeOAuthSignOut.mjs';\nimport { oAuthSignOutRedirect } from './oAuthSignOutRedirect.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst handleOAuthSignOut = async (cognitoConfig, store, tokenOrchestrator, redirectUrl) => {\n    const { isOAuthSignIn } = await store.loadOAuthSignIn();\n    const oauthMetadata = await tokenOrchestrator.getOAuthMetadata();\n    // Clear everything before attempting to visted logout endpoint since the current application\n    // state could be wiped away on redirect\n    await completeOAuthSignOut(store);\n    // The isOAuthSignIn flag is propagated by the oAuthToken store which manages oauth keys in local storage only.\n    // These keys are used to determine if a user is in an inflight or signedIn oauth states.\n    // However, this behavior represents an issue when 2 apps share the same set of tokens in Cookie storage because the app that didn't\n    // start the OAuth will not have access to the oauth keys.\n    // A heuristic solution is to add oauth metadata to the tokenOrchestrator which will have access to the underlying\n    // storage mechanism that is used by Amplify.\n    if (isOAuthSignIn || oauthMetadata?.oauthSignIn) {\n        // On web, this will always end up being a void action\n        return oAuthSignOutRedirect(cognitoConfig, false, redirectUrl);\n    }\n};\n\nexport { handleOAuthSignOut };\n"], "mappings": ";AAAA,SAASA,oBAAoB,QAAQ,4BAA4B;AACjE,SAASC,oBAAoB,QAAQ,4BAA4B;;AAEjE;AACA;AACA,MAAMC,kBAAkB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,aAAa,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,WAAW,EAAK;IACvF,MAAM;MAAEC;IAAc,CAAC,SAASH,KAAK,CAACI,eAAe,CAAC,CAAC;IACvD,MAAMC,aAAa,SAASJ,iBAAiB,CAACK,gBAAgB,CAAC,CAAC;IAChE;IACA;IACA,MAAMZ,oBAAoB,CAACM,KAAK,CAAC;IACjC;IACA;IACA;IACA;IACA;IACA;IACA,IAAIG,aAAa,IAAIE,aAAa,EAAEE,WAAW,EAAE;MAC7C;MACA,OAAOZ,oBAAoB,CAACI,aAAa,EAAE,KAAK,EAAEG,WAAW,CAAC;IAClE;EACJ,CAAC;EAAA,gBAhBKN,kBAAkBA,CAAAY,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAd,IAAA,CAAAe,KAAA,OAAAC,SAAA;EAAA;AAAA,GAgBvB;AAED,SAASjB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}