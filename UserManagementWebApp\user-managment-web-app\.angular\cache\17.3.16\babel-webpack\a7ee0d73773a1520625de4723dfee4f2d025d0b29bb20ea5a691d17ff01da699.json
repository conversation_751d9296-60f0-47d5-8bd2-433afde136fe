{"ast": null, "code": "import { cryptoSecureRandomInt } from './cryptoSecureRandomInt.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Hex encoding strategy.\n * Converts a word array to a hex string.\n * @param {WordArray} wordArray The word array.\n * @return {string} The hex string.\n * @static\n */\nfunction hexStringify(wordArray) {\n  // Shortcuts\n  const {\n    words\n  } = wordArray;\n  const {\n    sigBytes\n  } = wordArray;\n  // Convert\n  const hexChars = [];\n  for (let i = 0; i < sigBytes; i++) {\n    const bite = words[i >>> 2] >>> 24 - i % 4 * 8 & 0xff;\n    hexChars.push((bite >>> 4).toString(16));\n    hexChars.push((bite & 0x0f).toString(16));\n  }\n  return hexChars.join('');\n}\nclass WordArray {\n  constructor(words, sigBytes) {\n    this.words = [];\n    let Words = words;\n    Words = this.words = Words || [];\n    if (sigBytes !== undefined) {\n      this.sigBytes = sigBytes;\n    } else {\n      this.sigBytes = Words.length * 4;\n    }\n  }\n  random(nBytes) {\n    const words = [];\n    for (let i = 0; i < nBytes; i += 4) {\n      words.push(cryptoSecureRandomInt());\n    }\n    return new WordArray(words, nBytes);\n  }\n  toString() {\n    return hexStringify(this);\n  }\n}\nexport { WordArray };", "map": {"version": 3, "names": ["cryptoSecureRandomInt", "hexStringify", "wordArray", "words", "sigBytes", "hexChars", "i", "bite", "push", "toString", "join", "WordArray", "constructor", "Words", "undefined", "length", "random", "nBytes"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/WordArray.mjs"], "sourcesContent": ["import { cryptoSecureRandomInt } from './cryptoSecureRandomInt.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Hex encoding strategy.\n * Converts a word array to a hex string.\n * @param {WordArray} wordArray The word array.\n * @return {string} The hex string.\n * @static\n */\nfunction hexStringify(wordArray) {\n    // Shortcuts\n    const { words } = wordArray;\n    const { sigBytes } = wordArray;\n    // Convert\n    const hexChars = [];\n    for (let i = 0; i < sigBytes; i++) {\n        const bite = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;\n        hexChars.push((bite >>> 4).toString(16));\n        hexChars.push((bite & 0x0f).toString(16));\n    }\n    return hexChars.join('');\n}\nclass WordArray {\n    constructor(words, sigBytes) {\n        this.words = [];\n        let Words = words;\n        Words = this.words = Words || [];\n        if (sigBytes !== undefined) {\n            this.sigBytes = sigBytes;\n        }\n        else {\n            this.sigBytes = Words.length * 4;\n        }\n    }\n    random(nBytes) {\n        const words = [];\n        for (let i = 0; i < nBytes; i += 4) {\n            words.push(cryptoSecureRandomInt());\n        }\n        return new WordArray(words, nBytes);\n    }\n    toString() {\n        return hexStringify(this);\n    }\n}\n\nexport { WordArray };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,6BAA6B;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,SAAS,EAAE;EAC7B;EACA,MAAM;IAAEC;EAAM,CAAC,GAAGD,SAAS;EAC3B,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAC9B;EACA,MAAMG,QAAQ,GAAG,EAAE;EACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,EAAEE,CAAC,EAAE,EAAE;IAC/B,MAAMC,IAAI,GAAIJ,KAAK,CAACG,CAAC,KAAK,CAAC,CAAC,KAAM,EAAE,GAAIA,CAAC,GAAG,CAAC,GAAI,CAAE,GAAI,IAAI;IAC3DD,QAAQ,CAACG,IAAI,CAAC,CAACD,IAAI,KAAK,CAAC,EAAEE,QAAQ,CAAC,EAAE,CAAC,CAAC;IACxCJ,QAAQ,CAACG,IAAI,CAAC,CAACD,IAAI,GAAG,IAAI,EAAEE,QAAQ,CAAC,EAAE,CAAC,CAAC;EAC7C;EACA,OAAOJ,QAAQ,CAACK,IAAI,CAAC,EAAE,CAAC;AAC5B;AACA,MAAMC,SAAS,CAAC;EACZC,WAAWA,CAACT,KAAK,EAAEC,QAAQ,EAAE;IACzB,IAAI,CAACD,KAAK,GAAG,EAAE;IACf,IAAIU,KAAK,GAAGV,KAAK;IACjBU,KAAK,GAAG,IAAI,CAACV,KAAK,GAAGU,KAAK,IAAI,EAAE;IAChC,IAAIT,QAAQ,KAAKU,SAAS,EAAE;MACxB,IAAI,CAACV,QAAQ,GAAGA,QAAQ;IAC5B,CAAC,MACI;MACD,IAAI,CAACA,QAAQ,GAAGS,KAAK,CAACE,MAAM,GAAG,CAAC;IACpC;EACJ;EACAC,MAAMA,CAACC,MAAM,EAAE;IACX,MAAMd,KAAK,GAAG,EAAE;IAChB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,MAAM,EAAEX,CAAC,IAAI,CAAC,EAAE;MAChCH,KAAK,CAACK,IAAI,CAACR,qBAAqB,CAAC,CAAC,CAAC;IACvC;IACA,OAAO,IAAIW,SAAS,CAACR,KAAK,EAAEc,MAAM,CAAC;EACvC;EACAR,QAAQA,CAAA,EAAG;IACP,OAAOR,YAAY,CAAC,IAAI,CAAC;EAC7B;AACJ;AAEA,SAASU,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}