<div [ngClass]="fileUploadContainerClass">
  <p-fileUpload [disabled]="isPreview || userPermissionLevel === 'View'" name="fileUploader" mode="advanced"
    class="customFileUploader" #fileUploader [multiple]="multipleAllowed" [maxFileSize]="maximumFileSize"
    [accept]="fileToAccept" (onSelect)="onFileSelectClick($event)" chooseLabel="Upload a File" chooseIcon="pi pi-upload"
    [showUploadButton]="false" [showCancelButton]="false">
    <ng-template pTemplate="toolbar">
      <div class="fileUpload-toolbar-text">You can upload files with the button or drag-and-drop.<br />
        Once uploaded, make sure you click the Save button on the grid to attach the files.</div>
    </ng-template>
    <ng-template pTemplate="content">
      @if(!uploadedFiles.length && !loadingFiles){
      <div class="fileUpload-content-template">
        Drag & Drop Files here</div>
      }
      @if(loadingFiles && !uploadedFiles.length){
      <div class="fileUpload-content-template">Loading...</div>
      }
      @if(uploadedFiles.length){
      <div class="fileUpload-datatable">
        <p-table [value]="uploadedFiles" styleClass="p-datatable-striped" [tableStyle]="tableStyle">
          <ng-template pTemplate="header">
            <tr>
              <th style="width: 40%;">File</th>
              <th style="width: 5%;">Type</th>
              <th style="width: 15%;">Uploaded On</th>
              <th style="width: 20%;">Uploaded By</th>
              <th style="width: 20%;"></th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-file>
            <tr>
              <td>{{file.name}}</td>
              <td>{{file.fileShareFileType}}</td>
              <td>{{file.uploadedDate}}</td>
              <td>{{file.uploadedByName}}</td>
              <td>
                <span class="flex">
                  <p-button icon="pi pi-download" [outlined]="true" (onClick)="onDownloadFileClick(file)"
                    pTooltip="Download"></p-button>
                  @if (userPermissionLevel !== 'View') {
                  <p-button [text]="true" severity="danger" icon="pi pi-trash" (onClick)="onRemoveFileClick(file)"
                    pTooltip="Delete"></p-button>
                  }
                  @if(imageFileTypes.includes(file.fileShareFileType)){
                  <p-button icon="pi pi-eye" [outlined]="true" (onClick)="onPreviewFileClick(file)"
                    pTooltip="Preview"></p-button>
                  }
                </span>
              </td>
            </tr>
          </ng-template>
          <ng-template pTemplate="footer">
            <div class="template-footer-text">Files: {{uploadedFiles.length}}</div>
          </ng-template>
        </p-table>
        @if (previewAttachmentInfo) {
        <p-panel [header]="'Preview (' + previewAttachmentInfo.fileName + ')'" class="attachment-preview-panel">
          <ng-template pTemplate="icons">
            <button class="p-panel-header-icon p-link" (click)="previewAttachmentInfo = undefined">
              <span class="pi pi-times"></span>
            </button>
          </ng-template>
          <p class="m-0">
            @if(imageFileTypes.includes(previewAttachmentInfo.fileType)){
            <img [src]="previewAttachmentInfo.url" alt="Preview" class="max-w-full h-auto" />
            }
            <!-- @todo: Add support for other file types -->
          </p>
        </p-panel>
        }
      </div>
      }
    </ng-template>
    <ng-template let-file pTemplate='file'></ng-template>
  </p-fileUpload>
</div>