{"ast": null, "code": "'use strict';\n\n/** @type {import('.')} */\nvar $defineProperty = Object.defineProperty || false;\nif ($defineProperty) {\n  try {\n    $defineProperty({}, 'a', {\n      value: 1\n    });\n  } catch (e) {\n    // IE 8 has a broken defineProperty\n    $defineProperty = false;\n  }\n}\nmodule.exports = $defineProperty;", "map": {"version": 3, "names": ["$defineProperty", "Object", "defineProperty", "value", "e", "module", "exports"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/es-define-property/index.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('.')} */\nvar $defineProperty = Object.defineProperty || false;\nif ($defineProperty) {\n\ttry {\n\t\t$defineProperty({}, 'a', { value: 1 });\n\t} catch (e) {\n\t\t// IE 8 has a broken defineProperty\n\t\t$defineProperty = false;\n\t}\n}\n\nmodule.exports = $defineProperty;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA,IAAIA,eAAe,GAAGC,MAAM,CAACC,cAAc,IAAI,KAAK;AACpD,IAAIF,eAAe,EAAE;EACpB,IAAI;IACHA,eAAe,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE;MAAEG,KAAK,EAAE;IAAE,CAAC,CAAC;EACvC,CAAC,CAAC,OAAOC,CAAC,EAAE;IACX;IACAJ,eAAe,GAAG,KAAK;EACxB;AACD;AAEAK,MAAM,CAACC,OAAO,GAAGN,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}