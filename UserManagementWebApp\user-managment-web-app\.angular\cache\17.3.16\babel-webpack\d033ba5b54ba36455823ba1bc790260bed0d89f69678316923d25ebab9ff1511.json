{"ast": null, "code": "import '@aws-amplify/core/internals/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { isString, isObject } from '../../utils/utils.mjs';\nimport { classNames } from '../../utils/classNames.mjs';\nfunction createComponentClasses({\n  name,\n  prefix = 'amplify-'\n}) {\n  const className = (props = {}, extraClassnames = []) => {\n    const baseComponentClassName = `${prefix}${name}`;\n    // get the element if there is one\n    // the _element argument can be a string\n    // like { _element: 'icon' }\n    // or it could be an object where the key is\n    // the element name and the value is the modifiers\n    // like { _element: { icon: [size] } }\n    const element = isString(props._element) ? props._element : isObject(props._element) ? Object.keys(props._element)[0] : undefined;\n    const className = element ? `${baseComponentClassName}__${element}` : baseComponentClassName;\n    const names = [className];\n    if (element) {\n      const modifiers = props._element[element];\n      names.push(...modifierClassnames({\n        className,\n        modifiers\n      }));\n    } else {\n      names.push(...modifierClassnames({\n        className,\n        modifiers: props._modifiers\n      }));\n    }\n    return classNames([...names, ...extraClassnames]);\n  };\n  return className;\n}\nfunction modifierClassnames({\n  className,\n  modifiers\n}) {\n  if (Array.isArray(modifiers)) {\n    return modifiers.map(modifier => {\n      if (!modifier || !isString(modifier)) {\n        return;\n      }\n      return `${className}--${modifier}`;\n    });\n  }\n  if (isObject(modifiers)) {\n    return Object.entries(modifiers).map(([key, value]) => {\n      if (value) {\n        return `${className}--${key}`;\n      }\n    });\n  }\n  if (isString(modifiers)) {\n    return [`${className}--${modifiers}`];\n  }\n  return [];\n}\nexport { createComponentClasses };", "map": {"version": 3, "names": ["isString", "isObject", "classNames", "createComponentClasses", "name", "prefix", "className", "props", "extraClassnames", "baseComponentClassName", "element", "_element", "Object", "keys", "undefined", "names", "modifiers", "push", "modifierClassnames", "_modifiers", "Array", "isArray", "map", "modifier", "entries", "key", "value"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/createTheme/createComponentClasses.mjs"], "sourcesContent": ["import '@aws-amplify/core/internals/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { isString, isObject } from '../../utils/utils.mjs';\nimport { classNames } from '../../utils/classNames.mjs';\n\nfunction createComponentClasses({ name, prefix = 'amplify-' }) {\n    const className = (props = {}, extraClassnames = []) => {\n        const baseComponentClassName = `${prefix}${name}`;\n        // get the element if there is one\n        // the _element argument can be a string\n        // like { _element: 'icon' }\n        // or it could be an object where the key is\n        // the element name and the value is the modifiers\n        // like { _element: { icon: [size] } }\n        const element = isString(props._element)\n            ? props._element\n            : isObject(props._element)\n                ? Object.keys(props._element)[0]\n                : undefined;\n        const className = element\n            ? `${baseComponentClassName}__${element}`\n            : baseComponentClassName;\n        const names = [className];\n        if (element) {\n            const modifiers = props._element[element];\n            names.push(...modifierClassnames({ className, modifiers }));\n        }\n        else {\n            names.push(...modifierClassnames({\n                className,\n                modifiers: props._modifiers,\n            }));\n        }\n        return classNames([...names, ...extraClassnames]);\n    };\n    return className;\n}\nfunction modifierClassnames({ className, modifiers, }) {\n    if (Array.isArray(modifiers)) {\n        return modifiers.map((modifier) => {\n            if (!modifier || !isString(modifier)) {\n                return;\n            }\n            return `${className}--${modifier}`;\n        });\n    }\n    if (isObject(modifiers)) {\n        return Object.entries(modifiers).map(([key, value]) => {\n            if (value) {\n                return `${className}--${key}`;\n            }\n        });\n    }\n    if (isString(modifiers)) {\n        return [`${className}--${modifiers}`];\n    }\n    return [];\n}\n\nexport { createComponentClasses };\n"], "mappings": "AAAA,OAAO,mCAAmC;AAC1C,OAAO,wCAAwC;AAC/C,SAASA,QAAQ,EAAEC,QAAQ,QAAQ,uBAAuB;AAC1D,SAASC,UAAU,QAAQ,4BAA4B;AAEvD,SAASC,sBAAsBA,CAAC;EAAEC,IAAI;EAAEC,MAAM,GAAG;AAAW,CAAC,EAAE;EAC3D,MAAMC,SAAS,GAAGA,CAACC,KAAK,GAAG,CAAC,CAAC,EAAEC,eAAe,GAAG,EAAE,KAAK;IACpD,MAAMC,sBAAsB,GAAG,GAAGJ,MAAM,GAAGD,IAAI,EAAE;IACjD;IACA;IACA;IACA;IACA;IACA;IACA,MAAMM,OAAO,GAAGV,QAAQ,CAACO,KAAK,CAACI,QAAQ,CAAC,GAClCJ,KAAK,CAACI,QAAQ,GACdV,QAAQ,CAACM,KAAK,CAACI,QAAQ,CAAC,GACpBC,MAAM,CAACC,IAAI,CAACN,KAAK,CAACI,QAAQ,CAAC,CAAC,CAAC,CAAC,GAC9BG,SAAS;IACnB,MAAMR,SAAS,GAAGI,OAAO,GACnB,GAAGD,sBAAsB,KAAKC,OAAO,EAAE,GACvCD,sBAAsB;IAC5B,MAAMM,KAAK,GAAG,CAACT,SAAS,CAAC;IACzB,IAAII,OAAO,EAAE;MACT,MAAMM,SAAS,GAAGT,KAAK,CAACI,QAAQ,CAACD,OAAO,CAAC;MACzCK,KAAK,CAACE,IAAI,CAAC,GAAGC,kBAAkB,CAAC;QAAEZ,SAAS;QAAEU;MAAU,CAAC,CAAC,CAAC;IAC/D,CAAC,MACI;MACDD,KAAK,CAACE,IAAI,CAAC,GAAGC,kBAAkB,CAAC;QAC7BZ,SAAS;QACTU,SAAS,EAAET,KAAK,CAACY;MACrB,CAAC,CAAC,CAAC;IACP;IACA,OAAOjB,UAAU,CAAC,CAAC,GAAGa,KAAK,EAAE,GAAGP,eAAe,CAAC,CAAC;EACrD,CAAC;EACD,OAAOF,SAAS;AACpB;AACA,SAASY,kBAAkBA,CAAC;EAAEZ,SAAS;EAAEU;AAAW,CAAC,EAAE;EACnD,IAAII,KAAK,CAACC,OAAO,CAACL,SAAS,CAAC,EAAE;IAC1B,OAAOA,SAAS,CAACM,GAAG,CAAEC,QAAQ,IAAK;MAC/B,IAAI,CAACA,QAAQ,IAAI,CAACvB,QAAQ,CAACuB,QAAQ,CAAC,EAAE;QAClC;MACJ;MACA,OAAO,GAAGjB,SAAS,KAAKiB,QAAQ,EAAE;IACtC,CAAC,CAAC;EACN;EACA,IAAItB,QAAQ,CAACe,SAAS,CAAC,EAAE;IACrB,OAAOJ,MAAM,CAACY,OAAO,CAACR,SAAS,CAAC,CAACM,GAAG,CAAC,CAAC,CAACG,GAAG,EAAEC,KAAK,CAAC,KAAK;MACnD,IAAIA,KAAK,EAAE;QACP,OAAO,GAAGpB,SAAS,KAAKmB,GAAG,EAAE;MACjC;IACJ,CAAC,CAAC;EACN;EACA,IAAIzB,QAAQ,CAACgB,SAAS,CAAC,EAAE;IACrB,OAAO,CAAC,GAAGV,SAAS,KAAKU,SAAS,EAAE,CAAC;EACzC;EACA,OAAO,EAAE;AACb;AAEA,SAASb,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}