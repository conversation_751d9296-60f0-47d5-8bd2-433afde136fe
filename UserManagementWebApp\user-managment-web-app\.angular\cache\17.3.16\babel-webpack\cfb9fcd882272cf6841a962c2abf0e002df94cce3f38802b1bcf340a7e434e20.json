{"ast": null, "code": "import { createAssertionFunction } from '../../errors/createAssertionFunction.mjs';\nimport '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar CacheErrorCode;\n(function (CacheErrorCode) {\n  CacheErrorCode[\"NoCacheItem\"] = \"NoCacheItem\";\n  CacheErrorCode[\"NullNextNode\"] = \"NullNextNode\";\n  CacheErrorCode[\"NullPreviousNode\"] = \"NullPreviousNode\";\n})(CacheErrorCode || (CacheErrorCode = {}));\nconst cacheErrorMap = {\n  [CacheErrorCode.NoCacheItem]: {\n    message: 'Item not found in the cache storage.'\n  },\n  [CacheErrorCode.NullNextNode]: {\n    message: 'Next node is null.'\n  },\n  [CacheErrorCode.NullPreviousNode]: {\n    message: 'Previous node is null.'\n  }\n};\nconst assert = createAssertionFunction(cacheErrorMap);\nexport { CacheErrorCode, assert };", "map": {"version": 3, "names": ["createAssertionFunction", "CacheErrorCode", "cacheErrorMap", "NoCacheItem", "message", "NullNextNode", "NullPreviousNode", "assert"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Cache/utils/errorHelpers.mjs"], "sourcesContent": ["import { createAssertionFunction } from '../../errors/createAssertionFunction.mjs';\nimport '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nvar CacheErrorCode;\n(function (CacheErrorCode) {\n    CacheErrorCode[\"NoCacheItem\"] = \"NoCacheItem\";\n    CacheErrorCode[\"NullNextNode\"] = \"NullNextNode\";\n    CacheErrorCode[\"NullPreviousNode\"] = \"NullPreviousNode\";\n})(CacheErrorCode || (CacheErrorCode = {}));\nconst cacheErrorMap = {\n    [CacheErrorCode.NoCacheItem]: {\n        message: 'Item not found in the cache storage.',\n    },\n    [CacheErrorCode.NullNextNode]: {\n        message: 'Next node is null.',\n    },\n    [CacheErrorCode.NullPreviousNode]: {\n        message: 'Previous node is null.',\n    },\n};\nconst assert = createAssertionFunction(cacheErrorMap);\n\nexport { CacheErrorCode, assert };\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,0CAA0C;AAClF,OAAO,wBAAwB;AAC/B,OAAO,+BAA+B;;AAEtC;AACA;AACA,IAAIC,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAAC,aAAa,CAAC,GAAG,aAAa;EAC7CA,cAAc,CAAC,cAAc,CAAC,GAAG,cAAc;EAC/CA,cAAc,CAAC,kBAAkB,CAAC,GAAG,kBAAkB;AAC3D,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,MAAMC,aAAa,GAAG;EAClB,CAACD,cAAc,CAACE,WAAW,GAAG;IAC1BC,OAAO,EAAE;EACb,CAAC;EACD,CAACH,cAAc,CAACI,YAAY,GAAG;IAC3BD,OAAO,EAAE;EACb,CAAC;EACD,CAACH,cAAc,CAACK,gBAAgB,GAAG;IAC/BF,OAAO,EAAE;EACb;AACJ,CAAC;AACD,MAAMG,MAAM,GAAGP,uBAAuB,CAACE,aAAa,CAAC;AAErD,SAASD,cAAc,EAAEM,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}