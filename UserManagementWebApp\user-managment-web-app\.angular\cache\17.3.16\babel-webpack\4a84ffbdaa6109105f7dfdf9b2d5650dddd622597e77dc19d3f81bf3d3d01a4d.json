{"ast": null, "code": "const defaultTexts = {\n  ADD_PROFILE: 'Add your Profile',\n  ADD_WEBSITE: 'Add your Website',\n  BACK_SIGN_IN: 'Back to Sign In',\n  BIRTHDATE: 'Birthdate',\n  CHANGE_PASSWORD: 'Change Password',\n  CHANGING_PASSWORD: 'Changing',\n  CODE: 'Code',\n  CODE_ARRIVAL: 'It may take a minute to arrive',\n  CODE_EMAILED: 'Your code is on the way. To log in, enter the code we emailed to',\n  CODE_SENT: 'Your code is on the way. To log in, enter the code we sent you',\n  CODE_TEXTED: 'Your code is on the way. To log in, enter the code we texted to',\n  CONFIRM_PASSWORD: 'Confirm Password',\n  CONFIRM_PASSWORD_PLACEHOLDER: 'Please confirm your Password',\n  CONFIRM_RESET_PASSWORD_HEADING: 'Reset your Password',\n  CONFIRM_SIGNUP_HEADING: 'Confirm Sign Up',\n  CONFIRM_SMS: 'Confirm SMS Code',\n  CONFIRM_EMAIL: 'Confirm Email Code',\n  // If challenge name is not returned\n  CONFIRM_MFA_DEFAULT: 'Confirm MFA Code',\n  CONFIRM_TOTP: 'Confirm TOTP Code',\n  CONFIRM: 'Confirm',\n  CONFIRMATION_CODE: 'Confirmation Code',\n  CONFIRMING: 'Confirming',\n  CREATE_ACCOUNT: 'Create Account',\n  CREATING_ACCOUNT: 'Creating Account',\n  EMAIL_ADDRESS: 'Email',\n  EMAIL_OTP: 'Email Message',\n  ENTER_BIRTHDATE: 'Enter your Birthdate',\n  ENTER_CODE: 'Enter your code',\n  ENTER_CONFIRMATION_CODE: 'Enter your Confirmation Code',\n  ENTER_EMAIL: 'Enter your Email',\n  ENTER_FAMILY_NAME: 'Enter your Family Name',\n  ENTER_GIVEN_NAME: 'Enter your Given Name',\n  ENTER_MIDDLE_NAME: 'Enter your Middle Name',\n  ENTER_NAME: 'Enter your Name',\n  ENTER_NICK_NAME: 'Enter your Nickname',\n  ENTER_PASSWORD: 'Enter your Password',\n  ENTER_PHONE_NUMBER: 'Enter your Phone Number',\n  ENTER_PREFERRED_USERNAME: 'Enter your Preferred Username',\n  ENTER_USERNAME: 'Enter your username',\n  FAMILY_NAME: 'Family Name',\n  GIVEN_NAME: 'Given Name',\n  FORGOT_PASSWORD: 'Forgot Password?',\n  FORGOT_YOUR_PASSWORD: 'Forgot your password?',\n  HIDE_PASSWORD: 'Hide password',\n  LOADING: 'Loading',\n  LOGIN_NAME: 'Username',\n  MIDDLE_NAME: 'Middle Name',\n  MFA_SETUP_SELECTION: 'Multi-Factor Authentication Setup',\n  MFA_SELECTION: 'Multi-Factor Authentication',\n  NAME: 'Name',\n  NICKNAME: 'Nickname',\n  NEW_PASSWORD: 'New password',\n  OR: 'or',\n  PASSWORD: 'Password',\n  PHONE_NUMBER: 'Phone Number',\n  PREFERRED_USERNAME: 'Preferred Username',\n  PROFILE: 'Profile',\n  RESEND_CODE: 'Resend Code',\n  RESET_PASSWORD_HEADING: 'Reset your password',\n  RESET_PASSWORD: 'Reset Password',\n  SEND_CODE: 'Send code',\n  SENDING: 'Sending',\n  SELECT_MFA_TYPE: 'Select MFA Type',\n  SETUP_EMAIL: 'Setup Email',\n  SETUP_TOTP: 'Setup TOTP',\n  SHOW_PASSWORD: 'Show password',\n  SIGN_IN_BUTTON: 'Sign in',\n  SIGN_IN_TAB: 'Sign In',\n  SIGN_IN_WITH_AMAZON: 'Sign In with Amazon',\n  SIGN_IN_WITH_APPLE: 'Sign In with Apple',\n  SIGN_IN_WITH_FACEBOOK: 'Sign In with Facebook',\n  SIGN_IN_WITH_GOOGLE: 'Sign In with Google',\n  SIGN_IN: 'Sign in to your account',\n  SIGN_UP_BUTTON: 'Create a new account',\n  SIGNING_IN_BUTTON: 'Signing in',\n  SKIP: 'Skip',\n  SMS_MFA: 'Text Message (SMS)',\n  SUBMIT: 'Submit',\n  SUBMITTING: 'Submitting',\n  SOFTWARE_TOKEN_MFA: 'Authenticator App (TOTP)',\n  UPPERCASE_COPY: 'COPY',\n  VERIFY_CONTACT: 'Verify Contact',\n  VERIFY_HEADING: 'Account recovery requires verified contact information',\n  VERIFY: 'Verify',\n  WE_EMAILED: 'We Emailed You',\n  WE_SENT_CODE: 'We Sent A Code',\n  WE_TEXTED: 'We Texted You',\n  WEBSITE: 'Website'\n};\nexport { defaultTexts };", "map": {"version": 3, "names": ["defaultTexts", "ADD_PROFILE", "ADD_WEBSITE", "BACK_SIGN_IN", "BIRTHDATE", "CHANGE_PASSWORD", "CHANGING_PASSWORD", "CODE", "CODE_ARRIVAL", "CODE_EMAILED", "CODE_SENT", "CODE_TEXTED", "CONFIRM_PASSWORD", "CONFIRM_PASSWORD_PLACEHOLDER", "CONFIRM_RESET_PASSWORD_HEADING", "CONFIRM_SIGNUP_HEADING", "CONFIRM_SMS", "CONFIRM_EMAIL", "CONFIRM_MFA_DEFAULT", "CONFIRM_TOTP", "CONFIRM", "CONFIRMATION_CODE", "CONFIRMING", "CREATE_ACCOUNT", "CREATING_ACCOUNT", "EMAIL_ADDRESS", "EMAIL_OTP", "ENTER_BIRTHDATE", "ENTER_CODE", "ENTER_CONFIRMATION_CODE", "ENTER_EMAIL", "ENTER_FAMILY_NAME", "ENTER_GIVEN_NAME", "ENTER_MIDDLE_NAME", "ENTER_NAME", "ENTER_NICK_NAME", "ENTER_PASSWORD", "ENTER_PHONE_NUMBER", "ENTER_PREFERRED_USERNAME", "ENTER_USERNAME", "FAMILY_NAME", "GIVEN_NAME", "FORGOT_PASSWORD", "FORGOT_YOUR_PASSWORD", "HIDE_PASSWORD", "LOADING", "LOGIN_NAME", "MIDDLE_NAME", "MFA_SETUP_SELECTION", "MFA_SELECTION", "NAME", "NICKNAME", "NEW_PASSWORD", "OR", "PASSWORD", "PHONE_NUMBER", "PREFERRED_USERNAME", "PROFILE", "RESEND_CODE", "RESET_PASSWORD_HEADING", "RESET_PASSWORD", "SEND_CODE", "SENDING", "SELECT_MFA_TYPE", "SETUP_EMAIL", "SETUP_TOTP", "SHOW_PASSWORD", "SIGN_IN_BUTTON", "SIGN_IN_TAB", "SIGN_IN_WITH_AMAZON", "SIGN_IN_WITH_APPLE", "SIGN_IN_WITH_FACEBOOK", "SIGN_IN_WITH_GOOGLE", "SIGN_IN", "SIGN_UP_BUTTON", "SIGNING_IN_BUTTON", "SKIP", "SMS_MFA", "SUBMIT", "SUBMITTING", "SOFTWARE_TOKEN_MFA", "UPPERCASE_COPY", "VERIFY_CONTACT", "VERIFY_HEADING", "VERIFY", "WE_EMAILED", "WE_SENT_CODE", "WE_TEXTED", "WEBSITE"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/i18n/dictionaries/authenticator/defaultTexts.mjs"], "sourcesContent": ["const defaultTexts = {\n    ADD_PROFILE: 'Add your Profile',\n    ADD_WEBSITE: 'Add your Website',\n    BACK_SIGN_IN: 'Back to Sign In',\n    BIRTHDATE: 'Birthdate',\n    CHANGE_PASSWORD: 'Change Password',\n    CHANGING_PASSWORD: 'Changing',\n    CODE: 'Code',\n    CODE_ARRIVAL: 'It may take a minute to arrive',\n    CODE_EMAILED: 'Your code is on the way. To log in, enter the code we emailed to',\n    CODE_SENT: 'Your code is on the way. To log in, enter the code we sent you',\n    CODE_TEXTED: 'Your code is on the way. To log in, enter the code we texted to',\n    CONFIRM_PASSWORD: 'Confirm Password',\n    CONFIRM_PASSWORD_PLACEHOLDER: 'Please confirm your Password',\n    CONFIRM_RESET_PASSWORD_HEADING: 'Reset your Password',\n    CONFIRM_SIGNUP_HEADING: 'Confirm Sign Up',\n    CONFIRM_SMS: 'Confirm SMS Code',\n    CONFIRM_EMAIL: 'Confirm Email Code',\n    // If challenge name is not returned\n    CONFIRM_MFA_DEFAULT: 'Confirm MFA Code',\n    CONFIRM_TOTP: 'Confirm TOTP Code',\n    CONFIRM: 'Confirm',\n    CONFIRMATION_CODE: 'Confirmation Code',\n    CONFIRMING: 'Confirming',\n    CREATE_ACCOUNT: 'Create Account',\n    CREATING_ACCOUNT: 'Creating Account',\n    EMAIL_ADDRESS: 'Email',\n    EMAIL_OTP: 'Email Message',\n    ENTER_BIRTHDATE: 'Enter your Birthdate',\n    ENTER_CODE: 'Enter your code',\n    ENTER_CONFIRMATION_CODE: 'Enter your Confirmation Code',\n    ENTER_EMAIL: 'Enter your Email',\n    ENTER_FAMILY_NAME: 'Enter your Family Name',\n    ENTER_GIVEN_NAME: 'Enter your Given Name',\n    ENTER_MIDDLE_NAME: 'Enter your Middle Name',\n    ENTER_NAME: 'Enter your Name',\n    ENTER_NICK_NAME: 'Enter your Nickname',\n    ENTER_PASSWORD: 'Enter your Password',\n    ENTER_PHONE_NUMBER: 'Enter your Phone Number',\n    ENTER_PREFERRED_USERNAME: 'Enter your Preferred Username',\n    ENTER_USERNAME: 'Enter your username',\n    FAMILY_NAME: 'Family Name',\n    GIVEN_NAME: 'Given Name',\n    FORGOT_PASSWORD: 'Forgot Password?',\n    FORGOT_YOUR_PASSWORD: 'Forgot your password?',\n    HIDE_PASSWORD: 'Hide password',\n    LOADING: 'Loading',\n    LOGIN_NAME: 'Username',\n    MIDDLE_NAME: 'Middle Name',\n    MFA_SETUP_SELECTION: 'Multi-Factor Authentication Setup',\n    MFA_SELECTION: 'Multi-Factor Authentication',\n    NAME: 'Name',\n    NICKNAME: 'Nickname',\n    NEW_PASSWORD: 'New password',\n    OR: 'or',\n    PASSWORD: 'Password',\n    PHONE_NUMBER: 'Phone Number',\n    PREFERRED_USERNAME: 'Preferred Username',\n    PROFILE: 'Profile',\n    RESEND_CODE: 'Resend Code',\n    RESET_PASSWORD_HEADING: 'Reset your password',\n    RESET_PASSWORD: 'Reset Password',\n    SEND_CODE: 'Send code',\n    SENDING: 'Sending',\n    SELECT_MFA_TYPE: 'Select MFA Type',\n    SETUP_EMAIL: 'Setup Email',\n    SETUP_TOTP: 'Setup TOTP',\n    SHOW_PASSWORD: 'Show password',\n    SIGN_IN_BUTTON: 'Sign in',\n    SIGN_IN_TAB: 'Sign In',\n    SIGN_IN_WITH_AMAZON: 'Sign In with Amazon',\n    SIGN_IN_WITH_APPLE: 'Sign In with Apple',\n    SIGN_IN_WITH_FACEBOOK: 'Sign In with Facebook',\n    SIGN_IN_WITH_GOOGLE: 'Sign In with Google',\n    SIGN_IN: 'Sign in to your account',\n    SIGN_UP_BUTTON: 'Create a new account',\n    SIGNING_IN_BUTTON: 'Signing in',\n    SKIP: 'Skip',\n    SMS_MFA: 'Text Message (SMS)',\n    SUBMIT: 'Submit',\n    SUBMITTING: 'Submitting',\n    SOFTWARE_TOKEN_MFA: 'Authenticator App (TOTP)',\n    UPPERCASE_COPY: 'COPY',\n    VERIFY_CONTACT: 'Verify Contact',\n    VERIFY_HEADING: 'Account recovery requires verified contact information',\n    VERIFY: 'Verify',\n    WE_EMAILED: 'We Emailed You',\n    WE_SENT_CODE: 'We Sent A Code',\n    WE_TEXTED: 'We Texted You',\n    WEBSITE: 'Website',\n};\n\nexport { defaultTexts };\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG;EACjBC,WAAW,EAAE,kBAAkB;EAC/BC,WAAW,EAAE,kBAAkB;EAC/BC,YAAY,EAAE,iBAAiB;EAC/BC,SAAS,EAAE,WAAW;EACtBC,eAAe,EAAE,iBAAiB;EAClCC,iBAAiB,EAAE,UAAU;EAC7BC,IAAI,EAAE,MAAM;EACZC,YAAY,EAAE,gCAAgC;EAC9CC,YAAY,EAAE,kEAAkE;EAChFC,SAAS,EAAE,gEAAgE;EAC3EC,WAAW,EAAE,iEAAiE;EAC9EC,gBAAgB,EAAE,kBAAkB;EACpCC,4BAA4B,EAAE,8BAA8B;EAC5DC,8BAA8B,EAAE,qBAAqB;EACrDC,sBAAsB,EAAE,iBAAiB;EACzCC,WAAW,EAAE,kBAAkB;EAC/BC,aAAa,EAAE,oBAAoB;EACnC;EACAC,mBAAmB,EAAE,kBAAkB;EACvCC,YAAY,EAAE,mBAAmB;EACjCC,OAAO,EAAE,SAAS;EAClBC,iBAAiB,EAAE,mBAAmB;EACtCC,UAAU,EAAE,YAAY;EACxBC,cAAc,EAAE,gBAAgB;EAChCC,gBAAgB,EAAE,kBAAkB;EACpCC,aAAa,EAAE,OAAO;EACtBC,SAAS,EAAE,eAAe;EAC1BC,eAAe,EAAE,sBAAsB;EACvCC,UAAU,EAAE,iBAAiB;EAC7BC,uBAAuB,EAAE,8BAA8B;EACvDC,WAAW,EAAE,kBAAkB;EAC/BC,iBAAiB,EAAE,wBAAwB;EAC3CC,gBAAgB,EAAE,uBAAuB;EACzCC,iBAAiB,EAAE,wBAAwB;EAC3CC,UAAU,EAAE,iBAAiB;EAC7BC,eAAe,EAAE,qBAAqB;EACtCC,cAAc,EAAE,qBAAqB;EACrCC,kBAAkB,EAAE,yBAAyB;EAC7CC,wBAAwB,EAAE,+BAA+B;EACzDC,cAAc,EAAE,qBAAqB;EACrCC,WAAW,EAAE,aAAa;EAC1BC,UAAU,EAAE,YAAY;EACxBC,eAAe,EAAE,kBAAkB;EACnCC,oBAAoB,EAAE,uBAAuB;EAC7CC,aAAa,EAAE,eAAe;EAC9BC,OAAO,EAAE,SAAS;EAClBC,UAAU,EAAE,UAAU;EACtBC,WAAW,EAAE,aAAa;EAC1BC,mBAAmB,EAAE,mCAAmC;EACxDC,aAAa,EAAE,6BAA6B;EAC5CC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,UAAU;EACpBC,YAAY,EAAE,cAAc;EAC5BC,EAAE,EAAE,IAAI;EACRC,QAAQ,EAAE,UAAU;EACpBC,YAAY,EAAE,cAAc;EAC5BC,kBAAkB,EAAE,oBAAoB;EACxCC,OAAO,EAAE,SAAS;EAClBC,WAAW,EAAE,aAAa;EAC1BC,sBAAsB,EAAE,qBAAqB;EAC7CC,cAAc,EAAE,gBAAgB;EAChCC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE,SAAS;EAClBC,eAAe,EAAE,iBAAiB;EAClCC,WAAW,EAAE,aAAa;EAC1BC,UAAU,EAAE,YAAY;EACxBC,aAAa,EAAE,eAAe;EAC9BC,cAAc,EAAE,SAAS;EACzBC,WAAW,EAAE,SAAS;EACtBC,mBAAmB,EAAE,qBAAqB;EAC1CC,kBAAkB,EAAE,oBAAoB;EACxCC,qBAAqB,EAAE,uBAAuB;EAC9CC,mBAAmB,EAAE,qBAAqB;EAC1CC,OAAO,EAAE,yBAAyB;EAClCC,cAAc,EAAE,sBAAsB;EACtCC,iBAAiB,EAAE,YAAY;EAC/BC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,oBAAoB;EAC7BC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,YAAY;EACxBC,kBAAkB,EAAE,0BAA0B;EAC9CC,cAAc,EAAE,MAAM;EACtBC,cAAc,EAAE,gBAAgB;EAChCC,cAAc,EAAE,wDAAwD;EACxEC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,gBAAgB;EAC5BC,YAAY,EAAE,gBAAgB;EAC9BC,SAAS,EAAE,eAAe;EAC1BC,OAAO,EAAE;AACb,CAAC;AAED,SAASxF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}