{"ast": null, "code": "import { PinpointEventBuffer } from './PinpointEventBuffer.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Map of buffers by region -> appId\nconst eventBufferMap = {};\n/**\n * Returns a PinpointEventBuffer instance for the specified region & app ID, creating one if it does not yet exist.\n *\n * @internal\n */\nconst getEventBuffer = ({\n  appId,\n  region,\n  credentials,\n  bufferSize,\n  flushInterval,\n  flushSize,\n  resendLimit,\n  identityId,\n  userAgentValue\n}) => {\n  if (eventBufferMap[region]?.[appId]) {\n    const buffer = eventBufferMap[region][appId];\n    /*\n    If the identity has changed flush out the buffer and create a new instance. The old instance will be garbage\n    collected.\n    */\n    if (buffer.identityHasChanged(identityId)) {\n      buffer.flush();\n    } else {\n      return buffer;\n    }\n  }\n  const buffer = new PinpointEventBuffer({\n    appId,\n    bufferSize,\n    credentials,\n    flushInterval,\n    flushSize,\n    identityId,\n    region,\n    resendLimit,\n    userAgentValue\n  });\n  if (!eventBufferMap[region]) {\n    eventBufferMap[region] = {};\n  }\n  eventBufferMap[region][appId] = buffer;\n  return buffer;\n};\nexport { getEventBuffer };", "map": {"version": 3, "names": ["PinpointEventBuffer", "eventBufferMap", "getEventBuffer", "appId", "region", "credentials", "bufferSize", "flushInterval", "flushSize", "resendLimit", "identityId", "userAgentValue", "buffer", "identityHasChanged", "flush"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/providers/pinpoint/utils/getEventBuffer.mjs"], "sourcesContent": ["import { PinpointEventBuffer } from './PinpointEventBuffer.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Map of buffers by region -> appId\nconst eventBufferMap = {};\n/**\n * Returns a PinpointEventBuffer instance for the specified region & app ID, creating one if it does not yet exist.\n *\n * @internal\n */\nconst getEventBuffer = ({ appId, region, credentials, bufferSize, flushInterval, flushSize, resendLimit, identityId, userAgentValue, }) => {\n    if (eventBufferMap[region]?.[appId]) {\n        const buffer = eventBufferMap[region][appId];\n        /*\n        If the identity has changed flush out the buffer and create a new instance. The old instance will be garbage\n        collected.\n        */\n        if (buffer.identityHasChanged(identityId)) {\n            buffer.flush();\n        }\n        else {\n            return buffer;\n        }\n    }\n    const buffer = new PinpointEventBuffer({\n        appId,\n        bufferSize,\n        credentials,\n        flushInterval,\n        flushSize,\n        identityId,\n        region,\n        resendLimit,\n        userAgentValue,\n    });\n    if (!eventBufferMap[region]) {\n        eventBufferMap[region] = {};\n    }\n    eventBufferMap[region][appId] = buffer;\n    return buffer;\n};\n\nexport { getEventBuffer };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,2BAA2B;;AAE/D;AACA;AACA;AACA,MAAMC,cAAc,GAAG,CAAC,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAGA,CAAC;EAAEC,KAAK;EAAEC,MAAM;EAAEC,WAAW;EAAEC,UAAU;EAAEC,aAAa;EAAEC,SAAS;EAAEC,WAAW;EAAEC,UAAU;EAAEC;AAAgB,CAAC,KAAK;EACvI,IAAIV,cAAc,CAACG,MAAM,CAAC,GAAGD,KAAK,CAAC,EAAE;IACjC,MAAMS,MAAM,GAAGX,cAAc,CAACG,MAAM,CAAC,CAACD,KAAK,CAAC;IAC5C;AACR;AACA;AACA;IACQ,IAAIS,MAAM,CAACC,kBAAkB,CAACH,UAAU,CAAC,EAAE;MACvCE,MAAM,CAACE,KAAK,CAAC,CAAC;IAClB,CAAC,MACI;MACD,OAAOF,MAAM;IACjB;EACJ;EACA,MAAMA,MAAM,GAAG,IAAIZ,mBAAmB,CAAC;IACnCG,KAAK;IACLG,UAAU;IACVD,WAAW;IACXE,aAAa;IACbC,SAAS;IACTE,UAAU;IACVN,MAAM;IACNK,WAAW;IACXE;EACJ,CAAC,CAAC;EACF,IAAI,CAACV,cAAc,CAACG,MAAM,CAAC,EAAE;IACzBH,cAAc,CAACG,MAAM,CAAC,GAAG,CAAC,CAAC;EAC/B;EACAH,cAAc,CAACG,MAAM,CAAC,CAACD,KAAK,CAAC,GAAGS,MAAM;EACtC,OAAOA,MAAM;AACjB,CAAC;AAED,SAASV,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}