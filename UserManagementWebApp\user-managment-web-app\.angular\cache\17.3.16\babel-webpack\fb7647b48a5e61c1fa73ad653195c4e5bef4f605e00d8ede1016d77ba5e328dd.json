{"ast": null, "code": "export * from \"./escape-uri\";\nexport * from \"./escape-uri-path\";", "map": {"version": 3, "names": [], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/util-uri-escape/dist/es/index.js"], "sourcesContent": ["export * from \"./escape-uri\";\nexport * from \"./escape-uri-path\";\n"], "mappings": "AAAA,cAAc,cAAc;AAC5B,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}