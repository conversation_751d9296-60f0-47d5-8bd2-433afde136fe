{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { SETUP_TOTP_EXCEPTION } from '../types/errors.mjs';\nimport { getTOTPSetupDetails } from '../utils/signInHelpers.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport { createAssociateSoftwareTokenClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createAssociateSoftwareTokenClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Sets up TOTP for the user.\n *\n * @returns SetUpTOTPOutput\n * @throws -{@link AssociateSoftwareTokenException}\n * Thrown if a service occurs while setting up TOTP.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n **/\nfunction setUpTOTP() {\n  return _setUpTOTP.apply(this, arguments);\n}\nfunction _setUpTOTP() {\n  _setUpTOTP = _asyncToGenerator(function* () {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const {\n      userPoolEndpoint,\n      userPoolId\n    } = authConfig;\n    const {\n      tokens\n    } = yield fetchAuthSession({\n      forceRefresh: false\n    });\n    assertAuthTokens(tokens);\n    const username = tokens.idToken?.payload['cognito:username'] ?? '';\n    const associateSoftwareToken = createAssociateSoftwareTokenClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const {\n      SecretCode\n    } = yield associateSoftwareToken({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.SetUpTOTP)\n    }, {\n      AccessToken: tokens.accessToken.toString()\n    });\n    if (!SecretCode) {\n      // This should never happen.\n      throw new AuthError({\n        name: SETUP_TOTP_EXCEPTION,\n        message: 'Failed to set up TOTP.'\n      });\n    }\n    return getTOTPSetupDetails(SecretCode, JSON.stringify(username));\n  });\n  return _setUpTOTP.apply(this, arguments);\n}\nexport { setUpTOTP };", "map": {"version": 3, "names": ["Amplify", "fetchAuthSession", "assertTokenProviderConfig", "AuthAction", "<PERSON>th<PERSON><PERSON><PERSON>", "SETUP_TOTP_EXCEPTION", "getTOTPSetupDetails", "getRegionFromUserPoolId", "assertAuthTokens", "getAuthUserAgentValue", "createAssociateSoftwareTokenClient", "createCognitoUserPoolEndpointResolver", "setUpTOTP", "_setUpTOTP", "apply", "arguments", "_asyncToGenerator", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "userPoolEndpoint", "userPoolId", "tokens", "forceRefresh", "username", "idToken", "payload", "associateSoftwareToken", "endpointResolver", "endpointOverride", "SecretCode", "region", "userAgentValue", "SetUpTOTP", "AccessToken", "accessToken", "toString", "name", "message", "JSON", "stringify"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/setUpTOTP.mjs"], "sourcesContent": ["import { Amplify, fetchAuthSession } from '@aws-amplify/core';\nimport { assertTokenProviderConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { AuthError } from '../../../errors/AuthError.mjs';\nimport { SETUP_TOTP_EXCEPTION } from '../types/errors.mjs';\nimport { getTOTPSetupDetails } from '../utils/signInHelpers.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { assertAuthTokens } from '../utils/types.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport { createAssociateSoftwareTokenClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createAssociateSoftwareTokenClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../factories/createCognitoUserPoolEndpointResolver.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Sets up TOTP for the user.\n *\n * @returns SetUpTOTPOutput\n * @throws -{@link AssociateSoftwareTokenException}\n * Thrown if a service occurs while setting up TOTP.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n **/\nasync function setUpTOTP() {\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    assertTokenProviderConfig(authConfig);\n    const { userPoolEndpoint, userPoolId } = authConfig;\n    const { tokens } = await fetchAuthSession({ forceRefresh: false });\n    assertAuthTokens(tokens);\n    const username = tokens.idToken?.payload['cognito:username'] ?? '';\n    const associateSoftwareToken = createAssociateSoftwareTokenClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const { SecretCode } = await associateSoftwareToken({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.SetUpTOTP),\n    }, {\n        AccessToken: tokens.accessToken.toString(),\n    });\n    if (!SecretCode) {\n        // This should never happen.\n        throw new AuthError({\n            name: SETUP_TOTP_EXCEPTION,\n            message: 'Failed to set up TOTP.',\n        });\n    }\n    return getTOTPSetupDetails(SecretCode, JSON.stringify(username));\n}\n\nexport { setUpTOTP };\n"], "mappings": ";AAAA,SAASA,OAAO,EAAEC,gBAAgB,QAAQ,mBAAmB;AAC7D,SAASC,yBAAyB,EAAEC,UAAU,QAAQ,mCAAmC;AACzF,SAASC,SAAS,QAAQ,+BAA+B;AACzD,SAASC,oBAAoB,QAAQ,qBAAqB;AAC1D,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,sCAAsC;AAC7C,SAASC,kCAAkC,QAAQ,6GAA6G;AAChK,SAASC,qCAAqC,QAAQ,wDAAwD;;AAE9G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAQeC,SAASA,CAAA;EAAA,OAAAC,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,WAAA;EAAAA,UAAA,GAAAG,iBAAA,CAAxB,aAA2B;IACvB,MAAMC,UAAU,GAAGjB,OAAO,CAACkB,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpDlB,yBAAyB,CAACe,UAAU,CAAC;IACrC,MAAM;MAAEI,gBAAgB;MAAEC;IAAW,CAAC,GAAGL,UAAU;IACnD,MAAM;MAAEM;IAAO,CAAC,SAAStB,gBAAgB,CAAC;MAAEuB,YAAY,EAAE;IAAM,CAAC,CAAC;IAClEhB,gBAAgB,CAACe,MAAM,CAAC;IACxB,MAAME,QAAQ,GAAGF,MAAM,CAACG,OAAO,EAAEC,OAAO,CAAC,kBAAkB,CAAC,IAAI,EAAE;IAClE,MAAMC,sBAAsB,GAAGlB,kCAAkC,CAAC;MAC9DmB,gBAAgB,EAAElB,qCAAqC,CAAC;QACpDmB,gBAAgB,EAAET;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAM;MAAEU;IAAW,CAAC,SAASH,sBAAsB,CAAC;MAChDI,MAAM,EAAEzB,uBAAuB,CAACe,UAAU,CAAC;MAC3CW,cAAc,EAAExB,qBAAqB,CAACN,UAAU,CAAC+B,SAAS;IAC9D,CAAC,EAAE;MACCC,WAAW,EAAEZ,MAAM,CAACa,WAAW,CAACC,QAAQ,CAAC;IAC7C,CAAC,CAAC;IACF,IAAI,CAACN,UAAU,EAAE;MACb;MACA,MAAM,IAAI3B,SAAS,CAAC;QAChBkC,IAAI,EAAEjC,oBAAoB;QAC1BkC,OAAO,EAAE;MACb,CAAC,CAAC;IACN;IACA,OAAOjC,mBAAmB,CAACyB,UAAU,EAAES,IAAI,CAACC,SAAS,CAAChB,QAAQ,CAAC,CAAC;EACpE,CAAC;EAAA,OAAAZ,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}