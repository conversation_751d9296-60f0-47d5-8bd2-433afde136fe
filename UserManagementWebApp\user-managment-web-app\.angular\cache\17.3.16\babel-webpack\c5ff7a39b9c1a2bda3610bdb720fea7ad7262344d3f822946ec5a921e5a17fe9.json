{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { parseJsonError } from '@aws-amplify/core/internals/aws-client-utils';\nimport { assertServiceError } from '../../../../../../errors/utils/assertServiceError.mjs';\nimport { AuthError } from '../../../../../../errors/AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createEmptyResponseDeserializer = () => (/*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (response) {\n    if (response.statusCode >= 300) {\n      const error = yield parseJsonError(response);\n      assertServiceError(error);\n      throw new AuthError({\n        name: error.name,\n        message: error.message\n      });\n    } else {\n      return undefined;\n    }\n  });\n  return function (_x) {\n    return _ref.apply(this, arguments);\n  };\n}());\nexport { createEmptyResponseDeserializer };", "map": {"version": 3, "names": ["parseJsonError", "assertServiceError", "<PERSON>th<PERSON><PERSON><PERSON>", "createEmptyResponseDeserializer", "_ref", "_asyncToGenerator", "response", "statusCode", "error", "name", "message", "undefined", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/factories/serviceClients/cognitoIdentityProvider/shared/serde/createEmptyResponseDeserializer.mjs"], "sourcesContent": ["import { parseJsonError } from '@aws-amplify/core/internals/aws-client-utils';\nimport { assertServiceError } from '../../../../../../errors/utils/assertServiceError.mjs';\nimport { AuthError } from '../../../../../../errors/AuthError.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createEmptyResponseDeserializer = () => async (response) => {\n    if (response.statusCode >= 300) {\n        const error = await parseJsonError(response);\n        assertServiceError(error);\n        throw new AuthError({ name: error.name, message: error.message });\n    }\n    else {\n        return undefined;\n    }\n};\n\nexport { createEmptyResponseDeserializer };\n"], "mappings": ";AAAA,SAASA,cAAc,QAAQ,8CAA8C;AAC7E,SAASC,kBAAkB,QAAQ,uDAAuD;AAC1F,SAASC,SAAS,QAAQ,wCAAwC;;AAElE;AACA;AACA,MAAMC,+BAA+B,GAAGA,CAAA;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAM,WAAOC,QAAQ,EAAK;IAC9D,IAAIA,QAAQ,CAACC,UAAU,IAAI,GAAG,EAAE;MAC5B,MAAMC,KAAK,SAASR,cAAc,CAACM,QAAQ,CAAC;MAC5CL,kBAAkB,CAACO,KAAK,CAAC;MACzB,MAAM,IAAIN,SAAS,CAAC;QAAEO,IAAI,EAAED,KAAK,CAACC,IAAI;QAAEC,OAAO,EAAEF,KAAK,CAACE;MAAQ,CAAC,CAAC;IACrE,CAAC,MACI;MACD,OAAOC,SAAS;IACpB;EACJ,CAAC;EAAA,iBAAAC,EAAA;IAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;EAAA;AAAA;AAED,SAASX,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}