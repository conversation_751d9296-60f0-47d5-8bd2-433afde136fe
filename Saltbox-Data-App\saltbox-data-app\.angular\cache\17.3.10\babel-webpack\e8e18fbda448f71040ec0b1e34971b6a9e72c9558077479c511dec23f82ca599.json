{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/SaltboxActionableGrid/Saltbox-Data-App/saltbox-data-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport { firstValueFrom } from 'rxjs';\nimport { MenuModule } from 'primeng/menu';\nimport { ButtonModule } from 'primeng/button';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { NgClass } from '@angular/common';\nimport { SaveGridLayoutDialogComponent } from './save-grid-layout-dialog/save-grid-layout-dialog.component';\nimport { ConfirmationService } from 'primeng/api';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { ParameterTypes } from 'src/app/core/enums/actionable-grid';\nimport { TagModule } from 'primeng/tag';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/grid-layouts.service\";\nimport * as i2 from \"src/app/core/services/notification.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"src/app/core/services/ag-grid.service\";\nimport * as i5 from \"src/app/core/services/user-param-filter.service\";\nimport * as i6 from \"../../services/change-history.service\";\nimport * as i7 from \"primeng/menu\";\nimport * as i8 from \"primeng/tooltip\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"primeng/tag\";\nimport * as i11 from \"primeng/confirmdialog\";\nconst _c0 = (a0, a1) => ({\n  \"font-bold\": a0,\n  \"text-gray-400\": a1\n});\nconst _c1 = (a0, a1) => ({\n  \"pi-check\": a0,\n  \"pi-fw\": a1\n});\nfunction GridLayoutManagerComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.selectedLayout.name);\n  }\n}\nfunction GridLayoutManagerComponent_ng_template_8_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 13);\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c1, (item_r4.state == null ? null : item_r4.state.layout.id) === (ctx_r2.selectedLayout == null ? null : ctx_r2.selectedLayout.id), !ctx_r2.selectedLayout || (item_r4.state == null ? null : item_r4.state.layout.id) !== (ctx_r2.selectedLayout == null ? null : ctx_r2.selectedLayout.id)));\n  }\n}\nfunction GridLayoutManagerComponent_ng_template_8_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-tag\", 14);\n  }\n}\nfunction GridLayoutManagerComponent_ng_template_8_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"p-button\", 15);\n    i0.ɵɵlistener(\"onClick\", function GridLayoutManagerComponent_ng_template_8_Conditional_6_Template_p_button_onClick_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const item_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.openSaveLayoutDialog(\"edit\", item_r4.state == null ? null : item_r4.state.layout, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p-button\", 16);\n    i0.ɵɵlistener(\"onClick\", function GridLayoutManagerComponent_ng_template_8_Conditional_6_Template_p_button_onClick_2_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const item_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteLayout(item_r4.state == null ? null : item_r4.state.layout, $event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", item_r4.state && item_r4.state.disabled)(\"text\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"text\", true);\n  }\n}\nfunction GridLayoutManagerComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"span\", 12);\n    i0.ɵɵtemplate(3, GridLayoutManagerComponent_ng_template_8_Conditional_3_Template, 1, 4, \"i\", 13);\n    i0.ɵɵtext(4);\n    i0.ɵɵtemplate(5, GridLayoutManagerComponent_ng_template_8_Conditional_5_Template, 1, 0, \"p-tag\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, GridLayoutManagerComponent_ng_template_8_Conditional_6_Template, 3, 3, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"pTooltip\", item_r4.state && item_r4.state.disabled ? \"The view has changed. This layout is no longer valid\" : null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c0, (item_r4.state == null ? null : item_r4.state.layout) && (item_r4.state == null ? null : item_r4.state.layout.id) === (ctx_r2.selectedLayout == null ? null : ctx_r2.selectedLayout.id), item_r4.state && item_r4.state.disabled));\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(3, !item_r4.icon ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r4.label, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(5, (item_r4.state == null ? null : item_r4.state.layout == null ? null : item_r4.state.layout.isDefault) ? 5 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(6, (item_r4.state == null ? null : item_r4.state.layout == null ? null : item_r4.state.layout.editable) ? 6 : -1);\n  }\n}\nfunction GridLayoutManagerComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 17);\n    i0.ɵɵlistener(\"onClick\", function GridLayoutManagerComponent_ng_template_12_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      i0.ɵɵnextContext();\n      const cd_r7 = i0.ɵɵreference(11);\n      return i0.ɵɵresetView(cd_r7.accept());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"p-button\", 18);\n    i0.ɵɵlistener(\"onClick\", function GridLayoutManagerComponent_ng_template_12_Template_p_button_onClick_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      i0.ɵɵnextContext();\n      const cd_r7 = i0.ɵɵreference(11);\n      return i0.ɵɵresetView(cd_r7.reject());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"outlined\", true);\n  }\n}\nexport class GridLayoutManagerComponent {\n  constructor(gridLayoutService, notificationService, confirmationService, aGGridService, filterService, changeHistoryService) {\n    this.gridLayoutService = gridLayoutService;\n    this.notificationService = notificationService;\n    this.confirmationService = confirmationService;\n    this.aGGridService = aGGridService;\n    this.filterService = filterService;\n    this.changeHistoryService = changeHistoryService;\n    this.disabled = false;\n    this.selectedLayoutChange = new EventEmitter();\n    this.layoutMenuItems = [];\n    this.layoutFormMode = 'create';\n    this.showSaveLayoutDialog = false;\n    this.layoutHashId = '';\n    this.disableSlicerSettings = true;\n    this.disableUserParams = true;\n    this.reportHasSlicers = false;\n    this.reportHasUserParams = false;\n  }\n  ngOnChanges(changes) {\n    if (changes.layouts && changes.layouts.currentValue !== changes.layouts.previousValue) this.initLayouts();\n    if (changes.agGrid && changes.agGrid.currentValue !== changes.agGrid.previousValue) {\n      this.agGrid?.api?.addEventListener('firstDataRendered', () => {\n        this.setGridLayout(this.selectedLayout && this.selectedLayout.layoutHashId === this.layoutHashId ? this.selectedLayout : undefined);\n      });\n    }\n  }\n  initLayouts() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Default user params\n      _this.userParams = _this.reportInfo?.params;\n      _this.reportHasSlicers = _this.reportInfo?.formatConfig?.actionableGridColumnsConfig?.some(col => col.slicerFilter);\n      _this.reportHasUserParams = _this.reportInfo?.params?.length > 0;\n      _this.reportProfileParameters = _this.reportInfo?.params?.filter(param => param.paramType === ParameterTypes.UserProfile);\n      if (_this.reportHasSlicers) {\n        _this.filterService.recordRefinersChanged$.subscribe(params => {\n          _this.refinerUserParams = params.userParams;\n          // We should save only the record refiners that have changes, plus no need to save the unique values\n          _this.recordRefiners = params.refiners?.filter(r => r.hasChanges()).map(r => Object.assign({}, r));\n          _this.recordRefiners?.forEach(r => r.uniqueValues = []);\n          if (!_this.recordRefiners?.length) _this.recordRefiners = undefined;\n          _this.reloadData(false);\n        });\n      }\n      if (_this.reportHasUserParams) {\n        _this.filterService.userParamsChanged$.subscribe(userParams => {\n          _this.userParams = [...(_this.reportProfileParameters ?? []), ...userParams];\n          if (_this.reportHasSlicers) {\n            _this.recordRefiners = undefined;\n            _this.refinerUserParams = undefined;\n            _this.filterService.setRecordRefiners({\n              refiners: _this.recordRefiners,\n              userParams: _this.userParams\n            });\n          }\n          _this.reloadData(false);\n        });\n      }\n      _this.layoutHashId = yield _this.gridLayoutService.getGridHashId(_this.reportInfo);\n      _this.loadLayoutMenuItems();\n    })();\n  }\n  checkPendingChangesAndReloadData(showError = true) {\n    this.changeHistoryService.checkPendingChangesAndReset(() => this.reloadData(showError));\n  }\n  reloadData(showError = true) {\n    const userParamsAreValid = this.filterService.validateUserEnteredParams(this.userParams ?? [], showError);\n    this.filterService.requestDataReload([...(this.refinerUserParams ?? []), ...(this.userParams ?? [])], !userParamsAreValid);\n  }\n  loadLayoutMenuItems(switchLayout = true) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const defaultMenuItems = [{\n        label: 'Actions',\n        items: [{\n          label: 'Save Layout',\n          icon: 'pi pi-save',\n          disabled: _this2.selectedLayout?.editable === false,\n          command: () => {\n            if (_this2.selectedLayout) {\n              _this2.selectedLayout.agGridSettings = {\n                columnState: JSON.stringify(_this2.agGrid?.api.getColumnState()),\n                filterModel: JSON.stringify(_this2.agGrid?.api.getFilterModel()),\n                sideBar: _this2.agGrid?.api.getGridOption('sideBar'),\n                pivotMode: _this2.agGrid?.api.getGridOption('pivotMode')\n              };\n              _this2.selectedLayout.recordRefiners = _this2.recordRefiners?.length ? JSON.stringify(_this2.recordRefiners) : undefined;\n              _this2.selectedLayout.userParams = _this2.userParams?.length ? JSON.stringify(_this2.filterUserParameters(_this2.userParams)) : undefined;\n              _this2.selectedLayout.hasRecordRefiners = _this2.recordRefiners?.length ? true : false;\n              _this2.selectedLayout.hasUserParams = _this2.userParams?.length ? true : false;\n              _this2.onSaveLayout(_this2.selectedLayout);\n            } else _this2.openSaveLayoutDialog('create');\n          }\n        }, {\n          label: 'Save Layout As',\n          icon: 'pi pi-save',\n          command: () => {\n            _this2.openSaveLayoutDialog(_this2.selectedLayout ? 'saveas' : 'create', _this2.selectedLayout);\n          }\n        }, {\n          label: 'Reset Layout',\n          icon: 'pi pi-filter-slash',\n          command: () => {\n            _this2.switchLayout(undefined, false, true);\n          }\n        }]\n      }];\n      _this2.layoutMenuItems = [...defaultMenuItems];\n      if (_this2.layouts?.length) {\n        let otherMenuItems = [];\n        const publicLayouts = _this2.layouts.filter(layout => layout.isPublic);\n        const privateLayouts = _this2.layouts.filter(layout => !layout.isPublic);\n        otherMenuItems = publicLayouts?.length ? otherMenuItems.concat({\n          label: 'Shared Layouts',\n          items: _this2.layouts.filter(layout => layout.isPublic).map(layout => {\n            return {\n              label: layout.name,\n              command: () => {\n                _this2.switchLayout(layout);\n              },\n              state: {\n                layout,\n                disabled: layout.layoutHashId !== _this2.layoutHashId\n              }\n            };\n          })\n        }) : otherMenuItems;\n        otherMenuItems = privateLayouts?.length ? otherMenuItems.concat({\n          label: 'My Layouts',\n          items: _this2.layouts.filter(layout => !layout.isPublic).map(layout => {\n            return {\n              label: layout.name,\n              command: () => {\n                _this2.switchLayout(layout);\n              },\n              state: {\n                layout,\n                disabled: layout.layoutHashId !== _this2.layoutHashId\n              }\n            };\n          })\n        }) : otherMenuItems;\n        _this2.layoutMenuItems = [...defaultMenuItems, {\n          separator: true\n        }, ...otherMenuItems];\n      }\n      const defaultLayout = _this2.layouts?.find(layout => layout.isCurrentLayout && layout.layoutHashId === _this2.layoutHashId) ?? _this2.layouts?.find(layout => layout.isDefault && layout.layoutHashId === _this2.layoutHashId);\n      switchLayout && _this2.switchLayout(defaultLayout, true, false);\n      if (!_this2.selectedLayout && switchLayout) _this2.setLayout(undefined);\n    })();\n  }\n  resetToDefaultLayout() {\n    this.selectedLayout = undefined;\n    this.userParams = this.reportInfo?.params;\n    if (this.userParams?.filter(p => p.paramType === ParameterTypes.UserEntered)?.length) this.filterService.setUserParams(this.userParams);\n    if (this.reportHasSlicers) {\n      this.refinerUserParams = [];\n      this.recordRefiners = [];\n      this.filterService.setRecordRefiners({\n        refiners: this.recordRefiners,\n        userParams: this.userParams\n      });\n    }\n    this.reloadData(false);\n    this.setGridLayout(undefined);\n    this.setUserSelectedLayout();\n    this.selectedLayoutChange.emit(undefined);\n  }\n  switchLayout(layout, isDefaultLayout = false, setUserCurrentLayout = true) {\n    if (layout === this.selectedLayout && layout !== undefined) return;\n    if (layout && layout.layoutHashId !== this.layoutHashId) {\n      this.notificationService.showWarning('Incompatible Layout', 'The view has changed. This layout is no longer valid');\n      return;\n    }\n    this.changeHistoryService.checkPendingChangesAndReset(() => this.setLayout(layout, isDefaultLayout, setUserCurrentLayout));\n    //Disabling SAVE button if the layout is not editable\n    if (this.layoutMenuItems?.length && this.layoutMenuItems[0].items?.length) this.layoutMenuItems[0].items[0].disabled = layout?.editable === false;\n  }\n  setLayout(layout, isDefaultLayout = false, setUserCurrentLayout = true) {\n    if (!layout) {\n      this.resetToDefaultLayout();\n      return;\n    }\n    if (layout.hasUserParams && layout.userParams) {\n      this.userParams = this.getUserParams(layout, this.reportInfo.params);\n    }\n    this.filterService.setUserParams(this.userParams);\n    if (this.reportHasSlicers) {\n      this.recordRefiners = [];\n      this.refinerUserParams = [];\n      if (layout.hasRecordRefiners && layout.recordRefiners) {\n        this.recordRefiners = JSON.parse(layout.recordRefiners);\n        this.refinerUserParams = this.filterService.getReportParametersByRefiners(this.recordRefiners);\n      }\n      this.filterService.setRecordRefiners({\n        refiners: this.recordRefiners,\n        userParams: this.userParams\n      });\n    }\n    this.selectedLayout = layout;\n    this.selectedLayoutChange.emit(layout);\n    this.reloadData(!isDefaultLayout);\n    this.setGridLayout(layout);\n    if (setUserCurrentLayout) this.setUserSelectedLayout(layout);\n  }\n  setUserSelectedLayout(layout) {\n    this.gridLayoutService.setUserSelectedLayout({\n      projectId: +this.reportInfo.projectId,\n      projectVersionId: +this.reportInfo.projectVersionId,\n      gridId: this.reportInfo.reportId,\n      selectedLayoutId: layout?.id\n    });\n  }\n  getUserParams(layout, params) {\n    const profileParams = params?.filter(param => param.paramType === ParameterTypes.UserProfile) ?? [];\n    const userParams = layout?.userParams ? JSON.parse(layout.userParams) : [];\n    return [...profileParams, ...userParams];\n  }\n  setGridLayout(layout) {\n    if (layout) {\n      this.agGrid?.api?.applyColumnState({\n        state: JSON.parse(layout.agGridSettings.columnState),\n        applyOrder: true,\n        defaultState: {\n          hide: true\n        }\n      });\n      this.agGrid?.api?.setFilterModel(JSON.parse(layout.agGridSettings.filterModel));\n      this.agGrid?.api?.setGridOption('sideBar', layout.agGridSettings.sideBar);\n      this.agGrid?.api?.setGridOption('pivotMode', layout.agGridSettings.pivotMode);\n    } else {\n      this.agGrid?.api?.resetColumnState();\n      this.agGrid?.api?.setFilterModel({});\n      this.agGrid?.api?.autoSizeAllColumns();\n      this.agGrid?.api?.setGridOption('sideBar', null);\n      this.agGrid?.api?.setGridOption('pivotMode', false);\n    }\n  }\n  openSaveLayoutDialog(formMode = 'create', layout, event) {\n    event?.stopPropagation();\n    this.layoutFormMode = formMode;\n    this.disableSlicerSettings = !this.reportInfo?.formatConfig?.actionableGridColumnsConfig?.some(col => col.slicerFilter) || !(!layout || formMode !== 'edit' ? this.recordRefiners?.length : layout.recordRefiners?.length);\n    this.disableUserParams = !this.reportInfo?.params?.some(param => param.paramType === ParameterTypes.UserEntered) || !(!layout || formMode !== 'edit' ? this.userParams?.length : layout.userParams?.length);\n    // Save as mode\n    if (layout && formMode === 'edit') {\n      this.layoutToEdit = layout;\n      // If the editing layout is the same as current, update the layout settings\n      if (this.layoutToEdit.id === this.selectedLayout?.id) {\n        this.layoutToEdit.agGridSettings = {\n          columnState: JSON.stringify(this.agGrid.api.getColumnState()),\n          filterModel: JSON.stringify(this.agGrid.api.getFilterModel()),\n          sideBar: this.agGrid.api.getGridOption('sideBar'),\n          pivotMode: this.agGrid.api.getGridOption('pivotMode')\n        };\n        this.layoutToEdit.recordRefiners = this.recordRefiners ? JSON.stringify(this.recordRefiners) : undefined;\n        this.layoutToEdit.userParams = this.userParams?.length ? JSON.stringify(this.filterUserParameters(this.userParams)) : undefined;\n      }\n    }\n    // Create mode\n    else {\n      this.layoutToEdit = {\n        gridId: this.reportInfo.reportId,\n        recordRefiners: this.recordRefiners ? JSON.stringify(this.recordRefiners) : undefined,\n        userParams: this.userParams?.length ? JSON.stringify(this.filterUserParameters(this.userParams)) : undefined,\n        name: formMode === 'saveas' && this.selectedLayout ? this.selectedLayout.name + ' Copy' : \"\",\n        projectId: +this.reportInfo.projectId,\n        hasRecordRefiners: this.selectedLayout?.hasRecordRefiners && !this.disableSlicerSettings,\n        hasUserParams: this.selectedLayout?.hasUserParams && !this.disableUserParams,\n        projectVersionId: +this.reportInfo.projectVersionId,\n        isPublic: formMode === 'saveas' && this.selectedLayout ? this.selectedLayout.isPublic : false,\n        isDefault: formMode === 'saveas' && this.selectedLayout ? this.selectedLayout.isDefault : false,\n        editable: true,\n        agGridSettings: {\n          columnState: JSON.stringify(this.agGrid.api.getColumnState()),\n          filterModel: JSON.stringify(this.agGrid.api.getFilterModel()),\n          sideBar: this.agGrid.api.getGridOption('sideBar'),\n          pivotMode: this.agGrid.api.getGridOption('pivotMode')\n        },\n        layoutHashId: this.layoutHashId\n      };\n    }\n    this.showSaveLayoutDialog = true;\n  }\n  deleteLayout(layout, event) {\n    event?.stopPropagation();\n    this.confirmationService.confirm({\n      message: `Are you sure you want to delete the \"${layout.name}\" layout?`,\n      header: 'Delete Confirmation',\n      icon: 'pi pi-exclamation-triangle text-yellow-500',\n      accept: () => {\n        firstValueFrom(this.gridLayoutService.deleteLayout(layout.id)).then(() => {\n          const deletedLayoutIndex = this.layouts.findIndex(l => l.id === layout.id);\n          this.layouts.splice(deletedLayoutIndex, 1);\n          this.loadLayoutMenuItems(false);\n          if (this.selectedLayout?.id === layout.id) this.switchLayout(this.layouts.find(layout => layout.isDefault));\n          this.notificationService.showSuccess('Layout deleted successfully');\n        }).catch(error => {\n          console.error('Failed to delete layout:', error);\n          this.notificationService.showError('Failed to delete layout');\n        });\n      }\n    });\n  }\n  onSaveLayout(layout) {\n    if (layout.id) {\n      firstValueFrom(this.gridLayoutService.updateLayout(layout.id, layout)).then(updatedLayout => {\n        const savedLayoutIndex = this.layouts.findIndex(l => l.id === updatedLayout.id);\n        this.layouts[savedLayoutIndex] = updatedLayout;\n        // If this layout is being set as default, remove default from other layouts\n        if (updatedLayout.isDefault) {\n          this.layouts.filter(l => l.isDefault && l.id !== updatedLayout.id).forEach(l => {\n            l.isDefault = false;\n          });\n        }\n        this.loadLayoutMenuItems(false);\n        this.notificationService.showSuccess('Layout saved successfully');\n      }).catch(error => {\n        this.notificationService.showError('Failed to update the layout!');\n      });\n      return;\n    } else firstValueFrom(this.gridLayoutService.saveLayout(layout)).then(savedLayout => {\n      // If new layout is default, remove default from other layouts\n      if (savedLayout.isDefault) {\n        this.layouts.forEach(l => l.isDefault = false);\n      }\n      this.layouts.push(savedLayout);\n      this.switchLayout(savedLayout);\n      this.loadLayoutMenuItems(false);\n      this.notificationService.showSuccess('Layout saved successfully');\n    }).catch(error => {\n      this.notificationService.showError('Failed to save the layout!');\n    });\n  }\n  /**\n   * Filters user parameters to include only the essential properties\n   * @param params The original user parameters\n   * @returns A new array with filtered user parameter objects\n   */\n  filterUserParameters(params) {\n    if (!params?.length) return [];\n    return params.filter(param => param.paramType === ParameterTypes.UserEntered).map(param => ({\n      id: param.id,\n      reportSetupId: param.reportSetupId,\n      paramType: param.paramType,\n      paramCondition: param.paramCondition,\n      valueType: param.valueType,\n      defaultValue: param.defaultValue,\n      paramLabel: param.paramLabel,\n      profileParamter: param.profileParamter,\n      paramSource: param.paramSource,\n      action: param.action,\n      allowEmpty: param.allowEmpty,\n      parameterValue: param.parameterValue,\n      columnUID: param.columnUID\n    }));\n  }\n  static {\n    this.ɵfac = function GridLayoutManagerComponent_Factory(t) {\n      return new (t || GridLayoutManagerComponent)(i0.ɵɵdirectiveInject(i1.GridLayoutsService), i0.ɵɵdirectiveInject(i2.NotificationService), i0.ɵɵdirectiveInject(i3.ConfirmationService), i0.ɵɵdirectiveInject(i4.AGGridService), i0.ɵɵdirectiveInject(i5.UserParamFilterService), i0.ɵɵdirectiveInject(i6.ChangeHistoryService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GridLayoutManagerComponent,\n      selectors: [[\"app-grid-layout-manager\"]],\n      inputs: {\n        reportInfo: \"reportInfo\",\n        agGrid: \"agGrid\",\n        disabled: \"disabled\",\n        layouts: \"layouts\"\n      },\n      outputs: {\n        selectedLayoutChange: \"selectedLayoutChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([ConfirmationService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 13,\n      vars: 11,\n      consts: [[\"menu_layout\", \"\"], [\"cd\", \"\"], [\"pTooltip\", \"Layout\", \"tooltipPosition\", \"top\", \"styleClass\", \"min-w-0\", 3, \"onClick\", \"rounded\", \"disabled\"], [1, \"pi\", \"pi-th-large\", \"vertical-align-bottom\"], [1, \"ml-2\"], [1, \"pi\", \"pi-angle-down\", \"vertical-align-middle\", \"ml-1\"], [\"appendTo\", \"body\", \"styleClass\", \"grid-layouts-menu\", 3, \"popup\", \"model\"], [\"pTemplate\", \"item\"], [3, \"visibleChange\", \"saveLayout\", \"visible\", \"formMode\", \"layout\", \"disableUserParams\", \"disableSlicerSettings\", \"layouts\"], [\"pTemplate\", \"footer\"], [\"tooltipPosition\", \"top\", 1, \"p-ripple\", \"cursor-pointer\", \"p-element\", \"flex\", \"align-items-center\", \"justify-content-between\", \"p-menu-item-link\", \"p-3\", \"py-2\", 3, \"pTooltip\"], [1, \"flex\", \"align-items-center\"], [1, \"text-sm\", \"pr-3\", 3, \"ngClass\"], [1, \"mr-2\", \"pi\", \"text-xs\", 3, \"ngClass\"], [\"value\", \"default\", 1, \"ml-2\"], [\"size\", \"small\", \"type\", \"button\", \"styleClass\", \"m-0 mr-1\", \"icon\", \"pi pi-pencil text-sm\", 3, \"onClick\", \"disabled\", \"text\"], [\"size\", \"small\", \"type\", \"button\", \"styleClass\", \"m-0\", \"icon\", \"pi pi-trash text-sm\", \"severity\", \"danger\", 3, \"onClick\", \"text\"], [\"label\", \"Delete\", \"severity\", \"danger\", \"styleClass\", \"border-none\", 3, \"onClick\"], [\"label\", \"Cancel\", \"severity\", \"secondary\", 3, \"onClick\", \"outlined\"]],\n      template: function GridLayoutManagerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"p-button\", 2);\n          i0.ɵɵlistener(\"onClick\", function GridLayoutManagerComponent_Template_p_button_onClick_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const menu_layout_r2 = i0.ɵɵreference(7);\n            return i0.ɵɵresetView(menu_layout_r2.toggle($event));\n          });\n          i0.ɵɵelementStart(1, \"span\");\n          i0.ɵɵelement(2, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, GridLayoutManagerComponent_Conditional_3_Template, 2, 1, \"span\", 4);\n          i0.ɵɵelementStart(4, \"span\");\n          i0.ɵɵelement(5, \"i\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"p-menu\", 6, 0);\n          i0.ɵɵtemplate(8, GridLayoutManagerComponent_ng_template_8_Template, 7, 9, \"ng-template\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"app-save-grid-layout-dialog\", 8);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function GridLayoutManagerComponent_Template_app_save_grid_layout_dialog_visibleChange_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.showSaveLayoutDialog, $event) || (ctx.showSaveLayoutDialog = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"saveLayout\", function GridLayoutManagerComponent_Template_app_save_grid_layout_dialog_saveLayout_9_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSaveLayout($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p-confirmDialog\", null, 1);\n          i0.ɵɵtemplate(12, GridLayoutManagerComponent_ng_template_12_Template, 2, 1, \"ng-template\", 9);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"rounded\", true)(\"disabled\", ctx.disabled);\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(3, ctx.selectedLayout ? 3 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"popup\", true)(\"model\", ctx.layoutMenuItems);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.showSaveLayoutDialog);\n          i0.ɵɵproperty(\"formMode\", ctx.layoutFormMode)(\"layout\", ctx.layoutToEdit)(\"disableUserParams\", ctx.disableUserParams)(\"disableSlicerSettings\", ctx.disableSlicerSettings)(\"layouts\", ctx.layouts);\n        }\n      },\n      dependencies: [MenuModule, i7.Menu, i8.Tooltip, ButtonModule, i9.Button, i3.PrimeTemplate, TooltipModule, TagModule, i10.Tag, NgClass, SaveGridLayoutDialogComponent, ConfirmDialogModule, i11.ConfirmDialog],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "firstValueFrom", "MenuModule", "ButtonModule", "TooltipModule", "Ng<PERSON><PERSON>", "SaveGridLayoutDialogComponent", "ConfirmationService", "ConfirmDialogModule", "ParameterTypes", "TagModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r2", "selected<PERSON>ayout", "name", "ɵɵelement", "ɵɵproperty", "ɵɵpureFunction2", "_c1", "item_r4", "state", "layout", "id", "ɵɵlistener", "GridLayoutManagerComponent_ng_template_8_Conditional_6_Template_p_button_onClick_1_listener", "$event", "ɵɵrestoreView", "_r5", "ɵɵnextContext", "$implicit", "ɵɵresetView", "openSaveLayoutDialog", "GridLayoutManagerComponent_ng_template_8_Conditional_6_Template_p_button_onClick_2_listener", "deleteLayout", "disabled", "ɵɵtemplate", "GridLayoutManagerComponent_ng_template_8_Conditional_3_Template", "GridLayoutManagerComponent_ng_template_8_Conditional_5_Template", "GridLayoutManagerComponent_ng_template_8_Conditional_6_Template", "_c0", "ɵɵconditional", "icon", "ɵɵtextInterpolate1", "label", "isDefault", "editable", "GridLayoutManagerComponent_ng_template_12_Template_p_button_onClick_0_listener", "_r6", "cd_r7", "ɵɵreference", "accept", "GridLayoutManagerComponent_ng_template_12_Template_p_button_onClick_1_listener", "reject", "GridLayoutManagerComponent", "constructor", "gridLayoutService", "notificationService", "confirmationService", "aGGridService", "filterService", "changeHistoryService", "selectedLayoutChange", "layoutMenuItems", "layoutFormMode", "showSaveLayoutDialog", "layoutHashId", "disableSlicerSettings", "disableUserParams", "reportHasSlicers", "reportHasUserParams", "ngOnChanges", "changes", "layouts", "currentValue", "previousValue", "initLayouts", "agGrid", "api", "addEventListener", "setGridLayout", "undefined", "_this", "_asyncToGenerator", "userParams", "reportInfo", "params", "formatConfig", "actionableGridColumnsConfig", "some", "col", "slicerFilter", "length", "reportProfileParameters", "filter", "param", "paramType", "UserProfile", "recordRefinersChanged$", "subscribe", "refinerUserParams", "recordRefiners", "refiners", "r", "has<PERSON><PERSON><PERSON>", "map", "Object", "assign", "for<PERSON>ach", "uniqueValues", "reloadData", "userParamsChanged$", "setRecordRefiners", "getGridHashId", "loadLayoutMenuItems", "checkPendingChangesAndReloadData", "showError", "checkPendingChangesAndReset", "userParamsAreValid", "validateUserEnteredParams", "requestDataReload", "switchLayout", "_this2", "defaultMenuItems", "items", "command", "agGridSettings", "columnState", "JSON", "stringify", "getColumnState", "filterModel", "getFilterModel", "sideBar", "getGridOption", "pivotMode", "filterUserParameters", "hasRecordRefiners", "hasUserParams", "onSaveLayout", "otherMenuItems", "publicLayouts", "isPublic", "privateLayouts", "concat", "separator", "defaultLayout", "find", "isCurrentLayout", "setLayout", "resetToDefaultLayout", "p", "UserEntered", "setUserParams", "setUserSelectedLayout", "emit", "isDefaultLayout", "setUserCurrentLayout", "showWarning", "getUserParams", "parse", "getReportParametersByRefiners", "projectId", "projectVersionId", "gridId", "reportId", "selectedLayoutId", "profileParams", "applyColumnState", "applyOrder", "defaultState", "hide", "setFilterModel", "setGridOption", "resetColumnState", "autoSizeAllColumns", "formMode", "event", "stopPropagation", "layoutToEdit", "confirm", "message", "header", "then", "deletedLayoutIndex", "findIndex", "l", "splice", "showSuccess", "catch", "error", "console", "updateLayout", "updatedLayout", "savedLayoutIndex", "saveLayout", "savedLayout", "push", "reportSetupId", "paramCondition", "valueType", "defaultValue", "paramLabel", "profileParamter", "paramSource", "action", "allowEmpty", "parameterValue", "columnUID", "ɵɵdirectiveInject", "i1", "GridLayoutsService", "i2", "NotificationService", "i3", "i4", "AGGridService", "i5", "UserParamFilterService", "i6", "ChangeHistoryService", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "GridLayoutManagerComponent_Template", "rf", "ctx", "GridLayoutManagerComponent_Template_p_button_onClick_0_listener", "_r1", "menu_layout_r2", "toggle", "GridLayoutManagerComponent_Conditional_3_Template", "GridLayoutManagerComponent_ng_template_8_Template", "ɵɵtwoWayListener", "GridLayoutManagerComponent_Template_app_save_grid_layout_dialog_visibleChange_9_listener", "ɵɵtwoWayBindingSet", "GridLayoutManagerComponent_Template_app_save_grid_layout_dialog_saveLayout_9_listener", "GridLayoutManagerComponent_ng_template_12_Template", "ɵɵtwoWayProperty", "i7", "<PERSON><PERSON>", "i8", "<PERSON><PERSON><PERSON>", "i9", "<PERSON><PERSON>", "PrimeTemplate", "i10", "Tag", "i11", "ConfirmDialog", "encapsulation"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\actions-menu\\grid-layout-manager\\grid-layout-manager.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\actions-menu\\grid-layout-manager\\grid-layout-manager.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';\r\nimport { GridLayout } from '../../model/grid-layout';\r\nimport { GridLayoutsService } from '../../services/grid-layouts.service';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { MenuModule } from 'primeng/menu';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { NgClass } from '@angular/common';\r\nimport { SaveGridLayoutDialogComponent } from './save-grid-layout-dialog/save-grid-layout-dialog.component';\r\nimport { NotificationService } from 'src/app/core/services/notification.service';\r\nimport { ConfirmationService } from 'primeng/api';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { AGGridService } from 'src/app/core/services/ag-grid.service';\r\nimport { AgGridAngular } from 'ag-grid-angular';\r\nimport { ReportInfo } from 'src/app/core/models/report-info';\r\nimport { ParameterTypes } from 'src/app/core/enums/actionable-grid';\r\nimport { UserParamFilterService } from 'src/app/core/services/user-param-filter.service';\r\nimport { RecordRefiner } from '../../refine-records/models/record-refiner';\r\nimport { ReportParameter } from 'src/app/core/models/report-parameter';\r\nimport { ChangeHistoryService } from '../../services/change-history.service';\r\nimport { TagModule } from 'primeng/tag';\r\n\r\n@Component({\r\n  selector: 'app-grid-layout-manager',\r\n  templateUrl: './grid-layout-manager.component.html',\r\n  standalone: true,\r\n  imports: [\r\n    MenuModule,\r\n    ButtonModule,\r\n    TooltipModule,\r\n    TagModule,\r\n    NgClass,\r\n    SaveGridLayoutDialogComponent,\r\n    ConfirmDialogModule\r\n  ],\r\n  providers: [ConfirmationService]\r\n})\r\nexport class GridLayoutManagerComponent implements OnChanges {\r\n  @Input() reportInfo: ReportInfo;\r\n  @Input() agGrid: AgGridAngular;\r\n  @Input() disabled = false;\r\n  @Input() layouts: GridLayout[];\r\n\r\n  @Output() selectedLayoutChange = new EventEmitter<GridLayout>();\r\n\r\n  layoutMenuItems: MenuItem[] = [];\r\n  selectedLayout: GridLayout | undefined;\r\n  layoutToEdit: GridLayout | undefined;\r\n  layoutFormMode: 'create' | 'edit' | 'saveas' = 'create';\r\n  showSaveLayoutDialog = false;\r\n  layoutHashId = '';\r\n  disableSlicerSettings = true;\r\n  disableUserParams = true;\r\n  recordRefiners: RecordRefiner[] | undefined;\r\n  userParams: ReportParameter[] | undefined;\r\n  refinerUserParams: ReportParameter[] | undefined;\r\n  reportHasSlicers = false;\r\n  reportHasUserParams = false;\r\n  reportProfileParameters: ReportParameter[] | undefined;\r\n\r\n  constructor(\r\n    private gridLayoutService: GridLayoutsService,\r\n    private notificationService: NotificationService,\r\n    private confirmationService: ConfirmationService,\r\n    private aGGridService: AGGridService,\r\n    private filterService: UserParamFilterService,\r\n    private changeHistoryService: ChangeHistoryService\r\n  ) { }\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (changes.layouts && changes.layouts.currentValue !== changes.layouts.previousValue)\r\n      this.initLayouts();\r\n\r\n    if (changes.agGrid && changes.agGrid.currentValue !== changes.agGrid.previousValue) {\r\n      this.agGrid?.api?.addEventListener('firstDataRendered', () => {\r\n        this.setGridLayout(this.selectedLayout && this.selectedLayout.layoutHashId === this.layoutHashId ? this.selectedLayout : undefined);\r\n      });\r\n    }\r\n  }\r\n\r\n  async initLayouts() {\r\n    // Default user params\r\n    this.userParams = this.reportInfo?.params;\r\n    this.reportHasSlicers = this.reportInfo?.formatConfig?.actionableGridColumnsConfig?.some(col => col.slicerFilter);\r\n    this.reportHasUserParams = this.reportInfo?.params?.length > 0;\r\n    this.reportProfileParameters = this.reportInfo?.params?.filter(param => param.paramType === ParameterTypes.UserProfile);\r\n\r\n    if (this.reportHasSlicers) {\r\n      this.filterService.recordRefinersChanged$.subscribe((params) => {\r\n        this.refinerUserParams = params.userParams;\r\n        // We should save only the record refiners that have changes, plus no need to save the unique values\r\n        this.recordRefiners = params.refiners?.filter(r => r.hasChanges()).map(r => Object.assign({}, r));\r\n        this.recordRefiners?.forEach(r => r.uniqueValues = []);\r\n\r\n        if (!this.recordRefiners?.length)\r\n          this.recordRefiners = undefined;\r\n\r\n        this.reloadData(false);\r\n      });\r\n    }\r\n\r\n    if (this.reportHasUserParams) {\r\n      this.filterService.userParamsChanged$.subscribe((userParams) => {\r\n        this.userParams = [...(this.reportProfileParameters ?? []), ...userParams];\r\n\r\n        if (this.reportHasSlicers) {\r\n          this.recordRefiners = undefined;\r\n          this.refinerUserParams = undefined;\r\n          this.filterService.setRecordRefiners({ refiners: this.recordRefiners, userParams: this.userParams });\r\n        }\r\n\r\n        this.reloadData(false);\r\n      });\r\n    }\r\n\r\n    this.layoutHashId = await this.gridLayoutService.getGridHashId(this.reportInfo);\r\n    this.loadLayoutMenuItems();\r\n  }\r\n\r\n  checkPendingChangesAndReloadData(showError = true) {\r\n    this.changeHistoryService.checkPendingChangesAndReset(\r\n      () => this.reloadData(showError)\r\n    );\r\n  }\r\n\r\n  reloadData(showError = true) {\r\n    const userParamsAreValid = this.filterService.validateUserEnteredParams(this.userParams ?? [], showError);\r\n    this.filterService.requestDataReload([...(this.refinerUserParams ?? []), ...(this.userParams ?? [])], !userParamsAreValid);\r\n  }\r\n\r\n  async loadLayoutMenuItems(switchLayout: boolean = true) {\r\n    const defaultMenuItems: MenuItem[] = [\r\n      {\r\n        label: 'Actions',\r\n        items: [{\r\n          label: 'Save Layout',\r\n          icon: 'pi pi-save',\r\n          disabled: this.selectedLayout?.editable === false,\r\n          command: () => {\r\n            if (this.selectedLayout) {\r\n              this.selectedLayout.agGridSettings = {\r\n                columnState: JSON.stringify(this.agGrid?.api.getColumnState()),\r\n                filterModel: JSON.stringify(this.agGrid?.api.getFilterModel()),\r\n                sideBar: this.agGrid?.api.getGridOption('sideBar'),\r\n                pivotMode: this.agGrid?.api.getGridOption('pivotMode')\r\n              };\r\n              this.selectedLayout.recordRefiners = this.recordRefiners?.length ? JSON.stringify(this.recordRefiners) : undefined;\r\n              this.selectedLayout.userParams = this.userParams?.length ? JSON.stringify(this.filterUserParameters(this.userParams)) : undefined;\r\n              this.selectedLayout.hasRecordRefiners = this.recordRefiners?.length ? true : false;\r\n              this.selectedLayout.hasUserParams = this.userParams?.length ? true : false;\r\n              this.onSaveLayout(this.selectedLayout);\r\n            }\r\n            else\r\n              this.openSaveLayoutDialog('create');\r\n          }\r\n        },\r\n        {\r\n          label: 'Save Layout As',\r\n          icon: 'pi pi-save',\r\n          command: () => {\r\n            this.openSaveLayoutDialog(this.selectedLayout ? 'saveas' : 'create', this.selectedLayout);\r\n          }\r\n        },\r\n        {\r\n          label: 'Reset Layout',\r\n          icon: 'pi pi-filter-slash',\r\n          command: () => {\r\n            this.switchLayout(undefined, false, true);\r\n          }\r\n        }\r\n        ]\r\n      }];\r\n\r\n    this.layoutMenuItems = [...defaultMenuItems];\r\n\r\n    if (this.layouts?.length) {\r\n      let otherMenuItems: MenuItem[] = [];\r\n      const publicLayouts = this.layouts.filter(layout => layout.isPublic);\r\n      const privateLayouts = this.layouts.filter(layout => !layout.isPublic);\r\n\r\n      otherMenuItems = publicLayouts?.length ? otherMenuItems.concat({\r\n        label: 'Shared Layouts',\r\n        items: this.layouts.filter(layout => layout.isPublic).map((layout: GridLayout) => {\r\n          return {\r\n            label: layout.name,\r\n            command: () => {\r\n              this.switchLayout(layout);\r\n            },\r\n            state: { layout, disabled: layout.layoutHashId !== this.layoutHashId }\r\n          };\r\n        })\r\n      }) : otherMenuItems;\r\n\r\n      otherMenuItems = privateLayouts?.length ? otherMenuItems.concat({\r\n        label: 'My Layouts',\r\n        items: this.layouts.filter(layout => !layout.isPublic).map((layout: GridLayout) => {\r\n          return {\r\n            label: layout.name,\r\n            command: () => {\r\n              this.switchLayout(layout);\r\n            },\r\n            state: { layout, disabled: layout.layoutHashId !== this.layoutHashId }\r\n          };\r\n        })\r\n      }) : otherMenuItems;\r\n\r\n      this.layoutMenuItems = [...defaultMenuItems, { separator: true }, ...otherMenuItems];\r\n    }\r\n\r\n    const defaultLayout = this.layouts?.find(layout => layout.isCurrentLayout && layout.layoutHashId === this.layoutHashId) ??\r\n      this.layouts?.find(layout => layout.isDefault && layout.layoutHashId === this.layoutHashId);\r\n    switchLayout && this.switchLayout(defaultLayout, true, false);\r\n\r\n    if (!this.selectedLayout && switchLayout)\r\n      this.setLayout(undefined);\r\n  }\r\n\r\n  resetToDefaultLayout() {\r\n    this.selectedLayout = undefined;\r\n    this.userParams = this.reportInfo?.params;\r\n\r\n    if (this.userParams?.filter(p => p.paramType === ParameterTypes.UserEntered)?.length)\r\n      this.filterService.setUserParams(this.userParams);\r\n\r\n    if (this.reportHasSlicers) {\r\n      this.refinerUserParams = [];\r\n      this.recordRefiners = [];\r\n      this.filterService.setRecordRefiners({ refiners: this.recordRefiners, userParams: this.userParams });\r\n    }\r\n\r\n    this.reloadData(false);\r\n\r\n    this.setGridLayout(undefined);\r\n    this.setUserSelectedLayout();\r\n    this.selectedLayoutChange.emit(undefined);\r\n  }\r\n\r\n  switchLayout(layout: GridLayout | undefined, isDefaultLayout: boolean = false, setUserCurrentLayout: boolean = true) {\r\n    if (layout === this.selectedLayout && layout !== undefined)\r\n      return;\r\n\r\n    if (layout && layout.layoutHashId !== this.layoutHashId) {\r\n      this.notificationService.showWarning('Incompatible Layout', 'The view has changed. This layout is no longer valid');\r\n      return;\r\n    }\r\n\r\n    this.changeHistoryService.checkPendingChangesAndReset(\r\n      () => this.setLayout(layout, isDefaultLayout, setUserCurrentLayout)\r\n    );\r\n\r\n    //Disabling SAVE button if the layout is not editable\r\n    if (this.layoutMenuItems?.length && this.layoutMenuItems[0].items?.length)\r\n      this.layoutMenuItems[0].items[0].disabled = layout?.editable === false;\r\n  }\r\n\r\n  setLayout(layout: GridLayout, isDefaultLayout: boolean = false, setUserCurrentLayout: boolean = true) {\r\n    if (!layout) {\r\n      this.resetToDefaultLayout();\r\n      return;\r\n    }\r\n\r\n    if (layout.hasUserParams && layout.userParams) {\r\n      this.userParams = this.getUserParams(layout, this.reportInfo.params);\r\n    }\r\n    this.filterService.setUserParams(this.userParams);\r\n\r\n    if (this.reportHasSlicers) {\r\n      this.recordRefiners = [];\r\n      this.refinerUserParams = [];\r\n      if (layout.hasRecordRefiners && layout.recordRefiners) {\r\n        this.recordRefiners = JSON.parse(layout.recordRefiners);\r\n        this.refinerUserParams = this.filterService.getReportParametersByRefiners(this.recordRefiners);\r\n      }\r\n\r\n      this.filterService.setRecordRefiners({ refiners: this.recordRefiners, userParams: this.userParams });\r\n    }\r\n\r\n    this.selectedLayout = layout;\r\n    this.selectedLayoutChange.emit(layout);\r\n    this.reloadData(!isDefaultLayout);\r\n    this.setGridLayout(layout);\r\n\r\n    if (setUserCurrentLayout)\r\n      this.setUserSelectedLayout(layout);\r\n  }\r\n\r\n  setUserSelectedLayout(layout?: GridLayout) {\r\n    this.gridLayoutService.setUserSelectedLayout({\r\n      projectId: +this.reportInfo.projectId,\r\n      projectVersionId: +this.reportInfo.projectVersionId,\r\n      gridId: this.reportInfo.reportId,\r\n      selectedLayoutId: layout?.id\r\n    });\r\n  }\r\n\r\n  getUserParams(layout: GridLayout, params: any): ReportParameter[] {\r\n    const profileParams = params?.filter(param => param.paramType === ParameterTypes.UserProfile) ?? [];\r\n    const userParams = layout?.userParams ? JSON.parse(layout.userParams) : [];\r\n    return [...profileParams, ...userParams];\r\n  }\r\n\r\n  setGridLayout(layout?: GridLayout) {\r\n    if (layout) {\r\n      this.agGrid?.api?.applyColumnState({ state: JSON.parse(layout.agGridSettings.columnState), applyOrder: true, defaultState: { hide: true } });\r\n      this.agGrid?.api?.setFilterModel(JSON.parse(layout.agGridSettings.filterModel));\r\n      this.agGrid?.api?.setGridOption('sideBar', layout.agGridSettings.sideBar);\r\n      this.agGrid?.api?.setGridOption('pivotMode', layout.agGridSettings.pivotMode);\r\n    } else {\r\n      this.agGrid?.api?.resetColumnState();\r\n      this.agGrid?.api?.setFilterModel({});\r\n      this.agGrid?.api?.autoSizeAllColumns();\r\n      this.agGrid?.api?.setGridOption('sideBar', null);\r\n      this.agGrid?.api?.setGridOption('pivotMode', false);\r\n    }\r\n  }\r\n\r\n  openSaveLayoutDialog(formMode: 'create' | 'edit' | 'saveas' = 'create', layout?: GridLayout, event?: Event) {\r\n    event?.stopPropagation();\r\n\r\n    this.layoutFormMode = formMode;\r\n    this.disableSlicerSettings = !this.reportInfo?.formatConfig?.actionableGridColumnsConfig?.some(col => col.slicerFilter)\r\n      || !(!layout || formMode !== 'edit' ? this.recordRefiners?.length : layout.recordRefiners?.length);\r\n    this.disableUserParams = !this.reportInfo?.params?.some(param => param.paramType === ParameterTypes.UserEntered)\r\n      || !(!layout || formMode !== 'edit' ? this.userParams?.length : layout.userParams?.length);\r\n\r\n    // Save as mode\r\n    if (layout && formMode === 'edit') {\r\n      this.layoutToEdit = layout;\r\n\r\n      // If the editing layout is the same as current, update the layout settings\r\n      if (this.layoutToEdit.id === this.selectedLayout?.id) {\r\n        this.layoutToEdit.agGridSettings = {\r\n          columnState: JSON.stringify(this.agGrid.api.getColumnState()),\r\n          filterModel: JSON.stringify(this.agGrid.api.getFilterModel()),\r\n          sideBar: this.agGrid.api.getGridOption('sideBar'),\r\n          pivotMode: this.agGrid.api.getGridOption('pivotMode')\r\n        };\r\n\r\n        this.layoutToEdit.recordRefiners = this.recordRefiners ? JSON.stringify(this.recordRefiners) : undefined;\r\n        this.layoutToEdit.userParams = this.userParams?.length ? JSON.stringify(this.filterUserParameters(this.userParams)) : undefined;\r\n      }\r\n    }\r\n    // Create mode\r\n    else {\r\n      this.layoutToEdit = {\r\n        gridId: this.reportInfo.reportId,\r\n        recordRefiners: this.recordRefiners ? JSON.stringify(this.recordRefiners) : undefined,\r\n        userParams: this.userParams?.length ? JSON.stringify(this.filterUserParameters(this.userParams)) : undefined,\r\n        name: formMode === 'saveas' && this.selectedLayout ? this.selectedLayout.name + ' Copy' : \"\",\r\n        projectId: +this.reportInfo.projectId,\r\n        hasRecordRefiners: this.selectedLayout?.hasRecordRefiners && !this.disableSlicerSettings,\r\n        hasUserParams: this.selectedLayout?.hasUserParams && !this.disableUserParams,\r\n        projectVersionId: +this.reportInfo.projectVersionId,\r\n        isPublic: formMode === 'saveas' && this.selectedLayout ? this.selectedLayout.isPublic : false,\r\n        isDefault: formMode === 'saveas' && this.selectedLayout ? this.selectedLayout.isDefault : false,\r\n        editable: true,\r\n        agGridSettings: {\r\n          columnState: JSON.stringify(this.agGrid.api.getColumnState()),\r\n          filterModel: JSON.stringify(this.agGrid.api.getFilterModel()),\r\n          sideBar: this.agGrid.api.getGridOption('sideBar'),\r\n          pivotMode: this.agGrid.api.getGridOption('pivotMode')\r\n        },\r\n        layoutHashId: this.layoutHashId\r\n      };\r\n    }\r\n\r\n    this.showSaveLayoutDialog = true;\r\n  }\r\n\r\n  deleteLayout(layout: GridLayout, event: Event): void {\r\n    event?.stopPropagation();\r\n\r\n    this.confirmationService.confirm({\r\n      message: `Are you sure you want to delete the \"${layout.name}\" layout?`,\r\n      header: 'Delete Confirmation',\r\n      icon: 'pi pi-exclamation-triangle text-yellow-500',\r\n      accept: () => {\r\n        firstValueFrom(this.gridLayoutService.deleteLayout(layout.id))\r\n          .then(() => {\r\n            const deletedLayoutIndex = this.layouts.findIndex(l => l.id === layout.id);\r\n            this.layouts.splice(deletedLayoutIndex, 1);\r\n            this.loadLayoutMenuItems(false);\r\n\r\n            if (this.selectedLayout?.id === layout.id)\r\n              this.switchLayout(this.layouts.find(layout => layout.isDefault));\r\n\r\n            this.notificationService.showSuccess('Layout deleted successfully');\r\n          })\r\n          .catch(error => {\r\n            console.error('Failed to delete layout:', error);\r\n            this.notificationService.showError('Failed to delete layout');\r\n          });\r\n      }\r\n    });\r\n  }\r\n\r\n  onSaveLayout(layout: GridLayout) {\r\n    if (layout.id) {\r\n      firstValueFrom(this.gridLayoutService.updateLayout(layout.id, layout))\r\n        .then((updatedLayout) => {\r\n          const savedLayoutIndex = this.layouts.findIndex(l => l.id === updatedLayout.id);\r\n          this.layouts[savedLayoutIndex] = updatedLayout;\r\n\r\n          // If this layout is being set as default, remove default from other layouts\r\n          if (updatedLayout.isDefault) {\r\n            this.layouts.filter(l => l.isDefault && l.id !== updatedLayout.id)\r\n              .forEach(l => { l.isDefault = false; });\r\n          }\r\n\r\n          this.loadLayoutMenuItems(false);\r\n          this.notificationService.showSuccess('Layout saved successfully');\r\n        })\r\n        .catch(error => {\r\n          this.notificationService.showError('Failed to update the layout!');\r\n        });\r\n      return;\r\n    }\r\n    else\r\n      firstValueFrom(this.gridLayoutService.saveLayout(layout))\r\n        .then((savedLayout) => {\r\n          // If new layout is default, remove default from other layouts\r\n          if (savedLayout.isDefault) {\r\n            this.layouts.forEach(l => l.isDefault = false);\r\n          }\r\n\r\n          this.layouts.push(savedLayout);\r\n          this.switchLayout(savedLayout);\r\n\r\n          this.loadLayoutMenuItems(false);\r\n          this.notificationService.showSuccess('Layout saved successfully');\r\n        })\r\n        .catch(error => {\r\n          this.notificationService.showError('Failed to save the layout!');\r\n        });\r\n  }\r\n\r\n  /**\r\n   * Filters user parameters to include only the essential properties\r\n   * @param params The original user parameters\r\n   * @returns A new array with filtered user parameter objects\r\n   */\r\n  private filterUserParameters(params: ReportParameter[]): any[] {\r\n    if (!params?.length) return [];\r\n\r\n    return params.filter(param => param.paramType === ParameterTypes.UserEntered).map(param => ({\r\n      id: param.id,\r\n      reportSetupId: param.reportSetupId,\r\n      paramType: param.paramType,\r\n      paramCondition: param.paramCondition,\r\n      valueType: param.valueType,\r\n      defaultValue: param.defaultValue,\r\n      paramLabel: param.paramLabel,\r\n      profileParamter: param.profileParamter,\r\n      paramSource: param.paramSource,\r\n      action: param.action,\r\n      allowEmpty: param.allowEmpty,\r\n      parameterValue: param.parameterValue,\r\n      columnUID: param.columnUID\r\n    }));\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n", "<p-button [rounded]=\"true\" (onClick)=\"menu_layout.toggle($event)\" [disabled]=\"disabled\" pTooltip=\"Layout\"\r\n  tooltipPosition=\"top\" styleClass=\"min-w-0\">\r\n  <span><i class=\"pi pi-th-large vertical-align-bottom\"></i></span>\r\n  @if (selectedLayout) {\r\n  <span class=\"ml-2\">{{selectedLayout.name}}</span>\r\n  }\r\n  <span><i class=\"pi pi-angle-down vertical-align-middle ml-1\"></i></span>\r\n</p-button>\r\n\r\n<p-menu #menu_layout [popup]=\"true\" [model]=\"layoutMenuItems\" appendTo=\"body\" styleClass=\"grid-layouts-menu\">\r\n  <ng-template pTemplate=\"item\" let-item>\r\n    <div [pTooltip]=\"item.state && item.state.disabled ? 'The view has changed. This layout is no longer valid' : null\"\r\n      tooltipPosition=\"top\"\r\n      class=\"p-ripple cursor-pointer p-element flex align-items-center justify-content-between p-menu-item-link p-3 py-2\">\r\n      <div class=\"flex align-items-center\">\r\n        <span class=\"text-sm pr-3\" [ngClass]=\"{\r\n            'font-bold': item.state?.layout && item.state?.layout.id === selectedLayout?.id,\r\n            'text-gray-400': item.state && item.state.disabled\r\n          }\">\r\n          @if (!item.icon) {\r\n          <i [ngClass]=\"{\r\n                  'pi-check': item.state?.layout.id === selectedLayout?.id,\r\n                  'pi-fw': !selectedLayout || item.state?.layout.id !== selectedLayout?.id\r\n                }\" class=\"mr-2 pi text-xs\"></i>\r\n          }\r\n          {{item.label}}\r\n          @if (item.state?.layout?.isDefault){<p-tag value=\"default\" class=\"ml-2\"></p-tag>}\r\n        </span>\r\n      </div>\r\n      @if (item.state?.layout?.editable) {\r\n      <div class=\"flex align-items-center\">\r\n        <p-button size=\"small\" type=\"button\" [disabled]=\"item.state && item.state.disabled\"\r\n          styleClass=\"m-0 mr-1\" icon=\"pi pi-pencil text-sm\" [text]=\"true\"\r\n          (onClick)=\"openSaveLayoutDialog('edit', item.state?.layout, $event)\"></p-button>\r\n        <p-button size=\"small\" type=\"button\" styleClass=\"m-0\" icon=\"pi pi-trash text-sm\"\r\n          [text]=\"true\" severity=\"danger\" (onClick)=\"deleteLayout(item.state?.layout, $event)\"></p-button>\r\n      </div>\r\n      }\r\n    </div>\r\n  </ng-template>\r\n</p-menu>\r\n\r\n<app-save-grid-layout-dialog [(visible)]=\"showSaveLayoutDialog\" [formMode]=\"layoutFormMode\" [layout]=\"layoutToEdit\"\r\n  [disableUserParams]=\"disableUserParams\" [disableSlicerSettings]=\"disableSlicerSettings\" [layouts]=\"layouts\"\r\n  (saveLayout)=\"onSaveLayout($event)\">\r\n</app-save-grid-layout-dialog>\r\n\r\n<p-confirmDialog #cd>\r\n  <ng-template pTemplate=\"footer\">\r\n    <p-button label=\"Delete\" (onClick)=\"cd.accept()\" severity=\"danger\" styleClass=\"border-none\"></p-button>\r\n    <p-button label=\"Cancel\" (onClick)=\"cd.reject()\" severity=\"secondary\" [outlined]=\"true\"></p-button>\r\n  </ng-template>\r\n</p-confirmDialog>"], "mappings": ";AAAA,SAAmCA,YAAY,QAAkC,eAAe;AAGhG,SAASC,cAAc,QAAQ,MAAM;AAErC,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,6BAA6B,QAAQ,6DAA6D;AAE3G,SAASC,mBAAmB,QAAQ,aAAa;AACjD,SAASC,mBAAmB,QAAQ,uBAAuB;AAI3D,SAASC,cAAc,QAAQ,oCAAoC;AAKnE,SAASC,SAAS,QAAQ,aAAa;;;;;;;;;;;;;;;;;;;;;;;ICjBrCC,EAAA,CAAAC,cAAA,cAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA9BH,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,cAAA,CAAAC,IAAA,CAAuB;;;;;IAgBlCR,EAAA,CAAAS,SAAA,YAGqC;;;;;IAHlCT,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAW,eAAA,IAAAC,GAAA,GAAAC,OAAA,CAAAC,KAAA,kBAAAD,OAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAC,EAAA,OAAAV,MAAA,CAAAC,cAAA,kBAAAD,MAAA,CAAAC,cAAA,CAAAS,EAAA,IAAAV,MAAA,CAAAC,cAAA,KAAAM,OAAA,CAAAC,KAAA,kBAAAD,OAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAC,EAAA,OAAAV,MAAA,CAAAC,cAAA,kBAAAD,MAAA,CAAAC,cAAA,CAAAS,EAAA,GAGK;;;;;IAG4BhB,EAAA,CAAAS,SAAA,gBAA4C;;;;;;IAKlFT,EADF,CAAAC,cAAA,cAAqC,mBAGoC;IAArED,EAAA,CAAAiB,UAAA,qBAAAC,4FAAAC,MAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAR,OAAA,GAAAb,EAAA,CAAAsB,aAAA,GAAAC,SAAA;MAAA,MAAAjB,MAAA,GAAAN,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAWlB,MAAA,CAAAmB,oBAAA,CAAqB,MAAM,EAAAZ,OAAA,CAAAC,KAAA,kBAAAD,OAAA,CAAAC,KAAA,CAAAC,MAAA,EAAAI,MAAA,CAA6B;IAAA,EAAC;IAACnB,EAAA,CAAAG,YAAA,EAAW;IAClFH,EAAA,CAAAC,cAAA,mBACuF;IAArDD,EAAA,CAAAiB,UAAA,qBAAAS,4FAAAP,MAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAR,OAAA,GAAAb,EAAA,CAAAsB,aAAA,GAAAC,SAAA;MAAA,MAAAjB,MAAA,GAAAN,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAwB,WAAA,CAAWlB,MAAA,CAAAqB,YAAA,CAAAd,OAAA,CAAAC,KAAA,kBAAAD,OAAA,CAAAC,KAAA,CAAAC,MAAA,EAAAI,MAAA,CAAwC;IAAA,EAAC;IACxFnB,EADyF,CAAAG,YAAA,EAAW,EAC9F;;;;IALiCH,EAAA,CAAAI,SAAA,EAA8C;IAC/BJ,EADf,CAAAU,UAAA,aAAAG,OAAA,CAAAC,KAAA,IAAAD,OAAA,CAAAC,KAAA,CAAAc,QAAA,CAA8C,cAClB;IAG/D5B,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAU,UAAA,cAAa;;;;;IApBfV,EAJJ,CAAAC,cAAA,cAEsH,cAC/E,eAI9B;IACHD,EAAA,CAAA6B,UAAA,IAAAC,+DAAA,gBAAkB;IAMlB9B,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAA6B,UAAA,IAAAE,+DAAA,oBAAoC;IAExC/B,EADE,CAAAG,YAAA,EAAO,EACH;IACNH,EAAA,CAAA6B,UAAA,IAAAG,+DAAA,kBAAoC;IAStChC,EAAA,CAAAG,YAAA,EAAM;;;;;IA3BDH,EAAA,CAAAU,UAAA,aAAAG,OAAA,CAAAC,KAAA,IAAAD,OAAA,CAAAC,KAAA,CAAAc,QAAA,iEAA8G;IAIpF5B,EAAA,CAAAI,SAAA,GAGvB;IAHuBJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAW,eAAA,IAAAsB,GAAA,GAAApB,OAAA,CAAAC,KAAA,kBAAAD,OAAA,CAAAC,KAAA,CAAAC,MAAA,MAAAF,OAAA,CAAAC,KAAA,kBAAAD,OAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAC,EAAA,OAAAV,MAAA,CAAAC,cAAA,kBAAAD,MAAA,CAAAC,cAAA,CAAAS,EAAA,GAAAH,OAAA,CAAAC,KAAA,IAAAD,OAAA,CAAAC,KAAA,CAAAc,QAAA,EAGvB;IACF5B,EAAA,CAAAI,SAAA,EAKC;IALDJ,EAAA,CAAAkC,aAAA,KAAArB,OAAA,CAAAsB,IAAA,UAKC;IACDnC,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAAoC,kBAAA,MAAAvB,OAAA,CAAAwB,KAAA,MACA;IAAArC,EAAA,CAAAI,SAAA,EAAiF;IAAjFJ,EAAA,CAAAkC,aAAA,KAAArB,OAAA,CAAAC,KAAA,kBAAAD,OAAA,CAAAC,KAAA,CAAAC,MAAA,kBAAAF,OAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAuB,SAAA,WAAiF;IAGrFtC,EAAA,CAAAI,SAAA,EAQC;IARDJ,EAAA,CAAAkC,aAAA,KAAArB,OAAA,CAAAC,KAAA,kBAAAD,OAAA,CAAAC,KAAA,CAAAC,MAAA,kBAAAF,OAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAwB,QAAA,WAQC;;;;;;IAYHvC,EAAA,CAAAC,cAAA,mBAA4F;IAAnED,EAAA,CAAAiB,UAAA,qBAAAuB,+EAAA;MAAAxC,EAAA,CAAAoB,aAAA,CAAAqB,GAAA;MAAAzC,EAAA,CAAAsB,aAAA;MAAA,MAAAoB,KAAA,GAAA1C,EAAA,CAAA2C,WAAA;MAAA,OAAA3C,EAAA,CAAAwB,WAAA,CAAWkB,KAAA,CAAAE,MAAA,EAAW;IAAA,EAAC;IAA4C5C,EAAA,CAAAG,YAAA,EAAW;IACvGH,EAAA,CAAAC,cAAA,mBAAwF;IAA/DD,EAAA,CAAAiB,UAAA,qBAAA4B,+EAAA;MAAA7C,EAAA,CAAAoB,aAAA,CAAAqB,GAAA;MAAAzC,EAAA,CAAAsB,aAAA;MAAA,MAAAoB,KAAA,GAAA1C,EAAA,CAAA2C,WAAA;MAAA,OAAA3C,EAAA,CAAAwB,WAAA,CAAWkB,KAAA,CAAAI,MAAA,EAAW;IAAA,EAAC;IAAwC9C,EAAA,CAAAG,YAAA,EAAW;;;IAA7BH,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAU,UAAA,kBAAiB;;;ADZ3F,OAAM,MAAOqC,0BAA0B;EAuBrCC,YACUC,iBAAqC,EACrCC,mBAAwC,EACxCC,mBAAwC,EACxCC,aAA4B,EAC5BC,aAAqC,EACrCC,oBAA0C;IAL1C,KAAAL,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,oBAAoB,GAApBA,oBAAoB;IA1BrB,KAAA1B,QAAQ,GAAG,KAAK;IAGf,KAAA2B,oBAAoB,GAAG,IAAIlE,YAAY,EAAc;IAE/D,KAAAmE,eAAe,GAAe,EAAE;IAGhC,KAAAC,cAAc,GAAiC,QAAQ;IACvD,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,qBAAqB,GAAG,IAAI;IAC5B,KAAAC,iBAAiB,GAAG,IAAI;IAIxB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,mBAAmB,GAAG,KAAK;EAUvB;EAEJC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAACC,OAAO,IAAID,OAAO,CAACC,OAAO,CAACC,YAAY,KAAKF,OAAO,CAACC,OAAO,CAACE,aAAa,EACnF,IAAI,CAACC,WAAW,EAAE;IAEpB,IAAIJ,OAAO,CAACK,MAAM,IAAIL,OAAO,CAACK,MAAM,CAACH,YAAY,KAAKF,OAAO,CAACK,MAAM,CAACF,aAAa,EAAE;MAClF,IAAI,CAACE,MAAM,EAAEC,GAAG,EAAEC,gBAAgB,CAAC,mBAAmB,EAAE,MAAK;QAC3D,IAAI,CAACC,aAAa,CAAC,IAAI,CAAClE,cAAc,IAAI,IAAI,CAACA,cAAc,CAACoD,YAAY,KAAK,IAAI,CAACA,YAAY,GAAG,IAAI,CAACpD,cAAc,GAAGmE,SAAS,CAAC;MACrI,CAAC,CAAC;IACJ;EACF;EAEML,WAAWA,CAAA;IAAA,IAAAM,KAAA;IAAA,OAAAC,iBAAA;MACf;MACAD,KAAI,CAACE,UAAU,GAAGF,KAAI,CAACG,UAAU,EAAEC,MAAM;MACzCJ,KAAI,CAACb,gBAAgB,GAAGa,KAAI,CAACG,UAAU,EAAEE,YAAY,EAAEC,2BAA2B,EAAEC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,YAAY,CAAC;MACjHT,KAAI,CAACZ,mBAAmB,GAAGY,KAAI,CAACG,UAAU,EAAEC,MAAM,EAAEM,MAAM,GAAG,CAAC;MAC9DV,KAAI,CAACW,uBAAuB,GAAGX,KAAI,CAACG,UAAU,EAAEC,MAAM,EAAEQ,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,SAAS,KAAK3F,cAAc,CAAC4F,WAAW,CAAC;MAEvH,IAAIf,KAAI,CAACb,gBAAgB,EAAE;QACzBa,KAAI,CAACtB,aAAa,CAACsC,sBAAsB,CAACC,SAAS,CAAEb,MAAM,IAAI;UAC7DJ,KAAI,CAACkB,iBAAiB,GAAGd,MAAM,CAACF,UAAU;UAC1C;UACAF,KAAI,CAACmB,cAAc,GAAGf,MAAM,CAACgB,QAAQ,EAAER,MAAM,CAACS,CAAC,IAAIA,CAAC,CAACC,UAAU,EAAE,CAAC,CAACC,GAAG,CAACF,CAAC,IAAIG,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEJ,CAAC,CAAC,CAAC;UACjGrB,KAAI,CAACmB,cAAc,EAAEO,OAAO,CAACL,CAAC,IAAIA,CAAC,CAACM,YAAY,GAAG,EAAE,CAAC;UAEtD,IAAI,CAAC3B,KAAI,CAACmB,cAAc,EAAET,MAAM,EAC9BV,KAAI,CAACmB,cAAc,GAAGpB,SAAS;UAEjCC,KAAI,CAAC4B,UAAU,CAAC,KAAK,CAAC;QACxB,CAAC,CAAC;MACJ;MAEA,IAAI5B,KAAI,CAACZ,mBAAmB,EAAE;QAC5BY,KAAI,CAACtB,aAAa,CAACmD,kBAAkB,CAACZ,SAAS,CAAEf,UAAU,IAAI;UAC7DF,KAAI,CAACE,UAAU,GAAG,CAAC,IAAIF,KAAI,CAACW,uBAAuB,IAAI,EAAE,CAAC,EAAE,GAAGT,UAAU,CAAC;UAE1E,IAAIF,KAAI,CAACb,gBAAgB,EAAE;YACzBa,KAAI,CAACmB,cAAc,GAAGpB,SAAS;YAC/BC,KAAI,CAACkB,iBAAiB,GAAGnB,SAAS;YAClCC,KAAI,CAACtB,aAAa,CAACoD,iBAAiB,CAAC;cAAEV,QAAQ,EAAEpB,KAAI,CAACmB,cAAc;cAAEjB,UAAU,EAAEF,KAAI,CAACE;YAAU,CAAE,CAAC;UACtG;UAEAF,KAAI,CAAC4B,UAAU,CAAC,KAAK,CAAC;QACxB,CAAC,CAAC;MACJ;MAEA5B,KAAI,CAAChB,YAAY,SAASgB,KAAI,CAAC1B,iBAAiB,CAACyD,aAAa,CAAC/B,KAAI,CAACG,UAAU,CAAC;MAC/EH,KAAI,CAACgC,mBAAmB,EAAE;IAAC;EAC7B;EAEAC,gCAAgCA,CAACC,SAAS,GAAG,IAAI;IAC/C,IAAI,CAACvD,oBAAoB,CAACwD,2BAA2B,CACnD,MAAM,IAAI,CAACP,UAAU,CAACM,SAAS,CAAC,CACjC;EACH;EAEAN,UAAUA,CAACM,SAAS,GAAG,IAAI;IACzB,MAAME,kBAAkB,GAAG,IAAI,CAAC1D,aAAa,CAAC2D,yBAAyB,CAAC,IAAI,CAACnC,UAAU,IAAI,EAAE,EAAEgC,SAAS,CAAC;IACzG,IAAI,CAACxD,aAAa,CAAC4D,iBAAiB,CAAC,CAAC,IAAI,IAAI,CAACpB,iBAAiB,IAAI,EAAE,CAAC,EAAE,IAAI,IAAI,CAAChB,UAAU,IAAI,EAAE,CAAC,CAAC,EAAE,CAACkC,kBAAkB,CAAC;EAC5H;EAEMJ,mBAAmBA,CAACO,YAAA,GAAwB,IAAI;IAAA,IAAAC,MAAA;IAAA,OAAAvC,iBAAA;MACpD,MAAMwC,gBAAgB,GAAe,CACnC;QACE/E,KAAK,EAAE,SAAS;QAChBgF,KAAK,EAAE,CAAC;UACNhF,KAAK,EAAE,aAAa;UACpBF,IAAI,EAAE,YAAY;UAClBP,QAAQ,EAAEuF,MAAI,CAAC5G,cAAc,EAAEgC,QAAQ,KAAK,KAAK;UACjD+E,OAAO,EAAEA,CAAA,KAAK;YACZ,IAAIH,MAAI,CAAC5G,cAAc,EAAE;cACvB4G,MAAI,CAAC5G,cAAc,CAACgH,cAAc,GAAG;gBACnCC,WAAW,EAAEC,IAAI,CAACC,SAAS,CAACP,MAAI,CAAC7C,MAAM,EAAEC,GAAG,CAACoD,cAAc,EAAE,CAAC;gBAC9DC,WAAW,EAAEH,IAAI,CAACC,SAAS,CAACP,MAAI,CAAC7C,MAAM,EAAEC,GAAG,CAACsD,cAAc,EAAE,CAAC;gBAC9DC,OAAO,EAAEX,MAAI,CAAC7C,MAAM,EAAEC,GAAG,CAACwD,aAAa,CAAC,SAAS,CAAC;gBAClDC,SAAS,EAAEb,MAAI,CAAC7C,MAAM,EAAEC,GAAG,CAACwD,aAAa,CAAC,WAAW;eACtD;cACDZ,MAAI,CAAC5G,cAAc,CAACuF,cAAc,GAAGqB,MAAI,CAACrB,cAAc,EAAET,MAAM,GAAGoC,IAAI,CAACC,SAAS,CAACP,MAAI,CAACrB,cAAc,CAAC,GAAGpB,SAAS;cAClHyC,MAAI,CAAC5G,cAAc,CAACsE,UAAU,GAAGsC,MAAI,CAACtC,UAAU,EAAEQ,MAAM,GAAGoC,IAAI,CAACC,SAAS,CAACP,MAAI,CAACc,oBAAoB,CAACd,MAAI,CAACtC,UAAU,CAAC,CAAC,GAAGH,SAAS;cACjIyC,MAAI,CAAC5G,cAAc,CAAC2H,iBAAiB,GAAGf,MAAI,CAACrB,cAAc,EAAET,MAAM,GAAG,IAAI,GAAG,KAAK;cAClF8B,MAAI,CAAC5G,cAAc,CAAC4H,aAAa,GAAGhB,MAAI,CAACtC,UAAU,EAAEQ,MAAM,GAAG,IAAI,GAAG,KAAK;cAC1E8B,MAAI,CAACiB,YAAY,CAACjB,MAAI,CAAC5G,cAAc,CAAC;YACxC,CAAC,MAEC4G,MAAI,CAAC1F,oBAAoB,CAAC,QAAQ,CAAC;UACvC;SACD,EACD;UACEY,KAAK,EAAE,gBAAgB;UACvBF,IAAI,EAAE,YAAY;UAClBmF,OAAO,EAAEA,CAAA,KAAK;YACZH,MAAI,CAAC1F,oBAAoB,CAAC0F,MAAI,CAAC5G,cAAc,GAAG,QAAQ,GAAG,QAAQ,EAAE4G,MAAI,CAAC5G,cAAc,CAAC;UAC3F;SACD,EACD;UACE8B,KAAK,EAAE,cAAc;UACrBF,IAAI,EAAE,oBAAoB;UAC1BmF,OAAO,EAAEA,CAAA,KAAK;YACZH,MAAI,CAACD,YAAY,CAACxC,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC;UAC3C;SACD;OAEF,CAAC;MAEJyC,MAAI,CAAC3D,eAAe,GAAG,CAAC,GAAG4D,gBAAgB,CAAC;MAE5C,IAAID,MAAI,CAACjD,OAAO,EAAEmB,MAAM,EAAE;QACxB,IAAIgD,cAAc,GAAe,EAAE;QACnC,MAAMC,aAAa,GAAGnB,MAAI,CAACjD,OAAO,CAACqB,MAAM,CAACxE,MAAM,IAAIA,MAAM,CAACwH,QAAQ,CAAC;QACpE,MAAMC,cAAc,GAAGrB,MAAI,CAACjD,OAAO,CAACqB,MAAM,CAACxE,MAAM,IAAI,CAACA,MAAM,CAACwH,QAAQ,CAAC;QAEtEF,cAAc,GAAGC,aAAa,EAAEjD,MAAM,GAAGgD,cAAc,CAACI,MAAM,CAAC;UAC7DpG,KAAK,EAAE,gBAAgB;UACvBgF,KAAK,EAAEF,MAAI,CAACjD,OAAO,CAACqB,MAAM,CAACxE,MAAM,IAAIA,MAAM,CAACwH,QAAQ,CAAC,CAACrC,GAAG,CAAEnF,MAAkB,IAAI;YAC/E,OAAO;cACLsB,KAAK,EAAEtB,MAAM,CAACP,IAAI;cAClB8G,OAAO,EAAEA,CAAA,KAAK;gBACZH,MAAI,CAACD,YAAY,CAACnG,MAAM,CAAC;cAC3B,CAAC;cACDD,KAAK,EAAE;gBAAEC,MAAM;gBAAEa,QAAQ,EAAEb,MAAM,CAAC4C,YAAY,KAAKwD,MAAI,CAACxD;cAAY;aACrE;UACH,CAAC;SACF,CAAC,GAAG0E,cAAc;QAEnBA,cAAc,GAAGG,cAAc,EAAEnD,MAAM,GAAGgD,cAAc,CAACI,MAAM,CAAC;UAC9DpG,KAAK,EAAE,YAAY;UACnBgF,KAAK,EAAEF,MAAI,CAACjD,OAAO,CAACqB,MAAM,CAACxE,MAAM,IAAI,CAACA,MAAM,CAACwH,QAAQ,CAAC,CAACrC,GAAG,CAAEnF,MAAkB,IAAI;YAChF,OAAO;cACLsB,KAAK,EAAEtB,MAAM,CAACP,IAAI;cAClB8G,OAAO,EAAEA,CAAA,KAAK;gBACZH,MAAI,CAACD,YAAY,CAACnG,MAAM,CAAC;cAC3B,CAAC;cACDD,KAAK,EAAE;gBAAEC,MAAM;gBAAEa,QAAQ,EAAEb,MAAM,CAAC4C,YAAY,KAAKwD,MAAI,CAACxD;cAAY;aACrE;UACH,CAAC;SACF,CAAC,GAAG0E,cAAc;QAEnBlB,MAAI,CAAC3D,eAAe,GAAG,CAAC,GAAG4D,gBAAgB,EAAE;UAAEsB,SAAS,EAAE;QAAI,CAAE,EAAE,GAAGL,cAAc,CAAC;MACtF;MAEA,MAAMM,aAAa,GAAGxB,MAAI,CAACjD,OAAO,EAAE0E,IAAI,CAAC7H,MAAM,IAAIA,MAAM,CAAC8H,eAAe,IAAI9H,MAAM,CAAC4C,YAAY,KAAKwD,MAAI,CAACxD,YAAY,CAAC,IACrHwD,MAAI,CAACjD,OAAO,EAAE0E,IAAI,CAAC7H,MAAM,IAAIA,MAAM,CAACuB,SAAS,IAAIvB,MAAM,CAAC4C,YAAY,KAAKwD,MAAI,CAACxD,YAAY,CAAC;MAC7FuD,YAAY,IAAIC,MAAI,CAACD,YAAY,CAACyB,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC;MAE7D,IAAI,CAACxB,MAAI,CAAC5G,cAAc,IAAI2G,YAAY,EACtCC,MAAI,CAAC2B,SAAS,CAACpE,SAAS,CAAC;IAAC;EAC9B;EAEAqE,oBAAoBA,CAAA;IAClB,IAAI,CAACxI,cAAc,GAAGmE,SAAS;IAC/B,IAAI,CAACG,UAAU,GAAG,IAAI,CAACC,UAAU,EAAEC,MAAM;IAEzC,IAAI,IAAI,CAACF,UAAU,EAAEU,MAAM,CAACyD,CAAC,IAAIA,CAAC,CAACvD,SAAS,KAAK3F,cAAc,CAACmJ,WAAW,CAAC,EAAE5D,MAAM,EAClF,IAAI,CAAChC,aAAa,CAAC6F,aAAa,CAAC,IAAI,CAACrE,UAAU,CAAC;IAEnD,IAAI,IAAI,CAACf,gBAAgB,EAAE;MACzB,IAAI,CAAC+B,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACzC,aAAa,CAACoD,iBAAiB,CAAC;QAAEV,QAAQ,EAAE,IAAI,CAACD,cAAc;QAAEjB,UAAU,EAAE,IAAI,CAACA;MAAU,CAAE,CAAC;IACtG;IAEA,IAAI,CAAC0B,UAAU,CAAC,KAAK,CAAC;IAEtB,IAAI,CAAC9B,aAAa,CAACC,SAAS,CAAC;IAC7B,IAAI,CAACyE,qBAAqB,EAAE;IAC5B,IAAI,CAAC5F,oBAAoB,CAAC6F,IAAI,CAAC1E,SAAS,CAAC;EAC3C;EAEAwC,YAAYA,CAACnG,MAA8B,EAAEsI,eAAA,GAA2B,KAAK,EAAEC,oBAAA,GAAgC,IAAI;IACjH,IAAIvI,MAAM,KAAK,IAAI,CAACR,cAAc,IAAIQ,MAAM,KAAK2D,SAAS,EACxD;IAEF,IAAI3D,MAAM,IAAIA,MAAM,CAAC4C,YAAY,KAAK,IAAI,CAACA,YAAY,EAAE;MACvD,IAAI,CAACT,mBAAmB,CAACqG,WAAW,CAAC,qBAAqB,EAAE,sDAAsD,CAAC;MACnH;IACF;IAEA,IAAI,CAACjG,oBAAoB,CAACwD,2BAA2B,CACnD,MAAM,IAAI,CAACgC,SAAS,CAAC/H,MAAM,EAAEsI,eAAe,EAAEC,oBAAoB,CAAC,CACpE;IAED;IACA,IAAI,IAAI,CAAC9F,eAAe,EAAE6B,MAAM,IAAI,IAAI,CAAC7B,eAAe,CAAC,CAAC,CAAC,CAAC6D,KAAK,EAAEhC,MAAM,EACvE,IAAI,CAAC7B,eAAe,CAAC,CAAC,CAAC,CAAC6D,KAAK,CAAC,CAAC,CAAC,CAACzF,QAAQ,GAAGb,MAAM,EAAEwB,QAAQ,KAAK,KAAK;EAC1E;EAEAuG,SAASA,CAAC/H,MAAkB,EAAEsI,eAAA,GAA2B,KAAK,EAAEC,oBAAA,GAAgC,IAAI;IAClG,IAAI,CAACvI,MAAM,EAAE;MACX,IAAI,CAACgI,oBAAoB,EAAE;MAC3B;IACF;IAEA,IAAIhI,MAAM,CAACoH,aAAa,IAAIpH,MAAM,CAAC8D,UAAU,EAAE;MAC7C,IAAI,CAACA,UAAU,GAAG,IAAI,CAAC2E,aAAa,CAACzI,MAAM,EAAE,IAAI,CAAC+D,UAAU,CAACC,MAAM,CAAC;IACtE;IACA,IAAI,CAAC1B,aAAa,CAAC6F,aAAa,CAAC,IAAI,CAACrE,UAAU,CAAC;IAEjD,IAAI,IAAI,CAACf,gBAAgB,EAAE;MACzB,IAAI,CAACgC,cAAc,GAAG,EAAE;MACxB,IAAI,CAACD,iBAAiB,GAAG,EAAE;MAC3B,IAAI9E,MAAM,CAACmH,iBAAiB,IAAInH,MAAM,CAAC+E,cAAc,EAAE;QACrD,IAAI,CAACA,cAAc,GAAG2B,IAAI,CAACgC,KAAK,CAAC1I,MAAM,CAAC+E,cAAc,CAAC;QACvD,IAAI,CAACD,iBAAiB,GAAG,IAAI,CAACxC,aAAa,CAACqG,6BAA6B,CAAC,IAAI,CAAC5D,cAAc,CAAC;MAChG;MAEA,IAAI,CAACzC,aAAa,CAACoD,iBAAiB,CAAC;QAAEV,QAAQ,EAAE,IAAI,CAACD,cAAc;QAAEjB,UAAU,EAAE,IAAI,CAACA;MAAU,CAAE,CAAC;IACtG;IAEA,IAAI,CAACtE,cAAc,GAAGQ,MAAM;IAC5B,IAAI,CAACwC,oBAAoB,CAAC6F,IAAI,CAACrI,MAAM,CAAC;IACtC,IAAI,CAACwF,UAAU,CAAC,CAAC8C,eAAe,CAAC;IACjC,IAAI,CAAC5E,aAAa,CAAC1D,MAAM,CAAC;IAE1B,IAAIuI,oBAAoB,EACtB,IAAI,CAACH,qBAAqB,CAACpI,MAAM,CAAC;EACtC;EAEAoI,qBAAqBA,CAACpI,MAAmB;IACvC,IAAI,CAACkC,iBAAiB,CAACkG,qBAAqB,CAAC;MAC3CQ,SAAS,EAAE,CAAC,IAAI,CAAC7E,UAAU,CAAC6E,SAAS;MACrCC,gBAAgB,EAAE,CAAC,IAAI,CAAC9E,UAAU,CAAC8E,gBAAgB;MACnDC,MAAM,EAAE,IAAI,CAAC/E,UAAU,CAACgF,QAAQ;MAChCC,gBAAgB,EAAEhJ,MAAM,EAAEC;KAC3B,CAAC;EACJ;EAEAwI,aAAaA,CAACzI,MAAkB,EAAEgE,MAAW;IAC3C,MAAMiF,aAAa,GAAGjF,MAAM,EAAEQ,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,SAAS,KAAK3F,cAAc,CAAC4F,WAAW,CAAC,IAAI,EAAE;IACnG,MAAMb,UAAU,GAAG9D,MAAM,EAAE8D,UAAU,GAAG4C,IAAI,CAACgC,KAAK,CAAC1I,MAAM,CAAC8D,UAAU,CAAC,GAAG,EAAE;IAC1E,OAAO,CAAC,GAAGmF,aAAa,EAAE,GAAGnF,UAAU,CAAC;EAC1C;EAEAJ,aAAaA,CAAC1D,MAAmB;IAC/B,IAAIA,MAAM,EAAE;MACV,IAAI,CAACuD,MAAM,EAAEC,GAAG,EAAE0F,gBAAgB,CAAC;QAAEnJ,KAAK,EAAE2G,IAAI,CAACgC,KAAK,CAAC1I,MAAM,CAACwG,cAAc,CAACC,WAAW,CAAC;QAAE0C,UAAU,EAAE,IAAI;QAAEC,YAAY,EAAE;UAAEC,IAAI,EAAE;QAAI;MAAE,CAAE,CAAC;MAC5I,IAAI,CAAC9F,MAAM,EAAEC,GAAG,EAAE8F,cAAc,CAAC5C,IAAI,CAACgC,KAAK,CAAC1I,MAAM,CAACwG,cAAc,CAACK,WAAW,CAAC,CAAC;MAC/E,IAAI,CAACtD,MAAM,EAAEC,GAAG,EAAE+F,aAAa,CAAC,SAAS,EAAEvJ,MAAM,CAACwG,cAAc,CAACO,OAAO,CAAC;MACzE,IAAI,CAACxD,MAAM,EAAEC,GAAG,EAAE+F,aAAa,CAAC,WAAW,EAAEvJ,MAAM,CAACwG,cAAc,CAACS,SAAS,CAAC;IAC/E,CAAC,MAAM;MACL,IAAI,CAAC1D,MAAM,EAAEC,GAAG,EAAEgG,gBAAgB,EAAE;MACpC,IAAI,CAACjG,MAAM,EAAEC,GAAG,EAAE8F,cAAc,CAAC,EAAE,CAAC;MACpC,IAAI,CAAC/F,MAAM,EAAEC,GAAG,EAAEiG,kBAAkB,EAAE;MACtC,IAAI,CAAClG,MAAM,EAAEC,GAAG,EAAE+F,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC;MAChD,IAAI,CAAChG,MAAM,EAAEC,GAAG,EAAE+F,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC;IACrD;EACF;EAEA7I,oBAAoBA,CAACgJ,QAAA,GAAyC,QAAQ,EAAE1J,MAAmB,EAAE2J,KAAa;IACxGA,KAAK,EAAEC,eAAe,EAAE;IAExB,IAAI,CAAClH,cAAc,GAAGgH,QAAQ;IAC9B,IAAI,CAAC7G,qBAAqB,GAAG,CAAC,IAAI,CAACkB,UAAU,EAAEE,YAAY,EAAEC,2BAA2B,EAAEC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,YAAY,CAAC,IAClH,EAAE,CAACrE,MAAM,IAAI0J,QAAQ,KAAK,MAAM,GAAG,IAAI,CAAC3E,cAAc,EAAET,MAAM,GAAGtE,MAAM,CAAC+E,cAAc,EAAET,MAAM,CAAC;IACpG,IAAI,CAACxB,iBAAiB,GAAG,CAAC,IAAI,CAACiB,UAAU,EAAEC,MAAM,EAAEG,IAAI,CAACM,KAAK,IAAIA,KAAK,CAACC,SAAS,KAAK3F,cAAc,CAACmJ,WAAW,CAAC,IAC3G,EAAE,CAAClI,MAAM,IAAI0J,QAAQ,KAAK,MAAM,GAAG,IAAI,CAAC5F,UAAU,EAAEQ,MAAM,GAAGtE,MAAM,CAAC8D,UAAU,EAAEQ,MAAM,CAAC;IAE5F;IACA,IAAItE,MAAM,IAAI0J,QAAQ,KAAK,MAAM,EAAE;MACjC,IAAI,CAACG,YAAY,GAAG7J,MAAM;MAE1B;MACA,IAAI,IAAI,CAAC6J,YAAY,CAAC5J,EAAE,KAAK,IAAI,CAACT,cAAc,EAAES,EAAE,EAAE;QACpD,IAAI,CAAC4J,YAAY,CAACrD,cAAc,GAAG;UACjCC,WAAW,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACpD,MAAM,CAACC,GAAG,CAACoD,cAAc,EAAE,CAAC;UAC7DC,WAAW,EAAEH,IAAI,CAACC,SAAS,CAAC,IAAI,CAACpD,MAAM,CAACC,GAAG,CAACsD,cAAc,EAAE,CAAC;UAC7DC,OAAO,EAAE,IAAI,CAACxD,MAAM,CAACC,GAAG,CAACwD,aAAa,CAAC,SAAS,CAAC;UACjDC,SAAS,EAAE,IAAI,CAAC1D,MAAM,CAACC,GAAG,CAACwD,aAAa,CAAC,WAAW;SACrD;QAED,IAAI,CAAC6C,YAAY,CAAC9E,cAAc,GAAG,IAAI,CAACA,cAAc,GAAG2B,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC5B,cAAc,CAAC,GAAGpB,SAAS;QACxG,IAAI,CAACkG,YAAY,CAAC/F,UAAU,GAAG,IAAI,CAACA,UAAU,EAAEQ,MAAM,GAAGoC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACO,oBAAoB,CAAC,IAAI,CAACpD,UAAU,CAAC,CAAC,GAAGH,SAAS;MACjI;IACF;IACA;IAAA,KACK;MACH,IAAI,CAACkG,YAAY,GAAG;QAClBf,MAAM,EAAE,IAAI,CAAC/E,UAAU,CAACgF,QAAQ;QAChChE,cAAc,EAAE,IAAI,CAACA,cAAc,GAAG2B,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC5B,cAAc,CAAC,GAAGpB,SAAS;QACrFG,UAAU,EAAE,IAAI,CAACA,UAAU,EAAEQ,MAAM,GAAGoC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACO,oBAAoB,CAAC,IAAI,CAACpD,UAAU,CAAC,CAAC,GAAGH,SAAS;QAC5GlE,IAAI,EAAEiK,QAAQ,KAAK,QAAQ,IAAI,IAAI,CAAClK,cAAc,GAAG,IAAI,CAACA,cAAc,CAACC,IAAI,GAAG,OAAO,GAAG,EAAE;QAC5FmJ,SAAS,EAAE,CAAC,IAAI,CAAC7E,UAAU,CAAC6E,SAAS;QACrCzB,iBAAiB,EAAE,IAAI,CAAC3H,cAAc,EAAE2H,iBAAiB,IAAI,CAAC,IAAI,CAACtE,qBAAqB;QACxFuE,aAAa,EAAE,IAAI,CAAC5H,cAAc,EAAE4H,aAAa,IAAI,CAAC,IAAI,CAACtE,iBAAiB;QAC5E+F,gBAAgB,EAAE,CAAC,IAAI,CAAC9E,UAAU,CAAC8E,gBAAgB;QACnDrB,QAAQ,EAAEkC,QAAQ,KAAK,QAAQ,IAAI,IAAI,CAAClK,cAAc,GAAG,IAAI,CAACA,cAAc,CAACgI,QAAQ,GAAG,KAAK;QAC7FjG,SAAS,EAAEmI,QAAQ,KAAK,QAAQ,IAAI,IAAI,CAAClK,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC+B,SAAS,GAAG,KAAK;QAC/FC,QAAQ,EAAE,IAAI;QACdgF,cAAc,EAAE;UACdC,WAAW,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACpD,MAAM,CAACC,GAAG,CAACoD,cAAc,EAAE,CAAC;UAC7DC,WAAW,EAAEH,IAAI,CAACC,SAAS,CAAC,IAAI,CAACpD,MAAM,CAACC,GAAG,CAACsD,cAAc,EAAE,CAAC;UAC7DC,OAAO,EAAE,IAAI,CAACxD,MAAM,CAACC,GAAG,CAACwD,aAAa,CAAC,SAAS,CAAC;UACjDC,SAAS,EAAE,IAAI,CAAC1D,MAAM,CAACC,GAAG,CAACwD,aAAa,CAAC,WAAW;SACrD;QACDpE,YAAY,EAAE,IAAI,CAACA;OACpB;IACH;IAEA,IAAI,CAACD,oBAAoB,GAAG,IAAI;EAClC;EAEA/B,YAAYA,CAACZ,MAAkB,EAAE2J,KAAY;IAC3CA,KAAK,EAAEC,eAAe,EAAE;IAExB,IAAI,CAACxH,mBAAmB,CAAC0H,OAAO,CAAC;MAC/BC,OAAO,EAAE,wCAAwC/J,MAAM,CAACP,IAAI,WAAW;MACvEuK,MAAM,EAAE,qBAAqB;MAC7B5I,IAAI,EAAE,4CAA4C;MAClDS,MAAM,EAAEA,CAAA,KAAK;QACXtD,cAAc,CAAC,IAAI,CAAC2D,iBAAiB,CAACtB,YAAY,CAACZ,MAAM,CAACC,EAAE,CAAC,CAAC,CAC3DgK,IAAI,CAAC,MAAK;UACT,MAAMC,kBAAkB,GAAG,IAAI,CAAC/G,OAAO,CAACgH,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACnK,EAAE,KAAKD,MAAM,CAACC,EAAE,CAAC;UAC1E,IAAI,CAACkD,OAAO,CAACkH,MAAM,CAACH,kBAAkB,EAAE,CAAC,CAAC;UAC1C,IAAI,CAACtE,mBAAmB,CAAC,KAAK,CAAC;UAE/B,IAAI,IAAI,CAACpG,cAAc,EAAES,EAAE,KAAKD,MAAM,CAACC,EAAE,EACvC,IAAI,CAACkG,YAAY,CAAC,IAAI,CAAChD,OAAO,CAAC0E,IAAI,CAAC7H,MAAM,IAAIA,MAAM,CAACuB,SAAS,CAAC,CAAC;UAElE,IAAI,CAACY,mBAAmB,CAACmI,WAAW,CAAC,6BAA6B,CAAC;QACrE,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;UACbC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChD,IAAI,CAACrI,mBAAmB,CAAC2D,SAAS,CAAC,yBAAyB,CAAC;QAC/D,CAAC,CAAC;MACN;KACD,CAAC;EACJ;EAEAuB,YAAYA,CAACrH,MAAkB;IAC7B,IAAIA,MAAM,CAACC,EAAE,EAAE;MACb1B,cAAc,CAAC,IAAI,CAAC2D,iBAAiB,CAACwI,YAAY,CAAC1K,MAAM,CAACC,EAAE,EAAED,MAAM,CAAC,CAAC,CACnEiK,IAAI,CAAEU,aAAa,IAAI;QACtB,MAAMC,gBAAgB,GAAG,IAAI,CAACzH,OAAO,CAACgH,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACnK,EAAE,KAAK0K,aAAa,CAAC1K,EAAE,CAAC;QAC/E,IAAI,CAACkD,OAAO,CAACyH,gBAAgB,CAAC,GAAGD,aAAa;QAE9C;QACA,IAAIA,aAAa,CAACpJ,SAAS,EAAE;UAC3B,IAAI,CAAC4B,OAAO,CAACqB,MAAM,CAAC4F,CAAC,IAAIA,CAAC,CAAC7I,SAAS,IAAI6I,CAAC,CAACnK,EAAE,KAAK0K,aAAa,CAAC1K,EAAE,CAAC,CAC/DqF,OAAO,CAAC8E,CAAC,IAAG;YAAGA,CAAC,CAAC7I,SAAS,GAAG,KAAK;UAAE,CAAC,CAAC;QAC3C;QAEA,IAAI,CAACqE,mBAAmB,CAAC,KAAK,CAAC;QAC/B,IAAI,CAACzD,mBAAmB,CAACmI,WAAW,CAAC,2BAA2B,CAAC;MACnE,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;QACb,IAAI,CAACrI,mBAAmB,CAAC2D,SAAS,CAAC,8BAA8B,CAAC;MACpE,CAAC,CAAC;MACJ;IACF,CAAC,MAECvH,cAAc,CAAC,IAAI,CAAC2D,iBAAiB,CAAC2I,UAAU,CAAC7K,MAAM,CAAC,CAAC,CACtDiK,IAAI,CAAEa,WAAW,IAAI;MACpB;MACA,IAAIA,WAAW,CAACvJ,SAAS,EAAE;QACzB,IAAI,CAAC4B,OAAO,CAACmC,OAAO,CAAC8E,CAAC,IAAIA,CAAC,CAAC7I,SAAS,GAAG,KAAK,CAAC;MAChD;MAEA,IAAI,CAAC4B,OAAO,CAAC4H,IAAI,CAACD,WAAW,CAAC;MAC9B,IAAI,CAAC3E,YAAY,CAAC2E,WAAW,CAAC;MAE9B,IAAI,CAAClF,mBAAmB,CAAC,KAAK,CAAC;MAC/B,IAAI,CAACzD,mBAAmB,CAACmI,WAAW,CAAC,2BAA2B,CAAC;IACnE,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;MACb,IAAI,CAACrI,mBAAmB,CAAC2D,SAAS,CAAC,4BAA4B,CAAC;IAClE,CAAC,CAAC;EACR;EAEA;;;;;EAKQoB,oBAAoBA,CAAClD,MAAyB;IACpD,IAAI,CAACA,MAAM,EAAEM,MAAM,EAAE,OAAO,EAAE;IAE9B,OAAON,MAAM,CAACQ,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,SAAS,KAAK3F,cAAc,CAACmJ,WAAW,CAAC,CAAC/C,GAAG,CAACV,KAAK,KAAK;MAC1FxE,EAAE,EAAEwE,KAAK,CAACxE,EAAE;MACZ+K,aAAa,EAAEvG,KAAK,CAACuG,aAAa;MAClCtG,SAAS,EAAED,KAAK,CAACC,SAAS;MAC1BuG,cAAc,EAAExG,KAAK,CAACwG,cAAc;MACpCC,SAAS,EAAEzG,KAAK,CAACyG,SAAS;MAC1BC,YAAY,EAAE1G,KAAK,CAAC0G,YAAY;MAChCC,UAAU,EAAE3G,KAAK,CAAC2G,UAAU;MAC5BC,eAAe,EAAE5G,KAAK,CAAC4G,eAAe;MACtCC,WAAW,EAAE7G,KAAK,CAAC6G,WAAW;MAC9BC,MAAM,EAAE9G,KAAK,CAAC8G,MAAM;MACpBC,UAAU,EAAE/G,KAAK,CAAC+G,UAAU;MAC5BC,cAAc,EAAEhH,KAAK,CAACgH,cAAc;MACpCC,SAAS,EAAEjH,KAAK,CAACiH;KAClB,CAAC,CAAC;EACL;;;uBAtaW1J,0BAA0B,EAAA/C,EAAA,CAAA0M,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAA5M,EAAA,CAAA0M,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAA9M,EAAA,CAAA0M,iBAAA,CAAAK,EAAA,CAAAnN,mBAAA,GAAAI,EAAA,CAAA0M,iBAAA,CAAAM,EAAA,CAAAC,aAAA,GAAAjN,EAAA,CAAA0M,iBAAA,CAAAQ,EAAA,CAAAC,sBAAA,GAAAnN,EAAA,CAAA0M,iBAAA,CAAAU,EAAA,CAAAC,oBAAA;IAAA;EAAA;;;YAA1BtK,0BAA0B;MAAAuK,SAAA;MAAAC,MAAA;QAAAzI,UAAA;QAAAR,MAAA;QAAA1C,QAAA;QAAAsC,OAAA;MAAA;MAAAsJ,OAAA;QAAAjK,oBAAA;MAAA;MAAAkK,UAAA;MAAAC,QAAA,GAAA1N,EAAA,CAAA2N,kBAAA,CAF1B,CAAC/N,mBAAmB,CAAC,GAAAI,EAAA,CAAA4N,oBAAA,EAAA5N,EAAA,CAAA6N,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCpClCnO,EAAA,CAAAC,cAAA,kBAC6C;UADlBD,EAAA,CAAAiB,UAAA,qBAAAoN,gEAAAlN,MAAA;YAAAnB,EAAA,CAAAoB,aAAA,CAAAkN,GAAA;YAAA,MAAAC,cAAA,GAAAvO,EAAA,CAAA2C,WAAA;YAAA,OAAA3C,EAAA,CAAAwB,WAAA,CAAW+M,cAAA,CAAAC,MAAA,CAAArN,MAAA,CAA0B;UAAA,EAAC;UAE/DnB,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAS,SAAA,WAAoD;UAAAT,EAAA,CAAAG,YAAA,EAAO;UACjEH,EAAA,CAAA6B,UAAA,IAAA4M,iDAAA,kBAAsB;UAGtBzO,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAS,SAAA,WAA2D;UACnET,EADmE,CAAAG,YAAA,EAAO,EAC/D;UAEXH,EAAA,CAAAC,cAAA,mBAA6G;UAC3GD,EAAA,CAAA6B,UAAA,IAAA6M,iDAAA,yBAAuC;UA8BzC1O,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,qCAEsC;UAFTD,EAAA,CAAA2O,gBAAA,2BAAAC,yFAAAzN,MAAA;YAAAnB,EAAA,CAAAoB,aAAA,CAAAkN,GAAA;YAAAtO,EAAA,CAAA6O,kBAAA,CAAAT,GAAA,CAAA1K,oBAAA,EAAAvC,MAAA,MAAAiN,GAAA,CAAA1K,oBAAA,GAAAvC,MAAA;YAAA,OAAAnB,EAAA,CAAAwB,WAAA,CAAAL,MAAA;UAAA,EAAkC;UAE7DnB,EAAA,CAAAiB,UAAA,wBAAA6N,sFAAA3N,MAAA;YAAAnB,EAAA,CAAAoB,aAAA,CAAAkN,GAAA;YAAA,OAAAtO,EAAA,CAAAwB,WAAA,CAAc4M,GAAA,CAAAhG,YAAA,CAAAjH,MAAA,CAAoB;UAAA,EAAC;UACrCnB,EAAA,CAAAG,YAAA,EAA8B;UAE9BH,EAAA,CAAAC,cAAA,gCAAqB;UACnBD,EAAA,CAAA6B,UAAA,KAAAkN,kDAAA,yBAAgC;UAIlC/O,EAAA,CAAAG,YAAA,EAAkB;;;UApDgDH,EAAxD,CAAAU,UAAA,iBAAgB,aAAA0N,GAAA,CAAAxM,QAAA,CAA6D;UAGrF5B,EAAA,CAAAI,SAAA,GAEC;UAFDJ,EAAA,CAAAkC,aAAA,IAAAkM,GAAA,CAAA7N,cAAA,UAEC;UAIkBP,EAAA,CAAAI,SAAA,GAAc;UAACJ,EAAf,CAAAU,UAAA,eAAc,UAAA0N,GAAA,CAAA5K,eAAA,CAA0B;UAiChCxD,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAAgP,gBAAA,YAAAZ,GAAA,CAAA1K,oBAAA,CAAkC;UAC2B1D,EAD1B,CAAAU,UAAA,aAAA0N,GAAA,CAAA3K,cAAA,CAA2B,WAAA2K,GAAA,CAAAxD,YAAA,CAAwB,sBAAAwD,GAAA,CAAAvK,iBAAA,CAC1E,0BAAAuK,GAAA,CAAAxK,qBAAA,CAAgD,YAAAwK,GAAA,CAAAlK,OAAA,CAAoB;;;qBDfzG3E,UAAU,EAAA0P,EAAA,CAAAC,IAAA,EAAAC,EAAA,CAAAC,OAAA,EACV5P,YAAY,EAAA6P,EAAA,CAAAC,MAAA,EAAAvC,EAAA,CAAAwC,aAAA,EACZ9P,aAAa,EACbM,SAAS,EAAAyP,GAAA,CAAAC,GAAA,EACT/P,OAAO,EACPC,6BAA6B,EAC7BE,mBAAmB,EAAA6P,GAAA,CAAAC,aAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}