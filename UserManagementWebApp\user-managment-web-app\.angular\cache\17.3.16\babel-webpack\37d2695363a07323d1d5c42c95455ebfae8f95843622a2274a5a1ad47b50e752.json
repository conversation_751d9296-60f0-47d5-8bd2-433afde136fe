{"ast": null, "code": "import { deDict as deDict$1 } from './authenticator/de.mjs';\nimport { enDict as enDict$1 } from './authenticator/en.mjs';\nimport { esDict as esDict$1 } from './authenticator/es.mjs';\nimport { frDict as frDict$1 } from './authenticator/fr.mjs';\nimport { itDict as itDict$1 } from './authenticator/it.mjs';\nimport { jaDict as jaDict$1 } from './authenticator/ja.mjs';\nimport { krDict as krDict$1 } from './authenticator/kr.mjs';\nimport { nbDict as nbDict$1 } from './authenticator/nb.mjs';\nimport { nlDict as nlDict$1 } from './authenticator/nl.mjs';\nimport { plDict as plDict$1 } from './authenticator/pl.mjs';\nimport { ptDict as ptDict$1 } from './authenticator/pt.mjs';\nimport { zhDict as zhDict$1 } from './authenticator/zh.mjs';\nimport { svDict as svDict$1 } from './authenticator/sv.mjs';\nimport { idDict as idDict$1 } from './authenticator/id.mjs';\nimport { trDict as trDict$1 } from './authenticator/tr.mjs';\nimport { ruDict as ruDict$1 } from './authenticator/ru.mjs';\nimport { heDict as heDict$1 } from './authenticator/he.mjs';\nimport { uaDict as uaDict$1 } from './authenticator/ua.mjs';\nimport { thDict as thDict$1 } from './authenticator/th.mjs';\nimport { defaultTexts as defaultTexts$1 } from './authenticator/defaultTexts.mjs';\n\n//merge all the new module translations in respective locale constants\nconst deDict = {\n  ...deDict$1\n};\nconst enDict = {\n  ...enDict$1\n};\nconst esDict = {\n  ...esDict$1\n};\nconst frDict = {\n  ...frDict$1\n};\nconst itDict = {\n  ...itDict$1\n};\nconst jaDict = {\n  ...jaDict$1\n};\nconst krDict = {\n  ...krDict$1\n};\nconst nbDict = {\n  ...nbDict$1\n};\nconst nlDict = {\n  ...nlDict$1\n};\nconst plDict = {\n  ...plDict$1\n};\nconst ptDict = {\n  ...ptDict$1\n};\nconst zhDict = {\n  ...zhDict$1\n};\nconst svDict = {\n  ...svDict$1\n};\nconst idDict = {\n  ...idDict$1\n};\nconst trDict = {\n  ...trDict$1\n};\nconst ruDict = {\n  ...ruDict$1\n};\nconst heDict = {\n  ...heDict$1\n};\nconst uaDict = {\n  ...uaDict$1\n};\nconst thDict = {\n  ...thDict$1\n};\nconst defaultTexts = {\n  ...defaultTexts$1\n  // new module related default texts goes here\n};\nexport { deDict, defaultTexts, enDict, esDict, frDict, heDict, idDict, itDict, jaDict, krDict, nbDict, nlDict, plDict, ptDict, ruDict, svDict, thDict, trDict, uaDict, zhDict };", "map": {"version": 3, "names": ["deDict", "deDict$1", "enDict", "enDict$1", "esDict", "esDict$1", "frDict", "frDict$1", "itDict", "itDict$1", "jaDict", "jaDict$1", "krDict", "krDict$1", "nbDict", "nbDict$1", "nlDict", "nlDict$1", "plDict", "plDict$1", "ptDict", "ptDict$1", "zhDict", "zhDict$1", "svDict", "svDict$1", "idDict", "idDict$1", "trDict", "trDict$1", "ruDict", "ruDict$1", "heDict", "heDict$1", "uaDict", "uaDict$1", "thDict", "thDict$1", "defaultTexts", "defaultTexts$1"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/i18n/dictionaries/index.mjs"], "sourcesContent": ["import { deDict as deDict$1 } from './authenticator/de.mjs';\nimport { enDict as enDict$1 } from './authenticator/en.mjs';\nimport { esDict as esDict$1 } from './authenticator/es.mjs';\nimport { frDict as frDict$1 } from './authenticator/fr.mjs';\nimport { itDict as itDict$1 } from './authenticator/it.mjs';\nimport { jaDict as jaDict$1 } from './authenticator/ja.mjs';\nimport { krDict as krDict$1 } from './authenticator/kr.mjs';\nimport { nbDict as nbDict$1 } from './authenticator/nb.mjs';\nimport { nlDict as nlDict$1 } from './authenticator/nl.mjs';\nimport { plDict as plDict$1 } from './authenticator/pl.mjs';\nimport { ptDict as ptDict$1 } from './authenticator/pt.mjs';\nimport { zhDict as zhDict$1 } from './authenticator/zh.mjs';\nimport { svDict as svDict$1 } from './authenticator/sv.mjs';\nimport { idDict as idDict$1 } from './authenticator/id.mjs';\nimport { trDict as trDict$1 } from './authenticator/tr.mjs';\nimport { ruDict as ruDict$1 } from './authenticator/ru.mjs';\nimport { heDict as heDict$1 } from './authenticator/he.mjs';\nimport { uaDict as uaDict$1 } from './authenticator/ua.mjs';\nimport { thDict as thDict$1 } from './authenticator/th.mjs';\nimport { defaultTexts as defaultTexts$1 } from './authenticator/defaultTexts.mjs';\n\n//merge all the new module translations in respective locale constants\nconst deDict = { ...deDict$1 };\nconst enDict = {\n    ...enDict$1,\n};\nconst esDict = { ...esDict$1 };\nconst frDict = { ...frDict$1 };\nconst itDict = { ...itDict$1 };\nconst jaDict = { ...jaDict$1 };\nconst krDict = { ...krDict$1 };\nconst nbDict = { ...nbDict$1 };\nconst nlDict = { ...nlDict$1 };\nconst plDict = { ...plDict$1 };\nconst ptDict = { ...ptDict$1 };\nconst zhDict = { ...zhDict$1 };\nconst svDict = { ...svDict$1 };\nconst idDict = { ...idDict$1 };\nconst trDict = { ...trDict$1 };\nconst ruDict = { ...ruDict$1 };\nconst heDict = { ...heDict$1 };\nconst uaDict = { ...uaDict$1 };\nconst thDict = { ...thDict$1 };\nconst defaultTexts = {\n    ...defaultTexts$1,\n    // new module related default texts goes here\n};\n\nexport { deDict, defaultTexts, enDict, esDict, frDict, heDict, idDict, itDict, jaDict, krDict, nbDict, nlDict, plDict, ptDict, ruDict, svDict, thDict, trDict, uaDict, zhDict };\n"], "mappings": "AAAA,SAASA,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,MAAM,IAAIC,QAAQ,QAAQ,wBAAwB;AAC3D,SAASC,YAAY,IAAIC,cAAc,QAAQ,kCAAkC;;AAEjF;AACA,MAAMvC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EACX,GAAGC;AACP,CAAC;AACD,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,MAAM,GAAG;EAAE,GAAGC;AAAS,CAAC;AAC9B,MAAMC,YAAY,GAAG;EACjB,GAAGC;EACH;AACJ,CAAC;AAED,SAASvC,MAAM,EAAEsC,YAAY,EAAEpC,MAAM,EAAEE,MAAM,EAAEE,MAAM,EAAE0B,MAAM,EAAEN,MAAM,EAAElB,MAAM,EAAEE,MAAM,EAAEE,MAAM,EAAEE,MAAM,EAAEE,MAAM,EAAEE,MAAM,EAAEE,MAAM,EAAEU,MAAM,EAAEN,MAAM,EAAEY,MAAM,EAAER,MAAM,EAAEM,MAAM,EAAEZ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}