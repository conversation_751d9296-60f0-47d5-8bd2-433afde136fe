{"ast": null, "code": "import { ConsoleLogger } from '../Logger/ConsoleLogger.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('I18n');\n/**\n * Language translation utility.\n */\nlet I18n$1 = class I18n {\n  constructor() {\n    /**\n     * @private\n     */\n    this._options = null;\n    /**\n     * @private\n     */\n    this._lang = null;\n    /**\n     * @private\n     */\n    this._dict = {};\n  }\n  /**\n   * Sets the default language from the configuration when required.\n   */\n  setDefaultLanguage() {\n    // Default to window language if not set in instance\n    if (!this._lang && typeof window !== 'undefined' && window && window.navigator) {\n      this._lang = window.navigator.language;\n    }\n    logger.debug(this._lang);\n  }\n  /**\n   * @method\n   * Explicitly setting language\n   * @param {String} lang\n   */\n  setLanguage(lang) {\n    this._lang = lang;\n  }\n  /**\n   * @method\n   * Get value\n   * @param {String} key\n   * @param {String} defVal - Default value\n   */\n  get(key, defVal = undefined) {\n    this.setDefaultLanguage();\n    if (!this._lang) {\n      return typeof defVal !== 'undefined' ? defVal : key;\n    }\n    const lang = this._lang;\n    let val = this.getByLanguage(key, lang);\n    if (val) {\n      return val;\n    }\n    if (lang.indexOf('-') > 0) {\n      val = this.getByLanguage(key, lang.split('-')[0]);\n    }\n    if (val) {\n      return val;\n    }\n    return typeof defVal !== 'undefined' ? defVal : key;\n  }\n  /**\n   * @method\n   * Get value according to specified language\n   * @param {String} key\n   * @param {String} language - Specified langurage to be used\n   * @param {String} defVal - Default value\n   */\n  getByLanguage(key, language, defVal = null) {\n    if (!language) {\n      return defVal;\n    }\n    const langDict = this._dict[language];\n    if (!langDict) {\n      return defVal;\n    }\n    return langDict[key];\n  }\n  /**\n   * @method\n   * Add vocabularies for one language\n   * @param {String} language - Language of the dictionary\n   * @param {Object} vocabularies - Object that has key-value as dictionary entry\n   */\n  putVocabulariesForLanguage(language, vocabularies) {\n    let langDict = this._dict[language];\n    if (!langDict) {\n      langDict = this._dict[language] = {};\n    }\n    this._dict[language] = {\n      ...langDict,\n      ...vocabularies\n    };\n  }\n  /**\n   * @method\n   * Add vocabularies for one language\n   * @param {Object} vocabularies - Object that has language as key,\n   *                                vocabularies of each language as value\n   */\n  putVocabularies(vocabularies) {\n    Object.keys(vocabularies).forEach(key => {\n      this.putVocabulariesForLanguage(key, vocabularies[key]);\n    });\n  }\n};\nexport { I18n$1 as I18n };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "logger", "I18n$1", "I18n", "constructor", "_options", "_lang", "_dict", "setDefaultLanguage", "window", "navigator", "language", "debug", "setLanguage", "lang", "get", "key", "defVal", "undefined", "val", "getByLanguage", "indexOf", "split", "langDict", "putVocabulariesForLanguage", "vocabularies", "putVocabularies", "Object", "keys", "for<PERSON>ach"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/I18n/I18n.mjs"], "sourcesContent": ["import { ConsoleLogger } from '../Logger/ConsoleLogger.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('I18n');\n/**\n * Language translation utility.\n */\nlet I18n$1 = class I18n {\n    constructor() {\n        /**\n         * @private\n         */\n        this._options = null;\n        /**\n         * @private\n         */\n        this._lang = null;\n        /**\n         * @private\n         */\n        this._dict = {};\n    }\n    /**\n     * Sets the default language from the configuration when required.\n     */\n    setDefaultLanguage() {\n        // Default to window language if not set in instance\n        if (!this._lang &&\n            typeof window !== 'undefined' &&\n            window &&\n            window.navigator) {\n            this._lang = window.navigator.language;\n        }\n        logger.debug(this._lang);\n    }\n    /**\n     * @method\n     * Explicitly setting language\n     * @param {String} lang\n     */\n    setLanguage(lang) {\n        this._lang = lang;\n    }\n    /**\n     * @method\n     * Get value\n     * @param {String} key\n     * @param {String} defVal - Default value\n     */\n    get(key, defVal = undefined) {\n        this.setDefaultLanguage();\n        if (!this._lang) {\n            return typeof defVal !== 'undefined' ? defVal : key;\n        }\n        const lang = this._lang;\n        let val = this.getByLanguage(key, lang);\n        if (val) {\n            return val;\n        }\n        if (lang.indexOf('-') > 0) {\n            val = this.getByLanguage(key, lang.split('-')[0]);\n        }\n        if (val) {\n            return val;\n        }\n        return typeof defVal !== 'undefined' ? defVal : key;\n    }\n    /**\n     * @method\n     * Get value according to specified language\n     * @param {String} key\n     * @param {String} language - Specified langurage to be used\n     * @param {String} defVal - Default value\n     */\n    getByLanguage(key, language, defVal = null) {\n        if (!language) {\n            return defVal;\n        }\n        const langDict = this._dict[language];\n        if (!langDict) {\n            return defVal;\n        }\n        return langDict[key];\n    }\n    /**\n     * @method\n     * Add vocabularies for one language\n     * @param {String} language - Language of the dictionary\n     * @param {Object} vocabularies - Object that has key-value as dictionary entry\n     */\n    putVocabulariesForLanguage(language, vocabularies) {\n        let langDict = this._dict[language];\n        if (!langDict) {\n            langDict = this._dict[language] = {};\n        }\n        this._dict[language] = { ...langDict, ...vocabularies };\n    }\n    /**\n     * @method\n     * Add vocabularies for one language\n     * @param {Object} vocabularies - Object that has language as key,\n     *                                vocabularies of each language as value\n     */\n    putVocabularies(vocabularies) {\n        Object.keys(vocabularies).forEach(key => {\n            this.putVocabulariesForLanguage(key, vocabularies[key]);\n        });\n    }\n};\n\nexport { I18n$1 as I18n };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,6BAA6B;;AAE3D;AACA;AACA,MAAMC,MAAM,GAAG,IAAID,aAAa,CAAC,MAAM,CAAC;AACxC;AACA;AACA;AACA,IAAIE,MAAM,GAAG,MAAMC,IAAI,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACV;AACR;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,IAAI;IACjB;AACR;AACA;IACQ,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACnB;EACA;AACJ;AACA;EACIC,kBAAkBA,CAAA,EAAG;IACjB;IACA,IAAI,CAAC,IAAI,CAACF,KAAK,IACX,OAAOG,MAAM,KAAK,WAAW,IAC7BA,MAAM,IACNA,MAAM,CAACC,SAAS,EAAE;MAClB,IAAI,CAACJ,KAAK,GAAGG,MAAM,CAACC,SAAS,CAACC,QAAQ;IAC1C;IACAV,MAAM,CAACW,KAAK,CAAC,IAAI,CAACN,KAAK,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACIO,WAAWA,CAACC,IAAI,EAAE;IACd,IAAI,CAACR,KAAK,GAAGQ,IAAI;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,GAAGA,CAACC,GAAG,EAAEC,MAAM,GAAGC,SAAS,EAAE;IACzB,IAAI,CAACV,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAAC,IAAI,CAACF,KAAK,EAAE;MACb,OAAO,OAAOW,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAGD,GAAG;IACvD;IACA,MAAMF,IAAI,GAAG,IAAI,CAACR,KAAK;IACvB,IAAIa,GAAG,GAAG,IAAI,CAACC,aAAa,CAACJ,GAAG,EAAEF,IAAI,CAAC;IACvC,IAAIK,GAAG,EAAE;MACL,OAAOA,GAAG;IACd;IACA,IAAIL,IAAI,CAACO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;MACvBF,GAAG,GAAG,IAAI,CAACC,aAAa,CAACJ,GAAG,EAAEF,IAAI,CAACQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD;IACA,IAAIH,GAAG,EAAE;MACL,OAAOA,GAAG;IACd;IACA,OAAO,OAAOF,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAGD,GAAG;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,aAAaA,CAACJ,GAAG,EAAEL,QAAQ,EAAEM,MAAM,GAAG,IAAI,EAAE;IACxC,IAAI,CAACN,QAAQ,EAAE;MACX,OAAOM,MAAM;IACjB;IACA,MAAMM,QAAQ,GAAG,IAAI,CAAChB,KAAK,CAACI,QAAQ,CAAC;IACrC,IAAI,CAACY,QAAQ,EAAE;MACX,OAAON,MAAM;IACjB;IACA,OAAOM,QAAQ,CAACP,GAAG,CAAC;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIQ,0BAA0BA,CAACb,QAAQ,EAAEc,YAAY,EAAE;IAC/C,IAAIF,QAAQ,GAAG,IAAI,CAAChB,KAAK,CAACI,QAAQ,CAAC;IACnC,IAAI,CAACY,QAAQ,EAAE;MACXA,QAAQ,GAAG,IAAI,CAAChB,KAAK,CAACI,QAAQ,CAAC,GAAG,CAAC,CAAC;IACxC;IACA,IAAI,CAACJ,KAAK,CAACI,QAAQ,CAAC,GAAG;MAAE,GAAGY,QAAQ;MAAE,GAAGE;IAAa,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACD,YAAY,EAAE;IAC1BE,MAAM,CAACC,IAAI,CAACH,YAAY,CAAC,CAACI,OAAO,CAACb,GAAG,IAAI;MACrC,IAAI,CAACQ,0BAA0B,CAACR,GAAG,EAAES,YAAY,CAACT,GAAG,CAAC,CAAC;IAC3D,CAAC,CAAC;EACN;AACJ,CAAC;AAED,SAASd,MAAM,IAAIC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}