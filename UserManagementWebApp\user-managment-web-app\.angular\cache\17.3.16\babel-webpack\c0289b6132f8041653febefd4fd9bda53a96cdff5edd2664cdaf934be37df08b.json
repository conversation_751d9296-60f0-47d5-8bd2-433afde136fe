{"ast": null, "code": "import { generateRandomString } from '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst generateState = () => {\n  return generateRandomString(32);\n};\nexport { generateState };", "map": {"version": 3, "names": ["generateRandomString", "generateState"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/oauth/generateState.mjs"], "sourcesContent": ["import { generateRandomString } from '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst generateState = () => {\n    return generateRandomString(32);\n};\n\nexport { generateState };\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,mCAAmC;;AAExE;AACA;AACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;EACxB,OAAOD,oBAAoB,CAAC,EAAE,CAAC;AACnC,CAAC;AAED,SAASC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}