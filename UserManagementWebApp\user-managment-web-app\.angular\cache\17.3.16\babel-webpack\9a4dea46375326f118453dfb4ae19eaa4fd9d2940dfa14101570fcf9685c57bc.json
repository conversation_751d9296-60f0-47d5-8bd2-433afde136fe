{"ast": null, "code": "import { getAmplifyUserAgent, Category } from '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst getAuthUserAgentValue = (action, customUserAgentDetails) => getAmplifyUserAgent({\n  category: Category.Auth,\n  action,\n  ...customUserAgentDetails\n});\nexport { getAuthUserAgentValue };", "map": {"version": 3, "names": ["getAmplifyUserAgent", "Category", "getAuthUserAgentValue", "action", "customUserAgentDetails", "category", "<PERSON><PERSON>"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/utils/getAuthUserAgentValue.mjs"], "sourcesContent": ["import { getAmplifyUserAgent, Category } from '@aws-amplify/core/internals/utils';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst getAuthUserAgentValue = (action, customUserAgentDetails) => getAmplifyUserAgent({\n    category: Category.Auth,\n    action,\n    ...customUserAgentDetails,\n});\n\nexport { getAuthUserAgentValue };\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,QAAQ,QAAQ,mCAAmC;;AAEjF;AACA;AACA,MAAMC,qBAAqB,GAAGA,CAACC,MAAM,EAAEC,sBAAsB,KAAKJ,mBAAmB,CAAC;EAClFK,QAAQ,EAAEJ,QAAQ,CAACK,IAAI;EACvBH,MAAM;EACN,GAAGC;AACP,CAAC,CAAC;AAEF,SAASF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}