{"ast": null, "code": "import { tokens } from './tokens/index.mjs';\nconst darkModeTokens = {\n  colors: {\n    red: {\n      10: tokens.colors.red[100],\n      20: tokens.colors.red[90],\n      40: tokens.colors.red[80],\n      // 60 doesn't change\n      80: tokens.colors.red[40],\n      90: tokens.colors.red[20],\n      100: tokens.colors.red[10]\n    },\n    orange: {\n      10: tokens.colors.orange[100],\n      20: tokens.colors.orange[90],\n      40: tokens.colors.orange[80],\n      // 60 doesn't change\n      80: tokens.colors.orange[40],\n      90: tokens.colors.orange[20],\n      100: tokens.colors.orange[10]\n    },\n    yellow: {\n      10: tokens.colors.yellow[100],\n      20: tokens.colors.yellow[90],\n      40: tokens.colors.yellow[80],\n      // 60 doesn't change\n      80: tokens.colors.yellow[40],\n      90: tokens.colors.yellow[20],\n      100: tokens.colors.yellow[10]\n    },\n    green: {\n      10: tokens.colors.green[100],\n      20: tokens.colors.green[90],\n      40: tokens.colors.green[80],\n      // 60 doesn't change\n      80: tokens.colors.green[40],\n      90: tokens.colors.green[20],\n      100: tokens.colors.green[10]\n    },\n    teal: {\n      10: tokens.colors.teal[100],\n      20: tokens.colors.teal[90],\n      40: tokens.colors.teal[80],\n      // 60 doesn't change\n      80: tokens.colors.teal[40],\n      90: tokens.colors.teal[20],\n      100: tokens.colors.teal[10]\n    },\n    blue: {\n      10: tokens.colors.blue[100],\n      20: tokens.colors.blue[90],\n      40: tokens.colors.blue[80],\n      // 60 doesn't change\n      80: tokens.colors.blue[40],\n      90: tokens.colors.blue[20],\n      100: tokens.colors.blue[10]\n    },\n    purple: {\n      10: tokens.colors.purple[100],\n      20: tokens.colors.purple[90],\n      40: tokens.colors.purple[80],\n      // 60 doesn't change\n      80: tokens.colors.purple[40],\n      90: tokens.colors.purple[20],\n      100: tokens.colors.purple[10]\n    },\n    pink: {\n      10: tokens.colors.pink[100],\n      20: tokens.colors.pink[90],\n      40: tokens.colors.pink[80],\n      // 60 doesn't change\n      80: tokens.colors.pink[40],\n      90: tokens.colors.pink[20],\n      100: tokens.colors.pink[10]\n    },\n    neutral: {\n      10: tokens.colors.neutral[100],\n      20: tokens.colors.neutral[90],\n      40: tokens.colors.neutral[80],\n      // 60 doesn't change\n      80: tokens.colors.neutral[40],\n      90: tokens.colors.neutral[20],\n      100: tokens.colors.neutral[10]\n    },\n    font: {\n      primary: '{colors.white}',\n      secondary: '{colors.neutral.100}',\n      tertiary: '{colors.neutral.90}',\n      inverse: '{colors.neutral.10}'\n    },\n    background: {\n      primary: '{colors.neutral.10}',\n      secondary: '{colors.neutral.20}',\n      tertiary: '{colors.neutral.40}'\n    },\n    border: {\n      primary: '{colors.neutral.60}',\n      secondary: '{colors.neutral.40}',\n      tertiary: '{colors.neutral.20}'\n    },\n    shadow: {\n      primary: {\n        value: 'hsla(100, 100%, 100%, 0.25)'\n      },\n      secondary: {\n        value: 'hsla(100, 100%, 100%, 0.15)'\n      },\n      tertiary: {\n        value: 'hsla(100, 100%, 100%, 0.05)'\n      }\n    },\n    overlay: {\n      5: 'hsla(0, 0%, 100%, 0.05)',\n      10: 'hsla(0, 0%, 100%, 0.1)',\n      20: 'hsla(0, 0%, 100%, 0.2)',\n      30: 'hsla(0, 0%, 100%, 0.3)',\n      40: 'hsla(0, 0%, 100%, 0.4)',\n      50: 'hsla(0, 0%, 100%, 0.5)',\n      60: 'hsla(0, 0%, 100%, 0.6)',\n      70: 'hsla(0, 0%, 100%, 0.7)',\n      80: 'hsla(0, 0%, 100%, 0.8)',\n      90: 'hsla(0, 0%, 100%, 0.9)'\n    }\n  }\n};\n/**\n * A basic dark mode that just flips the base color\n * palette.\n */\nconst defaultDarkModeOverride = {\n  colorMode: 'dark',\n  tokens: darkModeTokens\n};\nconst reactNativeDarkTokens = {\n  ...darkModeTokens\n};\nexport { defaultDarkModeOverride, reactNativeDarkTokens };", "map": {"version": 3, "names": ["tokens", "darkModeTokens", "colors", "red", "orange", "yellow", "green", "teal", "blue", "purple", "pink", "neutral", "font", "primary", "secondary", "tertiary", "inverse", "background", "border", "shadow", "value", "overlay", "defaultDarkModeOverride", "colorMode", "reactNativeDarkTokens"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/defaultDarkModeOverride.mjs"], "sourcesContent": ["import { tokens } from './tokens/index.mjs';\n\nconst darkModeTokens = {\n    colors: {\n        red: {\n            10: tokens.colors.red[100],\n            20: tokens.colors.red[90],\n            40: tokens.colors.red[80],\n            // 60 doesn't change\n            80: tokens.colors.red[40],\n            90: tokens.colors.red[20],\n            100: tokens.colors.red[10],\n        },\n        orange: {\n            10: tokens.colors.orange[100],\n            20: tokens.colors.orange[90],\n            40: tokens.colors.orange[80],\n            // 60 doesn't change\n            80: tokens.colors.orange[40],\n            90: tokens.colors.orange[20],\n            100: tokens.colors.orange[10],\n        },\n        yellow: {\n            10: tokens.colors.yellow[100],\n            20: tokens.colors.yellow[90],\n            40: tokens.colors.yellow[80],\n            // 60 doesn't change\n            80: tokens.colors.yellow[40],\n            90: tokens.colors.yellow[20],\n            100: tokens.colors.yellow[10],\n        },\n        green: {\n            10: tokens.colors.green[100],\n            20: tokens.colors.green[90],\n            40: tokens.colors.green[80],\n            // 60 doesn't change\n            80: tokens.colors.green[40],\n            90: tokens.colors.green[20],\n            100: tokens.colors.green[10],\n        },\n        teal: {\n            10: tokens.colors.teal[100],\n            20: tokens.colors.teal[90],\n            40: tokens.colors.teal[80],\n            // 60 doesn't change\n            80: tokens.colors.teal[40],\n            90: tokens.colors.teal[20],\n            100: tokens.colors.teal[10],\n        },\n        blue: {\n            10: tokens.colors.blue[100],\n            20: tokens.colors.blue[90],\n            40: tokens.colors.blue[80],\n            // 60 doesn't change\n            80: tokens.colors.blue[40],\n            90: tokens.colors.blue[20],\n            100: tokens.colors.blue[10],\n        },\n        purple: {\n            10: tokens.colors.purple[100],\n            20: tokens.colors.purple[90],\n            40: tokens.colors.purple[80],\n            // 60 doesn't change\n            80: tokens.colors.purple[40],\n            90: tokens.colors.purple[20],\n            100: tokens.colors.purple[10],\n        },\n        pink: {\n            10: tokens.colors.pink[100],\n            20: tokens.colors.pink[90],\n            40: tokens.colors.pink[80],\n            // 60 doesn't change\n            80: tokens.colors.pink[40],\n            90: tokens.colors.pink[20],\n            100: tokens.colors.pink[10],\n        },\n        neutral: {\n            10: tokens.colors.neutral[100],\n            20: tokens.colors.neutral[90],\n            40: tokens.colors.neutral[80],\n            // 60 doesn't change\n            80: tokens.colors.neutral[40],\n            90: tokens.colors.neutral[20],\n            100: tokens.colors.neutral[10],\n        },\n        font: {\n            primary: '{colors.white}',\n            secondary: '{colors.neutral.100}',\n            tertiary: '{colors.neutral.90}',\n            inverse: '{colors.neutral.10}',\n        },\n        background: {\n            primary: '{colors.neutral.10}',\n            secondary: '{colors.neutral.20}',\n            tertiary: '{colors.neutral.40}',\n        },\n        border: {\n            primary: '{colors.neutral.60}',\n            secondary: '{colors.neutral.40}',\n            tertiary: '{colors.neutral.20}',\n        },\n        shadow: {\n            primary: { value: 'hsla(100, 100%, 100%, 0.25)' },\n            secondary: { value: 'hsla(100, 100%, 100%, 0.15)' },\n            tertiary: { value: 'hsla(100, 100%, 100%, 0.05)' },\n        },\n        overlay: {\n            5: 'hsla(0, 0%, 100%, 0.05)',\n            10: 'hsla(0, 0%, 100%, 0.1)',\n            20: 'hsla(0, 0%, 100%, 0.2)',\n            30: 'hsla(0, 0%, 100%, 0.3)',\n            40: 'hsla(0, 0%, 100%, 0.4)',\n            50: 'hsla(0, 0%, 100%, 0.5)',\n            60: 'hsla(0, 0%, 100%, 0.6)',\n            70: 'hsla(0, 0%, 100%, 0.7)',\n            80: 'hsla(0, 0%, 100%, 0.8)',\n            90: 'hsla(0, 0%, 100%, 0.9)',\n        },\n    },\n};\n/**\n * A basic dark mode that just flips the base color\n * palette.\n */\nconst defaultDarkModeOverride = {\n    colorMode: 'dark',\n    tokens: darkModeTokens,\n};\nconst reactNativeDarkTokens = {\n    ...darkModeTokens,\n};\n\nexport { defaultDarkModeOverride, reactNativeDarkTokens };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,oBAAoB;AAE3C,MAAMC,cAAc,GAAG;EACnBC,MAAM,EAAE;IACJC,GAAG,EAAE;MACD,EAAE,EAAEH,MAAM,CAACE,MAAM,CAACC,GAAG,CAAC,GAAG,CAAC;MAC1B,EAAE,EAAEH,MAAM,CAACE,MAAM,CAACC,GAAG,CAAC,EAAE,CAAC;MACzB,EAAE,EAAEH,MAAM,CAACE,MAAM,CAACC,GAAG,CAAC,EAAE,CAAC;MACzB;MACA,EAAE,EAAEH,MAAM,CAACE,MAAM,CAACC,GAAG,CAAC,EAAE,CAAC;MACzB,EAAE,EAAEH,MAAM,CAACE,MAAM,CAACC,GAAG,CAAC,EAAE,CAAC;MACzB,GAAG,EAAEH,MAAM,CAACE,MAAM,CAACC,GAAG,CAAC,EAAE;IAC7B,CAAC;IACDC,MAAM,EAAE;MACJ,EAAE,EAAEJ,MAAM,CAACE,MAAM,CAACE,MAAM,CAAC,GAAG,CAAC;MAC7B,EAAE,EAAEJ,MAAM,CAACE,MAAM,CAACE,MAAM,CAAC,EAAE,CAAC;MAC5B,EAAE,EAAEJ,MAAM,CAACE,MAAM,CAACE,MAAM,CAAC,EAAE,CAAC;MAC5B;MACA,EAAE,EAAEJ,MAAM,CAACE,MAAM,CAACE,MAAM,CAAC,EAAE,CAAC;MAC5B,EAAE,EAAEJ,MAAM,CAACE,MAAM,CAACE,MAAM,CAAC,EAAE,CAAC;MAC5B,GAAG,EAAEJ,MAAM,CAACE,MAAM,CAACE,MAAM,CAAC,EAAE;IAChC,CAAC;IACDC,MAAM,EAAE;MACJ,EAAE,EAAEL,MAAM,CAACE,MAAM,CAACG,MAAM,CAAC,GAAG,CAAC;MAC7B,EAAE,EAAEL,MAAM,CAACE,MAAM,CAACG,MAAM,CAAC,EAAE,CAAC;MAC5B,EAAE,EAAEL,MAAM,CAACE,MAAM,CAACG,MAAM,CAAC,EAAE,CAAC;MAC5B;MACA,EAAE,EAAEL,MAAM,CAACE,MAAM,CAACG,MAAM,CAAC,EAAE,CAAC;MAC5B,EAAE,EAAEL,MAAM,CAACE,MAAM,CAACG,MAAM,CAAC,EAAE,CAAC;MAC5B,GAAG,EAAEL,MAAM,CAACE,MAAM,CAACG,MAAM,CAAC,EAAE;IAChC,CAAC;IACDC,KAAK,EAAE;MACH,EAAE,EAAEN,MAAM,CAACE,MAAM,CAACI,KAAK,CAAC,GAAG,CAAC;MAC5B,EAAE,EAAEN,MAAM,CAACE,MAAM,CAACI,KAAK,CAAC,EAAE,CAAC;MAC3B,EAAE,EAAEN,MAAM,CAACE,MAAM,CAACI,KAAK,CAAC,EAAE,CAAC;MAC3B;MACA,EAAE,EAAEN,MAAM,CAACE,MAAM,CAACI,KAAK,CAAC,EAAE,CAAC;MAC3B,EAAE,EAAEN,MAAM,CAACE,MAAM,CAACI,KAAK,CAAC,EAAE,CAAC;MAC3B,GAAG,EAAEN,MAAM,CAACE,MAAM,CAACI,KAAK,CAAC,EAAE;IAC/B,CAAC;IACDC,IAAI,EAAE;MACF,EAAE,EAAEP,MAAM,CAACE,MAAM,CAACK,IAAI,CAAC,GAAG,CAAC;MAC3B,EAAE,EAAEP,MAAM,CAACE,MAAM,CAACK,IAAI,CAAC,EAAE,CAAC;MAC1B,EAAE,EAAEP,MAAM,CAACE,MAAM,CAACK,IAAI,CAAC,EAAE,CAAC;MAC1B;MACA,EAAE,EAAEP,MAAM,CAACE,MAAM,CAACK,IAAI,CAAC,EAAE,CAAC;MAC1B,EAAE,EAAEP,MAAM,CAACE,MAAM,CAACK,IAAI,CAAC,EAAE,CAAC;MAC1B,GAAG,EAAEP,MAAM,CAACE,MAAM,CAACK,IAAI,CAAC,EAAE;IAC9B,CAAC;IACDC,IAAI,EAAE;MACF,EAAE,EAAER,MAAM,CAACE,MAAM,CAACM,IAAI,CAAC,GAAG,CAAC;MAC3B,EAAE,EAAER,MAAM,CAACE,MAAM,CAACM,IAAI,CAAC,EAAE,CAAC;MAC1B,EAAE,EAAER,MAAM,CAACE,MAAM,CAACM,IAAI,CAAC,EAAE,CAAC;MAC1B;MACA,EAAE,EAAER,MAAM,CAACE,MAAM,CAACM,IAAI,CAAC,EAAE,CAAC;MAC1B,EAAE,EAAER,MAAM,CAACE,MAAM,CAACM,IAAI,CAAC,EAAE,CAAC;MAC1B,GAAG,EAAER,MAAM,CAACE,MAAM,CAACM,IAAI,CAAC,EAAE;IAC9B,CAAC;IACDC,MAAM,EAAE;MACJ,EAAE,EAAET,MAAM,CAACE,MAAM,CAACO,MAAM,CAAC,GAAG,CAAC;MAC7B,EAAE,EAAET,MAAM,CAACE,MAAM,CAACO,MAAM,CAAC,EAAE,CAAC;MAC5B,EAAE,EAAET,MAAM,CAACE,MAAM,CAACO,MAAM,CAAC,EAAE,CAAC;MAC5B;MACA,EAAE,EAAET,MAAM,CAACE,MAAM,CAACO,MAAM,CAAC,EAAE,CAAC;MAC5B,EAAE,EAAET,MAAM,CAACE,MAAM,CAACO,MAAM,CAAC,EAAE,CAAC;MAC5B,GAAG,EAAET,MAAM,CAACE,MAAM,CAACO,MAAM,CAAC,EAAE;IAChC,CAAC;IACDC,IAAI,EAAE;MACF,EAAE,EAAEV,MAAM,CAACE,MAAM,CAACQ,IAAI,CAAC,GAAG,CAAC;MAC3B,EAAE,EAAEV,MAAM,CAACE,MAAM,CAACQ,IAAI,CAAC,EAAE,CAAC;MAC1B,EAAE,EAAEV,MAAM,CAACE,MAAM,CAACQ,IAAI,CAAC,EAAE,CAAC;MAC1B;MACA,EAAE,EAAEV,MAAM,CAACE,MAAM,CAACQ,IAAI,CAAC,EAAE,CAAC;MAC1B,EAAE,EAAEV,MAAM,CAACE,MAAM,CAACQ,IAAI,CAAC,EAAE,CAAC;MAC1B,GAAG,EAAEV,MAAM,CAACE,MAAM,CAACQ,IAAI,CAAC,EAAE;IAC9B,CAAC;IACDC,OAAO,EAAE;MACL,EAAE,EAAEX,MAAM,CAACE,MAAM,CAACS,OAAO,CAAC,GAAG,CAAC;MAC9B,EAAE,EAAEX,MAAM,CAACE,MAAM,CAACS,OAAO,CAAC,EAAE,CAAC;MAC7B,EAAE,EAAEX,MAAM,CAACE,MAAM,CAACS,OAAO,CAAC,EAAE,CAAC;MAC7B;MACA,EAAE,EAAEX,MAAM,CAACE,MAAM,CAACS,OAAO,CAAC,EAAE,CAAC;MAC7B,EAAE,EAAEX,MAAM,CAACE,MAAM,CAACS,OAAO,CAAC,EAAE,CAAC;MAC7B,GAAG,EAAEX,MAAM,CAACE,MAAM,CAACS,OAAO,CAAC,EAAE;IACjC,CAAC;IACDC,IAAI,EAAE;MACFC,OAAO,EAAE,gBAAgB;MACzBC,SAAS,EAAE,sBAAsB;MACjCC,QAAQ,EAAE,qBAAqB;MAC/BC,OAAO,EAAE;IACb,CAAC;IACDC,UAAU,EAAE;MACRJ,OAAO,EAAE,qBAAqB;MAC9BC,SAAS,EAAE,qBAAqB;MAChCC,QAAQ,EAAE;IACd,CAAC;IACDG,MAAM,EAAE;MACJL,OAAO,EAAE,qBAAqB;MAC9BC,SAAS,EAAE,qBAAqB;MAChCC,QAAQ,EAAE;IACd,CAAC;IACDI,MAAM,EAAE;MACJN,OAAO,EAAE;QAAEO,KAAK,EAAE;MAA8B,CAAC;MACjDN,SAAS,EAAE;QAAEM,KAAK,EAAE;MAA8B,CAAC;MACnDL,QAAQ,EAAE;QAAEK,KAAK,EAAE;MAA8B;IACrD,CAAC;IACDC,OAAO,EAAE;MACL,CAAC,EAAE,yBAAyB;MAC5B,EAAE,EAAE,wBAAwB;MAC5B,EAAE,EAAE,wBAAwB;MAC5B,EAAE,EAAE,wBAAwB;MAC5B,EAAE,EAAE,wBAAwB;MAC5B,EAAE,EAAE,wBAAwB;MAC5B,EAAE,EAAE,wBAAwB;MAC5B,EAAE,EAAE,wBAAwB;MAC5B,EAAE,EAAE,wBAAwB;MAC5B,EAAE,EAAE;IACR;EACJ;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,GAAG;EAC5BC,SAAS,EAAE,MAAM;EACjBvB,MAAM,EAAEC;AACZ,CAAC;AACD,MAAMuB,qBAAqB,GAAG;EAC1B,GAAGvB;AACP,CAAC;AAED,SAASqB,uBAAuB,EAAEE,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}