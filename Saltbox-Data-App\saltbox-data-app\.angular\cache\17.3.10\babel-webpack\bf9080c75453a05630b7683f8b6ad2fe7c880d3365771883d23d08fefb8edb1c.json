{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/SaltboxActionableGrid/Saltbox-Data-App/saltbox-data-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { throwError, of } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { ParameterTypes } from 'src/app/core/enums/actionable-grid';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"src/app/core/services/env.service\";\nimport * as i3 from \"src/app/core/services/crypto.service\";\nexport class GridLayoutsService {\n  constructor(httpClient, envService, cryptoService) {\n    this.httpClient = httpClient;\n    this.envService = envService;\n    this.cryptoService = cryptoService;\n    this.baseUrl = `${this.envService.gatewayServiceApiUrl}api/gridlayouts`;\n  }\n  getLayouts(gridId) {\n    const url = `${this.baseUrl}/grids/${gridId}`;\n    return this.httpClient.get(url, {\n      withCredentials: true\n    }).pipe(catchError(error => {\n      if (error.status === 404) {\n        return of([]);\n      }\n      return throwError(() => error);\n    }));\n  }\n  getLayout(id) {\n    const url = `${this.baseUrl}/${id}`;\n    return this.httpClient.get(url, {\n      withCredentials: true\n    }).pipe(catchError(error => {\n      if (error.status === 404) {\n        return of(undefined);\n      }\n      return throwError(() => error);\n    }));\n  }\n  saveLayout(layout) {\n    const url = `${this.baseUrl}`;\n    return this.httpClient.post(url, layout, {\n      withCredentials: true\n    }).pipe(catchError(error => throwError(() => error)));\n  }\n  updateLayout(id, layout) {\n    console.log('Updating layout', layout.agGridSettings);\n    const url = `${this.baseUrl}/${id}`;\n    return this.httpClient.put(url, layout, {\n      withCredentials: true\n    }).pipe(catchError(error => throwError(() => error)));\n  }\n  deleteLayout(id) {\n    const url = `${this.baseUrl}/${id}`;\n    return this.httpClient.delete(url, {\n      withCredentials: true\n    }).pipe(catchError(error => {\n      if (error.status === 404) {\n        return of(undefined);\n      }\n      return throwError(() => error);\n    }));\n  }\n  setDefaultLayout(id, gridId) {\n    const url = `${this.baseUrl}/${id}/default`;\n    return this.httpClient.put(url, {\n      gridId\n    }, {\n      withCredentials: true\n    }).pipe(catchError(error => throwError(() => error)));\n  }\n  setUserSelectedLayout(userSelectedLayout) {\n    const url = `${this.baseUrl}/setuserlayout`;\n    this.httpClient.post(url, userSelectedLayout, {\n      withCredentials: true\n    }).pipe(catchError(error => {\n      console.error('Error setting user selected layout:', error);\n      return throwError(() => error);\n    })).subscribe();\n  }\n  getGridHashId(reportInfo) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const columnsList = reportInfo.formatConfig?.actionableGridColumnsConfig?.sort((a, b) => a.column.localeCompare(b.column)).map(col => {\n        return {\n          column: col.column,\n          hasSlicer: col.slicerFilter,\n          groupByHeader: col.groupByHeader\n        };\n      });\n      const userParams = reportInfo?.params?.filter(param => param.paramType === ParameterTypes.UserEntered).map(param => {\n        return {\n          column: param.paramSource,\n          allowEmpty: param.allowEmpty,\n          columnUID: param.columnUID,\n          valueType: param.valueType,\n          paramType: param.paramType,\n          paramValidValuesList: param.paramValidValuesList\n        };\n      });\n      return yield _this.cryptoService.hashSHA256(userParams ? JSON.stringify([columnsList, userParams]) : JSON.stringify(columnsList));\n    })();\n  }\n  static {\n    this.ɵfac = function GridLayoutsService_Factory(t) {\n      return new (t || GridLayoutsService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.EnvService), i0.ɵɵinject(i3.CryptoService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GridLayoutsService,\n      factory: GridLayoutsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["throwError", "of", "catchError", "ParameterTypes", "GridLayoutsService", "constructor", "httpClient", "envService", "cryptoService", "baseUrl", "gatewayServiceApiUrl", "getLayouts", "gridId", "url", "get", "withCredentials", "pipe", "error", "status", "getLayout", "id", "undefined", "saveLayout", "layout", "post", "updateLayout", "console", "log", "agGridSettings", "put", "deleteLayout", "delete", "setDefaultLayout", "setUserSelectedLayout", "userSelectedLayout", "subscribe", "getGridHashId", "reportInfo", "_this", "_asyncToGenerator", "columnsList", "formatConfig", "actionableGridColumnsConfig", "sort", "a", "b", "column", "localeCompare", "map", "col", "hasSlicer", "slicerFilter", "groupByHeader", "userParams", "params", "filter", "param", "paramType", "UserEntered", "paramSource", "allowEmpty", "columnUID", "valueType", "paramValidValuesList", "hashSHA256", "JSON", "stringify", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "EnvService", "i3", "CryptoService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\services\\grid-layouts.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, throwError, of } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { GridLayout } from '../model/grid-layout';\nimport { UserSelectedLayout } from '../model/user-selected-layout';\nimport { EnvService } from 'src/app/core/services/env.service';\nimport { ReportInfo } from 'src/app/core/models/report-info';\nimport { ParameterTypes } from 'src/app/core/enums/actionable-grid';\nimport { CryptoService } from 'src/app/core/services/crypto.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class GridLayoutsService {\n  private baseUrl: string;\n\n  constructor(\n    private httpClient: HttpClient,\n    private envService: EnvService,\n    private cryptoService: CryptoService\n  ) {\n    this.baseUrl = `${this.envService.gatewayServiceApiUrl}api/gridlayouts`;\n  }\n\n  getLayouts(gridId: string): Observable<GridLayout[]> {\n    const url = `${this.baseUrl}/grids/${gridId}`;\n    return this.httpClient\n      .get<GridLayout[]>(url, { withCredentials: true })\n      .pipe(\n        catchError(error => {\n          if (error.status === 404) {\n            return of([]);\n          }\n          return throwError(() => error);\n        })\n      );\n  }\n\n  getLayout(id: string): Observable<GridLayout> {\n    const url = `${this.baseUrl}/${id}`;\n    return this.httpClient\n      .get<GridLayout>(url, { withCredentials: true })\n      .pipe(\n        catchError(error => {\n          if (error.status === 404) {\n            return of(undefined);\n          }\n          return throwError(() => error);\n        })\n      );\n  }\n\n  saveLayout(layout: GridLayout): Observable<GridLayout> {\n    const url = `${this.baseUrl}`;\n    return this.httpClient\n      .post<GridLayout>(url, layout, { withCredentials: true })\n      .pipe(\n        catchError(error => throwError(() => error))\n      );\n  }\n\n  updateLayout(id: string, layout: GridLayout): Observable<GridLayout> {\n    console.log('Updating layout', layout.agGridSettings);\n    const url = `${this.baseUrl}/${id}`;\n    return this.httpClient\n      .put<GridLayout>(url, layout, { withCredentials: true })\n      .pipe(\n        catchError(error => throwError(() => error))\n      );\n  }\n\n  deleteLayout(id: string): Observable<void> {\n    const url = `${this.baseUrl}/${id}`;\n    return this.httpClient\n      .delete<void>(url, { withCredentials: true })\n      .pipe(\n        catchError(error => {\n          if (error.status === 404) {\n            return of(undefined);\n          }\n          return throwError(() => error);\n        })\n      );\n  }\n\n  setDefaultLayout(id: string, gridId: string): Observable<GridLayout> {\n    const url = `${this.baseUrl}/${id}/default`;\n    return this.httpClient\n      .put<GridLayout>(url, { gridId }, { withCredentials: true })\n      .pipe(\n        catchError(error => throwError(() => error))\n      );\n  }\n\n  setUserSelectedLayout(userSelectedLayout: UserSelectedLayout): void {\n    const url = `${this.baseUrl}/setuserlayout`;\n    this.httpClient\n      .post<UserSelectedLayout>(url, userSelectedLayout, { withCredentials: true })\n      .pipe(\n        catchError(error => {\n          console.error('Error setting user selected layout:', error);\n          return throwError(() => error);\n        })\n      )\n      .subscribe();\n  }\n\n  async getGridHashId(reportInfo: ReportInfo) {\n    const columnsList = reportInfo.formatConfig?.actionableGridColumnsConfig?.sort((a, b) => a.column.localeCompare(b.column)).map((col) => {\n      return { column: col.column, hasSlicer: col.slicerFilter, groupByHeader: col.groupByHeader };\n    });\n\n    const userParams = reportInfo?.params?.filter(param => param.paramType === ParameterTypes.UserEntered).map(param => {\n      return {\n        column: param.paramSource, allowEmpty: param.allowEmpty, columnUID: param.columnUID,\n        valueType: param.valueType, paramType: param.paramType, paramValidValuesList: param.paramValidValuesList\n      };\n    });\n\n    return await this.cryptoService.hashSHA256(userParams ? JSON.stringify([columnsList, userParams]) : JSON.stringify(columnsList));\n  }\n}\n"], "mappings": ";AAEA,SAAqBA,UAAU,EAAEC,EAAE,QAAQ,MAAM;AACjD,SAASC,UAAU,QAAQ,gBAAgB;AAK3C,SAASC,cAAc,QAAQ,oCAAoC;;;;;AAMnE,OAAM,MAAOC,kBAAkB;EAG7BC,YACUC,UAAsB,EACtBC,UAAsB,EACtBC,aAA4B;IAF5B,KAAAF,UAAU,GAAVA,UAAU;IACV,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IAErB,IAAI,CAACC,OAAO,GAAG,GAAG,IAAI,CAACF,UAAU,CAACG,oBAAoB,iBAAiB;EACzE;EAEAC,UAAUA,CAACC,MAAc;IACvB,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACJ,OAAO,UAAUG,MAAM,EAAE;IAC7C,OAAO,IAAI,CAACN,UAAU,CACnBQ,GAAG,CAAeD,GAAG,EAAE;MAAEE,eAAe,EAAE;IAAI,CAAE,CAAC,CACjDC,IAAI,CACHd,UAAU,CAACe,KAAK,IAAG;MACjB,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;QACxB,OAAOjB,EAAE,CAAC,EAAE,CAAC;MACf;MACA,OAAOD,UAAU,CAAC,MAAMiB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEAE,SAASA,CAACC,EAAU;IAClB,MAAMP,GAAG,GAAG,GAAG,IAAI,CAACJ,OAAO,IAAIW,EAAE,EAAE;IACnC,OAAO,IAAI,CAACd,UAAU,CACnBQ,GAAG,CAAaD,GAAG,EAAE;MAAEE,eAAe,EAAE;IAAI,CAAE,CAAC,CAC/CC,IAAI,CACHd,UAAU,CAACe,KAAK,IAAG;MACjB,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;QACxB,OAAOjB,EAAE,CAACoB,SAAS,CAAC;MACtB;MACA,OAAOrB,UAAU,CAAC,MAAMiB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEAK,UAAUA,CAACC,MAAkB;IAC3B,MAAMV,GAAG,GAAG,GAAG,IAAI,CAACJ,OAAO,EAAE;IAC7B,OAAO,IAAI,CAACH,UAAU,CACnBkB,IAAI,CAAaX,GAAG,EAAEU,MAAM,EAAE;MAAER,eAAe,EAAE;IAAI,CAAE,CAAC,CACxDC,IAAI,CACHd,UAAU,CAACe,KAAK,IAAIjB,UAAU,CAAC,MAAMiB,KAAK,CAAC,CAAC,CAC7C;EACL;EAEAQ,YAAYA,CAACL,EAAU,EAAEG,MAAkB;IACzCG,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEJ,MAAM,CAACK,cAAc,CAAC;IACrD,MAAMf,GAAG,GAAG,GAAG,IAAI,CAACJ,OAAO,IAAIW,EAAE,EAAE;IACnC,OAAO,IAAI,CAACd,UAAU,CACnBuB,GAAG,CAAahB,GAAG,EAAEU,MAAM,EAAE;MAAER,eAAe,EAAE;IAAI,CAAE,CAAC,CACvDC,IAAI,CACHd,UAAU,CAACe,KAAK,IAAIjB,UAAU,CAAC,MAAMiB,KAAK,CAAC,CAAC,CAC7C;EACL;EAEAa,YAAYA,CAACV,EAAU;IACrB,MAAMP,GAAG,GAAG,GAAG,IAAI,CAACJ,OAAO,IAAIW,EAAE,EAAE;IACnC,OAAO,IAAI,CAACd,UAAU,CACnByB,MAAM,CAAOlB,GAAG,EAAE;MAAEE,eAAe,EAAE;IAAI,CAAE,CAAC,CAC5CC,IAAI,CACHd,UAAU,CAACe,KAAK,IAAG;MACjB,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;QACxB,OAAOjB,EAAE,CAACoB,SAAS,CAAC;MACtB;MACA,OAAOrB,UAAU,CAAC,MAAMiB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEAe,gBAAgBA,CAACZ,EAAU,EAAER,MAAc;IACzC,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACJ,OAAO,IAAIW,EAAE,UAAU;IAC3C,OAAO,IAAI,CAACd,UAAU,CACnBuB,GAAG,CAAahB,GAAG,EAAE;MAAED;IAAM,CAAE,EAAE;MAAEG,eAAe,EAAE;IAAI,CAAE,CAAC,CAC3DC,IAAI,CACHd,UAAU,CAACe,KAAK,IAAIjB,UAAU,CAAC,MAAMiB,KAAK,CAAC,CAAC,CAC7C;EACL;EAEAgB,qBAAqBA,CAACC,kBAAsC;IAC1D,MAAMrB,GAAG,GAAG,GAAG,IAAI,CAACJ,OAAO,gBAAgB;IAC3C,IAAI,CAACH,UAAU,CACZkB,IAAI,CAAqBX,GAAG,EAAEqB,kBAAkB,EAAE;MAAEnB,eAAe,EAAE;IAAI,CAAE,CAAC,CAC5EC,IAAI,CACHd,UAAU,CAACe,KAAK,IAAG;MACjBS,OAAO,CAACT,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,OAAOjB,UAAU,CAAC,MAAMiB,KAAK,CAAC;IAChC,CAAC,CAAC,CACH,CACAkB,SAAS,EAAE;EAChB;EAEMC,aAAaA,CAACC,UAAsB;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACxC,MAAMC,WAAW,GAAGH,UAAU,CAACI,YAAY,EAAEC,2BAA2B,EAAEC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,MAAM,CAACC,aAAa,CAACF,CAAC,CAACC,MAAM,CAAC,CAAC,CAACE,GAAG,CAAEC,GAAG,IAAI;QACrI,OAAO;UAAEH,MAAM,EAAEG,GAAG,CAACH,MAAM;UAAEI,SAAS,EAAED,GAAG,CAACE,YAAY;UAAEC,aAAa,EAAEH,GAAG,CAACG;QAAa,CAAE;MAC9F,CAAC,CAAC;MAEF,MAAMC,UAAU,GAAGhB,UAAU,EAAEiB,MAAM,EAAEC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,SAAS,KAAKtD,cAAc,CAACuD,WAAW,CAAC,CAACV,GAAG,CAACQ,KAAK,IAAG;QACjH,OAAO;UACLV,MAAM,EAAEU,KAAK,CAACG,WAAW;UAAEC,UAAU,EAAEJ,KAAK,CAACI,UAAU;UAAEC,SAAS,EAAEL,KAAK,CAACK,SAAS;UACnFC,SAAS,EAAEN,KAAK,CAACM,SAAS;UAAEL,SAAS,EAAED,KAAK,CAACC,SAAS;UAAEM,oBAAoB,EAAEP,KAAK,CAACO;SACrF;MACH,CAAC,CAAC;MAEF,aAAazB,KAAI,CAAC9B,aAAa,CAACwD,UAAU,CAACX,UAAU,GAAGY,IAAI,CAACC,SAAS,CAAC,CAAC1B,WAAW,EAAEa,UAAU,CAAC,CAAC,GAAGY,IAAI,CAACC,SAAS,CAAC1B,WAAW,CAAC,CAAC;IAAC;EACnI;;;uBA3GWpC,kBAAkB,EAAA+D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAlBtE,kBAAkB;MAAAuE,OAAA,EAAlBvE,kBAAkB,CAAAwE,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}