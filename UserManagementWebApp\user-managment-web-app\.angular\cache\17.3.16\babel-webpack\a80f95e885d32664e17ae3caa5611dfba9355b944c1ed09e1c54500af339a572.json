{"ast": null, "code": "const checkboxfield = {\n  alignItems: {\n    value: 'flex-start'\n  },\n  alignContent: {\n    value: 'center'\n  },\n  flexDirection: {\n    value: 'column'\n  },\n  justifyContent: {\n    value: 'center'\n  }\n};\nexport { checkboxfield };", "map": {"version": 3, "names": ["checkboxfield", "alignItems", "value", "align<PERSON><PERSON><PERSON>", "flexDirection", "justifyContent"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/checkboxField.mjs"], "sourcesContent": ["const checkboxfield = {\n    alignItems: { value: 'flex-start' },\n    alignContent: { value: 'center' },\n    flexDirection: { value: 'column' },\n    justifyContent: { value: 'center' },\n};\n\nexport { checkboxfield };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAG;EAClBC,UAAU,EAAE;IAAEC,KAAK,EAAE;EAAa,CAAC;EACnCC,YAAY,EAAE;IAAED,KAAK,EAAE;EAAS,CAAC;EACjCE,aAAa,EAAE;IAAEF,KAAK,EAAE;EAAS,CAAC;EAClCG,cAAc,EAAE;IAAEH,KAAK,EAAE;EAAS;AACtC,CAAC;AAED,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}