{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport '../../../types/errors.mjs';\nimport '../../../errors/errorHelpers.mjs';\nimport { getClientInfo } from '../../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../../utils/retry/retry.mjs';\nimport '../../../parseAWSExports.mjs';\nimport { amplifyUuid } from '../../../utils/amplifyUuid/index.mjs';\nimport '../../../singleton/Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport '../../../Platform/index.mjs';\nimport '../../../Platform/types.mjs';\nimport '../../../BackgroundProcessManager/types.mjs';\nimport '../../../Reachability/Reachability.mjs';\nimport '../../../Hub/index.mjs';\nimport '../../../utils/sessionListener/index.mjs';\nimport '../../../awsClients/pinpoint/base.mjs';\nimport '../../../awsClients/pinpoint/errorHelpers.mjs';\nimport { updateEndpoint as updateEndpoint$1 } from '../../../awsClients/pinpoint/updateEndpoint.mjs';\nimport { cacheEndpointId } from '../utils/cacheEndpointId.mjs';\nimport { createEndpointId, clearCreatedEndpointId } from '../utils/createEndpointId.mjs';\nimport { getEndpointId } from '../utils/getEndpointId.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nconst updateEndpoint = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* ({\n    address,\n    appId,\n    category,\n    channelType,\n    credentials,\n    identityId,\n    optOut,\n    region,\n    userAttributes,\n    userId,\n    userProfile,\n    userAgentValue\n  }) {\n    const endpointId = yield getEndpointId(appId, category);\n    // only generate a new endpoint id if one was not found in cache\n    const createdEndpointId = !endpointId ? createEndpointId(appId, category) : undefined;\n    const {\n      customProperties,\n      demographic,\n      email,\n      location,\n      metrics,\n      name,\n      plan\n    } = userProfile ?? {};\n    // only automatically populate the endpoint with client info and identity id upon endpoint creation to\n    // avoid overwriting the endpoint with these values every time the endpoint is updated\n    const demographicsFromClientInfo = {};\n    const resolvedUserId = createdEndpointId ? userId ?? identityId : userId;\n    if (createdEndpointId) {\n      const clientInfo = getClientInfo();\n      demographicsFromClientInfo.appVersion = clientInfo.appVersion;\n      demographicsFromClientInfo.make = clientInfo.make;\n      demographicsFromClientInfo.model = clientInfo.model;\n      demographicsFromClientInfo.modelVersion = clientInfo.version;\n      demographicsFromClientInfo.platform = clientInfo.platform;\n    }\n    const mergedDemographic = {\n      ...demographicsFromClientInfo,\n      ...demographic\n    };\n    const attributes = {\n      ...(email && {\n        email: [email]\n      }),\n      ...(name && {\n        name: [name]\n      }),\n      ...(plan && {\n        plan: [plan]\n      }),\n      ...customProperties\n    };\n    const shouldAddDemographics = createdEndpointId || demographic;\n    const shouldAddAttributes = email || customProperties || name || plan;\n    const shouldAddUser = resolvedUserId || userAttributes;\n    const input = {\n      ApplicationId: appId,\n      EndpointId: endpointId ?? createdEndpointId,\n      EndpointRequest: {\n        RequestId: amplifyUuid(),\n        EffectiveDate: new Date().toISOString(),\n        ChannelType: channelType,\n        Address: address,\n        ...(shouldAddAttributes && {\n          Attributes: attributes\n        }),\n        ...(shouldAddDemographics && {\n          Demographic: {\n            AppVersion: mergedDemographic.appVersion,\n            Locale: mergedDemographic.locale,\n            Make: mergedDemographic.make,\n            Model: mergedDemographic.model,\n            ModelVersion: mergedDemographic.modelVersion,\n            Platform: mergedDemographic.platform,\n            PlatformVersion: mergedDemographic.platformVersion,\n            Timezone: mergedDemographic.timezone\n          }\n        }),\n        ...(location && {\n          Location: {\n            City: location.city,\n            Country: location.country,\n            Latitude: location.latitude,\n            Longitude: location.longitude,\n            PostalCode: location.postalCode,\n            Region: location.region\n          }\n        }),\n        Metrics: metrics,\n        OptOut: optOut,\n        ...(shouldAddUser && {\n          User: {\n            UserId: resolvedUserId,\n            UserAttributes: userAttributes\n          }\n        })\n      }\n    };\n    try {\n      yield updateEndpoint$1({\n        credentials,\n        region,\n        userAgentValue\n      }, input);\n      // if we had to create an endpoint id, we need to now cache it\n      if (createdEndpointId) {\n        yield cacheEndpointId(appId, category, createdEndpointId);\n      }\n    } finally {\n      // at this point, we completely reset the behavior so even if the update was unsuccessful\n      // we can just start over with a newly created endpoint id\n      if (createdEndpointId) {\n        clearCreatedEndpointId(appId, category);\n      }\n    }\n  });\n  return function updateEndpoint(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { updateEndpoint };", "map": {"version": 3, "names": ["getClientInfo", "amplifyUuid", "updateEndpoint", "updateEndpoint$1", "cacheEndpointId", "createEndpointId", "clearCreatedEndpointId", "getEndpointId", "_ref", "_asyncToGenerator", "address", "appId", "category", "channelType", "credentials", "identityId", "optOut", "region", "userAttributes", "userId", "userProfile", "userAgentValue", "endpointId", "createdEndpointId", "undefined", "customProperties", "demographic", "email", "location", "metrics", "name", "plan", "demographicsFromClientInfo", "resolvedUserId", "clientInfo", "appVersion", "make", "model", "modelVersion", "version", "platform", "mergedDemographic", "attributes", "shouldAddDemographics", "shouldAddAttributes", "shouldAddUser", "input", "ApplicationId", "EndpointId", "EndpointRequest", "RequestId", "EffectiveDate", "Date", "toISOString", "ChannelType", "Address", "Attributes", "Demographic", "AppVersion", "Locale", "locale", "Make", "Model", "ModelVersion", "Platform", "PlatformVersion", "platformVersion", "Timezone", "timezone", "Location", "City", "city", "Country", "country", "Latitude", "latitude", "Longitude", "longitude", "PostalCode", "postalCode", "Region", "Metrics", "OptOut", "User", "UserId", "UserAttributes", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/providers/pinpoint/apis/updateEndpoint.mjs"], "sourcesContent": ["import '../../../types/errors.mjs';\nimport '../../../errors/errorHelpers.mjs';\nimport { getClientInfo } from '../../../utils/getClientInfo/getClientInfo.mjs';\nimport '../../../utils/retry/retry.mjs';\nimport '../../../parseAWSExports.mjs';\nimport { amplifyUuid } from '../../../utils/amplifyUuid/index.mjs';\nimport '../../../singleton/Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport '../../../Platform/index.mjs';\nimport '../../../Platform/types.mjs';\nimport '../../../BackgroundProcessManager/types.mjs';\nimport '../../../Reachability/Reachability.mjs';\nimport '../../../Hub/index.mjs';\nimport '../../../utils/sessionListener/index.mjs';\nimport '../../../awsClients/pinpoint/base.mjs';\nimport '../../../awsClients/pinpoint/errorHelpers.mjs';\nimport { updateEndpoint as updateEndpoint$1 } from '../../../awsClients/pinpoint/updateEndpoint.mjs';\nimport { cacheEndpointId } from '../utils/cacheEndpointId.mjs';\nimport { createEndpointId, clearCreatedEndpointId } from '../utils/createEndpointId.mjs';\nimport { getEndpointId } from '../utils/getEndpointId.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nconst updateEndpoint = async ({ address, appId, category, channelType, credentials, identityId, optOut, region, userAttributes, userId, userProfile, userAgentValue, }) => {\n    const endpointId = await getEndpointId(appId, category);\n    // only generate a new endpoint id if one was not found in cache\n    const createdEndpointId = !endpointId\n        ? createEndpointId(appId, category)\n        : undefined;\n    const { customProperties, demographic, email, location, metrics, name, plan, } = userProfile ?? {};\n    // only automatically populate the endpoint with client info and identity id upon endpoint creation to\n    // avoid overwriting the endpoint with these values every time the endpoint is updated\n    const demographicsFromClientInfo = {};\n    const resolvedUserId = createdEndpointId ? (userId ?? identityId) : userId;\n    if (createdEndpointId) {\n        const clientInfo = getClientInfo();\n        demographicsFromClientInfo.appVersion = clientInfo.appVersion;\n        demographicsFromClientInfo.make = clientInfo.make;\n        demographicsFromClientInfo.model = clientInfo.model;\n        demographicsFromClientInfo.modelVersion = clientInfo.version;\n        demographicsFromClientInfo.platform = clientInfo.platform;\n    }\n    const mergedDemographic = {\n        ...demographicsFromClientInfo,\n        ...demographic,\n    };\n    const attributes = {\n        ...(email && { email: [email] }),\n        ...(name && { name: [name] }),\n        ...(plan && { plan: [plan] }),\n        ...customProperties,\n    };\n    const shouldAddDemographics = createdEndpointId || demographic;\n    const shouldAddAttributes = email || customProperties || name || plan;\n    const shouldAddUser = resolvedUserId || userAttributes;\n    const input = {\n        ApplicationId: appId,\n        EndpointId: endpointId ?? createdEndpointId,\n        EndpointRequest: {\n            RequestId: amplifyUuid(),\n            EffectiveDate: new Date().toISOString(),\n            ChannelType: channelType,\n            Address: address,\n            ...(shouldAddAttributes && { Attributes: attributes }),\n            ...(shouldAddDemographics && {\n                Demographic: {\n                    AppVersion: mergedDemographic.appVersion,\n                    Locale: mergedDemographic.locale,\n                    Make: mergedDemographic.make,\n                    Model: mergedDemographic.model,\n                    ModelVersion: mergedDemographic.modelVersion,\n                    Platform: mergedDemographic.platform,\n                    PlatformVersion: mergedDemographic.platformVersion,\n                    Timezone: mergedDemographic.timezone,\n                },\n            }),\n            ...(location && {\n                Location: {\n                    City: location.city,\n                    Country: location.country,\n                    Latitude: location.latitude,\n                    Longitude: location.longitude,\n                    PostalCode: location.postalCode,\n                    Region: location.region,\n                },\n            }),\n            Metrics: metrics,\n            OptOut: optOut,\n            ...(shouldAddUser && {\n                User: {\n                    UserId: resolvedUserId,\n                    UserAttributes: userAttributes,\n                },\n            }),\n        },\n    };\n    try {\n        await updateEndpoint$1({ credentials, region, userAgentValue }, input);\n        // if we had to create an endpoint id, we need to now cache it\n        if (createdEndpointId) {\n            await cacheEndpointId(appId, category, createdEndpointId);\n        }\n    }\n    finally {\n        // at this point, we completely reset the behavior so even if the update was unsuccessful\n        // we can just start over with a newly created endpoint id\n        if (createdEndpointId) {\n            clearCreatedEndpointId(appId, category);\n        }\n    }\n};\n\nexport { updateEndpoint };\n"], "mappings": ";AAAA,OAAO,2BAA2B;AAClC,OAAO,kCAAkC;AACzC,SAASA,aAAa,QAAQ,gDAAgD;AAC9E,OAAO,gCAAgC;AACvC,OAAO,8BAA8B;AACrC,SAASC,WAAW,QAAQ,sCAAsC;AAClE,OAAO,gDAAgD;AACvD,OAAO,uBAAuB;AAC9B,OAAO,2BAA2B;AAClC,OAAO,6BAA6B;AACpC,OAAO,6BAA6B;AACpC,OAAO,6CAA6C;AACpD,OAAO,wCAAwC;AAC/C,OAAO,wBAAwB;AAC/B,OAAO,0CAA0C;AACjD,OAAO,uCAAuC;AAC9C,OAAO,+CAA+C;AACtD,SAASC,cAAc,IAAIC,gBAAgB,QAAQ,iDAAiD;AACpG,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,+BAA+B;AACxF,SAASC,aAAa,QAAQ,4BAA4B;;AAE1D;AACA;AACA;AACA;AACA;AACA,MAAML,cAAc;EAAA,IAAAM,IAAA,GAAAC,iBAAA,CAAG,WAAO;IAAEC,OAAO;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,WAAW;IAAEC,UAAU;IAAEC,MAAM;IAAEC,MAAM;IAAEC,cAAc;IAAEC,MAAM;IAAEC,WAAW;IAAEC;EAAgB,CAAC,EAAK;IACvK,MAAMC,UAAU,SAASf,aAAa,CAACI,KAAK,EAAEC,QAAQ,CAAC;IACvD;IACA,MAAMW,iBAAiB,GAAG,CAACD,UAAU,GAC/BjB,gBAAgB,CAACM,KAAK,EAAEC,QAAQ,CAAC,GACjCY,SAAS;IACf,MAAM;MAAEC,gBAAgB;MAAEC,WAAW;MAAEC,KAAK;MAAEC,QAAQ;MAAEC,OAAO;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGX,WAAW,IAAI,CAAC,CAAC;IAClG;IACA;IACA,MAAMY,0BAA0B,GAAG,CAAC,CAAC;IACrC,MAAMC,cAAc,GAAGV,iBAAiB,GAAIJ,MAAM,IAAIJ,UAAU,GAAII,MAAM;IAC1E,IAAII,iBAAiB,EAAE;MACnB,MAAMW,UAAU,GAAGlC,aAAa,CAAC,CAAC;MAClCgC,0BAA0B,CAACG,UAAU,GAAGD,UAAU,CAACC,UAAU;MAC7DH,0BAA0B,CAACI,IAAI,GAAGF,UAAU,CAACE,IAAI;MACjDJ,0BAA0B,CAACK,KAAK,GAAGH,UAAU,CAACG,KAAK;MACnDL,0BAA0B,CAACM,YAAY,GAAGJ,UAAU,CAACK,OAAO;MAC5DP,0BAA0B,CAACQ,QAAQ,GAAGN,UAAU,CAACM,QAAQ;IAC7D;IACA,MAAMC,iBAAiB,GAAG;MACtB,GAAGT,0BAA0B;MAC7B,GAAGN;IACP,CAAC;IACD,MAAMgB,UAAU,GAAG;MACf,IAAIf,KAAK,IAAI;QAAEA,KAAK,EAAE,CAACA,KAAK;MAAE,CAAC,CAAC;MAChC,IAAIG,IAAI,IAAI;QAAEA,IAAI,EAAE,CAACA,IAAI;MAAE,CAAC,CAAC;MAC7B,IAAIC,IAAI,IAAI;QAAEA,IAAI,EAAE,CAACA,IAAI;MAAE,CAAC,CAAC;MAC7B,GAAGN;IACP,CAAC;IACD,MAAMkB,qBAAqB,GAAGpB,iBAAiB,IAAIG,WAAW;IAC9D,MAAMkB,mBAAmB,GAAGjB,KAAK,IAAIF,gBAAgB,IAAIK,IAAI,IAAIC,IAAI;IACrE,MAAMc,aAAa,GAAGZ,cAAc,IAAIf,cAAc;IACtD,MAAM4B,KAAK,GAAG;MACVC,aAAa,EAAEpC,KAAK;MACpBqC,UAAU,EAAE1B,UAAU,IAAIC,iBAAiB;MAC3C0B,eAAe,EAAE;QACbC,SAAS,EAAEjD,WAAW,CAAC,CAAC;QACxBkD,aAAa,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACvCC,WAAW,EAAEzC,WAAW;QACxB0C,OAAO,EAAE7C,OAAO;QAChB,IAAIkC,mBAAmB,IAAI;UAAEY,UAAU,EAAEd;QAAW,CAAC,CAAC;QACtD,IAAIC,qBAAqB,IAAI;UACzBc,WAAW,EAAE;YACTC,UAAU,EAAEjB,iBAAiB,CAACN,UAAU;YACxCwB,MAAM,EAAElB,iBAAiB,CAACmB,MAAM;YAChCC,IAAI,EAAEpB,iBAAiB,CAACL,IAAI;YAC5B0B,KAAK,EAAErB,iBAAiB,CAACJ,KAAK;YAC9B0B,YAAY,EAAEtB,iBAAiB,CAACH,YAAY;YAC5C0B,QAAQ,EAAEvB,iBAAiB,CAACD,QAAQ;YACpCyB,eAAe,EAAExB,iBAAiB,CAACyB,eAAe;YAClDC,QAAQ,EAAE1B,iBAAiB,CAAC2B;UAChC;QACJ,CAAC,CAAC;QACF,IAAIxC,QAAQ,IAAI;UACZyC,QAAQ,EAAE;YACNC,IAAI,EAAE1C,QAAQ,CAAC2C,IAAI;YACnBC,OAAO,EAAE5C,QAAQ,CAAC6C,OAAO;YACzBC,QAAQ,EAAE9C,QAAQ,CAAC+C,QAAQ;YAC3BC,SAAS,EAAEhD,QAAQ,CAACiD,SAAS;YAC7BC,UAAU,EAAElD,QAAQ,CAACmD,UAAU;YAC/BC,MAAM,EAAEpD,QAAQ,CAACX;UACrB;QACJ,CAAC,CAAC;QACFgE,OAAO,EAAEpD,OAAO;QAChBqD,MAAM,EAAElE,MAAM;QACd,IAAI6B,aAAa,IAAI;UACjBsC,IAAI,EAAE;YACFC,MAAM,EAAEnD,cAAc;YACtBoD,cAAc,EAAEnE;UACpB;QACJ,CAAC;MACL;IACJ,CAAC;IACD,IAAI;MACA,MAAMf,gBAAgB,CAAC;QAAEW,WAAW;QAAEG,MAAM;QAAEI;MAAe,CAAC,EAAEyB,KAAK,CAAC;MACtE;MACA,IAAIvB,iBAAiB,EAAE;QACnB,MAAMnB,eAAe,CAACO,KAAK,EAAEC,QAAQ,EAAEW,iBAAiB,CAAC;MAC7D;IACJ,CAAC,SACO;MACJ;MACA;MACA,IAAIA,iBAAiB,EAAE;QACnBjB,sBAAsB,CAACK,KAAK,EAAEC,QAAQ,CAAC;MAC3C;IACJ;EACJ,CAAC;EAAA,gBAvFKV,cAAcA,CAAAoF,EAAA;IAAA,OAAA9E,IAAA,CAAA+E,KAAA,OAAAC,SAAA;EAAA;AAAA,GAuFnB;AAED,SAAStF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}