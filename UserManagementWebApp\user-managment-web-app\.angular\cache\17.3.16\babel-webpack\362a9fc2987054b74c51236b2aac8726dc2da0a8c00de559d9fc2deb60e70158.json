{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Middleware injects user agent string to specified header(default to 'x-amz-user-agent'),\n * if the header is not set already.\n *\n * TODO: incorporate new user agent design\n */\nconst userAgentMiddlewareFactory = ({\n  userAgentHeader = 'x-amz-user-agent',\n  userAgentValue = ''\n}) => next => {\n  return /*#__PURE__*/function () {\n    var _userAgentMiddleware = _asyncToGenerator(function* (request) {\n      if (userAgentValue.trim().length === 0) {\n        const result = yield next(request);\n        return result;\n      } else {\n        const headerName = userAgentHeader.toLowerCase();\n        request.headers[headerName] = request.headers[headerName] ? `${request.headers[headerName]} ${userAgentValue}` : userAgentValue;\n        const response = yield next(request);\n        return response;\n      }\n    });\n    function userAgentMiddleware(_x) {\n      return _userAgentMiddleware.apply(this, arguments);\n    }\n    return userAgentMiddleware;\n  }();\n};\nexport { userAgentMiddlewareFactory };", "map": {"version": 3, "names": ["userAgentMiddlewareFactory", "userAgentHeader", "userAgentValue", "next", "_userAgentMiddleware", "_asyncToGenerator", "request", "trim", "length", "result", "headerName", "toLowerCase", "headers", "response", "userAgentMiddleware", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/userAgent/middleware.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Middleware injects user agent string to specified header(default to 'x-amz-user-agent'),\n * if the header is not set already.\n *\n * TODO: incorporate new user agent design\n */\nconst userAgentMiddlewareFactory = ({ userAgentHeader = 'x-amz-user-agent', userAgentValue = '', }) => next => {\n    return async function userAgentMiddleware(request) {\n        if (userAgentValue.trim().length === 0) {\n            const result = await next(request);\n            return result;\n        }\n        else {\n            const headerName = userAgentHeader.toLowerCase();\n            request.headers[headerName] = request.headers[headerName]\n                ? `${request.headers[headerName]} ${userAgentValue}`\n                : userAgentValue;\n            const response = await next(request);\n            return response;\n        }\n    };\n};\n\nexport { userAgentMiddlewareFactory };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,0BAA0B,GAAGA,CAAC;EAAEC,eAAe,GAAG,kBAAkB;EAAEC,cAAc,GAAG;AAAI,CAAC,KAAKC,IAAI,IAAI;EAC3G;IAAA,IAAAC,oBAAA,GAAAC,iBAAA,CAAO,WAAmCC,OAAO,EAAE;MAC/C,IAAIJ,cAAc,CAACK,IAAI,CAAC,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;QACpC,MAAMC,MAAM,SAASN,IAAI,CAACG,OAAO,CAAC;QAClC,OAAOG,MAAM;MACjB,CAAC,MACI;QACD,MAAMC,UAAU,GAAGT,eAAe,CAACU,WAAW,CAAC,CAAC;QAChDL,OAAO,CAACM,OAAO,CAACF,UAAU,CAAC,GAAGJ,OAAO,CAACM,OAAO,CAACF,UAAU,CAAC,GACnD,GAAGJ,OAAO,CAACM,OAAO,CAACF,UAAU,CAAC,IAAIR,cAAc,EAAE,GAClDA,cAAc;QACpB,MAAMW,QAAQ,SAASV,IAAI,CAACG,OAAO,CAAC;QACpC,OAAOO,QAAQ;MACnB;IACJ,CAAC;IAAA,SAbqBC,mBAAmBA,CAAAC,EAAA;MAAA,OAAAX,oBAAA,CAAAY,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAnBH,mBAAmB;EAAA;AAc7C,CAAC;AAED,SAASd,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}