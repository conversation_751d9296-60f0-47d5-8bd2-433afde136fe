{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Date & time utility functions to abstract the `aws-sdk` away from users.\n * (v2 => v3 modularization is a breaking change)\n *\n * @see https://github.com/aws/aws-sdk-js/blob/6edf586dcc1de7fe8fbfbbd9a0d2b1847921e6e1/lib/util.js#L262\n */\nconst FIVE_MINUTES_IN_MS = 1000 * 60 * 5;\n/**\n * This utility is intended to be deprecated and replaced by `signRequest` and `presignUrl` functions from\n * `clients/middleware/signing/signer/signatureV4`.\n *\n * TODO: refactor the logics here into `signRequest` and `presignUrl` functions and remove this class.\n *\n * @internal\n * @deprecated\n */\nconst DateUtils = {\n  /**\n   * Milliseconds to offset the date to compensate for clock skew between device & services\n   */\n  clockOffset: 0,\n  getDateWithClockOffset() {\n    if (DateUtils.clockOffset) {\n      return new Date(new Date().getTime() + DateUtils.clockOffset);\n    } else {\n      return new Date();\n    }\n  },\n  /**\n   * @returns {number} Clock offset in milliseconds\n   */\n  getClockOffset() {\n    return DateUtils.clockOffset;\n  },\n  getHeaderStringFromDate(date = DateUtils.getDateWithClockOffset()) {\n    return date.toISOString().replace(/[:-]|\\.\\d{3}/g, '');\n  },\n  getDateFromHeaderString(header) {\n    const [, year, month, day, hour, minute, second] = header.match(/^(\\d{4})(\\d{2})(\\d{2})T(\\d{2})(\\d{2})(\\d{2}).+/);\n    return new Date(Date.UTC(Number(year), Number(month) - 1, Number(day), Number(hour), Number(minute), Number(second)));\n  },\n  isClockSkewed(serverDate) {\n    // API gateway permits client calls that are off by no more than ±5 minutes\n    return Math.abs(serverDate.getTime() - DateUtils.getDateWithClockOffset().getTime()) >= FIVE_MINUTES_IN_MS;\n  },\n  isClockSkewError(error) {\n    if (!error.response || !error.response.headers) {\n      return false;\n    }\n    const {\n      headers\n    } = error.response;\n    return Boolean(['BadRequestException', 'InvalidSignatureException'].includes(headers['x-amzn-errortype']) && (headers.date || headers.Date));\n  },\n  /**\n   * @param {number} offset Clock offset in milliseconds\n   */\n  setClockOffset(offset) {\n    DateUtils.clockOffset = offset;\n  }\n};\nexport { DateUtils };", "map": {"version": 3, "names": ["FIVE_MINUTES_IN_MS", "DateUtils", "clockOffset", "getDateWithClockOffset", "Date", "getTime", "getClockOffset", "getHeaderStringFromDate", "date", "toISOString", "replace", "getDateFromHeaderString", "header", "year", "month", "day", "hour", "minute", "second", "match", "UTC", "Number", "isClockSkewed", "serverDate", "Math", "abs", "isClockSkewError", "error", "response", "headers", "Boolean", "includes", "setClockOffset", "offset"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Signer/DateUtils.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Date & time utility functions to abstract the `aws-sdk` away from users.\n * (v2 => v3 modularization is a breaking change)\n *\n * @see https://github.com/aws/aws-sdk-js/blob/6edf586dcc1de7fe8fbfbbd9a0d2b1847921e6e1/lib/util.js#L262\n */\nconst FIVE_MINUTES_IN_MS = 1000 * 60 * 5;\n/**\n * This utility is intended to be deprecated and replaced by `signRequest` and `presignUrl` functions from\n * `clients/middleware/signing/signer/signatureV4`.\n *\n * TODO: refactor the logics here into `signRequest` and `presignUrl` functions and remove this class.\n *\n * @internal\n * @deprecated\n */\nconst DateUtils = {\n    /**\n     * Milliseconds to offset the date to compensate for clock skew between device & services\n     */\n    clockOffset: 0,\n    getDateWithClockOffset() {\n        if (DateUtils.clockOffset) {\n            return new Date(new Date().getTime() + DateUtils.clockOffset);\n        }\n        else {\n            return new Date();\n        }\n    },\n    /**\n     * @returns {number} Clock offset in milliseconds\n     */\n    getClockOffset() {\n        return DateUtils.clockOffset;\n    },\n    getHeaderStringFromDate(date = DateUtils.getDateWithClockOffset()) {\n        return date.toISOString().replace(/[:-]|\\.\\d{3}/g, '');\n    },\n    getDateFromHeaderString(header) {\n        const [, year, month, day, hour, minute, second] = header.match(/^(\\d{4})(\\d{2})(\\d{2})T(\\d{2})(\\d{2})(\\d{2}).+/);\n        return new Date(Date.UTC(Number(year), Number(month) - 1, Number(day), Number(hour), Number(minute), Number(second)));\n    },\n    isClockSkewed(serverDate) {\n        // API gateway permits client calls that are off by no more than ±5 minutes\n        return (Math.abs(serverDate.getTime() - DateUtils.getDateWithClockOffset().getTime()) >= FIVE_MINUTES_IN_MS);\n    },\n    isClockSkewError(error) {\n        if (!error.response || !error.response.headers) {\n            return false;\n        }\n        const { headers } = error.response;\n        return Boolean(['BadRequestException', 'InvalidSignatureException'].includes(headers['x-amzn-errortype']) &&\n            (headers.date || headers.Date));\n    },\n    /**\n     * @param {number} offset Clock offset in milliseconds\n     */\n    setClockOffset(offset) {\n        DateUtils.clockOffset = offset;\n    },\n};\n\nexport { DateUtils };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,kBAAkB,GAAG,IAAI,GAAG,EAAE,GAAG,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG;EACd;AACJ;AACA;EACIC,WAAW,EAAE,CAAC;EACdC,sBAAsBA,CAAA,EAAG;IACrB,IAAIF,SAAS,CAACC,WAAW,EAAE;MACvB,OAAO,IAAIE,IAAI,CAAC,IAAIA,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGJ,SAAS,CAACC,WAAW,CAAC;IACjE,CAAC,MACI;MACD,OAAO,IAAIE,IAAI,CAAC,CAAC;IACrB;EACJ,CAAC;EACD;AACJ;AACA;EACIE,cAAcA,CAAA,EAAG;IACb,OAAOL,SAAS,CAACC,WAAW;EAChC,CAAC;EACDK,uBAAuBA,CAACC,IAAI,GAAGP,SAAS,CAACE,sBAAsB,CAAC,CAAC,EAAE;IAC/D,OAAOK,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;EAC1D,CAAC;EACDC,uBAAuBA,CAACC,MAAM,EAAE;IAC5B,MAAM,GAAGC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,CAAC,GAAGN,MAAM,CAACO,KAAK,CAAC,gDAAgD,CAAC;IACjH,OAAO,IAAIf,IAAI,CAACA,IAAI,CAACgB,GAAG,CAACC,MAAM,CAACR,IAAI,CAAC,EAAEQ,MAAM,CAACP,KAAK,CAAC,GAAG,CAAC,EAAEO,MAAM,CAACN,GAAG,CAAC,EAAEM,MAAM,CAACL,IAAI,CAAC,EAAEK,MAAM,CAACJ,MAAM,CAAC,EAAEI,MAAM,CAACH,MAAM,CAAC,CAAC,CAAC;EACzH,CAAC;EACDI,aAAaA,CAACC,UAAU,EAAE;IACtB;IACA,OAAQC,IAAI,CAACC,GAAG,CAACF,UAAU,CAAClB,OAAO,CAAC,CAAC,GAAGJ,SAAS,CAACE,sBAAsB,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,IAAIL,kBAAkB;EAC/G,CAAC;EACD0B,gBAAgBA,CAACC,KAAK,EAAE;IACpB,IAAI,CAACA,KAAK,CAACC,QAAQ,IAAI,CAACD,KAAK,CAACC,QAAQ,CAACC,OAAO,EAAE;MAC5C,OAAO,KAAK;IAChB;IACA,MAAM;MAAEA;IAAQ,CAAC,GAAGF,KAAK,CAACC,QAAQ;IAClC,OAAOE,OAAO,CAAC,CAAC,qBAAqB,EAAE,2BAA2B,CAAC,CAACC,QAAQ,CAACF,OAAO,CAAC,kBAAkB,CAAC,CAAC,KACpGA,OAAO,CAACrB,IAAI,IAAIqB,OAAO,CAACzB,IAAI,CAAC,CAAC;EACvC,CAAC;EACD;AACJ;AACA;EACI4B,cAAcA,CAACC,MAAM,EAAE;IACnBhC,SAAS,CAACC,WAAW,GAAG+B,MAAM;EAClC;AACJ,CAAC;AAED,SAAShC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}