{"ast": null, "code": "/* AUTO-GENERATED. DO NOT MODIFY. */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n\n JS Beautifier\n---------------\n\n\n  Written by Einar <PERSON>lmanis, <<EMAIL>>\n      https://beautifier.io/\n\n  Originally converted to javascript by Vital, <<EMAIL>>\n  \"End braces on own line\" added by <PERSON> J. Shull, <<EMAIL>>\n  Parsing improvements for brace-less statements by Liam Newman <<EMAIL>>\n\n\n  Usage:\n    js_beautify(js_source_text);\n    js_beautify(js_source_text, options);\n\n  The options are:\n    indent_size (default 4)          - indentation size,\n    indent_char (default space)      - character to indent with,\n    preserve_newlines (default true) - whether existing line breaks should be preserved,\n    max_preserve_newlines (default unlimited) - maximum number of line breaks to be preserved in one chunk,\n\n    jslint_happy (default false) - if true, then jslint-stricter mode is enforced.\n\n            jslint_happy        !jslint_happy\n            ---------------------------------\n            function ()         function()\n\n            switch () {         switch() {\n            case 1:               case 1:\n              break;                break;\n            }                   }\n\n    space_after_anon_function (default false) - should the space before an anonymous function's parens be added, \"function()\" vs \"function ()\",\n          NOTE: This option is overridden by jslint_happy (i.e. if jslint_happy is true, space_after_anon_function is true by design)\n\n    brace_style (default \"collapse\") - \"collapse\" | \"expand\" | \"end-expand\" | \"none\" | any of the former + \",preserve-inline\"\n            put braces on the same line as control statements (default), or put braces on own line (Allman / ANSI style), or just put end braces on own line, or attempt to keep them where they are.\n            preserve-inline will try to preserve inline blocks of curly braces\n\n    space_before_conditional (default true) - should the space before conditional statement be added, \"if(true)\" vs \"if (true)\",\n\n    unescape_strings (default false) - should printable characters in strings encoded in \\xNN notation be unescaped, \"example\" vs \"\\x65\\x78\\x61\\x6d\\x70\\x6c\\x65\"\n\n    wrap_line_length (default unlimited) - lines should wrap at next opportunity after this number of characters.\n          NOTE: This is not a hard limit. Lines will continue until a point where a newline would\n                be preserved if it were present.\n\n    end_with_newline (default false)  - end output with a newline\n\n\n    e.g\n\n    js_beautify(js_source_text, {\n      'indent_size': 1,\n      'indent_char': '\\t'\n    });\n\n*/\n\n(function () {\n  /* GENERATED_BUILD_OUTPUT */\n  var legacy_beautify_js;\n  /******/\n  (function () {\n    // webpackBootstrap\n    /******/\n    \"use strict\";\n\n    /******/\n    var __webpack_modules__ = [(/* 0 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var Beautifier = __webpack_require__(1).Beautifier,\n        Options = __webpack_require__(5).Options;\n      function js_beautify(js_source_text, options) {\n        var beautifier = new Beautifier(js_source_text, options);\n        return beautifier.beautify();\n      }\n      module.exports = js_beautify;\n      module.exports.defaultOptions = function () {\n        return new Options();\n      };\n\n      /***/\n    }), (/* 1 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var Output = __webpack_require__(2).Output;\n      var Token = __webpack_require__(3).Token;\n      var acorn = __webpack_require__(4);\n      var Options = __webpack_require__(5).Options;\n      var Tokenizer = __webpack_require__(7).Tokenizer;\n      var line_starters = __webpack_require__(7).line_starters;\n      var positionable_operators = __webpack_require__(7).positionable_operators;\n      var TOKEN = __webpack_require__(7).TOKEN;\n      function in_array(what, arr) {\n        return arr.indexOf(what) !== -1;\n      }\n      function ltrim(s) {\n        return s.replace(/^\\s+/g, '');\n      }\n      function generateMapFromStrings(list) {\n        var result = {};\n        for (var x = 0; x < list.length; x++) {\n          // make the mapped names underscored instead of dash\n          result[list[x].replace(/-/g, '_')] = list[x];\n        }\n        return result;\n      }\n      function reserved_word(token, word) {\n        return token && token.type === TOKEN.RESERVED && token.text === word;\n      }\n      function reserved_array(token, words) {\n        return token && token.type === TOKEN.RESERVED && in_array(token.text, words);\n      }\n      // Unsure of what they mean, but they work. Worth cleaning up in future.\n      var special_words = ['case', 'return', 'do', 'if', 'throw', 'else', 'await', 'break', 'continue', 'async'];\n      var validPositionValues = ['before-newline', 'after-newline', 'preserve-newline'];\n\n      // Generate map from array\n      var OPERATOR_POSITION = generateMapFromStrings(validPositionValues);\n      var OPERATOR_POSITION_BEFORE_OR_PRESERVE = [OPERATOR_POSITION.before_newline, OPERATOR_POSITION.preserve_newline];\n      var MODE = {\n        BlockStatement: 'BlockStatement',\n        // 'BLOCK'\n        Statement: 'Statement',\n        // 'STATEMENT'\n        ObjectLiteral: 'ObjectLiteral',\n        // 'OBJECT',\n        ArrayLiteral: 'ArrayLiteral',\n        //'[EXPRESSION]',\n        ForInitializer: 'ForInitializer',\n        //'(FOR-EXPRESSION)',\n        Conditional: 'Conditional',\n        //'(COND-EXPRESSION)',\n        Expression: 'Expression' //'(EXPRESSION)'\n      };\n      function remove_redundant_indentation(output, frame) {\n        // This implementation is effective but has some issues:\n        //     - can cause line wrap to happen too soon due to indent removal\n        //           after wrap points are calculated\n        // These issues are minor compared to ugly indentation.\n\n        if (frame.multiline_frame || frame.mode === MODE.ForInitializer || frame.mode === MODE.Conditional) {\n          return;\n        }\n\n        // remove one indent from each line inside this section\n        output.remove_indent(frame.start_line_index);\n      }\n\n      // we could use just string.split, but\n      // IE doesn't like returning empty strings\n      function split_linebreaks(s) {\n        //return s.split(/\\x0d\\x0a|\\x0a/);\n\n        s = s.replace(acorn.allLineBreaks, '\\n');\n        var out = [],\n          idx = s.indexOf(\"\\n\");\n        while (idx !== -1) {\n          out.push(s.substring(0, idx));\n          s = s.substring(idx + 1);\n          idx = s.indexOf(\"\\n\");\n        }\n        if (s.length) {\n          out.push(s);\n        }\n        return out;\n      }\n      function is_array(mode) {\n        return mode === MODE.ArrayLiteral;\n      }\n      function is_expression(mode) {\n        return in_array(mode, [MODE.Expression, MODE.ForInitializer, MODE.Conditional]);\n      }\n      function all_lines_start_with(lines, c) {\n        for (var i = 0; i < lines.length; i++) {\n          var line = lines[i].trim();\n          if (line.charAt(0) !== c) {\n            return false;\n          }\n        }\n        return true;\n      }\n      function each_line_matches_indent(lines, indent) {\n        var i = 0,\n          len = lines.length,\n          line;\n        for (; i < len; i++) {\n          line = lines[i];\n          // allow empty lines to pass through\n          if (line && line.indexOf(indent) !== 0) {\n            return false;\n          }\n        }\n        return true;\n      }\n      function Beautifier(source_text, options) {\n        options = options || {};\n        this._source_text = source_text || '';\n        this._output = null;\n        this._tokens = null;\n        this._last_last_text = null;\n        this._flags = null;\n        this._previous_flags = null;\n        this._flag_store = null;\n        this._options = new Options(options);\n      }\n      Beautifier.prototype.create_flags = function (flags_base, mode) {\n        var next_indent_level = 0;\n        if (flags_base) {\n          next_indent_level = flags_base.indentation_level;\n          if (!this._output.just_added_newline() && flags_base.line_indent_level > next_indent_level) {\n            next_indent_level = flags_base.line_indent_level;\n          }\n        }\n        var next_flags = {\n          mode: mode,\n          parent: flags_base,\n          last_token: flags_base ? flags_base.last_token : new Token(TOKEN.START_BLOCK, ''),\n          // last token text\n          last_word: flags_base ? flags_base.last_word : '',\n          // last TOKEN.WORD passed\n          declaration_statement: false,\n          declaration_assignment: false,\n          multiline_frame: false,\n          inline_frame: false,\n          if_block: false,\n          else_block: false,\n          class_start_block: false,\n          // class A { INSIDE HERE } or class B extends C { INSIDE HERE }\n          do_block: false,\n          do_while: false,\n          import_block: false,\n          in_case_statement: false,\n          // switch(..){ INSIDE HERE }\n          in_case: false,\n          // we're on the exact line with \"case 0:\"\n          case_body: false,\n          // the indented case-action block\n          case_block: false,\n          // the indented case-action block is wrapped with {}\n          indentation_level: next_indent_level,\n          alignment: 0,\n          line_indent_level: flags_base ? flags_base.line_indent_level : next_indent_level,\n          start_line_index: this._output.get_line_number(),\n          ternary_depth: 0\n        };\n        return next_flags;\n      };\n      Beautifier.prototype._reset = function (source_text) {\n        var baseIndentString = source_text.match(/^[\\t ]*/)[0];\n        this._last_last_text = ''; // pre-last token text\n        this._output = new Output(this._options, baseIndentString);\n\n        // If testing the ignore directive, start with output disable set to true\n        this._output.raw = this._options.test_output_raw;\n\n        // Stack of parsing/formatting states, including MODE.\n        // We tokenize, parse, and output in an almost purely a forward-only stream of token input\n        // and formatted output.  This makes the beautifier less accurate than full parsers\n        // but also far more tolerant of syntax errors.\n        //\n        // For example, the default mode is MODE.BlockStatement. If we see a '{' we push a new frame of type\n        // MODE.BlockStatement on the the stack, even though it could be object literal.  If we later\n        // encounter a \":\", we'll switch to to MODE.ObjectLiteral.  If we then see a \";\",\n        // most full parsers would die, but the beautifier gracefully falls back to\n        // MODE.BlockStatement and continues on.\n        this._flag_store = [];\n        this.set_mode(MODE.BlockStatement);\n        var tokenizer = new Tokenizer(source_text, this._options);\n        this._tokens = tokenizer.tokenize();\n        return source_text;\n      };\n      Beautifier.prototype.beautify = function () {\n        // if disabled, return the input unchanged.\n        if (this._options.disabled) {\n          return this._source_text;\n        }\n        var sweet_code;\n        var source_text = this._reset(this._source_text);\n        var eol = this._options.eol;\n        if (this._options.eol === 'auto') {\n          eol = '\\n';\n          if (source_text && acorn.lineBreak.test(source_text || '')) {\n            eol = source_text.match(acorn.lineBreak)[0];\n          }\n        }\n        var current_token = this._tokens.next();\n        while (current_token) {\n          this.handle_token(current_token);\n          this._last_last_text = this._flags.last_token.text;\n          this._flags.last_token = current_token;\n          current_token = this._tokens.next();\n        }\n        sweet_code = this._output.get_code(eol);\n        return sweet_code;\n      };\n      Beautifier.prototype.handle_token = function (current_token, preserve_statement_flags) {\n        if (current_token.type === TOKEN.START_EXPR) {\n          this.handle_start_expr(current_token);\n        } else if (current_token.type === TOKEN.END_EXPR) {\n          this.handle_end_expr(current_token);\n        } else if (current_token.type === TOKEN.START_BLOCK) {\n          this.handle_start_block(current_token);\n        } else if (current_token.type === TOKEN.END_BLOCK) {\n          this.handle_end_block(current_token);\n        } else if (current_token.type === TOKEN.WORD) {\n          this.handle_word(current_token);\n        } else if (current_token.type === TOKEN.RESERVED) {\n          this.handle_word(current_token);\n        } else if (current_token.type === TOKEN.SEMICOLON) {\n          this.handle_semicolon(current_token);\n        } else if (current_token.type === TOKEN.STRING) {\n          this.handle_string(current_token);\n        } else if (current_token.type === TOKEN.EQUALS) {\n          this.handle_equals(current_token);\n        } else if (current_token.type === TOKEN.OPERATOR) {\n          this.handle_operator(current_token);\n        } else if (current_token.type === TOKEN.COMMA) {\n          this.handle_comma(current_token);\n        } else if (current_token.type === TOKEN.BLOCK_COMMENT) {\n          this.handle_block_comment(current_token, preserve_statement_flags);\n        } else if (current_token.type === TOKEN.COMMENT) {\n          this.handle_comment(current_token, preserve_statement_flags);\n        } else if (current_token.type === TOKEN.DOT) {\n          this.handle_dot(current_token);\n        } else if (current_token.type === TOKEN.EOF) {\n          this.handle_eof(current_token);\n        } else if (current_token.type === TOKEN.UNKNOWN) {\n          this.handle_unknown(current_token, preserve_statement_flags);\n        } else {\n          this.handle_unknown(current_token, preserve_statement_flags);\n        }\n      };\n      Beautifier.prototype.handle_whitespace_and_comments = function (current_token, preserve_statement_flags) {\n        var newlines = current_token.newlines;\n        var keep_whitespace = this._options.keep_array_indentation && is_array(this._flags.mode);\n        if (current_token.comments_before) {\n          var comment_token = current_token.comments_before.next();\n          while (comment_token) {\n            // The cleanest handling of inline comments is to treat them as though they aren't there.\n            // Just continue formatting and the behavior should be logical.\n            // Also ignore unknown tokens.  Again, this should result in better behavior.\n            this.handle_whitespace_and_comments(comment_token, preserve_statement_flags);\n            this.handle_token(comment_token, preserve_statement_flags);\n            comment_token = current_token.comments_before.next();\n          }\n        }\n        if (keep_whitespace) {\n          for (var i = 0; i < newlines; i += 1) {\n            this.print_newline(i > 0, preserve_statement_flags);\n          }\n        } else {\n          if (this._options.max_preserve_newlines && newlines > this._options.max_preserve_newlines) {\n            newlines = this._options.max_preserve_newlines;\n          }\n          if (this._options.preserve_newlines) {\n            if (newlines > 1) {\n              this.print_newline(false, preserve_statement_flags);\n              for (var j = 1; j < newlines; j += 1) {\n                this.print_newline(true, preserve_statement_flags);\n              }\n            }\n          }\n        }\n      };\n      var newline_restricted_tokens = ['async', 'break', 'continue', 'return', 'throw', 'yield'];\n      Beautifier.prototype.allow_wrap_or_preserved_newline = function (current_token, force_linewrap) {\n        force_linewrap = force_linewrap === undefined ? false : force_linewrap;\n\n        // Never wrap the first token on a line\n        if (this._output.just_added_newline()) {\n          return;\n        }\n        var shouldPreserveOrForce = this._options.preserve_newlines && current_token.newlines || force_linewrap;\n        var operatorLogicApplies = in_array(this._flags.last_token.text, positionable_operators) || in_array(current_token.text, positionable_operators);\n        if (operatorLogicApplies) {\n          var shouldPrintOperatorNewline = in_array(this._flags.last_token.text, positionable_operators) && in_array(this._options.operator_position, OPERATOR_POSITION_BEFORE_OR_PRESERVE) || in_array(current_token.text, positionable_operators);\n          shouldPreserveOrForce = shouldPreserveOrForce && shouldPrintOperatorNewline;\n        }\n        if (shouldPreserveOrForce) {\n          this.print_newline(false, true);\n        } else if (this._options.wrap_line_length) {\n          if (reserved_array(this._flags.last_token, newline_restricted_tokens)) {\n            // These tokens should never have a newline inserted\n            // between them and the following expression.\n            return;\n          }\n          this._output.set_wrap_point();\n        }\n      };\n      Beautifier.prototype.print_newline = function (force_newline, preserve_statement_flags) {\n        if (!preserve_statement_flags) {\n          if (this._flags.last_token.text !== ';' && this._flags.last_token.text !== ',' && this._flags.last_token.text !== '=' && (this._flags.last_token.type !== TOKEN.OPERATOR || this._flags.last_token.text === '--' || this._flags.last_token.text === '++')) {\n            var next_token = this._tokens.peek();\n            while (this._flags.mode === MODE.Statement && !(this._flags.if_block && reserved_word(next_token, 'else')) && !this._flags.do_block) {\n              this.restore_mode();\n            }\n          }\n        }\n        if (this._output.add_new_line(force_newline)) {\n          this._flags.multiline_frame = true;\n        }\n      };\n      Beautifier.prototype.print_token_line_indentation = function (current_token) {\n        if (this._output.just_added_newline()) {\n          if (this._options.keep_array_indentation && current_token.newlines && (current_token.text === '[' || is_array(this._flags.mode))) {\n            this._output.current_line.set_indent(-1);\n            this._output.current_line.push(current_token.whitespace_before);\n            this._output.space_before_token = false;\n          } else if (this._output.set_indent(this._flags.indentation_level, this._flags.alignment)) {\n            this._flags.line_indent_level = this._flags.indentation_level;\n          }\n        }\n      };\n      Beautifier.prototype.print_token = function (current_token) {\n        if (this._output.raw) {\n          this._output.add_raw_token(current_token);\n          return;\n        }\n        if (this._options.comma_first && current_token.previous && current_token.previous.type === TOKEN.COMMA && this._output.just_added_newline()) {\n          if (this._output.previous_line.last() === ',') {\n            var popped = this._output.previous_line.pop();\n            // if the comma was already at the start of the line,\n            // pull back onto that line and reprint the indentation\n            if (this._output.previous_line.is_empty()) {\n              this._output.previous_line.push(popped);\n              this._output.trim(true);\n              this._output.current_line.pop();\n              this._output.trim();\n            }\n\n            // add the comma in front of the next token\n            this.print_token_line_indentation(current_token);\n            this._output.add_token(',');\n            this._output.space_before_token = true;\n          }\n        }\n        this.print_token_line_indentation(current_token);\n        this._output.non_breaking_space = true;\n        this._output.add_token(current_token.text);\n        if (this._output.previous_token_wrapped) {\n          this._flags.multiline_frame = true;\n        }\n      };\n      Beautifier.prototype.indent = function () {\n        this._flags.indentation_level += 1;\n        this._output.set_indent(this._flags.indentation_level, this._flags.alignment);\n      };\n      Beautifier.prototype.deindent = function () {\n        if (this._flags.indentation_level > 0 && (!this._flags.parent || this._flags.indentation_level > this._flags.parent.indentation_level)) {\n          this._flags.indentation_level -= 1;\n          this._output.set_indent(this._flags.indentation_level, this._flags.alignment);\n        }\n      };\n      Beautifier.prototype.set_mode = function (mode) {\n        if (this._flags) {\n          this._flag_store.push(this._flags);\n          this._previous_flags = this._flags;\n        } else {\n          this._previous_flags = this.create_flags(null, mode);\n        }\n        this._flags = this.create_flags(this._previous_flags, mode);\n        this._output.set_indent(this._flags.indentation_level, this._flags.alignment);\n      };\n      Beautifier.prototype.restore_mode = function () {\n        if (this._flag_store.length > 0) {\n          this._previous_flags = this._flags;\n          this._flags = this._flag_store.pop();\n          if (this._previous_flags.mode === MODE.Statement) {\n            remove_redundant_indentation(this._output, this._previous_flags);\n          }\n          this._output.set_indent(this._flags.indentation_level, this._flags.alignment);\n        }\n      };\n      Beautifier.prototype.start_of_object_property = function () {\n        return this._flags.parent.mode === MODE.ObjectLiteral && this._flags.mode === MODE.Statement && (this._flags.last_token.text === ':' && this._flags.ternary_depth === 0 || reserved_array(this._flags.last_token, ['get', 'set']));\n      };\n      Beautifier.prototype.start_of_statement = function (current_token) {\n        var start = false;\n        start = start || reserved_array(this._flags.last_token, ['var', 'let', 'const']) && current_token.type === TOKEN.WORD;\n        start = start || reserved_word(this._flags.last_token, 'do');\n        start = start || !(this._flags.parent.mode === MODE.ObjectLiteral && this._flags.mode === MODE.Statement) && reserved_array(this._flags.last_token, newline_restricted_tokens) && !current_token.newlines;\n        start = start || reserved_word(this._flags.last_token, 'else') && !(reserved_word(current_token, 'if') && !current_token.comments_before);\n        start = start || this._flags.last_token.type === TOKEN.END_EXPR && (this._previous_flags.mode === MODE.ForInitializer || this._previous_flags.mode === MODE.Conditional);\n        start = start || this._flags.last_token.type === TOKEN.WORD && this._flags.mode === MODE.BlockStatement && !this._flags.in_case && !(current_token.text === '--' || current_token.text === '++') && this._last_last_text !== 'function' && current_token.type !== TOKEN.WORD && current_token.type !== TOKEN.RESERVED;\n        start = start || this._flags.mode === MODE.ObjectLiteral && (this._flags.last_token.text === ':' && this._flags.ternary_depth === 0 || reserved_array(this._flags.last_token, ['get', 'set']));\n        if (start) {\n          this.set_mode(MODE.Statement);\n          this.indent();\n          this.handle_whitespace_and_comments(current_token, true);\n\n          // Issue #276:\n          // If starting a new statement with [if, for, while, do], push to a new line.\n          // if (a) if (b) if(c) d(); else e(); else f();\n          if (!this.start_of_object_property()) {\n            this.allow_wrap_or_preserved_newline(current_token, reserved_array(current_token, ['do', 'for', 'if', 'while']));\n          }\n          return true;\n        }\n        return false;\n      };\n      Beautifier.prototype.handle_start_expr = function (current_token) {\n        // The conditional starts the statement if appropriate.\n        if (!this.start_of_statement(current_token)) {\n          this.handle_whitespace_and_comments(current_token);\n        }\n        var next_mode = MODE.Expression;\n        if (current_token.text === '[') {\n          if (this._flags.last_token.type === TOKEN.WORD || this._flags.last_token.text === ')') {\n            // this is array index specifier, break immediately\n            // a[x], fn()[x]\n            if (reserved_array(this._flags.last_token, line_starters)) {\n              this._output.space_before_token = true;\n            }\n            this.print_token(current_token);\n            this.set_mode(next_mode);\n            this.indent();\n            if (this._options.space_in_paren) {\n              this._output.space_before_token = true;\n            }\n            return;\n          }\n          next_mode = MODE.ArrayLiteral;\n          if (is_array(this._flags.mode)) {\n            if (this._flags.last_token.text === '[' || this._flags.last_token.text === ',' && (this._last_last_text === ']' || this._last_last_text === '}')) {\n              // ], [ goes to new line\n              // }, [ goes to new line\n              if (!this._options.keep_array_indentation) {\n                this.print_newline();\n              }\n            }\n          }\n          if (!in_array(this._flags.last_token.type, [TOKEN.START_EXPR, TOKEN.END_EXPR, TOKEN.WORD, TOKEN.OPERATOR, TOKEN.DOT])) {\n            this._output.space_before_token = true;\n          }\n        } else {\n          if (this._flags.last_token.type === TOKEN.RESERVED) {\n            if (this._flags.last_token.text === 'for') {\n              this._output.space_before_token = this._options.space_before_conditional;\n              next_mode = MODE.ForInitializer;\n            } else if (in_array(this._flags.last_token.text, ['if', 'while', 'switch'])) {\n              this._output.space_before_token = this._options.space_before_conditional;\n              next_mode = MODE.Conditional;\n            } else if (in_array(this._flags.last_word, ['await', 'async'])) {\n              // Should be a space between await and an IIFE, or async and an arrow function\n              this._output.space_before_token = true;\n            } else if (this._flags.last_token.text === 'import' && current_token.whitespace_before === '') {\n              this._output.space_before_token = false;\n            } else if (in_array(this._flags.last_token.text, line_starters) || this._flags.last_token.text === 'catch') {\n              this._output.space_before_token = true;\n            }\n          } else if (this._flags.last_token.type === TOKEN.EQUALS || this._flags.last_token.type === TOKEN.OPERATOR) {\n            // Support of this kind of newline preservation.\n            // a = (b &&\n            //     (c || d));\n            if (!this.start_of_object_property()) {\n              this.allow_wrap_or_preserved_newline(current_token);\n            }\n          } else if (this._flags.last_token.type === TOKEN.WORD) {\n            this._output.space_before_token = false;\n\n            // function name() vs function name ()\n            // function* name() vs function* name ()\n            // async name() vs async name ()\n            // In ES6, you can also define the method properties of an object\n            // var obj = {a: function() {}}\n            // It can be abbreviated\n            // var obj = {a() {}}\n            // var obj = { a() {}} vs var obj = { a () {}}\n            // var obj = { * a() {}} vs var obj = { * a () {}}\n            var peek_back_two = this._tokens.peek(-3);\n            if (this._options.space_after_named_function && peek_back_two) {\n              // peek starts at next character so -1 is current token\n              var peek_back_three = this._tokens.peek(-4);\n              if (reserved_array(peek_back_two, ['async', 'function']) || peek_back_two.text === '*' && reserved_array(peek_back_three, ['async', 'function'])) {\n                this._output.space_before_token = true;\n              } else if (this._flags.mode === MODE.ObjectLiteral) {\n                if (peek_back_two.text === '{' || peek_back_two.text === ',' || peek_back_two.text === '*' && (peek_back_three.text === '{' || peek_back_three.text === ',')) {\n                  this._output.space_before_token = true;\n                }\n              } else if (this._flags.parent && this._flags.parent.class_start_block) {\n                this._output.space_before_token = true;\n              }\n            }\n          } else {\n            // Support preserving wrapped arrow function expressions\n            // a.b('c',\n            //     () => d.e\n            // )\n            this.allow_wrap_or_preserved_newline(current_token);\n          }\n\n          // function() vs function ()\n          // yield*() vs yield* ()\n          // function*() vs function* ()\n          if (this._flags.last_token.type === TOKEN.RESERVED && (this._flags.last_word === 'function' || this._flags.last_word === 'typeof') || this._flags.last_token.text === '*' && (in_array(this._last_last_text, ['function', 'yield']) || this._flags.mode === MODE.ObjectLiteral && in_array(this._last_last_text, ['{', ',']))) {\n            this._output.space_before_token = this._options.space_after_anon_function;\n          }\n        }\n        if (this._flags.last_token.text === ';' || this._flags.last_token.type === TOKEN.START_BLOCK) {\n          this.print_newline();\n        } else if (this._flags.last_token.type === TOKEN.END_EXPR || this._flags.last_token.type === TOKEN.START_EXPR || this._flags.last_token.type === TOKEN.END_BLOCK || this._flags.last_token.text === '.' || this._flags.last_token.type === TOKEN.COMMA) {\n          // do nothing on (( and )( and ][ and ]( and .(\n          // TODO: Consider whether forcing this is required.  Review failing tests when removed.\n          this.allow_wrap_or_preserved_newline(current_token, current_token.newlines);\n        }\n        this.print_token(current_token);\n        this.set_mode(next_mode);\n        if (this._options.space_in_paren) {\n          this._output.space_before_token = true;\n        }\n\n        // In all cases, if we newline while inside an expression it should be indented.\n        this.indent();\n      };\n      Beautifier.prototype.handle_end_expr = function (current_token) {\n        // statements inside expressions are not valid syntax, but...\n        // statements must all be closed when their container closes\n        while (this._flags.mode === MODE.Statement) {\n          this.restore_mode();\n        }\n        this.handle_whitespace_and_comments(current_token);\n        if (this._flags.multiline_frame) {\n          this.allow_wrap_or_preserved_newline(current_token, current_token.text === ']' && is_array(this._flags.mode) && !this._options.keep_array_indentation);\n        }\n        if (this._options.space_in_paren) {\n          if (this._flags.last_token.type === TOKEN.START_EXPR && !this._options.space_in_empty_paren) {\n            // () [] no inner space in empty parens like these, ever, ref #320\n            this._output.trim();\n            this._output.space_before_token = false;\n          } else {\n            this._output.space_before_token = true;\n          }\n        }\n        this.deindent();\n        this.print_token(current_token);\n        this.restore_mode();\n        remove_redundant_indentation(this._output, this._previous_flags);\n\n        // do {} while () // no statement required after\n        if (this._flags.do_while && this._previous_flags.mode === MODE.Conditional) {\n          this._previous_flags.mode = MODE.Expression;\n          this._flags.do_block = false;\n          this._flags.do_while = false;\n        }\n      };\n      Beautifier.prototype.handle_start_block = function (current_token) {\n        this.handle_whitespace_and_comments(current_token);\n\n        // Check if this is should be treated as a ObjectLiteral\n        var next_token = this._tokens.peek();\n        var second_token = this._tokens.peek(1);\n        if (this._flags.last_word === 'switch' && this._flags.last_token.type === TOKEN.END_EXPR) {\n          this.set_mode(MODE.BlockStatement);\n          this._flags.in_case_statement = true;\n        } else if (this._flags.case_body) {\n          this.set_mode(MODE.BlockStatement);\n        } else if (second_token && (in_array(second_token.text, [':', ',']) && in_array(next_token.type, [TOKEN.STRING, TOKEN.WORD, TOKEN.RESERVED]) || in_array(next_token.text, ['get', 'set', '...']) && in_array(second_token.type, [TOKEN.WORD, TOKEN.RESERVED]))) {\n          // We don't support TypeScript,but we didn't break it for a very long time.\n          // We'll try to keep not breaking it.\n          if (in_array(this._last_last_text, ['class', 'interface']) && !in_array(second_token.text, [':', ','])) {\n            this.set_mode(MODE.BlockStatement);\n          } else {\n            this.set_mode(MODE.ObjectLiteral);\n          }\n        } else if (this._flags.last_token.type === TOKEN.OPERATOR && this._flags.last_token.text === '=>') {\n          // arrow function: (param1, paramN) => { statements }\n          this.set_mode(MODE.BlockStatement);\n        } else if (in_array(this._flags.last_token.type, [TOKEN.EQUALS, TOKEN.START_EXPR, TOKEN.COMMA, TOKEN.OPERATOR]) || reserved_array(this._flags.last_token, ['return', 'throw', 'import', 'default'])) {\n          // Detecting shorthand function syntax is difficult by scanning forward,\n          //     so check the surrounding context.\n          // If the block is being returned, imported, export default, passed as arg,\n          //     assigned with = or assigned in a nested object, treat as an ObjectLiteral.\n          this.set_mode(MODE.ObjectLiteral);\n        } else {\n          this.set_mode(MODE.BlockStatement);\n        }\n        if (this._flags.last_token) {\n          if (reserved_array(this._flags.last_token.previous, ['class', 'extends'])) {\n            this._flags.class_start_block = true;\n          }\n        }\n        var empty_braces = !next_token.comments_before && next_token.text === '}';\n        var empty_anonymous_function = empty_braces && this._flags.last_word === 'function' && this._flags.last_token.type === TOKEN.END_EXPR;\n        if (this._options.brace_preserve_inline)\n          // check for inline, set inline_frame if so\n          {\n            // search forward for a newline wanted inside this block\n            var index = 0;\n            var check_token = null;\n            this._flags.inline_frame = true;\n            do {\n              index += 1;\n              check_token = this._tokens.peek(index - 1);\n              if (check_token.newlines) {\n                this._flags.inline_frame = false;\n                break;\n              }\n            } while (check_token.type !== TOKEN.EOF && !(check_token.type === TOKEN.END_BLOCK && check_token.opened === current_token));\n          }\n        if ((this._options.brace_style === \"expand\" || this._options.brace_style === \"none\" && current_token.newlines) && !this._flags.inline_frame) {\n          if (this._flags.last_token.type !== TOKEN.OPERATOR && (empty_anonymous_function || this._flags.last_token.type === TOKEN.EQUALS || reserved_array(this._flags.last_token, special_words) && this._flags.last_token.text !== 'else')) {\n            this._output.space_before_token = true;\n          } else {\n            this.print_newline(false, true);\n          }\n        } else {\n          // collapse || inline_frame\n          if (is_array(this._previous_flags.mode) && (this._flags.last_token.type === TOKEN.START_EXPR || this._flags.last_token.type === TOKEN.COMMA)) {\n            if (this._flags.last_token.type === TOKEN.COMMA || this._options.space_in_paren) {\n              this._output.space_before_token = true;\n            }\n            if (this._flags.last_token.type === TOKEN.COMMA || this._flags.last_token.type === TOKEN.START_EXPR && this._flags.inline_frame) {\n              this.allow_wrap_or_preserved_newline(current_token);\n              this._previous_flags.multiline_frame = this._previous_flags.multiline_frame || this._flags.multiline_frame;\n              this._flags.multiline_frame = false;\n            }\n          }\n          if (this._flags.last_token.type !== TOKEN.OPERATOR && this._flags.last_token.type !== TOKEN.START_EXPR) {\n            if (in_array(this._flags.last_token.type, [TOKEN.START_BLOCK, TOKEN.SEMICOLON]) && !this._flags.inline_frame) {\n              this.print_newline();\n            } else {\n              this._output.space_before_token = true;\n            }\n          }\n        }\n        this.print_token(current_token);\n        this.indent();\n\n        // Except for specific cases, open braces are followed by a new line.\n        if (!empty_braces && !(this._options.brace_preserve_inline && this._flags.inline_frame)) {\n          this.print_newline();\n        }\n      };\n      Beautifier.prototype.handle_end_block = function (current_token) {\n        // statements must all be closed when their container closes\n        this.handle_whitespace_and_comments(current_token);\n        while (this._flags.mode === MODE.Statement) {\n          this.restore_mode();\n        }\n        var empty_braces = this._flags.last_token.type === TOKEN.START_BLOCK;\n        if (this._flags.inline_frame && !empty_braces) {\n          // try inline_frame (only set if this._options.braces-preserve-inline) first\n          this._output.space_before_token = true;\n        } else if (this._options.brace_style === \"expand\") {\n          if (!empty_braces) {\n            this.print_newline();\n          }\n        } else {\n          // skip {}\n          if (!empty_braces) {\n            if (is_array(this._flags.mode) && this._options.keep_array_indentation) {\n              // we REALLY need a newline here, but newliner would skip that\n              this._options.keep_array_indentation = false;\n              this.print_newline();\n              this._options.keep_array_indentation = true;\n            } else {\n              this.print_newline();\n            }\n          }\n        }\n        this.restore_mode();\n        this.print_token(current_token);\n      };\n      Beautifier.prototype.handle_word = function (current_token) {\n        if (current_token.type === TOKEN.RESERVED) {\n          if (in_array(current_token.text, ['set', 'get']) && this._flags.mode !== MODE.ObjectLiteral) {\n            current_token.type = TOKEN.WORD;\n          } else if (current_token.text === 'import' && in_array(this._tokens.peek().text, ['(', '.'])) {\n            current_token.type = TOKEN.WORD;\n          } else if (in_array(current_token.text, ['as', 'from']) && !this._flags.import_block) {\n            current_token.type = TOKEN.WORD;\n          } else if (this._flags.mode === MODE.ObjectLiteral) {\n            var next_token = this._tokens.peek();\n            if (next_token.text === ':') {\n              current_token.type = TOKEN.WORD;\n            }\n          }\n        }\n        if (this.start_of_statement(current_token)) {\n          // The conditional starts the statement if appropriate.\n          if (reserved_array(this._flags.last_token, ['var', 'let', 'const']) && current_token.type === TOKEN.WORD) {\n            this._flags.declaration_statement = true;\n          }\n        } else if (current_token.newlines && !is_expression(this._flags.mode) && (this._flags.last_token.type !== TOKEN.OPERATOR || this._flags.last_token.text === '--' || this._flags.last_token.text === '++') && this._flags.last_token.type !== TOKEN.EQUALS && (this._options.preserve_newlines || !reserved_array(this._flags.last_token, ['var', 'let', 'const', 'set', 'get']))) {\n          this.handle_whitespace_and_comments(current_token);\n          this.print_newline();\n        } else {\n          this.handle_whitespace_and_comments(current_token);\n        }\n        if (this._flags.do_block && !this._flags.do_while) {\n          if (reserved_word(current_token, 'while')) {\n            // do {} ## while ()\n            this._output.space_before_token = true;\n            this.print_token(current_token);\n            this._output.space_before_token = true;\n            this._flags.do_while = true;\n            return;\n          } else {\n            // do {} should always have while as the next word.\n            // if we don't see the expected while, recover\n            this.print_newline();\n            this._flags.do_block = false;\n          }\n        }\n\n        // if may be followed by else, or not\n        // Bare/inline ifs are tricky\n        // Need to unwind the modes correctly: if (a) if (b) c(); else d(); else e();\n        if (this._flags.if_block) {\n          if (!this._flags.else_block && reserved_word(current_token, 'else')) {\n            this._flags.else_block = true;\n          } else {\n            while (this._flags.mode === MODE.Statement) {\n              this.restore_mode();\n            }\n            this._flags.if_block = false;\n            this._flags.else_block = false;\n          }\n        }\n        if (this._flags.in_case_statement && reserved_array(current_token, ['case', 'default'])) {\n          this.print_newline();\n          if (!this._flags.case_block && (this._flags.case_body || this._options.jslint_happy)) {\n            // switch cases following one another\n            this.deindent();\n          }\n          this._flags.case_body = false;\n          this.print_token(current_token);\n          this._flags.in_case = true;\n          return;\n        }\n        if (this._flags.last_token.type === TOKEN.COMMA || this._flags.last_token.type === TOKEN.START_EXPR || this._flags.last_token.type === TOKEN.EQUALS || this._flags.last_token.type === TOKEN.OPERATOR) {\n          if (!this.start_of_object_property() && !(\n          // start of object property is different for numeric values with +/- prefix operators\n          in_array(this._flags.last_token.text, ['+', '-']) && this._last_last_text === ':' && this._flags.parent.mode === MODE.ObjectLiteral)) {\n            this.allow_wrap_or_preserved_newline(current_token);\n          }\n        }\n        if (reserved_word(current_token, 'function')) {\n          if (in_array(this._flags.last_token.text, ['}', ';']) || this._output.just_added_newline() && !(in_array(this._flags.last_token.text, ['(', '[', '{', ':', '=', ',']) || this._flags.last_token.type === TOKEN.OPERATOR)) {\n            // make sure there is a nice clean space of at least one blank line\n            // before a new function definition\n            if (!this._output.just_added_blankline() && !current_token.comments_before) {\n              this.print_newline();\n              this.print_newline(true);\n            }\n          }\n          if (this._flags.last_token.type === TOKEN.RESERVED || this._flags.last_token.type === TOKEN.WORD) {\n            if (reserved_array(this._flags.last_token, ['get', 'set', 'new', 'export']) || reserved_array(this._flags.last_token, newline_restricted_tokens)) {\n              this._output.space_before_token = true;\n            } else if (reserved_word(this._flags.last_token, 'default') && this._last_last_text === 'export') {\n              this._output.space_before_token = true;\n            } else if (this._flags.last_token.text === 'declare') {\n              // accomodates Typescript declare function formatting\n              this._output.space_before_token = true;\n            } else {\n              this.print_newline();\n            }\n          } else if (this._flags.last_token.type === TOKEN.OPERATOR || this._flags.last_token.text === '=') {\n            // foo = function\n            this._output.space_before_token = true;\n          } else if (!this._flags.multiline_frame && (is_expression(this._flags.mode) || is_array(this._flags.mode))) {\n            // (function\n          } else {\n            this.print_newline();\n          }\n          this.print_token(current_token);\n          this._flags.last_word = current_token.text;\n          return;\n        }\n        var prefix = 'NONE';\n        if (this._flags.last_token.type === TOKEN.END_BLOCK) {\n          if (this._previous_flags.inline_frame) {\n            prefix = 'SPACE';\n          } else if (!reserved_array(current_token, ['else', 'catch', 'finally', 'from'])) {\n            prefix = 'NEWLINE';\n          } else {\n            if (this._options.brace_style === \"expand\" || this._options.brace_style === \"end-expand\" || this._options.brace_style === \"none\" && current_token.newlines) {\n              prefix = 'NEWLINE';\n            } else {\n              prefix = 'SPACE';\n              this._output.space_before_token = true;\n            }\n          }\n        } else if (this._flags.last_token.type === TOKEN.SEMICOLON && this._flags.mode === MODE.BlockStatement) {\n          // TODO: Should this be for STATEMENT as well?\n          prefix = 'NEWLINE';\n        } else if (this._flags.last_token.type === TOKEN.SEMICOLON && is_expression(this._flags.mode)) {\n          prefix = 'SPACE';\n        } else if (this._flags.last_token.type === TOKEN.STRING) {\n          prefix = 'NEWLINE';\n        } else if (this._flags.last_token.type === TOKEN.RESERVED || this._flags.last_token.type === TOKEN.WORD || this._flags.last_token.text === '*' && (in_array(this._last_last_text, ['function', 'yield']) || this._flags.mode === MODE.ObjectLiteral && in_array(this._last_last_text, ['{', ',']))) {\n          prefix = 'SPACE';\n        } else if (this._flags.last_token.type === TOKEN.START_BLOCK) {\n          if (this._flags.inline_frame) {\n            prefix = 'SPACE';\n          } else {\n            prefix = 'NEWLINE';\n          }\n        } else if (this._flags.last_token.type === TOKEN.END_EXPR) {\n          this._output.space_before_token = true;\n          prefix = 'NEWLINE';\n        }\n        if (reserved_array(current_token, line_starters) && this._flags.last_token.text !== ')') {\n          if (this._flags.inline_frame || this._flags.last_token.text === 'else' || this._flags.last_token.text === 'export') {\n            prefix = 'SPACE';\n          } else {\n            prefix = 'NEWLINE';\n          }\n        }\n        if (reserved_array(current_token, ['else', 'catch', 'finally'])) {\n          if ((!(this._flags.last_token.type === TOKEN.END_BLOCK && this._previous_flags.mode === MODE.BlockStatement) || this._options.brace_style === \"expand\" || this._options.brace_style === \"end-expand\" || this._options.brace_style === \"none\" && current_token.newlines) && !this._flags.inline_frame) {\n            this.print_newline();\n          } else {\n            this._output.trim(true);\n            var line = this._output.current_line;\n            // If we trimmed and there's something other than a close block before us\n            // put a newline back in.  Handles '} // comment' scenario.\n            if (line.last() !== '}') {\n              this.print_newline();\n            }\n            this._output.space_before_token = true;\n          }\n        } else if (prefix === 'NEWLINE') {\n          if (reserved_array(this._flags.last_token, special_words)) {\n            // no newline between 'return nnn'\n            this._output.space_before_token = true;\n          } else if (this._flags.last_token.text === 'declare' && reserved_array(current_token, ['var', 'let', 'const'])) {\n            // accomodates Typescript declare formatting\n            this._output.space_before_token = true;\n          } else if (this._flags.last_token.type !== TOKEN.END_EXPR) {\n            if ((this._flags.last_token.type !== TOKEN.START_EXPR || !reserved_array(current_token, ['var', 'let', 'const'])) && this._flags.last_token.text !== ':') {\n              // no need to force newline on 'var': for (var x = 0...)\n              if (reserved_word(current_token, 'if') && reserved_word(current_token.previous, 'else')) {\n                // no newline for } else if {\n                this._output.space_before_token = true;\n              } else {\n                this.print_newline();\n              }\n            }\n          } else if (reserved_array(current_token, line_starters) && this._flags.last_token.text !== ')') {\n            this.print_newline();\n          }\n        } else if (this._flags.multiline_frame && is_array(this._flags.mode) && this._flags.last_token.text === ',' && this._last_last_text === '}') {\n          this.print_newline(); // }, in lists get a newline treatment\n        } else if (prefix === 'SPACE') {\n          this._output.space_before_token = true;\n        }\n        if (current_token.previous && (current_token.previous.type === TOKEN.WORD || current_token.previous.type === TOKEN.RESERVED)) {\n          this._output.space_before_token = true;\n        }\n        this.print_token(current_token);\n        this._flags.last_word = current_token.text;\n        if (current_token.type === TOKEN.RESERVED) {\n          if (current_token.text === 'do') {\n            this._flags.do_block = true;\n          } else if (current_token.text === 'if') {\n            this._flags.if_block = true;\n          } else if (current_token.text === 'import') {\n            this._flags.import_block = true;\n          } else if (this._flags.import_block && reserved_word(current_token, 'from')) {\n            this._flags.import_block = false;\n          }\n        }\n      };\n      Beautifier.prototype.handle_semicolon = function (current_token) {\n        if (this.start_of_statement(current_token)) {\n          // The conditional starts the statement if appropriate.\n          // Semicolon can be the start (and end) of a statement\n          this._output.space_before_token = false;\n        } else {\n          this.handle_whitespace_and_comments(current_token);\n        }\n        var next_token = this._tokens.peek();\n        while (this._flags.mode === MODE.Statement && !(this._flags.if_block && reserved_word(next_token, 'else')) && !this._flags.do_block) {\n          this.restore_mode();\n        }\n\n        // hacky but effective for the moment\n        if (this._flags.import_block) {\n          this._flags.import_block = false;\n        }\n        this.print_token(current_token);\n      };\n      Beautifier.prototype.handle_string = function (current_token) {\n        if (current_token.text.startsWith(\"`\") && current_token.newlines === 0 && current_token.whitespace_before === '' && (current_token.previous.text === ')' || this._flags.last_token.type === TOKEN.WORD)) {\n          //Conditional for detectign backtick strings\n        } else if (this.start_of_statement(current_token)) {\n          // The conditional starts the statement if appropriate.\n          // One difference - strings want at least a space before\n          this._output.space_before_token = true;\n        } else {\n          this.handle_whitespace_and_comments(current_token);\n          if (this._flags.last_token.type === TOKEN.RESERVED || this._flags.last_token.type === TOKEN.WORD || this._flags.inline_frame) {\n            this._output.space_before_token = true;\n          } else if (this._flags.last_token.type === TOKEN.COMMA || this._flags.last_token.type === TOKEN.START_EXPR || this._flags.last_token.type === TOKEN.EQUALS || this._flags.last_token.type === TOKEN.OPERATOR) {\n            if (!this.start_of_object_property()) {\n              this.allow_wrap_or_preserved_newline(current_token);\n            }\n          } else if (current_token.text.startsWith(\"`\") && this._flags.last_token.type === TOKEN.END_EXPR && (current_token.previous.text === ']' || current_token.previous.text === ')') && current_token.newlines === 0) {\n            this._output.space_before_token = true;\n          } else {\n            this.print_newline();\n          }\n        }\n        this.print_token(current_token);\n      };\n      Beautifier.prototype.handle_equals = function (current_token) {\n        if (this.start_of_statement(current_token)) {\n          // The conditional starts the statement if appropriate.\n        } else {\n          this.handle_whitespace_and_comments(current_token);\n        }\n        if (this._flags.declaration_statement) {\n          // just got an '=' in a var-line, different formatting/line-breaking, etc will now be done\n          this._flags.declaration_assignment = true;\n        }\n        this._output.space_before_token = true;\n        this.print_token(current_token);\n        this._output.space_before_token = true;\n      };\n      Beautifier.prototype.handle_comma = function (current_token) {\n        this.handle_whitespace_and_comments(current_token, true);\n        this.print_token(current_token);\n        this._output.space_before_token = true;\n        if (this._flags.declaration_statement) {\n          if (is_expression(this._flags.parent.mode)) {\n            // do not break on comma, for(var a = 1, b = 2)\n            this._flags.declaration_assignment = false;\n          }\n          if (this._flags.declaration_assignment) {\n            this._flags.declaration_assignment = false;\n            this.print_newline(false, true);\n          } else if (this._options.comma_first) {\n            // for comma-first, we want to allow a newline before the comma\n            // to turn into a newline after the comma, which we will fixup later\n            this.allow_wrap_or_preserved_newline(current_token);\n          }\n        } else if (this._flags.mode === MODE.ObjectLiteral || this._flags.mode === MODE.Statement && this._flags.parent.mode === MODE.ObjectLiteral) {\n          if (this._flags.mode === MODE.Statement) {\n            this.restore_mode();\n          }\n          if (!this._flags.inline_frame) {\n            this.print_newline();\n          }\n        } else if (this._options.comma_first) {\n          // EXPR or DO_BLOCK\n          // for comma-first, we want to allow a newline before the comma\n          // to turn into a newline after the comma, which we will fixup later\n          this.allow_wrap_or_preserved_newline(current_token);\n        }\n      };\n      Beautifier.prototype.handle_operator = function (current_token) {\n        var isGeneratorAsterisk = current_token.text === '*' && (reserved_array(this._flags.last_token, ['function', 'yield']) || in_array(this._flags.last_token.type, [TOKEN.START_BLOCK, TOKEN.COMMA, TOKEN.END_BLOCK, TOKEN.SEMICOLON]));\n        var isUnary = in_array(current_token.text, ['-', '+']) && (in_array(this._flags.last_token.type, [TOKEN.START_BLOCK, TOKEN.START_EXPR, TOKEN.EQUALS, TOKEN.OPERATOR]) || in_array(this._flags.last_token.text, line_starters) || this._flags.last_token.text === ',');\n        if (this.start_of_statement(current_token)) {\n          // The conditional starts the statement if appropriate.\n        } else {\n          var preserve_statement_flags = !isGeneratorAsterisk;\n          this.handle_whitespace_and_comments(current_token, preserve_statement_flags);\n        }\n\n        // hack for actionscript's import .*;\n        if (current_token.text === '*' && this._flags.last_token.type === TOKEN.DOT) {\n          this.print_token(current_token);\n          return;\n        }\n        if (current_token.text === '::') {\n          // no spaces around exotic namespacing syntax operator\n          this.print_token(current_token);\n          return;\n        }\n        if (in_array(current_token.text, ['-', '+']) && this.start_of_object_property()) {\n          // numeric value with +/- symbol in front as a property\n          this.print_token(current_token);\n          return;\n        }\n\n        // Allow line wrapping between operators when operator_position is\n        //   set to before or preserve\n        if (this._flags.last_token.type === TOKEN.OPERATOR && in_array(this._options.operator_position, OPERATOR_POSITION_BEFORE_OR_PRESERVE)) {\n          this.allow_wrap_or_preserved_newline(current_token);\n        }\n        if (current_token.text === ':' && this._flags.in_case) {\n          this.print_token(current_token);\n          this._flags.in_case = false;\n          this._flags.case_body = true;\n          if (this._tokens.peek().type !== TOKEN.START_BLOCK) {\n            this.indent();\n            this.print_newline();\n            this._flags.case_block = false;\n          } else {\n            this._flags.case_block = true;\n            this._output.space_before_token = true;\n          }\n          return;\n        }\n        var space_before = true;\n        var space_after = true;\n        var in_ternary = false;\n        if (current_token.text === ':') {\n          if (this._flags.ternary_depth === 0) {\n            // Colon is invalid javascript outside of ternary and object, but do our best to guess what was meant.\n            space_before = false;\n          } else {\n            this._flags.ternary_depth -= 1;\n            in_ternary = true;\n          }\n        } else if (current_token.text === '?') {\n          this._flags.ternary_depth += 1;\n        }\n\n        // let's handle the operator_position option prior to any conflicting logic\n        if (!isUnary && !isGeneratorAsterisk && this._options.preserve_newlines && in_array(current_token.text, positionable_operators)) {\n          var isColon = current_token.text === ':';\n          var isTernaryColon = isColon && in_ternary;\n          var isOtherColon = isColon && !in_ternary;\n          switch (this._options.operator_position) {\n            case OPERATOR_POSITION.before_newline:\n              // if the current token is : and it's not a ternary statement then we set space_before to false\n              this._output.space_before_token = !isOtherColon;\n              this.print_token(current_token);\n              if (!isColon || isTernaryColon) {\n                this.allow_wrap_or_preserved_newline(current_token);\n              }\n              this._output.space_before_token = true;\n              return;\n            case OPERATOR_POSITION.after_newline:\n              // if the current token is anything but colon, or (via deduction) it's a colon and in a ternary statement,\n              //   then print a newline.\n\n              this._output.space_before_token = true;\n              if (!isColon || isTernaryColon) {\n                if (this._tokens.peek().newlines) {\n                  this.print_newline(false, true);\n                } else {\n                  this.allow_wrap_or_preserved_newline(current_token);\n                }\n              } else {\n                this._output.space_before_token = false;\n              }\n              this.print_token(current_token);\n              this._output.space_before_token = true;\n              return;\n            case OPERATOR_POSITION.preserve_newline:\n              if (!isOtherColon) {\n                this.allow_wrap_or_preserved_newline(current_token);\n              }\n\n              // if we just added a newline, or the current token is : and it's not a ternary statement,\n              //   then we set space_before to false\n              space_before = !(this._output.just_added_newline() || isOtherColon);\n              this._output.space_before_token = space_before;\n              this.print_token(current_token);\n              this._output.space_before_token = true;\n              return;\n          }\n        }\n        if (isGeneratorAsterisk) {\n          this.allow_wrap_or_preserved_newline(current_token);\n          space_before = false;\n          var next_token = this._tokens.peek();\n          space_after = next_token && in_array(next_token.type, [TOKEN.WORD, TOKEN.RESERVED]);\n        } else if (current_token.text === '...') {\n          this.allow_wrap_or_preserved_newline(current_token);\n          space_before = this._flags.last_token.type === TOKEN.START_BLOCK;\n          space_after = false;\n        } else if (in_array(current_token.text, ['--', '++', '!', '~']) || isUnary) {\n          // unary operators (and binary +/- pretending to be unary) special cases\n          if (this._flags.last_token.type === TOKEN.COMMA || this._flags.last_token.type === TOKEN.START_EXPR) {\n            this.allow_wrap_or_preserved_newline(current_token);\n          }\n          space_before = false;\n          space_after = false;\n\n          // http://www.ecma-international.org/ecma-262/5.1/#sec-7.9.1\n          // if there is a newline between -- or ++ and anything else we should preserve it.\n          if (current_token.newlines && (current_token.text === '--' || current_token.text === '++' || current_token.text === '~')) {\n            var new_line_needed = reserved_array(this._flags.last_token, special_words) && current_token.newlines;\n            if (new_line_needed && (this._previous_flags.if_block || this._previous_flags.else_block)) {\n              this.restore_mode();\n            }\n            this.print_newline(new_line_needed, true);\n          }\n          if (this._flags.last_token.text === ';' && is_expression(this._flags.mode)) {\n            // for (;; ++i)\n            //        ^^^\n            space_before = true;\n          }\n          if (this._flags.last_token.type === TOKEN.RESERVED) {\n            space_before = true;\n          } else if (this._flags.last_token.type === TOKEN.END_EXPR) {\n            space_before = !(this._flags.last_token.text === ']' && (current_token.text === '--' || current_token.text === '++'));\n          } else if (this._flags.last_token.type === TOKEN.OPERATOR) {\n            // a++ + ++b;\n            // a - -b\n            space_before = in_array(current_token.text, ['--', '-', '++', '+']) && in_array(this._flags.last_token.text, ['--', '-', '++', '+']);\n            // + and - are not unary when preceeded by -- or ++ operator\n            // a-- + b\n            // a * +b\n            // a - -b\n            if (in_array(current_token.text, ['+', '-']) && in_array(this._flags.last_token.text, ['--', '++'])) {\n              space_after = true;\n            }\n          }\n          if ((this._flags.mode === MODE.BlockStatement && !this._flags.inline_frame || this._flags.mode === MODE.Statement) && (this._flags.last_token.text === '{' || this._flags.last_token.text === ';')) {\n            // { foo; --i }\n            // foo(); --bar;\n            this.print_newline();\n          }\n        }\n        this._output.space_before_token = this._output.space_before_token || space_before;\n        this.print_token(current_token);\n        this._output.space_before_token = space_after;\n      };\n      Beautifier.prototype.handle_block_comment = function (current_token, preserve_statement_flags) {\n        if (this._output.raw) {\n          this._output.add_raw_token(current_token);\n          if (current_token.directives && current_token.directives.preserve === 'end') {\n            // If we're testing the raw output behavior, do not allow a directive to turn it off.\n            this._output.raw = this._options.test_output_raw;\n          }\n          return;\n        }\n        if (current_token.directives) {\n          this.print_newline(false, preserve_statement_flags);\n          this.print_token(current_token);\n          if (current_token.directives.preserve === 'start') {\n            this._output.raw = true;\n          }\n          this.print_newline(false, true);\n          return;\n        }\n\n        // inline block\n        if (!acorn.newline.test(current_token.text) && !current_token.newlines) {\n          this._output.space_before_token = true;\n          this.print_token(current_token);\n          this._output.space_before_token = true;\n          return;\n        } else {\n          this.print_block_commment(current_token, preserve_statement_flags);\n        }\n      };\n      Beautifier.prototype.print_block_commment = function (current_token, preserve_statement_flags) {\n        var lines = split_linebreaks(current_token.text);\n        var j; // iterator for this case\n        var javadoc = false;\n        var starless = false;\n        var lastIndent = current_token.whitespace_before;\n        var lastIndentLength = lastIndent.length;\n\n        // block comment starts with a new line\n        this.print_newline(false, preserve_statement_flags);\n\n        // first line always indented\n        this.print_token_line_indentation(current_token);\n        this._output.add_token(lines[0]);\n        this.print_newline(false, preserve_statement_flags);\n        if (lines.length > 1) {\n          lines = lines.slice(1);\n          javadoc = all_lines_start_with(lines, '*');\n          starless = each_line_matches_indent(lines, lastIndent);\n          if (javadoc) {\n            this._flags.alignment = 1;\n          }\n          for (j = 0; j < lines.length; j++) {\n            if (javadoc) {\n              // javadoc: reformat and re-indent\n              this.print_token_line_indentation(current_token);\n              this._output.add_token(ltrim(lines[j]));\n            } else if (starless && lines[j]) {\n              // starless: re-indent non-empty content, avoiding trim\n              this.print_token_line_indentation(current_token);\n              this._output.add_token(lines[j].substring(lastIndentLength));\n            } else {\n              // normal comments output raw\n              this._output.current_line.set_indent(-1);\n              this._output.add_token(lines[j]);\n            }\n\n            // for comments on their own line or  more than one line, make sure there's a new line after\n            this.print_newline(false, preserve_statement_flags);\n          }\n          this._flags.alignment = 0;\n        }\n      };\n      Beautifier.prototype.handle_comment = function (current_token, preserve_statement_flags) {\n        if (current_token.newlines) {\n          this.print_newline(false, preserve_statement_flags);\n        } else {\n          this._output.trim(true);\n        }\n        this._output.space_before_token = true;\n        this.print_token(current_token);\n        this.print_newline(false, preserve_statement_flags);\n      };\n      Beautifier.prototype.handle_dot = function (current_token) {\n        if (this.start_of_statement(current_token)) {\n          // The conditional starts the statement if appropriate.\n        } else {\n          this.handle_whitespace_and_comments(current_token, true);\n        }\n        if (this._flags.last_token.text.match('^[0-9]+$')) {\n          this._output.space_before_token = true;\n        }\n        if (reserved_array(this._flags.last_token, special_words)) {\n          this._output.space_before_token = false;\n        } else {\n          // allow preserved newlines before dots in general\n          // force newlines on dots after close paren when break_chained - for bar().baz()\n          this.allow_wrap_or_preserved_newline(current_token, this._flags.last_token.text === ')' && this._options.break_chained_methods);\n        }\n\n        // Only unindent chained method dot if this dot starts a new line.\n        // Otherwise the automatic extra indentation removal will handle the over indent\n        if (this._options.unindent_chained_methods && this._output.just_added_newline()) {\n          this.deindent();\n        }\n        this.print_token(current_token);\n      };\n      Beautifier.prototype.handle_unknown = function (current_token, preserve_statement_flags) {\n        this.print_token(current_token);\n        if (current_token.text[current_token.text.length - 1] === '\\n') {\n          this.print_newline(false, preserve_statement_flags);\n        }\n      };\n      Beautifier.prototype.handle_eof = function (current_token) {\n        // Unwind any open statements\n        while (this._flags.mode === MODE.Statement) {\n          this.restore_mode();\n        }\n        this.handle_whitespace_and_comments(current_token);\n      };\n      module.exports.Beautifier = Beautifier;\n\n      /***/\n    }), (/* 2 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      function OutputLine(parent) {\n        this.__parent = parent;\n        this.__character_count = 0;\n        // use indent_count as a marker for this.__lines that have preserved indentation\n        this.__indent_count = -1;\n        this.__alignment_count = 0;\n        this.__wrap_point_index = 0;\n        this.__wrap_point_character_count = 0;\n        this.__wrap_point_indent_count = -1;\n        this.__wrap_point_alignment_count = 0;\n        this.__items = [];\n      }\n      OutputLine.prototype.clone_empty = function () {\n        var line = new OutputLine(this.__parent);\n        line.set_indent(this.__indent_count, this.__alignment_count);\n        return line;\n      };\n      OutputLine.prototype.item = function (index) {\n        if (index < 0) {\n          return this.__items[this.__items.length + index];\n        } else {\n          return this.__items[index];\n        }\n      };\n      OutputLine.prototype.has_match = function (pattern) {\n        for (var lastCheckedOutput = this.__items.length - 1; lastCheckedOutput >= 0; lastCheckedOutput--) {\n          if (this.__items[lastCheckedOutput].match(pattern)) {\n            return true;\n          }\n        }\n        return false;\n      };\n      OutputLine.prototype.set_indent = function (indent, alignment) {\n        if (this.is_empty()) {\n          this.__indent_count = indent || 0;\n          this.__alignment_count = alignment || 0;\n          this.__character_count = this.__parent.get_indent_size(this.__indent_count, this.__alignment_count);\n        }\n      };\n      OutputLine.prototype._set_wrap_point = function () {\n        if (this.__parent.wrap_line_length) {\n          this.__wrap_point_index = this.__items.length;\n          this.__wrap_point_character_count = this.__character_count;\n          this.__wrap_point_indent_count = this.__parent.next_line.__indent_count;\n          this.__wrap_point_alignment_count = this.__parent.next_line.__alignment_count;\n        }\n      };\n      OutputLine.prototype._should_wrap = function () {\n        return this.__wrap_point_index && this.__character_count > this.__parent.wrap_line_length && this.__wrap_point_character_count > this.__parent.next_line.__character_count;\n      };\n      OutputLine.prototype._allow_wrap = function () {\n        if (this._should_wrap()) {\n          this.__parent.add_new_line();\n          var next = this.__parent.current_line;\n          next.set_indent(this.__wrap_point_indent_count, this.__wrap_point_alignment_count);\n          next.__items = this.__items.slice(this.__wrap_point_index);\n          this.__items = this.__items.slice(0, this.__wrap_point_index);\n          next.__character_count += this.__character_count - this.__wrap_point_character_count;\n          this.__character_count = this.__wrap_point_character_count;\n          if (next.__items[0] === \" \") {\n            next.__items.splice(0, 1);\n            next.__character_count -= 1;\n          }\n          return true;\n        }\n        return false;\n      };\n      OutputLine.prototype.is_empty = function () {\n        return this.__items.length === 0;\n      };\n      OutputLine.prototype.last = function () {\n        if (!this.is_empty()) {\n          return this.__items[this.__items.length - 1];\n        } else {\n          return null;\n        }\n      };\n      OutputLine.prototype.push = function (item) {\n        this.__items.push(item);\n        var last_newline_index = item.lastIndexOf('\\n');\n        if (last_newline_index !== -1) {\n          this.__character_count = item.length - last_newline_index;\n        } else {\n          this.__character_count += item.length;\n        }\n      };\n      OutputLine.prototype.pop = function () {\n        var item = null;\n        if (!this.is_empty()) {\n          item = this.__items.pop();\n          this.__character_count -= item.length;\n        }\n        return item;\n      };\n      OutputLine.prototype._remove_indent = function () {\n        if (this.__indent_count > 0) {\n          this.__indent_count -= 1;\n          this.__character_count -= this.__parent.indent_size;\n        }\n      };\n      OutputLine.prototype._remove_wrap_indent = function () {\n        if (this.__wrap_point_indent_count > 0) {\n          this.__wrap_point_indent_count -= 1;\n        }\n      };\n      OutputLine.prototype.trim = function () {\n        while (this.last() === ' ') {\n          this.__items.pop();\n          this.__character_count -= 1;\n        }\n      };\n      OutputLine.prototype.toString = function () {\n        var result = '';\n        if (this.is_empty()) {\n          if (this.__parent.indent_empty_lines) {\n            result = this.__parent.get_indent_string(this.__indent_count);\n          }\n        } else {\n          result = this.__parent.get_indent_string(this.__indent_count, this.__alignment_count);\n          result += this.__items.join('');\n        }\n        return result;\n      };\n      function IndentStringCache(options, baseIndentString) {\n        this.__cache = [''];\n        this.__indent_size = options.indent_size;\n        this.__indent_string = options.indent_char;\n        if (!options.indent_with_tabs) {\n          this.__indent_string = new Array(options.indent_size + 1).join(options.indent_char);\n        }\n\n        // Set to null to continue support for auto detection of base indent\n        baseIndentString = baseIndentString || '';\n        if (options.indent_level > 0) {\n          baseIndentString = new Array(options.indent_level + 1).join(this.__indent_string);\n        }\n        this.__base_string = baseIndentString;\n        this.__base_string_length = baseIndentString.length;\n      }\n      IndentStringCache.prototype.get_indent_size = function (indent, column) {\n        var result = this.__base_string_length;\n        column = column || 0;\n        if (indent < 0) {\n          result = 0;\n        }\n        result += indent * this.__indent_size;\n        result += column;\n        return result;\n      };\n      IndentStringCache.prototype.get_indent_string = function (indent_level, column) {\n        var result = this.__base_string;\n        column = column || 0;\n        if (indent_level < 0) {\n          indent_level = 0;\n          result = '';\n        }\n        column += indent_level * this.__indent_size;\n        this.__ensure_cache(column);\n        result += this.__cache[column];\n        return result;\n      };\n      IndentStringCache.prototype.__ensure_cache = function (column) {\n        while (column >= this.__cache.length) {\n          this.__add_column();\n        }\n      };\n      IndentStringCache.prototype.__add_column = function () {\n        var column = this.__cache.length;\n        var indent = 0;\n        var result = '';\n        if (this.__indent_size && column >= this.__indent_size) {\n          indent = Math.floor(column / this.__indent_size);\n          column -= indent * this.__indent_size;\n          result = new Array(indent + 1).join(this.__indent_string);\n        }\n        if (column) {\n          result += new Array(column + 1).join(' ');\n        }\n        this.__cache.push(result);\n      };\n      function Output(options, baseIndentString) {\n        this.__indent_cache = new IndentStringCache(options, baseIndentString);\n        this.raw = false;\n        this._end_with_newline = options.end_with_newline;\n        this.indent_size = options.indent_size;\n        this.wrap_line_length = options.wrap_line_length;\n        this.indent_empty_lines = options.indent_empty_lines;\n        this.__lines = [];\n        this.previous_line = null;\n        this.current_line = null;\n        this.next_line = new OutputLine(this);\n        this.space_before_token = false;\n        this.non_breaking_space = false;\n        this.previous_token_wrapped = false;\n        // initialize\n        this.__add_outputline();\n      }\n      Output.prototype.__add_outputline = function () {\n        this.previous_line = this.current_line;\n        this.current_line = this.next_line.clone_empty();\n        this.__lines.push(this.current_line);\n      };\n      Output.prototype.get_line_number = function () {\n        return this.__lines.length;\n      };\n      Output.prototype.get_indent_string = function (indent, column) {\n        return this.__indent_cache.get_indent_string(indent, column);\n      };\n      Output.prototype.get_indent_size = function (indent, column) {\n        return this.__indent_cache.get_indent_size(indent, column);\n      };\n      Output.prototype.is_empty = function () {\n        return !this.previous_line && this.current_line.is_empty();\n      };\n      Output.prototype.add_new_line = function (force_newline) {\n        // never newline at the start of file\n        // otherwise, newline only if we didn't just add one or we're forced\n        if (this.is_empty() || !force_newline && this.just_added_newline()) {\n          return false;\n        }\n\n        // if raw output is enabled, don't print additional newlines,\n        // but still return True as though you had\n        if (!this.raw) {\n          this.__add_outputline();\n        }\n        return true;\n      };\n      Output.prototype.get_code = function (eol) {\n        this.trim(true);\n\n        // handle some edge cases where the last tokens\n        // has text that ends with newline(s)\n        var last_item = this.current_line.pop();\n        if (last_item) {\n          if (last_item[last_item.length - 1] === '\\n') {\n            last_item = last_item.replace(/\\n+$/g, '');\n          }\n          this.current_line.push(last_item);\n        }\n        if (this._end_with_newline) {\n          this.__add_outputline();\n        }\n        var sweet_code = this.__lines.join('\\n');\n        if (eol !== '\\n') {\n          sweet_code = sweet_code.replace(/[\\n]/g, eol);\n        }\n        return sweet_code;\n      };\n      Output.prototype.set_wrap_point = function () {\n        this.current_line._set_wrap_point();\n      };\n      Output.prototype.set_indent = function (indent, alignment) {\n        indent = indent || 0;\n        alignment = alignment || 0;\n\n        // Next line stores alignment values\n        this.next_line.set_indent(indent, alignment);\n\n        // Never indent your first output indent at the start of the file\n        if (this.__lines.length > 1) {\n          this.current_line.set_indent(indent, alignment);\n          return true;\n        }\n        this.current_line.set_indent();\n        return false;\n      };\n      Output.prototype.add_raw_token = function (token) {\n        for (var x = 0; x < token.newlines; x++) {\n          this.__add_outputline();\n        }\n        this.current_line.set_indent(-1);\n        this.current_line.push(token.whitespace_before);\n        this.current_line.push(token.text);\n        this.space_before_token = false;\n        this.non_breaking_space = false;\n        this.previous_token_wrapped = false;\n      };\n      Output.prototype.add_token = function (printable_token) {\n        this.__add_space_before_token();\n        this.current_line.push(printable_token);\n        this.space_before_token = false;\n        this.non_breaking_space = false;\n        this.previous_token_wrapped = this.current_line._allow_wrap();\n      };\n      Output.prototype.__add_space_before_token = function () {\n        if (this.space_before_token && !this.just_added_newline()) {\n          if (!this.non_breaking_space) {\n            this.set_wrap_point();\n          }\n          this.current_line.push(' ');\n        }\n      };\n      Output.prototype.remove_indent = function (index) {\n        var output_length = this.__lines.length;\n        while (index < output_length) {\n          this.__lines[index]._remove_indent();\n          index++;\n        }\n        this.current_line._remove_wrap_indent();\n      };\n      Output.prototype.trim = function (eat_newlines) {\n        eat_newlines = eat_newlines === undefined ? false : eat_newlines;\n        this.current_line.trim();\n        while (eat_newlines && this.__lines.length > 1 && this.current_line.is_empty()) {\n          this.__lines.pop();\n          this.current_line = this.__lines[this.__lines.length - 1];\n          this.current_line.trim();\n        }\n        this.previous_line = this.__lines.length > 1 ? this.__lines[this.__lines.length - 2] : null;\n      };\n      Output.prototype.just_added_newline = function () {\n        return this.current_line.is_empty();\n      };\n      Output.prototype.just_added_blankline = function () {\n        return this.is_empty() || this.current_line.is_empty() && this.previous_line.is_empty();\n      };\n      Output.prototype.ensure_empty_line_above = function (starts_with, ends_with) {\n        var index = this.__lines.length - 2;\n        while (index >= 0) {\n          var potentialEmptyLine = this.__lines[index];\n          if (potentialEmptyLine.is_empty()) {\n            break;\n          } else if (potentialEmptyLine.item(0).indexOf(starts_with) !== 0 && potentialEmptyLine.item(-1) !== ends_with) {\n            this.__lines.splice(index + 1, 0, new OutputLine(this));\n            this.previous_line = this.__lines[this.__lines.length - 2];\n            break;\n          }\n          index--;\n        }\n      };\n      module.exports.Output = Output;\n\n      /***/\n    }), (/* 3 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      function Token(type, text, newlines, whitespace_before) {\n        this.type = type;\n        this.text = text;\n\n        // comments_before are\n        // comments that have a new line before them\n        // and may or may not have a newline after\n        // this is a set of comments before\n        this.comments_before = null; /* inline comment*/\n\n        // this.comments_after =  new TokenStream(); // no new line before and newline after\n        this.newlines = newlines || 0;\n        this.whitespace_before = whitespace_before || '';\n        this.parent = null;\n        this.next = null;\n        this.previous = null;\n        this.opened = null;\n        this.closed = null;\n        this.directives = null;\n      }\n      module.exports.Token = Token;\n\n      /***/\n    }), (/* 4 */\n    /***/function (__unused_webpack_module, exports) {\n      /* jshint node: true, curly: false */\n      // Parts of this section of code is taken from acorn.\n      //\n      // Acorn was written by Marijn Haverbeke and released under an MIT\n      // license. The Unicode regexps (for identifiers and whitespace) were\n      // taken from [Esprima](http://esprima.org) by Ariya Hidayat.\n      //\n      // Git repositories for Acorn are available at\n      //\n      //     http://marijnhaverbeke.nl/git/acorn\n      //     https://github.com/marijnh/acorn.git\n\n      // ## Character categories\n\n      // acorn used char codes to squeeze the last bit of performance out\n      // Beautifier is okay without that, so we're using regex\n      // permit # (23), $ (36), and @ (64). @ is used in ES7 decorators.\n      // 65 through 91 are uppercase letters.\n      // permit _ (95).\n      // 97 through 123 are lowercase letters.\n      var baseASCIIidentifierStartChars = \"\\\\x23\\\\x24\\\\x40\\\\x41-\\\\x5a\\\\x5f\\\\x61-\\\\x7a\";\n\n      // inside an identifier @ is not allowed but 0-9 are.\n      var baseASCIIidentifierChars = \"\\\\x24\\\\x30-\\\\x39\\\\x41-\\\\x5a\\\\x5f\\\\x61-\\\\x7a\";\n\n      // Big ugly regular expressions that match characters in the\n      // whitespace, identifier, and identifier-start categories. These\n      // are only applied when a character is found to actually have a\n      // code point above 128.\n      var nonASCIIidentifierStartChars = \"\\\\xaa\\\\xb5\\\\xba\\\\xc0-\\\\xd6\\\\xd8-\\\\xf6\\\\xf8-\\\\u02c1\\\\u02c6-\\\\u02d1\\\\u02e0-\\\\u02e4\\\\u02ec\\\\u02ee\\\\u0370-\\\\u0374\\\\u0376\\\\u0377\\\\u037a-\\\\u037d\\\\u0386\\\\u0388-\\\\u038a\\\\u038c\\\\u038e-\\\\u03a1\\\\u03a3-\\\\u03f5\\\\u03f7-\\\\u0481\\\\u048a-\\\\u0527\\\\u0531-\\\\u0556\\\\u0559\\\\u0561-\\\\u0587\\\\u05d0-\\\\u05ea\\\\u05f0-\\\\u05f2\\\\u0620-\\\\u064a\\\\u066e\\\\u066f\\\\u0671-\\\\u06d3\\\\u06d5\\\\u06e5\\\\u06e6\\\\u06ee\\\\u06ef\\\\u06fa-\\\\u06fc\\\\u06ff\\\\u0710\\\\u0712-\\\\u072f\\\\u074d-\\\\u07a5\\\\u07b1\\\\u07ca-\\\\u07ea\\\\u07f4\\\\u07f5\\\\u07fa\\\\u0800-\\\\u0815\\\\u081a\\\\u0824\\\\u0828\\\\u0840-\\\\u0858\\\\u08a0\\\\u08a2-\\\\u08ac\\\\u0904-\\\\u0939\\\\u093d\\\\u0950\\\\u0958-\\\\u0961\\\\u0971-\\\\u0977\\\\u0979-\\\\u097f\\\\u0985-\\\\u098c\\\\u098f\\\\u0990\\\\u0993-\\\\u09a8\\\\u09aa-\\\\u09b0\\\\u09b2\\\\u09b6-\\\\u09b9\\\\u09bd\\\\u09ce\\\\u09dc\\\\u09dd\\\\u09df-\\\\u09e1\\\\u09f0\\\\u09f1\\\\u0a05-\\\\u0a0a\\\\u0a0f\\\\u0a10\\\\u0a13-\\\\u0a28\\\\u0a2a-\\\\u0a30\\\\u0a32\\\\u0a33\\\\u0a35\\\\u0a36\\\\u0a38\\\\u0a39\\\\u0a59-\\\\u0a5c\\\\u0a5e\\\\u0a72-\\\\u0a74\\\\u0a85-\\\\u0a8d\\\\u0a8f-\\\\u0a91\\\\u0a93-\\\\u0aa8\\\\u0aaa-\\\\u0ab0\\\\u0ab2\\\\u0ab3\\\\u0ab5-\\\\u0ab9\\\\u0abd\\\\u0ad0\\\\u0ae0\\\\u0ae1\\\\u0b05-\\\\u0b0c\\\\u0b0f\\\\u0b10\\\\u0b13-\\\\u0b28\\\\u0b2a-\\\\u0b30\\\\u0b32\\\\u0b33\\\\u0b35-\\\\u0b39\\\\u0b3d\\\\u0b5c\\\\u0b5d\\\\u0b5f-\\\\u0b61\\\\u0b71\\\\u0b83\\\\u0b85-\\\\u0b8a\\\\u0b8e-\\\\u0b90\\\\u0b92-\\\\u0b95\\\\u0b99\\\\u0b9a\\\\u0b9c\\\\u0b9e\\\\u0b9f\\\\u0ba3\\\\u0ba4\\\\u0ba8-\\\\u0baa\\\\u0bae-\\\\u0bb9\\\\u0bd0\\\\u0c05-\\\\u0c0c\\\\u0c0e-\\\\u0c10\\\\u0c12-\\\\u0c28\\\\u0c2a-\\\\u0c33\\\\u0c35-\\\\u0c39\\\\u0c3d\\\\u0c58\\\\u0c59\\\\u0c60\\\\u0c61\\\\u0c85-\\\\u0c8c\\\\u0c8e-\\\\u0c90\\\\u0c92-\\\\u0ca8\\\\u0caa-\\\\u0cb3\\\\u0cb5-\\\\u0cb9\\\\u0cbd\\\\u0cde\\\\u0ce0\\\\u0ce1\\\\u0cf1\\\\u0cf2\\\\u0d05-\\\\u0d0c\\\\u0d0e-\\\\u0d10\\\\u0d12-\\\\u0d3a\\\\u0d3d\\\\u0d4e\\\\u0d60\\\\u0d61\\\\u0d7a-\\\\u0d7f\\\\u0d85-\\\\u0d96\\\\u0d9a-\\\\u0db1\\\\u0db3-\\\\u0dbb\\\\u0dbd\\\\u0dc0-\\\\u0dc6\\\\u0e01-\\\\u0e30\\\\u0e32\\\\u0e33\\\\u0e40-\\\\u0e46\\\\u0e81\\\\u0e82\\\\u0e84\\\\u0e87\\\\u0e88\\\\u0e8a\\\\u0e8d\\\\u0e94-\\\\u0e97\\\\u0e99-\\\\u0e9f\\\\u0ea1-\\\\u0ea3\\\\u0ea5\\\\u0ea7\\\\u0eaa\\\\u0eab\\\\u0ead-\\\\u0eb0\\\\u0eb2\\\\u0eb3\\\\u0ebd\\\\u0ec0-\\\\u0ec4\\\\u0ec6\\\\u0edc-\\\\u0edf\\\\u0f00\\\\u0f40-\\\\u0f47\\\\u0f49-\\\\u0f6c\\\\u0f88-\\\\u0f8c\\\\u1000-\\\\u102a\\\\u103f\\\\u1050-\\\\u1055\\\\u105a-\\\\u105d\\\\u1061\\\\u1065\\\\u1066\\\\u106e-\\\\u1070\\\\u1075-\\\\u1081\\\\u108e\\\\u10a0-\\\\u10c5\\\\u10c7\\\\u10cd\\\\u10d0-\\\\u10fa\\\\u10fc-\\\\u1248\\\\u124a-\\\\u124d\\\\u1250-\\\\u1256\\\\u1258\\\\u125a-\\\\u125d\\\\u1260-\\\\u1288\\\\u128a-\\\\u128d\\\\u1290-\\\\u12b0\\\\u12b2-\\\\u12b5\\\\u12b8-\\\\u12be\\\\u12c0\\\\u12c2-\\\\u12c5\\\\u12c8-\\\\u12d6\\\\u12d8-\\\\u1310\\\\u1312-\\\\u1315\\\\u1318-\\\\u135a\\\\u1380-\\\\u138f\\\\u13a0-\\\\u13f4\\\\u1401-\\\\u166c\\\\u166f-\\\\u167f\\\\u1681-\\\\u169a\\\\u16a0-\\\\u16ea\\\\u16ee-\\\\u16f0\\\\u1700-\\\\u170c\\\\u170e-\\\\u1711\\\\u1720-\\\\u1731\\\\u1740-\\\\u1751\\\\u1760-\\\\u176c\\\\u176e-\\\\u1770\\\\u1780-\\\\u17b3\\\\u17d7\\\\u17dc\\\\u1820-\\\\u1877\\\\u1880-\\\\u18a8\\\\u18aa\\\\u18b0-\\\\u18f5\\\\u1900-\\\\u191c\\\\u1950-\\\\u196d\\\\u1970-\\\\u1974\\\\u1980-\\\\u19ab\\\\u19c1-\\\\u19c7\\\\u1a00-\\\\u1a16\\\\u1a20-\\\\u1a54\\\\u1aa7\\\\u1b05-\\\\u1b33\\\\u1b45-\\\\u1b4b\\\\u1b83-\\\\u1ba0\\\\u1bae\\\\u1baf\\\\u1bba-\\\\u1be5\\\\u1c00-\\\\u1c23\\\\u1c4d-\\\\u1c4f\\\\u1c5a-\\\\u1c7d\\\\u1ce9-\\\\u1cec\\\\u1cee-\\\\u1cf1\\\\u1cf5\\\\u1cf6\\\\u1d00-\\\\u1dbf\\\\u1e00-\\\\u1f15\\\\u1f18-\\\\u1f1d\\\\u1f20-\\\\u1f45\\\\u1f48-\\\\u1f4d\\\\u1f50-\\\\u1f57\\\\u1f59\\\\u1f5b\\\\u1f5d\\\\u1f5f-\\\\u1f7d\\\\u1f80-\\\\u1fb4\\\\u1fb6-\\\\u1fbc\\\\u1fbe\\\\u1fc2-\\\\u1fc4\\\\u1fc6-\\\\u1fcc\\\\u1fd0-\\\\u1fd3\\\\u1fd6-\\\\u1fdb\\\\u1fe0-\\\\u1fec\\\\u1ff2-\\\\u1ff4\\\\u1ff6-\\\\u1ffc\\\\u2071\\\\u207f\\\\u2090-\\\\u209c\\\\u2102\\\\u2107\\\\u210a-\\\\u2113\\\\u2115\\\\u2119-\\\\u211d\\\\u2124\\\\u2126\\\\u2128\\\\u212a-\\\\u212d\\\\u212f-\\\\u2139\\\\u213c-\\\\u213f\\\\u2145-\\\\u2149\\\\u214e\\\\u2160-\\\\u2188\\\\u2c00-\\\\u2c2e\\\\u2c30-\\\\u2c5e\\\\u2c60-\\\\u2ce4\\\\u2ceb-\\\\u2cee\\\\u2cf2\\\\u2cf3\\\\u2d00-\\\\u2d25\\\\u2d27\\\\u2d2d\\\\u2d30-\\\\u2d67\\\\u2d6f\\\\u2d80-\\\\u2d96\\\\u2da0-\\\\u2da6\\\\u2da8-\\\\u2dae\\\\u2db0-\\\\u2db6\\\\u2db8-\\\\u2dbe\\\\u2dc0-\\\\u2dc6\\\\u2dc8-\\\\u2dce\\\\u2dd0-\\\\u2dd6\\\\u2dd8-\\\\u2dde\\\\u2e2f\\\\u3005-\\\\u3007\\\\u3021-\\\\u3029\\\\u3031-\\\\u3035\\\\u3038-\\\\u303c\\\\u3041-\\\\u3096\\\\u309d-\\\\u309f\\\\u30a1-\\\\u30fa\\\\u30fc-\\\\u30ff\\\\u3105-\\\\u312d\\\\u3131-\\\\u318e\\\\u31a0-\\\\u31ba\\\\u31f0-\\\\u31ff\\\\u3400-\\\\u4db5\\\\u4e00-\\\\u9fcc\\\\ua000-\\\\ua48c\\\\ua4d0-\\\\ua4fd\\\\ua500-\\\\ua60c\\\\ua610-\\\\ua61f\\\\ua62a\\\\ua62b\\\\ua640-\\\\ua66e\\\\ua67f-\\\\ua697\\\\ua6a0-\\\\ua6ef\\\\ua717-\\\\ua71f\\\\ua722-\\\\ua788\\\\ua78b-\\\\ua78e\\\\ua790-\\\\ua793\\\\ua7a0-\\\\ua7aa\\\\ua7f8-\\\\ua801\\\\ua803-\\\\ua805\\\\ua807-\\\\ua80a\\\\ua80c-\\\\ua822\\\\ua840-\\\\ua873\\\\ua882-\\\\ua8b3\\\\ua8f2-\\\\ua8f7\\\\ua8fb\\\\ua90a-\\\\ua925\\\\ua930-\\\\ua946\\\\ua960-\\\\ua97c\\\\ua984-\\\\ua9b2\\\\ua9cf\\\\uaa00-\\\\uaa28\\\\uaa40-\\\\uaa42\\\\uaa44-\\\\uaa4b\\\\uaa60-\\\\uaa76\\\\uaa7a\\\\uaa80-\\\\uaaaf\\\\uaab1\\\\uaab5\\\\uaab6\\\\uaab9-\\\\uaabd\\\\uaac0\\\\uaac2\\\\uaadb-\\\\uaadd\\\\uaae0-\\\\uaaea\\\\uaaf2-\\\\uaaf4\\\\uab01-\\\\uab06\\\\uab09-\\\\uab0e\\\\uab11-\\\\uab16\\\\uab20-\\\\uab26\\\\uab28-\\\\uab2e\\\\uabc0-\\\\uabe2\\\\uac00-\\\\ud7a3\\\\ud7b0-\\\\ud7c6\\\\ud7cb-\\\\ud7fb\\\\uf900-\\\\ufa6d\\\\ufa70-\\\\ufad9\\\\ufb00-\\\\ufb06\\\\ufb13-\\\\ufb17\\\\ufb1d\\\\ufb1f-\\\\ufb28\\\\ufb2a-\\\\ufb36\\\\ufb38-\\\\ufb3c\\\\ufb3e\\\\ufb40\\\\ufb41\\\\ufb43\\\\ufb44\\\\ufb46-\\\\ufbb1\\\\ufbd3-\\\\ufd3d\\\\ufd50-\\\\ufd8f\\\\ufd92-\\\\ufdc7\\\\ufdf0-\\\\ufdfb\\\\ufe70-\\\\ufe74\\\\ufe76-\\\\ufefc\\\\uff21-\\\\uff3a\\\\uff41-\\\\uff5a\\\\uff66-\\\\uffbe\\\\uffc2-\\\\uffc7\\\\uffca-\\\\uffcf\\\\uffd2-\\\\uffd7\\\\uffda-\\\\uffdc\";\n      var nonASCIIidentifierChars = \"\\\\u0300-\\\\u036f\\\\u0483-\\\\u0487\\\\u0591-\\\\u05bd\\\\u05bf\\\\u05c1\\\\u05c2\\\\u05c4\\\\u05c5\\\\u05c7\\\\u0610-\\\\u061a\\\\u0620-\\\\u0649\\\\u0672-\\\\u06d3\\\\u06e7-\\\\u06e8\\\\u06fb-\\\\u06fc\\\\u0730-\\\\u074a\\\\u0800-\\\\u0814\\\\u081b-\\\\u0823\\\\u0825-\\\\u0827\\\\u0829-\\\\u082d\\\\u0840-\\\\u0857\\\\u08e4-\\\\u08fe\\\\u0900-\\\\u0903\\\\u093a-\\\\u093c\\\\u093e-\\\\u094f\\\\u0951-\\\\u0957\\\\u0962-\\\\u0963\\\\u0966-\\\\u096f\\\\u0981-\\\\u0983\\\\u09bc\\\\u09be-\\\\u09c4\\\\u09c7\\\\u09c8\\\\u09d7\\\\u09df-\\\\u09e0\\\\u0a01-\\\\u0a03\\\\u0a3c\\\\u0a3e-\\\\u0a42\\\\u0a47\\\\u0a48\\\\u0a4b-\\\\u0a4d\\\\u0a51\\\\u0a66-\\\\u0a71\\\\u0a75\\\\u0a81-\\\\u0a83\\\\u0abc\\\\u0abe-\\\\u0ac5\\\\u0ac7-\\\\u0ac9\\\\u0acb-\\\\u0acd\\\\u0ae2-\\\\u0ae3\\\\u0ae6-\\\\u0aef\\\\u0b01-\\\\u0b03\\\\u0b3c\\\\u0b3e-\\\\u0b44\\\\u0b47\\\\u0b48\\\\u0b4b-\\\\u0b4d\\\\u0b56\\\\u0b57\\\\u0b5f-\\\\u0b60\\\\u0b66-\\\\u0b6f\\\\u0b82\\\\u0bbe-\\\\u0bc2\\\\u0bc6-\\\\u0bc8\\\\u0bca-\\\\u0bcd\\\\u0bd7\\\\u0be6-\\\\u0bef\\\\u0c01-\\\\u0c03\\\\u0c46-\\\\u0c48\\\\u0c4a-\\\\u0c4d\\\\u0c55\\\\u0c56\\\\u0c62-\\\\u0c63\\\\u0c66-\\\\u0c6f\\\\u0c82\\\\u0c83\\\\u0cbc\\\\u0cbe-\\\\u0cc4\\\\u0cc6-\\\\u0cc8\\\\u0cca-\\\\u0ccd\\\\u0cd5\\\\u0cd6\\\\u0ce2-\\\\u0ce3\\\\u0ce6-\\\\u0cef\\\\u0d02\\\\u0d03\\\\u0d46-\\\\u0d48\\\\u0d57\\\\u0d62-\\\\u0d63\\\\u0d66-\\\\u0d6f\\\\u0d82\\\\u0d83\\\\u0dca\\\\u0dcf-\\\\u0dd4\\\\u0dd6\\\\u0dd8-\\\\u0ddf\\\\u0df2\\\\u0df3\\\\u0e34-\\\\u0e3a\\\\u0e40-\\\\u0e45\\\\u0e50-\\\\u0e59\\\\u0eb4-\\\\u0eb9\\\\u0ec8-\\\\u0ecd\\\\u0ed0-\\\\u0ed9\\\\u0f18\\\\u0f19\\\\u0f20-\\\\u0f29\\\\u0f35\\\\u0f37\\\\u0f39\\\\u0f41-\\\\u0f47\\\\u0f71-\\\\u0f84\\\\u0f86-\\\\u0f87\\\\u0f8d-\\\\u0f97\\\\u0f99-\\\\u0fbc\\\\u0fc6\\\\u1000-\\\\u1029\\\\u1040-\\\\u1049\\\\u1067-\\\\u106d\\\\u1071-\\\\u1074\\\\u1082-\\\\u108d\\\\u108f-\\\\u109d\\\\u135d-\\\\u135f\\\\u170e-\\\\u1710\\\\u1720-\\\\u1730\\\\u1740-\\\\u1750\\\\u1772\\\\u1773\\\\u1780-\\\\u17b2\\\\u17dd\\\\u17e0-\\\\u17e9\\\\u180b-\\\\u180d\\\\u1810-\\\\u1819\\\\u1920-\\\\u192b\\\\u1930-\\\\u193b\\\\u1951-\\\\u196d\\\\u19b0-\\\\u19c0\\\\u19c8-\\\\u19c9\\\\u19d0-\\\\u19d9\\\\u1a00-\\\\u1a15\\\\u1a20-\\\\u1a53\\\\u1a60-\\\\u1a7c\\\\u1a7f-\\\\u1a89\\\\u1a90-\\\\u1a99\\\\u1b46-\\\\u1b4b\\\\u1b50-\\\\u1b59\\\\u1b6b-\\\\u1b73\\\\u1bb0-\\\\u1bb9\\\\u1be6-\\\\u1bf3\\\\u1c00-\\\\u1c22\\\\u1c40-\\\\u1c49\\\\u1c5b-\\\\u1c7d\\\\u1cd0-\\\\u1cd2\\\\u1d00-\\\\u1dbe\\\\u1e01-\\\\u1f15\\\\u200c\\\\u200d\\\\u203f\\\\u2040\\\\u2054\\\\u20d0-\\\\u20dc\\\\u20e1\\\\u20e5-\\\\u20f0\\\\u2d81-\\\\u2d96\\\\u2de0-\\\\u2dff\\\\u3021-\\\\u3028\\\\u3099\\\\u309a\\\\ua640-\\\\ua66d\\\\ua674-\\\\ua67d\\\\ua69f\\\\ua6f0-\\\\ua6f1\\\\ua7f8-\\\\ua800\\\\ua806\\\\ua80b\\\\ua823-\\\\ua827\\\\ua880-\\\\ua881\\\\ua8b4-\\\\ua8c4\\\\ua8d0-\\\\ua8d9\\\\ua8f3-\\\\ua8f7\\\\ua900-\\\\ua909\\\\ua926-\\\\ua92d\\\\ua930-\\\\ua945\\\\ua980-\\\\ua983\\\\ua9b3-\\\\ua9c0\\\\uaa00-\\\\uaa27\\\\uaa40-\\\\uaa41\\\\uaa4c-\\\\uaa4d\\\\uaa50-\\\\uaa59\\\\uaa7b\\\\uaae0-\\\\uaae9\\\\uaaf2-\\\\uaaf3\\\\uabc0-\\\\uabe1\\\\uabec\\\\uabed\\\\uabf0-\\\\uabf9\\\\ufb20-\\\\ufb28\\\\ufe00-\\\\ufe0f\\\\ufe20-\\\\ufe26\\\\ufe33\\\\ufe34\\\\ufe4d-\\\\ufe4f\\\\uff10-\\\\uff19\\\\uff3f\";\n      //var nonASCIIidentifierStart = new RegExp(\"[\" + nonASCIIidentifierStartChars + \"]\");\n      //var nonASCIIidentifier = new RegExp(\"[\" + nonASCIIidentifierStartChars + nonASCIIidentifierChars + \"]\");\n\n      var unicodeEscapeOrCodePoint = \"\\\\\\\\u[0-9a-fA-F]{4}|\\\\\\\\u\\\\{[0-9a-fA-F]+\\\\}\";\n      var identifierStart = \"(?:\" + unicodeEscapeOrCodePoint + \"|[\" + baseASCIIidentifierStartChars + nonASCIIidentifierStartChars + \"])\";\n      var identifierChars = \"(?:\" + unicodeEscapeOrCodePoint + \"|[\" + baseASCIIidentifierChars + nonASCIIidentifierStartChars + nonASCIIidentifierChars + \"])*\";\n      exports.identifier = new RegExp(identifierStart + identifierChars, 'g');\n      exports.identifierStart = new RegExp(identifierStart);\n      exports.identifierMatch = new RegExp(\"(?:\" + unicodeEscapeOrCodePoint + \"|[\" + baseASCIIidentifierChars + nonASCIIidentifierStartChars + nonASCIIidentifierChars + \"])+\");\n      var nonASCIIwhitespace = /[\\u1680\\u180e\\u2000-\\u200a\\u202f\\u205f\\u3000\\ufeff]/; // jshint ignore:line\n\n      // Whether a single character denotes a newline.\n\n      exports.newline = /[\\n\\r\\u2028\\u2029]/;\n\n      // Matches a whole line break (where CRLF is considered a single\n      // line break). Used to count lines.\n\n      // in javascript, these two differ\n      // in python they are the same, different methods are called on them\n      exports.lineBreak = new RegExp('\\r\\n|' + exports.newline.source);\n      exports.allLineBreaks = new RegExp(exports.lineBreak.source, 'g');\n\n      /***/\n    }), (/* 5 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var BaseOptions = __webpack_require__(6).Options;\n      var validPositionValues = ['before-newline', 'after-newline', 'preserve-newline'];\n      function Options(options) {\n        BaseOptions.call(this, options, 'js');\n\n        // compatibility, re\n        var raw_brace_style = this.raw_options.brace_style || null;\n        if (raw_brace_style === \"expand-strict\") {\n          //graceful handling of deprecated option\n          this.raw_options.brace_style = \"expand\";\n        } else if (raw_brace_style === \"collapse-preserve-inline\") {\n          //graceful handling of deprecated option\n          this.raw_options.brace_style = \"collapse,preserve-inline\";\n        } else if (this.raw_options.braces_on_own_line !== undefined) {\n          //graceful handling of deprecated option\n          this.raw_options.brace_style = this.raw_options.braces_on_own_line ? \"expand\" : \"collapse\";\n          // } else if (!raw_brace_style) { //Nothing exists to set it\n          //   raw_brace_style = \"collapse\";\n        }\n\n        //preserve-inline in delimited string will trigger brace_preserve_inline, everything\n        //else is considered a brace_style and the last one only will have an effect\n\n        var brace_style_split = this._get_selection_list('brace_style', ['collapse', 'expand', 'end-expand', 'none', 'preserve-inline']);\n        this.brace_preserve_inline = false; //Defaults in case one or other was not specified in meta-option\n        this.brace_style = \"collapse\";\n        for (var bs = 0; bs < brace_style_split.length; bs++) {\n          if (brace_style_split[bs] === \"preserve-inline\") {\n            this.brace_preserve_inline = true;\n          } else {\n            this.brace_style = brace_style_split[bs];\n          }\n        }\n        this.unindent_chained_methods = this._get_boolean('unindent_chained_methods');\n        this.break_chained_methods = this._get_boolean('break_chained_methods');\n        this.space_in_paren = this._get_boolean('space_in_paren');\n        this.space_in_empty_paren = this._get_boolean('space_in_empty_paren');\n        this.jslint_happy = this._get_boolean('jslint_happy');\n        this.space_after_anon_function = this._get_boolean('space_after_anon_function');\n        this.space_after_named_function = this._get_boolean('space_after_named_function');\n        this.keep_array_indentation = this._get_boolean('keep_array_indentation');\n        this.space_before_conditional = this._get_boolean('space_before_conditional', true);\n        this.unescape_strings = this._get_boolean('unescape_strings');\n        this.e4x = this._get_boolean('e4x');\n        this.comma_first = this._get_boolean('comma_first');\n        this.operator_position = this._get_selection('operator_position', validPositionValues);\n\n        // For testing of beautify preserve:start directive\n        this.test_output_raw = this._get_boolean('test_output_raw');\n\n        // force this._options.space_after_anon_function to true if this._options.jslint_happy\n        if (this.jslint_happy) {\n          this.space_after_anon_function = true;\n        }\n      }\n      Options.prototype = new BaseOptions();\n      module.exports.Options = Options;\n\n      /***/\n    }), (/* 6 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      function Options(options, merge_child_field) {\n        this.raw_options = _mergeOpts(options, merge_child_field);\n\n        // Support passing the source text back with no change\n        this.disabled = this._get_boolean('disabled');\n        this.eol = this._get_characters('eol', 'auto');\n        this.end_with_newline = this._get_boolean('end_with_newline');\n        this.indent_size = this._get_number('indent_size', 4);\n        this.indent_char = this._get_characters('indent_char', ' ');\n        this.indent_level = this._get_number('indent_level');\n        this.preserve_newlines = this._get_boolean('preserve_newlines', true);\n        this.max_preserve_newlines = this._get_number('max_preserve_newlines', 32786);\n        if (!this.preserve_newlines) {\n          this.max_preserve_newlines = 0;\n        }\n        this.indent_with_tabs = this._get_boolean('indent_with_tabs', this.indent_char === '\\t');\n        if (this.indent_with_tabs) {\n          this.indent_char = '\\t';\n\n          // indent_size behavior changed after 1.8.6\n          // It used to be that indent_size would be\n          // set to 1 for indent_with_tabs. That is no longer needed and\n          // actually doesn't make sense - why not use spaces? Further,\n          // that might produce unexpected behavior - tabs being used\n          // for single-column alignment. So, when indent_with_tabs is true\n          // and indent_size is 1, reset indent_size to 4.\n          if (this.indent_size === 1) {\n            this.indent_size = 4;\n          }\n        }\n\n        // Backwards compat with 1.3.x\n        this.wrap_line_length = this._get_number('wrap_line_length', this._get_number('max_char'));\n        this.indent_empty_lines = this._get_boolean('indent_empty_lines');\n\n        // valid templating languages ['django', 'erb', 'handlebars', 'php', 'smarty', 'angular']\n        // For now, 'auto' = all off for javascript, all except angular on for html (and inline javascript/css).\n        // other values ignored\n        this.templating = this._get_selection_list('templating', ['auto', 'none', 'angular', 'django', 'erb', 'handlebars', 'php', 'smarty'], ['auto']);\n      }\n      Options.prototype._get_array = function (name, default_value) {\n        var option_value = this.raw_options[name];\n        var result = default_value || [];\n        if (typeof option_value === 'object') {\n          if (option_value !== null && typeof option_value.concat === 'function') {\n            result = option_value.concat();\n          }\n        } else if (typeof option_value === 'string') {\n          result = option_value.split(/[^a-zA-Z0-9_\\/\\-]+/);\n        }\n        return result;\n      };\n      Options.prototype._get_boolean = function (name, default_value) {\n        var option_value = this.raw_options[name];\n        var result = option_value === undefined ? !!default_value : !!option_value;\n        return result;\n      };\n      Options.prototype._get_characters = function (name, default_value) {\n        var option_value = this.raw_options[name];\n        var result = default_value || '';\n        if (typeof option_value === 'string') {\n          result = option_value.replace(/\\\\r/, '\\r').replace(/\\\\n/, '\\n').replace(/\\\\t/, '\\t');\n        }\n        return result;\n      };\n      Options.prototype._get_number = function (name, default_value) {\n        var option_value = this.raw_options[name];\n        default_value = parseInt(default_value, 10);\n        if (isNaN(default_value)) {\n          default_value = 0;\n        }\n        var result = parseInt(option_value, 10);\n        if (isNaN(result)) {\n          result = default_value;\n        }\n        return result;\n      };\n      Options.prototype._get_selection = function (name, selection_list, default_value) {\n        var result = this._get_selection_list(name, selection_list, default_value);\n        if (result.length !== 1) {\n          throw new Error(\"Invalid Option Value: The option '\" + name + \"' can only be one of the following values:\\n\" + selection_list + \"\\nYou passed in: '\" + this.raw_options[name] + \"'\");\n        }\n        return result[0];\n      };\n      Options.prototype._get_selection_list = function (name, selection_list, default_value) {\n        if (!selection_list || selection_list.length === 0) {\n          throw new Error(\"Selection list cannot be empty.\");\n        }\n        default_value = default_value || [selection_list[0]];\n        if (!this._is_valid_selection(default_value, selection_list)) {\n          throw new Error(\"Invalid Default Value!\");\n        }\n        var result = this._get_array(name, default_value);\n        if (!this._is_valid_selection(result, selection_list)) {\n          throw new Error(\"Invalid Option Value: The option '\" + name + \"' can contain only the following values:\\n\" + selection_list + \"\\nYou passed in: '\" + this.raw_options[name] + \"'\");\n        }\n        return result;\n      };\n      Options.prototype._is_valid_selection = function (result, selection_list) {\n        return result.length && selection_list.length && !result.some(function (item) {\n          return selection_list.indexOf(item) === -1;\n        });\n      };\n\n      // merges child options up with the parent options object\n      // Example: obj = {a: 1, b: {a: 2}}\n      //          mergeOpts(obj, 'b')\n      //\n      //          Returns: {a: 2}\n      function _mergeOpts(allOptions, childFieldName) {\n        var finalOpts = {};\n        allOptions = _normalizeOpts(allOptions);\n        var name;\n        for (name in allOptions) {\n          if (name !== childFieldName) {\n            finalOpts[name] = allOptions[name];\n          }\n        }\n\n        //merge in the per type settings for the childFieldName\n        if (childFieldName && allOptions[childFieldName]) {\n          for (name in allOptions[childFieldName]) {\n            finalOpts[name] = allOptions[childFieldName][name];\n          }\n        }\n        return finalOpts;\n      }\n      function _normalizeOpts(options) {\n        var convertedOpts = {};\n        var key;\n        for (key in options) {\n          var newKey = key.replace(/-/g, \"_\");\n          convertedOpts[newKey] = options[key];\n        }\n        return convertedOpts;\n      }\n      module.exports.Options = Options;\n      module.exports.normalizeOpts = _normalizeOpts;\n      module.exports.mergeOpts = _mergeOpts;\n\n      /***/\n    }), (/* 7 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var InputScanner = __webpack_require__(8).InputScanner;\n      var BaseTokenizer = __webpack_require__(9).Tokenizer;\n      var BASETOKEN = __webpack_require__(9).TOKEN;\n      var Directives = __webpack_require__(13).Directives;\n      var acorn = __webpack_require__(4);\n      var Pattern = __webpack_require__(12).Pattern;\n      var TemplatablePattern = __webpack_require__(14).TemplatablePattern;\n      function in_array(what, arr) {\n        return arr.indexOf(what) !== -1;\n      }\n      var TOKEN = {\n        START_EXPR: 'TK_START_EXPR',\n        END_EXPR: 'TK_END_EXPR',\n        START_BLOCK: 'TK_START_BLOCK',\n        END_BLOCK: 'TK_END_BLOCK',\n        WORD: 'TK_WORD',\n        RESERVED: 'TK_RESERVED',\n        SEMICOLON: 'TK_SEMICOLON',\n        STRING: 'TK_STRING',\n        EQUALS: 'TK_EQUALS',\n        OPERATOR: 'TK_OPERATOR',\n        COMMA: 'TK_COMMA',\n        BLOCK_COMMENT: 'TK_BLOCK_COMMENT',\n        COMMENT: 'TK_COMMENT',\n        DOT: 'TK_DOT',\n        UNKNOWN: 'TK_UNKNOWN',\n        START: BASETOKEN.START,\n        RAW: BASETOKEN.RAW,\n        EOF: BASETOKEN.EOF\n      };\n      var directives_core = new Directives(/\\/\\*/, /\\*\\//);\n      var number_pattern = /0[xX][0123456789abcdefABCDEF_]*n?|0[oO][01234567_]*n?|0[bB][01_]*n?|\\d[\\d_]*n|(?:\\.\\d[\\d_]*|\\d[\\d_]*\\.?[\\d_]*)(?:[eE][+-]?[\\d_]+)?/;\n      var digit = /[0-9]/;\n\n      // Dot \".\" must be distinguished from \"...\" and decimal\n      var dot_pattern = /[^\\d\\.]/;\n      var positionable_operators = (\">>> === !== &&= ??= ||= \" + \"<< && >= ** != == <= >> || ?? |> \" + \"< / - + > : & % ? ^ | *\").split(' ');\n\n      // IMPORTANT: this must be sorted longest to shortest or tokenizing many not work.\n      // Also, you must update possitionable operators separately from punct\n      var punct = \">>>= \" + \"... >>= <<= === >>> !== **= &&= ??= ||= \" + \"=> ^= :: /= << <= == && -= >= >> != -- += ** || ?? ++ %= &= *= |= |> \" + \"= ! ? > < : / ^ - + * & % ~ |\";\n      punct = punct.replace(/[-[\\]{}()*+?.,\\\\^$|#]/g, \"\\\\$&\");\n      // ?. but not if followed by a number \n      punct = '\\\\?\\\\.(?!\\\\d) ' + punct;\n      punct = punct.replace(/ /g, '|');\n      var punct_pattern = new RegExp(punct);\n\n      // words which should always start on new line.\n      var line_starters = 'continue,try,throw,return,var,let,const,if,switch,case,default,for,while,break,function,import,export'.split(',');\n      var reserved_words = line_starters.concat(['do', 'in', 'of', 'else', 'get', 'set', 'new', 'catch', 'finally', 'typeof', 'yield', 'async', 'await', 'from', 'as', 'class', 'extends']);\n      var reserved_word_pattern = new RegExp('^(?:' + reserved_words.join('|') + ')$');\n\n      // var template_pattern = /(?:(?:<\\?php|<\\?=)[\\s\\S]*?\\?>)|(?:<%[\\s\\S]*?%>)/g;\n\n      var in_html_comment;\n      var Tokenizer = function (input_string, options) {\n        BaseTokenizer.call(this, input_string, options);\n        this._patterns.whitespace = this._patterns.whitespace.matching(/\\u00A0\\u1680\\u180e\\u2000-\\u200a\\u202f\\u205f\\u3000\\ufeff/.source, /\\u2028\\u2029/.source);\n        var pattern_reader = new Pattern(this._input);\n        var templatable = new TemplatablePattern(this._input).read_options(this._options);\n        this.__patterns = {\n          template: templatable,\n          identifier: templatable.starting_with(acorn.identifier).matching(acorn.identifierMatch),\n          number: pattern_reader.matching(number_pattern),\n          punct: pattern_reader.matching(punct_pattern),\n          // comment ends just before nearest linefeed or end of file\n          comment: pattern_reader.starting_with(/\\/\\//).until(/[\\n\\r\\u2028\\u2029]/),\n          //  /* ... */ comment ends with nearest */ or end of file\n          block_comment: pattern_reader.starting_with(/\\/\\*/).until_after(/\\*\\//),\n          html_comment_start: pattern_reader.matching(/<!--/),\n          html_comment_end: pattern_reader.matching(/-->/),\n          include: pattern_reader.starting_with(/#include/).until_after(acorn.lineBreak),\n          shebang: pattern_reader.starting_with(/#!/).until_after(acorn.lineBreak),\n          xml: pattern_reader.matching(/[\\s\\S]*?<(\\/?)([-a-zA-Z:0-9_.]+|{[^}]+?}|!\\[CDATA\\[[^\\]]*?\\]\\]|)(\\s*{[^}]+?}|\\s+[-a-zA-Z:0-9_.]+|\\s+[-a-zA-Z:0-9_.]+\\s*=\\s*('[^']*'|\"[^\"]*\"|{([^{}]|{[^}]+?})+?}))*\\s*(\\/?)\\s*>/),\n          single_quote: templatable.until(/['\\\\\\n\\r\\u2028\\u2029]/),\n          double_quote: templatable.until(/[\"\\\\\\n\\r\\u2028\\u2029]/),\n          template_text: templatable.until(/[`\\\\$]/),\n          template_expression: templatable.until(/[`}\\\\]/)\n        };\n      };\n      Tokenizer.prototype = new BaseTokenizer();\n      Tokenizer.prototype._is_comment = function (current_token) {\n        return current_token.type === TOKEN.COMMENT || current_token.type === TOKEN.BLOCK_COMMENT || current_token.type === TOKEN.UNKNOWN;\n      };\n      Tokenizer.prototype._is_opening = function (current_token) {\n        return current_token.type === TOKEN.START_BLOCK || current_token.type === TOKEN.START_EXPR;\n      };\n      Tokenizer.prototype._is_closing = function (current_token, open_token) {\n        return (current_token.type === TOKEN.END_BLOCK || current_token.type === TOKEN.END_EXPR) && open_token && (current_token.text === ']' && open_token.text === '[' || current_token.text === ')' && open_token.text === '(' || current_token.text === '}' && open_token.text === '{');\n      };\n      Tokenizer.prototype._reset = function () {\n        in_html_comment = false;\n      };\n      Tokenizer.prototype._get_next_token = function (previous_token, open_token) {\n        // jshint unused:false\n        var token = null;\n        this._readWhitespace();\n        var c = this._input.peek();\n        if (c === null) {\n          return this._create_token(TOKEN.EOF, '');\n        }\n        token = token || this._read_non_javascript(c);\n        token = token || this._read_string(c);\n        token = token || this._read_pair(c, this._input.peek(1)); // Issue #2062 hack for record type '#{'\n        token = token || this._read_word(previous_token);\n        token = token || this._read_singles(c);\n        token = token || this._read_comment(c);\n        token = token || this._read_regexp(c, previous_token);\n        token = token || this._read_xml(c, previous_token);\n        token = token || this._read_punctuation();\n        token = token || this._create_token(TOKEN.UNKNOWN, this._input.next());\n        return token;\n      };\n      Tokenizer.prototype._read_word = function (previous_token) {\n        var resulting_string;\n        resulting_string = this.__patterns.identifier.read();\n        if (resulting_string !== '') {\n          resulting_string = resulting_string.replace(acorn.allLineBreaks, '\\n');\n          if (!(previous_token.type === TOKEN.DOT || previous_token.type === TOKEN.RESERVED && (previous_token.text === 'set' || previous_token.text === 'get')) && reserved_word_pattern.test(resulting_string)) {\n            if ((resulting_string === 'in' || resulting_string === 'of') && (previous_token.type === TOKEN.WORD || previous_token.type === TOKEN.STRING)) {\n              // hack for 'in' and 'of' operators\n              return this._create_token(TOKEN.OPERATOR, resulting_string);\n            }\n            return this._create_token(TOKEN.RESERVED, resulting_string);\n          }\n          return this._create_token(TOKEN.WORD, resulting_string);\n        }\n        resulting_string = this.__patterns.number.read();\n        if (resulting_string !== '') {\n          return this._create_token(TOKEN.WORD, resulting_string);\n        }\n      };\n      Tokenizer.prototype._read_singles = function (c) {\n        var token = null;\n        if (c === '(' || c === '[') {\n          token = this._create_token(TOKEN.START_EXPR, c);\n        } else if (c === ')' || c === ']') {\n          token = this._create_token(TOKEN.END_EXPR, c);\n        } else if (c === '{') {\n          token = this._create_token(TOKEN.START_BLOCK, c);\n        } else if (c === '}') {\n          token = this._create_token(TOKEN.END_BLOCK, c);\n        } else if (c === ';') {\n          token = this._create_token(TOKEN.SEMICOLON, c);\n        } else if (c === '.' && dot_pattern.test(this._input.peek(1))) {\n          token = this._create_token(TOKEN.DOT, c);\n        } else if (c === ',') {\n          token = this._create_token(TOKEN.COMMA, c);\n        }\n        if (token) {\n          this._input.next();\n        }\n        return token;\n      };\n      Tokenizer.prototype._read_pair = function (c, d) {\n        var token = null;\n        if (c === '#' && d === '{') {\n          token = this._create_token(TOKEN.START_BLOCK, c + d);\n        }\n        if (token) {\n          this._input.next();\n          this._input.next();\n        }\n        return token;\n      };\n      Tokenizer.prototype._read_punctuation = function () {\n        var resulting_string = this.__patterns.punct.read();\n        if (resulting_string !== '') {\n          if (resulting_string === '=') {\n            return this._create_token(TOKEN.EQUALS, resulting_string);\n          } else if (resulting_string === '?.') {\n            return this._create_token(TOKEN.DOT, resulting_string);\n          } else {\n            return this._create_token(TOKEN.OPERATOR, resulting_string);\n          }\n        }\n      };\n      Tokenizer.prototype._read_non_javascript = function (c) {\n        var resulting_string = '';\n        if (c === '#') {\n          if (this._is_first_token()) {\n            resulting_string = this.__patterns.shebang.read();\n            if (resulting_string) {\n              return this._create_token(TOKEN.UNKNOWN, resulting_string.trim() + '\\n');\n            }\n          }\n\n          // handles extendscript #includes\n          resulting_string = this.__patterns.include.read();\n          if (resulting_string) {\n            return this._create_token(TOKEN.UNKNOWN, resulting_string.trim() + '\\n');\n          }\n          c = this._input.next();\n\n          // Spidermonkey-specific sharp variables for circular references. Considered obsolete.\n          var sharp = '#';\n          if (this._input.hasNext() && this._input.testChar(digit)) {\n            do {\n              c = this._input.next();\n              sharp += c;\n            } while (this._input.hasNext() && c !== '#' && c !== '=');\n            if (c === '#') {\n              //\n            } else if (this._input.peek() === '[' && this._input.peek(1) === ']') {\n              sharp += '[]';\n              this._input.next();\n              this._input.next();\n            } else if (this._input.peek() === '{' && this._input.peek(1) === '}') {\n              sharp += '{}';\n              this._input.next();\n              this._input.next();\n            }\n            return this._create_token(TOKEN.WORD, sharp);\n          }\n          this._input.back();\n        } else if (c === '<' && this._is_first_token()) {\n          resulting_string = this.__patterns.html_comment_start.read();\n          if (resulting_string) {\n            while (this._input.hasNext() && !this._input.testChar(acorn.newline)) {\n              resulting_string += this._input.next();\n            }\n            in_html_comment = true;\n            return this._create_token(TOKEN.COMMENT, resulting_string);\n          }\n        } else if (in_html_comment && c === '-') {\n          resulting_string = this.__patterns.html_comment_end.read();\n          if (resulting_string) {\n            in_html_comment = false;\n            return this._create_token(TOKEN.COMMENT, resulting_string);\n          }\n        }\n        return null;\n      };\n      Tokenizer.prototype._read_comment = function (c) {\n        var token = null;\n        if (c === '/') {\n          var comment = '';\n          if (this._input.peek(1) === '*') {\n            // peek for comment /* ... */\n            comment = this.__patterns.block_comment.read();\n            var directives = directives_core.get_directives(comment);\n            if (directives && directives.ignore === 'start') {\n              comment += directives_core.readIgnored(this._input);\n            }\n            comment = comment.replace(acorn.allLineBreaks, '\\n');\n            token = this._create_token(TOKEN.BLOCK_COMMENT, comment);\n            token.directives = directives;\n          } else if (this._input.peek(1) === '/') {\n            // peek for comment // ...\n            comment = this.__patterns.comment.read();\n            token = this._create_token(TOKEN.COMMENT, comment);\n          }\n        }\n        return token;\n      };\n      Tokenizer.prototype._read_string = function (c) {\n        if (c === '`' || c === \"'\" || c === '\"') {\n          var resulting_string = this._input.next();\n          this.has_char_escapes = false;\n          if (c === '`') {\n            resulting_string += this._read_string_recursive('`', true, '${');\n          } else {\n            resulting_string += this._read_string_recursive(c);\n          }\n          if (this.has_char_escapes && this._options.unescape_strings) {\n            resulting_string = unescape_string(resulting_string);\n          }\n          if (this._input.peek() === c) {\n            resulting_string += this._input.next();\n          }\n          resulting_string = resulting_string.replace(acorn.allLineBreaks, '\\n');\n          return this._create_token(TOKEN.STRING, resulting_string);\n        }\n        return null;\n      };\n      Tokenizer.prototype._allow_regexp_or_xml = function (previous_token) {\n        // regex and xml can only appear in specific locations during parsing\n        return previous_token.type === TOKEN.RESERVED && in_array(previous_token.text, ['return', 'case', 'throw', 'else', 'do', 'typeof', 'yield']) || previous_token.type === TOKEN.END_EXPR && previous_token.text === ')' && previous_token.opened.previous.type === TOKEN.RESERVED && in_array(previous_token.opened.previous.text, ['if', 'while', 'for']) || in_array(previous_token.type, [TOKEN.COMMENT, TOKEN.START_EXPR, TOKEN.START_BLOCK, TOKEN.START, TOKEN.END_BLOCK, TOKEN.OPERATOR, TOKEN.EQUALS, TOKEN.EOF, TOKEN.SEMICOLON, TOKEN.COMMA]);\n      };\n      Tokenizer.prototype._read_regexp = function (c, previous_token) {\n        if (c === '/' && this._allow_regexp_or_xml(previous_token)) {\n          // handle regexp\n          //\n          var resulting_string = this._input.next();\n          var esc = false;\n          var in_char_class = false;\n          while (this._input.hasNext() && (esc || in_char_class || this._input.peek() !== c) && !this._input.testChar(acorn.newline)) {\n            resulting_string += this._input.peek();\n            if (!esc) {\n              esc = this._input.peek() === '\\\\';\n              if (this._input.peek() === '[') {\n                in_char_class = true;\n              } else if (this._input.peek() === ']') {\n                in_char_class = false;\n              }\n            } else {\n              esc = false;\n            }\n            this._input.next();\n          }\n          if (this._input.peek() === c) {\n            resulting_string += this._input.next();\n\n            // regexps may have modifiers /regexp/MOD , so fetch those, too\n            // Only [gim] are valid, but if the user puts in garbage, do what we can to take it.\n            resulting_string += this._input.read(acorn.identifier);\n          }\n          return this._create_token(TOKEN.STRING, resulting_string);\n        }\n        return null;\n      };\n      Tokenizer.prototype._read_xml = function (c, previous_token) {\n        if (this._options.e4x && c === \"<\" && this._allow_regexp_or_xml(previous_token)) {\n          var xmlStr = '';\n          var match = this.__patterns.xml.read_match();\n          // handle e4x xml literals\n          //\n          if (match) {\n            // Trim root tag to attempt to\n            var rootTag = match[2].replace(/^{\\s+/, '{').replace(/\\s+}$/, '}');\n            var isCurlyRoot = rootTag.indexOf('{') === 0;\n            var depth = 0;\n            while (match) {\n              var isEndTag = !!match[1];\n              var tagName = match[2];\n              var isSingletonTag = !!match[match.length - 1] || tagName.slice(0, 8) === \"![CDATA[\";\n              if (!isSingletonTag && (tagName === rootTag || isCurlyRoot && tagName.replace(/^{\\s+/, '{').replace(/\\s+}$/, '}'))) {\n                if (isEndTag) {\n                  --depth;\n                } else {\n                  ++depth;\n                }\n              }\n              xmlStr += match[0];\n              if (depth <= 0) {\n                break;\n              }\n              match = this.__patterns.xml.read_match();\n            }\n            // if we didn't close correctly, keep unformatted.\n            if (!match) {\n              xmlStr += this._input.match(/[\\s\\S]*/g)[0];\n            }\n            xmlStr = xmlStr.replace(acorn.allLineBreaks, '\\n');\n            return this._create_token(TOKEN.STRING, xmlStr);\n          }\n        }\n        return null;\n      };\n      function unescape_string(s) {\n        // You think that a regex would work for this\n        // return s.replace(/\\\\x([0-9a-f]{2})/gi, function(match, val) {\n        //         return String.fromCharCode(parseInt(val, 16));\n        //     })\n        // However, dealing with '\\xff', '\\\\xff', '\\\\\\xff' makes this more fun.\n        var out = '',\n          escaped = 0;\n        var input_scan = new InputScanner(s);\n        var matched = null;\n        while (input_scan.hasNext()) {\n          // Keep any whitespace, non-slash characters\n          // also keep slash pairs.\n          matched = input_scan.match(/([\\s]|[^\\\\]|\\\\\\\\)+/g);\n          if (matched) {\n            out += matched[0];\n          }\n          if (input_scan.peek() === '\\\\') {\n            input_scan.next();\n            if (input_scan.peek() === 'x') {\n              matched = input_scan.match(/x([0-9A-Fa-f]{2})/g);\n            } else if (input_scan.peek() === 'u') {\n              matched = input_scan.match(/u([0-9A-Fa-f]{4})/g);\n              if (!matched) {\n                matched = input_scan.match(/u\\{([0-9A-Fa-f]+)\\}/g);\n              }\n            } else {\n              out += '\\\\';\n              if (input_scan.hasNext()) {\n                out += input_scan.next();\n              }\n              continue;\n            }\n\n            // If there's some error decoding, return the original string\n            if (!matched) {\n              return s;\n            }\n            escaped = parseInt(matched[1], 16);\n            if (escaped > 0x7e && escaped <= 0xff && matched[0].indexOf('x') === 0) {\n              // we bail out on \\x7f..\\xff,\n              // leaving whole string escaped,\n              // as it's probably completely binary\n              return s;\n            } else if (escaped >= 0x00 && escaped < 0x20) {\n              // leave 0x00...0x1f escaped\n              out += '\\\\' + matched[0];\n            } else if (escaped > 0x10FFFF) {\n              // If the escape sequence is out of bounds, keep the original sequence and continue conversion\n              out += '\\\\' + matched[0];\n            } else if (escaped === 0x22 || escaped === 0x27 || escaped === 0x5c) {\n              // single-quote, apostrophe, backslash - escape these\n              out += '\\\\' + String.fromCharCode(escaped);\n            } else {\n              out += String.fromCharCode(escaped);\n            }\n          }\n        }\n        return out;\n      }\n\n      // handle string\n      //\n      Tokenizer.prototype._read_string_recursive = function (delimiter, allow_unescaped_newlines, start_sub) {\n        var current_char;\n        var pattern;\n        if (delimiter === '\\'') {\n          pattern = this.__patterns.single_quote;\n        } else if (delimiter === '\"') {\n          pattern = this.__patterns.double_quote;\n        } else if (delimiter === '`') {\n          pattern = this.__patterns.template_text;\n        } else if (delimiter === '}') {\n          pattern = this.__patterns.template_expression;\n        }\n        var resulting_string = pattern.read();\n        var next = '';\n        while (this._input.hasNext()) {\n          next = this._input.next();\n          if (next === delimiter || !allow_unescaped_newlines && acorn.newline.test(next)) {\n            this._input.back();\n            break;\n          } else if (next === '\\\\' && this._input.hasNext()) {\n            current_char = this._input.peek();\n            if (current_char === 'x' || current_char === 'u') {\n              this.has_char_escapes = true;\n            } else if (current_char === '\\r' && this._input.peek(1) === '\\n') {\n              this._input.next();\n            }\n            next += this._input.next();\n          } else if (start_sub) {\n            if (start_sub === '${' && next === '$' && this._input.peek() === '{') {\n              next += this._input.next();\n            }\n            if (start_sub === next) {\n              if (delimiter === '`') {\n                next += this._read_string_recursive('}', allow_unescaped_newlines, '`');\n              } else {\n                next += this._read_string_recursive('`', allow_unescaped_newlines, '${');\n              }\n              if (this._input.hasNext()) {\n                next += this._input.next();\n              }\n            }\n          }\n          next += pattern.read();\n          resulting_string += next;\n        }\n        return resulting_string;\n      };\n      module.exports.Tokenizer = Tokenizer;\n      module.exports.TOKEN = TOKEN;\n      module.exports.positionable_operators = positionable_operators.slice();\n      module.exports.line_starters = line_starters.slice();\n\n      /***/\n    }), (/* 8 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var regexp_has_sticky = RegExp.prototype.hasOwnProperty('sticky');\n      function InputScanner(input_string) {\n        this.__input = input_string || '';\n        this.__input_length = this.__input.length;\n        this.__position = 0;\n      }\n      InputScanner.prototype.restart = function () {\n        this.__position = 0;\n      };\n      InputScanner.prototype.back = function () {\n        if (this.__position > 0) {\n          this.__position -= 1;\n        }\n      };\n      InputScanner.prototype.hasNext = function () {\n        return this.__position < this.__input_length;\n      };\n      InputScanner.prototype.next = function () {\n        var val = null;\n        if (this.hasNext()) {\n          val = this.__input.charAt(this.__position);\n          this.__position += 1;\n        }\n        return val;\n      };\n      InputScanner.prototype.peek = function (index) {\n        var val = null;\n        index = index || 0;\n        index += this.__position;\n        if (index >= 0 && index < this.__input_length) {\n          val = this.__input.charAt(index);\n        }\n        return val;\n      };\n\n      // This is a JavaScript only helper function (not in python)\n      // Javascript doesn't have a match method\n      // and not all implementation support \"sticky\" flag.\n      // If they do not support sticky then both this.match() and this.test() method\n      // must get the match and check the index of the match.\n      // If sticky is supported and set, this method will use it.\n      // Otherwise it will check that global is set, and fall back to the slower method.\n      InputScanner.prototype.__match = function (pattern, index) {\n        pattern.lastIndex = index;\n        var pattern_match = pattern.exec(this.__input);\n        if (pattern_match && !(regexp_has_sticky && pattern.sticky)) {\n          if (pattern_match.index !== index) {\n            pattern_match = null;\n          }\n        }\n        return pattern_match;\n      };\n      InputScanner.prototype.test = function (pattern, index) {\n        index = index || 0;\n        index += this.__position;\n        if (index >= 0 && index < this.__input_length) {\n          return !!this.__match(pattern, index);\n        } else {\n          return false;\n        }\n      };\n      InputScanner.prototype.testChar = function (pattern, index) {\n        // test one character regex match\n        var val = this.peek(index);\n        pattern.lastIndex = 0;\n        return val !== null && pattern.test(val);\n      };\n      InputScanner.prototype.match = function (pattern) {\n        var pattern_match = this.__match(pattern, this.__position);\n        if (pattern_match) {\n          this.__position += pattern_match[0].length;\n        } else {\n          pattern_match = null;\n        }\n        return pattern_match;\n      };\n      InputScanner.prototype.read = function (starting_pattern, until_pattern, until_after) {\n        var val = '';\n        var match;\n        if (starting_pattern) {\n          match = this.match(starting_pattern);\n          if (match) {\n            val += match[0];\n          }\n        }\n        if (until_pattern && (match || !starting_pattern)) {\n          val += this.readUntil(until_pattern, until_after);\n        }\n        return val;\n      };\n      InputScanner.prototype.readUntil = function (pattern, until_after) {\n        var val = '';\n        var match_index = this.__position;\n        pattern.lastIndex = this.__position;\n        var pattern_match = pattern.exec(this.__input);\n        if (pattern_match) {\n          match_index = pattern_match.index;\n          if (until_after) {\n            match_index += pattern_match[0].length;\n          }\n        } else {\n          match_index = this.__input_length;\n        }\n        val = this.__input.substring(this.__position, match_index);\n        this.__position = match_index;\n        return val;\n      };\n      InputScanner.prototype.readUntilAfter = function (pattern) {\n        return this.readUntil(pattern, true);\n      };\n      InputScanner.prototype.get_regexp = function (pattern, match_from) {\n        var result = null;\n        var flags = 'g';\n        if (match_from && regexp_has_sticky) {\n          flags = 'y';\n        }\n        // strings are converted to regexp\n        if (typeof pattern === \"string\" && pattern !== '') {\n          // result = new RegExp(pattern.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'), flags);\n          result = new RegExp(pattern, flags);\n        } else if (pattern) {\n          result = new RegExp(pattern.source, flags);\n        }\n        return result;\n      };\n      InputScanner.prototype.get_literal_regexp = function (literal_string) {\n        return RegExp(literal_string.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'));\n      };\n\n      /* css beautifier legacy helpers */\n      InputScanner.prototype.peekUntilAfter = function (pattern) {\n        var start = this.__position;\n        var val = this.readUntilAfter(pattern);\n        this.__position = start;\n        return val;\n      };\n      InputScanner.prototype.lookBack = function (testVal) {\n        var start = this.__position - 1;\n        return start >= testVal.length && this.__input.substring(start - testVal.length, start).toLowerCase() === testVal;\n      };\n      module.exports.InputScanner = InputScanner;\n\n      /***/\n    }), (/* 9 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var InputScanner = __webpack_require__(8).InputScanner;\n      var Token = __webpack_require__(3).Token;\n      var TokenStream = __webpack_require__(10).TokenStream;\n      var WhitespacePattern = __webpack_require__(11).WhitespacePattern;\n      var TOKEN = {\n        START: 'TK_START',\n        RAW: 'TK_RAW',\n        EOF: 'TK_EOF'\n      };\n      var Tokenizer = function (input_string, options) {\n        this._input = new InputScanner(input_string);\n        this._options = options || {};\n        this.__tokens = null;\n        this._patterns = {};\n        this._patterns.whitespace = new WhitespacePattern(this._input);\n      };\n      Tokenizer.prototype.tokenize = function () {\n        this._input.restart();\n        this.__tokens = new TokenStream();\n        this._reset();\n        var current;\n        var previous = new Token(TOKEN.START, '');\n        var open_token = null;\n        var open_stack = [];\n        var comments = new TokenStream();\n        while (previous.type !== TOKEN.EOF) {\n          current = this._get_next_token(previous, open_token);\n          while (this._is_comment(current)) {\n            comments.add(current);\n            current = this._get_next_token(previous, open_token);\n          }\n          if (!comments.isEmpty()) {\n            current.comments_before = comments;\n            comments = new TokenStream();\n          }\n          current.parent = open_token;\n          if (this._is_opening(current)) {\n            open_stack.push(open_token);\n            open_token = current;\n          } else if (open_token && this._is_closing(current, open_token)) {\n            current.opened = open_token;\n            open_token.closed = current;\n            open_token = open_stack.pop();\n            current.parent = open_token;\n          }\n          current.previous = previous;\n          previous.next = current;\n          this.__tokens.add(current);\n          previous = current;\n        }\n        return this.__tokens;\n      };\n      Tokenizer.prototype._is_first_token = function () {\n        return this.__tokens.isEmpty();\n      };\n      Tokenizer.prototype._reset = function () {};\n      Tokenizer.prototype._get_next_token = function (previous_token, open_token) {\n        // jshint unused:false\n        this._readWhitespace();\n        var resulting_string = this._input.read(/.+/g);\n        if (resulting_string) {\n          return this._create_token(TOKEN.RAW, resulting_string);\n        } else {\n          return this._create_token(TOKEN.EOF, '');\n        }\n      };\n      Tokenizer.prototype._is_comment = function (current_token) {\n        // jshint unused:false\n        return false;\n      };\n      Tokenizer.prototype._is_opening = function (current_token) {\n        // jshint unused:false\n        return false;\n      };\n      Tokenizer.prototype._is_closing = function (current_token, open_token) {\n        // jshint unused:false\n        return false;\n      };\n      Tokenizer.prototype._create_token = function (type, text) {\n        var token = new Token(type, text, this._patterns.whitespace.newline_count, this._patterns.whitespace.whitespace_before_token);\n        return token;\n      };\n      Tokenizer.prototype._readWhitespace = function () {\n        return this._patterns.whitespace.read();\n      };\n      module.exports.Tokenizer = Tokenizer;\n      module.exports.TOKEN = TOKEN;\n\n      /***/\n    }), (/* 10 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      function TokenStream(parent_token) {\n        // private\n        this.__tokens = [];\n        this.__tokens_length = this.__tokens.length;\n        this.__position = 0;\n        this.__parent_token = parent_token;\n      }\n      TokenStream.prototype.restart = function () {\n        this.__position = 0;\n      };\n      TokenStream.prototype.isEmpty = function () {\n        return this.__tokens_length === 0;\n      };\n      TokenStream.prototype.hasNext = function () {\n        return this.__position < this.__tokens_length;\n      };\n      TokenStream.prototype.next = function () {\n        var val = null;\n        if (this.hasNext()) {\n          val = this.__tokens[this.__position];\n          this.__position += 1;\n        }\n        return val;\n      };\n      TokenStream.prototype.peek = function (index) {\n        var val = null;\n        index = index || 0;\n        index += this.__position;\n        if (index >= 0 && index < this.__tokens_length) {\n          val = this.__tokens[index];\n        }\n        return val;\n      };\n      TokenStream.prototype.add = function (token) {\n        if (this.__parent_token) {\n          token.parent = this.__parent_token;\n        }\n        this.__tokens.push(token);\n        this.__tokens_length += 1;\n      };\n      module.exports.TokenStream = TokenStream;\n\n      /***/\n    }), (/* 11 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var Pattern = __webpack_require__(12).Pattern;\n      function WhitespacePattern(input_scanner, parent) {\n        Pattern.call(this, input_scanner, parent);\n        if (parent) {\n          this._line_regexp = this._input.get_regexp(parent._line_regexp);\n        } else {\n          this.__set_whitespace_patterns('', '');\n        }\n        this.newline_count = 0;\n        this.whitespace_before_token = '';\n      }\n      WhitespacePattern.prototype = new Pattern();\n      WhitespacePattern.prototype.__set_whitespace_patterns = function (whitespace_chars, newline_chars) {\n        whitespace_chars += '\\\\t ';\n        newline_chars += '\\\\n\\\\r';\n        this._match_pattern = this._input.get_regexp('[' + whitespace_chars + newline_chars + ']+', true);\n        this._newline_regexp = this._input.get_regexp('\\\\r\\\\n|[' + newline_chars + ']');\n      };\n      WhitespacePattern.prototype.read = function () {\n        this.newline_count = 0;\n        this.whitespace_before_token = '';\n        var resulting_string = this._input.read(this._match_pattern);\n        if (resulting_string === ' ') {\n          this.whitespace_before_token = ' ';\n        } else if (resulting_string) {\n          var matches = this.__split(this._newline_regexp, resulting_string);\n          this.newline_count = matches.length - 1;\n          this.whitespace_before_token = matches[this.newline_count];\n        }\n        return resulting_string;\n      };\n      WhitespacePattern.prototype.matching = function (whitespace_chars, newline_chars) {\n        var result = this._create();\n        result.__set_whitespace_patterns(whitespace_chars, newline_chars);\n        result._update();\n        return result;\n      };\n      WhitespacePattern.prototype._create = function () {\n        return new WhitespacePattern(this._input, this);\n      };\n      WhitespacePattern.prototype.__split = function (regexp, input_string) {\n        regexp.lastIndex = 0;\n        var start_index = 0;\n        var result = [];\n        var next_match = regexp.exec(input_string);\n        while (next_match) {\n          result.push(input_string.substring(start_index, next_match.index));\n          start_index = next_match.index + next_match[0].length;\n          next_match = regexp.exec(input_string);\n        }\n        if (start_index < input_string.length) {\n          result.push(input_string.substring(start_index, input_string.length));\n        } else {\n          result.push('');\n        }\n        return result;\n      };\n      module.exports.WhitespacePattern = WhitespacePattern;\n\n      /***/\n    }), (/* 12 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      function Pattern(input_scanner, parent) {\n        this._input = input_scanner;\n        this._starting_pattern = null;\n        this._match_pattern = null;\n        this._until_pattern = null;\n        this._until_after = false;\n        if (parent) {\n          this._starting_pattern = this._input.get_regexp(parent._starting_pattern, true);\n          this._match_pattern = this._input.get_regexp(parent._match_pattern, true);\n          this._until_pattern = this._input.get_regexp(parent._until_pattern);\n          this._until_after = parent._until_after;\n        }\n      }\n      Pattern.prototype.read = function () {\n        var result = this._input.read(this._starting_pattern);\n        if (!this._starting_pattern || result) {\n          result += this._input.read(this._match_pattern, this._until_pattern, this._until_after);\n        }\n        return result;\n      };\n      Pattern.prototype.read_match = function () {\n        return this._input.match(this._match_pattern);\n      };\n      Pattern.prototype.until_after = function (pattern) {\n        var result = this._create();\n        result._until_after = true;\n        result._until_pattern = this._input.get_regexp(pattern);\n        result._update();\n        return result;\n      };\n      Pattern.prototype.until = function (pattern) {\n        var result = this._create();\n        result._until_after = false;\n        result._until_pattern = this._input.get_regexp(pattern);\n        result._update();\n        return result;\n      };\n      Pattern.prototype.starting_with = function (pattern) {\n        var result = this._create();\n        result._starting_pattern = this._input.get_regexp(pattern, true);\n        result._update();\n        return result;\n      };\n      Pattern.prototype.matching = function (pattern) {\n        var result = this._create();\n        result._match_pattern = this._input.get_regexp(pattern, true);\n        result._update();\n        return result;\n      };\n      Pattern.prototype._create = function () {\n        return new Pattern(this._input, this);\n      };\n      Pattern.prototype._update = function () {};\n      module.exports.Pattern = Pattern;\n\n      /***/\n    }), (/* 13 */\n    /***/function (module) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      function Directives(start_block_pattern, end_block_pattern) {\n        start_block_pattern = typeof start_block_pattern === 'string' ? start_block_pattern : start_block_pattern.source;\n        end_block_pattern = typeof end_block_pattern === 'string' ? end_block_pattern : end_block_pattern.source;\n        this.__directives_block_pattern = new RegExp(start_block_pattern + / beautify( \\w+[:]\\w+)+ /.source + end_block_pattern, 'g');\n        this.__directive_pattern = / (\\w+)[:](\\w+)/g;\n        this.__directives_end_ignore_pattern = new RegExp(start_block_pattern + /\\sbeautify\\signore:end\\s/.source + end_block_pattern, 'g');\n      }\n      Directives.prototype.get_directives = function (text) {\n        if (!text.match(this.__directives_block_pattern)) {\n          return null;\n        }\n        var directives = {};\n        this.__directive_pattern.lastIndex = 0;\n        var directive_match = this.__directive_pattern.exec(text);\n        while (directive_match) {\n          directives[directive_match[1]] = directive_match[2];\n          directive_match = this.__directive_pattern.exec(text);\n        }\n        return directives;\n      };\n      Directives.prototype.readIgnored = function (input) {\n        return input.readUntilAfter(this.__directives_end_ignore_pattern);\n      };\n      module.exports.Directives = Directives;\n\n      /***/\n    }), (/* 14 */\n    /***/function (module, __unused_webpack_exports, __webpack_require__) {\n      /*jshint node:true */\n      /*\n      \n        The MIT License (MIT)\n      \n        Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n      \n        Permission is hereby granted, free of charge, to any person\n        obtaining a copy of this software and associated documentation files\n        (the \"Software\"), to deal in the Software without restriction,\n        including without limitation the rights to use, copy, modify, merge,\n        publish, distribute, sublicense, and/or sell copies of the Software,\n        and to permit persons to whom the Software is furnished to do so,\n        subject to the following conditions:\n      \n        The above copyright notice and this permission notice shall be\n        included in all copies or substantial portions of the Software.\n      \n        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n        EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n        MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n        NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n        BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n        ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n        CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n        SOFTWARE.\n      */\n\n      var Pattern = __webpack_require__(12).Pattern;\n      var template_names = {\n        django: false,\n        erb: false,\n        handlebars: false,\n        php: false,\n        smarty: false,\n        angular: false\n      };\n\n      // This lets templates appear anywhere we would do a readUntil\n      // The cost is higher but it is pay to play.\n      function TemplatablePattern(input_scanner, parent) {\n        Pattern.call(this, input_scanner, parent);\n        this.__template_pattern = null;\n        this._disabled = Object.assign({}, template_names);\n        this._excluded = Object.assign({}, template_names);\n        if (parent) {\n          this.__template_pattern = this._input.get_regexp(parent.__template_pattern);\n          this._excluded = Object.assign(this._excluded, parent._excluded);\n          this._disabled = Object.assign(this._disabled, parent._disabled);\n        }\n        var pattern = new Pattern(input_scanner);\n        this.__patterns = {\n          handlebars_comment: pattern.starting_with(/{{!--/).until_after(/--}}/),\n          handlebars_unescaped: pattern.starting_with(/{{{/).until_after(/}}}/),\n          handlebars: pattern.starting_with(/{{/).until_after(/}}/),\n          php: pattern.starting_with(/<\\?(?:[= ]|php)/).until_after(/\\?>/),\n          erb: pattern.starting_with(/<%[^%]/).until_after(/[^%]%>/),\n          // django coflicts with handlebars a bit.\n          django: pattern.starting_with(/{%/).until_after(/%}/),\n          django_value: pattern.starting_with(/{{/).until_after(/}}/),\n          django_comment: pattern.starting_with(/{#/).until_after(/#}/),\n          smarty: pattern.starting_with(/{(?=[^}{\\s\\n])/).until_after(/[^\\s\\n]}/),\n          smarty_comment: pattern.starting_with(/{\\*/).until_after(/\\*}/),\n          smarty_literal: pattern.starting_with(/{literal}/).until_after(/{\\/literal}/)\n        };\n      }\n      TemplatablePattern.prototype = new Pattern();\n      TemplatablePattern.prototype._create = function () {\n        return new TemplatablePattern(this._input, this);\n      };\n      TemplatablePattern.prototype._update = function () {\n        this.__set_templated_pattern();\n      };\n      TemplatablePattern.prototype.disable = function (language) {\n        var result = this._create();\n        result._disabled[language] = true;\n        result._update();\n        return result;\n      };\n      TemplatablePattern.prototype.read_options = function (options) {\n        var result = this._create();\n        for (var language in template_names) {\n          result._disabled[language] = options.templating.indexOf(language) === -1;\n        }\n        result._update();\n        return result;\n      };\n      TemplatablePattern.prototype.exclude = function (language) {\n        var result = this._create();\n        result._excluded[language] = true;\n        result._update();\n        return result;\n      };\n      TemplatablePattern.prototype.read = function () {\n        var result = '';\n        if (this._match_pattern) {\n          result = this._input.read(this._starting_pattern);\n        } else {\n          result = this._input.read(this._starting_pattern, this.__template_pattern);\n        }\n        var next = this._read_template();\n        while (next) {\n          if (this._match_pattern) {\n            next += this._input.read(this._match_pattern);\n          } else {\n            next += this._input.readUntil(this.__template_pattern);\n          }\n          result += next;\n          next = this._read_template();\n        }\n        if (this._until_after) {\n          result += this._input.readUntilAfter(this._until_pattern);\n        }\n        return result;\n      };\n      TemplatablePattern.prototype.__set_templated_pattern = function () {\n        var items = [];\n        if (!this._disabled.php) {\n          items.push(this.__patterns.php._starting_pattern.source);\n        }\n        if (!this._disabled.handlebars) {\n          items.push(this.__patterns.handlebars._starting_pattern.source);\n        }\n        if (!this._disabled.angular) {\n          // Handlebars ('{{' and '}}') are also special tokens in Angular)\n          items.push(this.__patterns.handlebars._starting_pattern.source);\n        }\n        if (!this._disabled.erb) {\n          items.push(this.__patterns.erb._starting_pattern.source);\n        }\n        if (!this._disabled.django) {\n          items.push(this.__patterns.django._starting_pattern.source);\n          // The starting pattern for django is more complex because it has different\n          // patterns for value, comment, and other sections\n          items.push(this.__patterns.django_value._starting_pattern.source);\n          items.push(this.__patterns.django_comment._starting_pattern.source);\n        }\n        if (!this._disabled.smarty) {\n          items.push(this.__patterns.smarty._starting_pattern.source);\n        }\n        if (this._until_pattern) {\n          items.push(this._until_pattern.source);\n        }\n        this.__template_pattern = this._input.get_regexp('(?:' + items.join('|') + ')');\n      };\n      TemplatablePattern.prototype._read_template = function () {\n        var resulting_string = '';\n        var c = this._input.peek();\n        if (c === '<') {\n          var peek1 = this._input.peek(1);\n          //if we're in a comment, do something special\n          // We treat all comments as literals, even more than preformatted tags\n          // we just look for the appropriate close tag\n          if (!this._disabled.php && !this._excluded.php && peek1 === '?') {\n            resulting_string = resulting_string || this.__patterns.php.read();\n          }\n          if (!this._disabled.erb && !this._excluded.erb && peek1 === '%') {\n            resulting_string = resulting_string || this.__patterns.erb.read();\n          }\n        } else if (c === '{') {\n          if (!this._disabled.handlebars && !this._excluded.handlebars) {\n            resulting_string = resulting_string || this.__patterns.handlebars_comment.read();\n            resulting_string = resulting_string || this.__patterns.handlebars_unescaped.read();\n            resulting_string = resulting_string || this.__patterns.handlebars.read();\n          }\n          if (!this._disabled.django) {\n            // django coflicts with handlebars a bit.\n            if (!this._excluded.django && !this._excluded.handlebars) {\n              resulting_string = resulting_string || this.__patterns.django_value.read();\n            }\n            if (!this._excluded.django) {\n              resulting_string = resulting_string || this.__patterns.django_comment.read();\n              resulting_string = resulting_string || this.__patterns.django.read();\n            }\n          }\n          if (!this._disabled.smarty) {\n            // smarty cannot be enabled with django or handlebars enabled\n            if (this._disabled.django && this._disabled.handlebars) {\n              resulting_string = resulting_string || this.__patterns.smarty_comment.read();\n              resulting_string = resulting_string || this.__patterns.smarty_literal.read();\n              resulting_string = resulting_string || this.__patterns.smarty.read();\n            }\n          }\n        }\n        return resulting_string;\n      };\n      module.exports.TemplatablePattern = TemplatablePattern;\n\n      /***/\n    }\n    /******/)];\n    /************************************************************************/\n    /******/ // The module cache\n    /******/\n    var __webpack_module_cache__ = {};\n    /******/\n    /******/ // The require function\n    /******/\n    function __webpack_require__(moduleId) {\n      /******/ // Check if module is in cache\n      /******/var cachedModule = __webpack_module_cache__[moduleId];\n      /******/\n      if (cachedModule !== undefined) {\n        /******/return cachedModule.exports;\n        /******/\n      }\n      /******/ // Create a new module (and put it into the cache)\n      /******/\n      var module = __webpack_module_cache__[moduleId] = {\n        /******/ // no module.id needed\n        /******/ // no module.loaded needed\n        /******/exports: {}\n        /******/\n      };\n      /******/\n      /******/ // Execute the module function\n      /******/\n      __webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n      /******/\n      /******/ // Return the exports of the module\n      /******/\n      return module.exports;\n      /******/\n    }\n    /******/\n    /************************************************************************/\n    /******/\n    /******/ // startup\n    /******/ // Load entry module and return exports\n    /******/ // This entry module is referenced by other modules so it can't be inlined\n    /******/\n    var __webpack_exports__ = __webpack_require__(0);\n    /******/\n    legacy_beautify_js = __webpack_exports__;\n    /******/\n    /******/\n  })();\n  var js_beautify = legacy_beautify_js;\n  /* Footer */\n  if (typeof define === \"function\" && define.amd) {\n    // Add support for AMD ( https://github.com/amdjs/amdjs-api/wiki/AMD#defineamd-property- )\n    define([], function () {\n      return {\n        js_beautify: js_beautify\n      };\n    });\n  } else if (typeof exports !== \"undefined\") {\n    // Add support for CommonJS. Just put this file somewhere on your require.paths\n    // and you will be able to `var js_beautify = require(\"beautify\").js_beautify`.\n    exports.js_beautify = js_beautify;\n  } else if (typeof window !== \"undefined\") {\n    // If we're running a web page and don't have either of the above, add our one global\n    window.js_beautify = js_beautify;\n  } else if (typeof global !== \"undefined\") {\n    // If we don't even have window, try global.\n    global.js_beautify = js_beautify;\n  }\n})();", "map": {"version": 3, "names": ["legacy_beautify_js", "__webpack_modules__", "module", "__unused_webpack_exports", "__webpack_require__", "Beautifier", "Options", "js_beautify", "js_source_text", "options", "beautifier", "beautify", "exports", "defaultOptions", "Output", "Token", "acorn", "Tokenizer", "line_starters", "positionable_operators", "TOKEN", "in_array", "what", "arr", "indexOf", "ltrim", "s", "replace", "generateMapFromStrings", "list", "result", "x", "length", "reserved_word", "token", "word", "type", "RESERVED", "text", "reserved_array", "words", "special_words", "validPositionValues", "OPERATOR_POSITION", "OPERATOR_POSITION_BEFORE_OR_PRESERVE", "before_newline", "preserve_newline", "MODE", "BlockStatement", "Statement", "ObjectLiteral", "ArrayLiteral", "ForInitializer", "Conditional", "Expression", "remove_redundant_indentation", "output", "frame", "multiline_frame", "mode", "remove_indent", "start_line_index", "split_linebreaks", "allLineBreaks", "out", "idx", "push", "substring", "is_array", "is_expression", "all_lines_start_with", "lines", "c", "i", "line", "trim", "char<PERSON>t", "each_line_matches_indent", "indent", "len", "source_text", "_source_text", "_output", "_tokens", "_last_last_text", "_flags", "_previous_flags", "_flag_store", "_options", "prototype", "create_flags", "flags_base", "next_indent_level", "indentation_level", "just_added_newline", "line_indent_level", "next_flags", "parent", "last_token", "START_BLOCK", "last_word", "declaration_statement", "declaration_assignment", "inline_frame", "if_block", "else_block", "class_start_block", "do_block", "do_while", "import_block", "in_case_statement", "in_case", "case_body", "case_block", "alignment", "get_line_number", "ternary_depth", "_reset", "baseIndentString", "match", "raw", "test_output_raw", "set_mode", "tokenizer", "tokenize", "disabled", "sweet_code", "eol", "lineBreak", "test", "current_token", "next", "handle_token", "get_code", "preserve_statement_flags", "START_EXPR", "handle_start_expr", "END_EXPR", "handle_end_expr", "handle_start_block", "END_BLOCK", "handle_end_block", "WORD", "handle_word", "SEMICOLON", "handle_semicolon", "STRING", "handle_string", "EQUALS", "handle_equals", "OPERATOR", "handle_operator", "COMMA", "handle_comma", "BLOCK_COMMENT", "handle_block_comment", "COMMENT", "handle_comment", "DOT", "handle_dot", "EOF", "handle_eof", "UNKNOWN", "handle_unknown", "handle_whitespace_and_comments", "newlines", "keep_whitespace", "keep_array_indentation", "comments_before", "comment_token", "print_newline", "max_preserve_newlines", "preserve_newlines", "j", "newline_restricted_tokens", "allow_wrap_or_preserved_newline", "force_linewrap", "undefined", "shouldPreserveOrForce", "operatorLogicApplies", "shouldPrintOperatorNewline", "operator_position", "wrap_line_length", "set_wrap_point", "force_newline", "next_token", "peek", "restore_mode", "add_new_line", "print_token_line_indentation", "current_line", "set_indent", "whitespace_before", "space_before_token", "print_token", "add_raw_token", "comma_first", "previous", "previous_line", "last", "popped", "pop", "is_empty", "add_token", "non_breaking_space", "previous_token_wrapped", "deindent", "start_of_object_property", "start_of_statement", "start", "next_mode", "space_in_paren", "space_before_conditional", "peek_back_two", "space_after_named_function", "peek_back_three", "space_after_anon_function", "space_in_empty_paren", "second_token", "empty_braces", "empty_anonymous_function", "brace_preserve_inline", "index", "check_token", "opened", "brace_style", "js<PERSON>_happy", "just_added_blankline", "prefix", "startsWith", "isGeneratorAsterisk", "isUnary", "space_before", "space_after", "in_ternary", "isColon", "isTernaryColon", "isOtherColon", "after_newline", "new_line_needed", "directives", "preserve", "newline", "print_block_commment", "javadoc", "starless", "lastIndent", "lastIndentLength", "slice", "break_chained_methods", "unindent_chained_methods", "OutputLine", "__parent", "__character_count", "__indent_count", "__alignment_count", "__wrap_point_index", "__wrap_point_character_count", "__wrap_point_indent_count", "__wrap_point_alignment_count", "__items", "clone_empty", "item", "has_match", "pattern", "lastCheckedOutput", "get_indent_size", "_set_wrap_point", "next_line", "_should_wrap", "_allow_wrap", "splice", "last_newline_index", "lastIndexOf", "_remove_indent", "indent_size", "_remove_wrap_indent", "toString", "indent_empty_lines", "get_indent_string", "join", "IndentStringCache", "__cache", "__indent_size", "__indent_string", "indent_char", "indent_with_tabs", "Array", "indent_level", "__base_string", "__base_string_length", "column", "__ensure_cache", "__add_column", "Math", "floor", "__indent_cache", "_end_with_newline", "end_with_newline", "__lines", "__add_outputline", "last_item", "printable_token", "__add_space_before_token", "output_length", "eat_newlines", "ensure_empty_line_above", "starts_with", "ends_with", "potentialEmptyLine", "closed", "__unused_webpack_module", "baseASCIIidentifierStartChars", "baseASCIIidentifierChars", "nonASCIIidentifierStartChars", "nonASCIIidentifierChars", "unicodeEscapeOrCodePoint", "identifierStart", "identifierChars", "identifier", "RegExp", "identifierMatch", "nonASCIIwhitespace", "source", "BaseOptions", "call", "raw_brace_style", "raw_options", "braces_on_own_line", "brace_style_split", "_get_selection_list", "bs", "_get_boolean", "unescape_strings", "e4x", "_get_selection", "merge_child_field", "_mergeOpts", "_get_characters", "_get_number", "templating", "_get_array", "name", "default_value", "option_value", "concat", "split", "parseInt", "isNaN", "selection_list", "Error", "_is_valid_selection", "some", "allOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "finalOpts", "_normalizeOpts", "convertedOpts", "key", "new<PERSON>ey", "normalizeOpts", "mergeOpts", "InputScanner", "BaseTokenizer", "BASETOKEN", "Directives", "Pattern", "TemplatablePattern", "START", "RAW", "directives_core", "number_pattern", "digit", "dot_pattern", "punct", "punct_pattern", "reserved_words", "reserved_word_pattern", "in_html_comment", "input_string", "_patterns", "whitespace", "matching", "pattern_reader", "_input", "templatable", "read_options", "__patterns", "template", "starting_with", "number", "comment", "until", "block_comment", "until_after", "html_comment_start", "html_comment_end", "include", "shebang", "xml", "single_quote", "double_quote", "template_text", "template_expression", "_is_comment", "_is_opening", "_is_closing", "open_token", "_get_next_token", "previous_token", "_readWhitespace", "_create_token", "_read_non_javascript", "_read_string", "_read_pair", "_read_word", "_read_singles", "_read_comment", "_read_regexp", "_read_xml", "_read_punctuation", "resulting_string", "read", "d", "_is_first_token", "sharp", "hasNext", "testChar", "back", "get_directives", "ignore", "readIgnored", "has_char_escapes", "_read_string_recursive", "unescape_string", "_allow_regexp_or_xml", "esc", "in_char_class", "xmlStr", "read_match", "rootTag", "isCurlyRoot", "depth", "isEndTag", "tagName", "isSingletonTag", "escaped", "input_scan", "matched", "String", "fromCharCode", "delimiter", "allow_unescaped_newlines", "start_sub", "current_char", "regexp_has_sticky", "hasOwnProperty", "__input", "__input_length", "__position", "restart", "val", "__match", "lastIndex", "pattern_match", "exec", "sticky", "starting_pattern", "until_pattern", "readUntil", "match_index", "readUntilAfter", "get_regexp", "match_from", "flags", "get_literal_regexp", "literal_string", "peekUntilAfter", "lookBack", "testVal", "toLowerCase", "TokenStream", "WhitespacePattern", "__tokens", "current", "open_stack", "comments", "add", "isEmpty", "newline_count", "whitespace_before_token", "parent_token", "__tokens_length", "__parent_token", "input_scanner", "_line_regexp", "__set_whitespace_patterns", "whitespace_chars", "newline_chars", "_match_pattern", "_newline_regexp", "matches", "__split", "_create", "_update", "regexp", "start_index", "next_match", "_starting_pattern", "_until_pattern", "_until_after", "start_block_pattern", "end_block_pattern", "__directives_block_pattern", "__directive_pattern", "__directives_end_ignore_pattern", "directive_match", "input", "template_names", "django", "erb", "handlebars", "php", "smarty", "angular", "__template_pattern", "_disabled", "Object", "assign", "_excluded", "handlebars_comment", "handlebars_unescaped", "django_value", "django_comment", "smarty_comment", "smarty_literal", "__set_templated_pattern", "disable", "language", "exclude", "_read_template", "items", "peek1", "__webpack_module_cache__", "moduleId", "cachedModule", "__webpack_exports__", "define", "amd", "window", "global"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/js-beautify/js/lib/beautify.js"], "sourcesContent": ["/* AUTO-GENERATED. DO NOT MODIFY. */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 <PERSON><PERSON>, <PERSON>, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n\n JS Beautifier\n---------------\n\n\n  Written by Einar <PERSON>lmanis, <<EMAIL>>\n      https://beautifier.io/\n\n  Originally converted to javascript by Vital, <<EMAIL>>\n  \"End braces on own line\" added by <PERSON> J. Shull, <<EMAIL>>\n  Parsing improvements for brace-less statements by Liam Newman <<EMAIL>>\n\n\n  Usage:\n    js_beautify(js_source_text);\n    js_beautify(js_source_text, options);\n\n  The options are:\n    indent_size (default 4)          - indentation size,\n    indent_char (default space)      - character to indent with,\n    preserve_newlines (default true) - whether existing line breaks should be preserved,\n    max_preserve_newlines (default unlimited) - maximum number of line breaks to be preserved in one chunk,\n\n    jslint_happy (default false) - if true, then jslint-stricter mode is enforced.\n\n            jslint_happy        !jslint_happy\n            ---------------------------------\n            function ()         function()\n\n            switch () {         switch() {\n            case 1:               case 1:\n              break;                break;\n            }                   }\n\n    space_after_anon_function (default false) - should the space before an anonymous function's parens be added, \"function()\" vs \"function ()\",\n          NOTE: This option is overridden by jslint_happy (i.e. if jslint_happy is true, space_after_anon_function is true by design)\n\n    brace_style (default \"collapse\") - \"collapse\" | \"expand\" | \"end-expand\" | \"none\" | any of the former + \",preserve-inline\"\n            put braces on the same line as control statements (default), or put braces on own line (Allman / ANSI style), or just put end braces on own line, or attempt to keep them where they are.\n            preserve-inline will try to preserve inline blocks of curly braces\n\n    space_before_conditional (default true) - should the space before conditional statement be added, \"if(true)\" vs \"if (true)\",\n\n    unescape_strings (default false) - should printable characters in strings encoded in \\xNN notation be unescaped, \"example\" vs \"\\x65\\x78\\x61\\x6d\\x70\\x6c\\x65\"\n\n    wrap_line_length (default unlimited) - lines should wrap at next opportunity after this number of characters.\n          NOTE: This is not a hard limit. Lines will continue until a point where a newline would\n                be preserved if it were present.\n\n    end_with_newline (default false)  - end output with a newline\n\n\n    e.g\n\n    js_beautify(js_source_text, {\n      'indent_size': 1,\n      'indent_char': '\\t'\n    });\n\n*/\n\n(function() {\n\n/* GENERATED_BUILD_OUTPUT */\nvar legacy_beautify_js;\n/******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ([\n/* 0 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar Beautifier = (__webpack_require__(1).Beautifier),\n  Options = (__webpack_require__(5).Options);\n\nfunction js_beautify(js_source_text, options) {\n  var beautifier = new Beautifier(js_source_text, options);\n  return beautifier.beautify();\n}\n\nmodule.exports = js_beautify;\nmodule.exports.defaultOptions = function() {\n  return new Options();\n};\n\n\n/***/ }),\n/* 1 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar Output = (__webpack_require__(2).Output);\nvar Token = (__webpack_require__(3).Token);\nvar acorn = __webpack_require__(4);\nvar Options = (__webpack_require__(5).Options);\nvar Tokenizer = (__webpack_require__(7).Tokenizer);\nvar line_starters = (__webpack_require__(7).line_starters);\nvar positionable_operators = (__webpack_require__(7).positionable_operators);\nvar TOKEN = (__webpack_require__(7).TOKEN);\n\n\nfunction in_array(what, arr) {\n  return arr.indexOf(what) !== -1;\n}\n\nfunction ltrim(s) {\n  return s.replace(/^\\s+/g, '');\n}\n\nfunction generateMapFromStrings(list) {\n  var result = {};\n  for (var x = 0; x < list.length; x++) {\n    // make the mapped names underscored instead of dash\n    result[list[x].replace(/-/g, '_')] = list[x];\n  }\n  return result;\n}\n\nfunction reserved_word(token, word) {\n  return token && token.type === TOKEN.RESERVED && token.text === word;\n}\n\nfunction reserved_array(token, words) {\n  return token && token.type === TOKEN.RESERVED && in_array(token.text, words);\n}\n// Unsure of what they mean, but they work. Worth cleaning up in future.\nvar special_words = ['case', 'return', 'do', 'if', 'throw', 'else', 'await', 'break', 'continue', 'async'];\n\nvar validPositionValues = ['before-newline', 'after-newline', 'preserve-newline'];\n\n// Generate map from array\nvar OPERATOR_POSITION = generateMapFromStrings(validPositionValues);\n\nvar OPERATOR_POSITION_BEFORE_OR_PRESERVE = [OPERATOR_POSITION.before_newline, OPERATOR_POSITION.preserve_newline];\n\nvar MODE = {\n  BlockStatement: 'BlockStatement', // 'BLOCK'\n  Statement: 'Statement', // 'STATEMENT'\n  ObjectLiteral: 'ObjectLiteral', // 'OBJECT',\n  ArrayLiteral: 'ArrayLiteral', //'[EXPRESSION]',\n  ForInitializer: 'ForInitializer', //'(FOR-EXPRESSION)',\n  Conditional: 'Conditional', //'(COND-EXPRESSION)',\n  Expression: 'Expression' //'(EXPRESSION)'\n};\n\nfunction remove_redundant_indentation(output, frame) {\n  // This implementation is effective but has some issues:\n  //     - can cause line wrap to happen too soon due to indent removal\n  //           after wrap points are calculated\n  // These issues are minor compared to ugly indentation.\n\n  if (frame.multiline_frame ||\n    frame.mode === MODE.ForInitializer ||\n    frame.mode === MODE.Conditional) {\n    return;\n  }\n\n  // remove one indent from each line inside this section\n  output.remove_indent(frame.start_line_index);\n}\n\n// we could use just string.split, but\n// IE doesn't like returning empty strings\nfunction split_linebreaks(s) {\n  //return s.split(/\\x0d\\x0a|\\x0a/);\n\n  s = s.replace(acorn.allLineBreaks, '\\n');\n  var out = [],\n    idx = s.indexOf(\"\\n\");\n  while (idx !== -1) {\n    out.push(s.substring(0, idx));\n    s = s.substring(idx + 1);\n    idx = s.indexOf(\"\\n\");\n  }\n  if (s.length) {\n    out.push(s);\n  }\n  return out;\n}\n\nfunction is_array(mode) {\n  return mode === MODE.ArrayLiteral;\n}\n\nfunction is_expression(mode) {\n  return in_array(mode, [MODE.Expression, MODE.ForInitializer, MODE.Conditional]);\n}\n\nfunction all_lines_start_with(lines, c) {\n  for (var i = 0; i < lines.length; i++) {\n    var line = lines[i].trim();\n    if (line.charAt(0) !== c) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction each_line_matches_indent(lines, indent) {\n  var i = 0,\n    len = lines.length,\n    line;\n  for (; i < len; i++) {\n    line = lines[i];\n    // allow empty lines to pass through\n    if (line && line.indexOf(indent) !== 0) {\n      return false;\n    }\n  }\n  return true;\n}\n\n\nfunction Beautifier(source_text, options) {\n  options = options || {};\n  this._source_text = source_text || '';\n\n  this._output = null;\n  this._tokens = null;\n  this._last_last_text = null;\n  this._flags = null;\n  this._previous_flags = null;\n\n  this._flag_store = null;\n  this._options = new Options(options);\n}\n\nBeautifier.prototype.create_flags = function(flags_base, mode) {\n  var next_indent_level = 0;\n  if (flags_base) {\n    next_indent_level = flags_base.indentation_level;\n    if (!this._output.just_added_newline() &&\n      flags_base.line_indent_level > next_indent_level) {\n      next_indent_level = flags_base.line_indent_level;\n    }\n  }\n\n  var next_flags = {\n    mode: mode,\n    parent: flags_base,\n    last_token: flags_base ? flags_base.last_token : new Token(TOKEN.START_BLOCK, ''), // last token text\n    last_word: flags_base ? flags_base.last_word : '', // last TOKEN.WORD passed\n    declaration_statement: false,\n    declaration_assignment: false,\n    multiline_frame: false,\n    inline_frame: false,\n    if_block: false,\n    else_block: false,\n    class_start_block: false, // class A { INSIDE HERE } or class B extends C { INSIDE HERE }\n    do_block: false,\n    do_while: false,\n    import_block: false,\n    in_case_statement: false, // switch(..){ INSIDE HERE }\n    in_case: false, // we're on the exact line with \"case 0:\"\n    case_body: false, // the indented case-action block\n    case_block: false, // the indented case-action block is wrapped with {}\n    indentation_level: next_indent_level,\n    alignment: 0,\n    line_indent_level: flags_base ? flags_base.line_indent_level : next_indent_level,\n    start_line_index: this._output.get_line_number(),\n    ternary_depth: 0\n  };\n  return next_flags;\n};\n\nBeautifier.prototype._reset = function(source_text) {\n  var baseIndentString = source_text.match(/^[\\t ]*/)[0];\n\n  this._last_last_text = ''; // pre-last token text\n  this._output = new Output(this._options, baseIndentString);\n\n  // If testing the ignore directive, start with output disable set to true\n  this._output.raw = this._options.test_output_raw;\n\n\n  // Stack of parsing/formatting states, including MODE.\n  // We tokenize, parse, and output in an almost purely a forward-only stream of token input\n  // and formatted output.  This makes the beautifier less accurate than full parsers\n  // but also far more tolerant of syntax errors.\n  //\n  // For example, the default mode is MODE.BlockStatement. If we see a '{' we push a new frame of type\n  // MODE.BlockStatement on the the stack, even though it could be object literal.  If we later\n  // encounter a \":\", we'll switch to to MODE.ObjectLiteral.  If we then see a \";\",\n  // most full parsers would die, but the beautifier gracefully falls back to\n  // MODE.BlockStatement and continues on.\n  this._flag_store = [];\n  this.set_mode(MODE.BlockStatement);\n  var tokenizer = new Tokenizer(source_text, this._options);\n  this._tokens = tokenizer.tokenize();\n  return source_text;\n};\n\nBeautifier.prototype.beautify = function() {\n  // if disabled, return the input unchanged.\n  if (this._options.disabled) {\n    return this._source_text;\n  }\n\n  var sweet_code;\n  var source_text = this._reset(this._source_text);\n\n  var eol = this._options.eol;\n  if (this._options.eol === 'auto') {\n    eol = '\\n';\n    if (source_text && acorn.lineBreak.test(source_text || '')) {\n      eol = source_text.match(acorn.lineBreak)[0];\n    }\n  }\n\n  var current_token = this._tokens.next();\n  while (current_token) {\n    this.handle_token(current_token);\n\n    this._last_last_text = this._flags.last_token.text;\n    this._flags.last_token = current_token;\n\n    current_token = this._tokens.next();\n  }\n\n  sweet_code = this._output.get_code(eol);\n\n  return sweet_code;\n};\n\nBeautifier.prototype.handle_token = function(current_token, preserve_statement_flags) {\n  if (current_token.type === TOKEN.START_EXPR) {\n    this.handle_start_expr(current_token);\n  } else if (current_token.type === TOKEN.END_EXPR) {\n    this.handle_end_expr(current_token);\n  } else if (current_token.type === TOKEN.START_BLOCK) {\n    this.handle_start_block(current_token);\n  } else if (current_token.type === TOKEN.END_BLOCK) {\n    this.handle_end_block(current_token);\n  } else if (current_token.type === TOKEN.WORD) {\n    this.handle_word(current_token);\n  } else if (current_token.type === TOKEN.RESERVED) {\n    this.handle_word(current_token);\n  } else if (current_token.type === TOKEN.SEMICOLON) {\n    this.handle_semicolon(current_token);\n  } else if (current_token.type === TOKEN.STRING) {\n    this.handle_string(current_token);\n  } else if (current_token.type === TOKEN.EQUALS) {\n    this.handle_equals(current_token);\n  } else if (current_token.type === TOKEN.OPERATOR) {\n    this.handle_operator(current_token);\n  } else if (current_token.type === TOKEN.COMMA) {\n    this.handle_comma(current_token);\n  } else if (current_token.type === TOKEN.BLOCK_COMMENT) {\n    this.handle_block_comment(current_token, preserve_statement_flags);\n  } else if (current_token.type === TOKEN.COMMENT) {\n    this.handle_comment(current_token, preserve_statement_flags);\n  } else if (current_token.type === TOKEN.DOT) {\n    this.handle_dot(current_token);\n  } else if (current_token.type === TOKEN.EOF) {\n    this.handle_eof(current_token);\n  } else if (current_token.type === TOKEN.UNKNOWN) {\n    this.handle_unknown(current_token, preserve_statement_flags);\n  } else {\n    this.handle_unknown(current_token, preserve_statement_flags);\n  }\n};\n\nBeautifier.prototype.handle_whitespace_and_comments = function(current_token, preserve_statement_flags) {\n  var newlines = current_token.newlines;\n  var keep_whitespace = this._options.keep_array_indentation && is_array(this._flags.mode);\n\n  if (current_token.comments_before) {\n    var comment_token = current_token.comments_before.next();\n    while (comment_token) {\n      // The cleanest handling of inline comments is to treat them as though they aren't there.\n      // Just continue formatting and the behavior should be logical.\n      // Also ignore unknown tokens.  Again, this should result in better behavior.\n      this.handle_whitespace_and_comments(comment_token, preserve_statement_flags);\n      this.handle_token(comment_token, preserve_statement_flags);\n      comment_token = current_token.comments_before.next();\n    }\n  }\n\n  if (keep_whitespace) {\n    for (var i = 0; i < newlines; i += 1) {\n      this.print_newline(i > 0, preserve_statement_flags);\n    }\n  } else {\n    if (this._options.max_preserve_newlines && newlines > this._options.max_preserve_newlines) {\n      newlines = this._options.max_preserve_newlines;\n    }\n\n    if (this._options.preserve_newlines) {\n      if (newlines > 1) {\n        this.print_newline(false, preserve_statement_flags);\n        for (var j = 1; j < newlines; j += 1) {\n          this.print_newline(true, preserve_statement_flags);\n        }\n      }\n    }\n  }\n\n};\n\nvar newline_restricted_tokens = ['async', 'break', 'continue', 'return', 'throw', 'yield'];\n\nBeautifier.prototype.allow_wrap_or_preserved_newline = function(current_token, force_linewrap) {\n  force_linewrap = (force_linewrap === undefined) ? false : force_linewrap;\n\n  // Never wrap the first token on a line\n  if (this._output.just_added_newline()) {\n    return;\n  }\n\n  var shouldPreserveOrForce = (this._options.preserve_newlines && current_token.newlines) || force_linewrap;\n  var operatorLogicApplies = in_array(this._flags.last_token.text, positionable_operators) ||\n    in_array(current_token.text, positionable_operators);\n\n  if (operatorLogicApplies) {\n    var shouldPrintOperatorNewline = (\n        in_array(this._flags.last_token.text, positionable_operators) &&\n        in_array(this._options.operator_position, OPERATOR_POSITION_BEFORE_OR_PRESERVE)\n      ) ||\n      in_array(current_token.text, positionable_operators);\n    shouldPreserveOrForce = shouldPreserveOrForce && shouldPrintOperatorNewline;\n  }\n\n  if (shouldPreserveOrForce) {\n    this.print_newline(false, true);\n  } else if (this._options.wrap_line_length) {\n    if (reserved_array(this._flags.last_token, newline_restricted_tokens)) {\n      // These tokens should never have a newline inserted\n      // between them and the following expression.\n      return;\n    }\n    this._output.set_wrap_point();\n  }\n};\n\nBeautifier.prototype.print_newline = function(force_newline, preserve_statement_flags) {\n  if (!preserve_statement_flags) {\n    if (this._flags.last_token.text !== ';' && this._flags.last_token.text !== ',' && this._flags.last_token.text !== '=' && (this._flags.last_token.type !== TOKEN.OPERATOR || this._flags.last_token.text === '--' || this._flags.last_token.text === '++')) {\n      var next_token = this._tokens.peek();\n      while (this._flags.mode === MODE.Statement &&\n        !(this._flags.if_block && reserved_word(next_token, 'else')) &&\n        !this._flags.do_block) {\n        this.restore_mode();\n      }\n    }\n  }\n\n  if (this._output.add_new_line(force_newline)) {\n    this._flags.multiline_frame = true;\n  }\n};\n\nBeautifier.prototype.print_token_line_indentation = function(current_token) {\n  if (this._output.just_added_newline()) {\n    if (this._options.keep_array_indentation &&\n      current_token.newlines &&\n      (current_token.text === '[' || is_array(this._flags.mode))) {\n      this._output.current_line.set_indent(-1);\n      this._output.current_line.push(current_token.whitespace_before);\n      this._output.space_before_token = false;\n    } else if (this._output.set_indent(this._flags.indentation_level, this._flags.alignment)) {\n      this._flags.line_indent_level = this._flags.indentation_level;\n    }\n  }\n};\n\nBeautifier.prototype.print_token = function(current_token) {\n  if (this._output.raw) {\n    this._output.add_raw_token(current_token);\n    return;\n  }\n\n  if (this._options.comma_first && current_token.previous && current_token.previous.type === TOKEN.COMMA &&\n    this._output.just_added_newline()) {\n    if (this._output.previous_line.last() === ',') {\n      var popped = this._output.previous_line.pop();\n      // if the comma was already at the start of the line,\n      // pull back onto that line and reprint the indentation\n      if (this._output.previous_line.is_empty()) {\n        this._output.previous_line.push(popped);\n        this._output.trim(true);\n        this._output.current_line.pop();\n        this._output.trim();\n      }\n\n      // add the comma in front of the next token\n      this.print_token_line_indentation(current_token);\n      this._output.add_token(',');\n      this._output.space_before_token = true;\n    }\n  }\n\n  this.print_token_line_indentation(current_token);\n  this._output.non_breaking_space = true;\n  this._output.add_token(current_token.text);\n  if (this._output.previous_token_wrapped) {\n    this._flags.multiline_frame = true;\n  }\n};\n\nBeautifier.prototype.indent = function() {\n  this._flags.indentation_level += 1;\n  this._output.set_indent(this._flags.indentation_level, this._flags.alignment);\n};\n\nBeautifier.prototype.deindent = function() {\n  if (this._flags.indentation_level > 0 &&\n    ((!this._flags.parent) || this._flags.indentation_level > this._flags.parent.indentation_level)) {\n    this._flags.indentation_level -= 1;\n    this._output.set_indent(this._flags.indentation_level, this._flags.alignment);\n  }\n};\n\nBeautifier.prototype.set_mode = function(mode) {\n  if (this._flags) {\n    this._flag_store.push(this._flags);\n    this._previous_flags = this._flags;\n  } else {\n    this._previous_flags = this.create_flags(null, mode);\n  }\n\n  this._flags = this.create_flags(this._previous_flags, mode);\n  this._output.set_indent(this._flags.indentation_level, this._flags.alignment);\n};\n\n\nBeautifier.prototype.restore_mode = function() {\n  if (this._flag_store.length > 0) {\n    this._previous_flags = this._flags;\n    this._flags = this._flag_store.pop();\n    if (this._previous_flags.mode === MODE.Statement) {\n      remove_redundant_indentation(this._output, this._previous_flags);\n    }\n    this._output.set_indent(this._flags.indentation_level, this._flags.alignment);\n  }\n};\n\nBeautifier.prototype.start_of_object_property = function() {\n  return this._flags.parent.mode === MODE.ObjectLiteral && this._flags.mode === MODE.Statement && (\n    (this._flags.last_token.text === ':' && this._flags.ternary_depth === 0) || (reserved_array(this._flags.last_token, ['get', 'set'])));\n};\n\nBeautifier.prototype.start_of_statement = function(current_token) {\n  var start = false;\n  start = start || reserved_array(this._flags.last_token, ['var', 'let', 'const']) && current_token.type === TOKEN.WORD;\n  start = start || reserved_word(this._flags.last_token, 'do');\n  start = start || (!(this._flags.parent.mode === MODE.ObjectLiteral && this._flags.mode === MODE.Statement)) && reserved_array(this._flags.last_token, newline_restricted_tokens) && !current_token.newlines;\n  start = start || reserved_word(this._flags.last_token, 'else') &&\n    !(reserved_word(current_token, 'if') && !current_token.comments_before);\n  start = start || (this._flags.last_token.type === TOKEN.END_EXPR && (this._previous_flags.mode === MODE.ForInitializer || this._previous_flags.mode === MODE.Conditional));\n  start = start || (this._flags.last_token.type === TOKEN.WORD && this._flags.mode === MODE.BlockStatement &&\n    !this._flags.in_case &&\n    !(current_token.text === '--' || current_token.text === '++') &&\n    this._last_last_text !== 'function' &&\n    current_token.type !== TOKEN.WORD && current_token.type !== TOKEN.RESERVED);\n  start = start || (this._flags.mode === MODE.ObjectLiteral && (\n    (this._flags.last_token.text === ':' && this._flags.ternary_depth === 0) || reserved_array(this._flags.last_token, ['get', 'set'])));\n\n  if (start) {\n    this.set_mode(MODE.Statement);\n    this.indent();\n\n    this.handle_whitespace_and_comments(current_token, true);\n\n    // Issue #276:\n    // If starting a new statement with [if, for, while, do], push to a new line.\n    // if (a) if (b) if(c) d(); else e(); else f();\n    if (!this.start_of_object_property()) {\n      this.allow_wrap_or_preserved_newline(current_token,\n        reserved_array(current_token, ['do', 'for', 'if', 'while']));\n    }\n    return true;\n  }\n  return false;\n};\n\nBeautifier.prototype.handle_start_expr = function(current_token) {\n  // The conditional starts the statement if appropriate.\n  if (!this.start_of_statement(current_token)) {\n    this.handle_whitespace_and_comments(current_token);\n  }\n\n  var next_mode = MODE.Expression;\n  if (current_token.text === '[') {\n\n    if (this._flags.last_token.type === TOKEN.WORD || this._flags.last_token.text === ')') {\n      // this is array index specifier, break immediately\n      // a[x], fn()[x]\n      if (reserved_array(this._flags.last_token, line_starters)) {\n        this._output.space_before_token = true;\n      }\n      this.print_token(current_token);\n      this.set_mode(next_mode);\n      this.indent();\n      if (this._options.space_in_paren) {\n        this._output.space_before_token = true;\n      }\n      return;\n    }\n\n    next_mode = MODE.ArrayLiteral;\n    if (is_array(this._flags.mode)) {\n      if (this._flags.last_token.text === '[' ||\n        (this._flags.last_token.text === ',' && (this._last_last_text === ']' || this._last_last_text === '}'))) {\n        // ], [ goes to new line\n        // }, [ goes to new line\n        if (!this._options.keep_array_indentation) {\n          this.print_newline();\n        }\n      }\n    }\n\n    if (!in_array(this._flags.last_token.type, [TOKEN.START_EXPR, TOKEN.END_EXPR, TOKEN.WORD, TOKEN.OPERATOR, TOKEN.DOT])) {\n      this._output.space_before_token = true;\n    }\n  } else {\n    if (this._flags.last_token.type === TOKEN.RESERVED) {\n      if (this._flags.last_token.text === 'for') {\n        this._output.space_before_token = this._options.space_before_conditional;\n        next_mode = MODE.ForInitializer;\n      } else if (in_array(this._flags.last_token.text, ['if', 'while', 'switch'])) {\n        this._output.space_before_token = this._options.space_before_conditional;\n        next_mode = MODE.Conditional;\n      } else if (in_array(this._flags.last_word, ['await', 'async'])) {\n        // Should be a space between await and an IIFE, or async and an arrow function\n        this._output.space_before_token = true;\n      } else if (this._flags.last_token.text === 'import' && current_token.whitespace_before === '') {\n        this._output.space_before_token = false;\n      } else if (in_array(this._flags.last_token.text, line_starters) || this._flags.last_token.text === 'catch') {\n        this._output.space_before_token = true;\n      }\n    } else if (this._flags.last_token.type === TOKEN.EQUALS || this._flags.last_token.type === TOKEN.OPERATOR) {\n      // Support of this kind of newline preservation.\n      // a = (b &&\n      //     (c || d));\n      if (!this.start_of_object_property()) {\n        this.allow_wrap_or_preserved_newline(current_token);\n      }\n    } else if (this._flags.last_token.type === TOKEN.WORD) {\n      this._output.space_before_token = false;\n\n      // function name() vs function name ()\n      // function* name() vs function* name ()\n      // async name() vs async name ()\n      // In ES6, you can also define the method properties of an object\n      // var obj = {a: function() {}}\n      // It can be abbreviated\n      // var obj = {a() {}}\n      // var obj = { a() {}} vs var obj = { a () {}}\n      // var obj = { * a() {}} vs var obj = { * a () {}}\n      var peek_back_two = this._tokens.peek(-3);\n      if (this._options.space_after_named_function && peek_back_two) {\n        // peek starts at next character so -1 is current token\n        var peek_back_three = this._tokens.peek(-4);\n        if (reserved_array(peek_back_two, ['async', 'function']) ||\n          (peek_back_two.text === '*' && reserved_array(peek_back_three, ['async', 'function']))) {\n          this._output.space_before_token = true;\n        } else if (this._flags.mode === MODE.ObjectLiteral) {\n          if ((peek_back_two.text === '{' || peek_back_two.text === ',') ||\n            (peek_back_two.text === '*' && (peek_back_three.text === '{' || peek_back_three.text === ','))) {\n            this._output.space_before_token = true;\n          }\n        } else if (this._flags.parent && this._flags.parent.class_start_block) {\n          this._output.space_before_token = true;\n        }\n      }\n    } else {\n      // Support preserving wrapped arrow function expressions\n      // a.b('c',\n      //     () => d.e\n      // )\n      this.allow_wrap_or_preserved_newline(current_token);\n    }\n\n    // function() vs function ()\n    // yield*() vs yield* ()\n    // function*() vs function* ()\n    if ((this._flags.last_token.type === TOKEN.RESERVED && (this._flags.last_word === 'function' || this._flags.last_word === 'typeof')) ||\n      (this._flags.last_token.text === '*' &&\n        (in_array(this._last_last_text, ['function', 'yield']) ||\n          (this._flags.mode === MODE.ObjectLiteral && in_array(this._last_last_text, ['{', ',']))))) {\n      this._output.space_before_token = this._options.space_after_anon_function;\n    }\n  }\n\n  if (this._flags.last_token.text === ';' || this._flags.last_token.type === TOKEN.START_BLOCK) {\n    this.print_newline();\n  } else if (this._flags.last_token.type === TOKEN.END_EXPR || this._flags.last_token.type === TOKEN.START_EXPR || this._flags.last_token.type === TOKEN.END_BLOCK || this._flags.last_token.text === '.' || this._flags.last_token.type === TOKEN.COMMA) {\n    // do nothing on (( and )( and ][ and ]( and .(\n    // TODO: Consider whether forcing this is required.  Review failing tests when removed.\n    this.allow_wrap_or_preserved_newline(current_token, current_token.newlines);\n  }\n\n  this.print_token(current_token);\n  this.set_mode(next_mode);\n  if (this._options.space_in_paren) {\n    this._output.space_before_token = true;\n  }\n\n  // In all cases, if we newline while inside an expression it should be indented.\n  this.indent();\n};\n\nBeautifier.prototype.handle_end_expr = function(current_token) {\n  // statements inside expressions are not valid syntax, but...\n  // statements must all be closed when their container closes\n  while (this._flags.mode === MODE.Statement) {\n    this.restore_mode();\n  }\n\n  this.handle_whitespace_and_comments(current_token);\n\n  if (this._flags.multiline_frame) {\n    this.allow_wrap_or_preserved_newline(current_token,\n      current_token.text === ']' && is_array(this._flags.mode) && !this._options.keep_array_indentation);\n  }\n\n  if (this._options.space_in_paren) {\n    if (this._flags.last_token.type === TOKEN.START_EXPR && !this._options.space_in_empty_paren) {\n      // () [] no inner space in empty parens like these, ever, ref #320\n      this._output.trim();\n      this._output.space_before_token = false;\n    } else {\n      this._output.space_before_token = true;\n    }\n  }\n  this.deindent();\n  this.print_token(current_token);\n  this.restore_mode();\n\n  remove_redundant_indentation(this._output, this._previous_flags);\n\n  // do {} while () // no statement required after\n  if (this._flags.do_while && this._previous_flags.mode === MODE.Conditional) {\n    this._previous_flags.mode = MODE.Expression;\n    this._flags.do_block = false;\n    this._flags.do_while = false;\n\n  }\n};\n\nBeautifier.prototype.handle_start_block = function(current_token) {\n  this.handle_whitespace_and_comments(current_token);\n\n  // Check if this is should be treated as a ObjectLiteral\n  var next_token = this._tokens.peek();\n  var second_token = this._tokens.peek(1);\n  if (this._flags.last_word === 'switch' && this._flags.last_token.type === TOKEN.END_EXPR) {\n    this.set_mode(MODE.BlockStatement);\n    this._flags.in_case_statement = true;\n  } else if (this._flags.case_body) {\n    this.set_mode(MODE.BlockStatement);\n  } else if (second_token && (\n      (in_array(second_token.text, [':', ',']) && in_array(next_token.type, [TOKEN.STRING, TOKEN.WORD, TOKEN.RESERVED])) ||\n      (in_array(next_token.text, ['get', 'set', '...']) && in_array(second_token.type, [TOKEN.WORD, TOKEN.RESERVED]))\n    )) {\n    // We don't support TypeScript,but we didn't break it for a very long time.\n    // We'll try to keep not breaking it.\n    if (in_array(this._last_last_text, ['class', 'interface']) && !in_array(second_token.text, [':', ','])) {\n      this.set_mode(MODE.BlockStatement);\n    } else {\n      this.set_mode(MODE.ObjectLiteral);\n    }\n  } else if (this._flags.last_token.type === TOKEN.OPERATOR && this._flags.last_token.text === '=>') {\n    // arrow function: (param1, paramN) => { statements }\n    this.set_mode(MODE.BlockStatement);\n  } else if (in_array(this._flags.last_token.type, [TOKEN.EQUALS, TOKEN.START_EXPR, TOKEN.COMMA, TOKEN.OPERATOR]) ||\n    reserved_array(this._flags.last_token, ['return', 'throw', 'import', 'default'])\n  ) {\n    // Detecting shorthand function syntax is difficult by scanning forward,\n    //     so check the surrounding context.\n    // If the block is being returned, imported, export default, passed as arg,\n    //     assigned with = or assigned in a nested object, treat as an ObjectLiteral.\n    this.set_mode(MODE.ObjectLiteral);\n  } else {\n    this.set_mode(MODE.BlockStatement);\n  }\n\n  if (this._flags.last_token) {\n    if (reserved_array(this._flags.last_token.previous, ['class', 'extends'])) {\n      this._flags.class_start_block = true;\n    }\n  }\n\n  var empty_braces = !next_token.comments_before && next_token.text === '}';\n  var empty_anonymous_function = empty_braces && this._flags.last_word === 'function' &&\n    this._flags.last_token.type === TOKEN.END_EXPR;\n\n  if (this._options.brace_preserve_inline) // check for inline, set inline_frame if so\n  {\n    // search forward for a newline wanted inside this block\n    var index = 0;\n    var check_token = null;\n    this._flags.inline_frame = true;\n    do {\n      index += 1;\n      check_token = this._tokens.peek(index - 1);\n      if (check_token.newlines) {\n        this._flags.inline_frame = false;\n        break;\n      }\n    } while (check_token.type !== TOKEN.EOF &&\n      !(check_token.type === TOKEN.END_BLOCK && check_token.opened === current_token));\n  }\n\n  if ((this._options.brace_style === \"expand\" ||\n      (this._options.brace_style === \"none\" && current_token.newlines)) &&\n    !this._flags.inline_frame) {\n    if (this._flags.last_token.type !== TOKEN.OPERATOR &&\n      (empty_anonymous_function ||\n        this._flags.last_token.type === TOKEN.EQUALS ||\n        (reserved_array(this._flags.last_token, special_words) && this._flags.last_token.text !== 'else'))) {\n      this._output.space_before_token = true;\n    } else {\n      this.print_newline(false, true);\n    }\n  } else { // collapse || inline_frame\n    if (is_array(this._previous_flags.mode) && (this._flags.last_token.type === TOKEN.START_EXPR || this._flags.last_token.type === TOKEN.COMMA)) {\n      if (this._flags.last_token.type === TOKEN.COMMA || this._options.space_in_paren) {\n        this._output.space_before_token = true;\n      }\n\n      if (this._flags.last_token.type === TOKEN.COMMA || (this._flags.last_token.type === TOKEN.START_EXPR && this._flags.inline_frame)) {\n        this.allow_wrap_or_preserved_newline(current_token);\n        this._previous_flags.multiline_frame = this._previous_flags.multiline_frame || this._flags.multiline_frame;\n        this._flags.multiline_frame = false;\n      }\n    }\n    if (this._flags.last_token.type !== TOKEN.OPERATOR && this._flags.last_token.type !== TOKEN.START_EXPR) {\n      if (in_array(this._flags.last_token.type, [TOKEN.START_BLOCK, TOKEN.SEMICOLON]) && !this._flags.inline_frame) {\n        this.print_newline();\n      } else {\n        this._output.space_before_token = true;\n      }\n    }\n  }\n  this.print_token(current_token);\n  this.indent();\n\n  // Except for specific cases, open braces are followed by a new line.\n  if (!empty_braces && !(this._options.brace_preserve_inline && this._flags.inline_frame)) {\n    this.print_newline();\n  }\n};\n\nBeautifier.prototype.handle_end_block = function(current_token) {\n  // statements must all be closed when their container closes\n  this.handle_whitespace_and_comments(current_token);\n\n  while (this._flags.mode === MODE.Statement) {\n    this.restore_mode();\n  }\n\n  var empty_braces = this._flags.last_token.type === TOKEN.START_BLOCK;\n\n  if (this._flags.inline_frame && !empty_braces) { // try inline_frame (only set if this._options.braces-preserve-inline) first\n    this._output.space_before_token = true;\n  } else if (this._options.brace_style === \"expand\") {\n    if (!empty_braces) {\n      this.print_newline();\n    }\n  } else {\n    // skip {}\n    if (!empty_braces) {\n      if (is_array(this._flags.mode) && this._options.keep_array_indentation) {\n        // we REALLY need a newline here, but newliner would skip that\n        this._options.keep_array_indentation = false;\n        this.print_newline();\n        this._options.keep_array_indentation = true;\n\n      } else {\n        this.print_newline();\n      }\n    }\n  }\n  this.restore_mode();\n  this.print_token(current_token);\n};\n\nBeautifier.prototype.handle_word = function(current_token) {\n  if (current_token.type === TOKEN.RESERVED) {\n    if (in_array(current_token.text, ['set', 'get']) && this._flags.mode !== MODE.ObjectLiteral) {\n      current_token.type = TOKEN.WORD;\n    } else if (current_token.text === 'import' && in_array(this._tokens.peek().text, ['(', '.'])) {\n      current_token.type = TOKEN.WORD;\n    } else if (in_array(current_token.text, ['as', 'from']) && !this._flags.import_block) {\n      current_token.type = TOKEN.WORD;\n    } else if (this._flags.mode === MODE.ObjectLiteral) {\n      var next_token = this._tokens.peek();\n      if (next_token.text === ':') {\n        current_token.type = TOKEN.WORD;\n      }\n    }\n  }\n\n  if (this.start_of_statement(current_token)) {\n    // The conditional starts the statement if appropriate.\n    if (reserved_array(this._flags.last_token, ['var', 'let', 'const']) && current_token.type === TOKEN.WORD) {\n      this._flags.declaration_statement = true;\n    }\n  } else if (current_token.newlines && !is_expression(this._flags.mode) &&\n    (this._flags.last_token.type !== TOKEN.OPERATOR || (this._flags.last_token.text === '--' || this._flags.last_token.text === '++')) &&\n    this._flags.last_token.type !== TOKEN.EQUALS &&\n    (this._options.preserve_newlines || !reserved_array(this._flags.last_token, ['var', 'let', 'const', 'set', 'get']))) {\n    this.handle_whitespace_and_comments(current_token);\n    this.print_newline();\n  } else {\n    this.handle_whitespace_and_comments(current_token);\n  }\n\n  if (this._flags.do_block && !this._flags.do_while) {\n    if (reserved_word(current_token, 'while')) {\n      // do {} ## while ()\n      this._output.space_before_token = true;\n      this.print_token(current_token);\n      this._output.space_before_token = true;\n      this._flags.do_while = true;\n      return;\n    } else {\n      // do {} should always have while as the next word.\n      // if we don't see the expected while, recover\n      this.print_newline();\n      this._flags.do_block = false;\n    }\n  }\n\n  // if may be followed by else, or not\n  // Bare/inline ifs are tricky\n  // Need to unwind the modes correctly: if (a) if (b) c(); else d(); else e();\n  if (this._flags.if_block) {\n    if (!this._flags.else_block && reserved_word(current_token, 'else')) {\n      this._flags.else_block = true;\n    } else {\n      while (this._flags.mode === MODE.Statement) {\n        this.restore_mode();\n      }\n      this._flags.if_block = false;\n      this._flags.else_block = false;\n    }\n  }\n\n  if (this._flags.in_case_statement && reserved_array(current_token, ['case', 'default'])) {\n    this.print_newline();\n    if (!this._flags.case_block && (this._flags.case_body || this._options.jslint_happy)) {\n      // switch cases following one another\n      this.deindent();\n    }\n    this._flags.case_body = false;\n\n    this.print_token(current_token);\n    this._flags.in_case = true;\n    return;\n  }\n\n  if (this._flags.last_token.type === TOKEN.COMMA || this._flags.last_token.type === TOKEN.START_EXPR || this._flags.last_token.type === TOKEN.EQUALS || this._flags.last_token.type === TOKEN.OPERATOR) {\n    if (!this.start_of_object_property() && !(\n        // start of object property is different for numeric values with +/- prefix operators\n        in_array(this._flags.last_token.text, ['+', '-']) && this._last_last_text === ':' && this._flags.parent.mode === MODE.ObjectLiteral)) {\n      this.allow_wrap_or_preserved_newline(current_token);\n    }\n  }\n\n  if (reserved_word(current_token, 'function')) {\n    if (in_array(this._flags.last_token.text, ['}', ';']) ||\n      (this._output.just_added_newline() && !(in_array(this._flags.last_token.text, ['(', '[', '{', ':', '=', ',']) || this._flags.last_token.type === TOKEN.OPERATOR))) {\n      // make sure there is a nice clean space of at least one blank line\n      // before a new function definition\n      if (!this._output.just_added_blankline() && !current_token.comments_before) {\n        this.print_newline();\n        this.print_newline(true);\n      }\n    }\n    if (this._flags.last_token.type === TOKEN.RESERVED || this._flags.last_token.type === TOKEN.WORD) {\n      if (reserved_array(this._flags.last_token, ['get', 'set', 'new', 'export']) ||\n        reserved_array(this._flags.last_token, newline_restricted_tokens)) {\n        this._output.space_before_token = true;\n      } else if (reserved_word(this._flags.last_token, 'default') && this._last_last_text === 'export') {\n        this._output.space_before_token = true;\n      } else if (this._flags.last_token.text === 'declare') {\n        // accomodates Typescript declare function formatting\n        this._output.space_before_token = true;\n      } else {\n        this.print_newline();\n      }\n    } else if (this._flags.last_token.type === TOKEN.OPERATOR || this._flags.last_token.text === '=') {\n      // foo = function\n      this._output.space_before_token = true;\n    } else if (!this._flags.multiline_frame && (is_expression(this._flags.mode) || is_array(this._flags.mode))) {\n      // (function\n    } else {\n      this.print_newline();\n    }\n\n    this.print_token(current_token);\n    this._flags.last_word = current_token.text;\n    return;\n  }\n\n  var prefix = 'NONE';\n\n  if (this._flags.last_token.type === TOKEN.END_BLOCK) {\n\n    if (this._previous_flags.inline_frame) {\n      prefix = 'SPACE';\n    } else if (!reserved_array(current_token, ['else', 'catch', 'finally', 'from'])) {\n      prefix = 'NEWLINE';\n    } else {\n      if (this._options.brace_style === \"expand\" ||\n        this._options.brace_style === \"end-expand\" ||\n        (this._options.brace_style === \"none\" && current_token.newlines)) {\n        prefix = 'NEWLINE';\n      } else {\n        prefix = 'SPACE';\n        this._output.space_before_token = true;\n      }\n    }\n  } else if (this._flags.last_token.type === TOKEN.SEMICOLON && this._flags.mode === MODE.BlockStatement) {\n    // TODO: Should this be for STATEMENT as well?\n    prefix = 'NEWLINE';\n  } else if (this._flags.last_token.type === TOKEN.SEMICOLON && is_expression(this._flags.mode)) {\n    prefix = 'SPACE';\n  } else if (this._flags.last_token.type === TOKEN.STRING) {\n    prefix = 'NEWLINE';\n  } else if (this._flags.last_token.type === TOKEN.RESERVED || this._flags.last_token.type === TOKEN.WORD ||\n    (this._flags.last_token.text === '*' &&\n      (in_array(this._last_last_text, ['function', 'yield']) ||\n        (this._flags.mode === MODE.ObjectLiteral && in_array(this._last_last_text, ['{', ',']))))) {\n    prefix = 'SPACE';\n  } else if (this._flags.last_token.type === TOKEN.START_BLOCK) {\n    if (this._flags.inline_frame) {\n      prefix = 'SPACE';\n    } else {\n      prefix = 'NEWLINE';\n    }\n  } else if (this._flags.last_token.type === TOKEN.END_EXPR) {\n    this._output.space_before_token = true;\n    prefix = 'NEWLINE';\n  }\n\n  if (reserved_array(current_token, line_starters) && this._flags.last_token.text !== ')') {\n    if (this._flags.inline_frame || this._flags.last_token.text === 'else' || this._flags.last_token.text === 'export') {\n      prefix = 'SPACE';\n    } else {\n      prefix = 'NEWLINE';\n    }\n\n  }\n\n  if (reserved_array(current_token, ['else', 'catch', 'finally'])) {\n    if ((!(this._flags.last_token.type === TOKEN.END_BLOCK && this._previous_flags.mode === MODE.BlockStatement) ||\n        this._options.brace_style === \"expand\" ||\n        this._options.brace_style === \"end-expand\" ||\n        (this._options.brace_style === \"none\" && current_token.newlines)) &&\n      !this._flags.inline_frame) {\n      this.print_newline();\n    } else {\n      this._output.trim(true);\n      var line = this._output.current_line;\n      // If we trimmed and there's something other than a close block before us\n      // put a newline back in.  Handles '} // comment' scenario.\n      if (line.last() !== '}') {\n        this.print_newline();\n      }\n      this._output.space_before_token = true;\n    }\n  } else if (prefix === 'NEWLINE') {\n    if (reserved_array(this._flags.last_token, special_words)) {\n      // no newline between 'return nnn'\n      this._output.space_before_token = true;\n    } else if (this._flags.last_token.text === 'declare' && reserved_array(current_token, ['var', 'let', 'const'])) {\n      // accomodates Typescript declare formatting\n      this._output.space_before_token = true;\n    } else if (this._flags.last_token.type !== TOKEN.END_EXPR) {\n      if ((this._flags.last_token.type !== TOKEN.START_EXPR || !reserved_array(current_token, ['var', 'let', 'const'])) && this._flags.last_token.text !== ':') {\n        // no need to force newline on 'var': for (var x = 0...)\n        if (reserved_word(current_token, 'if') && reserved_word(current_token.previous, 'else')) {\n          // no newline for } else if {\n          this._output.space_before_token = true;\n        } else {\n          this.print_newline();\n        }\n      }\n    } else if (reserved_array(current_token, line_starters) && this._flags.last_token.text !== ')') {\n      this.print_newline();\n    }\n  } else if (this._flags.multiline_frame && is_array(this._flags.mode) && this._flags.last_token.text === ',' && this._last_last_text === '}') {\n    this.print_newline(); // }, in lists get a newline treatment\n  } else if (prefix === 'SPACE') {\n    this._output.space_before_token = true;\n  }\n  if (current_token.previous && (current_token.previous.type === TOKEN.WORD || current_token.previous.type === TOKEN.RESERVED)) {\n    this._output.space_before_token = true;\n  }\n  this.print_token(current_token);\n  this._flags.last_word = current_token.text;\n\n  if (current_token.type === TOKEN.RESERVED) {\n    if (current_token.text === 'do') {\n      this._flags.do_block = true;\n    } else if (current_token.text === 'if') {\n      this._flags.if_block = true;\n    } else if (current_token.text === 'import') {\n      this._flags.import_block = true;\n    } else if (this._flags.import_block && reserved_word(current_token, 'from')) {\n      this._flags.import_block = false;\n    }\n  }\n};\n\nBeautifier.prototype.handle_semicolon = function(current_token) {\n  if (this.start_of_statement(current_token)) {\n    // The conditional starts the statement if appropriate.\n    // Semicolon can be the start (and end) of a statement\n    this._output.space_before_token = false;\n  } else {\n    this.handle_whitespace_and_comments(current_token);\n  }\n\n  var next_token = this._tokens.peek();\n  while (this._flags.mode === MODE.Statement &&\n    !(this._flags.if_block && reserved_word(next_token, 'else')) &&\n    !this._flags.do_block) {\n    this.restore_mode();\n  }\n\n  // hacky but effective for the moment\n  if (this._flags.import_block) {\n    this._flags.import_block = false;\n  }\n  this.print_token(current_token);\n};\n\nBeautifier.prototype.handle_string = function(current_token) {\n  if (current_token.text.startsWith(\"`\") && current_token.newlines === 0 && current_token.whitespace_before === '' && (current_token.previous.text === ')' || this._flags.last_token.type === TOKEN.WORD)) {\n    //Conditional for detectign backtick strings\n  } else if (this.start_of_statement(current_token)) {\n    // The conditional starts the statement if appropriate.\n    // One difference - strings want at least a space before\n    this._output.space_before_token = true;\n  } else {\n    this.handle_whitespace_and_comments(current_token);\n    if (this._flags.last_token.type === TOKEN.RESERVED || this._flags.last_token.type === TOKEN.WORD || this._flags.inline_frame) {\n      this._output.space_before_token = true;\n    } else if (this._flags.last_token.type === TOKEN.COMMA || this._flags.last_token.type === TOKEN.START_EXPR || this._flags.last_token.type === TOKEN.EQUALS || this._flags.last_token.type === TOKEN.OPERATOR) {\n      if (!this.start_of_object_property()) {\n        this.allow_wrap_or_preserved_newline(current_token);\n      }\n    } else if ((current_token.text.startsWith(\"`\") && this._flags.last_token.type === TOKEN.END_EXPR && (current_token.previous.text === ']' || current_token.previous.text === ')') && current_token.newlines === 0)) {\n      this._output.space_before_token = true;\n    } else {\n      this.print_newline();\n    }\n  }\n  this.print_token(current_token);\n};\n\nBeautifier.prototype.handle_equals = function(current_token) {\n  if (this.start_of_statement(current_token)) {\n    // The conditional starts the statement if appropriate.\n  } else {\n    this.handle_whitespace_and_comments(current_token);\n  }\n\n  if (this._flags.declaration_statement) {\n    // just got an '=' in a var-line, different formatting/line-breaking, etc will now be done\n    this._flags.declaration_assignment = true;\n  }\n  this._output.space_before_token = true;\n  this.print_token(current_token);\n  this._output.space_before_token = true;\n};\n\nBeautifier.prototype.handle_comma = function(current_token) {\n  this.handle_whitespace_and_comments(current_token, true);\n\n  this.print_token(current_token);\n  this._output.space_before_token = true;\n  if (this._flags.declaration_statement) {\n    if (is_expression(this._flags.parent.mode)) {\n      // do not break on comma, for(var a = 1, b = 2)\n      this._flags.declaration_assignment = false;\n    }\n\n    if (this._flags.declaration_assignment) {\n      this._flags.declaration_assignment = false;\n      this.print_newline(false, true);\n    } else if (this._options.comma_first) {\n      // for comma-first, we want to allow a newline before the comma\n      // to turn into a newline after the comma, which we will fixup later\n      this.allow_wrap_or_preserved_newline(current_token);\n    }\n  } else if (this._flags.mode === MODE.ObjectLiteral ||\n    (this._flags.mode === MODE.Statement && this._flags.parent.mode === MODE.ObjectLiteral)) {\n    if (this._flags.mode === MODE.Statement) {\n      this.restore_mode();\n    }\n\n    if (!this._flags.inline_frame) {\n      this.print_newline();\n    }\n  } else if (this._options.comma_first) {\n    // EXPR or DO_BLOCK\n    // for comma-first, we want to allow a newline before the comma\n    // to turn into a newline after the comma, which we will fixup later\n    this.allow_wrap_or_preserved_newline(current_token);\n  }\n};\n\nBeautifier.prototype.handle_operator = function(current_token) {\n  var isGeneratorAsterisk = current_token.text === '*' &&\n    (reserved_array(this._flags.last_token, ['function', 'yield']) ||\n      (in_array(this._flags.last_token.type, [TOKEN.START_BLOCK, TOKEN.COMMA, TOKEN.END_BLOCK, TOKEN.SEMICOLON]))\n    );\n  var isUnary = in_array(current_token.text, ['-', '+']) && (\n    in_array(this._flags.last_token.type, [TOKEN.START_BLOCK, TOKEN.START_EXPR, TOKEN.EQUALS, TOKEN.OPERATOR]) ||\n    in_array(this._flags.last_token.text, line_starters) ||\n    this._flags.last_token.text === ','\n  );\n\n  if (this.start_of_statement(current_token)) {\n    // The conditional starts the statement if appropriate.\n  } else {\n    var preserve_statement_flags = !isGeneratorAsterisk;\n    this.handle_whitespace_and_comments(current_token, preserve_statement_flags);\n  }\n\n  // hack for actionscript's import .*;\n  if (current_token.text === '*' && this._flags.last_token.type === TOKEN.DOT) {\n    this.print_token(current_token);\n    return;\n  }\n\n  if (current_token.text === '::') {\n    // no spaces around exotic namespacing syntax operator\n    this.print_token(current_token);\n    return;\n  }\n\n  if (in_array(current_token.text, ['-', '+']) && this.start_of_object_property()) {\n    // numeric value with +/- symbol in front as a property\n    this.print_token(current_token);\n    return;\n  }\n\n  // Allow line wrapping between operators when operator_position is\n  //   set to before or preserve\n  if (this._flags.last_token.type === TOKEN.OPERATOR && in_array(this._options.operator_position, OPERATOR_POSITION_BEFORE_OR_PRESERVE)) {\n    this.allow_wrap_or_preserved_newline(current_token);\n  }\n\n  if (current_token.text === ':' && this._flags.in_case) {\n    this.print_token(current_token);\n\n    this._flags.in_case = false;\n    this._flags.case_body = true;\n    if (this._tokens.peek().type !== TOKEN.START_BLOCK) {\n      this.indent();\n      this.print_newline();\n      this._flags.case_block = false;\n    } else {\n      this._flags.case_block = true;\n      this._output.space_before_token = true;\n    }\n    return;\n  }\n\n  var space_before = true;\n  var space_after = true;\n  var in_ternary = false;\n  if (current_token.text === ':') {\n    if (this._flags.ternary_depth === 0) {\n      // Colon is invalid javascript outside of ternary and object, but do our best to guess what was meant.\n      space_before = false;\n    } else {\n      this._flags.ternary_depth -= 1;\n      in_ternary = true;\n    }\n  } else if (current_token.text === '?') {\n    this._flags.ternary_depth += 1;\n  }\n\n  // let's handle the operator_position option prior to any conflicting logic\n  if (!isUnary && !isGeneratorAsterisk && this._options.preserve_newlines && in_array(current_token.text, positionable_operators)) {\n    var isColon = current_token.text === ':';\n    var isTernaryColon = (isColon && in_ternary);\n    var isOtherColon = (isColon && !in_ternary);\n\n    switch (this._options.operator_position) {\n      case OPERATOR_POSITION.before_newline:\n        // if the current token is : and it's not a ternary statement then we set space_before to false\n        this._output.space_before_token = !isOtherColon;\n\n        this.print_token(current_token);\n\n        if (!isColon || isTernaryColon) {\n          this.allow_wrap_or_preserved_newline(current_token);\n        }\n\n        this._output.space_before_token = true;\n        return;\n\n      case OPERATOR_POSITION.after_newline:\n        // if the current token is anything but colon, or (via deduction) it's a colon and in a ternary statement,\n        //   then print a newline.\n\n        this._output.space_before_token = true;\n\n        if (!isColon || isTernaryColon) {\n          if (this._tokens.peek().newlines) {\n            this.print_newline(false, true);\n          } else {\n            this.allow_wrap_or_preserved_newline(current_token);\n          }\n        } else {\n          this._output.space_before_token = false;\n        }\n\n        this.print_token(current_token);\n\n        this._output.space_before_token = true;\n        return;\n\n      case OPERATOR_POSITION.preserve_newline:\n        if (!isOtherColon) {\n          this.allow_wrap_or_preserved_newline(current_token);\n        }\n\n        // if we just added a newline, or the current token is : and it's not a ternary statement,\n        //   then we set space_before to false\n        space_before = !(this._output.just_added_newline() || isOtherColon);\n\n        this._output.space_before_token = space_before;\n        this.print_token(current_token);\n        this._output.space_before_token = true;\n        return;\n    }\n  }\n\n  if (isGeneratorAsterisk) {\n    this.allow_wrap_or_preserved_newline(current_token);\n    space_before = false;\n    var next_token = this._tokens.peek();\n    space_after = next_token && in_array(next_token.type, [TOKEN.WORD, TOKEN.RESERVED]);\n  } else if (current_token.text === '...') {\n    this.allow_wrap_or_preserved_newline(current_token);\n    space_before = this._flags.last_token.type === TOKEN.START_BLOCK;\n    space_after = false;\n  } else if (in_array(current_token.text, ['--', '++', '!', '~']) || isUnary) {\n    // unary operators (and binary +/- pretending to be unary) special cases\n    if (this._flags.last_token.type === TOKEN.COMMA || this._flags.last_token.type === TOKEN.START_EXPR) {\n      this.allow_wrap_or_preserved_newline(current_token);\n    }\n\n    space_before = false;\n    space_after = false;\n\n    // http://www.ecma-international.org/ecma-262/5.1/#sec-7.9.1\n    // if there is a newline between -- or ++ and anything else we should preserve it.\n    if (current_token.newlines && (current_token.text === '--' || current_token.text === '++' || current_token.text === '~')) {\n      var new_line_needed = reserved_array(this._flags.last_token, special_words) && current_token.newlines;\n      if (new_line_needed && (this._previous_flags.if_block || this._previous_flags.else_block)) {\n        this.restore_mode();\n      }\n      this.print_newline(new_line_needed, true);\n    }\n\n    if (this._flags.last_token.text === ';' && is_expression(this._flags.mode)) {\n      // for (;; ++i)\n      //        ^^^\n      space_before = true;\n    }\n\n    if (this._flags.last_token.type === TOKEN.RESERVED) {\n      space_before = true;\n    } else if (this._flags.last_token.type === TOKEN.END_EXPR) {\n      space_before = !(this._flags.last_token.text === ']' && (current_token.text === '--' || current_token.text === '++'));\n    } else if (this._flags.last_token.type === TOKEN.OPERATOR) {\n      // a++ + ++b;\n      // a - -b\n      space_before = in_array(current_token.text, ['--', '-', '++', '+']) && in_array(this._flags.last_token.text, ['--', '-', '++', '+']);\n      // + and - are not unary when preceeded by -- or ++ operator\n      // a-- + b\n      // a * +b\n      // a - -b\n      if (in_array(current_token.text, ['+', '-']) && in_array(this._flags.last_token.text, ['--', '++'])) {\n        space_after = true;\n      }\n    }\n\n\n    if (((this._flags.mode === MODE.BlockStatement && !this._flags.inline_frame) || this._flags.mode === MODE.Statement) &&\n      (this._flags.last_token.text === '{' || this._flags.last_token.text === ';')) {\n      // { foo; --i }\n      // foo(); --bar;\n      this.print_newline();\n    }\n  }\n\n  this._output.space_before_token = this._output.space_before_token || space_before;\n  this.print_token(current_token);\n  this._output.space_before_token = space_after;\n};\n\nBeautifier.prototype.handle_block_comment = function(current_token, preserve_statement_flags) {\n  if (this._output.raw) {\n    this._output.add_raw_token(current_token);\n    if (current_token.directives && current_token.directives.preserve === 'end') {\n      // If we're testing the raw output behavior, do not allow a directive to turn it off.\n      this._output.raw = this._options.test_output_raw;\n    }\n    return;\n  }\n\n  if (current_token.directives) {\n    this.print_newline(false, preserve_statement_flags);\n    this.print_token(current_token);\n    if (current_token.directives.preserve === 'start') {\n      this._output.raw = true;\n    }\n    this.print_newline(false, true);\n    return;\n  }\n\n  // inline block\n  if (!acorn.newline.test(current_token.text) && !current_token.newlines) {\n    this._output.space_before_token = true;\n    this.print_token(current_token);\n    this._output.space_before_token = true;\n    return;\n  } else {\n    this.print_block_commment(current_token, preserve_statement_flags);\n  }\n};\n\nBeautifier.prototype.print_block_commment = function(current_token, preserve_statement_flags) {\n  var lines = split_linebreaks(current_token.text);\n  var j; // iterator for this case\n  var javadoc = false;\n  var starless = false;\n  var lastIndent = current_token.whitespace_before;\n  var lastIndentLength = lastIndent.length;\n\n  // block comment starts with a new line\n  this.print_newline(false, preserve_statement_flags);\n\n  // first line always indented\n  this.print_token_line_indentation(current_token);\n  this._output.add_token(lines[0]);\n  this.print_newline(false, preserve_statement_flags);\n\n\n  if (lines.length > 1) {\n    lines = lines.slice(1);\n    javadoc = all_lines_start_with(lines, '*');\n    starless = each_line_matches_indent(lines, lastIndent);\n\n    if (javadoc) {\n      this._flags.alignment = 1;\n    }\n\n    for (j = 0; j < lines.length; j++) {\n      if (javadoc) {\n        // javadoc: reformat and re-indent\n        this.print_token_line_indentation(current_token);\n        this._output.add_token(ltrim(lines[j]));\n      } else if (starless && lines[j]) {\n        // starless: re-indent non-empty content, avoiding trim\n        this.print_token_line_indentation(current_token);\n        this._output.add_token(lines[j].substring(lastIndentLength));\n      } else {\n        // normal comments output raw\n        this._output.current_line.set_indent(-1);\n        this._output.add_token(lines[j]);\n      }\n\n      // for comments on their own line or  more than one line, make sure there's a new line after\n      this.print_newline(false, preserve_statement_flags);\n    }\n\n    this._flags.alignment = 0;\n  }\n};\n\n\nBeautifier.prototype.handle_comment = function(current_token, preserve_statement_flags) {\n  if (current_token.newlines) {\n    this.print_newline(false, preserve_statement_flags);\n  } else {\n    this._output.trim(true);\n  }\n\n  this._output.space_before_token = true;\n  this.print_token(current_token);\n  this.print_newline(false, preserve_statement_flags);\n};\n\nBeautifier.prototype.handle_dot = function(current_token) {\n  if (this.start_of_statement(current_token)) {\n    // The conditional starts the statement if appropriate.\n  } else {\n    this.handle_whitespace_and_comments(current_token, true);\n  }\n\n  if (this._flags.last_token.text.match('^[0-9]+$')) {\n    this._output.space_before_token = true;\n  }\n\n  if (reserved_array(this._flags.last_token, special_words)) {\n    this._output.space_before_token = false;\n  } else {\n    // allow preserved newlines before dots in general\n    // force newlines on dots after close paren when break_chained - for bar().baz()\n    this.allow_wrap_or_preserved_newline(current_token,\n      this._flags.last_token.text === ')' && this._options.break_chained_methods);\n  }\n\n  // Only unindent chained method dot if this dot starts a new line.\n  // Otherwise the automatic extra indentation removal will handle the over indent\n  if (this._options.unindent_chained_methods && this._output.just_added_newline()) {\n    this.deindent();\n  }\n\n  this.print_token(current_token);\n};\n\nBeautifier.prototype.handle_unknown = function(current_token, preserve_statement_flags) {\n  this.print_token(current_token);\n\n  if (current_token.text[current_token.text.length - 1] === '\\n') {\n    this.print_newline(false, preserve_statement_flags);\n  }\n};\n\nBeautifier.prototype.handle_eof = function(current_token) {\n  // Unwind any open statements\n  while (this._flags.mode === MODE.Statement) {\n    this.restore_mode();\n  }\n  this.handle_whitespace_and_comments(current_token);\n};\n\nmodule.exports.Beautifier = Beautifier;\n\n\n/***/ }),\n/* 2 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nfunction OutputLine(parent) {\n  this.__parent = parent;\n  this.__character_count = 0;\n  // use indent_count as a marker for this.__lines that have preserved indentation\n  this.__indent_count = -1;\n  this.__alignment_count = 0;\n  this.__wrap_point_index = 0;\n  this.__wrap_point_character_count = 0;\n  this.__wrap_point_indent_count = -1;\n  this.__wrap_point_alignment_count = 0;\n\n  this.__items = [];\n}\n\nOutputLine.prototype.clone_empty = function() {\n  var line = new OutputLine(this.__parent);\n  line.set_indent(this.__indent_count, this.__alignment_count);\n  return line;\n};\n\nOutputLine.prototype.item = function(index) {\n  if (index < 0) {\n    return this.__items[this.__items.length + index];\n  } else {\n    return this.__items[index];\n  }\n};\n\nOutputLine.prototype.has_match = function(pattern) {\n  for (var lastCheckedOutput = this.__items.length - 1; lastCheckedOutput >= 0; lastCheckedOutput--) {\n    if (this.__items[lastCheckedOutput].match(pattern)) {\n      return true;\n    }\n  }\n  return false;\n};\n\nOutputLine.prototype.set_indent = function(indent, alignment) {\n  if (this.is_empty()) {\n    this.__indent_count = indent || 0;\n    this.__alignment_count = alignment || 0;\n    this.__character_count = this.__parent.get_indent_size(this.__indent_count, this.__alignment_count);\n  }\n};\n\nOutputLine.prototype._set_wrap_point = function() {\n  if (this.__parent.wrap_line_length) {\n    this.__wrap_point_index = this.__items.length;\n    this.__wrap_point_character_count = this.__character_count;\n    this.__wrap_point_indent_count = this.__parent.next_line.__indent_count;\n    this.__wrap_point_alignment_count = this.__parent.next_line.__alignment_count;\n  }\n};\n\nOutputLine.prototype._should_wrap = function() {\n  return this.__wrap_point_index &&\n    this.__character_count > this.__parent.wrap_line_length &&\n    this.__wrap_point_character_count > this.__parent.next_line.__character_count;\n};\n\nOutputLine.prototype._allow_wrap = function() {\n  if (this._should_wrap()) {\n    this.__parent.add_new_line();\n    var next = this.__parent.current_line;\n    next.set_indent(this.__wrap_point_indent_count, this.__wrap_point_alignment_count);\n    next.__items = this.__items.slice(this.__wrap_point_index);\n    this.__items = this.__items.slice(0, this.__wrap_point_index);\n\n    next.__character_count += this.__character_count - this.__wrap_point_character_count;\n    this.__character_count = this.__wrap_point_character_count;\n\n    if (next.__items[0] === \" \") {\n      next.__items.splice(0, 1);\n      next.__character_count -= 1;\n    }\n    return true;\n  }\n  return false;\n};\n\nOutputLine.prototype.is_empty = function() {\n  return this.__items.length === 0;\n};\n\nOutputLine.prototype.last = function() {\n  if (!this.is_empty()) {\n    return this.__items[this.__items.length - 1];\n  } else {\n    return null;\n  }\n};\n\nOutputLine.prototype.push = function(item) {\n  this.__items.push(item);\n  var last_newline_index = item.lastIndexOf('\\n');\n  if (last_newline_index !== -1) {\n    this.__character_count = item.length - last_newline_index;\n  } else {\n    this.__character_count += item.length;\n  }\n};\n\nOutputLine.prototype.pop = function() {\n  var item = null;\n  if (!this.is_empty()) {\n    item = this.__items.pop();\n    this.__character_count -= item.length;\n  }\n  return item;\n};\n\n\nOutputLine.prototype._remove_indent = function() {\n  if (this.__indent_count > 0) {\n    this.__indent_count -= 1;\n    this.__character_count -= this.__parent.indent_size;\n  }\n};\n\nOutputLine.prototype._remove_wrap_indent = function() {\n  if (this.__wrap_point_indent_count > 0) {\n    this.__wrap_point_indent_count -= 1;\n  }\n};\nOutputLine.prototype.trim = function() {\n  while (this.last() === ' ') {\n    this.__items.pop();\n    this.__character_count -= 1;\n  }\n};\n\nOutputLine.prototype.toString = function() {\n  var result = '';\n  if (this.is_empty()) {\n    if (this.__parent.indent_empty_lines) {\n      result = this.__parent.get_indent_string(this.__indent_count);\n    }\n  } else {\n    result = this.__parent.get_indent_string(this.__indent_count, this.__alignment_count);\n    result += this.__items.join('');\n  }\n  return result;\n};\n\nfunction IndentStringCache(options, baseIndentString) {\n  this.__cache = [''];\n  this.__indent_size = options.indent_size;\n  this.__indent_string = options.indent_char;\n  if (!options.indent_with_tabs) {\n    this.__indent_string = new Array(options.indent_size + 1).join(options.indent_char);\n  }\n\n  // Set to null to continue support for auto detection of base indent\n  baseIndentString = baseIndentString || '';\n  if (options.indent_level > 0) {\n    baseIndentString = new Array(options.indent_level + 1).join(this.__indent_string);\n  }\n\n  this.__base_string = baseIndentString;\n  this.__base_string_length = baseIndentString.length;\n}\n\nIndentStringCache.prototype.get_indent_size = function(indent, column) {\n  var result = this.__base_string_length;\n  column = column || 0;\n  if (indent < 0) {\n    result = 0;\n  }\n  result += indent * this.__indent_size;\n  result += column;\n  return result;\n};\n\nIndentStringCache.prototype.get_indent_string = function(indent_level, column) {\n  var result = this.__base_string;\n  column = column || 0;\n  if (indent_level < 0) {\n    indent_level = 0;\n    result = '';\n  }\n  column += indent_level * this.__indent_size;\n  this.__ensure_cache(column);\n  result += this.__cache[column];\n  return result;\n};\n\nIndentStringCache.prototype.__ensure_cache = function(column) {\n  while (column >= this.__cache.length) {\n    this.__add_column();\n  }\n};\n\nIndentStringCache.prototype.__add_column = function() {\n  var column = this.__cache.length;\n  var indent = 0;\n  var result = '';\n  if (this.__indent_size && column >= this.__indent_size) {\n    indent = Math.floor(column / this.__indent_size);\n    column -= indent * this.__indent_size;\n    result = new Array(indent + 1).join(this.__indent_string);\n  }\n  if (column) {\n    result += new Array(column + 1).join(' ');\n  }\n\n  this.__cache.push(result);\n};\n\nfunction Output(options, baseIndentString) {\n  this.__indent_cache = new IndentStringCache(options, baseIndentString);\n  this.raw = false;\n  this._end_with_newline = options.end_with_newline;\n  this.indent_size = options.indent_size;\n  this.wrap_line_length = options.wrap_line_length;\n  this.indent_empty_lines = options.indent_empty_lines;\n  this.__lines = [];\n  this.previous_line = null;\n  this.current_line = null;\n  this.next_line = new OutputLine(this);\n  this.space_before_token = false;\n  this.non_breaking_space = false;\n  this.previous_token_wrapped = false;\n  // initialize\n  this.__add_outputline();\n}\n\nOutput.prototype.__add_outputline = function() {\n  this.previous_line = this.current_line;\n  this.current_line = this.next_line.clone_empty();\n  this.__lines.push(this.current_line);\n};\n\nOutput.prototype.get_line_number = function() {\n  return this.__lines.length;\n};\n\nOutput.prototype.get_indent_string = function(indent, column) {\n  return this.__indent_cache.get_indent_string(indent, column);\n};\n\nOutput.prototype.get_indent_size = function(indent, column) {\n  return this.__indent_cache.get_indent_size(indent, column);\n};\n\nOutput.prototype.is_empty = function() {\n  return !this.previous_line && this.current_line.is_empty();\n};\n\nOutput.prototype.add_new_line = function(force_newline) {\n  // never newline at the start of file\n  // otherwise, newline only if we didn't just add one or we're forced\n  if (this.is_empty() ||\n    (!force_newline && this.just_added_newline())) {\n    return false;\n  }\n\n  // if raw output is enabled, don't print additional newlines,\n  // but still return True as though you had\n  if (!this.raw) {\n    this.__add_outputline();\n  }\n  return true;\n};\n\nOutput.prototype.get_code = function(eol) {\n  this.trim(true);\n\n  // handle some edge cases where the last tokens\n  // has text that ends with newline(s)\n  var last_item = this.current_line.pop();\n  if (last_item) {\n    if (last_item[last_item.length - 1] === '\\n') {\n      last_item = last_item.replace(/\\n+$/g, '');\n    }\n    this.current_line.push(last_item);\n  }\n\n  if (this._end_with_newline) {\n    this.__add_outputline();\n  }\n\n  var sweet_code = this.__lines.join('\\n');\n\n  if (eol !== '\\n') {\n    sweet_code = sweet_code.replace(/[\\n]/g, eol);\n  }\n  return sweet_code;\n};\n\nOutput.prototype.set_wrap_point = function() {\n  this.current_line._set_wrap_point();\n};\n\nOutput.prototype.set_indent = function(indent, alignment) {\n  indent = indent || 0;\n  alignment = alignment || 0;\n\n  // Next line stores alignment values\n  this.next_line.set_indent(indent, alignment);\n\n  // Never indent your first output indent at the start of the file\n  if (this.__lines.length > 1) {\n    this.current_line.set_indent(indent, alignment);\n    return true;\n  }\n\n  this.current_line.set_indent();\n  return false;\n};\n\nOutput.prototype.add_raw_token = function(token) {\n  for (var x = 0; x < token.newlines; x++) {\n    this.__add_outputline();\n  }\n  this.current_line.set_indent(-1);\n  this.current_line.push(token.whitespace_before);\n  this.current_line.push(token.text);\n  this.space_before_token = false;\n  this.non_breaking_space = false;\n  this.previous_token_wrapped = false;\n};\n\nOutput.prototype.add_token = function(printable_token) {\n  this.__add_space_before_token();\n  this.current_line.push(printable_token);\n  this.space_before_token = false;\n  this.non_breaking_space = false;\n  this.previous_token_wrapped = this.current_line._allow_wrap();\n};\n\nOutput.prototype.__add_space_before_token = function() {\n  if (this.space_before_token && !this.just_added_newline()) {\n    if (!this.non_breaking_space) {\n      this.set_wrap_point();\n    }\n    this.current_line.push(' ');\n  }\n};\n\nOutput.prototype.remove_indent = function(index) {\n  var output_length = this.__lines.length;\n  while (index < output_length) {\n    this.__lines[index]._remove_indent();\n    index++;\n  }\n  this.current_line._remove_wrap_indent();\n};\n\nOutput.prototype.trim = function(eat_newlines) {\n  eat_newlines = (eat_newlines === undefined) ? false : eat_newlines;\n\n  this.current_line.trim();\n\n  while (eat_newlines && this.__lines.length > 1 &&\n    this.current_line.is_empty()) {\n    this.__lines.pop();\n    this.current_line = this.__lines[this.__lines.length - 1];\n    this.current_line.trim();\n  }\n\n  this.previous_line = this.__lines.length > 1 ?\n    this.__lines[this.__lines.length - 2] : null;\n};\n\nOutput.prototype.just_added_newline = function() {\n  return this.current_line.is_empty();\n};\n\nOutput.prototype.just_added_blankline = function() {\n  return this.is_empty() ||\n    (this.current_line.is_empty() && this.previous_line.is_empty());\n};\n\nOutput.prototype.ensure_empty_line_above = function(starts_with, ends_with) {\n  var index = this.__lines.length - 2;\n  while (index >= 0) {\n    var potentialEmptyLine = this.__lines[index];\n    if (potentialEmptyLine.is_empty()) {\n      break;\n    } else if (potentialEmptyLine.item(0).indexOf(starts_with) !== 0 &&\n      potentialEmptyLine.item(-1) !== ends_with) {\n      this.__lines.splice(index + 1, 0, new OutputLine(this));\n      this.previous_line = this.__lines[this.__lines.length - 2];\n      break;\n    }\n    index--;\n  }\n};\n\nmodule.exports.Output = Output;\n\n\n/***/ }),\n/* 3 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nfunction Token(type, text, newlines, whitespace_before) {\n  this.type = type;\n  this.text = text;\n\n  // comments_before are\n  // comments that have a new line before them\n  // and may or may not have a newline after\n  // this is a set of comments before\n  this.comments_before = null; /* inline comment*/\n\n\n  // this.comments_after =  new TokenStream(); // no new line before and newline after\n  this.newlines = newlines || 0;\n  this.whitespace_before = whitespace_before || '';\n  this.parent = null;\n  this.next = null;\n  this.previous = null;\n  this.opened = null;\n  this.closed = null;\n  this.directives = null;\n}\n\n\nmodule.exports.Token = Token;\n\n\n/***/ }),\n/* 4 */\n/***/ (function(__unused_webpack_module, exports) {\n\n/* jshint node: true, curly: false */\n// Parts of this section of code is taken from acorn.\n//\n// Acorn was written by Marijn Haverbeke and released under an MIT\n// license. The Unicode regexps (for identifiers and whitespace) were\n// taken from [Esprima](http://esprima.org) by Ariya Hidayat.\n//\n// Git repositories for Acorn are available at\n//\n//     http://marijnhaverbeke.nl/git/acorn\n//     https://github.com/marijnh/acorn.git\n\n// ## Character categories\n\n\n\n\n// acorn used char codes to squeeze the last bit of performance out\n// Beautifier is okay without that, so we're using regex\n// permit # (23), $ (36), and @ (64). @ is used in ES7 decorators.\n// 65 through 91 are uppercase letters.\n// permit _ (95).\n// 97 through 123 are lowercase letters.\nvar baseASCIIidentifierStartChars = \"\\\\x23\\\\x24\\\\x40\\\\x41-\\\\x5a\\\\x5f\\\\x61-\\\\x7a\";\n\n// inside an identifier @ is not allowed but 0-9 are.\nvar baseASCIIidentifierChars = \"\\\\x24\\\\x30-\\\\x39\\\\x41-\\\\x5a\\\\x5f\\\\x61-\\\\x7a\";\n\n// Big ugly regular expressions that match characters in the\n// whitespace, identifier, and identifier-start categories. These\n// are only applied when a character is found to actually have a\n// code point above 128.\nvar nonASCIIidentifierStartChars = \"\\\\xaa\\\\xb5\\\\xba\\\\xc0-\\\\xd6\\\\xd8-\\\\xf6\\\\xf8-\\\\u02c1\\\\u02c6-\\\\u02d1\\\\u02e0-\\\\u02e4\\\\u02ec\\\\u02ee\\\\u0370-\\\\u0374\\\\u0376\\\\u0377\\\\u037a-\\\\u037d\\\\u0386\\\\u0388-\\\\u038a\\\\u038c\\\\u038e-\\\\u03a1\\\\u03a3-\\\\u03f5\\\\u03f7-\\\\u0481\\\\u048a-\\\\u0527\\\\u0531-\\\\u0556\\\\u0559\\\\u0561-\\\\u0587\\\\u05d0-\\\\u05ea\\\\u05f0-\\\\u05f2\\\\u0620-\\\\u064a\\\\u066e\\\\u066f\\\\u0671-\\\\u06d3\\\\u06d5\\\\u06e5\\\\u06e6\\\\u06ee\\\\u06ef\\\\u06fa-\\\\u06fc\\\\u06ff\\\\u0710\\\\u0712-\\\\u072f\\\\u074d-\\\\u07a5\\\\u07b1\\\\u07ca-\\\\u07ea\\\\u07f4\\\\u07f5\\\\u07fa\\\\u0800-\\\\u0815\\\\u081a\\\\u0824\\\\u0828\\\\u0840-\\\\u0858\\\\u08a0\\\\u08a2-\\\\u08ac\\\\u0904-\\\\u0939\\\\u093d\\\\u0950\\\\u0958-\\\\u0961\\\\u0971-\\\\u0977\\\\u0979-\\\\u097f\\\\u0985-\\\\u098c\\\\u098f\\\\u0990\\\\u0993-\\\\u09a8\\\\u09aa-\\\\u09b0\\\\u09b2\\\\u09b6-\\\\u09b9\\\\u09bd\\\\u09ce\\\\u09dc\\\\u09dd\\\\u09df-\\\\u09e1\\\\u09f0\\\\u09f1\\\\u0a05-\\\\u0a0a\\\\u0a0f\\\\u0a10\\\\u0a13-\\\\u0a28\\\\u0a2a-\\\\u0a30\\\\u0a32\\\\u0a33\\\\u0a35\\\\u0a36\\\\u0a38\\\\u0a39\\\\u0a59-\\\\u0a5c\\\\u0a5e\\\\u0a72-\\\\u0a74\\\\u0a85-\\\\u0a8d\\\\u0a8f-\\\\u0a91\\\\u0a93-\\\\u0aa8\\\\u0aaa-\\\\u0ab0\\\\u0ab2\\\\u0ab3\\\\u0ab5-\\\\u0ab9\\\\u0abd\\\\u0ad0\\\\u0ae0\\\\u0ae1\\\\u0b05-\\\\u0b0c\\\\u0b0f\\\\u0b10\\\\u0b13-\\\\u0b28\\\\u0b2a-\\\\u0b30\\\\u0b32\\\\u0b33\\\\u0b35-\\\\u0b39\\\\u0b3d\\\\u0b5c\\\\u0b5d\\\\u0b5f-\\\\u0b61\\\\u0b71\\\\u0b83\\\\u0b85-\\\\u0b8a\\\\u0b8e-\\\\u0b90\\\\u0b92-\\\\u0b95\\\\u0b99\\\\u0b9a\\\\u0b9c\\\\u0b9e\\\\u0b9f\\\\u0ba3\\\\u0ba4\\\\u0ba8-\\\\u0baa\\\\u0bae-\\\\u0bb9\\\\u0bd0\\\\u0c05-\\\\u0c0c\\\\u0c0e-\\\\u0c10\\\\u0c12-\\\\u0c28\\\\u0c2a-\\\\u0c33\\\\u0c35-\\\\u0c39\\\\u0c3d\\\\u0c58\\\\u0c59\\\\u0c60\\\\u0c61\\\\u0c85-\\\\u0c8c\\\\u0c8e-\\\\u0c90\\\\u0c92-\\\\u0ca8\\\\u0caa-\\\\u0cb3\\\\u0cb5-\\\\u0cb9\\\\u0cbd\\\\u0cde\\\\u0ce0\\\\u0ce1\\\\u0cf1\\\\u0cf2\\\\u0d05-\\\\u0d0c\\\\u0d0e-\\\\u0d10\\\\u0d12-\\\\u0d3a\\\\u0d3d\\\\u0d4e\\\\u0d60\\\\u0d61\\\\u0d7a-\\\\u0d7f\\\\u0d85-\\\\u0d96\\\\u0d9a-\\\\u0db1\\\\u0db3-\\\\u0dbb\\\\u0dbd\\\\u0dc0-\\\\u0dc6\\\\u0e01-\\\\u0e30\\\\u0e32\\\\u0e33\\\\u0e40-\\\\u0e46\\\\u0e81\\\\u0e82\\\\u0e84\\\\u0e87\\\\u0e88\\\\u0e8a\\\\u0e8d\\\\u0e94-\\\\u0e97\\\\u0e99-\\\\u0e9f\\\\u0ea1-\\\\u0ea3\\\\u0ea5\\\\u0ea7\\\\u0eaa\\\\u0eab\\\\u0ead-\\\\u0eb0\\\\u0eb2\\\\u0eb3\\\\u0ebd\\\\u0ec0-\\\\u0ec4\\\\u0ec6\\\\u0edc-\\\\u0edf\\\\u0f00\\\\u0f40-\\\\u0f47\\\\u0f49-\\\\u0f6c\\\\u0f88-\\\\u0f8c\\\\u1000-\\\\u102a\\\\u103f\\\\u1050-\\\\u1055\\\\u105a-\\\\u105d\\\\u1061\\\\u1065\\\\u1066\\\\u106e-\\\\u1070\\\\u1075-\\\\u1081\\\\u108e\\\\u10a0-\\\\u10c5\\\\u10c7\\\\u10cd\\\\u10d0-\\\\u10fa\\\\u10fc-\\\\u1248\\\\u124a-\\\\u124d\\\\u1250-\\\\u1256\\\\u1258\\\\u125a-\\\\u125d\\\\u1260-\\\\u1288\\\\u128a-\\\\u128d\\\\u1290-\\\\u12b0\\\\u12b2-\\\\u12b5\\\\u12b8-\\\\u12be\\\\u12c0\\\\u12c2-\\\\u12c5\\\\u12c8-\\\\u12d6\\\\u12d8-\\\\u1310\\\\u1312-\\\\u1315\\\\u1318-\\\\u135a\\\\u1380-\\\\u138f\\\\u13a0-\\\\u13f4\\\\u1401-\\\\u166c\\\\u166f-\\\\u167f\\\\u1681-\\\\u169a\\\\u16a0-\\\\u16ea\\\\u16ee-\\\\u16f0\\\\u1700-\\\\u170c\\\\u170e-\\\\u1711\\\\u1720-\\\\u1731\\\\u1740-\\\\u1751\\\\u1760-\\\\u176c\\\\u176e-\\\\u1770\\\\u1780-\\\\u17b3\\\\u17d7\\\\u17dc\\\\u1820-\\\\u1877\\\\u1880-\\\\u18a8\\\\u18aa\\\\u18b0-\\\\u18f5\\\\u1900-\\\\u191c\\\\u1950-\\\\u196d\\\\u1970-\\\\u1974\\\\u1980-\\\\u19ab\\\\u19c1-\\\\u19c7\\\\u1a00-\\\\u1a16\\\\u1a20-\\\\u1a54\\\\u1aa7\\\\u1b05-\\\\u1b33\\\\u1b45-\\\\u1b4b\\\\u1b83-\\\\u1ba0\\\\u1bae\\\\u1baf\\\\u1bba-\\\\u1be5\\\\u1c00-\\\\u1c23\\\\u1c4d-\\\\u1c4f\\\\u1c5a-\\\\u1c7d\\\\u1ce9-\\\\u1cec\\\\u1cee-\\\\u1cf1\\\\u1cf5\\\\u1cf6\\\\u1d00-\\\\u1dbf\\\\u1e00-\\\\u1f15\\\\u1f18-\\\\u1f1d\\\\u1f20-\\\\u1f45\\\\u1f48-\\\\u1f4d\\\\u1f50-\\\\u1f57\\\\u1f59\\\\u1f5b\\\\u1f5d\\\\u1f5f-\\\\u1f7d\\\\u1f80-\\\\u1fb4\\\\u1fb6-\\\\u1fbc\\\\u1fbe\\\\u1fc2-\\\\u1fc4\\\\u1fc6-\\\\u1fcc\\\\u1fd0-\\\\u1fd3\\\\u1fd6-\\\\u1fdb\\\\u1fe0-\\\\u1fec\\\\u1ff2-\\\\u1ff4\\\\u1ff6-\\\\u1ffc\\\\u2071\\\\u207f\\\\u2090-\\\\u209c\\\\u2102\\\\u2107\\\\u210a-\\\\u2113\\\\u2115\\\\u2119-\\\\u211d\\\\u2124\\\\u2126\\\\u2128\\\\u212a-\\\\u212d\\\\u212f-\\\\u2139\\\\u213c-\\\\u213f\\\\u2145-\\\\u2149\\\\u214e\\\\u2160-\\\\u2188\\\\u2c00-\\\\u2c2e\\\\u2c30-\\\\u2c5e\\\\u2c60-\\\\u2ce4\\\\u2ceb-\\\\u2cee\\\\u2cf2\\\\u2cf3\\\\u2d00-\\\\u2d25\\\\u2d27\\\\u2d2d\\\\u2d30-\\\\u2d67\\\\u2d6f\\\\u2d80-\\\\u2d96\\\\u2da0-\\\\u2da6\\\\u2da8-\\\\u2dae\\\\u2db0-\\\\u2db6\\\\u2db8-\\\\u2dbe\\\\u2dc0-\\\\u2dc6\\\\u2dc8-\\\\u2dce\\\\u2dd0-\\\\u2dd6\\\\u2dd8-\\\\u2dde\\\\u2e2f\\\\u3005-\\\\u3007\\\\u3021-\\\\u3029\\\\u3031-\\\\u3035\\\\u3038-\\\\u303c\\\\u3041-\\\\u3096\\\\u309d-\\\\u309f\\\\u30a1-\\\\u30fa\\\\u30fc-\\\\u30ff\\\\u3105-\\\\u312d\\\\u3131-\\\\u318e\\\\u31a0-\\\\u31ba\\\\u31f0-\\\\u31ff\\\\u3400-\\\\u4db5\\\\u4e00-\\\\u9fcc\\\\ua000-\\\\ua48c\\\\ua4d0-\\\\ua4fd\\\\ua500-\\\\ua60c\\\\ua610-\\\\ua61f\\\\ua62a\\\\ua62b\\\\ua640-\\\\ua66e\\\\ua67f-\\\\ua697\\\\ua6a0-\\\\ua6ef\\\\ua717-\\\\ua71f\\\\ua722-\\\\ua788\\\\ua78b-\\\\ua78e\\\\ua790-\\\\ua793\\\\ua7a0-\\\\ua7aa\\\\ua7f8-\\\\ua801\\\\ua803-\\\\ua805\\\\ua807-\\\\ua80a\\\\ua80c-\\\\ua822\\\\ua840-\\\\ua873\\\\ua882-\\\\ua8b3\\\\ua8f2-\\\\ua8f7\\\\ua8fb\\\\ua90a-\\\\ua925\\\\ua930-\\\\ua946\\\\ua960-\\\\ua97c\\\\ua984-\\\\ua9b2\\\\ua9cf\\\\uaa00-\\\\uaa28\\\\uaa40-\\\\uaa42\\\\uaa44-\\\\uaa4b\\\\uaa60-\\\\uaa76\\\\uaa7a\\\\uaa80-\\\\uaaaf\\\\uaab1\\\\uaab5\\\\uaab6\\\\uaab9-\\\\uaabd\\\\uaac0\\\\uaac2\\\\uaadb-\\\\uaadd\\\\uaae0-\\\\uaaea\\\\uaaf2-\\\\uaaf4\\\\uab01-\\\\uab06\\\\uab09-\\\\uab0e\\\\uab11-\\\\uab16\\\\uab20-\\\\uab26\\\\uab28-\\\\uab2e\\\\uabc0-\\\\uabe2\\\\uac00-\\\\ud7a3\\\\ud7b0-\\\\ud7c6\\\\ud7cb-\\\\ud7fb\\\\uf900-\\\\ufa6d\\\\ufa70-\\\\ufad9\\\\ufb00-\\\\ufb06\\\\ufb13-\\\\ufb17\\\\ufb1d\\\\ufb1f-\\\\ufb28\\\\ufb2a-\\\\ufb36\\\\ufb38-\\\\ufb3c\\\\ufb3e\\\\ufb40\\\\ufb41\\\\ufb43\\\\ufb44\\\\ufb46-\\\\ufbb1\\\\ufbd3-\\\\ufd3d\\\\ufd50-\\\\ufd8f\\\\ufd92-\\\\ufdc7\\\\ufdf0-\\\\ufdfb\\\\ufe70-\\\\ufe74\\\\ufe76-\\\\ufefc\\\\uff21-\\\\uff3a\\\\uff41-\\\\uff5a\\\\uff66-\\\\uffbe\\\\uffc2-\\\\uffc7\\\\uffca-\\\\uffcf\\\\uffd2-\\\\uffd7\\\\uffda-\\\\uffdc\";\nvar nonASCIIidentifierChars = \"\\\\u0300-\\\\u036f\\\\u0483-\\\\u0487\\\\u0591-\\\\u05bd\\\\u05bf\\\\u05c1\\\\u05c2\\\\u05c4\\\\u05c5\\\\u05c7\\\\u0610-\\\\u061a\\\\u0620-\\\\u0649\\\\u0672-\\\\u06d3\\\\u06e7-\\\\u06e8\\\\u06fb-\\\\u06fc\\\\u0730-\\\\u074a\\\\u0800-\\\\u0814\\\\u081b-\\\\u0823\\\\u0825-\\\\u0827\\\\u0829-\\\\u082d\\\\u0840-\\\\u0857\\\\u08e4-\\\\u08fe\\\\u0900-\\\\u0903\\\\u093a-\\\\u093c\\\\u093e-\\\\u094f\\\\u0951-\\\\u0957\\\\u0962-\\\\u0963\\\\u0966-\\\\u096f\\\\u0981-\\\\u0983\\\\u09bc\\\\u09be-\\\\u09c4\\\\u09c7\\\\u09c8\\\\u09d7\\\\u09df-\\\\u09e0\\\\u0a01-\\\\u0a03\\\\u0a3c\\\\u0a3e-\\\\u0a42\\\\u0a47\\\\u0a48\\\\u0a4b-\\\\u0a4d\\\\u0a51\\\\u0a66-\\\\u0a71\\\\u0a75\\\\u0a81-\\\\u0a83\\\\u0abc\\\\u0abe-\\\\u0ac5\\\\u0ac7-\\\\u0ac9\\\\u0acb-\\\\u0acd\\\\u0ae2-\\\\u0ae3\\\\u0ae6-\\\\u0aef\\\\u0b01-\\\\u0b03\\\\u0b3c\\\\u0b3e-\\\\u0b44\\\\u0b47\\\\u0b48\\\\u0b4b-\\\\u0b4d\\\\u0b56\\\\u0b57\\\\u0b5f-\\\\u0b60\\\\u0b66-\\\\u0b6f\\\\u0b82\\\\u0bbe-\\\\u0bc2\\\\u0bc6-\\\\u0bc8\\\\u0bca-\\\\u0bcd\\\\u0bd7\\\\u0be6-\\\\u0bef\\\\u0c01-\\\\u0c03\\\\u0c46-\\\\u0c48\\\\u0c4a-\\\\u0c4d\\\\u0c55\\\\u0c56\\\\u0c62-\\\\u0c63\\\\u0c66-\\\\u0c6f\\\\u0c82\\\\u0c83\\\\u0cbc\\\\u0cbe-\\\\u0cc4\\\\u0cc6-\\\\u0cc8\\\\u0cca-\\\\u0ccd\\\\u0cd5\\\\u0cd6\\\\u0ce2-\\\\u0ce3\\\\u0ce6-\\\\u0cef\\\\u0d02\\\\u0d03\\\\u0d46-\\\\u0d48\\\\u0d57\\\\u0d62-\\\\u0d63\\\\u0d66-\\\\u0d6f\\\\u0d82\\\\u0d83\\\\u0dca\\\\u0dcf-\\\\u0dd4\\\\u0dd6\\\\u0dd8-\\\\u0ddf\\\\u0df2\\\\u0df3\\\\u0e34-\\\\u0e3a\\\\u0e40-\\\\u0e45\\\\u0e50-\\\\u0e59\\\\u0eb4-\\\\u0eb9\\\\u0ec8-\\\\u0ecd\\\\u0ed0-\\\\u0ed9\\\\u0f18\\\\u0f19\\\\u0f20-\\\\u0f29\\\\u0f35\\\\u0f37\\\\u0f39\\\\u0f41-\\\\u0f47\\\\u0f71-\\\\u0f84\\\\u0f86-\\\\u0f87\\\\u0f8d-\\\\u0f97\\\\u0f99-\\\\u0fbc\\\\u0fc6\\\\u1000-\\\\u1029\\\\u1040-\\\\u1049\\\\u1067-\\\\u106d\\\\u1071-\\\\u1074\\\\u1082-\\\\u108d\\\\u108f-\\\\u109d\\\\u135d-\\\\u135f\\\\u170e-\\\\u1710\\\\u1720-\\\\u1730\\\\u1740-\\\\u1750\\\\u1772\\\\u1773\\\\u1780-\\\\u17b2\\\\u17dd\\\\u17e0-\\\\u17e9\\\\u180b-\\\\u180d\\\\u1810-\\\\u1819\\\\u1920-\\\\u192b\\\\u1930-\\\\u193b\\\\u1951-\\\\u196d\\\\u19b0-\\\\u19c0\\\\u19c8-\\\\u19c9\\\\u19d0-\\\\u19d9\\\\u1a00-\\\\u1a15\\\\u1a20-\\\\u1a53\\\\u1a60-\\\\u1a7c\\\\u1a7f-\\\\u1a89\\\\u1a90-\\\\u1a99\\\\u1b46-\\\\u1b4b\\\\u1b50-\\\\u1b59\\\\u1b6b-\\\\u1b73\\\\u1bb0-\\\\u1bb9\\\\u1be6-\\\\u1bf3\\\\u1c00-\\\\u1c22\\\\u1c40-\\\\u1c49\\\\u1c5b-\\\\u1c7d\\\\u1cd0-\\\\u1cd2\\\\u1d00-\\\\u1dbe\\\\u1e01-\\\\u1f15\\\\u200c\\\\u200d\\\\u203f\\\\u2040\\\\u2054\\\\u20d0-\\\\u20dc\\\\u20e1\\\\u20e5-\\\\u20f0\\\\u2d81-\\\\u2d96\\\\u2de0-\\\\u2dff\\\\u3021-\\\\u3028\\\\u3099\\\\u309a\\\\ua640-\\\\ua66d\\\\ua674-\\\\ua67d\\\\ua69f\\\\ua6f0-\\\\ua6f1\\\\ua7f8-\\\\ua800\\\\ua806\\\\ua80b\\\\ua823-\\\\ua827\\\\ua880-\\\\ua881\\\\ua8b4-\\\\ua8c4\\\\ua8d0-\\\\ua8d9\\\\ua8f3-\\\\ua8f7\\\\ua900-\\\\ua909\\\\ua926-\\\\ua92d\\\\ua930-\\\\ua945\\\\ua980-\\\\ua983\\\\ua9b3-\\\\ua9c0\\\\uaa00-\\\\uaa27\\\\uaa40-\\\\uaa41\\\\uaa4c-\\\\uaa4d\\\\uaa50-\\\\uaa59\\\\uaa7b\\\\uaae0-\\\\uaae9\\\\uaaf2-\\\\uaaf3\\\\uabc0-\\\\uabe1\\\\uabec\\\\uabed\\\\uabf0-\\\\uabf9\\\\ufb20-\\\\ufb28\\\\ufe00-\\\\ufe0f\\\\ufe20-\\\\ufe26\\\\ufe33\\\\ufe34\\\\ufe4d-\\\\ufe4f\\\\uff10-\\\\uff19\\\\uff3f\";\n//var nonASCIIidentifierStart = new RegExp(\"[\" + nonASCIIidentifierStartChars + \"]\");\n//var nonASCIIidentifier = new RegExp(\"[\" + nonASCIIidentifierStartChars + nonASCIIidentifierChars + \"]\");\n\nvar unicodeEscapeOrCodePoint = \"\\\\\\\\u[0-9a-fA-F]{4}|\\\\\\\\u\\\\{[0-9a-fA-F]+\\\\}\";\nvar identifierStart = \"(?:\" + unicodeEscapeOrCodePoint + \"|[\" + baseASCIIidentifierStartChars + nonASCIIidentifierStartChars + \"])\";\nvar identifierChars = \"(?:\" + unicodeEscapeOrCodePoint + \"|[\" + baseASCIIidentifierChars + nonASCIIidentifierStartChars + nonASCIIidentifierChars + \"])*\";\n\nexports.identifier = new RegExp(identifierStart + identifierChars, 'g');\nexports.identifierStart = new RegExp(identifierStart);\nexports.identifierMatch = new RegExp(\"(?:\" + unicodeEscapeOrCodePoint + \"|[\" + baseASCIIidentifierChars + nonASCIIidentifierStartChars + nonASCIIidentifierChars + \"])+\");\n\nvar nonASCIIwhitespace = /[\\u1680\\u180e\\u2000-\\u200a\\u202f\\u205f\\u3000\\ufeff]/; // jshint ignore:line\n\n// Whether a single character denotes a newline.\n\nexports.newline = /[\\n\\r\\u2028\\u2029]/;\n\n// Matches a whole line break (where CRLF is considered a single\n// line break). Used to count lines.\n\n// in javascript, these two differ\n// in python they are the same, different methods are called on them\nexports.lineBreak = new RegExp('\\r\\n|' + exports.newline.source);\nexports.allLineBreaks = new RegExp(exports.lineBreak.source, 'g');\n\n\n/***/ }),\n/* 5 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar BaseOptions = (__webpack_require__(6).Options);\n\nvar validPositionValues = ['before-newline', 'after-newline', 'preserve-newline'];\n\nfunction Options(options) {\n  BaseOptions.call(this, options, 'js');\n\n  // compatibility, re\n  var raw_brace_style = this.raw_options.brace_style || null;\n  if (raw_brace_style === \"expand-strict\") { //graceful handling of deprecated option\n    this.raw_options.brace_style = \"expand\";\n  } else if (raw_brace_style === \"collapse-preserve-inline\") { //graceful handling of deprecated option\n    this.raw_options.brace_style = \"collapse,preserve-inline\";\n  } else if (this.raw_options.braces_on_own_line !== undefined) { //graceful handling of deprecated option\n    this.raw_options.brace_style = this.raw_options.braces_on_own_line ? \"expand\" : \"collapse\";\n    // } else if (!raw_brace_style) { //Nothing exists to set it\n    //   raw_brace_style = \"collapse\";\n  }\n\n  //preserve-inline in delimited string will trigger brace_preserve_inline, everything\n  //else is considered a brace_style and the last one only will have an effect\n\n  var brace_style_split = this._get_selection_list('brace_style', ['collapse', 'expand', 'end-expand', 'none', 'preserve-inline']);\n\n  this.brace_preserve_inline = false; //Defaults in case one or other was not specified in meta-option\n  this.brace_style = \"collapse\";\n\n  for (var bs = 0; bs < brace_style_split.length; bs++) {\n    if (brace_style_split[bs] === \"preserve-inline\") {\n      this.brace_preserve_inline = true;\n    } else {\n      this.brace_style = brace_style_split[bs];\n    }\n  }\n\n  this.unindent_chained_methods = this._get_boolean('unindent_chained_methods');\n  this.break_chained_methods = this._get_boolean('break_chained_methods');\n  this.space_in_paren = this._get_boolean('space_in_paren');\n  this.space_in_empty_paren = this._get_boolean('space_in_empty_paren');\n  this.jslint_happy = this._get_boolean('jslint_happy');\n  this.space_after_anon_function = this._get_boolean('space_after_anon_function');\n  this.space_after_named_function = this._get_boolean('space_after_named_function');\n  this.keep_array_indentation = this._get_boolean('keep_array_indentation');\n  this.space_before_conditional = this._get_boolean('space_before_conditional', true);\n  this.unescape_strings = this._get_boolean('unescape_strings');\n  this.e4x = this._get_boolean('e4x');\n  this.comma_first = this._get_boolean('comma_first');\n  this.operator_position = this._get_selection('operator_position', validPositionValues);\n\n  // For testing of beautify preserve:start directive\n  this.test_output_raw = this._get_boolean('test_output_raw');\n\n  // force this._options.space_after_anon_function to true if this._options.jslint_happy\n  if (this.jslint_happy) {\n    this.space_after_anon_function = true;\n  }\n\n}\nOptions.prototype = new BaseOptions();\n\n\n\nmodule.exports.Options = Options;\n\n\n/***/ }),\n/* 6 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nfunction Options(options, merge_child_field) {\n  this.raw_options = _mergeOpts(options, merge_child_field);\n\n  // Support passing the source text back with no change\n  this.disabled = this._get_boolean('disabled');\n\n  this.eol = this._get_characters('eol', 'auto');\n  this.end_with_newline = this._get_boolean('end_with_newline');\n  this.indent_size = this._get_number('indent_size', 4);\n  this.indent_char = this._get_characters('indent_char', ' ');\n  this.indent_level = this._get_number('indent_level');\n\n  this.preserve_newlines = this._get_boolean('preserve_newlines', true);\n  this.max_preserve_newlines = this._get_number('max_preserve_newlines', 32786);\n  if (!this.preserve_newlines) {\n    this.max_preserve_newlines = 0;\n  }\n\n  this.indent_with_tabs = this._get_boolean('indent_with_tabs', this.indent_char === '\\t');\n  if (this.indent_with_tabs) {\n    this.indent_char = '\\t';\n\n    // indent_size behavior changed after 1.8.6\n    // It used to be that indent_size would be\n    // set to 1 for indent_with_tabs. That is no longer needed and\n    // actually doesn't make sense - why not use spaces? Further,\n    // that might produce unexpected behavior - tabs being used\n    // for single-column alignment. So, when indent_with_tabs is true\n    // and indent_size is 1, reset indent_size to 4.\n    if (this.indent_size === 1) {\n      this.indent_size = 4;\n    }\n  }\n\n  // Backwards compat with 1.3.x\n  this.wrap_line_length = this._get_number('wrap_line_length', this._get_number('max_char'));\n\n  this.indent_empty_lines = this._get_boolean('indent_empty_lines');\n\n  // valid templating languages ['django', 'erb', 'handlebars', 'php', 'smarty', 'angular']\n  // For now, 'auto' = all off for javascript, all except angular on for html (and inline javascript/css).\n  // other values ignored\n  this.templating = this._get_selection_list('templating', ['auto', 'none', 'angular', 'django', 'erb', 'handlebars', 'php', 'smarty'], ['auto']);\n}\n\nOptions.prototype._get_array = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  var result = default_value || [];\n  if (typeof option_value === 'object') {\n    if (option_value !== null && typeof option_value.concat === 'function') {\n      result = option_value.concat();\n    }\n  } else if (typeof option_value === 'string') {\n    result = option_value.split(/[^a-zA-Z0-9_\\/\\-]+/);\n  }\n  return result;\n};\n\nOptions.prototype._get_boolean = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  var result = option_value === undefined ? !!default_value : !!option_value;\n  return result;\n};\n\nOptions.prototype._get_characters = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  var result = default_value || '';\n  if (typeof option_value === 'string') {\n    result = option_value.replace(/\\\\r/, '\\r').replace(/\\\\n/, '\\n').replace(/\\\\t/, '\\t');\n  }\n  return result;\n};\n\nOptions.prototype._get_number = function(name, default_value) {\n  var option_value = this.raw_options[name];\n  default_value = parseInt(default_value, 10);\n  if (isNaN(default_value)) {\n    default_value = 0;\n  }\n  var result = parseInt(option_value, 10);\n  if (isNaN(result)) {\n    result = default_value;\n  }\n  return result;\n};\n\nOptions.prototype._get_selection = function(name, selection_list, default_value) {\n  var result = this._get_selection_list(name, selection_list, default_value);\n  if (result.length !== 1) {\n    throw new Error(\n      \"Invalid Option Value: The option '\" + name + \"' can only be one of the following values:\\n\" +\n      selection_list + \"\\nYou passed in: '\" + this.raw_options[name] + \"'\");\n  }\n\n  return result[0];\n};\n\n\nOptions.prototype._get_selection_list = function(name, selection_list, default_value) {\n  if (!selection_list || selection_list.length === 0) {\n    throw new Error(\"Selection list cannot be empty.\");\n  }\n\n  default_value = default_value || [selection_list[0]];\n  if (!this._is_valid_selection(default_value, selection_list)) {\n    throw new Error(\"Invalid Default Value!\");\n  }\n\n  var result = this._get_array(name, default_value);\n  if (!this._is_valid_selection(result, selection_list)) {\n    throw new Error(\n      \"Invalid Option Value: The option '\" + name + \"' can contain only the following values:\\n\" +\n      selection_list + \"\\nYou passed in: '\" + this.raw_options[name] + \"'\");\n  }\n\n  return result;\n};\n\nOptions.prototype._is_valid_selection = function(result, selection_list) {\n  return result.length && selection_list.length &&\n    !result.some(function(item) { return selection_list.indexOf(item) === -1; });\n};\n\n\n// merges child options up with the parent options object\n// Example: obj = {a: 1, b: {a: 2}}\n//          mergeOpts(obj, 'b')\n//\n//          Returns: {a: 2}\nfunction _mergeOpts(allOptions, childFieldName) {\n  var finalOpts = {};\n  allOptions = _normalizeOpts(allOptions);\n  var name;\n\n  for (name in allOptions) {\n    if (name !== childFieldName) {\n      finalOpts[name] = allOptions[name];\n    }\n  }\n\n  //merge in the per type settings for the childFieldName\n  if (childFieldName && allOptions[childFieldName]) {\n    for (name in allOptions[childFieldName]) {\n      finalOpts[name] = allOptions[childFieldName][name];\n    }\n  }\n  return finalOpts;\n}\n\nfunction _normalizeOpts(options) {\n  var convertedOpts = {};\n  var key;\n\n  for (key in options) {\n    var newKey = key.replace(/-/g, \"_\");\n    convertedOpts[newKey] = options[key];\n  }\n  return convertedOpts;\n}\n\nmodule.exports.Options = Options;\nmodule.exports.normalizeOpts = _normalizeOpts;\nmodule.exports.mergeOpts = _mergeOpts;\n\n\n/***/ }),\n/* 7 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar InputScanner = (__webpack_require__(8).InputScanner);\nvar BaseTokenizer = (__webpack_require__(9).Tokenizer);\nvar BASETOKEN = (__webpack_require__(9).TOKEN);\nvar Directives = (__webpack_require__(13).Directives);\nvar acorn = __webpack_require__(4);\nvar Pattern = (__webpack_require__(12).Pattern);\nvar TemplatablePattern = (__webpack_require__(14).TemplatablePattern);\n\n\nfunction in_array(what, arr) {\n  return arr.indexOf(what) !== -1;\n}\n\n\nvar TOKEN = {\n  START_EXPR: 'TK_START_EXPR',\n  END_EXPR: 'TK_END_EXPR',\n  START_BLOCK: 'TK_START_BLOCK',\n  END_BLOCK: 'TK_END_BLOCK',\n  WORD: 'TK_WORD',\n  RESERVED: 'TK_RESERVED',\n  SEMICOLON: 'TK_SEMICOLON',\n  STRING: 'TK_STRING',\n  EQUALS: 'TK_EQUALS',\n  OPERATOR: 'TK_OPERATOR',\n  COMMA: 'TK_COMMA',\n  BLOCK_COMMENT: 'TK_BLOCK_COMMENT',\n  COMMENT: 'TK_COMMENT',\n  DOT: 'TK_DOT',\n  UNKNOWN: 'TK_UNKNOWN',\n  START: BASETOKEN.START,\n  RAW: BASETOKEN.RAW,\n  EOF: BASETOKEN.EOF\n};\n\n\nvar directives_core = new Directives(/\\/\\*/, /\\*\\//);\n\nvar number_pattern = /0[xX][0123456789abcdefABCDEF_]*n?|0[oO][01234567_]*n?|0[bB][01_]*n?|\\d[\\d_]*n|(?:\\.\\d[\\d_]*|\\d[\\d_]*\\.?[\\d_]*)(?:[eE][+-]?[\\d_]+)?/;\n\nvar digit = /[0-9]/;\n\n// Dot \".\" must be distinguished from \"...\" and decimal\nvar dot_pattern = /[^\\d\\.]/;\n\nvar positionable_operators = (\n  \">>> === !== &&= ??= ||= \" +\n  \"<< && >= ** != == <= >> || ?? |> \" +\n  \"< / - + > : & % ? ^ | *\").split(' ');\n\n// IMPORTANT: this must be sorted longest to shortest or tokenizing many not work.\n// Also, you must update possitionable operators separately from punct\nvar punct =\n  \">>>= \" +\n  \"... >>= <<= === >>> !== **= &&= ??= ||= \" +\n  \"=> ^= :: /= << <= == && -= >= >> != -- += ** || ?? ++ %= &= *= |= |> \" +\n  \"= ! ? > < : / ^ - + * & % ~ |\";\n\npunct = punct.replace(/[-[\\]{}()*+?.,\\\\^$|#]/g, \"\\\\$&\");\n// ?. but not if followed by a number \npunct = '\\\\?\\\\.(?!\\\\d) ' + punct;\npunct = punct.replace(/ /g, '|');\n\nvar punct_pattern = new RegExp(punct);\n\n// words which should always start on new line.\nvar line_starters = 'continue,try,throw,return,var,let,const,if,switch,case,default,for,while,break,function,import,export'.split(',');\nvar reserved_words = line_starters.concat(['do', 'in', 'of', 'else', 'get', 'set', 'new', 'catch', 'finally', 'typeof', 'yield', 'async', 'await', 'from', 'as', 'class', 'extends']);\nvar reserved_word_pattern = new RegExp('^(?:' + reserved_words.join('|') + ')$');\n\n// var template_pattern = /(?:(?:<\\?php|<\\?=)[\\s\\S]*?\\?>)|(?:<%[\\s\\S]*?%>)/g;\n\nvar in_html_comment;\n\nvar Tokenizer = function(input_string, options) {\n  BaseTokenizer.call(this, input_string, options);\n\n  this._patterns.whitespace = this._patterns.whitespace.matching(\n    /\\u00A0\\u1680\\u180e\\u2000-\\u200a\\u202f\\u205f\\u3000\\ufeff/.source,\n    /\\u2028\\u2029/.source);\n\n  var pattern_reader = new Pattern(this._input);\n  var templatable = new TemplatablePattern(this._input)\n    .read_options(this._options);\n\n  this.__patterns = {\n    template: templatable,\n    identifier: templatable.starting_with(acorn.identifier).matching(acorn.identifierMatch),\n    number: pattern_reader.matching(number_pattern),\n    punct: pattern_reader.matching(punct_pattern),\n    // comment ends just before nearest linefeed or end of file\n    comment: pattern_reader.starting_with(/\\/\\//).until(/[\\n\\r\\u2028\\u2029]/),\n    //  /* ... */ comment ends with nearest */ or end of file\n    block_comment: pattern_reader.starting_with(/\\/\\*/).until_after(/\\*\\//),\n    html_comment_start: pattern_reader.matching(/<!--/),\n    html_comment_end: pattern_reader.matching(/-->/),\n    include: pattern_reader.starting_with(/#include/).until_after(acorn.lineBreak),\n    shebang: pattern_reader.starting_with(/#!/).until_after(acorn.lineBreak),\n    xml: pattern_reader.matching(/[\\s\\S]*?<(\\/?)([-a-zA-Z:0-9_.]+|{[^}]+?}|!\\[CDATA\\[[^\\]]*?\\]\\]|)(\\s*{[^}]+?}|\\s+[-a-zA-Z:0-9_.]+|\\s+[-a-zA-Z:0-9_.]+\\s*=\\s*('[^']*'|\"[^\"]*\"|{([^{}]|{[^}]+?})+?}))*\\s*(\\/?)\\s*>/),\n    single_quote: templatable.until(/['\\\\\\n\\r\\u2028\\u2029]/),\n    double_quote: templatable.until(/[\"\\\\\\n\\r\\u2028\\u2029]/),\n    template_text: templatable.until(/[`\\\\$]/),\n    template_expression: templatable.until(/[`}\\\\]/)\n  };\n\n};\nTokenizer.prototype = new BaseTokenizer();\n\nTokenizer.prototype._is_comment = function(current_token) {\n  return current_token.type === TOKEN.COMMENT || current_token.type === TOKEN.BLOCK_COMMENT || current_token.type === TOKEN.UNKNOWN;\n};\n\nTokenizer.prototype._is_opening = function(current_token) {\n  return current_token.type === TOKEN.START_BLOCK || current_token.type === TOKEN.START_EXPR;\n};\n\nTokenizer.prototype._is_closing = function(current_token, open_token) {\n  return (current_token.type === TOKEN.END_BLOCK || current_token.type === TOKEN.END_EXPR) &&\n    (open_token && (\n      (current_token.text === ']' && open_token.text === '[') ||\n      (current_token.text === ')' && open_token.text === '(') ||\n      (current_token.text === '}' && open_token.text === '{')));\n};\n\nTokenizer.prototype._reset = function() {\n  in_html_comment = false;\n};\n\nTokenizer.prototype._get_next_token = function(previous_token, open_token) { // jshint unused:false\n  var token = null;\n  this._readWhitespace();\n  var c = this._input.peek();\n\n  if (c === null) {\n    return this._create_token(TOKEN.EOF, '');\n  }\n\n  token = token || this._read_non_javascript(c);\n  token = token || this._read_string(c);\n  token = token || this._read_pair(c, this._input.peek(1)); // Issue #2062 hack for record type '#{'\n  token = token || this._read_word(previous_token);\n  token = token || this._read_singles(c);\n  token = token || this._read_comment(c);\n  token = token || this._read_regexp(c, previous_token);\n  token = token || this._read_xml(c, previous_token);\n  token = token || this._read_punctuation();\n  token = token || this._create_token(TOKEN.UNKNOWN, this._input.next());\n\n  return token;\n};\n\nTokenizer.prototype._read_word = function(previous_token) {\n  var resulting_string;\n  resulting_string = this.__patterns.identifier.read();\n  if (resulting_string !== '') {\n    resulting_string = resulting_string.replace(acorn.allLineBreaks, '\\n');\n    if (!(previous_token.type === TOKEN.DOT ||\n        (previous_token.type === TOKEN.RESERVED && (previous_token.text === 'set' || previous_token.text === 'get'))) &&\n      reserved_word_pattern.test(resulting_string)) {\n      if ((resulting_string === 'in' || resulting_string === 'of') &&\n        (previous_token.type === TOKEN.WORD || previous_token.type === TOKEN.STRING)) { // hack for 'in' and 'of' operators\n        return this._create_token(TOKEN.OPERATOR, resulting_string);\n      }\n      return this._create_token(TOKEN.RESERVED, resulting_string);\n    }\n    return this._create_token(TOKEN.WORD, resulting_string);\n  }\n\n  resulting_string = this.__patterns.number.read();\n  if (resulting_string !== '') {\n    return this._create_token(TOKEN.WORD, resulting_string);\n  }\n};\n\nTokenizer.prototype._read_singles = function(c) {\n  var token = null;\n  if (c === '(' || c === '[') {\n    token = this._create_token(TOKEN.START_EXPR, c);\n  } else if (c === ')' || c === ']') {\n    token = this._create_token(TOKEN.END_EXPR, c);\n  } else if (c === '{') {\n    token = this._create_token(TOKEN.START_BLOCK, c);\n  } else if (c === '}') {\n    token = this._create_token(TOKEN.END_BLOCK, c);\n  } else if (c === ';') {\n    token = this._create_token(TOKEN.SEMICOLON, c);\n  } else if (c === '.' && dot_pattern.test(this._input.peek(1))) {\n    token = this._create_token(TOKEN.DOT, c);\n  } else if (c === ',') {\n    token = this._create_token(TOKEN.COMMA, c);\n  }\n\n  if (token) {\n    this._input.next();\n  }\n  return token;\n};\n\nTokenizer.prototype._read_pair = function(c, d) {\n  var token = null;\n  if (c === '#' && d === '{') {\n    token = this._create_token(TOKEN.START_BLOCK, c + d);\n  }\n\n  if (token) {\n    this._input.next();\n    this._input.next();\n  }\n  return token;\n};\n\nTokenizer.prototype._read_punctuation = function() {\n  var resulting_string = this.__patterns.punct.read();\n\n  if (resulting_string !== '') {\n    if (resulting_string === '=') {\n      return this._create_token(TOKEN.EQUALS, resulting_string);\n    } else if (resulting_string === '?.') {\n      return this._create_token(TOKEN.DOT, resulting_string);\n    } else {\n      return this._create_token(TOKEN.OPERATOR, resulting_string);\n    }\n  }\n};\n\nTokenizer.prototype._read_non_javascript = function(c) {\n  var resulting_string = '';\n\n  if (c === '#') {\n    if (this._is_first_token()) {\n      resulting_string = this.__patterns.shebang.read();\n\n      if (resulting_string) {\n        return this._create_token(TOKEN.UNKNOWN, resulting_string.trim() + '\\n');\n      }\n    }\n\n    // handles extendscript #includes\n    resulting_string = this.__patterns.include.read();\n\n    if (resulting_string) {\n      return this._create_token(TOKEN.UNKNOWN, resulting_string.trim() + '\\n');\n    }\n\n    c = this._input.next();\n\n    // Spidermonkey-specific sharp variables for circular references. Considered obsolete.\n    var sharp = '#';\n    if (this._input.hasNext() && this._input.testChar(digit)) {\n      do {\n        c = this._input.next();\n        sharp += c;\n      } while (this._input.hasNext() && c !== '#' && c !== '=');\n      if (c === '#') {\n        //\n      } else if (this._input.peek() === '[' && this._input.peek(1) === ']') {\n        sharp += '[]';\n        this._input.next();\n        this._input.next();\n      } else if (this._input.peek() === '{' && this._input.peek(1) === '}') {\n        sharp += '{}';\n        this._input.next();\n        this._input.next();\n      }\n      return this._create_token(TOKEN.WORD, sharp);\n    }\n\n    this._input.back();\n\n  } else if (c === '<' && this._is_first_token()) {\n    resulting_string = this.__patterns.html_comment_start.read();\n    if (resulting_string) {\n      while (this._input.hasNext() && !this._input.testChar(acorn.newline)) {\n        resulting_string += this._input.next();\n      }\n      in_html_comment = true;\n      return this._create_token(TOKEN.COMMENT, resulting_string);\n    }\n  } else if (in_html_comment && c === '-') {\n    resulting_string = this.__patterns.html_comment_end.read();\n    if (resulting_string) {\n      in_html_comment = false;\n      return this._create_token(TOKEN.COMMENT, resulting_string);\n    }\n  }\n\n  return null;\n};\n\nTokenizer.prototype._read_comment = function(c) {\n  var token = null;\n  if (c === '/') {\n    var comment = '';\n    if (this._input.peek(1) === '*') {\n      // peek for comment /* ... */\n      comment = this.__patterns.block_comment.read();\n      var directives = directives_core.get_directives(comment);\n      if (directives && directives.ignore === 'start') {\n        comment += directives_core.readIgnored(this._input);\n      }\n      comment = comment.replace(acorn.allLineBreaks, '\\n');\n      token = this._create_token(TOKEN.BLOCK_COMMENT, comment);\n      token.directives = directives;\n    } else if (this._input.peek(1) === '/') {\n      // peek for comment // ...\n      comment = this.__patterns.comment.read();\n      token = this._create_token(TOKEN.COMMENT, comment);\n    }\n  }\n  return token;\n};\n\nTokenizer.prototype._read_string = function(c) {\n  if (c === '`' || c === \"'\" || c === '\"') {\n    var resulting_string = this._input.next();\n    this.has_char_escapes = false;\n\n    if (c === '`') {\n      resulting_string += this._read_string_recursive('`', true, '${');\n    } else {\n      resulting_string += this._read_string_recursive(c);\n    }\n\n    if (this.has_char_escapes && this._options.unescape_strings) {\n      resulting_string = unescape_string(resulting_string);\n    }\n\n    if (this._input.peek() === c) {\n      resulting_string += this._input.next();\n    }\n\n    resulting_string = resulting_string.replace(acorn.allLineBreaks, '\\n');\n\n    return this._create_token(TOKEN.STRING, resulting_string);\n  }\n\n  return null;\n};\n\nTokenizer.prototype._allow_regexp_or_xml = function(previous_token) {\n  // regex and xml can only appear in specific locations during parsing\n  return (previous_token.type === TOKEN.RESERVED && in_array(previous_token.text, ['return', 'case', 'throw', 'else', 'do', 'typeof', 'yield'])) ||\n    (previous_token.type === TOKEN.END_EXPR && previous_token.text === ')' &&\n      previous_token.opened.previous.type === TOKEN.RESERVED && in_array(previous_token.opened.previous.text, ['if', 'while', 'for'])) ||\n    (in_array(previous_token.type, [TOKEN.COMMENT, TOKEN.START_EXPR, TOKEN.START_BLOCK, TOKEN.START,\n      TOKEN.END_BLOCK, TOKEN.OPERATOR, TOKEN.EQUALS, TOKEN.EOF, TOKEN.SEMICOLON, TOKEN.COMMA\n    ]));\n};\n\nTokenizer.prototype._read_regexp = function(c, previous_token) {\n\n  if (c === '/' && this._allow_regexp_or_xml(previous_token)) {\n    // handle regexp\n    //\n    var resulting_string = this._input.next();\n    var esc = false;\n\n    var in_char_class = false;\n    while (this._input.hasNext() &&\n      ((esc || in_char_class || this._input.peek() !== c) &&\n        !this._input.testChar(acorn.newline))) {\n      resulting_string += this._input.peek();\n      if (!esc) {\n        esc = this._input.peek() === '\\\\';\n        if (this._input.peek() === '[') {\n          in_char_class = true;\n        } else if (this._input.peek() === ']') {\n          in_char_class = false;\n        }\n      } else {\n        esc = false;\n      }\n      this._input.next();\n    }\n\n    if (this._input.peek() === c) {\n      resulting_string += this._input.next();\n\n      // regexps may have modifiers /regexp/MOD , so fetch those, too\n      // Only [gim] are valid, but if the user puts in garbage, do what we can to take it.\n      resulting_string += this._input.read(acorn.identifier);\n    }\n    return this._create_token(TOKEN.STRING, resulting_string);\n  }\n  return null;\n};\n\nTokenizer.prototype._read_xml = function(c, previous_token) {\n\n  if (this._options.e4x && c === \"<\" && this._allow_regexp_or_xml(previous_token)) {\n    var xmlStr = '';\n    var match = this.__patterns.xml.read_match();\n    // handle e4x xml literals\n    //\n    if (match) {\n      // Trim root tag to attempt to\n      var rootTag = match[2].replace(/^{\\s+/, '{').replace(/\\s+}$/, '}');\n      var isCurlyRoot = rootTag.indexOf('{') === 0;\n      var depth = 0;\n      while (match) {\n        var isEndTag = !!match[1];\n        var tagName = match[2];\n        var isSingletonTag = (!!match[match.length - 1]) || (tagName.slice(0, 8) === \"![CDATA[\");\n        if (!isSingletonTag &&\n          (tagName === rootTag || (isCurlyRoot && tagName.replace(/^{\\s+/, '{').replace(/\\s+}$/, '}')))) {\n          if (isEndTag) {\n            --depth;\n          } else {\n            ++depth;\n          }\n        }\n        xmlStr += match[0];\n        if (depth <= 0) {\n          break;\n        }\n        match = this.__patterns.xml.read_match();\n      }\n      // if we didn't close correctly, keep unformatted.\n      if (!match) {\n        xmlStr += this._input.match(/[\\s\\S]*/g)[0];\n      }\n      xmlStr = xmlStr.replace(acorn.allLineBreaks, '\\n');\n      return this._create_token(TOKEN.STRING, xmlStr);\n    }\n  }\n\n  return null;\n};\n\nfunction unescape_string(s) {\n  // You think that a regex would work for this\n  // return s.replace(/\\\\x([0-9a-f]{2})/gi, function(match, val) {\n  //         return String.fromCharCode(parseInt(val, 16));\n  //     })\n  // However, dealing with '\\xff', '\\\\xff', '\\\\\\xff' makes this more fun.\n  var out = '',\n    escaped = 0;\n\n  var input_scan = new InputScanner(s);\n  var matched = null;\n\n  while (input_scan.hasNext()) {\n    // Keep any whitespace, non-slash characters\n    // also keep slash pairs.\n    matched = input_scan.match(/([\\s]|[^\\\\]|\\\\\\\\)+/g);\n\n    if (matched) {\n      out += matched[0];\n    }\n\n    if (input_scan.peek() === '\\\\') {\n      input_scan.next();\n      if (input_scan.peek() === 'x') {\n        matched = input_scan.match(/x([0-9A-Fa-f]{2})/g);\n      } else if (input_scan.peek() === 'u') {\n        matched = input_scan.match(/u([0-9A-Fa-f]{4})/g);\n        if (!matched) {\n          matched = input_scan.match(/u\\{([0-9A-Fa-f]+)\\}/g);\n        }\n      } else {\n        out += '\\\\';\n        if (input_scan.hasNext()) {\n          out += input_scan.next();\n        }\n        continue;\n      }\n\n      // If there's some error decoding, return the original string\n      if (!matched) {\n        return s;\n      }\n\n      escaped = parseInt(matched[1], 16);\n\n      if (escaped > 0x7e && escaped <= 0xff && matched[0].indexOf('x') === 0) {\n        // we bail out on \\x7f..\\xff,\n        // leaving whole string escaped,\n        // as it's probably completely binary\n        return s;\n      } else if (escaped >= 0x00 && escaped < 0x20) {\n        // leave 0x00...0x1f escaped\n        out += '\\\\' + matched[0];\n      } else if (escaped > 0x10FFFF) {\n        // If the escape sequence is out of bounds, keep the original sequence and continue conversion\n        out += '\\\\' + matched[0];\n      } else if (escaped === 0x22 || escaped === 0x27 || escaped === 0x5c) {\n        // single-quote, apostrophe, backslash - escape these\n        out += '\\\\' + String.fromCharCode(escaped);\n      } else {\n        out += String.fromCharCode(escaped);\n      }\n    }\n  }\n\n  return out;\n}\n\n// handle string\n//\nTokenizer.prototype._read_string_recursive = function(delimiter, allow_unescaped_newlines, start_sub) {\n  var current_char;\n  var pattern;\n  if (delimiter === '\\'') {\n    pattern = this.__patterns.single_quote;\n  } else if (delimiter === '\"') {\n    pattern = this.__patterns.double_quote;\n  } else if (delimiter === '`') {\n    pattern = this.__patterns.template_text;\n  } else if (delimiter === '}') {\n    pattern = this.__patterns.template_expression;\n  }\n\n  var resulting_string = pattern.read();\n  var next = '';\n  while (this._input.hasNext()) {\n    next = this._input.next();\n    if (next === delimiter ||\n      (!allow_unescaped_newlines && acorn.newline.test(next))) {\n      this._input.back();\n      break;\n    } else if (next === '\\\\' && this._input.hasNext()) {\n      current_char = this._input.peek();\n\n      if (current_char === 'x' || current_char === 'u') {\n        this.has_char_escapes = true;\n      } else if (current_char === '\\r' && this._input.peek(1) === '\\n') {\n        this._input.next();\n      }\n      next += this._input.next();\n    } else if (start_sub) {\n      if (start_sub === '${' && next === '$' && this._input.peek() === '{') {\n        next += this._input.next();\n      }\n\n      if (start_sub === next) {\n        if (delimiter === '`') {\n          next += this._read_string_recursive('}', allow_unescaped_newlines, '`');\n        } else {\n          next += this._read_string_recursive('`', allow_unescaped_newlines, '${');\n        }\n        if (this._input.hasNext()) {\n          next += this._input.next();\n        }\n      }\n    }\n    next += pattern.read();\n    resulting_string += next;\n  }\n\n  return resulting_string;\n};\n\nmodule.exports.Tokenizer = Tokenizer;\nmodule.exports.TOKEN = TOKEN;\nmodule.exports.positionable_operators = positionable_operators.slice();\nmodule.exports.line_starters = line_starters.slice();\n\n\n/***/ }),\n/* 8 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar regexp_has_sticky = RegExp.prototype.hasOwnProperty('sticky');\n\nfunction InputScanner(input_string) {\n  this.__input = input_string || '';\n  this.__input_length = this.__input.length;\n  this.__position = 0;\n}\n\nInputScanner.prototype.restart = function() {\n  this.__position = 0;\n};\n\nInputScanner.prototype.back = function() {\n  if (this.__position > 0) {\n    this.__position -= 1;\n  }\n};\n\nInputScanner.prototype.hasNext = function() {\n  return this.__position < this.__input_length;\n};\n\nInputScanner.prototype.next = function() {\n  var val = null;\n  if (this.hasNext()) {\n    val = this.__input.charAt(this.__position);\n    this.__position += 1;\n  }\n  return val;\n};\n\nInputScanner.prototype.peek = function(index) {\n  var val = null;\n  index = index || 0;\n  index += this.__position;\n  if (index >= 0 && index < this.__input_length) {\n    val = this.__input.charAt(index);\n  }\n  return val;\n};\n\n// This is a JavaScript only helper function (not in python)\n// Javascript doesn't have a match method\n// and not all implementation support \"sticky\" flag.\n// If they do not support sticky then both this.match() and this.test() method\n// must get the match and check the index of the match.\n// If sticky is supported and set, this method will use it.\n// Otherwise it will check that global is set, and fall back to the slower method.\nInputScanner.prototype.__match = function(pattern, index) {\n  pattern.lastIndex = index;\n  var pattern_match = pattern.exec(this.__input);\n\n  if (pattern_match && !(regexp_has_sticky && pattern.sticky)) {\n    if (pattern_match.index !== index) {\n      pattern_match = null;\n    }\n  }\n\n  return pattern_match;\n};\n\nInputScanner.prototype.test = function(pattern, index) {\n  index = index || 0;\n  index += this.__position;\n\n  if (index >= 0 && index < this.__input_length) {\n    return !!this.__match(pattern, index);\n  } else {\n    return false;\n  }\n};\n\nInputScanner.prototype.testChar = function(pattern, index) {\n  // test one character regex match\n  var val = this.peek(index);\n  pattern.lastIndex = 0;\n  return val !== null && pattern.test(val);\n};\n\nInputScanner.prototype.match = function(pattern) {\n  var pattern_match = this.__match(pattern, this.__position);\n  if (pattern_match) {\n    this.__position += pattern_match[0].length;\n  } else {\n    pattern_match = null;\n  }\n  return pattern_match;\n};\n\nInputScanner.prototype.read = function(starting_pattern, until_pattern, until_after) {\n  var val = '';\n  var match;\n  if (starting_pattern) {\n    match = this.match(starting_pattern);\n    if (match) {\n      val += match[0];\n    }\n  }\n  if (until_pattern && (match || !starting_pattern)) {\n    val += this.readUntil(until_pattern, until_after);\n  }\n  return val;\n};\n\nInputScanner.prototype.readUntil = function(pattern, until_after) {\n  var val = '';\n  var match_index = this.__position;\n  pattern.lastIndex = this.__position;\n  var pattern_match = pattern.exec(this.__input);\n  if (pattern_match) {\n    match_index = pattern_match.index;\n    if (until_after) {\n      match_index += pattern_match[0].length;\n    }\n  } else {\n    match_index = this.__input_length;\n  }\n\n  val = this.__input.substring(this.__position, match_index);\n  this.__position = match_index;\n  return val;\n};\n\nInputScanner.prototype.readUntilAfter = function(pattern) {\n  return this.readUntil(pattern, true);\n};\n\nInputScanner.prototype.get_regexp = function(pattern, match_from) {\n  var result = null;\n  var flags = 'g';\n  if (match_from && regexp_has_sticky) {\n    flags = 'y';\n  }\n  // strings are converted to regexp\n  if (typeof pattern === \"string\" && pattern !== '') {\n    // result = new RegExp(pattern.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'), flags);\n    result = new RegExp(pattern, flags);\n  } else if (pattern) {\n    result = new RegExp(pattern.source, flags);\n  }\n  return result;\n};\n\nInputScanner.prototype.get_literal_regexp = function(literal_string) {\n  return RegExp(literal_string.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&'));\n};\n\n/* css beautifier legacy helpers */\nInputScanner.prototype.peekUntilAfter = function(pattern) {\n  var start = this.__position;\n  var val = this.readUntilAfter(pattern);\n  this.__position = start;\n  return val;\n};\n\nInputScanner.prototype.lookBack = function(testVal) {\n  var start = this.__position - 1;\n  return start >= testVal.length && this.__input.substring(start - testVal.length, start)\n    .toLowerCase() === testVal;\n};\n\nmodule.exports.InputScanner = InputScanner;\n\n\n/***/ }),\n/* 9 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar InputScanner = (__webpack_require__(8).InputScanner);\nvar Token = (__webpack_require__(3).Token);\nvar TokenStream = (__webpack_require__(10).TokenStream);\nvar WhitespacePattern = (__webpack_require__(11).WhitespacePattern);\n\nvar TOKEN = {\n  START: 'TK_START',\n  RAW: 'TK_RAW',\n  EOF: 'TK_EOF'\n};\n\nvar Tokenizer = function(input_string, options) {\n  this._input = new InputScanner(input_string);\n  this._options = options || {};\n  this.__tokens = null;\n\n  this._patterns = {};\n  this._patterns.whitespace = new WhitespacePattern(this._input);\n};\n\nTokenizer.prototype.tokenize = function() {\n  this._input.restart();\n  this.__tokens = new TokenStream();\n\n  this._reset();\n\n  var current;\n  var previous = new Token(TOKEN.START, '');\n  var open_token = null;\n  var open_stack = [];\n  var comments = new TokenStream();\n\n  while (previous.type !== TOKEN.EOF) {\n    current = this._get_next_token(previous, open_token);\n    while (this._is_comment(current)) {\n      comments.add(current);\n      current = this._get_next_token(previous, open_token);\n    }\n\n    if (!comments.isEmpty()) {\n      current.comments_before = comments;\n      comments = new TokenStream();\n    }\n\n    current.parent = open_token;\n\n    if (this._is_opening(current)) {\n      open_stack.push(open_token);\n      open_token = current;\n    } else if (open_token && this._is_closing(current, open_token)) {\n      current.opened = open_token;\n      open_token.closed = current;\n      open_token = open_stack.pop();\n      current.parent = open_token;\n    }\n\n    current.previous = previous;\n    previous.next = current;\n\n    this.__tokens.add(current);\n    previous = current;\n  }\n\n  return this.__tokens;\n};\n\n\nTokenizer.prototype._is_first_token = function() {\n  return this.__tokens.isEmpty();\n};\n\nTokenizer.prototype._reset = function() {};\n\nTokenizer.prototype._get_next_token = function(previous_token, open_token) { // jshint unused:false\n  this._readWhitespace();\n  var resulting_string = this._input.read(/.+/g);\n  if (resulting_string) {\n    return this._create_token(TOKEN.RAW, resulting_string);\n  } else {\n    return this._create_token(TOKEN.EOF, '');\n  }\n};\n\nTokenizer.prototype._is_comment = function(current_token) { // jshint unused:false\n  return false;\n};\n\nTokenizer.prototype._is_opening = function(current_token) { // jshint unused:false\n  return false;\n};\n\nTokenizer.prototype._is_closing = function(current_token, open_token) { // jshint unused:false\n  return false;\n};\n\nTokenizer.prototype._create_token = function(type, text) {\n  var token = new Token(type, text,\n    this._patterns.whitespace.newline_count,\n    this._patterns.whitespace.whitespace_before_token);\n  return token;\n};\n\nTokenizer.prototype._readWhitespace = function() {\n  return this._patterns.whitespace.read();\n};\n\n\n\nmodule.exports.Tokenizer = Tokenizer;\nmodule.exports.TOKEN = TOKEN;\n\n\n/***/ }),\n/* 10 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nfunction TokenStream(parent_token) {\n  // private\n  this.__tokens = [];\n  this.__tokens_length = this.__tokens.length;\n  this.__position = 0;\n  this.__parent_token = parent_token;\n}\n\nTokenStream.prototype.restart = function() {\n  this.__position = 0;\n};\n\nTokenStream.prototype.isEmpty = function() {\n  return this.__tokens_length === 0;\n};\n\nTokenStream.prototype.hasNext = function() {\n  return this.__position < this.__tokens_length;\n};\n\nTokenStream.prototype.next = function() {\n  var val = null;\n  if (this.hasNext()) {\n    val = this.__tokens[this.__position];\n    this.__position += 1;\n  }\n  return val;\n};\n\nTokenStream.prototype.peek = function(index) {\n  var val = null;\n  index = index || 0;\n  index += this.__position;\n  if (index >= 0 && index < this.__tokens_length) {\n    val = this.__tokens[index];\n  }\n  return val;\n};\n\nTokenStream.prototype.add = function(token) {\n  if (this.__parent_token) {\n    token.parent = this.__parent_token;\n  }\n  this.__tokens.push(token);\n  this.__tokens_length += 1;\n};\n\nmodule.exports.TokenStream = TokenStream;\n\n\n/***/ }),\n/* 11 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar Pattern = (__webpack_require__(12).Pattern);\n\nfunction WhitespacePattern(input_scanner, parent) {\n  Pattern.call(this, input_scanner, parent);\n  if (parent) {\n    this._line_regexp = this._input.get_regexp(parent._line_regexp);\n  } else {\n    this.__set_whitespace_patterns('', '');\n  }\n\n  this.newline_count = 0;\n  this.whitespace_before_token = '';\n}\nWhitespacePattern.prototype = new Pattern();\n\nWhitespacePattern.prototype.__set_whitespace_patterns = function(whitespace_chars, newline_chars) {\n  whitespace_chars += '\\\\t ';\n  newline_chars += '\\\\n\\\\r';\n\n  this._match_pattern = this._input.get_regexp(\n    '[' + whitespace_chars + newline_chars + ']+', true);\n  this._newline_regexp = this._input.get_regexp(\n    '\\\\r\\\\n|[' + newline_chars + ']');\n};\n\nWhitespacePattern.prototype.read = function() {\n  this.newline_count = 0;\n  this.whitespace_before_token = '';\n\n  var resulting_string = this._input.read(this._match_pattern);\n  if (resulting_string === ' ') {\n    this.whitespace_before_token = ' ';\n  } else if (resulting_string) {\n    var matches = this.__split(this._newline_regexp, resulting_string);\n    this.newline_count = matches.length - 1;\n    this.whitespace_before_token = matches[this.newline_count];\n  }\n\n  return resulting_string;\n};\n\nWhitespacePattern.prototype.matching = function(whitespace_chars, newline_chars) {\n  var result = this._create();\n  result.__set_whitespace_patterns(whitespace_chars, newline_chars);\n  result._update();\n  return result;\n};\n\nWhitespacePattern.prototype._create = function() {\n  return new WhitespacePattern(this._input, this);\n};\n\nWhitespacePattern.prototype.__split = function(regexp, input_string) {\n  regexp.lastIndex = 0;\n  var start_index = 0;\n  var result = [];\n  var next_match = regexp.exec(input_string);\n  while (next_match) {\n    result.push(input_string.substring(start_index, next_match.index));\n    start_index = next_match.index + next_match[0].length;\n    next_match = regexp.exec(input_string);\n  }\n\n  if (start_index < input_string.length) {\n    result.push(input_string.substring(start_index, input_string.length));\n  } else {\n    result.push('');\n  }\n\n  return result;\n};\n\n\n\nmodule.exports.WhitespacePattern = WhitespacePattern;\n\n\n/***/ }),\n/* 12 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nfunction Pattern(input_scanner, parent) {\n  this._input = input_scanner;\n  this._starting_pattern = null;\n  this._match_pattern = null;\n  this._until_pattern = null;\n  this._until_after = false;\n\n  if (parent) {\n    this._starting_pattern = this._input.get_regexp(parent._starting_pattern, true);\n    this._match_pattern = this._input.get_regexp(parent._match_pattern, true);\n    this._until_pattern = this._input.get_regexp(parent._until_pattern);\n    this._until_after = parent._until_after;\n  }\n}\n\nPattern.prototype.read = function() {\n  var result = this._input.read(this._starting_pattern);\n  if (!this._starting_pattern || result) {\n    result += this._input.read(this._match_pattern, this._until_pattern, this._until_after);\n  }\n  return result;\n};\n\nPattern.prototype.read_match = function() {\n  return this._input.match(this._match_pattern);\n};\n\nPattern.prototype.until_after = function(pattern) {\n  var result = this._create();\n  result._until_after = true;\n  result._until_pattern = this._input.get_regexp(pattern);\n  result._update();\n  return result;\n};\n\nPattern.prototype.until = function(pattern) {\n  var result = this._create();\n  result._until_after = false;\n  result._until_pattern = this._input.get_regexp(pattern);\n  result._update();\n  return result;\n};\n\nPattern.prototype.starting_with = function(pattern) {\n  var result = this._create();\n  result._starting_pattern = this._input.get_regexp(pattern, true);\n  result._update();\n  return result;\n};\n\nPattern.prototype.matching = function(pattern) {\n  var result = this._create();\n  result._match_pattern = this._input.get_regexp(pattern, true);\n  result._update();\n  return result;\n};\n\nPattern.prototype._create = function() {\n  return new Pattern(this._input, this);\n};\n\nPattern.prototype._update = function() {};\n\nmodule.exports.Pattern = Pattern;\n\n\n/***/ }),\n/* 13 */\n/***/ (function(module) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nfunction Directives(start_block_pattern, end_block_pattern) {\n  start_block_pattern = typeof start_block_pattern === 'string' ? start_block_pattern : start_block_pattern.source;\n  end_block_pattern = typeof end_block_pattern === 'string' ? end_block_pattern : end_block_pattern.source;\n  this.__directives_block_pattern = new RegExp(start_block_pattern + / beautify( \\w+[:]\\w+)+ /.source + end_block_pattern, 'g');\n  this.__directive_pattern = / (\\w+)[:](\\w+)/g;\n\n  this.__directives_end_ignore_pattern = new RegExp(start_block_pattern + /\\sbeautify\\signore:end\\s/.source + end_block_pattern, 'g');\n}\n\nDirectives.prototype.get_directives = function(text) {\n  if (!text.match(this.__directives_block_pattern)) {\n    return null;\n  }\n\n  var directives = {};\n  this.__directive_pattern.lastIndex = 0;\n  var directive_match = this.__directive_pattern.exec(text);\n\n  while (directive_match) {\n    directives[directive_match[1]] = directive_match[2];\n    directive_match = this.__directive_pattern.exec(text);\n  }\n\n  return directives;\n};\n\nDirectives.prototype.readIgnored = function(input) {\n  return input.readUntilAfter(this.__directives_end_ignore_pattern);\n};\n\n\nmodule.exports.Directives = Directives;\n\n\n/***/ }),\n/* 14 */\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\n/*jshint node:true */\n/*\n\n  The MIT License (MIT)\n\n  Copyright (c) 2007-2018 Einar Lielmanis, Liam Newman, and contributors.\n\n  Permission is hereby granted, free of charge, to any person\n  obtaining a copy of this software and associated documentation files\n  (the \"Software\"), to deal in the Software without restriction,\n  including without limitation the rights to use, copy, modify, merge,\n  publish, distribute, sublicense, and/or sell copies of the Software,\n  and to permit persons to whom the Software is furnished to do so,\n  subject to the following conditions:\n\n  The above copyright notice and this permission notice shall be\n  included in all copies or substantial portions of the Software.\n\n  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n  MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS\n  BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN\n  ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n  CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n  SOFTWARE.\n*/\n\n\n\nvar Pattern = (__webpack_require__(12).Pattern);\n\n\nvar template_names = {\n  django: false,\n  erb: false,\n  handlebars: false,\n  php: false,\n  smarty: false,\n  angular: false\n};\n\n// This lets templates appear anywhere we would do a readUntil\n// The cost is higher but it is pay to play.\nfunction TemplatablePattern(input_scanner, parent) {\n  Pattern.call(this, input_scanner, parent);\n  this.__template_pattern = null;\n  this._disabled = Object.assign({}, template_names);\n  this._excluded = Object.assign({}, template_names);\n\n  if (parent) {\n    this.__template_pattern = this._input.get_regexp(parent.__template_pattern);\n    this._excluded = Object.assign(this._excluded, parent._excluded);\n    this._disabled = Object.assign(this._disabled, parent._disabled);\n  }\n  var pattern = new Pattern(input_scanner);\n  this.__patterns = {\n    handlebars_comment: pattern.starting_with(/{{!--/).until_after(/--}}/),\n    handlebars_unescaped: pattern.starting_with(/{{{/).until_after(/}}}/),\n    handlebars: pattern.starting_with(/{{/).until_after(/}}/),\n    php: pattern.starting_with(/<\\?(?:[= ]|php)/).until_after(/\\?>/),\n    erb: pattern.starting_with(/<%[^%]/).until_after(/[^%]%>/),\n    // django coflicts with handlebars a bit.\n    django: pattern.starting_with(/{%/).until_after(/%}/),\n    django_value: pattern.starting_with(/{{/).until_after(/}}/),\n    django_comment: pattern.starting_with(/{#/).until_after(/#}/),\n    smarty: pattern.starting_with(/{(?=[^}{\\s\\n])/).until_after(/[^\\s\\n]}/),\n    smarty_comment: pattern.starting_with(/{\\*/).until_after(/\\*}/),\n    smarty_literal: pattern.starting_with(/{literal}/).until_after(/{\\/literal}/)\n  };\n}\nTemplatablePattern.prototype = new Pattern();\n\nTemplatablePattern.prototype._create = function() {\n  return new TemplatablePattern(this._input, this);\n};\n\nTemplatablePattern.prototype._update = function() {\n  this.__set_templated_pattern();\n};\n\nTemplatablePattern.prototype.disable = function(language) {\n  var result = this._create();\n  result._disabled[language] = true;\n  result._update();\n  return result;\n};\n\nTemplatablePattern.prototype.read_options = function(options) {\n  var result = this._create();\n  for (var language in template_names) {\n    result._disabled[language] = options.templating.indexOf(language) === -1;\n  }\n  result._update();\n  return result;\n};\n\nTemplatablePattern.prototype.exclude = function(language) {\n  var result = this._create();\n  result._excluded[language] = true;\n  result._update();\n  return result;\n};\n\nTemplatablePattern.prototype.read = function() {\n  var result = '';\n  if (this._match_pattern) {\n    result = this._input.read(this._starting_pattern);\n  } else {\n    result = this._input.read(this._starting_pattern, this.__template_pattern);\n  }\n  var next = this._read_template();\n  while (next) {\n    if (this._match_pattern) {\n      next += this._input.read(this._match_pattern);\n    } else {\n      next += this._input.readUntil(this.__template_pattern);\n    }\n    result += next;\n    next = this._read_template();\n  }\n\n  if (this._until_after) {\n    result += this._input.readUntilAfter(this._until_pattern);\n  }\n  return result;\n};\n\nTemplatablePattern.prototype.__set_templated_pattern = function() {\n  var items = [];\n\n  if (!this._disabled.php) {\n    items.push(this.__patterns.php._starting_pattern.source);\n  }\n  if (!this._disabled.handlebars) {\n    items.push(this.__patterns.handlebars._starting_pattern.source);\n  }\n  if (!this._disabled.angular) {\n    // Handlebars ('{{' and '}}') are also special tokens in Angular)\n    items.push(this.__patterns.handlebars._starting_pattern.source);\n  }\n  if (!this._disabled.erb) {\n    items.push(this.__patterns.erb._starting_pattern.source);\n  }\n  if (!this._disabled.django) {\n    items.push(this.__patterns.django._starting_pattern.source);\n    // The starting pattern for django is more complex because it has different\n    // patterns for value, comment, and other sections\n    items.push(this.__patterns.django_value._starting_pattern.source);\n    items.push(this.__patterns.django_comment._starting_pattern.source);\n  }\n  if (!this._disabled.smarty) {\n    items.push(this.__patterns.smarty._starting_pattern.source);\n  }\n\n  if (this._until_pattern) {\n    items.push(this._until_pattern.source);\n  }\n  this.__template_pattern = this._input.get_regexp('(?:' + items.join('|') + ')');\n};\n\nTemplatablePattern.prototype._read_template = function() {\n  var resulting_string = '';\n  var c = this._input.peek();\n  if (c === '<') {\n    var peek1 = this._input.peek(1);\n    //if we're in a comment, do something special\n    // We treat all comments as literals, even more than preformatted tags\n    // we just look for the appropriate close tag\n    if (!this._disabled.php && !this._excluded.php && peek1 === '?') {\n      resulting_string = resulting_string ||\n        this.__patterns.php.read();\n    }\n    if (!this._disabled.erb && !this._excluded.erb && peek1 === '%') {\n      resulting_string = resulting_string ||\n        this.__patterns.erb.read();\n    }\n  } else if (c === '{') {\n    if (!this._disabled.handlebars && !this._excluded.handlebars) {\n      resulting_string = resulting_string ||\n        this.__patterns.handlebars_comment.read();\n      resulting_string = resulting_string ||\n        this.__patterns.handlebars_unescaped.read();\n      resulting_string = resulting_string ||\n        this.__patterns.handlebars.read();\n    }\n    if (!this._disabled.django) {\n      // django coflicts with handlebars a bit.\n      if (!this._excluded.django && !this._excluded.handlebars) {\n        resulting_string = resulting_string ||\n          this.__patterns.django_value.read();\n      }\n      if (!this._excluded.django) {\n        resulting_string = resulting_string ||\n          this.__patterns.django_comment.read();\n        resulting_string = resulting_string ||\n          this.__patterns.django.read();\n      }\n    }\n    if (!this._disabled.smarty) {\n      // smarty cannot be enabled with django or handlebars enabled\n      if (this._disabled.django && this._disabled.handlebars) {\n        resulting_string = resulting_string ||\n          this.__patterns.smarty_comment.read();\n        resulting_string = resulting_string ||\n          this.__patterns.smarty_literal.read();\n        resulting_string = resulting_string ||\n          this.__patterns.smarty.read();\n      }\n    }\n  }\n  return resulting_string;\n};\n\n\nmodule.exports.TemplatablePattern = TemplatablePattern;\n\n\n/***/ })\n/******/ \t]);\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t\n/******/ \t// startup\n/******/ \t// Load entry module and return exports\n/******/ \t// This entry module is referenced by other modules so it can't be inlined\n/******/ \tvar __webpack_exports__ = __webpack_require__(0);\n/******/ \tlegacy_beautify_js = __webpack_exports__;\n/******/ \t\n/******/ })()\n;\nvar js_beautify = legacy_beautify_js;\n/* Footer */\nif (typeof define === \"function\" && define.amd) {\n    // Add support for AMD ( https://github.com/amdjs/amdjs-api/wiki/AMD#defineamd-property- )\n    define([], function() {\n        return { js_beautify: js_beautify };\n    });\n} else if (typeof exports !== \"undefined\") {\n    // Add support for CommonJS. Just put this file somewhere on your require.paths\n    // and you will be able to `var js_beautify = require(\"beautify\").js_beautify`.\n    exports.js_beautify = js_beautify;\n} else if (typeof window !== \"undefined\") {\n    // If we're running a web page and don't have either of the above, add our one global\n    window.js_beautify = js_beautify;\n} else if (typeof global !== \"undefined\") {\n    // If we don't even have window, try global.\n    global.js_beautify = js_beautify;\n}\n\n}());\n\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEC,aAAW;EAEZ;EACA,IAAIA,kBAAkB;EACtB;EAAS,CAAC,YAAW;IAAE;IACvB;IAAU,YAAY;;IACtB;IAAU,IAAIC,mBAAmB,GAAI,EACrC;IACA,KAAO,UAASC,MAAM,EAAEC,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAIC,UAAU,GAAID,mBAAmB,CAAC,CAAC,CAAC,CAACC,UAAW;QAClDC,OAAO,GAAIF,mBAAmB,CAAC,CAAC,CAAC,CAACE,OAAQ;MAE5C,SAASC,WAAWA,CAACC,cAAc,EAAEC,OAAO,EAAE;QAC5C,IAAIC,UAAU,GAAG,IAAIL,UAAU,CAACG,cAAc,EAAEC,OAAO,CAAC;QACxD,OAAOC,UAAU,CAACC,QAAQ,CAAC,CAAC;MAC9B;MAEAT,MAAM,CAACU,OAAO,GAAGL,WAAW;MAC5BL,MAAM,CAACU,OAAO,CAACC,cAAc,GAAG,YAAW;QACzC,OAAO,IAAIP,OAAO,CAAC,CAAC;MACtB,CAAC;;MAGD;IAAM,CAAC,IACP;IACA,KAAO,UAASJ,MAAM,EAAEC,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAIU,MAAM,GAAIV,mBAAmB,CAAC,CAAC,CAAC,CAACU,MAAO;MAC5C,IAAIC,KAAK,GAAIX,mBAAmB,CAAC,CAAC,CAAC,CAACW,KAAM;MAC1C,IAAIC,KAAK,GAAGZ,mBAAmB,CAAC,CAAC,CAAC;MAClC,IAAIE,OAAO,GAAIF,mBAAmB,CAAC,CAAC,CAAC,CAACE,OAAQ;MAC9C,IAAIW,SAAS,GAAIb,mBAAmB,CAAC,CAAC,CAAC,CAACa,SAAU;MAClD,IAAIC,aAAa,GAAId,mBAAmB,CAAC,CAAC,CAAC,CAACc,aAAc;MAC1D,IAAIC,sBAAsB,GAAIf,mBAAmB,CAAC,CAAC,CAAC,CAACe,sBAAuB;MAC5E,IAAIC,KAAK,GAAIhB,mBAAmB,CAAC,CAAC,CAAC,CAACgB,KAAM;MAG1C,SAASC,QAAQA,CAACC,IAAI,EAAEC,GAAG,EAAE;QAC3B,OAAOA,GAAG,CAACC,OAAO,CAACF,IAAI,CAAC,KAAK,CAAC,CAAC;MACjC;MAEA,SAASG,KAAKA,CAACC,CAAC,EAAE;QAChB,OAAOA,CAAC,CAACC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;MAC/B;MAEA,SAASC,sBAAsBA,CAACC,IAAI,EAAE;QACpC,IAAIC,MAAM,GAAG,CAAC,CAAC;QACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;UACpC;UACAD,MAAM,CAACD,IAAI,CAACE,CAAC,CAAC,CAACJ,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,GAAGE,IAAI,CAACE,CAAC,CAAC;QAC9C;QACA,OAAOD,MAAM;MACf;MAEA,SAASG,aAAaA,CAACC,KAAK,EAAEC,IAAI,EAAE;QAClC,OAAOD,KAAK,IAAIA,KAAK,CAACE,IAAI,KAAKhB,KAAK,CAACiB,QAAQ,IAAIH,KAAK,CAACI,IAAI,KAAKH,IAAI;MACtE;MAEA,SAASI,cAAcA,CAACL,KAAK,EAAEM,KAAK,EAAE;QACpC,OAAON,KAAK,IAAIA,KAAK,CAACE,IAAI,KAAKhB,KAAK,CAACiB,QAAQ,IAAIhB,QAAQ,CAACa,KAAK,CAACI,IAAI,EAAEE,KAAK,CAAC;MAC9E;MACA;MACA,IAAIC,aAAa,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC;MAE1G,IAAIC,mBAAmB,GAAG,CAAC,gBAAgB,EAAE,eAAe,EAAE,kBAAkB,CAAC;;MAEjF;MACA,IAAIC,iBAAiB,GAAGf,sBAAsB,CAACc,mBAAmB,CAAC;MAEnE,IAAIE,oCAAoC,GAAG,CAACD,iBAAiB,CAACE,cAAc,EAAEF,iBAAiB,CAACG,gBAAgB,CAAC;MAEjH,IAAIC,IAAI,GAAG;QACTC,cAAc,EAAE,gBAAgB;QAAE;QAClCC,SAAS,EAAE,WAAW;QAAE;QACxBC,aAAa,EAAE,eAAe;QAAE;QAChCC,YAAY,EAAE,cAAc;QAAE;QAC9BC,cAAc,EAAE,gBAAgB;QAAE;QAClCC,WAAW,EAAE,aAAa;QAAE;QAC5BC,UAAU,EAAE,YAAY,CAAC;MAC3B,CAAC;MAED,SAASC,4BAA4BA,CAACC,MAAM,EAAEC,KAAK,EAAE;QACnD;QACA;QACA;QACA;;QAEA,IAAIA,KAAK,CAACC,eAAe,IACvBD,KAAK,CAACE,IAAI,KAAKZ,IAAI,CAACK,cAAc,IAClCK,KAAK,CAACE,IAAI,KAAKZ,IAAI,CAACM,WAAW,EAAE;UACjC;QACF;;QAEA;QACAG,MAAM,CAACI,aAAa,CAACH,KAAK,CAACI,gBAAgB,CAAC;MAC9C;;MAEA;MACA;MACA,SAASC,gBAAgBA,CAACpC,CAAC,EAAE;QAC3B;;QAEAA,CAAC,GAAGA,CAAC,CAACC,OAAO,CAACX,KAAK,CAAC+C,aAAa,EAAE,IAAI,CAAC;QACxC,IAAIC,GAAG,GAAG,EAAE;UACVC,GAAG,GAAGvC,CAAC,CAACF,OAAO,CAAC,IAAI,CAAC;QACvB,OAAOyC,GAAG,KAAK,CAAC,CAAC,EAAE;UACjBD,GAAG,CAACE,IAAI,CAACxC,CAAC,CAACyC,SAAS,CAAC,CAAC,EAAEF,GAAG,CAAC,CAAC;UAC7BvC,CAAC,GAAGA,CAAC,CAACyC,SAAS,CAACF,GAAG,GAAG,CAAC,CAAC;UACxBA,GAAG,GAAGvC,CAAC,CAACF,OAAO,CAAC,IAAI,CAAC;QACvB;QACA,IAAIE,CAAC,CAACM,MAAM,EAAE;UACZgC,GAAG,CAACE,IAAI,CAACxC,CAAC,CAAC;QACb;QACA,OAAOsC,GAAG;MACZ;MAEA,SAASI,QAAQA,CAACT,IAAI,EAAE;QACtB,OAAOA,IAAI,KAAKZ,IAAI,CAACI,YAAY;MACnC;MAEA,SAASkB,aAAaA,CAACV,IAAI,EAAE;QAC3B,OAAOtC,QAAQ,CAACsC,IAAI,EAAE,CAACZ,IAAI,CAACO,UAAU,EAAEP,IAAI,CAACK,cAAc,EAAEL,IAAI,CAACM,WAAW,CAAC,CAAC;MACjF;MAEA,SAASiB,oBAAoBA,CAACC,KAAK,EAAEC,CAAC,EAAE;QACtC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACvC,MAAM,EAAEyC,CAAC,EAAE,EAAE;UACrC,IAAIC,IAAI,GAAGH,KAAK,CAACE,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC;UAC1B,IAAID,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,KAAKJ,CAAC,EAAE;YACxB,OAAO,KAAK;UACd;QACF;QACA,OAAO,IAAI;MACb;MAEA,SAASK,wBAAwBA,CAACN,KAAK,EAAEO,MAAM,EAAE;QAC/C,IAAIL,CAAC,GAAG,CAAC;UACPM,GAAG,GAAGR,KAAK,CAACvC,MAAM;UAClB0C,IAAI;QACN,OAAOD,CAAC,GAAGM,GAAG,EAAEN,CAAC,EAAE,EAAE;UACnBC,IAAI,GAAGH,KAAK,CAACE,CAAC,CAAC;UACf;UACA,IAAIC,IAAI,IAAIA,IAAI,CAAClD,OAAO,CAACsD,MAAM,CAAC,KAAK,CAAC,EAAE;YACtC,OAAO,KAAK;UACd;QACF;QACA,OAAO,IAAI;MACb;MAGA,SAASzE,UAAUA,CAAC2E,WAAW,EAAEvE,OAAO,EAAE;QACxCA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;QACvB,IAAI,CAACwE,YAAY,GAAGD,WAAW,IAAI,EAAE;QAErC,IAAI,CAACE,OAAO,GAAG,IAAI;QACnB,IAAI,CAACC,OAAO,GAAG,IAAI;QACnB,IAAI,CAACC,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACC,MAAM,GAAG,IAAI;QAClB,IAAI,CAACC,eAAe,GAAG,IAAI;QAE3B,IAAI,CAACC,WAAW,GAAG,IAAI;QACvB,IAAI,CAACC,QAAQ,GAAG,IAAIlF,OAAO,CAACG,OAAO,CAAC;MACtC;MAEAJ,UAAU,CAACoF,SAAS,CAACC,YAAY,GAAG,UAASC,UAAU,EAAEhC,IAAI,EAAE;QAC7D,IAAIiC,iBAAiB,GAAG,CAAC;QACzB,IAAID,UAAU,EAAE;UACdC,iBAAiB,GAAGD,UAAU,CAACE,iBAAiB;UAChD,IAAI,CAAC,IAAI,CAACX,OAAO,CAACY,kBAAkB,CAAC,CAAC,IACpCH,UAAU,CAACI,iBAAiB,GAAGH,iBAAiB,EAAE;YAClDA,iBAAiB,GAAGD,UAAU,CAACI,iBAAiB;UAClD;QACF;QAEA,IAAIC,UAAU,GAAG;UACfrC,IAAI,EAAEA,IAAI;UACVsC,MAAM,EAAEN,UAAU;UAClBO,UAAU,EAAEP,UAAU,GAAGA,UAAU,CAACO,UAAU,GAAG,IAAInF,KAAK,CAACK,KAAK,CAAC+E,WAAW,EAAE,EAAE,CAAC;UAAE;UACnFC,SAAS,EAAET,UAAU,GAAGA,UAAU,CAACS,SAAS,GAAG,EAAE;UAAE;UACnDC,qBAAqB,EAAE,KAAK;UAC5BC,sBAAsB,EAAE,KAAK;UAC7B5C,eAAe,EAAE,KAAK;UACtB6C,YAAY,EAAE,KAAK;UACnBC,QAAQ,EAAE,KAAK;UACfC,UAAU,EAAE,KAAK;UACjBC,iBAAiB,EAAE,KAAK;UAAE;UAC1BC,QAAQ,EAAE,KAAK;UACfC,QAAQ,EAAE,KAAK;UACfC,YAAY,EAAE,KAAK;UACnBC,iBAAiB,EAAE,KAAK;UAAE;UAC1BC,OAAO,EAAE,KAAK;UAAE;UAChBC,SAAS,EAAE,KAAK;UAAE;UAClBC,UAAU,EAAE,KAAK;UAAE;UACnBpB,iBAAiB,EAAED,iBAAiB;UACpCsB,SAAS,EAAE,CAAC;UACZnB,iBAAiB,EAAEJ,UAAU,GAAGA,UAAU,CAACI,iBAAiB,GAAGH,iBAAiB;UAChF/B,gBAAgB,EAAE,IAAI,CAACqB,OAAO,CAACiC,eAAe,CAAC,CAAC;UAChDC,aAAa,EAAE;QACjB,CAAC;QACD,OAAOpB,UAAU;MACnB,CAAC;MAED3F,UAAU,CAACoF,SAAS,CAAC4B,MAAM,GAAG,UAASrC,WAAW,EAAE;QAClD,IAAIsC,gBAAgB,GAAGtC,WAAW,CAACuC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAEtD,IAAI,CAACnC,eAAe,GAAG,EAAE,CAAC,CAAC;QAC3B,IAAI,CAACF,OAAO,GAAG,IAAIpE,MAAM,CAAC,IAAI,CAAC0E,QAAQ,EAAE8B,gBAAgB,CAAC;;QAE1D;QACA,IAAI,CAACpC,OAAO,CAACsC,GAAG,GAAG,IAAI,CAAChC,QAAQ,CAACiC,eAAe;;QAGhD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,CAAClC,WAAW,GAAG,EAAE;QACrB,IAAI,CAACmC,QAAQ,CAAC3E,IAAI,CAACC,cAAc,CAAC;QAClC,IAAI2E,SAAS,GAAG,IAAI1G,SAAS,CAAC+D,WAAW,EAAE,IAAI,CAACQ,QAAQ,CAAC;QACzD,IAAI,CAACL,OAAO,GAAGwC,SAAS,CAACC,QAAQ,CAAC,CAAC;QACnC,OAAO5C,WAAW;MACpB,CAAC;MAED3E,UAAU,CAACoF,SAAS,CAAC9E,QAAQ,GAAG,YAAW;QACzC;QACA,IAAI,IAAI,CAAC6E,QAAQ,CAACqC,QAAQ,EAAE;UAC1B,OAAO,IAAI,CAAC5C,YAAY;QAC1B;QAEA,IAAI6C,UAAU;QACd,IAAI9C,WAAW,GAAG,IAAI,CAACqC,MAAM,CAAC,IAAI,CAACpC,YAAY,CAAC;QAEhD,IAAI8C,GAAG,GAAG,IAAI,CAACvC,QAAQ,CAACuC,GAAG;QAC3B,IAAI,IAAI,CAACvC,QAAQ,CAACuC,GAAG,KAAK,MAAM,EAAE;UAChCA,GAAG,GAAG,IAAI;UACV,IAAI/C,WAAW,IAAIhE,KAAK,CAACgH,SAAS,CAACC,IAAI,CAACjD,WAAW,IAAI,EAAE,CAAC,EAAE;YAC1D+C,GAAG,GAAG/C,WAAW,CAACuC,KAAK,CAACvG,KAAK,CAACgH,SAAS,CAAC,CAAC,CAAC,CAAC;UAC7C;QACF;QAEA,IAAIE,aAAa,GAAG,IAAI,CAAC/C,OAAO,CAACgD,IAAI,CAAC,CAAC;QACvC,OAAOD,aAAa,EAAE;UACpB,IAAI,CAACE,YAAY,CAACF,aAAa,CAAC;UAEhC,IAAI,CAAC9C,eAAe,GAAG,IAAI,CAACC,MAAM,CAACa,UAAU,CAAC5D,IAAI;UAClD,IAAI,CAAC+C,MAAM,CAACa,UAAU,GAAGgC,aAAa;UAEtCA,aAAa,GAAG,IAAI,CAAC/C,OAAO,CAACgD,IAAI,CAAC,CAAC;QACrC;QAEAL,UAAU,GAAG,IAAI,CAAC5C,OAAO,CAACmD,QAAQ,CAACN,GAAG,CAAC;QAEvC,OAAOD,UAAU;MACnB,CAAC;MAEDzH,UAAU,CAACoF,SAAS,CAAC2C,YAAY,GAAG,UAASF,aAAa,EAAEI,wBAAwB,EAAE;QACpF,IAAIJ,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACmH,UAAU,EAAE;UAC3C,IAAI,CAACC,iBAAiB,CAACN,aAAa,CAAC;QACvC,CAAC,MAAM,IAAIA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACqH,QAAQ,EAAE;UAChD,IAAI,CAACC,eAAe,CAACR,aAAa,CAAC;QACrC,CAAC,MAAM,IAAIA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAAC+E,WAAW,EAAE;UACnD,IAAI,CAACwC,kBAAkB,CAACT,aAAa,CAAC;QACxC,CAAC,MAAM,IAAIA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACwH,SAAS,EAAE;UACjD,IAAI,CAACC,gBAAgB,CAACX,aAAa,CAAC;QACtC,CAAC,MAAM,IAAIA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAAC0H,IAAI,EAAE;UAC5C,IAAI,CAACC,WAAW,CAACb,aAAa,CAAC;QACjC,CAAC,MAAM,IAAIA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACiB,QAAQ,EAAE;UAChD,IAAI,CAAC0G,WAAW,CAACb,aAAa,CAAC;QACjC,CAAC,MAAM,IAAIA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAAC4H,SAAS,EAAE;UACjD,IAAI,CAACC,gBAAgB,CAACf,aAAa,CAAC;QACtC,CAAC,MAAM,IAAIA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAAC8H,MAAM,EAAE;UAC9C,IAAI,CAACC,aAAa,CAACjB,aAAa,CAAC;QACnC,CAAC,MAAM,IAAIA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACgI,MAAM,EAAE;UAC9C,IAAI,CAACC,aAAa,CAACnB,aAAa,CAAC;QACnC,CAAC,MAAM,IAAIA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACkI,QAAQ,EAAE;UAChD,IAAI,CAACC,eAAe,CAACrB,aAAa,CAAC;QACrC,CAAC,MAAM,IAAIA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACoI,KAAK,EAAE;UAC7C,IAAI,CAACC,YAAY,CAACvB,aAAa,CAAC;QAClC,CAAC,MAAM,IAAIA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACsI,aAAa,EAAE;UACrD,IAAI,CAACC,oBAAoB,CAACzB,aAAa,EAAEI,wBAAwB,CAAC;QACpE,CAAC,MAAM,IAAIJ,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACwI,OAAO,EAAE;UAC/C,IAAI,CAACC,cAAc,CAAC3B,aAAa,EAAEI,wBAAwB,CAAC;QAC9D,CAAC,MAAM,IAAIJ,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAAC0I,GAAG,EAAE;UAC3C,IAAI,CAACC,UAAU,CAAC7B,aAAa,CAAC;QAChC,CAAC,MAAM,IAAIA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAAC4I,GAAG,EAAE;UAC3C,IAAI,CAACC,UAAU,CAAC/B,aAAa,CAAC;QAChC,CAAC,MAAM,IAAIA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAAC8I,OAAO,EAAE;UAC/C,IAAI,CAACC,cAAc,CAACjC,aAAa,EAAEI,wBAAwB,CAAC;QAC9D,CAAC,MAAM;UACL,IAAI,CAAC6B,cAAc,CAACjC,aAAa,EAAEI,wBAAwB,CAAC;QAC9D;MACF,CAAC;MAEDjI,UAAU,CAACoF,SAAS,CAAC2E,8BAA8B,GAAG,UAASlC,aAAa,EAAEI,wBAAwB,EAAE;QACtG,IAAI+B,QAAQ,GAAGnC,aAAa,CAACmC,QAAQ;QACrC,IAAIC,eAAe,GAAG,IAAI,CAAC9E,QAAQ,CAAC+E,sBAAsB,IAAInG,QAAQ,CAAC,IAAI,CAACiB,MAAM,CAAC1B,IAAI,CAAC;QAExF,IAAIuE,aAAa,CAACsC,eAAe,EAAE;UACjC,IAAIC,aAAa,GAAGvC,aAAa,CAACsC,eAAe,CAACrC,IAAI,CAAC,CAAC;UACxD,OAAOsC,aAAa,EAAE;YACpB;YACA;YACA;YACA,IAAI,CAACL,8BAA8B,CAACK,aAAa,EAAEnC,wBAAwB,CAAC;YAC5E,IAAI,CAACF,YAAY,CAACqC,aAAa,EAAEnC,wBAAwB,CAAC;YAC1DmC,aAAa,GAAGvC,aAAa,CAACsC,eAAe,CAACrC,IAAI,CAAC,CAAC;UACtD;QACF;QAEA,IAAImC,eAAe,EAAE;UACnB,KAAK,IAAI7F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4F,QAAQ,EAAE5F,CAAC,IAAI,CAAC,EAAE;YACpC,IAAI,CAACiG,aAAa,CAACjG,CAAC,GAAG,CAAC,EAAE6D,wBAAwB,CAAC;UACrD;QACF,CAAC,MAAM;UACL,IAAI,IAAI,CAAC9C,QAAQ,CAACmF,qBAAqB,IAAIN,QAAQ,GAAG,IAAI,CAAC7E,QAAQ,CAACmF,qBAAqB,EAAE;YACzFN,QAAQ,GAAG,IAAI,CAAC7E,QAAQ,CAACmF,qBAAqB;UAChD;UAEA,IAAI,IAAI,CAACnF,QAAQ,CAACoF,iBAAiB,EAAE;YACnC,IAAIP,QAAQ,GAAG,CAAC,EAAE;cAChB,IAAI,CAACK,aAAa,CAAC,KAAK,EAAEpC,wBAAwB,CAAC;cACnD,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,QAAQ,EAAEQ,CAAC,IAAI,CAAC,EAAE;gBACpC,IAAI,CAACH,aAAa,CAAC,IAAI,EAAEpC,wBAAwB,CAAC;cACpD;YACF;UACF;QACF;MAEF,CAAC;MAED,IAAIwC,yBAAyB,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;MAE1FzK,UAAU,CAACoF,SAAS,CAACsF,+BAA+B,GAAG,UAAS7C,aAAa,EAAE8C,cAAc,EAAE;QAC7FA,cAAc,GAAIA,cAAc,KAAKC,SAAS,GAAI,KAAK,GAAGD,cAAc;;QAExE;QACA,IAAI,IAAI,CAAC9F,OAAO,CAACY,kBAAkB,CAAC,CAAC,EAAE;UACrC;QACF;QAEA,IAAIoF,qBAAqB,GAAI,IAAI,CAAC1F,QAAQ,CAACoF,iBAAiB,IAAI1C,aAAa,CAACmC,QAAQ,IAAKW,cAAc;QACzG,IAAIG,oBAAoB,GAAG9J,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACa,UAAU,CAAC5D,IAAI,EAAEnB,sBAAsB,CAAC,IACtFE,QAAQ,CAAC6G,aAAa,CAAC5F,IAAI,EAAEnB,sBAAsB,CAAC;QAEtD,IAAIgK,oBAAoB,EAAE;UACxB,IAAIC,0BAA0B,GAC1B/J,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACa,UAAU,CAAC5D,IAAI,EAAEnB,sBAAsB,CAAC,IAC7DE,QAAQ,CAAC,IAAI,CAACmE,QAAQ,CAAC6F,iBAAiB,EAAEzI,oCAAoC,CAAC,IAEjFvB,QAAQ,CAAC6G,aAAa,CAAC5F,IAAI,EAAEnB,sBAAsB,CAAC;UACtD+J,qBAAqB,GAAGA,qBAAqB,IAAIE,0BAA0B;QAC7E;QAEA,IAAIF,qBAAqB,EAAE;UACzB,IAAI,CAACR,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC;QACjC,CAAC,MAAM,IAAI,IAAI,CAAClF,QAAQ,CAAC8F,gBAAgB,EAAE;UACzC,IAAI/I,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAE4E,yBAAyB,CAAC,EAAE;YACrE;YACA;YACA;UACF;UACA,IAAI,CAAC5F,OAAO,CAACqG,cAAc,CAAC,CAAC;QAC/B;MACF,CAAC;MAEDlL,UAAU,CAACoF,SAAS,CAACiF,aAAa,GAAG,UAASc,aAAa,EAAElD,wBAAwB,EAAE;QACrF,IAAI,CAACA,wBAAwB,EAAE;UAC7B,IAAI,IAAI,CAACjD,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,KAAK,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACkI,QAAQ,IAAI,IAAI,CAACjE,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,IAAI,CAAC,EAAE;YACzP,IAAImJ,UAAU,GAAG,IAAI,CAACtG,OAAO,CAACuG,IAAI,CAAC,CAAC;YACpC,OAAO,IAAI,CAACrG,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACE,SAAS,IACxC,EAAE,IAAI,CAACoC,MAAM,CAACmB,QAAQ,IAAIvE,aAAa,CAACwJ,UAAU,EAAE,MAAM,CAAC,CAAC,IAC5D,CAAC,IAAI,CAACpG,MAAM,CAACsB,QAAQ,EAAE;cACvB,IAAI,CAACgF,YAAY,CAAC,CAAC;YACrB;UACF;QACF;QAEA,IAAI,IAAI,CAACzG,OAAO,CAAC0G,YAAY,CAACJ,aAAa,CAAC,EAAE;UAC5C,IAAI,CAACnG,MAAM,CAAC3B,eAAe,GAAG,IAAI;QACpC;MACF,CAAC;MAEDrD,UAAU,CAACoF,SAAS,CAACoG,4BAA4B,GAAG,UAAS3D,aAAa,EAAE;QAC1E,IAAI,IAAI,CAAChD,OAAO,CAACY,kBAAkB,CAAC,CAAC,EAAE;UACrC,IAAI,IAAI,CAACN,QAAQ,CAAC+E,sBAAsB,IACtCrC,aAAa,CAACmC,QAAQ,KACrBnC,aAAa,CAAC5F,IAAI,KAAK,GAAG,IAAI8B,QAAQ,CAAC,IAAI,CAACiB,MAAM,CAAC1B,IAAI,CAAC,CAAC,EAAE;YAC5D,IAAI,CAACuB,OAAO,CAAC4G,YAAY,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;YACxC,IAAI,CAAC7G,OAAO,CAAC4G,YAAY,CAAC5H,IAAI,CAACgE,aAAa,CAAC8D,iBAAiB,CAAC;YAC/D,IAAI,CAAC9G,OAAO,CAAC+G,kBAAkB,GAAG,KAAK;UACzC,CAAC,MAAM,IAAI,IAAI,CAAC/G,OAAO,CAAC6G,UAAU,CAAC,IAAI,CAAC1G,MAAM,CAACQ,iBAAiB,EAAE,IAAI,CAACR,MAAM,CAAC6B,SAAS,CAAC,EAAE;YACxF,IAAI,CAAC7B,MAAM,CAACU,iBAAiB,GAAG,IAAI,CAACV,MAAM,CAACQ,iBAAiB;UAC/D;QACF;MACF,CAAC;MAEDxF,UAAU,CAACoF,SAAS,CAACyG,WAAW,GAAG,UAAShE,aAAa,EAAE;QACzD,IAAI,IAAI,CAAChD,OAAO,CAACsC,GAAG,EAAE;UACpB,IAAI,CAACtC,OAAO,CAACiH,aAAa,CAACjE,aAAa,CAAC;UACzC;QACF;QAEA,IAAI,IAAI,CAAC1C,QAAQ,CAAC4G,WAAW,IAAIlE,aAAa,CAACmE,QAAQ,IAAInE,aAAa,CAACmE,QAAQ,CAACjK,IAAI,KAAKhB,KAAK,CAACoI,KAAK,IACpG,IAAI,CAACtE,OAAO,CAACY,kBAAkB,CAAC,CAAC,EAAE;UACnC,IAAI,IAAI,CAACZ,OAAO,CAACoH,aAAa,CAACC,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;YAC7C,IAAIC,MAAM,GAAG,IAAI,CAACtH,OAAO,CAACoH,aAAa,CAACG,GAAG,CAAC,CAAC;YAC7C;YACA;YACA,IAAI,IAAI,CAACvH,OAAO,CAACoH,aAAa,CAACI,QAAQ,CAAC,CAAC,EAAE;cACzC,IAAI,CAACxH,OAAO,CAACoH,aAAa,CAACpI,IAAI,CAACsI,MAAM,CAAC;cACvC,IAAI,CAACtH,OAAO,CAACP,IAAI,CAAC,IAAI,CAAC;cACvB,IAAI,CAACO,OAAO,CAAC4G,YAAY,CAACW,GAAG,CAAC,CAAC;cAC/B,IAAI,CAACvH,OAAO,CAACP,IAAI,CAAC,CAAC;YACrB;;YAEA;YACA,IAAI,CAACkH,4BAA4B,CAAC3D,aAAa,CAAC;YAChD,IAAI,CAAChD,OAAO,CAACyH,SAAS,CAAC,GAAG,CAAC;YAC3B,IAAI,CAACzH,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;UACxC;QACF;QAEA,IAAI,CAACJ,4BAA4B,CAAC3D,aAAa,CAAC;QAChD,IAAI,CAAChD,OAAO,CAAC0H,kBAAkB,GAAG,IAAI;QACtC,IAAI,CAAC1H,OAAO,CAACyH,SAAS,CAACzE,aAAa,CAAC5F,IAAI,CAAC;QAC1C,IAAI,IAAI,CAAC4C,OAAO,CAAC2H,sBAAsB,EAAE;UACvC,IAAI,CAACxH,MAAM,CAAC3B,eAAe,GAAG,IAAI;QACpC;MACF,CAAC;MAEDrD,UAAU,CAACoF,SAAS,CAACX,MAAM,GAAG,YAAW;QACvC,IAAI,CAACO,MAAM,CAACQ,iBAAiB,IAAI,CAAC;QAClC,IAAI,CAACX,OAAO,CAAC6G,UAAU,CAAC,IAAI,CAAC1G,MAAM,CAACQ,iBAAiB,EAAE,IAAI,CAACR,MAAM,CAAC6B,SAAS,CAAC;MAC/E,CAAC;MAED7G,UAAU,CAACoF,SAAS,CAACqH,QAAQ,GAAG,YAAW;QACzC,IAAI,IAAI,CAACzH,MAAM,CAACQ,iBAAiB,GAAG,CAAC,KACjC,CAAC,IAAI,CAACR,MAAM,CAACY,MAAM,IAAK,IAAI,CAACZ,MAAM,CAACQ,iBAAiB,GAAG,IAAI,CAACR,MAAM,CAACY,MAAM,CAACJ,iBAAiB,CAAC,EAAE;UACjG,IAAI,CAACR,MAAM,CAACQ,iBAAiB,IAAI,CAAC;UAClC,IAAI,CAACX,OAAO,CAAC6G,UAAU,CAAC,IAAI,CAAC1G,MAAM,CAACQ,iBAAiB,EAAE,IAAI,CAACR,MAAM,CAAC6B,SAAS,CAAC;QAC/E;MACF,CAAC;MAED7G,UAAU,CAACoF,SAAS,CAACiC,QAAQ,GAAG,UAAS/D,IAAI,EAAE;QAC7C,IAAI,IAAI,CAAC0B,MAAM,EAAE;UACf,IAAI,CAACE,WAAW,CAACrB,IAAI,CAAC,IAAI,CAACmB,MAAM,CAAC;UAClC,IAAI,CAACC,eAAe,GAAG,IAAI,CAACD,MAAM;QACpC,CAAC,MAAM;UACL,IAAI,CAACC,eAAe,GAAG,IAAI,CAACI,YAAY,CAAC,IAAI,EAAE/B,IAAI,CAAC;QACtD;QAEA,IAAI,CAAC0B,MAAM,GAAG,IAAI,CAACK,YAAY,CAAC,IAAI,CAACJ,eAAe,EAAE3B,IAAI,CAAC;QAC3D,IAAI,CAACuB,OAAO,CAAC6G,UAAU,CAAC,IAAI,CAAC1G,MAAM,CAACQ,iBAAiB,EAAE,IAAI,CAACR,MAAM,CAAC6B,SAAS,CAAC;MAC/E,CAAC;MAGD7G,UAAU,CAACoF,SAAS,CAACkG,YAAY,GAAG,YAAW;QAC7C,IAAI,IAAI,CAACpG,WAAW,CAACvD,MAAM,GAAG,CAAC,EAAE;UAC/B,IAAI,CAACsD,eAAe,GAAG,IAAI,CAACD,MAAM;UAClC,IAAI,CAACA,MAAM,GAAG,IAAI,CAACE,WAAW,CAACkH,GAAG,CAAC,CAAC;UACpC,IAAI,IAAI,CAACnH,eAAe,CAAC3B,IAAI,KAAKZ,IAAI,CAACE,SAAS,EAAE;YAChDM,4BAA4B,CAAC,IAAI,CAAC2B,OAAO,EAAE,IAAI,CAACI,eAAe,CAAC;UAClE;UACA,IAAI,CAACJ,OAAO,CAAC6G,UAAU,CAAC,IAAI,CAAC1G,MAAM,CAACQ,iBAAiB,EAAE,IAAI,CAACR,MAAM,CAAC6B,SAAS,CAAC;QAC/E;MACF,CAAC;MAED7G,UAAU,CAACoF,SAAS,CAACsH,wBAAwB,GAAG,YAAW;QACzD,OAAO,IAAI,CAAC1H,MAAM,CAACY,MAAM,CAACtC,IAAI,KAAKZ,IAAI,CAACG,aAAa,IAAI,IAAI,CAACmC,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACE,SAAS,KACzF,IAAI,CAACoC,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC+C,MAAM,CAAC+B,aAAa,KAAK,CAAC,IAAM7E,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAE,CAAC;MACzI,CAAC;MAED7F,UAAU,CAACoF,SAAS,CAACuH,kBAAkB,GAAG,UAAS9E,aAAa,EAAE;QAChE,IAAI+E,KAAK,GAAG,KAAK;QACjBA,KAAK,GAAGA,KAAK,IAAI1K,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,IAAIgC,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAAC0H,IAAI;QACrHmE,KAAK,GAAGA,KAAK,IAAIhL,aAAa,CAAC,IAAI,CAACoD,MAAM,CAACa,UAAU,EAAE,IAAI,CAAC;QAC5D+G,KAAK,GAAGA,KAAK,IAAK,EAAE,IAAI,CAAC5H,MAAM,CAACY,MAAM,CAACtC,IAAI,KAAKZ,IAAI,CAACG,aAAa,IAAI,IAAI,CAACmC,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACE,SAAS,CAAC,IAAKV,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAE4E,yBAAyB,CAAC,IAAI,CAAC5C,aAAa,CAACmC,QAAQ;QAC3M4C,KAAK,GAAGA,KAAK,IAAIhL,aAAa,CAAC,IAAI,CAACoD,MAAM,CAACa,UAAU,EAAE,MAAM,CAAC,IAC5D,EAAEjE,aAAa,CAACiG,aAAa,EAAE,IAAI,CAAC,IAAI,CAACA,aAAa,CAACsC,eAAe,CAAC;QACzEyC,KAAK,GAAGA,KAAK,IAAK,IAAI,CAAC5H,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACqH,QAAQ,KAAK,IAAI,CAACnD,eAAe,CAAC3B,IAAI,KAAKZ,IAAI,CAACK,cAAc,IAAI,IAAI,CAACkC,eAAe,CAAC3B,IAAI,KAAKZ,IAAI,CAACM,WAAW,CAAE;QAC1K4J,KAAK,GAAGA,KAAK,IAAK,IAAI,CAAC5H,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAAC0H,IAAI,IAAI,IAAI,CAACzD,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACC,cAAc,IACtG,CAAC,IAAI,CAACqC,MAAM,CAAC0B,OAAO,IACpB,EAAEmB,aAAa,CAAC5F,IAAI,KAAK,IAAI,IAAI4F,aAAa,CAAC5F,IAAI,KAAK,IAAI,CAAC,IAC7D,IAAI,CAAC8C,eAAe,KAAK,UAAU,IACnC8C,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAAC0H,IAAI,IAAIZ,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACiB,QAAS;QAC7E4K,KAAK,GAAGA,KAAK,IAAK,IAAI,CAAC5H,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACG,aAAa,KACtD,IAAI,CAACmC,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC+C,MAAM,CAAC+B,aAAa,KAAK,CAAC,IAAK7E,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAE;QAEtI,IAAI+G,KAAK,EAAE;UACT,IAAI,CAACvF,QAAQ,CAAC3E,IAAI,CAACE,SAAS,CAAC;UAC7B,IAAI,CAAC6B,MAAM,CAAC,CAAC;UAEb,IAAI,CAACsF,8BAA8B,CAAClC,aAAa,EAAE,IAAI,CAAC;;UAExD;UACA;UACA;UACA,IAAI,CAAC,IAAI,CAAC6E,wBAAwB,CAAC,CAAC,EAAE;YACpC,IAAI,CAAChC,+BAA+B,CAAC7C,aAAa,EAChD3F,cAAc,CAAC2F,aAAa,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;UAChE;UACA,OAAO,IAAI;QACb;QACA,OAAO,KAAK;MACd,CAAC;MAED7H,UAAU,CAACoF,SAAS,CAAC+C,iBAAiB,GAAG,UAASN,aAAa,EAAE;QAC/D;QACA,IAAI,CAAC,IAAI,CAAC8E,kBAAkB,CAAC9E,aAAa,CAAC,EAAE;UAC3C,IAAI,CAACkC,8BAA8B,CAAClC,aAAa,CAAC;QACpD;QAEA,IAAIgF,SAAS,GAAGnK,IAAI,CAACO,UAAU;QAC/B,IAAI4E,aAAa,CAAC5F,IAAI,KAAK,GAAG,EAAE;UAE9B,IAAI,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAAC0H,IAAI,IAAI,IAAI,CAACzD,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,EAAE;YACrF;YACA;YACA,IAAIC,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAEhF,aAAa,CAAC,EAAE;cACzD,IAAI,CAACgE,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;YACxC;YACA,IAAI,CAACC,WAAW,CAAChE,aAAa,CAAC;YAC/B,IAAI,CAACR,QAAQ,CAACwF,SAAS,CAAC;YACxB,IAAI,CAACpI,MAAM,CAAC,CAAC;YACb,IAAI,IAAI,CAACU,QAAQ,CAAC2H,cAAc,EAAE;cAChC,IAAI,CAACjI,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;YACxC;YACA;UACF;UAEAiB,SAAS,GAAGnK,IAAI,CAACI,YAAY;UAC7B,IAAIiB,QAAQ,CAAC,IAAI,CAACiB,MAAM,CAAC1B,IAAI,CAAC,EAAE;YAC9B,IAAI,IAAI,CAAC0B,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,IACpC,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,KAAK,IAAI,CAAC8C,eAAe,KAAK,GAAG,IAAI,IAAI,CAACA,eAAe,KAAK,GAAG,CAAE,EAAE;cACzG;cACA;cACA,IAAI,CAAC,IAAI,CAACI,QAAQ,CAAC+E,sBAAsB,EAAE;gBACzC,IAAI,CAACG,aAAa,CAAC,CAAC;cACtB;YACF;UACF;UAEA,IAAI,CAACrJ,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACa,UAAU,CAAC9D,IAAI,EAAE,CAAChB,KAAK,CAACmH,UAAU,EAAEnH,KAAK,CAACqH,QAAQ,EAAErH,KAAK,CAAC0H,IAAI,EAAE1H,KAAK,CAACkI,QAAQ,EAAElI,KAAK,CAAC0I,GAAG,CAAC,CAAC,EAAE;YACrH,IAAI,CAAC5E,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;UACxC;QACF,CAAC,MAAM;UACL,IAAI,IAAI,CAAC5G,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACiB,QAAQ,EAAE;YAClD,IAAI,IAAI,CAACgD,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,KAAK,EAAE;cACzC,IAAI,CAAC4C,OAAO,CAAC+G,kBAAkB,GAAG,IAAI,CAACzG,QAAQ,CAAC4H,wBAAwB;cACxEF,SAAS,GAAGnK,IAAI,CAACK,cAAc;YACjC,CAAC,MAAM,IAAI/B,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACa,UAAU,CAAC5D,IAAI,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,EAAE;cAC3E,IAAI,CAAC4C,OAAO,CAAC+G,kBAAkB,GAAG,IAAI,CAACzG,QAAQ,CAAC4H,wBAAwB;cACxEF,SAAS,GAAGnK,IAAI,CAACM,WAAW;YAC9B,CAAC,MAAM,IAAIhC,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACe,SAAS,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE;cAC9D;cACA,IAAI,CAAClB,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;YACxC,CAAC,MAAM,IAAI,IAAI,CAAC5G,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,QAAQ,IAAI4F,aAAa,CAAC8D,iBAAiB,KAAK,EAAE,EAAE;cAC7F,IAAI,CAAC9G,OAAO,CAAC+G,kBAAkB,GAAG,KAAK;YACzC,CAAC,MAAM,IAAI5K,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACa,UAAU,CAAC5D,IAAI,EAAEpB,aAAa,CAAC,IAAI,IAAI,CAACmE,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,OAAO,EAAE;cAC1G,IAAI,CAAC4C,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;YACxC;UACF,CAAC,MAAM,IAAI,IAAI,CAAC5G,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACgI,MAAM,IAAI,IAAI,CAAC/D,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACkI,QAAQ,EAAE;YACzG;YACA;YACA;YACA,IAAI,CAAC,IAAI,CAACyD,wBAAwB,CAAC,CAAC,EAAE;cACpC,IAAI,CAAChC,+BAA+B,CAAC7C,aAAa,CAAC;YACrD;UACF,CAAC,MAAM,IAAI,IAAI,CAAC7C,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAAC0H,IAAI,EAAE;YACrD,IAAI,CAAC5D,OAAO,CAAC+G,kBAAkB,GAAG,KAAK;;YAEvC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAIoB,aAAa,GAAG,IAAI,CAAClI,OAAO,CAACuG,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,IAAI,CAAClG,QAAQ,CAAC8H,0BAA0B,IAAID,aAAa,EAAE;cAC7D;cACA,IAAIE,eAAe,GAAG,IAAI,CAACpI,OAAO,CAACuG,IAAI,CAAC,CAAC,CAAC,CAAC;cAC3C,IAAInJ,cAAc,CAAC8K,aAAa,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,IACrDA,aAAa,CAAC/K,IAAI,KAAK,GAAG,IAAIC,cAAc,CAACgL,eAAe,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAE,EAAE;gBACxF,IAAI,CAACrI,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;cACxC,CAAC,MAAM,IAAI,IAAI,CAAC5G,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACG,aAAa,EAAE;gBAClD,IAAKmK,aAAa,CAAC/K,IAAI,KAAK,GAAG,IAAI+K,aAAa,CAAC/K,IAAI,KAAK,GAAG,IAC1D+K,aAAa,CAAC/K,IAAI,KAAK,GAAG,KAAKiL,eAAe,CAACjL,IAAI,KAAK,GAAG,IAAIiL,eAAe,CAACjL,IAAI,KAAK,GAAG,CAAE,EAAE;kBAChG,IAAI,CAAC4C,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;gBACxC;cACF,CAAC,MAAM,IAAI,IAAI,CAAC5G,MAAM,CAACY,MAAM,IAAI,IAAI,CAACZ,MAAM,CAACY,MAAM,CAACS,iBAAiB,EAAE;gBACrE,IAAI,CAACxB,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;cACxC;YACF;UACF,CAAC,MAAM;YACL;YACA;YACA;YACA;YACA,IAAI,CAAClB,+BAA+B,CAAC7C,aAAa,CAAC;UACrD;;UAEA;UACA;UACA;UACA,IAAK,IAAI,CAAC7C,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACiB,QAAQ,KAAK,IAAI,CAACgD,MAAM,CAACe,SAAS,KAAK,UAAU,IAAI,IAAI,CAACf,MAAM,CAACe,SAAS,KAAK,QAAQ,CAAC,IAChI,IAAI,CAACf,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,KACjCjB,QAAQ,CAAC,IAAI,CAAC+D,eAAe,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,IACnD,IAAI,CAACC,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACG,aAAa,IAAI7B,QAAQ,CAAC,IAAI,CAAC+D,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAE,CAAE,EAAE;YAC/F,IAAI,CAACF,OAAO,CAAC+G,kBAAkB,GAAG,IAAI,CAACzG,QAAQ,CAACgI,yBAAyB;UAC3E;QACF;QAEA,IAAI,IAAI,CAACnI,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAAC+E,WAAW,EAAE;UAC5F,IAAI,CAACuE,aAAa,CAAC,CAAC;QACtB,CAAC,MAAM,IAAI,IAAI,CAACrF,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACqH,QAAQ,IAAI,IAAI,CAACpD,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACmH,UAAU,IAAI,IAAI,CAAClD,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACwH,SAAS,IAAI,IAAI,CAACvD,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACoI,KAAK,EAAE;UACtP;UACA;UACA,IAAI,CAACuB,+BAA+B,CAAC7C,aAAa,EAAEA,aAAa,CAACmC,QAAQ,CAAC;QAC7E;QAEA,IAAI,CAAC6B,WAAW,CAAChE,aAAa,CAAC;QAC/B,IAAI,CAACR,QAAQ,CAACwF,SAAS,CAAC;QACxB,IAAI,IAAI,CAAC1H,QAAQ,CAAC2H,cAAc,EAAE;UAChC,IAAI,CAACjI,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;QACxC;;QAEA;QACA,IAAI,CAACnH,MAAM,CAAC,CAAC;MACf,CAAC;MAEDzE,UAAU,CAACoF,SAAS,CAACiD,eAAe,GAAG,UAASR,aAAa,EAAE;QAC7D;QACA;QACA,OAAO,IAAI,CAAC7C,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACE,SAAS,EAAE;UAC1C,IAAI,CAAC0I,YAAY,CAAC,CAAC;QACrB;QAEA,IAAI,CAACvB,8BAA8B,CAAClC,aAAa,CAAC;QAElD,IAAI,IAAI,CAAC7C,MAAM,CAAC3B,eAAe,EAAE;UAC/B,IAAI,CAACqH,+BAA+B,CAAC7C,aAAa,EAChDA,aAAa,CAAC5F,IAAI,KAAK,GAAG,IAAI8B,QAAQ,CAAC,IAAI,CAACiB,MAAM,CAAC1B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC6B,QAAQ,CAAC+E,sBAAsB,CAAC;QACtG;QAEA,IAAI,IAAI,CAAC/E,QAAQ,CAAC2H,cAAc,EAAE;UAChC,IAAI,IAAI,CAAC9H,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACmH,UAAU,IAAI,CAAC,IAAI,CAAC/C,QAAQ,CAACiI,oBAAoB,EAAE;YAC3F;YACA,IAAI,CAACvI,OAAO,CAACP,IAAI,CAAC,CAAC;YACnB,IAAI,CAACO,OAAO,CAAC+G,kBAAkB,GAAG,KAAK;UACzC,CAAC,MAAM;YACL,IAAI,CAAC/G,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;UACxC;QACF;QACA,IAAI,CAACa,QAAQ,CAAC,CAAC;QACf,IAAI,CAACZ,WAAW,CAAChE,aAAa,CAAC;QAC/B,IAAI,CAACyD,YAAY,CAAC,CAAC;QAEnBpI,4BAA4B,CAAC,IAAI,CAAC2B,OAAO,EAAE,IAAI,CAACI,eAAe,CAAC;;QAEhE;QACA,IAAI,IAAI,CAACD,MAAM,CAACuB,QAAQ,IAAI,IAAI,CAACtB,eAAe,CAAC3B,IAAI,KAAKZ,IAAI,CAACM,WAAW,EAAE;UAC1E,IAAI,CAACiC,eAAe,CAAC3B,IAAI,GAAGZ,IAAI,CAACO,UAAU;UAC3C,IAAI,CAAC+B,MAAM,CAACsB,QAAQ,GAAG,KAAK;UAC5B,IAAI,CAACtB,MAAM,CAACuB,QAAQ,GAAG,KAAK;QAE9B;MACF,CAAC;MAEDvG,UAAU,CAACoF,SAAS,CAACkD,kBAAkB,GAAG,UAAST,aAAa,EAAE;QAChE,IAAI,CAACkC,8BAA8B,CAAClC,aAAa,CAAC;;QAElD;QACA,IAAIuD,UAAU,GAAG,IAAI,CAACtG,OAAO,CAACuG,IAAI,CAAC,CAAC;QACpC,IAAIgC,YAAY,GAAG,IAAI,CAACvI,OAAO,CAACuG,IAAI,CAAC,CAAC,CAAC;QACvC,IAAI,IAAI,CAACrG,MAAM,CAACe,SAAS,KAAK,QAAQ,IAAI,IAAI,CAACf,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACqH,QAAQ,EAAE;UACxF,IAAI,CAACf,QAAQ,CAAC3E,IAAI,CAACC,cAAc,CAAC;UAClC,IAAI,CAACqC,MAAM,CAACyB,iBAAiB,GAAG,IAAI;QACtC,CAAC,MAAM,IAAI,IAAI,CAACzB,MAAM,CAAC2B,SAAS,EAAE;UAChC,IAAI,CAACU,QAAQ,CAAC3E,IAAI,CAACC,cAAc,CAAC;QACpC,CAAC,MAAM,IAAI0K,YAAY,KAClBrM,QAAQ,CAACqM,YAAY,CAACpL,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAIjB,QAAQ,CAACoK,UAAU,CAACrJ,IAAI,EAAE,CAAChB,KAAK,CAAC8H,MAAM,EAAE9H,KAAK,CAAC0H,IAAI,EAAE1H,KAAK,CAACiB,QAAQ,CAAC,CAAC,IAChHhB,QAAQ,CAACoK,UAAU,CAACnJ,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,IAAIjB,QAAQ,CAACqM,YAAY,CAACtL,IAAI,EAAE,CAAChB,KAAK,CAAC0H,IAAI,EAAE1H,KAAK,CAACiB,QAAQ,CAAC,CAAE,CAChH,EAAE;UACH;UACA;UACA,IAAIhB,QAAQ,CAAC,IAAI,CAAC+D,eAAe,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,IAAI,CAAC/D,QAAQ,CAACqM,YAAY,CAACpL,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE;YACtG,IAAI,CAACoF,QAAQ,CAAC3E,IAAI,CAACC,cAAc,CAAC;UACpC,CAAC,MAAM;YACL,IAAI,CAAC0E,QAAQ,CAAC3E,IAAI,CAACG,aAAa,CAAC;UACnC;QACF,CAAC,MAAM,IAAI,IAAI,CAACmC,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACkI,QAAQ,IAAI,IAAI,CAACjE,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,IAAI,EAAE;UACjG;UACA,IAAI,CAACoF,QAAQ,CAAC3E,IAAI,CAACC,cAAc,CAAC;QACpC,CAAC,MAAM,IAAI3B,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACa,UAAU,CAAC9D,IAAI,EAAE,CAAChB,KAAK,CAACgI,MAAM,EAAEhI,KAAK,CAACmH,UAAU,EAAEnH,KAAK,CAACoI,KAAK,EAAEpI,KAAK,CAACkI,QAAQ,CAAC,CAAC,IAC7G/G,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,EAChF;UACA;UACA;UACA;UACA;UACA,IAAI,CAACwB,QAAQ,CAAC3E,IAAI,CAACG,aAAa,CAAC;QACnC,CAAC,MAAM;UACL,IAAI,CAACwE,QAAQ,CAAC3E,IAAI,CAACC,cAAc,CAAC;QACpC;QAEA,IAAI,IAAI,CAACqC,MAAM,CAACa,UAAU,EAAE;UAC1B,IAAI3D,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,CAACmG,QAAQ,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,EAAE;YACzE,IAAI,CAAChH,MAAM,CAACqB,iBAAiB,GAAG,IAAI;UACtC;QACF;QAEA,IAAIiH,YAAY,GAAG,CAAClC,UAAU,CAACjB,eAAe,IAAIiB,UAAU,CAACnJ,IAAI,KAAK,GAAG;QACzE,IAAIsL,wBAAwB,GAAGD,YAAY,IAAI,IAAI,CAACtI,MAAM,CAACe,SAAS,KAAK,UAAU,IACjF,IAAI,CAACf,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACqH,QAAQ;QAEhD,IAAI,IAAI,CAACjD,QAAQ,CAACqI,qBAAqB;UAAE;UACzC;YACE;YACA,IAAIC,KAAK,GAAG,CAAC;YACb,IAAIC,WAAW,GAAG,IAAI;YACtB,IAAI,CAAC1I,MAAM,CAACkB,YAAY,GAAG,IAAI;YAC/B,GAAG;cACDuH,KAAK,IAAI,CAAC;cACVC,WAAW,GAAG,IAAI,CAAC5I,OAAO,CAACuG,IAAI,CAACoC,KAAK,GAAG,CAAC,CAAC;cAC1C,IAAIC,WAAW,CAAC1D,QAAQ,EAAE;gBACxB,IAAI,CAAChF,MAAM,CAACkB,YAAY,GAAG,KAAK;gBAChC;cACF;YACF,CAAC,QAAQwH,WAAW,CAAC3L,IAAI,KAAKhB,KAAK,CAAC4I,GAAG,IACrC,EAAE+D,WAAW,CAAC3L,IAAI,KAAKhB,KAAK,CAACwH,SAAS,IAAImF,WAAW,CAACC,MAAM,KAAK9F,aAAa,CAAC;UACnF;QAEA,IAAI,CAAC,IAAI,CAAC1C,QAAQ,CAACyI,WAAW,KAAK,QAAQ,IACtC,IAAI,CAACzI,QAAQ,CAACyI,WAAW,KAAK,MAAM,IAAI/F,aAAa,CAACmC,QAAS,KAClE,CAAC,IAAI,CAAChF,MAAM,CAACkB,YAAY,EAAE;UAC3B,IAAI,IAAI,CAAClB,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACkI,QAAQ,KAC/CsE,wBAAwB,IACvB,IAAI,CAACvI,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACgI,MAAM,IAC3C7G,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAEzD,aAAa,CAAC,IAAI,IAAI,CAAC4C,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,MAAO,CAAC,EAAE;YACtG,IAAI,CAAC4C,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;UACxC,CAAC,MAAM;YACL,IAAI,CAACvB,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC;UACjC;QACF,CAAC,MAAM;UAAE;UACP,IAAItG,QAAQ,CAAC,IAAI,CAACkB,eAAe,CAAC3B,IAAI,CAAC,KAAK,IAAI,CAAC0B,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACmH,UAAU,IAAI,IAAI,CAAClD,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACoI,KAAK,CAAC,EAAE;YAC5I,IAAI,IAAI,CAACnE,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACoI,KAAK,IAAI,IAAI,CAAChE,QAAQ,CAAC2H,cAAc,EAAE;cAC/E,IAAI,CAACjI,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;YACxC;YAEA,IAAI,IAAI,CAAC5G,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACoI,KAAK,IAAK,IAAI,CAACnE,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACmH,UAAU,IAAI,IAAI,CAAClD,MAAM,CAACkB,YAAa,EAAE;cACjI,IAAI,CAACwE,+BAA+B,CAAC7C,aAAa,CAAC;cACnD,IAAI,CAAC5C,eAAe,CAAC5B,eAAe,GAAG,IAAI,CAAC4B,eAAe,CAAC5B,eAAe,IAAI,IAAI,CAAC2B,MAAM,CAAC3B,eAAe;cAC1G,IAAI,CAAC2B,MAAM,CAAC3B,eAAe,GAAG,KAAK;YACrC;UACF;UACA,IAAI,IAAI,CAAC2B,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACkI,QAAQ,IAAI,IAAI,CAACjE,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACmH,UAAU,EAAE;YACtG,IAAIlH,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACa,UAAU,CAAC9D,IAAI,EAAE,CAAChB,KAAK,CAAC+E,WAAW,EAAE/E,KAAK,CAAC4H,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC3D,MAAM,CAACkB,YAAY,EAAE;cAC5G,IAAI,CAACmE,aAAa,CAAC,CAAC;YACtB,CAAC,MAAM;cACL,IAAI,CAACxF,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;YACxC;UACF;QACF;QACA,IAAI,CAACC,WAAW,CAAChE,aAAa,CAAC;QAC/B,IAAI,CAACpD,MAAM,CAAC,CAAC;;QAEb;QACA,IAAI,CAAC6I,YAAY,IAAI,EAAE,IAAI,CAACnI,QAAQ,CAACqI,qBAAqB,IAAI,IAAI,CAACxI,MAAM,CAACkB,YAAY,CAAC,EAAE;UACvF,IAAI,CAACmE,aAAa,CAAC,CAAC;QACtB;MACF,CAAC;MAEDrK,UAAU,CAACoF,SAAS,CAACoD,gBAAgB,GAAG,UAASX,aAAa,EAAE;QAC9D;QACA,IAAI,CAACkC,8BAA8B,CAAClC,aAAa,CAAC;QAElD,OAAO,IAAI,CAAC7C,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACE,SAAS,EAAE;UAC1C,IAAI,CAAC0I,YAAY,CAAC,CAAC;QACrB;QAEA,IAAIgC,YAAY,GAAG,IAAI,CAACtI,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAAC+E,WAAW;QAEpE,IAAI,IAAI,CAACd,MAAM,CAACkB,YAAY,IAAI,CAACoH,YAAY,EAAE;UAAE;UAC/C,IAAI,CAACzI,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;QACxC,CAAC,MAAM,IAAI,IAAI,CAACzG,QAAQ,CAACyI,WAAW,KAAK,QAAQ,EAAE;UACjD,IAAI,CAACN,YAAY,EAAE;YACjB,IAAI,CAACjD,aAAa,CAAC,CAAC;UACtB;QACF,CAAC,MAAM;UACL;UACA,IAAI,CAACiD,YAAY,EAAE;YACjB,IAAIvJ,QAAQ,CAAC,IAAI,CAACiB,MAAM,CAAC1B,IAAI,CAAC,IAAI,IAAI,CAAC6B,QAAQ,CAAC+E,sBAAsB,EAAE;cACtE;cACA,IAAI,CAAC/E,QAAQ,CAAC+E,sBAAsB,GAAG,KAAK;cAC5C,IAAI,CAACG,aAAa,CAAC,CAAC;cACpB,IAAI,CAAClF,QAAQ,CAAC+E,sBAAsB,GAAG,IAAI;YAE7C,CAAC,MAAM;cACL,IAAI,CAACG,aAAa,CAAC,CAAC;YACtB;UACF;QACF;QACA,IAAI,CAACiB,YAAY,CAAC,CAAC;QACnB,IAAI,CAACO,WAAW,CAAChE,aAAa,CAAC;MACjC,CAAC;MAED7H,UAAU,CAACoF,SAAS,CAACsD,WAAW,GAAG,UAASb,aAAa,EAAE;QACzD,IAAIA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACiB,QAAQ,EAAE;UACzC,IAAIhB,QAAQ,CAAC6G,aAAa,CAAC5F,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC+C,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACG,aAAa,EAAE;YAC3FgF,aAAa,CAAC9F,IAAI,GAAGhB,KAAK,CAAC0H,IAAI;UACjC,CAAC,MAAM,IAAIZ,aAAa,CAAC5F,IAAI,KAAK,QAAQ,IAAIjB,QAAQ,CAAC,IAAI,CAAC8D,OAAO,CAACuG,IAAI,CAAC,CAAC,CAACpJ,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE;YAC5F4F,aAAa,CAAC9F,IAAI,GAAGhB,KAAK,CAAC0H,IAAI;UACjC,CAAC,MAAM,IAAIzH,QAAQ,CAAC6G,aAAa,CAAC5F,IAAI,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC+C,MAAM,CAACwB,YAAY,EAAE;YACpFqB,aAAa,CAAC9F,IAAI,GAAGhB,KAAK,CAAC0H,IAAI;UACjC,CAAC,MAAM,IAAI,IAAI,CAACzD,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACG,aAAa,EAAE;YAClD,IAAIuI,UAAU,GAAG,IAAI,CAACtG,OAAO,CAACuG,IAAI,CAAC,CAAC;YACpC,IAAID,UAAU,CAACnJ,IAAI,KAAK,GAAG,EAAE;cAC3B4F,aAAa,CAAC9F,IAAI,GAAGhB,KAAK,CAAC0H,IAAI;YACjC;UACF;QACF;QAEA,IAAI,IAAI,CAACkE,kBAAkB,CAAC9E,aAAa,CAAC,EAAE;UAC1C;UACA,IAAI3F,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,IAAIgC,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAAC0H,IAAI,EAAE;YACxG,IAAI,CAACzD,MAAM,CAACgB,qBAAqB,GAAG,IAAI;UAC1C;QACF,CAAC,MAAM,IAAI6B,aAAa,CAACmC,QAAQ,IAAI,CAAChG,aAAa,CAAC,IAAI,CAACgB,MAAM,CAAC1B,IAAI,CAAC,KAClE,IAAI,CAAC0B,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACkI,QAAQ,IAAK,IAAI,CAACjE,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,IAAK,CAAC,IAClI,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACgI,MAAM,KAC3C,IAAI,CAAC5D,QAAQ,CAACoF,iBAAiB,IAAI,CAACrI,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;UACrH,IAAI,CAACkE,8BAA8B,CAAClC,aAAa,CAAC;UAClD,IAAI,CAACwC,aAAa,CAAC,CAAC;QACtB,CAAC,MAAM;UACL,IAAI,CAACN,8BAA8B,CAAClC,aAAa,CAAC;QACpD;QAEA,IAAI,IAAI,CAAC7C,MAAM,CAACsB,QAAQ,IAAI,CAAC,IAAI,CAACtB,MAAM,CAACuB,QAAQ,EAAE;UACjD,IAAI3E,aAAa,CAACiG,aAAa,EAAE,OAAO,CAAC,EAAE;YACzC;YACA,IAAI,CAAChD,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;YACtC,IAAI,CAACC,WAAW,CAAChE,aAAa,CAAC;YAC/B,IAAI,CAAChD,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;YACtC,IAAI,CAAC5G,MAAM,CAACuB,QAAQ,GAAG,IAAI;YAC3B;UACF,CAAC,MAAM;YACL;YACA;YACA,IAAI,CAAC8D,aAAa,CAAC,CAAC;YACpB,IAAI,CAACrF,MAAM,CAACsB,QAAQ,GAAG,KAAK;UAC9B;QACF;;QAEA;QACA;QACA;QACA,IAAI,IAAI,CAACtB,MAAM,CAACmB,QAAQ,EAAE;UACxB,IAAI,CAAC,IAAI,CAACnB,MAAM,CAACoB,UAAU,IAAIxE,aAAa,CAACiG,aAAa,EAAE,MAAM,CAAC,EAAE;YACnE,IAAI,CAAC7C,MAAM,CAACoB,UAAU,GAAG,IAAI;UAC/B,CAAC,MAAM;YACL,OAAO,IAAI,CAACpB,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACE,SAAS,EAAE;cAC1C,IAAI,CAAC0I,YAAY,CAAC,CAAC;YACrB;YACA,IAAI,CAACtG,MAAM,CAACmB,QAAQ,GAAG,KAAK;YAC5B,IAAI,CAACnB,MAAM,CAACoB,UAAU,GAAG,KAAK;UAChC;QACF;QAEA,IAAI,IAAI,CAACpB,MAAM,CAACyB,iBAAiB,IAAIvE,cAAc,CAAC2F,aAAa,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,EAAE;UACvF,IAAI,CAACwC,aAAa,CAAC,CAAC;UACpB,IAAI,CAAC,IAAI,CAACrF,MAAM,CAAC4B,UAAU,KAAK,IAAI,CAAC5B,MAAM,CAAC2B,SAAS,IAAI,IAAI,CAACxB,QAAQ,CAAC0I,YAAY,CAAC,EAAE;YACpF;YACA,IAAI,CAACpB,QAAQ,CAAC,CAAC;UACjB;UACA,IAAI,CAACzH,MAAM,CAAC2B,SAAS,GAAG,KAAK;UAE7B,IAAI,CAACkF,WAAW,CAAChE,aAAa,CAAC;UAC/B,IAAI,CAAC7C,MAAM,CAAC0B,OAAO,GAAG,IAAI;UAC1B;QACF;QAEA,IAAI,IAAI,CAAC1B,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACoI,KAAK,IAAI,IAAI,CAACnE,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACmH,UAAU,IAAI,IAAI,CAAClD,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACgI,MAAM,IAAI,IAAI,CAAC/D,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACkI,QAAQ,EAAE;UACrM,IAAI,CAAC,IAAI,CAACyD,wBAAwB,CAAC,CAAC,IAAI;UACpC;UACA1L,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACa,UAAU,CAAC5D,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC8C,eAAe,KAAK,GAAG,IAAI,IAAI,CAACC,MAAM,CAACY,MAAM,CAACtC,IAAI,KAAKZ,IAAI,CAACG,aAAa,CAAC,EAAE;YACxI,IAAI,CAAC6H,+BAA+B,CAAC7C,aAAa,CAAC;UACrD;QACF;QAEA,IAAIjG,aAAa,CAACiG,aAAa,EAAE,UAAU,CAAC,EAAE;UAC5C,IAAI7G,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACa,UAAU,CAAC5D,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAClD,IAAI,CAAC4C,OAAO,CAACY,kBAAkB,CAAC,CAAC,IAAI,EAAEzE,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACa,UAAU,CAAC5D,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACkI,QAAQ,CAAE,EAAE;YACnK;YACA;YACA,IAAI,CAAC,IAAI,CAACpE,OAAO,CAACiJ,oBAAoB,CAAC,CAAC,IAAI,CAACjG,aAAa,CAACsC,eAAe,EAAE;cAC1E,IAAI,CAACE,aAAa,CAAC,CAAC;cACpB,IAAI,CAACA,aAAa,CAAC,IAAI,CAAC;YAC1B;UACF;UACA,IAAI,IAAI,CAACrF,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACiB,QAAQ,IAAI,IAAI,CAACgD,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAAC0H,IAAI,EAAE;YAChG,IAAIvG,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,IACzE3D,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAE4E,yBAAyB,CAAC,EAAE;cACnE,IAAI,CAAC5F,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;YACxC,CAAC,MAAM,IAAIhK,aAAa,CAAC,IAAI,CAACoD,MAAM,CAACa,UAAU,EAAE,SAAS,CAAC,IAAI,IAAI,CAACd,eAAe,KAAK,QAAQ,EAAE;cAChG,IAAI,CAACF,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;YACxC,CAAC,MAAM,IAAI,IAAI,CAAC5G,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,SAAS,EAAE;cACpD;cACA,IAAI,CAAC4C,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;YACxC,CAAC,MAAM;cACL,IAAI,CAACvB,aAAa,CAAC,CAAC;YACtB;UACF,CAAC,MAAM,IAAI,IAAI,CAACrF,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACkI,QAAQ,IAAI,IAAI,CAACjE,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,EAAE;YAChG;YACA,IAAI,CAAC4C,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;UACxC,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC5G,MAAM,CAAC3B,eAAe,KAAKW,aAAa,CAAC,IAAI,CAACgB,MAAM,CAAC1B,IAAI,CAAC,IAAIS,QAAQ,CAAC,IAAI,CAACiB,MAAM,CAAC1B,IAAI,CAAC,CAAC,EAAE;YAC1G;UAAA,CACD,MAAM;YACL,IAAI,CAAC+G,aAAa,CAAC,CAAC;UACtB;UAEA,IAAI,CAACwB,WAAW,CAAChE,aAAa,CAAC;UAC/B,IAAI,CAAC7C,MAAM,CAACe,SAAS,GAAG8B,aAAa,CAAC5F,IAAI;UAC1C;QACF;QAEA,IAAI8L,MAAM,GAAG,MAAM;QAEnB,IAAI,IAAI,CAAC/I,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACwH,SAAS,EAAE;UAEnD,IAAI,IAAI,CAACtD,eAAe,CAACiB,YAAY,EAAE;YACrC6H,MAAM,GAAG,OAAO;UAClB,CAAC,MAAM,IAAI,CAAC7L,cAAc,CAAC2F,aAAa,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,EAAE;YAC/EkG,MAAM,GAAG,SAAS;UACpB,CAAC,MAAM;YACL,IAAI,IAAI,CAAC5I,QAAQ,CAACyI,WAAW,KAAK,QAAQ,IACxC,IAAI,CAACzI,QAAQ,CAACyI,WAAW,KAAK,YAAY,IACzC,IAAI,CAACzI,QAAQ,CAACyI,WAAW,KAAK,MAAM,IAAI/F,aAAa,CAACmC,QAAS,EAAE;cAClE+D,MAAM,GAAG,SAAS;YACpB,CAAC,MAAM;cACLA,MAAM,GAAG,OAAO;cAChB,IAAI,CAAClJ,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;YACxC;UACF;QACF,CAAC,MAAM,IAAI,IAAI,CAAC5G,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAAC4H,SAAS,IAAI,IAAI,CAAC3D,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACC,cAAc,EAAE;UACtG;UACAoL,MAAM,GAAG,SAAS;QACpB,CAAC,MAAM,IAAI,IAAI,CAAC/I,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAAC4H,SAAS,IAAI3E,aAAa,CAAC,IAAI,CAACgB,MAAM,CAAC1B,IAAI,CAAC,EAAE;UAC7FyK,MAAM,GAAG,OAAO;QAClB,CAAC,MAAM,IAAI,IAAI,CAAC/I,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAAC8H,MAAM,EAAE;UACvDkF,MAAM,GAAG,SAAS;QACpB,CAAC,MAAM,IAAI,IAAI,CAAC/I,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACiB,QAAQ,IAAI,IAAI,CAACgD,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAAC0H,IAAI,IACpG,IAAI,CAACzD,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,KACjCjB,QAAQ,CAAC,IAAI,CAAC+D,eAAe,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,IACnD,IAAI,CAACC,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACG,aAAa,IAAI7B,QAAQ,CAAC,IAAI,CAAC+D,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAE,CAAE,EAAE;UAC/FgJ,MAAM,GAAG,OAAO;QAClB,CAAC,MAAM,IAAI,IAAI,CAAC/I,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAAC+E,WAAW,EAAE;UAC5D,IAAI,IAAI,CAACd,MAAM,CAACkB,YAAY,EAAE;YAC5B6H,MAAM,GAAG,OAAO;UAClB,CAAC,MAAM;YACLA,MAAM,GAAG,SAAS;UACpB;QACF,CAAC,MAAM,IAAI,IAAI,CAAC/I,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACqH,QAAQ,EAAE;UACzD,IAAI,CAACvD,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;UACtCmC,MAAM,GAAG,SAAS;QACpB;QAEA,IAAI7L,cAAc,CAAC2F,aAAa,EAAEhH,aAAa,CAAC,IAAI,IAAI,CAACmE,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,EAAE;UACvF,IAAI,IAAI,CAAC+C,MAAM,CAACkB,YAAY,IAAI,IAAI,CAAClB,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,QAAQ,EAAE;YAClH8L,MAAM,GAAG,OAAO;UAClB,CAAC,MAAM;YACLA,MAAM,GAAG,SAAS;UACpB;QAEF;QAEA,IAAI7L,cAAc,CAAC2F,aAAa,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,EAAE;UAC/D,IAAI,CAAC,EAAE,IAAI,CAAC7C,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACwH,SAAS,IAAI,IAAI,CAACtD,eAAe,CAAC3B,IAAI,KAAKZ,IAAI,CAACC,cAAc,CAAC,IACxG,IAAI,CAACwC,QAAQ,CAACyI,WAAW,KAAK,QAAQ,IACtC,IAAI,CAACzI,QAAQ,CAACyI,WAAW,KAAK,YAAY,IACzC,IAAI,CAACzI,QAAQ,CAACyI,WAAW,KAAK,MAAM,IAAI/F,aAAa,CAACmC,QAAS,KAClE,CAAC,IAAI,CAAChF,MAAM,CAACkB,YAAY,EAAE;YAC3B,IAAI,CAACmE,aAAa,CAAC,CAAC;UACtB,CAAC,MAAM;YACL,IAAI,CAACxF,OAAO,CAACP,IAAI,CAAC,IAAI,CAAC;YACvB,IAAID,IAAI,GAAG,IAAI,CAACQ,OAAO,CAAC4G,YAAY;YACpC;YACA;YACA,IAAIpH,IAAI,CAAC6H,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;cACvB,IAAI,CAAC7B,aAAa,CAAC,CAAC;YACtB;YACA,IAAI,CAACxF,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;UACxC;QACF,CAAC,MAAM,IAAImC,MAAM,KAAK,SAAS,EAAE;UAC/B,IAAI7L,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAEzD,aAAa,CAAC,EAAE;YACzD;YACA,IAAI,CAACyC,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;UACxC,CAAC,MAAM,IAAI,IAAI,CAAC5G,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,SAAS,IAAIC,cAAc,CAAC2F,aAAa,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE;YAC9G;YACA,IAAI,CAAChD,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;UACxC,CAAC,MAAM,IAAI,IAAI,CAAC5G,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACqH,QAAQ,EAAE;YACzD,IAAI,CAAC,IAAI,CAACpD,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACmH,UAAU,IAAI,CAAChG,cAAc,CAAC2F,aAAa,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC7C,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,EAAE;cACxJ;cACA,IAAIL,aAAa,CAACiG,aAAa,EAAE,IAAI,CAAC,IAAIjG,aAAa,CAACiG,aAAa,CAACmE,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACvF;gBACA,IAAI,CAACnH,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;cACxC,CAAC,MAAM;gBACL,IAAI,CAACvB,aAAa,CAAC,CAAC;cACtB;YACF;UACF,CAAC,MAAM,IAAInI,cAAc,CAAC2F,aAAa,EAAEhH,aAAa,CAAC,IAAI,IAAI,CAACmE,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,EAAE;YAC9F,IAAI,CAACoI,aAAa,CAAC,CAAC;UACtB;QACF,CAAC,MAAM,IAAI,IAAI,CAACrF,MAAM,CAAC3B,eAAe,IAAIU,QAAQ,CAAC,IAAI,CAACiB,MAAM,CAAC1B,IAAI,CAAC,IAAI,IAAI,CAAC0B,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC8C,eAAe,KAAK,GAAG,EAAE;UAC3I,IAAI,CAACsF,aAAa,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,MAAM,IAAI0D,MAAM,KAAK,OAAO,EAAE;UAC7B,IAAI,CAAClJ,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;QACxC;QACA,IAAI/D,aAAa,CAACmE,QAAQ,KAAKnE,aAAa,CAACmE,QAAQ,CAACjK,IAAI,KAAKhB,KAAK,CAAC0H,IAAI,IAAIZ,aAAa,CAACmE,QAAQ,CAACjK,IAAI,KAAKhB,KAAK,CAACiB,QAAQ,CAAC,EAAE;UAC5H,IAAI,CAAC6C,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;QACxC;QACA,IAAI,CAACC,WAAW,CAAChE,aAAa,CAAC;QAC/B,IAAI,CAAC7C,MAAM,CAACe,SAAS,GAAG8B,aAAa,CAAC5F,IAAI;QAE1C,IAAI4F,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACiB,QAAQ,EAAE;UACzC,IAAI6F,aAAa,CAAC5F,IAAI,KAAK,IAAI,EAAE;YAC/B,IAAI,CAAC+C,MAAM,CAACsB,QAAQ,GAAG,IAAI;UAC7B,CAAC,MAAM,IAAIuB,aAAa,CAAC5F,IAAI,KAAK,IAAI,EAAE;YACtC,IAAI,CAAC+C,MAAM,CAACmB,QAAQ,GAAG,IAAI;UAC7B,CAAC,MAAM,IAAI0B,aAAa,CAAC5F,IAAI,KAAK,QAAQ,EAAE;YAC1C,IAAI,CAAC+C,MAAM,CAACwB,YAAY,GAAG,IAAI;UACjC,CAAC,MAAM,IAAI,IAAI,CAACxB,MAAM,CAACwB,YAAY,IAAI5E,aAAa,CAACiG,aAAa,EAAE,MAAM,CAAC,EAAE;YAC3E,IAAI,CAAC7C,MAAM,CAACwB,YAAY,GAAG,KAAK;UAClC;QACF;MACF,CAAC;MAEDxG,UAAU,CAACoF,SAAS,CAACwD,gBAAgB,GAAG,UAASf,aAAa,EAAE;QAC9D,IAAI,IAAI,CAAC8E,kBAAkB,CAAC9E,aAAa,CAAC,EAAE;UAC1C;UACA;UACA,IAAI,CAAChD,OAAO,CAAC+G,kBAAkB,GAAG,KAAK;QACzC,CAAC,MAAM;UACL,IAAI,CAAC7B,8BAA8B,CAAClC,aAAa,CAAC;QACpD;QAEA,IAAIuD,UAAU,GAAG,IAAI,CAACtG,OAAO,CAACuG,IAAI,CAAC,CAAC;QACpC,OAAO,IAAI,CAACrG,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACE,SAAS,IACxC,EAAE,IAAI,CAACoC,MAAM,CAACmB,QAAQ,IAAIvE,aAAa,CAACwJ,UAAU,EAAE,MAAM,CAAC,CAAC,IAC5D,CAAC,IAAI,CAACpG,MAAM,CAACsB,QAAQ,EAAE;UACvB,IAAI,CAACgF,YAAY,CAAC,CAAC;QACrB;;QAEA;QACA,IAAI,IAAI,CAACtG,MAAM,CAACwB,YAAY,EAAE;UAC5B,IAAI,CAACxB,MAAM,CAACwB,YAAY,GAAG,KAAK;QAClC;QACA,IAAI,CAACqF,WAAW,CAAChE,aAAa,CAAC;MACjC,CAAC;MAED7H,UAAU,CAACoF,SAAS,CAAC0D,aAAa,GAAG,UAASjB,aAAa,EAAE;QAC3D,IAAIA,aAAa,CAAC5F,IAAI,CAAC+L,UAAU,CAAC,GAAG,CAAC,IAAInG,aAAa,CAACmC,QAAQ,KAAK,CAAC,IAAInC,aAAa,CAAC8D,iBAAiB,KAAK,EAAE,KAAK9D,aAAa,CAACmE,QAAQ,CAAC/J,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAAC0H,IAAI,CAAC,EAAE;UACvM;QAAA,CACD,MAAM,IAAI,IAAI,CAACkE,kBAAkB,CAAC9E,aAAa,CAAC,EAAE;UACjD;UACA;UACA,IAAI,CAAChD,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;QACxC,CAAC,MAAM;UACL,IAAI,CAAC7B,8BAA8B,CAAClC,aAAa,CAAC;UAClD,IAAI,IAAI,CAAC7C,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACiB,QAAQ,IAAI,IAAI,CAACgD,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAAC0H,IAAI,IAAI,IAAI,CAACzD,MAAM,CAACkB,YAAY,EAAE;YAC5H,IAAI,CAACrB,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;UACxC,CAAC,MAAM,IAAI,IAAI,CAAC5G,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACoI,KAAK,IAAI,IAAI,CAACnE,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACmH,UAAU,IAAI,IAAI,CAAClD,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACgI,MAAM,IAAI,IAAI,CAAC/D,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACkI,QAAQ,EAAE;YAC5M,IAAI,CAAC,IAAI,CAACyD,wBAAwB,CAAC,CAAC,EAAE;cACpC,IAAI,CAAChC,+BAA+B,CAAC7C,aAAa,CAAC;YACrD;UACF,CAAC,MAAM,IAAKA,aAAa,CAAC5F,IAAI,CAAC+L,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAChJ,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACqH,QAAQ,KAAKP,aAAa,CAACmE,QAAQ,CAAC/J,IAAI,KAAK,GAAG,IAAI4F,aAAa,CAACmE,QAAQ,CAAC/J,IAAI,KAAK,GAAG,CAAC,IAAI4F,aAAa,CAACmC,QAAQ,KAAK,CAAC,EAAG;YACjN,IAAI,CAACnF,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;UACxC,CAAC,MAAM;YACL,IAAI,CAACvB,aAAa,CAAC,CAAC;UACtB;QACF;QACA,IAAI,CAACwB,WAAW,CAAChE,aAAa,CAAC;MACjC,CAAC;MAED7H,UAAU,CAACoF,SAAS,CAAC4D,aAAa,GAAG,UAASnB,aAAa,EAAE;QAC3D,IAAI,IAAI,CAAC8E,kBAAkB,CAAC9E,aAAa,CAAC,EAAE;UAC1C;QAAA,CACD,MAAM;UACL,IAAI,CAACkC,8BAA8B,CAAClC,aAAa,CAAC;QACpD;QAEA,IAAI,IAAI,CAAC7C,MAAM,CAACgB,qBAAqB,EAAE;UACrC;UACA,IAAI,CAAChB,MAAM,CAACiB,sBAAsB,GAAG,IAAI;QAC3C;QACA,IAAI,CAACpB,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;QACtC,IAAI,CAACC,WAAW,CAAChE,aAAa,CAAC;QAC/B,IAAI,CAAChD,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;MACxC,CAAC;MAED5L,UAAU,CAACoF,SAAS,CAACgE,YAAY,GAAG,UAASvB,aAAa,EAAE;QAC1D,IAAI,CAACkC,8BAA8B,CAAClC,aAAa,EAAE,IAAI,CAAC;QAExD,IAAI,CAACgE,WAAW,CAAChE,aAAa,CAAC;QAC/B,IAAI,CAAChD,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;QACtC,IAAI,IAAI,CAAC5G,MAAM,CAACgB,qBAAqB,EAAE;UACrC,IAAIhC,aAAa,CAAC,IAAI,CAACgB,MAAM,CAACY,MAAM,CAACtC,IAAI,CAAC,EAAE;YAC1C;YACA,IAAI,CAAC0B,MAAM,CAACiB,sBAAsB,GAAG,KAAK;UAC5C;UAEA,IAAI,IAAI,CAACjB,MAAM,CAACiB,sBAAsB,EAAE;YACtC,IAAI,CAACjB,MAAM,CAACiB,sBAAsB,GAAG,KAAK;YAC1C,IAAI,CAACoE,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC;UACjC,CAAC,MAAM,IAAI,IAAI,CAAClF,QAAQ,CAAC4G,WAAW,EAAE;YACpC;YACA;YACA,IAAI,CAACrB,+BAA+B,CAAC7C,aAAa,CAAC;UACrD;QACF,CAAC,MAAM,IAAI,IAAI,CAAC7C,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACG,aAAa,IAC/C,IAAI,CAACmC,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACE,SAAS,IAAI,IAAI,CAACoC,MAAM,CAACY,MAAM,CAACtC,IAAI,KAAKZ,IAAI,CAACG,aAAc,EAAE;UACzF,IAAI,IAAI,CAACmC,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACE,SAAS,EAAE;YACvC,IAAI,CAAC0I,YAAY,CAAC,CAAC;UACrB;UAEA,IAAI,CAAC,IAAI,CAACtG,MAAM,CAACkB,YAAY,EAAE;YAC7B,IAAI,CAACmE,aAAa,CAAC,CAAC;UACtB;QACF,CAAC,MAAM,IAAI,IAAI,CAAClF,QAAQ,CAAC4G,WAAW,EAAE;UACpC;UACA;UACA;UACA,IAAI,CAACrB,+BAA+B,CAAC7C,aAAa,CAAC;QACrD;MACF,CAAC;MAED7H,UAAU,CAACoF,SAAS,CAAC8D,eAAe,GAAG,UAASrB,aAAa,EAAE;QAC7D,IAAIoG,mBAAmB,GAAGpG,aAAa,CAAC5F,IAAI,KAAK,GAAG,KACjDC,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,IAC3D7E,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACa,UAAU,CAAC9D,IAAI,EAAE,CAAChB,KAAK,CAAC+E,WAAW,EAAE/E,KAAK,CAACoI,KAAK,EAAEpI,KAAK,CAACwH,SAAS,EAAExH,KAAK,CAAC4H,SAAS,CAAC,CAAE,CAC5G;QACH,IAAIuF,OAAO,GAAGlN,QAAQ,CAAC6G,aAAa,CAAC5F,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,KACpDjB,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACa,UAAU,CAAC9D,IAAI,EAAE,CAAChB,KAAK,CAAC+E,WAAW,EAAE/E,KAAK,CAACmH,UAAU,EAAEnH,KAAK,CAACgI,MAAM,EAAEhI,KAAK,CAACkI,QAAQ,CAAC,CAAC,IAC1GjI,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACa,UAAU,CAAC5D,IAAI,EAAEpB,aAAa,CAAC,IACpD,IAAI,CAACmE,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,CACpC;QAED,IAAI,IAAI,CAAC0K,kBAAkB,CAAC9E,aAAa,CAAC,EAAE;UAC1C;QAAA,CACD,MAAM;UACL,IAAII,wBAAwB,GAAG,CAACgG,mBAAmB;UACnD,IAAI,CAAClE,8BAA8B,CAAClC,aAAa,EAAEI,wBAAwB,CAAC;QAC9E;;QAEA;QACA,IAAIJ,aAAa,CAAC5F,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAAC0I,GAAG,EAAE;UAC3E,IAAI,CAACoC,WAAW,CAAChE,aAAa,CAAC;UAC/B;QACF;QAEA,IAAIA,aAAa,CAAC5F,IAAI,KAAK,IAAI,EAAE;UAC/B;UACA,IAAI,CAAC4J,WAAW,CAAChE,aAAa,CAAC;UAC/B;QACF;QAEA,IAAI7G,QAAQ,CAAC6G,aAAa,CAAC5F,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,IAAI,CAACyK,wBAAwB,CAAC,CAAC,EAAE;UAC/E;UACA,IAAI,CAACb,WAAW,CAAChE,aAAa,CAAC;UAC/B;QACF;;QAEA;QACA;QACA,IAAI,IAAI,CAAC7C,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACkI,QAAQ,IAAIjI,QAAQ,CAAC,IAAI,CAACmE,QAAQ,CAAC6F,iBAAiB,EAAEzI,oCAAoC,CAAC,EAAE;UACrI,IAAI,CAACmI,+BAA+B,CAAC7C,aAAa,CAAC;QACrD;QAEA,IAAIA,aAAa,CAAC5F,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC+C,MAAM,CAAC0B,OAAO,EAAE;UACrD,IAAI,CAACmF,WAAW,CAAChE,aAAa,CAAC;UAE/B,IAAI,CAAC7C,MAAM,CAAC0B,OAAO,GAAG,KAAK;UAC3B,IAAI,CAAC1B,MAAM,CAAC2B,SAAS,GAAG,IAAI;UAC5B,IAAI,IAAI,CAAC7B,OAAO,CAACuG,IAAI,CAAC,CAAC,CAACtJ,IAAI,KAAKhB,KAAK,CAAC+E,WAAW,EAAE;YAClD,IAAI,CAACrB,MAAM,CAAC,CAAC;YACb,IAAI,CAAC4F,aAAa,CAAC,CAAC;YACpB,IAAI,CAACrF,MAAM,CAAC4B,UAAU,GAAG,KAAK;UAChC,CAAC,MAAM;YACL,IAAI,CAAC5B,MAAM,CAAC4B,UAAU,GAAG,IAAI;YAC7B,IAAI,CAAC/B,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;UACxC;UACA;QACF;QAEA,IAAIuC,YAAY,GAAG,IAAI;QACvB,IAAIC,WAAW,GAAG,IAAI;QACtB,IAAIC,UAAU,GAAG,KAAK;QACtB,IAAIxG,aAAa,CAAC5F,IAAI,KAAK,GAAG,EAAE;UAC9B,IAAI,IAAI,CAAC+C,MAAM,CAAC+B,aAAa,KAAK,CAAC,EAAE;YACnC;YACAoH,YAAY,GAAG,KAAK;UACtB,CAAC,MAAM;YACL,IAAI,CAACnJ,MAAM,CAAC+B,aAAa,IAAI,CAAC;YAC9BsH,UAAU,GAAG,IAAI;UACnB;QACF,CAAC,MAAM,IAAIxG,aAAa,CAAC5F,IAAI,KAAK,GAAG,EAAE;UACrC,IAAI,CAAC+C,MAAM,CAAC+B,aAAa,IAAI,CAAC;QAChC;;QAEA;QACA,IAAI,CAACmH,OAAO,IAAI,CAACD,mBAAmB,IAAI,IAAI,CAAC9I,QAAQ,CAACoF,iBAAiB,IAAIvJ,QAAQ,CAAC6G,aAAa,CAAC5F,IAAI,EAAEnB,sBAAsB,CAAC,EAAE;UAC/H,IAAIwN,OAAO,GAAGzG,aAAa,CAAC5F,IAAI,KAAK,GAAG;UACxC,IAAIsM,cAAc,GAAID,OAAO,IAAID,UAAW;UAC5C,IAAIG,YAAY,GAAIF,OAAO,IAAI,CAACD,UAAW;UAE3C,QAAQ,IAAI,CAAClJ,QAAQ,CAAC6F,iBAAiB;YACrC,KAAK1I,iBAAiB,CAACE,cAAc;cACnC;cACA,IAAI,CAACqC,OAAO,CAAC+G,kBAAkB,GAAG,CAAC4C,YAAY;cAE/C,IAAI,CAAC3C,WAAW,CAAChE,aAAa,CAAC;cAE/B,IAAI,CAACyG,OAAO,IAAIC,cAAc,EAAE;gBAC9B,IAAI,CAAC7D,+BAA+B,CAAC7C,aAAa,CAAC;cACrD;cAEA,IAAI,CAAChD,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;cACtC;YAEF,KAAKtJ,iBAAiB,CAACmM,aAAa;cAClC;cACA;;cAEA,IAAI,CAAC5J,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;cAEtC,IAAI,CAAC0C,OAAO,IAAIC,cAAc,EAAE;gBAC9B,IAAI,IAAI,CAACzJ,OAAO,CAACuG,IAAI,CAAC,CAAC,CAACrB,QAAQ,EAAE;kBAChC,IAAI,CAACK,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC;gBACjC,CAAC,MAAM;kBACL,IAAI,CAACK,+BAA+B,CAAC7C,aAAa,CAAC;gBACrD;cACF,CAAC,MAAM;gBACL,IAAI,CAAChD,OAAO,CAAC+G,kBAAkB,GAAG,KAAK;cACzC;cAEA,IAAI,CAACC,WAAW,CAAChE,aAAa,CAAC;cAE/B,IAAI,CAAChD,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;cACtC;YAEF,KAAKtJ,iBAAiB,CAACG,gBAAgB;cACrC,IAAI,CAAC+L,YAAY,EAAE;gBACjB,IAAI,CAAC9D,+BAA+B,CAAC7C,aAAa,CAAC;cACrD;;cAEA;cACA;cACAsG,YAAY,GAAG,EAAE,IAAI,CAACtJ,OAAO,CAACY,kBAAkB,CAAC,CAAC,IAAI+I,YAAY,CAAC;cAEnE,IAAI,CAAC3J,OAAO,CAAC+G,kBAAkB,GAAGuC,YAAY;cAC9C,IAAI,CAACtC,WAAW,CAAChE,aAAa,CAAC;cAC/B,IAAI,CAAChD,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;cACtC;UACJ;QACF;QAEA,IAAIqC,mBAAmB,EAAE;UACvB,IAAI,CAACvD,+BAA+B,CAAC7C,aAAa,CAAC;UACnDsG,YAAY,GAAG,KAAK;UACpB,IAAI/C,UAAU,GAAG,IAAI,CAACtG,OAAO,CAACuG,IAAI,CAAC,CAAC;UACpC+C,WAAW,GAAGhD,UAAU,IAAIpK,QAAQ,CAACoK,UAAU,CAACrJ,IAAI,EAAE,CAAChB,KAAK,CAAC0H,IAAI,EAAE1H,KAAK,CAACiB,QAAQ,CAAC,CAAC;QACrF,CAAC,MAAM,IAAI6F,aAAa,CAAC5F,IAAI,KAAK,KAAK,EAAE;UACvC,IAAI,CAACyI,+BAA+B,CAAC7C,aAAa,CAAC;UACnDsG,YAAY,GAAG,IAAI,CAACnJ,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAAC+E,WAAW;UAChEsI,WAAW,GAAG,KAAK;QACrB,CAAC,MAAM,IAAIpN,QAAQ,CAAC6G,aAAa,CAAC5F,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,IAAIiM,OAAO,EAAE;UAC1E;UACA,IAAI,IAAI,CAAClJ,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACoI,KAAK,IAAI,IAAI,CAACnE,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACmH,UAAU,EAAE;YACnG,IAAI,CAACwC,+BAA+B,CAAC7C,aAAa,CAAC;UACrD;UAEAsG,YAAY,GAAG,KAAK;UACpBC,WAAW,GAAG,KAAK;;UAEnB;UACA;UACA,IAAIvG,aAAa,CAACmC,QAAQ,KAAKnC,aAAa,CAAC5F,IAAI,KAAK,IAAI,IAAI4F,aAAa,CAAC5F,IAAI,KAAK,IAAI,IAAI4F,aAAa,CAAC5F,IAAI,KAAK,GAAG,CAAC,EAAE;YACxH,IAAIyM,eAAe,GAAGxM,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAEzD,aAAa,CAAC,IAAIyF,aAAa,CAACmC,QAAQ;YACrG,IAAI0E,eAAe,KAAK,IAAI,CAACzJ,eAAe,CAACkB,QAAQ,IAAI,IAAI,CAAClB,eAAe,CAACmB,UAAU,CAAC,EAAE;cACzF,IAAI,CAACkF,YAAY,CAAC,CAAC;YACrB;YACA,IAAI,CAACjB,aAAa,CAACqE,eAAe,EAAE,IAAI,CAAC;UAC3C;UAEA,IAAI,IAAI,CAAC1J,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,IAAI+B,aAAa,CAAC,IAAI,CAACgB,MAAM,CAAC1B,IAAI,CAAC,EAAE;YAC1E;YACA;YACA6K,YAAY,GAAG,IAAI;UACrB;UAEA,IAAI,IAAI,CAACnJ,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACiB,QAAQ,EAAE;YAClDmM,YAAY,GAAG,IAAI;UACrB,CAAC,MAAM,IAAI,IAAI,CAACnJ,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACqH,QAAQ,EAAE;YACzD+F,YAAY,GAAG,EAAE,IAAI,CAACnJ,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,KAAK4F,aAAa,CAAC5F,IAAI,KAAK,IAAI,IAAI4F,aAAa,CAAC5F,IAAI,KAAK,IAAI,CAAC,CAAC;UACvH,CAAC,MAAM,IAAI,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC9D,IAAI,KAAKhB,KAAK,CAACkI,QAAQ,EAAE;YACzD;YACA;YACAkF,YAAY,GAAGnN,QAAQ,CAAC6G,aAAa,CAAC5F,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,IAAIjB,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACa,UAAU,CAAC5D,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;YACpI;YACA;YACA;YACA;YACA,IAAIjB,QAAQ,CAAC6G,aAAa,CAAC5F,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,IAAIjB,QAAQ,CAAC,IAAI,CAACgE,MAAM,CAACa,UAAU,CAAC5D,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE;cACnGmM,WAAW,GAAG,IAAI;YACpB;UACF;UAGA,IAAI,CAAE,IAAI,CAACpJ,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACC,cAAc,IAAI,CAAC,IAAI,CAACqC,MAAM,CAACkB,YAAY,IAAK,IAAI,CAAClB,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACE,SAAS,MAChH,IAAI,CAACoC,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC+C,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,CAAC,EAAE;YAC9E;YACA;YACA,IAAI,CAACoI,aAAa,CAAC,CAAC;UACtB;QACF;QAEA,IAAI,CAACxF,OAAO,CAAC+G,kBAAkB,GAAG,IAAI,CAAC/G,OAAO,CAAC+G,kBAAkB,IAAIuC,YAAY;QACjF,IAAI,CAACtC,WAAW,CAAChE,aAAa,CAAC;QAC/B,IAAI,CAAChD,OAAO,CAAC+G,kBAAkB,GAAGwC,WAAW;MAC/C,CAAC;MAEDpO,UAAU,CAACoF,SAAS,CAACkE,oBAAoB,GAAG,UAASzB,aAAa,EAAEI,wBAAwB,EAAE;QAC5F,IAAI,IAAI,CAACpD,OAAO,CAACsC,GAAG,EAAE;UACpB,IAAI,CAACtC,OAAO,CAACiH,aAAa,CAACjE,aAAa,CAAC;UACzC,IAAIA,aAAa,CAAC8G,UAAU,IAAI9G,aAAa,CAAC8G,UAAU,CAACC,QAAQ,KAAK,KAAK,EAAE;YAC3E;YACA,IAAI,CAAC/J,OAAO,CAACsC,GAAG,GAAG,IAAI,CAAChC,QAAQ,CAACiC,eAAe;UAClD;UACA;QACF;QAEA,IAAIS,aAAa,CAAC8G,UAAU,EAAE;UAC5B,IAAI,CAACtE,aAAa,CAAC,KAAK,EAAEpC,wBAAwB,CAAC;UACnD,IAAI,CAAC4D,WAAW,CAAChE,aAAa,CAAC;UAC/B,IAAIA,aAAa,CAAC8G,UAAU,CAACC,QAAQ,KAAK,OAAO,EAAE;YACjD,IAAI,CAAC/J,OAAO,CAACsC,GAAG,GAAG,IAAI;UACzB;UACA,IAAI,CAACkD,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC;UAC/B;QACF;;QAEA;QACA,IAAI,CAAC1J,KAAK,CAACkO,OAAO,CAACjH,IAAI,CAACC,aAAa,CAAC5F,IAAI,CAAC,IAAI,CAAC4F,aAAa,CAACmC,QAAQ,EAAE;UACtE,IAAI,CAACnF,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;UACtC,IAAI,CAACC,WAAW,CAAChE,aAAa,CAAC;UAC/B,IAAI,CAAChD,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;UACtC;QACF,CAAC,MAAM;UACL,IAAI,CAACkD,oBAAoB,CAACjH,aAAa,EAAEI,wBAAwB,CAAC;QACpE;MACF,CAAC;MAEDjI,UAAU,CAACoF,SAAS,CAAC0J,oBAAoB,GAAG,UAASjH,aAAa,EAAEI,wBAAwB,EAAE;QAC5F,IAAI/D,KAAK,GAAGT,gBAAgB,CAACoE,aAAa,CAAC5F,IAAI,CAAC;QAChD,IAAIuI,CAAC,CAAC,CAAC;QACP,IAAIuE,OAAO,GAAG,KAAK;QACnB,IAAIC,QAAQ,GAAG,KAAK;QACpB,IAAIC,UAAU,GAAGpH,aAAa,CAAC8D,iBAAiB;QAChD,IAAIuD,gBAAgB,GAAGD,UAAU,CAACtN,MAAM;;QAExC;QACA,IAAI,CAAC0I,aAAa,CAAC,KAAK,EAAEpC,wBAAwB,CAAC;;QAEnD;QACA,IAAI,CAACuD,4BAA4B,CAAC3D,aAAa,CAAC;QAChD,IAAI,CAAChD,OAAO,CAACyH,SAAS,CAACpI,KAAK,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAACmG,aAAa,CAAC,KAAK,EAAEpC,wBAAwB,CAAC;QAGnD,IAAI/D,KAAK,CAACvC,MAAM,GAAG,CAAC,EAAE;UACpBuC,KAAK,GAAGA,KAAK,CAACiL,KAAK,CAAC,CAAC,CAAC;UACtBJ,OAAO,GAAG9K,oBAAoB,CAACC,KAAK,EAAE,GAAG,CAAC;UAC1C8K,QAAQ,GAAGxK,wBAAwB,CAACN,KAAK,EAAE+K,UAAU,CAAC;UAEtD,IAAIF,OAAO,EAAE;YACX,IAAI,CAAC/J,MAAM,CAAC6B,SAAS,GAAG,CAAC;UAC3B;UAEA,KAAK2D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtG,KAAK,CAACvC,MAAM,EAAE6I,CAAC,EAAE,EAAE;YACjC,IAAIuE,OAAO,EAAE;cACX;cACA,IAAI,CAACvD,4BAA4B,CAAC3D,aAAa,CAAC;cAChD,IAAI,CAAChD,OAAO,CAACyH,SAAS,CAAClL,KAAK,CAAC8C,KAAK,CAACsG,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,MAAM,IAAIwE,QAAQ,IAAI9K,KAAK,CAACsG,CAAC,CAAC,EAAE;cAC/B;cACA,IAAI,CAACgB,4BAA4B,CAAC3D,aAAa,CAAC;cAChD,IAAI,CAAChD,OAAO,CAACyH,SAAS,CAACpI,KAAK,CAACsG,CAAC,CAAC,CAAC1G,SAAS,CAACoL,gBAAgB,CAAC,CAAC;YAC9D,CAAC,MAAM;cACL;cACA,IAAI,CAACrK,OAAO,CAAC4G,YAAY,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;cACxC,IAAI,CAAC7G,OAAO,CAACyH,SAAS,CAACpI,KAAK,CAACsG,CAAC,CAAC,CAAC;YAClC;;YAEA;YACA,IAAI,CAACH,aAAa,CAAC,KAAK,EAAEpC,wBAAwB,CAAC;UACrD;UAEA,IAAI,CAACjD,MAAM,CAAC6B,SAAS,GAAG,CAAC;QAC3B;MACF,CAAC;MAGD7G,UAAU,CAACoF,SAAS,CAACoE,cAAc,GAAG,UAAS3B,aAAa,EAAEI,wBAAwB,EAAE;QACtF,IAAIJ,aAAa,CAACmC,QAAQ,EAAE;UAC1B,IAAI,CAACK,aAAa,CAAC,KAAK,EAAEpC,wBAAwB,CAAC;QACrD,CAAC,MAAM;UACL,IAAI,CAACpD,OAAO,CAACP,IAAI,CAAC,IAAI,CAAC;QACzB;QAEA,IAAI,CAACO,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;QACtC,IAAI,CAACC,WAAW,CAAChE,aAAa,CAAC;QAC/B,IAAI,CAACwC,aAAa,CAAC,KAAK,EAAEpC,wBAAwB,CAAC;MACrD,CAAC;MAEDjI,UAAU,CAACoF,SAAS,CAACsE,UAAU,GAAG,UAAS7B,aAAa,EAAE;QACxD,IAAI,IAAI,CAAC8E,kBAAkB,CAAC9E,aAAa,CAAC,EAAE;UAC1C;QAAA,CACD,MAAM;UACL,IAAI,CAACkC,8BAA8B,CAAClC,aAAa,EAAE,IAAI,CAAC;QAC1D;QAEA,IAAI,IAAI,CAAC7C,MAAM,CAACa,UAAU,CAAC5D,IAAI,CAACiF,KAAK,CAAC,UAAU,CAAC,EAAE;UACjD,IAAI,CAACrC,OAAO,CAAC+G,kBAAkB,GAAG,IAAI;QACxC;QAEA,IAAI1J,cAAc,CAAC,IAAI,CAAC8C,MAAM,CAACa,UAAU,EAAEzD,aAAa,CAAC,EAAE;UACzD,IAAI,CAACyC,OAAO,CAAC+G,kBAAkB,GAAG,KAAK;QACzC,CAAC,MAAM;UACL;UACA;UACA,IAAI,CAAClB,+BAA+B,CAAC7C,aAAa,EAChD,IAAI,CAAC7C,MAAM,CAACa,UAAU,CAAC5D,IAAI,KAAK,GAAG,IAAI,IAAI,CAACkD,QAAQ,CAACiK,qBAAqB,CAAC;QAC/E;;QAEA;QACA;QACA,IAAI,IAAI,CAACjK,QAAQ,CAACkK,wBAAwB,IAAI,IAAI,CAACxK,OAAO,CAACY,kBAAkB,CAAC,CAAC,EAAE;UAC/E,IAAI,CAACgH,QAAQ,CAAC,CAAC;QACjB;QAEA,IAAI,CAACZ,WAAW,CAAChE,aAAa,CAAC;MACjC,CAAC;MAED7H,UAAU,CAACoF,SAAS,CAAC0E,cAAc,GAAG,UAASjC,aAAa,EAAEI,wBAAwB,EAAE;QACtF,IAAI,CAAC4D,WAAW,CAAChE,aAAa,CAAC;QAE/B,IAAIA,aAAa,CAAC5F,IAAI,CAAC4F,aAAa,CAAC5F,IAAI,CAACN,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;UAC9D,IAAI,CAAC0I,aAAa,CAAC,KAAK,EAAEpC,wBAAwB,CAAC;QACrD;MACF,CAAC;MAEDjI,UAAU,CAACoF,SAAS,CAACwE,UAAU,GAAG,UAAS/B,aAAa,EAAE;QACxD;QACA,OAAO,IAAI,CAAC7C,MAAM,CAAC1B,IAAI,KAAKZ,IAAI,CAACE,SAAS,EAAE;UAC1C,IAAI,CAAC0I,YAAY,CAAC,CAAC;QACrB;QACA,IAAI,CAACvB,8BAA8B,CAAClC,aAAa,CAAC;MACpD,CAAC;MAEDhI,MAAM,CAACU,OAAO,CAACP,UAAU,GAAGA,UAAU;;MAGtC;IAAM,CAAC,IACP;IACA,KAAO,UAASH,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,SAASyP,UAAUA,CAAC1J,MAAM,EAAE;QAC1B,IAAI,CAAC2J,QAAQ,GAAG3J,MAAM;QACtB,IAAI,CAAC4J,iBAAiB,GAAG,CAAC;QAC1B;QACA,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAACC,iBAAiB,GAAG,CAAC;QAC1B,IAAI,CAACC,kBAAkB,GAAG,CAAC;QAC3B,IAAI,CAACC,4BAA4B,GAAG,CAAC;QACrC,IAAI,CAACC,yBAAyB,GAAG,CAAC,CAAC;QACnC,IAAI,CAACC,4BAA4B,GAAG,CAAC;QAErC,IAAI,CAACC,OAAO,GAAG,EAAE;MACnB;MAEAT,UAAU,CAAClK,SAAS,CAAC4K,WAAW,GAAG,YAAW;QAC5C,IAAI3L,IAAI,GAAG,IAAIiL,UAAU,CAAC,IAAI,CAACC,QAAQ,CAAC;QACxClL,IAAI,CAACqH,UAAU,CAAC,IAAI,CAAC+D,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC;QAC5D,OAAOrL,IAAI;MACb,CAAC;MAEDiL,UAAU,CAAClK,SAAS,CAAC6K,IAAI,GAAG,UAASxC,KAAK,EAAE;QAC1C,IAAIA,KAAK,GAAG,CAAC,EAAE;UACb,OAAO,IAAI,CAACsC,OAAO,CAAC,IAAI,CAACA,OAAO,CAACpO,MAAM,GAAG8L,KAAK,CAAC;QAClD,CAAC,MAAM;UACL,OAAO,IAAI,CAACsC,OAAO,CAACtC,KAAK,CAAC;QAC5B;MACF,CAAC;MAED6B,UAAU,CAAClK,SAAS,CAAC8K,SAAS,GAAG,UAASC,OAAO,EAAE;QACjD,KAAK,IAAIC,iBAAiB,GAAG,IAAI,CAACL,OAAO,CAACpO,MAAM,GAAG,CAAC,EAAEyO,iBAAiB,IAAI,CAAC,EAAEA,iBAAiB,EAAE,EAAE;UACjG,IAAI,IAAI,CAACL,OAAO,CAACK,iBAAiB,CAAC,CAAClJ,KAAK,CAACiJ,OAAO,CAAC,EAAE;YAClD,OAAO,IAAI;UACb;QACF;QACA,OAAO,KAAK;MACd,CAAC;MAEDb,UAAU,CAAClK,SAAS,CAACsG,UAAU,GAAG,UAASjH,MAAM,EAAEoC,SAAS,EAAE;QAC5D,IAAI,IAAI,CAACwF,QAAQ,CAAC,CAAC,EAAE;UACnB,IAAI,CAACoD,cAAc,GAAGhL,MAAM,IAAI,CAAC;UACjC,IAAI,CAACiL,iBAAiB,GAAG7I,SAAS,IAAI,CAAC;UACvC,IAAI,CAAC2I,iBAAiB,GAAG,IAAI,CAACD,QAAQ,CAACc,eAAe,CAAC,IAAI,CAACZ,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC;QACrG;MACF,CAAC;MAEDJ,UAAU,CAAClK,SAAS,CAACkL,eAAe,GAAG,YAAW;QAChD,IAAI,IAAI,CAACf,QAAQ,CAACtE,gBAAgB,EAAE;UAClC,IAAI,CAAC0E,kBAAkB,GAAG,IAAI,CAACI,OAAO,CAACpO,MAAM;UAC7C,IAAI,CAACiO,4BAA4B,GAAG,IAAI,CAACJ,iBAAiB;UAC1D,IAAI,CAACK,yBAAyB,GAAG,IAAI,CAACN,QAAQ,CAACgB,SAAS,CAACd,cAAc;UACvE,IAAI,CAACK,4BAA4B,GAAG,IAAI,CAACP,QAAQ,CAACgB,SAAS,CAACb,iBAAiB;QAC/E;MACF,CAAC;MAEDJ,UAAU,CAAClK,SAAS,CAACoL,YAAY,GAAG,YAAW;QAC7C,OAAO,IAAI,CAACb,kBAAkB,IAC5B,IAAI,CAACH,iBAAiB,GAAG,IAAI,CAACD,QAAQ,CAACtE,gBAAgB,IACvD,IAAI,CAAC2E,4BAA4B,GAAG,IAAI,CAACL,QAAQ,CAACgB,SAAS,CAACf,iBAAiB;MACjF,CAAC;MAEDF,UAAU,CAAClK,SAAS,CAACqL,WAAW,GAAG,YAAW;QAC5C,IAAI,IAAI,CAACD,YAAY,CAAC,CAAC,EAAE;UACvB,IAAI,CAACjB,QAAQ,CAAChE,YAAY,CAAC,CAAC;UAC5B,IAAIzD,IAAI,GAAG,IAAI,CAACyH,QAAQ,CAAC9D,YAAY;UACrC3D,IAAI,CAAC4D,UAAU,CAAC,IAAI,CAACmE,yBAAyB,EAAE,IAAI,CAACC,4BAA4B,CAAC;UAClFhI,IAAI,CAACiI,OAAO,GAAG,IAAI,CAACA,OAAO,CAACZ,KAAK,CAAC,IAAI,CAACQ,kBAAkB,CAAC;UAC1D,IAAI,CAACI,OAAO,GAAG,IAAI,CAACA,OAAO,CAACZ,KAAK,CAAC,CAAC,EAAE,IAAI,CAACQ,kBAAkB,CAAC;UAE7D7H,IAAI,CAAC0H,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACI,4BAA4B;UACpF,IAAI,CAACJ,iBAAiB,GAAG,IAAI,CAACI,4BAA4B;UAE1D,IAAI9H,IAAI,CAACiI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAC3BjI,IAAI,CAACiI,OAAO,CAACW,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;YACzB5I,IAAI,CAAC0H,iBAAiB,IAAI,CAAC;UAC7B;UACA,OAAO,IAAI;QACb;QACA,OAAO,KAAK;MACd,CAAC;MAEDF,UAAU,CAAClK,SAAS,CAACiH,QAAQ,GAAG,YAAW;QACzC,OAAO,IAAI,CAAC0D,OAAO,CAACpO,MAAM,KAAK,CAAC;MAClC,CAAC;MAED2N,UAAU,CAAClK,SAAS,CAAC8G,IAAI,GAAG,YAAW;QACrC,IAAI,CAAC,IAAI,CAACG,QAAQ,CAAC,CAAC,EAAE;UACpB,OAAO,IAAI,CAAC0D,OAAO,CAAC,IAAI,CAACA,OAAO,CAACpO,MAAM,GAAG,CAAC,CAAC;QAC9C,CAAC,MAAM;UACL,OAAO,IAAI;QACb;MACF,CAAC;MAED2N,UAAU,CAAClK,SAAS,CAACvB,IAAI,GAAG,UAASoM,IAAI,EAAE;QACzC,IAAI,CAACF,OAAO,CAAClM,IAAI,CAACoM,IAAI,CAAC;QACvB,IAAIU,kBAAkB,GAAGV,IAAI,CAACW,WAAW,CAAC,IAAI,CAAC;QAC/C,IAAID,kBAAkB,KAAK,CAAC,CAAC,EAAE;UAC7B,IAAI,CAACnB,iBAAiB,GAAGS,IAAI,CAACtO,MAAM,GAAGgP,kBAAkB;QAC3D,CAAC,MAAM;UACL,IAAI,CAACnB,iBAAiB,IAAIS,IAAI,CAACtO,MAAM;QACvC;MACF,CAAC;MAED2N,UAAU,CAAClK,SAAS,CAACgH,GAAG,GAAG,YAAW;QACpC,IAAI6D,IAAI,GAAG,IAAI;QACf,IAAI,CAAC,IAAI,CAAC5D,QAAQ,CAAC,CAAC,EAAE;UACpB4D,IAAI,GAAG,IAAI,CAACF,OAAO,CAAC3D,GAAG,CAAC,CAAC;UACzB,IAAI,CAACoD,iBAAiB,IAAIS,IAAI,CAACtO,MAAM;QACvC;QACA,OAAOsO,IAAI;MACb,CAAC;MAGDX,UAAU,CAAClK,SAAS,CAACyL,cAAc,GAAG,YAAW;QAC/C,IAAI,IAAI,CAACpB,cAAc,GAAG,CAAC,EAAE;UAC3B,IAAI,CAACA,cAAc,IAAI,CAAC;UACxB,IAAI,CAACD,iBAAiB,IAAI,IAAI,CAACD,QAAQ,CAACuB,WAAW;QACrD;MACF,CAAC;MAEDxB,UAAU,CAAClK,SAAS,CAAC2L,mBAAmB,GAAG,YAAW;QACpD,IAAI,IAAI,CAAClB,yBAAyB,GAAG,CAAC,EAAE;UACtC,IAAI,CAACA,yBAAyB,IAAI,CAAC;QACrC;MACF,CAAC;MACDP,UAAU,CAAClK,SAAS,CAACd,IAAI,GAAG,YAAW;QACrC,OAAO,IAAI,CAAC4H,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;UAC1B,IAAI,CAAC6D,OAAO,CAAC3D,GAAG,CAAC,CAAC;UAClB,IAAI,CAACoD,iBAAiB,IAAI,CAAC;QAC7B;MACF,CAAC;MAEDF,UAAU,CAAClK,SAAS,CAAC4L,QAAQ,GAAG,YAAW;QACzC,IAAIvP,MAAM,GAAG,EAAE;QACf,IAAI,IAAI,CAAC4K,QAAQ,CAAC,CAAC,EAAE;UACnB,IAAI,IAAI,CAACkD,QAAQ,CAAC0B,kBAAkB,EAAE;YACpCxP,MAAM,GAAG,IAAI,CAAC8N,QAAQ,CAAC2B,iBAAiB,CAAC,IAAI,CAACzB,cAAc,CAAC;UAC/D;QACF,CAAC,MAAM;UACLhO,MAAM,GAAG,IAAI,CAAC8N,QAAQ,CAAC2B,iBAAiB,CAAC,IAAI,CAACzB,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC;UACrFjO,MAAM,IAAI,IAAI,CAACsO,OAAO,CAACoB,IAAI,CAAC,EAAE,CAAC;QACjC;QACA,OAAO1P,MAAM;MACf,CAAC;MAED,SAAS2P,iBAAiBA,CAAChR,OAAO,EAAE6G,gBAAgB,EAAE;QACpD,IAAI,CAACoK,OAAO,GAAG,CAAC,EAAE,CAAC;QACnB,IAAI,CAACC,aAAa,GAAGlR,OAAO,CAAC0Q,WAAW;QACxC,IAAI,CAACS,eAAe,GAAGnR,OAAO,CAACoR,WAAW;QAC1C,IAAI,CAACpR,OAAO,CAACqR,gBAAgB,EAAE;UAC7B,IAAI,CAACF,eAAe,GAAG,IAAIG,KAAK,CAACtR,OAAO,CAAC0Q,WAAW,GAAG,CAAC,CAAC,CAACK,IAAI,CAAC/Q,OAAO,CAACoR,WAAW,CAAC;QACrF;;QAEA;QACAvK,gBAAgB,GAAGA,gBAAgB,IAAI,EAAE;QACzC,IAAI7G,OAAO,CAACuR,YAAY,GAAG,CAAC,EAAE;UAC5B1K,gBAAgB,GAAG,IAAIyK,KAAK,CAACtR,OAAO,CAACuR,YAAY,GAAG,CAAC,CAAC,CAACR,IAAI,CAAC,IAAI,CAACI,eAAe,CAAC;QACnF;QAEA,IAAI,CAACK,aAAa,GAAG3K,gBAAgB;QACrC,IAAI,CAAC4K,oBAAoB,GAAG5K,gBAAgB,CAACtF,MAAM;MACrD;MAEAyP,iBAAiB,CAAChM,SAAS,CAACiL,eAAe,GAAG,UAAS5L,MAAM,EAAEqN,MAAM,EAAE;QACrE,IAAIrQ,MAAM,GAAG,IAAI,CAACoQ,oBAAoB;QACtCC,MAAM,GAAGA,MAAM,IAAI,CAAC;QACpB,IAAIrN,MAAM,GAAG,CAAC,EAAE;UACdhD,MAAM,GAAG,CAAC;QACZ;QACAA,MAAM,IAAIgD,MAAM,GAAG,IAAI,CAAC6M,aAAa;QACrC7P,MAAM,IAAIqQ,MAAM;QAChB,OAAOrQ,MAAM;MACf,CAAC;MAED2P,iBAAiB,CAAChM,SAAS,CAAC8L,iBAAiB,GAAG,UAASS,YAAY,EAAEG,MAAM,EAAE;QAC7E,IAAIrQ,MAAM,GAAG,IAAI,CAACmQ,aAAa;QAC/BE,MAAM,GAAGA,MAAM,IAAI,CAAC;QACpB,IAAIH,YAAY,GAAG,CAAC,EAAE;UACpBA,YAAY,GAAG,CAAC;UAChBlQ,MAAM,GAAG,EAAE;QACb;QACAqQ,MAAM,IAAIH,YAAY,GAAG,IAAI,CAACL,aAAa;QAC3C,IAAI,CAACS,cAAc,CAACD,MAAM,CAAC;QAC3BrQ,MAAM,IAAI,IAAI,CAAC4P,OAAO,CAACS,MAAM,CAAC;QAC9B,OAAOrQ,MAAM;MACf,CAAC;MAED2P,iBAAiB,CAAChM,SAAS,CAAC2M,cAAc,GAAG,UAASD,MAAM,EAAE;QAC5D,OAAOA,MAAM,IAAI,IAAI,CAACT,OAAO,CAAC1P,MAAM,EAAE;UACpC,IAAI,CAACqQ,YAAY,CAAC,CAAC;QACrB;MACF,CAAC;MAEDZ,iBAAiB,CAAChM,SAAS,CAAC4M,YAAY,GAAG,YAAW;QACpD,IAAIF,MAAM,GAAG,IAAI,CAACT,OAAO,CAAC1P,MAAM;QAChC,IAAI8C,MAAM,GAAG,CAAC;QACd,IAAIhD,MAAM,GAAG,EAAE;QACf,IAAI,IAAI,CAAC6P,aAAa,IAAIQ,MAAM,IAAI,IAAI,CAACR,aAAa,EAAE;UACtD7M,MAAM,GAAGwN,IAAI,CAACC,KAAK,CAACJ,MAAM,GAAG,IAAI,CAACR,aAAa,CAAC;UAChDQ,MAAM,IAAIrN,MAAM,GAAG,IAAI,CAAC6M,aAAa;UACrC7P,MAAM,GAAG,IAAIiQ,KAAK,CAACjN,MAAM,GAAG,CAAC,CAAC,CAAC0M,IAAI,CAAC,IAAI,CAACI,eAAe,CAAC;QAC3D;QACA,IAAIO,MAAM,EAAE;UACVrQ,MAAM,IAAI,IAAIiQ,KAAK,CAACI,MAAM,GAAG,CAAC,CAAC,CAACX,IAAI,CAAC,GAAG,CAAC;QAC3C;QAEA,IAAI,CAACE,OAAO,CAACxN,IAAI,CAACpC,MAAM,CAAC;MAC3B,CAAC;MAED,SAAShB,MAAMA,CAACL,OAAO,EAAE6G,gBAAgB,EAAE;QACzC,IAAI,CAACkL,cAAc,GAAG,IAAIf,iBAAiB,CAAChR,OAAO,EAAE6G,gBAAgB,CAAC;QACtE,IAAI,CAACE,GAAG,GAAG,KAAK;QAChB,IAAI,CAACiL,iBAAiB,GAAGhS,OAAO,CAACiS,gBAAgB;QACjD,IAAI,CAACvB,WAAW,GAAG1Q,OAAO,CAAC0Q,WAAW;QACtC,IAAI,CAAC7F,gBAAgB,GAAG7K,OAAO,CAAC6K,gBAAgB;QAChD,IAAI,CAACgG,kBAAkB,GAAG7Q,OAAO,CAAC6Q,kBAAkB;QACpD,IAAI,CAACqB,OAAO,GAAG,EAAE;QACjB,IAAI,CAACrG,aAAa,GAAG,IAAI;QACzB,IAAI,CAACR,YAAY,GAAG,IAAI;QACxB,IAAI,CAAC8E,SAAS,GAAG,IAAIjB,UAAU,CAAC,IAAI,CAAC;QACrC,IAAI,CAAC1D,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACW,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACC,sBAAsB,GAAG,KAAK;QACnC;QACA,IAAI,CAAC+F,gBAAgB,CAAC,CAAC;MACzB;MAEA9R,MAAM,CAAC2E,SAAS,CAACmN,gBAAgB,GAAG,YAAW;QAC7C,IAAI,CAACtG,aAAa,GAAG,IAAI,CAACR,YAAY;QACtC,IAAI,CAACA,YAAY,GAAG,IAAI,CAAC8E,SAAS,CAACP,WAAW,CAAC,CAAC;QAChD,IAAI,CAACsC,OAAO,CAACzO,IAAI,CAAC,IAAI,CAAC4H,YAAY,CAAC;MACtC,CAAC;MAEDhL,MAAM,CAAC2E,SAAS,CAAC0B,eAAe,GAAG,YAAW;QAC5C,OAAO,IAAI,CAACwL,OAAO,CAAC3Q,MAAM;MAC5B,CAAC;MAEDlB,MAAM,CAAC2E,SAAS,CAAC8L,iBAAiB,GAAG,UAASzM,MAAM,EAAEqN,MAAM,EAAE;QAC5D,OAAO,IAAI,CAACK,cAAc,CAACjB,iBAAiB,CAACzM,MAAM,EAAEqN,MAAM,CAAC;MAC9D,CAAC;MAEDrR,MAAM,CAAC2E,SAAS,CAACiL,eAAe,GAAG,UAAS5L,MAAM,EAAEqN,MAAM,EAAE;QAC1D,OAAO,IAAI,CAACK,cAAc,CAAC9B,eAAe,CAAC5L,MAAM,EAAEqN,MAAM,CAAC;MAC5D,CAAC;MAEDrR,MAAM,CAAC2E,SAAS,CAACiH,QAAQ,GAAG,YAAW;QACrC,OAAO,CAAC,IAAI,CAACJ,aAAa,IAAI,IAAI,CAACR,YAAY,CAACY,QAAQ,CAAC,CAAC;MAC5D,CAAC;MAED5L,MAAM,CAAC2E,SAAS,CAACmG,YAAY,GAAG,UAASJ,aAAa,EAAE;QACtD;QACA;QACA,IAAI,IAAI,CAACkB,QAAQ,CAAC,CAAC,IAChB,CAAClB,aAAa,IAAI,IAAI,CAAC1F,kBAAkB,CAAC,CAAE,EAAE;UAC/C,OAAO,KAAK;QACd;;QAEA;QACA;QACA,IAAI,CAAC,IAAI,CAAC0B,GAAG,EAAE;UACb,IAAI,CAACoL,gBAAgB,CAAC,CAAC;QACzB;QACA,OAAO,IAAI;MACb,CAAC;MAED9R,MAAM,CAAC2E,SAAS,CAAC4C,QAAQ,GAAG,UAASN,GAAG,EAAE;QACxC,IAAI,CAACpD,IAAI,CAAC,IAAI,CAAC;;QAEf;QACA;QACA,IAAIkO,SAAS,GAAG,IAAI,CAAC/G,YAAY,CAACW,GAAG,CAAC,CAAC;QACvC,IAAIoG,SAAS,EAAE;UACb,IAAIA,SAAS,CAACA,SAAS,CAAC7Q,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;YAC5C6Q,SAAS,GAAGA,SAAS,CAAClR,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;UAC5C;UACA,IAAI,CAACmK,YAAY,CAAC5H,IAAI,CAAC2O,SAAS,CAAC;QACnC;QAEA,IAAI,IAAI,CAACJ,iBAAiB,EAAE;UAC1B,IAAI,CAACG,gBAAgB,CAAC,CAAC;QACzB;QAEA,IAAI9K,UAAU,GAAG,IAAI,CAAC6K,OAAO,CAACnB,IAAI,CAAC,IAAI,CAAC;QAExC,IAAIzJ,GAAG,KAAK,IAAI,EAAE;UAChBD,UAAU,GAAGA,UAAU,CAACnG,OAAO,CAAC,OAAO,EAAEoG,GAAG,CAAC;QAC/C;QACA,OAAOD,UAAU;MACnB,CAAC;MAEDhH,MAAM,CAAC2E,SAAS,CAAC8F,cAAc,GAAG,YAAW;QAC3C,IAAI,CAACO,YAAY,CAAC6E,eAAe,CAAC,CAAC;MACrC,CAAC;MAED7P,MAAM,CAAC2E,SAAS,CAACsG,UAAU,GAAG,UAASjH,MAAM,EAAEoC,SAAS,EAAE;QACxDpC,MAAM,GAAGA,MAAM,IAAI,CAAC;QACpBoC,SAAS,GAAGA,SAAS,IAAI,CAAC;;QAE1B;QACA,IAAI,CAAC0J,SAAS,CAAC7E,UAAU,CAACjH,MAAM,EAAEoC,SAAS,CAAC;;QAE5C;QACA,IAAI,IAAI,CAACyL,OAAO,CAAC3Q,MAAM,GAAG,CAAC,EAAE;UAC3B,IAAI,CAAC8J,YAAY,CAACC,UAAU,CAACjH,MAAM,EAAEoC,SAAS,CAAC;UAC/C,OAAO,IAAI;QACb;QAEA,IAAI,CAAC4E,YAAY,CAACC,UAAU,CAAC,CAAC;QAC9B,OAAO,KAAK;MACd,CAAC;MAEDjL,MAAM,CAAC2E,SAAS,CAAC0G,aAAa,GAAG,UAASjK,KAAK,EAAE;QAC/C,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,KAAK,CAACmI,QAAQ,EAAEtI,CAAC,EAAE,EAAE;UACvC,IAAI,CAAC6Q,gBAAgB,CAAC,CAAC;QACzB;QACA,IAAI,CAAC9G,YAAY,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAACD,YAAY,CAAC5H,IAAI,CAAChC,KAAK,CAAC8J,iBAAiB,CAAC;QAC/C,IAAI,CAACF,YAAY,CAAC5H,IAAI,CAAChC,KAAK,CAACI,IAAI,CAAC;QAClC,IAAI,CAAC2J,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACW,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACC,sBAAsB,GAAG,KAAK;MACrC,CAAC;MAED/L,MAAM,CAAC2E,SAAS,CAACkH,SAAS,GAAG,UAASmG,eAAe,EAAE;QACrD,IAAI,CAACC,wBAAwB,CAAC,CAAC;QAC/B,IAAI,CAACjH,YAAY,CAAC5H,IAAI,CAAC4O,eAAe,CAAC;QACvC,IAAI,CAAC7G,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACW,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAACf,YAAY,CAACgF,WAAW,CAAC,CAAC;MAC/D,CAAC;MAEDhQ,MAAM,CAAC2E,SAAS,CAACsN,wBAAwB,GAAG,YAAW;QACrD,IAAI,IAAI,CAAC9G,kBAAkB,IAAI,CAAC,IAAI,CAACnG,kBAAkB,CAAC,CAAC,EAAE;UACzD,IAAI,CAAC,IAAI,CAAC8G,kBAAkB,EAAE;YAC5B,IAAI,CAACrB,cAAc,CAAC,CAAC;UACvB;UACA,IAAI,CAACO,YAAY,CAAC5H,IAAI,CAAC,GAAG,CAAC;QAC7B;MACF,CAAC;MAEDpD,MAAM,CAAC2E,SAAS,CAAC7B,aAAa,GAAG,UAASkK,KAAK,EAAE;QAC/C,IAAIkF,aAAa,GAAG,IAAI,CAACL,OAAO,CAAC3Q,MAAM;QACvC,OAAO8L,KAAK,GAAGkF,aAAa,EAAE;UAC5B,IAAI,CAACL,OAAO,CAAC7E,KAAK,CAAC,CAACoD,cAAc,CAAC,CAAC;UACpCpD,KAAK,EAAE;QACT;QACA,IAAI,CAAChC,YAAY,CAACsF,mBAAmB,CAAC,CAAC;MACzC,CAAC;MAEDtQ,MAAM,CAAC2E,SAAS,CAACd,IAAI,GAAG,UAASsO,YAAY,EAAE;QAC7CA,YAAY,GAAIA,YAAY,KAAKhI,SAAS,GAAI,KAAK,GAAGgI,YAAY;QAElE,IAAI,CAACnH,YAAY,CAACnH,IAAI,CAAC,CAAC;QAExB,OAAOsO,YAAY,IAAI,IAAI,CAACN,OAAO,CAAC3Q,MAAM,GAAG,CAAC,IAC5C,IAAI,CAAC8J,YAAY,CAACY,QAAQ,CAAC,CAAC,EAAE;UAC9B,IAAI,CAACiG,OAAO,CAAClG,GAAG,CAAC,CAAC;UAClB,IAAI,CAACX,YAAY,GAAG,IAAI,CAAC6G,OAAO,CAAC,IAAI,CAACA,OAAO,CAAC3Q,MAAM,GAAG,CAAC,CAAC;UACzD,IAAI,CAAC8J,YAAY,CAACnH,IAAI,CAAC,CAAC;QAC1B;QAEA,IAAI,CAAC2H,aAAa,GAAG,IAAI,CAACqG,OAAO,CAAC3Q,MAAM,GAAG,CAAC,GAC1C,IAAI,CAAC2Q,OAAO,CAAC,IAAI,CAACA,OAAO,CAAC3Q,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;MAChD,CAAC;MAEDlB,MAAM,CAAC2E,SAAS,CAACK,kBAAkB,GAAG,YAAW;QAC/C,OAAO,IAAI,CAACgG,YAAY,CAACY,QAAQ,CAAC,CAAC;MACrC,CAAC;MAED5L,MAAM,CAAC2E,SAAS,CAAC0I,oBAAoB,GAAG,YAAW;QACjD,OAAO,IAAI,CAACzB,QAAQ,CAAC,CAAC,IACnB,IAAI,CAACZ,YAAY,CAACY,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACJ,aAAa,CAACI,QAAQ,CAAC,CAAE;MACnE,CAAC;MAED5L,MAAM,CAAC2E,SAAS,CAACyN,uBAAuB,GAAG,UAASC,WAAW,EAAEC,SAAS,EAAE;QAC1E,IAAItF,KAAK,GAAG,IAAI,CAAC6E,OAAO,CAAC3Q,MAAM,GAAG,CAAC;QACnC,OAAO8L,KAAK,IAAI,CAAC,EAAE;UACjB,IAAIuF,kBAAkB,GAAG,IAAI,CAACV,OAAO,CAAC7E,KAAK,CAAC;UAC5C,IAAIuF,kBAAkB,CAAC3G,QAAQ,CAAC,CAAC,EAAE;YACjC;UACF,CAAC,MAAM,IAAI2G,kBAAkB,CAAC/C,IAAI,CAAC,CAAC,CAAC,CAAC9O,OAAO,CAAC2R,WAAW,CAAC,KAAK,CAAC,IAC9DE,kBAAkB,CAAC/C,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK8C,SAAS,EAAE;YAC3C,IAAI,CAACT,OAAO,CAAC5B,MAAM,CAACjD,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI6B,UAAU,CAAC,IAAI,CAAC,CAAC;YACvD,IAAI,CAACrD,aAAa,GAAG,IAAI,CAACqG,OAAO,CAAC,IAAI,CAACA,OAAO,CAAC3Q,MAAM,GAAG,CAAC,CAAC;YAC1D;UACF;UACA8L,KAAK,EAAE;QACT;MACF,CAAC;MAED5N,MAAM,CAACU,OAAO,CAACE,MAAM,GAAGA,MAAM;;MAG9B;IAAM,CAAC,IACP;IACA,KAAO,UAASZ,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,SAASa,KAAKA,CAACqB,IAAI,EAAEE,IAAI,EAAE+H,QAAQ,EAAE2B,iBAAiB,EAAE;QACtD,IAAI,CAAC5J,IAAI,GAAGA,IAAI;QAChB,IAAI,CAACE,IAAI,GAAGA,IAAI;;QAEhB;QACA;QACA;QACA;QACA,IAAI,CAACkI,eAAe,GAAG,IAAI,CAAC,CAAC;;QAG7B;QACA,IAAI,CAACH,QAAQ,GAAGA,QAAQ,IAAI,CAAC;QAC7B,IAAI,CAAC2B,iBAAiB,GAAGA,iBAAiB,IAAI,EAAE;QAChD,IAAI,CAAC/F,MAAM,GAAG,IAAI;QAClB,IAAI,CAACkC,IAAI,GAAG,IAAI;QAChB,IAAI,CAACkE,QAAQ,GAAG,IAAI;QACpB,IAAI,CAAC2B,MAAM,GAAG,IAAI;QAClB,IAAI,CAACsF,MAAM,GAAG,IAAI;QAClB,IAAI,CAACtE,UAAU,GAAG,IAAI;MACxB;MAGA9O,MAAM,CAACU,OAAO,CAACG,KAAK,GAAGA,KAAK;;MAG5B;IAAM,CAAC,IACP;IACA,KAAO,UAASwS,uBAAuB,EAAE3S,OAAO,EAAE;MAElD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;;MAKA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI4S,6BAA6B,GAAG,4CAA4C;;MAEhF;MACA,IAAIC,wBAAwB,GAAG,6CAA6C;;MAE5E;MACA;MACA;MACA;MACA,IAAIC,4BAA4B,GAAG,0vJAA0vJ;MAC7xJ,IAAIC,uBAAuB,GAAG,+9EAA+9E;MAC7/E;MACA;;MAEA,IAAIC,wBAAwB,GAAG,6CAA6C;MAC5E,IAAIC,eAAe,GAAG,KAAK,GAAGD,wBAAwB,GAAG,IAAI,GAAGJ,6BAA6B,GAAGE,4BAA4B,GAAG,IAAI;MACnI,IAAII,eAAe,GAAG,KAAK,GAAGF,wBAAwB,GAAG,IAAI,GAAGH,wBAAwB,GAAGC,4BAA4B,GAAGC,uBAAuB,GAAG,KAAK;MAEzJ/S,OAAO,CAACmT,UAAU,GAAG,IAAIC,MAAM,CAACH,eAAe,GAAGC,eAAe,EAAE,GAAG,CAAC;MACvElT,OAAO,CAACiT,eAAe,GAAG,IAAIG,MAAM,CAACH,eAAe,CAAC;MACrDjT,OAAO,CAACqT,eAAe,GAAG,IAAID,MAAM,CAAC,KAAK,GAAGJ,wBAAwB,GAAG,IAAI,GAAGH,wBAAwB,GAAGC,4BAA4B,GAAGC,uBAAuB,GAAG,KAAK,CAAC;MAEzK,IAAIO,kBAAkB,GAAG,qDAAqD,CAAC,CAAC;;MAEhF;;MAEAtT,OAAO,CAACsO,OAAO,GAAG,oBAAoB;;MAEtC;MACA;;MAEA;MACA;MACAtO,OAAO,CAACoH,SAAS,GAAG,IAAIgM,MAAM,CAAC,OAAO,GAAGpT,OAAO,CAACsO,OAAO,CAACiF,MAAM,CAAC;MAChEvT,OAAO,CAACmD,aAAa,GAAG,IAAIiQ,MAAM,CAACpT,OAAO,CAACoH,SAAS,CAACmM,MAAM,EAAE,GAAG,CAAC;;MAGjE;IAAM,CAAC,IACP;IACA,KAAO,UAASjU,MAAM,EAAEC,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAIgU,WAAW,GAAIhU,mBAAmB,CAAC,CAAC,CAAC,CAACE,OAAQ;MAElD,IAAIoC,mBAAmB,GAAG,CAAC,gBAAgB,EAAE,eAAe,EAAE,kBAAkB,CAAC;MAEjF,SAASpC,OAAOA,CAACG,OAAO,EAAE;QACxB2T,WAAW,CAACC,IAAI,CAAC,IAAI,EAAE5T,OAAO,EAAE,IAAI,CAAC;;QAErC;QACA,IAAI6T,eAAe,GAAG,IAAI,CAACC,WAAW,CAACtG,WAAW,IAAI,IAAI;QAC1D,IAAIqG,eAAe,KAAK,eAAe,EAAE;UAAE;UACzC,IAAI,CAACC,WAAW,CAACtG,WAAW,GAAG,QAAQ;QACzC,CAAC,MAAM,IAAIqG,eAAe,KAAK,0BAA0B,EAAE;UAAE;UAC3D,IAAI,CAACC,WAAW,CAACtG,WAAW,GAAG,0BAA0B;QAC3D,CAAC,MAAM,IAAI,IAAI,CAACsG,WAAW,CAACC,kBAAkB,KAAKvJ,SAAS,EAAE;UAAE;UAC9D,IAAI,CAACsJ,WAAW,CAACtG,WAAW,GAAG,IAAI,CAACsG,WAAW,CAACC,kBAAkB,GAAG,QAAQ,GAAG,UAAU;UAC1F;UACA;QACF;;QAEA;QACA;;QAEA,IAAIC,iBAAiB,GAAG,IAAI,CAACC,mBAAmB,CAAC,aAAa,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAC;QAEhI,IAAI,CAAC7G,qBAAqB,GAAG,KAAK,CAAC,CAAC;QACpC,IAAI,CAACI,WAAW,GAAG,UAAU;QAE7B,KAAK,IAAI0G,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGF,iBAAiB,CAACzS,MAAM,EAAE2S,EAAE,EAAE,EAAE;UACpD,IAAIF,iBAAiB,CAACE,EAAE,CAAC,KAAK,iBAAiB,EAAE;YAC/C,IAAI,CAAC9G,qBAAqB,GAAG,IAAI;UACnC,CAAC,MAAM;YACL,IAAI,CAACI,WAAW,GAAGwG,iBAAiB,CAACE,EAAE,CAAC;UAC1C;QACF;QAEA,IAAI,CAACjF,wBAAwB,GAAG,IAAI,CAACkF,YAAY,CAAC,0BAA0B,CAAC;QAC7E,IAAI,CAACnF,qBAAqB,GAAG,IAAI,CAACmF,YAAY,CAAC,uBAAuB,CAAC;QACvE,IAAI,CAACzH,cAAc,GAAG,IAAI,CAACyH,YAAY,CAAC,gBAAgB,CAAC;QACzD,IAAI,CAACnH,oBAAoB,GAAG,IAAI,CAACmH,YAAY,CAAC,sBAAsB,CAAC;QACrE,IAAI,CAAC1G,YAAY,GAAG,IAAI,CAAC0G,YAAY,CAAC,cAAc,CAAC;QACrD,IAAI,CAACpH,yBAAyB,GAAG,IAAI,CAACoH,YAAY,CAAC,2BAA2B,CAAC;QAC/E,IAAI,CAACtH,0BAA0B,GAAG,IAAI,CAACsH,YAAY,CAAC,4BAA4B,CAAC;QACjF,IAAI,CAACrK,sBAAsB,GAAG,IAAI,CAACqK,YAAY,CAAC,wBAAwB,CAAC;QACzE,IAAI,CAACxH,wBAAwB,GAAG,IAAI,CAACwH,YAAY,CAAC,0BAA0B,EAAE,IAAI,CAAC;QACnF,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACD,YAAY,CAAC,kBAAkB,CAAC;QAC7D,IAAI,CAACE,GAAG,GAAG,IAAI,CAACF,YAAY,CAAC,KAAK,CAAC;QACnC,IAAI,CAACxI,WAAW,GAAG,IAAI,CAACwI,YAAY,CAAC,aAAa,CAAC;QACnD,IAAI,CAACvJ,iBAAiB,GAAG,IAAI,CAAC0J,cAAc,CAAC,mBAAmB,EAAErS,mBAAmB,CAAC;;QAEtF;QACA,IAAI,CAAC+E,eAAe,GAAG,IAAI,CAACmN,YAAY,CAAC,iBAAiB,CAAC;;QAE3D;QACA,IAAI,IAAI,CAAC1G,YAAY,EAAE;UACrB,IAAI,CAACV,yBAAyB,GAAG,IAAI;QACvC;MAEF;MACAlN,OAAO,CAACmF,SAAS,GAAG,IAAI2O,WAAW,CAAC,CAAC;MAIrClU,MAAM,CAACU,OAAO,CAACN,OAAO,GAAGA,OAAO;;MAGhC;IAAM,CAAC,IACP;IACA,KAAO,UAASJ,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,SAASI,OAAOA,CAACG,OAAO,EAAEuU,iBAAiB,EAAE;QAC3C,IAAI,CAACT,WAAW,GAAGU,UAAU,CAACxU,OAAO,EAAEuU,iBAAiB,CAAC;;QAEzD;QACA,IAAI,CAACnN,QAAQ,GAAG,IAAI,CAAC+M,YAAY,CAAC,UAAU,CAAC;QAE7C,IAAI,CAAC7M,GAAG,GAAG,IAAI,CAACmN,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC;QAC9C,IAAI,CAACxC,gBAAgB,GAAG,IAAI,CAACkC,YAAY,CAAC,kBAAkB,CAAC;QAC7D,IAAI,CAACzD,WAAW,GAAG,IAAI,CAACgE,WAAW,CAAC,aAAa,EAAE,CAAC,CAAC;QACrD,IAAI,CAACtD,WAAW,GAAG,IAAI,CAACqD,eAAe,CAAC,aAAa,EAAE,GAAG,CAAC;QAC3D,IAAI,CAAClD,YAAY,GAAG,IAAI,CAACmD,WAAW,CAAC,cAAc,CAAC;QAEpD,IAAI,CAACvK,iBAAiB,GAAG,IAAI,CAACgK,YAAY,CAAC,mBAAmB,EAAE,IAAI,CAAC;QACrE,IAAI,CAACjK,qBAAqB,GAAG,IAAI,CAACwK,WAAW,CAAC,uBAAuB,EAAE,KAAK,CAAC;QAC7E,IAAI,CAAC,IAAI,CAACvK,iBAAiB,EAAE;UAC3B,IAAI,CAACD,qBAAqB,GAAG,CAAC;QAChC;QAEA,IAAI,CAACmH,gBAAgB,GAAG,IAAI,CAAC8C,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC/C,WAAW,KAAK,IAAI,CAAC;QACxF,IAAI,IAAI,CAACC,gBAAgB,EAAE;UACzB,IAAI,CAACD,WAAW,GAAG,IAAI;;UAEvB;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,IAAI,CAACV,WAAW,KAAK,CAAC,EAAE;YAC1B,IAAI,CAACA,WAAW,GAAG,CAAC;UACtB;QACF;;QAEA;QACA,IAAI,CAAC7F,gBAAgB,GAAG,IAAI,CAAC6J,WAAW,CAAC,kBAAkB,EAAE,IAAI,CAACA,WAAW,CAAC,UAAU,CAAC,CAAC;QAE1F,IAAI,CAAC7D,kBAAkB,GAAG,IAAI,CAACsD,YAAY,CAAC,oBAAoB,CAAC;;QAEjE;QACA;QACA;QACA,IAAI,CAACQ,UAAU,GAAG,IAAI,CAACV,mBAAmB,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;MACjJ;MAEApU,OAAO,CAACmF,SAAS,CAAC4P,UAAU,GAAG,UAASC,IAAI,EAAEC,aAAa,EAAE;QAC3D,IAAIC,YAAY,GAAG,IAAI,CAACjB,WAAW,CAACe,IAAI,CAAC;QACzC,IAAIxT,MAAM,GAAGyT,aAAa,IAAI,EAAE;QAChC,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;UACpC,IAAIA,YAAY,KAAK,IAAI,IAAI,OAAOA,YAAY,CAACC,MAAM,KAAK,UAAU,EAAE;YACtE3T,MAAM,GAAG0T,YAAY,CAACC,MAAM,CAAC,CAAC;UAChC;QACF,CAAC,MAAM,IAAI,OAAOD,YAAY,KAAK,QAAQ,EAAE;UAC3C1T,MAAM,GAAG0T,YAAY,CAACE,KAAK,CAAC,oBAAoB,CAAC;QACnD;QACA,OAAO5T,MAAM;MACf,CAAC;MAEDxB,OAAO,CAACmF,SAAS,CAACmP,YAAY,GAAG,UAASU,IAAI,EAAEC,aAAa,EAAE;QAC7D,IAAIC,YAAY,GAAG,IAAI,CAACjB,WAAW,CAACe,IAAI,CAAC;QACzC,IAAIxT,MAAM,GAAG0T,YAAY,KAAKvK,SAAS,GAAG,CAAC,CAACsK,aAAa,GAAG,CAAC,CAACC,YAAY;QAC1E,OAAO1T,MAAM;MACf,CAAC;MAEDxB,OAAO,CAACmF,SAAS,CAACyP,eAAe,GAAG,UAASI,IAAI,EAAEC,aAAa,EAAE;QAChE,IAAIC,YAAY,GAAG,IAAI,CAACjB,WAAW,CAACe,IAAI,CAAC;QACzC,IAAIxT,MAAM,GAAGyT,aAAa,IAAI,EAAE;QAChC,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;UACpC1T,MAAM,GAAG0T,YAAY,CAAC7T,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;QACtF;QACA,OAAOG,MAAM;MACf,CAAC;MAEDxB,OAAO,CAACmF,SAAS,CAAC0P,WAAW,GAAG,UAASG,IAAI,EAAEC,aAAa,EAAE;QAC5D,IAAIC,YAAY,GAAG,IAAI,CAACjB,WAAW,CAACe,IAAI,CAAC;QACzCC,aAAa,GAAGI,QAAQ,CAACJ,aAAa,EAAE,EAAE,CAAC;QAC3C,IAAIK,KAAK,CAACL,aAAa,CAAC,EAAE;UACxBA,aAAa,GAAG,CAAC;QACnB;QACA,IAAIzT,MAAM,GAAG6T,QAAQ,CAACH,YAAY,EAAE,EAAE,CAAC;QACvC,IAAII,KAAK,CAAC9T,MAAM,CAAC,EAAE;UACjBA,MAAM,GAAGyT,aAAa;QACxB;QACA,OAAOzT,MAAM;MACf,CAAC;MAEDxB,OAAO,CAACmF,SAAS,CAACsP,cAAc,GAAG,UAASO,IAAI,EAAEO,cAAc,EAAEN,aAAa,EAAE;QAC/E,IAAIzT,MAAM,GAAG,IAAI,CAAC4S,mBAAmB,CAACY,IAAI,EAAEO,cAAc,EAAEN,aAAa,CAAC;QAC1E,IAAIzT,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;UACvB,MAAM,IAAI8T,KAAK,CACb,oCAAoC,GAAGR,IAAI,GAAG,8CAA8C,GAC5FO,cAAc,GAAG,oBAAoB,GAAG,IAAI,CAACtB,WAAW,CAACe,IAAI,CAAC,GAAG,GAAG,CAAC;QACzE;QAEA,OAAOxT,MAAM,CAAC,CAAC,CAAC;MAClB,CAAC;MAGDxB,OAAO,CAACmF,SAAS,CAACiP,mBAAmB,GAAG,UAASY,IAAI,EAAEO,cAAc,EAAEN,aAAa,EAAE;QACpF,IAAI,CAACM,cAAc,IAAIA,cAAc,CAAC7T,MAAM,KAAK,CAAC,EAAE;UAClD,MAAM,IAAI8T,KAAK,CAAC,iCAAiC,CAAC;QACpD;QAEAP,aAAa,GAAGA,aAAa,IAAI,CAACM,cAAc,CAAC,CAAC,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,CAACE,mBAAmB,CAACR,aAAa,EAAEM,cAAc,CAAC,EAAE;UAC5D,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;QAC3C;QAEA,IAAIhU,MAAM,GAAG,IAAI,CAACuT,UAAU,CAACC,IAAI,EAAEC,aAAa,CAAC;QACjD,IAAI,CAAC,IAAI,CAACQ,mBAAmB,CAACjU,MAAM,EAAE+T,cAAc,CAAC,EAAE;UACrD,MAAM,IAAIC,KAAK,CACb,oCAAoC,GAAGR,IAAI,GAAG,4CAA4C,GAC1FO,cAAc,GAAG,oBAAoB,GAAG,IAAI,CAACtB,WAAW,CAACe,IAAI,CAAC,GAAG,GAAG,CAAC;QACzE;QAEA,OAAOxT,MAAM;MACf,CAAC;MAEDxB,OAAO,CAACmF,SAAS,CAACsQ,mBAAmB,GAAG,UAASjU,MAAM,EAAE+T,cAAc,EAAE;QACvE,OAAO/T,MAAM,CAACE,MAAM,IAAI6T,cAAc,CAAC7T,MAAM,IAC3C,CAACF,MAAM,CAACkU,IAAI,CAAC,UAAS1F,IAAI,EAAE;UAAE,OAAOuF,cAAc,CAACrU,OAAO,CAAC8O,IAAI,CAAC,KAAK,CAAC,CAAC;QAAE,CAAC,CAAC;MAChF,CAAC;;MAGD;MACA;MACA;MACA;MACA;MACA,SAAS2E,UAAUA,CAACgB,UAAU,EAAEC,cAAc,EAAE;QAC9C,IAAIC,SAAS,GAAG,CAAC,CAAC;QAClBF,UAAU,GAAGG,cAAc,CAACH,UAAU,CAAC;QACvC,IAAIX,IAAI;QAER,KAAKA,IAAI,IAAIW,UAAU,EAAE;UACvB,IAAIX,IAAI,KAAKY,cAAc,EAAE;YAC3BC,SAAS,CAACb,IAAI,CAAC,GAAGW,UAAU,CAACX,IAAI,CAAC;UACpC;QACF;;QAEA;QACA,IAAIY,cAAc,IAAID,UAAU,CAACC,cAAc,CAAC,EAAE;UAChD,KAAKZ,IAAI,IAAIW,UAAU,CAACC,cAAc,CAAC,EAAE;YACvCC,SAAS,CAACb,IAAI,CAAC,GAAGW,UAAU,CAACC,cAAc,CAAC,CAACZ,IAAI,CAAC;UACpD;QACF;QACA,OAAOa,SAAS;MAClB;MAEA,SAASC,cAAcA,CAAC3V,OAAO,EAAE;QAC/B,IAAI4V,aAAa,GAAG,CAAC,CAAC;QACtB,IAAIC,GAAG;QAEP,KAAKA,GAAG,IAAI7V,OAAO,EAAE;UACnB,IAAI8V,MAAM,GAAGD,GAAG,CAAC3U,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;UACnC0U,aAAa,CAACE,MAAM,CAAC,GAAG9V,OAAO,CAAC6V,GAAG,CAAC;QACtC;QACA,OAAOD,aAAa;MACtB;MAEAnW,MAAM,CAACU,OAAO,CAACN,OAAO,GAAGA,OAAO;MAChCJ,MAAM,CAACU,OAAO,CAAC4V,aAAa,GAAGJ,cAAc;MAC7ClW,MAAM,CAACU,OAAO,CAAC6V,SAAS,GAAGxB,UAAU;;MAGrC;IAAM,CAAC,IACP;IACA,KAAO,UAAS/U,MAAM,EAAEC,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAIsW,YAAY,GAAItW,mBAAmB,CAAC,CAAC,CAAC,CAACsW,YAAa;MACxD,IAAIC,aAAa,GAAIvW,mBAAmB,CAAC,CAAC,CAAC,CAACa,SAAU;MACtD,IAAI2V,SAAS,GAAIxW,mBAAmB,CAAC,CAAC,CAAC,CAACgB,KAAM;MAC9C,IAAIyV,UAAU,GAAIzW,mBAAmB,CAAC,EAAE,CAAC,CAACyW,UAAW;MACrD,IAAI7V,KAAK,GAAGZ,mBAAmB,CAAC,CAAC,CAAC;MAClC,IAAI0W,OAAO,GAAI1W,mBAAmB,CAAC,EAAE,CAAC,CAAC0W,OAAQ;MAC/C,IAAIC,kBAAkB,GAAI3W,mBAAmB,CAAC,EAAE,CAAC,CAAC2W,kBAAmB;MAGrE,SAAS1V,QAAQA,CAACC,IAAI,EAAEC,GAAG,EAAE;QAC3B,OAAOA,GAAG,CAACC,OAAO,CAACF,IAAI,CAAC,KAAK,CAAC,CAAC;MACjC;MAGA,IAAIF,KAAK,GAAG;QACVmH,UAAU,EAAE,eAAe;QAC3BE,QAAQ,EAAE,aAAa;QACvBtC,WAAW,EAAE,gBAAgB;QAC7ByC,SAAS,EAAE,cAAc;QACzBE,IAAI,EAAE,SAAS;QACfzG,QAAQ,EAAE,aAAa;QACvB2G,SAAS,EAAE,cAAc;QACzBE,MAAM,EAAE,WAAW;QACnBE,MAAM,EAAE,WAAW;QACnBE,QAAQ,EAAE,aAAa;QACvBE,KAAK,EAAE,UAAU;QACjBE,aAAa,EAAE,kBAAkB;QACjCE,OAAO,EAAE,YAAY;QACrBE,GAAG,EAAE,QAAQ;QACbI,OAAO,EAAE,YAAY;QACrB8M,KAAK,EAAEJ,SAAS,CAACI,KAAK;QACtBC,GAAG,EAAEL,SAAS,CAACK,GAAG;QAClBjN,GAAG,EAAE4M,SAAS,CAAC5M;MACjB,CAAC;MAGD,IAAIkN,eAAe,GAAG,IAAIL,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC;MAEpD,IAAIM,cAAc,GAAG,oIAAoI;MAEzJ,IAAIC,KAAK,GAAG,OAAO;;MAEnB;MACA,IAAIC,WAAW,GAAG,SAAS;MAE3B,IAAIlW,sBAAsB,GAAG,CAC3B,0BAA0B,GAC1B,mCAAmC,GACnC,yBAAyB,EAAEuU,KAAK,CAAC,GAAG,CAAC;;MAEvC;MACA;MACA,IAAI4B,KAAK,GACP,OAAO,GACP,0CAA0C,GAC1C,uEAAuE,GACvE,+BAA+B;MAEjCA,KAAK,GAAGA,KAAK,CAAC3V,OAAO,CAAC,wBAAwB,EAAE,MAAM,CAAC;MACvD;MACA2V,KAAK,GAAG,gBAAgB,GAAGA,KAAK;MAChCA,KAAK,GAAGA,KAAK,CAAC3V,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;MAEhC,IAAI4V,aAAa,GAAG,IAAIvD,MAAM,CAACsD,KAAK,CAAC;;MAErC;MACA,IAAIpW,aAAa,GAAG,uGAAuG,CAACwU,KAAK,CAAC,GAAG,CAAC;MACtI,IAAI8B,cAAc,GAAGtW,aAAa,CAACuU,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;MACrL,IAAIgC,qBAAqB,GAAG,IAAIzD,MAAM,CAAC,MAAM,GAAGwD,cAAc,CAAChG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;;MAEhF;;MAEA,IAAIkG,eAAe;MAEnB,IAAIzW,SAAS,GAAG,SAAAA,CAAS0W,YAAY,EAAElX,OAAO,EAAE;QAC9CkW,aAAa,CAACtC,IAAI,CAAC,IAAI,EAAEsD,YAAY,EAAElX,OAAO,CAAC;QAE/C,IAAI,CAACmX,SAAS,CAACC,UAAU,GAAG,IAAI,CAACD,SAAS,CAACC,UAAU,CAACC,QAAQ,CAC5D,yDAAyD,CAAC3D,MAAM,EAChE,cAAc,CAACA,MAAM,CAAC;QAExB,IAAI4D,cAAc,GAAG,IAAIjB,OAAO,CAAC,IAAI,CAACkB,MAAM,CAAC;QAC7C,IAAIC,WAAW,GAAG,IAAIlB,kBAAkB,CAAC,IAAI,CAACiB,MAAM,CAAC,CAClDE,YAAY,CAAC,IAAI,CAAC1S,QAAQ,CAAC;QAE9B,IAAI,CAAC2S,UAAU,GAAG;UAChBC,QAAQ,EAAEH,WAAW;UACrBlE,UAAU,EAAEkE,WAAW,CAACI,aAAa,CAACrX,KAAK,CAAC+S,UAAU,CAAC,CAAC+D,QAAQ,CAAC9W,KAAK,CAACiT,eAAe,CAAC;UACvFqE,MAAM,EAAEP,cAAc,CAACD,QAAQ,CAACX,cAAc,CAAC;UAC/CG,KAAK,EAAES,cAAc,CAACD,QAAQ,CAACP,aAAa,CAAC;UAC7C;UACAgB,OAAO,EAAER,cAAc,CAACM,aAAa,CAAC,MAAM,CAAC,CAACG,KAAK,CAAC,oBAAoB,CAAC;UACzE;UACAC,aAAa,EAAEV,cAAc,CAACM,aAAa,CAAC,MAAM,CAAC,CAACK,WAAW,CAAC,MAAM,CAAC;UACvEC,kBAAkB,EAAEZ,cAAc,CAACD,QAAQ,CAAC,MAAM,CAAC;UACnDc,gBAAgB,EAAEb,cAAc,CAACD,QAAQ,CAAC,KAAK,CAAC;UAChDe,OAAO,EAAEd,cAAc,CAACM,aAAa,CAAC,UAAU,CAAC,CAACK,WAAW,CAAC1X,KAAK,CAACgH,SAAS,CAAC;UAC9E8Q,OAAO,EAAEf,cAAc,CAACM,aAAa,CAAC,IAAI,CAAC,CAACK,WAAW,CAAC1X,KAAK,CAACgH,SAAS,CAAC;UACxE+Q,GAAG,EAAEhB,cAAc,CAACD,QAAQ,CAAC,iLAAiL,CAAC;UAC/MkB,YAAY,EAAEf,WAAW,CAACO,KAAK,CAAC,uBAAuB,CAAC;UACxDS,YAAY,EAAEhB,WAAW,CAACO,KAAK,CAAC,uBAAuB,CAAC;UACxDU,aAAa,EAAEjB,WAAW,CAACO,KAAK,CAAC,QAAQ,CAAC;UAC1CW,mBAAmB,EAAElB,WAAW,CAACO,KAAK,CAAC,QAAQ;QACjD,CAAC;MAEH,CAAC;MACDvX,SAAS,CAACwE,SAAS,GAAG,IAAIkR,aAAa,CAAC,CAAC;MAEzC1V,SAAS,CAACwE,SAAS,CAAC2T,WAAW,GAAG,UAASlR,aAAa,EAAE;QACxD,OAAOA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACwI,OAAO,IAAI1B,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACsI,aAAa,IAAIxB,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAAC8I,OAAO;MACnI,CAAC;MAEDjJ,SAAS,CAACwE,SAAS,CAAC4T,WAAW,GAAG,UAASnR,aAAa,EAAE;QACxD,OAAOA,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAAC+E,WAAW,IAAI+B,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACmH,UAAU;MAC5F,CAAC;MAEDtH,SAAS,CAACwE,SAAS,CAAC6T,WAAW,GAAG,UAASpR,aAAa,EAAEqR,UAAU,EAAE;QACpE,OAAO,CAACrR,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACwH,SAAS,IAAIV,aAAa,CAAC9F,IAAI,KAAKhB,KAAK,CAACqH,QAAQ,KACpF8Q,UAAU,KACRrR,aAAa,CAAC5F,IAAI,KAAK,GAAG,IAAIiX,UAAU,CAACjX,IAAI,KAAK,GAAG,IACrD4F,aAAa,CAAC5F,IAAI,KAAK,GAAG,IAAIiX,UAAU,CAACjX,IAAI,KAAK,GAAI,IACtD4F,aAAa,CAAC5F,IAAI,KAAK,GAAG,IAAIiX,UAAU,CAACjX,IAAI,KAAK,GAAI,CAAE;MAC/D,CAAC;MAEDrB,SAAS,CAACwE,SAAS,CAAC4B,MAAM,GAAG,YAAW;QACtCqQ,eAAe,GAAG,KAAK;MACzB,CAAC;MAEDzW,SAAS,CAACwE,SAAS,CAAC+T,eAAe,GAAG,UAASC,cAAc,EAAEF,UAAU,EAAE;QAAE;QAC3E,IAAIrX,KAAK,GAAG,IAAI;QAChB,IAAI,CAACwX,eAAe,CAAC,CAAC;QACtB,IAAIlV,CAAC,GAAG,IAAI,CAACwT,MAAM,CAACtM,IAAI,CAAC,CAAC;QAE1B,IAAIlH,CAAC,KAAK,IAAI,EAAE;UACd,OAAO,IAAI,CAACmV,aAAa,CAACvY,KAAK,CAAC4I,GAAG,EAAE,EAAE,CAAC;QAC1C;QAEA9H,KAAK,GAAGA,KAAK,IAAI,IAAI,CAAC0X,oBAAoB,CAACpV,CAAC,CAAC;QAC7CtC,KAAK,GAAGA,KAAK,IAAI,IAAI,CAAC2X,YAAY,CAACrV,CAAC,CAAC;QACrCtC,KAAK,GAAGA,KAAK,IAAI,IAAI,CAAC4X,UAAU,CAACtV,CAAC,EAAE,IAAI,CAACwT,MAAM,CAACtM,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1DxJ,KAAK,GAAGA,KAAK,IAAI,IAAI,CAAC6X,UAAU,CAACN,cAAc,CAAC;QAChDvX,KAAK,GAAGA,KAAK,IAAI,IAAI,CAAC8X,aAAa,CAACxV,CAAC,CAAC;QACtCtC,KAAK,GAAGA,KAAK,IAAI,IAAI,CAAC+X,aAAa,CAACzV,CAAC,CAAC;QACtCtC,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACgY,YAAY,CAAC1V,CAAC,EAAEiV,cAAc,CAAC;QACrDvX,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACiY,SAAS,CAAC3V,CAAC,EAAEiV,cAAc,CAAC;QAClDvX,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACkY,iBAAiB,CAAC,CAAC;QACzClY,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACyX,aAAa,CAACvY,KAAK,CAAC8I,OAAO,EAAE,IAAI,CAAC8N,MAAM,CAAC7P,IAAI,CAAC,CAAC,CAAC;QAEtE,OAAOjG,KAAK;MACd,CAAC;MAEDjB,SAAS,CAACwE,SAAS,CAACsU,UAAU,GAAG,UAASN,cAAc,EAAE;QACxD,IAAIY,gBAAgB;QACpBA,gBAAgB,GAAG,IAAI,CAAClC,UAAU,CAACpE,UAAU,CAACuG,IAAI,CAAC,CAAC;QACpD,IAAID,gBAAgB,KAAK,EAAE,EAAE;UAC3BA,gBAAgB,GAAGA,gBAAgB,CAAC1Y,OAAO,CAACX,KAAK,CAAC+C,aAAa,EAAE,IAAI,CAAC;UACtE,IAAI,EAAE0V,cAAc,CAACrX,IAAI,KAAKhB,KAAK,CAAC0I,GAAG,IAClC2P,cAAc,CAACrX,IAAI,KAAKhB,KAAK,CAACiB,QAAQ,KAAKoX,cAAc,CAACnX,IAAI,KAAK,KAAK,IAAImX,cAAc,CAACnX,IAAI,KAAK,KAAK,CAAE,CAAC,IAC/GmV,qBAAqB,CAACxP,IAAI,CAACoS,gBAAgB,CAAC,EAAE;YAC9C,IAAI,CAACA,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,IAAI,MACxDZ,cAAc,CAACrX,IAAI,KAAKhB,KAAK,CAAC0H,IAAI,IAAI2Q,cAAc,CAACrX,IAAI,KAAKhB,KAAK,CAAC8H,MAAM,CAAC,EAAE;cAAE;cAChF,OAAO,IAAI,CAACyQ,aAAa,CAACvY,KAAK,CAACkI,QAAQ,EAAE+Q,gBAAgB,CAAC;YAC7D;YACA,OAAO,IAAI,CAACV,aAAa,CAACvY,KAAK,CAACiB,QAAQ,EAAEgY,gBAAgB,CAAC;UAC7D;UACA,OAAO,IAAI,CAACV,aAAa,CAACvY,KAAK,CAAC0H,IAAI,EAAEuR,gBAAgB,CAAC;QACzD;QAEAA,gBAAgB,GAAG,IAAI,CAAClC,UAAU,CAACG,MAAM,CAACgC,IAAI,CAAC,CAAC;QAChD,IAAID,gBAAgB,KAAK,EAAE,EAAE;UAC3B,OAAO,IAAI,CAACV,aAAa,CAACvY,KAAK,CAAC0H,IAAI,EAAEuR,gBAAgB,CAAC;QACzD;MACF,CAAC;MAEDpZ,SAAS,CAACwE,SAAS,CAACuU,aAAa,GAAG,UAASxV,CAAC,EAAE;QAC9C,IAAItC,KAAK,GAAG,IAAI;QAChB,IAAIsC,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,EAAE;UAC1BtC,KAAK,GAAG,IAAI,CAACyX,aAAa,CAACvY,KAAK,CAACmH,UAAU,EAAE/D,CAAC,CAAC;QACjD,CAAC,MAAM,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,EAAE;UACjCtC,KAAK,GAAG,IAAI,CAACyX,aAAa,CAACvY,KAAK,CAACqH,QAAQ,EAAEjE,CAAC,CAAC;QAC/C,CAAC,MAAM,IAAIA,CAAC,KAAK,GAAG,EAAE;UACpBtC,KAAK,GAAG,IAAI,CAACyX,aAAa,CAACvY,KAAK,CAAC+E,WAAW,EAAE3B,CAAC,CAAC;QAClD,CAAC,MAAM,IAAIA,CAAC,KAAK,GAAG,EAAE;UACpBtC,KAAK,GAAG,IAAI,CAACyX,aAAa,CAACvY,KAAK,CAACwH,SAAS,EAAEpE,CAAC,CAAC;QAChD,CAAC,MAAM,IAAIA,CAAC,KAAK,GAAG,EAAE;UACpBtC,KAAK,GAAG,IAAI,CAACyX,aAAa,CAACvY,KAAK,CAAC4H,SAAS,EAAExE,CAAC,CAAC;QAChD,CAAC,MAAM,IAAIA,CAAC,KAAK,GAAG,IAAI6S,WAAW,CAACpP,IAAI,CAAC,IAAI,CAAC+P,MAAM,CAACtM,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;UAC7DxJ,KAAK,GAAG,IAAI,CAACyX,aAAa,CAACvY,KAAK,CAAC0I,GAAG,EAAEtF,CAAC,CAAC;QAC1C,CAAC,MAAM,IAAIA,CAAC,KAAK,GAAG,EAAE;UACpBtC,KAAK,GAAG,IAAI,CAACyX,aAAa,CAACvY,KAAK,CAACoI,KAAK,EAAEhF,CAAC,CAAC;QAC5C;QAEA,IAAItC,KAAK,EAAE;UACT,IAAI,CAAC8V,MAAM,CAAC7P,IAAI,CAAC,CAAC;QACpB;QACA,OAAOjG,KAAK;MACd,CAAC;MAEDjB,SAAS,CAACwE,SAAS,CAACqU,UAAU,GAAG,UAAStV,CAAC,EAAE+V,CAAC,EAAE;QAC9C,IAAIrY,KAAK,GAAG,IAAI;QAChB,IAAIsC,CAAC,KAAK,GAAG,IAAI+V,CAAC,KAAK,GAAG,EAAE;UAC1BrY,KAAK,GAAG,IAAI,CAACyX,aAAa,CAACvY,KAAK,CAAC+E,WAAW,EAAE3B,CAAC,GAAG+V,CAAC,CAAC;QACtD;QAEA,IAAIrY,KAAK,EAAE;UACT,IAAI,CAAC8V,MAAM,CAAC7P,IAAI,CAAC,CAAC;UAClB,IAAI,CAAC6P,MAAM,CAAC7P,IAAI,CAAC,CAAC;QACpB;QACA,OAAOjG,KAAK;MACd,CAAC;MAEDjB,SAAS,CAACwE,SAAS,CAAC2U,iBAAiB,GAAG,YAAW;QACjD,IAAIC,gBAAgB,GAAG,IAAI,CAAClC,UAAU,CAACb,KAAK,CAACgD,IAAI,CAAC,CAAC;QAEnD,IAAID,gBAAgB,KAAK,EAAE,EAAE;UAC3B,IAAIA,gBAAgB,KAAK,GAAG,EAAE;YAC5B,OAAO,IAAI,CAACV,aAAa,CAACvY,KAAK,CAACgI,MAAM,EAAEiR,gBAAgB,CAAC;UAC3D,CAAC,MAAM,IAAIA,gBAAgB,KAAK,IAAI,EAAE;YACpC,OAAO,IAAI,CAACV,aAAa,CAACvY,KAAK,CAAC0I,GAAG,EAAEuQ,gBAAgB,CAAC;UACxD,CAAC,MAAM;YACL,OAAO,IAAI,CAACV,aAAa,CAACvY,KAAK,CAACkI,QAAQ,EAAE+Q,gBAAgB,CAAC;UAC7D;QACF;MACF,CAAC;MAEDpZ,SAAS,CAACwE,SAAS,CAACmU,oBAAoB,GAAG,UAASpV,CAAC,EAAE;QACrD,IAAI6V,gBAAgB,GAAG,EAAE;QAEzB,IAAI7V,CAAC,KAAK,GAAG,EAAE;UACb,IAAI,IAAI,CAACgW,eAAe,CAAC,CAAC,EAAE;YAC1BH,gBAAgB,GAAG,IAAI,CAAClC,UAAU,CAACW,OAAO,CAACwB,IAAI,CAAC,CAAC;YAEjD,IAAID,gBAAgB,EAAE;cACpB,OAAO,IAAI,CAACV,aAAa,CAACvY,KAAK,CAAC8I,OAAO,EAAEmQ,gBAAgB,CAAC1V,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;YAC1E;UACF;;UAEA;UACA0V,gBAAgB,GAAG,IAAI,CAAClC,UAAU,CAACU,OAAO,CAACyB,IAAI,CAAC,CAAC;UAEjD,IAAID,gBAAgB,EAAE;YACpB,OAAO,IAAI,CAACV,aAAa,CAACvY,KAAK,CAAC8I,OAAO,EAAEmQ,gBAAgB,CAAC1V,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;UAC1E;UAEAH,CAAC,GAAG,IAAI,CAACwT,MAAM,CAAC7P,IAAI,CAAC,CAAC;;UAEtB;UACA,IAAIsS,KAAK,GAAG,GAAG;UACf,IAAI,IAAI,CAACzC,MAAM,CAAC0C,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC1C,MAAM,CAAC2C,QAAQ,CAACvD,KAAK,CAAC,EAAE;YACxD,GAAG;cACD5S,CAAC,GAAG,IAAI,CAACwT,MAAM,CAAC7P,IAAI,CAAC,CAAC;cACtBsS,KAAK,IAAIjW,CAAC;YACZ,CAAC,QAAQ,IAAI,CAACwT,MAAM,CAAC0C,OAAO,CAAC,CAAC,IAAIlW,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG;YACxD,IAAIA,CAAC,KAAK,GAAG,EAAE;cACb;YAAA,CACD,MAAM,IAAI,IAAI,CAACwT,MAAM,CAACtM,IAAI,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAACsM,MAAM,CAACtM,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;cACpE+O,KAAK,IAAI,IAAI;cACb,IAAI,CAACzC,MAAM,CAAC7P,IAAI,CAAC,CAAC;cAClB,IAAI,CAAC6P,MAAM,CAAC7P,IAAI,CAAC,CAAC;YACpB,CAAC,MAAM,IAAI,IAAI,CAAC6P,MAAM,CAACtM,IAAI,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,CAACsM,MAAM,CAACtM,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;cACpE+O,KAAK,IAAI,IAAI;cACb,IAAI,CAACzC,MAAM,CAAC7P,IAAI,CAAC,CAAC;cAClB,IAAI,CAAC6P,MAAM,CAAC7P,IAAI,CAAC,CAAC;YACpB;YACA,OAAO,IAAI,CAACwR,aAAa,CAACvY,KAAK,CAAC0H,IAAI,EAAE2R,KAAK,CAAC;UAC9C;UAEA,IAAI,CAACzC,MAAM,CAAC4C,IAAI,CAAC,CAAC;QAEpB,CAAC,MAAM,IAAIpW,CAAC,KAAK,GAAG,IAAI,IAAI,CAACgW,eAAe,CAAC,CAAC,EAAE;UAC9CH,gBAAgB,GAAG,IAAI,CAAClC,UAAU,CAACQ,kBAAkB,CAAC2B,IAAI,CAAC,CAAC;UAC5D,IAAID,gBAAgB,EAAE;YACpB,OAAO,IAAI,CAACrC,MAAM,CAAC0C,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC1C,MAAM,CAAC2C,QAAQ,CAAC3Z,KAAK,CAACkO,OAAO,CAAC,EAAE;cACpEmL,gBAAgB,IAAI,IAAI,CAACrC,MAAM,CAAC7P,IAAI,CAAC,CAAC;YACxC;YACAuP,eAAe,GAAG,IAAI;YACtB,OAAO,IAAI,CAACiC,aAAa,CAACvY,KAAK,CAACwI,OAAO,EAAEyQ,gBAAgB,CAAC;UAC5D;QACF,CAAC,MAAM,IAAI3C,eAAe,IAAIlT,CAAC,KAAK,GAAG,EAAE;UACvC6V,gBAAgB,GAAG,IAAI,CAAClC,UAAU,CAACS,gBAAgB,CAAC0B,IAAI,CAAC,CAAC;UAC1D,IAAID,gBAAgB,EAAE;YACpB3C,eAAe,GAAG,KAAK;YACvB,OAAO,IAAI,CAACiC,aAAa,CAACvY,KAAK,CAACwI,OAAO,EAAEyQ,gBAAgB,CAAC;UAC5D;QACF;QAEA,OAAO,IAAI;MACb,CAAC;MAEDpZ,SAAS,CAACwE,SAAS,CAACwU,aAAa,GAAG,UAASzV,CAAC,EAAE;QAC9C,IAAItC,KAAK,GAAG,IAAI;QAChB,IAAIsC,CAAC,KAAK,GAAG,EAAE;UACb,IAAI+T,OAAO,GAAG,EAAE;UAChB,IAAI,IAAI,CAACP,MAAM,CAACtM,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAC/B;YACA6M,OAAO,GAAG,IAAI,CAACJ,UAAU,CAACM,aAAa,CAAC6B,IAAI,CAAC,CAAC;YAC9C,IAAItL,UAAU,GAAGkI,eAAe,CAAC2D,cAAc,CAACtC,OAAO,CAAC;YACxD,IAAIvJ,UAAU,IAAIA,UAAU,CAAC8L,MAAM,KAAK,OAAO,EAAE;cAC/CvC,OAAO,IAAIrB,eAAe,CAAC6D,WAAW,CAAC,IAAI,CAAC/C,MAAM,CAAC;YACrD;YACAO,OAAO,GAAGA,OAAO,CAAC5W,OAAO,CAACX,KAAK,CAAC+C,aAAa,EAAE,IAAI,CAAC;YACpD7B,KAAK,GAAG,IAAI,CAACyX,aAAa,CAACvY,KAAK,CAACsI,aAAa,EAAE6O,OAAO,CAAC;YACxDrW,KAAK,CAAC8M,UAAU,GAAGA,UAAU;UAC/B,CAAC,MAAM,IAAI,IAAI,CAACgJ,MAAM,CAACtM,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACtC;YACA6M,OAAO,GAAG,IAAI,CAACJ,UAAU,CAACI,OAAO,CAAC+B,IAAI,CAAC,CAAC;YACxCpY,KAAK,GAAG,IAAI,CAACyX,aAAa,CAACvY,KAAK,CAACwI,OAAO,EAAE2O,OAAO,CAAC;UACpD;QACF;QACA,OAAOrW,KAAK;MACd,CAAC;MAEDjB,SAAS,CAACwE,SAAS,CAACoU,YAAY,GAAG,UAASrV,CAAC,EAAE;QAC7C,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,EAAE;UACvC,IAAI6V,gBAAgB,GAAG,IAAI,CAACrC,MAAM,CAAC7P,IAAI,CAAC,CAAC;UACzC,IAAI,CAAC6S,gBAAgB,GAAG,KAAK;UAE7B,IAAIxW,CAAC,KAAK,GAAG,EAAE;YACb6V,gBAAgB,IAAI,IAAI,CAACY,sBAAsB,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;UAClE,CAAC,MAAM;YACLZ,gBAAgB,IAAI,IAAI,CAACY,sBAAsB,CAACzW,CAAC,CAAC;UACpD;UAEA,IAAI,IAAI,CAACwW,gBAAgB,IAAI,IAAI,CAACxV,QAAQ,CAACqP,gBAAgB,EAAE;YAC3DwF,gBAAgB,GAAGa,eAAe,CAACb,gBAAgB,CAAC;UACtD;UAEA,IAAI,IAAI,CAACrC,MAAM,CAACtM,IAAI,CAAC,CAAC,KAAKlH,CAAC,EAAE;YAC5B6V,gBAAgB,IAAI,IAAI,CAACrC,MAAM,CAAC7P,IAAI,CAAC,CAAC;UACxC;UAEAkS,gBAAgB,GAAGA,gBAAgB,CAAC1Y,OAAO,CAACX,KAAK,CAAC+C,aAAa,EAAE,IAAI,CAAC;UAEtE,OAAO,IAAI,CAAC4V,aAAa,CAACvY,KAAK,CAAC8H,MAAM,EAAEmR,gBAAgB,CAAC;QAC3D;QAEA,OAAO,IAAI;MACb,CAAC;MAEDpZ,SAAS,CAACwE,SAAS,CAAC0V,oBAAoB,GAAG,UAAS1B,cAAc,EAAE;QAClE;QACA,OAAQA,cAAc,CAACrX,IAAI,KAAKhB,KAAK,CAACiB,QAAQ,IAAIhB,QAAQ,CAACoY,cAAc,CAACnX,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,IAC1ImX,cAAc,CAACrX,IAAI,KAAKhB,KAAK,CAACqH,QAAQ,IAAIgR,cAAc,CAACnX,IAAI,KAAK,GAAG,IACpEmX,cAAc,CAACzL,MAAM,CAAC3B,QAAQ,CAACjK,IAAI,KAAKhB,KAAK,CAACiB,QAAQ,IAAIhB,QAAQ,CAACoY,cAAc,CAACzL,MAAM,CAAC3B,QAAQ,CAAC/J,IAAI,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAE,IACjIjB,QAAQ,CAACoY,cAAc,CAACrX,IAAI,EAAE,CAAChB,KAAK,CAACwI,OAAO,EAAExI,KAAK,CAACmH,UAAU,EAAEnH,KAAK,CAAC+E,WAAW,EAAE/E,KAAK,CAAC4V,KAAK,EAC7F5V,KAAK,CAACwH,SAAS,EAAExH,KAAK,CAACkI,QAAQ,EAAElI,KAAK,CAACgI,MAAM,EAAEhI,KAAK,CAAC4I,GAAG,EAAE5I,KAAK,CAAC4H,SAAS,EAAE5H,KAAK,CAACoI,KAAK,CACvF,CAAE;MACP,CAAC;MAEDvI,SAAS,CAACwE,SAAS,CAACyU,YAAY,GAAG,UAAS1V,CAAC,EAAEiV,cAAc,EAAE;QAE7D,IAAIjV,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC2W,oBAAoB,CAAC1B,cAAc,CAAC,EAAE;UAC1D;UACA;UACA,IAAIY,gBAAgB,GAAG,IAAI,CAACrC,MAAM,CAAC7P,IAAI,CAAC,CAAC;UACzC,IAAIiT,GAAG,GAAG,KAAK;UAEf,IAAIC,aAAa,GAAG,KAAK;UACzB,OAAO,IAAI,CAACrD,MAAM,CAAC0C,OAAO,CAAC,CAAC,IACzB,CAACU,GAAG,IAAIC,aAAa,IAAI,IAAI,CAACrD,MAAM,CAACtM,IAAI,CAAC,CAAC,KAAKlH,CAAC,KAChD,CAAC,IAAI,CAACwT,MAAM,CAAC2C,QAAQ,CAAC3Z,KAAK,CAACkO,OAAO,CAAE,EAAE;YACzCmL,gBAAgB,IAAI,IAAI,CAACrC,MAAM,CAACtM,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC0P,GAAG,EAAE;cACRA,GAAG,GAAG,IAAI,CAACpD,MAAM,CAACtM,IAAI,CAAC,CAAC,KAAK,IAAI;cACjC,IAAI,IAAI,CAACsM,MAAM,CAACtM,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;gBAC9B2P,aAAa,GAAG,IAAI;cACtB,CAAC,MAAM,IAAI,IAAI,CAACrD,MAAM,CAACtM,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;gBACrC2P,aAAa,GAAG,KAAK;cACvB;YACF,CAAC,MAAM;cACLD,GAAG,GAAG,KAAK;YACb;YACA,IAAI,CAACpD,MAAM,CAAC7P,IAAI,CAAC,CAAC;UACpB;UAEA,IAAI,IAAI,CAAC6P,MAAM,CAACtM,IAAI,CAAC,CAAC,KAAKlH,CAAC,EAAE;YAC5B6V,gBAAgB,IAAI,IAAI,CAACrC,MAAM,CAAC7P,IAAI,CAAC,CAAC;;YAEtC;YACA;YACAkS,gBAAgB,IAAI,IAAI,CAACrC,MAAM,CAACsC,IAAI,CAACtZ,KAAK,CAAC+S,UAAU,CAAC;UACxD;UACA,OAAO,IAAI,CAAC4F,aAAa,CAACvY,KAAK,CAAC8H,MAAM,EAAEmR,gBAAgB,CAAC;QAC3D;QACA,OAAO,IAAI;MACb,CAAC;MAEDpZ,SAAS,CAACwE,SAAS,CAAC0U,SAAS,GAAG,UAAS3V,CAAC,EAAEiV,cAAc,EAAE;QAE1D,IAAI,IAAI,CAACjU,QAAQ,CAACsP,GAAG,IAAItQ,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC2W,oBAAoB,CAAC1B,cAAc,CAAC,EAAE;UAC/E,IAAI6B,MAAM,GAAG,EAAE;UACf,IAAI/T,KAAK,GAAG,IAAI,CAAC4Q,UAAU,CAACY,GAAG,CAACwC,UAAU,CAAC,CAAC;UAC5C;UACA;UACA,IAAIhU,KAAK,EAAE;YACT;YACA,IAAIiU,OAAO,GAAGjU,KAAK,CAAC,CAAC,CAAC,CAAC5F,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;YAClE,IAAI8Z,WAAW,GAAGD,OAAO,CAACha,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;YAC5C,IAAIka,KAAK,GAAG,CAAC;YACb,OAAOnU,KAAK,EAAE;cACZ,IAAIoU,QAAQ,GAAG,CAAC,CAACpU,KAAK,CAAC,CAAC,CAAC;cACzB,IAAIqU,OAAO,GAAGrU,KAAK,CAAC,CAAC,CAAC;cACtB,IAAIsU,cAAc,GAAI,CAAC,CAACtU,KAAK,CAACA,KAAK,CAACvF,MAAM,GAAG,CAAC,CAAC,IAAM4Z,OAAO,CAACpM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,UAAW;cACxF,IAAI,CAACqM,cAAc,KAChBD,OAAO,KAAKJ,OAAO,IAAKC,WAAW,IAAIG,OAAO,CAACja,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAE,CAAC,EAAE;gBAC/F,IAAIga,QAAQ,EAAE;kBACZ,EAAED,KAAK;gBACT,CAAC,MAAM;kBACL,EAAEA,KAAK;gBACT;cACF;cACAJ,MAAM,IAAI/T,KAAK,CAAC,CAAC,CAAC;cAClB,IAAImU,KAAK,IAAI,CAAC,EAAE;gBACd;cACF;cACAnU,KAAK,GAAG,IAAI,CAAC4Q,UAAU,CAACY,GAAG,CAACwC,UAAU,CAAC,CAAC;YAC1C;YACA;YACA,IAAI,CAAChU,KAAK,EAAE;cACV+T,MAAM,IAAI,IAAI,CAACtD,MAAM,CAACzQ,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YAC5C;YACA+T,MAAM,GAAGA,MAAM,CAAC3Z,OAAO,CAACX,KAAK,CAAC+C,aAAa,EAAE,IAAI,CAAC;YAClD,OAAO,IAAI,CAAC4V,aAAa,CAACvY,KAAK,CAAC8H,MAAM,EAAEoS,MAAM,CAAC;UACjD;QACF;QAEA,OAAO,IAAI;MACb,CAAC;MAED,SAASJ,eAAeA,CAACxZ,CAAC,EAAE;QAC1B;QACA;QACA;QACA;QACA;QACA,IAAIsC,GAAG,GAAG,EAAE;UACV8X,OAAO,GAAG,CAAC;QAEb,IAAIC,UAAU,GAAG,IAAIrF,YAAY,CAAChV,CAAC,CAAC;QACpC,IAAIsa,OAAO,GAAG,IAAI;QAElB,OAAOD,UAAU,CAACrB,OAAO,CAAC,CAAC,EAAE;UAC3B;UACA;UACAsB,OAAO,GAAGD,UAAU,CAACxU,KAAK,CAAC,qBAAqB,CAAC;UAEjD,IAAIyU,OAAO,EAAE;YACXhY,GAAG,IAAIgY,OAAO,CAAC,CAAC,CAAC;UACnB;UAEA,IAAID,UAAU,CAACrQ,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE;YAC9BqQ,UAAU,CAAC5T,IAAI,CAAC,CAAC;YACjB,IAAI4T,UAAU,CAACrQ,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;cAC7BsQ,OAAO,GAAGD,UAAU,CAACxU,KAAK,CAAC,oBAAoB,CAAC;YAClD,CAAC,MAAM,IAAIwU,UAAU,CAACrQ,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;cACpCsQ,OAAO,GAAGD,UAAU,CAACxU,KAAK,CAAC,oBAAoB,CAAC;cAChD,IAAI,CAACyU,OAAO,EAAE;gBACZA,OAAO,GAAGD,UAAU,CAACxU,KAAK,CAAC,sBAAsB,CAAC;cACpD;YACF,CAAC,MAAM;cACLvD,GAAG,IAAI,IAAI;cACX,IAAI+X,UAAU,CAACrB,OAAO,CAAC,CAAC,EAAE;gBACxB1W,GAAG,IAAI+X,UAAU,CAAC5T,IAAI,CAAC,CAAC;cAC1B;cACA;YACF;;YAEA;YACA,IAAI,CAAC6T,OAAO,EAAE;cACZ,OAAOta,CAAC;YACV;YAEAoa,OAAO,GAAGnG,QAAQ,CAACqG,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YAElC,IAAIF,OAAO,GAAG,IAAI,IAAIA,OAAO,IAAI,IAAI,IAAIE,OAAO,CAAC,CAAC,CAAC,CAACxa,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;cACtE;cACA;cACA;cACA,OAAOE,CAAC;YACV,CAAC,MAAM,IAAIoa,OAAO,IAAI,IAAI,IAAIA,OAAO,GAAG,IAAI,EAAE;cAC5C;cACA9X,GAAG,IAAI,IAAI,GAAGgY,OAAO,CAAC,CAAC,CAAC;YAC1B,CAAC,MAAM,IAAIF,OAAO,GAAG,QAAQ,EAAE;cAC7B;cACA9X,GAAG,IAAI,IAAI,GAAGgY,OAAO,CAAC,CAAC,CAAC;YAC1B,CAAC,MAAM,IAAIF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,IAAI,EAAE;cACnE;cACA9X,GAAG,IAAI,IAAI,GAAGiY,MAAM,CAACC,YAAY,CAACJ,OAAO,CAAC;YAC5C,CAAC,MAAM;cACL9X,GAAG,IAAIiY,MAAM,CAACC,YAAY,CAACJ,OAAO,CAAC;YACrC;UACF;QACF;QAEA,OAAO9X,GAAG;MACZ;;MAEA;MACA;MACA/C,SAAS,CAACwE,SAAS,CAACwV,sBAAsB,GAAG,UAASkB,SAAS,EAAEC,wBAAwB,EAAEC,SAAS,EAAE;QACpG,IAAIC,YAAY;QAChB,IAAI9L,OAAO;QACX,IAAI2L,SAAS,KAAK,IAAI,EAAE;UACtB3L,OAAO,GAAG,IAAI,CAAC2H,UAAU,CAACa,YAAY;QACxC,CAAC,MAAM,IAAImD,SAAS,KAAK,GAAG,EAAE;UAC5B3L,OAAO,GAAG,IAAI,CAAC2H,UAAU,CAACc,YAAY;QACxC,CAAC,MAAM,IAAIkD,SAAS,KAAK,GAAG,EAAE;UAC5B3L,OAAO,GAAG,IAAI,CAAC2H,UAAU,CAACe,aAAa;QACzC,CAAC,MAAM,IAAIiD,SAAS,KAAK,GAAG,EAAE;UAC5B3L,OAAO,GAAG,IAAI,CAAC2H,UAAU,CAACgB,mBAAmB;QAC/C;QAEA,IAAIkB,gBAAgB,GAAG7J,OAAO,CAAC8J,IAAI,CAAC,CAAC;QACrC,IAAInS,IAAI,GAAG,EAAE;QACb,OAAO,IAAI,CAAC6P,MAAM,CAAC0C,OAAO,CAAC,CAAC,EAAE;UAC5BvS,IAAI,GAAG,IAAI,CAAC6P,MAAM,CAAC7P,IAAI,CAAC,CAAC;UACzB,IAAIA,IAAI,KAAKgU,SAAS,IACnB,CAACC,wBAAwB,IAAIpb,KAAK,CAACkO,OAAO,CAACjH,IAAI,CAACE,IAAI,CAAE,EAAE;YACzD,IAAI,CAAC6P,MAAM,CAAC4C,IAAI,CAAC,CAAC;YAClB;UACF,CAAC,MAAM,IAAIzS,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC6P,MAAM,CAAC0C,OAAO,CAAC,CAAC,EAAE;YACjD4B,YAAY,GAAG,IAAI,CAACtE,MAAM,CAACtM,IAAI,CAAC,CAAC;YAEjC,IAAI4Q,YAAY,KAAK,GAAG,IAAIA,YAAY,KAAK,GAAG,EAAE;cAChD,IAAI,CAACtB,gBAAgB,GAAG,IAAI;YAC9B,CAAC,MAAM,IAAIsB,YAAY,KAAK,IAAI,IAAI,IAAI,CAACtE,MAAM,CAACtM,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;cAChE,IAAI,CAACsM,MAAM,CAAC7P,IAAI,CAAC,CAAC;YACpB;YACAA,IAAI,IAAI,IAAI,CAAC6P,MAAM,CAAC7P,IAAI,CAAC,CAAC;UAC5B,CAAC,MAAM,IAAIkU,SAAS,EAAE;YACpB,IAAIA,SAAS,KAAK,IAAI,IAAIlU,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC6P,MAAM,CAACtM,IAAI,CAAC,CAAC,KAAK,GAAG,EAAE;cACpEvD,IAAI,IAAI,IAAI,CAAC6P,MAAM,CAAC7P,IAAI,CAAC,CAAC;YAC5B;YAEA,IAAIkU,SAAS,KAAKlU,IAAI,EAAE;cACtB,IAAIgU,SAAS,KAAK,GAAG,EAAE;gBACrBhU,IAAI,IAAI,IAAI,CAAC8S,sBAAsB,CAAC,GAAG,EAAEmB,wBAAwB,EAAE,GAAG,CAAC;cACzE,CAAC,MAAM;gBACLjU,IAAI,IAAI,IAAI,CAAC8S,sBAAsB,CAAC,GAAG,EAAEmB,wBAAwB,EAAE,IAAI,CAAC;cAC1E;cACA,IAAI,IAAI,CAACpE,MAAM,CAAC0C,OAAO,CAAC,CAAC,EAAE;gBACzBvS,IAAI,IAAI,IAAI,CAAC6P,MAAM,CAAC7P,IAAI,CAAC,CAAC;cAC5B;YACF;UACF;UACAA,IAAI,IAAIqI,OAAO,CAAC8J,IAAI,CAAC,CAAC;UACtBD,gBAAgB,IAAIlS,IAAI;QAC1B;QAEA,OAAOkS,gBAAgB;MACzB,CAAC;MAEDna,MAAM,CAACU,OAAO,CAACK,SAAS,GAAGA,SAAS;MACpCf,MAAM,CAACU,OAAO,CAACQ,KAAK,GAAGA,KAAK;MAC5BlB,MAAM,CAACU,OAAO,CAACO,sBAAsB,GAAGA,sBAAsB,CAACqO,KAAK,CAAC,CAAC;MACtEtP,MAAM,CAACU,OAAO,CAACM,aAAa,GAAGA,aAAa,CAACsO,KAAK,CAAC,CAAC;;MAGpD;IAAM,CAAC,IACP;IACA,KAAO,UAAStP,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAIqc,iBAAiB,GAAGvI,MAAM,CAACvO,SAAS,CAAC+W,cAAc,CAAC,QAAQ,CAAC;MAEjE,SAAS9F,YAAYA,CAACiB,YAAY,EAAE;QAClC,IAAI,CAAC8E,OAAO,GAAG9E,YAAY,IAAI,EAAE;QACjC,IAAI,CAAC+E,cAAc,GAAG,IAAI,CAACD,OAAO,CAACza,MAAM;QACzC,IAAI,CAAC2a,UAAU,GAAG,CAAC;MACrB;MAEAjG,YAAY,CAACjR,SAAS,CAACmX,OAAO,GAAG,YAAW;QAC1C,IAAI,CAACD,UAAU,GAAG,CAAC;MACrB,CAAC;MAEDjG,YAAY,CAACjR,SAAS,CAACmV,IAAI,GAAG,YAAW;QACvC,IAAI,IAAI,CAAC+B,UAAU,GAAG,CAAC,EAAE;UACvB,IAAI,CAACA,UAAU,IAAI,CAAC;QACtB;MACF,CAAC;MAEDjG,YAAY,CAACjR,SAAS,CAACiV,OAAO,GAAG,YAAW;QAC1C,OAAO,IAAI,CAACiC,UAAU,GAAG,IAAI,CAACD,cAAc;MAC9C,CAAC;MAEDhG,YAAY,CAACjR,SAAS,CAAC0C,IAAI,GAAG,YAAW;QACvC,IAAI0U,GAAG,GAAG,IAAI;QACd,IAAI,IAAI,CAACnC,OAAO,CAAC,CAAC,EAAE;UAClBmC,GAAG,GAAG,IAAI,CAACJ,OAAO,CAAC7X,MAAM,CAAC,IAAI,CAAC+X,UAAU,CAAC;UAC1C,IAAI,CAACA,UAAU,IAAI,CAAC;QACtB;QACA,OAAOE,GAAG;MACZ,CAAC;MAEDnG,YAAY,CAACjR,SAAS,CAACiG,IAAI,GAAG,UAASoC,KAAK,EAAE;QAC5C,IAAI+O,GAAG,GAAG,IAAI;QACd/O,KAAK,GAAGA,KAAK,IAAI,CAAC;QAClBA,KAAK,IAAI,IAAI,CAAC6O,UAAU;QACxB,IAAI7O,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAAC4O,cAAc,EAAE;UAC7CG,GAAG,GAAG,IAAI,CAACJ,OAAO,CAAC7X,MAAM,CAACkJ,KAAK,CAAC;QAClC;QACA,OAAO+O,GAAG;MACZ,CAAC;;MAED;MACA;MACA;MACA;MACA;MACA;MACA;MACAnG,YAAY,CAACjR,SAAS,CAACqX,OAAO,GAAG,UAAStM,OAAO,EAAE1C,KAAK,EAAE;QACxD0C,OAAO,CAACuM,SAAS,GAAGjP,KAAK;QACzB,IAAIkP,aAAa,GAAGxM,OAAO,CAACyM,IAAI,CAAC,IAAI,CAACR,OAAO,CAAC;QAE9C,IAAIO,aAAa,IAAI,EAAET,iBAAiB,IAAI/L,OAAO,CAAC0M,MAAM,CAAC,EAAE;UAC3D,IAAIF,aAAa,CAAClP,KAAK,KAAKA,KAAK,EAAE;YACjCkP,aAAa,GAAG,IAAI;UACtB;QACF;QAEA,OAAOA,aAAa;MACtB,CAAC;MAEDtG,YAAY,CAACjR,SAAS,CAACwC,IAAI,GAAG,UAASuI,OAAO,EAAE1C,KAAK,EAAE;QACrDA,KAAK,GAAGA,KAAK,IAAI,CAAC;QAClBA,KAAK,IAAI,IAAI,CAAC6O,UAAU;QAExB,IAAI7O,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAAC4O,cAAc,EAAE;UAC7C,OAAO,CAAC,CAAC,IAAI,CAACI,OAAO,CAACtM,OAAO,EAAE1C,KAAK,CAAC;QACvC,CAAC,MAAM;UACL,OAAO,KAAK;QACd;MACF,CAAC;MAED4I,YAAY,CAACjR,SAAS,CAACkV,QAAQ,GAAG,UAASnK,OAAO,EAAE1C,KAAK,EAAE;QACzD;QACA,IAAI+O,GAAG,GAAG,IAAI,CAACnR,IAAI,CAACoC,KAAK,CAAC;QAC1B0C,OAAO,CAACuM,SAAS,GAAG,CAAC;QACrB,OAAOF,GAAG,KAAK,IAAI,IAAIrM,OAAO,CAACvI,IAAI,CAAC4U,GAAG,CAAC;MAC1C,CAAC;MAEDnG,YAAY,CAACjR,SAAS,CAAC8B,KAAK,GAAG,UAASiJ,OAAO,EAAE;QAC/C,IAAIwM,aAAa,GAAG,IAAI,CAACF,OAAO,CAACtM,OAAO,EAAE,IAAI,CAACmM,UAAU,CAAC;QAC1D,IAAIK,aAAa,EAAE;UACjB,IAAI,CAACL,UAAU,IAAIK,aAAa,CAAC,CAAC,CAAC,CAAChb,MAAM;QAC5C,CAAC,MAAM;UACLgb,aAAa,GAAG,IAAI;QACtB;QACA,OAAOA,aAAa;MACtB,CAAC;MAEDtG,YAAY,CAACjR,SAAS,CAAC6U,IAAI,GAAG,UAAS6C,gBAAgB,EAAEC,aAAa,EAAE1E,WAAW,EAAE;QACnF,IAAImE,GAAG,GAAG,EAAE;QACZ,IAAItV,KAAK;QACT,IAAI4V,gBAAgB,EAAE;UACpB5V,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC4V,gBAAgB,CAAC;UACpC,IAAI5V,KAAK,EAAE;YACTsV,GAAG,IAAItV,KAAK,CAAC,CAAC,CAAC;UACjB;QACF;QACA,IAAI6V,aAAa,KAAK7V,KAAK,IAAI,CAAC4V,gBAAgB,CAAC,EAAE;UACjDN,GAAG,IAAI,IAAI,CAACQ,SAAS,CAACD,aAAa,EAAE1E,WAAW,CAAC;QACnD;QACA,OAAOmE,GAAG;MACZ,CAAC;MAEDnG,YAAY,CAACjR,SAAS,CAAC4X,SAAS,GAAG,UAAS7M,OAAO,EAAEkI,WAAW,EAAE;QAChE,IAAImE,GAAG,GAAG,EAAE;QACZ,IAAIS,WAAW,GAAG,IAAI,CAACX,UAAU;QACjCnM,OAAO,CAACuM,SAAS,GAAG,IAAI,CAACJ,UAAU;QACnC,IAAIK,aAAa,GAAGxM,OAAO,CAACyM,IAAI,CAAC,IAAI,CAACR,OAAO,CAAC;QAC9C,IAAIO,aAAa,EAAE;UACjBM,WAAW,GAAGN,aAAa,CAAClP,KAAK;UACjC,IAAI4K,WAAW,EAAE;YACf4E,WAAW,IAAIN,aAAa,CAAC,CAAC,CAAC,CAAChb,MAAM;UACxC;QACF,CAAC,MAAM;UACLsb,WAAW,GAAG,IAAI,CAACZ,cAAc;QACnC;QAEAG,GAAG,GAAG,IAAI,CAACJ,OAAO,CAACtY,SAAS,CAAC,IAAI,CAACwY,UAAU,EAAEW,WAAW,CAAC;QAC1D,IAAI,CAACX,UAAU,GAAGW,WAAW;QAC7B,OAAOT,GAAG;MACZ,CAAC;MAEDnG,YAAY,CAACjR,SAAS,CAAC8X,cAAc,GAAG,UAAS/M,OAAO,EAAE;QACxD,OAAO,IAAI,CAAC6M,SAAS,CAAC7M,OAAO,EAAE,IAAI,CAAC;MACtC,CAAC;MAEDkG,YAAY,CAACjR,SAAS,CAAC+X,UAAU,GAAG,UAAShN,OAAO,EAAEiN,UAAU,EAAE;QAChE,IAAI3b,MAAM,GAAG,IAAI;QACjB,IAAI4b,KAAK,GAAG,GAAG;QACf,IAAID,UAAU,IAAIlB,iBAAiB,EAAE;UACnCmB,KAAK,GAAG,GAAG;QACb;QACA;QACA,IAAI,OAAOlN,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,EAAE,EAAE;UACjD;UACA1O,MAAM,GAAG,IAAIkS,MAAM,CAACxD,OAAO,EAAEkN,KAAK,CAAC;QACrC,CAAC,MAAM,IAAIlN,OAAO,EAAE;UAClB1O,MAAM,GAAG,IAAIkS,MAAM,CAACxD,OAAO,CAAC2D,MAAM,EAAEuJ,KAAK,CAAC;QAC5C;QACA,OAAO5b,MAAM;MACf,CAAC;MAED4U,YAAY,CAACjR,SAAS,CAACkY,kBAAkB,GAAG,UAASC,cAAc,EAAE;QACnE,OAAO5J,MAAM,CAAC4J,cAAc,CAACjc,OAAO,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;MACzE,CAAC;;MAED;MACA+U,YAAY,CAACjR,SAAS,CAACoY,cAAc,GAAG,UAASrN,OAAO,EAAE;QACxD,IAAIvD,KAAK,GAAG,IAAI,CAAC0P,UAAU;QAC3B,IAAIE,GAAG,GAAG,IAAI,CAACU,cAAc,CAAC/M,OAAO,CAAC;QACtC,IAAI,CAACmM,UAAU,GAAG1P,KAAK;QACvB,OAAO4P,GAAG;MACZ,CAAC;MAEDnG,YAAY,CAACjR,SAAS,CAACqY,QAAQ,GAAG,UAASC,OAAO,EAAE;QAClD,IAAI9Q,KAAK,GAAG,IAAI,CAAC0P,UAAU,GAAG,CAAC;QAC/B,OAAO1P,KAAK,IAAI8Q,OAAO,CAAC/b,MAAM,IAAI,IAAI,CAACya,OAAO,CAACtY,SAAS,CAAC8I,KAAK,GAAG8Q,OAAO,CAAC/b,MAAM,EAAEiL,KAAK,CAAC,CACpF+Q,WAAW,CAAC,CAAC,KAAKD,OAAO;MAC9B,CAAC;MAED7d,MAAM,CAACU,OAAO,CAAC8V,YAAY,GAAGA,YAAY;;MAG1C;IAAM,CAAC,IACP;IACA,KAAO,UAASxW,MAAM,EAAEC,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAIsW,YAAY,GAAItW,mBAAmB,CAAC,CAAC,CAAC,CAACsW,YAAa;MACxD,IAAI3V,KAAK,GAAIX,mBAAmB,CAAC,CAAC,CAAC,CAACW,KAAM;MAC1C,IAAIkd,WAAW,GAAI7d,mBAAmB,CAAC,EAAE,CAAC,CAAC6d,WAAY;MACvD,IAAIC,iBAAiB,GAAI9d,mBAAmB,CAAC,EAAE,CAAC,CAAC8d,iBAAkB;MAEnE,IAAI9c,KAAK,GAAG;QACV4V,KAAK,EAAE,UAAU;QACjBC,GAAG,EAAE,QAAQ;QACbjN,GAAG,EAAE;MACP,CAAC;MAED,IAAI/I,SAAS,GAAG,SAAAA,CAAS0W,YAAY,EAAElX,OAAO,EAAE;QAC9C,IAAI,CAACuX,MAAM,GAAG,IAAItB,YAAY,CAACiB,YAAY,CAAC;QAC5C,IAAI,CAACnS,QAAQ,GAAG/E,OAAO,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC0d,QAAQ,GAAG,IAAI;QAEpB,IAAI,CAACvG,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAACA,SAAS,CAACC,UAAU,GAAG,IAAIqG,iBAAiB,CAAC,IAAI,CAAClG,MAAM,CAAC;MAChE,CAAC;MAED/W,SAAS,CAACwE,SAAS,CAACmC,QAAQ,GAAG,YAAW;QACxC,IAAI,CAACoQ,MAAM,CAAC4E,OAAO,CAAC,CAAC;QACrB,IAAI,CAACuB,QAAQ,GAAG,IAAIF,WAAW,CAAC,CAAC;QAEjC,IAAI,CAAC5W,MAAM,CAAC,CAAC;QAEb,IAAI+W,OAAO;QACX,IAAI/R,QAAQ,GAAG,IAAItL,KAAK,CAACK,KAAK,CAAC4V,KAAK,EAAE,EAAE,CAAC;QACzC,IAAIuC,UAAU,GAAG,IAAI;QACrB,IAAI8E,UAAU,GAAG,EAAE;QACnB,IAAIC,QAAQ,GAAG,IAAIL,WAAW,CAAC,CAAC;QAEhC,OAAO5R,QAAQ,CAACjK,IAAI,KAAKhB,KAAK,CAAC4I,GAAG,EAAE;UAClCoU,OAAO,GAAG,IAAI,CAAC5E,eAAe,CAACnN,QAAQ,EAAEkN,UAAU,CAAC;UACpD,OAAO,IAAI,CAACH,WAAW,CAACgF,OAAO,CAAC,EAAE;YAChCE,QAAQ,CAACC,GAAG,CAACH,OAAO,CAAC;YACrBA,OAAO,GAAG,IAAI,CAAC5E,eAAe,CAACnN,QAAQ,EAAEkN,UAAU,CAAC;UACtD;UAEA,IAAI,CAAC+E,QAAQ,CAACE,OAAO,CAAC,CAAC,EAAE;YACvBJ,OAAO,CAAC5T,eAAe,GAAG8T,QAAQ;YAClCA,QAAQ,GAAG,IAAIL,WAAW,CAAC,CAAC;UAC9B;UAEAG,OAAO,CAACnY,MAAM,GAAGsT,UAAU;UAE3B,IAAI,IAAI,CAACF,WAAW,CAAC+E,OAAO,CAAC,EAAE;YAC7BC,UAAU,CAACna,IAAI,CAACqV,UAAU,CAAC;YAC3BA,UAAU,GAAG6E,OAAO;UACtB,CAAC,MAAM,IAAI7E,UAAU,IAAI,IAAI,CAACD,WAAW,CAAC8E,OAAO,EAAE7E,UAAU,CAAC,EAAE;YAC9D6E,OAAO,CAACpQ,MAAM,GAAGuL,UAAU;YAC3BA,UAAU,CAACjG,MAAM,GAAG8K,OAAO;YAC3B7E,UAAU,GAAG8E,UAAU,CAAC5R,GAAG,CAAC,CAAC;YAC7B2R,OAAO,CAACnY,MAAM,GAAGsT,UAAU;UAC7B;UAEA6E,OAAO,CAAC/R,QAAQ,GAAGA,QAAQ;UAC3BA,QAAQ,CAAClE,IAAI,GAAGiW,OAAO;UAEvB,IAAI,CAACD,QAAQ,CAACI,GAAG,CAACH,OAAO,CAAC;UAC1B/R,QAAQ,GAAG+R,OAAO;QACpB;QAEA,OAAO,IAAI,CAACD,QAAQ;MACtB,CAAC;MAGDld,SAAS,CAACwE,SAAS,CAAC+U,eAAe,GAAG,YAAW;QAC/C,OAAO,IAAI,CAAC2D,QAAQ,CAACK,OAAO,CAAC,CAAC;MAChC,CAAC;MAEDvd,SAAS,CAACwE,SAAS,CAAC4B,MAAM,GAAG,YAAW,CAAC,CAAC;MAE1CpG,SAAS,CAACwE,SAAS,CAAC+T,eAAe,GAAG,UAASC,cAAc,EAAEF,UAAU,EAAE;QAAE;QAC3E,IAAI,CAACG,eAAe,CAAC,CAAC;QACtB,IAAIW,gBAAgB,GAAG,IAAI,CAACrC,MAAM,CAACsC,IAAI,CAAC,KAAK,CAAC;QAC9C,IAAID,gBAAgB,EAAE;UACpB,OAAO,IAAI,CAACV,aAAa,CAACvY,KAAK,CAAC6V,GAAG,EAAEoD,gBAAgB,CAAC;QACxD,CAAC,MAAM;UACL,OAAO,IAAI,CAACV,aAAa,CAACvY,KAAK,CAAC4I,GAAG,EAAE,EAAE,CAAC;QAC1C;MACF,CAAC;MAED/I,SAAS,CAACwE,SAAS,CAAC2T,WAAW,GAAG,UAASlR,aAAa,EAAE;QAAE;QAC1D,OAAO,KAAK;MACd,CAAC;MAEDjH,SAAS,CAACwE,SAAS,CAAC4T,WAAW,GAAG,UAASnR,aAAa,EAAE;QAAE;QAC1D,OAAO,KAAK;MACd,CAAC;MAEDjH,SAAS,CAACwE,SAAS,CAAC6T,WAAW,GAAG,UAASpR,aAAa,EAAEqR,UAAU,EAAE;QAAE;QACtE,OAAO,KAAK;MACd,CAAC;MAEDtY,SAAS,CAACwE,SAAS,CAACkU,aAAa,GAAG,UAASvX,IAAI,EAAEE,IAAI,EAAE;QACvD,IAAIJ,KAAK,GAAG,IAAInB,KAAK,CAACqB,IAAI,EAAEE,IAAI,EAC9B,IAAI,CAACsV,SAAS,CAACC,UAAU,CAAC4G,aAAa,EACvC,IAAI,CAAC7G,SAAS,CAACC,UAAU,CAAC6G,uBAAuB,CAAC;QACpD,OAAOxc,KAAK;MACd,CAAC;MAEDjB,SAAS,CAACwE,SAAS,CAACiU,eAAe,GAAG,YAAW;QAC/C,OAAO,IAAI,CAAC9B,SAAS,CAACC,UAAU,CAACyC,IAAI,CAAC,CAAC;MACzC,CAAC;MAIDpa,MAAM,CAACU,OAAO,CAACK,SAAS,GAAGA,SAAS;MACpCf,MAAM,CAACU,OAAO,CAACQ,KAAK,GAAGA,KAAK;;MAG5B;IAAM,CAAC,IACP;IACA,KAAO,UAASlB,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,SAAS+d,WAAWA,CAACU,YAAY,EAAE;QACjC;QACA,IAAI,CAACR,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACS,eAAe,GAAG,IAAI,CAACT,QAAQ,CAACnc,MAAM;QAC3C,IAAI,CAAC2a,UAAU,GAAG,CAAC;QACnB,IAAI,CAACkC,cAAc,GAAGF,YAAY;MACpC;MAEAV,WAAW,CAACxY,SAAS,CAACmX,OAAO,GAAG,YAAW;QACzC,IAAI,CAACD,UAAU,GAAG,CAAC;MACrB,CAAC;MAEDsB,WAAW,CAACxY,SAAS,CAAC+Y,OAAO,GAAG,YAAW;QACzC,OAAO,IAAI,CAACI,eAAe,KAAK,CAAC;MACnC,CAAC;MAEDX,WAAW,CAACxY,SAAS,CAACiV,OAAO,GAAG,YAAW;QACzC,OAAO,IAAI,CAACiC,UAAU,GAAG,IAAI,CAACiC,eAAe;MAC/C,CAAC;MAEDX,WAAW,CAACxY,SAAS,CAAC0C,IAAI,GAAG,YAAW;QACtC,IAAI0U,GAAG,GAAG,IAAI;QACd,IAAI,IAAI,CAACnC,OAAO,CAAC,CAAC,EAAE;UAClBmC,GAAG,GAAG,IAAI,CAACsB,QAAQ,CAAC,IAAI,CAACxB,UAAU,CAAC;UACpC,IAAI,CAACA,UAAU,IAAI,CAAC;QACtB;QACA,OAAOE,GAAG;MACZ,CAAC;MAEDoB,WAAW,CAACxY,SAAS,CAACiG,IAAI,GAAG,UAASoC,KAAK,EAAE;QAC3C,IAAI+O,GAAG,GAAG,IAAI;QACd/O,KAAK,GAAGA,KAAK,IAAI,CAAC;QAClBA,KAAK,IAAI,IAAI,CAAC6O,UAAU;QACxB,IAAI7O,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAAC8Q,eAAe,EAAE;UAC9C/B,GAAG,GAAG,IAAI,CAACsB,QAAQ,CAACrQ,KAAK,CAAC;QAC5B;QACA,OAAO+O,GAAG;MACZ,CAAC;MAEDoB,WAAW,CAACxY,SAAS,CAAC8Y,GAAG,GAAG,UAASrc,KAAK,EAAE;QAC1C,IAAI,IAAI,CAAC2c,cAAc,EAAE;UACvB3c,KAAK,CAAC+D,MAAM,GAAG,IAAI,CAAC4Y,cAAc;QACpC;QACA,IAAI,CAACV,QAAQ,CAACja,IAAI,CAAChC,KAAK,CAAC;QACzB,IAAI,CAAC0c,eAAe,IAAI,CAAC;MAC3B,CAAC;MAED1e,MAAM,CAACU,OAAO,CAACqd,WAAW,GAAGA,WAAW;;MAGxC;IAAM,CAAC,IACP;IACA,KAAO,UAAS/d,MAAM,EAAEC,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAI0W,OAAO,GAAI1W,mBAAmB,CAAC,EAAE,CAAC,CAAC0W,OAAQ;MAE/C,SAASoH,iBAAiBA,CAACY,aAAa,EAAE7Y,MAAM,EAAE;QAChD6Q,OAAO,CAACzC,IAAI,CAAC,IAAI,EAAEyK,aAAa,EAAE7Y,MAAM,CAAC;QACzC,IAAIA,MAAM,EAAE;UACV,IAAI,CAAC8Y,YAAY,GAAG,IAAI,CAAC/G,MAAM,CAACwF,UAAU,CAACvX,MAAM,CAAC8Y,YAAY,CAAC;QACjE,CAAC,MAAM;UACL,IAAI,CAACC,yBAAyB,CAAC,EAAE,EAAE,EAAE,CAAC;QACxC;QAEA,IAAI,CAACP,aAAa,GAAG,CAAC;QACtB,IAAI,CAACC,uBAAuB,GAAG,EAAE;MACnC;MACAR,iBAAiB,CAACzY,SAAS,GAAG,IAAIqR,OAAO,CAAC,CAAC;MAE3CoH,iBAAiB,CAACzY,SAAS,CAACuZ,yBAAyB,GAAG,UAASC,gBAAgB,EAAEC,aAAa,EAAE;QAChGD,gBAAgB,IAAI,MAAM;QAC1BC,aAAa,IAAI,QAAQ;QAEzB,IAAI,CAACC,cAAc,GAAG,IAAI,CAACnH,MAAM,CAACwF,UAAU,CAC1C,GAAG,GAAGyB,gBAAgB,GAAGC,aAAa,GAAG,IAAI,EAAE,IAAI,CAAC;QACtD,IAAI,CAACE,eAAe,GAAG,IAAI,CAACpH,MAAM,CAACwF,UAAU,CAC3C,UAAU,GAAG0B,aAAa,GAAG,GAAG,CAAC;MACrC,CAAC;MAEDhB,iBAAiB,CAACzY,SAAS,CAAC6U,IAAI,GAAG,YAAW;QAC5C,IAAI,CAACmE,aAAa,GAAG,CAAC;QACtB,IAAI,CAACC,uBAAuB,GAAG,EAAE;QAEjC,IAAIrE,gBAAgB,GAAG,IAAI,CAACrC,MAAM,CAACsC,IAAI,CAAC,IAAI,CAAC6E,cAAc,CAAC;QAC5D,IAAI9E,gBAAgB,KAAK,GAAG,EAAE;UAC5B,IAAI,CAACqE,uBAAuB,GAAG,GAAG;QACpC,CAAC,MAAM,IAAIrE,gBAAgB,EAAE;UAC3B,IAAIgF,OAAO,GAAG,IAAI,CAACC,OAAO,CAAC,IAAI,CAACF,eAAe,EAAE/E,gBAAgB,CAAC;UAClE,IAAI,CAACoE,aAAa,GAAGY,OAAO,CAACrd,MAAM,GAAG,CAAC;UACvC,IAAI,CAAC0c,uBAAuB,GAAGW,OAAO,CAAC,IAAI,CAACZ,aAAa,CAAC;QAC5D;QAEA,OAAOpE,gBAAgB;MACzB,CAAC;MAED6D,iBAAiB,CAACzY,SAAS,CAACqS,QAAQ,GAAG,UAASmH,gBAAgB,EAAEC,aAAa,EAAE;QAC/E,IAAIpd,MAAM,GAAG,IAAI,CAACyd,OAAO,CAAC,CAAC;QAC3Bzd,MAAM,CAACkd,yBAAyB,CAACC,gBAAgB,EAAEC,aAAa,CAAC;QACjEpd,MAAM,CAAC0d,OAAO,CAAC,CAAC;QAChB,OAAO1d,MAAM;MACf,CAAC;MAEDoc,iBAAiB,CAACzY,SAAS,CAAC8Z,OAAO,GAAG,YAAW;QAC/C,OAAO,IAAIrB,iBAAiB,CAAC,IAAI,CAAClG,MAAM,EAAE,IAAI,CAAC;MACjD,CAAC;MAEDkG,iBAAiB,CAACzY,SAAS,CAAC6Z,OAAO,GAAG,UAASG,MAAM,EAAE9H,YAAY,EAAE;QACnE8H,MAAM,CAAC1C,SAAS,GAAG,CAAC;QACpB,IAAI2C,WAAW,GAAG,CAAC;QACnB,IAAI5d,MAAM,GAAG,EAAE;QACf,IAAI6d,UAAU,GAAGF,MAAM,CAACxC,IAAI,CAACtF,YAAY,CAAC;QAC1C,OAAOgI,UAAU,EAAE;UACjB7d,MAAM,CAACoC,IAAI,CAACyT,YAAY,CAACxT,SAAS,CAACub,WAAW,EAAEC,UAAU,CAAC7R,KAAK,CAAC,CAAC;UAClE4R,WAAW,GAAGC,UAAU,CAAC7R,KAAK,GAAG6R,UAAU,CAAC,CAAC,CAAC,CAAC3d,MAAM;UACrD2d,UAAU,GAAGF,MAAM,CAACxC,IAAI,CAACtF,YAAY,CAAC;QACxC;QAEA,IAAI+H,WAAW,GAAG/H,YAAY,CAAC3V,MAAM,EAAE;UACrCF,MAAM,CAACoC,IAAI,CAACyT,YAAY,CAACxT,SAAS,CAACub,WAAW,EAAE/H,YAAY,CAAC3V,MAAM,CAAC,CAAC;QACvE,CAAC,MAAM;UACLF,MAAM,CAACoC,IAAI,CAAC,EAAE,CAAC;QACjB;QAEA,OAAOpC,MAAM;MACf,CAAC;MAID5B,MAAM,CAACU,OAAO,CAACsd,iBAAiB,GAAGA,iBAAiB;;MAGpD;IAAM,CAAC,IACP;IACA,KAAO,UAAShe,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,SAAS4W,OAAOA,CAACgI,aAAa,EAAE7Y,MAAM,EAAE;QACtC,IAAI,CAAC+R,MAAM,GAAG8G,aAAa;QAC3B,IAAI,CAACc,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACT,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACU,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACC,YAAY,GAAG,KAAK;QAEzB,IAAI7Z,MAAM,EAAE;UACV,IAAI,CAAC2Z,iBAAiB,GAAG,IAAI,CAAC5H,MAAM,CAACwF,UAAU,CAACvX,MAAM,CAAC2Z,iBAAiB,EAAE,IAAI,CAAC;UAC/E,IAAI,CAACT,cAAc,GAAG,IAAI,CAACnH,MAAM,CAACwF,UAAU,CAACvX,MAAM,CAACkZ,cAAc,EAAE,IAAI,CAAC;UACzE,IAAI,CAACU,cAAc,GAAG,IAAI,CAAC7H,MAAM,CAACwF,UAAU,CAACvX,MAAM,CAAC4Z,cAAc,CAAC;UACnE,IAAI,CAACC,YAAY,GAAG7Z,MAAM,CAAC6Z,YAAY;QACzC;MACF;MAEAhJ,OAAO,CAACrR,SAAS,CAAC6U,IAAI,GAAG,YAAW;QAClC,IAAIxY,MAAM,GAAG,IAAI,CAACkW,MAAM,CAACsC,IAAI,CAAC,IAAI,CAACsF,iBAAiB,CAAC;QACrD,IAAI,CAAC,IAAI,CAACA,iBAAiB,IAAI9d,MAAM,EAAE;UACrCA,MAAM,IAAI,IAAI,CAACkW,MAAM,CAACsC,IAAI,CAAC,IAAI,CAAC6E,cAAc,EAAE,IAAI,CAACU,cAAc,EAAE,IAAI,CAACC,YAAY,CAAC;QACzF;QACA,OAAOhe,MAAM;MACf,CAAC;MAEDgV,OAAO,CAACrR,SAAS,CAAC8V,UAAU,GAAG,YAAW;QACxC,OAAO,IAAI,CAACvD,MAAM,CAACzQ,KAAK,CAAC,IAAI,CAAC4X,cAAc,CAAC;MAC/C,CAAC;MAEDrI,OAAO,CAACrR,SAAS,CAACiT,WAAW,GAAG,UAASlI,OAAO,EAAE;QAChD,IAAI1O,MAAM,GAAG,IAAI,CAACyd,OAAO,CAAC,CAAC;QAC3Bzd,MAAM,CAACge,YAAY,GAAG,IAAI;QAC1Bhe,MAAM,CAAC+d,cAAc,GAAG,IAAI,CAAC7H,MAAM,CAACwF,UAAU,CAAChN,OAAO,CAAC;QACvD1O,MAAM,CAAC0d,OAAO,CAAC,CAAC;QAChB,OAAO1d,MAAM;MACf,CAAC;MAEDgV,OAAO,CAACrR,SAAS,CAAC+S,KAAK,GAAG,UAAShI,OAAO,EAAE;QAC1C,IAAI1O,MAAM,GAAG,IAAI,CAACyd,OAAO,CAAC,CAAC;QAC3Bzd,MAAM,CAACge,YAAY,GAAG,KAAK;QAC3Bhe,MAAM,CAAC+d,cAAc,GAAG,IAAI,CAAC7H,MAAM,CAACwF,UAAU,CAAChN,OAAO,CAAC;QACvD1O,MAAM,CAAC0d,OAAO,CAAC,CAAC;QAChB,OAAO1d,MAAM;MACf,CAAC;MAEDgV,OAAO,CAACrR,SAAS,CAAC4S,aAAa,GAAG,UAAS7H,OAAO,EAAE;QAClD,IAAI1O,MAAM,GAAG,IAAI,CAACyd,OAAO,CAAC,CAAC;QAC3Bzd,MAAM,CAAC8d,iBAAiB,GAAG,IAAI,CAAC5H,MAAM,CAACwF,UAAU,CAAChN,OAAO,EAAE,IAAI,CAAC;QAChE1O,MAAM,CAAC0d,OAAO,CAAC,CAAC;QAChB,OAAO1d,MAAM;MACf,CAAC;MAEDgV,OAAO,CAACrR,SAAS,CAACqS,QAAQ,GAAG,UAAStH,OAAO,EAAE;QAC7C,IAAI1O,MAAM,GAAG,IAAI,CAACyd,OAAO,CAAC,CAAC;QAC3Bzd,MAAM,CAACqd,cAAc,GAAG,IAAI,CAACnH,MAAM,CAACwF,UAAU,CAAChN,OAAO,EAAE,IAAI,CAAC;QAC7D1O,MAAM,CAAC0d,OAAO,CAAC,CAAC;QAChB,OAAO1d,MAAM;MACf,CAAC;MAEDgV,OAAO,CAACrR,SAAS,CAAC8Z,OAAO,GAAG,YAAW;QACrC,OAAO,IAAIzI,OAAO,CAAC,IAAI,CAACkB,MAAM,EAAE,IAAI,CAAC;MACvC,CAAC;MAEDlB,OAAO,CAACrR,SAAS,CAAC+Z,OAAO,GAAG,YAAW,CAAC,CAAC;MAEzCtf,MAAM,CAACU,OAAO,CAACkW,OAAO,GAAGA,OAAO;;MAGhC;IAAM,CAAC,IACP;IACA,KAAO,UAAS5W,MAAM,EAAE;MAExB;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,SAAS2W,UAAUA,CAACkJ,mBAAmB,EAAEC,iBAAiB,EAAE;QAC1DD,mBAAmB,GAAG,OAAOA,mBAAmB,KAAK,QAAQ,GAAGA,mBAAmB,GAAGA,mBAAmB,CAAC5L,MAAM;QAChH6L,iBAAiB,GAAG,OAAOA,iBAAiB,KAAK,QAAQ,GAAGA,iBAAiB,GAAGA,iBAAiB,CAAC7L,MAAM;QACxG,IAAI,CAAC8L,0BAA0B,GAAG,IAAIjM,MAAM,CAAC+L,mBAAmB,GAAG,yBAAyB,CAAC5L,MAAM,GAAG6L,iBAAiB,EAAE,GAAG,CAAC;QAC7H,IAAI,CAACE,mBAAmB,GAAG,iBAAiB;QAE5C,IAAI,CAACC,+BAA+B,GAAG,IAAInM,MAAM,CAAC+L,mBAAmB,GAAG,0BAA0B,CAAC5L,MAAM,GAAG6L,iBAAiB,EAAE,GAAG,CAAC;MACrI;MAEAnJ,UAAU,CAACpR,SAAS,CAACoV,cAAc,GAAG,UAASvY,IAAI,EAAE;QACnD,IAAI,CAACA,IAAI,CAACiF,KAAK,CAAC,IAAI,CAAC0Y,0BAA0B,CAAC,EAAE;UAChD,OAAO,IAAI;QACb;QAEA,IAAIjR,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,CAACkR,mBAAmB,CAACnD,SAAS,GAAG,CAAC;QACtC,IAAIqD,eAAe,GAAG,IAAI,CAACF,mBAAmB,CAACjD,IAAI,CAAC3a,IAAI,CAAC;QAEzD,OAAO8d,eAAe,EAAE;UACtBpR,UAAU,CAACoR,eAAe,CAAC,CAAC,CAAC,CAAC,GAAGA,eAAe,CAAC,CAAC,CAAC;UACnDA,eAAe,GAAG,IAAI,CAACF,mBAAmB,CAACjD,IAAI,CAAC3a,IAAI,CAAC;QACvD;QAEA,OAAO0M,UAAU;MACnB,CAAC;MAED6H,UAAU,CAACpR,SAAS,CAACsV,WAAW,GAAG,UAASsF,KAAK,EAAE;QACjD,OAAOA,KAAK,CAAC9C,cAAc,CAAC,IAAI,CAAC4C,+BAA+B,CAAC;MACnE,CAAC;MAGDjgB,MAAM,CAACU,OAAO,CAACiW,UAAU,GAAGA,UAAU;;MAGtC;IAAM,CAAC,IACP;IACA,KAAO,UAAS3W,MAAM,EAAEC,wBAAwB,EAAEC,mBAAmB,EAAE;MAEvE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAIA,IAAI0W,OAAO,GAAI1W,mBAAmB,CAAC,EAAE,CAAC,CAAC0W,OAAQ;MAG/C,IAAIwJ,cAAc,GAAG;QACnBC,MAAM,EAAE,KAAK;QACbC,GAAG,EAAE,KAAK;QACVC,UAAU,EAAE,KAAK;QACjBC,GAAG,EAAE,KAAK;QACVC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;MACX,CAAC;;MAED;MACA;MACA,SAAS7J,kBAAkBA,CAAC+H,aAAa,EAAE7Y,MAAM,EAAE;QACjD6Q,OAAO,CAACzC,IAAI,CAAC,IAAI,EAAEyK,aAAa,EAAE7Y,MAAM,CAAC;QACzC,IAAI,CAAC4a,kBAAkB,GAAG,IAAI;QAC9B,IAAI,CAACC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEV,cAAc,CAAC;QAClD,IAAI,CAACW,SAAS,GAAGF,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEV,cAAc,CAAC;QAElD,IAAIra,MAAM,EAAE;UACV,IAAI,CAAC4a,kBAAkB,GAAG,IAAI,CAAC7I,MAAM,CAACwF,UAAU,CAACvX,MAAM,CAAC4a,kBAAkB,CAAC;UAC3E,IAAI,CAACI,SAAS,GAAGF,MAAM,CAACC,MAAM,CAAC,IAAI,CAACC,SAAS,EAAEhb,MAAM,CAACgb,SAAS,CAAC;UAChE,IAAI,CAACH,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACF,SAAS,EAAE7a,MAAM,CAAC6a,SAAS,CAAC;QAClE;QACA,IAAItQ,OAAO,GAAG,IAAIsG,OAAO,CAACgI,aAAa,CAAC;QACxC,IAAI,CAAC3G,UAAU,GAAG;UAChB+I,kBAAkB,EAAE1Q,OAAO,CAAC6H,aAAa,CAAC,OAAO,CAAC,CAACK,WAAW,CAAC,MAAM,CAAC;UACtEyI,oBAAoB,EAAE3Q,OAAO,CAAC6H,aAAa,CAAC,KAAK,CAAC,CAACK,WAAW,CAAC,KAAK,CAAC;UACrE+H,UAAU,EAAEjQ,OAAO,CAAC6H,aAAa,CAAC,IAAI,CAAC,CAACK,WAAW,CAAC,IAAI,CAAC;UACzDgI,GAAG,EAAElQ,OAAO,CAAC6H,aAAa,CAAC,iBAAiB,CAAC,CAACK,WAAW,CAAC,KAAK,CAAC;UAChE8H,GAAG,EAAEhQ,OAAO,CAAC6H,aAAa,CAAC,QAAQ,CAAC,CAACK,WAAW,CAAC,QAAQ,CAAC;UAC1D;UACA6H,MAAM,EAAE/P,OAAO,CAAC6H,aAAa,CAAC,IAAI,CAAC,CAACK,WAAW,CAAC,IAAI,CAAC;UACrD0I,YAAY,EAAE5Q,OAAO,CAAC6H,aAAa,CAAC,IAAI,CAAC,CAACK,WAAW,CAAC,IAAI,CAAC;UAC3D2I,cAAc,EAAE7Q,OAAO,CAAC6H,aAAa,CAAC,IAAI,CAAC,CAACK,WAAW,CAAC,IAAI,CAAC;UAC7DiI,MAAM,EAAEnQ,OAAO,CAAC6H,aAAa,CAAC,gBAAgB,CAAC,CAACK,WAAW,CAAC,UAAU,CAAC;UACvE4I,cAAc,EAAE9Q,OAAO,CAAC6H,aAAa,CAAC,KAAK,CAAC,CAACK,WAAW,CAAC,KAAK,CAAC;UAC/D6I,cAAc,EAAE/Q,OAAO,CAAC6H,aAAa,CAAC,WAAW,CAAC,CAACK,WAAW,CAAC,aAAa;QAC9E,CAAC;MACH;MACA3B,kBAAkB,CAACtR,SAAS,GAAG,IAAIqR,OAAO,CAAC,CAAC;MAE5CC,kBAAkB,CAACtR,SAAS,CAAC8Z,OAAO,GAAG,YAAW;QAChD,OAAO,IAAIxI,kBAAkB,CAAC,IAAI,CAACiB,MAAM,EAAE,IAAI,CAAC;MAClD,CAAC;MAEDjB,kBAAkB,CAACtR,SAAS,CAAC+Z,OAAO,GAAG,YAAW;QAChD,IAAI,CAACgC,uBAAuB,CAAC,CAAC;MAChC,CAAC;MAEDzK,kBAAkB,CAACtR,SAAS,CAACgc,OAAO,GAAG,UAASC,QAAQ,EAAE;QACxD,IAAI5f,MAAM,GAAG,IAAI,CAACyd,OAAO,CAAC,CAAC;QAC3Bzd,MAAM,CAACgf,SAAS,CAACY,QAAQ,CAAC,GAAG,IAAI;QACjC5f,MAAM,CAAC0d,OAAO,CAAC,CAAC;QAChB,OAAO1d,MAAM;MACf,CAAC;MAEDiV,kBAAkB,CAACtR,SAAS,CAACyS,YAAY,GAAG,UAASzX,OAAO,EAAE;QAC5D,IAAIqB,MAAM,GAAG,IAAI,CAACyd,OAAO,CAAC,CAAC;QAC3B,KAAK,IAAImC,QAAQ,IAAIpB,cAAc,EAAE;UACnCxe,MAAM,CAACgf,SAAS,CAACY,QAAQ,CAAC,GAAGjhB,OAAO,CAAC2U,UAAU,CAAC5T,OAAO,CAACkgB,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC1E;QACA5f,MAAM,CAAC0d,OAAO,CAAC,CAAC;QAChB,OAAO1d,MAAM;MACf,CAAC;MAEDiV,kBAAkB,CAACtR,SAAS,CAACkc,OAAO,GAAG,UAASD,QAAQ,EAAE;QACxD,IAAI5f,MAAM,GAAG,IAAI,CAACyd,OAAO,CAAC,CAAC;QAC3Bzd,MAAM,CAACmf,SAAS,CAACS,QAAQ,CAAC,GAAG,IAAI;QACjC5f,MAAM,CAAC0d,OAAO,CAAC,CAAC;QAChB,OAAO1d,MAAM;MACf,CAAC;MAEDiV,kBAAkB,CAACtR,SAAS,CAAC6U,IAAI,GAAG,YAAW;QAC7C,IAAIxY,MAAM,GAAG,EAAE;QACf,IAAI,IAAI,CAACqd,cAAc,EAAE;UACvBrd,MAAM,GAAG,IAAI,CAACkW,MAAM,CAACsC,IAAI,CAAC,IAAI,CAACsF,iBAAiB,CAAC;QACnD,CAAC,MAAM;UACL9d,MAAM,GAAG,IAAI,CAACkW,MAAM,CAACsC,IAAI,CAAC,IAAI,CAACsF,iBAAiB,EAAE,IAAI,CAACiB,kBAAkB,CAAC;QAC5E;QACA,IAAI1Y,IAAI,GAAG,IAAI,CAACyZ,cAAc,CAAC,CAAC;QAChC,OAAOzZ,IAAI,EAAE;UACX,IAAI,IAAI,CAACgX,cAAc,EAAE;YACvBhX,IAAI,IAAI,IAAI,CAAC6P,MAAM,CAACsC,IAAI,CAAC,IAAI,CAAC6E,cAAc,CAAC;UAC/C,CAAC,MAAM;YACLhX,IAAI,IAAI,IAAI,CAAC6P,MAAM,CAACqF,SAAS,CAAC,IAAI,CAACwD,kBAAkB,CAAC;UACxD;UACA/e,MAAM,IAAIqG,IAAI;UACdA,IAAI,GAAG,IAAI,CAACyZ,cAAc,CAAC,CAAC;QAC9B;QAEA,IAAI,IAAI,CAAC9B,YAAY,EAAE;UACrBhe,MAAM,IAAI,IAAI,CAACkW,MAAM,CAACuF,cAAc,CAAC,IAAI,CAACsC,cAAc,CAAC;QAC3D;QACA,OAAO/d,MAAM;MACf,CAAC;MAEDiV,kBAAkB,CAACtR,SAAS,CAAC+b,uBAAuB,GAAG,YAAW;QAChE,IAAIK,KAAK,GAAG,EAAE;QAEd,IAAI,CAAC,IAAI,CAACf,SAAS,CAACJ,GAAG,EAAE;UACvBmB,KAAK,CAAC3d,IAAI,CAAC,IAAI,CAACiU,UAAU,CAACuI,GAAG,CAACd,iBAAiB,CAACzL,MAAM,CAAC;QAC1D;QACA,IAAI,CAAC,IAAI,CAAC2M,SAAS,CAACL,UAAU,EAAE;UAC9BoB,KAAK,CAAC3d,IAAI,CAAC,IAAI,CAACiU,UAAU,CAACsI,UAAU,CAACb,iBAAiB,CAACzL,MAAM,CAAC;QACjE;QACA,IAAI,CAAC,IAAI,CAAC2M,SAAS,CAACF,OAAO,EAAE;UAC3B;UACAiB,KAAK,CAAC3d,IAAI,CAAC,IAAI,CAACiU,UAAU,CAACsI,UAAU,CAACb,iBAAiB,CAACzL,MAAM,CAAC;QACjE;QACA,IAAI,CAAC,IAAI,CAAC2M,SAAS,CAACN,GAAG,EAAE;UACvBqB,KAAK,CAAC3d,IAAI,CAAC,IAAI,CAACiU,UAAU,CAACqI,GAAG,CAACZ,iBAAiB,CAACzL,MAAM,CAAC;QAC1D;QACA,IAAI,CAAC,IAAI,CAAC2M,SAAS,CAACP,MAAM,EAAE;UAC1BsB,KAAK,CAAC3d,IAAI,CAAC,IAAI,CAACiU,UAAU,CAACoI,MAAM,CAACX,iBAAiB,CAACzL,MAAM,CAAC;UAC3D;UACA;UACA0N,KAAK,CAAC3d,IAAI,CAAC,IAAI,CAACiU,UAAU,CAACiJ,YAAY,CAACxB,iBAAiB,CAACzL,MAAM,CAAC;UACjE0N,KAAK,CAAC3d,IAAI,CAAC,IAAI,CAACiU,UAAU,CAACkJ,cAAc,CAACzB,iBAAiB,CAACzL,MAAM,CAAC;QACrE;QACA,IAAI,CAAC,IAAI,CAAC2M,SAAS,CAACH,MAAM,EAAE;UAC1BkB,KAAK,CAAC3d,IAAI,CAAC,IAAI,CAACiU,UAAU,CAACwI,MAAM,CAACf,iBAAiB,CAACzL,MAAM,CAAC;QAC7D;QAEA,IAAI,IAAI,CAAC0L,cAAc,EAAE;UACvBgC,KAAK,CAAC3d,IAAI,CAAC,IAAI,CAAC2b,cAAc,CAAC1L,MAAM,CAAC;QACxC;QACA,IAAI,CAAC0M,kBAAkB,GAAG,IAAI,CAAC7I,MAAM,CAACwF,UAAU,CAAC,KAAK,GAAGqE,KAAK,CAACrQ,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;MACjF,CAAC;MAEDuF,kBAAkB,CAACtR,SAAS,CAACmc,cAAc,GAAG,YAAW;QACvD,IAAIvH,gBAAgB,GAAG,EAAE;QACzB,IAAI7V,CAAC,GAAG,IAAI,CAACwT,MAAM,CAACtM,IAAI,CAAC,CAAC;QAC1B,IAAIlH,CAAC,KAAK,GAAG,EAAE;UACb,IAAIsd,KAAK,GAAG,IAAI,CAAC9J,MAAM,CAACtM,IAAI,CAAC,CAAC,CAAC;UAC/B;UACA;UACA;UACA,IAAI,CAAC,IAAI,CAACoV,SAAS,CAACJ,GAAG,IAAI,CAAC,IAAI,CAACO,SAAS,CAACP,GAAG,IAAIoB,KAAK,KAAK,GAAG,EAAE;YAC/DzH,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAAClC,UAAU,CAACuI,GAAG,CAACpG,IAAI,CAAC,CAAC;UAC9B;UACA,IAAI,CAAC,IAAI,CAACwG,SAAS,CAACN,GAAG,IAAI,CAAC,IAAI,CAACS,SAAS,CAACT,GAAG,IAAIsB,KAAK,KAAK,GAAG,EAAE;YAC/DzH,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAAClC,UAAU,CAACqI,GAAG,CAAClG,IAAI,CAAC,CAAC;UAC9B;QACF,CAAC,MAAM,IAAI9V,CAAC,KAAK,GAAG,EAAE;UACpB,IAAI,CAAC,IAAI,CAACsc,SAAS,CAACL,UAAU,IAAI,CAAC,IAAI,CAACQ,SAAS,CAACR,UAAU,EAAE;YAC5DpG,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAAClC,UAAU,CAAC+I,kBAAkB,CAAC5G,IAAI,CAAC,CAAC;YAC3CD,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAAClC,UAAU,CAACgJ,oBAAoB,CAAC7G,IAAI,CAAC,CAAC;YAC7CD,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAAClC,UAAU,CAACsI,UAAU,CAACnG,IAAI,CAAC,CAAC;UACrC;UACA,IAAI,CAAC,IAAI,CAACwG,SAAS,CAACP,MAAM,EAAE;YAC1B;YACA,IAAI,CAAC,IAAI,CAACU,SAAS,CAACV,MAAM,IAAI,CAAC,IAAI,CAACU,SAAS,CAACR,UAAU,EAAE;cACxDpG,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAAClC,UAAU,CAACiJ,YAAY,CAAC9G,IAAI,CAAC,CAAC;YACvC;YACA,IAAI,CAAC,IAAI,CAAC2G,SAAS,CAACV,MAAM,EAAE;cAC1BlG,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAAClC,UAAU,CAACkJ,cAAc,CAAC/G,IAAI,CAAC,CAAC;cACvCD,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAAClC,UAAU,CAACoI,MAAM,CAACjG,IAAI,CAAC,CAAC;YACjC;UACF;UACA,IAAI,CAAC,IAAI,CAACwG,SAAS,CAACH,MAAM,EAAE;YAC1B;YACA,IAAI,IAAI,CAACG,SAAS,CAACP,MAAM,IAAI,IAAI,CAACO,SAAS,CAACL,UAAU,EAAE;cACtDpG,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAAClC,UAAU,CAACmJ,cAAc,CAAChH,IAAI,CAAC,CAAC;cACvCD,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAAClC,UAAU,CAACoJ,cAAc,CAACjH,IAAI,CAAC,CAAC;cACvCD,gBAAgB,GAAGA,gBAAgB,IACjC,IAAI,CAAClC,UAAU,CAACwI,MAAM,CAACrG,IAAI,CAAC,CAAC;YACjC;UACF;QACF;QACA,OAAOD,gBAAgB;MACzB,CAAC;MAGDna,MAAM,CAACU,OAAO,CAACmW,kBAAkB,GAAGA,kBAAkB;;MAGtD;IAAM;IACN,UAAY;IACZ;IACA,SAAU;IACV;IAAU,IAAIgL,wBAAwB,GAAG,CAAC,CAAC;IAC3C;IACA,SAAU;IACV;IAAU,SAAS3hB,mBAAmBA,CAAC4hB,QAAQ,EAAE;MACjD,SAAW;MACX,QAAW,IAAIC,YAAY,GAAGF,wBAAwB,CAACC,QAAQ,CAAC;MAChE;MAAW,IAAIC,YAAY,KAAKhX,SAAS,EAAE;QAC3C,QAAY,OAAOgX,YAAY,CAACrhB,OAAO;QACvC;MAAW;MACX,SAAW;MACX;MAAW,IAAIV,MAAM,GAAG6hB,wBAAwB,CAACC,QAAQ,CAAC,GAAG;QAC7D,SAAY;QACZ,SAAY;QACZ,QAAYphB,OAAO,EAAE,CAAC;QACtB;MAAW,CAAC;MACZ;MACA,SAAW;MACX;MAAWX,mBAAmB,CAAC+hB,QAAQ,CAAC,CAAC9hB,MAAM,EAAEA,MAAM,CAACU,OAAO,EAAER,mBAAmB,CAAC;MACrF;MACA,SAAW;MACX;MAAW,OAAOF,MAAM,CAACU,OAAO;MAChC;IAAU;IACV;IACA;IACA;IACA,SAAU;IACV,SAAU;IACV,SAAU;IACV;IAAU,IAAIshB,mBAAmB,GAAG9hB,mBAAmB,CAAC,CAAC,CAAC;IAC1D;IAAUJ,kBAAkB,GAAGkiB,mBAAmB;IAClD;IACA;EAAS,CAAC,EAAE,CAAC;EAEb,IAAI3hB,WAAW,GAAGP,kBAAkB;EACpC;EACA,IAAI,OAAOmiB,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IAC5C;IACAD,MAAM,CAAC,EAAE,EAAE,YAAW;MAClB,OAAO;QAAE5hB,WAAW,EAAEA;MAAY,CAAC;IACvC,CAAC,CAAC;EACN,CAAC,MAAM,IAAI,OAAOK,OAAO,KAAK,WAAW,EAAE;IACvC;IACA;IACAA,OAAO,CAACL,WAAW,GAAGA,WAAW;EACrC,CAAC,MAAM,IAAI,OAAO8hB,MAAM,KAAK,WAAW,EAAE;IACtC;IACAA,MAAM,CAAC9hB,WAAW,GAAGA,WAAW;EACpC,CAAC,MAAM,IAAI,OAAO+hB,MAAM,KAAK,WAAW,EAAE;IACtC;IACAA,MAAM,CAAC/hB,WAAW,GAAGA,WAAW;EACpC;AAEA,CAAC,EAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}