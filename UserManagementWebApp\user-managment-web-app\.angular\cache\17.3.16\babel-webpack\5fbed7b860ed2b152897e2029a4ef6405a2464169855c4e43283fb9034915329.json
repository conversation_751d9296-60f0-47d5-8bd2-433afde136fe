{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport { getCurrentUser as getCurrentUser$1 } from './internal/getCurrentUser.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Gets the current user from the idToken.\n *\n * @param input -  The GetCurrentUserInput object.\n * @returns GetCurrentUserOutput\n * @throws - {@link InitiateAuthException} - Thrown when the service fails to refresh the tokens.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nconst getCurrentUser = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* () {\n    return getCurrentUser$1(Amplify);\n  });\n  return function getCurrentUser() {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { getCurrentUser };", "map": {"version": 3, "names": ["Amplify", "getCurrentUser", "getCurrentUser$1", "_ref", "_asyncToGenerator", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/getCurrentUser.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { getCurrentUser as getCurrentUser$1 } from './internal/getCurrentUser.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Gets the current user from the idToken.\n *\n * @param input -  The GetCurrentUserInput object.\n * @returns GetCurrentUserOutput\n * @throws - {@link InitiateAuthException} - Thrown when the service fails to refresh the tokens.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nconst getCurrentUser = async () => {\n    return getCurrentUser$1(Amplify);\n};\n\nexport { getCurrentUser };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,cAAc,IAAIC,gBAAgB,QAAQ,+BAA+B;;AAElF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,cAAc;EAAA,IAAAE,IAAA,GAAAC,iBAAA,CAAG,aAAY;IAC/B,OAAOF,gBAAgB,CAACF,OAAO,CAAC;EACpC,CAAC;EAAA,gBAFKC,cAAcA,CAAA;IAAA,OAAAE,IAAA,CAAAE,KAAA,OAAAC,SAAA;EAAA;AAAA,GAEnB;AAED,SAASL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}