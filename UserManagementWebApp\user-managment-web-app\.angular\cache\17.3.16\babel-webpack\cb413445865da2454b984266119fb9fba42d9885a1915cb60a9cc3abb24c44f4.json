{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Compose a service API handler that accepts input as defined shape and responds conforming to defined output shape.\n * A service API handler is composed with:\n * * A transfer handler\n * * A serializer function\n * * A deserializer function\n * * A default config object\n *\n * The returned service API handler, when called, will trigger the following workflow:\n * 1. When calling the service API handler function, the default config object is merged into the input config\n * object to assign the default values of some omitted configs, resulting to a resolved config object.\n * 2. The `endpointResolver` function from the default config object will be invoked with the resolved config object and\n * API input object resulting to an endpoint instance.\n * 3. The serializer function is invoked with API input object and the endpoint instance resulting to an HTTP request\n * instance.\n * 4. The HTTP request instance and the resolved config object is passed to the transfer handler function.\n * 5. The transfer handler function resolves to an HTTP response instance(can be either successful or failed status code).\n * 6. The deserializer function is invoked with the HTTP response instance resulting to the API output object, and\n * return to the caller.\n *\n *\n * @param transferHandler Async function for dispatching HTTP requests and returning HTTP response.\n * @param serializer  Async function for converting object in defined input shape into HTTP request targeting a given\n * \tendpoint.\n * @param deserializer Async function for converting HTTP response into output object in defined output shape, or error\n * \tshape.\n * @param defaultConfig  object containing default options to be consumed by transfer handler, serializer and\n *  deserializer.\n * @returns a async service API handler function that accepts a config object and input object in defined shape, returns\n * \tan output object in defined shape. It may also throw error instance in defined shape in deserializer. The config\n *  object type is composed with options type of transferHandler, endpointResolver function as well as endpointResolver\n *  function's input options type, region string. The config object property will be marked as optional if it's also\n * \tdefined in defaultConfig.\n *\n * @internal\n */\nconst composeServiceApi = (transferHandler, serializer, deserializer, defaultConfig) => {\n  return /*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(function* (config, input) {\n      const resolvedConfig = {\n        ...defaultConfig,\n        ...config\n      };\n      // We need to allow different endpoints based on both given config(other than region) and input.\n      // However for most of non-S3 services, region is the only input for endpoint resolver.\n      const endpoint = yield resolvedConfig.endpointResolver(resolvedConfig, input);\n      // Unlike AWS SDK clients, a serializer should NOT populate the `host` or `content-length` headers.\n      // Both of these headers are prohibited per Spec(https://developer.mozilla.org/en-US/docs/Glossary/Forbidden_header_name).\n      // They will be populated automatically by browser, or node-fetch polyfill.\n      const request = yield serializer(input, endpoint);\n      const response = yield transferHandler(request, {\n        ...resolvedConfig\n      });\n      return deserializer(response);\n    });\n    return function (_x, _x2) {\n      return _ref.apply(this, arguments);\n    };\n  }();\n};\nexport { composeServiceApi };", "map": {"version": 3, "names": ["composeServiceApi", "transfer<PERSON><PERSON><PERSON>", "serializer", "deserializer", "defaultConfig", "_ref", "_asyncToGenerator", "config", "input", "resolvedConfig", "endpoint", "endpointResolver", "request", "response", "_x", "_x2", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/internal/composeServiceApi.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Compose a service API handler that accepts input as defined shape and responds conforming to defined output shape.\n * A service API handler is composed with:\n * * A transfer handler\n * * A serializer function\n * * A deserializer function\n * * A default config object\n *\n * The returned service API handler, when called, will trigger the following workflow:\n * 1. When calling the service API handler function, the default config object is merged into the input config\n * object to assign the default values of some omitted configs, resulting to a resolved config object.\n * 2. The `endpointResolver` function from the default config object will be invoked with the resolved config object and\n * API input object resulting to an endpoint instance.\n * 3. The serializer function is invoked with API input object and the endpoint instance resulting to an HTTP request\n * instance.\n * 4. The HTTP request instance and the resolved config object is passed to the transfer handler function.\n * 5. The transfer handler function resolves to an HTTP response instance(can be either successful or failed status code).\n * 6. The deserializer function is invoked with the HTTP response instance resulting to the API output object, and\n * return to the caller.\n *\n *\n * @param transferHandler Async function for dispatching HTTP requests and returning HTTP response.\n * @param serializer  Async function for converting object in defined input shape into HTTP request targeting a given\n * \tendpoint.\n * @param deserializer Async function for converting HTTP response into output object in defined output shape, or error\n * \tshape.\n * @param defaultConfig  object containing default options to be consumed by transfer handler, serializer and\n *  deserializer.\n * @returns a async service API handler function that accepts a config object and input object in defined shape, returns\n * \tan output object in defined shape. It may also throw error instance in defined shape in deserializer. The config\n *  object type is composed with options type of transferHandler, endpointResolver function as well as endpointResolver\n *  function's input options type, region string. The config object property will be marked as optional if it's also\n * \tdefined in defaultConfig.\n *\n * @internal\n */\nconst composeServiceApi = (transferHandler, serializer, deserializer, defaultConfig) => {\n    return async (config, input) => {\n        const resolvedConfig = {\n            ...defaultConfig,\n            ...config,\n        };\n        // We need to allow different endpoints based on both given config(other than region) and input.\n        // However for most of non-S3 services, region is the only input for endpoint resolver.\n        const endpoint = await resolvedConfig.endpointResolver(resolvedConfig, input);\n        // Unlike AWS SDK clients, a serializer should NOT populate the `host` or `content-length` headers.\n        // Both of these headers are prohibited per Spec(https://developer.mozilla.org/en-US/docs/Glossary/Forbidden_header_name).\n        // They will be populated automatically by browser, or node-fetch polyfill.\n        const request = await serializer(input, endpoint);\n        const response = await transferHandler(request, {\n            ...resolvedConfig,\n        });\n        return deserializer(response);\n    };\n};\n\nexport { composeServiceApi };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,iBAAiB,GAAGA,CAACC,eAAe,EAAEC,UAAU,EAAEC,YAAY,EAAEC,aAAa,KAAK;EACpF;IAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAO,WAAOC,MAAM,EAAEC,KAAK,EAAK;MAC5B,MAAMC,cAAc,GAAG;QACnB,GAAGL,aAAa;QAChB,GAAGG;MACP,CAAC;MACD;MACA;MACA,MAAMG,QAAQ,SAASD,cAAc,CAACE,gBAAgB,CAACF,cAAc,EAAED,KAAK,CAAC;MAC7E;MACA;MACA;MACA,MAAMI,OAAO,SAASV,UAAU,CAACM,KAAK,EAAEE,QAAQ,CAAC;MACjD,MAAMG,QAAQ,SAASZ,eAAe,CAACW,OAAO,EAAE;QAC5C,GAAGH;MACP,CAAC,CAAC;MACF,OAAON,YAAY,CAACU,QAAQ,CAAC;IACjC,CAAC;IAAA,iBAAAC,EAAA,EAAAC,GAAA;MAAA,OAAAV,IAAA,CAAAW,KAAA,OAAAC,SAAA;IAAA;EAAA;AACL,CAAC;AAED,SAASjB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}