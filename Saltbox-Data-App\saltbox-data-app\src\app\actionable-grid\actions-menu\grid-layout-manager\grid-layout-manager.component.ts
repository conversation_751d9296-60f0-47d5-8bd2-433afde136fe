import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { GridLayout } from '../../model/grid-layout';
import { GridLayoutsService } from '../../services/grid-layouts.service';
import { firstValueFrom } from 'rxjs';
import { MenuItem } from 'primeng/api';
import { MenuModule } from 'primeng/menu';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';
import { NgClass } from '@angular/common';
import { SaveGridLayoutDialogComponent } from './save-grid-layout-dialog/save-grid-layout-dialog.component';
import { NotificationService } from 'src/app/core/services/notification.service';
import { ConfirmationService } from 'primeng/api';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { AGGridService } from 'src/app/core/services/ag-grid.service';
import { AgGridAngular } from 'ag-grid-angular';
import { ReportInfo } from 'src/app/core/models/report-info';
import { ParameterTypes } from 'src/app/core/enums/actionable-grid';
import { UserParamFilterService } from 'src/app/core/services/user-param-filter.service';
import { RecordRefiner } from '../../refine-records/models/record-refiner';
import { ReportParameter } from 'src/app/core/models/report-parameter';
import { ChangeHistoryService } from '../../services/change-history.service';
import { TagModule } from 'primeng/tag';

@Component({
  selector: 'app-grid-layout-manager',
  templateUrl: './grid-layout-manager.component.html',
  standalone: true,
  imports: [
    MenuModule,
    ButtonModule,
    TooltipModule,
    TagModule,
    NgClass,
    SaveGridLayoutDialogComponent,
    ConfirmDialogModule
  ],
  providers: [ConfirmationService]
})
export class GridLayoutManagerComponent implements OnChanges {
  @Input() reportInfo: ReportInfo;
  @Input() agGrid: AgGridAngular;
  @Input() disabled = false;
  @Input() layouts: GridLayout[];

  @Output() selectedLayoutChange = new EventEmitter<GridLayout>();

  layoutMenuItems: MenuItem[] = [];
  selectedLayout: GridLayout | undefined;
  layoutToEdit: GridLayout | undefined;
  layoutFormMode: 'create' | 'edit' | 'saveas' = 'create';
  showSaveLayoutDialog = false;
  layoutHashId = '';
  disableSlicerSettings = true;
  disableUserParams = true;
  recordRefiners: RecordRefiner[] | undefined;
  userParams: ReportParameter[] | undefined;
  refinerUserParams: ReportParameter[] | undefined;
  reportHasSlicers = false;
  reportHasUserParams = false;
  reportProfileParameters: ReportParameter[] | undefined;

  constructor(
    private gridLayoutService: GridLayoutsService,
    private notificationService: NotificationService,
    private confirmationService: ConfirmationService,
    private aGGridService: AGGridService,
    private filterService: UserParamFilterService,
    private changeHistoryService: ChangeHistoryService
  ) { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.layouts && changes.layouts.currentValue !== changes.layouts.previousValue)
      this.initLayouts();

    if (changes.agGrid && changes.agGrid.currentValue !== changes.agGrid.previousValue) {
      this.agGrid?.api?.addEventListener('firstDataRendered', () => {
        this.setGridLayout(this.selectedLayout && this.selectedLayout.layoutHashId === this.layoutHashId ? this.selectedLayout : undefined);
      });
    }
  }

  async initLayouts() {
    // Default user params
    this.userParams = this.reportInfo?.params;
    this.reportHasSlicers = this.reportInfo?.formatConfig?.actionableGridColumnsConfig?.some(col => col.slicerFilter);
    this.reportHasUserParams = this.reportInfo?.params?.length > 0;
    this.reportProfileParameters = this.reportInfo?.params?.filter(param => param.paramType === ParameterTypes.UserProfile);

    if (this.reportHasSlicers) {
      this.filterService.recordRefinersChanged$.subscribe((params) => {
        this.refinerUserParams = params.userParams;
        // We should save only the record refiners that have changes, plus no need to save the unique values
        this.recordRefiners = params.refiners?.filter(r => r.hasChanges()).map(r => Object.assign({}, r));
        this.recordRefiners?.forEach(r => r.uniqueValues = []);

        if (!this.recordRefiners?.length)
          this.recordRefiners = undefined;

        this.reloadData(false);
      });
    }

    if (this.reportHasUserParams) {
      this.filterService.userParamsChanged$.subscribe((userParams) => {
        this.userParams = [...(this.reportProfileParameters ?? []), ...userParams];

        if (this.reportHasSlicers) {
          this.recordRefiners = undefined;
          this.refinerUserParams = undefined;
          this.filterService.setRecordRefiners({ refiners: this.recordRefiners, userParams: this.userParams });
        }

        this.reloadData(false);
      });
    }

    this.layoutHashId = await this.gridLayoutService.getGridHashId(this.reportInfo);
    this.loadLayoutMenuItems();
  }

  checkPendingChangesAndReloadData(showError = true) {
    this.changeHistoryService.checkPendingChangesAndReset(
      () => this.reloadData(showError)
    );
  }

  reloadData(showError = true) {
    const userParamsAreValid = this.filterService.validateUserEnteredParams(this.userParams ?? [], showError);
    this.filterService.requestDataReload([...(this.refinerUserParams ?? []), ...(this.userParams ?? [])], !userParamsAreValid);
  }

  async loadLayoutMenuItems(switchLayout: boolean = true) {
    const defaultMenuItems: MenuItem[] = [
      {
        label: 'Actions',
        items: [{
          label: 'Save Layout',
          icon: 'pi pi-save',
          disabled: this.selectedLayout?.editable === false,
          command: () => {
            if (this.selectedLayout) {
              this.selectedLayout.agGridSettings = {
                columnState: JSON.stringify(this.agGrid?.api.getColumnState()),
                filterModel: JSON.stringify(this.agGrid?.api.getFilterModel()),
                sideBar: this.agGrid?.api.getGridOption('sideBar'),
                pivotMode: this.agGrid?.api.getGridOption('pivotMode')
              };
              this.selectedLayout.recordRefiners = this.recordRefiners?.length ? JSON.stringify(this.recordRefiners) : undefined;
              this.selectedLayout.userParams = this.userParams?.length ? JSON.stringify(this.filterUserParameters(this.userParams)) : undefined;
              this.selectedLayout.hasRecordRefiners = this.recordRefiners?.length ? true : false;
              this.selectedLayout.hasUserParams = this.userParams?.length ? true : false;
              this.onSaveLayout(this.selectedLayout);
            }
            else
              this.openSaveLayoutDialog('create');
          }
        },
        {
          label: 'Save Layout As',
          icon: 'pi pi-save',
          command: () => {
            this.openSaveLayoutDialog(this.selectedLayout ? 'saveas' : 'create', this.selectedLayout);
          }
        },
        {
          label: 'Reset Layout',
          icon: 'pi pi-filter-slash',
          command: () => {
            this.switchLayout(undefined, false, true);
          }
        }
        ]
      }];

    this.layoutMenuItems = [...defaultMenuItems];

    if (this.layouts?.length) {
      let otherMenuItems: MenuItem[] = [];
      const publicLayouts = this.layouts.filter(layout => layout.isPublic);
      const privateLayouts = this.layouts.filter(layout => !layout.isPublic);

      otherMenuItems = publicLayouts?.length ? otherMenuItems.concat({
        label: 'Shared Layouts',
        items: this.layouts.filter(layout => layout.isPublic).map((layout: GridLayout) => {
          return {
            label: layout.name,
            command: () => {
              this.switchLayout(layout);
            },
            state: { layout, disabled: layout.layoutHashId !== this.layoutHashId }
          };
        })
      }) : otherMenuItems;

      otherMenuItems = privateLayouts?.length ? otherMenuItems.concat({
        label: 'My Layouts',
        items: this.layouts.filter(layout => !layout.isPublic).map((layout: GridLayout) => {
          return {
            label: layout.name,
            command: () => {
              this.switchLayout(layout);
            },
            state: { layout, disabled: layout.layoutHashId !== this.layoutHashId }
          };
        })
      }) : otherMenuItems;

      this.layoutMenuItems = [...defaultMenuItems, { separator: true }, ...otherMenuItems];
    }

    const defaultLayout = this.layouts?.find(layout => layout.isCurrentLayout && layout.layoutHashId === this.layoutHashId) ??
      this.layouts?.find(layout => layout.isDefault && layout.layoutHashId === this.layoutHashId);
    switchLayout && this.switchLayout(defaultLayout, true, false);

    if (!this.selectedLayout && switchLayout)
      this.setLayout(undefined);
  }

  resetToDefaultLayout() {
    this.selectedLayout = undefined;
    this.userParams = this.reportInfo?.params;

    if (this.userParams?.filter(p => p.paramType === ParameterTypes.UserEntered)?.length)
      this.filterService.setUserParams(this.userParams);

    if (this.reportHasSlicers) {
      this.refinerUserParams = [];
      this.recordRefiners = [];
      this.filterService.setRecordRefiners({ refiners: this.recordRefiners, userParams: this.userParams });
    }

    this.reloadData(false);

    this.setGridLayout(undefined);
    this.setUserSelectedLayout();
    this.selectedLayoutChange.emit(undefined);
  }

  switchLayout(layout: GridLayout | undefined, isDefaultLayout: boolean = false, setUserCurrentLayout: boolean = true) {
    if (layout === this.selectedLayout && layout !== undefined)
      return;

    if (layout && layout.layoutHashId !== this.layoutHashId) {
      this.notificationService.showWarning('Incompatible Layout', 'The view has changed. This layout is no longer valid');
      return;
    }

    this.changeHistoryService.checkPendingChangesAndReset(
      () => this.setLayout(layout, isDefaultLayout, setUserCurrentLayout)
    );

    //Disabling SAVE button if the layout is not editable
    if (this.layoutMenuItems?.length && this.layoutMenuItems[0].items?.length)
      this.layoutMenuItems[0].items[0].disabled = layout?.editable === false;
  }

  setLayout(layout: GridLayout, isDefaultLayout: boolean = false, setUserCurrentLayout: boolean = true) {
    if (!layout) {
      this.resetToDefaultLayout();
      return;
    }

    if (layout.hasUserParams && layout.userParams) {
      this.userParams = this.getUserParams(layout, this.reportInfo.params);
    }
    this.filterService.setUserParams(this.userParams);

    if (this.reportHasSlicers) {
      this.recordRefiners = [];
      this.refinerUserParams = [];
      if (layout.hasRecordRefiners && layout.recordRefiners) {
        this.recordRefiners = JSON.parse(layout.recordRefiners);
        this.refinerUserParams = this.filterService.getReportParametersByRefiners(this.recordRefiners);
      }

      this.filterService.setRecordRefiners({ refiners: this.recordRefiners, userParams: this.userParams });
    }

    this.selectedLayout = layout;
    this.selectedLayoutChange.emit(layout);
    this.reloadData(!isDefaultLayout);
    this.setGridLayout(layout);

    if (setUserCurrentLayout)
      this.setUserSelectedLayout(layout);
  }

  setUserSelectedLayout(layout?: GridLayout) {
    this.gridLayoutService.setUserSelectedLayout({
      projectId: +this.reportInfo.projectId,
      projectVersionId: +this.reportInfo.projectVersionId,
      gridId: this.reportInfo.reportId,
      selectedLayoutId: layout?.id
    });
  }

  getUserParams(layout: GridLayout, params: any): ReportParameter[] {
    const profileParams = params?.filter(param => param.paramType === ParameterTypes.UserProfile) ?? [];
    const userParams = layout?.userParams ? JSON.parse(layout.userParams) : [];
    return [...profileParams, ...userParams];
  }

  setGridLayout(layout?: GridLayout) {
    if (layout) {
      this.agGrid?.api?.applyColumnState({ state: JSON.parse(layout.agGridSettings.columnState), applyOrder: true, defaultState: { hide: true } });
      this.agGrid?.api?.setFilterModel(JSON.parse(layout.agGridSettings.filterModel));
      this.agGrid?.api?.setGridOption('sideBar', layout.agGridSettings.sideBar);
      this.agGrid?.api?.setGridOption('pivotMode', layout.agGridSettings.pivotMode);
    } else {
      this.agGrid?.api?.resetColumnState();
      this.agGrid?.api?.setFilterModel({});
      this.agGrid?.api?.autoSizeAllColumns();
      this.agGrid?.api?.setGridOption('sideBar', null);
      this.agGrid?.api?.setGridOption('pivotMode', false);
    }
  }

  openSaveLayoutDialog(formMode: 'create' | 'edit' | 'saveas' = 'create', layout?: GridLayout, event?: Event) {
    event?.stopPropagation();

    this.layoutFormMode = formMode;
    this.disableSlicerSettings = !this.reportInfo?.formatConfig?.actionableGridColumnsConfig?.some(col => col.slicerFilter)
      || !(!layout || formMode !== 'edit' ? this.recordRefiners?.length : layout.recordRefiners?.length);
    this.disableUserParams = !this.reportInfo?.params?.some(param => param.paramType === ParameterTypes.UserEntered)
      || !(!layout || formMode !== 'edit' ? this.userParams?.length : layout.userParams?.length);

    // Save as mode
    if (layout && formMode === 'edit') {
      this.layoutToEdit = layout;

      // If the editing layout is the same as current, update the layout settings
      if (this.layoutToEdit.id === this.selectedLayout?.id) {
        this.layoutToEdit.agGridSettings = {
          columnState: JSON.stringify(this.agGrid.api.getColumnState()),
          filterModel: JSON.stringify(this.agGrid.api.getFilterModel()),
          sideBar: this.agGrid.api.getGridOption('sideBar'),
          pivotMode: this.agGrid.api.getGridOption('pivotMode')
        };

        this.layoutToEdit.recordRefiners = this.recordRefiners ? JSON.stringify(this.recordRefiners) : undefined;
        this.layoutToEdit.userParams = this.userParams?.length ? JSON.stringify(this.filterUserParameters(this.userParams)) : undefined;
      }
    }
    // Create mode
    else {
      this.layoutToEdit = {
        gridId: this.reportInfo.reportId,
        recordRefiners: this.recordRefiners ? JSON.stringify(this.recordRefiners) : undefined,
        userParams: this.userParams?.length ? JSON.stringify(this.filterUserParameters(this.userParams)) : undefined,
        name: formMode === 'saveas' && this.selectedLayout ? this.selectedLayout.name + ' Copy' : "",
        projectId: +this.reportInfo.projectId,
        hasRecordRefiners: this.selectedLayout?.hasRecordRefiners && !this.disableSlicerSettings,
        hasUserParams: this.selectedLayout?.hasUserParams && !this.disableUserParams,
        projectVersionId: +this.reportInfo.projectVersionId,
        isPublic: formMode === 'saveas' && this.selectedLayout ? this.selectedLayout.isPublic : false,
        isDefault: formMode === 'saveas' && this.selectedLayout ? this.selectedLayout.isDefault : false,
        editable: true,
        agGridSettings: {
          columnState: JSON.stringify(this.agGrid.api.getColumnState()),
          filterModel: JSON.stringify(this.agGrid.api.getFilterModel()),
          sideBar: this.agGrid.api.getGridOption('sideBar'),
          pivotMode: this.agGrid.api.getGridOption('pivotMode')
        },
        layoutHashId: this.layoutHashId
      };
    }

    this.showSaveLayoutDialog = true;
  }

  deleteLayout(layout: GridLayout, event: Event): void {
    event?.stopPropagation();

    this.confirmationService.confirm({
      message: `Are you sure you want to delete the "${layout.name}" layout?`,
      header: 'Delete Confirmation',
      icon: 'pi pi-exclamation-triangle text-warning',
      accept: () => {
        firstValueFrom(this.gridLayoutService.deleteLayout(layout.id))
          .then(() => {
            const deletedLayoutIndex = this.layouts.findIndex(l => l.id === layout.id);
            this.layouts.splice(deletedLayoutIndex, 1);
            this.loadLayoutMenuItems(false);

            if (this.selectedLayout?.id === layout.id)
              this.switchLayout(this.layouts.find(layout => layout.isDefault));

            this.notificationService.showSuccess('Layout deleted successfully');
          })
          .catch(error => {
            console.error('Failed to delete layout:', error);
            this.notificationService.showError('Failed to delete layout');
          });
      }
    });
  }

  onSaveLayout(layout: GridLayout) {
    if (layout.id) {
      firstValueFrom(this.gridLayoutService.updateLayout(layout.id, layout))
        .then((updatedLayout) => {
          const savedLayoutIndex = this.layouts.findIndex(l => l.id === updatedLayout.id);
          this.layouts[savedLayoutIndex] = updatedLayout;

          // If this layout is being set as default, remove default from other layouts
          if (updatedLayout.isDefault) {
            this.layouts.filter(l => l.isDefault && l.id !== updatedLayout.id)
              .forEach(l => { l.isDefault = false; });
          }

          this.loadLayoutMenuItems(false);
          this.notificationService.showSuccess('Layout saved successfully');
        })
        .catch(error => {
          this.notificationService.showError('Failed to update the layout!');
        });
      return;
    }
    else
      firstValueFrom(this.gridLayoutService.saveLayout(layout))
        .then((savedLayout) => {
          // If new layout is default, remove default from other layouts
          if (savedLayout.isDefault) {
            this.layouts.forEach(l => l.isDefault = false);
          }

          this.layouts.push(savedLayout);
          this.switchLayout(savedLayout);

          this.loadLayoutMenuItems(false);
          this.notificationService.showSuccess('Layout saved successfully');
        })
        .catch(error => {
          this.notificationService.showError('Failed to save the layout!');
        });
  }

  /**
   * Filters user parameters to include only the essential properties
   * @param params The original user parameters
   * @returns A new array with filtered user parameter objects
   */
  private filterUserParameters(params: ReportParameter[]): any[] {
    if (!params?.length) return [];

    return params.filter(param => param.paramType === ParameterTypes.UserEntered).map(param => ({
      id: param.id,
      reportSetupId: param.reportSetupId,
      paramType: param.paramType,
      paramCondition: param.paramCondition,
      valueType: param.valueType,
      defaultValue: param.defaultValue,
      paramLabel: param.paramLabel,
      profileParamter: param.profileParamter,
      paramSource: param.paramSource,
      action: param.action,
      allowEmpty: param.allowEmpty,
      parameterValue: param.parameterValue,
      columnUID: param.columnUID
    }));
  }
}







