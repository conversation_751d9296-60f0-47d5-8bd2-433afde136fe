{"ast": null, "code": "import { getSkewCorrectedDate } from './getSkewCorrectedDate.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// 5 mins in milliseconds. Ref: https://github.com/aws/aws-sdk-js-v3/blob/6c0f44fab30a1bb2134af47362a31332abc3666b/packages/middleware-signing/src/utils/isClockSkewed.ts#L10\nconst SKEW_WINDOW = 5 * 60 * 1000;\n/**\n * Checks if the provided date is within the skew window of 5 minutes.\n *\n * @param clockTimeInMilliseconds Time to check for skew in milliseconds.\n * @param clockOffsetInMilliseconds Offset to check clock against in milliseconds.\n *\n * @returns True if skewed. False otherwise.\n *\n * @internal\n */\nconst isClockSkewed = (clockTimeInMilliseconds, clockOffsetInMilliseconds) => Math.abs(getSkewCorrectedDate(clockOffsetInMilliseconds).getTime() - clockTimeInMilliseconds) >= SKEW_WINDOW;\nexport { isClockSkewed };", "map": {"version": 3, "names": ["getSkewCorrectedDate", "SKEW_WINDOW", "isClockSkewed", "clockTimeInMilliseconds", "clockOffsetInMilliseconds", "Math", "abs", "getTime"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/utils/isClockSkewed.mjs"], "sourcesContent": ["import { getSkewCorrectedDate } from './getSkewCorrectedDate.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// 5 mins in milliseconds. Ref: https://github.com/aws/aws-sdk-js-v3/blob/6c0f44fab30a1bb2134af47362a31332abc3666b/packages/middleware-signing/src/utils/isClockSkewed.ts#L10\nconst SKEW_WINDOW = 5 * 60 * 1000;\n/**\n * Checks if the provided date is within the skew window of 5 minutes.\n *\n * @param clockTimeInMilliseconds Time to check for skew in milliseconds.\n * @param clockOffsetInMilliseconds Offset to check clock against in milliseconds.\n *\n * @returns True if skewed. False otherwise.\n *\n * @internal\n */\nconst isClockSkewed = (clockTimeInMilliseconds, clockOffsetInMilliseconds) => Math.abs(getSkewCorrectedDate(clockOffsetInMilliseconds).getTime() -\n    clockTimeInMilliseconds) >= SKEW_WINDOW;\n\nexport { isClockSkewed };\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,4BAA4B;;AAEjE;AACA;AACA;AACA,MAAMC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAGA,CAACC,uBAAuB,EAAEC,yBAAyB,KAAKC,IAAI,CAACC,GAAG,CAACN,oBAAoB,CAACI,yBAAyB,CAAC,CAACG,OAAO,CAAC,CAAC,GAC5IJ,uBAAuB,CAAC,IAAIF,WAAW;AAE3C,SAASC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}