{"ast": null, "code": "import { AWS_CLOUDWATCH_CATEGORY } from '../constants.mjs';\nimport { LogType } from './types.mjs';\n\n/* eslint-disable no-console */\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst LOG_LEVELS = {\n  VERBOSE: 1,\n  DEBUG: 2,\n  INFO: 3,\n  WARN: 4,\n  ERROR: 5,\n  NONE: 6\n};\n/**\n * Write logs\n * @class Logger\n */\nclass ConsoleLogger {\n  /**\n   * @constructor\n   * @param {string} name - Name of the logger\n   */\n  constructor(name, level = LogType.WARN) {\n    this.name = name;\n    this.level = level;\n    this._pluggables = [];\n  }\n  _padding(n) {\n    return n < 10 ? '0' + n : '' + n;\n  }\n  _ts() {\n    const dt = new Date();\n    return [this._padding(dt.getMinutes()), this._padding(dt.getSeconds())].join(':') + '.' + dt.getMilliseconds();\n  }\n  configure(config) {\n    if (!config) return this._config;\n    this._config = config;\n    return this._config;\n  }\n  /**\n   * Write log\n   * @method\n   * @memeberof Logger\n   * @param {LogType|string} type - log type, default INFO\n   * @param {string|object} msg - Logging message or object\n   */\n  _log(type, ...msg) {\n    let loggerLevelName = this.level;\n    if (ConsoleLogger.LOG_LEVEL) {\n      loggerLevelName = ConsoleLogger.LOG_LEVEL;\n    }\n    if (typeof window !== 'undefined' && window.LOG_LEVEL) {\n      loggerLevelName = window.LOG_LEVEL;\n    }\n    const loggerLevel = LOG_LEVELS[loggerLevelName];\n    const typeLevel = LOG_LEVELS[type];\n    if (!(typeLevel >= loggerLevel)) {\n      // Do nothing if type is not greater than or equal to logger level (handle undefined)\n      return;\n    }\n    let log = console.log.bind(console);\n    if (type === LogType.ERROR && console.error) {\n      log = console.error.bind(console);\n    }\n    if (type === LogType.WARN && console.warn) {\n      log = console.warn.bind(console);\n    }\n    if (ConsoleLogger.BIND_ALL_LOG_LEVELS) {\n      if (type === LogType.INFO && console.info) {\n        log = console.info.bind(console);\n      }\n      if (type === LogType.DEBUG && console.debug) {\n        log = console.debug.bind(console);\n      }\n    }\n    const prefix = `[${type}] ${this._ts()} ${this.name}`;\n    let message = '';\n    if (msg.length === 1 && typeof msg[0] === 'string') {\n      message = `${prefix} - ${msg[0]}`;\n      log(message);\n    } else if (msg.length === 1) {\n      message = `${prefix} ${msg[0]}`;\n      log(prefix, msg[0]);\n    } else if (typeof msg[0] === 'string') {\n      let obj = msg.slice(1);\n      if (obj.length === 1) {\n        obj = obj[0];\n      }\n      message = `${prefix} - ${msg[0]} ${obj}`;\n      log(`${prefix} - ${msg[0]}`, obj);\n    } else {\n      message = `${prefix} ${msg}`;\n      log(prefix, msg);\n    }\n    for (const plugin of this._pluggables) {\n      const logEvent = {\n        message,\n        timestamp: Date.now()\n      };\n      plugin.pushLogs([logEvent]);\n    }\n  }\n  /**\n   * Write General log. Default to INFO\n   * @method\n   * @memeberof Logger\n   * @param {string|object} msg - Logging message or object\n   */\n  log(...msg) {\n    this._log(LogType.INFO, ...msg);\n  }\n  /**\n   * Write INFO log\n   * @method\n   * @memeberof Logger\n   * @param {string|object} msg - Logging message or object\n   */\n  info(...msg) {\n    this._log(LogType.INFO, ...msg);\n  }\n  /**\n   * Write WARN log\n   * @method\n   * @memeberof Logger\n   * @param {string|object} msg - Logging message or object\n   */\n  warn(...msg) {\n    this._log(LogType.WARN, ...msg);\n  }\n  /**\n   * Write ERROR log\n   * @method\n   * @memeberof Logger\n   * @param {string|object} msg - Logging message or object\n   */\n  error(...msg) {\n    this._log(LogType.ERROR, ...msg);\n  }\n  /**\n   * Write DEBUG log\n   * @method\n   * @memeberof Logger\n   * @param {string|object} msg - Logging message or object\n   */\n  debug(...msg) {\n    this._log(LogType.DEBUG, ...msg);\n  }\n  /**\n   * Write VERBOSE log\n   * @method\n   * @memeberof Logger\n   * @param {string|object} msg - Logging message or object\n   */\n  verbose(...msg) {\n    this._log(LogType.VERBOSE, ...msg);\n  }\n  addPluggable(pluggable) {\n    if (pluggable && pluggable.getCategoryName() === AWS_CLOUDWATCH_CATEGORY) {\n      this._pluggables.push(pluggable);\n      pluggable.configure(this._config);\n    }\n  }\n  listPluggables() {\n    return this._pluggables;\n  }\n}\nConsoleLogger.LOG_LEVEL = null;\nConsoleLogger.BIND_ALL_LOG_LEVELS = false;\nexport { ConsoleLogger };", "map": {"version": 3, "names": ["AWS_CLOUDWATCH_CATEGORY", "LogType", "LOG_LEVELS", "VERBOSE", "DEBUG", "INFO", "WARN", "ERROR", "NONE", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "name", "level", "_pluggables", "_padding", "n", "_ts", "dt", "Date", "getMinutes", "getSeconds", "join", "getMilliseconds", "configure", "config", "_config", "_log", "type", "msg", "loggerLevelName", "LOG_LEVEL", "window", "loggerLevel", "typeLevel", "log", "console", "bind", "error", "warn", "BIND_ALL_LOG_LEVELS", "info", "debug", "prefix", "message", "length", "obj", "slice", "plugin", "logEvent", "timestamp", "now", "pushLogs", "verbose", "addPluggable", "pluggable", "getCategoryName", "push", "listPluggables"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/Logger/ConsoleLogger.mjs"], "sourcesContent": ["import { AWS_CLOUDWATCH_CATEGORY } from '../constants.mjs';\nimport { LogType } from './types.mjs';\n\n/* eslint-disable no-console */\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst LOG_LEVELS = {\n    VERBOSE: 1,\n    DEBUG: 2,\n    INFO: 3,\n    WARN: 4,\n    ERROR: 5,\n    NONE: 6,\n};\n/**\n * Write logs\n * @class Logger\n */\nclass ConsoleLogger {\n    /**\n     * @constructor\n     * @param {string} name - Name of the logger\n     */\n    constructor(name, level = LogType.WARN) {\n        this.name = name;\n        this.level = level;\n        this._pluggables = [];\n    }\n    _padding(n) {\n        return n < 10 ? '0' + n : '' + n;\n    }\n    _ts() {\n        const dt = new Date();\n        return ([this._padding(dt.getMinutes()), this._padding(dt.getSeconds())].join(':') +\n            '.' +\n            dt.getMilliseconds());\n    }\n    configure(config) {\n        if (!config)\n            return this._config;\n        this._config = config;\n        return this._config;\n    }\n    /**\n     * Write log\n     * @method\n     * @memeberof Logger\n     * @param {LogType|string} type - log type, default INFO\n     * @param {string|object} msg - Logging message or object\n     */\n    _log(type, ...msg) {\n        let loggerLevelName = this.level;\n        if (ConsoleLogger.LOG_LEVEL) {\n            loggerLevelName = ConsoleLogger.LOG_LEVEL;\n        }\n        if (typeof window !== 'undefined' && window.LOG_LEVEL) {\n            loggerLevelName = window.LOG_LEVEL;\n        }\n        const loggerLevel = LOG_LEVELS[loggerLevelName];\n        const typeLevel = LOG_LEVELS[type];\n        if (!(typeLevel >= loggerLevel)) {\n            // Do nothing if type is not greater than or equal to logger level (handle undefined)\n            return;\n        }\n        let log = console.log.bind(console);\n        if (type === LogType.ERROR && console.error) {\n            log = console.error.bind(console);\n        }\n        if (type === LogType.WARN && console.warn) {\n            log = console.warn.bind(console);\n        }\n        if (ConsoleLogger.BIND_ALL_LOG_LEVELS) {\n            if (type === LogType.INFO && console.info) {\n                log = console.info.bind(console);\n            }\n            if (type === LogType.DEBUG && console.debug) {\n                log = console.debug.bind(console);\n            }\n        }\n        const prefix = `[${type}] ${this._ts()} ${this.name}`;\n        let message = '';\n        if (msg.length === 1 && typeof msg[0] === 'string') {\n            message = `${prefix} - ${msg[0]}`;\n            log(message);\n        }\n        else if (msg.length === 1) {\n            message = `${prefix} ${msg[0]}`;\n            log(prefix, msg[0]);\n        }\n        else if (typeof msg[0] === 'string') {\n            let obj = msg.slice(1);\n            if (obj.length === 1) {\n                obj = obj[0];\n            }\n            message = `${prefix} - ${msg[0]} ${obj}`;\n            log(`${prefix} - ${msg[0]}`, obj);\n        }\n        else {\n            message = `${prefix} ${msg}`;\n            log(prefix, msg);\n        }\n        for (const plugin of this._pluggables) {\n            const logEvent = { message, timestamp: Date.now() };\n            plugin.pushLogs([logEvent]);\n        }\n    }\n    /**\n     * Write General log. Default to INFO\n     * @method\n     * @memeberof Logger\n     * @param {string|object} msg - Logging message or object\n     */\n    log(...msg) {\n        this._log(LogType.INFO, ...msg);\n    }\n    /**\n     * Write INFO log\n     * @method\n     * @memeberof Logger\n     * @param {string|object} msg - Logging message or object\n     */\n    info(...msg) {\n        this._log(LogType.INFO, ...msg);\n    }\n    /**\n     * Write WARN log\n     * @method\n     * @memeberof Logger\n     * @param {string|object} msg - Logging message or object\n     */\n    warn(...msg) {\n        this._log(LogType.WARN, ...msg);\n    }\n    /**\n     * Write ERROR log\n     * @method\n     * @memeberof Logger\n     * @param {string|object} msg - Logging message or object\n     */\n    error(...msg) {\n        this._log(LogType.ERROR, ...msg);\n    }\n    /**\n     * Write DEBUG log\n     * @method\n     * @memeberof Logger\n     * @param {string|object} msg - Logging message or object\n     */\n    debug(...msg) {\n        this._log(LogType.DEBUG, ...msg);\n    }\n    /**\n     * Write VERBOSE log\n     * @method\n     * @memeberof Logger\n     * @param {string|object} msg - Logging message or object\n     */\n    verbose(...msg) {\n        this._log(LogType.VERBOSE, ...msg);\n    }\n    addPluggable(pluggable) {\n        if (pluggable && pluggable.getCategoryName() === AWS_CLOUDWATCH_CATEGORY) {\n            this._pluggables.push(pluggable);\n            pluggable.configure(this._config);\n        }\n    }\n    listPluggables() {\n        return this._pluggables;\n    }\n}\nConsoleLogger.LOG_LEVEL = null;\nConsoleLogger.BIND_ALL_LOG_LEVELS = false;\n\nexport { ConsoleLogger };\n"], "mappings": "AAAA,SAASA,uBAAuB,QAAQ,kBAAkB;AAC1D,SAASC,OAAO,QAAQ,aAAa;;AAErC;AACA;AACA;AACA,MAAMC,UAAU,GAAG;EACfC,OAAO,EAAE,CAAC;EACVC,KAAK,EAAE,CAAC;EACRC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,IAAI,EAAE;AACV,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChB;AACJ;AACA;AACA;EACIC,WAAWA,CAACC,IAAI,EAAEC,KAAK,GAAGX,OAAO,CAACK,IAAI,EAAE;IACpC,IAAI,CAACK,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,WAAW,GAAG,EAAE;EACzB;EACAC,QAAQA,CAACC,CAAC,EAAE;IACR,OAAOA,CAAC,GAAG,EAAE,GAAG,GAAG,GAAGA,CAAC,GAAG,EAAE,GAAGA,CAAC;EACpC;EACAC,GAAGA,CAAA,EAAG;IACF,MAAMC,EAAE,GAAG,IAAIC,IAAI,CAAC,CAAC;IACrB,OAAQ,CAAC,IAAI,CAACJ,QAAQ,CAACG,EAAE,CAACE,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAACL,QAAQ,CAACG,EAAE,CAACG,UAAU,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,GAC9E,GAAG,GACHJ,EAAE,CAACK,eAAe,CAAC,CAAC;EAC5B;EACAC,SAASA,CAACC,MAAM,EAAE;IACd,IAAI,CAACA,MAAM,EACP,OAAO,IAAI,CAACC,OAAO;IACvB,IAAI,CAACA,OAAO,GAAGD,MAAM;IACrB,OAAO,IAAI,CAACC,OAAO;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,IAAIA,CAACC,IAAI,EAAE,GAAGC,GAAG,EAAE;IACf,IAAIC,eAAe,GAAG,IAAI,CAACjB,KAAK;IAChC,IAAIH,aAAa,CAACqB,SAAS,EAAE;MACzBD,eAAe,GAAGpB,aAAa,CAACqB,SAAS;IAC7C;IACA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACD,SAAS,EAAE;MACnDD,eAAe,GAAGE,MAAM,CAACD,SAAS;IACtC;IACA,MAAME,WAAW,GAAG9B,UAAU,CAAC2B,eAAe,CAAC;IAC/C,MAAMI,SAAS,GAAG/B,UAAU,CAACyB,IAAI,CAAC;IAClC,IAAI,EAAEM,SAAS,IAAID,WAAW,CAAC,EAAE;MAC7B;MACA;IACJ;IACA,IAAIE,GAAG,GAAGC,OAAO,CAACD,GAAG,CAACE,IAAI,CAACD,OAAO,CAAC;IACnC,IAAIR,IAAI,KAAK1B,OAAO,CAACM,KAAK,IAAI4B,OAAO,CAACE,KAAK,EAAE;MACzCH,GAAG,GAAGC,OAAO,CAACE,KAAK,CAACD,IAAI,CAACD,OAAO,CAAC;IACrC;IACA,IAAIR,IAAI,KAAK1B,OAAO,CAACK,IAAI,IAAI6B,OAAO,CAACG,IAAI,EAAE;MACvCJ,GAAG,GAAGC,OAAO,CAACG,IAAI,CAACF,IAAI,CAACD,OAAO,CAAC;IACpC;IACA,IAAI1B,aAAa,CAAC8B,mBAAmB,EAAE;MACnC,IAAIZ,IAAI,KAAK1B,OAAO,CAACI,IAAI,IAAI8B,OAAO,CAACK,IAAI,EAAE;QACvCN,GAAG,GAAGC,OAAO,CAACK,IAAI,CAACJ,IAAI,CAACD,OAAO,CAAC;MACpC;MACA,IAAIR,IAAI,KAAK1B,OAAO,CAACG,KAAK,IAAI+B,OAAO,CAACM,KAAK,EAAE;QACzCP,GAAG,GAAGC,OAAO,CAACM,KAAK,CAACL,IAAI,CAACD,OAAO,CAAC;MACrC;IACJ;IACA,MAAMO,MAAM,GAAG,IAAIf,IAAI,KAAK,IAAI,CAACX,GAAG,CAAC,CAAC,IAAI,IAAI,CAACL,IAAI,EAAE;IACrD,IAAIgC,OAAO,GAAG,EAAE;IAChB,IAAIf,GAAG,CAACgB,MAAM,KAAK,CAAC,IAAI,OAAOhB,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MAChDe,OAAO,GAAG,GAAGD,MAAM,MAAMd,GAAG,CAAC,CAAC,CAAC,EAAE;MACjCM,GAAG,CAACS,OAAO,CAAC;IAChB,CAAC,MACI,IAAIf,GAAG,CAACgB,MAAM,KAAK,CAAC,EAAE;MACvBD,OAAO,GAAG,GAAGD,MAAM,IAAId,GAAG,CAAC,CAAC,CAAC,EAAE;MAC/BM,GAAG,CAACQ,MAAM,EAAEd,GAAG,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,MACI,IAAI,OAAOA,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACjC,IAAIiB,GAAG,GAAGjB,GAAG,CAACkB,KAAK,CAAC,CAAC,CAAC;MACtB,IAAID,GAAG,CAACD,MAAM,KAAK,CAAC,EAAE;QAClBC,GAAG,GAAGA,GAAG,CAAC,CAAC,CAAC;MAChB;MACAF,OAAO,GAAG,GAAGD,MAAM,MAAMd,GAAG,CAAC,CAAC,CAAC,IAAIiB,GAAG,EAAE;MACxCX,GAAG,CAAC,GAAGQ,MAAM,MAAMd,GAAG,CAAC,CAAC,CAAC,EAAE,EAAEiB,GAAG,CAAC;IACrC,CAAC,MACI;MACDF,OAAO,GAAG,GAAGD,MAAM,IAAId,GAAG,EAAE;MAC5BM,GAAG,CAACQ,MAAM,EAAEd,GAAG,CAAC;IACpB;IACA,KAAK,MAAMmB,MAAM,IAAI,IAAI,CAAClC,WAAW,EAAE;MACnC,MAAMmC,QAAQ,GAAG;QAAEL,OAAO;QAAEM,SAAS,EAAE/B,IAAI,CAACgC,GAAG,CAAC;MAAE,CAAC;MACnDH,MAAM,CAACI,QAAQ,CAAC,CAACH,QAAQ,CAAC,CAAC;IAC/B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACId,GAAGA,CAAC,GAAGN,GAAG,EAAE;IACR,IAAI,CAACF,IAAI,CAACzB,OAAO,CAACI,IAAI,EAAE,GAAGuB,GAAG,CAAC;EACnC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIY,IAAIA,CAAC,GAAGZ,GAAG,EAAE;IACT,IAAI,CAACF,IAAI,CAACzB,OAAO,CAACI,IAAI,EAAE,GAAGuB,GAAG,CAAC;EACnC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIU,IAAIA,CAAC,GAAGV,GAAG,EAAE;IACT,IAAI,CAACF,IAAI,CAACzB,OAAO,CAACK,IAAI,EAAE,GAAGsB,GAAG,CAAC;EACnC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIS,KAAKA,CAAC,GAAGT,GAAG,EAAE;IACV,IAAI,CAACF,IAAI,CAACzB,OAAO,CAACM,KAAK,EAAE,GAAGqB,GAAG,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIa,KAAKA,CAAC,GAAGb,GAAG,EAAE;IACV,IAAI,CAACF,IAAI,CAACzB,OAAO,CAACG,KAAK,EAAE,GAAGwB,GAAG,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIwB,OAAOA,CAAC,GAAGxB,GAAG,EAAE;IACZ,IAAI,CAACF,IAAI,CAACzB,OAAO,CAACE,OAAO,EAAE,GAAGyB,GAAG,CAAC;EACtC;EACAyB,YAAYA,CAACC,SAAS,EAAE;IACpB,IAAIA,SAAS,IAAIA,SAAS,CAACC,eAAe,CAAC,CAAC,KAAKvD,uBAAuB,EAAE;MACtE,IAAI,CAACa,WAAW,CAAC2C,IAAI,CAACF,SAAS,CAAC;MAChCA,SAAS,CAAC/B,SAAS,CAAC,IAAI,CAACE,OAAO,CAAC;IACrC;EACJ;EACAgC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC5C,WAAW;EAC3B;AACJ;AACAJ,aAAa,CAACqB,SAAS,GAAG,IAAI;AAC9BrB,aAAa,CAAC8B,mBAAmB,GAAG,KAAK;AAEzC,SAAS9B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}