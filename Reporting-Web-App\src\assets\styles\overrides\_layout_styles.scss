* {
  outline: none;
}

body.saltbox-reporting.sbreport{

  // Text Colors
  .text-saltbox-blue{
    color: var(--saltbox-blue) !important;
  }
  .text-saltbox-red{
    color: var(--saltbox-red) !important;
  }

  .text-base-text{
    color: var(--BaseText) !important;
  }
  .text-light{
    color: var(--TextLight) !important;
  }
  .text-dark{
    color: var(--TextDark) !important;
  }
  .text-text-secondary{
    color: var(--text-secondary) !important;
  }
  .text-placeholder-text{
    color: var(--placeholderText) !important;
  }
  .text-inverse-text{
    color: var(--InverseText) !important;
  }

  .text-border-color{
    color: var(--BorderColor) !important;
  }

  .text-primary-override{
    color: var(--primary) !important;
  }
  .text-primary-active{
    color: var(--primaryActive) !important;
  }
  .text-secondary{
    color: var(--secondary) !important;
  }
  .text-secondary-active{
    color: var(--secondaryActive) !important;
  }
  .text-secondary-shade-1{
    color: var(--secondaryShade1) !important;
  }
  .text-secondary-shade-2{
    color: var(--secondaryShade2) !important;
  }
  .text-secondary-shade-3{
    color: var(--secondaryShade3) !important;
  }

  .text-surface-primary{
    color: var(--surfacePrimary) !important;
  }
  .text-surface-primary-alt{
    color: var(--surfacePrimaryAlt) !important;
  }
  .text-surface-secondary{
    color: var(--surfaceSecondary) !important;
  }
  .text-surface-secondary-alt{
    color: var(--surfaceSecondaryAlt) !important;
  }
  .text-highlight-primary{
    color: var(--highlightPrimary) !important;
  }
  .text-highlight-high-contrast{
    color: var(--highlightHighContrast) !important;
  }
  .text-surface-nav{
    color: var(--surfaceNav) !important;
  }
  .text-surface-blue{
    color: var(--surfaceBlue) !important;
  }
  .text-surface-green{
    color: var(--surfaceGreen) !important;
  }
  .text-surface-yellow{
    color: var(--surfaceYellow) !important;
  }
  .text-surface-orange{
    color: var(--surfaceOrange) !important;
  }
  .text-surface-red{
    color: var(--surfaceRed) !important;
  }

  .text-success{
    color: var(--success) !important;
  }
  .text-success-light{
    color: var(--successLight) !important;
  }
  .text-success-dark{
    color: var(--successDark) !important;
  }
  .text-success-color-dull{
    color: var(--successColorDull) !important;
  }

  .text-warning{
    color: var(--warning) !important;
  }
  .text-warning-light{
    color: var(--warningLight) !important;
  }
  .text-warning-dark{
    color: var(--warningDark) !important;
  }

  .text-danger{
    color: var(--danger) !important;
  }
  .text-danger-light{
    color: var(--dangerLight) !important;
  }
  .text-danger-dark{
    color: var(--dangerDark) !important;
  }
  .text-fail-color-dull{
    color: var(--FailColorDull) !important;
  }

  .text-data-1{
    color: var(--data1) !important;
  }
  .text-data-2{
    color: var(--data2) !important;
  }
  .text-data-3{
    color: var(--data3) !important;
  }
  .text-data-4{
    color: var(--data4) !important;
  }
  .text-data-5{
    color: var(--data5) !important;
  }
  .text-data-6{
    color: var(--data6) !important;
  }
  .text-data-7{
    color: var(--data7) !important;
  }
  .text-data-8{
    color: var(--data8) !important;
  }
  .text-data-9{
    color: var(--data9) !important;
  }


  //BG Background Colors
  .bg-saltbox-blue{
    background-color: var(--saltbox-blue) !important;
  }
  .bg-saltbox-red{
    background-color: var(--saltbox-red) !important;
  }

  .bg-base-text{
    background-color: var(--BaseText) !important;
  }
  .bg-text-light{
    background-color: var(--TextLight) !important;
  }
  .bg-text-dark{
    background-color: var(--TextDark) !important;
  }
  .bg-text-secondary{
    background-color: var(--text-secondary) !important;
  }
  .bg-placeholder-text{
    background-color: var(--placeholderText) !important;
  }
  .bg-inverse-text{
    background-color: var(--InverseText) !important;
  }

  .bg-border-color{
    background-color: var(--BorderColor) !important;
  }

  .bg-primary-override{
    background-color: var(--primary) !important;
  }
  .bg-primary-active{
    background-color: var(--primaryActive) !important;
  }

  .bg-secondary{
    background-color: var(--secondary) !important;
  }
  .bg-secondary-active{
    background-color: var(--secondaryActive) !important;
  }
  .bg-secondary-shade-1{
    background-color: var(--secondaryShade1) !important;
  }
  .bg-secondary-shade-2{
    background-color: var(--secondaryShade2) !important;
  }
  .bg-secondary-shade-3{
    background-color: var(--secondaryShade3) !important;
  }

  .bg-shade-2{
    background-color: var(--bgShade2) !important;
  }
  .bg-shade-3{
    background-color: var(--bgShade3) !important;
  }
  .bg-shade-4{
    background-color: var(--bgShade4) !important;
  }
  .bg-inverse{
    background-color: var(--bgInverse) !important;
  }
  .bg-inverse-shade-1{
    background-color: var(--bgInverseShade1) !important;
  }
  .bg-inverse-shade-2{
    background-color: var(--bgInverseShade2) !important;
  }
  .bg-inverse-shade-3{
    background-color: var(--bgInverseShade3) !important;
  }

  .bg-surface-primary{
    background-color: var(--surfacePrimary) !important;
  }
  .bg-surface-primary-alt{
    background-color: var(--surfacePrimaryAlt) !important;
  }
  .bg-surface-secondary{
    background-color: var(--surfaceSecondary) !important;
  }
  .bg-surface-secondary-alt{
    background-color: var(--surfaceSecondaryAlt) !important;
  }
  .bg-highlight-primary{
    background-color: var(--highlightPrimary) !important;
  }
  .bg-highlight-high-contrast{
    background-color: var(--highlightHighContrast) !important;
  }
  .bg-surface-nav{
    background-color: var(--surfaceNav) !important;
  }
  .bg-surface-blue{
    background-color: var(--surfaceBlue) !important;
  }
  .bg-surface-green{
    background-color: var(--surfaceGreen) !important;
  }
  .bg-surface-yellow{
    background-color: var(--surfaceYellow) !important;
  }
  .bg-surface-orange{
    background-color: var(--surfaceOrange) !important;
  }
  .bg-surface-red{
    background-color: var(--surfaceRed) !important;
  }

  .bg-success{
    background-color: var(--success) !important;
  }
  .bg-success-light{
    background-color: var(--successLight) !important;
  }
  .bg-success-dark{
    background-color: var(--successDark) !important;
  }
  .bg-success-color-dull{
    background-color: var(--successColorDull) !important;
  }

  .bg-warning{
    background-color: var(--warning) !important;
  }
  .bg-warning-light{
    background-color: var(--warningLight) !important;
  }
  .bg-warning-dark{
    background-color: var(--warningDark) !important;
  }

  .bg-danger{
    background-color: var(--danger) !important;
  }
  .bg-danger-light{
    background-color: var(--dangerLight) !important;
  }
  .bg-danger-dark{
    background-color: var(--dangerDark) !important;
  }
  .bg-fail-color-dull{
    background-color: var(--FailColorDull) !important;
  }

  .bg-data-1{
    background-color: var(--data1) !important;
  }
  .bg-data-2{
    background-color: var(--data2) !important;
  }
  .bg-data-3{
    background-color: var(--data3) !important;
  }
  .bg-data-4{
    background-color: var(--data4) !important;
  }
  .bg-data-5{
    background-color: var(--data5) !important;
  }
  .bg-data-6{
    background-color: var(--data6) !important;
  }
  .bg-data-7{
    background-color: var(--data7) !important;
  }
  .bg-data-8{
    background-color: var(--data8) !important;
  }
  .bg-data-9{
    background-color: var(--data9) !important;
  }

/*Wizard Styles*/

.wizard-wrapper{
  background-color: var(--surfaceSecondary);

  .col-11{
    max-width: calc(100% - 80px);

    .col-11{
      max-width: initial;
    }

    .field{
      max-width: 100%;
    }

    p-inputnumber, .p-inputnumber{
      width: 100%;
    }
  }

  .tree-card{
    .p-card{
      .p-card-body{
        padding: 0;
      }
    }
  }

  .p-float-label.search-box{
    background: var(--primary);
    max-width: 660px;
    color: var(--InverseText);

    input{
      color: var(--InverseText);
    }

    .pi-icon-search{
      vertical-align: middle;
    }

    label{
      color: var(--InverseText);
    }

    .p-inputwrapper-filled ~ label, .p-inputwrapper-focus ~ label{
      color: var(--primary);
    }

  }

  &.add-new-project{
    &.p-dialog{
      .p-dialog-content{
        .p-panel-content{
          max-height: 60vh;
          overflow-x: hidden;
          overflow-y: auto;
        }
      }
    }
  }

 .p-tabview .p-tabview-panels{
  padding: 0;
  max-height: 450px;
 }
 .cron .p-tabview .p-tabview-panels{
  max-height: initial;
 }

  &.p-dialog{

    min-width: 400px;

    .p-dialog-content{
      background-color: var(--surfaceSecondary);
      padding: 0 0 0 1.5rem !important;
      overflow: hidden; //needs to be hidden or content overflows on resize
    }

    .p-dialog-footer{
      padding: 0.714em 1em !important;
      font-size: 14px;
      background-color: var(--surfaceSecondary)
    }
  }

  .label{
    color: var(--primary);
  }

  .col-11{
    .grid{
      margin: 0;
    }
  }

  h3{
    margin: 0.5rem 0;
    color: var(--primary);
    display: flex;
    font-size: 1em;
    font-weight: normal;

    i{
      margin-right: 5px;
    }
  }

  .p-toolbar-secondary h3{
    color: var(--InverseText);
    margin: 0;
  }

  .p-panel {
    p-header{
      width: 100%;
    }

    .p-panel-header {
      color: var(--primary);
      background: var(--surfaceSecondary);
      padding: 0;

      .p-panel-title{
        line-height: 2;
        font-size: 0.95rem;
      }
      .square-icon{
        float: right;
        clear:right;
      }
    }
    .p-dropdown{
      padding-left: 7px;
    }
  }


  .p-tabview-nav-link{
    padding: 0.75rem !important;
  }

  .btn-config {
    margin-top: 1rem;
  }

  .p-dropdown{
    display: flex;
  }

  .p-card-content{
    padding: 0;
  }

  .p-button.p-button-icon-only{
    &.square-icon{
      padding: 0.3rem;
      width: auto;
      min-width: fit-content !important;
      text-align: center;
      border-radius: 0 !important;
      &.mt-0{
        margin-top: 0;
      }
    }
  }

  p-header{
    margin-left: 0 !important;
  }

  input, .p-dropdown, .p-multiselect, .p-autocomplete, textarea{
    width: 100%;
    max-width: 100%;
    min-width: 30rem;

    &#iconTextbox{
      min-width: 28rem;
    }
  }

  .p-fileupload {
    p-messages {
      display: none;
    }

    .p-fileupload-choose {
      margin-top: 8px;

      .pi {
        transition: all 0.8s;
      }
    }
  }

  .scrollPanel-wrap {
    hr {
      margin: 16px 0;
      border: none;
      border-bottom: 2px dashed var(--primaryActive);
      width: 100%;
    }

    .p-scrollpanel {
      border: none !important;
      padding: 0 5px;
    }
  }


  .p-treetable {
    .p-treetable-tbody{
      display: block;
      tr {
        display: block;
        border: 0;
        pointer-events: none;
        background-color: var(--surfacePrimary);

        &.p-highlight {
          background: inherit !important;
        }

        &.p-highlight>td {
          background: inherit;
          border: var(--dropShadowMain);
        }

        td{
          border: 0;
          font-size: 14px;
          display: flex;
          justify-content: space-between;
          border-bottom: 1px solid var(--bgShade2);
          padding: 0;
          background-color: var(--surfacePrimary);

          div{
            display: flex;
            align-items: center;
            flex: 1;

            p-treetabletoggler {
              pointer-events: visible;
            }

            div{
              display: contents;
            }

            .pi-minus{
              color: var(--bgShade2);
            }

            input[type=checkbox] {
              width: 20px !important;
              height: 18px;
              pointer-events: visible;
              min-width: initial;
            }
          }
        }
      }
    }
  }

  .include-btn {
    font-size: 10px;
    padding: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 45px;
    max-width: 45px;
    cursor: pointer;
    background-color: var(--primary);
    color: var(--InverseText);

    .pi{
      color: var(--InverseText) !important;
    }
  }

  .exclude-btn {
    background: var(--surfaceSecondary);
    color: var(--bgInverseShade1);
    max-width: 45px;
  }

  .new{
    background-color: var(--surfaceSecondary);
    border: 1px solid var(--BorderColor);
    border-radius: 10px;
    padding: 0px 7px;
    font-size: 12px;
  }

.empty-notification input {
  color: var(--warningLight);
  font-size: 12px;
}

  .p-card-content{
    .empty-content {
      .watermark-cust {
          background-color: transparent;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          .content{
              margin: 0;
              strong {
                font-weight: 500;
                opacity: 1;
              }
          }
          .saltbox-watermark {
            width: 30px;
            height: 30px;
            position: relative;
            margin-bottom: 0.5rem;
            svg {
              opacity: .5!important;
            }
          }
        }
    }
  }
}

.wizard-menu {
  padding: 1.5em 10px 1.5em 0;
  min-width: 80px;
  max-width: 80px;

  .col-12 {
    margin-bottom: 0.5rem;
    border-radius: 6px;
    background: var(--surfacePrimary);
    box-shadow: 0 1px 3px 2px var(--dropShadowStrong);
    padding: 0;

    a {
      display: flex;
      color: var(--BaseText);
      justify-content: center;
      align-items: center;
      padding: 1.3rem;
      font-size: 1.2rem;

      svg {
        path {
          fill: var(--primary);
        }
      }
    }

    a.disabled {
      background: var(--surfaceSecondary) !important;
      color: var(--bgShade3)!important;
      fill: var(--bgShade3)!important;
      cursor: unset;

      svg {
        path {
          background: var(--surfaceSecondary) !important;
          color: var(--bgShade3)!important;
          fill: var(--bgShade3)!important;
        }
      }
    }
  }

}

p-header img, p-header i {
  max-width: 2rem;
  vertical-align: middle;
  margin-right: 0.9rem;
  margin-left: 0;
}

/**/

/*Remove after navigation app clean up*/
.horizontal-sub-nav .sub-title{
  padding: 0 0.5rem;
}

.section-description {
  margin: 0.5rem 0;
  padding: 0 0.5rem;
  width: 100%;
  border-left: 0.25rem solid var(--primary);
  display: inline-block;

  &.description-gray {
    border-color: var(--bgShade3);
  }
  &.description-yellow {
    border-color: var(--warningLight);
  }
  &.description-red {
    border-color: var(--danger);
  }
  &.grey{
    border-color: var(--BorderColor);
    color: var(--text-secondary);
  }

  p {
    font-size: 0.875rem;
  }

  p:first-of-type {
    margin-top: 0px;
  }

  p:last-of-type {
    margin-bottom: 0px;
  }
}

.description-complex {
  p:first-of-type {
    margin-top: 0px;
  }

  p:last-of-type {
    margin-bottom: 0px;
  }
}

.p-treenode-droppoint {
  border: 1px solid transparent;
  height: auto;
}

.p-treenode-dragover {
  .p-treenode-droppoint {
    border: 1px dashed var(--primary) !important;
    padding: 0.5rem;
    background-color: var(--surfaceBlue);
    margin: 0.5rem;

    &.p-treenode-droppoint-active {
      border: 1px dashed var(--primary) !important;
      padding: 0.5rem;
      background-color: var(--surfaceBlue);
      margin: 0.5rem;
    }
  }
}

.p-datatable th{
  background: var(--bgShade2);
  border-width: 0;
}

.p-datatable .p-datatable-thead>tr>th:not(:last-of-type) {
  border-right: 1px solid var(--BorderColor);
}

/* Setting Menu */

.tab-view {
  position: relative;

  &.col{
    max-width: calc(100vw - 12rem) !important;
  }

  .p-tabview .p-tabview-panels{
    padding: 0 0.75rem;
  }
}

.settings-menu {
  .p-button{
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1.5rem;
    height: 100%;
    width: 100%;
    border-radius: 6px;
    padding: 0;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 1px 3px 2px var(--dropShadowStrong) !important;
    border: none;
    .pi{
      font-size: 1.7rem;
    }
    &.p-button-primary, &:hover, &:active, &:focus{
      background-color: var(--primary);
      color: var(--InverseText);
    }
  }
}


  .img-ip-icon-wrapper {
    display: inline-block;
    width: 32px;
    height: 32px;
    padding: 0;
    margin-left: -10px;
    margin-right: 5px;
    background: transparent;
  }

  /*Tabbed Sub Navigation*/
  .tabbed-sub-nav {
    position: absolute;
    right: 17px;
    top: 0px;
    z-index: 1;
  }

  .tabbed-sub-nav a,
  .tabbed-sub-nav i {
    line-height: 12px;
    vertical-align: top;
    color: var(--BaseText);
  }


  .tabbed-sub-nav a {
    font-size: 14px;
    padding: 15px 10px 0 10px;
    display: inline-block;
    height: 39px;
  }

  .tabbed-sub-nav .p-icon-call-split {
    transform: rotate(180deg);
    margin-top: -10px;
  }

  /*Clearifx*/
  .clearfix {
    clear: both;
  }

  .message-empty {
    width: 100%;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;

    .content {
      z-index: 2;
      position: absolute;
    }
  }

  tabs-widget > ul li {
    display: inline-block;
    padding: 8px 4px;
    border: 1px solid transparent;
  }

  tabs-widget > ul {
    padding: 0;
  }

  tabs-widget fieldset:not(fieldset fieldset) {
    border: 1px solid transparent;
  }

  tabs-widget fieldset:not(fieldset fieldset) > legend {
    display: none;
    margin: 0;
    padding: 0;
  }

  .no-border,
  .no-border .p-panel {
    border: none !important;
    box-shadow: none;
  }

  /*Add Scrolling on Reports*/

  .ag-root-wrapper.ag-layout-normal.ag-ltr {
    overflow: auto !important;
  }

  /**/

  .embedInputs input {
    width: 98%;
    height: 30pt;
  }

  /*Filter List Styling*/
  .filters-list .p-dropdown-items-wrapper p-dropdownitem:first-of-type div,
  .filters-list .p-dropdown-items-wrapper p-dropdownitem:last-of-type div {
    color: var(--dropShadowLight);
    font-weight: 400;
  }

  /*hide extra tabs on report setings*/

  .report-settings{ 
    .p-tabview-nav {
      display: none;
    }

    .p-panel {
      box-shadow: none;
    }
  }

  /*Visualization Admin*/

  .selected-tile-setup{
    max-height: calc(100vh - 20rem);
    overflow: auto;
  }

  /*Toast Message Wrapping Fix*/
  .p-toast .p-toast-summary {
    word-break: normal;
  }

  /*Toast message style adjustments*/
  .p-toast-detail {
    white-space: pre-line;
  }

  .p-toast .p-toast-message-content {
    padding: 0.5em 2em 0.5em 1em;
  }

.drill__table {
  .p-datatable-wrapper {
    border-radius: var(--border-radius);
    border: var(--BorderColor) solid 1px;
  }
  .p-datatable-header{
    padding: 0;
    background: transparent !important;
    &+.p-datatable-wrapper {
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      margin-top: -1px;
    }
  }
  .p-datatable-no-margin{
    .p-datatable{
      margin: 0;
    }
  }
  table {
    .p-datatable-wrapper {
      border-radius: 0;
      overflow: hidden;
      margin-top: 0;
      border: none;
    }
    .drill-url {
      width: 72%;
      border-top: none !important;
    }
    .drill-head {
      th {
        background: var(--bgShade2);
        border-right: var(--BorderColor) 1px solid;
        &:first-child,&:last-child {
          width: 5%;
        }
        &:last-child {
          border-right: none;
        }
        &:first-child {
          text-align: center;
        }
      }
    }
    .drill-table-custom-tr {
      vertical-align: top;
    }
    tr {
      &:nth-child(odd) {
        background: var(--InverseText);
      }
      &:last-child {
        td {
          border-bottom: none;
        }
      }
    }
    .drill-url-substitution {
      tr {
        background: none;
        &:nth-child(odd) {
          background: none;
        }
        td {
          border-width: 0 0 1px;
        }
      }
    }
    table {
      .drill-url-substitution-tr {
        border-bottom: 1px solid var(--BorderColor);
        &:last-child {
          border: none;
        }
        td {
          &:nth-child(2), &:nth-child(3) {
            padding: 0;
            float: left;
            margin-top: 18px;
            border: none;
          }
        }
        &:nth-child(even) {
          background: none;
        }
      }
    }
  }
}

.columns-table {
  .p-datatable-wrapper {
    border-radius: var(--border-radius);
    overflow: hidden;
    border: var(--BorderColor) solid 1px;
  }
  .p-datatable-thead {
    tr {
      background: var(--bgShade2);
      border-bottom: 1px solid var(--BorderColor);
      td {
        padding: 15px;
        border-right: 1px solid var(--BorderColor);
        color: var(--BaseText);
        font-weight: 600;
      }
    }
  }
  .p-datatable-tbody {
    tr {
      border-bottom: 1px solid var(--BorderColor);
      td {
        border-color: var(--BorderColor);
      }
    }
  }
  .border-less {
    border: none !important;
  }
}

.pi.pi-arrow-down:before {
  content: "\e919" !important;
}

.pivot__btn::before {
  content: "";
  position: absolute;
  left: 20px;
  top: 7px;
  z-index: 9;
  width: 15px;
  height: 15px;
}

.pivot__ctrl__btn {
  width: 155px;
  padding-left: 42px;
  margin-left: 15px;
}

.p0 {
  padding: 0;
}


.chart_button {
  position: absolute;
  top: 7px;
  z-index: 1;
  width: 100px;
  .p-dropdown {
    background: var(--secondary);
    color: var(--surfacePrimary);
    height: 35px;
    width: 100%;
    position: relative;
    &::before {
      content: "";
      position: absolute;
      left: 20px;
      top: 7px;
      z-index: 9;
      width: 15px;
      height: 15px;
    }
    &:hover, &:focus, &:not(.p-disabled):hover {
      background: transparent !important;
      color: var(--surfacePrimary) !important;
    }
  }
}

.pivot__btn {
  &:hover, &:focus {
    background: var(--primary) !important;
    color: var(--surfacePrimary) !important;
  }
}

.chart_button {
  .p-dropdown {
    .p-dropdown-label.p-placeholder {
      color: var(--InverseText);
    }
    .p-dropdown-trigger {
      display: none;
    }
  }
  .p-inputtext {
    padding: 6px;
  }
  .p-dropdown.p-dropdown-clearable .p-dropdown-label {
    padding-right: 0;
    padding-left: 40px;
  }
}
.pivot__btn {
  position: absolute;
  right: 40px;
  top: 7px;
  z-index: 1;
  background: var(--secondary);
  color: var(--surfacePrimary);
  padding: 4px 10px;
  height: 35px;
  img {
    padding-right: 8px;
  }
}

.reporting-parent {
  margin-left: 5.3em;
  margin-top: 1.5rem;
  padding: 0 0.5rem
}

.uep {
  margin: -0.8rem -0.7rem;
  position: relative;

  .p-accordion .p-accordion-tab-active>.p-toggleable-content {
    position: absolute !important;
    top: 2.5rem;
    left: 0;
    width: 100%;
    z-index: 999;
    height: fit-content;
    box-shadow: 5px 4px 7px var(--BorderColor);
  }
}

.menu-layout-static {
  .report__view__chart {
    .uep {
      padding: 0 6px;
    }
  }
  .report__view__pivot {
    .chart_button {
      right: 550px;
    }
    .pivot__ctrl__btn {
      right: 384px;
    }
  }
  .report__viewer {
    .report__view__pivot {
      .pivot__ctrl__btn {
        right: 410px;
      }
      .chart_button {
        right: 576px;
      }
    }
  }

  .report__view__grid {
    .chart_button {
      right: 20px;
    }
  }
}

.layout-menu-static-inactive {
  .report__view__chart {
    .uep {
      padding: 0 6px;
    }
  }
  .report__view__grid {
    .chart_button {
      right: 25px;
    }
  }
  .report__view__pivot {
    .chart_button {
      right: 282px;
    }
    .pivot__ctrl__btn {
      right: 115px;
    }
  }
  .report__viewer {
    .report__view__pivot {
      .pivot__ctrl__btn {
        right: 155px;
      }
      .chart_button {
        right: 322px;
      }
    }
  }
}

.p-overflow-hidden {
  overflow-y: scroll;
}
.circle-tick {
    float: left;

    &::before {
        background: var(--surfacePrimary);
        border-radius: 50%;
        color: var(--success);
        padding: 3px;
    }
}

.msg-ui {
    float: left;
    margin-top: 0 !important;
    padding: 3px 15px !important;
}

.analytical-data-filter {
  display: flex;
  justify-content: space-between;
  .analytical-data-filter-dropdown {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

app-report-view-main p-toolbar{
  margin-left: -0.5rem;
}

.grid-pivot-chart-btn-ui {
  .p-dropdown:not(.p-disabled).p-focus {
    box-shadow: none;
  }
  .chart-btn {
    width: 60px;
    border: var(--surfacePrimary) 1px solid;
    position: relative;
    border-radius: 1rem;
    height: 2rem;
    margin: 0 0.25rem;
    i {
      position: absolute;
      left: 8px;
      top: 50%;
      z-index: 9;
      transform: translateY(-50%);
    }
    .p-dropdown {
      background: transparent !important;
      border: 0 !important;
      height: 100%;
      .p-dropdown-trigger {
        width: auto;
        color: var(--surfacePrimary);
        height: 2rem;
        margin: 0 0.3rem 0 0;
      }
    }
    .p-inputwrapper-focus {
      .p-dropdown-trigger {
        color: var(--InverseText);
      }
      .p-dropdown {
        background: var(--surfacePrimary);
        border-radius: 1rem;
        height: 2rem;
      }
    }
    &:hover:not(.disabled) {
      background: var(--primaryActive);
      color: var(--InverseText);
      .p-dropdown-trigger {
        color: var(--InverseText);
      }
      i{
        color: var(--InverseText) !important;
      }
    }
    &:focus-within i {
      color: var(--InverseText) !important;
    }
    .p-dropdown.p-dropdown-clearable .p-dropdown-label, .p-dropdown .p-dropdown-clear-icon {
      color: transparent;
    }
    &.disabled{
      opacity: 0.5;
    }
  }
  .custom-split-btn {
    display: flex;
    align-items: center;
    border: var(--surfacePrimary) 1px solid;
    border-radius: 1rem;
    height: 2rem;
    margin: 0 0.25rem;
    .custom-split-btn-icon {
      border-right: 1px solid var(--surfacePrimary);
      padding: 0 0.5rem;
      display: inline-flex;
    }
    p-dropdown {
      width: 100%;
      height: 100%;
      color: black;
      transition: all .5s;
      border-top-right-radius: 1rem;
      border-bottom-right-radius: 1rem;
      &.p-inputwrapper-focus, &:hover {
        background: var(--primaryActive);
        .p-dropdown-trigger {
          color: var(--InverseText);
        }
      }
    }
    .p-dropdown-item-group, .p-dropdown .p-dropdown-label {
      display: none;
    }
    .p-dropdown {
      background: transparent !important;
      border: none !important;
      display: inline;
      .p-dropdown-trigger {
        color: var(--InverseText);
        padding: 0.5rem 0.3rem;
        width: auto;
      }
      .p-dropdown-panel {
        .p-dropdown-items .p-dropdown-item {
          border-bottom: var(--bgInverse) solid 1px;
          &:last-child {
            border: none;
          }
        }
      }
    }
    &.disabled{
      opacity: 0.5;
    }
  }
}

.splitBtnPanel {
  margin-left: 0rem;
  width: 175px;
  font-size: 0.875rem;
  .p-dropdown-item-group {
    display: none;
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item{
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
  .layout-list {
    display: flex;
    align-items: center;
  }
  .custom-split-btn-options {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .custom-split-btn-options-label {
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .unsaved-view {
    border: solid var(--surfaceSecondaryAlt);
    border-width: 1px 0;
  }
}
.textarea-full {
  width: 100%;
}

.sbi-frame-holder {
  border:0;
  margin:0 -0.75rem 0 -0.75rem;
  height: calc(100vh - 15vh);
  width: auto;
}

.sbi-frame-holder iframe {
  border:none;
}

.format-columns-data .p-dropdown{
  max-height: 3rem;
  width: 8rem;
}

/* Formula Custom Renderer Styles for the Formula button and cell */
/* wrapper for (formula-icon) and radius to a div and adds a border */
/* Base icon loaded in formula-icon is in dynamic.scss */
.formula-cell {
  width: 100%;
  height:100%;
}
.formula-icon-holder {
  border: 1px solid var(--primary);
  border-radius: 1em;
  width: 2em;
  height: 2em;
  opacity: .4;
  margin:0 auto;
}
.formula-icon-holder:hover {
  cursor: pointer;
  opacity: 1;
}
.formula-icon-holder-active {
  border: 1px solid var(--primary);
  border-radius: 1em;
  width: 2em;
  height: 2em;
  opacity: 1;
  margin:0 auto;
  background-color: var(--secondaryActive);
}
.formula-icon-holder-active:hover {
  cursor: pointer;
  opacity: 1;
  background-color: var(--secondaryActive);
}
.formula-icon {
  margin-top: .5em;
  margin-left: .55em;
  width: 3rem;
  height: 3rem;
}

/*Moved from layout_blue.scss*/

label.page{
  font-size: 11pt;
  margin: 0 3pt;
}

.export-btn-list{
  min-width: 4rem;
}

.chart-menu-list,
.export-btn-list {
  position: relative;
  height: 25px;
  .chart-menu-list-box,
  .export-btn-list-box {
    font-size: 14px;
    float: left;
    margin: 4px 10px 0 0;
    a {
      color: var(--primary);
    }
  }
  .chart-menu-data-type,
  .chart-menu-no-data-type,
  .export-btn-list-box {
    font-size: 14px; 
    float: left; 
    margin-top: 4px; 
    color: var(--BaseText);
  }
  .chart-menu-no-data-type {
    color: var(--primary);
  }
  hr {
    color: var(--surfacePrimaryAlt);
  }
}

.filter-btn-list {
  font-size:14px;
  float:left;
}

.drill-generated-link {
  overflow-wrap: break-word;
}

.page-size {
  left: 75px;
}

.pin-container {
  transition: transform 0.25s ease-in-out;
  width: 20px;
  float: left;
  margin: 0 0 0 -6px;
}

.pin-container.not-active {
  transform: rotate(0deg);
  opacity: 0.2;
}

.pin-container.not-active:hover {
  transform: rotate(-10deg);
  opacity: 0.6;
}

.pin-container.is-active {
  transform: rotate(-20deg);
}

.pin-container svg {
  width: 12px;
  fill: var(--primary);
}
.report-setup-drop-down{
  display: contents;
  width: 130px;
  .p-dropdown{
    max-width: 130px;
    .p-dropdown-panel{
      width: inherit;
    }
  }
}
.connection-settings-drop-down{
  .p-dropdown-open{
    .p-dropdown-panel{
      width: -webkit-fill-available;
    }
  }
}
.chart-menu-list{
  border-bottom: 1px solid var(--BorderColor);
  padding: 0 0 30px 0;
  margin: -0.5rem 0;
}
.format-data-input{
  .p-inputnumber{
    .p-inputnumber-input{
      padding: 0px 0px 0px 15px;
      min-width:10px;
    }
    .p-inputnumber-button-group{
      margin: 0 -4px 0 0;
    }
  }
}
.report-email-text-area{
  height: fit-content;
}
.decimal-places-inbox{
  width: 100px;
}

.report-setup{
  .p-datatable-wrapper{
    border: var(--BorderColor) solid 1px;
    border-radius: var(--border-radius);
  }
}

.panel-actionable-grid-format-dropdown{
  width: 135px;
}

.p-dialog-default-value{
  width: 500px;
}

.icon-selector-container {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
  height: 200px;
  overflow-y: auto;
}

.link-values-config {
  width: 94rem;

  .substitution-button {
    width: 3.5rem;
  }
}

.url-substitution-values {
  width: 30rem;
}

.grid-panel-setting{
  .p-panel-content{
    padding-top: 0;
  }
}
.calendar-panel-setting{
  .p-panel-content{
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 0.0625rem;
    padding-right: 0.0625rem;
  }
}
.custom__param__setup{
  padding-top: 0.563rem;
  .form-group{
    margin: -0.563rem -0.875rem;
  }
}
.overlay-button {
  pointer-events: revert;
  position: absolute;
  right: 2rem;
  height: 100%;
  z-index: 997 !important;
  span {
    height: 100%;
    flex-direction: column;
    justify-content: space-around;
    font-size: 1.125rem;
    span {
      padding-left: .125rem;
      margin-top: 1.25rem;
    }
  }
}

.color-picker-input-group {
  .p-colorpicker{
    max-width: 2.25rem;
  }
}
.p-colorpicker-color-selector {
  background: linear-gradient(to top, black, transparent), linear-gradient(to right, white, transparent); // FIXES PrimeNG BUG https://github.com/primefaces/primeng/issues/16449
}
.p-colorpicker-hue {
  background: linear-gradient(
    to bottom,
    #ff0000 0%,   /* Red */
    #ff00ff 16.6%, /* Magenta */
    #0000ff 33.3%, /* Blue */
    #00ffff 50%,   /* Cyan */
    #00ff00 66.6%, /* Green */
    #ffff00 83.3%, /* Yellow */
    #ff0000 100%   /* Back to Red */
  ); // FIXES PrimeNG BUG https://github.com/primefaces/primeng/issues/16449
}
  .max-w-unset{
    max-width: unset !important;
  }

  .p-splitter {
    .p-splitter-gutter-handle, .p-splitter-gutter-handle:focus {
      box-shadow: none;
    }
  }

  .p-scrollpanel {
    .p-scrollpanel-bar, .p-scrollpanel-bar:focus {
      box-shadow: none;
    }
  }
} //Closing for body class wrapper. Custom CSS must be added above this line

@layer primeng {
  body.saltbox-reporting.sbreport {
    
  .fake-field, .fake-field-readonly{
    .grid{
      background-color: var(--surfaceSecondary);
      border: none;
      border-bottom: 2px solid var(--BorderColor);
      width: 100%;
      padding: 1rem 0.5rem 0.25rem;
      border-radius: 0.25rem;
      max-width: 30rem;
    }
    &.p-float-label{
      label{
        top: 0.375rem !important;
        font-size: 0.675rem;
        left: 1rem !important;
      }
    }
    img{
      width: 1.5rem;
      margin-right: 0.375rem;
    }
  }

  .fake-field-readonly .grid {
    background-color: var(--surfacePrimaryAlt);
    border-bottom-color: var(--bgShade3);
  }
  
  .no-label{
    .p-inputtext{
      padding: 0.62rem 0.5rem !important;
      line-height: normal;
    }
  }
  .p-inputtext.no-label{
    padding: 0.62rem 0.5rem !important;
    line-height: normal;
  }

  .min-h-3rem{
    min-height: 3rem;
  }
  .min-h-4rem{
    min-height: 4rem;
  }
  .min-h-5rem{
    min-height: 5rem;
  }

  .p-modal-compact-header{
    .p-dialog-header{
      padding: 0.5rem !important;
    }
  }
  .p-modal-compact-content{
    .p-dialog-content{
      padding: 0.25rem 0.5rem !important;
    }
  }
  .p-modal-compact-footer{
    .p-dialog-footer{
      padding: 0.5rem !important;
    }
  }
  }
}