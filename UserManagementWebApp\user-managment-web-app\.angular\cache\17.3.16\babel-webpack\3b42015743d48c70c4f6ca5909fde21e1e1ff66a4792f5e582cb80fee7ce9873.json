{"ast": null, "code": "import { composeService<PERSON><PERSON> } from '@aws-amplify/core/internals/aws-client-utils/composers';\nimport { cognitoUserPoolTransferHandler } from './shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport { createUserPoolSerializer } from './shared/serde/createUserPoolSerializer.mjs';\nimport { createUserPoolDeserializer } from './shared/serde/createUserPoolDeserializer.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '@aws-amplify/core/internals/utils';\nimport { DEFAULT_SERVICE_CLIENT_API_CONFIG } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createGetUserAttributeVerificationCodeClient = config => composeServiceApi(cognitoUserPoolTransferHandler, createUserPoolSerializer('GetUserAttributeVerificationCode'), createUserPoolDeserializer(), {\n  ...DEFAULT_SERVICE_CLIENT_API_CONFIG,\n  ...config\n});\nexport { createGetUserAttributeVerificationCodeClient };", "map": {"version": 3, "names": ["composeServiceApi", "cognitoUserPoolTransferHandler", "createUserPoolSerializer", "createUserPoolDeserializer", "DEFAULT_SERVICE_CLIENT_API_CONFIG", "createGetUserAttributeVerificationCodeClient", "config"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/foundation/factories/serviceClients/cognitoIdentityProvider/createGetUserAttributeVerificationCodeClient.mjs"], "sourcesContent": ["import { composeService<PERSON><PERSON> } from '@aws-amplify/core/internals/aws-client-utils/composers';\nimport { cognitoUserPoolTransferHandler } from './shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport { createUserPoolSerializer } from './shared/serde/createUserPoolSerializer.mjs';\nimport { createUserPoolDeserializer } from './shared/serde/createUserPoolDeserializer.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '@aws-amplify/core/internals/utils';\nimport { DEFAULT_SERVICE_CLIENT_API_CONFIG } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst createGetUserAttributeVerificationCodeClient = (config) => composeServiceApi(cognitoUserPoolTransferHandler, createUserPoolSerializer('GetUserAttributeVerificationCode'), createUserPoolDeserializer(), {\n    ...DEFAULT_SERVICE_CLIENT_API_CONFIG,\n    ...config,\n});\n\nexport { createGetUserAttributeVerificationCodeClient };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,wDAAwD;AAC1F,SAASC,8BAA8B,QAAQ,qDAAqD;AACpG,SAASC,wBAAwB,QAAQ,6CAA6C;AACtF,SAASC,0BAA0B,QAAQ,+CAA+C;AAC1F,OAAO,8CAA8C;AACrD,OAAO,mCAAmC;AAC1C,SAASC,iCAAiC,QAAQ,iBAAiB;;AAEnE;AACA;AACA,MAAMC,4CAA4C,GAAIC,MAAM,IAAKN,iBAAiB,CAACC,8BAA8B,EAAEC,wBAAwB,CAAC,kCAAkC,CAAC,EAAEC,0BAA0B,CAAC,CAAC,EAAE;EAC3M,GAAGC,iCAAiC;EACpC,GAAGE;AACP,CAAC,CAAC;AAEF,SAASD,4CAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}