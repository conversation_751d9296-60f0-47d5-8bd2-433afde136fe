{"ast": null, "code": "import { EMPTY_HASH, UNSIGNED_PAYLOAD } from '../constants.mjs';\nimport { getHashedDataAsHex } from './dataHashHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns the hashed payload.\n *\n * @param body `body` (payload) from the request.\n * @returns String created using the payload in the body of the HTTP request as input to a hash function. This string\n * uses lowercase hexadecimal characters. If the payload is empty, return precalculated result of an empty hash.\n *\n * @internal\n */\nconst getHashedPayload = body => {\n  // return precalculated empty hash if body is undefined or null\n  if (body == null) {\n    return EMPTY_HASH;\n  }\n  if (isSourceData(body)) {\n    const hashedData = getHashedDataAsHex(null, body);\n    return hashedData;\n  }\n  // Defined body is not signable. Return unsigned payload which may or may not be accepted by the service.\n  return UNSIGNED_PAYLOAD;\n};\nconst isSourceData = body => typeof body === 'string' || ArrayBuffer.isView(body) || isArrayBuffer(body);\nconst isArrayBuffer = arg => typeof ArrayBuffer === 'function' && arg instanceof ArrayBuffer || Object.prototype.toString.call(arg) === '[object ArrayBuffer]';\nexport { getHashedPayload };", "map": {"version": 3, "names": ["EMPTY_HASH", "UNSIGNED_PAYLOAD", "getHashedDataAsHex", "getHashedPayload", "body", "isSourceData", "hashedData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arg", "Object", "prototype", "toString", "call"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/signer/signatureV4/utils/getHashedPayload.mjs"], "sourcesContent": ["import { EMPTY_HASH, UNSIGNED_PAYLOAD } from '../constants.mjs';\nimport { getHashedDataAsHex } from './dataHashHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns the hashed payload.\n *\n * @param body `body` (payload) from the request.\n * @returns String created using the payload in the body of the HTTP request as input to a hash function. This string\n * uses lowercase hexadecimal characters. If the payload is empty, return precalculated result of an empty hash.\n *\n * @internal\n */\nconst getHashedPayload = (body) => {\n    // return precalculated empty hash if body is undefined or null\n    if (body == null) {\n        return EMPTY_HASH;\n    }\n    if (isSourceData(body)) {\n        const hashedData = getHashedDataAsHex(null, body);\n        return hashedData;\n    }\n    // Defined body is not signable. Return unsigned payload which may or may not be accepted by the service.\n    return UNSIGNED_PAYLOAD;\n};\nconst isSourceData = (body) => typeof body === 'string' || ArrayBuffer.isView(body) || isArrayBuffer(body);\nconst isArrayBuffer = (arg) => (typeof ArrayBuffer === 'function' && arg instanceof ArrayBuffer) ||\n    Object.prototype.toString.call(arg) === '[object ArrayBuffer]';\n\nexport { getHashedPayload };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,gBAAgB,QAAQ,kBAAkB;AAC/D,SAASC,kBAAkB,QAAQ,uBAAuB;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAIC,IAAI,IAAK;EAC/B;EACA,IAAIA,IAAI,IAAI,IAAI,EAAE;IACd,OAAOJ,UAAU;EACrB;EACA,IAAIK,YAAY,CAACD,IAAI,CAAC,EAAE;IACpB,MAAME,UAAU,GAAGJ,kBAAkB,CAAC,IAAI,EAAEE,IAAI,CAAC;IACjD,OAAOE,UAAU;EACrB;EACA;EACA,OAAOL,gBAAgB;AAC3B,CAAC;AACD,MAAMI,YAAY,GAAID,IAAI,IAAK,OAAOA,IAAI,KAAK,QAAQ,IAAIG,WAAW,CAACC,MAAM,CAACJ,IAAI,CAAC,IAAIK,aAAa,CAACL,IAAI,CAAC;AAC1G,MAAMK,aAAa,GAAIC,GAAG,IAAM,OAAOH,WAAW,KAAK,UAAU,IAAIG,GAAG,YAAYH,WAAW,IAC3FI,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,GAAG,CAAC,KAAK,sBAAsB;AAElE,SAASP,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}