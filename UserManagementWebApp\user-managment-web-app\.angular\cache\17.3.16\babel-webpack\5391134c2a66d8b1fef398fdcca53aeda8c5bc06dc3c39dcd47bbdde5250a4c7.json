{"ast": null, "code": "const togglebuttongroup = {\n  alignItems: {\n    value: 'center'\n  },\n  alignContent: {\n    value: 'center'\n  },\n  justifyContent: {\n    value: 'flex-start'\n  }\n};\nexport { togglebuttongroup };", "map": {"version": 3, "names": ["togglebuttongroup", "alignItems", "value", "align<PERSON><PERSON><PERSON>", "justifyContent"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/toggleButtonGroup.mjs"], "sourcesContent": ["const togglebuttongroup = {\n    alignItems: { value: 'center' },\n    alignContent: { value: 'center' },\n    justifyContent: { value: 'flex-start' },\n};\n\nexport { togglebuttongroup };\n"], "mappings": "AAAA,MAAMA,iBAAiB,GAAG;EACtBC,UAAU,EAAE;IAAEC,KAAK,EAAE;EAAS,CAAC;EAC/BC,YAAY,EAAE;IAAED,KAAK,EAAE;EAAS,CAAC;EACjCE,cAAc,EAAE;IAAEF,KAAK,EAAE;EAAa;AAC1C,CAAC;AAED,SAASF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}