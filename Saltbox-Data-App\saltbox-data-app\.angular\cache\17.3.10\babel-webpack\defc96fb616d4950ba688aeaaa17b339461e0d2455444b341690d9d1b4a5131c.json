{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { theme<PERSON><PERSON><PERSON>, themeBalham, themeMaterial, themeQuartz } from 'ag-grid-community';\nimport { formatCurrency } from 'src/app/shared/utilities/format.functions';\nimport { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';\nimport { MenuModule } from 'primeng/menu';\nimport { ButtonModule } from 'primeng/button';\nimport { BadgeModule } from 'primeng/badge';\nimport { NgClass } from '@angular/common';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ToolbarModule } from 'primeng/toolbar';\nimport { SplitButtonModule } from 'primeng/splitbutton';\nimport { ActionableGridViewModeLabels, ActionableGridViewModes } from 'src/app/core/enums/actionable-grid';\nimport { GridLayoutManagerComponent } from './grid-layout-manager/grid-layout-manager.component';\nimport { ToggleButtonModule } from 'primeng/togglebutton';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/communication.service\";\nimport * as i2 from \"src/app/core/services/user-activity.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"primeng/toolbar\";\nimport * as i5 from \"primeng/tooltip\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/menu\";\nimport * as i9 from \"primeng/splitbutton\";\nimport * as i10 from \"primeng/togglebutton\";\nconst _c0 = () => ({\n  width: \"auto\"\n});\nconst _c1 = a0 => ({\n  \"bg-blue-100\": a0\n});\nconst _c2 = a0 => ({\n  \"font-bold\": a0\n});\nconst _c3 = a0 => ({\n  \"vertical-align-top\": a0\n});\nfunction ActionsMenuComponent_Conditional_1_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 31);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_7_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onClickShowValidationResults());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"badge\", ctx_r3.validationResultsCount);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"span\", 33);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c1, item_r5.value === ctx_r3.selectedTheme));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c2, item_r5.value === ctx_r3.selectedTheme));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r5.label);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-toggleButton\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ActionsMenuComponent_Conditional_1_Conditional_11_Template_p_toggleButton_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.columnsOptions, $event) || (ctx_r3.columnsOptions = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function ActionsMenuComponent_Conditional_1_Conditional_11_Template_p_toggleButton_onChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onColumnsOptionsChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.columnsOptions);\n    i0.ɵɵproperty(\"onLabel\", \"Columns Options On\")(\"offLabel\", \"Columns Options Off\");\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 35);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_12_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onUpdateUserFavorite());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"icon\", ctx_r3.isFavorite ? \"pi pi-star-fill\" : \"pi pi-star\")(\"pTooltip\", ctx_r3.isFavorite ? \"Remove Favorite\" : \"Add Favorite\");\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 36);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_14_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onClickShowFormEditor(false));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !(ctx_r3.reportInfo == null ? null : ctx_r3.reportInfo.allowAddNewRow) || ctx_r3.disabled);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 37);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_15_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onClickShowFormEditor(false));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !(ctx_r3.reportInfo == null ? null : ctx_r3.reportInfo.allowAddNewRow) || ctx_r3.disabled);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 38);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_17_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onClickShowAgent());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_19_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"span\", 43);\n    i0.ɵɵelement(2, \"i\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 33);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c1, item_r12.value === ctx_r3.selectedView));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(item_r12.icon);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c3, item_r12.icon === \"sb sb-icon-slice\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c2, item_r12.value === ctx_r3.selectedView));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r12.label);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-menu\", 16, 3);\n    i0.ɵɵtemplate(2, ActionsMenuComponent_Conditional_1_Conditional_19_ng_template_2_Template, 5, 12, \"ng-template\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-button\", 39);\n    i0.ɵɵlistener(\"click\", function ActionsMenuComponent_Conditional_1_Conditional_19_Template_p_button_click_3_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const calendar_view_r13 = i0.ɵɵreference(1);\n      return i0.ɵɵresetView(calendar_view_r13.toggle($event));\n    });\n    i0.ɵɵelementStart(4, \"span\", 40);\n    i0.ɵɵelement(5, \"i\");\n    i0.ɵɵelementStart(6, \"span\", 41);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 42);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"popup\", true)(\"model\", ctx_r3.calendarViews);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"rounded\", true)(\"disabled\", ctx_r3.disabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"\", ctx_r3.selectedViewIcon, \" vertical-align-middle\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.selectedView);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Conditional_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 45);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Conditional_20_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onClickShowRefiner());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.disabled);\n  }\n}\nfunction ActionsMenuComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-toolbar\", 11)(1, \"div\", 12)(2, \"h3\");\n    i0.ɵɵelement(3, \"i\", 8);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 13)(6, \"span\", 14);\n    i0.ɵɵtemplate(7, ActionsMenuComponent_Conditional_1_Conditional_7_Template, 1, 1, \"p-button\", 15);\n    i0.ɵɵelementStart(8, \"p-menu\", 16, 1);\n    i0.ɵɵtemplate(10, ActionsMenuComponent_Conditional_1_ng_template_10_Template, 3, 7, \"ng-template\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, ActionsMenuComponent_Conditional_1_Conditional_11_Template, 1, 3, \"p-toggleButton\", 18)(12, ActionsMenuComponent_Conditional_1_Conditional_12_Template, 1, 2, \"p-button\", 19);\n    i0.ɵɵelementStart(13, \"span\", 20);\n    i0.ɵɵtemplate(14, ActionsMenuComponent_Conditional_1_Conditional_14_Template, 1, 1, \"p-button\", 21)(15, ActionsMenuComponent_Conditional_1_Conditional_15_Template, 1, 1);\n    i0.ɵɵelementStart(16, \"p-splitButton\", 22);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Template_p_splitButton_onClick_16_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.undoActions[0] == null ? null : ctx_r3.undoActions[0].command());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, ActionsMenuComponent_Conditional_1_Conditional_17_Template, 1, 0, \"p-button\", 23);\n    i0.ɵɵelementStart(18, \"app-grid-layout-manager\", 24);\n    i0.ɵɵlistener(\"selectedLayoutChange\", function ActionsMenuComponent_Conditional_1_Template_app_grid_layout_manager_selectedLayoutChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSelectedLayoutChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ActionsMenuComponent_Conditional_1_Conditional_19_Template, 9, 8)(20, ActionsMenuComponent_Conditional_1_Conditional_20_Template, 1, 1, \"p-button\", 25);\n    i0.ɵɵelementStart(21, \"p-button\", 26);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Template_p_button_onClick_21_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const menu_download_r15 = i0.ɵɵreference(27);\n      return i0.ɵɵresetView(menu_download_r15.toggle($event));\n    });\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵelement(23, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵelement(25, \"i\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(26, \"p-menu\", 16, 2);\n    i0.ɵɵelementStart(28, \"p-button\", 29);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Template_p_button_onClick_28_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClickRefreshGridData());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"p-button\", 30);\n    i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Conditional_1_Template_p_button_onClick_29_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onClickSaveChanges());\n    });\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(26, _c0));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.reportInfo == null ? null : ctx_r3.reportInfo.description, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(7, ctx_r3.validationResultsCount ? 7 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"popup\", true)(\"model\", ctx_r3.themes);\n    i0.ɵɵadvance(3);\n    i0.ɵɵconditional(11, ctx_r3.showColumnsOptions ? 11 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(12, !ctx_r3.previewMode ? 12 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"pTooltip\", ctx_r3.disabled ? \"Project is locked\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(14, !(ctx_r3.reportInfo == null ? null : ctx_r3.reportInfo.allowAddNewRow) || ctx_r3.disabled ? 14 : 15);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"icon\", ctx_r3.undoActions[0] == null ? null : ctx_r3.undoActions[0].icon)(\"disabled\", !ctx_r3.checkHasChanges || ctx_r3.disabled)(\"model\", ctx_r3.undoActions);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(17, ctx_r3.agentChatFlag ? 17 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"reportInfo\", ctx_r3.reportInfo)(\"agGrid\", ctx_r3.agGrid)(\"disabled\", ctx_r3.disabled)(\"layouts\", ctx_r3.layouts);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(19, ctx_r3.calendarViewFlag && (ctx_r3.reportInfo == null ? null : ctx_r3.reportInfo.enableCalendarView) ? 19 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(20, ctx_r3.checkHasSlicers ? 20 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rounded\", true)(\"disabled\", ctx_r3.disabled);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"popup\", true)(\"model\", ctx_r3.exportItems);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r3.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.checkHasChanges || ctx_r3.disabled);\n  }\n}\nexport class ActionsMenuComponent {\n  constructor(communicationService, changeDetectorRef, userActivityService) {\n    this.communicationService = communicationService;\n    this.changeDetectorRef = changeDetectorRef;\n    this.userActivityService = userActivityService;\n    this.hasFailures = false;\n    this.disabled = false;\n    this.selectedView = ActionableGridViewModes.Month;\n    this.calendarViewFlag = false;\n    this.agentChatFlag = false;\n    this.showColumnsOptions = false;\n    this.showFormEditor = new EventEmitter();\n    this.undoLastChange = new EventEmitter();\n    this.undoAll = new EventEmitter();\n    this.saveChanges = new EventEmitter();\n    this.refreshGridData = new EventEmitter();\n    this.sendEmailClick = new EventEmitter();\n    this.showRefiner = new EventEmitter();\n    this.showValidationResults = new EventEmitter();\n    this.changeSelectedView = new EventEmitter();\n    this.showAgentChat = new EventEmitter();\n    this.columnsOptionsChange = new EventEmitter();\n    this.themeChange = new EventEmitter();\n    this.exportItems = [{\n      value: null,\n      styleClass: 'hidden'\n    }, {\n      label: 'Send Email',\n      icon: 'pi pi-envelope',\n      command: () => {\n        this.onClickSendEmail();\n      }\n    }, {\n      label: 'Export as Excel',\n      icon: 'pi pi-file-excel',\n      command: () => {\n        this.onExcelExportClick();\n      }\n    }, {\n      label: 'Export as CSV',\n      icon: 'pi pi-file',\n      command: () => {\n        this.onCsvExportClick();\n      }\n    }];\n    this.themes = [{\n      label: \"themeQuartz\",\n      theme: themeQuartz\n    }, {\n      label: \"themeBalham\",\n      theme: themeBalham\n    }, {\n      label: \"themeMaterial\",\n      theme: themeMaterial\n    }, {\n      label: \"themeAlpine\",\n      theme: themeAlpine\n    }];\n    this.calendarViews = [];\n    this.undoActions = [];\n    this.selectedViewIcon = 'fa-regular fa-calendar';\n    this.columnsOptions = false;\n    this.allReportFavorites = [];\n  }\n  ngOnInit() {\n    this.userFavoriteSub = this.communicationService.userFavorite.subscribe(event => {\n      this.allReportFavorites = event.data;\n      this.setIsFavorite();\n    });\n    this.communicationService.refreshUserFavoriteList();\n    this.mobileActions = [{\n      label: 'Favorite',\n      icon: 'pi pi-star',\n      command: () => {\n        this.onUpdateUserFavorite();\n      }\n    }, {\n      label: 'Add',\n      icon: 'pi pi-plus',\n      command: () => {\n        this.onClickShowFormEditor(false);\n      }\n    }, {\n      label: 'Undo',\n      icon: 'sb sb-icon-undo',\n      command: () => {\n        this.onClickUndoLastChange();\n      }\n    }, {\n      label: 'Undo All',\n      icon: 'pi pi-times',\n      command: () => {\n        this.onClickUndoAll();\n      }\n    }, {\n      label: 'Save',\n      icon: 'pi pi-save',\n      command: () => {\n        this.onClickSaveChanges();\n      }\n    }, {\n      label: 'Refresh',\n      icon: 'pi pi-sync',\n      command: () => {\n        this.onClickRefreshGridData();\n      }\n    }, {\n      label: 'Send Email',\n      icon: 'pi pi-envelope',\n      command: () => {\n        this.onClickSendEmail();\n      }\n    }, {\n      label: 'Export to Excel',\n      icon: 'pi pi-file-excel',\n      command: () => {\n        this.onExcelExportClick();\n      }\n    }, {\n      label: 'Export to CSV',\n      icon: 'pi pi-file',\n      command: () => {\n        this.onCsvExportClick();\n      }\n    }];\n    this.calendarViews = [{\n      value: null,\n      styleClass: 'hidden'\n    }, {\n      value: ActionableGridViewModes.Table,\n      label: ActionableGridViewModeLabels.Table,\n      icon: 'pi pi-list',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Table, 'pi pi-list')\n    }, {\n      value: ActionableGridViewModes.Year,\n      label: ActionableGridViewModeLabels.Year,\n      icon: 'fa-solid fa-calendar',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Year, 'fa-solid fa-calendar')\n    }, {\n      value: ActionableGridViewModes.Month,\n      label: ActionableGridViewModeLabels.Month,\n      icon: 'fa-regular fa-calendar',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Month, 'fa-regular fa-calendar')\n    }, {\n      value: ActionableGridViewModes.Week,\n      label: ActionableGridViewModeLabels.Week,\n      icon: 'fa-solid fa-calendar-week',\n      command: () => this.onChangeSelectedView(ActionableGridViewModes.Week, 'fa-solid fa-calendar-week')\n    }];\n    this.undoActions = [{\n      label: 'Undo',\n      icon: 'sb sb-icon-undo',\n      command: () => this.onClickUndoLastChange()\n    }, {\n      label: 'Undo All',\n      icon: 'pi pi-times',\n      command: () => this.onClickUndoAll()\n    }];\n  }\n  ngOnDestroy() {\n    if (this.userFavoriteSub) {\n      this.userFavoriteSub.unsubscribe();\n    }\n  }\n  onClickShowFormEditor(arg) {\n    if (this.disabled) return;\n    this.showFormEditor.emit(arg);\n  }\n  onClickUndoLastChange() {\n    if (this.disabled) return;\n    this.undoLastChange.emit();\n  }\n  onClickUndoAll() {\n    if (this.disabled) return;\n    this.undoAll.emit();\n  }\n  onClickSaveChanges() {\n    if (this.disabled) return;\n    this.saveChanges.emit();\n  }\n  onClickRefreshGridData() {\n    if (this.disabled) return;\n    this.refreshGridData.emit();\n  }\n  onClickShowRefiner() {\n    if (this.disabled) return;\n    this.showRefiner.emit();\n  }\n  onClickShowValidationResults() {\n    this.showValidationResults.emit();\n  }\n  onClickShowAgent() {\n    this.showAgentChat.emit();\n  }\n  setIsFavorite() {\n    this.isFavorite = this.allReportFavorites.some(f => f.reportId === this.reportInfo?.reportId && f.isApp);\n    // for some weird reason angular doesn't detect the changes\n    this.changeDetectorRef.detectChanges();\n  }\n  onUpdateUserFavorite() {\n    let userFavorite = this.allReportFavorites?.find(favorite => favorite.projectId.toString() === this.reportInfo.projectId && favorite.reportId === this.reportInfo.reportId);\n    if (userFavorite) {\n      userFavorite.active = false;\n      userFavorite.isApp = window.location.pathname.includes('app-view') ? true : false;\n    } else {\n      userFavorite = this.addNewUserFavorite();\n    }\n    this.userActivityService.upsertUserFavorite(userFavorite);\n    // because of the delay we set isFavorite temporarily, after updating is done it will update again.\n    this.isFavorite = userFavorite.active;\n  }\n  addNewUserFavorite() {\n    const userFavorite = {\n      projectId: +this.reportInfo.projectId,\n      url: window.location.pathname,\n      reportId: this.reportInfo.reportId,\n      reportName: this.reportInfo.reportName,\n      projectVersionId: +this.reportInfo.projectVersionId,\n      active: true,\n      isApp: window.location.pathname.includes('app-view') ? true : false\n    };\n    return userFavorite;\n  }\n  onClickSendEmail() {\n    if (this.disabled) return;\n    this.sendEmailClick.emit();\n  }\n  onExcelExportClick() {\n    if (this.disabled) return;\n    this.agGrid.api.exportDataAsExcel({\n      processCellCallback: params => {\n        return this.processCellsForExport(params);\n      },\n      columnKeys: this.columnsToExport,\n      fileName: this.reportInfo.reportName,\n      sheetName: this.reportInfo.reportName\n    });\n  }\n  onCsvExportClick() {\n    if (this.disabled) return;\n    this.agGrid.api.exportDataAsCsv({\n      processCellCallback: params => {\n        return this.processCellsForExport(params);\n      },\n      columnKeys: this.columnsToExport,\n      fileName: this.reportInfo.reportName\n    });\n  }\n  processCellsForExport(params) {\n    const exportParamsColDef = params.column.getColDef();\n    const actionableGridColDef = this.reportInfo.formatConfig?.actionableGridColumnsConfig.find(x => x.column === exportParamsColDef.field);\n    if (actionableGridColDef?.format.type === EditorColumnType.Currency) {\n      exportParamsColDef.valueFormatter = formatCurrency(params.value, actionableGridColDef.format.currency, actionableGridColDef.format.decimalPlaces);\n      return exportParamsColDef.valueFormatter;\n    }\n    return params.value;\n  }\n  onChangeSelectedView(view, icon) {\n    this.changeSelectedView.emit(view);\n    this.selectedViewIcon = icon;\n    this.selectedView = view;\n    // Resize columns if no layout is selected\n    setTimeout(() => {\n      if (this.slectedLayout === undefined) {\n        this.agGrid.api?.autoSizeAllColumns();\n      }\n    }, 0);\n  }\n  onSelectedLayoutChange(layout) {\n    this.columnsOptions = layout?.agGridSettings?.sideBar === 'columns';\n    this.slectedLayout = layout;\n  }\n  onColumnsOptionsChange(event) {\n    this.columnsOptionsChange.emit(event.checked);\n  }\n  static {\n    this.ɵfac = function ActionsMenuComponent_Factory(t) {\n      return new (t || ActionsMenuComponent)(i0.ɵɵdirectiveInject(i1.CommunicationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.UserActivityService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActionsMenuComponent,\n      selectors: [[\"app-actions-menu\"]],\n      inputs: {\n        previewMode: \"previewMode\",\n        reportInfo: \"reportInfo\",\n        checkHasChanges: \"checkHasChanges\",\n        checkHasSlicers: \"checkHasSlicers\",\n        checkHasPendingChanges: \"checkHasPendingChanges\",\n        agGrid: \"agGrid\",\n        columnsToExport: \"columnsToExport\",\n        validationResultsCount: \"validationResultsCount\",\n        hasFailures: \"hasFailures\",\n        disabled: \"disabled\",\n        selectedView: \"selectedView\",\n        calendarViewFlag: \"calendarViewFlag\",\n        agentChatFlag: \"agentChatFlag\",\n        showColumnsOptions: \"showColumnsOptions\",\n        layouts: \"layouts\"\n      },\n      outputs: {\n        showFormEditor: \"showFormEditor\",\n        undoLastChange: \"undoLastChange\",\n        undoAll: \"undoAll\",\n        saveChanges: \"saveChanges\",\n        refreshGridData: \"refreshGridData\",\n        sendEmailClick: \"sendEmailClick\",\n        showRefiner: \"showRefiner\",\n        showValidationResults: \"showValidationResults\",\n        changeSelectedView: \"changeSelectedView\",\n        showAgentChat: \"showAgentChat\",\n        columnsOptionsChange: \"columnsOptionsChange\",\n        themeChange: \"themeChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 9,\n      vars: 4,\n      consts: [[\"menu\", \"\"], [\"theme_menu\", \"\"], [\"menu_download\", \"\"], [\"calendar_view\", \"\"], [1, \"grid-toolbar\"], [1, \"desktop-grid-tb\", 3, \"style\"], [1, \"mobile-app-menu\"], [1, \"report-title\"], [1, \"pi\", \"pi-chart-line\"], [\"appendTo\", \"body\", 3, \"model\", \"popup\"], [\"type\", \"button\", \"icon\", \"pi pi-ellipsis-h\", 1, \"mobile-menu-tb\", 3, \"onClick\"], [1, \"desktop-grid-tb\"], [1, \"p-toolbar-group-start\"], [1, \"p-toolbar-group-end\"], [1, \"tb-menu-desktop\", \"inline-flex\", \"align-items-center\"], [\"icon\", \"fa-solid fa-exclamation\", \"severity\", \"warning\", \"pTooltip\", \"Show Errors/Warnings\", \"tooltipPosition\", \"top\", \"styleClass\", \"p-overlay-badge mr-3\", \"badgeClass\", \"p-badge-warning\", 3, \"badge\"], [\"appendTo\", \"body\", 3, \"popup\", \"model\"], [\"pTemplate\", \"item\"], [3, \"ngModel\", \"onLabel\", \"offLabel\"], [\"tooltipPosition\", \"top\", 3, \"icon\", \"pTooltip\"], [\"tooltipPosition\", \"top\", 1, \"inline-flex\", \"align-items-center\", 3, \"pTooltip\"], [\"pTooltip\", \"Add Row (permission required)\", \"tooltipPosition\", \"top\", \"icon\", \"pi pi-plus\", 3, \"disabled\"], [\"appendTo\", \"body\", 3, \"onClick\", \"icon\", \"disabled\", \"model\"], [\"icon\", \"fa-regular fa-comment\", \"pTooltip\", \"Launch Agent Chat\"], [3, \"selectedLayoutChange\", \"reportInfo\", \"agGrid\", \"disabled\", \"layouts\"], [\"icon\", \"pi pi-filter\", \"pTooltip\", \"Filter Your Data\", \"tooltipPosition\", \"top\", 3, \"disabled\"], [\"pTooltip\", \"Export\", \"tooltipPosition\", \"top\", \"styleClass\", \"min-w-0\", 3, \"onClick\", \"rounded\", \"disabled\"], [1, \"pi\", \"pi-file-export\", \"vertical-align-bottom\"], [1, \"pi\", \"pi-angle-down\", \"vertical-align-middle\", \"ml-1\"], [\"icon\", \"pi pi-refresh\", \"pTooltip\", \"Refresh\", \"tooltipPosition\", \"top\", 3, \"onClick\", \"disabled\"], [\"icon\", \"pi pi-save\", \"pTooltip\", \"Save\", \"tooltipPosition\", \"top\", 3, \"onClick\", \"disabled\"], [\"icon\", \"fa-solid fa-exclamation\", \"severity\", \"warning\", \"pTooltip\", \"Show Errors/Warnings\", \"tooltipPosition\", \"top\", \"styleClass\", \"p-overlay-badge mr-3\", \"badgeClass\", \"p-badge-warning\", 3, \"onClick\", \"badge\"], [\"pRipple\", \"\", 1, \"p-ripple\", \"cursor-pointer\", \"p-element\", \"flex\", \"items-center\", \"p-menu-item-link\", \"p-3\", 3, \"ngClass\"], [1, \"ml-2\", \"p-menuitem-text\", \"text-sm\", \"text-base-text\", \"vertical-align-middle\", \"line-height-3\", 3, \"ngClass\"], [3, \"ngModelChange\", \"onChange\", \"ngModel\", \"onLabel\", \"offLabel\"], [\"tooltipPosition\", \"top\", 3, \"onClick\", \"icon\", \"pTooltip\"], [\"pTooltip\", \"Add Row (permission required)\", \"tooltipPosition\", \"top\", \"icon\", \"pi pi-plus\", 3, \"onClick\", \"disabled\"], [\"icon\", \"pi pi-plus\", 3, \"onClick\", \"disabled\"], [\"icon\", \"fa-regular fa-comment\", \"pTooltip\", \"Launch Agent Chat\", 3, \"onClick\"], [\"pTooltip\", \"Switch View\", \"tooltipPosition\", \"top\", \"styleClass\", \"min-w-0\", 3, \"click\", \"rounded\", \"disabled\"], [1, \"inline-flex\", \"align-items-center\"], [1, \"ml-2\"], [1, \"pi\", \"pi-angle-down\", \"vertical-align-middle\", \"ml-2\"], [1, \"text-base\", \"text-base-text\"], [1, \"p-menuitem-icon\", 3, \"ngClass\"], [\"icon\", \"pi pi-filter\", \"pTooltip\", \"Filter Your Data\", \"tooltipPosition\", \"top\", 3, \"onClick\", \"disabled\"]],\n      template: function ActionsMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 4);\n          i0.ɵɵtemplate(1, ActionsMenuComponent_Conditional_1_Template, 30, 27, \"p-toolbar\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 6)(3, \"span\", 7);\n          i0.ɵɵelement(4, \"i\", 8);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"p-menu\", 9, 0);\n          i0.ɵɵelementStart(8, \"p-button\", 10);\n          i0.ɵɵlistener(\"onClick\", function ActionsMenuComponent_Template_p_button_onClick_8_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const menu_r16 = i0.ɵɵreference(7);\n            return i0.ɵɵresetView(menu_r16.toggle($event));\n          });\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, (ctx.calendarViews == null ? null : ctx.calendarViews.length) > 0 && (ctx.undoActions == null ? null : ctx.undoActions.length) > 0 ? 1 : -1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.reportInfo == null ? null : ctx.reportInfo.description, \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"model\", ctx.mobileActions)(\"popup\", true);\n        }\n      },\n      dependencies: [FormsModule, i3.NgControlStatus, i3.NgModel, ToolbarModule, i4.Toolbar, TooltipModule, i5.Tooltip, BadgeModule, i6.PrimeTemplate, NgClass, ButtonModule, i7.Button, MenuModule, i8.Menu, SplitButtonModule, i9.SplitButton, GridLayoutManagerComponent, ToggleButtonModule, i10.ToggleButton],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "themeAlpine", "theme<PERSON><PERSON><PERSON>", "themeMaterial", "themeQuartz", "formatCurrency", "EditorColumnType", "MenuModule", "ButtonModule", "BadgeModule", "Ng<PERSON><PERSON>", "TooltipModule", "ToolbarModule", "SplitButtonModule", "ActionableGridViewModeLabels", "ActionableGridViewModes", "GridLayoutManagerComponent", "ToggleButtonModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵlistener", "ActionsMenuComponent_Conditional_1_Conditional_7_Template_p_button_onClick_0_listener", "ɵɵrestoreView", "_r3", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "onClickShowValidationResults", "ɵɵelementEnd", "ɵɵproperty", "validationResultsCount", "ɵɵtext", "ɵɵpureFunction1", "_c1", "item_r5", "value", "selectedTheme", "ɵɵadvance", "_c2", "ɵɵtextInterpolate", "label", "ɵɵtwoWayListener", "ActionsMenuComponent_Conditional_1_Conditional_11_Template_p_toggleButton_ngModelChange_0_listener", "$event", "_r6", "ɵɵtwoWayBindingSet", "columnsOptions", "ActionsMenuComponent_Conditional_1_Conditional_11_Template_p_toggleButton_onChange_0_listener", "onColumnsOptionsChange", "ɵɵtwoWayProperty", "ActionsMenuComponent_Conditional_1_Conditional_12_Template_p_button_onClick_0_listener", "_r7", "onUpdateUserFavorite", "isFavorite", "ActionsMenuComponent_Conditional_1_Conditional_14_Template_p_button_onClick_0_listener", "_r8", "onClickShowFormEditor", "reportInfo", "allowAddNewRow", "disabled", "ActionsMenuComponent_Conditional_1_Conditional_15_Template_p_button_onClick_0_listener", "_r9", "ActionsMenuComponent_Conditional_1_Conditional_17_Template_p_button_onClick_0_listener", "_r10", "onClickShowAgent", "ɵɵelement", "item_r12", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵclassMap", "icon", "_c3", "ɵɵtemplate", "ActionsMenuComponent_Conditional_1_Conditional_19_ng_template_2_Template", "ActionsMenuComponent_Conditional_1_Conditional_19_Template_p_button_click_3_listener", "_r11", "calendar_view_r13", "ɵɵreference", "toggle", "calendarViews", "ɵɵclassMapInterpolate1", "selectedViewIcon", "ActionsMenuComponent_Conditional_1_Conditional_20_Template_p_button_onClick_0_listener", "_r14", "onClickShowRefiner", "ActionsMenuComponent_Conditional_1_Conditional_7_Template", "ActionsMenuComponent_Conditional_1_ng_template_10_Template", "ActionsMenuComponent_Conditional_1_Conditional_11_Template", "ActionsMenuComponent_Conditional_1_Conditional_12_Template", "ActionsMenuComponent_Conditional_1_Conditional_14_Template", "ActionsMenuComponent_Conditional_1_Conditional_15_Template", "ActionsMenuComponent_Conditional_1_Template_p_splitButton_onClick_16_listener", "_r2", "undoActions", "command", "ActionsMenuComponent_Conditional_1_Conditional_17_Template", "ActionsMenuComponent_Conditional_1_Template_app_grid_layout_manager_selectedLayoutChange_18_listener", "onSelectedLayoutChange", "ActionsMenuComponent_Conditional_1_Conditional_19_Template", "ActionsMenuComponent_Conditional_1_Conditional_20_Template", "ActionsMenuComponent_Conditional_1_Template_p_button_onClick_21_listener", "menu_download_r15", "ActionsMenuComponent_Conditional_1_Template_p_button_onClick_28_listener", "onClickRefreshGridData", "ActionsMenuComponent_Conditional_1_Template_p_button_onClick_29_listener", "onClickSaveChanges", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵtextInterpolate1", "description", "ɵɵconditional", "themes", "showColumnsOptions", "previewMode", "ɵɵpropertyInterpolate", "checkHasChanges", "agentChatFlag", "agGrid", "layouts", "calendarViewFlag", "enableCalendarView", "checkHasSlicers", "exportItems", "ActionsMenuComponent", "constructor", "communicationService", "changeDetectorRef", "userActivityService", "hasFailures", "Month", "showFormEditor", "undoLastChange", "undoAll", "saveChanges", "refreshGridData", "sendEmailClick", "showRefiner", "showValidationResults", "changeSelectedView", "showAgentChat", "columnsOptionsChange", "themeChange", "styleClass", "onClickSendEmail", "onExcelExportClick", "onCsvExportClick", "theme", "allReportFavorites", "ngOnInit", "userFavoriteSub", "userFavorite", "subscribe", "event", "data", "setIsFavorite", "refreshUserFavoriteList", "mobileActions", "onClickUndoLastChange", "onClickUndoAll", "Table", "onChangeSelectedView", "Year", "Week", "ngOnDestroy", "unsubscribe", "arg", "emit", "some", "f", "reportId", "isApp", "detectChanges", "find", "favorite", "projectId", "toString", "active", "window", "location", "pathname", "includes", "addNewUserFavorite", "upsertUserFavorite", "url", "reportName", "projectVersionId", "api", "exportDataAsExcel", "processCellCallback", "params", "processCellsForExport", "columnKeys", "columnsToExport", "fileName", "sheetName", "exportDataAsCsv", "exportParamsColDef", "column", "getColDef", "actionableGridColDef", "formatConfig", "actionableGridColumnsConfig", "x", "field", "format", "type", "<PERSON><PERSON><PERSON><PERSON>", "valueFormatter", "currency", "decimalPlaces", "view", "setTimeout", "s<PERSON><PERSON><PERSON><PERSON>", "undefined", "autoSizeAllColumns", "layout", "agGridSettings", "sideBar", "checked", "ɵɵdirectiveInject", "i1", "CommunicationService", "ChangeDetectorRef", "i2", "UserActivityService", "selectors", "inputs", "checkHasPendingChanges", "outputs", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ActionsMenuComponent_Template", "rf", "ctx", "ActionsMenuComponent_Conditional_1_Template", "ActionsMenuComponent_Template_p_button_onClick_8_listener", "_r1", "menu_r16", "length", "i3", "NgControlStatus", "NgModel", "i4", "<PERSON><PERSON><PERSON>", "i5", "<PERSON><PERSON><PERSON>", "i6", "PrimeTemplate", "i7", "<PERSON><PERSON>", "i8", "<PERSON><PERSON>", "i9", "SplitButton", "i10", "ToggleButton", "styles"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\actions-menu\\actions-menu.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\actions-menu\\actions-menu.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';\r\nimport { AgGridAngular } from 'ag-grid-angular';\r\nimport { ProcessCellForExportParams, themeAlpine, themeBalham, themeMaterial, themeQuartz } from 'ag-grid-community';\r\nimport { Subscription } from 'rxjs';\r\nimport { UserFavorite } from 'src/app/core/models/user-favorite';\r\nimport { CommunicationService } from 'src/app/core/services/communication.service';\r\nimport { UserActivityService } from 'src/app/core/services/user-activity.service';\r\nimport { ReportInfo } from 'src/app/core/models/report-info';\r\nimport { formatCurrency } from 'src/app/shared/utilities/format.functions';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { EditorColumnType } from 'src/app/shared/enums/editor-column-type.enum';\r\nimport { MenuModule } from 'primeng/menu';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { BadgeModule } from 'primeng/badge';\r\nimport { NgClass, NgIf } from '@angular/common';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { ToolbarModule } from 'primeng/toolbar';\r\nimport { SplitButtonModule } from 'primeng/splitbutton';\r\nimport { ActionableGridViewModeLabels, ActionableGridViewModes } from 'src/app/core/enums/actionable-grid';\r\nimport { GridLayoutManagerComponent } from './grid-layout-manager/grid-layout-manager.component';\r\nimport { GridLayout } from '../model/grid-layout';\r\nimport { ToggleButtonModule } from 'primeng/togglebutton';\r\nimport { FormsModule } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-actions-menu',\r\n  templateUrl: './actions-menu.component.html',\r\n  styleUrls: ['./actions-menu.component.scss'],\r\n  standalone: true,\r\n  imports: [FormsModule, ToolbarModule, TooltipModule, BadgeModule, NgClass, ButtonModule, MenuModule, SplitButtonModule, NgIf, GridLayoutManagerComponent, ToggleButtonModule]\r\n})\r\nexport class ActionsMenuComponent implements OnInit, OnDestroy {\r\n\r\n  @Input() previewMode: boolean;\r\n  @Input() reportInfo: ReportInfo;\r\n  @Input() checkHasChanges: boolean;\r\n  @Input() checkHasSlicers: boolean;\r\n  @Input() checkHasPendingChanges: boolean;\r\n  @Input() agGrid: AgGridAngular;\r\n  @Input() columnsToExport: string[];\r\n  @Input() validationResultsCount: any;\r\n  @Input() hasFailures = false;\r\n  @Input() disabled = false;\r\n  @Input() selectedView = ActionableGridViewModes.Month;\r\n  @Input() calendarViewFlag = false;\r\n  @Input() agentChatFlag = false;\r\n  @Input() showColumnsOptions = false;\r\n  @Input() layouts: GridLayout[];\r\n\r\n  @Output() showFormEditor: EventEmitter<any> = new EventEmitter();\r\n  @Output() undoLastChange: EventEmitter<any> = new EventEmitter();\r\n  @Output() undoAll: EventEmitter<any> = new EventEmitter();\r\n  @Output() saveChanges: EventEmitter<any> = new EventEmitter();\r\n  @Output() refreshGridData: EventEmitter<any> = new EventEmitter();\r\n  @Output() sendEmailClick: EventEmitter<any> = new EventEmitter();\r\n  @Output() showRefiner: EventEmitter<any> = new EventEmitter();\r\n  @Output() showValidationResults: EventEmitter<any> = new EventEmitter();\r\n  @Output() changeSelectedView: EventEmitter<string> = new EventEmitter();\r\n  @Output() showAgentChat: EventEmitter<string> = new EventEmitter();\r\n  @Output() columnsOptionsChange: EventEmitter<boolean> = new EventEmitter();\r\n  @Output() themeChange: EventEmitter<string> = new EventEmitter();\r\n\r\n  mobileActions: MenuItem[];\r\n\r\n  exportItems = [\r\n    {\r\n      value: null,\r\n      styleClass: 'hidden'\r\n    },\r\n    {\r\n      label: 'Send Email', icon: 'pi pi-envelope', command: () => {\r\n        this.onClickSendEmail();\r\n      }\r\n    },\r\n    {\r\n      label: 'Export as Excel', icon: 'pi pi-file-excel', command: () => {\r\n        this.onExcelExportClick();\r\n      },\r\n    },\r\n    {\r\n      label: 'Export as CSV', icon: 'pi pi-file', command: () => {\r\n        this.onCsvExportClick();\r\n      }\r\n    }\r\n  ];\r\n\r\n\r\n  themes = [\r\n    { label: \"themeQuartz\", theme: themeQuartz },\r\n    { label: \"themeBalham\", theme: themeBalham },\r\n    { label: \"themeMaterial\", theme: themeMaterial },\r\n    { label: \"themeAlpine\", theme: themeAlpine },\r\n  ];\r\n\r\n  calendarViews = [];\r\n  undoActions = [];\r\n  isFavorite: boolean;\r\n  selectedViewIcon: string = 'fa-regular fa-calendar';\r\n  slectedLayout: GridLayout | undefined;\r\n  columnsOptions = false;\r\n\r\n  private userFavoriteSub: Subscription;\r\n  private allReportFavorites: UserFavorite[] = [];\r\n\r\n  constructor(\r\n    private communicationService: CommunicationService,\r\n    private changeDetectorRef: ChangeDetectorRef,\r\n    private userActivityService: UserActivityService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.userFavoriteSub = this.communicationService.userFavorite\r\n      .subscribe(event => {\r\n        this.allReportFavorites = event.data;\r\n        this.setIsFavorite();\r\n      });\r\n    this.communicationService.refreshUserFavoriteList();\r\n\r\n    this.mobileActions = [\r\n      {\r\n        label: 'Favorite',\r\n        icon: 'pi pi-star',\r\n        command: () => {\r\n          this.onUpdateUserFavorite();\r\n        }\r\n      },\r\n      {\r\n        label: 'Add',\r\n        icon: 'pi pi-plus',\r\n        command: () => {\r\n          this.onClickShowFormEditor(false);\r\n        }\r\n      },\r\n      {\r\n        label: 'Undo',\r\n        icon: 'sb sb-icon-undo',\r\n        command: () => {\r\n          this.onClickUndoLastChange();\r\n        }\r\n      },\r\n      {\r\n        label: 'Undo All',\r\n        icon: 'pi pi-times',\r\n        command: () => {\r\n          this.onClickUndoAll();\r\n        }\r\n      },\r\n      {\r\n        label: 'Save',\r\n        icon: 'pi pi-save',\r\n        command: () => {\r\n          this.onClickSaveChanges();\r\n        }\r\n      },\r\n      {\r\n        label: 'Refresh',\r\n        icon: 'pi pi-sync',\r\n        command: () => {\r\n          this.onClickRefreshGridData();\r\n        }\r\n      },\r\n      {\r\n        label: 'Send Email',\r\n        icon: 'pi pi-envelope',\r\n        command: () => {\r\n          this.onClickSendEmail();\r\n        }\r\n      },\r\n      {\r\n        label: 'Export to Excel',\r\n        icon: 'pi pi-file-excel',\r\n        command: () => {\r\n          this.onExcelExportClick();\r\n        }\r\n      },\r\n      {\r\n        label: 'Export to CSV',\r\n        icon: 'pi pi-file',\r\n        command: () => {\r\n          this.onCsvExportClick();\r\n        }\r\n      },\r\n    ];\r\n\r\n    this.calendarViews = [\r\n      {\r\n        value: null,\r\n        styleClass: 'hidden'\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Table, label: ActionableGridViewModeLabels.Table, icon: 'pi pi-list',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Table, 'pi pi-list')\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Year, label: ActionableGridViewModeLabels.Year, icon: 'fa-solid fa-calendar',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Year, 'fa-solid fa-calendar')\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Month, label: ActionableGridViewModeLabels.Month, icon: 'fa-regular fa-calendar',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Month, 'fa-regular fa-calendar')\r\n      },\r\n      {\r\n        value: ActionableGridViewModes.Week, label: ActionableGridViewModeLabels.Week, icon: 'fa-solid fa-calendar-week',\r\n        command: () => this.onChangeSelectedView(ActionableGridViewModes.Week, 'fa-solid fa-calendar-week')\r\n      },\r\n    ];\r\n\r\n    this.undoActions = [\r\n      {\r\n        label: 'Undo', icon: 'sb sb-icon-undo', command: () => this.onClickUndoLastChange()\r\n      },\r\n      {\r\n        label: 'Undo All', icon: 'pi pi-times', command: () => this.onClickUndoAll()\r\n      },\r\n    ];\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    if (this.userFavoriteSub) {\r\n      this.userFavoriteSub.unsubscribe();\r\n    }\r\n  }\r\n\r\n  onClickShowFormEditor(arg: boolean): void {\r\n    if (this.disabled) return;\r\n\r\n    this.showFormEditor.emit(arg);\r\n  }\r\n\r\n  onClickUndoLastChange(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.undoLastChange.emit();\r\n  }\r\n\r\n  onClickUndoAll(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.undoAll.emit();\r\n  }\r\n\r\n  onClickSaveChanges(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.saveChanges.emit();\r\n  }\r\n\r\n  onClickRefreshGridData(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.refreshGridData.emit();\r\n  }\r\n\r\n  onClickShowRefiner(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.showRefiner.emit();\r\n  }\r\n\r\n  onClickShowValidationResults(): void {\r\n    this.showValidationResults.emit();\r\n  }\r\n\r\n  onClickShowAgent(): void {\r\n    this.showAgentChat.emit();\r\n  }\r\n\r\n  setIsFavorite() {\r\n    this.isFavorite = this.allReportFavorites.some(f => f.reportId === this.reportInfo?.reportId && f.isApp);\r\n    // for some weird reason angular doesn't detect the changes\r\n    this.changeDetectorRef.detectChanges();\r\n  }\r\n\r\n  onUpdateUserFavorite(): void {\r\n    let userFavorite: UserFavorite = this.allReportFavorites?.find(favorite =>\r\n      favorite.projectId.toString() === this.reportInfo.projectId && favorite.reportId === this.reportInfo.reportId);\r\n\r\n    if (userFavorite) {\r\n      userFavorite.active = false;\r\n      userFavorite.isApp = window.location.pathname.includes('app-view') ? true : false;\r\n    }\r\n    else {\r\n      userFavorite = this.addNewUserFavorite();\r\n    }\r\n    this.userActivityService.upsertUserFavorite(userFavorite);\r\n    // because of the delay we set isFavorite temporarily, after updating is done it will update again.\r\n    this.isFavorite = userFavorite.active;\r\n  }\r\n\r\n  addNewUserFavorite() {\r\n    const userFavorite: UserFavorite = {\r\n      projectId: +this.reportInfo.projectId,\r\n      url: window.location.pathname,\r\n      reportId: this.reportInfo.reportId,\r\n      reportName: this.reportInfo.reportName,\r\n      projectVersionId: +this.reportInfo.projectVersionId,\r\n      active: true,\r\n      isApp: window.location.pathname.includes('app-view') ? true : false\r\n    };\r\n    return userFavorite;\r\n  }\r\n\r\n  onClickSendEmail(): void {\r\n    if (this.disabled) return;\r\n\r\n    this.sendEmailClick.emit();\r\n  }\r\n\r\n  onExcelExportClick() {\r\n    if (this.disabled) return;\r\n\r\n    this.agGrid.api.exportDataAsExcel({\r\n      processCellCallback: (params) => {\r\n        return this.processCellsForExport(params);\r\n      },\r\n      columnKeys: this.columnsToExport, fileName: this.reportInfo.reportName, sheetName: this.reportInfo.reportName\r\n    });\r\n  }\r\n\r\n  onCsvExportClick() {\r\n    if (this.disabled) return;\r\n\r\n    this.agGrid.api.exportDataAsCsv({\r\n      processCellCallback: (params) => {\r\n        return this.processCellsForExport(params);\r\n      },\r\n      columnKeys: this.columnsToExport, fileName: this.reportInfo.reportName\r\n    });\r\n  }\r\n\r\n  processCellsForExport(params: ProcessCellForExportParams) {\r\n    const exportParamsColDef = params.column.getColDef();\r\n    const actionableGridColDef = this.reportInfo.formatConfig?.actionableGridColumnsConfig.find(x => x.column === exportParamsColDef.field);\r\n    if (actionableGridColDef?.format.type === EditorColumnType.Currency) {\r\n      exportParamsColDef.valueFormatter = formatCurrency(\r\n        params.value,\r\n        actionableGridColDef.format.currency,\r\n        actionableGridColDef.format.decimalPlaces);\r\n      return exportParamsColDef.valueFormatter;\r\n    }\r\n\r\n    return params.value;\r\n  }\r\n\r\n  onChangeSelectedView(view: ActionableGridViewModes, icon: string) {\r\n    this.changeSelectedView.emit(view);\r\n    this.selectedViewIcon = icon;\r\n    this.selectedView = view;\r\n\r\n\r\n    // Resize columns if no layout is selected\r\n    setTimeout(() => {\r\n      if (this.slectedLayout === undefined) {\r\n        this.agGrid.api?.autoSizeAllColumns();\r\n      }\r\n    }, 0);\r\n  }\r\n\r\n  onSelectedLayoutChange(layout: GridLayout) {\r\n    this.columnsOptions = layout?.agGridSettings?.sideBar === 'columns';\r\n    this.slectedLayout = layout;\r\n  }\r\n\r\n  onColumnsOptionsChange(event: any) {\r\n    this.columnsOptionsChange.emit(event.checked);\r\n  }\r\n}\r\n", "<div class=\"grid-toolbar\">\r\n  @if(this.calendarViews?.length > 0 && this.undoActions?.length > 0) {\r\n  <p-toolbar [style]=\"{width: 'auto'}\" class=\"desktop-grid-tb\">\r\n    <div class=\"p-toolbar-group-start\">\r\n      <h3><i class=\"pi pi-chart-line\"></i> {{reportInfo?.description}}</h3>\r\n    </div>\r\n    <div class=\"p-toolbar-group-end\">\r\n      <span class=\"tb-menu-desktop inline-flex align-items-center\">\r\n        @if (this.validationResultsCount) {\r\n        <p-button icon=\"fa-solid fa-exclamation\" severity=\"warning\" pTooltip=\"Show Errors/Warnings\"\r\n          tooltipPosition=\"top\" (onClick)=\"onClickShowValidationResults()\" styleClass=\"p-overlay-badge mr-3\"\r\n          [badge]=\"validationResultsCount\" badgeClass=\"p-badge-warning\"></p-button>\r\n        }\r\n\r\n        <p-menu #theme_menu [popup]=\"true\" [model]=\"this.themes\" appendTo=\"body\">\r\n          <ng-template pTemplate=\"item\" let-item>\r\n            <div pRipple class=\"p-ripple cursor-pointer p-element flex items-center p-menu-item-link p-3\"\r\n              [ngClass]=\"{'bg-blue-100': (item.value === selectedTheme)}\">\r\n              <span class=\"ml-2 p-menuitem-text text-sm text-base-text vertical-align-middle line-height-3\"\r\n                [ngClass]=\"{'font-bold': (item.value === selectedTheme)}\">{{ item.label }}</span>\r\n            </div>\r\n          </ng-template>\r\n        </p-menu>\r\n\r\n        @if (this.showColumnsOptions) {\r\n        <p-toggleButton [(ngModel)]=\"columnsOptions\" [onLabel]=\"'Columns Options On'\" [offLabel]=\"'Columns Options Off'\"\r\n          (onChange)=\"onColumnsOptionsChange($event)\" />\r\n        }\r\n\r\n        @if (!this.previewMode) {\r\n        <p-button [icon]=\"isFavorite ? 'pi pi-star-fill' : 'pi pi-star'\"\r\n          [pTooltip]=\"isFavorite ? 'Remove Favorite' : 'Add Favorite'\" tooltipPosition=\"top\"\r\n          (onClick)=\"onUpdateUserFavorite()\"></p-button>\r\n        }\r\n\r\n        <span pTooltip=\"{{disabled? 'Project is locked' : ''}}\" tooltipPosition=\"top\"\r\n          class=\"inline-flex align-items-center\">\r\n\r\n          @if (!this.reportInfo?.allowAddNewRow || disabled) {\r\n          <p-button pTooltip=\"Add Row (permission required)\" tooltipPosition=\"top\" icon=\"pi pi-plus\"\r\n            (onClick)=\"onClickShowFormEditor(false)\"\r\n            [disabled]=\"!this.reportInfo?.allowAddNewRow || disabled\"></p-button>\r\n          } @else {\r\n          <p-button icon=\"pi pi-plus\" (onClick)=\"onClickShowFormEditor(false)\"\r\n            [disabled]=\"!this.reportInfo?.allowAddNewRow || disabled\"></p-button>\r\n          }\r\n\r\n          <p-splitButton [icon]=\"undoActions[0]?.icon\" (onClick)=\"undoActions[0]?.command()\"\r\n            [disabled]=\"!checkHasChanges || disabled\" appendTo=\"body\" [model]=\"undoActions\"></p-splitButton>\r\n\r\n          @if (this.agentChatFlag) {\r\n          <p-button icon=\"fa-regular fa-comment\" pTooltip=\"Launch Agent Chat\" (onClick)=\"onClickShowAgent()\"></p-button>\r\n          }\r\n\r\n          <app-grid-layout-manager [reportInfo]=\"reportInfo\" [agGrid]=\"agGrid\" [disabled]=\"disabled\" [layouts]=\"layouts\"\r\n            (selectedLayoutChange)=\"onSelectedLayoutChange($event)\">\r\n          </app-grid-layout-manager>\r\n\r\n          @if (calendarViewFlag && reportInfo?.enableCalendarView) {\r\n          <p-menu #calendar_view [popup]=\"true\" [model]=\"this.calendarViews\" appendTo=\"body\">\r\n            <ng-template pTemplate=\"item\" let-item>\r\n              <div pRipple class=\"p-ripple cursor-pointer p-element flex items-center p-menu-item-link p-3\"\r\n                [ngClass]=\"{'bg-blue-100': (item.value === selectedView)}\">\r\n                <span class=\"text-base text-base-text\">\r\n                  <i class=\"p-menuitem-icon\" [class]=\"item.icon\"\r\n                    [ngClass]=\"{'vertical-align-top': (item.icon === 'sb sb-icon-slice')}\"></i>\r\n                </span>\r\n                <span class=\"ml-2 p-menuitem-text text-sm text-base-text vertical-align-middle line-height-3\"\r\n                  [ngClass]=\"{'font-bold': (item.value === selectedView)}\">{{ item.label }}</span>\r\n              </div>\r\n            </ng-template>\r\n          </p-menu>\r\n          <p-button [rounded]=\"true\" (click)=\"calendar_view.toggle($event)\" [disabled]=\"disabled\" pTooltip=\"Switch View\"\r\n            tooltipPosition=\"top\" styleClass=\"min-w-0\">\r\n            <span class=\"inline-flex align-items-center\">\r\n              <i class=\"{{selectedViewIcon}} vertical-align-middle\"></i>\r\n              <span class=\"ml-2\">{{selectedView}}</span>\r\n              <i class=\"pi pi-angle-down vertical-align-middle ml-2\"></i>\r\n            </span>\r\n          </p-button>\r\n          }\r\n\r\n          @if (checkHasSlicers) {\r\n          <p-button icon=\"pi pi-filter\" (onClick)=\"onClickShowRefiner()\" [disabled]=\"disabled\"\r\n            pTooltip=\"Filter Your Data\" tooltipPosition=\"top\"></p-button>\r\n          }\r\n\r\n          <p-button [rounded]=\"true\" (onClick)=\"menu_download.toggle($event)\" [disabled]=\"disabled\" pTooltip=\"Export\"\r\n            tooltipPosition=\"top\" styleClass=\"min-w-0\">\r\n            <span><i class=\"pi pi-file-export vertical-align-bottom\"></i></span>\r\n            <span><i class=\"pi pi-angle-down vertical-align-middle ml-1\"></i></span>\r\n          </p-button>\r\n          <p-menu #menu_download [popup]=\"true\" [model]=\"this.exportItems\" appendTo=\"body\"></p-menu>\r\n\r\n          <p-button icon=\"pi pi-refresh\" (onClick)=\"onClickRefreshGridData()\" [disabled]=\"disabled\" pTooltip=\"Refresh\"\r\n            tooltipPosition=\"top\"></p-button>\r\n\r\n          <p-button icon=\"pi pi-save\" (onClick)=\"onClickSaveChanges()\" [disabled]=\"!checkHasChanges || disabled\"\r\n            pTooltip=\"Save\" tooltipPosition=\"top\"></p-button>\r\n        </span>\r\n      </span>\r\n    </div>\r\n  </p-toolbar>\r\n  }\r\n</div>\r\n<div class=\"mobile-app-menu\">\r\n  <span class=\"report-title\"><i class=\"pi pi-chart-line\"></i> {{reportInfo?.description}}</span>\r\n  <p-menu #menu [model]=\"mobileActions\" [popup]=\"true\" appendTo=\"body\"></p-menu>\r\n  <p-button class=\"mobile-menu-tb\" type=\"button\" (onClick)=\"menu.toggle($event)\" icon=\"pi pi-ellipsis-h\"></p-button>\r\n</div>"], "mappings": "AAAA,SAAuCA,YAAY,QAA0C,eAAe;AAE5G,SAAqCC,WAAW,EAAEC,WAAW,EAAEC,aAAa,EAAEC,WAAW,QAAQ,mBAAmB;AAMpH,SAASC,cAAc,QAAQ,2CAA2C;AAE1E,SAASC,gBAAgB,QAAQ,8CAA8C;AAC/E,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,OAAO,QAAc,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,4BAA4B,EAAEC,uBAAuB,QAAQ,oCAAoC;AAC1G,SAASC,0BAA0B,QAAQ,qDAAqD;AAEhG,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;ICbpCC,EAAA,CAAAC,cAAA,mBAEgE;IADxCD,EAAA,CAAAE,UAAA,qBAAAC,sFAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAG,4BAAA,EAA8B;IAAA,EAAC;IACFT,EAAA,CAAAU,YAAA,EAAW;;;;IAAzEV,EAAA,CAAAW,UAAA,UAAAL,MAAA,CAAAM,sBAAA,CAAgC;;;;;IAO5BZ,EAFF,CAAAC,cAAA,cAC8D,eAEA;IAAAD,EAAA,CAAAa,MAAA,GAAgB;IAC9Eb,EAD8E,CAAAU,YAAA,EAAO,EAC/E;;;;;IAHJV,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,KAAA,KAAAX,MAAA,CAAAY,aAAA,EAA2D;IAEzDlB,EAAA,CAAAmB,SAAA,EAAyD;IAAzDnB,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAAc,eAAA,IAAAM,GAAA,EAAAJ,OAAA,CAAAC,KAAA,KAAAX,MAAA,CAAAY,aAAA,EAAyD;IAAClB,EAAA,CAAAmB,SAAA,EAAgB;IAAhBnB,EAAA,CAAAqB,iBAAA,CAAAL,OAAA,CAAAM,KAAA,CAAgB;;;;;;IAMlFtB,EAAA,CAAAC,cAAA,yBACgD;IADhCD,EAAA,CAAAuB,gBAAA,2BAAAC,mGAAAC,MAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAsB,GAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAA2B,kBAAA,CAAArB,MAAA,CAAAsB,cAAA,EAAAH,MAAA,MAAAnB,MAAA,CAAAsB,cAAA,GAAAH,MAAA;MAAA,OAAAzB,EAAA,CAAAQ,WAAA,CAAAiB,MAAA;IAAA,EAA4B;IAC1CzB,EAAA,CAAAE,UAAA,sBAAA2B,8FAAAJ,MAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAsB,GAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAAwB,sBAAA,CAAAL,MAAA,CAA8B;IAAA,EAAC;IAD7CzB,EAAA,CAAAU,YAAA,EACgD;;;;IADhCV,EAAA,CAAA+B,gBAAA,YAAAzB,MAAA,CAAAsB,cAAA,CAA4B;IAAkC5B,EAAjC,CAAAW,UAAA,iCAAgC,mCAAmC;;;;;;IAKhHX,EAAA,CAAAC,cAAA,mBAEqC;IAAnCD,EAAA,CAAAE,UAAA,qBAAA8B,uFAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAA6B,GAAA;MAAA,MAAA3B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAA4B,oBAAA,EAAsB;IAAA,EAAC;IAAClC,EAAA,CAAAU,YAAA,EAAW;;;;IAD9CV,EADQ,CAAAW,UAAA,SAAAL,MAAA,CAAA6B,UAAA,oCAAsD,aAAA7B,MAAA,CAAA6B,UAAA,sCACF;;;;;;IAQ5DnC,EAAA,CAAAC,cAAA,mBAE4D;IAD1DD,EAAA,CAAAE,UAAA,qBAAAkC,uFAAA;MAAApC,EAAA,CAAAI,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAgC,qBAAA,CAAsB,KAAK,CAAC;IAAA,EAAC;IACkBtC,EAAA,CAAAU,YAAA,EAAW;;;;IAArEV,EAAA,CAAAW,UAAA,eAAAL,MAAA,CAAAiC,UAAA,kBAAAjC,MAAA,CAAAiC,UAAA,CAAAC,cAAA,KAAAlC,MAAA,CAAAmC,QAAA,CAAyD;;;;;;IAE3DzC,EAAA,CAAAC,cAAA,mBAC4D;IADhCD,EAAA,CAAAE,UAAA,qBAAAwC,uFAAA;MAAA1C,EAAA,CAAAI,aAAA,CAAAuC,GAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAgC,qBAAA,CAAsB,KAAK,CAAC;IAAA,EAAC;IACRtC,EAAA,CAAAU,YAAA,EAAW;;;;IAArEV,EAAA,CAAAW,UAAA,eAAAL,MAAA,CAAAiC,UAAA,kBAAAjC,MAAA,CAAAiC,UAAA,CAAAC,cAAA,KAAAlC,MAAA,CAAAmC,QAAA,CAAyD;;;;;;IAO3DzC,EAAA,CAAAC,cAAA,mBAAmG;IAA/BD,EAAA,CAAAE,UAAA,qBAAA0C,uFAAA;MAAA5C,EAAA,CAAAI,aAAA,CAAAyC,IAAA;MAAA,MAAAvC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAwC,gBAAA,EAAkB;IAAA,EAAC;IAAC9C,EAAA,CAAAU,YAAA,EAAW;;;;;IAYxGV,EAFF,CAAAC,cAAA,cAC6D,eACpB;IACrCD,EAAA,CAAA+C,SAAA,YAC6E;IAC/E/C,EAAA,CAAAU,YAAA,EAAO;IACPV,EAAA,CAAAC,cAAA,eAC2D;IAAAD,EAAA,CAAAa,MAAA,GAAgB;IAC7Eb,EAD6E,CAAAU,YAAA,EAAO,EAC9E;;;;;IAPJV,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAiC,QAAA,CAAA/B,KAAA,KAAAX,MAAA,CAAA2C,YAAA,EAA0D;IAE7BjD,EAAA,CAAAmB,SAAA,GAAmB;IAAnBnB,EAAA,CAAAkD,UAAA,CAAAF,QAAA,CAAAG,IAAA,CAAmB;IAC5CnD,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAAc,eAAA,IAAAsC,GAAA,EAAAJ,QAAA,CAAAG,IAAA,yBAAsE;IAGxEnD,EAAA,CAAAmB,SAAA,EAAwD;IAAxDnB,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAAc,eAAA,KAAAM,GAAA,EAAA4B,QAAA,CAAA/B,KAAA,KAAAX,MAAA,CAAA2C,YAAA,EAAwD;IAACjD,EAAA,CAAAmB,SAAA,EAAgB;IAAhBnB,EAAA,CAAAqB,iBAAA,CAAA2B,QAAA,CAAA1B,KAAA,CAAgB;;;;;;IATjFtB,EAAA,CAAAC,cAAA,oBAAmF;IACjFD,EAAA,CAAAqD,UAAA,IAAAC,wEAAA,2BAAuC;IAWzCtD,EAAA,CAAAU,YAAA,EAAS;IACTV,EAAA,CAAAC,cAAA,mBAC6C;IADlBD,EAAA,CAAAE,UAAA,mBAAAqD,qFAAA9B,MAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAoD,IAAA;MAAA,MAAAC,iBAAA,GAAAzD,EAAA,CAAA0D,WAAA;MAAA,OAAA1D,EAAA,CAAAQ,WAAA,CAASiD,iBAAA,CAAAE,MAAA,CAAAlC,MAAA,CAA4B;IAAA,EAAC;IAE/DzB,EAAA,CAAAC,cAAA,eAA6C;IAC3CD,EAAA,CAAA+C,SAAA,QAA0D;IAC1D/C,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAa,MAAA,GAAgB;IAAAb,EAAA,CAAAU,YAAA,EAAO;IAC1CV,EAAA,CAAA+C,SAAA,YAA2D;IAE/D/C,EADE,CAAAU,YAAA,EAAO,EACE;;;;IApB2BV,EAAf,CAAAW,UAAA,eAAc,UAAAL,MAAA,CAAAsD,aAAA,CAA6B;IAaxD5D,EAAA,CAAAmB,SAAA,GAAgB;IAAwCnB,EAAxD,CAAAW,UAAA,iBAAgB,aAAAL,MAAA,CAAAmC,QAAA,CAA6D;IAGhFzC,EAAA,CAAAmB,SAAA,GAAkD;IAAlDnB,EAAA,CAAA6D,sBAAA,KAAAvD,MAAA,CAAAwD,gBAAA,2BAAkD;IAClC9D,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAA2C,YAAA,CAAgB;;;;;;IAOvCjD,EAAA,CAAAC,cAAA,mBACoD;IADtBD,EAAA,CAAAE,UAAA,qBAAA6D,uFAAA;MAAA/D,EAAA,CAAAI,aAAA,CAAA4D,IAAA;MAAA,MAAA1D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAA2D,kBAAA,EAAoB;IAAA,EAAC;IACVjE,EAAA,CAAAU,YAAA,EAAW;;;;IADAV,EAAA,CAAAW,UAAA,aAAAL,MAAA,CAAAmC,QAAA,CAAqB;;;;;;IA/ExFzC,EAFJ,CAAAC,cAAA,oBAA6D,cACxB,SAC7B;IAAAD,EAAA,CAAA+C,SAAA,WAAgC;IAAC/C,EAAA,CAAAa,MAAA,GAA2B;IAClEb,EADkE,CAAAU,YAAA,EAAK,EACjE;IAEJV,EADF,CAAAC,cAAA,cAAiC,eAC8B;IAC3DD,EAAA,CAAAqD,UAAA,IAAAa,yDAAA,uBAAmC;IAMnClE,EAAA,CAAAC,cAAA,oBAAyE;IACvED,EAAA,CAAAqD,UAAA,KAAAc,0DAAA,0BAAuC;IAOzCnE,EAAA,CAAAU,YAAA,EAAS;IAOTV,EALA,CAAAqD,UAAA,KAAAe,0DAAA,6BAA+B,KAAAC,0DAAA,uBAKN;IAMzBrE,EAAA,CAAAC,cAAA,gBACyC;IAMrCD,EAJF,CAAAqD,UAAA,KAAAiB,0DAAA,uBAAoD,KAAAC,0DAAA,OAI3C;IAKTvE,EAAA,CAAAC,cAAA,yBACkF;IADrCD,EAAA,CAAAE,UAAA,qBAAAsE,8EAAA;MAAAxE,EAAA,CAAAI,aAAA,CAAAqE,GAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAoE,WAAA,CAAuB,CAAC,mBAAApE,MAAA,CAAAoE,WAAA,CAAD,CAAC,EAAAC,OAAA;IAAA,EAAa;IACA3E,EAAA,CAAAU,YAAA,EAAgB;IAElGV,EAAA,CAAAqD,UAAA,KAAAuB,0DAAA,uBAA0B;IAI1B5E,EAAA,CAAAC,cAAA,mCAC0D;IAAxDD,EAAA,CAAAE,UAAA,kCAAA2E,qGAAApD,MAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAqE,GAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAwBF,MAAA,CAAAwE,sBAAA,CAAArD,MAAA,CAA8B;IAAA,EAAC;IACzDzB,EAAA,CAAAU,YAAA,EAA0B;IA0B1BV,EAxBA,CAAAqD,UAAA,KAAA0B,0DAAA,OAA0D,KAAAC,0DAAA,uBAwBnC;IAKvBhF,EAAA,CAAAC,cAAA,oBAC6C;IADlBD,EAAA,CAAAE,UAAA,qBAAA+E,yEAAAxD,MAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAqE,GAAA;MAAA,MAAAS,iBAAA,GAAAlF,EAAA,CAAA0D,WAAA;MAAA,OAAA1D,EAAA,CAAAQ,WAAA,CAAW0E,iBAAA,CAAAvB,MAAA,CAAAlC,MAAA,CAA4B;IAAA,EAAC;IAEjEzB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAA+C,SAAA,aAAuD;IAAA/C,EAAA,CAAAU,YAAA,EAAO;IACpEV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAA+C,SAAA,aAA2D;IACnE/C,EADmE,CAAAU,YAAA,EAAO,EAC/D;IACXV,EAAA,CAAA+C,SAAA,qBAA0F;IAE1F/C,EAAA,CAAAC,cAAA,oBACwB;IADOD,EAAA,CAAAE,UAAA,qBAAAiF,yEAAA;MAAAnF,EAAA,CAAAI,aAAA,CAAAqE,GAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAA8E,sBAAA,EAAwB;IAAA,EAAC;IAC3CpF,EAAA,CAAAU,YAAA,EAAW;IAEnCV,EAAA,CAAAC,cAAA,oBACwC;IADZD,EAAA,CAAAE,UAAA,qBAAAmF,yEAAA;MAAArF,EAAA,CAAAI,aAAA,CAAAqE,GAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAWF,MAAA,CAAAgF,kBAAA,EAAoB;IAAA,EAAC;IAKpEtF,EAJgD,CAAAU,YAAA,EAAW,EAC9C,EACF,EACH,EACI;;;;IApGDV,EAAA,CAAAuF,UAAA,CAAAvF,EAAA,CAAAwF,eAAA,KAAAC,GAAA,EAAyB;IAEKzF,EAAA,CAAAmB,SAAA,GAA2B;IAA3BnB,EAAA,CAAA0F,kBAAA,MAAApF,MAAA,CAAAiC,UAAA,kBAAAjC,MAAA,CAAAiC,UAAA,CAAAoD,WAAA,KAA2B;IAI9D3F,EAAA,CAAAmB,SAAA,GAIC;IAJDnB,EAAA,CAAA4F,aAAA,IAAAtF,MAAA,CAAAM,sBAAA,UAIC;IAEmBZ,EAAA,CAAAmB,SAAA,EAAc;IAACnB,EAAf,CAAAW,UAAA,eAAc,UAAAL,MAAA,CAAAuF,MAAA,CAAsB;IAUxD7F,EAAA,CAAAmB,SAAA,GAGC;IAHDnB,EAAA,CAAA4F,aAAA,KAAAtF,MAAA,CAAAwF,kBAAA,WAGC;IAED9F,EAAA,CAAAmB,SAAA,EAIC;IAJDnB,EAAA,CAAA4F,aAAA,MAAAtF,MAAA,CAAAyF,WAAA,WAIC;IAEK/F,EAAA,CAAAmB,SAAA,EAAiD;IAAjDnB,EAAA,CAAAgG,qBAAA,aAAA1F,MAAA,CAAAmC,QAAA,4BAAiD;IAGrDzC,EAAA,CAAAmB,SAAA,EAOC;IAPDnB,EAAA,CAAA4F,aAAA,OAAAtF,MAAA,CAAAiC,UAAA,kBAAAjC,MAAA,CAAAiC,UAAA,CAAAC,cAAA,KAAAlC,MAAA,CAAAmC,QAAA,WAOC;IAEczC,EAAA,CAAAmB,SAAA,GAA6B;IACgBnB,EAD7C,CAAAW,UAAA,SAAAL,MAAA,CAAAoE,WAAA,qBAAApE,MAAA,CAAAoE,WAAA,IAAAvB,IAAA,CAA6B,cAAA7C,MAAA,CAAA2F,eAAA,IAAA3F,MAAA,CAAAmC,QAAA,CACD,UAAAnC,MAAA,CAAAoE,WAAA,CAAsC;IAEjF1E,EAAA,CAAAmB,SAAA,EAEC;IAFDnB,EAAA,CAAA4F,aAAA,KAAAtF,MAAA,CAAA4F,aAAA,WAEC;IAEwBlG,EAAA,CAAAmB,SAAA,EAAyB;IAAyCnB,EAAlE,CAAAW,UAAA,eAAAL,MAAA,CAAAiC,UAAA,CAAyB,WAAAjC,MAAA,CAAA6F,MAAA,CAAkB,aAAA7F,MAAA,CAAAmC,QAAA,CAAsB,YAAAnC,MAAA,CAAA8F,OAAA,CAAoB;IAI9GpG,EAAA,CAAAmB,SAAA,EAsBC;IAtBDnB,EAAA,CAAA4F,aAAA,KAAAtF,MAAA,CAAA+F,gBAAA,KAAA/F,MAAA,CAAAiC,UAAA,kBAAAjC,MAAA,CAAAiC,UAAA,CAAA+D,kBAAA,YAsBC;IAEDtG,EAAA,CAAAmB,SAAA,EAGC;IAHDnB,EAAA,CAAA4F,aAAA,KAAAtF,MAAA,CAAAiG,eAAA,WAGC;IAESvG,EAAA,CAAAmB,SAAA,EAAgB;IAA0CnB,EAA1D,CAAAW,UAAA,iBAAgB,aAAAL,MAAA,CAAAmC,QAAA,CAA+D;IAKlEzC,EAAA,CAAAmB,SAAA,GAAc;IAACnB,EAAf,CAAAW,UAAA,eAAc,UAAAL,MAAA,CAAAkG,WAAA,CAA2B;IAEIxG,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAAW,UAAA,aAAAL,MAAA,CAAAmC,QAAA,CAAqB;IAG5BzC,EAAA,CAAAmB,SAAA,EAAyC;IAAzCnB,EAAA,CAAAW,UAAA,cAAAL,MAAA,CAAA2F,eAAA,IAAA3F,MAAA,CAAAmC,QAAA,CAAyC;;;ADlEhH,OAAM,MAAOgE,oBAAoB;EAyE/BC,YACUC,oBAA0C,EAC1CC,iBAAoC,EACpCC,mBAAwC;IAFxC,KAAAF,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAlEpB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAArE,QAAQ,GAAG,KAAK;IAChB,KAAAQ,YAAY,GAAGrD,uBAAuB,CAACmH,KAAK;IAC5C,KAAAV,gBAAgB,GAAG,KAAK;IACxB,KAAAH,aAAa,GAAG,KAAK;IACrB,KAAAJ,kBAAkB,GAAG,KAAK;IAGzB,KAAAkB,cAAc,GAAsB,IAAInI,YAAY,EAAE;IACtD,KAAAoI,cAAc,GAAsB,IAAIpI,YAAY,EAAE;IACtD,KAAAqI,OAAO,GAAsB,IAAIrI,YAAY,EAAE;IAC/C,KAAAsI,WAAW,GAAsB,IAAItI,YAAY,EAAE;IACnD,KAAAuI,eAAe,GAAsB,IAAIvI,YAAY,EAAE;IACvD,KAAAwI,cAAc,GAAsB,IAAIxI,YAAY,EAAE;IACtD,KAAAyI,WAAW,GAAsB,IAAIzI,YAAY,EAAE;IACnD,KAAA0I,qBAAqB,GAAsB,IAAI1I,YAAY,EAAE;IAC7D,KAAA2I,kBAAkB,GAAyB,IAAI3I,YAAY,EAAE;IAC7D,KAAA4I,aAAa,GAAyB,IAAI5I,YAAY,EAAE;IACxD,KAAA6I,oBAAoB,GAA0B,IAAI7I,YAAY,EAAE;IAChE,KAAA8I,WAAW,GAAyB,IAAI9I,YAAY,EAAE;IAIhE,KAAA2H,WAAW,GAAG,CACZ;MACEvF,KAAK,EAAE,IAAI;MACX2G,UAAU,EAAE;KACb,EACD;MACEtG,KAAK,EAAE,YAAY;MAAE6B,IAAI,EAAE,gBAAgB;MAAEwB,OAAO,EAAEA,CAAA,KAAK;QACzD,IAAI,CAACkD,gBAAgB,EAAE;MACzB;KACD,EACD;MACEvG,KAAK,EAAE,iBAAiB;MAAE6B,IAAI,EAAE,kBAAkB;MAAEwB,OAAO,EAAEA,CAAA,KAAK;QAChE,IAAI,CAACmD,kBAAkB,EAAE;MAC3B;KACD,EACD;MACExG,KAAK,EAAE,eAAe;MAAE6B,IAAI,EAAE,YAAY;MAAEwB,OAAO,EAAEA,CAAA,KAAK;QACxD,IAAI,CAACoD,gBAAgB,EAAE;MACzB;KACD,CACF;IAGD,KAAAlC,MAAM,GAAG,CACP;MAAEvE,KAAK,EAAE,aAAa;MAAE0G,KAAK,EAAE/I;IAAW,CAAE,EAC5C;MAAEqC,KAAK,EAAE,aAAa;MAAE0G,KAAK,EAAEjJ;IAAW,CAAE,EAC5C;MAAEuC,KAAK,EAAE,eAAe;MAAE0G,KAAK,EAAEhJ;IAAa,CAAE,EAChD;MAAEsC,KAAK,EAAE,aAAa;MAAE0G,KAAK,EAAElJ;IAAW,CAAE,CAC7C;IAED,KAAA8E,aAAa,GAAG,EAAE;IAClB,KAAAc,WAAW,GAAG,EAAE;IAEhB,KAAAZ,gBAAgB,GAAW,wBAAwB;IAEnD,KAAAlC,cAAc,GAAG,KAAK;IAGd,KAAAqG,kBAAkB,GAAmB,EAAE;EAM3C;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,GAAG,IAAI,CAACxB,oBAAoB,CAACyB,YAAY,CAC1DC,SAAS,CAACC,KAAK,IAAG;MACjB,IAAI,CAACL,kBAAkB,GAAGK,KAAK,CAACC,IAAI;MACpC,IAAI,CAACC,aAAa,EAAE;IACtB,CAAC,CAAC;IACJ,IAAI,CAAC7B,oBAAoB,CAAC8B,uBAAuB,EAAE;IAEnD,IAAI,CAACC,aAAa,GAAG,CACnB;MACEpH,KAAK,EAAE,UAAU;MACjB6B,IAAI,EAAE,YAAY;MAClBwB,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACzC,oBAAoB,EAAE;MAC7B;KACD,EACD;MACEZ,KAAK,EAAE,KAAK;MACZ6B,IAAI,EAAE,YAAY;MAClBwB,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACrC,qBAAqB,CAAC,KAAK,CAAC;MACnC;KACD,EACD;MACEhB,KAAK,EAAE,MAAM;MACb6B,IAAI,EAAE,iBAAiB;MACvBwB,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACgE,qBAAqB,EAAE;MAC9B;KACD,EACD;MACErH,KAAK,EAAE,UAAU;MACjB6B,IAAI,EAAE,aAAa;MACnBwB,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACiE,cAAc,EAAE;MACvB;KACD,EACD;MACEtH,KAAK,EAAE,MAAM;MACb6B,IAAI,EAAE,YAAY;MAClBwB,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACW,kBAAkB,EAAE;MAC3B;KACD,EACD;MACEhE,KAAK,EAAE,SAAS;MAChB6B,IAAI,EAAE,YAAY;MAClBwB,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACS,sBAAsB,EAAE;MAC/B;KACD,EACD;MACE9D,KAAK,EAAE,YAAY;MACnB6B,IAAI,EAAE,gBAAgB;MACtBwB,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACkD,gBAAgB,EAAE;MACzB;KACD,EACD;MACEvG,KAAK,EAAE,iBAAiB;MACxB6B,IAAI,EAAE,kBAAkB;MACxBwB,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACmD,kBAAkB,EAAE;MAC3B;KACD,EACD;MACExG,KAAK,EAAE,eAAe;MACtB6B,IAAI,EAAE,YAAY;MAClBwB,OAAO,EAAEA,CAAA,KAAK;QACZ,IAAI,CAACoD,gBAAgB,EAAE;MACzB;KACD,CACF;IAED,IAAI,CAACnE,aAAa,GAAG,CACnB;MACE3C,KAAK,EAAE,IAAI;MACX2G,UAAU,EAAE;KACb,EACD;MACE3G,KAAK,EAAErB,uBAAuB,CAACiJ,KAAK;MAAEvH,KAAK,EAAE3B,4BAA4B,CAACkJ,KAAK;MAAE1F,IAAI,EAAE,YAAY;MACnGwB,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACmE,oBAAoB,CAAClJ,uBAAuB,CAACiJ,KAAK,EAAE,YAAY;KACrF,EACD;MACE5H,KAAK,EAAErB,uBAAuB,CAACmJ,IAAI;MAAEzH,KAAK,EAAE3B,4BAA4B,CAACoJ,IAAI;MAAE5F,IAAI,EAAE,sBAAsB;MAC3GwB,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACmE,oBAAoB,CAAClJ,uBAAuB,CAACmJ,IAAI,EAAE,sBAAsB;KAC9F,EACD;MACE9H,KAAK,EAAErB,uBAAuB,CAACmH,KAAK;MAAEzF,KAAK,EAAE3B,4BAA4B,CAACoH,KAAK;MAAE5D,IAAI,EAAE,wBAAwB;MAC/GwB,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACmE,oBAAoB,CAAClJ,uBAAuB,CAACmH,KAAK,EAAE,wBAAwB;KACjG,EACD;MACE9F,KAAK,EAAErB,uBAAuB,CAACoJ,IAAI;MAAE1H,KAAK,EAAE3B,4BAA4B,CAACqJ,IAAI;MAAE7F,IAAI,EAAE,2BAA2B;MAChHwB,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACmE,oBAAoB,CAAClJ,uBAAuB,CAACoJ,IAAI,EAAE,2BAA2B;KACnG,CACF;IAED,IAAI,CAACtE,WAAW,GAAG,CACjB;MACEpD,KAAK,EAAE,MAAM;MAAE6B,IAAI,EAAE,iBAAiB;MAAEwB,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACgE,qBAAqB;KAClF,EACD;MACErH,KAAK,EAAE,UAAU;MAAE6B,IAAI,EAAE,aAAa;MAAEwB,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACiE,cAAc;KAC3E,CACF;EACH;EAEAK,WAAWA,CAAA;IACT,IAAI,IAAI,CAACd,eAAe,EAAE;MACxB,IAAI,CAACA,eAAe,CAACe,WAAW,EAAE;IACpC;EACF;EAEA5G,qBAAqBA,CAAC6G,GAAY;IAChC,IAAI,IAAI,CAAC1G,QAAQ,EAAE;IAEnB,IAAI,CAACuE,cAAc,CAACoC,IAAI,CAACD,GAAG,CAAC;EAC/B;EAEAR,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAAClG,QAAQ,EAAE;IAEnB,IAAI,CAACwE,cAAc,CAACmC,IAAI,EAAE;EAC5B;EAEAR,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACnG,QAAQ,EAAE;IAEnB,IAAI,CAACyE,OAAO,CAACkC,IAAI,EAAE;EACrB;EAEA9D,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC7C,QAAQ,EAAE;IAEnB,IAAI,CAAC0E,WAAW,CAACiC,IAAI,EAAE;EACzB;EAEAhE,sBAAsBA,CAAA;IACpB,IAAI,IAAI,CAAC3C,QAAQ,EAAE;IAEnB,IAAI,CAAC2E,eAAe,CAACgC,IAAI,EAAE;EAC7B;EAEAnF,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACxB,QAAQ,EAAE;IAEnB,IAAI,CAAC6E,WAAW,CAAC8B,IAAI,EAAE;EACzB;EAEA3I,4BAA4BA,CAAA;IAC1B,IAAI,CAAC8G,qBAAqB,CAAC6B,IAAI,EAAE;EACnC;EAEAtG,gBAAgBA,CAAA;IACd,IAAI,CAAC2E,aAAa,CAAC2B,IAAI,EAAE;EAC3B;EAEAZ,aAAaA,CAAA;IACX,IAAI,CAACrG,UAAU,GAAG,IAAI,CAAC8F,kBAAkB,CAACoB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,KAAK,IAAI,CAAChH,UAAU,EAAEgH,QAAQ,IAAID,CAAC,CAACE,KAAK,CAAC;IACxG;IACA,IAAI,CAAC5C,iBAAiB,CAAC6C,aAAa,EAAE;EACxC;EAEAvH,oBAAoBA,CAAA;IAClB,IAAIkG,YAAY,GAAiB,IAAI,CAACH,kBAAkB,EAAEyB,IAAI,CAACC,QAAQ,IACrEA,QAAQ,CAACC,SAAS,CAACC,QAAQ,EAAE,KAAK,IAAI,CAACtH,UAAU,CAACqH,SAAS,IAAID,QAAQ,CAACJ,QAAQ,KAAK,IAAI,CAAChH,UAAU,CAACgH,QAAQ,CAAC;IAEhH,IAAInB,YAAY,EAAE;MAChBA,YAAY,CAAC0B,MAAM,GAAG,KAAK;MAC3B1B,YAAY,CAACoB,KAAK,GAAGO,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK;IACnF,CAAC,MACI;MACH9B,YAAY,GAAG,IAAI,CAAC+B,kBAAkB,EAAE;IAC1C;IACA,IAAI,CAACtD,mBAAmB,CAACuD,kBAAkB,CAAChC,YAAY,CAAC;IACzD;IACA,IAAI,CAACjG,UAAU,GAAGiG,YAAY,CAAC0B,MAAM;EACvC;EAEAK,kBAAkBA,CAAA;IAChB,MAAM/B,YAAY,GAAiB;MACjCwB,SAAS,EAAE,CAAC,IAAI,CAACrH,UAAU,CAACqH,SAAS;MACrCS,GAAG,EAAEN,MAAM,CAACC,QAAQ,CAACC,QAAQ;MAC7BV,QAAQ,EAAE,IAAI,CAAChH,UAAU,CAACgH,QAAQ;MAClCe,UAAU,EAAE,IAAI,CAAC/H,UAAU,CAAC+H,UAAU;MACtCC,gBAAgB,EAAE,CAAC,IAAI,CAAChI,UAAU,CAACgI,gBAAgB;MACnDT,MAAM,EAAE,IAAI;MACZN,KAAK,EAAEO,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG;KAC/D;IACD,OAAO9B,YAAY;EACrB;EAEAP,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACpF,QAAQ,EAAE;IAEnB,IAAI,CAAC4E,cAAc,CAAC+B,IAAI,EAAE;EAC5B;EAEAtB,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACrF,QAAQ,EAAE;IAEnB,IAAI,CAAC0D,MAAM,CAACqE,GAAG,CAACC,iBAAiB,CAAC;MAChCC,mBAAmB,EAAGC,MAAM,IAAI;QAC9B,OAAO,IAAI,CAACC,qBAAqB,CAACD,MAAM,CAAC;MAC3C,CAAC;MACDE,UAAU,EAAE,IAAI,CAACC,eAAe;MAAEC,QAAQ,EAAE,IAAI,CAACxI,UAAU,CAAC+H,UAAU;MAAEU,SAAS,EAAE,IAAI,CAACzI,UAAU,CAAC+H;KACpG,CAAC;EACJ;EAEAvC,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACtF,QAAQ,EAAE;IAEnB,IAAI,CAAC0D,MAAM,CAACqE,GAAG,CAACS,eAAe,CAAC;MAC9BP,mBAAmB,EAAGC,MAAM,IAAI;QAC9B,OAAO,IAAI,CAACC,qBAAqB,CAACD,MAAM,CAAC;MAC3C,CAAC;MACDE,UAAU,EAAE,IAAI,CAACC,eAAe;MAAEC,QAAQ,EAAE,IAAI,CAACxI,UAAU,CAAC+H;KAC7D,CAAC;EACJ;EAEAM,qBAAqBA,CAACD,MAAkC;IACtD,MAAMO,kBAAkB,GAAGP,MAAM,CAACQ,MAAM,CAACC,SAAS,EAAE;IACpD,MAAMC,oBAAoB,GAAG,IAAI,CAAC9I,UAAU,CAAC+I,YAAY,EAAEC,2BAA2B,CAAC7B,IAAI,CAAC8B,CAAC,IAAIA,CAAC,CAACL,MAAM,KAAKD,kBAAkB,CAACO,KAAK,CAAC;IACvI,IAAIJ,oBAAoB,EAAEK,MAAM,CAACC,IAAI,KAAKxM,gBAAgB,CAACyM,QAAQ,EAAE;MACnEV,kBAAkB,CAACW,cAAc,GAAG3M,cAAc,CAChDyL,MAAM,CAAC1J,KAAK,EACZoK,oBAAoB,CAACK,MAAM,CAACI,QAAQ,EACpCT,oBAAoB,CAACK,MAAM,CAACK,aAAa,CAAC;MAC5C,OAAOb,kBAAkB,CAACW,cAAc;IAC1C;IAEA,OAAOlB,MAAM,CAAC1J,KAAK;EACrB;EAEA6H,oBAAoBA,CAACkD,IAA6B,EAAE7I,IAAY;IAC9D,IAAI,CAACqE,kBAAkB,CAAC4B,IAAI,CAAC4C,IAAI,CAAC;IAClC,IAAI,CAAClI,gBAAgB,GAAGX,IAAI;IAC5B,IAAI,CAACF,YAAY,GAAG+I,IAAI;IAGxB;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACC,aAAa,KAAKC,SAAS,EAAE;QACpC,IAAI,CAAChG,MAAM,CAACqE,GAAG,EAAE4B,kBAAkB,EAAE;MACvC;IACF,CAAC,EAAE,CAAC,CAAC;EACP;EAEAtH,sBAAsBA,CAACuH,MAAkB;IACvC,IAAI,CAACzK,cAAc,GAAGyK,MAAM,EAAEC,cAAc,EAAEC,OAAO,KAAK,SAAS;IACnE,IAAI,CAACL,aAAa,GAAGG,MAAM;EAC7B;EAEAvK,sBAAsBA,CAACwG,KAAU;IAC/B,IAAI,CAACZ,oBAAoB,CAAC0B,IAAI,CAACd,KAAK,CAACkE,OAAO,CAAC;EAC/C;;;uBA9UW/F,oBAAoB,EAAAzG,EAAA,CAAAyM,iBAAA,CAAAC,EAAA,CAAAC,oBAAA,GAAA3M,EAAA,CAAAyM,iBAAA,CAAAzM,EAAA,CAAA4M,iBAAA,GAAA5M,EAAA,CAAAyM,iBAAA,CAAAI,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAApBrG,oBAAoB;MAAAsG,SAAA;MAAAC,MAAA;QAAAjH,WAAA;QAAAxD,UAAA;QAAA0D,eAAA;QAAAM,eAAA;QAAA0G,sBAAA;QAAA9G,MAAA;QAAA2E,eAAA;QAAAlK,sBAAA;QAAAkG,WAAA;QAAArE,QAAA;QAAAQ,YAAA;QAAAoD,gBAAA;QAAAH,aAAA;QAAAJ,kBAAA;QAAAM,OAAA;MAAA;MAAA8G,OAAA;QAAAlG,cAAA;QAAAC,cAAA;QAAAC,OAAA;QAAAC,WAAA;QAAAC,eAAA;QAAAC,cAAA;QAAAC,WAAA;QAAAC,qBAAA;QAAAC,kBAAA;QAAAC,aAAA;QAAAC,oBAAA;QAAAC,WAAA;MAAA;MAAAwF,UAAA;MAAAC,QAAA,GAAApN,EAAA,CAAAqN,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC/BjC3N,EAAA,CAAAC,cAAA,aAA0B;UACxBD,EAAA,CAAAqD,UAAA,IAAAwK,2CAAA,yBAAqE;UAuGvE7N,EAAA,CAAAU,YAAA,EAAM;UAEJV,EADF,CAAAC,cAAA,aAA6B,cACA;UAAAD,EAAA,CAAA+C,SAAA,WAAgC;UAAC/C,EAAA,CAAAa,MAAA,GAA2B;UAAAb,EAAA,CAAAU,YAAA,EAAO;UAC9FV,EAAA,CAAA+C,SAAA,mBAA8E;UAC9E/C,EAAA,CAAAC,cAAA,mBAAuG;UAAxDD,EAAA,CAAAE,UAAA,qBAAA4N,0DAAArM,MAAA;YAAAzB,EAAA,CAAAI,aAAA,CAAA2N,GAAA;YAAA,MAAAC,QAAA,GAAAhO,EAAA,CAAA0D,WAAA;YAAA,OAAA1D,EAAA,CAAAQ,WAAA,CAAWwN,QAAA,CAAArK,MAAA,CAAAlC,MAAA,CAAmB;UAAA,EAAC;UAChFzB,EADyG,CAAAU,YAAA,EAAW,EAC9G;;;UA5GJV,EAAA,CAAAmB,SAAA,EAsGC;UAtGDnB,EAAA,CAAA4F,aAAA,KAAAgI,GAAA,CAAAhK,aAAA,kBAAAgK,GAAA,CAAAhK,aAAA,CAAAqK,MAAA,UAAAL,GAAA,CAAAlJ,WAAA,kBAAAkJ,GAAA,CAAAlJ,WAAA,CAAAuJ,MAAA,eAsGC;UAG2DjO,EAAA,CAAAmB,SAAA,GAA2B;UAA3BnB,EAAA,CAAA0F,kBAAA,MAAAkI,GAAA,CAAArL,UAAA,kBAAAqL,GAAA,CAAArL,UAAA,CAAAoD,WAAA,KAA2B;UACzE3F,EAAA,CAAAmB,SAAA,EAAuB;UAACnB,EAAxB,CAAAW,UAAA,UAAAiN,GAAA,CAAAlF,aAAA,CAAuB,eAAe;;;qBD9E1C3I,WAAW,EAAAmO,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EAAE3O,aAAa,EAAA4O,EAAA,CAAAC,OAAA,EAAE9O,aAAa,EAAA+O,EAAA,CAAAC,OAAA,EAAElP,WAAW,EAAAmP,EAAA,CAAAC,aAAA,EAAEnP,OAAO,EAAEF,YAAY,EAAAsP,EAAA,CAAAC,MAAA,EAAExP,UAAU,EAAAyP,EAAA,CAAAC,IAAA,EAAEpP,iBAAiB,EAAAqP,EAAA,CAAAC,WAAA,EAAQnP,0BAA0B,EAAEC,kBAAkB,EAAAmP,GAAA,CAAAC,YAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}