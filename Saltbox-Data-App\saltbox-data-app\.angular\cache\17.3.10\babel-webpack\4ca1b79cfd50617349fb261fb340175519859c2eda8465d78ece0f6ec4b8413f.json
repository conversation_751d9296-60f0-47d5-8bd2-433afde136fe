{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/SaltboxActionableGrid/Saltbox-Data-App/saltbox-data-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AgGridModule } from 'ag-grid-angular';\nimport { firstValueFrom } from 'rxjs';\nimport { CRUDActions, DynamicAssetTypes, ModifierSourceType } from '../core/enums/shared';\nimport { CommunicationToken } from '../core/models/communication-service-type';\nimport { ActionableGridViewModes, ParameterTypes } from '../core/enums/actionable-grid';\nimport { JSONSchemaParam } from '../core/models/json-schema-param';\nimport { deepCopy } from '../shared/utilities/copy.functions';\nimport { CustomDateComponent } from '../core/custom-component/custom-date-component';\nimport { SharedModule } from 'primeng/api';\nimport { UserActivity } from '../shared/models/user-activity';\nimport { RecordsSaveEvent } from '../shared/models/records-save-event';\nimport { AssetEventType } from '../shared/enums/asset-workflow';\nimport { getDefaultValue } from '../shared/utilities/datatype.functions';\nimport { Features } from '../shared/enums/features.enum';\nimport { GroupUpdateInfo } from './model/group-update-info';\nimport { RefineRecordsRequest } from './refine-records/models/refine-records-request';\nimport { DB_PRIMARY_KEY } from '../shared/constants/record.const';\nimport { ConnectorAppConfig } from '../shared/models/connector-app-config';\nimport { UserPermissionLevels } from '../core/enums/shared';\nimport { FormlyRendererTypes } from '../sb-formly-renderer/enums/formly-renderer-types.enum';\nimport { GridFormulaProcessorService } from './services/grid-formula-processor.service';\nimport { AssetUrlPipe } from '../shared/pipes/asset-url.pipe';\nimport { BlockUIModule } from 'primeng/blockui';\nimport { ValidationResultsDialogComponent } from '../business-validation-results/validation-results-dialog.component';\nimport { ConfirmationDialogComponent } from '../shared/confirmation-dialog/confirmation-dialog.component';\nimport { EmailGridComponent } from './email-grid/email-grid.component';\nimport { DataEntryFormDialogComponent } from '../dynamic-forms/data-entry-form-dialog/data-entry-form-dialog.component';\nimport { IframeDialogComponent } from '../shared/iframe-dialog/iframe-dialog.component';\nimport { DynamicFormBodyComponent } from '../dynamic-forms/dynamic-form-body/dynamic-form-body.component';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { ChartsPanelComponent } from './grid-charts/charts-panel/charts-panel.component';\nimport { VisualizationPanelComponent } from '../visualizations/visualization-panel/visualization-panel.component';\nimport { AccordionModule } from 'primeng/accordion';\nimport { UserParamFilterComponent } from './user-param-filter/user-param-filter.component';\nimport { RefineRecordsComponent } from './refine-records/refine-records.component';\nimport { ActionsMenuComponent } from './actions-menu/actions-menu.component';\nimport { NgClass, NgStyle } from '@angular/common';\nimport { PanelModule } from 'primeng/panel';\nimport { SbFullCalendarComponent } from '../sb-full-calendar/sb-full-calendar.component';\nimport { EditorColumnType } from '../shared/enums/editor-column-type.enum';\nimport { SBCalendarOptions } from '../sb-full-calendar/models/sb-calendar-options';\nimport { AgentChatComponent } from '../utilities/agent-chat/agent-chat.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../core/services/notification.service\";\nimport * as i3 from \"./services/actionable-grid.service\";\nimport * as i4 from \"./services/actionable-grid-col.service\";\nimport * as i5 from \"../core/services/resolution.service\";\nimport * as i6 from \"../core/services/datastores.service\";\nimport * as i7 from \"../core/services/user-activity.service\";\nimport * as i8 from \"./services/change-history.service\";\nimport * as i9 from \"../core/services/projects.service\";\nimport * as i10 from \"../core/services/feature-flag.service\";\nimport * as i11 from \"./services/actionable-grid-context-menu.service\";\nimport * as i12 from \"../visualizations/services/tiles.service\";\nimport * as i13 from \"./services/grid-options-service\";\nimport * as i14 from \"../core/services/ag-grid.service\";\nimport * as i15 from \"../core/services/post-save-events.service\";\nimport * as i16 from \"../shared/confirmation-dialog/services/confirmation-dialog.service\";\nimport * as i17 from \"../core/services/user.service\";\nimport * as i18 from \"./services/conditional-evaluation.service\";\nimport * as i19 from \"./services/grid-formula-processor.service\";\nimport * as i20 from \"../core/services/crypto.service\";\nimport * as i21 from \"./services/grid-layouts.service\";\nimport * as i22 from \"../core/services/user-param-filter.service\";\nimport * as i23 from \"primeng/panel\";\nimport * as i24 from \"primeng/api\";\nimport * as i25 from \"primeng/accordion\";\nimport * as i26 from \"ag-grid-angular\";\nimport * as i27 from \"primeng/sidebar\";\nimport * as i28 from \"primeng/blockui\";\nimport * as i29 from \"../core/models/communication-service-type\";\nconst _c0 = [\"sbFullCalendar\"];\nconst _c1 = [\"agGrid\"];\nconst _c2 = [\"chat\"];\nconst _c3 = a0 => ({\n  display: a0\n});\nconst _c4 = () => ({\n  width: \"50rem\"\n});\nconst _c5 = a0 => ({\n  \"preview-actions-fix\": a0\n});\nconst _c6 = (a0, a1) => ({\n  \"hidden\": a0,\n  \"preview-height-fix\": a1\n});\nfunction ActionableGridComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-actions-menu\", 23);\n    i0.ɵɵlistener(\"showFormEditor\", function ActionableGridComponent_Conditional_2_Template_app_actions_menu_showFormEditor_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showFormEditor($event));\n    })(\"undoLastChange\", function ActionableGridComponent_Conditional_2_Template_app_actions_menu_undoLastChange_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.undoLastChange());\n    })(\"undoAll\", function ActionableGridComponent_Conditional_2_Template_app_actions_menu_undoAll_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.undoAll());\n    })(\"saveChanges\", function ActionableGridComponent_Conditional_2_Template_app_actions_menu_saveChanges_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveChanges());\n    })(\"refreshGridData\", function ActionableGridComponent_Conditional_2_Template_app_actions_menu_refreshGridData_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRefreshGridData());\n    })(\"sendEmailClick\", function ActionableGridComponent_Conditional_2_Template_app_actions_menu_sendEmailClick_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSendEmail());\n    })(\"showRefiner\", function ActionableGridComponent_Conditional_2_Template_app_actions_menu_showRefiner_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showRefiner());\n    })(\"showValidationResults\", function ActionableGridComponent_Conditional_2_Template_app_actions_menu_showValidationResults_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showValidationResults = true);\n    })(\"changeSelectedView\", function ActionableGridComponent_Conditional_2_Template_app_actions_menu_changeSelectedView_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.switchView($event));\n    })(\"showAgentChat\", function ActionableGridComponent_Conditional_2_Template_app_actions_menu_showAgentChat_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onShowAgentChat());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"previewMode\", ctx_r2.isPreview)(\"reportInfo\", ctx_r2.reportInfo)(\"checkHasPendingChanges\", ctx_r2.hasPendingChanges() && !ctx_r2.showFormlyDataEntryDialog)(\"checkHasChanges\", ctx_r2.hasChanges() && !ctx_r2.showFormlyDataEntryDialog)(\"checkHasSlicers\", ctx_r2.hasSlicer())(\"agGrid\", ctx_r2.agGrid)(\"columnsToExport\", ctx_r2.columnsToExport)(\"disabled\", ctx_r2.projectIsLocked)(\"selectedView\", ctx_r2.selectedView)(\"validationResultsCount\", (ctx_r2.saveRecordsResponse == null ? null : ctx_r2.saveRecordsResponse.failedValidations == null ? null : ctx_r2.saveRecordsResponse.failedValidations.length) > 9 ? \"9+\" : ctx_r2.saveRecordsResponse == null ? null : ctx_r2.saveRecordsResponse.failedValidations == null ? null : ctx_r2.saveRecordsResponse.failedValidations.length)(\"hasFailures\", ctx_r2.saveRecordsResponse == null ? null : ctx_r2.saveRecordsResponse.hasFailures)(\"calendarViewFlag\", ctx_r2.calendarViewFlag)(\"agentChatFlag\", ctx_r2.agentChatFlag)(\"layouts\", ctx_r2.layouts)(\"ngClass\", i0.ɵɵpureFunction1(15, _c5, ctx_r2.isPreview));\n  }\n}\nfunction ActionableGridComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-refine-records\", 24);\n    i0.ɵɵlistener(\"hideSliderPanel\", function ActionableGridComponent_Conditional_3_Template_app_refine_records_hideSliderPanel_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onHideSliderPanel($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"datastoreId\", ctx_r2.datastore.id)(\"reportId\", ctx_r2.reportId)(\"dataViewId\", ctx_r2.reportInfo.dataStoreViewName)(\"refinerFields\", ctx_r2.refinerFields)(\"showRefinerPanel\", ctx_r2.showRefinerPanel)(\"datastore\", ctx_r2.datastore);\n  }\n}\nfunction ActionableGridComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-user-param-filter\", 25);\n    i0.ɵɵlistener(\"userEnteredParamsSubmitted\", function ActionableGridComponent_Conditional_4_Template_app_user_param_filter_userEnteredParamsSubmitted_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUserEnteredParamsSubmitted());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"userParameters\", ctx_r2.userParameters)(\"datastore\", ctx_r2.datastore)(\"reportId\", ctx_r2.reportInfo == null ? null : ctx_r2.reportInfo.reportId)(\"dataViewId\", ctx_r2.reportInfo == null ? null : ctx_r2.reportInfo.dataStoreViewName);\n  }\n}\nfunction ActionableGridComponent_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-accordionTab\", 8);\n    i0.ɵɵelement(1, \"app-visualization-panel\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selected\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"tilesConfigs\", ctx_r2.tilesConfigs);\n  }\n}\nfunction ActionableGridComponent_Conditional_7_For_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-charts-panel\", 29);\n  }\n  if (rf & 2) {\n    const chart_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"chartConfig\", chart_r6)(\"gridApi\", ctx_r2.agGrid.api);\n  }\n}\nfunction ActionableGridComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-accordionTab\", 9)(1, \"div\", 27)(2, \"div\", 28);\n    i0.ɵɵrepeaterCreate(3, ActionableGridComponent_Conditional_7_For_4_Template, 1, 2, \"app-charts-panel\", 29, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"selected\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵrepeater(ctx_r2.chartConfigs);\n  }\n}\nfunction ActionableGridComponent_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-message\", 11);\n  }\n}\nfunction ActionableGridComponent_Conditional_10_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-sb-full-calendar\", 32, 2);\n    i0.ɵɵlistener(\"eventClick\", function ActionableGridComponent_Conditional_10_Conditional_3_Template_app_sb_full_calendar_eventClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.showFormEditor(true, $event));\n    })(\"dataUpdated\", function ActionableGridComponent_Conditional_10_Conditional_3_Template_app_sb_full_calendar_dataUpdated_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onCalendarDataUpdated($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"data\", ctx_r2.calendarData)(\"options\", ctx_r2.calendarOptions)(\"selectedView\", ctx_r2.selectedView);\n  }\n}\nfunction ActionableGridComponent_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"ag-grid-angular\", 30, 1);\n    i0.ɵɵlistener(\"columnRowGroupChanged\", function ActionableGridComponent_Conditional_10_Template_ag_grid_angular_columnRowGroupChanged_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onColumnRowGroupChanged($event));\n    })(\"gridReady\", function ActionableGridComponent_Conditional_10_Template_ag_grid_angular_gridReady_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onGridReady($event));\n    })(\"cellFocused\", function ActionableGridComponent_Conditional_10_Template_ag_grid_angular_cellFocused_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCellFocused($event));\n    })(\"firstDataRendered\", function ActionableGridComponent_Conditional_10_Template_ag_grid_angular_firstDataRendered_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onFirstDataRendered($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ActionableGridComponent_Conditional_10_Conditional_3_Template, 2, 3, \"app-sb-full-calendar\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"--ag-value-change-value-highlight-background-color\", ctx_r2.flashingCellColor);\n    i0.ɵɵproperty(\"rowData\", ctx_r2.reportData)(\"columnDefs\", ctx_r2.reportInfo == null ? null : ctx_r2.reportInfo.columnDefs)(\"animateRows\", true)(\"gridOptions\", ctx_r2.gridOptions)(\"suppressRowClickSelection\", false)(\"groupHideOpenParents\", true)(\"rowSelection\", \"single\")(\"rowHeight\", 40)(\"headerHeight\", 40)(\"enableCharts\", true)(\"chartThemeOverrides\", ctx_r2.chartThemeOverrides)(\"getContextMenuItems\", ctx_r2.contextMenuItems)(\"ngClass\", i0.ɵɵpureFunction2(16, _c6, ctx_r2.selectedView !== \"Table\", ctx_r2.isPreview));\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(3, ctx_r2.selectedView !== \"Table\" && ctx_r2.calendarOptions ? 3 : -1);\n  }\n}\nfunction ActionableGridComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\");\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.jsonSchemaParam.title, \"\");\n  }\n}\nfunction ActionableGridComponent_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-dynamic-form-body\", 34);\n    i0.ɵɵtwoWayListener(\"dataChange\", function ActionableGridComponent_Conditional_13_Template_app_dynamic_form_body_dataChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.jsonSchemaParam.data, $event) || (ctx_r2.jsonSchemaParam.data = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"formOnSubmit\", function ActionableGridComponent_Conditional_13_Template_app_dynamic_form_body_formOnSubmit_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDataEntryFormSubmit($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"dynamicForm\", ctx_r2.formAsset == null ? null : ctx_r2.formAsset.targetObj);\n    i0.ɵɵtwoWayProperty(\"data\", ctx_r2.jsonSchemaParam.data);\n    i0.ɵɵproperty(\"projectId\", ctx_r2.projectId)(\"projectVersionId\", ctx_r2.projectVersionId)(\"datastore\", ctx_r2.datastore)(\"recordId\", ctx_r2.selectedRecordId)(\"showFormOptions\", false)(\"userPermissionLevel\", ctx_r2.userPermissionLevel)(\"showSubmitButton\", true);\n  }\n}\nfunction ActionableGridComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"h4\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵtext(2, \" Chat\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ActionableGridComponent_Conditional_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-iframe-dialog\", 37);\n    i0.ɵɵtwoWayListener(\"visibleChange\", function ActionableGridComponent_Conditional_18_Template_app_iframe_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.showConnectorScreen, $event) || (ctx_r2.showConnectorScreen = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"dialogClosed\", function ActionableGridComponent_Conditional_18_Template_app_iframe_dialog_dialogClosed_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.connectorAppClose());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"idFieldValue\", ctx_r2.formAsset == null ? null : ctx_r2.formAsset.targetObj == null ? null : ctx_r2.formAsset.targetObj.connectorIdFieldValue)(\"src\", ctx_r2.formAsset == null ? null : ctx_r2.formAsset.targetObj == null ? null : ctx_r2.formAsset.targetObj.iframeUrl)(\"title\", ctx_r2.formAsset == null ? null : ctx_r2.formAsset.targetObj == null ? null : ctx_r2.formAsset.targetObj.iframeTitle);\n    i0.ɵɵtwoWayProperty(\"visible\", ctx_r2.showConnectorScreen);\n  }\n}\nfunction ActionableGridComponent_Conditional_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-email-grid\", 38);\n    i0.ɵɵlistener(\"displayChange\", function ActionableGridComponent_Conditional_20_Template_app_email_grid_displayChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onEmailDialogClose($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"reportInfo\", ctx_r2.reportInfo)(\"gridApi\", ctx_r2.agGrid == null ? null : ctx_r2.agGrid.api)(\"columnsToExport\", ctx_r2.columnsToExport);\n  }\n}\nfunction ActionableGridComponent_Conditional_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-validation-results-dialog\", 39);\n    i0.ɵɵtwoWayListener(\"visibleChange\", function ActionableGridComponent_Conditional_22_Template_app_validation_results_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.showValidationResults, $event) || (ctx_r2.showValidationResults = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"saveRecordsResponse\", ctx_r2.saveRecordsResponse);\n    i0.ɵɵtwoWayProperty(\"visible\", ctx_r2.showValidationResults);\n    i0.ɵɵproperty(\"data\", ctx_r2.reportData)(\"datastore\", ctx_r2.datastore);\n  }\n}\nfunction ActionableGridComponent_Conditional_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-blockUI\", 22);\n    i0.ɵɵelement(1, \"img\", 40);\n    i0.ɵɵpipe(2, \"assetUrl\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"blocked\", ctx_r2.isShowSpinner);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", i0.ɵɵpipeBind1(2, 2, \"layout/images/salt-box-loading.gif\"), i0.ɵɵsanitizeUrl);\n  }\n}\nexport class ActionableGridComponent {\n  constructor(activatedRoute, router, notificationService, actionableGridService, actionableGridColService, resolutionService, datastoresService, userActivityService, changeHistoryService, projectsService, featureFlagService, actionableGridContextMenuService, tilesService, gridOptionsService, aGGridService, postSaveEventService, confirmationDialogService, userService, conditionalEvaluationService, formulaProcessorService, cryptoService, gridLayoutService, filterService, communicationService) {\n    this.activatedRoute = activatedRoute;\n    this.router = router;\n    this.notificationService = notificationService;\n    this.actionableGridService = actionableGridService;\n    this.actionableGridColService = actionableGridColService;\n    this.resolutionService = resolutionService;\n    this.datastoresService = datastoresService;\n    this.userActivityService = userActivityService;\n    this.changeHistoryService = changeHistoryService;\n    this.projectsService = projectsService;\n    this.featureFlagService = featureFlagService;\n    this.actionableGridContextMenuService = actionableGridContextMenuService;\n    this.tilesService = tilesService;\n    this.gridOptionsService = gridOptionsService;\n    this.aGGridService = aGGridService;\n    this.postSaveEventService = postSaveEventService;\n    this.confirmationDialogService = confirmationDialogService;\n    this.userService = userService;\n    this.conditionalEvaluationService = conditionalEvaluationService;\n    this.formulaProcessorService = formulaProcessorService;\n    this.cryptoService = cryptoService;\n    this.gridLayoutService = gridLayoutService;\n    this.filterService = filterService;\n    this.communicationService = communicationService;\n    this.selectedRecordId = '';\n    this.projectIsLocked = false;\n    this.requiredColumns = [];\n    this.refinerFields = [];\n    this.newRowIdPrefix = '__';\n    this.newRowId = 0;\n    this.isPreview = false;\n    this.sbiWrapperPad = 'sbi-view-pad';\n    this.showUserParameter = false;\n    this.showAgentChatSidebar = false;\n    // This is to expand all row groups by default.\n    this.groupDefaultExpanded = -1;\n    this.jsonSchemaParam = new JSONSchemaParam();\n    this.showDataEntryForm = false;\n    this.showFormlyDataEntryDialog = false;\n    this.showConnectorScreen = false;\n    this.showRefinerPanel = false;\n    this.showValidationResults = false;\n    this.showEmailForm = false;\n    this.columnsToExport = [];\n    this.contextMenuItems = params => this.actionableGridContextMenuService.getContextMenuItems(params, this.agGrid.api);\n    this.gridCharts = [];\n    this.gridOptions = {\n      components: {\n        agDateInput: CustomDateComponent\n      },\n      defaultColDef: {\n        // enable sorting across all columns - including Row Group Columns\n        sortable: true,\n        filter: 'agMultiColumnFilter',\n        resizable: true,\n        enableValue: true,\n        enableRowGroup: true,\n        enablePivot: true\n      },\n      detailRowAutoHeight: true,\n      customChartThemes: {\n        sbChartsTheme: {\n          palette: {\n            fills: ['#007BB5', '#c35467', '#6197c4', '#d78a92', '#c3d2e2', '#e6bdc0'],\n            strokes: ['#ccc']\n          }\n        }\n      },\n      pivotMode: false,\n      sideBar: \"columns\",\n      pivotPanelShow: \"always\",\n      rowGroupPanelShow: 'always',\n      chartThemes: ['sbChartsTheme', 'ag-material'],\n      getRowId: params => {\n        return String(params.data[DB_PRIMARY_KEY]);\n      }\n    };\n    // for user entered parameters\n    this.userParameters = [];\n    this.showActionableGrid = false;\n    this.isShowSpinner = false;\n    this.disableFileUpload = false;\n    this.visualizationFlag = false;\n    this.gridChartsFlag = false;\n    this.tilesConfigs = [];\n    this.chartConfigs = [];\n    this.connectorAppConfig = new ConnectorAppConfig();\n    this.selectedView = ActionableGridViewModes.Table; // Default to Table view\n  }\n  setResolution(event) {\n    this.resolutionService.setResolution(event.innerWidth, event.innerHeight);\n  }\n  unloadNotification($event) {\n    if (!this.canDeactivate()) {\n      $event.returnValue = true;\n    }\n  }\n  ngOnInit() {\n    if (this.activatedRoute) {\n      this.activatedRoute.queryParams.subscribe(urlParams => {\n        // check if the route variable is coming from Reporting iFrame request (url variable: hideMenu)\n        this.isPreview = urlParams.hideMenus;\n      });\n      this.activatedRoute.params.subscribe(params => {\n        this.reportId = params.reportId;\n        this.projectId = params.projectId;\n        this.projectVersionId = params.projectVersionId;\n        // logging user activity\n        if (!this.isPreview) {\n          this.userActivityService.addUserActivity(new UserActivity({\n            projectId: this.projectId,\n            projectVersionId: this.projectVersionId,\n            url: window.location.pathname,\n            reportId: this.reportId\n          }));\n        }\n        this.loadActionableGrid();\n      });\n    }\n  }\n  loadActionableGrid() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // RESET THE DEFAULTS WHEN THE COMPONENT RELOADS\n      _this.tilesConfigs = [];\n      _this.chartConfigs = [];\n      _this.showUserParameter = false;\n      _this.showActionableGrid = false;\n      _this.reportInfo = undefined;\n      _this.reportData = [];\n      _this.unmodifiedRowData = [];\n      _this.reportData = [];\n      _this.datastore = undefined;\n      _this.recordRefiners = [];\n      _this.calendarOptions = undefined;\n      _this.saveRecordsResponse = undefined;\n      _this.selectedView = ActionableGridViewModes.Table; // Default to Table view\n      _this.isShowSpinner = true;\n      _this.projectVersion = yield _this.getProjectVersion();\n      if (_this.projectVersion) {\n        _this.projectIsLocked = _this.projectVersion.isLocked;\n      }\n      yield _this.setFeatureFlags();\n      if (_this.router.url.startsWith('/saltboxdataapp/embeddedview')) {\n        _this.isPreview = true;\n        _this.hideNavigationApp();\n        _this.sbiWrapperPad = 'sbi-view-pad-none';\n      }\n      if (!_this.isPreview) {\n        if (_this.router.url.startsWith('/saltboxdataapp/app-view')) {\n          yield _this.getAppUserPermission();\n          if (_this.userPermissionLevel === UserPermissionLevels.None) {\n            return;\n          }\n        }\n      }\n      if (_this.reportId) {\n        // refactored out into its own method\n        _this.loadReportInfo();\n      }\n      _this.actionableGridService.projectIdSubject$.subscribe(pId => _this.fileUploadProjectId = pId);\n      _this.actionableGridService.versionIdSubject$.subscribe(vId => _this.fileUploadProjectVersionId = vId);\n      _this.actionableGridService.datastoreNameSubject$.subscribe(dSn => _this.fileUploadDataStoreName = dSn);\n    })();\n  }\n  // After checking the content and ensuring it's loaded - remove the menus\n  ngAfterContentInit() {\n    if (this.isPreview) {\n      this.hideMenusForEmbed();\n    }\n  }\n  // this should be the new way to do it.  will need to refactor the other feature flag calls\n  setFeatureFlags() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const featureFlags = yield _this2.featureFlagService.getFeatureFlags();\n      _this2.visualizationFlag = featureFlags.some(x => x.name === Features.Visualizations);\n      _this2.gridChartsFlag = featureFlags.some(x => x.name === Features.Visualizations);\n      _this2.calendarViewFlag = featureFlags.some(x => x.name === Features.CalendarView);\n      _this2.agentChatFlag = featureFlags.some(x => x.name === Features.AgentChat);\n      _this2.pivotModeFlag = true; // featureFlags.some(x => x.name === Features.PivotMode);\n    })();\n  }\n  loadReportInfo() {\n    var _this3 = this;\n    this.actionableGridService.getReportInfo(this.reportId).subscribe({\n      next: function () {\n        var _ref = _asyncToGenerator(function* (reportInfo) {\n          if (!reportInfo) {\n            _this3.notificationService.showError('Error', `No report found for Report ID ${_this3.reportId}`);\n            return;\n          }\n          // extracting paramList from reportInfo\n          _this3.reportInfo = reportInfo;\n          _this3.userParameters = reportInfo.params;\n          _this3.slicerParams = deepCopy(_this3.userParameters);\n          _this3.reportInfo.includeIdColumn = true;\n          if (_this3.userPermissionLevel === UserPermissionLevels.View) {\n            _this3.reportInfo.allowAddNewRow = false;\n          }\n          if (_this3.reportInfo.formatConfig?.tilesConfig) {\n            _this3.tilesConfigs.push(..._this3.reportInfo.formatConfig.tilesConfig);\n          }\n          _this3.changeHistoryService.initChangeHistory(_this3.reportInfo.projectId, _this3.reportInfo.projectVersionId);\n          _this3.requiredColumns = _this3.reportInfo?.formatConfig?.actionableGridColumnsConfig?.filter(actionableGridColumnConfig => actionableGridColumnConfig.required);\n          _this3.refinerFields = _this3.reportInfo?.formatConfig?.actionableGridColumnsConfig?.filter(actionableGridColumnConfig => actionableGridColumnConfig.slicerFilter);\n          _this3.gridOptionsService.setRowClassRules(_this3.gridOptions, _this3.requiredColumns);\n          // Enabling pivot mode\n          if (_this3.pivotModeFlag) _this3.gridOptionsService.enablePivotMode(_this3.gridOptions);\n          // setting the row actions context. it's only here because of the allowDeleteRow\n          const fieldsConfig = _this3.actionableGridColService.getFormlyFieldsConfigByGridConfig(_this3.reportInfo?.formatConfig?.actionableGridColumnsConfig);\n          _this3.gridOptions.context = {\n            parentComponent: _this3,\n            allowDeleteRow: _this3.reportInfo?.allowAddNewRow,\n            allowUploadFiles: true,\n            userPermissionLevel: _this3.userPermissionLevel,\n            showEditorFunction: editMode => _this3.showFormEditor(editMode),\n            deleteRowFunction: data => _this3.deleteRow(data),\n            getMainGridId: () => {\n              return _this3.agGrid.api.getGridId();\n            },\n            getMasterRecordParams: recordId => {\n              return {\n                baseSchema: _this3.datastore.baseSchema,\n                data: _this3.agGrid?.api?.getRowNode(recordId)?.data,\n                fieldsConfig\n              };\n            }\n          };\n          if (_this3.reportInfo.enablePagination) {\n            _this3.gridOptions.pagination = true;\n            _this3.gridOptions.paginateChildRows = true;\n            _this3.gridOptions.paginationAutoPageSize = _this3.reportInfo.paginationAutoPaging;\n            if (!_this3.reportInfo.paginationAutoPaging) {\n              _this3.gridOptions.paginationPageSize = _this3.reportInfo.defaultPageSize;\n              _this3.gridOptions.paginationPageSizeSelector = [20, 50, 100];\n            }\n          }\n          // Loading datastore\n          _this3.datastoresService.getDatastores(_this3.reportInfo.projectId, _this3.reportInfo.projectVersionId, _this3.reportInfo.formName).subscribe( /*#__PURE__*/function () {\n            var _ref2 = _asyncToGenerator(function* (datastores) {\n              if (!datastores || datastores.length === 0) {\n                _this3.notificationService.showError('Error', `Datastore not found!`);\n                _this3.isShowSpinner = false;\n                return;\n              }\n              if (datastores.length > 1) {\n                _this3.notificationService.showError('Error', `Multiple datastore with the same name have been found!`);\n                _this3.isShowSpinner = false;\n                return;\n              }\n              _this3.datastore = datastores[0];\n              _this3.reportInfo.columnDefs = yield _this3.actionableGridColService.getColumnDefs(_this3.reportInfo.formatConfig?.actionableGridColumnsConfig, _this3.datastore.baseSchema, _this3.gridOptions, true, _this3.projectVersionId, _this3.reportInfo.allowAddNewRow, null, _this3.userPermissionLevel, true, _this3.pivotModeFlag);\n              _this3.setConditionalFormatting();\n              // setting the columns which has to be exported\n              _this3.columnsToExport = _this3.reportInfo?.columnDefs?.filter(x => x.field !== 'fileUpload' && x.field !== '_id').map(x => x.field);\n              _this3.showUserParameter = _this3.reportInfo?.params?.filter(x => x.paramType === ParameterTypes.UserEntered)?.length > 0;\n              _this3.subscribeToDataReload();\n              // Loading grid layouts\n              yield _this3.loadLayouts();\n            });\n            return function (_x2) {\n              return _ref2.apply(this, arguments);\n            };\n          }());\n          _this3.buildBreadcrumb();\n        });\n        return function next(_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()\n    });\n  }\n  setConditionalFormatting() {\n    if (!this.reportInfo.enableConditionalFormatting || !this.reportInfo?.conditionalFormattingConfig?.length) return;\n    const setStyles = profileProperties => {\n      // Row Styles\n      const getRowStyleFunc = params => {\n        return this.gridOptionsService.getRowStyle(params.data, this.reportInfo?.conditionalFormattingConfig, profileProperties);\n      };\n      this.agGrid?.api ? this.agGrid.api.setGridOption('getRowStyle', getRowStyleFunc) : this.gridOptions.getRowStyle = getRowStyleFunc;\n      // Cell Styles\n      this.gridOptionsService.setCellStyles(this.reportInfo.columnDefs, this.reportInfo.conditionalFormattingConfig, profileProperties);\n      if (this.agGrid?.api) this.agGrid.api.setGridOption('columnDefs', this.reportInfo.columnDefs);\n      this.agGrid?.api?.redrawRows();\n    };\n    if (this.reportInfo?.conditionalFormattingConfig?.find(c => c.conditions?.some(c => c.isProfileParameter))) {\n      this.userService.getCompleteUserProfilePropertyList().subscribe({\n        next: profileProperties => {\n          setStyles(profileProperties);\n        }\n      });\n    } else {\n      setStyles([]);\n    }\n  }\n  subscribeToDataReload() {\n    this.filterService.reloadData$.subscribe(params => {\n      if (params.clearData) {\n        this.saveRecordsResponse = undefined;\n        if (this.reportData?.length > 0) {\n          this.unmodifiedRowData = [];\n          this.reportData = [];\n        }\n        this.isShowSpinner = false;\n        return;\n      }\n      this.slicerParams = params.params;\n      this.showActionableGrid = true;\n      this.loadReportData(this.slicerParams, true);\n    });\n  }\n  loadLayouts() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      let layouts = [];\n      // If by any chance the grid layout service throws an error, we will catch it and continue without layouts\n      try {\n        layouts = yield firstValueFrom(_this4.gridLayoutService.getLayouts(_this4.reportInfo.reportId));\n      } catch (error) {\n        layouts = [];\n      }\n      if (layouts.length > 0) {\n        const layoutHashId = yield _this4.gridLayoutService.getGridHashId(_this4.reportInfo);\n        const defaultLayout = layouts.find(layout => layout.isCurrentLayout && layout.layoutHashId === layoutHashId) ?? layouts.find(layout => layout.isDefault && layout.layoutHashId === layoutHashId);\n        _this4.layouts = layouts;\n        // If there are saved slicers or user parameters and all parameters are optional, layout manager will load the report\n        if (defaultLayout && (defaultLayout.hasRecordRefiners || defaultLayout.hasUserParams)) {\n          _this4.showActionableGrid = true;\n          return;\n        }\n      } else {\n        _this4.layouts = [];\n      }\n    })();\n  }\n  getAppUserPermission() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      _this5.userPermissionLevel = UserPermissionLevels[yield _this5.userService.getUserAppGroupRoles(_this5.projectVersionId, _this5.projectVersion.appManifest, _this5.reportId)];\n      if (_this5.userPermissionLevel === UserPermissionLevels.None) {\n        _this5.notificationService.showError('Access Denied', \"You do not have access to this grid. Please request access from your app admin.\");\n        _this5.isShowSpinner = false;\n      }\n    })();\n  }\n  loadReportData(params, showSpinner = false) {\n    var _this6 = this;\n    if (this.filterService.validateUserEnteredParams(params, true) === false) return;\n    if (showSpinner) this.isShowSpinner = showSpinner;\n    // Resetting the profile properties so that the latest values are fetched from the db\n    const refineRequest = new RefineRecordsRequest(this.reportId, this.reportInfo.dataStoreViewName, true, params);\n    // Subscribe() would be enough to bind the grid, we are just setting the backup dataset here\n    this.datastoresService.getRecords(this.datastore.id, refineRequest).subscribe({\n      next: function () {\n        var _ref3 = _asyncToGenerator(function* (reportData) {\n          if (reportData) {\n            _this6.reportData = reportData;\n            _this6.unmodifiedRowData = deepCopy(_this6.reportData);\n            // default to calendar view if calendar view is enabled\n            if (_this6.calendarViewFlag && _this6.reportInfo.enableCalendarView && !_this6.calendarData) {\n              yield _this6.checkViewHashId();\n              _this6.setCalendarOptions();\n              _this6.switchView(_this6.reportInfo.calendarViewConfig?.initialView);\n            }\n            if (_this6.selectedView !== ActionableGridViewModes.Table) _this6.calendarData = _this6.reportData;\n            // for refresh button so spinner timing is dependent on completion\n            _this6.isShowSpinner = false;\n          }\n        });\n        return function next(_x3) {\n          return _ref3.apply(this, arguments);\n        };\n      }(),\n      error: error => {\n        if (error.status === 404) {\n          this.reportData = [];\n          this.unmodifiedRowData = [];\n        }\n        this.notificationService.showError('Error', error.message);\n        this.isShowSpinner = false;\n      }\n    });\n    // checking if agGrid is initialized to prevent a console error\n    this.agGrid?.api?.refreshCells({\n      force: true\n    });\n  }\n  onFirstDataRendered(params) {\n    this.loadGridCharts();\n  }\n  loadGridCharts() {\n    if (this.reportInfo.formatConfig?.chartConfigs) {\n      this.chartConfigs.push(...this.reportInfo.formatConfig.chartConfigs);\n    }\n  }\n  // If the embedded view is triggered we use the CommunicationService to remove the menus inside the iframe\n  hideMenusForEmbed() {\n    // Communication Service call to remove Menus\n    this.hideNavigationApp();\n    this.sbiWrapperPad = 'sbi-view-pad-none';\n  }\n  onColumnRowGroupChanged(event) {\n    const groupedColumns = event.columns.map(item => item.colId);\n    const autoColumn = event.api.getColumn('ag-Grid-AutoColumn'); // This is the 'Group' Column.\n    if (autoColumn !== null) {\n      autoColumn.colDef.headerName = `Group by ${groupedColumns.join(', ')}`;\n      event.api.refreshHeader();\n      event.api.autoSizeColumn(autoColumn);\n    }\n  }\n  onGridReady(params) {\n    const datastoreStructure = this.actionableGridService.getDatastoreStructure(this.reportInfo.formatConfig?.actionableGridColumnsConfig, this.datastore.baseSchema);\n    this.aGGridService.registerGridDataProvider(params.api.getGridId(), {\n      grid: this.agGrid,\n      datastoreStructure\n    });\n    this.formulaProcessorService.setupDataChangeListener(this.agGrid);\n    this.lockRowsActionSubscription = this.datastoresService.lockRowsAction.subscribe(lock => {\n      if (lock) {\n        this.changeHistoryService.getAllModifiedRecords().forEach(modifiedRecord => modifiedRecord.saveApplied = lock);\n      }\n    });\n  }\n  saveChanges() {\n    if (!this.hasChanges()) {\n      return;\n    }\n    const errors = this.getValidationErrors();\n    if (errors?.length) {\n      this.notificationService.showError('Error', errors.join(',\\n'));\n      return;\n    }\n    this.isShowSpinner = true;\n    this.addInvokeWorkflowEvent();\n    this.datastoresService.saveRecords(this.changeHistoryService.getBatchChnages(true), this.datastore.id).subscribe({\n      next: saveRecordsResponse => {\n        this.saveRecordsResponse = saveRecordsResponse;\n        this.isShowSpinner = false;\n        this.showValidationResults = this.saveRecordsResponse?.failedValidations?.length > 0;\n        if (!saveRecordsResponse.hasFailures) {\n          // warning if some records sent to update but they didn't\n          if (saveRecordsResponse.recordIds?.length != this.changeHistoryService.getBatchChnages()?.getMasterRecordObjectIds()?.size) {\n            this.notificationService.showWarning(\"Warning\", \"The record(s) you are trying to update do not exist or have already been updated. Only the existing record(s) were updated.\");\n          }\n          this.flashSavedRows(true);\n          this.changeHistoryService.resetPendingChanges();\n          this.notificationService.showSuccess('Success', 'Saved Successfully');\n          // note: if loadReportData happens fast user will not set the flash changes\n          this.loadReportData(this.slicerParams);\n        }\n      },\n      error: err => {\n        this.isShowSpinner = false;\n        this.flashSavedRows(false);\n        this.notificationService.showError('Error', err.error);\n      }\n    });\n  }\n  getValidationErrors() {\n    const errors = [];\n    if (!this.requiredColumns?.length) {\n      return errors;\n    }\n    this.changeHistoryService.getAllModifiedRecords().forEach(modifiedRecord => {\n      if (!modifiedRecord?.data || modifiedRecord.data?.action === CRUDActions.Delete || modifiedRecord.data?.action === CRUDActions.DeleteArray) return;\n      const requiredDisplayNames = [];\n      const missingRequiredProperties = modifiedRecord.data?.properties?.filter(changeDetail => changeDetail.newValue == null || changeDetail.newValue === '');\n      this.requiredColumns.forEach(requiredColumn => {\n        const rowData = this.reportData.find(row => row[DB_PRIMARY_KEY] === modifiedRecord.id);\n        if (!rowData) {\n          return;\n        }\n        const modifiedProperty = missingRequiredProperties.find(changeDetail => changeDetail.property === requiredColumn.column);\n        if (modifiedProperty || rowData[requiredColumn.column] == null || rowData[requiredColumn.column] === '') {\n          requiredDisplayNames.push(requiredColumn.displayName || requiredColumn.column);\n        }\n      });\n      if (requiredDisplayNames?.length) {\n        if (requiredDisplayNames.length === 1) {\n          errors.push(`${requiredDisplayNames[0]} is a mandatory field and must be provided`);\n        } else {\n          const displayNameCsv = requiredDisplayNames.slice(0, requiredDisplayNames.length - 1).join(', ');\n          errors.push(`${displayNameCsv} & ${requiredDisplayNames[requiredDisplayNames.length - 1]} are mandatory fields and must be provided`);\n        }\n      }\n    });\n    return errors;\n  }\n  addInvokeWorkflowEvent() {\n    const saveGridEvent = this.reportInfo.gridEvents?.find(gridEvent => gridEvent.eventType === AssetEventType.GridSave);\n    if (!saveGridEvent) {\n      return;\n    }\n    this.postSaveEventService.addInvokeWorkflowEvent({\n      recordsSaveEvent: new RecordsSaveEvent(this.reportInfo.reportId, this.reportInfo.reportName, ModifierSourceType.ActionableGrid, this.datastore.name, this.datastore.id),\n      assetEvent: saveGridEvent\n    });\n  }\n  flashSavedRows(isSuccessful) {\n    if (this.selectedView !== ActionableGridViewModes.Table) return;\n    this.flashingCellColor = isSuccessful ? 'rgb(106, 186, 69, .35)' : 'pink';\n    const modifiedRecordNodes = [];\n    for (const modifiedRecord of this.changeHistoryService.getAllModifiedRecords().filter(x => !x.recordPath)) {\n      if (modifiedRecord.data?.action === CRUDActions.Delete) {\n        continue;\n      }\n      if (modifiedRecord.saveApplied) {\n        const rowNode = this.agGrid.api.getRowNode(modifiedRecord.id);\n        if (rowNode) modifiedRecordNodes.push(rowNode);\n      }\n    }\n    if (modifiedRecordNodes.length === 0) return;\n    this.agGrid.api.flashCells({\n      rowNodes: modifiedRecordNodes,\n      flashDuration: 5000,\n      fadeDuration: 2000\n    });\n  }\n  undoAll() {\n    // we need to make sure if this is the optimized way\n    if (this.unmodifiedRowData) {\n      this.reportData = deepCopy(this.unmodifiedRowData);\n      this.changeHistoryService.resetPendingChanges();\n      this.saveRecordsResponse = undefined;\n      this.agGrid.api.updateGridOptions({\n        rowData: this.reportData\n      });\n      this.calendarData = this.reportData;\n    }\n  }\n  undoLastChange() {\n    // Note: reportData and ag grid row data is different, ag grid apply transactions doesn't apply on the actual data\n    const recordIds = this.changeHistoryService.undo(this.agGrid, this.aGGridService.getRowData(this.agGrid.api));\n    if (this.sbFullCalendarComponent) {\n      const data = [];\n      recordIds.forEach(id => {\n        const node = this.agGrid.api.getRowNode(id);\n        if (node) {\n          data.push(node.data);\n        }\n      });\n      this.sbFullCalendarComponent?.updateEvents(data);\n    }\n  }\n  getProjectVersion() {\n    return this.projectsService.getProjectVersion(`${this.projectVersionId}`);\n  }\n  buildBreadcrumb() {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      if (_this7.projectVersion) {\n        const items = [{\n          label: 'My Apps',\n          routerLink: '/dashboard-v3'\n        }, {\n          label: _this7.projectVersion.appManifest?.name,\n          routerLink: `/app-view/${_this7.reportInfo.projectId}/${_this7.reportInfo.projectVersionId}`\n        }, {\n          label: _this7.reportInfo?.reportName\n        }];\n        _this7.communicationService.updateBreadcrumb(items);\n      }\n    })();\n  }\n  hideNavigationApp() {\n    this.communicationService.next({\n      fromAppName: this.communicationService.currentAppName,\n      toAppName: this.communicationService.navigationAppName,\n      eventType: 'HideNavigationBar',\n      data: {}\n    });\n  }\n  onUserEnteredParamsSubmitted(event) {\n    this.showActionableGrid = true;\n    this.userParameters = deepCopy(this.userParameters);\n    this.slicerParams = deepCopy(this.userParameters);\n    const parameterFilters = this.userParameters.map(up => {\n      return {\n        'label': up.paramSource,\n        'value': up.parameterValue\n      };\n    });\n    this.tilesService.updateFilterParams(parameterFilters);\n  }\n  onHideSliderPanel(event) {\n    this.showRefinerPanel = event;\n  }\n  canDeactivate() {\n    if (this.changeHistoryService.hasModifiedRecords()) {\n      return confirm('There are unsaved changes. Do you want to continue without saving?');\n    }\n    return true;\n  }\n  showFormEditor(editMode, calendarData) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      if (!calendarData && (!editMode && !_this8.reportInfo?.allowAddNewRow || editMode && (_this8.agGrid.api.getSelectedNodes().length < 1 || _this8.agGrid.api.getSelectedNodes()[0].group))) {\n        return;\n      }\n      const handleUnsavedChanges = onCancelAction => {\n        if (_this8.changeHistoryService.hasChanges() || _this8.changeHistoryService.hasModifiedRecords()) {\n          _this8.confirmationDialogService.showDialog(\"actionable-grid\", {\n            header: 'Unsaved Changes Detected',\n            message: 'You have made changes that have not been saved. What would you like to do?',\n            okButtonText: 'Return to Editing',\n            cancelButtonText: 'Discard Changes',\n            onCancel: onCancelAction\n          });\n          return true;\n        }\n        return false;\n      };\n      const data = calendarData ? calendarData : editMode ? _this8.agGrid.api.getSelectedNodes()[0].data : undefined;\n      _this8.formAsset = yield _this8.conditionalEvaluationService.getFormAsset(_this8.projectId, _this8.projectVersionId, data, _this8.reportInfo.enableConditionalForms ? _this8.reportInfo.conditionalFormsConfig : undefined, _this8.reportInfo?.formatConfig?.dynamicAssetInfo, _this8.reportInfo.formatConfig?.dynamicFormId, _this8.reportInfo.formatConfig.actionableGridColumnsConfig);\n      _this8.selectedRecordId = editMode ? data[DB_PRIMARY_KEY] : '';\n      let excludedRenderes = [];\n      switch (_this8.formAsset?.assetInfo?.type) {\n        case DynamicAssetTypes.ConnectorApp:\n          if (!_this8.formAsset?.targetObj) return;\n          _this8.formAsset.targetObj.connectorIdField = _this8.formAsset.assetInfo.idField;\n          _this8.formAsset.targetObj.connectorIdFieldValue = data ? data[_this8.connectorAppConfig.connectorIdField] : undefined;\n          if (handleUnsavedChanges(() => {\n            _this8.undoAll();\n            _this8.showConnectorScreen = true;\n          })) return;\n          _this8.showConnectorScreen = true;\n          return;\n        case DynamicAssetTypes.DynamicForm:\n          if (handleUnsavedChanges(() => {\n            _this8.undoAll();\n            _this8.showFormlyDataEntryDialog = true;\n          })) return;\n          _this8.showFormlyDataEntryDialog = true;\n          break;\n        case DynamicAssetTypes.Default:\n          // filtering the arrays and objects ** Note: type of target object is DynamicForm\n          excludedRenderes = [FormlyRendererTypes.Object, FormlyRendererTypes.CollectionForm, FormlyRendererTypes.CollectionGrid, FormlyRendererTypes.CollectionRepeat];\n          _this8.formAsset.targetObj.fieldsConfig = _this8.formAsset.targetObj.fieldsConfig.filter(fc => !excludedRenderes.includes(fc.type));\n          _this8.jsonSchemaParam.action = editMode ? CRUDActions.Update : CRUDActions.Create;\n          _this8.jsonSchemaParam.title = editMode ? 'Update Selected Record' : 'Add New Record';\n          _this8.jsonSchemaParam.originalData = editMode ? data : yield _this8.actionableGridService.getNewRowData(_this8.reportInfo.formatConfig?.actionableGridColumnsConfig);\n          _this8.jsonSchemaParam.data = deepCopy(_this8.jsonSchemaParam.originalData);\n          _this8.showDataEntryForm = true;\n          return;\n        default:\n          break;\n      }\n    })();\n  }\n  showRefiner() {\n    this.showRefinerPanel = !this.showRefinerPanel;\n  }\n  onDataEntryFormSubmit(event) {\n    const data = event.data;\n    if (!data) {\n      this.showDataEntryForm = false;\n      return;\n    }\n    this.jsonSchemaParam.data = data;\n    const fields = Object.getOwnPropertyNames(this.datastore.baseSchema.properties);\n    let id = undefined;\n    switch (this.jsonSchemaParam.action) {\n      case CRUDActions.Update:\n        {\n          const originalData = deepCopy(this.jsonSchemaParam.originalData);\n          id = originalData[DB_PRIMARY_KEY];\n          const selectedNode = this.agGrid.api.getRowNode(id);\n          if (selectedNode) {\n            fields.forEach(field => {\n              let fieldValue = data[field];\n              if (fieldValue != this.jsonSchemaParam.originalData[field]) {\n                const fieldType = this.datastore.baseSchema.properties[field].type;\n                const fieldFormat = this.datastore.baseSchema.properties[field].format;\n                fieldValue = fieldValue == null ? getDefaultValue(fieldType, fieldFormat) : fieldValue;\n                // If this field exists in the grid, we need to update the grid, if not, we need to update the data\n                if (this.agGrid.api.getColumnDef(field)) {\n                  selectedNode.setDataValue(field, fieldValue);\n                } else {\n                  selectedNode.data[field] = fieldValue;\n                }\n              }\n            });\n            // Update the data in the calendar\n            this.sbFullCalendarComponent?.updateEvents([selectedNode.data]);\n            this.changeHistoryService.trackGroupUpdate([new GroupUpdateInfo(id, originalData, selectedNode.data)]);\n            this.updateMetadata(selectedNode, event.aliasIds);\n            this.agGrid.api.refreshClientSideRowModel('group');\n          }\n        }\n        break;\n      case CRUDActions.Create:\n        {\n          id = this.newRowIdPrefix + this.newRowId.toString();\n          const newRowData = {};\n          newRowData[DB_PRIMARY_KEY] = id;\n          fields.forEach(field => {\n            const fieldType = this.datastore.baseSchema.properties[field].type;\n            const fieldFormat = this.datastore.baseSchema.properties[field].format;\n            const fieldValue = data[field] !== undefined && data[field] !== null ? data[field] : getDefaultValue(fieldType, fieldFormat);\n            // first we need to create the new rowdata, and we are doing this to cover undefined fields\n            newRowData[field] = fieldValue;\n          });\n          // Update the data in the calendar\n          this.sbFullCalendarComponent?.updateEvents([newRowData]);\n          this.changeHistoryService.trackAdd(id, newRowData);\n          this.reportData.push(newRowData);\n          this.agGrid.api.applyTransaction({\n            add: [newRowData]\n          });\n          this.agGrid.api.refreshClientSideRowModel('group');\n          this.updateMetadata(this.agGrid.api.getRowNode(id), event.aliasIds);\n          this.aGGridService.scrollToRowNode(this.agGrid.api, id);\n          this.newRowId++;\n        }\n        break;\n    }\n    this.aGGridService.triggerGridDataChangeEvent(this.agGrid.api.getGridId(), id);\n    this.showDataEntryForm = false;\n  }\n  onDataEntryFormHide() {\n    this.actionableGridService.dataEntryFormClosed.next(this.jsonSchemaParam.originalData[DB_PRIMARY_KEY]);\n  }\n  // for refresh button\n  onRefreshGridData() {\n    if (!this.showActionableGrid) return;\n    const reset = () => {\n      this.saveRecordsResponse = undefined;\n      this.userService.reset();\n      this.setConditionalFormatting();\n      this.loadReportData(this.slicerParams, true);\n    };\n    if (this.hasPendingChanges() || this.hasChanges()) {\n      this.confirmationDialogService.showDialog(\"actionable-grid\", {\n        header: 'Confirm Refresh',\n        message: 'Unsaved changes detected, do you want to refresh without saving?',\n        okButtonText: 'Confirm',\n        onConfirm: () => {\n          this.changeHistoryService.resetPendingChanges();\n          reset();\n        }\n      });\n    } else {\n      reset();\n    }\n  }\n  hasChanges() {\n    return this.changeHistoryService.hasModifiedRecords();\n  }\n  hasSlicer() {\n    return this.refinerFields?.length > 0;\n  }\n  hasPendingChanges() {\n    return this.changeHistoryService.hasChanges();\n  }\n  onCellFocused(event) {\n    this.actionableGridService.focusOnCellEditor(event?.column?.colId, event.rowIndex, this.agGrid.api);\n  }\n  updateMetadata(rowNode, aliasIds) {\n    if (aliasIds) {\n      const rowNodeAliasIds = rowNode.data?.__sbmeta?.Attachments;\n      if (!rowNodeAliasIds || rowNodeAliasIds.filter(x => !aliasIds.includes(x)).concat(aliasIds.filter(x => !rowNodeAliasIds.includes(x))).length !== 0) {\n        this.changeHistoryService.trackAttachments(rowNode.id, aliasIds);\n        rowNode.data.__sbmeta = this.actionableGridService.updateAttachments(rowNode.data.__sbmeta, aliasIds);\n        rowNode.setData(rowNode.data);\n      }\n    }\n  }\n  onEmailDialogClose(event) {\n    this.showEmailForm = event;\n  }\n  onSendEmail() {\n    this.showEmailForm = true;\n  }\n  deleteRow(data) {\n    const id = data[DB_PRIMARY_KEY];\n    const rowNode = this.agGrid.api.getRowNode(id);\n    this.changeHistoryService.trackDelete(id, data, rowNode.rowIndex);\n    this.agGrid.api.applyTransaction({\n      remove: [data]\n    });\n  }\n  connectorAppClose() {\n    this.loadReportData(this.slicerParams, true);\n  }\n  switchView(view) {\n    if (this.selectedView === ActionableGridViewModes.Table && view !== ActionableGridViewModes.Table) this.calendarData = this.agGrid?.api ? this.aGGridService.getRowData(this.agGrid.api) : this.reportData;\n    this.selectedView = view;\n  }\n  checkViewHashId() {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      const colsToShow = _this9.reportInfo.dataStoreViewName ? _this9.datastore.dataViews.find(view => view.viewId === _this9.reportInfo.dataStoreViewName)?.columnsToShow : 'none';\n      const newHashId = colsToShow === 'none' ? 'none' : yield _this9.cryptoService.hashSHA256(colsToShow?.sort()?.toString());\n      if (_this9.reportInfo.calendarViewConfig.viewHashId === newHashId) {\n        return;\n      }\n      _this9.notificationService.showWarning('The DataView associated with this grid has been editted. Errors may be present with Calendar and Grid views. Please contact your administrator if any errors are encountered.');\n    })();\n  }\n  onCalendarDataUpdated(params) {\n    if (!params.prevData || !params.prevData[DB_PRIMARY_KEY] || !this.agGrid.api.getRowNode(params.prevData[DB_PRIMARY_KEY])) {\n      this.notificationService.showError('Error', 'Record not found! Please refresh the data and try again.');\n      console.error(`Record with the given ID:${params.prevData ? params.prevData[DB_PRIMARY_KEY] : 'undefined'} not found to update!`);\n      return;\n    }\n    this.changeHistoryService.trackGroupUpdate([new GroupUpdateInfo(params.prevData[DB_PRIMARY_KEY], params.prevData, params.newData)]);\n    this.agGrid.api.applyTransaction({\n      update: [params.newData]\n    });\n    this.agGrid.api.refreshClientSideRowModel('group');\n  }\n  setCalendarOptions() {\n    const config = this.reportInfo?.calendarViewConfig;\n    if (!config) return;\n    const columns = {\n      startDate: this.getColumnConfig(config.startDateCol),\n      endDate: this.getColumnConfig(config.endDateCol),\n      resourceId: this.getColumnConfig(config.resourceIdCol),\n      resourceTitle: this.getColumnConfig(config.resourceTitleCol)\n    };\n    this.calendarOptions = new SBCalendarOptions({\n      baseSchemaProps: this.datastore.baseSchema.properties,\n      calendarViewConfig: config,\n      isPreview: this.isPreview,\n      showTime: columns.startDate?.format?.type === EditorColumnType.DateTime && columns.endDate?.format?.type === EditorColumnType.DateTime,\n      startEditable: columns.startDate?.allowUserUpdate ?? false,\n      startType: [EditorColumnType.DateTime, EditorColumnType.Date].includes(columns.startDate?.format?.type) ? columns.startDate?.format?.type : undefined,\n      // If the type is something like link, we will follow the datastore\n      endType: [EditorColumnType.DateTime, EditorColumnType.Date].includes(columns.endDate?.format?.type) ? columns.endDate?.format?.type : undefined,\n      // If the type is something like link, we will follow the datastore\n      durationEditable: (config.endDateCol && columns.endDate?.allowUserUpdate) ?? false,\n      resourceEditable: (config.resourceIdCol && columns.resourceId?.allowUserUpdate) ?? false,\n      resourceTitleEditable: (config.resourceTitleCol && columns.resourceTitle?.allowUserUpdate) ?? false\n    });\n  }\n  getColumnConfig(columnName) {\n    return this.reportInfo?.formatConfig?.actionableGridColumnsConfig?.find(x => x.column === columnName);\n  }\n  onHideAgentChat() {\n    this.showAgentChatSidebar = false;\n    this.onRefreshGridData();\n  }\n  onShowAgentChat() {\n    this.showAgentChatSidebar = true;\n    setTimeout(() => {\n      this.agentChat.scrollToBottom();\n    }, 0);\n  }\n  static {\n    this.ɵfac = function ActionableGridComponent_Factory(t) {\n      return new (t || ActionableGridComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.NotificationService), i0.ɵɵdirectiveInject(i3.ActionableGridService), i0.ɵɵdirectiveInject(i4.ActionableGridColService), i0.ɵɵdirectiveInject(i5.ResolutionService), i0.ɵɵdirectiveInject(i6.DatastoresService), i0.ɵɵdirectiveInject(i7.UserActivityService), i0.ɵɵdirectiveInject(i8.ChangeHistoryService), i0.ɵɵdirectiveInject(i9.ProjectsService), i0.ɵɵdirectiveInject(i10.FeatureFlagService), i0.ɵɵdirectiveInject(i11.ActionableGridContextMenuService), i0.ɵɵdirectiveInject(i12.TilesService), i0.ɵɵdirectiveInject(i13.GridOptionsService), i0.ɵɵdirectiveInject(i14.AGGridService), i0.ɵɵdirectiveInject(i15.PostSaveEventService), i0.ɵɵdirectiveInject(i16.ConfirmationDialogService), i0.ɵɵdirectiveInject(i17.UserService), i0.ɵɵdirectiveInject(i18.ConditionalEvaluationService), i0.ɵɵdirectiveInject(i19.GridFormulaProcessorService), i0.ɵɵdirectiveInject(i20.CryptoService), i0.ɵɵdirectiveInject(i21.GridLayoutsService), i0.ɵɵdirectiveInject(i22.UserParamFilterService), i0.ɵɵdirectiveInject(CommunicationToken));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActionableGridComponent,\n      selectors: [[\"app-actionable-grid\"]],\n      viewQuery: function ActionableGridComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5, SbFullCalendarComponent);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sbFullCalendarComponent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.agGrid = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.agentChat = _t.first);\n        }\n      },\n      hostBindings: function ActionableGridComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function ActionableGridComponent_resize_HostBindingHandler($event) {\n            return ctx.setResolution($event.target);\n          }, false, i0.ɵɵresolveWindow)(\"beforeunload\", function ActionableGridComponent_beforeunload_HostBindingHandler($event) {\n            return ctx.unloadNotification($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([GridFormulaProcessorService]), i0.ɵɵStandaloneFeature],\n      decls: 24,\n      vars: 30,\n      consts: [[\"chat\", \"\"], [\"agGrid\", \"\"], [\"sbFullCalendar\", \"\"], [\"styleClass\", \"py-0 mb-0\", 1, \"actionable-grid-d-container\", 3, \"showHeader\", \"ngClass\"], [1, \"sbi-actionable-grid-display\"], [3, \"previewMode\", \"reportInfo\", \"checkHasPendingChanges\", \"checkHasChanges\", \"checkHasSlicers\", \"agGrid\", \"columnsToExport\", \"disabled\", \"selectedView\", \"validationResultsCount\", \"hasFailures\", \"calendarViewFlag\", \"agentChatFlag\", \"layouts\", \"ngClass\"], [3, \"userParameters\", \"datastore\", \"reportId\", \"dataViewId\"], [\"multiple\", \"true\", 1, \"accordion-nb\", 3, \"ngStyle\"], [\"header\", \"Visualizations\", 3, \"selected\"], [\"header\", \"Chart\", 3, \"selected\"], [1, \"px-2\"], [\"severity\", \"light\", \"styleClass\", \"w-full max-w-full\", \"text\", \"Project is Locked\"], [\"appendTo\", \"body\", \"blockScroll\", \"true\", \"position\", \"right\", \"styleClass\", \"sidebar-sm\", 3, \"visibleChange\", \"onHide\", \"visible\"], [\"pTemplate\", \"header\"], [\"submitBtnIcon\", \"pi pi-save\", 3, \"dynamicForm\", \"data\", \"projectId\", \"projectVersionId\", \"datastore\", \"recordId\", \"showFormOptions\", \"userPermissionLevel\", \"showSubmitButton\"], [\"appendTo\", \"body\", \"blockScroll\", \"true\", \"position\", \"right\", \"styleClass\", \"agent-sidebar min-w-min py-0\", 3, \"visibleChange\", \"onHide\", \"visible\"], [3, \"assetId\"], [3, \"idFieldValue\", \"src\", \"title\", \"visible\"], [3, \"visibleChange\", \"afterSave\", \"visible\", \"dynamicForm\", \"datastore\", \"recordId\", \"projectId\", \"projectVersionId\", \"userPermissionLevel\"], [3, \"reportInfo\", \"gridApi\", \"columnsToExport\"], [\"id\", \"actionable-grid\"], [3, \"saveRecordsResponse\", \"visible\", \"data\", \"datastore\"], [3, \"blocked\"], [3, \"showFormEditor\", \"undoLastChange\", \"undoAll\", \"saveChanges\", \"refreshGridData\", \"sendEmailClick\", \"showRefiner\", \"showValidationResults\", \"changeSelectedView\", \"showAgentChat\", \"previewMode\", \"reportInfo\", \"checkHasPendingChanges\", \"checkHasChanges\", \"checkHasSlicers\", \"agGrid\", \"columnsToExport\", \"disabled\", \"selectedView\", \"validationResultsCount\", \"hasFailures\", \"calendarViewFlag\", \"agentChatFlag\", \"layouts\", \"ngClass\"], [3, \"hideSliderPanel\", \"datastoreId\", \"reportId\", \"dataViewId\", \"refinerFields\", \"showRefinerPanel\", \"datastore\"], [3, \"userEnteredParamsSubmitted\", \"userParameters\", \"datastore\", \"reportId\", \"dataViewId\"], [3, \"tilesConfigs\"], [1, \"p-3\"], [1, \"grid\"], [1, \"col-12\", \"sm:col-6\", \"flex-grow-1\", 3, \"chartConfig\", \"gridApi\"], [\"id\", \"agGrid\", \"suppressMenuHide\", \"true\", 1, \"ag-theme-material\", \"ag-display-size\", 3, \"columnRowGroupChanged\", \"gridReady\", \"cellFocused\", \"firstDataRendered\", \"rowData\", \"columnDefs\", \"animateRows\", \"gridOptions\", \"suppressRowClickSelection\", \"groupHideOpenParents\", \"rowSelection\", \"rowHeight\", \"headerHeight\", \"enableCharts\", \"chartThemeOverrides\", \"getContextMenuItems\", \"ngClass\"], [1, \"max-h-24rem\", 3, \"data\", \"options\", \"selectedView\"], [1, \"max-h-24rem\", 3, \"eventClick\", \"dataUpdated\", \"data\", \"options\", \"selectedView\"], [1, \"sb\", \"sb-icon-form\"], [\"submitBtnIcon\", \"pi pi-save\", 3, \"dataChange\", \"formOnSubmit\", \"dynamicForm\", \"data\", \"projectId\", \"projectVersionId\", \"datastore\", \"recordId\", \"showFormOptions\", \"userPermissionLevel\", \"showSubmitButton\"], [1, \"m-0\"], [1, \"fa-regular\", \"fa-comment\"], [3, \"visibleChange\", \"dialogClosed\", \"idFieldValue\", \"src\", \"title\", \"visible\"], [3, \"displayChange\", \"reportInfo\", \"gridApi\", \"columnsToExport\"], [3, \"visibleChange\", \"saveRecordsResponse\", \"visible\", \"data\", \"datastore\"], [1, \"ui-progress-spinner\", 3, \"src\"]],\n      template: function ActionableGridComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"p-panel\", 3)(1, \"div\", 4);\n          i0.ɵɵtemplate(2, ActionableGridComponent_Conditional_2_Template, 1, 17, \"app-actions-menu\", 5)(3, ActionableGridComponent_Conditional_3_Template, 2, 6, \"div\")(4, ActionableGridComponent_Conditional_4_Template, 1, 4, \"app-user-param-filter\", 6);\n          i0.ɵɵelementStart(5, \"p-accordion\", 7);\n          i0.ɵɵtemplate(6, ActionableGridComponent_Conditional_6_Template, 2, 2, \"p-accordionTab\", 8)(7, ActionableGridComponent_Conditional_7_Template, 5, 1, \"p-accordionTab\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 10);\n          i0.ɵɵtemplate(9, ActionableGridComponent_Conditional_9_Template, 1, 0, \"p-message\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, ActionableGridComponent_Conditional_10_Template, 4, 19, \"div\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"p-sidebar\", 12);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function ActionableGridComponent_Template_p_sidebar_visibleChange_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.showDataEntryForm, $event) || (ctx.showDataEntryForm = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onHide\", function ActionableGridComponent_Template_p_sidebar_onHide_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onDataEntryFormHide());\n          });\n          i0.ɵɵtemplate(12, ActionableGridComponent_ng_template_12_Template, 3, 1, \"ng-template\", 13)(13, ActionableGridComponent_Conditional_13_Template, 1, 9, \"app-dynamic-form-body\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"p-sidebar\", 15);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function ActionableGridComponent_Template_p_sidebar_visibleChange_14_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.showAgentChatSidebar, $event) || (ctx.showAgentChatSidebar = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onHide\", function ActionableGridComponent_Template_p_sidebar_onHide_14_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onHideAgentChat());\n          });\n          i0.ɵɵtemplate(15, ActionableGridComponent_ng_template_15_Template, 3, 0, \"ng-template\", 13);\n          i0.ɵɵelement(16, \"app-chat\", 16, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, ActionableGridComponent_Conditional_18_Template, 1, 4, \"app-iframe-dialog\", 17);\n          i0.ɵɵelementStart(19, \"app-data-entry-form-dialog\", 18);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function ActionableGridComponent_Template_app_data_entry_form_dialog_visibleChange_19_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.showFormlyDataEntryDialog, $event) || (ctx.showFormlyDataEntryDialog = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"afterSave\", function ActionableGridComponent_Template_app_data_entry_form_dialog_afterSave_19_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.loadReportData(ctx.slicerParams, true));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, ActionableGridComponent_Conditional_20_Template, 1, 3, \"app-email-grid\", 19);\n          i0.ɵɵelement(21, \"app-confirmation-dialog\", 20);\n          i0.ɵɵtemplate(22, ActionableGridComponent_Conditional_22_Template, 1, 4, \"app-validation-results-dialog\", 21)(23, ActionableGridComponent_Conditional_23_Template, 3, 4, \"p-blockUI\", 22);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"showHeader\", false)(\"ngClass\", ctx.sbiWrapperPad);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(2, ctx.reportInfo ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(3, ctx.datastore && ctx.reportInfo ? 3 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(4, ctx.showUserParameter ? 4 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(27, _c3, ctx.showActionableGrid ? \"block\" : \"none\"));\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(6, ctx.visualizationFlag && (ctx.tilesConfigs == null ? null : ctx.tilesConfigs.length) > 0 ? 6 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(7, ctx.gridChartsFlag && (ctx.chartConfigs == null ? null : ctx.chartConfigs.length) > 0 ? 7 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(9, ctx.projectIsLocked ? 9 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(10, !ctx.projectIsLocked && ctx.showActionableGrid ? 10 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.showDataEntryForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(13, ctx.showDataEntryForm ? 13 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.showAgentChatSidebar);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"assetId\", ctx.reportId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(18, ctx.showConnectorScreen ? 18 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(29, _c4));\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.showFormlyDataEntryDialog);\n          i0.ɵɵproperty(\"dynamicForm\", ctx.formAsset == null ? null : ctx.formAsset.targetObj)(\"datastore\", ctx.datastore)(\"recordId\", ctx.selectedRecordId)(\"projectId\", ctx.fileUploadProjectId)(\"projectVersionId\", ctx.fileUploadProjectVersionId)(\"userPermissionLevel\", ctx.userPermissionLevel);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(20, ctx.showEmailForm ? 20 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(22, ctx.showValidationResults ? 22 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(23, ctx.isShowSpinner ? 23 : -1);\n        }\n      },\n      dependencies: [PanelModule, i23.Panel, i24.PrimeTemplate, NgClass, ActionsMenuComponent, RefineRecordsComponent, UserParamFilterComponent, AccordionModule, i25.Accordion, i25.AccordionTab, NgStyle, VisualizationPanelComponent, ChartsPanelComponent, AgGridModule, i26.AgGridAngular, SidebarModule, i27.Sidebar, SharedModule, DynamicFormBodyComponent, IframeDialogComponent, DataEntryFormDialogComponent, EmailGridComponent, ConfirmationDialogComponent, ValidationResultsDialogComponent, BlockUIModule, i28.BlockUI, AssetUrlPipe, SbFullCalendarComponent, AgentChatComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AgGridModule", "firstValueFrom", "CRUDActions", "DynamicAssetTypes", "ModifierSourceType", "CommunicationToken", "ActionableGridViewModes", "ParameterTypes", "JSONSchemaParam", "deepCopy", "CustomDateComponent", "SharedModule", "UserActivity", "RecordsSaveEvent", "AssetEventType", "getDefaultValue", "Features", "GroupUpdateInfo", "RefineRecordsRequest", "DB_PRIMARY_KEY", "ConnectorAppConfig", "UserPermissionLevels", "FormlyRendererTypes", "GridFormulaProcessorService", "AssetUrlPipe", "BlockUIModule", "ValidationResultsDialogComponent", "ConfirmationDialogComponent", "EmailGridComponent", "DataEntryFormDialogComponent", "IframeDialogComponent", "DynamicFormBodyComponent", "SidebarModule", "ChartsPanelComponent", "VisualizationPanelComponent", "AccordionModule", "UserParamFilterComponent", "RefineRecordsComponent", "ActionsMenuComponent", "Ng<PERSON><PERSON>", "NgStyle", "PanelModule", "SbFullCalendarComponent", "EditorColumnType", "SBCalendarOptions", "AgentChatComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "ActionableGridComponent_Conditional_2_Template_app_actions_menu_showFormEditor_0_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "showFormEditor", "ActionableGridComponent_Conditional_2_Template_app_actions_menu_undoLastChange_0_listener", "undoLastChange", "ActionableGridComponent_Conditional_2_Template_app_actions_menu_undoAll_0_listener", "undoAll", "ActionableGridComponent_Conditional_2_Template_app_actions_menu_saveChanges_0_listener", "saveChanges", "ActionableGridComponent_Conditional_2_Template_app_actions_menu_refreshGridData_0_listener", "onRefreshGridData", "ActionableGridComponent_Conditional_2_Template_app_actions_menu_sendEmailClick_0_listener", "onSendEmail", "ActionableGridComponent_Conditional_2_Template_app_actions_menu_showRefiner_0_listener", "showRefiner", "ActionableGridComponent_Conditional_2_Template_app_actions_menu_showValidationResults_0_listener", "showValidationResults", "ActionableGridComponent_Conditional_2_Template_app_actions_menu_changeSelectedView_0_listener", "switchView", "ActionableGridComponent_Conditional_2_Template_app_actions_menu_showAgentChat_0_listener", "onShowAgentChat", "ɵɵelementEnd", "ɵɵproperty", "isPreview", "reportInfo", "has<PERSON><PERSON><PERSON><PERSON><PERSON>", "showFormlyDataEntryDialog", "has<PERSON><PERSON><PERSON>", "hasSlicer", "agGrid", "columnsToExport", "projectIsLocked", "<PERSON><PERSON><PERSON><PERSON>", "saveRecordsResponse", "failedValidations", "length", "hasFailures", "calendarViewFlag", "agentChatFlag", "layouts", "ɵɵpureFunction1", "_c5", "ActionableGridComponent_Conditional_3_Template_app_refine_records_hideSliderPanel_1_listener", "_r4", "onHideSliderPanel", "ɵɵadvance", "datastore", "id", "reportId", "dataStoreViewName", "refinerFields", "showRefinerPanel", "ActionableGridComponent_Conditional_4_Template_app_user_param_filter_userEnteredParamsSubmitted_0_listener", "_r5", "onUserEnteredParamsSubmitted", "userParameters", "ɵɵelement", "tilesConfigs", "chart_r6", "api", "ɵɵrepeaterCreate", "ActionableGridComponent_Conditional_7_For_4_Template", "ɵɵrepeaterTrackByIdentity", "ɵɵrepeater", "chartConfigs", "ActionableGridComponent_Conditional_10_Conditional_3_Template_app_sb_full_calendar_eventClick_0_listener", "_r8", "ActionableGridComponent_Conditional_10_Conditional_3_Template_app_sb_full_calendar_dataUpdated_0_listener", "onCalendarDataUpdated", "calendarData", "calendarOptions", "ActionableGridComponent_Conditional_10_Template_ag_grid_angular_columnRowGroupChanged_1_listener", "_r7", "onColumnRowGroupChanged", "ActionableGridComponent_Conditional_10_Template_ag_grid_angular_gridReady_1_listener", "onGridReady", "ActionableGridComponent_Conditional_10_Template_ag_grid_angular_cellFocused_1_listener", "onCellFocused", "ActionableGridComponent_Conditional_10_Template_ag_grid_angular_firstDataRendered_1_listener", "onFirstDataR<PERSON>ed", "ɵɵtemplate", "ActionableGridComponent_Conditional_10_Conditional_3_Template", "ɵɵstyleProp", "flashingCellColor", "reportData", "columnDefs", "gridOptions", "chartThemeOverrides", "contextMenuItems", "ɵɵpureFunction2", "_c6", "ɵɵconditional", "ɵɵtext", "ɵɵtextInterpolate1", "jsonSchemaParam", "title", "ɵɵtwoWayListener", "ActionableGridComponent_Conditional_13_Template_app_dynamic_form_body_dataChange_0_listener", "_r9", "ɵɵtwoWayBindingSet", "data", "ActionableGridComponent_Conditional_13_Template_app_dynamic_form_body_formOnSubmit_0_listener", "onDataEntryFormSubmit", "formAsset", "targetObj", "ɵɵtwoWayProperty", "projectId", "projectVersionId", "selectedRecordId", "userPermissionLevel", "ActionableGridComponent_Conditional_18_Template_app_iframe_dialog_visibleChange_0_listener", "_r10", "showConnectorScreen", "ActionableGridComponent_Conditional_18_Template_app_iframe_dialog_dialogClosed_0_listener", "connectorAppClose", "connectorIdFieldValue", "iframeUrl", "iframeTitle", "ActionableGridComponent_Conditional_20_Template_app_email_grid_displayChange_0_listener", "_r11", "onEmailDialogClose", "ActionableGridComponent_Conditional_22_Template_app_validation_results_dialog_visibleChange_0_listener", "_r12", "isShowSpinner", "ɵɵpipeBind1", "ɵɵsanitizeUrl", "ActionableGridComponent", "constructor", "activatedRoute", "router", "notificationService", "actionableGridService", "actionableGridColService", "resolutionService", "datastoresService", "userActivityService", "changeHistoryService", "projectsService", "featureFlagService", "actionableGridContextMenuService", "tilesService", "gridOptionsService", "aGGridService", "postSaveEventService", "confirmationDialogService", "userService", "conditionalEvaluationService", "formulaProcessorService", "cryptoService", "gridLayoutService", "filterService", "communicationService", "requiredColumns", "newRowIdPrefix", "newRowId", "sbiWrapperPad", "showUserParameter", "showAgentChatSidebar", "groupDefaultExpanded", "showDataEntryForm", "showEmailForm", "params", "getContextMenuItems", "gridCharts", "components", "agDateInput", "defaultColDef", "sortable", "filter", "resizable", "enableValue", "enableRowGroup", "enablePivot", "detailRowAutoHeight", "customChartThemes", "sbChartsTheme", "palette", "fills", "strokes", "pivotMode", "sideBar", "pivotPanelShow", "rowGroupPanelShow", "chartThemes", "getRowId", "String", "showActionableGrid", "disableFileUpload", "visualizationFlag", "gridChartsFlag", "connectorAppConfig", "Table", "setResolution", "event", "innerWidth", "innerHeight", "unloadNotification", "canDeactivate", "returnValue", "ngOnInit", "queryParams", "subscribe", "urlParams", "hideMenus", "addUserActivity", "url", "window", "location", "pathname", "loadActionableGrid", "_this", "_asyncToGenerator", "undefined", "unmodifiedRowData", "recordRefiners", "projectVersion", "getProjectVersion", "isLocked", "setFeatureFlags", "startsWith", "hideNavigationApp", "getAppUserPermission", "None", "loadReportInfo", "projectIdSubject$", "pId", "fileUploadProjectId", "versionIdSubject$", "vId", "fileUploadProjectVersionId", "datastoreNameSubject$", "dSn", "fileUploadDataStoreName", "ngAfterContentInit", "hideMenusForEmbed", "_this2", "featureFlags", "getFeatureFlags", "some", "x", "name", "Visualizations", "CalendarView", "AgentChat", "pivotModeFlag", "_this3", "getReportInfo", "next", "_ref", "showError", "slicerParams", "includeIdColumn", "View", "allowAddNewRow", "formatConfig", "tilesConfig", "push", "initChangeHistory", "actionableGridColumnsConfig", "actionableGridColumnConfig", "required", "slicerFilter", "setRowClassRules", "enablePivotMode", "fieldsConfig", "getFormlyFieldsConfigByGridConfig", "context", "parentComponent", "allowDeleteRow", "allowUploadFiles", "showEditorFunction", "editMode", "deleteRowFunction", "deleteRow", "getMainGridId", "getGridId", "getMasterRecordParams", "recordId", "baseSchema", "getRowNode", "enablePagination", "pagination", "paginateChildRows", "paginationAutoPageSize", "paginationAutoPaging", "paginationPageSize", "defaultPageSize", "paginationPageSizeSelector", "getDatastores", "formName", "_ref2", "datastores", "getColumnDefs", "setConditionalFormatting", "field", "map", "paramType", "UserEntered", "subscribeToDataReload", "loadLayouts", "_x2", "apply", "arguments", "buildBreadcrumb", "_x", "enableConditionalFormatting", "conditionalFormattingConfig", "setStyles", "profileProperties", "getRowStyleFunc", "getRowStyle", "setGridOption", "setCellStyles", "redrawRows", "find", "c", "conditions", "isProfileParameter", "getCompleteUserProfilePropertyList", "reloadData$", "clearData", "loadReportData", "_this4", "getLayouts", "error", "layoutHashId", "getGridHashId", "defaultLayout", "layout", "isCurrentLayout", "isDefault", "hasRecordRefiners", "hasUserParams", "_this5", "getUserAppGroupRoles", "appManifest", "showSpinner", "_this6", "validateUserEnteredParams", "refineRequest", "getRecords", "_ref3", "enableCalendarView", "checkViewHashId", "setCalendarOptions", "calendarViewConfig", "initialView", "_x3", "status", "message", "refresh<PERSON>ells", "force", "loadGridCharts", "groupedColumns", "columns", "item", "colId", "autoColumn", "getColumn", "colDef", "headerName", "join", "refreshHeader", "autoSizeColumn", "datastoreStructure", "getDatastoreStructure", "registerGridDataProvider", "grid", "setupDataChangeListener", "lockRowsActionSubscription", "lockRowsAction", "lock", "getAllModifiedRecords", "for<PERSON>ach", "modifiedRecord", "saveApplied", "errors", "getValidationErrors", "addInvokeWorkflowEvent", "saveRecords", "getBatchChnages", "recordIds", "getMasterRecordObjectIds", "size", "showWarning", "flashSavedRows", "resetPendingChanges", "showSuccess", "err", "action", "Delete", "DeleteArray", "requiredDisplayNames", "missingRequiredProperties", "properties", "changeDetail", "newValue", "requiredColumn", "rowData", "row", "modifiedProperty", "property", "column", "displayName", "displayNameCsv", "slice", "saveGridEvent", "gridEvents", "gridEvent", "eventType", "GridSave", "recordsSaveEvent", "reportName", "ActionableGrid", "assetEvent", "isSuccessful", "modifiedRecordNodes", "recordPath", "rowNode", "flashCells", "rowNodes", "flashDuration", "fadeDuration", "updateGridOptions", "undo", "getRowData", "sbFullCalendarComponent", "node", "updateEvents", "_this7", "items", "label", "routerLink", "updateBreadcrumb", "fromAppName", "currentAppName", "toAppName", "navigationAppName", "parameterFilters", "up", "paramSource", "parameterValue", "updateFilterParams", "hasModifiedRecords", "confirm", "_this8", "getSelectedNodes", "group", "handleUnsavedChanges", "onCancelAction", "showDialog", "header", "okButtonText", "cancelButtonText", "onCancel", "getFormAsset", "enableConditionalForms", "conditionalFormsConfig", "dynamicAssetInfo", "dynamicFormId", "excluded<PERSON><PERSON>es", "assetInfo", "type", "ConnectorApp", "connectorIdField", "idField", "DynamicForm", "<PERSON><PERSON><PERSON>", "Object", "CollectionForm", "CollectionGrid", "CollectionRepeat", "fc", "includes", "Update", "Create", "originalData", "getNewRowData", "fields", "getOwnPropertyNames", "selectedNode", "fieldValue", "fieldType", "fieldFormat", "format", "getColumnDef", "setDataValue", "trackGroupUpdate", "updateMetadata", "aliasIds", "refreshClientSideRowModel", "toString", "newRowData", "trackAdd", "applyTransaction", "add", "scrollToRowNode", "triggerGridDataChangeEvent", "onDataEntryFormHide", "dataEntryFormClosed", "reset", "onConfirm", "focusOnCellEditor", "rowIndex", "rowNodeAliasIds", "__sbmeta", "Attachments", "concat", "trackAttachments", "updateAttachments", "setData", "trackDelete", "remove", "view", "_this9", "colsToShow", "dataViews", "viewId", "columnsToShow", "newHashId", "hashSHA256", "sort", "viewHashId", "prevData", "console", "newData", "update", "config", "startDate", "getColumnConfig", "startDateCol", "endDate", "endDateCol", "resourceId", "resourceIdCol", "resourceTitle", "resourceTitleCol", "baseSchemaProps", "showTime", "DateTime", "startEditable", "allowUserUpdate", "startType", "Date", "endType", "durationEditable", "resourceEditable", "resourceTitleEditable", "columnName", "onHideAgentChat", "setTimeout", "agent<PERSON><PERSON>", "scrollToBottom", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "NotificationService", "i3", "ActionableGridService", "i4", "ActionableGridColService", "i5", "ResolutionService", "i6", "DatastoresService", "i7", "UserActivityService", "i8", "ChangeHistoryService", "i9", "ProjectsService", "i10", "FeatureFlagService", "i11", "ActionableGridContextMenuService", "i12", "TilesService", "i13", "GridOptionsService", "i14", "AGGridService", "i15", "PostSaveEventService", "i16", "ConfirmationDialogService", "i17", "UserService", "i18", "ConditionalEvaluationService", "i19", "i20", "CryptoService", "i21", "GridLayoutsService", "i22", "UserParamFilterService", "selectors", "viewQuery", "ActionableGridComponent_Query", "rf", "ctx", "ActionableGridComponent_resize_HostBindingHandler", "target", "ɵɵresolveWindow", "ActionableGridComponent_beforeunload_HostBindingHandler", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ActionableGridComponent_Template", "ActionableGridComponent_Conditional_2_Template", "ActionableGridComponent_Conditional_3_Template", "ActionableGridComponent_Conditional_4_Template", "ActionableGridComponent_Conditional_6_Template", "ActionableGridComponent_Conditional_7_Template", "ActionableGridComponent_Conditional_9_Template", "ActionableGridComponent_Conditional_10_Template", "ActionableGridComponent_Template_p_sidebar_visibleChange_11_listener", "_r1", "ActionableGridComponent_Template_p_sidebar_onHide_11_listener", "ActionableGridComponent_ng_template_12_Template", "ActionableGridComponent_Conditional_13_Template", "ActionableGridComponent_Template_p_sidebar_visibleChange_14_listener", "ActionableGridComponent_Template_p_sidebar_onHide_14_listener", "ActionableGridComponent_ng_template_15_Template", "ActionableGridComponent_Conditional_18_Template", "ActionableGridComponent_Template_app_data_entry_form_dialog_visibleChange_19_listener", "ActionableGridComponent_Template_app_data_entry_form_dialog_afterSave_19_listener", "ActionableGridComponent_Conditional_20_Template", "ActionableGridComponent_Conditional_22_Template", "ActionableGridComponent_Conditional_23_Template", "_c3", "ɵɵstyleMap", "ɵɵpureFunction0", "_c4", "i23", "Panel", "i24", "PrimeTemplate", "i25", "Accordion", "AccordionTab", "i26", "AgGridAngular", "i27", "Sidebar", "i28", "BlockUI", "styles"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\actionable-grid.component.ts", "C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\actionable-grid.component.html"], "sourcesContent": ["import { AfterContentInit, Component, HostListener, Inject, OnInit, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AgGridAngular, AgGridModule } from 'ag-grid-angular';\r\nimport { GridOptions, IRowNode, GetRowIdParams, RowClassParams, RowStyle } from 'ag-grid-community';\r\nimport { firstValueFrom, Subscription } from 'rxjs';\r\nimport { CRUDActions, DynamicAssetTypes, ModifierSourceType } from '../core/enums/shared';\r\nimport { CommunicationServiceType, CommunicationToken } from '../core/models/communication-service-type';\r\nimport { ReportInfo } from '../core/models/report-info';\r\nimport { ReportParameter } from '../core/models/report-parameter';\r\nimport { ActionableGridService } from './services/actionable-grid.service';\r\nimport { NotificationService } from '../core/services/notification.service';\r\nimport { ResolutionService } from '../core/services/resolution.service';\r\nimport { ActionableGridViewModes, ParameterTypes } from '../core/enums/actionable-grid';\r\nimport { JSONSchemaParam } from '../core/models/json-schema-param';\r\nimport { deepCopy } from '../shared/utilities/copy.functions';\r\nimport { CustomDateComponent } from '../core/custom-component/custom-date-component';\r\nimport { ICanComponentDeactivate } from '../core/interface/can-component-deactivate';\r\nimport { ActionableGridColumnConfig } from '../core/models/actionable-grid-column-config';\r\nimport { MenuItem, SharedModule } from 'primeng/api';\r\nimport { ProjectVersion } from '../core/models/project-version';\r\nimport { ProjectsService } from '../core/services/projects.service';\r\nimport { UserActivity } from '../shared/models/user-activity';\r\nimport { UserActivityService } from '../core/services/user-activity.service';\r\nimport { DatastoresService } from '../core/services/datastores.service';\r\nimport { Datastore } from '../shared/models/datastore';\r\nimport { RecordsSaveEvent } from '../shared/models/records-save-event';\r\nimport { AssetEventType } from '../shared/enums/asset-workflow';\r\nimport { getDefaultValue } from '../shared/utilities/datatype.functions';\r\nimport { TilesConfig } from '../visualizations/models/base/tiles-config';\r\nimport { ChartConfig } from './grid-charts/models/chart-config';\r\nimport { FeatureFlagService } from '../core/services/feature-flag.service';\r\nimport { Features } from '../shared/enums/features.enum';\r\nimport { ActionableGridContextMenuService } from './services/actionable-grid-context-menu.service';\r\nimport { TilesService } from '../visualizations/services/tiles.service';\r\nimport { IRowActionsParamsContext } from '../core/models/row-actions-params-context';\r\nimport { ChangeHistoryService } from './services/change-history.service';\r\nimport { GroupUpdateInfo } from './model/group-update-info';\r\nimport { GridOptionsService } from './services/grid-options-service';\r\nimport { PostSaveEventService } from '../core/services/post-save-events.service';\r\nimport { RecordRefiner } from './refine-records/models/record-refiner';\r\nimport { RefineRecordsRequest } from './refine-records/models/refine-records-request';\r\nimport { DB_PRIMARY_KEY } from '../shared/constants/record.const';\r\nimport { ConfirmationDialogService } from '../shared/confirmation-dialog/services/confirmation-dialog.service';\r\nimport { ConnectorAppConfig } from '../shared/models/connector-app-config';\r\nimport { SaveRecordsResponse } from '../core/models/save-records-response';\r\nimport { UserService } from '../core/services/user.service';\r\nimport { UserPermissionLevels } from '../core/enums/shared';\r\nimport { ConditionalEvaluationService } from './services/conditional-evaluation.service';\r\nimport { DynamicAssetInfo } from '../core/models/dynamic-asset-info';\r\nimport { FormlyRendererTypes } from '../sb-formly-renderer/enums/formly-renderer-types.enum';\r\nimport { AGGridService } from '../core/services/ag-grid.service';\r\nimport { GridFormulaProcessorService } from './services/grid-formula-processor.service';\r\nimport { ActionableGridColService } from './services/actionable-grid-col.service';\r\nimport { AssetUrlPipe } from '../shared/pipes/asset-url.pipe';\r\nimport { BlockUIModule } from 'primeng/blockui';\r\nimport { ValidationResultsDialogComponent } from '../business-validation-results/validation-results-dialog.component';\r\nimport { ConfirmationDialogComponent } from '../shared/confirmation-dialog/confirmation-dialog.component';\r\nimport { EmailGridComponent } from './email-grid/email-grid.component';\r\nimport { DataEntryFormDialogComponent } from '../dynamic-forms/data-entry-form-dialog/data-entry-form-dialog.component';\r\nimport { IframeDialogComponent } from '../shared/iframe-dialog/iframe-dialog.component';\r\nimport { DynamicFormBodyComponent } from '../dynamic-forms/dynamic-form-body/dynamic-form-body.component';\r\nimport { SidebarModule } from 'primeng/sidebar';\r\nimport { ChartsPanelComponent } from './grid-charts/charts-panel/charts-panel.component';\r\nimport { VisualizationPanelComponent } from '../visualizations/visualization-panel/visualization-panel.component';\r\nimport { AccordionModule } from 'primeng/accordion';\r\nimport { UserParamFilterComponent } from './user-param-filter/user-param-filter.component';\r\nimport { RefineRecordsComponent } from './refine-records/refine-records.component';\r\nimport { ActionsMenuComponent } from './actions-menu/actions-menu.component';\r\nimport { NgClass, NgStyle } from '@angular/common';\r\nimport { PanelModule } from 'primeng/panel';\r\nimport { SbFullCalendarComponent } from '../sb-full-calendar/sb-full-calendar.component';\r\nimport { CryptoService } from '../core/services/crypto.service';\r\nimport { EditorColumnType } from '../shared/enums/editor-column-type.enum';\r\nimport { SBCalendarOptions } from '../sb-full-calendar/models/sb-calendar-options';\r\nimport { AgentChatComponent } from '../utilities/agent-chat/agent-chat.component';\r\nimport { GridLayout } from './model/grid-layout';\r\nimport { GridLayoutsService } from './services/grid-layouts.service';\r\nimport { UserParamFilterService } from '../core/services/user-param-filter.service';\r\nimport { ProfileProperty } from '../core/models/profile-property';\r\n\r\n@Component({\r\n  selector: 'app-actionable-grid',\r\n  templateUrl: './actionable-grid.component.html',\r\n  styleUrls: ['./actionable-grid.component.scss'],\r\n  providers: [GridFormulaProcessorService],\r\n  standalone: true,\r\n  imports: [PanelModule, NgClass, ActionsMenuComponent, RefineRecordsComponent, UserParamFilterComponent, AccordionModule, NgStyle, VisualizationPanelComponent, ChartsPanelComponent, AgGridModule, SidebarModule, SharedModule, DynamicFormBodyComponent, IframeDialogComponent, DataEntryFormDialogComponent, EmailGridComponent, ConfirmationDialogComponent, ValidationResultsDialogComponent, BlockUIModule, AssetUrlPipe, SbFullCalendarComponent, AgentChatComponent]\r\n})\r\n\r\nexport class ActionableGridComponent implements OnInit, AfterContentInit, ICanComponentDeactivate {\r\n  @ViewChild('sbFullCalendar', { static: false, read: SbFullCalendarComponent }) sbFullCalendarComponent?: SbFullCalendarComponent;\r\n  @ViewChild('agGrid') agGrid!: AgGridAngular;\r\n  @ViewChild('chat') agentChat?: AgentChatComponent;\r\n\r\n  selectedRecordId = '';\r\n  reportInfo: ReportInfo;\r\n  reportData: any[];\r\n  calendarData: any[];\r\n  // keeping the original dataset to reset the data\r\n  unmodifiedRowData: any[];\r\n  reportId: string;\r\n  projectId: number;\r\n  projectVersionId: number;\r\n  projectIsLocked = false;\r\n  requiredColumns: ActionableGridColumnConfig[] = [];\r\n  refinerFields: ActionableGridColumnConfig[] = [];\r\n\r\n  newRowIdPrefix = '__';\r\n  newRowId = 0;\r\n  isPreview = false;\r\n  sbiWrapperPad = 'sbi-view-pad';\r\n  showUserParameter = false;\r\n  showAgentChatSidebar = false;\r\n\r\n  // This is used to lock updated rows when the save is taking place.\r\n  lockRowsActionSubscription: Subscription;\r\n\r\n  // This is to set color of flashing cell after save.\r\n  flashingCellColor: string;\r\n\r\n  // This is to expand all row groups by default.\r\n  groupDefaultExpanded = -1;\r\n  jsonSchemaParam = new JSONSchemaParam();\r\n  showDataEntryForm = false;\r\n  showFormlyDataEntryDialog = false;\r\n  showConnectorScreen = false;\r\n  showRefinerPanel = false;\r\n  showValidationResults = false;\r\n  saveRecordsResponse: SaveRecordsResponse;\r\n\r\n  showEmailForm = false;\r\n  columnsToExport: string[] = [];\r\n  datastore: Datastore;\r\n  uniqueFormName: string;\r\n  layouts: GridLayout[];\r\n\r\n  contextMenuItems = (params) => this.actionableGridContextMenuService.getContextMenuItems(params, this.agGrid.api);\r\n\r\n  gridCharts: any[] = [];\r\n\r\n  public gridOptions: GridOptions = {\r\n    components: {\r\n      agDateInput: CustomDateComponent,\r\n    },\r\n    defaultColDef: {\r\n      // enable sorting across all columns - including Row Group Columns\r\n      sortable: true,\r\n      filter: 'agMultiColumnFilter',\r\n      resizable: true,\r\n      enableValue: true,\r\n      enableRowGroup: true,\r\n      enablePivot: true,\r\n    },\r\n    detailRowAutoHeight: true,\r\n    customChartThemes: {\r\n      sbChartsTheme: {\r\n        palette: {\r\n          fills: ['#007BB5', '#c35467', '#6197c4', '#d78a92', '#c3d2e2', '#e6bdc0'],\r\n          strokes: ['#ccc'],\r\n        }\r\n      },\r\n    },\r\n    pivotMode: false,\r\n    sideBar: \"columns\",\r\n    pivotPanelShow: \"always\",\r\n    rowGroupPanelShow: 'always',\r\n    chartThemes: ['sbChartsTheme', 'ag-material'],\r\n    getRowId: (params: GetRowIdParams) => { return String(params.data[DB_PRIMARY_KEY]) },\r\n  };\r\n\r\n  // for user entered parameters\r\n  userParameters: ReportParameter[] = [];\r\n  slicerParams: ReportParameter[];\r\n  showActionableGrid = false;\r\n\r\n  isShowSpinner = false;\r\n\r\n  fileUploadProjectId: string;\r\n  fileUploadProjectVersionId: string;\r\n  fileUploadDataStoreName: string;\r\n\r\n  projectVersion: ProjectVersion;\r\n\r\n  disableFileUpload = false;\r\n  visualizationFlag = false;\r\n  gridChartsFlag = false;\r\n\r\n  tilesConfigs: TilesConfig[] = [];\r\n  chartConfigs: ChartConfig[] = [];\r\n  recordRefiners: RecordRefiner[];\r\n\r\n  connectorAppConfig = new ConnectorAppConfig();\r\n  userPermissionLevel: UserPermissionLevels;\r\n  formAsset: { assetInfo: DynamicAssetInfo, targetObj: any };\r\n\r\n  selectedView: ActionableGridViewModes = ActionableGridViewModes.Table;  // Default to Table view\r\n  calendarViewFlag: boolean;\r\n  agentChatFlag: boolean;\r\n  pivotModeFlag: boolean;\r\n  calendarOptions: SBCalendarOptions;\r\n\r\n  constructor(\r\n    private activatedRoute: ActivatedRoute,\r\n    private router: Router,\r\n    private notificationService: NotificationService,\r\n    private actionableGridService: ActionableGridService,\r\n    private actionableGridColService: ActionableGridColService,\r\n    private resolutionService: ResolutionService,\r\n    private datastoresService: DatastoresService,\r\n    private userActivityService: UserActivityService,\r\n    private changeHistoryService: ChangeHistoryService,\r\n    private projectsService: ProjectsService,\r\n    private featureFlagService: FeatureFlagService,\r\n    private actionableGridContextMenuService: ActionableGridContextMenuService,\r\n    private tilesService: TilesService,\r\n    private gridOptionsService: GridOptionsService,\r\n    private aGGridService: AGGridService,\r\n    private postSaveEventService: PostSaveEventService,\r\n    private confirmationDialogService: ConfirmationDialogService,\r\n    private userService: UserService,\r\n    private conditionalEvaluationService: ConditionalEvaluationService,\r\n    private formulaProcessorService: GridFormulaProcessorService,\r\n    private cryptoService: CryptoService,\r\n    private gridLayoutService: GridLayoutsService,\r\n    private filterService: UserParamFilterService,\r\n    @Inject(CommunicationToken) private communicationService: CommunicationServiceType) {\r\n  }\r\n\r\n  @HostListener('window:resize', ['$event.target'])\r\n  setResolution(event): void {\r\n    this.resolutionService.setResolution(event.innerWidth, event.innerHeight);\r\n  }\r\n\r\n  @HostListener('window:beforeunload', ['$event'])\r\n  unloadNotification($event: any) {\r\n    if (!this.canDeactivate()) {\r\n      $event.returnValue = true;\r\n    }\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    if (this.activatedRoute) {\r\n      this.activatedRoute.queryParams.subscribe(urlParams => {\r\n        // check if the route variable is coming from Reporting iFrame request (url variable: hideMenu)\r\n        this.isPreview = urlParams.hideMenus;\r\n      });\r\n\r\n      this.activatedRoute.params.subscribe(params => {\r\n        this.reportId = params.reportId;\r\n        this.projectId = params.projectId;\r\n        this.projectVersionId = params.projectVersionId;\r\n\r\n        // logging user activity\r\n        if (!this.isPreview) {\r\n          this.userActivityService.addUserActivity(\r\n            new UserActivity({ projectId: this.projectId, projectVersionId: this.projectVersionId, url: window.location.pathname, reportId: this.reportId }));\r\n        }\r\n\r\n        this.loadActionableGrid();\r\n      });\r\n    }\r\n  }\r\n\r\n  async loadActionableGrid() {\r\n    // RESET THE DEFAULTS WHEN THE COMPONENT RELOADS\r\n    this.tilesConfigs = [];\r\n    this.chartConfigs = [];\r\n    this.showUserParameter = false;\r\n    this.showActionableGrid = false;\r\n    this.reportInfo = undefined;\r\n    this.reportData = [];\r\n    this.unmodifiedRowData = [];\r\n    this.reportData = [];\r\n    this.datastore = undefined;\r\n    this.recordRefiners = [];\r\n    this.calendarOptions = undefined;\r\n    this.saveRecordsResponse = undefined;\r\n    this.selectedView = ActionableGridViewModes.Table;  // Default to Table view\r\n\r\n    this.isShowSpinner = true;\r\n\r\n    this.projectVersion = await this.getProjectVersion();\r\n    if (this.projectVersion) {\r\n      this.projectIsLocked = this.projectVersion.isLocked;\r\n    }\r\n\r\n    await this.setFeatureFlags();\r\n\r\n    if (this.router.url.startsWith('/saltboxdataapp/embeddedview')) {\r\n      this.isPreview = true;\r\n      this.hideNavigationApp();\r\n      this.sbiWrapperPad = 'sbi-view-pad-none';\r\n    }\r\n\r\n    if (!this.isPreview) {\r\n      if (this.router.url.startsWith('/saltboxdataapp/app-view')) {\r\n        await this.getAppUserPermission();\r\n\r\n        if (this.userPermissionLevel === UserPermissionLevels.None) {\r\n          return;\r\n        }\r\n      }\r\n    }\r\n\r\n    if (this.reportId) {\r\n      // refactored out into its own method\r\n      this.loadReportInfo();\r\n    }\r\n\r\n    this.actionableGridService.projectIdSubject$.subscribe(pId => this.fileUploadProjectId = pId);\r\n    this.actionableGridService.versionIdSubject$.subscribe(vId => this.fileUploadProjectVersionId = vId);\r\n    this.actionableGridService.datastoreNameSubject$.subscribe(dSn => this.fileUploadDataStoreName = dSn);\r\n  }\r\n\r\n  // After checking the content and ensuring it's loaded - remove the menus\r\n  ngAfterContentInit() {\r\n    if (this.isPreview) {\r\n      this.hideMenusForEmbed();\r\n    }\r\n\r\n  }\r\n  // this should be the new way to do it.  will need to refactor the other feature flag calls\r\n  async setFeatureFlags(): Promise<void> {\r\n    const featureFlags = await this.featureFlagService.getFeatureFlags();\r\n\r\n    this.visualizationFlag = featureFlags.some(x => x.name === Features.Visualizations);\r\n    this.gridChartsFlag = featureFlags.some(x => x.name === Features.Visualizations);\r\n    this.calendarViewFlag = featureFlags.some(x => x.name === Features.CalendarView);\r\n    this.agentChatFlag = featureFlags.some(x => x.name === Features.AgentChat);\r\n    this.pivotModeFlag = true; // featureFlags.some(x => x.name === Features.PivotMode);\r\n  }\r\n\r\n  loadReportInfo() {\r\n    this.actionableGridService.getReportInfo(this.reportId).subscribe({\r\n      next: async (reportInfo: ReportInfo) => {\r\n        if (!reportInfo) {\r\n          this.notificationService.showError('Error', `No report found for Report ID ${this.reportId}`);\r\n          return;\r\n        }\r\n\r\n        // extracting paramList from reportInfo\r\n        this.reportInfo = reportInfo;\r\n        this.userParameters = reportInfo.params;\r\n        this.slicerParams = deepCopy(this.userParameters);\r\n        this.reportInfo.includeIdColumn = true;\r\n\r\n        if (this.userPermissionLevel === UserPermissionLevels.View) {\r\n          this.reportInfo.allowAddNewRow = false;\r\n        }\r\n\r\n        if (this.reportInfo.formatConfig?.tilesConfig) {\r\n          this.tilesConfigs.push(...this.reportInfo.formatConfig.tilesConfig);\r\n        }\r\n\r\n        this.changeHistoryService.initChangeHistory(this.reportInfo.projectId, this.reportInfo.projectVersionId);\r\n\r\n        this.requiredColumns = this.reportInfo?.formatConfig?.actionableGridColumnsConfig\r\n          ?.filter(actionableGridColumnConfig => actionableGridColumnConfig.required);\r\n\r\n        this.refinerFields = this.reportInfo?.formatConfig?.actionableGridColumnsConfig\r\n          ?.filter(actionableGridColumnConfig => actionableGridColumnConfig.slicerFilter);\r\n\r\n        this.gridOptionsService.setRowClassRules(this.gridOptions, this.requiredColumns);\r\n\r\n        // Enabling pivot mode\r\n        if (this.pivotModeFlag) this.gridOptionsService.enablePivotMode(this.gridOptions);\r\n\r\n        // setting the row actions context. it's only here because of the allowDeleteRow\r\n        const fieldsConfig = this.actionableGridColService.getFormlyFieldsConfigByGridConfig(this.reportInfo?.formatConfig?.actionableGridColumnsConfig);\r\n        this.gridOptions.context = {\r\n          parentComponent: this,\r\n          allowDeleteRow: this.reportInfo?.allowAddNewRow,\r\n          allowUploadFiles: true,\r\n          userPermissionLevel: this.userPermissionLevel,\r\n          showEditorFunction: (editMode) => this.showFormEditor(editMode),\r\n          deleteRowFunction: (data) => this.deleteRow(data),\r\n          getMainGridId: () => { return this.agGrid.api.getGridId() },\r\n          getMasterRecordParams: (recordId) => { return { baseSchema: this.datastore.baseSchema, data: this.agGrid?.api?.getRowNode(recordId)?.data, fieldsConfig } }\r\n        } as IRowActionsParamsContext;\r\n\r\n        if (this.reportInfo.enablePagination) {\r\n          this.gridOptions.pagination = true;\r\n          this.gridOptions.paginateChildRows = true;\r\n          this.gridOptions.paginationAutoPageSize = this.reportInfo.paginationAutoPaging;\r\n\r\n          if (!this.reportInfo.paginationAutoPaging) {\r\n            this.gridOptions.paginationPageSize = this.reportInfo.defaultPageSize;\r\n            this.gridOptions.paginationPageSizeSelector = [20, 50, 100];\r\n          }\r\n        }\r\n\r\n        // Loading datastore\r\n        this.datastoresService.getDatastores(\r\n          this.reportInfo.projectId,\r\n          this.reportInfo.projectVersionId,\r\n          this.reportInfo.formName).subscribe(\r\n            async (datastores: Datastore[]) => {\r\n              if (!datastores || datastores.length === 0) {\r\n                this.notificationService.showError('Error', `Datastore not found!`);\r\n                this.isShowSpinner = false;\r\n                return;\r\n              }\r\n\r\n              if (datastores.length > 1) {\r\n                this.notificationService.showError('Error', `Multiple datastore with the same name have been found!`);\r\n                this.isShowSpinner = false;\r\n                return;\r\n              }\r\n\r\n              this.datastore = datastores[0];\r\n\r\n              this.reportInfo.columnDefs = await this.actionableGridColService\r\n                .getColumnDefs(this.reportInfo.formatConfig?.actionableGridColumnsConfig,\r\n                  this.datastore.baseSchema,\r\n                  this.gridOptions,\r\n                  true,\r\n                  this.projectVersionId,\r\n                  this.reportInfo.allowAddNewRow,\r\n                  null,\r\n                  this.userPermissionLevel,\r\n                  true,\r\n                  this.pivotModeFlag);\r\n\r\n              this.setConditionalFormatting();\r\n\r\n              // setting the columns which has to be exported\r\n              this.columnsToExport = this.reportInfo?.columnDefs\r\n                ?.filter(x => x.field !== 'fileUpload' && x.field !== '_id')\r\n                .map(x => x.field);\r\n\r\n              this.showUserParameter = this.reportInfo?.params?.filter(x => x.paramType === ParameterTypes.UserEntered)?.length > 0;\r\n              this.subscribeToDataReload();\r\n\r\n              // Loading grid layouts\r\n              await this.loadLayouts();\r\n            });\r\n\r\n        this.buildBreadcrumb();\r\n      }\r\n    });\r\n  }\r\n\r\n  private setConditionalFormatting() {\r\n    if (!this.reportInfo.enableConditionalFormatting || !this.reportInfo?.conditionalFormattingConfig?.length)\r\n      return;\r\n\r\n    const setStyles = (profileProperties: ProfileProperty[]) => {\r\n\r\n      // Row Styles\r\n      const getRowStyleFunc: (params: RowClassParams) => RowStyle | undefined = (params) => {\r\n        return this.gridOptionsService.getRowStyle(params.data, this.reportInfo?.conditionalFormattingConfig, profileProperties);\r\n      };\r\n\r\n      this.agGrid?.api ? this.agGrid.api.setGridOption('getRowStyle', getRowStyleFunc) : this.gridOptions.getRowStyle = getRowStyleFunc;\r\n\r\n      // Cell Styles\r\n      this.gridOptionsService.setCellStyles(this.reportInfo.columnDefs, this.reportInfo.conditionalFormattingConfig, profileProperties);\r\n      if (this.agGrid?.api)\r\n        this.agGrid.api.setGridOption('columnDefs', this.reportInfo.columnDefs);\r\n\r\n      this.agGrid?.api?.redrawRows();\r\n    }\r\n\r\n    if (this.reportInfo?.conditionalFormattingConfig?.find(c => c.conditions?.some(c => c.isProfileParameter))) {\r\n      this.userService.getCompleteUserProfilePropertyList().subscribe({\r\n        next: (profileProperties: ProfileProperty[]) => {\r\n          setStyles(profileProperties);\r\n        }\r\n      });\r\n    } else {\r\n      setStyles([]);\r\n    }\r\n  }\r\n\r\n  private subscribeToDataReload() {\r\n    this.filterService.reloadData$.subscribe(params => {\r\n      if (params.clearData) {\r\n        this.saveRecordsResponse = undefined;\r\n\r\n        if (this.reportData?.length > 0) {\r\n          this.unmodifiedRowData = [];\r\n          this.reportData = [];\r\n        }\r\n\r\n        this.isShowSpinner = false;\r\n        return;\r\n      }\r\n\r\n      this.slicerParams = params.params;\r\n      this.showActionableGrid = true;\r\n      this.loadReportData(this.slicerParams, true);\r\n    });\r\n\r\n  }\r\n\r\n  private async loadLayouts() {\r\n\r\n    let layouts = [];\r\n    // If by any chance the grid layout service throws an error, we will catch it and continue without layouts\r\n    try {\r\n      layouts = await firstValueFrom(this.gridLayoutService.getLayouts(this.reportInfo.reportId));\r\n    } catch (error) {\r\n      layouts = [];\r\n    }\r\n\r\n    if (layouts.length > 0) {\r\n      const layoutHashId = await this.gridLayoutService.getGridHashId(this.reportInfo);\r\n      const defaultLayout = layouts.find(layout => layout.isCurrentLayout && layout.layoutHashId === layoutHashId)\r\n        ?? layouts.find(layout => layout.isDefault && layout.layoutHashId === layoutHashId);\r\n      this.layouts = layouts;\r\n\r\n      // If there are saved slicers or user parameters and all parameters are optional, layout manager will load the report\r\n      if (defaultLayout && (defaultLayout.hasRecordRefiners || defaultLayout.hasUserParams)) {\r\n        this.showActionableGrid = true;\r\n        return;\r\n      }\r\n    } else {\r\n      this.layouts = [];\r\n    }\r\n  }\r\n\r\n  private async getAppUserPermission() {\r\n    this.userPermissionLevel = UserPermissionLevels[await this.userService.getUserAppGroupRoles(this.projectVersionId, this.projectVersion.appManifest, this.reportId)];\r\n\r\n    if (this.userPermissionLevel === UserPermissionLevels.None) {\r\n      this.notificationService.showError('Access Denied', \"You do not have access to this grid. Please request access from your app admin.\");\r\n      this.isShowSpinner = false;\r\n    }\r\n  }\r\n\r\n  loadReportData(params: ReportParameter[], showSpinner = false): void {\r\n\r\n    if (this.filterService.validateUserEnteredParams(params, true) === false)\r\n      return;\r\n\r\n    if (showSpinner)\r\n      this.isShowSpinner = showSpinner;\r\n\r\n    // Resetting the profile properties so that the latest values are fetched from the db\r\n    const refineRequest = new RefineRecordsRequest(this.reportId, this.reportInfo.dataStoreViewName, true, params);\r\n\r\n    // Subscribe() would be enough to bind the grid, we are just setting the backup dataset here\r\n    this.datastoresService.getRecords(this.datastore.id, refineRequest).subscribe({\r\n      next: async (reportData: any[]) => {\r\n        if (reportData) {\r\n          this.reportData = reportData;\r\n          this.unmodifiedRowData = deepCopy(this.reportData);\r\n\r\n          // default to calendar view if calendar view is enabled\r\n          if (this.calendarViewFlag && this.reportInfo.enableCalendarView && !this.calendarData) {\r\n            await this.checkViewHashId();\r\n            this.setCalendarOptions();\r\n            this.switchView(this.reportInfo.calendarViewConfig?.initialView);\r\n          }\r\n\r\n          if (this.selectedView !== ActionableGridViewModes.Table)\r\n            this.calendarData = this.reportData;\r\n\r\n          // for refresh button so spinner timing is dependent on completion\r\n          this.isShowSpinner = false;\r\n        }\r\n      },\r\n      error: (error: any) => {\r\n        if (error.status === 404) {\r\n          this.reportData = [];\r\n          this.unmodifiedRowData = [];\r\n        }\r\n\r\n        this.notificationService.showError('Error', error.message);\r\n        this.isShowSpinner = false;\r\n      }\r\n    });\r\n\r\n    // checking if agGrid is initialized to prevent a console error\r\n    this.agGrid?.api?.refreshCells({ force: true });\r\n  }\r\n\r\n  onFirstDataRendered(params) {\r\n    this.loadGridCharts();\r\n  }\r\n\r\n  loadGridCharts() {\r\n    if (this.reportInfo.formatConfig?.chartConfigs) {\r\n      this.chartConfigs.push(...this.reportInfo.formatConfig.chartConfigs);\r\n    }\r\n  }\r\n\r\n  // If the embedded view is triggered we use the CommunicationService to remove the menus inside the iframe\r\n  hideMenusForEmbed() {\r\n    // Communication Service call to remove Menus\r\n    this.hideNavigationApp();\r\n    this.sbiWrapperPad = 'sbi-view-pad-none';\r\n  }\r\n\r\n  onColumnRowGroupChanged(event): void {\r\n    const groupedColumns = event.columns.map((item) => item.colId);\r\n    const autoColumn = event.api.getColumn('ag-Grid-AutoColumn'); // This is the 'Group' Column.\r\n\r\n    if (autoColumn !== null) {\r\n      autoColumn.colDef.headerName = `Group by ${groupedColumns.join(', ')}`;\r\n      event.api.refreshHeader();\r\n      event.api.autoSizeColumn(autoColumn);\r\n    }\r\n  }\r\n\r\n  onGridReady(params) {\r\n    const datastoreStructure = this.actionableGridService.getDatastoreStructure(this.reportInfo.formatConfig?.actionableGridColumnsConfig, this.datastore.baseSchema);\r\n    this.aGGridService.registerGridDataProvider(params.api.getGridId(), { grid: this.agGrid, datastoreStructure });\r\n    this.formulaProcessorService.setupDataChangeListener(this.agGrid);\r\n\r\n    this.lockRowsActionSubscription = this.datastoresService.lockRowsAction.subscribe(lock => {\r\n      if (lock) {\r\n        this.changeHistoryService.getAllModifiedRecords()\r\n          .forEach(modifiedRecord => modifiedRecord.saveApplied = lock);\r\n      }\r\n    });\r\n  }\r\n\r\n  saveChanges(): void {\r\n\r\n    if (!this.hasChanges()) {\r\n      return;\r\n    }\r\n\r\n    const errors = this.getValidationErrors();\r\n    if (errors?.length) {\r\n      this.notificationService.showError('Error', errors.join(',\\n'));\r\n      return;\r\n    }\r\n\r\n    this.isShowSpinner = true;\r\n    this.addInvokeWorkflowEvent();\r\n    this.datastoresService.saveRecords(this.changeHistoryService.getBatchChnages(true), this.datastore.id)\r\n      .subscribe({\r\n        next: (saveRecordsResponse: SaveRecordsResponse) => {\r\n          this.saveRecordsResponse = saveRecordsResponse;\r\n          this.isShowSpinner = false;\r\n          this.showValidationResults = this.saveRecordsResponse?.failedValidations?.length > 0;\r\n\r\n          if (!saveRecordsResponse.hasFailures) {\r\n            // warning if some records sent to update but they didn't\r\n            if (saveRecordsResponse.recordIds?.length != this.changeHistoryService.getBatchChnages()?.getMasterRecordObjectIds()?.size) {\r\n              this.notificationService.showWarning(\"Warning\", \"The record(s) you are trying to update do not exist or have already been updated. Only the existing record(s) were updated.\");\r\n            }\r\n\r\n            this.flashSavedRows(true);\r\n            this.changeHistoryService.resetPendingChanges();\r\n            this.notificationService.showSuccess('Success', 'Saved Successfully');\r\n\r\n            // note: if loadReportData happens fast user will not set the flash changes\r\n            this.loadReportData(this.slicerParams);\r\n          }\r\n        },\r\n        error: err => {\r\n          this.isShowSpinner = false;\r\n          this.flashSavedRows(false);\r\n          this.notificationService.showError('Error', err.error);\r\n        }\r\n      });\r\n  }\r\n\r\n  getValidationErrors() {\r\n    const errors: string[] = [];\r\n\r\n    if (!this.requiredColumns?.length) {\r\n      return errors;\r\n    }\r\n\r\n    this.changeHistoryService.getAllModifiedRecords()\r\n      .forEach(modifiedRecord => {\r\n        if (!modifiedRecord?.data\r\n          || modifiedRecord.data?.action === CRUDActions.Delete\r\n          || modifiedRecord.data?.action === CRUDActions.DeleteArray)\r\n          return;\r\n\r\n        const requiredDisplayNames: string[] = [];\r\n        const missingRequiredProperties = modifiedRecord.data?.properties\r\n          ?.filter(changeDetail => changeDetail.newValue == null || changeDetail.newValue === '');\r\n        this.requiredColumns.forEach(requiredColumn => {\r\n          const rowData = this.reportData.find(row => row[DB_PRIMARY_KEY] === modifiedRecord.id);\r\n          if (!rowData) {\r\n            return;\r\n          }\r\n\r\n          const modifiedProperty = missingRequiredProperties.find(changeDetail => changeDetail.property === requiredColumn.column);\r\n\r\n          if (modifiedProperty || rowData[requiredColumn.column] == null || rowData[requiredColumn.column] === '') {\r\n            requiredDisplayNames.push(requiredColumn.displayName || requiredColumn.column);\r\n          }\r\n        });\r\n\r\n        if (requiredDisplayNames?.length) {\r\n          if (requiredDisplayNames.length === 1) {\r\n            errors.push(`${requiredDisplayNames[0]} is a mandatory field and must be provided`);\r\n          } else {\r\n            const displayNameCsv = requiredDisplayNames\r\n              .slice(0, requiredDisplayNames.length - 1)\r\n              .join(', ');\r\n            errors.push(`${displayNameCsv} & ${requiredDisplayNames[requiredDisplayNames.length - 1]} are mandatory fields and must be provided`);\r\n          }\r\n        }\r\n      });\r\n\r\n    return errors;\r\n  }\r\n\r\n  addInvokeWorkflowEvent() {\r\n    const saveGridEvent = this.reportInfo.gridEvents?.find(gridEvent => gridEvent.eventType === AssetEventType.GridSave);\r\n    if (!saveGridEvent) {\r\n      return;\r\n    }\r\n\r\n    this.postSaveEventService.addInvokeWorkflowEvent({\r\n      recordsSaveEvent: new RecordsSaveEvent(\r\n        this.reportInfo.reportId, this.reportInfo.reportName,\r\n        ModifierSourceType.ActionableGrid, this.datastore.name,\r\n        this.datastore.id),\r\n      assetEvent: saveGridEvent\r\n    });\r\n  }\r\n\r\n  flashSavedRows(isSuccessful: boolean) {\r\n    if (this.selectedView !== ActionableGridViewModes.Table) return;\r\n\r\n    this.flashingCellColor = isSuccessful ? 'rgb(106, 186, 69, .35)' : 'pink';\r\n    const modifiedRecordNodes: IRowNode[] = [];\r\n    for (const modifiedRecord of this.changeHistoryService.getAllModifiedRecords().filter(x => !x.recordPath)) {\r\n      if (modifiedRecord.data?.action === CRUDActions.Delete) { continue; }\r\n\r\n      if (modifiedRecord.saveApplied) {\r\n        const rowNode = this.agGrid.api.getRowNode(modifiedRecord.id);\r\n\r\n        if (rowNode)\r\n          modifiedRecordNodes.push(rowNode);\r\n      }\r\n    }\r\n\r\n    if (modifiedRecordNodes.length === 0) return;\r\n\r\n    this.agGrid.api.flashCells(\r\n      {\r\n        rowNodes: modifiedRecordNodes,\r\n        flashDuration: 5000,\r\n        fadeDuration: 2000\r\n      });\r\n  }\r\n\r\n  undoAll(): void {\r\n    // we need to make sure if this is the optimized way\r\n    if (this.unmodifiedRowData) {\r\n      this.reportData = deepCopy(this.unmodifiedRowData);\r\n      this.changeHistoryService.resetPendingChanges();\r\n      this.saveRecordsResponse = undefined;\r\n\r\n      this.agGrid.api.updateGridOptions({ rowData: this.reportData });\r\n      this.calendarData = this.reportData;\r\n    }\r\n  }\r\n\r\n  undoLastChange(): void {\r\n    // Note: reportData and ag grid row data is different, ag grid apply transactions doesn't apply on the actual data\r\n    const recordIds = this.changeHistoryService.undo(this.agGrid, this.aGGridService.getRowData(this.agGrid.api));\r\n\r\n    if (this.sbFullCalendarComponent) {\r\n      const data = [];\r\n      recordIds.forEach(id => {\r\n        const node = this.agGrid.api.getRowNode(id);\r\n        if (node) {\r\n          data.push(node.data);\r\n        }\r\n      });\r\n\r\n      this.sbFullCalendarComponent?.updateEvents(data);\r\n    }\r\n  }\r\n\r\n  getProjectVersion() {\r\n    return this.projectsService.getProjectVersion(`${this.projectVersionId}`);\r\n  }\r\n\r\n  async buildBreadcrumb() {\r\n    if (this.projectVersion) {\r\n      const items: MenuItem[] = [\r\n        { label: 'My Apps', routerLink: '/dashboard-v3' },\r\n        {\r\n          label: this.projectVersion.appManifest?.name,\r\n          routerLink: `/app-view/${this.reportInfo.projectId}/${this.reportInfo.projectVersionId}`\r\n        },\r\n        { label: this.reportInfo?.reportName }\r\n      ];\r\n      this.communicationService.updateBreadcrumb(items);\r\n    }\r\n  }\r\n\r\n  hideNavigationApp() {\r\n    this.communicationService.next({\r\n      fromAppName: this.communicationService.currentAppName,\r\n      toAppName: this.communicationService.navigationAppName,\r\n      eventType: 'HideNavigationBar',\r\n      data: {}\r\n    });\r\n  }\r\n\r\n  onUserEnteredParamsSubmitted(event: ReportParameter[]): void {\r\n    this.showActionableGrid = true;\r\n    this.userParameters = deepCopy(this.userParameters);\r\n    this.slicerParams = deepCopy(this.userParameters);\r\n\r\n    const parameterFilters = this.userParameters.map(up => { return { 'label': up.paramSource, 'value': up.parameterValue }; });\r\n    this.tilesService.updateFilterParams(parameterFilters);\r\n  }\r\n\r\n  onHideSliderPanel(event: boolean) {\r\n    this.showRefinerPanel = event;\r\n  }\r\n\r\n  canDeactivate() {\r\n    if (this.changeHistoryService.hasModifiedRecords()) {\r\n      return confirm('There are unsaved changes. Do you want to continue without saving?');\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  async showFormEditor(editMode: boolean, calendarData?) {\r\n    if (!calendarData && ((!editMode && !this.reportInfo?.allowAddNewRow)\r\n      || (editMode && (this.agGrid.api.getSelectedNodes().length < 1 || this.agGrid.api.getSelectedNodes()[0].group)))) {\r\n      return;\r\n    }\r\n\r\n    const handleUnsavedChanges = (onCancelAction) => {\r\n      if (this.changeHistoryService.hasChanges() || this.changeHistoryService.hasModifiedRecords()) {\r\n        this.confirmationDialogService.showDialog(\"actionable-grid\", {\r\n          header: 'Unsaved Changes Detected',\r\n          message: 'You have made changes that have not been saved. What would you like to do?',\r\n          okButtonText: 'Return to Editing',\r\n          cancelButtonText: 'Discard Changes',\r\n          onCancel: onCancelAction,\r\n        });\r\n        return true;\r\n      }\r\n      return false;\r\n    };\r\n\r\n    const data = calendarData ? calendarData : editMode ? this.agGrid.api.getSelectedNodes()[0].data : undefined;\r\n    this.formAsset = await this.conditionalEvaluationService.getFormAsset(\r\n      this.projectId, this.projectVersionId, data,\r\n      this.reportInfo.enableConditionalForms ? this.reportInfo.conditionalFormsConfig : undefined,\r\n      this.reportInfo?.formatConfig?.dynamicAssetInfo,\r\n      this.reportInfo.formatConfig?.dynamicFormId,\r\n      this.reportInfo.formatConfig.actionableGridColumnsConfig);\r\n\r\n    this.selectedRecordId = editMode ? data[DB_PRIMARY_KEY] : '';\r\n\r\n    let excludedRenderes = [];\r\n    switch (this.formAsset?.assetInfo?.type) {\r\n      case DynamicAssetTypes.ConnectorApp:\r\n        if (!(this.formAsset?.targetObj as ConnectorAppConfig))\r\n          return;\r\n\r\n        (this.formAsset.targetObj as ConnectorAppConfig).connectorIdField = this.formAsset.assetInfo.idField;\r\n        (this.formAsset.targetObj as ConnectorAppConfig).connectorIdFieldValue = data ? data[this.connectorAppConfig.connectorIdField] : undefined;\r\n\r\n        if (handleUnsavedChanges(() => {\r\n          this.undoAll();\r\n          this.showConnectorScreen = true;\r\n        })) return;\r\n\r\n        this.showConnectorScreen = true;\r\n        return;\r\n\r\n      case DynamicAssetTypes.DynamicForm:\r\n        if (handleUnsavedChanges(() => {\r\n          this.undoAll();\r\n          this.showFormlyDataEntryDialog = true;\r\n        })) return;\r\n\r\n        this.showFormlyDataEntryDialog = true;\r\n        break;\r\n      case DynamicAssetTypes.Default:\r\n        // filtering the arrays and objects ** Note: type of target object is DynamicForm\r\n        excludedRenderes = [FormlyRendererTypes.Object, FormlyRendererTypes.CollectionForm, FormlyRendererTypes.CollectionGrid, FormlyRendererTypes.CollectionRepeat];\r\n        this.formAsset.targetObj.fieldsConfig = this.formAsset.targetObj.fieldsConfig.filter(fc => !excludedRenderes.includes(fc.type as FormlyRendererTypes));\r\n\r\n        this.jsonSchemaParam.action = editMode ? CRUDActions.Update : CRUDActions.Create;\r\n        this.jsonSchemaParam.title = editMode ? 'Update Selected Record' : 'Add New Record';\r\n        this.jsonSchemaParam.originalData = editMode ? data : await this.actionableGridService.getNewRowData(this.reportInfo.formatConfig?.actionableGridColumnsConfig);\r\n        this.jsonSchemaParam.data = deepCopy(this.jsonSchemaParam.originalData);\r\n        this.showDataEntryForm = true;\r\n        return;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  showRefiner() {\r\n    this.showRefinerPanel = !this.showRefinerPanel;\r\n  }\r\n\r\n  onDataEntryFormSubmit(event: any) {\r\n    const data = event.data;\r\n    if (!data) {\r\n      this.showDataEntryForm = false;\r\n      return;\r\n    }\r\n    this.jsonSchemaParam.data = data;\r\n    const fields = Object.getOwnPropertyNames(this.datastore.baseSchema.properties);\r\n\r\n    let id = undefined;\r\n    switch (this.jsonSchemaParam.action) {\r\n      case CRUDActions.Update:\r\n        {\r\n          const originalData = deepCopy(this.jsonSchemaParam.originalData);\r\n          id = originalData[DB_PRIMARY_KEY];\r\n          const selectedNode = this.agGrid.api.getRowNode(id);\r\n          if (selectedNode) {\r\n            fields.forEach(field => {\r\n              let fieldValue = data[field];\r\n\r\n              if (fieldValue != this.jsonSchemaParam.originalData[field]) {\r\n                const fieldType = this.datastore.baseSchema.properties[field].type;\r\n                const fieldFormat = this.datastore.baseSchema.properties[field].format;\r\n\r\n                fieldValue = fieldValue == null ? getDefaultValue(fieldType, fieldFormat) : fieldValue;\r\n\r\n                // If this field exists in the grid, we need to update the grid, if not, we need to update the data\r\n                if (this.agGrid.api.getColumnDef(field)) {\r\n                  selectedNode.setDataValue(field, fieldValue);\r\n                } else {\r\n                  selectedNode.data[field] = fieldValue;\r\n                }\r\n              }\r\n            });\r\n\r\n            // Update the data in the calendar\r\n            this.sbFullCalendarComponent?.updateEvents([selectedNode.data]);\r\n\r\n            this.changeHistoryService.trackGroupUpdate([new GroupUpdateInfo(id, originalData, selectedNode.data)]);\r\n            this.updateMetadata(selectedNode, event.aliasIds);\r\n            this.agGrid.api.refreshClientSideRowModel('group');\r\n          }\r\n        }\r\n        break;\r\n      case CRUDActions.Create:\r\n        {\r\n          id = this.newRowIdPrefix + this.newRowId.toString();\r\n          const newRowData = {};\r\n          newRowData[DB_PRIMARY_KEY] = id;\r\n\r\n          fields.forEach(field => {\r\n            const fieldType = this.datastore.baseSchema.properties[field].type;\r\n            const fieldFormat = this.datastore.baseSchema.properties[field].format;\r\n            const fieldValue = (data[field] !== undefined && data[field] !== null) ? data[field] : getDefaultValue(fieldType, fieldFormat);\r\n\r\n            // first we need to create the new rowdata, and we are doing this to cover undefined fields\r\n            newRowData[field] = fieldValue;\r\n          });\r\n\r\n          // Update the data in the calendar\r\n          this.sbFullCalendarComponent?.updateEvents([newRowData]);\r\n\r\n          this.changeHistoryService.trackAdd(id, newRowData);\r\n\r\n          this.reportData.push(newRowData);\r\n          this.agGrid.api.applyTransaction({ add: [newRowData] });\r\n          this.agGrid.api.refreshClientSideRowModel('group');\r\n\r\n          this.updateMetadata(this.agGrid.api.getRowNode(id), event.aliasIds);\r\n          this.aGGridService.scrollToRowNode(this.agGrid.api, id);\r\n\r\n          this.newRowId++;\r\n        }\r\n        break;\r\n    }\r\n\r\n    this.aGGridService.triggerGridDataChangeEvent(this.agGrid.api.getGridId(), id);\r\n    this.showDataEntryForm = false;\r\n  }\r\n\r\n  onDataEntryFormHide() {\r\n    this.actionableGridService.dataEntryFormClosed.next(this.jsonSchemaParam.originalData[DB_PRIMARY_KEY]);\r\n  }\r\n\r\n  // for refresh button\r\n  onRefreshGridData() {\r\n    if (!this.showActionableGrid)\r\n      return;\r\n\r\n    const reset = () => {\r\n      this.saveRecordsResponse = undefined;\r\n      this.userService.reset();\r\n      this.setConditionalFormatting();\r\n      this.loadReportData(this.slicerParams, true);\r\n    }\r\n\r\n    if (this.hasPendingChanges() || this.hasChanges()) {\r\n      this.confirmationDialogService.showDialog(\"actionable-grid\",\r\n        {\r\n          header: 'Confirm Refresh', message: 'Unsaved changes detected, do you want to refresh without saving?', okButtonText: 'Confirm',\r\n          onConfirm: () => {\r\n            this.changeHistoryService.resetPendingChanges();\r\n            reset();\r\n          }\r\n        })\r\n    }\r\n    else {\r\n      reset();\r\n    }\r\n  }\r\n\r\n  hasChanges() {\r\n    return this.changeHistoryService.hasModifiedRecords();\r\n  }\r\n\r\n  hasSlicer() {\r\n    return this.refinerFields?.length > 0;\r\n  }\r\n\r\n  hasPendingChanges() {\r\n    return this.changeHistoryService.hasChanges();\r\n  }\r\n\r\n  onCellFocused(event) {\r\n    this.actionableGridService.focusOnCellEditor(event?.column?.colId, event.rowIndex, this.agGrid.api);\r\n  }\r\n\r\n  updateMetadata(rowNode: IRowNode, aliasIds: string[]) {\r\n    if (aliasIds) {\r\n      const rowNodeAliasIds = rowNode.data?.__sbmeta?.Attachments;\r\n      if (!rowNodeAliasIds ||\r\n        rowNodeAliasIds.filter(x => !aliasIds.includes(x)).concat(aliasIds.filter(x => !rowNodeAliasIds.includes(x))).length !== 0) {\r\n        this.changeHistoryService.trackAttachments(rowNode.id, aliasIds);\r\n        rowNode.data.__sbmeta = this.actionableGridService.updateAttachments(rowNode.data.__sbmeta, aliasIds);\r\n        rowNode.setData(rowNode.data);\r\n      }\r\n    }\r\n  }\r\n\r\n  onEmailDialogClose(event: boolean) {\r\n    this.showEmailForm = event;\r\n  }\r\n\r\n  onSendEmail() {\r\n    this.showEmailForm = true;\r\n  }\r\n\r\n  deleteRow(data: any): void {\r\n    const id = data[DB_PRIMARY_KEY];\r\n    const rowNode = this.agGrid.api.getRowNode(id);\r\n    this.changeHistoryService.trackDelete(id, data, rowNode.rowIndex);\r\n    this.agGrid.api.applyTransaction({ remove: [data] });\r\n  }\r\n\r\n  connectorAppClose() {\r\n    this.loadReportData(this.slicerParams, true);\r\n  }\r\n\r\n  switchView(view: ActionableGridViewModes) {\r\n    if (this.selectedView === ActionableGridViewModes.Table && view !== ActionableGridViewModes.Table)\r\n      this.calendarData = this.agGrid?.api ? this.aGGridService.getRowData(this.agGrid.api) : this.reportData;\r\n\r\n    this.selectedView = view;\r\n  }\r\n\r\n  async checkViewHashId() {\r\n    const colsToShow = this.reportInfo.dataStoreViewName ? this.datastore.dataViews.find(view => view.viewId === this.reportInfo.dataStoreViewName)?.columnsToShow : 'none';\r\n\r\n    const newHashId = colsToShow === 'none' ? 'none' : await this.cryptoService.hashSHA256(colsToShow?.sort()?.toString());\r\n    if (this.reportInfo.calendarViewConfig.viewHashId === newHashId) {\r\n      return;\r\n    }\r\n\r\n    this.notificationService.showWarning('The DataView associated with this grid has been editted. Errors may be present with Calendar and Grid views. Please contact your administrator if any errors are encountered.');\r\n  }\r\n\r\n  onCalendarDataUpdated(params: { prevData: any, newData: any }) {\r\n    if (!params.prevData || !params.prevData[DB_PRIMARY_KEY] || !this.agGrid.api.getRowNode(params.prevData[DB_PRIMARY_KEY])) {\r\n      this.notificationService.showError('Error', 'Record not found! Please refresh the data and try again.');\r\n      console.error(`Record with the given ID:${params.prevData ? params.prevData[DB_PRIMARY_KEY] : 'undefined'} not found to update!`);\r\n      return;\r\n    }\r\n\r\n    this.changeHistoryService.trackGroupUpdate([new GroupUpdateInfo(params.prevData[DB_PRIMARY_KEY], params.prevData, params.newData)]);\r\n    this.agGrid.api.applyTransaction({ update: [params.newData] });\r\n    this.agGrid.api.refreshClientSideRowModel('group');\r\n  }\r\n\r\n  setCalendarOptions() {\r\n    const config = this.reportInfo?.calendarViewConfig;\r\n    if (!config) return;\r\n\r\n    const columns = {\r\n      startDate: this.getColumnConfig(config.startDateCol),\r\n      endDate: this.getColumnConfig(config.endDateCol),\r\n      resourceId: this.getColumnConfig(config.resourceIdCol),\r\n      resourceTitle: this.getColumnConfig(config.resourceTitleCol)\r\n    };\r\n\r\n    this.calendarOptions = new SBCalendarOptions({\r\n      baseSchemaProps: this.datastore.baseSchema.properties,\r\n      calendarViewConfig: config,\r\n      isPreview: this.isPreview,\r\n      showTime: columns.startDate?.format?.type === EditorColumnType.DateTime && columns.endDate?.format?.type === EditorColumnType.DateTime,\r\n      startEditable: columns.startDate?.allowUserUpdate ?? false,\r\n      startType: [EditorColumnType.DateTime, EditorColumnType.Date].includes(columns.startDate?.format?.type) ? columns.startDate?.format?.type : undefined, // If the type is something like link, we will follow the datastore\r\n      endType: [EditorColumnType.DateTime, EditorColumnType.Date].includes(columns.endDate?.format?.type) ? columns.endDate?.format?.type : undefined, // If the type is something like link, we will follow the datastore\r\n      durationEditable: (config.endDateCol && columns.endDate?.allowUserUpdate) ?? false,\r\n      resourceEditable: (config.resourceIdCol && columns.resourceId?.allowUserUpdate) ?? false,\r\n      resourceTitleEditable: (config.resourceTitleCol && columns.resourceTitle?.allowUserUpdate) ?? false,\r\n    });\r\n  }\r\n\r\n  private getColumnConfig(columnName: string): any {\r\n    return this.reportInfo?.formatConfig?.actionableGridColumnsConfig?.find(x => x.column === columnName);\r\n  }\r\n\r\n  onHideAgentChat() {\r\n    this.showAgentChatSidebar = false;\r\n    this.onRefreshGridData();\r\n  }\r\n  onShowAgentChat() {\r\n    this.showAgentChatSidebar = true;\r\n    setTimeout(() => {\r\n      this.agentChat.scrollToBottom();\r\n    }, 0);\r\n  }\r\n}\r\n", "<p-panel class=\"actionable-grid-d-container\" [showHeader]=\"false\" [ngClass]=\"sbiWrapperPad\" styleClass=\"py-0 mb-0\">\r\n  <div class=\"sbi-actionable-grid-display\">\r\n    @if (this.reportInfo) {\r\n    <app-actions-menu [previewMode]=\"isPreview\" [reportInfo]=\"reportInfo\"\r\n      [checkHasPendingChanges]=\"hasPendingChanges() && !showFormlyDataEntryDialog\"\r\n      [checkHasChanges]=\"hasChanges() && !showFormlyDataEntryDialog\" [checkHasSlicers]=\"hasSlicer()\"\r\n      (showFormEditor)=\"showFormEditor($event)\" (undoLastChange)=\"undoLastChange()\" (undoAll)=\"undoAll()\"\r\n      (saveChanges)=\"saveChanges()\" (refreshGridData)=\"onRefreshGridData()\" (sendEmailClick)=\"onSendEmail()\"\r\n      (showRefiner)=\"showRefiner()\" (showValidationResults)=\"showValidationResults = true\" [agGrid]=\"agGrid\"\r\n      [columnsToExport]=\"columnsToExport\" [disabled]=\"projectIsLocked\" [selectedView]=\"selectedView\"\r\n      (changeSelectedView)=\"switchView($event)\"\r\n      [validationResultsCount]=\"saveRecordsResponse?.failedValidations?.length > 9 ? '9+' : saveRecordsResponse?.failedValidations?.length\"\r\n      [hasFailures]=\"saveRecordsResponse?.hasFailures\" [calendarViewFlag]=\"calendarViewFlag\"\r\n      (showAgentChat)=\"onShowAgentChat()\" [agentChatFlag]=\"agentChatFlag\" [layouts]=\"layouts\"\r\n      [ngClass]=\"{'preview-actions-fix' : isPreview}\">\r\n    </app-actions-menu>\r\n    }\r\n    @if (datastore && reportInfo){\r\n    <div>\r\n      <app-refine-records [datastoreId]=\"datastore.id\" [reportId]=\"reportId\" [dataViewId]=\"reportInfo.dataStoreViewName\"\r\n        [refinerFields]=\"refinerFields\" [showRefinerPanel]=\"showRefinerPanel\" [datastore]=\"datastore\"\r\n        (hideSliderPanel)=\"onHideSliderPanel($event)\"></app-refine-records>\r\n    </div>\r\n    }\r\n\r\n    @if (this.showUserParameter) {\r\n    <app-user-param-filter [userParameters]=\"userParameters\" [datastore]=\"datastore\" [reportId]=\"reportInfo?.reportId\"\r\n      [dataViewId]=\"reportInfo?.dataStoreViewName\" (userEnteredParamsSubmitted)=\"onUserEnteredParamsSubmitted()\">\r\n    </app-user-param-filter>\r\n    }\r\n\r\n    <p-accordion [ngStyle]=\"{display: showActionableGrid ? 'block' : 'none'}\" multiple=\"true\" class=\"accordion-nb\">\r\n      @if (this.visualizationFlag && this.tilesConfigs?.length > 0) {\r\n      <p-accordionTab [selected]=\"true\" header=\"Visualizations\">\r\n        <app-visualization-panel [tilesConfigs]=\"tilesConfigs\"></app-visualization-panel>\r\n      </p-accordionTab>\r\n      }\r\n\r\n      @if (this.gridChartsFlag && this.chartConfigs?.length > 0) {\r\n      <p-accordionTab [selected]=\"true\" header=\"Chart\">\r\n        <div class=\"p-3\">\r\n          <div class=\"grid\">\r\n            @for (chart of chartConfigs; track chart) {\r\n            <app-charts-panel class=\"col-12 sm:col-6 flex-grow-1\" [chartConfig]=\"chart\" [gridApi]=\"agGrid.api\">\r\n            </app-charts-panel>\r\n            }\r\n          </div>\r\n        </div>\r\n      </p-accordionTab>\r\n      }\r\n    </p-accordion>\r\n\r\n    <div class=\"px-2\">\r\n      @if (this.projectIsLocked) {\r\n      <p-message severity=\"light\" styleClass=\"w-full max-w-full\" text=\"Project is Locked\"></p-message>\r\n      }\r\n    </div>\r\n    @if (!this.projectIsLocked && this.showActionableGrid) {\r\n    <div>\r\n      <ag-grid-angular #agGrid [rowData]=\"reportData\" [columnDefs]=\"reportInfo?.columnDefs\" [animateRows]=\"true\"\r\n        [style.--ag-value-change-value-highlight-background-color]=\"flashingCellColor\" [gridOptions]=\"gridOptions\"\r\n        (columnRowGroupChanged)=\"onColumnRowGroupChanged($event)\" (gridReady)=\"onGridReady($event)\"\r\n        [suppressRowClickSelection]=\"false\" class=\"ag-theme-material ag-display-size\"\r\n        [groupHideOpenParents]=\"true\" id=\"agGrid\"\r\n        [rowSelection]=\"'single'\" [rowHeight]=\"40\" [headerHeight]=\"40\" suppressMenuHide=\"true\"\r\n        (cellFocused)=\"onCellFocused($event)\" [enableCharts]=\"true\" [chartThemeOverrides]=\"chartThemeOverrides\"\r\n        [getContextMenuItems]=\"contextMenuItems\" (firstDataRendered)=\"onFirstDataRendered($event)\"\r\n        [ngClass]=\"{'hidden': selectedView !== 'Table', 'preview-height-fix' : isPreview}\">\r\n      </ag-grid-angular>\r\n      @if (this.selectedView !== 'Table' && this.calendarOptions) {\r\n      <app-sb-full-calendar #sbFullCalendar class=\"max-h-24rem\" [data]=\"calendarData\" [options]=\"calendarOptions\"\r\n        [selectedView]=\"selectedView\" (eventClick)=\"showFormEditor(true, $event)\"\r\n        (dataUpdated)=\"onCalendarDataUpdated($event)\"></app-sb-full-calendar>\r\n      }\r\n    </div>\r\n    }\r\n  </div>\r\n</p-panel>\r\n\r\n<p-sidebar [(visible)]=\"showDataEntryForm\" appendTo=\"body\" blockScroll=\"true\" position=\"right\"\r\n  (onHide)=\"onDataEntryFormHide()\" styleClass=\"sidebar-sm\">\r\n  <ng-template pTemplate=\"header\">\r\n    <h4><i class=\"sb sb-icon-form\"></i> {{jsonSchemaParam.title}}</h4>\r\n  </ng-template>\r\n  @if (this.showDataEntryForm) {\r\n  <app-dynamic-form-body [dynamicForm]=\"formAsset?.targetObj\" [(data)]=\"jsonSchemaParam.data\" [projectId]='projectId'\r\n    [projectVersionId]='projectVersionId' [datastore]=\"datastore\" [recordId]=\"selectedRecordId\"\r\n    (formOnSubmit)=\"onDataEntryFormSubmit($event)\" [showFormOptions]=\"false\" submitBtnIcon=\"pi pi-save\"\r\n    [userPermissionLevel]=\"userPermissionLevel\" [showSubmitButton]=\"true\">\r\n  </app-dynamic-form-body>\r\n  }\r\n\r\n</p-sidebar>\r\n\r\n\r\n<p-sidebar [(visible)]=\"showAgentChatSidebar\" appendTo=\"body\" blockScroll=\"true\" position=\"right\"\r\n  (onHide)=\"onHideAgentChat()\" styleClass=\"agent-sidebar min-w-min py-0\">\r\n  <ng-template pTemplate=\"header\">\r\n    <h4 class=\"m-0\"><i class=\"fa-regular fa-comment\"></i> Chat</h4>\r\n  </ng-template>\r\n  <app-chat [assetId]=\"reportId\" #chat></app-chat>\r\n</p-sidebar>\r\n\r\n\r\n@if (this.showConnectorScreen) {\r\n<app-iframe-dialog [idFieldValue]=\"this.formAsset?.targetObj?.connectorIdFieldValue\"\r\n  [src]=\"this.formAsset?.targetObj?.iframeUrl\" [title]=\"this.formAsset?.targetObj?.iframeTitle\"\r\n  [(visible)]=\"showConnectorScreen\" (dialogClosed)=\"connectorAppClose()\">\r\n</app-iframe-dialog>\r\n}\r\n\r\n<app-data-entry-form-dialog [(visible)]=\"showFormlyDataEntryDialog\" [style]=\"{width: '50rem'}\"\r\n  [dynamicForm]=\"formAsset?.targetObj\" [datastore]=\"datastore\" [recordId]=\"selectedRecordId\"\r\n  [projectId]=\"fileUploadProjectId\" [projectVersionId]=\"fileUploadProjectVersionId\"\r\n  [userPermissionLevel]=\"userPermissionLevel\"\r\n  (afterSave)=\"loadReportData(slicerParams, true)\"></app-data-entry-form-dialog>\r\n\r\n@if (this.showEmailForm) {\r\n<app-email-grid [reportInfo]=\"reportInfo\" [gridApi]=\"agGrid?.api\" [columnsToExport]=\"columnsToExport\"\r\n  (displayChange)=\"onEmailDialogClose($event)\"></app-email-grid>\r\n}\r\n\r\n<app-confirmation-dialog id=\"actionable-grid\"></app-confirmation-dialog>\r\n\r\n@if (this.showValidationResults) {\r\n<app-validation-results-dialog [saveRecordsResponse]=\"saveRecordsResponse\" [(visible)]=\"showValidationResults\"\r\n  [data]=\"reportData\" [datastore]=\"datastore\">\r\n</app-validation-results-dialog>\r\n}\r\n\r\n@if (this.isShowSpinner) {\r\n<p-blockUI [blocked]=\"isShowSpinner\">\r\n  <img class=\"ui-progress-spinner\" [src]=\"'layout/images/salt-box-loading.gif' | assetUrl\">\r\n</p-blockUI>\r\n}"], "mappings": ";AAEA,SAAwBA,YAAY,QAAQ,iBAAiB;AAE7D,SAASC,cAAc,QAAsB,MAAM;AACnD,SAASC,WAAW,EAAEC,iBAAiB,EAAEC,kBAAkB,QAAQ,sBAAsB;AACzF,SAAmCC,kBAAkB,QAAQ,2CAA2C;AAMxG,SAASC,uBAAuB,EAAEC,cAAc,QAAQ,+BAA+B;AACvF,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,QAAQ,QAAQ,oCAAoC;AAC7D,SAASC,mBAAmB,QAAQ,gDAAgD;AAGpF,SAAmBC,YAAY,QAAQ,aAAa;AAGpD,SAASC,YAAY,QAAQ,gCAAgC;AAI7D,SAASC,gBAAgB,QAAQ,qCAAqC;AACtE,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,eAAe,QAAQ,wCAAwC;AAIxE,SAASC,QAAQ,QAAQ,+BAA+B;AAKxD,SAASC,eAAe,QAAQ,2BAA2B;AAI3D,SAASC,oBAAoB,QAAQ,gDAAgD;AACrF,SAASC,cAAc,QAAQ,kCAAkC;AAEjE,SAASC,kBAAkB,QAAQ,uCAAuC;AAG1E,SAASC,oBAAoB,QAAQ,sBAAsB;AAG3D,SAASC,mBAAmB,QAAQ,wDAAwD;AAE5F,SAASC,2BAA2B,QAAQ,2CAA2C;AAEvF,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,gCAAgC,QAAQ,oEAAoE;AACrH,SAASC,2BAA2B,QAAQ,6DAA6D;AACzG,SAASC,kBAAkB,QAAQ,mCAAmC;AACtE,SAASC,4BAA4B,QAAQ,0EAA0E;AACvH,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,wBAAwB,QAAQ,gEAAgE;AACzG,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,oBAAoB,QAAQ,mDAAmD;AACxF,SAASC,2BAA2B,QAAQ,qEAAqE;AACjH,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,wBAAwB,QAAQ,iDAAiD;AAC1F,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,OAAO,EAAEC,OAAO,QAAQ,iBAAiB;AAClD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,uBAAuB,QAAQ,gDAAgD;AAExF,SAASC,gBAAgB,QAAQ,yCAAyC;AAC1E,SAASC,iBAAiB,QAAQ,gDAAgD;AAClF,SAASC,kBAAkB,QAAQ,8CAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICvE7EC,EAAA,CAAAC,cAAA,2BAWkD;IADhDD,EAPA,CAAAE,UAAA,4BAAAC,0FAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAkBF,MAAA,CAAAG,cAAA,CAAAN,MAAA,CAAsB;IAAA,EAAC,4BAAAO,0FAAA;MAAAX,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAmBF,MAAA,CAAAK,cAAA,EAAgB;IAAA,EAAC,qBAAAC,mFAAA;MAAAb,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAYF,MAAA,CAAAO,OAAA,EAAS;IAAA,EAAC,yBAAAC,uFAAA;MAAAf,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CACpFF,MAAA,CAAAS,WAAA,EAAa;IAAA,EAAC,6BAAAC,2FAAA;MAAAjB,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAoBF,MAAA,CAAAW,iBAAA,EAAmB;IAAA,EAAC,4BAAAC,0FAAA;MAAAnB,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAmBF,MAAA,CAAAa,WAAA,EAAa;IAAA,EAAC,yBAAAC,uFAAA;MAAArB,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CACvFF,MAAA,CAAAe,WAAA,EAAa;IAAA,EAAC,mCAAAC,iGAAA;MAAAvB,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAiB,qBAAA,GAAkD,IAAI;IAAA,EAAC,gCAAAC,8FAAArB,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAE9DF,MAAA,CAAAmB,UAAA,CAAAtB,MAAA,CAAkB;IAAA,EAAC,2BAAAuB,yFAAA;MAAA3B,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAGxBF,MAAA,CAAAqB,eAAA,EAAiB;IAAA,EAAC;IAErC5B,EAAA,CAAA6B,YAAA,EAAmB;;;;IADjB7B,EAXgB,CAAA8B,UAAA,gBAAAvB,MAAA,CAAAwB,SAAA,CAAyB,eAAAxB,MAAA,CAAAyB,UAAA,CAA0B,2BAAAzB,MAAA,CAAA0B,iBAAA,OAAA1B,MAAA,CAAA2B,yBAAA,CACS,oBAAA3B,MAAA,CAAA4B,UAAA,OAAA5B,MAAA,CAAA2B,yBAAA,CACd,oBAAA3B,MAAA,CAAA6B,SAAA,GAAgC,WAAA7B,MAAA,CAAA8B,MAAA,CAGQ,oBAAA9B,MAAA,CAAA+B,eAAA,CACnE,aAAA/B,MAAA,CAAAgC,eAAA,CAA6B,iBAAAhC,MAAA,CAAAiC,YAAA,CAA8B,4BAAAjC,MAAA,CAAAkC,mBAAA,kBAAAlC,MAAA,CAAAkC,mBAAA,CAAAC,iBAAA,kBAAAnC,MAAA,CAAAkC,mBAAA,CAAAC,iBAAA,CAAAC,MAAA,eAAApC,MAAA,CAAAkC,mBAAA,kBAAAlC,MAAA,CAAAkC,mBAAA,CAAAC,iBAAA,kBAAAnC,MAAA,CAAAkC,mBAAA,CAAAC,iBAAA,CAAAC,MAAA,CAEuC,gBAAApC,MAAA,CAAAkC,mBAAA,kBAAAlC,MAAA,CAAAkC,mBAAA,CAAAG,WAAA,CACrF,qBAAArC,MAAA,CAAAsC,gBAAA,CAAsC,kBAAAtC,MAAA,CAAAuC,aAAA,CACnB,YAAAvC,MAAA,CAAAwC,OAAA,CAAoB,YAAA/C,EAAA,CAAAgD,eAAA,KAAAC,GAAA,EAAA1C,MAAA,CAAAwB,SAAA,EACxC;;;;;;IAK/C/B,EADF,CAAAC,cAAA,UAAK,6BAG6C;IAA9CD,EAAA,CAAAE,UAAA,6BAAAgD,6FAAA9C,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAmBF,MAAA,CAAA6C,iBAAA,CAAAhD,MAAA,CAAyB;IAAA,EAAC;IACjDJ,EADkD,CAAA6B,YAAA,EAAqB,EACjE;;;;IAHgB7B,EAAA,CAAAqD,SAAA,EAA4B;IACwBrD,EADpD,CAAA8B,UAAA,gBAAAvB,MAAA,CAAA+C,SAAA,CAAAC,EAAA,CAA4B,aAAAhD,MAAA,CAAAiD,QAAA,CAAsB,eAAAjD,MAAA,CAAAyB,UAAA,CAAAyB,iBAAA,CAA4C,kBAAAlD,MAAA,CAAAmD,aAAA,CACjF,qBAAAnD,MAAA,CAAAoD,gBAAA,CAAsC,cAAApD,MAAA,CAAA+C,SAAA,CAAwB;;;;;;IAMjGtD,EAAA,CAAAC,cAAA,gCAC6G;IAA9DD,EAAA,CAAAE,UAAA,wCAAA0D,2GAAA;MAAA5D,EAAA,CAAAK,aAAA,CAAAwD,GAAA;MAAA,MAAAtD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAA8BF,MAAA,CAAAuD,4BAAA,EAA8B;IAAA,EAAC;IAC5G9D,EAAA,CAAA6B,YAAA,EAAwB;;;;IADtB7B,EADqB,CAAA8B,UAAA,mBAAAvB,MAAA,CAAAwD,cAAA,CAAiC,cAAAxD,MAAA,CAAA+C,SAAA,CAAwB,aAAA/C,MAAA,CAAAyB,UAAA,kBAAAzB,MAAA,CAAAyB,UAAA,CAAAwB,QAAA,CAAkC,eAAAjD,MAAA,CAAAyB,UAAA,kBAAAzB,MAAA,CAAAyB,UAAA,CAAAyB,iBAAA,CACpE;;;;;IAM5CzD,EAAA,CAAAC,cAAA,wBAA0D;IACxDD,EAAA,CAAAgE,SAAA,kCAAiF;IACnFhE,EAAA,CAAA6B,YAAA,EAAiB;;;;IAFD7B,EAAA,CAAA8B,UAAA,kBAAiB;IACN9B,EAAA,CAAAqD,SAAA,EAA6B;IAA7BrD,EAAA,CAAA8B,UAAA,iBAAAvB,MAAA,CAAA0D,YAAA,CAA6B;;;;;IASlDjE,EAAA,CAAAgE,SAAA,2BACmB;;;;;IADyDhE,EAAtB,CAAA8B,UAAA,gBAAAoC,QAAA,CAAqB,YAAA3D,MAAA,CAAA8B,MAAA,CAAA8B,GAAA,CAAuB;;;;;IAFpGnE,EAFJ,CAAAC,cAAA,wBAAiD,cAC9B,cACG;IAChBD,EAAA,CAAAoE,gBAAA,IAAAC,oDAAA,gCAAArE,EAAA,CAAAsE,yBAAA,CAGC;IAGPtE,EAFI,CAAA6B,YAAA,EAAM,EACF,EACS;;;;IATD7B,EAAA,CAAA8B,UAAA,kBAAiB;IAG3B9B,EAAA,CAAAqD,SAAA,GAGC;IAHDrD,EAAA,CAAAuE,UAAA,CAAAhE,MAAA,CAAAiE,YAAA,CAGC;;;;;IASPxE,EAAA,CAAAgE,SAAA,oBAAgG;;;;;;IAgBhGhE,EAAA,CAAAC,cAAA,kCAEgD;IAA9CD,EAD8B,CAAAE,UAAA,wBAAAuE,yGAAArE,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqE,GAAA;MAAA,MAAAnE,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAcF,MAAA,CAAAG,cAAA,CAAe,IAAI,EAAAN,MAAA,CAAS;IAAA,EAAC,yBAAAuE,0GAAAvE,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqE,GAAA;MAAA,MAAAnE,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAC1DF,MAAA,CAAAqE,qBAAA,CAAAxE,MAAA,CAA6B;IAAA,EAAC;IAACJ,EAAA,CAAA6B,YAAA,EAAuB;;;;IADrE7B,EADwD,CAAA8B,UAAA,SAAAvB,MAAA,CAAAsE,YAAA,CAAqB,YAAAtE,MAAA,CAAAuE,eAAA,CAA4B,iBAAAvE,MAAA,CAAAiC,YAAA,CAC5E;;;;;;IAZ/BxC,EADF,CAAAC,cAAA,UAAK,6BASkF;IAD1CD,EALzC,CAAAE,UAAA,mCAAA6E,iGAAA3E,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA2E,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAyBF,MAAA,CAAA0E,uBAAA,CAAA7E,MAAA,CAA+B;IAAA,EAAC,uBAAA8E,qFAAA9E,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA2E,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAcF,MAAA,CAAA4E,WAAA,CAAA/E,MAAA,CAAmB;IAAA,EAAC,yBAAAgF,uFAAAhF,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA2E,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAI5EF,MAAA,CAAA8E,aAAA,CAAAjF,MAAA,CAAqB;IAAA,EAAC,+BAAAkF,6FAAAlF,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA2E,GAAA;MAAA,MAAAzE,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CACyBF,MAAA,CAAAgF,mBAAA,CAAAnF,MAAA,CAA2B;IAAA,EAAC;IAE5FJ,EAAA,CAAA6B,YAAA,EAAkB;IAClB7B,EAAA,CAAAwF,UAAA,IAAAC,6DAAA,mCAA6D;IAK/DzF,EAAA,CAAA6B,YAAA,EAAM;;;;IAdF7B,EAAA,CAAAqD,SAAA,EAA8E;IAA9ErD,EAAA,CAAA0F,WAAA,uDAAAnF,MAAA,CAAAoF,iBAAA,CAA8E;IAO9E3F,EARuB,CAAA8B,UAAA,YAAAvB,MAAA,CAAAqF,UAAA,CAAsB,eAAArF,MAAA,CAAAyB,UAAA,kBAAAzB,MAAA,CAAAyB,UAAA,CAAA6D,UAAA,CAAsC,qBAAqB,gBAAAtF,MAAA,CAAAuF,WAAA,CACE,oCAEvE,8BACN,0BACJ,iBAAiB,oBAAoB,sBACH,wBAAAvF,MAAA,CAAAwF,mBAAA,CAA4C,wBAAAxF,MAAA,CAAAyF,gBAAA,CAC/D,YAAAhG,EAAA,CAAAiG,eAAA,KAAAC,GAAA,EAAA3F,MAAA,CAAAiC,YAAA,cAAAjC,MAAA,CAAAwB,SAAA,EAC0C;IAEpF/B,EAAA,CAAAqD,SAAA,GAIC;IAJDrD,EAAA,CAAAmG,aAAA,IAAA5F,MAAA,CAAAiC,YAAA,gBAAAjC,MAAA,CAAAuE,eAAA,UAIC;;;;;IASH9E,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAgE,SAAA,YAA+B;IAAChE,EAAA,CAAAoG,MAAA,GAAyB;IAAApG,EAAA,CAAA6B,YAAA,EAAK;;;;IAA9B7B,EAAA,CAAAqD,SAAA,GAAyB;IAAzBrD,EAAA,CAAAqG,kBAAA,MAAA9F,MAAA,CAAA+F,eAAA,CAAAC,KAAA,KAAyB;;;;;;IAG/DvG,EAAA,CAAAC,cAAA,gCAGwE;IAHZD,EAAA,CAAAwG,gBAAA,wBAAAC,4FAAArG,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqG,GAAA;MAAA,MAAAnG,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAA2G,kBAAA,CAAApG,MAAA,CAAA+F,eAAA,CAAAM,IAAA,EAAAxG,MAAA,MAAAG,MAAA,CAAA+F,eAAA,CAAAM,IAAA,GAAAxG,MAAA;MAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;IAAA,EAA+B;IAEzFJ,EAAA,CAAAE,UAAA,0BAAA2G,8FAAAzG,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAqG,GAAA;MAAA,MAAAnG,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAgBF,MAAA,CAAAuG,qBAAA,CAAA1G,MAAA,CAA6B;IAAA,EAAC;IAEhDJ,EAAA,CAAA6B,YAAA,EAAwB;;;;IAJD7B,EAAA,CAAA8B,UAAA,gBAAAvB,MAAA,CAAAwG,SAAA,kBAAAxG,MAAA,CAAAwG,SAAA,CAAAC,SAAA,CAAoC;IAAChH,EAAA,CAAAiH,gBAAA,SAAA1G,MAAA,CAAA+F,eAAA,CAAAM,IAAA,CAA+B;IAG7C5G,EAH8C,CAAA8B,UAAA,cAAAvB,MAAA,CAAA2G,SAAA,CAAuB,qBAAA3G,MAAA,CAAA4G,gBAAA,CAC5E,cAAA5G,MAAA,CAAA+C,SAAA,CAAwB,aAAA/C,MAAA,CAAA6G,gBAAA,CAA8B,0BACnB,wBAAA7G,MAAA,CAAA8G,mBAAA,CAC7B,0BAA0B;;;;;IAUrErH,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAgE,SAAA,YAAqC;IAAChE,EAAA,CAAAoG,MAAA,YAAI;IAAApG,EAAA,CAAA6B,YAAA,EAAK;;;;;;IAOnE7B,EAAA,CAAAC,cAAA,4BAEyE;IAAvED,EAAA,CAAAwG,gBAAA,2BAAAc,2FAAAlH,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAkH,IAAA;MAAA,MAAAhH,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAA2G,kBAAA,CAAApG,MAAA,CAAAiH,mBAAA,EAAApH,MAAA,MAAAG,MAAA,CAAAiH,mBAAA,GAAApH,MAAA;MAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;IAAA,EAAiC;IAACJ,EAAA,CAAAE,UAAA,0BAAAuH,0FAAA;MAAAzH,EAAA,CAAAK,aAAA,CAAAkH,IAAA;MAAA,MAAAhH,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAgBF,MAAA,CAAAmH,iBAAA,EAAmB;IAAA,EAAC;IACxE1H,EAAA,CAAA6B,YAAA,EAAoB;;;;IAF2B7B,EAD5B,CAAA8B,UAAA,iBAAAvB,MAAA,CAAAwG,SAAA,kBAAAxG,MAAA,CAAAwG,SAAA,CAAAC,SAAA,kBAAAzG,MAAA,CAAAwG,SAAA,CAAAC,SAAA,CAAAW,qBAAA,CAAiE,QAAApH,MAAA,CAAAwG,SAAA,kBAAAxG,MAAA,CAAAwG,SAAA,CAAAC,SAAA,kBAAAzG,MAAA,CAAAwG,SAAA,CAAAC,SAAA,CAAAY,SAAA,CACtC,UAAArH,MAAA,CAAAwG,SAAA,kBAAAxG,MAAA,CAAAwG,SAAA,CAAAC,SAAA,kBAAAzG,MAAA,CAAAwG,SAAA,CAAAC,SAAA,CAAAa,WAAA,CAAiD;IAC7F7H,EAAA,CAAAiH,gBAAA,YAAA1G,MAAA,CAAAiH,mBAAA,CAAiC;;;;;;IAWnCxH,EAAA,CAAAC,cAAA,yBAC+C;IAA7CD,EAAA,CAAAE,UAAA,2BAAA4H,wFAAA1H,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA0H,IAAA;MAAA,MAAAxH,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAiBF,MAAA,CAAAyH,kBAAA,CAAA5H,MAAA,CAA0B;IAAA,EAAC;IAACJ,EAAA,CAAA6B,YAAA,EAAiB;;;;IADE7B,EAAlD,CAAA8B,UAAA,eAAAvB,MAAA,CAAAyB,UAAA,CAAyB,YAAAzB,MAAA,CAAA8B,MAAA,kBAAA9B,MAAA,CAAA8B,MAAA,CAAA8B,GAAA,CAAwB,oBAAA5D,MAAA,CAAA+B,eAAA,CAAoC;;;;;;IAOrGtC,EAAA,CAAAC,cAAA,wCAC8C;IAD6BD,EAAA,CAAAwG,gBAAA,2BAAAyB,uGAAA7H,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAA6H,IAAA;MAAA,MAAA3H,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAAR,EAAA,CAAA2G,kBAAA,CAAApG,MAAA,CAAAiB,qBAAA,EAAApB,MAAA,MAAAG,MAAA,CAAAiB,qBAAA,GAAApB,MAAA;MAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;IAAA,EAAmC;IAE9GJ,EAAA,CAAA6B,YAAA,EAAgC;;;;IAFD7B,EAAA,CAAA8B,UAAA,wBAAAvB,MAAA,CAAAkC,mBAAA,CAA2C;IAACzC,EAAA,CAAAiH,gBAAA,YAAA1G,MAAA,CAAAiB,qBAAA,CAAmC;IACxFxB,EAApB,CAAA8B,UAAA,SAAAvB,MAAA,CAAAqF,UAAA,CAAmB,cAAArF,MAAA,CAAA+C,SAAA,CAAwB;;;;;IAK7CtD,EAAA,CAAAC,cAAA,oBAAqC;IACnCD,EAAA,CAAAgE,SAAA,cAAyF;;IAC3FhE,EAAA,CAAA6B,YAAA,EAAY;;;;IAFD7B,EAAA,CAAA8B,UAAA,YAAAvB,MAAA,CAAA4H,aAAA,CAAyB;IACDnI,EAAA,CAAAqD,SAAA,EAAuD;IAAvDrD,EAAA,CAAA8B,UAAA,QAAA9B,EAAA,CAAAoI,WAAA,8CAAApI,EAAA,CAAAqI,aAAA,CAAuD;;;AD3C1F,OAAM,MAAOC,uBAAuB;EAgHlCC,YACUC,cAA8B,EAC9BC,MAAc,EACdC,mBAAwC,EACxCC,qBAA4C,EAC5CC,wBAAkD,EAClDC,iBAAoC,EACpCC,iBAAoC,EACpCC,mBAAwC,EACxCC,oBAA0C,EAC1CC,eAAgC,EAChCC,kBAAsC,EACtCC,gCAAkE,EAClEC,YAA0B,EAC1BC,kBAAsC,EACtCC,aAA4B,EAC5BC,oBAA0C,EAC1CC,yBAAoD,EACpDC,WAAwB,EACxBC,4BAA0D,EAC1DC,uBAAoD,EACpDC,aAA4B,EAC5BC,iBAAqC,EACrCC,aAAqC,EACTC,oBAA8C;IAvB1E,KAAAvB,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,qBAAqB,GAArBA,qBAAqB;IACrB,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,gCAAgC,GAAhCA,gCAAgC;IAChC,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,4BAA4B,GAA5BA,4BAA4B;IAC5B,KAAAC,uBAAuB,GAAvBA,uBAAuB;IACvB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,aAAa,GAAbA,aAAa;IACe,KAAAC,oBAAoB,GAApBA,oBAAoB;IAnI1D,KAAA3C,gBAAgB,GAAG,EAAE;IASrB,KAAA7E,eAAe,GAAG,KAAK;IACvB,KAAAyH,eAAe,GAAiC,EAAE;IAClD,KAAAtG,aAAa,GAAiC,EAAE;IAEhD,KAAAuG,cAAc,GAAG,IAAI;IACrB,KAAAC,QAAQ,GAAG,CAAC;IACZ,KAAAnI,SAAS,GAAG,KAAK;IACjB,KAAAoI,aAAa,GAAG,cAAc;IAC9B,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,oBAAoB,GAAG,KAAK;IAQ5B;IACA,KAAAC,oBAAoB,GAAG,CAAC,CAAC;IACzB,KAAAhE,eAAe,GAAG,IAAI5I,eAAe,EAAE;IACvC,KAAA6M,iBAAiB,GAAG,KAAK;IACzB,KAAArI,yBAAyB,GAAG,KAAK;IACjC,KAAAsF,mBAAmB,GAAG,KAAK;IAC3B,KAAA7D,gBAAgB,GAAG,KAAK;IACxB,KAAAnC,qBAAqB,GAAG,KAAK;IAG7B,KAAAgJ,aAAa,GAAG,KAAK;IACrB,KAAAlI,eAAe,GAAa,EAAE;IAK9B,KAAA0D,gBAAgB,GAAIyE,MAAM,IAAK,IAAI,CAACtB,gCAAgC,CAACuB,mBAAmB,CAACD,MAAM,EAAE,IAAI,CAACpI,MAAM,CAAC8B,GAAG,CAAC;IAEjH,KAAAwG,UAAU,GAAU,EAAE;IAEf,KAAA7E,WAAW,GAAgB;MAChC8E,UAAU,EAAE;QACVC,WAAW,EAAEjN;OACd;MACDkN,aAAa,EAAE;QACb;QACAC,QAAQ,EAAE,IAAI;QACdC,MAAM,EAAE,qBAAqB;QAC7BC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,IAAI;QACjBC,cAAc,EAAE,IAAI;QACpBC,WAAW,EAAE;OACd;MACDC,mBAAmB,EAAE,IAAI;MACzBC,iBAAiB,EAAE;QACjBC,aAAa,EAAE;UACbC,OAAO,EAAE;YACPC,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;YACzEC,OAAO,EAAE,CAAC,MAAM;;;OAGrB;MACDC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE,SAAS;MAClBC,cAAc,EAAE,QAAQ;MACxBC,iBAAiB,EAAE,QAAQ;MAC3BC,WAAW,EAAE,CAAC,eAAe,EAAE,aAAa,CAAC;MAC7CC,QAAQ,EAAGvB,MAAsB,IAAI;QAAG,OAAOwB,MAAM,CAACxB,MAAM,CAAC7D,IAAI,CAACvI,cAAc,CAAC,CAAC;MAAC;KACpF;IAED;IACA,KAAA0F,cAAc,GAAsB,EAAE;IAEtC,KAAAmI,kBAAkB,GAAG,KAAK;IAE1B,KAAA/D,aAAa,GAAG,KAAK;IAQrB,KAAAgE,iBAAiB,GAAG,KAAK;IACzB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,cAAc,GAAG,KAAK;IAEtB,KAAApI,YAAY,GAAkB,EAAE;IAChC,KAAAO,YAAY,GAAkB,EAAE;IAGhC,KAAA8H,kBAAkB,GAAG,IAAIhO,kBAAkB,EAAE;IAI7C,KAAAkE,YAAY,GAA4BhF,uBAAuB,CAAC+O,KAAK,CAAC,CAAE;EA+BxE;EAGAC,aAAaA,CAACC,KAAK;IACjB,IAAI,CAAC5D,iBAAiB,CAAC2D,aAAa,CAACC,KAAK,CAACC,UAAU,EAAED,KAAK,CAACE,WAAW,CAAC;EAC3E;EAGAC,kBAAkBA,CAACxM,MAAW;IAC5B,IAAI,CAAC,IAAI,CAACyM,aAAa,EAAE,EAAE;MACzBzM,MAAM,CAAC0M,WAAW,GAAG,IAAI;IAC3B;EACF;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACvE,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACwE,WAAW,CAACC,SAAS,CAACC,SAAS,IAAG;QACpD;QACA,IAAI,CAACnL,SAAS,GAAGmL,SAAS,CAACC,SAAS;MACtC,CAAC,CAAC;MAEF,IAAI,CAAC3E,cAAc,CAACiC,MAAM,CAACwC,SAAS,CAACxC,MAAM,IAAG;QAC5C,IAAI,CAACjH,QAAQ,GAAGiH,MAAM,CAACjH,QAAQ;QAC/B,IAAI,CAAC0D,SAAS,GAAGuD,MAAM,CAACvD,SAAS;QACjC,IAAI,CAACC,gBAAgB,GAAGsD,MAAM,CAACtD,gBAAgB;QAE/C;QACA,IAAI,CAAC,IAAI,CAACpF,SAAS,EAAE;UACnB,IAAI,CAACgH,mBAAmB,CAACqE,eAAe,CACtC,IAAItP,YAAY,CAAC;YAAEoJ,SAAS,EAAE,IAAI,CAACA,SAAS;YAAEC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;YAAEkG,GAAG,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;YAAEhK,QAAQ,EAAE,IAAI,CAACA;UAAQ,CAAE,CAAC,CAAC;QACrJ;QAEA,IAAI,CAACiK,kBAAkB,EAAE;MAC3B,CAAC,CAAC;IACJ;EACF;EAEMA,kBAAkBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB;MACAD,KAAI,CAACzJ,YAAY,GAAG,EAAE;MACtByJ,KAAI,CAAClJ,YAAY,GAAG,EAAE;MACtBkJ,KAAI,CAACtD,iBAAiB,GAAG,KAAK;MAC9BsD,KAAI,CAACxB,kBAAkB,GAAG,KAAK;MAC/BwB,KAAI,CAAC1L,UAAU,GAAG4L,SAAS;MAC3BF,KAAI,CAAC9H,UAAU,GAAG,EAAE;MACpB8H,KAAI,CAACG,iBAAiB,GAAG,EAAE;MAC3BH,KAAI,CAAC9H,UAAU,GAAG,EAAE;MACpB8H,KAAI,CAACpK,SAAS,GAAGsK,SAAS;MAC1BF,KAAI,CAACI,cAAc,GAAG,EAAE;MACxBJ,KAAI,CAAC5I,eAAe,GAAG8I,SAAS;MAChCF,KAAI,CAACjL,mBAAmB,GAAGmL,SAAS;MACpCF,KAAI,CAAClL,YAAY,GAAGhF,uBAAuB,CAAC+O,KAAK,CAAC,CAAE;MAEpDmB,KAAI,CAACvF,aAAa,GAAG,IAAI;MAEzBuF,KAAI,CAACK,cAAc,SAASL,KAAI,CAACM,iBAAiB,EAAE;MACpD,IAAIN,KAAI,CAACK,cAAc,EAAE;QACvBL,KAAI,CAACnL,eAAe,GAAGmL,KAAI,CAACK,cAAc,CAACE,QAAQ;MACrD;MAEA,MAAMP,KAAI,CAACQ,eAAe,EAAE;MAE5B,IAAIR,KAAI,CAACjF,MAAM,CAAC4E,GAAG,CAACc,UAAU,CAAC,8BAA8B,CAAC,EAAE;QAC9DT,KAAI,CAAC3L,SAAS,GAAG,IAAI;QACrB2L,KAAI,CAACU,iBAAiB,EAAE;QACxBV,KAAI,CAACvD,aAAa,GAAG,mBAAmB;MAC1C;MAEA,IAAI,CAACuD,KAAI,CAAC3L,SAAS,EAAE;QACnB,IAAI2L,KAAI,CAACjF,MAAM,CAAC4E,GAAG,CAACc,UAAU,CAAC,0BAA0B,CAAC,EAAE;UAC1D,MAAMT,KAAI,CAACW,oBAAoB,EAAE;UAEjC,IAAIX,KAAI,CAACrG,mBAAmB,KAAK9I,oBAAoB,CAAC+P,IAAI,EAAE;YAC1D;UACF;QACF;MACF;MAEA,IAAIZ,KAAI,CAAClK,QAAQ,EAAE;QACjB;QACAkK,KAAI,CAACa,cAAc,EAAE;MACvB;MAEAb,KAAI,CAAC/E,qBAAqB,CAAC6F,iBAAiB,CAACvB,SAAS,CAACwB,GAAG,IAAIf,KAAI,CAACgB,mBAAmB,GAAGD,GAAG,CAAC;MAC7Ff,KAAI,CAAC/E,qBAAqB,CAACgG,iBAAiB,CAAC1B,SAAS,CAAC2B,GAAG,IAAIlB,KAAI,CAACmB,0BAA0B,GAAGD,GAAG,CAAC;MACpGlB,KAAI,CAAC/E,qBAAqB,CAACmG,qBAAqB,CAAC7B,SAAS,CAAC8B,GAAG,IAAIrB,KAAI,CAACsB,uBAAuB,GAAGD,GAAG,CAAC;IAAC;EACxG;EAEA;EACAE,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAClN,SAAS,EAAE;MAClB,IAAI,CAACmN,iBAAiB,EAAE;IAC1B;EAEF;EACA;EACMhB,eAAeA,CAAA;IAAA,IAAAiB,MAAA;IAAA,OAAAxB,iBAAA;MACnB,MAAMyB,YAAY,SAASD,MAAI,CAACjG,kBAAkB,CAACmG,eAAe,EAAE;MAEpEF,MAAI,CAAC/C,iBAAiB,GAAGgD,YAAY,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKtR,QAAQ,CAACuR,cAAc,CAAC;MACnFN,MAAI,CAAC9C,cAAc,GAAG+C,YAAY,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKtR,QAAQ,CAACuR,cAAc,CAAC;MAChFN,MAAI,CAACtM,gBAAgB,GAAGuM,YAAY,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKtR,QAAQ,CAACwR,YAAY,CAAC;MAChFP,MAAI,CAACrM,aAAa,GAAGsM,YAAY,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKtR,QAAQ,CAACyR,SAAS,CAAC;MAC1ER,MAAI,CAACS,aAAa,GAAG,IAAI,CAAC,CAAC;IAAA;EAC7B;EAEArB,cAAcA,CAAA;IAAA,IAAAsB,MAAA;IACZ,IAAI,CAAClH,qBAAqB,CAACmH,aAAa,CAAC,IAAI,CAACtM,QAAQ,CAAC,CAACyJ,SAAS,CAAC;MAChE8C,IAAI;QAAA,IAAAC,IAAA,GAAArC,iBAAA,CAAE,WAAO3L,UAAsB,EAAI;UACrC,IAAI,CAACA,UAAU,EAAE;YACf6N,MAAI,CAACnH,mBAAmB,CAACuH,SAAS,CAAC,OAAO,EAAE,iCAAiCJ,MAAI,CAACrM,QAAQ,EAAE,CAAC;YAC7F;UACF;UAEA;UACAqM,MAAI,CAAC7N,UAAU,GAAGA,UAAU;UAC5B6N,MAAI,CAAC9L,cAAc,GAAG/B,UAAU,CAACyI,MAAM;UACvCoF,MAAI,CAACK,YAAY,GAAGvS,QAAQ,CAACkS,MAAI,CAAC9L,cAAc,CAAC;UACjD8L,MAAI,CAAC7N,UAAU,CAACmO,eAAe,GAAG,IAAI;UAEtC,IAAIN,MAAI,CAACxI,mBAAmB,KAAK9I,oBAAoB,CAAC6R,IAAI,EAAE;YAC1DP,MAAI,CAAC7N,UAAU,CAACqO,cAAc,GAAG,KAAK;UACxC;UAEA,IAAIR,MAAI,CAAC7N,UAAU,CAACsO,YAAY,EAAEC,WAAW,EAAE;YAC7CV,MAAI,CAAC5L,YAAY,CAACuM,IAAI,CAAC,GAAGX,MAAI,CAAC7N,UAAU,CAACsO,YAAY,CAACC,WAAW,CAAC;UACrE;UAEAV,MAAI,CAAC7G,oBAAoB,CAACyH,iBAAiB,CAACZ,MAAI,CAAC7N,UAAU,CAACkF,SAAS,EAAE2I,MAAI,CAAC7N,UAAU,CAACmF,gBAAgB,CAAC;UAExG0I,MAAI,CAAC7F,eAAe,GAAG6F,MAAI,CAAC7N,UAAU,EAAEsO,YAAY,EAAEI,2BAA2B,EAC7E1F,MAAM,CAAC2F,0BAA0B,IAAIA,0BAA0B,CAACC,QAAQ,CAAC;UAE7Ef,MAAI,CAACnM,aAAa,GAAGmM,MAAI,CAAC7N,UAAU,EAAEsO,YAAY,EAAEI,2BAA2B,EAC3E1F,MAAM,CAAC2F,0BAA0B,IAAIA,0BAA0B,CAACE,YAAY,CAAC;UAEjFhB,MAAI,CAACxG,kBAAkB,CAACyH,gBAAgB,CAACjB,MAAI,CAAC/J,WAAW,EAAE+J,MAAI,CAAC7F,eAAe,CAAC;UAEhF;UACA,IAAI6F,MAAI,CAACD,aAAa,EAAEC,MAAI,CAACxG,kBAAkB,CAAC0H,eAAe,CAAClB,MAAI,CAAC/J,WAAW,CAAC;UAEjF;UACA,MAAMkL,YAAY,GAAGnB,MAAI,CAACjH,wBAAwB,CAACqI,iCAAiC,CAACpB,MAAI,CAAC7N,UAAU,EAAEsO,YAAY,EAAEI,2BAA2B,CAAC;UAChJb,MAAI,CAAC/J,WAAW,CAACoL,OAAO,GAAG;YACzBC,eAAe,EAAEtB,MAAI;YACrBuB,cAAc,EAAEvB,MAAI,CAAC7N,UAAU,EAAEqO,cAAc;YAC/CgB,gBAAgB,EAAE,IAAI;YACtBhK,mBAAmB,EAAEwI,MAAI,CAACxI,mBAAmB;YAC7CiK,kBAAkB,EAAGC,QAAQ,IAAK1B,MAAI,CAACnP,cAAc,CAAC6Q,QAAQ,CAAC;YAC/DC,iBAAiB,EAAG5K,IAAI,IAAKiJ,MAAI,CAAC4B,SAAS,CAAC7K,IAAI,CAAC;YACjD8K,aAAa,EAAEA,CAAA,KAAK;cAAG,OAAO7B,MAAI,CAACxN,MAAM,CAAC8B,GAAG,CAACwN,SAAS,EAAE;YAAC,CAAC;YAC3DC,qBAAqB,EAAGC,QAAQ,IAAI;cAAG,OAAO;gBAAEC,UAAU,EAAEjC,MAAI,CAACvM,SAAS,CAACwO,UAAU;gBAAElL,IAAI,EAAEiJ,MAAI,CAACxN,MAAM,EAAE8B,GAAG,EAAE4N,UAAU,CAACF,QAAQ,CAAC,EAAEjL,IAAI;gBAAEoK;cAAY,CAAE;YAAC;WAC/H;UAE7B,IAAInB,MAAI,CAAC7N,UAAU,CAACgQ,gBAAgB,EAAE;YACpCnC,MAAI,CAAC/J,WAAW,CAACmM,UAAU,GAAG,IAAI;YAClCpC,MAAI,CAAC/J,WAAW,CAACoM,iBAAiB,GAAG,IAAI;YACzCrC,MAAI,CAAC/J,WAAW,CAACqM,sBAAsB,GAAGtC,MAAI,CAAC7N,UAAU,CAACoQ,oBAAoB;YAE9E,IAAI,CAACvC,MAAI,CAAC7N,UAAU,CAACoQ,oBAAoB,EAAE;cACzCvC,MAAI,CAAC/J,WAAW,CAACuM,kBAAkB,GAAGxC,MAAI,CAAC7N,UAAU,CAACsQ,eAAe;cACrEzC,MAAI,CAAC/J,WAAW,CAACyM,0BAA0B,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;YAC7D;UACF;UAEA;UACA1C,MAAI,CAAC/G,iBAAiB,CAAC0J,aAAa,CAClC3C,MAAI,CAAC7N,UAAU,CAACkF,SAAS,EACzB2I,MAAI,CAAC7N,UAAU,CAACmF,gBAAgB,EAChC0I,MAAI,CAAC7N,UAAU,CAACyQ,QAAQ,CAAC,CAACxF,SAAS;YAAA,IAAAyF,KAAA,GAAA/E,iBAAA,CACjC,WAAOgF,UAAuB,EAAI;cAChC,IAAI,CAACA,UAAU,IAAIA,UAAU,CAAChQ,MAAM,KAAK,CAAC,EAAE;gBAC1CkN,MAAI,CAACnH,mBAAmB,CAACuH,SAAS,CAAC,OAAO,EAAE,sBAAsB,CAAC;gBACnEJ,MAAI,CAAC1H,aAAa,GAAG,KAAK;gBAC1B;cACF;cAEA,IAAIwK,UAAU,CAAChQ,MAAM,GAAG,CAAC,EAAE;gBACzBkN,MAAI,CAACnH,mBAAmB,CAACuH,SAAS,CAAC,OAAO,EAAE,wDAAwD,CAAC;gBACrGJ,MAAI,CAAC1H,aAAa,GAAG,KAAK;gBAC1B;cACF;cAEA0H,MAAI,CAACvM,SAAS,GAAGqP,UAAU,CAAC,CAAC,CAAC;cAE9B9C,MAAI,CAAC7N,UAAU,CAAC6D,UAAU,SAASgK,MAAI,CAACjH,wBAAwB,CAC7DgK,aAAa,CAAC/C,MAAI,CAAC7N,UAAU,CAACsO,YAAY,EAAEI,2BAA2B,EACtEb,MAAI,CAACvM,SAAS,CAACwO,UAAU,EACzBjC,MAAI,CAAC/J,WAAW,EAChB,IAAI,EACJ+J,MAAI,CAAC1I,gBAAgB,EACrB0I,MAAI,CAAC7N,UAAU,CAACqO,cAAc,EAC9B,IAAI,EACJR,MAAI,CAACxI,mBAAmB,EACxB,IAAI,EACJwI,MAAI,CAACD,aAAa,CAAC;cAEvBC,MAAI,CAACgD,wBAAwB,EAAE;cAE/B;cACAhD,MAAI,CAACvN,eAAe,GAAGuN,MAAI,CAAC7N,UAAU,EAAE6D,UAAU,EAC9CmF,MAAM,CAACuE,CAAC,IAAIA,CAAC,CAACuD,KAAK,KAAK,YAAY,IAAIvD,CAAC,CAACuD,KAAK,KAAK,KAAK,CAAC,CAC3DC,GAAG,CAACxD,CAAC,IAAIA,CAAC,CAACuD,KAAK,CAAC;cAEpBjD,MAAI,CAACzF,iBAAiB,GAAGyF,MAAI,CAAC7N,UAAU,EAAEyI,MAAM,EAAEO,MAAM,CAACuE,CAAC,IAAIA,CAAC,CAACyD,SAAS,KAAKvV,cAAc,CAACwV,WAAW,CAAC,EAAEtQ,MAAM,GAAG,CAAC;cACrHkN,MAAI,CAACqD,qBAAqB,EAAE;cAE5B;cACA,MAAMrD,MAAI,CAACsD,WAAW,EAAE;YAC1B,CAAC;YAAA,iBAAAC,GAAA;cAAA,OAAAV,KAAA,CAAAW,KAAA,OAAAC,SAAA;YAAA;UAAA,IAAC;UAENzD,MAAI,CAAC0D,eAAe,EAAE;QACxB,CAAC;QAAA,gBAxGDxD,IAAIA,CAAAyD,EAAA;UAAA,OAAAxD,IAAA,CAAAqD,KAAA,OAAAC,SAAA;QAAA;MAAA;KAyGL,CAAC;EACJ;EAEQT,wBAAwBA,CAAA;IAC9B,IAAI,CAAC,IAAI,CAAC7Q,UAAU,CAACyR,2BAA2B,IAAI,CAAC,IAAI,CAACzR,UAAU,EAAE0R,2BAA2B,EAAE/Q,MAAM,EACvG;IAEF,MAAMgR,SAAS,GAAIC,iBAAoC,IAAI;MAEzD;MACA,MAAMC,eAAe,GAAsDpJ,MAAM,IAAI;QACnF,OAAO,IAAI,CAACpB,kBAAkB,CAACyK,WAAW,CAACrJ,MAAM,CAAC7D,IAAI,EAAE,IAAI,CAAC5E,UAAU,EAAE0R,2BAA2B,EAAEE,iBAAiB,CAAC;MAC1H,CAAC;MAED,IAAI,CAACvR,MAAM,EAAE8B,GAAG,GAAG,IAAI,CAAC9B,MAAM,CAAC8B,GAAG,CAAC4P,aAAa,CAAC,aAAa,EAAEF,eAAe,CAAC,GAAG,IAAI,CAAC/N,WAAW,CAACgO,WAAW,GAAGD,eAAe;MAEjI;MACA,IAAI,CAACxK,kBAAkB,CAAC2K,aAAa,CAAC,IAAI,CAAChS,UAAU,CAAC6D,UAAU,EAAE,IAAI,CAAC7D,UAAU,CAAC0R,2BAA2B,EAAEE,iBAAiB,CAAC;MACjI,IAAI,IAAI,CAACvR,MAAM,EAAE8B,GAAG,EAClB,IAAI,CAAC9B,MAAM,CAAC8B,GAAG,CAAC4P,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC/R,UAAU,CAAC6D,UAAU,CAAC;MAEzE,IAAI,CAACxD,MAAM,EAAE8B,GAAG,EAAE8P,UAAU,EAAE;IAChC,CAAC;IAED,IAAI,IAAI,CAACjS,UAAU,EAAE0R,2BAA2B,EAAEQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU,EAAE9E,IAAI,CAAC6E,CAAC,IAAIA,CAAC,CAACE,kBAAkB,CAAC,CAAC,EAAE;MAC1G,IAAI,CAAC5K,WAAW,CAAC6K,kCAAkC,EAAE,CAACrH,SAAS,CAAC;QAC9D8C,IAAI,EAAG6D,iBAAoC,IAAI;UAC7CD,SAAS,CAACC,iBAAiB,CAAC;QAC9B;OACD,CAAC;IACJ,CAAC,MAAM;MACLD,SAAS,CAAC,EAAE,CAAC;IACf;EACF;EAEQT,qBAAqBA,CAAA;IAC3B,IAAI,CAACpJ,aAAa,CAACyK,WAAW,CAACtH,SAAS,CAACxC,MAAM,IAAG;MAChD,IAAIA,MAAM,CAAC+J,SAAS,EAAE;QACpB,IAAI,CAAC/R,mBAAmB,GAAGmL,SAAS;QAEpC,IAAI,IAAI,CAAChI,UAAU,EAAEjD,MAAM,GAAG,CAAC,EAAE;UAC/B,IAAI,CAACkL,iBAAiB,GAAG,EAAE;UAC3B,IAAI,CAACjI,UAAU,GAAG,EAAE;QACtB;QAEA,IAAI,CAACuC,aAAa,GAAG,KAAK;QAC1B;MACF;MAEA,IAAI,CAAC+H,YAAY,GAAGzF,MAAM,CAACA,MAAM;MACjC,IAAI,CAACyB,kBAAkB,GAAG,IAAI;MAC9B,IAAI,CAACuI,cAAc,CAAC,IAAI,CAACvE,YAAY,EAAE,IAAI,CAAC;IAC9C,CAAC,CAAC;EAEJ;EAEciD,WAAWA,CAAA;IAAA,IAAAuB,MAAA;IAAA,OAAA/G,iBAAA;MAEvB,IAAI5K,OAAO,GAAG,EAAE;MAChB;MACA,IAAI;QACFA,OAAO,SAAS5F,cAAc,CAACuX,MAAI,CAAC7K,iBAAiB,CAAC8K,UAAU,CAACD,MAAI,CAAC1S,UAAU,CAACwB,QAAQ,CAAC,CAAC;MAC7F,CAAC,CAAC,OAAOoR,KAAK,EAAE;QACd7R,OAAO,GAAG,EAAE;MACd;MAEA,IAAIA,OAAO,CAACJ,MAAM,GAAG,CAAC,EAAE;QACtB,MAAMkS,YAAY,SAASH,MAAI,CAAC7K,iBAAiB,CAACiL,aAAa,CAACJ,MAAI,CAAC1S,UAAU,CAAC;QAChF,MAAM+S,aAAa,GAAGhS,OAAO,CAACmR,IAAI,CAACc,MAAM,IAAIA,MAAM,CAACC,eAAe,IAAID,MAAM,CAACH,YAAY,KAAKA,YAAY,CAAC,IACvG9R,OAAO,CAACmR,IAAI,CAACc,MAAM,IAAIA,MAAM,CAACE,SAAS,IAAIF,MAAM,CAACH,YAAY,KAAKA,YAAY,CAAC;QACrFH,MAAI,CAAC3R,OAAO,GAAGA,OAAO;QAEtB;QACA,IAAIgS,aAAa,KAAKA,aAAa,CAACI,iBAAiB,IAAIJ,aAAa,CAACK,aAAa,CAAC,EAAE;UACrFV,MAAI,CAACxI,kBAAkB,GAAG,IAAI;UAC9B;QACF;MACF,CAAC,MAAM;QACLwI,MAAI,CAAC3R,OAAO,GAAG,EAAE;MACnB;IAAC;EACH;EAEcsL,oBAAoBA,CAAA;IAAA,IAAAgH,MAAA;IAAA,OAAA1H,iBAAA;MAChC0H,MAAI,CAAChO,mBAAmB,GAAG9I,oBAAoB,OAAO8W,MAAI,CAAC5L,WAAW,CAAC6L,oBAAoB,CAACD,MAAI,CAAClO,gBAAgB,EAAEkO,MAAI,CAACtH,cAAc,CAACwH,WAAW,EAAEF,MAAI,CAAC7R,QAAQ,CAAC,CAAC;MAEnK,IAAI6R,MAAI,CAAChO,mBAAmB,KAAK9I,oBAAoB,CAAC+P,IAAI,EAAE;QAC1D+G,MAAI,CAAC3M,mBAAmB,CAACuH,SAAS,CAAC,eAAe,EAAE,iFAAiF,CAAC;QACtIoF,MAAI,CAAClN,aAAa,GAAG,KAAK;MAC5B;IAAC;EACH;EAEAsM,cAAcA,CAAChK,MAAyB,EAAE+K,WAAW,GAAG,KAAK;IAAA,IAAAC,MAAA;IAE3D,IAAI,IAAI,CAAC3L,aAAa,CAAC4L,yBAAyB,CAACjL,MAAM,EAAE,IAAI,CAAC,KAAK,KAAK,EACtE;IAEF,IAAI+K,WAAW,EACb,IAAI,CAACrN,aAAa,GAAGqN,WAAW;IAElC;IACA,MAAMG,aAAa,GAAG,IAAIvX,oBAAoB,CAAC,IAAI,CAACoF,QAAQ,EAAE,IAAI,CAACxB,UAAU,CAACyB,iBAAiB,EAAE,IAAI,EAAEgH,MAAM,CAAC;IAE9G;IACA,IAAI,CAAC3B,iBAAiB,CAAC8M,UAAU,CAAC,IAAI,CAACtS,SAAS,CAACC,EAAE,EAAEoS,aAAa,CAAC,CAAC1I,SAAS,CAAC;MAC5E8C,IAAI;QAAA,IAAA8F,KAAA,GAAAlI,iBAAA,CAAE,WAAO/H,UAAiB,EAAI;UAChC,IAAIA,UAAU,EAAE;YACd6P,MAAI,CAAC7P,UAAU,GAAGA,UAAU;YAC5B6P,MAAI,CAAC5H,iBAAiB,GAAGlQ,QAAQ,CAAC8X,MAAI,CAAC7P,UAAU,CAAC;YAElD;YACA,IAAI6P,MAAI,CAAC5S,gBAAgB,IAAI4S,MAAI,CAACzT,UAAU,CAAC8T,kBAAkB,IAAI,CAACL,MAAI,CAAC5Q,YAAY,EAAE;cACrF,MAAM4Q,MAAI,CAACM,eAAe,EAAE;cAC5BN,MAAI,CAACO,kBAAkB,EAAE;cACzBP,MAAI,CAAC/T,UAAU,CAAC+T,MAAI,CAACzT,UAAU,CAACiU,kBAAkB,EAAEC,WAAW,CAAC;YAClE;YAEA,IAAIT,MAAI,CAACjT,YAAY,KAAKhF,uBAAuB,CAAC+O,KAAK,EACrDkJ,MAAI,CAAC5Q,YAAY,GAAG4Q,MAAI,CAAC7P,UAAU;YAErC;YACA6P,MAAI,CAACtN,aAAa,GAAG,KAAK;UAC5B;QACF,CAAC;QAAA,gBAlBD4H,IAAIA,CAAAoG,GAAA;UAAA,OAAAN,KAAA,CAAAxC,KAAA,OAAAC,SAAA;QAAA;MAAA,GAkBH;MACDsB,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAIA,KAAK,CAACwB,MAAM,KAAK,GAAG,EAAE;UACxB,IAAI,CAACxQ,UAAU,GAAG,EAAE;UACpB,IAAI,CAACiI,iBAAiB,GAAG,EAAE;QAC7B;QAEA,IAAI,CAACnF,mBAAmB,CAACuH,SAAS,CAAC,OAAO,EAAE2E,KAAK,CAACyB,OAAO,CAAC;QAC1D,IAAI,CAAClO,aAAa,GAAG,KAAK;MAC5B;KACD,CAAC;IAEF;IACA,IAAI,CAAC9F,MAAM,EAAE8B,GAAG,EAAEmS,YAAY,CAAC;MAAEC,KAAK,EAAE;IAAI,CAAE,CAAC;EACjD;EAEAhR,mBAAmBA,CAACkF,MAAM;IACxB,IAAI,CAAC+L,cAAc,EAAE;EACvB;EAEAA,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACxU,UAAU,CAACsO,YAAY,EAAE9L,YAAY,EAAE;MAC9C,IAAI,CAACA,YAAY,CAACgM,IAAI,CAAC,GAAG,IAAI,CAACxO,UAAU,CAACsO,YAAY,CAAC9L,YAAY,CAAC;IACtE;EACF;EAEA;EACA0K,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACd,iBAAiB,EAAE;IACxB,IAAI,CAACjE,aAAa,GAAG,mBAAmB;EAC1C;EAEAlF,uBAAuBA,CAACwH,KAAK;IAC3B,MAAMgK,cAAc,GAAGhK,KAAK,CAACiK,OAAO,CAAC3D,GAAG,CAAE4D,IAAI,IAAKA,IAAI,CAACC,KAAK,CAAC;IAC9D,MAAMC,UAAU,GAAGpK,KAAK,CAACtI,GAAG,CAAC2S,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC;IAE9D,IAAID,UAAU,KAAK,IAAI,EAAE;MACvBA,UAAU,CAACE,MAAM,CAACC,UAAU,GAAG,YAAYP,cAAc,CAACQ,IAAI,CAAC,IAAI,CAAC,EAAE;MACtExK,KAAK,CAACtI,GAAG,CAAC+S,aAAa,EAAE;MACzBzK,KAAK,CAACtI,GAAG,CAACgT,cAAc,CAACN,UAAU,CAAC;IACtC;EACF;EAEA1R,WAAWA,CAACsF,MAAM;IAChB,MAAM2M,kBAAkB,GAAG,IAAI,CAACzO,qBAAqB,CAAC0O,qBAAqB,CAAC,IAAI,CAACrV,UAAU,CAACsO,YAAY,EAAEI,2BAA2B,EAAE,IAAI,CAACpN,SAAS,CAACwO,UAAU,CAAC;IACjK,IAAI,CAACxI,aAAa,CAACgO,wBAAwB,CAAC7M,MAAM,CAACtG,GAAG,CAACwN,SAAS,EAAE,EAAE;MAAE4F,IAAI,EAAE,IAAI,CAAClV,MAAM;MAAE+U;IAAkB,CAAE,CAAC;IAC9G,IAAI,CAACzN,uBAAuB,CAAC6N,uBAAuB,CAAC,IAAI,CAACnV,MAAM,CAAC;IAEjE,IAAI,CAACoV,0BAA0B,GAAG,IAAI,CAAC3O,iBAAiB,CAAC4O,cAAc,CAACzK,SAAS,CAAC0K,IAAI,IAAG;MACvF,IAAIA,IAAI,EAAE;QACR,IAAI,CAAC3O,oBAAoB,CAAC4O,qBAAqB,EAAE,CAC9CC,OAAO,CAACC,cAAc,IAAIA,cAAc,CAACC,WAAW,GAAGJ,IAAI,CAAC;MACjE;IACF,CAAC,CAAC;EACJ;EAEA3W,WAAWA,CAAA;IAET,IAAI,CAAC,IAAI,CAACmB,UAAU,EAAE,EAAE;MACtB;IACF;IAEA,MAAM6V,MAAM,GAAG,IAAI,CAACC,mBAAmB,EAAE;IACzC,IAAID,MAAM,EAAErV,MAAM,EAAE;MAClB,IAAI,CAAC+F,mBAAmB,CAACuH,SAAS,CAAC,OAAO,EAAE+H,MAAM,CAACf,IAAI,CAAC,KAAK,CAAC,CAAC;MAC/D;IACF;IAEA,IAAI,CAAC9O,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC+P,sBAAsB,EAAE;IAC7B,IAAI,CAACpP,iBAAiB,CAACqP,WAAW,CAAC,IAAI,CAACnP,oBAAoB,CAACoP,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC9U,SAAS,CAACC,EAAE,CAAC,CACnG0J,SAAS,CAAC;MACT8C,IAAI,EAAGtN,mBAAwC,IAAI;QACjD,IAAI,CAACA,mBAAmB,GAAGA,mBAAmB;QAC9C,IAAI,CAAC0F,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC3G,qBAAqB,GAAG,IAAI,CAACiB,mBAAmB,EAAEC,iBAAiB,EAAEC,MAAM,GAAG,CAAC;QAEpF,IAAI,CAACF,mBAAmB,CAACG,WAAW,EAAE;UACpC;UACA,IAAIH,mBAAmB,CAAC4V,SAAS,EAAE1V,MAAM,IAAI,IAAI,CAACqG,oBAAoB,CAACoP,eAAe,EAAE,EAAEE,wBAAwB,EAAE,EAAEC,IAAI,EAAE;YAC1H,IAAI,CAAC7P,mBAAmB,CAAC8P,WAAW,CAAC,SAAS,EAAE,6HAA6H,CAAC;UAChL;UAEA,IAAI,CAACC,cAAc,CAAC,IAAI,CAAC;UACzB,IAAI,CAACzP,oBAAoB,CAAC0P,mBAAmB,EAAE;UAC/C,IAAI,CAAChQ,mBAAmB,CAACiQ,WAAW,CAAC,SAAS,EAAE,oBAAoB,CAAC;UAErE;UACA,IAAI,CAAClE,cAAc,CAAC,IAAI,CAACvE,YAAY,CAAC;QACxC;MACF,CAAC;MACD0E,KAAK,EAAEgE,GAAG,IAAG;QACX,IAAI,CAACzQ,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACsQ,cAAc,CAAC,KAAK,CAAC;QAC1B,IAAI,CAAC/P,mBAAmB,CAACuH,SAAS,CAAC,OAAO,EAAE2I,GAAG,CAAChE,KAAK,CAAC;MACxD;KACD,CAAC;EACN;EAEAqD,mBAAmBA,CAAA;IACjB,MAAMD,MAAM,GAAa,EAAE;IAE3B,IAAI,CAAC,IAAI,CAAChO,eAAe,EAAErH,MAAM,EAAE;MACjC,OAAOqV,MAAM;IACf;IAEA,IAAI,CAAChP,oBAAoB,CAAC4O,qBAAqB,EAAE,CAC9CC,OAAO,CAACC,cAAc,IAAG;MACxB,IAAI,CAACA,cAAc,EAAElR,IAAI,IACpBkR,cAAc,CAAClR,IAAI,EAAEiS,MAAM,KAAKzb,WAAW,CAAC0b,MAAM,IAClDhB,cAAc,CAAClR,IAAI,EAAEiS,MAAM,KAAKzb,WAAW,CAAC2b,WAAW,EAC1D;MAEF,MAAMC,oBAAoB,GAAa,EAAE;MACzC,MAAMC,yBAAyB,GAAGnB,cAAc,CAAClR,IAAI,EAAEsS,UAAU,EAC7DlO,MAAM,CAACmO,YAAY,IAAIA,YAAY,CAACC,QAAQ,IAAI,IAAI,IAAID,YAAY,CAACC,QAAQ,KAAK,EAAE,CAAC;MACzF,IAAI,CAACpP,eAAe,CAAC6N,OAAO,CAACwB,cAAc,IAAG;QAC5C,MAAMC,OAAO,GAAG,IAAI,CAAC1T,UAAU,CAACsO,IAAI,CAACqF,GAAG,IAAIA,GAAG,CAAClb,cAAc,CAAC,KAAKyZ,cAAc,CAACvU,EAAE,CAAC;QACtF,IAAI,CAAC+V,OAAO,EAAE;UACZ;QACF;QAEA,MAAME,gBAAgB,GAAGP,yBAAyB,CAAC/E,IAAI,CAACiF,YAAY,IAAIA,YAAY,CAACM,QAAQ,KAAKJ,cAAc,CAACK,MAAM,CAAC;QAExH,IAAIF,gBAAgB,IAAIF,OAAO,CAACD,cAAc,CAACK,MAAM,CAAC,IAAI,IAAI,IAAIJ,OAAO,CAACD,cAAc,CAACK,MAAM,CAAC,KAAK,EAAE,EAAE;UACvGV,oBAAoB,CAACxI,IAAI,CAAC6I,cAAc,CAACM,WAAW,IAAIN,cAAc,CAACK,MAAM,CAAC;QAChF;MACF,CAAC,CAAC;MAEF,IAAIV,oBAAoB,EAAErW,MAAM,EAAE;QAChC,IAAIqW,oBAAoB,CAACrW,MAAM,KAAK,CAAC,EAAE;UACrCqV,MAAM,CAACxH,IAAI,CAAC,GAAGwI,oBAAoB,CAAC,CAAC,CAAC,4CAA4C,CAAC;QACrF,CAAC,MAAM;UACL,MAAMY,cAAc,GAAGZ,oBAAoB,CACxCa,KAAK,CAAC,CAAC,EAAEb,oBAAoB,CAACrW,MAAM,GAAG,CAAC,CAAC,CACzCsU,IAAI,CAAC,IAAI,CAAC;UACbe,MAAM,CAACxH,IAAI,CAAC,GAAGoJ,cAAc,MAAMZ,oBAAoB,CAACA,oBAAoB,CAACrW,MAAM,GAAG,CAAC,CAAC,4CAA4C,CAAC;QACvI;MACF;IACF,CAAC,CAAC;IAEJ,OAAOqV,MAAM;EACf;EAEAE,sBAAsBA,CAAA;IACpB,MAAM4B,aAAa,GAAG,IAAI,CAAC9X,UAAU,CAAC+X,UAAU,EAAE7F,IAAI,CAAC8F,SAAS,IAAIA,SAAS,CAACC,SAAS,KAAKjc,cAAc,CAACkc,QAAQ,CAAC;IACpH,IAAI,CAACJ,aAAa,EAAE;MAClB;IACF;IAEA,IAAI,CAACvQ,oBAAoB,CAAC2O,sBAAsB,CAAC;MAC/CiC,gBAAgB,EAAE,IAAIpc,gBAAgB,CACpC,IAAI,CAACiE,UAAU,CAACwB,QAAQ,EAAE,IAAI,CAACxB,UAAU,CAACoY,UAAU,EACpD9c,kBAAkB,CAAC+c,cAAc,EAAE,IAAI,CAAC/W,SAAS,CAACkM,IAAI,EACtD,IAAI,CAAClM,SAAS,CAACC,EAAE,CAAC;MACpB+W,UAAU,EAAER;KACb,CAAC;EACJ;EAEArB,cAAcA,CAAC8B,YAAqB;IAClC,IAAI,IAAI,CAAC/X,YAAY,KAAKhF,uBAAuB,CAAC+O,KAAK,EAAE;IAEzD,IAAI,CAAC5G,iBAAiB,GAAG4U,YAAY,GAAG,wBAAwB,GAAG,MAAM;IACzE,MAAMC,mBAAmB,GAAe,EAAE;IAC1C,KAAK,MAAM1C,cAAc,IAAI,IAAI,CAAC9O,oBAAoB,CAAC4O,qBAAqB,EAAE,CAAC5M,MAAM,CAACuE,CAAC,IAAI,CAACA,CAAC,CAACkL,UAAU,CAAC,EAAE;MACzG,IAAI3C,cAAc,CAAClR,IAAI,EAAEiS,MAAM,KAAKzb,WAAW,CAAC0b,MAAM,EAAE;QAAE;MAAU;MAEpE,IAAIhB,cAAc,CAACC,WAAW,EAAE;QAC9B,MAAM2C,OAAO,GAAG,IAAI,CAACrY,MAAM,CAAC8B,GAAG,CAAC4N,UAAU,CAAC+F,cAAc,CAACvU,EAAE,CAAC;QAE7D,IAAImX,OAAO,EACTF,mBAAmB,CAAChK,IAAI,CAACkK,OAAO,CAAC;MACrC;IACF;IAEA,IAAIF,mBAAmB,CAAC7X,MAAM,KAAK,CAAC,EAAE;IAEtC,IAAI,CAACN,MAAM,CAAC8B,GAAG,CAACwW,UAAU,CACxB;MACEC,QAAQ,EAAEJ,mBAAmB;MAC7BK,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE;KACf,CAAC;EACN;EAEAha,OAAOA,CAAA;IACL;IACA,IAAI,IAAI,CAAC+M,iBAAiB,EAAE;MAC1B,IAAI,CAACjI,UAAU,GAAGjI,QAAQ,CAAC,IAAI,CAACkQ,iBAAiB,CAAC;MAClD,IAAI,CAAC7E,oBAAoB,CAAC0P,mBAAmB,EAAE;MAC/C,IAAI,CAACjW,mBAAmB,GAAGmL,SAAS;MAEpC,IAAI,CAACvL,MAAM,CAAC8B,GAAG,CAAC4W,iBAAiB,CAAC;QAAEzB,OAAO,EAAE,IAAI,CAAC1T;MAAU,CAAE,CAAC;MAC/D,IAAI,CAACf,YAAY,GAAG,IAAI,CAACe,UAAU;IACrC;EACF;EAEAhF,cAAcA,CAAA;IACZ;IACA,MAAMyX,SAAS,GAAG,IAAI,CAACrP,oBAAoB,CAACgS,IAAI,CAAC,IAAI,CAAC3Y,MAAM,EAAE,IAAI,CAACiH,aAAa,CAAC2R,UAAU,CAAC,IAAI,CAAC5Y,MAAM,CAAC8B,GAAG,CAAC,CAAC;IAE7G,IAAI,IAAI,CAAC+W,uBAAuB,EAAE;MAChC,MAAMtU,IAAI,GAAG,EAAE;MACfyR,SAAS,CAACR,OAAO,CAACtU,EAAE,IAAG;QACrB,MAAM4X,IAAI,GAAG,IAAI,CAAC9Y,MAAM,CAAC8B,GAAG,CAAC4N,UAAU,CAACxO,EAAE,CAAC;QAC3C,IAAI4X,IAAI,EAAE;UACRvU,IAAI,CAAC4J,IAAI,CAAC2K,IAAI,CAACvU,IAAI,CAAC;QACtB;MACF,CAAC,CAAC;MAEF,IAAI,CAACsU,uBAAuB,EAAEE,YAAY,CAACxU,IAAI,CAAC;IAClD;EACF;EAEAoH,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC/E,eAAe,CAAC+E,iBAAiB,CAAC,GAAG,IAAI,CAAC7G,gBAAgB,EAAE,CAAC;EAC3E;EAEMoM,eAAeA,CAAA;IAAA,IAAA8H,MAAA;IAAA,OAAA1N,iBAAA;MACnB,IAAI0N,MAAI,CAACtN,cAAc,EAAE;QACvB,MAAMuN,KAAK,GAAe,CACxB;UAAEC,KAAK,EAAE,SAAS;UAAEC,UAAU,EAAE;QAAe,CAAE,EACjD;UACED,KAAK,EAAEF,MAAI,CAACtN,cAAc,CAACwH,WAAW,EAAE/F,IAAI;UAC5CgM,UAAU,EAAE,aAAaH,MAAI,CAACrZ,UAAU,CAACkF,SAAS,IAAImU,MAAI,CAACrZ,UAAU,CAACmF,gBAAgB;SACvF,EACD;UAAEoU,KAAK,EAAEF,MAAI,CAACrZ,UAAU,EAAEoY;QAAU,CAAE,CACvC;QACDiB,MAAI,CAACtR,oBAAoB,CAAC0R,gBAAgB,CAACH,KAAK,CAAC;MACnD;IAAC;EACH;EAEAlN,iBAAiBA,CAAA;IACf,IAAI,CAACrE,oBAAoB,CAACgG,IAAI,CAAC;MAC7B2L,WAAW,EAAE,IAAI,CAAC3R,oBAAoB,CAAC4R,cAAc;MACrDC,SAAS,EAAE,IAAI,CAAC7R,oBAAoB,CAAC8R,iBAAiB;MACtD5B,SAAS,EAAE,mBAAmB;MAC9BrT,IAAI,EAAE;KACP,CAAC;EACJ;EAEA9C,4BAA4BA,CAAC2I,KAAwB;IACnD,IAAI,CAACP,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACnI,cAAc,GAAGpG,QAAQ,CAAC,IAAI,CAACoG,cAAc,CAAC;IACnD,IAAI,CAACmM,YAAY,GAAGvS,QAAQ,CAAC,IAAI,CAACoG,cAAc,CAAC;IAEjD,MAAM+X,gBAAgB,GAAG,IAAI,CAAC/X,cAAc,CAACgP,GAAG,CAACgJ,EAAE,IAAG;MAAG,OAAO;QAAE,OAAO,EAAEA,EAAE,CAACC,WAAW;QAAE,OAAO,EAAED,EAAE,CAACE;MAAc,CAAE;IAAE,CAAC,CAAC;IAC3H,IAAI,CAAC7S,YAAY,CAAC8S,kBAAkB,CAACJ,gBAAgB,CAAC;EACxD;EAEA1Y,iBAAiBA,CAACqJ,KAAc;IAC9B,IAAI,CAAC9I,gBAAgB,GAAG8I,KAAK;EAC/B;EAEAI,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC7D,oBAAoB,CAACmT,kBAAkB,EAAE,EAAE;MAClD,OAAOC,OAAO,CAAC,oEAAoE,CAAC;IACtF;IAEA,OAAO,IAAI;EACb;EAEM1b,cAAcA,CAAC6Q,QAAiB,EAAE1M,YAAa;IAAA,IAAAwX,MAAA;IAAA,OAAA1O,iBAAA;MACnD,IAAI,CAAC9I,YAAY,KAAM,CAAC0M,QAAQ,IAAI,CAAC8K,MAAI,CAACra,UAAU,EAAEqO,cAAc,IAC9DkB,QAAQ,KAAK8K,MAAI,CAACha,MAAM,CAAC8B,GAAG,CAACmY,gBAAgB,EAAE,CAAC3Z,MAAM,GAAG,CAAC,IAAI0Z,MAAI,CAACha,MAAM,CAAC8B,GAAG,CAACmY,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAACC,KAAK,CAAE,CAAC,EAAE;QAClH;MACF;MAEA,MAAMC,oBAAoB,GAAIC,cAAc,IAAI;QAC9C,IAAIJ,MAAI,CAACrT,oBAAoB,CAAC7G,UAAU,EAAE,IAAIka,MAAI,CAACrT,oBAAoB,CAACmT,kBAAkB,EAAE,EAAE;UAC5FE,MAAI,CAAC7S,yBAAyB,CAACkT,UAAU,CAAC,iBAAiB,EAAE;YAC3DC,MAAM,EAAE,0BAA0B;YAClCtG,OAAO,EAAE,4EAA4E;YACrFuG,YAAY,EAAE,mBAAmB;YACjCC,gBAAgB,EAAE,iBAAiB;YACnCC,QAAQ,EAAEL;WACX,CAAC;UACF,OAAO,IAAI;QACb;QACA,OAAO,KAAK;MACd,CAAC;MAED,MAAM7V,IAAI,GAAG/B,YAAY,GAAGA,YAAY,GAAG0M,QAAQ,GAAG8K,MAAI,CAACha,MAAM,CAAC8B,GAAG,CAACmY,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC1V,IAAI,GAAGgH,SAAS;MAC5GyO,MAAI,CAACtV,SAAS,SAASsV,MAAI,CAAC3S,4BAA4B,CAACqT,YAAY,CACnEV,MAAI,CAACnV,SAAS,EAAEmV,MAAI,CAAClV,gBAAgB,EAAEP,IAAI,EAC3CyV,MAAI,CAACra,UAAU,CAACgb,sBAAsB,GAAGX,MAAI,CAACra,UAAU,CAACib,sBAAsB,GAAGrP,SAAS,EAC3FyO,MAAI,CAACra,UAAU,EAAEsO,YAAY,EAAE4M,gBAAgB,EAC/Cb,MAAI,CAACra,UAAU,CAACsO,YAAY,EAAE6M,aAAa,EAC3Cd,MAAI,CAACra,UAAU,CAACsO,YAAY,CAACI,2BAA2B,CAAC;MAE3D2L,MAAI,CAACjV,gBAAgB,GAAGmK,QAAQ,GAAG3K,IAAI,CAACvI,cAAc,CAAC,GAAG,EAAE;MAE5D,IAAI+e,gBAAgB,GAAG,EAAE;MACzB,QAAQf,MAAI,CAACtV,SAAS,EAAEsW,SAAS,EAAEC,IAAI;QACrC,KAAKjgB,iBAAiB,CAACkgB,YAAY;UACjC,IAAI,CAAElB,MAAI,CAACtV,SAAS,EAAEC,SAAgC,EACpD;UAEDqV,MAAI,CAACtV,SAAS,CAACC,SAAgC,CAACwW,gBAAgB,GAAGnB,MAAI,CAACtV,SAAS,CAACsW,SAAS,CAACI,OAAO;UACnGpB,MAAI,CAACtV,SAAS,CAACC,SAAgC,CAACW,qBAAqB,GAAGf,IAAI,GAAGA,IAAI,CAACyV,MAAI,CAAC/P,kBAAkB,CAACkR,gBAAgB,CAAC,GAAG5P,SAAS;UAE1I,IAAI4O,oBAAoB,CAAC,MAAK;YAC5BH,MAAI,CAACvb,OAAO,EAAE;YACdub,MAAI,CAAC7U,mBAAmB,GAAG,IAAI;UACjC,CAAC,CAAC,EAAE;UAEJ6U,MAAI,CAAC7U,mBAAmB,GAAG,IAAI;UAC/B;QAEF,KAAKnK,iBAAiB,CAACqgB,WAAW;UAChC,IAAIlB,oBAAoB,CAAC,MAAK;YAC5BH,MAAI,CAACvb,OAAO,EAAE;YACdub,MAAI,CAACna,yBAAyB,GAAG,IAAI;UACvC,CAAC,CAAC,EAAE;UAEJma,MAAI,CAACna,yBAAyB,GAAG,IAAI;UACrC;QACF,KAAK7E,iBAAiB,CAACsgB,OAAO;UAC5B;UACAP,gBAAgB,GAAG,CAAC5e,mBAAmB,CAACof,MAAM,EAAEpf,mBAAmB,CAACqf,cAAc,EAAErf,mBAAmB,CAACsf,cAAc,EAAEtf,mBAAmB,CAACuf,gBAAgB,CAAC;UAC7J1B,MAAI,CAACtV,SAAS,CAACC,SAAS,CAACgK,YAAY,GAAGqL,MAAI,CAACtV,SAAS,CAACC,SAAS,CAACgK,YAAY,CAAChG,MAAM,CAACgT,EAAE,IAAI,CAACZ,gBAAgB,CAACa,QAAQ,CAACD,EAAE,CAACV,IAA2B,CAAC,CAAC;UAEtJjB,MAAI,CAAC/V,eAAe,CAACuS,MAAM,GAAGtH,QAAQ,GAAGnU,WAAW,CAAC8gB,MAAM,GAAG9gB,WAAW,CAAC+gB,MAAM;UAChF9B,MAAI,CAAC/V,eAAe,CAACC,KAAK,GAAGgL,QAAQ,GAAG,wBAAwB,GAAG,gBAAgB;UACnF8K,MAAI,CAAC/V,eAAe,CAAC8X,YAAY,GAAG7M,QAAQ,GAAG3K,IAAI,SAASyV,MAAI,CAAC1T,qBAAqB,CAAC0V,aAAa,CAAChC,MAAI,CAACra,UAAU,CAACsO,YAAY,EAAEI,2BAA2B,CAAC;UAC/J2L,MAAI,CAAC/V,eAAe,CAACM,IAAI,GAAGjJ,QAAQ,CAAC0e,MAAI,CAAC/V,eAAe,CAAC8X,YAAY,CAAC;UACvE/B,MAAI,CAAC9R,iBAAiB,GAAG,IAAI;UAC7B;QACF;UACE;MACJ;IAAC;EACH;EAEAjJ,WAAWA,CAAA;IACT,IAAI,CAACqC,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;EAChD;EAEAmD,qBAAqBA,CAAC2F,KAAU;IAC9B,MAAM7F,IAAI,GAAG6F,KAAK,CAAC7F,IAAI;IACvB,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAAC2D,iBAAiB,GAAG,KAAK;MAC9B;IACF;IACA,IAAI,CAACjE,eAAe,CAACM,IAAI,GAAGA,IAAI;IAChC,MAAM0X,MAAM,GAAGV,MAAM,CAACW,mBAAmB,CAAC,IAAI,CAACjb,SAAS,CAACwO,UAAU,CAACoH,UAAU,CAAC;IAE/E,IAAI3V,EAAE,GAAGqK,SAAS;IAClB,QAAQ,IAAI,CAACtH,eAAe,CAACuS,MAAM;MACjC,KAAKzb,WAAW,CAAC8gB,MAAM;QACrB;UACE,MAAME,YAAY,GAAGzgB,QAAQ,CAAC,IAAI,CAAC2I,eAAe,CAAC8X,YAAY,CAAC;UAChE7a,EAAE,GAAG6a,YAAY,CAAC/f,cAAc,CAAC;UACjC,MAAMmgB,YAAY,GAAG,IAAI,CAACnc,MAAM,CAAC8B,GAAG,CAAC4N,UAAU,CAACxO,EAAE,CAAC;UACnD,IAAIib,YAAY,EAAE;YAChBF,MAAM,CAACzG,OAAO,CAAC/E,KAAK,IAAG;cACrB,IAAI2L,UAAU,GAAG7X,IAAI,CAACkM,KAAK,CAAC;cAE5B,IAAI2L,UAAU,IAAI,IAAI,CAACnY,eAAe,CAAC8X,YAAY,CAACtL,KAAK,CAAC,EAAE;gBAC1D,MAAM4L,SAAS,GAAG,IAAI,CAACpb,SAAS,CAACwO,UAAU,CAACoH,UAAU,CAACpG,KAAK,CAAC,CAACwK,IAAI;gBAClE,MAAMqB,WAAW,GAAG,IAAI,CAACrb,SAAS,CAACwO,UAAU,CAACoH,UAAU,CAACpG,KAAK,CAAC,CAAC8L,MAAM;gBAEtEH,UAAU,GAAGA,UAAU,IAAI,IAAI,GAAGxgB,eAAe,CAACygB,SAAS,EAAEC,WAAW,CAAC,GAAGF,UAAU;gBAEtF;gBACA,IAAI,IAAI,CAACpc,MAAM,CAAC8B,GAAG,CAAC0a,YAAY,CAAC/L,KAAK,CAAC,EAAE;kBACvC0L,YAAY,CAACM,YAAY,CAAChM,KAAK,EAAE2L,UAAU,CAAC;gBAC9C,CAAC,MAAM;kBACLD,YAAY,CAAC5X,IAAI,CAACkM,KAAK,CAAC,GAAG2L,UAAU;gBACvC;cACF;YACF,CAAC,CAAC;YAEF;YACA,IAAI,CAACvD,uBAAuB,EAAEE,YAAY,CAAC,CAACoD,YAAY,CAAC5X,IAAI,CAAC,CAAC;YAE/D,IAAI,CAACoC,oBAAoB,CAAC+V,gBAAgB,CAAC,CAAC,IAAI5gB,eAAe,CAACoF,EAAE,EAAE6a,YAAY,EAAEI,YAAY,CAAC5X,IAAI,CAAC,CAAC,CAAC;YACtG,IAAI,CAACoY,cAAc,CAACR,YAAY,EAAE/R,KAAK,CAACwS,QAAQ,CAAC;YACjD,IAAI,CAAC5c,MAAM,CAAC8B,GAAG,CAAC+a,yBAAyB,CAAC,OAAO,CAAC;UACpD;QACF;QACA;MACF,KAAK9hB,WAAW,CAAC+gB,MAAM;QACrB;UACE5a,EAAE,GAAG,IAAI,CAAC0G,cAAc,GAAG,IAAI,CAACC,QAAQ,CAACiV,QAAQ,EAAE;UACnD,MAAMC,UAAU,GAAG,EAAE;UACrBA,UAAU,CAAC/gB,cAAc,CAAC,GAAGkF,EAAE;UAE/B+a,MAAM,CAACzG,OAAO,CAAC/E,KAAK,IAAG;YACrB,MAAM4L,SAAS,GAAG,IAAI,CAACpb,SAAS,CAACwO,UAAU,CAACoH,UAAU,CAACpG,KAAK,CAAC,CAACwK,IAAI;YAClE,MAAMqB,WAAW,GAAG,IAAI,CAACrb,SAAS,CAACwO,UAAU,CAACoH,UAAU,CAACpG,KAAK,CAAC,CAAC8L,MAAM;YACtE,MAAMH,UAAU,GAAI7X,IAAI,CAACkM,KAAK,CAAC,KAAKlF,SAAS,IAAIhH,IAAI,CAACkM,KAAK,CAAC,KAAK,IAAI,GAAIlM,IAAI,CAACkM,KAAK,CAAC,GAAG7U,eAAe,CAACygB,SAAS,EAAEC,WAAW,CAAC;YAE9H;YACAS,UAAU,CAACtM,KAAK,CAAC,GAAG2L,UAAU;UAChC,CAAC,CAAC;UAEF;UACA,IAAI,CAACvD,uBAAuB,EAAEE,YAAY,CAAC,CAACgE,UAAU,CAAC,CAAC;UAExD,IAAI,CAACpW,oBAAoB,CAACqW,QAAQ,CAAC9b,EAAE,EAAE6b,UAAU,CAAC;UAElD,IAAI,CAACxZ,UAAU,CAAC4K,IAAI,CAAC4O,UAAU,CAAC;UAChC,IAAI,CAAC/c,MAAM,CAAC8B,GAAG,CAACmb,gBAAgB,CAAC;YAAEC,GAAG,EAAE,CAACH,UAAU;UAAC,CAAE,CAAC;UACvD,IAAI,CAAC/c,MAAM,CAAC8B,GAAG,CAAC+a,yBAAyB,CAAC,OAAO,CAAC;UAElD,IAAI,CAACF,cAAc,CAAC,IAAI,CAAC3c,MAAM,CAAC8B,GAAG,CAAC4N,UAAU,CAACxO,EAAE,CAAC,EAAEkJ,KAAK,CAACwS,QAAQ,CAAC;UACnE,IAAI,CAAC3V,aAAa,CAACkW,eAAe,CAAC,IAAI,CAACnd,MAAM,CAAC8B,GAAG,EAAEZ,EAAE,CAAC;UAEvD,IAAI,CAAC2G,QAAQ,EAAE;QACjB;QACA;IACJ;IAEA,IAAI,CAACZ,aAAa,CAACmW,0BAA0B,CAAC,IAAI,CAACpd,MAAM,CAAC8B,GAAG,CAACwN,SAAS,EAAE,EAAEpO,EAAE,CAAC;IAC9E,IAAI,CAACgH,iBAAiB,GAAG,KAAK;EAChC;EAEAmV,mBAAmBA,CAAA;IACjB,IAAI,CAAC/W,qBAAqB,CAACgX,mBAAmB,CAAC5P,IAAI,CAAC,IAAI,CAACzJ,eAAe,CAAC8X,YAAY,CAAC/f,cAAc,CAAC,CAAC;EACxG;EAEA;EACA6C,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACgL,kBAAkB,EAC1B;IAEF,MAAM0T,KAAK,GAAGA,CAAA,KAAK;MACjB,IAAI,CAACnd,mBAAmB,GAAGmL,SAAS;MACpC,IAAI,CAACnE,WAAW,CAACmW,KAAK,EAAE;MACxB,IAAI,CAAC/M,wBAAwB,EAAE;MAC/B,IAAI,CAAC4B,cAAc,CAAC,IAAI,CAACvE,YAAY,EAAE,IAAI,CAAC;IAC9C,CAAC;IAED,IAAI,IAAI,CAACjO,iBAAiB,EAAE,IAAI,IAAI,CAACE,UAAU,EAAE,EAAE;MACjD,IAAI,CAACqH,yBAAyB,CAACkT,UAAU,CAAC,iBAAiB,EACzD;QACEC,MAAM,EAAE,iBAAiB;QAAEtG,OAAO,EAAE,kEAAkE;QAAEuG,YAAY,EAAE,SAAS;QAC/HiD,SAAS,EAAEA,CAAA,KAAK;UACd,IAAI,CAAC7W,oBAAoB,CAAC0P,mBAAmB,EAAE;UAC/CkH,KAAK,EAAE;QACT;OACD,CAAC;IACN,CAAC,MACI;MACHA,KAAK,EAAE;IACT;EACF;EAEAzd,UAAUA,CAAA;IACR,OAAO,IAAI,CAAC6G,oBAAoB,CAACmT,kBAAkB,EAAE;EACvD;EAEA/Z,SAASA,CAAA;IACP,OAAO,IAAI,CAACsB,aAAa,EAAEf,MAAM,GAAG,CAAC;EACvC;EAEAV,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC+G,oBAAoB,CAAC7G,UAAU,EAAE;EAC/C;EAEAkD,aAAaA,CAACoH,KAAK;IACjB,IAAI,CAAC9D,qBAAqB,CAACmX,iBAAiB,CAACrT,KAAK,EAAEiN,MAAM,EAAE9C,KAAK,EAAEnK,KAAK,CAACsT,QAAQ,EAAE,IAAI,CAAC1d,MAAM,CAAC8B,GAAG,CAAC;EACrG;EAEA6a,cAAcA,CAACtE,OAAiB,EAAEuE,QAAkB;IAClD,IAAIA,QAAQ,EAAE;MACZ,MAAMe,eAAe,GAAGtF,OAAO,CAAC9T,IAAI,EAAEqZ,QAAQ,EAAEC,WAAW;MAC3D,IAAI,CAACF,eAAe,IAClBA,eAAe,CAAChV,MAAM,CAACuE,CAAC,IAAI,CAAC0P,QAAQ,CAAChB,QAAQ,CAAC1O,CAAC,CAAC,CAAC,CAAC4Q,MAAM,CAAClB,QAAQ,CAACjU,MAAM,CAACuE,CAAC,IAAI,CAACyQ,eAAe,CAAC/B,QAAQ,CAAC1O,CAAC,CAAC,CAAC,CAAC,CAAC5M,MAAM,KAAK,CAAC,EAAE;QAC5H,IAAI,CAACqG,oBAAoB,CAACoX,gBAAgB,CAAC1F,OAAO,CAACnX,EAAE,EAAE0b,QAAQ,CAAC;QAChEvE,OAAO,CAAC9T,IAAI,CAACqZ,QAAQ,GAAG,IAAI,CAACtX,qBAAqB,CAAC0X,iBAAiB,CAAC3F,OAAO,CAAC9T,IAAI,CAACqZ,QAAQ,EAAEhB,QAAQ,CAAC;QACrGvE,OAAO,CAAC4F,OAAO,CAAC5F,OAAO,CAAC9T,IAAI,CAAC;MAC/B;IACF;EACF;EAEAoB,kBAAkBA,CAACyE,KAAc;IAC/B,IAAI,CAACjC,aAAa,GAAGiC,KAAK;EAC5B;EAEArL,WAAWA,CAAA;IACT,IAAI,CAACoJ,aAAa,GAAG,IAAI;EAC3B;EAEAiH,SAASA,CAAC7K,IAAS;IACjB,MAAMrD,EAAE,GAAGqD,IAAI,CAACvI,cAAc,CAAC;IAC/B,MAAMqc,OAAO,GAAG,IAAI,CAACrY,MAAM,CAAC8B,GAAG,CAAC4N,UAAU,CAACxO,EAAE,CAAC;IAC9C,IAAI,CAACyF,oBAAoB,CAACuX,WAAW,CAAChd,EAAE,EAAEqD,IAAI,EAAE8T,OAAO,CAACqF,QAAQ,CAAC;IACjE,IAAI,CAAC1d,MAAM,CAAC8B,GAAG,CAACmb,gBAAgB,CAAC;MAAEkB,MAAM,EAAE,CAAC5Z,IAAI;IAAC,CAAE,CAAC;EACtD;EAEAc,iBAAiBA,CAAA;IACf,IAAI,CAAC+M,cAAc,CAAC,IAAI,CAACvE,YAAY,EAAE,IAAI,CAAC;EAC9C;EAEAxO,UAAUA,CAAC+e,IAA6B;IACtC,IAAI,IAAI,CAACje,YAAY,KAAKhF,uBAAuB,CAAC+O,KAAK,IAAIkU,IAAI,KAAKjjB,uBAAuB,CAAC+O,KAAK,EAC/F,IAAI,CAAC1H,YAAY,GAAG,IAAI,CAACxC,MAAM,EAAE8B,GAAG,GAAG,IAAI,CAACmF,aAAa,CAAC2R,UAAU,CAAC,IAAI,CAAC5Y,MAAM,CAAC8B,GAAG,CAAC,GAAG,IAAI,CAACyB,UAAU;IAEzG,IAAI,CAACpD,YAAY,GAAGie,IAAI;EAC1B;EAEM1K,eAAeA,CAAA;IAAA,IAAA2K,MAAA;IAAA,OAAA/S,iBAAA;MACnB,MAAMgT,UAAU,GAAGD,MAAI,CAAC1e,UAAU,CAACyB,iBAAiB,GAAGid,MAAI,CAACpd,SAAS,CAACsd,SAAS,CAAC1M,IAAI,CAACuM,IAAI,IAAIA,IAAI,CAACI,MAAM,KAAKH,MAAI,CAAC1e,UAAU,CAACyB,iBAAiB,CAAC,EAAEqd,aAAa,GAAG,MAAM;MAEvK,MAAMC,SAAS,GAAGJ,UAAU,KAAK,MAAM,GAAG,MAAM,SAASD,MAAI,CAAC9W,aAAa,CAACoX,UAAU,CAACL,UAAU,EAAEM,IAAI,EAAE,EAAE9B,QAAQ,EAAE,CAAC;MACtH,IAAIuB,MAAI,CAAC1e,UAAU,CAACiU,kBAAkB,CAACiL,UAAU,KAAKH,SAAS,EAAE;QAC/D;MACF;MAEAL,MAAI,CAAChY,mBAAmB,CAAC8P,WAAW,CAAC,+KAA+K,CAAC;IAAC;EACxN;EAEA5T,qBAAqBA,CAAC6F,MAAuC;IAC3D,IAAI,CAACA,MAAM,CAAC0W,QAAQ,IAAI,CAAC1W,MAAM,CAAC0W,QAAQ,CAAC9iB,cAAc,CAAC,IAAI,CAAC,IAAI,CAACgE,MAAM,CAAC8B,GAAG,CAAC4N,UAAU,CAACtH,MAAM,CAAC0W,QAAQ,CAAC9iB,cAAc,CAAC,CAAC,EAAE;MACxH,IAAI,CAACqK,mBAAmB,CAACuH,SAAS,CAAC,OAAO,EAAE,0DAA0D,CAAC;MACvGmR,OAAO,CAACxM,KAAK,CAAC,4BAA4BnK,MAAM,CAAC0W,QAAQ,GAAG1W,MAAM,CAAC0W,QAAQ,CAAC9iB,cAAc,CAAC,GAAG,WAAW,uBAAuB,CAAC;MACjI;IACF;IAEA,IAAI,CAAC2K,oBAAoB,CAAC+V,gBAAgB,CAAC,CAAC,IAAI5gB,eAAe,CAACsM,MAAM,CAAC0W,QAAQ,CAAC9iB,cAAc,CAAC,EAAEoM,MAAM,CAAC0W,QAAQ,EAAE1W,MAAM,CAAC4W,OAAO,CAAC,CAAC,CAAC;IACnI,IAAI,CAAChf,MAAM,CAAC8B,GAAG,CAACmb,gBAAgB,CAAC;MAAEgC,MAAM,EAAE,CAAC7W,MAAM,CAAC4W,OAAO;IAAC,CAAE,CAAC;IAC9D,IAAI,CAAChf,MAAM,CAAC8B,GAAG,CAAC+a,yBAAyB,CAAC,OAAO,CAAC;EACpD;EAEAlJ,kBAAkBA,CAAA;IAChB,MAAMuL,MAAM,GAAG,IAAI,CAACvf,UAAU,EAAEiU,kBAAkB;IAClD,IAAI,CAACsL,MAAM,EAAE;IAEb,MAAM7K,OAAO,GAAG;MACd8K,SAAS,EAAE,IAAI,CAACC,eAAe,CAACF,MAAM,CAACG,YAAY,CAAC;MACpDC,OAAO,EAAE,IAAI,CAACF,eAAe,CAACF,MAAM,CAACK,UAAU,CAAC;MAChDC,UAAU,EAAE,IAAI,CAACJ,eAAe,CAACF,MAAM,CAACO,aAAa,CAAC;MACtDC,aAAa,EAAE,IAAI,CAACN,eAAe,CAACF,MAAM,CAACS,gBAAgB;KAC5D;IAED,IAAI,CAACld,eAAe,GAAG,IAAIhF,iBAAiB,CAAC;MAC3CmiB,eAAe,EAAE,IAAI,CAAC3e,SAAS,CAACwO,UAAU,CAACoH,UAAU;MACrDjD,kBAAkB,EAAEsL,MAAM;MAC1Bxf,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBmgB,QAAQ,EAAExL,OAAO,CAAC8K,SAAS,EAAE5C,MAAM,EAAEtB,IAAI,KAAKzd,gBAAgB,CAACsiB,QAAQ,IAAIzL,OAAO,CAACiL,OAAO,EAAE/C,MAAM,EAAEtB,IAAI,KAAKzd,gBAAgB,CAACsiB,QAAQ;MACtIC,aAAa,EAAE1L,OAAO,CAAC8K,SAAS,EAAEa,eAAe,IAAI,KAAK;MAC1DC,SAAS,EAAE,CAACziB,gBAAgB,CAACsiB,QAAQ,EAAEtiB,gBAAgB,CAAC0iB,IAAI,CAAC,CAACtE,QAAQ,CAACvH,OAAO,CAAC8K,SAAS,EAAE5C,MAAM,EAAEtB,IAAI,CAAC,GAAG5G,OAAO,CAAC8K,SAAS,EAAE5C,MAAM,EAAEtB,IAAI,GAAG1P,SAAS;MAAE;MACvJ4U,OAAO,EAAE,CAAC3iB,gBAAgB,CAACsiB,QAAQ,EAAEtiB,gBAAgB,CAAC0iB,IAAI,CAAC,CAACtE,QAAQ,CAACvH,OAAO,CAACiL,OAAO,EAAE/C,MAAM,EAAEtB,IAAI,CAAC,GAAG5G,OAAO,CAACiL,OAAO,EAAE/C,MAAM,EAAEtB,IAAI,GAAG1P,SAAS;MAAE;MACjJ6U,gBAAgB,EAAE,CAAClB,MAAM,CAACK,UAAU,IAAIlL,OAAO,CAACiL,OAAO,EAAEU,eAAe,KAAK,KAAK;MAClFK,gBAAgB,EAAE,CAACnB,MAAM,CAACO,aAAa,IAAIpL,OAAO,CAACmL,UAAU,EAAEQ,eAAe,KAAK,KAAK;MACxFM,qBAAqB,EAAE,CAACpB,MAAM,CAACS,gBAAgB,IAAItL,OAAO,CAACqL,aAAa,EAAEM,eAAe,KAAK;KAC/F,CAAC;EACJ;EAEQZ,eAAeA,CAACmB,UAAkB;IACxC,OAAO,IAAI,CAAC5gB,UAAU,EAAEsO,YAAY,EAAEI,2BAA2B,EAAEwD,IAAI,CAAC3E,CAAC,IAAIA,CAAC,CAACmK,MAAM,KAAKkJ,UAAU,CAAC;EACvG;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACxY,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACnJ,iBAAiB,EAAE;EAC1B;EACAU,eAAeA,CAAA;IACb,IAAI,CAACyI,oBAAoB,GAAG,IAAI;IAChCyY,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,SAAS,CAACC,cAAc,EAAE;IACjC,CAAC,EAAE,CAAC,CAAC;EACP;;;uBA5gCW1a,uBAAuB,EAAAtI,EAAA,CAAAijB,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAnjB,EAAA,CAAAijB,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAApjB,EAAA,CAAAijB,iBAAA,CAAAI,EAAA,CAAAC,mBAAA,GAAAtjB,EAAA,CAAAijB,iBAAA,CAAAM,EAAA,CAAAC,qBAAA,GAAAxjB,EAAA,CAAAijB,iBAAA,CAAAQ,EAAA,CAAAC,wBAAA,GAAA1jB,EAAA,CAAAijB,iBAAA,CAAAU,EAAA,CAAAC,iBAAA,GAAA5jB,EAAA,CAAAijB,iBAAA,CAAAY,EAAA,CAAAC,iBAAA,GAAA9jB,EAAA,CAAAijB,iBAAA,CAAAc,EAAA,CAAAC,mBAAA,GAAAhkB,EAAA,CAAAijB,iBAAA,CAAAgB,EAAA,CAAAC,oBAAA,GAAAlkB,EAAA,CAAAijB,iBAAA,CAAAkB,EAAA,CAAAC,eAAA,GAAApkB,EAAA,CAAAijB,iBAAA,CAAAoB,GAAA,CAAAC,kBAAA,GAAAtkB,EAAA,CAAAijB,iBAAA,CAAAsB,GAAA,CAAAC,gCAAA,GAAAxkB,EAAA,CAAAijB,iBAAA,CAAAwB,GAAA,CAAAC,YAAA,GAAA1kB,EAAA,CAAAijB,iBAAA,CAAA0B,GAAA,CAAAC,kBAAA,GAAA5kB,EAAA,CAAAijB,iBAAA,CAAA4B,GAAA,CAAAC,aAAA,GAAA9kB,EAAA,CAAAijB,iBAAA,CAAA8B,GAAA,CAAAC,oBAAA,GAAAhlB,EAAA,CAAAijB,iBAAA,CAAAgC,GAAA,CAAAC,yBAAA,GAAAllB,EAAA,CAAAijB,iBAAA,CAAAkC,GAAA,CAAAC,WAAA,GAAAplB,EAAA,CAAAijB,iBAAA,CAAAoC,GAAA,CAAAC,4BAAA,GAAAtlB,EAAA,CAAAijB,iBAAA,CAAAsC,GAAA,CAAA9mB,2BAAA,GAAAuB,EAAA,CAAAijB,iBAAA,CAAAuC,GAAA,CAAAC,aAAA,GAAAzlB,EAAA,CAAAijB,iBAAA,CAAAyC,GAAA,CAAAC,kBAAA,GAAA3lB,EAAA,CAAAijB,iBAAA,CAAA2C,GAAA,CAAAC,sBAAA,GAAA7lB,EAAA,CAAAijB,iBAAA,CAwIxB1lB,kBAAkB;IAAA;EAAA;;;YAxIjB+K,uBAAuB;MAAAwd,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;iCACkBrmB,uBAAuB;;;;;;;;;;;;;UADhEI,EAAA,CAAAE,UAAA,oBAAAimB,kDAAA/lB,MAAA;YAAA,OAAA8lB,GAAA,CAAA1Z,aAAA,CAAApM,MAAA,CAAAgmB,MAAA,CAA4B;UAAA,UAAApmB,EAAA,CAAAqmB,eAAA,CAAL,0BAAAC,wDAAAlmB,MAAA;YAAA,OAAvB8lB,GAAA,CAAAtZ,kBAAA,CAAAxM,MAAA,CAA0B;UAAA,UAAAJ,EAAA,CAAAqmB,eAAA,CAAH;;;;uCALvB,CAAC5nB,2BAA2B,CAAC,GAAAuB,EAAA,CAAAumB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAX,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCnFxCjmB,EADF,CAAAC,cAAA,iBAAmH,aACxE;UAwBvCD,EAvBA,CAAAwF,UAAA,IAAAqhB,8CAAA,+BAAuB,IAAAC,8CAAA,cAeO,IAAAC,8CAAA,mCAQA;UAM9B/mB,EAAA,CAAAC,cAAA,qBAA+G;UAO7GD,EANA,CAAAwF,UAAA,IAAAwhB,8CAAA,4BAA+D,IAAAC,8CAAA,4BAMH;UAY9DjnB,EAAA,CAAA6B,YAAA,EAAc;UAEd7B,EAAA,CAAAC,cAAA,cAAkB;UAChBD,EAAA,CAAAwF,UAAA,IAAA0hB,8CAAA,wBAA4B;UAG9BlnB,EAAA,CAAA6B,YAAA,EAAM;UACN7B,EAAA,CAAAwF,UAAA,KAAA2hB,+CAAA,eAAwD;UAoB5DnnB,EADE,CAAA6B,YAAA,EAAM,EACE;UAEV7B,EAAA,CAAAC,cAAA,qBAC2D;UADhDD,EAAA,CAAAwG,gBAAA,2BAAA4gB,qEAAAhnB,MAAA;YAAAJ,EAAA,CAAAK,aAAA,CAAAgnB,GAAA;YAAArnB,EAAA,CAAA2G,kBAAA,CAAAuf,GAAA,CAAA3b,iBAAA,EAAAnK,MAAA,MAAA8lB,GAAA,CAAA3b,iBAAA,GAAAnK,MAAA;YAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;UAAA,EAA+B;UACxCJ,EAAA,CAAAE,UAAA,oBAAAonB,8DAAA;YAAAtnB,EAAA,CAAAK,aAAA,CAAAgnB,GAAA;YAAA,OAAArnB,EAAA,CAAAS,WAAA,CAAUylB,GAAA,CAAAxG,mBAAA,EAAqB;UAAA,EAAC;UAIhC1f,EAHA,CAAAwF,UAAA,KAAA+hB,+CAAA,0BAAgC,KAAAC,+CAAA,oCAGF;UAQhCxnB,EAAA,CAAA6B,YAAA,EAAY;UAGZ7B,EAAA,CAAAC,cAAA,qBACyE;UAD9DD,EAAA,CAAAwG,gBAAA,2BAAAihB,qEAAArnB,MAAA;YAAAJ,EAAA,CAAAK,aAAA,CAAAgnB,GAAA;YAAArnB,EAAA,CAAA2G,kBAAA,CAAAuf,GAAA,CAAA7b,oBAAA,EAAAjK,MAAA,MAAA8lB,GAAA,CAAA7b,oBAAA,GAAAjK,MAAA;YAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;UAAA,EAAkC;UAC3CJ,EAAA,CAAAE,UAAA,oBAAAwnB,8DAAA;YAAA1nB,EAAA,CAAAK,aAAA,CAAAgnB,GAAA;YAAA,OAAArnB,EAAA,CAAAS,WAAA,CAAUylB,GAAA,CAAArD,eAAA,EAAiB;UAAA,EAAC;UAC5B7iB,EAAA,CAAAwF,UAAA,KAAAmiB,+CAAA,0BAAgC;UAGhC3nB,EAAA,CAAAgE,SAAA,uBAAgD;UAClDhE,EAAA,CAAA6B,YAAA,EAAY;UAGZ7B,EAAA,CAAAwF,UAAA,KAAAoiB,+CAAA,gCAAgC;UAOhC5nB,EAAA,CAAAC,cAAA,sCAImD;UAJvBD,EAAA,CAAAwG,gBAAA,2BAAAqhB,sFAAAznB,MAAA;YAAAJ,EAAA,CAAAK,aAAA,CAAAgnB,GAAA;YAAArnB,EAAA,CAAA2G,kBAAA,CAAAuf,GAAA,CAAAhkB,yBAAA,EAAA9B,MAAA,MAAA8lB,GAAA,CAAAhkB,yBAAA,GAAA9B,MAAA;YAAA,OAAAJ,EAAA,CAAAS,WAAA,CAAAL,MAAA;UAAA,EAAuC;UAIjEJ,EAAA,CAAAE,UAAA,uBAAA4nB,kFAAA;YAAA9nB,EAAA,CAAAK,aAAA,CAAAgnB,GAAA;YAAA,OAAArnB,EAAA,CAAAS,WAAA,CAAaylB,GAAA,CAAAzR,cAAA,CAAAyR,GAAA,CAAAhW,YAAA,EAA6B,IAAI,CAAC;UAAA,EAAC;UAAClQ,EAAA,CAAA6B,YAAA,EAA6B;UAEhF7B,EAAA,CAAAwF,UAAA,KAAAuiB,+CAAA,6BAA0B;UAK1B/nB,EAAA,CAAAgE,SAAA,mCAAwE;UAQxEhE,EANA,CAAAwF,UAAA,KAAAwiB,+CAAA,4CAAkC,KAAAC,+CAAA,wBAMR;;;UAlIwCjoB,EAArB,CAAA8B,UAAA,qBAAoB,YAAAokB,GAAA,CAAA/b,aAAA,CAA0B;UAEvFnK,EAAA,CAAAqD,SAAA,GAcC;UAdDrD,EAAA,CAAAmG,aAAA,IAAA+f,GAAA,CAAAlkB,UAAA,UAcC;UACDhC,EAAA,CAAAqD,SAAA,EAMC;UANDrD,EAAA,CAAAmG,aAAA,IAAA+f,GAAA,CAAA5iB,SAAA,IAAA4iB,GAAA,CAAAlkB,UAAA,UAMC;UAEDhC,EAAA,CAAAqD,SAAA,EAIC;UAJDrD,EAAA,CAAAmG,aAAA,IAAA+f,GAAA,CAAA9b,iBAAA,UAIC;UAEYpK,EAAA,CAAAqD,SAAA,EAA4D;UAA5DrD,EAAA,CAAA8B,UAAA,YAAA9B,EAAA,CAAAgD,eAAA,KAAAklB,GAAA,EAAAhC,GAAA,CAAAha,kBAAA,qBAA4D;UACvElM,EAAA,CAAAqD,SAAA,EAIC;UAJDrD,EAAA,CAAAmG,aAAA,IAAA+f,GAAA,CAAA9Z,iBAAA,KAAA8Z,GAAA,CAAAjiB,YAAA,kBAAAiiB,GAAA,CAAAjiB,YAAA,CAAAtB,MAAA,eAIC;UAED3C,EAAA,CAAAqD,SAAA,EAWC;UAXDrD,EAAA,CAAAmG,aAAA,IAAA+f,GAAA,CAAA7Z,cAAA,KAAA6Z,GAAA,CAAA1hB,YAAA,kBAAA0hB,GAAA,CAAA1hB,YAAA,CAAA7B,MAAA,eAWC;UAID3C,EAAA,CAAAqD,SAAA,GAEC;UAFDrD,EAAA,CAAAmG,aAAA,IAAA+f,GAAA,CAAA3jB,eAAA,UAEC;UAEHvC,EAAA,CAAAqD,SAAA,EAkBC;UAlBDrD,EAAA,CAAAmG,aAAA,MAAA+f,GAAA,CAAA3jB,eAAA,IAAA2jB,GAAA,CAAAha,kBAAA,WAkBC;UAIMlM,EAAA,CAAAqD,SAAA,EAA+B;UAA/BrD,EAAA,CAAAiH,gBAAA,YAAAif,GAAA,CAAA3b,iBAAA,CAA+B;UAKxCvK,EAAA,CAAAqD,SAAA,GAMC;UANDrD,EAAA,CAAAmG,aAAA,KAAA+f,GAAA,CAAA3b,iBAAA,WAMC;UAKQvK,EAAA,CAAAqD,SAAA,EAAkC;UAAlCrD,EAAA,CAAAiH,gBAAA,YAAAif,GAAA,CAAA7b,oBAAA,CAAkC;UAKjCrK,EAAA,CAAAqD,SAAA,GAAoB;UAApBrD,EAAA,CAAA8B,UAAA,YAAAokB,GAAA,CAAA1iB,QAAA,CAAoB;UAIhCxD,EAAA,CAAAqD,SAAA,GAKC;UALDrD,EAAA,CAAAmG,aAAA,KAAA+f,GAAA,CAAA1e,mBAAA,WAKC;UAEmExH,EAAA,CAAAqD,SAAA,EAA0B;UAA1BrD,EAAA,CAAAmoB,UAAA,CAAAnoB,EAAA,CAAAooB,eAAA,KAAAC,GAAA,EAA0B;UAAlEroB,EAAA,CAAAiH,gBAAA,YAAAif,GAAA,CAAAhkB,yBAAA,CAAuC;UAGjElC,EAFA,CAAA8B,UAAA,gBAAAokB,GAAA,CAAAnf,SAAA,kBAAAmf,GAAA,CAAAnf,SAAA,CAAAC,SAAA,CAAoC,cAAAkf,GAAA,CAAA5iB,SAAA,CAAwB,aAAA4iB,GAAA,CAAA9e,gBAAA,CAA8B,cAAA8e,GAAA,CAAAxX,mBAAA,CACzD,qBAAAwX,GAAA,CAAArX,0BAAA,CAAgD,wBAAAqX,GAAA,CAAA7e,mBAAA,CACtC;UAG7CrH,EAAA,CAAAqD,SAAA,EAGC;UAHDrD,EAAA,CAAAmG,aAAA,KAAA+f,GAAA,CAAA1b,aAAA,WAGC;UAIDxK,EAAA,CAAAqD,SAAA,GAIC;UAJDrD,EAAA,CAAAmG,aAAA,KAAA+f,GAAA,CAAA1kB,qBAAA,WAIC;UAEDxB,EAAA,CAAAqD,SAAA,EAIC;UAJDrD,EAAA,CAAAmG,aAAA,KAAA+f,GAAA,CAAA/d,aAAA,WAIC;;;qBDhDWxI,WAAW,EAAA2oB,GAAA,CAAAC,KAAA,EAAAC,GAAA,CAAAC,aAAA,EAAEhpB,OAAO,EAAED,oBAAoB,EAAED,sBAAsB,EAAED,wBAAwB,EAAED,eAAe,EAAAqpB,GAAA,CAAAC,SAAA,EAAAD,GAAA,CAAAE,YAAA,EAAElpB,OAAO,EAAEN,2BAA2B,EAAED,oBAAoB,EAAEjC,YAAY,EAAA2rB,GAAA,CAAAC,aAAA,EAAE5pB,aAAa,EAAA6pB,GAAA,CAAAC,OAAA,EAAEnrB,YAAY,EAAEoB,wBAAwB,EAAED,qBAAqB,EAAED,4BAA4B,EAAED,kBAAkB,EAAED,2BAA2B,EAAED,gCAAgC,EAAED,aAAa,EAAAsqB,GAAA,CAAAC,OAAA,EAAExqB,YAAY,EAAEkB,uBAAuB,EAAEG,kBAAkB;MAAAopB,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}