{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst IdentityIdStorageKeys = {\n  identityId: 'identityId'\n};\nexport { IdentityIdStorageKeys };", "map": {"version": 3, "names": ["IdentityIdStorageKeys", "identityId"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/credentialsProvider/types.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst IdentityIdStorageKeys = {\n    identityId: 'identityId',\n};\n\nexport { IdentityIdStorageKeys };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,qBAAqB,GAAG;EAC1BC,UAAU,EAAE;AAChB,CAAC;AAED,SAASD,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}