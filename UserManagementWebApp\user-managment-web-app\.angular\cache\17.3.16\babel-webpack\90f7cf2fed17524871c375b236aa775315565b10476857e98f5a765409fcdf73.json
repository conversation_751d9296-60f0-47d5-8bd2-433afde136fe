{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ConsoleLogger } from '@aws-amplify/core';\nimport { assertIdentityPoolIdConfig } from '@aws-amplify/core/internals/utils';\nimport { getAuthStorageKeys } from '../tokenProvider/TokenStore.mjs';\nimport { IdentityIdStorageKeys } from './types.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('DefaultIdentityIdStore');\nclass DefaultIdentityIdStore {\n  setAuthConfig(authConfigParam) {\n    assertIdentityPoolIdConfig(authConfigParam.Cognito);\n    this.authConfig = authConfigParam;\n    this._authKeys = createKeysForAuthStorage('Cognito', authConfigParam.Cognito.identityPoolId);\n  }\n  constructor(keyValueStorage) {\n    this._authKeys = {};\n    this._hasGuestIdentityId = false;\n    this.keyValueStorage = keyValueStorage;\n  }\n  loadIdentityId() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      assertIdentityPoolIdConfig(_this.authConfig?.Cognito);\n      try {\n        if (_this._primaryIdentityId) {\n          return {\n            id: _this._primaryIdentityId,\n            type: 'primary'\n          };\n        } else {\n          const storedIdentityId = yield _this.keyValueStorage.getItem(_this._authKeys.identityId);\n          if (storedIdentityId) {\n            _this._hasGuestIdentityId = true;\n            return {\n              id: storedIdentityId,\n              type: 'guest'\n            };\n          }\n          return null;\n        }\n      } catch (err) {\n        logger.log('Error getting stored IdentityId.', err);\n        return null;\n      }\n    })();\n  }\n  storeIdentityId(identity) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      assertIdentityPoolIdConfig(_this2.authConfig?.Cognito);\n      if (identity.type === 'guest') {\n        _this2.keyValueStorage.setItem(_this2._authKeys.identityId, identity.id);\n        // Clear in-memory storage of primary identityId\n        _this2._primaryIdentityId = undefined;\n        _this2._hasGuestIdentityId = true;\n      } else {\n        _this2._primaryIdentityId = identity.id;\n        // Clear locally stored guest id\n        if (_this2._hasGuestIdentityId) {\n          _this2.keyValueStorage.removeItem(_this2._authKeys.identityId);\n          _this2._hasGuestIdentityId = false;\n        }\n      }\n    })();\n  }\n  clearIdentityId() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3._primaryIdentityId = undefined;\n      yield _this3.keyValueStorage.removeItem(_this3._authKeys.identityId);\n    })();\n  }\n}\nconst createKeysForAuthStorage = (provider, identifier) => {\n  return getAuthStorageKeys(IdentityIdStorageKeys)(`com.amplify.${provider}`, identifier);\n};\nexport { DefaultIdentityIdStore };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assertIdentityPoolIdConfig", "getAuthStorageKeys", "IdentityIdStorageKeys", "logger", "DefaultIdentityIdStore", "setAuthConfig", "authConfigParam", "Cognito", "authConfig", "_auth<PERSON><PERSON>s", "createKeysForAuthStorage", "identityPoolId", "constructor", "keyValueStorage", "_hasGuestIdentityId", "loadIdentityId", "_this", "_asyncToGenerator", "_primaryIdentityId", "id", "type", "storedIdentityId", "getItem", "identityId", "err", "log", "storeIdentityId", "identity", "_this2", "setItem", "undefined", "removeItem", "clearIdentityId", "_this3", "provider", "identifier"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/credentialsProvider/IdentityIdStore.mjs"], "sourcesContent": ["import { ConsoleLogger } from '@aws-amplify/core';\nimport { assertIdentityPoolIdConfig } from '@aws-amplify/core/internals/utils';\nimport { getAuthStorageKeys } from '../tokenProvider/TokenStore.mjs';\nimport { IdentityIdStorageKeys } from './types.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst logger = new ConsoleLogger('DefaultIdentityIdStore');\nclass DefaultIdentityIdStore {\n    setAuthConfig(authConfigParam) {\n        assertIdentityPoolIdConfig(authConfigParam.Cognito);\n        this.authConfig = authConfigParam;\n        this._authKeys = createKeysForAuthStorage('Cognito', authConfigParam.Cognito.identityPoolId);\n    }\n    constructor(keyValueStorage) {\n        this._authKeys = {};\n        this._hasGuestIdentityId = false;\n        this.keyValueStorage = keyValueStorage;\n    }\n    async loadIdentityId() {\n        assertIdentityPoolIdConfig(this.authConfig?.Cognito);\n        try {\n            if (this._primaryIdentityId) {\n                return {\n                    id: this._primaryIdentityId,\n                    type: 'primary',\n                };\n            }\n            else {\n                const storedIdentityId = await this.keyValueStorage.getItem(this._authKeys.identityId);\n                if (storedIdentityId) {\n                    this._hasGuestIdentityId = true;\n                    return {\n                        id: storedIdentityId,\n                        type: 'guest',\n                    };\n                }\n                return null;\n            }\n        }\n        catch (err) {\n            logger.log('Error getting stored IdentityId.', err);\n            return null;\n        }\n    }\n    async storeIdentityId(identity) {\n        assertIdentityPoolIdConfig(this.authConfig?.Cognito);\n        if (identity.type === 'guest') {\n            this.keyValueStorage.setItem(this._authKeys.identityId, identity.id);\n            // Clear in-memory storage of primary identityId\n            this._primaryIdentityId = undefined;\n            this._hasGuestIdentityId = true;\n        }\n        else {\n            this._primaryIdentityId = identity.id;\n            // Clear locally stored guest id\n            if (this._hasGuestIdentityId) {\n                this.keyValueStorage.removeItem(this._authKeys.identityId);\n                this._hasGuestIdentityId = false;\n            }\n        }\n    }\n    async clearIdentityId() {\n        this._primaryIdentityId = undefined;\n        await this.keyValueStorage.removeItem(this._authKeys.identityId);\n    }\n}\nconst createKeysForAuthStorage = (provider, identifier) => {\n    return getAuthStorageKeys(IdentityIdStorageKeys)(`com.amplify.${provider}`, identifier);\n};\n\nexport { DefaultIdentityIdStore };\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,mBAAmB;AACjD,SAASC,0BAA0B,QAAQ,mCAAmC;AAC9E,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,qBAAqB,QAAQ,aAAa;;AAEnD;AACA;AACA,MAAMC,MAAM,GAAG,IAAIJ,aAAa,CAAC,wBAAwB,CAAC;AAC1D,MAAMK,sBAAsB,CAAC;EACzBC,aAAaA,CAACC,eAAe,EAAE;IAC3BN,0BAA0B,CAACM,eAAe,CAACC,OAAO,CAAC;IACnD,IAAI,CAACC,UAAU,GAAGF,eAAe;IACjC,IAAI,CAACG,SAAS,GAAGC,wBAAwB,CAAC,SAAS,EAAEJ,eAAe,CAACC,OAAO,CAACI,cAAc,CAAC;EAChG;EACAC,WAAWA,CAACC,eAAe,EAAE;IACzB,IAAI,CAACJ,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACK,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACD,eAAe,GAAGA,eAAe;EAC1C;EACME,cAAcA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACnBjB,0BAA0B,CAACgB,KAAI,CAACR,UAAU,EAAED,OAAO,CAAC;MACpD,IAAI;QACA,IAAIS,KAAI,CAACE,kBAAkB,EAAE;UACzB,OAAO;YACHC,EAAE,EAAEH,KAAI,CAACE,kBAAkB;YAC3BE,IAAI,EAAE;UACV,CAAC;QACL,CAAC,MACI;UACD,MAAMC,gBAAgB,SAASL,KAAI,CAACH,eAAe,CAACS,OAAO,CAACN,KAAI,CAACP,SAAS,CAACc,UAAU,CAAC;UACtF,IAAIF,gBAAgB,EAAE;YAClBL,KAAI,CAACF,mBAAmB,GAAG,IAAI;YAC/B,OAAO;cACHK,EAAE,EAAEE,gBAAgB;cACpBD,IAAI,EAAE;YACV,CAAC;UACL;UACA,OAAO,IAAI;QACf;MACJ,CAAC,CACD,OAAOI,GAAG,EAAE;QACRrB,MAAM,CAACsB,GAAG,CAAC,kCAAkC,EAAED,GAAG,CAAC;QACnD,OAAO,IAAI;MACf;IAAC;EACL;EACME,eAAeA,CAACC,QAAQ,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAX,iBAAA;MAC5BjB,0BAA0B,CAAC4B,MAAI,CAACpB,UAAU,EAAED,OAAO,CAAC;MACpD,IAAIoB,QAAQ,CAACP,IAAI,KAAK,OAAO,EAAE;QAC3BQ,MAAI,CAACf,eAAe,CAACgB,OAAO,CAACD,MAAI,CAACnB,SAAS,CAACc,UAAU,EAAEI,QAAQ,CAACR,EAAE,CAAC;QACpE;QACAS,MAAI,CAACV,kBAAkB,GAAGY,SAAS;QACnCF,MAAI,CAACd,mBAAmB,GAAG,IAAI;MACnC,CAAC,MACI;QACDc,MAAI,CAACV,kBAAkB,GAAGS,QAAQ,CAACR,EAAE;QACrC;QACA,IAAIS,MAAI,CAACd,mBAAmB,EAAE;UAC1Bc,MAAI,CAACf,eAAe,CAACkB,UAAU,CAACH,MAAI,CAACnB,SAAS,CAACc,UAAU,CAAC;UAC1DK,MAAI,CAACd,mBAAmB,GAAG,KAAK;QACpC;MACJ;IAAC;EACL;EACMkB,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAhB,iBAAA;MACpBgB,MAAI,CAACf,kBAAkB,GAAGY,SAAS;MACnC,MAAMG,MAAI,CAACpB,eAAe,CAACkB,UAAU,CAACE,MAAI,CAACxB,SAAS,CAACc,UAAU,CAAC;IAAC;EACrE;AACJ;AACA,MAAMb,wBAAwB,GAAGA,CAACwB,QAAQ,EAAEC,UAAU,KAAK;EACvD,OAAOlC,kBAAkB,CAACC,qBAAqB,CAAC,CAAC,eAAegC,QAAQ,EAAE,EAAEC,UAAU,CAAC;AAC3F,CAAC;AAED,SAAS/B,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}