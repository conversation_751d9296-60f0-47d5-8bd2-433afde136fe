{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AuthAction } from '@aws-amplify/core/internals/utils';\nimport { getUserContextData } from '../../../providers/cognito/utils/userContextData.mjs';\nimport { getAuthenticationHelper } from '../../../providers/cognito/utils/srp/getAuthenticationHelper.mjs';\nimport '../../../providers/cognito/utils/srp/constants.mjs';\nimport '@aws-crypto/sha256-js';\nimport { createInitiateAuthClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createInitiateAuthClient.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../../../providers/cognito/types/errors.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { handlePasswordVerifierChallenge } from '../../../providers/cognito/utils/handlePasswordVerifierChallenge.mjs';\nimport { retryOnResourceNotFoundException } from '../../../providers/cognito/utils/retryOnResourceNotFoundException.mjs';\nimport { setActiveSignInUsername } from '../../../providers/cognito/utils/setActiveSignInUsername.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Handles the Password SRP (Secure Remote Password) authentication flow.\n * This function can be used with both USER_SRP_AUTH and USER_AUTH flows.\n *\n * @param {Object} params - The parameters for the Password SRP authentication\n * @param {string} params.username - The username for authentication\n * @param {string} params.password - The user's password\n * @param {ClientMetadata} [params.clientMetadata] - Optional metadata to be sent with auth requests\n * @param {CognitoUserPoolConfig} params.config - Cognito User Pool configuration\n * @param {AuthTokenOrchestrator} params.tokenOrchestrator - Token orchestrator for managing auth tokens\n * @param {AuthFlowType} params.authFlow - The type of authentication flow ('USER_SRP_AUTH' or 'USER_AUTH')\n * @param {AuthFactorType} [params.preferredChallenge] - Optional preferred challenge type when using USER_AUTH flow\n *\n * @returns {Promise<RespondToAuthChallengeCommandOutput>} The authentication response\n */\nfunction handlePasswordSRP(_x) {\n  return _handlePasswordSRP.apply(this, arguments);\n}\nfunction _handlePasswordSRP() {\n  _handlePasswordSRP = _asyncToGenerator(function* ({\n    username,\n    password,\n    clientMetadata,\n    config,\n    tokenOrchestrator,\n    authFlow,\n    preferredChallenge\n  }) {\n    const {\n      userPoolId,\n      userPoolClientId,\n      userPoolEndpoint\n    } = config;\n    const userPoolName = userPoolId?.split('_')[1] || '';\n    const authenticationHelper = yield getAuthenticationHelper(userPoolName);\n    const authParameters = {\n      USERNAME: username,\n      SRP_A: authenticationHelper.A.toString(16)\n    };\n    if (authFlow === 'USER_AUTH' && preferredChallenge) {\n      authParameters.PREFERRED_CHALLENGE = preferredChallenge;\n    }\n    const UserContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const jsonReq = {\n      AuthFlow: authFlow,\n      AuthParameters: authParameters,\n      ClientMetadata: clientMetadata,\n      ClientId: userPoolClientId,\n      UserContextData\n    };\n    const initiateAuth = createInitiateAuthClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const resp = yield initiateAuth({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.SignIn)\n    }, jsonReq);\n    const {\n      ChallengeParameters: challengeParameters,\n      Session: session\n    } = resp;\n    const activeUsername = challengeParameters?.USERNAME ?? username;\n    setActiveSignInUsername(activeUsername);\n    if (resp.ChallengeName === 'PASSWORD_VERIFIER') {\n      return retryOnResourceNotFoundException(handlePasswordVerifierChallenge, [password, challengeParameters, clientMetadata, session, authenticationHelper, config, tokenOrchestrator], activeUsername, tokenOrchestrator);\n    }\n    return resp;\n  });\n  return _handlePasswordSRP.apply(this, arguments);\n}\nexport { handlePasswordSRP };", "map": {"version": 3, "names": ["AuthAction", "getUserContextData", "getAuthenticationHelper", "createInitiateAuthClient", "createCognitoUserPoolEndpointResolver", "getRegionFromUserPoolId", "getAuthUserAgentValue", "handlePasswordVerifierChallenge", "retryOnResourceNotFoundException", "setActiveSignInUsername", "handlePasswordSRP", "_x", "_handlePasswordSRP", "apply", "arguments", "_asyncToGenerator", "username", "password", "clientMetadata", "config", "tokenOrchestrator", "authFlow", "preferredChallenge", "userPoolId", "userPoolClientId", "userPoolEndpoint", "userPoolName", "split", "authenticationHelper", "authParameters", "USERNAME", "SRP_A", "A", "toString", "PREFERRED_CHALLENGE", "UserContextData", "jsonReq", "AuthFlow", "AuthParameters", "ClientMetadata", "ClientId", "initiateAuth", "endpointResolver", "endpointOverride", "resp", "region", "userAgentValue", "SignIn", "ChallengeParameters", "challengeParameters", "Session", "session", "activeUsername", "ChallengeName"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/flows/shared/handlePasswordSRP.mjs"], "sourcesContent": ["import { AuthAction } from '@aws-amplify/core/internals/utils';\nimport { getUserContextData } from '../../../providers/cognito/utils/userContextData.mjs';\nimport { getAuthenticationHelper } from '../../../providers/cognito/utils/srp/getAuthenticationHelper.mjs';\nimport '../../../providers/cognito/utils/srp/constants.mjs';\nimport '@aws-crypto/sha256-js';\nimport { createInitiateAuthClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createInitiateAuthClient.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../../../providers/cognito/types/errors.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { handlePasswordVerifierChallenge } from '../../../providers/cognito/utils/handlePasswordVerifierChallenge.mjs';\nimport { retryOnResourceNotFoundException } from '../../../providers/cognito/utils/retryOnResourceNotFoundException.mjs';\nimport { setActiveSignInUsername } from '../../../providers/cognito/utils/setActiveSignInUsername.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Handles the Password SRP (Secure Remote Password) authentication flow.\n * This function can be used with both USER_SRP_AUTH and USER_AUTH flows.\n *\n * @param {Object} params - The parameters for the Password SRP authentication\n * @param {string} params.username - The username for authentication\n * @param {string} params.password - The user's password\n * @param {ClientMetadata} [params.clientMetadata] - Optional metadata to be sent with auth requests\n * @param {CognitoUserPoolConfig} params.config - Cognito User Pool configuration\n * @param {AuthTokenOrchestrator} params.tokenOrchestrator - Token orchestrator for managing auth tokens\n * @param {AuthFlowType} params.authFlow - The type of authentication flow ('USER_SRP_AUTH' or 'USER_AUTH')\n * @param {AuthFactorType} [params.preferredChallenge] - Optional preferred challenge type when using USER_AUTH flow\n *\n * @returns {Promise<RespondToAuthChallengeCommandOutput>} The authentication response\n */\nasync function handlePasswordSRP({ username, password, clientMetadata, config, tokenOrchestrator, authFlow, preferredChallenge, }) {\n    const { userPoolId, userPoolClientId, userPoolEndpoint } = config;\n    const userPoolName = userPoolId?.split('_')[1] || '';\n    const authenticationHelper = await getAuthenticationHelper(userPoolName);\n    const authParameters = {\n        USERNAME: username,\n        SRP_A: authenticationHelper.A.toString(16),\n    };\n    if (authFlow === 'USER_AUTH' && preferredChallenge) {\n        authParameters.PREFERRED_CHALLENGE = preferredChallenge;\n    }\n    const UserContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const jsonReq = {\n        AuthFlow: authFlow,\n        AuthParameters: authParameters,\n        ClientMetadata: clientMetadata,\n        ClientId: userPoolClientId,\n        UserContextData,\n    };\n    const initiateAuth = createInitiateAuthClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const resp = await initiateAuth({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.SignIn),\n    }, jsonReq);\n    const { ChallengeParameters: challengeParameters, Session: session } = resp;\n    const activeUsername = challengeParameters?.USERNAME ?? username;\n    setActiveSignInUsername(activeUsername);\n    if (resp.ChallengeName === 'PASSWORD_VERIFIER') {\n        return retryOnResourceNotFoundException(handlePasswordVerifierChallenge, [\n            password,\n            challengeParameters,\n            clientMetadata,\n            session,\n            authenticationHelper,\n            config,\n            tokenOrchestrator,\n        ], activeUsername, tokenOrchestrator);\n    }\n    return resp;\n}\n\nexport { handlePasswordSRP };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,uBAAuB,QAAQ,kEAAkE;AAC1G,OAAO,oDAAoD;AAC3D,OAAO,uBAAuB;AAC9B,SAASC,wBAAwB,QAAQ,mGAAmG;AAC5I,OAAO,wDAAwD;AAC/D,OAAO,wHAAwH;AAC/H,OAAO,8CAA8C;AACrD,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,sCAAsC;AAC7C,OAAO,6CAA6C;AACpD,SAASC,qCAAqC,QAAQ,gFAAgF;AACtI,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,+BAA+B,QAAQ,sEAAsE;AACtH,SAASC,gCAAgC,QAAQ,uEAAuE;AACxH,SAASC,uBAAuB,QAAQ,8DAA8D;;AAEtG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,SAeeC,iBAAiBA,CAAAC,EAAA;EAAA,OAAAC,kBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,mBAAA;EAAAA,kBAAA,GAAAG,iBAAA,CAAhC,WAAiC;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC,cAAc;IAAEC,MAAM;IAAEC,iBAAiB;IAAEC,QAAQ;IAAEC;EAAoB,CAAC,EAAE;IAC/H,MAAM;MAAEC,UAAU;MAAEC,gBAAgB;MAAEC;IAAiB,CAAC,GAAGN,MAAM;IACjE,MAAMO,YAAY,GAAGH,UAAU,EAAEI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;IACpD,MAAMC,oBAAoB,SAAS1B,uBAAuB,CAACwB,YAAY,CAAC;IACxE,MAAMG,cAAc,GAAG;MACnBC,QAAQ,EAAEd,QAAQ;MAClBe,KAAK,EAAEH,oBAAoB,CAACI,CAAC,CAACC,QAAQ,CAAC,EAAE;IAC7C,CAAC;IACD,IAAIZ,QAAQ,KAAK,WAAW,IAAIC,kBAAkB,EAAE;MAChDO,cAAc,CAACK,mBAAmB,GAAGZ,kBAAkB;IAC3D;IACA,MAAMa,eAAe,GAAGlC,kBAAkB,CAAC;MACvCe,QAAQ;MACRO,UAAU;MACVC;IACJ,CAAC,CAAC;IACF,MAAMY,OAAO,GAAG;MACZC,QAAQ,EAAEhB,QAAQ;MAClBiB,cAAc,EAAET,cAAc;MAC9BU,cAAc,EAAErB,cAAc;MAC9BsB,QAAQ,EAAEhB,gBAAgB;MAC1BW;IACJ,CAAC;IACD,MAAMM,YAAY,GAAGtC,wBAAwB,CAAC;MAC1CuC,gBAAgB,EAAEtC,qCAAqC,CAAC;QACpDuC,gBAAgB,EAAElB;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMmB,IAAI,SAASH,YAAY,CAAC;MAC5BI,MAAM,EAAExC,uBAAuB,CAACkB,UAAU,CAAC;MAC3CuB,cAAc,EAAExC,qBAAqB,CAACN,UAAU,CAAC+C,MAAM;IAC3D,CAAC,EAAEX,OAAO,CAAC;IACX,MAAM;MAAEY,mBAAmB,EAAEC,mBAAmB;MAAEC,OAAO,EAAEC;IAAQ,CAAC,GAAGP,IAAI;IAC3E,MAAMQ,cAAc,GAAGH,mBAAmB,EAAEnB,QAAQ,IAAId,QAAQ;IAChEP,uBAAuB,CAAC2C,cAAc,CAAC;IACvC,IAAIR,IAAI,CAACS,aAAa,KAAK,mBAAmB,EAAE;MAC5C,OAAO7C,gCAAgC,CAACD,+BAA+B,EAAE,CACrEU,QAAQ,EACRgC,mBAAmB,EACnB/B,cAAc,EACdiC,OAAO,EACPvB,oBAAoB,EACpBT,MAAM,EACNC,iBAAiB,CACpB,EAAEgC,cAAc,EAAEhC,iBAAiB,CAAC;IACzC;IACA,OAAOwB,IAAI;EACf,CAAC;EAAA,OAAAhC,kBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}