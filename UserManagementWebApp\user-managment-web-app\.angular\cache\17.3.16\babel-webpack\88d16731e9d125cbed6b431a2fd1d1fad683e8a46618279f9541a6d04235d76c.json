{"ast": null, "code": "import { createComponentCSS } from './createComponentCSS.mjs';\nimport { createComponentClasses } from './createComponentClasses.mjs';\n\n/**\n * Use this to create the theme of a component. You can use this\n * to both completely customize built-in components and\n * build your own components!\n *\n *\n * ```ts\n * // built-in component styling\n * const alertTheme = defineComponentTheme({\n *   name: 'alert',\n *   theme: (tokens) => {\n *     return {\n *       padding: tokens.space.large\n *     }\n *   }\n * });\n *\n * // custom component styling\n * const avatarTheme = defineComponentTheme({\n *   name: 'avatar',\n *   theme: (tokens) => {\n *     return {\n *       padding: tokens.space.large\n *     }\n *   }\n * })\n *\n * const theme = createTheme({\n *   name: 'my-theme',\n *   components: [alertTheme, avatarTheme]\n * })\n * ```\n *\n * @param {Object} params\n * @param {string} params.name  - The name of the component. Use a built-in component name like button to theme buttons.\n * @returns\n */\nfunction defineComponentTheme({\n  name,\n  theme,\n  overrides\n}) {\n  const prefix = 'amplify-';\n  const className = createComponentClasses({\n    name,\n    prefix\n  });\n  const cssText = props => {\n    return createComponentCSS({\n      theme: props.theme,\n      components: [{\n        name,\n        theme\n      }]\n    });\n  };\n  return {\n    className,\n    theme,\n    overrides,\n    name,\n    cssText\n  };\n}\nexport { defineComponentTheme };", "map": {"version": 3, "names": ["createComponentCSS", "createComponentClasses", "defineComponentTheme", "name", "theme", "overrides", "prefix", "className", "cssText", "props", "components"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/createTheme/defineComponentTheme.mjs"], "sourcesContent": ["import { createComponentCSS } from './createComponentCSS.mjs';\nimport { createComponentClasses } from './createComponentClasses.mjs';\n\n/**\n * Use this to create the theme of a component. You can use this\n * to both completely customize built-in components and\n * build your own components!\n *\n *\n * ```ts\n * // built-in component styling\n * const alertTheme = defineComponentTheme({\n *   name: 'alert',\n *   theme: (tokens) => {\n *     return {\n *       padding: tokens.space.large\n *     }\n *   }\n * });\n *\n * // custom component styling\n * const avatarTheme = defineComponentTheme({\n *   name: 'avatar',\n *   theme: (tokens) => {\n *     return {\n *       padding: tokens.space.large\n *     }\n *   }\n * })\n *\n * const theme = createTheme({\n *   name: 'my-theme',\n *   components: [alertTheme, avatarTheme]\n * })\n * ```\n *\n * @param {Object} params\n * @param {string} params.name  - The name of the component. Use a built-in component name like button to theme buttons.\n * @returns\n */\nfunction defineComponentTheme({ name, theme, overrides, }) {\n    const prefix = 'amplify-';\n    const className = createComponentClasses({\n        name,\n        prefix,\n    });\n    const cssText = (props) => {\n        return createComponentCSS({\n            theme: props.theme,\n            components: [{ name, theme }],\n        });\n    };\n    return {\n        className,\n        theme,\n        overrides,\n        name,\n        cssText,\n    };\n}\n\nexport { defineComponentTheme };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,sBAAsB,QAAQ,8BAA8B;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAC;EAAEC,IAAI;EAAEC,KAAK;EAAEC;AAAW,CAAC,EAAE;EACvD,MAAMC,MAAM,GAAG,UAAU;EACzB,MAAMC,SAAS,GAAGN,sBAAsB,CAAC;IACrCE,IAAI;IACJG;EACJ,CAAC,CAAC;EACF,MAAME,OAAO,GAAIC,KAAK,IAAK;IACvB,OAAOT,kBAAkB,CAAC;MACtBI,KAAK,EAAEK,KAAK,CAACL,KAAK;MAClBM,UAAU,EAAE,CAAC;QAAEP,IAAI;QAAEC;MAAM,CAAC;IAChC,CAAC,CAAC;EACN,CAAC;EACD,OAAO;IACHG,SAAS;IACTH,KAAK;IACLC,SAAS;IACTF,IAAI;IACJK;EACJ,CAAC;AACL;AAEA,SAASN,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}