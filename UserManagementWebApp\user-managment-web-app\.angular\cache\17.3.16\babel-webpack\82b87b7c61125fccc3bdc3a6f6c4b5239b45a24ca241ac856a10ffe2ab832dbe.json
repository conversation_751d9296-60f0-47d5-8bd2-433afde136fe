{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AmplifyError } from '../../errors/AmplifyError.mjs';\nimport { AmplifyErrorCode } from '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\nimport { withMemoization } from '../utils/memoization.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst shouldSendBody = method => !['HEAD', 'GET', 'DELETE'].includes(method.toUpperCase());\n// TODO[AllanZhengYP]: we need to provide isCanceledError utility\nconst fetchTransferHandler = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* ({\n    url,\n    method,\n    headers,\n    body\n  }, {\n    abortSignal,\n    cache,\n    withCrossDomainCredentials\n  }) {\n    let resp;\n    try {\n      resp = yield fetch(url, {\n        method,\n        headers,\n        body: shouldSendBody(method) ? body : undefined,\n        signal: abortSignal,\n        cache,\n        credentials: withCrossDomainCredentials ? 'include' : 'same-origin'\n      });\n    } catch (e) {\n      if (e instanceof TypeError) {\n        throw new AmplifyError({\n          name: AmplifyErrorCode.NetworkError,\n          message: 'A network error has occurred.',\n          underlyingError: e\n        });\n      }\n      throw e;\n    }\n    const responseHeaders = {};\n    resp.headers?.forEach((value, key) => {\n      responseHeaders[key.toLowerCase()] = value;\n    });\n    const httpResponse = {\n      statusCode: resp.status,\n      headers: responseHeaders,\n      body: null\n    };\n    // resp.body is a ReadableStream according to Fetch API spec, but React Native\n    // does not implement it.\n    const bodyWithMixin = Object.assign(resp.body ?? {}, {\n      text: withMemoization(() => resp.text()),\n      blob: withMemoization(() => resp.blob()),\n      json: withMemoization(() => resp.json())\n    });\n    return {\n      ...httpResponse,\n      body: bodyWithMixin\n    };\n  });\n  return function fetchTransferHandler(_x, _x2) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { fetchTransferHandler };", "map": {"version": 3, "names": ["AmplifyError", "AmplifyErrorCode", "withMemoization", "shouldSendBody", "method", "includes", "toUpperCase", "fetchTransferHandler", "_ref", "_asyncToGenerator", "url", "headers", "body", "abortSignal", "cache", "withCrossDomainCredentials", "resp", "fetch", "undefined", "signal", "credentials", "e", "TypeError", "name", "NetworkError", "message", "underlyingError", "responseHeaders", "for<PERSON>ach", "value", "key", "toLowerCase", "httpResponse", "statusCode", "status", "bodyWithMixin", "Object", "assign", "text", "blob", "json", "_x", "_x2", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/handlers/fetch.mjs"], "sourcesContent": ["import { AmplifyError } from '../../errors/AmplifyError.mjs';\nimport { AmplifyErrorCode } from '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\nimport { withMemoization } from '../utils/memoization.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst shouldSendBody = (method) => !['HEAD', 'GET', 'DELETE'].includes(method.toUpperCase());\n// TODO[AllanZhengYP]: we need to provide isCanceledError utility\nconst fetchTransferHandler = async ({ url, method, headers, body }, { abortSignal, cache, withCrossDomainCredentials }) => {\n    let resp;\n    try {\n        resp = await fetch(url, {\n            method,\n            headers,\n            body: shouldSendBody(method) ? body : undefined,\n            signal: abortSignal,\n            cache,\n            credentials: withCrossDomainCredentials ? 'include' : 'same-origin',\n        });\n    }\n    catch (e) {\n        if (e instanceof TypeError) {\n            throw new AmplifyError({\n                name: AmplifyErrorCode.NetworkError,\n                message: 'A network error has occurred.',\n                underlyingError: e,\n            });\n        }\n        throw e;\n    }\n    const responseHeaders = {};\n    resp.headers?.forEach((value, key) => {\n        responseHeaders[key.toLowerCase()] = value;\n    });\n    const httpResponse = {\n        statusCode: resp.status,\n        headers: responseHeaders,\n        body: null,\n    };\n    // resp.body is a ReadableStream according to Fetch API spec, but React Native\n    // does not implement it.\n    const bodyWithMixin = Object.assign(resp.body ?? {}, {\n        text: withMemoization(() => resp.text()),\n        blob: withMemoization(() => resp.blob()),\n        json: withMemoization(() => resp.json()),\n    });\n    return {\n        ...httpResponse,\n        body: bodyWithMixin,\n    };\n};\n\nexport { fetchTransferHandler };\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,OAAO,+BAA+B;AACtC,SAASC,eAAe,QAAQ,0BAA0B;;AAE1D;AACA;AACA,MAAMC,cAAc,GAAIC,MAAM,IAAK,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAACC,QAAQ,CAACD,MAAM,CAACE,WAAW,CAAC,CAAC,CAAC;AAC5F;AACA,MAAMC,oBAAoB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAO;IAAEC,GAAG;IAAEN,MAAM;IAAEO,OAAO;IAAEC;EAAK,CAAC,EAAE;IAAEC,WAAW;IAAEC,KAAK;IAAEC;EAA2B,CAAC,EAAK;IACvH,IAAIC,IAAI;IACR,IAAI;MACAA,IAAI,SAASC,KAAK,CAACP,GAAG,EAAE;QACpBN,MAAM;QACNO,OAAO;QACPC,IAAI,EAAET,cAAc,CAACC,MAAM,CAAC,GAAGQ,IAAI,GAAGM,SAAS;QAC/CC,MAAM,EAAEN,WAAW;QACnBC,KAAK;QACLM,WAAW,EAAEL,0BAA0B,GAAG,SAAS,GAAG;MAC1D,CAAC,CAAC;IACN,CAAC,CACD,OAAOM,CAAC,EAAE;MACN,IAAIA,CAAC,YAAYC,SAAS,EAAE;QACxB,MAAM,IAAItB,YAAY,CAAC;UACnBuB,IAAI,EAAEtB,gBAAgB,CAACuB,YAAY;UACnCC,OAAO,EAAE,+BAA+B;UACxCC,eAAe,EAAEL;QACrB,CAAC,CAAC;MACN;MACA,MAAMA,CAAC;IACX;IACA,MAAMM,eAAe,GAAG,CAAC,CAAC;IAC1BX,IAAI,CAACL,OAAO,EAAEiB,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;MAClCH,eAAe,CAACG,GAAG,CAACC,WAAW,CAAC,CAAC,CAAC,GAAGF,KAAK;IAC9C,CAAC,CAAC;IACF,MAAMG,YAAY,GAAG;MACjBC,UAAU,EAAEjB,IAAI,CAACkB,MAAM;MACvBvB,OAAO,EAAEgB,eAAe;MACxBf,IAAI,EAAE;IACV,CAAC;IACD;IACA;IACA,MAAMuB,aAAa,GAAGC,MAAM,CAACC,MAAM,CAACrB,IAAI,CAACJ,IAAI,IAAI,CAAC,CAAC,EAAE;MACjD0B,IAAI,EAAEpC,eAAe,CAAC,MAAMc,IAAI,CAACsB,IAAI,CAAC,CAAC,CAAC;MACxCC,IAAI,EAAErC,eAAe,CAAC,MAAMc,IAAI,CAACuB,IAAI,CAAC,CAAC,CAAC;MACxCC,IAAI,EAAEtC,eAAe,CAAC,MAAMc,IAAI,CAACwB,IAAI,CAAC,CAAC;IAC3C,CAAC,CAAC;IACF,OAAO;MACH,GAAGR,YAAY;MACfpB,IAAI,EAAEuB;IACV,CAAC;EACL,CAAC;EAAA,gBA1CK5B,oBAAoBA,CAAAkC,EAAA,EAAAC,GAAA;IAAA,OAAAlC,IAAA,CAAAmC,KAAA,OAAAC,SAAA;EAAA;AAAA,GA0CzB;AAED,SAASrC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}