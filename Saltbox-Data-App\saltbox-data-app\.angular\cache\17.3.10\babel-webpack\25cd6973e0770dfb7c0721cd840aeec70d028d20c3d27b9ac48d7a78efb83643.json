{"ast": null, "code": "import { DB_PRIMARY_KEY } from \"src/app/shared/constants/record.const\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/change-tracking.service\";\nimport * as i2 from \"./conditional-evaluation.service\";\nexport class GridOptionsService {\n  constructor(changeTrackingService, conditionalEvaluationService) {\n    this.changeTrackingService = changeTrackingService;\n    this.conditionalEvaluationService = conditionalEvaluationService;\n  }\n  setRowClassRules(gridOptions, requiredColumns) {\n    gridOptions.rowClassRules = {\n      'initial-row-pointer-events-auto': () => true,\n      'updated-row-pointer-events-none': params => {\n        if (params.data) {\n          const modifiedRecord = this.changeTrackingService.batchChange?.modifiedRecords.find(r => r.id === params.data[DB_PRIMARY_KEY] || r.recordPath?.includes(params.data[DB_PRIMARY_KEY]));\n          return modifiedRecord ? modifiedRecord.saveApplied : false;\n        }\n      },\n      'updated-row-pointer-events-auto': params => {\n        if (params.data) {\n          const modifiedRecord = this.changeTrackingService.batchChange?.modifiedRecords.find(r => r.id === params.data[DB_PRIMARY_KEY] || r.recordPath?.includes(params.data[DB_PRIMARY_KEY]));\n          return modifiedRecord ? !modifiedRecord.saveApplied : false;\n        }\n      },\n      'has-errors': params => {\n        if (!params.data || !requiredColumns?.length) {\n          return false;\n        }\n        return this.changeTrackingService.batchChange?.modifiedRecords?.find(r => r.id === params.data[DB_PRIMARY_KEY] || r.recordPath?.includes(params.data[DB_PRIMARY_KEY]))?.data?.properties?.filter(changeDetail => requiredColumns.some(colDef => colDef.column === changeDetail.property))?.some(changeDetail => changeDetail.newValue == null || changeDetail.newValue === '') ?? false;\n      }\n    };\n  }\n  getRowStyle(data, conditionalFormattingConfig, profileProperties) {\n    const filteredConfigs = conditionalFormattingConfig?.filter(c => !c.applyToCells);\n    if (!data || !filteredConfigs?.length) return undefined;\n    const config = this.conditionalEvaluationService.evaluateConditions(data, filteredConfigs, profileProperties);\n    return this.getCSSStyle(config);\n  }\n  setCellStyles(columnDefs, conditionalFormattingConfig, profileProperties) {\n    const filteredConfigs = conditionalFormattingConfig?.filter(c => c.applyToCells || !c.applyToCells && (c.fontStyle != 'Normal' || c.fontWeight != 'Normal' || c.textDecoration != 'None'));\n    if (!filteredConfigs?.length) return;\n    columnDefs.forEach(colDef => {\n      const filteredColConfigs = filteredConfigs.filter(c => c.applyToCells && c.columns?.some(cell => cell === colDef.field) || !c.applyToCells);\n      if (!filteredColConfigs.length) return;\n      colDef.cellStyle = params => {\n        const config = this.conditionalEvaluationService.evaluateConditions(params.data, filteredColConfigs, profileProperties);\n        return this.getCSSStyle(config, !config?.applyToCells);\n      };\n    });\n  }\n  getCSSStyle(config, justFontStyles = false) {\n    if (!config) return undefined;\n    const style = {\n      fontWeight: `${config.fontWeight} !important`,\n      fontStyle: `${config.fontStyle} !important`,\n      textDecoration: `${config.textDecoration} !important`\n    };\n    if (justFontStyles) {\n      return style;\n    }\n    style['backgroundColor'] = `${config.backgroundColor} !important`;\n    style['color'] = `${config.fontColor} !important`;\n    return style;\n  }\n  enablePivotMode(gridOptions) {\n    gridOptions.defaultColDef = {\n      ...gridOptions.defaultColDef,\n      enableValue: true,\n      enableRowGroup: true,\n      enablePivot: true\n    };\n    gridOptions.pivotMode = false;\n    gridOptions.pivotPanelShow = \"always\";\n  }\n  toggleColumnsOptions(gridApi, visible) {\n    gridApi.setGridOption('sideBar', visible ? \"columns\" : null);\n  }\n  static {\n    this.ɵfac = function GridOptionsService_Factory(t) {\n      return new (t || GridOptionsService)(i0.ɵɵinject(i1.ChangeTrackingService), i0.ɵɵinject(i2.ConditionalEvaluationService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GridOptionsService,\n      factory: GridOptionsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["DB_PRIMARY_KEY", "GridOptionsService", "constructor", "changeTrackingService", "conditionalEvaluationService", "setRowClassRules", "gridOptions", "requiredColumns", "rowClassRules", "initial-row-pointer-events-auto", "params", "data", "modifiedRecord", "batchChange", "modifiedRecords", "find", "r", "id", "recordPath", "includes", "saveApplied", "length", "properties", "filter", "changeDetail", "some", "colDef", "column", "property", "newValue", "getRowStyle", "conditionalFormattingConfig", "profileProperties", "filteredConfigs", "c", "<PERSON><PERSON><PERSON><PERSON>ells", "undefined", "config", "evaluateConditions", "getCSSStyle", "setCellStyles", "columnDefs", "fontStyle", "fontWeight", "textDecoration", "for<PERSON>ach", "filteredColConfigs", "columns", "cell", "field", "cellStyle", "justFontStyles", "style", "backgroundColor", "fontColor", "enablePivotMode", "defaultColDef", "enableValue", "enableRowGroup", "enablePivot", "pivotMode", "pivotPanelShow", "toggleColumnsOptions", "gridApi", "visible", "setGridOption", "i0", "ɵɵinject", "i1", "ChangeTrackingService", "i2", "ConditionalEvaluationService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\SaltboxActionableGrid\\Saltbox-Data-App\\saltbox-data-app\\src\\app\\actionable-grid\\services\\grid-options-service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport { <PERSON><PERSON><PERSON>, <PERSON>rid<PERSON><PERSON>, GridOptions } from \"ag-grid-community\";\r\nimport { ActionableGridColumnConfig } from \"src/app/core/models/actionable-grid-column-config\";\r\nimport { ConditionalFormattingConfig } from \"src/app/core/models/conditional-formatting-config\";\r\nimport { ChangeTrackingService } from \"src/app/core/services/change-tracking.service\";\r\nimport { DB_PRIMARY_KEY } from \"src/app/shared/constants/record.const\";\r\nimport { ConditionalEvaluationService } from \"./conditional-evaluation.service\";\r\nimport { ProfileProperty } from \"src/app/core/models/profile-property\";\r\n\r\n@Injectable({\r\n    providedIn: 'root'\r\n})\r\n\r\nexport class GridOptionsService {\r\n    constructor(private changeTrackingService: ChangeTrackingService, private conditionalEvaluationService: ConditionalEvaluationService) { }\r\n\r\n    setRowClassRules(gridOptions: GridOptions, requiredColumns: ActionableGridColumnConfig[]) {\r\n        gridOptions.rowClassRules = {\r\n            'initial-row-pointer-events-auto': () => true,\r\n            'updated-row-pointer-events-none': params => {\r\n                if (params.data) {\r\n                    const modifiedRecord = this.changeTrackingService.batchChange?.modifiedRecords\r\n                        .find(r => r.id === params.data[DB_PRIMARY_KEY] || r.recordPath?.includes(params.data[DB_PRIMARY_KEY]));\r\n                    return modifiedRecord ? modifiedRecord.saveApplied : false;\r\n                }\r\n            },\r\n            'updated-row-pointer-events-auto': params => {\r\n                if (params.data) {\r\n                    const modifiedRecord = this.changeTrackingService.batchChange?.modifiedRecords\r\n                        .find(r => r.id === params.data[DB_PRIMARY_KEY] || r.recordPath?.includes(params.data[DB_PRIMARY_KEY]));\r\n                    return modifiedRecord ? !modifiedRecord.saveApplied : false;\r\n                }\r\n            },\r\n            'has-errors': params => {\r\n                if (!params.data || !requiredColumns?.length) {\r\n                    return false;\r\n                }\r\n\r\n                return this.changeTrackingService.batchChange?.modifiedRecords\r\n                    ?.find(r => r.id === params.data[DB_PRIMARY_KEY] || r.recordPath?.includes(params.data[DB_PRIMARY_KEY]))?.data?.properties\r\n                    ?.filter(changeDetail => requiredColumns.some(colDef => colDef.column === changeDetail.property))\r\n                    ?.some(changeDetail => changeDetail.newValue == null || changeDetail.newValue === '') ?? false;\r\n            }\r\n        }\r\n    }\r\n\r\n    getRowStyle(data: any, conditionalFormattingConfig: ConditionalFormattingConfig[], profileProperties: ProfileProperty[]) {\r\n        const filteredConfigs = conditionalFormattingConfig?.filter(c => !c.applyToCells);\r\n        if (!data || !filteredConfigs?.length)\r\n            return undefined;\r\n\r\n        const config = this.conditionalEvaluationService.evaluateConditions(data, filteredConfigs, profileProperties);\r\n        return this.getCSSStyle(config);\r\n    }\r\n\r\n    setCellStyles(columnDefs: ColDef[], conditionalFormattingConfig: ConditionalFormattingConfig[], profileProperties: ProfileProperty[]) {\r\n        const filteredConfigs = conditionalFormattingConfig?.filter(c =>\r\n            c.applyToCells ||\r\n            (!c.applyToCells && (c.fontStyle != 'Normal' || c.fontWeight != 'Normal' || c.textDecoration != 'None')));\r\n\r\n        if (!filteredConfigs?.length)\r\n            return;\r\n\r\n\r\n        columnDefs.forEach(colDef => {\r\n            const filteredColConfigs = filteredConfigs.filter(c => (c.applyToCells && c.columns?.some(cell => cell === colDef.field)) || !c.applyToCells);\r\n            if (!filteredColConfigs.length)\r\n                return;\r\n\r\n            colDef.cellStyle = (params) => {\r\n                const config = this.conditionalEvaluationService.evaluateConditions(params.data, filteredColConfigs, profileProperties);\r\n                return this.getCSSStyle(config, !config?.applyToCells);\r\n            };\r\n        });\r\n    }\r\n\r\n    getCSSStyle(config: ConditionalFormattingConfig, justFontStyles = false) {\r\n        if (!config)\r\n            return undefined;\r\n\r\n        const style = {\r\n            fontWeight: `${config.fontWeight} !important`,\r\n            fontStyle: `${config.fontStyle} !important`,\r\n            textDecoration: `${config.textDecoration} !important`,\r\n        };\r\n\r\n        if (justFontStyles) {\r\n            return style;\r\n        }\r\n\r\n        style['backgroundColor'] = `${config.backgroundColor} !important`;\r\n        style['color'] = `${config.fontColor} !important`;\r\n\r\n        return style;\r\n    }\r\n\r\n    enablePivotMode(gridOptions: GridOptions) {\r\n        gridOptions.defaultColDef = {\r\n            ...gridOptions.defaultColDef,\r\n            enableValue: true,\r\n            enableRowGroup: true,\r\n            enablePivot: true,\r\n        };\r\n\r\n        gridOptions.pivotMode = false;\r\n        gridOptions.pivotPanelShow = \"always\";\r\n    }\r\n\r\n    toggleColumnsOptions(gridApi: GridApi, visible: boolean) {\r\n        gridApi.setGridOption('sideBar', visible ? \"columns\" : null);\r\n    }\r\n}\r\n"], "mappings": "AAKA,SAASA,cAAc,QAAQ,uCAAuC;;;;AAQtE,OAAM,MAAOC,kBAAkB;EAC3BC,YAAoBC,qBAA4C,EAAUC,4BAA0D;IAAhH,KAAAD,qBAAqB,GAArBA,qBAAqB;IAAiC,KAAAC,4BAA4B,GAA5BA,4BAA4B;EAAkC;EAExIC,gBAAgBA,CAACC,WAAwB,EAAEC,eAA6C;IACpFD,WAAW,CAACE,aAAa,GAAG;MACxB,iCAAiC,EAAEC,CAAA,KAAM,IAAI;MAC7C,iCAAiC,EAAEC,MAAM,IAAG;QACxC,IAAIA,MAAM,CAACC,IAAI,EAAE;UACb,MAAMC,cAAc,GAAG,IAAI,CAACT,qBAAqB,CAACU,WAAW,EAAEC,eAAe,CACzEC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKP,MAAM,CAACC,IAAI,CAACX,cAAc,CAAC,IAAIgB,CAAC,CAACE,UAAU,EAAEC,QAAQ,CAACT,MAAM,CAACC,IAAI,CAACX,cAAc,CAAC,CAAC,CAAC;UAC3G,OAAOY,cAAc,GAAGA,cAAc,CAACQ,WAAW,GAAG,KAAK;QAC9D;MACJ,CAAC;MACD,iCAAiC,EAAEV,MAAM,IAAG;QACxC,IAAIA,MAAM,CAACC,IAAI,EAAE;UACb,MAAMC,cAAc,GAAG,IAAI,CAACT,qBAAqB,CAACU,WAAW,EAAEC,eAAe,CACzEC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKP,MAAM,CAACC,IAAI,CAACX,cAAc,CAAC,IAAIgB,CAAC,CAACE,UAAU,EAAEC,QAAQ,CAACT,MAAM,CAACC,IAAI,CAACX,cAAc,CAAC,CAAC,CAAC;UAC3G,OAAOY,cAAc,GAAG,CAACA,cAAc,CAACQ,WAAW,GAAG,KAAK;QAC/D;MACJ,CAAC;MACD,YAAY,EAAEV,MAAM,IAAG;QACnB,IAAI,CAACA,MAAM,CAACC,IAAI,IAAI,CAACJ,eAAe,EAAEc,MAAM,EAAE;UAC1C,OAAO,KAAK;QAChB;QAEA,OAAO,IAAI,CAAClB,qBAAqB,CAACU,WAAW,EAAEC,eAAe,EACxDC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKP,MAAM,CAACC,IAAI,CAACX,cAAc,CAAC,IAAIgB,CAAC,CAACE,UAAU,EAAEC,QAAQ,CAACT,MAAM,CAACC,IAAI,CAACX,cAAc,CAAC,CAAC,CAAC,EAAEW,IAAI,EAAEW,UAAU,EACxHC,MAAM,CAACC,YAAY,IAAIjB,eAAe,CAACkB,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACC,MAAM,KAAKH,YAAY,CAACI,QAAQ,CAAC,CAAC,EAC/FH,IAAI,CAACD,YAAY,IAAIA,YAAY,CAACK,QAAQ,IAAI,IAAI,IAAIL,YAAY,CAACK,QAAQ,KAAK,EAAE,CAAC,IAAI,KAAK;MACtG;KACH;EACL;EAEAC,WAAWA,CAACnB,IAAS,EAAEoB,2BAA0D,EAAEC,iBAAoC;IACnH,MAAMC,eAAe,GAAGF,2BAA2B,EAAER,MAAM,CAACW,CAAC,IAAI,CAACA,CAAC,CAACC,YAAY,CAAC;IACjF,IAAI,CAACxB,IAAI,IAAI,CAACsB,eAAe,EAAEZ,MAAM,EACjC,OAAOe,SAAS;IAEpB,MAAMC,MAAM,GAAG,IAAI,CAACjC,4BAA4B,CAACkC,kBAAkB,CAAC3B,IAAI,EAAEsB,eAAe,EAAED,iBAAiB,CAAC;IAC7G,OAAO,IAAI,CAACO,WAAW,CAACF,MAAM,CAAC;EACnC;EAEAG,aAAaA,CAACC,UAAoB,EAAEV,2BAA0D,EAAEC,iBAAoC;IAChI,MAAMC,eAAe,GAAGF,2BAA2B,EAAER,MAAM,CAACW,CAAC,IACzDA,CAAC,CAACC,YAAY,IACb,CAACD,CAAC,CAACC,YAAY,KAAKD,CAAC,CAACQ,SAAS,IAAI,QAAQ,IAAIR,CAAC,CAACS,UAAU,IAAI,QAAQ,IAAIT,CAAC,CAACU,cAAc,IAAI,MAAM,CAAE,CAAC;IAE7G,IAAI,CAACX,eAAe,EAAEZ,MAAM,EACxB;IAGJoB,UAAU,CAACI,OAAO,CAACnB,MAAM,IAAG;MACxB,MAAMoB,kBAAkB,GAAGb,eAAe,CAACV,MAAM,CAACW,CAAC,IAAKA,CAAC,CAACC,YAAY,IAAID,CAAC,CAACa,OAAO,EAAEtB,IAAI,CAACuB,IAAI,IAAIA,IAAI,KAAKtB,MAAM,CAACuB,KAAK,CAAC,IAAK,CAACf,CAAC,CAACC,YAAY,CAAC;MAC7I,IAAI,CAACW,kBAAkB,CAACzB,MAAM,EAC1B;MAEJK,MAAM,CAACwB,SAAS,GAAIxC,MAAM,IAAI;QAC1B,MAAM2B,MAAM,GAAG,IAAI,CAACjC,4BAA4B,CAACkC,kBAAkB,CAAC5B,MAAM,CAACC,IAAI,EAAEmC,kBAAkB,EAAEd,iBAAiB,CAAC;QACvH,OAAO,IAAI,CAACO,WAAW,CAACF,MAAM,EAAE,CAACA,MAAM,EAAEF,YAAY,CAAC;MAC1D,CAAC;IACL,CAAC,CAAC;EACN;EAEAI,WAAWA,CAACF,MAAmC,EAAEc,cAAc,GAAG,KAAK;IACnE,IAAI,CAACd,MAAM,EACP,OAAOD,SAAS;IAEpB,MAAMgB,KAAK,GAAG;MACVT,UAAU,EAAE,GAAGN,MAAM,CAACM,UAAU,aAAa;MAC7CD,SAAS,EAAE,GAAGL,MAAM,CAACK,SAAS,aAAa;MAC3CE,cAAc,EAAE,GAAGP,MAAM,CAACO,cAAc;KAC3C;IAED,IAAIO,cAAc,EAAE;MAChB,OAAOC,KAAK;IAChB;IAEAA,KAAK,CAAC,iBAAiB,CAAC,GAAG,GAAGf,MAAM,CAACgB,eAAe,aAAa;IACjED,KAAK,CAAC,OAAO,CAAC,GAAG,GAAGf,MAAM,CAACiB,SAAS,aAAa;IAEjD,OAAOF,KAAK;EAChB;EAEAG,eAAeA,CAACjD,WAAwB;IACpCA,WAAW,CAACkD,aAAa,GAAG;MACxB,GAAGlD,WAAW,CAACkD,aAAa;MAC5BC,WAAW,EAAE,IAAI;MACjBC,cAAc,EAAE,IAAI;MACpBC,WAAW,EAAE;KAChB;IAEDrD,WAAW,CAACsD,SAAS,GAAG,KAAK;IAC7BtD,WAAW,CAACuD,cAAc,GAAG,QAAQ;EACzC;EAEAC,oBAAoBA,CAACC,OAAgB,EAAEC,OAAgB;IACnDD,OAAO,CAACE,aAAa,CAAC,SAAS,EAAED,OAAO,GAAG,SAAS,GAAG,IAAI,CAAC;EAChE;;;uBAjGS/D,kBAAkB,EAAAiE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,qBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,4BAAA;IAAA;EAAA;;;aAAlBtE,kBAAkB;MAAAuE,OAAA,EAAlBvE,kBAAkB,CAAAwE,IAAA;MAAAC,UAAA,EAHf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}