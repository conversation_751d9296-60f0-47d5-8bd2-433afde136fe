{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { assertTokenProviderConfig, assertOAuthConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { getAuthUserAgentValue } from '../../../../utils/getAuthUserAgentValue.mjs';\nimport { oAuthStore } from './oAuthStore.mjs';\nimport { completeOAuthFlow } from './completeOAuthFlow.mjs';\nimport { getRedirectUrl } from './getRedirectUrl.mjs';\nimport { handleFailure } from './handleFailure.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst attemptCompleteOAuthFlow = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (authConfig) {\n    try {\n      assertTokenProviderConfig(authConfig);\n      assertOAuthConfig(authConfig);\n      oAuthStore.setAuthConfig(authConfig);\n    } catch (_) {\n      // no-op\n      // This should not happen as Amplify singleton checks the oauth config key\n      // unless the oauth config object doesn't contain required properties\n      return;\n    }\n    // No inflight OAuth\n    if (!(yield oAuthStore.loadOAuthInFlight())) {\n      return;\n    }\n    try {\n      const currentUrl = window.location.href;\n      const {\n        loginWith,\n        userPoolClientId\n      } = authConfig;\n      const {\n        domain,\n        redirectSignIn,\n        responseType\n      } = loginWith.oauth;\n      const redirectUri = getRedirectUrl(redirectSignIn);\n      yield completeOAuthFlow({\n        currentUrl,\n        clientId: userPoolClientId,\n        domain,\n        redirectUri,\n        responseType,\n        userAgentValue: getAuthUserAgentValue(AuthAction.SignInWithRedirect)\n      });\n    } catch (err) {\n      yield handleFailure(err);\n    }\n  });\n  return function attemptCompleteOAuthFlow(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { attemptCompleteOAuthFlow };", "map": {"version": 3, "names": ["assertTokenProviderConfig", "assertOAuthConfig", "AuthAction", "getAuthUserAgentValue", "oAuthStore", "completeOAuthFlow", "getRedirectUrl", "handleFailure", "attemptCompleteOAuthFlow", "_ref", "_asyncToGenerator", "authConfig", "setAuthConfig", "_", "loadOAuthInFlight", "currentUrl", "window", "location", "href", "loginWith", "userPoolClientId", "domain", "redirectSignIn", "responseType", "o<PERSON>h", "redirectUri", "clientId", "userAgentValue", "SignInWithRedirect", "err", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/utils/oauth/attemptCompleteOAuthFlow.mjs"], "sourcesContent": ["import { assertTokenProviderConfig, assertOAuthConfig, AuthAction } from '@aws-amplify/core/internals/utils';\nimport { getAuthUserAgentValue } from '../../../../utils/getAuthUserAgentValue.mjs';\nimport { oAuthStore } from './oAuthStore.mjs';\nimport { completeOAuthFlow } from './completeOAuthFlow.mjs';\nimport { getRedirectUrl } from './getRedirectUrl.mjs';\nimport { handleFailure } from './handleFailure.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst attemptCompleteOAuthFlow = async (authConfig) => {\n    try {\n        assertTokenProviderConfig(authConfig);\n        assertOAuthConfig(authConfig);\n        oAuthStore.setAuthConfig(authConfig);\n    }\n    catch (_) {\n        // no-op\n        // This should not happen as Amplify singleton checks the oauth config key\n        // unless the oauth config object doesn't contain required properties\n        return;\n    }\n    // No inflight OAuth\n    if (!(await oAuthStore.loadOAuthInFlight())) {\n        return;\n    }\n    try {\n        const currentUrl = window.location.href;\n        const { loginWith, userPoolClientId } = authConfig;\n        const { domain, redirectSignIn, responseType } = loginWith.oauth;\n        const redirectUri = getRedirectUrl(redirectSignIn);\n        await completeOAuthFlow({\n            currentUrl,\n            clientId: userPoolClientId,\n            domain,\n            redirectUri,\n            responseType,\n            userAgentValue: getAuthUserAgentValue(AuthAction.SignInWithRedirect),\n        });\n    }\n    catch (err) {\n        await handleFailure(err);\n    }\n};\n\nexport { attemptCompleteOAuthFlow };\n"], "mappings": ";AAAA,SAASA,yBAAyB,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,mCAAmC;AAC5G,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,aAAa,QAAQ,qBAAqB;;AAEnD;AACA;AACA,MAAMC,wBAAwB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,UAAU,EAAK;IACnD,IAAI;MACAX,yBAAyB,CAACW,UAAU,CAAC;MACrCV,iBAAiB,CAACU,UAAU,CAAC;MAC7BP,UAAU,CAACQ,aAAa,CAACD,UAAU,CAAC;IACxC,CAAC,CACD,OAAOE,CAAC,EAAE;MACN;MACA;MACA;MACA;IACJ;IACA;IACA,IAAI,QAAQT,UAAU,CAACU,iBAAiB,CAAC,CAAC,CAAC,EAAE;MACzC;IACJ;IACA,IAAI;MACA,MAAMC,UAAU,GAAGC,MAAM,CAACC,QAAQ,CAACC,IAAI;MACvC,MAAM;QAAEC,SAAS;QAAEC;MAAiB,CAAC,GAAGT,UAAU;MAClD,MAAM;QAAEU,MAAM;QAAEC,cAAc;QAAEC;MAAa,CAAC,GAAGJ,SAAS,CAACK,KAAK;MAChE,MAAMC,WAAW,GAAGnB,cAAc,CAACgB,cAAc,CAAC;MAClD,MAAMjB,iBAAiB,CAAC;QACpBU,UAAU;QACVW,QAAQ,EAAEN,gBAAgB;QAC1BC,MAAM;QACNI,WAAW;QACXF,YAAY;QACZI,cAAc,EAAExB,qBAAqB,CAACD,UAAU,CAAC0B,kBAAkB;MACvE,CAAC,CAAC;IACN,CAAC,CACD,OAAOC,GAAG,EAAE;MACR,MAAMtB,aAAa,CAACsB,GAAG,CAAC;IAC5B;EACJ,CAAC;EAAA,gBAjCKrB,wBAAwBA,CAAAsB,EAAA;IAAA,OAAArB,IAAA,CAAAsB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAiC7B;AAED,SAASxB,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}