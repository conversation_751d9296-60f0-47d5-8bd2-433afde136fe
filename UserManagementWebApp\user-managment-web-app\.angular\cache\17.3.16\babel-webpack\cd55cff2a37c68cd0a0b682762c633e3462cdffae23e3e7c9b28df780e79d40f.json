{"ast": null, "code": "const fieldset = {\n  backgroundColor: {\n    value: 'transparent'\n  },\n  borderRadius: {\n    value: '{radii.xs.value}'\n  },\n  flexDirection: {\n    value: 'column'\n  },\n  gap: {\n    value: '{components.field.gap.value}'\n  },\n  legend: {\n    color: {\n      value: '{colors.font.primary.value}'\n    },\n    fontSize: {\n      value: '{components.field.fontSize.value}'\n    },\n    fontWeight: {\n      value: '{fontWeights.bold.value}'\n    },\n    lineHeight: {\n      value: '{lineHeights.medium.value}'\n    },\n    small: {\n      fontSize: '{components.field.small.fontSize.value}'\n    },\n    large: {\n      fontSize: '{components.field.large.fontSize.value}'\n    }\n  },\n  outlined: {\n    padding: '{space.medium.value}',\n    borderColor: '{colors.neutral.40.value}',\n    borderWidth: '{borderWidths.small.value}',\n    borderStyle: 'solid',\n    small: {\n      padding: '{space.small.value}'\n    },\n    large: {\n      padding: '{space.large.value}'\n    }\n  },\n  small: {\n    gap: '{components.field.small.gap.value}'\n  },\n  large: {\n    gap: '{components.field.large.gap.value}'\n  }\n};\nexport { fieldset };", "map": {"version": 3, "names": ["fieldset", "backgroundColor", "value", "borderRadius", "flexDirection", "gap", "legend", "color", "fontSize", "fontWeight", "lineHeight", "small", "large", "outlined", "padding", "borderColor", "borderWidth", "borderStyle"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/tokens/components/fieldset.mjs"], "sourcesContent": ["const fieldset = {\n    backgroundColor: { value: 'transparent' },\n    borderRadius: { value: '{radii.xs.value}' },\n    flexDirection: {\n        value: 'column',\n    },\n    gap: { value: '{components.field.gap.value}' },\n    legend: {\n        color: { value: '{colors.font.primary.value}' },\n        fontSize: { value: '{components.field.fontSize.value}' },\n        fontWeight: { value: '{fontWeights.bold.value}' },\n        lineHeight: { value: '{lineHeights.medium.value}' },\n        small: {\n            fontSize: '{components.field.small.fontSize.value}',\n        },\n        large: {\n            fontSize: '{components.field.large.fontSize.value}',\n        },\n    },\n    outlined: {\n        padding: '{space.medium.value}',\n        borderColor: '{colors.neutral.40.value}',\n        borderWidth: '{borderWidths.small.value}',\n        borderStyle: 'solid',\n        small: {\n            padding: '{space.small.value}',\n        },\n        large: {\n            padding: '{space.large.value}',\n        },\n    },\n    small: {\n        gap: '{components.field.small.gap.value}',\n    },\n    large: {\n        gap: '{components.field.large.gap.value}',\n    },\n};\n\nexport { fieldset };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAG;EACbC,eAAe,EAAE;IAAEC,KAAK,EAAE;EAAc,CAAC;EACzCC,YAAY,EAAE;IAAED,KAAK,EAAE;EAAmB,CAAC;EAC3CE,aAAa,EAAE;IACXF,KAAK,EAAE;EACX,CAAC;EACDG,GAAG,EAAE;IAAEH,KAAK,EAAE;EAA+B,CAAC;EAC9CI,MAAM,EAAE;IACJC,KAAK,EAAE;MAAEL,KAAK,EAAE;IAA8B,CAAC;IAC/CM,QAAQ,EAAE;MAAEN,KAAK,EAAE;IAAoC,CAAC;IACxDO,UAAU,EAAE;MAAEP,KAAK,EAAE;IAA2B,CAAC;IACjDQ,UAAU,EAAE;MAAER,KAAK,EAAE;IAA6B,CAAC;IACnDS,KAAK,EAAE;MACHH,QAAQ,EAAE;IACd,CAAC;IACDI,KAAK,EAAE;MACHJ,QAAQ,EAAE;IACd;EACJ,CAAC;EACDK,QAAQ,EAAE;IACNC,OAAO,EAAE,sBAAsB;IAC/BC,WAAW,EAAE,2BAA2B;IACxCC,WAAW,EAAE,4BAA4B;IACzCC,WAAW,EAAE,OAAO;IACpBN,KAAK,EAAE;MACHG,OAAO,EAAE;IACb,CAAC;IACDF,KAAK,EAAE;MACHE,OAAO,EAAE;IACb;EACJ,CAAC;EACDH,KAAK,EAAE;IACHN,GAAG,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACHP,GAAG,EAAE;EACT;AACJ,CAAC;AAED,SAASL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}