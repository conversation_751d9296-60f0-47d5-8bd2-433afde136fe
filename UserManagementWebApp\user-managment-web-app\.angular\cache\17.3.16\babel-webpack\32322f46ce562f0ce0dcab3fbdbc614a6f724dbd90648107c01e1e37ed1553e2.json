{"ast": null, "code": "const ComponentClassName = {\n  Accordion: 'amplify-accordion',\n  AccordionItem: 'amplify-accordion__item',\n  AccordionItemTrigger: 'amplify-accordion__item__trigger',\n  AccordionItemContent: 'amplify-accordion__item__content',\n  AccordionItemIcon: 'amplify-accordion__item__icon',\n  Alert: 'amplify-alert',\n  AlertIcon: 'amplify-alert__icon',\n  AlertHeading: 'amplify-alert__heading',\n  AlertBody: 'amplify-alert__body',\n  AlertDismiss: 'amplify-alert__dismiss',\n  Autocomplete: 'amplify-autocomplete',\n  AutocompleteMenu: 'amplify-autocomplete__menu',\n  AutocompleteMenuEmpty: 'amplify-autocomplete__menu--empty',\n  AutocompleteMenuFooter: 'amplify-autocomplete__menu__footer',\n  AutocompleteMenuHeader: 'amplify-autocomplete__menu__header',\n  AutocompleteMenuLoading: 'amplify-autocomplete__menu--loading',\n  AutocompleteMenuOption: 'amplify-autocomplete__menu__option',\n  AutocompleteMenuOptions: 'amplify-autocomplete__menu__options',\n  Avatar: 'amplify-avatar',\n  AvatarIcon: 'amplify-avatar__icon',\n  AvatarImage: 'amplify-avatar__image',\n  AvatarLoader: 'amplify-avatar__loader',\n  AIConversation: 'amplify-ai-conversation',\n  AIConversationScrollView: 'amplify-ai-conversation__scrollview',\n  AIConversationAttachment: 'amplify-ai-conversation__attachment',\n  AIConversationAttachmentList: 'amplify-ai-conversation__attachment__list',\n  AIConversationAttachmentImage: 'amplify-ai-conversation__attachment__image',\n  AIConversationAttachmentName: 'amplify-ai-conversation__attachment__name',\n  AIConversationAttachmentSize: 'amplify-ai-conversation__attachment__size',\n  AIConversationAttachmentRemove: 'amplify-ai-conversation__attachment__remove',\n  AIConversationForm: 'amplify-ai-conversation__form',\n  AIConversationFormAttach: 'amplify-ai-conversation__form__attach',\n  AIConversationFormError: 'amplify-ai-conversation__form__error',\n  AIConversationFormSend: 'amplify-ai-conversation__form__send',\n  AIConversationFormField: 'amplify-ai-conversation__form__field',\n  AIConversationFormDropzone: 'amplify-ai-conversation__form__dropzone',\n  AIConversationMessage: 'amplify-ai-conversation__message',\n  AIConversationMessageAvatar: 'amplify-ai-conversation__message__avatar',\n  AIConversationMessageSender: 'amplify-ai-conversation__message__sender',\n  AIConversationMessageSenderUsername: 'amplify-ai-conversation__message__sender__username',\n  AIConversationMessageSenderTimestamp: 'amplify-ai-conversation__message__sender__timestamp',\n  AIConversationMessageBody: 'amplify-ai-conversation__message__body',\n  AIConversationMessageContent: 'amplify-ai-conversation__message__content',\n  AIConversationMessageActions: 'amplify-ai-conversation__message__actions',\n  AIConversationMessageList: 'amplify-ai-conversation__message__list',\n  AIConversationPrompt: 'amplify-ai-conversation__prompt',\n  Badge: 'amplify-badge',\n  Breadcrumbs: 'amplify-breadcrumbs',\n  BreadcrumbsList: 'amplify-breadcrumbs__list',\n  BreadcrumbsItem: 'amplify-breadcrumbs__item',\n  BreadcrumbsSeparator: 'amplify-breadcrumbs__separator',\n  BreadcrumbsLink: 'amplify-breadcrumbs__link',\n  Button: 'amplify-button',\n  ButtonGroup: 'amplify-buttongroup',\n  ButtonLoaderWrapper: 'amplify-button__loader-wrapper',\n  Card: 'amplify-card',\n  Checkbox: 'amplify-checkbox',\n  CheckboxButton: 'amplify-checkbox__button',\n  CheckboxIcon: 'amplify-checkbox__icon',\n  CheckboxInput: 'amplify-checkbox__input',\n  CheckboxLabel: 'amplify-checkbox__label',\n  CheckboxField: 'amplify-checkboxfield',\n  Collection: 'amplify-collection',\n  CollectionItems: 'amplify-collection-items',\n  CollectionSearch: 'amplify-collection-search',\n  CollectionPagination: 'amplify-collection-pagination',\n  CountryCodeSelect: 'amplify-countrycodeselect',\n  DialCodeSelect: 'amplify-dialcodeselect',\n  Divider: 'amplify-divider',\n  DividerLabel: 'amplify-divider--label',\n  DropZone: 'amplify-dropzone',\n  Field: 'amplify-field',\n  FieldDescription: 'amplify-field__description',\n  FieldErrorMessage: 'amplify-field__error-message',\n  FieldGroup: 'amplify-field-group',\n  FieldGroupControl: 'amplify-field-group__control',\n  FieldGroupOuterEnd: 'amplify-field-group__outer-end',\n  FieldGroupOuterStart: 'amplify-field-group__outer-start',\n  FieldGroupInnerEnd: 'amplify-field-group__inner-end',\n  FieldGroupInnerStart: 'amplify-field-group__inner-start',\n  FieldGroupIcon: 'amplify-field-group__icon',\n  FieldGroupIconButton: 'amplify-field-group__icon-button',\n  FieldGroupHasInnerEnd: 'amplify-field-group--has-inner-end',\n  FieldGroupHasInnerStart: 'amplify-field-group--has-inner-start',\n  FieldShowPassword: 'amplify-field__show-password',\n  FieldGroupFieldWrapper: 'amplify-field-group__field-wrapper',\n  Fieldset: 'amplify-fieldset',\n  FieldsetLegend: 'amplify-fieldset__legend',\n  FileUploader: 'amplify-fileuploader',\n  FileUploaderDropZone: 'amplify-fileuploader__dropzone',\n  FileUploaderDropZoneIcon: 'amplify-fileuploader__dropzone__icon',\n  FileUploaderDropZoneText: 'amplify-fileuploader__dropzone__text',\n  FileUploaderFilePicker: 'amplify-fileuploader__file__picker',\n  FileUploaderFile: 'amplify-fileuploader__file',\n  FileUploaderFileWrapper: 'amplify-fileuploader__file__wrapper',\n  FileUploaderFileList: 'amplify-fileuploader__file__list',\n  FileUploaderFileName: 'amplify-fileuploader__file__name',\n  FileUploaderFileSize: 'amplify-fileuploader__file__size',\n  FileUploaderFileInfo: 'amplify-fileuploader__file__info',\n  FileUploaderFileImage: 'amplify-fileuploader__file__image',\n  FileUploaderFileMain: 'amplify-fileuploader__file__main',\n  FileUploaderFileStatus: 'amplify-fileuploader__file__status',\n  FileUploaderLoader: 'amplify-fileuploader__loader',\n  FileUploaderPreviewer: 'amplify-fileuploader__previewer',\n  FileUploaderPreviewerText: 'amplify-fileuploader__previewer__text',\n  FileUploaderPreviewerActions: 'amplify-fileuploader__previewer__actions',\n  FileUploaderPreviewerFooter: 'amplify-fileuploader__previewer__footer',\n  Flex: 'amplify-flex',\n  Grid: 'amplify-grid',\n  Heading: 'amplify-heading',\n  HighlightMatch: 'amplify-highlightmatch',\n  HighlightMatchHighlighted: 'amplify-highlightmatch__highlighted',\n  Icon: 'amplify-icon',\n  Image: 'amplify-image',\n  Input: 'amplify-input',\n  Label: 'amplify-label',\n  Link: 'amplify-link',\n  Loader: 'amplify-loader',\n  LoaderLabel: 'amplify-loader__label',\n  MenuContent: 'amplify-menu__content',\n  MenuItem: 'amplify-menu__content__item',\n  MenuTrigger: 'amplify-menu__trigger',\n  MenuWrapper: 'amplify-menu__wrapper',\n  Message: 'amplify-message',\n  MessageIcon: 'amplify-message__icon',\n  MessageHeading: 'amplify-message__heading',\n  MessageBody: 'amplify-message__body',\n  MessageContent: 'amplify-message__content',\n  MessageDismiss: 'amplify-message__dismiss',\n  Pagination: 'amplify-pagination',\n  PaginationItem: 'amplify-pagination__item',\n  PasswordField: 'amplify-passwordfield',\n  PhoneNumberField: 'amplify-phonenumberfield',\n  Placeholder: 'amplify-placeholder',\n  Radio: 'amplify-radio',\n  RadioButton: 'amplify-radio__button',\n  RadioInput: 'amplify-radio__input',\n  RadioLabel: 'amplify-radio__label',\n  RadioGroupField: 'amplify-radiogroupfield',\n  RadioGroup: 'amplify-radiogroup',\n  Rating: 'amplify-rating',\n  RatingItem: 'amplify-rating__item',\n  RatingIcon: 'amplify-rating__icon',\n  RatingLabel: 'amplify-rating__label',\n  ScrollView: 'amplify-scrollview',\n  SearchField: 'amplify-searchfield',\n  SearchFieldClear: 'amplify-searchfield__clear',\n  SearchFieldSearch: 'amplify-searchfield__search',\n  Select: 'amplify-select',\n  SelectField: 'amplify-selectfield',\n  SelectWrapper: 'amplify-select__wrapper',\n  SelectIcon: 'amplify-select__icon',\n  SliderField: 'amplify-sliderfield',\n  SliderFieldGroup: 'amplify-sliderfield__group',\n  SliderFieldLabel: 'amplify-sliderfield__label',\n  SliderFieldRange: 'amplify-sliderfield__range',\n  SliderFieldRoot: 'amplify-sliderfield__root',\n  SliderFieldThumb: 'amplify-sliderfield__thumb',\n  SliderFieldTrack: 'amplify-sliderfield__track',\n  StepperField: 'amplify-stepperfield',\n  StepperFieldButtonDecrease: 'amplify-stepperfield__button--decrease',\n  StepperFieldButtonIncrease: 'amplify-stepperfield__button--increase',\n  StepperFieldInput: 'amplify-stepperfield__input',\n  StorageImage: 'amplify-storageimage',\n  StorageManager: 'amplify-storagemanager',\n  StorageManagerDropZone: 'amplify-storagemanager__dropzone',\n  StorageManagerDropZoneIcon: 'amplify-storagemanager__dropzone__icon',\n  StorageManagerDropZoneText: 'amplify-storagemanager__dropzone__text',\n  StorageManagerFilePicker: 'amplify-storagemanager__file__picker',\n  StorageManagerFile: 'amplify-storagemanager__file',\n  StorageManagerFileWrapper: 'amplify-storagemanager__file__wrapper',\n  StorageManagerFileList: 'amplify-storagemanager__file__list',\n  StorageManagerFileName: 'amplify-storagemanager__file__name',\n  StorageManagerFileSize: 'amplify-storagemanager__file__size',\n  StorageManagerFileInfo: 'amplify-storagemanager__file__info',\n  StorageManagerFileImage: 'amplify-storagemanager__file__image',\n  StorageManagerFileMain: 'amplify-storagemanager__file__main',\n  StorageManagerFileStatus: 'amplify-storagemanager__file__status',\n  StorageManagerLoader: 'amplify-storagemanager__loader',\n  StorageManagerPreviewer: 'amplify-storagemanager__previewer',\n  StorageManagerPreviewerText: 'amplify-storagemanager__previewer__text',\n  StorageManagerPreviewerActions: 'amplify-storagemanager__previewer__actions',\n  StorageManagerPreviewerFooter: 'amplify-storagemanager__previewer__footer',\n  SwitchField: 'amplify-switchfield',\n  SwitchLabel: 'amplify-switch__label',\n  SwitchThumb: 'amplify-switch__thumb',\n  SwitchTrack: 'amplify-switch__track',\n  SwitchWrapper: 'amplify-switch__wrapper',\n  Table: 'amplify-table',\n  TableCaption: 'amplify-table__caption',\n  TableBody: 'amplify-table__body',\n  TableTd: 'amplify-table__td',\n  TableTh: 'amplify-table__th',\n  TableFoot: 'amplify-table__foot',\n  TableHead: 'amplify-table__head',\n  TableRow: 'amplify-table__row',\n  Tabs: 'amplify-tabs',\n  TabsList: 'amplify-tabs__list',\n  TabsItem: 'amplify-tabs__item',\n  TabsPanel: 'amplify-tabs__panel',\n  Text: 'amplify-text',\n  Textarea: 'amplify-textarea',\n  TextAreaField: 'amplify-textareafield',\n  TextField: 'amplify-textfield',\n  ToggleButton: 'amplify-togglebutton',\n  ToggleButtonGroup: 'amplify-togglebuttongroup',\n  VisuallyHidden: 'amplify-visually-hidden'\n};\nexport { ComponentClassName };", "map": {"version": 3, "names": ["ComponentClassName", "Accordion", "AccordionItem", "AccordionItemTrigger", "Accordion<PERSON><PERSON><PERSON><PERSON>nt", "AccordionItemIcon", "<PERSON><PERSON>", "AlertIcon", "AlertHeading", "AlertBody", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Autocomplete", "AutocompleteMenu", "AutocompleteMenuEmpty", "AutocompleteMenuFooter", "AutocompleteMenuHeader", "AutocompleteMenuLoading", "AutocompleteMenuOption", "AutocompleteMenuOptions", "Avatar", "AvatarIcon", "AvatarImage", "AvatarLoader", "AIConversation", "AIConversationScrollView", "AIConversationAttachment", "AIConversationAttachmentList", "AIConversationAttachmentImage", "AIConversationAttachmentName", "AIConversationAttachmentSize", "AIConversationAttachmentRemove", "AIConversationForm", "AIConversationFormAttach", "AIConversationFormError", "AIConversationFormSend", "AIConversationFormField", "AIConversationFormDropzone", "AIConversationMessage", "AIConversationMessageAvatar", "AIConversationMessageSender", "AIConversationMessageSenderUsername", "AIConversationMessageSenderTimestamp", "AIConversationMessageBody", "AIConversationMessageContent", "AIConversationMessageActions", "AIConversationMessageList", "AIConversationPrompt", "Badge", "Breadcrumbs", "BreadcrumbsList", "BreadcrumbsItem", "BreadcrumbsSeparator", "BreadcrumbsLink", "<PERSON><PERSON>", "ButtonGroup", "ButtonLoaderWrapper", "Card", "Checkbox", "CheckboxButton", "CheckboxIcon", "CheckboxInput", "CheckboxLabel", "CheckboxField", "Collection", "CollectionItems", "CollectionSearch", "CollectionPagination", "CountryCodeSelect", "DialCodeSelect", "Divider", "Di<PERSON><PERSON><PERSON><PERSON><PERSON>", "DropZone", "Field", "FieldDescription", "FieldErrorMessage", "FieldGroup", "FieldGroupControl", "FieldGroupOuterEnd", "FieldGroupOuterStart", "FieldGroupInnerEnd", "FieldGroupInnerStart", "FieldGroupIcon", "FieldGroupIconButton", "FieldGroupHasInnerEnd", "FieldGroupHasInnerStart", "FieldShowPassword", "FieldGroupFieldWrapper", "<PERSON><PERSON>", "FieldsetLegend", "FileUploader", "FileUploaderDropZone", "FileUploaderDropZoneIcon", "FileUploaderDropZoneText", "FileUploaderFilePicker", "FileUploaderFile", "FileUploaderFileWrapper", "FileUploaderFileList", "FileUploaderFileName", "FileUploaderFileSize", "FileUploaderFileInfo", "FileUploaderFileImage", "FileUploaderFileMain", "FileUploaderFileStatus", "FileUploaderLoader", "FileUploaderPreviewer", "FileUploaderPreviewerText", "FileUploaderPreviewerActions", "FileUploaderPreviewerFooter", "Flex", "Grid", "Heading", "HighlightMatch", "HighlightMatchHighlighted", "Icon", "Image", "Input", "Label", "Link", "Loader", "LoaderLabel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MenuItem", "<PERSON>uTrigger", "MenuWrapper", "Message", "MessageIcon", "MessageHeading", "MessageBody", "MessageContent", "MessageDismiss", "Pagination", "PaginationItem", "PasswordField", "PhoneNumberField", "Placeholder", "Radio", "RadioButton", "RadioInput", "RadioLabel", "RadioGroupField", "RadioGroup", "Rating", "RatingItem", "RatingIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ScrollView", "SearchField", "SearchFieldClear", "SearchFieldSearch", "Select", "SelectField", "SelectWrapper", "SelectIcon", "<PERSON><PERSON>rField", "SliderFieldGroup", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SliderFieldRange", "SliderFieldRoot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SliderFieldTrack", "StepperField", "StepperFieldButtonDecrease", "StepperFieldButtonIncrease", "StepperFieldInput", "StorageImage", "StorageManager", "StorageManagerDropZone", "StorageManagerDropZoneIcon", "StorageManagerDropZoneText", "StorageManagerFilePicker", "StorageManagerFile", "StorageManagerFileWrapper", "StorageManagerFileList", "StorageManagerFileName", "StorageManagerFileSize", "StorageManagerFileInfo", "StorageManagerFileImage", "StorageManagerFileMain", "StorageManagerFileStatus", "StorageManagerLoader", "StorageManagerPreviewer", "StorageManagerPreviewerText", "StorageManagerPreviewerActions", "StorageManagerPreviewerFooter", "SwitchField", "SwitchLabel", "SwitchThumb", "SwitchTrack", "SwitchWrapper", "Table", "TableCaption", "TableBody", "TableTd", "TableTh", "TableFoot", "TableHead", "TableRow", "Tabs", "TabsList", "TabsItem", "TabsPanel", "Text", "Textarea", "TextAreaField", "TextField", "ToggleButton", "ToggleButtonGroup", "VisuallyHidden"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/types/primitives/componentClassName.mjs"], "sourcesContent": ["const ComponentClassName = {\n    Accordion: 'amplify-accordion',\n    AccordionItem: 'amplify-accordion__item',\n    AccordionItemTrigger: 'amplify-accordion__item__trigger',\n    AccordionItemContent: 'amplify-accordion__item__content',\n    AccordionItemIcon: 'amplify-accordion__item__icon',\n    Alert: 'amplify-alert',\n    AlertIcon: 'amplify-alert__icon',\n    AlertHeading: 'amplify-alert__heading',\n    AlertBody: 'amplify-alert__body',\n    AlertDismiss: 'amplify-alert__dismiss',\n    Autocomplete: 'amplify-autocomplete',\n    AutocompleteMenu: 'amplify-autocomplete__menu',\n    AutocompleteMenuEmpty: 'amplify-autocomplete__menu--empty',\n    AutocompleteMenuFooter: 'amplify-autocomplete__menu__footer',\n    AutocompleteMenuHeader: 'amplify-autocomplete__menu__header',\n    AutocompleteMenuLoading: 'amplify-autocomplete__menu--loading',\n    AutocompleteMenuOption: 'amplify-autocomplete__menu__option',\n    AutocompleteMenuOptions: 'amplify-autocomplete__menu__options',\n    Avatar: 'amplify-avatar',\n    AvatarIcon: 'amplify-avatar__icon',\n    AvatarImage: 'amplify-avatar__image',\n    AvatarLoader: 'amplify-avatar__loader',\n    AIConversation: 'amplify-ai-conversation',\n    AIConversationScrollView: 'amplify-ai-conversation__scrollview',\n    AIConversationAttachment: 'amplify-ai-conversation__attachment',\n    AIConversationAttachmentList: 'amplify-ai-conversation__attachment__list',\n    AIConversationAttachmentImage: 'amplify-ai-conversation__attachment__image',\n    AIConversationAttachmentName: 'amplify-ai-conversation__attachment__name',\n    AIConversationAttachmentSize: 'amplify-ai-conversation__attachment__size',\n    AIConversationAttachmentRemove: 'amplify-ai-conversation__attachment__remove',\n    AIConversationForm: 'amplify-ai-conversation__form',\n    AIConversationFormAttach: 'amplify-ai-conversation__form__attach',\n    AIConversationFormError: 'amplify-ai-conversation__form__error',\n    AIConversationFormSend: 'amplify-ai-conversation__form__send',\n    AIConversationFormField: 'amplify-ai-conversation__form__field',\n    AIConversationFormDropzone: 'amplify-ai-conversation__form__dropzone',\n    AIConversationMessage: 'amplify-ai-conversation__message',\n    AIConversationMessageAvatar: 'amplify-ai-conversation__message__avatar',\n    AIConversationMessageSender: 'amplify-ai-conversation__message__sender',\n    AIConversationMessageSenderUsername: 'amplify-ai-conversation__message__sender__username',\n    AIConversationMessageSenderTimestamp: 'amplify-ai-conversation__message__sender__timestamp',\n    AIConversationMessageBody: 'amplify-ai-conversation__message__body',\n    AIConversationMessageContent: 'amplify-ai-conversation__message__content',\n    AIConversationMessageActions: 'amplify-ai-conversation__message__actions',\n    AIConversationMessageList: 'amplify-ai-conversation__message__list',\n    AIConversationPrompt: 'amplify-ai-conversation__prompt',\n    Badge: 'amplify-badge',\n    Breadcrumbs: 'amplify-breadcrumbs',\n    BreadcrumbsList: 'amplify-breadcrumbs__list',\n    BreadcrumbsItem: 'amplify-breadcrumbs__item',\n    BreadcrumbsSeparator: 'amplify-breadcrumbs__separator',\n    BreadcrumbsLink: 'amplify-breadcrumbs__link',\n    Button: 'amplify-button',\n    ButtonGroup: 'amplify-buttongroup',\n    ButtonLoaderWrapper: 'amplify-button__loader-wrapper',\n    Card: 'amplify-card',\n    Checkbox: 'amplify-checkbox',\n    CheckboxButton: 'amplify-checkbox__button',\n    CheckboxIcon: 'amplify-checkbox__icon',\n    CheckboxInput: 'amplify-checkbox__input',\n    CheckboxLabel: 'amplify-checkbox__label',\n    CheckboxField: 'amplify-checkboxfield',\n    Collection: 'amplify-collection',\n    CollectionItems: 'amplify-collection-items',\n    CollectionSearch: 'amplify-collection-search',\n    CollectionPagination: 'amplify-collection-pagination',\n    CountryCodeSelect: 'amplify-countrycodeselect',\n    DialCodeSelect: 'amplify-dialcodeselect',\n    Divider: 'amplify-divider',\n    DividerLabel: 'amplify-divider--label',\n    DropZone: 'amplify-dropzone',\n    Field: 'amplify-field',\n    FieldDescription: 'amplify-field__description',\n    FieldErrorMessage: 'amplify-field__error-message',\n    FieldGroup: 'amplify-field-group',\n    FieldGroupControl: 'amplify-field-group__control',\n    FieldGroupOuterEnd: 'amplify-field-group__outer-end',\n    FieldGroupOuterStart: 'amplify-field-group__outer-start',\n    FieldGroupInnerEnd: 'amplify-field-group__inner-end',\n    FieldGroupInnerStart: 'amplify-field-group__inner-start',\n    FieldGroupIcon: 'amplify-field-group__icon',\n    FieldGroupIconButton: 'amplify-field-group__icon-button',\n    FieldGroupHasInnerEnd: 'amplify-field-group--has-inner-end',\n    FieldGroupHasInnerStart: 'amplify-field-group--has-inner-start',\n    FieldShowPassword: 'amplify-field__show-password',\n    FieldGroupFieldWrapper: 'amplify-field-group__field-wrapper',\n    Fieldset: 'amplify-fieldset',\n    FieldsetLegend: 'amplify-fieldset__legend',\n    FileUploader: 'amplify-fileuploader',\n    FileUploaderDropZone: 'amplify-fileuploader__dropzone',\n    FileUploaderDropZoneIcon: 'amplify-fileuploader__dropzone__icon',\n    FileUploaderDropZoneText: 'amplify-fileuploader__dropzone__text',\n    FileUploaderFilePicker: 'amplify-fileuploader__file__picker',\n    FileUploaderFile: 'amplify-fileuploader__file',\n    FileUploaderFileWrapper: 'amplify-fileuploader__file__wrapper',\n    FileUploaderFileList: 'amplify-fileuploader__file__list',\n    FileUploaderFileName: 'amplify-fileuploader__file__name',\n    FileUploaderFileSize: 'amplify-fileuploader__file__size',\n    FileUploaderFileInfo: 'amplify-fileuploader__file__info',\n    FileUploaderFileImage: 'amplify-fileuploader__file__image',\n    FileUploaderFileMain: 'amplify-fileuploader__file__main',\n    FileUploaderFileStatus: 'amplify-fileuploader__file__status',\n    FileUploaderLoader: 'amplify-fileuploader__loader',\n    FileUploaderPreviewer: 'amplify-fileuploader__previewer',\n    FileUploaderPreviewerText: 'amplify-fileuploader__previewer__text',\n    FileUploaderPreviewerActions: 'amplify-fileuploader__previewer__actions',\n    FileUploaderPreviewerFooter: 'amplify-fileuploader__previewer__footer',\n    Flex: 'amplify-flex',\n    Grid: 'amplify-grid',\n    Heading: 'amplify-heading',\n    HighlightMatch: 'amplify-highlightmatch',\n    HighlightMatchHighlighted: 'amplify-highlightmatch__highlighted',\n    Icon: 'amplify-icon',\n    Image: 'amplify-image',\n    Input: 'amplify-input',\n    Label: 'amplify-label',\n    Link: 'amplify-link',\n    Loader: 'amplify-loader',\n    LoaderLabel: 'amplify-loader__label',\n    MenuContent: 'amplify-menu__content',\n    MenuItem: 'amplify-menu__content__item',\n    MenuTrigger: 'amplify-menu__trigger',\n    MenuWrapper: 'amplify-menu__wrapper',\n    Message: 'amplify-message',\n    MessageIcon: 'amplify-message__icon',\n    MessageHeading: 'amplify-message__heading',\n    MessageBody: 'amplify-message__body',\n    MessageContent: 'amplify-message__content',\n    MessageDismiss: 'amplify-message__dismiss',\n    Pagination: 'amplify-pagination',\n    PaginationItem: 'amplify-pagination__item',\n    PasswordField: 'amplify-passwordfield',\n    PhoneNumberField: 'amplify-phonenumberfield',\n    Placeholder: 'amplify-placeholder',\n    Radio: 'amplify-radio',\n    RadioButton: 'amplify-radio__button',\n    RadioInput: 'amplify-radio__input',\n    RadioLabel: 'amplify-radio__label',\n    RadioGroupField: 'amplify-radiogroupfield',\n    RadioGroup: 'amplify-radiogroup',\n    Rating: 'amplify-rating',\n    RatingItem: 'amplify-rating__item',\n    RatingIcon: 'amplify-rating__icon',\n    RatingLabel: 'amplify-rating__label',\n    ScrollView: 'amplify-scrollview',\n    SearchField: 'amplify-searchfield',\n    SearchFieldClear: 'amplify-searchfield__clear',\n    SearchFieldSearch: 'amplify-searchfield__search',\n    Select: 'amplify-select',\n    SelectField: 'amplify-selectfield',\n    SelectWrapper: 'amplify-select__wrapper',\n    SelectIcon: 'amplify-select__icon',\n    SliderField: 'amplify-sliderfield',\n    SliderFieldGroup: 'amplify-sliderfield__group',\n    SliderFieldLabel: 'amplify-sliderfield__label',\n    SliderFieldRange: 'amplify-sliderfield__range',\n    SliderFieldRoot: 'amplify-sliderfield__root',\n    SliderFieldThumb: 'amplify-sliderfield__thumb',\n    SliderFieldTrack: 'amplify-sliderfield__track',\n    StepperField: 'amplify-stepperfield',\n    StepperFieldButtonDecrease: 'amplify-stepperfield__button--decrease',\n    StepperFieldButtonIncrease: 'amplify-stepperfield__button--increase',\n    StepperFieldInput: 'amplify-stepperfield__input',\n    StorageImage: 'amplify-storageimage',\n    StorageManager: 'amplify-storagemanager',\n    StorageManagerDropZone: 'amplify-storagemanager__dropzone',\n    StorageManagerDropZoneIcon: 'amplify-storagemanager__dropzone__icon',\n    StorageManagerDropZoneText: 'amplify-storagemanager__dropzone__text',\n    StorageManagerFilePicker: 'amplify-storagemanager__file__picker',\n    StorageManagerFile: 'amplify-storagemanager__file',\n    StorageManagerFileWrapper: 'amplify-storagemanager__file__wrapper',\n    StorageManagerFileList: 'amplify-storagemanager__file__list',\n    StorageManagerFileName: 'amplify-storagemanager__file__name',\n    StorageManagerFileSize: 'amplify-storagemanager__file__size',\n    StorageManagerFileInfo: 'amplify-storagemanager__file__info',\n    StorageManagerFileImage: 'amplify-storagemanager__file__image',\n    StorageManagerFileMain: 'amplify-storagemanager__file__main',\n    StorageManagerFileStatus: 'amplify-storagemanager__file__status',\n    StorageManagerLoader: 'amplify-storagemanager__loader',\n    StorageManagerPreviewer: 'amplify-storagemanager__previewer',\n    StorageManagerPreviewerText: 'amplify-storagemanager__previewer__text',\n    StorageManagerPreviewerActions: 'amplify-storagemanager__previewer__actions',\n    StorageManagerPreviewerFooter: 'amplify-storagemanager__previewer__footer',\n    SwitchField: 'amplify-switchfield',\n    SwitchLabel: 'amplify-switch__label',\n    SwitchThumb: 'amplify-switch__thumb',\n    SwitchTrack: 'amplify-switch__track',\n    SwitchWrapper: 'amplify-switch__wrapper',\n    Table: 'amplify-table',\n    TableCaption: 'amplify-table__caption',\n    TableBody: 'amplify-table__body',\n    TableTd: 'amplify-table__td',\n    TableTh: 'amplify-table__th',\n    TableFoot: 'amplify-table__foot',\n    TableHead: 'amplify-table__head',\n    TableRow: 'amplify-table__row',\n    Tabs: 'amplify-tabs',\n    TabsList: 'amplify-tabs__list',\n    TabsItem: 'amplify-tabs__item',\n    TabsPanel: 'amplify-tabs__panel',\n    Text: 'amplify-text',\n    Textarea: 'amplify-textarea',\n    TextAreaField: 'amplify-textareafield',\n    TextField: 'amplify-textfield',\n    ToggleButton: 'amplify-togglebutton',\n    ToggleButtonGroup: 'amplify-togglebuttongroup',\n    VisuallyHidden: 'amplify-visually-hidden',\n};\n\nexport { ComponentClassName };\n"], "mappings": "AAAA,MAAMA,kBAAkB,GAAG;EACvBC,SAAS,EAAE,mBAAmB;EAC9BC,aAAa,EAAE,yBAAyB;EACxCC,oBAAoB,EAAE,kCAAkC;EACxDC,oBAAoB,EAAE,kCAAkC;EACxDC,iBAAiB,EAAE,+BAA+B;EAClDC,KAAK,EAAE,eAAe;EACtBC,SAAS,EAAE,qBAAqB;EAChCC,YAAY,EAAE,wBAAwB;EACtCC,SAAS,EAAE,qBAAqB;EAChCC,YAAY,EAAE,wBAAwB;EACtCC,YAAY,EAAE,sBAAsB;EACpCC,gBAAgB,EAAE,4BAA4B;EAC9CC,qBAAqB,EAAE,mCAAmC;EAC1DC,sBAAsB,EAAE,oCAAoC;EAC5DC,sBAAsB,EAAE,oCAAoC;EAC5DC,uBAAuB,EAAE,qCAAqC;EAC9DC,sBAAsB,EAAE,oCAAoC;EAC5DC,uBAAuB,EAAE,qCAAqC;EAC9DC,MAAM,EAAE,gBAAgB;EACxBC,UAAU,EAAE,sBAAsB;EAClCC,WAAW,EAAE,uBAAuB;EACpCC,YAAY,EAAE,wBAAwB;EACtCC,cAAc,EAAE,yBAAyB;EACzCC,wBAAwB,EAAE,qCAAqC;EAC/DC,wBAAwB,EAAE,qCAAqC;EAC/DC,4BAA4B,EAAE,2CAA2C;EACzEC,6BAA6B,EAAE,4CAA4C;EAC3EC,4BAA4B,EAAE,2CAA2C;EACzEC,4BAA4B,EAAE,2CAA2C;EACzEC,8BAA8B,EAAE,6CAA6C;EAC7EC,kBAAkB,EAAE,+BAA+B;EACnDC,wBAAwB,EAAE,uCAAuC;EACjEC,uBAAuB,EAAE,sCAAsC;EAC/DC,sBAAsB,EAAE,qCAAqC;EAC7DC,uBAAuB,EAAE,sCAAsC;EAC/DC,0BAA0B,EAAE,yCAAyC;EACrEC,qBAAqB,EAAE,kCAAkC;EACzDC,2BAA2B,EAAE,0CAA0C;EACvEC,2BAA2B,EAAE,0CAA0C;EACvEC,mCAAmC,EAAE,oDAAoD;EACzFC,oCAAoC,EAAE,qDAAqD;EAC3FC,yBAAyB,EAAE,wCAAwC;EACnEC,4BAA4B,EAAE,2CAA2C;EACzEC,4BAA4B,EAAE,2CAA2C;EACzEC,yBAAyB,EAAE,wCAAwC;EACnEC,oBAAoB,EAAE,iCAAiC;EACvDC,KAAK,EAAE,eAAe;EACtBC,WAAW,EAAE,qBAAqB;EAClCC,eAAe,EAAE,2BAA2B;EAC5CC,eAAe,EAAE,2BAA2B;EAC5CC,oBAAoB,EAAE,gCAAgC;EACtDC,eAAe,EAAE,2BAA2B;EAC5CC,MAAM,EAAE,gBAAgB;EACxBC,WAAW,EAAE,qBAAqB;EAClCC,mBAAmB,EAAE,gCAAgC;EACrDC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,kBAAkB;EAC5BC,cAAc,EAAE,0BAA0B;EAC1CC,YAAY,EAAE,wBAAwB;EACtCC,aAAa,EAAE,yBAAyB;EACxCC,aAAa,EAAE,yBAAyB;EACxCC,aAAa,EAAE,uBAAuB;EACtCC,UAAU,EAAE,oBAAoB;EAChCC,eAAe,EAAE,0BAA0B;EAC3CC,gBAAgB,EAAE,2BAA2B;EAC7CC,oBAAoB,EAAE,+BAA+B;EACrDC,iBAAiB,EAAE,2BAA2B;EAC9CC,cAAc,EAAE,wBAAwB;EACxCC,OAAO,EAAE,iBAAiB;EAC1BC,YAAY,EAAE,wBAAwB;EACtCC,QAAQ,EAAE,kBAAkB;EAC5BC,KAAK,EAAE,eAAe;EACtBC,gBAAgB,EAAE,4BAA4B;EAC9CC,iBAAiB,EAAE,8BAA8B;EACjDC,UAAU,EAAE,qBAAqB;EACjCC,iBAAiB,EAAE,8BAA8B;EACjDC,kBAAkB,EAAE,gCAAgC;EACpDC,oBAAoB,EAAE,kCAAkC;EACxDC,kBAAkB,EAAE,gCAAgC;EACpDC,oBAAoB,EAAE,kCAAkC;EACxDC,cAAc,EAAE,2BAA2B;EAC3CC,oBAAoB,EAAE,kCAAkC;EACxDC,qBAAqB,EAAE,oCAAoC;EAC3DC,uBAAuB,EAAE,sCAAsC;EAC/DC,iBAAiB,EAAE,8BAA8B;EACjDC,sBAAsB,EAAE,oCAAoC;EAC5DC,QAAQ,EAAE,kBAAkB;EAC5BC,cAAc,EAAE,0BAA0B;EAC1CC,YAAY,EAAE,sBAAsB;EACpCC,oBAAoB,EAAE,gCAAgC;EACtDC,wBAAwB,EAAE,sCAAsC;EAChEC,wBAAwB,EAAE,sCAAsC;EAChEC,sBAAsB,EAAE,oCAAoC;EAC5DC,gBAAgB,EAAE,4BAA4B;EAC9CC,uBAAuB,EAAE,qCAAqC;EAC9DC,oBAAoB,EAAE,kCAAkC;EACxDC,oBAAoB,EAAE,kCAAkC;EACxDC,oBAAoB,EAAE,kCAAkC;EACxDC,oBAAoB,EAAE,kCAAkC;EACxDC,qBAAqB,EAAE,mCAAmC;EAC1DC,oBAAoB,EAAE,kCAAkC;EACxDC,sBAAsB,EAAE,oCAAoC;EAC5DC,kBAAkB,EAAE,8BAA8B;EAClDC,qBAAqB,EAAE,iCAAiC;EACxDC,yBAAyB,EAAE,uCAAuC;EAClEC,4BAA4B,EAAE,0CAA0C;EACxEC,2BAA2B,EAAE,yCAAyC;EACtEC,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,cAAc;EACpBC,OAAO,EAAE,iBAAiB;EAC1BC,cAAc,EAAE,wBAAwB;EACxCC,yBAAyB,EAAE,qCAAqC;EAChEC,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE,eAAe;EACtBC,KAAK,EAAE,eAAe;EACtBC,KAAK,EAAE,eAAe;EACtBC,IAAI,EAAE,cAAc;EACpBC,MAAM,EAAE,gBAAgB;EACxBC,WAAW,EAAE,uBAAuB;EACpCC,WAAW,EAAE,uBAAuB;EACpCC,QAAQ,EAAE,6BAA6B;EACvCC,WAAW,EAAE,uBAAuB;EACpCC,WAAW,EAAE,uBAAuB;EACpCC,OAAO,EAAE,iBAAiB;EAC1BC,WAAW,EAAE,uBAAuB;EACpCC,cAAc,EAAE,0BAA0B;EAC1CC,WAAW,EAAE,uBAAuB;EACpCC,cAAc,EAAE,0BAA0B;EAC1CC,cAAc,EAAE,0BAA0B;EAC1CC,UAAU,EAAE,oBAAoB;EAChCC,cAAc,EAAE,0BAA0B;EAC1CC,aAAa,EAAE,uBAAuB;EACtCC,gBAAgB,EAAE,0BAA0B;EAC5CC,WAAW,EAAE,qBAAqB;EAClCC,KAAK,EAAE,eAAe;EACtBC,WAAW,EAAE,uBAAuB;EACpCC,UAAU,EAAE,sBAAsB;EAClCC,UAAU,EAAE,sBAAsB;EAClCC,eAAe,EAAE,yBAAyB;EAC1CC,UAAU,EAAE,oBAAoB;EAChCC,MAAM,EAAE,gBAAgB;EACxBC,UAAU,EAAE,sBAAsB;EAClCC,UAAU,EAAE,sBAAsB;EAClCC,WAAW,EAAE,uBAAuB;EACpCC,UAAU,EAAE,oBAAoB;EAChCC,WAAW,EAAE,qBAAqB;EAClCC,gBAAgB,EAAE,4BAA4B;EAC9CC,iBAAiB,EAAE,6BAA6B;EAChDC,MAAM,EAAE,gBAAgB;EACxBC,WAAW,EAAE,qBAAqB;EAClCC,aAAa,EAAE,yBAAyB;EACxCC,UAAU,EAAE,sBAAsB;EAClCC,WAAW,EAAE,qBAAqB;EAClCC,gBAAgB,EAAE,4BAA4B;EAC9CC,gBAAgB,EAAE,4BAA4B;EAC9CC,gBAAgB,EAAE,4BAA4B;EAC9CC,eAAe,EAAE,2BAA2B;EAC5CC,gBAAgB,EAAE,4BAA4B;EAC9CC,gBAAgB,EAAE,4BAA4B;EAC9CC,YAAY,EAAE,sBAAsB;EACpCC,0BAA0B,EAAE,wCAAwC;EACpEC,0BAA0B,EAAE,wCAAwC;EACpEC,iBAAiB,EAAE,6BAA6B;EAChDC,YAAY,EAAE,sBAAsB;EACpCC,cAAc,EAAE,wBAAwB;EACxCC,sBAAsB,EAAE,kCAAkC;EAC1DC,0BAA0B,EAAE,wCAAwC;EACpEC,0BAA0B,EAAE,wCAAwC;EACpEC,wBAAwB,EAAE,sCAAsC;EAChEC,kBAAkB,EAAE,8BAA8B;EAClDC,yBAAyB,EAAE,uCAAuC;EAClEC,sBAAsB,EAAE,oCAAoC;EAC5DC,sBAAsB,EAAE,oCAAoC;EAC5DC,sBAAsB,EAAE,oCAAoC;EAC5DC,sBAAsB,EAAE,oCAAoC;EAC5DC,uBAAuB,EAAE,qCAAqC;EAC9DC,sBAAsB,EAAE,oCAAoC;EAC5DC,wBAAwB,EAAE,sCAAsC;EAChEC,oBAAoB,EAAE,gCAAgC;EACtDC,uBAAuB,EAAE,mCAAmC;EAC5DC,2BAA2B,EAAE,yCAAyC;EACtEC,8BAA8B,EAAE,4CAA4C;EAC5EC,6BAA6B,EAAE,2CAA2C;EAC1EC,WAAW,EAAE,qBAAqB;EAClCC,WAAW,EAAE,uBAAuB;EACpCC,WAAW,EAAE,uBAAuB;EACpCC,WAAW,EAAE,uBAAuB;EACpCC,aAAa,EAAE,yBAAyB;EACxCC,KAAK,EAAE,eAAe;EACtBC,YAAY,EAAE,wBAAwB;EACtCC,SAAS,EAAE,qBAAqB;EAChCC,OAAO,EAAE,mBAAmB;EAC5BC,OAAO,EAAE,mBAAmB;EAC5BC,SAAS,EAAE,qBAAqB;EAChCC,SAAS,EAAE,qBAAqB;EAChCC,QAAQ,EAAE,oBAAoB;EAC9BC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,oBAAoB;EAC9BC,QAAQ,EAAE,oBAAoB;EAC9BC,SAAS,EAAE,qBAAqB;EAChCC,IAAI,EAAE,cAAc;EACpBC,QAAQ,EAAE,kBAAkB;EAC5BC,aAAa,EAAE,uBAAuB;EACtCC,SAAS,EAAE,mBAAmB;EAC9BC,YAAY,EAAE,sBAAsB;EACpCC,iBAAiB,EAAE,2BAA2B;EAC9CC,cAAc,EAAE;AACpB,CAAC;AAED,SAAS/M,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}