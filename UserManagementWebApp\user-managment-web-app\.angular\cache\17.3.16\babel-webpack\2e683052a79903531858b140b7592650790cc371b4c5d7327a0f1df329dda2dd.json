{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AmplifyErrorCode } from '../../../types/errors.mjs';\nimport { assert } from '../../../errors/errorHelpers.mjs';\nimport { updateEndpoint } from '../apis/updateEndpoint.mjs';\nimport { getEndpointId } from './getEndpointId.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Resolves an endpoint id from cache or prepare via updateEndpoint if one does not already exist,\n * which will generate and cache an endpoint id between calls.\n *\n * @internal\n */\nconst resolveEndpointId = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* ({\n    address,\n    appId,\n    category,\n    channelType,\n    credentials,\n    identityId,\n    region,\n    userAgentValue\n  }) {\n    let endpointId = yield getEndpointId(appId, category);\n    if (!endpointId) {\n      yield updateEndpoint({\n        address,\n        appId,\n        category,\n        channelType,\n        credentials,\n        identityId,\n        region,\n        userAgentValue\n      });\n      endpointId = yield getEndpointId(appId, category);\n    }\n    assert(!!endpointId, AmplifyErrorCode.NoEndpointId);\n    return endpointId;\n  });\n  return function resolveEndpointId(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { resolveEndpointId };", "map": {"version": 3, "names": ["AmplifyErrorCode", "assert", "updateEndpoint", "getEndpointId", "resolveEndpointId", "_ref", "_asyncToGenerator", "address", "appId", "category", "channelType", "credentials", "identityId", "region", "userAgentValue", "endpointId", "NoEndpointId", "_x", "apply", "arguments"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/providers/pinpoint/utils/resolveEndpointId.mjs"], "sourcesContent": ["import { AmplifyErrorCode } from '../../../types/errors.mjs';\nimport { assert } from '../../../errors/errorHelpers.mjs';\nimport { updateEndpoint } from '../apis/updateEndpoint.mjs';\nimport { getEndpointId } from './getEndpointId.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Resolves an endpoint id from cache or prepare via updateEndpoint if one does not already exist,\n * which will generate and cache an endpoint id between calls.\n *\n * @internal\n */\nconst resolveEndpointId = async ({ address, appId, category, channelType, credentials, identityId, region, userAgentValue, }) => {\n    let endpointId = await getEndpointId(appId, category);\n    if (!endpointId) {\n        await updateEndpoint({\n            address,\n            appId,\n            category,\n            channelType,\n            credentials,\n            identityId,\n            region,\n            userAgentValue,\n        });\n        endpointId = await getEndpointId(appId, category);\n    }\n    assert(!!endpointId, AmplifyErrorCode.NoEndpointId);\n    return endpointId;\n};\n\nexport { resolveEndpointId };\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,MAAM,QAAQ,kCAAkC;AACzD,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,aAAa,QAAQ,qBAAqB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAO;IAAEC,OAAO;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,WAAW;IAAEC,UAAU;IAAEC,MAAM;IAAEC;EAAgB,CAAC,EAAK;IAC7H,IAAIC,UAAU,SAASZ,aAAa,CAACK,KAAK,EAAEC,QAAQ,CAAC;IACrD,IAAI,CAACM,UAAU,EAAE;MACb,MAAMb,cAAc,CAAC;QACjBK,OAAO;QACPC,KAAK;QACLC,QAAQ;QACRC,WAAW;QACXC,WAAW;QACXC,UAAU;QACVC,MAAM;QACNC;MACJ,CAAC,CAAC;MACFC,UAAU,SAASZ,aAAa,CAACK,KAAK,EAAEC,QAAQ,CAAC;IACrD;IACAR,MAAM,CAAC,CAAC,CAACc,UAAU,EAAEf,gBAAgB,CAACgB,YAAY,CAAC;IACnD,OAAOD,UAAU;EACrB,CAAC;EAAA,gBAjBKX,iBAAiBA,CAAAa,EAAA;IAAA,OAAAZ,IAAA,CAAAa,KAAA,OAAAC,SAAA;EAAA;AAAA,GAiBtB;AAED,SAASf,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}