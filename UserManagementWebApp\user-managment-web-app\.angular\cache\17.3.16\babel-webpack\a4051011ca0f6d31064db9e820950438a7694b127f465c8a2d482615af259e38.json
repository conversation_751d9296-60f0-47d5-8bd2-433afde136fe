{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { AuthAction } from '@aws-amplify/core/internals/utils';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../../../providers/cognito/types/errors.mjs';\nimport { createRespondToAuthChallengeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createRespondToAuthChallengeClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { getAuthenticationHelper } from '../../../providers/cognito/utils/srp/getAuthenticationHelper.mjs';\nimport '../../../providers/cognito/utils/srp/constants.mjs';\nimport '@aws-crypto/sha256-js';\nimport { getUserContextData } from '../../../providers/cognito/utils/userContextData.mjs';\nimport { setActiveSignInUsername } from '../../../providers/cognito/utils/setActiveSignInUsername.mjs';\nimport { retryOnResourceNotFoundException } from '../../../providers/cognito/utils/retryOnResourceNotFoundException.mjs';\nimport { handlePasswordVerifierChallenge } from '../../../providers/cognito/utils/handlePasswordVerifierChallenge.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Handles the SELECT_CHALLENGE response specifically for Password SRP authentication.\n * This function combines the SELECT_CHALLENGE flow with Password SRP protocol.\n *\n * @param {string} username - The username for authentication\n * @param {string} password - The user's password\n * @param {ClientMetadata} [clientMetadata] - Optional metadata to be sent with auth requests\n * @param {CognitoUserPoolConfig} config - Cognito User Pool configuration\n * @param {string} session - The current authentication session token\n * @param {AuthTokenOrchestrator} tokenOrchestrator - Token orchestrator for managing auth tokens\n *\n * @returns {Promise<RespondToAuthChallengeCommandOutput>} The challenge response\n */\nfunction handleSelectChallengeWithPasswordSRP(_x, _x2, _x3, _x4, _x5, _x6) {\n  return _handleSelectChallengeWithPasswordSRP.apply(this, arguments);\n}\nfunction _handleSelectChallengeWithPasswordSRP() {\n  _handleSelectChallengeWithPasswordSRP = _asyncToGenerator(function* (username, password, clientMetadata, config, session, tokenOrchestrator) {\n    const {\n      userPoolId,\n      userPoolClientId,\n      userPoolEndpoint\n    } = config;\n    const userPoolName = userPoolId.split('_')[1] || '';\n    const authenticationHelper = yield getAuthenticationHelper(userPoolName);\n    const authParameters = {\n      ANSWER: 'PASSWORD_SRP',\n      USERNAME: username,\n      SRP_A: authenticationHelper.A.toString(16)\n    };\n    const userContextData = getUserContextData({\n      username,\n      userPoolId,\n      userPoolClientId\n    });\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n      endpointResolver: createCognitoUserPoolEndpointResolver({\n        endpointOverride: userPoolEndpoint\n      })\n    });\n    const response = yield respondToAuthChallenge({\n      region: getRegionFromUserPoolId(userPoolId),\n      userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn)\n    }, {\n      ChallengeName: 'SELECT_CHALLENGE',\n      ChallengeResponses: authParameters,\n      ClientId: userPoolClientId,\n      ClientMetadata: clientMetadata,\n      Session: session,\n      UserContextData: userContextData\n    });\n    const activeUsername = response.ChallengeParameters?.USERNAME ?? username;\n    setActiveSignInUsername(activeUsername);\n    if (response.ChallengeName === 'PASSWORD_VERIFIER') {\n      return retryOnResourceNotFoundException(handlePasswordVerifierChallenge, [password, response.ChallengeParameters, clientMetadata, response.Session, authenticationHelper, config, tokenOrchestrator], activeUsername, tokenOrchestrator);\n    }\n    return response;\n  });\n  return _handleSelectChallengeWithPasswordSRP.apply(this, arguments);\n}\nexport { handleSelectChallengeWithPasswordSRP };", "map": {"version": 3, "names": ["AuthAction", "createRespondToAuthChallengeClient", "createCognitoUserPoolEndpointResolver", "getRegionFromUserPoolId", "getAuthUserAgentValue", "getAuthenticationHelper", "getUserContextData", "setActiveSignInUsername", "retryOnResourceNotFoundException", "handlePasswordVerifierChallenge", "handleSelectChallengeWithPasswordSRP", "_x", "_x2", "_x3", "_x4", "_x5", "_x6", "_handleSelectChallengeWithPasswordSRP", "apply", "arguments", "_asyncToGenerator", "username", "password", "clientMetadata", "config", "session", "tokenOrchestrator", "userPoolId", "userPoolClientId", "userPoolEndpoint", "userPoolName", "split", "authenticationHelper", "authParameters", "ANSWER", "USERNAME", "SRP_A", "A", "toString", "userContextData", "respondToAuthChallenge", "endpointResolver", "endpointOverride", "response", "region", "userAgentValue", "ConfirmSignIn", "ChallengeName", "ChallengeResponses", "ClientId", "ClientMetadata", "Session", "UserContextData", "activeUsername", "ChallengeParameters"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/client/flows/userAuth/handleSelectChallengeWithPasswordSRP.mjs"], "sourcesContent": ["import { AuthAction } from '@aws-amplify/core/internals/utils';\nimport '@aws-amplify/core/internals/aws-client-utils/composers';\nimport '@aws-amplify/core/internals/aws-client-utils';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/shared/handler/cognitoUserPoolTransferHandler.mjs';\nimport '../../../foundation/factories/serviceClients/cognitoIdentityProvider/constants.mjs';\nimport '../../../common/AuthErrorStrings.mjs';\nimport '../../../errors/types/validation.mjs';\nimport '../../../providers/cognito/types/errors.mjs';\nimport { createRespondToAuthChallengeClient } from '../../../foundation/factories/serviceClients/cognitoIdentityProvider/createRespondToAuthChallengeClient.mjs';\nimport { createCognitoUserPoolEndpointResolver } from '../../../providers/cognito/factories/createCognitoUserPoolEndpointResolver.mjs';\nimport { getRegionFromUserPoolId } from '../../../foundation/parsers/regionParsers.mjs';\nimport { getAuthUserAgentValue } from '../../../utils/getAuthUserAgentValue.mjs';\nimport { getAuthenticationHelper } from '../../../providers/cognito/utils/srp/getAuthenticationHelper.mjs';\nimport '../../../providers/cognito/utils/srp/constants.mjs';\nimport '@aws-crypto/sha256-js';\nimport { getUserContextData } from '../../../providers/cognito/utils/userContextData.mjs';\nimport { setActiveSignInUsername } from '../../../providers/cognito/utils/setActiveSignInUsername.mjs';\nimport { retryOnResourceNotFoundException } from '../../../providers/cognito/utils/retryOnResourceNotFoundException.mjs';\nimport { handlePasswordVerifierChallenge } from '../../../providers/cognito/utils/handlePasswordVerifierChallenge.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Handles the SELECT_CHALLENGE response specifically for Password SRP authentication.\n * This function combines the SELECT_CHALLENGE flow with Password SRP protocol.\n *\n * @param {string} username - The username for authentication\n * @param {string} password - The user's password\n * @param {ClientMetadata} [clientMetadata] - Optional metadata to be sent with auth requests\n * @param {CognitoUserPoolConfig} config - Cognito User Pool configuration\n * @param {string} session - The current authentication session token\n * @param {AuthTokenOrchestrator} tokenOrchestrator - Token orchestrator for managing auth tokens\n *\n * @returns {Promise<RespondToAuthChallengeCommandOutput>} The challenge response\n */\nasync function handleSelectChallengeWithPasswordSRP(username, password, clientMetadata, config, session, tokenOrchestrator) {\n    const { userPoolId, userPoolClientId, userPoolEndpoint } = config;\n    const userPoolName = userPoolId.split('_')[1] || '';\n    const authenticationHelper = await getAuthenticationHelper(userPoolName);\n    const authParameters = {\n        ANSWER: 'PASSWORD_SRP',\n        USERNAME: username,\n        SRP_A: authenticationHelper.A.toString(16),\n    };\n    const userContextData = getUserContextData({\n        username,\n        userPoolId,\n        userPoolClientId,\n    });\n    const respondToAuthChallenge = createRespondToAuthChallengeClient({\n        endpointResolver: createCognitoUserPoolEndpointResolver({\n            endpointOverride: userPoolEndpoint,\n        }),\n    });\n    const response = await respondToAuthChallenge({\n        region: getRegionFromUserPoolId(userPoolId),\n        userAgentValue: getAuthUserAgentValue(AuthAction.ConfirmSignIn),\n    }, {\n        ChallengeName: 'SELECT_CHALLENGE',\n        ChallengeResponses: authParameters,\n        ClientId: userPoolClientId,\n        ClientMetadata: clientMetadata,\n        Session: session,\n        UserContextData: userContextData,\n    });\n    const activeUsername = response.ChallengeParameters?.USERNAME ?? username;\n    setActiveSignInUsername(activeUsername);\n    if (response.ChallengeName === 'PASSWORD_VERIFIER') {\n        return retryOnResourceNotFoundException(handlePasswordVerifierChallenge, [\n            password,\n            response.ChallengeParameters,\n            clientMetadata,\n            response.Session,\n            authenticationHelper,\n            config,\n            tokenOrchestrator,\n        ], activeUsername, tokenOrchestrator);\n    }\n    return response;\n}\n\nexport { handleSelectChallengeWithPasswordSRP };\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,mCAAmC;AAC9D,OAAO,wDAAwD;AAC/D,OAAO,8CAA8C;AACrD,OAAO,wHAAwH;AAC/H,OAAO,oFAAoF;AAC3F,OAAO,sCAAsC;AAC7C,OAAO,sCAAsC;AAC7C,OAAO,6CAA6C;AACpD,SAASC,kCAAkC,QAAQ,6GAA6G;AAChK,SAASC,qCAAqC,QAAQ,gFAAgF;AACtI,SAASC,uBAAuB,QAAQ,+CAA+C;AACvF,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,uBAAuB,QAAQ,kEAAkE;AAC1G,OAAO,oDAAoD;AAC3D,OAAO,uBAAuB;AAC9B,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,uBAAuB,QAAQ,8DAA8D;AACtG,SAASC,gCAAgC,QAAQ,uEAAuE;AACxH,SAASC,+BAA+B,QAAQ,sEAAsE;;AAEtH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,SAaeC,oCAAoCA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,qCAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,sCAAA;EAAAA,qCAAA,GAAAG,iBAAA,CAAnD,WAAoDC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,MAAM,EAAEC,OAAO,EAAEC,iBAAiB,EAAE;IACxH,MAAM;MAAEC,UAAU;MAAEC,gBAAgB;MAAEC;IAAiB,CAAC,GAAGL,MAAM;IACjE,MAAMM,YAAY,GAAGH,UAAU,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;IACnD,MAAMC,oBAAoB,SAAS3B,uBAAuB,CAACyB,YAAY,CAAC;IACxE,MAAMG,cAAc,GAAG;MACnBC,MAAM,EAAE,cAAc;MACtBC,QAAQ,EAAEd,QAAQ;MAClBe,KAAK,EAAEJ,oBAAoB,CAACK,CAAC,CAACC,QAAQ,CAAC,EAAE;IAC7C,CAAC;IACD,MAAMC,eAAe,GAAGjC,kBAAkB,CAAC;MACvCe,QAAQ;MACRM,UAAU;MACVC;IACJ,CAAC,CAAC;IACF,MAAMY,sBAAsB,GAAGvC,kCAAkC,CAAC;MAC9DwC,gBAAgB,EAAEvC,qCAAqC,CAAC;QACpDwC,gBAAgB,EAAEb;MACtB,CAAC;IACL,CAAC,CAAC;IACF,MAAMc,QAAQ,SAASH,sBAAsB,CAAC;MAC1CI,MAAM,EAAEzC,uBAAuB,CAACwB,UAAU,CAAC;MAC3CkB,cAAc,EAAEzC,qBAAqB,CAACJ,UAAU,CAAC8C,aAAa;IAClE,CAAC,EAAE;MACCC,aAAa,EAAE,kBAAkB;MACjCC,kBAAkB,EAAEf,cAAc;MAClCgB,QAAQ,EAAErB,gBAAgB;MAC1BsB,cAAc,EAAE3B,cAAc;MAC9B4B,OAAO,EAAE1B,OAAO;MAChB2B,eAAe,EAAEb;IACrB,CAAC,CAAC;IACF,MAAMc,cAAc,GAAGV,QAAQ,CAACW,mBAAmB,EAAEnB,QAAQ,IAAId,QAAQ;IACzEd,uBAAuB,CAAC8C,cAAc,CAAC;IACvC,IAAIV,QAAQ,CAACI,aAAa,KAAK,mBAAmB,EAAE;MAChD,OAAOvC,gCAAgC,CAACC,+BAA+B,EAAE,CACrEa,QAAQ,EACRqB,QAAQ,CAACW,mBAAmB,EAC5B/B,cAAc,EACdoB,QAAQ,CAACQ,OAAO,EAChBnB,oBAAoB,EACpBR,MAAM,EACNE,iBAAiB,CACpB,EAAE2B,cAAc,EAAE3B,iBAAiB,CAAC;IACzC;IACA,OAAOiB,QAAQ;EACnB,CAAC;EAAA,OAAA1B,qCAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAAST,oCAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}