{"ast": null, "code": "import { SessionListener } from './SessionListener.mjs';\nexport { SESSION_START_EVENT, SESSION_STOP_EVENT } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst sessionListener = new SessionListener();\nexport { sessionListener };", "map": {"version": 3, "names": ["SessionListener", "SESSION_START_EVENT", "SESSION_STOP_EVENT", "sessionListener"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/sessionListener/index.mjs"], "sourcesContent": ["import { SessionListener } from './SessionListener.mjs';\nexport { SESSION_START_EVENT, SESSION_STOP_EVENT } from './constants.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst sessionListener = new SessionListener();\n\nexport { sessionListener };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,uBAAuB;AACvD,SAASC,mBAAmB,EAAEC,kBAAkB,QAAQ,iBAAiB;;AAEzE;AACA;AACA,MAAMC,eAAe,GAAG,IAAIH,eAAe,CAAC,CAAC;AAE7C,SAASG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}