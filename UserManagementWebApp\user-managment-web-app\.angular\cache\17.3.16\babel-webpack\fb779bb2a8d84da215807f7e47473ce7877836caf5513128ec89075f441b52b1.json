{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { DetectFacesRequest, DetectFacesResponse } from \"../models/models_0\";\nimport { deserializeAws_json1_1DetectFacesCommand, serializeAws_json1_1DetectFacesCommand } from \"../protocols/Aws_json1_1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Detects faces within an image that is provided as input.</p>\n *\n *          <p>\n *             <code>DetectFaces</code> detects the 100 largest faces in the image. For each face\n *       detected, the operation returns face details. These details include a bounding box of the\n *       face, a confidence value (that the bounding box contains a face), and a fixed set of\n *       attributes such as facial landmarks (for example, coordinates of eye and mouth),\n *       presence of beard, sunglasses, and so on. </p>\n *          <p>The face-detection algorithm is most effective on frontal faces. For non-frontal or\n *       obscured faces, the algorithm might not detect the faces or might detect faces with lower\n *       confidence. </p>\n *          <p>You pass the input image either as base64-encoded image bytes or as a reference to an\n *       image in an Amazon S3 bucket. If you use the AWS CLI\n *        to call Amazon Rekognition operations, passing image bytes is not\n *       supported. The image must be either a PNG or JPEG formatted file. </p>\n *\n *          <note>\n *             <p>This is a stateless API operation. That is, the operation does not persist any\n *         data.</p>\n *          </note>\n *\n *          <p>This operation requires permissions to perform the\n *       <code>rekognition:DetectFaces</code> action. </p>\n */\nvar DetectFacesCommand = /** @class */function (_super) {\n  __extends(DetectFacesCommand, _super);\n  // Start section: command_properties\n  // End section: command_properties\n  function DetectFacesCommand(input) {\n    var _this =\n    // Start section: command_constructor\n    _super.call(this) || this;\n    _this.input = input;\n    return _this;\n    // End section: command_constructor\n  }\n  /**\n   * @internal\n   */\n  DetectFacesCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n    this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n    var stack = clientStack.concat(this.middlewareStack);\n    var logger = configuration.logger;\n    var clientName = \"RekognitionClient\";\n    var commandName = \"DetectFacesCommand\";\n    var handlerExecutionContext = {\n      logger: logger,\n      clientName: clientName,\n      commandName: commandName,\n      inputFilterSensitiveLog: DetectFacesRequest.filterSensitiveLog,\n      outputFilterSensitiveLog: DetectFacesResponse.filterSensitiveLog\n    };\n    var requestHandler = configuration.requestHandler;\n    return stack.resolve(function (request) {\n      return requestHandler.handle(request.request, options || {});\n    }, handlerExecutionContext);\n  };\n  DetectFacesCommand.prototype.serialize = function (input, context) {\n    return serializeAws_json1_1DetectFacesCommand(input, context);\n  };\n  DetectFacesCommand.prototype.deserialize = function (output, context) {\n    return deserializeAws_json1_1DetectFacesCommand(output, context);\n  };\n  return DetectFacesCommand;\n}($Command);\nexport { DetectFacesCommand };", "map": {"version": 3, "names": ["__extends", "DetectFacesRequest", "DetectFacesResponse", "deserializeAws_json1_1DetectFacesCommand", "serializeAws_json1_1DetectFacesCommand", "getSerdePlugin", "Command", "$Command", "DetectFacesCommand", "_super", "input", "_this", "call", "prototype", "resolveMiddleware", "clientStack", "configuration", "options", "middlewareStack", "use", "serialize", "deserialize", "stack", "concat", "logger", "clientName", "commandName", "handlerExecutionContext", "inputFilterSensitiveLog", "filterSensitiveLog", "outputFilterSensitiveLog", "requestHandler", "resolve", "request", "handle", "context", "output"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-rekognition/dist/es/commands/DetectFacesCommand.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { DetectFacesRequest, DetectFacesResponse } from \"../models/models_0\";\nimport { deserializeAws_json1_1DetectFacesCommand, serializeAws_json1_1DetectFacesCommand, } from \"../protocols/Aws_json1_1\";\nimport { getSerdePlugin } from \"@aws-sdk/middleware-serde\";\nimport { Command as $Command } from \"@aws-sdk/smithy-client\";\n/**\n * <p>Detects faces within an image that is provided as input.</p>\n *\n *          <p>\n *             <code>DetectFaces</code> detects the 100 largest faces in the image. For each face\n *       detected, the operation returns face details. These details include a bounding box of the\n *       face, a confidence value (that the bounding box contains a face), and a fixed set of\n *       attributes such as facial landmarks (for example, coordinates of eye and mouth),\n *       presence of beard, sunglasses, and so on. </p>\n *          <p>The face-detection algorithm is most effective on frontal faces. For non-frontal or\n *       obscured faces, the algorithm might not detect the faces or might detect faces with lower\n *       confidence. </p>\n *          <p>You pass the input image either as base64-encoded image bytes or as a reference to an\n *       image in an Amazon S3 bucket. If you use the AWS CLI\n *        to call Amazon Rekognition operations, passing image bytes is not\n *       supported. The image must be either a PNG or JPEG formatted file. </p>\n *\n *          <note>\n *             <p>This is a stateless API operation. That is, the operation does not persist any\n *         data.</p>\n *          </note>\n *\n *          <p>This operation requires permissions to perform the\n *       <code>rekognition:DetectFaces</code> action. </p>\n */\nvar DetectFacesCommand = /** @class */ (function (_super) {\n    __extends(DetectFacesCommand, _super);\n    // Start section: command_properties\n    // End section: command_properties\n    function DetectFacesCommand(input) {\n        var _this = \n        // Start section: command_constructor\n        _super.call(this) || this;\n        _this.input = input;\n        return _this;\n        // End section: command_constructor\n    }\n    /**\n     * @internal\n     */\n    DetectFacesCommand.prototype.resolveMiddleware = function (clientStack, configuration, options) {\n        this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));\n        var stack = clientStack.concat(this.middlewareStack);\n        var logger = configuration.logger;\n        var clientName = \"RekognitionClient\";\n        var commandName = \"DetectFacesCommand\";\n        var handlerExecutionContext = {\n            logger: logger,\n            clientName: clientName,\n            commandName: commandName,\n            inputFilterSensitiveLog: DetectFacesRequest.filterSensitiveLog,\n            outputFilterSensitiveLog: DetectFacesResponse.filterSensitiveLog,\n        };\n        var requestHandler = configuration.requestHandler;\n        return stack.resolve(function (request) {\n            return requestHandler.handle(request.request, options || {});\n        }, handlerExecutionContext);\n    };\n    DetectFacesCommand.prototype.serialize = function (input, context) {\n        return serializeAws_json1_1DetectFacesCommand(input, context);\n    };\n    DetectFacesCommand.prototype.deserialize = function (output, context) {\n        return deserializeAws_json1_1DetectFacesCommand(output, context);\n    };\n    return DetectFacesCommand;\n}($Command));\nexport { DetectFacesCommand };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,kBAAkB,EAAEC,mBAAmB,QAAQ,oBAAoB;AAC5E,SAASC,wCAAwC,EAAEC,sCAAsC,QAAS,0BAA0B;AAC5H,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,OAAO,IAAIC,QAAQ,QAAQ,wBAAwB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,kBAAkB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACtDT,SAAS,CAACQ,kBAAkB,EAAEC,MAAM,CAAC;EACrC;EACA;EACA,SAASD,kBAAkBA,CAACE,KAAK,EAAE;IAC/B,IAAIC,KAAK;IACT;IACAF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACzBD,KAAK,CAACD,KAAK,GAAGA,KAAK;IACnB,OAAOC,KAAK;IACZ;EACJ;EACA;AACJ;AACA;EACIH,kBAAkB,CAACK,SAAS,CAACC,iBAAiB,GAAG,UAAUC,WAAW,EAAEC,aAAa,EAAEC,OAAO,EAAE;IAC5F,IAAI,CAACC,eAAe,CAACC,GAAG,CAACd,cAAc,CAACW,aAAa,EAAE,IAAI,CAACI,SAAS,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC;IACzF,IAAIC,KAAK,GAAGP,WAAW,CAACQ,MAAM,CAAC,IAAI,CAACL,eAAe,CAAC;IACpD,IAAIM,MAAM,GAAGR,aAAa,CAACQ,MAAM;IACjC,IAAIC,UAAU,GAAG,mBAAmB;IACpC,IAAIC,WAAW,GAAG,oBAAoB;IACtC,IAAIC,uBAAuB,GAAG;MAC1BH,MAAM,EAAEA,MAAM;MACdC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA,WAAW;MACxBE,uBAAuB,EAAE3B,kBAAkB,CAAC4B,kBAAkB;MAC9DC,wBAAwB,EAAE5B,mBAAmB,CAAC2B;IAClD,CAAC;IACD,IAAIE,cAAc,GAAGf,aAAa,CAACe,cAAc;IACjD,OAAOT,KAAK,CAACU,OAAO,CAAC,UAAUC,OAAO,EAAE;MACpC,OAAOF,cAAc,CAACG,MAAM,CAACD,OAAO,CAACA,OAAO,EAAEhB,OAAO,IAAI,CAAC,CAAC,CAAC;IAChE,CAAC,EAAEU,uBAAuB,CAAC;EAC/B,CAAC;EACDnB,kBAAkB,CAACK,SAAS,CAACO,SAAS,GAAG,UAAUV,KAAK,EAAEyB,OAAO,EAAE;IAC/D,OAAO/B,sCAAsC,CAACM,KAAK,EAAEyB,OAAO,CAAC;EACjE,CAAC;EACD3B,kBAAkB,CAACK,SAAS,CAACQ,WAAW,GAAG,UAAUe,MAAM,EAAED,OAAO,EAAE;IAClE,OAAOhC,wCAAwC,CAACiC,MAAM,EAAED,OAAO,CAAC;EACpE,CAAC;EACD,OAAO3B,kBAAkB;AAC7B,CAAC,CAACD,QAAQ,CAAE;AACZ,SAASC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}