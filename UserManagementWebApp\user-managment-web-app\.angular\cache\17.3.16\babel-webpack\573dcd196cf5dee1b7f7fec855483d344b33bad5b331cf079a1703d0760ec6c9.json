{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass NonRetryableError extends Error {\n  constructor() {\n    super(...arguments);\n    this.nonRetryable = true;\n  }\n}\nexport { NonRetryableError };", "map": {"version": 3, "names": ["NonRetryableError", "Error", "constructor", "arguments", "nonRetryable"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/retry/NonRetryableError.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nclass NonRetryableError extends Error {\n    constructor() {\n        super(...arguments);\n        this.nonRetryable = true;\n    }\n}\n\nexport { NonRetryableError };\n"], "mappings": "AAAA;AACA;AACA,MAAMA,iBAAiB,SAASC,KAAK,CAAC;EAClCC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,YAAY,GAAG,IAAI;EAC5B;AACJ;AAEA,SAASJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}