{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport bowser from \"bowser\";\nexport var defaultUserAgent = function (_a) {\n  var serviceId = _a.serviceId,\n    clientVersion = _a.clientVersion;\n  return function () {\n    return __awaiter(void 0, void 0, void 0, function () {\n      var parsedUA, sections;\n      var _a, _b, _c, _d, _e, _f, _g;\n      return __generator(this, function (_h) {\n        parsedUA = typeof window !== \"undefined\" && ((_a = window === null || window === void 0 ? void 0 : window.navigator) === null || _a === void 0 ? void 0 : _a.userAgent) ? bowser.parse(window.navigator.userAgent) : undefined;\n        sections = [[\"aws-sdk-js\", clientVersion], [\"os/\".concat(((_b = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.os) === null || _b === void 0 ? void 0 : _b.name) || \"other\"), (_c = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.os) === null || _c === void 0 ? void 0 : _c.version], [\"lang/js\"], [\"md/browser\", \"\".concat((_e = (_d = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.browser) === null || _d === void 0 ? void 0 : _d.name) !== null && _e !== void 0 ? _e : \"unknown\", \"_\").concat((_g = (_f = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.browser) === null || _f === void 0 ? void 0 : _f.version) !== null && _g !== void 0 ? _g : \"unknown\")]];\n        if (serviceId) {\n          sections.push([\"api/\".concat(serviceId), clientVersion]);\n        }\n        return [2, sections];\n      });\n    });\n  };\n};", "map": {"version": 3, "names": ["__awaiter", "__generator", "bowser", "defaultUserAgent", "_a", "serviceId", "clientVersion", "parsedUA", "sections", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "window", "navigator", "userAgent", "parse", "undefined", "concat", "os", "name", "version", "browser", "push"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-sdk/client-lex-runtime-v2/node_modules/@aws-sdk/util-user-agent-browser/dist-es/index.js"], "sourcesContent": ["import { __awaiter, __generator } from \"tslib\";\nimport bowser from \"bowser\";\nexport var defaultUserAgent = function (_a) {\n    var serviceId = _a.serviceId, clientVersion = _a.clientVersion;\n    return function () { return __awaiter(void 0, void 0, void 0, function () {\n        var parsedUA, sections;\n        var _a, _b, _c, _d, _e, _f, _g;\n        return __generator(this, function (_h) {\n            parsedUA = typeof window !== \"undefined\" && ((_a = window === null || window === void 0 ? void 0 : window.navigator) === null || _a === void 0 ? void 0 : _a.userAgent)\n                ? bowser.parse(window.navigator.userAgent)\n                : undefined;\n            sections = [\n                [\"aws-sdk-js\", clientVersion],\n                [\"os/\".concat(((_b = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.os) === null || _b === void 0 ? void 0 : _b.name) || \"other\"), (_c = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.os) === null || _c === void 0 ? void 0 : _c.version],\n                [\"lang/js\"],\n                [\"md/browser\", \"\".concat((_e = (_d = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.browser) === null || _d === void 0 ? void 0 : _d.name) !== null && _e !== void 0 ? _e : \"unknown\", \"_\").concat((_g = (_f = parsedUA === null || parsedUA === void 0 ? void 0 : parsedUA.browser) === null || _f === void 0 ? void 0 : _f.version) !== null && _g !== void 0 ? _g : \"unknown\")],\n            ];\n            if (serviceId) {\n                sections.push([\"api/\".concat(serviceId), clientVersion]);\n            }\n            return [2, sections];\n        });\n    }); };\n};\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC9C,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAO,IAAIC,gBAAgB,GAAG,SAAAA,CAAUC,EAAE,EAAE;EACxC,IAAIC,SAAS,GAAGD,EAAE,CAACC,SAAS;IAAEC,aAAa,GAAGF,EAAE,CAACE,aAAa;EAC9D,OAAO,YAAY;IAAE,OAAON,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MACtE,IAAIO,QAAQ,EAAEC,QAAQ;MACtB,IAAIJ,EAAE,EAAEK,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;MAC9B,OAAOb,WAAW,CAAC,IAAI,EAAE,UAAUc,EAAE,EAAE;QACnCR,QAAQ,GAAG,OAAOS,MAAM,KAAK,WAAW,KAAK,CAACZ,EAAE,GAAGY,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,SAAS,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,SAAS,CAAC,GACjKhB,MAAM,CAACiB,KAAK,CAACH,MAAM,CAACC,SAAS,CAACC,SAAS,CAAC,GACxCE,SAAS;QACfZ,QAAQ,GAAG,CACP,CAAC,YAAY,EAAEF,aAAa,CAAC,EAC7B,CAAC,KAAK,CAACe,MAAM,CAAC,CAAC,CAACZ,EAAE,GAAGF,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACe,EAAE,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,IAAI,KAAK,OAAO,CAAC,EAAE,CAACb,EAAE,GAAGH,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACe,EAAE,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,OAAO,CAAC,EACxQ,CAAC,SAAS,CAAC,EACX,CAAC,YAAY,EAAE,EAAE,CAACH,MAAM,CAAC,CAACT,EAAE,GAAG,CAACD,EAAE,GAAGJ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACkB,OAAO,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,IAAI,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,SAAS,EAAE,GAAG,CAAC,CAACS,MAAM,CAAC,CAACP,EAAE,GAAG,CAACD,EAAE,GAAGN,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACkB,OAAO,MAAM,IAAI,IAAIZ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,OAAO,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,SAAS,CAAC,CAAC,CACnY;QACD,IAAIT,SAAS,EAAE;UACXG,QAAQ,CAACkB,IAAI,CAAC,CAAC,MAAM,CAACL,MAAM,CAAChB,SAAS,CAAC,EAAEC,aAAa,CAAC,CAAC;QAC5D;QACA,OAAO,CAAC,CAAC,EAAEE,QAAQ,CAAC;MACxB,CAAC,CAAC;IACN,CAAC,CAAC;EAAE,CAAC;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}