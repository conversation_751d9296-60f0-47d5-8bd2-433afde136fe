{"ast": null, "code": "import '@aws-amplify/core/internals/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { isFunction } from '../../utils/utils.mjs';\nimport { propsToString } from './utils.mjs';\nfunction createAnimationCSS({\n  animations,\n  tokens\n}) {\n  let cssText = '';\n  Object.entries(animations).forEach(([key, value]) => {\n    cssText += `\\n  @keyframes ${key} {`;\n    Object.entries(value).forEach(([step, properties]) => {\n      cssText += `\\n    ${step} {\\n`;\n      const animationProperties = isFunction(properties) ? properties(tokens) : properties;\n      cssText += propsToString(animationProperties);\n      cssText += `\\n    }`;\n    });\n    cssText += `\\n  }`;\n  });\n  return cssText;\n}\nexport { createAnimationCSS };", "map": {"version": 3, "names": ["isFunction", "propsToString", "createAnimationCSS", "animations", "tokens", "cssText", "Object", "entries", "for<PERSON>ach", "key", "value", "step", "properties", "animationProperties"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/theme/createTheme/createAnimationCSS.mjs"], "sourcesContent": ["import '@aws-amplify/core/internals/utils';\nimport '../../utils/setUserAgent/constants.mjs';\nimport { isFunction } from '../../utils/utils.mjs';\nimport { propsToString } from './utils.mjs';\n\nfunction createAnimationCSS({ animations, tokens, }) {\n    let cssText = '';\n    Object.entries(animations).forEach(([key, value]) => {\n        cssText += `\\n  @keyframes ${key} {`;\n        Object.entries(value).forEach(([step, properties]) => {\n            cssText += `\\n    ${step} {\\n`;\n            const animationProperties = isFunction(properties)\n                ? properties(tokens)\n                : properties;\n            cssText += propsToString(animationProperties);\n            cssText += `\\n    }`;\n        });\n        cssText += `\\n  }`;\n    });\n    return cssText;\n}\n\nexport { createAnimationCSS };\n"], "mappings": "AAAA,OAAO,mCAAmC;AAC1C,OAAO,wCAAwC;AAC/C,SAASA,UAAU,QAAQ,uBAAuB;AAClD,SAASC,aAAa,QAAQ,aAAa;AAE3C,SAASC,kBAAkBA,CAAC;EAAEC,UAAU;EAAEC;AAAQ,CAAC,EAAE;EACjD,IAAIC,OAAO,GAAG,EAAE;EAChBC,MAAM,CAACC,OAAO,CAACJ,UAAU,CAAC,CAACK,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;IACjDL,OAAO,IAAI,kBAAkBI,GAAG,IAAI;IACpCH,MAAM,CAACC,OAAO,CAACG,KAAK,CAAC,CAACF,OAAO,CAAC,CAAC,CAACG,IAAI,EAAEC,UAAU,CAAC,KAAK;MAClDP,OAAO,IAAI,SAASM,IAAI,MAAM;MAC9B,MAAME,mBAAmB,GAAGb,UAAU,CAACY,UAAU,CAAC,GAC5CA,UAAU,CAACR,MAAM,CAAC,GAClBQ,UAAU;MAChBP,OAAO,IAAIJ,aAAa,CAACY,mBAAmB,CAAC;MAC7CR,OAAO,IAAI,SAAS;IACxB,CAAC,CAAC;IACFA,OAAO,IAAI,OAAO;EACtB,CAAC,CAAC;EACF,OAAOA,OAAO;AAClB;AAEA,SAASH,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}