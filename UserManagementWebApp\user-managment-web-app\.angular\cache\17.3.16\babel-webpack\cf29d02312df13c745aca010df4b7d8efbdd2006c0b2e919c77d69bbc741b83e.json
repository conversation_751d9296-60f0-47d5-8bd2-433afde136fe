{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { getActiveSignInUsername, getSignInResult, getSignInResultFromError } from '../utils/signInHelpers.mjs';\nimport { autoSignInStore } from '../../../client/utils/store/autoSignInStore.mjs';\nimport { setActiveSignInState, resetActiveSignInState } from '../../../client/utils/store/signInStore.mjs';\nimport { cacheCognitoTokens } from '../tokenProvider/cacheTokens.mjs';\nimport { dispatchSignedInHubEvent } from '../utils/dispatchSignedInHubEvent.mjs';\nimport '../utils/refreshAuthTokens.mjs';\nimport '../tokenProvider/errorHelpers.mjs';\nimport '../utils/types.mjs';\nimport { tokenOrchestrator } from '../tokenProvider/tokenProvider.mjs';\nimport { handleUserAuthFlow } from '../../../client/flows/userAuth/handleUserAuthFlow.mjs';\nimport { getNewDeviceMetadata } from '../utils/getNewDeviceMetadata.mjs';\nimport { resetAutoSignIn } from './autoSignIn.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Signs a user in through a registered email or phone number without a password by by receiving and entering an OTP.\n *\n * @param input - The SignInWithUserAuthInput object\n * @returns SignInWithUserAuthOutput\n * @throws service: {@link InitiateAuthException }, {@link RespondToAuthChallengeException } - Cognito service errors\n * thrown during the sign-in process.\n * @throws validation: {@link AuthValidationErrorCode  } - Validation errors thrown when either username or password -- needs to change\n *  are not defined.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nfunction signInWithUserAuth(_x) {\n  return _signInWithUserAuth.apply(this, arguments);\n}\nfunction _signInWithUserAuth() {\n  _signInWithUserAuth = _asyncToGenerator(function* (input) {\n    const {\n      username,\n      password,\n      options\n    } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    const signInDetails = {\n      loginId: username,\n      authFlowType: 'USER_AUTH'\n    };\n    assertTokenProviderConfig(authConfig);\n    const clientMetaData = options?.clientMetadata;\n    const preferredChallenge = options?.preferredChallenge;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptySignInUsername);\n    try {\n      const handleUserAuthFlowInput = {\n        username,\n        config: authConfig,\n        tokenOrchestrator,\n        clientMetadata: clientMetaData,\n        preferredChallenge,\n        password\n      };\n      const autoSignInStoreState = autoSignInStore.getState();\n      if (autoSignInStoreState.active && autoSignInStoreState.username === username) {\n        handleUserAuthFlowInput.session = autoSignInStoreState.session;\n      }\n      const response = yield handleUserAuthFlow(handleUserAuthFlowInput);\n      const activeUsername = getActiveSignInUsername(username);\n      setActiveSignInState({\n        signInSession: response.Session,\n        username: activeUsername,\n        challengeName: response.ChallengeName,\n        signInDetails\n      });\n      if (response.AuthenticationResult) {\n        yield cacheCognitoTokens({\n          username: activeUsername,\n          ...response.AuthenticationResult,\n          NewDeviceMetadata: yield getNewDeviceMetadata({\n            userPoolId: authConfig.userPoolId,\n            userPoolEndpoint: authConfig.userPoolEndpoint,\n            newDeviceMetadata: response.AuthenticationResult.NewDeviceMetadata,\n            accessToken: response.AuthenticationResult.AccessToken\n          }),\n          signInDetails\n        });\n        resetActiveSignInState();\n        yield dispatchSignedInHubEvent();\n        resetAutoSignIn();\n        return {\n          isSignedIn: true,\n          nextStep: {\n            signInStep: 'DONE'\n          }\n        };\n      }\n      return getSignInResult({\n        challengeName: response.ChallengeName,\n        challengeParameters: response.ChallengeParameters,\n        availableChallenges: 'AvailableChallenges' in response ? response.AvailableChallenges : undefined\n      });\n    } catch (error) {\n      resetActiveSignInState();\n      resetAutoSignIn();\n      assertServiceError(error);\n      const result = getSignInResultFromError(error.name);\n      if (result) return result;\n      throw error;\n    }\n  });\n  return _signInWithUserAuth.apply(this, arguments);\n}\nexport { signInWithUserAuth };", "map": {"version": 3, "names": ["Amplify", "assertTokenProviderConfig", "AuthValidationErrorCode", "assertValidationError", "assertServiceError", "getActiveSignInUsername", "getSignInResult", "getSignInResultFromError", "autoSignInStore", "setActiveSignInState", "resetActiveSignInState", "cacheCognitoTokens", "dispatchSignedInHubEvent", "tokenOrchestrator", "handleUserAuthFlow", "getNewDeviceMetadata", "resetAutoSignIn", "signInWithUserAuth", "_x", "_signInWithUserAuth", "apply", "arguments", "_asyncToGenerator", "input", "username", "password", "options", "authConfig", "getConfig", "<PERSON><PERSON>", "Cognito", "signInDetails", "loginId", "authFlowType", "clientMetaData", "clientMetadata", "preferredChallenge", "EmptySignInUsername", "handleUserAuthFlowInput", "config", "autoSignInStoreState", "getState", "active", "session", "response", "activeUsername", "signInSession", "Session", "challenge<PERSON>ame", "ChallengeName", "AuthenticationResult", "NewDeviceMetadata", "userPoolId", "userPoolEndpoint", "newDeviceMetadata", "accessToken", "AccessToken", "isSignedIn", "nextStep", "signInStep", "challengeParameters", "ChallengeParameters", "availableChallenges", "AvailableChallenges", "undefined", "error", "result", "name"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/auth/dist/esm/providers/cognito/apis/signInWithUserAuth.mjs"], "sourcesContent": ["import { Amplify } from '@aws-amplify/core';\nimport { assertTokenProviderConfig } from '@aws-amplify/core/internals/utils';\nimport { AuthValidationErrorCode } from '../../../errors/types/validation.mjs';\nimport { assertValidationError } from '../../../errors/utils/assertValidationError.mjs';\nimport { assertServiceError } from '../../../errors/utils/assertServiceError.mjs';\nimport { getActiveSignInUsername, getSignInResult, getSignInResultFromError } from '../utils/signInHelpers.mjs';\nimport { autoSignInStore } from '../../../client/utils/store/autoSignInStore.mjs';\nimport { setActiveSignInState, resetActiveSignInState } from '../../../client/utils/store/signInStore.mjs';\nimport { cacheCognitoTokens } from '../tokenProvider/cacheTokens.mjs';\nimport { dispatchSignedInHubEvent } from '../utils/dispatchSignedInHubEvent.mjs';\nimport '../utils/refreshAuthTokens.mjs';\nimport '../tokenProvider/errorHelpers.mjs';\nimport '../utils/types.mjs';\nimport { tokenOrchestrator } from '../tokenProvider/tokenProvider.mjs';\nimport { handleUserAuthFlow } from '../../../client/flows/userAuth/handleUserAuthFlow.mjs';\nimport { getNewDeviceMetadata } from '../utils/getNewDeviceMetadata.mjs';\nimport { resetAutoSignIn } from './autoSignIn.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Signs a user in through a registered email or phone number without a password by by receiving and entering an OTP.\n *\n * @param input - The SignInWithUserAuthInput object\n * @returns SignInWithUserAuthOutput\n * @throws service: {@link InitiateAuthException }, {@link RespondToAuthChallengeException } - Cognito service errors\n * thrown during the sign-in process.\n * @throws validation: {@link AuthValidationErrorCode  } - Validation errors thrown when either username or password -- needs to change\n *  are not defined.\n * @throws AuthTokenConfigException - Thrown when the token provider config is invalid.\n */\nasync function signInWithUserAuth(input) {\n    const { username, password, options } = input;\n    const authConfig = Amplify.getConfig().Auth?.Cognito;\n    const signInDetails = {\n        loginId: username,\n        authFlowType: 'USER_AUTH',\n    };\n    assertTokenProviderConfig(authConfig);\n    const clientMetaData = options?.clientMetadata;\n    const preferredChallenge = options?.preferredChallenge;\n    assertValidationError(!!username, AuthValidationErrorCode.EmptySignInUsername);\n    try {\n        const handleUserAuthFlowInput = {\n            username,\n            config: authConfig,\n            tokenOrchestrator,\n            clientMetadata: clientMetaData,\n            preferredChallenge,\n            password,\n        };\n        const autoSignInStoreState = autoSignInStore.getState();\n        if (autoSignInStoreState.active &&\n            autoSignInStoreState.username === username) {\n            handleUserAuthFlowInput.session = autoSignInStoreState.session;\n        }\n        const response = await handleUserAuthFlow(handleUserAuthFlowInput);\n        const activeUsername = getActiveSignInUsername(username);\n        setActiveSignInState({\n            signInSession: response.Session,\n            username: activeUsername,\n            challengeName: response.ChallengeName,\n            signInDetails,\n        });\n        if (response.AuthenticationResult) {\n            await cacheCognitoTokens({\n                username: activeUsername,\n                ...response.AuthenticationResult,\n                NewDeviceMetadata: await getNewDeviceMetadata({\n                    userPoolId: authConfig.userPoolId,\n                    userPoolEndpoint: authConfig.userPoolEndpoint,\n                    newDeviceMetadata: response.AuthenticationResult.NewDeviceMetadata,\n                    accessToken: response.AuthenticationResult.AccessToken,\n                }),\n                signInDetails,\n            });\n            resetActiveSignInState();\n            await dispatchSignedInHubEvent();\n            resetAutoSignIn();\n            return {\n                isSignedIn: true,\n                nextStep: { signInStep: 'DONE' },\n            };\n        }\n        return getSignInResult({\n            challengeName: response.ChallengeName,\n            challengeParameters: response.ChallengeParameters,\n            availableChallenges: 'AvailableChallenges' in response\n                ? response.AvailableChallenges\n                : undefined,\n        });\n    }\n    catch (error) {\n        resetActiveSignInState();\n        resetAutoSignIn();\n        assertServiceError(error);\n        const result = getSignInResultFromError(error.name);\n        if (result)\n            return result;\n        throw error;\n    }\n}\n\nexport { signInWithUserAuth };\n"], "mappings": ";AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,yBAAyB,QAAQ,mCAAmC;AAC7E,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,qBAAqB,QAAQ,iDAAiD;AACvF,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,uBAAuB,EAAEC,eAAe,EAAEC,wBAAwB,QAAQ,4BAA4B;AAC/G,SAASC,eAAe,QAAQ,iDAAiD;AACjF,SAASC,oBAAoB,EAAEC,sBAAsB,QAAQ,6CAA6C;AAC1G,SAASC,kBAAkB,QAAQ,kCAAkC;AACrE,SAASC,wBAAwB,QAAQ,uCAAuC;AAChF,OAAO,gCAAgC;AACvC,OAAO,mCAAmC;AAC1C,OAAO,oBAAoB;AAC3B,SAASC,iBAAiB,QAAQ,oCAAoC;AACtE,SAASC,kBAAkB,QAAQ,uDAAuD;AAC1F,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,eAAe,QAAQ,kBAAkB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,SAWeC,kBAAkBA,CAAAC,EAAA;EAAA,OAAAC,mBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,oBAAA;EAAAA,mBAAA,GAAAG,iBAAA,CAAjC,WAAkCC,KAAK,EAAE;IACrC,MAAM;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC;IAAQ,CAAC,GAAGH,KAAK;IAC7C,MAAMI,UAAU,GAAG3B,OAAO,CAAC4B,SAAS,CAAC,CAAC,CAACC,IAAI,EAAEC,OAAO;IACpD,MAAMC,aAAa,GAAG;MAClBC,OAAO,EAAER,QAAQ;MACjBS,YAAY,EAAE;IAClB,CAAC;IACDhC,yBAAyB,CAAC0B,UAAU,CAAC;IACrC,MAAMO,cAAc,GAAGR,OAAO,EAAES,cAAc;IAC9C,MAAMC,kBAAkB,GAAGV,OAAO,EAAEU,kBAAkB;IACtDjC,qBAAqB,CAAC,CAAC,CAACqB,QAAQ,EAAEtB,uBAAuB,CAACmC,mBAAmB,CAAC;IAC9E,IAAI;MACA,MAAMC,uBAAuB,GAAG;QAC5Bd,QAAQ;QACRe,MAAM,EAAEZ,UAAU;QAClBd,iBAAiB;QACjBsB,cAAc,EAAED,cAAc;QAC9BE,kBAAkB;QAClBX;MACJ,CAAC;MACD,MAAMe,oBAAoB,GAAGhC,eAAe,CAACiC,QAAQ,CAAC,CAAC;MACvD,IAAID,oBAAoB,CAACE,MAAM,IAC3BF,oBAAoB,CAAChB,QAAQ,KAAKA,QAAQ,EAAE;QAC5Cc,uBAAuB,CAACK,OAAO,GAAGH,oBAAoB,CAACG,OAAO;MAClE;MACA,MAAMC,QAAQ,SAAS9B,kBAAkB,CAACwB,uBAAuB,CAAC;MAClE,MAAMO,cAAc,GAAGxC,uBAAuB,CAACmB,QAAQ,CAAC;MACxDf,oBAAoB,CAAC;QACjBqC,aAAa,EAAEF,QAAQ,CAACG,OAAO;QAC/BvB,QAAQ,EAAEqB,cAAc;QACxBG,aAAa,EAAEJ,QAAQ,CAACK,aAAa;QACrClB;MACJ,CAAC,CAAC;MACF,IAAIa,QAAQ,CAACM,oBAAoB,EAAE;QAC/B,MAAMvC,kBAAkB,CAAC;UACrBa,QAAQ,EAAEqB,cAAc;UACxB,GAAGD,QAAQ,CAACM,oBAAoB;UAChCC,iBAAiB,QAAQpC,oBAAoB,CAAC;YAC1CqC,UAAU,EAAEzB,UAAU,CAACyB,UAAU;YACjCC,gBAAgB,EAAE1B,UAAU,CAAC0B,gBAAgB;YAC7CC,iBAAiB,EAAEV,QAAQ,CAACM,oBAAoB,CAACC,iBAAiB;YAClEI,WAAW,EAAEX,QAAQ,CAACM,oBAAoB,CAACM;UAC/C,CAAC,CAAC;UACFzB;QACJ,CAAC,CAAC;QACFrB,sBAAsB,CAAC,CAAC;QACxB,MAAME,wBAAwB,CAAC,CAAC;QAChCI,eAAe,CAAC,CAAC;QACjB,OAAO;UACHyC,UAAU,EAAE,IAAI;UAChBC,QAAQ,EAAE;YAAEC,UAAU,EAAE;UAAO;QACnC,CAAC;MACL;MACA,OAAOrD,eAAe,CAAC;QACnB0C,aAAa,EAAEJ,QAAQ,CAACK,aAAa;QACrCW,mBAAmB,EAAEhB,QAAQ,CAACiB,mBAAmB;QACjDC,mBAAmB,EAAE,qBAAqB,IAAIlB,QAAQ,GAChDA,QAAQ,CAACmB,mBAAmB,GAC5BC;MACV,CAAC,CAAC;IACN,CAAC,CACD,OAAOC,KAAK,EAAE;MACVvD,sBAAsB,CAAC,CAAC;MACxBM,eAAe,CAAC,CAAC;MACjBZ,kBAAkB,CAAC6D,KAAK,CAAC;MACzB,MAAMC,MAAM,GAAG3D,wBAAwB,CAAC0D,KAAK,CAACE,IAAI,CAAC;MACnD,IAAID,MAAM,EACN,OAAOA,MAAM;MACjB,MAAMD,KAAK;IACf;EACJ,CAAC;EAAA,OAAA9C,mBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASJ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}