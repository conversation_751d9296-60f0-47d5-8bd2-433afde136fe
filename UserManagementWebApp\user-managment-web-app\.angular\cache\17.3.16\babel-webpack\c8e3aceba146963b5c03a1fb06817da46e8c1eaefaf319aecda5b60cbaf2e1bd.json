{"ast": null, "code": "import { countryDialCodes } from '../../i18n/country-dial-codes.mjs';\nimport 'aws-amplify/utils';\n\n/**\n * This file contains helpers related to forms and input attributes.\n */\nconst defaultFormFieldOptions = {\n  birthdate: {\n    label: 'Birthdate',\n    placeholder: 'Enter your Birthdate',\n    type: 'date',\n    autocomplete: 'bday',\n    isRequired: true\n  },\n  confirmation_code: {\n    label: 'Confirmation Code',\n    placeholder: 'Enter your Confirmation Code',\n    type: 'text',\n    autocomplete: 'one-time-code',\n    isRequired: true\n  },\n  confirm_password: {\n    label: 'Confirm Password',\n    placeholder: 'Please confirm your Password',\n    type: 'password',\n    autocomplete: 'new-password',\n    isRequired: true\n  },\n  email: {\n    label: 'Email',\n    placeholder: 'Enter your Email',\n    type: 'email',\n    autocomplete: 'username',\n    isRequired: true\n  },\n  family_name: {\n    label: 'Family Name',\n    placeholder: 'Enter your Family Name',\n    type: 'text',\n    autocomplete: 'family-name',\n    isRequired: true\n  },\n  given_name: {\n    label: 'Given Name',\n    placeholder: 'Enter your Given Name',\n    type: 'text',\n    autocomplete: 'given-name',\n    isRequired: true\n  },\n  middle_name: {\n    label: 'Middle Name',\n    placeholder: 'Enter your Middle Name',\n    type: 'text',\n    autocomplete: 'additional-name',\n    isRequired: true\n  },\n  name: {\n    label: 'Name',\n    placeholder: 'Enter your Name',\n    type: 'text',\n    autocomplete: 'name',\n    isRequired: true\n  },\n  nickname: {\n    label: 'Nickname',\n    placeholder: 'Enter your Nickname',\n    type: 'text',\n    autocomplete: 'tel',\n    isRequired: true\n  },\n  password: {\n    label: 'Password',\n    placeholder: 'Enter your Password',\n    type: 'password',\n    autocomplete: 'new-password',\n    isRequired: true\n  },\n  phone_number: {\n    label: 'Phone Number',\n    placeholder: 'Enter your Phone Number',\n    type: 'tel',\n    autocomplete: 'tel',\n    dialCode: '+1',\n    dialCodeList: countryDialCodes,\n    isRequired: true\n  },\n  preferred_username: {\n    label: 'Preferred Username',\n    placeholder: 'Enter your Preferred Username',\n    type: 'text',\n    isRequired: true\n  },\n  profile: {\n    label: 'Profile',\n    placeholder: 'Add your Profile',\n    type: 'url',\n    autocomplete: 'url',\n    isRequired: true\n  },\n  website: {\n    label: 'Website',\n    placeholder: 'Add your Website',\n    type: 'url',\n    autocomplete: 'url',\n    isRequired: true\n  },\n  username: {\n    label: 'Username',\n    placeholder: 'Enter your Username',\n    type: 'text',\n    autocomplete: 'username',\n    isRequired: true\n  }\n};\n/**\n * List of special characters that Cognito allows.\n *\n * Adapted from https://docs.aws.amazon.com/cognito/latest/developerguide/user-pool-settings-policies.html\n */\n// prettier-ignore\nconst ALLOWED_SPECIAL_CHARACTERS = ['^', '$', '*', '.', '[', ']', '{', '}', '(', ')', '?', '\"', '!', '@', '#', '%', '&', '/', '\\\\', ',', '>', '<', \"'\", ':', ';', '|', '_', '~', '`', '=', '+', '-', ' '];\n/**\n * Email validation regex\n *\n * source: HTML5 spec https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address\n */\nconst emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n/**\n+ * map navigable route keys to auth event names\n+ */\nconst NAVIGABLE_ROUTE_EVENT = {\n  forgotPassword: 'FORGOT_PASSWORD',\n  signIn: 'SIGN_IN',\n  signUp: 'SIGN_UP'\n};\nexport { ALLOWED_SPECIAL_CHARACTERS, NAVIGABLE_ROUTE_EVENT, defaultFormFieldOptions, emailRegex };", "map": {"version": 3, "names": ["countryDialCodes", "defaultFormFieldOptions", "birthdate", "label", "placeholder", "type", "autocomplete", "isRequired", "confirmation_code", "confirm_password", "email", "family_name", "given_name", "middle_name", "name", "nickname", "password", "phone_number", "dialCode", "dialCodeList", "preferred_username", "profile", "website", "username", "ALLOWED_SPECIAL_CHARACTERS", "emailRegex", "NAVIGABLE_ROUTE_EVENT", "forgotPassword", "signIn", "signUp"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/ui/dist/esm/helpers/authenticator/constants.mjs"], "sourcesContent": ["import { countryDialCodes } from '../../i18n/country-dial-codes.mjs';\nimport 'aws-amplify/utils';\n\n/**\n * This file contains helpers related to forms and input attributes.\n */\nconst defaultFormFieldOptions = {\n    birthdate: {\n        label: 'Birthdate',\n        placeholder: 'Enter your Birthdate',\n        type: 'date',\n        autocomplete: 'bday',\n        isRequired: true,\n    },\n    confirmation_code: {\n        label: 'Confirmation Code',\n        placeholder: 'Enter your Confirmation Code',\n        type: 'text',\n        autocomplete: 'one-time-code',\n        isRequired: true,\n    },\n    confirm_password: {\n        label: 'Confirm Password',\n        placeholder: 'Please confirm your Password',\n        type: 'password',\n        autocomplete: 'new-password',\n        isRequired: true,\n    },\n    email: {\n        label: 'Email',\n        placeholder: 'Enter your Email',\n        type: 'email',\n        autocomplete: 'username',\n        isRequired: true,\n    },\n    family_name: {\n        label: 'Family Name',\n        placeholder: 'Enter your Family Name',\n        type: 'text',\n        autocomplete: 'family-name',\n        isRequired: true,\n    },\n    given_name: {\n        label: 'Given Name',\n        placeholder: 'Enter your Given Name',\n        type: 'text',\n        autocomplete: 'given-name',\n        isRequired: true,\n    },\n    middle_name: {\n        label: 'Middle Name',\n        placeholder: 'Enter your Middle Name',\n        type: 'text',\n        autocomplete: 'additional-name',\n        isRequired: true,\n    },\n    name: {\n        label: 'Name',\n        placeholder: 'Enter your Name',\n        type: 'text',\n        autocomplete: 'name',\n        isRequired: true,\n    },\n    nickname: {\n        label: 'Nickname',\n        placeholder: 'Enter your Nickname',\n        type: 'text',\n        autocomplete: 'tel',\n        isRequired: true,\n    },\n    password: {\n        label: 'Password',\n        placeholder: 'Enter your Password',\n        type: 'password',\n        autocomplete: 'new-password',\n        isRequired: true,\n    },\n    phone_number: {\n        label: 'Phone Number',\n        placeholder: 'Enter your Phone Number',\n        type: 'tel',\n        autocomplete: 'tel',\n        dialCode: '+1',\n        dialCodeList: countryDialCodes,\n        isRequired: true,\n    },\n    preferred_username: {\n        label: 'Preferred Username',\n        placeholder: 'Enter your Preferred Username',\n        type: 'text',\n        isRequired: true,\n    },\n    profile: {\n        label: 'Profile',\n        placeholder: 'Add your Profile',\n        type: 'url',\n        autocomplete: 'url',\n        isRequired: true,\n    },\n    website: {\n        label: 'Website',\n        placeholder: 'Add your Website',\n        type: 'url',\n        autocomplete: 'url',\n        isRequired: true,\n    },\n    username: {\n        label: 'Username',\n        placeholder: 'Enter your Username',\n        type: 'text',\n        autocomplete: 'username',\n        isRequired: true,\n    },\n};\n/**\n * List of special characters that Cognito allows.\n *\n * Adapted from https://docs.aws.amazon.com/cognito/latest/developerguide/user-pool-settings-policies.html\n */\n// prettier-ignore\nconst ALLOWED_SPECIAL_CHARACTERS = [\n    '^', '$', '*', '.', '[', ']',\n    '{', '}', '(', ')', '?', '\"',\n    '!', '@', '#', '%', '&', '/',\n    '\\\\', ',', '>', '<', \"'\", ':',\n    ';', '|', '_', '~', '`', '=',\n    '+', '-', ' '\n];\n/**\n * Email validation regex\n *\n * source: HTML5 spec https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address\n */\nconst emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n/**\n+ * map navigable route keys to auth event names\n+ */\nconst NAVIGABLE_ROUTE_EVENT = {\n    forgotPassword: 'FORGOT_PASSWORD',\n    signIn: 'SIGN_IN',\n    signUp: 'SIGN_UP',\n};\n\nexport { ALLOWED_SPECIAL_CHARACTERS, NAVIGABLE_ROUTE_EVENT, defaultFormFieldOptions, emailRegex };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,mCAAmC;AACpE,OAAO,mBAAmB;;AAE1B;AACA;AACA;AACA,MAAMC,uBAAuB,GAAG;EAC5BC,SAAS,EAAE;IACPC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,sBAAsB;IACnCC,IAAI,EAAE,MAAM;IACZC,YAAY,EAAE,MAAM;IACpBC,UAAU,EAAE;EAChB,CAAC;EACDC,iBAAiB,EAAE;IACfL,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,8BAA8B;IAC3CC,IAAI,EAAE,MAAM;IACZC,YAAY,EAAE,eAAe;IAC7BC,UAAU,EAAE;EAChB,CAAC;EACDE,gBAAgB,EAAE;IACdN,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,8BAA8B;IAC3CC,IAAI,EAAE,UAAU;IAChBC,YAAY,EAAE,cAAc;IAC5BC,UAAU,EAAE;EAChB,CAAC;EACDG,KAAK,EAAE;IACHP,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,kBAAkB;IAC/BC,IAAI,EAAE,OAAO;IACbC,YAAY,EAAE,UAAU;IACxBC,UAAU,EAAE;EAChB,CAAC;EACDI,WAAW,EAAE;IACTR,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,wBAAwB;IACrCC,IAAI,EAAE,MAAM;IACZC,YAAY,EAAE,aAAa;IAC3BC,UAAU,EAAE;EAChB,CAAC;EACDK,UAAU,EAAE;IACRT,KAAK,EAAE,YAAY;IACnBC,WAAW,EAAE,uBAAuB;IACpCC,IAAI,EAAE,MAAM;IACZC,YAAY,EAAE,YAAY;IAC1BC,UAAU,EAAE;EAChB,CAAC;EACDM,WAAW,EAAE;IACTV,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,wBAAwB;IACrCC,IAAI,EAAE,MAAM;IACZC,YAAY,EAAE,iBAAiB;IAC/BC,UAAU,EAAE;EAChB,CAAC;EACDO,IAAI,EAAE;IACFX,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,iBAAiB;IAC9BC,IAAI,EAAE,MAAM;IACZC,YAAY,EAAE,MAAM;IACpBC,UAAU,EAAE;EAChB,CAAC;EACDQ,QAAQ,EAAE;IACNZ,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAE,MAAM;IACZC,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE;EAChB,CAAC;EACDS,QAAQ,EAAE;IACNb,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAE,UAAU;IAChBC,YAAY,EAAE,cAAc;IAC5BC,UAAU,EAAE;EAChB,CAAC;EACDU,YAAY,EAAE;IACVd,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,yBAAyB;IACtCC,IAAI,EAAE,KAAK;IACXC,YAAY,EAAE,KAAK;IACnBY,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAEnB,gBAAgB;IAC9BO,UAAU,EAAE;EAChB,CAAC;EACDa,kBAAkB,EAAE;IAChBjB,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,+BAA+B;IAC5CC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE;EAChB,CAAC;EACDc,OAAO,EAAE;IACLlB,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,kBAAkB;IAC/BC,IAAI,EAAE,KAAK;IACXC,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE;EAChB,CAAC;EACDe,OAAO,EAAE;IACLnB,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,kBAAkB;IAC/BC,IAAI,EAAE,KAAK;IACXC,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE;EAChB,CAAC;EACDgB,QAAQ,EAAE;IACNpB,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,qBAAqB;IAClCC,IAAI,EAAE,MAAM;IACZC,YAAY,EAAE,UAAU;IACxBC,UAAU,EAAE;EAChB;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiB,0BAA0B,GAAG,CAC/B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC7B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC5B,GAAG,EAAE,GAAG,EAAE,GAAG,CAChB;AACD;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAG,sIAAsI;AACzJ;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG;EAC1BC,cAAc,EAAE,iBAAiB;EACjCC,MAAM,EAAE,SAAS;EACjBC,MAAM,EAAE;AACZ,CAAC;AAED,SAASL,0BAA0B,EAAEE,qBAAqB,EAAEzB,uBAAuB,EAAEwB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}