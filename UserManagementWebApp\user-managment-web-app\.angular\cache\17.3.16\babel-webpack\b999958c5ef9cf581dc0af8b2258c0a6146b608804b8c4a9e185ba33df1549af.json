{"ast": null, "code": "export { DefaultAmplify as Amplify } from './initSingleton.mjs';", "map": {"version": 3, "names": ["DefaultAmplify", "Amplify"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/aws-amplify/dist/esm/index.mjs"], "sourcesContent": ["export { DefaultAmplify as Amplify } from './initSingleton.mjs';\n"], "mappings": "AAAA,SAASA,cAAc,IAAIC,OAAO,QAAQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}