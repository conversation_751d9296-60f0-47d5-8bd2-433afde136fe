{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns canonical headers.\n *\n * @param headers Headers from the request.\n * @returns Request headers that will be signed, and their values, separated by newline characters. Header names must\n * use lowercase characters, must appear in alphabetical order, and must be followed by a colon (:). For the values,\n * trim any leading or trailing spaces, convert sequential spaces to a single space, and separate the values\n * for a multi-value header using commas.\n *\n * @internal\n */\nconst getCanonicalHeaders = headers => Object.entries(headers).map(([key, value]) => ({\n  key: key.toLowerCase(),\n  value: value?.trim().replace(/\\s+/g, ' ') ?? ''\n})).sort((a, b) => a.key < b.key ? -1 : 1).map(entry => `${entry.key}:${entry.value}\\n`).join('');\nexport { getCanonicalHeaders };", "map": {"version": 3, "names": ["getCanonicalHeaders", "headers", "Object", "entries", "map", "key", "value", "toLowerCase", "trim", "replace", "sort", "a", "b", "entry", "join"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/clients/middleware/signing/signer/signatureV4/utils/getCanonicalHeaders.mjs"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Returns canonical headers.\n *\n * @param headers Headers from the request.\n * @returns Request headers that will be signed, and their values, separated by newline characters. Header names must\n * use lowercase characters, must appear in alphabetical order, and must be followed by a colon (:). For the values,\n * trim any leading or trailing spaces, convert sequential spaces to a single space, and separate the values\n * for a multi-value header using commas.\n *\n * @internal\n */\nconst getCanonicalHeaders = (headers) => Object.entries(headers)\n    .map(([key, value]) => ({\n    key: key.toLowerCase(),\n    value: value?.trim().replace(/\\s+/g, ' ') ?? '',\n}))\n    .sort((a, b) => (a.key < b.key ? -1 : 1))\n    .map(entry => `${entry.key}:${entry.value}\\n`)\n    .join('');\n\nexport { getCanonicalHeaders };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,mBAAmB,GAAIC,OAAO,IAAKC,MAAM,CAACC,OAAO,CAACF,OAAO,CAAC,CAC3DG,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,MAAM;EACxBD,GAAG,EAAEA,GAAG,CAACE,WAAW,CAAC,CAAC;EACtBD,KAAK,EAAEA,KAAK,EAAEE,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI;AACjD,CAAC,CAAC,CAAC,CACEC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAMD,CAAC,CAACN,GAAG,GAAGO,CAAC,CAACP,GAAG,GAAG,CAAC,CAAC,GAAG,CAAE,CAAC,CACxCD,GAAG,CAACS,KAAK,IAAI,GAAGA,KAAK,CAACR,GAAG,IAAIQ,KAAK,CAACP,KAAK,IAAI,CAAC,CAC7CQ,IAAI,CAAC,EAAE,CAAC;AAEb,SAASd,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}