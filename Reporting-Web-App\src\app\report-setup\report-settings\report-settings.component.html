<form #form="ngForm">
  <p-toolbar styleClass="flex-parent sticky">
    <div class="p-toolbar-group-start">
      <h3><span class="pi sb-icon-nav-settings"></span> {{this.reportConfig.reportType !== 'actionablegrid'? 'Report
        Settings' :
        'Grid Settings'}}</h3>
    </div>
    <div class="p-toolbar-group-end flex-child">
      <p-button [rounded]="true" [icon]="isFavorite ? 'pi pi-star-fill' : 'pi pi-star'"
        [pTooltip]="isFavorite ? 'Remove Favorite' : 'Add Favorite'" tooltipPosition="top"
        (onClick)="onUpdateUserFavorite()"></p-button>
      @if (this.reportConfig.reportType === 'actionablegrid') {
      <app-help-launcher [outlined]="false" helpLink="{{helpLink}}"></app-help-launcher>
      }
      <span pTooltip="{{projectIsLocked? 'Project is locked' : 'Save'}}" tooltipPosition="top">
        <p-button [rounded]="true" icon="pi pi-save" name="btnSave" id="btnSave" (click)="onClickSave()"
          [disabled]='isSaveDisabled || projectIsLocked'></p-button>
      </span>
    </div>
  </p-toolbar>

  <h2>General Settings</h2>
  <div class="card py-0">
    <p-message *ngIf="showHelp" severity="info" styleClass="m-2"
      text="Review the settings found on this page and then save to finalize."></p-message>
    <div class="grid field mb-0">
      <div class="col-12 p-float-label">
        <input id="ReportName" [(ngModel)]="reportConfig.reportName" required maxlength="100" name="name" pInputText
          [pKeyFilter]="dirFilterRegExp" [disabled]="projectIsLocked" />
        <label for="ReportName">{{this.reportConfig.reportType !== 'actionablegrid'? 'Report Name' : 'Name'}}<span
            class="required-field">*</span></label>
      </div>
      <div class="col-12 p-float-label">
        <input id="Description" [(ngModel)]="reportConfig.description" maxlength="100" name="description" pInputText
          [disabled]="projectIsLocked" />
        <label for="DisplayName">Display Name<span class="required-field">*</span></label>
      </div>
      <div *ngIf="groupTagFlag" class="col-12 p-float-label">
        <p-dropdown [(ngModel)]="parentGroup" name="parentGroup" [disabled]="projectIsLocked"
          [options]="groupTagOptions" id="parentGroup" [ngClass]="{'disabled':projectIsLocked}" [filter]="true"
          filterBy="name" appendTo="body">
          <ng-template pTemplate="selectedItem">
            {{ parentGroup?.dropdownDisplayName ?? 'None' }}
          </ng-template>
        </p-dropdown>
        <label for="ParentGroup">Parent Group</label>
      </div>
      <div *ngIf="this.reportConfig.reportType !== 'actionablegrid'" class="col-12 radio-group mb-3">
        <label class="text-xs text-primary-override">Report Type</label>
        <div *ngFor="let category of categories" class="flex">
          <p-radioButton [inputId]="category.key" class="mb-1" name="category.name" [value]="category.key"
            [(ngModel)]="selectedCategory" [disabled]="category.key === 'R' || projectIsLocked"></p-radioButton>
          <label [for]="category.key" class="mx-1">{{category.name}}</label>
        </div>
      </div>
      <div class="col-12">
        <p-checkbox id="Active" name="active" binary="true" [label]="nameLabelMap?.active"
          [(ngModel)]="reportConfig.isActive" [disabled]="projectIsLocked">
        </p-checkbox>
      </div>

      <div class="col-12 mt-3 p-float-label fake-field-readonly">
        <span id="reportIDFakeField" class="grid align-items-center mx-0">{{reportConfig.reportId}}</span>
        <label for="reportIDFakeField">Report ID</label>
      </div>
    </div>
  </div>
  <p-accordion [multiple]="true">
    <p-accordionTab [selected]="true" header="Connection Settings">
      <div class="grid">
        <div *ngIf="this.reportConfig.reportType !== 'actionablegrid'" class="col-12 radio-group">
          <label class="text-xs text-primary-override">Connection Type</label>
          <div *ngFor="let connectorType of connectorTypes" class="mb-2 flex">
            <p-radioButton [inputId]="connectorType.key" name="connectorType" [value]="connectorType" class="mb-1 mr-1"
              [(ngModel)]="selectedConnectorType" [disabled]="connectorType.key === 'R' || projectIsLocked"
              (onClick)="onConnectorTypeChange()"></p-radioButton>
            <label [for]="connectorType.key">{{connectorType.name}}</label>
          </div>
        </div>
        <div class="col-12 grid field">
          <ng-container *ngIf="selectedConnectorType?.key==='connector'">
            <div class="col-12 p-float-label">
              <p-dropdown inputId="connectors" name="connectors" [options]="connectors" optionValue="value"
                (onChange)="onConnectorChange()" [(ngModel)]="selectedConnector" [disabled]="projectIsLocked"
                [ngClass]="{'disabled':projectIsLocked}">
              </p-dropdown>
              <label for="connectors">Connection</label>
            </div>
            <div class="col-12 p-float-label">
              <p-dropdown inputId="functions" name="functions" [options]="functions" optionValue="value"
                [(ngModel)]="selectedFunction" [disabled]="projectIsLocked" [ngClass]="{'disabled':projectIsLocked}">
              </p-dropdown>
              <label for="functions">Function</label>
            </div>
          </ng-container>

          <ng-container *ngIf="selectedConnectorType?.key==='workflow'">
            <div class="col-12 p-float-label">
              <p-dropdown inputId="workflows" name="workflows" [options]="workflows" optionLabel="name" optionValue="id"
                [(ngModel)]="selectedWorkflow" [disabled]="projectIsLocked" [ngClass]="{'disabled':projectIsLocked}">
              </p-dropdown>
            </div>
          </ng-container>

          <ng-container *ngIf="selectedConnectorType?.key==='forms'">
            <div class="col-12 p-float-label">
              <p-dropdown inputId="forms" name="forms" [options]="datastores" optionLabel="name"
                emptyMessage="No datastores found" [(ngModel)]="selectedDatastore" [disabled]="projectIsLocked"
                (onChange)="onSelectedDatastoreChange()" class="connection-settings-drop-down"
                [ngClass]="{'disabled':projectIsLocked}">
              </p-dropdown>
              <label for="forms">Datastore</label>
            </div>
            <div *ngIf="selectedDatastore" class="col-12 p-float-label p-inputgroup analytical-data-filter">
              <p-dropdown inputId="formsFilter" name="formsFilter" [options]="this.dataViews" optionLabel="name"
                (onChange)="onSelectedViewChange()" [(ngModel)]="this.selectedDataView" [disabled]="projectIsLocked"
                [filter]="true" filterBy="name" class="connection-settings-drop-down"
                [ngClass]="{'disabled':projectIsLocked}">
                <ng-template let-dataView pTemplate="item">
                  <div class="analytical-data-filter-dropdown">
                    <div>{{dataView.name}}</div>
                    <div *ngIf="dataView.viewId !== '0'">
                      <p-button pRipple name="editView" icon="pi pi-pencil" pTooltip="Edit View" tooltipPosition="top"
                        [text]="true" (onClick)="openAnalyticsFilterModal(formMode.EDIT, dataView)"></p-button>
                      <p-button pRipple name="deleteView" icon="pi pi-trash" pTooltip="Delete View"
                        tooltipPosition="top" [text]="true" severity="danger"
                        (onClick)="deleteView(dataView)"></p-button>
                    </div>
                  </div>
                </ng-template>
              </p-dropdown>
              <label for="formsFilter">View</label>
              <span class="p-inputgroup-addon">
                <p-button pRipple name="addNew" icon="pi pi-plus" pTooltip="Add New View" tooltipPosition="top"
                  [outlined]="true" (onClick)="openAnalyticsFilterModal(formMode.CREATE)"
                  [disabled]="projectIsLocked"></p-button>
              </span>
            </div>
          </ng-container>
        </div>
      </div>
    </p-accordionTab>

    <p-accordionTab *ngIf="this.reportConfig.reportType === 'actionablegrid'" [selected]="false" header="Grid Settings">
      <div class="grid field">
        <div class="col-12 p-field-checkbox my-2">
          <p-checkbox binary="true" [(ngModel)]="reportConfig.allowAddNewRow" name="allowAddNewRow"
                      label="Allow User to Add New Rows" [disabled]="projectIsLocked">
          </p-checkbox>
        </div>
        <div class="col-12 p-float-label p-inputgroup">
          <p-dropdown id="eventWorklow" inputId="ouwSettings" name="ouwSettings" [options]="workflows"
                      optionLabel="name" [(ngModel)]="selectedSavedEventWorkflow" [showClear]="true" [disabled]="projectIsLocked"
                      [ngClass]="{'disabled':projectIsLocked}">
          </p-dropdown>
          <label for="eventWorklow">Save Event Workflow</label>
        </div>
        <div *ngIf="this.selectedDataView" class="col-12 p-float-label p-inputgroup">
          <p-dropdown inputId="inlineForm" name="inlineForm" [options]="assetsInfoGroups" optionLabel="name"
                      [(ngModel)]="selectedDynamicAsset" filter="true" filterBy="name" group="true"
                      (onChange)="onDynamicAssetChange()" [disabled]="projectIsLocked" [ngClass]="{'disabled':projectIsLocked}">
            <ng-template pTemplate="selectedItem">
              <div>
                <span *ngIf="selectedDynamicAsset?.type === 'dynamicForm' || selectedDynamicAsset?.type === 'connectorApp'">
                  <i class="sb"
                     [ngClass]="{'sb-icon-form': selectedDynamicAsset.type === 'dynamicForm', 'sb-icon-screen': selectedDynamicAsset.type === 'connectorApp'}"></i>
                </span>
                {{ selectedDynamicAsset?.name }}
              </div>
            </ng-template>
            <ng-template let-group pTemplate="group">
              <div *ngIf="group.label" class="font-bold">
                {{group.label}}
              </div>
            </ng-template>
            <ng-template let-asset pTemplate="item">
              <div class="ml-4">
                <span *ngIf="asset.type === 'dynamicForm' || asset.type === 'connectorApp'">
                  <i class="sb"
                     [ngClass]="{'sb-icon-form': asset.type === 'dynamicForm', 'sb-icon-screen': asset.type === 'connectorApp'}"></i>
                </span>
                {{asset.name}}
              </div>
            </ng-template>
          </p-dropdown>
          <label for="inlineForm">Edit Mode</label>
          <span class="p-inputgroup-addon">
            <p-button [outlined]="true" pRipple name="refreshList" icon="pi p-icon-refresh" [disabled]="projectIsLocked"
                      (onClick)="getDatastoreAssets()" pTooltip="Refresh List" tooltipPosition="top"></p-button>
          </span>
          <span class="p-inputgroup-addon">
            <p-button [outlined]="true" pRipple name="generateAsset" icon="pi pi-plus" [disabled]="projectIsLocked"
                      (onClick)="showDynamicAssetSettingsDialog()" pTooltip="Add New Asset" tooltipPosition="top"></p-button>
          </span>
        </div>
        <div *ngIf="selectedDynamicAsset?.type === 'connectorApp'" class="col-12 p-float-label p-inputgroup">
          <p-dropdown inputId="idField" name="idField" [options]="idFieldOptions" [disabled]="projectIsLocked"
                      [(ngModel)]="this.reportConfig.formatConfig.dynamicAssetInfo.idField" [autoDisplayFirst]="false"
                      [ngClass]="{'disabled':projectIsLocked}">
          </p-dropdown>
          <label for="idField">ID Field</label>
        </div>
        <app-conditional-forms class="w-full" [(reportConfig)]="reportConfig" [datastore]="selectedDatastore"
                               [projectId]="projectId" [projectVersionId]="projectVersionId" [disabled]="projectIsLocked"
                               [actionableGridConfig]="selectedUserConfiguredFormat"></app-conditional-forms>

        <app-conditional-formatting class="w-full" [(reportConfig)]="reportConfig" [datastore]="selectedDatastore"
                               [projectId]="projectId" [projectVersionId]="projectVersionId" [disabled]="projectIsLocked"
                               [actionableGridConfig]="selectedUserConfiguredFormat"></app-conditional-formatting>
                               
        <div class="col-12 p-field-checkbox my-2">
          <p-checkbox binary="true" [(ngModel)]="reportConfig.enablePagination" name="enablePagination"
                      label="Enable Pagination" [disabled]="projectIsLocked">
          </p-checkbox>
        </div>
        <div *ngIf="reportConfig?.enablePagination" class="col-12 p-field-checkbox my-2">
          <p-checkbox binary="true" [(ngModel)]="reportConfig.paginationAutoPaging" name="paginationAutoPaging"
                      label="Pagination Auto Paging" [disabled]="projectIsLocked">
          </p-checkbox>
        </div>
        <div *ngIf="reportConfig?.enablePagination" class="col-12 p-float-label p-inputgroup">
          <p-dropdown inputId="defaultPageSize" name="defaultPageSize" [options]="pageSizes" [disabled]="projectIsLocked"
                      [(ngModel)]="this.reportConfig.defaultPageSize" [autoDisplayFirst]="false" optionLabel="key" optionValue="value"
                      [ngClass]="{'disabled':projectIsLocked}">
          </p-dropdown>
          <label for="pageSize">Default Page Size</label>
        </div>
      </div>
    </p-accordionTab>

    <p-accordionTab [selected]="false" header="Data Feed Settings" *ngIf="selectedConnectorType?.key!=='forms'">
      <div class="grid field">
        <div class="col-12 p-float-label">
          <p-dropdown *ngIf="categoryFunctionExists" inputId="queryCategory" name="queryCategory"
            [options]="queryCategory" optionLabel="label" optionValue="value" [disabled]="projectIsLocked"
            [(ngModel)]="selectedQueryCategory" (onChange)="onCategoryChange()"
            [ngClass]="{'disabled':projectIsLocked}">
          </p-dropdown>
          <label for="queryCategory">Query Category</label>
        </div>
        <div class="col-12 p-float-label" *ngIf="!categoryFunctionExists">
          <input id="queryCategory" [(ngModel)]="selectedQueryCategory" maxlength="100" name="queryCategory" pInputText
            [disabled]="projectIsLocked" />
          <label for="queryCategory">Query Category</label>
        </div>
        <div *ngIf="queryFunctionExists" class="col-12 p-float-label">
          <p-dropdown inputId="queryName" name="queryName" [disabled]="projectIsLocked" [options]="queryName"
            optionLabel="label" optionValue="value" [(ngModel)]="selectedQueryName" (onChange)="getColumnNames()"
            [ngClass]="{'disabled':projectIsLocked}">
          </p-dropdown>
          <label for="queryName">Query Name</label>
        </div>
        <div class="col-12 p-float-label" *ngIf="!queryFunctionExists">
          <input id="queryName" [(ngModel)]="selectedQueryName" maxlength="100" name="queryName" pInputText
            [disabled]="projectIsLocked" />
          <label for="queryName">Query Name</label>
        </div>
      </div>
    </p-accordionTab>

    <p-accordionTab
      *ngIf="this.reportConfig.reportType === reportType.ActionableGrid && this.selectedUserConfiguredFormat"
      [selected]="showHelp" header="Format Columns/Data" contentStyleClass="grid field mx-0 max-w-unset block">
      <div class="section-description">
        <p>
          App roles with "View" level of access will provide read-only access to all visible fields in this grid. Field
          details for "Edit" and "Edit & Delete" access levels are managed below.
        </p>

        <p>
          App permissions are managed within the project's <a [href]="appSettingsUrl">App Settings <i
              class="sb sb-icon-link"></i></a> page, in the Roles section.
        </p>
      </div>
      <app-actionable-grid-config #actionableGridConfigComponent [columnsConfig]="this.selectedUserConfiguredFormat"
        [projectVersionId]="projectVersionId" [disabled]="projectIsLocked"></app-actionable-grid-config>
    </p-accordionTab>

    @if (this.reportConfig?.reportType === reportType.ActionableGrid && calendarViewFlag)
    {
    <p-accordionTab [selected]="false" header="Calendar View">
      <div class="grid">
        <div class="col-12">
          <p-checkbox id="enableCalendarView" name="enableCalendarView" binary="true"
            [label]="nameLabelMap?.enableCalendarView" [(ngModel)]="reportConfig.enableCalendarView"
            [disabled]="projectIsLocked || !hasDateColumn" (onChange)="onChangeCalendarView()" class="mb-2"
            [pTooltip]="hasDateColumn ? null : 'No Date Field(s) in selected data'" tooltipPosition="top">
          </p-checkbox>

          @if (this.reportConfig?.enableCalendarView && this.selectedDatastore && this.selectedDataView) {
          <app-calendar-view-settings [calendarViewConfig]="reportConfig?.calendarViewConfig"
            [selectedDataView]="selectedDataView" [baseSchema]="selectedDatastore?.baseSchema">
          </app-calendar-view-settings>
          }
        </div>
      </div>
    </p-accordionTab>
    }

    <p-accordionTab
      *ngIf="this.reportConfig.reportType === reportType.ActionableGrid && visualizationFlag && reportConfig.formatConfig"
      [selected]="false" header="Visualization Details">
      <app-linked-tiles [tilesConfig]="tilesConfig" [projectId]="projectId" [projectVersionId]="projectVersionId"
        [datastoreId]="selectedDatastore?.id" [selectedDataViewId]="selectedDataView?.viewId"
        [advancedJsonEditFlag]="advancedJsonEditFlag" (tilesListUpdated)="tilesListUpdated($event)"
        [disabled]="projectIsLocked"></app-linked-tiles>
    </p-accordionTab>

    <!-- this is the cross filter charts for actionable grid-->
    <p-accordionTab
      *ngIf="this.reportConfig.reportType === reportType.ActionableGrid && gridChartsFlag && reportConfig.formatConfig"
      [selected]="false" header="Grid Chart Details">
      <app-linked-charts [chartConfigs]="chartConfigs" (linkedChartsUpdated)="chartListUpdated($event)"
        [disabled]="projectIsLocked">
      </app-linked-charts>
    </p-accordionTab>

    <!-- this is the standlone chart type setup -->
    <p-accordionTab [selected]="false" header="Chart Details" *ngIf="selectedCategory === 'chart'">
      <div class="grid">
        <div class="col-12 p-float-label">
          <p-dropdown inputId="chartTypes" [options]="chartTypes" optionLabel="name" optionValue="key"
            [(ngModel)]="chartType" (onChange)="onChartChange()" name="chartType" [disabled]="projectIsLocked"
            [ngClass]="{'disabled':projectIsLocked}">
          </p-dropdown>
          <label for="firstname">Chart Type</label>
        </div>
        <div class="col-12 p-float-label" *ngIf="chartSubTypes">
          <p-dropdown inputId="chartSubTypes" [options]="chartSubTypes" optionLabel="name" optionValue="key"
            [(ngModel)]="chartSubType" name="chartSubType" [disabled]="projectIsLocked"
            [ngClass]="{'disabled':projectIsLocked}">
          </p-dropdown>
          <label for="firstname">Sub Type</label>
        </div>
        <div class="col-12 p-float-label">
          <textarea id="float-input" rows="5" cols="70" pInputTextarea [(ngModel)]="chartConfigDetails"
            name="chartConfigDetails" [ngClass]="{ 'p-filled': chartConfigDetails?.length > 0}"
            [disabled]="projectIsLocked"></textarea>
          <label for="firstname">Configuration Details</label>
          <p-message severity="warn"
            text="It is not recommended to exceed 20 rows per chart as exceeding this may cause display issues."
            styleClass="max-w-30rem"></p-message>
        </div>
      </div>
    </p-accordionTab>

    <p-accordionTab [selected]="showHelp" header="Format Columns/Data" *ngIf="formatColumns.length > 0">
      <div class="grid field max-w-full m-0">
        <div class="col-12 p-0">
          <p-table class="report-setup" #dt [value]="formatColumns">
            <ng-template pTemplate="header">
              <tr>
                <th>Column</th>
                <th>Format Settings</th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-rowData let-i="rowIndex">
              <tr [pSelectableRow]="rowData">
                <td class="py-0">
                  <input pInputText type="text" name="name{{i}}{{rowData.name}}" class="no-label w-10rem"
                    [ngModel]="rowData.displayName || rowData.name" [disabled]="true">
                </td>
                <td class="p-0 flex m-0">
                  <span class="p-float-label col-12 w-10rem">
                    <p-dropdown inputId="format" name="format{{rowData.name}}" [autoDisplayFirst]="false"
                      [options]="formats" optionLabel="name" optionValue="key" [(ngModel)]="rowData.format"
                      (onChange)="setDecimalPlaces(rowData)"
                      [ngClass]="{'disabled': projectIsLocked || selectedConnectorType?.key === 'forms'}"
                      appendTo="body" [disabled]="projectIsLocked || selectedConnectorType?.key === 'forms'">
                    </p-dropdown>
                    <label>Format</label>
                  </span>
                  <span
                    *ngIf="(rowData.format === 'numeric' || rowData.format === 'currency') && rowData?.baseType !== 'integer'"
                    class="p-float-label col-12 w-7rem">
                    <p-inputNumber mode="decimal" name="decimalPlaces{{i}}" [showButtons]="true"
                      [disabled]="selectedConnectorType?.key === 'forms' || projectIsLocked"
                      [(ngModel)]="rowData.decimalPlaces" inputId="minmax-buttons" [min]="0" [max]="100"
                      inputStyleClass="min-w-0" [ngClass]="{'disabled': selectedConnectorType?.key === 'forms'}">
                    </p-inputNumber>
                    <label>Dec. Points</label>
                  </span>
                  <span class="p-float-label col-12 w-8rem" *ngIf="rowData.format === 'currency'">
                    <p-dropdown inputId="format" name="currency{{i}}" [options]="currencies" optionLabel="code"
                      optionValue="code" [(ngModel)]="rowData.currency" appendTo="body"
                      [disabled]="projectIsLocked || selectedConnectorType?.key === 'forms'"
                      [ngClass]="{'disabled': projectIsLocked || selectedConnectorType?.key === 'forms'}">
                    </p-dropdown>
                    <label>Currency</label>
                  </span>
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
    </p-accordionTab>

    <p-accordionTab [selected]="false" header="Drill Through"
      *ngIf="urlColumns.length >0 && this.selectedCategory === 'grid'">
      <div class="grid field max-w-full m-0">
        <div class="col-12 p-0">
          <p-table #dt [value]="drillThroughs" class="drill__table flex"
            styleClass="p-datatable-header-no-background p-datatable-no-margin">
            <ng-template pTemplate="caption">
              <p-toolbar styleClass="p-toolbar-secondary">
                <div class="p-toolbar-group-start"></div>
                <div class="p-toolbar-group-end">
                  <p-button [disabled]="this.selectedDrillThroughs.length <= 0" pRipple
                    name="clearSelectedDrillThroughs" icon="pi pi-trash" [outlined]="true" [rounded]="true"
                    severity="danger" (click)="clearSelectedDrillThroughs()" [disabled]="projectIsLocked"></p-button>
                  <p-button pRipple name="addNew" icon="pi pi-plus" [outlined]="true" [rounded]="true"
                    (click)="addNew()" [disabled]="projectIsLocked"></p-button>
                </div>
              </p-toolbar>
            </ng-template>
            <ng-template pTemplate="header">
              <tr class="drill-head">
                <th pSortableColumn="displayColumn" class="w-3rem p-0">Select</th>
                <th pSortableColumn="displayColumn" class="w-9rem">Display Column</th>
                <th pSortableColumn="urlAlias">URL Alias String</th>
                <th pSortableColumn="urlSubstitution" class="w-14rem">URL Substitutions</th>
                <th pSortableColumn="openIn" class="w-11rem">Open In</th>
                <th pSortableColumn="gridGeneratedLink">Grid Generated Link</th>
                <th></th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-drillThrough let-i="rowIndex">
              <tr class="drill-table-custom-tr">
                <td>
                  <div class="col-12">
                    <p-checkbox (onChange)="drillThroughCheckedChange($event.checked,drillThrough)" [binary]="true"
                      class="p-element ng-untouched ng-pristine ng-valid" name="drillThrough-{{i}}"
                      [disabled]="projectIsLocked">
                    </p-checkbox>
                  </div>
                </td>
                <td class="p-0">
                  <span class="col-12 p-float-label">
                    <p-dropdown name="displayColumn{{i}}" [options]="urlColumns"
                      [(ngModel)]="drillThrough.displayColumn" [style]="{'width':'100%'}" optionValue="value"
                      optionLabel="label" appendTo="body" [disabled]="projectIsLocked"
                      [ngClass]="{'disabled': projectIsLocked}"></p-dropdown>
                    <label for="displayColumn{{i}}">Display Columns</label>
                  </span>
                </td>
                <td class="p-0">
                  <span class="col-12 p-float-label">
                    <input pInputText type="text" [style]="{'width':'100%'}" name="urlAlias{{i}}"
                      [disabled]="projectIsLocked" [(ngModel)]="drillThrough.urlAlias"
                      (focusout)="onSubstitutionChange(drillThrough)">
                    <label for="urlAlias{{i}}"></label>
                    <p-message severity="info" text="eg: http://xxxx.com?param={0}&param={1}"></p-message>
                  </span>
                </td>
                <td class="drill-url-substitution p-0">
                  <p-table [value]="drillThrough.urlSubstitution">
                    <ng-template pTemplate="body" let-rowdata let-j="rowIndex">
              <tr class="drill-url-substitution-tr">
                <td class="drill-url p-0">
                  <div class="col-12 p-inputgroup p-float-label">
                    <input pInputText type="text" [style]="{'width':'100px'}" name="urlSubstitution{{i}}{{j}}"
                      [(ngModel)]="rowdata.urlName" [ngModelOptions]="{updateOn: 'blur'}"
                      (focusout)="onSubstitutionChange(drillThrough)" [disabled]="projectIsLocked">
                    <label for="urlSubstitution{{i}}{{j}}">Parameter</label>
                    <span class="p-inputgroup-addon">
                      <p-button pRipple name="deleteSubstitution{{i}}{{j}}" icon="pi pi-pencil" [outlined]="true"
                        (onClick)="selectUrlSubstitution(drillThrough.urlSubstitution,i,j)"
                        [disabled]="projectIsLocked"></p-button>
                    </span>
                    <span class="p-inputgroup-addon">
                      <p-button pRipple name="deleteSubstitution{{i}}{{j}}" icon="pi pi-trash" [outlined]="true"
                        (onClick)="deleteSubstitution(drillThrough.urlSubstitution,j)"
                        [disabled]="projectIsLocked"></p-button>
                    </span>
                    <span class="p-inputgroup-addon">
                      <p-button pRipple name="addSubstitution{{i}}{{j}}" icon="pi pi-plus" [outlined]="true"
                        (onClick)="addSubstitution(drillThrough.urlSubstitution)"
                        [disabled]="projectIsLocked"></p-button>
                    </span>
                  </div>
                </td>
              </tr>
            </ng-template>
          </p-table>
          </td>
          <td class="p-0">
            <span class="col-12 p-float-label">
              <p-dropdown name="openIn{{i}}" appendTo="body" [options]="openInValues" [(ngModel)]="drillThrough.openIn"
                [style]="{'width':'100%'}" optionValue="code" optionLabel="name" [disabled]="projectIsLocked"
                [ngClass]="{'disabled': projectIsLocked}"></p-dropdown>
              <label for="openIn{{i}}">Value</label>
            </span>
          </td>
          <td class="drill-generated-link px-2 py-1">{{drillThrough.gridGeneratedLink}}</td>
          <td class="px-2 py-1">
            <p-button pRipple icon="pi pi-trash" [text]="true" severity="danger" (click)="deleteProduct(drillThrough)"
              [disabled]="projectIsLocked"></p-button>
          </td>
          </tr>
          </ng-template>
          </p-table>
        </div>
      </div>
    </p-accordionTab>
    @if (this.reportConfig.reportType === 'actionablegrid' && gridAgentChatFlag ) {
      <p-accordionTab [selected]="false" header="Chat Agent">
        <div class="grid field">
          <div class="col-12">
            <!--<p-checkbox binary="true" [(ngModel)]="allowAgentChat" name="allowAgentChat" label="Enable chat agent in this grid"></p-checkbox>-->
          </div>
          <div class="col-12 p-float-label">
            <textarea id="agentAssetInstructions" name="agentAssetInstructions" pInputTextarea
              [autoResize]="true" class="min-h-5rem"
               [(ngModel)]="agentAssetConfig.instructions" [disabled]="projectIsLocked"></textarea>
            <label for="agentAssetInstructions">Agent Instructions</label>
          </div>
          <div class="col-12 p-float-label">
            <textarea id="agentAssetSchema" name="agentAssetSchema" pInputTextarea
              [autoResize]="true" class="min-h-5rem"
               [(ngModel)]="agentAssetConfig.schema" [disabled]="projectIsLocked"></textarea>
            <label for="agentAssetSchema">Sample JSON</label>
          </div>
          <div class="col-12 p-float-label">
            <input id="agentAssetWorkflowId" [(ngModel)]="agentAssetConfig.workflowId" maxlength="50" name="agentAssetWorkflowId"
            pInputText [disabled]="projectIsLocked" />
          <label for="agentAssetWorkflowId">Workflow ID to Run</label>
          </div>
          <div class="col-12 p-float-label">
            <input id="agentAssetStartMessage" [(ngModel)]="agentAssetConfig.startMessage" maxlength="50" name="agentAssetStartMessage"
            pInputText [disabled]="projectIsLocked" />
          <label for="agentAssetStartMessage">Chat Window Starting Message</label>
          </div>
        </div>
      </p-accordionTab>
    }
    <p-accordionTab [selected]="false" header="Sharing">
      <div class="grid embedInputs field">
        <div class="col-12 p-float-label p-inputgroup">
          <input [disabled]="true" [(ngModel)]="reportConfig.url" pInputText type="text" id="LinkToEmbed"
            name="LinkToEmbed" />
          <label for="LinkToEmbed">{{ nameLabelMap.LinkToEmbed }}</label>
          <span class="p-inputgroup-addon">
            <i class="pi pi-copy" (click)="copyToClipboard(reportConfig.url)"></i>
          </span>
        </div>
        <div class="col-12 p-float-label p-inputgroup">
          <input [disabled]="true" id="HtmlToPaste" type="text" pInputText [(ngModel)]="reportConfig.htmlCode"
            name="HtmlToPaste">
          <label for="HtmlToPaste">{{ nameLabelMap.HtmlToPaste }}</label>
          <span class="p-inputgroup-addon">
            <i class="pi pi-copy" (click)="copyToClipboard(reportConfig.htmlCode)"></i>
          </span>
        </div>
      </div>
    </p-accordionTab>

    <p-accordionTab [selected]="false" header="Security">
      <div class="grid field">
        <p-button *ngIf="hasManagerPermissions" type="button" (onClick)="openAssetModal()" icon="p-icon-group"
          label="Manage Report Access" [disabled]="projectIsLocked"></p-button>
        <div class="col-12 mt-3">
          <p-checkbox binary="true" [(ngModel)]="allowAnonymous" name="allowAnonymous" label="Allow anonymous access"
            [disabled]="projectIsLocked">
          </p-checkbox>
        </div>
        <div class="col-12 p-float-label indented mt-1"
          [ngClass]="{ 'border-400' : !allowAnonymous, 'disabled' : !allowAnonymous }">
          <p-dropdown inputId="users" name="users" [disabled]="!allowAnonymous || projectIsLocked"
            [ngClass]="{ 'disabled' : !allowAnonymous || projectIsLocked }" [options]="users" optionLabel="label"
            optionValue="value" [(ngModel)]="selectedUser" [filter]="true">
          </p-dropdown>
          <label for="users">Proxy User</label>
          <p-message severity="info"
            text="When a report is run anonymously the selected user profile and its corresponding security settings will be used to run the report."></p-message>
        </div>
      </div>
    </p-accordionTab>

    <p-accordionTab [selected]="false" header="Cache Settings">
      <div class="grid field">
        <div class="col-12 p-float-label">
          <p-confirmDialog [style]="{width: '25vw'}" appendTo="body">
          </p-confirmDialog>
          <input id="connectionTimeOut" [(ngModel)]="selectedConnectionTimeOut" maxlength="100" name="connectionTimeOut"
            pInputText (keypress)="numberOnly($event)" (blur)="checkTimeOutValid()" [disabled]="projectIsLocked" />
          <label for="connectionTimeOut">Data Cache Refresh Time (in seconds)</label>
          <p-dialog header="Minimum Cache Refresh" [(visible)]="minimunCache" [style]="{width: '25vw'}"
            (onHide)="hideModal()" modal="modal">
            Minimum Cache Refresh time allowed is 5 seconds, It is not recommended to use times under 60 seconds.
            <p-footer>
              <p-button type="button" (click)="hideModal()" label="Ok" icon="pi pi-check" [outlined]="true"></p-button>
            </p-footer>
          </p-dialog>
        </div>
      </div>
    </p-accordionTab>

    <p-accordionTab [selected]="false" header="Email Settings" *ngIf="showEmailSettings">
      <div class="grid field">
        <div class="col-12 p-float-label">
          <p-confirmDialog [style]="{width: '25vw'}">
          </p-confirmDialog>
          <input id="emailLimit" [(ngModel)]="selectedEmailLimit" maxlength="100" name="emailLimit" pInputText
            (keypress)="numberOnly($event)" (blur)="checkTimeOutValid()" [disabled]="projectIsLocked" />
          <label for="emailLimit">Email per minute</label>
        </div>
      </div>
    </p-accordionTab>

  </p-accordion>

</form>


<p-dialog header="Delete Report" [(visible)]="displayModal" [style]="{width: '20vw'}" [baseZIndex]="10000"
  [modal]="true">
  <div class="grid">
    <p>Do you want to delete the report...?</p>
  </div>
  <ng-template pTemplate="footer">
    <p-button type="button" (onClick)="deleteReport()" [disabled]="disableButton" severity="danger"
      label="Delete"></p-button>
    <p-button type="button" [outlined]="true" severity="secondary" label="Cancel"
      (onClick)="displayModal=false"></p-button>
  </ng-template>
</p-dialog>

<p-dialog header="Select URL Substitution" [(visible)]="displayURLSubstitution" [style]="{width: '30vw'}"
  [modal]="true">
  <div class="card">
    <div class="grid field">
      <div class="col-6">
        <div class="field-radiobutton">
          <p-radioButton name="urlSubType" value="column" [(ngModel)]="urlSubType" inputId="rdburlColumn" class="mb-1">
          </p-radioButton>
          <label for="rdbUrlColumn">Column</label>
        </div>
      </div>
      <div class="col-6">
        <div class="field-radiobutton">
          <p-radioButton name="urlSubType" value="projectVariable" [(ngModel)]="urlSubType" class="mb-1"
            inputId="rdburlProjectVariable">
          </p-radioButton>
          <label for="rdburlProjectVariable">Project Variable</label>
        </div>
      </div>
      <div *ngIf="urlSubType==='column'" class="col-12 p-float-label">
        <p-dropdown [options]="urlColumns" [(ngModel)]="selectedColumn" optionLabel="label" [filter]="true"
          filterBy="label" [showClear]="true">
          <ng-template pTemplate="selectedItem">
            <div *ngIf="selectedColumn">
              <div>{{selectedColumn.label}}</div>
            </div>
          </ng-template>
          <ng-template let-columnName pTemplate="item">
            <div>
              {{columnName.label}}
            </div>
          </ng-template>
        </p-dropdown>
        <label>Column Name</label>
      </div>
      <div *ngIf="urlSubType==='projectVariable'" class="col-12 p-float-label">
        <p-dropdown [options]="urlProjectVariables" [(ngModel)]="selectedProjectVariable" optionLabel="label"
          [filter]="true" filterBy="label" [showClear]="true">
          <ng-template pTemplate="selectedItem">
            <div *ngIf="selectedProjectVariable">
              <div>{{selectedProjectVariable.label}}</div>
            </div>
          </ng-template>
          <ng-template let-projectVariable pTemplate="item">
            <div>
              {{projectVariable.label}}
              <span style="float: right;"><i [pTooltip]="projectVariable.title" tooltipPosition="top"
                  tooltipZIndex="99999" class="pi pi-info-circle"></i></span>
            </div>
          </ng-template>
        </p-dropdown>
        <label>Project Variable</label>
      </div>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <p-button pRipple type="button" label="Save" (click)="applyURLSubstitution()"></p-button>
    <p-button pRipple type="button" label="Cancel" [outlined]="true" severity="secondary"
      (click)="displayURLSubstitution=false"></p-button>
  </ng-template>
</p-dialog>
<app-new-dynamic-asset [showDialog]="showDynamicAssetSettings" [defaultName]="reportConfig?.reportName"
  [defaultTitle]="reportConfig?.description" [projectId]="projectId" [projectVersionId]="projectVersionId"
  [actionableGridColumnsConfig]="reportConfig?.formatConfig?.actionableGridColumnsConfig"
  [datastoreId]="selectedDatastore?.id" [datastoreName]="selectedDatastore?.name"
  [dataViewId]="selectedDataView?.viewId === '0' ? null : selectedDataView?.viewId"
  (dialogVisibleChanged)=(newDynamicAssetDialogChanged($event))>
</app-new-dynamic-asset>
