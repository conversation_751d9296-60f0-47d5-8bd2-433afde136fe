{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/IntegrationWebApp/integration-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { constants } from '../constants/constants';\nimport { AppService } from '../services/app.service';\nimport { BreadcrumbService } from '../services/breadcrumb.service';\nimport { ProjectsService } from '../services/projects.service';\nimport { RouterInfoService } from '../services/router-info.service';\nimport { CompanyService } from '../services/company.service ';\nimport { firstValueFrom } from 'rxjs';\nimport { TenantFeature } from '../enums/tenant-feature.enum';\nexport const appAuthGuard = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (route) {\n    const appService = inject(AppService);\n    const projectsService = inject(ProjectsService);\n    const breadcrumbService = inject(BreadcrumbService);\n    const routerInfoService = inject(RouterInfoService);\n    const router = inject(Router);\n    const companyService = inject(CompanyService);\n    const routerParams = routerInfoService.collectRouteParams(route.root);\n    const versionId = +routerParams.versionId;\n    const assetId = routerParams.workflowId;\n    let appGroupRoles = appService.cachedAppGroupRoles;\n    if (!appGroupRoles?.length) {\n      appGroupRoles = yield firstValueFrom(appService.fetchAppGroupRolesForCurrentUserTenant());\n    }\n    console.log('appGroupRoles', appGroupRoles);\n    let app = null;\n    if (assetId) {\n      app = yield getApp(breadcrumbService, projectsService, versionId);\n    }\n    console.log('app', app);\n    return checkAccess(companyService, router, appGroupRoles, versionId, assetId, app);\n  });\n  return function appAuthGuard(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nfunction getApp(_x2, _x3, _x4) {\n  return _getApp.apply(this, arguments);\n}\nfunction _getApp() {\n  _getApp = _asyncToGenerator(function* (breadcrumbService, projectsService, versionId) {\n    let currentProjectVersion = versionId ? breadcrumbService?.currentProjectVersion : null;\n    let app = null;\n    console.log('currentProjectVersion', currentProjectVersion);\n    if (!currentProjectVersion || currentProjectVersion.id !== versionId) {\n      currentProjectVersion = yield firstValueFrom(projectsService.getProjectVersion(versionId));\n      console.log('currentProjectVersion', currentProjectVersion);\n      breadcrumbService.currentProjectVersion = currentProjectVersion;\n    }\n    if (currentProjectVersion?.isApp) {\n      app = currentProjectVersion.appManifest;\n    }\n    return app;\n  });\n  return _getApp.apply(this, arguments);\n}\nfunction checkAccess(companyService, router, appGroupRoles, versionId, assetId, app) {\n  let filteredAppGroupRoles = [...appGroupRoles];\n  if (versionId) {\n    filteredAppGroupRoles = filteredAppGroupRoles.filter(appGroupRole => appGroupRole.projectVersionId === versionId);\n  }\n  if (assetId) {\n    const defaultMenuDetails = app?.menus?.find(m => m.name === 'Default');\n    const roles = defaultMenuDetails?.roles ?? [];\n    const assetRoles = roles.filter(role => role.assets.some(asset => asset.identifier === assetId && asset.permission !== constants.appPermissions.nonePermission.name));\n    filteredAppGroupRoles = filteredAppGroupRoles.filter(appGroupRole => assetRoles.some(role => role.code === appGroupRole.roleCode));\n  }\n  let hasAccess = filteredAppGroupRoles.length > 0;\n  if (hasAccess) {\n    hasAccess = companyService.hasTenantFeatures(TenantFeature.APP_DASHBOARD);\n    console.log('hasAccess', hasAccess);\n  }\n  if (!hasAccess) {\n    router.navigateByUrl('/accessDenied');\n  }\n  return hasAccess;\n}", "map": {"version": 3, "names": ["inject", "Router", "constants", "AppService", "BreadcrumbService", "ProjectsService", "RouterInfoService", "CompanyService", "firstValueFrom", "TenantFeature", "appAuthGuard", "_ref", "_asyncToGenerator", "route", "appService", "projectsService", "breadcrumbService", "routerInfoService", "router", "companyService", "routerParams", "collectRouteParams", "root", "versionId", "assetId", "workflowId", "appGroupRoles", "cachedAppGroupRoles", "length", "fetchAppGroupRolesForCurrentUserTenant", "console", "log", "app", "getApp", "checkAccess", "_x", "apply", "arguments", "_x2", "_x3", "_x4", "_getApp", "currentProjectVersion", "id", "getProjectVersion", "isApp", "appManifest", "filteredAppGroupRoles", "filter", "appGroupRole", "projectVersionId", "defaultMenuDetails", "menus", "find", "m", "name", "roles", "assetRoles", "role", "assets", "some", "asset", "identifier", "permission", "appPermissions", "nonePermission", "code", "roleCode", "hasAccess", "hasTenantFeatures", "APP_DASHBOARD", "navigateByUrl"], "sources": ["C:\\Projects\\IntegrationPlatform\\IntegrationWebApp\\integration-web-app\\src\\app\\core\\guards\\app-auth.guard.ts"], "sourcesContent": ["import { inject } from '@angular/core';\r\nimport { ActivatedRouteSnapshot, CanActivateFn, Router } from '@angular/router';\r\nimport { constants } from '../constants/constants';\r\nimport { AppGroupRole } from '../models/app-group-role';\r\nimport { Application } from '../models/application';\r\nimport { AppService } from '../services/app.service';\r\nimport { BreadcrumbService } from '../services/breadcrumb.service';\r\nimport { ProjectsService } from '../services/projects.service';\r\nimport { RouterInfoService } from '../services/router-info.service';\r\nimport { CompanyService } from '../services/company.service ';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { TenantFeature } from '../enums/tenant-feature.enum';\r\n\r\nexport const appAuthGuard: CanActivateFn = async (route: ActivatedRouteSnapshot) => {\r\n  const appService = inject(AppService);\r\n  const projectsService = inject(ProjectsService);\r\n  const breadcrumbService = inject(BreadcrumbService);\r\n  const routerInfoService = inject(RouterInfoService);\r\n  const router = inject(Router);\r\n  const companyService = inject(CompanyService);\r\n  const routerParams = routerInfoService\r\n    .collectRouteParams(route.root);\r\n  const versionId = +routerParams.versionId;\r\n  const assetId = routerParams.workflowId;\r\n  let appGroupRoles = appService.cachedAppGroupRoles;\r\n  if (!appGroupRoles?.length) {\r\n    appGroupRoles = await firstValueFrom(appService.fetchAppGroupRolesForCurrentUserTenant());\r\n  }\r\n  console.log('appGroupRoles', appGroupRoles);\r\n\r\n  let app: Application = null;\r\n\r\n  if (assetId) {\r\n    app = await getApp(breadcrumbService, projectsService, versionId);\r\n  }\r\n  console.log('app', app);\r\n\r\n  return checkAccess(companyService, router, appGroupRoles, versionId, assetId, app);\r\n};\r\n\r\nasync function getApp(breadcrumbService: BreadcrumbService, projectsService: ProjectsService, versionId: number) {\r\n  let currentProjectVersion = versionId ? breadcrumbService?.currentProjectVersion : null;\r\n  let app: Application = null;\r\n  console.log('currentProjectVersion', currentProjectVersion);\r\n  if (!currentProjectVersion || currentProjectVersion.id !== versionId) {\r\n    currentProjectVersion = await firstValueFrom(projectsService.getProjectVersion(versionId));\r\n    console.log('currentProjectVersion', currentProjectVersion);\r\n    breadcrumbService.currentProjectVersion = currentProjectVersion;\r\n  }\r\n  if (currentProjectVersion?.isApp) {\r\n    app = currentProjectVersion.appManifest;\r\n  }\r\n  return app;\r\n}\r\n\r\nfunction checkAccess(companyService: CompanyService, router: Router, appGroupRoles: AppGroupRole[], versionId?: number, assetId?: string, app?: Application) {\r\n  let filteredAppGroupRoles = [...appGroupRoles];\r\n  if (versionId) {\r\n    filteredAppGroupRoles = filteredAppGroupRoles\r\n      .filter(appGroupRole => appGroupRole.projectVersionId === versionId);\r\n  }\r\n  if (assetId) {\r\n    const defaultMenuDetails = app?.menus?.find(m => m.name === 'Default');\r\n    const roles = defaultMenuDetails?.roles ?? [];\r\n    const assetRoles = roles\r\n      .filter(role => role.assets\r\n        .some(asset => asset.identifier === assetId &&\r\n          asset.permission !== constants.appPermissions.nonePermission.name));\r\n    filteredAppGroupRoles = filteredAppGroupRoles\r\n      .filter(appGroupRole => assetRoles.some(role => role.code === appGroupRole.roleCode));\r\n  }\r\n  let hasAccess = filteredAppGroupRoles.length > 0;\r\n  if (hasAccess) {\r\n    hasAccess = companyService.hasTenantFeatures(TenantFeature.APP_DASHBOARD);\r\n    console.log('hasAccess', hasAccess);\r\n  }\r\n  if (!hasAccess) {\r\n    router.navigateByUrl('/accessDenied');\r\n  }\r\n  return hasAccess;\r\n}\r\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,eAAe;AACtC,SAAgDC,MAAM,QAAQ,iBAAiB;AAC/E,SAASC,SAAS,QAAQ,wBAAwB;AAGlD,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,iBAAiB,QAAQ,gCAAgC;AAClE,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,cAAc,QAAQ,MAAM;AACrC,SAASC,aAAa,QAAQ,8BAA8B;AAE5D,OAAO,MAAMC,YAAY;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAkB,WAAOC,KAA6B,EAAI;IACjF,MAAMC,UAAU,GAAGd,MAAM,CAACG,UAAU,CAAC;IACrC,MAAMY,eAAe,GAAGf,MAAM,CAACK,eAAe,CAAC;IAC/C,MAAMW,iBAAiB,GAAGhB,MAAM,CAACI,iBAAiB,CAAC;IACnD,MAAMa,iBAAiB,GAAGjB,MAAM,CAACM,iBAAiB,CAAC;IACnD,MAAMY,MAAM,GAAGlB,MAAM,CAACC,MAAM,CAAC;IAC7B,MAAMkB,cAAc,GAAGnB,MAAM,CAACO,cAAc,CAAC;IAC7C,MAAMa,YAAY,GAAGH,iBAAiB,CACnCI,kBAAkB,CAACR,KAAK,CAACS,IAAI,CAAC;IACjC,MAAMC,SAAS,GAAG,CAACH,YAAY,CAACG,SAAS;IACzC,MAAMC,OAAO,GAAGJ,YAAY,CAACK,UAAU;IACvC,IAAIC,aAAa,GAAGZ,UAAU,CAACa,mBAAmB;IAClD,IAAI,CAACD,aAAa,EAAEE,MAAM,EAAE;MAC1BF,aAAa,SAASlB,cAAc,CAACM,UAAU,CAACe,sCAAsC,EAAE,CAAC;IAC3F;IACAC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEL,aAAa,CAAC;IAE3C,IAAIM,GAAG,GAAgB,IAAI;IAE3B,IAAIR,OAAO,EAAE;MACXQ,GAAG,SAASC,MAAM,CAACjB,iBAAiB,EAAED,eAAe,EAAEQ,SAAS,CAAC;IACnE;IACAO,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEC,GAAG,CAAC;IAEvB,OAAOE,WAAW,CAACf,cAAc,EAAED,MAAM,EAAEQ,aAAa,EAAEH,SAAS,EAAEC,OAAO,EAAEQ,GAAG,CAAC;EACpF,CAAC;EAAA,gBAzBYtB,YAAYA,CAAAyB,EAAA;IAAA,OAAAxB,IAAA,CAAAyB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAyBxB;AAAC,SAEaJ,MAAMA,CAAAK,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,OAAA,CAAAL,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAI,QAAA;EAAAA,OAAA,GAAA7B,iBAAA,CAArB,WAAsBI,iBAAoC,EAAED,eAAgC,EAAEQ,SAAiB;IAC7G,IAAImB,qBAAqB,GAAGnB,SAAS,GAAGP,iBAAiB,EAAE0B,qBAAqB,GAAG,IAAI;IACvF,IAAIV,GAAG,GAAgB,IAAI;IAC3BF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEW,qBAAqB,CAAC;IAC3D,IAAI,CAACA,qBAAqB,IAAIA,qBAAqB,CAACC,EAAE,KAAKpB,SAAS,EAAE;MACpEmB,qBAAqB,SAASlC,cAAc,CAACO,eAAe,CAAC6B,iBAAiB,CAACrB,SAAS,CAAC,CAAC;MAC1FO,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEW,qBAAqB,CAAC;MAC3D1B,iBAAiB,CAAC0B,qBAAqB,GAAGA,qBAAqB;IACjE;IACA,IAAIA,qBAAqB,EAAEG,KAAK,EAAE;MAChCb,GAAG,GAAGU,qBAAqB,CAACI,WAAW;IACzC;IACA,OAAOd,GAAG;EACZ,CAAC;EAAA,OAAAS,OAAA,CAAAL,KAAA,OAAAC,SAAA;AAAA;AAED,SAASH,WAAWA,CAACf,cAA8B,EAAED,MAAc,EAAEQ,aAA6B,EAAEH,SAAkB,EAAEC,OAAgB,EAAEQ,GAAiB;EACzJ,IAAIe,qBAAqB,GAAG,CAAC,GAAGrB,aAAa,CAAC;EAC9C,IAAIH,SAAS,EAAE;IACbwB,qBAAqB,GAAGA,qBAAqB,CAC1CC,MAAM,CAACC,YAAY,IAAIA,YAAY,CAACC,gBAAgB,KAAK3B,SAAS,CAAC;EACxE;EACA,IAAIC,OAAO,EAAE;IACX,MAAM2B,kBAAkB,GAAGnB,GAAG,EAAEoB,KAAK,EAAEC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,SAAS,CAAC;IACtE,MAAMC,KAAK,GAAGL,kBAAkB,EAAEK,KAAK,IAAI,EAAE;IAC7C,MAAMC,UAAU,GAAGD,KAAK,CACrBR,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACC,MAAM,CACxBC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,KAAKtC,OAAO,IACzCqC,KAAK,CAACE,UAAU,KAAK7D,SAAS,CAAC8D,cAAc,CAACC,cAAc,CAACV,IAAI,CAAC,CAAC;IACzER,qBAAqB,GAAGA,qBAAqB,CAC1CC,MAAM,CAACC,YAAY,IAAIQ,UAAU,CAACG,IAAI,CAACF,IAAI,IAAIA,IAAI,CAACQ,IAAI,KAAKjB,YAAY,CAACkB,QAAQ,CAAC,CAAC;EACzF;EACA,IAAIC,SAAS,GAAGrB,qBAAqB,CAACnB,MAAM,GAAG,CAAC;EAChD,IAAIwC,SAAS,EAAE;IACbA,SAAS,GAAGjD,cAAc,CAACkD,iBAAiB,CAAC5D,aAAa,CAAC6D,aAAa,CAAC;IACzExC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEqC,SAAS,CAAC;EACrC;EACA,IAAI,CAACA,SAAS,EAAE;IACdlD,MAAM,CAACqD,aAAa,CAAC,eAAe,CAAC;EACvC;EACA,OAAOH,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}