{"ast": null, "code": "import { KeyValueStorage } from './KeyValueStorage.mjs';\nimport { getLocalStorageWithFallback } from './utils.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nclass DefaultStorage extends KeyValueStorage {\n  constructor() {\n    super(getLocalStorageWithFallback());\n  }\n}\nexport { DefaultStorage };", "map": {"version": 3, "names": ["KeyValueStorage", "getLocalStorageWithFallback", "DefaultStorage", "constructor"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/storage/DefaultStorage.mjs"], "sourcesContent": ["import { KeyValueStorage } from './KeyValueStorage.mjs';\nimport { getLocalStorageWithFallback } from './utils.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * @internal\n */\nclass DefaultStorage extends KeyValueStorage {\n    constructor() {\n        super(getLocalStorageWithFallback());\n    }\n}\n\nexport { DefaultStorage };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,uBAAuB;AACvD,SAASC,2BAA2B,QAAQ,aAAa;;AAEzD;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,SAASF,eAAe,CAAC;EACzCG,WAAWA,CAAA,EAAG;IACV,KAAK,CAACF,2BAA2B,CAAC,CAAC,CAAC;EACxC;AACJ;AAEA,SAASC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}