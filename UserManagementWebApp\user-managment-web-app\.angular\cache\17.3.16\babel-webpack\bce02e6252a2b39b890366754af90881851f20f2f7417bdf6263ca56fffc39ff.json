{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ConsoleLogger } from '../Logger/ConsoleLogger.mjs';\nimport { AmplifyError } from '../errors/AmplifyError.mjs';\nimport '../types/errors.mjs';\nimport '../errors/errorHelpers.mjs';\nimport '../utils/getClientInfo/getClientInfo.mjs';\nimport { isBrowser } from '../utils/isBrowser.mjs';\nimport '../utils/retry/retry.mjs';\nimport '../parseAWSExports.mjs';\nimport 'uuid';\nimport '../singleton/Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport '../Platform/index.mjs';\nimport '../Platform/types.mjs';\nimport '../BackgroundProcessManager/types.mjs';\nimport '../Reachability/Reachability.mjs';\nimport '../Hub/index.mjs';\nimport '../utils/sessionListener/index.mjs';\nimport '../awsClients/pinpoint/base.mjs';\nimport '../awsClients/pinpoint/errorHelpers.mjs';\nimport '../Cache/index.mjs';\nimport { record } from '../providers/pinpoint/apis/record.mjs';\nimport '../providers/pinpoint/utils/PinpointEventBuffer.mjs';\nimport '../providers/pinpoint/types/errors.mjs';\nimport { Amplify } from '../singleton/Amplify.mjs';\nimport { fetchAuthSession } from '../singleton/apis/fetchAuthSession.mjs';\nimport { assert, ServiceWorkerErrorCode } from './errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Provides a means to registering a service worker in the browser\n * and communicating with it via postMessage events.\n * https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API/\n *\n * postMessage events are currently not supported in all browsers. See:\n * https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API\n *\n * At the minmum this class will register the service worker and listen\n * and attempt to dispatch messages on state change and record analytics\n * events based on the service worker lifecycle.\n */\nclass ServiceWorkerClass {\n  constructor() {\n    // The AWS Amplify logger\n    this._logger = new ConsoleLogger('ServiceWorker');\n  }\n  /**\n   * Get the currently active service worker\n   */\n  get serviceWorker() {\n    assert(this._serviceWorker !== undefined, ServiceWorkerErrorCode.UndefinedInstance);\n    return this._serviceWorker;\n  }\n  /**\n   * Register the service-worker.js file in the browser\n   * Make sure the service-worker.js is part of the build\n   * for example with Angular, modify the angular-cli.json file\n   * and add to \"assets\" array \"service-worker.js\"\n   * @param {string} filePath Service worker file. Defaults to \"/service-worker.js\"\n   * @param {string} scope The service worker scope. Defaults to \"/\"\n   *  - API Doc: https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/register\n   * @returns {Promise}\n   *\t- resolve(ServiceWorkerRegistration)\n   *\t- reject(Error)\n   **/\n  register(filePath = '/service-worker.js', scope = '/') {\n    this._logger.debug(`registering ${filePath}`);\n    this._logger.debug(`registering service worker with scope ${scope}`);\n    return new Promise((resolve, reject) => {\n      if (navigator && 'serviceWorker' in navigator) {\n        navigator.serviceWorker.register(filePath, {\n          scope\n        }).then(registration => {\n          if (registration.installing) {\n            this._serviceWorker = registration.installing;\n          } else if (registration.waiting) {\n            this._serviceWorker = registration.waiting;\n          } else if (registration.active) {\n            this._serviceWorker = registration.active;\n          }\n          this._registration = registration;\n          this._setupListeners();\n          this._logger.debug(`Service Worker Registration Success: ${registration}`);\n          resolve(registration);\n        }).catch(error => {\n          this._logger.debug(`Service Worker Registration Failed ${error}`);\n          reject(new AmplifyError({\n            name: ServiceWorkerErrorCode.Unavailable,\n            message: 'Service Worker not available',\n            underlyingError: error\n          }));\n        });\n      } else {\n        reject(new AmplifyError({\n          name: ServiceWorkerErrorCode.Unavailable,\n          message: 'Service Worker not available'\n        }));\n      }\n    });\n  }\n  /**\n   * Enable web push notifications. If not subscribed, a new subscription will\n   * be created and registered.\n   * \tTest Push Server: https://web-push-codelab.glitch.me/\n   * \tPush Server Libraries: https://github.com/web-push-libs/\n   * \tAPI Doc: https://developers.google.com/web/fundamentals/codelabs/push-notifications/\n   * @param publicKey\n   * @returns {Promise}\n   * \t- resolve(PushSubscription)\n   *  - reject(Error)\n   */\n  enablePush(publicKey) {\n    assert(this._registration !== undefined, ServiceWorkerErrorCode.UndefinedRegistration);\n    this._publicKey = publicKey;\n    return new Promise((resolve, reject) => {\n      if (isBrowser()) {\n        assert(this._registration !== undefined, ServiceWorkerErrorCode.UndefinedRegistration);\n        this._registration.pushManager.getSubscription().then(subscription => {\n          if (subscription) {\n            this._subscription = subscription;\n            this._logger.debug(`User is subscribed to push: ${JSON.stringify(subscription)}`);\n            resolve(subscription);\n          } else {\n            this._logger.debug(`User is NOT subscribed to push`);\n            return this._registration.pushManager.subscribe({\n              userVisibleOnly: true,\n              applicationServerKey: this._urlB64ToUint8Array(publicKey)\n            }).then(pushManagerSubscription => {\n              this._subscription = pushManagerSubscription;\n              this._logger.debug(`User subscribed: ${JSON.stringify(pushManagerSubscription)}`);\n              resolve(pushManagerSubscription);\n            }).catch(error => {\n              this._logger.error(error);\n            });\n          }\n        });\n      } else {\n        reject(new AmplifyError({\n          name: ServiceWorkerErrorCode.Unavailable,\n          message: 'Service Worker not available'\n        }));\n      }\n    });\n  }\n  /**\n   * Convert a base64 encoded string to a Uint8 array for the push server key\n   * @param base64String\n   */\n  _urlB64ToUint8Array(base64String) {\n    const padding = '='.repeat((4 - base64String.length % 4) % 4);\n    const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/');\n    const rawData = window.atob(base64);\n    const outputArray = new Uint8Array(rawData.length);\n    for (let i = 0; i < rawData.length; ++i) {\n      outputArray[i] = rawData.charCodeAt(i);\n    }\n    return outputArray;\n  }\n  /**\n   * Send a message to the service worker. The service worker needs\n   * to implement `self.addEventListener('message') to handle the\n   * message. This ***currently*** does not work in Safari or IE.\n   * @param {object | string} message An arbitrary JSON object or string message to send to the service worker\n   *\t- see: https://developer.mozilla.org/en-US/docs/Web/API/Transferable\n   * @returns {Promise}\n   **/\n  send(message) {\n    if (this._serviceWorker) {\n      this._serviceWorker.postMessage(typeof message === 'object' ? JSON.stringify(message) : message);\n    }\n  }\n  /**\n   * Listen for service worker state change and message events\n   * https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/state\n   **/\n  _setupListeners() {\n    var _this = this;\n    this.serviceWorker.addEventListener('statechange', /*#__PURE__*/_asyncToGenerator(function* () {\n      const currentState = _this.serviceWorker.state;\n      _this._logger.debug(`ServiceWorker statechange: ${currentState}`);\n      const {\n        appId,\n        region,\n        bufferSize,\n        flushInterval,\n        flushSize,\n        resendLimit\n      } = Amplify.getConfig().Analytics?.Pinpoint ?? {};\n      const {\n        credentials\n      } = yield fetchAuthSession();\n      if (appId && region && credentials) {\n        // Pinpoint is configured, record an event\n        record({\n          appId,\n          region,\n          category: 'Core',\n          credentials,\n          bufferSize,\n          flushInterval,\n          flushSize,\n          resendLimit,\n          event: {\n            name: 'ServiceWorker',\n            attributes: {\n              state: currentState\n            }\n          }\n        });\n      }\n    }));\n    this.serviceWorker.addEventListener('message', event => {\n      this._logger.debug(`ServiceWorker message event: ${event}`);\n    });\n  }\n}\nexport { ServiceWorkerClass };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AmplifyError", "<PERSON><PERSON><PERSON><PERSON>", "record", "Amplify", "fetchAuthSession", "assert", "ServiceWorkerErrorCode", "ServiceWorkerClass", "constructor", "_logger", "serviceWorker", "_serviceWorker", "undefined", "UndefinedInstance", "register", "filePath", "scope", "debug", "Promise", "resolve", "reject", "navigator", "then", "registration", "installing", "waiting", "active", "_registration", "_setupListeners", "catch", "error", "name", "Unavailable", "message", "underlyingError", "enablePush", "public<PERSON>ey", "UndefinedRegistration", "_public<PERSON>ey", "pushManager", "getSubscription", "subscription", "_subscription", "JSON", "stringify", "subscribe", "userVisibleOnly", "applicationServerKey", "_urlB64ToUint8Array", "pushManagerSubscription", "base64String", "padding", "repeat", "length", "base64", "replace", "rawData", "window", "atob", "outputArray", "Uint8Array", "i", "charCodeAt", "send", "postMessage", "_this", "addEventListener", "_asyncToGenerator", "currentState", "state", "appId", "region", "bufferSize", "flushInterval", "flushSize", "resendLimit", "getConfig", "Analytics", "Pinpoint", "credentials", "category", "event", "attributes"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/ServiceWorker/ServiceWorker.mjs"], "sourcesContent": ["import { ConsoleLogger } from '../Logger/ConsoleLogger.mjs';\nimport { AmplifyError } from '../errors/AmplifyError.mjs';\nimport '../types/errors.mjs';\nimport '../errors/errorHelpers.mjs';\nimport '../utils/getClientInfo/getClientInfo.mjs';\nimport { isBrowser } from '../utils/isBrowser.mjs';\nimport '../utils/retry/retry.mjs';\nimport '../parseAWSExports.mjs';\nimport 'uuid';\nimport '../singleton/Auth/utils/errorHelpers.mjs';\nimport '@aws-crypto/sha256-js';\nimport '@smithy/util-hex-encoding';\nimport '../Platform/index.mjs';\nimport '../Platform/types.mjs';\nimport '../BackgroundProcessManager/types.mjs';\nimport '../Reachability/Reachability.mjs';\nimport '../Hub/index.mjs';\nimport '../utils/sessionListener/index.mjs';\nimport '../awsClients/pinpoint/base.mjs';\nimport '../awsClients/pinpoint/errorHelpers.mjs';\nimport '../Cache/index.mjs';\nimport { record } from '../providers/pinpoint/apis/record.mjs';\nimport '../providers/pinpoint/utils/PinpointEventBuffer.mjs';\nimport '../providers/pinpoint/types/errors.mjs';\nimport { Amplify } from '../singleton/Amplify.mjs';\nimport { fetchAuthSession } from '../singleton/apis/fetchAuthSession.mjs';\nimport { assert, ServiceWorkerErrorCode } from './errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n/**\n * Provides a means to registering a service worker in the browser\n * and communicating with it via postMessage events.\n * https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API/\n *\n * postMessage events are currently not supported in all browsers. See:\n * https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API\n *\n * At the minmum this class will register the service worker and listen\n * and attempt to dispatch messages on state change and record analytics\n * events based on the service worker lifecycle.\n */\nclass ServiceWorkerClass {\n    constructor() {\n        // The AWS Amplify logger\n        this._logger = new ConsoleLogger('ServiceWorker');\n    }\n    /**\n     * Get the currently active service worker\n     */\n    get serviceWorker() {\n        assert(this._serviceWorker !== undefined, ServiceWorkerErrorCode.UndefinedInstance);\n        return this._serviceWorker;\n    }\n    /**\n     * Register the service-worker.js file in the browser\n     * Make sure the service-worker.js is part of the build\n     * for example with Angular, modify the angular-cli.json file\n     * and add to \"assets\" array \"service-worker.js\"\n     * @param {string} filePath Service worker file. Defaults to \"/service-worker.js\"\n     * @param {string} scope The service worker scope. Defaults to \"/\"\n     *  - API Doc: https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/register\n     * @returns {Promise}\n     *\t- resolve(ServiceWorkerRegistration)\n     *\t- reject(Error)\n     **/\n    register(filePath = '/service-worker.js', scope = '/') {\n        this._logger.debug(`registering ${filePath}`);\n        this._logger.debug(`registering service worker with scope ${scope}`);\n        return new Promise((resolve, reject) => {\n            if (navigator && 'serviceWorker' in navigator) {\n                navigator.serviceWorker\n                    .register(filePath, {\n                    scope,\n                })\n                    .then(registration => {\n                    if (registration.installing) {\n                        this._serviceWorker = registration.installing;\n                    }\n                    else if (registration.waiting) {\n                        this._serviceWorker = registration.waiting;\n                    }\n                    else if (registration.active) {\n                        this._serviceWorker = registration.active;\n                    }\n                    this._registration = registration;\n                    this._setupListeners();\n                    this._logger.debug(`Service Worker Registration Success: ${registration}`);\n                    resolve(registration);\n                })\n                    .catch(error => {\n                    this._logger.debug(`Service Worker Registration Failed ${error}`);\n                    reject(new AmplifyError({\n                        name: ServiceWorkerErrorCode.Unavailable,\n                        message: 'Service Worker not available',\n                        underlyingError: error,\n                    }));\n                });\n            }\n            else {\n                reject(new AmplifyError({\n                    name: ServiceWorkerErrorCode.Unavailable,\n                    message: 'Service Worker not available',\n                }));\n            }\n        });\n    }\n    /**\n     * Enable web push notifications. If not subscribed, a new subscription will\n     * be created and registered.\n     * \tTest Push Server: https://web-push-codelab.glitch.me/\n     * \tPush Server Libraries: https://github.com/web-push-libs/\n     * \tAPI Doc: https://developers.google.com/web/fundamentals/codelabs/push-notifications/\n     * @param publicKey\n     * @returns {Promise}\n     * \t- resolve(PushSubscription)\n     *  - reject(Error)\n     */\n    enablePush(publicKey) {\n        assert(this._registration !== undefined, ServiceWorkerErrorCode.UndefinedRegistration);\n        this._publicKey = publicKey;\n        return new Promise((resolve, reject) => {\n            if (isBrowser()) {\n                assert(this._registration !== undefined, ServiceWorkerErrorCode.UndefinedRegistration);\n                this._registration.pushManager.getSubscription().then(subscription => {\n                    if (subscription) {\n                        this._subscription = subscription;\n                        this._logger.debug(`User is subscribed to push: ${JSON.stringify(subscription)}`);\n                        resolve(subscription);\n                    }\n                    else {\n                        this._logger.debug(`User is NOT subscribed to push`);\n                        return this._registration.pushManager.subscribe({\n                            userVisibleOnly: true,\n                            applicationServerKey: this._urlB64ToUint8Array(publicKey),\n                        })\n                            .then(pushManagerSubscription => {\n                            this._subscription = pushManagerSubscription;\n                            this._logger.debug(`User subscribed: ${JSON.stringify(pushManagerSubscription)}`);\n                            resolve(pushManagerSubscription);\n                        })\n                            .catch(error => {\n                            this._logger.error(error);\n                        });\n                    }\n                });\n            }\n            else {\n                reject(new AmplifyError({\n                    name: ServiceWorkerErrorCode.Unavailable,\n                    message: 'Service Worker not available',\n                }));\n            }\n        });\n    }\n    /**\n     * Convert a base64 encoded string to a Uint8 array for the push server key\n     * @param base64String\n     */\n    _urlB64ToUint8Array(base64String) {\n        const padding = '='.repeat((4 - (base64String.length % 4)) % 4);\n        const base64 = (base64String + padding)\n            .replace(/-/g, '+')\n            .replace(/_/g, '/');\n        const rawData = window.atob(base64);\n        const outputArray = new Uint8Array(rawData.length);\n        for (let i = 0; i < rawData.length; ++i) {\n            outputArray[i] = rawData.charCodeAt(i);\n        }\n        return outputArray;\n    }\n    /**\n     * Send a message to the service worker. The service worker needs\n     * to implement `self.addEventListener('message') to handle the\n     * message. This ***currently*** does not work in Safari or IE.\n     * @param {object | string} message An arbitrary JSON object or string message to send to the service worker\n     *\t- see: https://developer.mozilla.org/en-US/docs/Web/API/Transferable\n     * @returns {Promise}\n     **/\n    send(message) {\n        if (this._serviceWorker) {\n            this._serviceWorker.postMessage(typeof message === 'object' ? JSON.stringify(message) : message);\n        }\n    }\n    /**\n     * Listen for service worker state change and message events\n     * https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorker/state\n     **/\n    _setupListeners() {\n        this.serviceWorker.addEventListener('statechange', async () => {\n            const currentState = this.serviceWorker.state;\n            this._logger.debug(`ServiceWorker statechange: ${currentState}`);\n            const { appId, region, bufferSize, flushInterval, flushSize, resendLimit, } = Amplify.getConfig().Analytics?.Pinpoint ?? {};\n            const { credentials } = await fetchAuthSession();\n            if (appId && region && credentials) {\n                // Pinpoint is configured, record an event\n                record({\n                    appId,\n                    region,\n                    category: 'Core',\n                    credentials,\n                    bufferSize,\n                    flushInterval,\n                    flushSize,\n                    resendLimit,\n                    event: {\n                        name: 'ServiceWorker',\n                        attributes: {\n                            state: currentState,\n                        },\n                    },\n                });\n            }\n        });\n        this.serviceWorker.addEventListener('message', event => {\n            this._logger.debug(`ServiceWorker message event: ${event}`);\n        });\n    }\n}\n\nexport { ServiceWorkerClass };\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,YAAY,QAAQ,4BAA4B;AACzD,OAAO,qBAAqB;AAC5B,OAAO,4BAA4B;AACnC,OAAO,0CAA0C;AACjD,SAASC,SAAS,QAAQ,wBAAwB;AAClD,OAAO,0BAA0B;AACjC,OAAO,wBAAwB;AAC/B,OAAO,MAAM;AACb,OAAO,0CAA0C;AACjD,OAAO,uBAAuB;AAC9B,OAAO,2BAA2B;AAClC,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,uCAAuC;AAC9C,OAAO,kCAAkC;AACzC,OAAO,kBAAkB;AACzB,OAAO,oCAAoC;AAC3C,OAAO,iCAAiC;AACxC,OAAO,yCAAyC;AAChD,OAAO,oBAAoB;AAC3B,SAASC,MAAM,QAAQ,uCAAuC;AAC9D,OAAO,qDAAqD;AAC5D,OAAO,wCAAwC;AAC/C,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE,SAASC,MAAM,EAAEC,sBAAsB,QAAQ,oBAAoB;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,OAAO,GAAG,IAAIV,aAAa,CAAC,eAAe,CAAC;EACrD;EACA;AACJ;AACA;EACI,IAAIW,aAAaA,CAAA,EAAG;IAChBL,MAAM,CAAC,IAAI,CAACM,cAAc,KAAKC,SAAS,EAAEN,sBAAsB,CAACO,iBAAiB,CAAC;IACnF,OAAO,IAAI,CAACF,cAAc;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,QAAQA,CAACC,QAAQ,GAAG,oBAAoB,EAAEC,KAAK,GAAG,GAAG,EAAE;IACnD,IAAI,CAACP,OAAO,CAACQ,KAAK,CAAC,eAAeF,QAAQ,EAAE,CAAC;IAC7C,IAAI,CAACN,OAAO,CAACQ,KAAK,CAAC,yCAAyCD,KAAK,EAAE,CAAC;IACpE,OAAO,IAAIE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC,IAAIC,SAAS,IAAI,eAAe,IAAIA,SAAS,EAAE;QAC3CA,SAAS,CAACX,aAAa,CAClBI,QAAQ,CAACC,QAAQ,EAAE;UACpBC;QACJ,CAAC,CAAC,CACGM,IAAI,CAACC,YAAY,IAAI;UACtB,IAAIA,YAAY,CAACC,UAAU,EAAE;YACzB,IAAI,CAACb,cAAc,GAAGY,YAAY,CAACC,UAAU;UACjD,CAAC,MACI,IAAID,YAAY,CAACE,OAAO,EAAE;YAC3B,IAAI,CAACd,cAAc,GAAGY,YAAY,CAACE,OAAO;UAC9C,CAAC,MACI,IAAIF,YAAY,CAACG,MAAM,EAAE;YAC1B,IAAI,CAACf,cAAc,GAAGY,YAAY,CAACG,MAAM;UAC7C;UACA,IAAI,CAACC,aAAa,GAAGJ,YAAY;UACjC,IAAI,CAACK,eAAe,CAAC,CAAC;UACtB,IAAI,CAACnB,OAAO,CAACQ,KAAK,CAAC,wCAAwCM,YAAY,EAAE,CAAC;UAC1EJ,OAAO,CAACI,YAAY,CAAC;QACzB,CAAC,CAAC,CACGM,KAAK,CAACC,KAAK,IAAI;UAChB,IAAI,CAACrB,OAAO,CAACQ,KAAK,CAAC,sCAAsCa,KAAK,EAAE,CAAC;UACjEV,MAAM,CAAC,IAAIpB,YAAY,CAAC;YACpB+B,IAAI,EAAEzB,sBAAsB,CAAC0B,WAAW;YACxCC,OAAO,EAAE,8BAA8B;YACvCC,eAAe,EAAEJ;UACrB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;MACN,CAAC,MACI;QACDV,MAAM,CAAC,IAAIpB,YAAY,CAAC;UACpB+B,IAAI,EAAEzB,sBAAsB,CAAC0B,WAAW;UACxCC,OAAO,EAAE;QACb,CAAC,CAAC,CAAC;MACP;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,UAAUA,CAACC,SAAS,EAAE;IAClB/B,MAAM,CAAC,IAAI,CAACsB,aAAa,KAAKf,SAAS,EAAEN,sBAAsB,CAAC+B,qBAAqB,CAAC;IACtF,IAAI,CAACC,UAAU,GAAGF,SAAS;IAC3B,OAAO,IAAIlB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC,IAAInB,SAAS,CAAC,CAAC,EAAE;QACbI,MAAM,CAAC,IAAI,CAACsB,aAAa,KAAKf,SAAS,EAAEN,sBAAsB,CAAC+B,qBAAqB,CAAC;QACtF,IAAI,CAACV,aAAa,CAACY,WAAW,CAACC,eAAe,CAAC,CAAC,CAAClB,IAAI,CAACmB,YAAY,IAAI;UAClE,IAAIA,YAAY,EAAE;YACd,IAAI,CAACC,aAAa,GAAGD,YAAY;YACjC,IAAI,CAAChC,OAAO,CAACQ,KAAK,CAAC,+BAA+B0B,IAAI,CAACC,SAAS,CAACH,YAAY,CAAC,EAAE,CAAC;YACjFtB,OAAO,CAACsB,YAAY,CAAC;UACzB,CAAC,MACI;YACD,IAAI,CAAChC,OAAO,CAACQ,KAAK,CAAC,gCAAgC,CAAC;YACpD,OAAO,IAAI,CAACU,aAAa,CAACY,WAAW,CAACM,SAAS,CAAC;cAC5CC,eAAe,EAAE,IAAI;cACrBC,oBAAoB,EAAE,IAAI,CAACC,mBAAmB,CAACZ,SAAS;YAC5D,CAAC,CAAC,CACGd,IAAI,CAAC2B,uBAAuB,IAAI;cACjC,IAAI,CAACP,aAAa,GAAGO,uBAAuB;cAC5C,IAAI,CAACxC,OAAO,CAACQ,KAAK,CAAC,oBAAoB0B,IAAI,CAACC,SAAS,CAACK,uBAAuB,CAAC,EAAE,CAAC;cACjF9B,OAAO,CAAC8B,uBAAuB,CAAC;YACpC,CAAC,CAAC,CACGpB,KAAK,CAACC,KAAK,IAAI;cAChB,IAAI,CAACrB,OAAO,CAACqB,KAAK,CAACA,KAAK,CAAC;YAC7B,CAAC,CAAC;UACN;QACJ,CAAC,CAAC;MACN,CAAC,MACI;QACDV,MAAM,CAAC,IAAIpB,YAAY,CAAC;UACpB+B,IAAI,EAAEzB,sBAAsB,CAAC0B,WAAW;UACxCC,OAAO,EAAE;QACb,CAAC,CAAC,CAAC;MACP;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIe,mBAAmBA,CAACE,YAAY,EAAE;IAC9B,MAAMC,OAAO,GAAG,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,GAAIF,YAAY,CAACG,MAAM,GAAG,CAAE,IAAI,CAAC,CAAC;IAC/D,MAAMC,MAAM,GAAG,CAACJ,YAAY,GAAGC,OAAO,EACjCI,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAClBA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IACvB,MAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC;IACnC,MAAMK,WAAW,GAAG,IAAIC,UAAU,CAACJ,OAAO,CAACH,MAAM,CAAC;IAClD,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,OAAO,CAACH,MAAM,EAAE,EAAEQ,CAAC,EAAE;MACrCF,WAAW,CAACE,CAAC,CAAC,GAAGL,OAAO,CAACM,UAAU,CAACD,CAAC,CAAC;IAC1C;IACA,OAAOF,WAAW;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACII,IAAIA,CAAC9B,OAAO,EAAE;IACV,IAAI,IAAI,CAACtB,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACqD,WAAW,CAAC,OAAO/B,OAAO,KAAK,QAAQ,GAAGU,IAAI,CAACC,SAAS,CAACX,OAAO,CAAC,GAAGA,OAAO,CAAC;IACpG;EACJ;EACA;AACJ;AACA;AACA;EACIL,eAAeA,CAAA,EAAG;IAAA,IAAAqC,KAAA;IACd,IAAI,CAACvD,aAAa,CAACwD,gBAAgB,CAAC,aAAa,eAAAC,iBAAA,CAAE,aAAY;MAC3D,MAAMC,YAAY,GAAGH,KAAI,CAACvD,aAAa,CAAC2D,KAAK;MAC7CJ,KAAI,CAACxD,OAAO,CAACQ,KAAK,CAAC,8BAA8BmD,YAAY,EAAE,CAAC;MAChE,MAAM;QAAEE,KAAK;QAAEC,MAAM;QAAEC,UAAU;QAAEC,aAAa;QAAEC,SAAS;QAAEC;MAAa,CAAC,GAAGxE,OAAO,CAACyE,SAAS,CAAC,CAAC,CAACC,SAAS,EAAEC,QAAQ,IAAI,CAAC,CAAC;MAC3H,MAAM;QAAEC;MAAY,CAAC,SAAS3E,gBAAgB,CAAC,CAAC;MAChD,IAAIkE,KAAK,IAAIC,MAAM,IAAIQ,WAAW,EAAE;QAChC;QACA7E,MAAM,CAAC;UACHoE,KAAK;UACLC,MAAM;UACNS,QAAQ,EAAE,MAAM;UAChBD,WAAW;UACXP,UAAU;UACVC,aAAa;UACbC,SAAS;UACTC,WAAW;UACXM,KAAK,EAAE;YACHlD,IAAI,EAAE,eAAe;YACrBmD,UAAU,EAAE;cACRb,KAAK,EAAED;YACX;UACJ;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,EAAC;IACF,IAAI,CAAC1D,aAAa,CAACwD,gBAAgB,CAAC,SAAS,EAAEe,KAAK,IAAI;MACpD,IAAI,CAACxE,OAAO,CAACQ,KAAK,CAAC,gCAAgCgE,KAAK,EAAE,CAAC;IAC/D,CAAC,CAAC;EACN;AACJ;AAEA,SAAS1E,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}