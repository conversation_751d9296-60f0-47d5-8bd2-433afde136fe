{"ast": null, "code": "import { AmplifyError } from '../../errors/AmplifyError.mjs';\nimport '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst getCrypto = () => {\n  if (typeof window === 'object' && typeof window.crypto === 'object') {\n    return window.crypto;\n  }\n  // Next.js global polyfill\n  if (typeof crypto === 'object') {\n    return crypto;\n  }\n  throw new AmplifyError({\n    name: 'MissingPolyfill',\n    message: 'Cannot resolve the `crypto` function from the environment.'\n  });\n};\nconst getBtoa = () => {\n  // browser\n  if (typeof window !== 'undefined' && typeof window.btoa === 'function') {\n    return window.btoa;\n  }\n  // Next.js global polyfill\n  if (typeof btoa === 'function') {\n    return btoa;\n  }\n  throw new AmplifyError({\n    name: 'Base64EncoderError',\n    message: 'Cannot resolve the `btoa` function from the environment.'\n  });\n};\nconst getAtob = () => {\n  // browser\n  if (typeof window !== 'undefined' && typeof window.atob === 'function') {\n    return window.atob;\n  }\n  // Next.js global polyfill\n  if (typeof atob === 'function') {\n    return atob;\n  }\n  throw new AmplifyError({\n    name: 'Base64EncoderError',\n    message: 'Cannot resolve the `atob` function from the environment.'\n  });\n};\nexport { getAtob, getBtoa, getCrypto };", "map": {"version": 3, "names": ["AmplifyError", "getCrypto", "window", "crypto", "name", "message", "getBtoa", "btoa", "getAtob", "atob"], "sources": ["C:/Projects/IntegrationPlatform/UserManagementWebApp/user-managment-web-app/node_modules/@aws-amplify/core/dist/esm/utils/globalHelpers/index.mjs"], "sourcesContent": ["import { AmplifyError } from '../../errors/AmplifyError.mjs';\nimport '../../types/errors.mjs';\nimport '../../errors/errorHelpers.mjs';\n\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst getCrypto = () => {\n    if (typeof window === 'object' && typeof window.crypto === 'object') {\n        return window.crypto;\n    }\n    // Next.js global polyfill\n    if (typeof crypto === 'object') {\n        return crypto;\n    }\n    throw new AmplifyError({\n        name: 'MissingPolyfill',\n        message: 'Cannot resolve the `crypto` function from the environment.',\n    });\n};\nconst getBtoa = () => {\n    // browser\n    if (typeof window !== 'undefined' && typeof window.btoa === 'function') {\n        return window.btoa;\n    }\n    // Next.js global polyfill\n    if (typeof btoa === 'function') {\n        return btoa;\n    }\n    throw new AmplifyError({\n        name: 'Base64EncoderError',\n        message: 'Cannot resolve the `btoa` function from the environment.',\n    });\n};\nconst getAtob = () => {\n    // browser\n    if (typeof window !== 'undefined' && typeof window.atob === 'function') {\n        return window.atob;\n    }\n    // Next.js global polyfill\n    if (typeof atob === 'function') {\n        return atob;\n    }\n    throw new AmplifyError({\n        name: 'Base64EncoderError',\n        message: 'Cannot resolve the `atob` function from the environment.',\n    });\n};\n\nexport { getAtob, getBtoa, getCrypto };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,+BAA+B;AAC5D,OAAO,wBAAwB;AAC/B,OAAO,+BAA+B;;AAEtC;AACA;AACA,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACpB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACC,MAAM,KAAK,QAAQ,EAAE;IACjE,OAAOD,MAAM,CAACC,MAAM;EACxB;EACA;EACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC5B,OAAOA,MAAM;EACjB;EACA,MAAM,IAAIH,YAAY,CAAC;IACnBI,IAAI,EAAE,iBAAiB;IACvBC,OAAO,EAAE;EACb,CAAC,CAAC;AACN,CAAC;AACD,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAClB;EACA,IAAI,OAAOJ,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACK,IAAI,KAAK,UAAU,EAAE;IACpE,OAAOL,MAAM,CAACK,IAAI;EACtB;EACA;EACA,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;IAC5B,OAAOA,IAAI;EACf;EACA,MAAM,IAAIP,YAAY,CAAC;IACnBI,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,EAAE;EACb,CAAC,CAAC;AACN,CAAC;AACD,MAAMG,OAAO,GAAGA,CAAA,KAAM;EAClB;EACA,IAAI,OAAON,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACO,IAAI,KAAK,UAAU,EAAE;IACpE,OAAOP,MAAM,CAACO,IAAI;EACtB;EACA;EACA,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;IAC5B,OAAOA,IAAI;EACf;EACA,MAAM,IAAIT,YAAY,CAAC;IACnBI,IAAI,EAAE,oBAAoB;IAC1BC,OAAO,EAAE;EACb,CAAC,CAAC;AACN,CAAC;AAED,SAASG,OAAO,EAAEF,OAAO,EAAEL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}